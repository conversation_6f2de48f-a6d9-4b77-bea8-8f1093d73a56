

export interface RetailerSite {
    Id: number;
    //FKs
    Site_Id: number;
    DealerGroup_Id: number;
    Name: string;
    RetailerId: number;
    IsActive: boolean;
    Postcode: string;
    Makes: string;
    FakeName: string;
    //AutoPrice settings
    //Updating Prices
    UpdatePricesAutomatically: boolean;
    UpdatePricesMon: boolean;
    UpdatePricesTue: boolean;
    UpdatePricesWed: boolean;
    UpdatePricesThu: boolean;
    UpdatePricesFri: boolean;
    UpdatePricesPubHolidays: boolean;
    UpdatePricesSat: boolean;
    UpdatePricesSun: boolean;
    WhenToActionChangesEachDay: number;
    MaximumOptOutDays: number;
    MinimumAutoPriceDecrease: number;
    MinimumAutoPriceIncrease: number;
    MinimumAutoPricePercentDecrease: number;
    MinimumAutoPricePercentIncrease: number;
    //Local Bargains
    LocalBargainThreshold: number;
    //Location Optimiser
    LocationMovePoundPerMile: number;
    LocationMoveFixedCostPerMove: number;
}
