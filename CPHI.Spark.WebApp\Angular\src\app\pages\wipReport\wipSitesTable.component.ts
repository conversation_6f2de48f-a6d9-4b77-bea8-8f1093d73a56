import { Component, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { GridApi } from 'ag-grid-community';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../cph.pipe';
import { WipSiteRow, } from '../../model/main.model';
import { AGGridMethodsService } from '../../services/agGridMethods.service';
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';
import { WipReportService } from './wipReport.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'wipSitesTable',
  template: `
    <div id="gridHolder">
      <div id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular 
      id="WipTable"
      
      class="ag-theme-balham" 
      [gridOptions]="mainTableGridOptions" 
      
             > 
      </ag-grid-angular>
    </div>
    `
  ,
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
  styles: [
    `
    
     
  `
  ]
})



export class WipSitesTableComponent implements OnInit,OnDestroy {
  private destroy$ = new Subject<void>();
 
  @Input() isRegional: boolean;
  @Output() clickedSite = new EventEmitter<WipSiteRow>();

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  showGrid = false;
  public gridApi: GridApi;
  mainTableGridOptions: GridOptionsCph;
  filterBy: string;

  constructor(
    public columnTypeService: ColumnTypesService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: WipReportService
  ) { }


  ngOnInit() {
    this.filterBy = this.isRegional ? 'regions' : 'sites';
    this.initParams();

    this.selections.wipReport.wipsChangedEmitter
    .pipe(takeUntil(this.destroy$))
    .subscribe(() => {
      if (this.gridApi) {
        this.gridApi.setRowData(this.selections.wipReport[`${this.filterBy}Rows`]);
        this.gridApi.setPinnedBottomRowData(this.selections.wipReport.sitesTotal);
      }
    })
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
}

  initParams() {
    this.mainTableGridOptions = {
      getMainMenuItems:(params)=>this.gridHelpersService.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlights),
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.selections.wipReport[`${this.filterBy}Rows`],
      pinnedBottomRowData: this.selections.wipReport.sitesTotal,
      onGridReady: (params) => this.onGridReady(params), 
      domLayout: 'autoHeight',
      onFirstDataRendered:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
      onCellClicked: (params) => {
        this.onCellClick(params);
      },
      
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },

      columnDefs: [
        { headerName: '', field: 'Label', colId: 'Label', width: 200, type: 'label' },
        { headerName: '<1 ' + this.constants.translatedText.Days, field: `ValueUnder1Day`, colId: 'ValueUnder1Day', width: 200, type: 'currency' },
        { headerName: '1-7 ' + this.constants.translatedText.Days, field: `Value1To7Days`, colId: 'Value1To7Days', width: 200, type: 'currency' },
        { headerName: '8-20 ' + this.constants.translatedText.Days, field: `Value8To20Days`, colId: 'Value8To20Days', width: 200, type: 'currency' },
        { headerName: '21-30 ' + this.constants.translatedText.Days, field: `Value21To30Days`, colId: 'Value21To30Days', width: 200, type: 'currency' },
        { headerName: '31-60 ' + this.constants.translatedText.Days, field: `Value31To60Days`, colId: 'Value31To60Days', width: 200, type: 'currency' },
        { headerName: '>60 ' + this.constants.translatedText.Days, field: `ValueOver60Days`, colId: 'ValueOver60Days', width: 200, type: 'currency' },
        { headerName: this.constants.translatedText.Total, field: `ValueTotal`, colId: 'ValueTotal', width: 200, type: 'currency' }
  
    ],
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }
  }

  // cellClassProvider(params: CellClassParams){
  //   if (this.isRegional) {
  //     return params?.value < 0 ? 'badFont ag-right-aligned-cell' : 'ag-right-aligned-cell';
  //   } else {
  //     return this.agGrid.cellClassProviderWithColourFontNew(params,this.service.topBottomHighlights);
  //   }
  // }


  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }
  }

  onCellClick(params) {
    this.clickedSite.next(params.node.data);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };
  }

  excelExport() {
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, `Debts Site Summary - Aged at ${this.selections.wipReport.ageAtMonthEnd ? 'month end' : 'now'}`, 1, 1);
  }
}
