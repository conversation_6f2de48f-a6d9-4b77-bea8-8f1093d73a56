﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using MoreLinq;
using StockPulse.WebApi.Model;
using System.Data;
using System.Linq;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IVehicleAdvertsDataAccess
   {
      Task<VehicleAdvertSpecBuild> AddVehicleAdvertSpecBuild(VehicleAdvertSpecBuild newVehicleAdvertSpecBuild);
      Task AddVehicleAdvertSpecBuildOptions(List<VehicleAdvertSpecBuildOption> allVehicleAdvertSpecBuildOptions);
      Task<IEnumerable<VehicleAdvertWithRetailRating>> GetActiveAdvertsHavingRetailRating(DealerGroupName dealerGroup);
      Task<IEnumerable<VehicleDerivativeAndMake>> GetVehicleDerivativesAndMakes(DealerGroupName dealerGroup);
      Task<IEnumerable<AdvertPriceAndViewsHistoryItem>> GetAdvertPriceAndViewsHistory(int advertId);
      Task<IEnumerable<DashboardAdvert>> GetDashboardAdverts(List<int> retailerSiteIds, bool includeNew);
      Task<VehicleAdvert> GetExistingVehicleAdvert(int vehicleAdvertId);
      Task<List<LocationOptimiserAdvert>> GetLocationOptimiserAdverts(DateTime effectiveDate, int? advertId, bool includeNew, int userId);
      Task<IEnumerable<SameModelAdvert>> GetSameModelAdvertsForAdvertId(int advertId, DealerGroupName dealerGroup);
      Task<VehicleAdvertSpecBuild> GetVehicleAdvertSpecBuild(int vehicleAdvertId);
      Task<List<VehicleAdvertSpecBuildOption>> GetVehicleAdvertSpecBuildOptions(int id);
      Task<VehicleIdentifierAndAdvertiser> GetWebsiteAdvert(string reg, string vin, int siteId);
      Task<string> GetWebSiteStockIdentifier(int advertId);
      Task UpdateVehicleAdvertSpecBuild(VehicleAdvertSpecBuild updatedVehicleAdvertSpecBuild);
      Task UpdateVehicleAdvertSpecBuildOptions(List<VehicleAdvertSpecBuildOption> allVehicleAdvertSpecBuildOptions);
      Task<List<StockIdentifierAndId>> UpsertVehicles(List<VehicleAdvert> incomingVehicles, DealerGroupName dealerGroup);
      Task<List<PortalOption>> UpsertPortalOptions(List<PortalOption> incomingOptions, DealerGroupName dealerGroup);
      Task<List<VehicleAdvertPortalOption>> GetVehicleAdvertsPortalOptions(List<int> advertsIds, DealerGroupName dealerGroup);
      Task UpsertVehicleAdvertPortalOptions(List<AutoTraderVehicleListing> allAdverts, List<StockIdentifierAndId> allIncomingVehicles, DealerGroupName dealerGroup);
      Task SaveNewVehicleAdverts(List<VehicleAdvert> newAdverts);
      Task<IEnumerable<OffStrategyVehicleSummaryItem>> GetOffStrategyAdverts(List<int> retailerSiteIds, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions);
      Task<VehicleAdvertDetail> GetVehicleAdvertDetails(int advertId, DateTime? effectiveDate, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions, DealerGroupName dealerGroup);
      Task<IEnumerable<AdvertViewsHistory>> GetVehicleAdvertSearchHistory(DealerGroupName dealerGroupName, DateTime effectiveDate);
      Task<List<VehicleAdvert>> GetExistingFakeVehicleAdverts(DealerGroupName dealerGroup);
      Task AddStockIdToIncomingVehicles(List<VehicleAdvert> incomingVehicles, CPHIDbContext db, DealerGroupName dealerGroup);
      Task<IEnumerable<SameModelAdvert>> GetSameModelAdverts(string model, DealerGroupName dealerGroup, bool includeUnpublishedAds);
      Task SaveNewOdometerAndEngineBadgeReadingForAdverts(List<VehicleAdvert> updatedAdverts);
      Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertsWithRatingsFromDb(DateTime effectiveDate, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions, DealerGroupName dealerGroupName, List<string> lifecycleStatuses, List<int> advertIds);
      Task<List<AdvertIdAndCompetitorLink>> GetAdsAndCompetitorLinks(DealerGroupName dealerGroupName, List<int> advertIds);
      //Task<IEnumerable<VehicleAdvertItem>> FetchVehicleAdvertItems(DateTime chosenDate, DealerGroupName dealerGroup, List<int> advertIds, List<string> lifecycles);
      Task SetAllDealerGroupSnapshotsToNoLongerLatest(int dealerGroupId);
      Task<string?> GetAdvertDerivativeId(DealerGroupName dealerGroup, int advertId);

      Task<bool> IsThereATodaySnapshotForAdId(int advertId);
   }

   public class VehicleAdvertsDataAccess : IVehicleAdvertsDataAccess
   {
      private readonly string _connectionString;
      public VehicleAdvertsDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }

      public async Task SetAllDealerGroupSnapshotsToNoLongerLatest(int dealerGroupId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            string sql = $"UPDATE autoprice.VehicleAdvertSnapshots SET IsTodayLatestSnapshot = 0 WHERE IsTodayLatestSnapshot = 1 AND DealerGroup_Id = {dealerGroupId}";
            await dapper.ExecuteAsync(sql, null, CommandType.Text);
         }
      }

      async public Task<string> GetWebSiteStockIdentifier(int advertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return (await db.VehicleAdverts.FirstAsync(x => x.Id == advertId)).WebSiteStockIdentifier;
         }
      }


      public async Task<IEnumerable<VehicleDerivativeAndMake>> GetVehicleDerivativesAndMakes(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("dealerGroupId", (int)dealerGroup);
            return await dapper.GetAllAsync<VehicleDerivativeAndMake>("autoprice.GET_DerivativesAndMakes", paramList, CommandType.StoredProcedure);
         }

      }

      public async Task<List<VehicleAdvert>> GetExistingFakeVehicleAdverts(DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleAdverts.Where(x =>
                x.WebSiteStockIdentifier == null
                && x.StockNumber != "LEGACY"
                && x.AutotraderAdvertStatus == "IN STOCK NOT ON PORTAL"
                && x.RetailerSite.DealerGroup_Id == (int)dealerGroup
                ).ToListAsync();
         }
      }

      private void SetVehicleAdvertProperties(VehicleAdvert existingDbVehicle, VehicleAdvert incomingVehicle)
      {
         //update existing props
         existingDbVehicle.StockNumber = incomingVehicle.StockNumber;
         existingDbVehicle.OwnershipCondition = incomingVehicle.OwnershipCondition;
         existingDbVehicle.RetailerIdentifier = incomingVehicle.RetailerIdentifier;
         existingDbVehicle.VehicleReg = incomingVehicle.VehicleReg;
         existingDbVehicle.Chassis = incomingVehicle.Chassis;
         existingDbVehicle.WebSiteSearchIdentifier = incomingVehicle.WebSiteSearchIdentifier;
         existingDbVehicle.Make = incomingVehicle.Make;
         existingDbVehicle.Model = incomingVehicle.Model;
         existingDbVehicle.Generation = incomingVehicle.Generation;
         existingDbVehicle.Derivative = incomingVehicle.Derivative;
         existingDbVehicle.VehicleType = incomingVehicle.VehicleType;
         existingDbVehicle.Trim = incomingVehicle.Trim;
         existingDbVehicle.BodyType = incomingVehicle.BodyType;
         existingDbVehicle.FuelType = incomingVehicle.FuelType;
         existingDbVehicle.TransmissionType = incomingVehicle.TransmissionType;
         existingDbVehicle.DriveTrain = incomingVehicle.DriveTrain;
         existingDbVehicle.Seats = incomingVehicle.Seats;
         existingDbVehicle.Doors = incomingVehicle.Doors;
         existingDbVehicle.EngineCapacityCC = incomingVehicle.EngineCapacityCC;
         existingDbVehicle.BadgeEngineSizeLitres = incomingVehicle.BadgeEngineSizeLitres;
         existingDbVehicle.EnginePowerBHP = incomingVehicle.EnginePowerBHP;
         existingDbVehicle.Owners = incomingVehicle.Owners;
         existingDbVehicle.FirstRegisteredDate = incomingVehicle.FirstRegisteredDate;
         existingDbVehicle.Colour = incomingVehicle.Colour;
         existingDbVehicle.SpecificColour = incomingVehicle.SpecificColour;
         existingDbVehicle.Gears = incomingVehicle.Gears;
         existingDbVehicle.VehicleExciseDuty = incomingVehicle.VehicleExciseDuty;
         existingDbVehicle.Sector = incomingVehicle.Sector;
         existingDbVehicle.DateOnForecourt = incomingVehicle.DateOnForecourt;
         existingDbVehicle.RetailerSite_Id = incomingVehicle.RetailerSite_Id;
         existingDbVehicle.OdometerReading = incomingVehicle.OdometerReading;
         existingDbVehicle.DerivativeId = incomingVehicle.DerivativeId;
         existingDbVehicle.CompetitorLink = incomingVehicle.CompetitorLink;
         existingDbVehicle.AttentionGrabber = incomingVehicle.AttentionGrabber;
         existingDbVehicle.Description = incomingVehicle.Description;
         existingDbVehicle.Description2 = incomingVehicle.Description2;
         existingDbVehicle.AutotraderAdvertStatus = incomingVehicle.AutotraderAdvertStatus;
         existingDbVehicle.AdvertiserAdvertStatus = incomingVehicle.AdvertiserAdvertStatus;
         existingDbVehicle.LocatorAdvertStatus = incomingVehicle.LocatorAdvertStatus;
         existingDbVehicle.ExportAdvertStatus = incomingVehicle.ExportAdvertStatus;
         existingDbVehicle.ProfileAdvertStatus = incomingVehicle.ProfileAdvertStatus;

         existingDbVehicle.ImageUrls = incomingVehicle.ImageUrls;
         existingDbVehicle.VideoUrl = incomingVehicle.VideoUrl;

         existingDbVehicle.Stock_Id = incomingVehicle.Stock_Id;

         //we don't consider createdDate as don't want this to update
      }


      public async Task SaveNewVehicleAdverts(List<VehicleAdvert> newAdverts)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.VehicleAdverts.AddRangeAsync(newAdverts);
            await db.SaveChangesAsync();
         }
      }

      public async Task SaveNewOdometerAndEngineBadgeReadingForAdverts(List<VehicleAdvert> updatedAdverts)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            foreach (var ad in updatedAdverts)
            {
               var dbAd = db.VehicleAdverts.First(x => x.Id == ad.Id);
               dbAd.OdometerReading = ad.OdometerReading;
               dbAd.BadgeEngineSizeLitres = ad.BadgeEngineSizeLitres;
               dbAd.CompetitorLink = ad.CompetitorLink;
            }

            await db.SaveChangesAsync();
         }
      }

      public async Task SaveOwnersAndSpecificColour(List<VehicleAdvertWithRating> adverts)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var adIds = adverts.Select(x => x.AdId).ToList();
            var advertsByadIds = adverts.ToDictionary(x => x.AdId);

            //update the adverts
            var dbVehicleAdverts = await db.VehicleAdverts.Where(x => adIds.Contains(x.Id)).ToListAsync();
            foreach (var vehicleAdvert in dbVehicleAdverts)
            {
               if (int.TryParse(advertsByadIds[vehicleAdvert.Id].Owners, out int owner))
               {
                  vehicleAdvert.Owners = owner;
               }
               vehicleAdvert.SpecificColour = advertsByadIds[vehicleAdvert.Id].SpecificColour;
            }

            await db.SaveChangesAsync();
         }
      }



      public async Task<List<StockIdentifierAndId>> UpsertVehicles(List<VehicleAdvert> incomingVehicles, DealerGroupName dealerGroup) //Based on WebSiteStockIdentifier
      {
         try
         {

            var maxLengths = new Dictionary<string, int>
        {
            { "StockNumber", 50 },
            { "RetailerIdentifier", 50 },
            { "VehicleReg", 7 },
            { "Chassis", 20 },
            { "WebSiteStockIdentifier", 75 },
            { "WebSiteSearchIdentifier", 50 },
            { "Make", 50 },
            { "Model", 150 },
            { "Generation", 50 },
            { "Derivative", 250 },
            { "VehicleType", 50 },
            { "Trim", 75 },
            { "BodyType", 50 },
            { "FuelType", 50 },
            { "TransmissionType", 50 },
            { "DriveTrain", 50 },
            { "Colour", 150 },
            { "Sector", 50 },
            { "AdvertiserAdvertStatus", 50 },
            { "AttentionGrabber", 75 },
            { "AutotraderAdvertStatus", 50 },
            { "ExportAdvertStatus", 50 },
            { "LocatorAdvertStatus", 50 },
            { "ProfileAdvertStatus", 50 },
            { "VideoUrl", 250 },
            { "DerivativeId", 75 },
            { "OwnershipCondition", 50 },
            { "CompetitorLink", 1000 },
            { "SpecificColour", 150 }
        };


            foreach (var vehicle in incomingVehicles)
            {
               foreach (var property in vehicle.GetType().GetProperties())
               {
                  if (maxLengths.TryGetValue(property.Name, out int maxLength))
                  {
                     var value = property.GetValue(vehicle) as string;
                     if (value != null && value.Length > maxLength)
                     {
                        Console.WriteLine($"Property '{property.Name}' exceeds max length of {maxLength}. Value: {value}");

                        // Truncate the string to the maximum allowed length
                        var truncatedValue = value.Substring(0, maxLength);
                        property.SetValue(vehicle, truncatedValue);

                        Console.WriteLine($"Truncated '{property.Name}' to: {truncatedValue}");
                     }
                  }
               }
            }


            using (var db = new CPHIDbContext(_connectionString))
            {
               //Firstly add the StockId to each vehicle
               await AddStockIdToIncomingVehicles(incomingVehicles, db, dealerGroup);



               //Based on WebSiteStockIdentifier
               List<string> vehicles = await db.VehicleAdverts.Include(x => x.RetailerSite).Where(x => x.RetailerSite.DealerGroup_Id == (int)dealerGroup).Select(x => x.WebSiteStockIdentifier).ToListAsync();
               HashSet<string> existingIds = new HashSet<string>(vehicles);

               List<VehicleAdvert> newVehicles = new List<VehicleAdvert>();

               Dictionary<string, VehicleAdvert> existingVehiclesIncoming = incomingVehicles
                   .Where(v => existingIds.Contains(v.WebSiteStockIdentifier))
                   .ToDictionary(v => v.WebSiteStockIdentifier, v => v);

               newVehicles.AddRange(incomingVehicles.Except(existingVehiclesIncoming.Values));

               await db.VehicleAdverts.AddRangeAsync(newVehicles);


               List<VehicleAdvert> existingDbVehicles = await db.VehicleAdverts
                   .Include(x => x.RetailerSite)
                   .Where(x => x.RetailerSite.DealerGroup_Id == (int)dealerGroup)
                   .Where(x => existingVehiclesIncoming.Keys.Contains(x.WebSiteStockIdentifier))
                   .ToListAsync();



               foreach (var existingDbVehicle in existingDbVehicles)
               {
                  var incomingVehicle = existingVehiclesIncoming[existingDbVehicle.WebSiteStockIdentifier];

                  if (!AreEqual(incomingVehicle, existingDbVehicle))
                  {
                     SetVehicleAdvertProperties(existingDbVehicle, incomingVehicle);
                  }
               }

               await db.SaveChangesAsync();




               return newVehicles.Concat(existingDbVehicles)
                           .Select(x => new StockIdentifierAndId() { VehicleId = x.Id, WebSiteStockIdentifier = x.WebSiteStockIdentifier })
                           .ToList();
            }
         }
         catch (Exception ex)
         {
            { }
            throw new Exception(ex.Message);
         }
      }

      public async Task AddStockIdToIncomingVehicles(List<VehicleAdvert> incomingVehicles, CPHIDbContext db, DealerGroupName dealerGroup)
      {
         // To optimize, load all stocks into a dictionary if the number of stocks is reasonable
         var aMonthAgo = DateTime.Now.AddDays(-30);
         var stockDict = await db.Stocks
             .Where(x => x.Reg != null && x.Reg != string.Empty)  // Filter out empty registrations
             .Where(x => x.IsRemoved == false || x.RemovedDate >= aMonthAgo)
             .Where(x => x.Site.DealerGroup_Id == (int)dealerGroup)
             .GroupBy(s => s.Reg)  // Group by registration
             .Select(g => g.OrderByDescending(s => s.Id).First())  // Within each group, order by Id descending and take the first
             .ToDictionaryAsync(s => s.Reg, s => s.Id);  // Convert to dictionary

         // Update each vehicle advert with the corresponding stock id
         foreach (var advert in incomingVehicles)
         {
            if (advert.VehicleReg != null && stockDict.TryGetValue(advert.VehicleReg, out var stockId))
            {
               advert.Stock_Id = stockId;
            }
         }

         // Save changes to the database
         await db.SaveChangesAsync();
      }



      public async Task<List<PortalOption>> UpsertPortalOptions(List<PortalOption> incomingOptions, DealerGroupName dealerGroup)
      {
         try
         {
            using (var db = new CPHIDbContext(_connectionString))
            {
               var strategy = db.Database.CreateExecutionStrategy();

               List<PortalOption> newOptions = null;

               await strategy.ExecuteAsync(async () =>
               {
                  // Start a transaction
                  using (var transaction = await db.Database.BeginTransactionAsync())
                  {
                     // Retrieve the set of names that already exist in the database
                     var existingNames = new HashSet<string>(
                              await db.PortalOptions.Select(o => o.OptionName).ToListAsync());

                     // Filter out the options that already exist
                     newOptions = incomingOptions
                              .Where(option => !existingNames.Contains(option.OptionName))
                              .ToList();

                     // Add the new options that don't exist in the database
                     if (newOptions.Any())
                     {

                        //Manually truncate any local bargain strings that are too long                                                
                        List<string> truncateMessages = StringTruncaterService.Truncate<PortalOption>(newOptions);
                        if (truncateMessages.Count > 0)
                        {
                           foreach (var message in truncateMessages)
                           {
                              Console.WriteLine(message);
                           }
                        }

                        await db.PortalOptions.AddRangeAsync(newOptions);
                        await db.SaveChangesAsync();
                     }

                     // Commit the transaction
                     await transaction.CommitAsync();
                  }
               });

               // Return the new options
               return newOptions;
            }
         }
         catch (Exception ex)
         {
            throw new Exception($"An error occurred while upserting portal options: {ex.Message}", ex);
         }
      }




      public async Task UpsertVehicleAdvertPortalOptions(List<AutoTraderVehicleListing> allAdverts, List<StockIdentifierAndId> allIncomingVehicles, DealerGroupName dealerGroup)
      {
         try
         {
            using (var db = new CPHIDbContext(_connectionString))
            {
               var advertListingLookup = allAdverts.ToDictionary(x => x.metadata.stockId);
               var allAddIds = allIncomingVehicles.Select(s => s.VehicleId).ToList();
               var dbOptionsByAdLookup = (await db.VehicleAdvertPortalOptions.Include(x => x.PortalOption).Where(v => allAddIds.Contains(v.VehicleAdvert_Id)).ToListAsync()).ToLookup(x => x.VehicleAdvert_Id);
               var allOptions = (await db.PortalOptions.ToListAsync()).ToDictionary(x => x.OptionName);

               List<VehicleAdvertPortalOption> vehicleAdvertPortalOptionToAdd = new List<VehicleAdvertPortalOption>();
               List<VehicleAdvertPortalOption> vehicleAdvertPortalOptionToRemoved = new List<VehicleAdvertPortalOption>();
               foreach (var incomingVehicle in allIncomingVehicles)
               {

                  List<string> incomingOptionsThisAd = advertListingLookup[incomingVehicle.WebSiteStockIdentifier].features.Select(s => s.name).ToList(); //.Where(f => f.featureType == "Optional")
                  List<VehicleAdvertPortalOption> dbOptionsThisAd = dbOptionsByAdLookup[incomingVehicle.VehicleId].ToList();
                  var dbOptionNamesThisAd = dbOptionsThisAd.Select(x => x.PortalOption.OptionName).ToList();

                  //walk through existing db options for this ad
                  foreach (var option in dbOptionsThisAd)
                  {
                     if (!incomingOptionsThisAd.Contains(option.PortalOption.OptionName))
                     {
                        vehicleAdvertPortalOptionToRemoved.Add(option);
                     }
                  }

                  //walk through incoming options for this add
                  foreach (var option in incomingOptionsThisAd)
                  {
                     if (!dbOptionNamesThisAd.Contains(option))
                     {
                        vehicleAdvertPortalOptionToAdd.Add(new VehicleAdvertPortalOption()
                        {
                           PortalOption_Id = allOptions[option].Id,
                           VehicleAdvert_Id = incomingVehicle.VehicleId
                        });
                     }
                  }
               }

               await db.VehicleAdvertPortalOptions.AddRangeAsync(vehicleAdvertPortalOptionToAdd);
               db.VehicleAdvertPortalOptions.RemoveRange(vehicleAdvertPortalOptionToRemoved);

               await db.SaveChangesAsync();
            }
         }
         catch (Exception ex)
         {
            { }
            throw new Exception(ex.Message);
         }
      }



      public async Task<IEnumerable<AdvertPriceAndViewsHistoryItem>> GetAdvertPriceAndViewsHistory(int advertId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("advertId", advertId);
            IEnumerable<AdvertPriceAndViewsHistoryItem> result = await dapper.GetAllAsync<AdvertPriceAndViewsHistoryItem>("autoprice.GET_AdvertPriceAndViewsHistory",
                    paramList, commandType: System.Data.CommandType.StoredProcedure);

            decimal? mostRecentStrategyPrice = null;

            // List is already ordered
            foreach (var item in result)
            {
               // If StrategyPrice is null, 
               if (!item.StrategyPrice.HasValue)
               {
                  item.StrategyPrice = mostRecentStrategyPrice;
               }
               else
               {
                  mostRecentStrategyPrice = item.StrategyPrice.Value;
               }

            }

            return result;

         }
      }

      public async Task<IEnumerable<SameModelAdvert>> GetSameModelAdverts(string model, DealerGroupName dealerGroup, bool includeUnpublishedAds)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("model", model);
            paramList.Add("dealerGroupId", (int)dealerGroup);
            paramList.Add("includeUnpublishedAds", includeUnpublishedAds);


            return await dapper.GetAllAsync<SameModelAdvert>("autoprice.GET_SameModelAdvertsForModel", paramList, commandType: System.Data.CommandType.StoredProcedure);
         }
      }


      public async Task<IEnumerable<SameModelAdvert>> GetSameModelAdvertsForAdvertId(int advertId, DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("advertId", advertId);
            paramList.Add("dealerGroupId", (int)dealerGroup);


            return await dapper.GetAllAsync<SameModelAdvert>("autoprice.GET_SameModelAdvertsForAdvertId", paramList, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      public async Task<bool> IsThereATodaySnapshotForAdId(int advertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleAdvertSnapshots.AnyAsync(x => x.VehicleAdvert_Id == advertId && x.SnapshotDate.Date == DateTime.Now.Date && x.IsTodayLatestSnapshot == true);
         }
      }


      public async Task<VehicleAdvertDetail> GetVehicleAdvertDetails(int advertId, DateTime? effectiveDate, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions, DealerGroupName dealerGroup)
      {
         {
            using (var dapper = new DADapperr(_connectionString))
            {
               try
               {

                  var paramList = new DynamicParameters();
                  int dealerGroupId = (int)dealerGroup;

                  paramList.Add("advertId", advertId);
                  paramList.Add("dealerGroupId", dealerGroupId);

                  if (effectiveDate != null)
                  {
                     paramList.Add("effectiveDate", effectiveDate);
                  }

                  VehicleAdvertDetail res = await dapper.GetAsync<VehicleAdvertDetail>("autoprice.GET_VehicleAdvertDetails", paramList, commandType: System.Data.CommandType.StoredProcedure);

                  res.PriceIndicatorRatingAtCurrentSelling = PriceStrategyClassifierService.ProvidePriceIndicatorName(res.PriceIndicatorRatingAtCurrentSelling);
                  res.PerfRating = AutoPriceHelperService.ProvidePerformanceRatingName(res.PerfRating);
                  res.UpdateVsStrategyBanding(bandingDefinitions);

                  return res;
               }
               catch (Exception ex)
               {
                  throw new Exception(ex.Message);
               }
            }
         }
      }


      public async Task<List<AdvertIdAndCompetitorLink>> GetAdsAndCompetitorLinks(DealerGroupName dealerGroupName, List<int> advertIds)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleAdverts.Where(x => advertIds.Contains(x.Id)).Select(x => new AdvertIdAndCompetitorLink(x)).ToListAsync();
         }
      }



      //public async Task<IEnumerable<VehicleAdvertItem>> FetchVehicleAdvertItems(DateTime chosenDate, DealerGroupName dealerGroup, List<int> advertIds, List<string> lifecycles)
      //{
      //    using (var db = new CPHIDbContext(_connectionString))
      //    {
      //        int dealerGroupId = (int)dealerGroup;
      //        if (advertIds != null)
      //        {
      //            return await db.VehicleAdvertItems
      //                    .Where(x => advertIds.Contains(x.Id))
      //                    .Where(x => x.DealerGroup_Id == dealerGroupId && x.SnapshotDate.Date == chosenDate.Date)
      //                    .Where(x => lifecycles.Contains(x.LifecycleStatus))
      //                    .ToListAsync();
      //        }
      //        else
      //        {
      //            try
      //            {

      //            return await db.VehicleAdvertItems
      //                    .Where(x => x.DealerGroup_Id == dealerGroupId && x.SnapshotDate.Date == chosenDate.Date)
      //                    .Where(x => lifecycles.Contains(x.LifecycleStatus))
      //                    .ToListAsync();
      //            }
      //            catch (Exception ex)
      //            {
      //                throw;
      //            }
      //        }
      //    }
      //}




      public async Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertsWithRatingsFromDb(
          DateTime effectiveDate,
          Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions,
          DealerGroupName dealerGroupName,
          List<string> lifecycleStatuses, List<int> advertIds)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string retailerSitesString = null;

            var spParams = new DynamicParameters(new
            {
               DealerGroupId = (int)dealerGroupName,
               EffectiveDate = effectiveDate
            });
            if (advertIds != null)
            {
               string adIdsString = string.Join(',', advertIds);
               spParams.Add("AdvertIds", adIdsString);
            }

            try
            {

               var results = await dapper.GetAllAsync<VehicleAdvertWithRating>("autoprice.GET_VehicleAdvertWithRatings", spParams, commandType: System.Data.CommandType.StoredProcedure);
               PerformPostGetActionsOnResults(effectiveDate, bandingDefinitions, results);
               return results.Where(x => lifecycleStatuses.Contains(x.LifecycleStatus)).ToList();
            }
            catch (Exception ex)
            {
               throw;
            }
         }
      }

      private static void PerformPostGetActionsOnResults(DateTime effectiveDate, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions, IEnumerable<VehicleAdvertWithRating> results)
      {
         foreach (var r in results)
         {
            r.PriceIndicatorRatingAtCurrentSelling = r.PriceIndicatorRatingAtCurrentSelling != null ? PriceStrategyClassifierService.ProvidePriceIndicatorName(r.PriceIndicatorRatingAtCurrentSelling) : null;
            r.PerfRating = AutoPriceHelperService.ProvidePerformanceRatingName(r.PerfRating);
            r.UpdateVsStrategyBanding(bandingDefinitions);

            if (r.MostRecentDailyPriceMoveDate != null)
            {
               r.DaysSinceMostRecentPriceMove = (effectiveDate.Date - ((DateTime)r.MostRecentDailyPriceMoveDate).Date).Days;
            }
         }
      }


      public async Task<IEnumerable<VehicleAdvertWithRetailRating>> GetActiveAdvertsHavingRetailRating(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var spParams = new DynamicParameters(new { dealerGroupId = (int)dealerGroup });
            return await dapper.GetAllAsync<VehicleAdvertWithRetailRating>("autoprice.GET_ActiveAdvertsHavingRetailRating", spParams, CommandType.StoredProcedure);
         }

      }

      public async Task<List<VehicleAdvertSpecBuildOption>> GetVehicleAdvertSpecBuildOptions(int id)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleAdvertSpecBuildOptions.Where(v => v.VehicleAdvertSpecBuild_Id == id).ToListAsync();
         }
      }

      public async Task UpdateVehicleAdvertSpecBuildOptions(List<VehicleAdvertSpecBuildOption> allVehicleAdvertSpecBuildOptions)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            db.VehicleAdvertSpecBuildOptions.UpdateRange(allVehicleAdvertSpecBuildOptions);
            await db.SaveChangesAsync();
         }
      }

      public async Task AddVehicleAdvertSpecBuildOptions(List<VehicleAdvertSpecBuildOption> allVehicleAdvertSpecBuildOptions)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.VehicleAdvertSpecBuildOptions.AddRangeAsync(allVehicleAdvertSpecBuildOptions);
            await db.SaveChangesAsync();
         }
      }

      async public Task<VehicleIdentifierAndAdvertiser> GetWebsiteAdvert(string reg, string vin, int siteId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            VehicleAdvert vehicleAdvert = await db.VehicleAdverts.Include(x => x.RetailerSite).FirstOrDefaultAsync(x => x.RetailerSite.Site_Id == siteId && (x.VehicleReg == reg || x.Chassis == vin));
            if (vehicleAdvert == null) { return null; }
            return new VehicleIdentifierAndAdvertiser(vehicleAdvert);
         }
      }
      public async Task<VehicleAdvert> GetExistingVehicleAdvert(int vehicleAdvertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleAdverts.Include(x => x.RetailerSite).FirstAsync(x => x.Id == vehicleAdvertId);
         }
      }

      public async Task<IEnumerable<AdvertViewsHistory>> GetVehicleAdvertSearchHistory(DealerGroupName dealerGroupName, DateTime effectiveDate)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleAdvertSnapshots
            .Include(x => x.VehicleAdvert)
            .Where(x => x.SnapshotDate.Date > effectiveDate.Date.AddDays(-7) &&
                (int)dealerGroupName == x.VehicleAdvert.RetailerSite.DealerGroup_Id
            )
            .Select(x => new AdvertViewsHistory()
            {
               AdvertId = x.VehicleAdvert_Id,
               AdvertViews = x.PerformanceAdvertViewsYest ?? 0,
               SearchViews = x.PerformanceSearchViewsYest ?? 0,
               SnapshotDate = x.SnapshotDate
            })
            .OrderBy(x => x.SnapshotDate)
            .ToListAsync();
         }
      }

      public async Task<IEnumerable<DashboardAdvert>> GetDashboardAdverts(List<int> retailerSiteIds, bool includeNew)
      {
         var paramList = new DynamicParameters();

         paramList.Add("chosenRetailerSiteIds", string.Join(",", retailerSiteIds));
         paramList.Add("includeNewVehicles", includeNew);

         using (var dapper = new DADapperr(_connectionString))
         {
            return await dapper.GetAllAsync<DashboardAdvert>("autoprice.GET_DashboardAdverts", paramList, System.Data.CommandType.StoredProcedure);
         }
      }

      public async Task<IEnumerable<OffStrategyVehicleSummaryItem>> GetOffStrategyAdverts(List<int> retailerSiteIds, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions)
      {

         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("RetailerSiteIds", string.Join(",", retailerSiteIds));

            var dtos = (await dapper.GetAllAsync<OffStrategyVehicleSummaryItemDTO>("autoprice.GET_OffStrategyAdverts", paramList, System.Data.CommandType.StoredProcedure)).ToList();

            return dtos.ConvertAll(x => new OffStrategyVehicleSummaryItem(x, bandingDefinitions));

         }


      }

      public async Task<List<LocationOptimiserAdvert>> GetLocationOptimiserAdverts(DateTime effectiveDate, int? advertId, bool includeNew, int userId)
      {

         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("effectiveDate", effectiveDate);
            paramList.Add("advertId", advertId);
            paramList.Add("includeNewVehicles", includeNew);
            paramList.Add("userId", userId);


            var dtos = await dapper.GetAllAsync<LocationOptimiserAdvertDTO>("autoprice.GET_LocationOptimiserAdverts", paramList, System.Data.CommandType.StoredProcedure);

            // Old: var toReturn = dtos.ToList().ConvertAll(x => new LocationOptimiserAdvert(x));
            // Remove zero StrategyPriceCurrent to prevent skewed net benefits
            var toReturn = dtos.Where(x => x.StrategyPriceCurrent != 0).ToList().ConvertAll(x => new LocationOptimiserAdvert(x));

            var sorted = toReturn.OrderBy(x => x.Reg).ToList();

            return sorted;
         }

      }





      public async Task<VehicleAdvertSpecBuild> GetVehicleAdvertSpecBuild(int vehicleAdvertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var vehicleAdvertSpecBuild = await db.VehicleAdvertSpecBuilds.Where(v => v.VehicleAdvert_Id == vehicleAdvertId).FirstOrDefaultAsync();
            if (vehicleAdvertSpecBuild == null) { return null; }
            return vehicleAdvertSpecBuild;
         }
      }

      public async Task UpdateVehicleAdvertSpecBuild(VehicleAdvertSpecBuild updatedVehicleAdvertSpecBuild)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            db.VehicleAdvertSpecBuilds.Update(updatedVehicleAdvertSpecBuild);
            await db.SaveChangesAsync();
         }

      }

      public async Task<VehicleAdvertSpecBuild> AddVehicleAdvertSpecBuild(VehicleAdvertSpecBuild newVehicleAdvertSpecBuild)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.VehicleAdvertSpecBuilds.AddAsync(newVehicleAdvertSpecBuild);
            await db.SaveChangesAsync();
            return newVehicleAdvertSpecBuild;
         }
      }



      private bool AreEqual(VehicleAdvert v1, VehicleAdvert v2)
      {
         return
                //v1.Id == v2.Id &&
                v1.StockNumber == v2.StockNumber &&
                v1.RetailerIdentifier == v2.RetailerIdentifier &&
                v1.VehicleReg == v2.VehicleReg &&
                v1.Chassis == v2.Chassis &&
                v1.OwnershipCondition == v2.OwnershipCondition &&
                //v1.WebSiteStockIdentifier == v2.WebSiteStockIdentifier &&
                v1.WebSiteSearchIdentifier == v2.WebSiteSearchIdentifier &&
                v1.Make == v2.Make &&
                v1.Model == v2.Model &&
                v1.Generation == v2.Generation &&
                v1.Derivative == v2.Derivative &&
                v1.VehicleType == v2.VehicleType &&
                v1.Trim == v2.Trim &&
                v1.BodyType == v2.BodyType &&
                v1.FuelType == v2.FuelType &&
                v1.TransmissionType == v2.TransmissionType &&
                v1.DriveTrain == v2.DriveTrain &&
                v1.Seats == v2.Seats &&
                v1.Doors == v2.Doors &&
                v1.EngineCapacityCC == v2.EngineCapacityCC &&
                v1.EnginePowerBHP == v2.EnginePowerBHP &&
                v1.Owners == v2.Owners &&
                v1.FirstRegisteredDate == v2.FirstRegisteredDate &&
                v1.Colour == v2.Colour &&
                v1.SpecificColour == v2.SpecificColour &&
                v1.Gears == v2.Gears &&
                v1.BadgeEngineSizeLitres == v2.BadgeEngineSizeLitres &&
                v1.CompetitorLink == v2.CompetitorLink &&
                v1.VehicleExciseDuty == v2.VehicleExciseDuty &&
                v1.Sector == v2.Sector &&
                v1.DateOnForecourt == v2.DateOnForecourt &&
                v1.RetailerSite_Id == v2.RetailerSite_Id &&
                 v1.AttentionGrabber == v2.AttentionGrabber &&
                 v1.Description == v2.Description &&
                 v1.Description2 == v2.Description2 &&
                 v1.AutotraderAdvertStatus == v2.AutotraderAdvertStatus &&
                 v1.AdvertiserAdvertStatus == v2.AdvertiserAdvertStatus &&
                 v1.LocatorAdvertStatus == v2.LocatorAdvertStatus &&
                 v1.ExportAdvertStatus == v2.ExportAdvertStatus &&
                 v1.VideoUrl == v2.VideoUrl &&
                 v1.ImageUrls == v2.ImageUrls &&
                 v1.ProfileAdvertStatus == v2.ProfileAdvertStatus &&
                  v1.OdometerReading == v2.OdometerReading &&
                  v1.DerivativeId == v2.DerivativeId &&
                  v1.Stock_Id == v2.Stock_Id;

         //we don't consider createdDate as don't want this to update    

      }

      public async Task<List<VehicleAdvertPortalOption>> GetVehicleAdvertsPortalOptions(List<int> advertsIds, DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var result = await db.VehicleAdvertPortalOptions.Include(v => v.PortalOption).Where(v => advertsIds.Contains(v.VehicleAdvert_Id)).ToListAsync();
            return result;

         }

      }
      public async Task<string?> GetAdvertDerivativeId(DealerGroupName dealerGroup, int advertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleAdverts
               .Where(x => x.RetailerSite.DealerGroup_Id == (int)dealerGroup && x.Id == advertId)
               .Select(x => x.DerivativeId)
               .FirstOrDefaultAsync();
         }
      }
   }
}
