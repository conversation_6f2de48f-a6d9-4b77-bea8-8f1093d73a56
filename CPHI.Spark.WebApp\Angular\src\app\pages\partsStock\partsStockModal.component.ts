import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CphPipe } from "../../cph.pipe";
import { ConstantsService } from "../../services/constants.service";
import { SelectionsService } from "../../services/selections.service";

@Component({
  selector: "partsStockModal",
  template: `
    <ng-template #modalRef let-modal>
      <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
          {{title}}
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div [ngClass]="constants.environment.customer" class="modal-body">

        <partsStockDetailedTable [rowData]="detailedStock"></partsStockDetailedTable>
        
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">{{constants.translatedText.Close}}</button>
      </div>
    </ng-template>
  `,
  styles: [
    `

    

    `
  ]
})
export class PartsStockModalComponent implements OnInit {
  @ViewChild("modalRef", { static: true }) modalRef: ElementRef;
  
  detailedStock: any[];
  title:string;
  siteLabel: string;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    
  ) { }

  ngOnDestroy() { }

  ngOnInit() {
    this.initParams();
  }

  initParams() {
    
  }


  openModal(){
    this.selections.triggerSpinner.next({show:false, message: this.constants.translatedText.Close})
    //launch modal
    this.modalService.open(this.modalRef, {size: 'lg', keyboard: false, ariaLabelledBy: "modal-basic-title" }).result.then(
      result => {
        //'okd'

      },
      //closed
      reason => {
        //cancelled, so no passback
      }
    );
  }
 

}
