import { Component, OnInit } from '@angular/core';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { GridOptions, GridApi, ColumnApi, RowDoubleClickedEvent, RowClickedEvent } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { AutoTraderPerformanceRatingSimpleComponent } from 'src/app/components/autoTraderPerformanceRatingSimple/autoTraderPerformanceRatingSimple.component';
import { BIChartTileDataType } from 'src/app/components/biChartTile/biChartTile.component';
import { BlobItem } from 'src/app/components/blobChart/blobItem';
import { StockItemModalComponent } from 'src/app/components/stockItemModal/stockItemModal.component';
import { StockListRow } from "../../stockList/StockListRow";
import { LeavingVehiclesService } from './leavingVehicles.service';
import { CphPipe } from 'src/app/cph.pipe';
import { RegPlateComponent } from 'src/app/_cellRenderers/regPlate.component';
import { TitleCasePipe } from '@angular/common';
import { LeavingPriceSummaryItem } from 'src/app/model/LeavingPriceSummaryItem';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AutoPriceInsightsModalComponent } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import { AnalysisDimensionTypeEnum } from "src/app/model/AnalysisDimensionTypeEnum";
import { AnalysisDimensionPipeTypeEnum } from "src/app/model/AnalysisDimensionPipeTypeEnum";
import { VehicleAdvertWithRating } from 'src/app/model/VehicleAdvertWithRating';
import { AutoTraderAdvertImage } from 'src/app/_cellRenderers/autoTraderAdvertImage.component';
import { CustomHeaderService } from 'src/app/components/customHeader/customHeader.service';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { AutopriceRendererService } from 'src/app/services/autopriceRenderer.service';
import { AutoPriceInsightsModalService } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service';
import { AutotraderImageCellComponent } from 'src/app/components/autotraderImageCell/autotraderImageCell.component';

@Component({
  selector: 'app-leavingVehicles',
  templateUrl: './leavingVehicles.component.html',
  styleUrls: ['./leavingVehicles.component.scss']
})
export class LeavingVehiclesComponent implements OnInit {
  public components: { [p: string]: any; } = {
    agColumnHeader: CustomHeaderNew,
  };

  public dataTypes = BIChartTileDataType;
  
  //Subs
  highlightChoiceMadeSub: Subscription;
  filterChoiceMadeSub: Subscription;
  filterSiteSub: Subscription;
  refreshTileSubscriber: Subscription;
  

  gridOptions: GridOptions;
  gridApi: GridApi;
  gridColumnApi: ColumnApi;


  PipeTypes = AnalysisDimensionPipeTypeEnum;
 
  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: LeavingVehiclesService,
    public cphPipe: CphPipe,
    public titleCasePipe: TitleCasePipe,
    private gridHelpersService: AGGridMethodsService,
    private modalService: NgbModal,
    private customHeader: CustomHeaderService,
    private autopriceRendererService: AutopriceRendererService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService,
    
  ) { }

  ngOnInit() {
    this.initParams()
    this.buildSubscriptions();

  }

  initParams() {
    if (!this.service.rawData) {
      this.service.initParams();
      this.service.getData();
    }else{

      this.gridOptions = this.initialiseVehicleDetailsGrid();
      this.selections.triggerSpinner.emit({show:false})
    }

  }

  toggleIncludeNewVehicles() {
    this.service.includeNewVehicles = !this.service.includeNewVehicles;
    this.service.getData();
  }

  private buildSubscriptions() {
    this.highlightChoiceMadeSub = this.service.highlightChoiceMadeEmitter.subscribe(res => {
      this.service.highlightItems();
      this.service.recalculateSummaryStats();
      if (this.gridApi) {
        this.gridApi.setRowData(this.service.rawDataHighlighted);
        this.applyGridFilterChanges();
      }
    });

    this.filterChoiceMadeSub = this.service.filterChoiceMadeEmitter.subscribe(res => {
      this.service.filterItems();
      this.service.recalculateSummaryStats();
    });

    this.refreshTileSubscriber = this.service.refreshTileEmitter.subscribe(res => {
      this.gridOptions = this.initialiseVehicleDetailsGrid();
    });
  }

  ngOnDestroy() {
    if (!!this.highlightChoiceMadeSub) { this.highlightChoiceMadeSub.unsubscribe() }
    if (!!this.filterSiteSub) this.filterSiteSub.unsubscribe();

    if (this.refreshTileSubscriber) this.refreshTileSubscriber.unsubscribe();
  }


  clearHighlights() {

    this.service.highlightChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.service.highlightItems();
    this.service.refreshTileEmitter.emit();
  }

  onRowClicked(params: RowClickedEvent<any, any>): void {
    const row:LeavingPriceSummaryItem = params.data;
    if(!row){return}
    this.autoPriceInsightsModalService.initialise(row.VehicleAdvertId,[])
    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });
  }


  initialiseVehicleDetailsGrid():GridOptions {
    return {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) =>  params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      animateRows: false,
      onRowClicked:(params)=>this.onRowClicked(params),
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getHeaderHeight();
        }
      },
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      rowData: this.service.rawData,
      onGridReady: (params) => this.onGridReady(params),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
        // floatingFilter:true,
        cellStyle: {
          display: 'flex',
          'align-items': 'center'
        },
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true
      },
      onRowDoubleClicked: (params) => this.rowDoubleClicked(params),
      columnTypes: {
        number: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true },
        label: { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter', sortable: true },
        special: { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' },
        currency: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true, cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 0) } },
        percent: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true, cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'percent', 0) } }
      },
      columnDefs: this.provideColDefs(),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      onFilterChanged: () => this.applyGridFilterChanges(),
    }
  }

  applyGridFilterChanges(){
    let filteredRows = [];
    this.gridApi.forEachNodeAfterFilter(node => filteredRows.push(node.data));

    const newBlobs = this.service.makeBlobItems(this.service.rawData, this.service.analysisDimensions);
    const lookup = {};
    filteredRows.map(x => x.VehicleAdvertId).forEach(item => {
        lookup[item] = true;
      });
      newBlobs.map(blob => {
        blob.IsActive = lookup[blob.Id];
      });
      this.service.newBlobsEmitter.emit(newBlobs);

    }
  
  rowDoubleClicked(params: RowDoubleClickedEvent) {
    const row:LeavingPriceSummaryItem=params.data;
    if(!row){return;}
    
    this.autoPriceInsightsModalService.initialise(row.VehicleAdvertId,[])
    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });

    modalRef.result.then((result) => { });
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
  }

  provideColDefs() {
    return [
      {
        headerName: '',
        colId: 'ImageURLs',
        field: 'ImageURL',
        type: 'image',
        cellRendererFramework: AutotraderImageCellComponent,
        cellRendererParams: { width: 70 },
        width: 30
      },   
      { headerName: 'Description', colId: 'Derivative', field: 'Derivative',  type: 'label', width: 125 },
      { headerName: 'VRM', colId: 'VehicleReg', field: 'VehicleReg', type: 'special', cellRenderer: (params) => this.autopriceRendererService.regPlateRenderer(params, { noMargin: true }), width: 40 },
      { headerName: 'Price Position', colId: 'FinalPricePosition', field: 'FinalPricePosition', type: 'percent', width: 30 },
      { headerName: 'Strategy Price', colId: 'RetailValuation',  field: 'RetailValuation', type: 'currency', width: 30 },
      { headerName: 'Retail Price', colId: 'FinalPrice', field: 'FinalPrice', type: 'currency', width: 30 },
      { headerName: 'Vs', colId: 'Vs', valueGetter:(params)=>this.vsColGetter(params), type: 'currency', width: 30 },
      //{ headerName: 'Target Valuation', colId: 'RetailValuation', field: 'RetailValuation', type: 'currency', width: 30 },
      { headerName: 'Performance Rating', colId: 'PerformanceRatingScore', field: 'PerformanceRatingScore', type: 'label', cellRenderer: (params) => this.autopriceRendererService.autoTraderPerformanceRatingSimpleRenderer(params), width: 40 },
      // { headerName: 'Search Appearences', colId: 'PerformanceSearchViewsYest', field: 'PerformanceSearchViewsYest', type: 'number', width: 10 },
      // { headerName: 'Advert Views', colId: 'PerformanceAdvertViewsYest', field: 'PerformanceAdvertViewsYest', type: 'number', width: 10 }
    ];
  }
  vsColGetter(params: any) {
    const row:LeavingPriceSummaryItem = params.data;
    if(!row){return ''}
    return row.FinalPrice - row.RetailValuation
  }

  getImage(params) {
    const row:LeavingPriceSummaryItem = params.data;
    if(!row){return ''}
    if(!row.ImageURL){return ''}
    
    return `<img style="height: 50px; width: 100%;" src=${row.ImageURL} />`;
  }

  setDate(event: any, dateType: string) {
    this.selections.triggerSpinner.emit({ show: true, message: 'Loading...' });

    if (dateType == 'from') {
      this.service.fromDate = event.target.value;
    } else {
      this.service.toDate = event.target.value;
    }

    this.service.getData();
  }
}
