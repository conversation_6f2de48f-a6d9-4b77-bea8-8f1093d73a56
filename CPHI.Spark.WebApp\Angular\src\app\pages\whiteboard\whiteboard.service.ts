import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { CphPipe } from 'src/app/cph.pipe';
import { Person, SiteVM } from 'src/app/model/main.model';
import { Deal, LateCostOption } from 'src/app/model/sales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDealDataService } from 'src/app/services/getDeals.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { OrderBookService } from '../orderBook/orderBook.service';
import { FakeDeal, SalesExecSummaryItem } from './whiteboard.model';

@Injectable({
  providedIn: 'root'
})
export class WhiteboardService {

  months: Array<Date>;
  dealsForEachSalesExec: Array<any>;
  mySubscription: Subscription;

  onlyLates: number = 0;
  excludeLates: number = 0;

  orderTypeIds: number[];
  vehicleTypeIds: number[];

  dealGroupingHeight: number;
  dealGroupingMaxHeight: number;
  dealGroupingCount: number;
  tileHeight: number;

  showRegPlate: boolean;
  showSiteName: boolean;

  maxTilesPerRow: number = 20;
  maxDealGroupTiles: number = 35;

  lateCostOption: LateCostOption;
  maxDealsCount: number;
  deals: Array<Deal>;

  sites: SiteVM[];
  sitesIds: number[];
  vehicleTypeTypes: string[];
  orderTypeTypes: string[];
  franchises: string[];
  salesExec: Person;
  salesExecSummaryItems: Array<SalesExecSummaryItem> = [];
  salesManagerSummaryItems: Array<SalesExecSummaryItem> = [];
  fakeTiles: Array<number>;
  managerName: string;
  managerId?: number;
  

  summaryTable: {
    financePen: number;
    productsPU: number;
    profit: number;
    dealsCount: number;
    percentDelivered: number;
    toGo: number;
  }

  deliveryDate: {
    startDate: Date,
    endDate: Date,
    monthName: string,
    amSelectingMonth: boolean,
    amSelectingCustom: boolean,
    lastSelectedMonthStartDate: Date,
  };

  orderDate: {
    startDate: Date,
    endDate: Date,
  };

  maxDeals: number;

  constructor(
    public constants: ConstantsService,
    public orderBookService: OrderBookService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public getDealData: GetDealDataService,
    public cphPipe: CphPipe

  ) {


  }



  public initParams(): void {

    if(!this.lateCostOption)
    {
      this.lateCostOption = this.constants.lateCostOptions[1]
    };

    if(!this.maxDealsCount)
    {
      this.maxDealsCount = 0;  
    };

    if(!this.deals)
    {
      this.deals = [];
    };

    if(!this.deliveryDate)
    {
      this.deliveryDate = {
        startDate:this.constants.deductTimezoneOffset(new Date(this.constants.thisMonthStart)),
        endDate: this.constants.deductTimezoneOffset(new Date(this.constants.thisMonthEnd)),
        monthName: this.constants.appStartTime.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' }),
        amSelectingMonth: true,
        amSelectingCustom: false,
        lastSelectedMonthStartDate: new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), 1),
      };
    }

    if(!this.orderDate)
    {
      this.orderDate = {
        startDate: this.constants.deductTimezoneOffset( new Date(2019, 6, 1)),
        endDate:this.constants.deductTimezoneOffset( new Date(this.constants.todayEnd)),
      };
    }

    if(!this.summaryTable)
    {
      this.summaryTable = {
        financePen: 0,
        productsPU: 0,
        profit: 0,
        dealsCount: 0,
        percentDelivered: 0,
        toGo: 0,
      }
    }

    if(!this.sites)
    {
      this.sites = [this.constants.clone(this.selections.userSite)];
      this.selections.selectedSites = [this.constants.clone(this.selections.userSite)]; 
    }

    if(!this.sitesIds)
    {
      this.sitesIds = [this.selections.userSite.SiteId];
      this.selections.selectedSitesIds = [this.selections.userSite.SiteId];
    }

    if(!this.vehicleTypeTypes)
    {
      this.vehicleTypeTypes = this.constants.vehicleTypeTypes
    } 

    if(!this.orderTypeTypes)
    {
      this.orderTypeTypes = this.constants.clone(this.constants.orderTypeTypesNoTrade);
    } 

    if(!this.franchises){
      this.franchises = this.constants.clone(this.constants.FranchiseCodes);
    }
    
  }

  public getDeals(): void {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    this.setFilters();

    this.mySubscription = this.getDealData.getWhiteboard(this.deliveryDate.startDate, this.selections.selectedSitesIds, this.orderTypeIds, this.vehicleTypeIds,
      this.franchises.toString(), this.onlyLates, this.excludeLates, this.managerId).subscribe(res => {

      this.dealsForEachSalesExec = res;

    }, error => {

      console.error("Error getting Whiteboard data: ", error);

    }, () => {

        // If no deals, return blankpage
        if(!this.dealsForEachSalesExec){  this.displayBlankPage(); return; }

        this.summariseDeals();

        // Find max number of deals for a person initially 
        // (we're only looking at real deals for now but this is ok as only need to know this for now if real deals is > 35)
        // let maxDeals = this.getInitialMaxDealCount();
      
        this.salesExecSummaryItems = this.generatePeopleSummary(this.maxDeals);
        this.salesManagerSummaryItems = this.generateManagerSummary(this.salesExecSummaryItems);
        
        // this.maxDealsCount = this.getSecondMaxDealCount();

        // Generate fake object to represent all deal tiles
        this.getFakeTiles(this.maxDealsCount);

        // Show / hide site names
        this.showOrHideSiteName();

        setTimeout(() => {
          this.selections.triggerSpinner.next({show:false});
        }, 20)

      })

  }
  generateManagerSummary(salesExecSummaryItems: SalesExecSummaryItem[]): SalesExecSummaryItem[] {
    let salesManagerSummaryItem: SalesExecSummaryItem[] = [];
    let maxDealCount = 0;

    salesExecSummaryItems.forEach(exec => {

      let totalTiles = exec.deals.length + exec.fillerDeals.length;
      if (totalTiles > maxDealCount) {
        maxDealCount = totalTiles;
      }

      const i = salesManagerSummaryItem.findIndex(s => s.managerId == exec.managerId)
      if (i > -1){
        salesManagerSummaryItem[i].dealsCount += exec.dealsCount;
      } else{
        salesManagerSummaryItem.push({
          managerId: exec.managerId,
          dealsCount: exec.dealsCount,
          managerName: exec.managerName,
          id: 0,
          person: null,
          productsPU: 0,
          profitPU: 0
        })
      }

    })
 
    this.maxDealsCount = maxDealCount / this.dealGroupingCount;
    return salesManagerSummaryItem;
    
  }

  private setFilters(): void
  {
    this.filterOrderType();

    this.filterVehicleType();

    this.filterLates();




  }

  private filterOrderType(): void 
  {
    this.orderTypeIds = [];

    // Get selection for order types
    if(this.orderTypeTypes){

      this.orderTypeTypes.forEach(y => {
        this.constants.OrderTypes.forEach(x => {

          if(x.Type == y){
            this.orderTypeIds.push(x.Id);
          }
        });
        
      });

    }
  }

  private filterLates() : void 
  {

    // Set selection criteria for API call
    if(this.lateCostOption.includeLateCosts && !this.lateCostOption.includeNormalCosts)
    {
      this.onlyLates = 1;
    }

    if(!this.lateCostOption.includeLateCosts && this.lateCostOption.includeNormalCosts)
    {
      this.excludeLates = 1;
    }
  }

  private filterVehicleType(): void
  {

    this.vehicleTypeIds = [];
    
    if(this.vehicleTypeTypes){
  
        this.vehicleTypeTypes.forEach(y => {
          this.constants.VehicleTypes.forEach(x => {
  
            if(x.Type == y){
              this.vehicleTypeIds.push(x.Id);
            }
          });
          
        });
    
    }

    this.vehicleTypeIds = [...new Set(this.vehicleTypeIds)];
  }

  private generatePeopleSummary(maxDeals: number): Array<SalesExecSummaryItem> 
  {

    let salesExecSummaryItems: Array<SalesExecSummaryItem> = [];

    this.dealsForEachSalesExec.forEach(salesExec => {

        let deals = salesExec.Deals;
        let dealCount = this.constants.sum(deals.map(e => e.Units));
        let dealProfits = this.constants.sum(deals.map(e => e.TotalNLProfit));
        let productsTotal = this.constants.sum(deals.map(e => e.TotalProductCount));
        let productsPU = this.constants.div(productsTotal, dealCount);
        let profitPU = this.constants.div(dealProfits, dealCount);
        let dealsOnFinance = deals.filter(s => s.IsFinanced);
        let financePen = this.constants.div(this.constants.sum(dealsOnFinance.map(x => x.Units)), dealCount)
        let target = this.getTargetForRole(deals[0].Role)

        // Add extra fakeDeals to generate tiles
        let fakeDeals = this.createFakeDeals(target, dealCount);

        // Tag last real deal as lastRealDeal
        if (deals.length > 0) { deals[deals.length - 1].isLastRealDeal = true; }

        // Adds the fake deals to the real deals and calculates index
        let dealsIncFakeDeals = deals.concat(fakeDeals);
        dealsIncFakeDeals.forEach((deal, i) => {
          deal.index = i;
          deal.isDealBroughtIn = deal.OrderDate && new Date(deal.OrderDate).getTime() < this.deliveryDate.startDate.getTime()
        })

        this.dealGroupingCount =  this.getDealGroupingCount(maxDeals);

        let dealGroups = this.createDealGroups(dealsIncFakeDeals);
        
        salesExecSummaryItems.push({
          id: salesExec.Salesman_Id,
          person: null,
          dealsCount: dealCount,
          productsPU: productsPU,
          profitPU: profitPU,
          dealGroups: dealGroups.slice(0, this.maxDealGroupTiles), //limit to just 35 pairs max so fleet guys don't blow out the grid
          deals: deals,
          fillerDeals: fakeDeals,
          financePen: financePen,
          profit: dealProfits,
          target: target,
          roleName: deals[0].Role,
          currentSite: deals[0].SalesmanSite,
          salesmanName: deals[0].Salesman,
          managerName: deals[0].ManagerName,
          managerId: deals[0].ManagerId,
        })
        
    });

    this.calculateHeights(salesExecSummaryItems);

    // Work out how many rows there will be which is salesmen count x dealGroupingCount
    let rows: number = salesExecSummaryItems.length * this.dealGroupingCount;
    let dealCounts: number = this.getDealCounts(salesExecSummaryItems);

    if (this.showRegPlateCheck(rows, dealCounts)) { this.showRegPlate = false } else { this.showRegPlate = true; }

    // If b.target - a.target is 0 it uses the other method
    return salesExecSummaryItems.sort((a, b) => b.dealsCount - a.dealsCount || b.target - a.target) 
  }

  private displayBlankPage(): void 
  {
    this.summaryTable = null;
    this.salesExecSummaryItems = null;
    this.salesManagerSummaryItems = null;
    this.selections.triggerSpinner.next({show:false});
  }

  private summariseDeals(): void {

    let count: number = 0;
    let profit: number = 0;
    let products: number = 0;
    let onFinCount: number = 0;
    let delivered: number = 0;

    if(this.dealsForEachSalesExec){
      let maxDeals: number = 0;

      this.dealsForEachSalesExec.forEach(salesman => {

        let dealCount = salesman.Deals.length;
        if (dealCount > maxDeals) { maxDeals = dealCount }

        salesman.Deals.forEach(deal => {
  
          if(deal.IsFinanced){
            onFinCount = onFinCount + 1;
          }
  
          if(deal.IsDelivered){
            delivered = delivered + 1;
          }
  
          if(deal.TotalProductCount){
            products = products + deal.TotalProductCount;
          }
  
          if(deal.Units){
            count = count + deal.Units;
          }
  
          if(deal.TotalNLProfit){
            profit = profit + deal.TotalNLProfit;
          }
          
        });
  
        this.summaryTable = {
          dealsCount: count,
          productsPU: this.constants.div(products, count),
          profit: profit,
          financePen: this.constants.div(onFinCount, count),
          percentDelivered: this.constants.div(delivered, count),
          toGo: count - delivered,
        }
        
      });

      this.maxDeals = maxDeals;

    }
    else{

      this.summaryTable = null;

    }

  }

  private createDealGroups(dealsIncFakeDeals): Deal[][]
  {
    let dealGroups = [];

    //put all dealsIncFakeDeals in pairs into dealGroups
    for (let i = 0; i < Math.ceil(dealsIncFakeDeals.length / this.dealGroupingCount); i++) {
      let groupOfDeals = []
      groupOfDeals.push(dealsIncFakeDeals[(i * this.dealGroupingCount + 0)]) //push the first of the group
      if (this.dealGroupingCount > 1) {
        if (dealsIncFakeDeals[(i * this.dealGroupingCount + 1)]) { groupOfDeals.push(dealsIncFakeDeals[(i * this.dealGroupingCount + 1)]) } //push the second of the group if it exists
      }
      dealGroups.push(groupOfDeals);
    }

    return dealGroups;
  }

  private showRegPlateCheck(rows: number, dealCounts: number) : boolean
  {
    return (rows > this.maxTilesPerRow || dealCounts > 50)
  }

  private getDealCounts(salesExecSummaryItems: SalesExecSummaryItem[]): number { 
    return salesExecSummaryItems.length > 1 ? salesExecSummaryItems.map(x => x.dealsCount).reduce(function (a, b) { return Math.max(a, b) }) : 0
  }

  private getDealGroupingCount(maxDeals: number): number { return maxDeals > this.maxTilesPerRow ? 2 : 1; }

  private getTargetForRole(role: string): number {

    let target: number = 20;

      if (role) {
        switch (role) {
          case ('New'):
            target = Math.round((200 / 12))
            break;
          case ('Used'):
            target = Math.round((240 / 12))
            break;
          case ('NewUsed'):
            target = Math.round((220 / 12))
            break;
          case ('Fleet'):
            target = Math.round((250 / 12))
            break;
          default:
            target = 20
            break;
        }

      }

      return target;
  }

  private getFakeTiles(maxDealCount: number): void
  {
    this.fakeTiles = [];

    for (let i = 0; i < maxDealCount; i++) {
      this.fakeTiles.push(
        1,
      )
    }
    this.fakeTiles = this.fakeTiles.slice(0, this.maxDealGroupTiles) //limit to just 35 pairings to prevent fleet guys blowing up the grid
  }

  private showOrHideSiteName(): void
  {
    if (this.sitesIds.length > 1) {
      this.showSiteName = true
      this.salesExecSummaryItems.forEach(x => {
       // x.person.CurrentSite = this.constants.Sites.find(a => a.Id == x.person.CurrentSite.Id)
      })

    } else {
      this.showSiteName = false
    }
  }

  private getInitialMaxDealCount(): number
  {

    let maxDeals: number = 0;
    
    if(this.dealsForEachSalesExec){

      this.dealsForEachSalesExec.forEach(salesExec => {
        let dealCount = salesExec.Deals.length;
        if (dealCount > maxDeals) { maxDeals = dealCount }
      });

    }

    return maxDeals;
  }

  private getSecondMaxDealCount(): number
  {
    let maxDealCount = 0;

    this.salesExecSummaryItems.forEach(s => {
      let totalTiles = s.deals.length + s.fillerDeals.length;
      if (totalTiles > maxDealCount) {
        maxDealCount = totalTiles;
      }
    })
    
    return maxDealCount / this.dealGroupingCount;
  }

  private createFakeDeals(target: number, dealCount: number): FakeDeal[]
  {
    let extraTilesNeeded:number = target - dealCount
    let fakeDeals: FakeDeal[] = [];

    for (let i = 0; i < extraTilesNeeded; i++) {
      fakeDeals.push(
        { isFiller: true, }
      );
    }

    return fakeDeals;
  }

  private calculateHeights(salesExecSummaryItems:SalesExecSummaryItem[]): void
  {
    this.dealGroupingHeight = 78 / this.salesExecSummaryItems.length // is in vh
    this.dealGroupingMaxHeight = 4 * this.dealGroupingCount //is in em
    this.tileHeight = (78 / this.dealGroupingCount) / salesExecSummaryItems.length
  }





}
