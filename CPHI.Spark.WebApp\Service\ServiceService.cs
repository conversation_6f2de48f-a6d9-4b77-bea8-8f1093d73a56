﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;
using CPHI.Spark.Model.Services;
using CPHI.Spark.WebApp.Caches;
using CPHI.Spark.WebApp.DataAccess;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess;

namespace CPHI.Spark.WebApp.Service
{


   public interface IServiceService
   {
      Task<List<ServiceChannelSplit>> GetServiceChannelSplits(string siteIds, DateTime monthCommencing, int userId);
      Task<List<ServiceDailyChaseItem>> GetServiceDailyChaseChartData(string siteIds, DateTime monthCommencing, int userId);
      Task<List<ServiceDailyChaseItem>> GetServiceDailyChaseChartDataSpain(string siteIds, DateTime monthCommencing, int userId);
      Task<List<AftersalesDailyDoneVsTarget>> GetServiceDailyDoneVsTarget(string siteIds, DateTime monthCommencing, int userId);
      Task<AftersalesRunRateSummary> GetServiceRunRate(string siteIds, DateTime monthCommencing, int userId);
      Task<List<ServiceSiteRow>> GetServiceSiteRows(DateTime monthCommencing, AftersalesTimePeriod timePeriod, string channelsString, int userId);
      Task<AftersalesVsTarget> GetServiceVsTarget(string siteIds, DateTime monthCommencing, int userId);
      Task<DashboardGuage> GetServiceSummary(AftersalesTimePeriod timePeriod, string siteIds, int userId, DateTime? monthCommencing);
      Task<IEnumerable<ServiceDailySalesSiteRow>> GetServiceDailySales(DateTime startDate, string channels, int userId);
   }

   public class ServiceService : IServiceService
   {
      private readonly IServiceDataAccess serviceDataAccess;
      private readonly IServiceSummaryCache serviceSummaryCache;
      private readonly IWorkingDaysService workingDaysService;
      private readonly IDashboardDataAccess dashboardDataAccess;
      private readonly ISitesService siteService;
      private readonly ITranslationService translationService;
      private readonly IUserService userService;
      private readonly IConfiguration configuration;



      public ServiceService(
          IServiceDataAccess serviceDataAccess,
          IWorkingDaysService workingDaysService,
          IDashboardDataAccess dashboardDataAccess,
          ISitesService siteService,
          ITranslationService translationService,
          IServiceSummaryCache serviceSummaryCache,
          IUserService userService
,
          IConfiguration configuration)
      {
         this.serviceDataAccess = serviceDataAccess;
         this.workingDaysService = workingDaysService;
         this.dashboardDataAccess = dashboardDataAccess;
         this.siteService = siteService;
         this.translationService = translationService;
         this.serviceSummaryCache = serviceSummaryCache;
         this.userService = userService;
         this.configuration = configuration;
      }



      public async Task<List<ServiceSiteRow>> GetServiceSiteRows(DateTime monthCommencing, AftersalesTimePeriod timePeriod, string channelsString, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var siteDataAccess = new SiteDataAccess(_connectionString);

         //data items
         IEnumerable<WipSummaryBySite> wips = await dashboardDataAccess.GetWipSummaryBySite(userId, showAllSites, dealerGroup);
         IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, showAllSites)).OrderBy(x => x.SortOrder).Where(x => x.IsService && x.IsActive);
         var siteIds = sites.Where(x => x.IsActive && x.IsService).Select(x => x.SiteId).ToList();

         List<SiteElapsedWorkDaysAndTarget> perSiteDaysAndTarget = new List<SiteElapsedWorkDaysAndTarget>();


         if (timePeriod == AftersalesTimePeriod.MTD)
         {
            perSiteDaysAndTarget.AddRange(await BuildoutDaysAndTargetForSites(monthCommencing, monthCommencing, timePeriod, channelsString, userId, siteIds, showAllSites));
         }
         else if (timePeriod == AftersalesTimePeriod.Yesterday)
         {
            DateTime yesterdayMonthCommencing = new DateTime(DateTime.UtcNow.AddDays(-1).Year, DateTime.UtcNow.AddDays(-1).Month, 1);

            perSiteDaysAndTarget.AddRange(await BuildoutDaysAndTargetForSites(yesterdayMonthCommencing, monthCommencing, timePeriod, channelsString, userId, siteIds, showAllSites));
         }

         else if (timePeriod == AftersalesTimePeriod.WTD)
         {
            //special approach as is possible that the WTD could span two months.
            DateTime wtdStart = StaticHelpersService.StartOfWeek(DateTime.Now, DayOfWeek.Monday);

            DateTime wtdEnd = wtdStart.AddDays(6);
            if (wtdStart.Month != wtdEnd.Month)
            {
               //complicated.  have to get targets for each month and working days for each month
               //results for part of week that's last month
               DateTime monthCommencingFirstMonth = new DateTime(wtdStart.Year, wtdStart.Month, 1);
               DateTime monthCommencingSecondMonth = new DateTime(wtdStart.Year, wtdStart.Month, 1);
               IEnumerable<ServiceSiteRowDataItem> doneAndTargetFirstMonth = await serviceDataAccess.GetServiceMonthlyDoneAndTargetBySite(monthCommencingFirstMonth, timePeriod, channelsString, userId, showAllSites, dealerGroup);
               IEnumerable<ServiceSiteRowDataItem> doneAndTargetSecondMonth = await serviceDataAccess.GetServiceMonthlyDoneAndTargetBySite(monthCommencingSecondMonth, timePeriod, channelsString, userId, showAllSites, dealerGroup);
               IEnumerable<SiteAndElapsedWorkDays> daysPerSiteFirstMonth = await workingDaysService.GetElapsedDaysForSites(monthCommencingFirstMonth, timePeriod, siteIds, userId, showAllSites, dealerGroup);
               IEnumerable<SiteAndElapsedWorkDays> daysPerSiteSecondMonth = await workingDaysService.GetElapsedDaysForSites(monthCommencingSecondMonth, timePeriod, siteIds, userId, showAllSites, dealerGroup);

               IEnumerable<ServiceSiteRowDataItem> doneAndTargetFullMonth = await serviceDataAccess.GetServiceMonthlyDoneAndTargetBySite(monthCommencing, timePeriod, channelsString, userId, showAllSites, dealerGroup);

               foreach (var doneAndTargetFirstMonthThisSite in doneAndTargetFirstMonth)
               {
                  var daysThisSiteFirstMonth = daysPerSiteFirstMonth.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);
                  var doneAndTargetSecondMonthThisSite = doneAndTargetSecondMonth.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);
                  var fullMonthTarget = doneAndTargetFullMonth.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);
                  var daysThisSiteSecondMonth = daysPerSiteSecondMonth.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);
                  //work out, and add together
                  perSiteDaysAndTarget.Add(new SiteElapsedWorkDaysAndTarget()
                  {
                     SiteId = doneAndTargetFirstMonthThisSite.SiteId,
                     WorkingDaysElapsed = daysThisSiteFirstMonth.WorkingDaysElapsed + daysThisSiteSecondMonth.WorkingDaysElapsed,
                     WorkingDaysInMonth = daysThisSiteFirstMonth.WorkingDaysInMonth + daysThisSiteSecondMonth.WorkingDaysInMonth,
                     TotalDone = doneAndTargetFirstMonthThisSite.Actual + doneAndTargetSecondMonthThisSite.Actual,
                     TargetPerDay = (daysThisSiteFirstMonth.WorkingDaysInMonth != 0 ? daysThisSiteFirstMonth.WorkingDaysElapsed / daysThisSiteFirstMonth.WorkingDaysInMonth * doneAndTargetFirstMonthThisSite.Target : 0) +
                                      (daysThisSiteSecondMonth.WorkingDaysInMonth != 0 ? daysThisSiteSecondMonth.WorkingDaysElapsed / daysThisSiteSecondMonth.WorkingDaysInMonth * doneAndTargetSecondMonthThisSite.Target : 0),
                     FullMonthTarget = fullMonthTarget.Target,
                  });
               }
            }
            else
            {
               //not complicated, start and end all in same month
               DateTime monthCommencingForWTDStart = new DateTime(wtdStart.Year, wtdStart.Month, 1);

               perSiteDaysAndTarget.AddRange(await BuildoutDaysAndTargetForSites(wtdStart, monthCommencing, timePeriod, channelsString, userId, siteIds, showAllSites));
            }
         }


         //walk through sites making results
         List<ServiceSiteRow> results = new List<ServiceSiteRow>();

         foreach (var site in sites)
         {
            var thisSiteWip = wips.FirstOrDefault(x => x.SiteId == site.SiteId);
            SiteElapsedWorkDaysAndTarget thisSiteStats = perSiteDaysAndTarget.FirstOrDefault(x => x.SiteId == site.SiteId);

            //This site done
            decimal thisSiteDone = thisSiteStats != null ? thisSiteStats.TotalDone : 0;
            decimal thisSiteTarget = thisSiteStats != null ? thisSiteStats.WorkingDaysInMonth * thisSiteStats.TargetPerDay : 0;
            decimal thisSiteFullMonthTarget = thisSiteStats != null ? thisSiteStats.FullMonthTarget : 0;

            //This site working days
            decimal thisSiteWorkingDaysElapsed = thisSiteStats != null ? thisSiteStats.WorkingDaysElapsed : 0;
            decimal thisSiteWorkingDaysInMonth = thisSiteStats != null ? thisSiteStats.WorkingDaysInMonth : 0;
            decimal thisSiteWorkingDaysRemaining = thisSiteWorkingDaysInMonth - thisSiteWorkingDaysElapsed;

            //Remaining
            decimal thisSiteRemainingTargetToGo = thisSiteTarget - thisSiteDone;
            decimal thisSiteTargetToDate = thisSiteWorkingDaysInMonth != 0 ? thisSiteTarget * thisSiteWorkingDaysElapsed / thisSiteWorkingDaysInMonth : 0;

            //calcs
            decimal donePerDay = thisSiteWorkingDaysElapsed > 0 ? thisSiteDone / thisSiteWorkingDaysElapsed : 0;
            decimal requiredPerDay = thisSiteWorkingDaysRemaining > 0 ? thisSiteRemainingTargetToGo / thisSiteWorkingDaysRemaining : 0;


            results.Add(new ServiceSiteRow()
            {
               RegionDescription = site.RegionDescription,
               SiteId = site.SiteId,
               RegionId = (int)site.RegionId,
               Label = site.SiteDescription,
               IsSite = true,
               IsRegion = false,
               IsTotal = false,

               MonthTarget = Math.Round(thisSiteFullMonthTarget, 1, MidpointRounding.AwayFromZero),
               TargetToDate = Math.Round(thisSiteTargetToDate, 1, MidpointRounding.AwayFromZero),
               DoneToDate = Math.Round(thisSiteDone, 1, MidpointRounding.AwayFromZero),
               VsToDate = Math.Round(thisSiteDone - thisSiteTargetToDate, 1, MidpointRounding.AwayFromZero),
               AchievementToDate = Math.Round(thisSiteTargetToDate > 0 ? thisSiteDone / thisSiteTargetToDate : 0, 3, MidpointRounding.AwayFromZero),

               TargetPerDay = Math.Round(thisSiteWorkingDaysElapsed > 0 ? thisSiteTargetToDate / thisSiteWorkingDaysElapsed : 0, 1, MidpointRounding.AwayFromZero),
               DonePerDay = Math.Round(thisSiteWorkingDaysElapsed > 0 ? thisSiteDone / thisSiteWorkingDaysElapsed : 0, 1, MidpointRounding.AwayFromZero),
               RequiredPerDay = Math.Round(requiredPerDay, 1, MidpointRounding.AwayFromZero),
               StepUpPerDay = Math.Round(requiredPerDay - donePerDay, 1, MidpointRounding.AwayFromZero),

               LessThanThirtyDays = thisSiteWip != null ? thisSiteWip.LessThanThirtyDays : 0,
               ThirtyToSixtyDays = thisSiteWip != null ? thisSiteWip.ThirtyToSixtyDays : 0,
               GreaterThanSixtyDays = thisSiteWip != null ? thisSiteWip.GreaterThanSixtyDays : 0,
            });


         }

         //add total
         decimal doneToDate = results.Select(x => x.DoneToDate).Sum();
         decimal targetToDate = results.Select(x => x.TargetToDate).Sum();


         var totalItem = new ServiceSiteRow()
         {
            SiteId = 0,
            RegionId = 0,
            Label = await translationService.GetTranslationForProperty(userId, "TotalSite"),
            IsSite = false,
            IsRegion = false,
            IsTotal = true,
         };


         foreach (var item in results.Where(x => x.IsSite))
         {
            AccumulateItemIntoNewItem(totalItem, item);
         }

         totalItem.AchievementToDate = Math.Round(totalItem.TargetToDate > 0 ? totalItem.DoneToDate / totalItem.TargetToDate : 0, 3, MidpointRounding.AwayFromZero);

         results.Add(totalItem);


         //add regions
         var byRegion = results.Where(x => x.IsSite).ToLookup(x => x.RegionDescription);

         foreach (var regionGrouping in byRegion)
         {

            doneToDate = regionGrouping.Select(x => x.DoneToDate).Sum();
            targetToDate = regionGrouping.Select(x => x.TargetToDate).Sum();

            var regionItem = new ServiceSiteRow()
            {
               SiteId = 0,
               RegionId = 0,
               Label = regionGrouping.First().RegionDescription,
               IsSite = false,
               IsRegion = true,
               IsTotal = false,
            };

            foreach (var item in regionGrouping)
            {
               AccumulateItemIntoNewItem(regionItem, item);
            }
            ;

            regionItem.AchievementToDate = Math.Round(regionItem.TargetToDate > 0 ? regionItem.DoneToDate / regionItem.TargetToDate : 0, 3, MidpointRounding.AwayFromZero);


            results.Add(regionItem);

         }

         //not sure why the normal regions work doesn't pick this up.   leave until test Vindis.

         //bool isVindis = Startup.Configuration[$"{ configAppSettingsSectionName }:{configEnv}"] == "vindis";

         //if(isVindis)
         //{
         //    // Add the few extra brands for this table
         //    List<ServiceSiteRow> bentley = results.Where(x => x.IsSite && x.Label.Contains("Bentley")).ToList();
         //    results = SumNewBrand(bentley, results, "Bentley", 5);

         //    List<ServiceSiteRow> ducati = results.Where(x => x.IsSite && x.Label.Contains("Ducati")).ToList();
         //    results = SumNewBrand(ducati, results, "Ducati", 6);

         //    List<ServiceSiteRow> seat = results.Where(x => x.IsSite && x.Label.Contains("SEAT")).ToList();
         //    results = SumNewBrand(seat, results, "SEAT", 7);

         //    List<ServiceSiteRow> autonow = results.Where(x => x.IsSite && x.Label.Contains("AutoNow")).ToList();
         //    results = SumNewBrand(autonow, results, "AutoNow", 8);

         //    List<ServiceSiteRow> group = results.Where(x => x.IsSite && x.Label.Contains("Group")).ToList();
         //    results = SumNewBrand(group, results, "Group", 9);
         //}


         return results;
      }

      private async Task<List<SiteElapsedWorkDaysAndTarget>> BuildoutDaysAndTargetForSites(DateTime periodStart, DateTime monthStart, AftersalesTimePeriod timePeriod, string channelsString, int userId, List<int> siteIds, bool showAllSites)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         IEnumerable<ServiceSiteRowDataItem> doneSoFar = await serviceDataAccess.GetServiceMonthlyDoneAndTargetBySite(periodStart, timePeriod, channelsString, userId, showAllSites, dealerGroup);
         IEnumerable<ServiceSiteRowDataItem> doneAndTargetFullMonth = await serviceDataAccess.GetServiceMonthlyDoneAndTargetBySite(monthStart, AftersalesTimePeriod.MTD, channelsString, userId, showAllSites, dealerGroup);
         IEnumerable<SiteAndElapsedWorkDays> daysPerSite = await workingDaysService.GetElapsedDaysForSites(periodStart, timePeriod, siteIds, userId, showAllSites, dealerGroup);

         List<SiteElapsedWorkDaysAndTarget> results = new List<SiteElapsedWorkDaysAndTarget>();

         try
         {
            foreach (var item in doneSoFar)
            {
               var thisSiteDays = daysPerSite.FirstOrDefault(x => x.SiteId == item.SiteId);
               var thisSiteFullMonthTarget = doneAndTargetFullMonth.FirstOrDefault(x => x.SiteId == item.SiteId);

               if (thisSiteDays == null) { continue; }

               results.Add(new SiteElapsedWorkDaysAndTarget()
               {
                  SiteId = item.SiteId,
                  WorkingDaysElapsed = thisSiteDays.WorkingDaysElapsed,
                  WorkingDaysInMonth = thisSiteDays.WorkingDaysInMonth,
                  TargetPerDay = thisSiteDays.WorkingDaysInMonth != 0 ? thisSiteFullMonthTarget.Target / thisSiteDays.WorkingDaysInMonth : 0,
                  TotalDone = item.Actual,
                  FullMonthTarget = thisSiteFullMonthTarget.Target
               });
            }
         }
         catch (Exception e)
         {
            throw;
         }
         ;

         return results;
      }

      private static void AccumulateItemIntoNewItem(ServiceSiteRow totalItem, ServiceSiteRow item)
      {
         totalItem.MonthTarget += item.MonthTarget;
         totalItem.TargetToDate += item.TargetToDate;
         totalItem.DoneToDate += item.DoneToDate;
         totalItem.VsToDate += item.VsToDate;
         totalItem.AchievementToDate += item.AchievementToDate;

         totalItem.TargetPerDay += item.TargetPerDay;
         totalItem.DonePerDay += item.DonePerDay;
         totalItem.RequiredPerDay += item.RequiredPerDay;
         totalItem.StepUpPerDay += item.StepUpPerDay;

         totalItem.LessThanThirtyDays += item.LessThanThirtyDays;
         totalItem.ThirtyToSixtyDays += item.ThirtyToSixtyDays;
         totalItem.GreaterThanSixtyDays += item.GreaterThanSixtyDays;
      }

      private List<ServiceSiteRow> SumNewBrand(List<ServiceSiteRow> input, List<ServiceSiteRow> results, string brand, int regionId)
      {
         decimal doneToDate = input.Select(x => x.DoneToDate).Sum();
         decimal targetToDate = input.Select(x => x.TargetToDate).Sum();

         results.Add(new ServiceSiteRow()
         {
            SiteId = 0,
            RegionId = regionId,
            Label = brand,
            IsSite = false,
            IsRegion = true,
            IsTotal = false,

            MonthTarget = input.Select(x => x.MonthTarget).Sum(),
            TargetToDate = targetToDate,
            DoneToDate = doneToDate,
            VsToDate = input.Select(x => x.VsToDate).Sum(),
            AchievementToDate = Math.Round(targetToDate > 0 ? doneToDate / targetToDate : 0, 3, MidpointRounding.AwayFromZero),

            TargetPerDay = input.Select(x => x.TargetPerDay).Sum(),
            DonePerDay = input.Select(x => x.DonePerDay).Sum(),
            RequiredPerDay = input.Select(x => x.RequiredPerDay).Sum(),
            StepUpPerDay = input.Select(x => x.StepUpPerDay).Sum(),

            LessThanThirtyDays = input.Select(x => x.LessThanThirtyDays).Sum(),
            ThirtyToSixtyDays = input.Select(x => x.ThirtyToSixtyDays).Sum(),
            GreaterThanSixtyDays = input.Select(x => x.GreaterThanSixtyDays).Sum(),
         });

         return results;
      }


      public async Task<List<ServiceChannelSplit>> GetServiceChannelSplits(string siteIds, DateTime monthCommencing, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         //get data
         IEnumerable<ServiceChannelSplit> done = (await dashboardDataAccess.GetServiceChannelSplits(siteIds, monthCommencing, userId, showAllSites, dealerGroup)).OrderBy(x => x.Order);

         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         var daysElapsed = await workingDaysService.GetWorkingDaysElapsedInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         List<ServiceChannelSplit> results = new List<ServiceChannelSplit>();
         //walk through to add recovery rate and hours per day

         //labour ones
         foreach (var item in done.Where(x => x.IsLabour))
         {
            results.Add(new ServiceChannelSplit()
            {
               Channel = item.Channel,
               Turnover = Math.Round(item.Turnover, 2, MidpointRounding.AwayFromZero),
               SoldHours = Math.Round(item.SoldHours, 2, MidpointRounding.AwayFromZero),
               HoursPerDay = Math.Round(daysElapsed > 0 ? item.SoldHours / daysElapsed : 0, 2, MidpointRounding.AwayFromZero),
               RecoveryRate = Math.Round(item.SoldHours > 0 ? item.Turnover / item.SoldHours : 0, 2, MidpointRounding.AwayFromZero),
               IsLabour = true,
               IsTotal = false,
            });
         }

         //add labour total
         decimal labourTurnover = done.Where(x => x.IsLabour).Select(x => x.Turnover).Sum();
         decimal labourHours = done.Where(x => x.IsLabour).Select(x => x.SoldHours).Sum();
         results.Add(new ServiceChannelSplit()
         {
            Channel = await translationService.GetTranslationForProperty(userId, "LabourTotal"),
            Turnover = Math.Round(labourTurnover, 2, MidpointRounding.AwayFromZero),
            SoldHours = Math.Round(labourHours, 2, MidpointRounding.AwayFromZero),
            HoursPerDay = Math.Round(daysElapsed > 0 ? labourHours / daysElapsed : 0, 2, MidpointRounding.AwayFromZero),
            RecoveryRate = Math.Round(labourHours > 0 ? labourTurnover / labourHours : 0, 2, MidpointRounding.AwayFromZero),
            IsLabour = true,
            IsTotal = true,
         });

         //add non-labour
         var retailHoursItem = done.FirstOrDefault(x => x.Channel == "Retail");
         decimal retailHours = retailHoursItem != null ? retailHoursItem.SoldHours : 0;
         foreach (var item in done.Where(x => !x.IsLabour))
         {
            results.Add(new ServiceChannelSplit()
            {
               Channel = item.Channel,
               Turnover = Math.Round(item.Turnover, 2, MidpointRounding.AwayFromZero),
               SoldHours = 0,
               HoursPerDay = 0,
               RecoveryRate = Math.Round(retailHours > 0 ? item.Turnover / retailHours : 0, 2, MidpointRounding.AwayFromZero),  //divide these by retailHours 
               IsLabour = false,
               IsTotal = false,
            });
         }

         //add total
         decimal totalTurnover = done.Select(x => x.Turnover).Sum();
         decimal totalHours = done.Select(x => x.SoldHours).Sum();
         results.Add(new ServiceChannelSplit()
         {
            Channel = "Total",
            Turnover = Math.Round(totalTurnover, 2, MidpointRounding.AwayFromZero),
            SoldHours = Math.Round(totalHours, 2, MidpointRounding.AwayFromZero),
            HoursPerDay = Math.Round(daysElapsed > 0 ? totalHours / daysElapsed : 0, 2, MidpointRounding.AwayFromZero),
            RecoveryRate = Math.Round(totalHours > 0 ? totalTurnover / totalHours : 0, 2, MidpointRounding.AwayFromZero),
            IsLabour = false,
            IsTotal = true,
         });

         return results;

      }


      public async Task<AftersalesRunRateSummary> GetServiceRunRate(string siteIds, DateTime monthCommencing, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         //get data
         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         decimal daysElapsed = await workingDaysService.GetWorkingDaysElapsedInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);
         decimal daysInMonth = await workingDaysService.GetWorkingDaysInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);
         decimal daysRemaining = daysInMonth - daysElapsed;


         var doneByDay = await dashboardDataAccess.GetServiceDailyDone(siteIds, monthCommencing, userId, showAllSites, false, dealerGroup);
         var doneTotal = doneByDay.Select(x => x.Done).Sum();
         var targetFullMonth = await dashboardDataAccess.GetServiceMonthlyTarget(siteIds, monthCommencing, userId, showAllSites, false, dealerGroup);

         return new AftersalesRunRateSummary()
         {
            WorkingDaysGone = daysElapsed,
            WorkingDaysTarget = daysInMonth,
            WorkingDaysRemaining = daysRemaining,

            TurnoverDone = doneTotal,
            TurnoverTarget = targetFullMonth,
            TurnoverNeed = targetFullMonth - doneTotal,

            TurnoverPerDayDone = Math.Round(daysElapsed > 0 ? doneTotal / daysElapsed : 0, 2, MidpointRounding.AwayFromZero),
            TurnoverPerDayTarget = Math.Round(targetFullMonth / daysInMonth, 2, MidpointRounding.AwayFromZero),
            TurnoverPerDayNeed = Math.Round(daysRemaining > 0 ? ((targetFullMonth - doneTotal) / daysRemaining) : 0, 2, MidpointRounding.AwayFromZero)
         };

      }


      public async Task<List<AftersalesDailyDoneVsTarget>> GetServiceDailyDoneVsTarget(string siteIds, DateTime monthCommencing, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;
         bool onlyLabour = dealerGroup == Model.DealerGroupName.Vindis;

         //get data
         decimal target = await dashboardDataAccess.GetServiceMonthlyTarget(siteIds, monthCommencing, userId, showAllSites, onlyLabour, dealerGroup);
         IEnumerable<ServiceDailyDone> done = await dashboardDataAccess.GetServiceDailyDone(siteIds, monthCommencing, userId, showAllSites, onlyLabour, dealerGroup);
         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();

         workingDaysService.GetWeekendOpening(siteIdsAsList, userId, out decimal saturdayOpening, out decimal sundayOpening);
         IEnumerable<DayAndWorkingDayValue> dailyWorkingDays = await workingDaysService.GetDailyWorkingDaysForMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         decimal workingDaysInMonth = dailyWorkingDays.Select(x => x.WorkingDayValue).Sum();

         //walk through days building up results
         List<AftersalesDailyDoneVsTarget> results = new List<AftersalesDailyDoneVsTarget>();
         DateTime currentDay = monthCommencing.Date;
         decimal vsCum = 0;
         while (currentDay.Month == monthCommencing.Month)
         {
            var thisDayDoneItem = done.FirstOrDefault(x => x.Day == currentDay);
            decimal thisDayDone = thisDayDoneItem != null ? thisDayDoneItem.Done : 0;
            var thisDayTarget = currentDay.Date > DateTime.Now.Date.AddDays(-1) ? 0 : dailyWorkingDays.First(x => x.DayDate == currentDay).WorkingDayValue / workingDaysInMonth * target;
            vsCum += thisDayDone - thisDayTarget;
            results.Add(new AftersalesDailyDoneVsTarget()
            {
               //Label = currentDay.ToString("ddd dd"),
               Target = Math.Round(thisDayTarget, MidpointRounding.AwayFromZero),
               Done = Math.Round(thisDayDone, 2, MidpointRounding.AwayFromZero),
               VsTarget = Math.Round(thisDayDone - thisDayTarget, MidpointRounding.AwayFromZero),
               VsTargetCum = Math.Round(vsCum, MidpointRounding.AwayFromZero),
               Day = currentDay,
               IsWeekendOrPublicHoliday = (currentDay.DayOfWeek == DayOfWeek.Saturday || currentDay.DayOfWeek == DayOfWeek.Sunday),
               IsAPastDay = currentDay.Date < DateTime.UtcNow.Date
            });

            currentDay = currentDay.AddDays(1);
         }

         decimal totalDone = results.Select(x => x.Done).Sum();
         decimal totalTarget = results.Select(x => x.Target).Sum();

         //add WTD
         decimal wtdTarget = 0;
         decimal wtdDone = 0;
         if (DateTime.Now.Month == monthCommencing.Month && DateTime.Now.Year == monthCommencing.Year)
         {
            //we are looking at this month so work out WTD
            currentDay = DateTime.Now.Date.AddDays(-1);
            while (currentDay.DayOfWeek != DayOfWeek.Sunday && currentDay.Month == DateTime.Now.Month)
            {
               var thisDayDoneItem = done.FirstOrDefault(x => x.Day == currentDay);
               decimal thisDayDone = thisDayDoneItem != null ? thisDayDoneItem.Done : 0;
               var thisDayTarget = dailyWorkingDays.First(x => x.DayDate == currentDay).WorkingDayValue / workingDaysInMonth * target;
               wtdDone += thisDayDone;
               wtdTarget += thisDayTarget;
               currentDay = currentDay.AddDays(-1);
            }

            results.Add(new AftersalesDailyDoneVsTarget()
            {
               Target = Math.Round(wtdTarget, MidpointRounding.AwayFromZero),
               Done = Math.Round(wtdDone, MidpointRounding.AwayFromZero),
               VsTarget = Math.Round(wtdDone - wtdTarget, MidpointRounding.AwayFromZero),
               VsTargetCum = 0,
               Day = DateTime.Now,
               IsWTD = true,
               IsAPastDay = true,
            });
         }

         //add Total
         results.Add(new AftersalesDailyDoneVsTarget()
         {
            Target = Math.Round(totalTarget, MidpointRounding.AwayFromZero),
            Done = Math.Round(totalDone, MidpointRounding.AwayFromZero),
            VsTarget = Math.Round(vsCum, MidpointRounding.AwayFromZero),
            VsTargetCum = Math.Round(vsCum, MidpointRounding.AwayFromZero),
            Day = DateTime.Now,
            IsTotal = true,
            IsAPastDay = true,
         });


         return results;
      }


      public async Task<List<ServiceDailyChaseItem>> GetServiceDailyChaseChartData(string siteIds, DateTime monthCommencing, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         //get data
         decimal target = await dashboardDataAccess.GetServiceMonthlyTarget(siteIds, monthCommencing, userId, showAllSites, false, dealerGroup);
         IEnumerable<ServiceDailyDone> done = await dashboardDataAccess.GetServiceDailyDone(siteIds, monthCommencing, userId, showAllSites, false, dealerGroup);
         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         workingDaysService.GetWeekendOpening(siteIdsAsList, userId, out decimal saturdayOpening, out decimal sundayOpening);
         IEnumerable<DayAndWorkingDayValue> dailyWorkingDays = await workingDaysService.GetDailyWorkingDaysForMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         decimal workingDaysInMonth = dailyWorkingDays.Select(x => x.WorkingDayValue).Sum();

         //walk through days building up results
         List<ServiceDailyChaseItem> results = new List<ServiceDailyChaseItem>();
         DateTime currentDay = monthCommencing.Date;
         decimal doneCum = 0;
         decimal tgtCum = 0;
         while (currentDay.Month == monthCommencing.Month)
         {
            var thisDayDoneItem = done.FirstOrDefault(x => x.Day == currentDay);
            decimal thisDayDone = thisDayDoneItem != null ? thisDayDoneItem.Done : 0;
            doneCum += thisDayDone;
            var thisDayTarget = dailyWorkingDays.First(x => x.DayDate == currentDay).WorkingDayValue / workingDaysInMonth * target;
            tgtCum += thisDayTarget;

            decimal? doneCumToAdd = null;
            if (currentDay.Date <= DateTime.Now.Date.AddDays(-1)) { doneCumToAdd = Math.Round(doneCum, MidpointRounding.AwayFromZero); }
            results.Add(new ServiceDailyChaseItem()
            {
               Label = currentDay,
               DoneCum = doneCumToAdd,
               TargetCum = Math.Round(tgtCum, MidpointRounding.AwayFromZero),
            });

            currentDay = currentDay.AddDays(1);
         }

         return results;
      }

      public async Task<List<ServiceDailyChaseItem>> GetServiceDailyChaseChartDataSpain(string siteIds, DateTime monthCommencing, int userId)
      {
         decimal target = await dashboardDataAccess.GetServiceMonthlyTargetSpain(siteIds, monthCommencing, userId, userService.GetUserDealerGroupName());
         IEnumerable<ServiceDailyDone> done = await dashboardDataAccess.GetServiceDailyDoneSpain(siteIds, monthCommencing, userId, userService.GetUserDealerGroupName());

         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         workingDaysService.GetWeekendOpening(siteIdsAsList, userId, out decimal saturdayOpening, out decimal sundayOpening);
         IEnumerable<DayAndWorkingDayValue> dailyWorkingDays = await workingDaysService.GetDailyWorkingDaysForMonth(monthCommencing, siteIdsAsList, userId, userService.GetUserDealerGroupName());

         decimal workingDaysInMonth = dailyWorkingDays.Select(x => x.WorkingDayValue).Sum();

         //walk through days building up results
         List<ServiceDailyChaseItem> results = new List<ServiceDailyChaseItem>();
         DateTime currentDay = monthCommencing.Date;
         decimal doneCum = 0;
         decimal tgtCum = 0;
         while (currentDay.Month == monthCommencing.Month)
         {
            var thisDayDoneItem = done.FirstOrDefault(x => x.Day == currentDay);
            decimal thisDayDone = thisDayDoneItem != null ? thisDayDoneItem.Done : 0;
            doneCum += thisDayDone;
            var thisDayTarget = dailyWorkingDays.First(x => x.DayDate == currentDay).WorkingDayValue / workingDaysInMonth * target;
            tgtCum += thisDayTarget;

            decimal? doneCumToAdd = null;
            if (currentDay.Date <= DateTime.Now.Date.AddDays(-1)) { doneCumToAdd = Math.Round(doneCum, MidpointRounding.AwayFromZero); }
            results.Add(new ServiceDailyChaseItem()
            {
               Label = currentDay,
               DoneCum = doneCumToAdd,
               TargetCum = Math.Round(tgtCum, MidpointRounding.AwayFromZero),
            });

            currentDay = currentDay.AddDays(1);
         }

         return results;
      }

      public async Task<AftersalesVsTarget> GetServiceVsTarget(string siteIds, DateTime monthCommencing, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var siteDataAccess = new SiteDataAccess(_connectionString);

         IEnumerable<ServiceChannelDataItem> dataItems = (await dashboardDataAccess.GetServiceChannelSummariesData(siteIds, monthCommencing, userId, showAllSites, dealerGroup)).OrderBy(x => x.Order);
         IEnumerable<SiteWithOpening> siteOpeningHours = await siteDataAccess.GetSitesWithOpeningForUser(userId, showAllSites, dealerGroup);

         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();

         decimal elapsedWorkingDays = await workingDaysService.GetWorkingDaysElapsedInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);
         decimal workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         List<ChannelSummary> channelSummaries = new List<ChannelSummary>();

         //build main rows for labour
         foreach (var item in dataItems.Where(x => x.IsLabour))
         {
            channelSummaries.Add(new ChannelSummary()
            {
               Label = item.Channel,
               IsTotal = false,
               Target = item.Target * elapsedWorkingDays / workingDaysInMonth,
               Actual = item.Actual,
               IsLabour = true
            }); ;
         }

         //add labour total
         channelSummaries.Add(new ChannelSummary()
         {
            Label = await translationService.GetTranslationForProperty(userId, "LabourTotal"),
            IsTotal = true,
            Target = dataItems.Where(x => x.IsLabour).Select(x => x.Target).Sum() * elapsedWorkingDays / workingDaysInMonth,
            Actual = dataItems.Where(x => x.IsLabour).Select(x => x.Actual).Sum(),
            IsLabour = true
         });

         //add the rows below labour
         foreach (var item in dataItems.Where(x => !x.IsLabour))
         {
            channelSummaries.Add(new ChannelSummary()
            {
               Label = item.Channel,
               IsTotal = false,
               Target = item.Target * elapsedWorkingDays / workingDaysInMonth,
               Actual = item.Actual,
               IsLabour = false
            });
         }

         //add  total
         channelSummaries.Add(new ChannelSummary()
         {
            Label = "Total",
            IsTotal = true,
            Target = dataItems.Select(x => x.Target).Sum() * elapsedWorkingDays / workingDaysInMonth,
            Actual = dataItems.Select(x => x.Actual).Sum(),
            IsLabour = false
         });


         //build object to return
         return new AftersalesVsTarget()
         {
            ChannelStats = channelSummaries,
            Done = dataItems.Select(x => x.Actual).Sum(),
            MonthTarget = dataItems.Select(x => x.Target).Sum(),
            ElapsedDays = elapsedWorkingDays,
            TotalDays = workingDaysInMonth
         };
      }


      public async Task<DashboardGuage> GetServiceSummary(AftersalesTimePeriod timePeriod, string siteIds, int userId, DateTime? monthCommencing)
      {
         try
         {
            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            IEnumerable<DashboardGuage> resultAllSites;

            if (monthCommencing != null)
            {
               resultAllSites = await serviceSummaryCache.GetServiceSummaryRows(timePeriod, monthCommencing, siteIds, userId, dealerGroup);
            }
            else
            {
               resultAllSites = await serviceSummaryCache.GetServiceSummaryRows(timePeriod, null, null, null, dealerGroup);
            }

            IEnumerable<int> chosenSiteIds = siteIds.Split(',').Select(x => int.Parse(x));
            IEnumerable<int> siteIdsToUse = userService.GetUserSiteIds().Intersect(chosenSiteIds);
            DashboardGuage result = new DashboardGuage(resultAllSites.Where(x => siteIdsToUse.Contains(x.SiteId)));

            DateTime today = DateTime.Now;
            List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();

            // MONTH TO DATE
            if (timePeriod == AftersalesTimePeriod.MTD)
            {
               decimal workingDaysInMonth;
               decimal elapsedWorkingDays;

               if (monthCommencing != null)
               {
                  DateTime monthCommencingNonNull = (DateTime)monthCommencing;

                  // Current month, use today
                  if (monthCommencingNonNull.Year == today.Year && monthCommencingNonNull.Month == today.Month)
                  {
                     workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup); //112ms
                     elapsedWorkingDays = await workingDaysService.GetWorkingDaysElapsedInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup);
                  }
                  // Previous month use month commencing
                  else
                  {
                     workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(new DateTime(monthCommencingNonNull.Year, monthCommencingNonNull.Month, 1), siteIdsAsList, userId, dealerGroup); //112ms
                     elapsedWorkingDays = await workingDaysService.GetWorkingDaysElapsedInMonth(new DateTime(monthCommencingNonNull.Year, monthCommencingNonNull.Month, 1), siteIdsAsList, userId, dealerGroup);
                  }

               }
               else
               {
                  workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup); //112ms
                  elapsedWorkingDays = await workingDaysService.GetWorkingDaysElapsedInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup);
               }

               result.DaysTotal = workingDaysInMonth;
               result.DaysElapsed = elapsedWorkingDays;
               result.TargetToDate = result.ThisMonthTarget * (workingDaysInMonth != 0 ? elapsedWorkingDays / workingDaysInMonth : 0);
               result.Vs = result.DoneInTimePeriod - result.TargetToDate;
            }

            // WEEK
            if (timePeriod == AftersalesTimePeriod.WTD)
            {
               WeekToDateWorkingDays wtdDays = await workingDaysService.DetermineWorkingDaysForCurrentWeek(userId, today, siteIdsAsList);

               decimal dailyTargetThisMonth = wtdDays.ThisMonth != 0 ? result.ThisMonthTarget / wtdDays.ThisMonth : 0;
               decimal dailyTargetLastMonth = wtdDays.LastMonth != 0 ? result.LastMonthTarget / wtdDays.LastMonth : 0;

               result.DaysTotal = wtdDays.LastMonthThisWeek + wtdDays.ThisMonthThisWeek;
               result.DaysElapsed = wtdDays.LastMonthThisWeekElapsed + wtdDays.ThisMonthThisWeekElapsed;
               result.TargetToDate = wtdDays.LastMonthThisWeek * dailyTargetLastMonth + wtdDays.ThisMonthThisWeek * dailyTargetThisMonth;
               result.Vs = result.DoneInTimePeriod - result.TargetToDate;
            }

            //YESTERDAY
            if (timePeriod == AftersalesTimePeriod.Yesterday)
            {
               decimal workingDaysYesterday = await workingDaysService.CountWorkingDaysBetweenAndIncluding(today.AddDays(-1), today.AddDays(-1), siteIdsAsList, userId);

               if (today.Day > 1)  //not 1st day of month
               {
                  decimal workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup);
                  result.DaysTotal = workingDaysYesterday;
                  result.DaysElapsed = workingDaysYesterday;
                  result.TargetToDate = workingDaysYesterday * (workingDaysInMonth != 0 ? result.ThisMonthTarget / workingDaysInMonth : 0);
                  result.Vs = result.DoneInTimePeriod - result.TargetToDate;
               }
               else  //1st day of month.  
               {
                  decimal workingDaysLastMonth = await workingDaysService.GetWorkingDaysInMonth(DateTime.Now.AddMonths(-1), siteIdsAsList, userId, dealerGroup);
                  result.DaysTotal = workingDaysYesterday;
                  result.DaysElapsed = workingDaysYesterday;

                  if (workingDaysYesterday != 0)
                  {
                     result.TargetToDate = workingDaysLastMonth != 0 ? result.LastMonthTarget / workingDaysLastMonth : 0;
                  }
                  else
                  {
                     result.TargetToDate = 0;
                     result.DoneInTimePeriod = 0;
                  }

                  result.Vs = result.DoneInTimePeriod - result.TargetToDate;
               }
            }

            return result;

         }
         catch (Exception e)
         {
            throw new Exception(e.Message);
         }
      }



      public async Task<IEnumerable<ServiceDailySalesSiteRow>> GetServiceDailySales(DateTime startDate, string channels, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         //parse the incoming channels list to a format that the db will recognise.
         List<string> cleanedUpChannels = new List<string>();
         List<string> incomingChannels = channels.Split(",").ToList();
         if (incomingChannels.Contains("Retail")) { cleanedUpChannels.Add("retail"); }
         if (incomingChannels.Contains("MOT")) { cleanedUpChannels.Add("mot"); }
         if (incomingChannels.Contains("Internal")) { cleanedUpChannels.Add("internal"); }
         if (incomingChannels.Contains("Warranty")) { cleanedUpChannels.Add("warranty"); }
         if (incomingChannels.Contains("Tyre")) { cleanedUpChannels.Add("tyre"); cleanedUpChannels.Add("sublet"); }  //RRG deliberately excludes Sundry (Shuan email)
         if (incomingChannels.Contains("Tyre/Sublet")) { cleanedUpChannels.Add("tyre"); cleanedUpChannels.Add("sublet"); }
         if (incomingChannels.Contains("Oil")) { cleanedUpChannels.Add("oil"); cleanedUpChannels.Add("oilInt"); cleanedUpChannels.Add("oilExt"); cleanedUpChannels.Add("oilWarr"); }
         if (incomingChannels.Contains("Other")) { cleanedUpChannels.Add("sundry"); }

         //get the underlying data
         string chanString = string.Join(",", cleanedUpChannels);

         IEnumerable<ServiceDailySalesSiteRow> dailyRows = await serviceDataAccess.GetServiceDailySales(startDate, chanString, userId, dealerGroup);
         if (!dailyRows.Any()) return new List<ServiceDailySalesSiteRow>();

         List<int> siteIds = dailyRows.Select(x => x.SiteId).Distinct().ToList();

         //get working days stuff
         var holidaysInMonth = await workingDaysService.GetPublicHolidays(startDate, startDate.AddMonths(1).Date);
         IEnumerable<SiteWithOpening> siteWeekendOpening = await workingDaysService.GetSiteWeekendOpening(userId, showAllSites, dealerGroup);

         Dictionary<int, SiteAndElapsedWorkDays> daysPerSite = (await workingDaysService.GetElapsedDaysForSites(startDate, AftersalesTimePeriod.MTD, siteIds, userId, showAllSites, dealerGroup)).ToDictionary(x => x.SiteId);

         //add working days to sites
         foreach (var row in dailyRows)
         {
            if (row.IsSite)
            {
               SiteWithOpening thisSiteWeekendOpening = siteWeekendOpening.FirstOrDefault(x => x.SiteId == row.SiteId);
               decimal satOpening = thisSiteWeekendOpening != null ? thisSiteWeekendOpening.SatOpening : 0;
               decimal sunOpening = thisSiteWeekendOpening != null ? thisSiteWeekendOpening.SunOpening : 0;
               row.WorkingDaysElapsed = daysPerSite[row.SiteId].WorkingDaysElapsed;
               row.WorkingDaysInMonth = daysPerSite[row.SiteId].WorkingDaysInMonth;
            }
         }

         dailyRows = dailyRows.OrderBy(x => x.SiteId);

         //sum working days for regions
         foreach (var row in dailyRows)
         {
            if (row.IsRegion)
            {
               row.WorkingDaysElapsed = dailyRows.Where(x => x.RegionDescription == row.RegionDescription).Select(x => x.WorkingDaysElapsed).Sum();
               row.WorkingDaysInMonth = dailyRows.Where(x => x.RegionDescription == row.RegionDescription).Select(x => x.WorkingDaysInMonth).Sum();
            }
         }

         //add working days to total row
         dailyRows.First(x => x.IsTotal).WorkingDaysElapsed = dailyRows.Where(x => x.IsRegion).Select(x => x.WorkingDaysElapsed).Sum();
         dailyRows.First(x => x.IsTotal).WorkingDaysInMonth = dailyRows.Where(x => x.IsRegion).Select(x => x.WorkingDaysInMonth).Sum();

         //add ratios to all rows
         foreach (var row in dailyRows)
         {
            if (row.SiteId == 33)
            {
               { }
            }
            row.Target = Math.Round((row.FullMonthTarget / row.WorkingDaysInMonth * row.WorkingDaysElapsed), 2);
            row.ActualVsTarget = Math.Round(row.Actual - row.Target, 2);
            row.DonePercent = row.Target != 0 ? Math.Round(row.Actual / row.Target, 2) : 0;
            row.AverageDone = row.WorkingDaysElapsed != 0 ? row.Actual / row.WorkingDaysElapsed : 0;

            if (!row.IsTotal)
            {
               if (!row.IsRegion)
               {
                  row.ActualPerTechPerDay = row.TechHeadCount != 0 ? Math.Round(((row.Actual / row.TechHeadCount) / row.WorkingDaysElapsed), 2) : 0;
               }
            }
            else
            {
               row.ActualPerTechPerDay = row.TechHeadCount != 0 ? Math.Round((row.Actual / row.TechHeadCount), 2) : 0;
            }

            row.PerTechPerDayVsTarget = row.ActualPerTechPerDay - row.TechTgtPerDay;
         }

         decimal totalFTE = dailyRows.Where(x => x.IsSite).Sum(x => x.TechHeadCount);
         decimal totalTargetBySite = dailyRows.Where(x => x.IsSite).Sum(x => (x.TechTgtPerDay * x.TechHeadCount));
         decimal totalActualBySite = dailyRows.Where(x => x.IsSite).Sum(x => (x.ActualPerTechPerDay * x.TechHeadCount));
         decimal totalAverageDone = dailyRows.Where(x => x.IsSite).Sum(x => x.AverageDone);

         // Total techcounts regions/total
         foreach (var row in dailyRows)
         {
            if (row.IsRegion)
            {
               decimal regFTE = dailyRows.Where(x => x.IsSite && x.RegionDescription == row.Label).Sum(x => x.TechHeadCount);
               decimal regTargetBySite = dailyRows.Where(x => x.IsSite && x.RegionDescription == row.Label).Sum(x => (x.TechTgtPerDay * x.TechHeadCount));
               decimal regActualBySite = dailyRows.Where(x => x.IsSite && x.RegionDescription == row.Label).Sum(x => (x.ActualPerTechPerDay * x.TechHeadCount));
               decimal regAvgDone = dailyRows.Where(x => x.IsSite && x.RegionDescription == row.Label).Sum(x => x.AverageDone);

               row.TechTgtPerDay = regFTE > 0 ? regTargetBySite / regFTE : 0;
               row.ActualPerTechPerDay = regFTE > 0 ? regActualBySite / regFTE : 0;
               row.PerTechPerDayVsTarget = row.ActualPerTechPerDay - row.TechTgtPerDay;
               row.AverageDone = regAvgDone;
            }

            if (row.IsTotal)
            {
               row.TechTgtPerDay = totalFTE > 0 ? totalTargetBySite / totalFTE : 0;
               row.ActualPerTechPerDay = totalFTE > 0 ? totalActualBySite / totalFTE : 0;
               row.PerTechPerDayVsTarget = row.ActualPerTechPerDay - row.TechTgtPerDay;
               row.AverageDone = totalAverageDone;
            }

         }
         ;

         return dailyRows;

      }








   }
}
