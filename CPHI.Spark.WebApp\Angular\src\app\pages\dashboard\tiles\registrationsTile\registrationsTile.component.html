<div class="tileHeader">
  <div class="headerWords">
    <h4>{{headerName}}
    </h4>
  </div>
</div>

<!-- *ngIf="selections.user.permissions.seeRegistrations" -->
<table >
  <tbody>
    <tr class="numberRow">
      <td>
        <h1 class=" clickable">{{Target|cph:'number':0}} </h1>
      </td>
      <td>
        <h1 class=" clickable">{{Achieved|cph:'number':0}} </h1>
      </td>
      <td>
        <h1 >
          <span *ngIf="vs()<0" class="badFont">{{vs()*-1|cph:'number':0}} </span>
          <span *ngIf="vs()>=0" class="goodFont">{{vs()|cph:'number':0}} </span>
        </h1>
      </td>
    </tr>

    <tr class="labelRow">
      <td>
        <div>{{constants.translatedText.Target}}</div>
      </td>
      <td>
        <div>{{constants.translatedText.Achieved}}</div>
      </td>

      <td>
        <div>
          <span *ngIf="vs() < 0">{{constants.translatedText.ToGo}}</span>
          <span *ngIf="vs() >= 0">{{constants.translatedText.Over}}</span>
        </div>
      </td>

    </tr>

  </tbody>
</table>