import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ConstantsService } from './constants.service'
import { SelectionsService } from './selections.service'
import { Observable, throwError } from 'rxjs';
import { EnvironmentService } from './environment.service';

export interface KeyValuePair {
  key: string;
  value: any;
}




@Injectable({
  providedIn: 'root'
})
export class ApiAccessService {

  language: string = this.constants.environment?.customer == 'RRGSpain' ? "ES" : "EN";

  constructor(
    public http: HttpClient,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public environmentService: EnvironmentService,


  ) {

  }

  provideBaseUrl(): string {
    return `${this.constants.backEndBaseURL}`;
    //return this.environmentService.config.default.backEndBaseURL;
  }


  get(controllerName: string, method: string, requestParams?: KeyValuePair[]): Observable<any> {

    let myObservable: Observable<any> = new Observable(observer => {


      let paramsString = ""
      if (requestParams && requestParams.length > 0) {
        paramsString += "?"
        requestParams.forEach((rp, i) => {
          if (i > 0) paramsString += '&';
          paramsString += `${rp.key}=${rp.value}`;
        })
      }
      this.http.get(`${this.provideBaseUrl()}/${controllerName}/${method}${paramsString}`, this.getHeaders()).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when retrieving ' + controllerName + JSON.stringify(error))
          //this.constants.toastDanger(`Error when retrieving ${controllerName}`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;


  }



  getHeaders() {
    let headers = new HttpHeaders({
      'Authorization': 'Bearer ' + localStorage.getItem('accessToken')
    });
    let options = { headers: headers };

    return options
  }

  changeLang(newLang: string)
  {
    if(newLang == null){ return; }
    this.language = newLang;
  }







  patch(controllerName: string, method: string, payload: any): Observable<any> {

    let myObservable: Observable<any> = new Observable(observer => {

      this.http.patch(`${this.provideBaseUrl()}/${controllerName}/${method}`, payload, this.getHeaders()).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when posting ' + controllerName + JSON.stringify(error))
          //this.constants.toastDanger(`Error when posting ${controllerName}`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;

  }



  post(controllerName: string, method: string, payload: any): Observable<any> {
    //console.log(this.provideBaseUrl() + '/' + controllerName + '/' + method);

    //console.log(payload);
    let myObservable: Observable<any> = new Observable(observer => {

      this.http.post(`${this.provideBaseUrl()}/${controllerName}/${method}`, payload, this.getHeaders()).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when posting ' + controllerName + JSON.stringify(error))
          //this.constants.toastDanger(`Error when posting ${controllerName}`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;


  }


  delete(controllerName: string, method: string, id: number): Observable<any> {
    let myObservable: Observable<any> = new Observable(observer => {

      this.http.delete(`${this.provideBaseUrl()}/${controllerName}/${method}?stockCheckId=${id}`, this.getHeaders()).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when deleting ' + controllerName + JSON.stringify(error))
          //this.constants.toastDanger(`Error when deleting ${controllerName}`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }

  
  deleteByString(controllerName: string, method: string, label: string): Observable<any> {
    let myObservable: Observable<any> = new Observable(observer => {

      this.http.delete(`${this.provideBaseUrl()}/${controllerName}/${method}?label=${label}`, this.getHeaders()).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when deleting ' + controllerName + JSON.stringify(error))
          //this.constants.toastDanger(`Error when deleting ${controllerName}`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }



  deleteUser(controllerName: string, method: string, id: string): Observable<any> {
    let myObservable: Observable<any> = new Observable(observer => {

      this.http.delete(`${this.provideBaseUrl()}/${controllerName}/${method}?id=${id}`, this.getHeaders()).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when deleting ' + controllerName + JSON.stringify(error))
          //this.constants.toastDanger(`Error when deleting ${controllerName}`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }

  deleteColumnState(controllerName: string, method: string, stateId: number, pageName: string): Observable<any> {
    let myObservable: Observable<any> = new Observable(observer => {

      this.http.delete(`${this.provideBaseUrl()}/${controllerName}/${method}?stateId=${stateId}&pageName=${pageName}`, this.getHeaders()).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when deleting ' + controllerName + JSON.stringify(error))
          //this.constants.toastDanger(`Error when deleting ${controllerName}`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }

  postWithOptions(controllerName: string, method: string, payload: any, options: any): Observable<any> {
    //console.log('in post');
    
    let myObservable: Observable<any> = new Observable(observer => {
      
      this.http.post(`${this.provideBaseUrl()}/${controllerName}/${method}`, payload, options).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('System error');
          observer.error(error);
          //this.constants.toastDanger(`System error`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;


  }


}
