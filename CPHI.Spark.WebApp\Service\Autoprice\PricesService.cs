﻿using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using CPHI.Spark.Model;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Model.ViewModels;
using System.Linq;
using System.Net.Http;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
    public interface IPricesService
    {
        Task UpdatePrice(int vehicleAdvertId, int newPrice, string rrgSiteItemStockId);
    }
    public class PricesService: IPricesService
    {
        private readonly AutoTraderApiClient atClient;
        private readonly AutoTraderApiTokenClient atClientToken;
        private readonly IUserService userService;
        //private readonly IStockDataAccess stockDataAccess;
        private readonly IConfiguration configuration;
        private readonly string _connectionString;
        private readonly IAutoPriceCache autoPriceCache;
        

        public PricesService(
            
            IUserService userService,
            //IStockDataAccess stockDataAccess, 
            IConfiguration configuration
,
            IAutoPriceCache autoPriceCache)
        {
            var httpClient = new HttpClient();
            string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
            string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
            string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
            atClient = new AutoTraderApiClient(httpClient, apiKey, apiSecret, baseURL);
            atClientToken = new AutoTraderApiTokenClient(httpClient, apiKey, apiSecret, baseURL);

            this.userService = userService;
            //this.stockDataAccess = stockDataAccess;
            this.configuration = configuration;
            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            _connectionString = configuration.GetConnectionString(dgName);
            this.autoPriceCache = autoPriceCache;
        }

        //Prices

        public async Task UpdatePrice(int vehicleAdvertId, int newPrice, string rrgSiteItemStockId)
        {
            

            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

            if(dealerGroup == DealerGroupName.RRGUK)
            {
                await UpdatePriceRRG(newPrice, rrgSiteItemStockId, vehicleAdvertId);
            }
            else if(dealerGroup == DealerGroupName.BrindleyGroup)
            {
                await UpdatePriceBrindley(newPrice, vehicleAdvertId, dealerGroup);
            }
            else
            {
                throw new Exception("Unexpected dealergroup");
            }

        }

        private async Task UpdatePriceBrindley(int newPrice, int vehicleAdvertId, DealerGroupName dealerGroup)
        {
            int userId = userService.GetUserId();

            var lifecycleStatuses = new List<string>() { "DUE_IN", "FORECOURT", "IN STOCK NOT ON PORTAL", "SALE_IN_PROGRESS" };
            List<VehicleAdvertWithRating> adverts = (await autoPriceCache.GetAutoPriceAdverts(DateTime.Now.Date, dealerGroup, lifecycleStatuses)).ToList();
            var advert = adverts.FirstOrDefault(x => x.AdId == vehicleAdvertId);
            PriceChangeManualItem priceChange = new PriceChangeManualItem()
            {
                Person_Id = userId,
                VehicleAdvertSnapshot_Id = advert.SnapshotId,
                CreatedDate = DateTime.Now,
                WasPrice = advert.AdvertisedPrice,
                NowPrice = newPrice
            };
            var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);
            await priceChangesDataAccess.SaveManualPriceChange(priceChange);
            //now we wait for the webscraper to do its thing
        }

        private async Task UpdatePriceRRG(int newPrice, string rrgSiteItemStockId, int vehicleAdvertId)
        {
            var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
            VehicleAdvert advert = await vehicleAdvertsDataAccess.GetExistingVehicleAdvert(vehicleAdvertId);
            await UpdatePriceNowOnAutotrader(newPrice, advert);
            if (rrgSiteItemStockId != null)
            {
                await UpdatePriceOnStockRRGSiteItem(rrgSiteItemStockId, newPrice);
            }
        }

        private async Task UpdatePriceNowOnAutotrader(int newPrice, VehicleAdvert advert)
        {
            var update = new UpdatePriceParams() { AdvertiserId = int.Parse(advert.RetailerIdentifier), WebsiteStockIdentifier = advert.WebSiteStockIdentifier, NewPrice = newPrice };
            await UpdatePriceOnAutotrader(update);
        }

        private async Task UpdatePriceOnStockRRGSiteItem(string rrgSiteItemStockId, int newPrice)
        {
            StockDataAccess stockDataAccess = new StockDataAccess(_connectionString);
            var rrgStockItem = await stockDataAccess.GetStockRRGSiteItem(rrgSiteItemStockId, DealerGroupName.RRGUK);
            if (rrgStockItem != null)
            {
                await stockDataAccess.UpdateStockRRGSiteItemPrice(rrgStockItem, newPrice, DealerGroupName.RRGUK);
            }
        }

        private async Task UpdatePriceOnAutotrader(UpdatePriceParams update)
        {

            string baseURL = Startup.Configuration["AutotraderSettings:BaseURLForChangingPrices"];
            string atToken = await GetAutotraderToken(baseURL);
            DateTime dateSent = DateTime.Now;
            string result = await atClient.UpdatePrice(update, atToken, baseURL);

            var vehicleAdvertSnapshotsDataAccess = new VehicleAdvertSnapshotsDataAccess(_connectionString);
            VehicleAdvertSnapshot snapshot = await vehicleAdvertSnapshotsDataAccess.GetVehicleSnapshot(update.WebsiteStockIdentifier);
            PriceChangeManualItem itemToSave = new PriceChangeManualItem()
            {
                Person_Id = userService.GetUserId(),
                VehicleAdvertSnapshot_Id = snapshot.Id,
                CreatedDate = DateTime.Now,
                WasPrice = (decimal)snapshot.TotalPrice,
                NowPrice = update.NewPrice,
                DateSent = dateSent,
                DateConfirmed = result == "OK" ? DateTime.Now : null,
                SaveResult = result
            };
            var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);
            await priceChangesDataAccess.SaveManualPriceChange(itemToSave);

            //we have updated the price on autotrader so now we should go ahead and create a new rating to reflect this.
            var newSnapshot = new VehicleAdvertSnapshot(snapshot, update.NewPrice);
            await vehicleAdvertSnapshotsDataAccess.SaveNewSnapshots(new List<VehicleAdvertSnapshot>() { newSnapshot });
            
            //and set the old snapshot to no longer be marked as the latest one
            await vehicleAdvertSnapshotsDataAccess.SetVehicleSnapshotToNotLatest(snapshot.Id);

        }

        private async Task<string> GetAutotraderToken(string baseURL)
        {
            string atToken = (await atClientToken.GetToken()).AccessToken;
            return atToken;
        }

    }
}