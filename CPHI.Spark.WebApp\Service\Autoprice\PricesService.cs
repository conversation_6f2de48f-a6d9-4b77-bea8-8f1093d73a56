﻿using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using CPHI.Spark.Model;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model.Services;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Model.ViewModels;
using System.Linq;
using System.Net.Http;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
   public interface IPricesService
   {
      Task UpdatePrice(int vehicleAdvertId, int newPrice);
   }
   public class PricesService : IPricesService
   {
      private readonly IUserService userService;
      private readonly string _connectionString;
      private readonly IAutoPriceCache autoPriceCache;
      private readonly HttpClient httpClient;


      public PricesService(

          IUserService userService,
          IConfiguration configuration,
          IAutoPriceCache autoPriceCache,
          IHttpClientFactory httpClientFactory)
      {
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];

         this.userService = userService;
         DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         _connectionString = configuration.GetConnectionString(dgName);
         this.autoPriceCache = autoPriceCache;
         this.httpClient = httpClientFactory.CreateClient();
      }

      //Prices

      public async Task UpdatePrice(int vehicleAdvertId, int newPrice)
      {


         DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         await CreateManualPriceChangeAndUpdateSnapshot(newPrice, vehicleAdvertId);

         //if (dealerGroup == DealerGroupName.RRGUK)
         //{
         //   await UpdatePriceOnStockRRGSiteItem(rrgSiteItemStockId, newPrice);
         //}

      }

     
      private async Task CreateManualPriceChangeAndUpdateSnapshot(int newPrice,  int advertId)
      {
         var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         VehicleAdvert advert = await vehicleAdvertsDataAccess.GetExistingVehicleAdvert(advertId);

         var vehicleAdvertSnapshotsDataAccess = new VehicleAdvertSnapshotsDataAccess(_connectionString);
         VehicleAdvertSnapshot snapshot = await vehicleAdvertSnapshotsDataAccess.GetSnapshot((int)advert.LatestVehicleAdvertSnapshotId);
         PriceChangeManualItem itemToSave = new PriceChangeManualItem()
         {
            Person_Id = userService.GetUserId(),
            VehicleAdvertSnapshot_Id = snapshot.Id,
            CreatedDate = DateTime.Now,
            WasPrice = snapshot.SuppliedPrice ?? 0 + snapshot.AdminFee ?? 0,
            NowPrice = newPrice,
            DateSent = DateTime.Now,
            DateConfirmed =  null,
            SaveResult = null
         };
         var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);
         await priceChangesDataAccess.SaveManualPriceChange(itemToSave);

         //we have updated the price on autotrader so now we should go ahead and create a new rating to reflect this.
         var newSnapshot = new VehicleAdvertSnapshot(snapshot, newPrice);
         await vehicleAdvertSnapshotsDataAccess.SaveNewSnapshots(new List<VehicleAdvertSnapshot>() { newSnapshot });

         //and set the old snapshot to no longer be marked as the latest one
         await vehicleAdvertSnapshotsDataAccess.SetVehicleSnapshotToNotLatest(snapshot.Id);
      }

      //private async Task UpdatePriceOnStockRRGSiteItem(string rrgSiteItemStockId, int newPrice)
      //{
      //   StockDataAccess stockDataAccess = new StockDataAccess(_connectionString);
      //   var rrgStockItem = await stockDataAccess.GetStockRRGSiteItem(rrgSiteItemStockId, DealerGroupName.RRGUK);
      //   if (rrgStockItem != null)
      //   {
      //      await stockDataAccess.UpdateStockRRGSiteItemPrice(rrgStockItem, newPrice, DealerGroupName.RRGUK);
      //   }
      //}

     


   }
}