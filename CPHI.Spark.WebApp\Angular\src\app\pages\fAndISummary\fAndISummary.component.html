<nav class="navbar">

 <nav class="generic" >
    <h4 id="pageTitle">
      <div >
        {{constants.translatedText.Dashboard_FinanceAddOnPerformance}}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <ng-container *ngIf="service.fAndISummary && service.fAndISummary.showSitesTable">

      <vehicleTypePickerSpain
        *ngIf="constants.environment.customer == 'RRGSpain'"
        [vehicleTypeTypesFromParent]="service.fAndISummary.vehicleTypeTypes"
        (updateVehicleTypes)="service.updateVehicleTypes($event)"
      >
      </vehicleTypePickerSpain>

      <div class="buttonGroup topDropdownButtons">

        <!-- VehicleType selector -->
      <vehicleTypePicker
        *ngIf="constants.environment.customer != 'RRGSpain'"
        [vehicleTypeTypesFromParent]="service.fAndISummary.vehicleTypeTypes"
        [buttonClass]="'buttonGroupCenter'"
        (updateVehicleTypes)="service.updateVehicleTypes($event)"
      >
      </vehicleTypePicker>

      <!-- OrderType selector -->
      <orderTypePicker
        *ngIf="constants.environment.orderTypePicker"
        [orderTypeTypesFromParent]="service.fAndISummary.orderTypeTypes"
        [buttonClass]="'buttonGroupCenter'"
        (updateOrderTypes)="service.updateOrderTypes($event)"
      >
      </orderTypePicker>

      <!-- Franchise selector -->
      <franchisePicker
        *ngIf="constants.environment.franchisePicker"
        [franchisesFromParent]="service.fAndISummary.franchises"
        [buttonClass]="'buttonGroupRight'"
        (updateFranchises)="service.updateFranchises($event)"
      >
      </franchisePicker>


        <!-- FOR SELECTING MONTH -->
        <div class="buttonGroup">

          <!-- previousMonth -->
          <button
           [disabled]="service.months.length>1"
            class="btn btn-primary" 
            (click)="changeMonth(-1)"
          >
            <i class="fas fa-caret-left"></i>
          </button>
          
          <!-- dropdownMonth -->
          <datePickerMultiSelect
          [monthsFromParent]="service.months"
          (selectMonths)="updateMonths($event)"
          [includeYTD]="true"
          [includeLastYear]="true"
          [includeThisYear]="true"
          >
          </datePickerMultiSelect> 

          <!-- nextMonth -->
          <button 
              [disabled]="service.months.length>1"              
              class="btn btn-primary" 
              (click)="changeMonth(1)"><i
              class="fas fa-caret-right"></i></button>
        </div>


      </div>

    </ng-container>


  </nav>


</nav>

<!-- Main Page -->
<div class="content-new">

    <div class="content-inner-new" *ngIf="service.fAndISummary">
      <button class="btn btn-primary" id="backToSites" (click)="backToSites()" *ngIf="service.fAndISummary.showPeopleTable && service.fAndISummary.salesManagerId == null">
        <i class="fas fa-undo"></i>
      </button>
      <button class="btn btn-primary" id="backToSites" (click)="backToManagers()" *ngIf="service.fAndISummary.peopleRowData && constants.environment.customer == 'Vindis' && service.fAndISummary.salesManagerId != null" >
        <i class="fas fa-undo"></i>
      </button>

      <ng-container *ngIf="service.fAndISummary.showSitesTable">
        <fAndITableSites
        [isSitesTable]  ="true"
        [rowData]="service.fAndISummary.sitesRowData"
        [totalRow]="service.fAndISummary.sitesRowDataTotal"
        (clickedSite)="selectSite($event)"
        >
      </fAndITableSites>
      <div class="tableSpacer"></div>
      <fAndITableSites
      [isSitesTable]  ="false"
          [rowData]="service.fAndISummary.regionalsRowData"
          [totalRow]="service.fAndISummary.sitesRowDataTotal"
          (clickedSite)="selectSite($event)"
        >
        </fAndITableSites>
      </ng-container>
      
      <fAndITablePeople
        *ngIf="service.fAndISummary.showPeopleTable && constants.environment.customer != 'Vindis'"
        [rowData]="service.fAndISummary.peopleRowData"
        [totalRow]="service.fAndISummary.peopleRowDataTotal"
      >
      </fAndITablePeople>

      <fAndITablePeople
      *ngIf="service.fAndISummary.showPeopleTable && constants.environment.customer == 'Vindis'"
      [rowData]="service.fAndISummary.peopleRowData"
      [totalRow]="service.fAndISummary.peopleRowDataTotal"
      (clickedPeople)="selectManager($event)"
      [managers]="service.fAndISummary.salesManagerId == null" 
    >
    </fAndITablePeople>
    </div>

</div>