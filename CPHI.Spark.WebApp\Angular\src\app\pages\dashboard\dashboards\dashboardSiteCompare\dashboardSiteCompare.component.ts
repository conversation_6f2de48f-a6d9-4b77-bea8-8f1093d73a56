//core angular
import { Component, EventEmitter, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
//model and cell renderers
//services
import { ConstantsService } from '../../../../services/constants.service';
import { SelectionsService } from '../../../../services/selections.service';
import { DashboardService } from '../../dashboard.service';
import { DashboardDataItem, DashboardDataPack, DashboardDataParams, DashboardTimePeriod } from '../../dashboard.model';



@Component({
  selector: 'dashboardSiteCompare',
  templateUrl: './dashboardSiteCompare.component.html',
  styleUrls: ['./dashboardSiteCompare.component.scss']
})


export class DashboardSiteCompareComponent implements OnInit {



  weekStart: Date;
  dataItems: DashboardDataItem[];
  dataPack: DashboardDataPack;
  department:string;
  isMargin:boolean;
  timePeriod:DashboardTimePeriod;
  sub: Subscription;

  newDataEmitter:EventEmitter<void>

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public service: DashboardService

  ) {


  }



  ngOnDestroy() {
    if(!!this.sub)this.sub.unsubscribe();
   }


  ngOnInit() {

    //launch control
    if(!this.department){
      this.initParams()
    }
    this.getData();

    this.sub = this.service.getNewDataTrigger.subscribe(res=>{
      this.getData();
    })

  }

  initParams() {

    this.newDataEmitter = new EventEmitter();
    this.weekStart = this.constants.startOfThisWeek();
    this.department = 'New',
    this.isMargin = false,
    this.timePeriod = DashboardTimePeriod.Month
    this.dataItems =
      [
        DashboardDataItem.SiteCompare,
        DashboardDataItem.TodayMap

      ]

  
  }


  getData() {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    
    let parms: DashboardDataParams = {
      SiteIds: this.service.chosenSites.map(x=>x.SiteId).join(','),
      WeekStart: this.weekStart,
      DataItems: this.dataItems,
      Department:this.department,
      IsMargin:this.isMargin,
      TimePeriod: this.timePeriod
    }

    this.getDataService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {
      
      if(!this.dataPack){
        this.dataPack = res;
      }else{
        Object.assign(this.dataPack,res)
      }

      //in order for the template to update the object it passes through the input tag to the child tile.   else the child tile regenerates its data again from the old dataPack
      setTimeout(()=>{
        this.newDataEmitter.emit();
        this.selections.triggerSpinner.next({show:false})
      },50)
    })

  }





}
