import {Component, ElementRef, Input, OnChanges, OnInit, ViewChild} from '@angular/core';
import { Chart, ChartConfiguration } from 'chart.js';
import moment from 'moment';
import { CphPipe } from 'src/app/cph.pipe';
import { TrendedValuationCollection } from 'src/app/model/TrendedValuationCollection';
import { ConstantsService } from 'src/app/services/constants.service';
import { ToastComponent } from '../../toast.component';
import { VehicleValuationService } from '../vehicleValuation.service';

@Component({
  selector: 'futureValuationChart',
  templateUrl: './futureValuationChart.component.html',
  styleUrls: ['./futureValuationChart.component.scss']
})
export class FutureValuationChartComponent implements OnInit, OnChanges {

  @ViewChild('futureValuationsCanvas', { static: true }) futureValuationsCanvas: ElementRef;
  @Input() data: TrendedValuationCollection;
  futureValuationsChart: any;

  constructor(
    public constantsService: ConstantsService,
    public cphPipe: CphPipe,
    private service: VehicleValuationService
  ) { }

  ngOnInit(): void {
    setTimeout(() => {
       this.createChart(this.service.valuationModalResultNew.TrendedValuations);
    }, 100)

    this.service.trendedValuationInstance = this;
  }

  ngOnChanges() {
     if (this.data != null) {
        if (this.futureValuationsChart != null) {
           this.futureValuationsChart.destroy();
        }
        this.dealWithNewData(this.data);
     }
  }

  dealWithNewData(data:TrendedValuationCollection){
    this.createChart(data);
  }

  createChart(data:TrendedValuationCollection) {
    const arbitraryLineHeight: number = (document.getElementById('futureValuationsCanvas').clientHeight) - 40;
    const labels = data.Dates.map(d => this.cphPipe.transform(d, 'shortDate', 0));

    const todayIndex: number = data.DayTypes.findIndex(x => x === 'today') + 1;
    const sellingDays = this.service.chosenVehicleLocationStrategyPriceBuild.DaysToSell;
    const sellingIndex:number = todayIndex + sellingDays;

    const config: ChartConfiguration = {
      data: {
        labels: labels,
        datasets: [
          {
            data: data.Valuations,
            borderColor: '#406BD7',
            fill: false,
            tension: 0.1,
            pointRadius: 0,
            segment: {
              borderColor: ctx => '#406BD7',
              borderDash: ctx => { return ctx.p0DataIndex >= todayIndex ? [6, 6] : undefined }
            },
          }
        ]
      },
      options: {
        interaction: {
          intersect: false,
          mode: 'index'
        },
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
          }
        },
        plugins: {
          datalabels: {
            display: false
          },
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                return this.cphPipe.transform(context.raw, 'currency', 0);
              }
            },
            enabled: true,
            mode: 'nearest'
          }
        },
        scales: {
          y: {
            ticks: {
              autoSkip: false,
              maxTicksLimit: 3,
              callback: (value, index, ticks) => {
                return this.cphPipe.transform(value, 'currency', 0);
              }
            }
          },
          x: {
            grid: {
              display: false
            },
            ticks: {
              autoSkip: false,
              maxTicksLimit: 3,
              callback: (value, index, values) => {
                if (index === 10) { return labels[0]; }
                if (index === labels.length - 10) { return labels[labels.length - 1]; }
                if (index === todayIndex) { return 'Today' }
                return '';
              }
            }
          }
        },
      },
      plugins: [this.createArbitraryVerticalLine(arbitraryLineHeight, todayIndex,false,sellingDays), this.createArbitraryVerticalLine(arbitraryLineHeight, sellingIndex, true,sellingDays)],
      type: 'line'
    }

    if (this.futureValuationsChart) {
      this.futureValuationsChart.destroy();
    }

    this.futureValuationsChart = new Chart(this.futureValuationsCanvas.nativeElement, config);
  }

  private createArbitraryVerticalLine(arbitraryLineHeight: number, index: number, dashed: boolean, sellingDays:number) {
    const label = `Selling (${this.constantsService.pluralise(sellingDays,'day','days')})`
    return {
      id: 'arbitraryLineNow',
      beforeDraw(chart, args, options) {
        const ctx = chart.ctx;
        const chartArea = chart.chartArea;
        ctx.save();

        // Line settings
        ctx.lineWidth = 2;
        ctx.strokeStyle = 'rgba(0,0,0,0.5)';

        if (dashed) {
          ctx.setLineDash([3, 3]);
        }

        //draw line
        let lineXPosition = chart.scales.x.getPixelForValue(index);
        ctx.beginPath();
        ctx.moveTo(lineXPosition, chartArea.top);
        ctx.lineTo(lineXPosition, chartArea.top + arbitraryLineHeight);
        ctx.stroke();

        if (dashed) {
          // Add text
          ctx.font = '14px Arial';
          ctx.fillStyle = 'rgba(0,0,0,0.5)';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'bottom';
          ctx.fillText(label, lineXPosition + 60, chartArea.top + 20);
        }

        ctx.restore();
      }
    };
  }
}
