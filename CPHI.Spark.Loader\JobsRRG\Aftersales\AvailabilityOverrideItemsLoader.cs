using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Threading.Tasks;
using System.Diagnostics;

namespace CPHI.Spark.Loader
{
    public class AvailabilityOverrideJob : IJob
    {


        private static readonly ILog Logger = LogManager.GetLogger(typeof(AvailabilityOverrideJob));
        private string fileSearch = "*SPK39.csv";
        /*
| Branch | Bolton | No |
|--------|--------|----|
| Date   | 45041  | No |
| Skill  |        | No |
| Hours  | 44     | No |
*/

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            string[] filePaths = await Task.Run(() => Directory.GetFiles(ConfigService.incomingRoot, fileSearch));

            //check for presence of file, if so, return as already running
            if (LocksService.AvailabilityOverride) { CentralLoggingService.ReportLock("AvailabilityOverrideJob"); return; }

            if (filePaths.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.AvailabilityOverride;
                if (age.Minutes > 120)
                {
                    PulsesService.AvailabilityOverride = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *SPK39.csv");
                }
                return;
            }


            //define Lists
            List<Site> dbSites;
            List<StandingValue> dbStandingValues;

            using (var db = new CPHIDbContext())

            {
                int errorCount = 0;
                LogMessage logMessage = new LogMessage();
                logMessage.DealerGroup_Id = 1;
                try
                {

                    dbSites = db.Sites.ToList();
                    dbStandingValues = db.StandingValues.ToList();


                    logMessage.SourceDate = DateTime.UtcNow;
                    logMessage.Job = this.GetType().Name;

                    Logger.Info($@"[{DateTime.UtcNow}] {filePaths.Count()} file(s) found at {ConfigService.incomingRoot}");   //update logger with how many found

                    //Dictionary<string, string> departmentLookup = new Dictionary<string, string>();
                    //departmentLookup.Add("00", "D90");


                    //go through first file
                    string filePath = filePaths[0];


                    //define variables for use in processing this file
                    int incomingCount = 0;
                    int removedCount = 0;
                    int newCount = 0;
                    int changedCount = 0;

                    if (File.Exists(filePath.Replace(".csv", "-p.csv")))
                    {
                        //already processing a file of this type, skip
                        Logger.Info($@"[{DateTime.UtcNow}] Could not interpret {filePath}, -p file already found ");
                        logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                    }
                    File.Move(filePath, filePath.Replace(".csv", "-p.csv")); //append _p to the file to prevent any other instances also processing these files
                    var newFilepath = filePath.Replace(".csv", "-p.csv");
                    LocksService.AvailabilityOverride = true;

                    string fileName = Path.GetFileName(filePath);
                    var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                    logMessage.SourceDate = fileDate;

                    Logger.Info($@"[{DateTime.UtcNow}] Starting {filePath} ");

                    List<AvailabilityOverride> incomingAvailabilityOverride = new List<AvailabilityOverride>(1000);  //preset the list size (slightly quicker than growing it each time)

                    var allText = File.ReadAllText(newFilepath);
                    string allTextNoCR = allText.Replace("\r", "");
                    var rows = allTextNoCR.Split('\n');


                    var headers = rows.Skip(0).First().ToUpper().Split(',');

                    int defaultRtsTypeId = db.StandingValueTypes.ToList().Find(x => x.Description == "RTSType").Id;
                    int defaultRtsId = db.StandingValues.ToList().Find(x => x.Description == "Other" && x.StandingValueType_Id == defaultRtsTypeId).Id;

                    foreach (var row in rows.Skip(1))
                    {
                        if (!string.IsNullOrEmpty(row))
                        {
                            incomingCount++;


                            try
                            {
                                var cells = row.Split(',');

                                if (cells.Length != headers.Length)
                                {
                                    //something weird happened, not got enough cells for the number of headerCols, skip this record
                                    logMessage.FailNotes = logMessage.FailNotes + $"Skipped item no. {incomingCount}. Had {cells.Length} cells and needed {headers.Length}";
                                    errorCount++;
                                    continue;
                                }

                                //lookup objects required

                                //site
                                string branchName = cells[Array.IndexOf(headers, "BRANCH")];
                                int siteId = dbSites.Find(x => x.Description.ToUpper() == branchName.ToUpper()).Id;

                                //rtsType
                                int rtsTypeId = defaultRtsId;
                                string skill = cells[Array.IndexOf(headers, "SKILL")];
                                var rtsType = dbStandingValues.FirstOrDefault(x => x.Description.ToUpper() == skill.ToUpper());
                                if (rtsType != null)
                                {
                                    rtsTypeId = rtsType.Id;
                                }

                                //dates
                                DateTime date = DateTime.ParseExact(cells[Array.IndexOf(headers, "DATE")], "dd/MM/yyyy", null);

                                AvailabilityOverride e = new AvailabilityOverride(); //initialise new one
                                e.Date = date;
                                e.Hours = decimal.Parse(cells[Array.IndexOf(headers, "HOURS")]);
                                e.RtsType_Id = rtsTypeId;
                                e.Site_Id = siteId;

                                incomingAvailabilityOverride.Add(e);
                            }

                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, VHC: item{incomingCount}  {err.ToString()}";  // <----- DON'T FORGET TO UPDATE!
                                errorCount++;
                                continue;
                            }

                        }
                    }

                    List<AvailabilityOverride> linesWithSkills = incomingAvailabilityOverride.Where(x => x.RtsType_Id != defaultRtsId).ToList();

                    foreach (var item in incomingAvailabilityOverride)
                    {
                        if (item.RtsType_Id == defaultRtsId)
                        {
                            List<AvailabilityOverride> skillLines = linesWithSkills.Where(x => x.Site_Id == item.Site_Id && x.Date == item.Date).ToList();
                            decimal totalSkillHours = 0;
                            foreach (var i in skillLines)
                            {
                                totalSkillHours += i.Hours;
                            }
                            item.Hours -= totalSkillHours;
                        }
                    }


                    var newItems = incomingAvailabilityOverride;
                    newCount = newItems.Count;

                    //find earliest incoming
                    DateTime earliestIncoming = newItems.OrderBy(x => x.Date).First().Date;

                    //find items to remove
                    List<AvailabilityOverride> toRemove = db.AvailabilityOverrides.Where(x => x.Date >= earliestIncoming).ToList();
                    removedCount = toRemove.Count;

                    try
                    {
                        db.AvailabilityOverrides.RemoveRange(toRemove);
                        db.AvailabilityOverrides.AddRange(newItems);  //add them all in one go
                        db.SaveChanges();
                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed add new  {err.ToString()}" + "\r\n\r\n";
                        errorCount++;
                    }

                    logMessage.FinishDate = DateTime.UtcNow;
                    logMessage.ProcessedCount = incomingAvailabilityOverride.Count;
                    logMessage.AddedCount = newCount;
                    logMessage.RemovedCount = removedCount;
                    logMessage.ChangedCount = changedCount;
                    logMessage.IsCompleted = true;
                    logMessage.ErrorCount = errorCount;


                    Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingAvailabilityOverride.Count} item(s) interpreted, found {newCount} new, {removedCount} removed.");
                    try
                    {
                        File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                        if (errorCount > 0)
                        {
                            //we have errors so use the reporter
                            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                            await CentralLoggingService.ReportError("AvailabilityOverride", logMessage, true);

                        }
                        else
                        {
                            //no errors so just save the log
                            logMessage.DealerGroup_Id = 1;
                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();

                        }
                    }
                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                        errorCount++;
                        await CentralLoggingService.ReportError("AvailabilityOverride", logMessage, true);

                    }

                    //trigger cache rebuild
                    await UpdateWebAppService.Trigger("AvailabilityOverrides");
                    stopwatch.Stop();
                }
                catch (Exception err)
                {
                    stopwatch.Stop();
                    errorMessage = err.ToString();
                    logMessage.FailNotes = logMessage.FailNotes + $"General file " + err.ToString();
                    errorCount++;
                    logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                    logMessage.DealerGroup_Id = 1;
                    await CentralLoggingService.ReportError("AvailabilityOverride", logMessage);

                }
                finally
                {
                    db.ChangeTracker.Clear();

                    LocksService.AvailabilityOverride = false;

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "RRG",
                        Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };
                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }



            }
        }



    }



}
