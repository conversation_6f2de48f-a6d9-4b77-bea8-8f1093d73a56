import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ColDef, ColGroupDef, ColumnApi, GridOptions, RowDoubleClickedEvent } from 'ag-grid-community';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { StockItemModalComponent } from 'src/app/components/stockItemModal/stockItemModal.component';
import { BuildTotalAndAverageRowsParams } from 'src/app/model/BuildTotalAndAverageRowsParams';
import { GetOptOutsParams } from 'src/app/model/GetOptOutsParams';
import { VehicleAdvertWithRating } from 'src/app/model/VehicleAdvertWithRating';
import { VehicleOptOutSummaryItem } from 'src/app/model/VehicleOptOutSummaryItem';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { StockListRow } from "../../stockList/StockListRow";
import { OptOutsService } from './optOuts.service';
import { CustomHeaderService } from 'src/app/components/customHeader/customHeader.service';
import { AutoPriceInsightsModalComponent } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import { AutoPriceInsightsModalService } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service';
import { CustomHeaderAdDetail } from 'src/app/components/customHeaderAdDetail/customHeaderAdDetail.component';

@Component({
  selector: 'app-optOuts',
  templateUrl: './optOuts.component.html',
  styleUrls: ['./optOuts.component.scss']
})
export class OptOutsComponent implements OnInit {
  public components: { [p: string]: any; } = {
    agColumnHeader: CustomHeaderAdDetail
  };

  gridOptions: GridOptions;
  gridColumnApi: ColumnApi;

  constructor(
    public service: OptOutsService,
    public customHeader: CustomHeaderService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public gridHelpersService: AGGridMethodsService,
    public columnTypeService: ColumnTypesService,
    public getData: GetDataMethodsService,
    public modalService: NgbModal,
    public excel: ExcelExportService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService
  ) { }

  ngOnInit() {
    if(!this.service.optOutsRowData){
      this.getOptOutsData(true);
    }else{
        this.setGridOptions();
    }
  }

  ngOnDestroy(){
    this.service.gridApi =  null;
  }

  getOptOutsData(resetCopy?: boolean) {
    this.selections.triggerSpinner.next({ message: 'Loading...', show: true });

    const params: GetOptOutsParams = {
      ChosenDate: this.service.chosenDate,
      RetailerSiteIds: this.service.chosenRetailerSiteIds != null || this.service.chosenRetailerSiteIds?.length > 0 ? this.service.chosenRetailerSiteIds : [],
      IncludeNewVehicles: this.service.includeNewVehicles,
      IncludeUnPublishedAds: this.service.includeUnPublishedAds,
      LifeCycleStatuses: Array.from(this.service.chosenLifecycleStatuses)
    }

    this.getData.getOptOuts(params).subscribe((res: VehicleOptOutSummaryItem[]) => {

      res.map(x => {
        if (x.LastManualChange) { x.LastManualChange = new Date(x.LastManualChange); }
      })

      this.service.optOutsRowData = res;

      if(resetCopy)
      {
        this.service.optOutsRowDataCopy = res;
      }
      
      if(!this.service.allLifecycleStatuses)
      {
        this.setLifecycleStatuses();
      }
      
      if (this.service.gridApi) {
        this.service.gridApi.setRowData(this.service.optOutsRowData);
      } else {
        this.setGridOptions();
      }

      this.selections.triggerSpinner.next({ show: false });
    }, (error: any) => {
      console.error('Failed to retrieve opt-outs data', error);
      this.selections.triggerSpinner.next({ show: false });
    });
  }

  setGridOptions() {
    this.gridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params, true),
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.service.optOutsRowData,
      onGridReady: (params) => this.onGridReady(params),
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      floatingFiltersHeight: this.gridHelpersService.getFloatingFilterHeight(),
      groupHeaderHeight: this.gridHelpersService.getGroupHeaderHeight(),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      rowSelection: 'multiple',
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        floatingFilter: true,
        filterParams: { applyButton: false, clearButton: true, applyMiniFilterWhileTyping: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
        headerComponentParams: { showPinAndRemoveOptions: false },
        autoHeaderHeight: true,
        floatingFilterComponentParams: { suppressFilterButton: true }
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: this.provideColumnDefs(),
      getRowClass: (params) => {
        if (params.data.Description === 'Total') return 'total';
      },
      onRowDoubleClicked: (params) => this.rowDoubleClicked(params),
      onSelectionChanged: (params) => this.onRowSelectionChange(),
      onFilterChanged: (params) => this.onRowSelectionChange(),
      pinnedBottomRowData: this.providePinnedBottomRowData(),
      // statusBar: {
      //   statusPanels: [
      //     { statusPanel: 'agTotalAndFilteredRowCountComponent', align: 'right' }
      //   ]
      // }
    }
  }

  onRowSelectionChange(): void {
    if (!this.service.gridApi) { return }

    this.service.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData())
  }
  providePinnedBottomRowData(): any[] {
    const params: BuildTotalAndAverageRowsParams = {
      colsToSkipAverageIfZero: [],
      colsToTotalOrAverage: [],
      colsToTotal: [],
      colsToSetToTrue: [],
      selectedCountFieldName: 'RetailerName',
      labelFieldName: 'Derivative',
      itemName: 'vehicle',
      api: this.service.gridApi,
      includeTotalRow:false,
      showTotalInAverage:false,
    }
    let colDefs = this.provideColumnDefs();
    this.gridHelpersService.extractAverageAndTotalCols(colDefs, params);
    return this.gridHelpersService.buildTotalAndAverageRows(params); 
  }


  
  provideColumnDefs(): (ColDef | ColGroupDef)[] {
    let defs = [
      { headerName: 'Retailer', field: 'RetailerName', colId: 'RetailerName', width: 20, type: 'labelSetFilter' },
      { headerName: 'Reg', field: 'VehicleReg', colId: 'VehicleReg', width: 15, type: 'label' },
      { headerName: 'Stock Number', field: 'StockNumber', colId: 'StockNumber', width: 15, type: 'label' },
      { headerName: 'Vehicle', field: 'Derivative', colId: 'Derivative', width: 10, type: 'label' },
      { headerName: 'Days Listed', field: 'DaysListed', colId: 'DaysListed', width: 10, type: 'number', shouldAverage: true },
      {
        headerName: 'Opt-Out Details', children: [
          { headerName: 'Type', field: 'OptOutType', colId: 'OptOutType', width: 10, type: 'labelSetFilter' },
          { headerName: 'When', field: 'OptOutCreatedDate', colId: 'OptOutCreatedDate', width: 10, type: 'dateShort' },
          { headerName: 'Days', field: 'DaysOptedOutFor', colId: 'DaysOptedOutFor', width: 10, type: 'number', shouldAverage: true },
          { headerName: 'By', field: 'OptOutPerson', colId: 'OptOutPerson', width: 10, type: 'labelSetFilter' }
        ]
      },
      {
        headerName: 'Opt-Out End', children: [
          { headerName: 'When', field: 'OptOutEndDate', colId: 'OptOutEndDate', width: 10, type: 'dateShort' },
          { headerName: 'Days', field: 'DaysOptedOutToGo', colId: 'DaysOptedOutToGo', width: 10, type: 'number', shouldAverage: true },
        ]
      },
      {
        headerName: 'Pricing', children: [
          { headerName: 'Strategy Price', field: 'StrategyPrice', colId: 'StrategyPrice', width: 10, type: 'currency', shouldAverage: true },
          { headerName: 'Selling Price', field: 'SellingPrice', colId: 'SellingPrice', width: 10, type: 'currency', shouldAverage: true },
          { headerName: 'Vs Strategy', valueGetter: (params) => params.data.SellingPrice - params.data.StrategyPrice, colId: 'StrategyPriceVsSelling', width: 10, type: 'currency', shouldAverage: true },
        ]
      },
      {
        headerName: 'Changes', children: [
          { headerName: 'Last changed', field: 'LastChangeDate', colId: 'LastChangeDate', width: 10, type: 'dateYearAndTime' },
          { headerName: 'Days', field: 'Days', colId: 'Days', width: 10, type: 'number', shouldAverage: true },
          { headerName: 'Change', field: 'LastChangeValue', colId: 'LastChangeValue', width: 10, type: 'currency' },
          { headerName: 'Changed By', field: 'LastChangedBy', colId: 'LastChangedBy', width: 10, type: 'labelSetFilter' }
        ]
      },
      {
        headerName: 'TotalChanges', children: [
          { headerName: 'Count', field: 'TotalChangesCount', colId: 'TotalChangesCount', width: 10, type: 'number' },
          { headerName: 'Value', field: 'TotalChangeValue', colId: 'TotalChangeValue', width: 10, type: 'currency', shouldAverage: true, shouldTotal: true },
        ]
      }
    ];

    this.gridHelpersService.workoutColWidths(this.service.optOutsRowData, defs, 14, 6);

    return defs;

  }

  rowDoubleClicked(params: RowDoubleClickedEvent) {

    const row: VehicleOptOutSummaryItem = params.data;

    let allAdIds:number[]=[]
    this.service.gridApi.forEachNodeAfterFilter(node=>{
      if(node.data && !node.isRowPinned()){
        let nodeRow:VehicleOptOutSummaryItem = node.data;
        allAdIds.push(nodeRow.VehicleAdvertId)
      }
    })

    this.autoPriceInsightsModalService.initialise(row.VehicleAdvertId,allAdIds)
    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });
    
  }

  resizeGrid() {
    if (this.service.gridApi) this.service.gridApi.sizeColumnsToFit();
  }

  onGridReady(params) {
    this.service.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.service.gridApi.sizeColumnsToFit();
    this.gridOptions.context = { thisComponent: this };
    this.selections.triggerSpinner.emit({ show: false });
  }

  excelExport() {
    let tableModel = this.service.gridApi.getModel();
    this.excel.createSheetObject(tableModel, 'Stock Pricing - Vehicle Opt-Outs', 1, 1);
  }

  onChosenNewEffectiveDate(newDateString: string) {
    const newDate = new Date(newDateString);
    this.service.chosenDate = newDate;
    this.getOptOutsData(true);
  }

  toggleIncludeNewVehicles() {
    this.service.includeNewVehicles = !this.service.includeNewVehicles;
    this.getOptOutsData(true);
    this.setLifecycleStatuses();
  }

  toggleIncludeUnPublishedAds() {
    this.service.includeUnPublishedAds = !this.service.includeUnPublishedAds;
    this.getOptOutsData(true);
    this.setLifecycleStatuses();
  }

  onChosenLifecycleStatusesChange() {
    this.getOptOutsData();
 }

 provideLifecyleCount(lifecycleStatus: string) {
  return this.service.optOutsRowDataCopy.filter(x => x.LifecycleStatus == lifecycleStatus).length
}

 setLifecycleStatuses(): void {
  // Chosen; if not set then set to all
  if (!this.service.chosenLifecycleStatuses) {
    this.service.chosenLifecycleStatuses = new Set<string>(
      this.service.optOutsRowDataCopy.map(x => x.LifecycleStatus)
    );
  }

  // All available
  this.service.allLifecycleStatuses = [
    ...new Set(this.service.optOutsRowDataCopy.map(x => x.LifecycleStatus))
  ];
}


}
