<!-- <ng-template #strategyModal let-modal> -->
<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
        Strategy Builder

    </h4>

    <button type="button" class="close" aria-label="Close" (click)="dismissModal(false)">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body" [ngClass]="constants.environment.customer">

    <!-- Top table -->
    <div class="autotraderCard">
        <!-- <div class="cardInner">
            <div class="cardHeader">
                Strategy Builder
            </div>
        </div> -->

        <div class="cardBody">



            <table id="topTable">
                <tbody>
                    <tr>
                        <td>
                            <label for="factorId">Strategy Id:</label>
                        </td>
                        <td>
                            <div id="factorId">#{{strategy.UniqueIdForDg}}</div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="factorName">Strategy Name:</label>
                        </td>
                        <td>
                            <input id="factorName" [(ngModel)]="strategy.Name" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="factorComment">Comment:</label>
                        </td>
                        <td>
                            <input id="factorComment" [(ngModel)]="strategy.Comment" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <br>
    <instructionRow [message]="instructionMessage"></instructionRow>

    <!-- placeholder if no rules configured -->
    <div class="panel autotraderCard" *ngIf="strategy.StrategyRules.length == 0">
        <div class="cardInner">
            <div class="cardHeader">

            </div>
            <div class="cardBody">
                No rules have been configured for this strategy. Use the 'Add another rule button below to add a rule.'
            </div>
        </div>
    </div>

    <!-- Each rule card -->
    <div class="panel autotraderCard" *ngFor="let rule of strategy.StrategyRules; let i = index">
        <div class="cardInner">
            <div class="cardHeader">

                <span *ngIf="rule.Criterias.length>1"> Rule {{i+1}} - Does the vehicle meet all of these
                    criteria?</span>
                <span *ngIf="rule.Criterias.length<=1"> Rule {{i+1}} - Does the vehicle meet this criteria?</span>

            </div>
            <div class="cardBody">

                <div class="ruleArea">
                    <!-- Each rule -->
                    <ng-container *ngFor="let criteria of rule.Criterias; let j = index">

                        <instructionRow *ngIf="criteria.boolLikeComparator()"
                        message="Type 'true' or 'false' (without quotes)"></instructionRow>
                        <!-- An equals type row -->
                        <div class="ruleRow" *ngIf="criteria.equalsLikeComparator()">


                            <div class="criteriaLabel"> Criteria {{j+1}}:</div>
                            <div class="criteriaSelection">

                                <!-- Dropdown to pick type of criteria -->
                                <div ngbDropdown container="body" class="d-inline-block" [autoClose]="true">
                                    <button [disabled]="!strategyFieldNames" class="btn btn-primary" id="dropdownBasic1"
                                        ngbDropdownToggle>
                                        <span *ngIf="criteria.FieldNameLabel">
                                            {{criteria.FieldNameLabel }}
                                        </span>
                                        <span *ngIf="!criteria.FieldNameLabel"> Choose criteria. </span>
                                    </button>

                                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                                        <ng-container *ngFor="let strategyFieldName of strategyFieldNames">
                                            <button ngbDropdownItem
                                                (click)="chooseStrategyFieldName(criteria,strategyFieldName)">
                                                {{strategyFieldName.Label}}
                                            </button>
                                        </ng-container>
                                    </div>


                                    <input [placeholder]="'Enter value'" [(ngModel)]="criteria.CriteriaValue" />
                                </div>

                            </div>



                            <div class="addRemoveButton">
                                <button class="btn btn-danger" (click)="maybeRemoveCriteria(rule,criteria)">Remove this
                                    criteria</button>
                            </div>
                        </div>



                        <!-- An between type row -->
                        <div class="ruleRow" *ngIf="criteria.betweenLikeComparator()">
                            <div class="criteriaLabel"> Criteria {{j+1}}:</div>
                            <div class="criteriaSelection">
                                <!-- Dropdown to pick type of criteria -->
                                <div class="criteriaDropdown" ngbDropdown container="body" class="d-inline-block"
                                    [autoClose]="true">
                                    <button [disabled]="!strategyFieldNames" class="btn btn-primary" id="dropdownBasic1"
                                        ngbDropdownToggle>
                                        <span *ngIf="criteria.FieldNameLabel">
                                            {{criteria.FieldNameLabel }}
                                        </span>
                                        <span *ngIf="!criteria.FieldNameLabel"> Choose criteria. </span>
                                    </button>

                                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                                        <ng-container *ngFor="let strategyFieldName of strategyFieldNames">
                                            <button ngbDropdownItem
                                                (click)="chooseStrategyFieldName(criteria,strategyFieldName)">
                                                {{strategyFieldName.Label}}
                                            </button>
                                        </ng-container>
                                    </div>
                                </div>

                                <div class="d-flex align-items-center">
                                    <input [(ngModel)]="criteria.CriteriaValueLow" />
                                    <div>and</div>
                                    <input [(ngModel)]="criteria.CriteriaValueHigh" />
                                </div>
                            </div>

                            <div class="addRemoveButton">
                                <button class="btn btn-danger" (click)="removeCriteria(rule,criteria)">Remove this
                                    criteria</button>
                            </div>

                        </div>

                        <!-- The and row -->
                        <div class="andRow" *ngIf="j!==rule.Criterias.length-1">
                            <div class="criteriaLabel"> </div>
                            <div class="criteriaSelection">
                                AND
                            </div>
                            <div class="addRemoveButton"></div>
                        </div>


                    </ng-container>

                    <!-- Add another criteria row-->
                    <div class="addCriteriaRow">
                        <!-- <div class="criteriaLabel"></div> -->
                        <div class="criteriaSelection"></div>
                        <div class="addRemoveButton">
                            <button class="btn btn-success" (click)="addCriteria(rule)">Add another criteria
                            </button>
                        </div>

                    </div>
                </div>

                <div class="policyArea">
                    <!-- Then this policy row -->
                    <div *ngIf="rule.Criterias.length > 0" class="policyRow d-flex align-items-center ">

                        <div class="policyRowText">
                         <span *ngIf="rule.Criterias.length == 1">      If the above criteria is met, use policy</span>
                         <span *ngIf="rule.Criterias.length > 1">      If all of the above criteria are met, use policy</span>
                        </div>

                        <div ngbDropdown container="body" class="d-inline-block" [autoClose]="true">
                            <button [disabled]="!pricingPolicies" class="btn btn-primary" id="dropdownBasic1"
                                ngbDropdownToggle>
                                <span *ngIf="rule.PricingPolicyLabel">
                                   <span *ngIf="pricingPolicies"> {{rule.PricingPolicyLabel }}</span>
                                   <span *ngIf="!pricingPolicies"> loading... </span>
                                </span>
                                <span *ngIf="!rule.PricingPolicyLabel"> Choose pricing policy... </span>
                            </button>


                            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                                <button (click)="showNewPolicyModal()" ngbDropdownItem>New Pricing
                                    Policy</button>
                                    <div class="dropdown-divider"></div>
                                <ng-container *ngFor="let pricingPolicy of pricingPolicies">
                                    <button ngbDropdownItem (click)="choosePricingPolicy(rule,pricingPolicy)">
                                        {{pricingPolicy.pricingPolicyTextSummary}}
                                    </button>
                                </ng-container>

                            </div>
                            <button class="btn btn-success" [disabled]="!rule.PricingPolicyLabel" (click)="reviewPolicy(rule.PricingPolicyId,rule,null)">Review
                                pricing
                                policy</button>
                        </div>

                    </div>
                </div>



                <!-- Remove rule row-->
                <div class="removeRow">
                    <div class="d-flex justify-content-end">
                        <button class="btn btn-danger" (click)="maybeRemoveRule(rule)">Remove this
                            rule</button>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <!-- Add rule card -->


                <!-- Add rule -->
                <div class="addRow">
                    <div class="d-flex justify-content-end">
                        <button class="btn btn-success" (click)="addRule()">Add another
                            rule</button>
                    </div>
                </div>




    <!-- Default policy card -->
    <div class="panel autotraderCard">
        <div class="cardInner">
            <div class="cardHeader">

                Default pricing policy for this strategy

            </div>
            <div class="cardBody">

                <div class="policyArea">
                    <!-- Then this policy row -->
                    <div class="policyRow d-flex align-items-center ">

                        <div class="policyRowText">
                        <span *ngIf="strategy.StrategyRules.length>1">    If none of the above rules are met, use policy</span>
                        <span *ngIf="strategy.StrategyRules.length==1">    If the above rule is not met, use policy</span>
                        <span *ngIf="strategy.StrategyRules.length==0">   As no rules have been chosen, default to this policy</span>
                        </div>

                        <div ngbDropdown container="body" class="d-inline-block" [autoClose]="true">
                            <button [disabled]="!pricingPolicies" class="btn btn-primary" id="dropdownBasic1"
                                ngbDropdownToggle>
                                <span *ngIf="strategy.DefaultPricingPolicy?.PricingPolicyId">
                                    {{strategy.DefaultPricingPolicy?.PricingPolicyLabel}} </span>
                                <span *ngIf="!strategy.DefaultPricingPolicy?.PricingPolicyId"> Choose pricing
                                    policy... </span>
                            </button>

                            <!-- Set the container to 'body' here -->
                            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                               <button (click)="showNewPolicyModal()" ngbDropdownItem>New Pricing
                                  Policy</button>
                               <div class="dropdown-divider"></div>
                                <ng-container *ngFor="let pricingPolicy of pricingPolicies">
                                    <button ngbDropdownItem (click)="chooseDefaultPricingPolicy(pricingPolicy)">
                                        {{pricingPolicy.pricingPolicyTextSummary}}
                                    </button>
                                </ng-container>

                            </div>
                            <button class="btn btn-success"
                            [disabled]="!strategy.DefaultPricingPolicy?.PricingPolicyId"
                                (click)="reviewPolicy(strategy.DefaultPricingPolicy.PricingPolicyId,null,strategy)">Review
                                pricing
                                policy</button>
                        </div>

                    </div>
                </div>




            </div>
        </div>
    </div>


</div>

<div class="modal-footer">

    <!-- Recalc strategy button -->
    <div [ngbPopover]="!strategy.StrategyId || strategy.HasBeenUsed ? 'Strategy has been used so cannot be deleted'  : null" placement="top" container="body"
    triggers="mouseenter:mouseleave"
    >
    <button type="button" class="btn btn-danger"  [disabled]="!strategy.StrategyId || strategy.HasBeenUsed"
            (click)="maybeDeleteStrategy()">Delete
            Pricing Strategy</button>
    </div>


    <!-- Delete pricing strategy button -->
    <div [ngbPopover]="!strategy.StrategyId || strategy.HasBeenUsed ? 'Strategy has been used so cannot be deleted'  : null" placement="top" container="body"
    triggers="mouseenter:mouseleave"
    >
    <button type="button" class="btn btn-danger"  [disabled]="!strategy.StrategyId || strategy.HasBeenUsed"
            (click)="maybeDeleteStrategy()">Delete
            Pricing Strategy</button>
    </div>


    <!-- Save pricing strategy button -->
    <div [ngbPopover]="isSaveDisabled() ?  errorsPopoverContent : null" placement="top" container="body"
    triggers="mouseenter:mouseleave"
    >
        <button type="button" class="btn btn-success"  [disabled]="isSaveDisabled()"
            (click)="saveStrategy()">Save
            Pricing Strategy</button>
    </div>

    <!-- Close -->
    <button type="button" class="btn btn-primary" (click)="dismissModal(false)">Close</button>
</div>



<!-- Errors template -->
<ng-template #errorsPopoverContent >
    <div *ngIf="buildValidationErrors().length > 0">
        <table>
            <tbody>
                <tr *ngFor="let error of errors">
                    <td>{{ error }}</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div *ngIf="errors.length === 0">

    </div>
</ng-template>
