import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CellClassParams, ColumnApi, DomLayoutType, GridOptions, GridReadyEvent, RowDataUpdatedEvent } from 'ag-grid-community';
import { SiteVM } from 'src/app/model/main.model';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../../cph.pipe';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from '../../../services/selections.service';
import { OrderBookService } from '../../orderBook/orderBook.service';
import { SalesPerformanceService } from '../salesPerformance.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

@Component({
  selector: 'salesPerformanceTable',
  templateUrl: './salesPerformanceTable.component.html',
  styleUrls: ['./../../../../styles/components/_agGrid.scss', './salesPerformanceTable.component.scss'],
})



export class SalesPerformanceTableComponent implements OnInit {
  @Input() public reportType: 'sites' | 'regions';

  @Output() cellClicked = new EventEmitter();

  //for agGrid
  domLayout: DomLayoutType = "autoHeight";

  public gridColumnApi: ColumnApi;
  mainTableGridOptions: GridOptions
  frameworkComponents: { agColumnHeader: any; };

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,

    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public service: SalesPerformanceService,
    public gridHelpersService: AGGridMethodsService,
    public orderBookService: OrderBookService,
    public columnTypeService: ColumnTypesService

  ) { }

  ngOnInit() {
    this.setupGrid();
  }

  ngOnDestroy(){
    this.service.gridApi = null;
    this.service.gridApiRegions = null;
  }

  setupGrid() {

    this.mainTableGridOptions = {
      rowData: this.reportType === 'sites' ? this.service.siteRows : this.service.regionRows,
      pinnedBottomRowData: this.service.totalRows,
      frameworkComponents: this.frameworkComponents,
      onGridReady: (params) => this.onGridReady(params),
      animateRows: false,
      suppressPropertyNamesCheck: true,
      getMainMenuItems: (params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlightsMainTable),
      getContextMenuItems: (params) => { return this.gridHelpersService.getContextMenuItems(params).concat(['separator', 'chartRange']) },
      getLocaleText: (params) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      enableRangeSelection: true,
      onCellClicked: (params) => {
        this.onCellClicked(params);
      },
      getRowId: (params) => { return `${params.data.SiteId}_${params.data.RegionId}_${params.data.IsTotal}` },
      onFirstDataRendered: () => { this.selections.triggerSpinner.next({ show: false }); },
      onRowDataUpdated:(params)=>this.onRowDataUpdated(params),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsMainTable),
      },
      columnDefs: this.getColumnDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }
    }
  }

  onRowDataUpdated(params: RowDataUpdatedEvent<any, any>): void {
    this.setColumnDefs();
    this.resizeGrid();
  }

  getColumnDefs() {
    let thisMonthName = 'In ' + this.constants.appStartTime.toLocaleString('default', { month: 'long' })

    const colDefinitions = [

      { headerName: '', cellRenderer: () => {
        return this.service.salesPerformanceReportType == 'vsLastYear' ? '' : '<div class="chartButton"><i class="fas fa-analytics"></i></div>'
      }, colId: 'chart', width: 60, type: 'label', excelField: 'none' },
      { headerName: this.constants.translatedText.Site, field: 'Label', colId: 'label', width: 150, type: 'label', },

      {
        headerName: this.constants.translatedText.Dashboard_SalesPerformance_UnitsVsTarget,
        children: [
          //vs Tgt cols
          { headerName: this.constants.translatedText.Target, field: 'UnitsTarget', showWithVsTarget: true, showWithRunRate: false, colId: 'UnitsVsTargetBudget', width: 95, type: 'number', },
          { headerName: this.constants.translatedText.Done, field: 'UnitsDone', showWithVsTarget: true, showWithRunRate: false, colId: 'UnitsVsTargetDone', width: 95, type: 'number', },
          { headerName: this.constants.translatedText.DealDetails_Gap, field: 'UnitsGap', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Gap.Units', width: 95, type: 'numberWithColour', },
          { headerName: this.constants.translatedText.Done + ' %', field: 'UnitsGapPercent', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Done.UnitsPercent', width: 95, type: 'percent', },
          { headerName: this.constants.translatedText.Delivered, field: 'Delivered', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Done.Delivered', width: 95, type: 'number', },
          { headerName: this.constants.translatedText.Delivered + ' %', field: 'DeliveredPercent', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Done.DeliveredPercent', width: 95, type: 'percent', },
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_SalesPerformance_GPUVsTarget,
        children: [
          { headerName: this.constants.translatedText.Target, field: 'ProfitPerUnitTarget', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Target.MarginPU', width: 95, type: 'currency', },
          { headerName: this.constants.translatedText.Done, field: 'ProfitPerUnitDone', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Done.MarginPU', width: 95, type: 'currency', },
          { headerName: this.constants.translatedText.DealDetails_Gap, field: 'ProfitPerUnitGap', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Gap.MarginPU', width: 95, type: 'currencyWithFontColour', },
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_SalesPerformance_GPVsTarget,
        children: [
          { headerName: this.constants.translatedText.Target, field: 'ProfitTarget', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Target.Margin', width: 95, type: 'currency', },
          { headerName: this.constants.translatedText.Done, field: 'ProfitDone', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Done.Margin', width: 95, type: 'currency', },
          { headerName: this.constants.translatedText.DealDetails_Gap, field: 'ProfitGap', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Gap.Margin', width: 95, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.Done + ' %', field: 'ProfitGapPercent', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Done.MarginPercent', width: 95, type: 'percent', },
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_SalesPerformance_ProjectedFinish,
        children: [
          { headerName: this.constants.translatedText.Units, field: 'UnitsProjected', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Forecast.Units', width: 95, type: 'number', },
          { headerName: this.constants.translatedText.Dashboard_SalesPerformance_GP, field: 'ProfitProjected', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.Forecast.Margin', width: 95, type: 'currency', },
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_SalesPerformance_ProjectedVs,
        children: [
          { headerName: this.constants.translatedText.Units, field: 'UnitsProjectedGap', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.ProjectedGap.Units', width: 95, type: 'numberWithColour', },
          { headerName: this.constants.translatedText.Dashboard_SalesPerformance_GP, field: 'ProfitProjectedGap', showWithVsTarget: true, showWithRunRate: false, colId: 'SalesPerformance.ProjectedGap.Margin', width: 95, type: 'currencyWithFontColour', },
        ]
      }
    ]
    return colDefinitions;
  }



  onGridReady(params: GridReadyEvent) {
    if (this.reportType === 'sites') {
      this.service.gridApi = params.api;
    } else {
      this.service.gridApiRegions = params.api;
    }

    this.gridColumnApi = params.columnApi;
    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    // to be able to then reference this things within custom menu methods
    if (this.reportType === 'sites') {
      this.service.gridApi.sizeColumnsToFit();
    } else {
      this.service.gridApiRegions.sizeColumnsToFit();
    }

    this.resizeGrid();
    this.selections.triggerSpinner.next({ show: false });
  }








  resizeGrid() {
    setTimeout(() => {

      if (this.gridColumnApi) {
        this.gridColumnApi.autoSizeAllColumns();
      }
      if (this.reportType === 'sites') {
        this.service.gridApi?.sizeColumnsToFit();
      } else {
        this.service.gridApiRegions.sizeColumnsToFit();
      }
    }, 10)
  }



  onCellClicked(site) {

    let cd = site.colDef;

    // console.log(site.node.data, "site.node.data!")

    // SPK-1191 - This prevents the graph from opening
    // Prevent orderbk click throughs on headers that aren't 'Done' / chart
    // if(cd.headerName != this.constants.translatedText.Done && cd.colId != 'chart' && cd.field != 'Label') { return; }

    // If no target value, do not generate graph (nothing to compare it to)
    // Maybe find a more elegant solution to this later
    if (site.node.data.Target == 0 || site.node.data.UnitsTarget == 0 || site.node.data.ProfitTarget == 0) { return; }

    this.cellClicked.next(site);

    // if is site/region name, show chart
    if (cd.field == 'Label' || cd.colId == 'chart') {
      if (!this.service.deliveryDate.amSelectingMonth) { return; } // If not on vs Budget, do not show modal

      this.service.siteNameForModal = site.data.Label;

   
      this.selectSite(site.node.data);
      return;
    }


    this.orderBookService.initOrderbook();

    this.orderBookService.franchises = this.service.franchises;

    this.orderBookService.orderDate.startDate = this.service.orderDate.startDate;
    this.orderBookService.orderDate.endDate = this.service.orderDate.endDate;

    this.orderBookService.accountingDate.startDate = this.service.deliveryDate.startDate;
    this.orderBookService.accountingDate.endDate = this.service.deliveryDate.endDate;
    this.orderBookService.accountingDate.lastChosenMonthName = this.orderBookService.cphPipe.transform(this.service.deliveryDate.startDate, "month", 0);

    this.orderBookService.lateCostOption = this.service.lateCostOption;

    let siteV: SiteVM[] = this.constants.sitesActive.filter(x => x.SiteId == site.node.data.SiteId);

    // Select appropriate sites based on Total/Regional/Single Site
    if (site.node.data.Label == 'Total') {
      siteV = this.constants.sitesActive;
    }
    else {
      if (site.node.data.IsRegion) {
        siteV = this.constants.sitesActive.filter(x => x.RegionId == site.node.data.RegionId);
      }
      else {
        siteV = this.constants.sitesActive.filter(x => x.SiteId == site.node.data.SiteId);
      }

    }

    this.selections.selectedSites = siteV;
    this.orderBookService.orderOption = this.service.orderOption;
    this.selections.selectedSitesIds = [];

    siteV.forEach(site => {
      this.selections.selectedSitesIds.push(site.SiteId);
    });

    let orderTypeTypes: string[];

    if(this.service.includeTradeUnits)
    {
      orderTypeTypes = this.service.department.OrderTypeTypes;
    }
    else
    {
      orderTypeTypes = this.service.department.OrderTypeTypes.filter(x => (!x.includes('Trade') && !x.includes('Auction')));
    }

    let vehicleTypeIds = this.service.vehicleTypeIds;
    let vehTypeTypes: string[] = [];

    vehicleTypeIds.forEach(id => {

      this.constants.VehicleTypes.forEach(t => {

        if (t.Id == id) {
          vehTypeTypes.push(t.Type);
        }
      })

    });

    //dictinct list
    vehTypeTypes = [...new Set(vehTypeTypes)];

    this.orderBookService.orderTypeTypes = orderTypeTypes;
    this.orderBookService.vehicleTypeTypes = vehTypeTypes;
    this.orderBookService.salesExecName = null;
    this.orderBookService.salesExecId = null;
    this.orderBookService.showOrderbook();

    // this.orderBookService.sites = this.orderBookService.sites.filter(x => x.SiteId == siteV[0].SiteId)

  }


  selectSite(s: SiteVM) {
    this.service.selectSite(s);
  }

  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.reportType === 'sites' ? this.service.gridApi.getModel() : this.service.gridApiRegions.getModel()
    let initialColsToColour = 1
    if (this.service.salesPerformanceReportType == 'vsLastYear' || this.service.salesPerformanceReportType == 'vsBudget') initialColsToColour = 2
    this.excel.createSheetObject(tableModel, 'Sales Performance', 1, initialColsToColour);
  }

  setColumnDefs()
  {
    this.mainTableGridOptions.columnDefs = this.getColumnDefs();
  }


}
