﻿using CPHI.Spark.Model;
using CPHI.Repository;
using CPHI.Spark.WindowsAppScraper.ViewModel;
using FlaUI.Core.AutomationElements;
using FlaUI.Core.WindowsAPI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Diagnostics;
using WinSCP;
using CPHI.Spark.Model.ViewModels;

namespace CPHI.Spark.WindowsAppScraper
{
    public class CollectReports
    {

        public static void ExecCollect(int dealerGroupId)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            try
            {
                //--------------------------------------------
                //force power shut if open (shouldn't be open)
                ScraperTools.KillAllPowerTasks();

                System.Diagnostics.Process.Start(@"C:\ucc\SYSTEM\PwrSuite.exe"); //start it
                ScraperTools.Sleep(60000); //wait

                ScraperTools.CheckIfUpdateRequired();

                ScraperTools.Login();  //login


                //wait for home page
                ScraperTools.PickupHomePage();
                ScraperTools.window.SetForeground();
                ScraperTools.Sleep(500);
                ScraperTools.window.SetForeground();


                //-------------collect reports-----------------
                List<ReportAndStatus> allReports = SetReports.BuildListOfReportsWeWant();
                Collect(allReports, dealerGroupId);
                int collectedCount = allReports.Where(x => x.haveCollected).Count();
                Logger.MakeNote($"Ran CollectReports, apparently {collectedCount} have been collected.", "CollectReportsJob");

                Emailer.SendMail($"✔️ SUCCESS: {collectedCount} POWER reports collected", "Have a nice day");

                ScraperTools.Logoff();
                stopwatch.Stop();
            } catch(Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                Emailer.SendMail("❌ FAILURE: Vindis POWER reports failed to collect", e.StackTrace);
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WindowsAppScraper",
                    Customer = "RRG",
                    Environment = "PROD",
                    Task = "CollectReports",
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };
                Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }

        }



        public static void Collect(List<ReportAndStatus> reportsToCollect, int dealerGroupId)
        {
            DateTime startTime = DateTime.Now;

            ScraperTools.NavigateToERM(ScraperTools.window);
            Logger.MakeNote($"File download started.", $"CollectReportsJob: {startTime}");
            DownloadReports(ScraperTools.window, true);
            Logger.MakeNote($"File download completed.", $"CollectReportsJob: {startTime}");
            
            //see which reports we managed to get
            List<FileInfo> newFiles = new DirectoryInfo(ConfigValues.config.fileDestinationTemp).GetFiles("*.*").Where(x => x.CreationTime > startTime).ToList();  //new DriveInfo(ConfigValues.config.fileDestination).RootDirectory.GetFiles("*.*").Where(x => x.CreationTime > startTime).ToList();
            string path = ConfigValues.config.fileDestinationLive;
            string pathTemp = ConfigValues.config.fileDestinationTemp;
            Logger.MakeNote($"Files found.", $"Total file count: {newFiles.Count}");
            //go through and note what we got
            foreach (var f in newFiles)
            {
                string fileName = f.Name;
                string clientId = fileName.Split('_')[0];
                string reportCode = fileName.Split('_')[1];
                ReportAndStatus matchingReport = reportsToCollect.First(x => x.clientId == clientId && x.reportCode == reportCode);
                matchingReport.haveCollected = true;

                //and rename the file
                string newFileName = $"{getDateTimePrefixForDateTime(f.LastAccessTime)}{matchingReport.clientId}_{matchingReport.report}.TXT";
                string ftpFileName = $"{matchingReport.clientId}_{matchingReport.report}.TXT";


                Logger.MakeNote($"FTP file {pathTemp} | {fileName} | {ftpFileName} - Start", "CollectReportsJob");

                //var resultxx = SFTPPut(ConfigValues.config.fileDestinationTemp, fileName);
                
                File.Move(f.FullName, Path.Combine(pathTemp, ftpFileName), true);
                
                var result = SFTPPut(pathTemp, ftpFileName);

                Logger.MakeNote($"FTP file: Result: {result} | {pathTemp} | {fileName}| {ftpFileName} - Complete", "CollectReportsJob");

                //File.Copy(pathTemp + "\\" + ftpFileName , Path.Combine(pathDev, ftpFileName),true);
                File.Move(pathTemp + "\\" + ftpFileName, Path.Combine(path, ftpFileName), true);
                
            }

            string failNotes = "";
            int i = 0;
            int errors = 0;
            foreach (var r in reportsToCollect)
            {
                if (!r.haveCollected)
                {
                    //oh dear.  Add to fail notes.
                    if (i == 0) failNotes += "Failed on ";
                    if (i > 0) failNotes += ",";
                    failNotes += $"{r.clientId}|{r.report}";
                    errors++;
                    i++;
                }
            }

            //report in
            LogMessage m = new LogMessage()
            {
                Job = "WindowsScraperCollected",
                SourceDate = startTime,
                FinishDate = DateTime.Now,
                ProcessedCount = reportsToCollect.Count,
                AddedCount = reportsToCollect.Where(x => x.haveCollected).Count(),
                ErrorCount = reportsToCollect.Where(x => !x.haveCollected).Count(),
                IsCompleted = true,
                FailNotes = failNotes,
                DealerGroup_Id = dealerGroupId
            };


            using (var db = new CPHIDbContext())
            {
                try
                {
                    db.LogMessages.Add(m);
                    db.SaveChanges();
                }
                catch (Exception e)
                {
                    Logger.ReportProblem($"Failed to record reports got stats to db! {e.Message}", "DownloadReports");
                }
            }


            //go back home
            //ScraperTools.NavigateToHomePageNew(ScraperTools.window);

        }



        private static int SFTPPut(string filePath, string fileName)
        {
            Logger.MakeNote($"In SFTPPut", "CollectReportsJob");
            try
            {
                // Setup session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = ConfigValues.config.ftpHostname,
                    UserName = ConfigValues.config.ftpUsername,
                    Password = ConfigValues.config.ftpPassword,
                };

                sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;

                Logger.MakeNote($"Using session", "CollectReportsJob");
                using (Session session = new Session())
                {
                    // Connect
                    Logger.MakeNote($"Going to Open Session", "CollectReportsJob");
                    session.Open(sessionOptions);
                    Logger.MakeNote($"Going to Open Session - complete", "CollectReportsJob");
                    UploadFilesToFtp(session, filePath, fileName);

                }

                return 0;
            }


            catch
            {
                return 1;
            }
        }

        private static void UploadFilesToFtp(Session session, string filePath, string fileName)
        {
            Logger.MakeNote($"In UploadFilesToFtp", "CollectReportsJob");
            var transferOptions = new TransferOptions();
            transferOptions.TransferMode = TransferMode.Binary;
            transferOptions.ResumeSupport.State = TransferResumeSupportState.Off;
            Logger.MakeNote($"FTP file - Start Transfer {filePath} | {fileName} | {session.HomePath}", "CollectReportsJob");

            TransferOperationResult transferResult = session.PutFiles(filePath + "/" + fileName, session.HomePath + "/vindisWinAppScraper/" , false, transferOptions);
            transferResult.Check();// Throw on any error
        }

        private static string getDateTimePrefixForDateTime(DateTime d)
        {
            string dateAndTime = d.ToString("yyyyMMdd_hhmmss-");
            return dateAndTime;
        }




        public static void DownloadReports(AutomationElement window, bool andDelete = false)
        {

            //new approach
            ScraperTools.TypeThings(ScraperTools.window, new[] { VirtualKeyShort.KEY_S }, 200, true); //search
            ScraperTools.TypeThings(ScraperTools.window, new[] { VirtualKeyShort.HOME }, 200); //search
            ScraperTools.TypeThingsWithShift(ScraperTools.window, "{END}", 200);


            ScraperTools.TypeThings(window, new[] { VirtualKeyShort.ALT }, 500);
            ScraperTools.TypeThings(window, "F", 500);
            ScraperTools.TypeThings(window, "A", 500);
            ScraperTools.TypeThings(window, "P", 500);

            //app will now download each report individually, popping up the 'Save In Progress' message whilst download each.
            //we must wait until we've had a period of say 30seconds without seeing the pop-up

            //This method is more sophisticated but having trouble with, just sleep for 15m for now
            //DateTime giveUpTime = DateTime.Now.AddMinutes(15);
            //bool hasDownloadingMessageGone = false;
            //while(DateTime.Now < giveUpTime && !hasDownloadingMessageGone)
            //{
            //    //check if no pop-up
            //    ScraperTools.Sleep(5000);
            //    bool firstCheck = ScraperTools.CheckForPresenceOfControl(ScraperTools.window, "Save In Progress....");
            //    if (!firstCheck)
            //    {
            //        //could be good, is not here now!  check in 5s then another 5s
            //        ScraperTools.Sleep(5000);
            //        bool secondCheck = ScraperTools.CheckForPresenceOfControl(ScraperTools.window, "Save In Progress....");
            //        if (!secondCheck)
            //        {
            //            ScraperTools.Sleep(5000);
            //            bool thirdCheck = ScraperTools.CheckForPresenceOfControl(ScraperTools.window, "Save In Progress....");
            //            if (!thirdCheck)
            //            {
            //                hasDownloadingMessageGone = true;
            //            }

            //        }
            //    }

            //    //is here, sleep for 5s then try again
            //    if(!hasDownloadingMessageGone)ScraperTools.Sleep(5000);
            //}

            ScraperTools.Sleep(5 * 60 * 1000);


            if (andDelete)
            {
                ScraperTools.TypeThings(window, new[] { VirtualKeyShort.DELETE }, 800);
                ScraperTools.TypeThings(window, new[] { VirtualKeyShort.KEY_Y }, 800, true);
            }

            ScraperTools.Sleep(60 * 1000);//sleep for a while to allow deletes

            return;

        }





    }
}
