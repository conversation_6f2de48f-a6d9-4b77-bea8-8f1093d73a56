﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Linq;

namespace CPHI.Spark.DataAccess.AutoPrice
{
    public interface IOptOutsDataAccess
    {
        Task CreateDailyOptOuts(DealerGroupName dealerGroup);
        Task CreateVehicleOptOut(VehicleOptOut newOptOut);
        //Task<List<VehicleOptOutWithSite>> GetLatestOptOuts(DealerGroupName dealerGroup);
       // Task<PricingRuleSet> GetPrevailingPricingRuleSet(PricingRuleSetParams parms);
        Task<VehicleOptOutStatus> GetVehicleOptOutStatus(int vehicleAdvertId, DealerGroupName dealerGroup);
        Task<IEnumerable<VehicleOptOutSummaryItemNew>> GetVehicleOptOutSummaryItems(GetVehicleOptOutSummaryItemsParams parms);
        Task UpdateVehicleOptOut(int vehicleOptOutId, DateTime endDate, int userId, DealerGroupName dealerGroup);
    }

    public class OptOutsDataAccess : IOptOutsDataAccess
    {
        private readonly string _connectionString;
        public OptOutsDataAccess(string connectionString)
        {
            this._connectionString = connectionString;
        }




        public async Task<IEnumerable<VehicleOptOutSummaryItemNew>> GetVehicleOptOutSummaryItems(GetVehicleOptOutSummaryItemsParams parms)
        {
            using (var dapper = new DADapperr(_connectionString))
            {
                string retailerSiteIdsString = null;
                string siteIdsString = null;
                if (parms.RetailerSiteIds?.Count > 0) { retailerSiteIdsString = string.Join(",", parms.RetailerSiteIds); }
                var spParams = new DynamicParameters(new 
                { 
                    retailerSiteIds = retailerSiteIdsString, 
                    parms.ChosenDate,  
                    parms.IncludeNewVehicles,
                    parms.DealerGroupId,
                    parms.IncludeUnPublishedAds,
                    parms.LifeCycleStatuses
                });
                return await dapper.GetAllAsync<VehicleOptOutSummaryItemNew>("autoprice.GET_VehicleOptOutSummaryItemsNew", spParams, commandType: System.Data.CommandType.StoredProcedure);
            }
        }

        public async Task CreateDailyOptOuts(DealerGroupName dealerGroup)
        {
            using (var dapper = new DADapperr(_connectionString))
            {
                var spParams = new DynamicParameters(new { dealerGroupId = (int)dealerGroup });
                await dapper.ExecuteAsync("[autoprice].[CREATE_VehicleOptOuts]", spParams, commandType: System.Data.CommandType.StoredProcedure);
            }
        }

     



        public async Task<VehicleOptOutStatus> GetVehicleOptOutStatus(int vehicleAdvertId, DealerGroupName dealerGroup)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                int dealerGroupId = (int) dealerGroup;

                var status = await db.VehicleOptOuts
                    .Include(x => x.Person)
                    .Join(db.VehicleAdverts,
                          optOut => optOut.VehicleAdvert_Id,
                          advert => advert.Id,
                          (optOut, advert) => new { optOut, advert })
                    .Join(db.RetailerSites,
                          result => result.advert.RetailerSite_Id,
                          site => site.Id,
                          (result, site) => new { result.optOut, result.advert, site })
                    .Where(x => x.optOut.ActualEndDate > DateTime.UtcNow
                    && x.advert.Id == vehicleAdvertId
                                && x.site.DealerGroup_Id == dealerGroupId)
                    .Select(x => x.optOut)
                    .FirstOrDefaultAsync();

                // Old
                // var status2 = await db.VehicleOptOuts.Include(x => x.Person).Where(x => x.ActualEndDate > DateTime.UtcNow).FirstOrDefaultAsync(x => x.VehicleAdvert_Id == vehicleAdvertId);

                if (status == null)
                {
                    return null;
                }

                if (status.ActualEndDate < DateTime.Today)
                {
                    //already ended
                    return null;
                }

                return new VehicleOptOutStatus(status);
            }
        }


        public async Task CreateVehicleOptOut(VehicleOptOut newOptOut)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                await db.AddAsync(newOptOut);
                await db.SaveChangesAsync();
            }
        }

        public async Task UpdateVehicleOptOut(int vehicleOptOutId, DateTime endDate, int userId, DealerGroupName dealerGroup)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                int dealerGroupId = (int)dealerGroup;

                var existing = await db.VehicleOptOuts
                    .Join(db.VehicleAdverts,
                          optOut => optOut.VehicleAdvert_Id,
                          advert => advert.Id,
                          (optOut, advert) => new { optOut, advert })
                    .Join(db.RetailerSites,
                          result => result.advert.RetailerSite_Id,
                          site => site.Id,
                          (result, site) => new { result.optOut, result.advert, site })
                    .Where(x => x.optOut.Id == vehicleOptOutId && x.site.DealerGroup_Id == dealerGroupId)
                    .Select(x => x.optOut)
                    .FirstAsync();

                existing.Person_Id = userId;
                existing.ActualEndDate = endDate;
                await db.SaveChangesAsync();
            }
        }

    }
}
