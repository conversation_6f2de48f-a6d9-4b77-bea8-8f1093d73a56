using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using Quartz;
using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using log4net;
using CPHI.Spark.WebScraper.Services;
using OpenQA.Selenium.Support.UI;
using System.Threading;

namespace CPHI.Spark.WebScraper.Jobs
{
   public class ClickDealerPriceChangeJob : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(ClickDealerPriceChangeJob));
      private static IWebDriver _driver;
      private string customerName;
      private string fileDestination;

      public void Execute() { }

      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         fileDestination = ConfigService.FileDestination;
         fileDestination = fileDestination.Replace("{destinationFolder}", "clickdealer");

         try
         {
            logger.Info("Starting ClickDealer scrape job...");

            // Set up Chrome driver
            var service = ChromeDriverService.CreateDefaultService();
            service.HostName = "127.0.0.1";
            ChromeOptions options = ScraperMethodsService.SetChromeOptions("ClickDealerScrape", 9250);

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

            try
            {
               // Execute the scraping process
               await UpdatePrices();

               // Clean up
               _driver.Quit();
               _driver.Dispose();
               logger.Info("Successfully completed ClickDealer scrape job");
               stopwatch.Stop();
            }
            catch (Exception e)
            {
               logger.Error($"Error during ClickDealer scraping: {e.Message}");
               throw;
            }
         }
         catch (Exception e)
         {
            stopwatch.Stop();
            errorMessage = e.ToString();
            logger.Error($"Problem with ClickDealer scrape job: {e}");
            EmailerService eService = new EmailerService();
            await eService.SendMail($"❌ FAILURE {this.GetType().Name}", $"{e.StackTrace}");
            throw;
         }
      }

      private async Task UpdatePrices()
      {
         // Navigate to ClickDealer login page
         _driver.Navigate().GoToUrl("https://myclickdealer.co.uk/dealer_interface_login.php");
         logger.Info("Navigated to ClickDealer login page");

         // Login process
         Login();

         _driver.Navigate().GoToUrl("https://myclickdealer.co.uk/onetrue_list.php");
         logger.Info("Navigated to ClickDealer stock list page");
      }

      private void Login()
      {
         logger.Info("Attempting to log in to ClickDealer...");

         try
         {
            try
            {
               // Wait for username field and enter username
               IWebElement usernameField = WaitAndFind("//input[@id='username']");
               usernameField.SendKeys("Johnf@the19fam"); // Replace with actual username from config
            }
            catch (Exception ex)
            {
               logger.Warn("Username field not found, checking for alternative indicator...");

               try
               {
                  // Check for alternative element that means login might be skipped
                  IWebElement menuDiv = WaitAndFind("//*[@id='menu']/div[3]");
                  if (menuDiv != null)
                  {
                     logger.Info("Login appears to be already completed, skipping login step.");
                     return;
                  }
               }
               catch
               {
                  // If neither element is found, rethrow the original exception
                  logger.Error("Username field and fallback element not found.");
                  throw new Exception("Username field missing and fallback check failed.", ex);
               }
            }

            // Wait for password field and enter password
            IWebElement passwordField = WaitAndFind("//input[@id='password']");
            passwordField.SendKeys(ConfigService.ClickDealerPassword);

            // Click login button
            IWebElement loginButton = WaitAndFind("//button[@type='submit']", true);

            // Wait for login to complete
            Thread.Sleep(3000);

            logger.Info("Login successful");
         }
         catch (Exception e)
         {
            logger.Error($"Login failed: {e.Message}");
            throw new Exception("Failed to log in to ClickDealer", e);
         }
      }

      public IWebElement WaitAndFind(string findXPath, bool andClick = false)
      {
         IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "ClickDealerScrape", findXPath, andClick);
         return result;
      }


   }
}
