using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using Quartz;
using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using log4net;
using CPHI.Spark.WebScraper.Services;
using OpenQA.Selenium.Support.UI;
using System.Threading;

namespace CPHI.Spark.WebScraper.Jobs
{
    public class ClickDealerPriceChangeJob : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(ClickDealerPriceChangeJob));
        private static IWebDriver _driver;
        private string customerName;
        private string fileDestination;
        
        public void Execute() { }
        
        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            fileDestination = ConfigService.FileDestination;
            fileDestination = fileDestination.Replace("{destinationFolder}", "clickdealer");

            try
            {
                logger.Info("Starting ClickDealer scrape job...");
               
                // Set up Chrome driver
                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";
                ChromeOptions options = ScraperMethodsService.SetChromeOptions("ClickDealerScrape", 9250);
                
                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
                
                try
                {
                    // Execute the scraping process
                    await UpdatePrices();
                    
                    // Clean up
                    _driver.Quit();
                    _driver.Dispose();
                    logger.Info("Successfully completed ClickDealer scrape job");
                    stopwatch.Stop();
                }
                catch (Exception e)
                {
                    logger.Error($"Error during ClickDealer scraping: {e.Message}");
                    throw;
                }
            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                logger.Error($"Problem with ClickDealer scrape job: {e}");
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name}", $"{e.StackTrace}");
                throw;
            }
        }
        
        private async Task UpdatePrices()
        {
            // Navigate to ClickDealer login page
            _driver.Navigate().GoToUrl("https://myclickdealer.co.uk/dealer_interface_login.php");
            logger.Info("Navigated to ClickDealer login page");
            
            // Login process
            await Login();
            
            // Navigate to reports section and download data
            await DownloadReports();
            
            logger.Info("ClickDealer data scraping completed successfully");
        }
        
        private async Task Login()
        {
            logger.Info("Attempting to log in to ClickDealer...");
            
            try
            {
                // Wait for username field and enter username
                IWebElement usernameField = WaitAndFind("//input[@id='username']");
                usernameField.SendKeys("Johnf@the19fam"); // Replace with actual username from config
                
                // Wait for password field and enter password
                IWebElement passwordField = WaitAndFind("//input[@id='password']");
                passwordField.SendKeys("Spark123!"); // Replace with actual password from config
                
                // Click login button
                IWebElement loginButton = WaitAndFind("//button[@type='submit']", true);
                
                // Wait for login to complete
                Thread.Sleep(3000);
                
                logger.Info("Login successful");
            }
            catch (Exception e)
            {
                logger.Error($"Login failed: {e.Message}");
                throw new Exception("Failed to log in to ClickDealer", e);
            }
        }
        
        private async Task DownloadReports()
        {
            logger.Info("Downloading reports from ClickDealer...");
            
            try
            {
                // Navigate to reports section
                _driver.Navigate().GoToUrl("https://www.clickdealer.co.uk/reports");
                Thread.Sleep(2000);
                
                // Select and download required reports
                // This is a placeholder - actual implementation will depend on the ClickDealer interface
                IWebElement exportButton = WaitAndFind("//button[contains(text(), 'Export')]", true);
                
                // Wait for download to complete
                ScraperMethodsService.WaitUntilFileDownloaded("Export");
                
                logger.Info("Reports downloaded successfully");
            }
            catch (Exception e)
            {
                logger.Error($"Failed to download reports: {e.Message}");
                throw new Exception("Failed to download reports from ClickDealer", e);
            }
        }
        
        public IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "ClickDealerScrape", findXPath, andClick);
            return result;
        }
        

    }
}
