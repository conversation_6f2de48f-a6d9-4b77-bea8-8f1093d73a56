import { Component, EventEmitter, Input, Output } from "@angular/core";
import { ConstantsService } from '../services/constants.service';

@Component({
  selector: 'vehicleTypePickerSpain',
  template: `
    <div class="buttonGroup">
      <button
        class="btn btn-primary"
        [ngClass]="{ 'active': vehicleTypeTypesFromParent.includes('New') }"
        (click)="onUpdateVehicleTypesSpain('New')"
      >
        {{ constants.translatedText.New }}

        <span *ngIf="vehicleTypeTypesFromParent.includes('New'); else unchecked;">&#9745;</span>
        <ng-template #unchecked>
          <span>&#9746;</span>
        </ng-template>
      </button>
      <button
        class="btn btn-primary"
        [ngClass]="{ 'active': vehicleTypeTypesFromParent.includes('Used') }"
        (click)="onUpdateVehicleTypesSpain('Used')"
      >
        {{ constants.translatedText.Used }}

        <span *ngIf="vehicleTypeTypesFromParent.includes('Used'); else unchecked;">&#9745;</span>
        <ng-template #unchecked>          <span>&#9746;</span>        </ng-template>
      </button>

      <button
        class="btn btn-primary"
        [ngClass]="{ 'active': vehicleTypeTypesFromParent.includes('Demo') }"
        (click)="onUpdateVehicleTypesSpain('Demo')"
      >
        {{ constants.translatedText.Demo }}

        <span *ngIf="vehicleTypeTypesFromParent.includes('Demo'); else unchecked;">&#9745;</span>
        <ng-template #unchecked>          <span>&#9746;</span>        </ng-template>
      </button>
    </div>  
  `,
  styles: [
    `
      .buttonGroup {
        display: inline-block;
      }
    `
  ]
})

export class VehicleTypePickerSpainComponent {
  @Input() vehicleTypeTypesFromParent: string[];
  @Output() updateVehicleTypes = new EventEmitter<string[]>();

  public vehicleTypeTypes: string[];

  constructor(
    public constants: ConstantsService
  ) { }

  onUpdateVehicleTypesSpain(vehicleType: string) {
    if (this.vehicleTypeTypesFromParent.find(x => x == vehicleType)) {
      this.vehicleTypeTypes = this.vehicleTypeTypesFromParent.filter(x => x != vehicleType);
    } else {
      if (!this.vehicleTypeTypes) this.vehicleTypeTypes = this.constants.clone(this.vehicleTypeTypesFromParent);
      this.vehicleTypeTypes.push(vehicleType);
    }

    this.updateVehicleTypes.emit(this.vehicleTypeTypes);
  }
}
