import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ConstantsService } from "src/app/services/constants.service";
import { RegistrationsService } from "./registrations.service";

// export interface RegistrationsRunRate {
//   RegistrationTarget: number;
//   OrdersBroughtIn: number;
//   OrdersInMonthRetail: number;
//   OrdersInMonthSelf: number;
//   OrdersDoneTotal: number;
//   OrdersDoneVsTarget: number;
//   OrdersDonePercent: number;

//   DaysElapsed: number;
//   DaysRemaining: number;
  
//   OrdersProjection: number;
//   OrdersProjectionPercent: number;
//   OrdersProjectionVsTarget: number;

//   DailyRegsItems: DailyRegsItem[];
// }

@Component({
  selector: "registrationsModal",
  template: `
    <ng-template #registrationsModal let-modal>

<div class="modal-header">
  <h4 class="modal-title" id="modal-basic-title">
   {{service.chosenRowLabel}} {{constants.translatedText.Dashboard_ServiceSales_RunRateAndRequirement}}
  </h4>
  <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div [ngClass]="constants.environment.customer" class="modal-body">

  <div id="chartAndPerUnitTableHolder" >

    <div id="chartHolder">
      <registrationsChart [registrationsRunRate]="service.registrationsRunRate"></registrationsChart>
    </div>

    <div id="perUnitTableHolder">
      <table class="cph noStripes fullWidth">

        <thead>
          <tr>
            <th class="noBottomBorder"></th>
            <th>{{ constants.translatedText.Target }}</th>
            <th class="thin"></th>
            <th colspan="6">{{constants.translatedText.Done}} ({{service.registrationsRunRate.DaysElapsed}} days elapsed)</th>
            <th class="thin"></th>
            <th colspan="3">Run-Rate ({{service.registrationsRunRate.DaysRemaining}} days to go)</th>
          </tr>
          <tr>
            <th></th>
            <th></th>
            <th></th>
            <th>{{ constants.translatedText.BroughtIn }}</th>
            <th>Retail in
              {{service.monthStart.toLocaleString("en-GB", { month: "short" }).substring(0,3)}}
            </th>
            <th>Self-Regs</th>
            <th>{{constants.translatedText.Done}}</th>
            <th>%</th>
            <th>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget>0" class="goodFont">{{ constants.translatedText.AheadBy }}</span>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget==0">{{ constants.translatedText.ShortBy }}</span>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget<0" class="badFont">{{ constants.translatedText.ShortBy }}</span>
            </th>
            <th></th>
            <th>Projection</th>
            <th>%</th>
            <th>
              <span *ngIf="service.registrationsRunRate.OrdersProjectionVsTarget>0" class="goodFont">{{ constants.translatedText.AheadBy }}</span>
              <span *ngIf="service.registrationsRunRate.OrdersProjectionVsTarget==0">{{ constants.translatedText.ShortBy }}</span>
              <span *ngIf="service.registrationsRunRate.OrdersProjectionVsTarget<0" class="badFont">{{ constants.translatedText.ShortBy }}</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Units</td>
            <td>{{service.registrationsRunRate.RegistrationTarget|cph:'number':0}}</td>
            <td></td>
            <td>{{service.registrationsRunRate.OrdersBroughtIn|cph:'number':0}}</td>
            <td>{{service.registrationsRunRate.OrdersInMonthRetail|cph:'number':0}}</td>
            <td>{{service.registrationsRunRate.OrdersInMonthSelf|cph:'number':0}}</td>
            <td>{{service.registrationsRunRate.OrdersDoneTotal|cph:'number':0}}</td>
            <td>{{service.registrationsRunRate.OrdersDonePercent|cph:'percent':0}}</td>
            <td>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget>0" class="goodFont"> {{service.registrationsRunRate.OrdersDoneVsTarget|cph:'number':0}}</span>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget==0"> {{0|cph:'number':0}}</span>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget<0" class="badFont"> {{(service.registrationsRunRate.OrdersDoneVsTarget * -1)|cph:'number':0}}</span>
            </td>
            <td></td>
            <td>{{service.registrationsRunRate.OrdersProjection|cph:'number':0}}</td>
            <td>{{service.registrationsRunRate.OrdersDonePercent|cph:'percent':0}}</td>
            <td>
              <span *ngIf="service.registrationsRunRate.OrdersProjectionVsTarget>0" class="goodFont">
                {{service.registrationsRunRate.OrdersProjectionVsTarget|cph:'number':0}}</span>
              <span *ngIf="service.registrationsRunRate.OrdersProjectionVsTarget==0"> {{0|cph:'number':0}}</span>
              <span *ngIf="service.registrationsRunRate.OrdersProjectionVsTarget<0" class="badFont">
                {{service.registrationsRunRate.OrdersProjectionVsTarget * -1|cph:'number':0}}</span>
            </td>
          </tr>
          <tr>
            <td>{{ constants.translatedText.PerDay }}</td>
            <td></td>
            <td></td>
            <td></td>
            <td>{{service.registrationsRunRate.OrdersProjectionPerDay|cph:'number':1}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget>0" class="goodFont"> {{service.registrationsRunRate.OrdersNeedPerDay |cph:'number':1}}</span>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget==0"> {{service.registrationsRunRate.OrdersNeedPerDay|cph:'number':1}}</span>
              <span *ngIf="service.registrationsRunRate.OrdersDoneVsTarget<0" class="badFont"> {{service.registrationsRunRate.OrdersNeedPerDay|cph:'number':1}}</span>
            </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </tbody>

      </table>
    </div>

  </div>

</div>
<div class="modal-footer">
  <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
</div>
</ng-template>
  `,
  styles: [
    `
    
    
    `
  ]
})
export class RegistrationsModalComponent implements OnInit {
  @ViewChild("registrationsModal", { static: true }) registrationsModal: ElementRef;
  @Input() public 


  constructor(
    public modalService: NgbModal,
    public constants: ConstantsService,
    public service: RegistrationsService

  ) { }

  ngOnDestroy() { }

  ngOnInit() {
    this.initParams();
  }

  initParams() {


  }





  showModal() {
    this.modalService.open(this.registrationsModal, { windowClass: "registrationsModal", keyboard: false, ariaLabelledBy: "modal-basic-title" }).result.then(
      result => {
        //'okd'
        this.modalService.dismissAll()
      },
      //closed
      reason => {
        //cancelled, so no passback
        this.modalService.dismissAll()
      }
    );
  }








}
