<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      Sold Dashboard
    </h4>

    <div *ngIf="service.fromDate && service.toDate" id="datePickersContainer">
      <div class="me-2">
        <label for="fromDate">From</label>
        <input type="date" name="fromDate" [value]="service.fromDate" (change)="setDate($event, 'from')">
      </div>
      <div>
        <label for="toDate">To</label>
        <input type="date" name="toDate" [value]="service.toDate" (change)="setDate($event, 'to')">
      </div>
    </div>

    <sliderSwitch text="Include New Vehicles" (toggle)="toggleIncludeNewVehicles()"
                  [defaultValue]="service.includeNewVehicles">
    </sliderSwitch>

  </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">
    <div class="content-inner-new">

      <div id="spain-overview-grid" class="dashboard-grid cols-18 rows-9">

        <!-- Main Page -->
        <div class="dashboard-grid-container" *ngIf="!!service.rawDataHighlighted">
          <instructionRow
            [message]="'Click on any green bar to filter for matching items.  Click any vehicle to view.'">
          </instructionRow>
          <div id="spain-overview-grid" class="dashboard-grid cols-18 rows-9">

            <!-- Total tile -->
            <div class="dashboard-tile grid-col-1-3 grid-row-1-2 ">

              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Total Vehicles'}"></ng-container>


              <div class="contentsHolder ">
                <!-- (click)="" -->
                <h1 class="bigNumber clickable" id="totalCount">
                  <strong>{{ service.rawDataHighlighted.length|cph:'number':0 }}</strong>
                </h1>
              </div>

            </div>


            <!-- Average selling price -->
            <div class="dashboard-tile grid-col-3-5 grid-row-1-2">

              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Selling Price'}"></ng-container>

              <div class="contentsHolder">
                <div class="bigNumber" id="">{{
                    service.summaryStats.averageFinalPrice
                      |cph:'currency':0
                  }}
                </div>
              </div>
            </div>

            <!-- First Price Position -->
            <div class="dashboard-tile grid-col-5-7 grid-row-1-2">

              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'First Price Position'}"></ng-container>

              <div class="contentsHolder">
                <div class="bigNumber" id="">{{
                    service.summaryStats.firstPricePosition
                      |cph:'percent1dp':0
                  }}
                </div>
              </div>
            </div>

            <!-- Final Price Position -->
            <div class="dashboard-tile grid-col-5-7 grid-row-2-3">

              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Final Price Position'}"></ng-container>

              <div class="contentsHolder">
                <div class="bigNumber" id="">{{
                    service.summaryStats.finalPricePosition
                      |cph:'percent1dp':0
                  }}
                </div>
              </div>
            </div>

            <!-- Total profit tile -->
            <div class="dashboard-tile grid-col-3-5 grid-row-2-3">

              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Profit / Vehicle'}"></ng-container>

              <div class="contentsHolder">
                <div class="bigNumber" id="totalProfit">{{
                    service.summaryStats.averagePerformanceRating
                      |cph:'currency':0
                  }}
                </div>
              </div>

            </div>

            <!-- Average retail rating -->
            <div class="dashboard-tile grid-col-7-9 grid-row-1-2">

              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Retail Rating'}"></ng-container>

              <div class="contentsHolder">
                <div class="bigNumber" id="totalProfit">
                  {{ service.summaryStats.averageRetailRating|cph:'number':0 }}
                </div>
              </div>

            </div>

            <!-- Average Days Listed -->
            <div class="dashboard-tile grid-col-1-3 grid-row-2-3">

              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Days Listed'}"></ng-container>


              <div class="contentsHolder">
                <div class="bigNumber" id="totalProfit">
                  {{ service.summaryStats.averageDaysListed|cph:'number':0 }}
                </div>
              </div>

            </div>


            <!-- Average perf rating -->
            <div class="dashboard-tile grid-col-7-9 grid-row-2-3">

              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Performance Rating'}"></ng-container>
              <div class="contentsHolder">
                <div class="bigNumber" id="totalProfit">{{
                    service.summaryStats.averagePerformanceRating
                      |cph:'number':0
                  }}
                </div>
              </div>

            </div>

            <!-- RetailerName  -->
            <div class="dashboard-tile grid-col-1-5 grid-row-5-7">
              <biChartTileAutoTrader [dataType]="dataTypes.label"
                                     [title]="constants.translatedText.Site" [tileType]="'VerticalBar'"
                                     [labelWidth]="35" [pageParams]="service.getPageParams()"
                                     [fieldName]="'RetailerSiteName'">
              </biChartTileAutoTrader>
            </div>

            <!-- PerformanceRatingScoreBand  -->
            <div class="dashboard-tile grid-col-1-3 grid-row-3-5 ">
              <biChartTile [dataType]="dataTypes.label" [title]="'Performance Rating'"
                           [tileType]="'VerticalBar'" [labelWidth]="30"
                           [pageParams]="service.getPageParams()" [fieldName]="'PerformanceRatingScoreBand'">
              </biChartTile>
            </div>

            <div class="dashboard-tile grid-col-3-5 grid-row-3-5 ">
              <biChartTile [dataType]="dataTypes.label" [title]="'Days Listed Band'"
                           [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
                           [fieldName]="'DaysListedBand'">
              </biChartTile>
            </div>

            <!-- FinallySeenYearMonth  -->
            <div class="dashboard-tile grid-col-7-9 grid-row-3-4">
              <biChartTile [dataType]="dataTypes.label" [title]="'Finally Seen'"
                           [tileType]="'VerticalBarPercent'" [labelWidth]="30"
                           [pageParams]="service.getPageParams()" [fieldName]="'FinallySeenYearMonth'">
              </biChartTile>
            </div>


            <!-- OnBrand  -->
            <div class="dashboard-tile grid-col-5-7 grid-row-3-5">
              <biChartTile [dataType]="dataTypes.label" [title]="'On Brand'" [tileType]="'DonutChart'"
                           [fieldName]="'SimpleBrand'" [customSort]="'SimpleBrand'"
                           [pageParams]="service.getPageParams()">
              </biChartTile>
            </div>


            <!-- Model  -->
            <div class="dashboard-tile grid-col-1-5 grid-row-7-9">
              <biChartTile [dataType]="dataTypes.label" [title]="'Model'" [tileType]="'HorizontalBar'"
                           [pageParams]="service.getPageParams()" [fieldName]="'Model'"></biChartTile>
            </div>

            <!-- First price position banding -->
            <div class="dashboard-tile grid-col-5-7 grid-row-7-8">
              <biChartTile [dataType]="dataTypes.label" [title]="'First Price Position Band'"
                           [tileType]="'HorizontalBar'" [pageParams]="service.getPageParams()"
                           [fieldName]="'FirstPricePositionBand'"></biChartTile>
            </div>

            <!-- Final price position banding -->
            <div class="dashboard-tile grid-col-5-7 grid-row-8-9">
              <biChartTile [dataType]="dataTypes.label" [title]="'Final Price Position Band'"
                           [tileType]="'HorizontalBar'" [pageParams]="service.getPageParams()"
                           [fieldName]="'FinalPricePositionBand'"></biChartTile>
            </div>

            <!-- SiteBrand  -->
            <div class="dashboard-tile grid-col-5-7 grid-row-5-7">
              <biChartTile [dataType]="dataTypes.label" [title]="'Site Brand'" [tileType]="'VerticalBar'"
                           [labelWidth]="30" [pageParams]="service.getPageParams()" [fieldName]="'SiteBrand'">
              </biChartTile>
            </div>

            <!-- AgeBand  -->
            <div class="dashboard-tile grid-col-7-9 grid-row-4-5">
              <biChartTile [dataType]="dataTypes.label" [title]="'Age Band'" [tileType]="'VerticalBar'"
                           [labelWidth]="30" [pageParams]="service.getPageParams()" [fieldName]="'AgeBand'">
              </biChartTile>
            </div>

            <!-- ValueBand  -->
            <div class="dashboard-tile grid-col-7-9 grid-row-5-7 ">
              <biChartTile [dataType]="dataTypes.label" [title]="'Value Band'"
                           [tileType]="'HorizontalBar'" [pageParams]="service.getPageParams()"
                           [fieldName]="'ValueBand'"></biChartTile>
            </div>

            <!-- difference between numbers is count of squares.    first number is where it starts.  e.g. 1-3 is 2 blocks wide, starts on block 1     -->

            <!-- RetailRatingBand  -->
            <div class="dashboard-tile grid-col-7-9 grid-row-7-9 ">
              <biChartTile [dataType]="dataTypes.label" [title]="'Retail Rating Band'"
                           [tileType]="'HorizontalBar'" [pageParams]="service.getPageParams()"
                           [fieldName]="'RetailRatingBand'"></biChartTile>
            </div>

            <!-- Blobs chart -->
            <div class="dashboard-tile grid-col-9-19 grid-row-1-5">
              <ng-container
                *ngTemplateOutlet="tileHeaderTemplate; context: { title: 'Vehicle distribution' }"></ng-container>

              <blobChart [analysisDimensions]="service.analysisDimensions"
                         [updatedBlobItems]="service.newBlobsEmitter"
                         [xAnalysisDimension]="service.xAnalysisDimension" [showAxisDropdowns]="true"
                         [yAnalysisDimension]="service.yAnalysisDimension" [blobItems]="service.blobItems"
                         [service]="service">
              </blobChart>


            </div>


            <!-- Vehicle details tile -->
            <div class="dashboard-tile grid-col-9-19 grid-row-5-9">
              <div class="tileInner">
                <div class="tileHeader">
                  <div class="headerWords">
                    Vehicle Details
                  </div>
                </div>
                <div *ngIf="service.rawData && gridOptions" class="contentHolder"
                     id="gridHolder" #gridHolder>
                  <ag-grid-angular [ngClass]="constants.getGridClass()"
                                   [gridOptions]="gridOptions" [components]="components"></ag-grid-angular>


                </div>
              </div>
            </div>
          </div>
        </div>


        <ng-template #tileHeaderTemplate let-title="title">
          <!-- Tile header -->
          <div class="tileHeader ">
            <div class="headerWords">
              {{ title }}

            </div>

            <div class="interactionIconsHolder">
              <!-- Icon that shows if you are filtering measures on this tile -->
              <div *ngIf="service.isFiltersOn()" class="cancelFilterHolder clickable"
                   (click)="service.clearFilters()">
                <i class="fas fa-filter"></i>
              </div>

              <!-- Icon that shows if you are highlighting measures on this tile -->
              <div *ngIf="service.isHighlightFiltersOn()" class="cancelHighlightsHolder clickable"
                   (click)="service.clearHighlights()">
                <i class="fas fa-filter"></i>
              </div>
            </div>
          </div>
        </ng-template>


      </div>
    </div>
  </div>
