import { EventEmitter, Injectable } from '@angular/core';
import { MultiSelectMonth } from 'src/app/components/datePickerMultiSelect/datePickerMultiSelect.component';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { TopBottomHighlightRule } from 'src/app/model/TopBottomHighlightRule';
import { SalesActivityRow } from './SalesActivityRow';
import { SalesActivityParams } from './SalesActivityParams';
import { SalesActivityInit } from './SalesActivityInit';

@Injectable({
  providedIn: 'root'
})

export class SalesActivityService {
  salesActivity: SalesActivityInit;
  topBottomHighlights:TopBottomHighlightRule[]=[]

  constructor(
    public getData: GetDataMethodsService,
    public selections: SelectionsService,
    public constants: ConstantsService
  ) { }

  initiateSalesActivity() {
    if (!this.salesActivity) {
      let today: Date = this.constants.appStartTime;
      let currentMonth: MultiSelectMonth = {
        startDate: this.constants.deductTimezoneOffset(new Date(today.getFullYear(), today.getMonth(), 1)),
        isSelected: true
      }
      

      this.salesActivity = {
        barThresholds: {
          good: 0.4,
          bad: 0.2
        },
        months: [currentMonth],
        peopleData: null,
        peopleDataChangedEmitter: new EventEmitter(),
        sitesData: null,
        sitesDataChangedEmitter: new EventEmitter(),
        siteIds: null,
        vehicleTypes: [
          { isSelected: true, label: 'New' },
          { isSelected: true, label: 'Used' }
        ],
        salesManagerId: null
      }
    }
  }

  getSitesData() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.getData.getSalesActivity(this.getParams()).subscribe((res: SalesActivityRow[]) => {
      this.salesActivity.sitesData = res;
    }, e => {
      console.error('Error retrieving sales activity sites data: ' + JSON.stringify(e));
    }, () => {
      this.salesActivity.sitesDataChangedEmitter.emit(true);
      this.selections.triggerSpinner.next({ show: false });
    })
  }


  getPeopleData() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.getData.getSalesActivityForSite(this.getParams()).subscribe((res: SalesActivityRow[]) => {
      this.salesActivity.peopleData = res;
    }, e => {
      console.error('Error retrieving sales activity people data: ' + JSON.stringify(e));
    }, () => {
      this.salesActivity.peopleDataChangedEmitter.emit(true);
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getParams(): SalesActivityParams {

    // Get selected vehicle types
    let selectedVehicleTypes: string = '';
    if (this.salesActivity.vehicleTypes.filter(x => x.isSelected).length == 1) {
      selectedVehicleTypes = this.salesActivity.vehicleTypes.find(x => x.isSelected).label;
    } else if (this.salesActivity.vehicleTypes.filter(x => x.isSelected).length > 1) {
      selectedVehicleTypes = 'NewUsed';
    }

    let params: SalesActivityParams = {
      siteIds: this.salesActivity.siteIds ? this.salesActivity.siteIds.toString() : null,
      monthYear: this.constants.formatMonthsForParams(this.salesActivity.months),
      vehicleType: selectedVehicleTypes,
      salesManagerId: (this.salesActivity.salesManagerId == null) ? '' : this.salesActivity.salesManagerId.toString()
    }

    return params;
  }
}
