<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        <span *ngIf="service.showSales && constants.environment.citNoww.pcOfSalesEnquiries">
          {{ constants.environment.citNoww.pcOfSalesEnquiriesText }} {{ service.monthStartDate | cph:'month':0 }}
        </span>
        <span *ngIf="!service.showSales && constants.environment.citNoww.pcOfInvoicedExtWips">
          {{ constants.environment.citNoww.pcOfInvoicedExtWipsText }} {{ service.monthStartDate | cph:'month':0 }}
        </span>
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.CitNow"></sourceDataUpdate>
      </div>
    </h4>


    <!-- FOR SELECTING MONTH -->
    <div class="buttonGroup">
      <!-- previousMonth -->
      <button class="btn btn-primary" (click)="changeMonth(-1)"><i class="fas fa-caret-left"></i></button>

      <!-- dropdownMonth -->
      <div ngbDropdown class="d-inline-block" [autoClose]="true">
        <button (click)="makeMonths()" class="btn btn-primary centreButton"
          ngbDropdownToggle>{{service.monthStartDate|cph:'month':0}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <!-- the ngFor buttons -->
          <button *ngFor="let month of months" (click)="selectMonth(month)" ngbDropdownItem>{{month.name}}</button>

        </div>
      </div>
      <!-- nextMonth -->
      <button class="btn btn-primary" (click)="changeMonth(1)"><i class="fas fa-caret-right"></i></button>
    </div>

    

    <div class="buttonGroup">
      <button class="btn btn-primary" (click)="chooseDepartment(true)"
        [ngClass]="{ active: service.showSales }">
        Sales </button>
      <button class="btn btn-primary" (click)="chooseDepartment(false)"
        [ngClass]="{ active: !service.showSales }">
        Aftersales </button>
    </div>



  </nav>
</nav>

<div class="content-new">
  
  
  <div class="content-inner-new">
    
    <div *ngIf="service.siteRows" id="citnowTableContainer">
      <ng-container *ngIf="constants.environment.citNoww.eDynamixView; else normalCitNowTables">
        <!-- The sites table (EDynamix view)-->
        <ng-container *ngIf="!service.personRows; else peopleTables">
          <citNowTableEDynamix (rowClicked)="onRowClickedEDynamix($event)" [isPeopleRows]="false" [isRegional]="false"></citNowTableEDynamix>
          <div class="tableSpacer"></div>
          <citNowTableEDynamix (rowClicked)="onRowClickedEDynamix($event)" [isPeopleRows]="false" [isRegional]="true"></citNowTableEDynamix>
        </ng-container>
          
        <!-- The people table (EDynamix view) -->
        <ng-template #peopleTables>
          <div class="tableTitle">Technicians</div>
          <citNowTableEDynamix [isPeopleRows]="true" [isTech]="true"></citNowTableEDynamix>
          <div class="tableSpacer"></div>
          <div class="tableTitle">Advisors</div>
          <citNowTableEDynamix [isPeopleRows]="true" [isTech]="false"></citNowTableEDynamix>
        </ng-template>
      </ng-container>

      <!-- The regular sites table -->
      <ng-template #normalCitNowTables>
        <citNowwTable (rowClicked)="onRowClicked($event)" [isRegional]=false></citNowwTable>
        <div class="tableSpacer"></div>
        <citNowwTable (rowClicked)="onRowClicked($event)" [isRegional]=true></citNowwTable>
      </ng-template>
    </div>

    <!-- The charts -->
    <!-- 09/01/2022 - Hide charts for RRG for Aftersales (JP request - SPK-2667) -->
    <div
      *ngIf="!service.showSales && constants.environment.customer !== 'RRGUK' || service.showSales"
      id="chartsHolder"
      [ngClass]="{ 'flex-1': constants.environment.citNoww.eDynamixView }">
      <ng-container *ngFor="let regionalChartSet of service.regionalChartDetails; trackBy:trackByFunction">
        <div class="citnowChartContainer" *ngIf="!!service.regionalChartDetails">
          <citNowwChart [regionDetails]="regionalChartSet"></citNowwChart>
        </div>

      </ng-container>
    </div>

  </div>


  <citNowSiteDetailModal #citNowSiteDetailModal ></citNowSiteDetailModal>
  <citNowVideoBreakdownModal #citNowVideoBreakdownModal></citNowVideoBreakdownModal>

</div>