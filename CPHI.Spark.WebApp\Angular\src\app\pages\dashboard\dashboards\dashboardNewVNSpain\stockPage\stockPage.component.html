<!-- Main Page -->
<div class="dashboard-grid-container" *ngIf="!!service.rawDataHighlighted">
    <div id="spain-overview-grid" class="dashboard-grid cols-8 rows-11">





        <!-- TOP ROW -->

        <!-- Total tile -->
        <div class="dashboard-tile grid-col-1-2 grid-row-1-3 ">

            <div class="tileHeader ">
                <div class="headerWords">
                    <h4> {{constants.capitalise(constants.translatedText.StockAsStock)}}
                    </h4>
                </div>

                <div class="interactionIconsHolder">
                    <!-- Icon that shows if you are filtering measures on this tile -->
                    <div *ngIf="service.isFiltersOn()" class="cancelFilterHolder clickable" (click)="service.clearFilters()">
                        <i class="fas fa-filter"></i>
                    </div>
                
                    <!-- Icon that shows if you are highlighting measures on this tile -->
                    <div *ngIf="service.isHighlightFiltersOn()" class="cancelHighlightsHolder clickable" (click)="service.clearHighlights()">
                        <i class="fas fa-highlighter"></i>
                    </div>
                </div>
                
            </div>

            <div class="contentsHolder ">
                <h1 class="bigNumber clickable" (click)="service.navigateToDistrinetPage()" id="totalCount">
                    <strong>{{service.rawDataHighlighted.length|cph:'number':0}}</strong>
                </h1>
            </div>

        </div>


      

        <!-- Make  -->
        <div class="dashboard-tile grid-col-2-5 grid-row-1-3 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Make" [tileType]="'DonutChartFlex'" [fieldName]="'Franchise'"
                [pageParams]="service.getPageParams()">
            </biChartTile>
        </div>

        <!-- RegionDescription  -->
        <div class="dashboard-tile grid-col-5-6 grid-row-1-3 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Region" [tileType]="'HorizontalBar'"
                [pageParams]="service.getPageParams()" [fieldName]="'RegionDescription'"></biChartTile>
        </div>
        <!-- Customer  -->
        <div class="dashboard-tile grid-col-6-9 grid-row-1-3 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Customer" [tileType]="'VerticalBar'" [labelWidth]="50"
                [pageParams]="service.getPageParams()" [fieldName]="'Customer'">
            </biChartTile>
        </div>

        <!-- Bloqueos  -->
        <!-- <div class="dashboard-tile grid-col-8-9 grid-row-1-3 ">
            <biChartTile [dataType]="dataTypes.label" [title]="'Bloqueos'" [tileType]="'HorizontalBar'"
                [pageParams]="service.getPageParams()" [fieldName]="'Bloqueos'">
            </biChartTile>
        </div> -->



        <!-- NEXT ROW -->

        <!-- Advance  -->
        <div class="dashboard-tile grid-col-1-2 grid-row-3-6 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_Advance" [tileType]="'VerticalBar'" [labelWidth]="40"
                [pageParams]="service.getPageParams()" [fieldName]="'Advance'" [customSort]="'Advance'">
            </biChartTile>
        </div>
        <!-- EnergyType  -->
        <div class="dashboard-tile grid-col-2-4 grid-row-3-6 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_EnergyType" [tileType]="'VerticalBar'" [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'EnergyType'"></biChartTile>
        </div>

        <!-- OriginType  -->
        <div class="dashboard-tile grid-col-4-5 grid-row-3-6 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_OriginType" [tileType]="'HorizontalBar'"
                [pageParams]="service.getPageParams()" [fieldName]="'OriginType'">
            </biChartTile>
        </div>


        <!-- Version  -->
        <div class="dashboard-tile grid-col-5-7 grid-row-3-6 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_Version" [tileType]="'VerticalBar'" [labelWidth]="60"
                [pageParams]="service.getPageParams()" [fieldName]="'Version'">
            </biChartTile>
        </div>

        <!-- Ubicacion   -->
        <div class="dashboard-tile grid-col-7-9 grid-row-3-11 ">
            <biChartTile [dataType]="dataTypes.label" [title]="'Ubicacion'" [tileType]="'VerticalBar'" [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'Ubicacion'">
            </biChartTile>
        </div>


        <!-- NEXT ROW -->

        <!-- CustomerType  -->
        <div class="dashboard-tile grid-col-1-3 grid-row-6-9 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_CustomerType" [tileType]="'HorizontalBar'"
                [pageParams]="service.getPageParams()" [fieldName]="'CustomerType'">
            </biChartTile>
        </div>

        <!-- CentroVendedor  -->
        <!-- <div class="dashboard-tile grid-col-3-5 grid-row-6-9 "> 
            <biChartTile [dataType]="dataTypes.label" [title]="'Centro Vendedor'" [tileType]="'VerticalBar'" [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'CentroVendedor'">
            </biChartTile>
        </div> -->

        <!-- Antiquity  -->
        <div class="dashboard-tile grid-col-3-7 grid-row-6-9 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_Antiquity" [tileType]="'HorizontalBar'"
                [pageParams]="service.getPageParams()" [fieldName]="'Antiquity'">
            </biChartTile>
        </div>


        <!-- NEXT ROW -->

        <!-- ModelCode  -->
        <div class="dashboard-tile grid-col-1-7 grid-row-9-11 ">
            <biChartTile [dataType]="dataTypes.label"  [title]="constants.translatedText.Distrinet_ModelCode" [tileType]="'HorizontalBar'"
                [pageParams]="service.getPageParams()" [fieldName]="'ModelCode'">
            </biChartTile>
        </div>
      
      
     
        <!-- MasLlegada  -->
        <!-- <div class="dashboard-tile grid-col-5-7 grid-row-9-11 ">
            <biChartTile [dataType]="dataTypes.label" [title]="'Mes llegada'" [tileType]="'HorizontalBar'"
                [pageParams]="service.getPageParams()" [fieldName]="'MasLlegada'">
            </biChartTile>
        </div> -->



        <!-- InvoiceDate  -->
        <!-- <div class="dashboard-tile grid-col-1-3 grid-row-2-6 ">
            <biChartTile [isDateBased]="true" [title]="'Invoice Date'" [tileType]="'HorizontalBar'"
                [pageParams]="service.getPageParams()" [fieldName]="'InvoiceDate'">
            </biChartTile>
        </div> -->

