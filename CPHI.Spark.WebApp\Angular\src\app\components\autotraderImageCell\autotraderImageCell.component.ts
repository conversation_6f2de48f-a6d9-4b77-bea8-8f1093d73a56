import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';

@Component({
  selector: 'autotraderImage-cell',
  template: `
    <autotraderImage
      *ngIf="imageUrl"
      [src]="imageUrl"
      [widthpx]="width"
      [alt]="'Car Image'"
    ></autotraderImage>
  `
})
export class AutotraderImageCellComponent implements ICellRendererAngularComp {
  imageUrl: string = '';
  width: number = 84; // default fallback

  agInit(params: any): void {

    // Grab width from cellRendererParams, if provided
    this.width = params?.colDef?.cellRendererParams?.width ?? this.width;
    this.imageUrl = this.getImageUrlFromParams(params);
  }

  refresh(): boolean {
    return false;
  }

  private getImageUrlFromParams(params: any): string {

    if (!params.data || params.node?.rowPinned) return '';

    const url = params.data.ImageURL || params.data.ImageUrl || params.data.AdvertFirstImage;
    if (!url) return '/assets/imgs/autoTrader/placeholder-car.png';

    return url.includes('{resize}') ? url.replace('{resize}', `w${this.width}`) : url;
  }
}
