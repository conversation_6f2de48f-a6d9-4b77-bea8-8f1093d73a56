<div class="dashboard-tile-inner" [ngClass]="{'chart-disabled': disabled }">
  <div class="tileHeader clickable" (click)="goToServiceBookings()">

    <div class="headerWords">

      <h4 *ngIf="this.constants.currentLang != 'es'">Service Bookings - Next {{nextDayCount}} Days
      </h4>

      <h4 *ngIf="this.constants.currentLang == 'es'">{{constants.translatedText.Dashboard_Aftersales_ServiceBookings}} - {{nextDayCount}} {{constants.translatedText.DaysLower}} {{constants.translatedText.Dashboard_Aftersales_Next}}
      </h4>

      <div *ngIf="dataSource" class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
    </div>
</div>
<div id="chartHolder">
  <canvas #barChartCanvas id="myChart"></canvas>
</div>
</div>