//core angular
import { Component, OnInit, } from '@angular/core';
//model and cell renderers
import { DebtType, DebtsSitesSummaryRow, DebtsBonusesSummaryRow, SiteVM } from '../../model/main.model'
//services
import { ConstantsService } from '../../services/constants.service'
import { SelectionsService } from '../../services/selections.service'
//Angular things, non-standard
import { UntypedFormControl } from '@angular/forms';
import { DebtsService } from './debts.service';

@Component({
  selector: 'app-debts',
  templateUrl: './debts.component.html',
  styleUrls: ['./debts.component.scss']
})

export class DebtsComponent implements OnInit {
  filterDebts = new UntypedFormControl('');
  filterBonuses = new UntypedFormControl('');

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: DebtsService
  ) { }
  
  ngOnInit() {
    this.selections.initiateDebts(this.service.debtTypes[0]);
    this.getData();
    this.translateDebtTypes();
    this.setupFilterSubscriptions();
  }

  getData() {
    // If coming in on a debt type, select it
    if (this.selections.debts.incomingDebtType) {
      this.selections.debts.debtType = this.service.debtTypes.find(x => x.description == this.selections.debts.incomingDebtType);
      this.selections.debts.incomingDebtType = null;
    }

    switch (this.selections.debts.debtType.value) {
      case 'debts':
        this.service.getDebtsSummary(
          this.selections.debts.ageAtMonthEnd,
          this.selections.debts.ageOnDueDate,
          this.selections.debts.chosenSiteIds
        );
        break;
      case 'bonuses':
        this.service.getBonusesSummary(
          this.selections.debts.ageAtMonthEnd,
          this.selections.debts.ageOnDueDate,
          this.selections.debts.chosenSiteIds
        );
        break;
      default:
        this.service.getSitesSummary(this.selections.debts.ageAtMonthEnd,this.selections.debts.ageOnDueDate);
        break;
    }
  }

  translateDebtTypes() {
    this.service.debtTypes.forEach(element => {
      if (element.description == 'Sites Overview') {
        element.translation = this.constants.translatedText.SitesOverview;
      }

      if (element.description == 'Debts') {
        element.translation = this.constants.translatedText.Debts;
      }

      if (element.description == 'Bonuses') {
        element.translation = this.constants.translatedText.Bonuses;
      }
    });
  }

  setupFilterSubscriptions() {
    this.filterDebts.valueChanges.subscribe(searchTerm => {
      if (searchTerm.length > 0) {
        this.searchDebts(searchTerm);
      } else {
        this.selections.debts.debtsFiltered = this.selections.debts.debts;
        this.selections.debts.debtsDataChangedEmitter.next(true);
      }
    })

    this.filterBonuses.valueChanges.subscribe(searchTerm => {
      if (searchTerm.length > 0) {
        this.searchBonuses(searchTerm);
      } else {
        this.selections.debts.bonusesFiltered = this.selections.debts.bonuses;
        this.selections.debts.debtsDataChangedEmitter.next(true);
      }
    })
  }

  selectSite(site: DebtsSitesSummaryRow) {
    if (site.IsRegion) {
      this.selections.debts.chosenSiteIds = this.selections.debts.siteSummaryRows.filter(x =>x.IsSite && x.RegionDescription == site.RegionDescription).map(x => x.SiteId);
      this.selections.selectedSites = this.constants.sitesActive.filter(x => this.selections.debts.chosenSiteIds.includes(x.SiteId));
    } else if (site.IsTotal) {
      this.selections.debts.chosenSiteIds = this.selections.debts.siteSummaryRows.filter(x => x.IsSite).map(x => x.SiteId);
      this.selections.selectedSites = this.constants.sitesActive;
    } else {
      this.selections.debts.chosenSiteIds = [site.SiteId];
      this.selections.selectedSites = this.constants.sitesActive.filter(x => x.SiteId == site.SiteId);
    }
    
    this.selections.debts.debtType = this.service.debtTypes[1];
    this.selections.debts.debtsFiltered = null;
    this.getData();
  }

  searchDebts(text: string): DebtsSitesSummaryRow[] {
    if (!this.selections.debts.debts) return;

    let result = this.selections.debts.debts.filter(item => {
      const term = text.toLowerCase();
      return (
        item.DocNo && item.DocNo.toLowerCase().includes(term)) ||
        (item.Customer && item.Customer.toLowerCase().includes(term)) ||
        (item.AccountNo && item.AccountNo.toLowerCase().includes(term)) ||
        (item.AccountName && item.AccountName.toLowerCase().includes(term)) ||
        (item.Invoicedto && item.Invoicedto.toLowerCase().includes(term)) ||
        (item.Name && item.Name.toLowerCase().includes(term)) ||
        (item.MiscRef && item.MiscRef.toLowerCase().includes(term)) ||
        (item.ProblemComment && item.ProblemComment.toLowerCase().includes(term)) ||
        (item.SentToName && item.SentToName.toLowerCase().includes(term)) ||
        (item.Problem && item.Problem.toLowerCase().includes(term)) ||
        (item.StockNo && item.StockNo.toLowerCase().includes(term)) ||
        (item.Type && item.Type.toLowerCase().includes(term)) ||
        (item.DebtType && item.DebtType.toLowerCase().includes(term)) ||
        (item.Exec && item.Exec.toLowerCase().includes(term)) ||
        (item.SiteDescription && item.SiteDescription.toLowerCase().includes(term)) ||
        (item.CustomerRef && item.CustomerRef.toLowerCase().includes(term))
        ;
    });
    //return result
    this.selections.debts.debtsFiltered = result;
    this.selections.debts.debtsDataChangedEmitter.emit(true);
  }

  searchBonuses(text: string): DebtsBonusesSummaryRow[] {
    if (!this.selections.debts.bonuses) return;

    let result = this.selections.debts.bonuses.filter(item => {
      const term = text.toLowerCase();
      return (
        item.Description && item.Description.toLowerCase().includes(term)) ||
        (item.Expense && item.Expense.toString().includes(term)) ||
        (item.Suffix && item.Suffix.toLowerCase().includes(term)) ||
        (item.CustomerRef && item.CustomerRef.toLowerCase().includes(term)) ||
        (item.Narrative && item.Narrative.toLowerCase().includes(term)) ||
        (item.JournalRef && item.JournalRef.toLowerCase().includes(term)) ||
        (item.Customer && item.Customer.toLowerCase().includes(term)) ||
        (item.SalesPerson && item.SalesPerson.toLowerCase().includes(term)) ||
        (item.StockNo && item.StockNo.toLowerCase().includes(term)) ||
        (item.SiteDescription && item.SiteDescription.toLowerCase().includes(term))
        ;
    });
    //return result
    this.selections.debts.bonusesFiltered = result;
    this.selections.debts.bonusesDataChangedEmitter.emit(true);
  }

  onUpdateSites(sites: SiteVM[]) {
    // Set global
    this.selections.selectedSites = sites;
    this.selections.selectedSitesIds = [];

    sites.forEach(element => {
      this.selections.selectedSitesIds.push(element.SiteId);
    });
    
    this.selections.debts.chosenSiteIds = sites.map(x => x.SiteId);
    this.selections.debts.sites = sites;
    this.getData();
  }

  chooseDebtType(debtType: DebtType) {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.selections.debts.debtType = debtType;
    this.getData();
  }

  setAgeOnDueDate(shouldset: boolean) {
    this.selections.debts.ageOnDueDate = shouldset;
    this.getData();
  }

  setAsAtMonthEnd(shouldset: boolean) {
    this.selections.debts.ageAtMonthEnd = shouldset;
    this.getData();
  }
}
