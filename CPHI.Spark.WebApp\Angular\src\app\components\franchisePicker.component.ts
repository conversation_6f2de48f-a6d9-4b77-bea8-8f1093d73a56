import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { selectableItem } from '../model/main.model';
import { ConstantsService } from '../services/constants.service';


@Component({
  selector: 'franchisePicker',
  template:    `
    <!-- Franchise selector -->
    <div ngbDropdown dropright class="d-inline-block">
        <button [ngClass]="buttonClass" class=" btn btn-primary" (click)="generateFranchisesList()"
          ngbDropdownToggle>{{franchiseChosenLabel()}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <!-- ngFor buttons -->  
        <button *ngFor="let franchise of franchises" (click)="toggleItem(franchise)"
            [ngClass]="{'active':franchise.isSelected}" ngbDropdownItem>{{franchise.label}}</button>
            <!-- quick select -->
            <button class="quickSelect" (click)="quickSelectAll()" ngbDropdownItem>{{constants.translatedText.All}}</button>
            <!-- ok / cancel -->
          <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle
              (click)="selectFranchises()">{{constants.translatedText.OKUpper}}</button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
          </div>

        </div>

        
      </div> 
    
    `
  ,
  styles: [`
  
    

    `]
})
export class FranchisePickerComponent implements OnInit {
  @Input() franchisesFromParent: string[];
  @Input() buttonClass: string;
  @Output() updateFranchises = new EventEmitter<string[]>();

  public franchises: selectableItem[];

  
  constructor(
    public constants: ConstantsService,
    
  ) {




  }


  ngOnInit(): void {  }


  franchiseChosenLabel() {
    if (this.franchisesFromParent?.length == 0) {
      return this.constants.translatedText.NoFranchises;
    } else if (this.franchisesFromParent?.length == 1) {
      return this.franchisesFromParent[0]
    } else {
      return this.constants.translatedText.Franchises;
    }
  }

  generateFranchisesList() {
    //recreate local list
    this.franchises = [];
    this.constants.FranchiseCodes.forEach(type => {
      this.franchises.push(
        { label: type, isSelected: false }
      )
    })
    //tag if it's selected
    this.franchises.forEach(s => {
      if (this.franchisesFromParent.indexOf(s.label) > -1) {
        s.isSelected = true;
      }
    })
  }

  
  toggleItem(item: any) {
    item.isSelected = !item.isSelected;
  }

  selectFranchises() {
    this.updateFranchises.emit(this.franchises.filter(e => e.isSelected).map(e => e.label));
  }

  quickSelectAll() {
    //if all selected, select none else select all
    if(this.franchises.filter(x=>x.isSelected).length == this.franchises.length){
      this.franchises.forEach(s=>s.isSelected = false)
    }else{
      this.franchises.forEach(s => {
          s.isSelected = true;
      })
    }
  }



}


