<div  class="tileHeader clickable" (click)="navigateToMainPage()">
  <div class="headerWords">
    <h4>
      EVHC Red & Amber Converted
    </h4>
  </div>
</div>


  <div class="spaceAround">
    <div class="spaceBetween column">
      <h1>
          {{data.SoldPerEvhc |cph:'currency':'0'}}   /  {{data.QuotedPerEvhc |cph:'currency':'0'}}
        </h1>
    <div class="label">
      Sold / Quoted
    </div>
    </div>

    <div class="spaceBetween column" >
      <div class="percentage" [ngClass]="{'goodFont' : data.Converted >= 1 , 'badFont' : data.Converted < 1}">
        <h1><strong>{{data.Converted  |cph:'percent':'0'}}</strong></h1>
      </div>
    <div class="label">
      Conversion Rate
    </div>
    </div>

   
 </div>