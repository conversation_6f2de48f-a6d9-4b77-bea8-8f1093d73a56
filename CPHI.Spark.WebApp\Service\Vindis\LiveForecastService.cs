﻿using CPHI.Spark.Model.Fcst;
using CPHI.Spark.Model.ViewModels.Vindis.Fcst;
using CPHI.Spark.WebApp.DataAccess;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Service.Vindis
{


    public interface ILiveForecastService
    {
        Task<ForecastInputVM> GetLiveForecast(int siteId, int departmentId, int forecastId, int userId, int userId1, DealerGroupName dealerGroup);
        Task<IEnumerable<ForecastWithHasForecastVersion>> GetForecasts(DealerGroupName dealerGroup);
        Task<IEnumerable<Model.Fcst.Department>> AllDepartments(int siteId, DealerGroupName dealerGroup);
        Task<IEnumerable<ForecastVersionVM>> GetForecastVersions(int siteId, int forecastId, DealerGroupName dealerGroup);
        Task<bool> SaveLiveForecast(SaveLiveForecast saveLiveForecast, int userId, DealerGroupName dealerGroup);
        Task<int> UpdateForecastVersionApprovalState(int forecastId, int forecastVersionId, ForecastApprovalStates forecastApprovalStates, int userId, DealerGroupName dealerGroup);
        Task<ForecastInputVM> Recalculate(ForecastInputVM forecastInputVM, int userId, DealerGroupName dealerGroup);
        Task<IEnumerable<ForecastStatus>> GetForecastStatus(DateTime month, int forecastId, DealerGroupName dealerGroup);
        Task<IEnumerable<ForecastReviewVM>> GetForecastReview(DateTime month, DealerGroupName dealerGroup);
        Task<int> RenameVersion(int forecastVersionId, string versionName, DealerGroupName dealerGroup);
        Task<ForecastInputVM> Refresh(ForecastRefresh forecastRefresh, DealerGroupName dealerGroup);
        Task<int> ClearAllForecastVersions(ClearAllForecastVersions clearAllForecastVersions, DealerGroupName dealerGroup);
        Task<IEnumerable<ForecastExcelDownloadItem>> GetForecastExcelDownload(DateTime forecastDate, DealerGroupName dealerGroup);
    }

    public class LiveForecastService : ILiveForecastService
    {
        public readonly ILiveForecastDataAccess liveForecastDataAccess;
        public readonly IUserService userService;

        public LiveForecastService(ILiveForecastDataAccess liveForecastDataAccess, IUserService userService)
        {
            this.liveForecastDataAccess = liveForecastDataAccess;
            this.userService = userService;
        }

        public async Task<ForecastInputVM> GetLiveForecast(int siteId, int departmentId, int forecastId, int forecastVersionId, int userId, DealerGroupName dealerGroup)
        {
            ForecastInputVM forecastInputVM = new ForecastInputVM();
            forecastInputVM.DepartmentId = departmentId;
            
            //Get month from ForecastId
            var month = await liveForecastDataAccess.GetMonth(forecastId, dealerGroup);
            var forecastActualsMonth1Value = await liveForecastDataAccess.GetForecastActuals(departmentId, siteId, month.AddMonths(-1), dealerGroup);
            var forecastActualsMonth2Value = await liveForecastDataAccess.GetForecastActuals(departmentId, siteId, month.AddMonths(-2), dealerGroup);
            var forecastActualsMonth3Value = await liveForecastDataAccess.GetForecastActuals(departmentId, siteId, month.AddMonths(-3), dealerGroup);

            if (siteId == 9 && departmentId == 12)
            {
                forecastActualsMonth1Value = await liveForecastDataAccess.GetForecastActuals(1, 8, month.AddMonths(-1), dealerGroup);
                forecastActualsMonth2Value = await liveForecastDataAccess.GetForecastActuals(1, 8, month.AddMonths(-2), dealerGroup);
                forecastActualsMonth3Value = await liveForecastDataAccess.GetForecastActuals(1, 8, month.AddMonths(-3), dealerGroup);
            }
            else if (siteId == 8 && departmentId == 1)
            {
                forecastActualsMonth1Value = new ForecastActuals();
                forecastActualsMonth2Value = new ForecastActuals();
                forecastActualsMonth3Value = new ForecastActuals();
            }
            

            var siteNonWorkingDays = await liveForecastDataAccess.GetNonWorkingDaysBySiteMonth(siteId, month, dealerGroup);
            var priorForecast = await GetPriorForecast(siteId, departmentId, forecastId, month, siteNonWorkingDays, dealerGroup);

            //var priorForecast = await liveForecastDataAccess.GetForecastLines(departmentId,)
            var financialLinesResult = await liveForecastDataAccess.GetFinancialLines(siteId, null, null, month, dealerGroup);
            var financialLines = financialLinesResult.ToList();

            if (forecastVersionId > 0)
            {
                return await GetLiveForecastForExistingForecastVersion(siteId, departmentId, forecastId, forecastVersionId, userId, forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, priorForecast, month, siteNonWorkingDays, true, dealerGroup);
            }
            else
            {
                //continue
            }

            List<ForecastRowGroup> forecastRowGroups = new List<ForecastRowGroup>();
            InputSummaryTable departmentSummaryTable = new InputSummaryTable();

            var departmentName = await liveForecastDataAccess.GetDepartmentName(departmentId, dealerGroup);
            departmentSummaryTable.Header = $"{departmentName} Summary";
            departmentSummaryTable.rows = new List<InputSummaryTableRow>();

            var forecastActualsAllDepartments = await liveForecastDataAccess.GetForecastActualsAllDepartments(siteId, month, dealerGroup);

            //If Dept is 'New Fakenham' then use details of Site 'VW Fakenham'
            if (siteId == 9)
            {
                var forecastActualsAllDepartmentsVWFakenham = await liveForecastDataAccess.GetForecastActualsAllDepartments(8, month, dealerGroup);

                int newRetailDeptIndex = forecastActualsAllDepartments.FindIndex(d => d.DepartmentId == 12);
                var newFakenhamDepartment = forecastActualsAllDepartmentsVWFakenham.Where(d => d.DepartmentId == 1).First();
                newFakenhamDepartment.DepartmentId = forecastActualsAllDepartments[newRetailDeptIndex].DepartmentId;
                newFakenhamDepartment.DepartmentName = forecastActualsAllDepartments[newRetailDeptIndex].DepartmentName;
                forecastActualsAllDepartments[newRetailDeptIndex] = newFakenhamDepartment;

            }
            else if (siteId == 8) //If Site is VW Fakenham then clear its 'New' department values
            {
                int newDepartmentIndex = forecastActualsAllDepartments.FindIndex(d => d.DepartmentId == 1);
                var newDepartment = forecastActualsAllDepartments.Where(d => d.DepartmentId == 1).First();
                var emptyDepartment = new ForecastActuals() { DepartmentId = newDepartment.DepartmentId, DepartmentName = newDepartment.DepartmentName };
                forecastActualsAllDepartments[newDepartmentIndex] = emptyDepartment;

               
            }


            var forecastActualsCurrentMonth = forecastActualsAllDepartments.FirstOrDefault(a => a.DepartmentId == departmentId);


            //Get values saved for selected Version
            var BroughtInPerUnit = GetBroughtIn(forecastActualsCurrentMonth, GetEnumFromAccountName("Retail Units"));
            var DoneInMonthPerUnit = GetDoneInMonth(forecastActualsCurrentMonth, GetEnumFromAccountName("Retail Units"));

            //Get the list if the Accounts required for selected Department
            var allGroupAccountsResult = await liveForecastDataAccess.GetGroupAccounts(null, dealerGroup);
            var allGroupAccounts = allGroupAccountsResult.ToList();
            var groupAccounts = allGroupAccounts.Where(a => a.DepartmentId == departmentId).ToList();
            var uniqueGroupAccounts = groupAccounts.Select(g => new { g.GroupId, g.GroupLabel }).Distinct();

            //Build up the rows, for selected Department.
            foreach (var groupAccount in uniqueGroupAccounts)
            {
                var forecastRowGroup = new ForecastRowGroup();
                forecastRowGroup.Label = groupAccount.GroupLabel;
                forecastRowGroup.ForecastRows = new List<ForecastRow>();


                var accounts = groupAccounts.Where(a => a.GroupId == groupAccount.GroupId).Select(a => new { a.AccountId, a.AccountName, a.IsStatistic, a.RowNumberFormat, a.Divisor, a.AccountCode}).OrderBy(o => o.AccountCode);

                foreach (var account in accounts)
                {
                    var BroughtIn = GetBroughtIn(forecastActualsCurrentMonth, GetEnumFromAccountName(account.AccountName));
                    var DoneInMonth = GetDoneInMonth(forecastActualsCurrentMonth, GetEnumFromAccountName(account.AccountName));
                    var RunRate = CalculateRunRate(departmentId, siteNonWorkingDays, DoneInMonth, month);//calculate
                    var ForecastMonths = GetForecastMonths(forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, month, GetEnumFromAccountName(account.AccountName));
                    var ForecastActuals = await GetForecastActuals(financialLines, account.AccountId, siteId, departmentId, forecastId, month, BroughtIn + DoneInMonth, priorForecast, dealerGroup);
                    var forecastRow = new ForecastRow()
                    {
                        AccountId = account.AccountId,
                        AccountLabel = account.AccountName,
                        RowNumberFormat = account.RowNumberFormat,
                        ForecastMonths = ForecastMonths,
                        BroughtIn = BroughtIn,
                        DoneInMonth = DoneInMonth,
                        RunRate = RunRate,
                        SiteForecast = 0, // input
                        SiteTotal = BroughtIn + DoneInMonth,
                        DirectorAdj = 0,//input
                        DirectorTotal = BroughtIn + DoneInMonth,


                        ForecastActuals = ForecastActuals
                    };

                    //----Building Department Summary Table data.//
                    var deptRow = new InputSummaryTableRow();
                    deptRow.Id = account.AccountId;
                    deptRow.Label = account.AccountName;
                    deptRow.Budget = ForecastActuals.Where(f => f.ActualLabel == "Budget").First().ActualValue;
                    deptRow.Forecast = BroughtIn + DoneInMonth;
                    deptRow.LastForecast = ForecastActuals.Where(f => f.ActualLabel == "Prior Forecast").First().ActualValue;
                    deptRow.LastYear = ForecastActuals.Where(f => f.ActualLabel == "Last Year").First().ActualValue;
                    deptRow.RowNumberFormart = account.RowNumberFormat;

                    departmentSummaryTable.rows.Add(deptRow);
                    //--------------------------------------------//

                    forecastRowGroup.ForecastRows.Add(forecastRow);


                    //Check and add Per Unit row
                    if (account.Divisor.HasValue)
                    {

                        var divisorAccount = groupAccounts.FirstOrDefault(a => a.AccountId == account.Divisor.Value);
                        var BroughtInPerUnitForDivisor = GetBroughtIn(forecastActualsCurrentMonth, GetEnumFromAccountName(divisorAccount.AccountName));
                        var DoneInMonthPerUnitForDivisor = GetDoneInMonth(forecastActualsCurrentMonth, GetEnumFromAccountName(divisorAccount.AccountName));
                        var SiteTotalPerUnitForDivisorRows = new List<ForecastRow>();

                        //If a Group like Units is already created then check in there
                        if (forecastRowGroups.Any())
                        {
                            SiteTotalPerUnitForDivisorRows = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == account.Divisor.Value)).ToList();
                        }
                        else //If not, Check in the currect group
                        {
                            SiteTotalPerUnitForDivisorRows = forecastRowGroup.ForecastRows.Where(f => f.AccountId == account.Divisor.Value).ToList();
                        }

                        var SiteTotalPerUnitForDivisor = SiteTotalPerUnitForDivisorRows.Any() ? SiteTotalPerUnitForDivisorRows.First().SiteTotal: 0;

                        var DirectorTotalPerUnitForDivisorRows = new List<ForecastRow>();
                        //If a Group like Units is already created then check in there
                        if (forecastRowGroups.Any())
                        {
                            DirectorTotalPerUnitForDivisorRows = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == account.Divisor.Value)).ToList();
                        }
                        else //If not, Check in the currect group
                        {
                            DirectorTotalPerUnitForDivisorRows = forecastRowGroup.ForecastRows.Where(f => f.AccountId == account.Divisor.Value).ToList();
                        }

                        var DirectorTotalPerUnitForDivisor = DirectorTotalPerUnitForDivisorRows.Any()? DirectorTotalPerUnitForDivisorRows.First().DirectorTotal: 0;


                        //Check if its F&I (deptId: 7) For Account: New Finanace Income(8), New Add-on Income (10)& New Gap Income(11) it should get Units from "New Retail"(DeptId: 1)
                        int[] FINewAccountIds = { 8, 10, 11 };
                        //Check if its F&I (deptId: 7) For Account: Used Finanace Income(13), Used Add-on Income (15)& used Gap Income (16)it should get Units from "Used"(DeptId: 6)
                        int[] FIUsedAccountIds = { 13, 15, 16 };
                        var forecastActualsNewRetail = new ForecastActuals();
                        var forecastActualsUsed = new ForecastActuals();
                        var ForecastMonthsPerUnit = new List<ForecastMonth>();

                        if (FINewAccountIds.Contains(account.AccountId))
                        {
                            forecastActualsNewRetail = forecastActualsAllDepartments.Where(d => d.DepartmentId == 1).First();

                            BroughtInPerUnitForDivisor = GetBroughtIn(forecastActualsNewRetail, GetEnumFromAccountName("Retail Units"));
                            DoneInMonthPerUnitForDivisor = GetDoneInMonth(forecastActualsNewRetail, GetEnumFromAccountName("Retail Units"));
                            var forecastActualsMonth1ValueNewRetail = await liveForecastDataAccess.GetForecastActuals(1, siteId, month.AddMonths(-1), dealerGroup);
                            var forecastActualsMonth2ValueNewRetail = await liveForecastDataAccess.GetForecastActuals(1, siteId, month.AddMonths(-2), dealerGroup);
                            var forecastActualsMonth3ValueNewRetail = await liveForecastDataAccess.GetForecastActuals(1, siteId, month.AddMonths(-3), dealerGroup);

                            ForecastMonthsPerUnit = GetForecastMonthsPerUnitCustomUnits(forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, month, GetEnumFromAccountName(account.AccountName), forecastActualsMonth1ValueNewRetail, forecastActualsMonth2ValueNewRetail, forecastActualsMonth3ValueNewRetail);

                        }
                        else if (FIUsedAccountIds.Contains(account.AccountId))
                        {
                            forecastActualsNewRetail = forecastActualsAllDepartments.Where(d => d.DepartmentId == 6).First();

                            BroughtInPerUnitForDivisor = GetBroughtIn(forecastActualsUsed, GetEnumFromAccountName("Retail Units"));
                            DoneInMonthPerUnitForDivisor = GetDoneInMonth(forecastActualsUsed, GetEnumFromAccountName("Retail Units"));
                            var forecastActualsMonth1ValueUsed = await liveForecastDataAccess.GetForecastActuals(6, siteId, month.AddMonths(-1), dealerGroup);
                            var forecastActualsMonth2ValueUsed = await liveForecastDataAccess.GetForecastActuals(6, siteId, month.AddMonths(-2), dealerGroup);
                            var forecastActualsMonth3ValueUsed = await liveForecastDataAccess.GetForecastActuals(6, siteId, month.AddMonths(-3), dealerGroup);

                            ForecastMonthsPerUnit = GetForecastMonthsPerUnitCustomUnits(forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, month, GetEnumFromAccountName(account.AccountName), forecastActualsMonth1ValueUsed, forecastActualsMonth2ValueUsed, forecastActualsMonth3ValueUsed);
                        }
                        else
                        {
                            ForecastMonthsPerUnit = GetForecastMonthsPerUnit(forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, month, GetEnumFromAccountName(account.AccountName));
                        }

                        

                        var unitRow = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == account.Divisor.Value)).FirstOrDefault();

                        if (unitRow == null)
                        {
                            unitRow = forecastRowGroup.ForecastRows.Where(f => f.AccountId == account.Divisor.Value).FirstOrDefault();
                        }
                            
                        var forecastRowPerUnit = new ForecastRow()
                        {
                            IsPerUnit = true,
                            AccountId = account.AccountId,
                            AccountLabel = GeneratePerAccountName(divisorAccount.AccountName),
                            RowNumberFormat = account.RowNumberFormat,

                            ForecastMonths = ForecastMonthsPerUnit,

                            BroughtIn = HelperService.DivideByDecimal(forecastRow.BroughtIn, BroughtInPerUnitForDivisor),
                            DoneInMonth = HelperService.DivideByDecimal(forecastRow.DoneInMonth, DoneInMonthPerUnitForDivisor),
                            RunRate = HelperService.DivideByDecimal(forecastRow.DoneInMonth, DoneInMonthPerUnitForDivisor), // Same as DoneInMonth
                            SiteForecast = HelperService.DivideByDecimal(forecastRow.SiteForecast, BroughtInPerUnitForDivisor + DoneInMonthPerUnitForDivisor), // input
                            SiteTotal = HelperService.DivideByDecimal(forecastRow.SiteTotal, SiteTotalPerUnitForDivisor),
                            DirectorAdj = HelperService.DivideByDecimal(forecastRow.DirectorAdj, BroughtInPerUnitForDivisor + DoneInMonthPerUnitForDivisor), // input
                            DirectorTotal = HelperService.DivideByDecimal(forecastRow.DirectorTotal, DirectorTotalPerUnitForDivisor),

                            ForecastActuals = new List<ForecastActual>()
                        };
                        forecastRowPerUnit.ForecastActuals = CalculateForecastActualsPerUnit(forecastRow, unitRow, forecastRowPerUnit.DirectorTotal);

                        //if its Labour Profit, then calculate percentage
                        if (account.Divisor.Value == 21)
                        {
                            forecastRowPerUnit.RowNumberFormat = "percent";
                        }

                        forecastRowGroup.ForecastRows.Add(forecastRowPerUnit);
                    }


                }

                forecastRowGroups.Add(forecastRowGroup);
            }


            GenerateGrossNetProfitExpensesRowGroups(departmentSummaryTable, groupAccounts, forecastRowGroups, month);

            int priorForecastId = await GetPriorForecastId(month, forecastId, dealerGroup);
            int priorForecastVersionId = 0;
            if (priorForecastId != 0)
            {
                priorForecastVersionId = await liveForecastDataAccess.GetMaxForecastVersionId(priorForecastId, siteId, dealerGroup);
            }

            var lastForecastLines = await liveForecastDataAccess.GetForecastLines(null, priorForecastVersionId, null, month, dealerGroup);


            var dealershipSummary = GetDealershipSummaryFromActuals(forecastActualsAllDepartments, allGroupAccounts, financialLines, departmentId, lastForecastLines, siteId);
            //var departmentSummary = GetDepartmentSummaryFromActuals(forecastRowGroups);

            forecastInputVM.ForecastRowGroups = forecastRowGroups;
            forecastInputVM.DealershipSummaryTable = dealershipSummary;
            forecastInputVM.DepartmentSummaryTable = departmentSummaryTable;

            return CleanUp(forecastInputVM);

        }

        private List<ForecastActual> CalculateForecastActualsPerUnit(ForecastRow forecastRow, ForecastRow unitRow, decimal directorTotal)
        {
            //var financialLines = financialLinesVM.Where(f => f.SiteId == siteId && f.DepartmentId == departmentId && f.AccountId == accountId).ToList();
            var unitBudget = unitRow.ForecastActuals.Where(f => f.ActualLabel == "Budget").Select(f => f.ActualValue).FirstOrDefault();
            var unitBudgetVar = unitRow.ForecastActuals.Where(f => f.ActualLabel == "Budget").Select(f => f.VarianceValue).FirstOrDefault();
            var unitPriorForecast = unitRow.ForecastActuals.Where(f => f.ActualLabel == "Prior Forecast").Select(f => f.ActualValue).FirstOrDefault();
            var unitPriorForecastVar = unitRow.ForecastActuals.Where(f => f.ActualLabel == "Prior Forecast").Select(f => f.VarianceValue).FirstOrDefault();
            var unitLastYear = unitRow.ForecastActuals.Where(f => f.ActualLabel == "Last Year").Select(f => f.ActualValue).FirstOrDefault();
            var unitLastYearVar = unitRow.ForecastActuals.Where(f => f.ActualLabel == "Last Year").Select(f => f.VarianceValue).FirstOrDefault();
            var unitDirectorTotal = unitRow.DirectorTotal;

            var forecastActuals = new List<ForecastActual>();
            var budget = HelperService.DivideByDecimal(forecastRow.ForecastActuals.Where(f => f.ActualLabel == "Budget").Select(f => f.ActualValue).FirstOrDefault(), unitBudget);
            var priorForecastValue = HelperService.DivideByDecimal(forecastRow.ForecastActuals.Where(f => f.ActualLabel == "Prior Forecast").Select(f => f.ActualValue).FirstOrDefault(), unitPriorForecast);
            var lastYear = HelperService.DivideByDecimal(forecastRow.ForecastActuals.Where(f => f.ActualLabel == "Last Year").Select(f => f.ActualValue).FirstOrDefault(), unitLastYear);
            

            var budgetLabel = "Budget"; //financialLines.Where(f => f.IsBudget).First().Name;
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 1, ActualLabel = budgetLabel, ActualValue = budget, VarianceLabel = "vs", VarianceValue = directorTotal - budget });
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 2, ActualLabel = "Prior Forecast", ActualValue = priorForecastValue, VarianceLabel = "vs", VarianceValue = directorTotal - priorForecastValue });
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 3, ActualLabel = "Last Year", ActualValue =lastYear, VarianceLabel = "vs", VarianceValue = directorTotal - lastYear });

            return forecastActuals;
        }

        private InputSummaryTable GetDealershipSummaryFromActuals(List<ForecastActuals> allForecastActuals, List<AccountGroup> allGroupAccounts, List<FinancialLinesVM> financialLinesVM, int departmentId, IEnumerable<ForecastLineVM> lastForecastLines, int siteId)
        {
            InputSummaryTable inputSummaryTable = new InputSummaryTable();
            inputSummaryTable.Header = "Dealership Net Profit Summary";
            inputSummaryTable.rows = new List<InputSummaryTableRow>();

            var groupAccounts = allGroupAccounts.ToList();
            var uniqueDepatments = groupAccounts.Select(g => new { g.DepartmentId, g.DepartmentName }).Distinct();

            if (lastForecastLines == null)
            {
                lastForecastLines = new List<ForecastLineVM>();
            }

            //Build up the rows, for all other Departments - DealerShip Summary.
            decimal deptForecast = 0;
            decimal deptBudget = 0;
            decimal deptlastForecast = 0;
            decimal deptlastYear = 0;
            foreach (var dept in uniqueDepatments)
            {
                deptForecast = 0;
                deptBudget = 0;
                deptlastForecast = 0;
                deptlastYear = 0;

                var forecastActuals = allForecastActuals.FirstOrDefault(a => a.DepartmentId == dept.DepartmentId);
                var deptLastForecastLines = lastForecastLines.Where(a => a.DepartmentId == dept.DepartmentId);
                var accounts = groupAccounts.Where(a => a.DepartmentId == dept.DepartmentId && a.IsStatistic == false).Select(a => new { a.AccountId, a.AccountName, a.IsStatistic, a.RowNumberFormat });
                foreach (var account in accounts)
                {
                    var accountLastForecastLines = deptLastForecastLines.Where(d => d.AccountId == account.AccountId).ToList();
                    var BroughtIn = GetBroughtIn(forecastActuals, GetEnumFromAccountName(account.AccountName));
                    var DoneInMonth = GetDoneInMonth(forecastActuals, GetEnumFromAccountName(account.AccountName));
                    deptForecast += BroughtIn + DoneInMonth;
                    var financialLines = financialLinesVM.Where(f => f.DepartmentId == dept.DepartmentId && f.AccountId == account.AccountId).ToList();
                    deptBudget += GetBudget(financialLines);
                    deptlastYear += GetLastYear(financialLines);
                    deptlastForecast += accountLastForecastLines.Select(a => a.Value).Sum();
                }

                inputSummaryTable.rows.Add(new InputSummaryTableRow()
                {
                    Id = dept.DepartmentId,
                    Label = dept.DepartmentName,
                    Budget = deptBudget,
                    Forecast = deptForecast,
                    LastForecast = deptlastForecast,
                    LastYear = deptlastYear,
                    RowNumberFormart = "currency"

                });
            }

            RemoveNewFakenhamFromDealershipSummary(inputSummaryTable, siteId);

            //Total 
            inputSummaryTable.rows.Add(new InputSummaryTableRow()
            {
                Id = 0,
                Label = "Dealership",
                Budget = inputSummaryTable.rows.Select(r => r.Budget).Sum(),
                Forecast = inputSummaryTable.rows.Select(r => r.Forecast).Sum(),
                LastForecast = inputSummaryTable.rows.Select(r => r.LastForecast).Sum(),
                LastYear = inputSummaryTable.rows.Select(r => r.LastYear).Sum(),
                RowNumberFormart = "currency"
            });

            return inputSummaryTable;
        }

        private InputSummaryTable GetDepartmentSummary(List<ForecastRowGroup> forecastRowGroups, string departmentName)
        {

            InputSummaryTable inputSummaryTable = new InputSummaryTable();
            
            inputSummaryTable.Header = $"{departmentName} Summary";
            inputSummaryTable.rows = new List<InputSummaryTableRow>();

            var accounts = forecastRowGroups.SelectMany(f => f.ForecastRows).Where(a => a.IsPerUnit == false && a.AccountId != 0);
            foreach (var row in accounts)
            {
                inputSummaryTable.rows.Add(new InputSummaryTableRow()
                {
                    Id = row.AccountId,
                    Label = row.AccountLabel,
                    Forecast = row.DirectorTotal,
                    Budget = row.ForecastActuals.Where(f => f.ActualLabel == "Budget").Select(f => f.ActualValue).FirstOrDefault(),
                    LastForecast = row.ForecastActuals.Where(f => f.ActualLabel == "Prior Forecast").Select(f => f.ActualValue).FirstOrDefault(),
                    LastYear = row.ForecastActuals.Where(f => f.ActualLabel == "Last Year").Select(f => f.ActualValue).FirstOrDefault(),
                    RowNumberFormart = row.RowNumberFormat
                }) ;
            }

            var deptRowGrossProfit = new InputSummaryTableRow();
            deptRowGrossProfit.Id = 0;
            deptRowGrossProfit.Label = "Gross Profit";
            deptRowGrossProfit.Budget = forecastRowGroups.Where(f => f.Label == "Gross Profit").First().ForecastRows.First().ForecastActuals.Where(f => f.ActualLabel == "Budget").First().ActualValue;
            deptRowGrossProfit.Forecast = forecastRowGroups.Where(f => f.Label == "Gross Profit").First().ForecastRows.First().DirectorTotal;
            deptRowGrossProfit.LastForecast = forecastRowGroups.Where(f => f.Label == "Gross Profit").First().ForecastRows.First().ForecastActuals.Where(f => f.ActualLabel == "Prior Forecast").First().ActualValue;
            deptRowGrossProfit.LastYear = forecastRowGroups.Where(f => f.Label == "Gross Profit").First().ForecastRows.First().ForecastActuals.Where(f => f.ActualLabel == "Last Year").First().ActualValue;

            deptRowGrossProfit.RowNumberFormart = "currency";

            var deptRowNetProfit = new InputSummaryTableRow();
            deptRowNetProfit.Id = 0;
            deptRowNetProfit.Label = "Net Profit";
            deptRowNetProfit.Budget = forecastRowGroups.Where(f => f.Label == "Net Profit").First().ForecastRows.First().ForecastActuals.Where(f => f.ActualLabel == "Budget").First().ActualValue;
            deptRowNetProfit.Forecast = forecastRowGroups.Where(f => f.Label == "Net Profit").First().ForecastRows.First().DirectorTotal;
            deptRowNetProfit.LastForecast = forecastRowGroups.Where(f => f.Label == "Net Profit").First().ForecastRows.First().ForecastActuals.Where(f => f.ActualLabel == "Prior Forecast").First().ActualValue;
            deptRowNetProfit.LastYear = forecastRowGroups.Where(f => f.Label == "Net Profit").First().ForecastRows.First().ForecastActuals.Where(f => f.ActualLabel == "Last Year").First().ActualValue;

            deptRowNetProfit.RowNumberFormart = "currency";

            inputSummaryTable.rows.Insert(inputSummaryTable.rows.Count - 2, deptRowGrossProfit);
            inputSummaryTable.rows.Add(deptRowNetProfit);

            return inputSummaryTable;

        }

        private async Task<InputSummaryTable> GetDealershipSummary(DateTime month, int forecastId, int siteId, DealerGroupName dealerGroup)
        {
            InputSummaryTable inputSummaryTable = new InputSummaryTable();
            inputSummaryTable.Header = "Dealership Net Profit Summary";
            inputSummaryTable.rows = new List<InputSummaryTableRow>();

            var result = await liveForecastDataAccess.GetDealershipSummary(month, null, siteId, dealerGroup);
            var dealershipSummaryCurrentForecastId = result.Where(r => r.ForecastId == forecastId).ToList();

            int priorForecastId = await GetPriorForecastId(month, forecastId, dealerGroup);
            int priorForecastVersionId = 0;
            if (priorForecastId != 0)
            {
                priorForecastVersionId = await liveForecastDataAccess.GetMaxForecastVersionId(priorForecastId, siteId, dealerGroup);
            }
            var dealershipSummaryPriorForecastId = result.Where(r => r.VersionId == priorForecastVersionId).ToList();

            foreach (var item in dealershipSummaryCurrentForecastId)
            {
                var lf = dealershipSummaryPriorForecastId.Where(d => d.DepartmentId == item.DepartmentId);
                inputSummaryTable.rows.Add(new InputSummaryTableRow()
                {
                    Id = item.DepartmentId,
                    Label = item.DepartmentName,
                    Budget = item.BudgetValueNet,
                    Forecast = item.DirectorTotalNet.HasValue ? item.DirectorTotalNet.Value : 0,
                    LastYear = item.LastYearValueNet,
                    LastForecast = (lf.Any()) && lf.First().DirectorTotalNet.HasValue ? lf.First().DirectorTotalNet.Value : 0,
                    RowNumberFormart = "currency"
                });
            }


            RemoveNewFakenhamFromDealershipSummary(inputSummaryTable, siteId);
            

            //Total 
            inputSummaryTable.rows.Add(new InputSummaryTableRow()
            {
                Id = 0,
                Label = "Dealership",
                Budget = inputSummaryTable.rows.Select(r => r.Budget).Sum(),
                Forecast = inputSummaryTable.rows.Select(r => r.Forecast).Sum(),
                LastYear = inputSummaryTable.rows.Select(r => r.LastYear).Sum(),
                LastForecast = inputSummaryTable.rows.Select(r => r.LastForecast).Sum(),
                RowNumberFormart = "currency"
            });

            return inputSummaryTable;
        }

        private void RemoveNewFakenhamFromDealershipSummary(InputSummaryTable inputSummaryTable, int siteId)
        {
            //Remove 'New Fakenham' if Site is not VW Huntingdon
            if (siteId == 9)//Reset the order
            {
                var rowIndexToMove = inputSummaryTable.rows.FindIndex(r => r.Id == 12);
                if (rowIndexToMove > 0)
                {
                    var rowToMove = inputSummaryTable.rows[rowIndexToMove];
                    inputSummaryTable.rows.RemoveAt(rowIndexToMove);
                    inputSummaryTable.rows.Insert(1, rowToMove);
                }
            }
            else // Remove from the list
            {
                var rowToRemove = inputSummaryTable.rows.Where(r => r.Id == 12);
                if (rowToRemove.Any()) inputSummaryTable.rows.Remove(rowToRemove.First());
            }
        }

        private ForecastRowGroup GenerateNetProfitRowGroup(IEnumerable<AccountGroup> groupAccounts, List<ForecastRowGroup> forecastRowGroups, decimal expenseSiteForecast, decimal expenseDirectorAdj, decimal expenseSiteTotal, decimal expenseDirectorTotal, DateTime month)
        {
            var netProfitforecastRowGroup = new ForecastRowGroup();
            netProfitforecastRowGroup.ForecastRows = new List<ForecastRow>();
            var netProfitForecastRow = new ForecastRow();
            var forecastActuals = new List<ForecastActual>();
            var forecastMonths = new List<ForecastMonth>();

            var netProfitAccountIds = groupAccounts.Where(ga => ga.IsStatistic == false).Select(ga => ga.AccountId).ToArray();

            //var month1 = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId)).SelectMany(fr => fr.ForecastMonths.Where(fm => fm.DisplayOrder == 3).Select(fm => fm.Value))).Sum();
            //var month2 = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId)).SelectMany(fr => fr.ForecastMonths.Where(fm => fm.DisplayOrder == 2).Select(fm => fm.Value))).Sum();
            //var month3 = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId)).SelectMany(fr => fr.ForecastMonths.Where(fm => fm.DisplayOrder == 1).Select(fm => fm.Value))).Sum();

            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = month1 });
            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = month2 });
            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = month3 });

            //netProfitForecastRow.BroughtIn = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.BroughtIn)).Sum();
            //netProfitForecastRow.DoneInMonth = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DoneInMonth)).Sum();
            //netProfitForecastRow.SiteForecast = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.SiteForecast)).Sum();// + expenseSiteForecast + expenseDirectorAdj;
            netProfitForecastRow.SiteTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.SiteTotal)).Sum();
            netProfitForecastRow.DirectorAdj = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorAdj)).Sum() ;
            netProfitForecastRow.DirectorTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorTotal)).Sum();



            netProfitForecastRow.ForecastMonths = forecastMonths;
            
            netProfitForecastRow.AccountLabel = "Net Profit";
            netProfitforecastRowGroup.Label = "Net Profit";

            var budget = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 1).Select(fm => fm.ActualValue))).Sum();
            var budgetVariance = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 1).Select(fm => fm.VarianceValue))).Sum();

            var priorForecastValue = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 2).Select(fm => fm.ActualValue))).Sum();
            var priorForecastVarianceValue = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 2).Select(fm => fm.VarianceValue))).Sum();

            var lastYear = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 3).Select(fm => fm.ActualValue))).Sum();
            var lastYearVariance = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => netProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 3).Select(fm => fm.VarianceValue))).Sum();

            forecastActuals.Add(new ForecastActual() { DisplayOrder = 1, ActualLabel = "Budget", ActualValue = budget, VarianceLabel = "vs", VarianceValue = budgetVariance });
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 2, ActualLabel = "Prior Forecast", ActualValue = priorForecastValue, VarianceLabel = "vs", VarianceValue = priorForecastVarianceValue });
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 3, ActualLabel = "Last Year", ActualValue = lastYear, VarianceLabel = "vs", VarianceValue = lastYearVariance });

            netProfitForecastRow.ForecastActuals = forecastActuals;




            netProfitforecastRowGroup.ForecastRows.Add(netProfitForecastRow);

            return netProfitforecastRowGroup;

        }

        private ForecastRowGroup GenerateGrossProfitRowGroup(IEnumerable<AccountGroup> groupAccounts, List<ForecastRowGroup> forecastRowGroups, DateTime month)
        {
            var grossProfitforecastRowGroup = new ForecastRowGroup();
            grossProfitforecastRowGroup.ForecastRows = new List<ForecastRow>();
            var grossProfitForecastRow = new ForecastRow();
            var forecastMonths = new List<ForecastMonth>();
            var forecastActuals = new List<ForecastActual>();

            var grossProfitAccountIds = groupAccounts.Where(ga => ga.IsInGrossProfit == true).Select(ga => ga.AccountId).ToArray();

            //var month1 = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId)).SelectMany(fr => fr.ForecastMonths.Where(fm => fm.DisplayOrder == 3).Select(fm => fm.Value))).Sum();
            //var month2 = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId)).SelectMany(fr => fr.ForecastMonths.Where(fm => fm.DisplayOrder == 2).Select(fm => fm.Value))).Sum();
            //var month3 = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId)).SelectMany(fr => fr.ForecastMonths.Where(fm => fm.DisplayOrder == 1).Select(fm => fm.Value))).Sum();

            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = month1 });
            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = month2 });
            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = month3 });

            //var forecastActual = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId)).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 1).Select(fm => fm.Value))).Sum();


            grossProfitForecastRow.BroughtIn = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.BroughtIn)).Sum();
            grossProfitForecastRow.DoneInMonth = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DoneInMonth)).Sum();
            grossProfitForecastRow.SiteForecast = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.SiteForecast)).Sum();
            grossProfitForecastRow.SiteTotal = grossProfitForecastRow.BroughtIn + grossProfitForecastRow.DoneInMonth + grossProfitForecastRow.SiteForecast;
            grossProfitForecastRow.DirectorAdj = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorAdj)).Sum();
            grossProfitForecastRow.DirectorTotal = grossProfitForecastRow.SiteTotal + grossProfitForecastRow.DirectorAdj;

            grossProfitForecastRow.ForecastMonths = forecastMonths;
            //grossProfitForecastRow.ForecastActuals = GetForecastActuals(1, 1, 1, 1, month, 0, null).Result; //Temp
            grossProfitForecastRow.AccountLabel = "Gross Profit";
            grossProfitforecastRowGroup.Label = "Gross Profit";

            
            var budget = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 1).Select(fm => fm.ActualValue))).Sum();
            var budgetVariance = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 1).Select(fm => fm.VarianceValue))).Sum();

            var priorForecastValue = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 2).Select(fm => fm.ActualValue))).Sum();
            var priorForecastVarianceValue = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 2).Select(fm => fm.VarianceValue))).Sum();

            var lastYear = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 3).Select(fm => fm.ActualValue))).Sum();
            var lastYearVariance = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).SelectMany(fr => fr.ForecastActuals.Where(fm => fm.DisplayOrder == 3).Select(fm => fm.VarianceValue))).Sum();

            forecastActuals.Add(new ForecastActual() { DisplayOrder = 1, ActualLabel = "Budget", ActualValue = budget, VarianceLabel = "vs", VarianceValue = budgetVariance });
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 2, ActualLabel = "Prior Forecast", ActualValue = priorForecastValue, VarianceLabel = "vs", VarianceValue = priorForecastVarianceValue });
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 3, ActualLabel = "Last Year", ActualValue = lastYear, VarianceLabel = "vs", VarianceValue = lastYearVariance});

            grossProfitForecastRow.ForecastActuals = forecastActuals;


            grossProfitforecastRowGroup.ForecastRows.Add(grossProfitForecastRow);

            return grossProfitforecastRowGroup;

        }

        
        private async Task<List<ForecastActual>> GetForecastActuals(List<FinancialLinesVM> financialLinesVM, int accountId,int siteId, int departmentId, int forecastId, DateTime month, decimal total, IEnumerable<ForecastRowGroup> priorForecast, DealerGroupName dealerGroup)
        {
            //If Dept is 'New Fakenham' then use details of Site 'VW Fakenham'
            if (siteId == 9)
            {
                var financialLinesVWFakenham = (await liveForecastDataAccess.GetFinancialLines(8, 1, 1, month, dealerGroup));
                if (financialLinesVWFakenham.Any())
                {
                    financialLinesVWFakenham.First().SiteId = 9;
                    financialLinesVWFakenham.First().DepartmentId = 12;

                    int newRetailDeptIndex = financialLinesVM.FindIndex(d => d.DepartmentId == 12);
                    if (newRetailDeptIndex == -1)
                    {
                        financialLinesVM.Add(financialLinesVWFakenham.First());
                    }
                    else
                    {
                        financialLinesVM[newRetailDeptIndex] = financialLinesVWFakenham.First();
                    }
                }
            }
            else if (siteId == 8) //If Site is VW Fakenham then clear its 'New' department values
            {
                int newDepartmentIndex = financialLinesVM.FindIndex(d => d.DepartmentId == 1);
                if (newDepartmentIndex != -1) financialLinesVM[newDepartmentIndex].Value = 0;
            }


            var financialLines = financialLinesVM.Where(f => f.SiteId == siteId && f.DepartmentId == departmentId && f.AccountId == accountId).ToList();
            var forecastActuals = new List<ForecastActual>();
            var budget = GetBudget(financialLines);
            var priorForecastValue = CalculatePriorForecast(accountId, priorForecast);
            var lastYear = GetLastYear(financialLines);

            var budgetLabel = "Budget"; //financialLines.Where(f => f.IsBudget).First().Name;
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 1, ActualLabel = budgetLabel, ActualValue = budget, VarianceLabel = "vs", VarianceValue = total - budget });
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 2, ActualLabel = "Prior Forecast", ActualValue = priorForecastValue, VarianceLabel = "vs", VarianceValue = total - priorForecastValue });
            forecastActuals.Add(new ForecastActual() { DisplayOrder = 3, ActualLabel = "Last Year", ActualValue = lastYear, VarianceLabel = "vs", VarianceValue = total - lastYear });

            return forecastActuals;
        }

        private decimal CalculatePriorForecast(int accountId, IEnumerable<ForecastRowGroup> priorForecast)
        {
            try
            {
                if (priorForecast == null) return 0;

                var result = priorForecast.SelectMany(p => p.ForecastRows.Where(f => f.AccountId == accountId));
                if (result.Any())
                {
                    return result.First().DirectorTotal;
                }
                return 0;
            }
            catch
            {
                return  0;
            }
        
        }

        private decimal GetLastYear(List<FinancialLinesVM> financialLines)
        {
            var result = financialLines.Where(f => f.Name == "Last Year").FirstOrDefault();

            if (result == null)
            {
                return 0;
            }

            return result.Value;
        }

        private async Task<IEnumerable<ForecastRowGroup>> GetPriorForecast(int siteId, int departmentId, int forecastId, DateTime month, List<DateTime> siteNonWorkingDays, DealerGroupName dealerGroup)
        {

            int priorForecastId = await GetPriorForecastId(month, forecastId, dealerGroup);

            if (priorForecastId == 0) return null;
            
            //Get PriorForecastVersionId,this needs to the max Id, as we need to the latest one.
            //Get PriorForecast Id and Max ForecastVersionId
            var priorForecastVersionId = await liveForecastDataAccess.GetMaxForecastVersionId(priorForecastId, siteId, dealerGroup);

            if(priorForecastVersionId == 0) return null;

            //Get 
            var empty = new ForecastActuals();
            var result = await GetLiveForecastForExistingForecastVersion(siteId, departmentId, priorForecastId, priorForecastVersionId, 0, empty, empty, empty,null, month, siteNonWorkingDays, false, dealerGroup);
            return result.ForecastRowGroups;
            //return 55;
        }

        private async Task<int> GetPriorForecastId(DateTime month, int forecastId, DealerGroupName dealerGroup)
        {
            int priorForecastId = 0;
            var forecastsForMonth = await liveForecastDataAccess.GetForecastForMonth(month, dealerGroup);

            //Check if currentforecast is 1st of the month
            var forecastOrderWithinMonth = forecastsForMonth.Where(f => f.Id == forecastId).First().OrderWithinMonth;
            if (forecastOrderWithinMonth == 1)
            {
                return priorForecastId;
            }

            //Get PriorForecastId
            priorForecastId = forecastsForMonth.Where(f => f.OrderWithinMonth == forecastOrderWithinMonth - 1).First().Id;

            return priorForecastId;
        }

        private decimal GetBudget(IEnumerable<FinancialLinesVM> financialLines)
        {
            var result = financialLines.Where(f => f.IsBudget).FirstOrDefault();

            if (result == null)
            {
                return 0;
            }

            return result.Value;
        }

        private async Task<ForecastInputVM> GetLiveForecastForExistingForecastVersion(int siteId, int departmentId, int forecastId, int forecastVersionId, int userId, ForecastActuals forecastActualsMonth1Value, ForecastActuals forecastActualsMonth2Value, ForecastActuals forecastActualsMonth3Value, IEnumerable<ForecastRowGroup> priorForecast, DateTime month, List<DateTime> siteNonWorkingDays, bool IncludeSummaryData, DealerGroupName dealerGroup)
        {
            ForecastInputVM forecastInputVM = new ForecastInputVM();
            forecastInputVM.DepartmentId = departmentId;
            
            List<ForecastRowGroup> forecastRowGroups = new List<ForecastRowGroup>();


            var financialLinesResult = await liveForecastDataAccess.GetFinancialLines(null, null, null, month, dealerGroup);
            var financialLines = financialLinesResult.ToList();
            var forecastLinesAll = await liveForecastDataAccess.GetForecastLines(null, forecastVersionId, null, month,dealerGroup);
            var forecastLines = forecastLinesAll.Where(f => f.DepartmentId == departmentId).ToList();
            var groupAccounts = await liveForecastDataAccess.GetGroupAccounts(departmentId, dealerGroup);
            var uniqueGroupAccounts = forecastLines.Select(g => new { g.GroupId, g.GroupLabel }).Distinct();


            //If Dept is Parts(10), then use Hours from Service Dept(8)
            if (departmentId == 10)
            {
                var serviceDeptforecastLines = forecastLinesAll.Where(f => f.DepartmentId == 8);
                //removing its mapped Hours account data
                forecastLines = forecastLines.Where(f => f.AccountId != 20).ToList();

                var newHoursForecastLines = serviceDeptforecastLines.Where(f => f.AccountId == 20).ToList();
                foreach(var line in newHoursForecastLines)
                {
                    line.DepartmentId = departmentId;
                }

                forecastLines.InsertRange(0, newHoursForecastLines);
            }

            foreach (var groupAccount in uniqueGroupAccounts)
            {
                ForecastRowGroup forecastRowGroup = new ForecastRowGroup();
                forecastRowGroup.Label = groupAccount.GroupLabel;
                forecastRowGroup.ForecastRows = new List<ForecastRow>();

                var uniqueAccounts = forecastLines
                    .Where(f => f.GroupId == groupAccount.GroupId && f.DepartmentId == departmentId)
                    .Select(f => new { f.AccountId, f.AccountCode, f.AccountName })
                    .OrderBy(f => f.AccountCode)
                    .Distinct().ToList();

                foreach (var account in uniqueAccounts)
                {
                    var accounts = groupAccounts.Where(a => a.GroupId == groupAccount.GroupId).Select(a => new { a.AccountId, a.AccountName, a.IsStatistic, a.RowNumberFormat, a.Divisor, a.AccountCode }).OrderBy(o => o.AccountCode);

                    var ForecastMonths = GetForecastMonths(forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, month, GetEnumFromAccountName(account.AccountName));
                    var forecastRow = new ForecastRow();

                    forecastRow.AccountId = account.AccountId;
                    forecastRow.AccountLabel = account.AccountName;
                    forecastRow.RowNumberFormat = accounts.Where(a => a.AccountId == account.AccountId).First().RowNumberFormat;

                    decimal BroughtIn;
                    Decimal.TryParse(forecastLines.Where(f => f.GroupId == groupAccount.GroupId && f.AccountId == account.AccountId && f.DoneStatusTypeName == "BroughtIn").FirstOrDefault().Value.ToString(), out BroughtIn);
                    forecastRow.BroughtIn = BroughtIn;

                    decimal DoneInMonth;
                    Decimal.TryParse(forecastLines.Where(f => f.GroupId == groupAccount.GroupId && f.AccountId == account.AccountId && f.DoneStatusTypeName == "DoneInMonth").FirstOrDefault().Value.ToString(), out DoneInMonth);
                    forecastRow.DoneInMonth = DoneInMonth;

                    decimal SiteForecast;
                    Decimal.TryParse(forecastLines.Where(f => f.GroupId == groupAccount.GroupId && f.AccountId == account.AccountId && f.DoneStatusTypeName == "SiteForecast").FirstOrDefault().Value.ToString(), out SiteForecast);
                    forecastRow.SiteForecast = SiteForecast;

                    decimal DirectorAdj;
                    Decimal.TryParse(forecastLines.Where(f => f.GroupId == groupAccount.GroupId && f.AccountId == account.AccountId && f.DoneStatusTypeName == "DirectorForecast").FirstOrDefault().Value.ToString(), out DirectorAdj);
                    forecastRow.DirectorAdj = DirectorAdj;

                    
                    forecastRow.SiteTotal = BroughtIn + DoneInMonth + SiteForecast;
                    forecastRow.DirectorTotal = forecastRow.SiteTotal + forecastRow.DirectorAdj;

                    

                    forecastRow.ForecastMonths = ForecastMonths;


                    forecastRow.RunRate = CalculateRunRate(departmentId, siteNonWorkingDays, DoneInMonth, month);

                    forecastRow.ForecastActuals = await GetForecastActuals(financialLines, account.AccountId, siteId, departmentId, forecastId, month, forecastRow.DirectorTotal, priorForecast, dealerGroup);


                    forecastRowGroup.ForecastRows.Add(forecastRow);


                    //Check and add Per Unit row
                    var perAccount = accounts.Where(a => a.AccountId == account.AccountId).First();

                    //Check and add Per Unit row
                    if (perAccount.Divisor.HasValue)
                    {
                        var ForecastMonthsPerUnit = new List<ForecastMonth>();
                        var divisorAccount = groupAccounts.Where(a => a.AccountId == perAccount.Divisor.Value).First();

                        decimal BroughtInForDivisor = forecastLines.Where(f => f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "BroughtIn").FirstOrDefault().Value;
                        decimal DoneInMonthForDivisor = forecastLines.Where(f => f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "DoneInMonth").FirstOrDefault().Value;

                        decimal SiteForecastForDivisor = forecastLines.Where(f => f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "SiteForecast").FirstOrDefault().Value;
                        decimal DirectorAdjForDivisor = forecastLines.Where(f => f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "DirectorForecast").FirstOrDefault().Value;
                        decimal SiteTotalForDivisor = BroughtInForDivisor + DoneInMonthForDivisor + SiteForecastForDivisor;
                        decimal DirectorTotalForDivisor = SiteTotalForDivisor + DirectorAdjForDivisor;


                        //Check if its F&I (deptId: 7) For Account: New Finanace Income(8), New Add-on Income (10)& New Gap Income(11) it should get Units from "New Retail"(DeptId: 1)
                        int[] FINewAccountIds = { 8, 10, 11 };
                        //Check if its F&I (deptId: 7) For Account: Used Finanace Income(13), Used Add-on Income (15)& used Gap Income (16)it should get Units from "Used"(DeptId: 6)
                        int[] FIUsedAccountIds = { 13, 15, 16 };
                        var forecastActualsNewRetail = new ForecastActuals();
                        var forecastActualsUsed = new ForecastActuals();


                        if (FINewAccountIds.Contains(account.AccountId))
                        {
                            Decimal.TryParse(forecastLinesAll.Where(f => f.DepartmentId == 1 && f.AccountId == 1 && f.DoneStatusTypeName == "BroughtIn").FirstOrDefault().Value.ToString(), out BroughtInForDivisor);
                            Decimal.TryParse(forecastLinesAll.Where(f => f.DepartmentId == 1 && f.AccountId == 1 && f.DoneStatusTypeName == "DoneInMonth").FirstOrDefault().Value.ToString(), out DoneInMonthForDivisor);

                            
                            var forecastActualsMonth1ValueNewRetail = await liveForecastDataAccess.GetForecastActuals(1, siteId, month.AddMonths(-1), dealerGroup);
                            var forecastActualsMonth2ValueNewRetail = await liveForecastDataAccess.GetForecastActuals(1, siteId, month.AddMonths(-2), dealerGroup);
                            var forecastActualsMonth3ValueNewRetail = await liveForecastDataAccess.GetForecastActuals(1, siteId, month.AddMonths(-3), dealerGroup);

                            ForecastMonthsPerUnit = GetForecastMonthsPerUnitCustomUnits(forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, month, GetEnumFromAccountName(account.AccountName), forecastActualsMonth1ValueNewRetail, forecastActualsMonth2ValueNewRetail, forecastActualsMonth3ValueNewRetail);

                            //New Retail Department
                            BroughtInForDivisor = forecastLinesAll.Where(f => f.DepartmentId == 1 && f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "BroughtIn").FirstOrDefault().Value;
                            DoneInMonthForDivisor = forecastLinesAll.Where(f => f.DepartmentId == 1 && f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "DoneInMonth").FirstOrDefault().Value;

                            SiteForecastForDivisor = forecastLinesAll.Where(f => f.DepartmentId == 1 && f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "SiteForecast").FirstOrDefault().Value;
                            DirectorAdjForDivisor = forecastLinesAll.Where(f => f.DepartmentId == 1 && f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "DirectorForecast").FirstOrDefault().Value;
                            SiteTotalForDivisor = BroughtInForDivisor + DoneInMonthForDivisor + SiteForecastForDivisor;
                            DirectorTotalForDivisor = SiteTotalForDivisor + DirectorAdjForDivisor;
                        }
                        else if (FIUsedAccountIds.Contains(account.AccountId))
                        {
                            Decimal.TryParse(forecastLinesAll.Where(f => f.DepartmentId == 6 && f.AccountId == 1 && f.DoneStatusTypeName == "BroughtIn").FirstOrDefault().Value.ToString(), out BroughtInForDivisor);
                            Decimal.TryParse(forecastLinesAll.Where(f => f.DepartmentId == 6 && f.AccountId == 1 && f.DoneStatusTypeName == "DoneInMonth").FirstOrDefault().Value.ToString(), out DoneInMonthForDivisor);

                            var forecastActualsMonth1ValueUsed = await liveForecastDataAccess.GetForecastActuals(6, siteId, month.AddMonths(-1), dealerGroup);
                            var forecastActualsMonth2ValueUsed = await liveForecastDataAccess.GetForecastActuals(6, siteId, month.AddMonths(-2), dealerGroup);
                            var forecastActualsMonth3ValueUsed = await liveForecastDataAccess.GetForecastActuals(6, siteId, month.AddMonths(-3), dealerGroup);

                            ForecastMonthsPerUnit = GetForecastMonthsPerUnitCustomUnits(forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, month, GetEnumFromAccountName(account.AccountName), forecastActualsMonth1ValueUsed, forecastActualsMonth2ValueUsed, forecastActualsMonth3ValueUsed);

                            //Used Department
                            BroughtInForDivisor = forecastLinesAll.Where(f => f.DepartmentId == 6 && f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "BroughtIn").FirstOrDefault().Value;
                            DoneInMonthForDivisor = forecastLinesAll.Where(f => f.DepartmentId == 6 && f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "DoneInMonth").FirstOrDefault().Value;

                            SiteForecastForDivisor = forecastLinesAll.Where(f => f.DepartmentId == 6 && f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "SiteForecast").FirstOrDefault().Value;
                            DirectorAdjForDivisor = forecastLinesAll.Where(f => f.DepartmentId == 6 && f.AccountId == divisorAccount.AccountId && f.DoneStatusTypeName == "DirectorForecast").FirstOrDefault().Value;
                            SiteTotalForDivisor = BroughtInForDivisor + DoneInMonthForDivisor + SiteForecastForDivisor;
                            DirectorTotalForDivisor = SiteTotalForDivisor + DirectorAdjForDivisor;
                        }
                        else
                        {
                            ForecastMonthsPerUnit = GetForecastMonthsPerUnit(forecastActualsMonth1Value, forecastActualsMonth2Value, forecastActualsMonth3Value, month, GetEnumFromAccountName(account.AccountName));
                        }

                        var unitRow = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == perAccount.Divisor.Value)).FirstOrDefault();
                        
                        if (unitRow == null)
                        {
                            unitRow = forecastRowGroup.ForecastRows.Where(f => f.AccountId == perAccount.Divisor.Value).FirstOrDefault();
                        }

                        var forecastRowPerUnit = new ForecastRow()
                        {
                            IsPerUnit = true,
                            AccountId = perAccount.AccountId,
                            AccountLabel = GeneratePerAccountName(divisorAccount.AccountName),
                            RowNumberFormat = perAccount.RowNumberFormat,

                            ForecastMonths = ForecastMonthsPerUnit,

                            BroughtIn = HelperService.DivideByDecimal(forecastRow.BroughtIn, BroughtInForDivisor),
                            DoneInMonth = HelperService.DivideByDecimal(forecastRow.DoneInMonth, DoneInMonthForDivisor),
                            RunRate = HelperService.DivideByDecimal(forecastRow.DoneInMonth, DoneInMonthForDivisor),//same as DoneInMonth
                            SiteForecast = HelperService.DivideByDecimal(forecastRow.SiteForecast, SiteForecastForDivisor), // input
                            //DirectorAdj = HelperService.DivideByDecimal(forecastRow.DirectorAdj, DirectorAdjForDivisor), // input //NO LONGER REQUIRED
                            SiteTotal = HelperService.DivideByDecimal(forecastRow.SiteTotal, SiteTotalForDivisor),
                            DirectorTotal = HelperService.DivideByDecimal(forecastRow.DirectorTotal, DirectorTotalForDivisor),
                            ForecastActuals = new List<ForecastActual>() 
                        };
                        forecastRowPerUnit.ForecastActuals = CalculateForecastActualsPerUnit(forecastRow, unitRow, forecastRowPerUnit.DirectorTotal);


                        //if its Labour Profit, then calculate percentage
                        if (perAccount.Divisor.Value == 21)
                        {
                            forecastRowPerUnit.RowNumberFormat = "percent";
                        }

                        forecastRowGroup.ForecastRows.Add(forecastRowPerUnit);
                    }

                }
                forecastRowGroups.Add(forecastRowGroup);


            }

            GenerateGrossNetProfitExpensesRowGroups(null, groupAccounts, forecastRowGroups, month);


            forecastInputVM.ForecastRowGroups = forecastRowGroups;
            if (IncludeSummaryData)
            {
                var departmentName = await liveForecastDataAccess.GetDepartmentName(departmentId, dealerGroup);
                
                var dealershipSummary = await GetDealershipSummary(month, forecastId, siteId, dealerGroup);
                var departmentSummary = GetDepartmentSummary(forecastRowGroups, departmentName);

                forecastInputVM.DealershipSummaryTable = dealershipSummary;
                forecastInputVM.DepartmentSummaryTable = departmentSummary;
            }

            
            return CleanUp(forecastInputVM);
        }

        private ForecastInputVM CleanUp(ForecastInputVM forecastInputVM)
        {
            //Remove Units from F&I Department
            if (forecastInputVM.DepartmentId == 7)
            {
                forecastInputVM.ForecastRowGroups[0].IsHidden = true;
            }


            //Remove all pre Site Total cols from SMART
            if (forecastInputVM.DepartmentId == 9)
            {
                foreach(var group in forecastInputVM.ForecastRowGroups)
                {
                    foreach(var row in group.ForecastRows)
                    {
                        row.ForecastMonths = new List<ForecastMonth>();
                    }
                }

            }

            //Parts - Hide Labour group from top
            if (forecastInputVM.DepartmentId == 10)
            {
                //forecastInputVM.ForecastRowGroups.RemoveAt(0);
                forecastInputVM.ForecastRowGroups[0].IsHidden = true;

            }

            //Add Total Units rows
            if (forecastInputVM.DepartmentId == 1 || forecastInputVM.DepartmentId == 12)
            {
                AddTotalUnitsRowToDepartment(forecastInputVM);
            }

            //Add Total Units rows
            if (forecastInputVM.DepartmentId == 6)
            {
                AddTotalUnitsRowToUsedDepartment(forecastInputVM);
            }

            return forecastInputVM;
        }

        private void AddTotalUnitsRowToDepartment(ForecastInputVM forecastInputVM)
        {

            var retailUnitsRow = forecastInputVM.ForecastRowGroups[0].ForecastRows.Where(f => f.AccountId == 1).ToList(); //Retail Units
            var motabilityUnitsRow = forecastInputVM.ForecastRowGroups[0].ForecastRows.Where(f => f.AccountId == 33).ToList(); //Motability Units

            if (retailUnitsRow.Count == 0)
            {
                return;
            }

            var month1 = new ForecastMonth()
            {
                DisplayOrder = 1,
                Month = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 1).First().Month,
                Value = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 1).First().Value + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 1).First().Value : 0)
            };
            var month2 = new ForecastMonth()
            {
                DisplayOrder = 2,
                Month = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 2).First().Month,
                Value = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 2).First().Value + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 2).First().Value : 0)
            };
            var month3 = new ForecastMonth()
            {
                DisplayOrder = 3,
                Month = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 3).First().Month,
                Value = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 3).First().Value + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 3).First().Value : 0)
            };
            var actual1 = new ForecastActual()
            {
                DisplayOrder = 1,
                ActualLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 1).First().ActualLabel,
                ActualValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 1).First().ActualValue + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 1).First().ActualValue : 0),
                VarianceLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 1).First().VarianceLabel,
                VarianceValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 1).First().VarianceValue + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 1).First().VarianceValue : 0)
            };

            var actual2 = new ForecastActual()
            {
                DisplayOrder = 2,
                ActualLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 2).First().ActualLabel,
                ActualValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 2).First().ActualValue + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 2).First().ActualValue : 0),
                VarianceLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 2).First().VarianceLabel,
                VarianceValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 2).First().VarianceValue + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder ==2).First().VarianceValue : 0)
            };
            var actual3 = new ForecastActual()
            {
                DisplayOrder = 3,
                ActualLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 3).First().ActualLabel,
                ActualValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 3).First().ActualValue + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 3).First().ActualValue : 0),
                VarianceLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 3).First().VarianceLabel,
                VarianceValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 3).First().VarianceValue + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder ==3).First().VarianceValue : 0)
            };




            var totalUnitsRow = new ForecastRow()
            {
                AccountId = 0,
                AccountLabel = "Total Units",
                BroughtIn = retailUnitsRow[0].BroughtIn + (motabilityUnitsRow.Any() ?  motabilityUnitsRow[0].BroughtIn: 0),
                DirectorAdj = retailUnitsRow[0].DirectorAdj + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].DirectorAdj : 0),
                DirectorTotal = retailUnitsRow[0].DirectorTotal + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].DirectorTotal : 0),
                DoneInMonth = retailUnitsRow[0].DoneInMonth + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].DoneInMonth : 0),
                ForecastActuals = new List<ForecastActual>() { actual1, actual2, actual3},
                ForecastMonths = new List<ForecastMonth>() { month1, month2, month3 },
                IsPerUnit = retailUnitsRow[0].IsPerUnit,
                RowNumberFormat = retailUnitsRow[0].RowNumberFormat,
                RunRate = Math.Round(retailUnitsRow[0].RunRate, 0, MidpointRounding.AwayFromZero) + (motabilityUnitsRow.Any() ? Math.Round(motabilityUnitsRow[0].RunRate, 0, MidpointRounding.AwayFromZero) : 0), //calculate
                SiteForecast = retailUnitsRow[0].SiteForecast + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].SiteForecast : 0),
                SiteTotal = retailUnitsRow[0].SiteTotal + (motabilityUnitsRow.Any() ? motabilityUnitsRow[0].SiteTotal : 0),
            };

            var existingTotalUnitsRow = forecastInputVM.ForecastRowGroups[0].ForecastRows.Where(f => f.AccountLabel == "Total Units");
            if (existingTotalUnitsRow.Any())
            {
                forecastInputVM.ForecastRowGroups[0].ForecastRows.Remove(existingTotalUnitsRow.First());
            }
            
            forecastInputVM.ForecastRowGroups[0].ForecastRows.Add(totalUnitsRow);
        }

        private void AddTotalUnitsRowToUsedDepartment(ForecastInputVM forecastInputVM)
        {

            var retailUnitsRow = forecastInputVM.ForecastRowGroups[0].ForecastRows.Where(f => f.AccountId == 1).ToList(); //Retail Units
            var demoUnitsRow = forecastInputVM.ForecastRowGroups[0].ForecastRows.Where(f => f.AccountId == 34).ToList(); //Motability Units
            var month1 = new ForecastMonth()
            {
                DisplayOrder = 1,
                Month = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 1).First().Month,
                Value = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 1).First().Value + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 1).First().Value : 0)
            };
            var month2 = new ForecastMonth()
            {
                DisplayOrder = 2,
                Month = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 2).First().Month,
                Value = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 2).First().Value + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 2).First().Value : 0)
            };
            var month3 = new ForecastMonth()
            {
                DisplayOrder = 3,
                Month = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 3).First().Month,
                Value = retailUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 3).First().Value + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastMonths.Where(fm => fm.DisplayOrder == 3).First().Value : 0)
            };
            var actual1 = new ForecastActual()
            {
                DisplayOrder = 1,
                ActualLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 1).First().ActualLabel,
                ActualValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 1).First().ActualValue + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 1).First().ActualValue : 0),
                VarianceLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 1).First().VarianceLabel,
                VarianceValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 1).First().VarianceValue + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 1).First().VarianceValue : 0)
            };

            var actual2 = new ForecastActual()
            {
                DisplayOrder = 2,
                ActualLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 2).First().ActualLabel,
                ActualValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 2).First().ActualValue + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 2).First().ActualValue : 0),
                VarianceLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 2).First().VarianceLabel,
                VarianceValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 2).First().VarianceValue + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 2).First().VarianceValue : 0)
            };
            var actual3 = new ForecastActual()
            {
                DisplayOrder = 3,
                ActualLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 3).First().ActualLabel,
                ActualValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 3).First().ActualValue + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 3).First().ActualValue : 0),
                VarianceLabel = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 3).First().VarianceLabel,
                VarianceValue = retailUnitsRow[0].ForecastActuals.Where(fa => fa.DisplayOrder == 3).First().VarianceValue + (demoUnitsRow.Any() ? demoUnitsRow[0].ForecastActuals.Where(fm => fm.DisplayOrder == 3).First().VarianceValue : 0)
            };




            var totalUnitsRow = new ForecastRow()
            {
                AccountId = 0,
                AccountLabel = "Total Units",
                BroughtIn = retailUnitsRow[0].BroughtIn + (demoUnitsRow.Any() ? demoUnitsRow[0].BroughtIn : 0),
                DirectorAdj = retailUnitsRow[0].DirectorAdj + (demoUnitsRow.Any() ? demoUnitsRow[0].DirectorAdj : 0),
                DirectorTotal = retailUnitsRow[0].DirectorTotal + (demoUnitsRow.Any() ? demoUnitsRow[0].DirectorTotal : 0),
                DoneInMonth = retailUnitsRow[0].DoneInMonth + (demoUnitsRow.Any() ? demoUnitsRow[0].DoneInMonth : 0),
                ForecastActuals = new List<ForecastActual>() { actual1, actual2, actual3 },
                ForecastMonths = new List<ForecastMonth>() { month1, month2, month3 },
                IsPerUnit = retailUnitsRow[0].IsPerUnit,
                RowNumberFormat = retailUnitsRow[0].RowNumberFormat,
                RunRate = HelperService.DivideByDecimal(retailUnitsRow[0].RunRate + (demoUnitsRow.Any() ? demoUnitsRow[0].RunRate : 0), 2), //calculate
                SiteForecast = retailUnitsRow[0].SiteForecast + (demoUnitsRow.Any() ? demoUnitsRow[0].SiteForecast : 0),
                SiteTotal = retailUnitsRow[0].SiteTotal + (demoUnitsRow.Any() ? demoUnitsRow[0].SiteTotal : 0),
            };

            var existingTotalUnitsRow = forecastInputVM.ForecastRowGroups[0].ForecastRows.Where(f => f.AccountLabel == "Total Units");
            if (existingTotalUnitsRow.Any())
            {
                forecastInputVM.ForecastRowGroups[0].ForecastRows.Remove(existingTotalUnitsRow.First());
            }

            if (forecastInputVM.ForecastRowGroups[0].ForecastRows.Where(f => f.AccountId == 35).Any())
            {
                forecastInputVM.ForecastRowGroups[0].ForecastRows.Insert(forecastInputVM.ForecastRowGroups[0].ForecastRows.Count -1, totalUnitsRow);
            }
            else
            {
                forecastInputVM.ForecastRowGroups[0].ForecastRows.Add(totalUnitsRow);
            }
            
        }

        private string GeneratePerAccountName(string accountName)
        {
            var account = GetEnumFromAccountName(accountName);

            switch (account)
            {
                case AccountNames.RetailUnits:
                    return "Per Unit";
                case AccountNames.HoursSold:
                    return "Per Hour";
                case AccountNames.LabourSales:
                    return "Labour Margin";
                default:
                    return accountName;
            }


        }

        private void GenerateGrossNetProfitExpensesRowGroups(InputSummaryTable departmentSummaryTable, IEnumerable<AccountGroup> groupAccounts, List<ForecastRowGroup> forecastRowGroups, DateTime month)
        {
            //Add Gross Profit
            var grossProfitforecastRowGroup = GenerateGrossProfitRowGroup(groupAccounts, forecastRowGroups, month);


            //Expenses
            var expensesAccountIds = groupAccounts.Where(ga => ga.IsInGrossProfit == false && ga.IsStatistic == false).Select(ga => ga.AccountId).ToArray();
            var expenseSiteForecast = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => expensesAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.SiteForecast)).Sum();
            var expenseDirectorAdj = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => expensesAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorAdj)).Sum();
            var expenseSiteTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => expensesAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.SiteTotal)).Sum();
            var expenseDirectorTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => expensesAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorTotal)).Sum();

            //Add Net Profit
            var netProfitforecastRowGroup = GenerateNetProfitRowGroup(groupAccounts, forecastRowGroups, expenseSiteForecast, expenseDirectorAdj, expenseSiteTotal, expenseDirectorTotal, month);

            var count = forecastRowGroups.Count;
            if (count < 2)
            {
                count = 2;
            }

            forecastRowGroups.Insert(count - 2, grossProfitforecastRowGroup);
            forecastRowGroups.Add(netProfitforecastRowGroup);


            if (departmentSummaryTable != null)
            {
                var deptRowGrossProfit = new InputSummaryTableRow();
                deptRowGrossProfit.Id = 0;
                deptRowGrossProfit.Label = grossProfitforecastRowGroup.Label;
                deptRowGrossProfit.Budget = grossProfitforecastRowGroup.ForecastRows.First().ForecastActuals.Where(f => f.ActualLabel == "Budget").First().ActualValue;
                deptRowGrossProfit.Forecast = grossProfitforecastRowGroup.ForecastRows.First().DirectorTotal;
                deptRowGrossProfit.RowNumberFormart = "currency";

                var deptRowNetProfit = new InputSummaryTableRow();
                deptRowNetProfit.Id = 0;
                deptRowNetProfit.Label = netProfitforecastRowGroup.Label;
                deptRowNetProfit.Budget = netProfitforecastRowGroup.ForecastRows.First().ForecastActuals.Where(f => f.ActualLabel == "Budget").First().ActualValue;
                deptRowNetProfit.Forecast = netProfitforecastRowGroup.ForecastRows.First().DirectorTotal;
                deptRowNetProfit.RowNumberFormart = "currency";

                departmentSummaryTable.rows.Insert(departmentSummaryTable.rows.Count - 2, deptRowGrossProfit);
                departmentSummaryTable.rows.Add(deptRowNetProfit);
            }

        }

        public async Task<IEnumerable<ForecastWithHasForecastVersion>> GetForecasts(DealerGroupName dealerGroup)
        {
            return await liveForecastDataAccess.GetForecasts(dealerGroup);
        }

        private AccountNames GetEnumFromAccountName(string accountName)
        {
            switch (accountName)
            {
                case "Retail Units":
                    return AccountNames.RetailUnits;
                case "Metal Profit":
                    return AccountNames.MetalProfit;
                case "Paint Protection Income":
                    return AccountNames.PaintProtectionIncome;
                case "Dealer Fit Accessory GP":
                    return AccountNames.DealerFitAccessoryGP;
                case "Delivery GP":
                    return AccountNames.DeliveryGP;
                case "Bonus Income":
                    return AccountNames.BonusIncome;
                case "Bonus Income 2":
                    return AccountNames.BonusIncome2;
                case "New Finance Income":
                    return AccountNames.NewFinanceIncome;
                case "New VB":
                    return AccountNames.NewVB;
                case "New Add-on Income (excluding GAP)":
                    return AccountNames.NewAddonIncomeexcludingGAP;
                case "New GAP Income":
                    return AccountNames.NewGAPIncome;
                case "New Clawbacks":
                    return AccountNames.NewClawbacks;
                case "Used Finance Income":
                    return AccountNames.UsedFinanceIncome;
                case "Used VB":
                    return AccountNames.UsedVB;
                case "Used Add-on Income (excluding GAP)":
                    return AccountNames.UsedAddonIncomeexcludingGAP;
                case "Used GAP Income":
                    return AccountNames.UsedGAPIncome;
                case "Used Clawbacks":
                    return AccountNames.UsedClawbacks;
                case "Hours Attended":
                    return AccountNames.HoursAttended;
                case "Hours Productive":
                    return AccountNames.HoursAttended;
                case "Hours Sold":
                    return AccountNames.HoursSold;
                case "Labour Sales":
                    return AccountNames.LabourSales;
                case "Labour Profit":
                    return AccountNames.LabourProfit;
                case "Non-Labour Sales":
                    return AccountNames.NonLabourSales;
                case "Non-Labour Profit":
                    return AccountNames.NonLabourProfit;
                case "Workshop Sales":
                    return AccountNames.WorkshopSales;
                case "Counter / Trade Sales":
                    return AccountNames.CounterTradeSales;
                case "Parts Profit":
                    return AccountNames.PartsProfit;
                case "Other Income":
                    return AccountNames.OtherIncome;
                case "Employment Expenses":
                    return AccountNames.EmploymentExpenses;
                case "Operating Expenses":
                    return AccountNames.OperatingExpenses;
                case "Transfer Gain/Loss":
                    return AccountNames.TransferGainLoss;
                case "Admin Fees":
                    return AccountNames.AdminFees;
                case "CBS / Motability Units":
                    return AccountNames.MotabilityUnits;
                case "Demo Units":
                    return AccountNames.DemoUnits;
                case "Trade Units":
                    return AccountNames.TradeUnits;
                case "Metal Profit (Demo)":
                    return AccountNames.MetalProfitDemo;
                case "Trade Profit/Loss":
                    return AccountNames.TradeProfitLoss;
                case "Month End Provision & Fines":
                    return AccountNames.MonthEndProvisionFines;
                default:
                    throw new Exception(accountName + ": Account Name Not Found");
            }

        }

        private List<ForecastMonth> GetForecastMonthsPerUnitCustomUnits(ForecastActuals forecastActualsMonth1Value, ForecastActuals forecastActualsMonth2Value, ForecastActuals forecastActualsMonth3Value, DateTime month, AccountNames accountName, ForecastActuals unitForecastActualsMonth1Value, ForecastActuals unitForecastActualsMonth2Value, ForecastActuals unitForecastActualsMonth3Value)
        {
            List<ForecastMonth> forecastMonths = new List<ForecastMonth>();

            switch (accountName)
            {
                case AccountNames.NewFinanceIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInNewFinanceIncome + forecastActualsMonth1Value.MonthNewFinanceIncome, unitForecastActualsMonth1Value.MonthUnits + unitForecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInNewFinanceIncome + forecastActualsMonth2Value.MonthNewFinanceIncome, unitForecastActualsMonth2Value.MonthUnits + unitForecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInNewFinanceIncome + forecastActualsMonth3Value.MonthNewFinanceIncome, unitForecastActualsMonth3Value.MonthUnits + unitForecastActualsMonth3Value.BroughtInUnits) });
                    break;
             
                case AccountNames.NewAddonIncomeexcludingGAP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInNewAddonIncome + forecastActualsMonth1Value.MonthNewAddonIncome, unitForecastActualsMonth1Value.MonthUnits + unitForecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInNewAddonIncome + forecastActualsMonth2Value.MonthNewAddonIncome, unitForecastActualsMonth2Value.MonthUnits + unitForecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInNewAddonIncome + forecastActualsMonth3Value.MonthNewAddonIncome, unitForecastActualsMonth3Value.MonthUnits + unitForecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.NewGAPIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInNewGAPIncome + forecastActualsMonth1Value.MonthNewGAPIncome, unitForecastActualsMonth1Value.MonthUnits + unitForecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInNewGAPIncome + forecastActualsMonth2Value.MonthNewGAPIncome, unitForecastActualsMonth2Value.MonthUnits + unitForecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInNewGAPIncome + forecastActualsMonth3Value.MonthNewGAPIncome, unitForecastActualsMonth3Value.MonthUnits + unitForecastActualsMonth3Value.BroughtInUnits) });
                    break;

                case AccountNames.UsedFinanceIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInUsedFinanceIncome + forecastActualsMonth1Value.MonthUsedFinanceIncome, unitForecastActualsMonth1Value.MonthUnits + unitForecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInUsedFinanceIncome + forecastActualsMonth2Value.MonthUsedFinanceIncome, unitForecastActualsMonth2Value.MonthUnits + unitForecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInUsedFinanceIncome + forecastActualsMonth3Value.MonthUsedFinanceIncome, unitForecastActualsMonth3Value.MonthUnits + unitForecastActualsMonth3Value.BroughtInUnits) });
                    break;

                case AccountNames.UsedAddonIncomeexcludingGAP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInUsedAddonIncome + forecastActualsMonth1Value.MonthUsedAddonIncome, unitForecastActualsMonth1Value.MonthUnits + unitForecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInUsedAddonIncome + forecastActualsMonth2Value.MonthUsedAddonIncome, unitForecastActualsMonth2Value.MonthUnits + unitForecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInUsedAddonIncome + forecastActualsMonth3Value.MonthUsedAddonIncome, unitForecastActualsMonth3Value.MonthUnits + unitForecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.UsedGAPIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInUsedGAPIncome + forecastActualsMonth1Value.MonthUsedGAPIncome, unitForecastActualsMonth1Value.MonthUnits + unitForecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInUsedGAPIncome + forecastActualsMonth2Value.MonthUsedGAPIncome, unitForecastActualsMonth2Value.MonthUnits + unitForecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInUsedGAPIncome + forecastActualsMonth3Value.MonthUsedGAPIncome, unitForecastActualsMonth3Value.MonthUnits + unitForecastActualsMonth3Value.BroughtInUnits) });
                    break;
                default:
                    throw new Exception(accountName + ": Account Name Not Found");

            }

            forecastMonths = forecastMonths.OrderBy(f => f.DisplayOrder).ToList();
            return forecastMonths;
        }

        private List<ForecastMonth> GetForecastMonthsPerUnit(ForecastActuals forecastActualsMonth1Value, ForecastActuals forecastActualsMonth2Value, ForecastActuals forecastActualsMonth3Value, DateTime month, AccountNames accountName)
        {
            List<ForecastMonth> forecastMonths = new List<ForecastMonth>();

            switch (accountName)
            {
                case AccountNames.RetailUnits:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInUnits + forecastActualsMonth1Value.MonthUnits, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInUnits + forecastActualsMonth2Value.MonthUnits, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInUnits + forecastActualsMonth3Value.MonthUnits, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.MotabilityUnits:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInMotabilityUnits + forecastActualsMonth1Value.MonthMotabilityUnits, forecastActualsMonth1Value.MonthMotabilityUnits + forecastActualsMonth1Value.BroughtInMotabilityUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInMotabilityUnits + forecastActualsMonth2Value.MonthMotabilityUnits, forecastActualsMonth2Value.MonthMotabilityUnits + forecastActualsMonth2Value.BroughtInMotabilityUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInMotabilityUnits + forecastActualsMonth3Value.MonthMotabilityUnits, forecastActualsMonth3Value.MonthMotabilityUnits + forecastActualsMonth3Value.BroughtInMotabilityUnits) });
                    break;
                case AccountNames.MetalProfit:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInMetalProfit + forecastActualsMonth1Value.MonthMetalProfit, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInMetalProfit + forecastActualsMonth2Value.MonthMetalProfit, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInMetalProfit + forecastActualsMonth3Value.MonthMetalProfit, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.MetalProfitDemo:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInMetalProfitDemo + forecastActualsMonth1Value.MonthMetalProfitDemo, forecastActualsMonth1Value.MonthDemoUnits + forecastActualsMonth1Value.BroughtInDemoUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInMetalProfitDemo + forecastActualsMonth2Value.MonthMetalProfitDemo, forecastActualsMonth2Value.MonthDemoUnits + forecastActualsMonth2Value.BroughtInDemoUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInMetalProfitDemo + forecastActualsMonth3Value.MonthMetalProfitDemo, forecastActualsMonth3Value.MonthDemoUnits + forecastActualsMonth3Value.BroughtInDemoUnits) });
                    break;
                case AccountNames.PaintProtectionIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInPaintProtectionIncome + forecastActualsMonth1Value.MonthPaintProtectionIncome, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInPaintProtectionIncome + forecastActualsMonth2Value.MonthPaintProtectionIncome, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInPaintProtectionIncome + forecastActualsMonth3Value.MonthPaintProtectionIncome, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.DealerFitAccessoryGP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInDealerFitAccessoryGP + forecastActualsMonth1Value.MonthDealerFitAccessoryGP, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInDealerFitAccessoryGP + forecastActualsMonth2Value.MonthDealerFitAccessoryGP, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInDealerFitAccessoryGP + forecastActualsMonth3Value.MonthDealerFitAccessoryGP, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break; 
                case AccountNames.DeliveryGP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInDeliveryGP + forecastActualsMonth1Value.MonthDeliveryGP, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInDeliveryGP + forecastActualsMonth2Value.MonthDeliveryGP, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInDeliveryGP + forecastActualsMonth3Value.MonthDeliveryGP, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.AdminFees:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInAdminFees + forecastActualsMonth1Value.MonthAdminFees, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInAdminFees + forecastActualsMonth2Value.MonthAdminFees, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInAdminFees + forecastActualsMonth3Value.MonthAdminFees, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;

                case AccountNames.BonusIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInBonusIncome + forecastActualsMonth1Value.MonthBonusIncome, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInBonusIncome + forecastActualsMonth2Value.MonthBonusIncome, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInBonusIncome + forecastActualsMonth3Value.MonthBonusIncome, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.BonusIncome2:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.NewFinanceIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInNewFinanceIncome + forecastActualsMonth1Value.MonthNewFinanceIncome, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInNewFinanceIncome + forecastActualsMonth2Value.MonthNewFinanceIncome, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInNewFinanceIncome + forecastActualsMonth3Value.MonthNewFinanceIncome, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.NewVB:
                    AddEmptyForecastMonths(forecastMonths, month); 
                    break;
                case AccountNames.NewAddonIncomeexcludingGAP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInNewAddonIncome + forecastActualsMonth1Value.MonthNewAddonIncome, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInNewAddonIncome + forecastActualsMonth2Value.MonthNewAddonIncome, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInNewAddonIncome + forecastActualsMonth3Value.MonthNewAddonIncome, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.NewGAPIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInNewGAPIncome + forecastActualsMonth1Value.MonthNewGAPIncome, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInNewGAPIncome + forecastActualsMonth2Value.MonthNewGAPIncome, forecastActualsMonth2Value.MonthUnits +forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInNewGAPIncome + forecastActualsMonth3Value.MonthNewGAPIncome, forecastActualsMonth3Value.MonthUnits +forecastActualsMonth1Value.BroughtInUnits) });
                    break;
                case AccountNames.NewClawbacks:
                    AddEmptyForecastMonths(forecastMonths, month); 
                    break;
                case AccountNames.UsedFinanceIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInUsedFinanceIncome+ forecastActualsMonth1Value.MonthUsedFinanceIncome, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInUsedFinanceIncome + forecastActualsMonth2Value.MonthUsedFinanceIncome, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInUsedFinanceIncome + forecastActualsMonth3Value.MonthUsedFinanceIncome, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    break;
                case AccountNames.UsedVB:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.UsedAddonIncomeexcludingGAP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInUsedAddonIncome + forecastActualsMonth1Value.MonthUsedAddonIncome, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInUsedAddonIncome + forecastActualsMonth2Value.MonthUsedAddonIncome, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInUsedAddonIncome + forecastActualsMonth3Value.MonthUsedAddonIncome, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    break;
                case AccountNames.UsedGAPIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInUsedGAPIncome + forecastActualsMonth1Value.MonthUsedGAPIncome, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInUsedGAPIncome + forecastActualsMonth2Value.MonthUsedGAPIncome, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInUsedGAPIncome + forecastActualsMonth3Value.MonthUsedGAPIncome, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    break;
                case AccountNames.UsedClawbacks:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.HoursAttended:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.HoursProductive:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.HoursSold:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInHoursSold + forecastActualsMonth1Value.MonthHoursSold, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInHoursSold + forecastActualsMonth2Value.MonthHoursSold, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInHoursSold + forecastActualsMonth3Value.MonthHoursSold, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.LabourSales:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInLabourSales + forecastActualsMonth1Value.MonthLabourSales, forecastActualsMonth1Value.MonthHoursSold + forecastActualsMonth1Value.BroughtInHoursSold) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInLabourSales + forecastActualsMonth2Value.MonthLabourSales, forecastActualsMonth2Value.MonthHoursSold + forecastActualsMonth2Value.BroughtInHoursSold) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInLabourSales + forecastActualsMonth3Value.MonthLabourSales, forecastActualsMonth3Value.MonthHoursSold + forecastActualsMonth3Value.BroughtInHoursSold) });
                    break;
                case AccountNames.LabourProfit:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.NonLabourSales:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInNonLabourSales + forecastActualsMonth1Value.MonthNonLabourSales, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInNonLabourSales + forecastActualsMonth2Value.MonthNonLabourSales, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInNonLabourSales + forecastActualsMonth3Value.MonthNonLabourSales, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.NonLabourProfit:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInNonLabourProfit + forecastActualsMonth1Value.MonthNonLabourProfit, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInNonLabourProfit + forecastActualsMonth2Value.MonthNonLabourProfit, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInNonLabourProfit + forecastActualsMonth3Value.MonthNonLabourProfit, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });
                    break;
                case AccountNames.WorkshopSales:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInWorkshopSales + forecastActualsMonth1Value.MonthWorkshopSales, forecastActualsMonth1Value.MonthHoursSold + forecastActualsMonth1Value.BroughtInHoursSold) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInWorkshopSales + forecastActualsMonth2Value.MonthWorkshopSales, forecastActualsMonth2Value.MonthHoursSold + forecastActualsMonth2Value.BroughtInHoursSold) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInWorkshopSales + forecastActualsMonth3Value.MonthWorkshopSales, forecastActualsMonth3Value.MonthHoursSold + forecastActualsMonth3Value.BroughtInHoursSold) });

                    break;
                case AccountNames.CounterTradeSales:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInCounterTradeSales + forecastActualsMonth1Value.MonthCounterTradeSales, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInCounterTradeSales + forecastActualsMonth2Value.MonthCounterTradeSales, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInCounterTradeSales + forecastActualsMonth3Value.MonthCounterTradeSales, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });

                    break;
                case AccountNames.PartsProfit:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInPartsProfit + forecastActualsMonth1Value.MonthPartsProfit, forecastActualsMonth1Value.MonthUnits + forecastActualsMonth1Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInPartsProfit + forecastActualsMonth2Value.MonthPartsProfit, forecastActualsMonth2Value.MonthUnits + forecastActualsMonth2Value.BroughtInUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInPartsProfit + forecastActualsMonth3Value.MonthPartsProfit, forecastActualsMonth3Value.MonthUnits + forecastActualsMonth3Value.BroughtInUnits) });

                    break;
                case AccountNames.OtherIncome:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.EmploymentExpenses:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.OperatingExpenses:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.DemoUnits:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = HelperService.DivideByDecimal(forecastActualsMonth1Value.BroughtInDemoUnits + forecastActualsMonth1Value.MonthDemoUnits, forecastActualsMonth1Value.MonthDemoUnits + forecastActualsMonth1Value.BroughtInDemoUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = HelperService.DivideByDecimal(forecastActualsMonth2Value.BroughtInDemoUnits + forecastActualsMonth2Value.MonthDemoUnits, forecastActualsMonth2Value.MonthDemoUnits + forecastActualsMonth2Value.BroughtInDemoUnits) });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = HelperService.DivideByDecimal(forecastActualsMonth3Value.BroughtInDemoUnits + forecastActualsMonth3Value.MonthDemoUnits, forecastActualsMonth3Value.MonthDemoUnits + forecastActualsMonth3Value.BroughtInDemoUnits) });
                    break;
                default:
                    break;
            }

            forecastMonths = forecastMonths.OrderBy(f => f.DisplayOrder).ToList();
            return forecastMonths;
        }

        private decimal GetDoneInMonth(ForecastActuals forecastActuals, AccountNames accountName)
        {
            if (forecastActuals == null)
            {
                return 0;
            }
            switch (accountName)
            {
                case AccountNames.RetailUnits:
                    return forecastActuals.MonthUnits;
                case AccountNames.MetalProfit:
                    return forecastActuals.MonthMetalProfit;
                case AccountNames.MetalProfitDemo:
                    return forecastActuals.MonthMetalProfitDemo;
                case AccountNames.PaintProtectionIncome:
                    return forecastActuals.MonthPaintProtectionIncome;
                case AccountNames.DealerFitAccessoryGP:
                    return forecastActuals.MonthDealerFitAccessoryGP;
                case AccountNames.DeliveryGP:
                    return forecastActuals.MonthDeliveryGP;
                case AccountNames.BonusIncome:
                    return forecastActuals.MonthBonusIncome;
                case AccountNames.BonusIncome2:
                    return 0;
                case AccountNames.NewFinanceIncome:
                    return forecastActuals.MonthNewFinanceIncome;
                case AccountNames.NewVB:
                    return 0;
                case AccountNames.NewAddonIncomeexcludingGAP:
                    return forecastActuals.MonthNewAddonIncome;
                case AccountNames.NewGAPIncome:
                    return forecastActuals.MonthNewGAPIncome;
                case AccountNames.NewClawbacks:
                    return 0;
                case AccountNames.UsedFinanceIncome:
                    return forecastActuals.MonthUsedFinanceIncome;
                case AccountNames.UsedVB:
                    return 0;
                case AccountNames.UsedAddonIncomeexcludingGAP:
                    return forecastActuals.MonthUsedAddonIncome;
                case AccountNames.UsedGAPIncome:
                    return forecastActuals.MonthUsedGAPIncome;
                case AccountNames.UsedClawbacks:
                    return 0;
                case AccountNames.HoursAttended:
                    return 0;
                case AccountNames.HoursProductive:
                    return 0;
                case AccountNames.HoursSold:
                    return forecastActuals.MonthHoursSold;//FL
                case AccountNames.LabourSales:
                    return forecastActuals.MonthLabourSales;
                case AccountNames.LabourProfit:
                    return 0;
                case AccountNames.NonLabourSales:
                    return forecastActuals.MonthNonLabourSales;
                case AccountNames.NonLabourProfit:
                    return forecastActuals.MonthNonLabourProfit;
                case AccountNames.WorkshopSales:
                    return forecastActuals.MonthWorkshopSales;
                case AccountNames.CounterTradeSales:
                    return forecastActuals.MonthCounterTradeSales;
                case AccountNames.PartsProfit:
                    return forecastActuals.MonthPartsProfit;
                case AccountNames.OtherIncome:
                    return 0;
                case AccountNames.EmploymentExpenses:
                    return 0;
                case AccountNames.OperatingExpenses:
                    return 0;
                case AccountNames.TransferGainLoss:
                    return 0;
                case AccountNames.TradeProfitLoss:
                    return 0;
                case AccountNames.MonthEndProvisionFines:
                    return 0;
                case AccountNames.AdminFees:
                    return forecastActuals.MonthAdminFees;
                case AccountNames.MotabilityUnits:
                    return forecastActuals.MonthMotabilityUnits;
                case AccountNames.DemoUnits:
                    return forecastActuals.MonthDemoUnits;
                case AccountNames.TradeUnits:
                    return 0;
                default:
                    throw new ArgumentException(accountName + "Invalid Account Name");
            }

        }

        private decimal GetBroughtIn(ForecastActuals forecastActuals, AccountNames accountName)
        {
            if (forecastActuals == null)
            {
                return 0;
            }
            switch (accountName)
            {
                case AccountNames.RetailUnits:
                    return forecastActuals.BroughtInUnits;
                case AccountNames.MetalProfit:
                    return forecastActuals.BroughtInMetalProfit;
                case AccountNames.MetalProfitDemo:
                    return forecastActuals.BroughtInMetalProfitDemo;
                case AccountNames.PaintProtectionIncome:
                    return forecastActuals.BroughtInPaintProtectionIncome;
                case AccountNames.DealerFitAccessoryGP:
                    return forecastActuals.BroughtInDealerFitAccessoryGP;
                case AccountNames.DeliveryGP:
                    return forecastActuals.BroughtInDeliveryGP;
                case AccountNames.BonusIncome:
                    return forecastActuals.BroughtInBonusIncome;
                case AccountNames.BonusIncome2:
                    return 0;
                case AccountNames.NewFinanceIncome:
                    return forecastActuals.BroughtInNewFinanceIncome;
                case AccountNames.NewVB:
                    return 0;
                case AccountNames.NewAddonIncomeexcludingGAP:
                    return forecastActuals.BroughtInNewAddonIncome;
                case AccountNames.NewGAPIncome:
                    return forecastActuals.BroughtInNewGAPIncome;
                case AccountNames.NewClawbacks:
                    return 0;
                case AccountNames.UsedFinanceIncome:
                    return forecastActuals.BroughtInUsedFinanceIncome;
                case AccountNames.UsedVB:
                    return 0;
                case AccountNames.UsedAddonIncomeexcludingGAP:
                    return forecastActuals.BroughtInUsedAddonIncome;
                case AccountNames.UsedGAPIncome:
                    return forecastActuals.BroughtInUsedGAPIncome;
                case AccountNames.UsedClawbacks:
                    return 0;
                case AccountNames.HoursAttended:
                    return 0;
                case AccountNames.HoursProductive:
                    return 0;
                case AccountNames.HoursSold:
                    return forecastActuals.BroughtInHoursSold;//FL
                case AccountNames.LabourSales:
                    return forecastActuals.BroughtInLabourSales;
                case AccountNames.LabourProfit:
                    return 0;
                case AccountNames.NonLabourSales:
                    return forecastActuals.BroughtInNonLabourSales;
                case AccountNames.NonLabourProfit:
                    return forecastActuals.BroughtInNonLabourProfit;
                case AccountNames.WorkshopSales:
                    return forecastActuals.BroughtInWorkshopSales;
                case AccountNames.CounterTradeSales:
                    return forecastActuals.BroughtInCounterTradeSales;
                case AccountNames.PartsProfit:
                    return forecastActuals.BroughtInPartsProfit;
                case AccountNames.OtherIncome:
                    return 0;
                case AccountNames.EmploymentExpenses:
                    return 0;
                case AccountNames.OperatingExpenses:
                    return 0;
                case AccountNames.TransferGainLoss:
                    return 0;
                case AccountNames.TradeProfitLoss:
                    return 0;
                case AccountNames.MonthEndProvisionFines:
                    return 0;
                case AccountNames.AdminFees:
                    return forecastActuals.BroughtInAdminFees;
                case AccountNames.MotabilityUnits:
                    return forecastActuals.BroughtInMotabilityUnits;
                case AccountNames.DemoUnits:
                    return forecastActuals.BroughtInDemoUnits;
                case AccountNames.TradeUnits:
                    return 0;
                default:
                    throw new ArgumentException(accountName + ": Invalid Account Name");
            }

           

        }

    
        private IList<ForecastMonth> GetForecastMonths(ForecastActuals forecastActualsMonth1Value, ForecastActuals forecastActualsMonth2Value, ForecastActuals forecastActualsMonth3Value, DateTime month, AccountNames accountName)
        {
            List<ForecastMonth> forecastMonths = new List<ForecastMonth>();
            switch (accountName)
            {
                case AccountNames.RetailUnits:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInUnits + forecastActualsMonth1Value.MonthUnits });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInUnits + forecastActualsMonth2Value.MonthUnits });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInUnits + forecastActualsMonth3Value.MonthUnits });
                    break;
                case AccountNames.MotabilityUnits:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInMotabilityUnits + forecastActualsMonth1Value.MonthMotabilityUnits });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInMotabilityUnits + forecastActualsMonth2Value.MonthMotabilityUnits });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInMotabilityUnits + forecastActualsMonth3Value.MonthMotabilityUnits });
                    break;
                case AccountNames.MetalProfit:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInMetalProfit + forecastActualsMonth1Value.MonthMetalProfit });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInMetalProfit + forecastActualsMonth2Value.MonthMetalProfit });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInMetalProfit + forecastActualsMonth3Value.MonthMetalProfit });
                    break;
                case AccountNames.MetalProfitDemo:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInMetalProfitDemo + forecastActualsMonth1Value.MonthMetalProfitDemo });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInMetalProfitDemo + forecastActualsMonth2Value.MonthMetalProfitDemo });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInMetalProfitDemo + forecastActualsMonth3Value.MonthMetalProfitDemo });
                    break;
                case AccountNames.PaintProtectionIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInPaintProtectionIncome + forecastActualsMonth1Value.MonthPaintProtectionIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInPaintProtectionIncome + forecastActualsMonth2Value.MonthPaintProtectionIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInPaintProtectionIncome + forecastActualsMonth3Value.MonthPaintProtectionIncome });
                    break;
                case AccountNames.DealerFitAccessoryGP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInDealerFitAccessoryGP + forecastActualsMonth1Value.MonthDealerFitAccessoryGP });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInDealerFitAccessoryGP + forecastActualsMonth2Value.MonthDealerFitAccessoryGP });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInDealerFitAccessoryGP + forecastActualsMonth3Value.MonthDealerFitAccessoryGP });
                    break;
                case AccountNames.DeliveryGP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInDeliveryGP + forecastActualsMonth1Value.MonthDeliveryGP });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInDeliveryGP + forecastActualsMonth2Value.MonthDeliveryGP });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInDeliveryGP + forecastActualsMonth3Value.MonthDeliveryGP });
                    break;
                case AccountNames.AdminFees:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInAdminFees + forecastActualsMonth1Value.MonthAdminFees });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInAdminFees + forecastActualsMonth2Value.MonthAdminFees });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInAdminFees + forecastActualsMonth3Value.MonthAdminFees });
                    break;
                case AccountNames.BonusIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInBonusIncome + forecastActualsMonth1Value.MonthBonusIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInBonusIncome + forecastActualsMonth2Value.MonthBonusIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInBonusIncome + forecastActualsMonth3Value.MonthBonusIncome });
                    break;
                case AccountNames.BonusIncome2:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.NewFinanceIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInNewFinanceIncome + forecastActualsMonth1Value.MonthNewFinanceIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInNewFinanceIncome + forecastActualsMonth2Value.MonthNewFinanceIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInNewFinanceIncome + forecastActualsMonth3Value.MonthNewFinanceIncome });
                    break;
                case AccountNames.NewVB:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.NewAddonIncomeexcludingGAP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInNewAddonIncome + forecastActualsMonth1Value.MonthNewAddonIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInNewAddonIncome + forecastActualsMonth2Value.MonthNewAddonIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInNewAddonIncome + forecastActualsMonth3Value.MonthNewAddonIncome });
                    break;
                case AccountNames.NewGAPIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInNewGAPIncome + forecastActualsMonth1Value.MonthNewGAPIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInNewGAPIncome + forecastActualsMonth2Value.MonthNewGAPIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInNewGAPIncome + forecastActualsMonth3Value.MonthNewGAPIncome });
                    break;
                case AccountNames.NewClawbacks:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.UsedFinanceIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInUsedFinanceIncome + forecastActualsMonth1Value.MonthUsedFinanceIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInUsedFinanceIncome + forecastActualsMonth2Value.MonthUsedFinanceIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInUsedFinanceIncome + forecastActualsMonth3Value.MonthUsedFinanceIncome });
                    break;
                case AccountNames.UsedVB:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.UsedAddonIncomeexcludingGAP:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInUsedAddonIncome + forecastActualsMonth1Value.MonthUsedAddonIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInUsedAddonIncome + forecastActualsMonth2Value.MonthUsedAddonIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInUsedAddonIncome + forecastActualsMonth3Value.MonthUsedAddonIncome });
                    break;
                case AccountNames.UsedGAPIncome:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInUsedGAPIncome + forecastActualsMonth1Value.MonthUsedGAPIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInUsedGAPIncome + forecastActualsMonth2Value.MonthUsedGAPIncome });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInUsedGAPIncome + forecastActualsMonth3Value.MonthUsedGAPIncome });
                    break;
                case AccountNames.UsedClawbacks:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.HoursAttended:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.HoursProductive:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.HoursSold:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInHoursSold + forecastActualsMonth1Value.MonthHoursSold });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInHoursSold + forecastActualsMonth2Value.MonthHoursSold });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInHoursSold + forecastActualsMonth3Value.MonthHoursSold });
                    break;
                case AccountNames.LabourSales:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInLabourSales + forecastActualsMonth1Value.MonthLabourSales });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInLabourSales + forecastActualsMonth2Value.MonthLabourSales });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInLabourSales + forecastActualsMonth3Value.MonthLabourSales });
                    break;
                case AccountNames.LabourProfit:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.NonLabourSales:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInNonLabourSales + forecastActualsMonth1Value.MonthNonLabourSales });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInNonLabourSales + forecastActualsMonth2Value.MonthNonLabourSales });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInNonLabourSales + forecastActualsMonth3Value.MonthNonLabourSales });
                    break;
                case AccountNames.NonLabourProfit:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.WorkshopSales:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInWorkshopSales + forecastActualsMonth1Value.MonthWorkshopSales });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInWorkshopSales + forecastActualsMonth2Value.MonthWorkshopSales });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInWorkshopSales + forecastActualsMonth3Value.MonthWorkshopSales});
                    break;
                case AccountNames.CounterTradeSales:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInCounterTradeSales + forecastActualsMonth1Value.MonthCounterTradeSales });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInCounterTradeSales + forecastActualsMonth2Value.MonthCounterTradeSales});
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInCounterTradeSales + forecastActualsMonth3Value.MonthCounterTradeSales});
                    break;
                case AccountNames.PartsProfit:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInPartsProfit + forecastActualsMonth1Value.MonthPartsProfit });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInPartsProfit + forecastActualsMonth2Value.MonthPartsProfit, });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInPartsProfit + forecastActualsMonth3Value.MonthPartsProfit });
                    break;
                case AccountNames.OtherIncome:
                case AccountNames.EmploymentExpenses:
                case AccountNames.OperatingExpenses:
                    AddEmptyForecastMonths(forecastMonths, month);
                    break;
                case AccountNames.TransferGainLoss:
                case AccountNames.TradeProfitLoss:
                case AccountNames.TradeUnits:
                case AccountNames.MonthEndProvisionFines:
                    break;
                case AccountNames.DemoUnits:
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)), Value = forecastActualsMonth1Value.BroughtInDemoUnits + forecastActualsMonth1Value.MonthDemoUnits });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)), Value = forecastActualsMonth2Value.BroughtInDemoUnits + forecastActualsMonth2Value.MonthDemoUnits });
                    forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)), Value = forecastActualsMonth3Value.BroughtInDemoUnits + forecastActualsMonth3Value.MonthDemoUnits });
                    break;
                default:
                    throw new ArgumentException(accountName + ": Invalid Account Name");
            }


            forecastMonths = forecastMonths.OrderBy(f => f.DisplayOrder).ToList();
            return forecastMonths;
            
        }

        private void AddEmptyForecastMonths(List<ForecastMonth> forecastMonths, DateTime month)
        {
            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 3, Month = GenerateMonthLabel(month.AddMonths(-1)) });
            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 2, Month = GenerateMonthLabel(month.AddMonths(-2)) });
            //forecastMonths.Add(new ForecastMonth() { DisplayOrder = 1, Month = GenerateMonthLabel(month.AddMonths(-3)) });
        }

        private string GenerateMonthLabel(DateTime dateTime)
        {
            return CultureInfo.CurrentCulture.DateTimeFormat.GetAbbreviatedMonthName(dateTime.Month);
        }

        private decimal CalculateRunRate(int departmentId, List<DateTime> siteNonWorkingDays, decimal doneInMonth, DateTime month)
        {
            //Runrate only provided if user looking at current month.

            int nonWorkingDaysTillDate = 0, nonWorkingDaysInMonth = 0, workingDaysTillDate = 0, workingDaysInMonth = 0;

            workingDaysTillDate = DateTime.UtcNow.Day;
            workingDaysInMonth = DateTime.DaysInMonth(month.Year, month.Month);

            //Department 8 & 10 (Service & Parts) don't work over the weekend or bank holidays.
            if (departmentId == 8 || departmentId == 10)
            {
                nonWorkingDaysTillDate = siteNonWorkingDays.Where(s => s <= DateTime.UtcNow).Count();
                nonWorkingDaysInMonth = siteNonWorkingDays.Count();

                workingDaysTillDate -= nonWorkingDaysTillDate;
                workingDaysInMonth -= nonWorkingDaysInMonth;
            }

            if (month.Month == DateTime.UtcNow.Month && month.Year == DateTime.UtcNow.Year)
            {
                var DaysComplete = workingDaysTillDate;
                var DaysToGo = workingDaysInMonth - workingDaysTillDate;

                return doneInMonth / DaysComplete * DaysToGo;
            }
            else
            {
                return 0;
            }
        }

        public async Task<IEnumerable<Model.Fcst.Department>> AllDepartments(int siteId, DealerGroupName dealerGroup)
        {
            var allDepartmentsResult = await liveForecastDataAccess.AllDepartments(dealerGroup);

            //Remove 'New Fakenham' if Site is not VW Huntingdon
            if (siteId == 9)//Reset the order
            {
                var rowIndexToMove = allDepartmentsResult.FindIndex(r => r.Id == 12);// New Fakenham
                if (rowIndexToMove != -1)
                {
                    var rowToMove = allDepartmentsResult[rowIndexToMove];
                    allDepartmentsResult.RemoveAt(rowIndexToMove);
                    allDepartmentsResult.Insert(1, rowToMove);
                }
            }
            else // Remove from the list
            {
                var rowToRemove = allDepartmentsResult.Where(r => r.Id == 12);// New Fakenham
                if (rowToRemove.Any()) allDepartmentsResult.Remove(rowToRemove.First());
            }

            return allDepartmentsResult;
        }

        public async Task<IEnumerable<ForecastVersionVM>> GetForecastVersions(int siteId, int forecastId, DealerGroupName dealerGroup)
        {
            return await liveForecastDataAccess.GetForecastVersions(siteId, forecastId, dealerGroup);
        }

        public async Task<bool> SaveLiveForecast(SaveLiveForecast saveLiveForecast, int userId, DealerGroupName dealerGroup)
        {
            var doneStatusTypes = await liveForecastDataAccess.GetAllDoneStatusTypes(dealerGroup);
            var accountDepartments = await liveForecastDataAccess.AllAccountDepartments(dealerGroup);
            var month = await liveForecastDataAccess.GetMonth(saveLiveForecast.ForecastId, dealerGroup);
            var allAccounts = await liveForecastDataAccess.GetAllAccounts(dealerGroup);
            //Check forcastVersionId
            var forcastVersionId = saveLiveForecast.ForecastVersionId;

            //Create new 
            if (forcastVersionId == 0)
            {
                var versionLabel = saveLiveForecast.VersionName;

                var forecastVersion = new ForecastVersion()
                {
                    Label = versionLabel,
                    LastSavedById = userId,
                    LastSavedDate = DateTime.UtcNow,
                    ApprovalStateId = 1,
                    SiteId = saveLiveForecast.SiteId,
                    ForecastId = saveLiveForecast.ForecastId
                };
                
                forcastVersionId = await liveForecastDataAccess.CreateForecastVersion(forecastVersion, dealerGroup);

                //

                //Generate ForecastLines for given department
                List<ForecastLine> forecastLines = new List<ForecastLine>();
                
                foreach(var forecastRowGroup in saveLiveForecast.ForecastInput.ForecastRowGroups)
                {
                    foreach(var forecastRow in forecastRowGroup.ForecastRows.Where(f => f.IsPerUnit == false && f.AccountId != 0).ToList())
                    {
                        //Create a line for given Department
                        var broughtInForecastLine = GenerateForecastLine(saveLiveForecast.DepartmentId, forecastRow.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "BroughtIn").First().Id, forecastRow.BroughtIn);
                        var doneInMonthForecastLine = GenerateForecastLine(saveLiveForecast.DepartmentId, forecastRow.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "DoneInMonth").First().Id, forecastRow.DoneInMonth);
                        var siteForecastForecastLine = GenerateForecastLine(saveLiveForecast.DepartmentId, forecastRow.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "SiteForecast").First().Id, forecastRow.SiteForecast);
                        var directorForecastForecastLine = GenerateForecastLine(saveLiveForecast.DepartmentId, forecastRow.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "DirectorForecast").First().Id, forecastRow.DirectorAdj);

                        forecastLines.Add(broughtInForecastLine);
                        forecastLines.Add(doneInMonthForecastLine);
                        forecastLines.Add(siteForecastForecastLine);
                        forecastLines.Add(directorForecastForecastLine);
                    }
                }

                //Generate ForecastLines for other departments
                var forecastActualsAllDepartments = await liveForecastDataAccess.GetForecastActualsAllDepartments(saveLiveForecast.SiteId, month,dealerGroup);
                var otherAccountDepartments = accountDepartments.Where(d => d.DepartmentId != saveLiveForecast.DepartmentId).ToList();

                //Remove New Fakenham department from the list of the Department from sites other than 'VW Huntingdon'
                if (saveLiveForecast.SiteId != 9)
                {
                    otherAccountDepartments = otherAccountDepartments.Where(d => d.DepartmentId != 12).ToList();
                }


                foreach (var accountDepartment in otherAccountDepartments.ToLookup(d => d.DepartmentId))
                {
                    var forecastActualDept = forecastActualsAllDepartments.Where(f => f.DepartmentId == accountDepartment.Key);

                    foreach (var account in accountDepartment)
                    {
                        var accountName = allAccounts.Where(a => a.Id == account.AccountId).First().Name;
                        var BroughtIn = forecastActualDept.Any() ? GetBroughtIn(forecastActualDept.First(), GetEnumFromAccountName(accountName)) : 0;
                        var DoneInMonth = forecastActualDept.Any() ? GetDoneInMonth(forecastActualDept.First(), GetEnumFromAccountName(accountName)) : 0;

                        var broughtInForecastLine = GenerateForecastLine(accountDepartment.Key, account.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "BroughtIn").First().Id, BroughtIn);
                        var doneInMonthForecastLine = GenerateForecastLine(accountDepartment.Key, account.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "DoneInMonth").First().Id, DoneInMonth);
                        var siteForecastForecastLine = GenerateForecastLine(accountDepartment.Key, account.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "SiteForecast").First().Id, 0);
                        var directorForecastForecastLine = GenerateForecastLine(accountDepartment.Key, account.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "DirectorForecast").First().Id, 0);

                        forecastLines.Add(broughtInForecastLine);
                        forecastLines.Add(doneInMonthForecastLine);
                        forecastLines.Add(siteForecastForecastLine);
                        forecastLines.Add(directorForecastForecastLine);
                        
                    }
                }

                //Removing AccountID = 0.
                forecastLines = forecastLines.Where(f => f.AccountId != 0).ToList();

                await liveForecastDataAccess.AddForecastLines(forecastLines, dealerGroup);

            }
            else if (forcastVersionId > 0)
            {
                await liveForecastDataAccess.UpdateForecastVersion(forcastVersionId, userId, DateTime.UtcNow, dealerGroup);

                //Update data for forecastLines

                //ForecastLines for given department
                List<ForecastLine> forecastLines = new List<ForecastLine>();

                foreach (var forecastRowGroup in saveLiveForecast.ForecastInput.ForecastRowGroups)
                {
                    foreach (var forecastRow in forecastRowGroup.ForecastRows.Where(f => f.IsPerUnit == false && f.AccountId != 0).ToList())
                    {
                        //Get a line for given Department
                        var broughtInForecastLine = await GetForecastLine(saveLiveForecast.DepartmentId, forecastRow.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "BroughtIn").First().Id, dealerGroup);
                        var doneInMonthForecastLine = await GetForecastLine(saveLiveForecast.DepartmentId, forecastRow.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "DoneInMonth").First().Id, dealerGroup);
                        var siteForecastForecastLine = await GetForecastLine(saveLiveForecast.DepartmentId, forecastRow.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "SiteForecast").First().Id,dealerGroup);
                        var directorForecastForecastLine = await GetForecastLine(saveLiveForecast.DepartmentId, forecastRow.AccountId, forcastVersionId, doneStatusTypes.Where(d => d.Name == "DirectorForecast").First().Id, dealerGroup);

                        if (broughtInForecastLine != null)
                        {
                            broughtInForecastLine.Value = forecastRow.BroughtIn;
                            forecastLines.Add(broughtInForecastLine);
                        }
                        if (doneInMonthForecastLine != null)
                        {
                            doneInMonthForecastLine.Value = forecastRow.DoneInMonth;
                            forecastLines.Add(doneInMonthForecastLine);

                        }
                        if (siteForecastForecastLine != null)
                        {
                            siteForecastForecastLine.Value = forecastRow.SiteForecast;
                            forecastLines.Add(siteForecastForecastLine);

                        }
                        if (directorForecastForecastLine != null)
                        {
                            directorForecastForecastLine.Value = forecastRow.DirectorAdj;
                            forecastLines.Add(directorForecastForecastLine);

                        }
                    }
                }

                await liveForecastDataAccess.UpdateForecastLines(forecastLines, dealerGroup);

            }

            //If the Update is made for Departments: New Retail (1), Used(6), Service(8), recal and update F&I and Parts
            //Call Save again to save rows for Linked Department.
            if (saveLiveForecast.DepartmentId == 1 || saveLiveForecast.DepartmentId == 6 || saveLiveForecast.DepartmentId == 8)
            {
                
                if (saveLiveForecast.ForecastInput.LinkedForecastInputVM != null)
                { 
                    var linkedSaveLiveForecast = new SaveLiveForecast()
                    {
                        DepartmentId = saveLiveForecast.ForecastInput.LinkedForecastInputVM.DepartmentId,
                        ForecastInput = CleanUp(saveLiveForecast.ForecastInput.LinkedForecastInputVM),
                        ForecastId = saveLiveForecast.ForecastId,
                        ForecastVersionId = forcastVersionId,
                        SiteId = saveLiveForecast.SiteId,
                        VersionName = saveLiveForecast.VersionName
                    };

                var result = await SaveLiveForecast(linkedSaveLiveForecast, userId, dealerGroup);
                }
            }


            return true;
            
        }

        private async Task<ForecastLine> GetForecastLine(int departmentId, int accountId, int forecastVersionId, int doneStatusTypeId, DealerGroupName dealerGroup)
        {
            return await liveForecastDataAccess.GetForecastLine(departmentId, accountId, forecastVersionId, doneStatusTypeId, dealerGroup);
        }

        private ForecastLine GenerateForecastLine(int departmentId, int accountId, int forcastVersionId, int doneStatusTypesId, decimal broughtIn)
        {
            return new ForecastLine()
            {
                DepartmentId = departmentId,
                AccountId = accountId,
                ForecastVersionId = forcastVersionId,
                DoneStatusTypeId = doneStatusTypesId,
                Value = broughtIn
            };

            


        }

        public async Task<int> UpdateForecastVersionApprovalState(int forecastId, int forecastVersionId, ForecastApprovalStates forecastApprovalState, int userId, DealerGroupName dealerGroup)
        {
            var currentForecastVersionRow = await liveForecastDataAccess.GetForecastVersion(forecastId, forecastVersionId, dealerGroup);
            currentForecastVersionRow.ApprovalStateId = ((int)forecastApprovalState);

            switch (forecastApprovalState)
            {
                case ForecastApprovalStates.Rejected:
                    var rejectorName = await userService.GetUsersNameFromDatabase(userId, dealerGroup);

                    await CreateVersionCopy(forecastId, forecastVersionId, userId, currentForecastVersionRow.Label, dealerGroup);
                    currentForecastVersionRow.LastSavedById = userId;
                    currentForecastVersionRow.LastSavedDate = DateTime.UtcNow;
                    currentForecastVersionRow.Label = $"{currentForecastVersionRow.Label} (Rejected {DateTime.UtcNow.ToString("dd MMM HH:mm")} by {rejectorName})";
                    break;
                case ForecastApprovalStates.Approved:
                case ForecastApprovalStates.InProgress:
                    currentForecastVersionRow.LastSavedById = userId;
                    currentForecastVersionRow.LastSavedDate = DateTime.UtcNow;
                    break;
                case ForecastApprovalStates.Submitted:
                    currentForecastVersionRow.SubmittedById = userId;
                    currentForecastVersionRow.SubmittedDate = DateTime.UtcNow;
                    break;
            }
                 
            return await liveForecastDataAccess.UpdateForecastVersionApprovalState(currentForecastVersionRow, dealerGroup);
        }

        private async Task CreateVersionCopy(int forecastId, int forecastVersionId, int userId, string label, DealerGroupName dealerGroup)
        {
            //Get current version
            var curerntForecastVersion = await liveForecastDataAccess.GetForecastVersion(forecastId, forecastVersionId, dealerGroup);
            //Create new version
            var newForecastVersion = new ForecastVersion();
            newForecastVersion.Label = label;
            newForecastVersion.LastSavedById = userId;
            newForecastVersion.LastSavedDate = DateTime.UtcNow;
            newForecastVersion.ApprovalStateId = (int)ForecastApprovalStates.InProgress;
            newForecastVersion.SiteId = curerntForecastVersion.SiteId;
            newForecastVersion.ForecastId = curerntForecastVersion.ForecastId;


            var newForecastVersionId = await liveForecastDataAccess.CreateForecastVersion(newForecastVersion, dealerGroup);

            //Copy data 
            await liveForecastDataAccess.CreateVersionCopy(curerntForecastVersion.Id, newForecastVersionId, dealerGroup);
        }

        public async Task<ForecastInputVM> Recalculate(ForecastInputVM forecastInputVM, int userId, DealerGroupName dealerGroup)
        {
            var groupAccounts = await liveForecastDataAccess.GetGroupAccounts(forecastInputVM.DepartmentId, dealerGroup);
            var forecastRowGroups = forecastInputVM.ForecastRowGroups;

            //recal per units..
            var updateRowAccountId = forecastInputVM.UpdatedRow.AccountId;
            var accountRow = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == updateRowAccountId && f.IsPerUnit == false));
            var perUnitRow = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == updateRowAccountId && f.IsPerUnit == true));
            decimal siteForecastPerUnitValue = forecastInputVM.ForecastRowGroups[0].ForecastRows[0].SiteForecast;
            decimal siteTotalPerUnitValue = forecastInputVM.ForecastRowGroups[0].ForecastRows[0].SiteTotal;
            decimal directorAdjPerUnitValue = forecastInputVM.ForecastRowGroups[0].ForecastRows[0].DirectorAdj;
            decimal directorTotalPerUnitValue = forecastInputVM.ForecastRowGroups[0].ForecastRows[0].DirectorTotal;

            //F&I
            if (forecastInputVM.DepartmentId == 7)
            {
                int[] FINewAccountIds = { 8, 10, 11 };
                //Check if its F&I (deptId: 7) For Account: Used Finanace Income(13), Used Add-on Income (15)& used Gap Income (16)it should get Units from "Used"(DeptId: 6)
                int[] FIUsedAccountIds = { 13, 15, 16 };
                var month = await liveForecastDataAccess.GetMonth(forecastInputVM.ForecastId, dealerGroup);
                if (FINewAccountIds.Contains(updateRowAccountId))
                {
                    //Get New Units of New Retail
                    var newUnit = await liveForecastDataAccess.GetForecastLines(1, forecastInputVM.ForecastVersionId, 1, month, dealerGroup);
                    if (newUnit.Any())
                    {
                        siteForecastPerUnitValue = newUnit.Where(x => x.DoneStatusTypeName == "SiteForecast").First().Value;
                        siteTotalPerUnitValue = newUnit.Where(x => x.DoneStatusTypeName == "BroughtIn").First().Value + newUnit.Where(x => x.DoneStatusTypeName == "DoneInMonth").First().Value + newUnit.Where(x => x.DoneStatusTypeName == "SiteForecast").First().Value;
                        directorAdjPerUnitValue = newUnit.Where(x => x.DoneStatusTypeName == "DirectorForecast").First().Value;
                        directorTotalPerUnitValue = siteTotalPerUnitValue + directorAdjPerUnitValue;
                    }
                }
                else if (FIUsedAccountIds.Contains(updateRowAccountId))
                {
                    //Get Used Units of Used
                    var usedUnit = await liveForecastDataAccess.GetForecastLines(6, forecastInputVM.ForecastVersionId, 1, month, dealerGroup);
                    if (usedUnit.Any())
                    {
                        siteForecastPerUnitValue = usedUnit.Where(x => x.DoneStatusTypeName == "SiteForecast").First().Value;
                        siteTotalPerUnitValue = usedUnit.Where(x => x.DoneStatusTypeName == "BroughtIn").First().Value + usedUnit.Where(x => x.DoneStatusTypeName == "DoneInMonth").First().Value + usedUnit.Where(x => x.DoneStatusTypeName == "SiteForecast").First().Value;
                        directorAdjPerUnitValue = usedUnit.Where(x => x.DoneStatusTypeName == "DirectorForecast").First().Value; ;
                        directorTotalPerUnitValue = siteTotalPerUnitValue + directorAdjPerUnitValue;
                    }
                }
            }
                


            //Labour Profit
            if (forecastInputVM.UpdatedRow.AccountId == 22)
            {
                siteForecastPerUnitValue = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 21 && f.IsPerUnit == false)).First().SiteForecast;
                siteTotalPerUnitValue = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 21 && f.IsPerUnit == false)).First().SiteTotal;
                directorAdjPerUnitValue = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 21 && f.IsPerUnit == false)).First().DirectorAdj;
                directorTotalPerUnitValue = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 21 && f.IsPerUnit == false)).First().DirectorTotal;
            }

            //Labour Sales
            if (forecastInputVM.UpdatedRow.AccountId == 21)
            {
                siteForecastPerUnitValue = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 20 && f.IsPerUnit == false)).First().SiteForecast;
                siteTotalPerUnitValue = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 20 && f.IsPerUnit == false)).First().SiteTotal;
                directorAdjPerUnitValue = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 20 && f.IsPerUnit == false)).First().DirectorAdj;
                directorTotalPerUnitValue = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 20 && f.IsPerUnit == false)).First().DirectorTotal;

                //in the end recall the function to recal Labout Profit.

            }


            if (updateRowAccountId == 1 || updateRowAccountId == 20 || updateRowAccountId == 34) // Units, Hours, Demo Units
            {
                var unitRow = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == updateRowAccountId)).First();
                unitRow.SiteTotal = unitRow.BroughtIn + unitRow.DoneInMonth + unitRow.SiteForecast;
                unitRow.DirectorTotal = unitRow.SiteTotal + unitRow.DirectorAdj;

                var totalUnitRow = new ForecastRow();

                if (forecastInputVM.DepartmentId == 6)
                {
                    var retailUnitRow = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 1)).First();
                    retailUnitRow.SiteTotal = retailUnitRow.BroughtIn + retailUnitRow.DoneInMonth + retailUnitRow.SiteForecast;
                    retailUnitRow.DirectorTotal = retailUnitRow.SiteTotal + retailUnitRow.DirectorAdj;

                    var demoUnitRow = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 34)).First();
                    demoUnitRow.SiteTotal = demoUnitRow.BroughtIn + demoUnitRow.DoneInMonth + demoUnitRow.SiteForecast;
                    demoUnitRow.DirectorTotal = demoUnitRow.SiteTotal + demoUnitRow.DirectorAdj;

                    totalUnitRow.SiteForecast = retailUnitRow.SiteForecast + demoUnitRow.SiteForecast;
                    totalUnitRow.SiteTotal = retailUnitRow.SiteTotal + demoUnitRow.SiteTotal;
                    totalUnitRow.DirectorTotal = retailUnitRow.DirectorTotal + demoUnitRow.DirectorTotal;
                    totalUnitRow.DirectorAdj = retailUnitRow.DirectorAdj + demoUnitRow.DirectorAdj;

                }
                

                foreach (var rowGroup in forecastInputVM.ForecastRowGroups)
                {
                    var forecastRowsToCalculate = new List<ForecastRow>();
                    
                    forecastRowsToCalculate = rowGroup.ForecastRows.Where(f => f.IsPerUnit == false ).ToList();
                    

                    foreach (var row in forecastRowsToCalculate)
                    {
                        var perAccountRow = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == row.AccountId && f.IsPerUnit == true));
                        if (perAccountRow.Any())
                        {
                            int[] FINewAccountIds = { 8, 10, 11 };
                            //Check if its F&I (deptId: 7) For Account: Used Finanace Income(13), Used Add-on Income (15)& used Gap Income (16)it should get Units from "Used"(DeptId: 6)
                            int[] FIUsedAccountIds = { 13, 15, 16 };

                            if ((forecastInputVM.UpdatedField == "Department_NewRetail" && FINewAccountIds.Contains(row.AccountId)) ||
                                (forecastInputVM.UpdatedField == "Department_Used" && FIUsedAccountIds.Contains(row.AccountId)) ||
                                (forecastInputVM.UpdatedField != "Department_NewRetail" && forecastInputVM.UpdatedField != "Department_Used"))
                            {
                                if (forecastInputVM.DepartmentId == 6 && updateRowAccountId == 34 && row.AccountId == 2)
                                {
                                    //do nothing
                                }
                                else if (forecastInputVM.DepartmentId == 6 && updateRowAccountId == 34 && row.AccountId == 36)
                                {
                                    SetForecastRowForAccountsWithPerAccount(row, unitRow, perAccountRow.First(), forecastInputVM.UpdatedRow);
                                }
                                else if (forecastInputVM.DepartmentId == 6 && updateRowAccountId == 1 && row.AccountId == 36)
                                {
                                    //do nothing
                                }
                                else if (forecastInputVM.DepartmentId == 6 && updateRowAccountId == 1 && row.AccountId == 2)
                                {
                                    SetForecastRowForAccountsWithPerAccount(row, unitRow, perAccountRow.First(), forecastInputVM.UpdatedRow);
                                }
                                else if (forecastInputVM.DepartmentId == 6)
                                {
                                    SetForecastRowForAccountsWithPerAccount(row, totalUnitRow, perAccountRow.First(), totalUnitRow);
                                }
                                else
                                {
                                    SetForecastRowForAccountsWithPerAccount(row, unitRow, perAccountRow.First(), forecastInputVM.UpdatedRow);
                                }
                            }
                            
                           
                        }
                    }
                }
            }
            else if (forecastInputVM.UpdatedRow.IsPerUnit || (updateRowAccountId == 20 && forecastInputVM.UpdatedRow.IsPerUnit))
            {
                //perUnitRow.First().SiteTotal = perUnitRow.First().BroughtIn + perUnitRow.First().DoneInMonth + perUnitRow.First().SiteForecast;

                if (accountRow.Any())
                {
                    //reset SiteTotal
                    accountRow.First().SiteTotal -= accountRow.First().SiteForecast;


                    accountRow.First().SiteForecast = perUnitRow.First().SiteForecast * siteForecastPerUnitValue;
                    accountRow.First().SiteTotal += accountRow.First().SiteForecast;
                    perUnitRow.First().SiteTotal = HelperService.DivideByDecimal(accountRow.First().SiteTotal, siteTotalPerUnitValue);


                    accountRow.First().DirectorAdj = perUnitRow.First().SiteTotal * directorAdjPerUnitValue;
                    accountRow.First().DirectorTotal = accountRow.First().SiteTotal + accountRow.First().DirectorAdj;
                    perUnitRow.First().DirectorTotal = HelperService.DivideByDecimal(accountRow.First().DirectorTotal, directorTotalPerUnitValue);
                }
            }
            else if (!forecastInputVM.UpdatedRow.IsPerUnit || (updateRowAccountId == 20 && !forecastInputVM.UpdatedRow.IsPerUnit))
            {
                accountRow.First().SiteTotal = accountRow.First().BroughtIn + accountRow.First().DoneInMonth + accountRow.First().SiteForecast;
                accountRow.First().DirectorTotal = accountRow.First().SiteTotal + accountRow.First().DirectorAdj;

                if (perUnitRow.Any())
                {
                    perUnitRow.First().SiteForecast = HelperService.DivideByDecimal(accountRow.First().SiteForecast, siteForecastPerUnitValue);
                    perUnitRow.First().DirectorAdj = HelperService.DivideByDecimal(accountRow.First().DirectorAdj, directorAdjPerUnitValue);
                    perUnitRow.First().SiteTotal = HelperService.DivideByDecimal(accountRow.First().SiteTotal, siteTotalPerUnitValue);
                    perUnitRow.First().DirectorTotal = HelperService.DivideByDecimal(accountRow.First().DirectorTotal, directorTotalPerUnitValue);

                }
            }



            //recal Gross Profit
            var grossProfitAccountIds = groupAccounts.Where(ga => ga.IsInGrossProfit == true).Select(ga => ga.AccountId).ToArray();
            var grossProfitForecastRow = forecastRowGroups.Where(f => f.Label == "Gross Profit").First().ForecastRows[0];
            grossProfitForecastRow.SiteForecast = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.SiteForecast)).Sum();
            grossProfitForecastRow.DirectorAdj = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorAdj)).Sum();
            grossProfitForecastRow.SiteTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.SiteTotal)).Sum();
            grossProfitForecastRow.DirectorTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorTotal)).Sum();




            //Expenses
            var expensesAccountIds = groupAccounts.Where(ga => ga.IsInGrossProfit == false && ga.IsStatistic == false).Select(ga => ga.AccountId).ToArray();
            var expenseSiteForecast = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => expensesAccountIds.Contains(fr.AccountId)).Select(fr => fr.SiteForecast)).Sum();
            var expenseDirectorAdj = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => expensesAccountIds.Contains(fr.AccountId)).Select(fr => fr.DirectorAdj)).Sum();
            var expenseSiteTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => expensesAccountIds.Contains(fr.AccountId)).Select(fr => fr.SiteTotal)).Sum();
            var expenseDirectorTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => expensesAccountIds.Contains(fr.AccountId)).Select(fr => fr.DirectorTotal)).Sum();

            //recal Net Profit
            var netProfitForecastRow = forecastRowGroups.Where(f => f.Label == "Net Profit").First().ForecastRows[0];
            var netProfitAccountIds = groupAccounts.Where(ga => ga.IsStatistic == false).Select(ga => ga.AccountId).ToArray();
            //netProfitForecastRow.SiteForecast = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId)).Select(fr => fr.SiteForecast)).Sum() + expenseSiteForecast;
            netProfitForecastRow.SiteTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.SiteTotal)).Sum() + expenseSiteTotal;
            netProfitForecastRow.DirectorAdj = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorAdj)).Sum() + expenseDirectorAdj;
            netProfitForecastRow.DirectorTotal = forecastRowGroups.SelectMany(f => f.ForecastRows.Where(fr => grossProfitAccountIds.Contains(fr.AccountId) && fr.IsPerUnit == false).Select(fr => fr.DirectorTotal)).Sum() + expenseDirectorTotal;




            //recal actualaties
            foreach (var accounts in forecastRowGroups.SelectMany(f => f.ForecastRows).ToList())
            {
                var directorTotal = accounts.DirectorTotal;

                foreach (var forecastActual in accounts.ForecastActuals)
                {
                    //accounts.ForecastActuals.Where(f => f.ActualLabel == forecastActual.ActualLabel).First().VarianceValue = 99;
                    var actualValue = accounts.ForecastActuals.Where(f => f.ActualLabel == forecastActual.ActualLabel).First().ActualValue;
                    accounts.ForecastActuals.Where(f => f.ActualLabel == forecastActual.ActualLabel).First().VarianceValue = directorTotal - actualValue;
                }
            }


            //dealership summary
            forecastInputVM.DealershipSummaryTable.rows.Where(d => d.Id == forecastInputVM.DepartmentId).First().Forecast = netProfitForecastRow.DirectorTotal;
            var dealershipSummaryForecastTotal = forecastInputVM.DealershipSummaryTable.rows.Where(d => d.Id != 0).Select(r => r.Forecast).Sum();
            forecastInputVM.DealershipSummaryTable.rows.Where(d => d.Id == 0).First().Forecast = dealershipSummaryForecastTotal;

            //department summary
            foreach (var account in forecastInputVM.DepartmentSummaryTable.rows)
            {
                account.Forecast = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == account.Id && f.IsPerUnit == false).Select(f => f.DirectorTotal)).Sum();
            }
            forecastInputVM.DepartmentSummaryTable.rows.Where(f => f.Id == 0 && f.Label == "Gross Profit").First().Forecast = grossProfitForecastRow.DirectorTotal;
            forecastInputVM.DepartmentSummaryTable.rows.Where(f => f.Id == 0 && f.Label == "Net Profit").First().Forecast = netProfitForecastRow.DirectorTotal;



            

            //If the update was for Department 1 and Unit (accountId 1) //Update F&I records 
            //Get F&I
            if ((forecastInputVM.DepartmentId == 1 || forecastInputVM.DepartmentId == 6) && forecastInputVM.UpdatedRow.AccountId == 1) //New Retail, Used | Units
            {
                var fAndIForecastInputVM = await GetLiveForecast(forecastInputVM.SiteId, 7, forecastInputVM.ForecastId, forecastInputVM.ForecastVersionId, userId, dealerGroup);
                fAndIForecastInputVM.ForecastRowGroups.Insert(0, forecastInputVM.ForecastRowGroups.First()); //Insert the Unit
                fAndIForecastInputVM.UpdatedRow = forecastInputVM.UpdatedRow;
                fAndIForecastInputVM.DepartmentId = 7; //F&I
                if (forecastInputVM.DepartmentId == 1)
                {
                    fAndIForecastInputVM.UpdatedField = "Department_NewRetail";
                }
                else if (forecastInputVM.DepartmentId == 6)
                {
                    fAndIForecastInputVM.UpdatedField = "Department_Used";
                }

                var fAndIForecastInputVMResult = await Recalculate(fAndIForecastInputVM, userId, dealerGroup);
                var fAndIForecast = fAndIForecastInputVMResult.DepartmentSummaryTable.rows.Where(r => r.Label == "Net Profit").First().Forecast;

                //Setting the value in the Dealership Summary
                forecastInputVM.DealershipSummaryTable.rows.Where(r => r.Id == 7).First().Forecast = fAndIForecast;
                forecastInputVM.LinkedForecastInputVM = fAndIForecastInputVMResult;
            }

            else if ((forecastInputVM.DepartmentId == 8) && forecastInputVM.UpdatedRow.AccountId == 20) //Service | Hours
            {
                var partsForecastInputVM = await GetLiveForecast(forecastInputVM.SiteId, 10, forecastInputVM.ForecastId, forecastInputVM.ForecastVersionId, userId, dealerGroup);
                partsForecastInputVM.ForecastRowGroups.Insert(0, forecastInputVM.ForecastRowGroups.First()); //Insert the Unit
                partsForecastInputVM.UpdatedRow = forecastInputVM.UpdatedRow;
                partsForecastInputVM.DepartmentId = 10; //Parts

                partsForecastInputVM.UpdatedField = "Department_Service";
                

                var partsForecastInputVMResult = await Recalculate(partsForecastInputVM, userId, dealerGroup);
                var partsForecast = partsForecastInputVMResult.DepartmentSummaryTable.rows.Where(r => r.Label == "Net Profit").First().Forecast;

                //Setting the value in the Dealership Summary
                forecastInputVM.DealershipSummaryTable.rows.Where(r => r.Id == 10).First().Forecast = partsForecast;
                forecastInputVM.LinkedForecastInputVM = partsForecastInputVMResult;
            }


            if (forecastInputVM.UpdatedRow.AccountId == 21 || forecastInputVM.UpdatedRow.AccountId == 20)
            {
                var labourProfitRow = forecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == 22 && f.IsPerUnit == true)).First();
                forecastInputVM.UpdatedRow = labourProfitRow;
                forecastInputVM = await Recalculate(forecastInputVM, userId, dealerGroup);
            }


            //if DepartmentId = 1 and either Units or Motability Units are updated. then TotalUnits row
            if ((forecastInputVM.DepartmentId == 1 || forecastInputVM.DepartmentId == 12) && (forecastInputVM.UpdatedRow.AccountId == 1 || forecastInputVM.UpdatedRow.AccountId == 33))
            {
                AddTotalUnitsRowToDepartment(forecastInputVM);
            }

            //if DepartmentId = 6 and either Units or Demo Units are updated. then TotalUnits row
            if (forecastInputVM.DepartmentId == 6 && (forecastInputVM.UpdatedRow.AccountId == 1 || forecastInputVM.UpdatedRow.AccountId == 34))
            {
                AddTotalUnitsRowToUsedDepartment(forecastInputVM);
            }


            return forecastInputVM;

        }

        private void SetForecastRowForAccountsWithPerAccount(ForecastRow row, ForecastRow unitRow, ForecastRow perAccountRow, ForecastRow updatedRow)
        {
            //reset SiteTotal
            row.SiteTotal -= row.SiteForecast;

            var tempPerAccountSiteForecast = (perAccountRow.SiteForecast == 0) ? perAccountRow.RunRate : perAccountRow.SiteForecast;
            row.SiteForecast = updatedRow.SiteForecast * tempPerAccountSiteForecast;
            row.DirectorAdj = updatedRow.DirectorAdj * perAccountRow.SiteTotal;
            row.SiteTotal += row.SiteForecast;
            row.DirectorTotal = row.SiteTotal + row.DirectorAdj;

            perAccountRow.SiteForecast = HelperService.DivideByDecimal(row.SiteForecast, unitRow.SiteForecast);
            perAccountRow.SiteTotal = HelperService.DivideByDecimal(row.SiteTotal, unitRow.SiteTotal);
            perAccountRow.DirectorTotal = HelperService.DivideByDecimal(row.DirectorTotal, unitRow.DirectorTotal);
        }

        public async Task<IEnumerable<ForecastStatus>> GetForecastStatus(DateTime month, int forecastId, DealerGroupName dealerGroup)
        {
            List<ForecastStatus> forecastStatuses = new List<ForecastStatus>();
            var allForecastStatus = await liveForecastDataAccess.GetForecastStatus(month, null, dealerGroup);

            //Generate list for each site
            var distinctSiteDetails = allForecastStatus.Select(a => new { a.SiteId, a.SiteName }).Distinct();
            foreach (var site in distinctSiteDetails)
            {
                forecastStatuses.Add(new ForecastStatus() { SiteId = site.SiteId, SiteName = site.SiteName, ForecastId = forecastId });
            }

            //Update list with currect forecast
            var currentForecastStatus = allForecastStatus.Where(a => a.ForecastId == forecastId).ToList();
            foreach (var forecastStatus in forecastStatuses)
            {
                var result = currentForecastStatus.Where(c => c.SiteId == forecastStatus.SiteId && c.ForecastId == forecastStatus.ForecastId).ToList();
                if (result.Count > 0)
                {
                    forecastStatus.VersionId = result[0].VersionId;
                    forecastStatus.Version = result[0].Version;
                    forecastStatus.ForecastId = result[0].ForecastId;
                    forecastStatus.OrderWithInMonth = result[0].OrderWithInMonth;
                    forecastStatus.StatusId = result[0].StatusId;
                    forecastStatus.Status = result[0].Status;
                    forecastStatus.Submitter = result[0].Submitter;
                    forecastStatus.DirectorTotalNet = result[0].DirectorTotalNet;
                    forecastStatus.BudgetLabel = result[0].BudgetLabel;
                    forecastStatus.BudgetValueNet = result[0].BudgetValueNet;
                    forecastStatus.LastYearValueNet = result[0].LastYearValueNet;
                    forecastStatus.SubmittedDate = result[0].SubmittedDate;
                    forecastStatus.LastSavedBy = result[0].LastSavedBy;
                    forecastStatus.LastSavedDate = result[0].LastSavedDate;
                } else
                {
                    result = allForecastStatus.Where(c => c.SiteId == forecastStatus.SiteId).ToList();
                    forecastStatus.BudgetValueNet = result[0].BudgetValueNet;
                }
            }

            //Update list with Prior forecast
            //Check if there is any prior forecast data available.
            var priorForecastStatus = allForecastStatus.Where(a => a.ForecastId < forecastId && a.ForecastId != 0).ToList();
            if (priorForecastStatus.Count > 0)
            {
                var priorForecastId = priorForecastStatus.Max(a => a.ForecastId); 
                foreach (var forecastStatus in forecastStatuses)
                {
                    var result = priorForecastStatus.Where(c => c.SiteId == forecastStatus.SiteId && c.ForecastId == priorForecastId).ToList();
                    if (result.Count > 0)
                    {
                        forecastStatus.PriorForecastValue = result[0].DirectorTotalNet;
                    }
                }

            }

            //Total
            forecastStatuses.Add(new ForecastStatus() { 
                SiteId = 0, 
                SiteName = "Total",
                VersionId = 0,
                Version = String.Empty,
                ForecastId = 0,
                BudgetValueNet = forecastStatuses.Select(f => f.BudgetValueNet).Sum(),
                DirectorTotalNet = forecastStatuses.Select(f => f.DirectorTotalNet).Sum(),
                LastYearValueNet = forecastStatuses.Select(f => f.LastYearValueNet).Sum() ,
                OrderWithInMonth = 1,
                PriorForecastValue = forecastStatuses.Where(f => f.PriorForecastValue.HasValue == true).Any() ? forecastStatuses.Select(f => f.PriorForecastValue).Sum() : null,
                StatusId = 0,
                Status = String.Empty
            });




            return forecastStatuses;

        }

        public async Task<IEnumerable<ForecastReviewVM>> GetForecastReview(DateTime month, DealerGroupName dealerGroup)
        {
            List<ForecastReviewVM> forecastReviewVMs = new List<ForecastReviewVM>();
            List<ForecastReviewVM> forecastReviewGroupVMs = new List<ForecastReviewVM>();
            List<ForecastReview> emptyForecastReviews = new List<ForecastReview>();
            var allDepartments = new List<Model.Fcst.Department>();
            var siteTotalRow = new ForecastReviewVM();
            var regionalTotalRow = new ForecastReviewVM();
            var groupTotalRow = new ForecastReviewVM();
            var groupReview = new List<ForecastReviewVM>();

            int[] deptIdsWithMotalbilityUnits = { 1,12};
            int[] deptIdsWithDemoUnits = { 6 };
            int[] deptIdsWithUnits = { 2, 3, 4, 5};
            int[] deptIdsWithHours = { 8};

            var allForecastReviews = await liveForecastDataAccess.GetForecastReview(month, dealerGroup);
            
            var maxForecastId = allForecastReviews.Max(a => a.ForecastId);
            var maxOrderWithinMonth = allForecastReviews.Max(a => a.OrderWithInMonth);

            var forecastLinesUnits = await liveForecastDataAccess.GetForecastLines(null, null, 1, month, dealerGroup); // Units Account ID= 1
            var forecastLinesMotabilityUnits = await liveForecastDataAccess.GetForecastLines(null, null, 33, month, dealerGroup); // MotabilityUnits Account ID= 33
            var forecastLinesDemoUnits = await liveForecastDataAccess.GetForecastLines(null, null, 34, month, dealerGroup); // DemoUnits Account ID= 34
            var forecastLinesHours = await liveForecastDataAccess.GetForecastLines(null, null, 20, month, dealerGroup); // Hours sold Account Id = 20
            var financialLinesUnits = await liveForecastDataAccess.GetFinancialLines(null, null, 1, month, dealerGroup);
            var financialLinesMotabilityUnits = await liveForecastDataAccess.GetFinancialLines(null, null, 33, month, dealerGroup);
            var financialLinesDemoUnits = await liveForecastDataAccess.GetFinancialLines(null, null, 34, month, dealerGroup);
            var financialLinesHours = await liveForecastDataAccess.GetFinancialLines(null, null, 20, month, dealerGroup);

            var forecastLinesServiceDept = await liveForecastDataAccess.GetForecastLines(8, null, null, month, dealerGroup); // Service Dept id = 8
            var financialLinesServiceDept = await liveForecastDataAccess.GetFinancialLines(null, 8, null, month, dealerGroup);
            //var forecastReviewAccountHours = new ForecastReviewAccount();

            //Generate list for each site
            var distinctSiteDetails = allForecastReviews.Select(a => new { a.SiteId, a.SiteName, a.Region }).Distinct();
            foreach (var site in distinctSiteDetails)
            {
                forecastReviewVMs.Add(new ForecastReviewVM() { SiteId = site.SiteId, SiteName = site.SiteName, Region = site.Region, IsSiteTotal = false, IsRegional = false, IsRegionalTotal = false, forecastReviewDepartments = new List<ForecastReviewDepartment>() });
            }

            
            //---Since no records are available, creating blank records.------
            allDepartments = await liveForecastDataAccess.AllDepartments(dealerGroup);
            if (maxForecastId == 0)
            {
                foreach (var review in allForecastReviews)
                {
                    foreach (var dep in allDepartments)
                    {
                        emptyForecastReviews.Add(new ForecastReview() { DepartmentId = dep.Id, DepartmentName = dep.Name, SiteId = review.SiteId, SiteName = review.SiteName });
                    }
                }

                allForecastReviews = emptyForecastReviews;
            }
            //---------------------------------------------------------------//


            //Update list with latest forecast (say Final)
            var currentForecastReview = allForecastReviews.Where(a => a.ForecastId == maxForecastId).ToList();
            foreach (var forecastReview in forecastReviewVMs)
            {
                //Each Department
                var currentForecastReviewDepts = currentForecastReview.Where(c => c.SiteId == forecastReview.SiteId && c.ForecastId == maxForecastId).ToList();
                foreach (var deptRow in allDepartments)
                {
                    var forecastReviewDepartment = new ForecastReviewDepartment();
                    var forecastReviewAccountUnits = new ForecastReviewAccount();
                    forecastReviewDepartment.DepartmentName = deptRow.Name;
                    forecastReviewDepartment.DepartmentId = deptRow.Id;
                    forecastReviewDepartment.forecastReviewAccounts = new List<ForecastReviewAccount>();
                    if (deptIdsWithMotalbilityUnits.Contains(deptRow.Id))
                    {
                        forecastReviewAccountUnits = new ForecastReviewAccount();
                        forecastReviewAccountUnits.AccountName = "Units";
                        forecastReviewAccountUnits.AccountId = 1;

                        forecastReviewAccountUnits.WK1Label = "F1";
                        forecastReviewAccountUnits.WK2Label = "F2";
                        forecastReviewAccountUnits.FinalLabel = "Final";

                        var wk1Units = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 1);
                        var wk1MotabilityUnits = forecastLinesMotabilityUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 1);
                        var wk2Units = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 2);
                        var wk2MotabilityUnits = forecastLinesMotabilityUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 2);
                        var finalUnits = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 3);
                        var finalMotabilityUnits = forecastLinesMotabilityUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 3);

                        var financialLines = financialLinesUnits.Where(f => f.SiteId == forecastReview.SiteId && f.DepartmentId == deptRow.Id && f.AccountId == 1).ToList();
                        var financialLinesMotability = financialLinesMotabilityUnits.Where(f => f.SiteId == forecastReview.SiteId && f.DepartmentId == deptRow.Id && f.AccountId == 33).ToList();
                        var budget = GetBudget(financialLines);
                        var lastYear = GetLastYear(financialLines);
                        var budgetMotability = GetBudget(financialLinesMotability);
                        var lastYearMotability = GetLastYear(financialLinesMotability);


                        forecastReviewAccountUnits.Wk1Value = wk1Units.Any() ? wk1Units.Select(a => a.Value).Sum() : 0;
                        forecastReviewAccountUnits.Wk1Value += wk1MotabilityUnits.Any() ? wk1MotabilityUnits.Select(a => a.Value).Sum() : 0;
                        
                        forecastReviewAccountUnits.Wk2Value = wk2Units.Any() ? wk2Units.Select(a => a.Value).Sum() : 0;
                        forecastReviewAccountUnits.Wk2Value += wk2MotabilityUnits.Any() ? wk2MotabilityUnits.Select(a => a.Value).Sum() : 0;

                        forecastReviewAccountUnits.FinalValue = finalUnits.Any() ? finalUnits.Select(a => a.Value).Sum() : 0;
                        forecastReviewAccountUnits.FinalValue += finalMotabilityUnits.Any() ? finalMotabilityUnits.Select(a => a.Value).Sum() : 0;

                        forecastReviewAccountUnits.BudgetVarianceValue = GenerateBudgetVarianceValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth, budget);
                        forecastReviewAccountUnits.BudgetVarianceValue += GenerateBudgetVarianceValue(wk1MotabilityUnits, wk2MotabilityUnits, finalMotabilityUnits, maxOrderWithinMonth, budgetMotability);

                        forecastReviewAccountUnits.LastYearVarianceValue = GenerateLastYearVarianceValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth, lastYear);
                        forecastReviewAccountUnits.LastYearVarianceValue += GenerateLastYearVarianceValue(wk1MotabilityUnits, wk2MotabilityUnits, finalMotabilityUnits, maxOrderWithinMonth, lastYearMotability);
                        
                        forecastReviewAccountUnits.LastForecastVarianceValue = GeneratePriorForecastLinesValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth);
                        forecastReviewAccountUnits.LastForecastVarianceValue += GeneratePriorForecastLinesValue(wk1MotabilityUnits, wk2MotabilityUnits, finalMotabilityUnits, maxOrderWithinMonth);

                        forecastReviewAccountUnits.BudgetValue = budget;
                        forecastReviewAccountUnits.LastForecastValue = GeneratePriorForecastValue(forecastReviewAccountUnits.Wk1Value, forecastReviewAccountUnits.Wk2Value, maxOrderWithinMonth);
                        forecastReviewAccountUnits.LastYearValue = lastYear;

                        forecastReviewDepartment.forecastReviewAccounts.Add(forecastReviewAccountUnits);
                    }
                    if (deptIdsWithDemoUnits.Contains(deptRow.Id))
                    {
                        forecastReviewAccountUnits = new ForecastReviewAccount();
                        forecastReviewAccountUnits.AccountName = "Units";
                        forecastReviewAccountUnits.AccountId = 1;

                        forecastReviewAccountUnits.WK1Label = "F1";
                        forecastReviewAccountUnits.WK2Label = "F2";
                        forecastReviewAccountUnits.FinalLabel = "Final";

                        var wk1Units = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 1);
                        var wk1DemoUnits = forecastLinesDemoUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 1);
                        var wk2Units = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 2);
                        var wk2DemoUnits = forecastLinesDemoUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 2);
                        var finalUnits = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 3);
                        var finalDemoUnits = forecastLinesDemoUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 3);

                        var financialLines = financialLinesUnits.Where(f => f.SiteId == forecastReview.SiteId && f.DepartmentId == deptRow.Id && f.AccountId == 1).ToList();
                        var financialLinesMotability = financialLinesMotabilityUnits.Where(f => f.SiteId == forecastReview.SiteId && f.DepartmentId == deptRow.Id && f.AccountId == 1).ToList();
                        var budget = GetBudget(financialLines);
                        var lastYear = GetLastYear(financialLines);
                        var budgetMotability = GetBudget(financialLinesMotability);
                        var lastYearMotability = GetLastYear(financialLinesMotability);


                        forecastReviewAccountUnits.Wk1Value = wk1Units.Any() ? wk1Units.Select(a => a.Value).Sum() : 0;
                        forecastReviewAccountUnits.Wk1Value += wk1DemoUnits.Any() ? wk1DemoUnits.Select(a => a.Value).Sum() : 0;

                        forecastReviewAccountUnits.Wk2Value = wk2Units.Any() ? wk2Units.Select(a => a.Value).Sum() : 0;
                        forecastReviewAccountUnits.Wk2Value += wk2DemoUnits.Any() ? wk2DemoUnits.Select(a => a.Value).Sum() : 0;

                        forecastReviewAccountUnits.FinalValue = finalUnits.Any() ? finalUnits.Select(a => a.Value).Sum() : 0;
                        forecastReviewAccountUnits.FinalValue += finalDemoUnits.Any() ? finalDemoUnits.Select(a => a.Value).Sum() : 0;

                        forecastReviewAccountUnits.BudgetVarianceValue = GenerateBudgetVarianceValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth, budget);
                        forecastReviewAccountUnits.BudgetVarianceValue += GenerateBudgetVarianceValue(wk1DemoUnits, wk2DemoUnits, finalDemoUnits, maxOrderWithinMonth, budgetMotability);

                        forecastReviewAccountUnits.LastYearVarianceValue = GenerateLastYearVarianceValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth, lastYear);
                        forecastReviewAccountUnits.LastYearVarianceValue += GenerateLastYearVarianceValue(wk1DemoUnits, wk2DemoUnits, finalDemoUnits, maxOrderWithinMonth, lastYearMotability);

                        forecastReviewAccountUnits.LastForecastVarianceValue = GeneratePriorForecastLinesValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth);
                        forecastReviewAccountUnits.LastForecastVarianceValue += GeneratePriorForecastLinesValue(wk1DemoUnits, wk2DemoUnits, finalDemoUnits, maxOrderWithinMonth);

                        forecastReviewAccountUnits.BudgetValue = budget;
                        forecastReviewAccountUnits.LastForecastValue = GeneratePriorForecastValue(forecastReviewAccountUnits.Wk1Value, forecastReviewAccountUnits.Wk2Value, maxOrderWithinMonth);
                        forecastReviewAccountUnits.LastYearValue = lastYear;

                        forecastReviewDepartment.forecastReviewAccounts.Add(forecastReviewAccountUnits);
                    }
                    else if (deptIdsWithUnits.Contains(deptRow.Id))
                    {
                        forecastReviewAccountUnits = new ForecastReviewAccount();
                        forecastReviewAccountUnits.AccountName = "Units";
                        forecastReviewAccountUnits.AccountId = 1;

                        forecastReviewAccountUnits.WK1Label = "F1";
                        forecastReviewAccountUnits.WK2Label = "F2";
                        forecastReviewAccountUnits.FinalLabel = "Final";

                        var wk1Units = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 1);
                        var wk2Units = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 2);
                        var finalUnits = forecastLinesUnits.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 3);

                        var financialLines = financialLinesUnits.Where(f => f.SiteId == forecastReview.SiteId && f.DepartmentId == deptRow.Id && f.AccountId == 1).ToList();
                        var budget = GetBudget(financialLines);
                        var lastYear = GetLastYear(financialLines);


                        forecastReviewAccountUnits.Wk1Value = wk1Units.Any() ? wk1Units.Select(a => a.Value).Sum() : forecastReviewAccountUnits.Wk1Value;
                        forecastReviewAccountUnits.Wk2Value = wk2Units.Any() ? wk2Units.Select(a => a.Value).Sum() : forecastReviewAccountUnits.Wk2Value;
                        forecastReviewAccountUnits.FinalValue = finalUnits.Any() ? finalUnits.Select(a => a.Value).Sum() : forecastReviewAccountUnits.FinalValue;
                        forecastReviewAccountUnits.BudgetVarianceValue =  GenerateBudgetVarianceValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth, budget);
                        forecastReviewAccountUnits.LastYearVarianceValue = GenerateLastYearVarianceValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth, lastYear);
                        forecastReviewAccountUnits.LastForecastVarianceValue = GeneratePriorForecastLinesValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth);
                        forecastReviewAccountUnits.BudgetValue = budget;
                        forecastReviewAccountUnits.LastForecastValue = GeneratePriorForecastValue(wk1Units, wk2Units, maxOrderWithinMonth);
                        forecastReviewAccountUnits.LastYearValue = lastYear;

                        forecastReviewDepartment.forecastReviewAccounts.Add(forecastReviewAccountUnits);
                    }

                    else if (deptIdsWithHours.Contains(deptRow.Id))
                    {
                        forecastReviewAccountUnits = new ForecastReviewAccount();
                        forecastReviewAccountUnits.AccountName = "Hours";
                        forecastReviewAccountUnits.AccountId = 20;

                        forecastReviewAccountUnits.WK1Label = "F1";
                        forecastReviewAccountUnits.WK2Label = "F2";
                        forecastReviewAccountUnits.FinalLabel = "Final";

                        var wk1Units = forecastLinesHours.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 1);
                        var wk2Units = forecastLinesHours.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 2);
                        var finalUnits = forecastLinesHours.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 3);

                        var financialLines = financialLinesHours.Where(f => f.SiteId == forecastReview.SiteId && f.DepartmentId == deptRow.Id && f.AccountId == 20).ToList();
                        var budget = GetBudget(financialLines);
                        var lastYear = GetLastYear(financialLines);

                        forecastReviewAccountUnits.Wk1Value = wk1Units.Any() ? wk1Units.Select(a => a.Value).Sum() : forecastReviewAccountUnits.Wk1Value;
                        forecastReviewAccountUnits.Wk2Value = wk2Units.Any() ? wk2Units.Select(a => a.Value).Sum() : forecastReviewAccountUnits.Wk2Value;
                        forecastReviewAccountUnits.FinalValue = finalUnits.Any() ? finalUnits.Select(a => a.Value).Sum() : forecastReviewAccountUnits.FinalValue;
                        forecastReviewAccountUnits.BudgetVarianceValue = GenerateBudgetVarianceValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth, budget);
                        forecastReviewAccountUnits.LastYearVarianceValue = GenerateLastYearVarianceValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth, lastYear);
                        forecastReviewAccountUnits.LastForecastVarianceValue = GeneratePriorForecastLinesValue(wk1Units, wk2Units, finalUnits, maxOrderWithinMonth);
                        forecastReviewAccountUnits.BudgetValue = budget;
                        forecastReviewAccountUnits.LastForecastValue = GeneratePriorForecastValue(wk1Units, wk2Units, maxOrderWithinMonth);
                        forecastReviewAccountUnits.LastYearValue = lastYear;

                        forecastReviewDepartment.forecastReviewAccounts.Add(forecastReviewAccountUnits);
                    }

                    //--GPU-------Only For new, new fakenham, LBDM, VG Fleet, VG NA, Used-----------------------------------------------------------//
                    int[] validGPUDeptIds = { 1, 12, 2, 3, 4, 5, 6 };
                    if (validGPUDeptIds.Contains(deptRow.Id))
                    {
                        var forecastReviewAccountGPU = GenerateForecastReviewAccountGPU(allForecastReviews, currentForecastReviewDepts, forecastReview, deptRow, maxOrderWithinMonth, forecastReviewAccountUnits);
                        forecastReviewDepartment.forecastReviewAccounts.Add(forecastReviewAccountGPU);
                    }

                    //--RecoveryRate--Only for Service----------------------------------------------------------------//
                    int[] validRRDeptIds = { 8 };
                    if (validRRDeptIds.Contains(deptRow.Id))
                    {
                        var forecastReviewAccountRR = GenerateForecastReviewAccountRecoveryRate(forecastReview, deptRow, maxOrderWithinMonth, forecastLinesServiceDept, financialLinesServiceDept);
                        forecastReviewDepartment.forecastReviewAccounts.Add(forecastReviewAccountRR);
                    }

                    //--Profit------------------------------------------------------------------//
                    var forecastReviewAccountProfit = GenerateForecastReviewAccountProfit(allForecastReviews, currentForecastReviewDepts, forecastReview, deptRow, maxOrderWithinMonth);
                    forecastReviewDepartment.forecastReviewAccounts.Add(forecastReviewAccountProfit);




                    //Moving 'New Fakenham department in front'
                    if (forecastReviewDepartment.DepartmentId == 12)
                    {
                        forecastReview.forecastReviewDepartments.Insert(1, forecastReviewDepartment);
                    }
                    else
                    {
                        forecastReview.forecastReviewDepartments.Add(forecastReviewDepartment);
                    }
                }
            }

            //Add Total Column
            foreach (var forecastReview in forecastReviewVMs)
            {
                var totalDepartment = new ForecastReviewDepartment();
                totalDepartment.DepartmentName = "Total";
                totalDepartment.DepartmentId = 0;
                totalDepartment.forecastReviewAccounts = new List<ForecastReviewAccount>();

                var totalForecastReviewAccount = new ForecastReviewAccount();
                totalForecastReviewAccount.AccountName = "Profit";
                totalForecastReviewAccount.AccountId = 0;

                totalForecastReviewAccount.WK1Label = "F1";
                totalForecastReviewAccount.WK2Label = "F2";
                totalForecastReviewAccount.FinalLabel = "Final";

                totalForecastReviewAccount.Wk1Value = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.Wk1Value))).Sum();
                totalForecastReviewAccount.Wk2Value = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.Wk2Value))).Sum();
                totalForecastReviewAccount.FinalValue = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.FinalValue))).Sum();
                totalForecastReviewAccount.BudgetVarianceValue = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.BudgetVarianceValue))).Sum();
                totalForecastReviewAccount.LastYearVarianceValue = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.LastYearVarianceValue))).Sum();
                totalForecastReviewAccount.LastForecastVarianceValue = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.LastForecastVarianceValue))).Sum();

                totalForecastReviewAccount.BudgetValue = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.BudgetValue))).Sum();
                totalForecastReviewAccount.LastForecastValue = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.LastForecastValue))).Sum();
                totalForecastReviewAccount.LastYearValue = forecastReviewVMs.Where(f => f.SiteId == forecastReview.SiteId).SelectMany(f => f.forecastReviewDepartments.SelectMany(d => d.forecastReviewAccounts.Where(a => a.AccountId == 0).Select(a => a.LastYearValue))).Sum();

                totalDepartment.forecastReviewAccounts.Add(totalForecastReviewAccount);
                forecastReview.forecastReviewDepartments.Insert(0,totalDepartment);
            }




            //Add Total Row
            siteTotalRow.SiteName = "Total";
            siteTotalRow.SiteId = 0;
            siteTotalRow.IsSiteTotal = true;
            siteTotalRow.IsRegionalTotal = false;
            siteTotalRow.IsRegional = false;
            siteTotalRow.forecastReviewDepartments = new List<ForecastReviewDepartment>();
            var listofDepartments = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments).Select(d => new { d.DepartmentId, d.DepartmentName }).Distinct().ToList();
            
            foreach (var department in listofDepartments)
            {
                var siteTotalRowDepartment = new ForecastReviewDepartment();
                siteTotalRowDepartment.DepartmentName = department.DepartmentName;
                siteTotalRowDepartment.DepartmentId = department.DepartmentId;
                siteTotalRowDepartment.forecastReviewAccounts = new List<ForecastReviewAccount>();
                var departmentAccounts = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments).Where(s => s.DepartmentId == department.DepartmentId).SelectMany(d => d.forecastReviewAccounts).Select(a => new { a.AccountId, a.AccountName } ).Distinct();
                foreach (var account in departmentAccounts)
                {
                    var siteTotalRowDepartmentAccount = new ForecastReviewAccount();
                    siteTotalRowDepartmentAccount.AccountId = account.AccountId;
                    siteTotalRowDepartmentAccount.AccountName = account.AccountName;
                    siteTotalRowDepartmentAccount.Wk1Value = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.Wk1Value)).Sum();
                    siteTotalRowDepartmentAccount.Wk2Value = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.Wk2Value)).Sum();
                    siteTotalRowDepartmentAccount.FinalValue = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.FinalValue)).Sum();
                    siteTotalRowDepartmentAccount.BudgetVarianceValue = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.BudgetVarianceValue)).Sum();
                    siteTotalRowDepartmentAccount.LastYearVarianceValue = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.LastYearVarianceValue)).Sum();
                    siteTotalRowDepartmentAccount.LastForecastVarianceValue = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.LastForecastVarianceValue)).Sum();
                    siteTotalRowDepartmentAccount.BudgetValue = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.BudgetValue)).Sum();
                    siteTotalRowDepartmentAccount.LastYearValue = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.LastYearValue)).Sum();
                    siteTotalRowDepartmentAccount.LastForecastValue = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.LastForecastValue)).Sum();
                    siteTotalRowDepartment.forecastReviewAccounts.Add(siteTotalRowDepartmentAccount);
                }
                siteTotalRow.forecastReviewDepartments.Add(siteTotalRowDepartment);
            }

            forecastReviewVMs.Add(siteTotalRow);


            //Add Group Row
            var forecastReviewVMsByRegion= forecastReviewVMs.ToLookup(f => f.Region);
            foreach (var region in forecastReviewVMsByRegion.Where(f => f.Key != null))
            {
                var regionalRow = new ForecastReviewVM() { SiteId = 0, SiteName = region.Key, Region = String.Empty, IsSiteTotal = false, IsRegional = true, IsRegionalTotal = false, forecastReviewDepartments = new List<ForecastReviewDepartment>() };

                foreach (var department in listofDepartments)
                {
                    var regionalRowDepartment = new ForecastReviewDepartment();
                    regionalRowDepartment.DepartmentName = department.DepartmentName;
                    regionalRowDepartment.DepartmentId = department.DepartmentId;
                    regionalRowDepartment.forecastReviewAccounts = new List<ForecastReviewAccount>();
                    var departmentAccounts = region.SelectMany(f => f.forecastReviewDepartments).Where(s => s.DepartmentId == department.DepartmentId).SelectMany(d => d.forecastReviewAccounts).Select(a => new { a.AccountId, a.AccountName }).Distinct();
                    foreach (var account in departmentAccounts)
                    {
                        var regionalRowDepartmentAccount = new ForecastReviewAccount();
                        regionalRowDepartmentAccount.AccountId = account.AccountId;
                        regionalRowDepartmentAccount.AccountName = account.AccountName;
                        regionalRowDepartmentAccount.Wk1Value = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.Wk1Value)).Sum();
                        regionalRowDepartmentAccount.Wk2Value = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.Wk2Value)).Sum();
                        regionalRowDepartmentAccount.FinalValue = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.FinalValue)).Sum();
                        regionalRowDepartmentAccount.BudgetVarianceValue = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.BudgetVarianceValue)).Sum();
                        regionalRowDepartmentAccount.LastYearVarianceValue = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.LastYearVarianceValue)).Sum();
                        regionalRowDepartmentAccount.LastForecastVarianceValue = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.LastForecastVarianceValue)).Sum();
                        regionalRowDepartmentAccount.BudgetValue = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.BudgetValue)).Sum();
                        regionalRowDepartmentAccount.LastYearValue = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.LastYearValue)).Sum();
                        regionalRowDepartmentAccount.LastForecastValue = forecastReviewVMs.Where(f => f.IsRegional == false && f.Region == region.Key).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId).Select(f => f.LastForecastValue)).Sum();
                        regionalRowDepartment.forecastReviewAccounts.Add(regionalRowDepartmentAccount);
                    }
                    regionalRow.forecastReviewDepartments.Add(regionalRowDepartment);
                }

                forecastReviewGroupVMs.Add(regionalRow);
            }


            //Add Regional Total Row 

            regionalTotalRow.SiteName = "Total";
            regionalTotalRow.SiteId = 0;
            regionalTotalRow.IsSiteTotal = false;
            regionalTotalRow.IsRegionalTotal = true;
            regionalTotalRow.IsRegional = true;
            regionalTotalRow.forecastReviewDepartments = new List<ForecastReviewDepartment>();
            //var regionallistofDepartments = forecastReviewVMs.SelectMany(f => f.forecastReviewDepartments).Select(d => new { d.DepartmentId, d.DepartmentName }).Distinct().ToList();

            foreach (var department in listofDepartments)
            {
                var regionalTotalRowDepartment = new ForecastReviewDepartment();
                regionalTotalRowDepartment.DepartmentName = department.DepartmentName;
                regionalTotalRowDepartment.DepartmentId = department.DepartmentId;
                regionalTotalRowDepartment.forecastReviewAccounts = new List<ForecastReviewAccount>();
                var departmentAccounts = forecastReviewGroupVMs.Where(f => f.IsRegional == true).SelectMany(f => f.forecastReviewDepartments).Where(s => s.DepartmentId == department.DepartmentId).SelectMany(d => d.forecastReviewAccounts).Select(a => new { a.AccountId, a.AccountName }).Distinct().ToList();
                foreach (var account in departmentAccounts)
                {
                    var regionalRowData = forecastReviewGroupVMs.Where(f => f.IsRegional == true).SelectMany(f => f.forecastReviewDepartments.Where(f => f.DepartmentId == department.DepartmentId && f.DepartmentName == department.DepartmentName).SelectMany(f => f.forecastReviewAccounts).Where(f => f.AccountId == account.AccountId && f.AccountName == account.AccountName)).ToList();
                    var regionalTotalRowDepartmentAccount = new ForecastReviewAccount();
                    regionalTotalRowDepartmentAccount.AccountId = account.AccountId;
                    regionalTotalRowDepartmentAccount.AccountName = account.AccountName;
                    regionalTotalRowDepartmentAccount.Wk1Value = regionalRowData.Select(f => f.Wk1Value).Sum();
                    regionalTotalRowDepartmentAccount.Wk2Value = regionalRowData.Select(f => f.Wk2Value).Sum();
                    regionalTotalRowDepartmentAccount.FinalValue = regionalRowData.Select(f => f.FinalValue).Sum();
                    regionalTotalRowDepartmentAccount.BudgetVarianceValue = regionalRowData.Select(f => f.BudgetVarianceValue).Sum();
                    regionalTotalRowDepartmentAccount.LastYearVarianceValue = regionalRowData.Select(f => f.LastYearVarianceValue).Sum();
                    regionalTotalRowDepartmentAccount.LastForecastVarianceValue = regionalRowData.Select(f => f.LastForecastVarianceValue).Sum();
                    regionalTotalRowDepartmentAccount.BudgetValue = regionalRowData.Select(f => f.BudgetValue).Sum();
                    regionalTotalRowDepartmentAccount.LastYearValue = regionalRowData.Select(f => f.LastYearValue).Sum();
                    regionalTotalRowDepartmentAccount.LastForecastValue = regionalRowData.Select(f => f.LastForecastValue).Sum();
                    regionalTotalRowDepartment.forecastReviewAccounts.Add(regionalTotalRowDepartmentAccount);
                }
                regionalTotalRow.forecastReviewDepartments.Add(regionalTotalRowDepartment);
            }

            forecastReviewGroupVMs.Add(regionalTotalRow);


            forecastReviewVMs.AddRange(forecastReviewGroupVMs);


            return forecastReviewVMs;
        }

        private ForecastReviewAccount GenerateForecastReviewAccountProfit(IEnumerable<ForecastReview> allForecastReviews, IEnumerable<ForecastReview> currentForecastReviewDepts, ForecastReviewVM forecastReview, Model.Fcst.Department deptRow, int maxOrderWithinMonth)
        {
            var forecastReviewAccount = new ForecastReviewAccount();
            forecastReviewAccount.AccountName = "Profit";
            forecastReviewAccount.AccountId = 0;

            forecastReviewAccount.WK1Label = "F1";
            forecastReviewAccount.WK2Label = "F2";
            forecastReviewAccount.FinalLabel = "Final";

            var wk1 = allForecastReviews.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithInMonth == 1);
            var wk2 = allForecastReviews.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithInMonth == 2);
            var final = allForecastReviews.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithInMonth == 3);
            var valueRow = currentForecastReviewDepts.Where(c => c.DepartmentId == deptRow.Id);
            forecastReviewAccount.Wk1Value = wk1.Any() && wk1.First().DirectorTotalNet.HasValue ? wk1.First().DirectorTotalNet.Value : forecastReviewAccount.Wk1Value;
            forecastReviewAccount.Wk2Value = wk2.Any() && wk2.First().DirectorTotalNet.HasValue ? wk2.First().DirectorTotalNet.Value : forecastReviewAccount.Wk2Value;
            forecastReviewAccount.FinalValue = final.Any() && final.First().DirectorTotalNet.HasValue ? final.First().DirectorTotalNet.Value : forecastReviewAccount.FinalValue;

            forecastReviewAccount.BudgetVarianceValue = valueRow.Any() && valueRow.First().DirectorTotalNet.HasValue ? valueRow.First().DirectorTotalNet.Value - valueRow.First().BudgetValueNet : 0;
            forecastReviewAccount.LastYearVarianceValue = valueRow.Any() && valueRow.First().DirectorTotalNet.HasValue ? valueRow.First().DirectorTotalNet.Value - valueRow.First().LastYearValueNet : 0;
            forecastReviewAccount.LastForecastVarianceValue = GeneratePriorForecastVarianceValueNet(wk1, wk2, final, maxOrderWithinMonth);

            forecastReviewAccount.BudgetValue = valueRow.Any() ? valueRow.First().BudgetValueNet : 0;
            forecastReviewAccount.LastForecastValue = GeneratePriorForecastValueNet(wk1, wk2, maxOrderWithinMonth);
            forecastReviewAccount.LastYearValue = valueRow.Any() ? valueRow.First().LastYearValueNet : 0;

            return forecastReviewAccount;
        }

        private ForecastReviewAccount GenerateForecastReviewAccountGPU(IEnumerable<ForecastReview> allForecastReviews, IEnumerable<ForecastReview> currentForecastReviewDepts, ForecastReviewVM forecastReview, Model.Fcst.Department deptRow, int maxOrderWithinMonth, ForecastReviewAccount forecastReviewAccountUnits)
        {
            var forecastReviewAccount = new ForecastReviewAccount();
            forecastReviewAccount.AccountName = "GPU";
            forecastReviewAccount.AccountId = -1; //So that it can be skipped from Total Calculation

            forecastReviewAccount.WK1Label = "F1";
            forecastReviewAccount.WK2Label = "F2";
            forecastReviewAccount.FinalLabel = "Final";

            var wk1 = allForecastReviews.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithInMonth == 1);
            var wk2 = allForecastReviews.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithInMonth == 2);
            var final = allForecastReviews.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithInMonth == 3);
            var valueRow = currentForecastReviewDepts.Where(c => c.DepartmentId == deptRow.Id);
            forecastReviewAccount.Wk1Value = wk1.Any() && wk1.First().DirectorTotalGross.HasValue ? wk1.First().DirectorTotalGross.Value : forecastReviewAccount.Wk1Value;
            forecastReviewAccount.Wk2Value = wk2.Any() && wk2.First().DirectorTotalGross.HasValue ? wk2.First().DirectorTotalGross.Value : forecastReviewAccount.Wk2Value;
            forecastReviewAccount.FinalValue = final.Any() && final.First().DirectorTotalGross.HasValue ? final.First().DirectorTotalGross.Value : forecastReviewAccount.FinalValue;

            forecastReviewAccount.BudgetVarianceValue = valueRow.Any() && valueRow.First().DirectorTotalGross.HasValue ? valueRow.First().DirectorTotalGross.Value - valueRow.First().BudgetValueGross : 0;
            forecastReviewAccount.LastYearVarianceValue = valueRow.Any() && valueRow.First().DirectorTotalGross.HasValue ? valueRow.First().DirectorTotalGross.Value - valueRow.First().LastYearValueGross : 0;
            forecastReviewAccount.LastForecastVarianceValue = GeneratePriorForecastVarianceValueGross(wk1, wk2, final, maxOrderWithinMonth);

            forecastReviewAccount.BudgetValue = valueRow.Any() ? valueRow.First().BudgetValueGross : 0;
            forecastReviewAccount.LastForecastValue = GeneratePriorForecastValueGross(wk1, wk2, maxOrderWithinMonth);
            forecastReviewAccount.LastYearValue = valueRow.Any() ? valueRow.First().LastYearValueGross : 0;

            //Divide Value by Total Units
            forecastReviewAccount.Wk1Value = HelperService.DivideByDecimal(forecastReviewAccount.Wk1Value, forecastReviewAccountUnits.Wk1Value);
            forecastReviewAccount.Wk2Value = HelperService.DivideByDecimal(forecastReviewAccount.Wk2Value, forecastReviewAccountUnits.Wk2Value);
            forecastReviewAccount.FinalValue = HelperService.DivideByDecimal(forecastReviewAccount.FinalValue, forecastReviewAccountUnits.FinalValue);

            forecastReviewAccount.BudgetValue = HelperService.DivideByDecimal(forecastReviewAccount.BudgetValue, forecastReviewAccountUnits.BudgetValue);
            forecastReviewAccount.BudgetVarianceValue = GenerateVarianceValue(forecastReviewAccount.Wk1Value, forecastReviewAccount.Wk2Value, forecastReviewAccount.FinalValue, maxOrderWithinMonth, forecastReviewAccount.BudgetValue);
            
            forecastReviewAccount.LastForecastValue = HelperService.DivideByDecimal(forecastReviewAccount.LastForecastValue, forecastReviewAccountUnits.LastForecastValue);
            forecastReviewAccount.LastForecastVarianceValue = GenerateVarianceValue(forecastReviewAccount.Wk1Value, forecastReviewAccount.Wk2Value, forecastReviewAccount.FinalValue, maxOrderWithinMonth, forecastReviewAccount.LastForecastValue);


            forecastReviewAccount.LastYearValue = HelperService.DivideByDecimal(forecastReviewAccount.LastYearValue, forecastReviewAccountUnits.LastYearValue);
            forecastReviewAccount.LastYearVarianceValue = HelperService.DivideByDecimal(forecastReviewAccount.LastYearVarianceValue, forecastReviewAccountUnits.LastYearVarianceValue);



            return forecastReviewAccount;
        }

        private ForecastReviewAccount GenerateForecastReviewAccountRecoveryRate(ForecastReviewVM forecastReview, Model.Fcst.Department deptRow, int maxOrderWithinMonth, IEnumerable<ForecastLineVM> forecastLinesServiceDept, IEnumerable<FinancialLinesVM> financialLinesServiceDept)
        {
            var forecastReviewAccount = new ForecastReviewAccount();
            forecastReviewAccount.AccountName = "RR";
            forecastReviewAccount.AccountId = -1; //So that it can be skipped from Total Calculation

            forecastReviewAccount.WK1Label = "F1";
            forecastReviewAccount.WK2Label = "F2";
            forecastReviewAccount.FinalLabel = "Final";

            //Hours Sold
            string[] vals = { "BroughtIn", "DoneInMonth", "SiteForecast", "DirectorForecast"};
            decimal DirTotalHours1;
            Decimal.TryParse(forecastLinesServiceDept.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 1 && a.AccountId == 20 && vals.Contains(a.DoneStatusTypeName)).Sum(x => x.Value).ToString(), out DirTotalHours1);

            decimal DirTotalHours2;
            Decimal.TryParse(forecastLinesServiceDept.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 2 && a.AccountId == 20 && vals.Contains(a.DoneStatusTypeName)).Sum(x => x.Value).ToString(), out DirTotalHours2);

            decimal DirTotalHours3;
            Decimal.TryParse(forecastLinesServiceDept.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 3 && a.AccountId == 20 && vals.Contains(a.DoneStatusTypeName)).Sum(x => x.Value).ToString(), out DirTotalHours3);

            var financialLinesHours = financialLinesServiceDept.Where(f => f.SiteId == forecastReview.SiteId && f.DepartmentId == deptRow.Id && f.AccountId == 20).ToList();
            var forecastActualsHours = new List<ForecastActual>();
            var budgetHours = GetBudget(financialLinesHours);
            var priorForecastValueHours = 0;
            switch (maxOrderWithinMonth)
            {
                case 1:
                    priorForecastValueHours = 0;
                    break;
                case 2:
                    priorForecastValueHours = (int)(DirTotalHours1);
                    break;
                case 3:
                    priorForecastValueHours = (int)(DirTotalHours2);
                    break;
                default:
                    priorForecastValueHours = 0;
                    break;
            }
            var lastYearHours = GetLastYear(financialLinesHours);

            //Labour Sales
            decimal DirTotalLabour1;
            Decimal.TryParse(forecastLinesServiceDept.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 1 && a.AccountId == 21 && vals.Contains(a.DoneStatusTypeName)).Sum(x => x.Value).ToString(), out DirTotalLabour1);

            decimal DirTotalLabour2;
            Decimal.TryParse(forecastLinesServiceDept.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 2 && a.AccountId == 21 && vals.Contains(a.DoneStatusTypeName)).Sum(x => x.Value).ToString(), out DirTotalLabour2);

            decimal DirTotalLabour3;
            Decimal.TryParse(forecastLinesServiceDept.Where(a => a.SiteId == forecastReview.SiteId && a.DepartmentId == deptRow.Id && a.OrderWithinMonth == 3 && a.AccountId == 21 && vals.Contains(a.DoneStatusTypeName)).Sum(x => x.Value).ToString(), out DirTotalLabour3);

            var financialLinesLabour = financialLinesServiceDept.Where(f => f.SiteId == forecastReview.SiteId && f.DepartmentId == deptRow.Id && f.AccountId == 21).ToList();
            var forecastActualsLabour = new List<ForecastActual>();
            var budgetLabour = GetBudget(financialLinesLabour);
            var priorForecastValueLabour = 0;
            switch (maxOrderWithinMonth)
            {
                case 1:
                    priorForecastValueLabour = 0;
                    break;
                case 2:
                    priorForecastValueLabour = (int)(DirTotalLabour1);
                    break;
                case 3:
                    priorForecastValueLabour = (int)(DirTotalLabour2);
                    break;
                default:
                    priorForecastValueLabour = 0;
                    break;
            }
            var lastYearLabour = GetLastYear(financialLinesHours);



            //PerHour
            decimal DirTotaPerHour1 = HelperService.DivideByDecimal(DirTotalLabour1, DirTotalHours1);
            decimal DirTotaPerHour2 = HelperService.DivideByDecimal(DirTotalLabour2, DirTotalHours2);
            decimal DirTotaPerHour3 = HelperService.DivideByDecimal(DirTotalLabour3, DirTotalHours3);
            decimal BudgetPerHour = HelperService.DivideByDecimal(budgetLabour, budgetHours);
            decimal LastYearForecastValuePerHour = HelperService.DivideByDecimal(lastYearLabour, lastYearHours);
            decimal priorForecastValuePerHour = HelperService.DivideByDecimal(priorForecastValueLabour, priorForecastValueHours);

            forecastReviewAccount.Wk1Value = DirTotaPerHour1;
            forecastReviewAccount.Wk2Value = DirTotaPerHour2;
            forecastReviewAccount.FinalValue = DirTotaPerHour3;

            forecastReviewAccount.BudgetValue = BudgetPerHour;
            forecastReviewAccount.LastForecastValue = priorForecastValuePerHour;
            forecastReviewAccount.LastYearValue = LastYearForecastValuePerHour;

            var BudgetValueForVariance = 0;
            var LastYearVarianceValueForVariance = 0;
            var LastForecastVarianceValueForVariance = 0;
            switch (maxOrderWithinMonth)
            {
                case 1:
                    BudgetValueForVariance = (int)(DirTotaPerHour1 - BudgetPerHour);
                    LastYearVarianceValueForVariance = (int)(DirTotaPerHour1 - LastYearForecastValuePerHour);
                    LastForecastVarianceValueForVariance = 0;
                    break;
                case 2:
                    BudgetValueForVariance = (int)(DirTotaPerHour2 - BudgetPerHour);
                    LastYearVarianceValueForVariance = (int)(DirTotaPerHour2 - LastYearForecastValuePerHour);
                    LastForecastVarianceValueForVariance = (int)(DirTotaPerHour2 - priorForecastValuePerHour);
                    break;
                case 3:
                    BudgetValueForVariance = (int)(DirTotaPerHour3 - BudgetPerHour);
                    LastYearVarianceValueForVariance = (int)(DirTotaPerHour3 - LastYearForecastValuePerHour);
                    LastForecastVarianceValueForVariance = (int)(DirTotaPerHour3 - priorForecastValuePerHour);
                    break;
                default:
                    BudgetValueForVariance = 0;
                    LastYearVarianceValueForVariance = 0;
                    LastForecastVarianceValueForVariance = 0;
                    break;
            }

            forecastReviewAccount.BudgetVarianceValue = BudgetValueForVariance;
            forecastReviewAccount.LastYearVarianceValue = LastYearVarianceValueForVariance;
            forecastReviewAccount.LastForecastVarianceValue = LastForecastVarianceValueForVariance;

            


            return forecastReviewAccount;
        }

        private decimal GenerateVarianceValue(decimal wk1, decimal wk2, decimal final, int maxOrderWithinMonth, decimal varianceByValue)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return wk1 - varianceByValue;
                case 2:
                    return wk2 - varianceByValue;
                case 3:
                    return final - varianceByValue;
                default:
                    return 0;

            }
        }
        private decimal GenerateBudgetVarianceValue(IEnumerable<ForecastLineVM> wk1, IEnumerable<ForecastLineVM> wk2, IEnumerable<ForecastLineVM> final, int maxOrderWithinMonth, decimal budgetValue)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return CalculateDirectorTotal(wk1) - budgetValue;
                case 2:
                    return CalculateDirectorTotal(wk2) - budgetValue;
                case 3:
                    return CalculateDirectorTotal(final) - budgetValue;
                default:
                    return 0;

            }
        }

        private decimal GenerateLastYearVarianceValue(IEnumerable<ForecastLineVM> wk1, IEnumerable<ForecastLineVM> wk2, IEnumerable<ForecastLineVM> final, int maxOrderWithinMonth, decimal lasYearValue)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return CalculateDirectorTotal(wk1) - lasYearValue;
                case 2:
                    return CalculateDirectorTotal(wk2) - lasYearValue;
                case 3:
                    return CalculateDirectorTotal(final) - lasYearValue;
                default:
                    return 0;

            }
        }

        private decimal CalculateDirectorTotal(IEnumerable<ForecastLineVM> forecastLines)
        {
            return forecastLines.Sum(f => f.Value); 
        }

        private decimal GeneratePriorForecastLinesValue(IEnumerable<ForecastLineVM> wk1, IEnumerable<ForecastLineVM> wk2, IEnumerable<ForecastLineVM> final, int maxOrderWithinMonth)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return 0;
                case 2:
                    return wk2.Select(a => a.Value).Sum() - wk1.Select(a => a.Value).Sum();
                case 3:
                    return final.Select(a => a.Value).Sum() - wk2.Select(a => a.Value).Sum();
                default:
                    return 0;

            }
        }

        private decimal GeneratePriorForecastValue(decimal wk1, decimal wk2, int maxOrderWithinMonth)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return 0;
                case 2:
                    return wk1;
                case 3:
                    return wk2;
                default:
                    return 0;

            }
        }

        private decimal GeneratePriorForecastValue(IEnumerable<ForecastLineVM> wk1, IEnumerable<ForecastLineVM> wk2, int maxOrderWithinMonth)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return 0;
                case 2:
                    return wk1.Select(a => a.Value).Sum();
                case 3:
                    return wk2.Select(a => a.Value).Sum();
                default:
                    return 0;

            }
        }

        private decimal GeneratePriorForecastVarianceValueNet(IEnumerable<ForecastReview> wk1, IEnumerable<ForecastReview> wk2, IEnumerable<ForecastReview> final, int maxOrderWithinMonth)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return 0;
                case 2:
                    return wk2.Any() && wk2.First().DirectorTotalNet.HasValue ? (wk2.First().DirectorTotalNet.Value - (wk1.Any() && wk1.First().DirectorTotalNet.HasValue ? wk1.First().DirectorTotalNet.Value : 0)) : 0 ;
                case 3:
                    return final.Any() && final.First().DirectorTotalNet.HasValue ? (final.First().DirectorTotalNet.Value - (wk2.Any() && wk2.First().DirectorTotalNet.HasValue ? wk2.First().DirectorTotalNet.Value : 0)) : 0;
                default:
                    return 0;

            }
        }

        private decimal GeneratePriorForecastVarianceValueGross(IEnumerable<ForecastReview> wk1, IEnumerable<ForecastReview> wk2, IEnumerable<ForecastReview> final, int maxOrderWithinMonth)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return 0;
                case 2:
                    return wk2.Any() && wk2.First().DirectorTotalGross.HasValue ? (wk2.First().DirectorTotalGross.Value - (wk1.Any() && wk1.First().DirectorTotalGross.HasValue ? wk1.First().DirectorTotalGross.Value : 0)) : 0;
                case 3:
                    return final.Any() && final.First().DirectorTotalGross.HasValue ? (final.First().DirectorTotalGross.Value - (wk2.Any() && wk2.First().DirectorTotalGross.HasValue ? wk2.First().DirectorTotalGross.Value : 0)) : 0;
                default:
                    return 0;

            }
        }


        private decimal GeneratePriorForecastValueNet(IEnumerable<ForecastReview> wk1, IEnumerable<ForecastReview> wk2, int maxOrderWithinMonth)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return 0;
                case 2:
                    return wk1.Any() ? wk1.First().DirectorTotalNet.Value : 0;
                case 3:
                    return wk2.Any() ? wk2.First().DirectorTotalNet.Value : 0;
                default:
                    return 0;

            }
        }

        private decimal GeneratePriorForecastValueGross(IEnumerable<ForecastReview> wk1, IEnumerable<ForecastReview> wk2, int maxOrderWithinMonth)
        {
            switch (maxOrderWithinMonth)
            {
                case 1:
                    return 0;
                case 2:
                    return wk1.Any() ? wk1.First().DirectorTotalGross.Value : 0;
                case 3:
                    return wk2.Any() ? wk2.First().DirectorTotalGross.Value : 0;
                default:
                    return 0;

            }
        }

        public async Task<int> RenameVersion(int forecastVersionId, string versionName, DealerGroupName dealerGroup)
        {
            return await liveForecastDataAccess.RenameVersion(forecastVersionId, versionName, dealerGroup);
        }

        public async Task<ForecastInputVM> Refresh(ForecastRefresh forecastRefresh, DealerGroupName dealerGroup)
        {
            //Get the updated Values
            var month = forecastRefresh.Month;
            var siteNonWorkingDays = await liveForecastDataAccess.GetNonWorkingDaysBySiteMonth(forecastRefresh.SiteId, month, dealerGroup);
            var forecastActualsAllDepartments = await liveForecastDataAccess.GetForecastActualsAllDepartments(forecastRefresh.SiteId, forecastRefresh.Month, dealerGroup);

            //var forecastActualsCurrentMonth = await liveForecastDataAccess.GetForecastActuals(departmentId, siteId, month);
            var forecastActualsCurrentMonth = forecastActualsAllDepartments.FirstOrDefault(a => a.DepartmentId == forecastRefresh.ForecastInputVM.DepartmentId);

            if (forecastRefresh.SiteId == 8 & forecastRefresh.ForecastInputVM.DepartmentId == 1 ) //If Site is VW Fakenham then clear its 'New' department values
            {
                //Skip the refresh;
                return forecastRefresh.ForecastInputVM;
            }

            //Get values saved for selected Version
            var BroughtInPerUnit = GetBroughtIn(forecastActualsCurrentMonth, GetEnumFromAccountName("Retail Units"));
            var DoneInMonthPerUnit = GetDoneInMonth(forecastActualsCurrentMonth, GetEnumFromAccountName("Retail Units"));

            //Get the list if the Accounts required for selected Department
            var allGroupAccountsResult = await liveForecastDataAccess.GetGroupAccounts(null, dealerGroup);
            var allGroupAccounts = allGroupAccountsResult.ToList();
            var groupAccounts = allGroupAccounts.Where(a => a.DepartmentId == forecastRefresh.ForecastInputVM.DepartmentId).ToList();
            var uniqueGroupAccounts = groupAccounts.Select(g => new { g.GroupId, g.GroupLabel }).Distinct();

            var forecastRowGroups = forecastRefresh.ForecastInputVM.ForecastRowGroups;

            // Build up the rows, for selected Department.
            foreach (var groupAccount in uniqueGroupAccounts)
            {
                var accounts = groupAccounts.Where(a => a.GroupId == groupAccount.GroupId).Select(a => new { a.AccountId, a.AccountName, a.IsStatistic, a.RowNumberFormat, a.Divisor });

                foreach (var account in accounts)
                {
                    if (!forecastRefresh.ForecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == account.AccountId && f.IsPerUnit == false)).Any())
                    {
                        continue;
                    }

                    var accountRowToUpdate = forecastRefresh.ForecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == account.AccountId && f.IsPerUnit == false)).First();

                    var BroughtIn = GetBroughtIn(forecastActualsCurrentMonth, GetEnumFromAccountName(account.AccountName));
                    var DoneInMonth = GetDoneInMonth(forecastActualsCurrentMonth, GetEnumFromAccountName(account.AccountName));
                    var RunRate = CalculateRunRate(forecastRefresh.ForecastInputVM.DepartmentId, siteNonWorkingDays, DoneInMonth, month);//calculate
                    var SiteTotal = accountRowToUpdate.SiteTotal;
                    accountRowToUpdate.BroughtIn = BroughtIn;
                    accountRowToUpdate.DoneInMonth = DoneInMonth;
                    accountRowToUpdate.RunRate = RunRate;
                    accountRowToUpdate.SiteForecast = SiteTotal - (BroughtIn + DoneInMonth);


                    //Check and add Per Unit row
                    if (account.Divisor.HasValue)
                    {
                        var divisorAccount = groupAccounts.FirstOrDefault(a => a.AccountId == account.Divisor.Value);
                        var unitAccountRowToUpdate = forecastRefresh.ForecastInputVM.ForecastRowGroups.SelectMany(f => f.ForecastRows.Where(f => f.AccountId == account.AccountId && f.IsPerUnit == true)).First();

                        var BroughtInPerUnitForDivisor = GetBroughtIn(forecastActualsCurrentMonth, GetEnumFromAccountName(divisorAccount.AccountName));
                        var DoneInMonthPerUnitForDivisor = GetDoneInMonth(forecastActualsCurrentMonth, GetEnumFromAccountName(divisorAccount.AccountName));
                        var SiteTotalUnit = unitAccountRowToUpdate.SiteTotal;



                        unitAccountRowToUpdate.BroughtIn = HelperService.DivideByDecimal(accountRowToUpdate.BroughtIn, BroughtInPerUnitForDivisor);
                        unitAccountRowToUpdate.DoneInMonth = HelperService.DivideByDecimal(accountRowToUpdate.DoneInMonth, DoneInMonthPerUnitForDivisor);
                        unitAccountRowToUpdate.RunRate = HelperService.DivideByDecimal(accountRowToUpdate.DoneInMonth, DoneInMonthPerUnitForDivisor);
                        
                        //Only if we have an active ForecastVersion
                        if (forecastRefresh.ForecastInputVM.ForecastVersionId > 0)
                        {
                            unitAccountRowToUpdate.SiteForecast = SiteTotalUnit - (unitAccountRowToUpdate.BroughtIn + unitAccountRowToUpdate.DoneInMonth); // input
                        }

                    }


                }


            }

            AddTotalUnitsRowToDepartment(forecastRefresh.ForecastInputVM);

            return forecastRefresh.ForecastInputVM;
        }



        public async Task<int> ClearAllForecastVersions(ClearAllForecastVersions clearAllForecastVersions, DealerGroupName dealerGroup)
        {
            return await liveForecastDataAccess.ClearAllForecastVersions(clearAllForecastVersions, dealerGroup);
        }

        public async Task<IEnumerable<ForecastExcelDownloadItem>> GetForecastExcelDownload(DateTime forecastDate, DealerGroupName dealerGroup)
        {
            // Get the forecast data
            IEnumerable<ForecastExcelDownloadItemByForecast> forecastRows =  await liveForecastDataAccess.GetForecastExcelDownload(forecastDate, dealerGroup);

            List<ForecastExcelDownloadItem> result = new List<ForecastExcelDownloadItem>();

            var filtered = forecastRows.GroupBy(n => new { n.Account, n.Department, n.Site })
                   .Select(x => new
                   {
                       x.Key.Account,
                       x.Key.Department,
                       x.Key.Site,
                   })
                   .ToList();

            foreach (var item in filtered)
            {
                ForecastExcelDownloadItem toAdd = new ForecastExcelDownloadItem
                {
                    Account = item.Account,
                    Site = item.Site,
                    Department = item.Department,
                    ForecastValues = new List<ForecastExcelDownloadItemForecastValue>()
                };

                var byForecast = forecastRows.Where(x => x.Account == toAdd.Account && toAdd.Site == x.Site && toAdd.Department == x.Department)
                                             .ToLookup(x => x.ForecastName);

                foreach (var regionGrouping in byForecast)
                {
                    ForecastExcelDownloadItemForecastValue valuePairToAdd = new ForecastExcelDownloadItemForecastValue();

                    string forecastName = regionGrouping.First().ForecastName;


                    valuePairToAdd.ForecastLabel = forecastName;
                    valuePairToAdd.Value = regionGrouping.Select(x => x.Value).Sum();
                    toAdd.ForecastValues.Add(valuePairToAdd);
                };

                // Add empty value pairs if not present
                foreach (string label in new string[] { "W1", "W2", "Final", "Budget", "Last Year" })
                {
                    if (!toAdd.ForecastValues.Any(x => x.ForecastLabel == label))
                    {
                        ForecastExcelDownloadItemForecastValue valuePairToAdd = new ForecastExcelDownloadItemForecastValue();
                        valuePairToAdd.ForecastLabel = label;
                        valuePairToAdd.Value = 0;
                        toAdd.ForecastValues.Add(valuePairToAdd);
                    }

                    MoveItemToEndOfList(toAdd.ForecastValues, label);
                };

                result.Add(toAdd);
            }


            return result;
        }

        public static void MoveItemToEndOfList(List<ForecastExcelDownloadItemForecastValue> list, string name)
        {
            // Find the index of the object with the specified name
            int index = list.FindIndex(x => x.ForecastLabel == name);

            // If an object with the specified name was found
            if (index != -1)
            {
                // Remove the object from its current position in the list
                ForecastExcelDownloadItemForecastValue item = list[index];
                list.RemoveAt(index);

                // Add the object to the end of the list
                list.Add(item);
            }
        }

        /*
        private decimal GetDealsDone(IEnumerable<DealsForTheMonthWeek> dealsresults, string GroupName, DealsDone boughtIn)
        {
            decimal? returnValue = 0;
            switch (boughtIn)
            {
                case DealsDone.BroughtIn:
                    var broughtInObj = dealsresults.Where(d => d.WeekName == "Brought In").First();
                    returnValue = (decimal?)broughtInObj.GetType().GetProperty(GroupName)?.GetValue(broughtInObj, null);
                    break;
                case DealsDone.Month:
                    var Week1Obj = dealsresults.Where(d => d.WeekName == "Week 1").First();
                    decimal? Week1Value = (decimal?)Week1Obj.GetType().GetProperty(GroupName)?.GetValue(Week1Obj, null);

                    var Week2Obj = dealsresults.Where(d => d.WeekName == "Week 2").First();
                    decimal? Week2Value = (decimal?)Week2Obj.GetType().GetProperty(GroupName)?.GetValue(Week2Obj, null);

                    var Week3Obj = dealsresults.Where(d => d.WeekName == "Week 3").First();
                    decimal? Week3Value = (decimal?)Week3Obj.GetType().GetProperty(GroupName)?.GetValue(Week3Obj, null);

                    var Week4Obj = dealsresults.Where(d => d.WeekName == "Week 4").First();
                    decimal? Week4Value = (decimal?)Week4Obj.GetType().GetProperty(GroupName)?.GetValue(Week4Obj, null);


                    returnValue = Week1Value + Week2Value + Week3Value + Week4Value;
                    break;
                default:
                    break;
            }


            return returnValue.GetValueOrDefault();
        }
        */


    }


}