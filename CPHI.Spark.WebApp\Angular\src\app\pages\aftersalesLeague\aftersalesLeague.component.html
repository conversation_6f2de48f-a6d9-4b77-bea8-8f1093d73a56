<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        <span>{{ constants.translatedText.Aftersales }}</span>
      </div>
    </h4>




    <!-- FOR SELECTING YEAR MONTH -->
    <div class="buttonGroup">
      <!-- dropdownMonth -->
      <div ngbDropdown *ngIf="selections.aftersalesLeague" class="d-inline-block" [autoClose]="true">
        <button class="btn btn-primary " id="pickMonth"
          ngbDropdownToggle>{{selections.aftersalesLeague.yearMonth.label}}</button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <!-- the ngFor buttons -->
          <button *ngFor="let y of selections.aftersalesLeague.yearMonths" (click)="selectYearMonth(y)"
            ngbDropdownItem>{{y.label}}</button>

        </div>
      </div>
    </div>

    <!-- FOR SELECTING LEAGUE -->
    <div class="buttonGroup">

      <div ngbDropdown *ngIf="selections.aftersalesLeague" class="d-inline-block" [autoClose]="true">
        <button class="btn btn-primary " id="pickLeague"
          ngbDropdownToggle>{{selections.aftersalesLeague.league?.label}}</button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <!-- the ngFor buttons -->
          <button *ngFor="let league of selections.aftersalesLeague.leagues" (click)="selectLeague(league)"
            ngbDropdownItem>{{league.label}}</button>

        </div>
      </div>
    </div>

    <!-- For choosing people -->
    <div class="buttonGroup">
      <button class="btn btn-primary" (click)="selections.aftersalesLeague.showAllSites = true"
        [ngClass]="{'active':selections.aftersalesLeague.showAllSites}">
        Show all people
      </button>
      <button class="btn btn-primary" (click)="selections.aftersalesLeague.showAllSites = false"
        [ngClass]="{'active':!selections.aftersalesLeague.showAllSites}">
        Just show people in my site(s)
      </button>
    </div>
  </nav>
</nav>

<div class="content column">
  <div class="contentInner">




    <!-- Tech League -->
    <div id="techsLeague" *ngIf="selections.aftersalesLeague.league.label.includes('Technician')">
      <div id="leagueTableHeaderHolder">



        <!-- Header if showing a league -->
        <ng-container
          *ngIf="selections.aftersalesLeague.league && selections.aftersalesLeague.league.label.includes('Technician')">

          <div class="leagueTableHeader" [ngClass]="selections.aftersalesLeague.league.cssClass"
            *ngFor="let each of selections.aftersalesLeague.league.columns; let i = index">
            <div id="techPositionHeader">Pos.</div>
            <div  id="techNameHeader">Technician</div>
            <div id="techRoleHeader">Role</div>
            <div [openDelay]="300" [closeDelay]="500"  [ngbPopover]="efficiencyPopover" popoverClass="reload" triggers="mouseenter:mouseleave" popoverTitle="Definition" id="techEfficiencyHeader">{{ constants.translatedText.Efficiency }}</div>
            <div [openDelay]="300" [closeDelay]="500"  [ngbPopover]="productivityPopover" popoverClass="reload" triggers="mouseenter:mouseleave" popoverTitle="Definition"  id="techProductivityHeader">{{ constants.translatedText.Productivity }}</div>
            <div id="techCitnowHeader">CitNOW</div>
            <div id="techRetailVhcHeader">
              <div class="headerRow">Retail</div>
              <div class="headerRow">VHC</div>
            </div>
            <div id="techVhcIdentifiedHeader">
              <div class="headerRow">VHC</div>
              <div class="headerRow">Identified</div>
            </div>
            <div id="techAttendedHeader">
              <div class="headerRow">{{ constants.translatedText.Hours }}</div>
              <div class="headerRow">Attended</div>
            </div>
            <div id="techSoldHeader">
              <div class="headerRow">{{ constants.translatedText.Hours }}</div>
              <div class="headerRow">{{ constants.translatedText.Sold }}</div>
            </div>
          </div>
        </ng-container>

      </div>

      <!-- league -->
      <div id="cardsHolderChosenLeague" [ngClass]="selections.aftersalesLeague.league.cssClass"
        *ngIf="selections.aftersalesLeague.league">
        <!-- The cards themselves -->
        <ng-container *ngFor="let tech of selections.aftersalesLeague.league.people; let i = index">
          <ng-container
            *ngIf="selections.aftersalesLeague.showAllSites || selections.user.eligibleSiteIds.includes(tech.CurrentSite.Id)">
            <div class="cardHolder">
              <performanceLeagueCardTechs [lowHeightCard]="!selections.aftersalesLeague.league.label.includes('Gold')"
                [highlight]="selections.user.CurrentSite.Id == tech.CurrentSite.Id" [technician]="tech"
                [position]="tech.AftersalesLeaguePerformance.Rank" class="spaceBetween performanceLeagueCard">
              </performanceLeagueCardTechs>
            </div>
          </ng-container>
        </ng-container>
      </div>




    </div>







    <!-- ===================== -->
    <!-- ===================== -->
    <!-- ===================== -->
    <!-- Advisors League -->
    <div id="advisorsLeague" *ngIf="selections.aftersalesLeague.league.label.includes('Advisor')">




      <!-- Header if showing a league -->
      <ng-container
        *ngIf=" selections.aftersalesLeague.league && selections.aftersalesLeague.league.label.includes('Advisor')">
        <div id="leagueTableHeaderHolder">
          <div class="leagueTableHeader" *ngFor="let each of selections.aftersalesLeague.league.columns; let i = index"
            [ngClass]="selections.aftersalesLeague.league.cssClass">
            <div id="advisorPositionHeader">{{constants.translatedText.PerformanceLeague_Rank}}</div>
            <div id="advisorNameHeader">{{constants.translatedText.PerformanceLeague_Advisor}}</div>
            <div id="advisorRoleHeader">{{constants.translatedText.PerformanceLeague_Role}}</div>
            <div id="advisorRedWorkConvertedHeader">
              <div class="headerRow">{{constants.translatedText.Evhc_RedWork}}</div>
              <div class="headerRow">Converted</div>
            </div>
            <div id="advisorAmberWorkConvertedHeader">
              <div class="headerRow">Amber Work</div>
              <div class="headerRow">Converted</div>
            </div>
            <div id="advisorInvoicedCountHeader">Invoices</div>
            <div id="advisorRedAndAmberWorkSoldHeader">Upsold</div>
            <div id="advisorInvoicedValueHeader">Total Invoiced</div>
          </div>
        </div>

      </ng-container>

      <!--  league -->
      <div id="cardsHolderChosenLeague" [ngClass]="selections.aftersalesLeague?.league?.cssClass"
        *ngIf="selections.aftersalesLeague.league">
        <ng-container *ngFor="let advisor of selections.aftersalesLeague.league.people; let i = index">
          <ng-container
            *ngIf="selections.aftersalesLeague.showAllSites || selections.user.eligibleSiteIds.includes(advisor.CurrentSite.Id)">
            <div class="cardHolder">

              <performanceLeagueCardAdvisors
                [lowHeightCard]="!selections.aftersalesLeague.league.label.includes('Gold')"
                [highlight]="selections.user.CurrentSite.Id == advisor.CurrentSite.Id" [advisor]="advisor"
                [position]="advisor.AftersalesLeaguePerformance.Rank" class="spaceBetween performanceLeagueCard">
              </performanceLeagueCardAdvisors>
            </div>
          </ng-container>
        </ng-container>
      </div>






    </div>
  </div>
</div>



<ng-template #efficiencyPopover>
  Efficiency is defined as Sold hours divided by Worked hours.
</ng-template>
<ng-template #productivityPopover>
  Efficiency is defined as Worked hours divided by Attended hours.
</ng-template>