﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Dapper;
using Microsoft.EntityFrameworkCore;

namespace CPHI.Spark.DataAccess.DataAccess.AutoPrice
{
    public interface ISiteSettingsDataAccess
    {
        Task<IEnumerable<SiteSettings>> GetSitesSettings(Model.DealerGroupName userDealerGroupName, int userId);
        Task<SiteSettings> SaveSiteSettings(SaveSiteSettingsParams parms, int userId);
    }

    public class SiteSettingsDataAccess : ISiteSettingsDataAccess
    {
        private readonly string _connectionString;
        public SiteSettingsDataAccess(string connectionString)
        {
            this._connectionString = connectionString;
        }

        public async Task<IEnumerable<SiteSettings>> GetSitesSettings(Model.DealerGroupName userDealerGroupName, int userId)
        {
            using (var dapper = new DADapperr(_connectionString))
            {
                var paramList = new DynamicParameters();
                paramList.Add("dealerGroupId", (int)userDealerGroupName);
                paramList.Add("userId", userId);
                return await dapper.GetAllAsync<SiteSettings>("autoprice.GET_SitesSettings", paramList, commandType: System.Data.CommandType.StoredProcedure);
            }
        }

        public async Task<SiteSettings> SaveSiteSettings(SaveSiteSettingsParams parms, int userId)
        {


            using (var db = new CPHIDbContext(_connectionString))
            {

                var retailerSite = await db.RetailerSites.Where(rs => rs.Id == parms.siteSettings.RetailerSiteId).FirstOrDefaultAsync();

                retailerSite.LocalBargainThreshold = parms.siteSettings.LocalBargainThreshold;
                retailerSite.LocationMoveFixedCostPerMove = parms.siteSettings.LocationMoveFixedCostPerMove;
                retailerSite.LocationMovePoundPerMile = parms.siteSettings.LocationMovePoundPerMile;
                retailerSite.UpdatePricesAutomatically = parms.siteSettings.UpdatePricesAutomatically;
                retailerSite.IncludeUnPublishedAds = parms.siteSettings.IncludeUnPublishedAdsInEmailReport;
                retailerSite.MinimumAutoPriceDecrease = parms.siteSettings.MinimumAutoPriceDecrease;
                retailerSite.MinimumAutoPriceIncrease = parms.siteSettings.MinimumAutoPriceIncrease;
                retailerSite.UpdatePricesMon = parms.siteSettings.UpdatePricesMon;
                retailerSite.UpdatePricesTue = parms.siteSettings.UpdatePricesTue;
                retailerSite.UpdatePricesWed = parms.siteSettings.UpdatePricesWed;
                retailerSite.UpdatePricesThu = parms.siteSettings.UpdatePricesThu;
                retailerSite.UpdatePricesFri = parms.siteSettings.UpdatePricesFri;
                retailerSite.UpdatePricesSat = parms.siteSettings.UpdatePricesSat;
                retailerSite.UpdatePricesSun = parms.siteSettings.UpdatePricesSun;
                retailerSite.UpdatePricesPubHolidays = parms.siteSettings.UpdatePricesPubHolidays;
                retailerSite.WhenToActionChangesEachDay = parms.siteSettings.WhenToActionChangesEachDay;
                retailerSite.MaximumOptOutDays = parms.siteSettings.MaximumOptOutDays;
                retailerSite.CompetitorPlateRange = parms.siteSettings.CompetitorPlateRange;
                retailerSite.MinimumAutoPricePercentDecrease = (decimal)parms.siteSettings.MinimumAutoPricePercentDecrease / 100;
                retailerSite.MinimumAutoPricePercentIncrease = (decimal)parms.siteSettings.MinimumAutoPricePercentIncrease / 100;
                retailerSite.LocalBargainsSearchRadius = parms.siteSettings.LocalBargainsSearchRadius;
                retailerSite.LocalBargainsMinRetailRating = parms.siteSettings.LocalBargainsMinRetailRating;

                retailerSite.StrategySelectionRuleSet_Id = parms.siteSettings.StrategySelectionRuleSetId;
                retailerSite.BuyingStrategySelectionRuleSet_Id = parms.siteSettings.BuyingStrategySelectionRuleSetId;
                retailerSite.BuyingStrategySelectionRuleSet2_Id = parms.siteSettings.BuyingStrategySelectionRuleSet2Id;

                if (parms.siteSettings.TestStrategySelectionRuleSetId != 0 && parms.siteSettings.TestStrategySelectionRuleSetId != null)
                {
                    retailerSite.TestStrategySelectionRuleSet_Id = parms.siteSettings.TestStrategySelectionRuleSetId;
                }

                retailerSite.TargetAdditionalMech = parms.siteSettings.TargetAdditionalMech;
                retailerSite.TargetPaintPrep = parms.siteSettings.TargetPaintPrep;
                retailerSite.TargetMargin = parms.siteSettings.TargetMargin;
                retailerSite.TargetAuctionFee = parms.siteSettings.TargetAuctionFee;
                retailerSite.TargetDelivery = parms.siteSettings.TargetDelivery;
                retailerSite.TargetOtherCost = parms.siteSettings.TargetOtherCost;

                await db.SaveChangesWithAuditAsync(userId);
                return parms.siteSettings;


            }
        }


    }
}
