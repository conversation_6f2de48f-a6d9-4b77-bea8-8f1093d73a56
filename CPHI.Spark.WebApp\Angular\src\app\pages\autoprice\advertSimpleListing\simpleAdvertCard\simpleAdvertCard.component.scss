.autoTraderVehicleCard {
    width: 100%;
    display: flex;
    border-radius: 4px;
    background-color: #FFFFFF;
    transition: box-shadow 0.25s ease-in-out 0s;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 8px 8px -8px, rgba(0, 0, 0, 0.1) 0px 0px 2px;
    cursor: pointer;
    position: relative;

    &:hover {
        box-shadow: rgba(0, 0, 0, 0.12) 0px 8px 12px -4px, rgba(0, 0, 0, 0.1) 0px 0px 2px;
    }

    #images {
        width: 30%;
        display: flex;
        padding: 1em;
        position: relative;

        #mainImage {
            width: 75%;
            height: 100%;
            background-size: cover;
            background-position: center;
        }

        #subImages {
            width: calc(25% - 1em);
            margin-left: 1em;
            justify-content: space-between;
            display: flex;
            flex-direction: column;

            .subImage {
                width: 100%;
                height: 30%;
                background-size: cover;
                background-position: center;
            }
        }

        #floatingReg {
            position: absolute;
            top: 1em;
            left: 1em;

            .regPlate {
                
            }
        }

        #floatingImagesVideoCount {
            position: absolute;
            bottom: 1.1em;
            left: 1.1em;
            background-color: var(--grey90);
            padding: 0.25em;
        }
    }

    .contentHolder {
        width: 70%;
        padding: 1em 1em 1em 0;
        display: flex;

        #vehicleDetails {
            display: flex;
            flex-direction: column;
            width: 33.3%;
            min-width: 19em;
            margin-right: 0.5em;
            justify-content: space-between;

            h2 {
                
            }

            .makeModel {
                display: flex;
                justify-content: space-between;

                .openAdButton {
                    margin-left: 1em;
                    padding: 0;
                    color: var(--secondary);
                }
            }

            .specs {
                font-weight: bold;
            }

            .pricePositionRank,
            .marketPositionScore,
            .lastPriceChange,
            .priceLastChanged {
                display: flex;
                justify-content: space-between;
            }
        }

        #vehicleDetailsContinued {
            display: flex;
            flex-direction: column;
            width: 33.3%;
            min-width: 19em;
            margin: 0 1em;
            justify-content: space-between;

            h2 {
                
            }

            .labelAndValue {
                display: flex;
                justify-content: space-between;

                .priceAndPosition {
                    display: flex;
                    align-items: baseline;

                    h2 {
                        margin-right: 0.5em;
                    }
                }
            }
        }

        #comments {
            display: flex;
            flex-direction: column;
            flex: 1;
            position: relative;
            margin-left: 0.5em;

            #priceRatingLozengeContainer {
                display: flex;
                justify-content: flex-end;
            }

            #comments-container {
                height: calc(100% - 5em);
                overflow-y: auto;
            }

            .comment-bubble-container {
                display: flex;
                flex-direction: column;
                margin-bottom: 1em;

                &:last-of-type {
                    margin-bottom: 0;
                }
            }

            .comment-bubble {
                border-radius: 5px;
                padding: 0.5em;
                color: #FFFFFF;
                min-width: 50%;
                max-width: 75%;
                background-color: var(--atSecondary);
                position: relative;

                .comment-person {
                    
                    color: #D2D2D2;
                }
            }

            #add-new-comment {
                width: 100%;
                height: 4em;
                position: absolute;
                bottom: 0;
                display: flex;
                align-items: flex-end;
                background-color: #FFFFFF;

                textarea {
                    width: 100%;
                    resize: none;
                    border: 1px solid var(--grey90);
                    border-radius: 8px;
                    padding: 0.5em;
                    height: 3em;

                    &.canSave {
                        border-radius: 8px 0 0 8px;
                    }
                }

                button {
                    height: 3em;
                    border: 1px solid var(--goodColour);
                    background: var(--goodColour);
                    width: 15%;
                    color: #FFFFFF;
                    border-radius: 0 8px 8px 0;
                }
            }
        }

    }
}