import { Component, EventEmitter, Input, Output } from "@angular/core";
import { ConstantsService } from '../../services/constants.service';

export interface MultiSelectMonth {
    startDate: Date;
    isSelected?: boolean;
}

@Component({
  selector: 'datePickerMultiSelect',
  templateUrl: './datePickerMultiSelect.component.html',
  styleUrls: ['./datePickerMultiSelect.component.scss']
})

export class DatePickerMultiSelectComponent {

    @Input() monthsFromParent: MultiSelectMonth[];
    @Input() includeYTD: boolean;
    @Input() includeThisYear: boolean;
    @Input() includeLastYear: boolean;
    @Output() selectMonths = new EventEmitter<MultiSelectMonth[]>();

    months: MultiSelectMonth[];

    selectedYTD: boolean;
    selectedLastYear: boolean;
    selectedThisYear: boolean;

    constructor(
        public constants: ConstantsService
    ) { }

    makeMonths() : void {
      let months: MultiSelectMonth[] = [];

      for (let i = -24; i < 7; i++) {
        months.push(this.generateMonth(this.constants.thisMonthStart, i));
      }
    

      this.months = months;

      setTimeout(() => {
        document.getElementsByClassName('buttonToScrollTo')[0].scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'start'
        });
      }, 100)
    }

    generateMonth(baseDate: Date, offset: number) : MultiSelectMonth {

      let month: MultiSelectMonth = {
          startDate: new Date(baseDate.getFullYear(), baseDate.getMonth() + offset, 1),
          isSelected: false
      }

      // console.log(this.monthsFromParent, this.monthsFromParent.find(x => x.startDate.getMonth() == month.startDate.getMonth() && x.startDate.getFullYear() == month.startDate.getFullYear()), "---")
      
      if (this.monthsFromParent, this.monthsFromParent.find(x => x.startDate.getMonth() == month.startDate.getMonth() && x.startDate.getFullYear() == month.startDate.getFullYear()))
      {
        month.isSelected = true;
      } 

      return month;
    }

    selectMonth(month: MultiSelectMonth): void {
      month.isSelected = !month.isSelected;
    }

    selectYTD(): void
    {
      this.selectedLastYear = false;
      this.selectedThisYear = false;
      this.selectedYTD = !this.selectedYTD;

      let currentYear: number = this.constants.todayStart.getFullYear();
      let currentMonth: number = this.constants.todayStart.getMonth();

      this.months.forEach(element => {
        if(
          element.startDate.getFullYear() == currentYear && element.startDate.getFullYear() &&
          element.startDate.getMonth() <= currentMonth
          )
        { 
          element.isSelected = this.selectedYTD; 
        }
        else 
        { 
          element.isSelected = false; 
        }
      });

    }

    selectLastYear(): void
    {
      this.selectedYTD = false;
      this.selectedThisYear = false;
      this.selectedLastYear = !this.selectedLastYear;
      
      let lastYear: number = this.constants.todayStart.getFullYear() - 1;

      this.months.forEach(element => {
        if(element.startDate.getFullYear() == lastYear){ element.isSelected = this.selectedLastYear; }
        else { element.isSelected = false; }
      });

    }

    selectThisYear(): void
    {
      this.selectedYTD = false;
      this.selectedLastYear = false;
      this.selectedThisYear = !this.selectedThisYear;

      let currentYear: number = this.constants.todayStart.getFullYear();

      this.months.forEach(element => {
        if(element.startDate.getFullYear() == currentYear){ element.isSelected = this.selectedThisYear; }
        else { element.isSelected = false; }
      });

    }

    confirmSelection(): void {
      this.selectMonths.emit(this.months.filter(m => m.isSelected));
    }

    getButtonLabel() : string {
        if (this.monthsFromParent.length == 0) return this.constants.translatedText.MonthMultiPicker_NoMonthsSelected;
        if (this.monthsFromParent.filter(x => x.isSelected).length == 1) {
            return this.monthsFromParent.find(x => x.isSelected).startDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
        }
        if (this.monthsFromParent.filter(x => x.isSelected).length > 1) return this.constants.translatedText.Months;
    }
}
