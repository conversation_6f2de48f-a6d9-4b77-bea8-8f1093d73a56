CREATE OR ALTER PROCEDURE [dbo].[MERGE_DealLatests]
	(
		@AccountingDateStart Date,
		@AccountingDateEnd Date,
		-- If set, does return deals with duplicate stock number 
		-- Set to 1 for <PERSON>dis, 0 for Renault
		@IncludeDuplicates Int,
		@DealerGroupId Int
	)
	AS
	BEGIN

	SET NOCOUNT ON

	DECLARE @eightWeeksAgo Date = DATEADD(week,-8,getDate());

	--RRG feed sometimes can fail to zero out proper (deals with a unit of 1) deals.   To eliminate this, we only use the highest Id version
		-- of each stocknumber
		SELECT max(d.Id) as id 
		INTO #HighestUniqueIdsForFullDeals
		FROM Deals d
		INNER JOIN Sites si on si.Id = d.Site_Id
		WHERE Convert(date,AccountingDate) >= @AccountingDateStart
		AND Convert(date,AccountingDate) <= @AccountingDateEnd
		AND IsRemoved = 0
		AND Units = 1
		AND IsLateCost = 0
		AND si.DealerGroup_Id = @DealerGroupId
		GROUP BY StockNumber

		SELECT
		ads.VehicleReg,
		snaps.TotalPrice,
		st.IsVatQ ,
		ROW_NUMBER() OVER (PARTITION BY ads.VehicleReg ORDER BY ads.Id desc) AS RowNumber  
		INTO #latestAdvertised
		FROM autoprice.LeavingPriceItems lpi
		INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.Id = lpi.LastVehicleAdvertSnapshot_Id
		INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
		INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
		LEFT JOIN stocks st on st.VehicleAdvert_Id = ads.Id 
		WHERE snaps.SnapshotDate > @eightWeeksAgo
		AND snaps.suppliedPrice IS NOT NULL AND snaps.suppliedPrice > 0
		AND rs.DealerGroup_Id = @DealerGroupId



	MERGE DealLatests AS TARGET
	USING (

	  SELECT 
	  d.[Description],
      d.[Id] as DealId,
      [Reg],
      [StockDate],
      [RegisteredDate],
      [Customer],
      [OrderDate],
      [ActualDeliveryDate],
      [IsDelivered],
      [Discount],
      [FuelSale],
      [FuelCost],
      [OemDeliverySale],
      [OemDeliveryCost],
      [AccessoriesSale],
      [AccessoriesCost],
      [NewBonus1],
      [NewBonus2],
      [PartExOverAllowance1],
      [Site_Id],
      [Salesman_Id],
      [StockNumber],
      [InvoiceDate],
      [IsLateCost],
      [BrokerCost],
      [IntroCommission],
      [VehicleType_Id],
      [Model],
      [ModelYear],
      [IsInvoiced],
      [FinanceCo],
      [Sale],
      [CoS],
      [MechPrep],
      [BodyPrep],
      [Error],
	  [StandardWarrantyCost],
	  [HasPaintProtectionAccessory],
	  [PaintProtectionAccessorySale],
	  [PaintProtectionAccessoryCost],
      [Franchise_Id],
      [OrderType_Id],
      [VehicleClass_Id],
      [TotalNLProfit],
      [PDICost],
      [Units],
      [HandoverDate],
      [EnquiryNumber],
      [VehicleSource],
      [AccountingDate],
      [Other],
      [VatCost],
      [OemReference],
      [LastPhysicalLocation],
      [DeliverySite_Id],
      [IsClosed],
	  [AuditPass],
      [FinanceType],
	  [IsFinanced],
      [FinanceCommission],
      [FinanceSubsidy],
      [SelectCommission],
      [StandardsCommission],
      [RCIFinanceCommission],
      [ProPlusCommission],
	  [HasServicePlan],
      [ServicePlanSale],
      [ServicePlanCost],
	  [HasCosmeticInsurance],
	  [CosmeticInsuranceSale]  ,
      [CosmeticInsuranceCost] ,
      [CosmeticInsuranceCommission],
      [HasPaintProtection],
      [PaintProtectionSale],
      [PaintProtectionCost],
	  [HasGapInsurance],
      [GapInsuranceSale]  ,
      [GapInsuranceCost] ,
      [GapInsuranceCommission] ,
      [HasWarranty],
      [HasShortWarranty],
	  [WarrantySale],
      [WarrantyCost],
	  [HasWheelGuard],
      [WheelGuardSale],
      [WheelGuardCost],
	  [WheelGuardCommission],
	  [HasTyreInsurance],
      [TyreInsuranceSale],
      [TyreInsuranceCost],
      [TyreInsuranceCommission],
      [HasAlloyInsurance],
      [AlloyInsuranceSale],
      [AlloyInsuranceCost],
      [AlloyInsuranceCommission],
      [HasTyreAndAlloyInsurance],
	  [TyreAndAlloyInsuranceSale],
      [TyreAndAlloyInsuranceCost],
	  [TyreAndAlloyInsuranceCommission],
      COALESCE(d.HasCosmeticInsurance * 1 
		+ d.HasGapInsurance * 1
		+ d.HasPaintProtection * 1
		+ d.HasPaintProtectionAccessory * 1
		+ d.HasServicePlan * 1
		+ d.HasWarranty * 1
		+ d.HasShortWarranty * 1
		+ d.HasWheelGuard * 1
		+ d.HasTyreInsurance * 1
		+ d.HasAlloyInsurance * 1
		+ d.HasTyreAndAlloyInsurance * 1,0)
	as TotalProductCount,
	la.TotalPrice as LastAdvertisedPrice,
	la.IsVatQ as IsVatQ,
	[VehicleAdvert_Id]


	FROM Deals d
	INNER JOIN Sites s on s.Id = d.Site_Id
	LEFT JOIN #HighestUniqueIdsForFullDeals hui on hui.id = d.id  
	LEFT JOIN #latestAdvertised la on la.RowNumber = 1 AND la.VehicleReg IS NOT NULL AND la.VehicleReg <> '' AND la.VehicleReg = d.Reg
	WHERE 
	s.DealerGroup_Id = @DealerGroupId
	AND d.IsRemoved = 0
	AND CONVERT(date,d.AccountingDate) >= @AccountingDateStart 
	AND CONVERT(date,AccountingDate) <= @AccountingDateEnd
	AND (
			(
				hui.id IS NOT NULL --it's the most recent Id for this stocknumber
				OR 	
				d.IsLateCost = 1  --or it's a late cost
			) OR 
			@IncludeDuplicates = 1 --we are a group like vindis for whom we just want to include everything
			)  
	) AS SOURCE
	

	--The merge key
	ON (TARGET.DealId = SOURCE.DealId)

	--INSERT 
	WHEN NOT MATCHED BY TARGET  THEN 
	INSERT 
	(
	  [Description],
      [DealId],
      [Reg],
      [StockDate],
      [RegisteredDate],
      [Customer],
      [OrderDate],
      [ActualDeliveryDate],
      [IsDelivered],
      [Discount],
      [FuelSale],
      [FuelCost],
      [OemDeliverySale],
      [OemDeliveryCost],
      [AccessoriesSale],
      [AccessoriesCost],
      [NewBonus1],
      [NewBonus2],
      [PartExOverAllowance1],
      [Site_Id],
      [Salesman_Id],
      [StockNumber],
      [InvoiceDate],
      [IsLateCost],
      [BrokerCost],
      [IntroCommission],
      [VehicleType_Id],
      [Model],
      [ModelYear],
      [IsInvoiced],
      [FinanceCo],
      [Sale],
      [CoS],
      [MechPrep],
      [BodyPrep],
      [Error],
	  [StandardWarrantyCost],
		[HasPaintProtectionAccessory],
		[PaintProtectionAccessorySale],
		[PaintProtectionAccessoryCost],
      [Franchise_Id],
      [OrderType_Id],
      [VehicleClass_Id],
      [TotalNLProfit],
      [PDICost],
      [Units],
      [HandoverDate],
      [EnquiryNumber],
      [VehicleSource],
      [AccountingDate],
      [Other],
      [VatCost],
      [OemReference],
      [LastPhysicalLocation],
      [DeliverySite_Id],
      [IsClosed],
	  [AuditPass],
      [FinanceType],
	  [IsFinanced],
      [FinanceCommission],
      [FinanceSubsidy],
      [SelectCommission],
      [StandardsCommission],
      [RCIFinanceCommission],
      [ProPlusCommission],
      [HasServicePlan],
      [ServicePlanSale],
      [ServicePlanCost],
	  [HasCosmeticInsurance],
      [CosmeticInsuranceSale],
      [CosmeticInsuranceCost],
      [CosmeticInsuranceCommission],
      [HasPaintProtection],
      [PaintProtectionSale],
      [PaintProtectionCost],
	  [HasGapInsurance],
      [GapInsuranceSale],
      [GapInsuranceCost],
      [GapInsuranceCommission],
      [HasWarranty],
      [HasShortWarranty],
	  [WarrantySale],
      [WarrantyCost],
      [HasWheelGuard],
      [WheelGuardSale],
      [WheelGuardCost],
	  [WheelGuardCommission],
	  [HasTyreInsurance],
      [TyreInsuranceSale],
      [TyreInsuranceCost],
      [TyreInsuranceCommission],
      [HasAlloyInsurance],
      [AlloyInsuranceSale],
      [AlloyInsuranceCost],
      [AlloyInsuranceCommission],
      [HasTyreAndAlloyInsurance],
	  [TyreAndAlloyInsuranceSale],
      [TyreAndAlloyInsuranceCost],
	  [TyreAndAlloyInsuranceCommission],
      [TotalProductCount],
	  [LastAdvertisedPrice],
	  [IsVatQ],
	  [VehicleAdvert_Id]
	)
	VALUES(
		SOURCE.[Description],
		SOURCE.DealId,
		SOURCE.[Reg],
		SOURCE.[StockDate],
		SOURCE.[RegisteredDate],
		SOURCE.[Customer],
		SOURCE.[OrderDate],
		SOURCE.[ActualDeliveryDate],
		SOURCE.[IsDelivered],
		SOURCE.[Discount],
		SOURCE.[FuelSale],
		SOURCE.[FuelCost],
		SOURCE.[OemDeliverySale],
		SOURCE.[OemDeliveryCost],
		SOURCE.[AccessoriesSale],
		SOURCE.[AccessoriesCost],
		SOURCE.[NewBonus1],
		SOURCE.[NewBonus2],
		SOURCE.[PartExOverAllowance1],
		SOURCE.[Site_Id],
		SOURCE.[Salesman_Id],
		SOURCE.[StockNumber],
		SOURCE.[InvoiceDate],
		SOURCE.[IsLateCost],
		SOURCE.[BrokerCost],
		SOURCE.[IntroCommission],
		SOURCE.[VehicleType_Id],
		SOURCE.[Model],
		SOURCE.[ModelYear],
		SOURCE.[IsInvoiced],
		SOURCE.[FinanceCo],
		SOURCE.[Sale],
		SOURCE.[CoS],
		SOURCE.[MechPrep],
		SOURCE.[BodyPrep],
		SOURCE.[Error],
		SOURCE.[StandardWarrantyCost],
		SOURCE.[HasPaintProtectionAccessory],
		SOURCE.[PaintProtectionAccessorySale],
		SOURCE.[PaintProtectionAccessoryCost],
		SOURCE.[Franchise_Id],
		SOURCE.[OrderType_Id],
		SOURCE.[VehicleClass_Id],
		SOURCE.[TotalNLProfit],
		SOURCE.[PDICost],
		SOURCE.[Units],
		SOURCE.[HandoverDate],
		SOURCE.[EnquiryNumber],
		SOURCE.[VehicleSource],
		SOURCE.[AccountingDate],
		SOURCE.[Other],
		SOURCE.[VatCost],
		SOURCE.[OemReference],
		SOURCE.[LastPhysicalLocation],
		SOURCE.[DeliverySite_Id],
		SOURCE.[IsClosed],
		SOURCE.[AuditPass],
		SOURCE.[FinanceType],
		SOURCE.[IsFinanced],
		SOURCE.[FinanceCommission],
		SOURCE.[FinanceSubsidy],
		SOURCE.[SelectCommission],
		SOURCE.[StandardsCommission],
		SOURCE.[RCIFinanceCommission],
		SOURCE.[ProPlusCommission],
		SOURCE.[HasServicePlan],
		SOURCE.[ServicePlanSale],
		SOURCE.[ServicePlanCost],
		SOURCE.[HasCosmeticInsurance],
		SOURCE.[CosmeticInsuranceSale],
		SOURCE.[CosmeticInsuranceCost],
		SOURCE.[CosmeticInsuranceCommission],
		SOURCE.[HasPaintProtection],
		SOURCE.[PaintProtectionSale],
		SOURCE.[PaintProtectionCost],
		SOURCE.[HasGapInsurance],
		SOURCE.[GapInsuranceSale],
		SOURCE.[GapInsuranceCost],
		SOURCE.[GapInsuranceCommission],
		SOURCE.[HasWarranty],
		SOURCE.[HasShortWarranty],
		SOURCE.[WarrantySale],
		SOURCE.[WarrantyCost],
		SOURCE.[HasWheelGuard],
		SOURCE.[WheelGuardSale],
		SOURCE.[WheelGuardCost],
		SOURCE.[WheelGuardCommission],
		SOURCE.[HasTyreInsurance],
		SOURCE.[TyreInsuranceSale],
		SOURCE.[TyreInsuranceCost],
		SOURCE.[TyreInsuranceCommission],
		SOURCE.[HasAlloyInsurance],
		SOURCE.[AlloyInsuranceSale],
		SOURCE.[AlloyInsuranceCost],
		SOURCE.[AlloyInsuranceCommission],
		SOURCE.[HasTyreAndAlloyInsurance],
		SOURCE.[TyreAndAlloyInsuranceSale],
		SOURCE.[TyreAndAlloyInsuranceCost],
		SOURCE.[TyreAndAlloyInsuranceCommission],
		SOURCE.[TotalProductCount],
		SOURCE.[LastAdvertisedPrice],
		SOURCE.[IsVatQ],
		SOURCE.[VehicleAdvert_Id]
	)


	--UPDATE - checking all cols to see if there is any update - performance improved from 55sec to 25sec
	WHEN MATCHED AND (
    TARGET.[Description] <> SOURCE.[Description] OR
    TARGET.[DealId] <> SOURCE.[DealId] OR
    TARGET.[Reg] <> SOURCE.[Reg] OR
    TARGET.[StockDate] <> SOURCE.[StockDate] OR
    TARGET.[RegisteredDate] <> SOURCE.[RegisteredDate] OR
    TARGET.[Customer] <> SOURCE.[Customer] OR
    TARGET.[OrderDate] <> SOURCE.[OrderDate] OR
    TARGET.[ActualDeliveryDate] <> SOURCE.[ActualDeliveryDate] OR
    TARGET.[IsDelivered] <> SOURCE.[IsDelivered] OR
    TARGET.[Discount] <> SOURCE.[Discount] OR
    TARGET.[FuelSale] <> SOURCE.[FuelSale] OR
    TARGET.[FuelCost] <> SOURCE.[FuelCost] OR
    TARGET.[OemDeliverySale] <> SOURCE.[OemDeliverySale] OR
    TARGET.[OemDeliveryCost] <> SOURCE.[OemDeliveryCost] OR
    TARGET.[AccessoriesSale] <> SOURCE.[AccessoriesSale] OR
    TARGET.[AccessoriesCost] <> SOURCE.[AccessoriesCost] OR
    TARGET.[NewBonus1] <> SOURCE.[NewBonus1] OR
    TARGET.[NewBonus2] <> SOURCE.[NewBonus2] OR
    TARGET.[PartExOverAllowance1] <> SOURCE.[PartExOverAllowance1] OR
    TARGET.[Site_Id] <> SOURCE.[Site_Id] OR
    TARGET.[Salesman_Id] <> SOURCE.[Salesman_Id] OR
    TARGET.[StockNumber] <> SOURCE.[StockNumber] OR
    TARGET.[InvoiceDate] <> SOURCE.[InvoiceDate] OR
    TARGET.[IsLateCost] <> SOURCE.[IsLateCost] OR
    TARGET.[BrokerCost] <> SOURCE.[BrokerCost] OR
    TARGET.[IntroCommission] <> SOURCE.[IntroCommission] OR
    TARGET.[VehicleType_Id] <> SOURCE.[VehicleType_Id] OR
    TARGET.[Model] <> SOURCE.[Model] OR
    TARGET.[ModelYear] <> SOURCE.[ModelYear] OR
    TARGET.[IsInvoiced] <> SOURCE.[IsInvoiced] OR
    TARGET.[FinanceCo] <> SOURCE.[FinanceCo] OR
    TARGET.[Sale] <> SOURCE.[Sale] OR
    TARGET.[CoS] <> SOURCE.[CoS] OR
    TARGET.[MechPrep] <> SOURCE.[MechPrep] OR
    TARGET.[BodyPrep] <> SOURCE.[BodyPrep] OR
    TARGET.[Error] <> SOURCE.[Error] OR
    TARGET.[StandardWarrantyCost] <> SOURCE.[StandardWarrantyCost] OR
    TARGET.[HasPaintProtectionAccessory] <> SOURCE.[HasPaintProtectionAccessory] OR
    TARGET.[PaintProtectionAccessorySale] <> SOURCE.[PaintProtectionAccessorySale] OR
    TARGET.[PaintProtectionAccessoryCost] <> SOURCE.[PaintProtectionAccessoryCost] OR
    TARGET.[Franchise_Id] <> SOURCE.[Franchise_Id] OR
    TARGET.[OrderType_Id] <> SOURCE.[OrderType_Id] OR
    TARGET.[VehicleClass_Id] <> SOURCE.[VehicleClass_Id] OR
    TARGET.[TotalNLProfit] <> SOURCE.[TotalNLProfit] OR
    TARGET.[PDICost] <> SOURCE.[PDICost] OR
    TARGET.[Units] <> SOURCE.[Units] OR
    TARGET.[HandoverDate] <> SOURCE.[HandoverDate] OR
    TARGET.[EnquiryNumber] <> SOURCE.[EnquiryNumber] OR
    TARGET.[VehicleSource] <> SOURCE.[VehicleSource] OR
    TARGET.[AccountingDate] <> SOURCE.[AccountingDate] OR
    TARGET.[Other] <> SOURCE.[Other] OR
    TARGET.[VatCost] <> SOURCE.[VatCost] OR
    TARGET.[OemReference] <> SOURCE.[OemReference] OR
    TARGET.[LastPhysicalLocation] <> SOURCE.[LastPhysicalLocation] OR
    TARGET.[DeliverySite_Id] <> SOURCE.[DeliverySite_Id] OR
    TARGET.[IsClosed] <> SOURCE.[IsClosed] OR
	TARGET.[AuditPass] IS NULL OR
    TARGET.[AuditPass] <> SOURCE.[AuditPass] OR
    TARGET.[FinanceType] <> SOURCE.[FinanceType] OR
    TARGET.[IsFinanced] <> SOURCE.[IsFinanced] OR
    TARGET.[FinanceCommission] <> SOURCE.[FinanceCommission] OR
    TARGET.[FinanceSubsidy] <> SOURCE.[FinanceSubsidy] OR
    TARGET.[SelectCommission] <> SOURCE.[SelectCommission] OR
    TARGET.[StandardsCommission] <> SOURCE.[StandardsCommission] OR
    TARGET.[RCIFinanceCommission] <> SOURCE.[RCIFinanceCommission] OR
    TARGET.[ProPlusCommission] <> SOURCE.[ProPlusCommission] OR
    TARGET.[HasServicePlan] <> SOURCE.[HasServicePlan] OR
    TARGET.[ServicePlanSale] <> SOURCE.[ServicePlanSale] OR
    TARGET.[ServicePlanCost] <> SOURCE.[ServicePlanCost] OR
    TARGET.[HasCosmeticInsurance] <> SOURCE.[HasCosmeticInsurance] OR
    TARGET.[CosmeticInsuranceSale] <> SOURCE.[CosmeticInsuranceSale] OR
    TARGET.[CosmeticInsuranceCost] <> SOURCE.[CosmeticInsuranceCost] OR
    TARGET.[CosmeticInsuranceCommission] <> SOURCE.[CosmeticInsuranceCommission] OR
    TARGET.[HasPaintProtection] <> SOURCE.[HasPaintProtection] OR
    TARGET.[PaintProtectionSale] <> SOURCE.[PaintProtectionSale] OR
    TARGET.[PaintProtectionCost] <> SOURCE.[PaintProtectionCost] OR
    TARGET.[HasGapInsurance] <> SOURCE.[HasGapInsurance] OR
    TARGET.[GapInsuranceSale] <> SOURCE.[GapInsuranceSale] OR
    TARGET.[GapInsuranceCost] <> SOURCE.[GapInsuranceCost] OR
    TARGET.[GapInsuranceCommission] <> SOURCE.[GapInsuranceCommission] OR
    TARGET.[HasWarranty] <> SOURCE.[HasWarranty] OR
    TARGET.[HasShortWarranty] <> SOURCE.[HasShortWarranty] OR
    TARGET.[WarrantySale] <> SOURCE.[WarrantySale] OR
    TARGET.[WarrantyCost] <> SOURCE.[WarrantyCost] OR
	TARGET.[HasWheelGuard] IS NULL OR
    TARGET.[HasWheelGuard] <> SOURCE.[HasWheelGuard] OR
    TARGET.[WheelGuardSale] <> SOURCE.[WheelGuardSale] OR
    TARGET.[WheelGuardCost] <> SOURCE.[WheelGuardCost] OR
    TARGET.[WheelGuardCommission] <> SOURCE.[WheelGuardCommission] OR
    TARGET.[HasTyreInsurance] <> SOURCE.[HasTyreInsurance] OR
    TARGET.[TyreInsuranceSale] <> SOURCE.[TyreInsuranceSale] OR
    TARGET.[TyreInsuranceCost] <> SOURCE.[TyreInsuranceCost] OR
    TARGET.[HasAlloyInsurance] <> SOURCE.[HasAlloyInsurance] OR
    TARGET.[AlloyInsuranceSale] <> SOURCE.[AlloyInsuranceSale] OR
    TARGET.[AlloyInsuranceCost] <> SOURCE.[AlloyInsuranceCost] OR
    TARGET.[AlloyInsuranceCommission] <> SOURCE.[AlloyInsuranceCommission] OR
    TARGET.[HasTyreAndAlloyInsurance] <> SOURCE.[HasTyreAndAlloyInsurance] OR
    TARGET.[TyreAndAlloyInsuranceSale] <> SOURCE.[TyreAndAlloyInsuranceSale] OR
    TARGET.[TyreAndAlloyInsuranceCost] <> SOURCE.[TyreAndAlloyInsuranceCost] OR
    TARGET.[TyreAndAlloyInsuranceCommission] <> SOURCE.[TyreAndAlloyInsuranceCommission] OR
    TARGET.[TotalProductCount] <> SOURCE.[TotalProductCount] OR
	TARGET.[LastAdvertisedPrice] <> SOURCE.[LastAdvertisedPrice] OR
	TARGET.[IsVatQ] <> SOURCE.[IsVatQ] OR
	ISNULL(TARGET.[VehicleAdvert_Id],0) <> ISNULL(SOURCE.[VehicleAdvert_Id],0) 
	) 
	
	
	THEN UPDATE SET 
		TARGET.[Description]=SOURCE.[Description],
		TARGET.[DealId]=SOURCE.DealId,
		TARGET.[Reg]=SOURCE.[Reg],
		TARGET.[StockDate]=SOURCE.[StockDate],
		TARGET.[RegisteredDate]=SOURCE.[RegisteredDate],
		TARGET.[Customer]=SOURCE.[Customer],
		TARGET.[OrderDate]=SOURCE.[OrderDate],
		TARGET.[ActualDeliveryDate]=SOURCE.[ActualDeliveryDate],
		TARGET.[IsDelivered]=SOURCE.[IsDelivered],
		TARGET.[Discount]=SOURCE.[Discount],
		TARGET.[FuelSale]=SOURCE.[FuelSale],
		TARGET.[FuelCost]=SOURCE.[FuelCost],
		TARGET.[OemDeliverySale]=SOURCE.[OemDeliverySale],
		TARGET.[OemDeliveryCost]=SOURCE.[OemDeliveryCost],
		TARGET.[AccessoriesSale]=SOURCE.[AccessoriesSale],
		TARGET.[AccessoriesCost]=SOURCE.[AccessoriesCost],
		TARGET.[NewBonus1]=SOURCE.[NewBonus1],
		TARGET.[NewBonus2]=SOURCE.[NewBonus2],
		TARGET.[PartExOverAllowance1]=SOURCE.[PartExOverAllowance1],
		TARGET.[Site_Id]=SOURCE.[Site_Id],
		TARGET.[Salesman_Id]=SOURCE.[Salesman_Id],
		TARGET.[StockNumber]=SOURCE.[StockNumber],
		TARGET.[InvoiceDate]=SOURCE.[InvoiceDate],
		TARGET.[IsLateCost]=SOURCE.[IsLateCost],
		TARGET.[BrokerCost]=SOURCE.[BrokerCost],
		TARGET.[IntroCommission]=SOURCE.[IntroCommission],
		TARGET.[VehicleType_Id]=SOURCE.[VehicleType_Id],
		TARGET.[Model]=SOURCE.[Model],
		TARGET.[ModelYear]=SOURCE.[ModelYear],
		TARGET.[IsInvoiced]=SOURCE.[IsInvoiced],
		TARGET.[FinanceCo]=SOURCE.[FinanceCo],
		TARGET.[Sale]=SOURCE.[Sale],
		TARGET.[CoS]=SOURCE.[CoS],
		TARGET.[MechPrep]=SOURCE.[MechPrep],
		TARGET.[BodyPrep]=SOURCE.[BodyPrep],
		TARGET.[Error]=SOURCE.[Error],
		TARGET.[StandardWarrantyCost]=SOURCE.[StandardWarrantyCost],
		TARGET.[HasPaintProtectionAccessory]=SOURCE.[HasPaintProtectionAccessory],
		TARGET.[PaintProtectionAccessorySale]=SOURCE.[PaintProtectionAccessorySale],
		TARGET.[PaintProtectionAccessoryCost]=SOURCE.[PaintProtectionAccessoryCost],
		TARGET.[Franchise_Id]=SOURCE.[Franchise_Id],
		TARGET.[OrderType_Id]=SOURCE.[OrderType_Id],
		TARGET.[VehicleClass_Id]=SOURCE.[VehicleClass_Id],
		TARGET.[TotalNLProfit]=SOURCE.[TotalNLProfit],
		TARGET.[PDICost]=SOURCE.[PDICost],
		TARGET.[Units]=SOURCE.[Units],
		TARGET.[HandoverDate]=SOURCE.[HandoverDate],
		TARGET.[EnquiryNumber]=SOURCE.[EnquiryNumber],
		TARGET.[VehicleSource]=SOURCE.[VehicleSource],
		TARGET.[AccountingDate]=SOURCE.[AccountingDate],
		TARGET.[Other]=SOURCE.[Other],
		TARGET.[VatCost]=SOURCE.[VatCost],
		TARGET.[OemReference]=SOURCE.[OemReference],
		TARGET.[LastPhysicalLocation]=SOURCE.[LastPhysicalLocation],
		TARGET.[DeliverySite_Id]=SOURCE.[DeliverySite_Id],
		TARGET.[IsClosed]=SOURCE.[IsClosed],
		TARGET.[AuditPass]=SOURCE.[AuditPass],
		TARGET.[FinanceType]=SOURCE.[FinanceType],
		TARGET.[IsFinanced]=SOURCE.[IsFinanced],
		TARGET.[FinanceCommission]=SOURCE.[FinanceCommission],
		TARGET.[FinanceSubsidy]=SOURCE.[FinanceSubsidy],
		TARGET.[SelectCommission]=SOURCE.[SelectCommission],
		TARGET.[StandardsCommission]=SOURCE.[StandardsCommission],
		TARGET.[RCIFinanceCommission]=SOURCE.[RCIFinanceCommission],
		TARGET.[ProPlusCommission]=SOURCE.[ProPlusCommission],
		TARGET.[HasServicePlan]=SOURCE.[HasServicePlan],
		TARGET.[ServicePlanSale]=SOURCE.[ServicePlanSale],
		TARGET.[ServicePlanCost]=SOURCE.[ServicePlanCost],
		TARGET.[HasCosmeticInsurance]=SOURCE.[HasCosmeticInsurance],
		TARGET.[CosmeticInsuranceSale]=SOURCE.[CosmeticInsuranceSale],
		TARGET.[CosmeticInsuranceCost]=SOURCE.[CosmeticInsuranceCost],
		TARGET.[CosmeticInsuranceCommission]=SOURCE.[CosmeticInsuranceCommission],
		TARGET.[HasPaintProtection]=SOURCE.[HasPaintProtection],
		TARGET.[PaintProtectionSale]=SOURCE.[PaintProtectionSale],
		TARGET.[PaintProtectionCost]=SOURCE.[PaintProtectionCost],
		TARGET.[HasGapInsurance]=SOURCE.[HasGapInsurance],
		TARGET.[GapInsuranceSale]=SOURCE.[GapInsuranceSale],
		TARGET.[GapInsuranceCost]=SOURCE.[GapInsuranceCost],
		TARGET.[GapInsuranceCommission]=SOURCE.[GapInsuranceCommission],
		TARGET.[HasWarranty]=SOURCE.[HasWarranty],
		TARGET.[HasShortWarranty]=SOURCE.[HasShortWarranty],
		TARGET.[WarrantySale]=SOURCE.[WarrantySale],
		TARGET.[WarrantyCost]=SOURCE.[WarrantyCost],
		TARGET.[HasWheelGuard]=SOURCE.[HasWheelGuard],
		TARGET.[WheelGuardSale]=SOURCE.[WheelGuardSale],
		TARGET.[WheelGuardCost]=SOURCE.[WheelGuardCost],
		TARGET.[WheelGuardCommission]=SOURCE.[WheelGuardCommission],
		TARGET.[HasTyreInsurance]=SOURCE.[HasTyreInsurance],
		TARGET.[TyreInsuranceSale]=SOURCE.[TyreInsuranceSale],
		TARGET.[TyreInsuranceCost]=SOURCE.[TyreInsuranceCost],
		TARGET.[HasAlloyInsurance]=SOURCE.[HasAlloyInsurance],
		TARGET.[AlloyInsuranceSale]=SOURCE.[AlloyInsuranceSale],
		TARGET.[AlloyInsuranceCost]=SOURCE.[AlloyInsuranceCost],
		TARGET.[AlloyInsuranceCommission]=SOURCE.[AlloyInsuranceCommission],
		TARGET.[HasTyreAndAlloyInsurance]=SOURCE.[HasTyreAndAlloyInsurance],
		TARGET.[TyreAndAlloyInsuranceSale]=SOURCE.[TyreAndAlloyInsuranceSale],
		TARGET.[TyreAndAlloyInsuranceCost]=SOURCE.[TyreAndAlloyInsuranceCost],
		TARGET.[TyreAndAlloyInsuranceCommission]=SOURCE.[TyreAndAlloyInsuranceCommission],
		TARGET.[TotalProductCount]=SOURCE.[TotalProductCount],
		TARGET.[LastAdvertisedPrice] = SOURCE.[LastAdvertisedPrice] ,
		TARGET.[IsVatQ] = SOURCE.[IsVatQ],
		TARGET.[VehicleAdvert_Id] = SOURCE.[VehicleAdvert_Id]

		-- DELETE
		WHEN NOT MATCHED BY SOURCE 
				AND CONVERT(date,TARGET.AccountingDate) BETWEEN @AccountingDateStart AND @AccountingDateEnd 
				AND EXISTS (
					-- Ensure that only rows belonging to the target DealerGroup_Id are deleted
					SELECT 1
					FROM Sites si
					WHERE si.Id = target.Site_Id
					AND si.DealerGroup_Id = @dealerGroupId
				)
				THEN 
		DELETE; 
		

		DROP TABLE #HighestUniqueIdsForFullDeals
		DROP TABLE #latestAdvertised
		END
		GO


