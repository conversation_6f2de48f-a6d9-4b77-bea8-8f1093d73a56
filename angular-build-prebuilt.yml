trigger:
  tags:
    include:
    - webapp*
    - all*

pool:
  default

jobs:
- job: PackagePrebuiltAngularApp
  displayName: 'Package Prebuilt Angular App'

  steps:

  - checkout: self
    fetchDepth: 1
    fetchTags: false
    clean: true
    persistCredentials: true

  # - script: |
  #     echo "##[group]Verifying contents of live folder"
  #     dir $(Build.SourcesDirectory)\CPHI.Spark.WebApp\live
  #     echo "##[endgroup]"
  #   displayName: 'Check Prebuilt Folder Contents (Optional Diagnostic)'

  - task: ArchiveFiles@2
    displayName: 'Zip Prebuilt App'
    inputs:
      rootFolderOrFile: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/live'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/rrgProdPrebuilt_$(Build.BuildId).zip'
      replaceExistingArchive: true

  - task: PublishBuildArtifacts@1
    displayName: 'Publish Prebuilt Artifact'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/rrgProdPrebuilt_$(Build.BuildId).zip'
      ArtifactName: 'RrgProdPrebuilt'
      publishLocation: 'Container'

  - task: DeleteFiles@1
    displayName: 'Clean Up Output Folder'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/live'
      Contents: '*'
      RemoveSourceFolder: true
