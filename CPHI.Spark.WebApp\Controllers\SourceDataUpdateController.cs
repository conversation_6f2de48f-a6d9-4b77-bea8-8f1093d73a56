﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.WebApp.Service;
using Microsoft.Exchange.WebServices.Data;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize] // Removed Authorize as its used by Loader.
    public class SourceDataUpdateController : ControllerBase
    {

        private readonly ISourceDataUpdateService sourceDataUpdateService;
        


        public SourceDataUpdateController(ISourceDataUpdateService sourceDataUpdateService)
        {
            this.sourceDataUpdateService = sourceDataUpdateService;
        }


        // GET: https://sparkapi.cphi.co.uk/api/SourceDataUpdate/UpdateApiFor?cacheName=Users&dealerGroup=MjMotorCo  can do through browser
        [HttpGet]
        [AllowAnonymous]
        [Route("UpdateApiFor")]
        public async Task<string> UpdateApiFor(string cacheName, DealerGroupName dealerGroup)
        {
            await sourceDataUpdateService.ResetCaches(cacheName, dealerGroup);

            //var response = new HttpResponseMessage(HttpStatusCode.OK);
            //response.Content = new StringContent($"updated api for cachename ${cacheName}");
            return $"updated api for cachename \"{cacheName}\"";
        }



    }
}