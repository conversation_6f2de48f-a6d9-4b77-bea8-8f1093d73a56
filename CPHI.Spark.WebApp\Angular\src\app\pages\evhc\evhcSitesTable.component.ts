import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GridApi, RowClickedEvent } from 'ag-grid-community';
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from "../../cph.pipe";
import { EvhcSiteRow } from '../../model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from "../../services/constants.service";
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from "../../services/selections.service";
import { EhvcService } from "./evhc.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";

@Component({
  selector: "evhcSitesTable",

  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        id="EVHCTable"
        class="ag-theme-balham"
        [gridOptions]="mainTableGridOptions"
        domLayout="autoHeight"
        
      >
      </ag-grid-angular>
    </div>
  `,

  styleUrls: ["./../../../styles/components/_agGrid.scss"],
  styles: [
    `

    `,
  ],
})
export class EvhcSitesTableComponent implements OnInit {
  @Input() public isRegional: boolean;

  @Output() rowClicked = new EventEmitter();

  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;

  columnDefs: any[];
  mainTableGridOptions: GridOptionsCph;
  showGrid: boolean;
  pinnedTop: EvhcSiteRow[];

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public router: Router,
    public excel: ExcelExportService,
    public service: EhvcService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.mainTableGridOptions.context = { thisComponent: this };
    this.gridApi.sizeColumnsToFit();
    // this.selections.triggerSpinner.next({ show: false });
  }

  ngOnInit() {
    this.initParams();
    this.showGrid = false;
    this.service.numbersHaveChanged.subscribe(res => {
      this.refreshTable();
    })
  }

  initParams() {
    this.initTopRowData();

    // table definitions
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false, cellClass: "ag-right-aligned-cell",
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
      },
      
      rowData: this.getRowData(),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onRowClicked: (params) => {
        this.onRowClick(params);
      },
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
      onGridReady: (params) => this.onGridReady(params),
      pinnedTopRowData: this.pinnedTop,
      pinnedBottomRowData: this.getPinnedBottomRowData(),
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },
      columnDefs: [
        { headerName: "", field: "Label", colId: "Label", width: 200, type: "label", },
        { headerName: "Vehicles Through", field: "VehiclesThrough", colId: "VehiclesThrough", width: 170, type: "number", },
        {
          headerName: "Vehicles Checked", children:
            [
              { headerName: "#", field: "VehiclesChecked", colId: "VehiclesChecked", width: 120, type: "number", },
              { headerName: "%", field: "VehiclesCheckedPercent", colId: "VehiclesCheckedPercent", width: 120, type: "percent", },
            ],
        },
        {
          headerName: "Work Quoted", children:
            [
              { headerName: "£", field: "WorkQuoted", colId: "WorkQuoted", width: 120, type: "currency", },
              { headerName: "£/vehicle", field: "WorkQuotedVehicle", colId: "WorkQuotedVehicle", width: 120, type: "currency", },
            ],
        },
        {
          headerName: "Work Sold", children:
            [
              { headerName: "£", field: "WorkSold", colId: "WorkSold", width: 120, type: "currency", },
              { headerName: "£/vehicle", field: "WorkSoldPerVehicle", colId: "WorkSoldPerVehicle", width: 120, type: "currency", },
              { headerName: "%", field: "WorkSoldPercent", colId: "WorkSoldPercent", width: 120, type: "percent", },
            ],
        },
        { headerName: "Opportunity", field: "Opportunity", colId: "Opportunity", width: 120, type: "currency", },
      ]
    }
  }
  getPinnedBottomRowData(): any[] {
    return this.service.siteRows.filter(r => r.IsTotal)
  }



  initTopRowData() {
    this.pinnedTop = [];

    if (this.isRegional) return;

    this.pinnedTop.push(
      {
        Label: this.constants.translatedText.Target,
        VehiclesCheckedPercent: this.constants.environment.evhc_vehiclesCheckedPercent / 100,
        WorkQuotedVehicle: this.constants.environment.evhc_workQuoted,
        WorkSoldPercent: this.constants.environment.evhc_workSoldPercent / 100
      } as EvhcSiteRow


    )

  }



  onRowClick(event: RowClickedEvent): void {
    if (event.rowPinned == 'top') return;

    this.rowClicked.next(event.data);
  }


  refreshTable(): void {
    if (!this.gridApi) return
    //this.initTopRowData();
    //this.gridApi.setPinnedTopRowData(this.pinnedTop);
    this.gridApi.setRowData(this.getRowData());
    this.gridApi.setPinnedBottomRowData(this.getPinnedBottomRowData())
    this.gridApi.sizeColumnsToFit();
  }


  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Vehicle Health Check Summary', 1, 1);
  }

  getRowData(): any[] {
    return this.service.siteRows.filter(x => this.isRegional && x.IsRegion || !this.isRegional && x.IsSite)
  }







}


