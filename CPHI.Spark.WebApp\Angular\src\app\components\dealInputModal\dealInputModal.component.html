<ng-template #dealInputModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            New Deal <span *ngIf="newDeal.customer!==''">- {{newDeal.customer}}</span>
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="d-flex justify-content-between">

            <table id="layoutTable">
                <tbody>
                    <tr>
                        <td id="dealDetails" class="mainLayoutTableCell">
                            <!-- Deal details -->
                            <ng-container *ngTemplateOutlet="dealDetailsCard"></ng-container>
                        </td>

                        <td id="tinAndOther" class="mainLayoutTableCell">
                            <!-- Tin profit -->
                            <ng-container *ngTemplateOutlet="tinProfit"></ng-container>
                            <!-- Other profit -->
                            <ng-container *ngTemplateOutlet="otherProfit"></ng-container>
                        </td>

                        <td id="financeAndAddOns" class="mainLayoutTableCell">
                            <!-- Finance profit -->
                            <ng-container *ngTemplateOutlet="financeProfit"></ng-container>
                            <!-- Add-on profit -->
                            <ng-container *ngTemplateOutlet="addOnProfit"></ng-container>
                            <!-- Total profit -->
                            <ng-container *ngTemplateOutlet="totalProfit"></ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
           
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">
            {{ constantsService.translatedText.Close }}
        </button>
        <button type="button" class="btn btn-success" (click)="saveNewDeal()">
            Save
        </button>
    </div>
</ng-template>




<ng-template #dealDetailsCard>
    <div class="table-card">
        <table>
            <thead>
                <tr>
                    <th colspan="2">Deal Details</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Site</td>
                    <td>
                        <div ngbDropdown class="d-inline-block fullWidthDropdown" [autoClose]="true">
                            <button class="btn btn-primary" ngbDropdownToggle>
                                Orpington (14)
                            </button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Customer</td>
                    <td><input type="text" class="fullWidth" id="customerNameInput" [(ngModel)]="newDeal.customer">
                    </td>
                </tr>
                <tr>
                    <td>Make</td>
                    <td>
                        <simpleTypeahead [searchList]="makes" [choiceName]="'make'" (onChosenItem)="setChoice('make',$event)" [chosenString]="newDeal.make">
                        </simpleTypeahead>
                    </td>
                </tr>
                <tr>
                    <td>Model</td>
                    <td>
                        <simpleTypeahead  (onChosenItem)="setChoice('model',$event)" [choiceName]="'model'"
                        [searchList]="models" [chosenString]="newDeal.model" ></simpleTypeahead>
                    </td>
                </tr>
                <tr>
                    <td>Derivative</td>
                    <td>
                        <simpleTypeahead  (onChosenItem)="setChoice('derivative',$event)" [choiceName]="'derivative'"
                        [searchList]="derivatives" [chosenString]="newDeal.derivative" ></simpleTypeahead>
                    </td>
                </tr>
                <tr>
                    <td>Order Type</td>
                    <td>
                        <simpleTypeahead  (onChosenItem)="setChoice('orderType',$event)" [choiceName]="'orderType'"
                        [searchList]="orderTypes" [chosenString]="newDeal.orderType" ></simpleTypeahead>
                    </td>
                </tr>
                <tr>
                    <td>Finance Type</td>
                    <td>
                        <simpleTypeahead  (onChosenItem)="setChoice('financeType',$event)" [choiceName]="'financeType'"
                        [searchList]="financeTypeNames" [chosenString]="newDeal.financeType" ></simpleTypeahead>
                    </td>
                </tr>
                <tr>
                    <td>Exec</td>
                    <td>
                        <simpleTypeahead  (onChosenItem)="setChoice('exec',$event)" [choiceName]="'exec'"
                        [searchList]="execs" [chosenString]="newDeal.salesExec" ></simpleTypeahead>
                    </td>
                </tr>
                <tr>
                    <td>Order Date</td>
                    <td>
                        <input type="date" class="fullWidth" name="orderDate" [(ngModel)]="newDeal.orderDate">
                    </td>
                </tr>
                <tr>
                    <td>Delivery Date</td>
                    <td>
                        <input type="date" class="fullWidth" name="deliveryDate" [(ngModel)]="newDeal.deliveryDate">
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #tinProfit>
    <div class="table-card">
        <table>
            <thead>
                <tr>
                    <th colspan="5">Vehicle Profit</th>
                </tr>
                <tr>
                    <th></th>
                    <th>Sale inc VAT</th>
                    <th>Cost</th>
                    <th>Profit</th>
                    <th>%</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Base</td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.saleIncVatBase| cph:'number':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.baseCoS| cph:'number':0 }}</div>
                        
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.baseMargin| cph:'number':0 }}</div>
                    </td>
                    <td><div class="likeAnInput">{{ newDeal.tinProfit.baseMarginPct| cph:'percent':0 }}
                    </div></td>
                </tr>
                <tr>
                    <td>Options</td>
                    <td>
                        <input type="number" [ngModel]="newDeal.tinProfit.saleIncVatOptions"
                            (ngModelChange)="newDeal.tinProfit.debounceSet('saleIncVatOptions',$event)">
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.optionsCoS| cph:'number':0 }}</div>
                        
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.optionsMargin| cph:'number':0 }}</div>
                    </td>
                    <td><div class="likeAnInput">{{ newDeal.tinProfit.optionsMarginPct| cph:'percent':0 }}
                    </div></td>
                </tr>
                <tr class="subtotal">
                    <td>Total RRP</td>
                    <td>
                        <input type="number" [ngModel]="newDeal.tinProfit.saleIncVatRRP"
                            (ngModelChange)="newDeal.tinProfit.debounceSet('saleIncVatRRP',$event)">
                    </td>
                    <td colspan="3"></td>
                </tr>
                <tr>
                    <td>Discount</td>
                    <td>
                        <input type="number" [ngModel]="newDeal.tinProfit.inputDiscount"
                            (ngModelChange)="newDeal.tinProfit.debounceSet('discount',$event)">
                    </td>
                    <td></td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.discountProfit | cph:'number':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.discountPct| cph:'percent':1 }}</div>
                    </td>
                </tr>
                <tr class="subtotal">
                    <td>Total </td>
                    <td><input type="number" [ngModel]="newDeal.tinProfit.saleIncVatTotal"
                            (ngModelChange)="newDeal.tinProfit.debounceSet('saleIncVatTotal',$event)"></td>
                    <td><div class="likeAnInput">{{ newDeal.tinProfit.costOfSale| cph:'number':0 }}</div></td>
                    <td> <div class="likeAnInput">{{ newDeal.tinProfit.profitBeforeManufacturerSupport|
                        cph:'number':0 }}</div></td>
                    <td><div class="likeAnInput">{{ newDeal.tinProfit.profitBeforeManufacturerSupportPct|
                        cph:'percent':1 }} </div></td>

                </tr>
                <!-- <tr>
                    <td>Ex VAT</td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.saleExVatTotal| cph:'number':0 }}
                        </div>
                    </td>
                    <td>
                        
                    </td>
                    <td>
                       
                    </td>
                    <td>
                        
                    </td>
                    <td></td>
                </tr> -->
                <tr>
                    <td colspan="2">Manufacturer Support</td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.manufacturerSupport| cph:'number':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.manufacturerSupportProfit|
                            cph:'number':0 }}</div>
                    </td>
                    <td>
                        <div class="likeAnInput">
                            {{newDeal.tinProfit.manufacturerSupportProfitPct|cph:'percent':1}}</div>
                    </td>
                </tr>

                <tr class="subtotal">
                    <td>Total Vehicle Profit</td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.saleExVatTotal| cph:'currency':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.totalTinCost | cph:'currency':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.totalTinProfit| cph:'currency':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.tinProfit.totalTinProfitPct | cph:'percent':1 }}
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #otherProfit>
    <div class="table-card">
        <table>
            <thead>
                <tr>
                    <th colspan="4">Other Profit</th>
                </tr>
                <tr>
                    <th></th>
                    <th>Sale</th>
                    <th>Cost</th>
                    <th>Profit</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Broker Cost</td>
                    <td></td>
                    <td><input type="number" [(ngModel)]="newDeal.accessories.brokerCost"></td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.accessories.brokerProfit | cph:'currency':0 }}
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Accessories</td>
                    <td><input type="number" [(ngModel)]="newDeal.accessories.accessorySale"></td>
                    <td><input type="number" [(ngModel)]="newDeal.accessories.accessoryCost"></td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.accessories.accessoryProfit| cph:'currency':0 }}
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Fuel</td>
                    <td><input type="number" [(ngModel)]="newDeal.accessories.fuelSale"></td>
                    <td><input type="number" [(ngModel)]="newDeal.accessories.fuelCost"></td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.accessories.fuelProfit| cph:'currency':0 }}
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Delivery</td>
                    <td><input type="number" [(ngModel)]="newDeal.accessories.deliverySale"></td>
                    <td><input type="number" [(ngModel)]="newDeal.accessories.deliveryCost"></td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.accessories.deliveryProfit| cph:'currency':0 }}
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>PDI</td>
                    <td></td>
                    <td><input type="number" [(ngModel)]="newDeal.accessories.prepCost"></td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.accessories.prepProfit | cph:'currency':0 }}
                        </div>
                    </td>
                    <td></td>
                </tr>
                <tr class="subtotal">
                    <td>Total Other Profit</td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.accessories.totalAccessorySale| cph:'currency':0
                            }}</div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.accessories.totalAccessoryCost| cph:'currency':0
                            }}</div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.accessories.totalAccessoryProfit|
                            cph:'currency':0 }}</div>
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #financeProfit>
    <div class="table-card">
        <table>
            <thead>
                <tr>
                    <th colspan="4">Finance Profit</th>
                </tr>
                <tr>
                    <th></th>
                    <th>Sale</th>
                    <th>Cost</th>
                    <th>Profit</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Finance Commission</td>
                    <td></td>
                    <td>
                        <input type="number" [(ngModel)]="newDeal.finance.financeCommissionCost">
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.finance.financeCommissionProfit |
                            cph:'currency':0 }}</div>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Finance Subsidy</td>
                    <td></td>
                    <td><input type="number" [(ngModel)]="newDeal.finance.financeSubsidyCost "></td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.finance.financeSubsidyProfit| cph:'currency':0
                            }}</div>
                    </td>
                    <td></td>
                </tr>
                <tr class="subtotal">
                    <td>Total Finance Profit</td>
                    <td></td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.finance.totalFinanceCost | cph:'currency':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.finance.totalFinanceProfit| cph:'currency':0 }}
                        </div>
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #addOnProfit>
    <div class="table-card">
        <table>
            <thead>
                <tr>
                    <th>Add-On Profit</th>
                    <th colspan="3">
                        <!-- Add product dropdown -->
                        <div ngbDropdown class="d-inline-block" [autoClose]="true">
                            <button class="btn btn-primary" ngbDropdownToggle>
                                Choose Product
                            </button>
                            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                                <button *ngFor="let addOn of addOnProducts" (click)="newDeal.addOns.addAddOn(addOn)"
                                    ngbDropdownItem>
                                    {{ addOn.name }}
                                </button>
                            </div>
                        </div>
                    </th>
                </tr>
                <tr>
                    <th></th>
                    <th>Sale</th>
                    <th>Cost</th>
                    <th>Profit</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngIf="newDeal.addOns.addOns.length===0">
                    <td>None Chosen</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td></td>
                </tr>
                <ng-container *ngIf="newDeal.addOns.addOns.length > 0">
                    <tr *ngFor="let addOn of newDeal.addOns.addOns">
                        <td>{{ addOn.name }}</td>
                        <td>
                            <div class="likeAnInput">{{ addOn.sale | cph:'currency':0 }}</div>
                        </td>
                        <td>
                            <div class="likeAnInput">{{ addOn.cos | cph:'currency':0 }}</div>
                        </td>
                        <td>
                            <div class="likeAnInput">{{ addOn.profit | cph:'currency':0 }}</div>
                        </td>
                        <td>
                            <button class="btn btn-danger" (click)="newDeal.addOns.removeAddOn(addOn)"> &times;
                            </button>
                        </td>
                    </tr>
                </ng-container>
                <tr>
                    <td>

                    </td>
                    <td colspan="4"></td>
                </tr>
                <tr class="subtotal">
                    <td>Total Add-On Profit</td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.addOns.totalAddOnSales| cph:'currency':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.addOns.totalAddOnCosts | cph:'currency':0 }}
                        </div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.addOns.totalAddOnProfits | cph:'currency':0 }}
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</ng-template>

<ng-template #totalProfit>
    <div class="table-card">
        <table>
            <thead>
                <tr>

                </tr>
                <tr>
                    <th></th>
                    <th>Sale</th>
                    <th>Cost</th>
                    <th>Profit</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr class="subtotal">
                    <td>Total Profit</td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.totalSales | cph:'currency':0 }}</div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.totalCosts| cph:'currency':0 }}</div>
                    </td>
                    <td>
                        <div class="likeAnInput">{{ newDeal.totalProfit| cph:'currency':0 }}</div>
                    </td>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</ng-template>