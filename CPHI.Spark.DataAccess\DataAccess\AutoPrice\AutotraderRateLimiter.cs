﻿using System.Threading.RateLimiting;

namespace CPHI.Spark.DataAccess.DataAccess.AutoPrice
{
   public static class AutotraderRateLimiter
   {
      public static FixedWindowRateLimiter FutureValuationsLimiter { get; }
      public static FixedWindowRateLimiter StockLimiter { get; }
      public static SlidingWindowRateLimiter TaxonomyLimiter { get; }
      public static SlidingWindowRateLimiter ValuationsLimiter { get; }
      public static FixedWindowRateLimiter VehicleMetricsLimiter { get; }
      public static FixedWindowRateLimiter VehiclesLimiter { get; }

      // Semaphore to limit the total number of concurrent requests across all clients
      public static SemaphoreSlim ConcurrencyLimiter { get; }




      static AutotraderRateLimiter()
      {
         // Using slightly reduced limits for safety margin
         FutureValuationsLimiter = new FixedWindowRateLimiter(BuildFixedWindowOptions(24)); //thought it was 50 but error message says 30
         StockLimiter = new FixedWindowRateLimiter(BuildFixedWindowOptions(200));  //quotes it as being 220
         TaxonomyLimiter = new SlidingWindowRateLimiter(BuildSlidingWindowOptions(48));
         ValuationsLimiter = new SlidingWindowRateLimiter(BuildSlidingWindowOptions(75));
         VehicleMetricsLimiter = new FixedWindowRateLimiter(BuildFixedWindowOptions(95));  //quotes the limit as being 100
         VehiclesLimiter = new FixedWindowRateLimiter(BuildFixedWindowOptions(45));

         // Initialize the concurrency limiter to allow 200 concurrent requests
         ConcurrencyLimiter = new SemaphoreSlim(200, 200);
      }

      private static SlidingWindowRateLimiterOptions BuildSlidingWindowOptions(int limit)
      {
         return new SlidingWindowRateLimiterOptions
         {
            PermitLimit = limit,
            Window = TimeSpan.FromSeconds(1),
            SegmentsPerWindow = 10, // Divides the window into 4 segments for smoother rate limiting
            QueueLimit = 300000,
            QueueProcessingOrder = QueueProcessingOrder.OldestFirst
         };
      }

      private static FixedWindowRateLimiterOptions BuildFixedWindowOptions(int limit)
      {
         int ms = (int)Math.Round(Math.Ceiling(1000 / (decimal)limit), 0);
         return new FixedWindowRateLimiterOptions
         {
            PermitLimit = 1,
            Window = TimeSpan.FromMilliseconds(ms),
            //SegmentsPerWindow = 10, // Divides the window into 4 segments for smoother rate limiting
            QueueLimit = 300000,
            QueueProcessingOrder = QueueProcessingOrder.OldestFirst
         };
      }
   }
}
