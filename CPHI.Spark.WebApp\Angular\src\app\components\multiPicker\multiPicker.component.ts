import { Component, Input, OnInit } from '@angular/core';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'multiPicker',
  templateUrl: './multiPicker.component.html',
  styleUrls: ['./multiPicker.component.scss']
})
export class MultiPickerComponent implements OnInit {

 

  @Input() menuItems: string[] ; // Array of unique car models from the parent component
  @Input() label: string ; // Array of unique car models from the parent component
  @Input() onChosenItemsChange: ()=>void;
  @Input() chosenItems: Set<string> = new Set<string>(); // Set to store active items

  constructor() {}

  ngOnInit(): void {
    
  }

  toggleSelection(item: string): void {
    if (this.chosenItems.has(item)) {
      this.chosenItems.delete(item);
    } else {
      this.chosenItems.add(item);
    }
  }

  isItemSelected(item: string): boolean {
    return this.chosenItems.has(item);
  }

  okButtonPushed(dropdown:NgbDropdown): void {
    this.onChosenItemsChange();
    this.closeDropdown(dropdown)
  }




  toggleSelectAll() {
    if (this.chosenItems.size === this.menuItems.length) {
      // If all items are selected, deselect all
      this.chosenItems.clear();
    } else {
      // If at least one item is not selected, select all
      this.menuItems.forEach(item => this.chosenItems.add(item));
    }
  }


  

  closeDropdown(dropdown: NgbDropdown) {
    dropdown.close(); // Close the dropdown
  }


}
