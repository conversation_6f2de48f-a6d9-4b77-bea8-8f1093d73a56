﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class GetOptOutsParams
   {
      public DateTime ChosenDate { get; set; }
      public List<int> RetailerSiteIds { get; set; }
      public bool IncludeNewVehicles { get; set; }
      public bool IncludeUnPublishedAds { get; set; }
      public List<string> LifeCycleStatuses { get; set; }
   }
}

