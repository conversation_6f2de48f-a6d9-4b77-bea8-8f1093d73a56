﻿using Microsoft.AspNetCore.Mvc;
using System;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using CPHI.Spark.WebApp.Service;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.Services;
using CPHI.Spark.WebApp.Service.AutoPrice;
using CPHI.Spark.WebApp.Service.Autoprice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model;
using System.Linq;
using CPHI.Repository;

namespace CPHI.Spark.WebApp.Controllers
{
   [Route("api/[controller]")]
   [ApiController]
   [Authorize]
   public class AutoPriceController : ControllerBase
   {
      private readonly IPricesService pricesService;
      private readonly IAdvertModalService advertModalService;
      private readonly IAutoPriceDashboardService autoPriceDashboardService;
      private readonly IOptOutsService optOutsService;
      private readonly IPriceChangesService priceChangesService;
      private readonly ITableStatesService tableStatesService;
      private readonly IVehicleAdvertService vehicleAdvertService;
      private readonly IVehicleValuationService vehicleValuationService;
      private readonly DealerGroupName userDealerGroupName;
      private readonly List<int> userRetailerSiteIds;
      private readonly ILocalBargainsService localBargainsService;
      private readonly ISiteSettingsService siteSettingsService;
      private readonly IAutoPriceNewDealerGroupService autoPriceNewDealerGroupService;
      private readonly int userId;
      private readonly IUserService _userService;

      public AutoPriceController(
                                 IUserService userService,
                                 IPricesService pricesService,
                                 IAdvertModalService advertModalService,
                                 IAutoPriceDashboardService autoPriceDashboardService,
                                 IOptOutsService optOutsService,
                                 IPriceChangesService priceChangesService,
                                 ITableStatesService tableStatesService,
                                 IVehicleAdvertService vehicleAdvertService,
                                 IVehicleValuationService vehicleValuationService,
                                 ILocalBargainsService localBargainsService,
                                 ISiteSettingsService siteSettingsService,
                                 IAutoPriceNewDealerGroupService autoPriceNewDealerGroupService)
      {
         this.userDealerGroupName = userService.GetUserDealerGroupName();
         this.pricesService = pricesService;
         this.advertModalService = advertModalService;
         this.userRetailerSiteIds = userService.GetUserRetailerSiteIds().ToList();
         this.autoPriceDashboardService = autoPriceDashboardService;
         this.optOutsService = optOutsService;
         this.priceChangesService = priceChangesService;
         this.tableStatesService = tableStatesService;
         this.vehicleAdvertService = vehicleAdvertService;
         this.vehicleValuationService = vehicleValuationService;
         this.localBargainsService = localBargainsService;
         this.siteSettingsService = siteSettingsService;
         this.autoPriceNewDealerGroupService = autoPriceNewDealerGroupService;
         this.userId = userService.GetUserId();
         this._userService = userService;
      }



      [HttpPost]
      [Route("GetStatsDashboard")]
      public async Task<StatsDashboard> GetStatsDashboard(GetStatsDashboardParams getVehicleAdvertWithRatingsParams)
      {
         return await vehicleAdvertService.GetStatsDashboard(getVehicleAdvertWithRatingsParams, userDealerGroupName);
      }

      [HttpPost]
      [Route("GetStatsSitesDashboard")]
      public async Task<List<StatsSiteDashboard>> GetStatsSitesDashboard(GetStatsDashboardParams getVehicleAdvertWithRatingsParams)
      {
         return await vehicleAdvertService.GetStatsSitesDashboard(getVehicleAdvertWithRatingsParams, userDealerGroupName);
      }

      [HttpPost]
      [Route("GetStatsStockProfileItems")]
      public async Task<List<StockProfileItem>> GetStatsStockProfileItems(GetStatsStockProfileItemsParams parms)
      {
         return await vehicleAdvertService.GetStatsStockProfileItems(parms);
      }

      [HttpPost]
      [Route("GetSingleVehicleAdvertWithRatings")] //Used by the modal
      public async Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertWithRatings(GetVehicleAdvertsWithRatingsParams getVehicleAdvertWithRatingsParams)
      {
         return await vehicleAdvertService.GetVehicleAdvertWithRatings(getVehicleAdvertWithRatingsParams);
      }

      [HttpPost]
      [Route("GetVehicleAdvertsWithRatings")] //This is the source of data for the main advert listing page.    Also is used by the blobs dashboard page.   Uses GET_VehicleAdvertWithRatings SP
      public async Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertsWithRatings(GetVehicleAdvertsWithRatingsParams getVehicleAdvertWithRatingsParams)
      {
         return await vehicleAdvertService.GetVehicleAdvertsWithRatings(getVehicleAdvertWithRatingsParams, userDealerGroupName);
      }

      [HttpGet]
      [Route("GetVehicleOptOutStatus")]
      public async Task<VehicleOptOutStatus> GetVehicleOptOutStatus(int vehicleAdvertId)
      {
         return await optOutsService.GetVehicleOptOutStatus(vehicleAdvertId, userDealerGroupName);
      }

      [HttpPost]
      [Route("CreateOrUpdateVehicleOptOut")]
      public async Task CreateOrUpdateVehicleOptOut(VehicleOptOutParams optOutParams)
      {
         string email = await _userService.GetUserEmailFromAspNetUsers(userId, (int)userDealerGroupName);
         var usersName = await _userService.GetUsersName(email, (int)userDealerGroupName);
         await optOutsService.CreateOrUpdateVehicleOptOut(optOutParams, userDealerGroupName, usersName);
      }


      [HttpGet]
      [Route("UpdateStockPrice")]
      public async Task UpdateStockPrice(int vehicleAdvertId, int newPrice, string rrgSiteItemStockId)
      {
         await pricesService.UpdatePrice(vehicleAdvertId, newPrice, rrgSiteItemStockId);
      }

      [HttpPost]
      [Route("SaveTableState")]
      public async Task<AutoPriceTableState> SaveNewTableState(AutoPriceTableStateParams parms)
      {
         return await tableStatesService.SaveTableState(parms);
      }

      [HttpDelete]
      [Route("DeleteTableState")]
      public async Task DeleteTableState(int stateId, string pageName)
      {
         await tableStatesService.DeleteTableState(stateId, pageName);
         return;
      }

      [HttpGet]
      [Route("GetTableState")]
      public async Task<AutoPriceTableStateParams> GetTableState(string label, string pageName)
      {
         return await tableStatesService.GetTableState(label, pageName);
      }

      [HttpGet]
      [Route("GetTableStateLabels")]
      public async Task<IEnumerable<TableStateLabelAndId>> GetTableStateLabels(string pageName, bool isRenault)
      {
         return await tableStatesService.GetTableStateLabels(pageName, isRenault);
      }

      [HttpGet]
      [Route("GetLastLoadedTableState")]
      public async Task<AutoPriceTableState> GetLastLoadedTableState(string pageName)
      {
         return await tableStatesService.GetLastLoadedTableState(pageName);
      }




      [HttpGet]
      [Route("GetVehicleAdvertDetails")]
      public async Task<VehicleAdvertDetail> GetVehicleAdvertDetails(int advertId, DateTime effectiveDate)
      {
         return await vehicleAdvertService.GetVehicleAdvertDetails(advertId, effectiveDate);
      }







      [HttpGet]
      [Route("GetAnalysisDimensions")]
      public List<AnalysisDimension> GetAnalysisDimensions(string pageName)
      {
         return DimensionsService.ProvideAnalysisDimensions(pageName);
      }


      [HttpGet]
      [Route("GetAdvertModalForAdId")]
      public async Task<VehicleDetailModalItem> GetAdvertModalForAdId(int advertId)
      {
         return await advertModalService.GetAdvertModal(advertId, userDealerGroupName);
      }



      [HttpPost]
      [Route("GetAdvertModalCompetitorAnalysis")]
      public async Task<CompetitorSummary> GetAdvertModalCompetitorAnalysis(GetCompetitorAnalysisParams parms)
      {
         return await advertModalService.GetAdvertModalCompetitorAnalysis(parms, userDealerGroupName);
      }



      [HttpPost]
      [Route("GetEstimatedDayToSellAndPriceIndicator")]
      public async Task<DayToSellAndPriceIndicator> GetEstimatedDayToSellAndPriceIndicator(GetEstimatedDayToSellAndPriceIndicatorParams parms)
      {
         return await vehicleValuationService.GetEstimatedDayToSellAndPriceIndicator(parms);
      }








      [HttpPost]
      [Route("GetDashboardDataSet")]
      public async Task<DashboardDataSet> GetDashboardDataSet(GetPricingDashboardParams parms)
      {
         return await autoPriceDashboardService.GetDashboardDataSet(parms);
      }



      [HttpPost]
      [Route("GetLocationOptimiserAdverts")]
      public async Task<List<LocationOptimiserAdvert>> GetLocationOptimiserAdverts(GetLocationOptimiserAdvertsParams parms)
      {
         return await vehicleAdvertService.GetLocationOptimiserAdverts(DateTime.Today, parms);
      }


      [HttpGet]
      [Route("GetVehicleValuationBatches")]
      public async Task<List<VehicleValuationBatchDTO>> GetVehicleValuationBatches([FromQuery] bool? single)
      {
         return await vehicleValuationService.GetVehicleValuationBatches(userDealerGroupName, single);
      }

      [HttpPost]
      [Route("GetValuationBatchResults")]
      public async Task<IEnumerable<ValuationBatchResult>> GetValuationBatchResults(GetValuationBatchResultsParams parms)
      {
         return await vehicleValuationService.GetValuationBatchResults(parms, userDealerGroupName);
      }

      [HttpPost]
      [Route("GetVehicleValuationRows")]
      public async Task<IEnumerable<ValuationBatchResult>> GetVehicleValuationRows(GetValuationBatchResultsParams parms)
      {
         return await vehicleValuationService.GetValuationBatchResults(parms, userDealerGroupName);
      }

      [HttpPost]
      [Route("RevalueVehicleValuationBatch")]
      public async Task RevalueVehicleValuationBatch(RevalueVehicleValuationBatchParams parms)
      {
         //await autoPriceService.RevalueVehicleValuationBatch(parms);
      }

      [HttpGet]
      [Route("GetLocalBargains")]
      public async Task<IEnumerable<LocalBargainDetail>> GetLocalBargains(string chosenDate)
      {
         return await localBargainsService.GetLocalBargains(chosenDate);
      }





      [HttpPost]
      [Route("GetPriceChanges")]
      public async Task<IEnumerable<PricingChangeNew>> GetPriceChanges(GetPriceChangesParams parms)
      {
         return await priceChangesService.GetPriceChanges(parms);
      }

      [HttpPost]
      [Route("SetPriceChanges")]
      public async Task SetPriceChanges(SetPriceChangesParams parms)
      {
         await priceChangesService.SetPriceChanges(parms);
      }

      [HttpPost]
      [Route("GetOptOuts")]
      public async Task<IEnumerable<VehicleOptOutSummaryItemNew>> GetOptOuts(GetOptOutsParams parms)
      {
         return await optOutsService.GetVehicleOptOuts(parms);
      }




      [HttpGet]
      [Route("GetSitesSettings")]
      public async Task<IEnumerable<SiteSettings>> GetSitesSettings()
      {
         return await siteSettingsService.GetSitesSettings(userDealerGroupName);
      }

      [HttpPost]
      [Route("SaveSiteSettings")]
      public async Task<SiteSettings> SaveSiteSettings(SaveSiteSettingsParams parms)
      {

         return await siteSettingsService.SaveSiteSettings(parms, userDealerGroupName, userRetailerSiteIds, userId);
      }

      [HttpPost]
      [Route("ShareTableState")]
      public async Task ShareTableState(ShareTableStateParams parms)
      {
         await tableStatesService.ShareTableState(parms, userDealerGroupName);
      }

      [HttpGet]
      [Route("GetSharedTableStateLabels")]
      public async Task<IEnumerable<ShareTableStateDetails>> GetSharedTableStateLabels(string pageName, bool isRenault)
      {
         return await tableStatesService.GetSharedTableStateLabels(pageName, isRenault);
      }

      [HttpGet]
      [Route("GetTableStateById")]
      public async Task<AutoPriceTableStateParams> GetTableStateById(int id)
      {
         return await tableStatesService.GetTableStateById(id, userDealerGroupName);
      }

      [HttpPost]
      [Route("SetStandardTableState")]
      public async Task SetStandardTableState(StandardTableStateParams parms)
      {
         await tableStatesService.SetStandardTableState(parms);
      }

      [HttpPost]
      [Route("UnsetStandardTableState")]
      public async Task UnsetStandardTableState(List<int> tableStateIds)
      {
         await tableStatesService.UnsetStandardTableState(tableStateIds, userDealerGroupName);
      }

      [HttpGet]
      [Route("GetStandardTableStateLabels")]
      public async Task<IEnumerable<StandardTableStateDetails>> GetStandardTableStateLabels(string pageName, bool isRenault)
      {
         return await tableStatesService.GetStandardTableStateLabels(pageName, isRenault);
      }

      [HttpGet]
      [Route("GetSparkTableStateLabels")]
      public async Task<IEnumerable<StandardTableStateDetails>> GetSparkTableStateLabels(string pageName, bool isRenault)
      {
         return await tableStatesService.GetSparkTableStateLabels(pageName, isRenault);
      }

      [HttpGet]
      [Route("GetAvailableTableStateLabels")]
      public async Task<IEnumerable<AvailableTableStateDetails>> GetAvailableTableStateLabels(string pageName, bool isRenault)
      {
         return await tableStatesService.GetAvailableTableStateLabels(pageName, isRenault);
      }




      [HttpGet]
      [Route("GetTotalAdvertsForAdvertiser")]
      public async Task<int> GetTotalAdvertsForAdvertiser(string advertiserName, int advertiserId)
      {
         return await advertModalService.GetTotalAdvertsForAdvertiser(advertiserName, advertiserId);
      }

      [HttpPatch]
      [Route("TableState/{id}")]
      public async Task<bool> PatchAutoPriceTableState(int id, [FromBody] AutoPriceTableStatePatch dto)
      {
         // Get UserID, add to 

         return await tableStatesService.PatchAutoPriceTableState(id, dto, this.userId);
      }
   }
}
