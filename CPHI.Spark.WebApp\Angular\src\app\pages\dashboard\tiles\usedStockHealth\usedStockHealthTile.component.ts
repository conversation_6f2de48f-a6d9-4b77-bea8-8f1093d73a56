import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
//import { ChartService } from '../../services/chart.service'
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from "rxjs";
import { StockReportModalComponent } from "src/app/components/stockReportModal/stockReportModal.component";
import { CphPipe } from "src/app/cph.pipe";
import { Stock, StockModalRow, StockModalRowWithNext30 } from "src/app/model/sales.model";
import { AutotraderService } from "src/app/services/autotrader.service";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { UsedStockHealth } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";

interface UsedStockType {
  type: string, stocksCount: number, percent: number; barHeight: number;
}

@Component({
  selector: 'usedStockHealth',
  templateUrl: './usedStockHealth.component.html',
  styles: [
    `
    #overallHolder{position: absolute; top: 0; right: 0; left: 0; bottom: 0; padding: 0.5em; display: flex; flex-direction: column;}
    #chartContainer{width:80%;margin:0em auto;flex:1;position:relative;}
    #totalBox{margin:0.8em auto;font-weight:200; }
  .barBlock:hover .label{font-weight:500;color:black}
  .barBlock.Tactical:hover .label{font-weight:500;color:black}
    .barBlock{cursor:pointer;width:100%;display:flex;align-items:center;justify-content:center;}
  .barBlock.ExDemo{background-color:var(--badColour)}
  .barBlock.ExManagement{background-color:var(--grey60);}
  .barBlock.Tactical{background-color:var(--brightColour);}
  .barBlock.CoreUsed{background-color:var(--goodColour);border-radius: 0em 0em 0px 0px;}
  .barBlock.Demo{background-color:var(--badColour)}
  .barBlock .label{
    width: 100%;
    border-radius: 0.1em;
    text-align: center;
    
  }

  .chartAndTotalBoxWrapper {
    position: absolute;
    top: 0;
    bottom: 5%;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
  }

  .stockChart {
    flex: 1;
  }
  
  `
  ]
})
export class UsedStockHealthTileComponent implements OnInit {

  @Input() public stockHealthData: UsedStockHealth;
  @Input() public newDataEmitter: EventEmitter<void>;
  
  @ViewChild('chartContainer', { static: true }) chartContainer: ElementRef

  stocks: Array<Stock>;
  usedStockTypes: UsedStockType[];
  //total: number;
  growType: string;
  StockBreakdown: any;
  totalHeight: any;
  minHeight: any;

  subscription: Subscription;


  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public router: Router,
    public analysis: AutotraderService,
    public modalService: NgbModal,
    public cphPipe: CphPipe,
    public getDataService: GetDataMethodsService,
    public service: DashboardService
  ) { }

  params: Date;

  ngOnInit(): void {

    this.initParams();

    this.subscription = this.newDataEmitter.subscribe(res => {
      this.initParams();
    })

  }

  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
  }

  initParams() {

    this.totalHeight = this.chartContainer.nativeElement.clientHeight
    
    this.minHeight = this.totalHeight * 0.008;

    var total = 0;

    if(!this.constants.environment.dashboard.includeDemoStockInStockHealth)
    {
      total = this.stockHealthData.Total - this.stockHealthData.Demo; 
    }
    else 
    {
      total = this.stockHealthData.Total;
    }

    if(this.stockHealthData)
    {
      this.usedStockTypes = [
        { type: 'CoreUsed', stocksCount: this.stockHealthData.CoreUsed, barHeight: this.getHeight(this.stockHealthData.CoreUsed, total), percent: this.constants.div(this.stockHealthData.CoreUsed, total) * 100 },
        { type: 'ExManagement', stocksCount: this.stockHealthData.ExManagement, barHeight: this.getHeight(this.stockHealthData.ExManagement, total), percent: this.constants.div(this.stockHealthData.ExManagement, total) * 100 },
        { type: 'Tactical', stocksCount: this.stockHealthData.Tactical, barHeight: this.getHeight(this.stockHealthData.Tactical, total), percent: this.constants.div(this.stockHealthData.Tactical, total) * 100 },
        { type: 'ExDemo', stocksCount: this.stockHealthData.ExDemo, barHeight: this.getHeight(this.stockHealthData.ExDemo, total), percent: this.constants.div(this.stockHealthData.ExDemo, total) * 100 },
      ]
  
      if (this.constants.environment.dashboard.includeDemoStockInStockHealth) {
        this.usedStockTypes.push({ type: 'Demo', stocksCount: this.stockHealthData.Demo, barHeight: this.getHeight(this.stockHealthData.Demo, total), percent: this.constants.div(this.stockHealthData.Demo, this.stockHealthData.Total) * 100 },)
      }


    }

    let totalBarHeights = this.constants.sum(this.usedStockTypes.map(x=>x.barHeight));

    // Bit of logic to align if percentage goes over 100%
    if(totalBarHeights > 100){
      this.usedStockTypes = this.usedStockTypes.sort((a,b) => b.barHeight - a.barHeight);
      this.usedStockTypes[0].barHeight = this.usedStockTypes[0].barHeight - (totalBarHeights - 100);
    }

    this.usedStockTypes = this.usedStockTypes.sort((a,b) => b.percent - a.percent);

  }

  private getHeight(type: number, total: number): number
  {
    if(type == 0){ return 0; }
    return this.constants.div(type, total) * 100 > this.minHeight ? this.constants.div(type, total) * 100 : this.minHeight
  }

  highlightType(usedStockType: UsedStockType) {
    this.growType = usedStockType.type;
  }


  goToStockPage() {
    this.selections.initiateStockReport();
    this.selections.stockReport.incomingReportNameChoice = 'Used Stock'
    this.service.chooseDashboardPage('StockReport')
  }

  selectStockType(stockType: UsedStockType) {
    let heading = stockType.type + ' - ' + this.cphPipe.transform(stockType.stocksCount, 'number', 0) + ' (' + this.cphPipe.transform(stockType.percent / 100, 'percent', 0) + ')'
    let siteIds = this.service.chosenSites.map(x => x.SiteId).join(',');

    this.getDataService.getStockModalRows(stockType.type, 'now', siteIds, 0, false, this.constants.FranchiseCodes.join(',')).subscribe((res: StockModalRowWithNext30) => {

      this.selections.initiateStockReportModal(true, res.AgedNow, res.AgedIn30, heading, true, stockType.type)
      const modalRef = this.modalService.open(StockReportModalComponent, { keyboard: true, size: 'lg' });

      modalRef.result.then((result) => { //I get back from modal
        if (result) {

        }
      });
    })


  }





}


