﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class GetVehicleOptOutSummaryItemsParams
   {
      public DateTime? ChosenDate { get; set; }
      public List<int> RetailerSiteIds { get; set; }
      public bool IncludeNewVehicles { get; set; }
      public bool IncludeUnPublishedAds { get; set; }
      public string LifeCycleStatuses { get; set; }
      public int DealerGroupId { get; set; }
   }
}

