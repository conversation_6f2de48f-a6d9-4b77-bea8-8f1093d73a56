

export interface LeavingVehicleItem {

    FirstSnapId: number;
    LastSnapId: number;
    AdvertId:number;
    
    //dimensions
    Region: string;
    RetailerSiteName: string;
    BodyType: string;
    FuelType: string;
    TransmissionType: string;
    Make: string;
    Model: string;
    RegYear: number;
    DaysListedBand: string;
    FirstPP: number;
    FirstPPBand: string;
    LastPP: number;
    LastPPBand: string;
    RetailRatingBand: string;
    MileageBand: string;
    IsOnStrategy:boolean;
    LastPriceBand: string;
    AchievedSaleType:string;
    ImageURL:string;
    
    //measures
    VehicleReg: string;
    DerivativeId: string;
    Derivative: string;
    MakeModelDerivative:string;
    ListedDate: Date | string;
    RemovedDate: Date | string;
    Mileage: number;
    DaysListed: number;
    FirstPrice: number;
    FirstValuation: number;
    LastPrice: number;
    LastValuation: number;
    LastPriceIndicator: string;
    LastRetailRating: number;

    FirstRetailDaysToSell: number;
    DaysListedVsFirstRetailDaysToSell: number;
    VehicleType: string;
    AgeBand:string;
    

}
