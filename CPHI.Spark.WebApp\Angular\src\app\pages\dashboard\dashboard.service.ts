import { EventEmitter, Injectable } from '@angular/core';
import { SimplePickerItem } from 'src/app/components/pickerSimple.component';
import { MenuItemNew, SiteVM } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardPageNew, DashboardSection } from './dashboard.component';
import { DataOriginsUpdate } from './dashboard.model';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  months: Date[];

  chosenSection: DashboardSection
  chosenPage: DashboardPageNew
  chosenSites: SiteVM[];
  getNewDataTrigger: EventEmitter<void>;
  aftersalesDashboardRedraw: EventEmitter<void>;

  chosenMonthStart:Date;
  activitesTileStartDate:Date;
  thisWeeksOrdersStartDateNew: Date;
  thisWeeksOrdersStartDateUsed: Date;
  thisWeeksOrdersStartDateFleet: Date;
  singleLineNav: boolean = true;

  orderTypeTypes: string[];
  franchises: string[];

  newOrderTypeTypes: SimplePickerItem[];
  usedOrderTypeTypes: SimplePickerItem[];

  mostRecentDateInDailyOrders: Date;

  hideReportButtons: boolean = false;

  currentMonthSelected: boolean = false;

  showMenuItemsAsDropdown: boolean;
  minScreenWidthForFullMenu: number;

  dataOriginUpdates: DataOriginsUpdate[];

  constructor(

    private constants: ConstantsService,
    private getDataMethodsService: GetDataMethodsService,
    private selections: SelectionsService
  ) {

    this.getNewDataTrigger = new EventEmitter();
    this.aftersalesDashboardRedraw = new EventEmitter();

  }



  chooseDashboardPage(pageName: string) : void {

    const menuItem = this.constants.quickFindMenuItem(pageName,'pageName');// this.findDashboardPage(pageName); 
     
    //this.getDataMethodsService.getTodayNewUsedOrders();

    //need to update the page last refresh time
    if (pageName==='StockReport') { this.getDataMethodsService.getLatestUpdatedDate('Stocks'); }
    if (pageName==='Registrations') { this.getDataMethodsService.getLatestUpdatedDate('Registrations'); }
    if (pageName==='CitNow') { this.getDataMethodsService.getLatestUpdatedDate('CitNow'); }
    if (pageName==='Voc') { this.getDataMethodsService.getLatestUpdatedDate('Voc'); }
    if (pageName==='ServiceBookings') { this.getDataMethodsService.getLatestUpdatedDate('Bookings'); }
    if (pageName==='PartsStock') { this.getDataMethodsService.getLatestUpdatedDate('PartsStock'); }
    if (pageName==='ServiceSales' || pageName==='PartsSales') { this.getDataMethodsService.getLatestUpdatedDate('FinancialLines'); }
    if (pageName==='EVHC' ) { this.getDataMethodsService.getLatestUpdatedDate('EVHC'); }
    if (pageName==='Wip' ) { this.getDataMethodsService.getLatestUpdatedDate('Wip'); }
    if (pageName==='FinanceAddOns' || pageName==='OrderRate' ) { this.getDataMethodsService.getLatestUpdatedDate('Deals'); }
    if (pageName==='Upsells' ) { this.getDataMethodsService.getLatestUpdatedDate('Upsells'); }
    

    //this.chosenPage = menuItem;
    this.singleLineNav = false;

    if (menuItem.link === '/dashboard') {
      this.dealWithDashboardLink(menuItem);
    } else {
      this.constants.navigateByUrl(menuItem);
    }
    //if (menuItem) {  } //,true
  }

private dealWithDashboardLink(menuItem: MenuItemNew) {
    this.chosenPage = {
      pageName: menuItem.pageName,
      translatedTextField: menuItem.pageName,
      translatedTextValue: menuItem.pageName
    };

    if (menuItem.isSales) {
      this.chosenSection = this.constants.environment.dashboard_sections.find(x => x.translatedTextField === 'Sales');
    } else {
      this.chosenSection = this.constants.environment.dashboard_sections.find(x => x.translatedTextField === 'Aftersales');
    }

    this.constants.navigateByUrlForDashboard(menuItem);
  }


  findDashboardPage(pageName: string) {
    let sections = this.constants.environment.dashboard_sections;


    let foundPage: DashboardPageNew;
    let i = 0;

    while (!foundPage && i < sections.length) {

      const thisSection: DashboardSection = sections[i];
      let j = 0;

      while (!foundPage && j < thisSection.pages.length) {

        let thisPage: DashboardPageNew = thisSection.pages[j];


        if (thisPage.pageName === pageName) {
          foundPage = thisPage;
        }
        j++;
      }

      i++;
    }

    return foundPage;
  }

  chooseSection(sectionName: string)
  {

    this.constants.environment.dashboard_sections.forEach(element => {

      if(element.sectionName == sectionName)
      {
        this.chosenSection = element;
      }

    });

  
    //SPK-936 hide whilst still testing
    //if(!this.selections.user.permissions.seeRegistrations){
      //this.service.chosenSection.pages = this.service.chosenSection.pages.filter(x=>x.pageName!=='Registrations') 
    //}

    this.chosenPage = {
      pageName: this.chosenSection.pageName,
      translatedTextField: this.chosenSection.translatedTextField,
      translatedTextValue: this.chosenSection.translatedTextValue
    } ;


    this.singleLineNav = !['DashboardOverviewSpain','DashboardNewSpain','DashboardUsedSpain'].includes(this.chosenSection.sectionName)
  }



  onUpdateFranchises(franchises: string[]) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.franchises = franchises
    this.getNewDataTrigger.emit(null);
  }


  chooseMonth(month:Date){
    this.chosenMonthStart = month;
    this.getNewDataTrigger.emit(null);
  }

  public selectMonth(date: Date): void {
    this.chosenMonthStart = date;
    this.getNewDataTrigger.emit(null);
  }


  public changeMonth(changeAmount: number): void {
    this.chosenMonthStart = this.constants.addMonths(this.chosenMonthStart,changeAmount);
    this.getNewDataTrigger.emit(null);
  }

  public getSpainMonths()
  {
    // Filter on here for the month 
    let twoMonthsPrior: Date = this.constants.addMonths(this.chosenMonthStart, -2);

    this.months = this.constants.makeMonthsNewV3(true)
                                .filter(x => x.getFullYear() == twoMonthsPrior.getFullYear() && 
                                        x.getMonth() >= twoMonthsPrior.getMonth()); 
  }


}
