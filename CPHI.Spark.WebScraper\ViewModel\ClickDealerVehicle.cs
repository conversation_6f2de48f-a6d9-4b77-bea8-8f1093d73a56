using CPHI.Spark.Model.ViewModels.AutoPricing;

namespace CPHI.WebScraper.ViewModel
{
    public class ClickDealerVehicle
    {
        public string Reg { get; set; }
        public int Price { get; set; }
        public int RetailerSiteId { get; set; } = 82; // Only one site for Farnham for now
        public bool PriceChanged { get; set; } = false;
        
        // Constructor from PricingChangeNew
        public ClickDealerVehicle(PricingChangeNew pr)
        {
            Reg = pr.VehicleReg;
            Price = pr.NewPrice;
            RetailerSiteId = pr.RetailerSiteId;
        }
        
        // Constructor for test items
        public ClickDealerVehicle(string reg, int price)
        {
            Reg = reg;
            Price = price;
        }
    }
}
