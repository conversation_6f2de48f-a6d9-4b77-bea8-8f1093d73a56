using CPHI.Spark.Model.ViewModels.AutoPricing;

namespace CPHI.WebScraper.ViewModel
{
    public class ClickDealerVehicle
    {
        public string Reg { get; set; }
        public int Price { get; set; }
        public int RetailerSiteId { get; set; }
        public bool PriceChanged { get; set; } = false;
        
        // Constructor from PricingChangeNew
        public ClickDealerVehicle(PricingChangeNew pr)
        {
            Reg = pr.VehicleReg;
            Price = pr.NewPrice;
            RetailerSiteId = pr.RetailerSiteId;
        }
        
        // Constructor for test items
        public ClickDealerVehicle(string reg, int price, int retailerSiteId)
        {
            Reg = reg;
            Price = price;
            RetailerSiteId = retailerSiteId;
        }
    }
}
