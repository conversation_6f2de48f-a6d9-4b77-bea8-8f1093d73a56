using CPHI.Spark.Model.ViewModels.AutoPricing;

namespace CPHI.WebScraper.ViewModel
{
   public class ClickDealerVehicle
   {
      // Constructor from PricingChangeNew
      public ClickDealerVehicle(PricingChangeNew pr)
      {
         Reg = pr.VehicleReg;
         Price = pr.NewPrice;
         RetailerSiteId = pr.RetailerSiteId;
         ClickDealerFee = pr.ClickDealerFee;
      }

      // Constructor for test items
      public ClickDealerVehicle(string reg, int price)
      {
         Reg = reg;
         Price = price;
      }


      public string Reg { get; set; }
      public int Price { get; set; }
      public int RetailerSiteId { get; set; }
      public int? ClickDealerFee { get; set; }

      public bool PriceChanged { get; set; } = false;
   }
}
