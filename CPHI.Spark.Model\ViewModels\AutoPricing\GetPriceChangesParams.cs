﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
    public class GetPriceChangesParams
    {
        public List<int> retailerSiteIds { get; set; }
        public DateTime date { get; set; }
        public bool showSmallPriceChanges { get; set; }
        public bool includeNewVehicles { get; set; }
        public bool includeUnPublishedAds { get; set; }
    }

    public class SetPriceChangesParams
    {
        public List<int> priceChangeIds { get; set; }
    }
}

