import {Component, Input, OnInit} from '@angular/core';
import {CphPipe} from 'src/app/cph.pipe';
import {NumberPlateYear} from "src/app/model/NumberPlateYear";
import {CompetitorSummary} from "src/app/model/CompetitorSummary";
import CompetitorSearchOurVehicle from 'src/app/pages/performanceTrends/CompetitorSearchOurVehicle';
import {CompetitorSearchUserChoices} from 'src/app/pages/performanceTrends/CompetitorSearchUserChoices';
import {
   CompetitorSearchParams,
   GetValuationModalCompetitorAnalysisParams,
   OurVehicleParams
} from 'src/app/pages/performanceTrends/GetValuationModalCompetitorAnalysisParams';
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {SelectionsService} from 'src/app/services/selections.service';
import * as utilities from "./../../services/utilityFunctions";
import {BubbleChartFields} from "./BubbleChartFields";
import {CompetitorAnalysisService} from './competitorAnalysis.service';
import {CompetitorAnalysisParams} from './CompetitorAnalysisParams';
import {AutoPriceInsightsModalService} from '../autoPriceInsightsModal/autoPriceInsightsModal.service';

@Component({
   selector: 'competitorAnalysis',
   templateUrl: './competitorAnalysis.component.html',
   styleUrls: ['./competitorAnalysis.component.scss']
})
export class CompetitorAnalysisComponent implements OnInit {

   // @Input() private params: CompetitorAnalysisParams //DO NOT REFERENCE THIS, INSTEAD USE THE VERSION IN SERVICE
   // things for the template to render

   distances: number[] = [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 70, 80, 90, 100, 200];
   chartXYFields: BubbleChartFields[] = [
      {
         x: 'Mileage',
         xAdvert: 'OdometerReading',
         xPretty: 'Mileage',
         xPipe: 'number',
         y: 'PricePosition',
         yPretty: 'Price Position',
         yPipe: 'percent'
      },
      {
         x: 'Mileage',
         xAdvert: 'OdometerReading',
         xPretty: 'Mileage',
         xPipe: 'number',
         y: 'AdvertisedPrice',
         yPretty: 'Price',
         yPipe: 'currency'
      }
   ];

   get amWithinInsightsModal(): boolean {
      return this.service.params.ParentType === 'insightsModal'
   }


   // the user choices and states
   selectedRadius: number;
   selectedMinPlate: number;
   selectedMaxPlate: number;
   plateChoices: number[] = [];


   chartData: BubbleChartFields;
   showBlobs: boolean = false;


   constructor(
      public service: CompetitorAnalysisService,
      public getDataService: GetDataMethodsService,
      public selectionsService: SelectionsService,
      public constants: ConstantsService,
      private cphPipe: CphPipe,
      private autoPriceInsightsService: AutoPriceInsightsModalService
   ) {
   }

   ngOnInit(): void {
      this.initParams();

      // if(this.service.params.ParentType == 'insightsModal' ){
      //   this.service.service.competitorAnalysisRef = this;
      // }
      this.autoPriceInsightsService.competitorAnalysisRef = this;
   }

   redrawTable(newData: CompetitorSummary, params: CompetitorAnalysisParams) {
      this.initParams();
      if (this.service.chartRef) {
         this.service.chartRef.dealWithNewData(newData);
      }
      if (this.service.tableRef && newData?.CompetitorVehicles) {
         this.service.tableRef.dealWithNewData(params);
      }
   }

   initParams() {
      this.chartData = this.chartXYFields[0];
      // this.buildMinMaxPlateYear();
      const firstRegDate = this.service.params.FirstRegisteredDate;
      this.setupPlateYears(firstRegDate);
      this.setPlatesRange();
      this.setRadius(this.service.params.CompetitorSummary.Radius);
   }

   setRadius(radius: number) {
      this.selectedRadius = radius;
   }

   public selectMinPlate(plate: number) {
      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading...'});
      this.selectedMinPlate = plate
      this.getAutoPriceCompetitorAnalysis();
      // this.setPlatesRange();
   }

   public selectMaxPlate(plate: number) {
      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading...'});
      this.selectedMaxPlate = plate
      this.getAutoPriceCompetitorAnalysis();
      // this.setPlatesRange();
   }

   selectRadius(radius: number) {
      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading...'});
      // get fresh data based on selected radius
      this.selectedRadius = radius;
      this.getAutoPriceCompetitorAnalysis();
   }


   private getAutoPriceCompetitorAnalysis() {
      if (this.service.params.ParentType === 'insightsModal') {
         if (this.service.params.WebSiteSearchIdentifier) {
            this.getCompetitorAnalysisForExistingAd();
         } else {
            this.getCompetitorAdvertCreatedFromDmsStock();
         }
      } else {
         this.getCompetitorAnalysisForNewValuation();
      }

   }

   private getCompetitorAnalysisForExistingAd() {
      const userChoiceParams: CompetitorSearchUserChoices = {
         MaxPlate: this.selectedMaxPlate,
         MinPlate: this.selectedMinPlate,
         Radius: this.selectedRadius
      };

      let plateYear = utilities.dateToNumberPlate(this.service.params.FirstRegisteredDate);


      const thisAdRetailerSite = this.constants.RetailerSites.find(x => x.RetailerId === this.service.params.RetailerSiteRetailerId);
      const thisAdSite = this.constants.Sites.find(x => x.SiteId === thisAdRetailerSite.Site_Id);
      const ourVehicleParams: CompetitorSearchOurVehicle = {
         VehicleReg: this.service.params.VehicleReg,
         VehicleMileage: this.service.params.OdometerReading,
         VehicleYear: plateYear,
         AdvertisedPrice: this.service.params.AdvertisedPrice,
         PricePosition: this.service.params.PricePosition,
         Valuation: this.service.params.Valuation,
         Postcode: thisAdRetailerSite.Postcode,
         SiteName: thisAdRetailerSite.Name,
         SiteGeoX: thisAdSite.GeoX,
         SiteGeoY: thisAdSite.GeoY,
         ImageURL: this.service.params.ImageURL
      };

      this.getDataService.getAutoPriceCompetitorAnalysis(this.service.params.AdvertId, this.service.params.WebSiteSearchIdentifier, this.service.params.RetailerSiteRetailerId, userChoiceParams, ourVehicleParams).subscribe((res: CompetitorSummary) => {
         this.dealWithNewData(res);
      }, error => {
         console.error('Failed to load modal data', error);
      });
   }


   private getCompetitorAnalysisForNewValuation() {

      const ourVehicleParams: OurVehicleParams = {
         AdvertisedPrice: this.service.params.AdvertisedPrice,
         Valuation: this.service.params.Valuation,

         VehicleReg: this.service.params.VehicleReg,
         Mileage: this.service.params.OdometerReading,
         FirstRegisteredYear: this.service.params.FirstRegisteredDate.getFullYear(),
      }
      const retailerSite = this.constants.RetailerSites.find(x => x.RetailerId == this.service.params.RetailerSiteRetailerId);
      const searchParams: CompetitorSearchParams = {
         standardMake: this.service.params.Make,
         standardModel: this.service.params.Model,
         standardTrim: this.service.params.Trim,
         standardTransmissionType: this.service.params.TransmissionType,
         standardFuelType: this.service.params.FuelType,
         standardBodyType: this.service.params.BodyType,
         standardDrivetrain: this.service.params.Drivetrain,
         doors: this.service.params.Doors.toString() ,

         advertiserId: this.service.params.RetailerSiteRetailerId,
         radius: this.selectedRadius,
         postcode: retailerSite.Postcode,
         minPlate: this.selectedMinPlate,
         maxPlate: this.selectedMaxPlate,
         selfRegToExclude: this.service.params.VehicleReg,
      }

      let parms: GetValuationModalCompetitorAnalysisParams = {
         SearchParams: searchParams,
         OurVehicleParams: ourVehicleParams,
      };

      this.getDataService.getAutoPriceCompetitorAnalysisForNewValuation(parms).subscribe((res: CompetitorSummary) => {
         this.dealWithNewData(res);
      }, error => {
         console.error('Failed to load modal data', error);
      });
   }

   private getCompetitorAdvertCreatedFromDmsStock() {
      // console.log(this.service.params);
      let ppToUse = this.service.params.PricePosition;
      if (ppToUse == 0) {
         ppToUse = this.constants.div(this.service.params.AdvertDetail.StrategyPrice, this.service.params.AdvertDetail.ValuationAdjRetail);
      }
      let sellingPrice = this.service.params.AdvertisedPrice;
      if (sellingPrice == 0) {
         sellingPrice = Math.round(this.service.params.AdvertDetail.StrategyPrice);
      }

      const ourVehicleParams: OurVehicleParams = {
         AdvertisedPrice: this.service.params.AdvertisedPrice,
         Valuation: this.service.params.Valuation,

         VehicleReg: this.service.params.VehicleReg,
         Mileage: this.service.params.OdometerReading,
         FirstRegisteredYear: this.service.params.FirstRegisteredDate.getFullYear(),
      }
      const retailerSite = this.constants.RetailerSites.find(x => x.RetailerId == this.service.params.RetailerSiteRetailerId);

      const searchParams: CompetitorSearchParams = {
         standardMake: this.service.params.Make,
         standardModel: this.service.params.Model,
         standardTrim: this.service.params.Trim,
         standardTransmissionType: this.service.params.TransmissionType,
         standardFuelType: this.service.params.FuelType,
         standardBodyType: this.service.params.BodyType,
         standardDrivetrain: this.service.params.Drivetrain,
         doors: this.service.params.Doors.toString() ,

         advertiserId: this.service.params.RetailerSiteRetailerId,
         radius: this.selectedRadius,
         postcode: retailerSite.Postcode,
         minPlate: this.selectedMinPlate,
         maxPlate: this.selectedMaxPlate,
         selfRegToExclude: this.service.params.VehicleReg,
      }

      let parms: GetValuationModalCompetitorAnalysisParams = {
         SearchParams: searchParams,
         OurVehicleParams: ourVehicleParams,
      };


      this.getDataService.getAutoPriceCompetitorAnalysisForNewValuation(parms).subscribe((res: CompetitorSummary) => {
         this.dealWithNewData(res);
      }, error => {
         console.error('Failed to load modal data', error);
      });
   }

   private dealWithNewData(res: CompetitorSummary) {
      this.selectionsService.triggerSpinner.emit({show: false});

      this.service.params.CompetitorSummary = res;

      if (this.service.params.VehicleValuationService) {
         this.service.params.VehicleValuationService.valuationModalResultNew.CompetitorCheckResult = res;
      }

      if (this.service.tableRef) {
         this.service.tableRef.dealWithNewData(this.service.params);
      }
      if (this.service.chartRef) {
         this.service.chartRef.dealWithNewData(this.service.params.CompetitorSummary);
      }
   }


   getDistanceLabel() {
      if (this.selectedRadius === 1000) return 'National';
      return `Within ${this.selectedRadius} ${this.selectedRadius === 1 ? 'mile' : 'miles'}`
   }

   showChartView(xyFields: BubbleChartFields) {

      this.chartData = xyFields;
      this.showBlobs = true;

      if (this.service.chartRef) {
         this.service.chartRef.selectedXYFields = xyFields;
         this.service.chartRef.dealWithNewData(this.service.params.CompetitorSummary);
      }
   }

   showActiveChartButton(xyFields: BubbleChartFields) {
      return this.chartData.x === xyFields.x && this.chartData.y === xyFields.y;
   }


   setupPlateYears(vehicleFirstRegDate: Date) {
      const retailerSite = this.constants.RetailerSites.find(x => x.Id == this.selectionsService.user.RetailerSiteId);
      const minMax = this.getMinMaxPlateNew(vehicleFirstRegDate, retailerSite.CompetitorPlateRange);
      this.selectedMinPlate = minMax.minPlate;
      this.selectedMaxPlate = minMax.maxPlate;
   }


   getMinMaxPlateNew(firstRegd: Date, plateRange: number): { minPlate: number | null; maxPlate: number | null } {
      const plateYear: number = this.getPlateFromFirstReg(firstRegd);
      const existingIsSep = plateYear > 50;
      const year = existingIsSep ? plateYear - 50 : plateYear;
      const forwardBackEven = plateRange % 2 === 0;
      const halfRange = Math.floor(plateRange / 2);

      let min: number;
      let max: number;

      if (forwardBackEven) {
         // Even range
         min = plateYear - halfRange;
         max = plateYear + halfRange;
      } else {
         // Odd range
         if (existingIsSep) {
            min = plateYear - (plateRange - 1 + 50);
            max = plateYear + (plateRange - 50);
         } else {
            min = year - (plateRange - 50);
            max = year + (plateRange - 1 + 50);
         }
      }

      return {minPlate: min, maxPlate: max};
   }


   getPlateFromFirstReg(firstRegisteredDate: Date | null): number {
      if (!firstRegisteredDate) {
         throw new Error("firstRegisteredDate is required");
      }

      const year = firstRegisteredDate.getFullYear();
      const month = firstRegisteredDate.getMonth() + 1; // getMonth returns 0-11, so add 1 for 1-12

      if (month >= 9) {
         return year - 1950;  // e.g., 2021 gives us 71
      } else if (month >= 3) {
         return year - 2000;  // e.g., 2021 gives us 21
      } else {
         return year - 1951;  // e.g., 2021 gives us 70
      }
   }

   nextLowerPlate(currentPlate: number) {
      if (currentPlate > 50) {
         //we're on a september plate.
         return currentPlate - 50;
      } else {
         return currentPlate - 1 + 50;
      }
   }

   nextHigherPlate(currentPlate: number) {
      if (currentPlate > 50) {
         //we're on a september plate.
         return currentPlate - 50 + 1;
      } else {
         return currentPlate + 50;
      }
   }

   setPlatesRange() {
      let results: number[] = [];

      const highestAllowablePlate: number = this.getHighestAllowablePlate();

      let currentPlate = this.getPlateFromFirstReg(this.service.params.FirstRegisteredDate);

      let lowPlates = [];
      for (let i = 0; i < 6; i++) {
         //step down
         currentPlate = this.nextLowerPlate(currentPlate);
         lowPlates.push(currentPlate);
      }
      lowPlates.reverse();
      results = results.concat(lowPlates)
      let highestPlate = results[results.length - 1];
      for (let i = 0; i < 7; i++) {
         //step up
         highestPlate = this.nextHigherPlate(highestPlate);
         results.push(highestPlate);
         if (highestPlate === highestAllowablePlate) {
            break;
         }
      }

      this.plateChoices = results

   }

   getHighestAllowablePlate(): number {
      let date: Date = new Date();
      let year: number = date.getFullYear();
      let month: number = date.getMonth() + 1;

      let plateNumber: number = year - 2000;
      if (month >= 9) {
         plateNumber += 50;
      }

      return plateNumber;
   }


   // buildMinMaxPlateYear() {
   //   this.numberPlateYear = [];
   //   const modalItemRegisteredDate: Date = new Date(this.service.params.FirstRegisteredDate);
   //   //const month: number = modalItemRegisteredDate.getMonth() + 1;
   //   const year: number = modalItemRegisteredDate.getFullYear() - 2;

   //   //this.workoutShortYear(month, year);

   //   const maxYear = new Date().getFullYear();

   //   for (let i = 0; i < maxYear - year; i++) {
   //     let thisYear: number = year + i;

   //     this.numberPlateYear.push(
   //       {
   //         Year: thisYear,
   //         Plate: this.workoutShortYear(thisYear, 1)
   //       },
   //       {
   //         Year: thisYear,
   //         Plate: this.workoutShortYear(thisYear, 10)
   //       }
   //     )
   //   }

   //   this.selectedMinPlate = this.numberPlateYear.find(n => n.Plate === this.service.params.CompetitorSummary.MinPlate);
   //   this.selectedMaxPlate = this.numberPlateYear.find(n => n.Plate === this.service.params.CompetitorSummary.MaxPlate);
   // }


   // private workoutShortYear(year: number, month: number): number {
   //   let toAdd = month < 9 ? 0 : 50;
   //   return year - 2000 + toAdd;
   // }


   public getHeight() {
      if (this.service.params.ParentType == 'insightsModal') {
         return null;
      }
      const height = 1000;
      return `${height}px`;
   }

   showBlobsAndTableTogether() {
      return this.service.params.ParentType == 'valuationModal'
   }

   scrollToThisVehicle() {
      const gridApi = this.service.tableRef.gridApi;

      gridApi.forEachNodeAfterFilterAndSort((node, index) => {
         if (node.data.IsOurVehicle === true) {
            gridApi.ensureIndexVisible(index, 'top');
         }
      });
   }
}
