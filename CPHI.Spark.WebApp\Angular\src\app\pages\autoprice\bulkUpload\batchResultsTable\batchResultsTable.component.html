<div id="gridHolder">
   <div id="gridHeader">
      <tableLayoutManagement *ngIf="service.tableLayoutManagement.gridApi"></tableLayoutManagement>

      <div class="searchBox autoHeight">
         <div class="searchBoxIconContainer">
            <i class="searchBoxIcon fas fa-search"></i>
         </div>
         <form>
            <input placeholder="{{ constants.translatedText.Search }}" class="form-control ml-2" type="text"
                   [formControl]="service.searchTerm"/>
            <div *ngIf="!!service.searchTerm.value" (click)="clearSearchTerm()" id="searchBarClearButton">
               <i class="fas fa-times-circle"></i>
            </div>
         </form>
      </div>
      <!-- <div (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()" [ngStyle]="{'width': '2em'}">
      </div> -->
   </div>
   <!-- Clear filters applied from dashboard -->
   <div id="clearFiltersButton" class="clickable" (click)="clearFilterModel()" *ngIf="showFilterRemoveButton()">
      <i class="fas fa-filter"></i>
   </div>

   <statusBar (excelExportClick)="excelExport()"
              [gridColumnApi]="service.tableLayoutManagement.gridColumnApi"
              [gridApi]="service.tableLayoutManagement.gridApi" [gridOptions]="gridOptions"></statusBar>
   <ag-grid-angular
      [rowData]="this.service.batchResultsRowData"
      [ngClass]="constants.getGridClass()"
      [gridOptions]="gridOptions"
      [components]="components"></ag-grid-angular>
</div>



