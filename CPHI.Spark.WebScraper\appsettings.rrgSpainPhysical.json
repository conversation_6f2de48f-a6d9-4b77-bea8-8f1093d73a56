{
  //-------------------------------------------------x
  // THIS IS WEB SCRAPER 
  //-------------------------------------------------x

  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "EmailSettings": {
    "mailAppTenantId": "7def64f8-8b3f-400a-8ad0-e50eb7e77eef",
    "mailAppId": "9a44d66f-cfe2-4d19-b129-62b3e0dd28aa",
    "mailSecret": "<PERSON> Swift, definitely",
    "mailSecretValue": "****************************************",
    "mailAccount": "<EMAIL>"
  },
  "AppSettings": {
    "mailUser": "<EMAIL>",
    "mailPwd": "X4KkGrS46MKHqEXR",
    "mailIn": "Inbox",
    "triggerJobSenders": "<EMAIL>,<EMAIL>",
    "mailArchive": "processed",
    "mailError": "error",



    //-------------------------------------------------x
    // For testing using this to force run this job now.  Or null.
    //-------------------------------------------------x
    "forceRunThisJobNowOnly": null, //put the job name in here if you wish to force run it  e.g. "PeopleJob".   Or null (without quotes).  Only ever change appsettings.json not the underlying rrgDev file


    //-------------------------------------------------x
    // Common
    //-------------------------------------------------x
    "serviceName": "CPHI.Spark.WebScraper",
    "fileDestination": "C:\\Users\\<USER>\\Downloads\\Outbound",
    //"fileDestinationDev": "",
    "fileDownloadLocation": "C:\\Users\\<USER>\\Downloads",
    "sendEmailsTo": "<EMAIL>,<EMAIL>,<EMAIL>",


    //-------------------------------------------------x
    // RRG
    //-------------------------------------------------x
    "RRGSiteStockSchedule": "DO NOT RUN",
    "RRGVocSchedule": "DO NOT RUN", // See http://www.cronmaker.com/;jsessionid=node01sik34gwl2vx3ezhdm1rnrgx736310.node0?0


    //-------------------------------------------------x
    // Spain
    //-------------------------------------------------x
    "AlcopaPassword": "",
    "OrdersMadridPassword": "",
    "OrdersValenciaPassword": "",

    "SpainOrdersSchedule": "DO NOT RUN",
    "SpainAlcopaSchedule": "DO NOT RUN",


    //-------------------------------------------------x
    // Spain Physical
    //-------------------------------------------------x
    "fileDestinationProcessed": "C:\\Users\\<USER>\\Downloads\\Processed",
    "DistrinetSchedule": "CUSTOM 0 0/30 6-19 * * ?",
    "ftpHostname": "access832882062.webspace-data.io",
    "ftpUsername": "u1211288265",
    "ftpPassword": "ZM54ASG33uJeXs9V",



    //-------------------------------------------------x
    // Vindis
    //-------------------------------------------------x
    "auditScrapePassword": "",
    "enquiryMAXPassword": "",
    "VindisDealsIntradaySchedule": "DO NOT RUN",
    "VindisDealsDailySchedule": "DO NOT RUN",
    "VindisActivitySchedule": "DO NOT RUN",
    "VindisGDPRSchedule": "DO NOT RUN",
    "EnquiriesScrapeJob": "DO NOT RUN",
    "VindisAuditSchedule": "DO NOT RUN"
  },
  "Monitor": {
    "AddLogMessageURL": "https://cphimonitorapi.azurewebsites.net/api/monitor/",
    "AppKey": "2"
  }


}
