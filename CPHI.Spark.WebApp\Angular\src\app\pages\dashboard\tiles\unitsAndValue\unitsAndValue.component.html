<div class="dashboard-tile-inner">
  <div class="tileHeader">
    <div class="headerWords">
      <h4>{{ title }}</h4>
      <div *ngIf="dataSource" class="right-aligned">
        <div *ngIf="showWarning" class="warning"></div>
        <div class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
      </div>
    </div>
  </div>


  <div id="innerContentHolder">
    <div id="chartHolder">
      <div id="actualUnits" (click)="navigateToOrderBook()">
        <h1><strong>{{data.Units|cph:'number':0}}</strong></h1>
      </div>
    </div>

    <div class="lastYearUnits">
      {{ data.Value|cph:'currency':0 }}  {{constants.translatedText.Common_TotalValue}}
    </div>

  </div>
</div>