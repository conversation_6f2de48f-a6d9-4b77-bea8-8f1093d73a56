{

   //-------------------------------------------------x
   // THIS IS USED TO PROCESS DEV MIGRATIONS
   //-------------------------------------------------x

   "ConnectionStrings": {
      //new approach - 1. Ensure you have saved your changes to the model file.  2. right click solution and choose 'open in terminal'.  3. Type '.\Generate_Migration.ps1' to generate migration 4. '.\Run_Migrations_In.ps1' to run it to all dbs
      "DefaultConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=sparkAutopricedev; Persist Security Info=true; User ID=SparkAutoPriceUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "RRGUKConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=sparkRRGDev; Persist Security Info=False; User ID=sparkRRGUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "VindisConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=sparkVindisDev; Persist Security Info=true; User ID=sparkVindisUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "RRGSpainConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=sparkRRGSpaindev; Persist Security Info=true; User ID=sparkSpainUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "SytnerConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=sparkSytnerdev; Persist Security Info=true; User ID=SparkSytnerUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "StockTakePhotos": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=stockpulsedev; Persist Security Info=true; User ID=StockPulseApiUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;"
   },

   "AppSettings": {
      "Environment": "dev",
      "ConfirmLeaverEmail": "false"
   },

   "BlobStorage": {
      //"Environment": "renault",
      "StorageAccount": "stockpulseimages",
      "StorageAccountKey": "****************************************************************************************",
      "ProfileFilePath": "https://stockpulseimages.blob.core.windows.net/spark-profile-image-dev/",
      "VehicleFilePath": "https://stockpulseimages.blob.core.windows.net/spark-vehicle-dev/",
      "ReadOnlyKey": "sp=r&st=2022-02-04T15:21:53Z&se=2044-12-01T23:21:53Z&spr=https&sv=2020-08-04&sr=c&sig=1N92HrHCE6JwOkMQrohTwfImXDlq2%2FAAB2qwOHG2Wio%3D"
   },

   "WebApp": {
      "URL": "https://sparkdev.cphi.co.uk"
   },

   "AutotraderSettings": {
      "ApiKey": "CPHInsight-PricingBI-22-07-24",
      "ApiSecret": "nyGQrMe4oUVTM7iGPZdgghrDP8nTeZKw",
      "BaseURL": "https://api.autotrader.co.uk",
      "BaseURLForChangingPrices": "https://api-sandbox.autotrader.co.uk"
   }








}
