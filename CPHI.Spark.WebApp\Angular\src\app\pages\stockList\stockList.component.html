<nav class="navbar">

  <nav class="generic">

    
    <h4 id="pageTitle">
      <div >

        {{ service.constants.translatedText.StockList_Title }}
        <sourceDataUpdate [chosenDate]="service.constants.LastUpdatedDates.Stocks"></sourceDataUpdate>
        <span *ngIf="service.stockList?.reportName"> - {{service.stockList?.reportName}}</span>


      </div>
    </h4>

    <vehicleTypePickerSpain
      *ngIf="service.constants.environment.vehicleTypePicker_showVehicleTypePickerSpain"
      [vehicleTypeTypesFromParent]="service.stockList.storedGridState.highLevelFilters.vehicleTypes"
      (updateVehicleTypes)="onUpdateVehicleTypesOnClick($event)"
    >
    </vehicleTypePickerSpain>

    <!-- Site selector -->
    <div class="buttonGroup">
      <ng-container *ngIf="service.constants.environment.dealDone_showRRGSitePicker">
    <sitePickerRRG [allSites]="service.constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites" [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSitesOnClick($event)"></sitePickerRRG>
      </ng-container>
      
      <ng-container *ngIf="service.constants.environment.dealDone_showVindisSitePicker">
    <sitePicker [allSites]="service.constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites" [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSitesOnClick($event)"></sitePicker>
      </ng-container>

     <!-- VehicleType selector -->
     <vehicleTypePicker *ngIf="!service.constants.environment.vehicleTypePicker_showVehicleTypePickerSpain"
     [vehicleTypeTypesFromParent]="service.stockList.storedGridState.highLevelFilters.vehicleTypes"
     [buttonClass]="'buttonGroupCenter'" (updateVehicleTypes)="onUpdateVehicleTypesOnClick($event)"></vehicleTypePicker>

      <!-- Franchise selector -->
      <franchisePicker *ngIf="this.service.constants.environment.franchisePicker" [franchisesFromParent]="service.stockList.storedGridState.highLevelFilters.franchises" [buttonClass]="'buttonGroupCenter'"
        (updateFranchises)="onUpdateFranchisesOnClick($event)"></franchisePicker>

         <!-- Model selector -->
      <modelPicker [allStock]="service.stockList.stocks" [modelsFromParent]="service.stockList.storedGridState.highLevelFilters.models" [buttonClass]="'buttonGroupRight'"
      (updateModels)="onUpdateModelsOnClick($event)"></modelPicker>


    </div>

  </nav>

  <nav class="pageSpecific" *ngIf="service.stockList" id="stockList">

    <!-- Search -->
    <div id="searchBox">
      <form>
        <i class="fas fa-search searchBoxIcon"></i> <input placeholder="{{service.constants.translatedText.Search}}..." class="form-control ml-2" type="text"
          [formControl]="filter" />
      </form>
    </div>

  </nav>
</nav>



<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constants.environment.customer">
  <div class="content-new">

    <div class="content-inner-new" *ngIf="service.stockList">

      <!-- <div id="fieldChooserPanel" *ngIf="showFieldChooserPanel">
        <div class="infoPanel"><i class="fas fa-info-circle"></i>{{service.constants.translatedText.StockList_HelpText}}</div>
      </div> -->

      <div id="gridHolder" *ngIf="!!service.stockList.showGrid">
        <!-- <div id="counterArea">
          {{service.constants.pluralise(service.stockList.displayedRowsLength,this.service.constants.translatedText.Vehicle,this.service.constants.translatedText.Vehicles)}}
        </div> -->

        <!-- <div id="excelExport" (click)="excelExportOnClick()">
          <img [src]="service.constants.provideExcelLogo()">
        </div> -->
        
        <!-- Status bar -->
        <statusBar (excelExportClick)="excelExportOnClick()" [gridColumnApi]="gridColumnApi" [gridApi]="gridApi"></statusBar>

        <ag-grid-angular
          class="ag-theme-balham"
          [gridOptions]="mainTableGridOptions"
          
          
          
        >
        </ag-grid-angular>
      </div>



    </div>

  </div>
</div>






