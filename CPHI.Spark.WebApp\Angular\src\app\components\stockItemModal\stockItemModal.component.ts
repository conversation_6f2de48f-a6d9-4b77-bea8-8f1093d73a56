import { Component, OnInit, ViewChild, ElementRef, Input } from '@angular/core';
import { ConstantsService } from '../../services/constants.service'
import { SelectionsService } from '../../services/selections.service'
import { NgbModal, NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { StockListRow } from "src/app/pages/stockList/StockListRow";
import { StockItemModalService } from './stockItemModal.service';

@Component({
  selector: 'app-stockItemModal',
  templateUrl: './stockItemModal.component.html',
  styleUrls: ['./stockItemModal.component.scss']
})

export class StockItemModalComponent implements OnInit {
  @ViewChild('stockModal', { static: true }) stockModal: ElementRef;
  @Input() public givenStockItem: StockListRow; 
  public givenRetailerSiteId:number;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public service: StockItemModalService
  ) { }

  ngOnInit() {
    this.initParams();
  }

  initParams() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.service.initialise(this.givenStockItem, this.givenRetailerSiteId);
    this.launchModal();
  }

  launchModal() {

    this.selections.triggerSpinner.next({ show: false });
    this.modalService.open(this.stockModal, { windowClass: 'stockDetailsModal', size: 'lg', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {

    }, (reason) => {
      this.activeModal.close();
    });
    
  }
}
