﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Model.Services
{
   public static class StringTruncaterService
   {
      public static List<string> Truncate<T>(List<T> items)
      {
         List<string> truncateMessages = new();
         PropertyInfo vehicleRegPropInfo = typeof(T).GetProperty("VehicleReg"); // Get PropertyInfo for VehicleReg once

         foreach (var item in items)
         {
            

            var properties = typeof(T).GetProperties()
                .Where(p => p.PropertyType == typeof(string));

            foreach (var prop in properties)
            {
               var maxLengthAttr = prop.GetCustomAttribute<MaxLengthAttribute>();
               if (maxLengthAttr != null)
               {
                  int maxLength = maxLengthAttr.Length;
                  string currentValue = (string)prop.GetValue(item);

                  if (!string.IsNullOrEmpty(currentValue) && currentValue.Length > maxLength)
                  {
                     // Prepend the VehicleReg info if it exists
                     string vehicleRegPrefix = "";
                     if (vehicleRegPropInfo != null)
                     {
                        object vehicleRegValueObj = vehicleRegPropInfo.GetValue(item);
                        if (vehicleRegValueObj != null)
                        {
                           string vehicleRegValue = vehicleRegValueObj.ToString();
                           if (!string.IsNullOrEmpty(vehicleRegValue))
                           {
                              vehicleRegPrefix = $"VehicleReg: {vehicleRegValue} - ";
                           }
                        }
                     }


                     string message = $"{vehicleRegPrefix}Truncating {prop.Name} from {currentValue.Length} to {maxLength} characters.";
                     truncateMessages.Add(message);
                     prop.SetValue(item, currentValue.Substring(0, maxLength));
                  }
               }
            }
         }
         return truncateMessages;
      }
   }
}