﻿//using System;
//using System.Collections.Generic;
//using System.Text;

//namespace MadDevs.Abstractions.EventHub
//{
//    public class EnquiryEventBody
//    {
//        public CustomerEventBody Customer { get; set; }
//        public string Status { get; set; }
//        public string? Type { get; set; }
//        public int? AnnualOdometer { get; set; }
//        public int? ChangeCycle { get; set; }
//        public decimal? MonthlyBudget { get; set; }
//        public string? FinancePreference { get; set; }
//        public string? MethodOfContact { get; set; }
//        public string? SourceOfEnquiry { get; set; }
//        public string? OwnerName { get; set; }
//        public string? OwnerId { get; set; }
//        public EventVehicleModel? Vehicle { get; set; }
//        public EventVehicleModel? OrderedVehicle { get; set; }
//        public List<EventVehicleModel>? TradeIns { get; set; }
//        public string? LostSaleReason { get; set; }
//        public string? AcceptedQuotationId { get; set; }
//        public string? EnquiryVehicleType { get; set; }
//        public DateTime? OrderCreated { get; set; }
//        public DateTime? Delivered { get; set; }
//        public DateTime? DeliveryDate { get; set; }
//        public DateTime? FirstTestDriveDate { get; set; }
//        public BaseAppointment? LastAppointment { get; set; }
//        public BaseAppointment? NextAppointment { get; set; }
//    }
//}
