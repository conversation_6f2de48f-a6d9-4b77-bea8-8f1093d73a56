import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Subscription} from 'rxjs';

@Component({
  selector: 'sliderSwitch',
  templateUrl: './sliderSwitch.component.html',
  styleUrls: ['./sliderSwitch.component.scss']
})
export class SliderSwitchComponent implements OnInit {

  @Input() defaultValue!: boolean;
  @Input() blackFont: boolean;
  @Input() width: number;
  @Input() updateTrigger: EventEmitter<void>;
  @Input() text!: string;
  @Input() disabled!: boolean;
  @Input() styleClass: string;
  @Output() toggle: EventEmitter<void> = new EventEmitter<void>();

  updateTriggerSub: Subscription;

  constructor() {
  }

  ngOnInit() {
    if (this.defaultValue) {
      const input = document.getElementById("checkbox") as HTMLInputElement;
      if (input !== null) input.checked = true;
    }

    if (this.updateTrigger) {
      this.updateTriggerSub = this.updateTrigger.subscribe(res => {
        this.defaultValue = !this.defaultValue;
      })
    }
  }

  ngOnDestroy() {
    if (this.updateTriggerSub) {
      this.updateTriggerSub.unsubscribe()
    }
  }

  toggleDefaultValue(): void {
    this.toggle.emit();
  }
}
