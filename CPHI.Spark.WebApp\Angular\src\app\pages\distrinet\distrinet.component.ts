import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { UntypedFormControl } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ColDef, ColGroupDef, Column<PERSON>pi, <PERSON>rid<PERSON><PERSON> } from "ag-grid-community";
import { CphPipe } from "src/app/cph.pipe";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SeedDataService } from "src/app/services/seedData.service";
import { SelectionsService } from "src/app/services/selections.service";
import { localeEs } from "src/environments/locale.es.js";
import { SiteVM } from "../../model/main.model";
import { ConstantsService } from "../../services/constants.service";
import { ExcelExportService } from "../../services/excelExportService";
import { StockListRow } from "../stockList/StockListRow";
import { DistrinetModal, DistrinetParams, DistrinetRow } from "./distrinet.model";
import { DistrinetService } from "./distrinet.service";
import { DistrinetModalComponent } from "./modal/distrinetModal.component";
import { Subscription } from "rxjs";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";

@Component({
   selector: "app-distrinet",
   templateUrl: "./distrinet.component.html",
   styleUrls: ["./distrinet.component.scss"],
})
export class DistrinetComponent implements OnInit {
   @ViewChild("saveColumnDefinitionsModal", { static: true }) saveColumnDefinitionsModal: ElementRef;
   @ViewChild("reportNameModal", { static: true }) reportNameModal: ElementRef;
   @ViewChild("openColumnDefinitionsModal", { static: true }) openColumnDefinitionsModal: ElementRef;

   public gridApi: GridApi;
   public gridColumnApi: ColumnApi;

   filterState: any;

   filter = new UntypedFormControl("");
   months: Date[];

   mainTableGridOptions: any;
   frameworkComponents: {
      agColumnHeader: any;
   };

   waitUntil: Date;
   subscription: Subscription;

   constructor(
      public constants: ConstantsService,
      public selections: SelectionsService,
      public modalService: NgbModal,
      public columnTypeService: ColumnTypesService,
      public excel: ExcelExportService,
      public dataMethods: GetDataMethodsService,
      public seed: SeedDataService,
      public service: DistrinetService,
      public cphPipe: CphPipe,
      public gridHelpersService: AGGridMethodsService
   ) {}

   ngOnDestroy() {
      this.service.showGrid = false;
      if (this.subscription) this.subscription.unsubscribe();
   }

   ngOnInit() {
      this.initParams();
      this.getData();

      this.subscription = this.filter.valueChanges.subscribe((searchTerm) => {
         if (searchTerm.length > 0) {
            this.searchDistrinet(searchTerm);
         } else {
            if (!!this.gridApi) {
               this.gridApi.setRowData(this.service.rows);
               this.gridApi.sizeColumnsToFit();
            }
         }
      });
   }

   searchDistrinet(text: string): void {
      this.service.rowsFiltered = this.service.rows.filter((item) => {
         const term = text.toLowerCase();

         return Object.values(item).some((property) => {
            // Ensure that the property is a string before calling toLowerCase
            if (typeof property === "string") {
               return property.toLowerCase().includes(term);
            }
            return false;
         });
      });

      if (!!this.gridApi) {
         this.gridApi.setRowData(this.service.rowsFiltered);
         this.gridApi.sizeColumnsToFit();
      }

      // this.selections.wipReport.wipsRowsFiltered = result;
      // this.selections.wipReport.wipsChangedEmitter.next(true);
   }

   initParams() {
      if (!this.service.chosenFranchises) {
         this.service.initParams();
      }
      this.months = this.constants.makeDistrinetMonths();
      this.service.chosenSnapshot = this.service.chosenSnapshot
         ? this.service.chosenSnapshot
         : this.months[this.months.length - 1];
   }

   provideGridOptions() {
      return {
         getLocaleText: (params) =>
            this.constants.currentLang == "es" ? localeEs[params.key] || params.defaultValue : params.defaultValue,
         floatingFilter: true,
         //context: null,
         rowData: this.service.rows,
         getRowHeight: (params) => {
            let normalHeight = Math.min(22, Math.max(19, Math.round((22 * this.selections.screenHeight) / 960)));
            if (params.node.rowPinned) {
               return this.gridHelpersService.getRowPinnedHeight();
            } else {
               return this.gridHelpersService.getStandardHeight();
            }
         },
         onRowDoubleClicked: (params) => {
            this.loadDistrinetModal(params.data);
         },
         //onAnimationQueueEmpty: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
         onGridReady: (params) => this.onGridReady(params),
         defaultColDef: {
            filterParams: {
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            autoHeight: true,
            resizable: true,
            sortable: true,
            hide: false,
         },
         columnTypes: {
            ...this.columnTypeService.provideColTypes([]),
         },
         columnDefs: this.provideCols(),
      };
   }

   getData() {
      this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Common_Loading });

      let siteIds = this.selections.selectedSites
         ? this.selections.selectedSites.map((x) => x.SiteId)
         : this.selections.userSites.map((x) => x.SiteId);
      if (siteIds.length === this.selections.userSites.length) {
         siteIds = null;
      }
      let franchises = this.service.chosenFranchises;

      if (this.service.chosenFranchises.length === this.constants.FranchiseCodes.length) {
         franchises = null;
      }

      let params: DistrinetParams = {
         SiteIds: siteIds,
         Franchises: franchises,
         OriginTypes: this.service.chosenOriginTypes,
         ChosenDate: this.service.chosenSnapshot,
      };

      this.dataMethods.getDistrinetRows(params).subscribe((stringRes: string[]) => {
         //they arrive as strings, need to turn them into an object.
         let allCols = [
            "Id",
            "Age",
            "SalesmanName",
            "SiteId",
            "SiteDescription",
            "RegionDescription",
            "SiteDmsId",
            "Franchise",
            "OrderTypeType",
            "OriginType",
            "SnapshotDate",
            "Model",
            "Options",
            "Paint",
            "Version",
            "Antiquity",
            "Advance",
            "Chassis",
            "Interior",
            "EnergyType",
            "QualityLocked",
            "TradeLocked",
            "FinancialLocked",
            "TransportLocked",
            "TransportLockReason",
            "Customer",
            "SalesmanCode",
            "Flexibility",
            "MADACommitment",
            "SubPropAccount",
            "RequestNumber",
            "PgeoBCV",
            "CustomerType",
            "DRType",
            "RecipientAccount",
            "Comments",
            "CustomerDeliveryControlDate",
            "AgreedCustomerDeliveryDate",
            "TestDate",
            "ActualAffectationDate",
            "ActualAnnulmentDate",
            "OrderDate",
            "OrangeFlexEndDate",
            "GreenFlexEndDate",
            "InvoiceDate",
            "DADDate",
            "ModelCode",
            "OrderNumber",
            "AgeBandNow",
            "AgeBandEOM",
         ];

         let dateCols = [
            "CustomerDeliveryControlDate",
            "AgreedCustomerDeliveryDate",
            "TestDate",
            "ActualAffectationDate",
            "ActualAnnulmentDate",
            "OrderDate",
            "OrangeFlexEndDate",
            "GreenFlexEndDate",
            "InvoiceDate",
            "DADDate",
            "SnapshotDate",
         ];

         let dateColIndexes = [];

         allCols.forEach((col, i) => {
            if (dateCols.includes(col)) {
               dateColIndexes.push(i);
            }
         });

         let res: DistrinetRow[] = [];

         stringRes.forEach((stringItem: string) => {
            const splitOut = stringItem.split("|");
            let rowItem: DistrinetRow = {} as DistrinetRow;

            splitOut.forEach((textItem, i) => {
               let val = textItem as string | Date;

               if (dateColIndexes.includes(i)) {
                  val = this.tryGetDate(val as string);

                  if (val == undefined) {
                     val = null;
                  } else if (isNaN(val.getTime())) {
                     val = null;
                  }
               }

               let thisItemColName = allCols[i];

               if (thisItemColName.includes("Locked")) {
                  rowItem[thisItemColName] =
                     val == "0" ? this.constants.translatedText.Common_No : this.constants.translatedText.Common_Yes;
               } else {
                  rowItem[thisItemColName] = val;
               }
            });
            res.push(rowItem);
         });

         this.service.rows = res;

         if (!!this.gridApi) {
            this.gridApi.setRowData(this.service.rows);
         }

         this.mainTableGridOptions = this.provideGridOptions();
         this.service.showGrid = true;
         this.recalcDisplayedVehiclesCounter();
         this.selections.triggerSpinner.emit({ show: false });
      });
   }

   provideCols() {
      let cols: (ColDef | ColGroupDef)[] = [
         {
            headerName: this.constants.translatedText.Distrinet_OriginType,
            pinned: "left",
            field: "OriginType",
            colId: "OriginType",
            type: "label",
            width: 50,
         },
         // { headerName: this.constants.translatedText.Distrinet_SnapshotDate, pinned:'left',field: 'SnapshotDate', colId: 'SnapshotDate', type: 'date', width: 60 },
         {
            headerName: this.constants.translatedText.Distrinet_SiteDescription,
            pinned: "left",
            field: "SiteDescription",
            colId: "SiteDescription",
            type: "label",
            width: 100,
         },
         {
            headerName: this.constants.translatedText.Distrinet_RegionDescription,
            pinned: "left",
            field: "RegionDescription",
            colId: "RegionDescription",
            type: "label",
            width: 80,
         },
         {
            headerName: this.constants.translatedText.Distrinet_SiteDMSId,
            pinned: "left",
            field: "SiteDMSId",
            colId: "SiteDMSId",
            type: "label",
            width: 50,
         },

         {
            headerName: this.constants.translatedText.Distrinet_SalesmanName,
            pinned: "left",
            field: "SalesmanName",
            colId: "SalesmanName",
            type: "labelLeft",
            width: 160,
         },
         {
            headerName: this.constants.translatedText.Distrinet_SalesmanCode,
            pinned: "left",
            field: "SalesmanCode",
            colId: "SalesmanCode",
            type: "number",
            columnGroupShow: "open",
            width: 60,
         },

         {
            headerName: this.constants.translatedText.Distrinet_OrderTypeType,
            pinned: "left",
            field: "OrderTypeType",
            colId: "OrderTypeType",
            type: "label",
            width: 100,
         },
         {
            headerName: this.constants.translatedText.Distrinet_Franchise,
            pinned: "left",
            field: "Franchise",
            colId: "Franchise",
            cellRenderer: (params) => this.getFranchiseLogo(params),
            type: "label",
            width: 80,
         },
         {
            headerName: this.constants.translatedText.Distrinet_CustomerType,
            pinned: "left",
            field: "CustomerType",
            colId: "CustomerType",
            type: "label",
            columnGroupShow: "open",
            width: 120,
         },
         {
            headerName: this.constants.translatedText.Distrinet_Customer,
            field: "Customer",
            colId: "Customer",
            type: "label",
            columnGroupShow: "open",
            width: 110,
         },
         {
            headerName: this.constants.translatedText.Distrinet_RequestNumber,
            field: "OrderNumber",
            colId: "OrderNumber",
            type: "number",
            columnGroupShow: "open",
            width: 60,
         },

         {
            headerName: this.constants.translatedText.Distrinet_Flexibility,
            field: "Flexibility",
            colId: "Flexibility",
            type: "label",
            columnGroupShow: "open",
            width: 140,
         },
         {
            headerName: this.constants.translatedText.Distrinet_Model,
            field: "Model",
            colId: "Model",
            type: "label",
            width: 80,
         },
         {
            headerName: this.constants.translatedText.Distrinet_ModelCode,
            field: "ModelCode",
            colId: "ModelCode",
            type: "label",
            width: 60,
         },
         {
            headerName: this.constants.translatedText.Distrinet_Version,
            field: "Version",
            colId: "Version",
            type: "label",
            columnGroupShow: "open",
            width: 80,
         },

         {
            headerName: this.constants.translatedText.Distrinet_Chassis,
            field: "Chassis",
            colId: "Chassis",
            type: "label",
            columnGroupShow: "open",
            width: 140,
         },

         //Extra Vehicle Details
         {
            headerName: this.constants.translatedText.Distrinet_VehicleDetails,
            children: [
               {
                  headerName: this.constants.translatedText.Distrinet_EnergyType,
                  field: "EnergyType",
                  colId: "EnergyType",
                  cellRenderer: (params) => this.addEnergyTypeIcon(params),
                  type: "label",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_Paint,
                  field: "Paint",
                  colId: "Paint",
                  type: "label",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_Interior,
                  field: "Interior",
                  colId: "Interior",
                  type: "label",
                  columnGroupShow: "open",
                  width: 90,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_Options,
                  field: "Options",
                  colId: "Options",
                  type: "label",
                  columnGroupShow: "open",
                  width: 220,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_Antiquity,
                  field: "Antiquity",
                  colId: "Antiquity",
                  type: "number",
                  columnGroupShow: "open",
                  width: 40,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_Advance,
                  field: "Advance",
                  colId: "Advance",
                  type: "label",
                  columnGroupShow: "open",
                  width: 60,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_MADACommitment,
                  field: "MADACommitment",
                  colId: "MADACommitment",
                  type: "label",
                  columnGroupShow: "open",
                  width: 80,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_SubPropAccount,
                  field: "SubPropAccount",
                  colId: "SubPropAccount",
                  type: "number",
                  columnGroupShow: "open",
                  width: 80,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_PgeoBCV,
                  field: "PgeoBCV",
                  colId: "PgeoBCV",
                  type: "label",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_DRType,
                  field: "DRType",
                  colId: "DRType",
                  type: "label",
                  columnGroupShow: "open",
                  width: 60,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_RecipientAccount,
                  field: "RecipientAccount",
                  colId: "RecipientAccount",
                  type: "label",
                  columnGroupShow: "open",
                  width: 80,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_Comments,
                  field: "Comments",
                  colId: "Comments",
                  type: "label",
                  columnGroupShow: "open",
                  width: 160,
               },
            ],
         },

         //Dates
         {
            headerName: this.constants.translatedText.Distrinet_Dates,
            children: [
               {
                  headerName: this.constants.translatedText.Common_Age,
                  field: "AgeBandNow",
                  colId: "AgeBandNow",
                  type: "label",
                  width: 120,
               },
               {
                  headerName: `${this.constants.translatedText.Common_Age} ${this.constants.translatedText.Common_MonthEnd}`,
                  field: "AgeBandEOM",
                  colId: "AgeBandEOM",
                  type: "label",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_ActualAnnulmentDate,
                  field: "ActualAnnulmentDate",
                  colId: "ActualAnnulmentDate",
                  type: "date",
                  width: 120,
                  columnGroupShow: "open",
               },
               {
                  headerName: this.constants.translatedText.Distrinet_CustomerDeliveryControlDate,
                  field: "CustomerDeliveryControlDate",
                  colId: "CustomerDeliveryControlDate",
                  columnGroupShow: "open",
                  type: "date",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_DateOfOrderCreation,
                  field: "OrderDate",
                  colId: "OrderDate",
                  type: "date",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_AgreedCustomerDeliveryDate,
                  field: "AgreedCustomerDeliveryDate",
                  colId: "AgreedCustomerDeliveryDate",
                  type: "date",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_TestDate,
                  field: "TestDate",
                  colId: "TestDate",
                  type: "date",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_OrangeFlexEndDate,
                  field: "OrangeFlexEndDate",
                  colId: "OrangeFlexEndDate",
                  type: "date",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_GreenFlexEndDate,
                  field: "GreenFlexEndDate",
                  colId: "GreenFlexEndDate",
                  type: "date",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_ActualAffectationDate,
                  field: "ActualAffectationDate",
                  colId: "ActualAffectationDate",
                  type: "date",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_InvoiceDate,
                  field: "InvoiceDate",
                  colId: "InvoiceDate",
                  type: "date",
                  columnGroupShow: "open",
                  width: 120,
               },
               {
                  headerName: this.constants.translatedText.Distrinet_DADDate,
                  field: "DADDate",
                  colId: "DADDate",
                  type: "date",
                  columnGroupShow: "open",
                  width: 120,
               },
            ],
         },

         //Lock status
         {
            headerName: this.constants.translatedText.Distrinet_LockStatus,
            children: [
               {
                  headerName: this.constants.translatedText.Distrinet_QualityLocked,
                  field: "QualityLocked",
                  colId: "QualityLocked",
                  cellRenderer: (params) => this.addLockedStatusIcon(params),
                  type: "label",
                  width: 60,
                  cellClass: "agAlignCentre",
               },
               {
                  headerName: this.constants.translatedText.Distrinet_TradeLocked,
                  field: "TradeLocked",
                  colId: "TradeLocked",
                  cellRenderer: (params) => this.addLockedStatusIcon(params),
                  type: "label",
                  columnGroupShow: "open",
                  width: 60,
                  cellClass: "agAlignCentre",
               },
               {
                  headerName: this.constants.translatedText.Distrinet_FinancialLocked,
                  field: "FinancialLocked",
                  colId: "FinancialLocked",
                  cellRenderer: (params) => this.addLockedStatusIcon(params),
                  type: "label",
                  columnGroupShow: "open",
                  width: 60,
                  cellClass: "agAlignCentre",
               },
               {
                  headerName: this.constants.translatedText.Distrinet_TransportLocked,
                  field: "TransportLocked",
                  colId: "TransportLocked",
                  cellRenderer: (params) => this.addLockedStatusIcon(params),
                  type: "label",
                  columnGroupShow: "open",
                  width: 60,
                  cellClass: "agAlignCentre",
               },
               {
                  headerName: this.constants.translatedText.Distrinet_TransportLockReason,
                  field: "TransportLockReason",
                  colId: "TransportLockReason",
                  type: "label",
                  columnGroupShow: "open",
                  width: 100,
               },
            ],
         },
      ];

      if (this.gridApi) {
         this.autoSizeAll();
      }

      return cols;
   }

   getFranchiseLogo(params: any) {
      return `<span class="cell-with-icon">${params.value} <img width="20px" src="../../../assets/imgs/${params.value}LogoTransparent.png"></img></span>`;
   }

   onFirstDataRendered(thing) {
      if (!!this.service.filterModel) {
         this.gridApi.setFilterModel(this.service.filterModel); //<---- why does this have no effect??!
         this.gridApi.onFilterChanged();
      }
   }

   addEnergyTypeIcon(params: any) {
      let icon: string;
      if (params.value == "Diesel") icon = "fas fa-gas-pump";
      if (params.value == "Eléctrico") icon = "fas fa-charging-station electric-green";
      if (params.value == "Gasolina") icon = "fas fa-gas-pump petrol-green";
      if (params.value == "GLP") icon = "fas fa-burn blue";

      return `<span class="cell-with-icon">${params.value}<i class="${icon}"></i></span>`;
   }

   addLockedStatusIcon(params: any) {
      let icon: string;
      if (params.value == this.constants.translatedText.Common_No) icon = "fad fa-unlock green";
      if (params.value == this.constants.translatedText.Common_Yes) icon = "fad fa-lock red";

      return `<span class="cell-with-icon">${params.value}<i class="${icon}"></i></span>`;
   }

   loadDistrinetModal(row: DistrinetRow) {
      this.dataMethods.getDistrinetModalItem(row.Id, row.OriginType).subscribe((res: DistrinetModal) => {
         const modalRef = this.modalService.open(DistrinetModalComponent);
         modalRef.componentInstance.data = res;
      });
   }

   onFilterChanged() {
      this.filterState = this.gridApi.getFilterModel();
      this.recalcDisplayedVehiclesCounter();
   }

   search(text: string): StockListRow[] {
      if (!this.gridApi) return;
      this.gridApi.setQuickFilter(text);
   }

   recalcDisplayedVehiclesCounter() {
      setTimeout(() => {
         if (!this.gridApi) return 0;
         this.service.displayedRowsLength = this.gridApi.getDisplayedRowCount();
      }, 100);
   }

   onUpdateSites(sites: SiteVM[]) {
      // Set global
      this.selections.selectedSites = sites;
      this.selections.selectedSitesIds = [];

      this.selections.selectedSites.forEach((site) => {
         this.selections.selectedSitesIds.push(site.SiteId);
      });

      this.getData();
   }

   onUpdateFranchises(franchises: string[]): void {
      this.service.chosenFranchises = franchises;
      this.getData();
   }

   onUpdateOriginTypes(originTypes: string[]): void {
      this.service.chosenOriginTypes = originTypes;
      this.getData();
   }

   onUpdateChosenDate(chosenDate: Date) {
      this.service.chosenSnapshot = chosenDate;
      this.getData();
   }

   onGridReady(params) {
      this.gridApi = params.api;
      this.gridColumnApi = params.columnApi;
      this.gridColumnApi.autoSizeAllColumns();

      this.selections.triggerSpinner.emit({ show: false });
   }

   autoSizeAll() {
      var allColumnIds: string[] = [];
      this.gridColumnApi.getAllColumns().forEach((column) => {
         allColumnIds.push(column["colId"]);
      });
      //this.gridColumnApi.autoSizeColumns(allColumnIds);
   }

   excelExport() {
      //get tableModel from ag-grid
      let tableModel = this.gridApi.getModel();
      if (tableModel == null) {
         throw new Error("stockList - excelExport: Attempting to export null table model.");
      }
      this.excel.createSheetObject(tableModel, "Stock List", 1.5);
   }

   private tryGetDate(val: string): Date {
      if (!!val) {
         return this.constants.deductTimezoneOffset(new Date(val));
      }
   }

   isOriginActive(originType: string) {
      return this.service.chosenOriginTypes.includes(originType);
   }

   toggleOriginType(originType: string) {
      let index = this.service.chosenOriginTypes.findIndex((x) => x === originType);
      if (index > -1) {
         this.service.chosenOriginTypes.splice(index, 1);
      } else {
         this.service.chosenOriginTypes.push(originType);
      }

      this.getData();
   }

   // testGetItem(distrinetItemId: number) {
   //   this.dataMethods.getDistrinetModalItem(distrinetItemId).subscribe(res=>{
   //     console.log(res)
   //   })

   // }
}
