﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;

namespace CPHI.WebScraper.ViewModel
{

    public class ModixVehicle
    {
        public string Reg { get; set; }
        public int Price { get; set; }
        public int RetailerSiteId { get; set; }

        public bool IsCupra { get; set; }


      public ModixVehicle(PricingChangeNew pr)
      {
         bool isCupra = pr.Make.ToUpper().Contains("CUPRA");
         Reg = pr.VehicleReg;
         Price = pr.NewPrice;
         RetailerSiteId = pr.RetailerSiteId;
         IsCupra = isCupra;
      }

      // To create test items
      public ModixVehicle(string reg, int price, int retailerSiteId, bool isCupra)
      {
         Reg = reg;
         Price = price;
         RetailerSiteId = retailerSiteId;
         IsCupra = isCupra;
      }

   }


}
