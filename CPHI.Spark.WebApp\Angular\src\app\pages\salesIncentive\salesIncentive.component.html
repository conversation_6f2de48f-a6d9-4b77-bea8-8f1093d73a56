<nav class="navbar">
    <nav class="generic">
        <h4 id="pageTitle">
            Dacia Spring Orders
        </h4>
    </nav>

    <nav class="pageSpecific">
        <button class="btn btn-primary" (click)="refresh()">
            Refresh
        </button>
    </nav>
</nav>

<div id="overallHolder" class="single-line-nav">
    <div class="content-new">
        <div class="fireworks-container">
            <div class="fireworks">
              <div class="before"></div>
              <div class="after"></div>
            </div>
          </div>
        <div class="content-inner-new">
            <div *ngIf="deals" id="podiumAndTableContainer">
                <div id="podium">
                    <div class="podiumStep second">
                        <img src="../../../assets/imgs/events/salesIncentive/Silver-Trophy-PNG.png" class="trophy">
                        <div class="position">
                            <div id="secondPrize" class="prize">
                                <h2 class="value"><strong>£300</strong></h2>
                                <div class="ribbon">
                                    <div>2nd</div>
                                </div>
                            </div>
                        </div>
                        <div class="nameAndCount">
                            <h3><strong>{{ deals[1].SalesPerson }}</strong></h3>
                            <div class="count">{{ deals[1].DealCount }}</div>
                        </div>
                    </div>
                    <div class="podiumStep first">
                        <img src="../../../assets/imgs/events/salesIncentive/Gold-Trophy-PNG.png" class="trophy">
                        <div class="position">
                            <div id="firstPrize" class="prize">
                                <h2 class="value"><strong>£500</strong></h2>
                                <div class="ribbon">
                                    <div>1st</div>
                                </div>
                            </div>
                        </div>
                        <div class="nameAndCount">
                            <h3><strong>{{ deals[0].SalesPerson }}</strong></h3>
                            <div class="count">{{ deals[0].DealCount }}</div>
                        </div>
                    </div>
                    <div class="podiumStep third">
                        <img src="../../../assets/imgs/events/salesIncentive/Bronze-Trophy-PNG.png" class="trophy">
                        <div class="position">
                            <div id="thirdPrize" class="prize">
                                <h2 class="value"><strong>£200</strong></h2>
                                <div class="ribbon">
                                    <div>3rd</div>
                                </div>
                            </div>
                        </div>
                        <div class="nameAndCount">
                            <h3><strong>{{ deals[2].SalesPerson }}</strong></h3>
                            <div class="count">{{ deals[2].DealCount }}</div>
                        </div>
                    </div>
                </div>
                <div id="table">
                    <table>
                        <thead>
                            <tr>
                                <th>Pos</th>
                                <th>Salesperson</th>
                                <th>Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let deal of deals; index as i;">
                                <td>{{ i+1 }}</td>
                                <td>{{ deal.SalesPerson }}</td>
                                <td>{{ deal.DealCount }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>