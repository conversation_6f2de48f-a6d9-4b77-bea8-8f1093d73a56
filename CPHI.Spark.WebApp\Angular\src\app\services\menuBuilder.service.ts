import { Injectable } from "@angular/core";
import { MenuItem, MenuItemNew, MenuSection } from "../model/main.model";
import { TranslatedText } from "../model/translations.model";
import { ConstantsService } from "./constants.service";
import { SelectionsService } from "./selections.service";
import { GlobalParamsService } from "./globalParams.service";
import { GlobalParamKey } from "../model/GlobalParam";
import { SparkEnvironment } from "./environment.service";

@Injectable({ providedIn: 'root' })
export class MenuBuilderService {


  constructor(

    private constants: ConstantsService,
    private selections: SelectionsService,
    private globalParamsService: GlobalParamsService

  ) { }


  public buildMenuItemsNew(openItems: string[]): MenuSection[] {
    
    //---------------- how this works ---------------------
    
    //each section has subItems
    //each subItem has link
    //each subItem has pageName.  this must be unique
    
    
    //-------------------------------------
    
    const translated: TranslatedText = this.constants.translatedText;
    const env = this.constants.environment;
    const perms = this.selections.user.permissions;

    const menuItems: MenuSection[] = [];
    
    //this.testUniquenessOfPageNames(env, translated); //don't delete this

    if (env.menuItems_dashboard_HasDashboard) {
      menuItems.push(
        ...this.provideDashboards(env, translated),
      )
    }

    if (env.menuItems_operationalReports_HasOperationReports) {
      menuItems.push(
        ...this.provideOperationalReports(env, translated),
      )
    }

    if (env.menuItems_salesReports_HasSalesReports) {
      menuItems.push(
        ...this.provideSalesReport(env, translated),
      )
    }

    if (env.menuItems_reportPortal) {
      menuItems.push(
        // REPORT PORTAL
        {
          group: 'reportPortal',
          name: translated.ReportPortal_Title,
          subItems: [
            {
              isSales: null, nameAbbreviated: translated.ReportPortal_Title, name: translated.ReportPortal_Title,
              icon: 'fas fa-file-chart-pie fa-fw', link: '/reportingCentre', pageName: 'ReportingCentre',
              isActive: false, visible: env.menuItems_reportPortal, parent: 'reportPortal'
            }
          ],
          expanded: null,
        },
      )
    }

    if (env.menuItems_aftersalesReports_HasAftersalesReports) {
      menuItems.push(
        ...this.provideAfterSalesReport(env, translated),
      )
    }

    if (env.menuItems_peopleReports_HasPeopleReports) {
      menuItems.push(
        ...this.providePeopleReports(env, translated),
      )
    }

    if (env.menuItems_vehiclePricing_HasVehiclePricing && !env.menuItems_vehiclePricing_ShowDetailedMenu) {
      menuItems.push(
        ...this.providePricingPages(env),
      )
    }

    if (env.menuItems_vehiclePricing_HasVehiclePricing && env.menuItems_vehiclePricing_ShowDetailedMenu) {
      menuItems.push(...this.providePricingSeparatePages(env))
    }


    //The various separate ones
    menuItems.push(...this.provideMaintenanceReports(env, translated))



    if (openItems) {
      menuItems.map(item => {
        item.expanded = openItems.includes(item.name);
        if (item.subItems) {
        }
      })
    }

    return menuItems;
  }










  private testUniquenessOfPageNames(env, translated: TranslatedText) {
    
    const names = [];
    this.testDupes(this.provideDashboards(env, translated), names);
    this.testDupes(this.provideOperationalReports(env, translated), names);
    this.testDupes(this.provideSalesReport(env, translated), names);
    this.testDupes(this.provideAfterSalesReport(env, translated), names);
    this.testDupes(this.providePeopleReports(env, translated), names);
    this.testDupes(this.providePricingPages(env), names);
    this.testDupes(this.providePricingSeparatePages(env), names);
    this.testDupes(this.provideMaintenanceReports(env, translated), names);
  }

  private testDupes(sections: MenuSection[], names: any[]) {
    sections.forEach(section => {
      section.subItems.forEach(menuItem => {
        let name = menuItem.pageName;
        if (names.includes(name)) {
          console.error(`Duplicate name ${name}`);
        }
        names.push(name);
      });
    });
  }

  private provideDashboards(env, translated: TranslatedText) {
    return [
      {
        group: 'dashboards',
        name: 'Dashboards',
        icon: 'fas fa-tachometer-fast fa-fw',
        isActive: false,
        visible: env.menuItems_dashboard_HasDashboard,
        expanded: true,
        nameAbbreviated: 'Dash boards',
        subItems: [
          //Overview Spain
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: 'Home', name: 'Home', icon: 'fa-solid fa-gauge-simple-high',
            link: '/dashboard', pageName: 'dashboardOverviewSpain', isActive: false, visible: env.menuItems_dashboard_Home && this.selections.user.permissions.seeDashboard, isDashboard: true, parent: 'dashboards'
          },
          //OVERVIEW
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.Overview, name: translated.Overview,
            icon: 'fa-solid fa-gauge-simple-high', link: '/dashboard', pageName: 'dashboardOverview', isActive: false, visible: env.menuItems_dashboard_Overview, isDashboard: true, parent: 'dashboards'
          },
          //Sales RRG
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.Sales, name: translated.Sales,
            icon: 'fa-solid fa-car-side', link: '/dashboard', pageName: 'dashboardSalesRRG', isActive: false,
            visible: env.menuItems_dashboard_Sales && this.constants.environment.dashboard_showRRGSalesDashboard, isDashboard: true, parent: 'dashboards'
          },
          //Sales Vindis
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.Sales, name: translated.Sales,
            icon: 'fa-solid fa-car-side', link: '/dashboard', pageName: 'dashboardSalesVindis', isActive: false,
            visible: env.menuItems_dashboard_Sales && this.constants.environment.dashboard_showVindisSalesDashboard, isDashboard: true, parent: 'dashboards'
          },
          //New VN Spain
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.Dashboard_NewKPIsTitle, name: translated.Dashboard_NewKPIsTitle,
            icon: 'fa-solid fa-car-side', link: '/dashboard', pageName: 'dashboardNewVNSpain', isActive: false, visible: env.menuItems_dashboard_NewKPIs, isDashboard: true, parent: 'dashboards'
          },
          //Used Spain
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.Dashboard_UsedKPIsTitle, name: translated.Dashboard_UsedKPIsTitle,
            icon: 'fa-solid fa-car-side', link: '/dashboard', pageName: 'dashboardUsedSpain', isActive: false, visible: env.menuItems_dashboard_UsedKPIs, isDashboard: true, parent: 'dashboards'
          },
          //Aftersales RRG
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.Aftersales, name: translated.Aftersales, icon: 'fa-solid fa-wrench',
            link: '/dashboard', pageName: 'dashboardAfterSalesRRG', isActive: false, visible: env.menuItems_dashboard_Aftersales && this.constants.environment.dashboard_showRRGAftersalesDashboard,
            isDashboard: true, parent: 'dashboards'
          },
          //Aftersales Vindis
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.Aftersales, name: translated.Aftersales, icon: 'fa-solid fa-wrench',
            link: '/dashboard', pageName: 'dashboardAfterSalesVindis', isActive: false, visible: env.menuItems_dashboard_Aftersales && this.constants.environment.dashboard_showVindisAftersalesDashboard,
            isDashboard: true, parent: 'dashboards'
          },
          //Aftersales
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.Aftersales, name: translated.Aftersales, icon: 'fa-solid fa-wrench',
            link: '/dashboard', pageName: 'dashboardAftersales', isActive: false, visible: env.menuItems_dashboard_Aftersales && this.constants.environment.dashboard_showSpainAftersalesDashboard,
            isDashboard: true, parent: 'dashboards'
          },
          //Site Compare
          {
            subItems: null, isSales: null, expanded: false, nameAbbreviated: translated.SiteCompare, name: translated.SiteCompare, icon: 'fa-solid fa-building-flag',
            link: '/dashboard', pageName: 'dashboardSiteCompare', isActive: false, visible: env.menuItems_dashboard_SiteCompare, isDashboard: true, parent: 'dashboards'
          }
        ],
        link: null,
        pageName: null,
        isSales: null
      },
    ]
  }

  private provideOperationalReports(env: SparkEnvironment, translated: TranslatedText) {

    let orderbookTitle: string = this.constants.environment.languageSelection ? 
                                 this.constants.translatedText.Orderbook_TitleSpain : this.constants.environment.orderBook_orderbookTitle;

    const perms = this.selections.user.permissions;

    return [
      {
        group: 'operationreports',
        name: 'Operational Reports',
        icon: 'fas fa-tachometer-fast fa-fw',
        isActive: false,
        visible: env.menuItems_operationalReports_HasOperationReports,
        nameAbbreviated: 'Op. Reports',
        subItems: [
          //Retail Orderbook
          {
            pageName: 'retailOrderbook', subItems: null, expanded: false, nameAbbreviated: orderbookTitle, name: orderbookTitle,
            icon: 'fas fa-list-ol fa-fw', link: this.constants.environment.orderBookURL, isActive: false, visible: env.menuItems_orderbook_Retail && perms.seeOrderbook, isSales: false,
            parent: 'operationreports'
          },
          //Distrinet
          {
            pageName: 'distrinet', subItems: null, expanded: false, nameAbbreviated: 'Retail Order book', name: 'Distrinet',
            icon: 'fas fa-cars fa-fw', link: '/distrinet', isActive: false, visible: env.menuItems_orderbook_Distrinet && perms.seeOrderbook, isSales: false, parent: 'operationreports'
          },
          //Fleet Orderbook
          {
            pageName: 'fleetOrderbook', subItems: null, expanded: false, nameAbbreviated: 'Fleet Order book', name: 'Fleet Order book',
            icon: 'fas fa-th-list', link: this.constants.environment.fleetOrderbookURL, isActive: false, visible: env.menuItems_orderbook_Fleet && perms.seeFleetOrderbook, isSales: false, parent: 'operationreports'
          },
          //Deals  for the week
          {
            pageName: 'dealsDoneThisWeek', subItems: null, expanded: false, nameAbbreviated: 'Deals For Week', name: translated.DealsDoneThisWeek_Title,
            icon: 'far fa-heart-rate fa-fw', link: '/dealsDoneThisWeek', isActive: false, visible: env.menuItems_operationalReports_DealsWeek && perms.seeDealsDoneThisWeek, isSales: false, parent: 'operationreports'
          },
          //Deals for the month
          {
            pageName: 'dealsForTheMonth', subItems: null, expanded: false, nameAbbreviated: 'Deals For Month', name: translated.DealsDoneThisMonth_TitleShort,
            icon: 'far fa-chart-line fa-fw', link: '/dealsForTheMonth', isActive: false, visible: env.menuItems_operationalReports_DealsMonth && perms.seeDealsForTheMonth, isSales: false, parent: 'operationreports'
          },
          //Whiteboard
          {
            pageName: 'whiteboard', subItems: null, expanded: false, nameAbbreviated: translated.Whiteboard, name: translated.Whiteboard,
            icon: 'fas fa-th fa-fw', link: '/whiteboard', isActive: false, visible: env.menuItems_operationalReports_Whiteboard && perms.seeDealsDoneThisWeek, isSales: false, parent: 'operationreports'
          },
          //Handover diary
          {
            pageName: 'handoverDiary', subItems: null, expanded: false, nameAbbreviated: translated.HandoverDiary_Title, name: translated.HandoverDiary_Title,
            icon: 'far fa-calendar-alt fa-fw', link: '/handoverDiary', isActive: false, visible: env.menuItems_operationalReports_HandoverDiary && perms.seeHandoverDiary, isSales: false, parent: 'operationreports'
          },
          //Telephone stats
          {
            subItems: '', pageName: 'telephoneStats', expanded: false, nameAbbreviated: translated.Dashboard_TelephoneStats, name: translated.Dashboard_TelephoneStats,
            icon: 'fa-solid fa-phone', link: '/dashboard', isActive: false, visible: env.menuItems_operationalReports_TelephoneStats, isSales: false, parent: 'operationreports'
          },
          //Stock Landing
          {
            pageName: 'stockLanding', subItems: null, expanded: false, nameAbbreviated: translated.StockLanding_Title, name: translated.StockLanding_Title,
            icon: 'fas fa-plane-arrival fa-fw', link: '/stockLanding', isActive: false, visible: env.menuItems_operationalReports_StockLanding && perms.seeStockLanding, isSales: false, parent: 'operationreports'
          },
          //Performance Trends
          {
            pageName: 'performanceTrends', subItems: null, expanded: false, nameAbbreviated: 'Perf. Trends', name: 'Performance Trends', icon: 'far fa-analytics fa-fw',
            link: '/performanceTrends', isActive: false, visible: env.menuItems_operationalReports_PerformanceTrends && perms.seePerformanceTrends, isSales: false, parent: 'operationreports'
          }
        ],
        link: null,
        pageName: null,
        expanded: null,
        isSales: null
      },
    ]
  }

  private provideAfterSalesReport(env: SparkEnvironment, translated: TranslatedText) {
    return [
      {
        group: 'aftersalesreports',
        name: 'Aftersales Reports',
        icon: 'fas fa-tachometer-fast fa-fw',
        isActive: false,
        visible: env.menuItems_aftersalesReports_HasAftersalesReports,
        nameAbbreviated: 'Aftersales Reports',
        subItems: [
          //Service sales
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_ServiceSales_Title,
            name: translated.Dashboard_ServiceSales_Title, icon: 'fa-solid fa-wrench', link: '/dashboard', pageName: 'ServiceSales',
            isActive: false, visible: env.menuItems_aftersalesReports_ServiceSales, parent: 'aftersalesreports'
          },
          //Service bookings
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_ServiceBookings_Title,
            name: translated.Dashboard_ServiceBookings_Title, icon: 'fa-regular fa-calendar', link: '/dashboard', pageName: 'ServiceBookings',
            isActive: false, visible: env.menuItems_aftersalesReports_ServiceBookings, parent: 'aftersalesreports'
          },
          //EVHC
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_Evhc_Title, name: translated.Dashboard_Evhc_Title,
            icon: 'fa-solid fa-notes-medical', link: '/dashboard', pageName: 'EVHC', isActive: false, visible: env.menuItems_aftersalesReports_EVHC, parent: 'aftersalesreports'
          },
          //Upsells
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_Upsells, name: translated.Dashboard_Upsells,
            icon: 'fa-solid fa-wrench', link: '/dashboard', pageName: 'Upsells', isActive: false, visible: env.menuItems_aftersalesReports_Upsells, parent: 'aftersalesreports'
          },
          //WIP
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_WipReport_Title, name: translated.Dashboard_WipReport_Title,
            icon: 'fa-solid fa-timer', link: '/dashboard', pageName: 'Wip', isActive: false, visible: env.menuItems_aftersalesReports_WIPReport, parent: 'aftersalesreports'
          },
          //Debtors
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_Debtors_Title,
            name: translated.Dashboard_Debtors_Title, icon: 'fa-solid fa-coins', link: '/dashboard', pageName: 'DebtorsAftersales', isActive: false, visible: env.menuItems_aftersalesReports_Debtors, parent: 'aftersalesreports'
          },
          //CitNow
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_CitNow_Title,
            name: translated.Dashboard_CitNow_Title, icon: 'fa-solid fa-camera', link: '/dashboard', pageName: 'CitNowAftersales', isActive: false, visible: env.menuItems_aftersalesReports_CitNOW, parent: 'aftersalesreports'
          },
          //Parts
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_PartsSales_Title,
            name: translated.Dashboard_PartsSales_Title, icon: 'fa-solid fa-wrench', link: '/dashboard', pageName: 'PartsSales', isActive: false,
            visible: env.menuItems_aftersalesReports_PartsSales, parent: 'aftersalesreports'
          },
          //Parts Stock
          {
            isSales: null, subItems: null, expanded: false, nameAbbreviated: translated.PartsStock,
            name: translated.PartsStock, icon: 'fa-solid fa-gears', link: '/dashboard', pageName: 'PartsStock', isActive: false, visible: env.menuItems_aftersalesReports_PartsStock, parent: 'aftersalesreports'
          }
        ],
        link: null,
        pageName: null,
        expanded: null,
        isSales: null
      },
    ]
  }

  private provideSalesReport(env: SparkEnvironment, translated: TranslatedText) {
    return [
      {
        group: 'salesreports',
        name: 'Sales Reports',
        icon: 'fas fa-tachometer-fast fa-fw',
        isActive: false,
        visible: env.menuItems_salesReports_HasSalesReports,
        nameAbbreviated: 'Sales Reports',
        subItems: [

          //Sales Performance
          {
            subItems: null, expanded: false, nameAbbreviated: 'Sales Perf.', name: translated.Dashboard_SalesPerformance_Title, icon: 'fa-solid fa-car-side',
            link: '/dashboard', pageName: 'SalesPerformance', isActive: false, visible: env.menuItems_salesReports_SalesPerformance, isSales: false, parent: 'salesreports'
          },
          //Alcopas
          {
            subItems: null, expanded: false, nameAbbreviated: 'Alcopas', name: 'Alcopas', icon: 'fa-solid fa-car-side', link: '/dashboard', pageName: 'Alcopa',
            isActive: false, visible: env.menuItems_salesReports_Alcopas, isSales: false, parent: 'salesreports'
          },
          //Order Rate
          {
            subItems: null, expanded: false, nameAbbreviated: translated.OrderRate, name: translated.OrderRate, icon: 'fa-solid fa-car-side',
            link: '/dashboard', pageName: 'OrderRate', isActive: false, visible: env.menuItems_salesReports_OrderRate, isSales: false, parent: 'salesreports'
          },
          //Registrations
          {
            subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_Registrations_Title, name: translated.Dashboard_Registrations_Title,
            icon: 'fa-solid fa-car', link: '/dashboard', pageName: 'Registrations', isActive: false, visible: env.menuItems_salesReports_Registrations, isSales: false, parent: 'salesreports'
          },
          //F&I
          {
            subItems: null, expanded: false, nameAbbreviated: translated.FinanceAddons_Title, name: translated.FinanceAddons_Title, icon: 'fa-solid fa-list',
            link: '/dashboard', pageName: 'FinanceAddOns', isActive: false, visible: env.menuItems_salesReports_FAndI, isSales: false, parent: 'salesreports'
          },
          //Stock Reports
          {
            subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_StockReport_Title, name: translated.Dashboard_StockReport_Title,
            icon: 'fa-solid fa-car-side', link: '/dashboard', pageName: 'StockReport', isActive: false, visible: env.menuItems_salesReports_StockReports, isSales: false, parent: 'salesreports'
          },
          //Stock List
          {
            subItems: null, expanded: false, nameAbbreviated: translated.StockList_Title, name: translated.StockList_Title, icon: 'fa-solid fa-cars',
            link: '/stockList', pageName: 'stockList', isActive: false, visible: env.menuItems_salesReports_StockList && this.selections.user.permissions.seeStockList, isSales: false, parent: 'salesreports'
          },
          //Debtors
          {
            subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_Debtors_Title, name: translated.Dashboard_Debtors_Title, icon: 'fa-solid fa-coins',
            link: '/dashboard', pageName: 'Debtors', isActive: false, visible: env.menuItems_salesReports_Debtors, isSales: false, parent: 'salesreports'
          },
          //CitNow
          {
            subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_CitNow_Title, name: translated.Dashboard_CitNow_Title, icon: 'fa-solid fa-camera',
            link: '/dashboard', pageName: 'CitNow', isActive: false, visible: env.menuItems_salesReports_CitNOW, isSales: true, parent: 'salesreports'
          },
          //Image Ratios
          {
            subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_ImageRatios_Title, name: translated.Dashboard_ImageRatios_Title, icon: 'fa-solid fa-camera',
            link: '/dashboard', pageName: 'ImageRatios', isActive: false, visible: env.menuItems_salesReports_ImageRatios, isSales: true, parent: 'salesreports'
          },
          //Activities
          {
            subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_Activities, name: translated.Dashboard_Activities, icon: 'fa-solid fa-chart-network',
            link: '/dashboard', pageName: 'Activities', isActive: false, visible: env.menuItems_salesReports_Activities, isSales: true, parent: 'salesreports'
          },
          //GDPR
          {
            subItems: null, expanded: false, nameAbbreviated: translated.Dashboard_GDPRCapture, name: translated.Dashboard_GDPRCapture, icon: 'fa-solid fa-lock',
            link: '/dashboard', pageName: 'GDPR', isActive: false, visible: env.menuItems_salesReports_GDPR, isSales: true, parent: 'salesreports'
          }
        ],
        link: null,
        pageName: null,
        expanded: null,
        isSales: null
      },
    ]
  }

  private providePeopleReports(env: SparkEnvironment, translated: TranslatedText): MenuSection[] {
    const perms = this.selections.user.permissions;

    return [
      {
        group: 'peoplereports',
        name: 'People Reports',
        subItems: [

          //Performance League
          {
            pageName: 'performanceLeague', isSales: null, nameAbbreviated: 'Perf. League', name: translated.PerformanceLeague_Title,
            icon: 'fas fa-star fa-fw', link: '/performanceLeague', isActive: false, visible: env.menuItems_peopleReports_PerformanceLeague && perms.seePerformanceLeague, parent: 'peoplereports'
          },
          //Salesman efficiency
          {
            isSales: null, nameAbbreviated: 'Sales Efficiency', name: translated.Dashboard_SalesmanEfficiency_Title,
            icon: 'fa-solid fa-user', link: '/dashboard', pageName: 'SalesmanEfficiency', isActive: false, visible: env.menuItems_peopleReports_SalespersonEffeciency, parent: 'peoplereports'
          },

          //Commission
          {
            pageName: 'salesCommission', isSales: null, nameAbbreviated: `${translated.Sales} ${translated.Commission}`,
            name: `${translated.Sales} ${translated.Commission}`, icon: 'fas fa-money-bill-wave fa-fw', link: '/salesCommission', isActive: false,
            visible: env.menuItems_peopleReports_SalespersonCommission && (perms.reviewCommission || perms.selfOnlyCommission) && this.constants.environment.dashboard_showCommissions, parent: 'peoplereports'
          },

          //Scratch Card
          {
            pageName: 'scratchCard', isSales: null, nameAbbreviated: 'Scratch Card', name: 'Scratchcard', icon: 'fas fa-coin fa-fw',
            link: '/scratchCard', isActive: false, visible: env.menuItems_peopleReports_Scratchcard && this.globalParamsService.getGlobalParam(GlobalParamKey.webAppShowScratchcard) && perms.seeScratchCards, parent: 'peoplereports'
          },

          //Sales Exec Review
          {
            pageName: 'salesExecReview', isSales: null, nameAbbreviated: 'Sales Exec Review', name: 'Sales Exec Review',
            icon: 'fas fa-handshake', link: '/salesExecReview', isActive: false, visible: env.menuItems_peopleReports_SalesExecReview, parent: 'peoplereports'
          },


        ],
        expanded: null,
      },
    ]
  }

  private providePricingPages(env): MenuSection[] {
    const perms = this.selections.user.permissions;

    return [
      {
        name: 'Vehicle Pricing',
        group: 'vehiclepricing',
        subItems: [
          {
            pageName: 'home', isSales: null, nameAbbreviated: 'Vehicle Valuation',
            name: 'Home', icon: 'fa fa-house', link: '/home', isActive: false, visible: env.menuItems_vehiclePricing_VehicleValuation, parent: 'vehiclepricing'
          },

          //Sites League  
          {
            pageName: 'sitesLeague', isSales: null, nameAbbreviated: 'Sites League',
            name: 'Sites League', icon: 'fa-solid fa-chart-bar', link: '/sitesLeague', isActive: false, visible: env.menuItems_vehiclePricing_Dashboard && this.constants.RetailerSites.length > 1, parent: 'vehiclepricing'
          },
          //Site Dashboard
          {
            pageName: 'statsDashboard', isSales: null, nameAbbreviated: 'Site Dashboard',
            name: 'Site Dashboard', icon: 'fa-solid fa-chart-column', link: '/statsDashboard', isActive: false, visible: env.menuItems_vehiclePricing_Dashboard, parent: 'vehiclepricing'
          },
          //Stock Dashboard
          {
            pageName: 'stockDashboard', isSales: null, nameAbbreviated: 'Stock Dashboard',
            name: 'Stock Dashboard', icon: 'fa-solid fa-chart-scatter', link: '/stockDashboard', isActive: false, visible: env.menuItems_vehiclePricing_Dashboard, parent: 'vehiclepricing'
          },
          //Stock Quick Search
          {
            pageName: 'advertSimpleListing', isSales: null, nameAbbreviated: 'Stock Quick Search',
            name: 'Stock Quick Search', icon: 'fa fa-magnifying-glass', link: '/advertSimpleListing', isActive: false, visible: env.menuItems_vehiclePricing_StockQuickSearch && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
          //Stock Reports
          {
            pageName: 'stockReports', isSales: null, nameAbbreviated: 'Stock Report',
            name: 'Stock Report', icon: 'fas fa-car fa-fw', link: '/stockReports', isActive: false, visible: env.menuItems_vehiclePricing_StockReport, parent: 'vehiclepricing'
          },
          //Leaving Vehicle Trends
          {
            pageName: 'leavingVehicleTrends', isSales: null, nameAbbreviated: 'Leaving V. Trends',
            name: 'Leaving Vehicle Trends', icon: 'fas fa-arrow-trend-up', link: '/leavingVehicleTrends', isActive: false, 
            visible: this.selections.user.Email==='<EMAIL>' && env.menuItems_vehiclePricing_LeavingVehicleTrends && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
          //Leaving Vehicle Trends Over Time
          {
            pageName: 'leavingVehicleTrendsOverTime', isSales: null, nameAbbreviated: 'Leaving Trends Change',
            name: 'Trends Over Time', icon: 'fas fa-arrow-trend-up', link: '/leavingVehicleTrendsOverTime', isActive: false, visible: env.menuItems_vehiclePricing_LeavingVehicleTrends && perms.canReviewStockPrices && this.selections.user.Email==='<EMAIL>', parent: 'vehiclepricing'
          },
          //Leaving Vehicle Detail
          {
            pageName: 'leavingVehicleDetail', isSales: null, nameAbbreviated: 'Leaving V. Detail',
            name: 'Leaving Vehicle Detail', icon: 'fas fa-magnifying-glass-chart', link: '/leavingVehicleDetail', isActive: false, visible: env.menuItems_vehiclePricing_LeavingVehicleDetail && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
          //Vehicle Valuation
          {
            pageName: 'bulkValuation', isSales: null, nameAbbreviated: 'Vehicle Valuation',
            name: 'Vehicle Valuation', icon: 'fa fa-pound-sign', link: '/bulkValuation', isActive: false, visible: env.menuItems_vehiclePricing_VehicleValuation, parent: 'vehiclepricing'
          },
          //Buying Opportunities
          {
            pageName: 'localBargains', isSales: null, nameAbbreviated: 'Buying Ops.',
            name: 'Buying Opportunities', icon: 'fas fa-gift', link: '/localBargains', isActive: false, visible: env.menuItems_vehiclePricing_BuyingOpportunities, parent: 'vehiclepricing'
          },
          //Location Optimiser
          {
            pageName: 'locationOptimiser', isSales: null, nameAbbreviated: 'Location Optimiser',
            name: 'Location Optimiser', icon: 'fa fa-map-marker-alt', link: '/locationOptimiser', isActive: false, visible: env.menuItems_vehiclePricing_LocationOptimiser && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },

          //Price Changes for today
          {
            pageName: 'todaysPrices', isSales: null, nameAbbreviated: "Price Changes",
            name: "Today's Price Changes", icon: 'fas fa-barcode-read fa-fw', link: '/todaysPrices', isActive: false,
            visible: env.menuItems_vehiclePricing_TodaysPriceChanges && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
          //Opted Out Vehicles
          {
            pageName: 'optOuts', isSales: null, nameAbbreviated: 'Opted Out Vehicles',
            name: 'Opted Out Vehicles', icon: 'fas fa-bell-slash', link: '/optOuts', isActive: false, visible: env.menuItems_vehiclePricing_OptedOutVehicles && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },

        ],
        expanded: null,
      },
    ]
  }



  private providePricingSeparatePages(env: SparkEnvironment): MenuSection[] {
    const perms = this.selections.user.permissions;

    return [
      {
        group: 'home',
        name: 'Home',
        subItems: [
          {
            pageName: 'home', isSales: null, nameAbbreviated: 'Vehicle Valuation',
            name: 'Home', icon: 'fa fa-house', link: '/home', isActive: false, visible: env.menuItems_vehiclePricing_Home, parent: 'vehiclepricing'
          }
        ],
        expanded: null
      },
      {
        group: 'vehiclepricingDashboards',
        name: 'Dashboards',
        subItems: [
          //Sites League  
          {
            pageName: 'sitesLeagueSeparate', isSales: null, nameAbbreviated: 'Sites League',
            name: 'Sites League', icon: 'fa-solid fa-chart-bar', link: '/sitesLeague', isActive: false, visible: env.menuItems_vehiclePricing_Dashboard && this.constants.RetailerSites.length > 1, parent: 'vehiclepricing'
          },
          //Site Dashboard
          {
            pageName: 'statsDashboardSeparate', isSales: null, nameAbbreviated: 'Site Dashboard',
            name: 'Site Dashboard', icon: 'fa-solid fa-chart-column', link: '/statsDashboard', isActive: false, visible: env.menuItems_vehiclePricing_Dashboard, parent: 'vehiclepricing'
          },
          //Stock Dashboard
          {
            pageName: 'stockDashboardSeparate', isSales: null, nameAbbreviated: 'Stock Dashboard',
            name: 'Stock Dashboard', icon: 'fa-solid fa-chart-scatter', link: '/stockDashboard', isActive: false, visible: env.menuItems_vehiclePricing_Dashboard, parent: 'vehiclepricing'
          },
        ],
        expanded: null,
      },

      {
        group: 'vehiclepricingReports',
        name: 'Reports',
        subItems: [

          //Home
          // {
          //   pageName: null, isSales: null,   nameAbbreviated: 'Vehicle Valuation',
          //   name: 'Home', icon: 'fa fa-house', link: '/home', isActive: false, visible: env.menuItems_vehiclePricing_VehicleValuation, parent: 'vehiclepricing'
          // },

          //Stock Quick Search
          {
            pageName: 'advertSimpleListingSeparate', isSales: null, nameAbbreviated: 'Stock Quick Search',
            name: 'Stock Quick Search', icon: 'fa fa-magnifying-glass', link: '/advertSimpleListing', isActive: false, visible: env.menuItems_vehiclePricing_StockQuickSearch && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
          //Stock Reports
          {
            pageName: 'stockReportsSeparate', isSales: null, nameAbbreviated: 'Stock Report',
            name: 'Stock Report', icon: 'fas fa-car fa-fw', link: '/stockReports', isActive: false, visible: env.menuItems_vehiclePricing_StockReport, parent: 'vehiclepricing'
          },
          //Leaving Vehicle Trends
          {
            pageName: 'leavingVehicleTrendsSeparate', isSales: null, nameAbbreviated: 'Leaving V. Trends',
            name: 'Leaving Vehicle Trends', icon: 'fas fa-arrow-trend-up', link: '/leavingVehicleTrends', isActive: false, visible: env.menuItems_vehiclePricing_LeavingVehicleTrends && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
          //Leaving Vehicle Trends Over Time
          {
            pageName: 'leavingVehicleTrendsOverTime', isSales: null, nameAbbreviated: 'Leaving Trends Change',
            name: 'Leaving Trends Change', icon: 'fas fa-arrow-trend-up', link: '/leavingVehicleTrendsOverTime', isActive: false, visible: env.menuItems_vehiclePricing_LeavingVehicleTrends && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
          //Leaving Vehicle Detail
          {
            pageName: 'leavingVehicleDetailSeparate', isSales: null, nameAbbreviated: 'Leaving V. Detail',
            name: 'Leaving Vehicle Detail', icon: 'fas fa-magnifying-glass-chart', link: '/leavingVehicleDetail', isActive: false, visible: env.menuItems_vehiclePricing_LeavingVehicleDetail && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
        ],
        expanded: null,
      },
      {
        group: 'vehiclepricingSourcing',
        name: 'Sourcing Tools',
        subItems: [
          //Vehicle Valuation
          {
            pageName: 'bulkValuationSeparate', isSales: null, nameAbbreviated: 'Vehicle Valuation',
            name: 'Vehicle Valuation', icon: 'fa fa-pound-sign', link: '/bulkValuation', isActive: false, visible: env.menuItems_vehiclePricing_VehicleValuation, parent: 'vehiclepricing'
          },
          //Buying Opportunities
          {
            pageName: 'localBargainsSeparate', isSales: null, nameAbbreviated: 'Buying Ops.',
            name: 'Buying Opportunities', icon: 'fas fa-gift', link: '/localBargains', isActive: false, visible: env.menuItems_vehiclePricing_BuyingOpportunities, parent: 'vehiclepricing'
          },
          //Location Optimiser
          {
            pageName: 'locationOptimiserSeparate', isSales: null, nameAbbreviated: 'Location Optimiser',
            name: 'Location Optimiser', icon: 'fa fa-map-marker-alt', link: '/locationOptimiser', isActive: false, visible: env.menuItems_vehiclePricing_LocationOptimiser && perms.canReviewStockPrices, parent: 'vehiclepricing'
          }
        ],
        expanded: null,
      },
      {
        group: 'vehiclepricingPricing',
        name: 'Pricing Pages',
        subItems: [

          //Price Changes for today
          {
            pageName: 'todaysPricesSeparate', isSales: null, nameAbbreviated: "Price Changes",
            name: "Today's Price Changes", icon: 'fas fa-barcode-read fa-fw', link: '/todaysPrices', isActive: false,
            visible: env.menuItems_vehiclePricing_TodaysPriceChanges && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },
          //Opted Out Vehicles
          {
            pageName: 'optOutsSeparate', isSales: null, nameAbbreviated: 'Opted Out Vehicles',
            name: 'Opted Out Vehicles', icon: 'fas fa-bell-slash', link: '/optOuts', isActive: false, visible: env.menuItems_vehiclePricing_OptedOutVehicles && perms.canReviewStockPrices, parent: 'vehiclepricing'
          },

        ],
        expanded: null,
      },
    ]
  }




  private provideMaintenanceReports(env: SparkEnvironment, translated: TranslatedText): MenuSection[] {
    const perms = this.selections.user.permissions;

    return [
      {
        group: 'settings',
        name: 'Settings',
        subItems: [
          //Site Settings
          {
            pageName: 'siteSettings', isSales: null, nameAbbreviated: 'Site Settings',
            name: 'Site Settings', icon: 'fas fa-cog fa-fw', link: '/siteSettings', isActive: false, visible: env.menuItems_vehiclePricing_SiteSettings && perms.canReviewStockPrices, parent: 'settings'
          },
          //User Maintenance
          {
            pageName: 'userMaintenance', isSales: null, nameAbbreviated: translated.UserMaintenance,
            name: translated.UserMaintenance, icon: 'fas fa-user fa-fw', link: '/userMaintenance', isActive: false,
            visible: env.menuItems_userMaintenance && this.selections.user.RoleName == 'System Administrator' && perms.seeUserMaintenance, parent: 'settings'
          },

          // Usage Report
          {
            isSales: null, nameAbbreviated: translated.UsageReport_Title, name: translated.UsageReport_Title,
            icon: 'fa-solid fa-user', link: '/usageReport', pageName: 'UsageReport', isActive: false, visible: true, parent: 'settings'
          },
        ],
        expanded: null,
      }
    ]
  }



}