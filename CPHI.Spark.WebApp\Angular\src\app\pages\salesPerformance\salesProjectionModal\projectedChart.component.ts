import { Component, OnInit, ViewChild, ElementRef, Input, SimpleChanges } from "@angular/core";
//import {ICellRendererAngularComp} from "ag-grid-angular";
import { ChartService } from '../../../services/chart.service'
import { SelectionsService } from '../../../services/selections.service'
import { ConstantsService } from '../../../services/constants.service';

import { CphPipe } from '../../../cph.pipe';
import { SiteRegistrationPosition, SiteVM } from '../../../model/main.model';
import { SalesPerformanceService } from "../salesPerformance.service";
import { SalesPerformanceModalData } from "src/app/model/sales.model";

import { Chart, ChartConfiguration, ChartDataset, ChartDatasetCustomTypesPerDataset, ChartOptions, registerables } from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';

Chart.register(...registerables);
Chart.register(annotationPlugin);


@Component({
  selector: 'projectedChart',
  template:    `

    <div id="chartHolder">
      <canvas id="chartCanvas" #myChart>    </canvas>
    </div>


    `
  ,
  styles: [`
    #chartHolder{width:90%;margin:0.3em auto 0em auto;position:relative;height:40em;} 
   @media (min-width: 1366px) and (max-width: 1920px) {
    #chartHolder{height:28em}
  }
    `]
})
export class ProjectedChartComponent implements OnInit {
  @ViewChild('myChart', { static: true }) myChart: ElementRef;
  // @Input() public labels: number[];
  @Input() public chosenSite: SalesPerformanceModalData;
  @Input() public IsCurrentMonth: boolean;
  @Input() public MeasureName: string;


  labels: number[];
  percentageDone: number;
  percentageTarget: any;
  myChart1: any;
  dataSets: any[];


  constructor(
    public chart: ChartService,
    public selections: SelectionsService,
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public service: SalesPerformanceService
  ) {

  }

  params: Date;

  ngOnInit(): void {

    this.setLabels();

    setTimeout(() => {
      this.createChart();
    }, 500)
  }


  ngOnChanges(changes: SimpleChanges) {
    if (this.myChart1) {
      this.myChart1.destroy();
      this.createChart();
    }
  }


  ngOnDestroy(){
    if (this.myChart1) {
      this.myChart1.destroy();
    }
  }


  createChart() {

    this.myChart1 = (<HTMLCanvasElement>this.myChart.nativeElement);

    let chartContext = this.myChart1.getContext('2d');
    let chartColours = [];

    chartColours.push(this.constants.actualColour);
    chartColours.push(this.constants.backgroundColours[2]);

    //projection label
    let requirementLineColour = '#dc3545';
    let projectionMessage = '';
    if (this.chosenSite.projectedGap > 0) {
      projectionMessage = this.constants.translatedText.SalesProjectionChart_AheadMessage + ' ' + this.cphPipe.transform(this.chosenSite.projectedGap,'number', 0);
      requirementLineColour = '#28a745'
    } else if (this.chosenSite.projectedGap == 0) {
      projectionMessage = this.constants.translatedText.SalesProjectionChart_OnTargetMessage
      requirementLineColour = 'black'
    } else {
      projectionMessage = this.constants.translatedText.SalesProjectionChart_BehindMessage + ' ' + this.cphPipe.transform(this.chosenSite.projectedGap * -1, 'number',0);
    }


    let broughtInData = this.chosenSite.broughtInGraphData;
    let retailInMonthData =  this.chosenSite.totalDoneCumGraphData;
    let requirementData = this.chosenSite.requiredGraphData;
    let projectionData = this.chosenSite.projectionGraphData;

    this.dataSets =
      [
        {
          label: this.constants.translatedText.Common_BroughtIn,
          borderColor: 'hsla(184,93.3%,6%,0)',
          backgroundColor: "hsl(184,93.3%,12%)",
          data: broughtInData,
          fill: true,
          pointRadius: 0,

        },
        {
          label: this.constants.translatedText.SalesProjectionChart_RetailInMonth,
          borderColor: 'hsla(184,93.3%,6%,0)',
          borderWidth: 5,
          backgroundColor: "hsl(184,93.3%,24%)",
          data: retailInMonthData,
          pointRadius: 0,
          fill: true,
          lineTension:0
        },



      ];

    if (this.IsCurrentMonth) {
      this.dataSets.push(
        {
          label: this.constants.translatedText.Common_Requirement,
          borderColor: requirementLineColour,
          backgroundColor: "hsla(184,93.3%,36%,0)",
          data: requirementData,
          pointRadius: 0,
          borderDash: [4, 1],
          lineTension:0,
        },
      )
      this.dataSets.push(
        {
          label: this.constants.translatedText.Common_Projection,
          borderColor: 'hsla(184,93.3%,6%,1)',
          backgroundColor: "hsla(45,100%,50%,0)",
          data: projectionData,
          pointRadius: 0,
          lineTension:0,
        },
      )
    }

    console.log(this.dataSets, "this.dataSets!")

    let yAxisMax = Math.max(this.chosenSite.done, this.chosenSite.projection, this.chosenSite.target) * 1.2; //greater of projected or target

    let chartOptions: ChartOptions = {
      responsive: true,
      // title: {
      //   display: false,
      // },
      animation: {
        duration: 300,
      },
      maintainAspectRatio: false,
      hover: {
        mode: 'index'
      },
      plugins: {
        annotation: {
          annotations: {
            'Tgt': {
              type: 'line',
              drawTime: 'afterDraw',
              yMin: this.chosenSite.target,
              yMax: this.chosenSite.target,
              borderColor: 'hsl(45,100%,50%)',
              borderWidth: 2,
              borderDash: [2, 2],
              label: {
                //enabled: true,
                display: true, 
                content: this.constants.translatedText.Common_Target + ' ' + this.cphPipe.transform(this.chosenSite.target, 'number', 0),
                position: 'start',
                backgroundColor: 'hsl(45,100%,50%)',
                color: 'black',
                font: {
                  size: this.selections.chartFontsize
                },
                borderRadius: 5,
                yAdjust: 20,
                xAdjust: 5
                // cornerRadius: 5
              }
            },
            'Done': {
              type: 'line',
              drawTime: 'afterDraw',
              yMin: this.chosenSite.done,
              yMax: this.chosenSite.done,
              borderColor: 'hsl(45,100%,50%)',
              borderWidth: 2,
              borderDash: [2, 2],
              label: {
                //enabled: true,
                display: true, 
                content: this.constants.translatedText.Common_Done + ' ' + this.cphPipe.transform(this.chosenSite.done, 'number', 0),
                position: 'start',
                backgroundColor: 'hsl(45,100%,50%)',
                color: 'black',
                font: {
                  size: this.selections.chartFontsize
                },
                borderRadius: 5,
                xAdjust: 150,
                yAdjust: 20
                // xPadding: 10,
                // yPadding: 10,
                // cornerRadius: 5
              }
            }
          }
        }, datalabels: {
          display: false
        },
        tooltip: {
          enabled: true,
          mode: 'index',
          position: 'average',
          intersect: false,
          titleSpacing: 12,
          bodySpacing: 10,
          padding: {
            x: 12,
            y: 4,
          },
          titleMarginBottom: 5,
          cornerRadius: 5,
          caretSize: 0,
          backgroundColor: "rgba(0,0,0,0.8)",
          titleFont: {
            size: this.selections.chartFontsize * 12 / 12,
            //color: "white",
          },
          bodyFont: {
            size: this.selections.chartFontsize * 12 / 12,
          },
          callbacks: {
            label: function (context) {
              const label = context.dataset.label || '';
              const value = Number(context.parsed.y);
              if (label === 'Brought in') {
                return `${label}: ${value}`;
              } else if (label === 'Retail in month') {
                const datasetValue = Number(context.chart.data.datasets[0].data[context.dataIndex]);
                return `${label}: ${Math.round(value - datasetValue)}`;
              } else if (label === 'Requirement') {
                const datasetValue = Number(context.chart.data.datasets[1].data[context.dataIndex]);
                return `${label}: ${Math.round(value - datasetValue)}`;
              } else if (label === 'Projection') {
                return `${label}: ${Math.round(value)}`;
              }
              return `${label}: ${value}`;
            }
          }
        },
        legend: {
          display: true,
          reverse: true, // Note: It's 'reverse', not 'reversed'
          position: 'right',
          labels: {
            font: {
              size: this.selections.chartFontsize * 11 / 12 // Setting font size
            }
          }
        }
      },
      scales: {
        x: {
          min: 1,
          ticks: {
            autoSkip: true,
            maxTicksLimit: 14,
            maxRotation: 0,
            minRotation: 0,
            font: {
              size: this.selections.chartFontsize
            },
            callback: (value, index, values) => {
              value = Number(value) + 1; // There is no 0
              return this.constants.ordinalSuffix(value);
            }
          },
          grid: {
            //borderColor: 'white'
          },
          // Assuming it's a category scale, not time
          // If it's a time scale, this needs to be adjusted
        },
        y: {
          min: 0,
          max: yAxisMax,
          stacked: false,
          // gridLines: {
          //   zeroLineColor: 'white',
          // },
          ticks: {
            callback:
                    (value,index,values)=>{
                      return this.cphPipe.transform(value,'number',0);
                    }
          },
          // scaleLabel: {
          //   display: false,
          //   labelString: 'Value'
          // }
        }
      },
    }


    if (this.IsCurrentMonth) {

      const newAnnotation = {
        id: 'ProjectionGap',
        type: 'line',
        yMin: yAxisMax,
        yMax: yAxisMax,
        borderColor: 'hsl(45,100%,50%)',
        borderWidth: 0.0001,
        borderDash: [0, 0],
        label: {
          //enabled: true,
          display: true, 
          content: projectionMessage,
          position: 'end',
          backgroundColor: requirementLineColour,
          color: 'white',
          font: {
            size: this.selections.chartFontsize
          },
          // xPadding: 10,
          // yPadding: 10,
          cornerRadius: 5
        }
      };
      
      // Add the annotation to the chart options
      chartOptions.plugins.annotation.annotations['ProjectionGap'] = newAnnotation;

      console.log(chartOptions, "chartOptions here!")
    }


    let config = {
      type: 'line',
      data: {
        labels: this.labels,
        datasets: this.dataSets,
      },
      options: chartOptions
    }


    this.myChart1 = new Chart(chartContext, config);
  }

  // Array of integers marking number of days in month
  private setLabels()
  {
    let dateSelecting: Date = this.service.deliveryDate.startDate;
    let daysThisMonth: number = this.daysInMonth(dateSelecting.getMonth()+1, dateSelecting.getFullYear());
    this.labels = [daysThisMonth];

    for (let i = 0; i < daysThisMonth; i++) {
      this.labels[i] = i + 1;
    }

  }

  private daysInMonth (month: number, year: number) {
    return new Date(year, month, 0).getDate();
  }

}








