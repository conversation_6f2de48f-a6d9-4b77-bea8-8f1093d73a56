﻿using System;

using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using System.Threading.Tasks;
using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;
using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Caches
{
    public interface IServiceSummaryCache
    {
        Task RefreshCache(Model.DealerGroupName dealerGroup);
        Task<IEnumerable<DashboardGuage>> GetServiceSummaryRows(AftersalesTimePeriod timePeriod, DateTime? monthCommencing, string siteIds, int? userId, Model.DealerGroupName dealerGroup);
    }


    public class ServiceSummaryCache : IServiceSummaryCache
    {
        private readonly IServiceDataAccess serviceDataAccess;
        private readonly IUserService userService;
        IConfiguration configuration;

        public ServiceSummaryCache(IServiceDataAccess serviceDataAccess, IUserService userService, IConfiguration configuration)
        {
            this.serviceDataAccess = serviceDataAccess;
            this.userService = userService;
            this.configuration = configuration;
        }


        public async Task<IEnumerable<DashboardGuage>> GetServiceSummaryRows(AftersalesTimePeriod timePeriod, DateTime? monthCommencing, string siteIds, int? userId, Model.DealerGroupName dealerGroup)
        {

            bool includeNonLabour = dealerGroup != Model.DealerGroupName.Vindis;

            IEnumerable<DashboardGuage> values;

            if (dealerGroup == Model.DealerGroupName.RRGSpain)
            {
                string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
                string _connectionString = configuration.GetConnectionString(dgName);
                var siteDataAccess = new SiteDataAccess(_connectionString);
                IEnumerable<SiteVM> allSites = await siteDataAccess.GetAllSites(dealerGroup);
                siteIds = string.Join(',', allSites.Select(x => x.SiteId).ToArray());
                values = await serviceDataAccess.GetServiceSummaryForCacheSpain((DateTime) monthCommencing, siteIds, dealerGroup);
            }
            else
            {
                values = await serviceDataAccess.GetServiceSummaryForCache(timePeriod, includeNonLabour, dealerGroup);
            };


            return values;
        }



        public async Task RefreshCache(Model.DealerGroupName dealerGroup)
        {
            foreach (var item in MemoryCache.Default.AsEnumerable())
            {
                if (item.Key.StartsWith($"{dealerGroup.ToString()}|ServiceSummary"))
                {
                    MemoryCache.Default.Remove(item.Key);
                }
            }

            //fill latest and prior months also
            DateTime lastMonthStart = new DateTime(DateTime.Now.AddMonths(-1).Year, DateTime.Now.AddMonths(-1).Month, 1);
            await GetServiceSummaryRows(AftersalesTimePeriod.MTD, lastMonthStart, null, null, dealerGroup);
            await GetServiceSummaryRows(AftersalesTimePeriod.WTD, lastMonthStart, null, null, dealerGroup);
            await GetServiceSummaryRows(AftersalesTimePeriod.Yesterday, lastMonthStart, null, null, dealerGroup);
        }





    }







}

