﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.WebApp.DataAccess;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Service
{
    public interface IWorkingDaysService
    {
        Task<IEnumerable<DateTime>> GetPublicHolidays(DateTime start, DateTime end);
        void GetWeekendOpening(List<int> chosenSiteIds, int userId, out decimal saturdayOpening, out decimal sundayOpening);
        Task<decimal> GetWorkingDaysElapsedInMonth(DateTime monthStart, List<int> chosenSiteIds, int userId, Model.DealerGroupName dealerGroup);
        Task<decimal> GetWorkingDaysInMonth(DateTime monthStart, List<int> chosenSiteIds, int userId, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<DayAndWorkingDayValue>> GetDailyWorkingDaysForMonth(DateTime monthStart, List<int> siteIdsAsList, int userId, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<SiteWithOpening>> GetSiteWeekendOpening(int userId, bool showAllSites, DealerGroupName dealerGroup);
        Task<decimal> CountWorkingDaysBetweenAndIncluding(DateTime start, DateTime end, List<int> chosenSiteIds, int userId);
        Task<WeekToDateWorkingDays> DetermineWorkingDaysForCurrentWeek(int userId, DateTime today, List<int> siteIdsAsList);
        Task<IEnumerable<DayAndWorkingDayValue>> GetDailyWorkingDaysBetweenAndIncluding(DateTime monthStart, DateTime endDate, List<int> chosenSiteIds, int userId, bool showAllSites, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<SiteAndWorkingDaysCount>> GetSiteAndWorkingDayCountsBetweenAndIncluding(DateTime start, DateTime end, List<int> siteIdsList, int userId, bool showAllSites, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<SiteAndElapsedWorkDays>> GetElapsedDaysForSites(DateTime monthCommencing, AftersalesTimePeriod timePeriod, List<int> siteIds, int userId, bool showAllSites, Model.DealerGroupName dealerGroup);
    }

    public class WorkingDaysService : IWorkingDaysService
    {

        private readonly IWorkingDaysDataAccess workingDaysDataAccess;
        private readonly IUserService userService;
        private readonly IConfiguration configuration;


        public WorkingDaysService(IWorkingDaysDataAccess workingDaysDataAccess, IUserService userService, IConfiguration configuration)
        {
            this.workingDaysDataAccess = workingDaysDataAccess;
            this.userService = userService;
            this.configuration = configuration;
        }


        public async Task<IEnumerable<DateTime>> GetPublicHolidays(DateTime start, DateTime end)
        {
            return await workingDaysDataAccess.GetPublicHolidays(start, end, userService.GetUserDealerGroupName());
        }

        public void GetWeekendOpening(List<int> chosenSiteIds, int userId, out decimal saturdayOpening, out decimal sundayOpening)
        {
            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

            string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            IEnumerable<SiteWithOpening> siteWeekendOpening = siteDataAccess.GetSitesWithOpeningForUser(userId, showAllSites, dealerGroup).Result.Where(x => chosenSiteIds.Contains(x.SiteId));
            saturdayOpening = siteWeekendOpening.Any() ? siteWeekendOpening.Select(x => x.SatOpening).Sum() / siteWeekendOpening.Count() : 0;
            sundayOpening = siteWeekendOpening.Any() ? siteWeekendOpening.Select(x => x.SunOpening).Sum() / siteWeekendOpening.Count() : 0;
        }

        public async Task<IEnumerable<SiteWithOpening>> GetSiteWeekendOpening(int userId, bool showAllSites, DealerGroupName dealerGroup)
        {
            string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            return await siteDataAccess.GetSitesWithOpeningForUser(userId, showAllSites, userService.GetUserDealerGroupName());
        }


        public async Task<decimal> GetWorkingDaysElapsedInMonth(DateTime monthStart, List<int> chosenSiteIds, int userId, Model.DealerGroupName dealerGroup)
        {
            DateTime monthEnd = monthStart.AddMonths(1).AddDays(-1).Date;
            DateTime endDate = monthEnd < DateTime.Now.Date ? monthEnd : DateTime.Now.Date.AddDays(-1);
            return await CountWorkingDaysBetweenAndIncluding(monthStart, endDate, chosenSiteIds, userId);
        }

        public async Task<decimal> GetWorkingDaysInMonth(DateTime monthStart, List<int> chosenSiteIds, int userId, Model.DealerGroupName dealerGroup)
        {
            DateTime endDate = monthStart.AddMonths(1).AddDays(-1);
            return await CountWorkingDaysBetweenAndIncluding(monthStart, endDate.Date, chosenSiteIds, userId);
        }

        public async Task<decimal> CountWorkingDaysBetweenAndIncluding(DateTime start, DateTime end, List<int> chosenSiteIds, int userId)
        {
            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

            var totalDays = await workingDaysDataAccess.CountWorkingDaysBetweenAndIncluding(start, end, chosenSiteIds, userId, showAllSites, dealerGroup);
            return chosenSiteIds.Count > 0 ? totalDays / chosenSiteIds.Count : 0;
        }


        public async Task<IEnumerable<DayAndWorkingDayValue>> GetDailyWorkingDaysForMonth(DateTime monthStart, List<int> chosenSiteIds, int userId, Model.DealerGroupName dealerGroup)
        {
            bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

            DateTime endDate = monthStart.AddMonths(1).AddDays(-1).Date;
            return await GetDailyWorkingDaysBetweenAndIncluding(monthStart, endDate, chosenSiteIds, userId, showAllSites, dealerGroup);
        }


        public async Task<IEnumerable<SiteAndWorkingDaysCount>> GetSiteAndWorkingDayCountsBetweenAndIncluding(DateTime start, DateTime end, List<int> siteIdsList, int userId, bool showAllSites, Model.DealerGroupName dealerGroup)
        {
            return await workingDaysDataAccess.GetSiteAndWorkingDayCountsBetweenAndIncluding(start, end, siteIdsList, userId, showAllSites, dealerGroup);
        }



        public async Task<IEnumerable<DayAndWorkingDayValue>> GetDailyWorkingDaysBetweenAndIncluding(DateTime monthStart, DateTime endDate, List<int> chosenSiteIds, int userId, bool showAllSites, Model.DealerGroupName dealerGroup)
        {
            return await workingDaysDataAccess.GetWorkingDaysBetweenAndIncluding(monthStart, endDate, chosenSiteIds, userId, showAllSites, dealerGroup);
        }



        public async Task<IEnumerable<SiteAndElapsedWorkDays>> GetElapsedDaysForSites(DateTime monthCommencing, AftersalesTimePeriod timePeriod, List<int> siteIds, int userId, bool showAllSites, Model.DealerGroupName dealerGroup)
        {
            DateTime startPoint = monthCommencing; //is replaced below
            DateTime endPoint = DateTime.UtcNow; //is replaced below

            if (timePeriod == AftersalesTimePeriod.MTD)
            {
                startPoint = monthCommencing;
                endPoint = monthCommencing.AddMonths(1).AddDays(-1) < DateTime.Now.AddDays(-1) ? monthCommencing.AddMonths(1).AddDays(-1).Date : DateTime.Now.Date.AddDays(-1);
            }
            else if (timePeriod == AftersalesTimePeriod.WTD)
            {
                startPoint = StaticHelpersService.StartOfWeek(DateTime.Now, DayOfWeek.Monday);
                endPoint = DateTime.UtcNow.AddDays(-1) < startPoint.AddDays(6) ? DateTime.UtcNow.AddDays(-1) : startPoint.AddDays(6);
            }
            else if (timePeriod == AftersalesTimePeriod.Yesterday)
            {
                startPoint = DateTime.UtcNow.AddDays(-1);
                endPoint = startPoint;
            }

            IEnumerable<SiteAndWorkingDaysCount> workingDaysElapsedBySite = await GetSiteAndWorkingDayCountsBetweenAndIncluding(startPoint, endPoint, siteIds, userId, showAllSites, dealerGroup);
            DateTime monthEnd = monthCommencing.AddMonths(1).AddDays(-1);
            IEnumerable<SiteAndWorkingDaysCount> workingDaysInMonth = await GetSiteAndWorkingDayCountsBetweenAndIncluding(monthCommencing, monthEnd, siteIds, userId, showAllSites, dealerGroup);
            Dictionary<int, SiteAndWorkingDaysCount> workingDaysElapsedLookup = workingDaysElapsedBySite.ToDictionary(x => x.SiteId);

            List<SiteAndElapsedWorkDays> results = new List<SiteAndElapsedWorkDays>();
            foreach (var item in workingDaysInMonth)
            {
                decimal elapsed = 0;
                if (workingDaysElapsedLookup.ContainsKey(item.SiteId))
                {
                    elapsed = workingDaysElapsedLookup[item.SiteId].WorkingDays;
                }
                results.Add(new SiteAndElapsedWorkDays()
                {
                    SiteId = item.SiteId,
                    WorkingDaysElapsed = elapsed,
                    WorkingDaysInMonth = item.WorkingDays
                });
            }
            return results;
        }







        public async Task<WeekToDateWorkingDays> DetermineWorkingDaysForCurrentWeek(int userId, DateTime today, List<int> siteIdsAsList)
        {
            DateTime thisWeekStart = StaticHelpersService.StartOfWeek(DateTime.Now, DayOfWeek.Monday);
            DateTime thisWeekEnd = thisWeekStart.AddDays(6);
            DateTime thisMonthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            DateTime lastMonthEnd = thisMonthStart.AddDays(-1);
            DateTime mostRecentCompletedDay = DateTime.Now.Date.AddDays(-1);

            bool weekIsFullyInThisMonth = thisWeekStart.Month == DateTime.Now.Month;
            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            if (weekIsFullyInThisMonth)
            {
                return new WeekToDateWorkingDays()
                {
                    LastMonth = 0,
                    ThisMonth = await GetWorkingDaysInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup),
                    LastMonthThisWeek = 0,
                    ThisMonthThisWeek = await CountWorkingDaysBetweenAndIncluding(thisWeekStart, thisWeekEnd, siteIdsAsList, userId),
                    LastMonthThisWeekElapsed = 0,
                    ThisMonthThisWeekElapsed = await CountWorkingDaysBetweenAndIncluding(thisWeekStart, mostRecentCompletedDay, siteIdsAsList, userId),
                };
            }
            else
            {
                //week straddles months, have to work target out for each month
                DateTime lastMonthElapsedMeasureUpTo = lastMonthEnd > mostRecentCompletedDay ? mostRecentCompletedDay : lastMonthEnd;
                return new WeekToDateWorkingDays()
                {
                    LastMonth = await GetWorkingDaysInMonth(new DateTime(today.AddMonths(-1).Year, today.AddMonths(-1).Month, 1), siteIdsAsList, userId, dealerGroup),
                    ThisMonth = await GetWorkingDaysInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup),
                    LastMonthThisWeek = await CountWorkingDaysBetweenAndIncluding(thisWeekStart, lastMonthEnd, siteIdsAsList, userId),
                    ThisMonthThisWeek = await CountWorkingDaysBetweenAndIncluding(thisMonthStart, thisWeekEnd, siteIdsAsList, userId),
                    LastMonthThisWeekElapsed = await CountWorkingDaysBetweenAndIncluding(thisWeekStart, lastMonthElapsedMeasureUpTo, siteIdsAsList, userId),
                    ThisMonthThisWeekElapsed = await CountWorkingDaysBetweenAndIncluding(thisMonthStart, mostRecentCompletedDay, siteIdsAsList, userId),
                };

            }
        }






    }
}
