.buttonWithSlider {
  display: flex;
  align-items: center;
  justify-content: space-between;

  span.sliderText {
    margin-right: 0.5em;
  }

  &.btn-primary {

    background-color: transparent;
    height:1.7em;

    &:not(.btn-dark) {
      .slider {
        border: 1px solid var(--mainAppColour);
        background-color: var(--mainAppColour);
      }

      .switch:has(.toggle.active) {

        .slider {
          border: 1px solid var(--bs-success);
          background-color: var(--bs-success);
        }

      }
    }

    &.btn-dark {

      .slider {
        border: 1px solid var(--grey80);
        background-color: var(--mainAppColour);
      }

      .switch:has(.toggle.active) {

        .slider {
          background-color: var(--secondary);
        }
      }
    }
  }
}

.switch {
  position: relative;
  display: inline-block;
  height: 1.4em;
  width: 2.5em;
  margin-bottom: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--primaryDark);
  border: .1em solid white;
  border-radius: 1.8em;
  -webkit-transition: .4s;
  transition: .4s;
}

.toggle {
  position: absolute;
  content: "";
  -webkit-transition: .4s;
  transition: .4s;
  height: 1.1em;
  width: 1.1em;
  left: 0.16em;
  bottom: 0.16em;
  border-radius: 50%;
  background-color: white !important;
  z-index: 1;
}

.toggle.active {
  -webkit-transform: translateX(1.1em);
  -ms-transform: translateX(1.1em);
  transform: translateX(1.1em);
}

.buttonWithSlider.active {
  .slider {
    border: .1em solid var(--buttonColourActive);
  }

  .toggle {
    background-color: var(--buttonColourActive) !important;
  }
}
