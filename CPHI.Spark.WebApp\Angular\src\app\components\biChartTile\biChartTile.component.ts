import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, ViewChild } from "@angular/core";
import { Grid<PERSON>pi, ValueGetterParams } from "ag-grid-community";
import { Subject, Subscription } from "rxjs";
import { CphPipe } from "src/app/cph.pipe";
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from "src/app/services/constants.service";
import { localeEs } from 'src/environments/locale.es.js';
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { VNTileTableRow } from "src/app/model/VNTileTableRow";
import { DashboardMeasure } from "src/app/model/DashboardMeasure";
import { VNTileParams } from "src/app/model/VNTileParams";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { Router } from "@angular/router";
import { AutotraderService } from "src/app/services/autotrader.service";
import { takeUntil } from "rxjs/operators";

export enum BIChartTileDataType {
  day = "day",
  weekly = "weekly",
  month = "month",
  label = "label"
}

export type TileType =
  'Table'
  | 'VerticalBar'
  | 'HorizontalBar'
  | 'VerticalBarPercent'
  | 'DonutChart'
  | 'DonutChartFlex'
  | 'BigNumber'
  | 'SimpleLabels'

@Component({
  selector: 'biChartTile',
  templateUrl: './biChartTile.component.html',
  styleUrls: ['./biChartTile.component.scss']
})

export class BIChartTileComponent implements OnInit, OnDestroy {

  private destroy$ = new Subject<void>();
  tableRows: VNTileTableRow[]
  //chartRefreshEmitter
  chartRefreshEmitter: EventEmitter<boolean> = new EventEmitter<boolean>(); // Define at the class level

  // @Input() public isDateBased: boolean
  @Input() public dataType: BIChartTileDataType
  @Input() public fieldName: string
  @Input() public title: string
  @Input() public itemsToExclude: string[]
  @Input() public tileType: TileType
  @Input() public pageParams: VNTileParams;
  @Input() public labelWidth: number;
  @Input() public labelHeight: number;
  @Input() public isAutoTrader: boolean;
  @Input() public pageName: string;
  @Input() public supressFilterClick: boolean;
  @Input() public customSort: 'DaysListedBand' | 'Mileage' | 'Advance' | 'AgeBandAtEom' | 'SimpleBrand' | 'RegYear' | 'PPBand' | 'LastPriceBand' | 'PriceIndicator' | 'VsStrategy' | 'imagesCount' | 'HighToLow';

  isStockInsightPage: boolean;
  isLeavingVehiclePage: boolean;

  @ViewChild('contentHolder', { static: true }) contentHolder: ElementRef;


  public gridApi: GridApi;
  mainTableGridOptions: GridOptionsCph
  maxValue: number;

  autoTraderPerformanceRatingChartParams: any;

  constructor(
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public gridHelpers: AGGridMethodsService,
    public router: Router,
    public columnTypeService: ColumnTypesService,
    public autotraderService: AutotraderService
  ) {
  }

  ngOnInit(): void {
    //this.chartRefreshEmitter = new EventEmitter();

    if (this.router.url === '/stockDashboard') {
      this.isStockInsightPage = true;
      this.isLeavingVehiclePage = false
    } else if (this.router.url === '/soldDashboard') {
      this.isStockInsightPage = false;
      this.isLeavingVehiclePage = true;
    }


    this.buildTableRows();

    if (this.tileType == 'Table' || this.tileType === 'DonutChart') {
      // this.initGrid();
    }

 this.pageParams.updateThisTile
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        this.buildTableRows();
        this.updateGrid();
        this.chartRefreshEmitter.emit(true);
      });

    // this.subscription = this.pageParams.updateThisTile.subscribe(res => {
    //     this.buildTableRows();
    //     this.updateGrid();
    //     this.chartRefreshEmitter.emit(true)
    // })

  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.gridApi) {
      this.gridApi.destroy();
      this.gridApi = null;
    }
  }

  isHighlightsChosen() {
    let choice = this.userChoice();
    if (!choice) {
      console.error('failed finding ', this.fieldName)
    }
    return this.userChoice().ChosenValues.length > 0
  }

  clearHighlights() {
    let choice = this.pageParams.highlightChoices.find(x => x.FieldName === this.fieldName);
    choice.ChosenValues = [];
    this.pageParams.highlightChoiceHasBeenMade.emit();
  }

  isFilterChosen() {
    let choice = this.filterChoice();
    if (!choice) {
      console.error('failed finding ', this.fieldName)
    }
    return this.filterChoice().ChosenValues.length > 0
  }

  clearFilter() {
    let choice = this.pageParams.filterChoices.find(x => x.FieldName === this.fieldName);
    choice.ChosenValues = [];
    this.pageParams.filterChoiceHasBeenMade.emit();
  }

  buildTableRows() {

    // if (this.fieldName === 'ImagesBand') { debugger; }
    this.tableRows = this.pageParams.parentMethods.buildRows(this.fieldName, this.dataType, this.tileType)

    if (this.itemsToExclude?.length > 0) {
      this.tableRows = this.tableRows.filter(x => !this.itemsToExclude.includes(x.Label))
    }

    this.maxValue = this.tableRows.map(x => x.FilteredTotal).sort((a, b) => b - a)[0];

    if (this.title == this.constants.translatedText.Distrinet_OriginType) {
      this.tableRows.forEach(element => {
        if (element.Label == 'Book') {
          element.Label = this.constants.translatedText.Common_Book;
        }
      });

    }

    if (this.isAutoTrader && this.title === 'Performance Rating') {
      this.autoTraderPerformanceRatingChartParams = {
        none: this.tableRows[0].FilteredTotal,
        aboveAverage: this.tableRows[1].FilteredTotal,
        belowAverage: this.tableRows[2].FilteredTotal,
        excellent: this.tableRows[3].FilteredTotal,
        low: this.tableRows[4].FilteredTotal
      }
    }

    //if percent, now go over again and turn into percents
    let filteredTotal = this.constants.sum(this.tableRows.map(x => x.FilteredTotal));
    let highlightedTotal = this.constants.sum(this.tableRows.map(x => x.HighlightedTotal));
    if (this.tileType === 'VerticalBarPercent') {
      this.tableRows.map(row => {
        row.FilteredTotal = this.constants.div(row.FilteredTotal, filteredTotal)
        row.HighlightedTotal = this.constants.div(row.HighlightedTotal, highlightedTotal)
      })

      this.maxValue = 1

    }

    // Order by specific order for advances
    if (this.customSort == 'Advance') {
      const sortOrder = this.autotraderService.getSortOrderForAdvance();
      this.applyCustomSort(this.tableRows, sortOrder);
    }

    if (this.customSort == 'AgeBandAtEom') {
      const sortOrder = this.autotraderService.getSortOrderForAgeBandAtEom();
      this.applyCustomSort(this.tableRows, sortOrder);
    }

    if (this.customSort == 'SimpleBrand') {
      const sortOrder = this.autotraderService.getSortOrderForOnBrand(this.tableRows);
      this.applyCustomSort(this.tableRows, sortOrder);
    }

    if (this.customSort == 'DaysListedBand') {
      const sortOrder = AutotraderService.getSortOrderForDaysBand();
      this.applyCustomSort(this.tableRows, sortOrder);
    }

    if (this.customSort == 'Mileage') {
      const sortOrder = AutotraderService.getSortOrderForMileage();
      this.applyCustomSort(this.tableRows, sortOrder);
    }
    if (this.customSort == 'PPBand') {
      const sortOrder = AutotraderService.getSortOrderForPPBand();
      this.applyCustomSort(this.tableRows, sortOrder);
    }
    if (this.customSort == 'LastPriceBand') {
      const sortOrder = AutotraderService.getSortOrderForValueBand();
      this.applyCustomSort(this.tableRows, sortOrder);
    }
    if (this.customSort == 'RegYear') {
      this.tableRows = this.tableRows.sort(function (a, b) {
        return parseInt(b.Label) - parseInt(a.Label);
      });
    }
    if (this.customSort == 'PriceIndicator') {
      const sortOrder = AutotraderService.getSortOrderForPriceIndicator();
      this.applyCustomSort(this.tableRows, sortOrder);
    }
    if (this.customSort == 'VsStrategy') {
      const sortOrder = AutotraderService.getSortOrderForVsStrategyBanding();
      this.applyCustomSort(this.tableRows, sortOrder);
    }

    if (this.customSort == 'imagesCount') {
      const sortOrder = AutotraderService.getSortOrderForImagesBand();
      this.applyCustomSort(this.tableRows, sortOrder);
    }

    if (this.customSort == 'HighToLow') {
      this.tableRows = this.tableRows.sort((a, b) => {
        return b.FilteredTotal - a.FilteredTotal;
      })
    }
  }

  private applyCustomSort(tableRows: VNTileTableRow[], sortOrder: string[]) {
    var ordering = {}; // map for efficient lookup of sortIndex
    for (var i = 0; i < sortOrder.length; i++) {
      ordering[sortOrder[i]] = i;
    }
    tableRows = tableRows.sort(function (a, b) {
      return (ordering[a.Label] - ordering[b.Label]) || a.Label.localeCompare(b.Label);
    });
  }

  getLabelHeight() {
    return this.labelHeight ?? 30;
  }

  isItemSelected(item: string) {
    return this.userChoice().ChosenValues.length === 0 || this.userChoice().ChosenValues.includes(item)
  }

  userChoice(): DashboardMeasure {
    let choice = this.pageParams.highlightChoices.find(x => x.FieldName === this.fieldName);
    if (!choice) {
      console.error('error finding ', this.fieldName)
    }
    return choice;
  }

  filterChoice(): DashboardMeasure {
    let choice = this.pageParams.filterChoices.find(x => x.FieldName === this.fieldName);
    if (!choice) {
      console.error('error finding ', this.fieldName)
    }
    return choice;
  }


  onBarClick(row: VNTileTableRow) {
    if (this.supressFilterClick) {
      return;
    }
    this.pageParams.parentMethods.highlightRow(row, this.fieldName);
    this.pageParams.highlightChoiceHasBeenMade.emit();
  }

  onBigNumberClick() {
    if (this.supressFilterClick) {
      return;
    }
    const row: VNTileTableRow = this.tableRows[0];
    this.pageParams.parentMethods.highlightRow(row, this.fieldName);
    this.pageParams.highlightChoiceHasBeenMade.emit();
  }

  updateGrid() {
    if (!!this.gridApi) {
      setTimeout(() => {
        this.gridApi.setRowData(this.tableRows)
      }, 60)
    }
  }

  initGrid() {
    const gridScaleValue = this.contentHolder.nativeElement.clientWidth / 210

    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpers.getContextMenuItems(params),
      getLocaleText: (params) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      suppressPropertyNamesCheck: true,
      context: { thisComponent: this },
      getRowHeight: (params) => {
        return 25
      },
      rowClassRules: { 'highlighted': (params) => this.isGridRowHighlighted(params) },
      onRowClicked: (params) => {
        this.onBarClick(params.data)
      },
      //onAnimationQueueEmpty:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
      defaultColDef: {
        resizable: true,
        sortable: true,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpers.getFilterListItemHeight() },
        autoHeight: true,
        floatingFilter: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      //domLayout: 'autoHeight',
      rowData: this.tableRows,
      columnDefs: [],
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }

    this.mainTableGridOptions.columnDefs = [

      { headerName: "", colId: "Label", field: 'Label', width: 50 * gridScaleValue, type: "label", },
      //for normal
      {
        headerName: "Total",
        colId: "FilteredTotal",
        hide: this.tileType == 'DonutChart' || this.tileType == 'DonutChartFlex',
        field: 'FilteredTotal',
        width: 75 * gridScaleValue,
        type: "number",
      },
      {
        headerName: this.constants.translatedText.Common_Selected,
        colId: "HighlightedTotal",
        hide: this.tileType == 'DonutChart' || this.tileType == 'DonutChartFlex',
        field: 'HighlightedTotal',
        width: 75 * gridScaleValue,
        type: "number",
      },
      //for when in donut tile
      {
        headerName: "% total",
        colId: "Percentage",
        hide: this.tileType !== 'DonutChart' && this.tileType !== 'DonutChartFlex',
        valueGetter: (params) => this.getFilterPercentage(params.data),
        width: 50 * gridScaleValue,
        type: "percent",
      },
      {
        headerName: "% " + this.constants.translatedText.Common_Highlighted.toLowerCase(),
        colId: "Percentage",
        hide: this.tileType !== 'DonutChart' && this.tileType !== 'DonutChartFlex',
        valueGetter: (params) => this.getHighlightPercentage(params.data),
        width: 50 * gridScaleValue,
        type: "percent",
      },

    ]
  }

  getHighlightPercentage(row: VNTileTableRow): any {
    return this.constants.div(row.HighlightedTotal, this.constants.sum(this.tableRows.map(x => x.HighlightedTotal)))
  }

  getFilterPercentage(row: VNTileTableRow): any {
    return this.constants.div(row.FilteredTotal, this.constants.sum(this.tableRows.map(x => x.FilteredTotal)))
  }

  isGridRowHighlighted(params: any): boolean {
    let row: VNTileTableRow = params.data;
    if (!row) {
      return false;
    }
    return this.isHighlighted(row.Label);
  }

  isHighlighted(label: string): boolean {
    return this.userChoice().ChosenValues.includes(label);
  }

  getSimpleLabelClass(row: VNTileTableRow): string[] {
    let result: string[] = [];
    if (!this.supressFilterClick) {
      result.push('clickable')
    }
    if (this.isHighlighted(row.Label)) {
      result.push('highlighted')
    }
    return result;
  }


  horizontalBarHolderHeight(): string {
    return `calc(100% - ${this.labelHeight ?? 30}px)`;
  }


  onGridReady(params) {
    this.gridApi = params.api;
    if (this.gridApi.getDisplayedRowCount() == 0) {
      setTimeout(() => {
        this.updateGrid()
      }, 100)
    }
    this.gridApi.sizeColumnsToFit();
  }

  barHeight() {
    return this.constants.div((this.contentHolder.nativeElement.clientHeight - 150), this.tableRows.length);
  }

  barWidth() {
    return this.constants.div((this.contentHolder.nativeElement.clientWidth - 80), this.tableRows.length);
  }

  tableRowValues() {
    return this.tableRows.map(x => x.HighlightedTotal)
  }

  tableRowLabels() {
    return this.tableRows.map(x => x.Label)
  }

  trackByFunction(index: number) {
    return index;
  }

  truncateLabel(label: string): string {
    return label ? label.substring(0, 8) : null;
  }
}
