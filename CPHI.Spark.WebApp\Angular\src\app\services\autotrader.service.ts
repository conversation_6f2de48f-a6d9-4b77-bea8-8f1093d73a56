import {Injectable} from '@angular/core';
import {ICellRendererParams, IRowNode} from 'ag-grid-community';
import {VNTileTableRow} from '../model/VNTileTableRow';
import {StrategyFactorItemVM} from '../model/StrategyFactorItemVM';
import {StrategyFactorName} from '../model/StrategyFactorName';
import {StrategyVersionVM} from '../model/StrategyVersionVM';
import {StrategyFactorVM} from '../model/StrategyFactorVM';


@Injectable({
   providedIn: "root",
})
export class AutotraderService {
   StrategyFactorName = StrategyFactorName;

   //scanToOpen:Scan;

   constructor() {}

   provideLabelForPriceIndicator(value: string) {
      //if (!params.data) { return '' }
      if (value === "LOW" || value === "Low") return "Low";
      if (value === "GREAT" || value === "Great") return "Great";
      if (value === "GOOD" || value === "Good") return "Good";
      if (value === "FAIR" || value === "Fair") return "Fair";
      if (value === "HIGH" || value === "High") return "Higher";
      if (value === "NOANALYSIS" || value === "NoAnalysis")
         return "No Analysis";
      return value;
   }

   static get getFuelTypes() {
      return [
         "Diesel Plug-in Hybrid",
         "Petrol",
         "Bi Fuel",
         "Electric",
         "Unlisted",
         //"NULL",
         "Diesel",
         "Diesel Hybrid",
         "Petrol Plug-in Hybrid",
         "Petrol Hybrid",
      ];
   }

   getPerformanceRatingColour(value: string): string {
      let colour: string;

      if (value === "Low") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--prLower"); //what is this magic?!
      }

      if (value === "Below Avg") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--prFair");
      }

      if (value === "Above Avg") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--prGood");
      }

      if (value === "Excellent") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--prGreat");
      }

      return colour;
   }

   getStrategyBandColour(value: string) {
      let colour: string = "#D2D2D2";

      if (
         value === "V. Overpriced" ||
         value === "V. Underpriced" ||
         value === "VeryUnderPriced" ||
         value === "VeryOverPriced"
      ) {
         colour = getComputedStyle(document.documentElement).getPropertyValue(
            "--rrSub20Lozenge"
         );
      }

      if (
         value === "On Strategy Price" ||
         value === "On Strategy" ||
         value === "OnStrategyPrice"
      ) {
         colour = getComputedStyle(document.documentElement).getPropertyValue(
            "--rrSub80"
         );
      }

      if (
         value === "Overpriced" ||
         value === "Underpriced" ||
         value === "OverPriced" ||
         value === "UnderPriced"
      ) {
         colour = getComputedStyle(document.documentElement).getPropertyValue(
            "--rrSub60"
         );
      }

      return colour;
   }

   getRetailRatingBandColour(value: string) {
      let colour: string;

      if (value === "<20") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--rrSub20Lozenge");
      }

      if (value === "<40") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--rrSub40Lozenge");
      }

      if (value === "<60") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--rrSub60");
      }

      if (value === "<80") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--rrSub80");
      }

      if (value === "80+") {
         colour = getComputedStyle(document.documentElement).getPropertyValue("--rr80Plus");
      }

      return colour;
   }

   getSortOrderForAdvance(): string[] {
      return [
         "PURG",
         "ARA",
         "TCON",
         "CI",
         "EXPE",
         "MADC",
         "FAB",
         "FICE",
         "M",
         "M+1",
         "M+2",
         "AVAF",
         "NAF",
      ];
   }
   getSortOrderForAgeBandAtEom(): string[] {
      return ["0-30", "30-60", "60-90", "90-180", "180+"];
   }

   static getSortOrderForRetailRatingBand(): string[] {
      return ["<20", "<40", "<60", "<80", "80+"];
   }

   static getSortOrderForRetailRating10sBand(): string[] {
      return ["<10", "<20", "<30", "<40", "<50", "<60", "<70", "<80", "<90", "90+"];
   }

   public static sortStrategyFactorItemsForVersions(res: StrategyVersionVM[]) {
      res.map((x) => {
         AutotraderService.sortStrategyFactorItems(x);
      });
   }

   public static sortStrategyFactorItems(x: StrategyVersionVM) {
      x.StrategyFactors.map((factor) => {
         if (factor.Name === StrategyFactorName.DaysListedBand) {
            const order = AutotraderService.getSortOrderForDaysBand();
            this.sortRatings(factor, order);
         } else if (factor.Name === StrategyFactorName.RetailRatingBand) {
            const order =
               AutotraderService.getSortOrderForRetailRatingBand().reverse();
            this.sortRatings(factor, order);
         } else if (factor.Name === StrategyFactorName.RetailRating10sBand) {
            const order =
               AutotraderService.getSortOrderForRetailRating10sBand().reverse();
            this.sortRatings(factor, order);
         } else if (factor.Name === StrategyFactorName.OnBrandCheck) {
            const order = AutotraderService.getSortOrderForOnBrand();
            this.sortRatings(factor, order);
         } else if (factor.Name === StrategyFactorName.ValueBand) {
            const order = AutotraderService.getSortOrderForValueBand();
            this.sortRatings(factor, order);
         } else if (factor.Name === StrategyFactorName.RegYear) {
            this.sortRegYearItems(factor);
         } else if (factor.Name === StrategyFactorName.LiveMarketCondition) {
            this.sortLiveMarketConditionItems(factor);
         }
      });
   }

   private static sortRatings(factor: StrategyFactorVM, order: string[]) {
      factor.StrategyFactorItems = factor.StrategyFactorItems.sort((itemA, itemB) => {
         // Getting the index of the labels in the daysListedOrder array
         const indexA = order.indexOf(itemA.Label);
         const indexB = order.indexOf(itemB.Label);

         // Sorting based on the index
         return indexA - indexB;
      });
   }

   private static sortRegYearItems(factor: StrategyFactorVM) {
      factor.StrategyFactorItems = factor.StrategyFactorItems.sort((itemA, itemB) => {
         // Handle <2010 case - it should come first
         if (itemA.Label === '<2010') return -1;
         if (itemB.Label === '<2010') return 1;

         // For numeric years, sort in ascending order (older years first)
         const yearA = parseInt(itemA.Label);
         const yearB = parseInt(itemB.Label);

         return yearA - yearB;
      });
   }

   private static sortLiveMarketConditionItems(factor: StrategyFactorVM) {
      factor.StrategyFactorItems = factor.StrategyFactorItems.sort((itemA, itemB) => {
         // Sort by numeric value (ascending order from -200 to +200)
         const valueA = parseInt(itemA.Label);
         const valueB = parseInt(itemB.Label);

         return valueA - valueB;
      });
   }

   static getSortOrderForOnBrand(): string[] {
      return ["OnBrand", "OffBrand"];
   }

   getSortOrderForOnBrand(tableRows: VNTileTableRow[]): string[] {
      const sortOrder = tableRows
         .filter((tr) => tr.Label != "Non Franchise")
         .map((t) => t.Label)
         .sort();
      sortOrder.push("Non Franchise");
      return sortOrder;
   }

   public static factorExplanation(factor: StrategyFactorVM) {
      if (!factor?.Name) {
         return "";
      }
      if (factor.Name == StrategyFactorName.RetailRatingBand) {
         return `This adjustment layer allows you to specify an impact based upon a vehicle's Daily Retail Rating band, split into bandings of <20, <40, <60,<80 and 80+.  `;
      }
      if (factor.Name == StrategyFactorName.DaysListedBand) {
         return `This adjustment layer allows you to specify an impact based upon a vehicle's number of Days Listed band, split into bandings of <20 days, <40 days, <60 days and 60+ days.  `;
      }
      if (factor.Name == StrategyFactorName.DaysInStockBand) {
         return `This adjustment layer allows you to specify an impact based upon a vehicle's number of days in-stock band, split into bandings of <20 days, <40 days, <60 days and 60+ days.  `;
      }
      if (factor.Name == StrategyFactorName.DaysListed) {
         return `This adjustment layer allows you to specify an impact based upon a vehicle's number of Days Listed, enabling you to pick the banding definitions.  Ensure the final band ends in 999, for example 60-999`;
      }
      if (factor.Name == StrategyFactorName.RetailRating) {
         return `This adjustment layer allows you to specify an impact based upon a vehicle's Retail Rating (1-100), using an 'up to and including' basis.`;
      }
      if (factor.Name == StrategyFactorName.DaysInStock) {
         return `This adjustment layer allows you to specify an impact based upon a vehicle's number of days in-stock, enabling you to pick the banding definitions.  Ensure the final band ends in 999, for example 60-999`;
      }
      if (factor.Name == StrategyFactorName.OnBrandCheck) {
         return `This adjustment layer allows you to specify a different impact on the strategy price based upon whether the vehicle is on-brand for the site it is at.  `;
      }
      if (factor.Name == StrategyFactorName.RetailerName) {
         return `This adjustment layer allows you to adjust the strategy price impact based on the retailer name.  `;
      }
      if (factor.Name == StrategyFactorName.Brand) {
         return `This adjustment layer allows you to adjust the strategy price impact based on the vehicle make.  `;
      }
      if (factor.Name == StrategyFactorName.MatchCheapestCompetitor) {
         return `This adjustment layer allows you to match to the cheapest competitor vehicle within a chosen radius, on a price position % basis.  This includes the option to pick whether you wish to be cheapest, 2nd cheapest etc.  For plate steps, a value of 1 would mean Spark will include 71 and 72 plate matches when searching for competitors on a 22 plate vehicle.`;
      }
      if (factor.Name == StrategyFactorName.DaysToSell) {
         return `This adjustment layer allows you to choose a number of days to sell a vehicle.   Spark will use the AutoTrader api to calculate a price to achieve this.   For certain makes, this can lead to aggressive pricing.`;
      }
      if (factor.Name == StrategyFactorName.ValuationChangeUntilSell) {
         return `This adjustment layer reflect the change in valuation between now and the days to sell.   Spark will use the AutoTrader api to calculate a price to achieve this.   For certain makes, this can lead to aggressive pricing.`;
      }
      if (factor.Name == StrategyFactorName.MinimumProfit) {
         return `This adjustment layer allows you to specify a minimum profit that a vehicle needs to make.   Spark will use your DMS cost information to increase the selling price to reach the level needed to generate this profit, if it is below this.`;
      }
      if (factor.Name == StrategyFactorName.MinimumPricePosition) {
         return `This adjustment layer allows you to specify a minimum price position percentage relative to the valuation. If the calculated strategy price would result in a price position below this threshold, Spark will increase the price to achieve the minimum price position.`;
      }
      if (factor.Name == StrategyFactorName.RoundToNearest) {
         return `This adjustment layer allows you to specify an amount that Spark should round the strategy price up to achieve.  For example £50 would round a strategy price of £17,834 to £17,850.`;
      }
      if (factor.Name == StrategyFactorName.RoundToPriceBreak) {
         return `This adjustment layer allows you to request that Spark increases or reduces the calculated strategy price in order to hit one of the AutoTrader search breaks e.g. £10,000.  Set the threshold within which you'd like Spark to adjust the price.`;
      }
      if (factor.Name == StrategyFactorName.RR_DL_Matrix) {
         return `This adjustment layer allows you to build a custom matrix based on the Retail Rating and Days Listed.   Each Retail Rating and Days Listed banding works on an 'up to and including' basis.`;
      }
      if (factor.Name == StrategyFactorName.RR_DS_Matrix) {
         return `This adjustment layer allows you to build a custom matrix based on the Retail Rating and Days In-Stock.   Each Retail Rating and Days Instock banding works on an 'up to and including' basis.`;
      }
      if (factor.Name == StrategyFactorName.RR_DB_Matrix) {
         return `This adjustment layer allows you to build a custom matrix based on the Retail Rating and Days Booked-in.   Each Retail Rating and Days Booked-in banding works on an 'up to and including' basis.`;
      }
      if (factor.Name == StrategyFactorName.DTS_DL_Matrix) {
         return `This adjustment layer allows you to build a custom matrix based on the Days To Sell and Days Listed.   Each Days To Sell and Days Listed banding works on an 'up to and including' basis.`;
      }
      if (factor.Name == StrategyFactorName.SpecificColour) {
         return `This adjustment layer allows you to add an adjustment layer based on the manufacturer colour name of the vehicle.`;
      }
      if (factor.Name == StrategyFactorName.AgeAndOwners) {
         return `This adjustment layer allows you to add an adjustment layer based on the vehicle age band and number of previous owners, for example increasing the price of a 1 owner vehicle.`;
      }
      if (factor.Name == StrategyFactorName.AchieveMarketPositionScore) {
         return `This adjustment layer allows you to achieve a certain market position score within a chosen radius, on a price position % basis.  For example if there are 50 competitors and you choose to achieve 90 this strategy layer will impact the strategy price down to ensure you are the 5th cheapest seller.  For plate steps, a value of 1 would mean Spark will include 71 and 72 plate matches when searching for competitors on a 22 plate vehicle.`;
      }
      if (factor.Name == StrategyFactorName.WholesaleAdjustment) {
         return `This adjustment layer allows you to adjust the strategy price by a percentage and/or amount. For example entering 94% and £700 the strategy price we will be multiplied by 94% and add £700.`;
      }
      if (factor.Name == StrategyFactorName.RetailRating10sBand) {
         return `This adjustment layer allows you to specify an impact based upon a vehicle's Daily Retail Rating band, split into bandings of 10.  `;
      }
      if (factor.Name == StrategyFactorName.MakeFuelType) {
         return `This adjustment layer allows you to adjust the strategy price build for specific fuel types and brands. `;
      }
      if (factor.Name == StrategyFactorName.MakeAgeBand) {
         return `This adjustment layer allows you to adjust the strategy price build for specific vehicle age bands and brands. `;
      }
      if (factor.Name == StrategyFactorName.ModelName) {
         return `This adjustment layer allows you to add an adjustment layer based on the model name of the vehicle.`;
      }
      if (factor.Name == StrategyFactorName.Mileage) {
         return `This adjustment layer allows you to add an adjustment layer based on the mileage of the vehicle.`;
      }
      if (factor.Name == StrategyFactorName.PerformanceRatingScore) {
         return `This adjustment layer allows you to specify an impact based upon a vehicle's Performance Rating Score.`;
      }
      if (factor.Name == StrategyFactorName.FuelType) {
         return `This adjustment layer allows you to adjust the strategy price impact based on the vehicle's fuel type (e.g., Electric, Diesel, Petrol, Hybrid).`;
      }
      if (factor.Name == StrategyFactorName.ValueBand) {
         return `This adjustment layer allows you to adjust the strategy price impact based on the vehicle's value band (e.g., £5k-£10k, £10k-£15k, £20k-£30k).`;
      }
      if (factor.Name == StrategyFactorName.RegYear) {
         return `This adjustment layer allows you to adjust the strategy price impact based on the vehicle's registration year (e.g., 2020, 2021, 2022).`;
      }
      if (factor.Name == StrategyFactorName.LiveMarketCondition) {
         return `This adjustment layer allows you to adjust the strategy price impact based on the live market condition percentage  It works on an up to and including basis.`;
      }
   }


   static getSortOrderForMileage(): string[] {
      return [
         "<100",
         "<500",
         "<1,000",
         "<5,000",
         "<10,000",
         "<20,000",
         "<30,000",
         "<40,000",
         "<50,000",
         "<60,000",
         "<70,000",
         "<80,000",
         "80,000+",
      ];
   }

   static getSortOrderForValueBand(): string[] {
      return [
         "£5k-£10k",
         "£10k-£15k",
         "£15k-£20k",
         "£20k-£30k",
         "£30k-£40k",
         "£40k-£50k",
         ">£50k",
      ];
   }
   static getSortOrderForDaysBand(): string[] {
      return ["0-20", "20-40", "40-60", "60+"];
   }

   static getSortOrderForMileageBand(): string[] {
      return [
         '<500',
         '<999,999',
      ];
   }

   static getSortOrderWhenListed(nodeA: IRowNode, nodeB: IRowNode) : number {
      return this.getSortOrderMonthYear(nodeA.data.ListedDate, nodeB.data.ListedDate);
   }

   static getSortOrderRemovedDate(nodeA: IRowNode, nodeB: IRowNode) : number {
      return this.getSortOrderMonthYear(nodeA.data.RemovedDate, nodeB.data.RemovedDate);
   }

   static getSortOrderMonthYear(dateA: string, dateB: string) : number {

      // Sorts dates by Year and Month

      const toYearMonth = (date: Date) =>
         `${date.getFullYear()}-${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}`;

      const ymA = toYearMonth(new Date(dateA));
      const ymB = toYearMonth(new Date(dateB));

      return ymA > ymB ? 1 : -1;
   }

   static getSortOrderForPriceIndicator(): string[] {
      return ["Low", "Great", "Good", "Fair", "High", "NoAnalysis"];
   }

   static getSortOrderForVsStrategyBanding(): string[] {
      return [
         "V. Overpriced",
         "VeryOverPriced",
         "Overpriced",
         "OverPriced",
         "On Strategy",
         "OnStrategyPrice",
         "Underpriced",
         "UnderPriced",
         "V. Underpriced",
         "VeryUnderPriced",
         "No Valuation",
         "NoValuation",
      ];
   }

   static getSortOrderForPerfRating(): string[] {
      return ["Low", "Below Avg", "Above Avg", "Excellent"];
   }

   static getSortOrderForImagesBand(): string[] {
      return [
         "48 images+",
         "<48 images",
         "<37 images",
         "<26 images",
         "14 images",
         "<14 images",
         "<4 images",
         "No images",
      ];
   }

   static getSortOrderForDaysToSellBanding(): string[] {
      return ["Any", "<10", "<20", "<30", "<40", "<50", "50+"];
   }

   static get getSortOrderForAgeBand(): string[] {
      return ["<1yrs", "1-3yrs", "3-5yrs", "5-10yrs", ">10yrs"];
   }

   static customSort(
      valueA: string,
      valueB: string,
      rangesOrder: string[]
   ): number {
      const indexA = rangesOrder.indexOf(valueA);
      const indexB = rangesOrder.indexOf(valueB);
      return indexA - indexB;
   }

   static getSortOrderForPPBand(): string[] {
      return ["<95%", "95%-100%", "100%-103%", "103%+"];
   }

   public static buildRetailRatingFactorItems() {
      const values = this.getSortOrderForRetailRatingBand();
      const items: StrategyFactorItemVM[] = [];
      values.forEach((val) => {
         items.push(new StrategyFactorItemVM(null, val, 100));
      });
      return items;
   }

   public static buildRetailRating10sFactorItems() {
      const values = this.getSortOrderForRetailRating10sBand();
      const items: StrategyFactorItemVM[] = [];
      values.forEach((val) => {
         items.push(new StrategyFactorItemVM(null, val, 100));
      });
      return items;
   }

   public static buildDaysListedBandFactorItems() {
      const values = this.getSortOrderForDaysBand();
      const items: StrategyFactorItemVM[] = [];
      values.forEach((val) => {
         items.push(new StrategyFactorItemVM(null, val, 100));
      });
      return items;
   }

   public static buildMileageBandFactorItems() {
      const values = this.getSortOrderForMileageBand();
      const items: StrategyFactorItemVM[] = [];

      values.forEach((val) => {
         items.push(new StrategyFactorItemVM(null, val, 100));
      });
      return items;
   }

   public static buildDaysInStockBandFactorItems() {
      const values = this.getSortOrderForDaysBand();
      const items: StrategyFactorItemVM[] = [];
      values.forEach(val => {
         items.push(new StrategyFactorItemVM(null, val, 100));
      });
      return items;
   }

   public static buildDaysFactorItems() {
      const items: StrategyFactorItemVM[] = [
         new StrategyFactorItemVM(null, "20", 101),
         new StrategyFactorItemVM(null, "40", 100),
         new StrategyFactorItemVM(null, "999", 99, null, true),
      ];
      return items;
   }

   public static buildPerformanceRatingItems(){
      const items: StrategyFactorItemVM[]=[
         new StrategyFactorItemVM(null,"0-25", 98),
         new StrategyFactorItemVM(null,"26-50", 99),
         new StrategyFactorItemVM(null,"51-75", 100),
         new StrategyFactorItemVM(null,"76-100", 101),
      ]
      return items;
   }

   public static buildRetailRatingItems() {
      const items: StrategyFactorItemVM[] = [
         new StrategyFactorItemVM(null, "20", 99),
         new StrategyFactorItemVM(null, "40", 100),
         new StrategyFactorItemVM(null, "60", 101),
         new StrategyFactorItemVM(null, "80", 102),
         new StrategyFactorItemVM(null, "100", 103, null, true),
      ];
      return items;
   }

   public static buildOnBrandCheckFactorItems() {
      const values = this.getSortOrderForOnBrand();
      const items: StrategyFactorItemVM[] = [];
      values.forEach((val) => {
         items.push(new StrategyFactorItemVM(null, val, 100));
      });
      return items;
   }

   public static getSortOrderForMonthYear(a, b) {
      return a > b;
   }
}
