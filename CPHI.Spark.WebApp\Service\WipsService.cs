﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.Service
{
    public interface IWipsService
    {
        Task<List<WipsSiteRow>> GetSiteSummary(bool asAtMonthEnd, int userId, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<WipDetailRow>> GetWipsDetailSummary(string siteIdsString, bool includeZeroWips, bool asAtMonthEnd, int userId, Model.DealerGroupName dealerGroup);
    }

    public class WipsService : IWipsService
    {
        private readonly IWipsDataAccess _wipsDataAccess;

        public WipsService(IWipsDataAccess wipsDataAccess)
        {
            _wipsDataAccess = wipsDataAccess;
        }

        public async Task<List<WipsSiteRow>> GetSiteSummary(bool asAtMonthEnd, int userId, Model.DealerGroupName dealerGroup)
        {
            IEnumerable<WipsSiteRow> siteRows = await _wipsDataAccess.GetSiteSummary(asAtMonthEnd,userId, dealerGroup);
            siteRows = siteRows.OrderBy(x => x.SortOrder);


            List<WipsSiteRow> results = new List<WipsSiteRow>();
            //add site rows
            results.AddRange(siteRows);
            
            //add region rows
            foreach (var region in siteRows.ToLookup(x=>x.RegionId))
            {
                results.Add(new WipsSiteRow()
                {
                    SiteId = 0,
                    IsSite = false,
                    IsRegion = true,
                    IsTotal = false,
                    RegionDescription = region.First().RegionDescription,
                    Label = region.First().RegionDescription,

                    ValueUnder1Day = region.Select(x => x.ValueUnder1Day).Sum(),
                    Value1To7Days = region.Select(x => x.Value1To7Days).Sum(),
                    Value8To20Days = region.Select(x => x.Value8To20Days).Sum(),
                    Value21To30Days = region.Select(x => x.Value21To30Days).Sum(),
                    Value31To60Days = region.Select(x => x.Value31To60Days).Sum(),
                    ValueOver60Days = region.Select(x => x.ValueOver60Days).Sum(),
                });
            }

            //add a total row
            results.Add(new WipsSiteRow()
            {
                SiteId = 0,
                IsSite = false,
                IsRegion = false,
                IsTotal = true,
                RegionDescription = "",
                Label = "Total",

                ValueUnder1Day = siteRows.Select(x => x.ValueUnder1Day).Sum(),
                Value1To7Days = siteRows.Select(x => x.Value1To7Days).Sum(),
                Value8To20Days = siteRows.Select(x => x.Value8To20Days).Sum(),
                Value21To30Days = siteRows.Select(x => x.Value21To30Days).Sum(),
                Value31To60Days = siteRows.Select(x => x.Value31To60Days).Sum(),
                ValueOver60Days = siteRows.Select(x => x.ValueOver60Days).Sum(),
            });

            return results;
        }


        public async Task<IEnumerable<WipDetailRow>> GetWipsDetailSummary(string siteIdsString, bool includeZeroWips, bool asAtMonthEnd, int userId, Model.DealerGroupName dealerGroup)
        {
            return await _wipsDataAccess.GetWipDetailRows(siteIdsString, includeZeroWips,asAtMonthEnd, userId, dealerGroup);
        }

    }
}
