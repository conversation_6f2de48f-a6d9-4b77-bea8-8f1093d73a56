import { EventEmitter, Injectable } from '@angular/core';
import { MultiSelectMonth } from 'src/app/components/datePickerMultiSelect/datePickerMultiSelect.component';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { Thresholds } from 'src/app/_cellRenderers/bar.component';
import { PerformanceTrendsInit } from './PerformanceTrendsInit';
import { PerformanceTrendsParams } from './PerformanceTrendsParams';
import { PerformanceTrendsMeasure } from './PerformanceTrendsMeasure';
import { PerformanceTrendsRow } from './PerformanceTrendsRow';
import { TopBottomHighlightRule } from 'src/app/model/TopBottomHighlightRule';


@Injectable({
  providedIn: 'root'
})

export class PerformanceTrendsService {
  topBottomHighlights:TopBottomHighlightRule[]=[]
  performanceTrends: PerformanceTrendsInit;

  vehicleTypeTypes: string[];
  orderTypeTypes: string[];

  selectedMeasureType: string;


  constructor(
    public getData: GetDataMethodsService,
    public selections: SelectionsService,
    public constants: ConstantsService
  ) { }

  initiatePerformanceTrends() {

    this.vehicleTypeTypes = this.constants.clone(this.constants.vehicleTypeTypes);
    this.orderTypeTypes = this.constants.clone(this.constants.orderTypeTypesNoTrade);
    
    if (!this.performanceTrends) {
      let today: Date = this.constants.appStartTime;
      let currentMonth: MultiSelectMonth = {
        startDate: this.constants.deductTimezoneOffset(new Date(today.getFullYear(), today.getMonth(), 1)),
        isSelected: true
      }
      

      this.performanceTrends = {
        // barThresholds: {
        //   good: 0.4,
        //   bad: 0.2
        // },
        //months: [currentMonth],
        peopleData: null,
        peopleDataChangedEmitter: new EventEmitter(),
        sitesData: null,
        sitesDataChangedEmitter: new EventEmitter(),
        siteIds: null,
        vehicleTypes: [
          { isSelected: true, label: 'New' },
          { isSelected: true, label: 'Used' },
          { isSelected: true, label: 'Demo' }
        ],
        orderTypes: [
          { isSelected: true, label: 'Retail' },
          { isSelected: false, label: 'Motability' },
          { isSelected: false, label: 'Fleet' },
          { isSelected: false, label: 'Trade' }
        ],

        measureTypes: [
          { isSelected: true, label: 'Orders Taken' },
          { isSelected: false, label: 'Deliveries' },
          { isSelected: false, label: 'Chassis Profit - Total'},
          { isSelected: false, label: 'Chassis Profit - Per unit' },
          { isSelected: false, label: 'Finance Profit - Total' },
          { isSelected: false, label: 'Finance Profit - Per unit' }, 
          { isSelected: false, label: 'Finance - Penetration' },
          { isSelected: false, label: 'Add-on Profit - Total'},
          { isSelected: false, label: 'Add-on Profit - Per unit'},
          { isSelected: false, label: 'Cosmetic Sales - Total'},
          { isSelected: false, label: 'Cosmetic Sales - Per unit'},
          { isSelected: false, label: 'Cosmetic Sales - Penetration'},
          { isSelected: false, label: 'Paint Protect Sales - Total'},
          { isSelected: false, label: 'Paint Protect Sales - Per unit'},
          { isSelected: false, label: 'Paint Protect Sales - Penetration'},
          { isSelected: false, label: 'GAP Sales - Total'},
          { isSelected: false, label: 'GAP Sales - Per unit'},
          { isSelected: false, label: 'GAP Sales - Penetration'},
          { isSelected: false, label: 'Service Plan Sales - Total'},
          { isSelected: false, label: 'Service Plan Sales - Per unit'},
          { isSelected: false, label: 'Service Plan Sales - Penetration'},
          { isSelected: false, label: 'Products Per Unit'},
          { isSelected: false, label: 'Average Days to Sale'},
          { isSelected: false, label: 'Average Days to Deliver'},
          { isSelected: false, label: 'Red Work - Identified'},
          { isSelected: false, label: 'Red Work - Sold'},
          { isSelected: false, label: 'Amber Work - Identified'},
          { isSelected: false, label: 'Amber Work - Sold'},
          { isSelected: false, label: 'Workshop CitNOW - Sent'},
          { isSelected: false, label: 'Workshop CitNOW - Viewed'},
          { isSelected: false, label: 'Labour - Hours Sold'},
          { isSelected: false, label: 'Labour - Sales'},
          { isSelected: false, label: 'Retail Recovery Rate'}
        ],
        salesManagerId: null
      }

      this.selectedMeasureType = this.performanceTrends.measureTypes.find(p => p.isSelected == true).label
    }
  }

  getSitesData() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.getData.getPerformingTrends(this.getParams()).subscribe((res: PerformanceTrendsRow[]) => {
      this.performanceTrends.sitesData = res;
    }, e => {
      console.error('Error retrieving performance trends sites data: ' + JSON.stringify(e));
    }, () => {
      this.performanceTrends.sitesDataChangedEmitter.emit(true);
      this.selections.triggerSpinner.next({ show: false });
    })
  }


  getPeopleData() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.getData.getPerformingTrendsForSite(this.getParams()).subscribe((res: PerformanceTrendsRow[]) => {
      this.performanceTrends.peopleData = res;
    }, e => {
      console.error('Error retrieving sales activity people data: ' + JSON.stringify(e));
    }, () => {
      this.performanceTrends.peopleDataChangedEmitter.emit(true);
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getParams(): PerformanceTrendsParams {

     //Get selected order types
     let selectedVehicleTypes: string = '';
     if (this.performanceTrends.vehicleTypes.filter(x => x.isSelected).length == 1) {
       const selectedVehicleLabel = this.performanceTrends.vehicleTypes.find(x => x.isSelected).label;
       selectedVehicleTypes = this.constants.VehicleTypes.find(o => o.Code == selectedVehicleLabel).SuperType.toString();
     } else if (this.performanceTrends.vehicleTypes.filter(x => x.isSelected).length > 1) {
       const selectedVehicleTypesLable = this.performanceTrends.vehicleTypes.filter(x => x.isSelected).map(v => v.label);
       selectedVehicleTypesLable.forEach(element => {
        selectedVehicleTypes += this.constants.VehicleTypes.find(o => o.Code == element).SuperType.toString();
        selectedVehicleTypes += ','
       });
     }

    //Get selected order types
    let selectedOrderTypes: string = '';
    if (this.performanceTrends.orderTypes.filter(x => x.isSelected).length == 1) {
      //selectedOrderTypeId = this.performanceTrends.orderTypes.find(x => x.isSelected).label;
      const selectedOrderLabel = this.performanceTrends.orderTypes.find(x => x.isSelected).label;
      selectedOrderTypes = this.constants.OrderTypes.find(o => o.Code == selectedOrderLabel).Type.toString();
    } else if (this.performanceTrends.orderTypes.filter(x => x.isSelected).length > 1) {
      const selectedOrderTypesLabel = this.performanceTrends.orderTypes.filter(x => x.isSelected).map(v => v.label);
      selectedOrderTypesLabel.forEach(element => {
        selectedOrderTypes += this.constants.OrderTypes.find(o => o.Code == element).Type.toString();
        selectedOrderTypes += ','
      });
    }

    //Get selected Measure
     let selectedMeasureType: string = '';
     let measureType: PerformanceTrendsMeasure;
     if (this.performanceTrends.measureTypes.filter(x => x.isSelected).length == 1) {
       selectedMeasureType = this.performanceTrends.measureTypes.find(x => x.isSelected).label;
       measureType = this.getMeasureType(selectedMeasureType);
     }
    //} else 
    // if (this.performanceTrends.measureTypes.filter(x => x.isSelected).length > 1) {
    //   selectedOrderTypes = '';
    //   console.log('only one selection is allowed')
    //   alert('only one selection is allowed');
    // }

    




    let params: PerformanceTrendsParams = {
      SiteIds: this.performanceTrends.siteIds ? this.performanceTrends.siteIds.toString() : null,
      VehicleTypes: selectedVehicleTypes,
      SalesManagerId: (this.performanceTrends.salesManagerId == null) ? '' : this.performanceTrends.salesManagerId.toString(),
      Measure : measureType,
      OrderTypes: selectedOrderTypes,
      ShowAllSites: true
    }

    return params;
  }


  getMeasureType(selectedMeasureType: string): PerformanceTrendsMeasure {
    if (selectedMeasureType == 'Orders Taken') return PerformanceTrendsMeasure.Order;
    if (selectedMeasureType == 'Deliveries') return PerformanceTrendsMeasure.Delivery;
    if (selectedMeasureType == 'Chassis Profit - Total') return PerformanceTrendsMeasure.ChassisProfit;
    if (selectedMeasureType == 'Chassis Profit - Per unit') return PerformanceTrendsMeasure.ChassisProfitPU;
    if (selectedMeasureType == 'Finance Profit - Total') return PerformanceTrendsMeasure.FinanceProfit;
    if (selectedMeasureType == 'Finance Profit - Per unit') return PerformanceTrendsMeasure.FinanceProfitPU;
    if (selectedMeasureType == 'Finance - Penetration') return PerformanceTrendsMeasure.FinancePT;
    if (selectedMeasureType == 'Add-on Profit - Total') return PerformanceTrendsMeasure.AddOnProfit;
    if (selectedMeasureType == 'Add-on Profit - Per unit') return PerformanceTrendsMeasure.AddOnProfitPU;
    if (selectedMeasureType == 'Cosmetic Sales - Total') return PerformanceTrendsMeasure.CosmeticSales;
    if (selectedMeasureType == 'Cosmetic Sales - Per unit') return PerformanceTrendsMeasure.CosmeticSalesPU;
    if (selectedMeasureType == 'Cosmetic Sales - Penetration') return PerformanceTrendsMeasure.CosmeticSalesPT;
    if (selectedMeasureType == 'Paint Protect Sales - Total') return PerformanceTrendsMeasure.PaintProtectionSales;
    if (selectedMeasureType == 'Paint Protect Sales - Per unit') return PerformanceTrendsMeasure.PaintProtectionSalesPU;
    if (selectedMeasureType == 'Paint Protect Sales - Penetration') return PerformanceTrendsMeasure.PaintProtectionSalesPT
    if (selectedMeasureType == 'GAP Sales - Total') return PerformanceTrendsMeasure.GapSales;
    if (selectedMeasureType == 'GAP Sales - Per unit') return PerformanceTrendsMeasure.GapSalesPU;
    if (selectedMeasureType == 'GAP Sales - Penetration') return PerformanceTrendsMeasure.GapSalesPT;
    if (selectedMeasureType == 'Service Plan Sales - Total') return PerformanceTrendsMeasure.ServicePlansSales;
    if (selectedMeasureType == 'Service Plan Sales - Per unit') return PerformanceTrendsMeasure.ServicePlansSalesPU;
    if (selectedMeasureType == 'Service Plan Sales - Penetration') return PerformanceTrendsMeasure.ServicePlansSalesPT;
    if (selectedMeasureType == 'Products Per Unit') return PerformanceTrendsMeasure.ProductsPerUnit;
    if (selectedMeasureType == 'Average Days to Sale') return PerformanceTrendsMeasure.AvgDaysToSales;
    if (selectedMeasureType == 'Average Days to Deliver') return PerformanceTrendsMeasure.AvgDaysToDeliver;

    if (selectedMeasureType == 'Red Work - Identified') return PerformanceTrendsMeasure.RedWorkId;
    if (selectedMeasureType == 'Red Work - Sold') return PerformanceTrendsMeasure.RedWorkSold;
    if (selectedMeasureType == 'Amber Work - Identified') return PerformanceTrendsMeasure.AmberWorkId;
    if (selectedMeasureType == 'Amber Work - Sold') return PerformanceTrendsMeasure.AmberWorkSold;
    if (selectedMeasureType == 'Workshop CitNOW - Sent') return PerformanceTrendsMeasure.CitNowSent;
    if (selectedMeasureType == 'Workshop CitNOW - Viewed') return PerformanceTrendsMeasure.CitNowViewed;
    if (selectedMeasureType == 'Labour - Hours Sold') return PerformanceTrendsMeasure.LabourHoursSold;
    if (selectedMeasureType == 'Labour - Sales') return PerformanceTrendsMeasure.LabourSales;
    if (selectedMeasureType == 'Retail Recovery Rate') return PerformanceTrendsMeasure.RetailRecoveryRate;
  }
}
