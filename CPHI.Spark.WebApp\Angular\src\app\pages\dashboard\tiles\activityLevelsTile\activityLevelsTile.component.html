<div class="tileHeader ">
  <div class="headerWords">
    <h4>{{constants.translatedText.Dashboard_ActivityLevels_Title}} {{service.activitesTileStartDate|cph:'shortDate':'0'}}
    </h4>
  </div>
</div>


<div id="changeWeek" class="buttonGroup">
  <button class="invisibleButton" (click)="changeWeek(-1)"><i class="fas fa-caret-circle-left"></i></button>
  <button class="invisibleButton" (click)="changeWeek(1)"><i class="fas fa-caret-circle-right"></i></button>
</div>

<!-- Main chart -->
<div id="chartHolder" [ngClass]="{ 'half-height': constants.environment.dashboard.showActivityOverdues }">
  <canvas id="chartCanvas" #myChart>
  </canvas>
</div>



<!-- Overdues -->

<div id="overdueTableHolder" *ngIf="constants.environment.dashboard.showActivityOverdues">
  <table class="cph" style="width: 100%;">
    <thead>
      <tr>
        <th colspan="2"> {{constants.translatedText.Dashboard_ActivityLevels_Overdues}}</th>
      </tr>
    </thead>
    <tbody>

      <tr *ngFor="let overdue of data.Overdues">
        <td>
          
          <span *ngIf="overdue.Ageing == 'Over1'">{{constants.translatedText.Dashboard_ActivityLevels_Over1Day}}</span>
          <span *ngIf="overdue.Ageing == 'Over7'">{{constants.translatedText.Dashboard_ActivityLevels_OverAWeek}}</span>
          <span *ngIf="overdue.Ageing == 'Over30'">{{constants.translatedText.Dashboard_ActivityLevels_Over30Days}}</span>
          <span *ngIf="overdue.Ageing == 'Over60'">{{constants.translatedText.Dashboard_ActivityLevels_Over60Days}}</span>
        
        </td>
        <td>{{overdue.Count|cph:'number':0}}</td>
      </tr>

      <tr class='total'>
        <td>{{constants.translatedText.Dashboard_ActivityLevels_TotalOverdue}}</td>
        <td>{{totalOverdues|cph:'number':0}}</td>
      </tr>

       
    </tbody>
  </table>

</div>