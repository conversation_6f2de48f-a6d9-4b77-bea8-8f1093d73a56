import { Component, OnInit } from '@angular/core';
import {  Column<PERSON><PERSON>, Grid<PERSON>pi, GridReadyEvent } from 'ag-grid-community';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { PredefinedImportTemplatesService } from '../predefinedImportTemplates.service';
import { BulkUploadService } from '../bulkUpload.service';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';

@Component({
  selector: 'app-bulkUploadTable',
  templateUrl: './bulkUploadTable.component.html',
  styleUrls: ['./bulkUploadTable.component.scss']
})
export class BulkUploadTableComponent implements OnInit {
  gridOptions: GridOptionsCph;
  components: { [p: string]: any; } = { agColumnHeader: CustomHeaderNew };

  constructor(
    public constantsService: ConstantsService,
    public columnTypeService: ColumnTypesService,
    public predefinedImportTemplatesService: PredefinedImportTemplatesService,
    private bulkUploadService: BulkUploadService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  ngOnInit(): void {
    this.setGridOptions();
  }

  setGridOptions() {
    this.gridOptions = {
      context: { thisComponent: this },
      rowData: this.predefinedImportTemplatesService.rowData,
      onGridReady: (params) => this.onGridReady(params),
      defaultColDef: {
        resizable: true,
        sortable: true,
        floatingFilter: true,
        filterParams: { applyButton: false, clearButton: true, applyMiniFilterWhileTyping: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true
      },
      columnTypes: { ...this.columnTypeService.provideColTypes([]) },
      columnDefs: this.predefinedImportTemplatesService.provideColumnDefs(this.bulkUploadService.chosenPredefinedCompany.Name),
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      }
    }
  }

  onGridReady(params: GridReadyEvent) {
    this.predefinedImportTemplatesService.gridApi = params.api;
    this.predefinedImportTemplatesService.columnApi = params.columnApi;
    this.predefinedImportTemplatesService.columnApi.autoSizeAllColumns();
  }
}
