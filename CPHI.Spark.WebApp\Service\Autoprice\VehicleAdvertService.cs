﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;

using CPHI.Spark.Repository;
using CPHI.Spark.WebApp.DataAccess;

using CPHI.Spark.Repository;
using CPHI.Spark.Model;

using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using System.Net.Http;
using CPHI.Spark.DataAccess;
using MoreLinq;
using CPHI.Spark.Model.ViewModels.AutoPricing.Taxonomy;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
   public interface IVehicleAdvertService
   {
      Task<int> AddNewComment(VehicleAdvertNewCommentParams parms);
      Task<IEnumerable<VehicleAdvertComment>> AddNewComments(VehicleAdvertNewCommentsParams parms);
      Task DeleteComment(int commentId);
      Task<List<VehicleAdComment>> GetCommentsForAdvertId(int advertId);
      Task<List<LocationOptimiserAdvert>> GetLocationOptimiserAdverts(DateTime effectiveDate, GetLocationOptimiserAdvertsParams parms);
      Task<StatsDashboard> GetStatsDashboard(GetStatsDashboardParams getVehicleAdvertWithRatingsParams, DealerGroupName dealerGroup);
      Task<List<StatsSiteDashboard>> GetStatsSitesDashboard(GetStatsDashboardParams getVehicleAdvertWithRatingsParams, DealerGroupName dealerGroup);
      Task<List<StockProfileItem>> GetStatsStockProfileItems(GetStatsStockProfileItemsParams parms);
      Task<VehicleAdvertDetail> GetVehicleAdvertDetails(int advertId, DateTime effectiveDate);
      Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertsWithRatings(GetVehicleAdvertsWithRatingsParams parms, DealerGroupName dealerGroup);
      //Task<IEnumerable<VehicleAdvertWithRatingOld>> GetVehicleAdvertsWithRatingsOld(GetVehicleAdvertsWithRatingsParamsOld parms);
      Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertWithRatings(GetVehicleAdvertsWithRatingsParams parms);
      Task UpdateComment(VehicleAdvertUpdatedCommentParams parms);
      Task<List<TaxonomyFacetAndChoices>> GetAdvertTaxonomies(DealerGroupName dealerGroup, int retailersiteId, string derivativeId);

   }

   public class VehicleAdvertService : IVehicleAdvertService
   {
      private readonly IUserService userService;
      private readonly IConfiguration configuration;
      private readonly string _connectionString;
      private readonly DealerGroupName dealerGroup;
      private readonly IAutoPriceCache autoPriceCache;
      private readonly HttpClient httpClient;
      private readonly IHttpClientFactory httpClientFactory;
      private readonly TaxonomyService _taxonomyService;

      public VehicleAdvertService(
         IUserService userService, 
         IConfiguration configuration, 
         IAutoPriceCache autoPriceCache, 
         IHttpClientFactory httpClientFactory)
      {
         this.userService = userService;
         this.configuration = configuration;

         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];


         dealerGroup = userService.GetUserDealerGroupName();
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         _connectionString = configuration.GetConnectionString(dgName);
         this.autoPriceCache = autoPriceCache;

         _taxonomyService = new TaxonomyService(httpClientFactory, apiKey, apiSecret, baseURL);
      }

      public async Task<int> AddNewComment(VehicleAdvertNewCommentParams parms)
      {
         parms.UserId = userService.GetUserId();
         var vehicleAdvertCommentsDataAccess = new VehicleAdvertCommentsDataAccess(_connectionString);
         return await vehicleAdvertCommentsDataAccess.AddNewComment(parms);
      }

      public async Task<IEnumerable<VehicleAdvertComment>> AddNewComments(VehicleAdvertNewCommentsParams parms)
      {
         parms.UserId = userService.GetUserId();
         var vehicleAdvertCommentsDataAccess = new VehicleAdvertCommentsDataAccess(_connectionString);
         return await vehicleAdvertCommentsDataAccess.AddNewComments(parms);
      }


      public async Task UpdateComment(VehicleAdvertUpdatedCommentParams parms)
      {
         parms.UserId = userService.GetUserId();
         var vehicleAdvertCommentsDataAccess = new VehicleAdvertCommentsDataAccess(_connectionString);
         await vehicleAdvertCommentsDataAccess.UpdateComment(parms);
      }

      public async Task DeleteComment(int commentId)
      {
         var vehicleAdvertCommentsDataAccess = new VehicleAdvertCommentsDataAccess(_connectionString);
         await vehicleAdvertCommentsDataAccess.DeleteComment(commentId, userService.GetUserId());
      }

      public async Task<List<VehicleAdComment>> GetCommentsForAdvertId(int advertId)
      {
         int userId = userService.GetUserId();
         var vehicleAdvertCommentsDataAccess = new VehicleAdvertCommentsDataAccess(_connectionString);
         return await vehicleAdvertCommentsDataAccess.GetCommentsForAdvertId(advertId, userId);
      }

      public async Task<StatsDashboard> GetStatsDashboard(GetStatsDashboardParams getVehicleAdvertWithRatingsParams, DealerGroupName dealerGroup)
      {
         //get adverts
         var effectiveDate = DateTime.Now;
         var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var retailerSites = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
         List<int> userSiteIds = userService.GetUserSiteIds().ToList();

         var exampleRetailerSite = default(RetailerSite);
         var lifecycleStatuses = new List<string>() { "DUE_IN", "FORECOURT", "IN STOCK NOT ON PORTAL", "SALE_IN_PROGRESS" };
         bool includeUnPublished = false;

         if (getVehicleAdvertWithRatingsParams.RetailerSiteIds.Count > 0)
         {
            exampleRetailerSite = retailerSites.First(x => x.Id == getVehicleAdvertWithRatingsParams.RetailerSiteIds.First());
            lifecycleStatuses = exampleRetailerSite.LifecycleStatusDefaults.Split(',').ToList() ?? new List<string>() { "DUE_IN", "FORECOURT", "IN STOCK NOT ON PORTAL", "SALE_IN_PROGRESS" };
            includeUnPublished = exampleRetailerSite.IncludeUnPublishedAds;
         }

         List<VehicleAdvertWithRating> adverts = (await autoPriceCache.GetAutoPriceAdverts(effectiveDate, dealerGroup, lifecycleStatuses)).ToList();
         adverts = adverts.Where(x => getVehicleAdvertWithRatingsParams.RetailerSiteIds.Contains(x.RetailerSiteId)).ToList();

         if (getVehicleAdvertWithRatingsParams.UseTestStrategy)
         {
            adverts = adverts.Where(x => x.TestStrategyPrice > 0).ToList();
         }

         GetVehicleAdvertsWithRatingsParams filterParms = new GetVehicleAdvertsWithRatingsParams()
         {
            Reg = null,
            Vin = null,
            RetailerSiteIds = string.Join(',', getVehicleAdvertWithRatingsParams.RetailerSiteIds),
            EffectiveDate = effectiveDate,
            UserEligibleSites = string.Join(',', userService.GetEligibleSitesIds()),
            IncludeNewVehicles = exampleRetailerSite?.ShowNewVehicles ?? false,
            IncludeUnPublishedAdverts = exampleRetailerSite?.IncludeUnPublishedAds ?? false,
            LifecycleStatuses = lifecycleStatuses,
            VehicleTypes = exampleRetailerSite?.DefaultVehicleTypes != null ? exampleRetailerSite.DefaultVehicleTypes.Split(',').ToList() : null,
         };
         adverts = AdvertFilteringService.FilterAdverts(adverts, filterParms, getVehicleAdvertWithRatingsParams.RetailerSiteIds, userSiteIds);

         //Build up result
         StatsDashboard result = new StatsDashboard(adverts, true, includeUnPublished, getVehicleAdvertWithRatingsParams.UseTestStrategy);

         // Get Usage
         var usageSummaryItems = await userService.GetUsageSummaryItems(dealerGroup);
         result.Usage.UserLoginsThisWeek = 0;
         result.Usage.UserLoginsLastWeek = 0;
         foreach (var item in usageSummaryItems)
         {
            result.Usage.UserLoginsThisWeek += item.ThisWeek;
            result.Usage.UserLoginsLastWeek += item.LastWeek;
         }

         return result;
      }

      public async Task<List<StatsSiteDashboard>> GetStatsSitesDashboard(GetStatsDashboardParams getVehicleAdvertWithRatingsParams, DealerGroupName dealerGroup)
      {
         //get adverts
         var effectiveDate = DateTime.Now;
         var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var retailerSites = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
         List<int> userSiteIds = userService.GetUserSiteIds().ToList();
         var exampleRetailerSite = retailerSites.First(x => x.Id == getVehicleAdvertWithRatingsParams.RetailerSiteIds.First());
         bool includeUnPublished = exampleRetailerSite.IncludeUnPublishedAds;


         var lifecycleStatuses = exampleRetailerSite.LifecycleStatusDefaults.Split(',').ToList() ?? new List<string>() { "DUE_IN", "FORECOURT", "IN STOCK NOT ON PORTAL", "SALE_IN_PROGRESS" };
         List<VehicleAdvertWithRating> adverts = (await autoPriceCache.GetAutoPriceAdverts(effectiveDate, dealerGroup, lifecycleStatuses)).ToList();
         adverts = adverts.Where(x => getVehicleAdvertWithRatingsParams.RetailerSiteIds.Contains(x.RetailerSiteId)).ToList();

         if (getVehicleAdvertWithRatingsParams.UseTestStrategy)
         {
            adverts = adverts.Where(x => x.TestStrategyPrice > 0).ToList();
         }

         GetVehicleAdvertsWithRatingsParams filterParms = new GetVehicleAdvertsWithRatingsParams()
         {
            Reg = null,
            Vin = null,
            RetailerSiteIds = string.Join(',', getVehicleAdvertWithRatingsParams.RetailerSiteIds),
            EffectiveDate = effectiveDate,
            UserEligibleSites = string.Join(',', userService.GetEligibleSitesIds()),
            IncludeNewVehicles = exampleRetailerSite.ShowNewVehicles,
            IncludeUnPublishedAdverts = exampleRetailerSite.IncludeUnPublishedAds,
            LifecycleStatuses = lifecycleStatuses,
            VehicleTypes = exampleRetailerSite.DefaultVehicleTypes != null ? exampleRetailerSite.DefaultVehicleTypes.Split(',').ToList() : null,
         };


         adverts = AdvertFilteringService.FilterAdverts(adverts, filterParms, getVehicleAdvertWithRatingsParams.RetailerSiteIds, userSiteIds);


         //Build up results
         Dictionary<string, StatsDashboard> results = new Dictionary<string, StatsDashboard>();
         foreach (var siteGrouping in adverts.ToLookup(x => x.RetailerSiteName))
         {
            StatsDashboard thisSiteStats = new StatsDashboard(siteGrouping.ToList(), true, includeUnPublished, getVehicleAdvertWithRatingsParams.UseTestStrategy);
            results.Add(siteGrouping.Key, thisSiteStats);
         }

         //get usage
         List<UsageSummaryItem> usageSummaryItems = await userService.GetUsageSummaryItems(dealerGroup);

         foreach (var rating in usageSummaryItems)
         {
            if (!results.ContainsKey(rating.RetailerSiteName)) continue;
            results[rating.RetailerSiteName].Usage.UserLoginsThisWeek = rating.ThisWeek;
            results[rating.RetailerSiteName].Usage.UserLoginsLastWeek = rating.LastWeek;
         }

         var toReturn = results.Select(x => new StatsSiteDashboard(x.Key, x.Value)).ToList();

         var totalStats = new StatsDashboard(adverts, true, includeUnPublished, getVehicleAdvertWithRatingsParams.UseTestStrategy);
         var totalDashboard = new StatsSiteDashboard("Total", totalStats);
         totalDashboard.Stats.Usage.UserLoginsThisWeek = usageSummaryItems.Select(x => x.ThisWeek).Sum();
         totalDashboard.Stats.Usage.UserLoginsLastWeek = usageSummaryItems.Select(x => x.LastWeek).Sum();


         toReturn.Add(totalDashboard);
         return toReturn;
      }


      public async Task<List<StockProfileItem>> GetStatsStockProfileItems(GetStatsStockProfileItemsParams parms)
      {
         //1. get data

         // leaving data
         var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
         var userId = userService.GetUserId();
         IEnumerable<LeavingVehicleModelItem> leavingItems = await leavingPricesDataAccess.GetLeavingVehicleModelItems(parms, userId);

         // ads
         var effectiveDate = DateTime.Now;
         var lifecycleStatuses = new List<string>() { "DUE_IN", "FORECOURT", "IN STOCK NOT ON PORTAL", "SALE_IN_PROGRESS" };

         List<VehicleAdvertWithRating> adverts = (await autoPriceCache.GetAutoPriceAdverts(effectiveDate, dealerGroup, lifecycleStatuses)).ToList();
         adverts = adverts.Where(x => parms.ChosenRetailerSiteIds.Contains(x.RetailerSiteId)).ToList();

         //2. build up results.    Create resulting VM.   has constructor that takes grouped list of adverts.   Then supplement this with leaving data information

         List<StockProfileItem> resultsBasedOnStock = adverts
                 .GroupBy(x => new StockProfileItemMatch(x))
                 .Select(grouping =>
                 {
                    var nonNullValuations = grouping.Where(v => (v.ValuationAdjRetail ?? v.ValuationAdjRetailExVat ?? v.ValuationMktAvRetail ?? v.ValuationMktAvRetailExVat).HasValue)
                                           .Select(v => (v.ValuationAdjRetail ?? v.ValuationAdjRetailExVat ?? v.ValuationMktAvRetail ?? v.ValuationMktAvRetailExVat).Value);

                    var withPricePosition = grouping.Where(x => x.PricePosition > 0);
                    CumulativeValue cumPricePosition = new CumulativeValue();
                    CumulativeValue cumDaysListed = new CumulativeValue();
                    CumulativeValue cumRetailRating = new CumulativeValue();
                    CumulativeValue cumPerfRating = new CumulativeValue();
                    CumulativeValue cumPricedProfit = new CumulativeValue();


                    var statsDaysListed = new StatsDaysListed();
                    var statsRetailRating = new StatsRetailRating();
                    var statsPerformanceRating = new StatsPerformanceRating();
                    var statsVsStrategyPrice = new StatsVsStrategyPrice();

                    foreach (var item in grouping)
                    {
                       if (item.PricePosition > 0)
                       {
                          cumPricePosition.Add(item.PricePosition);
                       }
                       cumDaysListed.Add(item.DaysListed);
                       if (item.RetailRating.HasValue && item.RetailRating > 0)
                       {
                          cumRetailRating.Add((decimal)item.RetailRating);
                       }
                       if (item.PerfRatingScore.HasValue && item.PerfRatingScore > 0)
                       {
                          cumPerfRating.Add((decimal)item.PerfRatingScore);
                       }
                       if (item.PricedProfit != null)
                       {
                          cumPricedProfit.Add((decimal)item.PricedProfit);
                       }
                       StatsDashboardService.TotUpDaysListed(statsDaysListed, item);
                       StatsDashboardService.TotUpRetailRating(statsRetailRating, item);
                       StatsDashboardService.TotUpPerformanceRating(statsPerformanceRating, item);
                       StatsDashboardService.TotUpVsStrategyPrice(statsVsStrategyPrice, item, false);
                    }

                    var daysListed = grouping.Sum(x => x.DaysListed);
                    return new StockProfileItem()
                    {
                       RetailerSiteId = grouping.Key.RetailerSiteId,
                       RetailerSiteName = grouping.Key.RetailerSiteName,
                       ModelClean = grouping.Key.ModelClean,
                       PriceBand = grouping.Key.PriceBand,
                       AgeBand = grouping.Key.AgeBand,
                       Drivetrain = grouping.Key.Drivetrain,
                       FuelType = grouping.Key.FuelType,
                       BodyType = grouping.Key.BodyType,

                       //metrics
                       StockLevel = grouping.Count(),
                       PricePosition = cumPricePosition.Average,
                       DaysListed = (int)Math.Round(cumDaysListed.Average, 0),
                       RetailRating = (int)Math.Round(cumRetailRating.Average, 0),
                       PerformanceRating = (int)Math.Round(cumPerfRating.Average, 0),
                       Profit = (int)Math.Round(cumPricedProfit.Average, 0),

                       //sub-objects
                       StatsDaysListed = statsDaysListed,
                       StatsRetailRating = statsRetailRating,
                       StatsPerformanceRating = statsPerformanceRating,
                       StatsVsStrategyPrice = statsVsStrategyPrice
                    };
                 })
                 .ToList();



         List<StockProfileItem> resultsBasedOnLeavingItems = leavingItems
                .GroupBy(x => new StockProfileItemMatch(x))
                .Select(grouping =>
                {
                   CumulativeValue cumPricePosition = new CumulativeValue();
                   CumulativeValue cumDaysListed = new CumulativeValue();
                   CumulativeValue cumProfit = new CumulativeValue();

                   foreach (var item in grouping)
                   {
                      //price position
                      if (item.Valuation != null && item.Valuation > 0 && item.SuppliedPrice > 0)
                      {
                         decimal pricePosition = (int)item.SuppliedPrice / (decimal)item.Valuation;
                         cumPricePosition.Add(pricePosition);
                      }
                      cumDaysListed.Add(item.DaysListed);

                      if (item.PricedProfit.HasValue)
                      {
                         cumProfit.Add(item.PricedProfit.Value);
                      }
                   }



                   return new StockProfileItem()
                   {
                      //dimensions
                      RetailerSiteId = grouping.Key.RetailerSiteId,
                      RetailerSiteName = grouping.Key.RetailerSiteName,
                      ModelClean = grouping.Key.ModelClean,
                      PriceBand = grouping.Key.PriceBand,
                      AgeBand = grouping.Key.AgeBand,
                      Drivetrain = grouping.Key.Drivetrain,
                      FuelType = grouping.Key.FuelType,
                      BodyType = grouping.Key.BodyType,

                      //metrics
                      MonthlySalesRate = grouping.Count(),
                      SoldPricePosition = cumPricePosition.Average,
                      SoldDaysListed = (int)Math.Ceiling(cumDaysListed.Average),
                      SoldProfit = (int)Math.Round(cumProfit.Average, 0)

                      //sub-objects - NONE
                   };
                })
                .ToList();


         //This is to effectively do a full outer join
         List<StockProfileItem> finalResults = new List<StockProfileItem>();


         //temp
         //resultsBasedOnLeavingItems = resultsBasedOnLeavingItems.Where(x=>x.RetailerSiteName == "Pentagon Sheffield" && x.ModelClean == "Astra").ToList();
         //resultsBasedOnStock = resultsBasedOnStock.Where(x=>x.RetailerSiteName == "Pentagon Sheffield" && x.ModelClean == "Astra").ToList();

         var stockResultsDict = resultsBasedOnStock.ToDictionary(x => new StockProfileItemMatch(x));
         foreach (var leavingItem in resultsBasedOnLeavingItems)
         {
            var key = new StockProfileItemMatch(leavingItem);
            StockProfileItem matchingStockItem = null;
            if (stockResultsDict.TryGetValue(key, out matchingStockItem))
            {
               //add leaving item details to the stock item match
               matchingStockItem.MonthlySalesRate = leavingItem.MonthlySalesRate;
               matchingStockItem.SoldPricePosition = leavingItem.SoldPricePosition;
               matchingStockItem.SoldDaysListed = leavingItem.SoldDaysListed;
               matchingStockItem.SoldProfit = leavingItem.SoldProfit;
               //matchedItem.StockLevel = item.StockLevel;

               if (matchingStockItem.StockLevel > 0)
               {
                  { }
               }

               finalResults.Add(matchingStockItem);
            }
            else
            {
               finalResults.Add(leavingItem);
            }
         }

         //also add in the stock items
         var leavingItemKeys = resultsBasedOnLeavingItems.ToDictionary(x => new StockProfileItemMatch(x));
         foreach (var stockItem in resultsBasedOnStock)
         {
            var itemKey = new StockProfileItemMatch(stockItem);
            if (!leavingItemKeys.ContainsKey(itemKey))
            {
               finalResults.Add(stockItem);
            }
         }


         var tst1 = resultsBasedOnStock.Where(x => x.StockLevel > 0).ToList();
         var tst = finalResults.Where(x => x.StockLevel > 0).ToList();
         return finalResults;
      }


      //public StatsDaysListed StatsDaysListed { get; set; }
      //public StatsRetailRating StatsRetailRating { get; set; }
      //public StatsPerformanceRating StatsPerformanceRating { get; set; }
      //public StatsVsStrategyPrice StatsVsStrategyPrice { get; set; }



      public async Task<VehicleAdvertDetail> GetVehicleAdvertDetails(int advertId, DateTime effectiveDate)
      {
         var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         return await (vehicleAdvertsDataAccess.GetVehicleAdvertDetails(advertId, effectiveDate, ConstantsCache.ProvideBandingsDictionary(dealerGroup), dealerGroup));
      }


      public async Task<List<LocationOptimiserAdvert>> GetLocationOptimiserAdverts(DateTime effectiveDate, GetLocationOptimiserAdvertsParams parms)
      {
         var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         var userId = userService.GetUserId();
         return await vehicleAdvertsDataAccess.GetLocationOptimiserAdverts(effectiveDate, parms.advertId, parms.includeNewVehicles, userId);
      }

      //Adverts

      public async Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertWithRatings(GetVehicleAdvertsWithRatingsParams parms)
      {
         parms.UserEligibleSites = string.Join(',', userService.GetEligibleSitesIds());
         List<int> userSiteIds = userService.GetUserSiteIds().ToList();
         List<int> userRetailerSiteIds = userService.GetUserRetailerSiteIds().ToList();
         //var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         VehicleAdvertsService vehicleAdvertsService = new VehicleAdvertsService(_connectionString);
         var adverts = await vehicleAdvertsService.GetVehicleAdvertsWithRatings(parms, ConstantsCache.ProvideBandingsDictionary(dealerGroup), dealerGroup,
             userRetailerSiteIds, userSiteIds, new List<string>() { "DUE_IN", "FORECOURT", "IN STOCK NOT ON PORTAL", "SALE_IN_PROGRESS" });
         var tst = adverts.FirstOrDefault(x => x.VehicleReg == "LG74NXP");
         return adverts.OrderBy(x => x.DaysListed);
      }








      public async Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertsWithRatings(GetVehicleAdvertsWithRatingsParams parms, Model.DealerGroupName dealerGroup)
      {
         List<VehicleAdvertWithRating> adverts = (await autoPriceCache.GetAutoPriceAdverts(parms.EffectiveDate, dealerGroup, parms.LifecycleStatuses, parms.UseTestStrategy)).ToList();
         // var tst = adverts.FirstOrDefault(x => x.AdId == 28809);
         List<int> siteIds = userService.GetUserSiteIds().ToList();
         List<int> retailerSiteIds = userService.GetUserRetailerSiteIds().ToList();
         adverts = AdvertFilteringService.FilterAdverts(adverts, parms, retailerSiteIds, siteIds);
         var tst = adverts.FirstOrDefault(x=>x.VehicleReg=="LG74NXP");
         return adverts;
      }


      public async Task<List<TaxonomyFacetAndChoices>> GetAdvertTaxonomies(DealerGroupName dealerGroup, int retailerSiteId, string derivativeId)
      {
         //var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         //var derivativeId = await vehicleAdvertsDataAccess.GetAdvertDerivativeId(dealerGroup, advertId);

         var taxonomies = await _taxonomyService.GetDerivativeTaxonomies(retailerSiteId, derivativeId);

         return taxonomies;
      }
   }
}