import { SparkEnvironment } from "src/app/services/environment.service";
import packagejson from '../../package.json';

export const environmentVindis: SparkEnvironment = {
  customer: 'Vindis',
  production: true,
  //webURL: 'https://vindisspark.cphi.co.uk',
  //backEndBaseURL: 'https://vindissparkapi.cphi.co.uk',
  version: packagejson.version,
  languagePickerOnLogin: false,
  franchisePicker: false,
  stockGroupPicker: false,
  lateCostPicker: false,
  orderTypePicker: true,
  ageingOptions: true,
  displayCurrency: 'GBP',
  displayCurrencySymbol: '£',
  fAndISummary_includeTargets: true,
  todayMap: {
    defaultPositionLat: 52.698926,
    defaultPositionLong: -1.046534,
    defaultZoom: 7,
  },
  // Better way for this?
  bookingsBar: {
    barStyle1: false,
    barStyle2: true,
  },

  dealDetails: {
    componentName: 'DealDetailsComponent',
    profitTableShowSale: false,
    profitTableShowCost: false,
    profitTableShowCommission: false,
    profitTableFinanceCommText: 'HP & Other Fin. Comm.',
    showDescription: false,
    showVehicleAge: false,
    showPaintProtection: true,
    paintProtectionText: 'Paint & Fabric',
    showPaintProtectionCost: false,
    showPaintProtectionSale: false,
    showPhysicalLocation: true,
    showIsDealClosed: true,
    showFinanceCo: false,
    showFinanceType: true,
    showWarrantyInfo: true,
    showRCIFinanceComm: false,
    showPCPFinanceComm: true,
    showStandardsCommission: false,
    showProPlusCommission: false,
    showSelectCommission: false,
    showGapInsurance: false,
    showTyreInsurance: false,
    showAlloyInsurance: false,
    showWheelGard: false,
    showServicePlan: false,
    showWarranty: true,
    showUnits: false,
    showDeliverySite: true,
    showAdditionalAddOnProfit: false,
    showCosmeticInsuranceSale: false,
    showCosmeticInsuranceCost: false,
    showCosmeticInsuranceCommission: false,
    showVATCost: true,
  },
  usedStockTable: {
    vindisFormatting: true,
    tactical: false,
    exManagementCount: false,
    exDemo: false
  },
  sideMenu: {
    oldStockPricing: false,
    pricingHome: false,
    dashboard: true,
    orderbook: true,
    fleetOrderbook: false,
    dealsDoneThisWeek: true,
    dealsForTheMonth: true,
    whiteboard: true,
    performanceLeague: true,
    performanceTrends: true,
    scratchCards: false,
    salesIncentive: false,
    supercup: false,
    supercup2: false,
    handoverDiary: true,
    distrinet: false,
    reportPortal: true,
    stockList: true,
    stockPricing: false,
    stockInsight: true,  //this is the blobs page
    leavingVehicles: false,
    pricingDashboard: false,
    siteDetailDashboard: false,
    applyStrategy: false,
    strategyBuilder: false,
    locationOptimiser: true,
    vehicleValuation: true,
    salesCommission: true,
    salesExecReview: true,
    localBargains: true,
    stockLanding: false,
    liveForecast: true,
    userMaintenance: true,
    autoPriceSiteSettings: true,

    summaryDashboard: false,
    stockReports: true,  //this is the main report
    bulkValuation: true,
    optOuts: false,
    todayPriceChanges: true,
    leavingVehicleDetail: false,
    leavingVehicleTrends: false,
  },
  fullSideMenu: {
    description: ' Vindis Group Ltd',
  },
  citNoww: {
    tileHeader: 'CitNOWs Viewed as a Proportion of Qualifying WIPs',
    moveNissanValuesToRenault: false,
    renaultRegions: false,
    vindisRegions: true,
    excludeAudi: true,
    pcOfSalesEnquiries: true,
    pcOfInvoicedExtWips: true,
    pcOfSalesEnquiriesText: 'CitNOW Summary - Videos Viewed as a % of Sales Enquiries - ',
    pcOfInvoicedExtWipsText: 'CitNOW Summary - Videos Viewed as a % of Invoiced External WIPs - ',
    showSimpleCitNowPersonDetail: true,
    showVideosViewed: true,
    eDynamixView: false
  },
  dealDetailModal:
  {
    showOtherProfit: true,
    showFinanceProfit: true,
    showAddOnProfit: true,
    showTotalProfit: true,
    showStockDetailButton: true,
    //  showStockWebsiteListingButton:false,
  },
  allGroups: ['R', 'O', 'N', 'Z', 'T', 'D'],
  allFamilyCodes: ['Franchise', 'Non-Franchise', 'Tyres', 'Oil', 'Exchange'],
  dashboard: {
    sections: [

      //Overview dashboard
      {
        sectionName: "Overview", translatedTextField: "Common_Overview", translatedTextValue: "", pageName: "dashboardOverview", enableSitesSelector: true, pages: [

          { pageName: "SalesPerformance", translatedTextField: "Dashboard_SalesPerformance_Title", translatedTextValue: "" },
          { pageName: "FinanceAddOns", translatedTextField: "Dashboard_FinanceAddons_Title", translatedTextValue: "" },
          { pageName: "StockReport", translatedTextField: "Dashboard_StockReport_Title", translatedTextValue: "" },
          { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
          { pageName: "GDPR", translatedTextField: "Dashboard_GDPRCapture", translatedTextValue: "" },
          { pageName: "Activities", translatedTextField: "Dashboard_Activities", translatedTextValue: "" },
          { pageName: "CitNow", translatedTextField: "Dashboard_CitNow_Title", translatedTextValue: "" },
          { pageName: "ImageRatios", translatedTextField: "Dashboard_ImageRatios_Title", translatedTextValue: "" }
        ]
      },


      //Sales dashboard
      {
        sectionName: "SalesVindis", translatedTextField: "Common_Sales", translatedTextValue: "", pageName: "dashboardSalesVindis", enableSitesSelector: false, pages: [
          { pageName: "SalesPerformance", translatedTextField: "Dashboard_SalesPerformance_Title", translatedTextValue: "" },
          { pageName: "FinanceAddOns", translatedTextField: "Dashboard_FinanceAddons_Title", translatedTextValue: "" },
          { pageName: "StockReport", translatedTextField: "Dashboard_StockReport_Title", translatedTextValue: "" },
          { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
          { pageName: "GDPR", translatedTextField: "Dashboard_GDPRCapture", translatedTextValue: "" },
          { pageName: "Activities", translatedTextField: "Dashboard_Activities", translatedTextValue: "" },
          { pageName: "CitNow", translatedTextField: "Dashboard_CitNow_Title", translatedTextValue: "" },
          { pageName: "ImageRatios", translatedTextField: "Dashboard_ImageRatios_Title", translatedTextValue: "" },
        ]
      },

      //Aftersales dashboard
      {
        sectionName: "Aftersales", translatedTextField: "Common_Aftersales", translatedTextValue: "", pageName: "dashboardAfterSalesVindis", enableSitesSelector: false, pages: [
          { pageName: "ServiceSales", translatedTextField: "Dashboard_ServiceSales_Title", translatedTextValue: "" },
          { pageName: "PartsSales", translatedTextField: "Dashboard_PartsSales_Title", translatedTextValue: "" },
          { pageName: "ServiceBookings", translatedTextField: "Dashboard_ServiceBookings_Title", translatedTextValue: "" },
          { pageName: "PartsStock", translatedTextField: "Dashboard_PartsStock_Title", translatedTextValue: "" },
          { pageName: "EVHC", translatedTextField: "Dashboard_Evhc_Title", translatedTextValue: "" },
          { pageName: "Debtors", translatedTextField: "Dashboard_Debtors_Title", translatedTextValue: "" },
          { pageName: "CitNow", translatedTextField: "Dashboard_CitNow_Title", translatedTextValue: "" },
          { pageName: "Wip", translatedTextField: "Dashboard_WipReport_Title", translatedTextValue: "" }
        ]
      },

      //Site compare dashboard
      {
        sectionName: "SiteCompare", translatedTextField: "SiteCompare", translatedTextValue: "", pageName: "dashboardSiteCompare", enableSitesSelector: false, pages: [
        ]
      }
    ],
    canChooseMonth: false,
    showStockCover: true,
    includeExtraSpainMenuButtons: false,
    showZoeSales: false,
    showHandoverDiarySummary: false,
    showCashDebts: false,
    showBonusDebts: false,
    showRenaultRegistrations: false,
    showDaciaRegistrations: false,
    showFleetRegistrations: false,
    showUsedStockMerchandising: false,
    showCommissions: true,
    showFinanceAddonPerformance: true,
    showfAndIPerformanceRRG: false,
    showVocNPSTile: false,
    showActivityLevels: true,
    showActivityOverdues: false,
    showFleetPerformance: true,
    showVoC: true,
    showServiceBookings: true,
    showSalesmanEfficiency: true,
    showCitNow: true,
    showImageRatios: true,
    showEvhc: true,
    showFinanceAddons: true,
    showRegistrations: true,
    showSimpleDealsByDay: true,
    excludeTypesFromBreakDown: ['Fleet', 'Commercial', 'Corporate'],
    showFinanceAddonsAllSites: true,
    includeDemoStockInStockHealth: true,
  },
  debts:
  {
    agedDebts: true,
    includeBonuses: true,
    simpleSitesTable: false,
    showAgedOnPicker: false,
    showBonusDebtType: false
  },
  evhcTile: 'Vindis',
  horizontalBar: {
    title: 'Demo',
    exDemo: 'params.data.Stock.StockBreakdown.Demo',
    forRenault: false,
    forVindis: true
  },
  stockItemModal: {
    onlineMerchandising: false
  },
  wipsTable: {
    hideBookingColumn: true,
    hideDepartmentColumn: true,
    hideAccountColumn: true,
    hideDueDateOutColumn: true,
    hideWDateInColumn: true,
    hideWDateOutColumn: true,
    hidePartsColumn: true,
    hideOilColumn: true,
    hideLabourColumn: true,
    hideSubletColumn: true,
    hideProvisionColumn: true,
    hideCreatedColumn: true,
    hideNotesColumn: false,
  },
  stockTable: {
    hideTacticalColumn: true,
    hideExManagementColumn: true,
    hideExDemoColumn: true,
  },
  stockList: {
    hideStockDetailsModal: false,
    tableColumns: [
      "Id",
      "SiteDescShort",
      "StockNumberFull",
      "Reg",
      "VehicleType",
      "ProgressCode",
      "DaysInStock",
      "Make",
      "Model",
      "ModelYear",
      "Description",
      "DisposalRoute",
      "PreviousUseCode",
      "Siv",
      "CarryingValue",
      "IsVatQ",
      "CapProvision",
      "StockcheckLocation",
      "SeenAtLatestStkchk",
      "PricedProfit",
      'VariantClass',
      'VehicleTypeCode',
      'VehicleSuperType',
      'AccountStatus',
      'Mileage',
      'Fuel',
      'ShouldBePrepped',
      'IsPrepped',
      'Selling',
      'CapCode'
    ],
    franchises: [],
  },
  performanceLeague: {
    hideBadges: false,
    showDeliveredButton: true,
    incLeaversButton: false,
    showExecAndManagerSelector: true,
  },
  sitesLeague: {
    includeToday: true,
  },
  overAgeStockTable: {
    hideDemoColumn: false,
    hideTacticalColumn: true,
    hideExManagementColumn: true,
    hideExDemoColumn: true,
    hideTradeColumn: false,
    usedColumnName: 'CoreUsed'
  },
  dealPopover: {
    showMetalProfit: true,
    showOtherProfit: true,
    showFinanceProfit: true,
    showAddons: true,
    showAddonProfit: true
  },
  orderBook: {
    showNewOrderbook:false,
    showNewDealButton: false,
    ordersDescription: 'Orders created between',
    hideDeliverySiteColumn: false,
    hideOemReferenceColumn: false,
    hideFinanceProfitColumn: false,
    hideVehicleTypeColumn: false,
    hideVehClassColumn: true,
    hideModelColumn: true,
    hideModelYearColumn: true,
    hideVehicleSourceColumn: true,
    hideDaysToDeliverColumn: false,
    hideDaysToSaleColumn: false,
    hideLocationColumn: false,
    hideIsConfirmedColumn: false,
    hideIsClosedColumn: false,
    hideUnitsColumn: true,
    hideFinanceTypeColumn: false,
    hideSalesChannel: true,
    hideComments: false,
    hideOrderAllocationDate: true,
    hideVehTypeColumn: false,
    hideIsLateCostColumn: true,
    hideOtherProfitColumn: false,
    hideMetalColumn: false,
    hideAddonsColumn: false,
    hideDiscountColumn: false,
    hideChannelColumn: true,
    hideTypeColumn: true,
    includeAccgDate: true,
    customDateTypes: [],
    showMetalSummary: true,
    showOtherSummary: true,
    showFinanceSummary: true,
    showInsuranceSummary: true,
    showAccountingDateButton: false,
    showDeliveryOptionButtons: true,
    defaultDateType: 'Delivery',
    siteColumnWidth: 80,
    customerColumnWidth: 130,
    vehicleClassColumnWidth: 30,
    salesExecColumnWidth: 100,
    descriptionColumnWidth: 200,
    showLateCost: true,
    showOrderOptions: false,
    hideOrderDateSelection: false,
    hideAuditColumn: false,
    showManagerSelector: true,
    hideDateFactoryTransportationColumn: true,
    hideDateVehicleReconditionColumn: true,
    hideDateSiteTransportationColumn: true,
    hideDateSiteArrivalColumn: true,
    hideReservedDateColumn: true,

  },
  partsSales:
  {
    showMarginColPerc: true,
    showMarginCol: true,
    includeMarginCols: true
  },
  handoverDiary: {
    includeCustomerName: true,
    includeLastPhysicalLocation: true,
    includeHandoverDate: false,
    isInvoiced: false,
    isConfirmed: true,
    futureHandoversGreyedOut: false,
    showManagerSelector: true
  },
  
  partsStockDetailedTable: {
    hideCreatedColumn: true,
    partStockBarCharts1: {
      headerName: '% > 6 months',
      field: 'PercentOver6months',
      colId: 'PercentOver6months'
    },
    partStockBarCharts2: {
      headerName: '% 6 - 12 months',
      field: 'Percent6to12Months',
      colId: 'Percent6to12Months'
    },
    showPartStockAgeingColumnsForRRG: false,
    showPartStockAgeingColumnsForVindis: true,
    hideOfWhichColumn: true,
    hideDeadValueColumn: true,
    hideDormantValueColumn: true,
    hideDeadProvColumn: true,
    hideDormantProvColumn: true,
    setClassesForVindis: true,
    setClassesForRRG: false
  },
  salesPerformance: {
    description: 'Orders created between',
    showFranchisePicker: true,
    showLateCostButtons: true,
    showIncludeExcludeOrders: false,
    showOrderRateReportType: true,
    showTradeUnitButtons: true,
    showMotabilityButtons: true,
    showCustomReportType: true,
    showAllSites: true
  },
  selectionsService: {
    ageingOptions: [
      { description: "30 days", ageCutoff: 30 },
      { description: "45 days", ageCutoff: 45 },
      { description: "60 days", ageCutoff: 60 },
      { description: '90 days', ageCutoff: 90 },
      { description: '120 days', ageCutoff: 120 },
      { description: '180 days', ageCutoff: 180 },
      { description: '270 days', ageCutoff: 270 },
      { description: '1 Year', ageCutoff: 365 },
      { description: '2 Years', ageCutoff: 730 },
      { description: '2+ Years', ageCutoff: 10000 }, // Big number which should never really be reached
    ],
    ageingOption: { description: '90 days', ageCutoff: 90 },
    deliveryDateDateType: 'Delivery',
    eligibleForCurrentUserCheck: false
  },
  stockReport: {
    showAgePicker: false,
    hideOnRRGSiteCol: true,
    initialStockReport: 'Dashboard_PartsStock_UsedStock',
    seeUsedStockReport: true,
    seeAllStockReport: true,
    seeUsedMerchandisingReport: false,
    seeOverageStockReport: true,
    seeStockGraphsReport: false,
    seeStockByAgeReport: false,
    includeReservedCarsOption: false
  },
  serviceBookingsTable: {
    showPrepHours: false,
    clickSiteEnable: false,
  },
  whiteboard: {
    showConfirmed: true,
    showNotConfirmed: true,
    showFinance: true,
    showAddons: true,
    showLateCostPicker: false,
    showManagerSelector: true
  },
  serviceChannels: [
    { displayName: 'Retail', name: 'Retail', channelTags: ['retail'], icon: 'fas fa-wrench', hasHours: true, divideByChannelName: 'Retail', isLabour: true },
    { displayName: 'MOT', name: 'MOT', channelTags: ['mot'], icon: 'fas fa-wrench', hasHours: true, divideByChannelName: 'MOT', isLabour: true },
    { displayName: 'Internal', name: 'Internal', channelTags: ['internal'], icon: 'fas fa-car-wash', hasHours: true, divideByChannelName: 'Internal', isLabour: true },
    { displayName: 'Warranty', name: 'Warranty', channelTags: ['warranty'], icon: 'fas fa-engine-warning', hasHours: true, divideByChannelName: 'Warranty', isLabour: true },
    { displayName: 'Labour', name: 'Labour', channelTags: ['retail', 'internal', 'warranty', 'mot'], isTotal: true, icon: '', hasHours: true, divideByChannelName: 'Labour', isLabour: false },
    { displayName: 'Tyre/Sublet', name: 'Tyre/Sublet', channelTags: ['tyre', 'sublet'], icon: 'fas fa-tire', hasHours: true, divideByChannelName: 'Retail', isLabour: false },
    { displayName: 'Oil', name: 'Oil', channelTags: ['oilWarr', 'oilExt', 'oilInt', 'oil'], icon: 'fas fa-oil-can', hasHours: true, divideByChannelName: 'Retail', isLabour: false },
    { displayName: 'Other', name: 'Other', channelTags: ['other', 'sundry'], icon: 'fal fa-circle', hasHours: true, divideByChannelName: 'Retail', isLabour: false },
    { displayName: 'Total', name: 'Total', channelTags: ['retail', 'mot', 'internal', 'warranty', 'tyre', 'oilWarr', 'oilExt', 'oilInt', 'oil', 'sublet', 'other', 'sundry', 'mot'], isTotal: true, icon: '', hasHours: true, divideByChannelName: 'Labour', isLabour: false },
  ],
  partsChannels: [
    { displayName: 'Retail', name: 'Retail', channelTags: ['retail', 'nonfran', 'network', 'trade'], icon: 'fas fa-wrench', channelTag: 'retail', hasHours: false, divideByChannelName: 'Retail', isLabour: false }, //added network in on 28Aug20
    { displayName: 'Internal', name: 'Internal', channelTags: ['internal'], icon: 'fas fa-car-wash', channelTag: 'internal', hasHours: false, divideByChannelName: 'Internal', isLabour: false },
    { displayName: 'Workshop Internal', name: 'Workshop Internal', channelTags: ['wshopInternal'], icon: 'fas fa-car-wash', channelTag: 'wshopInternal', hasHours: false, divideByChannelName: 'Workshop Internal', isLabour: false },
    { displayName: 'Workshop Retail', name: 'Workshop Retail', channelTags: ['wshopRetail'], icon: 'fas fas fa-tire ', channelTag: 'wshopRetail', hasHours: false, divideByChannelName: 'Workshop Retail', isLabour: false },
    { displayName: 'Workshop Warranty', name: 'Workshop Warranty', channelTags: ['wshopWarranty'], icon: 'fas fa-engine-warning', channelTag: 'wshopWarranty', hasHours: false, divideByChannelName: 'Workshop Warranty', isLabour: false },
    { displayName: 'Total', name: 'Total', isTotal: true, channelTags: ['retail', 'nonfran', 'internal', 'wshopInternal', 'wshopRetail', 'wshopWarranty'], icon: '', channelTag: 'total', hasHours: false, divideByChannelName: 'Total', isLabour: false },
  ],
  initialPageURL: "/dashboard",

  orderBookURL: "/orderBook",
  fleetOrderbookURL: "/fleetOrderbook",
  product:
  {
    tyreInsurance: 'HasTyreInsurance',
    tyreAlloyInsurance: 'HasTyreAndAlloyInsurance',
    showAlloyInsurance: true,
  },
  dealDone: {
    showVindisSitePicker: true,
    showRRGSitePicker: false,
    showRRGPopoverContent: false,
    showVindisPopoverContent: true,
  },
  evhc: {
    showTechTable: false,
    vehiclesCheckedPercent: 100,
    workQuoted: 0,
    workSoldPercent: 65,
    eDynamixView: false,
    redWorkSoldPercent: 65,
    amberWorkSoldPercent: 25
  },
  fAndISummary: {
    processTypeAndTypeAlloy: false,
    hideAlloyColumn: false
  },
  partsStock: {
    includeOfWhichColumns: true
  },
  dealsForTheMonth: {
    showMetal: true,
    showOther: true,
    showFinance: true,
    showAddons: true,
    showGpu: true,
    showBroughtInColumn: true,
    showLateCostPicker: false,
    showIncludeExcludeOrders: false
  },
  partsStockSitesCoverTable: {
    partStockName: 'PartsStock',
  },

  dealsDoneThisWeek: {
    showPlotOptions: true
  },
  orderTypePickerOptions: {
    showRetail: true,
    showFleet: true
  },
  vehicleTypePicker:
  {
    showUsed: true,
    showNew: true,
    showAll: true,
    hiddenVehicleTypes: []
  },
  userSetup: {
    hideUploadReports: false,
    hideViewReports: false,
    hideCommReview: false,
    hideCommSelf: false,
    hideSerReviewer: false,
    hideSerSubmitter: false,
    hideStockLanding: true,
    hideSuperCup: true,
    hideIsSalesExec: false,
    hideAllowReportUpload: false,
    hideAllowReportCentre: false,
    hideLiveforecast: false,
    hideSalesRoles: true,
    hideCanEditExecManagerMappings: false,
    hideTMgr: false,
    allSalesRoles: ['None', 'NewUsed'],
    canReviewStockPrices: true,
    canActionStockPrices: true,
    canEditStockPriceMatrix: true,

  },
  languageSelection: false,


  serviceSummary: {
    showTableTypeSelector: true,
    defaultTableType: 'Cumulative',
    tableTypes: ['Cumulative', 'Daily'],
    defaultTimeOption: 'MTD',
    timeOptions: ['MTD', 'WTD', 'Yesterday'],
    showTechGroupColumns: true,
  },
  partsSummary: {
    showTableTypeSelector: true,
    defaultTableType: 'Cumulative',
    tableTypes: ['Cumulative', 'Daily'],
  },
  serviceSalesDashboard: {
    onlyLabour: false
  },



  dealDetailsModal: {
    currencyDP: 2,
    costColumnTranslation: 'Common_Cost',
    dealDetailsSection: {
      showVariant: false,
      showWebsiteDiscount: false,
      showFinanceType: true,
      showOEMReference: true,
      showQualifyingPartEx: false,
      showPhysicalLocation: true,
      showIsClosed: true,
      showFinanceCo: false,
      showDescription: false,
      showUnits: false,
      showVehicleAge: false,
      showIsLateCost: false,
      showAuditPass: true,
      showInvoiceNo: false
    },
    metalProfitSection: {
      headerTranslation: 'DealDetails_MetalProfit',
      showVATCost: true
    },
    otherProfitSection: {
      showRegBonus: true,
      showIntroComm: true,
      showBrokerCost: true,
      showAccessories: true,
      showPaintProtectionAccessory: true,
      showFuel: true,
      showDelivery: true,
      showStandardWarranty: true,
      showPdi: true,
      showMechPrep: true,
      showBodyPrep: true,
      showOther: true,
      showError: true,
      showTotal: true
    },
    addonsSection: {
      showPaintProtection: false,
      showWarrantyForNewCar: true
    },
    datesSection: {
      showCustomerDestinationDeliveryDate: false,
      showEnterImportCentreDate: false,
      showShipDate: false,
      showExitImportCentreDate: false,
      showAllocationDate: false,
      showDateVehicleRecondition: false,
      showDateFactoryTransportation: false,
      showDateSiteArrival: false,
      showDateSiteTransportation: false
    },
    financeProfitSection: {
      show: true,
      rciFinanceCommissionText: 'DealDetails_PCPFinanceCommission',
      financeCommissionText: 'DealDetails_HPAndOtherFinanceCommission',
      showSelectCommission: false,
      showProPlusCommission: false,
      showStandardsCommission: false
    },
    showTotalProfitExludingFactoryBonusSection: true,
    showTotalProfitSection: true
  },
  donutShowLastYearUnits: false,
  showNewUsedSummaryBadges: true,
  showPrepCostsWhenValuing: false,
  isSingleSiteGroup: false,

  showChangePriceNowInputAlways:false,
  menuItems: {
    dashboard_HasDashboard: true,
    dashboard_Home: false,
    dashboard_Overview: true,
    dashboard_Sales: true,
    dashboard_NewKPIs: false,
    dashboard_UsedKPIs: false,
    dashboard_Aftersales: true,
    dashboard_SiteCompare: true,
    orderbook: true,
    orderbook_HasOrderbook: true,
    orderbook_Retail: true,
    orderbook_Distrinet:false,
    orderbook_Fleet: false,
    operationalReports_HasOperationReports: true,
    operationalReports_DealsWeek: true,
    operationalReports_DealsMonth: true,
    operationalReports_Whiteboard: true,
    operationalReports_HandoverDiary: true,
    operationalReports_TelephoneStats: false,
    operationalReports_StockLanding: false,
    operationalReports_PerformanceTrends: true,
    salesReports_HasSalesReports: true,
    salesReports_SalesPerformance: true,
    salesReports_Alcopas: false,
    salesReports_OrderRate: false,
    salesReports_Registrations: false,
    salesReports_FAndI: true,
    salesReports_StockReports: true,
    salesReports_StockList: true,
    salesReports_Debtors: true,
    salesReports_CitNOW: true,
    salesReports_ImageRatios: true,
    salesReports_GDPR: true,
    salesReports_Activities: true,
    reportPortal: true,
    aftersalesReports_HasAftersalesReports: true,
    aftersalesReports_ServiceSales: true,
    aftersalesReports_ServiceBookings: true,
    aftersalesReports_EVHC: true,
    aftersalesReports_Upsells: false,
    aftersalesReports_WIPReport: true,
    aftersalesReports_Debtors: true,
    aftersalesReports_CitNOW: true,
    aftersalesReports_PartsSales: true,
    aftersalesReports_PartsStock: true,
    peopleReports_HasPeopleReports: true,
    peopleReports_PerformanceLeague: true,
    peopleReports_SalespersonEffeciency: false,
    peopleReports_SalespersonCommission: true,
    peopleReports_Scratchcard: false,
    peopleReports_SalesExecReview: true,
    vehiclePricing_HasVehiclePricing: true,vehiclePricing_ShowDetailedMenu:true,
    vehiclePricing_Dashboard: true,vehiclePricing_SitesLeague:true,
    vehiclePricing_StockReport: true,
    vehiclePricing_TodaysPriceChanges: true,
    vehiclePricing_OptedOutVehicles: true,
    vehiclePricing_LocationOptimiser: true,
    vehiclePricing_VehicleValuation: true,vehiclePricing_Home:true,
    vehiclePricing_BuyingOpportunities: true,
    vehiclePricing_LeavingVehicleTrends: true,
    vehiclePricing_LeavingVehicleDetail: true,
    vehiclePricing_SiteSettings: true,
    vehiclePricing_StockQuickSearch: true,
    userMaintenance: true
  },
  showRotationButton: true,
  showLatestSnapshotDate: false,
  showApproveAutoPrices:false,
  showNestedSideMenu: true,
  // autoprice: {
  //   defaultShowUnpublishedAds:true,
  //   defaultShowNewVehicles: false,
  //   vehicleValuationShowCostingDetail: true,
  //   applyPriceScenarios: false,
  //   lifecycleStatusDefault: ['FORECOURT','SALE_IN_PROGRESS'],
  //   vehicleTypes: null,
  //   defaultVehicleTypes: null,
  //   separateBuyingStrategy:false,separateBuyingStrategy2:false,
  //   allowChooseNewStrategy:true,
  //   allowTestStrategy:false,
  //   stockReport: {
  //     showDMSSellingPrice_Col: true,
  //     showVsDMSSellingPrice_Col: true,
  //     showPhysicalLocation_Col: false,

  //   },
  //   defaultToDaysInStock:false,
  // },

  vehiclePricing_StockReport_showBcaColumns: false,
  dealershipBackgroundImageName: 'Vindis',
  homeIsLandingPage: false,
  showRegionFilterOnSiteDashboard: false

};
