import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { SiteVM } from '../model/main.model';
import { ConstantsService } from '../services/constants.service';
import { SelectionsService } from '../services/selections.service';



@Component({
  selector: 'sitePicker',
  template: `
    <!-- Site selector -->
    <div ngbDropdown dropright class="d-inline-block" id="siteDropdown">

        <button [disabled]="disabled" 
          [ngClass]="getClassList()"
          [ngStyle]="{'margin': '0'}"
          class="btn btn-primary" (click)="generateSitesList()"
          ngbDropdownToggle>{{siteChosenLabel()}}
        </button>
          
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

        <!-- ngFor buttons -->  
        <ng-container    *ngFor="let site of sites">
        <button *ngIf="site.SiteId!==0" (click)="toggleItem(site)" [ngClass]="{'active':site.isSelected}"
        ngbDropdownItem>{{site.SiteDescription}}</button>
        </ng-container>

        <!-- Sites by franchise -->
        <ng-container *ngIf="this.selections && this.selections.user?.AccessAllSites">
        <ng-container *ngFor="let franchise of sitesByFranchise">
          <button (click)="toggleItem(franchise)" [ngClass]="{ 'active': franchise.isSelected }" ngbDropdownItem>
          {{constants.translatedText.All}} {{ franchise.label }}
          </button>
        </ng-container>
        </ng-container>

        <!-- select Total -->  
        <button class="quickSelect" (click)="quickSelectTotal()" ngbDropdownItem>{{constants.translatedText.Total}}</button>
        <!-- quick select -->
          <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="selectSites()" [disabled]="noSitesSelected" [ngClass]="{'disabled':noSitesSelected}">OK</button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
          </div>

        </div>
      </div>
    
    `
  ,
  styles: [`

        @media (min-width: 0px) and (max-width: 1920px) and (hover:none) {
          #siteDropdown .dropdown-menu{columns:2}

        }  

    `]
})


export class SitePickerComponent implements OnInit {
  @Input() sitesFromParent: SiteVM[];
  @Input() buttonClass: string;
  @Input() onlyOneSite: boolean;
  @Input() disabled: boolean;
  @Input() allSites: SiteVM[];
  @Output() updateSites = new EventEmitter<SiteVM[]>();

  public sites: SiteVM[];
  public siteIds: number[];
  public noSitesSelected: boolean = false;

  public sitesByFranchise: Array<{ label: string, isSelected?: boolean }> = [
    { label: 'Audi', isSelected: false },
    { label: 'VW', isSelected: false },
    { label: 'VW Commercial', isSelected: false },
    { label: 'Skoda', isSelected: false },
    { label: 'Other', isSelected: false }
  ]

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,

  ) { }


  ngOnInit(): void {
  }


  siteChosenLabel() {

    if (!this.sitesFromParent) return 'Sites'
    if (this.sitesFromParent.length == 0) {
      return 'No sites selected'
    } else if (this.sitesFromParent.length == 1) {
      return this.sitesFromParent[0].SiteDescription
    } else if (this.sitesFromParent.length < 4) {
      let siteNames = ''
      this.sitesFromParent.forEach((site, i) => {
        if (i > 0) { siteNames = siteNames + ',' } //leading comma for 2nd item onwards
        siteNames = siteNames + site.SiteDescShort;
      })
      return siteNames
    } else if (this.sitesFromParent.length == this.allSites.length - 1) {
      return 'All Sites'
    } else {
      return 'Sites'
    }
  }


  // First to run on Button click
  generateSitesList() {

    if(this.sitesFromParent){
      this.siteIds = this.sitesFromParent.map(x => x.SiteId);
    }
    else{
      this.siteIds = this.allSites.map(x => x.SiteId);
    }

    //recreate local list
    if (!this.sites)
    {
      this.sites = this.constants.clone(this.allSites.filter(x => x.IsEligibleForUser));
    
      this.selections.totalSelected = false;

      this.sites.forEach(s => {
        if (this.siteIds.indexOf(s.SiteId) > -1) {
          s.isSelected = true;
        }
      })

    }

    // Find out which, if any, franchises are selected.
    this.determineSelectedFranchises();
  }


  toggleItem(item: any) {

    if (!this.onlyOneSite) {
      item.isSelected = !item.isSelected

      // If an entire franchise has been selected/unselected, select/unselect all the sites which match.
      if (item.label) {
        this.sites.forEach(site => {
          if (site.RegionDescription === item.label) 
          { 
            site.isSelected = item.isSelected 
          }
        })

        this.selections.totalSelected = false;
      }

      this.determineSelectedFranchises();
    }
    else {
      this.sites.forEach(s => {
        s.isSelected = false
      })
      item.isSelected = true;
      this.selections.totalSelected = false;
    }

    this.checkIfNoSitesSelected();

  }

  checkIfNoSitesSelected()
  {
    let selectionCount: number = 0;

    this.sites.forEach(s => {

      if(s.isSelected)
      {
        selectionCount++;
      }
      
    })
 
    this.noSitesSelected = selectionCount == 0 ? true : false;
  }


  determineSelectedFranchises() {
    // Loop through each franchise.
    this.sitesByFranchise.forEach(f => {
      let allSelected = true;
      // Loop through each site.
      this.sites.forEach(s => {
        // If the site matches the franchise, and it is not selected, then the "All <franchise_name>" will not be selected.
        if (s.RegionDescription === f.label && !s.isSelected) { allSelected = false; }
      });
      allSelected ? f.isSelected = true : f.isSelected = false
    });
  }

  selectSites() {
    this.updateSites.emit(this.sites.filter(e => e.isSelected));
  }


  quickSelectTotal() {
    //if all selected (except site 0), select none else select all

    let sites = this.sites.filter(x => x.RegionDescription !== 'Total');
    
    if (sites.filter(x => x.isSelected && x.RegionDescription !== 'Total').length == sites.length) {
      this.selections.totalSelected = false;
      this.sites.forEach(s => s.isSelected = false)
      this.sitesByFranchise.forEach(f => f.isSelected = false);
      this.noSitesSelected = true;
    } else {
      this.selections.totalSelected = true;
      this.sites.forEach(s => { s.isSelected = s.SiteId !== 0; })
      this.sitesByFranchise.forEach(f => f.isSelected = true);
      this.noSitesSelected = false;
    }
  }

  getClassList() {
    let classList: string = '';

    if (this.buttonClass) classList += this.buttonClass;
    if (this.selections.selectedSitesIds && this.selections.selectedSitesIds.length > 0 && !this.selections.totalSelected) classList += ' active';
    
    return classList;
  }


}


