import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { Stock } from '../../model/sales.model';
import { StockListRow } from "./StockListRow";
import { StockListService } from "./stockList.service";
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';

interface model {
  name: string,
  isSelected: boolean,
  count: number,
}

@Component({
  selector: 'modelPicker',
  template: `
    <!-- Model selector -->
    <div ngbDropdown dropright class="d-inline-block" id="model">
        <button  class="buttonGroupRight btn btn-primary" (click)="generateModelsList()" [ngClass]="{'active':stockListService.stockList.storedGridState.highLevelFilters.models.length>0}"
          ngbDropdownToggle>{{chosenLabel()}}</button>

        <div id="dropdownButtonHolder" ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <div class="list-container">
            <!-- ngFor buttons -->  
            <ng-container *ngFor="let model of models">
              <button [ngClass]="{ 'active': model.isSelected }" ngbDropdownItem (click)="toggleItem(model)">
                {{ model.name }} ({{ model.count }})
              </button>
            </ng-container>
          </div>
          
          <!-- select Total -->  
          <button
            *ngIf="stockListService.stockList.storedGridState.highLevelFilters.models.length <= 0"
            class="quickSelect"
            ngbDropdownItem
            (click)="quickSelectAll()"
          >
            {{ constants.translatedText.All }}
          </button>

          <!-- clear selection -->  
          <button
            *ngIf="stockListService.stockList.storedGridState.highLevelFilters.models.length > 0"
            class="dropdownBottomButton"
            ngbDropdownItem ngbDropdownToggle
            (click)="clearSelection()"
          >
            {{ constants.translatedText.Clear }}
          </button>

          <!-- quick select -->
          <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="selectModels()">
              {{ constants.translatedText.OKUpper }}
            </button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>
              {{ constants.translatedText.Cancel }}
            </button>
          </div>
        </div>

      </div>
    
    `
  ,
  styles: [`
    .list-container {
      max-height: 40em;
      overflow-y: scroll;
    }
  `]
})


export class ModelPickerComponent implements OnInit {
  @Input() modelsFromParent: string[];
  @Input() buttonClass: string;
  @Input() allStock: StockListRow[];
  @Output() updateModels = new EventEmitter<string[]>();

  public models: model[];
  public dropdownWidth: number;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public stockListService: StockListService

  ) { }


  ngOnInit(): void {

  }


  chosenLabel() {
    if (!this.modelsFromParent) return 'Models'
    if (this.modelsFromParent.length == 0) {
      return 'Models'
    } else if (this.modelsFromParent.length == 1) {
      return this.modelsFromParent[0]
    }  else {
      return 'Models..'
    }

    // else if (this.modelsFromParent.length < 4) {
    //   let models = ''
    //   this.modelsFromParent.forEach((model, i) => {
    //     if (i > 0) { models = models + ',' } //leading comma for 2nd item onwards
    //     models = models + model;
    //   })
    //   return models
    // }
  }

  generateModelsList() {
    this.models = []

    this.allStock.forEach(stockItem => {
      let existingModel = this.models.find(x => x.name === stockItem.Model)
      if (!existingModel) {
        this.models.push({
          name: stockItem.Model,
          isSelected: false,
          count: 1,
        })
      } else {
        existingModel.count++
      }
    })

    this.dropdownWidth = Math.min(Math.ceil((this.models.length/40)),6)

    //tag if it's selected
    this.models.forEach(m => {
      if (this.modelsFromParent.indexOf(m.name) > -1) {
        m.isSelected = true;
      }
    })

    //sort
    this.models.sort((a, b) => b.count - a.count)
  }


  toggleItem(item: any) {
    item.isSelected = !item.isSelected;
  }

  selectModels() {
    this.updateModels.emit(this.models.filter(e => e.isSelected).map(x => x.name));
  }

  quickSelectAll() {
    let allLength = this.models.length;

    if(this.models.filter(x=>x.isSelected).length === allLength){
      this.models.forEach(s => {
        s.isSelected = false;
      })

    }else{
      this.models.forEach(s => {
        s.isSelected = true;
      })

    }

  }


  clearSelection() {
    this.stockListService.stockList.storedGridState.highLevelFilters.models = [];
    this.updateModels.emit([]);
  }


}


