import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { CellClickedEvent, CellDoubleClickedEvent, CellEditingStoppedEvent, CellKeyDownEvent, ColDef, FullWidthCellKeyDownEvent, GetContextMenuItemsParams, GridApi, GridOptions, GridReadyEvent, IRowNode, MenuItemDef, PasteEndEvent, RowClassParams, RowClickedEvent, RowDoubleClickedEvent, SelectionChangedEvent, SideBarDef } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { CphPipe } from 'src/app/cph.pipe';
import { FleetOrderComment } from 'src/app/model/FleetOrderComment';
import { FleetOrderbookNewCommentsParams } from 'src/app/model/FleetOrderbookNewCommentsParams';
import { FleetOrderbookRow } from 'src/app/model/FleetOrderbookRow';
import { ExcelChoices, ExcelReportNames, ProfilePicSize, SheetToExtract } from 'src/app/model/main.model';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { GetDealDataService } from 'src/app/services/getDeals.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { localeEs } from 'src/environments/locale.es.js';
import { FleetOrderbookService } from '../fleetOrderbook.service';
import { SimpleTextModalComponent } from '../simpleTextModal/simpleTextModal.component';
import { CustomHeaderService } from 'src/app/components/customHeader/customHeader.service';
import { FleetOrderBookModalComponent } from '../modal/modal.component';
import { TableLayoutManagementService } from 'src/app/components/tableLayoutManagement/tableLayoutManagement.service';

@Component({
  selector: 'fleetOrderbookTable',
  templateUrl: './fleetOrderbookTable.component.html',
  styleUrls: ['./fleetOrderbookTable.component.scss']
})


export class FleetOrderbookTableComponent implements OnInit {
  @ViewChild('deleteTableStateModal', { static: true }) deleteTableStateModal: ElementRef;

  viewPortHeight: number;

  gridOptions: GridOptions;


  //gridHeight: number;

  public sideBar: SideBarDef | string | string[] | boolean | null = {
    toolPanels: [

      {
        id: 'filters',
        labelDefault: 'Filters',
        labelKey: 'filters',
        iconKey: 'filter',
        toolPanel: 'agFiltersToolPanel',
        minWidth: 100,
        width: 200,
        maxWidth: 200,
      },
      {
        id: 'columns',
        labelDefault: 'Columns',
        labelKey: 'columns',
        iconKey: 'columns',
        toolPanel: 'agColumnsToolPanel',
        minWidth: 100,
        width: 200,
        maxWidth: 200,
        toolPanelParams: {
          suppressPivots: true,
          suppressPivotMode: true
        }


      },
    ],
    position: 'left',

    defaultToolPanel: '',
  };

  profilePicSize: ProfilePicSize;
  subscriptionComments: Subscription;
  newData: Subscription;

  public components: {
    [p: string]: any;
  } = {
      agColumnHeader: CustomHeaderNew,
    };

  //private frameworkComponents;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public excel: ExcelExportService,
    public getDealData: GetDealDataService,
    public service: FleetOrderbookService,
    public getDataMethods: GetDataMethodsService,
    public gridHelpersService: AGGridMethodsService,
    private customHeader: CustomHeaderService,
    public tableLayoutManagementService: TableLayoutManagementService
  ) {

    //this.frameworkComponents = { agColumnHeader: CustomHeader1Component }; //sliderFloatingFilter: ExtendedFilterFloatingFilterComponent, myFilter: ExtendedFilterComponent,

  }


  ngOnInit() {

    this.initiateGrid();
    //this.getData();

    this.tableLayoutManagementService.defaultFilterState = null;
    this.tableLayoutManagementService.parent = this.service.tableLayoutManagement;
  }

  ngOnDestroy() {
    this.service.tableLayoutManagement.gridApi = null;
    this.service.tableLayoutManagement.gridColumnApi = null;
  }

  saveColumnState() {
    // Assuming `this.service` is where you want to save the column state
    if (this.service.tableLayoutManagement.gridColumnApi) { // Check to ensure gridColumnApi is not destroyed
      this.service.currentColumnState = this.service.tableLayoutManagement.gridColumnApi.getColumnState();
    }
  }



  onResize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.service.tableLayoutManagement.gridApi) {
      this.service.tableLayoutManagement.gridApi.resetRowHeights();
      //this.resizeGrid();
    }
  }

  currencyFormatter(value: number) {
    return this.cphPipe.transform(value, 'currency', 0);

  }

  dateFormatter(value: Date) {
    return this.cphPipe.transform(value, 'dateLongYear', 0);
  }

  removeZeros(value: any) {
    return value == 0 ? "" : value;
  }


  @HostListener('window:resize', ['$event'])
  getScreenSize(event?) {
    return window.innerHeight;
  }

  resizeGrid() {

  }

  autoSizeColumn(colDef: ColDef, api: GridApi) {

  }


  private provideDefaultColDef(includingFloatingFilters: boolean): ColDef<any> {
    let result = {
      filterParams: {
        newRowsAction: 'keep',
        applyMiniFilterWhileTyping: true,
        buttons: ['clear'],
        cellHeight: this.gridHelpersService.getFilterListItemHeight()
      },
      floatingFilter: false,
      menuTabs: ['generalMenuTab', 'filterMenuTab'],
      resizable: true,
      sortable: true,
      hide: false,
      headerComponentParams: { showPinAndRemoveOptions: true }
    }

    if (includingFloatingFilters) {
      result.floatingFilter = true;
      result.menuTabs = ['generalMenuTab'];  //if we don't have floatingFilter we should include 'filterMenuTab' in here
    }

    return result as ColDef<any>;
  }

  initiateGrid() {

    this.service.editedNodeIds = [];
    this.gridOptions = {

      getLocaleText: (params) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      rowData: this.service.rowData,
      rowGroupPanelShow: 'always',
      animateRows: true,
      context: { thisComponent: this },
      onFilterChanged: () => this.onFilterChange(),
      sideBar: this.sideBar,
      pivotMode: false,
      rowSelection: 'multiple',
      onSelectionChanged: (params) => { this.onSelectionChanged(params) },
      onCellClicked: (params) => this.onCellClicked(params),
      onGridReady: (params) => this.onGridReady(params),
      defaultColDef: this.provideDefaultColDef(true),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      onRowClicked: (params) => this.onRowClicked(params),
      onCellDoubleClicked: (params: CellDoubleClickedEvent) => this.onCellDoubleClickedEvent(params),
      headerHeight: this.gridHelpersService.getRowHeight(70),
      floatingFiltersHeight: this.gridHelpersService.getRowHeight(30),
      pivotRowTotals: 'after',
      rowHeight: this.gridHelpersService.getRowHeight(20),
      copyHeadersToClipboard: false,
      enableRangeSelection: true,
      suppressDragLeaveHidesColumns: true,
      onPasteEnd: (params) => this.onPasteEnd(params),
      undoRedoCellEditing: true,
      onCellEditingStopped: (params) => this.onCellEditingStopped(params),
      onCellKeyDown: (params) => this.onCellKeyDown(params),
      getRowClass: (params) => this.rowClassProvider(params),
      getContextMenuItems: (params) => this.getContextMenuItems(params),
      columnTypes: this.provideColumnTypes(),
      enableCellChangeFlash: true,
      cellFadeDelay: 5000,
      columnDefs: this.service.provideColDefs(),
      onColumnMoved: params => this.saveColumnState(),
      onColumnResized: params => this.saveColumnState(),
      onColumnPinned: params => this.saveColumnState(),
      onColumnVisible: params => this.saveColumnState()
    }

  }


  provideColumnTypes() {
    let types = this.columnTypeService.provideColTypes([]) as any;

    types.delDepot = {
      ...this.standardLabelPropsFltOrd(),
      cellClass: ['agAlignLeft', 'green'],
      editable: true,
      cellEditor: 'agRichSelectCellEditor', cellEditorPopup: true, cellEditorParams: { cellHeight: 30, values: ['MANCHESTER', 'RENAULT MANCHESTER'], }
    };

    types.bcaDamage = {
      cellClass: 'ag-right-aligned-cell',
      enableValue: true, filter: 'agSetColumnFilter',
      cellStyle: (params) => this.bcaDamageCellStyler(params), filterParams: {
        buttons: ['clear', 'apply'],
        comparator: (a, b) => {
          const valA = parseInt(a);
          const valB = parseInt(b);
          if (valA === valB) return 0;
          return valA > valB ? 1 : -1;
        }
      }
    };

    types.stockCategory = {
      ...this.standardLabelPropsFltOrd(),
      cellClass: ['agAlignLeft', 'green'],
      editable: true,
      cellEditor: 'agRichSelectCellEditor',
      cellEditorPopup: true,
      cellEditorParams: { cellHeight: 30, values: ['CAR / PERSONALISATION', 'FULL CONVERSION'], }
    };

   

    types.labelEditable = {
      ...this.standardLabelPropsFltOrd(),
      editable: true,
    };

    return types
  }

  standardLabelProps() {
    return { cellClass: 'agAlignLeft', enableRowGroup: true, enablePivot: true, filter: 'agSetColumnFilter', }


  }


  standardLabelPropsFltOrd() {
    return { cellClass: 'agAlignLeft', enableRowGroup: true, enablePivot: true, filter: 'agSetColumnFilter', }
  }



  bcaDamageCellStyler(params: any) {
    //console.log(params)
    if (params.value == null) {
      return;
    }
    if (params.value > 0) {
      return { backgroundColor: 'red' }
    } else {
      return { backgroundColor: 'lightGreen' }
    }
  }

  rightAlignedProps() {
    return {
      //headerClass: 'ag-right-aligned-header',
      cellClass: 'ag-right-aligned-cell'
    }
  }

  standardBooleanProps() {
    return {
      cellRenderer: (params) => params.value ? '&#x2714;' : '&square;',
      floatingFilter: true,
      filterParams: {
        buttons: ['clear', 'apply'],
        cellRenderer: (params) => {
          if (params.value == '(Select All)') { return params.value }
          return params.value ? '&#x2714;' : '&square;'
        },
        cellHeight: this.gridHelpersService.getFilterListItemHeight()
      },
      filter: 'agSetColumnFilter',
      enablePivot: true,
    }
  }

  onRowClicked(params: RowClickedEvent<any, any>): void {
    params.api.closeToolPanel()
  }

  onCellKeyDown(params: CellKeyDownEvent<any, any> | FullWidthCellKeyDownEvent<any, any>): void {
    if (params.event['keyCode'] === 46) {
      //alert('delete')
      this.tagSelectedNodesAsEdited();
    }
  }
  onPasteEnd(params: PasteEndEvent<any, any>): void {
    //console.log('pasteEnd', params)
    this.tagSelectedNodesAsEdited();
  }
  private tagSelectedNodesAsEdited() {
    const selectedNodes = this.service.tableLayoutManagement.gridApi.getSelectedNodes();
    this.service.editedNodeIds.push(...selectedNodes.map(x => x.id));

    selectedNodes.map(node=>{
      const row:FleetOrderbookRow = node.data;
          row.updateForecastReasons();
    })

    let redrawParms = { rowNodes: selectedNodes };
    this.service.tableLayoutManagement.gridApi.redrawRows(redrawParms);
  }

  onCellEditingStopped(params: CellEditingStoppedEvent<any, any>): void {


    if (params.column.getColId() === "RUKForecastFRD") {
      // console.log('Original newValue:', params.newValue);
      const newValAsDate = new Date(params.newValue);
      if (!isNaN(newValAsDate.getTime())) {
          // Valid date
          params.node.setDataValue(params.column.getColId(), newValAsDate);
          // console.log('Updated cell value to valid date:', newValAsDate);
      } else {
          // Handle invalid date
          console.error('Invalid date entered:', params.newValue);
          // Optionally reset to previous value or handle error
          // params.node.setDataValue(params.column.getColId(), null);
      }
  }



    if (params.valueChanged) {
      this.service.editedNodeIds.push(params.node.id)
      this.service.editedNodeIds = [...new Set(this.service.editedNodeIds)] //distinct them
      this.service.tableLayoutManagement.gridApi.redrawRows({ rowNodes: [params.node] })  //redraw this row, to force the rowClass to pickup that it's edited

      //SPK-4313 dynamic RUK forecast cols
      const forecastCols:string[]=['RUKIsGoing','RUKForecastReason', 'RUKForecastSubReason', 'RUKForecastFRD']
      if (forecastCols.includes(params.colDef.field)){
        let node = params.node;
        const row:FleetOrderbookRow = node.data;
        row.updateForecastReasons();
        params.api.redrawRows({rowNodes:[node]})
      }
    }
  }

  updateRUKForecastReasonSettings() {
    
    const reasonsThatRequireSubReason:string[]=[
      'Sold add. Works / conversion required',
'Sold conversion required',
`Sold, customer can't take delivery`,
'Unsold, add. Works / conversion required',
'Unsold, conversion required',
    ]

    this.service.tableLayoutManagement.gridApi.forEachLeafNode(node => {
      const data: FleetOrderbookRow = node.data;
      data.updateForecastReasons();
    })
  }

  onCellClicked(params: CellClickedEvent): void {
    if (params.colDef.colId !== 'SparkComments') {
      this.service.columnDefsService.closeCommentEmitter.emit()
    }

    this.service.resetRowChoices();
    this.service.lastClickedCell = params;

  }

  rowClassProvider(params: RowClassParams): string | string[] {
    //console.log(params);
    let results = []

    if (this.service.editedNodeIds.includes(params.node.id)) { results.push('edited') }
    if (params.data?.IsHidden) { results.push('hidden') }
    if (params.data?.RemovedDate) { results.push('removed') }

    return results.join(' ');
  }

  onSelectionChanged(params: SelectionChangedEvent) {
    if (!this.service.tableLayoutManagement.gridApi) { return }
    // this.service.tableLayoutManagement.gridApi.setPinnedBottomRowData([])
    // this.service.tableLayoutManagement.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData())

  }

  onFilterChange() {
    this.service.currentFilterState = this.service.tableLayoutManagement.gridApi?.isAnyFilterPresent() ? this.service.tableLayoutManagement.gridApi.getFilterModel() : null;
    this.service.updateCounts();
  }



  getDetailedExcel() {

    let dealIds: number[] = [];
    this.service.tableLayoutManagement.gridApi.forEachNodeAfterFilter(node => {
      if (!!node.data) {
        dealIds.push(node.data.DealId)
      }
    })

    let params: ExcelChoices = {
      DealIds: dealIds,
      SheetName: "Fleet Orderbook"
    }

    this.getDataMethods.getDetailedExcel(ExcelReportNames.DetailedOrderbook, params)
  }


  onGridReady(params: GridReadyEvent) {

    this.service.tableLayoutManagement.gridApi = params.api;
    this.service.tableLayoutManagement.gridColumnApi = params.columnApi;
    //this.service.tableLayoutManagement.gridApi.sizeColumnsToFit();

    this.service.searchTerm.valueChanges.subscribe(value => {
      if (this.service.tableLayoutManagement.gridApi) {
        this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value)
      }
    })

    //reapply table col state
    if (this.service.currentColumnState) {
      this.service.restoreGridState(true, false)
      //this.service.tableLayoutManagement.gridColumnApi.applyColumnState({ state: this.service.currentColumnState, applyOrder: true });
      //this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.filterModel)
    }

    //reapply filter model
    if (this.service.currentFilterState) {
      this.service.restoreGridState(false, true) //restore
      // if (this.service.skipNextFilterStateApplication) {
      //   //we won't restore it
      //   this.service.skipNextFilterStateApplication = false; //but clear for next time
      // } else {
      //   this.service.restoreGridState(false, true) //restore
      // }
    }

    this.service.tableLayoutManagement.originalColDefs = this.service.provideColDefs();

    this.service.updateCounts();
  }








  public excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.service.tableLayoutManagement.gridApi.getModel()
    this.service.excel.createSheetObject(tableModel, 'Fleet Orderbook', 1.3);
  }

  showFilterRemoveButton() {
    return !this.service.showTiles && this.service.tableLayoutManagement.gridApi?.isAnyFilterPresent();
  }


  clearFilterModel() {
    this.service.currentFilterState = null;
    this.service.tableLayoutManagement.gridApi.setFilterModel(null)
  }

 

  private getColWidths(colIndexesOrdered: number[], colDefs: ColDef[]) {
    let colWidths = [];
    colIndexesOrdered.forEach(i => {
      colWidths.push(colDefs[i].width / 7);
    });
    return colWidths;
  }

  private getRows(colIndexesOrdered: number[], colDefs: ColDef[]) {
    let rows = [];
    this.service.tableLayoutManagement.gridApi.forEachNodeAfterFilter(node => {
      let rowData: FleetOrderbookRow = node.data;
      let row = [];
      colIndexesOrdered.forEach(i => {
        let field = colDefs[i].field;
        let colId = colDefs[i].colId;
        let value = rowData[field];
        //specific formatting things.  have to do this for each cellValueGetter
        if (colId === 'VehicleOrderNumberNoLeadingLetters') { value = value.replace('EGB', '') }
        if (colId === 'CustomerOrderNo') { value = value.padStart(6, '0') }
        row.push(value);
      });
      rows.push(row);
    });
    return rows;
  }

  private getHeaders(colIndexesOrdered: number[], colDefs: ColDef[]) {
    let headers = [];
    colIndexesOrdered.forEach(i => {
      headers.push(colDefs[i].headerName);
    });
    return headers;
  }

  private getColumnIndexesToMatchGrid(colDefs: ColDef[]) {
    let colIndexesOrdered: number[] = [];
    if (!!this.service.currentColumnState) {
      this.service.currentColumnState.map(x => x.colId).forEach(colId => {
        const findLocation = colDefs.findIndex(x => x.colId === colId);
        colIndexesOrdered.push(findLocation);
      });
    } else {
      colIndexesOrdered = [];
      for (let i = 0; i < colDefs.length; i++) {
        colIndexesOrdered.push(i);
      }
    }
    return colIndexesOrdered;
  }

  getContextMenuItems(params: GetContextMenuItemsParams<any, any>): (string | MenuItemDef)[] {
    const selectedNodeIds = this.service.tableLayoutManagement.gridApi.getSelectedNodes().map(x => x.id);
    if (!selectedNodeIds.includes(params.node.id)) {
      this.service.tableLayoutManagement.gridApi.forEachLeafNode(node => {
        node.setSelected(node.id == params.node.id)
      })
    }

    const result: (string | MenuItemDef)[] = [];

    //standard ones
    result.push(
      'cut', 'copy', 'copyWithGroupHeaders',  //'copyWithHeaders',
    )
    const colDef = params.column.getUserProvidedColDef();
    const row: FleetOrderbookRow = params.node.data;
    const selectedNodes = params.api.getSelectedNodes();

    this.addArchiveOrHideMenu(result, row);
    //this.addInstantChartOptions(result);
    this.addQuickSetMethod(colDef, result);

    if (colDef.colId == 'SparkComments' && selectedNodes.length > 1) {
      result.push(
        'separator',
        {
          icon: '<span class="my-menu-icon">&#x1F4AC</span>',
          name: `Add comment to all ${this.constants.pluralise(selectedNodes.length, 'row', 'rows')}`,
          action: () => {
            this.addBulkComment(selectedNodes)


          },
        }
      )
    }

    this.customHeader.addRegularSorts(result, colDef, params.columnApi, params.column.getSortIndex());

    return result.concat(this.tableLayoutManagementService.getTableContextMenuItems());
  }



  addBulkComment(selectedNodes: IRowNode[]) {
    //launch modal

    const modalRef = this.service.modalService.open(SimpleTextModalComponent);
    modalRef.componentInstance.header = 'Add Comment'
    modalRef.componentInstance.placeholder = 'Enter comment...';

    modalRef.result.then(res => {

      let newCommentParams: FleetOrderbookNewCommentsParams = {
        Text: res.chosenLabel,
        FleetOrderbookIds: selectedNodes.map(x => x.data).map(x => x.RenaultOrNissanOrderItemId),
        IsRenault: this.service.isRenault
      };

      this.service.apiAccess.post('api/FleetOrderbook', 'AddComments', newCommentParams).subscribe((results: FleetOrderComment[]) => {

        results.forEach(result => {
          if (this.service.isRenault) {
            const matchInRows = this.service.rowData.find(x => x.RenaultOrNissanOrderItemId === result.RenaultOrderItem_Id);
            matchInRows.SparkComments += `${result.Id}|${result.Date}|${result.Text}|${this.service.selections.user.Name}`

          } else {
            const matchInRows = this.service.rowData.find(x => x.RenaultOrNissanOrderItemId === result.NissanOrderItem_Id);
            matchInRows.SparkComments += `${result.Id}|${result.Date}|${result.Text}|${this.service.selections.user.Name}`
            // console.log(matchInRows)
          }
        })

        this.service.chosenRowNodes = null;

        this.service.tableLayoutManagement.gridApi.redrawRows();
        this.service.constants.toastSuccess('Comment saved')
      },
        e => {
          console.error("failed to save comment " + JSON.stringify(e));
        })
    })





  }

  private addQuickSetMethod(colDef: ColDef<any>, result: (string | MenuItemDef)[]) {
    if (['labelEditable', 'numberEditableFltOrd', 'stockCategory', 'delDepot'].includes(colDef.type as string) && this.service.tableLayoutManagement.gridApi.getSelectedNodes().length > 0) {
      this.addQuickSetMenu(result, colDef);
    }
  }

  private addInstantChartOptions(result: (string | MenuItemDef)[]) {
    result.push('separator', 'chartRange');
  }

  private addArchiveOrHideMenu(result: (string | MenuItemDef)[], row: FleetOrderbookRow) {
    result.push('separator');

    result.push(

      {
        name: row.IsHidden ? 'Set to not retailed/cancelled' : 'Set to retailed/cancelled',
        cssClasses: ['redFont'],
        //icon: '&#x1F3AB;',
        icon: '&#x274C;',
        action: () => {
          this.service.tableLayoutManagement.gridApi.getSelectedNodes().forEach(node => {
            let thisRow = node.data;
            thisRow.IsHidden = !thisRow.IsHidden;
            node.setData(thisRow);
          });
          this.tagSelectedNodesAsEdited();
        },
      },
      {
        name: row.IsRemoved ? 'Set to not removed' : 'Set to removed',
        cssClasses: ['redFont'],
        action: () => {
          this.service.tableLayoutManagement.gridApi.getSelectedNodes().forEach(node => {
            let thisRow = node.data;
            thisRow.IsRemoved = !thisRow.IsRemoved;
            node.setData(thisRow);
          });
          this.tagSelectedNodesAsEdited();
        },
      },

    );
  }

  private addQuickSetMenu(result: (string | MenuItemDef)[], colDef: ColDef<any>) {
    result.push('separator');

    const allCurrents: string[] = [];
    this.service.tableLayoutManagement.gridApi.forEachLeafNode(leafNode => {
      allCurrents.push(leafNode.data[colDef.field]);
    });
    const distinctCurrents = [...new Set(allCurrents)].sort((a, b) => a.localeCompare(b));
    const subMenus = [];
    distinctCurrents.forEach(item => {
      subMenus.push(
        {
          name: item || '(blank)',
          action: () => {
            this.service.tableLayoutManagement.gridApi.getSelectedNodes().forEach(node => {
              node.data[colDef.field] = item;
            });
            this.tagSelectedNodesAsEdited();
          }
        }
      );
    });
    result.push(
      {
        name: 'Quick set',
        subMenu: subMenus
      }
    );
  }





  clearSearchTerm() {
    this.service.searchTerm.setValue("");
    if (this.service.tableLayoutManagement.gridApi) this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value);
  }



  onCellDoubleClickedEvent(event: CellDoubleClickedEvent) {
    const colType = event.column.getColDef().type;
    if (colType == 'stockCategory' || colType.includes('Editable')) {
      return;
    }
    const modalRef: NgbModalRef = this.modalService.open(FleetOrderBookModalComponent, { size: 'lg' });
    modalRef.componentInstance.header = `Vehicle Chassis ${event.data.Chassis}`;
    modalRef.componentInstance.body = event.data;
    modalRef.componentInstance.isRenault = this.service.isRenault;
  }
}

