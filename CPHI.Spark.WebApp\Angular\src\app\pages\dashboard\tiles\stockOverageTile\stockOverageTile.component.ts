import { Component, OnInit, Input, EventEmitter } from "@angular/core";
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { StockReportModalComponent } from "src/app/components/stockReportModal/stockReportModal.component";
import { CphPipe } from "src/app/cph.pipe";
import { MenuItemNew } from "src/app/model/main.model";
import { StockModalRowWithNext30 } from "src/app/model/sales.model";
import { DistrinetService } from "src/app/pages/distrinet/distrinet.service";
import { StockListService } from "src/app/pages/stockList/stockList.service";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { OverageStockSummaryItem } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";
import { SpainDashboardUsedService } from "../../dashboards/dashboardUsedSpain/dashboardUsedSpain.service";




@Component({
  selector: 'stockOverageTile',
  templateUrl: './stockOverageTile.component.html',

  styles: [
    `
    table { table-layout: fixed; width: 100%; height: 100%; }
    tr { height: 50%; }
    tr.numberRow td { vertical-align: bottom; }
    tr.labelRow td { vertical-align: top; }
    #overageCarCountToday { color: red; font-weight: 400; }
    #overageCarCountEOM { color: red; font-weight: 200; } 

    .numberHolder { height: 100%; }
  `
  ]

})
export class StockOverageTileComponent implements OnInit {

  @Input() public department: string;
  @Input() public agedOver: number;
  @Input() public newDataEmitter: EventEmitter<void>;
  @Input() public data: OverageStockSummaryItem[];
  @Input() public dataSource: string;

  overageCarsToday: number;
  overageCarsEOM: number;
  cardTitle: string;
  subscription: any;



  constructor(
    public selections: SelectionsService,
    public dashboardUsedService: SpainDashboardUsedService,
    public constants: ConstantsService,
    public router: Router,
    public modalService: NgbModal,
    public cphPipe: CphPipe,
    public service: DashboardService,
    public distrinetService: DistrinetService,
    public stockListServic: StockListService,
    public getData: GetDataMethodsService,
    public dashboardService: DashboardService
  ) {

  }

  ngOnInit(): void {
    //this.initParams();

    this.subscription = this.newDataEmitter.subscribe(res => {
      this.initParams();
    })

  }


  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
  }

  initParams() {

    this.overageCarsToday = this.data.find(d => d.Department == this.department && d.AgedOver == this.agedOver && d.Type == 'OverNow').VehicleCount
    this.overageCarsEOM = this.data.find(d => d.Department == this.department && d.AgedOver == this.agedOver && d.Type == 'OverEoM').VehicleCount

    if (this.constants.environment.stockOverageTile_SpainThresholds) {
      this.buildCardTitleSpain();
    } else {
      this.buildCardTitleNotSpain();
    }

  }


  private buildCardTitleNotSpain() {
    switch (this.department) {
      case ('Used'): {
        this.cardTitle = this.constants.translatedText.Dashboard_StockOverage_UsedVehiclesGreaterThan + this.agedOver + ' ' + this.constants.translatedText.DaysLower;
        break;
      };
      case ('Trade'): {
        this.cardTitle = this.constants.translatedText.Dashboard_StockOverage_TradeVehiclesGreaterThan + this.agedOver + ' ' + this.constants.translatedText.DaysLower;
        break;
      }
      case ('New'): {
        this.cardTitle = this.constants.translatedText.Dashboard_StockOverage_NewVehiclesGreaterThan + this.agedOver + ' ' + this.constants.translatedText.DaysLower;
        break;
      }
    };
  }

  private buildCardTitleSpain() {
    let firstPart = this.department === 'Used' ? this.constants.translatedText.Used : this.constants.translatedText.New;


    switch (this.agedOver) {
      case (0): {
        this.cardTitle = `${firstPart} <90 ${this.constants.translatedText.DaysLower}`;
        break;
      };
      case (90): {
        this.cardTitle = `${firstPart} >=90 <180 ${this.constants.translatedText.DaysLower}`;
        break;
      }
      case (180): {
        this.cardTitle = `${firstPart} >=180 ${this.constants.translatedText.DaysLower}`;
        break;
      }
    };
  }

  navigateAway() {
    if (this.constants.environment.stockOverageTile_SpainThresholds) {
      if (this.department == 'New') {
        // Distrinet
        this.distrinetService.initParams();

        this.distrinetService.chosenOriginTypes = ['Stock'];
        this.distrinetService.chosenFranchises = this.service.franchises;

        // Build up filter model to set on the distrinet page
        let filterModel = {
          Antiquity: { type: 'greaterThanOrEqual', filter: this.agedOver }
        };

        this.distrinetService.filterModel = filterModel;

        let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/distrinet');
        if (menuItem) { this.constants.navigateByUrl(menuItem); }  //, 'operationreports'
      } else if (this.department == 'Used') {
        // Stock List
        this.stockListServic.initiateStockList();

        this.stockListServic.stockList.sites = this.service.chosenSites;
        this.stockListServic.stockList.storedGridState.highLevelFilters.franchises = this.service.franchises;
        this.stockListServic.stockList.storedGridState.highLevelFilters.vehicleTypes = ['Used'];

        // Build up filter model to set on the distrinet page
        let filterModel = {
          ageing: { type: 'greaterThanOrEqual', filter: this.agedOver }
        };

        this.stockListServic.stockList.filterModel = filterModel;

        let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/stockList');
        if (menuItem) { this.constants.navigateByUrl(menuItem); }  //, 'salesreports'
      }
    } else {
      this.selections.initiateStockReport();

      this.selections.stockReport.franchises = this.service.franchises;
      this.selections.stockReport.ageingOption = this.selections.stockReport.ageingOptions.find(x => x.ageCutoff == this.agedOver);
      this.selections.stockReport.incomingReportNameChoice = 'Overage Stock';

      this.service.chooseDashboardPage('StockReport');
    }
  }



  goToPageOnClick(timePeriod: string) {
    if (this.constants.environment.stockOverageTile_SpainThresholds) {

      if (this.department == 'New') {
        this.goToDistrinetPage(timePeriod);
      }
      if (this.department == 'Used') {
        this.goToStockPage();
      }

    } else {
      this.openModal(timePeriod);
    }

  }

  openModal(timePeriod: string): void {
    this.selections.triggerSpinner.next({ show: true, message: 'Loading...' });

    let department: string = this.department == 'Used' ? 'CoreUsed' : this.department;
    let siteIds: string = this.dashboardService.chosenSites.map(x => x.SiteId).join(',');
    let franchises: string = this.dashboardService.franchises.toString();

    this.getData.getStockModalRows(department, timePeriod, siteIds, this.agedOver, true, franchises).subscribe((res: StockModalRowWithNext30) => {
      this.selections.initiateStockReportModal(false, res.AgedNow, res.AgedIn30, this.cardTitle, department == 'CoreUsed', this.cardTitle);
      const modalRef = this.modalService.open(StockReportModalComponent, { keyboard: true, size: 'lg' });
      this.selections.triggerSpinner.next({ show: true, message: 'Loading...' });
      modalRef.result.then((result) => { });
    }, error => {
      console.error("Failed to get stock modal rows: ", error);
    }, () => { });
  }

  goToDistrinetPage(timePeriod: string) {


    let fieldName = timePeriod === 'monthEnd' ? "AgeBandEOM" : "AgeBandNow"
    let filterModel: {};
    switch (this.agedOver) {
      case 0: {
        filterModel = {}
        filterModel[fieldName] = { filter: '<90', filterType: 'text', type: 'contains' }
      };

        break;
      case 90: {
        filterModel = {};
        filterModel[fieldName] = { filter: '<1', filterType: 'text', type: 'startsWith' }
      };

        break;
      case 180: {
        filterModel = {};
        filterModel[fieldName] = {
          "filterType": "text",
          "operator": "OR",
          "condition1": {
            "filterType": "text",
            "type": "contains",
            "filter": "<365"
          },
          "condition2": {
            "filterType": "text",
            "type": "contains",
            "filter": ">365"
          }
        }
      }
        break;

    }


    this.distrinetService.initParams();
    this.distrinetService.filterModel = filterModel;
    this.distrinetService.chosenOriginTypes = ['Stock'];

    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/distrinet');
    if (menuItem) { this.constants.navigateByUrl(menuItem); }  //, 'operationreports'
  }

  goToStockPage() {
    this.service.chooseSection('DashboardUsedSpain');
    this.dashboardUsedService.chosenReport = 'StockByAge';
  }

  get showWarning(): boolean {
    const now = new Date();
    let origin: string = this.dataSource == 'DistriNET' ? 'Distrinet Stock' : 'Quiter';

    const hasStaleEntries = this.dashboardService.dataOriginUpdates
      .filter(entry => entry.DataOrigin.includes(origin))
      .some(entry => {
        const hoursSinceUpdate = (now.getTime() - new Date(entry.LastUpdate).getTime()) / (1000 * 60 * 60);
        return hoursSinceUpdate > 24;
      });
  
    return hasStaleEntries;
  }

}


