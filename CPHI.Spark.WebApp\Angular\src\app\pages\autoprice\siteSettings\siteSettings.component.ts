import { DatePipe } from "@angular/common";
import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { ColDef, GridOptions, GridReadyEvent, MenuItemDef, RowDoubleClickedEvent } from "ag-grid-community";
import { fork<PERSON>oin } from "rxjs";
import { CustomHeaderService } from "src/app/components/customHeader/customHeader.service";
import { SiteSettings } from "src/app/model/SiteSettings";
import { StrategyFactorName } from "src/app/model/StrategyFactorName";
import { StrategyVersionVM } from "src/app/model/StrategyVersionVM";
import { SiteSettingsService } from "./siteSettings.service";
import { SiteSettingsModalComponent } from "./siteSettingsModal/siteSettingsModal.component";
import { StrategyFull } from "src/app/model/StrategyFull";
import { ConfirmModalNewComponent } from "src/app/components/confirmModalNew/confirmModalNew.component";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { CustomHeaderAdDetail } from "src/app/components/customHeaderAdDetail/customHeaderAdDetail.component";
import { SelectionsService } from "src/app/services/selections.service";

@Component({
   selector: "app-siteSettings",
   templateUrl: "./siteSettings.component.html",
   styleUrls: ["./siteSettings.component.scss"],
})
export class SiteSettingsComponent implements OnInit {
   StrategyFactorName = StrategyFactorName;
   @ViewChild("currencyModal", { static: true }) currencyModal: ElementRef;
   @ViewChild("numberModal", { static: true }) numberModal: ElementRef;
   @ViewChild("strategyModal", { static: true }) strategyModal: ElementRef;

   chosenNewStrategy: StrategyFull; //transient, just for use in displaying in the dropdown box.   don't move this to service
   chosenNewStrategyEffectiveDate: string; //transient, just for use in displaying in the dropdown box.   don't move this to service

   selectedRowData: SiteSettings[];
   gridOptions: GridOptions;
   columnToAction: ColDef;
   newCurrencyValue: number = 0;
   newNumberValue: number = 0;
   lastSelectedColumnValue: number;
   whenToActionTime: string;

   components: { [p: string]: any } = {
      agColumnHeader: CustomHeaderAdDetail,
   };

   constructor(
      public service: SiteSettingsService,
      private customHeader: CustomHeaderService,
      private datePipe: DatePipe,
      private gridHelpersService: AGGridMethodsService,
      private selectionsService: SelectionsService
   ) {}

   ngOnInit(): void {
      this.service.siteSettingsComponentRef = this;
      this.service.getSitesSettings();
      this.service.getAllPricingPolicies();
      this.service.getAllStrategies();
      //this.service.newStrategyEffectiveDate = this.datePipe.transform(new Date(), 'yyyy-MM-dd');
   }

   async newStrategy() {
      const newStrategy: StrategyFull = new StrategyFull(null);
      await this.service.openStrategy(newStrategy);
   }

   async maybeRegenerateTestStrategyValues() {
      const modalRef = this.service.modalService.open(ConfirmModalNewComponent, {
         size: "md",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      });
      const component: ConfirmModalNewComponent = modalRef.componentInstance;

      component.header = "Are you sure you want to recalculate the test strategy price for all adverts?";
      component.body =
         "This will use the currently selected test strategy for each site to recalculate strategy prices for every advert. This process may take a while, please be patient.";

      try {
         // Await the result of the modal
         await modalRef.result;

         // If confirmed, proceed with recalculation
         this.service.selections.triggerSpinner.emit({ show: true, message: "Recalculating strategy prices" });
         await this.service.recalculateTestStrategyPrices();
         this.service.selections.triggerSpinner.emit({ show: false });
         this.service.constants.toastSuccess("Recalculated test strategy prices successfully");
      } catch {
         // The user closed the modal without confirmation
         // Nothing needed here, handle it as necessary
      }
   }

   async maybeRegenerateTestStrategyDaysToSell() {
      const modalRef = this.service.modalService.open(ConfirmModalNewComponent, {
         size: "md",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      });
      const component: ConfirmModalNewComponent = modalRef.componentInstance;

      component.header = "Are you sure you want to recalculate the test strategy Days To Sell for all adverts?";
      component.body =
         "This will use the currently selected test strategy for each site to recalculate strategy DaysToSell for every advert. This process may take a while, please be patient.";

      try {
         // Await the result of the modal
         await modalRef.result;

         // If confirmed, proceed with recalculation
         this.service.selections.triggerSpinner.emit({ show: true, message: "Recalculating strategy DaysToSell" });
         await this.service.recalculateTestStrategyDaysToSell();
         this.service.selections.triggerSpinner.emit({ show: false });
         this.service.constants.toastSuccess("Recalculated test strategy DaysToSell successfully");
      } catch {
         // The user closed the modal without confirmation
         // Nothing needed here, handle it as necessary
      }
   }

   newPricingPolicy() {
      //generate a new strategy
      let newPolicy: StrategyVersionVM = {
         Id: null,
         CreatedByName: this.service.selections.user.Name,
         CreatedDate: new Date(),
         Name: "New Strategy",
         Comment: "",
         StrategyFactors: [],
         FirstUsed: null,
         LastUsed: null,
      } as StrategyVersionVM;

      let newPolicyToUse = new StrategyVersionVM(newPolicy);

      this.service.openPricingPolicy(newPolicyToUse);
   }

   ngOnDestroy() {
      this.service.gridApi = null;
      this.service.siteSettingsComponentRef = null;
   }

   pricingPolicyTextSummary(strategy: StrategyVersionVM) {
      if (!strategy) {
         console.trace;
      }
      return strategy.pricingPolicyTextSummary;
   }

   // strategyTextSummary(strategy:StrategyFull) {
   //   if (!strategy) {
   //     console.trace;
   //   }
   //   return `Strategy '${strategy.Name}'`
   // }

   initialiseGrid() {
      this.gridOptions = {
         context: { thisComponent: this },
         getContextMenuItems: (params) => this.getContextMenuItems(params),
         getRowHeight: (params) => {
            if (params.node.rowPinned) {
               return this.gridHelpersService.getRowPinnedHeight();
            } else {
               return this.gridHelpersService.getRowPinnedHeight();
            }
         },
         headerHeight: this.gridHelpersService.getHeaderHeight(),
         floatingFiltersHeight: this.gridHelpersService.getFloatingFilterHeight(),
         groupHeaderHeight: this.gridHelpersService.getGroupHeaderHeight(),
         defaultColDef: {
            resizable: true,
            sortable: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               applyMiniFilterWhileTyping: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            autoHeight: true,
            autoHeaderHeight: true,
            floatingFilter: true,
            headerComponentParams: {
               showPinAndRemoveOptions: false,
            },
            floatingFilterComponentParams: { suppressFilterButton: true },
            cellStyle: {
               display: "flex",
               "align-items": "center",
            },
         },
         columnTypes: this.provideColumnTypes(),
         rowSelection: "multiple",
         onSelectionChanged: (params) => this.onSelectionChanged(),
         rowData: this.service.siteSettingsRowData,
         columnDefs: this.getColDefs(),
         onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
         onRowDoubleClicked: (event: RowDoubleClickedEvent) => this.openModal(event),
         getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      };
   }

   getColDefs() {
      return [
         {
            headerName: "Site Id",
            colId: "RetailerSiteId",
            field: "RetailerSiteId",
            type: "label",
            width: 80,
            suppressMovable: true,
         },
         { headerName: "Site", colId: "Name", field: "Name", type: "label", width: 200, suppressMovable: true },
         {
            headerName: "Strategies",
            children: [
               {
                  headerName: "Pricing Strategy",
                  colId: "PricingStrategyComment",
                  field: "strategyName",
                  type: "label",
                  width: 250,
               },
               {
                  // hide: !this.service.constants.autopriceEnvironment.separateBuyingStrategy,
                  headerName: "Buying Strategy",
                  colId: "BuyingStrategyComment",
                  field: "buyingStrategyName",
                  type: "label",
                  width: 250,
               },
               {
                  headerName: "Test Strategy",
                  colId: "testStrategyName",
                  field: "testStrategyName",
                  type: "label",
                  width: 250,
               },
            ],
         },
         {
            headerName: "",
            children: [
               {
                  headerName: "Competitor SearchPlate Range",
                  colId: "CompetitorPlateRange",
                  field: "CompetitorPlateRange",
                  type: "number",
                  width: 75,
               },
            ],
         },
         {
            headerName: "Buying opportunities",
            children: [
               {
                  headerName: "Minimum Opp",
                  colId: "LocalBargainThreshold",
                  field: "LocalBargainThreshold",
                  type: "currency",
                  width: 75,
               },
               {
                  headerName: "Search Radius",
                  colId: "LocalBargainsSearchRadius",
                  field: "LocalBargainsSearchRadius",
                  type: "number",
                  width: 75,
               },
               {
                  headerName: "Min Retail Rating",
                  colId: "LocalBargainsMinRetailRating",
                  field: "LocalBargainsMinRetailRating",
                  type: "number",
                  width: 75,
               },
            ],
         },

         {
            headerName: "Buying Targets",
            children: [
               { headerName: "Margin", colId: "TargetMargin", field: "TargetMargin", type: "currency", width: 75 },
               {
                  headerName: "Additional Mech.",
                  colId: "TargetAdditionalMech",
                  field: "TargetAdditionalMech",
                  type: "currency",
                  width: 75,
               },
               {
                  headerName: "Painwork",
                  colId: "TargetPaintPrep",
                  field: "TargetPaintPrep",
                  type: "currency",
                  width: 75,
               },
               { headerName: "Fee", colId: "TargetAuctionFee", field: "TargetAuctionFee", type: "currency", width: 75 },
               {
                  headerName: "Delivery",
                  colId: "TargetDelivery",
                  field: "TargetDelivery",
                  type: "currency",
                  width: 75,
               },
            ],
         },
         {
            headerName: "Location Optimiser",
            children: [
               {
                  headerName: "Cost Per Mile",
                  hide: this.service.constants.environment.isSingleSiteGroup,
                  colId: "LocationMovePoundPerMile",
                  field: "LocationMovePoundPerMile",
                  type: "currency2dp",
                  width: 75,
               },
               {
                  headerName: "Cost Per Move",
                  hide: this.service.constants.environment.isSingleSiteGroup,
                  colId: "LocationMoveFixedCostPerMove",
                  field: "LocationMoveFixedCostPerMove",
                  type: "currency",
                  width: 75,
               },
            ],
         },
         {
            headerName: "Automatic Pricing",
            children: [
               {
                  headerName: "Enabled",
                  colId: "UpdatePricesAutomatically",
                  field: "UpdatePricesAutomatically",
                  type: "boolean",
                  width: 50,
               },
               {
                  headerName: "Min Price Down",
                  colId: "MinimumAutoPriceDecrease",
                  field: "MinimumAutoPriceDecrease",
                  type: "currency",
                  width: 75,
               },
               {
                  headerName: "Min Price Up",
                  colId: "MinimumAutoPriceIncrease",
                  field: "MinimumAutoPriceIncrease",
                  type: "currency",
                  width: 75,
               },
               {
                  headerName: "Min % Price Down",
                  colId: "MinimumAutoPricePercentDecrease",
                  field: "MinimumAutoPricePercentDecrease",
                  type: "number1dp",
                  width: 75,
               },
               {
                  headerName: "Min % Price Up",
                  colId: "MinimumAutoPricePercentIncrease",
                  field: "MinimumAutoPricePercentIncrease",
                  type: "number1dp",
                  width: 75,
               },
               {
                  headerName: "Include Unpublished in Email",
                  colId: "IncludeUnPublishedAdsInEmailReport",
                  field: "IncludeUnPublishedAdsInEmailReport",
                  type: "boolean",
                  width: 75,
               },
            ],
         },

         {
            headerName: "Automatic Pricing - Timing",
            children: [
               { headerName: "Mon", colId: "UpdatePricesMon", field: "UpdatePricesMon", type: "boolean", width: 50 },
               { headerName: "Tue", colId: "UpdatePricesTue", field: "UpdatePricesTue", type: "boolean", width: 50 },
               { headerName: "Wed", colId: "UpdatePricesWed", field: "UpdatePricesWed", type: "boolean", width: 50 },
               { headerName: "Thu", colId: "UpdatePricesThu", field: "UpdatePricesThu", type: "boolean", width: 50 },
               { headerName: "Fri", colId: "UpdatePricesFri", field: "UpdatePricesFri", type: "boolean", width: 50 },
               { headerName: "Sat", colId: "UpdatePricesSat", field: "UpdatePricesSat", type: "boolean", width: 50 },
               { headerName: "Sun", colId: "UpdatePricesSun", field: "UpdatePricesSun", type: "boolean", width: 50 },
               {
                  headerName: "Public Hols",
                  colId: "UpdatePricesPubHolidays",
                  field: "UpdatePricesPubHolidays",
                  type: "boolean",
                  width: 50,
               },
               {
                  headerName: "When Each Day",
                  colId: "WhenToActionChangesEachDay",
                  field: "WhenToActionChangesEachDay",
                  type: "number",
                  width: 75,
               },
            ],
         },
         {
            headerName: "Opt-Outs",
            children: [
               {
                  headerName: "Max Days",
                  colId: "MaximumOptOutDays",
                  field: "MaximumOptOutDays",
                  type: "number",
                  width: 100,
               },
            ],
         },
      ];
   }

   provideColumnTypes() {
      let commonTypes = { ...this.service.colTypesService.provideColTypes([]) };
      return commonTypes;
   }

   onSelectionChanged() {
      this.selectedRowData = this.service.gridApi.getSelectedRows();
   }

   onGridReady(event: GridReadyEvent) {
      if (this.service.gridApi) {
         this.service.gridApi.redrawRows();
      }
      this.service.gridApi = event.api;
      //this.service.gridApi.sizeColumnsToFit();
      this.service.selections.triggerSpinner.emit({ show: false });
   }

   getContextMenuItems(params: any) {
      this.onSelectionChanged();

      let toReturn: (string | MenuItemDef)[] = ["copy", "copyWithHeaders"];
      // If we have selected rows, show amendment option
      if (this.selectedRowData.length > 0) {
         this.columnToAction = params.column.colDef;
         this.lastSelectedColumnValue = params.node.data[params.column.colDef.field];

         let addActionCommand:boolean = false;
         if(this.selectionsService.user.permissions.canEditPricingStrategy){addActionCommand = true;}
         if(this.columnToAction.colId === "CompetitorPlateRange"){addActionCommand = true;}
         if(this.columnToAction.colId === "CompetitorPlateRange"){addActionCommand = true;}
         if(this.columnToAction.colId === "LocalBargainThreshold"){addActionCommand = true;}
         if(this.columnToAction.colId === "LocalBargainsSearchRadius"){addActionCommand = true;}
         if(this.columnToAction.colId === "LocalBargainsMinRetailRating"){addActionCommand = true;}
         if(this.columnToAction.colId === "TargetAdditionalMech"){addActionCommand = true;}
         if(this.columnToAction.colId === "TargetPaintPrep"){addActionCommand = true;}
         if(this.columnToAction.colId === "TargetDelivery"){addActionCommand = true;}
         if(this.columnToAction.colId === "LocationMovePoundPerMile"){addActionCommand = true;}
         if(this.columnToAction.colId === "LocationMoveFixedCostPerMove"){addActionCommand = true;}


         if (addActionCommand) {
            toReturn.push("separator", {
               icon: "✏️",
               name: `Update the "${this.columnToAction.headerName}" field for ${this.selectedRowData.length} selected sites`,
               cssClasses: ["bold"],
               subMenu: this.getSubMenuForColDef(),
               action: () => this.getActionForColDef(params),
            });
         }
      }


      return toReturn;
   }

   getSubMenuForColDef() {
      let subMenu: MenuItemDef[];

      if (this.columnToAction.type === "boolean") {
         subMenu = [
            {
               icon: "✔️",
               name: "Yes",
               action: () => this.updateBooleanValue(this.columnToAction.field, true),
            },
            {
               icon: "❌",
               name: "No",
               action: () => this.updateBooleanValue(this.columnToAction.field, false),
            },
         ];
      } else if (this.columnToAction.colId == "CompetitorPlateRange") {
         subMenu = [];
         for (let i = 0; i < 5; i++) {
            // Using `let` instead of `var`
            subMenu.push({
               name: i.toString(),
               action: () => {
                  this.newNumberValue = i; // Each `i` is unique to its loop iteration
                  this.updateNumberValueForSelectedRows();
               },
            });
         }
      }

      return subMenu;
   }

   updateBooleanValue(field: string, setTrue: boolean) {
      let requests = [];
      this.selectedRowData.forEach((row) => {
         row[field] = setTrue;
         requests.push(this.service.saveMultipleSiteSettings(row));
      });
      forkJoin(requests).subscribe((res) => {
         this.service.constants.toastSuccess("Saved site settings");
         this.service.modalService.dismissAll();
         this.service.getSitesSettings();
      });
   }

   getActionForColDef(params) {
      // console.log(this.columnToAction.type, "this.columnToAction.type!");
      // console.log(this.columnToAction.colId, "this.columnToAction.colId!");

      if (this.columnToAction.colId === "CompetitorPlateRange") {
         return null;
      } else if (
         this.columnToAction.type === "currency" ||
         this.columnToAction.type === "currency2dp" ||
         this.columnToAction.type === "number1dp" ||
         this.columnToAction.colId === "LocalBargainsSearchRadius" ||
         this.columnToAction.colId === "MaximumOptOutDays" ||
         this.columnToAction.colId === "LocalBargainsMinRetailRating"
      ) {
         // Modal with single input

         this.newCurrencyValue = this.lastSelectedColumnValue;
         const modalRef = this.service.modalService.open(this.currencyModal, { size: "md", windowClass: "autoHeight" });
         setTimeout(() => {
            const currencyInput = document.getElementById("currencyInput") as HTMLInputElement;
            currencyInput.focus();
            currencyInput.select();
         }, 250);
         modalRef.result.then((result) => {});
      } else if (this.columnToAction.type === "number") {
         // Modal with single input

         this.newNumberValue = this.lastSelectedColumnValue;
         this.whenToActionTime = `${this.service.constants.clone(this.newNumberValue)}:00`;
         const modalRef = this.service.modalService.open(this.numberModal, { size: "md", windowClass: "autoHeight" });

         setTimeout(() => {
            const numberInput = document.getElementById("numberInput") as HTMLInputElement;
            numberInput.focus();
            numberInput.select();
         }, 250);

         modalRef.result.then((result) => {});
      } else if (
         this.columnToAction.colId === "PricingStrategyComment" ||
         this.columnToAction.colId === "BuyingStrategyComment" ||
         this.columnToAction.colId === "testStrategyName"
      ) {
         const modalRef = this.service.modalService.open(this.strategyModal, { size: "md", windowClass: "autoHeight" });

         modalRef.result.then((result) => {});
      }

      return null;
   }

   openModal(params: any) {
      this.service.selectedSite = params.data;
      // Store a snapshot of the site settings before opening the modal
      this.service.storeSiteSettingsSnapshot();
      const modalRef = this.service.modalService.open(SiteSettingsModalComponent, { keyboard: true, size: "md" });
      modalRef.result.then((result) => {
         // Modal was closed with a result (save button)
         // No need to do anything as changes were already saved
      }, (reason) => {
         // Modal was dismissed (close button or clicking outside)
         // No need to do anything as resetModal is called in the closeModal method
      });
   }

   updateCurrencyValueForSelectedRows() {
      let requests = [];

      this.selectedRowData.forEach((row) => {
         row[this.columnToAction.field] = this.newCurrencyValue;
         requests.push(this.service.saveMultipleSiteSettings(row));
      });

      forkJoin(requests).subscribe((res) => {
         this.service.constants.toastSuccess("Saved site settings");
         this.service.modalService.dismissAll();
         this.service.getSitesSettings();
      });
   }

   updateNumberValueForSelectedRows() {
      let requests = [];

      this.selectedRowData.forEach((row) => {
         row[this.columnToAction.field] = this.newNumberValue;
         requests.push(this.service.saveMultipleSiteSettings(row));
      });

      forkJoin(requests).subscribe((res) => {
         this.service.constants.toastSuccess("Saved site settings");
         this.service.modalService.dismissAll();
         this.service.getSitesSettings();
      });
   }

   convertWhenToActionTimeToInt() {
      const chosenTime: string = this.whenToActionTime.slice(0, this.whenToActionTime.length - 3);
      this.newNumberValue = parseInt(chosenTime);
   }

   chooseNewStrategyForSelectedSites(strategy: StrategyFull) {
      this.chosenNewStrategy = strategy;
   }

   chooseNewStrategyEffectiveDate(dateString: any) {
      this.chosenNewStrategyEffectiveDate = dateString;
   }

   applyNewStrategyForSelectedRows() {
      let requests = [];

      if (this.columnToAction.field === "strategyName") {
         this.selectedRowData.forEach((row) => {
            row.StrategySelectionRuleSetName = this.chosenNewStrategy.Name;
            row.StrategySelectionRuleSetId = this.chosenNewStrategy.StrategyId;
            requests.push(this.service.saveMultipleSiteSettings(row));
         });
      } else if (this.columnToAction.field == "buyingStrategyName") {
         this.selectedRowData.forEach((row) => {
            row.BuyingStrategySelectionRuleSetName = this.chosenNewStrategy.Name;
            row.BuyingStrategySelectionRuleSetId = this.chosenNewStrategy.StrategyId;
            requests.push(this.service.saveMultipleSiteSettings(row));
         });
      } else if (this.columnToAction.field == "testStrategyName") {
         this.selectedRowData.forEach((row) => {
            row.TestStrategySelectionRuleSetName = this.chosenNewStrategy.Name;
            row.TestStrategySelectionRuleSetId = this.chosenNewStrategy.StrategyId;
            requests.push(this.service.saveMultipleSiteSettings(row));
         });
      }

      forkJoin(requests).subscribe((res) => {
         this.service.constants.toastSuccess("Saved site settings");
         this.service.modalService.dismissAll();
         this.service.getSitesSettings();
         this.chosenNewStrategy = null;
      });

      // //apply strategy
      // if (this.service.gridApi) {
      //   this.service.gridApi.forEachLeafNode(node => {
      //     if (node.isSelected()) {
      //       let site: SiteSettings = node.data;
      //       site.NewStrategyId = this.chosenNewStrategy.Id;
      //       site.NewStrategyName = this.chosenNewStrategy.Name;
      //     }
      //   })
      // }

      // //apply date
      // if (this.service.gridApi) {
      //   this.service.gridApi.forEachLeafNode(node => {
      //     if (node.isSelected()) {
      //       let site: SiteSettings = node.data;
      //       site.NewStrategyEffectiveDate = this.chosenNewStrategyEffectiveDate
      //     }
      //   })
      // }
   }
}
