import { ColumnState } from "ag-grid-community";


export interface ValuationBatchResult {
    AgeBand: string
    BatchId: number;
    BatteryCapacityKWH: number;
    BatteryRangeMiles: number;
    BodyType: string;

    Colour: string;
    Condition: string;
    Co2EmissionGPKM: number;
    Cylinders: number;
    Derivative: string;
    Doors: number;
    Drivetrain: string;
    DriveType: string;
    EngineCapacityCC: number;
    EnginePowerBHP: number;
    FuelType: string;

    Gears: number;
    Generation: string;
    HasBeenValued: boolean;
    LastRunBy: string;
    LastRunDate: string; // Consider using DateTime
    LocationRank: number | null; // Nullable
    Make: string;
    Mileage: number;
    Model: string;
    PartEx: number;
    Reg: string;
    RegisteredDate: Date | string | null; // Consider using DateTime if the date format is consistent
    DaysToSellAtCurrentSelling: number | null; // Nullable
    RetailRating: number;
    RetailRatingBand: string
    RetailerId: number | null; // Nullable
    RetailerName: string;
    Retail: number;
    PrivateValue: number;
    Seats: number;
    Sector: string;
    SiteName: string;
    SiteId: number;
    StartStop: boolean | null;
    StrategyPrice: number | null; // Nullable
    TopSpeedMPH: number;
    TransmissionType: string;
    Trade: number;
    Trim: string;
    ValuationId: number;
    ValuationMktAvPartExAverageSpec: number | null;
    ValuationMktAvPartExThisVehicle: number | null;
    ValuationMktAvPrivateAverageSpec: number | null;
    ValuationMktAvPrivateThisVehicle: number | null;
    ValuationMktAvRetailAverageSpec: number | null;
    ValuationMktAvRetailThisVehicle: number | null;
    ValuationMktAvTradeAverageSpec: number | null;
    ValuationMktAvTradeThisVehicle: number | null;
    VehicleValuationBatch_Id: number;
    VehicleOwnership: string;
    VehicleType: string;
    Vin: string;

    ValueBand: string;

    VehicleExciseDutyWithoutSupplementGBP: number;
    ZeroToSixtyMPHSeconds: number;
    SIV: number | null;
    IsVatQualifying: boolean | null;
    Reference1: string;
    Reference2: string;
    Reference3: string;
    PriceScenario: number;

    CapValue: number | null;
    DateOfLastService: Date | string | null;
    EventDate: Date | string | null;
    EventType: string | null;
    Imported: boolean | null;
    InsuranceCat: string | null;
    Link: string | null;
    Location: string | null;
    LotNumber: string | null;
    MileageWarranty: boolean | null;
    MotExpiry: Date | string | null;
    Notes: string | null;
    NumberOfKeys: string | null;
    OnFinance: string | null;
    ReserveOrBuyItNow: number | null;
    Seller: string | null;
    ServiceHistory: string | null;
    Services: number | null;
    V5Status: string | null;

    // For ApplyPrice
    CurrentPP: number | null;
    LowestPP: number | null;
    SecondLowestPP: number | null;
    ThirdLowestPP: number | null;

    LowestPPRetailer: string;
    LowestPPVehicleReg: string;
    LowestPPMileage: number | null;

    SecondLowestPPRetailer: string;
    SecondLowestPPVehicleReg: string;
    SecondLowestPPMileage: number | null;

    ThirdLowestPPRetailer: string;
    ThirdLowestPPVehicleReg: string;
    ThirdLowestPPMileage: number | null;

    D90: number | null;
    D91: number | null;
    D92: number | null;
    D93: number | null;
    D94: number | null;
    D95: number | null;
    D96: number | null;
    D97: number | null;
    D98: number | null;
    D99: number | null;
    D100: number | null;


    P85: number | null;
    P86: number | null;
    P87: number | null;
    P88: number | null;
    P89: number | null;
    P90: number | null;
    P91: number | null;
    P92: number | null;
    P93: number | null;
    P94: number | null;
    P95: number | null;
    P96: number | null;
    P97: number | null;
    P98: number | null;
    P99: number | null;
    P100: number | null;

    CapValuation: number | null;

    CapNew: number | null;
    CapRetail: number | null;
    CapAverage: number | null;
    CapBelow: number | null;
    RecallStatus: string;

    isBestMove?: boolean;
}


export interface TableState {
    columnState: ColumnState[]
    filterModel: any,
    label: string;
}