﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using System.Linq;
using CPHI.Spark.Model;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using CPHI.Repository;
using Microsoft.EntityFrameworkCore.Storage;
using System.Runtime.CompilerServices;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.BusinessLogic.AutoPrice;
using System.Net.Http;
using CPHI.Spark.Model.Services;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
   public interface IStrategyService
   {
      Task CreateStrategyVersion(string name, int userId);
      Task DeleteStrategy(DealerGroupName dealerGroup, int strategyId, int userId);
      Task DeleteStrategyVersion(DealerGroupName dealerGroup, int strategyVersionId, int userId);
      Task<List<StrategyVersionVM>> GetAllPricingPolicies(DealerGroupName dealerGroup);
      Task<List<StrategyFull>> GetAllStrategies(DealerGroupName dealerGroup);
      Task<List<StrategyFieldName>> GetAllStrategyFieldNames();

      Task<StrategyVersionVM> GetStrategy(DealerGroupName dealerGroup, int strategyId);
      Task RecalculateTestStrategyImpacts(DealerGroupName userDealerGroupName);
      Task RecalculateTestStrategyDaysToSell(DealerGroupName userDealerGroupName);
      Task<int?> SaveStrategy(StrategyFull incomingStrategy, int userId, DealerGroupName userDealerGroupName);

      Task<int?> SaveStrategyVersion(StrategyVersionVM incomingStrategyVersion, int userId, DealerGroupName userDealerGroupName);
   }
   public class StrategyService : IStrategyService
   {
      private readonly string _connectionString;
      private readonly IAutoPriceCache autoPriceCache;
      private readonly HttpClient httpClient;
      private readonly IHttpClientFactory httpClientFactory;

      public StrategyService(IUserService userService, IConfiguration configurationIn, IAutoPriceCache autoPriceCacheIn, IHttpClientFactory httpClientFactory)

      {
         DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         _connectionString = configurationIn.GetConnectionString(dgName);
         autoPriceCache = autoPriceCacheIn;
         this.httpClient = httpClientFactory.CreateClient();
         this.httpClientFactory = httpClientFactory;
      }

      public async Task CreateStrategyVersion(string name, int userId)
      {

         //This is static list should be coming either from DB or a static list.
         Dictionary<string, List<string>> factorNamesWithItems = new Dictionary<string, List<string>>();

         StrategyVersion strategyVersion = new StrategyVersion()
         {
            Name = name,
            //IsActive = false,
            CreatedBy_Id = userId,
            LastUpdatedDate = DateTime.UtcNow,
            StrategyFactors = new List<StrategyFactor>()
         };




         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         await strategyDataAccess.CreateStrategyVersion(strategyVersion, userId);
      }



      //Strategy
      public async Task<List<StrategyVersionVM>> GetAllPricingPolicies(DealerGroupName dealerGroup)
      {
         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         return await strategyDataAccess.GetAllPricingPolicies(dealerGroup);
      }


      public async Task<List<StrategyFull>> GetAllStrategies(DealerGroupName dealerGroup)
      {
         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         return await strategyDataAccess.GetAllStrategies(dealerGroup);
      }
      public async Task<List<StrategyFieldName>> GetAllStrategyFieldNames()
      {
         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         return await strategyDataAccess.GetStrategyFieldNames();
      }


      public async Task RecalculateTestStrategyImpacts(DealerGroupName dealerGroupName)
      {

         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         CalculateStrategyPricesService calculateStrategyPricesService = new CalculateStrategyPricesService(_connectionString, httpClientFactory);

         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         var bandingsDict = ConstantsCache.ProvideBandingsDictionary(dealerGroupName);
         await calculateStrategyPricesService.CalculateStrategyPricesForTodayAdverts(dealerGroupName, apiKey, apiSecret, baseURL, null, true, bandingsDict);

         //trigger a cache refresh
         await autoPriceCache.RefreshAdvertsCache(dealerGroupName);
      }
      public async Task RecalculateTestStrategyDaysToSell(DealerGroupName dealerGroupName)
      {

         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         CalculateStrategyPricesService calculateStrategyPricesService = new CalculateStrategyPricesService(_connectionString, httpClientFactory);

         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         var bandingsDict = ConstantsCache.ProvideBandingsDictionary(dealerGroupName);
         await calculateStrategyPricesService.CalculateStrategyDaysToSellForTodayAdverts(dealerGroupName, apiKey, apiSecret, baseURL, null, true, bandingsDict);

         //trigger a cache refresh
         await autoPriceCache.RefreshAdvertsCache(dealerGroupName);
      }



      //Strategy
      public async Task<StrategyVersionVM> GetStrategy(DealerGroupName dealerGroup, int strategyId)
      {
         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         List<StrategyVersionVM> all = await strategyDataAccess.GetAllPricingPolicies(dealerGroup);
         StrategyVersionVM strategy = all.FirstOrDefault(x => x.Id == strategyId);

         foreach (var factor in strategy.StrategyFactors)
         {
            factor.StrategyFactorItems = factor.StrategyFactorItems.OrderBy(x => x.Label).ToList();
         }


         return strategy;
      }

      public async Task DeleteStrategy(DealerGroupName dealerGroup, int strategyId, int userId)
      {
         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         await strategyDataAccess.DeleteStrategy(dealerGroup, strategyId, userId);
      }

      public async Task DeleteStrategyVersion(DealerGroupName dealerGroup, int strategyVersionId, int userId)
      {
         var strategyDataAccess = new StrategyDataAccess(_connectionString);
         await strategyDataAccess.DeleteStrategyVersion(dealerGroup, strategyVersionId, userId);
      }



      public async Task<int?> SaveStrategy(StrategyFull incomingStrategy, int userId, DealerGroupName userDealerGroupName)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Create the execution strategy
            var strategy = db.Database.CreateExecutionStrategy();

            int? newStrategyId = null;

            await strategy.ExecuteAsync(async () =>
            {
               // Start a new transaction within the execution strategy
               using (var transaction = await db.Database.BeginTransactionAsync())
               {
                  try
                  {
                     if (incomingStrategy.StrategyId == null)
                     {
                        // We have to create a new one
                        newStrategyId = await CreateNewStrategyFull(incomingStrategy, userId, userDealerGroupName, db);
                     }
                     else
                     {
                        // Check if the rule set has been used before
                        var strategyDataAccess = new StrategyDataAccess(_connectionString);
                        bool ruleSetHasBeenUsedBefore = await strategyDataAccess.HasStrategySelectionRuleSetBeenUsedToGenerateStrategyPrices((int)incomingStrategy.StrategyId);

                        if (ruleSetHasBeenUsedBefore)
                        {
                           // Create a new strategy full and mark the old rule set as deprecated
                           newStrategyId = await CreateNewStrategyFull(incomingStrategy, userId, userDealerGroupName, db);
                           var oldRuleSet = await db.StrategySelectionRuleSets.FirstAsync(x => x.Id == incomingStrategy.StrategyId);
                           oldRuleSet.HasBeenDeprecated = true;
                           await db.SaveChangesAsync();
                        }
                        else
                        {
                           // Update the existing rule set
                           await UpdateExistingStrategySelectionRuleSet(incomingStrategy, userId, db);
                           newStrategyId = null;
                        }
                     }

                     // Commit the transaction
                     await transaction.CommitAsync();
                  }
                  catch (Exception ex)
                  {
                     // Roll back the transaction on error
                     await transaction.RollbackAsync();
                     throw new Exception("Save Strategy Version failed", ex);
                  }
               }
            });

            return newStrategyId;
         }
      }


      public async Task UpdateExistingStrategySelectionRuleSet(StrategyFull updatedStrategyFull, int userId, CPHIDbContext db)
      {
         try
         {
            // Retrieve the existing StrategySelectionRuleSet
            var existingRuleSet = await db.StrategySelectionRuleSets
                .Include(r => r.StrategySelectionRules)
                .ThenInclude(c => c.StrategySelectionCriterias)
                .ThenInclude(x => x.StrategyFieldName)
                .FirstOrDefaultAsync(r => r.Id == updatedStrategyFull.StrategyId);

            if (existingRuleSet == null)
            {
               throw new Exception("StrategySelectionRuleSet not found.");
            }

            // Update the properties of the existing RuleSet
            existingRuleSet.Name = updatedStrategyFull.Name;
            existingRuleSet.Comment = updatedStrategyFull.Comment;
            existingRuleSet.LastUpdatedBy_Id = userId;
            existingRuleSet.LastUpdatedDate = DateTime.UtcNow;
            existingRuleSet.CreatedDate = updatedStrategyFull.CreatedDate;
            existingRuleSet.DefaultStrategyVersion_Id = updatedStrategyFull.DefaultPricingPolicy.PricingPolicyId;

            // Track rules that were processed
            var processedRuleIds = new List<int>();

            // Process each StrategyRule in the updatedStrategyFull
            foreach (var updatedRule in updatedStrategyFull.StrategyRules)
            {
               StrategySelectionRule existingRule = ProcessThisRule(userId, db, existingRuleSet, processedRuleIds, updatedRule);
               ProcessCriteriasForThisRule(db, updatedRule, existingRule);
            }

            // Remove unprocessed rules (those that were deleted in the update)
            var rulesToRemove = existingRuleSet.StrategySelectionRules
                .Where(r => !processedRuleIds.Contains(r.Id))
                .ToList();

            db.StrategySelectionRules.RemoveRange(rulesToRemove);

            // Save changes and commit the transaction
            await db.SaveChangesAsync();
         }
         catch (Exception ex)
         {
            throw new Exception("Failed to update StrategySelectionRuleSet.", ex);
         }
      }

      private static StrategySelectionRule ProcessThisRule(int userId, CPHIDbContext db, StrategySelectionRuleSet existingRuleSet, List<int> processedRuleIds, StrategyRule updatedRule)
      {
         StrategySelectionRule existingRule;

         if (updatedRule.RuleId.HasValue)
         {
            existingRule = existingRuleSet.StrategySelectionRules.FirstOrDefault(r => r.Id == updatedRule.RuleId.Value);

            if (existingRule == null)
            {
               throw new Exception("StrategyRule not found.");
            }

            // Update existing rule
            existingRule.StrategyVersion_Id = updatedRule.PricingPolicyId;
            existingRule.LastUpdatedBy_Id = userId;
            existingRule.LastUpdatedDate = DateTime.UtcNow;
            existingRule.CreatedDate = DateTime.UtcNow;
         }
         else
         {
            // Create a new rule
            existingRule = new StrategySelectionRule
            {
               CreatedBy_Id = userId,
               LastUpdatedBy_Id = userId,
               StrategyVersion_Id = updatedRule.PricingPolicyId,
               StrategySelectionRuleSet_Id = existingRuleSet.Id,
               CreatedDate = DateTime.UtcNow,
               LastUpdatedDate = DateTime.UtcNow,
               StrategySelectionCriterias = new List<StrategySelectionCriteria>()
            };
            db.StrategySelectionRules.Add(existingRule);
            existingRuleSet.StrategySelectionRules.Add(existingRule);
         }

         processedRuleIds.Add(existingRule.Id);
         return existingRule;
      }

      private static void ProcessCriteriasForThisRule(CPHIDbContext db, StrategyRule updatedRule, StrategySelectionRule existingRule)
      {
         // Track criteria that were processed
         var processedCriteriaIds = new List<int>();

         // Process each StrategyCriteria
         foreach (var updatedCriteria in updatedRule.Criterias)
         {
            StrategySelectionCriteria existingCriteria = existingRule.StrategySelectionCriterias
                    .FirstOrDefault(c => c.StrategyFieldName.FieldName == updatedCriteria.FieldNameLabel);

            if (existingCriteria != null)
            {

               // Update existing criteria
               existingCriteria.Value = updatedCriteria.CriteriaValue;
               existingCriteria.StrategyFieldName_Id = db.StrategyFieldNames
                   .First(f => f.Label == updatedCriteria.FieldNameLabel && f.ComparatorType == updatedCriteria.ComparatorType)
                   .Id;
            }
            else
            {
               // Create a new criteria
               existingCriteria = new StrategySelectionCriteria
               {
                  StrategySelectionRule_Id = existingRule.Id,
                  Value = updatedCriteria.CriteriaValue,
                  StrategyFieldName_Id = db.StrategyFieldNames
                       .First(f => f.Label == updatedCriteria.FieldNameLabel && f.ComparatorType == updatedCriteria.ComparatorType)
                       .Id
               };
               db.StrategySelectionCriterias.Add(existingCriteria);
               existingRule.StrategySelectionCriterias.Add(existingCriteria);
            }

            // Track the criteria as processed
            processedCriteriaIds.Add(existingCriteria.Id);
         }

         // Remove unprocessed criteria (those that were deleted in the update)
         var criteriaToRemove = existingRule.StrategySelectionCriterias
             .Where(c => !processedCriteriaIds.Contains(c.Id))
             .ToList();

         db.StrategySelectionCriterias.RemoveRange(criteriaToRemove);
      }

      private async Task<int> CreateNewStrategyFull(StrategyFull incomingStrategy, int userId, DealerGroupName userDealerGroupName, CPHIDbContext db)
      {
         //just make a new one and bits below
         var strategyDataAccess = new StrategyDataAccess(_connectionString);

         // Find the highest uniqueId for this dealergroup, and increment it
         var totalStrategies = await db.StrategySelectionRuleSets
               .Where(x => x.CreatedBy.DealerGroup_Id == (int)userDealerGroupName)
               .CountAsync();

         var uniqueIdForDg = (uint)totalStrategies + 1;

         //make StrategySelectionRuleSet
         DateTime createdDate = incomingStrategy.StrategyId != null ? incomingStrategy.CreatedDate : DateTime.UtcNow;
         StrategySelectionRuleSet newRuleSet = new StrategySelectionRuleSet(
           userId,
           incomingStrategy.Comment,
           (int)incomingStrategy.DefaultPricingPolicy.PricingPolicyId,
           createdDate,
           incomingStrategy.Name,
           uniqueIdForDg);

         await db.StrategySelectionRuleSets.AddAsync(newRuleSet);
         await db.SaveChangesAsync(); //to generate the id for the ruleset

         int newStrategyId = newRuleSet.Id;

         //make each rule within a loop, within this make criteria
         //get all the StrategyFieldNames from the db
         var fieldNames = await strategyDataAccess.GetStrategyFieldNames();
         foreach (var rule in incomingStrategy.StrategyRules)
         {
            //make the new rule
            StrategySelectionRule newRule = new StrategySelectionRule(userId, rule.PricingPolicyId, newRuleSet.Id, createdDate);
            await db.StrategySelectionRules.AddAsync(newRule);
            await db.SaveChangesAsync(); //to generate the Id

            //make the new criterias
            foreach (var criteria in rule.Criterias)
            {
               var fieldName = fieldNames.First(x => x.Label == criteria.FieldNameLabel);
               StrategySelectionCriteria newCriteria = new StrategySelectionCriteria(fieldName.Id, newRule.Id, criteria.CriteriaValue);
               await db.StrategySelectionCriterias.AddAsync(newCriteria);
            }
            await db.SaveChangesAsync(); //to save the criterias
         }

         return newStrategyId;
      }

      public async Task<int?> SaveStrategyVersion(StrategyVersionVM incomingStrategyVersion, int userId, DealerGroupName userDealerGroupName)
      {
         //This approach works
         using (var db = new CPHIDbContext(_connectionString))
         {
            var transaction = await db.Database.BeginTransactionAsync();
            try
            {

               int? newStrategyId = null;


               var strategyDataAccess = new StrategyDataAccess(_connectionString); // Adjust StrategyDataAccess to accept DbContext
               StrategyVersion strategyVersionToUse = null;


               if (incomingStrategyVersion.Id == null || (incomingStrategyVersion.SaveAsNew.HasValue && incomingStrategyVersion.SaveAsNew.Value))
               {
                  newStrategyId = await CreateNewStrategy(incomingStrategyVersion, strategyDataAccess, userId, userId);
               }
               else
               {
                  //might still need to make new one if the old one has been used
                  int incomingId = (int)incomingStrategyVersion.Id;
                  StrategyVersion existingStrategyVersion = await strategyDataAccess.GetStrategyVersion(incomingId, userDealerGroupName);
                  bool mustCreateNewStrategyVersion = await DetermineIfNeedToCreateNewStrategy(incomingStrategyVersion, strategyDataAccess, existingStrategyVersion);
                  if (mustCreateNewStrategyVersion)
                  {
                     newStrategyId = await CreateNewStrategy(incomingStrategyVersion, strategyDataAccess, userId, userId);
                  }
                  else
                  {
                     //update existing
                     List<int> existingFactorIdsDealtWith = new List<int>();
                     foreach (var incomingFactor in incomingStrategyVersion.StrategyFactors)
                     {
                        if (incomingFactor.Name == StrategyFactorName.Mileage)
                        {
                           incomingFactor.StrategyFactorItems.ForEach((x) =>
                           {
                              x.Label = x.Label.Replace(" ", "").Replace(",", "");
                           });
                        }

                        var existingFactor = existingStrategyVersion.StrategyFactors.FirstOrDefault(x => x.Name == incomingFactor.Name);
                        if (existingFactor == null)
                        {
                           await CreateNewStrategyFactorAndItems(strategyDataAccess, existingStrategyVersion, incomingFactor, userId);
                        }
                        else
                        {
                           await UpdateExistingStrategyFactor(strategyDataAccess, existingFactorIdsDealtWith, incomingFactor, existingFactor, userId);
                        }
                     }

                     List<StrategyFactor> remainingExistingFactors = existingStrategyVersion.StrategyFactors.Where(x => !existingFactorIdsDealtWith.Contains(x.Id)).ToList();
                     if (remainingExistingFactors.Count > 0)
                     {
                        await strategyDataAccess.RemoveStrategyFactors(remainingExistingFactors, userId);
                     }

                     await strategyDataAccess.UpdateStrategyVersion(incomingStrategyVersion, userId);
                  }
               }

               await transaction.CommitAsync(); // Commit the transaction

               return newStrategyId;
            }
            catch (Exception ex)
            {
               await transaction.RollbackAsync(); // Roll back the transaction on error
               throw new Exception("Save Pricing Policy failed", ex); // Include the original exception as the inner exception
            }
         }
      }


      private static async Task UpdateExistingStrategyFactor(StrategyDataAccess strategyDataAccess, List<int> existingFactorIdsDealtWith, StrategyFactorVM incomingFactor, StrategyFactor existingFactor, int userId)
      {
         existingFactorIdsDealtWith.Add(existingFactor.Id);
         List<StrategyFactorItem> newFactorItems = new List<StrategyFactorItem>();
         List<StrategyFactorItem> changedFactorItems = new List<StrategyFactorItem>();

         foreach (var incomingFactorItem in incomingFactor.StrategyFactorItems)
         {
            var existingFactorItem = existingFactor.StrategyFactorItems.FirstOrDefault(x => x.Label == incomingFactorItem.Label);
            if (existingFactorItem == null)
            {
               newFactorItems.Add(new StrategyFactorItem()
               {
                  StrategyFactor_Id = existingFactor.Id,
                  Label = incomingFactorItem.Label,
                  Value = incomingFactorItem.Value,
                  ValueAmount = incomingFactorItem.ValueAmount,
                  Comment = incomingFactorItem.Comment,
               });
            }
            else
            {
               existingFactorItem.Value = incomingFactorItem.Value;
               existingFactorItem.ValueAmount = incomingFactorItem.ValueAmount;
               existingFactorItem.Comment = incomingFactorItem.Comment;
               changedFactorItems.Add(existingFactorItem);

            }
         }

         //also identify any removals
         List<StrategyFactorItem> removedFactorItems = new();
         foreach (var existingFactorItem in existingFactor.StrategyFactorItems)
         {
            var incomingFactorItem = incomingFactor.StrategyFactorItems.FirstOrDefault(x => x.Label == existingFactorItem.Label);
            if (incomingFactorItem == null)
            {
               removedFactorItems.Add(existingFactorItem);
            }
         }

         await strategyDataAccess.CreateNewFactorItems(newFactorItems, userId);
         await strategyDataAccess.UpdateFactorItems(changedFactorItems, userId);
         await strategyDataAccess.DeleteFactorItems(removedFactorItems, userId);
      }

      private static async Task CreateNewStrategyFactorAndItems(StrategyDataAccess strategyDataAccess, StrategyVersion existingStrategyVersion, StrategyFactorVM incomingFactor, int userId)
      {
         var newFactor = new StrategyFactor()
         {
            StrategyVersion_Id = existingStrategyVersion.Id,
            Name = incomingFactor.Name,
         };
         await strategyDataAccess.CreateNewFactor(newFactor, userId);
         List<StrategyFactorItem> newFactorItems = new List<StrategyFactorItem>();
         foreach (var item in incomingFactor.StrategyFactorItems)
         {
            newFactorItems.Add(new StrategyFactorItem()
            {
               StrategyFactor_Id = newFactor.Id,
               Label = item.Label,
               Comment = item.Comment,
               Value = item.Value,
               ValueAmount = item.ValueAmount
            });
         }
         await strategyDataAccess.CreateNewFactorItems(newFactorItems, userId);
      }

      private static async Task<int> CreateNewStrategy(StrategyVersionVM incomingStrategyVersion, StrategyDataAccess strategyDataAccess, int createdById, int userId)
      {
         StrategyVersion newStrategy = new StrategyVersion()
         {
            CreatedBy_Id = createdById,
            LastUpdatedBy_Id = userId,
            LastUpdatedDate = DateTime.Now,
            Name = incomingStrategyVersion.Name,
            Comment = incomingStrategyVersion.Comment
         };
         await strategyDataAccess.CreateStrategyVersion(newStrategy, userId);

         ///create factors
         List<StrategyFactor> newFactors = new List<StrategyFactor>();
         foreach (var factor in incomingStrategyVersion.StrategyFactors)
         {
            newFactors.Add(new StrategyFactor()
            {
               StrategyVersion_Id = newStrategy.Id,
               Name = factor.Name,
            });
         }

         await strategyDataAccess.SaveStrategyFactors(newFactors, userId);

         ///create factorItems
         List<StrategyFactorItem> newFactorItems = new List<StrategyFactorItem>();
         foreach (var factor in incomingStrategyVersion.StrategyFactors)
         {
            var newFactor = newFactors.First(x => x.Name == factor.Name);
            foreach (var item in factor.StrategyFactorItems)
            {
               newFactorItems.Add(new StrategyFactorItem()
               {
                  StrategyFactor_Id = newFactor.Id,
                  Label = item.Label,
                  Comment = item.Comment,
                  Value = item.Value,
                  ValueAmount = item.ValueAmount,
               });
            }
         }
         await strategyDataAccess.CreateNewFactorItems(newFactorItems, userId);

         //int displayId = displayIdToUse.HasValue ? (int)displayIdToUse : newStrategy.Id;
         //await strategyDataAccess.SetStrategyVersionDisplayId(newStrategy.Id, displayId);

         return newStrategy.Id;
      }




      private static async Task<bool> DetermineIfNeedToCreateNewStrategy(StrategyVersionVM incomingStrategyVersion, StrategyDataAccess strategyDataAccess, StrategyVersion existingStrategyVersion)
      {
         bool needToCreateNewStrategy = false;
         int i = 0;
         while (i < existingStrategyVersion.StrategyFactors.Count && !needToCreateNewStrategy)
         {
            var existingFactor = existingStrategyVersion.StrategyFactors.ToList()[i];
            var incomingFactor = incomingStrategyVersion.StrategyFactors.FirstOrDefault(x => x.Name == existingFactor.Name);
            bool existingFactorHasBeenUsed = await strategyDataAccess.HasStrategyFactorBeenUsedToGenerateStrategyPrices((int)existingFactor.Id);

            if (existingFactorHasBeenUsed)
            {
               if (incomingFactor == null)
               {
                  ///looks like we have removed a factor that has been used, this won't work
                  needToCreateNewStrategy = true;
               }
               else
               {
                  ///we still have an incoming factor BUT now have to check if any item has changed
                  int j = 0;
                  while (j < existingFactor.StrategyFactorItems.Count && !needToCreateNewStrategy)
                  {
                     var existingFactorItem = existingFactor.StrategyFactorItems.ToList()[j];
                     var incomingFactorItem = incomingFactor.StrategyFactorItems.FirstOrDefault(x => x.Label == existingFactorItem.Label);
                     bool existingFactorItemHasBeenUsed = await strategyDataAccess.HasStrategyFactorItemBeenUsedToGenerateStrategyPrices(existingFactorItem.Id);

                     if (existingFactorItemHasBeenUsed)
                     {
                        if (incomingFactorItem == null)
                        {
                           ///looks like we have removed a factor item that has been used, this won't work
                           needToCreateNewStrategy = true;
                        }
                        else
                        {
                           ///we still have an incoming factor item BUT now have to check if the value has changed
                           if (existingFactorItem.Value != incomingFactorItem.Value)
                           {
                              needToCreateNewStrategy = true;
                           }
                        }
                     }
                     j++;
                  }
               }
            }
            i++;
         }

         return needToCreateNewStrategy;
      }


      //public async Task SaveStrategyFactors(List<StrategyFactorVM> strategyFactorVMs, int strategyVersionId, int userId, DealerGroupName dealerGroup)
      //{

      //    //Create Copy of existing strategy
      //    var strategyDataAccess = new StrategyDataAccess(_connectionString);
      //    var copyOfStartegyVersion = (await strategyDataAccess.GetAllStrategies(dealerGroup)).FirstOrDefault(s => s.Id == strategyVersionId);

      //    StrategyVersion newStrategyVersion = new StrategyVersion()
      //    {
      //        Name = copyOfStartegyVersion.Name,
      //        Comment = copyOfStartegyVersion.Comment,
      //        CreatedBy_Id = userId,
      //        CreatedDate = DateTime.UtcNow,
      //        //IsActive = false,
      //        StrategyFactors = new List<StrategyFactor>()
      //    };


      //    //Add Factors and items
      //    //Full copy gets created
      //    foreach (var item in strategyFactorVMs)
      //    {
      //        var newSF = new StrategyFactor()
      //        {
      //            Name = item.Name,
      //            StrategyFactorItems = new List<StrategyFactorItem>()
      //        };

      //        foreach (var sfi in item.StrategyFactorItems)
      //        {
      //            var newSFI = new StrategyFactorItem()
      //            {
      //                Comment = sfi.Comment,
      //                Label = sfi.Label,
      //                Value = sfi.Value,
      //            };
      //            newSF.StrategyFactorItems.Add(newSFI);
      //        }

      //        newStrategyVersion.StrategyFactors.Add(newSF);
      //    }


      //    await strategyDataAccess.SaveStrategyVersionIncludingFactorsItems(newStrategyVersion, strategyVersionId);
      //}


   }
}