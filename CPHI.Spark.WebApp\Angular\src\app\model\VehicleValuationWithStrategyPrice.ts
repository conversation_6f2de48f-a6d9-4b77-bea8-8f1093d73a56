export interface VehicleValuationWithStrategyPrice {

   Id: number;

   // Regular props
   VehicleReg: string;
   FirstRegistered: Date | string | null;
   Mileage: number;
   Condition: string;
   DerivativeId: string;
   ValuationMktAvRetailAverageSpec: number;
   ValuationMktAvTradeAverageSpec: number;
   ValuationMktAvPartExAverageSpec: number;
   ValuationMktAvPrivateAverageSpec: number;

   ValuationMktAvRetailThisVehicle: number;
   ValuationMktAvTradeThisVehicle: number;
   ValuationMktAvPartExThisVehicle: number;
   ValuationMktAvPrivateThisVehicle: number;
   HasBeenValued: boolean;


   // all other properties
   OwnershipCondition: string;
   Vin: string;
   Make: string;
   Model: string;
   Generation: string;
   Derivative: string;
   VehicleType: string;
   Trim: string;
   BodyType: string;
   FuelType: string;
   TransmissionType: string;
   Drivetrain: string;
   Seats: number | null;
   Doors: number | null;
   Cylinders: number | null;
   EngineTorqueNM: number | null;
   Co2EmissionGPKM: number | null;
   TopSpeedMPH: number | null;
   ZeroToSixtyMPHSeconds: number | null;
   EngineCapacityCC: number | null;
   EnginePowerBHP: number | null;
   Owners: number | null;
   Colour: string;
   Gears: number | null;
   StartStop: boolean | null;
   BatteryRangeMiles: number | null;
   BatteryCapacityKWH: number | null;
   DriveType: string;
   VehicleExciseDutyWithoutSupplementGBP: number | null;
   sector: string;
   SIV: number | null;
   IsVatQualifying: boolean | null;
   Reference1: string;
   Reference2: string;
   Reference3: string;
   CurrentRetailPrice: number | null;
   CAPValuation: number | null;

   // Lowest competitor
   LowestPPPrice: number | null;
   LowestPPRetailer: string;
   LowestPPVehicleReg: string;
   LowestPPMileage: number | null;
   LowestPPValuation: number | null;

   // SecondLowestCompetitor
   SecondLowestPPPrice: number | null;
   SecondLowestPPRetailer: string;
   SecondLowestPPVehicleReg: string;
   SecondLowestPPMileage: number | null;
   SecondLowestPPValuation: number | null;

   // ThirdLowestCompetitor
   ThirdLowestPPPrice: number | null;
   ThirdLowestPPRetailer: string;
   ThirdLowestPPVehicleReg: string;
   ThirdLowestPPMileage: number | null;
   ThirdLowestPPValuation: number | null;

   IsSpecKnown: boolean;
   RetailRating: number | null;

   ValuationMktAvTradeExVatThisVehicle: number;
   ValuationMktAvRetailExVatAverageSpec: number;
   ValuationMktAvRetailExVatThisVehicle: number;
   ValuationMktAvTradeExVatAverageSpec: number;

   EventType: string | null;
   EventDate: string | null;
   Location: string | null;
   LotNumber: string | null;
   Seller: string | null;
   Link: string | null;
   V5Status: string | null;
   MotExpiry: string | null;
   MileageWarranty: boolean | null;
   ServiceHistory: string | null;
   Services: number | null;
   DateOfLastService: string | null;
   InsuranceCat: string | null;
   NumberOfKeys: string | null;
   OnFinance: boolean | null;
   Imported: boolean | null;
   Notes: string | null;
   CapValue: number | null;
   ReserveOrBuyItNow: number | null;

   // Costing
   Sales: number | null;
   Valet: number | null;
   SpareKey: number | null;
   MOT: number | null;
   MOTAdvisory: number | null;
   Servicing: number | null;
   Paint: number | null;
   Tyres: number | null;
   Warranty: number | null;
   Parts: number | null;
   AdditionalMech: number | null;
   Fee: number | null;
   Delivery: number | null;
   Other: number | null;
   Cost: number | null;
   Profit: number | null;
   VatCost: number | null;
   Valuation: number | null;
   StrategyPrice: number | null;
}
