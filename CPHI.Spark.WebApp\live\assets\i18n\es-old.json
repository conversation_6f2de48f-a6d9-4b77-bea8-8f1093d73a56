{"dashboadName": "Test ES", "ROOT": {"AccountSettings": "Configuraciones de la Cuenta", "Common": {"Achievement": "Logro", "Actual": "Real", "AddOnCount": "Recuento de complementos", "AddOnProfit": "Benef<PERSON>o <PERSON>", "AddOns": "Complementos", "Aftersales": "Después de las Ventas", "All": "Todos", "AllAges": "Todas las Edades", "Anytime": "Alguno", "AreYouSure": "¿Estás seguro?", "Between": "<PERSON><PERSON>", "Bonuses": "Bonificaciones", "Cancel": "<PERSON><PERSON><PERSON>", "Clear": "Limpiar", "Close": "<PERSON><PERSON><PERSON>", "Colour": "Color", "Comments": "Comentarios", "Commission": "Comisión", "CoreUsed": "<PERSON><PERSON><PERSON><PERSON> utiliza<PERSON>", "CoS": "CoS", "Cost": "Costo", "Cumulative": "Acumulativo", "Custom": "Personalizado", "Customer": "Cliente", "Daily": "Diario", "Date": "<PERSON><PERSON>", "Dates": "<PERSON><PERSON><PERSON>", "DayLower": "día", "Days": "<PERSON><PERSON>", "DaysLower": "dias", "Deal": "trato", "Deals": "<PERSON><PERSON><PERSON>", "Debts": "<PERSON><PERSON><PERSON>", "DeliveryDate": "<PERSON><PERSON>", "Demo": "Manifestación", "Description": "Descripción", "Detail": "Detalle", "Discount": "Descuento", "Done": "<PERSON><PERSON>", "Error": "Error", "ExcludeLateCosts": "Excluir Costos Tardíos", "ExcludeOrders": "Excluir Pedidos", "Finance": "Finanzas", "FinanceProfit": "<PERSON><PERSON><PERSON><PERSON>", "Fleet": "<PERSON><PERSON><PERSON>", "Franchise": "Fran<PERSON>cia", "Franchises": "Fran<PERSON><PERSON><PERSON>", "Friday": "Viernes", "FullMonthTarget": "Objetivo de mes Completo", "Gap": "Brecha", "GettingDeals": "Obtener Ofertas...", "GP": "GP", "GPU": "GPU", "Id": "Id", "IncludeLateCosts": "Incluir Costos Tardíos", "IncludeOrders": "Incluir Pedidos", "LastWeek": "<PERSON><PERSON>", "Make": "<PERSON><PERSON>", "Margin": "Margen", "Metal": "Metal", "MetalProfit": "Beneficio del Metal", "Model": "<PERSON><PERSON>", "ModelYear": "<PERSON><PERSON>", "Monday": "<PERSON><PERSON>", "Month": "<PERSON><PERSON>", "MonthTarget": "Objetivo Mensual", "Name": "Nombre", "Net": "Neto", "New": "Nuevo", "NextWeek": "La Semana Próxima", "No": "Sin", "NoFranchises": "Sin franquicias", "NoLower": "sin", "NoOrderType": "Sin tipo de orden", "NoVehicleType": "Sin tipo de vehículo", "Now": "<PERSON><PERSON>", "Of": "de", "Ok": "Ok", "OnlyLateCosts": "Solo Costos Atrasados", "OnlyOrders": "Solo Pedidos", "Order": "pedido", "OrderRate": "Tasa de pedido", "Orders": "pedidos", "OrderType": "<PERSON><PERSON><PERSON> Orden", "Other": "<PERSON><PERSON>", "OtherProfit": "<PERSON><PERSON>", "Over": "Sobre", "PerUnit": "Por Unidad", "Products": "Productos", "Profit": "Ben<PERSON><PERSON><PERSON>", "Reconnect": "Reconectar", "Refresh": "Actualizar", "Reg": "Reg", "Registered": "Registrado", "Relogin": "Acceso", "Required": "Requerido", "RestartingMessage": "Reiniciando Spark...", "Sale": "<PERSON><PERSON><PERSON>", "Sales": "Ventas", "SalesExec": "Ejecutivo de Ventas", "Saturday": "Sábado", "Search": "Buscar", "ServicePlan": "Plan de Servicio", "Site": "Sitio", "Sites": "Sit<PERSON>", "SitesOverview": "Sit<PERSON>", "SortBy": "Ordenar Por", "StepUp": "Aumentar", "StockNumber": "Número de Inventario", "Summary": "Resumen", "Sunday": "Domingo", "Tactical": "Táctica", "Target": "Objetivo", "ThisWeek": "<PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Today": "Hoy", "ToGo": "<PERSON>r", "Tomorrow": "<PERSON><PERSON><PERSON>", "Total": "Total", "TotalOf": "Total de", "TotalProfit": "Beneficio Total", "TotalSite": "Sitio Total", "TotalValue": "Valor Total", "Trade": "<PERSON><PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Unit": "Unidad", "Units": "Unidades", "Used": "<PERSON><PERSON>", "Variant": "<PERSON><PERSON><PERSON>", "VariantClass": "<PERSON><PERSON><PERSON>", "Vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VehicleAge": "Le Edad", "VehicleClass": "Clase de Vehículo", "Vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VehicleType": "Tipo de vehículo", "vsBudget": "vs Presupuesto", "vsLastMonth": "vs El mes Pasado", "vsLastYear": "vs el año Pasado", "vsThisMonth": "vs Este Mes", "Warranty": "Garantía", "Wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Week": "Se<PERSON>", "WelcomeMessage": "A partir de Spark...", "WheelGuard": "WheelGuard", "Wips": "WIPs", "With": "With", "Year": "<PERSON><PERSON>", "Yesterday": "Ayer"}, "Dashboard": {"ActivityLevels": {"Over1Day": "Más de 1 día", "Over30Days": "Más de 30 días", "Over60Days": "Más de 60 días", "OverAWeek": "Más de una semana", "Overdues": "Sobrecostes", "Title": "Niveles de actividad c/c ", "TotalOverdue": "Nuevos pedidos realizados"}, "AgedWips": "Envejecido WIPs", "CitNow": {"ofSalesEnquiries": "de consultas de ventas"}, "Debtors": {"BonusesAging": "Bonificaciones de Envejecimiento", "DebtsAging": "Envejecimiento de las Deudas", "OfWhichOver30Days": "De los cuales: más de 30 días", "OfWhichOver60Days": "De los cuales: más de 60 días", "Title": "<PERSON><PERSON><PERSON>", "TotalBonuses": "Bonificaciones totales", "TotalDebts": "Deudas Totales", "TotalWips": "Wips Total", "WipsAging": "Wips Envejecimiento"}, "Donut": {"FleetPerformanceForTheMonth": "Rendimiento de la flota del mes", "NewPerformanceForTheMonth": "Nuevo rendimiento para el mes", "UsedPerformanceForTheMonth": "Rendimiento de los usados del mes"}, "Evhc": {"RedWork": "Trabajo rojo", "Title": "Verificación electrónica del estado del vehículo"}, "FinanceAddOnPerformance": "Financiación Adicional Rendimiento", "FinanceAddons": {"Title": "Complementos de Finanzas"}, "FleetDealsByType": "Ofertas de flotas por tipo", "NewDealsBreakdown": "Desglose de nuevas of<PERSON>", "NoAgedWips": "Sin Envejecido WIPs", "PartsSales": {"Title": "Venta de Repuestos"}, "PartsSalesVsTarget": "Ventas de piezas frente a Destino", "PartsStock": {"OfTotalStock": "Del Stock Total", "OverageStock": "Cobertura de excedentes", "StockCover": "Cobertura de existencias", "Title": "Stock de piezas", "TotalValue": "Valor total"}, "PartsStockValue": "Valor de existencias de Piezas", "Registrations": {"Title": "Inscripciones"}, "RegistrationsDacia": "Matriculaciones Dacia", "RegistrationsRenault": "Matriculaciones Renault", "SalesmanEfficiency": {"Title": "Eficiencia del Vendedor"}, "SalesPerformance": {"ExcludingTradeUnits": "Excluyendo las unidades comerciales", "ForDeliveryInMonth": "Para entrega en el mes de", "IncludingTradeUnits": "Incluidas las unidades comerciales", "OrderRateAndProjection": "Ratio de pedidos y proyección", "ProjectedFinish": "Acabado Proyectado", "ProjectedVs": "Vs Proyectadas", "Title": "Rendimiento de la sitio", "TotalVehicles": "<PERSON>eh<PERSON><PERSON>los <PERSON>", "UnitsVsTarget": "Unidades frente a objetivo", "VehiclesOrdered": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ServiceBookings": {"Title": "Reservas de Servicios"}, "ServiceSales": {"SalesPerDay": "Ventas por Día", "SalesVsTarget": "Ventas Vs Target", "Title": "Ventas de Servicios", "WorkInProgress": "Trabajo en Progreso"}, "ServiceSalesVsTarget": "Ventas de servicios vs Objetivo", "SiteCompare": "Concesiones Comparación", "SitePerformanceLeague": {"FleetUnits": "Unidades de Flota", "NewMargin": "Nuevo Margen", "NewUnits": "Nuevas Unidades", "PartsSales": "Venta de Repuestos", "ServiceSales": "Ventas de Servicios", "Title": "Liga de Rendimiento del Sitio", "UsedMargin": "<PERSON><PERSON>", "UsedUnits": "Unidades Usadas"}, "StockOverage": {"NewVehiclesGreaterThan": "Vehículos Nuevos > ", "TradeVehiclesGreaterThan": "<PERSON><PERSON><PERSON><PERSON><PERSON> comerciales > ", "UsedVehiclesGreaterThan": "Vehículos de ocasión > "}, "StockReport": {"AgedOver": "Envejecido", "AsAt": "Como en", "BranchDays": "Días de sucursal", "GroupDays": "Días de grupo", "Title": "Informe de existencias", "UsingDaysAtBranch": "Uso de días en la sucursal", "UsingGroupDays": "Usando días de grupo"}, "ThisWeeksOrders": {"FleetOrdersTaken": "Pedidos de flota realizados", "NewOrdersTaken": "Nuevos pedidos realizados", "UsedOrdersTaken": "Pedidos de segunda mano"}, "Title": "<PERSON><PERSON>", "UsedDealsByType": "Ofertas de segunda mano por tipo", "UsedStockHealth": "Stock Usado Salud", "UsedStockMerchandising": "Ofertas de Stock Usado", "VoC": {"Title": "Voz de la Cliente"}, "WipReport": {"Account": "C<PERSON><PERSON>", "Age": "La edad", "Ageing": "Envejecimiento", "AsAtMonthEnd": "Al: <PERSON> de <PERSON>s", "AsAtNow": "Como en: Ahora", "BookingStatus": "Estado de la Reservación", "Created": "<PERSON><PERSON><PERSON>", "Department": "Departamento", "DueIn": "Debido En", "MonthEnd": "Fin de Mes", "Notes": "Notas", "Provision": "Provisión", "Title": "Informe WIP", "WipNumber": "Número WIP"}, "WithPrepCost": "Con coste de preparación"}, "DealDetails": {"Accessories": "Accesorios", "AccountingDate": "Fecha de contabilización", "BodyPrep": "Preparación de la carrocería", "BrokerCost": "Coste del corredor", "CosmeticInsurance": "<PERSON><PERSON><PERSON>", "DealRemoved": "Acuerdo retirado", "Delivery": "Entrega", "FactoryBonus": "Bonificación de fábrica", "FinanceAddOnProfit": "Financiación y beneficios adicionales", "FinanceCo": "Co. de Financiación", "FinanceCommission": "Comisión de financiación", "FinanceSubsidy": "Subvención financiera", "Fuel": "Combustible", "GapInsurance": "<PERSON><PERSON><PERSON>", "IntroCommission": "Comisión de ventas", "InvoiceDate": "<PERSON><PERSON> la factura", "IsDelivered": "¿Se entrega?", "IsLateCost": "¿Coste de retraso?", "IsOnFinance": "¿Está financiado?", "MechanicalPrep": "Preparación Mecánica", "OrderDate": "<PERSON><PERSON>edido", "PaintProtection": "Protección de pintura", "PartExchange": "Intercambio de piezas", "Pdi": "PDI", "ProPlusCommision": "Comisión Pro Plus", "RciFinanceCommission": "Comisión de Financiación RCI", "RegBonus": "Bono Reg", "RegisteredDate": "<PERSON>cha de registro", "SelectCommission": "Comisión Select", "StandardsCommission": "Comisión Estándar", "StockDate": "Fecha de la acción", "TinProfit": "Beneficio <PERSON>", "Title": "Detalles de la operación", "TyreAlloyInsurance": "Seguro de aleación de neumáticos", "TyreInsurance": "<PERSON><PERSON><PERSON> de Neumáticos", "Variant": "<PERSON><PERSON><PERSON>", "VariantText": "Texto variante"}, "DealsDoneThisMonth": {"BroughtIn": "<PERSON><PERSON><PERSON><PERSON>", "Mtd": "<PERSON><PERSON> Has<PERSON>", "Title": "Facturas Para Este Mes"}, "DealsDoneThisWeek": {"FuelSale": "Venta de Combustible", "Title": "Facturas <PERSON>"}, "Debts": {"AgedDebtsOn": "Deudas envejecidas en", "AsAt": "Como en", "DocDate": "<PERSON><PERSON> doc", "DueDate": "<PERSON><PERSON>nc<PERSON>o", "MonthEnd": "Fin de mes"}, "DisconnectedMessage": "Desconectado de la aplicación. Intente volver a conectarse, si eso falla, intente volver a iniciar sesión.", "FamilyPicker": {"AllFamilyCodes": "Todos los códigos de familia", "FamilyCodes": "Códigos de Familia", "NoFamilyCodes": "Sin códigos de familia"}, "FinanceAddons": {"Title": "Complementos de Finanzas"}, "HandoverDiary": {"Handover": "traspaso", "Handovers": "traspasos", "Title": "Diario de Traspaso"}, "LoadingPage": "Cargando <PERSON>...", "ManualReloadMessage": "Spark se actualiza automáticamente con los datos más recientes a medida que cambia de página, pero puede hacer clic aquí para volver a cargar manualmente todos los datos del servidor.", "Orderbook": {"Del": "Entrega", "IsDelivered": "E", "L": "T", "LateCost": "Costo tardío. Filtrar utilizando es igual a verdadero.", "LoadingOrderbook": "Cargando el libro de pedidos ...", "OrdersApprovedBetween": "Pedidos aprobados entre", "Q": "U", "Title": "Cartera de Pedidos"}, "PartsStockAgeing": {"Title": "Envejecimiento del stock de piezas"}, "PerformanceLeague": {"Advisor": "Advisor", "Bronze": "Bronce", "Gold": "Oro", "IncLeavers": "Incluir a los Egresados", "Rank": "Ra<PERSON>", "Role": "Papel", "ShowAllSites": "<PERSON><PERSON>", "ShowDelivered": "<PERSON><PERSON> en<PERSON>", "ShowProfit": "<PERSON>rar beneficio", "Silver": "Plata", "Title": "Liga de Ventas"}, "ReportPortal": {"Title": "Informes"}, "SitePicker": {"AllSitesSelected": "Todos los sitios", "NoSitesSelected": "No hay sitios"}, "StockItemModal": {"AccountStatus": "Estado de la cuenta", "CapCode": "Código Cap", "CapId": "CapId", "CapNotes": "Notas del Cap", "CapProven": "Cap Probada", "CarryingValue": "Valor en Libros", "Chassis": "<PERSON><PERSON>", "DaysAtBranch": "Días en la sucursal", "DaysInStock": "Días en stock", "DisposalRoute": "Ruta de eliminación", "Doors": "<PERSON><PERSON><PERSON>", "Fuel": "Combustible", "Mileage": "Kilometraje", "Options": "Opciones", "PreviousSite": "Sitio anterior", "PreviousUse": "Uso anterior", "ProgressCode": "Código de Progreso", "Registration": "<PERSON><PERSON><PERSON><PERSON>", "StockcheckLocation": "Ubicación del control de existencias", "StockcheckPhoto": "Foto de control de existencias", "StockcheckTime": "Tiempo de control de existencias", "Title": "Artículo de stock", "Transmission": "Transmisión", "VatQualifying": "Calificación de Impuestos", "VehicleDetails": "Detalles del vehículo"}, "StockLanding": {"Title": "Desembarco de Acciones"}, "StockList": {"ChooseFields": "Elegir campos", "ChooseNewReportName": "Elija un nombre para el nuevo informe", "HelpText": "Haga clic en cada campo del informe para alternar si aparece en la tabla. Reorganice las columnas arrastrándolas dentro de la tabla", "LastSaved": "Último informe guardado", "OpenReport": "Abrir informe", "ReportName": "Nombre del informe", "SaveAsNewReport": "Guardar como nuevo informe", "SaveReport": "Guardar informe", "ThisWillOverwriteReport": "Esto sobrescribirá el informe:", "Title": "Lista de existencias"}, "SuperCup": {"Title": "Supercopa de España"}, "TodayMap": {"Title": "Especiales de Hoy"}, "UpdateProfilePicture": "Actualizar Foto de Perfil", "UserMaintenance": {"Title": "Mantenimiento Usuario"}, "Whiteboard": {"Delivered": "<PERSON><PERSON><PERSON>", "Profit": "<PERSON><PERSON>", "Title": "Pizarron", "ToDo": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}