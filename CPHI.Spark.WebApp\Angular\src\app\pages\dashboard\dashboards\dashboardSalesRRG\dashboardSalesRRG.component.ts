//core angular
import { Component, EventEmitter, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardDataItem, DashboardDataPack, DashboardDataParams, DonutData } from '../../dashboard.model';
import { DashboardService } from '../../dashboard.service';
//model and cell renderers
//services



@Component({
  selector: 'dashboardSalesRRG',
  templateUrl: './dashboardSalesRRG.component.html',
  styleUrls: ['./dashboardSalesRRG.component.scss']
})


export class DashboardSalesComponent implements OnInit {



  dataItems: DashboardDataItem[];
  weekStart: Date;
  dataPack: DashboardDataPack;
  sub: Subscription;

  newDataEmitter:EventEmitter<void>
  donutDataNew: DonutData;
  donutDataFleet: DonutData;
  donutDataUsed: DonutData;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public service: DashboardService

  ) {


  }



  ngOnDestroy() {
    if(!!this.sub)this.sub.unsubscribe();
   }


  ngOnInit() {

    //launch control
    this.initParams()
    this.getData();

    this.sub = this.service.getNewDataTrigger.subscribe(res=>{
      this.getData();
    })

  }

  initParams() {

    this.newDataEmitter = new EventEmitter();
    this.weekStart = this.constants.startOfThisWeek();
    if(!this.service.thisWeeksOrdersStartDateFleet){this.service.thisWeeksOrdersStartDateFleet = this.constants.startOfThisWeek()}
    if(!this.service.thisWeeksOrdersStartDateUsed){this.service.thisWeeksOrdersStartDateUsed = this.constants.startOfThisWeek()}
    if(!this.service.thisWeeksOrdersStartDateNew){this.service.thisWeeksOrdersStartDateNew = this.constants.startOfThisWeek()}

    this.dataItems =
      [
        DashboardDataItem.DepartmentDealBreakdown,
        DashboardDataItem.DonutDataSets,
        DashboardDataItem.DailyNetOrdersCancellationsNew,
        DashboardDataItem.DailyNetOrdersCancellationsUsed,
        DashboardDataItem.DailyNetOrdersCancellationsFleet,
        DashboardDataItem.FinanceAndAddOn,
        DashboardDataItem.Registrations,
        DashboardDataItem.UsedStockMerchandising,
        DashboardDataItem.UsedStockMerchandisingBySite,
        DashboardDataItem.OverageStockSummary,
        //DashboardDataItem.ActivityLevelsAndOverdues,
        DashboardDataItem.UsedDelivered

      ]

  
  }


  getData() {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    let parms: DashboardDataParams = {
      SiteIds: this.service.chosenSites.map(x=>x.SiteId).join(','),
      WeekStart: this.weekStart,
      DataItems: this.dataItems,
      Department:'',
      WeekStartActivitiesTile:this.service.activitesTileStartDate,
      WeekStartOrdersTileNew: this.service.thisWeeksOrdersStartDateNew,
      WeekStartOrdersTileUsed: this.service.thisWeeksOrdersStartDateUsed,
      WeekStartOrdersTileFleet: this.service.thisWeeksOrdersStartDateFleet,
    }

    this.getDataService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {

      if(!this.dataPack){
        this.dataPack = res;
        this.donutDataNew = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='New')
        this.donutDataFleet = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Fleet')
        this.donutDataUsed = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Used')
      }else{
        Object.assign(this.dataPack,res)
        Object.assign(this.donutDataNew, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='New'))
        Object.assign(this.donutDataFleet, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Fleet'))
        Object.assign(this.donutDataUsed, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Used'))
      }

      //in order for the template to update the object it passes through the input tag to the child tile.   else the child tile regenerates its data again from the old dataPack
      setTimeout(()=>{
        this.newDataEmitter.emit();
        this.selections.triggerSpinner.next({ show: false });
      },50)
    })

  }





}
