import { Compo<PERSON>, ElementRef, HostListener, OnInit, ViewChild } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
   CellDoubleClickedEvent,
   ColDef,
   ColGroupDef,
   FilterChangedEvent,
   GetContextMenuItemsParams,
   ICellRendererParams,
   MenuItemDef,
   RowClickedEvent,
   SideBarDef,
   ValueFormatterParams,
   ValueGetterParams,
} from "ag-grid-community";
import { CphPipe, FormatType } from "src/app/cph.pipe";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExportService";
import { SelectionsService } from "src/app/services/selections.service";
import { BuildTotalAndAverageRowsParams } from "src/app/model/BuildTotalAndAverageRowsParams";
import { TodaySiteStats } from "src/app/model/TodaySiteStats";
import { VehicleAdvertDetail } from "src/app/model/VehicleAdvertDetail";
import { Subscription } from "rxjs";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";
import { LocalBargainsService } from "../localBargains.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { CustomHeaderService } from "src/app/components/customHeader/customHeader.service";
import { AutopriceRendererService } from "src/app/services/autopriceRenderer.service";
import { TableLayoutManagementService } from "src/app/components/tableLayoutManagement/tableLayoutManagement.service";
import { CPHAutoPriceColDef } from "src/app/model/CPHColDef";
import { CustomHeaderAdDetail } from "src/app/components/customHeaderAdDetail/customHeaderAdDetail.component";

export interface AdvertListingColGroupDef extends ColGroupDef {
   shouldAverage?: boolean;
   shouldAverageIfValue?: boolean;
   shouldTotal?: boolean;
   children: AdvertListingColDef[];
}

export interface AdvertListingColDef extends ColDef {
   shouldAverage?: boolean;
   shouldAverageIfValue?: boolean;
   shouldTotal?: boolean;
}

@Component({
   selector: "localBargainsTable",
   templateUrl: "./localBargainsTable.component.html",
   styleUrls: ["./localBargainsTable.component.scss"],
})
export class LocalBargainsTableComponent implements OnInit {
   @HostListener("window:resize", [])
   private onresize(event) {
      this.selections.screenWidth = window.innerWidth;
      this.selections.screenHeight = window.innerHeight;
      if (this.service.tableLayoutManagement.gridApi) {
         this.service.tableLayoutManagement.gridApi.resetRowHeights();
      }
   }

   @ViewChild("deleteTableStateModal", { static: true }) deleteTableStateModal: ElementRef;
   public components: { [p: string]: any } = {
      agColumnHeader: CustomHeaderAdDetail,
   };

   gridOptions: GridOptionsCph;
   public sideBar: SideBarDef | string | string[] | boolean | null = {
      toolPanels: [
         {
            id: "filters",
            labelDefault: "Filters",
            labelKey: "filters",
            iconKey: "filter",
            toolPanel: "agFiltersToolPanel",
            minWidth: 100,
            width: 200,
            maxWidth: 200,
         },
         {
            id: "columns",
            labelDefault: "Columns",
            labelKey: "columns",
            iconKey: "columns",

            toolPanel: "agColumnsToolPanel",
            minWidth: 100,
            width: 400,
            maxWidth: 400,
            toolPanelParams: {
               enableColumnGroup: true,
               enableRowGroup: true,
               suppressPivots: true,
               suppressPivotMode: true,
            },
         },
      ],
      position: "left",
      defaultToolPanel: "",
   };
   newDataLoadedSubscription: Subscription;

   constructor(
      public selections: SelectionsService,
      public excel: ExcelExportService,
      public gridHelpersService: AGGridMethodsService,
      public service: LocalBargainsService,
      public constants: ConstantsService,
      public cphPipe: CphPipe,
      public modalService: NgbModal,
      private colTypesService: ColumnTypesService,
      private customHeader: CustomHeaderService,
      private autopriceRendererService: AutopriceRendererService,
      public tableLayoutManagementService: TableLayoutManagementService
   ) {}

   ngOnInit() {
      this.setGridOptions();

      this.service.searchTerm.valueChanges.subscribe((value) => {
         this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value);
      });

      this.newDataLoadedSubscription = this.service.newDataLoaded.subscribe(() => {
         if (this.service.tableLayoutManagement.gridApi) {
            console.log("new data loaded");
            this.service.tableLayoutManagement.gridApi.setRowData(this.service.localBargainsRowData);
            this.service.tableLayoutManagement.gridApi.setColumnDefs(this.provideColumnDefs());
            this.service.tableLayoutManagement.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
         }
      });

      this.tableLayoutManagementService.defaultFilterState = null;
      this.tableLayoutManagementService.parent = this.service.tableLayoutManagement;
   }

   ngOnDestroy() {
      this.service.tableLayoutManagement.gridApi = null;
      this.service.tableLayoutManagement.gridColumnApi = null;
      this.newDataLoadedSubscription.unsubscribe();
   }

   setGridOptions() {
      this.gridOptions = {
         context: { thisComponent: this },
         suppressPropertyNamesCheck: true,
         showOpenedGroup: true,
         rowData: this.service.localBargainsRowData,
         onGridReady: (params) => this.onGridReady(params),
         rowHeight: this.gridHelpersService.getRowHeight(40),
         onCellDoubleClicked: (params) => this.onCellDoubleClicked(params),
         rowGroupPanelShow: "always",

         headerHeight: this.gridHelpersService.getHeaderHeight(),
         floatingFiltersHeight: this.gridHelpersService.getFloatingFilterHeight(),
         defaultColDef: {
            resizable: true,
            sortable: true,
            hide: false,
            floatingFilter: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               applyMiniFilterWhileTyping: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            cellClass: "agAlignCentre",
            headerComponentParams: {
               showPinAndRemoveOptions: false,
            },
            autoHeaderHeight: true,
            floatingFilterComponentParams: { suppressFilterButton: true },
         },
         rowSelection: "multiple",
         getContextMenuItems: (params) => this.getContextMenuItems(params),
         pinnedBottomRowData: this.providePinnedBottomRowData(),
         columnTypes: this.provideColTypes(),
         columnDefs: this.provideColumnDefs(),
         getRowClass: (params) => {
            if (params.data?.Description === "Total") return "total";
         },
         onRowClicked: (params) => this.onRowClicked(params),
         onSelectionChanged: (params) => this.onRowSelectionChange(),

         // statusBar: {
         //   statusPanels: [
         //     { statusPanel: 'agTotalAndFilteredRowCountComponent', align: 'right' },
         //   ]
         // },
         onFilterChanged: (event) => {
            this.onFilterChanged(event);
         },

         sideBar: this.sideBar,
         pivotMode: false,
         autoGroupColumnDef: {
            sortable: true,
         },
         onColumnVisible: (event) => this.onColumnVisible(event),
         getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
         groupUseEntireRow: true
      };
   }

   onCellDoubleClicked(params: CellDoubleClickedEvent<any, any>): void {
      if (params.node.data) {
         this.goToAutoTraderAdvert(params.node.data);
      }
   }

   onColumnVisible(event) {
      if (!event.api) {
         return;
      }

      let normalRowHeight = 40;
      let groupRowHeight = 40;

      event.api.forEachNode((node) => {
         if (!node.isRowPinned()) {
            node.setRowHeight(node.data ? normalRowHeight : groupRowHeight);
         }
      });
      event.api.onRowHeightChanged();

      this.tableLayoutManagementService.setColumnsForTypeahead();
   }

   provideColTypes() {
      let commonTypes = { ...this.colTypesService.provideColTypes([]) };

      //override for specifics
      commonTypes.boolean.cellRenderer = (params: ValueFormatterParams) => {
         if (params.node.isRowPinned()) {
            return null;
         }
         let value = params.value;
         if (value === "(Select All)") {
            return value;
         } else if (value === true || value === "true") {
            return "&#x2714;";
         } else {
            return "&#x2717;"; //'&square;'
         }
      };

      commonTypes.label.enableRowGroup = true;

      return commonTypes;
   }

   onFilterChanged(event: FilterChangedEvent<any, any>) {
      const filterModel = event.api.getFilterModel();

      if (Object.keys(filterModel).length === 0) {
         this.service.setDaysAgoFilter(null);
      }

      event.api.redrawRows();

      if (!this.service.tableLayoutManagement.gridApi) {
         return;
      }

      this.service.tableLayoutManagement.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
   }

   onRowClicked(params: RowClickedEvent<any, any>): void {
      if (params.api.isSideBarVisible()) {
         params.api.closeToolPanel();
      }
   }

   booleanGetter(params: ValueGetterParams<any>): any {
      const colId = params.column.getColId();
      if (params.data) {
         //is a cell
         return params.data[params.colDef.field];
      } else {
         let tot = 0;
         params.node.allLeafChildren.forEach((child) => {
            if (child.data[params.colDef.field]) tot++;
         });
         const result: number = tot;
      }
   }

   provideColumnDefs(): CPHAutoPriceColDef[] {
      let defs: CPHAutoPriceColDef[] = [
         {
            headerName: "Site Name",
            field: "RetailerSiteName",
            colId: "RetailerSiteName",
             pinned: "left",
            width: 20,
            type: "labelSetFilter",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Image",
            field: "AdvertFirstImage",
            colId: "ImageUrl",
            width: 80,
            maxWidth: 80,
             pinned: "left",
            type: "image",
            cellRenderer: (params) => this.autopriceRendererService.autoTraderAdvertImageRenderer(params),
            columnSection: "Local Bargains",
         },
         {
            headerName: "Make",
             pinned: "left",
            hide: false,
            field: "Make",
            colId: "Make",
            width: 15,
            type: "labelSetFilter",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Model",
            field: "Model",
            colId: "Model",
             pinned: "left",
            width: 20,
            type: "labelSetFilter",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Derivative",
             pinned: "left",
            field: "Derivative",
            colId: "Derivative",
            width: 20,
            type: "label",
            columnSection: "Local Bargains",
         },

         {
            headerName: "Reg",
            hide: true,
            field: "VehicleReg",
            colId: "VehicleReg",
            width: 10,
            type: "label",
            columnSection: "Local Bargains",
         },
         //{ headerName: 'Derivative', field: 'Derivative', colId: 'Derivative', width: 20, type: 'label', columnSection:'Local Bargains'},
         {
            headerName: "Chassis",
            hide: true,
            field: "Vin",
            colId: "Vin",
            width: 25,
            type: "label",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Vehicle",
            hide: true,
            field: "VehicleType",
            colId: "VehicleType",
            width: 10,
            type: "label",
            columnSection: "Local Bargains",
         }, //hide: true,
         {
            headerName: "Trim",
            hide: true,
            field: "Trim",
            colId: "Trim",
            width: 10,
            type: "label",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Body Type",
            hide: true,
            field: "BodyType",
            colId: "BodyType",
            width: 10,
            type: "labelSetFilter",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Fuel Type",
            field: "FuelType",
            colId: "FuelType",
            width: 10,
            type: "labelSetFilter",

            columnSection: "Local Bargains",
         },
         {
            headerName: "Transmission Type",
            hide: true,
            field: "TransmissionType",
            colId: "TransmissionType",
            width: 10,
            type: "labelSetFilter",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Mileage",
            field: "Mileage",
            colId: "Mileage",
            width: 10,
            type: "number",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Registered Date",
            hide: true,
            field: "FirstRegistered",
            colId: "FirstRegistered",
            width: 10,
            type: "date",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Age Band",
            field: "AgeBand",
            colId: "AgeBand",
            minWidth: 50,
            type: "labelSetFilter",
            cellRenderer: (params) => this.autopriceRendererService.autoTraderLozengeRenderer(params),
            columnSection: "Local Bargains",
         },
         {
            headerName: "Value Band",
            field: "ValueBand",
            colId: "ValueBand",
            minWidth: 100,
            type: "labelSetFilter",
            cellRenderer: (params) => this.autopriceRendererService.autoTraderLozengeRenderer(params),
            columnSection: "Local Bargains",
         },
         {
            headerName: "Valn Mkt. Av. Retail",
            field: "ValuationAdjRetail",
            shouldAverage: true,
            colId: "ValuationAdjRetail",
            width: 10,
            type: "currency",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Valn Mkt. Av. Trade",
            field: "ValuationAdjTrade",
            shouldAverage: true,
            colId: "ValuationAdjTrade",
            width: 10,
            type: "currency",
            columnSection: "Local Bargains",
         },
        

         {
            headerName: "Advertiser Price",
            field: "AdvertTotalPrice",
            colId: "AdvertTotalPrice",
            width: 10,
            shouldAverage: true,
            type: "currency",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Our Strategy Price",
            field: "StrategyPrice",
            colId: "StrategyPrice",
            shouldAverage: true,
            width: 10,
            type: "currency",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Opportunity",
            sort: "desc",
            sortIndex: 1,
            field: "Opportunity",
            colId: "Opportunity",
            shouldAverage: true,
            width: 10,
            type: "currency",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Local Retail Rating",
            field: "RetailRating",
            colId: "RetailRating",
            minWidth: 100,
            type: "number",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderRetailRatingRenderer(
                  this.getRetailRatingParams(params.data?.RetailRating)
               ),
            columnSection: "Local Bargains",
         },
         {
            headerName: "Retail Rating Band",
            field: "RetailRatingBand",
            colId: "RetailRatingBand",
            width: 10,
            type: "labelSetFilter",
            hide: false,
            columnSection: "Local Bargains",
         },
         {
            headerName: "Attention Grabber",
            field: "AdvertAttentionGrabber",
            colId: "AdvertAttentionGrabber",
            width: 10,
            type: "label",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Price Indicator",
            field: "AdvertPriceIndicator",
            colId: "AdvertPriceIndicator",
            minWidth: 100,
            type: "label",
            cellRenderer: (params) => this.autopriceRendererService.autoTraderPriceIndicatorRenderer(params),
            columnSection: "Local Bargains",
         },
         {
            headerName: "Supplied Price",
            field: "AdvertAdvertisedPrice",
            colId: "AdvertSuppliedPrice",
            width: 10,
            type: "currency",
            hide: true,
            columnSection: "Local Bargains",
         },
         {
            headerName: "Forecourt Price",
            hide: true,
            field: "AdvertForecourtPrice",
            colId: "AdvertForecourtPrice",
            width: 10,
            type: "currency",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Price Position",
            field: "PricePosition",
            colId: "PricePosition",
            width: 10,
            type: "percent",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Date On Forecourt",
            hide: true,
            field: "DateOnForecourt",
            colId: "DateOnForecourt",
            width: 10,
            type: "date",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Days Listed",
            field: "DaysListed",
            colId: "DaysListed",
            width: 10,
            type: "number",
            columnSection: "Local Bargains",
         },

         {
            headerName: "Advertister Name",
            field: "AdvertiserName",
            colId: "AdvertiserName",
            width: 10,
            type: "labelSetFilter",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Advertiser Segment",
            field: "AdvertiserSegment",
            colId: "AdvertiserSegment",
            width: 10,
            type: "labelSetFilter",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Advertiser Phone",
            hide: true,
            field: "AdvertiserPhone",
            colId: "AdvertiserPhone",
            width: 10,
            type: "label",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Advertiser Website",
            field: "AdvertiserWebsite",
            colId: "AdvertiserWebsite",
            width: 10,
            type: "label",
            cellRenderer: (params) => this.advertiserWebsiteURL(params),
            columnSection: "Local Bargains",
         },

         {
            headerName: "Local Days To Sell",
            field: "DaysToSellLocal",
            colId: "DaysToSellLocal",
            width: 10,
            type: "number",
            columnSection: "Local Bargains",
         },
         // {
         //   headerName: 'National Days To Sell',
         //   field: 'DaysToSellNational',
         //   colId: 'DaysToSellNational',
         //   width: 10,
         //   type: 'number',
         //   columnSection: 'Local Bargains'
         // },
         {
            headerName: "National Retail Rating",
            field: "NationalRetailRating",
            colId: "NationalRetailRating",
            minWidth: 100,
            type: "number",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderRetailRatingRenderer(
                  this.getRetailRatingParams(params.data.NationalRetailRating)
               ),
            columnSection: "Local Bargains",
         },

         {
            headerName: "National Demand",
            field: "Demand",
            colId: "Demand",
            width: 10,
            type: "label",
            columnSection: "Local Bargains",
         },
         {
            headerName: "National Supply",
            field: "Supply",
            colId: "Supply",
            width: 10,
            type: "label",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Market Condition",
            field: "MarketCondition",
            colId: "MarketCondition",
            width: 10,
            type: "label",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Last Price Change",
            field: "BargainPriceChange",
            colId: "BargainPriceChange",
            width: 15,
            type: "currencyWithPlusMinus",
            columnSection: "Local Bargains",
         },
         {
            headerName: "Last Price Change Days Ago",
            field: "BargainPriceChangeDaysAgo",
            colId: "BargainPriceChangeDaysAgo",
            width: 15,
            type: "number",
            columnSection: "Local Bargains",
         },
      ];
      this.gridHelpersService.workoutColWidths(this.service.localBargainsRowData, defs, 14, 6);
      (defs.find((x) => x.headerName == "Model") as ColDef).width = 100;
      // (defs.find(x => x.headerName == 'Derivative') as ColDef).width = 500;
      // (defs.find(x => x['field'] == 'AdvertiserName') as ColDef).width = 300;
      // (defs.find(x => x.headerName == 'Advertiser Website') as ColDef).width = 300;
      //console.log(defs)

      return defs as CPHAutoPriceColDef[];
   }

   onRowSelectionChange(): void {
      if (!this.service.tableLayoutManagement.gridApi) {
         return;
      }
      this.service.tableLayoutManagement.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
   }

   myBooleanRenderer(params: ICellRendererParams) {
      if (params.data) {
         return this.gridHelpersService.booleanRenderer(params.value);
      } else {
         let tot = 0;
         params.node.allLeafChildren.forEach((child) => {
            if (child.data[params.colDef.field]) tot++;
         });
         const result: number = tot;
         return result;
      }
   }

   myCellRenderer(
      params: ICellRendererParams,
      pipeType: FormatType,
      decimalPlaces: number,
      aggregation: "sum" | "average" | "averageIfValue"
   ) {
      if (params.data) {
         return this.cphPipe.transform(params.value, pipeType, 0);
      } else {
         let filteredMap = {};
         params.api.forEachNodeAfterFilter((node) => {
            filteredMap[node.id] = true;
         });
         let tot = 0;
         let count = 0;
         let countWithValue = 0;
         //let column = params.api.getColumnDef(params.colDef.colId)
         params.node.allLeafChildren.forEach((child) => {
            if (filteredMap[child.id]) {
               tot += params.api.getValue(params.column, child); // child.data[params.colDef.field];
               count++;
            }
         });
         let result = 0;
         if (aggregation === "sum") {
            result = tot;
         } else if (aggregation === "average") {
            result = count > 0 ? tot / count : 0;
         } else if (aggregation === "averageIfValue") {
            result = countWithValue > 0 ? tot / countWithValue : 0;
         }
         return this.cphPipe.transform(result, pipeType, decimalPlaces);
      }
   }

   myValueGetter(params: ValueGetterParams, aggregation: "sum" | "average" | "averageIfValue") {
      if (params.data) {
         return params.data[params.colDef.field];
      } else {
         let filteredMap = {};
         params.api.forEachNodeAfterFilter((node) => {
            filteredMap[node.id] = true;
         });
         let tot = 0;
         let count = 0;
         let countWithValue = 0;
         params.node.allLeafChildren.forEach((child) => {
            if (filteredMap[child.id]) {
               tot += child.data[params.colDef.field];
               count++;
               if (aggregation === "averageIfValue" && child.data[params.colDef.field]) {
                  countWithValue++;
               }
            }
         });
         if (aggregation === "sum") {
            return tot;
         } else if (aggregation === "average") {
            return count > 0 ? tot / count : 0;
         } else if (aggregation === "averageIfValue") {
            return countWithValue > 0 ? tot / countWithValue : 0;
         }
      }
   }

   providePinnedBottomRowData(): VehicleAdvertDetail[] {
      if (!this.service.tableLayoutManagement.gridApi) {
         return;
      }
      const params: BuildTotalAndAverageRowsParams = this.buildUpParamsForBottomRows();
      return this.gridHelpersService.buildTotalAndAverageRows(params);
   }

   private buildUpParamsForBottomRows() {
      const params: BuildTotalAndAverageRowsParams = {
         colsToTotalOrAverage: [],
         colsToTotal: [],
         selectedCountFieldName: "RetailerSiteName",
         labelFieldName: "Derivative",
         colsToSetToTrue: [],
         itemName: "vehicle",
         api: this.service.tableLayoutManagement.gridApi,
         colsToSkipAverageIfZero: [],
         includeTotalRow: false,
         showTotalInAverage: false,
      };
      let colDefs = this.provideColumnDefs();
      colDefs.forEach((colDef) => {
         if ((colDef as AdvertListingColGroupDef).children) {
            (colDef as AdvertListingColGroupDef).children.forEach((child) => {
               if (child.shouldAverageIfValue) {
                  params.colsToTotalOrAverage.push(child.field);
               }
               if (child.shouldAverage) {
                  params.colsToTotalOrAverage.push(child.field);
               }
               if (child.shouldTotal) {
                  params.colsToTotal.push(child.field);
               }
            });
         } else {
            if (colDef.shouldAverageIfValue) {
               params.colsToTotalOrAverage.push((colDef as AdvertListingColDef).field);
            }
            if (colDef.shouldAverage) {
               params.colsToTotalOrAverage.push((colDef as AdvertListingColDef).field);
            }
            if (colDef.shouldTotal) {
               params.colsToTotal.push((colDef as AdvertListingColDef).field);
            }
         }
      });
      return params;
   }

   getContextMenuItems(params: GetContextMenuItemsParams<any, any>): (string | MenuItemDef)[] {
      const selectedNodeIds: string[] = this.service.tableLayoutManagement.gridApi.getSelectedNodes().map((x) => x.id);
      const row: TodaySiteStats = params.node.data;
      if (!selectedNodeIds.includes(params.node.id)) {
         this.service.tableLayoutManagement.gridApi.forEachLeafNode((node) => {
            node.setSelected(node.id === params.node.id);
         });
      }

      let result: (string | MenuItemDef)[] = [];

      result.push(
         {
            name: "Go to advert",
            action: () => {
               this.goToAutoTraderAdvert(params.node.data);
            },
            cssClasses: ["bold"],
         },
         {
            name: "Go to advertiser website",
            action: () => {
               this.goToAdvertiserWebsite(params.node.data);
            },
         },
         "separator"
      );

      result = result.concat(this.tableLayoutManagementService.getTableContextMenuItems());

      result = result.concat(["separator", "cut", "copy", "copyWithHeaders", "copyWithGroupHeaders"]);

      return result;
   }

   // saveNewTableState() {
   //   if (!this.service.tableLayoutManagement.gridColumnApi) return;

   //   const tableState: ColumnState[] = this.service.tableLayoutManagement.gridColumnApi.getColumnState();

   //   const modalRef: NgbModalRef = this.modalService.open(SimpleTextModalComponent);
   //   modalRef.componentInstance.header = 'Choose label';
   //   modalRef.componentInstance.chosenLabel = `New layout created ${this.cphPipe.transform(new Date(), 'date', 0)}`;
   //   modalRef.componentInstance.placeholder = 'Enter layout label...';

   //   modalRef.result.then(res => {
   //     if (!res.chosenLabel) return this.constants.toastDanger('Please provide a layout label');

   //     let params: AutoPriceTableStateParams = {
   //       State: JSON.stringify(tableState),
   //       FilterModel: this.service.tableLayoutManagement.gridApi.isAnyFilterPresent() ? JSON.stringify(this.service.tableLayoutManagement.gridApi.getFilterModel()) : null,
   //       Label: res.chosenLabel,
   //       PageName: 'localBargains'
   //     }

   //     this.service.saveNewTableState(params, res.chosenLabel);
   //   }, () => { })
   // }

   // saveExistingTableState() {
   //   const tableState: ColumnState[] = this.service.tableLayoutManagement.gridColumnApi.getColumnState();

   //   let params: AutoPriceTableStateParams = {
   //     State: JSON.stringify(tableState),
   //     FilterModel: this.service.tableLayoutManagement.gridApi.isAnyFilterPresent() ? JSON.stringify(this.service.tableLayoutManagement.gridApi.getFilterModel()) : null,
   //     Label: this.service.chosenTableStateLabel,
   //     PageName: 'localBargains'
   //   }

   //   this.service.saveExistingTableState(params);
   // }

   // maybeDeleteTableState() {
   //   this.modalService.open(this.deleteTableStateModal, { size: 'md', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then(() => {
   //     this.service.deleteTableState();
   //   }, () => {
   //     this.modalService.dismissAll();
   //   });
   // }

   onGridReady(params) {
      this.service.tableLayoutManagement.gridApi = params.api;
      this.service.tableLayoutManagement.gridColumnApi = params.columnApi;
      this.gridOptions.context = { thisComponent: this };

      //reapply table col state
      if (this.service.loadedTableState) {
         this.service.tableLayoutManagement.gridColumnApi.applyColumnState({ state: this.service.loadedTableState });
         this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.filterModel);
      }

      //reapply filter model
      if (this.service.filterModel) {
         this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.filterModel);
         this.service.tableLayoutManagement.gridApi.onFilterChanged();
      }

      if (this.service.externalFilterModel) {
         this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.externalFilterModel);
         this.service.tableLayoutManagement.gridApi.onFilterChanged();
      }

      this.service.tableLayoutManagement.originalColDefs = this.provideColumnDefs();

      this.selections.triggerSpinner.emit({ show: false });
   }

   excelExport() {
      let tableModel = this.service.tableLayoutManagement.gridApi.getModel();
      this.excel.createSheetObject(tableModel, "Local Bargains", 1, 1);
   }

   addEnergyTypeIcon(params: ICellRendererParams): string {
      if (!params.data) {
         return "";
      }
      if (params.node.isRowPinned()) {
         return "";
      }
      let icon: string;
      let icon2: string;

      if (params.value == "Diesel") icon = "fas fa-gas-pump";
      if (params.value == "Electric") icon = "fas fa-charging-station electric-green";
      if (params.value == "Petrol") icon = "fas fa-gas-pump petrol-green";
      if (params.value == "Petrol Hybrid" || params.value == "Petrol Plug-in Hybrid" || params.value == "Bi Fuel") {
         icon = "fas fa-plug blue";
         icon2 = "fas fa-gas-pump petrol-green";
      }

      return `${params.value}<i class="${icon}"></i><i class="${icon2}"></i>`;
   }

   clearSearchTerm() {
      this.service.searchTerm.setValue("");
      if (this.service.tableLayoutManagement.gridApi)
         this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value);
   }

   clearFilterModel() {
      this.service.filterModel = null;
      this.service.tableLayoutManagement.gridApi.setFilterModel(null);
   }

   showFilterRemoveButton() {
      return this.service.tableLayoutManagement.gridApi?.isAnyFilterPresent();
   }

   goToAutoTraderAdvert(data: any) {
      let url: string = this.constants.buildAdUrl(data.WebSiteSearchIdentifier, data.VehicleType);
      window.open(url, "_blank").focus();
   }

   goToAdvertiserWebsite(data: any) {
      window.open(data.AdvertiserWebsite, "_blank").focus();
   }

   getRetailRatingParams(field: any) {
      return {
         params: {
            RetailRating: field,
            InTableCell: true,
         },
      };
   }

   advertiserWebsiteURL(params: ICellRendererParams) {
      function truncate(str, maxLength) {
         if (!str) return "";
         if (str.length <= maxLength) {
            return str;
         } else {
            return str.slice(0, maxLength) + "...";
         }
      }

      if(!params.data){return null}
      return `
      <a href="${params.data.AdvertiserWebsite}" target="_blank" class="advertiserWebsiteLink">
        ${truncate(params.data.AdvertiserWebsite, 50)}
      </a>
    `;
   }
}
