import { DatePipe } from "@angular/common";
import { Injectable } from "@angular/core";
import { ColumnApi, ColumnState, GridApi } from "ag-grid-community";
import { PricingChangeNew } from "src/app/model/PricingChangeNew";

@Injectable({
    providedIn: 'root'
})

export class TodayPricesService {
    constructor(
        private datePipe: DatePipe
    ) { }

    priceChangesRowData: PricingChangeNew[];
    chosenRetailerSiteIds: number[];
    chosenDate: Date = new Date();
    includeNewVehicles: boolean 
    showSmallPriceChanges: boolean = false;
    includeUnPublishedAds:boolean
    summariseBySite: boolean;
    gridApi: GridApi;
    gridColumnApi: ColumnApi;

    gridApiForExcelDownload: GridApi;
    filterModel;
    columnState: ColumnState[];

    get chosenDateAsString(): string {
        return this.datePipe.transform(this.chosenDate, 'yyyy-MM-dd');
    }
}