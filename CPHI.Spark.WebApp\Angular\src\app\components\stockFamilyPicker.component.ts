import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { ConstantsService } from '../services/constants.service';



@Component({
  selector: 'stockFamilyPicker',
  template:    `
    <!-- Site selector -->
    <div ngbDropdown dropright class="d-inline-block" id="siteDropdown">
        <button [ngClass]="buttonClass" class=" btn btn-primary" (click)="generateFamiliesList()"
          ngbDropdownToggle>{{siteChosenLabel()}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <!-- ngFor buttons -->  
        <ng-container    *ngFor="let familyCode of familyCodes">
        <button  (click)="toggleItem(familyCode)" [ngClass]="{'active':familyCode.isSelected}"
        ngbDropdownItem>{{familyCode.Description}}</button>
        </ng-container>
        <!-- select Total -->  
        <button class="quickSelect" (click)="quickSelectTotal()" ngbDropdownItem>Total</button>
        <!-- quick select -->
          <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="selectFamilyCodes()">{{constants.translatedText.OKUpper}}</button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
          </div>

        </div>
      </div>
    
    `
  ,
  styles: [`
  @media (min-width: 0px) and (max-width: 1920px) and (hover:none) {
    #siteDropdown .dropdown-menu{columns:2}

  }  

    `]
})


export class StockFamilyPickerComponent implements OnInit {
  @Input() familyCodesFromParent: string[];
  @Input() buttonClass: string;
  @Input() onlyOneSite: boolean;
  @Output() updateFamilyCodes = new EventEmitter<string[]>();

  public familyCodes: {Description: string, isSelected:boolean}[];
  public familyCodeSelectedDescriptions: string[];
  
  constructor(
    public constants: ConstantsService,
    
  ) { }


  ngOnInit(): void { 
    
   }




  siteChosenLabel() {
    if(!this.familyCodesFromParent) return this.constants.translatedText.FamilyPicker_FamilyCodes;
    if (this.familyCodesFromParent.length == 0) {
      return this.constants.translatedText.FamilyPicker_NoFamilyCodes;
    } else if (this.familyCodesFromParent.length == 1) {
      return this.familyCodesFromParent[0]
    } else if (this.familyCodesFromParent.length < 4) {
      let familyCodes = ''
      this.familyCodesFromParent.forEach((familyCode, i) => {
        if (i > 0) { familyCodes = familyCodes + ',' } //leading comma for 2nd item onwards
        familyCodes += familyCode;
      })
      return familyCodes
    } else if (this.familyCodesFromParent.length == 0){
      return this.constants.translatedText.FamilyPicker_AllFamilyCodes;
    }else{
      return this.constants.translatedText.FamilyPicker_FamilyCodes;
    }
  }

  generateFamiliesList() {
    this.familyCodeSelectedDescriptions = this.familyCodesFromParent

    //recreate local list
    this.familyCodes = []
    
    this.constants.allFamilyCodes.forEach(x=>{
      this.familyCodes.push({Description:x,isSelected:false})
    })
    
    //tag if it's selected
    this.familyCodes.forEach(s => {
      if (this.familyCodeSelectedDescriptions.includes(s.Description)) {
        s.isSelected = true;
      }
    })
  }

  
  toggleItem(item: any) {
    if (!this.onlyOneSite){item.isSelected = !item.isSelected}
    else {
      this.familyCodes.forEach(s => {
        s.isSelected = false
      })
      item.isSelected = true;
    }
    
  }

  selectFamilyCodes() {
    this.updateFamilyCodes.emit(this.familyCodes.filter(e => e.isSelected).map(x=>x.Description));
  }

 

  quickSelectTotal() {
    //if all selected (except site 0), select none else select all
    const isTrue = (currentValue) => currentValue.isSelected;

    if (this.familyCodes.every(isTrue)){
      this.familyCodes.forEach(s=>s.isSelected = false)
    }else{
      this.familyCodes.forEach(s => {
          s.isSelected = true;
      })
    }
  }

  


}


