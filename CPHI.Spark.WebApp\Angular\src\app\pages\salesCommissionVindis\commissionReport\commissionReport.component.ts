import { Component, Input, OnInit, SimpleChanges } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CommissionAdjustmentComponent } from "src/app/components/commissionAdjustment/commissionAdjustment.component";
import { DealDetailsComponent } from "src/app/components/dealDetails/dealDetails.component";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { CommissionAdjustmentItem, CommissionMeasures, CommissionPayoutPersonSummary, CommissionsDataItemVindis } from "../salesCommissionVindis.model";
import { SalesCommissionVindisService } from "../salesCommissionVindis.service";
import { Scheme, ThresholdConfig, CommissionItem, DealDetailsTotal } from './commissionReport.model';
import { GlobalParamsService } from "src/app/services/globalParams.service";
import { GlobalParamKey } from "src/app/model/GlobalParam";


@Component({
  selector: 'commissionReport',
  templateUrl: './commissionReport.component.html',
  styleUrls: ['./commissionReport.component.scss']
})

export class CommissionReportComponent implements OnInit {
  @Input() statement: CommissionPayoutPersonSummary;

  Scheme = Scheme;

  commissionScheme: Scheme;
  commissionItems: CommissionItem[];
  commissionItemsServicePlanOnly: CommissionItem[];

  commissionBand: number;

  profitBand: number;
  vehicleTypes: string[];
  dealDetailsTotalRow: DealDetailsTotal;

  showProfitPot: boolean;

  totalPerUnit: number;
  totalPayout: number;
  totalAdjustments: number;

  totalOTE: number;
  totalPayoutOTE: number;

  hideGap: boolean;

  bonusThresholdsFY: string[]


  constructor(
    public constants: ConstantsService,
    public service: SalesCommissionVindisService,
    public modalService: NgbModal,
    public selections: SelectionsService,
    private globalParamsService: GlobalParamsService

  ) { }

  ngOnInit() {

    this.service.isReviewer = this.selections.user.permissions.reviewCommission;

    this.commissionScheme = this.getScheme();

    this.showProfitPot = this.shouldShowProfitPot();

    this.setCommissionItems();

    // Remove/add certain items from AutoNow sites
    if(this.statement.SalesmanSiteDescription.includes('AutoNow'))
    {
      this.commissionItems = this.commissionItems.filter(x => x.label != 'Motability Volume' && 
                                                          x.label != 'New Volume' && 
                                                          x.label != 'Demo Volume' &&
                                                          x.label != 'Service Plan'
                                                          );

      this.commissionItems.push({ label: 'Warranty', field: 'Warranty' });      
    }

    let commissionBandString: string = this.statement.CommissionItems[0].ItemPayoutRates.RangeDescription;

    switch (commissionBandString) {
      case '0.0% - 75.1%':
        this.commissionBand = 1;
        break;
      case '75.1% - 100.0%':
        this.commissionBand = 2;
        break;
      case '100.0% - 100.1%':
        this.commissionBand = 3;
        break;
      case '>100%':
        this.commissionBand = 4;
        break;
    }

    this.calculateProfitBand();

    this.totalPerUnit = 0;

    this.commissionItems.forEach(item => {
      this.totalPerUnit += this.statement.ActualEarnings[item.field]
    });

    this.totalOTE = 0;

    this.commissionItems.forEach(item => {
      this.totalOTE += this.statement.OTEIllustration[item.field]
    });

    let vehicleTypes: string[] = ['New', 'Used', 'Demo'];

    this.vehicleTypes = vehicleTypes;

    let dataItems: CommissionsDataItemVindis[] = this.statement.CommissionItems.map(x => x.DataItem);


    this.dealDetailsTotalRow = {
      ChassisProfit: this.constants.sum(dataItems.map(x => x.ChassisProfit)),
      FinanceProfit: this.constants.sum(dataItems.map(x => x.FinanceProfit)),
      Financed: dataItems.filter(x => x.IsOnFinance).length,
      Gap: this.constants.sum(dataItems.map(x => x.Gap)),
      PaintProtect: this.constants.sum(dataItems.map(x => x.Paint)),
      Cosmetic: this.constants.sum(dataItems.map(x => x.Cosmetic)),
      ServicePlan: this.constants.sum(dataItems.map(x => x.ServicePlan)),
      Warranty: this.constants.sum(dataItems.map(x => x.Warranty)),
      InMonthForMonth: this.constants.sum(dataItems.map(x => x.InMonthForMonth)),
    }


    this.totalAdjustments = this.getTotalAdjustments();

    this.calculateTotalPayout();
    
    this.totalPayoutOTE = this.totalOTE + this.statement.OTEPayouts.ProfitPot + this.service.chosenPerson.BonusSummary.OTEPayout;

    // If 2024 onwards, remove the Service Plan to display seperately
    if(this.commissionScheme == Scheme.From2024)
    {
      this.commissionItemsServicePlanOnly = this.commissionItems.filter(x => x.label == 'Service Plan');
      this.commissionItems = this.commissionItems.filter(x => x.label != 'Service Plan');
    }


  }


  setCommissionItems(): void
  {

    this.hideGap = this.service.chosenMonth.getFullYear() >= 2025;
    
    // Base items
    let commissionItems: CommissionItem[] = [
      { label: 'New Volume', field: 'NewValue' },
      { label: 'Motability Volume', field: 'MotabValue' },
      { label: 'Used Volume', field: 'UsedValue' },
      { label: 'Demo Volume', field: 'DemoValue' }
    ];

    let siteName = this.statement.SalesmanSiteDescription;

    // If Three10
    if (this.statement.SalesmanSiteDescription.includes('Three10')) {
      commissionItems = [
        { label: 'Used Volume', field: 'UsedValue' },
        { label: 'Used Volume >£60k', field: 'UsedHighValueUnits' }
      ];
    }

    let additionalCommissionItems: CommissionItem[];

    // 2024 scheme + Not AutoNow + Not Ducati
    if(this.commissionScheme == Scheme.From2024 && !siteName.includes('AutoNow') && !siteName.includes('Three10') && !siteName.includes('Ducati'))
    {
      additionalCommissionItems = [
        { label: 'Cosmetic', field: 'Cosmetic' },
        { label: 'GAP', field: 'Gap' },
        { label: 'Paint Protection', field: 'Paint' },
        { label: 'Used Finance Cases', field: 'UsedFinanceCases' },
        { label: 'ITMFTM', field: 'InMonthForMonth' },
        { label: 'Service Plan', field: 'ServicePlan' }
      ]
    }
    // 2024 scheme + AutoNow
    else if(this.commissionScheme == Scheme.From2024 && siteName.includes('AutoNow'))
    {
      additionalCommissionItems = [
        { label: 'Cosmetic', field: 'Cosmetic' },
        { label: 'GAP', field: 'Gap' },
        { label: 'Paint Protection', field: 'Paint' },
      ]
    }
    // 2024 scheme + Three10
    else if(this.commissionScheme == Scheme.From2024 && siteName.includes('Three10'))
      {
        additionalCommissionItems = [
          { label: 'Cosmetic', field: 'Cosmetic' },
          { label: 'Paint Protection', field: 'Paint' },
          { label: 'Used Finance Cases', field: 'UsedFinanceCases' },
          { label: 'Warranty', field: 'Warranty' }
        ]
      }
    // 2024 scheme + Ducati
    else if(this.commissionScheme == Scheme.From2024 && siteName.includes('Ducati'))
    {
      additionalCommissionItems = [
        { label: 'GAP', field: 'Gap' },
        { label: 'Used Finance Cases', field: 'UsedFinanceCases' },
        { label: 'Paint Protection', field: 'Paint' },
      ]
    }
    // Pre-2024 scheme
    else
    {
      additionalCommissionItems = [
        { label: 'Cosmetic', field: 'Cosmetic' },
        { label: 'GAP', field: 'Gap' },
        { label: 'Paint Protection', field: 'Paint' },
        { label: 'Service Plan', field: 'ServicePlan' }
      ]
    }

    // Apply hidegap boolean to filter out GAP
    if (this.hideGap) {
      additionalCommissionItems = additionalCommissionItems.filter(item => item.field !== 'Gap');
    }

    this.commissionItems = commissionItems.concat(additionalCommissionItems);
  }

  // Externalize the thresholds map as a class property or in a separate configuration file
  private thresholdsMap: { [key: string]: ThresholdConfig } = {
    'Three10': { 
      bonusThresholdsFY: ['<155', '155 - 171', '172 - 186', '187 - 201', '202+'],
      bonusThresholdsFYPerc: ['<100%', '100 - 110%', '110 - 120%', '120 - 130%', '130%']
    }, 
    'VW CV': {
      bonusThresholdsFY: ['<145', '145 - 160', '161 - 174', '175 - 189', '190+'],
      bonusThresholdsFYPerc: ['<100%', '100 - 110%', '110 - 120%', '120 - 130%', '130%']
    },
    'Audi': {
      bonusThresholdsFY: ['<135', '135 - 148', '149 - 161', '162 - 175', '176+'],
      bonusThresholdsFYPerc: ['<90%', '90 - 100%', '100 - 110%', '110 - 120%', '120%+']
    },
    // Add more thresholds here as needed
    'Default': {
      bonusThresholdsFY: ['<155', '155 - 171', '172 - 186', '187 - 201', '202+'],
      bonusThresholdsFYPerc: ['<100%', '100 - 110%', '110 - 120%', '120 - 130%', '130%']
    }
  };

  // These are the values that wil appear at the top of the Annual Bonus table
  setAnnualBonusThresholds(salesmanSite: string): void {
    // Default to 'Default' thresholds
    let key = 'Default';
  
    // Dynamically find the matching key
    for (const siteKey of Object.keys(this.thresholdsMap)) {
      if (salesmanSite.includes(siteKey)) {
        key = siteKey;
        break;
      }
    }

    console.log(key, "key!");
  
    const thresholds = this.thresholdsMap[key];
    this.bonusThresholdsFY = thresholds.bonusThresholdsFY;
    this.service.bonusThresholdsFYPerc = thresholds.bonusThresholdsFYPerc;
  }

  // Recalc per unit payout if month is changed
  ngOnChanges(changes: SimpleChanges) {
    this.ngOnInit();
    this.getBonus();
  }

  getBonus()
  { 

    // Set the bonus variables
    this.shouldDisplayAnnualBonus2025Onw();
    this.shouldDisplayHalfYearlyBonusPre2024();
    this.shouldDisplayQuarterlyBonus();

    // Jan 2025 onwards
    if(this.service.showAnnualBonus)
    {
      this.service.getAnnualBonusSummaryForPerson(this.service.chosenMonth, this.service.chosenPerson.SalesmanId);
      this.setAnnualBonusThresholds(this.statement.SalesmanSiteDescription);
      this.service.bonusPayouts = !this.statement.SalesmanSiteDescription.includes('Three10') ? ['-', '£5.00', '£10.00','£17.50','£25.00'] : ['-', '£9.70','£12.50','£15.00','£20.00'];
    }

    // Pre 2024
    if(this.service.showHalfYearlyBonus)
    {
      this.service.getBonusSummaryForPerson(this.service.chosenMonth, this.service.chosenPerson.SalesmanId,'HY');

      if(this.service.chosenMonth.getFullYear() == 2022)
      {
        this.service.bonusPayouts = !this.statement.SalesmanSiteDescription.includes('Three10') ? ['-', '£5.28', '£15.71','£21.04','£26.40'] : ['-', '£10.71','£31.83','£42.07','£53.42'];

      }
      if(this.service.chosenMonth.getFullYear() == 2023)
      {
        this.service.bonusPayouts = !this.statement.SalesmanSiteDescription.includes('Three10') ? ['-', '£5.00', '£10.00','£17.50','£25.00'] : ['-', '£9.70','£12.50','£15.00','£20.00'];

      }
    }

    // Can be any year
    if(this.service.showQtrlyBonus)
    {
      this.service.getBonusSummaryForPerson(this.service.chosenMonth, this.service.chosenPerson.SalesmanId,'Qtr');
    }


  }

  calculateProfitBand(): void
  {

    if(!this.statement.SalesmanSiteDescription.includes('AutoNow'))
    {
      let actual: CommissionMeasures = this.statement.ActualPerformance;
      let target: CommissionMeasures = this.statement.Targets;
      let profitBand: number = (actual.NewProfit + actual.UsedProfit + actual.DemoProfit) / (target.NewProfit + target.UsedProfit + target.DemoProfit);
  
      if (profitBand < .75) this.profitBand = 1;
      else if (profitBand >= .75 && profitBand <= 1) this.profitBand = 2;
      else this.profitBand = 3;
    }
    else
    {
      this.profitBand = this.commissionBand;
    }

  }


  getColumnToOutline(table: string): string {

    const tableToBandMap = {
      profit: this.profitBand,
      commission: this.commissionBand,
      bonus1: this.service.chosenPerson.BonusSummary.H1Band,
      bonus2: this.service.chosenPerson.BonusSummary.H2Band,
      bonusFY: this.service.chosenPerson.BonusSummary.FYBand
    };
    
    let band: number = tableToBandMap[table] || 0;
    if(band == 0){ debugger; }

    return `outline-column-${band}`;
  }

  getColumnToOutlineServicePlan(table: string): string
   {
      let band = 1;
      let totalVolumeExcMotab = this.statement.ActualPerformance['TotalVolume'] - this.statement.ActualPerformance['MotabValue'];
      let percAchieved: number = this.statement.ActualPerformance['ServicePlan'] / totalVolumeExcMotab;

      if(percAchieved >= 0.80)
      {
        band = 4;
      }
      else if(percAchieved >= 0.65)
      {
        band = 3;
      }
      else if(percAchieved >= 0.40)
      {
        band = 2;
      }

      return `outline-column-${band}`;
   }

  counter(i: number) {
    return new Array(i);
  }

  showCommissionAdjustment(salesExecId: number): void {
    setTimeout(() => {
      const modalRef = this.modalService.open(CommissionAdjustmentComponent);
      modalRef.componentInstance.salesExecId = salesExecId;
      modalRef.componentInstance.month = this.service.chosenMonth;
      modalRef.result.then((result: CommissionAdjustmentItem[]) => {

        if (result) {
          this.statement.Adjustments = result;
          this.totalAdjustments = this.getTotalAdjustments();
          this.calculateTotalPayout();
        }
      });
    }, 10)
  }

  getTotalAdjustments() : number {
    return this.constants.sum(this.statement.Adjustments.map(x => x.Value));
  }

  formatAuditStatus(status: string) : string {
    if (status == null) return 'Incomplete';
    if (status) return 'Passed';
    if (!status) return 'Failed';
  }

  openDealModal(dealId: number) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });

    const modalRef = this.modalService.open(DealDetailsComponent);
    modalRef.componentInstance.givenDealId = dealId;
    modalRef.result;
  }

  enableAdjustmentsButton(): boolean
  {
    if(!this.selections.user.permissions.reviewCommission){ return false; };

    const latestArchivedMonth = this.globalParamsService.getGlobalParam(GlobalParamKey.lastCommissionLockedMonth);  //  this.constants.GlobalParams.find(x => x.Description == 'lastCommissionLockedMonth');
    if (!latestArchivedMonth) return false;
    if (latestArchivedMonth && this.service.chosenMonth.getTime() > (latestArchivedMonth as Date).getTime()) return true;
    return false;
  }


  backToSiteViewOnClick(): void
  {
    this.service.chosenPerson = null;

    // If the bonus has been qualified/unqualified, refresh this data
    if(this.service.showHalfYearlyBonus){ this.service.refreshBonuses('people'); }

  }

  // Annual bonus - paid January
  shouldDisplayAnnualBonus2025Onw(): void
  {

    // We will not display this until 2025
    if(this.service.chosenMonth.getFullYear() <= 2024)
    { 
      this.service.showAnnualBonus = false;
    }
    // Only display in January 205 onwards
    else if(this.commissionScheme == Scheme.From2024 && this.service.chosenMonth.getMonth() == 0)
    { 
      this.service.showAnnualBonus = true;
    
    }

  };

  // Half yearly bonus - paid Dec/Jun
  shouldDisplayHalfYearlyBonusPre2024(): void {
    // If 2024 onwards, do not show
    if (this.commissionScheme == Scheme.From2024) {
      this.service.showHalfYearlyBonus = false;
    // Only show Dec/Jun
    } else if (
      (this.service.chosenMonth.getMonth() == 11 || this.service.chosenMonth.getMonth() == 5) &&  
      !this.statement.SalesmanSiteDescription.includes('AutoNow')
    ) {
      this.service.showHalfYearlyBonus = true; 
    } else {
      this.service.showHalfYearlyBonus = false; 
    }
  };

  // Quarterly bonus - paid Mar/Jun/Sep/Dec
  shouldDisplayQuarterlyBonus(): void {
    const quarterlyMonths = [2, 5, 8, 11]; // March, June, September, December (0-indexed months)
    this.service.showQtrlyBonus = quarterlyMonths.includes(this.service.chosenMonth.getMonth());
  }

  // Criteria met for bonus (either yearly, half-yearly or quarterly)
  updateCriteriaMet(newSelection: string, bonusType: string): void
  {
    const selectionAsBool: boolean = newSelection == 'Yes' ? true : false;
    this.service.setCommissionQualificationForPerson(this.service.chosenMonth, this.service.chosenPerson.SalesmanId, selectionAsBool, bonusType);

    // Recalculate the total payout based on new bonus figures
    this.calculateTotalPayout();
  }

  calculateTotalPayout(): void
  {
    setTimeout(()=>{
      this.totalPayout = this.totalAdjustments + this.totalPerUnit + this.statement.ActualEarnings.ProfitPot + this.service.chosenPerson.BonusSummary.FYPayout + this.service.chosenPerson.BonusSummary.HalfYearlyPayout + this.service.chosenPerson.BonusSummary.QtrBonus; 
      this.setAnnualBonusThresholds(this.statement.SalesmanSiteDescription);
    },150)

  }

  shouldShowProfitPot(): boolean
  {
    if(this.statement.SalesmanSiteDescription == 'Three 10 Automotive')
    {
      return false;
    }
    else if(this.statement.SalesmanSiteDescription.includes('AutoNow'))
    {
      return true
    }
    else if(this.commissionScheme == Scheme.From2024)
    {
      return false
    }

    return true;
  }

  getScheme(): Scheme
  {
    if(this.statement.SalesmanSiteDescription.includes('AutoNow'))
    {
      return Scheme.Pre2024
    }
    else if(this.service.chosenMonth.getFullYear() >= 2024)
    {
      return Scheme.From2024;
    }
    else 
    {
      return Scheme.Pre2024;
    }

  }


  isValidThreshold(value: string): boolean {
    return !value.includes('NaN');
  }


  get showTrackback(){
    return this.service.chosenMonth >= new Date('2025-06-01');
  }

}
