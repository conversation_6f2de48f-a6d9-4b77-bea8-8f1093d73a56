import {Component, OnInit, ViewChild, ElementRef, Input} from '@angular/core';
import {NgbModal, NgbActiveModal} from '@ng-bootstrap/ng-bootstrap';
import {ConstantsService} from '../../services/constants.service';

@Component({
   selector: 'datePickerDateModal',
   templateUrl: './datePickerModal.component.html',
   styleUrls: ['./datePickerModal.component.scss']
})

export class DatePickerModalComponent implements OnInit {

   @ViewChild('datePickerModalRef', {static: true}) datePickerModalRef: ElementRef;
   @Input() public heading: string;

   @Input() public fromDate: string;
   @Input() public toDate: string;
   minDate: string;
   maxDate: string;

   constructor(
      public constants: ConstantsService,
      public modalService: NgbModal,
      public activeModal: NgbActiveModal
   ) {
   }

   ngOnInit() {
      this.initParams();
   }

   initParams() {
      this.setDates();

      this.modalService.open(this.datePickerModalRef, {
         size: 'sm',
         keyboard: false,
         ariaLabelledBy: 'modal-basic-title'
      }).result.then((result) => {
            // 'okd'
            this.passBack(),
            this.modalService.dismissAll();
         },
         // closed
         (reason) => {
            // cancelled, so no passback
            this.modalService.dismissAll();
         });
   }

   setDates() {
      // if(!this.fromDate){this.fromDate = this.constants.appStartTime.toISOString().split('T')[0];}
      // if(!this.toDate){ this.toDate = this.constants.addYears(this.constants.appStartTime,10).toISOString().split('T')[0];}

      this.minDate = this.constants.addYears(this.constants.appStartTime, -10).toISOString().split('T')[0];
      this.maxDate = this.constants.addYears(this.constants.appStartTime, 10).toISOString().split('T')[0];
   }

   setDate(event: any, dateType: string) {
      if (dateType == 'from') {
         return this.fromDate = event.target.value;
      }
      this.toDate = event.target.value;
   }

   passBack() {
      const resultToPassBack = {
         startDate: new Date(this.fromDate),
         endDate: new Date(this.toDate)
      };
      this.activeModal.close(resultToPassBack);
   }
}
