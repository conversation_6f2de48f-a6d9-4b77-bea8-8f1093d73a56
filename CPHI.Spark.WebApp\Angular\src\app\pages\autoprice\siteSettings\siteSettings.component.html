<nav class="navbar">
    <nav class="generic">
        <h4 id="pageTitle">
            Site Settings
        </h4>


        <!-- Strategy Editor.  Uses StrategyFull -->
        <div ngbDropdown class="d-inline-block ">

            <button [disabled]="!service.strategies" class="btn btn-primary" id="dropdownBasic1" ngbDropdownToggle>
                <span>Update available pricing strategies</span>
            </button>

            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                <ng-container *ngFor="let strategy of service.strategies">
                    <button ngbDropdownItem (click)="service.openStrategy(strategy)">
                        {{strategy.strategyDetailedName}}
                    </button>
                </ng-container>

                <div class="dropdown-divider"></div>
                <button (click)="newStrategy()" ngbDropdownItem>New Strategy</button>
            </div>
        </div>


        <!-- Calc test strategy -->
        <button class="btn btn-success" *ngIf="service.constants.autopriceEnvironment.allowTestStrategy" (click)="maybeRegenerateTestStrategyValues()">Recalculate Test Strategy Values</button>
        <button class="btn btn-success" *ngIf="service.constants.autopriceEnvironment.allowTestStrategy" (click)="maybeRegenerateTestStrategyDaysToSell()">Recalculate Test Strategy Days To Sell</button>


    </nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constants.environment.customer">
    <div class="content-new">
        <div class="content-inner-new">
            <ag-grid-angular *ngIf="gridOptions" class="ag-theme-balham h-100" [gridOptions]="gridOptions"
                [components]="components">
            </ag-grid-angular>
        </div>
    </div>
</div>

<!-- Small modal to update currency fields -->
<ng-template #currencyModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            Update {{ columnToAction.headerName }}
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <input id="currencyInput" type="number" [(ngModel)]="newCurrencyValue" autofocus
            (keydown.enter)="updateCurrencyValueForSelectedRows()">
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-success" (click)="updateCurrencyValueForSelectedRows()">Save</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
    </div>
</ng-template>

<!-- Small modal to update number fields -->
<ng-template #numberModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            Update {{ columnToAction.headerName }}
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <input id="numberInput" type="time" [(ngModel)]="whenToActionTime"
            (ngModelChange)="convertWhenToActionTimeToInt()" autofocus
            (keydown.enter)="updateNumberValueForSelectedRows()">
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-success" (click)="updateNumberValueForSelectedRows()">Save</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
    </div>
</ng-template>

<!-- Small modal to update pricing/buying strategy fields -->
<ng-template #strategyModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            Update {{ columnToAction.headerName }}
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="d-inline-block" ngbDropdown container="body" popoverClass="infoPopover">
            <button class="btn btn-primary" id="newStrategyDropdown" ngbDropdownToggle>
                {{ chosenNewStrategy ? chosenNewStrategy.strategyDetailedName : 'Select a strategy...' }}
            </button>

            <div ngbDropdownMenu aria-labelledby="newStrategyDropdown">
                <ng-container *ngFor="let strategy of service.strategies">
                    <button ngbDropdownItem ngbDropdownToggle
                        class="manualToggleCloseItem" (click)="chooseNewStrategyForSelectedSites(strategy)">
                        {{ strategy.strategyDetailedName }}
                    </button>
                </ng-container>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-success" (click)="applyNewStrategyForSelectedRows()">Save</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
    </div>
</ng-template>

