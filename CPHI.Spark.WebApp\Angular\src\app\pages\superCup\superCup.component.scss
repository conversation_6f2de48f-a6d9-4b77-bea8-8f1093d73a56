@use "sass:math";

nav.navbar .generic #pageTitle {min-width:10em;}
.topDropdownButtons{display:flex;
    .dropdown-toggle{padding:0em 2em;}
}


#totalRow .team{background:none;
    .homeTeam{}
    .awayTeam{}
} 

nav.navbar h3{margin:0.3em 0em;}

#magic{left:0px}
.magicLogoHolder {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;

    img {
        height: 100%;
    }
}    

#confettiHolder{position:absolute;left:-21vw;width:100vw;height:90vh;}

.content-new{   background-size:cover!important;

    .content-inner-new{width:90%;height:90%;margin:1em auto;overflow:hidden;max-height: 93vh;

        // the container for the front page summary of all the teams 
        #matchesContainer{ width:70%;margin:1em auto;height:100%;position:relative;
            #header{ width:100%;background:var(--glass);border-radius:0.3em;padding:0.5em 2em;position: relative;
                .headline{display:flex;justify-content: space-around;align-items:center;
                    svg{}
                    .words{}
                }
                .tagLine{width:100%;text-align: center;padding:0.4em 2em;}
            }
            
            #matches{width: 100%; display: flex;flex-direction:column;margin-top: 7em;height:calc(100vh - 18em);max-height: 856px;    padding-bottom: 4em; 
                
            }
        }

        // once you have clicked on a match
        #matchContainer{width:100%;margin:1em auto;height:100%;position:relative;
            #backButton{position:absolute;top:1em;left:1em;}  
            
            #peopleContainersArea{display: flex;align-items:top;    height: calc(100vh - 13em);
                #homeContainer{left:0%;}
                #awayContainer{right:0%;}
                .teamContainer{width: 50%;                display: inline-block;
                    .teamContainerInner{
                        height:80%;
                        margin-top: 2em;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-around;
                        
                    }
                }
            }
            
            }
        
        }

        //this sits within both matchesContainer (match is within each team pairing) plus in matchContainer (used at top as a summary)
        .match{margin: 0.4em auto 2.5em auto;               display: flex;                               width: 73%;                
            .team{border-radius:0.3em;background:var(--glass);border-radius:0.3em;width:44em;height:60px;
                .teamInner{display: flex;align-items:center;height:100%;position:relative;
                    .managerMoney{    position: absolute;
                        border-radius: 0.3em;
                        background: var(--brightColourLight);
                        color: var(--grey20);;
                        
                        padding: 0.2em 0.5em;
                        font-weight: 700;}
                    .name{right:100%;position:absolute;right:4em;top:0px;display:flex;align-items: center;white-space: nowrap;}
                    .score{width:3em;text-align:center;height:100%;color:black;font-weight: 700;
                        background:var(--brightColourDark);border-radius:0px 0.2em 0.2em 0px;height:100%;position: absolute;right:-5px;top:0px;display:flex;align-items:center;justify-content: center;}
                }
            }
            
           
            .awayTeam .score{left:-5px;right:unset;border-radius:0.2em 0px 0px 0.2em !important;}
            .awayTeam.team .name{left:4em !important} //away team names should line up left

            .awayTeam .managerMoney{right:-5em;}
            .homeTeam .managerMoney{left:-5em;}
        }

        #matchContainer .match{
            
            .team{height:100px}
        } 


    //1366px monitor
    @media (max-width: 1920px)  {
        #matches{margin-top:3em!important;}
        .match{width:85%}
        .match .team{width: 50%  !important; height: 38px !important;}
        .personChip{height: 57px!important;
            img{width:62px!important;}
        }
        #peopleContainersArea{padding-top: 2em!important;}
    }

    }


   

    .chipHolder{text-align: center;}
    .chipHolder.ownRow{width:100%;}
    
    .personChip.fullSalesman{opacity:1!important}
    .personChip{border-radius:0.5em;background:var(--glass);width:23em;height:80px;display: inline-block;margin:3.2em 2em 0.2em 4em;opacity:0.5;
        .personChipInner{display: flex;align-items:center;height:100%;position:relative;
            img{width:80px;border-radius:50%;position:absolute;left:-40px}
            .name{ 
                white-space: nowrap;
                padding-left: 4em;
                    flex-direction: column;
                width: calc(100% - 6.4em);
               text-align: left;
                align-items: flex-start;
                .actualName{    width: 8.7em;                    text-overflow: ellipsis;                    overflow: hidden;}
                // .payoutCalc.topUp{display:flex;}
                .payoutCalc{display:flex;
                    .payoutRow{  display:flex;  white-space: nowrap;    align-items: center;                    width: 100%;                        display: flex;
                        .normalPayout{    margin-left: 1em;
                            
                            background: lightblue;
                            padding: 2px 9px;
                            border-radius: 4px;
                            font-weight: 700;}
                        .superPayout{    margin-left: 1em;
                            
                            background: var(--brightColour);
                            padding: 2px 9px;
                            border-radius: 4px;
                            font-weight: 700;}
                    }
                }
            }
            .score{width:3em;text-align:center;height:100%;color:black;font-weight:700;background:var(--brightColourDark) ;border-radius:0px 0.2em 0.2em 0px;
                height:100%;position: absolute;right:-5px;top:0px;display:flex;align-items:center;justify-content: center;}
        }
    


}

.superCupBackground.content-new {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url("../../../assets/imgs/events/superCup/footballBackground.jpg");
    background-size: cover;
  }

.star-wars-background.content-new {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url("../../../assets/imgs/events/superCup/star-wars-background.webp");
    background-size: cover;
}

$stars: 350; // Number of stars per layer
$depth: 300; // Depth between star layers
$speed: 3s; // Number of seconds to transition between layers
$width: 3000; // Width of the starfield
$height: 960; // Height of the starfield

.loading-screen {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #000;
    perspective: 340px;
}

.stars {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 2px;
    $box-shadow: ();

    @for $i from 0 through $stars {
        $box-shadow: $box-shadow,
        (random($width) - calc(math.div($width, 2)) + px)
        (random($height) - calc(math.div($height, 2)) + px)
        hsl(90deg, 0%, 75% + random(25%));
    }

    box-shadow: $box-shadow;
    animation: fly $speed linear infinite;
    transform-style: preserve-3d;

    &:before,
    &:after {
        content: "";
        position: absolute;
        width: inherit;
        height: inherit;
        box-shadow: inherit;
    }

    &:before {
        transform: translateZ(-$depth + px);
        animation: fade1 $speed linear infinite;
    }

    &:after {
        transform: translateZ(-$depth * 2 + px);
        animation: fade2 $speed linear infinite;
    }
}

@keyframes fly {
    from {
        transform: translateZ(0px);
    }

    to {
        transform: translateZ($depth + px);
    }
}

@keyframes fade1 {
    from {
        opacity: .5;
    }

    to {
        opacity: 1;
    }
}

@keyframes fade2 {
    from {
        opacity: 0;
    }

    to {
        opacity: .5;
    }
}

.fighter-jet-container {
    position: absolute;
    width: 300px;
    height: 250px;
    top: calc(50% - 150px);
    left: calc(50% - 150px);
    text-align: center;
    z-index: 1;
}

.fighter-jet {
    display: flex;
    justify-content: space-between;
    align-items: center;
    transform: scale(0);
    animation: zoomzoom 5s;
}

.fighter-jet-body {
    position: relative;
    height: 140px;
    width: 140px;
    border-radius: 50%;
    background-color: #D9DDD4;
    border: 5px solid gray;
    position: relative;
    box-shadow: inset -10px -10px rgba(0, 0, 0, 0.2);
}

.inset-circle {
    position: absolute;
    height: 60px;
    width: 60px;
    border: 2px solid rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    top: calc(50% - 31px);
    left: calc(50% - 31px);
    box-shadow: inset -5px -5px rgba(0, 0, 0, 0.2);
}

.light {
    position: absolute;
    height: 10px;
    width: 10px;
    background-color: red;
    border-radius: 50%;
    top: calc(50% - 5px);
}

.light.left {
    left: -20px;
}

.light.right {
    right: -20px;
}

.fighter-jet-wing {
    height: 240px;
    width: 30px;
    background-color: #D9DDD4;
    border: 5px solid gray;
}

.fighter-jet-wing.left {
    border-top-left-radius: 250px;
    border-bottom-left-radius: 250px;
    box-shadow: inset 5px 5px rgba(0, 0, 0, 0.2);
}

.fighter-jet-wing.right {
    border-top-right-radius: 250px;
    border-bottom-right-radius: 250px;
    box-shadow: inset -5px -5px rgba(0, 0, 0, 0.2);
}

.fighter-jet-wing-to-body {
    position: absolute;
    border-bottom: 70px solid #D9DDD4;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    height: 0;
    width: 70px;
    z-index: -1;
}

.fighter-jet-wing-to-body.left {
    left: 25px;
    transform: rotate(-90deg);
    border-bottom: 70px solid #AEB1AA;
}

.fighter-jet-wing-to-body.right {
    right: 25px;
    transform: rotate(90deg);
}

@keyframes zoomzoom {
    0% {
        transform: scale(0.9);
    }

    25% {
        transform: scale(1) rotate(25deg) translateX(-150px);
    }

    50% {
        transform: scale(0.9) rotate(-60deg) translate(450px, 200px);
    }

    75% {
        transform: scale(1);
    }

    90% {
        transform: scale(1);
    }

    100% {
        transform: scale(0);
    }
}

.loading-text {
    font-family: 'StarWars';
    color: #F9E711;
    
    width: 100%;
    position: absolute;
    top: 50%;
    left: 0;
    text-align: center;
    margin-top: 150px;
}

.header-container {
    display: flex;
    flex-direction: column;
    text-align: center;
    font-family: 'StarWars';

    .event-title {
        
        color: #000000;
        -webkit-text-stroke: 2px #F9E711;
    }

    .event-sub-title {
        
        color: #F9E711;
    }
}

.score-board {
    margin-top: 2em;

    table {
        width: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        color: #FFFFFF;

        thead tr {
            th {
                padding: 0.5em;
            }

            th:nth-of-type(1) {
                text-align: left;
                
                width: 40%;
            }
            
            th:nth-of-type(2) {
                text-align: center;
                
            }
            
            th:nth-of-type(3) {
                text-align: right;
                
                width: 40%;
            }
        }

        tbody {
            tr {
                td {
                    position: relative;
                    
                    padding: 0.5em;

                    .winner-symbol {
                        position: absolute;
                        width: 1.5em;
                        height: 1.5em;
                    }

                    .winner-symbol.home {
                        background-image: url('../../../assets/imgs/events/superCup/gallactic-empire-symbol.png');
                        background-size: cover;
                        left: -3em;
                    }

                    .winner-symbol.away {
                        background-image: url('../../../assets/imgs/events/superCup/rebel-alliance-symbol.png');
                        background-size: cover;
                        right: -3em;
                    }

                    span.close-match {
                        position: absolute;
                        
                        color: #97FF71;
                        line-height: 2em;
                    }

                    span.close-match.home {
                        right: 0;
                    }

                    span.close-match.away {
                        left: 0;
                    }
                }
    
                td:nth-of-type(1) {
                    text-align: left;
                }
    
                td:nth-of-type(2),
                td:nth-of-type(3) {
                    text-align: center;
                }
    
                td:nth-of-type(4) {
                    text-align: right;
                }
    
                td.winner {
                    color: #F9E711;
                }
            }

            tr:nth-child(odd) {
                background-color: #1A1A1E;
            }
        }
    }
}

.overall-result {
    color: #F9E711;
    text-align: center;
    margin-top: 1em;
    
}