﻿using System;

public class SiteSettings
{
    public int RetailerSiteId { get; set; }
    public string Name { get; set; }
    public string StrategySelectionRuleSetName { get; set; }
    public string BuyingStrategySelectionRuleSetName { get; set; }
    public string BuyingStrategySelectionRuleSet2Name { get; set; }
    public string TestStrategySelectionRuleSetName { get; set; }
    public int StrategySelectionRuleSetId { get; set; }
    public uint? StrategySelectionRuleSetUniqueIdForDg { get; set; }
    public int BuyingStrategySelectionRuleSetId { get; set; }
    public uint? BuyingStrategySelectionRuleSetUniqueIdForDg { get; set; }
    public int BuyingStrategySelectionRuleSet2Id { get; set; }
    public uint? BuyingStrategySelectionRuleSet2UniqueIdForDg { get; set; }
    public int? TestStrategySelectionRuleSetId { get; set; }
    public uint? TestStrategySelectionRuleSetUniqueIdForDg { get; set; }
    public int LocalBargainThreshold { get; set; }
    public decimal LocationMoveFixedCostPerMove { get; set; }
    public decimal LocationMovePoundPerMile { get; set; }
    public bool UpdatePricesAutomatically { get; set; }
    public int MinimumAutoPriceDecrease { get; set; }
    public int MinimumAutoPriceIncrease { get; set; }
    public decimal MinimumAutoPricePercentDecrease { get; set; }
    public decimal MinimumAutoPricePercentIncrease { get; set; }
    public bool UpdatePricesMon { get; set; }
    public bool UpdatePricesTue { get; set; }
    public bool UpdatePricesWed { get; set; }
    public bool UpdatePricesThu { get; set; }
    public bool UpdatePricesFri { get; set; }
    public bool UpdatePricesSat { get; set; }
    public bool UpdatePricesSun { get; set; }
    public bool UpdatePricesPubHolidays { get; set; }
    public int WhenToActionChangesEachDay { get; set; }
    public int MaximumOptOutDays { get; set; }
    public int CompetitorPlateRange { get; set; }
    public int LocalBargainsSearchRadius { get; set; }
    public int LocalBargainsMinRetailRating { get; set; }
    public bool IncludeUnPublishedAdsInEmailReport { get; set; }

    public int TargetMargin { get; set; }
    public decimal TargetAdditionalMech { get; set; }
    public decimal TargetPaintPrep { get; set; }
    public decimal TargetAuctionFee { get; set; }
    public decimal TargetDelivery { get; set; }
    public decimal TargetOtherCost { get; set; }


}
