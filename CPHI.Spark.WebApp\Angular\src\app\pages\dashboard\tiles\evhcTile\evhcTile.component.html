<div  class="tileHeader clickable" (click)="navigateToMainPage()">
  <div class="headerWords">
    <h5>
      EVHCs as a Proportion of Qualifying WIPs
    </h5>
  </div>
</div>


  <div class="spaceAround">
    <div class="spaceBetween column">
      <h1>{{data.EvhcCount |cph:'number':'0'}} / {{data.WipCount |cph:'number':'0'}}</h1>
    <div class="label">
      EVHCs / WIPs
    </div>
    </div>

    <div class="spaceBetween column" >
      <h1 class="percentage" [ngClass]="{'goodFont' : data.Percentage >= 1 , 'badFont' : data.Percentage < 1}">
        {{data.Percentage  |cph:'percent':'0'}}  
      </h1>
    <div class="label">
      of qualifying WIPs
    </div>
    </div>

   
 </div>