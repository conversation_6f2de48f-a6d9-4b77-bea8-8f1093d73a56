//below old ipad landscape.  Not really support.   Xxs and Xs
@media (min-width: 0px) and (max-width: 1023px) and (hover:none) {
    body {
        
    }

    .popover {
    }
}

//all ipads  
@media (min-width: 0px) and (max-width: 1920px) and (hover:none) {
    .amLoggedIn .content {
        margin-left: 0em;
        max-width: calc(100%);
        min-width: calc(100%);
    }
}

//old ipad landscape, ipadPro portrait.  Known as Sm
@media (min-width: 1024px) and (max-width: 1365px) and (hover:none) {
    body {
        
    }

    .popover {
    }

    .btn {
        padding: 0.4em 10px !important
    }
}

//iPad pro landscape
@media (min-width: 1366px) and (max-width: 1920px) and (hover:none) {
    body {
        
    }

    .popover {
    }

    .btn {
        padding: 0.4em 10px !important
    }
}




//lots of nonHD monitors.  Known as Md
@media (min-width: 0px) and (max-width: 1920px) and (hover:hover) {
    .amLoggedIn .content {
        margin-left: 3.1em;
        max-width: calc(100% - 3.1em);
        min-width: calc(100% - 3.1em);
    }

    body {
        
    }

    .popover {
        
    }
}

//full HD monitor up.  Known as Lg
@media (min-width: 1920px) and (max-width:50000px) {
    .amLoggedIn .content {
        margin-left: 3.1em;
        max-width: calc(100% - 3.1em);
        min-width: calc(100% - 3.1em);
    }

    body {
        
    }

    .popover {
        
    }
}



@media (max-width: 639px) {
    .visibleAboveXxs {
        display: none !important;
    }
}

@media (max-width: 1023px) {
    .visibleAboveXs {
        display: none !important;
    }
}

@media (max-width: 1365px) {
    .visibleAboveSm {
        display: none !important;
    }
}

@media (max-width: 1920px) {
    .visibleAboveMd {
        display: none !important;
    }
}

@media (min-width: 640px) {
    .visibleBelowXs {
        display: none !important;
    }
}

@media (min-width: 1024px) {
    .visibleBelowSm {
        display: none !important;
    }
}

@media (min-width: 1366px) {
    .visibleBelowMd {
        display: none !important;
    }
}

@media (min-width: 1920px) {
    .visibleBelowLg {
        display: none !important;
    }
}

//below full HD monitor
@media (max-width: 1920px) and (hover:hover) {}


//iPad
@media (max-width: 1920px) and (hover:none) {}

//.popover{display:none!important;}

//touch devices e.g. iPad, iPhone
@media (hover: none) {
    .visibleOnlyOnNonTouchDevice {
        display: none !important
    }
}

@media (hover: hover) {
    .visibleOnlyOnTouchDevice {
        display: none !important
    }
}

@media (max-width: 1401px) and (hover:hover) {
    body {
    }
}

@media (max-width: 1920px) and (hover:hover) {
    nav.navbar.btn {
        
        height: 2em;
    }

    .content {
        padding-top: 2.7em;
        min-height: calc(100vh)
    }
}

@media (min-width: 1350px) {
    // .navbar {
    //     height: 2.692em !important;
    // }

    .total-column {
        font-weight: 700;
    }

    .total-column.ag-cell {
        background-color: var(--grey90);
    }

    .cell-border-left {
        border-left: 1px solid rgba(189, 195, 199, 0.5) !important;
        transform: translateX(-1px);
    }

    bar-cell {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
    }

    .ag-floating-bottom bar-cell .barHolder {
        height: 50%;
    }
}

// Overriding styles when on a touch screen device
@media (hover: none) {
    nav.navbar {
        left: 0;
        width: 100%;
        padding-left: 5em;
    }

    #overallHolder {
        margin-left: 0;
    }

    #topSection.stackOnSmallScreens .spaceBetween {
        flex-direction: row;
        align-items: flex-end;

        #orderDate.dateTable {
            width: 100% !important;
        }

        #deliveryDate.dateTable {
            width: 100% !important;
        }

        #resultsTableHolder {
            width: 40%;
        }
    }

    #dateSelectionTables .buttonGroup {
        padding-bottom: 0 !important;
    }
}

@media (min-width: 1350px) {
    .fullSizeCard .navbar {
        // top: 2.7em !important;
    }
}