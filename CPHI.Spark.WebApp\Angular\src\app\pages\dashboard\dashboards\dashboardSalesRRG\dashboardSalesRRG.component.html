<!-- Main Page -->
<div *ngIf="!!dataPack" class="dashboard-grid-container sales stack-on-smaller-screens">
  <div class="dashboard-grid cols-12">
    <!-- First Row -->
    <!-- Donut New -->
    <div class="dashboard-tile  grid-row-1-4 grid-col-1-3">
      <donutTile
        [newDataEmitter]="newDataEmitter"
        [data]="donutDataNew"
        [timePeriod]="'Month'"
        [departmentName]="'New'"
      ></donutTile>
    </div>
    <!-- Donut Fleet -->
    <div class="dashboard-tile  grid-row-1-4 grid-col-3-5">
      <donutTile
        [newDataEmitter]="newDataEmitter"
        [data]="donutDataFleet"
        [timePeriod]="'Month'"
        [departmentName]="'Fleet'"
      ></donutTile>
    </div>
    <!-- Donut Used -->
    <div class="dashboard-tile  grid-row-1-4 grid-col-5-7">
      <donutTile
        [newDataEmitter]="newDataEmitter"
        [data]="donutDataUsed"
        [timePeriod]="'Month'"
        [departmentName]="'Used'"
      ></donutTile>
    </div>
    <!-- Finance and Add-On -->
    <div class="dashboard-tile   grid-col-7-9 grid-row-1-3">
      <fAndITile [data]="dataPack.FinanceAddOnSummary"></fAndITile>
    </div>

    <!-- Used Stock Health -->
    <div class="dashboard-tile   grid-col-9-11 grid-row-1-7">
      <!-- <usedStockHealth [stockHealthData]="dataPack.UsedStockHealth" [newDataEmitter]="newDataEmitter"></usedStockHealth> -->
      <usedStockMerchBySiteTile [data]="dataPack.UsedStockMerchandisingBySite" [newDataEmitter]="newDataEmitter"></usedStockMerchBySiteTile>
    </div>

    <!-- Used Stock Overage -->
    <div *ngIf="constants.environment.customer != 'Carco'" class="dashboard-tile   grid-col-11-13 grid-row-1-2">
      <stockOverageTile [newDataEmitter]="newDataEmitter" [department]="'Used'" [agedOver]="60" [data]="dataPack.OverageStockSummary"></stockOverageTile>
    </div>

    <!-- Second Row -->
    <!-- Deal Breakdown New -->
    <div class="dashboard-tile  grid-row-4-7 grid-col-1-3">
      <departmentDealBreakdown [data]="dataPack.DepartmentDealBreakdown" [dept]="'New'"></departmentDealBreakdown>
    </div>
    <!-- Deal Breakdown Fleet -->
    <div class="dashboard-tile  grid-row-4-7 grid-col-3-5">
      <departmentDealBreakdown [data]="dataPack.DepartmentDealBreakdown" [dept]="'Fleet'"></departmentDealBreakdown>
    </div>
    <!-- Deal Breakdown Used -->
    <div class="dashboard-tile grid-row-4-7 grid-col-5-7">
      <departmentDealBreakdown [data]="dataPack.DepartmentDealBreakdown" [dept]="'Used'"></departmentDealBreakdown>
    </div>
    <!-- Registrations Dacia -->
    <div class="dashboard-tile   grid-col-7-9 grid-row-3-5">
      <registrationsTile [registrationType]="'DaciaRegs'" [headerName]="'Dacia Quarterly Orders'" [data]="dataPack.RegistrationSummaries"></registrationsTile>
    </div>
    <!-- Trade Stock Overage -->
    <div *ngIf="constants.environment.customer != 'Carco'" class="dashboard-tile  grid-col-11-13 grid-row-2-3">
      <stockOverageTile  [newDataEmitter]="newDataEmitter" [department]="'Trade'" [agedOver]="30" [data]="dataPack.OverageStockSummary"></stockOverageTile>
    </div>

    <!-- Third Row -->
    <!-- Orders Taken New -->
    <div class="dashboard-tile  grid-row-7-10 grid-col-1-3">
      <thisWeekOrders [dailyNetOrders]="dataPack.DailyNetOrdersCancellationsNew" [initialStart]="service.thisWeeksOrdersStartDateNew" [dept]="'New'" [newDataEmitter]="newDataEmitter"></thisWeekOrders>
    </div>
    <!-- Orders Taken Fleet -->
    <div class="dashboard-tile  grid-row-7-10 grid-col-3-5">
      <thisWeekOrders [dailyNetOrders]="dataPack.DailyNetOrdersCancellationsFleet" [initialStart]="service.thisWeeksOrdersStartDateFleet" [dept]="'Fleet'" [newDataEmitter]="newDataEmitter"></thisWeekOrders>
    </div>
    <!-- Orders Taken Used -->
    <div class="dashboard-tile  grid-row-7-10 grid-col-5-7">
      <thisWeekOrders [dailyNetOrders]="dataPack.DailyNetOrdersCancellationsUsed" [initialStart]="service.thisWeeksOrdersStartDateUsed" [dept]="'Used'" [newDataEmitter]="newDataEmitter"></thisWeekOrders>
    </div>

    <!-- Registrations Renault -->
    <div class="dashboard-tile   grid-col-7-9 grid-row-5-7">
      <registrationsTile [registrationType]="'LMTRegs'" [headerName]="'LMT Quarterly Orders'"  [data]="dataPack.RegistrationSummaries"></registrationsTile>
    </div>
    <!-- New Stock Overage -->
    <div *ngIf="constants.environment.customer != 'Carco'" class="dashboard-tile   grid-col-11-13 grid-row-3-4">
      <stockOverageTile [newDataEmitter]="newDataEmitter" [department]="'New'" [agedOver]="90" [data]="dataPack.OverageStockSummary"></stockOverageTile>
    </div>

    <!-- Fourth Row -->
    <!-- Used Stock Merchandising -->
    <!-- <div class="dashboard-tile   grid-col-7-11 grid-row-7-10">
      <usedStockMerchTile [data]="dataPack.UsedStockMerchandising"></usedStockMerchTile>
    </div> -->
    
    <!-- Used delivered -->
    <div class="dashboard-tile grid-col-7-11 grid-row-7-10">
      <!-- <activityLevelsTile [newDataEmitter]="newDataEmitter" [data]="dataPack.ActivityLevelsAndOverdues"></activityLevelsTile> -->
      <usedDeliveredTile [newDataEmitter]="newDataEmitter" [data]="dataPack.UsedDelivered"></usedDeliveredTile>
    </div>

  </div>
</div>
