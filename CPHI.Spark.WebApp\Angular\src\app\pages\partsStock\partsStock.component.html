<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        <span>{{constants.translatedText.Dashboard_PartsStock_Title}}</span>
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.PartsStock"></sourceDataUpdate>
      </div>  
    </h4>

    <div class="buttonGroup">
      <button class="btn btn-primary" (click)="selectShowAgeing(true)"
        [ngClass]="{'active':selections.partsStock.showAgeing}">{{constants.translatedText.Dashboard_PartsStock_OverageStock}}</button>
      <button *ngIf="this.constants.environment.dashboard.showStockCover" class="btn btn-primary" (click)="selectShowAgeing(false)"
        [ngClass]="{'active':!selections.partsStock.showAgeing}">{{constants.translatedText.Dashboard_PartsStock_StockCover}}</button>
    </div>

    <!-- Dropdown for family code -->
    <stockFamilyPicker [familyCodesFromParent]="selections.partsStock.chosenFamilyCodes" [buttonClass]=""
      (updateFamilyCodes)="onUpdateFamilyCodes($event)"></stockFamilyPicker>

    <!-- Dropdown for group -->
    <stockGroupPicker *ngIf="this.constants.environment.stockGroupPicker" [groupsFromParent]="selections.partsStock.chosenGroups" [buttonClass]=""
      (updateGroups)="onUpdateGroups($event)"></stockGroupPicker>

  </nav>

</nav>




<div class="content-new">

  <div class="content-inner-new">

    <!-- If we want to see all sites -->

    <!-- Sites table based on ageing -->
    <ng-container *ngIf="!!service.partsStockAgeSitesRows && selections.partsStock.showAgeing">
      <partsStockSitesAgeingTable [isRegionalTable]="false"></partsStockSitesAgeingTable>
      <div class="tableSpacer"></div>
      <partsStockSitesAgeingTable [isRegionalTable]="true"></partsStockSitesAgeingTable>
    </ng-container>

    <!-- Sites table based on cover -->
    <ng-container *ngIf="!!service.partsStockCoverSitesRows && !selections.partsStock.showAgeing">
      <partsStockSitesCoverTable [isRegionalTable]="false"></partsStockSitesCoverTable>
      <div class="tableSpacer"></div>
      <partsStockSitesCoverTable [isRegionalTable]="true"></partsStockSitesCoverTable>
    </ng-container>


    <!-- If we have clicked a site -->
    <div class="contentInner" *ngIf="selections.partsStock.showGrid && selections.partsStock.site">

      <button class="btn btn-primary" id="backButton" (click)="revertToSiteTable()">
      </button>

      <div class="tableContainer">

      </div>

      <div class="tableContainer">
      </div>
      
    </div>


  </div>


</div>