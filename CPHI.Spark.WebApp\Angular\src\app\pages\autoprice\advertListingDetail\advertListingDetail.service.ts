import { DatePipe } from "@angular/common";
import { EventEmitter, Injectable } from "@angular/core";
import { UntypedFormControl } from "@angular/forms";
import {
   ColDef,
   GridApi,
   LineSparklineOptions,
   ValueGetterParams,
} from "ag-grid-community";
import { TableLayoutManagementService } from "src/app/components/tableLayoutManagement/tableLayoutManagement.service";
import { CPHAutoPriceColDef, CPHColDef } from "src/app/model/CPHColDef";
import { GetVehicleAdvertsWithRatingsParams } from "src/app/model/GetVehicleAdvertWithRatingsParams";
import { TableLayoutManagementParams } from "src/app/model/TableLayoutManagementParams";
import { ValuationBatchResult } from "src/app/model/ValuationBatchResult";
import {
   VehicleAdvertWithRating,
   VehicleAdvertWithRatingDTO,
} from "src/app/model/VehicleAdvertWithRating";
import { VehicleValutionBatch } from "src/app/model/VehicleValuationBatch";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { AutopriceRendererService } from "src/app/services/autopriceRenderer.service";
import { AutotraderService } from "src/app/services/autotrader.service";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { CphPipe } from "src/app/cph.pipe";
import { PriceBoardsService } from "src/app/services/priceBoards.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { AdvertListingTableColumnExplanations } from "./advertListingTableColumnExplanations";
import { AutoPriceInsightsModalComponent } from "src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component";
import { AutoPriceInsightsModalService } from "src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GlobalParamsService } from "src/app/services/globalParams.service";
import { GlobalParamKey } from "src/app/model/GlobalParam";

export interface LifecycleStatus {
   label: string;
   isChosen: boolean;
}

@Injectable({
   providedIn: "root",
})
export class AdvertListingDetailService {
   allLifecycleStatuses: string[] = [];
   defaultVehTypes: string[] = [];

   chosenVehicleTypes: Set<string>;
   chosenLifecycleStatuses: Set<string>;

   vehicleAdvertsRowData: VehicleAdvertWithRating[];

   get vehicleAdvertsRowDataAfterNavbarFilters(): VehicleAdvertWithRating[] {
      return this.vehicleAdvertsRowData.filter(
         (x) =>
            (this.chosenVehicleTypes == null ||
               this.chosenVehicleTypes.has(x.VehicleTypeDesc)) &&
            (this.chosenLifecycleStatuses == null ||
               this.chosenLifecycleStatuses.has(x.LifecycleStatus))
      );
   }

   vehicleAdvertsRowDataFiltered: VehicleAdvertWithRating[];
   chosenDate: string; //2023, 11, 12

   searchTerm: UntypedFormControl = new UntypedFormControl();

   viewByBulkUpload: boolean;
   vehicleValuationBatches: VehicleValutionBatch[];
   chosenVehicleValuationBatch: VehicleValutionBatch;
   vehicleValuationBatchResults: ValuationBatchResult[];
   newBatchLoaded: EventEmitter<void> = new EventEmitter<void>();

   includeNewVehicles: boolean;
   includeUnPublishedAds: boolean;
   defaultFilterState: any;

   tableLayoutManagementParams: TableLayoutManagementParams;

   //gridApi: GridApi

   constructor(
      private apiAccessService: ApiAccessService,
      private datePipe: DatePipe,
      public selectionsService: SelectionsService,
      private constantsService: ConstantsService,
      public tableLayoutManagementService: TableLayoutManagementService,
      private autopriceRendererService: AutopriceRendererService,
      public agGridMethodsService: AGGridMethodsService,
      public cphPipe: CphPipe,
      public priceBoardsService: PriceBoardsService,
      private getDataMethodsService: GetDataMethodsService,
      private autoPriceInsightsModalService: AutoPriceInsightsModalService,
      public modalService: NgbModal,
      private globalParmsService: GlobalParamsService
   ) {}

   initParams() {
      if (!this.includeUnPublishedAds) {
         this.includeUnPublishedAds = this.globalParmsService.getGlobalParam(
            GlobalParamKey.webAppShowDefaultShowUnPublishedVehicles
         ) as boolean;
      }
      if (!this.includeNewVehicles) {
         this.includeNewVehicles =
            this.constantsService.autopriceEnvironment.defaultShowNewVehicles;
      }

      this.defaultFilterState = {};
      if (!this.chosenDate) {
         this.chosenDate = this.datePipe.transform(
            this.constantsService.todayStart,
            "yyyy-MM-dd"
         );
      }

      this.initialiseTableLayoutManagement();

      if (!this.chosenLifecycleStatuses) {
         this.resetChosenLifecycleStatues();
      }

      if (!this.chosenVehicleTypes) {
         this.resetChosenVehTypes();
      }
   }

   resetChosenLifecycleStatues() {
      this.chosenLifecycleStatuses = new Set(
         this.constantsService.autopriceEnvironment.lifecycleStatusDefault
      ); //to make them regenerate
   }

   resetChosenVehTypes() {
      if (this.constantsService.autopriceEnvironment.defaultVehicleTypes) {
         this.chosenVehicleTypes = new Set(
            this.constantsService.autopriceEnvironment.defaultVehicleTypes
         ); //to make them regenerate
      } else {
         this.chosenVehicleTypes = null;
      }
   }

   initialiseTableLayoutManagement() {
      if (this.tableLayoutManagementParams) {
         return;
      }
      this.tableLayoutManagementParams = {
         pageName: "advertListingDetail",
         ownTableStates: null,
         standardTableStates: null,
         sharedTableStates: null,
         sparkTableStates: null,
         availableTableStates: null,
         selectedTableState: null,
         loadedTableState: null,
         usersToShareWith: null,
         gridApi: null,
         gridColumnApi: null,
         filterModel: null,
         originalColDefs: null,
      };
   }

   setExternalFilterModel(model: any) {
      if (
         this.tableLayoutManagementParams &&
         this.tableLayoutManagementParams.lastTableState
      ) {
         this.tableLayoutManagementParams.lastTableState.FilterModel = model;
      } else {
         this.initialiseTableLayoutManagement();
         this.tableLayoutManagementParams.lastTableState = {
            FilterModel: model,
         };
      }
   }

   async getData() {
      this.selectionsService.triggerSpinner.next({
         message: "Loading...",
         show: true,
      });

    const params: GetVehicleAdvertsWithRatingsParams = {
      Reg: null,
      Vin: null,
      RetailerSiteIds: null,
      EffectiveDate: this.chosenDate,
      UserEligibleSites: null,
      IncludeNewVehicles: this.includeNewVehicles,
      IncludeUnPublishedAdverts: this.includeUnPublishedAds,
      LifecycleStatuses: null,// this.chosenLifecycleStatuses ? [...this.chosenLifecycleStatuses] : null
      VehicleTypes: null,
      UseTestStrategy: false

    };

      try {
         const res: VehicleAdvertWithRatingDTO[] =
            await this.getDataMethodsService.getVehicleAdvertWithRatings(
               params
            );
         const adverts: VehicleAdvertWithRating[] = res.map(
            (x) => new VehicleAdvertWithRating(x)
         );
         this.dealWithNewData(adverts);
         this.selectionsService.triggerSpinner.next({ show: false });
      } catch (error: any) {
         console.error("Failed to retrieve vehicle adverts", error);
         this.selectionsService.triggerSpinner.next({ show: false });
      }
   }

   async getRegDataAndOpenModal(reg: string) {
      this.selectionsService.triggerSpinner.next({
         message: "Loading...",
         show: true,
      });

    const params: GetVehicleAdvertsWithRatingsParams = {
      Reg: reg,
      Vin: null,
      RetailerSiteIds: null,
      EffectiveDate: this.chosenDate,
      UserEligibleSites: null,
      IncludeNewVehicles: true,
      IncludeUnPublishedAdverts: true,
      LifecycleStatuses: null,// this.chosenLifecycleStatuses ? [...this.chosenLifecycleStatuses] : null
      VehicleTypes: null,
      UseTestStrategy: false

    };

      try {
         const res: VehicleAdvertWithRatingDTO[] =
            await this.getDataMethodsService.getVehicleAdvertWithRatings(
               params
            );
         const adverts = res.map((x) => new VehicleAdvertWithRating(x));

         const maxAdId = Math.max(...adverts.map((obj) => obj.AdId));
         const objectWithMaxId = adverts.find((obj) => obj.AdId === maxAdId);
         this.openModal(objectWithMaxId);
         this.selectionsService.triggerSpinner.next({ show: false });
      } catch (error: any) {
         console.error("Failed to retrieve vehicle advert", error);
         this.selectionsService.triggerSpinner.next({ show: false });
      }
   }

   dealWithNewData(data: VehicleAdvertWithRating[]) {
      this.vehicleAdvertsRowData = data;

      const allStatuses = new Set(data.map((x) => x.LifecycleStatus));
      this.allLifecycleStatuses = [...allStatuses];

      this.reFilterVehicleAds();
   }

   public reFilterVehicleAds() {
      if (
         this.vehicleAdvertsRowData &&
         this.tableLayoutManagementParams.gridApi
      ) {
         if (this.tableLayoutManagementParams.gridApi) {
            this.tableLayoutManagementParams.gridApi.setRowData(
               this.vehicleAdvertsRowDataAfterNavbarFilters
            );
            this.tableLayoutManagementParams.gridApi.setFilterModel(
               this.tableLayoutManagementParams.filterModel
            );
            this.tableLayoutManagementParams.gridApi.onFilterChanged();
            // this.tableLayoutManagementParams.gridColumnApi.autoSizeAllColumns();
            // this.tableLayoutManagementParams.gridApi.setColumnDefs(this.provideColumnDefs());
         }
      }
   }

   resetTableState() {
      //this.chosenTableStateLabel = null;
      this.tableLayoutManagementParams.loadedTableState = null;
      this.tableLayoutManagementParams.gridApi?.setFilterModel(
         this.defaultFilterState
      );
      this.tableLayoutManagementParams.gridColumnApi?.resetColumnState();
   }

   getVehicleValuationBatches() {
      this.apiAccessService
         .get("api/AutoPrice", "GetVehicleValuationBatches")
         .subscribe(
            (res: VehicleValutionBatch[]) => {
               this.vehicleValuationBatches = res;
               this.chosenVehicleValuationBatch = res[0];
               this.getValuationBatchResults();
            },
            (error: any) => {
               console.error(
                  "Failed to retrieve vehicle valuation batches",
                  error
               );
               this.selectionsService.triggerSpinner.next({ show: false });
            }
         );
   }

   getValuationBatchResults() {
      const params = {
         batchIds: this.chosenVehicleValuationBatch.Id.toString(),
         onlyShowBestLocation: false,
      };

      this.apiAccessService
         .post("api/AutoPrice", "GetValuationBatchResults", params)
         .subscribe(
            (res: ValuationBatchResult[]) => {
               this.vehicleValuationBatchResults = res;
               this.newBatchLoaded.emit();
               this.selectionsService.triggerSpinner.next({ show: false });
            },
            (error: any) => {
               console.error(
                  "Failed to retrieve vehicle valuation batches",
                  error
               );
               this.selectionsService.triggerSpinner.next({ show: false });
            }
         );
   }

   provideColumnDefs(): CPHAutoPriceColDef[] {
      let defs: CPHAutoPriceColDef[] = [
         //Site (3)
         {
            headerName: "Site Name",
            field: "RetailerSiteName",
            columnSection: "Site",
            colId: "RetailerSiteName",
            width: 10,
            type: "labelSetFilter",
         }, //pinned: 'left'
         {
            headerName: "Site Brand(s)",
            field: "SiteBrand",
            columnSection: "Site",
            colId: "SiteBrand",
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
            hide: true,
         }, // Only for filters
         {
            headerName: "Region",
            field: "RegionName",
            columnSection: "Site",
            colId: "RegionName",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },

         //Vehicle (13)
         {
            headerName: "Reg",
            field: "VehicleReg",
            columnSection: "Vehicle",
            colId: "VehicleReg",
            width: 10,
            type: "label",
         }, //pinned: 'left'
         {
            headerName: "Chassis",
            field: "Chassis",
            columnSection: "Vehicle",
            colId: "Chassis",
            width: 10,
            type: "label",
            hide: true,
         },
         {
            headerName: "Vehicle",
            hide: true,
            field: "VehicleType",
            columnSection: "Vehicle",
            colId: "VehicleType",
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Body Type",
            hide: true,
            field: "BodyType",
            columnSection: "Vehicle",
            colId: "BodyType",
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Make",
            field: "Make",
            columnSection: "Vehicle",
            colId: "Make",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "Model",
            field: "Model",
            columnSection: "Vehicle",
            colId: "Model",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "Model Clean",
            field: "ModelCleanedUp",
            columnSection: "Vehicle",
            colId: "ModelCleanedUp",
            width: 10,
            type: "labelSetFilter",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "30 day Sell Rate",
            field: "ModelSellRate",
            columnSection: "Vehicle",
            shouldAverageIfValue: true,
            colId: "ModelSellRate",
            hide: true,
            width: 10,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Stock Count",
            shouldAverageIfValue: true,
            field: "count",
            columnSection: "Vehicle",
            shouldTotal: true,
            colId: "count",
            hide: true,
            width: 10,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Days cover",
            shouldAverageIfValue: true,
            columnSection: "Vehicle",
            hide: true,
            valueGetter: (params) => this.daysCoverGetter(params),
            colId: "weeksCover",
            width: 10,
            type: "number1dp",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Derivative",
            field: "Derivative",
            columnSection: "Vehicle",
            colId: "Derivative",
            width: 10,
            maxWidth: 350,
            type: "label",
         }, //pinned: 'left'
         {
            headerName: "Derivative wrapped",
            field: "Derivative",
            columnSection: "Vehicle",
            hide: true,
            cellClass: "wrapText",
            colId: "Derivative1",
            width: 10,
            maxWidth: 350,
            type: "label",
         }, //pinned: 'left'
         {
            headerName: "Trim",
            field: "Trim",
            columnSection: "Vehicle",
            colId: "Trim",
            width: 10,
            type: "label",
            hide: true,
         },
         {
            headerName: "Fuel Type",
            field: "FuelType",
            columnSection: "Vehicle",
            colId: "FuelType",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },
         {
            headerName: "Transmission Type",
            field: "TransmissionType",
            columnSection: "Vehicle",
            colId: "TransmissionType",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },
         {
            headerName: "Colour",
            field: "Colour",
            columnSection: "Vehicle",
            colId: "Colour",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },
         {
            headerName: "Manufacturer Colour",
            field: "SpecificColour",
            columnSection: "Vehicle",
            colId: "SpecificColour",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },
         {
            headerName: "Owners",
            shouldAverageIfValue: true,
            field: "Owners",
            columnSection: "Vehicle",
            colId: "Owners",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },
         {
            headerName: "Age and Owners",
            valueGetter: (params) => this.ageAndOwnersGetter(params),
            field: "AgeAndOwners",
            columnSection: "Vehicle",
            colId: "AgeAndOwners",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },
         {
            headerName: "Odometer",
            shouldAverageIfValue: true,
            field: "OdometerReading",
            columnSection: "Vehicle",
            colId: "OdometerReading",
            width: 10,
            type: "number",
            hide: true,
         },
         {
            headerName: "Age Band",
            field: "AgeBand",
            columnSection: "Vehicle",
            colId: "AgeBand",
            width: 10,
            type: "labelSetFilter",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            hide: true,
            sortable: true,
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForAgeBand
               ),
         },
         {
            headerName: "Registered Date",
            field: "FirstRegisteredDate",
            columnSection: "Vehicle",
            colId: "FirstRegisteredDate",
            width: 10,
            type: "date",
            hide: true,
         },

         {
            headerName: "Value Band",
            field: "ValueBand",
            columnSection: "Vehicle",
            colId: "ValueBand",
            width: 10,
            type: "labelSetFilter",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            hide: true,
            sortable: true,
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForValueBand()
               ),
         },

         //Stock Information (6)
         {
            headerName: "Stock Date",
            hide: true,
            field: "StockDate",
            columnSection: "Stock Information",
            colId: "StockDate",
            width: 10,
            type: "date",
         },

         {
            headerName: "Days in Stock",
            field: "DaysInStock",
            columnSection: "Stock Information",
            shouldAverage: true,
            colId: "DaysInStock",
            width: 10,
            type: "number",
            filter: "agNumberColumnFilter",
            sortable: true,
            hide: !this.constantsService.autopriceEnvironment
               .defaultToDaysInStock,
         },
         {
            headerName: "Days in Stock Band",
            field: "DaysInStockBand",
            columnSection: "Stock Information",
            colId: "DaysInStockBand",
            width: 10,
            type: "labelSetFilter",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            hide: true,
            sortable: true,
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForDaysBand()
               ),
         },
         {
            headerName: "Vehicle Type",
            hide: true,
            field: "VehicleTypeDesc",
            columnSection: "Stock Information",
            colId: "VehicleTypeDesc",
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "New/Used",
            hide: true,
            field: "OwnershipCondition",
            columnSection: "Stock Information",
            colId: "OwnershipCondition",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "StockPrefix",
            hide: true,
            field: "StockPrefix",
            columnSection: "Stock Information",
            colId: "StockPrefix",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "StockNumber",
            hide: true,
            field: "StockNumber",
            columnSection: "Stock Information",
            colId: "StockNumber",
            width: 10,
            type: "label",
         },
         {
            headerName: "Stock Source",
            field: "StockSource",
            hide: true,
            columnSection: "Stock Information",
            colId: "StockSource",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "Days To Advertise",
            colId: "DaysToAdvertise",
            field: "DaysToAdvertise",
            explanation: (params) => this.provideExplanation(params),
            columnSection: "Stock Information",
            hide: true,
            width: 10,
            type: "number",
            shouldAverageIfValue: true,
            filter: "agNumberColumnFilter",
            sortable: true,
         },
      ];

      if (
         this.constantsService.autopriceEnvironment.stockReport
            .showPhysicalLocation_Col
      ) {
         defs.push({
            headerName: "Physical Location",
            field: "PhysicalLocation",
            hide: true,
            columnSection: "Stock Information",
            colId: "PhysicalLocation",
            shouldAverageIfValue: false,
            width: 10,
            type: "label",
            explanation: (params) => this.provideExplanation(params),
         });
      }

      //Test strategy
      if (this.constantsService.autopriceEnvironment.allowTestStrategy) {
         defs.push(
            {
               headerName: "Test Strategy Price",
               hide: true,
               field: "TestStrategyPrice",
               shouldAverageIfValue: true,
               columnSection: "Test Strategy",
               colId: "TestStrategyPrice",
               width: 10,
               type: "currency",
               explanation: (params) => this.provideExplanation(params),
            },
            {
               headerName: "Test Strategy Days To Sell",
               hide: true,
               field: "TestStrategyDaysToSell",
               shouldAverageIfValue: true,
               columnSection: "Test Strategy",
               colId: "TestStrategyDaysToSell",
               width: 10,
               type: "number",
               explanation: (params) => this.provideExplanation(params),
            },
            {
               headerName: "Advertised Vs Test Strategy",
               hide: true,
               shouldAverageIfValue: true,
               columnSection: "Test Strategy",
               colId: "VsTestStrategyPrice",
               field: "VsTestStrategyPrice",
               width: 10,
               type: "currencyWithPlusMinus",
               explanation: (params) => this.provideExplanation(params),
            },
            {
               headerName: "Vs Test Strategy Band",
               hide: true,
               field: "VsTestStrategyBanding",
               valueGetter: (params) =>
                  this.formatTestStrategyBandLabel(params),
               colId: "VsTestStrategyBanding",
               width: 7,
               type: "labelSetFilter",
               columnSection: "Test Strategy",
               cellRenderer: (params) =>
                  this.autopriceRendererService.autoTraderLozengeRenderer(
                     params
                  ),
               cellClass: "agAlignCentre",
               sortable: true,
               comparator: (valA, valB) =>
                  this.customSort(
                     valA,
                     valB,
                     AutotraderService.getSortOrderForVsStrategyBanding()
                  ),
            },
            {
               headerName: "Test Strategy vs Strategy",
               hide: true,
               field: "testStrategyPriceVsStrategyPrice",
               shouldAverageIfValue: true,
               columnSection: "Test Strategy",
               colId: "testStrategyPriceVsStrategyPrice",
               width: 10,
               type: "currencyWithPlusMinus",
            },
         );
      }

      defs.push(
         //Advert details (15)
         {
            headerName: "Image",
            field: "ImageURL",
            columnSection: "Advert Details",
            colId: "ImageURL",
            type: "image",
            cellRenderer:
               this.autopriceRendererService.customImageCellRenderer.bind(this),
            width: 100,
            maxWidth: 100,
            hide: true,
         }, //pinned: 'left'
         {
            headerName: "Ad Link",
            columnSection: "Advert Details",
            colId: "AdLink",
            width: 10,
            cellRenderer: this.customAdLinkRenderer.bind(this),
            type: "special",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         //{ headerName: 'Ad Link', columnSection: 'Advert', colId: 'AdLink', width: 10, cellRenderer: this.autopriceRendererService.customAdLinkRenderer.bind(this), type: 'special', hide: true },
         {
            headerName: "Attention Grabber",
            field: "AttentionGrabber",
            columnSection: "Advert Details",
            colId: "AttentionGrabber",
            width: 10,
            type: "label",
         },
         {
            headerName: "Attention Grabber wrapped",
            hide: true,
            field: "AttentionGrabber",
            cellClass: "wrapText",
            columnSection: "Advert Details",
            colId: "AttentionGrabber1",
            width: 10,
            type: "label",
         },
         {
            headerName: "Date on Forecourt",
            hide: true,
            field: "DateOnForecourt",
            columnSection: "Advert Details",
            colId: "DateOnForecourt",
            width: 10,
            type: "date",
         },
         {
            headerName: "Search Identifier",
            field: "WebSiteSearchIdentifier",
            columnSection: "Advert Details",
            colId: "WebSiteSearchIdentifier",
            width: 10,
            type: "label",
            hide: true,
         },
         {
            headerName: "Lifecycle status",
            hide: true,
            field: "LifecycleStatus",
            columnSection: "Advert Details",
            colId: "LifecycleStatus",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "Days Listed",
            field: "DaysListed",
            columnSection: "Advert Details",
            hide: this.constantsService.autopriceEnvironment
               .defaultToDaysInStock,
            colId: "DaysListed",
            width: 10,
            type: "number",
            //valueGetter: (params) => this.daysListedGetter(params),
            shouldAverage: true,
            filter: "agNumberColumnFilter",
            sortable: true,
         },
         {
            headerName: "DaysListed Band",
            hide: true,
            field: "DaysListedBand",
            columnSection: "Advert Details",
            colId: "DaysListedBand",
            width: 10,
            type: "labelSetFilter",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            sortable: true,
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForDaysBand()
               ),
         },

         //Missing info
         {
            headerName: "Has Images?",
            field: "HasImages",
            enableRowGroup: true,
            columnSection: "Advert Details",
            colId: "HasImages",
            width: 10,
            type: "boolean",
            hide: true,
         },
         {
            headerName: "No Images",
            hide: true,
            enableRowGroup: true,
            columnSection: "Advert Details",
            field: "IsMissingImages",
            colId: "IsMissingImages",
            type: "boolean",
            cellClass: "agAlignCentre",
            sortable: true,
         },
         {
            headerName: "Images Count",
            shouldAverageIfValue: true,
            field: "ImagesCount",
            enableRowGroup: true,
            columnSection: "Advert Details",
            colId: "ImagesCount",
            width: 10,
            type: "number",
            hide: true,
         },
         {
            headerName: "Images Banding",
            field: "ImagesBand",
            columnSection: "Advert Details",
            colId: "ImagesBand",
            width: 10,
            type: "labelSetFilter",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "NoVideo",
            field: "NoVideo",
            columnSection: "Advert Details",
            colId: "NoVideo",
            width: 10,
            type: "boolean",
            hide: true,
         },
         {
            headerName: "No Attn Grabber",
            field: "NoAttentionGrabber",
            columnSection: "Advert Details",
            colId: "NoAttentionGrabber",
            width: 10,
            type: "boolean",
            hide: true,
         },
         {
            headerName: "Low Quality",
            field: "IsLowQuality",
            columnSection: "Advert Details",
            colId: "IsLowQuality",
            width: 10,
            type: "boolean",
            hide: true,
         },

         // Comment
         {
            headerName: "Last Comment",
            valueGetter: (params) => this.lastCommentGetter(params),
            cellRenderer: (params) =>
               this.autopriceRendererService.lastCommentRenderer(params),
            columnSection: "Advert Details",
            colId: "lastComment",
            width: 10,
            type: "label",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },

         //Vehicle metrics (10)
         {
            headerName: "Retail Rating",
            field: "RetailRating",
            shouldAverageIfValue: true,
            hide: true,
            colId: "RetailRating",
            columnSection: "Vehicle Metrics",
            width: 10,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "RR Band",
            field: "RetailRatingBand",
            colId: "RetailRatingBand",
            columnSection: "Vehicle Metrics",
            width: 10,
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForRetailRatingBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            type: "labelSetFilter",
            hide: false,
         },
         {
            headerName: "Days To Sell",
            field: "DaysToSellAtCurrentSelling",
            shouldAverage: true,
            explanation: (params) => this.provideExplanation(params),
            colId: "DaysToSellAtCurrentSelling",
            columnSection: "Vehicle Metrics",
            width: 10,
            type: "number",
         },

         {
            headerName: "Demand",
            shouldAverageIfValue: true,
            explanation: (params) => this.provideExplanation(params),
            field: "RetailDemand",
            columnSection: "Vehicle Metrics",
            colId: "RetailDemand",
            width: 10,
            type: "percent",
            hide: true,
         },
         {
            headerName: "Supply",
            shouldAverageIfValue: true,
            explanation: (params) => this.provideExplanation(params),
            field: "RetailSupply",
            columnSection: "Vehicle Metrics",
            colId: "RetailSupply",
            width: 10,
            type: "percent",
            hide: true,
         },
         {
            headerName: "Market condition",
            shouldAverageIfValue: true,
            field: "RetailMarketCondition",
            columnSection: "Vehicle Metrics",
            colId: "RetailMarketCondition",
            width: 10,
            type: "percent",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },

         {
            headerName: "National Market condition",
            shouldAverageIfValue: true,
            explanation: (params) => this.provideExplanation(params),
            field: "NationalRetailMarketCondition",
            columnSection: "Vehicle Metrics",
            colId: "NationalRetailMarketCondition",
            width: 10,
            type: "percent",
            hide: true,
         },
         {
            headerName: "National Retail Rating",
            hide: true,
            field: "NationalRetailRating",
            shouldAverageIfValue: true,
            columnSection: "Vehicle Metrics",
            colId: "NationalRetailRating",
            width: 10,
            type: "number",
         },
         {
            headerName: "National Retail Rating Band",
            field: "NationalRetailRatingBand",
            hide: true,
            columnSection: "Vehicle Metrics",
            colId: "NationalRetailRatingBand",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "National Days To Sell",
            shouldAverageIfValue: true,
            field: "NationalRetailDaysToSell",
            hide: true,
            shouldAverage: true,
            columnSection: "Vehicle Metrics",
            colId: "NationalRetailDaysToSell",
            width: 10,
            type: "number",
         },

         //Competitor Information
         {
            headerName: "Total Competitors",
            shouldAverageIfValue: true,
            field: "CompetitorCount",
            columnSection: "Competitor Information",
            colId: "CompetitorCount",
            width: 10,
            type: "number",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Average PP",
            shouldAverageIfValue: true,
            field: "AveragePP",
            columnSection: "Competitor Information",
            colId: "AveragePP",
            width: 10,
            type: "percent1dp",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Highest PP",
            shouldAverageIfValue: true,
            field: "HighestPP",
            columnSection: "Competitor Information",
            colId: "HighestPP",
            width: 10,
            type: "percent1dp",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Lowest PP",
            shouldAverageIfValue: true,
            field: "LowestPP",
            columnSection: "Competitor Information",
            colId: "LowestPP",
            width: 10,
            type: "percent1dp",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Franchise Avg PP%",
            shouldAverageIfValue: true,
            field: "PPAverageFranchised", 
            columnSection: "Competitor Information",
            colId: "PPAverageFranchised",
            width: 10,
            type: "percent1dp",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Independent Avg PP%",
            shouldAverageIfValue: true,
            field: "PPAverageIndependents",
            columnSection: "Competitor Information",
            colId: "PPAverageIndependents",
            width: 10,
            type: "percent1dp",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         // {
         //    headerName: "Supermarket Avg PP%",
         //    shouldAverageIfValue: true,
         //    field: "PPAverageSupermarkets",
         //    columnSection: "Competitor Information",
         //    colId: "PPAverageSupermarkets",
         //    width: 10,
         //    type: "percent1dp",
         //    hide: true,
         //    explanation: (params) => this.provideExplanation(params),
         // },
         {
            headerName: "Private Avg PP%",
            shouldAverageIfValue: true,
            field: "PPAveragePrivates",
            columnSection: "Competitor Information",
            colId: "PPAveragePrivates",
            width: 10,
            type: "percent1dp",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Our PP% Rank",
            field: "OurPPRank",
            columnSection: "Competitor Information",
            colId: "OurPPRank",
            width: 10,
            type: "number",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Our Value Rank",
            field: "OurValueRank",
            columnSection: "Competitor Information",
            colId: "OurValueRank",
            width: 10,
            type: "number",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Cheapest Seller Name",
            field: "CheapestSellerName",
            columnSection: "Competitor Information",
            colId: "CheapestSellerName",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },
         {
            headerName: "Cheapest Seller Type",
            field: "CheapestSellerType",
            columnSection: "Competitor Information",
            colId: "CheapestSellerType",
            width: 10,
            type: "labelSetFilter",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "We are cheapest",
            field: "CheapestVehicle",
            columnSection: "Competitor Information",
            colId: "CheapestVehicle",
            width: 10,
            type: "boolean",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Vehicle is unique",
            field: "OnlyVehicle",
            columnSection: "Competitor Information",
            colId: "OnlyVehicle",
            width: 10,
            type: "boolean",
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Market Position Score",
            shouldAverageIfValue: true,
            field: "MarketPositionScore",
            explanation: (params) => this.provideExplanation(params),
            columnSection: "Competitor Information",
            colId: "MarketPositionScore",
            width: 10,
            type: "number",
            hide: true,
         },
         {
            headerName: "Price Up Maintain Rank",
            shouldAverageIfValue: true,
            explanation: (params) => this.provideExplanation(params),
            field: "PriceUpMaintainRank",
            columnSection: "Competitor Information",
            colId: "PriceUpMaintainRank",
            width: 10,
            type: "currencyWithPlusMinus",
            hide: true,
         },
         {
            headerName: "Price Down Improve Rank",
            shouldAverageIfValue: true,
            explanation: (params) => this.provideExplanation(params),
            field: "PriceDownImproveRank",
            columnSection: "Competitor Information",
            colId: "PriceDownImproveRank",
            width: 10,
            type: "currencyWithPlusMinus",
            hide: true,
         },
         {
            headerName: "Price Change To Be Cheapest",
            shouldAverageIfValue: true,
            explanation: (params) => this.provideExplanation(params),
            field: "PriceToBeCheapest",
            columnSection: "Competitor Information",
            colId: "PriceToBeCheapest",
            width: 10,
            type: "currencyWithPlusMinus",
            hide: true,
         },

         //Costings  (6)
         {
            headerName: "Cost Price",
            field: "SIV",
            columnSection: "Costings",
            colId: "SIV",
            width: 10,
            hide: true,
            shouldAverageIfValue: true,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Orig Purch Price",
            field: "OriginalPurchasePrice",
            shouldAverageIfValue: true,
            columnSection: "Costings",
            colId: "OriginalPurchasePrice",
            width: 10,
            hide: true,
            type: "currency",
         },
         {
            headerName: "Prep Cost",
            shouldAverageIfValue: true,
            field: "PrepCost",
            columnSection: "Costings",
            colId: "PrepCost",
            hide: true,
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Priced Profit",
            hide: true,
            columnSection: "Costings",
            colId: "PricedProfit",
            shouldAverageIfValue: true,
            field: "PricedProfit",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Vat Qualifying",
            shouldAverageIfValue: true,
            hide: true,
            columnSection: "Costings",
            colId: "IsVatQ",
            field: "IsVatQ",
            width: 10,
            type: "boolean",
         },
         {
            headerName: "HaveProfit",
            hide: true,
            columnSection: "Costings",
            colId: "HaveProfit",
            field: "PricedProfit",
            valueGetter: (parms) => parms.data?.PricedProfit !== null,
            width: 10,
            type: "boolean",
            explanation: (params) => this.provideExplanation(params),
         },

         //Valuation (12)

         {
            headerName: "Valn Adjusted PartEx",
            columnSection: "Valuation",
            field: "ValuationAdjAvPartEx",
            shouldAverageIfValue: true,
            hide: true,
            colId: "ValuationAdjAvPartEx",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Adjusted Trade",
            columnSection: "Valuation",
            field: "ValuationAdjAvTrade",
            shouldAverageIfValue: true,
            hide: true,
            colId: "ValuationAdjAvTrade",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Adjusted Private",
            columnSection: "Valuation",
            field: "ValuationAdjAvPrivate",
            shouldAverageIfValue: true,
            hide: true,
            colId: "ValuationAdjAvPrivate",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Adjusted RetailExVat",
            columnSection: "Valuation",
            field: "ValuationAdjAvRetailExVat",
            shouldAverageIfValue: true,
            hide: true,
            colId: "ValuationAdjAvRetailExVat",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Adjusted Retail",
            columnSection: "Valuation",
            field: "ValuationAdjAvRetail",
            shouldAverageIfValue: true,
            hide: true,
            colId: "ValuationAdjAvRetail",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },

         {
            headerName: "Valuation",
            columnSection: "Valuation",
            field: "RelevantValuation",
            shouldAverageIfValue: true,
            colId: "RelevantValuation",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "This Vehicle Valn vs Av.",
            hide: true,
            columnSection: "Valuation",
            field: "ThisVehicleValnVsAverage",
            shouldAverageIfValue: true,
            colId: "ThisVehicleValnVsAverage",
            width: 10,
            type: "currencyWithPlusMinus",
            explanation: (params) => this.provideExplanation(params),
         },

         {
            headerName: "Valn Adjusted M+1",
            shouldAverageIfValue: true,
            hide: true,
            field: "ValuationMonthPlus1",
            colId: "ValuationMonthPlus1",
            columnSection: "Valuation",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Adjusted M+2",
            shouldAverageIfValue: true,
            hide: true,
            field: "ValuationMonthPlus2",
            colId: "ValuationMonthPlus2",
            columnSection: "Valuation",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Adjusted M+3",
            shouldAverageIfValue: true,
            hide: true,
            field: "ValuationMonthPlus3",
            colId: "ValuationMonthPlus3",
            columnSection: "Valuation",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },

         {
            headerName: "Valn Mkt Avg PartEx",
            columnSection: "Valuation - Average Spec",
            hide: true,
            field: "ValuationMktAvPartEx",
            shouldAverageIfValue: true,
            colId: "ValuationMktAvPartEx",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Mkt Avg Trade",
            columnSection: "Valuation - Average Spec",
            hide: true,
            field: "ValuationMktAvTrade",
            shouldAverageIfValue: true,
            colId: "ValuationMktAvTrade",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Mkt Avg Private",
            columnSection: "Valuation - Average Spec",
            hide: true,
            field: "ValuationMktAvPrivate",
            shouldAverageIfValue: true,
            colId: "ValuationMktAvPrivate",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Mkt Avg RetailExVat",
            columnSection: "Valuation - Average Spec",
            hide: true,
            field: "ValuationMktAvRetailExVat",
            shouldAverageIfValue: true,
            colId: "ValuationMktAvRetailExVat",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Valn Mkt Avg Retail",
            columnSection: "Valuation - Average Spec",
            hide: true,
            field: "ValuationMktAvRetail",
            shouldAverageIfValue: true,
            colId: "ValuationMktAvRetail",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },

         //Advertised Price (7)  +2 for VIndis
         {
            headerName: "Advertised Price",
            field: "AdvertisedPrice",
            columnSection: "Advertised Price",
            colId: "SuppliedPrice",
            shouldAverageIfValue: true,
            width: 10,
            type: "currency",
         },
         {
            headerName: "Advertised Price Excl Admin",
            field: "AdvertisedPriceExclAdminFee",
            columnSection: "Advertised Price",
            colId: "AdvertisedPriceExclAdminFee",
            shouldAverageIfValue: true,
            hide: true,
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "IncludingVat",
            field: "IncludingVat",
            columnSection: "Advertised Price",
            colId: "IncludingVat",
            type: "boolean",
            width: 10,
            hide: true,
         },
         {
            headerName: "Price Position",
            shouldAverageIfValue: true,
            field: "PricePosition",
            type: "percent1dp",
            columnSection: "Advertised Price",
            colId: "PricePosition",
            width: 10,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Strategy Price Calculated?",
            field: "StrategyPriceHasBeenCalculated",
            shouldAverageIfValue: true,
            columnSection: "Advertised Price",
            colId: "StrategyPriceHasBeenCalculated",
            width: 10,
            hide: true,
            type: "boolean",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Strategy Price",
            field: "StrategyPrice",
            shouldAverageIfValue: true,
            columnSection: "Advertised Price",
            colId: "StrategyPrice",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Advertised Vs Strategy",
            shouldAverageIfValue: true,
            columnSection: "Advertised Price",
            colId: "VsStrategyPrice",
            field: "VsStrategyPrice",
            width: 10,
            type: "currencyWithPlusMinus",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Vs Strategy Band",
            field: "VsStrategyBanding",
            valueGetter: (params) => this.formatStrategyBandLabel(params),
            colId: "VsStrategyBanding",
            width: 7,
            type: "labelSetFilter",
            columnSection: "Advertised Price",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            cellClass: "agAlignCentre",
            sortable: true,
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForVsStrategyBanding()
               ),
         },
         //The price indicator lozenge
         {
            headerName: "Price Indicator",
            field: "PriceIndicatorRatingAtCurrentSelling",
            colId: "PriceIndicatorRating",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderPriceIndicatorRenderer(
                  params
               ),
            width: 7,
            minWidth: 80,
            type: "labelSetFilter",
            cellClass: "agAlignCentre",
            sortable: true,
            columnSection: "Advertised Price",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPriceIndicator()
               ),
            explanation: (params) => this.provideExplanation(params),
         }
      );

      if (
         this.constantsService.autopriceEnvironment.stockReport
            .showDMSSellingPrice_Col
      ) {
         defs.push({
            headerName: "DMS Selling Price",
            field: "DMSSellingPrice",
            columnSection: "Advertised Price",
            colId: "DMSSellingPrice",
            shouldAverageIfValue: true,
            hide: true,
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         });
      }

      if (
         this.constantsService.autopriceEnvironment.stockReport
            .showVsDMSSellingPrice_Col
      ) {
         defs.push({
            headerName: "Vs DMS Selling",
            field: "VsDMSSellingPrice",
            columnSection: "Advertised Price",
            colId: "VsDMSSellingPrice",
            shouldAverageIfValue: true,
            hide: true,
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         });
      }

      defs.push(
         //Advert Performance Rating (9)
         {
            headerName: "Perf Rtg Score",
            field: "PerfRatingScore",
            shouldAverageIfValue: true,
            hide: true,
            columnSection: "Advert Performance",
            colId: "PerfRatingScore",
            width: 10,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Perf Rtg",
            field: "PerfRating",
            shouldAverageIfValue: true,
            hide: true,
            columnSection: "Advert Performance",
            colId: "PerformanceRating",
            width: 10,
            type: "number",
         },
         {
            headerName: "Perf Rtg Band",
            field: "PerformanceRatingScoreBand",
            columnSection: "Advert Performance",
            colId: "PerformanceRatingScoreBand",
            width: 10,
            type: "labelSetFilter",
            hide: true,
         },
         {
            headerName: "Perf Rtg",
            field: "PerfRating",
            type: "labelSetFilter",
            valueGetter: (params) =>
               params.data ? params.data.PerfRating : "",
            colId: "PerfRating",
            explanation: (params) => this.provideExplanation(params),
            minWidth: 100,
            width: 10,
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            cellClass: "agAlignCentre",
            sortable: true,
            columnSection: "Advert Performance",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPerfRating()
               ),
         },
         {
            headerName: "Searches Yesterday",
            shouldAverageIfValue: true,
            field: "SearchViewsYest",
            shouldTotal: true,
            shouldAverage: true,
            columnSection: "Advert Performance",
            colId: "SearchViewsYest",
            width: 10,
            type: "number",
            hide: true,
         },
         {
            headerName: "Ad views Yesterday",
            shouldAverageIfValue: true,
            field: "AdvertViewsYest",
            shouldTotal: true,
            shouldAverage: true,
            columnSection: "Advert Performance",
            colId: "AdvertViewsYest",
            width: 10,
            type: "number",
            hide: true,
         },
         {
            headerName: "Searches 7 Day",
            shouldAverageIfValue: true,
            explanation: (params) => this.provideExplanation(params),
            field: "SearchViews7Day",
            shouldTotal: true,
            shouldAverage: true,
            columnSection: "Advert Performance",
            colId: "SearchViews7Day",
            hide: true,
            width: 10,
            type: "number",
         },
         {
            headerName: "Ad views 7 Day",
            shouldAverageIfValue: true,
            explanation: (params) => this.provideExplanation(params),
            field: "AdvertViews7Day",
            shouldTotal: true,
            shouldAverage: true,
            columnSection: "Advert Performance",
            colId: "AdvertViews7Day",
            hide: true,
            width: 10,
            type: "number",
         },

         {
            headerName: "Daily Search Views Last7",
            cellRenderer: "agSparklineCellRenderer",
            cellRendererParams: this.provideSparkLineParams(),
            field: "DailySearchViewsLast7",
            columnSection: "Advert Performance",
            colId: "DailySearchViewsLast7",
            width: 50,
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },

         {
            headerName: "Daily Advert Views Last7",
            cellRenderer: "agSparklineCellRenderer",
            cellRendererParams: this.provideSparkLineParams(),
            field: "DailyAdvertViewsLast7",
            columnSection: "Advert Performance",
            colId: "DailyAdvertViewsLast7",
            width: 50,
            hide: true,
            explanation: (params) => this.provideExplanation(params),
         },

         //Opt outs (4)
         {
            headerName: "Is Opted Out",
            hide: true,
            field: "IsOptedOut",
            columnSection: "Opt Outs",
            colId: "IsOptedOut",
            width: 10,
            type: "boolean",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Opted Out By",
            hide: true,
            field: "OptedOutBy",
            columnSection: "Opt Outs",
            colId: "OptedOutBy",
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "When Opted Out",
            hide: true,
            field: "WhenOptedOut",
            columnSection: "Opt Outs",
            colId: "WhenOptedOut",
            width: 10,
            type: "date",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Opted Out Until",
            hide: true,
            field: "OptedOutUntil",
            columnSection: "Opt Outs",
            colId: "OptedOutUntil",
            width: 10,
            type: "date",
            explanation: (params) => this.provideExplanation(params),
         },

         //Price Changes (6)

         //any price change
         {
            headerName: "Total Daily Price Moves Value",
            shouldAverageIfValue: true,
            hide: true,
            field: "TotalPriceChanges",
            columnSection: "Price Changes",
            colId: "TotalPriceChanges",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Total Daily Price Moves Quantity",
            shouldAverageIfValue: true,
            hide: true,
            field: "DailyPriceMovesCount",
            columnSection: "Price Changes",
            colId: "DailyPriceMovesCount",
            width: 10,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Most Recent Price Change Value",
            shouldAverageIfValue: true,
            hide: true,
            field: "MostRecentDailyPriceMove",
            columnSection: "Price Changes",
            colId: "MostRecentDailyPriceMove",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Most Recent Price Change Date",
            shouldAverageIfValue: true,
            hide: true,
            field: "MostRecentDailyPriceMoveDate",
            columnSection: "Price Changes",
            colId: "MostRecentDailyPriceMoveDate",
            width: 10,
            type: "date",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Days Since Price Change",
            shouldAverageIfValue: true,
            hide: true,
            field: "DaysSinceMostRecentPriceMove",
            columnSection: "Price Changes",
            colId: "DaysSinceMostRecentPriceMove",
            width: 10,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         }
      );

      defs.push(
         //manual changes
         {
            headerName: "Last Price Change Value",
            shouldAverageIfValue: true,
            hide: true,
            field: "LastPriceChangeValue",
            columnSection: "Price Changes - Manually Via Spark",
            colId: "LastPriceChangeValue",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Days Since Last Price Change",
            shouldAverageIfValue: true,
            hide: true,
            field: "DaysSinceLastPriceChange",
            columnSection: "Price Changes - Manually Via Spark",
            colId: "DaysSinceLastPriceChange",
            width: 10,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "When Price Last Manually Changed",
            hide: true,
            field: "WhenPriceLastManuallyChanged",
            columnSection: "Price Changes - Manually Via Spark",
            colId: "WhenPriceLastManuallyChanged",
            width: 10,
            type: "date",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Who Last Manually Changed",
            hide: true,
            field: "WhoLastManuallyChanged",
            columnSection: "Price Changes - Manually Via Spark",
            colId: "WhoLastManuallyChanged",
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Total Manual Price Changes Count",
            shouldAverageIfValue: true,
            hide: true,
            field: "TotalManualPriceChangesCount",
            columnSection: "Price Changes - Manually Via Spark",
            colId: "TotalManualPriceChangesCount",
            width: 10,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Total Manual Price Changes Value",
            shouldAverageIfValue: true,
            hide: true,
            field: "TotalManualPriceChangesValue",
            columnSection: "Price Changes - Manually Via Spark",
            colId: "TotalManualPriceChangesValue",
            width: 10,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         }
      );

      defs.push(
         //manual changes
         {
            headerName: "Bca VIN",
            hide: true,
            field: "BcaVin",
            columnSection: "BCA Information",
            colId: "BcaVin",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "Bca Mileage",
            hide: true,
            field: "BcaMileage",
            columnSection: "BCA Information",
            colId: "BcaMileage",
            width: 12,
            type: "number",
         },
         {
            headerName: "Days on All Sites",
            hide: true,
            field: "DaysOnAllSites",
            columnSection: "BCA Information",
            colId: "DaysOnAllSites",
            width: 15,
            type: "number",
         },
         {
            headerName: "Number of Previous Sales",
            hide: true,
            field: "NumberOfPreviousSales",
            columnSection: "BCA Information",
            colId: "NumberOfPreviousSales",
            width: 20,
            type: "number",
         },
         {
            headerName: "V5 Status",
            hide: true,
            field: "V5Status",
            columnSection: "BCA Information",
            colId: "V5Status",
            width: 10,
            type: "boolean",
         },
         {
            headerName: "Service History",
            hide: true,
            field: "ServiceHistory",
            columnSection: "BCA Information",
            colId: "ServiceHistory",
            width: 12,
            type: "boolean",
         },
         {
            headerName: "Runner",
            hide: true,
            field: "Runner",
            columnSection: "BCA Information",
            colId: "Runner",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "Sales Comment",
            hide: true,
            field: "SalesComment",
            columnSection: "BCA Information",
            colId: "SalesComment",
            width: 25,
            type: "labelSetFilter",
         },
         {
            headerName: "Hold Date",
            hide: true,
            field: "HoldDate",
            columnSection: "BCA Information",
            colId: "HoldDate",
            width: 15,
            type: "date",
         },
         {
            headerName: "Hold Code",
            hide: true,
            field: "HoldCode",
            columnSection: "BCA Information",
            colId: "HoldCode",
            width: 10,
            type: "labelSetFilter",
         },
         {
            headerName: "Hold Description",
            hide: true,
            field: "HoldDescription",
            columnSection: "BCA Information",
            colId: "HoldDescription",
            width: 20,
            type: "labelSetFilter",
         }
      );

      defs.push(
         // Today's Price Changes (5)
         {
            headerName: "New Price",
            shouldAverageIfValue: true,
            hide: true,
            field: "NewPrice",
            colId: "NewPrice",
            width: 10,
            type: "currency",
            columnSection: `Today's Price Change`,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "New Price excl AdminFee",
            shouldAverageIfValue: true,
            hide: true,
            field: "NewPriceExAdminFee",
            colId: "NewPriceExAdminFee",
            width: 10,
            type: "currency",
            columnSection: `Today's Price Change`,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "New PP%",
            shouldAverageIfValue: true,
            hide: true,
            field: "NewPP",
            colId: "NewPP",
            width: 10,
            type: "percent1dp",
            columnSection: `Today's Price Change`,
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Price Change",
            shouldAverageIfValue: true,
            hide: true,
            field: "todayPriceChange",
            colId: "todayPriceChange",
            width: 10,
            columnSection: `Today's Price Change`,
            type: "currency",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Is Small Change",
            hide: true,
            field: "IsSmallPriceChange",
            colId: "IsSmallPriceChange",
            width: 10,
            columnSection: `Today's Price Change`,
            type: "boolean",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "New Days To Sell",
            shouldAverageIfValue: true,
            hide: true,
            field: "DaysToSellAtNewPrice",
            colId: "DaysToSellAtNewPrice",
            width: 10,
            columnSection: `Today's Price Change`,
            type: "number",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "New Price Indicator",
            field: "PriceIndicatorAtNewPrice",
            colId: "PriceIndicatorAtNewPrice",
            hide: true,
            columnSection: `Today's Price Change`,
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderPriceIndicatorRenderer(
                  params
               ),
            width: 7,
            type: "labelSetFilter",
            cellClass: "agAlignCentre",
            sortable: true,
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPriceIndicator()
               ),
            explanation: (params) => this.provideExplanation(params),
         },

         //Ad status
         {
            headerName: "AT Ad Status",
            hide: false,
            field: "AutotraderAdvertStatus",
            columnSection: "Advert Details",
            colId: "AutotraderAdvertStatus",
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
         },
         {
            headerName: "Advertiser Ad Status",
            hide: true,
            field: "AdvertiserAdvertStatus",
            columnSection: "Advert Details",
            colId: "AdvertiserAdvertStatus",
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
         },

         {
            headerName: "Portal Options",
            hide: false,
            field: "PortalOptions",
            colId: "PortalOptions",
            columnSection: "Vehicle",
            maxWidth: 450,
            width: 10,
            type: "labelSetFilter",
            explanation: (params) => this.provideExplanation(params),
         },

         //Analysis Columns
         {
            headerName: "Count",
            hide: true,
            field: "count",
            shouldTotal: true,
            colId: "count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },

         //By Retail Rating
         {
            headerName: "RR <20",
            hide: true,
            field: "rrU20Count",
            shouldTotal: true,
            colId: "rrU20Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },
         {
            headerName: "RR <40",
            hide: true,
            field: "rrU40Count",
            shouldTotal: true,
            colId: "rrU40Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },
         {
            headerName: "RR <60",
            hide: true,
            field: "rrU60Count",
            shouldTotal: true,
            colId: "rrU60Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },
         {
            headerName: "RR <80",
            hide: true,
            field: "rrU80Count",
            shouldTotal: true,
            colId: "rrU80Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },
         {
            headerName: "RR 80+",
            hide: true,
            field: "rrO80Count",
            shouldTotal: true,
            colId: "rrO80Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },

         //By Days Listed
         {
            headerName: "DL <20",
            hide: true,
            field: "dlU20Count",
            shouldTotal: true,
            colId: "dlU20Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },
         {
            headerName: "DL <40",
            hide: true,
            field: "dlU40Count",
            shouldTotal: true,
            colId: "dlU40Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },
         {
            headerName: "DL <60",
            hide: true,
            field: "dlU60Count",
            shouldTotal: true,
            colId: "dlU60Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },
         {
            headerName: "DL 60+",
            hide: true,
            field: "dlO60Count",
            shouldTotal: true,
            colId: "dlO60Count",
            columnSection: "Analysis",
            maxWidth: 50,
            type: "number",
         },

         //By Strategy
         {
            headerName: "No strategy",
            hide: true,
            field: "noStrategyCount",
            shouldTotal: true,
            colId: "noStrategyCount",
            columnSection: "Analysis",
            maxWidth: 80,
            type: "number",
         },
         {
            headerName: "V. Under Strategy",
            hide: true,
            field: "veryUnderStrategyCount",
            shouldTotal: true,
            colId: "veryUnderStrategyCount",
            columnSection: "Analysis",
            maxWidth: 80,
            type: "number",
         },
         {
            headerName: "Under Strategy",
            hide: true,
            field: "underStrategyCount",
            shouldTotal: true,
            colId: "underStrategyCount",
            columnSection: "Analysis",
            maxWidth: 80,
            type: "number",
         },
         {
            headerName: "On Strategy",
            hide: true,
            field: "onStrategyCount",
            shouldTotal: true,
            colId: "onStrategyCount",
            columnSection: "Analysis",
            maxWidth: 80,
            type: "number",
         },
         {
            headerName: "Over Strategy",
            hide: true,
            field: "overStrategyCount",
            shouldTotal: true,
            colId: "overStrategyCount",
            columnSection: "Analysis",
            maxWidth: 80,
            type: "number",
         },
         {
            headerName: "V. Over Strategy",
            hide: true,
            field: "veryOverStrategyCount",
            shouldTotal: true,
            colId: "veryOverStrategyCount",
            columnSection: "Analysis",
            maxWidth: 80,
            type: "number",
         }

         //   dlU20Count: number;
         // dlU40Count: number;
         // dlU60Count: number;
         // dlO60Count: number;

         // noStrategyCount: number;
         // underStrategyCount: number;
         // veryUnderStrategyCount: number;
         // overStrategyCount: number;
         // veryOverStrategyCount: number;
         // onStrategyCount: number;
      );
      this.agGridMethodsService.workoutColWidths(
         this.vehicleAdvertsRowData,
         defs,
         10,
         6
      );
      // (defs.find(x => x.headerName == 'Reg') as ColDef).width = 100;
      // (defs.find(x => x.headerName === 'Image') as ColDef).width = 100;
      // (defs.find(x => x.headerName == 'Daily Search Views Last7') as ColDef).width = 200;
      // (defs.find(x => x.headerName == 'Daily Advert Views Last7') as ColDef).width = 200;
      // (defs.find(x => x.headerName == 'Last Comment') as ColDef).width = 200;
      // (defs.find(x => x.headerName == 'Model') as ColDef).width = 150;
      // (defs.find(x => x.headerName == 'Derivative') as ColDef).width = 350;
      // (defs.find(x => x.headerName == 'Portal Options') as ColDef).width = 350;

      return defs;
   }

   ageAndOwnersGetter(params: ValueGetterParams<any>): any {
      const row: VehicleAdvertWithRating = params.data;
      return this.autopriceRendererService.provideAgeAndOwnersString(
         row.AgeAndOwners
      );
   }

   customSort(valueA: string, valueB: string, rangesOrder: string[]): number {
      return AutotraderService.customSort(valueA, valueB, rangesOrder);
   }

   vsStrategyGetter(params: ValueGetterParams<any>): any {
      const row: VehicleAdvertWithRating = params.data;
      if (!row) {
         return "";
      }
      return row.StrategyPrice > 0
         ? row.AdvertisedPrice - row.StrategyPrice
         : 0;
   }

   provideSparkLineParams(): LineSparklineOptions {
      return {
         sparklineOptions: {
            type: "line",
            line: {
               stroke: "#4871D9",
               strokeWidth: 2,
            },
            padding: {
               top: 5,
               bottom: 5,
            },
            highlightStyle: {
               size: 7,
               fill: "rgb(255,0,0)",
               strokeWidth: 3,
            },
         },
      } as LineSparklineOptions;
   }

   getImage(params) {
      const row: VehicleAdvertWithRating = params.data;
      if (!row) {
         return "";
      }
      if (!row.ImageURL) {
         return "";
      }

      return `<img style="height: 60px; width: 80px;" src=${row.ImageURL} />`;
   }

   lastCommentGetter(params: ValueGetterParams<any>): any {
      const row: VehicleAdvertWithRating = params.data;
      if (!row) {
         return "";
      }
      if (!row.LastCommentText || !row.LastCommentName) {
         return "";
      }
      return row.LastCommentText;
   }

   formatStrategyBandLabel(params) {
      if (!params.data) {
         return "";
      }
      if (params.data.VsStrategyBanding === "OnStrategyPrice")
         return "On Strategy";
      if (params.data.VsStrategyBanding === "UnderPriced") return "Underpriced";
      if (params.data.VsStrategyBanding === "VeryUnderPriced")
         return "V. Underpriced";
      if (params.data.VsStrategyBanding === "OverPriced") return "Overpriced";
      if (params.data.VsStrategyBanding === "VeryOverPriced")
         return "V. Overpriced";
      if (params.data.VsStrategyBanding === "NoValuation")
         return "No Valuation";
      if (params.data.VsStrategyBanding === "NoStrategyPrice")
         return "No Strat Price";
      if (params.data.VsStrategyBanding === "NoAdvertisedPrice")
         return "No Price";
      return params.data.VsStrategyBanding;
   }

   formatTestStrategyBandLabel(params) {
      if (!params.data) {
         return "";
      }
      if (params.data.VsTestStrategyBanding === "OnStrategyPrice")
         return "On Strategy";
      if (params.data.VsTestStrategyBanding === "UnderPriced")
         return "Underpriced";
      if (params.data.VsTestStrategyBanding === "VeryUnderPriced")
         return "V. Underpriced";
      if (params.data.VsTestStrategyBanding === "OverPriced")
         return "Overpriced";
      if (params.data.VsTestStrategyBanding === "VeryOverPriced")
         return "V. Overpriced";
      if (params.data.VsTestStrategyBanding === "NoValuation")
         return "No Valuation";
      if (params.data.VsTestStrategyBanding === "NoStrategyPrice")
         return "No Strat Price";
      if (params.data.VsTestStrategyBanding === "NoAdvertisedPrice")
         return "No Price";
      return params.data.VsTestStrategyBanding;
   }

   provideExplanation(col: CPHColDef): string {
      const colId = col.colId;
      return AdvertListingTableColumnExplanations.getExplanation(colId);
   }

   customAdLinkRenderer(params) {
      const showIcon = params.data && !params.node.isRowPinned();
      const autoTraderListingIdentifier = params.data?.WebSiteSearchIdentifier;

      let url: string = this.constantsService.buildAdUrl(
         autoTraderListingIdentifier,
         params.data.VehicleType
      );

      return `<a *ngIf="${showIcon}" id="iconWrapper" target="_blank" href="${url}">
    <i class="fas fa-car"></i></div>`;
   }

   daysCoverGetter(params: ValueGetterParams<any>): any {
      // Early exit for pinned rows or if the value is undefined.
      if (params.node.isRowPinned()) {
         return null;
      }

      let stockCount: number = 0;
      let totalSellRate: number = 0;

      const seenSiteModels: string[] = [];

      if (params.node.group) {
         // If it's a grouped node, average the ModelSellRate and count leaves.
         const resultingLeafs = [];
         this.agGridMethodsService.aggregateUnfilteredLeafs(
            params.node,
            resultingLeafs
         );
         resultingLeafs.forEach((childNode) => {
            const row: VehicleAdvertWithRating = childNode.data;
            if (row && childNode.data.ModelSellRate) {
               stockCount++; // Assuming each leaf node represents one stock item.
               if (!seenSiteModels.includes(row.siteModelClean)) {
                  totalSellRate += childNode.data.ModelSellRate;
                  seenSiteModels.push(row.siteModelClean);
               }
            }
         });

         return totalSellRate > 0 ? (stockCount / totalSellRate) * 30 : 0;
      } else {
         // If it's a leaf node, show nothing
         if (params.data) {
            return null;
         }
      }
   }

   openModal(row: VehicleAdvertWithRating) {
      if (row.AdId == 0) {
         return;
      }

      let allAdIds: number[] = [];
      this.tableLayoutManagementParams.gridApi?.forEachNodeAfterFilterAndSort(
         (node) => {
            if (node.data && !node.isRowPinned()) {
               let nodeRow: VehicleAdvertWithRating = node.data;
               allAdIds.push(nodeRow.AdId);
            }
         }
      );

      this.autoPriceInsightsModalService.initialise(row.AdId, allAdIds);

      const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, {
         keyboard: true,
         size: "lg",
      });
      modalRef.result.then((result) => {});
   }
}
