import { Component, Input } from "@angular/core";
import { AlcopaService } from "src/app/pages/alcopaSummary/alcopaSummary.service";
import { ConstantsService } from "src/app/services/constants.service";
import { DashboardService } from "../../dashboard.service";

interface AlcopasTile {
  ThisMonth: number;
  Yesterday: number;
}

@Component({
  selector: 'alcopasSummaryTile',
  templateUrl: './alcopasSummaryTile.component.html',
  styleUrls: ['./alcopasSummaryTile.component.scss']
})

export class AlcopasSummaryTileComponent {
  @Input() public data: AlcopasTile;
  @Input() public title: string;
  @Input() public dataSource: string;

  constructor(
    public constants: ConstantsService,
    public alcopaService: AlcopaService,
    public service: DashboardService
  ) { }


  goToAlcopaPageOnClick()
  {
    this.alcopaService.initParams();
    this.alcopaService.chosenMonthStart = this.service.chosenMonthStart;
    this.alcopaService.getSiteRows();
    this.service.chosenPage = this.service.chosenSection.pages.filter(x => x.pageName == 'Alcopa')[0];
  }

  get showWarning(): boolean {
    const alcopaEntry = this.service.dataOriginUpdates.find(entry => entry.DataOrigin === 'ALCOPA');
    if (!alcopaEntry) {
      return true;
    }
  
    const now = new Date();
    const hoursSinceUpdate = (now.getTime() - new Date(alcopaEntry.LastUpdate).getTime()) / (1000 * 60 * 60);
    return hoursSinceUpdate > 24;
  }

}
