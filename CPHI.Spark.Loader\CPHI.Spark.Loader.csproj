﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <OutputType>Exe</OutputType>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWPF>true</UseWPF>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
  </PropertyGroup>
  <ItemGroup>
    <None Update="appsettings.v12Prod.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.stockpulse.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.stockpulseDev.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.rrgDev.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.rrgProd.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.rrgSpainDev.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.rrgSpainProd.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.vindisDev.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.vindisProd.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="job_scheduling_data_2_0.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Update="log4net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="appsettings.json" />
    <Content Include="dlls\Common.Logging.Core.dll" />
    <Content Include="dlls\Common.Logging.dll" />
    <Content Include="dlls\EntityFramework.dll" />
    <Content Include="dlls\EntityFramework.SqlServer.dll" />
    <Content Include="dlls\EPPlus.dll" />
    <Content Include="dlls\log4net.dll" />
    <Content Include="dlls\r\newtonsoft.Json.dll" />
    <Content Include="dlls\Quartz.dll" />
    <Content Include="dlls\Topshelf.dll" />
    <Content Include="dlls\Topshelf.Quartz.dll" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CPHI.Spark.BusinessLogic\CPHI.Spark.BusinessLogic.csproj" />
    <ProjectReference Include="..\CPHI.Spark.DataAccess\CPHI.Spark.DataAccess.csproj" />
    <ProjectReference Include="..\CPHI.Spark.Model\CPHI.Spark.Model.csproj" />
    <ProjectReference Include="..\CPHI.Spark.Repository\CPHI.Spark.Repository.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aspose.Cells" Version="24.6.0" />
    <PackageReference Include="CPHI.Monitor.PostLogs" Version="1.0.1" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Microsoft.Exchange.WebServices.NETStandard" Version="1.1.3" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="Quartz" Version="3.10.0" />
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.421302">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.0" />
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="8.0.6" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="EPPlus" Version="*******" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.61" />
    <PackageReference Include="log4net" Version="2.0.17" />
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CPHI.Spark.Model\CPHI.Spark.Model.csproj" />
    <ProjectReference Include="..\CPHI.Spark.Repository\CPHI.Spark.Repository.csproj" />
  </ItemGroup>
</Project>