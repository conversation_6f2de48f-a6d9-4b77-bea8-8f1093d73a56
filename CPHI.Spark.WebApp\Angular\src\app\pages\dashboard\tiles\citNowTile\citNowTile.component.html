<div  class="tileHeader clickable" (click)="navigateToMainPage()">
  <div class="headerWords">
    <h4>CitNOWs {{constants.translatedText.ProportionOfQualifyingWips}}
    </h4>
  </div>
</div>


  <div class="spaceAround">
    <div class="spaceBetween column">
      <h1>{{data.CitNowCount |cph:'number':0}} / {{data.WipCount |cph:'number':0}}</h1>
    <div class="label">
      CitNOWs / WIPs
    </div>
    </div>

    <div class="spaceBetween column" >
      <h1 class="percentage" [ngClass]="{'goodFont' : data.Percentage >= 0.9 , 'badFont' : data.Percentage < 0.9}">
        {{data.Percentage  |cph:'percent':0}}  
      </h1>
    <div class="label">
      Of Qualifying WIPs
    </div>
    </div>

   
 </div>