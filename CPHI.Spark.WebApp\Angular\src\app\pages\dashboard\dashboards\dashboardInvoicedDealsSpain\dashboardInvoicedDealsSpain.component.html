<!-- Main Page -->
<div class="dashboard-grid-container" *ngIf="!!service.rawDataHighlighted">
    <div id="spain-overview-grid" class="dashboard-grid cols-12">

        <!-- Total tile -->
        <div class="dashboard-tile grid-col-1-7 grid-row-1-5">

            <div class="tileHeader h4">
                <div class="headerWords mb-1">
                    {{ constants.translatedText.Total }}

                    <div *ngIf="service.isHighlightFiltersOn()" class="cancelHighlightsHolder clickable"
                        (click)="service.clearHighlights()">
                        <i class="fas fa-filter"></i>
                    </div>
                </div>
            </div>

            <div class="contentsHolder">
                <dashboardInvoicedDealsTable [pageParams]="service.getPageParams()"></dashboardInvoicedDealsTable>
            </div>

        </div>

        <!-- Status tile -->
        <div class="dashboard-tile grid-col-7-8 grid-row-1-3">
            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.Status" [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'StatusCode'"></biSlicerTile>
        </div>

        <!-- Marca/Brand/Make tile -->
        <div class="dashboard-tile grid-col-8-9 grid-row-1-3">

            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.Brand"  [labelWidth]="30"
            [pageParams]="service.getPageParams()" [fieldName]="'Make'"></biSlicerTile>
        </div>

        <!-- Placa tile -->
        <div class="dashboard-tile grid-col-9-10 grid-row-1-3">
            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.Region"  [labelWidth]="30"
            [pageParams]="service.getPageParams()" [fieldName]="'RegionDescription'"></biSlicerTile>
        </div>



        <!-- Segmento  -->
        <div class="dashboard-tile grid-col-7-8 grid-row-3-5">
            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.Segment"  [labelWidth]="30"
            [pageParams]="service.getPageParams()" [fieldName]="'SegmentCode'"></biSlicerTile>
        </div>

        <!-- Mercado  -->
        <div class="dashboard-tile grid-col-8-10 grid-row-3-5">
            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.Market"  [labelWidth]="30"
            [fieldName]="'MarketCode'" [pageParams]="service.getPageParams()">
            </biSlicerTile>
        </div>

        <!-- Concesion  -->
        <div class="dashboard-tile grid-col-10-13 grid-row-1-5">
            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.Site"  [labelWidth]="30"
            [fieldName]="'SiteDescription'" [pageParams]="service.getPageParams()">
            </biSlicerTile>
        </div>

        <!-- Model -->
        <div class="dashboard-tile grid-col-10-13 grid-row-5-9">
            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.Model"  [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'ModelCode'"></biSlicerTile>
        </div>

        <!-- Salesman  -->
        <div class="dashboard-tile grid-col-1-8 grid-row-5-9">
           <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.SalesExec"  [labelWidth]="30"
               [pageParams]="service.getPageParams()" [fieldName]="'Salesman'"></biSlicerTile>
       </div>

           <!-- Tipo Venta /Order type -->
           <div class="dashboard-tile grid-col-8-10 grid-row-5-7">
            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.OrderType"  [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'OrderType'"></biSlicerTile>
        </div>

         <!-- Motor  -->
         <div class="dashboard-tile grid-col-8-10 grid-row-7-9">
            <biSlicerTile [isDateBased]="false" [title]="constants.translatedText.Variant"  [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'Variant'"></biSlicerTile>
        </div>


        </div>
</div>

      