import {CdkDragDrop, moveItemInArray, transferArrayItem} from '@angular/cdk/drag-drop';
import {Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {GridOptions, SelectionChangedEvent} from 'ag-grid-community';
import {CphPipe} from 'src/app/cph.pipe';
import {AvailableTableStateDetails} from 'src/app/model/AvailableTableStateDetails';
import {ProfilePicSize} from 'src/app/model/main.model';
import {SharedTableStateDetails} from 'src/app/model/SharedTableStateDetails';
import {StandardTableStateDetails} from 'src/app/model/StandardTableStateDetails';
import {TableStateIdAndSortIndex} from 'src/app/model/StandardTableStateParams';
import {TableStateDetails} from 'src/app/model/TableStateDetails';
import {ApiAccessService} from 'src/app/services/apiAccess.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {TableLayoutManagementService, UserProvidedColDef} from './tableLayoutManagement.service';
import {SelectedTableState} from "../../model/SelectedTableState";

@Component({
  selector: 'tableLayoutManagement',
  templateUrl: './tableLayoutManagement.component.html',
  styleUrls: ['./tableLayoutManagement.component.scss']
})
export class TableLayoutManagementComponent implements OnInit {
  //@Input() parent: TableLayoutManagementParams;
  @Input() showCustomiseColumns: boolean;
  @ViewChild('shareTableStateModal', {static: true}) shareTableStateModal: ElementRef;
  @ViewChild('setStandardTableStateModal', {static: true}) setStandardTableStateModal: ElementRef;

  hideSubMenu: boolean = true;
  gridOptions: GridOptions;

  standardTableStatesCopy: StandardTableStateDetails[];
  availableTableStatesCopy: AvailableTableStateDetails[];
  profilePicSize: ProfilePicSize = ProfilePicSize.small;

  columnsForTypeahead: string[];
  allColumns: UserProvidedColDef[];
  canRenameLayout = false;


  constructor(
    public modalService: NgbModal,
    public cphPipe: CphPipe,
    public constantsService: ConstantsService,
    public apiAccessService: ApiAccessService,
    public service: TableLayoutManagementService,
    public selectionsService: SelectionsService
  ) {
  }

  get renameMessage(){
     if(this.canRenameLayout){return 'Rename report'}
      return `Only reports within the 'My reports' section can be renamed`
  }

  ngOnInit(): void {
    this.service.initParams();
  }


  ngOnDestroy(): void {
    this.service.parent.lastTableState = {
      State: this.service.parent.gridColumnApi.getColumnState(),
      FilterModel: this.service.parent.gridApi.getFilterModel()
    }

  }

  loadTableStateById(id: number, reportType: string, onlyColumns?: boolean, onlyFilters?: boolean) {
    this.hideSubMenu = true;

    if (reportType === 'own') {
      const report: TableStateDetails = this.service.parent.ownTableStates.find(x => x.Id === id);
      this.service.parent.selectedTableState = {
        id: report.Id,
        label: report.StateLabel,
        isShared: report.IsShared,
        isSparkState: false,
      }
    } 
    else if (reportType === 'standard') {
      const report: StandardTableStateDetails = this.service.parent.standardTableStates.find(x => x.Id === id);
      this.service.parent.selectedTableState = {
        id: report.Id,
        label: report.StateLabel,
        isSparkState: false,
      }
    } 
    else if (reportType === 'spark') {
      const report: StandardTableStateDetails = this.service.parent.sparkTableStates.find(x => x.Id === id);
      this.service.parent.selectedTableState = {
        id: report.Id,
        label: report.StateLabel,
        isSparkState: true,
      }
    } 
    else {
      const report: SharedTableStateDetails = this.service.parent.sharedTableStates.find(x => x.Id === id);
      this.service.parent.selectedTableState = {
        id: report.Id,
        label: report.StateLabel,
        isSparkState: false,
      }
    }

    this.canRenameLayout = (reportType === 'own');
    let isSparkState = reportType == 'spark' ? true : false;
    this.service.loadTableStateById(id, isSparkState, onlyColumns, onlyFilters);
  }

  maybeShareTableState() {
    this.modalService.open(this.shareTableStateModal, {
      size: 'sm',
      keyboard: false,
      ariaLabelledBy: 'modal-basic-title'
    }).result.then(() => {
      this.service.shareTableState();
    }, () => {
      this.modalService.dismissAll();
    });
  }

  maybeOpenMyReports() {
    if (!this.service.parent.ownTableStates || this.service.parent.ownTableStates?.length === 0) return;

    const button: HTMLButtonElement = document.getElementById('myReportsDropdown') as HTMLButtonElement;
    setTimeout(() => {
      button.click();
    }, 200)
  }

  openStandardReportsModal() {
    this.hideSubMenu = true;
    this.standardTableStatesCopy = JSON.parse(JSON.stringify(this.service.parent.standardTableStates));
    this.availableTableStatesCopy = JSON.parse(JSON.stringify(this.service.parent.availableTableStates));

    this.modalService.open(this.setStandardTableStateModal, {
      size: 'md',
      keyboard: false,
      ariaLabelledBy: 'modal-basic-title'
    }).result.then(() => {
      this.setStandardTableState();
      this.unsetStandardTableState();
    }, () => {
      this.modalService.dismissAll();
    });
  }

  setStandardTableState() {
    let tableStateAndSortIndex: TableStateIdAndSortIndex[] = [];
    this.standardTableStatesCopy.forEach((state, index) => {
      tableStateAndSortIndex.push({
        tableStateId: state.Id,
        sortIndex: index + 1
      })
    })

    this.service.setStandardTableState(tableStateAndSortIndex);
  }

  unsetStandardTableState() {
    const initialStandardStateIds: number[] = this.service.parent.standardTableStates.map(x => x.Id);
    const newStandardStateIds: number[] = this.standardTableStatesCopy.map(x => x.Id);
    const idsToRemove: number[] = initialStandardStateIds.filter(value => !newStandardStateIds.includes(value));
    this.service.unsetStandardTableState(idsToRemove);
  }

  drop(event: CdkDragDrop<any[]>) {
    if (event.previousContainer === event.container) {
      // Reorder items within the same list
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      // Move items between lists
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
  }

  getTopPositionForSubMenu(buttonClicked: string) {
    this.hideSubMenu = true;

    setTimeout(() => {
      const dropdown: HTMLElement = document.getElementById(`${buttonClicked}Dropdown`) as HTMLElement;
      const dropdownButtonTop: number = dropdown.getBoundingClientRect().top;
      const subMenu: HTMLElement = document.getElementById(`${buttonClicked}SubMenu`) as HTMLElement;
      const subMenuTop: number = subMenu.getBoundingClientRect().top;

      if (dropdownButtonTop === subMenuTop + 1 || subMenuTop === 0) {
        this.hideSubMenu = false;
        return;
      } else if (dropdownButtonTop > subMenuTop) {
        subMenu.style.transform = `translate(-2px, ${dropdownButtonTop - subMenuTop - 1}px)`;
      } else {
        subMenu.style.transform = `translate(-2px, -${subMenuTop - dropdownButtonTop + 1}px)`;
      }

      this.hideSubMenu = false;
    }, 150)
  }

  onSelectionChanged(event: SelectionChangedEvent) {
    this.service.parent.usersToShareWith = event.api.getSelectedRows();
  }

  columnToScrollTo(columnName: string) {
    if (!this.service.allColumns) {
      return;
    }
    let col = this.service.allColumns.find(x => x.headerName === columnName);
    if (!col) {
      return;
    }
    const columnId = col.colId;
    this.service.parent.gridApi.ensureColumnVisible(columnId);
    this.service.parent.gridApi.flashCells({columns: [columnId], fadeDelay: 5000});
  }

  justColumnsMessage() {
    return ` Click this icon to only apply the column choices and ordering from this report.   Clicking the report name will load the column choices as well as the saved report filters.`;
  }

  justFiltersMessage() {
    return ` Click this icon to only apply the filter choices from this report.   Clicking the report name will load both the filter choices as well as the saved column choices.`;

  }
}
