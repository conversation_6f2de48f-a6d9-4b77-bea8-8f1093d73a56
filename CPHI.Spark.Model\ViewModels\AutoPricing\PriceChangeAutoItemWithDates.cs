﻿using CPHI.Spark.Model.Services;
using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class PriceChangeAutoItemWithDates
   {

      public int VehicleAdvertSnapshot_Id { get; set; }
      public int RetailerSiteId { get; set; }
      public bool IsApproved { get; set; }
      public DateTime CreatedDate { get; set; }
      public decimal? WasPrice { get; set; }
      public decimal NowPrice { get; set; }
      public bool WasOptedOutOfWhenGenerated { get; set; }
      public bool CouldNotGenerateNoDaysAdvertised { get; set; }
      public bool CouldNotGenerateNoRetailRating { get; set; }
      public bool CouldNotGenerateNoRuleSet { get; set; }
      public bool CouldNotGenerateNoValuation { get; set; }
      public bool CouldGenerateNewPrice { get; set; }

      
      //for use in supressing price ups over x days listed
      public DateTime DateOnForecourt { get; set; }
      public DateTime CreatedInSparkDate { get; set; }
      public bool IsKeyChange { get; set; }
      public int DaysListed
      {
         get
         {
            return AutoPriceHelperService.CalculateDaysListed(DateOnForecourt, CreatedInSparkDate);
         }
      }
   }



}