import { Component, OnInit, HostListener } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuItemNew, MenuSection } from 'src/app/model/main.model';
import { PreferenceKey } from 'src/app/model/UserPreference';
import { DashboardService } from 'src/app/pages/dashboard/dashboard.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { UserPreferenceService } from 'src/app/services/userPreference.service';

@Component({
  selector: 'sidenav',
  templateUrl: './sidenav.component.html',
  styleUrls: ['./sidenav.component.scss']
})
export class SidenavComponent implements OnInit {
  menuSlideOutTimeout: any;
  menuIsWide: boolean;
  showMenuLabels: boolean;
  showSubMenuLabels: boolean;
  keepMenuWide: boolean;
  amHoveringSideMenu: boolean;
  
  get keepMenuFixed(): boolean{
    return this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed);
  }

  @HostListener('document:click', ['$event'])
  detectClickOutsideMenu(event) {
    if (event.pageX > 200) {
      this.keepMenuWide = false;
      this.closeSideMenu();
    }
  }

  constructor(
    public selections: SelectionsService,
    public currentRoute: ActivatedRoute,
    private userPrefsService:UserPreferenceService,
    public router: Router,
    public constants: ConstantsService,
    public dashboardService: DashboardService
  ) {
  }

  ngOnInit(): void {
    if (this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed)){
      this.makeMenuWide();
    }

    // if (this.constants.environment.showNestedSideMenu) {
    //   this.constants.menuSections[0].subItems.filter(x => x.visible)[0].isActive = true;
    // } 
    // else {
    //   this.constants.menuSections.forEach(group => {
    //     if (group.visible) {
    //       if (group.subItems.find(x => x.link === this.router.url)) {
    //         group.subItems.find(x => x.link === this.router.url).isActive = true;
    //       }
    //     }
    //   })
    // }
  }

  recordOpenMenuItems(){
    const openItems = this.constants.menuSections.filter(x=>x.expanded).map(x=>x.name);
    this.userPrefsService.setPreference(PreferenceKey.OpenSideMenuItems, openItems);
  }

  makeMenuWide() {
    this.menuIsWide = true;

    setTimeout(() => {
      this.showMenuLabels = true;
      this.showSubMenuLabels = true;
    }, 50);
  }

  onMenuSectionClicked($event: any, menuSection: MenuSection, group?: string) {
    //this.constants.chosenPage = menuSection;
    // if (menuSection.link === '/dashboard') {
    //   this.dealWithDashboardLink(menuSection, group);
    // } 
    // else {
      //if (menuItem.subItems) {
        menuSection.expanded = !menuSection.expanded;
        this.recordOpenMenuItems();
      //} 
      // else {
      //   this.constants.navigateByUrl(menuItem, group);
      // }
    //}
  }

  onMenuItemClicked($event: any, menuItem: MenuItemNew) {
    if (menuItem.link === '/dashboard') {
      this.dealWithDashboardLink(menuItem);
    } else {
      this.constants.navigateByUrl(menuItem);
    }
  }

  private dealWithDashboardLink(menuItem: MenuItemNew) {
    this.dashboardService.chosenPage = {
      pageName: menuItem.pageName,
      translatedTextField: menuItem.pageName,
      translatedTextValue: menuItem.pageName
    };

    if (menuItem.isSales) {
      this.dashboardService.chosenSection = this.constants.environment.dashboard.sections.find(x => x.translatedTextField === 'Common_Sales');
    } else {
      this.dashboardService.chosenSection = this.constants.environment.dashboard.sections.find(x => x.translatedTextField === 'Common_Aftersales');
    }

    this.constants.navigateByUrlForDashboard(menuItem);
  }

  closeSideMenu() {
    if (this.keepMenuFixed) { return; }
    this.showSubMenuLabels = false;
    this.showMenuLabels = false;
    this.menuIsWide = false;
  }

  routeIsActive(menuItem: MenuItemNew) {
    return menuItem.link === this.currentRoute.snapshot['_routerState'].url;
  }

  togglePersistMenu() {
    this.keepMenuWide = !this.keepMenuWide;
  }


  maybeHideMenu(event) {
    this.amHoveringSideMenu = false;
    
    const sidenavWidth: number = document.getElementById('sidenav').clientWidth;
    if (this.keepMenuWide || this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed) || event.pageX < sidenavWidth) {
      return;
    }
    this.closeSideMenu();
  }

  fixMenu() {
    const isFixed:boolean = this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed);

    if(isFixed){
      this.closeSideMenu();
      this.userPrefsService.setPreference(PreferenceKey.KeepMenuFixed, false);
    }
    else{
      this.userPrefsService.setPreference(PreferenceKey.KeepMenuFixed, true);
    }

  }

  showFixMenuToggle() {
    if (this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed)  && this.amHoveringSideMenu) {
      return true;
    } else if (this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed) && !this.amHoveringSideMenu) {
      return false;
    } else if (!this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed) && this.showMenuLabels) {
      return true;
    } else {
      return false;
    }
  }
}
