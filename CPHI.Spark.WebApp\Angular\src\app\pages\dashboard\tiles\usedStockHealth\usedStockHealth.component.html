<div class="dashboard-tile-inner">
  <div (click)="goToStockPage()" class="clickable tileHeader ">
    <div class="headerWords">
      <h4>{{constants.translatedText.Dashboard_UsedStockHealth}}
      </h4>
    </div>
  </div>



  <div #chartContainer id="chartContainer">

    <div class="chartAndTotalBoxWrapper">

      <h1 *ngIf="stockHealthData" id="totalBox" class="spaceBetween column">{{stockHealthData.Total|cph:'number':0}} units</h1>
      <h1 *ngIf="!stockHealthData" id="totalBox" class="spaceBetween column">{{0|cph:'number':0}} units</h1>


      <div class="stockChart" *ngIf="stockHealthData">
        <div class="barBlock" (click)="selectStockType(usedStockType)"
          *ngFor="let usedStockType of usedStockTypes;let i = index" [ngClass]="usedStockType.type"
          [ngStyle]="{'height.%':usedStockType.barHeight,'z-index':10-i}">

          <div class="label" *ngIf="usedStockType.stocksCount != 0">{{usedStockType.type}} -
            {{usedStockType.stocksCount|cph:'number':0}} ({{usedStockType.percent / 100|cph:'percent':'0'}})</div>
        </div>
      </div>

    </div>

  </div>
</div>