import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuItemNew } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
//import { StockPricingService } from '../stockPricing/stockPricing.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  constructor(
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public router: Router,
    //public stockPricingService: StockPricingService
  ) { }

  ngOnInit(): void {
    this.selectionsService.triggerSpinner.emit({ show: false });
  }

  goTo(pageName: string, subPage?: "Price Changes" | "Summary By Site" | "Vehicle Adverts" | "Opt-outs") {
    //if (subPage) { this.stockPricingService.view = subPage }

    let menuItem: MenuItemNew | undefined = this.constantsService.getMenuItemFromUrl(pageName);
    if (menuItem) { this.constantsService.navigateByUrl(menuItem ); }//, menuItem.parent
  }

  getUserFirstName() {
    let name: string = this.selectionsService.user.Name;
    return name.split(" ")[0];
  }

  overrideShowPricingPages(){
    return (this.constantsService.environment.customer==='RRGUK' || this.constantsService.environment.customer==='Vindis')
     && this.selectionsService.user.Name==='Richard ProcterNO';
  }

  showPage(pageName:string){
    if(pageName==='sitesLeague'){
      return this.constantsService.RetailerSites?.length>1;
    }
    if(this.overrideShowPricingPages()){return true;}
    if(!this.selectionsService.user.permissions.canReviewStockPrices){return false;}
    return this.constantsService.environment.sideMenu[pageName]
  }
}
