﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.RRG;
using log4net;
using MoreLinq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

public static class CalculateStrategyImpactService
{




   //THIS IS THE MAIN METHOD
   public static async Task<StrategyPriceBuildUpItem> CalculateStrategyFactorImpact(
       DateTime runDate,  //ok, just static item
       AutoTraderCompetitorClient competitorClient,
       TokenResponse tokenResponse, //ok, the token to use
       RetailerSite retailerSite,
       StrategyFactor factor,   //the factor
       AdvertParamsForStrategyCalculator parms, //the advert parameters.   THIS MUST INCLUDE EVERYTHING WE NEED
           string atBaseURL,
           StrategySelectionRuleSet ruleSet
 , decimal? preCalculatedDTSPrice,
 AutoTraderFutureValuationsClient atFutureValsClient,
 AutoTraderVehicleMetricsClient atMetricsClient, ILog logger)
   {

      if (parms.AdvertId == 203276)
      {
         { }
      }

      switch (factor.Name)
      {
         case StrategyFactorName.RetailRatingBand:
            return CalculateRetailRatingBandImpact(runDate, factor, parms, ruleSet);

         case StrategyFactorName.DaysListed:
            return CalculateDaysListedImpact(factor, parms);

         case StrategyFactorName.DaysInStock:
            return CalculateDaysInStockImpact(factor, parms);

         case StrategyFactorName.RR_DL_Matrix:
            return CalculateRRDLMatrixImpact(factor, parms, ruleSet);

         case StrategyFactorName.RR_DS_Matrix:
            return CalculateRRDSMatrix(factor, parms, ruleSet);

         case StrategyFactorName.RR_DB_Matrix:
            return CalculateRRDBMatrix(factor, parms, ruleSet);

         case StrategyFactorName.DTS_DL_Matrix:
            return CalculateDTSDLMatrix(factor, parms, ruleSet);

         case StrategyFactorName.DaysListedBand:
            return CalculateDaysListedBandImpact(runDate, factor, parms, ruleSet);

         case StrategyFactorName.DaysInStockBand:
            {
               int value = ((int)parms.DaysInStock);
               var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet);
               return impactItem;
            }

         case StrategyFactorName.RetailerName:
            return CalculateRetailerNameImpact(retailerSite, factor, parms, ruleSet);

         case StrategyFactorName.Brand:
            return CalculateBrandImpact(retailerSite, factor, parms, ruleSet);

         case StrategyFactorName.ModelName:
            return CalculateModelNameImpact(retailerSite, factor, parms, ruleSet);

         case StrategyFactorName.MatchCheapestCompetitor:
            return await CalculateCheapestCompetitorImpact(competitorClient, retailerSite, factor, parms, ruleSet);

         case StrategyFactorName.AchieveMarketPositionScore:
            return await CalculateMarketPositionImpact(competitorClient, retailerSite, factor, parms, ruleSet);

         case StrategyFactorName.WholesaleAdjustment:
            return await CalculateWholesaleAdjustmentImpact(factor, parms, ruleSet);

         case StrategyFactorName.Mileage:
            return CalculateMileageImpact(factor, parms);

         case StrategyFactorName.DaysToSell:
            {
               // if (parms.VehicleReg == "YY70SOH")
               // {
               //   { }
               // }

               if (preCalculatedDTSPrice != null)
               {
                  //we already worked it out so just use it
                  if (preCalculatedDTSPrice == 0)
                  {
                     return EarlyReturnAsCannotDoDaysToSellStrategyPrice(runDate, (int)parms.valuationAdjusted, factor, parms.snapshotId, ruleSet);
                  }
                  else
                  {
                     decimal impact = (decimal)preCalculatedDTSPrice - (decimal)parms.valuationAdjusted;
                     var daysTarget = factor.StrategyFactorItems.First().Value;
                     string extendedNote = $"Adjustment to valuation to achieve {daysTarget} days to sell";
                     return ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
                                 factor,
                                 "DaysToSell",
                                 impact,
                                 extendedNote,
                                 parms.snapshotId,
                                 ruleSet);
                  }
               }
               else
               {
                  //no pre-calcd, do it by using api calls
                  var impactItem = await WorkoutStrategyPriceBasedOnDaysToSellRequirementAndReturnImpact(
                      runDate,
                      atMetricsClient,
                      tokenResponse,
                      retailerSite,
                      factor,
                      parms,
                      atBaseURL, ruleSet, logger);
                  return impactItem;
               }
            }

         case StrategyFactorName.OnBrandCheck:
            return CalculateOnBrandImpact(factor, parms, ruleSet);

         case StrategyFactorName.MinimumProfit:
            return CalculateMinimumProfitBand(factor, parms, ruleSet);

         case StrategyFactorName.RoundToNearest:
            return CalculateRoundToNearestImpact(factor, parms, ruleSet);

         case StrategyFactorName.RoundToPriceBreak:
            return CalculateRoundToNearestPriceBreakImpact(factor, parms, ruleSet);

         case StrategyFactorName.ValuationChangeUntilSell:
            {
               var impactItem = await WorkoutStrategyPriceBasedOnValuationChangeUntilSellAndReturnImpact(
                   runDate,
                   atFutureValsClient,
                   tokenResponse,
                   retailerSite,
                   factor,
                   parms,
                   atBaseURL, ruleSet);
               return impactItem;
            }

         case StrategyFactorName.SpecificColour:
            return CalculateSpecificColourImpact(factor, parms);

         case StrategyFactorName.AgeAndOwners:
            return CalculateOwnersVsAgeImpact(factor, parms);

         case StrategyFactorName.RetailRating10sBand:
            return CalculateRetailRatingBand10sImpact(runDate, factor, parms, ruleSet);
         case StrategyFactorName.MakeFuelType:
            return CalculateMakeFuelTypeImpact(factor, parms, ruleSet);
         case StrategyFactorName.MakeAgeBand:
            return CalculateMakeAgeBandImpact(factor, parms, ruleSet);
         case StrategyFactorName.PerformanceRatingScore:
            return CalculatePerfRatingImpact(factor, parms);
         default:
            throw new Exception("Unknown factor name");
      }
   }



   private static StrategyPriceBuildUpItem CalculateRoundToNearestPriceBreakImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      var existingStrategyPrice = parms.currentStrategyPrice;
      var strategyFactorItem = factor.StrategyFactorItems.First();
      var roundWithin = strategyFactorItem.Value;
      string formattedRounding = roundWithin.ToString("C0", CultureInfo.GetCultureInfo("en-GB"));

      decimal newPrice = existingStrategyPrice;
      bool matchFound = false;
      int i = 0;
      while (i < AdvertStratifierService.ATPriceBreaks.Count && !matchFound)
      {
         var priceBreak = AdvertStratifierService.ATPriceBreaks[i];
         if (Math.Abs(existingStrategyPrice - priceBreak) <= roundWithin)
         {
            newPrice = priceBreak;
            matchFound = true; // Set the flag to true to break out of the loop
         }
         i++;
      }

      var impact = newPrice - existingStrategyPrice;

      if (impact > 0)
      {

         StrategyPriceBuildUpItem buildUp = new StrategyPriceBuildUpItem();
         buildUp.VersionName = factor.StrategyVersion.Name;
         buildUp.FactorName = factor.Name.ToString();
         buildUp.FactorItemLabel = string.Empty;
         buildUp.FactorItemValue = (int)Math.Round(impact, 0, MidpointRounding.AwayFromZero);
         buildUp.SourceValue = string.Empty;
         buildUp.Impact = impact;
         buildUp.RuleSetComment = ruleSet.Comment;
         buildUp.VehicleAdvertSnapshotId = parms.snapshotId;
         buildUp.ExtendedNotes = $"Round to price break as within {formattedRounding}";
         buildUp.StrategyFactorItemId = strategyFactorItem.Id;
         buildUp.StrategySelectionRuleSetId = ruleSet.Id;

         return buildUp;


      }
      else
      {
         return null;
      }
   }

   private static StrategyPriceBuildUpItem CalculateRoundToNearestImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      decimal existingStrategyPrice = parms.currentStrategyPrice;
      var strategyFactorItem = factor.StrategyFactorItems.First();


      decimal roundToNearest = strategyFactorItem.Value;
      decimal rounded = RoundToNearestEndingX(existingStrategyPrice, roundToNearest); // 14751

      string formattedRounding = roundToNearest.ToString("C0", CultureInfo.GetCultureInfo("en-GB"));
      var impact = rounded - existingStrategyPrice;

      if (impact > 0)
      {
         var impactItem = new StrategyPriceBuildUpItem();
         impactItem.VersionName = factor.StrategyVersion.Name;
         impactItem.FactorName = factor.Name.ToString();
         impactItem.FactorItemLabel = strategyFactorItem.Label;
         impactItem.FactorItemValue = strategyFactorItem.Value;
         impactItem.Impact = impact;
         impactItem.RuleSetComment = ruleSet.Comment;
         impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem.ExtendedNotes = $"Round to nearest {formattedRounding}";
         impactItem.StrategyFactorItemId = strategyFactorItem.Id;
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;

         //var impactItem = new StrategyFactorItemVehicleWebsiteRating(strategyFactorItem.Id, parms.snapshotId,  $"Round to nearest {formattedRounding}", impact, null, strategySelectionRuleSetId);
         return impactItem;
      }
      else
      {
         return null;
      }
   }

   private static async Task<StrategyPriceBuildUpItem> CalculateWholesaleAdjustmentImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      decimal existingStrategyPrice = parms.currentStrategyPrice;

      var adjustmentPct = factor.StrategyFactorItems.First(x => x.Label == "AdjustmentPct");
      var adjustmentAmount = factor.StrategyFactorItems.First(x => x.Label == "AdjustmentValue");

      decimal adjustedValue = existingStrategyPrice;

      if (adjustmentPct?.Value != null)
      {
         adjustedValue = (adjustedValue * (adjustmentPct.Value / 100));
      }

      if (adjustmentAmount?.Value != null)
      {
         adjustedValue = adjustedValue + adjustmentAmount.Value;
      }

      var impact = adjustedValue - existingStrategyPrice;

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = adjustmentPct.Label;
      impactItem.FactorItemValue = adjustmentPct.Value;
      impactItem.Impact = impact;
      impactItem.RuleSetComment = ruleSet.Comment;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = $"Adjust to {adjustmentPct.Value}% and by £{adjustmentAmount.Value}";
      impactItem.StrategyFactorItemId = adjustmentPct.Id;
      impactItem.StrategySelectionRuleSetId = ruleSet.Id;

      return impactItem;
   }


   private static decimal RoundToNearestEndingX(decimal price, decimal targetEnding)
   {
      decimal baseValue = Math.Round(price / 100) * 100; // Round to the nearest hundred
      decimal lower = baseValue - (100 - targetEnding);  // Find lower value ending in targetEnding
      decimal upper = baseValue + targetEnding;          // Find upper value ending in targetEnding

      // Choose the closer one, rounding up if exactly halfway
      return (price - lower < upper - price) ? lower : upper;
   }




   private static StrategyPriceBuildUpItem CalculateMinimumProfitBand(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      var profitAtStrategyPrice = PricedProfitCalculatorService.CalculatePricedProfit((int)parms.currentStrategyPrice, parms.siv, parms.prepCost, parms.ownershipCondition, parms.isVatQ, (int)Math.Round(parms.originalPurchasePrice, 0, MidpointRounding.AwayFromZero));
      var minProfit = factor.StrategyFactorItems.First().Value;
      if (profitAtStrategyPrice < minProfit)
      {
         var newStrategyPrice = PricedProfitCalculatorService.CalculateRevisedSellingPrice(minProfit, parms.siv, parms.prepCost, parms.ownershipCondition, parms.isVatQ, (int)Math.Round(parms.originalPurchasePrice, 0, MidpointRounding.AwayFromZero));
         var impact = newStrategyPrice - parms.currentStrategyPrice;
         string formattedMinProfit = minProfit.ToString("C", CultureInfo.GetCultureInfo("en-GB"));
         var impactItem = new StrategyPriceBuildUpItem();
         impactItem.VersionName = factor.StrategyVersion.Name;
         impactItem.FactorName = factor.Name.ToString();
         impactItem.FactorItemLabel = factor.StrategyFactorItems.First().Label;
         impactItem.FactorItemValue = factor.StrategyFactorItems.First().Value;
         impactItem.Impact = impact;
         impactItem.RuleSetComment = ruleSet.Comment;
         impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
         impactItem.ExtendedNotes = $"Minimum Profit ({formattedMinProfit})";
         impactItem.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;

         return impactItem;
      }
      else
      {
         //profit is not below min profit level, that's ok then - no adjustment to make
         return null;
      }
   }

   private static StrategyPriceBuildUpItem CalculateOnBrandImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string thisItemLabel = parms.onBrand ? "OnBrand" : "OffBrand";
      //decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(thisItemLabel, parms.strategyPrice, factor, true);

      var factorItem = factor.StrategyFactorItems.FirstOrDefault(x => x.Label == thisItemLabel);
      if (factorItem == null)
      {
         throw new Exception($"Unknown label {thisItemLabel} when evaluating strategy for {factor.Name}");
      }
      decimal impact = parms.currentStrategyPrice * ((factorItem.Value / 100) - 1);

      var impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(factor, thisItemLabel, thisItemLabel, impact, parms.snapshotId, ruleSet, null);
      return impactItem;
   }

   private static async Task<StrategyPriceBuildUpItem> CalculateCheapestCompetitorImpact(
       AutoTraderCompetitorClient competitorClient,
       RetailerSite retailerSite,
       StrategyFactor factor,
       AdvertParamsForStrategyCalculator parms,
       StrategySelectionRuleSet ruleSet)
   {
      //do competitor search
      return await WorkoutStrategyPriceForLocalCompetitor(
          competitorClient,
          retailerSite,
          parms,
          factor,
          ruleSet
          );
   }


   private static async Task<StrategyPriceBuildUpItem> CalculateMarketPositionImpact(
       AutoTraderCompetitorClient competitorClient,
       RetailerSite retailerSite,
       StrategyFactor factor,
       AdvertParamsForStrategyCalculator parms,
       StrategySelectionRuleSet ruleSet)
   {
      //do competitor search
      return await WorkoutStrategyPriceForMarketPositionScore(
          competitorClient,
          retailerSite,
          parms,
          factor,
          ruleSet
          );
   }

   private static StrategyPriceBuildUpItem CalculateRetailerNameImpact(RetailerSite retailerSite, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = retailerSite.Name;
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false);
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          parms.RetailerName,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          string.Empty
          );
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateBrandImpact(RetailerSite retailerSite, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = parms.Make;
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          string.Empty
          );
      return impactItem;
   }
   private static StrategyPriceBuildUpItem CalculateModelNameImpact(RetailerSite retailerSite, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = parms.Model;
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          string.Empty
          );
      return impactItem;
   }
   private static StrategyPriceBuildUpItem CalculateMakeFuelTypeImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = $"{parms.Make}|{parms.FuelType}";
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          string.Empty
          );
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateMakeAgeBandImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      string value = $"{parms.Make}|{parms.AgeBand}";
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactor(value, parms.currentStrategyPrice, factor, false);
      if (impact == 0)
      {
         return null;
      }
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          value,
          value,
          impact,
          parms.snapshotId,
          ruleSet,
          string.Empty
          );
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateDaysListedBandImpact(DateTime runDate, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      int value = ((int)parms.DaysListed);
      var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet);
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateDTSDLMatrix(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.DaysToSell == null)
      {
         throw new Exception("No Days to Sell");
      }
      if (parms.DaysInStock == null)
      {
         throw new Exception("No Days Listed");
      }

      int dts = ((int)parms.DaysToSell);
      int daysListed = ((int)parms.DaysListed);
      if (daysListed > 999)
      {
         daysListed = 999;
      }

      // Find the break point for retail rating
      List<int> dtsBreaks = factor.StrategyFactorItems
          .Select(x => ExtractDTSValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int dtsBreak;
      try
      {
         dtsBreak = dtsBreaks.First(x => x >= dts);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching retail rating found.");
      }

      // Filter items based on the retail rating break point
      var itemsForDTSBand = factor.StrategyFactorItems
          .Where(x => ExtractDTSValue(x.Label) == dtsBreak)
          .OrderBy(x => ExtractDLValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      try
      {
         // Find the final item based on days listed
         finalItem = itemsForDTSBand.First(x => ExtractDLValue(x.Label) >= daysListed);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching days listed found.");
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateRRDSMatrix(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception("No Retail Rating");
      }
      if (parms.DaysInStock == null)
      {
         throw new Exception("No Days Listed");
      }

      int rr = ((int)parms.RetailRating);
      int daysInStock = ((int)parms.DaysInStock);

      // Find the break point for retail rating
      List<int> rrBreaks = factor.StrategyFactorItems
          .Select(x => ExtractRRValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int rrBreak;
      try
      {
         rrBreak = rrBreaks.First(x => x >= rr);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching retail rating found.");
      }

      // Filter items based on the retail rating break point
      var itemsForRetailRating = factor.StrategyFactorItems
          .Where(x => ExtractRRValue(x.Label) == rrBreak)
          .OrderBy(x => ExtractDSValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      try
      {
         // Find the final item based on days in stock
         finalItem = itemsForRetailRating.First(x => ExtractDSValue(x.Label) >= daysInStock);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching days in stock found.");
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateRRDBMatrix(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception("No Retail Rating");
      }
      if (parms.DaysBookedIn == null)
      {
         throw new Exception("No Days Booked In");
      }

      int rr = ((int)parms.RetailRating);
      int daysBookedIn = ((int)parms.DaysBookedIn);

      // Find the break point for retail rating
      List<int> rrBreaks = factor.StrategyFactorItems
          .Select(x => ExtractRRValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int rrBreak;
      try
      {
         rrBreak = rrBreaks.First(x => x >= rr);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching retail rating found.");
      }

      // Filter items based on the retail rating break point
      var itemsForRetailRating = factor.StrategyFactorItems
          .Where(x => ExtractRRValue(x.Label) == rrBreak)
          .OrderBy(x => ExtractDBValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      try
      {
         // Find the final item based on days booked in
         finalItem = itemsForRetailRating.First(x => ExtractDBValue(x.Label) >= daysBookedIn);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching days booked in found.");
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateRRDLMatrixImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception("No Retail Rating");
      }
      if (parms.DaysListed == null)
      {
         throw new Exception("No Days Listed");
      }

      int rr = ((int)parms.RetailRating);
      int daysListed = ((int)parms.DaysListed);
      if (daysListed > 999)
      {
         daysListed = 999;
      }

      // Find the break point for retail rating
      List<int> rrBreaks = factor.StrategyFactorItems
          .Select(x => ExtractRRValue(x.Label))
          .OrderBy(x => x)
          .Distinct()
          .ToList();

      int rrBreak;
      try
      {
         rrBreak = rrBreaks.First(x => x >= rr);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching retail rating found.");
      }

      // Filter items based on the retail rating break point
      var itemsForRetailRating = factor.StrategyFactorItems
          .Where(x => ExtractRRValue(x.Label) == rrBreak)
          .OrderBy(x => ExtractDLValue(x.Label))
          .ToList();

      StrategyFactorItem finalItem;
      try
      {
         // Find the final item based on days listed
         finalItem = itemsForRetailRating.First(x => ExtractDLValue(x.Label) >= daysListed);
      }
      catch (InvalidOperationException)
      {
         throw new InvalidOperationException("No matching days listed found.");
      }

      decimal impact = parms.currentStrategyPrice * (finalItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = finalItem.Label;
      impactItem.FactorItemValue = finalItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = finalItem.Id;

      if (ruleSet != null)
      {
         impactItem.StrategySelectionRuleSetId = ruleSet.Id;
         impactItem.RuleSetComment = ruleSet.Comment;
      }

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateDaysInStockImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.DaysInStock == null)
      {
         throw new Exception("No Days Listed");
      }

      int daysInStock = ((int)parms.DaysInStock);
      var orderedItems = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label.Split("-")[0])).ToList();
      var matchingItem = orderedItems.FirstOrDefault(x => daysInStock >= ExtractMinMaxValue(x.Label).Item1 && daysInStock <= ExtractMinMaxValue(x.Label).Item2);

      if (matchingItem == null)
      {
         throw new Exception($"Failed to finding matching strategy factor item for days in stock {daysInStock}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateRetailRatingBandImpact(DateTime runDate, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception($"No Retail Rating");
      }
      int value = ((int)parms.RetailRating);
      var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet);
      return impactItem;
   }
   private static StrategyPriceBuildUpItem CalculateRetailRatingBand10sImpact(DateTime runDate, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.RetailRating == null)
      {
         throw new Exception($"No Retail Rating");
      }
      int value = ((int)parms.RetailRating);
      var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet);
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculatePerformanceRatingScoreImpact(DateTime runDate, StrategyFactor factor, AdvertParamsForStrategyCalculator parms, StrategySelectionRuleSet ruleSet)
   {
      if (parms.PerfRatingScore == null)
      {
         // Skip this factor if PerfRatingScore is null
         return null;
      }
      int value = ((int)parms.PerfRatingScore);
      var impactItem = ApplyFactorAndCreateFactorRatings(runDate, factor, value, parms.currentStrategyPrice, parms.snapshotId, ruleSet);
      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateDaysListedImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.DaysListed == null)
      {
         throw new Exception("No Days Listed");
      }

      int daysListed = ((int)parms.DaysListed);
      if (daysListed > 999)
      {
         daysListed = 999;
      }
      var orderedItems = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label.Split("-")[0])).ToList();
      var matchingItem = orderedItems.FirstOrDefault(x => daysListed >= ExtractMinMaxValue(x.Label).Item1 && daysListed <= ExtractMinMaxValue(x.Label).Item2);

      if (matchingItem == null)
      {
         throw new Exception($"Failed to finding matching strategy factor item for days listed {daysListed}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculatePerfRatingImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.PerfRatingScore == null)
      {
         throw new Exception("No Performance Rating");
      }

      int perfRating = ((int)parms.PerfRatingScore);
      var orderedItems = factor.StrategyFactorItems.OrderBy(x => int.Parse(x.Label.Split("-")[0])).ToList();
      var matchingItem = orderedItems.FirstOrDefault(x => perfRating >= ExtractMinMaxValue(x.Label).Item1 && perfRating <= ExtractMinMaxValue(x.Label).Item2);

      if (matchingItem == null)
      {
         throw new Exception($"Failed to finding matching strategy factor item for days listed {perfRating}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateMileageImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.OdometerReading == null)
      {
         return null;
         //throw new Exception("No Odometer Reading");
      }

      int odometerReading = ((int)parms.OdometerReading);

      var orderedItems = factor.StrategyFactorItems
         .Select(x => new StrategyFactorItem() { Id = x.Id, Label = x.Label.Replace("<", ""), Value = x.Value })
         .ToList();


      var matchingItem = orderedItems
         .Where(x => odometerReading < int.Parse(x.Label))
         .OrderBy(x => x.Label)
         .FirstOrDefault();

      if (matchingItem == null)
      {
         return null;
         //throw new Exception($"Failed to finding matching strategy factor item for odometer {odometerReading}");
      }
      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateSpecificColourImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.SpecificColour == null)
      {
         return null;
      }


      string specificColour = parms.SpecificColour;
      var matchingItem = factor.StrategyFactorItems.FirstOrDefault(x => specificColour.StartsWith(x.Label, StringComparison.OrdinalIgnoreCase));

      if (matchingItem == null)
      {
         return null;
      }

      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = specificColour;// string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   private static StrategyPriceBuildUpItem CalculateOwnersVsAgeImpact(StrategyFactor factor, AdvertParamsForStrategyCalculator parms)
   {
      if (parms.AgeAndOwners == null)
      {
         return null;
      }

      var matchingItem = factor.StrategyFactorItems.FirstOrDefault(x => x.Label == parms.AgeAndOwners);

      if (matchingItem == null)
      {
         return null;
      }

      decimal impact = parms.currentStrategyPrice * (matchingItem.Value / 100 - 1);

      var impactItem = new StrategyPriceBuildUpItem();
      impactItem.VersionName = factor.StrategyVersion.Name;
      impactItem.FactorName = factor.Name.ToString();
      impactItem.FactorItemLabel = matchingItem.Label;
      impactItem.FactorItemValue = matchingItem.Value;
      impactItem.SourceValue = parms.AgeAndOwners; // string.Empty; //maybe we should put something in here
      impactItem.Impact = impact;
      impactItem.VehicleAdvertSnapshotId = parms.snapshotId;
      impactItem.ExtendedNotes = string.Empty;
      impactItem.StrategyFactorItemId = matchingItem.Id;

      return impactItem;
   }

   public static int ExtractRRValue(string input)
   {
      // Regular expression to match "RR" followed by digits before the "|"
      Regex regex = new Regex(@"RR(\d+)\|");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Retail Rating");
   }

   public static int ExtractDTSValue(string input)
   {
      // Regular expression to match "DTS" followed by digits before the "|"
      Regex regex = new Regex(@"DTS(\d+)\|");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Days to Sell");


   }

   public static (int, int) ExtractMinMaxValue(string input)

   {
      if (string.IsNullOrEmpty(input))
      {
         throw new ArgumentException("Input string cannot be null or empty.", nameof(input));
      }

      var parts = input.Split('-');

      if (parts.Length != 2)
      {
         throw new ArgumentException("Input string must be in the format 'start-end'.", nameof(input));
      }

      if (!int.TryParse(parts[0], out int start) || !int.TryParse(parts[1], out int end))
      {
         throw new ArgumentException("Both parts of the input string must be valid integers.", nameof(input));
      }

      return (start, end);
   }

   private static int ExtractDLValue(string input)
   {
      // Regular expression to match "DL" followed by digits
      Regex regex = new Regex(@"\|DL(\d+)");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Days Listed");
   }

   private static int ExtractDSValue(string input)
   {
      // Regular expression to match "DS" followed by digits
      Regex regex = new Regex(@"\|DS(\d+)");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Days In Stock");
   }

   private static int ExtractDBValue(string input)
   {
      // Regular expression to match "DB" followed by digits
      Regex regex = new Regex(@"\|DB(\d+)");
      Match match = regex.Match(input);

      if (match.Success && int.TryParse(match.Groups[1].Value, out int result))
      {
         return result;
      }

      throw new Exception("Failure extracting Days Booked In");
   }


   private static StrategyPriceBuildUpItem ApplyFactorAndCreateFactorRatings(DateTime runDate, StrategyFactor factor, int value, decimal strategyPrice, int snapshotId, StrategySelectionRuleSet ruleSet)
   {
      string thisItemLabel = AdvertStratifierService.Stratify(value, factor.Name);
      decimal impact = ApplyStrategyFactorsService.calculateImpactForFactorThatRequiresStratification(thisItemLabel, factor, strategyPrice);
      StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateStrategyPriceBuildUpItem(
          factor,
          thisItemLabel,
          (value).ToString(),
          impact,
          snapshotId,
          ruleSet,
          string.Empty
          );
      return impactItem;
   }


   private static StrategyPriceBuildUpItem EarlyReturnAsCannotDoDaysToSellStrategyPrice(DateTime runDate, int? valuationToUse, StrategyFactor factor, int snapshotId, StrategySelectionRuleSet ruleSet)
   {
      string note = $"Unable to calculate strategy price as days to sell cannot be measured";
      StrategyPriceBuildUpItem buildUpItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
                  factor,
                  "DaysToSell",
                  0,
                  note,
                  snapshotId,
                  ruleSet);

      return buildUpItem;

   }




   public static async Task<decimal?> FindPricePoint(
       AutoTraderVehicleMetricsClient atMetricsClient,
       TokenResponse tokenResponse,
       RetailerSite retailerSite,
       int valuationToUse,
       decimal targetDays,
       int mileageToUse,
       decimal lowPP,
       decimal highPP,
       string derivativeId, string portalOptions, bool hasOptionsSpecified, DateTime? firstRegisteredDate, string atBaseUrl, ILog logger)
   {
      int lowPrice = (int)Math.Ceiling(valuationToUse * lowPP);
      int highPrice = (int)Math.Floor(valuationToUse * highPP);


      try
      {

         Task<decimal> taskLow = ProvideDaysToSellTask(retailerSite, mileageToUse,
         lowPrice, atMetricsClient, tokenResponse.AccessToken, derivativeId, portalOptions,
          hasOptionsSpecified, firstRegisteredDate, atBaseUrl, valuationToUse, logger)();


         Task<decimal> taskHigh = ProvideDaysToSellTask(retailerSite, mileageToUse,
          highPrice, atMetricsClient, tokenResponse.AccessToken, derivativeId, portalOptions,
           hasOptionsSpecified, firstRegisteredDate, atBaseUrl, valuationToUse, logger)();

         await Task.WhenAll(taskLow, taskHigh);


         decimal daysToSellAtHigh = taskHigh.Result;
         decimal daysToSellAtLow = taskLow.Result;
         if (daysToSellAtHigh == 0 || daysToSellAtLow == 0)
         {
            return null;
         }

         return WorkoutPricePointFromDaysToSellResults(targetDays, lowPP, highPP, daysToSellAtHigh, daysToSellAtLow);

      }
      catch (Exception ex)
      {
         throw new Exception(ex.Message, ex);
      }
   }

   public static decimal WorkoutPricePointFromDaysToSellResults(decimal targetDays, decimal lowPP, decimal highPP, decimal daysToSellAtHigh, decimal daysToSellAtLow)
   {
      var daysRange = daysToSellAtHigh - daysToSellAtLow;
      var resultingPricePoint = ((targetDays - daysToSellAtLow) / daysRange * (highPP - lowPP)) + lowPP;
      return resultingPricePoint;
   }




   public static Func<Task<decimal>> ProvideDaysToSellTask(
       RetailerSite retailerSite,
       int mileageToUse,
       int amount,
       AutoTraderVehicleMetricsClient atMetricsClient,
       string token,
       string derivativeId,
       string portalOptions,
       bool hasOptionsSpecified,
       DateTime? firstRegisteredDate, string atBaseURL, decimal valuationToUse, ILog? logger)
   {
      return async () =>
      {
         try
         {

            GetAdvertPriceAdjustedDaysToSellParams parms = new GetAdvertPriceAdjustedDaysToSellParams()
            {
               AutotraderBaseURL = atBaseURL,
               AdvertiserId = retailerSite.RetailerId,
               DerivativeId = derivativeId,
               FirstRegistrationDate = firstRegisteredDate,
               OdometerReadingMiles = mileageToUse,
               Amount = amount,
               UseSpecificOptions = hasOptionsSpecified,
               SpecificOptionNames = hasOptionsSpecified ? portalOptions.Split(',') : new List<string>(),

               AverageValuation = valuationToUse,
               AdjustedValuation = valuationToUse
            };

            return await atMetricsClient.GetAdvertPriceAdjustedDaysToSell(parms, token, logger);
         }
         catch (Exception ex)
         {
            return 0;
         }
      };
   }


   private static async Task<StrategyPriceBuildUpItem> WorkoutStrategyPriceBasedOnValuationChangeUntilSellAndReturnImpact(
      DateTime runDate,
      AutoTraderFutureValuationsClient atFutureValsClient,
      TokenResponse tokenResponse,
      RetailerSite retailerSite,
      StrategyFactor factor,
      AdvertParamsForStrategyCalculator parms
      , string atBaseURL, StrategySelectionRuleSet ruleSet)
   {
      //we already know the days to sell (check what this would be based on for a newly valued vehicle)
      //we just need to get a future valuation at that point
      //diff between valuation then and valuation now is this layer i guess.


      //throw new NotImplementedException();

      List<DateTime> futureValnPoints = new List<DateTime>();//
      DateTime daysToSell = DateTime.Now.AddDays((int)Math.Ceiling(parms.DaysToSell));
      futureValnPoints.Add(daysToSell);


      GetFutureValuationsParams getFutureValuationsParams = new GetFutureValuationsParams()
      {
         futureValuationPoints = futureValnPoints,
         derivativeId = parms.derivativeId,
         firstRegistrationDate = parms.firstRegisteredDate.Value,
         odometerReading = parms.OdometerReading.Value,
         retailerId = retailerSite.RetailerId,
         currentValuation = parms.valuationAverage.Value,
         currentAdjustedValuation = parms.valuationAdjusted.Value
      };
      var futureValuations = await atFutureValsClient.GetFutureValuation(getFutureValuationsParams,
      tokenResponse, null);


      var valuationForLastDay = futureValuations.Last().RetailValueExcludingVat ?? futureValuations.Last().RetailValue;
      var impact = (decimal)(valuationForLastDay - parms.valuationAdjusted);

      if (impact != 0)
      {
         StrategyPriceBuildUpItem buildUp = new StrategyPriceBuildUpItem();
         buildUp.VersionName = factor.StrategyVersion.Name;
         buildUp.FactorName = factor.Name.ToString();
         buildUp.FactorItemLabel = string.Empty;
         buildUp.FactorItemValue = (int)Math.Round(impact, 0, MidpointRounding.AwayFromZero);
         buildUp.SourceValue = string.Empty;
         buildUp.Impact = impact;
         buildUp.RuleSetComment = ruleSet.Comment;
         buildUp.VehicleAdvertSnapshotId = parms.snapshotId;
         buildUp.StrategyFactorItemId = factor.StrategyFactorItems.First().Id;
         //buildUp.ExtendedNotes = $"Round to price break as within {formattedRounding}";
         buildUp.StrategySelectionRuleSetId = ruleSet.Id;

         return buildUp;
      }
      else
      {
         return null;
      }




   }



   private static async Task<StrategyPriceBuildUpItem> WorkoutStrategyPriceBasedOnDaysToSellRequirementAndReturnImpact(
       DateTime runDate,
       AutoTraderVehicleMetricsClient atMetricsClient,
       TokenResponse tokenResponse,
       RetailerSite retailerSite,
       StrategyFactor factor,
       AdvertParamsForStrategyCalculator parms
       , string atBaseURL, StrategySelectionRuleSet ruleSet, ILog logger)
   {
      decimal factorItemValue = factor.StrategyFactorItems.First().Value;
      var targetDays = factorItemValue;

      //check must have at least one of these
      if (parms.dateOnForecourt == null && parms.OdometerReading == null)
      {
         var returnItem = EarlyReturnAsCannotDoDaysToSellStrategyPrice(runDate, (int)parms.valuationAdjusted, factor, parms.snapshotId, ruleSet);
         return returnItem;
      }

      int mileageToUse = WorkoutMileageToUse(parms.OdometerReading, parms.firstRegisteredDate, parms.dateOnForecourt);

      //firstly find out what the very lowest price is we can submit to the portal

      //we send in the adjusted val.  but the AT endpoint will limit us based on the average val.  FFS
      decimal lowestPPNew = Workout90PctPrice(parms.valuationAdjusted, parms.valuationAverage);

      //get a rough estimate of the ideal price point using 90% and 100%
      decimal highPP = 1M;
      decimal? resultingPricePoint = null;
      try
      {
         decimal? pricePointResult = await FindPricePoint(
             atMetricsClient,
             tokenResponse,
             retailerSite,
             (int)parms.valuationAdjusted,
             targetDays,
             mileageToUse,
             lowestPPNew,
             highPP,
             parms.derivativeId,
             parms.portalOptions,
             parms.hasOptionsSpecified,
             parms.firstRegisteredDate,
             atBaseURL, logger);
         resultingPricePoint = pricePointResult;
      }
      catch (Exception ex)
      {
         StrategyPriceBuildUpItem returnItem = EarlyReturnAsCannotDoDaysToSellStrategyPrice(runDate, (int)parms.valuationAdjusted, factor, parms.snapshotId, ruleSet);
         return returnItem;
      }

      if (resultingPricePoint == null || resultingPricePoint == 0)
      {
         //early return if either of the days to sell are 0 due to some error e.g. unusual vehicle
         StrategyPriceBuildUpItem returnItem = EarlyReturnAsCannotDoDaysToSellStrategyPrice(runDate, (int)parms.valuationAdjusted, factor, parms.snapshotId, ruleSet);
         return returnItem;
      }
      else
      {
         //further increase the accuracy of the price point if we are able to
         decimal highPercent = Math.Ceiling((decimal)resultingPricePoint * 100) / 100;
         decimal lowPercent = highPercent - 0.01M;
         if (lowPercent > lowestPPNew && highPercent < 1.1M)
         {
            try
            {
               var pricePointRes = await FindPricePoint(
                   atMetricsClient,
                   tokenResponse,
                   retailerSite,
                   (int)parms.valuationAdjusted,
                   targetDays,
                   mileageToUse,
                   lowPercent,
                   highPercent,
                   parms.derivativeId,
                   parms.portalOptions,
                   parms.hasOptionsSpecified,
                   parms.firstRegisteredDate, atBaseURL, logger);
               resultingPricePoint = pricePointRes;

            }
            catch (Exception ex)
            {
               //Guess it couldn't do the fine tuning
            }
            decimal impact = ((decimal)resultingPricePoint - 1M) * (decimal)parms.valuationAdjusted;
            string extendedNote = $"Adjustment to valuation to achieve {targetDays} days to sell";
            StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
                        factor,
                        "DaysToSell",
                        impact,
                        extendedNote,
                        parms.snapshotId, ruleSet);
            return impactItem;
         }
         else
         {
            //failed to further finnesse it, use what we have
            decimal impact = ((decimal)resultingPricePoint - 1M) * (decimal)parms.valuationAdjusted;
            string extendedNote = $"Adjustment to valuation to achieve {targetDays} days to sell";
            StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
                        factor,
                        "DaysToSell",
                        impact,
                        extendedNote,
                        parms.snapshotId,
                        ruleSet);
            return impactItem;
         }

      }
   }

   public static decimal Workout90PctPrice(int? valuationAvg, int? valuationAdj)
   {
      decimal lowestPPNew = 0.9M;
      if (valuationAvg > valuationAdj)
      {
         lowestPPNew = 0.9M * (int)valuationAvg / (int)valuationAdj;
      }

      return lowestPPNew;
   }

   public static int WorkoutMileageToUse(int? odometerReading, DateTime? firstRegDate, DateTime? dateOnForecourt)
   {
      int mileageToUse = 0;
      if (odometerReading != null)
      {
         mileageToUse = (int)odometerReading;
      }
      else
      {
         DateTime adFirstRegdDate = firstRegDate != null ? (DateTime)firstRegDate : DateTime.Now;
         double vehicleAgeInMonths = ((DateTime)dateOnForecourt - adFirstRegdDate).Days / (365.25 / 12);
         mileageToUse = odometerReading ?? (int)Math.Round((vehicleAgeInMonths * 1000));
      }

      return mileageToUse;
   }

   private static async Task<StrategyPriceBuildUpItem> WorkoutStrategyPriceForLocalCompetitor(
       AutoTraderCompetitorClient competitorClient,
       RetailerSite retailerSite,
       AdvertParamsForStrategyCalculator parms,
       StrategyFactor factor,
       StrategySelectionRuleSet ruleSet
       )
   {

      int radius = (int)factor.StrategyFactorItems.First(s => s.Label == "Radius").Value;
      int competitorRank = (int)factor.StrategyFactorItems.First(s => s.Label == "Ranking").Value;
      int plateRange = (int)factor.StrategyFactorItems.First(s => s.Label == "PlateSteps").Value;
      int mileageSteps = (int)factor.StrategyFactorItems.First(s => s.Label == "MileageSteps").Value;
      var competitorTypesToUse = factor.StrategyFactorItems
          .Where(s => s.Value == 1 && (s.Label == "Independent" || s.Label == "Franchise" || s.Label == "Supermarket" || s.Label == "Private"))
          .Select(s => s.Label)
          .ToList();

      CompetitorSearchParams searchParams = new CompetitorSearchParams(parms, radius);
      VehicleListing competitorAnalysis = await competitorClient.GetCompetitorNew(searchParams, null);

      if (parms.valuationAdjusted == null)
      {
         return null;
      }

      int valuationToUse = (int)parms.valuationAdjusted;

      CompetitorSummary competitorSummary = ConstantMethodsService.CreateCompetitorSummary(
          valuationToUse,
          valuationToUse,
          decimal.Parse(retailerSite.Site.GeoY),
          decimal.Parse(retailerSite.Site.GeoX),
          competitorAnalysis,
          radius,
          competitorTypesToUse
          );

      int mileageToUse = parms.OdometerReading ?? 0;
      var minMileage = Math.Max(1000, (mileageToUse - mileageSteps));
      var maxMileage = mileageToUse + mileageSteps;
      var relevantCompetitor = competitorSummary.CompetitorVehicles
          .Where(x => x.CompetitorName != null && x.CompetitorName != string.Empty && x.Mileage >= minMileage && x.Mileage <= maxMileage) //no privates, similar mileage
          .OrderBy(x => x.PricePosition)
          .Skip(competitorRank - 1)
          .FirstOrDefault();
      //var tst = competitorSummary.CompetitorVehicles.OrderBy(x => x.PricePosition).Take(20).ToList();
      if (relevantCompetitor != null)
      {
         //var ourAdPricePosition = parms.currentStrategyPrice / (decimal)valuationToUse;
         //if (relevantCompetitor.PricePosition < ourAdPricePosition)
         //{
         decimal targetPP = (Math.Round(relevantCompetitor.PricePosition * 100, 1, MidpointRounding.AwayFromZero) - 0.1M) / 100;
         decimal priceNeedsToBe = (decimal)(targetPP * valuationToUse);
         decimal priceMatchImpact = priceNeedsToBe - parms.currentStrategyPrice;

         string lowestPPString = (relevantCompetitor.PricePosition * 100).ToString("0.0\\%");
         string extendedNote = $"Matched to {relevantCompetitor.VehicleReg} at {relevantCompetitor.CompetitorName} having PP of {lowestPPString}";
         StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
             factor,
             "Radius",
             priceMatchImpact,
             extendedNote,
             parms.snapshotId,
             ruleSet);
         return impactItem;

      }
      else
      {
         return null;
      }
   }


   private static async Task<StrategyPriceBuildUpItem> WorkoutStrategyPriceForMarketPositionScore(
       AutoTraderCompetitorClient competitorClient,
       RetailerSite retailerSite,
       AdvertParamsForStrategyCalculator parms,
       StrategyFactor factor,
       StrategySelectionRuleSet ruleSet
       )
   {
      //// Copy of WorkoutStrategyPriceForLocalCompetitor, with few changes ///


      int radius = (int)factor.StrategyFactorItems.First(s => s.Label == "Radius").Value;
      int desiredMarketPositionScore = (int)factor.StrategyFactorItems.First(s => s.Label == "Ranking").Value;
      int plateRange = (int)factor.StrategyFactorItems.First(s => s.Label == "PlateSteps").Value;
      int mileageSteps = (int)factor.StrategyFactorItems.First(s => s.Label == "MileageSteps").Value;
      var competitorTypesToUse = factor.StrategyFactorItems
          .Where(s => s.Value == 1 && (s.Label == "Independent" || s.Label == "Franchise" || s.Label == "Supermarket" || s.Label == "Private"))
          .Select(s => s.Label)
          .ToList();

      CompetitorSearchParams searchParams = new CompetitorSearchParams(parms, radius);
      VehicleListing competitorAnalysis = await competitorClient.GetCompetitorNew(searchParams, null);

      if (parms.valuationAdjusted == null)
      {
         return null;
      }

      int valuationToUse = (int)parms.valuationAdjusted;

      CompetitorSummary competitorSummary = ConstantMethodsService.CreateCompetitorSummary(
          valuationToUse,
          valuationToUse,
          decimal.Parse(retailerSite.Site.GeoY),
          decimal.Parse(retailerSite.Site.GeoX),
          competitorAnalysis,
          radius,
          competitorTypesToUse
          );

      int mileageToUse = parms.OdometerReading ?? 0;
      var minMileage = Math.Max(1000, (mileageToUse - mileageSteps));
      var maxMileage = mileageToUse + mileageSteps;
      var relevantCompetitors = competitorSummary.CompetitorVehicles
          .Where(x => x.CompetitorName != null && x.CompetitorName != string.Empty && x.Mileage >= minMileage && x.Mileage <= maxMileage) //no privates, similar mileage
          .OrderBy(x => x.PricePosition);

      int competitorIndex = Math.Max(1, (100 - desiredMarketPositionScore) * relevantCompetitors.Count() / 100);


      var relevantCompetitor = relevantCompetitors
          .Skip(competitorIndex - 1)
          .FirstOrDefault();

      if (relevantCompetitor != null)
      {
         decimal targetPP = (Math.Round(relevantCompetitor.PricePosition * 100, 1, MidpointRounding.AwayFromZero) - 0.1M) / 100;
         decimal priceNeedsToBe = (decimal)(targetPP * valuationToUse);
         decimal priceMatchImpact = priceNeedsToBe - parms.currentStrategyPrice;

         string lowestPPString = (relevantCompetitor.PricePosition * 100).ToString("0.0\\%");
         string extendedNote = $"Matched to {relevantCompetitor.VehicleReg} at {relevantCompetitor.CompetitorName} having PP of {lowestPPString}";
         StrategyPriceBuildUpItem impactItem = ApplyStrategyFactorsService.CreateBuildUpItemWithExtendedNote(
             factor,
             "Radius",
             priceMatchImpact,
             extendedNote,
             parms.snapshotId,
             ruleSet);
         return impactItem;

      }
      else
      {
         return null;
      }
   }
}