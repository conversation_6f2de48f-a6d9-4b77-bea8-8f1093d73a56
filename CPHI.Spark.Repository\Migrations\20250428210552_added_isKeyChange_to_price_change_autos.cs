﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CPHI.Spark.Repository.Migrations
{
    /// <inheritdoc />
    public partial class added_isKeyChange_to_price_change_autos : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsKeyChang<PERSON>",
                schema: "autoprice",
                table: "PriceChangeAutoItems",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON>hang<PERSON>",
                schema: "autoprice",
                table: "PriceChangeAutoItems");
        }
    }
}
