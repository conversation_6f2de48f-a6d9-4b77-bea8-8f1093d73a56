import { Component, OnInit } from "@angular/core";
import { NgbActiveModal, <PERSON><PERSON><PERSON>odal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import { StrategyFactorName } from "src/app/model/StrategyFactorName";
import { StrategyFull } from "src/app/model/StrategyFull";
import { StrategyRule } from "src/app/model/StrategyRule";
import { StrategyVersionVM } from "src/app/model/StrategyVersionVM";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { ConfirmModalNewComponent } from "../confirmModalNew/confirmModalNew.component";
import { PricingPolicyBuilderModalComponent } from "../pricingPolicyModal/pricingPolicyModal.component";
import { StrategyCriteria } from "src/app/model/StrategyCriteria";
import { StrategyFieldName } from "src/app/model/StrategyFieldName";
import { DomSanitizer, SafeHtml } from "@angular/platform-browser";
import { ComparatorType } from "src/app/model/ComparatorType";
import { SimpleTextModalComponent } from "src/app/pages/fleetOrderbook/simpleTextModal/simpleTextModal.component";
import { BasicInfoModalComponent } from "../basicInfoModal/basicInfoModal.component";
import { environment } from "../../../environments/environment";

@Component({
   selector: "strategyBuilderModal",
   templateUrl: "./strategyBuilderModal.component.html",
   styleUrls: ["./strategyBuilderModal.component.scss"],
})
export class StrategyBuilderModalComponent implements OnInit {
   public StrategyFactorName = StrategyFactorName;
   confirmationModalTitle: string = "Are you sure you want to remove this item?";
   strategy: StrategyFull;
   public pricingPolicies: StrategyVersionVM[];
   strategyClone: StrategyFull;
   strategyFieldNames: StrategyFieldName[];
   errors: string[];

   get instructionMessage(): string {
      if (this.constants.environment.isSingleSiteGroup) {
         return `Spark uses a pricing strategy to determine which pricing policy to apply to each vehicle.   Use the form below to build up a strategy.   This strategy can then be chosen on the site settings page.`;
      } else {
         return `Spark uses a pricing strategy to determine which pricing policy to apply to each vehicle.   Use the form below to build up a strategy.   This strategy can then be used at one or more sites using the site settings page.`;
      }
   }

   constructor(
      public selections: SelectionsService,
      public constants: ConstantsService,
      private getDataService: GetDataMethodsService,
      private modalService: NgbModal,
      private activeModal: NgbActiveModal
   ) {}

   async ngOnInit() {
      await this.getAllPricingPoliciesNew();
      this.getAllStrategyFieldNames();
   }

   public setStrategy(strategy: StrategyFull) {
      this.strategyClone = JSON.parse(JSON.stringify(strategy));
      this.strategy = new StrategyFull(strategy);
   }

   deleteStrategy() {
      this.getDataService.deleteStrategy(this.strategy.StrategyId).subscribe(
         () => {
            this.constants.toastSuccess("Deleted strategy");
            this.dismissModal(true);
         },
         () => {
            this.constants.toastDanger("Failed to delete strategy");
         }
      );
   }

   async getAllPricingPoliciesNew() {
      try {
         const res: StrategyVersionVM[] = await this.getDataService.getAllPricingPolicies().toPromise();
         this.pricingPolicies = res
            .map((x) => new StrategyVersionVM(x))
            .sort((a, b) => (b.Id - a.Id));
      } catch (error) {
         console.error("Failed to retrieve strategies", error);
         this.selections.triggerSpinner.emit({ show: false });
      }
   }

   getAllPricingPolicies() {
      this.getDataService.getAllPricingPolicies().subscribe(
         (res: StrategyVersionVM[]) => {
            this.pricingPolicies = res
               .map((x) => new StrategyVersionVM(x))
               .sort((a, b) => (b.Id - a.Id));
         },
         (error) => {
            console.error("Failed to retrieve strategies", error);
            this.selections.triggerSpinner.emit({ show: false });
         }
      );
   }

   getAllStrategyFieldNames() {
      this.getDataService.getAllStrategyFieldNames().subscribe(
         (res: StrategyFieldName[]) => {
            this.strategyFieldNames = res;
         },
         (error) => {
            console.error("Failed to retrieve strategy field names", error);
            this.selections.triggerSpinner.emit({ show: false });
         }
      );
   }

   // RULES
   addRule() {
      this.strategy.StrategyRules.push(new StrategyRule(null));
   }

   maybeRemoveRule(rule: StrategyRule) {
      const modalRef = this.modalService.open(ConfirmModalNewComponent, {
         size: "md",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      });
      const component: ConfirmModalNewComponent = modalRef.componentInstance;
      component.header = "Are you sure you want to remove this rule?";

      modalRef.result.then(
         () => {
            this.removeRule(rule);
         },
         () => {
            //nothing
         }
      );
   }

   maybeDeleteStrategy() {
      const modalRef = this.modalService.open(ConfirmModalNewComponent, {
         size: "md",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      });
      const component: ConfirmModalNewComponent = modalRef.componentInstance;
      component.header = "Are you sure you want to delete this strategy?";

      modalRef.result.then(
         () => {
            this.deleteStrategy();
         },
         () => {
            //nothing
         }
      );
   }

   removeRule(rule: StrategyRule) {
      this.strategy.StrategyRules = this.strategy.StrategyRules.filter((x) => x != rule);
   }

   choosePricingPolicy(rule: StrategyRule, policy: StrategyVersionVM) {
      rule.PricingPolicyId = policy.Id;
      rule.PricingPolicyLabel = policy.pricingPolicyTextSummary;
   }

   // CRITERIA
   addCriteria(rule: StrategyRule) {
      rule.Criterias.push(new StrategyCriteria(null));
   }

   maybeRemoveCriteria(rule: StrategyRule, criteria: StrategyCriteria) {
      const modalRef = this.modalService.open(ConfirmModalNewComponent, {
         size: "md",
         keyboard: false,
         ariaLabelledBy: "modal-basic-title",
      });
      const component: ConfirmModalNewComponent = modalRef.componentInstance;
      component.header = "Are you sure you want to remove this criteria?";

      modalRef.result.then(
         () => {
            this.removeCriteria(rule, criteria);
         },
         () => {
            //nothing
         }
      );
   }

   removeCriteria(rule: StrategyRule, criteria: StrategyCriteria) {
      rule.Criterias = rule.Criterias.filter((x) => x != criteria);
   }

   chooseStrategyFieldName(criteria: StrategyCriteria, fieldName: StrategyFieldName) {
      criteria.FieldNameLabel = fieldName.Label;
      criteria.ComparatorType = fieldName.ComparatorType;
   }

   async saveStrategy() {
      this.strategy.prepareToPersist();
      try {
         const saveResult: number | null = await this.getDataService.saveStrategy(this.strategy).toPromise(); //.subscribe(() => {
         this.closeModal(saveResult);
         this.constants.toastSuccess("Saved strategy");
      } catch (error) {
         this.constants.toastDanger("Failed to save strategy");
      }
   }

   changesMade(): boolean {
      return this.strategy.isChangesMade(this.strategyClone);
   }

   reviewPolicy(policyId: number, rule: StrategyRule, strategy: StrategyFull) {
      //load strategy
      this.selections.triggerSpinner.emit({ show: true, message: "Loading pricing policy" });
      this.getDataService.getStrategy(policyId).subscribe((res: StrategyVersionVM) => {

         let mileageFactors = res.StrategyFactors.find((x) => x.Name == StrategyFactorName.Mileage)?.StrategyFactorItems;

         if (mileageFactors) {
           mileageFactors.sort((a, b) => {
               const aLabel = Number(a.Label.replace('<','').trim());
               const bLabel = Number(b.Label.replace('<','').trim());
               console.log("aLabel ", aLabel, " bLabel ", bLabel);
               return bLabel - aLabel;
           });
         }

         this.selections.triggerSpinner.emit({ show: false });

         const policy: StrategyVersionVM = new StrategyVersionVM(res);

         this.openPricingPolicyModal(policy, rule, strategy);
      });
   }

   private async openPricingPolicyModal(policyToEdit: StrategyVersionVM, rule: StrategyRule, strategy: StrategyFull) {
      const openStrategyModalRef = this.modalService.open(PricingPolicyBuilderModalComponent);
      const instance: PricingPolicyBuilderModalComponent = openStrategyModalRef.componentInstance;
      instance.service.chosenPolicy = policyToEdit;
      instance.chosenStrategyClone = this.constants.clone(policyToEdit);
      instance.service.initAllFactors();

      try {
         // Await the result of the modal
         const response: { pricingPolicyId?: number, savedAsNew?: boolean } = await openStrategyModalRef.result;

         console.log("RESPONSE ", response);

         const savedPolicyNewId = response.pricingPolicyId;
         const savedAsNew = response.savedAsNew;

         await this.getAllPricingPoliciesNew();
         this.selections.triggerSpinner.next({ show: false });

         const messageModalRef = this.modalService.open(BasicInfoModalComponent, { size: "md" });
         const instance: BasicInfoModalComponent = messageModalRef.componentInstance;

         if (savedPolicyNewId) {
            // Created new policy
            instance.header = "New Pricing Policy Created";


            if (policyToEdit.Id == null || savedAsNew == true) {
               //we had chosen to create a new policy so will be expecting a new policy
               instance.body = `New pricing policy #${savedPolicyNewId} has been created.  Ensure you now pick this from the dropdown.`;
            } else {
               //we were editing an existing policy, might not expect to now have a new policy Id
               instance.body = `Pricing policy #${policyToEdit.Id} had previously been used to calculate prices so cannot be edited.  Instead, new pricing policy #${savedPolicyNewId} has been created.   Ensure you now pick this from the dropdown.`;
            }
         } else {
            instance.header = "Pricing Policy Saved";
            instance.body = `Pricing policy #${policyToEdit.Id} has been saved.`;
         }
      } catch (error) {
         if (error) {
            //we deleted a policy

            await this.getAllPricingPolicies();
            const messageModalRef = this.modalService.open(BasicInfoModalComponent, { size: "md" });
            const instance: BasicInfoModalComponent = messageModalRef.componentInstance;
            instance.header = "Pricing Policy Deleted";
            if (rule) {
               //must ensure rule is no longer using this policy
               const ruleIndex = this.pricingPolicies.findIndex((x) => x.Id == rule.PricingPolicyId);
               if (ruleIndex > -1) {
                  const newRuleIndex: number = Math.max(0, ruleIndex - 1);
                  rule.PricingPolicyLabel = this.pricingPolicies[newRuleIndex].pricingPolicyTextSummary;
                  rule.PricingPolicyId = this.pricingPolicies[newRuleIndex].Id;
               }
               instance.body = `Pricing policy #${policyToEdit.Id} has been deleted, and the current rule has been updated to use pricing policy #${rule.PricingPolicyId}.`;
            }

            if (strategy) {
               //must ensure strategy is no longer using this policy
               const ruleIndex = this.pricingPolicies.findIndex(
                  (x) => x.Id == strategy.DefaultPricingPolicy.PricingPolicyId
               );
               if (ruleIndex > -1) {
                  const newRuleIndex: number = Math.max(0, ruleIndex - 1);
                  strategy.DefaultPricingPolicy.PricingPolicyLabel =
                     this.pricingPolicies[newRuleIndex].pricingPolicyTextSummary;
                  strategy.DefaultPricingPolicy.PricingPolicyId = this.pricingPolicies[newRuleIndex].Id;
               }
               instance.body = `Pricing policy #${policyToEdit.Id} has been deleted, and the strategy defaulte has been updated to use pricing policy #${strategy.DefaultPricingPolicy.PricingPolicyId}.`;
            }
         } else {
         }

         this.selections.triggerSpinner.next({ show: false });
      }
   }

   chooseDefaultPricingPolicy(policy: StrategyVersionVM) {
      this.strategy.DefaultPricingPolicy = {
         PricingPolicyId: policy.Id,
         PricingPolicyLabel: policy.pricingPolicyTextSummary,
      };
   }

   defaultPolicyMessage() {
      if (this.strategy.StrategyRules.length === 0) {
         return "As no criteria have been set above, this strategy will use this pricing policy:";
      } else if (this.strategy.StrategyRules.length === 1) {
         return "If the above criteria is not met for this vehicle, use this pricing policy:";
      }
      return "If none of above above criteria are met for this vehicle, use this pricing policy:";
   }

   showNewPolicyModal() {
      const policy: StrategyVersionVM = new StrategyVersionVM();
      this.openPricingPolicyModal(policy, null, null);
   }

   closeModal(saveResult: number | null) {
      this.activeModal.close(saveResult);
   }

   public dismissModal(didDeleteAnItem: boolean) {
      this.activeModal.dismiss(didDeleteAnItem);
   }

   isSaveDisabled() {
      //there must be changes, and each rule and criteria must be valid
      return this.buildValidationErrors().length > 0;
   }

   buildValidationErrors() {
      const errors: string[] = [];

      //Strategy header validations
      if (!this.strategy.isChangesMade(this.strategyClone)) {
         errors.push("No changes have been made");
      }

      if (this.strategy.Name == null || this.strategy.Name.length == 0) {
         errors.push("Strategy name is required");
      }

      //Rule validations
      this.strategy.StrategyRules.forEach((rule, r) => {
         if (rule.Criterias.length === 0) {
            errors.push(`Rule ${r + 1} must have at least one criteria`);
         }
         rule.Criterias.forEach((c, i) => {
            if (c.FieldNameLabel == null || c.FieldNameLabel.length == 0) {
               errors.push(`Rule ${r + 1} criteria ${i + 1} must have a field name`);
            }
            if (c.FieldNameLabel == null || c.FieldNameLabel.length == 0) {
               errors.push(`Rule ${r + 1} criteria ${i + 1} must have a comparator type`);
            }
            if (
               (c.CriteriaValue == null || c.CriteriaValue.length == 0) &&
               (c.CriteriaValueHigh == null || c.CriteriaValueLow == null)
            ) {
               errors.push(`Rule ${r + 1} criteria ${i + 1} must have a value`);
            }
            if (
               c.ComparatorType == ComparatorType.range &&
               (c.CriteriaValueLow == null || c.CriteriaValueHigh == null)
            ) {
               errors.push(`Rule ${r + 1} criteria ${i + 1} must have a low and high value`);
            }

            if (rule.PricingPolicyId === this.strategy.DefaultPricingPolicy?.PricingPolicyId) {
               errors.push("Rule " + (r + 1) + " cannot use the default pricing policy");
            }
         });

         if (!rule.PricingPolicyLabel) {
            errors.push("Rule " + (r + 1) + " must have a pricing policy");
         }
      });

      //Strategy must have default pricing policy
      if (!this.strategy.DefaultPricingPolicy || !this.strategy.DefaultPricingPolicy.PricingPolicyLabel) {
         errors.push("Strategy must have a default pricing policy");
      }

      this.errors = errors;
      return errors;
   }
}
