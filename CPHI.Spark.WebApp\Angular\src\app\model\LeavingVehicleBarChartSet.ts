import { BarChartParams } from "./BarChartParams";
import { LeavingVehicleItem } from "./LeavingVehicleItem";
import * as utilities from "./../services/utilityFunctions";
import { max } from "moment";

export class LeavingVehicleBarChartSet {

  rrSoldVolume: BarChartParams;
  rrDaysListed: BarChartParams;
  rrFirstPP: BarChartParams;
  rrLastPP: BarChartParams;
  rrChangedPP: BarChartParams;
  dlSoldVolume: BarChartParams;
  dlDaysListed: BarChartParams;
  dlLastPP: BarChartParams;
  







  constructor() {
    const rrSub20 = getComputedStyle(document.documentElement).getPropertyValue('--rrSub20');
    const rrSub40 = getComputedStyle(document.documentElement).getPropertyValue('--rrSub40');
    const rrSub60 = getComputedStyle(document.documentElement).getPropertyValue('--rrSub60');
    const rrSub80 = getComputedStyle(document.documentElement).getPropertyValue('--rrSub80');
    const rr80Plus = getComputedStyle(document.documentElement).getPropertyValue('--rr80Plus');

    const retailRatingBarChartParams: BarChartParams = {
      Labels: ['Below 20', '20 to 39', '40 to 59', '60 to 79', '80 or Above'],
      Values: [0, 0, 0, 0, 0],
      Colours: [rrSub20, rrSub40, rrSub60, rrSub80, rr80Plus],
      Title: '',
      AverageLineValue: 0,
      DataFormat: 'number',
      yMax: undefined,
      yMin: undefined
    };
    const daysListedBarChartParams: BarChartParams = {
      Labels: ['<20', '<40', '<60', '60+'],
      Values: [0, 0, 0, 0],
      Colours: [rr80Plus, rrSub80, rrSub40, rrSub20],
      Title: '',
      AverageLineValue: 0,
      DataFormat: 'number',
      yMax: undefined,
      yMin: undefined
    };

    this.rrSoldVolume = { ...retailRatingBarChartParams, Values: [0, 0, 0, 0, 0], };
    this.rrDaysListed = { ...retailRatingBarChartParams, Values: [0, 0, 0, 0, 0], DataFormat: 'days', };
    this.rrFirstPP = { ...retailRatingBarChartParams, Values: [0, 0, 0, 0, 0], DataFormat: 'percent1dp', yMax: 1.05, yMin: 0.95 };
    this.rrLastPP = { ...retailRatingBarChartParams, Values: [0, 0, 0, 0, 0], DataFormat: 'percent1dp', yMax: 1.05, yMin: 0.95 };
    this.rrChangedPP = { ...retailRatingBarChartParams, Values: [0, 0, 0, 0, 0], DataFormat: 'percent1dp',  };


    this.dlSoldVolume = { ...daysListedBarChartParams, Values: [0, 0, 0, 0], Title: 'Sold Volume by Days Listed Band' };
    this.dlDaysListed = { ...daysListedBarChartParams, Values: [0, 0, 0, 0], DataFormat: 'days', Title: 'Average Days Listed' };
    this.dlLastPP = { ...daysListedBarChartParams, Values: [0, 0, 0, 0], DataFormat: 'percent1dp', yMax: 1.05, yMin: 0.95 };

  }

  calculateSet(data: LeavingVehicleItem[]) {

    this.calculateRetailRatingMetrics(data.filter(x => x.RetailRatingBand == '<20'), 0);
    this.calculateRetailRatingMetrics(data.filter(x => x.RetailRatingBand == '<40'), 1);
    this.calculateRetailRatingMetrics(data.filter(x => x.RetailRatingBand == '<60'), 2);
    this.calculateRetailRatingMetrics(data.filter(x => x.RetailRatingBand == '<80'), 3);
    this.calculateRetailRatingMetrics(data.filter(x => x.RetailRatingBand == '80+'), 4);

    this.calculateDayListedMetrics(data.filter(x => x.DaysListedBand == '0-20'), 0);
    this.calculateDayListedMetrics(data.filter(x => x.DaysListedBand == '20-40'), 1);
    this.calculateDayListedMetrics(data.filter(x => x.DaysListedBand == '40-60'), 2);
    this.calculateDayListedMetrics(data.filter(x => x.DaysListedBand == '60+'), 3);

    this.rrDaysListed.AverageLineValue = utilities.sum(this.rrDaysListed.Values) / 5

    this.setTitles(data);
    this.setYAxisMinMax();
  }





  private setYAxisMinMax() {
    let allPPValues = [...this.rrFirstPP.Values, ...this.rrLastPP.Values, ...this.dlLastPP.Values];
    let minPricePosition = Math.min(...allPPValues) - 0.02;
    let maxPricePosition = Math.max(...allPPValues) + 0.02;
    this.rrFirstPP.yMax = maxPricePosition;
    this.rrFirstPP.yMin = minPricePosition;
    this.rrLastPP.yMax = maxPricePosition;
    this.rrLastPP.yMin = minPricePosition;
    this.dlLastPP.yMax = maxPricePosition;
    this.dlLastPP.yMin = minPricePosition;
  }

  private setTitles(data: LeavingVehicleItem[]) {
    const averageSellDays = utilities.div(utilities.sum(data.map(x => x.DaysListed)), data.length);
    const averageFirstPP = utilities.div(utilities.sum(data.map(x => x.FirstPP)), data.length);
    const averageLastPP = utilities.div(utilities.sum(data.map(x => x.LastPP)), data.length);
    const averageRR = utilities.div(utilities.sum(data.map(x => x.LastRetailRating)), data.length);
    const averageDaysListed = utilities.div(utilities.sum(data.map(x => x.DaysListed)), data.length);

    this.rrSoldVolume.Title = `Sold Volume by Retail Rating (Total volume ${utilities.formatNumberWithComma(Math.round(data.length))}, average rating ${Math.round(averageRR)})`;
    this.rrSoldVolume.average = averageRR;
    this.dlSoldVolume.Title = `Sold Volume by Days Listed Band (Total volume ${utilities.formatNumberWithComma(Math.round(data.length))}, average days ${Math.round(averageDaysListed)})`;
    this.dlSoldVolume.average = averageDaysListed;
    this.rrDaysListed.Title = `How Fast Your Vehicles Sell.  Should be flat.  Average is ${Math.round(averageSellDays)} days`;
    this.rrDaysListed.average = averageSellDays;
    this.dlDaysListed.Title = `How Fast Your Vehicles Sell.  Average is ${Math.round(averageSellDays)} days`;
    this.dlDaysListed.average = averageSellDays;
    this.rrFirstPP.Title = `First Price Position.  Should be increasing.  Average is ${utilities.formatAsPercent(averageFirstPP)}`;
    this.rrFirstPP.average = averageFirstPP;
    this.rrLastPP.Title = `Final Price Position.  Should be increasing.  Average is ${utilities.formatAsPercent(averageLastPP)}`;
    this.rrLastPP.average = averageLastPP;
    this.rrChangedPP.Title = `Average Price Position change.  Should be flat.  Average is ${utilities.formatAsPercent(averageLastPP - averageFirstPP)}`;
    this.rrChangedPP.average = averageLastPP - averageFirstPP;
    this.dlLastPP.Title = `Final Price Position.  Should be decreasing.  Average is ${utilities.formatAsPercent(averageLastPP)}`;
    this.dlLastPP.average = averageLastPP;
  }

  private calculateRetailRatingMetrics(items: LeavingVehicleItem[], arrayIndex: number) {
    let volumeCum = 0;
    let daysListedCum = 0;
    let firstPPCum = 0;
    let lastPPCum = 0;

    items.forEach(item => {
      volumeCum++;
      daysListedCum += item.DaysListed;
      firstPPCum += item.FirstPP;
      lastPPCum += item.LastPP;
    });
    const firstPP = utilities.div(firstPPCum, volumeCum);
    const lastPP = utilities.div(lastPPCum, volumeCum);
    this.rrSoldVolume.Values[arrayIndex] = volumeCum;
    this.rrDaysListed.Values[arrayIndex] = utilities.div(daysListedCum, volumeCum);
    this.rrFirstPP.Values[arrayIndex] = firstPP;
    this.rrLastPP.Values[arrayIndex] = lastPP;
    this.rrChangedPP.Values[arrayIndex] = lastPP - firstPP;
  }

  private calculateDayListedMetrics(items: LeavingVehicleItem[], arrayIndex: number) {
    let volumeCum = 0;
    let daysListedCum = 0;
    let lastPPCum = 0;

    items.forEach(item => {
      volumeCum++;
      daysListedCum += item.DaysListed;
      lastPPCum += item.LastPP;
    });
    const lastPP = utilities.div(lastPPCum, volumeCum);
    this.dlSoldVolume.Values[arrayIndex] = volumeCum;
    this.dlDaysListed.Values[arrayIndex] = utilities.div(daysListedCum, volumeCum);
    this.dlLastPP.Values[arrayIndex] = lastPP;
  }
}



