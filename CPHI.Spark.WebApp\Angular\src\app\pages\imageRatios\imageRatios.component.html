<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      <span>
        {{ constants.translatedText.Dashboard_ImageRatios_Title }} - {{ service.monthStartDate | cph:'month':0 }}
      </span>
    </h4>
  </nav>
</nav>

<div class="content-new">
  <div class="content-inner-new">
    <div id="imageRatiosTableContainer">
      <imageRatiosTable></imageRatiosTable>
    </div>

    <!-- The charts -->
    <div
      id="chartsHolder"
      [ngClass]="{ 'flex-1': constants.environment.citNoww.eDynamixView }">
      <ng-container *ngIf="service.chartDetails?.length">
        <ng-container *ngFor="let chartSet of service.chartDetails; trackBy: trackByFunction">
          <div class="imageRatiosChartContainer">
            <imageRatiosChart [chartDetails]="chartSet"></imageRatiosChart>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</div>
