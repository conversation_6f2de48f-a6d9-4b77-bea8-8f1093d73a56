CREATE OR ALTER PROCEDURE [autoprice].[GET_VehicleOptOutSummaryItemsNew]
(
		@chosenDate Date = null,
		@RetailerSiteIds varchar(max)=null,
		@IncludeNewVehicles bit = 0,
		@DealerGroupId int
)
  
AS  
BEGIN 

SELECT  Value as Id INTO #retailerSites from STRING_SPLIT(@RetailerSiteIds,',') 
IF @chosenDate IS NULL SET @chosenDate = GETDATE()
DECLARE @chosenDatePlus1 Date = DATEADD(day,1,@chosenDate)
SET NOCOUNT ON;

--------------------------------------------
-- LATEST SNAPSHOTS
--------------------------------------------
WITH snapshotsrownum AS
(
	SELECT
	snaps.Id as SnapshotId,
	ads.Id as AdId,
	ROW_NUMBER() OVER (PARTITION BY ads.Id ORDER BY snaps.Id desc) AS RowNumber   
	FROM autoprice.VehicleAdvertSnapshots snaps 
	INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
	LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and CONVERT(date,opts.ActualEndDate) >= @chosenDate
	WHERE snaps.SnapshotDate >= @chosenDate AND snaps.SnapshotDate < @chosenDatePlus1
	AND opts.Id IS NOT NULL
)
SELECT 
SnapshotId,
AdId
INTO #latestSnapshots
FROM snapshotsrownum
WHERE RowNumber = 1;



--------------------------------------------
-- LAST MANUAL CHANGES
--------------------------------------------


WITH manualsRowNum as
(
	SELECT
	TOP 10000
	ads.Id as AdId,
	prices.CreatedDate,
	(prices.NowPrice - COALESCE(prices.WasPrice, 0)) as ChangeValue,
	p.Name as ChangedBy,
	ROW_NUMBER() OVER (PARTITION BY ads.Id ORDER BY prices.CreatedDate desc) AS RowNumber   
	FROM autoprice.PriceChangeManualItems prices 
	LEFT JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = prices.VehicleAdvertSnapshot_Id
	INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
	LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and CONVERT(date,opts.ActualEndDate) >= @chosenDate
	INNER JOIN People p on p.Id = prices.Person_Id
	--INNER JOIN #latestSnapshots ls on ls.SnapshotId = snaps.Id
	WHERE opts.Id IS NOT NULL
	ORDER BY ads.Id
)

SELECT
AdId,
CreatedDate,
ChangeValue,
ChangedBy
INTO #lastManualChangePerAd
FROM manualsRowNum
WHERE RowNumber = 1


----------------------------------------------
--COMMENTS
----------------------------------------------

SELECT
comm.Text as LastCommentText,
p.Name as LastCommentName,
comm.Date,
comm.VehicleAdvert_Id as AdvertId,
ROW_NUMBER() OVER (PARTITION BY ads.Id ORDER BY comm.Date desc ) AS RowNumber 
INTO #latestComments
FROM autoprice.VehicleAdvertComments comm
INNER JOIN autoprice.VehicleAdverts ads on ads.Id = comm.VehicleAdvert_Id
INNER JOIN #latestSnapshots snaps on snaps.AdId = ads.Id
INNER JOIN people p on p.Id = comm.Person_Id
WHERE comm.IsRemoved = 0




--------------------------------------------
-- TOTAL CHANGES
--------------------------------------------
SELECT
snaps.VehicleAdvert_Id,
COUNT(manuals.Id) as Count,
SUM(manuals.NowPrice - COALESCE(manuals.WasPrice, 0)) as TotalChangeValue
INTO #totalChanges
FROM autoprice.PriceChangeManualItems manuals
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = manuals.VehicleAdvertSnapshot_Id
GROUP BY snaps.VehicleAdvert_Id;

--------------------------------------------
-- ADVERTS AND SNAPSHOTS
--------------------------------------------

SELECT
adSites.Id as RetailerSiteId,
adSites.Name as RetailerName,
snaps.Id as SnapshotId,
snaps.VehicleAdvert_Id as VehicleAdvertId,
ads.VehicleReg,
ads.Chassis,
ads.Model,
ads.Make,
ISNULL(stkReg.Id,stkChassis.Id) as StockItemId,
ISNULL(stkReg.StockNumberFull,stkChassis.StockNumberFull) as StockNumber,
ads.Derivative ,
DATEDIFF(day,ads.CreatedInSparkDate,CONVERT(date,getDate())) as DaysListed,
RetailRating,
opts.CreatedDate as OptOutCreatedDate,
opts.AutoOptOutType as OptOutType,
COALESCE(opts.ActualEndDate,opts.OriginalEndDate) as OptOutEndDate,
optPerson.Name as OptOutPerson,
snaps.StrategyPrice as StrategyPrice,
snaps.TotalPrice as SellingPrice,
lastmanual.ChangeValue as LastChangeValue,
lastmanual.CreatedDate as LastChangeDate,
lastmanual.ChangedBy as LastChangedBy,
ISNULL(DATEDIFF(day,lastmanual.CreatedDate,CONVERT(date,getDate())),0) as Days,
tc.Count as TotalChangesCount,
tc.TotalChangeValue,
ads.ownershipCondition,
CONCAT(lc.LastCommentText,' - ',lc.LastCommentName,' ',FORMAT(lc.Date, 'dd MMM yy')) as LastComment
FROM autoprice.VehicleAdvertSnapshots snaps 
INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites adSites on adSites.Id = ads.RetailerSite_Id
INNER JOIN #latestSnapshots latests on latests.SnapshotId = snaps.Id
LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and CONVERT(date,opts.ActualEndDate) >CONVERT(date,@chosenDate) AND CONVERT(date,opts.CreatedDate) <= CONVERT(date,@chosenDate)
LEFT JOIN people optPerson on optPerson.id = opts.Person_Id
LEFT JOIN autoprice.PriceChangeAutoItems autoPrices on autoprices.VehicleAdvertSnapshot_Id = snaps.id 
LEFT JOIN #lastManualChangePerAd lastmanual on lastmanual.AdId = ads.id
LEFT JOIN #retailerSites srs on srs.id = adSites.Id
LEFT JOIN Stocks stkReg on stkReg.Reg <> '' AND stkReg.Reg = ads.VehicleReg and stkReg.IsRemoved = 0 and stkReg.Id = ads.Stock_Id
LEFT JOIN Stocks stkChassis on stkChassis.Chassis <> '' AND stkChassis.Chassis = ads.Chassis AND stkChassis.IsRemoved = 0 and stkChassis.Id = ads.Stock_Id
LEFT JOIN #totalChanges tc on tc.VehicleAdvert_Id = ads.Id
LEFT JOIN #latestComments lc on lc.AdvertId = ads.Id AND lc.RowNumber = 1

WHERE snaps.SnapshotDate >= @chosenDate AND snaps.SnapshotDate < @chosenDatePlus1
AND opts.Id IS NOT NULL
--For locking down
AND (srs.Id IS NOT NULL OR @RetailerSiteIds IS NULL)
AND
(
	(
		NOT (ads.StockNumber = 'Unknown' AND ads.VehicleReg IS NULL)
		AND ads.StockNumber NOT LIKE '%/0'
	)
	OR @includeNewVehicles = 1
)
AND adSites.IsActive = 1
AND 
(
	adSites.LifecycleStatusDefaults IS NULL OR
	EXISTS (
		SELECT 1
		FROM  STRING_SPLIT(adSites.LifecycleStatusDefaults, ',') s
		WHERE LTRIM(RTRIM(s.value)) = snaps.LifecycleStatus
	)
)

DROP TABLE #lastManualChangePerAd
DROP TABLE #latestSnapshots
DROP TABLE #retailerSites
DROP TABLE #totalChanges




END
GO
