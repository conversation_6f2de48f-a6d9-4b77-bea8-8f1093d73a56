﻿using System;
namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class UserModel
    {
        public Guid Id { get; set; }

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public string? Name
        {
            get
            {
                return !string.IsNullOrWhiteSpace(this.FirstName) || !string.IsNullOrWhiteSpace(this.LastName) ? this.FirstName?.Trim() + " " + this.LastName?.Trim() : (string)null;
            }
        }

        //public string? Initials => this.GetInitials();
    }
}