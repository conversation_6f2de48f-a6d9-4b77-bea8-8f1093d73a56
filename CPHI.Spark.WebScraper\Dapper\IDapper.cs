﻿using CPHI.Spark.Model;
using CPHI.Spark.Repository;
using Dapper;

using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;

namespace CPHI.Spark.WebScraper
{
    public interface IDapper : IDisposable
    {
        DbConnection GetDbconnection(DealerGroupName customer);

        Task<DataTable> GetDataTableAsync(DealerGroupName customer, string query, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        T Get<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        Task<T> GetAsync<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);

        Task<IEnumerable<T>> GetAllAsync<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        Task<int> ExecuteAsync(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        T Insert<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        Task<T> InsertAsync<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);

        Task<T> InsertAsync<T>(string sp, DynamicParameters parms, IDbTransaction tran, IDbConnection db, CommandType commandType = CommandType.StoredProcedure);
        T Update<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        IEnumerable<T> GetAll<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);

        //Task<SearchResult> GetMultipleAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
    }
}
