import { Component, HostListener, OnInit } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CphPipe } from "src/app/cph.pipe";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { DiscussionSummaryAction, GetSEReviewFormParams, SaveSEReviewFormParams, SEReviewMeasureSimple, SEReviewNonScoringMeasure, SEReviewScoringMeasure, UpdateSEReviewFormApprovalStateParams } from "../salesExecReview.model";
import { SalesExecReviewService } from "../salesExecReview.service";
import jspdf from "jspdf";
import { Subscription } from "rxjs";
import { SalesExecReviewForm } from "./SalesExecReviewForm";

@Component({
  selector: 'salesExecReviewForm',
  templateUrl: './form.component.html',
  styleUrls: ['./form.component.scss']
})

export class SalesExecReviewFormComponent implements OnInit {

  data: SalesExecReviewForm;
  approvalStates: string[];
  lastRefreshed: string;
  isReviewer: boolean;
  isSysAdmin: boolean;
  subscriberRefresh: Subscription;
  subscriberGet: Subscription;
  newFormIdSubscriber: Subscription;
  approvalStateUpdatedSubscriber: Subscription;
  charLimit: number;
  showWarning: boolean;

  //isLatestCommentSaved: boolean;

  get isLatestCommentSaved(): boolean {
    return !this.service.salesExecReview.form.hasChanged(this.service.salesExecReview.formClone)
  }

  get isEPR(): boolean {
    return this.service.salesExecReview.form.isEPR();
  }

  constructor(
    public constants: ConstantsService,
    public service: SalesExecReviewService,
    public modalService: NgbModal,
    public selections: SelectionsService,
    public cph: CphPipe
  ) { }

  // This listener is triggered when the user tries to close or reload the page
  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: BeforeUnloadEvent): void {  // Explicitly stating the return type as void

    //this.isLatestCommentSaved = !this.service.salesExecReview.form.hasChanged(this.service.salesExecReview.formClone);

    if (!this.isLatestCommentSaved) {
      const message = 'You have unsaved changes. Are you sure you want to leave?';
      $event.returnValue = message;  // Sets the message for the dialog
      // Do not return anything, as the return type is void
    }
  }

  ngOnInit() {
    this.initParams();
    this.formatMeasures();
    this.createDiscussions();

    this.isEPR;

    this.subscriberRefresh = this.service.salesExecReview.formRefreshedEmitter.subscribe(res => {
      this.initParams();
      this.formatMeasures();
    })

    // If user moves to different month
    this.subscriberGet = this.service.salesExecReview.getFormEmitter.subscribe(res => {
      this.initParams();
      this.formatMeasures();
      this.createDiscussions();
    })

    this.newFormIdSubscriber = this.service.salesExecReview.newFormIdEmitter.subscribe((id: number) => {
      this.data.FormId = id;
    })

    // If user successfully updates approval state
    this.approvalStateUpdatedSubscriber = this.service.salesExecReview.approvalStateUpdatedEmitter.subscribe(() => {
      this.data.ReviewedByName = this.selections.user.Name;
      this.data.ReviewedDate = new Date();
    })
  }

  ngOnDestroy() {

    //this.isLatestCommentSaved = !this.service.salesExecReview.form.hasChanged(this.service.salesExecReview.formClone);

    if (!this.isLatestCommentSaved) {
      const confirmLeave = confirm('You have unsaved changes. Save comments before leaving?');

      if (!confirmLeave) {
        // throw new Error('Navigation cancelled'); // Prevent navigation by throwing an error
        // console.log("Pressed Cancel")

      }
      else {
        this.saveForm(false);
      }
    }

    if (!!this.subscriberRefresh) { this.subscriberRefresh.unsubscribe(); }
    if (!!this.subscriberGet) { this.subscriberGet.unsubscribe(); }
  }


  initParams() {

    //this.isLatestCommentSaved = true;
    this.data = this.service.salesExecReview.form;
    this.showWarning = false;
    this.charLimit = 4264;

    this.approvalStates = ['Draft', 'Locked', 'Final'];

    if (this.data.LastRefreshed) {
      this.lastRefreshed = `Last refreshed: ${this.cph.transform(this.data.LastRefreshed, 'dateAndTime', 0)}`;
    }
    else {
      this.lastRefreshed = `Last refreshed: ${this.cph.transform(new Date(), 'dateAndTime', 0)}`;
    }

    this.isReviewer = this.selections.user.permissions.salesExecReview == 'reviewer';
    this.isSysAdmin = this.selections.user.RoleName == 'System Administrator';
  }

  formatMeasures() {
    let editableScoringMeasures: string[] = ['New CEM (Rolling 3 months)', 'Used CEM (Rolling 3 months)'];
    let currencyScoringMeasures: string[] = ['Profit Pot'];
    let integerScoringMeasures: string[] = ['New Units', 'Used & Demo Units'];

    // if (this.service.salesExecReview.chosenSiteName == 'Three10 Automotive') {
    //   integerScoringMeasures.push('Non-Franchise Units');
    //   this.data.ScoringMeasures = this.data.ScoringMeasures.filter(x => x.MeasureName != 'Used CEM (Rolling 3 months)' && x.MeasureName != 'Profit Pot');
    // } else {
    // }

    this.data.ScoringMeasures = this.data.ScoringMeasures.filter(x => x.MeasureName != 'Non-Franchise Units');

    if (!this.data) { return; }

    this.data.ScoringMeasures.forEach(measure => {
      measure.PipeDP = 0;

      measure.displayName = measure.MeasureName;

      // Amend the display name for certain items
      if(this.service.salesExecReview.chosenSiteName.includes('Three10') || this.service.salesExecReview.chosenSiteName.includes('AutoNow'))
      {
        if(measure.MeasureName == 'New CEM (Rolling 3 months)')
        {
          measure.displayName = 'Autotrader Review score';
        }
        if(measure.MeasureName == 'Used CEM (Rolling 3 months)')
        {
          measure.displayName = 'Google Review score';
        }
      }

      if (currencyScoringMeasures.includes(measure.MeasureName)) {
        measure.PipeType = 'currency';
      } else if (integerScoringMeasures.includes(measure.MeasureName)) {
        measure.PipeType = 'number';

        let combinedObjective: number = 0;
        let combinedActual: number = 0;

        this.data.ScoringMeasures.filter(x => integerScoringMeasures.includes(x.MeasureName)).forEach(m => {
          combinedObjective += m.ObjectiveThisMonth;
          combinedActual += m.Actual;
        })

        if (combinedActual / combinedObjective < .75) measure.ActualPercentage = 0;
      } else {
        measure.PipeType = 'percent';
      }

      if (editableScoringMeasures.includes(measure.MeasureName)) {
        measure.Editable = true;

        if (this.service.salesExecReview.chosenSiteName != 'Three 10 Automotive') {
          measure.PipeDP = 2;
          measure.ObjectiveThisMonth = (measure.ObjectiveThisMonth / 2) * 10;
          measure.Actual = (measure.Actual / 2) * 10;
          measure.ObjectiveNextMonth = (measure.ObjectiveNextMonth / 2) * 10;
          measure.Editable = true;
          measure.PipeType = 'number';
        }
      }
    })

    let editableNonScoringMeasures: string[] = ['Deals with CEM Response %', 'New Renewals', 'Used Renewals', 'Telephone Appointment Ratio', 'Martec Right Words Score'];
    let currencyNonScoringMeasures: string[] = ['Profit per Unit (Products)'];
    let decimalNonScoringMeasures: string[] = ['Products per Unit'];
    let integerNonScoringMeasures: string[] = ['New Renewals', 'Used Renewals', 'New Enquiries', 'Orders Taken in the Month', 'Undelivered Orders', 'Days to Deliver'];

    let measuresWithActualOnly: string[] = ['Orders Taken in the Month', 'Undelivered Orders'];
    let measuresWithObjectiveAndActual: string[] = ['Days to Deliver'];

    this.data.NonScoringMeasures.forEach(measure => {
      if (currencyNonScoringMeasures.includes(measure.MeasureName)) {
        measure.PipeDP = 0;
        measure.PipeType = 'currency';
      } else if (decimalNonScoringMeasures.includes(measure.MeasureName)) {
        measure.PipeDP = 1;
        measure.PipeType = 'number';
      } else if (integerNonScoringMeasures.includes(measure.MeasureName)) {
        measure.PipeDP = 0;
        measure.PipeType = 'number';
      } else {
        measure.PipeDP = 0;
        measure.PipeType = 'percent';
      }

      if (editableNonScoringMeasures.includes(measure.MeasureName)) {
        measure.Editable = true;
      }

      if (measuresWithActualOnly.includes(measure.MeasureName)) {
        measure.HideObjective = true;
        measure.HidePercentage = true;
        measure.HideNextMonthObjective = true;
      }

      if (measuresWithObjectiveAndActual.includes(measure.MeasureName)) {
        measure.HidePercentage = true;
        measure.HideNextMonthObjective = true;
      }
    })
  }

  createDiscussions() {

    

    this.service.salesExecReview.formClone = Object.assign(new SalesExecReviewForm(), this.constants.clone(this.service.salesExecReview.form));
  }

  refreshForm() {
    this.selections.triggerSpinner.emit({ show: true, message: 'Refreshing form...' });

    let params: GetSEReviewFormParams = {
      Month: this.data.Month,
      SalesmanId: this.service.salesExecReview.chosenPersonId,
      SiteId: this.service.salesExecReview.chosenSiteIds[0]
    }

    this.service.salesExecReview.refreshFormEmitter.emit(params);

    setTimeout(() => {
      this.recalculateOverallScore();
    }, 1000)

    this.lastRefreshed = `Last refreshed: ${this.cph.transform(new Date(), 'dateAndTime', 0)}`;
  }

  saveForm(getNewForm: boolean, thenUpdateApprovalState?: boolean) {
    this.selections.triggerSpinner.emit({ show: true, message: 'Saving form...' });

    let measuresSimple: SEReviewMeasureSimple[] = [];
    let editableMeasures: string[] = ['New CEM (Rolling 3 months)', 'Used CEM (Rolling 3 months)'];

    this.data.ScoringMeasures.forEach(measure => {
      measuresSimple.push({
        MeasureName: measure.MeasureName,
        Actual: editableMeasures.includes(measure.MeasureName) && this.service.salesExecReview.chosenSiteName != 'Three 10 Automotive' ? (measure.Actual / 5) : measure.Actual
      })
    })

    this.data.NonScoringMeasures.forEach(measure => {
      measuresSimple.push({
        MeasureName: measure.MeasureName,
        Actual: measure.Actual
      })
    })

    let params: SaveSEReviewFormParams = {
      DiscussionSummaryActions: this.data.DiscussionSummaryActions,
      FormId: this.data.FormId,
      Month: this.data.Month,
      SEReviewMeasures: measuresSimple,
      SalesmanId: this.service.salesExecReview.chosenPersonId,
      getNewForm: getNewForm
    }

    this.service.salesExecReview.saveFormEmitter.emit({ SaveSEReviewFormParams: params, thenUpdateApprovalState: thenUpdateApprovalState });

    // If saved, the clone should be the same
    this.service.salesExecReview.formClone = this.constants.clone(this.service.salesExecReview.form);

    this.isEPR;
    //this.isLatestCommentSaved = true;
  }

  updateFormApprovalState(approvalState: string): void {

    this.selections.triggerSpinner.emit({ show: true, message: 'Updating approval state...' });

    this.data.ApprovalState = approvalState;

    // If form doesn't exist, create and add approval state
    if (this.data.FormId == null) {

      this.saveForm(true, true);

      let params: UpdateSEReviewFormApprovalStateParams = {
        ApprovalState: approvalState,
        FormId: this.data.FormId
      }

    }
    // Form should already exist, just update Approval state
    else {

      let params: UpdateSEReviewFormApprovalStateParams = {
        ApprovalState: approvalState,
        FormId: this.data.FormId
      }

      this.service.salesExecReview.updateFormApprovalStateEmitter.emit(params);
    }

  }

  setActual(measure: SEReviewScoringMeasure | SEReviewNonScoringMeasure, event: any) {
    if (!event.target || (event.target && event.target.value == '')) return;
    let actual = parseFloat(event.target.value);
    measure.Actual = measure.PipeType == 'percent' ? actual / 100 : actual;
    measure.ActualPercentage = measure.Actual / measure.ObjectiveThisMonth;
    this.recalculateOverallScore();
  }

  takeScreenshot() {
    let screenshotArea: HTMLElement = document.getElementById("screenshot-area");
    let fileName: string = `Sales Exec Review Form - ${this.data.SalesmanName} - ${this.cph.transform(this.data.Month, 'month', 0)} - ${this.cph.transform(new Date(), 'short', 0)}.pdf`;

    let pdf: jspdf = new jspdf('l', 'px', [screenshotArea.offsetWidth + 40, screenshotArea.offsetHeight + 40]);
    pdf.html(screenshotArea, {
      callback: (doc) => { doc.save(fileName); },
      x: 20,
      y: 20,
      width: screenshotArea.offsetWidth
    });
  }

  backToPeople() {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.getPeopleByMeasureRows(true);
  }

  recalculateOverallScore(): void {

    let newOverallScore = 0;

    this.data.ScoringMeasures.forEach(element => {
      let cemScoringMeasures: string[] = ['New CEM (Rolling 3 months)', 'Used CEM (Rolling 3 months)'];

      // If CEM do special checks
      if (cemScoringMeasures.includes(element.MeasureName)) {
        element.ActualPercentage = this.calculateScoreForCEM(element.Actual, element.ObjectiveThisMonth);
        element.Score = element.ActualPercentage * element.Weighting;
      } else {
        element.Score = element.ActualPercentage * element.Weighting;
      }

      newOverallScore += element.Score;

    });

    this.data.OverallScore = newOverallScore;
  }

  // Ensure logic matches backend (CalculateCEM)
  // We need this to calculate the score after manual input
  calculateScoreForCEM(actual: number, obj: number): number {

    let difference = actual - obj;
    
    const differenceRounded: number = Math.round(difference * 100) / 100;

    if (differenceRounded <= -0.06) { return 0; }
    if (differenceRounded <= -0.04) { return 0.40; }
    if (differenceRounded <= -0.02) { return 0.70; }
    if (differenceRounded > -0.02 && differenceRounded < 0.02) { return 1.00; }
    if (differenceRounded >= 0.1) { return 1.40; }
    if (differenceRounded >= 0.08) { return 1.32; }
    if (differenceRounded >= 0.06) { return 1.24; }
    if (differenceRounded >= 0.04) { return 1.16; }
    if (differenceRounded >= 0.02) { return 1.08; }

  }

  updateComment(event: any, summaryAction: DiscussionSummaryAction) {
    if (event.target.value == '') {
      summaryAction.Text = null;
      return;
    }
    
    summaryAction.Text = event.target.value;

    let currentLength = summaryAction.Text.length;

    if (currentLength > this.charLimit) {
      event.preventDefault(); // Prevent additional input
      summaryAction.Text = summaryAction.Text.substring(0, this.charLimit);
    }

  }

  maybeClearPlaceholder(event: any) {
    if (event.target.innerHTML.includes('Start typing here...')) event.target.innerHTML = null;
  }
}
