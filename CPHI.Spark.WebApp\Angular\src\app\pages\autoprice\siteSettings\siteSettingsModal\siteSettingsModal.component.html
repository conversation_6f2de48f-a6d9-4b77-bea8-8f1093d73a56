<div class="modal-header">
  <h4 class="modal-title" id="modal-basic-title">
    AutoPrice Settings - {{ service.selectedSite.Name }}
  </h4>
  <button type="button" class="close" aria-label="Close" (click)="closeModal()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body" [ngClass]="service.constants.environment.customer">

  <div class="column">

    <!-- Pricing Strategy Choice -->
    <div class="autotraderCard" *ngIf="service.selections.user.permissions.canEditPricingStrategy">
      <div class="cardInner">
        <div class="cardHeader">Pricing Strategy</div>
        <div class="cardBody">

          <br>
          <!-- Pick new strategy -->
          <div class="d-inline-block" ngbDropdown container="body" popoverClass="infoPopover">
            <button class="btn btn-primary" id="newStrategyDropdown" ngbDropdownToggle>
              {{ service.selectedSite.strategyName }}
            </button>

            <div ngbDropdownMenu aria-labelledby="newStrategyDropdown">

              <!-- New strategies to choose from -->
              <ng-container *ngFor="let strategy of service.strategies">
                <button ngbDropdownItem (click)="chooseNewStrategy(strategy)" ngbDropdownToggle
                        class="manualToggleCloseItem">
                  {{ strategy.strategyDetailedName }}
                </button>
              </ng-container>


            </div>
          </div>
          &nbsp;
          <button class="btn btn-success" (click)="reviewStrategy(service.selectedSite.StrategySelectionRuleSetId)">
            Review pricing strategy
          </button>


        </div>
      </div>
    </div>


    <!-- Buying Strategy Choice -->
    <div class="autotraderCard" *ngIf="service.selections.user.permissions.canEditPricingStrategy">
      <div class="cardInner">
        <div class="cardHeader">Buying Strategy</div>
        <div class="cardBody">

          <br>
          <!-- Pick new strategy -->
          <div class="d-inline-block" ngbDropdown container="body" popoverClass="infoPopover">
            <button class="btn btn-primary" id="newStrategyDropdown" ngbDropdownToggle>
              {{ service.selectedSite.buyingStrategyName }}
            </button>

            <div ngbDropdownMenu aria-labelledby="newStrategyDropdown">

              <!-- New strategies to choose from -->
              <ng-container *ngFor="let strategy of service.strategies">
                <button ngbDropdownItem (click)="chooseNewBuyingStrategy(strategy)" ngbDropdownToggle
                        class="manualToggleCloseItem">
                  {{ strategy.strategyDetailedName }}
                </button>
              </ng-container>


            </div>
          </div>
          &nbsp;
          <button class="btn btn-success"
                  (click)="reviewStrategy(service.selectedSite.BuyingStrategySelectionRuleSetId)">Review pricing
            strategy
          </button>


        </div>
      </div>
    </div>

    <!-- Buying Strategy Choice 2-->
    <div class="autotraderCard"
         *ngIf="service.selections.user.permissions.canEditPricingStrategy">
      <div class="cardInner">
        <div class="cardHeader">Buying Strategy 2</div>
        <div class="cardBody">

          <br>
          <!-- Pick new strategy -->
          <div class="d-inline-block" ngbDropdown container="body" popoverClass="infoPopover">
            <button class="btn btn-primary" id="newStrategyDropdown" ngbDropdownToggle>
              {{ service.selectedSite.buyingStrategy2Name }}
            </button>

            <div ngbDropdownMenu aria-labelledby="newStrategyDropdown">

              <!-- New strategies to choose from -->
              <ng-container *ngFor="let strategy of service.strategies">
                <button ngbDropdownItem (click)="chooseNewBuying2Strategy(strategy)" ngbDropdownToggle
                        class="manualToggleCloseItem">
                  {{ strategy.strategyDetailedName }}
                </button>
              </ng-container>


            </div>
          </div>
          &nbsp;
          <button class="btn btn-success"
                  (click)="reviewStrategy(service.selectedSite.BuyingStrategySelectionRuleSet2Id)">Review pricing
            strategy
          </button>


        </div>
      </div>
    </div>

    <!-- Test Strategy Choice -->
    <div class="autotraderCard" *ngIf="service.selections.user.permissions.canEditPricingStrategy">
      <div class="cardInner">
        <div class="cardHeader">Test Strategy</div>
        <div class="cardBody">

          <br>
          <!-- Pick new strategy -->
          <div class="d-inline-block" ngbDropdown container="body" popoverClass="infoPopover">
            <button class="btn btn-primary" id="newStrategyDropdown" ngbDropdownToggle>
              {{ service.selectedSite.testStrategyName }}
            </button>

            <div ngbDropdownMenu aria-labelledby="newStrategyDropdown">

              <!-- New strategies to choose from -->
              <ng-container *ngFor="let strategy of service.strategies">
                <button ngbDropdownItem (click)="chooseNewTestStrategy(strategy)" ngbDropdownToggle
                        class="manualToggleCloseItem">
                  {{ strategy.strategyDetailedName }}
                </button>
              </ng-container>


            </div>
          </div>
          &nbsp;
          <button class="btn btn-success" (click)="reviewStrategy(service.selectedSite.TestStrategySelectionRuleSetId)">
            Review pricing strategy
          </button>


        </div>
      </div>
    </div>


    <!-- Competitor Search -->
    <div class="autotraderCard">
      <div class="cardInner">
        <div class="cardHeader">Competitor Search</div>
        <div class="cardBody">
          <div class="labelAndInputContainer">
            <span class="labelForInput">Plate Range</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2" [(ngModel)]="service.selectedSite.CompetitorPlateRange"
                     [min]="0">
              <instructionButton
                [note]="'This specifies the number of plates either side of a vehicle’s plate that the competitor search should default to searching. For example a value of 1 would mean that for a 70 plate vehicle Spark would include 20 plate and 21 plate vehicles.'">
              </instructionButton>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- Buying Opps -->
    <div class="autotraderCard">
      <div class="cardInner">
        <div class="cardHeader">Buying opportunities report</div>
        <div class="cardBody">

          <!-- Minimum Opportunity -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Minimum opportunity</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2" [(ngModel)]="service.selectedSite.LocalBargainThreshold"
                     [min]="1">
              <instructionButton
                [note]="'This specifies the minimum opportunity required to record a buying opportunity. Spark will search all relevant derivatives for sale within the search radius of each dealership each day to identify buying opportunities. It will compare the current selling price with what the case Spark will still generate the price change however it will not apply it. This setting can be useful to avoid overwhelming the onsite team with price hanger changes.'">
              </instructionButton>

            </div>
          </div>

          <!-- Radius -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Search Radius (Miles)</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.LocalBargainsSearchRadius" [min]="0">
              <instructionButton
                [note]="'The distance from the dealership to search for buying opportunities, measured in miles. Default is 50.'">
              </instructionButton>
            </div>
          </div>

          <!-- Retail Rating -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Minimum Retail Rating</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.LocalBargainsMinRetailRating" [min]="0">
              <instructionButton
                [note]="'The minimum retail rating for recording a buying opportunity. Default is 0.'">
              </instructionButton>
            </div>
          </div>


        </div>
      </div>
    </div>


    <!-- Buying Targets -->
    <div class="autotraderCard">
      <div class="cardInner">
        <div class="cardHeader">Buying Targets</div>
        <div class="cardBody">

          <!-- Margin -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Margin</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2" [(ngModel)]="service.selectedSite.TargetMargin"
                     [min]="1">
              <instructionButton
                [note]="'This sets the margin requirement which is used to populate the initial figures in a single and bulk valuation.'">
              </instructionButton>

            </div>
          </div>

          <!-- AdditionalMech -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Additional Mech.</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.TargetAdditionalMech" [min]="0">
              <instructionButton
                [note]="'This sets the prep cost which is used to populate the initial figures in a single and bulk valuation.'">
              </instructionButton>
            </div>
          </div>

          <!-- TargetPaintPrep -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Paint Prep</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.TargetPaintPrep" [min]="0">
              <instructionButton
                [note]="'This sets the paint prep cost which is used to populate the initial figures in a single and bulk valuation.'">
              </instructionButton>
            </div>
          </div>

          <!-- TargetAuctionFee -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Auction Fee</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.TargetAuctionFee" [min]="0">
              <instructionButton
                [note]="'This sets the auction fee cost which is used to populate the initial figures in a single and bulk valuation.'">
              </instructionButton>
            </div>
          </div>

          <!-- TargetDelivery -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Delivery</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.TargetDelivery" [min]="0">
              <instructionButton
                [note]="'This sets the delivery cost which is used to populate the initial figures in a single and bulk valuation.'">
              </instructionButton>
            </div>
          </div>

          <!-- TargetOtherCost -->
          <div class="labelAndInputContainer">
            <span class="labelForInput">Other Cost</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.TargetOtherCost" [min]="0">
              <instructionButton
                [note]="'This sets the other cost which is used to populate the initial figures in a single and bulk valuation.'">
              </instructionButton>
            </div>
          </div>


        </div>
      </div>
    </div>



  </div>

  <div class="column">
    <ng-container>


      <!-- Which days for autopricing -->
      <div class="autotraderCard">
        <div class="cardInner">
          <div class="cardHeader">Automatic pricing - timing</div>
          <div class="cardBody">
            <p class="bold">Update prices on:</p>

            <div class="switchContainer">
              <span class="labelForInput">Monday</span>
              <div class="d-flex align-items-center">
                <sliderSwitch (toggle)="togglePricesOnDay('Mon')" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                              [defaultValue]="service.selectedSite.UpdatePricesMon">
                </sliderSwitch>
                <instructionButton
                  [note]="'Determines whether Spark should action price changes on a Monday through Friday.'">
                </instructionButton>
              </div>
            </div>
            <div class="switchContainer">
              <span class="labelForInput">Tuesday</span>
              <div class="d-flex align-items-center">
                <sliderSwitch (toggle)="togglePricesOnDay('Tue')" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                              [defaultValue]="service.selectedSite.UpdatePricesTue">
                </sliderSwitch>
                <instructionButton
                  [note]="'Determines whether Spark should action price changes on a Monday through Friday.'">
                </instructionButton>
              </div>
            </div>
            <div class="switchContainer">
              <span class="labelForInput">Wednesday</span>
              <div class="d-flex align-items-center">
                <sliderSwitch (toggle)="togglePricesOnDay('Wed')" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                              [defaultValue]="service.selectedSite.UpdatePricesWed">
                </sliderSwitch>
                <instructionButton
                  [note]="'Determines whether Spark should action price changes on a Monday through Friday.'">
                </instructionButton>
              </div>
            </div>
            <div class="switchContainer">
              <span class="labelForInput">Thursday</span>
              <div class="d-flex align-items-center">
                <sliderSwitch (toggle)="togglePricesOnDay('Thu')" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                              [defaultValue]="service.selectedSite.UpdatePricesThu">
                </sliderSwitch>
                <instructionButton
                  [note]="'Determines whether Spark should action price changes on a Monday through Friday.'">
                </instructionButton>
              </div>
            </div>
            <div class="switchContainer">
              <span class="labelForInput">Friday</span>
              <div class="d-flex align-items-center">
                <sliderSwitch (toggle)="togglePricesOnDay('Fri')" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                              [defaultValue]="service.selectedSite.UpdatePricesFri">
                </sliderSwitch>
                <instructionButton
                  [note]="'Determines whether Spark should action price changes on a Monday through Friday.'">
                </instructionButton>
              </div>
            </div>
            <div class="switchContainer">
              <span class="labelForInput">Saturday</span>
              <div class="d-flex align-items-center">
                <sliderSwitch (toggle)="togglePricesOnDay('Sat')" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                              [defaultValue]="service.selectedSite.UpdatePricesSat">
                </sliderSwitch>
                <instructionButton
                  [note]="'Determines whether Spark should action price changes on a Saturday'">
                </instructionButton>
              </div>
            </div>
            <div class="switchContainer">
              <span class="labelForInput">Sunday</span>
              <div class="d-flex align-items-center">
                <sliderSwitch (toggle)="togglePricesOnDay('Sun')" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                              [defaultValue]="service.selectedSite.UpdatePricesSun">
                </sliderSwitch>
                <instructionButton
                  [note]="'Determines whether Spark should action price changes on a Sunday.'">
                </instructionButton>
              </div>
            </div>
            <div class="switchContainer">
              <span class="labelForInput">Public holidays</span>
              <div class="d-flex align-items-center">
                <sliderSwitch (toggle)="togglePricesOnDay('PubHolidays')" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                              [defaultValue]="service.selectedSite.UpdatePricesPubHolidays">
                </sliderSwitch>
                <instructionButton
                  [note]="'Determines whether Spark should action price changes on public holidays.'">
                </instructionButton>
              </div>
            </div>
            <div class="labelAndInputContainer">
              <span class="labelForInput">When to action prices each day</span>
              <div class="d-flex align-items-center">
                <div class="d-inline-block me-2" ngbDropdown container="body"
                     popoverClass="infoPopover">
                  <button class="btn btn-primary" [disabled]="!service.selections.user.permissions.canEditPricingStrategy" id="newStrategyDropdown" ngbDropdownToggle>
                    {{ service.selectedSite.WhenToActionChangesEachDay }}:00
                  </button>

                  <div ngbDropdownMenu aria-labelledby="newStrategyDropdown">
                    <ng-container
                      *ngFor="let hour of [8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]">
                      <button ngbDropdownItem
                              (click)="service.selectedSite.WhenToActionChangesEachDay = hour"
                              ngbDropdownToggle class="manualToggleCloseItem">
                        {{ hour < 10 ? 0 : null }}{{ hour }}:00
                      </button>
                    </ng-container>
                  </div>
                </div>
                <instructionButton
                  [note]="'This setting determines when Spark will go ahead and action the price changes each day. Note that sites can still mark a day\'s worth of price changes as \'approved\' earlier than this which will immediately action the changes.'">
                </instructionButton>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- Opt outs -->
      <div class="autotraderCard">
        <div class="cardInner">
          <div class="cardHeader">Automatic pricing - opting out</div>
          <div class="cardBody">

            <div class="labelAndInputContainer">
              <span class="labelForInput">Maximum length in days of opt-out request</span>
              <div class="d-flex align-items-center">
                <input type="number" class="me-2" [(ngModel)]="service.selectedSite.MaximumOptOutDays">
                <instructionButton
                  [note]="'Controls how long a user can opt for a vehicle to be opted out of auto-pricing for.'">
                </instructionButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <!-- Location optimiser stuff -->
    <div class="autotraderCard" *ngIf="!service.constants.environment.isSingleSiteGroup">
      <div class="cardInner">
        <div class="cardHeader">Location optimiser report</div>
        <div class="cardBody">
          <div class="labelAndInputContainer">
            <span class="labelForInput">Cost per mile per move</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.LocationMovePoundPerMile">
              <instructionButton [note]="'This is used to cost up possible moves between sites'">
              </instructionButton>
            </div>
          </div>
          <div class="labelAndInputContainer">
            <span class="labelForInput">Fixed cost per move</span>
            <div class="d-flex align-items-center">
              <input type="number" class="me-2"
                     [(ngModel)]="service.selectedSite.LocationMoveFixedCostPerMove">
              <instructionButton [note]="'This is used to cost up possible moves between sites'">
              </instructionButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Automatic Pricing -->
    <div class="autotraderCard">
      <div class="cardInner">
        <div class="cardHeader">Automatic pricing</div>
        <div class="cardBody">
          <div id="autoUpdatePricesContainer" class="switchContainer ">
            <span class="labelForInput">Automatically update prices</span>
            <sliderSwitch [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
              (toggle)="service.selectedSite.UpdatePricesAutomatically = !service.selectedSite.UpdatePricesAutomatically"
              [defaultValue]="service.selectedSite.UpdatePricesAutomatically">
            </sliderSwitch>
          </div>

          <ng-container>
            <div class="labelAndInputContainer">
              <span class="labelForInput">Gap to strategy to generate a price change down</span>
              <div class="d-flex align-items-center">
                <input type="number" class="me-2" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                       [(ngModel)]="service.selectedSite.MinimumAutoPriceDecrease">
                <instructionButton
                  [note]="'This setting controls which automatically generated price changes will be actioned by Spark. For example a vehicle currently on sale at £12,500 will not be priced down to a strategy price of £12,450 if minimum gap is set to -£100. In this case Spark will still indicate the difference vs strategy but will not generate a price change. This setting can be useful to avoid overwhelming the onsite team with price hanger changes.'">
                </instructionButton>
              </div>
            </div>
            <div class="labelAndInputContainer">
              <span class="labelForInput">Gap to strategy to generate a price change up</span>
              <div class="d-flex align-items-center">
                <input type="number" class="me-2" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                       [(ngModel)]="service.selectedSite.MinimumAutoPriceIncrease">
                <instructionButton
                  [note]="'This setting controls which automatically generated price changes will be actioned by Spark. For example a vehicle currently on sale at £12,500 will not be priced up to a strategy price of £12,550 if the minimum gap is set to +£100. In this case Spark will still indicate the difference vs strategy but will not generate the price change. This setting can be useful to avoid overwhelming the onsite team with price hanger changes.'">
                </instructionButton>
              </div>
            </div>
            <div class="labelAndInputContainer">
              <span class="labelForInput">Gap to strategy to generate a price change down %</span>
              <div class="d-flex align-items-center">
                <input type="number" class="me-2" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                       [(ngModel)]="service.selectedSite.MinimumAutoPricePercentDecrease">
                <instructionButton
                  [note]="'This setting controls which automatically generated price changes will be actioned by Spark. For example a vehicle currently on sale at £12,500 will not be priced down to a strategy price of £12,450 if minimum % gap is set to -1%. In this case Spark will still indicate the difference vs strategy but will not generate a price change. This setting can be useful to avoid overwhelming the onsite team with price hanger changes.'">
                </instructionButton>
              </div>
            </div>

            <div class="labelAndInputContainer">
              <span class="labelForInput">Gap to strategy to generate a price change up %</span>
              <div class="d-flex align-items-center">
                <input type="number" class="me-2" [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                       [(ngModel)]="service.selectedSite.MinimumAutoPricePercentIncrease">
                <instructionButton
                  [note]="'This setting controls which automatically generated price changes will be actioned by Spark. For example a vehicle currently on sale at £12,500 will not be priced up to a strategy price of £12,550 if the minimum gap is set to 1%. In this case Spark will still indicate the difference vs strategy but will not generate the price change. This setting can be useful to avoid overwhelming the onsite team with price hanger changes.'">
                </instructionButton>
              </div>
            </div>

            <div id="autoUpdatePricesContainer" class="switchContainer ">
              <span class="labelForInput">Include Unpublished Ads in Daily Email Report</span>
              <sliderSwitch [disabled]="!service.selections.user.permissions.canEditPricingStrategy"
                (toggle)="service.selectedSite.IncludeUnPublishedAdsInEmailReport = !service.selectedSite.IncludeUnPublishedAdsInEmailReport"
                [defaultValue]="service.selectedSite.IncludeUnPublishedAdsInEmailReport">
              </sliderSwitch>
            </div>
          </ng-container>

        </div>
      </div>
    </div>

  </div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-success" (click)="save()">Save</button>
  <button type="button" class="btn btn-primary" (click)="closeModal()">Close</button>
</div>
