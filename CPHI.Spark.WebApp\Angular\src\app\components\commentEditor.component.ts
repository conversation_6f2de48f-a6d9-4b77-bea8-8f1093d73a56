import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CphPipe } from "../cph.pipe";
import { CommentTextAndDeal, DealComment, NewCommentParams } from '../model/sales.model';
import { WhiteboardService } from "../pages/whiteboard/whiteboard.service";
import { ConstantsService } from '../services/constants.service';
import { GetDataMethodsService } from '../services/getDataMethods.service';
import { SelectionsService } from '../services/selections.service';


@Component({
  selector: "app-commentEditor",
  template: `
    <ng-template #modalRef let-modal>
      <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title"><i class="fas fa-comment"></i> {{ dealStockNumber }}  </h4> 
        <!-- {{ deal.Description }} - {{ deal.Customer }} -->
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div [ngClass]="constants.environment.customer" class="modal-body">
        <table id="commentsTable" class="cph fullWidth darkerRows ">
          <tbody>

            <!-- Existing Comment -->
            <tr *ngFor="let comment of comments; let i = index">
              <td>{{ comment.Date |cph:'date' :0}}</td>
              <td class="commentCell">
                <div *ngIf="!comment.isEditing" class="commentText">{{ comment.Text }}</div>
                <input *ngIf="comment.isEditing" (keydown.enter)="saveExistingComment(comment)" ngbAutofocus (ngModelChange)="initDateAndUser()" [(ngModel)]="comment.Text" />
              </td>
              <td>
                <div class="initials long">{{ comment.Name }}</div>
              </td>
              <td>
                <div *ngIf="selections.user.PersonId == comment.PersonId" class="buttonsHolder">  

                  <div (click)="editComment(comment)"><i class="goodFont addComment fas fa-edit"></i></div>  

                  <div (click)="deleteComment(comment.Id)">
                      <i  class="badFont deleteComment fas fa-times-circle"></i>
                  </div>

                </div>
              </td>
            </tr>
            
            
            <!-- New Comment -->
            <tr class="newComment">
              <td>{{ newComment.Date |cph:'date':0 }}</td>
              <td>
                <input (keydown.enter)="saveComment()" ngbAutofocus (ngModelChange)="initDateAndUser()" [(ngModel)]="newComment.Text" />
              </td>
              <td>
                <span *ngIf="userName">{{ userName }}</span>
              </td>
              <td>
              
              <div *ngIf="newComment?.Text.length > 0" (click)="saveComment()"><i class="goodFont addComment fas fa-check-circle"></i></div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">{{constants.translatedText.Close}}</button>
      </div>
    </ng-template>
  `,
  styles: [
    `
    .buttonsHolder{display:flex;}
    .modal-header,.modal-footer{padding: 0.4rem 1rem;}
    .modal-body{min-height: 30vh;max-height:60vh!important;}
    table{margin-top:2em;}
    table.cph tbody td{text-align:left;}
    .deleteComment{opacity:0.5;transition:ease all 0.2s;cursor:pointer}
    .deleteComment:hover{opacity:1;transform:scale(1.5);}
    .addComment{transition:ease all 0.2s;cursor:pointer;}
    .addComment:hover{transform:scale(1.5);}
    svg{}
    #commentsTable input{text-align: left;      height: 2.2em;      border: 1px solid var(--grey80);      background: white;}    
    .commentCell{max-width:50em;}
    .commentText{line-height:2em;padding:1em 0em;}
    .fa-edit{margin-right: 30px;}
    `
  ]
})
export class CommentEditorComponent implements OnInit {
  @ViewChild("modalRef", { static: true }) modalRef: ElementRef;
  @Input() public comments: CommentTextAndDeal[];
  @Input() public dealStockNumber: string;
  @Input() public dealId: number;


  newComment: DealComment;
  userName: string;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cph: CphPipe,
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public dataMethods: GetDataMethodsService,
    public whiteBoardService: WhiteboardService,


  ) { }

  ngOnDestroy() { }

  ngOnInit() {
    this.initParams();
  }

  initParams() {
    //this.deal = this.constants.Deals.find(d => d.Id === this.givenDealId);
    //initiate new comment
    this.initComment();


    //launch modal

    this.modalService.open(this.modalRef, { windowClass: "dealDetailsModal", keyboard: false, ariaLabelledBy: "modal-basic-title" }).result.then(
      result => {
        //'okd'
        this.passBack();
        //this.modalService.dismissAll();
      },
      //closed
      reason => {
        //cancelled, so no passback
        this.passBack();

        // If on the Whiteboard page, refresh it to show marker
        if(this.whiteBoardService.dealsForEachSalesExec)
        {
          this.whiteBoardService.getDeals();
        }
        
      }
    );

  }


  initComment() {
    this.newComment = {
      DealId: null,
      Text: "",
      Date: null,
      PersonId: null,
      StockNumber: this.dealStockNumber,
      Name: this.selections.user.Name,
      Initials: null,
    };
  }

  initDateAndUser() {

    if (!this.newComment.Date) {
      this.newComment.Date = new Date();
      this.newComment.PersonId = this.selections.user.PersonId;
      this.newComment.Initials = "";

      this.userName = this.selections.user.Name;
      let names = this.selections.user.Name.split(" ");

      names.forEach(name => {
        this.newComment.Initials = this.newComment.Initials + name.substring(0, 1);
      });
    }
  }

  
  passBack() {
    let thingToPassBack = "foo";
    this.activeModal.close(thingToPassBack); //<--put things in here
  }

  saveComment() {
    if (this.newComment.Text == "") {
      return;
    }

    let commentToAddParams: NewCommentParams = {
      Text: this.newComment.Text,
      StockNumber: this.dealStockNumber,
      DealId: this.dealId
    };


    this.dataMethods.addNewComment(commentToAddParams).subscribe((newCommentId: number) => {
      let newItem: CommentTextAndDeal = {
        Id: newCommentId,
        StockNumber: this.newComment.StockNumber,
        Text: this.newComment.Text,
        Name: this.selections.user.Name,
        Initials: this.constants.makeInitials(this.selections.user.Name),
        PersonId: this.selections.user.PersonId,
        Date: new Date(),
        isEditing:false
      };
      
      this.newComment.Id = newCommentId
      this.comments.push(newItem);
      this.selections.commentsChanged.emit();
      this.initComment();
    },
      e => {
        console.error("failed to save comment " + JSON.stringify(e));
      }
    );


  }

  saveExistingComment(comment: CommentTextAndDeal) {
    this.dataMethods.updateComment(comment.Id, comment.Text).subscribe(res => {
      //success
      this.selections.commentsChanged.emit();
      this.constants.toastSuccess('Comment updated')
      comment.isEditing = false;
    })
  }

  deleteComment(commentId: number) {

    // Need to add translation here!
    this.constants.alertModal.title = "Really delete this comment?";
    this.constants.alertModal.message = "";

    this.modalService.open(this.constants.alertModal.elementRef, { size: "sm", keyboard: false, ariaLabelledBy: "modal-basic-title" }).result.then(result => {
      //have chosen to 'OK' selections
      this.dataMethods.deleteComment(commentId).subscribe((result: any) => {

        //success
        this.comments = this.comments.filter(x => x.Id !== commentId);
        this.selections.commentsChanged.emit();
        this.initComment();

      },
        reason => {
          //chose to cancel, do nothing
          return;
        }
      )
    }
    )
  }


  editComment(comment: CommentTextAndDeal) {
    this.comments.map(x => x.isEditing = false)
    comment.isEditing = true;
  }


}
