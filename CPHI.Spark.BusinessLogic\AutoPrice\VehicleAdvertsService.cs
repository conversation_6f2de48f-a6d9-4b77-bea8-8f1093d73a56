﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CPHI.Repository;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.Services;
using Microsoft.AspNetCore.Identity;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
    public class VehicleAdvertsService
    {
        public readonly string _connString;

        public VehicleAdvertsService(string connString)
        {
            this._connString = connString;
        }


        public async Task<IEnumerable<VehicleAdvertWithRating>> GetVehicleAdvertsWithRatings(
            GetVehicleAdvertsWithRatingsParams parms,
            Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions,
            DealerGroupName dealerGroupName,
            List<int> retailerSiteIds,
            List<int> siteIds,
            List<string> lifecycleStatuses
            )
        {
            var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connString);
            var allResults = await vehicleAdvertsDataAccess.GetVehicleAdvertsWithRatingsFromDb(parms.EffectiveDate, bandingDefinitions, dealerGroupName, lifecycleStatuses, null);

            // Apply all filters 
            allResults = AdvertFilteringService.FilterAdverts(allResults, parms, retailerSiteIds, siteIds);// allResults
            return allResults;
        }
        public async Task<List<AdvertIdAndCompetitorLink>> GetAdvertsAndCompetitorLinks(DealerGroupName dealerGroupName, List<int> advertIds)
        {
            var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connString);
            return await vehicleAdvertsDataAccess.GetAdsAndCompetitorLinks(dealerGroupName, advertIds);
        }


        //For getting VehicleAdvertWithRatings from db cache table
        //public async Task<IEnumerable<VehicleAdvertWithRating>> FetchAndFilterVehicleAdvertsFromDbCacheTable(GetVehicleAdvertsWithRatingsParams parms, DealerGroupName dealerGroup, List<int> siteIds, List<int> retailerSiteIds, List<string> lifecycleStatuses)
        //{
        //    var adverts = await FetchVehicleAdvertsWithRatingsFromMainDbTables(parms.EffectiveDate, dealerGroup, lifecycleStatuses, null);
        //    var filtered = AdvertFilteringService.FilterAdverts(adverts, parms, retailerSiteIds, siteIds);
        //    return filtered;
        //}
       


        //public async Task RegenerateAdvertsAsync(DealerGroupName dealerGroup, DateTime chosenDate)
        //{
        //    List<string> lifecycles = AutoPriceHelperService.GetAllLifecycleStatuses();
        //    using (var db = new CPHIDbContext(_connString))
        //    {

        //        var strategy = db.Database.CreateExecutionStrategy();
        //        await strategy.ExecuteAsync(async () =>
        //        {
        //            // Start a transaction
        //            using (var transaction = await db.Database.BeginTransactionAsync())
        //                try
        //                {
        //                    {
        //                        // Generate new adverts for today
        //                        var newAdverts = await FetchVehicleAdvertsWithRatingsFromMainDbTables(chosenDate, dealerGroup, lifecycles, null);

        //                        // Remove existing adverts for the chosen date and dealer group
        //                        var existingAdverts = await db.VehicleAdvertItems
        //                                .Where(a => a.DealerGroup_Id == (int)dealerGroup && a.SnapshotDate.Date == chosenDate.Date)
        //                                .ToListAsync();

        //                        db.VehicleAdvertItems.RemoveRange(existingAdverts);

        //                        // Save new adverts
        //                        List<VehicleAdvertItem> toPersist = newAdverts.ToList().ConvertAll(x => new VehicleAdvertItem(x, chosenDate, (int)dealerGroup));
        //                        await db.VehicleAdvertItems.AddRangeAsync(toPersist);

        //                        // Save changes
        //                        await db.SaveChangesAsync();

        //                        //-----------------------
        //                        // Commit the transaction
        //                        //-----------------------
        //                        await transaction.CommitAsync();
        //                    }

        //                }
        //                catch (Exception ex)
        //                {
        //                    // Rollback transaction if any operation fails
        //                    await transaction.RollbackAsync();
        //                    // Handle exception (e.g., log the error, throw it, etc.)
        //                    throw new InvalidOperationException("An error occurred during the transaction.", ex);
        //                }
        //        });

                
        //    }
        //}


        //For getting VehicleAdvertsWithRatings directly from db main tables
        public async Task<IEnumerable<VehicleAdvertWithRating>> FetchAndFilterVehicleAdvertsFromMainDbTables(GetVehicleAdvertsWithRatingsParams parms, DealerGroupName dealerGroup, List<int> siteIds, List<int> retailerSiteIds, List<string> lifecycleStatuses)
        {
            var adverts = await FetchVehicleAdvertsWithRatingsFromMainDbTables(parms.EffectiveDate, dealerGroup, lifecycleStatuses, null);
            var filtered = AdvertFilteringService.FilterAdverts(adverts, parms, retailerSiteIds, siteIds);
            return filtered;
        }
        public async Task<IEnumerable<VehicleAdvertWithRating>> FetchVehicleAdvertsWithRatingsFromMainDbTables(DateTime? chosenDateIn, DealerGroupName dealerGroup, List<string> lifecycleStatuses, List<int> advertIds)
        {
            DateTime chosenDate = chosenDateIn.HasValue ? (DateTime)chosenDateIn : DateTime.Now.Date;
            var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connString);
            var retailerSitesDataAccess = new RetailerSitesDataAccess(_connString);
            var bandingDefinitions = await retailerSitesDataAccess.GetRetailerStrategyBandings();
            Dictionary<int, RetailerSiteStrategyBandingDefinition> bandings = await retailerSitesDataAccess.GetRetailerStrategyBandings();
            IEnumerable<VehicleAdvertWithRating> adverts = await vehicleAdvertsDataAccess.GetVehicleAdvertsWithRatingsFromDb(chosenDate, bandingDefinitions, dealerGroup, lifecycleStatuses, advertIds);
            var advertsIds = adverts.Select(a => a.AdId).ToList();
            var advertPortalOptionsTask = vehicleAdvertsDataAccess.GetVehicleAdvertsPortalOptions(advertsIds, dealerGroup);
            var searchHistoryTask = vehicleAdvertsDataAccess.GetVehicleAdvertSearchHistory(dealerGroup, chosenDate);

            await Task.WhenAll(advertPortalOptionsTask, searchHistoryTask);

            var optionsLookup = advertPortalOptionsTask.Result.ToLookup(x => x.VehicleAdvert_Id);
            var historyLookup = searchHistoryTask.Result.ToLookup(x => x.AdvertId);

            var dealsDataAccess = new DealDataAccess(_connString);

            var siteModelSellRates = await dealsDataAccess.GetSiteModelSellRates(dealerGroup);
            Dictionary<string, SiteModelSellRate> siteModelLookup = siteModelSellRates.ToDictionary(x => x.SiteIdModel);

            foreach (var advert in adverts)
            {
                //if(advert.VehicleReg == "MM67ZXK")
                //{
                //    { }
                //}

                var allOptionNames = optionsLookup[advert.AdId];// advertPortalOptions.Where(a => a.VehicleAdvert_Id == advert.AdId).SelectMany(s => s.PortalOption.OptionName);
                if (allOptionNames.Count() > 0)
                {
                    advert.PortalOptions = string.Join(",", allOptionNames.Select(x => x.PortalOption.OptionName));
                    advert.VehicleHasOptionsSpecified = true;
                }

                string thisAdSiteAndModel = $"{advert.SiteId.ToString()}|{advert.ModelCleanedUp}";

                SiteModelSellRate thisAdSellRate = null;
                if (siteModelLookup.TryGetValue(thisAdSiteAndModel, out thisAdSellRate))
                {
                    advert.ModelSellRate = thisAdSellRate.Sold;
                }

                var thisAdSearches = historyLookup[advert.AdId];
                if (thisAdSearches != null && thisAdSearches.Count() > 0)
                {
                    var sorted = thisAdSearches.OrderBy(x => x.SnapshotDate);
                    var earliest = sorted.First().SnapshotDate;
                    int missingCount = (earliest - DateTime.Now.AddDays(-7)).Days;

                    //build up List
                    advert.DailyAdvertViewsLast7 = new List<int>();
                    advert.DailySearchViewsLast7 = new List<int>();
                    for (int i = 0; i < missingCount; i++)
                    {
                        advert.DailyAdvertViewsLast7.Add(0);
                        advert.DailySearchViewsLast7.Add(0);
                    }
                    foreach (var item in sorted)
                    {
                        advert.DailyAdvertViewsLast7.Add(item.AdvertViews);
                        advert.DailySearchViewsLast7.Add(item.SearchViews);
                    }

                }

            }

            return adverts.OrderBy(x => x.DaysListed);
        }


    }
}
