//core angular
import { Component, OnInit } from '@angular/core';
//services
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
//pipes and interceptors
import { Month, selectableItem, DateSelectionObject, ServiceSalesSiteRow } from '../../model/main.model';
import { PartsSummaryService } from './partsSummary.service';

@Component({
  selector: 'app-partsSummary',
  templateUrl: './partsSummary.component.html',
  styleUrls: ['./partsSummary.component.scss']
})

export class PartsSummaryComponent implements OnInit {
  months: Array<Month>;
  monthsOffsetNumber: number = 0;
  channelsDropdownList: selectableItem[];
  showAllChannels: boolean = true;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: PartsSummaryService
  ) { }

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.selections.initiatePartsSummary();
    this.service.getData();
  }

  makeMonths(offset?: number) {
    this.months = this.constants.makeMonths(this.monthsOffsetNumber, offset);
  }

  selectMonth(object: DateSelectionObject, date: Date) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.constants.selectMonth(object, date);
    this.selections.partsSummary.month.name = this.selections.partsSummary.month.startDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
    
    this.service.getData(true);
    if (this.selections.partsSummary.chosenSite) {
      this.service.getSalesDataForSite(true);
    }
  }

  changeMonth(object: DateSelectionObject, changeAmount: number) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    object.startDate = this.constants.addMonths(object.startDate, changeAmount);
    var lastDayOfMonth = new Date(object.startDate.getFullYear(), object.startDate.getMonth() + 1, 0);
    object.endDate = lastDayOfMonth;

    this.selections.partsSummary.month.name = object.startDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });

    this.service.getData(true);
    if (this.selections.partsSummary.chosenSite) {
      this.service.getSalesDataForSite(true);
    }
  }

  selectSite(site: ServiceSalesSiteRow) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.selections.partsSummary.chosenSite = site.Label;

    if (site.IsRegion) {
      this.selections.partsSummary.sitesIds = this.service.partsSalesSitesRows.filter(x => x.RegionDescription == site.Label).map(x => x.SiteId);
    } else if (site.IsTotal) {
      this.selections.partsSummary.sitesIds = this.service.partsSalesSitesRows.filter(x => x.SiteId != 0).map(x => x.SiteId);
    } else {
      this.selections.partsSummary.sitesIds = [site.SiteId];
    }

    this.service.getSalesDataForSite();
  }

  generateChannelList() {
    //recreate local list
    this.channelsDropdownList = [];
    this.constants.environment.partsChannels.filter(x => !x.isTotal).forEach(f => {
      this.channelsDropdownList.push(
        { label: f.displayName, isSelected: this.showAllChannels }
      )
    })
    this.channelsDropdownList.forEach(f => {
      if (this.selections.partsSummary.channelNames.indexOf(f.label) > -1) {
        f.isSelected = true;
      }
    })
  }

  selectChannels() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.selections.partsSummary.channelNames = this.channelsDropdownList.filter(e => e.isSelected).map(e => e.label);
    this.service.getData(true);
  }

  selectTimeOption(timeOption: string) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.selections.partsSummary.timeOption = timeOption;
    this.service.getData(true);
  }

  channelChosenLabel() {
    if (this.selections.partsSummary.channelNames.length == 1) {
      return this.selections.partsSummary.channelNames[0];
    } else if (this.selections.partsSummary.channelNames.length == 0) {
      return 'None';
    } else {
      return 'Channels';
    }
  }

  toggleItem(toggleTotal: boolean, item?: selectableItem) {
    if (!toggleTotal) {
      this.showAllChannels = false;
      return item.isSelected = !item.isSelected;
    }

    this.showAllChannels = !this.showAllChannels;
    this.channelsDropdownList.forEach(channel => {
      channel.isSelected = this.showAllChannels;
    })
  }

  chooseTableType(type:string){

    if(type == 'Daily')
    {
      this.selections.partsSummary.showDailyView = true;
      this.service.getPartsDailySales(true)
    }
    else{
      this.selections.partsSummary.showDailyView = false;
      this.selections.initiatePartsSummary();
      this.service.getData();
    }

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading})
    this.selections.partsSummary.tableType=type;
  }


}
