
import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DealDetailsComponent } from 'src/app/components/dealDetails/dealDetails.component';
import { FileSentDate } from 'src/app/components/dealDetails/dealDetailsFileSentDates.component';

import { ProfilePicSize, SiteVM, } from '../../model/main.model';
import { Deal, LateCostOption } from '../../model/sales.model';
import { OrderBookService } from '../orderBook/orderBook.service';

import { SalesExecSummaryItem } from './whiteboard.model';
import { WhiteboardService } from './whiteboard.service';
import { ConstantsService } from 'src/app/services/constants.service';


@Component({
  selector: 'app-whiteboard',
  templateUrl: './whiteboard.component.html',
  styleUrls: ['./whiteboard.component.scss']
})


export class WhiteboardComponent implements OnInit {

  profilePicSize: ProfilePicSize = ProfilePicSize.medium;
  profilePicSizeOnMenu: ProfilePicSize = ProfilePicSize.small;
  subscription: Subscription;
  
  constructor(
    public service: WhiteboardService,
    public orderBookService:OrderBookService,

    public constants: ConstantsService

  ) {

  }

  ngOnDestroy() { 
    if(!!this.service.mySubscription){ this.service.mySubscription.unsubscribe(); }
    if(this.subscription){this.subscription.unsubscribe()}
  }

  ngOnInit() {

    this.service.initParams()
    this.service.getDeals()
    this.subscription = this.service.constants.newFileSentDateEmitter.subscribe((res:FileSentDate)=>{
      //console.log(this.service.salesExecSummaryItems)  
      this.service.salesExecSummaryItems.forEach(item=>{
        item.deals.forEach(deal=>{
          if(deal.StockNumber === res.StockNumber){deal.hasFileSentDate=res.hasDate;}
        })
      })
    })

  }

  //--------------------------------------
  //Template stuff
  //--------------------------------------

  getTileClass(deal: Deal, i: number, exec: SalesExecSummaryItem): string {

    let classToReturn: string = '';

    //check for brought in
    if(deal.isDealBroughtIn){classToReturn = 'broughtIn'}

    //check for handed over
    if (deal.IsDelivered) {
      classToReturn = classToReturn + ' ' + 'delivered'
    } else if (deal.isConfirmed && this.service.constants.environment.whiteboard.showConfirmed) {
      classToReturn = classToReturn + ' ' + 'confirmed'
    } else if (!deal.isFiller && !deal.isConfirmed && this.service.constants.environment.whiteboard.showNotConfirmed) {
      classToReturn = classToReturn + ' ' + 'notConfirmed'
    }

    if (exec.target && deal.index + 1 === exec.target) {
      classToReturn = classToReturn + ' ' + 'targetTile'
    }

    //check for done today
    if (deal.OrderDate && this.service.constants.startTimeOfDay(deal.OrderDate) >= this.service.constants.todayStart.getTime()) {   //for checking if done in last 2 hours: new Date().getTime() - deal.OrderDate.getTime() ) < (1000 * 60 * 120)
      classToReturn = classToReturn + ' ' + 'pulsing'
    }

    // some specific to rrg
    if(this.constants.environment.customer == 'RRGUK')
    {
      if(!deal.IsFinanced && deal.IsDelivered && !deal.isFiller)
      {
        classToReturn = classToReturn + ' ' + 'rrgNotFinancedDelivered'
      }

      if(!deal.IsFinanced && !deal.IsDelivered && !deal.isFiller)
      {
        classToReturn = classToReturn + ' ' + 'rrgNotFinancedNotDelivered'
      }
    }

    return classToReturn
  }


  isDealBroughtIn(deal:Deal){

    return deal.OrderDate && new Date(deal.OrderDate).getTime() < this.service.deliveryDate.startDate.getTime()
  }


  dealGroupHolderWidth(): number {
    let count: number = Math.min(this.service.maxDealGroupTiles, this.service.maxDealsCount) //limit to just 35 to prevent fleet guys blowing up grid
    let dealGroupings = Math.ceil(count) //maxDealsCount is effectively max dealGroups count i.e. how many columns of deals there are
    return 100 / dealGroupings;
  }

  makeMonths(): void {
    this.service.months = this.service.constants.makeMonthsNewV3();
    //console.log(this.service.months, 'this.service.months!')
  }

  getMonthName(date: Date): string
  {
    return date.toLocaleDateString(this.service.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
  }

  selectMonth(date: Date): void {
    this.service.deliveryDate.startDate = this.constants.deductTimezoneOffset(new Date(date));
    this.service.deliveryDate.endDate = this.constants.endOfMonth(date);
    this.service.getDeals();
  }

  openDealOnClick(deal: Deal): void {

    let modalRef;

    if(this.service.constants.environment.dealDetails.componentName == 'DealDetailsRRGComponent'){
      modalRef = this.service.modalService.open(DealDetailsComponent);
    }

    if(this.service.constants.environment.dealDetails.componentName == 'DealDetailsComponent'){
      modalRef = this.service.modalService.open(DealDetailsComponent);
    }

    //I give to modal
    modalRef.componentInstance.givenDealId = deal.Id;

    modalRef.result.then((result) => { //I get back from modal

    });

  }

  changeMonthOnClick(changeAmount: number): void {
    this.service.deliveryDate.startDate = this.constants.deductTimezoneOffset(new Date(this.service.constants.addMonths(this.service.deliveryDate.startDate, changeAmount)));
    this.service.deliveryDate.endDate = this.constants.endOfMonth(this.service.deliveryDate.startDate);
    this.service.getDeals();
  }


  onUpdateSitesOnClick(sites: SiteVM[]): void {
    this.service.selections.selectedSites = sites;
    this.service.selections.selectedSitesIds = [];

    sites.forEach(site => {
      this.service.selections.selectedSitesIds.push(site.SiteId);
    });

    this.service.getDeals();
  }

  onUpdateOrderTypesOnClick(orderTypes: string[]): void {
    this.service.orderTypeTypes = orderTypes;
    this.service.getDeals();
  }

  onUpdateVehicleTypesOnClick(vehicleTypes: string[]): void {
    this.service.vehicleTypeTypes = vehicleTypes
    this.service.getDeals();
  }

  onUpdateFranchisesOnClick(franchises: string[]): void {
    this.service.franchises = franchises
    this.service.getDeals();
  }

  selectLateCostOptionOnClick(lateCostOption: LateCostOption): void {
    this.service.lateCostOption = lateCostOption;
    this.service.getDeals();
  }

  showPersonDealsOnClick(salesExecSummary: any): void {
    this.service.orderBookService.initOrderbook();
    this.service.orderBookService.salesExecId = salesExecSummary.id;
    this.service.orderBookService.salesExecName = salesExecSummary.salesmanName;
    this.service.orderBookService.orderTypeTypes =  this.service.orderTypeTypes;
    this.service.orderBookService.vehicleTypeTypes =  this.service.vehicleTypeTypes;
    this.service.orderBookService.franchises =  this.service.franchises;
    this.service.orderBookService.accountingDate.startDate = this.service.constants.addTimezoneOffset(this.service.deliveryDate.startDate);
    this.service.orderBookService.accountingDate.endDate = this.service.constants.addTimezoneOffset(this.service.deliveryDate.endDate);
    this.service.orderBookService.lateCostOption = this.service.lateCostOption;
    this.service.orderBookService.showOrderbook();
  }

  shouldShowDeliverySummary(): boolean {

    if (
      this.service.summaryTable &&
      this.service.deliveryDate.startDate.getMonth() == this.service.constants.appStartTime.getMonth() &&
      this.service.deliveryDate.startDate.getFullYear() == this.service.constants.appStartTime.getFullYear()
    ) {
      return true;
    }

    return false;
  }

  showPersonDeals(salesExecSummary: any): void {

    this.orderBookService.initOrderbook();

    this.orderBookService.salesExecId = salesExecSummary.id;
    this.orderBookService.salesExecName = salesExecSummary.salesmanName;
    this.orderBookService.orderTypeTypes =  this.service.orderTypeTypes;
    this.orderBookService.vehicleTypeTypes =  this.service.vehicleTypeTypes;
    this.orderBookService.franchises =  this.service.franchises;

    this.orderBookService.accountingDate.startDate = this.service.constants.addTimezoneOffset(this.service.deliveryDate.startDate);
    this.orderBookService.accountingDate.endDate = this.service.constants.addTimezoneOffset(this.service.deliveryDate.endDate);

    this.orderBookService.lateCostOption = this.service.lateCostOption;

    this.orderBookService.showOrderbook();
  }

  
  generatePeopleSummary(): void {

    let groupId = ''
    let groupName = ''
    let summaryName = ''
    
    groupId = 'ManagerId', 
    groupName = 'ManagerName', 
    summaryName = 'salesManagerSummary'

    let groupedByExec: SalesExecSummaryItem[][] = this.groupBy(this.service.salesExecSummaryItems, groupId);

    this.service[summaryName] = []
    groupedByExec.forEach(item => {
      let unitCount = this.constants.sum(item.map(x => x.dealsCount));
      //let productsCount = this.constants.sum(item.map(x => x.TotalProductCount));
      let profit = this.constants.sum(item.map(x => x.profit));
      this.service[summaryName].push({
        name: item[0][groupName],
        salesmanId: item[0][groupId],
        dealCount: item.length,
        //products: productsCount,
        units: unitCount,
        profit: profit,
        //productsPU: this.constants.div(productsCount, unitCount),
        profitPU: this.constants.div(profit, unitCount)
      })
    })

    //this.service.salesExecSummaryItems.sort((a, b) => a.managerName.localeCompare(b.managerName));


  }

  
  groupBy(arr, prop) {
    const map = new Map(Array.from(arr, obj => [obj[prop], []]));
    arr.forEach(obj => map.get(obj[prop]).push(obj));
    return Array.from(map.values());
  }

  selectManagerExec(salesExecName: string, salesExecId: number) {
    this.service.managerName = salesExecName;
    this.service.managerId = salesExecId;
    this.service.getDeals()
  }

}
