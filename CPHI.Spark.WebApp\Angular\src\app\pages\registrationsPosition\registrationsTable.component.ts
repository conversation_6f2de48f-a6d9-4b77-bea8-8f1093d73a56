//core angular
import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { GridOptions, RowClickedEvent } from 'ag-grid-community';
import { SiteRegistrationPosition } from 'src/app/model/main.model';
import { OemOrdersSiteRow, RegistrationsSiteRow } from 'src/app/model/sales.model';
import { localeEs } from 'src/environments/locale.es.js';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
import { AutotraderService } from '../../services/autotrader.service';
//model and cell renderers
//services
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
//Angular things, non-standard
import { DateComponent } from './../../_cellRenderers/date.component';
import { RegistrationsService } from './registrations.service';
import { RegReportType } from './registrationsPosition.component';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

type thisTableRowData = RegistrationsSiteRow | OemOrdersSiteRow;


@Component({
  selector: 'registrationsTable',
  template: `
    <div id="gridHolder">
      <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular 
        id="RegistrationsTable"
        class="ag-theme-balham" 
        [suppressRowHoverHighlight]="true"
        [gridOptions]="mainTableGridOptions"
        
        >
        
      </ag-grid-angular>
    </div>
    `
  ,
  styles: [
    `
   
  `
  ],
  styleUrls: ['./../../../styles/components/_agGrid.scss']
})



export class RegistrationsTableComponent implements OnInit {

  @Input() public tableType: string;
  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi;

  mainTableGridOptions: GridOptions;

  gridApiColumnDefinitions: any;

  flipCols: Array<string>;
  currentRowHeight: number;
  frameworkComponents: { agColumnHeader: any; };



  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public gridHelpersService: AGGridMethodsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public service: RegistrationsService,

  ) {

    
  }


  ngOnInit() {
    this.initParams();
    if(this.tableType==='sites'){
      this.service.sitesTableRef = this;
    }else{
      this.service.regionsTableRef = this;
    }

  }
  ngOnDestroy() { 
    this.service.sitesTableRef = null;
    this.service.regionsTableRef = null;
  }


  initParams() {
    this.mainTableGridOptions = this.provideMainTableGridOptions();
  }

  provideMainTableGridOptions(){
    let rowSourceData = this.service.chosenReportType.type==RegReportType.oemOrders ? this.service.siteOemOrdersRows : this.service.siteRegsRows;
    let options: GridOptions = {
      getMainMenuItems:(params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlights),
      rowData:this.filterRows(rowSourceData),
      frameworkComponents: this.frameworkComponents,
      domLayout: 'autoHeight',
      onGridReady: (parms) => this.onGridReady(parms),
      // onRowClicked:(parms)=>this.onRowClicked(parms),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      suppressPropertyNamesCheck: true,
      context: { thisComponent: this },
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      pinnedBottomRowData: (rowSourceData as thisTableRowData[]).filter(x => !!x.IsTotal),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.tableType==='sites' ? this.service.topBottomHighlights  : []),
      },
      columnDefs: this.provideColDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }
    return options;
  }

  filterRows(rowData: thisTableRowData[]): thisTableRowData[] {
    
    if(this.tableType==='sites'){
      return rowData.filter(x=>!!x.IsSite )
    }else{
      return rowData.filter(x=>!x.IsSite && !x.IsTotal )
    }
  }


  dealWithNewData(rowData:thisTableRowData[]){
    //set columns
    this.gridApi.setColumnDefs([]);
    this.gridApi.setColumnDefs(this.provideColDefs())
    this.gridApi.setRowData(this.filterRows(rowData));
    this.gridApi.setPinnedBottomRowData(rowData.filter(x=>x.IsTotal));
    this.resizeGrid();
  }


  provideColDefs(){
    if(this.service.chosenReportType.type===RegReportType.oemOrders){
      return this.provideColDefsOrders()
    }else{
      return this.provideColDefsRegs();
    }
  }


  provideColDefsRegs(){
    return [
      { headerName: '', field: 'Label', colId: 'label', width: 320, type: 'label', },
      { headerName: this.constants.translatedText.Target, field: 'Target', colId: 'Target', width: 97, type: 'number', },
      { headerName: 'Registered', field: 'Registered', colId: 'Registered', width: 97, type: 'number', },
      { headerName: 'Sold Portfolio', field: 'SoldPortfolio', colId: 'SoldPortfolio', width: 97, type: 'number', },
      { headerName: 'Achievement', field: 'Achievement', colId: 'Achievement', width: 97, type: 'number', },
      { headerName: 'Achievement %', field: 'AchievementPercent', colId: 'AchievementPercent', width: 97, type: 'percent', },
      { headerName: 'Gap to Target', field: 'GapToTarget', colId: 'GapToTarget', width: 97, type: 'numberWithColour', },
      { headerName: 'Daily Run Rate', field: 'DailyRunRate', colId: 'DailyRunRate', width: 97, type: 'number', },
      {
        headerName: 'Projection', children: [
          { headerName: 'Landing', field: 'ProjectedLanding', colId: 'ProjectedLanding', width: 97, type: 'number', },
          { headerName: 'GapToTarget', field: 'ProjectedGapToTarget', colId: 'ProjectedGapToTarget', width: 97, type: 'numberWithColour', },
          { headerName: 'Achievement %', field: 'ProjectedAchievementPercent', colId: 'ProjectedAchievementPercent', width: 97, type: 'percent', },
        ]
      }

    ]
  }

  provideColDefsOrders(){
    return [
      { headerName: '', field: 'Label', colId: 'label', width: 320, type: 'label', },
      { headerName: this.constants.translatedText.Target, field: 'Target', colId: 'Target', width: 97, type: 'number', },
      { headerName: 'Gross Orders', field: 'GrossOrders', colId: 'GrossOrders', width: 97, type: 'number', },
      { headerName: 'Cancellations', field: 'Cancellations', colId: 'Cancellations', width: 97, type: 'number', },
      { headerName: 'Net Orders', field: 'NetOrders', colId: 'NetOrders', width: 97, type: 'number', },
      { headerName: 'Achievement %', field: 'AchievementPercent', colId: 'AchievementPercent', width: 97, type: 'percent', },
      { headerName: 'Gap', field: 'GapToTarget', colId: 'GapToTarget', width: 97, type: 'numberWithColour', },
      { headerName: 'DailyRunRate', field: 'DailyRunRate', colId: 'DailyRunRate', width: 97, type: 'number', },
      
      {
        headerName: 'Projection', children: [
          { headerName: 'Landing', field: 'ProjectedLanding', colId: 'ProjectedLanding', width: 97, type: 'number', },
          { headerName: 'GapToTarget', field: 'ProjectedGapToTarget', colId: 'ProjectedGapToTarget', width: 97, type: 'numberWithColour', },
          { headerName: 'Achievement %', field: 'ProjectedAchievementPercent', colId: 'ProjectedAchievementPercent', width: 97, type: 'percent', },
        ]
      },
      
      { headerName: 'Cancellations %', field: 'CancellationsPercent', colId: 'CancellationsPercent', width: 97, type: 'percent', },

      

    ]
  }



  onRowClicked(parms: RowClickedEvent){
    this.service.loadDetailsForRow(parms.data)
  }

  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }

  }

  onGridReady(params) {
    //this.gridHelpers.topBottomHighlights = [];
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
  }


  refreshCells() {
    if (this.gridApi) {
      this.gridApi.refreshCells();
    }
  }


  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Regs - ' + this.service.chosenReportType.label, 1, 1);
  }


}
