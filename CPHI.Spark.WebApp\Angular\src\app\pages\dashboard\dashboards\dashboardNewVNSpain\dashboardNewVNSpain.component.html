<nav class="navbar">
    <nav class="generic">

        <!-- Page title -->
        <h4 id="pageTitle">
            <div>
                {{constants.translatedText.New}}
                <span *ngIf="service.chosenPage==='orders'">{{constants.translatedText.Orders}}</span>
                <span *ngIf="service.chosenPage==='position'">{{constants.translatedText.Position}}</span>
                <span *ngIf="service.chosenPage == 'invoiceDeals'">{{ constants.translatedText.Dashboard_InvoiceDeals }}</span>
            </div>
        </h4>


         <!-- Choose time period if on orders -->
         <ng-container *ngIf="service.chosenPage==='orders'">
            <!-- SnapshotChoice -->
            <div *ngIf="!!ordersService.months" ngbDropdown class="d-inline-block language-select">
                <button class="btn btn-primary" id="dropdownBasic1" ngbDropdownToggle>
                    {{ordersService.chosenMonth|cph:'month':0}}
                </button>
                <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                    <button *ngFor="let choice of ordersService.months" ngbDropdownItem
                        (click)="ordersService.selectSnapshot(choice)">
                        {{choice|cph:'month':0}} 
                    </button>
                </div>
            </div>
        </ng-container>



        <!-- Choose time period if not on orders -->
        <ng-container *ngIf="service.chosenPage!=='orders' ">
            <!-- SnapshotChoice -->
            <div *ngIf="!!stocksService.months" ngbDropdown class="d-inline-block language-select">
                <button class="btn btn-primary" id="dropdownBasic1" ngbDropdownToggle>
                    {{stocksService.chosenMonth|cph:'month':0}}
                </button>
                <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                    <button *ngFor="let month of stocksService.months" ngbDropdownItem
                        (click)="stocksService.chooseMonth(month)">
                        <span>{{month|cph:'month':0}}</span>
                    </button>
                </div>
            </div>

        </ng-container>



        <!-- Choose to show orders, position or invoiced -->
        <div class="buttonGroup">
            <!-- Orders -->
            <button class="btn btn-primary" (click)="service.chosenPage='orders'"
                [ngClass]="{'active':service.chosenPage==='orders'}">
                <span >{{constants.translatedText.Order}}</span>
                
            </button>
            <!-- Position -->
            <button class="btn btn-primary" (click)="service.chosenPage='position'"
                [ngClass]="{'active':service.chosenPage==='position'}">
                <span >{{constants.translatedText.Position}}</span>
              
            </button>
            <!-- Invoiced deals -->
            <button
                class="btn btn-primary"
                [ngClass]="{ 'active': service.chosenPage == 'invoiceDeals' }"
                (click)="service.chosenPage = 'invoiceDeals'"
            >
                <span>{{ constants.translatedText.Dashboard_InvoiceDeals }}</span>
            </button>
        </div>


        <!-- Sub menu if you have chosen position -->
        <div class="buttonGroup" *ngIf="service.chosenPage=='position'" >
            <!-- Stock -->
            <button class="btn btn-primary" (click)="stocksService.toggleShowStocks()"
                [ngClass]="{'active':stocksService.isStocksSelected && service.chosenPage==='position'}">
                <span >{{constants.translatedText.Stock}}</span>
                <span *ngIf="stocksService.isStocksSelected; else unchecked;">&#9745;</span>
            </button>
            <!-- Book -->
            <button class="btn btn-primary" (click)="stocksService.toggleShowBook()"
                [ngClass]="{'active':stocksService.isBookSelected && service.chosenPage==='position'}">
                <span >{{constants.translatedText.Book}}</span>
                <span *ngIf="stocksService.isBookSelected && service.chosenPage==='position'; else unchecked;">&#9745;</span>
            </button>
        </div>
        <ng-template #unchecked> <span>&#9746;</span> </ng-template>
        

       

        <button class="btn btn-primary" (click)="launchFilterChoicesModal()">
            {{ constants.translatedText.Dashboard_FilterChoices }}
        </button>

    </nav>
</nav>

<!-- Main Page -->
<dashboardNewVNSpainOrdersPage *ngIf="service.chosenPage==='orders'"></dashboardNewVNSpainOrdersPage>
<dashboardNewVNSpainStocksPage *ngIf="service.chosenPage==='position'"></dashboardNewVNSpainStocksPage>
<dashboardInvoicedDealsSpain *ngIf="service.chosenPage == 'invoiceDeals'" [department]="'New'"></dashboardInvoicedDealsSpain>

<ng-template #filterChoicesModal let-modal>
    <div class="modal-header">
      <h4 class="modal-title">{{ constants.translatedText.Dashboard_FilterChoices }}</h4>
      <button class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
        <div *ngIf="service.chosenPage == 'orders'; else showStockFilterChoices;" class="buttonGroup">
            <ng-container *ngFor="let choice of ordersService.filterChoices">
                <vnStringPicker [pageParams]="ordersService.getPageParams()" [fieldName]="choice.FieldName" [fieldNameTranslation]="choice.FieldNameTranslation">
                </vnStringPicker>
            </ng-container>
        </div>
        <ng-template #showStockFilterChoices>
            <!-- The various dropdown buttons -->
            <div class="buttonGroup">
                <ng-container *ngFor="let choice of stocksService.filterChoices">
                    <vnStringPicker  [pageParams]="stocksService.getPageParams()" [fieldName]="choice.FieldName" [fieldNameTranslation]="choice.FieldNameTranslation">
                    </vnStringPicker>
                </ng-container>
            </div>
        </ng-template>
    </div>
    <div class="modal-footer">
      <button class="btn btn-primary" (click)="modal.close('Save click')">Close</button>
    </div>
</ng-template>
