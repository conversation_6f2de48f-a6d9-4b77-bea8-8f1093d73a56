import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CellClickedEvent, ColDef, GridApi } from 'ag-grid-community';
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from "../../cph.pipe";
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from "../../services/constants.service";
import { ExcelExportService } from '../../services/excelExportService';
import { GetDataMethodsService } from '../../services/getDataMethods.service';
import { SelectionsService } from "../../services/selections.service";
import { CustomHeaderComponent } from "../../_cellRenderers/customHeader.component";
import { PartsStockBarComponent } from '../../_cellRenderers/partsStockBar.component';
import { PartsStockService } from "./partsStock.service";
import { PartsStockAgeBySite } from './PartsStockAgeBySite';
import { PartsStockModalComponent } from './partsStockModal.component';
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { PartsStockAgeBySiteDetailed } from "src/app/model/PartsStockAgeBySiteDetailed";




@Component({
  selector: "partsStockSitesAgeingTable",

  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        id="PartsStockTable"
        class="ag-theme-balham"
        [gridOptions]="mainTableGridOptions"
        domLayout="autoHeight"
        
      >
      </ag-grid-angular>
    </div>

    <partsStockModal #partsStockModal></partsStockModal>
  `,

  styleUrls: ["./../../../styles/components/_agGrid.scss"],
  styles: [
    `

    `,
  ],
})
export class PartsStockTableAgeingComponent implements OnInit {
  @Input() public isRegionalTable: boolean;
  @ViewChild('partsStockModal', { static: true }) partsStockModal: PartsStockModalComponent;
  //@Output() rowClicked = new EventEmitter();

  columnDefs: any[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;
  showGrid: boolean;


  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public router: Router,
    public excel: ExcelExportService,
    public getDataMethods: GetDataMethodsService,
    public service: PartsStockService,
    public gridHelpersService: AGGridMethodsService

  ) { }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.mainTableGridOptions.context = { thisComponent: this };
    this.gridApi.sizeColumnsToFit();

    if (this.gridApi) {
      this.gridApi.refreshCells();
    }

  }

  ngOnInit() {
    this.initParams();
    this.showGrid = false;

    this.service.filterUpdated.subscribe(value => {
      this.setRowData();
    });
  }

  initParams() {


    // table definitions
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false, cellClass: "ag-right-aligned-cell",
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
      },

      rowData: this.provideRowData(),

      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
      onGridReady: (params) => this.onGridReady(params),
      pinnedBottomRowData: this.service.partsStockAgeSitesRows.filter(x => x.IsTotal),
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsSitesAgeing),
      },
      columnDefs: this.makeColDefs()
    }
  }
  provideRowData() {
    return this.isRegionalTable ? this.service.partsStockAgeSitesRows.filter(x => x.IsRegion) : this.service.partsStockAgeSitesRows.filter(x => x.IsSite)
  }





  makeColDefs() {

    let partStockBarCharts;

    if (!this.constants.environment.partsStockDetailedTable.partStockBarCharts2) {
      partStockBarCharts = [{
        headerName: this.constants.environment.partsStockDetailedTable.partStockBarCharts1.headerName,
        children: [{
          headerName: '',
          cellRenderer: PartsStockBarComponent,
          cellRendererParams: 170,
          field: this.constants.environment.partsStockDetailedTable.partStockBarCharts1.field,
          colId: this.constants.environment.partsStockDetailedTable.partStockBarCharts1.colId,
          width: 100,
          type: "label",
        }]
      }]
    } else {
      partStockBarCharts = [
        {
          headerName: this.constants.environment.partsStockDetailedTable.partStockBarCharts1.headerName,
          children: [{
            headerName: this.constants.environment.partsStockDetailedTable.partStockBarCharts1.headerName,
            cellRenderer: PartsStockBarComponent,
            cellRendererParams: 170,
            field: this.constants.environment.partsStockDetailedTable.partStockBarCharts1.field,
            colId: this.constants.environment.partsStockDetailedTable.partStockBarCharts1.colId,
            width: 100,
            type: "label"
          }]
        },
        {
          headerName: this.constants.environment.partsStockDetailedTable.partStockBarCharts2.headerName,
          children: [{
            headerName: this.constants.environment.partsStockDetailedTable.partStockBarCharts2.headerName,
            cellRenderer: PartsStockBarComponent,
            cellRendererParams: 170,
            field: this.constants.environment.partsStockDetailedTable.partStockBarCharts2.field,
            colId: this.constants.environment.partsStockDetailedTable.partStockBarCharts2.colId,
            width: 100,
            type: "label"
          }]
        }]
    }



    let partStockAgeingColumns: ColDef[];

    if (this.constants.environment.partsStockDetailedTable.showPartStockAgeingColumnsForRRG) {
      partStockAgeingColumns = [
        { headerName: "<30 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Under30Days", colId: "<30", width: 50, type: "currency", },
        { headerName: "<60 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Over30Under60", colId: "<60", width: 50, type: "currency", },
        { headerName: "<90 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Over60Under90", colId: "<90", width: 50, type: "currency", },
        { headerName: "<120 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "PartsStockRRG.Under120", colId: "<120", width: 50, type: "currency", },
        { headerName: "<150 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "PartsStockRRG.Under150", colId: "<150", width: 50, type: "currency", },
        { headerName: "<180 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "PartsStockRRG.Under180", colId: "<180", width: 50, type: "currency", },
        { headerName: "<1 " + this.constants.translatedText.Common_Year, onCellClicked: (params) => this.openStockModal(params), field: "PartsStockRRG.Under1Yr", colId: "<1yr", width: 50, type: "currency", },
        { headerName: ">1 " + this.constants.translatedText.Common_Year, onCellClicked: (params) => this.openStockModal(params), field: "PartsStockRRG.Over1Yr", colId: "1yr+", width: 50, type: "currency", },
      ]
    } else if (this.constants.environment.partsStockDetailedTable.showPartStockAgeingColumnsForVindis) {
      partStockAgeingColumns = [
        { headerName: "<90 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Under90", colId: "<90", width: 50, type: "currency", },
        { headerName: "<120 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Under120", colId: "<120", width: 50, type: "currency", },
        { headerName: "<180 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Under180", colId: "<180", width: 50, type: "currency", },
        { headerName: "<270 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Under270", colId: "<270", width: 50, type: "currency", },
        { headerName: "<1 " + this.constants.translatedText.Common_Year, onCellClicked: (params) => this.openStockModal(params), field: "Under1yr", colId: "<1yr", width: 50, type: "currency", },
        { headerName: "<18 months", onCellClicked: (params) => this.openStockModal(params), field: "Under18M", colId: "<18m", width: 50, type: "currency", },
        { headerName: "<2 " + this.constants.translatedText.Common_Years, onCellClicked: (params) => this.openStockModal(params), field: "Under2yr", colId: "<2yr", width: 50, type: "currency", },
        { headerName: ">2 " + this.constants.translatedText.Common_Years, onCellClicked: (params) => this.openStockModal(params), field: "Over2yr", colId: "2yr+", width: 50, type: "currency", },
      ]
    }



    let colDefs: any[] = [
      { headerName: "", field: "Label", colId: "Label", width: 100, type: "label", },
    ];

    if (this.constants.environment.partsStockDetailedTable.showPartStockAgeingColumnsForRRG) {
      colDefs.push(
        {
          headerName: this.constants.translatedText.PartsStockAgeing_Title, children:
            [
              { headerName: "<30 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Under30Days", colId: "<30", width: 50, type: "currency", },
              { headerName: "<60 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Over30Under60", colId: "<60", width: 50, type: "currency", },
              { headerName: "<90 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Over60Under90", colId: "<90", width: 50, type: "currency", },
              { headerName: "<120 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Over90Under120", colId: "<120", width: 50, type: "currency", },
              { headerName: "<150 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Over120Under150", colId: "<150", width: 50, type: "currency", },
              { headerName: "<180 " + this.constants.translatedText.Common_DaysLower, onCellClicked: (params) => this.openStockModal(params), field: "Over150Under180", colId: "<180", width: 50, type: "currency", },
              { headerName: "<1 " + this.constants.translatedText.Common_Year, onCellClicked: (params) => this.openStockModal(params), field: "Over180Under1yr", colId: "<1yr", width: 50, type: "currency", },
              { headerName: ">1 " + this.constants.translatedText.Common_Year, onCellClicked: (params) => this.openStockModal(params), field: "Over1yr", colId: "1yr+", width: 50, type: "currency", },
            ],
        }
      )

      colDefs.push({ headerName: "Total", onCellClicked: (params) => this.openStockModal(params), field: "Total", colId: "all", width: 70, type: "currency", })
      colDefs.push({ headerName: "% >1 " + this.constants.translatedText.Common_Year, children: [{ headerName: '', cellRenderer: PartsStockBarComponent, cellRendererParams: 170, field: "PercentOver1Yr", colId: "PartsStockRRG.PercentOver1yr", width: 100, type: "percent", }] })

    } else if (this.constants.environment.partsStockDetailedTable.showPartStockAgeingColumnsForVindis) {
      colDefs.push({ headerName: this.constants.translatedText.PartsStockAgeing_Title, children: partStockAgeingColumns });
      colDefs.push({ headerName: "Total", onCellClicked: (params) => this.openStockModal(params), field: "Total", colId: "all", width: 90, type: "currency", })
      colDefs.push(partStockBarCharts[0])
      colDefs.push(partStockBarCharts[1] ? partStockBarCharts[1] : null)

    }

    if (this.constants.environment.partsStock.includeOfWhichColumns) {

      colDefs.push({
        headerName: "Of which", hide: this.constants.environment.partsStockDetailedTable.hideOfWhichColumn, children: [
          { headerName: "Dead value", field: "DeadValue", colId: "PartsStockRRG.Dead", width: 50, type: "currency", hide: this.constants.environment.partsStockDetailedTable.hideDeadValueColumn },
          { headerName: "Dormant value", field: "DormantValue", colId: "PartsStockRRG.Dormant", width: 50, type: "currency", hide: this.constants.environment.partsStockDetailedTable.hideDormantValueColumn },
          { headerName: "Dead prov", field: "DeadProvision", colId: "DeadPartStockProvn", width: 50, type: "currency", valueGetter: (params) => this.invertValue(params), hide: this.constants.environment.partsStockDetailedTable.hideDeadValueColumn },
          { headerName: "Dormant prov", field: "DormantProvision", colId: "DormantPartStockProvn", width: 50, type: "currency", valueGetter: (params) => this.invertValue(params), hide: this.constants.environment.partsStockDetailedTable.hideDormantProvColumn },
        ]
      })

    }

    return colDefs;
  }


  invertValue(params) {
    if (!params.data) { return '-' }
    return Number(params.data[params.colDef.field]) * -1
  }

  openStockModal(params: CellClickedEvent) {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading })

    let siteIds: number[] = [];
    let row: PartsStockAgeBySite = params.data;

    if (row.IsTotal) {
      siteIds = this.service.partsStockAgeSitesRows.filter(x => x.IsSite).map(x => x.SiteId)
    } else if (row.IsRegion) {
      siteIds = this.service.partsStockAgeSitesRows.filter(x => x.IsSite && x.RegionDescription == row.RegionDescription).map(x => x.SiteId)
    } else {
      siteIds = [row.SiteId]
    }

    let ageBand = params.colDef.headerName;

    this.getDataMethods.loadPartsStockDetailed(ageBand, siteIds, this.selections.partsStock.chosenFamilyCodes,
      this.selections.partsStock.chosenGroups)
      .subscribe((res:PartsStockAgeBySiteDetailed[]) => {
        res.map(x=>{
          if(x.HandoverDate){x.HandoverDate = new Date(x.HandoverDate) }
          if(x.Created){x.Created = new Date(x.Created) }
          if(x.LastPurchased){x.LastPurchased = new Date(x.LastPurchased) }
          if(x.LastSold){x.LastSold = new Date(x.LastSold) }
        })

        this.partsStockModal.detailedStock = res;
        this.partsStockModal.title = `Detailed Parts Stock ${row.Label} ${ageBand}`
        this.partsStockModal.openModal()

      }, error => {
        console.error('failed getting parts detailed stock')
        this.selections.triggerSpinner.next({ show: false, message: this.constants.translatedText.Common_Loading })
      })

  }

  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Parts Stock Ageing', 1, 1);
  }

  setRowData(): void {

    if (this.gridApi) {
      this.gridApi.setRowData(this.provideRowData())
      this.gridApi.setPinnedBottomRowData(this.service.partsStockAgeSitesRows.filter(x => x.IsTotal));
    }

  }

}

