
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { CphPipe } from 'src/app/cph.pipe';
import { OrderbookParams } from 'src/app/model/OrderbookParams';
import { CommentTextAndDeal } from 'src/app/model/sales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { OrderbookRow } from "../../model/OrderbookRow";
import { OrderBookService } from './orderBook.service';







@Injectable({
  providedIn: 'root'
})
export class OrderBookGetDataService {

  


  constructor(
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public router: Router,
    public getDataMethods: GetDataMethodsService,
    public service: OrderBookService,
    
  ) {
  }


  getData() {

    this.service.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading })

    this.getOrderbookRowsDirectFromServer().subscribe((res: OrderbookRow[]) => {
      //now have got the data, get any comments

      this.getDataMethods.getComments(res.map(x => x.StockNumber), res.map(x => x.DealId)).subscribe((comments: CommentTextAndDeal[]) => {
        comments.map(x => x.Date = new Date(x.Date))
        comments.forEach(comment => {
          let row = res.find(x => x.StockNumber === comment.StockNumber);
          if (!!row) { row.comments.push(comment) }
        })

        this.service.rows = res;
        this.service.rowsFiltered = this.service.filterForUserChoices(res);
        if (!!this.service.tableLayoutManagementParams.gridApi) {
          this.service.dealWithNewRowData(this.service.rowsFiltered)
        } else {
          this.service.orderBookComponent.initiateGrid();
        }

        this.service.selections.triggerSpinner.emit()

      })



    })
  }








  getOrderbookRowsDirectFromServer(): Observable<OrderbookRow[]> {

    let myObservable: Observable<OrderbookRow[]> = new Observable(observer => {

      let parms: OrderbookParams = {
        DeliveryDateStart: this.service.accountingDate.startDate,
        DeliveryDateEnd: this.service.accountingDate.endDate,
        DeliveryDateType: this.service.accountingDate.dateType,
        OrderDateStart: this.service.orderDate.startDate,
        OrderDateEnd: this.service.orderDate.endDate,
        SiteIds: this.service.selections.selectedSites.map(x => x.SiteId),
        VehicleTypeTypes: this.service.vehicleTypeTypes,
        OrderTypeTypes: this.service.orderTypeTypes,
        Franchises: this.service.franchises,
        IncludeLateCosts: this.service.lateCostOption.includeLateCosts,
        IncludeNormalCosts: this.service.lateCostOption.includeNormalCosts,
        IncludeInvoiced: this.service.orderOption.includeInvoiced,
        IncludeOrders: this.service.orderOption.includeOrders,
        SalesmanId: this.service.salesExecId,
        ManagerId: this.service.salesManagerId
      }

      this.getDataMethods.getOrderbookRows(parms).subscribe((dealsIn: string[]) => {

        let deals: OrderbookRow[] = [];
        dealsIn.forEach(deal=>{
          deals.push(new OrderbookRow(deal))
        })

        observer.next(deals)
        observer.complete();
      })

    })

    return myObservable;
  }



}




