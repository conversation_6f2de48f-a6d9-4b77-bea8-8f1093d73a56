import { SparkEnvironment } from "src/app/services/environment.service";
import packagejson from '../../package.json';

export const environmentSpain:SparkEnvironment = {
  customer: 'RRGSpain',
  production: true,
  version: packagejson.version,
  //webURL: 'https://sparkrrges.cphi.co.uk',
  //backEndBaseURL: 'https://sparkrrgesapi.cphi.co.uk',
  languagePickerOnLogin: true,
  franchisePicker: true,
  stockGroupPicker: true,
  lateCostPicker: false,
  orderTypePicker: true,
  ageingOptions: false,
  displayCurrency: 'EUR',
  displayCurrencySymbol: '€',
  fAndISummary_includeTargets: false,
  // Better way for this?
  bookingsBar:{
    barStyle1: true,
    barStyle2: false,
  },

  dealDetails:{
    componentName: 'DealDetailsRRGComponent',
    profitTableShowSale: true,
    profitTableShowCost: true,
    profitTableShowCommission: true,
    profitTableFinanceCommText: 'Finance Comm.',
    showDescription: true,
    showVehicleAge: true,
    showPaintProtection: true,
    paintProtectionText: 'Paint Protect',
    showPaintProtectionCost: true,
    showPaintProtectionSale: true,
    showPhysicalLocation: false,
    showIsDealClosed: true,
    showFinanceCo: true,
    showFinanceType: false,
    showWarrantyInfo: false,
    showRCIFinanceComm: true,
    showPCPFinanceComm: false,
    showStandardsCommission: true,
    showProPlusCommission: true,
    showSelectCommission: true,
    showGapInsurance: true,
    showTyreInsurance: true,
    showAlloyInsurance: true,
    showWheelGard: true,
    showServicePlan: true,
    showWarranty: true,
    showUnits: true,
    showDeliverySite: false,
    showAdditionalAddOnProfit: true,
    showCosmeticInsuranceSale: true,
    showCosmeticInsuranceCost: true,
    showCosmeticInsuranceCommission: true,
    showVATCost: true,
  },
  dealPopover:{
    showMetalProfit: false,
    showOtherProfit: false,
    showFinanceProfit: false,
    showAddons: false,
    showAddonProfit: false
  },
  usedStockTable: 
  { 
    vindisFormatting: false,
    tactical: true,
    exManagementCount: true,
    exDemo: true 
  },
  sideMenu:
  { 
    oldStockPricing:false,
    pricingHome:false,
    dashboard: true,
    orderbook: true,
    fleetOrderbook: false,
    dealsDoneThisWeek: true,
    dealsForTheMonth: true,
    whiteboard: true,
    performanceLeague: true,
    performanceTrends: false,
    scratchCards: false,
    salesIncentive: false,
    supercup:false,
    supercup2:false,
    handoverDiary: true,
    distrinet:true,
    reportPortal: false,
    stockList:  true, 
    leavingVehicles:false,
    stockInsight: false,
    stockPricing: false,
    pricingDashboard:false,
    siteDetailDashboard: false,
    applyStrategy: false,
    strategyBuilder:false,
    locationOptimiser:false,
    vehicleValuation:false,
    salesCommission: false,
    localBargains:false,
    salesExecReview: false,
    stockLanding: false,
    liveForecast: false,
    userMaintenance: true,
    autoPriceSiteSettings: false,

    summaryDashboard:false,
    stockReports:false,
    bulkValuation:false,
    optOuts:false,
    todayPriceChanges:false,
    leavingVehicleDetail:false,
leavingVehicleTrends:false,
  },
  fullSideMenu: 
  { 
    description: ' RRG Espana ' 
  },
  citNoww: 
  { 
    tileHeader: 'CitNOWs como proporción de WIP calificados',
    moveNissanValuesToRenault: true, 
    renaultRegions: true, 
    vindisRegions: false, 
    excludeAudi: false,
    pcOfSalesEnquiries: true,
    pcOfInvoicedExtWips: true,
    pcOfSalesEnquiriesText: 'CitNOW Resumen: videos como% de consultas de ventas - ',
    pcOfInvoicedExtWipsText: 'CitNOW Resumen: vídeos como porcentaje de WIP externos facturados - ',
    showSimpleCitNowPersonDetail: false,
    showVideosViewed: false,
    eDynamixView: false
  },
  dashboard: 
  { 
    sections:[

    
      { sectionName: "DashboardOverviewSpain", translatedTextField: "Dashboard_KPIsTitle", translatedTextValue: "",  pageName: "dashboardOverviewSpain", enableSitesSelector: true, pages: [
        {pageName:"SalesPerformance", translatedTextField: "Dashboard_SalesPerformance_Title",translatedTextValue:"",  },
        {pageName:"Alcopa", translatedTextField: "Dashboard_Alcopa_Title",translatedTextValue:"",  },
        {pageName:"OrderRate", translatedTextField: "Common_OrderRate",translatedTextValue:"",  },
        {pageName:"Debtors", translatedTextField: "Dashboard_Debtors_Title",translatedTextValue:"",  },        
      ] },

      
      { sectionName: "DashboardNewSpain", translatedTextField: "Dashboard_NewKPIsTitle", translatedTextValue: "",  pageName: "dashboardNewVNSpain", enableSitesSelector: true, pages: [
        {pageName:"SalesPerformance", translatedTextField: "Dashboard_SalesPerformance_Title",translatedTextValue:"",},
        {pageName:"Alcopa", translatedTextField: "Dashboard_Alcopa_Title",translatedTextValue:"",},
        {pageName:"OrderRate", translatedTextField: "Common_OrderRate",translatedTextValue:"",},
        {pageName:"Debtors", translatedTextField: "Dashboard_Debtors_Title",translatedTextValue:""}
      ] },
      
      { sectionName: "DashboardUsedSpain", translatedTextField: "Dashboard_UsedKPIsTitle", translatedTextValue: "",  pageName: "dashboardUsedSpain", enableSitesSelector: true, pages: [
        {pageName:"SalesPerformance", translatedTextField: "Dashboard_SalesPerformance_Title",translatedTextValue:"",},
        {pageName:"Alcopa", translatedTextField: "Dashboard_Alcopa_Title",translatedTextValue:"",},
        {pageName:"OrderRate", translatedTextField: "Common_OrderRate",translatedTextValue:"",},
        {pageName:"Debtors", translatedTextField: "Dashboard_Debtors_Title",translatedTextValue:""}
      ] },

      {sectionName:"DashboardAftersalesSpain",translatedTextField:"Common_Aftersales", translatedTextValue:"",  pageName: "dashboardAftersales",enableSitesSelector:true, pages:[
        
        {pageName:"dashboardAftersales", translatedTextField: "Dashboard_Title",translatedTextValue:"",  },
        {pageName:"AftersalesKPIs", translatedTextField: "Dashboard_KPIs",translatedTextValue:"", },
        {pageName:"AftersalesDetail", translatedTextField: "Common_Detail",translatedTextValue:"", },
        {pageName:"AftersalesDatasets", translatedTextField: "Dashboard_Datasets", translatedTextValue:"", }
      ]},


      //Site compare dashboard
      {sectionName:"SiteCompare",translatedTextField:"SiteCompare", translatedTextValue:"",  pageName: "dashboardSiteCompare",enableSitesSelector:true, pages:[
      ]}
     
      
    ],
    canChooseMonth:true,
    showStockCover: false,
    includeExtraSpainMenuButtons:true,
    showZoeSales: true,
    showHandoverDiarySummary: true,
    showCashDebts: true,
    showBonusDebts: true,
    showRenaultRegistrations: false,
    showDaciaRegistrations: false,
    showFleetRegistrations: true,
    showUsedStockMerchandising: false,
    showCommissions: false,
    showVocNPSTile: true,
    showfAndIPerformanceRRG: false,
    showActivityLevels: false,
    showActivityOverdues:false,
    showFinanceAddonPerformance: false,
    showFleetPerformance: true,
    showVoC: false,
    showServiceBookings: false,
    showCitNow: false,
    showImageRatios: false,
    showSalesmanEfficiency: false,
    showEvhc: false,
    showFinanceAddons: false,
    showRegistrations: false,
    showSimpleDealsByDay: true,
    excludeTypesFromBreakDown: null,
    showFinanceAddonsAllSites: false,
    includeDemoStockInStockHealth:false,
  },
  dealDetailModal: 
  { 
    showOtherProfit: false, 
    showFinanceProfit: false, 
    showAddOnProfit: false,
    showTotalProfit: false ,
    showStockDetailButton:false,
    //  showStockWebsiteListingButton:false,
  },
  debts: 
  { 
    agedDebts: true,
    includeBonuses: false,
    simpleSitesTable: true,
    showAgedOnPicker: true,
    showBonusDebtType: false
  },
  evhcTile: 'Renault',
  horizontalBar:{
    title: 'ExDemo',
    exDemo: 'params.data.Stock.StockBreakdown.ExDemo',
    forRenault: true,
    forVindis: false,
  },
  stockItemModal: 
  { 
    onlineMerchandising: true 
  },
  wipsTable: 
  { 
    hideBookingColumn: false,
    hideDepartmentColumn: false,
    hideAccountColumn: false,
    hideDueDateOutColumn: true,
    hideWDateInColumn: true,
    hideWDateOutColumn: true,
    hidePartsColumn: false,
    hideOilColumn: true,
    hideLabourColumn: false,
    hideSubletColumn: true,
    hideProvisionColumn: true,
    hideCreatedColumn: false,
    hideNotesColumn: false,
  },
  stockTable: {
    hideTacticalColumn: false,
    hideExManagementColumn: false,
    hideExDemoColumn: false,
  },
  stockList: {
     hideStockDetailsModal: true,
    tableColumns: [
      "Id",
      "SiteCode",
      "SiteDescription",
      "StockNumber",
      "Reg",
      "VehicleType",
      "Chassis",
      "RegDate",
      "StockDate",
      "DaysInStock",
      "ReservedDate",
      "Make",
      "Model",
      "ModelYear",
      "Description",
      "VehicleType",
      "VehicleSuperType",
      "DisposalRoute",
      "Colour",
      "Mileage",
      "Fuel",
      "Doors",
      "Transmission",
      "C02",
      "Purchased",
      "Siv",
      "Selling",
      "VehicleTypeCode",
      "PhysicalLocation",
      "Zona",
      "DisposalRouteEs",
      "MakeEs",
      "ModelEs",
      "VehicleSource",
      "Antiguedad_Stock",
      "Edad_Vehiculo",
      "Concesionario",
      "Ubicacion",
      "DateFactoryTransportation",
      "DateVehicleRecondition",
      "DateSiteTransportation",
      "DateSiteArrival",
      "IdFamily",
      "IdBrand",
      "IdLocation",
      "IdDataSource"
    ],
    franchises: ["R", "D", "A", "Z"]
  },
  performanceLeague: {
    hideBadges: true,
    showDeliveredButton: false,
    incLeaversButton: false,
    showExecAndManagerSelector: false,
  },
  sitesLeague:{
    includeToday: false,
  },
  overAgeStockTable: {
    hideDemoColumn: true,
    hideTacticalColumn: true,
    hideExManagementColumn: true,
    hideExDemoColumn: true,
    hideTradeColumn: true,
    usedColumnName: 'Used'
  },
  partsSales: 
  { 
      showMarginColPerc: false,
      showMarginCol: false,
      includeMarginCols: false
  },
  orderBook:  {
    showNewOrderbook:false,
    showNewDealButton:false,
    ordersDescription: 'Pedidos aprobados entre',
    hideDeliverySiteColumn: true,
    hideVehicleTypeColumn:true,
    hideVehClassColumn: true,
    hideFinanceProfitColumn:true,
    hideVehTypeColumn: true,
    hideModelColumn: false,
    hideModelYearColumn: false,
    hideVehicleSourceColumn: true,
    hideIsConfirmedColumn: true,
    hideIsClosedColumn: true,
    hideUnitsColumn: false,
    hideOtherProfitColumn: true,
    hideFinanceTypeColumn: true,
    hideIsLateCostColumn: true,
    hideMetalColumn: true,
    hideSalesChannel: true,
    hideComments: true,
    hideOrderAllocationDate: true,
    customDateTypes: ['Fecha de entrega', 'Fecha de la factura', 'Fecha de contabilidad'],
    defaultDateType: 'Invoice',
    showLateCost: true,
    showAccountingDateButton:true,
    showDeliveryOptionButtons:true,
    showOrderOptions: false,
    hideDiscountColumn: true,
    includeAccgDate: false,
    hideAddonsColumn: true,
    hideChannelColumn: false,
    hideTypeColumn: false,
    showMetalSummary: false,
    showOtherSummary: false,
    showFinanceSummary: false,
    showInsuranceSummary: false,
    siteColumnWidth: 130,
    customerColumnWidth: 220,
    vehicleClassColumnWidth: 90,
    salesExecColumnWidth: 200,
    descriptionColumnWidth: 300,
    hideOrderDateSelection: true,
    hideDaysToDeliverColumn:true,
    hideDaysToSaleColumn: true,
    hideLocationColumn:true,
    hideOemReferenceColumn:true,
    hideAuditColumn: true,
    showManagerSelector: false,
    hideDateFactoryTransportationColumn: false,
    hideDateVehicleReconditionColumn: false,
    hideDateSiteTransportationColumn: false,
    hideDateSiteArrivalColumn: false,
    hideReservedDateColumn: false,
  },
  handoverDiary: 
  { 
    includeCustomerName: true,
    includeLastPhysicalLocation: false,
    includeHandoverDate: true,
    isInvoiced: true,
    isConfirmed: false,
    futureHandoversGreyedOut: false,
    showManagerSelector: false
  },
  
  partsStockDetailedTable:{
    hideCreatedColumn: false,
    partStockBarCharts1: {
      headerName: '% >1 year',
      field: 'PartsStockRRG.PercentOver1yr',
      colId: 'PartsStockRRG.PercentOver1yr'
    },
    partStockBarCharts2: null,
    showPartStockAgeingColumnsForRRG: true,
    showPartStockAgeingColumnsForVindis: false,
    hideOfWhichColumn: true,
    hideDeadValueColumn: true,
    hideDormantValueColumn: false,
    hideDeadProvColumn: false,
    hideDormantProvColumn: false,
    setClassesForVindis:false,
    setClassesForRRG:true
  },
  salesPerformance: 
  { 
      description: 'Pedidos aprobados entre',
      showFranchisePicker: false,
      showLateCostButtons: false,
      showIncludeExcludeOrders: false,
      showTradeUnitButtons: false,
      showMotabilityButtons: false,
      showOrderRateReportType:false,
      showCustomReportType: true,
      showAllSites: false
  },
  selectionsService: {
    ageingOptions: [
      { description: '30 dias', ageCutoff: 30 },
      { description: '60 dias', ageCutoff: 60 },
      { description: '90 dias', ageCutoff: 90 },
      { description: '120 dias', ageCutoff: 120 },
      { description: '150 dias', ageCutoff: 150 },
      { description: '180 dias', ageCutoff: 180 },
      { description: '1 año', ageCutoff: 365 }
    ],
    ageingOption: { description: '60 dias', ageCutoff: 60 },
    deliveryDateDateType: 'Fecha de contabilidad',
    eligibleForCurrentUserCheck: true
  },
  serviceBookingsTable:{
    showPrepHours: true,
    clickSiteEnable: true,
  },
  stockReport:
  { 
    showAgePicker: false,
    hideOnRRGSiteCol: false,
    initialStockReport: 'Dashboard_PartsStock_UsedStock',
    seeUsedStockReport: false,
    seeAllStockReport: false,
    seeUsedMerchandisingReport: false,
    seeOverageStockReport: false,
    seeStockGraphsReport: true,
    seeStockByAgeReport: true,
    includeReservedCarsOption: false
  },
  whiteboard:{
    showConfirmed: false,
    showNotConfirmed: false,
    showFinance: false,
    showAddons: false,
    showLateCostPicker: true,
    showManagerSelector: false
  },
  serviceChannels:[
    { displayName: 'Mecánica', name: 'Mechanical', channelTags: ['mechanical'], icon: 'fas fa-wrench', hasHours: true, divideByChannelName: 'Mechanical', isLabour:true},
    { displayName: 'RMS',name: 'RMS', channelTags: ['rms'], icon: 'fas fa-car-wash', hasHours: true, divideByChannelName: 'RMS', isLabour:true},
    { displayName: 'Carrocería',name: 'Bodyshop', channelTags: ['bodyshop'], icon: 'fas fa-engine-warning', hasHours: true, divideByChannelName: 'Bodyshop', isLabour:true},
    { displayName: 'Total',name: 'Total', channelTags: ['mechanical', 'rms', 'bodyshop'], isTotal: true, icon: '', hasHours: true, divideByChannelName: 'Total', isLabour:true},
  ],

  partsChannels:[
    {displayName: 'Recnmbios',  name: 'Retail', channelTags: ['retail'], icon: 'fas fa-wrench', channelTag: 'retail', hasHours: false, divideByChannelName: 'Retail', isLabour: false},
    {displayName: 'Cesiones',  name: 'Transfers', channelTags: ['transfers'], icon: 'fas fa-car-wash', channelTag: 'transfers', hasHours: false, divideByChannelName: 'Transfers', isLabour: false},
    {displayName: 'Taller', name: 'Workshop', channelTags: ['workshop'], icon: 'fas fas fa-tire ', channelTag: 'workshop', hasHours: false, divideByChannelName: 'Workshop', isLabour: false},
    {displayName: 'Garantia', name: 'Warranty', channelTags: ['warranty'], icon: 'fas fa-engine-warning', channelTag: 'warranty', hasHours: false, divideByChannelName: 'Warranty', isLabour: false},
    {displayName: 'Total',  name: 'Total', isTotal: true, channelTags: ['retail', 'transfers', 'workshop', 'warranty'], icon: '', channelTag: 'total', hasHours: false, divideByChannelName: 'Total', isLabour: false},
  ],
  initialPageURL:"/dashboard",

  orderBookURL: "/orderBook",
  fleetOrderbookURL: "/fleetOrderbook",
  product:
  { 
    tyreInsurance: 'deal.HasTyre',
    tyreAlloyInsurance: 'deal.HasTyreAlloy',
    showAlloyInsurance: false,
  },
  dealDone:{
    showVindisSitePicker: false,
    showRRGSitePicker: true,
    showRRGPopoverContent: true,
    showVindisPopoverContent: false,
  },
  evhc:{
    showTechTable: true,
    vehiclesCheckedPercent: 100,
    workQuoted: 205,
    workSoldPercent: 65,
    eDynamixView: false,
    redWorkSoldPercent: 65,
    amberWorkSoldPercent: 25
  },
  fAndISummary:{
    processTypeAndTypeAlloy: true,
    hideAlloyColumn: false
  },
  partsStock:{
    includeOfWhichColumns: false
  },
  dealsForTheMonth:{
    showMetal: false,
    showOther: false,
    showFinance: false,
    showAddons: false,
    showGpu: false,
    showBroughtInColumn: false,
    showIncludeExcludeOrders: false,
    showLateCostPicker: true,
  },
  allGroups: ['O', 'RE'],
  allFamilyCodes: ['P.PROFESIONAL', 
    'MOTRIO', 
    'VARIOS', 
    'NULL', 
    'NEUMATICOS', 
    'OTROS', 
    'ACC.Y BOUTIQUE', 
    'LUBRICANTES', 
    'IXELL', 
    'MANT.Y DESGASTE', 
    'CARROC Y PINTUR', 
    'MECANICA INCIDE'], 
  partsStockSitesCoverTable: {
    partStockName: 'PartsStockRRG',
  },


  dealsDoneThisWeek:{
    showPlotOptions: false
  },
  orderTypePickerOptions:{
    showRetail: false,
    showFleet: false
  },
  todayMap:{
    defaultPositionLat: 39.955071,
    defaultPositionLong: -1,
    defaultZoom: 7,
  },
  vehicleTypePicker:
  { 
    showUsed: false,
    showNew: false,
    showAll: false,
    hiddenVehicleTypes: ['Demo']
  },
  userSetup:{
    hideUploadReports: true,
    hideViewReports: true,
    hideCommReview: true,
    hideCommSelf: true,
    hideSerReviewer: true,
    hideSerSubmitter: true,
    hideStockLanding: true,
    hideSuperCup: true,
    hideIsSalesExec: true,
    hideAllowReportUpload: true,
    hideAllowReportCentre: true,
    hideCanEditExecManagerMappings: true,
    hideLiveforecast: true,
    hideSalesRoles: true,
    hideTMgr: true,
    allSalesRoles: ['None'],
    canReviewStockPrices: false,
    canActionStockPrices: false,
    canEditStockPriceMatrix: false
  },
  languageSelection: true,


  serviceSummary: {
    showTableTypeSelector: false,
    defaultTableType: null,
    tableTypes: null,
    defaultTimeOption: 'MTD',
    timeOptions: ['MTD', 'WTD', 'Yesterday'],
    showTechGroupColumns: false,
  },
  partsSummary: {
    showTableTypeSelector: false,
    defaultTableType: null,
    tableTypes: null,
  },
  serviceSalesDashboard: {
    onlyLabour: true
  },
  dealDetailsModal: {
    currencyDP: 0,
    costColumnTranslation: 'Common_CoS',
    dealDetailsSection: {
      showVariant:true,
      showWebsiteDiscount:false,
      showFinanceType: false,
      showOEMReference: false,
      showQualifyingPartEx: false,
      showPhysicalLocation: false,
      showIsClosed: false,
      showFinanceCo: true,
      showDescription: true,
      showUnits: true,
      showVehicleAge: true,
      showIsLateCost: true,
      showAuditPass: false,
      showInvoiceNo: true
    },
    metalProfitSection: {
      headerTranslation: 'DealDetails_TinProfit',
      showVATCost: false
    },
    otherProfitSection: {
      showRegBonus:true,
      showIntroComm:true,
      showBrokerCost:false,
      showAccessories:false,
      showPaintProtectionAccessory: false,
      showFuel:false,
      showDelivery:false,
      showStandardWarranty:false,
      showPdi:false,
      showMechPrep:false,
      showBodyPrep:false,
      showOther: false,
      showError:false,
      showTotal:false,
    },
    addonsSection:{
      showPaintProtection:true,
      showWarrantyForNewCar:false
    },
    datesSection: {
      showCustomerDestinationDeliveryDate: true,
      showEnterImportCentreDate: true,
      showShipDate: true,
      showExitImportCentreDate: true,
      showAllocationDate: true,
      showDateVehicleRecondition: true,
      showDateFactoryTransportation: true,
      showDateSiteArrival: true,
      showDateSiteTransportation: true
    },
    financeProfitSection: {
      show: false,
      rciFinanceCommissionText: 'DealDetails_RciFinanceCommission',
      financeCommissionText: 'DealDetails_FinanceCommission',
      showSelectCommission: true,
      showProPlusCommission: true,
      showStandardsCommission: true
    },
    showTotalProfitExludingFactoryBonusSection: false,
    showTotalProfitSection: true
  },
  donutShowLastYearUnits: true,
  showNewUsedSummaryBadges: false,
  showPrepCostsWhenValuing: false,
  isSingleSiteGroup: false,

  showChangePriceNowInputAlways:false,
  
  menuItems: {
    dashboard_HasDashboard: true,
    dashboard_Home: true,
    dashboard_Overview: false,
    dashboard_Sales: false,
    dashboard_NewKPIs: true,
    dashboard_UsedKPIs: true,
    dashboard_Aftersales: true,
    dashboard_SiteCompare: true,
    orderbook: true,
    orderbook_HasOrderbook: false,
    orderbook_Retail: true,orderbook_Distrinet:true,
    orderbook_Fleet: false,
    operationalReports_HasOperationReports: true,
    operationalReports_DealsWeek: true,
    operationalReports_DealsMonth: true,
    operationalReports_Whiteboard: true,
    operationalReports_HandoverDiary: true,
    operationalReports_TelephoneStats: false,
    operationalReports_StockLanding: false,
    operationalReports_PerformanceTrends: false,
    salesReports_HasSalesReports: true,
    salesReports_SalesPerformance: true,
    salesReports_Alcopas: true,
    salesReports_OrderRate: true,
    salesReports_Registrations: false,
    salesReports_FAndI: false,
    salesReports_StockReports: false,
    salesReports_StockList: true,
    salesReports_Debtors: true,
    salesReports_CitNOW: false,
    salesReports_ImageRatios: false,
    salesReports_GDPR: false,
    salesReports_Activities: false,
    reportPortal: false,
    aftersalesReports_HasAftersalesReports: false,
    aftersalesReports_ServiceSales: false,
    aftersalesReports_ServiceBookings: false,
    aftersalesReports_EVHC: false,
    aftersalesReports_Upsells: false,
    aftersalesReports_WIPReport: false,
    aftersalesReports_Debtors: false,
    aftersalesReports_CitNOW: false,
    aftersalesReports_PartsSales: false,
    aftersalesReports_PartsStock: false,
    peopleReports_PerformanceLeague: true,
    peopleReports_SalespersonEffeciency: false,
    peopleReports_SalespersonCommission: false,
    peopleReports_Scratchcard: false,
    peopleReports_SalesExecReview: false,
    peopleReports_HasPeopleReports: true,
    vehiclePricing_HasVehiclePricing: false,
    vehiclePricing_ShowDetailedMenu:false,
    vehiclePricing_Dashboard: false,vehiclePricing_SitesLeague:false,
    vehiclePricing_StockReport: false,
    vehiclePricing_TodaysPriceChanges: false,
    vehiclePricing_OptedOutVehicles: false,
    vehiclePricing_LocationOptimiser: false,
    vehiclePricing_VehicleValuation: false, vehiclePricing_Home:false,
    vehiclePricing_BuyingOpportunities: false,
    vehiclePricing_LeavingVehicleTrends: false,
    vehiclePricing_LeavingVehicleDetail: false,
    vehiclePricing_SiteSettings: false,
    vehiclePricing_StockQuickSearch: false,
    userMaintenance: true
  },
  showRotationButton:false,
  showLatestSnapshotDate: false,
  showApproveAutoPrices:false,
  showNestedSideMenu: true,
  vehiclePricing_StockReport_showBcaColumns: false,
  // autoprice: {
  //   defaultShowUnpublishedAds:false,
  //   defaultShowNewVehicles: false,
  //   vehicleValuationShowCostingDetail: true,
  //   lifecycleStatusDefault: ['FORECOURT','SALE_IN_PROGRESS'],
  //   applyPriceScenarios: false,
  //   vehicleTypes: null,
  //   defaultVehicleTypes: null,
  //   separateBuyingStrategy:false,separateBuyingStrategy2:false,
  //   allowChooseNewStrategy:false,
  //   allowTestStrategy:false,
  //   stockReport: {
  //     showDMSSellingPrice_Col: false,
  //     showVsDMSSellingPrice_Col: false, 
  //     showPhysicalLocation_Col: false,

  //   },
  //   defaultToDaysInStock:false,
  // },
  dealershipBackgroundImageName: 'RRGSpain',
  homeIsLandingPage: false,
  showRegionFilterOnSiteDashboard: false

}
