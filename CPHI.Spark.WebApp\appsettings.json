{

  //-------------------------------------------------x
  // THIS IS WEB API
  //-------------------------------------------------x 
  //Migration instructions updated 13 Feb 2024:
  // Change the line that starts:   options.UseSqlServer(Configuration.GetConnectionString("RRGUKConnection")).LogTo(msg =>   within startups.cs to use the right connection string
  // choose Production at the top in VS, then update that connection string username / password in the DEVELOPMENT appsettings to be richardp<PERSON>cter and <password>
  // repeat the above to apply the migrations to each db
  // ensure you revert any startup and appsettings changes later


  //-------------------------------------------------x
  // Enable for all section
  //-------------------------------------------------x

  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.EntityFrameworkCore": "Error", //supresses the yellow warnings when you run a EF migration
      "Microsoft.Hosting.Lifetime": "Information"

    }
  },
  "AllowedHosts": "*",
  "EmailSettings": {

    "mailAppTenantId": "7def64f8-8b3f-400a-8ad0-e50eb7e77eef",
    "mailAppId": "9a44d66f-cfe2-4d19-b129-62b3e0dd28aa",
    "mailSecret": "Taylor Swift, definitely",
    "mailSecretValue": "****************************************",
    "mailAccount": "<EMAIL>"
  },

  "AutotraderSettings": {

    "ApiKeyForChangingPrices": "CPHInsight-PricingBI-22-07-24_FOOBAR",
    "ApiSecretForChangingPrices": "nyGQrMe4oUVTM7iGPZdgghrDP8nTeZKw_FOOBAR",
    "BaseURLForChangingPrices": "https://api.autotrader.co.uk_FOOBAR" //"mioG2v2IDd6G0SyiYbKDyymF64SPgJfe",
  },
  "DVSASettings": {
    "TokenURL": "https://login.microsoftonline.com/a455b827-244f-4c97-b5b4-ce5d13b4d00c/oauth2/v2.0/token",
    "BaseURLForRecallApi": "https://history.mot.api.gov.uk/v1/trade/vehicles/registration/",
    "ApiKey": "VGJ4SuSrf768lAPZCtMhlLQPEkkjBPp6vii4NCH3",
    "ClientId": "ab4d91a5-990a-4424-8486-80692e12f82e",
    "ClientSecret": "****************************************",
    "Scope": "https://tapi.dvsa.gov.uk/.default"
  },


  "BlobStorage": {
    "StorageAccount": "stockpulseimages",
    "StorageAccountKey": "****************************************************************************************",

    //Dev

    "ReadOnlyKey": "sp=r&st=2021-12-17T17:27:52Z&se=2041-02-14T00:27:52Z&spr=https&sv=2020-08-04&sr=c&sig=HC3gt1Pj6Vbiph%2BmA4TeTjWlR7Toghvt%2BXV3CAiFym4%3D"

  }


}

 