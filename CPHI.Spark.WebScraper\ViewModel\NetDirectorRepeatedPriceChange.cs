namespace CPHI.Spark.Model.ViewModels
{
   public class NetDirectorRepeatedPriceChange
   {
      public string Reg { get; set; }
      public decimal TodayNowPrice { get; set; }
      public decimal YesterdayNowPrice { get; set; }
      public decimal DailyDifference { get; set; }
      public decimal MinimumAutoPriceDecrease { get; set; }
      public decimal MinimumAutoPriceIncrease { get; set; }
      public string RetailerSiteName { get; set; }
   }

}
