import {Injectable} from '@angular/core';
import {StrategyFactorName} from 'src/app/model/StrategyFactorName';
import {StrategyFactorVM} from 'src/app/model/StrategyFactorVM';
import {StrategyVersionVM} from "src/app/model/StrategyVersionVM";
import {AutotraderService} from 'src/app/services/autotrader.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {SelectionsService} from 'src/app/services/selections.service';


@Injectable({
   providedIn: 'root'
})
export class PricingPolicyModalService {

   public chosenPolicy: StrategyVersionVM;

   allStrategyFactors = [];

   constructor(
      public selections: SelectionsService,
      public constants: ConstantsService,
   ) {
   }

   initAllFactors() {
      //this controls the ordering of them in the modal when u choose to add another
      this.allStrategyFactors = [
         new StrategyFactorVM(StrategyFactorName.RR_DL_Matrix, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.RR_DS_Matrix, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.RR_DB_Matrix, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.DTS_DL_Matrix, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.RetailRatingBand, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.RetailRating10sBand, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.DaysListed, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.DaysListedBand, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.DaysInStock, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.DaysInStockBand, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.DaysToSell, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.OnBrandCheck, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.RetailerName, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.Brand, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.ModelName, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.MakeFuelType, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.MakeAgeBand, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.SpecificColour, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.AgeAndOwners, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.ValuationChangeUntilSell, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.MatchCheapestCompetitor, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.AchieveMarketPositionScore, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.MinimumProfit, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.MinimumPricePosition, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.RoundToNearest, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.RoundToPriceBreak, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.WholesaleAdjustment, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.Mileage, this.constants.RetailerSites, null),
         new StrategyFactorVM(StrategyFactorName.PerformanceRatingScore, this.constants.RetailerSites, null),
      ]
   }

   public getFactorNameToPrettyPrint(factor: StrategyFactorVM): string {
      if (factor.Name === StrategyFactorName.RR_DL_Matrix) {
         return 'Retail Rating vs Days Listed Matrix'
      } else if (factor.Name === StrategyFactorName.RR_DS_Matrix) {
         return 'Retail Rating vs Days In Stock Matrix'
      } else if (factor.Name === StrategyFactorName.RR_DB_Matrix) {
         return 'Retail Rating vs Days Booked In Matrix'
      } else if (factor.Name === StrategyFactorName.DTS_DL_Matrix) {
         return 'Days To Sell vs Days Listed Matrix'
      } else if (factor.Name === StrategyFactorName.MakeFuelType) {
         return 'Brand and fuel type';
      } else if (factor.Name === StrategyFactorName.MakeAgeBand) {
         return 'Brand and age band';
      } else if (factor.Name === StrategyFactorName.PerformanceRatingScore) {
         return 'Performance Rating Score';
      } else {
         return factor.Name.toString()
      }
   }


   factorExplanation(factor: StrategyFactorVM) {
      return AutotraderService.factorExplanation(factor)
   }

   chooseNewFactor(factor: StrategyFactorVM) {

      this.chosenPolicy.StrategyFactors.push(factor);
   }


}
