import { Injectable } from '@angular/core';
import { MultiSelectMonth } from 'src/app/components/datePickerMultiSelect/datePickerMultiSelect.component';
import { TopBottomHighlightRule } from "src/app/model/TopBottomHighlightRule";
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDealDataService } from 'src/app/services/getDeals.service';
import { ProfilePictureService } from 'src/app/services/profilePicture.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { FAndIGetRowsParams, FAndISalesExecRow, FAndISiteRow, FAndISummary } from './fAndISummary.model';

@Injectable({
  providedIn: 'root'
})

export class FAndISummaryService {
  fAndISummary: FAndISummary;
  months: MultiSelectMonth[];
  topBottomHighlightsSites: TopBottomHighlightRule[] = [];
  topBottomHighlightsPeople: TopBottomHighlightRule[] = [];

  constructor(
    public constants: ConstantsService,
    public getData: GetDealDataService,
    public profilePictureService: ProfilePictureService,
    public selections: SelectionsService
  ) { }

  initialiseFAndISummary() {
    let orderTypes: string[];

    if (this.constants.environment.customer == 'Vindis') {
      orderTypes = this.constants.orderTypeTypesNoTrade.filter(x => x != 'Motability' && x != 'Fleet');
    } else {
      orderTypes = this.constants.orderTypeTypesNoTrade.filter(x => x !== 'Motability');
    }

    this.fAndISummary = {
      deliveryDate: {
        startDate: this.constants.thisMonthStart,
        endDate: this.constants.thisMonthEnd,
        monthName: this.constants.appStartTime.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' })
      },
      franchiseEncoded: null,
      franchises: this.constants.FranchiseCodes,
      orderTypeIds: [],
      orderTypeTypes: orderTypes,
      peopleRowData: null,
      peopleRowDataTotal: null,
      regionalsRowData: null,
      selectedSiteIds: null,
      selectedSites: [],
      sites: this.constants.clone(this.constants.sitesActiveSales.filter(x => x.IsEligibleForUser)).sort((a, b) => (a.SortOrder > b.SortOrder) ? 1 : -1),
      sitesRowData: null,
      sitesRowDataTotal: null,
      showPeopleTable: false,
      showSitesTable: false,
      totalRowSiteId: 0,
      vehicleTypeIds: [],
      vehicleTypeTypes: this.constants.vehicleTypeTypes,
      salesManagerId: null
    }

    this.setFilters();
  }

  setFilters() {
    this.updateFranchises();
    this.updateOrderTypes();
    this.updateVehicleTypes();
  }

  updateFranchises(franchises?: string[]) {
    if (franchises) this.fAndISummary.franchises = franchises;

    this.fAndISummary.franchiseEncoded = encodeURI(this.fAndISummary.franchises.toString());

    if (franchises) this.getSiteRows();
  }

  updateOrderTypes(orderTypes?: string[]) {
    this.fAndISummary.orderTypeIds = [];

    if (orderTypes) this.fAndISummary.orderTypeTypes = orderTypes;

    if (this.fAndISummary.orderTypeTypes) {
      this.fAndISummary.orderTypeTypes.forEach((ott: string) => {
        this.constants.OrderTypes.forEach(x => {
          if (x.Type == ott) this.fAndISummary.orderTypeIds.push(x.Id);
        });
      });
    }

    if (orderTypes) this.getSiteRows();
  }

  updateVehicleTypes(vehicleTypes?: string[]) {
    this.fAndISummary.vehicleTypeIds = [];

    if (vehicleTypes) this.fAndISummary.vehicleTypeTypes = vehicleTypes;

    if (this.fAndISummary.vehicleTypeTypes) {
      this.fAndISummary.vehicleTypeTypes.forEach((vtt: string) => {
        this.constants.VehicleTypes.forEach(x => {
          if (x.Type == vtt) this.fAndISummary.vehicleTypeIds.push(x.Id);
        });
      });
    }

    if (vehicleTypes) this.getSiteRows();
  }

  getSiteRows() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });

    let allActiveSitesForUser: number[] = [];

    let includeUsedTargets: boolean = false;
    let includeNewTargets: boolean = false;

    //console.log(this.fAndISummary.vehicleTypeTypes, "this.fAndISummary.orderTypeTypes");

    if(this.fAndISummary.vehicleTypeTypes.includes('CoreUsed'))
    {
      includeUsedTargets = true;
    }

    if(this.fAndISummary.vehicleTypeTypes.includes('New'))
    {
      includeNewTargets = true;
    }

    if (this.fAndISummary.selectedSiteIds) {
      allActiveSitesForUser = this.fAndISummary.selectedSiteIds;
    } else {
      // Show all sites if environment is true
      if (this.constants.environment.dashboard.showFinanceAddonsAllSites) {
        this.constants.sitesActiveSales.sort((a, b) => (a.SortOrder > b.SortOrder) ? 1 : -1).forEach(site => {
          if (site.IsActive && site.IsSales) {
            allActiveSitesForUser.push(site.SiteId);
          }
        });
      } else {
        allActiveSitesForUser = this.fAndISummary.sites.map(x => x.SiteId);
      }
    }

    let params: FAndIGetRowsParams = {
      YearMonths: this.constants.formatMonthsForParams(this.months),
      Sites: allActiveSitesForUser.toString(),
      OrderTypeIds: this.fAndISummary.orderTypeIds,
      VehicleTypeIds: this.fAndISummary.vehicleTypeIds,
      Franchises: this.fAndISummary.franchiseEncoded,
      ShowAllSites: this.constants.environment.dashboard.showFinanceAddonsAllSites ? true : false,
      IncludeTargets: this.constants.environment.fAndISummary_includeTargets,
      SalesManagerId: '',
      IncludeNewTargets: includeNewTargets,
      IncludeUsedTargets: includeUsedTargets
    }

    this.getData.getFinanceAddons(params).subscribe((res: FAndISiteRow[]) => {
      this.fAndISummary.sitesRowData = res.filter(s => s.SiteId != this.fAndISummary.totalRowSiteId && s.SiteId != null);

      this.fAndISummary.sitesRowData.forEach((element: FAndISiteRow) => {
        element.siteName = this.constants.getSiteNameFromSiteId(element.SiteId);
      });

      this.fAndISummary.sitesRowDataTotal = res.filter(s => s.SiteId == this.fAndISummary.totalRowSiteId && s.IsTotal);
      this.fAndISummary.sitesRowDataTotal[0].siteName = this.constants.translatedText.Common_Total;

      this.getRegionRows(res);
    }, error => {
      console.error("Error retrieving F&I site rows: ", error);
    }, () => {
      this.fAndISummary.showSitesTable = true;
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getRegionRows(rows: FAndISiteRow[]) {
    this.fAndISummary.regionalsRowData = rows.filter(s => s.SiteId == null && s.RegionId > 0).sort((a, b) => a.RegionId - b.RegionId);

    if (this.fAndISummary.regionalsRowData.length > 0) {
      this.fAndISummary.regionalsRowData.forEach((element: FAndISiteRow) => {
        element.siteName = element.RegionName
      });
    }
  }

  getPeopleRows(site: FAndISiteRow) : void {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });

    let siteString: string = "";

    let includeUsedTargets: boolean = false;
    let includeNewTargets: boolean = false;

    if(this.fAndISummary.vehicleTypeTypes.includes('CoreUsed'))
    {
      includeUsedTargets = true;
    }

    if(this.fAndISummary.vehicleTypeTypes.includes('New'))
    {
      includeNewTargets = true;
    }

    if (this.fAndISummary.salesManagerId != null && this.fAndISummary.selectedSites && this.fAndISummary.selectedSites[0].SiteId != null && this.fAndISummary.selectedSites[0].SiteId != 0 ){
      this.fAndISummary.selectedSites.forEach((site: any) => {
        siteString += site.SiteId + ',';
      });
    }
    // Row is a normal site
    else if (site.SiteId != this.fAndISummary.totalRowSiteId && site.SiteId != null) {
      siteString = site.SiteId.toString();
    } else {
      siteString = this.totalOrRegionalRowClicked(site).toString();
    }

    let parms: FAndIGetRowsParams = {
      YearMonths: this.constants.formatMonthsForParams(this.months),
      Sites: siteString,
      OrderTypeIds: this.fAndISummary.orderTypeIds,
      VehicleTypeIds: this.fAndISummary.vehicleTypeIds,
      Franchises: this.fAndISummary.franchiseEncoded,
      ShowAllSites: this.constants.environment.dashboard.showFinanceAddonsAllSites ? true : false,
      SalesManagerId: (this.fAndISummary.salesManagerId == null)? '' : this.fAndISummary.salesManagerId.toString(),
      IncludeTargets: this.constants.environment.fAndISummary_includeTargets,
      IncludeNewTargets: includeNewTargets,
      IncludeUsedTargets: includeUsedTargets
    }

    this.getData.getFinanceAddonsBySite(parms).subscribe((res: FAndISalesExecRow[]) => {
      this.fAndISummary.peopleRowData = res.filter(s => !s.IsTotal);

      //doing this as we are using component to display the name and profile pic
      if (this.constants.environment.customer == 'Vindis'){
        if (this.fAndISummary.salesManagerId == null){
          this.fAndISummary.peopleRowData.forEach(r => {r.SalesExecName = r.SalesManagerName, r.SalesmanId = r.SalesManagerId});
        }
      }
      
      this.fAndISummary.peopleRowData = this.getPhotosForAllExecs(this.fAndISummary.peopleRowData);
      this.fAndISummary.peopleRowData.sort((a, b) => a.SalesExecName.localeCompare(b.SalesExecName));
      this.fAndISummary.peopleRowDataTotal = res.filter(s => s.IsTotal);
    }, error => {
      console.error("Error retrieving F&I people rows: ", error);
    }, () => {
      this.fAndISummary.showSitesTable = false;
      this.fAndISummary.showPeopleTable = true;
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  totalOrRegionalRowClicked(site: FAndISiteRow): number[] {
    let siteIdArray: number[] = [];

    // If total row, else region row
    if (site.SiteId == this.fAndISummary.totalRowSiteId) {
      this.fAndISummary.sitesRowData.forEach((row: FAndISiteRow) => {
        siteIdArray.push(row.SiteId);
      })
    } else {
      this.fAndISummary.sitesRowData.forEach((row: FAndISiteRow) => {
        if (site.RegionId == row.RegionId) {
          siteIdArray.push(row.SiteId);
        }
      })
    }

    return siteIdArray;
  }

  getPhotosForAllExecs(listOfExecRows: FAndISalesExecRow[]): FAndISalesExecRow[] {
    listOfExecRows.forEach((element: FAndISalesExecRow) => {
      // Get user profile photos
      if (element.SalesmanId) element.imageBase64 = this.profilePictureService.getProfilePictureURL(element.SalesmanId);
    });

    return listOfExecRows;
  }
}
