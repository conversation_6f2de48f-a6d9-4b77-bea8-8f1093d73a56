﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Reporter.Services;
using CPHI.Spark.Reporter.Services.AutoPrice;
using CPHI.Spark.Repository;
using Datadog.Trace;
using log4net;
using Quartz;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Jobs.AutoPrice
{

   public class TemporaryJob : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(FetchAndGenerateAutoTraderPrices_Job));


      // ------------------------
      // START HERE 
      // ------------------------
      public async Task Execute(IJobExecutionContext context)
      {
         logger.Info("=========================================================================================");
         logger.Info("Starting TEMP JOB");
         HttpClient httpClient = HttpClientFactoryService.HttpClientFactory.CreateClient();

         //############################################################################################################################################
         //Setup here to choose which parts of the daily fetch to perform
         //############################################################################################################################################
         ReporterDealerGroupsConfig config = new ReporterDealerGroupsConfig();

         //RUN FOR THESE: Vindis,V12,BrindleyGroup,KCSOfSurrey,WaylandsGroup,PentagonGroup,HippoApproved,LMCOfFarnham,Startin,OakwoodMotorCo,SparshattsGroup,EMGGroup,AcornGroup,Lithia,FordsOfWinsford,LSH,Sytner,Sandicliffe,Nuneaton
        

         config.sendEmailsGroup = new List<DealerGroupName>() {
            //DealerGroupName.Enterprise,
         //   DealerGroupName.V12,
         //   DealerGroupName.BrindleyGroup,
         //   DealerGroupName.KCSOfSurrey,
         //   DealerGroupName.WaylandsGroup,
         //DealerGroupName.PentagonGroup,
         //DealerGroupName.HippoApproved,
         //DealerGroupName.LMCOfFarnham,
         //DealerGroupName.Startin,
         //DealerGroupName.OakwoodMotorCo,
         //DealerGroupName.SparshattsGroup,
         //DealerGroupName.EMGGroup,
         //DealerGroupName.AcornGroup,
         //DealerGroupName.Lithia,
         //DealerGroupName.FordsOfWinsford,
         DealerGroupName.LSH,
         DealerGroupName.Sytner,
         //DealerGroupName.Nuneaton

         };


         //--------------
         // Adhoc
         //--------------
         //"FetchJob_LocationOptimiserGroups": "RRGUK,Vindis,WaylandsGroup,PentagonGroup,LSH,Lithia,Enterprise", //ok
         //config.locationOptimiserGroup = new List<DealerGroupName>()
         //            {
         //                DealerGroupName.RRGUK,
         //DealerGroupName.Vindis,
         ////DealerGroupName.V12,
         ////DealerGroupName.BrindleyGroup,
         ////DealerGroupName.KCSOfSurrey,
         //DealerGroupName.WaylandsGroup,
         //DealerGroupName.PentagonGroup,
         ////DealerGroupName.HippoApproved,
         ////DealerGroupName.LMCOfFarnham,
         ////DealerGroupName.Startin,
         ////DealerGroupName.OakwoodMotorCo,
         ////DealerGroupName.SparshattsGroup,
         ////DealerGroupName.BellsCrossgar,
         ////DealerGroupName.CroxdaleGroup,
         ////DealerGroupName.EMGGroup,
         ////DealerGroupName.AcornGroup,
         //DealerGroupName.Lithia,
         ////DealerGroupName.FordsOfWinsford,
         //DealerGroupName.LSH,
         ////DealerGroupName.Sytner
         //DealerGroupName.Enterprise
         //          };





         //############################################################################################################################################
         //if instead we want to run a pre-defined set of jobs, uncomment one of these
         //############################################################################################################################################


         //--------------------------------------------------------
         // OPTION A: For setting up a group, the first step in dev
         //--------------------------------------------------------
         //config = DoInitialFetchNewDG(DealerGroupName.Sytner);

         //--------------------------------------------------------
         // OPTION A.2: For setting up a group, the first step in prod
         //--------------------------------------------------------
         //config = DoInitialFetchNewDGProd(DealerGroupName.Sandicliffe);


         //--------------------------------------------------------
         // OPTION B: Second part of setting up a group
         //--------------------------------------------------------
         //bool doLocalBargains = true;
         //bool doLocationOptimiser = false;
         //config = SetupNewDealerGroup(DealerGroupName.Sandicliffe, doLocalBargains, doLocationOptimiser);


         //--------------------------------------------------------
         // OPTION C: Testing the morning routine
         //--------------------------------------------------------
         //List<DealerGroupName> dealerGroups = new List<DealerGroupName>()
         //{
         ////    DealerGroupName.CroxdaleGroup,
         ////    //DealerGroupName.Vindis,
         ////    //DealerGroupName.Sytner,
         ////    //DealerGroupName.JJPremiumCars,
         ////    //DealerGroupName.WaylandsGroup,
         ////    //DealerGroupName.PentagonGroup,
         //    DealerGroupName.LMCOfFarnham,
         //};
         //config = DoNormalMorningFetch(dealerGroups);




















         // ###############################
         //  DO THE STUFF 
         // ###############################
         Stopwatch sw = new Stopwatch();
         sw.Start();


         FetchAutoTraderService fetchAutoTraderService = new FetchAutoTraderService(HttpClientFactoryService.HttpClientFactory);
         GenerateStrategyPricesService generateStrategyPricesService = new GenerateStrategyPricesService(HttpClientFactoryService.HttpClientFactory);
         try
         //download latest data
         {
            await fetchAutoTraderService.FetchAutoTraderAdverts(logger, config.fetchAdvertsGroup);
         }
         catch (Exception ex)
         {

         }
         LoggingService.LogTiming(sw, "FetchAdverts", logger);

         //Value and save those items in stock that are not found in AT
         try
         {
            VehicleValuationService valuationService = new VehicleValuationService(HttpClientFactoryService.HttpClientFactory);
            await valuationService.ValueAndSaveVehiclesInStockNotInAT(logger, config.inStockNotOnPortalGroup);
         }
         catch (Exception ex)
         {

         }
         LoggingService.LogTiming(sw, "InStockNotOnPortal", logger);


         //----------------------------------------------------------------
         // OwnerAndSpecificColour: Get previous owners and manufacturer specific colour for the missing ones from AT.
         // ----------------------------------------------------------------
         try
         {
            UpdateOwnersAndSpecificColourService updateOwnersAndSpecificColourService = new UpdateOwnersAndSpecificColourService();
            await updateOwnersAndSpecificColourService.UpdateOwnersAndSpecificColour(logger, config.ownerAndSpecificColourGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "OwnerAndSpecificColour", logger);

         //try
         ////populate the db cache table (VehicleAdvertItems)
         //{
         //    var populateVehicleAdvertItemsService = new PopulateVehicleAdvertItemsService();
         //    await populateVehicleAdvertItemsService.PopulateVehicleAdvertItems(logger, config.populateCacheTableGroup);
         //}
         //catch (Exception ex)
         //{
         //}
         //LoggingService.LogTiming(sw, "PopulateCacheTable", logger);


         //measure daily price moves
         try
         {
            var measure = new MeasureDailyPriceMovesService();
            await measure.MeasureDailyPriceMoves(logger, config.countPriceChangesGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "CountPriceChanges", logger);





         try
         //apply any opt-outs
         {
            var createOverageOptOutsService = new CreateOverageOptOutsService();
            await createOverageOptOutsService.CreateOptOuts(logger, config.createOptOutsGroup);  //ok
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "CreateOptOuts", logger);


         try
         //now update the leaving deals
         {
            var updateLeavingItemsService = new UpdateLeavingItemsService();
            await updateLeavingItemsService.PopulateLeavingItems(logger, config.leavingItemsGroup);  //ok
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "LeavingItems", logger);

         try
         //now track our leaving vehicles
         {
            var trackSoldVehiclesService = new TrackSoldVehiclesService();
            await trackSoldVehiclesService.TrackOurSoldVehicles(logger, config.trackOurSoldVehiclesGroup);  //ok

         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "TrackOurSoldVehicles", logger);


         try
         //update the strategy price for each snapshot
         {
            await generateStrategyPricesService.GenerateStrategyPricesAndSave(logger, config.strategyPricesGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "StrategyPrices", logger);



         try
         //now update the price indicator and days to sell for each snapshot (we are updating it based on the current selling price)
         {
            var updateDaysToSell = new UpdateDaysToSellService(httpClient);
            await updateDaysToSell.UpdateDaysToSell(logger, config.updateDaysToSellGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "UpdateDaysToSell", logger);

         try
         //SPK-4244 now also get competitor information
         {
            var getCompetitorSituationService = new GetCompetitorSituationService(httpClient);
            await getCompetitorSituationService.GetCompetitorInformationAndSave(logger, config.competitorInformationGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "CompetitorInformation", logger);

         //Weekday create changes and send emails
         try
         //use our rules to generate prices changes
         {
            var generatePriceChangesService = new GeneratePriceChangesService(httpClient);
            await generatePriceChangesService.GeneratePriceChanges(logger, config.generateAutoChangesGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "GenerateAutoChanges", logger);
         try
         //email a summary of the price changes
         {
            var reportTodayPriceChangesService = new ReportTodayPriceChangesGeneratedService();
            await reportTodayPriceChangesService.SendEmails(logger, config.sendEmailsGroup, false);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "SendEmails", logger);

         try
         //tidy away old valuations
         {
            var deleteOldValuationsService = new DeleteOldValuationsService();
            await deleteOldValuationsService.DeleteOldValuations(logger, config.deleteOldValuationsGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "DeleteOldValuations", logger);

         //Location Optimiser bits
         try
         {
            //think this actually creates them
            await fetchAutoTraderService.MeasureOtherSiteRetailRatings(logger, config.locationOptimiserGroup);

            //then this works out the prices.  although it's using the wrong way of calculating strategy
            await generateStrategyPricesService.CalcStrategyPriceOtherSiteLocations(logger, config.locationOptimiserGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "LocationOptimiser", logger);
         try
         {
            var localBargainsService = new LocalBargainsService();
            await localBargainsService.FindLocalBargains(logger, config.localBargainsGroup);
            await localBargainsService.DeleteOldBargains(logger, config.localBargainsGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "FindAndDeleteLocalBargains", logger);

         //Update globalParams with last download date
         try
         {
            var updateGLobalParamsService = new UpdateGlobalParamsLastUpdateDateService();
            await updateGLobalParamsService.UpdateDates(logger, config.globalParamsGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "GlobalParams", logger);


         //Finally trigger cache rebuilds
         try
         {
            var triggerCacheRebuildService = new TriggerCacheRebuildService();
            await triggerCacheRebuildService.TriggerCacheRebuilds(logger, config.triggerCacheGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "TriggerCache", logger);

         try
         {
            ConfirmDataLoadedService confirmDataLoadedService = new ConfirmDataLoadedService();
            await confirmDataLoadedService.SendEmails(logger, config.sendEmailConfirmationGroup);
         }
         catch (Exception ex)
         {
         }
         LoggingService.LogTiming(sw, "SendConfirmationEmail", logger);

         await Tracer.Instance.ForceFlushAsync();


         logger.Info("Completed TEMP JOB");
         logger.Info("============================================================================================");
      }


      private static ReporterDealerGroupsConfig SetupNewDealerGroup(DealerGroupName newDg, bool doLocalBargains, bool doLocationOptimiser)
      {
         ReporterDealerGroupsConfig config = new ReporterDealerGroupsConfig();



         config.fetchAdvertsGroup = new List<DealerGroupName>() { newDg };
         //config.populateCacheTableGroup = new List<DealerGroupName> { newDg };
         //config.inStockNotOnPortalGroup = new List<DealerGroupName> { newDg};
         //config.countPriceChangesGroup = new List<DealerGroupName> { };
         //config.createOptOutsGroup = new List<DealerGroupName>() { };
         //config.leavingItemsGroup = new List<DealerGroupName>() { };
         config.strategyPricesGroup = new List<DealerGroupName>() { newDg };
         config.updateDaysToSellGroup = new List<DealerGroupName>() { newDg };
         config.competitorInformationGroup = new List<DealerGroupName>() { newDg };
         config.generateAutoChangesGroup = new List<DealerGroupName>() { newDg };
         //config.sendEmailsGroup = new List<DealerGroupName>() { };
         //config.emailChangesWeekendGroup = new List<DealerGroupName>() { };
         if (doLocationOptimiser) { config.locationOptimiserGroup = new List<DealerGroupName>() { newDg }; }
         //config.triggerCacheGroup = new List<DealerGroupName> { newDg };
         config.globalParamsGroup = new List<DealerGroupName> { newDg };
         if (doLocalBargains) { config.localBargainsGroup = new List<DealerGroupName>() { newDg }; }
         return config;
      }

      private static ReporterDealerGroupsConfig DoNormalMorningFetch(List<DealerGroupName> runThroughDG)
      {
         ReporterDealerGroupsConfig config = new ReporterDealerGroupsConfig();

         //------------------------------------------------------
         // things we will do for the morning fetch
         //------------------------------------------------------
         config.fetchAdvertsGroup = runThroughDG;
         //config.populateCacheTableGroup = runThroughDG;
         config.inStockNotOnPortalGroup = runThroughDG;

         config.countPriceChangesGroup = runThroughDG;

         config.createOptOutsGroup = runThroughDG;
         config.leavingItemsGroup = runThroughDG;
         config.strategyPricesGroup = runThroughDG;
         config.updateDaysToSellGroup = runThroughDG;
         config.competitorInformationGroup = runThroughDG;
         config.generateAutoChangesGroup = runThroughDG;

         //------------------------------------------------------
         //things we optionally will do depending on the DG
         //------------------------------------------------------
         //config.locationOptimiserGroup = runThroughDG;
         //config.localBargainsGroup = runThroughDG;

         config.globalParamsGroup = runThroughDG;
         //------------------------------------------------------
         //things we don't do
         //------------------------------------------------------
         //config.sendEmailsGroup = runThroughDG;
         //config.emailChangesWeekendGroup = runThroughDG;
         config.triggerCacheGroup = runThroughDG;
         return config;
      }

      private static ReporterDealerGroupsConfig DoInitialFetchNewDG(DealerGroupName runThroughDG)
      {
         ReporterDealerGroupsConfig config = new ReporterDealerGroupsConfig();
         config.fetchAdvertsGroup = new List<DealerGroupName>() { runThroughDG };
         config.strategyPricesGroup = new List<DealerGroupName>() { runThroughDG };
         return config;
      }

      private static ReporterDealerGroupsConfig DoInitialFetchNewDGProd(DealerGroupName runThroughDG)
      {
         ReporterDealerGroupsConfig config = new ReporterDealerGroupsConfig();
         config.fetchAdvertsGroup = new List<DealerGroupName>() { runThroughDG };
         return config;
      }
   }

}

