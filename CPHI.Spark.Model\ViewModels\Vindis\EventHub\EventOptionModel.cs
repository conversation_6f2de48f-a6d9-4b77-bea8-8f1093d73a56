﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class EventOptionModel
    {
        public string Id { get; set; }
        
        public string? Code { get; set; }
        
        public string? Provider { get; set; }
        
        public string? Title { get; set; }
        
        public string? Description { get; set; }
        
        public int? Term { get; set; }
        
        public string? OptionTypeId { get; set; }
        
        public decimal GrossPrice { get; set; }

        public decimal NetPrice { get; set; }

        public decimal Discount { get; set; }

        public string? TaxCode { get; set; }
        
        public decimal TaxRate { get; set; }
        
        public Dictionary<string, string> MetaData { get; set; } = new Dictionary<string, string>();
        
        public DateTime? Updated { get; set; }
        
        public bool Included { get; set; }

        public bool Supplementary { get; set; }

        public bool Manual { get; set; }

        public DateTime? Deleted { get; set; }
        
        public bool ValueAddedProduct { get; set; }

        public string? PolicyNo { get; set; }
    }
}
