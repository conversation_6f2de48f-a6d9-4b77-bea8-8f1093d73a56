<div class="flex-card-contents">
  <div class="header-with-buttons">
    <h4>{{ constants.environment.showTodayMap ? constants.translatedText.TodayMap_Title : constants.translatedText.YesterdayMap_Title}}</h4>
   
  </div>

  <div id="map">
    <div #gmap style="width: 100%; height: 100%;"></div>
    <div id="newUsed" class="buttonGroup">
      <button class="btn btn-primary" [ngClass]="{ 'active': department == 'New' }" (click)="chooseDepartment('New')">
        {{ constants.translatedText.New }}
      </button>
      <button class="btn btn-primary" [ngClass]="{ 'active': department == 'Used' }" (click)="chooseDepartment('Used')">
        {{ constants.translatedText.Used }}
      </button>
    </div>
  </div>
</div>
