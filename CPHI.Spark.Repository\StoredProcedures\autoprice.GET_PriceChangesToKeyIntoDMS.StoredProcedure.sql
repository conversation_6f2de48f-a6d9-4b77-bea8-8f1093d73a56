CREATE OR ALTER PROCEDURE [autoprice].[GET_PriceChangesToKeyIntoDMS]
(
	@DealerGroupId int,
	@overrideIsAfter6pm bit = 0
)




  
AS  
BEGIN  

-- Set the first day of the week to Monday
    SET DATEFIRST 1;

DECLARE @today Date = CONVERT(date,getDate());
DECLARE @tomorrowDate Date = DATEADD(day,1,@today);

-------------------------------------
-- Get Auto price changes
-------------------------------------

DECLARE @todayIsAPubHoliday Bit;
IF EXISTS (SELECT 1 FROM PublicHolidays WHERE Date = CONVERT(date, GETDATE()))
BEGIN
    SET @todayIsAPubHoliday = 1;
END
ELSE
BEGIN
    SET @todayIsAPubHoliday = 0;
END

DECLARE @now datetime = GETDATE();
DECLARE @todaysUTCDate datetime = GETUTCDATE();
DECLARE @todaysDateMinus48Hours date = DATEADD(DAY,-2, @today);


WITH latestOptOuts as
(
	SELECT
	optouts.VehicleAdvert_Id,
	optouts.Id,
	ROW_NUMBER() OVER (PARTITION BY optouts.VehicleAdvert_Id ORDER BY optouts.Id desc) AS RowNumber   
	FROM autoprice.VehicleOptOuts optouts
	INNER JOIN autoprice.VehicleAdverts ads on ads.id = optouts.VehicleAdvert_Id
	INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
	WHERE 
	rs.DealerGroup_Id = @DealerGroupId
	AND optouts.CreatedDate > @today
	AND optouts.ActualEndDate > @now
	AND rs.IsActive = 1
)

--Get any Auto Price Changes
SELECT
autos.Id as PriceChangeId,
1 as IsAutoPriceChange,
ads.StockNumber as StockNumber,
rs.Name as SiteName,
autos.NowPrice as NewPrice,
IIF(autos.DateConfirmed IS NULL,1,0) as StillNeedsKeying,
	ads.WebsiteStockIdentifier,
rs.RetailerId as AdvertiserId
FROM autoprice.PriceChangeAutoItems autos
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = autos.VehicleAdvertSnapshot_Id
INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
INNER JOIN Stocks st on st.id = ads.Stock_Id
LEFT JOIN latestOptOuts opts on opts.VehicleAdvert_Id = ads.Id and opts.RowNumber = 1
WHERE snaps.SnapshotDate >= @today AND snaps.SnapshotDate < @tomorrowDate
AND CONVERT(date,autos.CreatedDate) = @today -- price change must be made since the CreatedSince arg
AND rs.DealerGroup_Id = @DealerGroupId -- must be the chosen DG
AND rs.IsActive = 1
--AND ads.AutotraderAdvertStatus = 'PUBLISHED' -- must be a published ad
AND autos.NowPrice <> 0 -- NOT 0 PRICE
AND ads.CreatedInSparkDate < @todaysDateMinus48Hours -- should only be updatable if in AT for more than 2 days
AND 
(
	(
		(autos.NowPrice - COALESCE(autos.WasPrice, 0)) > rs.MinimumAutoPriceIncrease AND 
		(
			COALESCE(autos.WasPrice, 0) = 0 OR
			(
				COALESCE(autos.WasPrice, 0) <> 0 AND
				(autos.NowPrice / NULLIF(COALESCE(autos.WasPrice, 0),0)) -1 >= rs.MinimumAutoPricePercentIncrease
			)
		)
	)  --price change must be sufficiently large
	OR
	(
		(autos.NowPrice - COALESCE(autos.WasPrice, 0)) < rs.MinimumAutoPriceDecrease AND 
		(
			COALESCE(autos.WasPrice, 0) = 0 OR
			(
				COALESCE(autos.WasPrice, 0) <> 0 AND
				(autos.NowPrice / NULLIF(COALESCE(autos.WasPrice, 0),0)) -1 <= rs.MinimumAutoPricePercentDecrease
			)
		)
	)
)
AND ads.VehicleReg IS NOT NULL -- must have a vehicle reg
AND opts.Id IS NULL -- must not be a relevant opt-out
--AND autos.DateConfirmed IS NULL -- must not already be confirmed
AND rs.UpdatePricesAutomatically = 1  --must be set to auto update prices
AND (rs.UpdatePricesPubHolidays = 1 OR @todayIsAPubHoliday = 0)
AND autos.WasOptedOutOfWhenGenerated = 0 --must not have been opted out when generated
AND (   --Must be set to do them on this day of the week
	(DATEPART(weekday, @now) = 1 AND rs.UpdatePricesMon=1) OR
	(DATEPART(weekday, @now) = 2 AND rs.UpdatePricesTue=1) OR
	(DATEPART(weekday, @now) = 3 AND rs.UpdatePricesWed=1) OR
	(DATEPART(weekday, @now) = 4 AND rs.UpdatePricesThu=1) OR
	(DATEPART(weekday, @now) = 5 AND rs.UpdatePricesFri=1) OR
	(DATEPART(weekday, @now) = 6 AND rs.UpdatePricesSat=1) OR
	(DATEPART(weekday, @now) = 7 AND rs.UpdatePricesSun=1)
	)
AND (
		autos.ApprovedDate IS NOT NULL  --Either approved
		OR
		CAST(@todaysUTCDate AT TIME ZONE 'UTC' AT TIME ZONE 'GMT Standard Time' AS time) > CONCAT(rs.WhenToActionChangesEachDay, ':00')  --Or we have gone past the required time
		OR
		@overrideIsAfter6pm = 1
	)

UNION ALL

--Add on any Manual Price Changes that we need to do
SELECT
manuals.Id as PriceChangeId,
0 as IsAutoPriceChange,
ads.StockNumber as StockNumber,
rs.Name as SiteName,
manuals.NowPrice as NewPrice,
IIF(manuals.DateConfirmed IS NULL,1,0) as StillNeedsKeying,
ads.WebsiteStockIdentifier,
rs.RetailerId as AdvertiserId

FROM autoprice.PriceChangeManualItems manuals
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.Id = manuals.VehicleAdvertSnapshot_Id
INNER JOIN autoprice.VehicleAdverts ads on ads.Id = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
INNER JOIN Stocks st on st.id = ads.Stock_Id
WHERE CONVERT(date,manuals.CreatedDate)  = @today
AND rs.IsActive = 1
AND rs.DealerGroup_Id = @DealerGroupId -- correct DG
AND rs.UpdatePricesAutomatically = 1 --must be set to auto update prices
AND manuals.NowPrice <> 0 -- NOT 0 PRICE
AND ads.CreatedInSparkDate < @todaysDateMinus48Hours -- should only be updatable if in AT for more than 2 days
AND ( --Must be set to do them on this day of the week
	(DATEPART(weekday, @now) = 1 AND rs.UpdatePricesMon=1) OR
	(DATEPART(weekday, @now) = 2 AND rs.UpdatePricesTue=1) OR
	(DATEPART(weekday, @now) = 3 AND rs.UpdatePricesWed=1) OR
	(DATEPART(weekday, @now) = 4 AND rs.UpdatePricesThu=1) OR
	(DATEPART(weekday, @now) = 5 AND rs.UpdatePricesFri=1) OR
	(DATEPART(weekday, @now) = 6 AND rs.UpdatePricesSat=1) OR
	(DATEPART(weekday, @now) = 7 AND rs.UpdatePricesSun=1)
	)
--AND manuals.DateConfirmed IS NULL -- must not already be confirmed
AND ads.VehicleReg IS NOT NULL -- must have a vehicle reg

END
GO