import {BIChartTileDataType, TileType} from '../components/biChartTile/biChartTile.component';
import {VNTileTableRow} from './VNTileTableRow';
import {WasNowEnum} from '../pages/autoprice/leavingVehicles/leavingVehicleWasNowEnum';


export interface VNTileParentMethods {
   buildRows: (fieldName: string, dataType: BIChartTileDataType, tileType: TileType, wasNow?: WasNowEnum) => VNTileTableRow[];
   highlightRow: (row: VNTileTableRow, fieldName: string, wasNow?: WasNowEnum) => void;
   provideItemsList: (fieldName: string, isDateField: boolean) => string[];
}
