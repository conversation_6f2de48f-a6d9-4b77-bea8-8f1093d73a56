<div class="dashboard-tile-inner">
  <div class="tileHeader">
    <div class="headerWords">
      <span *ngIf="dept==='New'">
        <h4>{{constants.translatedText.Dashboard_NewDealsBreakdown}} </h4>
      </span>
      <span *ngIf="dept==='Fleet'">
        <h4>{{constants.translatedText.Dashboard_FleetDealsByType}} </h4>
      </span>
      <span *ngIf="dept==='Used'">
        <h4>{{constants.translatedText.Dashboard_UsedDealsByType}} </h4>
      </span>
    </div>
  </div>

  <div class="salesSplitInner">

    <!-- New Department -->
    <ng-container *ngIf="dept=='New'">
      <!-- Brought in   -->
      <div (click)="navigateToOrderBook('Brought In')" class=" spaceBetween clickable typeAndValue">
        <div class="h4Thin  label">{{constants.translatedText.BroughtIn}}</div>
        <div class="h4Thin value">{{data.NewBroughtIn|cph:'number':0}}</div>
      </div>

      <!-- Done in month -->
      <div (click)="navigateToOrderBook('Done In Month')" class=" spaceBetween clickable typeAndValue">
        <div class="h4Thin label">{{constants.translatedText.DoneInMonth}}</div>
        <div class="h4Thin value">{{data.NewDoneInMonth|cph:'number':0}}</div>
      </div>


      <!-- Total -->
      <div (click)="navigateToOrderBook()" class="total spaceBetween clickable typeAndValue">
        <div class="h4Thin label">{{constants.translatedText.TotalNew}}</div>
        <div class="h4Thin value">{{data.TotalNew|cph:'number':0}}</div>
      </div>

    </ng-container>

    <!-- Used Department -->
    <ng-container *ngIf="dept=='Used'">


      <!-- Core Used  -->
      <div (click)="navigateToOrderBook('CoreUsed')" class=" spaceBetween clickable typeAndValue">
        <div class="h4Thin label">{{constants.translatedText.CoreUsed}}</div>
        <div class="h4Thin value">{{data.UsedCoreUsed|cph:'number':0}}</div>
      </div>
      <!-- Demo  -->
      <div (click)="navigateToOrderBook('Demo')" class=" spaceBetween clickable typeAndValue">
        <div class="h4Thin label">{{constants.translatedText.Demo}}</div>
        <div class="h4Thin value">{{data.UsedDemo|cph:'number':0}}</div>
      </div>

      <ng-container *ngIf="!constants.environment.departmentDealBreakDown_Vindis">
        <!-- ExDemo  -->
        <div (click)="navigateToOrderBook('ExDemo')" class=" spaceBetween clickable typeAndValue">
          <div class="h4Thin label">{{constants.translatedText.ExDemo}}</div>
          <div class="h4Thin value">{{data.UsedExDemo|cph:'number':0}}</div>
        </div>
        <!-- ExManagement  -->
        <div (click)="navigateToOrderBook('ExManagement')" class=" spaceBetween clickable typeAndValue">
          <div class="h4Thin label">{{constants.translatedText.ExManagement}}</div>
          <div class="h4Thin value">{{data.UsedExManagement|cph:'number':0}}</div>
        </div>
        <!-- Tactical  -->
        <div (click)="navigateToOrderBook('Tactical')" class=" spaceBetween clickable typeAndValue">
          <div class="h4Thin label">{{constants.translatedText.Tactical}}</div>
          <div class="h4Thin value">{{data.UsedTactical|cph:'number':0}}</div>
        </div>
      </ng-container>


      <!-- Total -->
      <div (click)="navigateToOrderBook()" class="total clickable spaceBetween typeAndValue">
        <div class="h4Thin label">{{constants.translatedText.TotalUsed}}</div>
        <div class="h4Thin value">{{data.TotalUsed|cph:'number':0}}</div>
      </div>

    </ng-container>




    <!-- Fleet Department RRG -->
    <ng-container *ngIf="dept=='Fleet' && constants.environment.departmentDealBreakDown_RRG">
      <!-- Fleet  -->
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin label">{{constants.translatedText.Fleet}}</div>
        <div class="h4Thin value">{{data.FleetFleet|cph:'number':0}}</div>
      </div>
      <!-- Fleet local  -->
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin label">{{constants.translatedText.FleetLocal}}</div>
        <div class="h4Thin value">{{data.FleetFleetlocal|cph:'number':0}}</div>
      </div>

      <!-- Fleet st  -->
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin label">{{constants.translatedText.FleetSt}}</div>
        <div class="h4Thin value">{{data.FleetFleetSt|cph:'number':0}}</div>
      </div>

      <!-- Total -->
      <div (click)="navigateToOrderBook()" class="total clickable spaceBetween typeAndValue">
        <div class="h4Thin  label">{{constants.translatedText.TotalFleet}}</div>
        <div class="h4Thin  value">{{data.TotalFleet|cph:'number':0}}</div>
      </div>

    </ng-container>


    <!-- Fleet Department Vindis -->
    <ng-container *ngIf="dept=='Fleet' && constants.environment.departmentDealBreakDown_Vindis">
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">Agency VWFS Agency</div>
        <div class="h4Thin  value">{{data.FleetAgencyVWFSAgency|cph:'number':0}}</div>
      </div>
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">New Agency</div>
        <div class="h4Thin  value">{{data.FleetNewAgency|cph:'number':0}}</div>
      </div>

      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">New Direct</div>
        <div class="h4Thin  value">{{data.FleetNewDirect|cph:'number':0}}</div>
      </div>

      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">New Fleet</div>
        <div class="h4Thin  value">{{data.FleetNewFleet|cph:'number':0}}</div>
      </div>

      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">New LM Agency</div>
        <div class="h4Thin  value">{{data.FleetNewLMAgency|cph:'number':0}}</div>
      </div>
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">New National Account</div>
        <div class="h4Thin  value">{{data.FleetNewNationalAccount|cph:'number':0}}</div>
      </div>
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">New RFO Fleet</div>
        <div class="h4Thin  value">{{data.FleetNewRFOFleet|cph:'number':0}}</div>
      </div>
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">New RFO Rental</div>
        <div class="h4Thin  value">{{data.FleetNewRFORental|cph:'number':0}}</div>
      </div>
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">New VWFS Agency</div>
        <div class="h4Thin  value">{{data.FleetNewVWFSAgency|cph:'number':0}}</div>
      </div>
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">Fleet Corporate</div>
        <div class="h4Thin  value">{{data.FleetCorporate|cph:'number':0}}</div>
      </div>
      <div class=" spaceBetween typeAndValue">
        <div class="h4Thin  label">Fleet Commercial</div>
        <div class="h4Thin  value">{{data.FleetCommercial|cph:'number':0}}</div>
      </div>

      <!-- Total -->
      <div (click)="navigateToOrderBook()" class="total clickable spaceBetween typeAndValue">
        <div class="h4Thin  label">{{constants.translatedText.TotalFleet}}</div>
        <div class="h4Thin  value">{{data.TotalFleet|cph:'number':0}}</div>
      </div>

    </ng-container>

  </div>
</div>