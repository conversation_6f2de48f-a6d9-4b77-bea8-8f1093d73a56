import { Component, OnInit, ViewChild, ElementRef, Input, SimpleChanges } from "@angular/core";
import { CphPipe } from '../cph.pipe';
import { ConstantsService } from '../services/constants.service';
import { SelectionsService } from '../services/selections.service';
import { Chart, registerables } from 'chart.js';
Chart.register(...registerables);

@Component({
  selector: 'runChaseChart',
  template:
    `
    <div id="chartHolder">
     <canvas id="chartCanvas" #chartCanvas>
    </canvas></div>
    `
  ,
  styles: [`
    #chartHolder{width:31em;margin:0.3em auto 0em auto;position:relative;height:27em;} 
    @media (min-width: 0px) and (max-width: 1920px) {
      #chartHolder{width:25em;height:20em;} 
    }
   canvas{}
    `]
})
export class RunChaseChartComponent implements OnInit {
  @ViewChild('chartCanvas', { static: true }) chartCanvas: ElementRef;
  @Input() public labels: number[];
  @Input() public dataPointSets: number[][];


  percentageDone: number;
  percentageTarget: any;
  //myChart1: any;
  myChartObject: any;


  constructor(

    public selections: SelectionsService,
    public constants: ConstantsService,
    public cphPipe: CphPipe,
  ) {

  }

  params: Date;



  ngOnInit(): void {
    this.makeChart()

  }

  makeChart(){
    if (this.myChartObject) {
      this.myChartObject.destroy()
    }
    this.myChartObject = this.createChart();
  }







  createChart(): any {

    let chartLabels = this.labels.map(x => x.toString());

    let dataSets = [];

    let chartColours = []
    chartColours.push(this.constants.actualColour);
    chartColours.push(this.constants.backgroundColours[2]);

    this.dataPointSets.forEach((set, i) => {
      dataSets.push(
        { backgroundColor: "rgba(255,255,255,0)", pointRadius: 0, borderWidth: 4, borderColor: chartColours[i], data: set, hoverBackgroundColor: chartColours[i] }
      )
    })

    dataSets[0].label = this.constants.translatedText.Done;
    dataSets[1].label = this.constants.translatedText.Target;


    //project roughly to see where to set the y-axis max.
    let nonNullDataPoints = this.dataPointSets[0].filter(x => x !== null);
    let projectedFinishBasedOnActuals = this.constants.div(this.dataPointSets[0].length, nonNullDataPoints.length) * nonNullDataPoints[nonNullDataPoints.length - 1] * 1.1;

    let yAxisMax = Math.max(projectedFinishBasedOnActuals, this.dataPointSets[1][this.dataPointSets[1].length - 1]); //greater of projected or target

    let config = {
      type: 'line',
      data: {
        labels: chartLabels,
        datasets: dataSets,
      },
      options: {
        maintainAspectRatio: false,
        responsive: true,
        legend: { display: false, position: 'bottom' },
        title: {
          display: false,
          text: ''
        },
        tooltips: {
          enabled: true,
          mode: 'index',
          fontSize: 12,
          intersect: false,
          callbacks: {
            title: (tooltipItem, data) => {
              //let month = this.selections.serviceSummary.month.name;
              return null

            },
            label: (tooltipItem, data) => {
              if (!tooltipItem.yLabel) { return; }
              let text = data.datasets[tooltipItem.datasetIndex].label;
              let value = this.cphPipe.transform((tooltipItem.yLabel), 'currency', 0);
              return text + ': ' + value;
            }
          }
        },
        hover: {
          mode: 'nearest',
          intersect: true
        },
        scales: {
          xAxes: [{
            display: true,
            ticks: { autoSkip: false, fontSize: 9 },
            scaleLabel: {
              display: false,
              labelString: 'Month'
            }
          }],
          yAxes: [{
            ticks: {
              min: 0,
              max: yAxisMax,
              fontSize: 12,

              callback: (value, index, values) => {
                let suffix = ''
                return value;
              }
            },
            display: false,
            scaleLabel: {
              display: false,
              labelString: 'Value'
            }
          }]
        }
      }
    };

  }


}








