<nav class="navbar">

    <nav class="generic">
        <h4 id="pageTitle">

           <h4>
            Stock Reports 
            <sourceDataUpdate *ngIf="!constants.environment.showLatestSnapshotDate"
                [chosenDate]="constants.LastUpdatedDates.AutotraderAdverts"></sourceDataUpdate>
           </h4>
            <!-- <button (click)="test()">test</button>
            <button (click)="test1()">test1</button>
            <button (click)="test2()">test2</button>  -->



        </h4>

        <input class="mx-2" type="date" [value]="service.chosenDate" (change)="setChosenDate($event)">

        <div *ngIf="service.viewByBulkUpload && service.chosenVehicleValuationBatch" ngbDropdown dropright
            class="d-inline-block">
            <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>
                Uploaded {{ service.chosenVehicleValuationBatch.LastRunDate | cph:'dateTime':0 }} by
                {{ service.chosenVehicleValuationBatch.LastRunBy }} ({{
                service.chosenVehicleValuationBatch.TotalVehicles }} vehicles)
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                <button *ngFor="let batch of service.vehicleValuationBatches" ngbDropdownToggle
                    class="manualToggleCloseItem" ngbDropdownItem (click)="selectBatch(batch)">
                    Uploaded {{ batch.LastRunDate | cph:'dateTime':0 }} by {{ batch.LastRunBy }} ({{ batch.TotalVehicles
                    }} vehicles)
                </button>
            </div>
        </div>

        <!-- Show / hide new vehicles -->
        <sliderSwitch text="New Vehicles" (toggle)="toggleIncludeNewVehicles()"
            [defaultValue]="service.includeNewVehicles">
        </sliderSwitch>

        <!-- Include unpublished -->
        <sliderSwitch text="Un-published Vehicles" (toggle)="toggleIncludeUnPublishedAds()"
            [defaultValue]="service.includeUnPublishedAds">
        </sliderSwitch>

           <!-- Vehicle type picker -->
           <multiPickerWithCount *ngIf="service.tableLayoutManagementParams.gridApi && constants.autopriceEnvironment.vehicleTypes != null" [label]="'Vehicle Types'"
           [menuItems]="constants.autopriceEnvironment.vehicleTypes" [chosenItems]="service.chosenVehicleTypes"
           [itemCount]="provideVehTypeCount.bind(this)"
           [onChosenItemsChange]="onChosenVehTypesChange.bind(this)"></multiPickerWithCount>


        <!-- Lifecycle status picker -->
        <multiPickerWithCount *ngIf="service.tableLayoutManagementParams.gridApi" [label]="'Lifecycle Statuses'"
            [menuItems]="service.allLifecycleStatuses" [chosenItems]="service.chosenLifecycleStatuses"
            [itemCount]="provideLifecyleCount.bind(this)"
            [onChosenItemsChange]="onChosenLifeCycleStatusesChange.bind(this)"></multiPickerWithCount>

         


    </nav>

    <nav class="pageSpecific">




    </nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
    <div class="content-new">
        <div class="content-inner-new">
            <div *ngIf="!service.viewByBulkUpload" class="d-flex flex-column h-100">
                <instructionRow
                    [message]="infoMessage()">
                </instructionRow>
                <advertListingDetailTable *ngIf="service.vehicleAdvertsRowData"></advertListingDetailTable>
            </div>
        </div>
    </div>
</div>