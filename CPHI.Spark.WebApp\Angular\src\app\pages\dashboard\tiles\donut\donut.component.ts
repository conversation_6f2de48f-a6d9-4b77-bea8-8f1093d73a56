import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from '@angular/router';
import { Subscription } from "rxjs";
import { DealsForTheMonthService } from "src/app/pages/dealsForTheMonth/dealsForTheMonth.service";
import { Department, MenuItemNew, OrderbookTimePeriod, SiteVM } from '../../../../model/main.model';
import { ChartService } from '../../../../services/chart.service';
import { ConstantsService } from '../../../../services/constants.service';
import { SelectionsService } from '../../../../services/selections.service';
import { OrderBookService } from "../../../orderBook/orderBook.service";
import { DonutData, DonutMonthlyData } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";

import { Chart, ChartConfiguration, ChartDataset, ChartDatasetCustomTypesPerDataset, registerables } from 'chart.js';

Chart.register(...registerables);

@Component({
  selector: 'donutTile',
  templateUrl: './donut.component.html',
  styleUrls: ['./donut.component.scss']
})
export class DonutTileComponent implements OnInit {

  @ViewChild('myChart', { static: true }) myChart: ElementRef;
  @Input() public departmentName: string;
  @Input() public timePeriod: 'Yesterday'|'Week'|'Month';
  @Input() public data: DonutData | DonutMonthlyData ;
  @Input() public newDataEmitter: EventEmitter<void>;
  @Input() public dataSource: string;

  actualWidth: number;
  budgetWidth: number;

  //data: DonutData | DonutMonthlyData;  //monthly donut gets the monthly version which includes vs last month etc.
  subscription: Subscription;

  constructor(
    public selections: SelectionsService,
    public dealsForTheMonthService: DealsForTheMonthService,
    public constants: ConstantsService,
    public chart: ChartService,
    public router: Router,
    public service: DashboardService,
    public orderBookService: OrderBookService,


  ) {

  }

  ngOnInit(): void {

    this.initParams();
    this.subscription = this.newDataEmitter.subscribe(res => {
      this.initParams();
      this.makeDonut();
    })

  }


  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
  }



  initParams() {
    //this.data = this.data.find(x => x.Department == this.departmentName)
    if(!this.data){
      this.data = {
        Department: this.departmentName,
        ActualUnits: 0,
        ActualMargin: 0,
        TargetUnits: 0,
        TargetMargin: 0,
        ActualUnitsLastYear: 0,
        ActualUnitsLastMonth:0
      }
    }
    
  }


  isClickableHeader(){
    return this.timePeriod === 'Month'
  }


  navigateToDealsForTheMonth() {
    if(!this.isClickableHeader()){return;}

    const department: Department = this.constants.departments.find(x => x.ShortName === this.departmentName);
    this.dealsForTheMonthService.initParams();

    let sites: SiteVM[] = this.constants.clone(this.service.chosenSites);
    this.selections.selectedSites = sites;
    this.selections.selectedSitesIds = this.service.chosenSites.map(x => x.SiteId)
    this.dealsForTheMonthService.vehicleTypeTypes = this.constants.clone(department.VehicleTypeTypes);
    let orderTypeTypes: string[] = department.OrderTypeTypes.filter(x => x !== 'Trade' && x !== 'Auction')
    this.dealsForTheMonthService.orderTypeTypes = orderTypeTypes;
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading })

    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/dealsForTheMonth');
    if (menuItem) { this.constants.navigateByUrl(menuItem); }  //, 'operationreports'
  }




  navigateToOrderBook() {

    const department: Department = this.constants.departments.find(x => x.ShortName === this.departmentName);

    let orderTypes: string[];

    if(this.constants.environment.orderTypePicker_rrgSpainSettings)
    {
      
      if(this.departmentName == 'Used')
      {
        orderTypes = this.constants.orderTypeTypes.filter(x => x != 'Inmovilizado' && x != 'Desguaces' && x != 'Cesión' && x != 'Cessiones')
      }
      else
      {
        orderTypes = this.constants.orderTypeTypes;
      }

    }
    else
    {
      orderTypes = department.OrderTypeTypes.filter(x => x !== 'Trade' && x !== 'Auction');
    }

    let vehicleTypeTypes: string[] = this.constants.clone(department.VehicleTypeTypes);

    this.orderBookService.initOrderbook();

    this.selections.selectedSites = this.service.chosenSites;
    this.selections.selectedSitesIds = this.service.chosenSites.map(x => x.SiteId);
    this.orderBookService.franchises = this.service.franchises;

    this.orderBookService.orderTypeTypes = orderTypes;
    this.orderBookService.vehicleTypeTypes = vehicleTypeTypes;

    const startDate = this.service.chosenMonthStart ? this.service.chosenMonthStart : this.constants.thisMonthStart;
    this.orderBookService.accountingDate.startDate = startDate;
    this.orderBookService.accountingDate.endDate = this.constants.endOfMonth(startDate);

    if(this.timePeriod ===  'Yesterday'){
      this.orderBookService.orderDate.startDate = this.constants.yesterdayStart;
      this.orderBookService.orderDate.endDate = this.constants.yesterdayStart;
      this.orderBookService.orderDate.lastChosenDay = this.constants.yesterdayStart
      this.orderBookService.orderDate.timePeriod = OrderbookTimePeriod.Day
      
      this.orderBookService.accountingDate.endDate = this.constants.addYears(this.constants.appStartTime,2)
      this.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Anytime
    }

    if(this.timePeriod === 'Week'){
      this.orderBookService.orderDate.startDate = this.constants.thisWeekStartDate;
      this.orderBookService.orderDate.endDate = this.constants.thisWeekEndDate;
      this.orderBookService.orderDate.timePeriod = OrderbookTimePeriod.Week;
      this.orderBookService.orderDate.lastChosenWeekStart = this.constants.thisWeekStartDate;

      this.orderBookService.accountingDate.endDate = this.constants.addYears(this.constants.appStartTime,2)
      this.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Anytime
    }

    this.orderBookService.salesExecName = null;
    this.orderBookService.salesExecId = null;

    this.orderBookService.showOrderbook();

  }


  makeDonut() {

    let budgetProfitGap = Math.max(0, this.data.TargetMargin - this.data.ActualMargin);

    if (budgetProfitGap < 0) {
      //more than past target
      this.actualWidth = 100;
      this.budgetWidth = 0;
    } else if (budgetProfitGap >= this.data.TargetMargin) {
      this.actualWidth = 0;
      this.budgetWidth = 100;
    } else {
      this.actualWidth = this.data.ActualMargin / this.data.TargetMargin * 100;
      this.budgetWidth = budgetProfitGap / this.data.TargetMargin * 100;
    }

    // Generate the chart
    let chartElement = (<HTMLCanvasElement>this.myChart.nativeElement);
    this.chart.createDonut(chartElement, this.data.ActualUnits, this.data.TargetUnits)
  }

  get showWarning(): boolean {
    const now = new Date();
    let origin: string = 'Quiter';

    const hasStaleEntries = this.service.dataOriginUpdates
      .filter(entry => entry.DataOrigin.includes(origin))
      .some(entry => {
        const hoursSinceUpdate = (now.getTime() - new Date(entry.LastUpdate).getTime()) / (1000 * 60 * 60);
        return hoursSinceUpdate > 24;
      });
  
    return hasStaleEntries;
  }
}


