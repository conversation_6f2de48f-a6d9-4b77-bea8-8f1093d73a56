<nav class="navbar">

  <nav class="generic" >
    <h4 id="pageTitle">
      <div >
        {{service.constants.translatedText.PerformanceLeague_Title}}
        <sourceDataUpdate [chosenDate]="service.constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <div class="buttonGroup topDropdownButtons" id="performanceLeague">

          <!-- FOR SELECTING DEPARTMENTS -->
      <div class="buttonGroup">
        <!-- dropdownMonth -->
        <div ngbDropdown class="d-inline-block" #dropdown="ngbDropdown">
          <button  class="btn btn-primary " id="pickDepartments"
            ngbDropdownToggle>{{getChosenDepartments()}}</button>
          
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

            <!-- the ngFor buttons -->
            <button [ngClass]="{'active':d.isSelected}" *ngFor="let d of service.departments"
              (click)="d.isSelected = !d.isSelected" class="btn btn-primary" ngbDropdownItem
              >{{d.label}}</button>

              <button ngbDropdownItem  class="btn btn-primary" (click)="okDepartmentsOnClick(dropdown)"> OK </button>

          </div>
        </div>
      </div>



      <!-- FOR SELECTING YEAR MONTH -->
      <div class="buttonGroup">

        <!-- Date selector -->
        <datePickerMultiSelect
          [monthsFromParent]="service.months"
          (selectMonths)="updateMonths($event)"
          [includeYTD]="true"
          [includeLastYear]="true"
          [includeThisYear]="true"
        >
        </datePickerMultiSelect> 
        
      </div>

      <div class="buttonGroup">

        <!-- For toggling show profit -->
        <button class="btn btn-primary" 
          (click)="service.showProfit = !service.showProfit"
          [ngClass]="{'active':service.showProfit}">{{service.constants.translatedText.PerformanceLeague_ShowProfit}}</button>

        <!-- For toggling show delivered -->
        <button class="btn btn-primary"  *ngIf="service.constants.environment.performanceLeague.showDeliveredButton"
          (click)="service.showDelivered = !service.showDelivered"
          [ngClass]="{'active':service.showDelivered}">{{service.constants.translatedText.PerformanceLeague_ShowDelivered}}</button>
        </div>

      <div *ngIf="service.constants.environment.performanceLeague.showExecAndManagerSelector">
        <span class="rsmToggleLabel">Exec</span>
        <label class="switch">
              <input type="checkbox" [checked]="!service.isSalesExecView" (click)="toggle()" id="checkbox">
              <span class="slider round"></span>
          </label>
          <span class="rsmToggleLabel">RSM</span>
        </div>
      

      <!-- Choose sort by -->
      <div ngbDropdown class="d-inline-block" id="sortByPicker" [autoClose]="true">

        <button class="btn btn-primary " ngbDropdownToggle>{{service.constants.translatedText.Common_SortBy}}:
          {{service.sortBy.description}}</button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <!-- the ngFor buttons -->
          <button *ngFor="let sortBy of service.sortBys" (click)="selectSortByOnClick(sortBy)"
            ngbDropdownItem>{{sortBy.description}}</button>

        </div>
      </div>

    </div>

  </nav>

</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constants.environment.customer">
  <div class="content-new">

  <div class="content-inner-new"
    [ngClass]="{'accessAllSites': service.selections.user.AccessAllSites && !service.nonConsecutiveMonthsSelected, 
                'showMySitePeople': false }">

    <div class="d-flex">

      <!-- League select -->
      <div class="buttonGroup" id="leagueChoiceButtonGroup">
        <button class="btn btn-primary" (click)="selectLeagueOnClick(league)"
          [ngClass]="{'active':service.league.label==league.label}"
          *ngFor="let league of service.leagues">
          {{league.label}}
        </button>
      </div>

      

      <!-- Cards -->
      <div id="leagueContainer" [ngClass]="service.league?.label" *ngIf="service.league">


        <div class="cardOuter" *ngFor="let person of service.league.people; let i = index" >

                <div class="card boxShadowSubtle" [ngClass]="getCardClass(person)" (click)="showPersonDealsOnClick(person)">

                  <div class="rank">{{service.constants.ordinalSuffix(i+1+service.league.skipCount)}}</div>
                  <div class="picture" *ngIf="service.league.label!=='Bronze'">
                    <profilePicImage  [personId]="person.Id" [size]="service.profilePicSize"></profilePicImage>
                  </div>
                  <div class="nameAndSite">
                    <!-- <span *ngIf="person.HasLeft">(Left)</span> -->
                    <div class="name">{{person.Name}} </div>
                    <div class="site">{{person.CurrentSite}}</div>
                  </div>

                  <div title="Financed" class="financePen" *ngIf="!service.constants.environment.performanceLeague.hideBadges">

                    <div class="bubble finance">
                      <i class="fas fa-coins "></i>
                      <div class="measure">{{person.financePen|cph:'percent':0}}</div>
                    </div>

                  </div>


                  <div class="products" *ngIf="!service.constants.environment.performanceLeague.hideBadges">

                    <div title="Cosmetic Insurance" class="bubble addOn boxShadowSubtle" *ngIf="person.cosmeticPen>0.2">
                      <i class="  far fa-shield-check"></i>
                      <div class="measure">{{person.cosmeticPen|cph:'percent':0}}</div>
                    </div>

                    <div title="Paint Protection" class="bubble addOn boxShadowSubtle" *ngIf="person.paintProtectionPen>0.2">
                      <i class="fas fa-claw-marks  "></i>
                      <div class="measure">{{person.paintProtectionPen|cph:'percent':0}}</div>
                    </div>


                    <div title="Service Plan" class="bubble addOn boxShadowSubtle" *ngIf="person.servicePen>0.2">
                      <i class="fas fa-car-mechanic "></i>
                      <div class="measure">{{person.servicePen|cph:'percent':0}}</div>
                    </div>


                    <div title="Gap Insurance" class="bubble addOn boxShadowSubtle" *ngIf="person.gapPen>0.2">
                      <i class="fas fa-car-crash  "></i>
                      <div class="measure">{{person.gapPen|cph:'percent':0}}</div>
                    </div>

                    <div title="Warranty" class="bubble addOn boxShadowSubtle" *ngIf="person.warrantyPen>0.2">
                      <i class="fas fa-file-certificate  "></i>
                      <div class="measure">{{person.warrantyPen|cph:'percent':0}}</div>
                    </div>

                  </div>

                  <div class="PPU" *ngIf="!service.constants.environment.performanceLeague.hideBadges">

                    <!-- If chosen to show delivery status -->
                    <div *ngIf="service.showDelivered">

                      <!-- Any non-bronze league -->
                      <span *ngIf="service.league.label!=='Bronze'">
                        <span class="deliveredCount">{{person.deliveredCount}}</span> {{ service.constants.translatedText.Whiteboard_Delivered }},
                      </span>

                      <!-- Any bronze leage -->
                      <span *ngIf="service.league.label=='Bronze'">
                        <span class="deliveredCount">{{person.deliveredCount}}</span>
                        <span class="visibleBelowLg"> del,</span>
                        <span class="visibleAboveMd"> {{ service.constants.translatedText.Whiteboard_Delivered }}, </span>
                      </span>

                      <span class="toGoCount">{{person.toGoCount}}</span> {{ service.constants.translatedText.Common_ToGo }}
                    </div>

                    <div *ngIf="!service.showDelivered">
                      <!-- If not chosen delivery, show PPU as usual -->
                      <span *ngIf="service.league.label!=='Bronze'"> {{ service.constants.translatedText.Common_Products }} PU: </span>
                      <span *ngIf="service.league.label=='Bronze'">
                        <span class="visibleBelowLg"> PPU: </span>
                        <span class="visibleAboveMd"> {{ service.constants.translatedText.Common_Products }} PU: </span>
                      </span>

                      {{person.productsPU|cph:'number':1}}
                    </div>

                  </div>

                  <div class="dealCountAndProfit">

                    <div class="dealCount h3">
                      {{person.dealCount}}
                    </div>
                    <div class="profit" *ngIf="service.showProfit">
                      {{person.profit|cph:'currency':0}}
                    </div>
                  </div>


                </div>


              </div>


      </div>

      
    </div>


  </div>

  </div>
</div>