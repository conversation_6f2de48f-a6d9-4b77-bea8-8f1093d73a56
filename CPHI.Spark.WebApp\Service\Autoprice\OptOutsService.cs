﻿using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using CPHI.Repository;
using System.Linq;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using Microsoft.IdentityModel.Tokens;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
   public interface IOptOutsService
   {
      Task CreateOrUpdateVehicleOptOut(VehicleOptOutParams optOutParams, Model.DealerGroupName dealerGroup, string usersName);
      Task<IEnumerable<VehicleOptOutSummaryItemNew>> GetVehicleOptOuts(GetOptOutsParams parms);
      Task<VehicleOptOutStatus> GetVehicleOptOutStatus(int vehicleAdvertId, Model.DealerGroupName dealerGroup);
   }

   public class OptOutsService : IOptOutsService
   {
      private readonly IUserService userService;
      private readonly IConfiguration configuration;
      private readonly string _connectionString;
      private readonly IAutoPriceCache autoPriceCache;

      public OptOutsService(
          IUserService userService, IConfiguration configuration
, IAutoPriceCache autoPriceCache)
      {
         this.userService = userService;
         this.configuration = configuration;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         _connectionString = configuration.GetConnectionString(dgName);
         this.autoPriceCache = autoPriceCache;
      }



      //OptOuts
      public async Task<IEnumerable<VehicleOptOutSummaryItemNew>> GetVehicleOptOuts(GetOptOutsParams parms)
      {
         int userDealerGroupId = (int)userService.GetUserDealerGroupName();
         IEnumerable<int> retailerSiteIds = userService.GetUserRetailerSiteIds();

         if (parms.RetailerSiteIds.Count() > 0)
         {
            retailerSiteIds = retailerSiteIds.Intersect(parms.RetailerSiteIds);
         }

         if(parms.LifeCycleStatuses.IsNullOrEmpty())
         {
            parms.LifeCycleStatuses = null;
         }

         var parmsForSP = new GetVehicleOptOutSummaryItemsParams()
         {
            RetailerSiteIds = retailerSiteIds.ToList(),
            ChosenDate = parms.ChosenDate.Date,
            IncludeNewVehicles = parms.IncludeNewVehicles,
            IncludeUnPublishedAds = parms.IncludeUnPublishedAds,
            LifeCycleStatuses = parms.LifeCycleStatuses != null ? string.Join(",", parms.LifeCycleStatuses) : null,
            DealerGroupId = (int)userDealerGroupId
         };

         var optOutsDataAccess = new OptOutsDataAccess(_connectionString);
         var optOuts = (await optOutsDataAccess.GetVehicleOptOutSummaryItems(parmsForSP)).ToList();

         return optOuts;
      }
      public async Task<VehicleOptOutStatus> GetVehicleOptOutStatus(int vehicleAdvertId, Model.DealerGroupName dealerGroup)
      {
         var optOutsDataAccess = new OptOutsDataAccess(_connectionString);
         return await optOutsDataAccess.GetVehicleOptOutStatus(vehicleAdvertId, dealerGroup);
      }

      public async Task CreateOrUpdateVehicleOptOut(VehicleOptOutParams optOutParams, Model.DealerGroupName dealerGroup, string usersName)
      {
         var optOutsDataAccess = new OptOutsDataAccess(_connectionString);
         var existingStatus = await optOutsDataAccess.GetVehicleOptOutStatus(optOutParams.VehicleAdvertId, dealerGroup);
         int userId = userService.GetUserId();

         if (existingStatus == null)
         {
            //don't have one already, make a new one
            var newOptOut = new VehicleOptOut()
            {
               Person_Id = userId,
               VehicleAdvert_Id = optOutParams.VehicleAdvertId,
               CreatedDate = DateTime.Now,
               OriginalEndDate = optOutParams.EndDate,
               ActualEndDate = optOutParams.EndDate,
            };
            await optOutsDataAccess.CreateVehicleOptOut(newOptOut);
         }
         else
         {
            //update the existing one
            await optOutsDataAccess.UpdateVehicleOptOut(existingStatus.VehicleOptOutId, optOutParams.EndDate, userId, dealerGroup);
         }

         //now update what is stored in the cache
         bool isNowOptedOut = optOutParams.EndDate > DateTime.Now;
         autoPriceCache.UpdateCacheOptOut(DateTime.Now.Date, dealerGroup, optOutParams.VehicleAdvertId, isNowOptedOut, usersName);
      }



   }
}