﻿using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using System.IO;
//using Microsoft.Extensions.Primitives;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess;
using CPHI.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using UserDataAccess = CPHI.Spark.DataAccess.UserDataAccess;
using CPHI.Spark.WebApp.Caches;
using StockPulse.WebApi.Model.ViewModels;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.JsonPatch.Internal;
using System.Configuration;
using CPHI.Spark.Model.Services;

namespace CPHI.Spark.WebApp.Service
{
   public interface IUserService
   {
      Task<int> CreatePerson(UserAndLogin user, Model.DealerGroupName dealerGroup);
      Task DeleteUsersAndLogins(int userId, string idToDelete);
      Task<IEnumerable<UserAndLogin>> GetAllUsersAndLoginsNew();
      int[] GetEligibleSitesIds();
      Task<Helpers.Languages?> GetLanguage(int linkedPersonId, Model.DealerGroupName dealerGroup);
      List<UserImage> GetProfileImageURL(List<int> userIds);
      IEnumerable<Claim> GetUserClaims();
      string GetUserCommissionAuthority();
      string GetUserConnectionString();
      Model.DealerGroupName GetUserDealerGroupName();
      int GetUserId();
      string GetUserName();
      Task<string> GetUserName(string email, int dealerGroupId);
      IEnumerable<int> GetUserRetailSiteIds();
      Task<IEnumerable<RetailerSite>> GetUserRetailSites(Model.DealerGroupName dealerGroup);
      IEnumerable<int> GetUserSiteIds();
      Task<IEnumerable<string>> GetUserSitesNameFromDatabase(int userId, Model.DealerGroupName dealerGroup);
      Task<string> GetUsersNameFromDatabase(int linkedPersonId, Model.DealerGroupName dealerGroup);
      Task SetDBContextConnectionString(string username, int dealerGroupId);
      Task UpdateLanguage(int linkedPersonId, Helpers.Languages language, Model.DealerGroupName dealerGroup);
      Task<string> UploadProfileImage(int userId, string imageBase64);
      Task<UserDetails> GetUserDetails(int linkedPersonId, Model.DealerGroupName dealerGroup);
      IEnumerable<int> GetUserRetailerSiteIds();
      string GetUserSiteIdsString();
      Task<List<DealerGroupVM>> GetUserDealerGroupByEmail(string email);
      string GetLoggedInUsersUsername();
      Task<Person> GetUserFromDatabase(string linkedPersonId, string userName, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<UserSimple>> GetAllUsersSimple();
      int GetIdFromAccessToken(string idType);
      decimal GetUserSiteGeoY();
      decimal GetUserSiteGeoX();
      string GetUserRetailerSitePostcode();
      int GetUserCurrentRetailerSiteId();
      string GetUserRetailerSiteRetailerName();
      void SetDealerGroupNameConnectionString(int dealerGroupId);
      Task ReLoadUserSiteRoleCache();
      Task<IEnumerable<ClaimTypeAndValues>> GetAllClaimTypes(Model.DealerGroupName dealerGroup);
      Task<object> GetAllRoles();
      int GetDealerGroupNameId();
      Task<SaveUserAndLoginResult> SaveUserAndLogin(int userId, UserAndLogin userAndLogin);
      int GetRetailerSiteRetailerId();
      string GetUserRetailSiteIdsString();
      Task<string> GetAspNetUserId(string email, int dealerGroupId);
      Task<List<UserPreference>> GetUserPreferences(int userId, DealerGroupName dealerGroup);
      Task<UserPreference> SaveUserPreference(UserPreference userPreference);
      string GetConnectionString();
      Task<string> GetUserEmailFromAspNetUsers(int linkedPersonId, int dealerGroupId);
      Task<IEnumerable<UsageItem>> GetUsageItems(GetUsageItemsParams parms);
      Task<List<UsageSummaryItem>> GetUsageSummaryItems(DealerGroupName dealerGroup);
      Task<IEnumerable<UsageItem>> GetUsageItemsThisWeek();
      Task<string> GetUsersName(string email, int dealerGroupId);
   }

   public class UserService : IUserService
   {
      //properties of the service
      private readonly IConfiguration config;
      private readonly IHttpContextAccessor httpContextAccessor;
      private readonly RoleManager<IdentityRole> roleManager;
      private readonly IEmailSenderService emailSender;
      private readonly IImageService imageService;
      private readonly IPeopleDataAccess peopleDataAccess;
      private readonly CPHIDbContext db;
      private readonly IUserCache userCache;


      private readonly string sectionName = "WebApp";
      private readonly string propertyName = "URL";
      private readonly CPHIDbContextFactory _dbContextFactory;


      //constructor
      public UserService(
      IHttpContextAccessor httpContextAccessor,
          IServiceProvider serviceProvider,
          UserManager<ApplicationUser> userManager,
          IEmailSenderService emailSender,
          IConfiguration config,
          IImageService imageService,
          IPeopleDataAccess peopleDataAccess,
          CPHIDbContext db,
          CPHIDbContextFactory dbContextFactory,
          IUserCache userCache,
          RoleManager<IdentityRole> roleManager)
      {

         this.httpContextAccessor = httpContextAccessor;
         this.emailSender = emailSender;
         this.config = config;
         this.imageService = imageService;
         this.peopleDataAccess = peopleDataAccess;
         this.db = db;
         this._dbContextFactory = dbContextFactory;
         this.userCache = userCache;
         this.roleManager = roleManager;
      }

      public async Task ReLoadUserSiteRoleCache()
      {
         await this.userCache.ReLoadUserSiteRoleCache();
      }

      public async Task<string> GetUserEmailFromAspNetUsers(int linkedPersonId, int dealerGroupId)
      {
         UserParamSet userParamSet = await userCache.GetUserParamSet(linkedPersonId, dealerGroupId);
         return userParamSet.Email;
      }


      public async Task<IEnumerable<UsageItem>> GetUsageItems(GetUsageItemsParams parms)
      {
         int userId = GetUserId();
         int dealerGroupId = (int)GetUserDealerGroupName();
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetUsageItems(dealerGroupId, parms);
      }
      public async Task<IEnumerable<UsageItem>> GetUsageItemsThisWeek()
      {
         int userId = GetUserId();
         int dealerGroupId = (int)GetUserDealerGroupName();
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetUsageItemsThisWeek(dealerGroupId);
      }

      public async Task<List<UsageSummaryItem>> GetUsageSummaryItems(DealerGroupName dealerGroup)
      {
         int userId = GetUserId();
         int dealerGroupId = (int)GetUserDealerGroupName();
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetUsageSummaryItems((int)dealerGroup);
      }

      public async Task<SaveUserAndLoginResult> SaveUserAndLogin(int userId, UserAndLogin userAndLogin)
      {
         Model.DealerGroupName dealerGroup = GetUserDealerGroupName();
         //User exists 
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         using (var context = _dbContextFactory.CreateDbContext(dgName))
         {
            var userManager = new UserManager<ApplicationUser>(new UserStore<ApplicationUser>(context), null, new PasswordHasher<ApplicationUser>(), null, null, null, null, null, null);
            try
            {

               if (userAndLogin.AppUserId == null)
               {
                  //new aspNet user required

                  //create
                  var applicationUser = new ApplicationUser()
                  {
                     UserName = userAndLogin.Email.Trim(),
                     Email = userAndLogin.Email.Trim(),
                     LinkedPersonId = userAndLogin.PersonId,
                     EmailConfirmed = true
                  };
                  try
                  {

                     var result = await userManager.CreateAsync(applicationUser);

                     // Successfully created the AspnetUser entry
                     if (result.Succeeded)
                     {
                        //find them
                        ApplicationUser aspNetUser = await userManager.FindByIdAsync(applicationUser.Id);

                        //set role
                        if (userAndLogin.RoleName != null)
                        {
                           await userManager.AddToRoleAsync(aspNetUser, userAndLogin.RoleName);
                        }

                        // Certain claims also need to be updated on the Person row
                        UserClaim allowReportUpload = userAndLogin.Claims.Where(x => x.ClaimType == "allowReportUpload").FirstOrDefault();

                        if (allowReportUpload != null)
                        {
                           userAndLogin.AllowReportUpload = allowReportUpload.ClaimValue == "true" ? true : false;
                        }

                        UserClaim accessReportCentre = userAndLogin.Claims.Where(x => x.ClaimType == "accessReportCentre").FirstOrDefault();

                        if (accessReportCentre != null)
                        {
                           userAndLogin.AccessReportCentre = accessReportCentre.ClaimValue == "true" ? true : false;
                        }

                        // There is not a currently existing Person row 
                        // Ie. creating user not from People line
                        if (userAndLogin.PersonId == null)
                        {
                           //need to add user to People table
                           int newUserId = await CreatePerson(userAndLogin, dealerGroup);
                           userAndLogin.PersonId = newUserId;
                           applicationUser.LinkedPersonId = newUserId;
                           await userManager.UpdateAsync(applicationUser);

                           return await DealWithClaimsAndSalesRolesAndFinishUp(userAndLogin, dealerGroup, userManager, aspNetUser);
                        }
                        else
                        {
                           // A Person row currently exists
                           // Ie. we are creating a row from the People line
                           // We may need to update IsSalesExec & AccessAllSites - do that here
                           await UpdatePerson(userAndLogin, dealerGroup);

                           return await DealWithClaimsAndSalesRolesAndFinishUp(userAndLogin, dealerGroup, userManager, aspNetUser);
                        }

                     }

                  }
                  catch (Exception ex)
                  {
                     await userManager.DeleteAsync(applicationUser);
                     throw new Exception("Error Saving User and Login");
                  }


               }
               else
               {
                  // We instead are looking to update an existing user
                  // Certain claims also need to be updated on the Person row
                  UserClaim allowReportUpload = userAndLogin.Claims.Where(x => x.ClaimType == "allowReportUpload").FirstOrDefault();

                  if (allowReportUpload != null)
                  {
                     userAndLogin.AllowReportUpload = allowReportUpload.ClaimValue == "true" ? true : false;
                  }

                  UserClaim accessReportCentre = userAndLogin.Claims.Where(x => x.ClaimType == "accessReportCentre").FirstOrDefault();

                  if (accessReportCentre != null)
                  {
                     userAndLogin.AccessReportCentre = accessReportCentre.ClaimValue == "true" ? true : false;
                  }

                  //find them
                  ApplicationUser aspNetUser = await userManager.FindByIdAsync(userAndLogin.AppUserId);

                  if (aspNetUser == null)
                  {
                     throw new Exception("Could not find user");
                  }

                  //setup
                  string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
                  var userDataAccess = new UserDataAccess(connectionString);

                  //get existing
                  var currentUserRole = await userManager.GetRolesAsync(aspNetUser);
                  var currentUserClaims = await userManager.GetClaimsAsync(aspNetUser);

                  //deal with roles
                  if (currentUserRole.Count > 0)
                  {
                     //already have a role, change it
                     if (currentUserRole[0] != userAndLogin.RoleName)
                     {
                        await userManager.RemoveFromRoleAsync(aspNetUser, currentUserRole[0]);
                        await userManager.AddToRoleAsync(aspNetUser, userAndLogin.RoleName);
                     }
                  }
                  else if (userAndLogin.RoleName != null)
                  {
                     //do not have a role, set it
                     await userManager.AddToRoleAsync(aspNetUser, userAndLogin.RoleName);
                  }

                  //deal with claims
                  await UpdateUserClaims(aspNetUser, userAndLogin, currentUserClaims);

                  //Update person
                  await userDataAccess.UpdatePerson((int)aspNetUser.LinkedPersonId, userAndLogin, dealerGroup);

                  //deal with sales roles
                  List<SalesRole> roleList = userAndLogin.SalesRoles.Select(vm => new SalesRole
                  {
                     Id = vm.Id,
                     Year = vm.Year,
                     Month = vm.Month,
                     Role = vm.Role,
                     SiteId = vm.SiteId,
                     PersonId = vm.PersonId
                  }).ToList();

                  await userDataAccess.UpdateSalesRoles(roleList, dealerGroup);

                  //finish up
                  await userCache.ReLoadUserSiteRoleCache();
                  return new SaveUserAndLoginResult() { PersonId = (int)userAndLogin.PersonId, AppUserId = aspNetUser.Id };
               }

            }
            catch (Exception ex)
            {
               throw new Exception("Error Saving User and Login");
            }
         }
         return new SaveUserAndLoginResult() { PersonId = (int)userAndLogin.PersonId, AppUserId = userAndLogin.AppUserId };


      }

      private async Task<SaveUserAndLoginResult> DealWithClaimsAndSalesRolesAndFinishUp(UserAndLogin userAndLogin, Model.DealerGroupName dealerGroup, UserManager<ApplicationUser> userManager, ApplicationUser aspNetUser)
      {
         //deal with claims
         var currentUserClaims = await userManager.GetClaimsAsync(aspNetUser);
         await UpdateUserClaims(aspNetUser, userAndLogin, currentUserClaims);

         //deal with sales roles
         await ProcessUsersSalesRoles(userAndLogin, dealerGroup, (int)userAndLogin.PersonId);


         //finish up
         await SendWelcomeEmail(aspNetUser.Id, dealerGroup, userAndLogin.Name);
         await userCache.ReLoadUserSiteRoleCache();
         return new SaveUserAndLoginResult() { PersonId = (int)userAndLogin.PersonId, AppUserId = aspNetUser.Id };
      }

      private async Task UpdatePerson(UserAndLogin userAndLogin, Model.DealerGroupName dealerGroup)
      {
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         await userDataAccess.UpdatePerson((int)userAndLogin.PersonId, userAndLogin, dealerGroup);
      }

      private async Task ProcessUsersSalesRoles(UserAndLogin userAndLogin, Model.DealerGroupName dealerGroup, int newUserId)
      {
         if (userAndLogin.SalesRoles?.Count > 0)
         {
            userAndLogin.SalesRoles.ForEach(item => item.PersonId = newUserId);
            userAndLogin.SalesRoles.ForEach(item => item.SiteId = (int)userAndLogin.SiteId);

            List<SalesRole> roleList = userAndLogin.SalesRoles.Select(vm => new SalesRole
            {
               Id = vm.Id,
               Year = vm.Year,
               Month = vm.Month,
               Role = vm.Role,
               SiteId = vm.SiteId,
               PersonId = vm.PersonId
               // Note that we're not setting the SiteDescription property because it does not exist on SalesRole
            }).ToList();

            string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
            var userDataAccess = new UserDataAccess(connectionString);
            await userDataAccess.AddSalesRoles(roleList, dealerGroup);
         }
         // Vindis Exec -- Add null salesroles for year
         else if (userAndLogin.IsSalesExec.HasValue &&  // IsSalesExec populated
                 userAndLogin.IsSalesExec == true &&  // Is True 
                 dealerGroup == DealerGroupName.Vindis) // Is Vindis
         {
            DateTime today = DateTime.Today;

            int year = today.Year;
            int month = today.Month;

            List<SalesRole> roleList = new List<SalesRole>();

            // Populate roles until end of the year
            for (int i = today.Month; i <= 12; i++)
            {
               SalesRole roleToAdd = new SalesRole();

               roleToAdd.Year = year;
               roleToAdd.Month = i;
               roleToAdd.Role = null; // Vindis don't use this
               roleToAdd.SiteId = userAndLogin.SiteId.Value;
               roleToAdd.PersonId = userAndLogin.PersonId.Value;

               roleList.Add(roleToAdd);
            }

            string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
            var userDataAccess = new UserDataAccess(connectionString);

            await userDataAccess.AddSalesRoles(roleList, dealerGroup);
         }
      }

      //methods of the service
      public Task<int> CreatePerson(UserAndLogin user, Model.DealerGroupName dealerGroup)
      {
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return userDataAccess.CreatePerson(user, dealerGroup);
      }


      public string GetConnectionString()
      {
         var dealerGroup = GetUserDealerGroupName();
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         return config.GetConnectionString(dgName);
      }


      //public string GeneratePassword(bool useLowercase, bool useUppercase, bool useNumbers, int passwordSize)
      //{

      //    const string LOWER_CASE = "abcdefghijklmnopqursuvwxyz";
      //    const string UPPER_CASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      //    const string NUMBERS = "123456789";


      //    char[] _password = new char[passwordSize];
      //    string charSet = ""; // Initialise to blank
      //    System.Random _random = new Random();
      //    int counter;

      //    // Build up the character set to choose from
      //    if (useLowercase) charSet += LOWER_CASE;

      //    if (useUppercase) charSet += UPPER_CASE;

      //    if (useNumbers) charSet += NUMBERS;


      //    for (counter = 0; counter < passwordSize; counter++)
      //    {
      //        _password[counter] = charSet[_random.Next(charSet.Length - 1)];
      //    }

      //    return String.Join(null, _password);
      //}

      //public async Task<object> GetAllUsersAndLogins(int userId)
      //{
      //    var users = await userDataAccess.GetAllUserWithSites(userId);

      //    var userIds = users.Select(u => u.Id).Distinct().ToList();

      //    //get all the aspNetUser stuff
      //    //var appUsers = userManager.Users.Where(u => userIds.Contains(u.LinkedPersonId.Value) && (u.LockoutEnd == null || u.LockoutEnd < DateTime.UtcNow)).ToList();
      //    var aspNetUsers = userManager.Users.ToList();


      //    List<UserAndLogin> usersAndLogins = new List<UserAndLogin>();

      //    IList<string> userRoles = new List<string>();
      //    IList<System.Security.Claims.Claim> userClaims = new List<System.Security.Claims.Claim>();
      //    ApplicationUser appUser = new ApplicationUser();

      //    //this loop takes 11 seconds..
      //    foreach (var user in users)  //610 users in vindisDev
      //    {
      //        appUser = aspNetUsers.Where(u => u.LinkedPersonId == user.Id).FirstOrDefault();
      //        //Check if user canLogin
      //        if (appUser != null)
      //        {
      //            userRoles = await userManager.GetRolesAsync(appUser);
      //            userClaims = await userManager.GetClaimsAsync(appUser);
      //        }
      //        else
      //        {
      //            userRoles.Clear();
      //        }


      //        UserAndLogin u = new UserAndLogin
      //        {
      //            AppUserId = appUser?.Id,
      //            Code = user.Id,
      //            Name = user.Name,
      //            //NameShort = users.Where(u => u.Id == appUser.LinkedPersonId).FirstOrDefault().Name,
      //            UserName = appUser?.UserName,
      //            RoleName = userRoles.FirstOrDefault(),
      //            Email = user.Email,
      //            Sites = user.Sites,
      //            SiteCode = user.CurrentSite_Id,
      //            IsSalesExec = user.IsSalesExec,
      //            AllowReportUpload = user.AllowReportUpload,
      //            AccessReportCentre = user.AccessReportCentre,
      //            CommAuthorityReview = userClaims.Where(x => x.Type == "commissionAuthority" && x.Value == "review").Any(),
      //            CommAuthoritySelfOnly = userClaims.Where(x => x.Type == "commissionAuthority" && x.Value == "selfOnly").Any(),
      //            SalesExecReviewReviewer = userClaims.Where(x => x.Type == "salesExecReview" && x.Value == "submitter").Any(),
      //            SalesExecReviewSubmitter = userClaims.Where(x => x.Type == "salesExecReview" && x.Value == "reviewer").Any(),
      //            // Add here
      //            CanSeeStockLanding = userClaims.Where(x => x.Type == "seeStockLanding" && x.Value == "true").Any(),
      //            CanSeeSuperCup = userClaims.Where(x => x.Type == "seeSuperCup" && x.Value == "true").Any(),
      //            CanLogin = (appUser != null) ? true : false

      //        };

      //        usersAndLogins.Add(u);
      //    }

      //    return usersAndLogins;
      //}

      public async Task<IEnumerable<UserAndLogin>> GetAllUsersAndLoginsNew()
      {
         Model.DealerGroupName dealerGroup = GetUserDealerGroupName();

         bool isSpain = dealerGroup == Model.DealerGroupName.RRGSpain;
         IEnumerable<UserAndLogin> result = null;
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         if (isSpain)
         {
            result = await userDataAccess.GetAllUsersAndLogins(dealerGroup);
            result.Where(x => x.Email != null);
         }
         else
         {
            result = await userDataAccess.GetAllUsersAndLogins(dealerGroup);
         }


         //Get the SalesRoles
         var usersSalesRoles = await userDataAccess.GetSalesRolesByYear(DateTime.UtcNow.Year, dealerGroup);

         foreach (var user in result)
         {
            user.SalesRoles = usersSalesRoles.Where(u => u.PersonId == user.PersonId).OrderBy(o => o.Month).ToList();
         }

         //Get the Claims
         var usersClaims = await userDataAccess.GetUsersClaims(dealerGroup);

         foreach (var user in result)
         {
            user.Claims = usersClaims.Where(u => u.UserId == user.AppUserId).ToList().ConvertAll(x => new UserClaim(x));
         }



         return result;



      }

      public string GetUserName()
      {
         Claim userClaimForId = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "userName").FirstOrDefault();
         if (userClaimForId != null)
         { return userClaimForId.Value; }

         if (httpContextAccessor.HttpContext.Request.Headers.TryGetValue("DeveloperId", out var devId))
         {
            string devIdValue = devId.FirstOrDefault();
            if (devIdValue == "mYxa63uCuwZg2hQA")
            {
               Model.DealerGroupName dealerGroup = GetUserDealerGroupName();
               if (dealerGroup == Model.DealerGroupName.RRGUK)
               { return "<EMAIL>"; }
               if (dealerGroup == Model.DealerGroupName.Vindis)
               { return "<EMAIL>"; }
               if (dealerGroup == Model.DealerGroupName.RRGSpain)
               { return "<EMAIL>"; }
               if (dealerGroup == Model.DealerGroupName.Jardine)
               { return "<EMAIL>"; }
            }
         }

         //still here, you're out of luck
         throw new Exception("User not found");
      }

      //public string GetUserUserName()
      //{
      //    Claim userClaimForId = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "userName").FirstOrDefault();
      //    if (userClaimForId != null) { return userClaimForId.Value; }

      //    //bool hasDevId = httpContextAccessor.HttpContext.Request.Headers.TryGetValue("DeveloperId", out devId);
      //    if (httpContextAccessor.HttpContext.Request.Headers.TryGetValue("DeveloperId", out var devId))
      //    {
      //        string devIdValue = devId.FirstOrDefault();
      //        if (devIdValue == "mYxa63uCuwZg2hQA")
      //        {
      //            DealerGroupName dealerGroup = GetUserDealerGroupName();
      //            if (dealerGroup == DealerGroupName.RRGUK) { return "<EMAIL>"; }
      //            if (dealerGroup == DealerGroupName.Vindis) { return "<EMAIL>"; }
      //            if (dealerGroup == DealerGroupName.RRGSpain) { return "<EMAIL>"; }
      //            if (dealerGroup == DealerGroupName.Jardine) { return "<EMAIL>"; }
      //            if (dealerGroup == DealerGroupName.PinkStones) { return "<EMAIL>"; }
      //        }
      //    }

      //    //still here, you're out of luck
      //    throw new Exception("User not found");
      //}


      public int GetUserId()
      {
         return GetIdFromAccessToken("LinkedPersonId");
      }



      //public async Task<int> GetUserRetailerSiteId(int userId, DealerGroupName dealerGroup)
      //{
      //    string connectionString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(GetUserDealerGroupName()));
      //    var userDataAccess = new UserDataAccess(connectionString);
      //    return await userDataAccess.GetUserRetailerSiteId(userId, dealerGroup);
      //}

      //public async Task<int> GetUserRetailerSiteRetailerId(int userId, DealerGroupName dealerGroup)
      //{
      //    string connectionString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(GetUserDealerGroupName()));
      //    var userDataAccess = new UserDataAccess(connectionString);
      //    return await userDataAccess.GetUserRetailerSiteRetailerId(userId, dealerGroup);
      //}

      public string GetUserRetailerSiteRetailerName()
      {
         Claim claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "CurrentRetailerSiteRetailerName").FirstOrDefault();

         if (claim != null)
         {
            return claim.Value;
         }
         else
         {
            throw new Exception($"No matching CurrentRetailerSiteRetailerName");
         }
      }

      public int GetUserCurrentRetailerSiteId()
      {
         return GetIdFromAccessToken("CurrentRetailerSiteId");
      }

      public int GetDealerGroupNameId()
      {
         return GetIdFromAccessToken("DealerGroupId");
      }

      public int GetRetailerSiteRetailerId()
      {
         return GetIdFromAccessToken("CurrentRetailerSiteRetailerId");
      }

      public int GetIdFromAccessToken(string idType)
      {

         Claim claim = null;
         if (idType == "LinkedPersonId")
         {
            claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "LinkedPersonId").FirstOrDefault();
            if (claim == null)
            {
               //we are probably trying to setup a new DG
               StringValues secretResult;
               bool canGetSecret = httpContextAccessor.HttpContext.Request.Headers.TryGetValue("newDGSecret", out secretResult);
               if (canGetSecret && secretResult == "rxpA4pCgMC38MKEQ")
               {
                  return 1140;
               }
            }
         }
         else if (idType == "CurrentSiteId")
         { claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "CurrentSiteId").FirstOrDefault(); }
         else if (idType == "CurrentRetailerSiteId")
         { claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "CurrentRetailerSiteId").FirstOrDefault(); }
         else if (idType == "CurrentRetailerSiteRetailerId")
         { claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "CurrentRetailerSiteRetailerId").FirstOrDefault(); }
         else if (idType == "DealerGroupId")
         {

            claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "DealerGroupId").FirstOrDefault();
            if (claim == null)
            {
               //we are probably trying to setup a new DG
               StringValues secretResult;
               bool canGetSecret = httpContextAccessor.HttpContext.Request.Headers.TryGetValue("newDGSecret", out secretResult);
               if (canGetSecret && secretResult == "rxpA4pCgMC38MKEQ")
               {
                  return 13;
               }
            }
         }

         if (claim != null)
         {
            return Int32.Parse(claim.Value);
         }
         else
         {
            throw new Exception($"No matching claim for user called {idType}");
         }
      }

      public decimal GetUserSiteGeoX()
      {
         var claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "CurrentSiteGeoX").FirstOrDefault();
         if (claim != null)
         {
            return decimal.Parse(claim.Value);
         }
         else
         {
            throw new Exception($"No matching Geo data for user");
         }
      }
      public decimal GetUserSiteGeoY()
      {
         var claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "CurrentSiteGeoY").FirstOrDefault();
         if (claim != null)
         {
            return decimal.Parse(claim.Value);
         }
         else
         {
            throw new Exception($"No matching Geo data for user");
         }
      }
      public string GetUserRetailerSitePostcode()
      {
         var claim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "CurrentRetailerSiteRetailerPostcode").FirstOrDefault();
         if (claim != null)
         {
            return claim.Value;
         }
         else
         {
            throw new Exception($"No matching Postcode data for user");
         }
      }

      public string GetUserConnectionString()
      {
         var userDealerGroupName = GetUserDealerGroupName();
         var result = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(userDealerGroupName));
         return result;
      }


      public IEnumerable<int> GetUserSiteIds()
      {
         string siteIdsString = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "EligibleSiteIdsString").FirstOrDefault().Value;
         return siteIdsString.Split(',').Select(x => int.Parse(x));
      }

     


      public string GetUserSiteIdsString()
      {
         string siteIdsString = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "EligibleSiteIdsString").FirstOrDefault().Value;
         return siteIdsString;
      }

      public IEnumerable<int> GetUserRetailerSiteIds()
      {
         string retailerSiteIdsString = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "EligibleRetailerSiteIdsString").FirstOrDefault().Value;
         return retailerSiteIdsString.Split(',').Select(x => int.Parse(x));
      }

      

      public int[] GetEligibleSitesIds()
      {
         return httpContextAccessor.HttpContext.User.Claims.First(x => x.Type == "EligibleSiteIdsString").Value.Split(',').Select(int.Parse).ToArray();
      }


      public string GetUserCommissionAuthority()
      {
         return httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "CommissionAuthority").FirstOrDefault().Value;
      }

      public IEnumerable<Claim> GetUserClaims()
      {
         return httpContextAccessor.HttpContext.User.Claims;
      }


      public async Task<string> GetUsersNameFromDatabase(int linkedPersonId, Model.DealerGroupName dealerGroup)
      {
         return await userCache.GetUsersName(dealerGroup, linkedPersonId);
         //string connectionString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(GetUserDealerGroupName()));
         //var userDataAccess = new UserDataAccess(connectionString);
         //return await userDataAccess.GetUsersName(linkedPersonId, dealerGroup);
      }





      private async Task UpdateUserClaims(ApplicationUser currentUser, UserAndLogin user, IList<Claim> currentUserClaims)
      {

         IList<Claim> addClaims = new List<Claim>();
         IList<Claim> removeClaims = new List<Claim>();
         List<Claim> changedClaims = new List<Claim>();

         Model.DealerGroupName dealerGroup = GetUserDealerGroupName();
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);

         using (var context = _dbContextFactory.CreateDbContext(dgName))
         {

            var userManager = new UserManager<ApplicationUser>(
           new UserStore<ApplicationUser>(context),
           null,
           new PasswordHasher<ApplicationUser>(),
           null,
           null,
           null,
           null,
           null,
           null);

            Dictionary<string, UserClaim> incomingClaims = user.Claims.Where(x => x.ClaimValue != "false" && x.ClaimValue != "none").ToDictionary(x => x.ClaimType);
            Dictionary<string, Claim> existingClaims = currentUserClaims.ToDictionary(x => x.Type);

            //walk through existing claims
            foreach (Claim item in currentUserClaims)
            {
               bool isIncoming = incomingClaims.TryGetValue(item.Type, out UserClaim incomingValue);

               if (!isIncoming)
               {
                  //user no longer has this claim, so add it to delete list
                  removeClaims.Add(item);
               }
               else
               {
                  //have it already, and is incoming, check for change.
                  if (item.Value != incomingValue.ClaimValue)
                  {
                     removeClaims.Add(item);
                  }
               }
            }

            //walk through incoming claims
            foreach (var item in incomingClaims)
            {
               bool isExisting = existingClaims.TryGetValue(item.Key, out Claim existingClaim);
               string key = item.Key;
               string value = item.Value.ClaimValue;
               if (!isExisting)
               {
                  addClaims.Add(new Claim(key, value));
               }
               else
               {
                  //have it already, it incoming, check for change
                  if (value != existingClaim.Value)
                  {
                     addClaims.Add(new Claim(key, value));
                  }
               }
            }



            await userManager.RemoveClaimsAsync(currentUser, removeClaims);
            await userManager.AddClaimsAsync(currentUser, addClaims);

         }
      }

      public async Task DeleteUsersAndLogins(int userId, string idToDelete)
      {
         Model.DealerGroupName dealerGroup = GetUserDealerGroupName();
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);

         using (var context = _dbContextFactory.CreateDbContext(dgName))
         {

            var userManager = new UserManager<ApplicationUser>(
           new UserStore<ApplicationUser>(context),
           null,
           new PasswordHasher<ApplicationUser>(),
           null,
           null,
           null,
           null,
           null,
           null);


            ApplicationUser appUser = await userManager.FindByIdAsync(idToDelete);
            await userManager.DeleteAsync(appUser);


            var person = await GetUserFromDatabase(appUser.LinkedPersonId.ToString(), appUser.UserName, GetUserDealerGroupName());

            person.IsRemoved = true;
            person.HasLeft = true;
            person.RemovedDate = DateTime.UtcNow;
            await peopleDataAccess.UpdatePerson(person, GetUserDealerGroupName());

            string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
            var userDataAccess = new UserDataAccess(connectionString);
            await userDataAccess.DeleteSalesroles(DateTime.UtcNow, person, GetUserDealerGroupName());
            await userCache.ReLoadUserSiteRoleCache();

         }
      }

      public async Task<IEnumerable<string>> GetUserSitesNameFromDatabase(int userId, Model.DealerGroupName dealerGroup)
      {
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetUserSitesName(userId, dealerGroup);
      }


      public async Task<IEnumerable<RetailerSite>> GetUserRetailSites(Model.DealerGroupName dealerGroup)
      {
         var siteIds = GetEligibleSitesIds().ToList();
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetUserRetailSites(siteIds, dealerGroup);
      }


      public string GetUserRetailSiteIdsString()
      {
         return httpContextAccessor.HttpContext.User.Claims.First(x => x.Type == "EligibleRetailerSiteIdsString").Value;
      }



      public IEnumerable<int> GetUserRetailSiteIds()
      {
         return httpContextAccessor.HttpContext.User.Claims.First(x => x.Type == "EligibleRetailerSiteIdsString").Value.Split(',').Select(x => int.Parse(x));
      }
      private async Task SendWelcomeEmail(string id, Model.DealerGroupName dealerGroup, string newUsersName)


      {
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);

         using (var context = _dbContextFactory.CreateDbContext(dgName))
         {

            var userManager = new UserManager<ApplicationUser>(
           new UserStore<ApplicationUser>(context),
           null,
           new PasswordHasher<ApplicationUser>(),
           null,
           null,
           null,
           null,
           null,
           null);
            var aspnetuser = await userManager.FindByIdAsync(id);
            //var dbuserName = await GetUsersNameFromDatabase(aspnetuser.LinkedPersonId.Value, dealerGroup);
            var userRoles = await userManager.GetRolesAsync(aspnetuser);
            var roleName = userRoles.First();
            var sites = await GetUserSitesNameFromDatabase(aspnetuser.LinkedPersonId.Value, dealerGroup);
            string sitesList = string.Join(", ", sites);
            //var companyName = await GetUserDealerGroupNameName(aspnetuser.LinkedPersonId.Value);
            //string nominatedPerson = await userDataAccess.GetNominatedUserName(aspnetuser.LinkedPersonId.Value);

            //string nominatedPersonFirstName = nominatedPerson.Split(' ')[0];
            string webURL = config[$"{sectionName}:{propertyName}"];

            string guideIfAvailable = "";
            //if (roleName != "Scanner")
            //{
            //    guideIfAvailable = "Here you will also find a training guide which explains how to scan vehicles using Stockpulse.";
            //}


            //main body build
            string body = $"**Automated welcome message for: <strong>{newUsersName}</strong>**<br>";

            body += $"<h2>Welcome to Spark</h2>";
            body += $"You have been set-up with a new username and access to Spark, the cloud-based dealership system from CPH Insight.<br><br>";
            body += $"Your details are: <br><br>";
            body += $"<table > <tbody><tr><td>Username:</td><td><strong>{aspnetuser.UserName}</strong>" +
                $"</td></tr><tr><td>User level:</td><td><strong>{roleName}</strong></td></tr><tr><td>Sites:</td><td><strong>{sitesList}</strong></td></tr></tbody></table><br><br>";
            body += $"To get started, visit <a href=\"{webURL}\"> {webURL} </a> to initially set a password - simply click on the red 'Forgot Password' button. {guideIfAvailable}<br><br>";
            //body += $"Once you have set your password, please download the Spark app to your iPhone, iPad or Android device.  It is free to download from the Apple AppStore / Google Play Store, simply search for CPHi Spark.  When you start the app, login using your username and password.<br><br>";
            //body += $"Your nominated Spark coordinator at {companyName} is {nominatedPerson}. Should you have any queries with Spark please pick up with {nominatedPersonFirstName} or contact CPHI support on 0207 971 1176.";
            body += $"Should you have any queries with Spark please contact CPHI <NAME_EMAIL> or give us a call on 0204 577 1239.";


            await emailSender.SendEmailAsync(aspnetuser.Email, "Welcome to Spark", body, null);

         }

      }


      public async Task<UserDetails> GetUserDetails(int linkedPersonId, Model.DealerGroupName dealerGroup)
      {
         var connString = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         string connectionString = config.GetConnectionString(connString);
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetUserDetails(linkedPersonId, dealerGroup);
      }



      public async Task<Helpers.Languages?> GetLanguage(int linkedPersonId, Model.DealerGroupName dealerGroup)
      {
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetLanguage(linkedPersonId, dealerGroup);
      }

      public async Task UpdateLanguage(int linkedPersonId, Helpers.Languages language, Model.DealerGroupName dealerGroup)
      {
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         await userDataAccess.UpdateLanguage(linkedPersonId, language, dealerGroup);
      }

      public async Task<string> UploadProfileImage(int userId, string imageBase64)
      {
         if (string.IsNullOrEmpty(imageBase64))
         {
            return string.Empty;
         }

         try
         {
            using (Stream stream = imageService.ConvertBase64ToStream(imageBase64))
            {
               string dealerGroupBlobName = DealerGroupBlobname.Get(GetUserDealerGroupName());
               var result = await imageService.UploadProfileImage(dealerGroupBlobName, stream, userId, true);
               if (result.Equals(false))
               {
                  return "Profile image upload failed";
               }
            }
         }
         catch
         {
            return "Profile image upload failed";
         }

         return string.Empty;
      }

      public List<Spark.Model.ViewModels.UserImage> GetProfileImageURL(List<int> userIds)
      {
         var userImages = new List<Spark.Model.ViewModels.UserImage>();
         string dealerGroupBlobName = DealerGroupBlobname.Get(GetUserDealerGroupName());
         foreach (var userId in userIds)
         {
            userImages.Add(new Spark.Model.ViewModels.UserImage() { UserId = userId, URL = imageService.GetProfileImageURL(userId, dealerGroupBlobName) });
         }

         return userImages;
      }


      public async Task<IEnumerable<ClaimTypeAndValues>> GetAllClaimTypes(Model.DealerGroupName dealerGroup)
      {
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetAllClaimTypes(dealerGroup);
      }

      public async Task<object> GetAllRoles()
      {

         return await roleManager.Roles.ToListAsync();
      }



      private async Task<int> GetDealerGroupNameIdFromCache(string username, int dealerGroupId)
      {
         var result = await userCache.GetUserParamSetByEmail(username, dealerGroupId);
         return result.DealerGroupId;
      }



      public async Task SetDBContextConnectionString(string username, int dealerGroupId)
      {
         //Get get the dealergroup and set the connection string
         int userDealerGroupNameId = await GetDealerGroupNameIdFromCache(username, dealerGroupId);
         SetDealerGroupNameConnectionString(userDealerGroupNameId);
      }



      public void SetDealerGroupNameConnectionString(int dealerGroupId)
      {
         var dealerGroup = (Model.DealerGroupName)dealerGroupId;
         string connectionStringName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         string connectionString = config.GetConnectionString(connectionStringName);
         this.db.Database.GetDbConnection().ConnectionString = connectionString;
         //this.userDataAccess.SetConnectionString(connectionString);
      }

      public async Task<List<UserPreference>> GetUserPreferences(int userId, DealerGroupName dealerGroup)
      {
         string connectionStringName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         string connectionString = config.GetConnectionString(connectionStringName);
         var userPreferencesDataAccess = new UserPreferencesDataAccess(connectionString);
         return (await userPreferencesDataAccess.GetUserPreferences(userId, dealerGroup)).ToList();
      }

      public Model.DealerGroupName GetUserDealerGroupName()
      {
         try
         {
            //return DealerGroupName.JJPremiumCars;
            int userDealerGroupNameId = GetIdFromAccessToken("DealerGroupId");
            return (Model.DealerGroupName)userDealerGroupNameId;
         }
         catch (Exception ex)
         {
            throw new Exception("No DealerGroupName in token");
         }
      }



      public async Task<string> GetUserName(string email, int dealerGroupId)
      {
         try
         {
            var result = await userCache.GetUserParamSetByEmail(email, dealerGroupId);
            return result.UserName;
         }
         catch
         {
            return null;
         }

      }

      public async Task<string> GetUsersName(string email, int dealerGroupId)
      {
         try
         {
            var result = await userCache.GetUserParamSetByEmail(email, dealerGroupId);
            return result.Name;
         }
         catch
         {
            return null;
         }

      }

      public async Task<string> GetAspNetUserId(string email, int dealerGroupId)
      {
         try
         {
            var result = await userCache.GetUserParamSetByEmail(email, dealerGroupId);
            return result.AspNetUserId;
         }
         catch
         {
            return null;
         }

      }

      public async Task<Person> GetUserFromDatabase(string linkedPersonId, string userName, Model.DealerGroupName dealerGroup)
      {
         //var dealerGroupId = GetDealerGroupIdFromCache(userName, dealerGroupId);
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.GetUser(linkedPersonId, dealerGroup);
      }

      public async Task<List<DealerGroupVM>> GetUserDealerGroupByEmail(string email)
      {
         var dealerGroupIds = await userCache.GetUserDealerGroupByEmail(email);
         var dealerGroups = new List<DealerGroupVM>();
         foreach (var id in dealerGroupIds)
         {
            dealerGroups.Add(new DealerGroupVM()
            {
               Id = id,
               DisplayName = DealerGroupDisplayName.Get((Model.DealerGroupName)id)
            });

         }

         return dealerGroups;

      }

      public string GetLoggedInUsersUsername()
      {
         return httpContextAccessor.HttpContext.User.Claims.First(x => x.Type == "userName").Value;
      }

      public async Task<IEnumerable<UserSimple>> GetAllUsersSimple()
      {
         Model.DealerGroupName dealerGroup = GetUserDealerGroupName();

         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         var userDataAccess = new UserDataAccess(connectionString);

         return await userDataAccess.GetAllUsersSimple(dealerGroup);
      }



      public async Task<UserPreference> SaveUserPreference(UserPreference userPreference)
      {
         Model.DealerGroupName dealerGroup = GetUserDealerGroupName();
         int userId = GetUserId();
         string connectionString = config.GetConnectionString(DealerGroupConnectionNameService.GetConnectionName(GetUserDealerGroupName()));
         userPreference.Person_Id = userId;
         var userDataAccess = new UserDataAccess(connectionString);
         return await userDataAccess.SaveUserPreference(userPreference);
      }


   }
}
