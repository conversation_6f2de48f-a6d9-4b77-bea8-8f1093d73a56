import { Component, HostListener, Input, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CellClassParams, GridApi, RowClickedEvent } from 'ag-grid-community';
import { CphPipe } from 'src/app/cph.pipe';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { AutotraderService } from "../../../services/autotrader.service";
import { ConstantsService } from "../../../services/constants.service";
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from "../../../services/selections.service";

import { CommissionRowRRG } from "../salesCommissionRRG.model";
import { SalesCommissionServiceRRG } from "../salesCommissionRRG.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";

@Component({
  selector: "commissionSitesTableRRG",

  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        class="ag-theme-balham"
        [gridOptions]="mainTableGridOptions"
        domLayout="autoHeight"
        
      >
      </ag-grid-angular>
</div>
  `,



  styleUrls: ["./../../../../styles/components/_agGrid.scss"],
  styles: [
    `


      ag-grid-angular {
        width: 100%;
        max-height:100%;
        margin: 0em auto;
      }
      #gridHolder {
        position: relative;
        width:100%;
      }
    `,
  ],
})
export class CommissionSitesTableRRGComponent implements OnInit {
  @Input() public rowData: CommissionRowRRG[];
  @Input() public isRegionsTable:boolean;
  @Input() public pinnedBottomData: CommissionRowRRG[];

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth
    this.selections.screenHeight = window.innerHeight
    if (this.gridApi) {
      this.gridApi.resetRowHeights()
      this.resizeGrid()
    }
  }

  columnDefs: any[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;
  //subscription: Subscription;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public analysis: AutotraderService,
    public router: Router,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: SalesCommissionServiceRRG
  ) { }

 

  ngOnInit() {
    this.initParams();
  }

  ngOnDestroy() {
  }

  dealWithNewData(){
    this.gridApi.setRowData(this.getRowData());
    this.gridApi.setPinnedBottomRowData(this.makeBottomRowData());
  }

  initParams() {

    // table definitions
    this.mainTableGridOptions = {
      getMainMenuItems:(params)=>this.gridHelpersService.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlights),
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      domLayout: 'autoHeight',
      
      onGridReady:(params)=>this.onGridReady(params),
      suppressPropertyNamesCheck: true,
      onRowClicked: (params) => this.onRowClick(params),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)))
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      rowData: this.getRowData(),
      pinnedBottomRowData:  this.makeBottomRowData(),
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },
      columnDefs: this.getColDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }
    }

  }

  // cellClassProvider(params: CellClassParams){
  //   if (this.isRegionsTable) {
  //     return params?.value < 0 ? 'badFont' : '';
  //   } else {
  //     return this.gridMethods.cellClassProviderWithColourFontNew(params,this.service.topBottomHighlights);
  //   }
  // }

  getColDefs()
  {
    if(this.service.schemeToShow === 'LBDMs')
    {
      return this.getColDefsLBDM();
    }
    else
    {
      return this.getColDefsNonLBDM();
    }
  }


  getColDefsNonLBDM()
  {
    return [
      { headerName: '', field: 'Label', colId: 'Label', width: 150, type: 'label', },
      { headerName: 'Execs', children:[
          { headerName: 'New', field: 'ExecsNew', colId: 'ExecsNew', width: 70, type: 'number', },
          { headerName: 'Used', field: 'ExecsUsed', colId: 'ExecsUsed', width: 70, type: 'number' },
          { headerName: 'New/Used', field: 'ExecsNewUsed', colId: 'ExecsNewUsed', width: 70, type: 'number' },
          { headerName: 'Fleet', field: 'ExecsFleet', colId: 'ExecsFleet', width: 70, type: 'number' },
      ]},
      {
          headerName: 'Units Sold - New', children: [
              { headerName: 'Renault', field: 'UnitsRenault', colId: 'UnitsRenault', width: 95, type: 'number' },
              { headerName: 'Nissan', field: 'UnitsNissan', colId: 'UnitsNissan', width: 95, type: 'number' },
              { headerName: 'Motability', field: 'UnitsMotability', colId: 'UnitsMotability', width: 95, type: 'number' },
              { headerName: 'Dacia', field: 'UnitsDacia', colId: 'UnitsDacia', width: 95, type: 'number' },
              { headerName: 'Other', field: 'UnitsOther', colId: 'UnitsOther', width: 95, type: 'number' }
          ]
      },
      {
          headerName: 'Units Sold - Fleet', children: [
              { headerName: 'Car', field: 'UnitsFleetCar', colId: 'UnitsFleetCar', width: 95, type: 'number' },
              { headerName: 'Van', field: 'UnitsFleetVan', colId: 'UnitsFleetVan', width: 95, type: 'number' }
          ]
      },
      {
          headerName: 'Units Sold - Used', children: [
              { headerName: this.constants.translatedText.Total, field: 'UnitsUsed', colId: 'UnitsUsed', width: 95, type: 'number' }
          ]
      },
      {
          headerName: this.constants.translatedText.Total, children: [
              { headerName: 'VOLUME', field: 'UnitsTotal', colId: 'UnitsTotal', width: 95, type: 'number' }
          ]
      },
      {
          headerName: 'Products Sold', children: [
              { headerName: 'Paint', field: 'PaintCount', colId: 'PaintCount', width: 95, type: 'number' },
              { headerName: this.constants.translatedText.Gap, field: 'GapCount', colId: 'GapCount', width: 95, type: 'number' },
              { headerName: 'Cosmetic', field: 'CosmeticCount', colId: 'CosmeticCount', width: 95, type: 'number' },
              { headerName: 'Tyre', field: 'TyreCount', colId: 'TyreCount', width: 95, type: 'number' },
              { headerName: 'Wheel', field: 'TyreAlloyCount', colId: 'TyreAlloyCount', width: 95, type: 'number' },
              { headerName: 'WheelGuard', field: 'WheelGuardCount', colId: 'WheelGuardCount', width: 95, type: 'number' },
              { headerName: 'Warranty', field: 'WarrantyCount', colId: 'WarrantyCount', width: 95, type: 'number' },
              { headerName: 'Mot', field: 'MotCount', colId: 'MotCount', width: 95, type: 'number' },
              { headerName: 'R_Assist', field: 'RoadsideAssistCount', colId: 'RoadsideAssistCount', width: 95, type: 'number' },
              { headerName: 'Finance', field: 'FinanceCount', colId: 'FinanceCount', width: 95, type: 'number' },
              { headerName: 'PartEx', field: 'PartExCount', colId: 'PartExCount', width: 95, type: 'number' },
              { headerName: 'Service', field: 'ServiceCount', colId: 'ServiceCount', width: 95, type: 'number' },
          ]
      },
      {
          headerName: 'Commission',
          children: [
              { headerName: 'Earned', minWidth: 100, field: 'TotalCommission', colId: 'TotalCommission', width: 95, type: 'currency' }
          ]
      }
  ];
  }

  getColDefsLBDM()
  {
    return [
      { headerName: '', field: 'Label', colId: 'Label', width: 150, type: 'label', },
      { headerName: 'Core Fleet', children:[
        { headerName: 'Units', field: 'UnitsFleetCar', colId: 'UnitsFleetCar', width: 95, type: 'number', },

      ] },
      {
          headerName: 'Renault Car', children: [
              { headerName: 'Hybrid / ICE', field: 'UnitsRenaultCar', colId: 'UnitsRenaultCar', width: 95, type: 'number' },
              { headerName: 'EV', field: 'UnitsRenaultCarEV', colId: 'UnitsRenaultCarEV', width: 95, type: 'number' },
          ]
      },
      {
        headerName: 'Renault Van', children: [
            { headerName: 'Hybrid / ICE', field: 'UnitsRenaultVan', colId: 'UnitsRenaultVan', width: 95, type: 'number' },
            { headerName: 'EV', field: 'UnitsRenaultVanEV', colId: 'UnitsRenaultVanEV', width: 95, type: 'number' },
        ]
      },
      { headerName: 'Dacia', field: 'UnitsDacia', colId: 'UnitsDacia', width: 95, type: 'number', },
      {
        headerName: 'Renault / Dacia Used', children: [
            { headerName: 'Cars', field: 'UnitsUsedCar', colId: 'UnitsUsedCar', width: 95, type: 'number' },
            { headerName: 'Van', field: 'UnitsUsedVan', colId: 'UnitsUsedVan', width: 95, type: 'number' },
        ]
      },

      {
          headerName: this.constants.translatedText.Total, children: [
              { headerName: 'VOLUME', field: 'UnitsTotal', colId: 'UnitsTotal', width: 95, type: 'number' }
          ]
      },
      {
          headerName: 'Products Sold', children: [
              { headerName: 'Service Plan', field: 'ServiceCount', colId: 'ServiceCount', width: 95, type: 'number' },
              { headerName: 'Paint', field: 'PaintCount', colId: 'PaintCount', width: 95, type: 'number' },
              { headerName: 'Cosmetic', field: 'CosmeticCount', colId: 'CosmeticCount', width: 95, type: 'number' },
              { headerName: 'Tyre & Alloy', field: 'TyreAlloyCount', colId: 'TyreAlloyCount', width: 95, type: 'number' },
              { headerName: 'Finance', field: 'FinanceCount', colId: 'FinanceCount', width: 95, type: 'number' },
              { headerName: 'Tyre', field: 'TyreCount', colId: 'TyreCount', width: 95, type: 'number' },
              { headerName: 'WheelGuard', field: 'WheelGuardCount', colId: 'WheelGuardCount', width: 95, type: 'number' },
          ]
      },
      {
          headerName: 'Commission',
          children: [
              { headerName: 'Earned', field: 'TotalCommission', colId: 'TotalCommission', width: 95, type: 'currency' }
          ]
      }
  ];
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.mainTableGridOptions.context = { thisComponent: this };
    this.gridApi.sizeColumnsToFit();
    if(this.isRegionsTable){
      this.service.mainTableRefRegions = this;
    }else{
      this.service.mainTableRef = this;
    }
  }


  getRowData(){
    if(this.service.chosenSite)
    {
      return this.service.peopleRows
    }
    else if(this.service.schemeToShow === 'LBDMs')
    {
      return this.service.lbdmRows.filter(x => !x.IsTotal);
    }
    else
    {
      //must be showing sites
      return this.isRegionsTable ? this.service.siteRows.filter(x=>x.IsRegion && !x.IsTotal) : this.service.siteRows.filter(x=>!x.IsRegion && !x.IsTotal)
    }
  }

  makeBottomRowData() {

    if(!!this.service.chosenSite)
    {
      return [this.service.siteRows.find(x => x.RowId == this.service.chosenSite.RowId)];
    }
    else if(this.service.schemeToShow === 'LBDMs')
    {
      return this.service.lbdmRows.filter(x=>x.IsTotal);
    }
    else
    {
      return this.service.siteRows.filter(x=>x.IsTotal)
    }
  }

  onRowClick(params:RowClickedEvent): void {
    if(params.rowPinned){return;}
    const row: CommissionRowRRG = params.data;
    this.service.onSiteTableClick(row);
  }



  cellClassProviderWithColourFont(params) {
    if (params.colDef.goods && params.colDef.goods.indexOf(params.data.label) > -1) {
      return 'good ag-right-aligned-cell';
    } else if (params.colDef.bads && params.colDef.bads.indexOf(params.data.label) > -1) {
      return 'bad ag-right-aligned-cell';
    }
    //still here so no goods / bads so do number colour
    if (params.value < 0) {
      return 'badFont ag-right-aligned-cell'
    } else {
      return 'ag-right-aligned-cell'
    }
  }



  resizeGrid() {
    if (this.gridApi)       this.gridApi.sizeColumnsToFit();
  }





  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    let title = this.service.chosenSite ? 'Commissions by person' : 'Commissions by site';
    this.excel.createSheetObject(tableModel, title, 1, 1);
  }
}
