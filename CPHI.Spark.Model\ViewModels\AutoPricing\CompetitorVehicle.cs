﻿using CPHI.Spark.Model.Services;
using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{

  public class CompetitorVehicle
  {
    public CompetitorVehicle() { }

    public CompetitorVehicle(AdvertDetailsForCompetitorList advert, decimal pricePosition, int retailerId)
    {
      CompetitorName = advert.AdSiteName;
      CompetitorId = retailerId;
      Mileage = advert.OdometerReading.HasValue ? advert.OdometerReading.Value : 0;
      Year = advert.FirstRegisteredDate != null ? ((DateTime)advert.FirstRegisteredDate).Year : DateTime.Now.Year;
      AdvertisedPrice = (int)advert.AdvertisedPrice;
      PricePosition = pricePosition;
      Distance = 0;
      VehicleReg = advert.VehicleReg;
      ImageURL = advert.ImageUrl;
      WebsiteSearchIdentifier = advert.WebSiteSearchIdentifier;
      Segment = string.Empty;
      IsOurVehicle = true;
    }
    public CompetitorVehicle(AdvertDetailsForCompetitorList advert, decimal pricePosition, int retailerId, string segment)
    {
      CompetitorName = advert.AdSiteName;
      CompetitorId = retailerId;
      Mileage = advert.OdometerReading.HasValue ? advert.OdometerReading.Value : 0;
      Year = advert.FirstRegisteredDate != null ? ((DateTime)advert.FirstRegisteredDate).Year : DateTime.Now.Year;
      AdvertisedPrice = (int)advert.AdvertisedPrice;
      PricePosition = pricePosition;
      Distance = 0;
      VehicleReg = advert.VehicleReg;
      ImageURL = advert.ImageUrl;
      WebsiteSearchIdentifier = advert.WebSiteSearchIdentifier;
      Segment = segment;
      IsOurVehicle = true;
    }

    public CompetitorVehicle(string siteName, string vehicleReg, int mileage, int year, int advertisedPrice, decimal pricePosition, string imageURL)
    {
      CompetitorName = siteName;
      Mileage = mileage;
      Year = year;
      AdvertisedPrice = advertisedPrice;
      PricePosition = pricePosition;
      Distance = 0;
      VehicleReg = vehicleReg;
      Segment = string.Empty;
      IsOurVehicle = true;
      ImageURL = imageURL;

    }

    public CompetitorVehicle(AutoTraderVehicleListing itemIn, decimal homeLong, decimal homeLat)
    {
      //string imageUrl = itemIn.media.images
      int advertisedPrice = itemIn.adverts.retailAdverts.totalPrice.amountGBP ?? 0;
      int valuation = itemIn.valuations.adjusted.retail.amountGBP ?? itemIn.valuations.marketAverage.retail.amountGBP ?? itemIn.valuations.marketAverage.retail.amountExcludingVatGBP ?? 0;
      WebsiteSearchIdentifier = itemIn.metadata.searchId;
      ImageURL = (itemIn.media.images.Count > 0) ? itemIn.media.images[0].href : null;
      int.TryParse(itemIn.advertiser.advertiserId, out int advertiserId);
      CompetitorId = advertiserId;
      CompetitorName = itemIn.advertiser.name;
      Segment = itemIn.advertiser.segment ?? "Private";
      Year = itemIn.vehicle.firstRegistrationDate.HasValue ? itemIn.vehicle.firstRegistrationDate.Value.Year : null;
      SeptemberPlateChange = itemIn.vehicle.firstRegistrationDate.HasValue ? (itemIn.vehicle.firstRegistrationDate.Value.Month > 7 ? true : false) : null;
      VehicleReg = itemIn.vehicle.registration;
      Mileage = itemIn.vehicle.odometerReadingMiles ?? 0;
      AdvertisedPrice = advertisedPrice;
      PricePosition = valuation > 0 ? advertisedPrice / (decimal)valuation : 0;
      Distance = (int)(
                      (itemIn.advertiser.location.longitude != null && itemIn.advertiser.location.latitude != null) ?
                      DistanceCalculatorService.CalculateDistance(homeLong, homeLat, (decimal)itemIn.advertiser.location.longitude, (decimal)itemIn.advertiser.location.latitude) :
                      0
                  );
      VehicleType = itemIn.vehicle.vehicleType;
      TotalAdvertCount = itemIn.advertiser.totalAdvertCount;
    }

    public CompetitorVehicle(VehicleValuationCompetitorAnalysis itemIn)
    {
      WebsiteSearchIdentifier = itemIn.WebsiteSearchIdentifier;
      ImageURL = itemIn.ImageURL;
      CompetitorName = itemIn.CompetitorName;
      Segment = itemIn.Segment;
      Year = itemIn.Year;
      SeptemberPlateChange = itemIn.SeptemberPlateChange;
      VehicleReg = itemIn.VehicleReg;
      Mileage = itemIn.Mileage;
      AdvertisedPrice = itemIn.AdvertisedPrice;
      PricePosition = itemIn.PricePosition;
      Distance = itemIn.Distance;
      VehicleType = itemIn.VehicleType;
    }
    public string WebsiteSearchIdentifier { get; set; }
    public string ImageURL { get; set; }
    public string CompetitorName { get; set; }
    public int CompetitorId { get; set; }
    public string Segment { get; set; }
    public string VehicleReg { get; set; }
    public int? Year { get; set; }
    public int Mileage { get; set; }
    public int AdvertisedPrice { get; set; }
    public decimal PricePosition { get; set; }
    public int Distance { get; set; }
    public bool? SeptemberPlateChange { get; set; }
    public bool IsOurVehicle { get; set; }
    public string? VehicleType { get; set; }
    public int? TotalAdvertCount { get; set; }
    public bool? IsTradeAdjusted { get; set; }
  }


}
