
CREATE OR ALTER  PROCEDURE [autoprice].[UPDATE_LeavingPriceItems]

(
		@dealerGroupId int,
		@includeUnPublished bit
)

AS
BEGIN

SET NOCOUNT ON;

DECLARE @todayStart Date = CONVERT(date,getDate());
DECLARE @sixMonthsAgo Date = DATEADD(month,-6,getDate());

WITH relevantSnaps as
(
	SELECT Id,VehicleAdvert_Id,LifecycleStatus,SuppliedPrice,AutotraderAdvertStatus
	FROM autoprice.VehicleAdvertSnapshots snaps
	WHERE DealerGroup_Id = @dealerGroupId
	AND (
			snaps.LifecycleStatus NOT IN ('SOLD','WASTEBIN')
			OR
			snaps.LifecycleStatus IS NULL
		) AND
		snaps.SuppliedPrice IS NOT NULL AND
		snaps.SuppliedPrice > 0
		AND (snaps.AutotraderAdvertStatus = 'PUBLISHED'	OR @includeUnPublished = 1)
		
)
, firstlast as
(
	SELECT
	MIN(snaps.Id) as FirstSnapId,
	MAX(snaps.Id) as LastSnapId,
	ads.Id as AdId
	FROM autoprice.VehicleAdverts ads
	INNER JOIN relevantSnaps snaps on snaps.VehicleAdvert_Id = ads.id 
	INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
	WHERE rs.IsActive = 1
	GROUP BY ads.Id
),
--Get more info on each snap
apparentFirstLasts as
(
	SELECT
	FirstSnapId,
	LastSnapId,
	AdId,
	firstsnap.SnapshotDate as FirstDate,
	lastsnap.SnapshotDate as LastDate
	FROM firstlast fl
	INNER JOIN autoprice.VehicleAdvertSnapshots firstsnap on firstsnap.id =  fl.FirstSnapId
	INNER JOIN autoprice.VehicleAdvertSnapshots lastsnap on lastsnap.Id = fl.LastSnapId
	--WHERE lastsnap.ValuationMktAvRetail IS NOT NULL --don't include any with null valuation, no use to us
)

--Find just those ads which have definitely left
SELECT
AdId as VehicleAdvert_Id,
0 as SIV,
0 as HaveSIV,
0 as Profit,
FirstSnapId as FirstVehicleAdvertSnapshot_Id,
LastSnapId as LastVehicleAdvertSnapshot_Id
INTO #mergeSource
FROM apparentFirstLasts
WHERE lastdate < @todayStart  --don't include any ads with last snapshot of today as this will be all those still advertised
AND LastDate > @sixMonthsAgo; -- only those from last 6m

WITH FilteredLeavingPriceItems AS (
    SELECT l.*, ads.StockNumber
    FROM [autoprice].LeavingPriceItems l
    INNER JOIN autoprice.VehicleAdvertSnapshots lastSnap on lastSnap.id = l.LastVehicleAdvertSnapshot_Id
	INNER JOIN autoprice.VehicleAdverts ads ON ads.id = lastSnap.VehicleAdvert_Id AND ads.id = l.VehicleAdvert_Id
    INNER JOIN autoprice.RetailerSites rs ON rs.id = ads.RetailerSite_Id 
    WHERE rs.DealerGroup_Id = @dealerGroupId
	AND rs.IsActive = 1
	AND lastSnap.SnapshotDate > @sixMonthsAgo --pick out last 6m leaving price items.
)

	MERGE INTO FilteredLeavingPriceItems AS Target

    USING 
	(
		--The query that gets the Source, including any joins required
		SELECT *
		FROM #mergeSource

	) 	AS Source
    ON Source.VehicleAdvert_Id = Target.VehicleAdvert_Id --The unique key that joins the two tables
    
	--The actions

    -----------------------------------
	-- For Inserts
	-----------------------------------
    WHEN NOT MATCHED BY Target THEN
        INSERT 
		(
			 [VehicleAdvert_Id],
			[HaveSiv],
			[Siv],
			[Profit],
			[FirstVehicleAdvertSnapshot_Id],
			[LastVehicleAdvertSnapshot_Id]
		) 
        VALUES 
		(
			Source.[VehicleAdvert_Id],
			Source.[HaveSiv],
			Source.[Siv],
			COALESCE(Source.[Profit],0),
			Source.[FirstVehicleAdvertSnapshot_Id],
			Source.[LastVehicleAdvertSnapshot_Id]
		)
    
    -----------------------------------
	-- For Updates
	-----------------------------------
	WHEN MATCHED THEN UPDATE SET
			  Target.FirstVehicleAdvertSnapshot_Id = Source.FirstVehicleAdvertSnapshot_Id,
			  Target.LastVehicleAdvertSnapshot_Id = Source.LastVehicleAdvertSnapshot_Id,
			  Target.[VehicleAdvert_Id] = Source.[VehicleAdvert_Id],
				Target.[HaveSiv] = Source.[HaveSiv],
				Target.[Siv] = Source.[Siv],
				Target.[Profit] = COALESCE(Source.[Profit],0)

		
		-----------------------------------
		-- For Removed
		-----------------------------------
		WHEN NOT MATCHED BY SOURCE AND TARGET.StockNumber <> 'LEGACY' THEN DELETE
OPTION (RECOMPILE);

DROP TABLE #mergeSource

END

GO