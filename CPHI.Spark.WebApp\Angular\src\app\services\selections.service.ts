import { EventEmitter, Injectable } from '@angular/core';
import {
  CitNowWipsAndVidsSummaryItem, DailyWipCountPerPerson, EvhcPersonMissedStat, EvhcPersonStat, PartsStockCoverSiteRow, PartsStockDeadDormantBySiteItem, ServiceSalesByPersonItem, TechDailyHoursItem
} from '../model/afterSales.model';
import {
  AppUser, BonusAgeing, Comparative,
  DebtType,
  DebtsBonusesSummaryRow,
  DebtsDebtsSummaryRow, DebtsSitesSummaryRow,
  Department, FAndISalesExecRow, FAndISiteRow, LabelAndAmount, Month,
  PartsSummarySelections, Person, ServiceSummarySelections, SiteVM,
  VocSiteRow, WipAgeingComparator, WipDetailRow, WipSiteRow, YearMonth
} from '../model/main.model';
import {
  AgeingOption, AsAt, Deal, LateCostOption, OrderOption, StockModalMerchRow, StockModalRow, StockReport,
} from '../model/sales.model';

import { BookingsSummaryChoices } from "../model/BookingsSummaryChoices";
import { SitesTableComponent } from '../pages/debts/tables/sitesTable.component';
import { PartsStockAgeBySite } from '../pages/partsStock/PartsStockAgeBySite';
import { PersonLeague } from '../pages/performanceLeague/performanceLeague.model';

import { DealerGroupVM } from '../model/DealerGroup';
import { ConstantsService } from './constants.service';
import { RetailerSiteVM } from '../model/RetailerSiteVM';



@Injectable({
  providedIn: 'root'
})
export class SelectionsService {


  //newUserAndLoginEmitter: EventEmitter<UserAndLoginKILL>;
  confirmModalEmitter: EventEmitter<boolean> = new EventEmitter()
  inputModalEmitter: EventEmitter<string> = new EventEmitter()


  commentsChanged: EventEmitter<void> = new EventEmitter();
  
  clickedTodayOrders: EventEmitter<void> = new EventEmitter();

  // userSetup: {
  //   rowData: UserAndLoginKILL[]
  // }

  user: AppUser;
  userSite: SiteVM;
  userSites: SiteVM[]; 
  userSiteIds: number[];

  retailerSiteId:RetailerSiteVM;

  selectedSites: SiteVM[];
  selectedSitesIds: number[];

  totalSelected: boolean = true;
  onWhiteboard: boolean = false;
  onHandoverDiary: boolean = false;

  spinner: { show: boolean, message?: string, icon?: string } = { show: false, message: 'none', icon: 'none' };
  triggerSpinner: EventEmitter<{ show: boolean, message?: string, icon?: string }> = new EventEmitter();

  screenWidth: number;
  screenHeight: number;
  chartFontsize: number;

  stockReportModal: {
    headerName: string,
    excelExportName: string,
    showUsedColumns: boolean,
    stocks: Array<any>,
    chartLabels: Array<string>,
    chartValues: Array<Array<number>>,
    hoveredBarModel: string,
    overIn30?: any;
    maxOverIn30BarLength?: number;
    totalVehicleCount: number;
  };


  vehiclesAwaitingPrepModal: {
    headerName: string,
    excelExportName: string,
    stocks: Array<any>,
    hoveredBarModel: string,
  };


  partsStock: {
    sites: SiteVM[],
    site: SiteVM,
    partsStockAgeBySiteItems: PartsStockAgeBySite[],
    partsStockByPartByCoverItems: PartsStockCoverSiteRow[],
    partsStockDeadDormantBySiteItems: PartsStockDeadDormantBySiteItem[],
    showGrid: boolean,
    showAgeing: boolean,
    chosenFamilyCodes: string[],
    chosenGroups: string[],
    triggerUpdatedPartsStock: EventEmitter<SiteVM[]>,
  }

  wipReport: {
    ageAtMonthEnd: boolean;
    chosenSiteIds: number[];
    includeZeroWips: boolean;
    regionsRows: WipSiteRow[];
    sitesRows: WipSiteRow[];
    sitesTotal: WipSiteRow[];
    showDetail: boolean;
    wipsChangedEmitter: EventEmitter<boolean>;
    wipsRows: WipDetailRow[];
    wipsRowsFiltered: WipDetailRow[];
  };

  debts: {
    sites: SiteVM[],
    chosenSiteIds: number[],
    debtType: DebtType,
    siteSummaryRows: DebtsSitesSummaryRow[],

    debts: DebtsDebtsSummaryRow[],
    debtsFiltered: DebtsDebtsSummaryRow[],
    bonuses: DebtsBonusesSummaryRow[],
    bonusesFiltered: DebtsBonusesSummaryRow[],
    averageAge: number,
    totalAmount: number,
    ageAtMonthEnd: boolean,
    ageOnDueDate: boolean,
    incomingDebtType?: string,
    siteTableRef: SitesTableComponent,

    sitesDataChangedEmitter: EventEmitter<boolean>,
    debtsDataChangedEmitter: EventEmitter<boolean>,
    bonusesDataChangedEmitter: EventEmitter<boolean>
  };

  debtsTable: {
    totalDebtBalance: number,
    totalDebtCount: number,
    selectedDebtCount: number,
    selectedDebtBalance: number,
    buttonSelections: {
      ageing: LabelAndAmount,
      department: LabelAndAmount,
      debtType: LabelAndAmount,
    },
    gridSelections: {
      filterState: any,
      sortState: any,
    }
    ageings: LabelAndAmount[],
    departments: LabelAndAmount[],
    debtTypes: LabelAndAmount[],
  }

  bonusesTable: {
    totalBonusBalance: number,
    totalBonusCount: number,
    selectedBonusCount: number,
    selectedBonusBalance: number,
    buttonSelections: {
      ageing: BonusAgeing,
    },
    gridSelections: {
      filterState: any,
      sortState: any,
    }
    ageings: BonusAgeing[],
    departments: LabelAndAmount[],
  }

  wipsTable: {
    totalWipBalance: number,
    totalWipCount: number,
    selectedWipCount: number,
    selectedWipBalance: number,
    buttonSelections: {
      ageing: WipAgeingComparator,
    },
    gridSelections: {
      filterState: any,
      sortState: any,
    }
    ageings: WipAgeingComparator[],
    departments: LabelAndAmount[],
  }

  

  salesPerformance: {
    lastInitiated: Date,
    sites: SiteVM[],
    totalSite: SiteVM[],
    department: Department,
    franchises: string[],
    orderOption: OrderOption
    lateCostOption: LateCostOption,
    deliveryDate: {
      dateType: string,
      startDate: Date,
      endDate: Date,
      monthName: string,
      dayName: string,
      amSelectingMonth: boolean,
      amSelectingDay: boolean,
      amSelectingAnytime: boolean,
      amSelectingCustom: boolean,
      lastSelectedMonthStartDate: Date,
      lastSelectedDayStartDate: Date,

    },
    orderDate: {
      startDate: Date,
      endDate: Date,
      monthName: string,
      weekName: string,
      dayName: string,
      amSelectingMonth: boolean,
      amSelectingAnytime: boolean,
      amSelectingCustom: boolean,
      amSelectingWeek: boolean,
      amSelectingDay: boolean,
      lastSelectedMonthStartDate: Date,
      lastSelectedWeekStartDate: Date,
      lastSelectedDayStartDate: Date,
    },
    comparative: Comparative,
    salesPerformanceReportType: string,
    Days?: {
      elapsed: number,
      total: number,
      remaining: number,
    },
    includeTradeUnits: boolean,
    IsCurrentMonth: boolean;
  }



  stockReport: {
    ageingOptions: AgeingOption[],
    ageingOption: AgeingOption,
    asAts: AsAt[],
    asAt: AsAt,
    reports: StockReport[],
    report: StockReport,
    franchises: string[],
    sites: SiteVM[],
    incomingSiteId: number,
    showNarrowGrid: boolean,
    incomingReportNameChoice: string,
    updatedSitesEmitter: EventEmitter<SiteVM[]>,
    useBranchDays: boolean,
  }

  serviceSummary: ServiceSummarySelections;

  bookingsSummary: BookingsSummaryChoices

  partsSummary: PartsSummarySelections;

  //need handoverDiary and PerformanceLeague  

  fAndISummary: {
    lastInitiated: Date,
    deals: Array<Deal>,
    sites: SiteVM[],
    selectedSites: SiteVM[],
    vehicleTypeTypes: string[],
    orderTypeTypes: string[],
    franchises: string[],
    lateCostOption: LateCostOption,
    deliveryDate: {
      startDate: Date,
      endDate: Date,
      monthName: string,
      amSelectingMonth: boolean,
      amSelectingCustom: boolean,
      lastSelectedMonthStartDate: Date,
    },
    sitesRowData: FAndISiteRow[],
    regionalsRowData: FAndISiteRow[],
    sitesRowDataTotal: FAndISiteRow[],
    peopleRowData: FAndISalesExecRow[]
  };


  aftersalesLeague: {
    //showTechs: boolean,
    sites: SiteVM[],
    sitesIds: number[],
    technicians: Person[],
    advisors: Person[],
    showAllSites: boolean
    yearMonth: YearMonth,
    yearMonths: YearMonth[],
    techDailyHoursItems: TechDailyHoursItem[]
    serviceSalesByPersonItems: ServiceSalesByPersonItem[]
    evhcPersonStats: EvhcPersonStat[]
    evhcPersonMissedStats: EvhcPersonMissedStat[]
    citNowWipsAndVidsSummaryItems: CitNowWipsAndVidsSummaryItem[];
    dailyWipCountsPerPerson: DailyWipCountPerPerson[];
    league: PersonLeague;
    leagues: PersonLeague[];
  }

  voc: {
    sites: VocSiteRow[],
    sitesSorted: VocSiteRow[],
    site: SiteVM,
    isSales: boolean,
    numbersHaveChanged: EventEmitter<boolean>,
    sitesIds: number[],
    month: Month
  }


  showSpinnerBackdrop: boolean;
  //showLoading: boolean;
  fullyLoaded: boolean;

  darkModeToggledEmitter: EventEmitter<void> = new EventEmitter();
  userDealerGroups: DealerGroupVM[] ;
  

  constructor(
    public constants: ConstantsService,
  ) {

    //this.newUserAndLoginEmitter = new EventEmitter();



  }


  get userRetailerSite(){
    if(!this.user?.RetailerSiteId){return null;}
    if(!this.constants.RetailerSites){return null;}
    return this.constants.RetailerSites.find(x=>x.Id==this.user.RetailerSiteId)
  }


  initiatePartsStock(reset?: boolean) {
    

    if (!this.partsStock || reset) {
      this.partsStock = {
        sites: this.constants.clone(this.constants.Sites.filter(x => (x.IsParts))),
        site: null,
        partsStockAgeBySiteItems: [],
        partsStockByPartByCoverItems: [],
        partsStockDeadDormantBySiteItems: [],
        showGrid: false,
        showAgeing: true,
        //familyCodes:familyCodes,
        chosenFamilyCodes: [],// this.constants.allFamilyCodes,
        chosenGroups: this.constants.allGroups,
        triggerUpdatedPartsStock: new EventEmitter(),
      }
    }
  }



  // initiateEvhc(reset?: boolean) {
  //   // if (!this.evhc || reset) {

  //     //let sitesCopy = this.constants.clone(this.SitesExtended.filter(x => x.IsService && x.IsActive))
  //     this.evhc = {
  //       workTypes: ['Red Work', 'Amber Work', 'Red and Amber Work'],
  //       workType: 'Red Work',
  //       sites: null,
  //       site: null,
  //       personRows: null,
  //       month: {
  //         startDate: new Date(this.constants.thisMonthStart),
  //         endDate: new Date(this.constants.thisMonthEnd),
  //         name: this.constants.thisMonthStart.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' })
  //       },
  //       numbersHaveChanged: new EventEmitter(),
  //       //EVHCJobSummaryItems: [],
  //       //dailyWipCountsPerPerson: [],
  //       //workConverted: 0.65,

  //     //}
  //   }
  // }

  

  initiateServiceSummary() {

    let channelsString: string[] = [];

    // Remove non-labour channels for Vindis
    if(this.constants.environment.customer == 'Vindis')
    {
        let temp = this.constants.environment.serviceChannels.filter(x => !x.isTotal && x.isLabour);
        temp.forEach(element => {
            channelsString.push(element.name);
        });
    }
    else
    {
        channelsString = [this.constants.environment.serviceChannels[this.constants.environment.serviceChannels.length - 1].name]
    }

    if (!this.serviceSummary) {
      this.serviceSummary = {
        channelNames: channelsString,
        chosenSite: null,
        month: {
          startDate: this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), 1)),
          endDate: this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth() + 1, 0, 0, 0, 0)),
          name: this.constants.appStartTime.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' })
        },
        //sites: this.constants.clone(this.constants.Sites.filter(s => s.IsActive && s.IsEligibleForUser && s.IsService)),
        sitesIds: this.constants.Sites.filter(s => s.IsActive).map(s => s.SiteId),
        summaryHasChangedEvent: new EventEmitter(),
        tableType: this.constants.environment.serviceSummary.defaultTableType,
        tableTypes: this.constants.environment.serviceSummary.tableTypes,
        timeOption: this.constants.environment.serviceSummary.defaultTimeOption,
        timeOptions: this.constants.environment.serviceSummary.timeOptions,
        showDailyView: this.constants.environment.serviceSummary.defaultTableType == 'Daily',
        showTechGroupColumns: this.constants.environment.serviceSummary.showTechGroupColumns
      }
    }
  }


  initiatePartsSummary() {
    if (!this.partsSummary) {

      this.partsSummary = {
        month: {
          startDate: this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), 1)),
          endDate: this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth() + 1, 0, 0, 0, 0)),
          name: this.constants.appStartTime.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' }),
        },
        //sites: null, //set below
        sitesIds: this.constants.Sites.filter(s => s.IsActive).map(s => s.SiteId),
        channelNames: [this.constants.environment.partsChannels[this.constants.environment.partsChannels.length - 1].name],
        chosenChannelTags: this.constants.environment.partsChannels[this.constants.environment.partsChannels.length - 1].channelTags,
        comparative: this.constants.comparatives[0],
        tableType: this.constants.environment.serviceSummary.defaultTableType,
        tableTypes: this.constants.environment.serviceSummary.tableTypes,
        timeOptions: ['MTD', 'WTD', 'Yesterday'],
        timeOption: 'MTD',
        showDailyView: false,
        sitesUpdateTrigger: new EventEmitter(),
        summaryHasChangedEvent: new EventEmitter(),
        chosenSite: null
      }

      // if (this.constants.environment.selectionsService.eligibleForCurrentUserCheck) {
      //   this.partsSummary.sites = this.constants.clone(this.constants.Sites.filter(s => s.IsActive && s.IsParts && s.IsEligibleForUser))
      // } else {
      //   this.partsSummary.sites = this.constants.clone(this.constants.Sites.filter(s => s.IsActive && s.IsParts))
      // }

    }
  }


  initiateStockReport(reset?: boolean) {
    if (!this.stockReport || reset) {

      let nearestQEnd = 3
      if (this.constants.appStartTime.getMonth() + 1 > 9) {
        nearestQEnd = 12
      } else {
        if (this.constants.appStartTime.getMonth() + 1 > 6) {
          nearestQEnd = 9
        } else {
          if (this.constants.appStartTime.getMonth() + 1 > 3) {
            nearestQEnd = 6
          }
        }
      }

      let ageingOptions;
      let ageingOption;

      ageingOptions = this.constants.environment.selectionsService.ageingOptions;
      ageingOption = this.constants.environment.selectionsService.ageingOption;

      this.stockReport = {
        ageingOptions: ageingOptions,
        ageingOption: ageingOption,
        asAts: [
          { description: this.constants.translatedText.Common_Now, date: this.constants.appStartTime, param: 'now' },
          { description: this.constants.translatedText.Common_MonthEnd, date: new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth() + 1, 0), param: 'monthEnd' },
          { description: this.constants.translatedText.Common_QuarterEnd, date: new Date(this.constants.appStartTime.getFullYear(), nearestQEnd, 0), param: 'quarterEnd'  },
        ],
        asAt: { description: this.constants.translatedText.Common_Now, date: this.constants.appStartTime, param: 'now'  },
        reports: [
          { name: this.constants.translatedText.Dashboard_PartsStock_StockGraphs, showInMenu: this.constants.environment.stockReport.seeStockGraphsReport },
          { name: this.constants.translatedText.Dashboard_PartsStock_StockByAge, showInMenu: this.constants.environment.stockReport.seeStockByAgeReport },
          { name: this.constants.translatedText.Dashboard_PartsStock_UsedStock, showInMenu: this.constants.environment.stockReport.seeUsedStockReport, colIds: ['Site', 'ExDemo', 'ExManagement', 'Tactical', 'CoreUsed', 'Total Used', 'SplitByType', 'Trade', 'Total Used Inc Trade'] },
          { name: this.constants.translatedText.Dashboard_PartsStock_AllStock, showInMenu: this.constants.environment.stockReport.seeAllStockReport, colIds: ['Site', 'New', 'Demo', 'ExDemo', 'ExManagement', 'Tactical', 'CoreUsed', 'Total', 'TradeBeforeTotal',] },
          { name: this.constants.translatedText.Dashboard_PartsStock_UsedMerchandising, showInMenu: this.constants.environment.stockReport.seeUsedMerchandisingReport, colIds: [] },
          { name: this.constants.translatedText.Dashboard_PartsStock_OverageStock, showInMenu: this.constants.environment.stockReport.seeOverageStockReport, colIds: ['Site', 'NewOverage', 'DemoOverage', 'ExDemoOverage', 'ExManagementOverage', 'TacticalOverage', 'CoreUsedOverage', 'TradeOverage', 'TotalAgedOverage'] },
        ],
        report: null,
        incomingSiteId: null,
        franchises: this.constants.FranchiseCodes,
        sites: this.constants.Sites.filter(s => s.IsActive && (s.IsRetail || s.SiteDescription == 'Northern Fleet') && s.IsEligibleForUser),
        showNarrowGrid: false,
        incomingReportNameChoice: null,
        updatedSitesEmitter: new EventEmitter(),
        useBranchDays: false
      }

      this.stockReport.report = this.stockReport.reports.find(x => x.name === this.constants.translatedText[this.constants.environment.stockReport.initialStockReport])

    }
  }

  initiateDebts(debtType:DebtType) {
    let sitesNormal = this.constants.clone(this.userSites.filter(x => !x.IsNissan))

    if (!this.debts ) {
      this.debts = {
        ageAtMonthEnd: false,
        ageOnDueDate: true,
        sites: sitesNormal,
        chosenSiteIds: sitesNormal.map(x => x.SiteId),
        debtType: debtType,
        siteSummaryRows: null,
        debts: null,
        debtsFiltered: null,
        bonuses: null,
        bonusesFiltered: null,
        averageAge: 0,
        totalAmount: 0,
        incomingDebtType: null,
        siteTableRef: null,

        sitesDataChangedEmitter: new EventEmitter(),
        debtsDataChangedEmitter: new EventEmitter(),
        bonusesDataChangedEmitter: new EventEmitter()
      }
    }

    this.debts.chosenSiteIds = this.debts.sites.filter(s => s.IsActive == true).map(s => s.SiteId);
  }

 


  initiateFAndISummary(reset?: boolean) {

    let orderTypes: string[];

    if(this.constants.environment.customer == 'Vindis')
    {
      orderTypes = this.constants.orderTypeTypesNoTrade.filter(x => x !== 'Motability' && x != 'Fleet');
    }
    else {
      orderTypes = this.constants.orderTypeTypesNoTrade.filter(x => x !== 'Motability');
    }

    if (!this.fAndISummary || reset) {

      this.fAndISummary = {
        lastInitiated: new Date(),
        deals: [],
        sites: this.constants.clone(this.constants.sitesActiveSales.filter(x => x.IsEligibleForUser)),
        selectedSites: [],
        vehicleTypeTypes: this.constants.vehicleTypeTypes,
        orderTypeTypes: orderTypes,
        franchises: this.constants.FranchiseCodes,
        lateCostOption: this.constants.lateCostOptions[1],
        deliveryDate: {
          startDate: new Date(this.constants.thisMonthStart),
          endDate: new Date(this.constants.thisMonthEnd),
          monthName: this.constants.appStartTime.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' }),
          amSelectingMonth: true,
          amSelectingCustom: false,
          lastSelectedMonthStartDate: new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), 1),
        },
        sitesRowData: null,
        regionalsRowData: null,
        sitesRowDataTotal: null,
        peopleRowData: null
      }
    }
  }

  initiateVehiclesAwaitingPrepModal(stocksIncoming: StockModalMerchRow[], header: string, excelExportName: string) {

    //data for the table
    let stocks: Array<StockModalMerchRow> = stocksIncoming.sort((a, b) => { return a.Age - b.Age })

    this.vehiclesAwaitingPrepModal = {
      headerName: header,
      stocks: stocks,
      excelExportName: excelExportName,
      hoveredBarModel: null
    }

  }

  initiateStockReportModal(skipProcessingGoingOver: boolean, stocksIncoming: StockModalRow[], stockOverIn30: StockModalRow[][], header: string, showUsedColumns: boolean, excelExportName: string) {

    //data for the table
    let stocks: Array<StockModalRow> = stocksIncoming.sort((a, b) => { return a.Age - b.Age })

    //data for the graph
    //find unique models and their counts
    let uniqueModels = [...new Set(stocks.map(e => e.Model))];
    let modelsAndCounts = []
    uniqueModels.forEach(model => {
      modelsAndCounts.push(
        { model: model, count: stocks.filter(s => s.Model === model).length }
      )
    })

    modelsAndCounts.sort((a, b) => b.count - a.count)
    let topModelsAndCounts = modelsAndCounts.slice(0, 20);

    let chartValues = []
    chartValues.push(topModelsAndCounts.map(e => e.count));

    let maxBarLength = 0;

    // Note every day of the thirty contains one empty to populate the graph
    if (!skipProcessingGoingOver && stockOverIn30) {
      stockOverIn30.forEach(b => {
        if (b.length > 1 &&  b.length > maxBarLength) { maxBarLength = b.length }
      })
    }

    let totalVehicleCount = 0;

    //set object
    if (!skipProcessingGoingOver && stockOverIn30) {
      stockOverIn30.forEach(x => {
        totalVehicleCount = totalVehicleCount + x.length;
      })
    }

    this.stockReportModal = {
      headerName: header,
      stocks: stocks,
      excelExportName: excelExportName,
      chartLabels: topModelsAndCounts.map(e => e.model),
      chartValues: chartValues,
      hoveredBarModel: null,
      overIn30: stockOverIn30,
      totalVehicleCount: totalVehicleCount - 30,
      maxOverIn30BarLength: maxBarLength,
      showUsedColumns: showUsedColumns,
    }

  }


  initiateAftersalesLeague(reset?: boolean) {

    let yearMonths: YearMonth[] = [];

    let startDate = new Date(this.constants.thisMonthStart.getFullYear(), this.constants.lastMonthStart.getMonth(), 1);

    for (let i = 0; i < 7; i++) {
      let latestDate = new Date(startDate.getFullYear(), startDate.getMonth() + i, 1);
      yearMonths.push({
        label: latestDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' }),
        stringValue: latestDate.getFullYear().toString() + (latestDate.getMonth() + 1).toString(),
        startDate: latestDate,
        isSelected: i == 12,
      })
    }

    yearMonths.push({
      label: `YTD ${yearMonths[3].label}`,
      startDate: new Date(this.constants.thisMonthStart.getFullYear(), 0, 1),
      stringValue: "YTD",
      isSelected: false,
    })

    yearMonths[1].isSelected = true

    if (!this.aftersalesLeague || reset) {

      this.aftersalesLeague = {
        //showTechs: true,
        sites: this.constants.clone(this.userSites),
        sitesIds: this.userSites.map(x => x.SiteId),
        technicians: [],
        advisors: [],
        showAllSites: false,
        yearMonth: yearMonths[1],
        yearMonths: yearMonths,
        techDailyHoursItems: [],
        serviceSalesByPersonItems: [],
        evhcPersonStats: [],
        evhcPersonMissedStats: [],
        citNowWipsAndVidsSummaryItems: [],
        dailyWipCountsPerPerson: [],

        league: null,
        leagues: [],


      }
    }
  }

  initiateVoc(reset?: boolean) {

    //find the most recent month for which we have sales data

    let mostRecentSalesScores: Date = new Date() //todo fix this
    let mostRecentSalesDataQuality: Date = new Date() //todo fix this

    let mostRecentDate = new Date(Math.min(mostRecentSalesScores.getTime(), mostRecentSalesDataQuality.getTime()))
    let mostRecentMonthEndDate = new Date(mostRecentDate.getFullYear(), mostRecentDate.getMonth() + 1, 0);

    if (!this.voc || reset) {
      this.voc = {
        sitesIds: [this.userSite.SiteId],
        sites: null,
        sitesSorted: [],
        site: null,
        isSales: true,
        numbersHaveChanged: new EventEmitter(),
        month: {
          startDate: new Date(mostRecentDate),
          endDate: new Date(mostRecentMonthEndDate),
          name: mostRecentDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' })
        },
      };



    }

  }



  getGridRowHeight(defaultValue: number) {
    if (this.screenWidth < 1280) { return defaultValue * .75 }
    return defaultValue;
  }

  getChartFontSize() {
    return parseFloat(getComputedStyle(document.body).fontSize);
    // const appFontSizeSmall: number = (appFontSize * 7) / 16;
    // const appFontSizeLarge: number = (appFontSize * 11) / 16;

    // if (this.screenWidth < 1280) { return appFontSizeSmall; }
    // return appFontSizeLarge;
  }


}
