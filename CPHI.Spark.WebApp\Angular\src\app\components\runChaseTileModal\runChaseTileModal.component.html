<ng-template #modalRef let-modal>

  <div class="modal-header">
      <h4 id="modal-basic-title" class="modal-title">
       {{page}}
      </h4>
       <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    
    <div [ngClass]="constants.environment.customer" class="modal-body">
          <div id="run-chase-chart">
            <div id="chartHolder">
              <runChaseChartLarge [data]="data">
              </runChaseChartLarge>
            </div>
          </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">
        {{ constants.translatedText.Close }}
      </button>
    </div>

</ng-template>