﻿using CPHI.Spark.Model.Services;
using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
    public class GetCompetitorListingParams
    {
        public int advertiserId { get; set; }
        public string searchId { get; set; }
        public int radius { get; set; }
        public string postcode { get; set; }
        public string token { get; set; }
        public int plateRange { get; set; }
    }

    public class GetCompetitorListingNewParams
    {
        //public string searchId { get; set; } //might be null, if we're coming from a vehicle we loaded from DMS stock, or a valuation
        public int? advertId { get; set; }  // from here we can collect the standardMake, model etc.   We only have this if we're coming from an ad, or a dms stock item loaded into ads
        public int? valuationId { get; set; } // from here we can collect the standardMake, model etc.   We only have this if we have a valuation

        public int advertiserId { get; set; }
        public int radius { get; set; }
        public string postcode { get; set; }
        public int mileageRange { get; set; }
        public string token { get; set; }


        //based on these we can workout the min and max plates
        //--------------------------------------------------
        public int plateRange { get; set; }
        public string registration { get; set; }
        //--------------------------------------------------
    }



    public class CompetitorSearchParams
    {

        public CompetitorSearchParams() { }
        public CompetitorSearchParams(ATNewVehicleGet vehicle, RetailerSite retailerSite, TokenResponse token, int radiusIn)
        {

            int currentPlate = AutoPriceMinMaxPlateService.GetPlateFromFirstReg(vehicle.vehicle.firstRegistrationDate);
            (int minPlate, int maxPlate) minMaxPlate = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, retailerSite.CompetitorPlateRange);
            minPlate = minMaxPlate.minPlate;
            maxPlate = minMaxPlate.maxPlate;



            if (vehicle.vehicle.make == null) { throw new System.Exception("Null vehicle make"); }
            if (vehicle.vehicle.model == null) { throw new System.Exception("Null vehicle model"); }
            //if (vehicle.vehicle.trim == null) { throw new System.Exception("Null vehicle trim"); }
            if (vehicle.vehicle.transmissionType == null) { throw new System.Exception("Null vehicle transmission"); }
            if (vehicle.vehicle.fuelType == null) { throw new System.Exception("Null vehicle fuel type"); }
            if (vehicle.vehicle.bodyType == null) { throw new System.Exception("Null vehicle body type"); }
            if (vehicle.vehicle.drivetrain == null) { throw new System.Exception("Null vehicle drivetrain"); }
            if (vehicle.vehicle.doors == 0) { throw new System.Exception("0 vehicle doors, seems unlikely"); }


            //stuff for the vehicle, we get from the underlying vehicle.vehicle or valuation
            standardMake = vehicle.vehicle.make;
            standardModel = vehicle.vehicle.model;

            standardTransmissionType = vehicle.vehicle.transmissionType;
            standardFuelType = vehicle.vehicle.fuelType;
            standardBodyType = vehicle.vehicle.bodyType;
            standardDrivetrain = vehicle.vehicle.drivetrain;
            doors = vehicle.vehicle.doors.ToString();

            if (vehicle.vehicle.trim != null)
            {
                standardTrim = vehicle.vehicle.trim;
            }
            if (vehicle.vehicle.badgeEngineSizeLitres != null)
            {
                minBadgeEngineSizeLitres = ((decimal)vehicle.vehicle.badgeEngineSizeLitres).ToString();
                maxBadgeEngineSizeLitres = ((decimal)vehicle.vehicle.badgeEngineSizeLitres).ToString();
            }
            if (vehicle.vehicle.enginePowerBHP != null)
            {
                maxEnginePowerBHP = vehicle.vehicle.enginePowerBHP.ToString();
            }

            //stuff about the retailer; or the search parameters 
            advertiserId = retailerSite.RetailerId;
            radius = radiusIn;
            postcode = retailerSite.Postcode;
            selfRegToExclude = vehicle.vehicle.registration;

            if (vehicle.links?.competitors?.href != null)
            {
                CompetitorLink = vehicle.links.competitors.href;
            }

            this.token = token;

        }
        public CompetitorSearchParams(AdvertParamsForStrategyCalculator strategyParams, int radiusIn)
        {
            if (strategyParams.firstRegisteredDate != null)
            {
                var currentPlate = AutoPriceMinMaxPlateService.GetPlateFromFirstReg(strategyParams.firstRegisteredDate);
                var minMaxPlate = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, strategyParams.PlateRange);
                minPlate = minMaxPlate.minPlate;
                maxPlate = minMaxPlate.maxPlate;
            }
            else
            {
                //don't know first registered so get from the reg
                if (strategyParams.VehicleReg.Length >= 4 && int.TryParse(strategyParams.VehicleReg.Substring(2, 2), out int currentPlate))
                {

                    var minMaxPlate = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, strategyParams.PlateRange);
                    minPlate = minMaxPlate.minPlate;
                    maxPlate = minMaxPlate.maxPlate;

                }
                else
                {
                    // Couldn't get plate from reg
                    throw new System.Exception("Could not get currentPlate from first reg'd date or vehicle reg");
                }

            }

            if (strategyParams.Make == null) { throw new System.Exception("Null vehicle make"); }
            if (strategyParams.Model == null) { throw new System.Exception("Null vehicle model"); }
            //if (strategyParams.Trim == null) { throw new System.Exception("Null vehicle trim"); }
            if (strategyParams.TransmissionType == null) { throw new System.Exception("Null vehicle transmission"); }
            if (strategyParams.FuelType == null) { throw new System.Exception("Null vehicle fuel type"); }
            if (strategyParams.BodyType == null) { throw new System.Exception("Null vehicle body type"); }
            if (strategyParams.Drivetrain == null) { throw new System.Exception("Null vehicle drivetrain"); }
            if (strategyParams.Doors == "0") { throw new System.Exception("0 vehicle doors, seems unlikely"); }

            standardMake = strategyParams.Make;
            standardModel = strategyParams.Model;
            standardTransmissionType = strategyParams.TransmissionType;
            standardFuelType = strategyParams.FuelType;
            standardBodyType = strategyParams.BodyType;
            standardDrivetrain = strategyParams.Drivetrain;
            doors = strategyParams.Doors;

            if (strategyParams.Trim != null)
            {
                standardTrim = strategyParams.Trim;
            }
            if (strategyParams.BadgeEngineSizeLitres != null)
            {
                minBadgeEngineSizeLitres = strategyParams.BadgeEngineSizeLitres;
                maxBadgeEngineSizeLitres = strategyParams.BadgeEngineSizeLitres;
            }
            if (strategyParams.MaxEnginePowerBHP != null)
            {
                maxEnginePowerBHP = strategyParams.MaxEnginePowerBHP;
            }
            advertiserId = strategyParams.RetailerSiteRetailerId;
            radius = radiusIn;
            postcode = strategyParams.Postcode;
            selfRegToExclude = strategyParams.VehicleReg;
            CompetitorLink = strategyParams.CompetitorLink;
            token = strategyParams.Token;
        }
        public CompetitorSearchParams(VehicleAdvertDetail advert, RetailerSite retailerSite, TokenResponse token, int radiusIn, string postcodeIn)
        {
            //int currentPlate = int.Parse(advert.VehicleReg.Substring(2, 2));

            if (advert.FirstRegisteredDate != null)
            {
                var currentPlate = AutoPriceMinMaxPlateService.GetPlateFromFirstReg(advert.FirstRegisteredDate);
                var minMaxPlate = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, retailerSite.CompetitorPlateRange);
                minPlate = minMaxPlate.minPlate;
                maxPlate = minMaxPlate.maxPlate;
            }
            else
            {
                //don't know first registered so get from the reg
                if (advert.VehicleReg.Length >= 4 && int.TryParse(advert.VehicleReg.Substring(2, 2), out int currentPlate))
                {

                    var minMaxPlate = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, retailerSite.CompetitorPlateRange);
                    minPlate = minMaxPlate.minPlate;
                    maxPlate = minMaxPlate.maxPlate;

                }
                else
                {
                    // Couldn't get plate from reg
                    throw new System.Exception("Could not get currentPlate from first reg'd date or vehicle reg");
                }

            }
            if (advert.Make == null) { throw new System.Exception("Null vehicle make"); }
            if (advert.Model == null) { throw new System.Exception("Null vehicle model"); }
            //if (advert.Trim == null) { throw new System.Exception("Null vehicle trim"); }
            if (advert.TransmissionType == null) { throw new System.Exception("Null vehicle transmission"); }
            if (advert.FuelType == null) { throw new System.Exception("Null vehicle fuel type"); }
            if (advert.BodyType == null) { throw new System.Exception("Null vehicle body type"); }
            // if (advert.Drivetrain == null) { throw new System.Exception("Null vehicle drivetrain"); }
            //if (advert.Doors == 0) { throw new System.Exception("0 vehicle doors, seems unlikely"); }

            CompetitorLink = advert.CompetitorLink;

            //stuff for the vehicle, we get from the underlying advert or valuation
            standardMake = advert.Make;
            standardModel = advert.Model;
            if (advert.Trim != null)
            {
                standardTrim = advert.Trim;
            }
            standardTransmissionType = advert.TransmissionType;
            standardFuelType = advert.FuelType;
            standardBodyType = advert.BodyType;
            if(advert.Drivetrain!=null){
                standardDrivetrain = advert.Drivetrain;
            }
            doors = advert.Doors.ToString();

            if (advert.BadgeEngineSizeLitres != null)
            {
                minBadgeEngineSizeLitres = advert.BadgeEngineSizeLitres;
                maxBadgeEngineSizeLitres = advert.BadgeEngineSizeLitres;
            }
            if (advert.EnginePowerBHP != null)
            {
                maxEnginePowerBHP = advert.EnginePowerBHP;
            }

            //stuff about the retailer; or the search parameters 
            advertiserId = advert.RetailerSiteRetailerId;
            radius = radiusIn;
            postcode = postcodeIn;
            selfRegToExclude = advert.VehicleReg;


            this.token = token;
        }



        public CompetitorSearchParams(VehicleAdvertWithRating advert, RetailerSite retailerSite, TokenResponse token, int radiusIn, string competitorLink)
        {
         if (advert.FirstRegisteredDate != null)
         {
            var currentPlate = AutoPriceMinMaxPlateService.GetPlateFromFirstReg(advert.FirstRegisteredDate);
            var minMaxPlate = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, retailerSite.CompetitorPlateRange);
            minPlate = minMaxPlate.minPlate;
            maxPlate = minMaxPlate.maxPlate;
         }
         else
         {
            //don't know first registered so get from the reg

            if (advert.OwnershipCondition != "New")
            {
               if (advert.VehicleReg.Length >= 4 && int.TryParse(advert.VehicleReg.Substring(2, 2), out int currentPlate))
               {

                  var minMaxPlate = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, retailerSite.CompetitorPlateRange);
                  minPlate = minMaxPlate.minPlate;
                  maxPlate = minMaxPlate.maxPlate;

               }
               else
               {
                  // Couldn't get plate from reg
                  throw new System.Exception("Could not get currentPlate from first reg'd date or vehicle reg");
               }

            }
            else if (advert.OwnershipCondition == "New")
            {
               var currentPlate = AutoPriceMinMaxPlateService.GetPlateFromFirstReg(DateTime.Now);
               var minMaxPlate = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, retailerSite.CompetitorPlateRange);
               minPlate = minMaxPlate.minPlate;
               maxPlate = minMaxPlate.maxPlate;
            }

         }



         if (advert.Make == null) { throw new System.Exception("Null vehicle make"); }
            if (advert.Model == null) { throw new System.Exception("Null vehicle model"); }
            //if (advert.Trim == null) { throw new System.Exception("Null vehicle trim"); }
            if (advert.TransmissionType == null) { throw new System.Exception("Null vehicle transmission"); }
            if (advert.FuelType == null) { throw new System.Exception("Null vehicle fuel type"); }
            if (advert.BodyType == null) { throw new System.Exception("Null vehicle body type"); }
            if (advert.Drivetrain == null) { throw new System.Exception("Null vehicle drivetrain"); }
            if (advert.Doors == 0) { throw new System.Exception("0 vehicle doors, seems unlikely"); }
            //if (advert.BadgeEngineSizeLitres == null) { throw new System.Exception("Null vehicle badge engine size"); }


            //stuff for the vehicle, we get from the underlying advert or valuation
            standardMake = advert.Make;
            standardModel = advert.Model;
            standardTransmissionType = advert.TransmissionType;
            standardFuelType = advert.FuelType;
            standardBodyType = advert.BodyType;
            standardDrivetrain = advert.Drivetrain;
            doors = advert.Doors.ToString();

            CompetitorLink = competitorLink;

            if (advert.Trim != null)
            {
                standardTrim = advert.Trim;
            }

            if (advert.BadgeEngineSizeLitres != null)
            {
                minBadgeEngineSizeLitres = advert.BadgeEngineSizeLitres;
                maxBadgeEngineSizeLitres = advert.BadgeEngineSizeLitres;
            }


            if (advert.EnginePowerBHP != null)
            {
                maxEnginePowerBHP = advert.EnginePowerBHP;
            }

            //stuff about the retailer; or the search parameters 
            advertiserId = advert.RetailerSiteRetailerId;
            radius = radiusIn;
            postcode = retailerSite.Postcode;
            selfRegToExclude = advert.VehicleReg;

            this.token = token;
        }

        //stuff for the vehicle, we get from the underlying advert or valuation
        public string standardMake { get; set; }
        public string standardModel { get; set; }
        public string standardTrim { get; set; }
        public string standardTransmissionType { get; set; }
        public string standardFuelType { get; set; }
        public string standardBodyType { get; set; }
        public string standardDrivetrain { get; set; }
        public string doors { get; set; }
        public string minBadgeEngineSizeLitres { get; set; }
        public string maxBadgeEngineSizeLitres { get; set; }
        public string maxEnginePowerBHP { get; set; }

        //stuff about the retailer, or the search parameters 
        public int advertiserId { get; set; }
        public int radius { get; set; }
        public string postcode { get; set; }
        public int minPlate { get; set; }
        public int maxPlate { get; set; }
        public string selfRegToExclude { get; set; }

        //if we have one of these, set it
        public string CompetitorLink { get; set; }
        //the token, in case we doing multiple retrieves

        public TokenResponse token { get; set; }
    }

}
