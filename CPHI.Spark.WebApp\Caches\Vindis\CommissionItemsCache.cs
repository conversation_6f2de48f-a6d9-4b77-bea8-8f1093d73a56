﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.BusinessLogic.Vindis;
using CPHI.Spark.Model.ViewModels.Vindis;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Vindis
{
    public interface ICommissionItemsCache
    {
        void EmptyCache(string dealerGroupName);
        Task<IEnumerable<CommissionPayoutPersonSummary>> GetCommissionItems(DateTime startOfMonth, bool ignoreAuditPass);
    }


    public class CommissionItemsCache : ICommissionItemsCache
    {
        private readonly IConfiguration config;

        public CommissionItemsCache(IConfiguration config)
        {
            this.config = config;
        }



        public async Task<IEnumerable<CommissionPayoutPersonSummary>> GetCommissionItems(DateTime startOfMonth, bool ignoreAuditPass)
        {
            string ignoreAuditPassStr = ignoreAuditPass ? "true" : "false";
            string cacheName = $"{DealerGroupName.Vindis.ToString()}|commissionItems{startOfMonth.Year}{startOfMonth.Month}{ignoreAuditPassStr}";


            // -----------------------------
            //REMOVE BEFORE PUBLISH
            //return await GetCommissionFromBusinessLogic(startOfMonth, cacheName, ignoreAuditPass); 
            // -----------------------------

            
            //if (!MemoryCache.Default.Contains(cacheName))
            //{
                IEnumerable<CommissionPayoutPersonSummary> rows = await GetCommissionFromBusinessLogic(startOfMonth, cacheName, ignoreAuditPass, DealerGroupName.Vindis);
                FillCache(cacheName, rows);
            //}

            return (IEnumerable<CommissionPayoutPersonSummary>)MemoryCache.Default.GetCacheItem(cacheName).Value;
            
        }


        public void EmptyCache(string dealerGroupName)
        {
            foreach (var item in MemoryCache.Default.AsEnumerable())
            {
                if (item.Key.StartsWith($"{dealerGroupName}|commissionItems"))
                {
                    MemoryCache.Default.Remove(item.Key);
                }
            }
        }







        private async Task<IEnumerable<CommissionPayoutPersonSummary>> GetCommissionFromBusinessLogic(DateTime startOfMonth, string cacheName, bool ignoreAuditPass, DealerGroupName dealerGroup)
        {
            string connectionString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(dealerGroup));
            CommissionLogicService cl = new CommissionLogicService(connectionString);
            return await cl.GetFullStatementForMonth(startOfMonth.Date, ignoreAuditPass, null);
        }


        private void FillCache(string cacheName, object contents)
        {
            lock (MemoryCache.Default)
            {
                DateTime expiry = DateTime.Now.AddMinutes(50);
                MemoryCache.Default.Add(new CacheItem(cacheName, contents), new CacheItemPolicy() { AbsoluteExpiration = expiry });
            }
        }


    }




}

