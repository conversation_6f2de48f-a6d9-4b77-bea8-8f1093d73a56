﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.IO;
using System.Linq;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Threading;
using CPHI.Spark.Model.ViewModels;
using System.Collections.Generic;
using OpenQA.Selenium.Interactions;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.WebScraper.Services;
using CPHI.Spark.Model;
using Dapper;
using System.Data;
using Microsoft.IdentityModel.Tokens;

namespace CPHI.Spark.WebScraper.Jobs
{
   [DisallowConcurrentExecution]
   public class NetDirectorUpdatePriceJob : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(NetDirectorUpdatePriceJob));
      private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.

      public void Execute() { }


      // EMG Mantles site only
      public async Task Execute(IJobExecutionContext context)
      {

         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         try
         {

            // For testing
            //var changes = new List<NetDirectorPriceChange>
            //    {
            //        new NetDirectorPriceChange { Reg = "AK24KWG", NewPrice = 26495, WebsiteStockIdentifier = "Test", AdvertiserId = 0 },
            //        new NetDirectorPriceChange { Reg = "AE74UYN", NewPrice = 21451, WebsiteStockIdentifier = "Test", AdvertiserId = 0 }, // 21450
            //        new NetDirectorPriceChange { Reg = "LD74CFG", NewPrice = 22651, WebsiteStockIdentifier = "Test", AdvertiserId = 0 }, // 22650
            //        new NetDirectorPriceChange { Reg = "AK24YFO", NewPrice = 26495, WebsiteStockIdentifier = "Test", AdvertiserId = 0 }, // Not found?
            //    };

            List<NetDirectorPriceChange> changes = await CreateNetDirectorPriceChangeList();

            if (changes.Count == 0)
            {
               return;
            }

            logger.Info($"Starting NetDirectorUpdatePriceJob... {changes.Count} changes.");

            var service = ChromeDriverService.CreateDefaultService();
            service.HostName = "127.0.0.1";

            ChromeOptions options = ScraperMethodsService.SetChromeOptions("NetDirector", 9999);

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
            _driver.Manage().Cookies.DeleteAllCookies();

            await UpdatePrices(changes);

            _driver.Manage().Cookies.DeleteAllCookies();
            _driver.Quit();
            _driver.Dispose();

            stopwatch.Stop();

            logger.Info($"Finishing NetDirectorUpdatePriceJob...");

         }
         catch (Exception e)
         {
            EmailerService eService = new EmailerService();
            await eService.SendMail($"❌ FAILURE {GetType().Name} ", $"{e.StackTrace}");

            stopwatch.Stop();
            errorMessage = e.ToString();

            logger.Error($"Problem {e.ToString()}");

            _driver.Quit();
            _driver.Dispose();
         }
         finally
         {
            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "WebScraper",
               Customer = "emg",
               Environment = "PROD",
               Task = GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };
            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }

      }


      public async Task UpdatePrices(List<NetDirectorPriceChange> changes)
      {

         AutoTraderStockClient atStockClient = new AutoTraderStockClient(
                            HttpClientFactoryService.HttpClientFactory,
                            ConfigService.AutoPriceConnectionString,
                            ConfigService.AutotraderApiSecret,
                            ConfigService.AutotraderBaseURL
                            );

         AutoTraderApiTokenClient atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         var token = await atTokenClient.GetToken();

         try
         {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            DateTime start = DateTime.Now;

            Login(wait);

            GoToStockPage(wait, start);

            // Example: Output the list to console
            foreach (var change in changes)
            {
               GoToInventory();

               if (change.Reg.IsNullOrEmpty())
               {
                  logger.Info($"Invalid reg. Skipping.");
                  continue;
               }

               SearchText(change);

               // Ensure we have results in the return table
               if (!CheckForResults())
               {
                  logger.Info($"No results found for {change.Reg}. Skipping.");
                  continue;
               }

               // Ensure reg matches before continuing
               if (!CheckForMatchingReg(change.Reg))
               {
                  logger.Info($"Results found but no match found for {change.Reg}. Skipping.");
                  continue;
               }

               // At this point, we have results and a matching reg
               await AmendPrice(change, token, atStockClient, atTokenClient);
            }

         }
         catch (Exception ex)
         {
            logger.Error($"Failed during UpdatePrices:", ex);
            throw ex;
         }
      }

      private async Task AmendPrice(NetDirectorPriceChange change, TokenResponse token, AutoTraderStockClient atStockClient, AutoTraderApiTokenClient atTokenClient)
      {

         Thread.Sleep(2000);

         ClickIntoItem();

         // ExpandPriceDetails();

         try
         {
            bool modified = ModifyVehiclePriceNow(change);

            if (!modified)
            {
               logger.Info($"Not amending prices for {change.Reg} as both already match. Continuing.");
               return;
            }

            bool verified = VerifyChanges(change.NewPrice);

            if (!verified)
            {
               logger.Info($"Not amending list price for {change.Reg} as already matches.");
               return;
            }

            bool saved = SaveVehicleDetails();

            if (!saved)
            {
               logger.Info($"Failed attempting to save changes for {change.Reg}");
               return;
            }
            else
            {
               logger.Info($"Amended price for: {change.Reg} to {change.NewPrice}");
            }

            Thread.Sleep(2000);

            bool verifiedAfterSave = VerifyChanges(change.NewPrice);

            if (!saved)
            {
               logger.Info($"Could not verify changes saved correctly {change.Reg}");
               return;
            }

            // still here, must have been ok, now update Autotrader
            await UpdateAutoTraderForPriceChange(change.WebsiteStockIdentifier, change.NewPrice, change.AdvertiserId, ConfigService.AutotraderBaseURL, token, atStockClient, atTokenClient);
            logger.Info($"Updated price on AT and saved for stock number {change.Reg}, new price: {change.NewPrice}");

         }
         catch (Exception e)
         {
            { }
         }


      }

      private void ExpandPriceDetails()
      {
         try
         {
            var expandPriceDetail = WaitAndFind("//div[contains(@class, 'nd-widget-title-accordion') and @data-target='#widget-accordion-vehicle-prices']//a[contains(@class, 'accordion-toggle')]",
               true);
         }
         catch
         {
            { }
         }
      }


      private static async Task UpdateAutoTraderForPriceChange(string stockId, int newPrice, int advertiserId, string atBaseUrl, TokenResponse atToken, AutoTraderStockClient atStockClient, AutoTraderApiTokenClient tokenClient)
      {
         // This method updates the price on AutoTrader using the AutoTraderStockClient
         if (atStockClient != null)
         {
            try
            {
               // Create the UpdatePriceParams object required by the AutoTraderStockClient
               var updateParams = new UpdatePriceParams
               {
                  WebsiteStockIdentifier = stockId,
                  NewPrice = newPrice,
                  AdvertiserId = advertiserId
               };


               // Call the API to update the price
               atToken = await tokenClient.CheckExpiryAndRegenerate(atToken);
               string result = await atStockClient.UpdatePrice(updateParams, atToken.AccessToken, atBaseUrl);
               logger.Info($"AutoTrader price update result: {result}");
            }
            catch (Exception ex)
            {
               logger.Error($"Failed to update AutoTrader price: {ex.Message}");
            }
         }
         else
         {
            logger.Warn("AutoTrader client not initialized, skipping price update");
         }
      }
      private bool SaveVehicleDetails()
      {
         try
         {
            WaitAndFind("//a[@id='btn-vehicle-detail-save-top']", true);
            return true;
         }
         catch (Exception e)
         {
            return false;
         }
      }

      private bool ModifyVehiclePriceNow(NetDirectorPriceChange change)
      {
         bool modified = false;

         // Vehicle Old Price (Was)
         var div = WaitAndFind("//div[contains(@class, 'control-group') and contains(., 'Vehicle Old Price (Was)')]", false);
         var input = div.FindElement(By.CssSelector("input[name='vehicle[clientdata-price-previous]']"));

         // Get the current list price
         string oldPriceWasStr = input.GetAttribute("value");
         decimal oldPriceWas = decimal.Parse(oldPriceWasStr);
         int oldPriceWasInt = (int)oldPriceWas;

         // Vehicle Price (Now) *
         var div2 = WaitAndFind("//div[contains(@class, 'control-group') and contains(., 'Vehicle Price (Now)')]", false);
         var input2 = div2.FindElement(By.CssSelector("input[name='vehicle[clientdata-price-current]']"));

         string priceNowStr = input2.GetAttribute("value");
         decimal priceNow = decimal.Parse(priceNowStr);
         int priceNowInt = (int)priceNow;

         if (priceNowInt == change.NewPrice)
         {
            logger.Info($"Not amending Vehicle Price (Now) for {change.Reg} as already matches. ({change.NewPrice})");
         }
         else
         {
            // Clear the existing value
            input2.Clear();

            // Set the new price
            input2.SendKeys(change.NewPrice.ToString());

            // Simulate a Tab keypress to move focus away
            Actions actions = new Actions(_driver);
            actions.SendKeys(Keys.Tab).Perform();
            modified = true;

            logger.Info($"Amended price for: {change.Reg} from {priceNowInt} ({oldPriceWasInt}) to {change.NewPrice}");
         }

         return modified;
      }

      private bool VerifyChanges(int newPrice)
      {

         var div = WaitAndFind("//div[contains(@class, 'control-group') and contains(., 'Vehicle Price (Now)')]", false);
         var input = div.FindElement(By.CssSelector("input[name='vehicle[clientdata-price-current]']"));

         // Get the current list price
         string curlistPriceStr = input.GetAttribute("value");
         decimal curlistPriceDec = decimal.Parse(curlistPriceStr);
         int curlistPriceInt = (int)curlistPriceDec;

         if (curlistPriceInt != newPrice)
         {
            return false;
         }

         return true;
      }

      public void ClickIntoItem()
      {
         try
         {
            var link = _driver.FindElement(By.CssSelector("table tbody tr:first-of-type td.vehicle-make-model-col a"));

            // Try standard click
            link.Click();
         }
         catch (Exception ex) when (ex is ElementClickInterceptedException || ex is ElementNotInteractableException)
         {
            // Fallback: click via JavaScript
            var link = _driver.FindElement(By.CssSelector("table tbody tr:first-of-type td.vehicle-make-model-col a"));

            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            js.ExecuteScript("arguments[0].click();", link);
         }
      }


      private bool CheckForMatchingReg(string reg)
      {
         string firstItemReg = GetFirstVehicleReg();

         return reg == firstItemReg;
      }

      private string GetFirstVehicleReg()
      {
         try
         {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IWebElement stockTable = wait.Until(drv => drv.FindElement(By.Id("stockTable")));

            IWebElement tbody = stockTable.FindElement(By.TagName("tbody"));
            var rows = tbody.FindElements(By.TagName("tr"));

            // First row
            IWebElement firstRow = rows[0];

            // First cell (VRM)
            IWebElement regCell = firstRow.FindElement(By.XPath("td[1]"));

            if (regCell == null)
            {
               return "";
            }

            return regCell.Text.Replace(" ", "").ToUpper().Trim();
         }
         catch (Exception ex)
         {
            return null;
         }
      }


      private static bool CheckForResults()
      {
         try
         {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IWebElement stockTable = wait.Until(drv => drv.FindElement(By.Id("stockTable")));

            IWebElement tbody = stockTable.FindElement(By.TagName("tbody"));

            // Get all rows inside tbody
            var rows = tbody.FindElements(By.TagName("tr"));

            return rows.Count > 0;
         }
         catch (NoSuchElementException)
         {
            return false;
         }
         catch (WebDriverTimeoutException)
         {
            return false;
         }
      }

      private void SearchText(NetDirectorPriceChange change)
      {
         logger.Info($"Searching item: {change.Reg}");

         // There seems to sometimes be issues waiting for this search box
         // even with the WaitAndFind
         Thread.Sleep(1000);

         IWebElement searchBox = WaitAndFind("//input[@id='keywordDetail']", false);

         Thread.Sleep(2000); // Allow page to settle

         searchBox.Click(); // Bring focus to the input
         searchBox.SendKeys(Keys.Control + "a"); // Select all
         searchBox.SendKeys(Keys.Delete); // Delete selected text

         // Optionally retry clear to make doubly sure
         searchBox.Clear();

         // Optional: wait and verify it's truly empty
         if (!string.IsNullOrEmpty(searchBox.GetAttribute("value")))
         {
            throw new Exception("Search box could not be cleared.");
         }

         Thread.Sleep(500);

         searchBox.SendKeys(change.Reg);

         Thread.Sleep(500);

         searchBox.SendKeys(Keys.Enter);

         Thread.Sleep(2000);
      }

      private void Login(WebDriverWait wait)
      {

         IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

         IWebElement loginButton = null;
         int maxWaitSeconds = 300;
         int elementCheckInterval = 5;
         int urlRefreshInterval = 30;
         int elapsedSeconds = 0;

         // Wait for login button
         // Every thirty seconds refresh the URL
         while (elapsedSeconds < maxWaitSeconds)
         {
            try
            {
               loginButton = _driver.FindElement(By.CssSelector("button.login-form-btn.btnSubmit"));
               if (loginButton != null)
                  break;
            }
            catch (NoSuchElementException)
            {
               // Not found, will retry
            }

            if (elapsedSeconds % urlRefreshInterval == 0)
            {
               _driver.Navigate().GoToUrl("https://netdirector.co.uk/");
            }

            Thread.Sleep(elementCheckInterval * 1000);
            elapsedSeconds += elementCheckInterval;
         }

         if (loginButton == null)
         {
            throw new Exception("Login button not found after waiting 5 minutes.");
         }

         Thread.Sleep(2000);

         WaitAndFind("//input [@Id='email']", false).SendKeys("<EMAIL>");
         Thread.Sleep(1000);
         WaitAndFind("//input [@Id='password']", false).SendKeys(ConfigService.NetDirectorPassword);
         Thread.Sleep(1000);

         // Click login
         loginButton.Click();

         Thread.Sleep(5000);
      }

      private void GoToStockPage(WebDriverWait wait, DateTime start)
      {
         // Go to Software Select options
         WaitAndFind("//*[@data-original-title='Software Select']", true);

         Thread.Sleep(1000);

         string originalTab = _driver.CurrentWindowHandle;

         // Select Stock
         WaitAndFind("//span[@class='product-title' and text()='NetDirector Stock']", true);

         // This creates a new tab - close the original to use the new one
         // Wait for the new tab to open
         WebDriverWait waitForNewTab = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
         waitForNewTab.Until(driver => _driver.WindowHandles.Count > 1);

         // Find the new tab (the one that's not the original)
         string newTab = _driver.WindowHandles.First(h => h != originalTab);

         // Close the original tab
         _driver.Close();

         // Switch to the new tab
         _driver.SwitchTo().Window(newTab);
      }

      private void GoToInventory()
      {
         _driver.Navigate().GoToUrl("https://stock.netdirector.co.uk/vehicle-management");
      }

      public IWebElement WaitAndFind(string findXPath, bool andClick = false)
      {
         IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "NetDirectorUpdatePrice", findXPath, andClick);

         return result;
      }

      private static async Task<List<NetDirectorPriceChange>> CreateNetDirectorPriceChangeList()
      {
         using (var dapper = new Dapperr())
         {
            var paramList = new DynamicParameters();

            paramList.Add("DealerGroupId", (int)DealerGroupName.EMGGroup);

            var results = await dapper.GetAllAsync<NetDirectorPriceChange>(
                DealerGroupName.EMGGroup,
                "[autoprice].[GET_PriceChangesToKeyIntoDMS]",
                paramList,
                CommandType.StoredProcedure
            );

            return results
                .Where(r => r.RetailerSiteName != null && r.RetailerSiteName.Contains("Mantles", StringComparison.OrdinalIgnoreCase))
                .ToList();
         }
      }


      private static async Task<List<NetDirectorRepeatedPriceChange>> CreateNetDirectorDayOnDaySamePriceChangeList()
      {
         using (var dapper = new Dapperr())
         {
            var paramList = new DynamicParameters();

            paramList.Add("DealerGroupId", (int)DealerGroupName.EMGGroup);

            var results = await dapper.GetAllAsync<NetDirectorRepeatedPriceChange>(
                DealerGroupName.EMGGroup,
                "[autoprice].[GET_CheckChangesToKeyIntoDMSNotRepeated]",
                paramList,
                CommandType.StoredProcedure
            );

            return results
                .Where(r => r.RetailerSiteName != null && r.RetailerSiteName.Contains("Mantles", StringComparison.OrdinalIgnoreCase))
                .ToList();
         }
      }



   }
}