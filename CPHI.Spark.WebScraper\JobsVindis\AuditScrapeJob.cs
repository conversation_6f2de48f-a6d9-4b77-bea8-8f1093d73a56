﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.IO;
using System.Linq;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Threading;
using log4net.Util;

namespace CPHI.Spark.WebScraper.Jobs
{
    public class AuditScrapeJob : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(AuditScrapeJob));
        private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
        private string customerName;
        private string fileDestination;
        //private string fileDestinationDev;

        public void Execute() { }
        public async Task Execute(IJobExecutionContext context)
        {

            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            fileDestination = ConfigService.FileDestination;
            //fileDestinationDev = ConfigService.FileDestinationDev;
            fileDestination = fileDestination.Replace("{destinationFolder}", "vindis");
            //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "vindis");
            customerName = "Vindis";

            try
            {
                ScraperMethodsService.ClearDownloadsFolder();
                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";

                ChromeOptions options = ScraperMethodsService.SetChromeOptions("VindisAudit", 9229);

                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
                _driver.Manage().Cookies.DeleteAllCookies();
                _driver.Manage().Window.Size = new System.Drawing.Size(1280, 1024);
                GetAudits();

                //string userDirectory = "C:\\cphiRoot\\ChromeProfiles\\VindisAudit";
                //System.IO.DirectoryInfo di = new DirectoryInfo(userDirectory);

                //foreach (FileInfo file in di.GetFiles())
                //{
                //    file.Delete();
                //}
                //foreach (DirectoryInfo dir in di.GetDirectories())
                //{
                //    dir.Delete(true);
                //}

                _driver.Manage().Cookies.DeleteAllCookies();
                Thread.Sleep(7000);

                _driver.Quit();
                _driver.Dispose();
                stopwatch.Stop();

            }
            catch (Exception e)
            {
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");


                stopwatch.Stop();
                errorMessage = e.ToString();
                
                //Emailer.SendMail("Vindis activity scraper failed", $"{e}");

                logger.Error($"Problem {e.ToString()}");

                _driver.Quit();
                _driver.Dispose();

                //Process[] chromeInstances = Process.GetProcessesByName("chrome");
                //foreach (Process p in chromeInstances) p.Kill();
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WebScraper",
                    Customer = "Vindis",
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };
                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
            
        }


        public void GetAudits()
        {

            try
            {
                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
                DateTime start = DateTime.Now;

                // Login Loop
                int maxAttempts = 5;
                int attempt = 0;
                bool isLoggedIn = false;

                while (attempt < maxAttempts)
                {
                    attempt++;
                    logger.Info($"Login attempt {attempt} of {maxAttempts}");

                    isLoggedIn = LoginToiStore(wait);

                    if (isLoggedIn)
                    {
                        logger.Info($"Login successful.");
                        System.Threading.Thread.Sleep(5000);
                        TakeScreenshot(_driver, "LoginSuccess");
                        break; // Exit loop once logged in
                    }

                    if (attempt < maxAttempts)
                    {
                        int sleepTime = (int)Math.Pow(2, attempt) * 1000;
                        logger.Info($"Login failed. Retrying in {sleepTime / 1000} seconds...");
                        System.Threading.Thread.Sleep(sleepTime);
                    }
                }

                if (!isLoggedIn)
                {
                    logger.Info("Failed to log in after maximum attempts.");
                    TakeScreenshot(_driver, "LoginFailure");
                    throw new Exception("Unable to login");
                }

                Thread.Sleep(4000);

                _driver.Navigate().GoToUrl("https://vindis.istoredocs.net/Home/VCore#/search");

                Thread.Sleep(7000);

                DownloadReport(wait, start);
            }
            catch (Exception e)
            {
                TakeScreenshot(_driver, "OtherFailure");
                System.Threading.Thread.Sleep(2000);
                SavePageSourceToFile(_driver, "OtherFailure");
                throw e;
            }
        }


        private bool LoginToiStore(WebDriverWait wait)
        {

            try
            {

                IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

                _driver.Navigate().GoToUrl("https://vindis.istoredocs.net/Home/V2#!/dashboard");

                try
                {
                    IWebElement alreadyLoggedIn = 
                        wait.Until(ExpectedConditions.ElementExists(By.XPath("//div[contains(@class, 'brand-description') and contains(text(), 'Admin')]")));

                    if(alreadyLoggedIn != null)
                    {
                        return true;
                    }
                }
                catch
                {

                }

                System.Threading.Thread.Sleep(5000);

                IWebElement loginButton = wait.Until(ExpectedConditions.ElementExists(By.Id("Username")));

                System.Threading.Thread.Sleep(1000);

                WaitAndFind("//input [@Id='Username']", false).SendKeys("<EMAIL>");
                System.Threading.Thread.Sleep(1000);
                WaitAndFind("//input [@Id='Password']", false).SendKeys(ConfigService.AuditScrapePassword);
                System.Threading.Thread.Sleep(1000);

                // Click login
                WaitAndFind("//body//main//button[text()='Login']", true);

                System.Threading.Thread.Sleep(5000);

                IWebElement adminBar = wait.Until(ExpectedConditions.ElementExists(By.XPath("//div[contains(@class, 'brand-description') and contains(text(), 'Admin')]")));

                return true;
            }
            catch(Exception e) 
            {
                logger.Info($"Error in AuditScrape function LoginToiStore: {e.StackTrace}");

                return false;
            }


        }

        private void DownloadReport(WebDriverWait wait, DateTime start)
        {
            // Go to search page
            System.Threading.Thread.Sleep(5000);

            try
            {
                WaitAndFind("//body/div/nav[1]/div[2]/section/ul/li[7]/a", true);
            }
            // Sometimes we already arrive there, just carry on
            catch
            {

            }

            System.Threading.Thread.Sleep(5000);

            WaitAndFind("//label[text()=\"Start Date\"]", true);

            System.Threading.Thread.Sleep(2000);

            WaitAndFind("//i[contains(@class, 'v-icon notranslate mdi mdi-chevron-left theme--light')]", true); //go back a month

            System.Threading.Thread.Sleep(2000);

            WaitAndFind("//i[contains(@class, 'v-icon notranslate mdi mdi-chevron-left theme--light')]", true); // go back another month

            System.Threading.Thread.Sleep(2000);

            WaitAndFind("//i[contains(@class, 'v-icon notranslate mdi mdi-chevron-left theme--light')]", true); // go back another month

            System.Threading.Thread.Sleep(2000);

            // Click first day for the month three months ago
            WaitAndFind("//div[@class='v-btn__content' and text()='1']", true);

            System.Threading.Thread.Sleep(3000);

            try
            {
                WaitAndFind("//span[@class='v-btn__content' and normalize-space(text())='OK']", true); // Button to select Okay on calendar / Failing here
            }
            // Sometimes this seems to not be required, date is selected and box auto disappears
            catch
            {
                { }
            }

            System.Threading.Thread.Sleep(3000);

            WaitAndFind("//label[text()=\"End Date\"]", true);

            // Click last day of this month
            DateTime date = DateTime.Today;
            int lastDayInThisMonth = DateTime.DaysInMonth(date.Year, date.Month);

            WaitAndFind($"//div[1]/div[3]//div[@class='v-btn__content' and text()='{lastDayInThisMonth.ToString()}']", true);

            try
            {
                WaitAndFind("//div[1]/div[3]//span[@class='v-btn__content' and normalize-space(text())='OK']", true);
            }
            // Sometimes this seems to not be required, date is selected and box auto disappears
            catch
            {
                { }
            }

            SelectDeliveryDate(wait);

            SelectTypes();

            System.Threading.Thread.Sleep(3000);

            // Include Closed
            string noButtonXPath = "//div[@class='col col-6'][2]/button"; 
            bool isNoSelected = IsSwitchSetToNo(noButtonXPath);

            // Ensure it is set to Yes
            if (isNoSelected)
            {
                WaitAndFind("//div[@class='col col-6'][1]/button", true);
            }

            System.Threading.Thread.Sleep(3000);

            // Click search button and wait
            WaitAndFind("//span[@class='v-btn__content' and text()='Search']", true);

            System.Threading.Thread.Sleep(6000);

            WaitAndFind("//span[@class='v-btn__content' and text()='Export']", true);

            System.Threading.Thread.Sleep(3000);

            WaitAndFind("//span[@class='v-btn__content' and text()='Yes']", true);

            System.Threading.Thread.Sleep(5000);

            string downloadButtonPath = "//tr[1]/td[5]/form/button";  // Download button found within 1st row, 5th cell

            System.Threading.Thread.Sleep(5000);

            waitForElement(By.XPath(downloadButtonPath));
            WaitAndFind(downloadButtonPath, true);

            ScraperMethodsService.WaitUntilFileDownloaded("iStoreDocs");

            logger.Info($"Succesfully saved down Audit Report");

            MoveReportToInbound(start);

        }

        private bool IsSwitchSetToNo(string noButtonXPath)
        {
            // Find the "No" button using the XPath
            IWebElement noButton = WaitAndFind(noButtonXPath);

            // Check if the "No" button has the "v-btn--has-bg" class indicating it's selected
            string classAttribute = noButton.GetAttribute("class");
            return classAttribute.Contains("v-btn--has-bg");
        }


        private void SelectDeliveryDate(WebDriverWait wait)
        {
            // Select Delivery Date
            WaitAndFind("//div[@class='v-select__selection v-select__selection--comma' and text()='Created Date']", true);

            WaitAndFind("//div[@class='v-list-item__title' and text()='Delivery Date']", true);
        }

        private void SelectTypes()
        {
            WaitAndFind("//div[contains(@class, 'v-select__slot')]//label[text()='All Types']/following-sibling::div[@class='v-select__selections']/input[@readonly='readonly' and @type='text' and @aria-readonly='false' and @autocomplete='off']", true);

            // New Retail
            WaitAndFind("//div[@class='v-list-item__title' and text()='New Retail Order - Corporate']", true);
            WaitAndFind("//div[@class='v-list-item__title' and text()='New Retail Order']", true);
            WaitAndFind("//div[@class='v-list-item__title' and text()='Used Retail Order']", true);
            WaitAndFind("//div[@class='v-list-item__title' and text()='Used Retail Order - Corporate']", true);
            WaitAndFind("//div[@class='v-list-item__title' and text()='New Motability Order']", true);

            System.Threading.Thread.Sleep(2000);
        }

        private void MoveReportToInbound(DateTime start)
        {
            
            string downloadPath = ConfigService.FileDownloadLocation;

            DirectoryInfo directory = new DirectoryInfo(downloadPath);

            // Get latest file with file name
            FileInfo generatedFile = directory.GetFiles().Where(x => x.LastWriteTime > start && x.Name.Contains("iStoreDocs")).First();

            string newFilePathAndName = downloadPath + @"\" + "Audit" + ".csv";

            //rename the file
            File.Move(generatedFile.FullName, newFilePathAndName);
            logger.Info($"Succesfully changed filename from {generatedFile.FullName} to {newFilePathAndName}");
            //move to the incoming folder
            moveFile("Audit" + ".csv");
        }


        public IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "AuditScrape", findXPath, andClick);
            
            return result;
        }


        public void moveFile(string fileName)
        {
            string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
            string newFilePathAndName = $@"{fileDestination}{prefix}{fileName}";
            //string newFilePathAndNameDev = $@"{fileDestinationDev}{prefix}{fileName}";

            string path = ConfigService.FileDownloadLocation;
            string oldLocation = path + @"\" + fileName;
            
            File.Move(oldLocation, newFilePathAndName); //move them to incoming
            //File.Copy(newFilePathAndName, newFilePathAndNameDev); //copy to dev

            logger.Info($"Moved file from {oldLocation} to {newFilePathAndName}");
        }


        private void NavigateToDownloadPage(WebDriverWait wait)
        {
            WaitAndFind("//button[contains(text(), 'Export')]", true);
            System.Threading.Thread.Sleep(1000);

            WaitAndFind("//div[@Id='em-popup']", false);
            System.Threading.Thread.Sleep(1000);

            WaitAndFind("//button[contains(text(), 'No')]", true);
            System.Threading.Thread.Sleep(1000);

            WaitAndFind("//li[contains(., 'Downloads')]/a", true);
            System.Threading.Thread.Sleep(1000);

            string downloadButtonPath = "//tr[1]/td[5]/form/button";  // Download button found within 1st row, 5th cell

            System.Threading.Thread.Sleep(5000);

            waitForElement(By.XPath(downloadButtonPath));
            WaitAndFind(downloadButtonPath, true);

            WaitAndFind("//tr[1]/td[7]/button", true); // Delete button found within 1st row, 7th cell
            System.Threading.Thread.Sleep(1000);

            WaitAndFind("//button[contains(., 'Yes')]", true);
            System.Threading.Thread.Sleep(1000);
        }

        public static bool elementExists(By by)
        {
            try
            {
                _driver.FindElement(by);
                return true;
            }
            catch (NoSuchElementException)
            {
                return false;
            }
        }

        public static void waitForElement(By by)
        {
            for (int i = 0; i < 30; i++)
            {
                System.Threading.Thread.Sleep(10000); // 10 second wait matches the refresh time on iStoreDocs
                if (elementExists(by))
                {
                    break;
                }
            }

        }

        private static void TakeScreenshot(IWebDriver driver, string error)
        {
            try
            {
                // Generate a timestamped file name
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string screenshotPath = $@"C:\cphiRoot\auditScrapeImages\screenshot_{timestamp}_{error}.png";

                // Take a screenshot using Selenium
                Screenshot screenshot = ((ITakesScreenshot)driver).GetScreenshot();

                // Save screenshot manually using byte array (avoiding ScreenshotImageFormat)
                File.WriteAllBytes(screenshotPath, screenshot.AsByteArray);

                logger.Info($"Screenshot saved at: {screenshotPath}");
            }
            catch (Exception ex)
            {
                logger.Error($"Failed to capture screenshot: {ex.Message}");
            }
        }


        public static void SavePageSourceToFile(IWebDriver driver, string error)
        {
            try
            {
                // Generate a timestamped file name
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string screenshotPath = $@"C:\cphiRoot\auditScrapeImages\fileSource_{timestamp}_{error}.html";

                // Get the page source
                string pageSource = driver.PageSource;

                // Write the page source to the file
                File.WriteAllText(screenshotPath, pageSource);

                Console.WriteLine($"Page source saved to: {screenshotPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving page source: {ex.Message}");
            }
        }
    }


}