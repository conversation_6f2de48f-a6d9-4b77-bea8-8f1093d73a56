import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { forkJoin } from 'rxjs';
import { CphPipe } from 'src/app/cph.pipe';
import { Person, YearMonth } from '../../model/main.model';

import { AutotraderService } from "../../services/autotrader.service";
import { ConstantsService } from "../../services/constants.service";
import { GetDataMethodsService } from '../../services/getDataMethods.service';
import { SelectionsService } from "../../services/selections.service";
import { PersonLeague } from "../performanceLeague/performanceLeague.model";

@Component({
  selector: "app-aftersalesLeague",
  templateUrl: "./aftersalesLeague.component.html",
  styleUrls: ["./aftersalesLeague.component.scss"],
})
export class AftersalesLeagueComponent implements OnInit {

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public analysis: AutotraderService,
    public router: Router,
    public getDataMethods: GetDataMethodsService
  ) { }

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: false });
    this.selections.initiateAftersalesLeague();
    this.initParams();
    this.getData();
  }

  initParams() {
    this.makeLeagues()
  }

  getData() {
    let start = this.selections.aftersalesLeague.yearMonth.startDate
    let end = new Date(start.getFullYear(), start.getMonth() + 1, 0);
    
    let requests = []
    requests.push(this.getDataMethods.getCitNowWipsAndVidsSummaryAftersales(start, end)) //0
    requests.push(this.getDataMethods.getAftersalesLeagueStats(start, end)) //1
    requests.push(this.getDataMethods.getDailyWipCountsPerPerson(start,end)) //2

    forkJoin(requests).subscribe((res:any) => {

      this.selections.aftersalesLeague.citNowWipsAndVidsSummaryItems = res[0]

      //this.constants.exportExcel(this.selections.aftersalesLeague.citNowSummarisedItems)
      
      this.selections.aftersalesLeague.techDailyHoursItems = res[1].techDailyHoursItems
      this.selections.aftersalesLeague.serviceSalesByPersonItems = res[1].serviceSalesByPersonItems ;
      this.selections.aftersalesLeague.evhcPersonStats = res[1].evhcPersonStats;
      this.selections.aftersalesLeague.evhcPersonMissedStats = res[1].evhcPersonMissedStats;


      this.selections.aftersalesLeague.dailyWipCountsPerPerson = res[2];
     
      this.calculatePeoplePerformance();

    },e=>{
      //error
      this.selections.triggerSpinner.next({show:false})
    })
  }


  calculatePeoplePerformance(){
    let uniqueTechIds: number[] = [];
    let uniqueAdvisorIds: number[] = [];

    this.selections.aftersalesLeague.serviceSalesByPersonItems.forEach(x => { if (!uniqueTechIds.includes(x.Technician_Id) && (x.HoursSold!==0 || x.NetSales!==0)) uniqueTechIds.push(x.Technician_Id) })
    this.selections.aftersalesLeague.serviceSalesByPersonItems.forEach(x => { if (!uniqueAdvisorIds.includes(x.ServiceAdvisor_Id) && (x.HoursSold!==0 || x.NetSales!==0)) uniqueAdvisorIds.push(x.ServiceAdvisor_Id) })

    //loop through each technician

    this.selections.aftersalesLeague.technicians = []
    this.selections.aftersalesLeague.advisors = []
    
    var analysePerson = (person:Person, isTech:boolean):void =>  {
      let matchingField:string = isTech ? 'Technician_Id' : 'Advisor_Id';
      let matchingFieldWips:string = isTech ? 'Technician_Id' : 'ServiceAdvisor_Id';

      let personTechDailyHoursItems  = this.selections.aftersalesLeague.techDailyHoursItems.filter(x => x[matchingField] == person.Id)
      let personServiceSalesByPersonItems  = this.selections.aftersalesLeague.serviceSalesByPersonItems.filter(x => x[matchingField] == person.Id)
      let personEvhcPersonStats = this.selections.aftersalesLeague.evhcPersonStats.filter(x => x[matchingField] == person.Id)
      //let personEvhcPersonMissedStats = this.selections.aftersalesLeague.evhcPersonMissedStats.filter(x => x[matchingField] == person.Id)
      let personWips = this.selections.aftersalesLeague.dailyWipCountsPerPerson.filter(x=>x[matchingFieldWips]==person.Id)
     // if(person.Name.includes('Baker'))debugger
      let personField = isTech ? 'Technician_Id' : 'ServiceAdvisor_Id';
      let personcitNowWipsAndVidsSummaryItems = this.selections.aftersalesLeague.citNowWipsAndVidsSummaryItems.filter(x => x[personField] == person.Id)

      let attended = this.constants.sum(personTechDailyHoursItems.map(x=>x.AttendedHrs))
      let worked = this.constants.sum(personTechDailyHoursItems.map(x=>x.WorkedHrs))
      let sold = this.constants.sum(personServiceSalesByPersonItems.map(x=>x.HoursSold))
      
      let vhcDone = this.constants.sum(personEvhcPersonStats.map(x=>x.WipCount))
      let vhcQualifying = this.constants.sum(personWips.map(x=>x.WipCount)) 

      let personCitNowDoneAndSent = this.constants.sum(personcitNowWipsAndVidsSummaryItems.map(x=>x.TotalSent))
      person.AftersalesLeaguePerformance ={
        Rank:0,
        AttendedHours: attended,
        WorkedHours:worked,
        SoldHours:sold,
        CitNowDone:  personCitNowDoneAndSent,
        
        CitNowConversion:this.constants.div(personCitNowDoneAndSent,vhcQualifying),
        Sales:this.constants.sum(personServiceSalesByPersonItems.map(x=>x.NetSales)),
        VHCQualifying: vhcQualifying,
        VHCDone:vhcDone,
        VHCConversion: this.constants.div(vhcDone,vhcQualifying),
        RedWorkFound:this.constants.sum(personEvhcPersonStats.map(x=>x.RedWorkFound)),
        RedWorkSold:this.constants.sum(personEvhcPersonStats.map(x=>x.RedWorkSold)),
        AmberWorkFound:this.constants.sum(personEvhcPersonStats.map(x=>x.AmberWorkFound)),
        AmberWorkSold:this.constants.sum(personEvhcPersonStats.map(x=>x.AmberWorkSold)),
        Efficiency:this.constants.div(sold,worked),
        Productivity:this.constants.div(worked,attended),
      }
    }
    
    uniqueTechIds.forEach(techId => {
      if(!techId)return;
      // let person = this.constants.People[this.constants.binarySearch(this.constants.PeopleIds, techId)]
      // analysePerson(person, true);
      // this.selections.aftersalesLeague.technicians.push(person);
    })
    
    uniqueAdvisorIds.forEach(advisorId => {
      if(!advisorId)return;
      // let person = this.constants.People[this.constants.binarySearch(this.constants.PeopleIds, advisorId)];
      // analysePerson(person, true);
      // this.selections.aftersalesLeague.advisors.push(person);
    })

    //sort
    this.selections.aftersalesLeague.technicians.sort((a, b) => b.AftersalesLeaguePerformance.Sales - a.AftersalesLeaguePerformance.Sales);
    this.selections.aftersalesLeague.advisors.sort((a, b) => b.AftersalesLeaguePerformance.Sales - a.AftersalesLeaguePerformance.Sales);

    //add rank
    this.selections.aftersalesLeague.technicians.forEach((t,i,a)=>{
      a[i].AftersalesLeaguePerformance.Rank = i+1
    })
    this.selections.aftersalesLeague.advisors.forEach((t,i,a)=>{
      a[i].AftersalesLeaguePerformance.Rank = i+1
    })

    let goldTech = this.selections.aftersalesLeague.leagues.find(x=>x.label=='Gold Technician');
    goldTech.people = this.selections.aftersalesLeague.technicians.slice(0,20)

    let silverTech = this.selections.aftersalesLeague.leagues.find(x=>x.label=='Silver Technician');
    silverTech.people = this.selections.aftersalesLeague.technicians.slice(20,50)

    let bronzeTech = this.selections.aftersalesLeague.leagues.find(x=>x.label=='Bronze Technician');
    bronzeTech.people = this.selections.aftersalesLeague.technicians.slice(50,this.selections.aftersalesLeague.technicians.length-50)
    
    let goldAdvisor = this.selections.aftersalesLeague.leagues.find(x=>x.label=='Gold Advisor');
    goldAdvisor.people = this.selections.aftersalesLeague.advisors.slice(0,20)

    let silverAdvisor = this.selections.aftersalesLeague.leagues.find(x=>x.label=='Silver Advisor');
    silverAdvisor.people = this.selections.aftersalesLeague.advisors.slice(20,50)

    let bronzeAdvisor = this.selections.aftersalesLeague.leagues.find(x=>x.label=='Bronze Advisor');
    bronzeAdvisor.people = this.selections.aftersalesLeague.advisors.slice(50,this.selections.aftersalesLeague.advisors.length-50)
   

    this.selections.triggerSpinner.next({show:false})
  }
  
  makeLeagues(){
    this.selections.aftersalesLeague.leagues = []
    //put into leagues  - techs
    //gold
    this.selections.aftersalesLeague.leagues.push(
      {
      label:'Gold Technician',
      cssClass:'GoldTech',
      people: [],
      columns:[0,0],
    },
    {
      label:'Silver Technician',
      cssClass:'SilverTech',
      people: [],
      columns:[0,0],
    },
    {
      label:'Bronze Technician',
      cssClass:'BronzeTech',
      people:[] ,
      columns:[0],
    },
    {
      label:'Gold Advisor',
      cssClass:'GoldAdvisor',
      people:[] ,
      columns:[0,0],
    },
    {
      label:'Silver Advisor',
      cssClass:'SilverAdvisor',
      people: [],
      columns:[0,0],
    },
    {
      label:'Bronze Advisor',
      cssClass:'BronzeAdvisor',
      people: [],
      columns:[0],
    }
    )

    this.selections.aftersalesLeague.league = this.selections.aftersalesLeague.leagues[0]
  }


  selectYearMonth(yearMonth: YearMonth) {
    this.selections.triggerSpinner.next({show:true,message: this.constants.translatedText.Loading})
    setTimeout(()=>{

      this.selections.aftersalesLeague.yearMonth = yearMonth;
      this.getData()
    },10)
  }




  selectLeague(league : PersonLeague){
      this.selections.aftersalesLeague.league = league;
  }
  


}
