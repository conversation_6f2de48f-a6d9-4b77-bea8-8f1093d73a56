//core angular
import { Component, EventEmitter, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
//model and cell renderers
//services
import { ConstantsService } from '../../../../services/constants.service';
import { SelectionsService } from '../../../../services/selections.service';
import { DashboardService } from '../../dashboard.service';
import { DashboardDataItem, DashboardDataPack, DashboardDataParams, DonutData } from '../../dashboard.model';



@Component({
  selector: 'dashboardSalesVindis',
  templateUrl: './dashboardSalesVindis.component.html',
  styleUrls: ['./dashboardSalesVindis.component.scss']
})


export class DashboardSalesVindisComponent implements OnInit {



  dataItems: DashboardDataItem[];
  weekStart: Date;
  dataPack: DashboardDataPack;
  sub: Subscription;

  newDataEmitter:EventEmitter<void>
  donutDataMonthNew: DonutData;
  donutDataMonthFleet: DonutData;
  donutDataMonthUsed: DonutData;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public service: DashboardService

  ) {


  }



  ngOnDestroy() {
    if(!!this.sub)this.sub.unsubscribe();
   }


  ngOnInit() {

    //launch control
    this.initParams()
    this.getData();

    this.sub = this.service.getNewDataTrigger.subscribe(res=>{
      this.getData();
    })

  }

  initParams() {

    this.newDataEmitter = new EventEmitter();
    this.weekStart = this.constants.startOfThisWeek();
    if(!this.service.thisWeeksOrdersStartDateFleet){this.service.thisWeeksOrdersStartDateFleet = this.constants.startOfThisWeek()}
    if(!this.service.thisWeeksOrdersStartDateUsed){this.service.thisWeeksOrdersStartDateUsed = this.constants.startOfThisWeek()}
    if(!this.service.thisWeeksOrdersStartDateNew){this.service.thisWeeksOrdersStartDateNew = this.constants.startOfThisWeek()}
    
    this.dataItems =
      [
        DashboardDataItem.DepartmentDealBreakdown,
        DashboardDataItem.DonutDataSets,
        DashboardDataItem.DailyNetOrdersNew,
        DashboardDataItem.DailyNetOrdersUsed,
        DashboardDataItem.DailyNetOrdersFleet,
        DashboardDataItem.FinanceAndAddOn,
        DashboardDataItem.UsedStockHealth,
        DashboardDataItem.OverageStockSummary,
        DashboardDataItem.ActivityLevelsAndOverdues,
        DashboardDataItem.DeliveredInTime,
       
      ]

  
  }


  getData() {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    let parms: DashboardDataParams = {
      SiteIds: this.service.chosenSites.map(x=>x.SiteId).join(','),
      WeekStart: this.weekStart,
      DataItems: this.dataItems,
      Department:'',
      WeekStartActivitiesTile:this.service.activitesTileStartDate,
      WeekStartOrdersTileNew: this.service.thisWeeksOrdersStartDateNew,
      WeekStartOrdersTileUsed: this.service.thisWeeksOrdersStartDateUsed,
      WeekStartOrdersTileFleet: this.service.thisWeeksOrdersStartDateFleet,
    }

    this.getDataService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {
      
      if(!this.dataPack){
        this.dataPack = res;
        this.donutDataMonthNew = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='New')
        this.donutDataMonthFleet = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Fleet')
        this.donutDataMonthUsed = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Used')
      }else{
        Object.assign(this.dataPack,res)
        Object.assign(this.donutDataMonthNew, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='New'));
        Object.assign(this.donutDataMonthFleet, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Fleet'));
        Object.assign(this.donutDataMonthUsed, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Used'));
      }

      //in order for the template to update the object it passes through the input tag to the child tile.   else the child tile regenerates its data again from the old dataPack
      setTimeout(()=>{
        this.newDataEmitter.emit();
        this.selections.triggerSpinner.next({ show: false });
      },50)
    })

  }





}
