import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from '@angular/router';
import { Subscription } from "rxjs";
import { DistrinetService } from "src/app/pages/distrinet/distrinet.service";
import { OrdersBySiteService } from "src/app/pages/ordersBySite/ordersBySite.service";
import { Department, MenuItemNew, OrderbookTimePeriod, SiteVM } from '../../../../model/main.model';
import { ChartService } from '../../../../services/chart.service';
import { ConstantsService } from '../../../../services/constants.service';
import { SelectionsService } from '../../../../services/selections.service';
import { OrderBookService } from "../../../orderBook/orderBook.service";
import { DonutData, RatioOrdersTileData } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";



@Component({
  selector: 'ordersDonut',
  templateUrl: './ordersDonut.component.html',
  styleUrls: ['./ordersDonut.component.scss']
})
export class OrdersDonutComponent implements OnInit {

  @ViewChild('donutCanvas', { static: true }) donutCanvas: ElementRef;

  @Input() public departmentName: string;
  @Input() public data: RatioOrdersTileData;
  @Input() public newDataEmitter: EventEmitter<void>;
  @Input() public dataSource: string;

  donut: any;
  actualWidth: number;
  budgetWidth: number;
  subscription: Subscription;

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public chart: ChartService,
    public router: Router,
    public service: DashboardService,
    public orderBookService: OrderBookService,
    public distrinetService: DistrinetService,
    public orderRateService: OrdersBySiteService
  ) {

  }

  ngOnInit(): void {

    this.initParams();

    this.subscription = this.newDataEmitter.subscribe(res => {
      this.initParams();
    })

  }


  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
  }



  initParams() {
    this.makeDonut();
  }

  // Go to service to create the chart
  makeDonut() {
    if(this.donut){ this.donut.destroy();}
    this.donut = this.chart.createDonut(this.donutCanvas.nativeElement, this.data.ThisMonth, this.data.ThisMonthTgt);
  }

  isClickableHeader(){
    return true;
  }


  // Below for the clickthroughs
  navigateToRatioOrders() {
    if(!this.isClickableHeader()){return;}

    this.service.chooseDashboardPage('OrderRate')
  }


  navigateAway() {

    if (this.constants.environment.customer == 'RRGSpain' && this.departmentName == 'New') {

      //this.distrinetService.selectFullMonth = true
      this.distrinetService.chosenSnapshot = this.service.chosenMonthStart;
      this.distrinetService.chosenFranchises = this.constants.FranchiseCodes;
      this.distrinetService.chosenOriginTypes = ['Orders'];

      this.distrinetService.filterModel = {
        ActualAnnulmentDate: { type: 'blanks', filter: 0 }
      };

      let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/distrinet');
      if (menuItem) { this.constants.navigateByUrl(menuItem); }  //, 'operationreports'

    } else if (this.constants.environment.customer == 'RRGSpain' && this.departmentName == 'Used') {

      this.orderRateService.orderTypeTypes = this.constants.clone(this.constants.orderTypeTypesNoTrade);
      this.orderRateService.vehicleTypeTypes = ['Used'];
      this.orderRateService.franchises = this.constants.clone(this.service.franchises);
      this.service.chosenPage = this.service.chosenSection.pages.find(x => x.pageName == 'OrderRate');

    } else {

      const department: Department = this.constants.departments.find(x => x.ShortName === this.departmentName);

      let orderTypes: string[] = department.OrderTypeTypes.filter(x => x !== 'Trade' && x !== 'Auction');
      let vehicleTypeTypes: string[] = this.constants.clone(department.VehicleTypeTypes);

      this.orderBookService.initOrderbook();

      this.selections.selectedSitesIds = this.service.chosenSites.map(x => x.SiteId);
      this.orderBookService.orderTypeTypes = orderTypes;
      this.orderBookService.vehicleTypeTypes = vehicleTypeTypes;

      this.orderBookService.salesExecName = null;
      this.orderBookService.salesExecId = null;

      this.orderBookService.accountingDate.startDate = this.constants.deductTimezoneOffset(new Date(this.constants.thisMonthStart));
      this.orderBookService.accountingDate.endDate = this.constants.deductTimezoneOffset(new Date(this.constants.thisMonthEnd));

      this.orderBookService.salesExecName = null;
      this.orderBookService.salesExecId = null;

      this.orderBookService.showOrderbook();
    }

  }



}


