<div class="modal-header">
  <h4 class="modal-title" id="modal-basic-title" (click)="test()">Choose derivative</h4>
  <button type="button" class="close" aria-label="Close" (click)="dismissModal('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div [ngClass]="constants.environment.customer" class="modal-body alertModalBody lowHeight">


  <ng-container *ngIf="chosenVehicleType">
    <div class="d-flex justify-content-between mb-2">
      <h4 id="goBackChoice" (click)="goBackToPreviousStep()">
        <i class="fas fa-angle-left"></i> <u>Back</u>
      </h4>
    </div>

    <h3 class="breadcrumb">
      <span class="hoverToDelete me-1" (mouseover)="maybeRemoveVehicleType = true"
        (mouseleave)="maybeRemoveVehicleType = false" (click)="removeVehicleType()">
        {{ chosenVehicleType }} <button *ngIf="maybeRemoveVehicleType" class="deleteChoice"><i
            class="fas fa-minus-circle text-danger"></i></button>
      </span>
      <span *ngIf="chosenMake" class="hoverToDelete me-1" (mouseover)="maybeRemoveMake = true"
        (mouseleave)="maybeRemoveMake = false" (click)="removeMake()">
        <i class="fas fa-angle-right"></i>
        {{ chosenMake.name }} <button *ngIf="maybeRemoveMake" class="deleteChoice"><i
            class="fas fa-minus-circle text-danger"></i></button>
      </span>
      <span *ngIf="chosenModel" class="hoverToDelete me-1" (mouseover)="maybeRemoveModel = true"
        (mouseleave)="maybeRemoveModel = false" (click)="removeModel()">
        <i class="fas fa-angle-right"></i>
        {{ chosenModel.name }} <button *ngIf="maybeRemoveModel" class="deleteChoice"><i
            class="fas fa-minus-circle text-danger"></i></button>
      </span>
      <span *ngIf="chosenGeneration" class="hoverToDelete me-1" (mouseover)="maybeRemoveGeneration = true"
        (mouseleave)="maybeRemoveGeneration = false" (click)="removeGeneration()">
        <i class="fas fa-angle-right"></i>
        {{ chosenGeneration.name }} <button *ngIf="maybeRemoveGeneration" class="deleteChoice"><i
            class="fas fa-minus-circle text-danger"></i></button>
      </span>
    </h3>
  </ng-container>

  <instructionRow [message]="instructionMessage()"></instructionRow> 

  <div class="tileHolder d-flex flex-row gap-2">

    <!-- Vehicle type -->
    <div *ngIf="vehicleTypesCopy" class="checkboxListWithSearch">
      <h4>Vehicle Type</h4>

      <input type="text" placeholder="Search" [(ngModel)]="vehicleTypesSearchString"
        (ngModelChange)="filterVehicleTypes()">

      <ul #container>
        <li *ngFor="let type of vehicleTypesCopy"
          [ngClass]="{ 'greyed': chosenVehicleType && chosenVehicleType !== type }">
          <button class="custom-checkbox me-2" [ngClass]="{'checked': chosenVehicleType === type }"
            (click)="container.scrollTop = 0; chooseVehicleType(type)">
            <span *ngIf="chosenVehicleType === type">
              <i class="fa fa-check"></i>
            </span>
          </button>
          <span>{{ type }}</span>
        </li>
      </ul>
    </div>

    <!-- Make -->
    <div *ngIf="makesNames" class="checkboxListWithSearch">
      <h4>Make</h4>

      <input type="text" placeholder="Search" [(ngModel)]="makesSearchString" (ngModelChange)="filterMakes()">

      <ul #container>
        <li *ngFor="let make of makesNames" [ngClass]="{ 'greyed': chosenMake && chosenMake.name !== make }">
          <button class="custom-checkbox me-2" [ngClass]="{ 'checked': chosenMake?.name === make }"
            (click)="container.scrollTop = 0; chooseMake(make)">
            <span *ngIf="chosenMake?.name === make">
              <i class="fa fa-check"></i>
            </span>
          </button>
          <span>{{ make }}</span>
        </li>
      </ul>
    </div>

    <!-- Model -->
    <div *ngIf="modelsNames" class="checkboxListWithSearch">
      <h4>Model</h4>

      <input type="text" placeholder="Search" [(ngModel)]="modelsSearchString" (ngModelChange)="filterModels()">

      <ul #container>
        <li *ngFor="let model of modelsNames" [ngClass]="{ 'greyed': chosenModel && chosenModel.name !== model }">
          <button class="custom-checkbox me-2" [ngClass]="{ 'checked': chosenModel?.name === model }"
            (click)="container.scrollTop = 0; chooseModel(model)">
            <span *ngIf="chosenModel?.name === model">
              <i class="fa fa-check"></i>
            </span>
          </button>
          <span>{{ model }}</span>
        </li>
      </ul>
    </div>

    <!-- Generation -->
    <div *ngIf="generationsNames" class="checkboxListWithSearch">
      <h4>Generation</h4>

      <input type="text" placeholder="Search" [(ngModel)]="generationsSearchString"
        (ngModelChange)="filterGenerations()">

      <ul #container>
        <li *ngFor="let generation of generationsNames"
          [ngClass]="{ 'greyed': chosenGeneration && chosenGeneration.name !== generation }">
          <button class="custom-checkbox me-2" [ngClass]="{ 'checked': chosenGeneration?.name === generation }"
            (click)="container.scrollTop = 0; chooseGeneration(generation)">
            <span *ngIf="chosenGeneration?.name === generation">
              <i class="fa fa-check"></i>
            </span>
          </button>
          <span>{{ generation }}</span>
        </li>
      </ul>
    </div>

    <!-- Derivatives -->
    <ng-container *ngIf="facets">
      <div *ngFor="let facet of facets" class="checkboxListWithSearch">
        <h4>{{ facet.Facet | cph:'splitPascalCase':0 }}</h4>

        <input type="text" placeholder="Search" [(ngModel)]="generationsSearchString"
          (ngModelChange)="filterGenerations()">

        <ul>
          <li *ngFor="let choice of facet.Choices">
            <button class="custom-checkbox me-2" [ngClass]="{ 'checked': isCheckBoxChosen(choice, facet) }"
              (click)="chooseFacet(choice, facet)">
              <span *ngIf="isCheckBoxChosen(choice, facet)">
                <i class="fa fa-check"></i>
              </span>
            </button>
            <span>{{ choice }}</span>
          </li>
        </ul>
      </div>
    </ng-container>
  </div>

  <div class="tileHolder d-flex flex-row">
    <!-- Derivatives  -->
    <div *ngIf="derivatives" id="availableDerivatives">

      <h4>Derivatives</h4>


      <!-- [searchListUpdatedEmitter]="service.searchListUpdatedEmitter" -->
      <!-- <typeaheadAndDropdown  [widthEm]="20" [placeholder]="'Quick pick derivative'"
          [searchList]="typeaheadList" 
          [clearInputOnChoiceMade]="true" (chosenItemEmitter)="chooseDerivative($event)" class="me-4">
      </typeaheadAndDropdown> -->

      <button *ngFor="let derivative of derivatives" class="btn btn-primary w-100"
        (click)="chooseDerivative(derivative)">
        {{ derivative.name }}<br>
        {{ derivative.introduced | cph:'date':0 }} to {{ derivative.discontinued|cph:'date':0 }}
      </button>
    </div>

  </div>
</div>








<div class="modal-footer">
  <!-- <button type="button" class="btn btn-primary"
    (click)="closeModal()">{{constants.translatedText.OKUpper}}</button> -->
  <button type="button" class="btn btn-primary"
    (click)="dismissModal('Cancelled')">{{constants.translatedText.Cancel}}</button>
</div>