﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.WebApp.DataAccess;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using MoreLinq;
using System.Threading.Tasks;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.DataAccess;

namespace CPHI.Spark.WebApp.Service
{
    public interface ISalesmanEfficiencyService
    {
        Task<List<SalesmanEfficiencyRow>> GetSalesmanEfficiencyRows(SalesmanEfficiencyParams parms, int userId, Model.DealerGroupName dealerGroup);
    }

    public class SalesmanEfficiencyService : ISalesmanEfficiencyService
    {

        private readonly IPeopleDataAccess peopleDataAccess;
        private readonly DealDataAccess dealDataAccess;

        private List<SalesmanWithRole> allSalesmenThisMonth;
        private List<SalesmanWithRole> allSalesmenLastMonth;
        private List<SalesmanEfficiencyDeal> allDealsThisMonth;
        private List<SalesmanEfficiencyDeal> allDealsLastMonth;

        private ILookup<int, SalesmanEfficiencyDeal> allDealsThisMonthBySalesman;
        private ILookup<int, SalesmanEfficiencyDeal> allDealsLastMonthBySalesman;

        private readonly IConfiguration configuration;




        public SalesmanEfficiencyService(IPeopleDataAccess peopleDataAccess,IUserService userService,IConfiguration configurationIn)
        {
            this.peopleDataAccess = peopleDataAccess;
            this.configuration = configurationIn;

            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            dealDataAccess = new DealDataAccess(_connectionString);
        }


        public async Task<List<SalesmanEfficiencyRow>> GetSalesmanEfficiencyRows(SalesmanEfficiencyParams parms, int userId, Model.DealerGroupName dealerGroup)
        {
            if (parms.ChosenLabel != null)
            {
                //we have chosen a site. 
                return await GetSalesmanEfficiencyRowsForSite(parms, userId, dealerGroup);
            }
            else
            {
                return await GetSalesmanEfficiencyRowsAllSites(parms, userId, dealerGroup);

            }
        }


        private async Task<List<SalesmanEfficiencyRow>> GetSalesmanEfficiencyRowsForSite(SalesmanEfficiencyParams parms, int userId, Model.DealerGroupName dealerGroup)
        {
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup)).Where(x => x.IsSales && x.IsActive);
            await FetchSalesmenAndDeals(parms, dealerGroup);

            List<SalesmanEfficiencyRow> results = new List<SalesmanEfficiencyRow>();
            //work out which people relate to the chosen row
            IEnumerable<SalesmanWithRole> siteSalesmenThisMonth = Enumerable.Empty<SalesmanWithRole>();
            IEnumerable<SalesmanWithRole> siteSalesmenLastMonth = Enumerable.Empty<SalesmanWithRole>();

            //make a reportRow that we'll use later to include a total for the final people table
            ReportRow totalRowAtEnd = new ReportRow();

            //find the right salesmen
            if (parms.IsSite)
            {
                SiteVM siteChosen = sites.First(x => x.SiteDescription == parms.ChosenLabel);
                totalRowAtEnd = new ReportRow(siteChosen.SiteDescription, siteChosen.SiteId, siteChosen.RegionId, true);
                siteSalesmenThisMonth = allSalesmenThisMonth.Where(x => x.SiteId == siteChosen.SiteId);
                siteSalesmenLastMonth = allSalesmenLastMonth.Where(x => x.SiteId == siteChosen.SiteId);
            }
            else if (parms.IsRegion)
            {
                IEnumerable<int> siteIdChosen = sites.Where(x => x.RegionDescription == parms.ChosenLabel).Select(x => x.SiteId);
                totalRowAtEnd = new ReportRow(parms.ChosenLabel, 0, 0, true);
                siteSalesmenThisMonth = allSalesmenThisMonth.Where(x => siteIdChosen.Contains(x.SiteId));
                siteSalesmenLastMonth = allSalesmenLastMonth.Where(x => siteIdChosen.Contains(x.SiteId));
            }
            else
            {
                totalRowAtEnd = new ReportRow(parms.ChosenLabel, 0, 0, true);
                siteSalesmenThisMonth = allSalesmenThisMonth;
                siteSalesmenLastMonth = allSalesmenLastMonth;
            }





            if (parms.Month != "YTD")
            {

                foreach (var person in siteSalesmenThisMonth)
                {
                    ReportRow personRow = new ReportRow(person.Name, person.SiteId, 0, false);
                    SalesmanWithRole personLastMonth = siteSalesmenLastMonth.FirstOrDefault(x => x.PersonId == person.PersonId);
                    //make collection of salesmen last month which actually will just have this salemsan in it from last month (because we're re-using the method used by the make rows for the site)
                    IEnumerable<SalesmanWithRole> thisPersonLastMonth = Enumerable.Empty<SalesmanWithRole>();
                    if (personLastMonth != null)
                    {
                        thisPersonLastMonth = new[] { personLastMonth };
                    }
                    results.Add(createSalesmanEfficiencyRow(personRow, new[] { person }, thisPersonLastMonth, true, false));
                }


                //now add one more row representing the chosen site
                results.Add(createSalesmanEfficiencyRow(totalRowAtEnd, siteSalesmenThisMonth, siteSalesmenLastMonth, false, true));
                return results;
            }

            //if YTD still here, have to do it differently as want to group down by salesman
            foreach (var salesmanGrouping in siteSalesmenThisMonth.ToLookup(x => x.PersonId))
            {
                SalesmanWithRole combinedSalesman = new SalesmanWithRole();
                combinedSalesman.Site = salesmanGrouping.First().Site;
                combinedSalesman.SiteId = salesmanGrouping.First().SiteId;
                combinedSalesman.Name = salesmanGrouping.First().Name;
                combinedSalesman.PersonId = salesmanGrouping.First().PersonId;
                combinedSalesman.Role = salesmanGrouping.First().Role;
                combinedSalesman.Target = salesmanGrouping.Select(x => x.Target).Sum();

                ReportRow personRow = new ReportRow(combinedSalesman.Name, combinedSalesman.SiteId, 0, false);
                //don't care too much about month on month progression so can just pass in this month row twice
                results.Add(createSalesmanEfficiencyRow(personRow, new[] { combinedSalesman }, new[] { combinedSalesman }, true, true));
            }

            //now add one more row representing the chosen site
            results.Add(createSalesmanEfficiencyRow(totalRowAtEnd, siteSalesmenThisMonth, siteSalesmenLastMonth, false, false));
            return results;




        }




        private async Task<List<SalesmanEfficiencyRow>> GetSalesmanEfficiencyRowsAllSites(SalesmanEfficiencyParams parms, int userId, Model.DealerGroupName dealerGroup)
        {
            //get sites
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup)).Where(x => x.IsSales && x.IsActive && x.SiteDescription != "Northern Fleet").OrderBy(x => x.SortOrder);
            await FetchSalesmenAndDeals(parms, dealerGroup);

            List<SalesmanEfficiencyRow> results = new List<SalesmanEfficiencyRow>();
            bool doPriorMonth = parms.Month != "PreviousYear" && parms.Month != "YTD";

            //iterate over sites, find deals, put through 'make row' method
            foreach (var site in sites)
            {
                IEnumerable<SalesmanWithRole> siteSalesmenThisMonth = allSalesmenThisMonth.Where(x => x.SiteId == site.SiteId);
                IEnumerable<SalesmanWithRole> siteSalesmenLastMonth = allSalesmenLastMonth.Where(x => x.SiteId == site.SiteId);
                ReportRow siteRow = new ReportRow(site.SiteDescription, site.SiteId, 0, false);


                results.Add(createSalesmanEfficiencyRow(siteRow, siteSalesmenThisMonth, siteSalesmenLastMonth, false, doPriorMonth));
            }

            //same for regions
            ILookup<int, SiteVM> regions = sites.ToLookup(x => x.RegionId);

            foreach (var region in regions)
            {
                IEnumerable<int> siteIdsThisRegion = region.Select(x => x.SiteId);
                IEnumerable<SalesmanWithRole> regionSalesmenThisMonth = allSalesmenThisMonth.Where(x => siteIdsThisRegion.Contains(x.SiteId));
                IEnumerable<SalesmanWithRole> regionSalesmenLastMonth = allSalesmenLastMonth.Where(x => siteIdsThisRegion.Contains(x.SiteId));
                ReportRow regionRow = new ReportRow(region.First().RegionDescription, 0, region.Key, false);

                results.Add(createSalesmanEfficiencyRow(regionRow, regionSalesmenThisMonth, regionSalesmenLastMonth, false, doPriorMonth));
            }

            //add total
            IEnumerable<int> siteIdsThisUser = sites.Select(x => x.SiteId);
            IEnumerable<SalesmanWithRole> userSalesmenThisMonth = allSalesmenThisMonth.Where(x => siteIdsThisUser.Contains(x.SiteId));
            IEnumerable<SalesmanWithRole> userSalesmenLastMonth = allSalesmenLastMonth.Where(x => siteIdsThisUser.Contains(x.SiteId));
            ReportRow totalRow = new ReportRow("Total", 0, 0, true); ;

            results.Add(createSalesmanEfficiencyRow(totalRow, userSalesmenThisMonth, userSalesmenLastMonth, false, doPriorMonth));

            //return rows
            return results;
        }

        private async Task FetchSalesmenAndDeals(SalesmanEfficiencyParams parms, Model.DealerGroupName dealerGroup)
        {

            if (parms.Month == "YTD" || parms.Month == "PreviousYear")
            {
                DateTime startDate;
                DateTime endDate;

                if (parms.Month == "YTD")
                {
                    startDate = new DateTime(DateTime.UtcNow.Year, 1, 1);
                    endDate = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).AddMonths(1);
                }
                else
                {
                    startDate = new DateTime(DateTime.UtcNow.Year - 1, 1, 1);
                    endDate = new DateTime(DateTime.UtcNow.Year, 1, 1);
                }

                allSalesmenThisMonth = (await peopleDataAccess.GetSalesmenWithRoles(DateTime.UtcNow, parms.Month, dealerGroup)).Where(x => x.Role != "None").ToList();
                foreach (var salesman in allSalesmenThisMonth) { salesman.Target = WorkoutSalesmanTarget(salesman.Role); }
                //crush down into one instance of each salesman
                List<SalesmanWithRole> uniqueSalesmen = new List<SalesmanWithRole>();
                foreach (var distinctSalesmanInstances in allSalesmenThisMonth.ToLookup(x => x.PersonId))
                {
                    SalesmanWithRole thisSalesman = distinctSalesmanInstances.Last();
                    uniqueSalesmen.Add(new SalesmanWithRole()
                    {
                        PersonId = thisSalesman.PersonId,
                        Role = thisSalesman.Role,
                        Target = distinctSalesmanInstances.Select(x => x.Target).Sum(),
                        Name = thisSalesman.Name,
                        SiteId = thisSalesman.SiteId,
                        Site = thisSalesman.Site
                    });
                }
                allSalesmenThisMonth = uniqueSalesmen;

                allSalesmenLastMonth = allSalesmenThisMonth; //don't need last year if we are doing YTD.  Just set to the same.
                allDealsThisMonth = (await dealDataAccess.GetSalesmanEfficiencyDeals(startDate, endDate, parms.VehicleTypeTypes, parms.OrderTypeTypes, parms.Franchises, dealerGroup )).ToList();
                allDealsLastMonth = allDealsThisMonth; // don't need
            }
            else
            {
                DateTime startDate = Convert.ToDateTime(parms.Month);// DateTime.ParseExact(parms.Month, "yyyy-MM-ddThh:mm:ss", CultureInfo.InvariantCulture);
                DateTime endDate = startDate.AddMonths(1);// DateTime.ParseExact(parms.Month, "yyyy-MM-ddThh:mm:ss", CultureInfo.InvariantCulture);
                allSalesmenThisMonth = (await peopleDataAccess.GetSalesmenWithRoles(startDate, parms.Month,dealerGroup)).Where(x => x.Role != "None").ToList();
                allSalesmenLastMonth = (await peopleDataAccess.GetSalesmenWithRoles(startDate.AddMonths(-1), parms.Month, dealerGroup)).Where(x => x.Role != "None").ToList();

                //find deals for site, turn into lookup per salesman
                allDealsThisMonth = (await dealDataAccess.GetSalesmanEfficiencyDeals(startDate, endDate, parms.VehicleTypeTypes, parms.OrderTypeTypes, parms.Franchises, dealerGroup)).ToList();
                allDealsLastMonth = (await dealDataAccess.GetSalesmanEfficiencyDeals(startDate.AddMonths(-1), endDate.AddMonths(-1), parms.VehicleTypeTypes, parms.OrderTypeTypes, parms.Franchises, dealerGroup)).ToList();
                foreach (var salesman in allSalesmenThisMonth) { salesman.Target = WorkoutSalesmanTarget(salesman.Role); }
                foreach (var salesman in allSalesmenLastMonth) { salesman.Target = WorkoutSalesmanTarget(salesman.Role); }
            }

            allDealsThisMonthBySalesman = allDealsThisMonth.ToLookup(x => x.SalesmanId);
            allDealsLastMonthBySalesman = allDealsLastMonth.ToLookup(x => x.SalesmanId);
        }








        private SalesmanEfficiencyRow createSalesmanEfficiencyRow(ReportRow thisRow, IEnumerable<SalesmanWithRole> salesmenThisMonth, IEnumerable<SalesmanWithRole> salesmenLastMonth, bool amMakingPersonRow, bool doPriorMonth)

        {
            //build up  target
            int targetThisMonth = 0;
            int targetLastMonth = 0;

            //build deal collections
            List<SalesmanEfficiencyDeal> dealsThisMonth = new List<SalesmanEfficiencyDeal>();
            List<SalesmanEfficiencyDeal> dealsLastMonth = new List<SalesmanEfficiencyDeal>();

            foreach (var salesman in salesmenThisMonth)
            {
                targetThisMonth += salesman.Target;
                dealsThisMonth.AddRange(allDealsThisMonthBySalesman[salesman.PersonId]);

                if (doPriorMonth)
                {
                    SalesmanWithRole personLastMonth = salesmenLastMonth.FirstOrDefault(x => x.PersonId == salesman.PersonId);
                    if (personLastMonth == null) personLastMonth = salesman;

                    targetLastMonth += personLastMonth.Target;
                    dealsLastMonth.AddRange(allDealsLastMonthBySalesman[salesman.PersonId]);
                }
            }

            IEnumerable<SalesmanEfficiencyDeal> newDeals = dealsThisMonth.Where(x => x.IsNewDeal);
            IEnumerable<SalesmanEfficiencyDeal> usedDeals = dealsThisMonth.Where(x => !x.IsNewDeal);


            //last month metrics
            int unitCountLastMonth = 0;
            int productsLastMonth = 0;
            decimal productTgtLastMonth = 0;
            if (doPriorMonth)
            {
                foreach (var deal in dealsLastMonth)
                {
                    unitCountLastMonth += deal.Units;
                    AggregateProductivityScore(ref productsLastMonth, ref productTgtLastMonth, deal);
                }
            }

            //this month metrics
            int unitCountThisMonth = 0;
            int unitCountNew = 0;
            int unitCountUsed = 0;

            //products
            int cosmeticCount = 0;
            int gapCount = 0;
            int paintCount = 0;
            int tyreCount = 0;
            int tyreAlloyCount = 0;
            int wheelGuardCount = 0;
            int warrantyCount = 0;


            //finance
            int financeUnitsNew = 0;
            int financeUnitsUsed = 0;

            int productsThisMonth = 0;
            decimal productTgtThisMonth = 0;
            foreach (var deal in dealsThisMonth)
            {
                unitCountThisMonth += deal.Units;
                AggregateProductivityScore(ref productsThisMonth, ref productTgtThisMonth, deal);
                if (deal.IsNewDeal)
                {
                    unitCountNew += deal.Units;
                    if (deal.IsFinanced) { financeUnitsNew += deal.Units; }
                }
                else
                {
                    unitCountUsed += deal.Units;
                    if (deal.IsFinanced) { financeUnitsUsed += deal.Units; }
                }
                //insurance prods
                if (deal.HasCosmeticInsurance) { cosmeticCount += deal.Units; }
                if (deal.HasGapInsurance) { gapCount += deal.Units; }
                if (deal.HasPaintProtection) { paintCount += deal.Units; }
                if (deal.HasTyreInsurance) { tyreCount += deal.Units; }
                if (deal.HasTyreAndAlloyInsurance) { tyreAlloyCount += deal.Units; }
                if (deal.HasWheelGuard) { wheelGuardCount += deal.Units; }
                if (deal.HasWarranty) { warrantyCount += deal.Units; }
            }

            //work out efficiency
            decimal efficiencyThisMonth = targetThisMonth == 0 ? 0 : (decimal)unitCountThisMonth / targetThisMonth;
            decimal efficiencyLastMonth = targetLastMonth == 0 ? 0 : (decimal)unitCountLastMonth / targetLastMonth;

            string efficiencyMonthOnMonth = "same";
            if (efficiencyThisMonth > efficiencyLastMonth) efficiencyMonthOnMonth = "better";
            if (efficiencyThisMonth < efficiencyLastMonth) efficiencyMonthOnMonth = "worse";

            static decimal Divide(int numerator, int denominator)
            {
                return denominator != 0 ? Math.Round((decimal)((double)numerator / (double)denominator), 3, MidpointRounding.AwayFromZero) : 0;
            }
            static decimal DivideByDecimal(int numerator, decimal denominator)
            {
                return denominator != 0 ? Math.Round((decimal)((double)numerator / (double)denominator), 3, MidpointRounding.AwayFromZero) : 0;
            }


            //work out productivity
            decimal productivityThisMonth = DivideByDecimal(productsThisMonth, productTgtThisMonth);
            decimal productivityLastMonth = DivideByDecimal(productsLastMonth, productTgtLastMonth);

            string productivityMonthOnMonth = "same";
            if (productivityThisMonth > productivityLastMonth) productivityMonthOnMonth = "better";
            if (productivityThisMonth < productivityLastMonth) productivityMonthOnMonth = "worse";


            //put all together
            SalesmanEfficiencyRow result = new SalesmanEfficiencyRow(thisRow);

            result.ExecCount = salesmenThisMonth.Count();
            result.Role = amMakingPersonRow ? salesmenThisMonth.First().Role : null;
            result.Site = amMakingPersonRow ? salesmenThisMonth.First().Site : null;
            result.PersonId = amMakingPersonRow ? salesmenThisMonth.First().PersonId : 0;
            result.Target = targetThisMonth;
            result.DealCountNew = (decimal)unitCountNew;
            result.DealCountUsed = (decimal)unitCountUsed;
            result.Efficiency = efficiencyThisMonth;
            result.EfficiencyLastMonth = efficiencyLastMonth;
            result.EfficiencyMonthOnMonth = efficiencyMonthOnMonth;
            result.FinancePenNew = Divide(financeUnitsNew, unitCountNew);
            result.FinancePenUsed = Divide(financeUnitsUsed, unitCountUsed);
            result.CosmeticPen = Divide(cosmeticCount, unitCountThisMonth);
            result.PaintPen = Divide(paintCount, unitCountThisMonth);
            result.GapPen = Divide(gapCount, unitCountThisMonth);
            result.TyrePen = Divide(tyreCount, unitCountThisMonth);
            result.TyreAlloyPen = Divide(tyreAlloyCount, unitCountThisMonth);
            result.WheelGuardPen = Divide(wheelGuardCount, unitCountThisMonth);
            result.WarrantyPen = Divide(warrantyCount, unitCountUsed);
            result.Productivity = productivityThisMonth;
            result.ProductivityMonthOnMonth = productivityMonthOnMonth;

            return result;


        }

        private static void AggregateProductivityScore(ref int productCount, ref decimal productTgt, SalesmanEfficiencyDeal deal)
        {
            if (deal.Units > 0)
            {
                productTgt += (decimal)(0.3 + 0.3 + 0.2 + 0.2 + 0.2 + 0.2);//should have GAP 30% of the time, Paint 30% of the time etc.,  (targets from Shaun)
                if (deal.IsNewDeal) { productTgt += (decimal)(0.75); } //should have new finance 75% of time
                else { productTgt += (decimal)(0.4 + 0.2); } //should have used fin 40% plus 20% warranty
            }

            if (deal.IsFinanced) { productCount += deal.Units; }
            if (deal.HasGapInsurance) { productCount += deal.Units; }
            if (deal.HasPaintProtection) { productCount += deal.Units; }
            if (deal.HasWarranty) { productCount += deal.Units; }
            if (deal.HasCosmeticInsurance) { productCount += deal.Units; }
            if (deal.HasTyreInsurance) { productCount += deal.Units; }
            if (deal.HasTyreAndAlloyInsurance) { productCount += deal.Units; }
            if (deal.HasWheelGuard) { productCount += deal.Units; }
        }

        //private static int BuildUpTargetForSalesmen(IEnumerable<SalesmanWithRole> salesmenThisMonth)
        //{
        //    int siteTargetThisMonth = 0;
        //    foreach (var salesman in salesmenThisMonth)
        //    {
        //        siteTargetThisMonth += WorkoutSalesmanTarget(salesman);
        //    }

        //    return siteTargetThisMonth;
        //}

        //private static int WorkoutSalesmanTarget(SalesmanWithRole salesman)
        //{
        //    if (salesman.Role == "New") return (int)Math.Round(200M / 12M, MidpointRounding.AwayFromZero);
        //    else if (salesman.Role == "Used") return (int)Math.Round(240M / 12M, MidpointRounding.AwayFromZero);
        //    else if (salesman.Role == "NewUsed") return (int)Math.Round(220M / 12M, MidpointRounding.AwayFromZero);
        //    else if (salesman.Role == "Fleet") return (int)Math.Round(250M / 12M, MidpointRounding.AwayFromZero);
        //    return 0;
        //}

        private int WorkoutSalesmanTarget(string role)
        {
            return 16; //SPK - 3209

            //if (role == "New") return (int)Math.Round(200M / 12M, MidpointRounding.AwayFromZero);
            //else if (role == "Used") return (int)Math.Round(240M / 12M, MidpointRounding.AwayFromZero);
            //else if (role == "NewUsed") return (int)Math.Round(220M / 12M, MidpointRounding.AwayFromZero);
            //else if (role == "Fleet") return (int)Math.Round(250M / 12M, MidpointRounding.AwayFromZero);
            //return 0;
        }
    }
}
