﻿using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using CPHI.Repository;
using System.Linq;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using CPHI.Spark.Model.Services;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
    public interface IAutoPriceNewDealerGroupService
    {
        Task AddNewSitesForExistingDealergroup(NewDealerGroupParams parms);
        Task CreateNewDealerGroup(NewDealerGroupParams parms);
    }


    public class AutoPriceNewDealerGroupService : IAutoPriceNewDealerGroupService
    {
        private readonly IConfiguration configuration;
        private readonly CPHIDbContext db;
        private readonly UserManager<ApplicationUser> userManager;

        public AutoPriceNewDealerGroupService(
            IConfiguration configuration,
            CPHIDbContext db,
            UserManager<ApplicationUser> userManagerIn
            )
        {
            this.configuration = configuration;
            this.userManager = userManagerIn;
            this.db = db;
        }

        
        public async Task CreateNewDealerGroup(NewDealerGroupParams parms)
        {

            foreach (var region in parms.Regions)
            {
                foreach (var site in region.Sites)
                {
                    if (site.SiteName.Count() > 30)
                    {
                        throw new Exception($"Site name {site.SiteName} is > 30 characters");
                    }
                }
            }


            //we always will use AutoPrice connString when creating new DG
            string dgName = DealerGroupConnectionNameService.GetConnectionName(Model.DealerGroupName.LMC); //this match does not need updating for a new DG
            string autoPriceConnString = configuration.GetConnectionString(dgName);

            db.Database.GetDbConnection().ConnectionString = autoPriceConnString;

            var autoPriceDealerOnboardDataAccess = new AutoPriceDealerOnboardDataAccess(autoPriceConnString);
            await autoPriceDealerOnboardDataAccess.CreateNewDealerGroup(parms, userManager);

        }

        public async Task AddNewSitesForExistingDealergroup(NewDealerGroupParams parms)
        {

            foreach (var region in parms.Regions)
            {
                foreach (var site in region.Sites)
                {
                    if (site.SiteName.Count() > 30)
                    {
                        throw new Exception($"Site name {site.SiteName} is > 30 characters");
                    }
                }
            }


            string dgName = DealerGroupConnectionNameService.GetConnectionName(Model.DealerGroupName.LMC); //this match does not need updating for a new DG
            string autoPriceConnString = configuration.GetConnectionString(dgName);

            db.Database.GetDbConnection().ConnectionString = autoPriceConnString;

           

            var autoPriceDealerOnboardDataAccess = new AutoPriceDealerOnboardDataAccess(autoPriceConnString);
            await autoPriceDealerOnboardDataAccess.AddNewSitesForExistingDealergroup(parms);
            
        }


    }
}