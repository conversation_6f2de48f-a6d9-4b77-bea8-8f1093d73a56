import {DatePipe} from '@angular/common';
import {Component, ElementRef, EventEmitter, OnInit, Output, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal, NgbModalRef} from '@ng-bootstrap/ng-bootstrap';
import {Subscription} from 'rxjs';
import {VehicleOptOutParams} from "src/app/model/VehicleOptOutParams";
import {VehicleOptOutStatus} from "src/app/model/VehicleOptOutStatus";
import {UpdatePriceParams} from "src/app/model/UpdatePriceParams";
import {DayToSellAndPriceIndicator} from "src/app/model/DayToSellAndPriceIndicator";
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {AutoPriceInsightsModalService} from '../autoPriceInsightsModal.service';
import {CphPipe} from 'src/app/cph.pipe';
import {
  GetEstimatedDayToSellAndPriceIndicatorParams
} from "src/app/pages/performanceTrends/GetEstimatedDayToSellAndPriceIndicatorParams";

@Component({
  selector: 'pricingHistory',
  templateUrl: './pricingHistory.component.html',
  styleUrls: ['./pricingHistory.component.scss']
})
export class PricingHistoryComponent implements OnInit {
  @ViewChild('optOutModal', {static: true}) optOutModal: ElementRef;
  //@Input() data: VehicleAdvertDetail;
  //  get data(): VehicleAdvertDetail {
  //   return this.service.modalItem.AdvertDetail
  // }
  @Output() updateOptOutStatus: EventEmitter<void> = new EventEmitter<void>();
  minDate: string;
  maxDate: string;
  optOutEndDate: string;
  optOutModalRef: NgbModalRef;
  vehicleOptOutStatus: VehicleOptOutStatus;
  newPrice: number;

  daysToSell: number = 0;
  priceIndicator: string = 'NOANALYSIS';

  constructor(
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public datePipe: DatePipe,
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public getDataService: GetDataMethodsService,
    public service: AutoPriceInsightsModalService,
    private cphPipe: CphPipe
  ) {
  }

  ngOnInit(): void {
    this.getVehicleOptOutStatus();
  }

  getVehicleOptOutStatus() {
    this.getDataService.getVehicleOptOutStatus(this.service.modalItem.AdvertDetail.AdId).subscribe((res: VehicleOptOutStatus) => {
      if (!res) {
        this.vehicleOptOutStatus = null;
        return;
      }

      if (res.EndDate) {
        res.EndDate = new Date(res.EndDate);
      }
      if (res.CreatedDate) {
        res.CreatedDate = new Date(res.CreatedDate);
      }
      if (res.EndDate < new Date()) {
        this.vehicleOptOutStatus = null;
      } else {
        this.vehicleOptOutStatus = res;
      }
    }, error => {
      console.error('Failed to retrieve vehicle opt-out status', error);
    })
  }

  isOptedOut() {
    return this.vehicleOptOutStatus?.EndDate > new Date();
  }

  public showSetNewPriceInput() {

    if (this.service.modalItem.HasLeft) {
      return false;
    }

    if (this.constantsService.environment.showChangePriceNowInputAlways) {
      return true;
    }
    return this.isOptedOut();
  }

  daysSinceLastChangeGetter(lastChangeDate: Date, comparedTo?: Date): string {
    if (lastChangeDate == null) {
      return '-';
    }

    if (!comparedTo) {
       comparedTo = new Date();
    }

    return this.cphPipe.transform(this.constantsService.differenceInDays(comparedTo, new Date(lastChangeDate)), 'number', 0);
  }

  maybeOptOut() {
    this.minDate = this.datePipe.transform(new Date().setDate(new Date().getDate() + 1), 'yyyy-MM-dd');
    this.maxDate = this.datePipe.transform(new Date().setDate(new Date().getDate() + 28), 'yyyy-MM-dd');
    this.optOutEndDate = this.datePipe.transform(new Date().setDate(new Date().getDate() + 1), 'yyyy-MM-dd');

    this.optOutModalRef = this.modalService.open(this.optOutModal, {
      size: 'sm',
      keyboard: true,
      ariaLabelledBy: 'modal-basic-title'
    });
    this.optOutModalRef.result.then((result) => {
      // Modal close
    }, (reason) => {
      this.optOutModalRef.close();
    });
  }

  optOut() {
    this.selectionsService.triggerSpinner.emit({show: true, message: 'Opting out...'});

    let params: VehicleOptOutParams = {
      RetailerSiteId: this.service.modalItem.AdvertDetail.RetailerSiteId,
      VehicleAdvertId: this.service.modalItem.AdvertDetail.AdId,
      Reg: this.service.modalItem.AdvertDetail.VehicleReg,
      Vin: this.service.modalItem.AdvertDetail.Chassis,
      EndDate: this.optOutEndDate
    }

    this.getDataService.optOut(params).subscribe(() => {
      this.constantsService.toastSuccess('Opt-out successful');
      this.getVehicleOptOutStatus();
      this.optOutModalRef.close();
      this.selectionsService.triggerSpinner.emit({show: false});
      this.service.pricingStatusSliderTrigger.emit();
    }, error => {
      this.constantsService.toastDanger('Failed to opt-out')
      console.error('Failed to opt-out', error);
      this.selectionsService.triggerSpinner.emit({show: false});
    })
  }

  maybeOptIn() {
    this.constantsService.confirmModal.showModal('Confirm opt-in', '');

    const confirmModalSubscription: Subscription = this.selectionsService.confirmModalEmitter.subscribe(res => {
      if (res) this.optIn();
      confirmModalSubscription.unsubscribe();
    })
  }

  onOptInToggle() {
    this.isOptedOut() ? this.maybeOptIn() : this.maybeOptOut();
  }

  optIn() {
    this.selectionsService.triggerSpinner.emit({show: true, message: 'Opting in...'});

    let params: VehicleOptOutParams = {
      RetailerSiteId: this.service.modalItem.AdvertDetail.RetailerSiteId,
      VehicleAdvertId: this.service.modalItem.AdvertDetail.AdId,
      Reg: this.service.modalItem.AdvertDetail.VehicleReg,
      Vin: this.service.modalItem.AdvertDetail.Chassis,
      EndDate: new Date().toISOString()
    }

    this.getDataService.optOut(params).subscribe(() => {
      this.constantsService.toastSuccess('Opt-in successful');
      this.getVehicleOptOutStatus();
      this.selectionsService.triggerSpinner.emit({show: false});
      this.service.pricingStatusSliderTrigger.emit();
    }, error => {
      this.constantsService.toastDanger('Failed to opt-in');
      console.error('Failed to opt-in', error);
      this.selectionsService.triggerSpinner.emit({show: false});
    })
  }

  setNewPrice(event: any) {
    if (!event.target || (event.target && event.target.value == '')) {
      return;
    }
    this.newPrice = parseFloat(event.target.value.replace('£', '').replace(',', ''));
  }

  getEstimatedDayToSellAndPriceIndicator() {
    const parms: GetEstimatedDayToSellAndPriceIndicatorParams = {
      AdvertiserIds: [this.service.modalItem.AdvertDetail.RetailerSiteRetailerId],
      DerivativeId: this.service.modalItem.AdvertDetail.DerivativeId,
      FirstRegisteredDate: this.service.modalItem.AdvertDetail.FirstRegisteredDate,
      Mileage: this.service.modalItem.AdvertDetail.OdometerReading,
      StrategyPrice: this.newPrice,
      VehicleHasOptionsSpecified: this.service.modalItem.AdvertDetail.VehicleHasOptionsSpecified,
      VehicleAdvertPortalOptions: this.service.modalItem.AdvertDetail.VehicleAdvertPortalOptions,

      AverageValuation: this.service.modalItem.AdvertDetail.ValuationMktAvRetail,
      AdjustedValuation: this.service.modalItem.AdvertDetail.ValuationAdjRetail
    }

    this.getDataService.getAutoPriceEstimatedDayToSellAndPriceIndicator(parms)
      .subscribe((res: DayToSellAndPriceIndicator) => {
        this.daysToSell = res.DaysToSellResults.find(x => x.RetailerSiteRetailerId === this.service.modalItem.AdvertDetail.RetailerSiteRetailerId).DaysToSell;
        this.priceIndicator = res.PriceIndicator;
      }, error => {
        console.error('Failed to retrieve estimated Day to Sell & Price Indicator', error);
      })
  }

  select(event: any) {
    event.target.select();
  }

  maybeUpdatePrice() {
    let modalResultSubscription: Subscription = this.selectionsService.confirmModalEmitter.subscribe(res => {
      if (res) {
        this.updatePrice();
      }
      modalResultSubscription.unsubscribe();
    })

    this.constantsService.confirmModal.showModal('Are you sure? This will update the live AutoTrader price.', null);
  }

  updatePrice() {
    this.selectionsService.triggerSpinner.emit({show: true, message: 'Setting price...'});

    let params: UpdatePriceParams = {
      vehicleAdvertId: this.service.modalItem.AdvertDetail.AdId,
      rrgSiteItemStockId: this.service.modalItem.AdvertDetail.WebSiteStockIdentifier,
      newPrice: this.newPrice,
    }

    this.getDataService.updateStockPrice(params).subscribe((res) => {
      this.constantsService.toastSuccess('Successfully set price');
      this.getVehicleOptOutStatus();
      this.selectionsService.triggerSpinner.emit({show: false});
    }, error => {
      this.constantsService.toastDanger('Failed to set price')
      console.error('Failed to set price', error);
      this.selectionsService.triggerSpinner.emit({show: false});
    })
  }

  isItemBeingAutoPriced() {
    if (!this.service.modalItem.AdvertDetail.SiteOptedIntoAutoPricing) {
      return false;
    } //site is not opted into pricing, must be false
    if (!this.isOptedOut()) {
      return true
    }
  }

  pricingMessage() : string {

    if (this.isItemBeingAutoPriced()) 
    {
      return `Auto pricing is enabled.  Click the slider to opt-out.`;
    } 
    else 
    {
      return `Vehicle is opted out of auto pricing until ${this.cphPipe.transform(this.vehicleOptOutStatus.EndDate, 'date', 0)}.  Click the slider to opt-in.`;
    }
  }
}
