import { Component, OnInit } from '@angular/core';
import { ConstantsService } from '../../services/constants.service'
import { CphPipe } from '../../cph.pipe'

import { Deal, LateCostOption, OrderOption } from '../../model/sales.model';
import { Week, SiteVM } from '../../model/main.model';

import { DealDetailsComponent } from 'src/app/components/dealDetails/dealDetails.component';
import { WeekAnalysis, WeeklyDealBreakdown, WeeklyDealBreakdownCard } from './dealsForTheMonth.model';
import { DealsForTheMonthService } from './dealsForTheMonth.service';

@Component({
  selector: 'app-dealsForTheMonth',
  templateUrl: './dealsForTheMonth.component.html',
  styleUrls: ['./dealsForTheMonth.component.scss']
})


export class DealsForTheMonthComponent implements OnInit {

  constructor(
    public service: DealsForTheMonthService,
    public constants: ConstantsService,
    public cphPipe: CphPipe

  ) {


  }

  ngOnInit() {
    this.service.initParams();
    this.service.getDeals();
  }

  //--------------------------------------
  //Template stuff
  //--------------------------------------

  getMonthName(date: Date): string
  {
    return date.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
  }

  getDayName(dayName: string): string
  {
    if(this.constants.translatedText.LocaleCode != 'en-gb')
    {
      let index = this.service.weekDays.indexOf(dayName);
      return this.service.translatedWeekDays[index].charAt(0).toUpperCase() + this.service.translatedWeekDays[index].slice(1);
    }
    else 
    {
      return dayName;
    }
    
  }

  getWeekCommencingTitle(w: Week) {
    if (w.name !== this.service.MtdTitle) {
      return "Week commencing " + this.cphPipe.transform(w.startDate, 'dayName', 0) + this.cphPipe.transform(w.startDate, 'shortDate', 0)
    }
  }

  onUpdateOrderTypes(orderTypes: string[]) {
    this.service.orderTypeTypes = orderTypes;
    this.service.getDeals();
  }

  onUpdateVehicleTypes(vehicleTypes: string[]) {
    this.service.vehicleTypeTypes = vehicleTypes
    this.service.getDeals();
  }

  onUpdateFranchises(franchises: string[]) {
    this.service.franchises = franchises
    this.service.getDeals();
  }

  onUpdateSites(sites: SiteVM[]) {
    this.service.selections.selectedSites = sites;
    this.service.selections.selectedSitesIds = [];

    sites.forEach(element => {
      this.service.selections.selectedSitesIds.push(element.SiteId);
    });

    this.service.getDeals();
  }

  selectLateCostOptionDropdownOnClick(lateCostOption: LateCostOption) {
    this.service.lateCostOption = lateCostOption;
    this.service.getDeals();
  }

  selectOrderOptionDropdownOnClick(orderOption: OrderOption) {
    this.service.orderOption = orderOption;
    this.service.getDeals();
  }

  changeMonthOnClick(changeAmount: number) {
    this.service.deliveryDate = this.constants.addMonths(this.service.deliveryDate, changeAmount);
    this.service.getDeals();
  }

  makeMonthsDropdownOnClick() {
    this.service.months = this.constants.makeMonthsNewV3();
  }

  selectMonthOnClick(chosenDate: Date) {
    this.service.deliveryDate = chosenDate;
    this.service.getDeals();
  }

  selectWeekAnalysisOnClick(weekAnalysis: WeekAnalysis) {
    this.service.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })
    this.service.showPopOut = false;
    
    this.service.getWeeklyAnalysis(weekAnalysis);

    setTimeout(() => {
      this.service.chosenWeek = weekAnalysis;
      this.service.showPopOut = true;
    }, 200)

  }

  openDealDetailModalOnClick(deal: WeeklyDealBreakdownCard): void {
    let modalRef;

    if(this.constants.environment.dealDetailModal_componentName == 'DealDetailsRRGComponent'){
      modalRef = this.service.modalService.open(DealDetailsComponent);
    }

    if(this.constants.environment.dealDetailModal_componentName == 'DealDetailsComponent'){
        modalRef = this.service.modalService.open(DealDetailsComponent);
    }

    //I give to modal
    modalRef.componentInstance.givenDealId = deal.Id;
    
    modalRef.result.then((result) => { //I get back from modal
      
    });
  }

  trackByFunction(index: number, deal: Deal): number {
    return index;
  }

  getDayDealCount(day: WeeklyDealBreakdown) {
    return this.constants.pluralise(day.DealCount, this.constants.translatedText.Order, this.constants.translatedText.Orders) + ', ' + this.constants.pluralise(day.Reversals * -1, 'cancellation', 'cancellations');
  }

}
