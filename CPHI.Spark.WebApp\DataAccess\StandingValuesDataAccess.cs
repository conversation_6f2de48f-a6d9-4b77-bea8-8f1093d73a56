﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.Model;
using CPHI.Spark.Repository;using CPHI.Spark.Model;
using System;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.DataAccess
{
    public interface IStandingValuesDataAccess
    {
        Task<string> GetFranchiseCodes(DealerGroupName dealerGroup);
    }

    public class StandingValuesDataAccess : IStandingValuesDataAccess
    {
        private readonly IDapperr dapper;

        public StandingValuesDataAccess(IDapperr dapper)
        {
            this.dapper = dapper;
        }

       

        //UnifiedDB - TODO - table doesnt have any dealergroup reference
        public async Task<string> GetFranchiseCodes(DealerGroupName dealerGroup)
        {
            return await dapper.GetAsync<string>("SELECT STRING_AGG(Description,',') FROM StandingValues WHERE StandingValueType_Id = 9 AND Code <> 'Z'", null, dealerGroup, System.Data.CommandType.Text);
        }

       
    }
}
