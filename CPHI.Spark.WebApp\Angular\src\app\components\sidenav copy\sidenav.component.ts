import { Component, ElementRef, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbDropdown, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { MenuItemNew } from 'src/app/model/main.model';
import { DashboardService } from 'src/app/pages/dashboard/dashboard.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Component({
  selector: 'app-sidenav',
  templateUrl: './sidenav.component.html',
  styleUrls: ['./sidenav.component.scss']
})
export class SidenavComponent implements OnInit {
  @ViewChild('p') popover: NgbPopover;
  @ViewChildren('p', { read: ElementRef }) popoverElements: QueryList<ElementRef>;

  menuIsWide: boolean;
  showMenuLabels: boolean;
  activePopover: NgbPopover;
  hoveredMenuItem: MenuItemNew;

  constructor(
    public selections: SelectionsService,
    public currentRoute: ActivatedRoute,
    public router: Router,
    public constants: ConstantsService,
    public dashboardService: DashboardService
  ) {
  }

  ngOnInit(): void {

  }

  makeMenuWide(event: MouseEvent, should: boolean) {
    if (should) {
      this.menuIsWide = should;
      setTimeout(() => {
        this.showMenuLabels = true;
      }, 200)
    } else {
      if (this.activePopover) {
        this.activePopover.close();
        this.activePopover = null;
      }

      setTimeout(() => {
        this.showMenuLabels = false;
        this.menuIsWide = should;
      }, 250)
    }
  }

  goTo(menuItem: MenuItemNew, group?: string) {
    if (this.activePopover) {
      this.activePopover.close();
      this.activePopover = null;
    }

    this.showMenuLabels = false;
    this.menuIsWide = false;

    if (menuItem.link === '/dashboard') {

      this.dashboardService.chosenPage = {
        pageName: menuItem.pageName,
        translatedTextField: menuItem.pageName,
        translatedTextValue: menuItem.pageName
      }

      if (menuItem.isSales) {
        this.dashboardService.chosenSection = this.constants.environment.dashboard_sections.find(x => x.translatedTextField === 'Sales');
      } else {
        this.dashboardService.chosenSection = this.constants.environment.dashboard_sections.find(x => x.translatedTextField === 'Aftersales');
      }

      this.router.navigateByUrl(menuItem.link);
      this.highlightActiveMenu(menuItem.name);

    } else {
      this.router.navigateByUrl(menuItem.link);
      this.highlightActiveMenu(menuItem.link);
    }
  }

  highlightActiveMenu(url: string) {
    // Reset current active
    this.constants.menuSections.forEach(menuItem => {
      //menuItem.isActive = false;
      //if (menuItem.group) {
        menuItem.subItems.forEach(subItem => {
          subItem.isActive = false;
        })
      //}
    })

    // if (group) {
    //   const menuGroup: MenuItemNew = this.constants.menuSections.find(x => x.group === group);
    //   menuGroup.isActive = true;
    //   menuGroup.subItems.forEach(menuItem => {
    //     if (url.includes('/')) {
    //       menuItem.isActive = menuItem.link === url ? true : false;
    //     } else {
    //       menuItem.isActive = menuItem.name === url ? true : false;
    //     }
    //   })
    // } else {
      // this.constants.menuSections.forEach(menuItem => {
      //   menuItem.isActive = menuItem.link === url ? true : false;
      // })
    //}
  }

  public openPopover(popover: NgbPopover, menuItem: any) {
    setTimeout(() => {
      this.hoveredMenuItem = menuItem
      // Close any previously active popover
      if (this.activePopover && this.activePopover !== popover) {
        this.activePopover.close();
      }

      // Open the new popover and set it as the active popover
      this.activePopover = popover;

      popover.open({ menuItem });
    }, 250)
  }

  public closePopover(popover: NgbPopover, event: MouseEvent) {
    if (!this.popoverContainsTarget(event.target as Element)) {
      popover.close();
    }
  }

  private popoverContainsTarget(target: Element): boolean {
    const popoverElements = this.popoverElements.map(p => p.nativeElement);
    return popoverElements.some(el => el.contains(target) || el === target);
  }
}
