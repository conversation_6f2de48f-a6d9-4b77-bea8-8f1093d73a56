
//###############################################################################################
//###############################################################################################
//If you wish to update the below you MUST also update the back end AND the mobile app
// AND note that we have to write everything in two places below, the property and the intialise method
//###############################################################################################
//###############################################################################################
export class TranslatedText {
  AccountSettings: string;
  AddUserModal_BadEmailMessage: string;
  AddUserModal_BadUsernameMessage: string;
  AddUserModal_TakenEmailMessage: string;
  AddUserModal_TakenUsernameMessage: string;
  BugFixes: string;
  Accounting: string;
  Achievement: string;
  Achieved: string;
  Actual: string;
  AddOnCount: string;
  AddOnProfit: string;
  AddOns: string;
  Aftersales: string;
  Age: string;
  Aging: string;
  AheadBy: string;
  AheadOfTarget: string;
  All: string;
  AllAges: string;
  AllDepartments: string;
  AllTypes: string;
  Alloy: string;
  Anytime: string;
  AreYouSure: string;
  BehindBy: string;
  BehindTargetOf: string;
  Between: string;
  Bonuses: string;
  Book:string;
  Bookings: string;
  BroughtIn: string;
  Budget: string;
  ByDay: string;
  Cancel: string;
  Cancellations:string;
  ChooseDepartmentAndComparative: string;
  ChooseFranchises: string;
  ChooseOptions: string;
  ChooseSites: string;
  Clear: string;
  Close: string;
  CoS: string;
  Colour: string;
  Comments: string;
  Commission: string;
  Complete: string;
  Contains: string;
  CoreUsed: string;
  Cost: string;
  Cumulative: string;
  Current: string;
  Custom: string;
  Customer: string;
  Daily: string;
  Date: string;
  Dates: string;
  Day: string;
  DayLower: string;
  Days: string;
  DaysLower: string;
  Deal: string;
  Deals: string;
  Debts: string;
  Delivered: string;
  DeliveryDate: string;
  Demo: string;
  Department: string;
  Description: string;
  Detail: string;
  Discount: string;
  Done: string;
  DoneInMonth: string;
  DoNotShowAgain: string;
  Efficiency: string;
  Elapsed: string;
  Error: string;
  ExDemo: string;
  ExManagement: string;
  ExcludeLateCosts: string;
  ExcludeOrders: string;
  Finance: string;
  FinanceProfit: string;
  FixedAsset: string;
  Fleet: string;
  FleetLocal: string;
  FleetSt: string;
  For: string;
  Franchise: string;
  Franchises: string;
  Friday: string;
  From: string;
  FullMonthTarget: string;
  GP: string;
  GPU: string;
  Gap: string;
  GeneratingExcel: string;
  GettingDeals: string;
  Highlighted: string;
  Hours: string;
  Id: string;
  InCapitalised:string;
  InLower: string;
  IncludeLateCosts: string;
  IncludeOrders: string;
  Invoice: string;
  InvoiceDate: string;
  Invoicing: string;
  July: string;
  LabourTotal: string;
  Latest: string;
  LastMonth: string;
  LastWeek: string;
  LastYear: string;
  Loading: string;
  Location: string;
  Make: string;
  Margin: string;
  Measures: string;
  Metal: string;
  MetalProfit: string;
  Model: string;
  ModelYear: string;
  Monday: string;
  Month: string;
  Months: string;
  MonthEnd: string;
  MonthTarget: string;
  Mot: string;
  Name: string;
  Need: string;
  Net: string;
  NetOrders:string;
  New: string;
  NewVehicles: string;
  Next: string;
  NextMonth: string;
  NextWeek: string;
  No: string;
  NoFranchises: string;
  NoLower: string;
  NoOrderType: string;
  NoVehicleType: string;
  Now: string;
  NonFranchise: string;
  Of: string;
  OfQualifyingWips: string;
  Ok: string;
  OKUpper:string;
  OnTarget: string;
  OnlyLateCosts: string;
  OnlyOrders: string;
  Order: string;
  OrderDate: string;
  OrderRate: string;
  Orders: string;
  OrdersPerDay:string;
  Orders_Capitals:string;
  OrderType: string;
  OrderedIn: string;
  Other: string;
  OtherProfit: string;
  Over: string;
  Overview: string;
  Parts: string;
  PartsStock: string;
  Password: string;
  PerDay: string;
  PerUnit: string;
  Performance: string;
  Pricing:string;
  Productivity: string;
  Products: string;
  Profit: string;
  Projection: string;
  ProportionOfQualifyingWips: string;
  QuarterEnd: string;
  Rank: string;
  Reconditioning: string;
  Reconnect: string;
  Refresh: string;
  Reg: string;
  Region: string;
  Registered: string;
  Relogin: string;
  Reports: string;
  Required: string;
  Requirement: string;
  Reset: string;
  RestartingMessage: string;
  RoadsideAssist: string;
  Sale: string;
  Sales: string;
  SalesByDay: string;
  SalesExec: string;
  SalesOps: string;
  Saturday: string;
  ScrapVehicles: string;
  Search: string;
  Selected: string;
  Service: string;
  ServiceOps: string;
  ServicePlan: string;
  ShortBy: string;
  Show: string;
  ShowOrdersCreated: string;
  Site: string;
  Sites: string;
  SitesOverview: string;
  Sold: string;
  SortBy: string;
  StepUp: string;
  Stock: string;
  StockAsStock:string;
  StockNumber: string;
  Summary: string;
  Sunday: string;
  Tactical: string;
  Target: string;
  TheMonth: string;
  TheWeek: string;
  ThisMonth: string;
  ThisWeek: string;
  ThisYear: string;
  Thursday: string;
  TimePeriods: string;
  To: string;
  ToGo: string;
  Today: string;
  Tomorrow: string;
  Total: string;
  TotalFleet: string;
  TotalNew: string;
  TotalOf: string;
  TotalProfit: string;
  TotalSite: string;
  TotalUsed: string;
  TotalValue: string;
  Trade: string;
  TradeVehicles: string;
  Trend: string;
  Tuesday: string;
  Turnover: string;
  Types: string;
  Tyre: string;
  Undelivered: string;
  Unit: string;
  Units: string;
  Update: string;
  Used: string;
  UsedVehicles: string;
  Value: string;
  Variant: string;
  VariantClass: string;
  Vehicle: string;
  VehicleAge: string;
  VehicleClass: string;
  VehicleType: string;
  Vehicles: string;
  Warranty: string;
  Wednesday: string;
  Week: string;
  WeekCommencing: string;
  WelcomeMessage: string;
  WheelGuard: string;
  Wip: string;
  Wips: string;
  With: string;
  WorkingDays: string;
  Year: string;
  Years: string;
  Yes: string;
  Yesterday: string;
  YTD: string;
  vsBudget: string;
  vsLastMonth: string;
  vsLastWeek: string;
  vsLastYear: string;
  vsTarget: string;
  vsThisMonth: string;
  vsYesterday: string;
  Position: string;
  Segment: string;
  Market: string;
  Status: string;
  Brand: string;
  Assignments: string;
  CurrentYear: string;
  YearBefore: string;
  DashboardPages: string;
  Dashboard_Activities: string;
  Dashboard_ActivityLevels_Over1Day: string;
  Dashboard_ActivityLevels_Over30Days: string;
  Dashboard_ActivityLevels_Over60Days: string;
  Dashboard_ActivityLevels_OverAWeek: string;
  Dashboard_ActivityLevels_Overdues: string;
  Dashboard_ActivityLevels_Title: string;
  Dashboard_ActivityLevels_TotalOverdue: string;
  Dashboard_AftersalesReports: string;

  Dashboard_Aftersales_TechnicalControl: string;
  Dashboard_Aftersales_ConversionRate: string;
  Dashboard_Aftersales_Productivity: string;
  Dashboard_Aftersales_Efficiency: string;
  Dashboard_Aftersales_FutureSales: string;
  Dashboard_Aftersales_CCRUpsell: string;
  Dashboard_Aftersales_ServiceBookings: string;
  Dashboard_Aftersales_TimeInvoiced: string;
  Dashboard_Aftersales_TimeInvested: string;
  Dashboard_Aftersales_Quantity: string;
  Dashboard_Aftersales_AllRevs : string;
  Dashboard_Aftersales_Next: string;
  Dashboard_Aftersales_BookedWithinCapacity: string;
  Dashboard_Aftersales_UnbookedCapacity: string;
  Dashboard_Aftersales_BookedOverCapacity: string;
  Dashboard_Aftersales_TurnoverPerOperative: string;
  Dashboard_Aftersales_RenaultMinutes: string;
  Dashboard_Aftersales_Bodyshop: string;
  Dashboard_Aftersales_Mechanical: string;
  Dashboard_Aftersales_DetailedBookings: string;
  Dashboard_Aftersales_ThisWeek: string;
  Dashboard_Aftersales_NextWeek: string;
  Dashboard_Aftersales_ServiceDetailed: string;
  Dashboard_Aftersales_HrsBilled: string;
  Dashboard_Aftersales_HrsInv: string;
  Dashboard_Aftersales_Prod: string;
  Dashboard_Aftersales_PartsRev: string;
  Dashboard_Aftersales_PartsGP: string;
  Dashboard_Aftersales_Entrances: string;
  Dashboard_Aftersales_MeanBill: string;
  Dashboard_Aftersales_HourlyRate: string;
  Dashboard_Aftersales_ClientPay: string;
  Dashboard_Aftersales_Insurance: string;
  Dashboard_Aftersales_Internal: string;
  Dashboard_Aftersales_Warranty: string;

  Dashboard_Aftersales_1to3days: string;
  Dashboard_Aftersales_4to5days: string;
  Dashboard_Aftersales_5plusdays: string;
  Dashboard_Aftersales_Service: string;
  Dashboard_Aftersales_Parts: string;
  Dashboard_Aftersales_lessThan15: string;
  Dashboard_Aftersales_15to30: string;
  Dashboard_Aftersales_plus30: string;
  Dashboard_Aftersales_AnomaliesPerVehicle: string;
  Dashboard_Aftersales_VideoConversion: string;
  Dashboard_Aftersales_Labour: string;
  Dashboard_Aftersales_Bookings: string;
  Dashboard_Aftersales_Revenue: string;
  Dashboard_Aftersales_Performance: string;
  Dashboard_Aftersales_WIP: string;
  Dashboard_Aftersales_VHC: string;

  Dashboard_AgedUsedStock_Title: string;
  Dashboard_AgedWips: string;
  Dashboard_Alcopa_Title: string;
  Dashboard_BonusDebts: string;
  Dashboard_CashDebts: string;
  Dashboard_CitNOWTile_Title: string;
  Dashboard_CitNow_Title: string;
  Dashboard_CitNow_ofSalesEnquiries: string;
  PerPerson: string;
  PerVehicle: string;
  Dashboard_Datasets: string;
  Dashboard_DataOriginsUpdates: string;
  Dashboard_Debtors_BonusesAging: string;
  Dashboard_Debtors_DebtsAging: string;
  Dashboard_Debtors_OfWhichOver30Days: string;
  Dashboard_Debtors_OfWhichOver60Days: string;
  Dashboard_Debtors_Title: string;
  Dashboard_Debtors_TotalBonuses: string;
  Dashboard_Debtors_TotalDebts: string;
  Dashboard_Debtors_TotalWips: string;
  Dashboard_Debtors_WipsAging: string;
  Dashboard_Donut_FleetPerformanceForTheMonth: string;
  Dashboard_Donut_NewPerformanceForTheMonth: string;
  Dashboard_Donut_UsedPerformanceForTheMonth: string;
  Dashboard_EvhcTile_Title: string;
  Dashboard_Evhc_RedWork: string;
  Dashboard_Evhc_Title: string;
  Dashboard_FilterChoices: string;
  Dashboard_FinanceAddOnPerformance: string;
  Dashboard_FinanceAddons_Title: string;
  Dashboard_FinanceAndAddons: string;
  Dashboard_FleetDealsByType: string;
  Dashboard_GDPRCapture: string;
  Dashboard_GaugeChart_TargetBeaten: string;
  Dashboard_ImageRatios_Title: string;
  Dashboard_InvoicedDonut_Invoices: string;
  Dashboard_InvoicedDonut_LastMonth: string;
  Dashboard_InvoicedDonut_LastYear: string;
  Dashboard_InvoicingPerformanceNew: string;
  Dashboard_InvoicingPerformanceUsed: string;
  Dashboard_InvoicingPerformance_InvoicedProfit: string;
  Dashboard_InvoicingPerformance_UnitInvoicingVsTarget: string;
  Dashboard_KPIs: string;
  Dashboard_KPIsTitle: string;
  Dashboard_NewDealsBreakdown: string;
  Dashboard_NewKPIsTitle: string;
  Dashboard_NewRetail: string;
  Dashboard_NewVehiclesGreaterThan90Days: string;
  Dashboard_NoAgedWips: string;
  Dashboard_OrdersDeliveredWithinFourDays: string;
  Dashboard_PartsSales_PartsSalesVsTarget: string;
  Dashboard_PartsSales_Title: string;
  Dashboard_PartsStockTile_Period: string;
  Dashboard_PartsStockValue: string;
  Dashboard_PartsStock_AllStock: string;
  Dashboard_PartsStock_OfTotalStock: string;
  Dashboard_PartsStock_OverageStock: string;
  Dashboard_PartsStock_StockByAge: string;
  Dashboard_PartsStock_StockByAge_DisposalRouteHeader: string;
  Dashboard_PartsStock_StockByAge_MakeHeader: string;
  Dashboard_PartsStock_StockByAge_VehicleAgeHeader: string;
  Dashboard_PartsStock_StockByAge_VehicleSourceHeader: string;
  Dashboard_PartsStock_StockCover: string;
  Dashboard_PartsStock_StockGraphs: string;
  Dashboard_PartsStock_StockGraphs_StockByMake: string;
  Dashboard_PartsStock_StockGraphs_StockByModel: string;
  Dashboard_PartsStock_StockGraphs_StockByOtherMakes: string;
  Dashboard_PartsStock_Title: string;
  Dashboard_PartsStock_TotalValue: string;
  Dashboard_PartsStock_UsedMerchandising: string;
  Dashboard_PartsStock_UsedStock: string;
  Dashboard_RegistrationsMajorFleet: string;
  Dashboard_Registrations_Dacia: string;
  Dashboard_Registrations_Renault: string;
  Dashboard_Registrations_Title: string;
  Dashboard_SalesActivity: string;
  Dashboard_SalesByType: string;
  Dashboard_SalesPerformance_ExcludingTradeUnits: string;
  Dashboard_SalesPerformance_ExcludingMotability: string;
  Dashboard_SalesPerformance_OnlyMotability: string;
  Dashboard_SalesPerformance_ForDeliveryInMonth: string;
  Dashboard_SalesPerformance_GP: string;
  Dashboard_SalesPerformance_GPU: string;
  Dashboard_SalesPerformance_GPUVsTarget: string;
  Dashboard_SalesPerformance_GPVsTarget: string;
  Dashboard_SalesPerformance_IncludingTradeUnits: string;
  Dashboard_SalesPerformance_IncludingMotability: string;
  Dashboard_SalesPerformance_OrderRateAndProjection: string;
  Dashboard_SalesPerformance_ProjectedFinish: string;
  Dashboard_SalesPerformance_ProjectedVs: string;
  Dashboard_SalesPerformance_Title: string;
  Dashboard_SalesPerformance_TotalVehicles: string;
  Dashboard_SalesPerformance_UnitsVsTarget: string;
  Dashboard_SalesPerformance_VehiclesOrdered: string;
  Dashboard_SalesPerformance_ViewDeals: string;
  Dashboard_SalesPerformance_vsBudget: string;
  Dashboard_SalesPerformance_RetailSales: string;
  Dashboard_SalesmanEfficiency_Title: string;
  Dashboard_ServiceBookings_Title: string;
  Dashboard_ServiceSales_PerRetailHour: string;
  Dashboard_ServiceSales_RecoveryRate: string;
  Dashboard_ServiceSales_RunRateAndRequirement: string;
  Dashboard_ServiceSales_SalesPerDay: string;
  Dashboard_ServiceSales_SalesPosition: string;
  Dashboard_ServiceSales_SalesVsTarget: string;
  Dashboard_ServiceSales_SoldHours: string;
  Dashboard_ServiceSales_Title: string;
  Dashboard_ServiceSales_WorkInProgress: string;
  Dashboard_SitePerformanceLeague_FleetUnits: string;
  Dashboard_SitePerformanceLeague_NewMargin: string;
  Dashboard_SitePerformanceLeague_NewUnits: string;
  Dashboard_SitePerformanceLeague_PartsSales: string;
  Dashboard_SitePerformanceLeague_ServiceSales: string;
  Dashboard_SitePerformanceLeague_Title: string;
  Dashboard_SitePerformanceLeague_UsedMargin: string;
  Dashboard_SitePerformanceLeague_UsedUnits: string;
  Dashboard_StockOverage_NewVehiclesGreaterThan: string;
  Dashboard_StockOverage_TradeVehiclesGreaterThan: string;
  Dashboard_StockOverage_UsedVehiclesGreaterThan: string;
  Dashboard_StockReport_AgedOver: string;
  Dashboard_StockReport_AsAt: string;
  Dashboard_StockReport_BranchDays: string;
  Dashboard_StockReport_GroupDays: string;
  Dashboard_StockReport_Title: string;
  Dashboard_StockReport_UsingDaysAtBranch: string;
  Dashboard_StockReport_UsingGroupDays: string;
  Dashboard_StockInsight:string;
  Dashboard_SiteBySite:string;
  Dashboard_VehiclesSold:string;
  Dashboard_StrategyBuilder:string;
  Dashboard_ApplyStrategy:string;
  Dashboard_LocationOptimiser:string;
  Dashboard_VehicleValuation:string;
  Dashboard_LocalBargains:string;
  Dashboard_ThisWeeksOrders: string;
  Dashboard_ThisWeeksOrders_FleetOrdersTaken: string;
  Dashboard_ThisWeeksOrders_NewOrdersTaken: string;
  Dashboard_ThisWeeksOrders_UsedOrdersTaken: string;
  Dashboard_Title: string;
  Dashboard_TradeVehiclesGreaterThan30Days: string;
  Dashboard_Upsells: string;
  Dashboard_TelephoneStats: string;
  Dashboard_UsedDealsByType: string;
  Dashboard_UsedKPIsTitle: string;
  Dashboard_UsedStockHealth: string;
  Dashboard_UsedStockMerchandising: string;
  Dashboard_UsedVehiclesGreaterThan60Days: string;
  Dashboard_VoC_Title: string;
  Dashboard_VoC_TitleShort: string;
  Dashboard_WIPAgeingSummary_Title: string;
  Dashboard_WipAgeingSummary: string;
  Dashboard_WipReport_Account: string;
  Dashboard_WipReport_Age: string;
  Dashboard_WipReport_Ageing: string;
  Dashboard_WipReport_AsAtMonthEnd: string;
  Dashboard_WipReport_AsAtNow: string;
  Dashboard_WipReport_BookingStatus: string;
  Dashboard_WipReport_Created: string;
  Dashboard_WipReport_Department: string;
  Dashboard_WipReport_DueIn: string;
  Dashboard_WipReport_IncludeZeroWips: string;
  Dashboard_WipReport_MonthEnd: string;
  Dashboard_WipReport_Notes: string;
  Dashboard_WipReport_Provision: string;
  Dashboard_WipReport_Title: string;
  Dashboard_WipReport_WipNumber: string;
  Dashboard_WithPrepCost: string;
  Dashboard_InvoiceDeals: string;
  DealDetails_Accessories: string;
  DealDetails_AccountingDate: string;
  DealDetails_AllocationDate: string;
  DealDetails_BodyPrep: string;
  DealDetails_BrokerCost: string;
  DealDetails_Comments: string;
  DealDetails_Cosmetic: string;
  DealDetails_CosmeticInsurance: string;
  DealDetails_CustomerDestinationDeliveryDate: string;
  DealDetails_DateFactoryTransportation: string;
  DealDetails_DateSiteArrival: string;
  DealDetails_DateSiteTransportation: string;
  DealDetails_DateVehicleRecondition: string;
  DealDetails_DetalDetails: string;
  DealDetails_DealRemoved: string;
  DealDetails_Delivery: string;
  DealDetails_EnterImportCentreDate: string;
  DealDetails_ExitImportCentreDate: string;
  DealDetails_FactoryBonus: string;
  DealDetails_FinanceAddOnProfit: string;
  DealDetails_FinanceAndAddonProfit: string;
  DealDetails_FinanceCo: string;
  DealDetails_FinanceCommission: string;
  DealDetails_FinanceSubsidy: string;
  DealDetails_Fuel: string;
  DealDetails_Gap: string;
  DealDetails_GapInsurance: string;
  DealDetails_HPAndOtherFinanceCommission: string;
  DealDetails_IntroCommission: string;
  DealDetails_InvoiceDate: string;
  DealDetails_InvoiceNo: string;
  DealDetails_IsDelivered: string;
  DealDetails_IsFinanced: string;
  DealDetails_IsLateCost: string;
  DealDetails_IsOnFinance: string;
  DealDetails_LockStatus:string;
  DealDetails_ManufacturerFinanceComm: string;
  DealDetails_MechanicalPrep: string;
  DealDetails_MetalProfit: string;
  DealDetails_ModelYear: string;
  DealDetails_OrderDate: string;
  DealDetails_OtherFinanceComm: string;
  DealDetails_PCPFinanceCommission: string;
  DealDetails_PaintProtect: string;
  DealDetails_PaintProtection: string;
  PaintProtectionAccessory: string;
  DealDetails_PartExchange: string;
  DealDetails_Pdi: string;
  DealDetails_ProPlusCommision: string;
  DealDetails_ProPlusCommission: string;
  DealDetails_RciFinanceCommission: string;
  DealDetails_RegBonus: string;
  DealDetails_RegisteredDate: string;
  DealDetails_SelectCommission: string;
  DealDetails_ServicePlan: string;
  DealDetails_ShipDate: string;
  StandardWarranty: string;
  DealDetails_StandardsCommission: string;
  DealDetails_StockDate: string;
  DealDetails_TinProfit: string;
  DealDetails_Title: string;
  DealDetails_TyreAlloyInsurance: string;
  DealDetails_TyreInsurance: string;
  DealDetails_TyreInsuranceAlloyInsurance: string;
  DealDetails_Variant: string;
  DealDetails_WebsitePrice:string;
  DealDetails_VariantText: string;
  DealDetails_VehicleUnit: string;
  DealDetails_VehicleDetails:string;
  DealDetails_WheelGuard: string;
  DealOptions_LateCosts: string;
  DealOptions_OrderTypes: string;
  DealOptions_Orders: string;
  DealOptions_ResetChoices: string;
  DealOptions_VehicleTypes: string;
  DealsByDay_Title: string;
  DealsDoneThisMonth_BroughtIn: string;
  DealsDoneThisMonth_Mtd: string;
  DealsDoneThisMonth_Title: string;
  DealsDoneThisMonth_TitleShort: string;
  DealsDoneThisWeek_DealsDoneEachDay: string;
  DealsDoneThisWeek_FuelSale: string;
  DealsDoneThisWeek_NoDeals: string;
  DealsDoneThisWeek_Title: string;
  DealsForTheMonth_ProductsPerUnit: string;
  Debts_AgedDebtsOn: string;
  Debts_AsAt: string;
  Debts_DocDate: string;
  Debts_DueDate: string;
  Debts_MonthEnd: string;
  DeliveryDateChoices_ChooseDeliveryDates: string;
  DeliveryDateChoices_DeliveryDay: string;
  DeliveryDateChoices_DeliveryMonth: string;
  DeliveryDateChoices_DeliveryWeek: string;
  DisconnectedMessage: string;
  Acquisition: string;
  ActualARADate: string;
  ActualAffectationDate: string;
  ActualAnnulmentDate: string;
  ActualAvailableDate: string;
  ActualDeliveryDate: string;
  ActualMADC: string;
  Advance: string;
  AgreedCustomerDeliveryDate: string;
  Antiquity: string;
  BillingStatus: string;
  Chassis: string;
  CommunityReceptionNo: string;
  Countermark: string;
  CustomerDeliveryControlDate: string;
  CustomerType: string;
  DADDate: string;
  DisFilter: string;
  DRType: string;
  DateOfOrderCreation: string;
  DepartureDateCI: string;
  EnergyType: string;
  EngineNumber: string;
  EntryDateCI: string;
  FinancialLocked: string;
  Flexibility: string;
  GreenFlexEndDate: string;
  Harmony1: string;
  Harmony: string;
  Interior1: string;
  Interior: string;
  LockStatus: string;
  MADACommitment: string;
  ModelCode: string;
  MountedAccessories: string;
  Options: string;
  OptionsCode: string;
  OrangeFlexEndDate: string;
  OrdenNumber: string;
  OrderNumber: string;
  OrderStatus: string;
  OrderTypeType: string;
  OriginType: string;
  Paint: string;
  PgeoBCV: string;
  PredictedMADC: string;
  PurseDate: string;
  QualityLocked: string;
  RecipientAccount: string;
  ReleaseDate: string;
  RemainingFranchise: string;
  RequestNumber: string;
  SalesmanCode: string;
  LastUpdated: string;
  DataLastUpdated: string;
  SalesmanName: string;
  SiteDMSId: string;
  SiteDescription: string;
  RegionDescription: string;
  SnapshotDate: string;
  StockDate: string;
  SubPropAccount: string;
  TestDate: string;
  OrderbookDistrinet: string;
  TradeLocked: string;
  TransportLockReason: string;
  TransportLocked: string;
  VehicleDetails: string;
  Version: string;
  VersionCode: string;
  FamilyPicker_AllFamilyCodes: string;
  FamilyPicker_FamilyCodes: string;
  FamilyPicker_NoFamilyCodes: string;
  FinanceAddons_Deals: string;
  FinanceAddons_PaintProtect: string;
  FinanceAddons_Title: string;
  FinanceAddons_TotalFinanceAndAddon: string;
  FinanceAddons_TyreAlloy: string;
  FinanceAddons_Units: string;
  FinanceAddons_YearToDate: string;
  FinanceAddons_FullPriorYear: string;
  FranchisePicker_AllFranchises: string;
  FranchisePicker_NoFranchisesSelected: string;
  HandoverDiary_Handover: string;
  HandoverDiary_Handovers: string;
  HandoverDiary_NoHandovers: string;
  HandoverDiary_Title: string;
  HandoverDiary_UpcomingHandovers: string;
  LoadingPage: string;
  LocaleCode: string;
  ManualReloadMessage: string;
  MonthMultiPicker_NoMonthsSelected: string;
  NewFeatureInProgress: string;
  OrderDateChoices_ChooseOrderDates: string;
  OrderTypePicker: string;
  Orderbook_AddOnsTooltip: string;
  Orderbook_ChannelAbbreviation: string;
  Orderbook_DaysInStock: string;
  Orderbook_DaysInStockAbbreviation: string;
  Orderbook_DaysToDeliverAbbreviation: string;
  Orderbook_DaysToDeliverTooltip: string;
  Orderbook_DaysToSaleAbbreviation: string;
  Orderbook_DaysToSaleTooltip: string;
  Orderbook_Del: string;
  Orderbook_DeliveryDateToolTip: string;
  Orderbook_DeliverySite: string;
  Orderbook_EmailForSupport: string;
  Orderbook_FinanceTypeAbbreviation: string;
  Orderbook_FranchiseAbbreviation: string;
  Orderbook_FranchiseCodeTooltip: string;
  Orderbook_InvoiceDateAbbreviation: string;
  Orderbook_IsClosedAbbreviation: string;
  Orderbook_IsClosedTooltip: string;
  Orderbook_IsConfirmedAbbreviation: string;
  Orderbook_IsConfirmedTooltip: string;
  Orderbook_IsDelivered: string;
  Orderbook_IsDeliveredAbbreviation: string;
  Orderbook_L: string;
  Orderbook_LateCost: string;
  Orderbook_LoadingOrderbook: string;
  Orderbook_ModelYear: string;
  Orderbook_ModelYearAbbreviation: string;
  Orderbook_OEMReference: string;
  Orderbook_OrderAllocationDateAbbreviation: string;
  Orderbook_OrderAllocationDateTooltip: string;
  Orderbook_OrderDateAbbreviation: string;
  Orderbook_OrderType: string;
  Orderbook_OrderTypeCodeAbbreviation: string;
  Orderbook_OrdersApprovedBetween: string;
  Orderbook_OrdersByNonExecs: string;
  Orderbook_OrdersCreatedBetween: string;
  Orderbook_OrdersLosingMoney: string;
  Orderbook_OrdersOver2k: string;
  Orderbook_PhysicalLocationTooltip: string;
  Orderbook_Q: string;
  Orderbook_SaleToolTip: string;
  Orderbook_SalesChannelTooltip: string;
  Orderbook_SourceAbbreviation: string;
  Orderbook_TitleSpain: string;
  Orderbook_VehicleClassAbbreviation: string;
  Orderbook_VehicleTypeCodeAbbreviation: string;
  OrderRate_OrdersBySite: string;
  PartsStockAgeing_Title: string;
  PerformanceEnhancements: string;
  PerformanceLeague_Advisor: string;
  PerformanceLeague_Bronze: string;
  PerformanceLeague_Gold: string;
  PerformanceLeague_IncLeavers: string;
  PerformanceLeague_League: string;
  PerformanceLeague_Rank: string;
  PerformanceLeague_Role: string;
  PerformanceLeague_ShowAllSites: string;
  PerformanceLeague_ShowDelivered: string;
  PerformanceLeague_ShowProfit: string;
  PerformanceLeague_Silver: string;
  PerformanceLeague_SortProfit: string;
  PerformanceLeague_Title: string;
  PerformanceLeague_YearToDate: string;
  PickANewImageToSetAsProfilePicture: string;
  PickImageToSetAsProfilePicture: string;
  ProfilePicture_CropError: string;
  ProfilePicture_DisplayError: string;
  ProfilePicture_NewPicture: string;
  ProfilePicture_ReplacePicture: string;
  ProfilePicture_SaveError: string;
  ProfilePicture_Saved: string;
  ProfilePicture_Saving: string;
  ProfilePicture_SetPicture: string;
  ProfilePicture_Title: string;
  Branch: string;
  BranchCode: string;
  Contacts: string;
  Origin: string;
  SaleDate: string;
  SalePrice: string;
  SalesPerson: string;
  Release: string;
  ReportPortal_Title: string;
  SalesProjectionChart_AheadMessage: string;
  SalesProjectionChart_BehindMessage: string;
  SalesProjectionChart_OnTargetMessage: string;
  SalesProjectionChart_RetailInMonth: string;
  SalesSplit_NewDealsForTheMonthBreakdown: string;
  ServiceSalesVsTarget: string;
  SetAsNewProfilePicture: string;
  SiteCompare: string;
  SitePicker_AllSitesSelected: string;
  SitePicker_MultipleSitesSelected: string;
  SitePicker_NoSitesSelected: string;
  SitesPerformanceTables: string;
  StockItemModal_AccountStatus: string;
  StockItemModal_CapCode: string;
  StockItemModal_CapId: string;
  StockItemModal_CapNotes: string;
  StockItemModal_CapProven: string;
  StockItemModal_CarryingValue: string;
  StockItemModal_Chassis: string;
  StockItemModal_DaysAtBranch: string;
  StockItemModal_DaysInStock: string;
  StockItemModal_DisposalRoute: string;
  StockItemModal_Doors: string;
  StockItemModal_Fuel: string;
  StockItemModal_Mileage: string;
  StockItemModal_Options: string;
  StockItemModal_PreviousSite: string;
  StockItemModal_PreviousUse: string;
  StockItemModal_ProgressCode: string;
  StockItemModal_Registration: string;
  StockItemModal_StockcheckLocation: string;
  StockItemModal_StockcheckPhoto: string;
  StockItemModal_StockcheckTime: string;
  StockItemModal_Title: string;
  StockItemModal_Transmission: string;
  StockItemModal_VatQualifying: string;
  StockItemModal_VehicleDetails: string;
  StockLanding_Title: string;
  StockList_ChooseFields: string;
  StockList_ChooseNewReportName: string;
  StockList_LastSaved: string;
  StockList_OpenReport: string;
  StockList_ReportName: string;
  StockList_SaveAsNewReport: string;
  StockList_SaveReport: string;
  StockList_ThisWillOverwriteReport: string;
  StockList_Title: string;
  StockList_DateFactoryTransportation: string;
  StockList_DateVehicleRecondition: string;
  StockList_DateSiteTransportation: string;
  StockList_DateSiteArrival: string;
  SuperCup_Title: string;
  TapToStart: string;
  TodayMap_Title: string;
  UpdateProfilePicture: string;
  UsageReport_Title: string;
  UsageReport_TotalDailyLoginsByWeek: string;
  UserMaintenance: string;
  BackToApp: string;
  ChangePassword: string;
  ChooseNewPassword: string;
  PasswordChangeSuccess: string;
  ClickButtonMessage: string;
  YourPasswordNeedsTo: string;
  BeAtLeast6Characters: string;
  IncludeBothUpperAndLower: string;
  LogOut: string;
  CurrentPassword: string;
  ConfirmPassword: string;
  FailedToChangePassword: string;
  PasswordMismatch: string;
  AllVehicleTypes: string;
  UsedVehicleTypes: string;
  Whiteboard: string;
  ToDo: string;
  YesterdayMap_Title: string;

  LessThan90Days: string;
  GreaterThan90Days: string;
  GreaterThan120Days: string;
  GreaterThan150Days: string;
  GreaterThan180Days: string;
  GreaterThan365Days: string;

  constructor(){
    this.AccountSettings = '';
        this.AddUserModal_BadEmailMessage='';
        this.AddUserModal_BadUsernameMessage='';
        this.AddUserModal_TakenEmailMessage='';
        this.AddUserModal_TakenUsernameMessage='';
        this.BugFixes='';
        this.Accounting='';
        this.Achievement='';
        this.Achieved='';
        this.Actual='';
        this.AddOnCount='';
        this.AddOnProfit='';
        this.AddOns='';
        this.Aftersales='';
        this.Age='';
        this.Aging='';
        this.AheadBy='';
        this.AheadOfTarget='';
        this.All='';
        this.AllAges='';
        this.AllDepartments='';
        this.AllTypes='';
        this.Alloy='';
        this.Anytime='';
        this.AreYouSure='';
        this.BehindBy='';
        this.BehindTargetOf='';
        this.Between='';
        this.Bonuses='';
        this.Book='';
        this.Bookings='';
        this.BroughtIn='';
        this.Budget='';
        this.ByDay='';
        this.Cancel='';
        this.Cancellations='';
        this.ChooseDepartmentAndComparative='';
        this.ChooseFranchises='';
        this.ChooseOptions='';
        this.ChooseSites='';
        this.Clear='';
        this.Close='';
        this.CoS='';
        this.Colour='';
        this.Comments='';
        this.Commission='';
        this.Complete='';
        this.Contains='';
        this.CoreUsed='';
        this.Cost='';
        this.Cumulative='';
        this.Current='';
        this.Custom='';
        this.Customer='';
        this.Daily='';
        this.Date='';
        this.Dates='';
        this.Day='';
        this.DayLower='';
        this.Days='';
        this.DaysLower='';
        this.Deal='';
        this.Deals='';
        this.Debts='';
        this.Delivered='';
        this.DeliveryDate='';
        this.Demo='';
        this.Department='';
        this.Description='';
        this.Detail='';
        this.Discount='';
        this.Done='';
        this.DoneInMonth='';
        this.Efficiency='';
        this.Elapsed='';
        this.Error='';
        this.ExDemo='';
        this.ExManagement='';
        this.ExcludeLateCosts='';
        this.ExcludeOrders='';
        this.Finance='';
        this.FinanceProfit='';
        this.FixedAsset='';
        this.Fleet='';
        this.FleetLocal='';
        this.FleetSt='';
        this.For='';
        this.Franchise='';
        this.Franchises='';
        this.Friday='';
        this.From='';
        this.FullMonthTarget='';
        this.GP='';
        this.GPU='';
        this.Gap='';
        this.GeneratingExcel='';
        this.GettingDeals='';
        this.Highlighted='';
        this.Hours='';
        this.Id='';
        this.InLower='';
        this.IncludeLateCosts='';
        this.IncludeOrders='';
        this.Invoice='';
        this.InvoiceDate='';
        this.Invoicing='';
        this.July='';
        this.LabourTotal='';
        this.Latest='';
        this.LastMonth='';
        this.LastWeek='';
        this.LastYear='';
        this.Loading='';
        this.Location='';
        this.Make='';
        this.Margin='';
        this.Measures='';
        this.Metal='';
        this.MetalProfit='';
        this.Model='';
        this.ModelYear='';
        this.Monday='';
        this.Month='';
        this.Months='';
        this.MonthEnd='';
        this.MonthTarget='';
        this.Mot='';
        this.Name='';
        this.Need='';
        this.Net='';
        this.NetOrders='';
        this.New='';
        this.NewVehicles='';
        this.Next='';
        this.NextMonth='';
        this.NextWeek='';
        this.No='';
        this.NoFranchises='';
        this.NoLower='';
        this.NoOrderType='';
        this.NoVehicleType='';
        this.Now='';
        this.NonFranchise='';
        this.Of='';
        this.OfQualifyingWips='';
        this.Ok='';
        this.OKUpper='';
        this.OnTarget='';
        this.OnlyLateCosts='';
        this.OnlyOrders='';
        this.Order='';
        this.OrderDate='';
        this.OrderRate='';
        this.Orders='';
        this.OrdersPerDay='';
        this.Orders_Capitals='';
        this.OrderType='';
        this.OrderedIn='';
        this.Other='';
        this.OtherProfit='';
        this.Over='';
        this.Overview='';
        this.Parts='';
        this.PartsStock='';
        this.Password='';
        this.PerDay='';
        this.PerUnit='';
        this.Performance='';
        this.Productivity='';
        this.Products='';
        this.Profit='';
        this.Projection='';
        this.ProportionOfQualifyingWips='';
        this.QuarterEnd='';
        this.Rank='';
        this.Reconditioning='';
        this.Reconnect='';
        this.Refresh='';
        this.Reg='';
        this.Region='';
        this.Registered='';
        this.Relogin='';
        this.Reports='';
        this.Required='';
        this.Requirement='';
        this.Reset='';
        this.RestartingMessage='';
        this.RoadsideAssist='';
        this.Sale='';
        this.Sales='';
        this.SalesByDay='';
        this.SalesExec='';
        this.SalesOps='';
        this.Saturday='';
        this.ScrapVehicles='';
        this.Search='';
        this.Selected='';
        this.Service='';
        this.ServiceOps='';
        this.ServicePlan='';
        this.ShortBy='';
        this.Show='';
        this.ShowOrdersCreated='';
        this.Site='';
        this.Sites='';
        this.SitesOverview='';
        this.Sold='';
        this.SortBy='';
        this.StepUp='';
        this.Stock='';
        this.StockAsStock='';
        this.StockNumber='';
        this.Summary='';
        this.Sunday='';
        this.Tactical='';
        this.Target='';
        this.TheMonth='';
        this.TheWeek='';
        this.ThisMonth='';
        this.ThisWeek='';
        this.ThisYear='';
        this.Thursday='';
        this.TimePeriods='';
        this.To='';
        this.ToGo='';
        this.Today='';
        this.Tomorrow='';
        this.Total='';
        this.TotalFleet='';
        this.TotalNew='';
        this.TotalOf='';
        this.TotalProfit='';
        this.TotalSite='';
        this.TotalUsed='';
        this.TotalValue='';
        this.Trade='';
        this.TradeVehicles='';
        this.Trend='';
        this.Tuesday='';
        this.Turnover='';
        this.Types='';
        this.Tyre='';
        this.Undelivered='';
        this.Unit='';
        this.Units='';
        this.Update='';
        this.Used='';
        this.UsedVehicles='';
        this.Value='';
        this.Variant='';
        this.VariantClass='';
        this.Vehicle='';
        this.VehicleAge='';
        this.VehicleClass='';
        this.VehicleType='';
        this.Vehicles='';
        this.Warranty='';
        this.Wednesday='';
        this.Week='';
        this.WeekCommencing='';
        this.WelcomeMessage='';
        this.WheelGuard='';
        this.Wip='';
        this.Wips='';
        this.With='';
        this.WorkingDays='';
        this.Year='';
        this.Years='';
        this.Yes='';
        this.Yesterday='';
        this.YTD='';
        this.vsBudget='';
        this.vsLastMonth='';
        this.vsLastWeek='';
        this.vsLastYear='';
        this.vsTarget='';
        this.vsThisMonth='';
        this.vsYesterday='';
        this.Position='';
        this.Segment='';
        this.Market='';
        this.Status='';
        this.Brand='';
        this.CurrentYear='';
        this.YearBefore='';
        this.DashboardPages='';
        this.Dashboard_Activities='';
        this.Dashboard_ActivityLevels_Over1Day='';
        this.Dashboard_ActivityLevels_Over30Days='';
        this.Dashboard_ActivityLevels_Over60Days='';
        this.Dashboard_ActivityLevels_OverAWeek='';
        this.Dashboard_ActivityLevels_Overdues='';
        this.Dashboard_ActivityLevels_Title='';
        this.Dashboard_ActivityLevels_TotalOverdue='';
        this.Dashboard_AgedUsedStock_Title='';
        this.Dashboard_AgedWips='';
        this.Dashboard_Alcopa_Title='';
        this.Dashboard_BonusDebts='';
        this.Dashboard_CashDebts='';
        this.Dashboard_CitNOWTile_Title='';
        this.Dashboard_CitNow_Title='';
        this.Dashboard_CitNow_ofSalesEnquiries='';
        this.PerPerson='';
        this.PerVehicle='';
        this.Dashboard_DataOriginsUpdates='';
        this.Dashboard_Debtors_BonusesAging='';
        this.Dashboard_Debtors_DebtsAging='';
        this.Dashboard_Debtors_OfWhichOver30Days='';
        this.Dashboard_Debtors_OfWhichOver60Days='';
        this.Dashboard_Debtors_Title='';
        this.Dashboard_Debtors_TotalBonuses='';
        this.Dashboard_Debtors_TotalDebts='';
        this.Dashboard_Debtors_TotalWips='';
        this.Dashboard_Debtors_WipsAging='';
        this.Dashboard_Donut_FleetPerformanceForTheMonth='';
        this.Dashboard_Donut_NewPerformanceForTheMonth='';
        this.Dashboard_Donut_UsedPerformanceForTheMonth='';
        this.Dashboard_EvhcTile_Title='';
        this.Dashboard_Evhc_RedWork='';
        this.Dashboard_Evhc_Title='';
        this.Dashboard_FilterChoices='';
        this.Dashboard_FinanceAddOnPerformance='';
        this.Dashboard_FinanceAddons_Title='';
        this.Dashboard_FinanceAndAddons='';
        this.Dashboard_FleetDealsByType='';
        this.Dashboard_GDPRCapture='';
        this.Dashboard_GaugeChart_TargetBeaten='';
        this.Dashboard_InvoicedDonut_Invoices='';
        this.Dashboard_InvoicedDonut_LastMonth='';
        this.Dashboard_InvoicedDonut_LastYear='';
        this.Dashboard_InvoicingPerformanceNew='';
        this.Dashboard_InvoicingPerformanceUsed='';
        this.Dashboard_InvoicingPerformance_InvoicedProfit='';
        this.Dashboard_InvoicingPerformance_UnitInvoicingVsTarget='';
        this.Dashboard_KPIsTitle='';
        this.Dashboard_ImageRatios_Title='';
        this.Dashboard_NewDealsBreakdown='';
        this.Dashboard_NewKPIsTitle='';
        this.Dashboard_NewRetail='';
        this.Dashboard_NewVehiclesGreaterThan90Days='';
        this.Dashboard_NoAgedWips='';
        this.Dashboard_OrdersDeliveredWithinFourDays='';
        this.Dashboard_PartsSales_PartsSalesVsTarget='';
        this.Dashboard_PartsSales_Title='';
        this.Dashboard_PartsStockTile_Period='';
        this.Dashboard_PartsStockValue='';
        this.Dashboard_PartsStock_AllStock='';
        this.Dashboard_PartsStock_OfTotalStock='';
        this.Dashboard_PartsStock_OverageStock='';
        this.Dashboard_PartsStock_StockByAge='';
        this.Dashboard_PartsStock_StockByAge_DisposalRouteHeader='';
        this.Dashboard_PartsStock_StockByAge_MakeHeader='';
        this.Dashboard_PartsStock_StockByAge_VehicleAgeHeader='';
        this.Dashboard_PartsStock_StockByAge_VehicleSourceHeader='';
        this.Dashboard_PartsStock_StockCover='';
        this.Dashboard_PartsStock_StockGraphs='';
        this.Dashboard_PartsStock_StockGraphs_StockByMake='';
        this.Dashboard_PartsStock_StockGraphs_StockByModel='';
        this.Dashboard_PartsStock_StockGraphs_StockByOtherMakes='';
        this.Dashboard_PartsStock_Title='';
        this.Dashboard_PartsStock_TotalValue='';
        this.Dashboard_PartsStock_UsedMerchandising='';
        this.Dashboard_PartsStock_UsedStock='';
        this.Dashboard_RegistrationsMajorFleet='';
        this.Dashboard_Registrations_Dacia='';
        this.Dashboard_Registrations_Renault='';
        this.Dashboard_Registrations_Title='';
        this.Dashboard_SalesActivity='';
        this.Dashboard_SalesByType='';
        this.Dashboard_SalesPerformance_ExcludingTradeUnits='';
        this.Dashboard_SalesPerformance_ForDeliveryInMonth='';
        this.Dashboard_SalesPerformance_GP='';
        this.Dashboard_SalesPerformance_GPU='';
        this.Dashboard_SalesPerformance_GPUVsTarget='';
        this.Dashboard_SalesPerformance_GPVsTarget='';
        this.Dashboard_SalesPerformance_IncludingTradeUnits='';
        this.Dashboard_SalesPerformance_OrderRateAndProjection='';
        this.Dashboard_SalesPerformance_ProjectedFinish='';
        this.Dashboard_SalesPerformance_ProjectedVs='';
        this.Dashboard_SalesPerformance_Title='';
        this.Dashboard_SalesPerformance_TotalVehicles='';
        this.Dashboard_SalesPerformance_UnitsVsTarget='';
        this.Dashboard_SalesPerformance_VehiclesOrdered='';
        this.Dashboard_SalesPerformance_ViewDeals='';
        this.Dashboard_SalesPerformance_vsBudget='';
        this.Dashboard_SalesPerformance_RetailSales='';
        this.Dashboard_SalesmanEfficiency_Title='';
        this.Dashboard_ServiceBookings_Title='';
        this.Dashboard_ServiceSales_PerRetailHour='';
        this.Dashboard_ServiceSales_RecoveryRate='';
        this.Dashboard_ServiceSales_RunRateAndRequirement='';
        this.Dashboard_ServiceSales_SalesPerDay='';
        this.Dashboard_ServiceSales_SalesPosition='';
        this.Dashboard_ServiceSales_SalesVsTarget='';
        this.Dashboard_ServiceSales_SoldHours='';
        this.Dashboard_ServiceSales_Title='';
        this.Dashboard_ServiceSales_WorkInProgress='';
        this.Dashboard_SitePerformanceLeague_FleetUnits='';
        this.Dashboard_SitePerformanceLeague_NewMargin='';
        this.Dashboard_SitePerformanceLeague_NewUnits='';
        this.Dashboard_SitePerformanceLeague_PartsSales='';
        this.Dashboard_SitePerformanceLeague_ServiceSales='';
        this.Dashboard_SitePerformanceLeague_Title='';
        this.Dashboard_SitePerformanceLeague_UsedMargin='';
        this.Dashboard_SitePerformanceLeague_UsedUnits='';
        this.Dashboard_StockOverage_NewVehiclesGreaterThan='';
        this.Dashboard_StockOverage_TradeVehiclesGreaterThan='';
        this.Dashboard_StockOverage_UsedVehiclesGreaterThan='';
        this.Dashboard_StockReport_AgedOver='';
        this.Dashboard_StockReport_AsAt='';
        this.Dashboard_StockReport_BranchDays='';
        this.Dashboard_StockReport_GroupDays='';
        this.Dashboard_StockReport_Title='';
        this.Dashboard_StockReport_UsingDaysAtBranch='';
        this.Dashboard_StockReport_UsingGroupDays='';
        this.Dashboard_ThisWeeksOrders='';
        this.Dashboard_ThisWeeksOrders_FleetOrdersTaken='';
        this.Dashboard_ThisWeeksOrders_NewOrdersTaken='';
        this.Dashboard_ThisWeeksOrders_UsedOrdersTaken='';
        this.Dashboard_TelephoneStats = '';
        this.Dashboard_Title='';
        this.Dashboard_TradeVehiclesGreaterThan30Days='';
        this.Dashboard_Upsells='';
        this.Dashboard_UsedDealsByType='';
        this.Dashboard_UsedKPIsTitle='';
        this.Dashboard_UsedStockHealth='';
        this.Dashboard_UsedStockMerchandising='';
        this.Dashboard_UsedVehiclesGreaterThan60Days='';
        this.Dashboard_VoC_Title='';
        this.Dashboard_VoC_TitleShort='';
        this.Dashboard_WIPAgeingSummary_Title='';
        this.Dashboard_WipAgeingSummary='';
        this.Dashboard_WipReport_Account='';
        this.Dashboard_WipReport_Age='';
        this.Dashboard_WipReport_Ageing='';
        this.Dashboard_WipReport_AsAtMonthEnd='';
        this.Dashboard_WipReport_AsAtNow='';
        this.Dashboard_WipReport_BookingStatus='';
        this.Dashboard_WipReport_Created='';
        this.Dashboard_WipReport_Department='';
        this.Dashboard_WipReport_DueIn='';
        this.Dashboard_WipReport_IncludeZeroWips='';
        this.Dashboard_WipReport_MonthEnd='';
        this.Dashboard_WipReport_Notes='';
        this.Dashboard_WipReport_Provision='';
        this.Dashboard_WipReport_Title='';
        this.Dashboard_WipReport_WipNumber='';
        this.Dashboard_WithPrepCost='';
        this.Dashboard_InvoiceDeals='';
        this.DealDetails_Accessories='';
        this.DealDetails_AccountingDate='';
        this.DealDetails_AllocationDate='';
        this.DealDetails_BodyPrep='';
        this.DealDetails_BrokerCost='';
        this.DealDetails_Comments='';
        this.DealDetails_Cosmetic='';
        this.DealDetails_CosmeticInsurance='';
        this.DealDetails_CustomerDestinationDeliveryDate='';
        this.DealDetails_DetalDetails='';
        this.DealDetails_DealRemoved='';
        this.DealDetails_Delivery='';
        this.DealDetails_EnterImportCentreDate='';
        this.DealDetails_ExitImportCentreDate='';
        this.DealDetails_FactoryBonus='';
        this.DealDetails_FinanceAddOnProfit='';
        this.DealDetails_FinanceAndAddonProfit='';
        this.DealDetails_FinanceCo='';
        this.DealDetails_FinanceCommission='';
        this.DealDetails_FinanceSubsidy='';
        this.DealDetails_Fuel='';
        this.DealDetails_Gap='';
        this.DealDetails_GapInsurance='';
        this.DealDetails_HPAndOtherFinanceCommission='';
        this.DealDetails_IntroCommission='';
        this.DealDetails_InvoiceDate='';
        this.DealDetails_IsDelivered='';
        this.DealDetails_IsFinanced='';
        this.DealDetails_IsLateCost='';
        this.DealDetails_IsOnFinance='';
        this.DealDetails_LockStatus='';
        this.DealDetails_ManufacturerFinanceComm='';
        this.DealDetails_MechanicalPrep='';
        this.DealDetails_MetalProfit='';
        this.DealDetails_ModelYear='';
        this.DealDetails_OrderDate='';
        this.DealDetails_OtherFinanceComm='';
        this.DealDetails_PCPFinanceCommission='';
        this.DealDetails_PaintProtect='';
        this.DealDetails_PaintProtection='';
        this.PaintProtectionAccessory='';
        this.DealDetails_PartExchange='';
        this.DealDetails_Pdi='';
        this.DealDetails_ProPlusCommision='';
        this.DealDetails_ProPlusCommission='';
        this.DealDetails_RciFinanceCommission='';
        this.DealDetails_RegBonus='';
        this.DealDetails_RegisteredDate='';
        this.DealDetails_SelectCommission='';
        this.DealDetails_ServicePlan='';
        this.DealDetails_ShipDate='';
        this.StandardWarranty='';
        this.DealDetails_StandardsCommission='';
        this.DealDetails_StockDate='';
        this.DealDetails_TinProfit='';
        this.DealDetails_Title='';
        this.DealDetails_TyreAlloyInsurance='';
        this.DealDetails_TyreInsurance='';
        this.DealDetails_TyreInsuranceAlloyInsurance='';
        this.DealDetails_Variant='';
        this.DealDetails_WebsitePrice='';
        this.DealDetails_VariantText='';
        this.DealDetails_VehicleUnit='';
        this.DealDetails_VehicleDetails='';
        this.DealDetails_WheelGuard='';
        this.DealOptions_LateCosts='';
        this.DealOptions_OrderTypes='';
        this.DealOptions_Orders='';
        this.DealOptions_ResetChoices='';
        this.DealOptions_VehicleTypes='';
        this.DealsByDay_Title='';
        this.DealsDoneThisMonth_BroughtIn='';
        this.DealsDoneThisMonth_Mtd='';
        this.DealsDoneThisMonth_Title='';
        this.DealsDoneThisMonth_TitleShort='';
        this.DealsDoneThisWeek_DealsDoneEachDay='';
        this.DealsDoneThisWeek_FuelSale='';
        this.DealsDoneThisWeek_NoDeals='';
        this.DealsDoneThisWeek_Title='';
        this.DealsForTheMonth_ProductsPerUnit='';
        this.Debts_AgedDebtsOn='';
        this.Debts_AsAt='';
        this.Debts_DocDate='';
        this.Debts_DueDate='';
        this.Debts_MonthEnd='';
        this.DeliveryDateChoices_ChooseDeliveryDates='';
        this.DeliveryDateChoices_DeliveryDay='';
        this.DeliveryDateChoices_DeliveryMonth='';
        this.DeliveryDateChoices_DeliveryWeek='';
        this.DisconnectedMessage='';
        this.Acquisition='';
        this.ActualARADate='';
        this.ActualAffectationDate='';
        this.ActualAnnulmentDate='';
        this.ActualAvailableDate='';
        this.ActualDeliveryDate='';
        this.ActualMADC='';
        this.Advance='';
        this.AgreedCustomerDeliveryDate='';
        this.Antiquity='';
        this.BillingStatus='';
        this.Chassis='';
        this.Colour='';
        this.Comments='';
        this.CommunityReceptionNo='';
        this.Countermark='';
        this.Customer='';
        this.CustomerDeliveryControlDate='';
        this.CustomerType='';
        this.DADDate='';
        this.DisFilter='';
        this.DRType='';
        this.DateOfOrderCreation='';
        this.Dates='';
        this.DepartureDateCI='';
        this.EnergyType='';
        this.EngineNumber='';
        this.EntryDateCI='';
        this.FinancialLocked='';
        this.Flexibility='';
        this.Franchise='';
        this.GreenFlexEndDate='';
        this.Harmony1='';
        this.Harmony='';
        this.Interior1='';
        this.Interior='';
        this.InvoiceDate='';
        this.LockStatus='';
        this.MADACommitment='';
        this.Model='';
        this.ModelCode='';
        this.MountedAccessories='';
        this.Options='';
        this.OptionsCode='';
        this.OrangeFlexEndDate='';
        this.OrdenNumber='';
        this.OrderNumber='';
        this.OrderStatus='';
        this.OrderTypeType='';
        this.OriginType='';
        this.Paint='';
        this.PgeoBCV='';
        this.PredictedMADC='';
        this.PurseDate='';
        this.QualityLocked='';
        this.RecipientAccount='';
        this.ReleaseDate='';
        this.RemainingFranchise='';
        this.RequestNumber='';
        this.SalesmanCode='';
        this.SalesmanName='';
        this.SiteDMSId='';
        this.SiteDescription='';
        this.RegionDescription='';
        this.SnapshotDate='';
        this.StockDate='';
        this.SubPropAccount='';
        this.TestDate='';
        this.TradeLocked='';
        this.TransportLockReason='';
        this.TransportLocked='';
        this.VehicleDetails='';
        this.Version='';
        this.VersionCode='';
        this.FamilyPicker_AllFamilyCodes='';
        this.FamilyPicker_FamilyCodes='';
        this.FamilyPicker_NoFamilyCodes='';
        this.FinanceAddons_Deals='';
        this.FinanceAddons_PaintProtect='';
        this.FinanceAddons_Title='';
        this.FinanceAddons_TotalFinanceAndAddon='';
        this.FinanceAddons_TyreAlloy='';
        this.FinanceAddons_Units='';
        this.FinanceAddons_YearToDate='';
        this.FinanceAddons_FullPriorYear='';
        this.FranchisePicker_AllFranchises='';
        this.FranchisePicker_NoFranchisesSelected='';
        this.GettingDeals='';
        this.HandoverDiary_Handover='';
        this.HandoverDiary_Handovers='';
        this.HandoverDiary_NoHandovers='';
        this.HandoverDiary_Title='';
        this.HandoverDiary_UpcomingHandovers='';
        this.LoadingPage='';
        this.LocaleCode='';
        this.ManualReloadMessage='';
        this.MonthMultiPicker_NoMonthsSelected='';
        this.NewFeatureInProgress='';
        this.OrderDateChoices_ChooseOrderDates='';
        this.OrderTypePicker='';
        this.Orderbook_AddOnsTooltip='';
        this.Orderbook_ChannelAbbreviation='';
        this.Orderbook_DaysInStock='';
        this.Orderbook_DaysInStockAbbreviation='';
        this.Orderbook_DaysToDeliverAbbreviation='';
        this.Orderbook_DaysToDeliverTooltip='';
        this.Orderbook_DaysToSaleAbbreviation='';
        this.Orderbook_DaysToSaleTooltip='';
        this.Orderbook_Del='';
        this.Orderbook_DeliveryDateToolTip='';
        this.Orderbook_DeliverySite='';
        this.Orderbook_EmailForSupport='';
        this.Orderbook_FinanceTypeAbbreviation='';
        this.Orderbook_FranchiseAbbreviation='';
        this.Orderbook_FranchiseCodeTooltip='';
        this.Orderbook_InvoiceDateAbbreviation='';
        this.Orderbook_IsClosedAbbreviation='';
        this.Orderbook_IsClosedTooltip='';
        this.Orderbook_IsConfirmedAbbreviation='';
        this.Orderbook_IsConfirmedTooltip='';
        this.Orderbook_IsDelivered='';
        this.Orderbook_IsDeliveredAbbreviation='';
        this.Orderbook_L='';
        this.Orderbook_LateCost='';
        this.Orderbook_LoadingOrderbook='';
        this.Orderbook_ModelYear='';
        this.Orderbook_ModelYearAbbreviation='';
        this.Orderbook_OEMReference='';
        this.Orderbook_OrderAllocationDateAbbreviation='';
        this.Orderbook_OrderAllocationDateTooltip='';
        this.Orderbook_OrderDateAbbreviation='';
        this.Orderbook_OrderType='';
        this.Orderbook_OrderTypeCodeAbbreviation='';
        this.Orderbook_OrdersApprovedBetween='';
        this.Orderbook_OrdersByNonExecs='';
        this.Orderbook_OrdersCreatedBetween='';
        this.Orderbook_OrdersLosingMoney='';
        this.Orderbook_OrdersOver2k='';
        this.Orderbook_PhysicalLocationTooltip='';
        this.Orderbook_Q='';
        this.Orderbook_SaleToolTip='';
        this.Orderbook_SalesChannelTooltip='';
        this.Orderbook_SourceAbbreviation='';
        this.Orderbook_TitleSpain='';
        this.Orderbook_VehicleClassAbbreviation='';
        this.Orderbook_VehicleTypeCodeAbbreviation='';
        this.PartsStockAgeing_Title='';
        this.PerformanceEnhancements='';
        this.PerformanceLeague_Advisor='';
        this.PerformanceLeague_Bronze='';
        this.PerformanceLeague_Gold='';
        this.PerformanceLeague_IncLeavers='';
        this.PerformanceLeague_League='';
        this.PerformanceLeague_Rank='';
        this.PerformanceLeague_Role='';
        this.PerformanceLeague_ShowAllSites='';
        this.PerformanceLeague_ShowDelivered='';
        this.PerformanceLeague_ShowProfit='';
        this.PerformanceLeague_Silver='';
        this.PerformanceLeague_SortProfit='';
        this.PerformanceLeague_Title='';
        this.PerformanceLeague_YearToDate='';
        this.PickANewImageToSetAsProfilePicture='';
        this.PickImageToSetAsProfilePicture='';
        this.ProfilePicture_CropError='';
        this.ProfilePicture_DisplayError='';
        this.ProfilePicture_NewPicture='';
        this.ProfilePicture_ReplacePicture='';
        this.ProfilePicture_SaveError='';
        this.ProfilePicture_Saved='';
        this.ProfilePicture_Saving='';
        this.ProfilePicture_SetPicture='';
        this.ProfilePicture_Title='';
        this.Branch='';
        this.BranchCode='';
        this.Chassis='';
        this.Contacts='';
        this.Origin='';
        this.SaleDate='';
        this.SalePrice='';
        this.SalesPerson='';
        this.Release='';
        this.ReportPortal_Title='';
        this.SalesProjectionChart_AheadMessage='';
        this.SalesProjectionChart_BehindMessage='';
        this.SalesProjectionChart_OnTargetMessage='';
        this.SalesProjectionChart_RetailInMonth='';
        this.SalesSplit_NewDealsForTheMonthBreakdown='';
        this.ServiceSalesVsTarget='';
        this.SetAsNewProfilePicture='';
        this.SiteCompare='';
        this.SitePicker_AllSitesSelected='';
        this.SitePicker_MultipleSitesSelected='';
        this.SitePicker_NoSitesSelected='';
        this.SitesPerformanceTables='';
        this.StockItemModal_AccountStatus='';
        this.StockItemModal_CapCode='';
        this.StockItemModal_CapId='';
        this.StockItemModal_CapNotes='';
        this.StockItemModal_CapProven='';
        this.StockItemModal_CarryingValue='';
        this.StockItemModal_Chassis='';
        this.StockItemModal_DaysAtBranch='';
        this.StockItemModal_DaysInStock='';
        this.StockItemModal_DisposalRoute='';
        this.StockItemModal_Doors='';
        this.StockItemModal_Fuel='';
        this.StockItemModal_Mileage='';
        this.StockItemModal_Options='';
        this.StockItemModal_PreviousSite='';
        this.StockItemModal_PreviousUse='';
        this.StockItemModal_ProgressCode='';
        this.StockItemModal_Registration='';
        this.StockItemModal_StockcheckLocation='';
        this.StockItemModal_StockcheckPhoto='';
        this.StockItemModal_StockcheckTime='';
        this.StockItemModal_Title='';
        this.StockItemModal_Transmission='';
        this.StockItemModal_VatQualifying='';
        this.StockItemModal_VehicleDetails='';
        this.StockLanding_Title='';
        this.StockList_ChooseFields='';
        this.StockList_ChooseNewReportName='';
        this.StockList_LastSaved='';
        this.StockList_OpenReport='';
        this.StockList_ReportName='';
        this.StockList_SaveAsNewReport='';
        this.StockList_SaveReport='';
        this.StockList_ThisWillOverwriteReport='';
        this.StockList_Title='';
        this.SuperCup_Title='';
        this.TapToStart='';
        this.TodayMap_Title='';
        this.UsageReport_Title='';
        this.UsageReport_TotalDailyLoginsByWeek='';
        this.UpdateProfilePicture='';
        this.UserMaintenance='';
        this.BackToApp='';
        this.ChangePassword='';
        this.ChooseNewPassword='';
        this.PasswordChangeSuccess='';
        this.ClickButtonMessage='';
        this.YourPasswordNeedsTo='';
        this.BeAtLeast6Characters='';
        this.IncludeBothUpperAndLower='';
        this.LogOut='';
        this.CurrentPassword='';
        this.ConfirmPassword='';
        this.FailedToChangePassword='';
        this.PasswordMismatch='';
        this.AllVehicleTypes='';
        this.NoVehicleType='';
        this.UsedVehicleTypes='';
        this.VehicleType='';
        this.Delivered='';
        this.Profit='';
        this.Whiteboard='';
        this.ToDo='';
        this.YesterdayMap_Title='';
      
        this.LessThan90Days='';
        this.GreaterThan90Days='';
        this.GreaterThan120Days='';
        this.GreaterThan150Days='';
        this.GreaterThan180Days='';
        this.GreaterThan365Days='';
        this.OrderRate_OrdersBySite='';
  }

  

}