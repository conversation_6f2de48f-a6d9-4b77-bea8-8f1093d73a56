import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CphPipe } from "src/app/cph.pipe";
import { DailySiteBookingStat } from "src/app/model/afterSales.model";
import { AutotraderService } from "src/app/services/autotrader.service";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { ServiceBookingsTileSummary } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";
import { Chart, registerables } from 'chart.js';
import { BookingsDailySummary } from "src/app/model/BookingsDailySummary";

Chart.register(...registerables);

@Component({
  selector: 'serviceBookingsTile',
  templateUrl: './serviceBookingsTile.component.html',
  styles: [
    `
    #overallHolder{height:100%;}
    .spaceAround{margin-top:2.1em;}
    .label{font-weight: 400; }
    .value{font-weight: 200; }
    .total{font-weight: 400}
    #chartHolder{height: 95%; width: 100%}
    .myChart{padding-bottom:3em !important;}

    .chart-disabled { 
      background: #e6e6e6;     
    }
  `
  ]
})


export class ServiceBookingsTileComponent implements OnInit {
  @ViewChild("barChartCanvas", { static: true }) barChartCanvas: ElementRef;

  subscription: Subscription;
  subscriptionOverview: Subscription;
  @Input() public data: ServiceBookingsTileSummary[];
  @Input() public newDataEmitter: EventEmitter<void>;
  @Input() public nextDayCount: number;
  @Input() public disabled?: boolean;
  @Input() public dataSource: string;

  sevenDaySummary: BookingsDailySummary[];
  serviceBookings: BookingsDailySummary[];
  barChart: any;
  chart: any;
  chartBookings: number[];
  chartCapacity: number[];
  chartDates: string[];
  greenBars: number[];
  amberBars: number[];
  redBars: number[];

  dailySiteBookingStats: DailySiteBookingStat[];


  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public analysis: AutotraderService,
    public router: Router,
    public service: DashboardService,
    public cph: CphPipe
  ) {

  }


  ngOnInit(): void {

    this.buildChart();
    

    if(this.newDataEmitter){
      this.subscriptionOverview = this.newDataEmitter.subscribe(res => {
        setTimeout(()=>{
          this.buildChart();
        },50)
      })
    }
    else
    {
      this.subscription = this.service.aftersalesDashboardRedraw.subscribe(res=>{
        setTimeout(()=>{
          this.buildChart();
        },50)
      })
    }


  }


  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
    if (!!this.subscriptionOverview) this.subscriptionOverview.unsubscribe();
  }


  buildChart(): void 
  {

    if (this.barChart) 
    {
      this.barChart.destroy()
    }

    let barChartContext = this.barChartCanvas.nativeElement.getContext("2d");

    Chart.defaults.font.size = this.selections.getChartFontSize();

    this.barChart = new Chart(barChartContext, {
      type: 'bar',
      data: {
        labels: this.data.map(x => this.cph.transform(x.DayDate, 'dayAndDayNumber', 0).split(' ')),
        datasets: [{
          label: this.constants.translatedText.Dashboard_Aftersales_BookedWithinCapacity,
          data: this.data.map(x=>x.BookedWithinCapacity) ,
          backgroundColor: '#28a745',
        },
        {
          label: this.constants.translatedText.Dashboard_Aftersales_UnbookedCapacity,
          data: this.data.map(x=>x.UnbookedCapacity),
          backgroundColor: 'hsl(45,100%,50%)',
        },
        {
          label: this.constants.translatedText.Dashboard_Aftersales_BookedOverCapacity,
          data: this.data.map(x=>x.BookedOverCapacity),
          backgroundColor: '#dc3545',
        }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          datalabels: {
            display: false
          },
          legend: {
            display: false,
            labels: {
              // font: {
              //   size: 10 // Set your desired font size here
              // }
            },
            position: 'bottom',
            // labels:{
            //   siz: 10,
            //   size
            // },
          }
        },
        scales: {
        y: {
          grid: {
            display: true
          },
          title: {
            display: true,
            text: this.constants.translatedText.Hours
          },
          min: 0,
          stacked: true,
          ticks: {
            stepSize: 200,
            autoSkip: true,  // Prevent automatic skipping of ticks
            // font: {
            //   size: this.selections.chartFontsize
            // }
          }
        },
        x: {
          grid: {
            display: false
          },
          stacked: true,
          min: 0,
          ticks: {
            autoSkip: false,
            maxRotation: 0
            // font: {
            //   size: this.selections.chartFontsize-1
            // }
          }
        }
      }

      }
    });

  }

  goToServiceBookings() : void {
    this.service.chooseDashboardPage('ServiceBookings');
  }

}


