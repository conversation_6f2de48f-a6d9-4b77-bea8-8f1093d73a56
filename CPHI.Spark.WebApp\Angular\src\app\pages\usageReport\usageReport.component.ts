import { Component, EventEmitter, OnInit } from '@angular/core';
import { ConstantsService } from 'src/app/services/constants.service';
import { UsageReportService } from './usageReport.service';
import { RowNode } from 'ag-grid-community';
import { SelectionsService } from 'src/app/services/selections.service';

@Component({
  selector: 'app-usageReport',
  templateUrl: './usageReport.component.html',
  styleUrls: ['./usageReport.component.scss']
})
export class UsageReportComponent implements OnInit {

  showPersonDetail: boolean = true;
  sliderUpdateEmitter:EventEmitter<void> = new  EventEmitter;




  constructor(
    public service: UsageReportService,
    public constants: ConstantsService,
    public selections: SelectionsService

  ) { }

  ngOnInit(): void {
    this.customisePageOptions();
    this.service.initialise();
  }


  customisePageOptions() {

    if (!this.constants.environment.sideMenu.fleetOrderbook) {
      const index = this.service.pageNames.indexOf("fleetOrderbook");
      if (index !== -1) {
          this.service.pageNames.splice(index, 1);
      }
    }

    if (!this.constants.environment.sideMenu.salesExecReview) {
      const index = this.service.pageNames.indexOf("salesExecReview");
      if (index !== -1) {
          this.service.pageNames.splice(index, 1);
      }
    }

    if (!this.constants.environment.sideMenu.reportPortal) {
      const index = this.service.pageNames.indexOf("reportingCentre");
      if (index !== -1) {
          this.service.pageNames.splice(index, 1);
      }
    }

    if (this.constants.environment.customer == 'Vindis') {
      const index = this.service.pageNames.indexOf("commission");
      if (index !== -1) {
          this.service.pageNames.splice(index, 1);
      }
    }

    if (this.constants.environment.customer == 'RRGUK') {
      const index = this.service.pageNames.indexOf("commissionVindis");
      if (index !== -1) {
          this.service.pageNames.splice(index, 1);
      }
    }
  
  }


  onSliderToggle() {
    this.showPersonDetail = !this.showPersonDetail;
    if (this.showPersonDetail) {
      this.expandAllSites()
    } else {
      this.collapseAllSites();
    }
    this.sliderUpdateEmitter.emit();
  }

  collapseAllSites() {
    this.service.tableLayoutManagement.gridApi.collapseAll();
  }
  expandAllSites() {

    this.service.tableLayoutManagement.gridApi.forEachNode((node: RowNode) => {
      if (node.level === 0) {
        node.setExpanded(true); // Expand first-level groups
      } else {
        node.setExpanded(false); // Collapse deeper levels
      }
    });


  }

  toggleFeature(page: string) {
    const index = this.service.selectedPageNames.indexOf(page);
    
    if (index === -1) {
      this.service.selectedPageNames.push(page);
    } else {
      this.service.selectedPageNames.splice(index, 1);
    }
  }
  
  selectIsPricing(): void {

    this.service.selectedPageNames = [];

    let pricingPages = 
    [
      'advertSimpleListing',
      'bulkValuation',
      'home',
      'leavingVehicleDetail',
      'leavingVehicleTrends',
      'localBargains',
      'locationOptimiser',
      'stockReports',
      'optOuts',
      'todaysPrices',
      'valuation',
    ]

    const commonPages = this.service.pageNames.filter(page => pricingPages.includes(page));

    this.service.selectedPageNames = commonPages;

  }

  toggleAllPages(): void {
    if(this.service.selectedPageNames.length < this.service.pageNames.length){
      //some are not selected, select all
      this.service.selectedPageNames = this.service.pageNames;
    }else{
      //all are selected, select none
      this.service.selectedPageNames = [];
    }
  }
  

  // 
  applySelection(): void {
    this.service.getData();
  }
  
  cancelSelection(): void 
  {
    this.service.selectedPageNames = [];
  }

}
