import { Component, Input, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { SalesVsTarget } from "src/app/model/main.model";
import { PartsSummaryService } from "src/app/pages/partsSummary/partsSummary.service";
import { ServiceSummaryService } from "src/app/pages/serviceSummary/serviceSummary.service";
import { ConstantsService } from "src/app/services/constants.service";

@Component({
    selector: "salesPositionTile",
    template: `
      <div class="dashboard-tile-inner">
        <div class="dashboard-tile-header contains-back-button">
          {{ constants.translatedText.Dashboard_ServiceSales_SalesPosition }}
        </div>
        <div class="dashboard-tile-body">
          <!-- Gauge chart -->
          <guageChart
            [daysElapsed]="data.ElapsedDays"
            [daysTotal]="data.TotalDays"
            [measureDone]="data.Done"
            [measureTotal]="data.MonthTarget"
          >
          </guageChart>
          <div class="headlineFigure">
            {{ constants.translatedText.Done }}&nbsp;{{ data.Done | cph:'currency':0}}&nbsp;{{constants.translatedText.InLower}}
            {{ data.ElapsedDays | cph:'number':1 }} {{ constants.translatedText.DaysLower }}
            {{ constants.translatedText.MonthTarget }} {{ data.MonthTarget | cph:'currency':0 }}
          </div>
          <!-- Table -->
          <table class="cph">
            <thead>
              <tr>
                <th></th>
                <th>{{ constants.translatedText.Target }}</th>
                <th>{{ constants.translatedText.Actual }}</th>
                <th>Vs</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let stat of data.ChannelStats">
                <tr
                  *ngIf="page == 'parts' || (stat.IsLabour || (!stat.IsLabour && !constants.environment.serviceSalesDashboard_onlyLabour))"
                  [ngClass]="{ 'total': stat.IsTotal }"
                >
                  <td>{{ stat.Label }}</td>
                  <td>{{ stat.Target | cph:'number':0 }}</td>
                  <td>{{ stat.Actual | cph:'number':0 }}</td>
                  <td [ngClass]="{ 'badFont': stat.Target > stat.Actual }">
                    {{ (stat.Actual - stat.Target) | cph:'number':0 }}
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>
      </div>
    `,
    styles: [
      `
        .contains-back-button { margin-left: 4em; }
        
        .headlineFigure { width: 100%; display: flex; justify-content: center; }

        table {  width: 90%; margin: 1em auto; }
        table thead tr th { height: 2em !important; }
        table tbody tr.total td { background-color: var(--grey95); }
      `,
    ],
  })

  export class SalesPositionTileComponent implements OnInit {
    @Input() page: string;

    data: SalesVsTarget;

    constructor (
      public constants: ConstantsService,
      public serviceSummaryService: ServiceSummaryService,
      public partsSummaryService: PartsSummaryService,
      public router: Router
    ) { }

    ngOnInit(): void {
      this.data = this[`${this.page}SummaryService`][`${this.page}VsTarget`];
    }

  }