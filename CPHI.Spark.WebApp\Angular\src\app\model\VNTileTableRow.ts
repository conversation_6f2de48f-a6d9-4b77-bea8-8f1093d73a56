// export interface VNDashboardDateChoices {
//     OrderDates: Date[];
// }

export interface VNTileTableRow {
    Label: string;
    HighlightedTotal: number; // normal tiles we just run through adding up the total number of items into here
    filteredTotalValue?: number; // some tiles we also aggregate value of items into here, then can divide this by the unit count, to populate HighlightedTotal with an average
    FilteredTotal: number;
    highlightedTotalValue?: number;
    SortOrder?: number;
    HighlightsRowValuesToFilter?: string[];

    FilteredSumForAverage?: number;
    HighlightedSumForAverage?: number;
}
