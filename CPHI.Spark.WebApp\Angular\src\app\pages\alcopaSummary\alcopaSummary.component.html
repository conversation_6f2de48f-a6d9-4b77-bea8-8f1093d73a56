<nav class="navbar">

  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        {{constants.translatedText.Dashboard_Alcopa_Title}}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>


      <div class="buttonGroup topDropdownButtons">
       


        <!-- FOR SELECTING MONTH -->
        <div class="buttonGroup">
          <!-- previousMonth -->
          <button class="btn btn-primary" (click)="changeMonth(-1)"><i
              class="fas fa-caret-left"></i></button>

          <!-- dropdownMonth -->
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button  class="btn btn-primary centreButton"
              ngbDropdownToggle>{{service.chosenMonthStart|cph:'month':0}}</button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

              <!-- the ngFor buttons -->
              <button *ngFor="let month of months"
                (click)="selectMonth(month)"
                ngbDropdownItem>{{month |cph:'month':0}}</button>

            </div>
          </div>
          <!-- nextMonth -->
          <button class="btn btn-primary" (click)="changeMonth(1)"><i
              class="fas fa-caret-right"></i></button>
        </div>


      </div>

      <!-- Order type selector -->
      <pickerSimple
        [pickerItemsFromParent]="service.chosenStatuses"
        [pickerLabel]="constants.translatedText.Types"
        (selectedPickerItems)="updateChosenStatuses($event)"
      >
      </pickerSimple>

  </nav>


</nav>

<!-- Main Page -->
<div class="content-new">

  <div class="content-inner-new">

    <div id="tableHolder"> 
      <button class="btn btn-primary" id="backToSites" (click)="backToSites()" *ngIf="!!service.peopleRowData"><i
          class="fas fa-undo"></i>
      </button>

      <ng-container *ngIf="!!service.sitesRowData && !service.chosenSiteRow">
        <alcopaTable [rowData]="service.sitesRowData" [isPersonTable]="false" [isRegionalTable]="false"></alcopaTable>
        <div class="tableSpacer"></div>
        <alcopaTable [rowData]="service.sitesRowData" [isPersonTable]="false" [isRegionalTable]="true"></alcopaTable>
      </ng-container>

      <alcopaTable *ngIf="!!service.chosenSiteRow && !!service.peopleRowData" [isPersonTable]="true" [rowData]="service.peopleRowData"></alcopaTable>

    </div>


  </div>

</div>