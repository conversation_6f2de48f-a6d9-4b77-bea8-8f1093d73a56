<div class="tile-inner">
   <div class="tile-header">
      <ng-container *ngIf="service.modalItem?.AdvertDetail?.isTradePricing">Retail </ng-container>
      Pricing Scenarios
   </div>
   <div class="tile-body">
      <table class="mb-2">
         <thead>
            <tr>
               <th></th>
               <th>Days to sell</th>
               <th>Price Indicator</th>
            </tr>
         </thead>
         <tbody>
            <!-- Precalculated -->
            <tr
               *ngFor="let scenario of service.modalItem.PricingScenarios"
               [ngClass]="{ highlightRow: scenario.Label.includes('Advertised Price') }"
            >
               <td>{{ scenario.Label }}</td>
               <td>{{ scenario.DaysToSell | cph : "number" : 0 }}</td>
               <td>
                  <div class="priceIndicatorLozenge floatRight" [ngClass]="scenario.PriceIndicator">
                     {{ scenario.PriceIndicator | titlecase }}
                  </div>
               </td>
            </tr>

            <tr>
               <!-- Choose new scenario -->
               <td>
                     <form [formGroup]="form" (ngSubmit)="onSubmit()">
                        <div class="errorMessage" *ngIf="form.get('price')?.hasError('tooHigh')">Price position must be below 110%</div>
                        <div class="errorMessage" *ngIf="form.get('price')?.hasError('tooLow')">Price position must be above 90%</div>
                        <div *ngIf="!form.get('price')?.errors">&nbsp;</div>
                        <currencyInput formControlName="price" [placeholder]="'Enter a new price'" (enterPressed)="handleEnterPressed()" ></currencyInput>
                     </form>
               </td>

               <!-- The days to sell -->
               <td>
                  <div>&nbsp;</div>
                  <span *ngIf="!checking">{{ daysToSell | cph : "number" : 0 }}</span>
                  <span *ngIf="checking"></span>
               </td>
               
               <!-- The price indicator -->
               <td>
                  <div>&nbsp;</div>
                  <div *ngIf="!checking" class="priceIndicatorLozenge floatRight" [ngClass]="priceIndicator">
                     {{ priceIndicator | titlecase }}
                  </div>
                  <span *ngIf="checking">checking..</span>
               </td>
            </tr>
         </tbody>
      </table>
   </div>
</div>
