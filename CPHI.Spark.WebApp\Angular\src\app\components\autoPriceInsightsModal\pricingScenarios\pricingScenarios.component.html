<div class="tile-inner">
   <div class="tile-header">
      <ng-container *ngIf="service.modalItem?.AdvertDetail?.isTradePricing">Retail </ng-container>
      Pricing Scenarios
   </div>
   <div class="tile-body">
      <table class="mb-2">
         <thead>
            <tr>
               <th></th>
               <th>Days to sell</th>
               <th>Price Indicator</th>
            </tr>
         </thead>
         <tbody>
            <!-- Precalculated -->
            <tr
               *ngFor="let scenario of service.modalItem.PricingScenarios"
               [ngClass]="{ highlightRow: scenario.Label.includes('Advertised Price') }"
            >
               <td>{{ scenario.Label }}</td>
               <td>{{ scenario.DaysToSell | cph : "number" : 0 }}</td>
               <td>
                  <div class="priceIndicatorLozenge floatRight" [ngClass]="scenario.PriceIndicator">
                     {{ scenario.PriceIndicator | titlecase }}
                  </div>
                  <!-- <img class="priceIndicatorImage" *ngIf="scenario.PriceIndicator !== 'NOANALYSIS'"
                   [src]=" '/assets/imgs/autoTrader/PriceIndicator' + scenario.PriceIndicator + '.png'"
                   alt="Price Indicator" > -->
               </td>
            </tr>

            <!-- Check your own -->
            <tr>
               <!-- The input box and button -->
               <td>
                  <div
                     id="inputAndGo"
                     placement="bottom"
                     [disablePopover]="!preventSave"
                     popoverClass="vehiclePopUpImage"
                     container="body"
                     triggers="mouseenter:mouseleave"
                     [ngbPopover]="'Please ensure scenario price is &gt; 90% of valuation and &lt; 110% of valuation'"
                  >
                     
                   
                     <form [formGroup]="form" (ngSubmit)="onSubmit()">
                        
                        <div class="errorMessage" *ngIf="form.get('price')?.hasError('tooHigh')">Price position must be below 110%</div>
                        <div class="errorMessage" *ngIf="form.get('price')?.hasError('tooLow')">Price position must be above 90%</div>
                        <div *ngIf="!form.get('price')?.errors">&nbsp;</div>

                        <currencyInput formControlName="price" (enterPressed)="onSubmit()" ></currencyInput>
                        
                        <button
                           class="btn btn-success" type="submit"
                          [disabled]="form.invalid"
                        >
                           Go
                        </button>
                     </form>

                  </div>
               </td>

               <!-- The days to sell -->
               <td>
                  <span *ngIf="!checking">{{ daysToSell | cph : "number" : 0 }}</span>
                  <span *ngIf="checking"></span>
               </td>

               <!-- The price indicator -->
               <td>
                  <div *ngIf="!checking" class="priceIndicatorLozenge floatRight" [ngClass]="priceIndicator">
                     {{ priceIndicator | titlecase }}
                  </div>
                  <span *ngIf="checking">checking..</span>
               </td>
            </tr>
         </tbody>
      </table>
   </div>
</div>
