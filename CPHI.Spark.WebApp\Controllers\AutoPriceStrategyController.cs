﻿using Microsoft.AspNetCore.Mvc;
using System;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using CPHI.Spark.WebApp.Service;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.Services;
using CPHI.Spark.WebApp.Service.AutoPrice;
using CPHI.Spark.WebApp.Service.Autoprice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class AutoPriceStrategyController : ControllerBase
  {
    private readonly IStrategyService strategyService;
    private readonly int userId;
    private readonly Model.DealerGroupName userDealerGroupName;

    public AutoPriceStrategyController(
                               IUserService userService,
                               IStrategyService strategyService
                               )
    {
      this.userId = userService.GetUserId();
      this.userDealerGroupName = userService.GetUserDealerGroupName();
      this.strategyService = strategyService;
    }

    [HttpGet]
    [Route("GetAllPricingPolicies")]
    public async Task<List<StrategyVersionVM>> GetAllPricingPolicies()
    {
      return await strategyService.GetAllPricingPolicies(userDealerGroupName);
    }

    [HttpGet]
    [Route("GetAllStrategies")]
    public async Task<List<StrategyFull>> GetAllStrategies()
    {
      return await strategyService.GetAllStrategies(userDealerGroupName);
    }
    [HttpGet]
    [Route("GetAllStrategyFieldNames")]
    public async Task<List<StrategyFieldName>> GetAllStrategyFieldNames()
    {
      return await strategyService.GetAllStrategyFieldNames();
    }
    [HttpGet]
    [Route("DeleteStrategy")]
    public async Task DeleteStrategy(int strategyId)
    {
      await strategyService.DeleteStrategy(userDealerGroupName, strategyId);
    }

    [HttpGet]
    [Route("DeleteStrategyVersion")]
    public async Task DeleteStrategyVersion(int strategyVersionId)
    {
      await strategyService.DeleteStrategyVersion(userDealerGroupName, strategyVersionId);
    }

    [HttpGet]
    [Route("GetStrategy")]
    public async Task<StrategyVersionVM> GetStrategy(int strategyId)
    {
      return await strategyService.GetStrategy(userDealerGroupName, strategyId);
    }

    [HttpGet]
    [Route("RecalculateTestStrategyImpacts")]
    public async Task RecalculateTestStrategyImpacts()
    {
      await strategyService.RecalculateTestStrategyImpacts(userDealerGroupName);
    }

    [HttpGet]
    [Route("RecalculateTestStrategyDaysToSell")]
    public async Task RecalculateTestStrategyDaysToSell()
    {
      await strategyService.RecalculateTestStrategyDaysToSell(userDealerGroupName);
    }

    [HttpPost]
    [Route("SaveStrategyVersion")]

    public async Task<int?> SaveStrategyVersion(StrategyVersionVM strategyVersion)
    {
      return await strategyService.SaveStrategyVersion(strategyVersion, userId, userDealerGroupName);
    }

    [HttpPost]
    [Route("SaveStrategy")]

    public async Task<int?> SaveStrategy(StrategyFull strategy)
    {
      return await strategyService.SaveStrategy(strategy, userId, userDealerGroupName);
    }

    [HttpPost]
    [Route("CreateStrategyVersion")]
    public async Task CreateStrategyVersion(StrategyVersionVM strategyVersionVM)
    {
      await strategyService.CreateStrategyVersion(strategyVersionVM.Name, userId);
    }





  }
}
