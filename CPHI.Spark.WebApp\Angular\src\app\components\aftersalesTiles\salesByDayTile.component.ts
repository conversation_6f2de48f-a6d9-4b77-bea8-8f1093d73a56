import { Component, Input, OnInit } from "@angular/core";
import { SalesDailyDoneVsTarget } from "src/app/model/main.model";
import { PartsSummaryService } from "src/app/pages/partsSummary/partsSummary.service";
import { ServiceSummaryService } from "src/app/pages/serviceSummary/serviceSummary.service";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";

@Component({
    selector: "salesByDayTile",
    template: `
      <div class="dashboard-tile-inner">
        <div class="dashboard-tile-header">
          {{ constants.translatedText.SalesByDay }} - {{ constants.translatedText.Total }}
        </div>
        <div class="dashboard-tile-body">
          <table class="cph noStripes">
            <thead>
              <tr>
                <th></th>
                <th></th>
                <th class="text-center" colspan="2">{{ constants.translatedText.vsTarget }}</th>
              </tr>
              <tr>
                <th>{{ constants.translatedText.Date }}</th>
                <th>{{ constants.translatedText.Done }}</th>
                <th>{{ constants.translatedText.Daily }}</th>
                <th>{{ constants.translatedText.Cumulative }}</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let row of data; let i = index">
                <tr [ngClass]="{
                  'weekendOrPh': row.IsWeekendOrPublicHoliday,
                  'wtdRow': row.IsWTD,
                  'totalRow': row.IsTotal
                  }"
                >
                  <td>
                    <ng-container>
                      <span *ngIf="row.IsWTD">
                        WTD
                      </span>
                      <span *ngIf="row.IsTotal">
                        {{constants.translatedText.Total}}
                      </span>
                      <span *ngIf="!row.IsWTD && !row.IsTotal">
                        {{ row.Day | cph:'dayAndDayNumber':0 | titlecase }}
                      </span>
                    </ng-container>
                    <ng-container *ngIf="!row.IsAPastDay">
                       &nbsp;
                    </ng-container>
                  </td>
                  <td>
                    <ng-container *ngIf="row.IsAPastDay">
                      {{ row.Done | cph:'number':0 }}
                    </ng-container>
                  </td>
                  <td>
                    <ng-container *ngIf="row.IsAPastDay">
                      {{ row.VsTarget | cph:'number':0:true }}
                    </ng-container>
                  </td>
                  <td [ngClass]="{ 'badFont': row.VsTargetCum < 0 }">
                    <ng-container *ngIf="row.IsAPastDay && (!row.IsWTD && !row.IsTotal)">
                      {{ row.VsTargetCum | cph:'number':0:true }}
                    </ng-container>
                  </td>
                </tr>
              </ng-container>          
            </tbody>
          </table>
        </div>
      </div>
    `,
    styles: [
      `
        table { height: 97%; width: 90%; margin: 0 auto;  }
        table thead tr th { height: 2em !important; }
        table tbody tr td { line-height: unset !important; }
        table tbody tr.weekendOrPh td { background-color: var(--grey95); }
        table tbody tr.wtdRow td { background-color: var(--grey90); line-height: 2.5em !important; }
        table tbody tr.totalRow td { background-color: var(--grey80); line-height: 3em !important; }
      `,
    ],
  })

  export class SalesByDayTileComponent implements OnInit {
    @Input() page: string;
    
    data: SalesDailyDoneVsTarget[];

    constructor (
      public constants: ConstantsService,
      public serviceSummaryService: ServiceSummaryService,
      public partsSummaryService: PartsSummaryService,
      public selections: SelectionsService
    ) { }

    ngOnInit(): void {
      this.data = this[`${this.page}SummaryService`][`${this.page}DailyDoneVsTarget`];
    }
  }