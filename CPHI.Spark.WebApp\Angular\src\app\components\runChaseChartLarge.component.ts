import { Component, OnInit, ViewChild, ElementRef, Input, SimpleChanges } from "@angular/core";
import { ChartService } from "../services/chart.service";
import { RunChaseDataPoint } from "../model/main.model";
import { ChartDataSet } from "./aftersalesTiles/runChaseTile.component";
import { ConstantsService } from "../services/constants.service";
import { ServiceSummaryService } from "../pages/serviceSummary/serviceSummary.service";
import { PartsSummaryService } from "../pages/partsSummary/partsSummary.service";
import { CphPipe } from "../cph.pipe";

import { Chart, ChartConfiguration, ChartDataset, ChartDatasetCustomTypesPerDataset, registerables } from 'chart.js';
Chart.register(...registerables);

@Component({
  selector: 'runChaseChartLarge',
  template:    `

    <div id="chartHolder">
      <canvas id="chartCanvas" #myChart>    </canvas>
    </div>


    `
  ,
  styles: [`
    #chartHolder{width:90%;margin:0.3em auto 0em auto;position:relative;height:40em; background-color:white} 

    `]
})
export class RunChaseChartLargeComponent implements OnInit {
  
  @ViewChild('myChart', { static: true }) myChart: ElementRef;

  @Input() data: RunChaseDataPoint[];

  myChart1: any;

  constructor(
    public chart: ChartService,
    public constants: ConstantsService,
    public serviceSummaryService: ServiceSummaryService,
    public partsSummaryService: PartsSummaryService,
    public pipe: CphPipe
  ) {

  }

  ngOnInit(): void {

    setTimeout(() => {
      this.createChart();
    }, 500)
  }


  ngOnChanges(changes: SimpleChanges) {
    if (this.myChart1) {
      this.myChart1.destroy();
      this.createChart();
    }
  }

  ngOnDestroy(){
    if (this.myChart1) {
      this.myChart1.destroy();
    }
  }

  createChart() {

    this.myChart1 = (<HTMLCanvasElement>this.myChart.nativeElement);

    let points: RunChaseDataPoint[] = this.data;
    let done: ChartDataSet;
    let target: ChartDataSet;

    done = {
      label: this.constants.translatedText.Done,
      backgroundColor: "rgba(255,255,255,0)",
      borderColor: this.constants.actualColour,
      borderWidth: 4,
      data: points.map(x => x.DoneCum),
      hoverBackgroundColor: this.constants.actualColour,
      pointRadius: 0,
      tension: 0.2
    }

    target = {
      label: this.constants.translatedText.Target,
      backgroundColor: "rgba(255,255,255,0)",
      borderColor: this.constants.backgroundColours[2],
      borderWidth: 4,
      data: points.map(x => x.TargetCum),
      hoverBackgroundColor: this.constants.backgroundColours[2],
      pointRadius: 0,
      tension: 0.2
    }

    let nonNullDataPoints: number[] = points.map(x => x.DoneCum).filter(x => x !== null);
    let projectedFinishBasedOnActuals: number = this.constants.div(points.map(x => x.DoneCum).length, nonNullDataPoints.length) * nonNullDataPoints[nonNullDataPoints.length - 1] * 1.1;
    let yAxisMax: number = Math.max(projectedFinishBasedOnActuals, points.map(x => x.TargetCum)[points.map(x => x.TargetCum).length - 1]);

    const self = this;

    let config: ChartConfiguration = {
      type: 'line',
      data: {
        labels: points.map(x => self.pipe.transform(x.Label, 'dayAndDayNumber', 0)),
        datasets: [done, target],
      },
      options: {
        maintainAspectRatio: false,
        responsive: true,
        
        plugins: {
          datalabels: {
            display: false
          },
          tooltip: {
            enabled: true,
            mode: 'index',
            intersect: false,
            callbacks: {
              title: () => null, // Disables the title in the tooltip
              label: function(context) {
                // Extracting the required information from the context
                const datasetLabel = context.dataset.label || '';
                const value = context.parsed.y; // Assuming y-axis value
                // Transforming the value, assuming 'this.pipe' is properly defined and accessible
                const transformedValue = self.pipe.transform(value, 'currency', 0);
                return datasetLabel + ': ' + transformedValue.toString();
              }
            }
          },
          legend: { display: false, position: 'bottom' },
          title: {
            display: false,
            text: ''
          },
        },
        hover: {
          mode: 'nearest',
          intersect: true
        },
        scales: {
          x: {
            display: true,
            ticks: { 
              autoSkip: false, 
              font: {
                size: 9
              },
            },
            title: {
              display: false, // Set to true if you want to display the title
              text: 'Month'   // Label string for the x-axis
            }
          },
          y: {
            min: 0,
            max: yAxisMax,
            ticks: {
              font: {
                size: 12,
              },
              callback: (value, index, values) => {
                return value;
              }
            },
            display: false,
            title: {
              display: false, // Set to true if you want to display the title
              text: 'Value'   // Label string for the x-axis
            }
          }
        }
      }
    };

    let context = this.myChart.nativeElement.getContext('2d');
    this.myChart1 = new Chart(context, config);
  }


}








