{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppSettings": {"AllowedHosts": "*", "inboundMailAccount": "<EMAIL>", "outboundMailAccount": "<EMAIL>", "mailAppTenantId": "7def64f8-8b3f-400a-8ad0-e50eb7e77eef", "mailAppId": "9a44d66f-cfe2-4d19-b129-62b3e0dd28aa", "mailSecretValue": "****************************************", "mailIn": "Inbox", "mailArchive": "processed", "overrideRunJobNow": "", "fileDestination": "c:\\cphiRoot\\{destinationFolder}\\inbound\\", "ClientSettingsProvider.ServiceUri": "", "leaverDealURLProd": "https://rrgsparkapi.cphi.co.uk/api/LeaverReporting/LeaverDeal", "hRRRGEmailAddress": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "inboundRRGAddresses": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>, <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<PERSON><PERSON>@emgmotorgroup.com,<PERSON><PERSON>@lshauto.co.uk,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "inboundVindisAddresses": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "ScrapeRRG": "REPEAT EVERY 30 SECONDS", "ScrapeVindis": "REPEAT EVERY 30 SECONDS"}, "Monitor": {"AddLogMessageURL": "https://cphimonitorapi.azurewebsites.net/api/monitor/", "AppKey": "5"}}