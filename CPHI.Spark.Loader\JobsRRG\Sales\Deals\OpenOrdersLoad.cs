﻿using System;
using System.Collections.Generic;
using System.Linq;

using System.IO;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Text.RegularExpressions;
using System.Globalization;
using CPHI.Spark.Loader.Comparers;
using CPHI.Spark.Model.ViewModels;
using System.Threading.Tasks;
using CPHI.Spark.BusinessLogic.Vindis.Comparers;

namespace CPHI.Spark.Loader
{
    public class OpenOrdersLoad
    {


        private static readonly ILog Logger = LogManager.GetLogger(typeof(OpenOrdersLoad));
        private string fileSearch = "*SPK5.csv";
        /*
| Column                   | Example                                | Sensitive           |
|--------------------------|----------------------------------------|---------------------|
| Sales Order Number       | 203374                                 | No                  |
| Vehicle Number           | 609061                                 | No                  |
| Vehicle Sequence Number  | 0                                      | No                  |
| Salutation               | Ms Vennings                            | YES - Customer name |
| Sales Executive          | dg18647                                | No                  |
| Registration Number      |                                        | No                  |
| Manufacturer             | NISSAN                                 | No                  |
| Model                    | MIC                                    | No                  |
| Variant                  | 61923                                  | No                  |
| Text Manufact            | NISSAN                                 | No                  |
| Text Model               | Micra                                  | No                  |
| Description              | Micra 1.2 Visia 5dr CVT [AC] Hatchback | No                  |
| Vehicle Type             | NEW                                    | No                  |
| Variant Class            | CAR                                    | No                  |
| Sales Order Type         | FLEET                                  | No                  |
| Finance                  | 0                                      | No                  |
| Total Profit             | -872.63                                | No                  |
| Branch Code              | 2031                                   | No                  |
| Sale Price               | 9750.83                                | No                  |
| Discount                 | -1357.42                               | No                  |
| Trade In OverAllowance   | 0                                      | No                  |
| Vcost                    | -9266.04                               | No                  |
| FINSUB                   | 0                                      | No                  |
| Factory Bonus            | 0                                      | No                  |
| Reg Bonus                | 0                                      | No                  |
| Broker                   | 0                                      | No                  |
| DFA SALE                 | 63                                     | No                  |
| DFA COST                 | -63                                    | No                  |
| Fuel Sale                | 0                                      | No                  |
| Fuel Cost                | 0                                      | No                  |
| Warranty Sale            | 0                                      | No                  |
| Warranty Cost            | 0                                      | No                  |
| Cosmetic Commission      | 0                                      | No                  |
| Cosmetic Sale            | 0                                      | No                  |
| Cosmetic Cost            | 0                                      | No                  |
| GAP Commission           | 0                                      | No                  |
| GAP Sale                 | 0                                      | No                  |
| GAP Cost                 | 0                                      | No                  |
| Service Plan Sale        | 0                                      | No                  |
| Service Plan Cost        | 0                                      | No                  |
| Delivery Sale            | 0                                      | No                  |
| Delivery Cost            | 0                                      | No                  |
| RCI Finance Commission   | 0                                      | No                  |
| Other Finance Commission | 0                                      | No                  |
| Selections Bonus         | 0                                      | No                  |
| Pro Plus Bonus           | 0                                      | No                  |
| Standards                | 0                                      | No                  |
| PDI Cost                 | 0                                      | No                  |
| Mechanical Prep          | 0                                      | No                  |
| Body Prep                | 0                                      | No                  |
| Intro Commission         | 0                                      | No                  |
| Age                      | 2457284                                | No                  |
| Adoption Date            | --/--/----                             | No                  |
| Error                    | 0                                      | No                  |
| Invoice Date             | --/--/----                             | No                  |
| Registration Date        | --/--/----                             | No                  |
| Delivery Date            | --/--/----                             | No                  |
| Date Order Confirmed     | 42265                                  | No                  |
| Progress Code            | Con                                    | No                  |
| Brand                    | N                                      | No                  |
| Finance Company Name     | ALD Automotive Ltd                     | No                  |
| Handover Date            | --/--/----                             | No                  |
| Handover Time            |                                        | No                  |
| Enquiry Number           | 168474                                 | No                  |
| Stock Source             |                                        | No                  |
| Paint Protection Cost    | 0                                      | No                  |
| Paint Protection Sale    | 0                                      | No                  |
| Commission Number        | 3631908                                | No                  |

*/

        public async Task LoadConfirmedOrdersReport()
        {
            string[] allMatchingFiles = Directory.GetFiles(ConfigService.incomingRoot, fileSearch);

            if (allMatchingFiles.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.OpenOrders;
                if (age.Minutes > 120)
                {
                    PulsesService.OpenOrders = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *SPK5.csv");
                }

                return;
            }



            string fileToProcess = allMatchingFiles[0];


            //try opening the file, if fail, return (common problem is loader trying to open file whilst scraper is saving it).
            try { FileStream fs = File.Open(fileToProcess, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None); fs.Close(); }
            catch (IOException) { return; }

            //create lock to prevent other instances running
            LocksService.Deals = true;


            //define Lists
            List<EnquiryAndOrderDate> dbEnquiriesAndOrders;
            List<Deal> dbDealsAtOrderedStatus;
            List<Site> dbSites;
            List<StandingValue> standingValues;
            List<VehicleType> vehicleTypes;
            List<OrderType> orderTypes;
            List<Person> people;
            List<GlobalParam> dbGlobalParams;
            List<SiteSummaryStat> dbSiteSummaryStats;


            using (var db = new CPHIDbContext())

            {
                using (var dapper = new Dapperr())
                {
                    int errorCount = 0;
                    LogMessage logMessage = new LogMessage();
                    logMessage.DealerGroup_Id = 1;


                    try
                    {

                        //LocksService.OrdersDeals = true;
                        if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
                        {
                            System.Threading.Thread.Sleep(1000 * 60 * 15); //15 minute sleep to prevent concurrent load on database with production loader
                        }
                        DateTime start = DateTime.UtcNow;

                        int openingDbCount = dapper.Get<int>("SELECT COUNT(*) FROM Deals WHERE IsRemoved = 0", null, System.Data.CommandType.Text);

                        dbEnquiriesAndOrders = dapper.GetAll<EnquiryAndOrderDate>("SELECT EnquiryNumber,OrderDate FROM Deals", null, System.Data.CommandType.Text).ToList();// db.Deals.Select(x => new EnquiryAndOrderDate { EnquiryNumber = x.EnquiryNumber, OrderDate = x.OrderDate }).ToList();
                        dbGlobalParams = db.GlobalParams.ToList();
                        dbDealsAtOrderedStatus = db.Deals.Where(d => !d.IsRemoved && !d.IsInvoiced).ToList();
                        dbSiteSummaryStats = db.SiteSummaryStats.ToList();
                        dbSites = db.Sites.ToList();
                        standingValues = db.StandingValues.ToList();
                        vehicleTypes = db.VehicleTypes.ToList();
                        orderTypes = db.OrderTypes.ToList();
                        people = db.People.ToList();

                        logMessage.SourceDate = DateTime.UtcNow;
                        logMessage.Job = GetType().Name;

                        Logger.Info($@"[{DateTime.UtcNow}] New file found at {ConfigService.incomingRoot}.  Starting {fileToProcess}");   //update logger 


                        //define variables for use in processing this file

                        int removedCount = 0;
                        int newCount = 0;
                        int changedCount = 0;
                        int incomingProcessCount = 0;
                        List<Diff> diffs = new List<Diff>(); //create empty list

                        if (File.Exists(fileToProcess.Replace(".csv", "-p.csv")))
                        {
                            //already processing a file of this type, skip
                            Logger.Error($@"Could not interpret {fileToProcess}, -p file already found ");
                            logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                            throw new Exception("Processing file already found");
                        }

                        string fileName = fileToProcess.Split('\\')[4];
                        var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                        logMessage.SourceDate = fileDate;
                        File.Move(fileToProcess, fileToProcess.Replace(".csv", "-p.csv")); //append _processing to the file to prevent any other instances also processing these files
                        var newFilepath = fileToProcess.Replace(".csv", "-p.csv");

                        List<Deal> incomingDeals = new List<Deal>(10000);  //preset the list size (slightly quicker than growing it each time)
                        var rows = GetDataFromFilesService.GetRowsCsv(newFilepath);
                        var headers = rows.Skip(0).First().ToUpper().Split(',');

                        //find latest accounting date (should have been updated by the early morning loads of new and used)
                        GlobalParam latestAccountingDate = dbGlobalParams.First(x => x.Description == "Latest Accounting Date");
                        //int accountingYear = int.Parse(latestAccountingDate.TextValue.Substring(0, 4));
                        //int accountingMonth = int.Parse(latestAccountingDate.TextValue.Substring(4, latestAccountingDate.TextValue.Length - 4));
                        Dictionary<string, int> headerLookup = createHeaderDictionary(headers);

                        //firstly get all the stocknumbers
                        List<string> stockNumbers = new List<string>(rows.Count());
                        foreach (var row in rows.Skip(2))
                        {
                            if (string.IsNullOrEmpty(row)) { continue; }
                            var cells = Regex.Matches(row, "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))")
                                    .Cast<Match>()
                                    .Select(m => m.Value)
                                    .ToArray();

                            if (cells.Length != headers.Length)
                            {
                                //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                                continue;
                            }
                            string stockNumber = cells[headerLookup["VEHICLE NUMBER"]];
                            string sequence = cells[headerLookup["VEHICLE SEQUENCE NUMBER"]];


                            stockNumbers.Add($"{stockNumber}/{sequence}");
                        }

                        List<SpecCommissionLine> allSpecCommLines = HelpersService.GetSpecCommissionLines(stockNumbers, dapper);

                        
                        var knownEnquiries = dbEnquiriesAndOrders.ToLookup(x => x.EnquiryNumber.ToString());

                        //IEnumerable<SpecCommissionLine> specCommLines = allSpecCommLines.Where(x => x.StockNumber == $"{stockNumber}/{sequence}" && x.EnquiryNum == int.Parse(enquiryNumber));
                        var specCommLinesLookup = allSpecCommLines.ToLookup(x => $"{x.StockNumber}|{x.EnquiryNum}");

                        foreach (var row in rows.Skip(1))
                        {
                            incomingProcessCount++;
                            try
                            {
                                if (string.IsNullOrEmpty(row)) { continue; } //skip empties

                                var rowCols = Regex.Matches(row, "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))")
                                    .Cast<Match>()
                                    .Select(m => m.Value)
                                    .ToArray();


                                string stockNumber = rowCols[headerLookup["VEHICLE NUMBER"]];
                                string sequence = rowCols[headerLookup["VEHICLE SEQUENCE NUMBER"]];
                                string progressCode = rowCols[headerLookup["PROGRESS CODE"]];
                                if (progressCode != "Con") { continue; }//only want orders at confirmed status

                                if (rowCols.Length != headers.Length)
                                {
                                    //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                                    logMessage.FailNotes = logMessage.FailNotes + $"{stockNumber}/{sequence}: Skipped rowCol as had {rowCols.Length} rowCols and needed {headers.Length}"; ;
                                    errorCount++;
                                    continue;
                                }

                                //strangely get both lower and upper case versions of SALES ORDER TYPE so have to make it upper
                                string salesOrderType = rowCols[headerLookup["SALES ORDER TYPE"]].ToUpper();

                                //lookup objects required
                                var site = dbSites.First(s => s.Code == int.Parse(rowCols[headerLookup["BRANCH CODE"]]));
                                var fran = standingValues.First(s => s.Code == "Z"); //start with non-fran as these seem to come through blank.
                                if (rowCols[headerLookup["BRAND"]] != " ") fran = standingValues.First(s => s.Code == rowCols[headerLookup["BRAND"]]);

                                var oType = orderTypes.First(o => o.Code2 == salesOrderType);
                                var salesman = people.FirstOrDefault(o => o.DmsId == rowCols[headerLookup["SALES EXECUTIVE"]]);

                                if (salesman == null)
                                {
                                    throw new Exception($"Could not find person matching {rowCols[headerLookup["SALES EXECUTIVE"]]}");
                                }

                                var vehClass = standingValues.First(o => o.Code == rowCols[headerLookup["VARIANT CLASS"]]);
                                var vehType = vehicleTypes.First(v => v.Code == rowCols[headerLookup["VEHICLE TYPE"]]);

                                //skip on for these
                                if (site.Description == "Southern Fleet") { continue; }
                                if (site.Description == "Nissan Birmingham") { continue; }
                                if (site.Description == "Nissan Leicester") { continue; }
                                if (site.Description == "Leicester") { continue; }
                                if (site.Description == "Autoworld") { continue; }

                                //other things to work out

                                //dates: nullable
                                DateTime? stockDate = null;
                                DateTime? registeredDate = null;
                                DateTime? deliveryDate = null;
                                DateTime? handoverDate = null;

                                stockDate = TryParseDate(rowCols[headerLookup["ADOPTION DATE"]]);
                                registeredDate = TryParseDate(rowCols[headerLookup["REGISTRATION DATE"]]);
                                deliveryDate = TryParseDate(rowCols[headerLookup["DELIVERY DATE"]]);


                                string handoverDateDate = rowCols[headerLookup["HANDOVER DATE"]];
                                string handoverDateTime = rowCols[headerLookup["HANDOVER TIME"]];
                                if (handoverDateTime == "" || handoverDateTime == " ") handoverDateTime = "09:00";
                                handoverDate = TryParseDateTime(handoverDateDate + handoverDateTime);



                                //date: notNullable
                                DateTime orderDate = DateTime.ParseExact(rowCols[headerLookup["DATE ORDER CONFIRMED"]], "dd/MM/yyyy", null);
                                //adjust the order date back to a previous order date if we have a new order with an enquiry number matching that of a previously removed order.
                                string enquiryNumber = rowCols[headerLookup["ENQUIRY NUMBER"]];

                                if (enquiryNumber != "0" && enquiryNumber != null && knownEnquiries[enquiryNumber] != null && knownEnquiries[enquiryNumber].Count() > 0)
                                {
                                    orderDate = knownEnquiries[enquiryNumber].First().OrderDate;
                                }

                                //if the delivery date is null, then set it based upon either a week ahead for used or 60 days ahead for new
                                DateTime deliveryDateProxy = orderDate.AddDays(7);
                                if (vehType.Code == "NEW") { deliveryDateProxy = orderDate.AddDays(60); }
                                if (salesOrderType == "FLEET") { deliveryDateProxy = orderDate.AddDays(365); }

                                DateTime deliveryDateToUse = deliveryDate ?? deliveryDateProxy;

                                //bump forward the delivery date to the report date if the report date is later and the vehicle is uninvoiced.  By definition.  (can't deliver a car without it having been invoiced)
                                DateTime endOfDayFileDate = new DateTime(fileDate.Year, fileDate.Month, fileDate.Day, 22, 0, 0);
                                if (deliveryDateToUse < endOfDayFileDate) { deliveryDateToUse = endOfDayFileDate; }


                                int vehicleAge = 0;
                                TimeSpan age = orderDate - orderDate;
                                if (stockDate != null)
                                {
                                    DateTime nonNullableStockDate = stockDate.HasValue ? stockDate.Value : DateTime.MinValue;
                                    age = orderDate - nonNullableStockDate;
                                    vehicleAge = age.Days;
                                }

                                string vehicleSource = "";
                                vehicleSource = rowCols[headerLookup["STOCK SOURCE"]];

                                //oem ref
                                string oemRefIn = rowCols[headerLookup["COMMISSION NUMBER"]];
                                string oemRef = oemRefIn.Length == 10 ? oemRefIn.Substring(5, 5) : string.Empty;


                                //find insurance products
                                SpecCommissionLine specCommLine = null;
                                IEnumerable<SpecCommissionLine> specCommLines = specCommLinesLookup[$"{stockNumber}/{sequence}|{enquiryNumber}"];// allSpecCommLines.Where(x => x.StockNumber == $"{stockNumber}/{sequence}" && x.EnquiryNum == int.Parse(enquiryNumber));
                                if (specCommLines.Count() > 0) specCommLine = specCommLines.Last();

                                decimal salePaint = 0;
                                decimal saleGap = 0;
                                decimal saleCosmetic = 0;
                                decimal saleAccidentRepair = 0;
                                decimal saleTyre = 0;
                                decimal saleAlloy = 0;
                                decimal saleWarranty = 0;
                                decimal saleTyreAlloy = 0;
                                decimal saleWheelGuard = 0;
                                decimal saleServicePlan = 0;

                                decimal costPaint = 0;
                                decimal costGap = 0;
                                decimal costCosmetic = 0;
                                decimal costAccidentRepair = 0;
                                decimal costTyre = 0;
                                decimal costAlloy = 0;
                                decimal costWarranty = 0;
                                decimal costTyreAlloy = 0;
                                decimal costWheelGuard = 0;
                                decimal costServicePlan = 0;

                                if (specCommLine != null)
                                {
                                    salePaint = specCommLine.SalePaint;
                                    saleGap = specCommLine.SaleGap;
                                    saleCosmetic = specCommLine.SaleCosmetic;
                                    saleAccidentRepair = specCommLine.SaleAccidentRepair;
                                    saleTyre = specCommLine.SaleTyre;
                                    saleAlloy = specCommLine.SaleAlloy;
                                    saleWarranty = specCommLine.SaleWarranty;
                                    saleTyreAlloy = specCommLine.SaleTyreAlloy;
                                    saleWheelGuard = specCommLine.SaleWheelGuard;
                                    saleServicePlan = specCommLine.SaleServicePlan;

                                    costPaint = specCommLine.CostPaint * -1;
                                    costGap = specCommLine.CostGap * -1;
                                    costCosmetic = specCommLine.CostCosmetic * -1;
                                    costAccidentRepair = specCommLine.CostAccidentRepair * -1;
                                    costTyre = specCommLine.CostTyre * -1;
                                    costAlloy = specCommLine.CostAlloy * -1;
                                    costWarranty = specCommLine.CostWarranty * -1;
                                    costTyreAlloy = specCommLine.CostTyreAlloy * -1;
                                    costWheelGuard = specCommLine.CostWheelGuard * -1;
                                    costServicePlan = specCommLine.CostServicePlan * -1;
                                }


                                //columns from report
                                decimal gapSaleAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["GAP SALE"]]);
                                decimal cosmeticSaleAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["COSMETIC SALE"]]);
                                decimal accsSaleAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["DFA SALE"]]);
                                decimal paintSaleAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["PAINT PROTECTION SALE"]]);

                                decimal gapCostAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["GAP COST"]]);
                                decimal cosmeticCostAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["COSMETIC COST"]]);
                                decimal accsCostAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["DFA COST"]]);
                                decimal paintCostAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["PAINT PROTECTION COST"]]);



                                decimal financeSubsidy = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FINSUB"]]);
                                decimal rciFinanceCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["RCI FINANCE COMMISSION"]]);
                                decimal financeCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["OTHER FINANCE COMMISSION"]]);
                                decimal selectCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["SELECTIONS BONUS"]]);
                                decimal proPlusCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["PRO PLUS BONUS"]]);

                                bool isFinanced = (financeSubsidy + rciFinanceCommission + financeCommission + selectCommission + proPlusCommission) != 0;


                                Deal d = new Deal(); //initialise new one
                                d.CreatedDate = fileDate;
                                d.WhenNew = fileDate;
                                d.StockNumber = $"{stockNumber}/{sequence}";
                                d.Customer = HelpersService.LimitTo(rowCols[headerLookup["SALUTATION"]], 50); ;
                                d.Reg = rowCols[headerLookup["REGISTRATION NUMBER"]];
                                d.Franchise_Id = fran.Id;
                                d.OrderType_Id = oType.Id;
                                d.IsFinanced = isFinanced;
                                d.TotalNLProfit = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["TOTAL PROFIT"]]);
                                //d.TotalVehicleProfit = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["TOTAL PROFIT"]]);
                                d.Site_Id = site.Id;
                                d.Sale = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["SALE PRICE"]]);
                                d.Discount = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["DISCOUNT"]]);
                                d.PartExOverAllowance1 = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["TRADE IN OVERALLOWANCE"]]);
                                d.CoS = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["VCOST"]]);
                                d.FinanceSubsidy = financeSubsidy;
                                d.NewBonus1 = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FACTORY BONUS"]]);
                                d.NewBonus2 = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["REG BONUS"]]);
                                d.BrokerCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["BROKER"]]);
                                d.AccessoriesSale = accsSaleAsGiven + paintSaleAsGiven - salePaint - saleWheelGuard;
                                d.AccessoriesCost = accsCostAsGiven + paintCostAsGiven - costPaint - costWheelGuard;
                                d.FuelSale = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FUEL SALE"]]);
                                d.FuelCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FUEL COST"]]);
                                d.WarrantySale = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["WARRANTY SALE"]]);
                                d.WarrantyCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["WARRANTY COST"]]);
                                d.CosmeticInsuranceCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["COSMETIC COMMISSION"]]);
                                d.CosmeticInsuranceSale = cosmeticSaleAsGiven - saleTyreAlloy - saleTyre;
                                d.CosmeticInsuranceCost = cosmeticCostAsGiven - costTyreAlloy - costTyre;
                                d.PaintProtectionSale = salePaint;
                                d.PaintProtectionCost = costPaint;
                                d.GapInsuranceCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["GAP COMMISSION"]]);
                                d.GapInsuranceSale = gapSaleAsGiven;
                                d.GapInsuranceCost = gapCostAsGiven;
                                d.TyreInsuranceSale = saleTyre;
                                d.TyreInsuranceCost = costTyre;
                                d.TyreAndAlloyInsuranceSale = saleTyreAlloy;
                                d.TyreAndAlloyInsuranceCost = costTyreAlloy;
                                d.WheelGuardSale = saleWheelGuard;
                                d.WheelGuardCost = costWheelGuard;
                                d.ServicePlanCost = costServicePlan;
                                d.ServicePlanSale = saleServicePlan;
                                d.ServicePlanSale = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["SERVICE PLAN SALE"]]);
                                d.ServicePlanCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["SERVICE PLAN COST"]]);
                                d.OemDeliverySale = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["DELIVERY SALE"]]);
                                d.OemDeliveryCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["DELIVERY COST"]]);
                                d.RCIFinanceCommission = rciFinanceCommission;
                                d.FinanceCommission = financeCommission;
                                d.SelectCommission = selectCommission;
                                d.ProPlusCommission = proPlusCommission;
                                d.StandardsCommission = 0; // not available, almost certainly zero
                                d.PDICost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["PDI COST"]]);
                                d.MechPrep = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["MECHANICAL PREP"]]);
                                d.BodyPrep = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["BODY PREP"]]);
                                d.IntroCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["INTRO COMMISSION"]]);
                                d.Error = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["ERROR"]]);
                                d.InvoiceDate = null;
                                d.IsInvoiced = false;
                                d.IsLateCost = false; // by definition can't be for an open order
                                d.Salesman_Id = salesman?.Id;
                                d.FinanceCo = rowCols[headerLookup["FINANCE COMPANY NAME"]];
                                if (registeredDate != null) { d.RegisteredDate = registeredDate; }
                                d.Model = HelpersService.LimitTo(rowCols[headerLookup["TEXT MODEL"]], 50);
                                d.Description = HelpersService.LimitTo(rowCols[headerLookup["DESCRIPTION"]], 50);
                                d.ActualDeliveryDate = deliveryDateToUse;
                                d.VehicleAge = Math.Max(0, vehicleAge); //else new cars have negative as order before comes into stock.  Unwanted.
                                if (stockDate != null) { d.StockDate = stockDate; }
                                d.VehicleClass_Id = vehClass.Id;
                                d.VehicleType_Id = vehType.Id;
                                d.OrderDate = orderDate;
                                d.IsDelivered = false; //assumed
                                d.VariantTxt = HelpersService.LimitTo(rowCols[headerLookup["TEXT MODEL"]], 50); //ish
                                d.Units = 1;
                                d.HandoverDate = handoverDate;
                                d.AccountingDate = deliveryDateToUse;
                                d.EnquiryNumber = enquiryNumber;
                                d.VehicleSource = vehicleSource;

                                //profit subtotals
                                //d.FAndIProfit = d.FinanceSubsidy + d.PaintProtectionSale + d.PaintProtectionCost + d.WarrantySale + d.WarrantyCost +
                                //    d.CosmeticInsuranceSale + d.CosmeticInsuranceCost + d.CosmeticInsuranceCommission + d.GapInsuranceSale + d.GapInsuranceCost
                                //    + d.GapInsuranceCommission + d.ServicePlanSale + d.ServicePlanCost + d.RCIFinanceCommission + d.FinanceCommission
                                //    + d.SelectCommission + d.ProPlusCommission + d.StandardsCommission
                                //    + d.TyreAlloySale + d.TyreAlloyCost 
                                //    //+                                d.TyreSale + d.TyreCost
                                //    + d.WheelGuardSale + d.WheelGuardCost;

                                //products
                                d.HasServicePlan = d.ServicePlanSale > 0;
                                d.HasCosmeticInsurance = specCommLine != null ? specCommLine.HasCosmetic : false;// d.CosmeticInsuranceSale > 0 || d.CosmeticInsuranceCommission > 0;
                                d.HasGapInsurance = specCommLine != null ? specCommLine.HasGap : false;// d.GapInsuranceSale > 0 || d.GapInsuranceCommission > 0;
                                d.HasPaintProtection = specCommLine != null ? specCommLine.HasPaint : false;
                                d.HasWarranty = specCommLine != null ? (specCommLine.HasWarranty || specCommLine.HasWarranty2Yr || specCommLine.HasWarrantyLifetime) : false;
                                d.HasTyreInsurance = specCommLine != null ? specCommLine.HasTyre : false;
                                d.HasTyreAndAlloyInsurance = specCommLine != null ? specCommLine.HasTyreAndAlloy : false;
                                d.HasWheelGuard = specCommLine != null ? specCommLine.HasWheelGuard : false;
                                //d.TotalProductCount = HelpersService.CountTrue(d.HasServicePlan, d.HasPaintProtection, d.HasCosmeticInsurance, d.HasGapInsurance, d.HasWarranty, d.HasTyreAlloy, d.HasTyre, d.HasWheelGuard.Value);

                                d.LastUpdated = DateTime.UtcNow;
                                d.OemReference = oemRef;

                                incomingDeals.Add(d);
                            }

                            catch (Exception err)
                            {

                                if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                                errorCount++;
                                continue;
                            }

                        }
                        DateTime finishedInterpetFile = DateTime.UtcNow;



                        //add the new ones
                        var newItems = incomingDeals.Except(dbDealsAtOrderedStatus.Where(d => !d.IsRemoved), new DealStockNoComp());
                        newCount = newItems.Count();

                        try
                        {
                            string originalSource = "OpenOrders" + fileDate.ToString("yyyyMMdd_HHmmss");
                            foreach (var item in newItems)
                            {
                                item.OriginalSource = originalSource;
                            }

                            db.Deals.AddRange(newItems);  //add them all in one go
                        }
                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $"Failed addingNewDeals range {err.ToString()}";
                            errorCount++;
                        }




                        //find any removed ones 
                        var removed = dbDealsAtOrderedStatus.Where(r => !r.IsRemoved).Except(incomingDeals, new DealStockNoComp());

                        foreach (var Deal in removed)
                        {
                            Deal.IsRemoved = true;
                            Deal.RemovedDate = DateTime.UtcNow;
                            Deal.IsUpdated = true;
                            Deal.LastUpdated = DateTime.UtcNow;
                            removedCount++;

                            //generate a diff also
                            Diff newDiff = new Diff()
                            {
                                Model = "Deal",
                                ModelIdent = Deal.StockNumber,
                                Key = "IsRemoved",
                                OldValue = "False",
                                NewValue = "True",
                                UpdateDate = fileDate,
                            };

                            diffs.Add(newDiff);

                        }


                        if (errorCount > 200) throw new Exception("Too many errors!");


                        //find changed ones
                        List<Deal> changed = new List<Deal>(10000);
                        var sameItems = incomingDeals.Except(newItems, new DealStockNoComp()); //quick piece to ensure we don't bother trying to diff deals we already know are new
                        var DealDeepComparer = new DealDeepComp(); //instantiate


                        foreach (var incomingDeal in sameItems)
                        {
                            try
                            {
                                var existingDeal = dbDealsAtOrderedStatus.First(s => s.StockNumber == incomingDeal.StockNumber && !s.IsRemoved);
                                List<Diff> thisItemDiffs = DealDeepComp.GetDiffs(existingDeal, incomingDeal, "Order", incomingDeal.StockNumber);
                                if (thisItemDiffs.Count > 0)
                                {
                                    //they are not the same so..
                                    diffs.AddRange(thisItemDiffs);
                                    DealDeepComp.updateExistingDeal(incomingDeal, existingDeal);
                                    changed.Add(existingDeal);
                                    changedCount++;
                                }

                            }

                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + $" failed on making change to item {incomingDeal.StockNumber}" + err.ToString();
                                errorCount++;
                            }


                        }

                        //add diffs to db
                        foreach (Diff diff in diffs)
                        {
                            diff.UpdateDate = fileDate;
                        }
                        db.Diffs.AddRange(diffs);  //add them all in one go



                        logMessage.FinishDate = DateTime.UtcNow;
                        logMessage.ProcessedCount = incomingDeals.Count;
                        logMessage.AddedCount = newCount;
                        logMessage.RemovedCount = removedCount;
                        logMessage.ChangedCount = changedCount;
                        logMessage.IsCompleted = true;
                        logMessage.ErrorCount = errorCount;
                        logMessage.StartCount = openingDbCount;

                        int closingDbCount = 0;


                        //move file

                        try
                        {
                            File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                            if (errorCount > 0)
                            {
                                //we have errors so use the reporter
                                logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                                await CentralLoggingService.ReportError("OpenOrders", logMessage, true);
                            }
                            //completed, do final things
                            try
                            {
                                GlobalParam lastUpdate = db.GlobalParams.First(x => x.Description == "openOrdersLastUpdate");
                                lastUpdate.TextValue = DateTime.UtcNow.ToString();
                                lastUpdate.DateFrom = DateTime.UtcNow;
                                db.SaveChanges();
                                DateTime finishedUpdateDb = DateTime.UtcNow;
                                logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                                logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                                logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;
                                closingDbCount = CountDeals(db);
                                logMessage.FinishCount = closingDbCount;
                            }
                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + "Failed to save to DB" + err.ToString();
                                logMessage.ErrorCount++;
                            }


                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();
                            
                            Logger.Info($"Leaving via door2 ");
                            return;

                            //Logger.Info($"[{DateTime.UtcNow}]  | Result: Started with {openingDbCount} item(s), interpreted {incomingDeals.Count} item(s),  found {newCount} new, {removedCount} removed and {changedCount} changed.  Closed with {closingDbCount } item(s)");
                        }

                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                            logMessage.ErrorCount++;
                            await CentralLoggingService.ReportError("OpenOrders", logMessage);
                            
                            Logger.Error($"Leaving via door3 ");
                            return;
                        }
                    }
                    catch (Exception err)
                    {
                        //Locks.Deals = false;
                        logMessage.FailNotes += $"General failure " + err.ToString();
                        logMessage.ErrorCount++;
                        logMessage.FailNotes = $"{logMessage.ErrorCount} errors " + logMessage.FailNotes;
                        await CentralLoggingService.ReportError("OpenOrders", logMessage);
                        
                        Logger.Error($"Leaving via door4 ");
                        return;
                    }
                    finally
                    {
                        db.ChangeTracker.Clear();
                        //LocksService.OrdersDeals = false;
                    }


                    //return; //should not get here

                    
                }

            }

        }








        private static DateTime? TryParseDate(string dateStringIn)
        {
            if (dateStringIn == "--/--/----") return null;
            if (dateStringIn == "-- / --/ ----") return null;
            return DateTime.ParseExact(dateStringIn, "dd/MM/yyyy", null);
        }

        private static DateTime? TryParseDateTime(string dateStringIn)
        {
            if (dateStringIn.Contains("-")) return null;
            return DateTime.ParseExact(dateStringIn, "dd/MM/yyyyHH:mm", CultureInfo.InvariantCulture);
        }



        private static int CountDeals(CPHIDbContext db)
        {
            return db.Deals.Where(x => !x.IsRemoved).Count();// FromSqlRaw ("SELECT COUNT (*) FROM Deals WHERE IsRemoved = 0");
        }



        private static Dictionary<string, int> createHeaderDictionary(string[] headers)
        {
            return new Dictionary<string, int>()
                    {
                       {"SALES ORDER NUMBER",Array.IndexOf(headers,"SALES ORDER NUMBER")},
                        {"VEHICLE NUMBER",Array.IndexOf(headers,"VEHICLE NUMBER")},
                        {"VEHICLE SEQUENCE NUMBER",Array.IndexOf(headers,"VEHICLE SEQUENCE NUMBER")},
                        {"SALUTATION",Array.IndexOf(headers,"SALUTATION")},
                        {"SALES EXECUTIVE",Array.IndexOf(headers,"SALES EXECUTIVE")},
                        {"REGISTRATION NUMBER",Array.IndexOf(headers,"REGISTRATION NUMBER")},
                        {"MANUFACTURER",Array.IndexOf(headers,"MANUFACTURER")},
                        {"MODEL",Array.IndexOf(headers,"MODEL")},
                        {"VARIANT",Array.IndexOf(headers,"VARIANT")},
                        {"TEXT MANUFACT",Array.IndexOf(headers,"TEXT MANUFACT")},
                        {"TEXT MODEL",Array.IndexOf(headers,"TEXT MODEL")},
                        {"DESCRIPTION",Array.IndexOf(headers,"DESCRIPTION")},
                        {"VEHICLE TYPE",Array.IndexOf(headers,"VEHICLE TYPE")},
                        {"VARIANT CLASS",Array.IndexOf(headers,"VARIANT CLASS")},
                        {"SALES ORDER TYPE",Array.IndexOf(headers,"SALES ORDER TYPE")},
                        {"FINANCE",Array.IndexOf(headers,"FINANCE")},
                        {"TOTAL PROFIT",Array.IndexOf(headers,"TOTAL PROFIT")},
                        {"BRANCH CODE",Array.IndexOf(headers,"BRANCH CODE")},
                        {"SALE PRICE",Array.IndexOf(headers,"SALE PRICE")},
                        {"DISCOUNT",Array.IndexOf(headers,"DISCOUNT")},
                        {"TRADE IN OVERALLOWANCE",Array.IndexOf(headers,"TRADE IN OVERALLOWANCE")},
                        {"VCOST",Array.IndexOf(headers,"VCOST")},
                        {"FINSUB",Array.IndexOf(headers,"FINSUB")},
                        {"FACTORY BONUS",Array.IndexOf(headers,"FACTORY BONUS")},
                        {"REG BONUS",Array.IndexOf(headers,"REG BONUS")},
                        {"BROKER",Array.IndexOf(headers,"BROKER")},
                        {"DFA SALE",Array.IndexOf(headers,"DFA SALE")},
                        {"DFA COST",Array.IndexOf(headers,"DFA COST")},
                        {"FUEL SALE",Array.IndexOf(headers,"FUEL SALE")},
                        {"FUEL COST",Array.IndexOf(headers,"FUEL COST")},
                        {"WARRANTY SALE",Array.IndexOf(headers,"WARRANTY SALE")},
                        {"WARRANTY COST",Array.IndexOf(headers,"WARRANTY COST")},
                        {"COSMETIC COMMISSION",Array.IndexOf(headers,"COSMETIC COMMISSION")},
                        {"COSMETIC SALE",Array.IndexOf(headers,"COSMETIC SALE")},
                        {"COSMETIC COST",Array.IndexOf(headers,"COSMETIC COST")},
                        {"GAP COMMISSION",Array.IndexOf(headers,"GAP COMMISSION")},
                        {"GAP SALE",Array.IndexOf(headers,"GAP SALE")},
                        {"GAP COST",Array.IndexOf(headers,"GAP COST")},
                        {"SERVICE PLAN SALE",Array.IndexOf(headers,"SERVICE PLAN SALE")},
                        {"SERVICE PLAN COST",Array.IndexOf(headers,"SERVICE PLAN COST")},
                        {"DELIVERY SALE",Array.IndexOf(headers,"DELIVERY SALE")},
                        {"DELIVERY COST",Array.IndexOf(headers,"DELIVERY COST")},
                        {"RCI FINANCE COMMISSION",Array.IndexOf(headers,"RCI FINANCE COMMISSION")},
                        {"OTHER FINANCE COMMISSION",Array.IndexOf(headers,"OTHER FINANCE COMMISSION")},
                        {"SELECTIONS BONUS",Array.IndexOf(headers,"SELECTIONS BONUS")},
                        {"PRO PLUS BONUS",Array.IndexOf(headers,"PRO PLUS BONUS")},
                        {"STANDARDS",Array.IndexOf(headers,"STANDARDS")},
                        {"PDI COST",Array.IndexOf(headers,"PDI COST")},
                        {"MECHANICAL PREP",Array.IndexOf(headers,"MECHANICAL PREP")},
                        {"BODY PREP",Array.IndexOf(headers,"BODY PREP")},
                        {"INTRO COMMISSION",Array.IndexOf(headers,"INTRO COMMISSION")},
                        {"AGE",Array.IndexOf(headers,"AGE")},
                        {"ADOPTION DATE",Array.IndexOf(headers,"ADOPTION DATE")},
                        {"ERROR",Array.IndexOf(headers,"ERROR")},
                        {"INVOICE DATE",Array.IndexOf(headers,"INVOICE DATE")},
                        {"REGISTRATION DATE",Array.IndexOf(headers,"REGISTRATION DATE")},
                        {"DELIVERY DATE",Array.IndexOf(headers,"DELIVERY DATE")},
                        {"DATE ORDER CONFIRMED",Array.IndexOf(headers,"DATE ORDER CONFIRMED")},
                        {"PROGRESS CODE",Array.IndexOf(headers,"PROGRESS CODE")},
                        {"BRAND",Array.IndexOf(headers,"BRAND")},
                        {"FINANCE COMPANY NAME",Array.IndexOf(headers,"FINANCE COMPANY NAME")},
                        {"HANDOVER DATE",Array.IndexOf(headers,"HANDOVER DATE")},
                        {"HANDOVER TIME",Array.IndexOf(headers,"HANDOVER TIME")},
                        {"ENQUIRY NUMBER",Array.IndexOf(headers,"ENQUIRY NUMBER")},
                        {"STOCK SOURCE",Array.IndexOf(headers,"STOCK SOURCE")},
                        {"PAINT PROTECTION COST",Array.IndexOf(headers,"PAINT PROTECTION COST")},
                        {"PAINT PROTECTION SALE",Array.IndexOf(headers,"PAINT PROTECTION SALE")},
                        {"COMMISSION NUMBER",Array.IndexOf(headers,"COMMISSION NUMBER")},


                    };
        }




    }
}
