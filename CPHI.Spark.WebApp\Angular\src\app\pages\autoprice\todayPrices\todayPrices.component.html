<nav class="navbar">
    <nav class="generic">
        <h4 id="pageTitle">
            Today's Price Changes&nbsp;
            <sourceDataUpdate *ngIf="!constants.environment.showLatestSnapshotDate" [chosenDate]="constants.LastUpdatedDates.AutotraderAdverts"></sourceDataUpdate>
        </h4>

        <!-- Choose effective date -->
        <div id="chooseEffectiveDate" class="me-4">
            <span class="me-2">Choose date:</span>
            <input type="date" #inputElem (change)="onChosenNewEffectiveDate(inputElem.value)"
                [ngModel]="service.chosenDateAsString">
        </div>

        <!-- Show / hide new vehicles -->
        <sliderSwitch text="New Vehicles" (toggle)="toggleIncludeNewVehicles()"
            [defaultValue]="service.includeNewVehicles">
        </sliderSwitch>

        <!-- Show / hide small changes -->
        <sliderSwitch text="Small Changes" (toggle)="toggleShowSmallPriceChanges()"
            [defaultValue]="service.showSmallPriceChanges">
        </sliderSwitch>


        <!-- Include unpublished -->
        <sliderSwitch text="Un-published" (toggle)="toggleIncludeUnPublishedAds()"
        [defaultValue]="service.includeUnPublishedAds">
        </sliderSwitch>

        <!-- Only Key Changes -->
        <sliderSwitch text="Only Key" (toggle)="toggleOnlyKeyChanges()"
        [defaultValue]="service.onlyKeyChanges">
        </sliderSwitch>

        <!-- Summarise by site -->
        <sliderSwitch *ngIf="constants.RetailerSites.length > 1" text="By site"
            (toggle)="toggleSummariseBySite()" [defaultValue]="service.summariseBySite">
        </sliderSwitch>

        <!-- Mark prices as approved -->
        <ng-container *ngIf="constants.environment.showApproveAutoPrices">
            <button class="btn btn-success subButton" *ngIf="getSelectedRowsCount()>0"
            id="markPricesAsApproved"
            [ngClass]="{'active':!! (getSelectedRowsCount() > 0)}"
            (click)="confirmPriceChange()">{{markPricesApprovedMessage()}}</button>
        </ng-container>
    </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
    <div class="content-new">
        <div class="content-inner-new">
            <instructionRow *ngIf="service.priceChangesRowData && service.priceChangesRowData.length === 0"
                    [isDanger]="true"
                    [message]="'No price changes to display. '">
                </instructionRow>

                <instructionRow *ngIf="!service.priceChangesRowData  || service.priceChangesRowData.length !== 0"
                    [message]="priceChangesHeaderMessage()">
                </instructionRow>

            <div *ngIf="service.priceChangesRowData && service.priceChangesRowData.length > 0" id="gridHolder">
                <statusBar (excelExportClick)="excelExport()" [gridColumnApi]="service.gridColumnApi"
                    [gridApi]="service.gridApi" [gridOptions]="gridOptions"></statusBar>

                <ag-grid-angular [components]="components" [class]="constants.getGridClass()"
                    [gridOptions]="gridOptions"></ag-grid-angular>
            </div>
        </div>
    </div>
</div>