<!-- <ng-template #modalRef let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title"><i class="fas fa-comment"></i> Edit comments  </h4> 
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div  class="modal-body"> -->

<div id="commentsHolder" [ngClass]="{ 'inModal': inModal }">
  
  <!-- ................................... -->
  <!-- Existing comments -->
  <!-- ................................... -->
  <div class="comment" *ngFor="let comment of row.comments; let i = index">

    <!-- Delete button -->
    <div *ngIf="canDelete(comment)" class="deleteButton" (click)="deleteComment(comment)">
      <i class="badFont deleteComment fas fa-times-circle"></i>
    </div>

     <!-- Edit button -->
     <div *ngIf="canEdit(comment)" class="editButton" (click)="editComment(comment)">
      <i class="goodFont editComment fas fa-edit"></i>
    </div>


    <!-- Comment text -->
    <!-- <div *ngIf="!comment.isEditing" class="commentText">
      {{comment.Text}}
    </div> -->
    <!-- *ngIf="comment.isEditing" -->
      <textarea [disabled]="!comment.isEditing" class="commentText" (keydown.enter)="saveExistingComment(comment)" ngbAutofocus [(ngModel)]="comment.Text"></textarea>


    <!-- Save button -->
    <div (click)="saveExistingComment(comment)" class="saveButton"  *ngIf="comment.isEditing">
      <i class="goodFont addComment fas fa-save"></i>
    </div>

    <!-- Name and when -->
    <div class="personName">
      {{ comment.Person }} {{comment.CommentDate |cph:'dateAndTime':0 }}
    </div>


  </div>


  <!-- ................................... -->
  <!-- New Comment -->
  <!-- ................................... -->
  <div class="comment" >


    <!-- Comment text -->
    <div  class="commentText">
      <textarea (keydown.enter)="saveComment()" ngbAutofocus [(ngModel)]="newComment.Text"></textarea>
    </div>


    <!-- Save button -->
    <div (click)="saveComment()" class="saveButton"  *ngIf="newComment?.Text.length > 0">
      <i class="goodFont addComment fas fa-save"></i>
    </div>

    <div class="personName">
      {{ service.selections.user.Name }}  
    </div>


  </div>

</div>



<table id="commentsTable" class="cph fullWidth darkerRows ">
  <tbody>

    <!-- Existing Comment -->
    <!-- <tr *ngFor="let comment of comments; let i = index">
      <td>{{ comment.CommentDate |cph:'dateAndTime':0 }}
      </td>
      <td class="commentCell">
        <div id="commentsCellContentHolder">
          <span *ngIf="!comment.isEditing">{{comment.Text}}</span>
          <input *ngIf="comment.isEditing" (keydown.enter)="saveExistingComment(comment)" ngbAutofocus
            [(ngModel)]="comment.Text" />
          <div (click)="saveExistingComment(comment)" *ngIf="comment.isEditing"><i
              class="goodFont addComment fas fa-save"></i></div>
        </div>
      </td>
      <td>
        <div class="initials long">{{ comment.Person }}</div>
      </td>
      <td>
        <div class="buttonsHolder">

          <div (click)="comment.isEditing = true" *ngIf="!comment.isEditing"><i
              class="goodFont addComment fas fa-edit"></i></div>

          <div (click)="deleteComment(comment)">
            <i class="badFont deleteComment fas fa-times-circle"></i>
          </div>

        </div>
      </td>
    </tr> -->


    <!-- New Comment -->
    <!-- <tr class="newComment">
      <td>{{ newComment.CommentDate |cph:'date':0 }}</td>
      <td>
        <input (keydown.enter)="saveComment()" ngbAutofocus [(ngModel)]="newComment.Text" />
      </td>
      <td>

      </td>
      <td>

        <div *ngIf="newComment?.Text.length > 0" (click)="saveComment()"><i
            class="goodFont addComment fas fa-check-circle"></i></div>
      </td>
    </tr> -->
  </tbody>
</table>
<!-- </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">{{service.constants.translatedText.Close}}</button>
  </div>
</ng-template> -->