﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Repository;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
  public interface ITableStatesService
  {
    Task DeleteTableState(int stateId, string pageName);
    Task<AutoPriceTableState> GetLastLoadedTableState(string pageName);
    Task<AutoPriceTableStateParams> GetTableState(string label, string pageName);
    Task<IEnumerable<TableStateLabelAndId>> GetTableStateLabels(string pageName, bool? isRenault);
    Task<AutoPriceTableState> SaveTableState(AutoPriceTableStateParams parms);
    Task ShareTableState(ShareTableStateParams parms, DealerGroupName userDealerGroupName);
    Task<IEnumerable<ShareTableStateDetails>> GetSharedTableStateLabels(string pageName, bool isRenault);
    Task<AutoPriceTableStateParams> GetTableStateById(int id, DealerGroupName dealerGroupName);
    Task SetStandardTableState(StandardTableStateParams parms);
    Task UnsetStandardTableState(List<int> tableStateIds, DealerGroupName dealerGroupName);
    Task<IEnumerable<StandardTableStateDetails>> GetStandardTableStateLabels(string pageName, bool isRenault);
    Task<IEnumerable<AvailableTableStateDetails>> GetAvailableTableStateLabels(string pageName, bool isRenault);
    Task<bool> PatchAutoPriceTableState(int id, AutoPriceTableStatePatch dto, int userId);
    Task<IEnumerable<StandardTableStateDetails>> GetSparkTableStateLabels(string pageName, bool isRenault);
  }
  public class TableStatesService : ITableStatesService
  {
    private readonly IUserService userService;
    private readonly IConfiguration configuration;
    private readonly string _connectionString;

    public TableStatesService(
        IUserService userService, IConfiguration configuration
        )
    {
      this.userService = userService;
      this.configuration = configuration;
      DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
      string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
      _connectionString = configuration.GetConnectionString(dgName);
    }

    //Table States

    public async Task<AutoPriceTableState> SaveTableState(AutoPriceTableStateParams parms)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.SaveTableState(parms, userService.GetUserId());
    }


    public async Task DeleteTableState(int stateId, string pageName)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      await tableStatesDataAccess.DeleteTableState(stateId, userService.GetUserId(), pageName);
    }


    public async Task<AutoPriceTableStateParams> GetTableState(string label, string pageName)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.GetTableState(label, userService.GetUserId(), pageName);
    }

    public async Task<IEnumerable<TableStateLabelAndId>> GetTableStateLabels(string pageName, bool? isRenault)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.GetTableStateLabels(userService.GetUserId(), pageName, isRenault);
    }


    public async Task<AutoPriceTableState> GetLastLoadedTableState(string pageName)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.GetLastLoadedTableState(userService.GetUserId(), pageName);
    }

    public async Task ShareTableState(ShareTableStateParams parms, DealerGroupName dealerGroupName)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      await tableStatesDataAccess.ShareTableState(parms, dealerGroupName);
    }

    public async Task<IEnumerable<ShareTableStateDetails>> GetSharedTableStateLabels(string pageName, bool isRenault)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      var dealerGroupName = userService.GetUserDealerGroupName();
      return await tableStatesDataAccess.GetSharedTableStateLabels(userService.GetUserId(), pageName, isRenault, dealerGroupName);
    }

    public async Task<AutoPriceTableStateParams> GetTableStateById(int id, DealerGroupName dealerGroupName)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.GetTableStateById(id, userService.GetUserId(), dealerGroupName);
    }

    public async Task SetStandardTableState(StandardTableStateParams parms)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      await tableStatesDataAccess.SetStandardTableState(parms, userService.GetUserId(), userService.GetIdFromAccessToken("DealerGroupId"));
    }

    public async Task UnsetStandardTableState(List<int> tableStateIds, DealerGroupName dealerGroupName)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      await tableStatesDataAccess.UnsetStandardTableState(tableStateIds, dealerGroupName);
    }

    public async Task<IEnumerable<StandardTableStateDetails>> GetStandardTableStateLabels(string pageName, bool isRenault)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.GetStandardTableStateLabels(userService.GetUserId(), pageName, userService.GetIdFromAccessToken("DealerGroupId"), isRenault);
    }

    public async Task<IEnumerable<StandardTableStateDetails>> GetSparkTableStateLabels(string pageName, bool isRenault)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.GetSparkTableStateLabels(userService.GetUserId(), pageName, userService.GetIdFromAccessToken("DealerGroupId"), isRenault);
    }

      public async Task<IEnumerable<AvailableTableStateDetails>> GetAvailableTableStateLabels(string pageName, bool isRenault)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.GetAvailableTableStateLabels(userService.GetUserId(), pageName, userService.GetIdFromAccessToken("DealerGroupId"), isRenault);
    }

    public async Task<bool> PatchAutoPriceTableState(int id, AutoPriceTableStatePatch dto, int userId)
    {
      var tableStatesDataAccess = new TableStatesDataAccess(_connectionString);
      return await tableStatesDataAccess.PatchAutoPriceTableState(userId, id, dto); 
    }
  }
}