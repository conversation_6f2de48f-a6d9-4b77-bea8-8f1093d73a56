import {Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {Chart, ChartConfiguration} from 'chart.js';
import {Subscription} from 'rxjs';
import {CphPipe, FormatType} from 'src/app/cph.pipe';
import {CompetitorSummary} from "src/app/model/CompetitorSummary";
import {CompetitorVehicle} from "src/app/model/CompetitorVehicle";
import {ConstantsService} from 'src/app/services/constants.service';
import {BubbleChartFields} from "../BubbleChartFields";
import {CompetitorAnalysisService} from '../competitorAnalysis.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {AutoPriceInsightsModalService} from '../../autoPriceInsightsModal/autoPriceInsightsModal.service';
// Import the plugin for adding labels to bubbles
import ChartDataLabels from 'chartjs-plugin-datalabels';

// Register the plugin
Chart.register(ChartDataLabels);

export interface SegmentSummary {
   name: string;
   averagePP: number;
}

export interface VehicleChartItem{
   x: number;
   y: number;
   r: number;
   label?: string;
}

@Component({
   selector: 'competitorAnalysisChart',
   templateUrl: './competitorAnalysisChart.component.html',
   styleUrls: ['./competitorAnalysisChart.component.scss']
})
export class CompetitorAnalysisChartComponent implements OnInit {

   @ViewChild('competitorAnalysisCanvas', {static: true}) competitorAnalysisCanvas: ElementRef;
   @Input() selectedXYFields: BubbleChartFields;
   @Input() fullScreen: boolean;

   competitorAnalysisChart: Chart;
   chartViewChangedSubscription: Subscription;
   hoverLabel: { newPP: string, newPriceAndChange: string };
   hoveredItem: CompetitorVehicle;
   segmentSummary: SegmentSummary[];
   vehicleIndicesMap: number[] = [];

   // lastHoveredYValue: number;

   constructor(
      public service: CompetitorAnalysisService,
      public constantsService: ConstantsService,
      public cphPipe: CphPipe,
      public getDataService: GetDataMethodsService,
      public autoPriceInsightsService: AutoPriceInsightsModalService
   ) {
   }

   ngOnInit(): void {
      this.createCompetitorAnalysisChart(this.service.params.CompetitorSummary);
      this.service.chartRef = this;
   }

   ngOnDestroy() {
      this.service.chartRef = null;
   }

   dealWithNewData(competitorSummary: CompetitorSummary) {
      this.service.params.CompetitorSummary = competitorSummary;
      this.createCompetitorAnalysisChart(this.service.params.CompetitorSummary);
   }

   getCompetitorAnalysisSummaryByChannel(): SegmentSummary[] {
      const vehicles: CompetitorVehicle[] = this.service.params.CompetitorSummary.CompetitorVehicles;

      const segments = vehicles.map(vehicle => vehicle.Segment);
      const uniqueSegments = Array.from(new Set(segments));

      const segmentSummary = uniqueSegments.map(segment => {
        const segmentVehicles = vehicles.filter(vehicle => vehicle.Segment === segment && !vehicle.IsOurVehicle);

        let totalPP = 0;
        segmentVehicles.forEach(vehicle => {
            totalPP += vehicle.PricePosition ;
        });

        const averagePP = totalPP / segmentVehicles.length;

        return {
          name: segment,
          averagePP: averagePP
        };
      });

      return segmentSummary;
    }

   createCompetitorAnalysisChart(competitorSummary: CompetitorSummary) {
      this.segmentSummary = this.getCompetitorAnalysisSummaryByChannel();

      let vehicleChartData: VehicleChartItem[] = [];
      let bubbleColours: string[] = [];
      // Reset the vehicle indices map
      this.vehicleIndicesMap = [];

      if (!competitorSummary) {
         return;
      }

      const vehicleList = [...competitorSummary.CompetitorVehicles];
      const ourVehicle = vehicleList.find(x => x.IsOurVehicle);
      const modalItem = this.autoPriceInsightsService.modalItem;

      if (ourVehicle != null && ourVehicle.AdvertisedPrice as number > 0) {

           // From an advert
           if (modalItem?.AdvertDetail?.isTradePricing) {

            ourVehicle.TradePrice = ourVehicle.AdvertisedPrice as number- (modalItem.AdvertDetail.tradeMarginAmount ?? 0);
            ourVehicle.TradePrice = ourVehicle.TradePrice * (modalItem.AdvertDetail.tradeMarginPercentage ?? 1);
            ourVehicle.TradeAdjustmentAmount = modalItem.AdvertDetail.tradeMarginAmount ?? 0;
            ourVehicle.TradeAdjustmentPercentage = modalItem.AdvertDetail.tradeMarginPercentage ?? 1;

           } // From a valuation
           else if (this.service.params.TradePriceSetting?.IsTradePricing) {

            const tradePriceSetting = this.service.params.TradePriceSetting;

            ourVehicle.TradePrice = ourVehicle.AdvertisedPrice as number - (tradePriceSetting.MarginAmount ?? 0);
            ourVehicle.TradePrice = ourVehicle.TradePrice * (tradePriceSetting.MarginPercentage ?? 1);
            ourVehicle.TradeAdjustmentAmount = tradePriceSetting.MarginAmount ?? 0;
            ourVehicle.TradeAdjustmentPercentage = tradePriceSetting.MarginPercentage ?? 1;
           }

           ourVehicle.IsTradeAdjusted = modalItem?.AdvertDetail?.isTradePricing

      }

      // Filter vehicles with positive price position and store their original indices
      vehicleList.forEach((vehicle, originalIndex) => {
         if ((vehicle.PricePosition as number) > 0) {
            // Store the original index in our map
            this.vehicleIndicesMap.push(originalIndex);

            // Determine the label for each bubble
            if (vehicle.IsOurVehicle == true) {
               if (vehicle.IsTradeAdjusted) {
                  bubbleColours.push('rgba(100, 100, 100, 1)'); // Black
               } else {
                  bubbleColours.push('rgba(30, 30, 30, 1)'); // Black
               }
            } else {
               switch (vehicle.Segment) {
                  case 'Independent':
                     bubbleColours.push('rgba(145, 110, 191, 0.5)'); // Purple
                     break;
                  case 'Franchise':
                     bubbleColours.push('rgba(255, 154, 0, 0.5)'); // Orange
                     break;
                  case 'Supermarket':
                     bubbleColours.push('rgba(255, 91, 79, 0.5)'); // Red
                     break;
                  case 'Private':
                     bubbleColours.push('rgba(19, 83, 255, 0.5)'); // Blue
                     break;
                  default: // Other
                     bubbleColours.push('rgba(81, 190, 81, 0.5)'); // Green
               }
            }

            vehicleChartData.push({
               x: vehicle[this.selectedXYFields.x],
               y: vehicle[this.selectedXYFields.y],
               r: 10,
               label: vehicle.IsVehicleWeSold ? 'x' : ''
            });
         }
      });

      let config: ChartConfiguration = {
         data: {
            datasets: [
               {
                  data: vehicleChartData,
                  backgroundColor: bubbleColours
               }
            ]
         },
         options: {

            responsive: true,
            maintainAspectRatio: false,
            interaction: {
               intersect: true,
               mode: 'point'
            },

            layout: {
               padding: {
                  top: 0,
                  right: 0,
                  bottom: 0,
                  left: 0
               }
            },
            plugins: {
               datalabels: {
                  display: true,
                  color: 'white',
                  font: {
                     weight: 'bold',
                     size: 12
                  },
                  backgroundColor: function(context) {
                     // Use the same color as the bubble but with full opacity
                     const bubbleColor = context.dataset.backgroundColor[context.dataIndex];
                     // Extract the color components but set opacity to 1
                     return 'rgba(0,0,0,0)';
                     //return bubbleColor.replace(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/, 'rgba($1, $2, $3, 1)');
                  },
                  borderRadius: 0,
                  padding: 1,
                  align: 'center',
                  anchor: 'center',
                  formatter: function(value) {
                     // Return the label property from the data
                     return value.label;
                  }
               },
               legend: {
                  display: false
               },
               tooltip: {
                  enabled: true,          // Keep tooltip logic active
                  // Make the tooltip box invisible
                  backgroundColor: 'rgba(0, 0, 0, 0)',
                  displayColors: false,
                  borderColor: 'rgba(0, 0, 0, 0)',
                  titleColor: 'rgba(0, 0, 0, 0)',
                  bodyColor: 'rgba(0, 0, 0, 0)',

                  // Return empty strings in callbacks to avoid any text
                  callbacks: {
                     label: () => '',
                     title: () => ''
                  }
               }
               //tooltip:{enabled:false}
            },
            scales: {
               y: {
                  ticks: {
                     autoSkip: true,
                     stepSize: 0.01,
                     maxRotation: 0,
                     minRotation: 0,
                     callback: (value, index, ticks) => {
                        return this.cphPipe.transform(value, this.selectedXYFields.yPipe as FormatType, 0);
                     }
                  }
               },
               x: {
                  min: 0,
                  ticks: {
                     autoSkip: false,
                     stepSize: 10000,
                     maxRotation: 0,
                     minRotation: 0,
                     callback: (value, index, ticks) => {
                        return value === 0 ? 0 : `${Math.floor(value as number / 1000)}k`;
                     }
                  }
               }
            },

            onClick: (event, elements, chart) => {
               if (elements.length > 0) {
                  // Use the mapping to get the correct vehicle index
                  const index = elements[0].index;
                  const vehicleIndex = this.vehicleIndicesMap[index];
                  const websiteSearchIdentifier: string = competitorSummary.CompetitorVehicles[vehicleIndex].WebsiteSearchIdentifier;
                  const vehicleType: string = competitorSummary.CompetitorVehicles[vehicleIndex].VehicleType;
                  const url: string = this.constantsService.buildAdUrl(websiteSearchIdentifier, vehicleType);
                  window.open(url, '_blank').focus();
               } else {
                  const yScale = chart.scales.y;
                  const yValue = yScale.getValueForPixel(event.y);

                  if (this.service.chartRef.selectedXYFields.y === 'PricePosition') {
                     const newPrice: number = this.service.params.VehicleValuationService.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle * ((yValue) - 0.0001);
                     this.service.params.VehicleValuationService.setSellingPriceTo(newPrice);
                  } else {
                     this.service.params.VehicleValuationService.setSellingPriceTo(yValue);
                  }
               }
            },

            onHover: (event, item) => {
               this.hoveredItem = null;

               if (item.length > 0) {
                  // Use the mapping to get the correct vehicle index
                  const index = item[0].index;
                  const vehicleIndex = this.vehicleIndicesMap[index];
                  this.hoveredItem = competitorSummary.CompetitorVehicles[vehicleIndex];
               }
            }
         },
         type: 'bubble'
      }

      if (this.selectedXYFields.yPretty !== 'Price') {
         if ((config.options.scales.y.min as number) > 90) {
            config.options.scales.y.min = 0.9
         }
         config.options.scales.y.max = 1.2
      }

      const followCursor = {
         id: 'followCursor',
         y: undefined,
         afterDraw: chart => {
            const ctx = chart.ctx;
            const xScale = chart.scales.x;
            const yScale = chart.scales.y;

            // Check if the `y` property exists on the plugin (set during the tooltip interception)
            if (followCursor.y !== undefined) {
               const y = yScale.getPixelForValue(followCursor.y);

               ctx.save();
               ctx.beginPath();
               ctx.moveTo(xScale.left, y);
               ctx.lineTo(xScale.right, y);
               ctx.lineWidth = 1;
               ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
               ctx.stroke();
               ctx.restore();
            }
         }
      };
      let isUpdating = false;

      const interceptTooltip = {
         id: 'interceptTooltip',
         beforeEvent: (chart, args, pluginOptions) => {
            const event = args.event;
            if (event.type === 'mousemove' && !isUpdating) {
               if (!chart.tooltip.title || !chart.tooltip.body) {
                  return this.hoverLabel = null;
               }

               const {data, scales: {x, y}} = chart;
               const yCoor = args.event.y;

               // If viewing Mileage x Price Position chart
               if (this.selectedXYFields.y === 'PricePosition') {
                  const newPricePosition = Math.round(y.getValueForPixel(yCoor) * 1000) / 1000;

                  const valuation = this.service.params.Valuation;
                  const currentPrice = this.service.params.AdvertisedPrice;
                  const newSelling = valuation * newPricePosition;
                  const newVsExisting = newSelling - currentPrice;

                  this.hoverLabel = {
                     newPP: this.cphPipe.transform(newPricePosition, 'percent', 1),
                     newPriceAndChange: `${this.cphPipe.transform(newSelling, 'currency', 0)} (${this.cphPipe.transform(newVsExisting, 'currency', 0, true)})`
                  }

                  // Update the `followCursor` plugin's internal `y` state
                  followCursor.y = newPricePosition;
               }
               // If viewing Mileage x Price chart
               else {
                  const newPrice = y.getValueForPixel(yCoor);
                  const currentPrice = this.service.params.AdvertisedPrice;
                  const newVsExisting = newPrice - currentPrice;

                  this.hoverLabel = {
                     newPP: null,
                     newPriceAndChange: `${this.cphPipe.transform(newPrice, 'currency', 0)} (${this.cphPipe.transform(newVsExisting, 'currency', 0, true)})`
                  }

                  // Update the `followCursor` plugin's internal `y` state
                  followCursor.y = newPrice;
               }

               // Trigger a redraw of the chart
               // Set isUpdating to true before updating the chart
               isUpdating = true;
               chart.update();
               // Reset the flag after a short delay
               setTimeout(() => isUpdating = false, 50); // Throttle the updates to avoid recursion
            }
         }
      };

      config.plugins = [followCursor, interceptTooltip];

      this.segmentSummary.forEach(segment => {
         config.plugins.push({
            id: segment.name,
            beforeDraw: (chart, args, options) => {
               const { ctx, scales: { y }, chartArea: { left, right } } = chart;
               ctx.save();

               const yPos = y.getPixelForValue(segment.averagePP);

               ctx.beginPath();
               ctx.lineWidth = 2;
               ctx.strokeStyle = this.getColourForSegment(segment.name);
               ctx.moveTo(left, yPos);
               ctx.lineTo(right, yPos);
               ctx.stroke();
               ctx.restore();
            },
         });
      });

      if (this.competitorAnalysisChart) {
         this.competitorAnalysisChart.destroy();
      }

      this.competitorAnalysisChart = new Chart(this.competitorAnalysisCanvas.nativeElement, config);

      this.service.chartRef = this;
   }

   getTotalAdvertsCount(hoveredItem: CompetitorVehicle) {
      if (hoveredItem.TotalAdvertCount != null) {
         return this.cphPipe.transform(hoveredItem.TotalAdvertCount, 'number', 0)
      }

      //Fetch the total adverts count from the API
      if (!hoveredItem.isFetchingTotalAdverts) {
         hoveredItem.isFetchingTotalAdverts = true;

         this.getDataService.getTotalAdvertsForAdvertiser(hoveredItem).subscribe((totalAdvertCount: number) => {
            hoveredItem.TotalAdvertCount = totalAdvertCount;
            hoveredItem.isFetchingTotalAdverts = false;
            this.competitorAnalysisChart.update();

         })

      }

      return 'Loading...';

   }

   getColourForSegment(segment: string) {
      let colour: string;

      switch (segment) {
         case 'Independent':
            colour = 'rgba(145, 110, 191, 0.5)';
            break;
         case 'Franchise':
            colour = 'rgba(255, 154, 0, 0.5)';
            break;
         case 'Supermarket':
            colour = 'rgba(255, 91, 79, 0.5)';
            break;
         case 'Private':
            colour = 'rgba(19, 83, 255, 0.5)';
            break;
         default:
            colour = 'rgba(20, 20, 20, 1)';
            break;
      }

      return colour;
   }

    tradeAdjustPctWholeNumber(tradeAdjustmentPercentage:number):string{
      if(!tradeAdjustmentPercentage){
         return '100.0%';
      }
      return  this.cphPipe.transform(tradeAdjustmentPercentage,'percent',1)
   }
   tradeAdjustPctImpact( tradeAdjustmentPercentage:number, tradePrice:number):string{
      const impact =    tradePrice / tradeAdjustmentPercentage - tradePrice;
      return this.cphPipe.transform(impact, 'currency', 0,true);

   }
}
