import { Component, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { forkJoin } from 'rxjs';
import { CitNowDetail } from "src/app/model/afterSales.model";
import { CphPipe } from "../../cph.pipe";
import {  CitNowSimpleDetail, CitNowSiteSummary, Month, SiteVM } from '../../model/main.model';
import { AutotraderService } from "../../services/autotrader.service";
import { ConstantsService } from "../../services/constants.service";
import { GetDataMethodsService } from '../../services/getDataMethods.service';
import { SelectionsService } from "../../services/selections.service";
import { DashboardService } from "../dashboard/dashboard.service";
import { CitNowService } from "./citNow.service";
import { CitNowSiteDetailModalComponent } from './citNowSiteDetailModal.component';
import { CitNowVideoBreakdownModalComponent } from "./citNowVideoBreakdownModal.component";


@Component({
  selector: "app-citNow",
  templateUrl: "./citNow.component.html",
  styleUrls: ["./citNow.component.scss"],
})
export class CitNowwComponent implements OnInit {
  @Input() public isSales: boolean;
  @ViewChild('citNowSiteDetailModal', { static: true }) citNowSiteDetailModal: CitNowSiteDetailModalComponent;
  @ViewChild('citNowVideoBreakdownModal', { static: true }) citNowVideoBreakdownModal: CitNowVideoBreakdownModalComponent;

  months: Array<Month>;
  myEventEmitter: EventEmitter<string>;
  mysubscription: any;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService, 

    public dataMethods: GetDataMethodsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public analysis: AutotraderService,
    public router: Router,
    public service: CitNowService,
    public dashboardService: DashboardService
  ) { }

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: true, message: 'Loading...' });
    if(!this.service.isInitialised) this.service.initiateCitNow();
    let isSales: boolean = this.dashboardService.chosenSection.sectionName === 'Aftersales' ? false : true;
    this.chooseDepartment(isSales);
  }


  getData() {

    const type = (this.service.showSales) ? 'Sales' : 'Service';

    let requests = []
    if (this.service.showSales) {
      requests.push(this.dataMethods.getCitNowSalesSiteRows(this.service.monthStartDate)) //[0]
    }
    else {
      requests.push(this.dataMethods.getCitNowServiceSiteRows(this.service.monthStartDate)) //[0]
    }
    requests.push(this.dataMethods.getCitNowChartDataSets(type)) //[1]

    forkJoin(requests).subscribe((result: any) => {
      this.selections.triggerSpinner.next({ show: false });

      this.service.siteRows = result[0];
      this.service.regionalChartDetails = result[1];

      this.service.newDataEmitter.emit();
    })



  }









  onRowClicked(site: CitNowSiteSummary) {
   

    if (this.constants.environment.citNoww.showSimpleCitNowPersonDetail) {
      //SIMPLE
      let siteIds = this.getSiteIds(site).join(',');
      this.dataMethods.GetCitNowSimpleDetail(this.constants.addTimezoneOffset(this.service.monthStartDate), siteIds, this.service.showSales === true).subscribe((results: CitNowSimpleDetail[]) => {

        this.service.simpleDetailedTableRows = results;

        this.citNowVideoBreakdownModal.showModal();
        this.selections.triggerSpinner.next({ show: false })

      }, e => {
        console.error('error getting simple detailed rows')
      })

    } else if (!this.service.showSales) {
      //DETAILED
      let siteIds = this.getSiteIds(site).join(',');

      this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading })
      this.dataMethods.getCitNowDetail(this.constants.addTimezoneOffset(this.service.monthStartDate), siteIds).subscribe((results: CitNowDetail[]) => {

        this.service.detailedTableRows = results;

        this.citNowSiteDetailModal.showModal()
        this.selections.triggerSpinner.next({ show: false })

      }, e => {
        console.error('error getting detailed rows')
      })

    }else{
      //nothing, we can't do detailed info for sales yet
    }
  }

  onRowClickedEDynamix(site: CitNowSiteSummary) {
    let siteIds: string = this.getSiteIds(site).join(',');
    this.service.getEDynamixPeopleRows(siteIds);
  }

  getSiteIds(site:CitNowSiteSummary){
    if(site.IsTotal){
      return this.service.siteRows.filter(x=>x.IsSite).map(x=>x.SiteId)
    }else if(site.IsRegion){
      return this.constants.getSiteIdsForRegion(site.RegionDescription);
    }else{
      return [site.SiteId]
    }
  }



  makeMonths() {
    this.months = this.constants.makeMonths(0, 0);
  }


  selectMonth(month: Month) {
    this.selections.triggerSpinner.next({ show: true });
    this.service.monthStartDate = month.startDate;
    this.getData()
  }

  trackByFunction(index: number) { return index; }


  changeMonth(changeAmount: number) {
    this.selections.triggerSpinner.next({ show: true });
    this.service.monthStartDate = this.constants.addMonths(this.service.monthStartDate,changeAmount);
    this.getData()
  }


  chooseDepartment(isSales: boolean) {
    this.service.showSales = isSales;
    this.getData();
  }




}
