import { TitleCasePipe } from '@angular/common';
import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ColDef, ColumnApi, GridApi, GridO<PERSON>s, RowClickedEvent, ValueGetterParams } from 'ag-grid-community';
import { AutoPriceInsightsModalComponent } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { CustomHeaderService } from 'src/app/components/customHeader/customHeader.service';
import { CphPipe } from 'src/app/cph.pipe';
import { VehicleAdvertWithRating } from 'src/app/model/VehicleAdvertWithRating';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { AutopriceRendererService } from 'src/app/services/autopriceRenderer.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { StockInsightService } from '../../stockInsight.service';
import { AutoPriceInsightsModalService } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service';
import { AutotraderImageCellComponent } from 'src/app/components/autotraderImageCell/autotraderImageCell.component';

@Component({
  selector: 'vehicleDetailsTable',
  templateUrl: './vehicleDetails.component.html',
  styleUrls: ['./vehicleDetails.component.scss']
})
export class VehicleDetailsTableComponent implements OnInit {
  @Input() rowData: VehicleAdvertWithRating[];

  public components: { [p: string]: any; } = {
    agColumnHeader: CustomHeaderNew,
  };

  gridOptions: GridOptions;
  gridApi: GridApi;
  gridColumnApi: ColumnApi;

  constructor(
    public gridHelpersService: AGGridMethodsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public titleCasePipe: TitleCasePipe,
    public constants: ConstantsService,
    private customHeader: CustomHeaderService,
    public service: StockInsightService,
    private autopriceRendererService: AutopriceRendererService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (this.gridApi) {
      this.gridApi.setRowData(changes.rowData.currentValue.filter(x => x.StrategyPrice));
      this.applyGridFilterChanges();
    }
  }

  ngOnInit(): void {
    this.initialiseGrid();
  }

  initialiseGrid() {
    this.gridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) => params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      animateRows: false,
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getHeaderHeight();
        }
      },
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      onRowClicked: (params) => this.onRowClicked(params),
      rowData: this.rowData.filter(x=>x.StrategyPrice),
      onGridReady: (params) => this.onGridReady(params),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
        cellStyle: {
          display: 'flex',
          'align-items': 'center'
        },
        floatingFilter: true,
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true
      },
      columnTypes: {
        number: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true },
        label: { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter', sortable: true },
        special: { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' },
        specialNoFilter: { floatingFilter: false, cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' },
        currency: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true, cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 0) } },
        percent: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true, cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'percent', 0) } }
      },
      columnDefs: this.provideColDefs(),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      onFilterChanged: () => this.applyGridFilterChanges(),
    }
  }

  applyGridFilterChanges(){
    let filteredRows = [];
    this.gridApi.forEachNodeAfterFilter(node => filteredRows.push(node.data));
    this.service.vehicleTableDataUpdatedEmitter.emit(filteredRows)
  }

  onRowClicked(params: RowClickedEvent<any, any>): void {
    const row: VehicleAdvertWithRating = params.data;
    if (!row) { return }
    this.autoPriceInsightsModalService.initialise(row.AdId,[])
    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
  }

  provideColDefs(): ColDef[] {
    return [
      {
        headerName: '',
        colId: 'ImageURLs',
        field: 'ImageURL',
        type: 'image',
        cellRendererFramework: AutotraderImageCellComponent,
        cellRendererParams: { width: 70 },
        width: 30
      },   
      { headerName: 'Description', cellClass: 'wrapText', colId: 'Derivative', field: 'Derivative', valueGetter: (params) => this.titleCasePipe.transform(params.data.Make + ' ' + params.data.Model + ' ' + params.data.Derivative), type: 'label', width: 125 },
      { headerName: 'VRM', colId: 'VehicleReg', field: 'VehicleReg', type: 'special', cellRenderer: (params) => this.autopriceRendererService.regPlateRenderer(params, { fontSize: 1.1, noMargin: true }), width: 40 },
      { headerName: 'Price Position', colId: 'PricePosition', field: 'PricePosition', type: 'percent', width: 30 },
      { headerName: 'Strategy Price', colId: 'StrategyPrice', field: 'StrategyPrice', type: 'currency', width: 30 },
      { headerName: 'Retail Price', colId: 'SuppliedPrice', field: 'AdvertisedPrice', type: 'currency', width: 30 },
      { headerName: 'Vs', sort: 'desc', sortIndex: 1, colId: 'VsPrice', valueGetter: params => params.data.AdvertisedPrice - params.data.StrategyPrice, type: 'currency', width: 30 },
      { headerName: 'Performance Rating', colId: 'PerfRatingScore', field: 'PerfRatingScore', type: 'specialNoFilter', cellRenderer: (params) => this.autopriceRendererService.autoTraderPerformanceRatingSimpleRenderer(params), width: 40 }
    ];
  }
  descriptionGetter(params: ValueGetterParams<any>): any {
    const ad:VehicleAdvertWithRating = params.data;
    const combined = `${ad.Make} ${ad.Model} ${ad.Derivative}`;
    return this.titleCasePipe.transform(combined)
  }

  getImage(params) {
    const row: VehicleAdvertWithRating = params.data;
    if (!row) { return ''; }
    if (!row.ImageURL) { return ''; }

    return `<img style="height: 50px; width: 100%;" src=${row.ImageURL} />`;
  }
}
