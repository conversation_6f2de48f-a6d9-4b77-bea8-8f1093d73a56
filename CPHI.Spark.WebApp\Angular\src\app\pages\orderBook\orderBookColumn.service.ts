
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CphPipe } from 'src/app/cph.pipe';
import { OrderbookTimePeriod } from 'src/app/model/main.model';
import { LateCostOption, OrderOption, SalesmanStat } from 'src/app/model/sales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { OrderbookRow } from "../../model/OrderbookRow";
import { OrderbookSummaryTable } from "../../model/OrderbookSummaryTable";
import { ColDef, ValueGetterParams } from 'ag-grid-community';
import { AuditPassComponent } from 'src/app/_cellRenderers/auditPass.component';
import { CommentsCellComponent } from 'src/app/_cellRenderers/commentsCell.component';
import { ProductsHeaderComponent } from 'src/app/_cellRenderers/productsHeader.component';
import { CPHAutoPriceColDef, CPHColDef } from 'src/app/model/CPHColDef';







@Injectable({
  providedIn: 'root'
})
export class OrderBookColumnService {

  


  constructor(
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public router: Router,
  ) {
  }





  provideColDefs(gridScaleValue: number) {
    let text = this.constants.translatedText;
    let env = this.constants.environment;

   

    let colDefs: CPHAutoPriceColDef[] = [
      
      {columnSection:'Site', headerName: ' ',   colId: 'index', cellRenderer: (params) => params.rowIndex + 1 + '>', width: gridScaleValue * 40 },
      {columnSection:'Site', headerName: text.Site, colId: 'Site', field: 'SiteDescription', width: gridScaleValue * 60, type: 'labelLowPad' },
      {columnSection:'Site', headerName: text.Orderbook_DeliverySite, colId: 'DeliverySiteDescription', field: 'DeliverySiteDescription', width: gridScaleValue * 80, type: 'labelLowPad', hide: env.orderBook_hideDeliverySiteColumn },
      {columnSection:'Order Information', headerName: text.Customer, colId: 'Customer', field: 'Customer', width: gridScaleValue * 90, type: 'label' },
      {columnSection:'Vehicle Information', headerName: text.Stock + " #", colId: 'StockNumber', field: 'StockNumber', width: gridScaleValue * 60, type: 'labelLowPad' },
      {columnSection:'Vehicle Information', headerName: text.Orderbook_OEMReference, colId: 'OEMReference', field: 'OEMReference', width: gridScaleValue * 60, type: 'labelLowPad', hide: env.orderBook_hideOemReferenceColumn },
      {columnSection:'Vehicle Information', headerName: text.Orderbook_FranchiseAbbreviation, colId: 'Franchise', field: 'Franchise', headerTooltip: text.Orderbook_FranchiseCodeTooltip, width: gridScaleValue * 25, type: 'labelLowPad' },
      {columnSection:'Vehicle Information', headerName: text.Orderbook_VehicleClassAbbreviation, colId: 'VehicleClass', field: 'VehicleClass', headerTooltip: text.VehicleClass, width: gridScaleValue * 30, type: 'labelLowPad', hide: env.orderBook_hideVehClassColumn },
      {columnSection:'Vehicle Information', headerName: text.Orderbook_VehicleTypeCodeAbbreviation, field: 'VehicleTypeCode', colId: 'Vtype', width: gridScaleValue * 35, type: 'labelLowPad', hide: env.orderBook_hideVehicleTypeColumn },
      {columnSection:'Order Information', headerName: text.Orderbook_OrderTypeCodeAbbreviation, colId: 'OrderTypeCode', field: 'OrderTypeCode', width: gridScaleValue * 50, type: 'label' },
      {columnSection:'Vehicle Information', headerName: 'Channel Vo', colId: 'ChannelVoDescription', field: 'ChannelVoDescription', width: gridScaleValue * 50, type: 'label', hide: env.orderBook_hideChannelColumn },
      {columnSection:'Order Information', headerName: 'Type', colId: 'TypeVoDescription', field: 'TypeVoDescription', width: gridScaleValue * 50, type: 'label', hide: env.orderBook_hideTypeColumn },
      {columnSection:'Vehicle Information', headerName: text.Model, colId: 'Model', field: 'Model', width: gridScaleValue * 45, type: 'label', hide: env.orderBook_hideModelColumn },
      {columnSection:'Vehicle Information', headerName: text.Orderbook_ModelYearAbbreviation, colId: 'ModelYear', field: 'ModelYear', width: gridScaleValue * 45, type: 'labelNoZeros', hide: env.orderBook_hideModelYearColumn },
      {columnSection:'Vehicle Information', headerName: text.Orderbook_SourceAbbreviation, colId: 'Source', field: 'Source', width: gridScaleValue * 25, type: 'labelLowPad', hide: env.orderBook_hideVehicleSourceColumn },
      {columnSection:'Dates', headerName: text.Orderbook_DaysInStockAbbreviation, colId: 'DIS', field: 'DIS', headerTooltip: text.Orderbook_DaysInStock, shouldAverage:true, width: gridScaleValue * 30, type: 'numberWithColour' },
      {columnSection:'Vehicle Information', headerName: text.Reg, colId: 'Reg', field: 'Reg', cellClass: 'regPlateHolder',  cellRenderer: (params)=>this.regPlateRenderer(params), width: gridScaleValue * 80, filter: 'agTextColumnFilter' },
      {columnSection:'Vehicle Information', headerName: text.Description, colId: 'Description', field: 'Description', width: gridScaleValue * 155, type: 'label' },
      {columnSection:'Order Information', headerName: text.SalesExec, colId: 'SalesmanName', field: 'SalesmanName', width: gridScaleValue * 60, type: 'label' },
      {columnSection:'Order Information', headerName: text.Orderbook_OrderDateAbbreviation, colId: 'OrderDate', field: 'OrderDate', headerTooltip: text.OrderDate, width: gridScaleValue * 40, type:'dateShortYear' },
      {columnSection:'Dates', headerName: text.Orderbook_InvoiceDateAbbreviation, colId: 'InvoiceDate', field: 'InvoiceDate', headerTooltip: text.InvoiceDate, width: gridScaleValue * 40, type:'dateShortYear' },
      {columnSection:'Dates', headerName: text.Orderbook_Del, colId: 'DeliveryDate', field: 'DeliveryDate', headerTooltip: text.Orderbook_DeliveryDateToolTip, width: gridScaleValue * 40, type:'dateShortYear' },
      {columnSection:'Progress', headerName: text.StockList_DateFactoryTransportation, field: 'DateFactoryTransportation', colId: 'DateFactoryTransportation', width: gridScaleValue * 40, type: 'dateShortYear', hide: env.orderBook_hideDateFactoryTransportationColumn  },
      {columnSection:'Progress', headerName: text.StockList_DateVehicleRecondition, field: 'DateVehicleRecondition', colId: 'DateVehicleRecondition', width: gridScaleValue * 40, type: 'dateShortYear', hide: env.orderBook_hideDateVehicleReconditionColumn  },
      {columnSection:'Progress', headerName: text.StockList_DateSiteTransportation, field: 'DateSiteTransportation', colId: 'DateSiteTransportation', width: gridScaleValue * 40, type: 'dateShortYear', hide: env.orderBook_hideDateSiteTransportationColumn  },
      {columnSection:'Progress', headerName: text.StockList_DateSiteArrival, field: 'DateSiteArrival', colId: 'DateSiteArrival', width: gridScaleValue * 40, type: 'dateShortYear', hide: env.orderBook_hideDateSiteArrivalColumn  },
      {columnSection:'Dates', headerName: 'Reserved Date', field: 'ReservedDate', colId: 'ReservedDate', width: gridScaleValue * 40, type: 'dateShortYear', hide: env.orderBook_hideReservedDateColumn  },
      {columnSection:'Dates', headerName: text.Orderbook_DaysToSaleAbbreviation, colId: 'DaysToSale', field: 'DaysToSale', headerTooltip: text.Orderbook_DaysToSaleTooltip, width: gridScaleValue * 30, type: 'number', hide: env.orderBook_hideDaysToSaleColumn },
      {columnSection:'Dates', headerName: text.Orderbook_DaysToDeliverAbbreviation, colId: 'DaysToDeliver', field: 'DaysToDeliver', headerTooltip: text.Orderbook_DaysToDeliverTooltip, width: gridScaleValue * 30, type: 'number', hide: env.orderBook_hideDaysToDeliverColumn },
      {columnSection:'Vehicle Information', headerName: text.Location, colId: 'Location', field: 'Location', headerTooltip: text.Orderbook_PhysicalLocationTooltip, width: gridScaleValue * 70, type: 'label', hide: env.orderBook_hideLocationColumn },
      {columnSection:'Order Information', headerName: text.Orderbook_IsConfirmedAbbreviation, colId: 'IsConfirmed', field: 'IsConfirmed', headerTooltip: text.Orderbook_IsConfirmedTooltip, width: gridScaleValue * 30, type: 'boolean', hide: env.orderBook_hideIsConfirmedColumn },
      {columnSection:'Order Information', headerName: text.Orderbook_IsDeliveredAbbreviation, colId: 'IsDelivered', field: 'IsDelivered', headerTooltip: text.Orderbook_IsDelivered, width: gridScaleValue * 30, type: 'boolean' },
      {columnSection:'Order Information', headerName: text.Orderbook_IsClosedAbbreviation, colId: 'IsClosed', field: 'IsClosed', headerTooltip: text.Orderbook_IsClosedTooltip, width: gridScaleValue * 30, type: 'boolean', hide: env.orderBook_hideIsClosedColumn },
      {columnSection:'Profit', headerName: text.Orderbook_Q, colId: 'Units', field: 'Units', headerTooltip: text.Units, width: gridScaleValue * 25, type: 'number', hide: env.orderBook_hideUnitsColumn },
      {columnSection:'Order Information', headerName: 'Audit', colId: 'AuditPass', field: 'AuditPass', type: 'boolean', width: gridScaleValue * 50, cellRenderer: AuditPassComponent, headerTooltip: 'Audit Pass/Fail/Incomplete', hide: env.orderBook_hideAuditColumn },
      {columnSection:'Profit', headerName: text.Sale, colId: 'Sale', field: 'Sale', width: gridScaleValue * 50, type: 'currencyWithFontColour' },
      {columnSection:'Profit', headerName: text.Discount, colId: 'Discount', field: 'Discount', headerTooltip: text.Discount, width: gridScaleValue * 60, type: 'currencyWithFontColour', hide: env.orderBook_hideDiscountColumn },
      {columnSection:'Profit', headerName: text.Metal, colId: 'MetalProfit', field: 'MetalProfit', width: gridScaleValue * 50, type: 'currencyWithFontColour', hide: env.orderBook_hideMetalColumn },
      {columnSection:'Profit', headerName: text.Other, colId: 'OtherProfit', field: 'OtherProfit', width: gridScaleValue * 50, type: 'currencyWithFontColour', hide: env.orderBook_hideOtherProfitColumn },
      {columnSection:'Profit', headerName: text.Finance, colId: 'FinanceProfit', field: 'FinanceProfit', width: gridScaleValue * 50, type: 'currencyWithFontColour', hide: env.orderBook_hideFinanceProfitColumn },
      {columnSection:'Profit', headerName: text.AddOnProfit, colId: 'AddOnProfit', field: 'AddOnProfit', width: gridScaleValue * 50, type: 'currencyWithFontColour', hide: env.orderBook_hideAddonsColumn },
      {columnSection:'Profit', headerName: text.Profit, colId: 'TotalProfit', field: 'TotalProfit', width: gridScaleValue * 50, type: 'currencyWithFontColour' },
      {columnSection:'Profit', headerName: text.Orderbook_FinanceTypeAbbreviation, colId: 'FinanceType', field: 'FinanceType', width: gridScaleValue * 35, type: 'label', hide: env.orderBook_hideFinanceTypeColumn },
      {
       columnSection:'Profit', headerName: text.AddOns, colId: 'Products', suppressMenu: true, width: gridScaleValue * 170,
        cellRenderer: (params) => this.simplerProductsRenderer(params), excelField: 'TotalProductCount', hide: env.orderBook_hideAddonsColumn, headerComponentFramework: ProductsHeaderComponent// ProductsComponent
      },
      {columnSection:'Profit', headerName: text.Orderbook_L, colId: 'IsLateCost', field: 'IsLateCost', headerTooltip: text.Orderbook_LateCost, width: gridScaleValue * 25, type: 'boolean', hide: env.orderBook_hideIsLateCostColumn },
      {
       columnSection:'Dates', headerName: text.Orderbook_OrderAllocationDateAbbreviation, colId: 'OrderAllocationDate', field: 'OrderAllocationDate',
        headerTooltip: text.Orderbook_OrderAllocationDateTooltip, width: gridScaleValue * 60, type: 'dateShort', hide: env.orderBook_hideOrderAllocationDate
      },
      {columnSection:'Order Information', headerName: text.Orderbook_ChannelAbbreviation, colId: 'Channel', field: 'Channel', headerTooltip: text.Orderbook_SalesChannelTooltip, width: gridScaleValue * 45, type: 'label', hide: env.orderBook_hideSalesChannel },
      {columnSection:'Progress', headerName: text.Comments, colId: 'Comment', cellRenderer: CommentsCellComponent, field: 'Comment', width: gridScaleValue * 100, filter: 'agTextColumnFilter', type: 'labelLowPad', hide: env.orderBook_hideComments },
      {columnSection:'Do not show',
        headerName: text.Comments, colId: 'CommentForSearch', width: gridScaleValue * 100, type: 'labelLowPad', hide: true,
        valueGetter: (params) => this.commentGetter(params),
      },
      
      
      
      {columnSection:'Profit', headerName: 'Last Advertised', colId: 'LastAdvertisedPrice', field: 'LastAdvertisedPrice', hide:true,  width: gridScaleValue * 60, type: 'currencyWithFontColour',  },
      {columnSection:'Profit', headerName: 'Selling vs Last Adv', colId: 'sellingVsLastAdvertised', field: 'sellingVsLastAdvertised', hide:true,  width: gridScaleValue * 60, type: 'currencyWithFontColour',  },
      {columnSection:'Profit', headerName: 'Selling vs Last Adv %', colId: 'sellingVsLastAdvertisedPct', field: 'sellingVsLastAdvertisedPct',hide:true,  width: gridScaleValue * 60, type: 'percent',  },
      

    ] as CPHAutoPriceColDef[];

    return colDefs
  }



  regPlateRenderer(params: any) {
    const row: OrderbookRow = params.data;
    if(!row){return}
    let formattedReg = this.cphPipe.transform(row.Reg,'numberPlate',0); 
    return    `<div class="visibleAboveMd regPlate">    ${formattedReg}</div>
    <div class="visibleBelowLg plainPlate">    ${formattedReg}</div>`
  }

  commentGetter(params: ValueGetterParams): any {
    let deal: OrderbookRow = params.data;
    if(!deal){return;}
    if (params.data.comments.length == 0) return '';
    return deal.comments.map(c => c.Text).join(' | ')
  }



  simplerProductsRenderer(params: any) {
    const row: OrderbookRow = params.data;
    if(!row){return;}

    let result: string = '';

    if(row.IsLateCost){return ''}

    result += `<div class="productsHolder">`
    if (row.IsFinanced) { result += `<i   title="Financed" class="fas fa-coins goodFont"></i>` }
    else { result += `<div  title="Financed" >&#x2219;</div>` }  //was 26AC

    if (row.IsCosmetic) { result += `<i   title="Cosmetic Insurance" class="fas fa-shield-check goodFont"></i>` }
    else { result += `<div  title="Cosmetic Insurance" >&#x2219;</div>` }  

    if (row.IsPaint) { result += `<i   title="Paint Protection" class="fas fa-claw-marks goodFont"></i>` }
    else { result += `<div  title="Paint Protection" >&#x2219;</div>` }

    if (row.IsGap) { result += `<i   title="Gap Insurance" class="fas fa-car-crash goodFont"></i>` }
    else { result += `<div  title="Gap Insurance" >&#x2219;</div>` }

    if (row.IsTyre) { result += `<i   title="Tyre Insurance" class="fas fa-tire goodFont"></i>` }
    else { result += `<div  title="Tyre Insurance" >&#x2219;</div>` }

    if (row.IsTyreAlloy) { result += `<i   title="TyreAlloy Insurance" class="fas fa-tire goodFont"></i>` }
    else { result += `<div  title="TyreAlloy Insurance" >&#x2219;</div>` }

    if (row.IsWheelGuard) { result += `<i   title="Wheel Guard" class="fas fa-circle goodFont"></i>` }
    else { result += `<div  title="Wheel Guard" >&#x2219;</div>` }

    if (row.IsServicePlan) { result += `<i   title="Service Plan" class="fas fa-car-mechanic goodFont"></i>` }
    else { result += `<div  title="Service Plan" >&#x2219;</div>` }

    if (row.IsWarranty) { result += `<i   title="Warranty" class="fas fa-file-certificate goodFont"></i>` }
    else { result += `<div  title="Warranty" >&#x2219;</div>` }


    result += `</div>`;

    return result;



  }
}




