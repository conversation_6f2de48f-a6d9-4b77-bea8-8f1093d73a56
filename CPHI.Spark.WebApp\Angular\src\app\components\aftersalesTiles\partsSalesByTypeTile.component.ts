import { Component, OnInit } from "@angular/core";
import { PartsChannelSplits } from "src/app/model/main.model";
import { PartsSummaryService } from "src/app/pages/partsSummary/partsSummary.service";
import { ConstantsService } from "src/app/services/constants.service";

@Component({
  selector: "partsSalesByTypeTile",
  template: `
    <div class="dashboard-tile-inner">
      <div class="dashboard-tile-header">
        {{ constants.translatedText.Dashboard_SalesByType }}
      </div>
      <div class="dashboard-tile-body">
        <table class="cph">
          <thead>
            <tr>
              <th></th>
              <th></th>
              <th>{{ constants.translatedText.Turnover}} / {{ constants.translatedText.Day }}</th>
              <th>{{ constants.translatedText.Turnover }}</th>
              <th *ngIf="constants.environment.partsSales_showMarginColPerc">
                {{ constants.translatedText.Margin }} %
              </th>
              <th *ngIf="constants.environment.partsSales_showMarginCol">
                {{ constants.translatedText.Margin }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of data; let i = index" [ngClass]="{ 'totalRow': row.IsTotal }">
              <td>
                <i *ngIf="!row.IsTotal" [ngClass]="icons[row.Channel]"></i>
              </td>
              <td>{{ row.Channel }}</td>
              <td>{{ row.TurnoverPerDay | cph:'currency':0 }}</td>
              <td>{{ row.Turnover | cph:'currency':0 }}</td>
              <td *ngIf="constants.environment.partsSales_showMarginColPerc">
                {{ row.MarginPercentage | cph:'percent':1 }}</td>
              <td *ngIf="constants.environment.partsSales_showMarginCol">
                {{ row.Margin | cph:'currency':0 }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
  `,
  styles: [
    `
      table { width: 90%; margin: 0 auto;  }
      table thead tr th { height: 2em !important; }
      table tbody tr.totalRow td { background-color: var(--grey80); font-weight: 700; }
    `,
  ],
})

export class PartsSalesByTypeTileComponent implements OnInit {
  data: PartsChannelSplits[];
  icons: { [key: string]: string; }

  constructor (
    public constants: ConstantsService,
    public service: PartsSummaryService
  ) { }
  
  ngOnInit(): void {
    this.icons = {
      Retail: 'fa fa-wrench',
      Internal: 'fa fa-car-wash',
      'Workshop Internal': 'fa fa-car-wash',
      'Workshop Retail': 'fa fa-tire',
      'Workshop Warranty': 'fa fa-engine-warning',
      'Transfers': 'fa fa-car-wash',
      'Workshop': 'fa fa-tire',
      'Warranty': 'fa fa-engine-warning'
    }

    this.data = this.service.partsChannelSplits;
  }
}
