import { Component, OnInit, ViewChild } from '@angular/core';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { TeleStatsService } from './teleStats.service';
import { ConstantsService } from 'src/app/services/constants.service';

@Component({
  selector: 'teleStats',
  templateUrl: './teleStats.component.html',
  styleUrls: ['./teleStats.component.scss']
})


export class TeleStatsComponent implements OnInit {

  @ViewChild('weekSelectorDropdown') weekSelectorDropdown!: NgbDropdown;
  
  viewPortHeight: number;
  gridHeight: number;
  
  constructor(
    public service: TeleStatsService,
    public constants: ConstantsService,
    public apiAccess: ApiAccessService,
    public selections: SelectionsService
  ) {



  }

  weeks: Date[] = [];

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading + '...' });

    if(!this.service.selectedWeek){
      this.service.initParams();
    }

    

    this.initializeWeeks();

    this.service.getData();

  }

  refresh(){

  }

  selectWeek(week: Date) {
    this.service.selectedWeek = week;
    this.service.getData();

    this.weekSelectorDropdown.close();

  }


  ngOnDestroy() {

  }

  onResize(event) {

  }

  initializeWeeks() {

    this.weeks = [];

    for (let i = 0; i < 10; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (i * 7 + date.getDay() - 1));
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      this.weeks.push(date);
    }
  }

}
