﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Model.Services
{
   public static class ConstantMethodsService
   {

      public static bool IsWeekDay(DateTime date)
      {
         DayOfWeek day = date.DayOfWeek;
         return day != DayOfWeek.Saturday && day != DayOfWeek.Sunday;
      }

      public static bool IsWeekend(DateTime date)
      {
         DayOfWeek day = date.DayOfWeek;
         return day == DayOfWeek.Saturday || day == DayOfWeek.Sunday;
      }



      public static void GetFirstRegisteredDateAndMileage(DateTime? firstRegisteredDate, DateTime dateOnForecourt, int? odometerReading, out DateTime adFirstRegdDate, out int mileageToUse)
      {
         adFirstRegdDate = firstRegisteredDate != null ? (DateTime)firstRegisteredDate : DateTime.Now;
         double vehicleAgeInMonths = (dateOnForecourt - adFirstRegdDate).Days / (365.25 / 12);
         mileageToUse = odometerReading ?? (int)Math.Round((vehicleAgeInMonths * 1000));
      }


      public static CompetitorSummary BuildCompetitorSummary(AdvertDetailsForCompetitorList adDetails, int suppliedPrice, int valuation, decimal geoY, decimal geoX, VehicleListing competitors, int radius, int retailerId, string retailerType,string postcode   )
      {
         CompetitorSummary competitorSummary = new CompetitorSummary(
            competitors,
            geoY,
            geoX,
               suppliedPrice,
               valuation,
               radius,
               null,
               true,
               postcode
            );
            
            

         if (adDetails.IsTradePricing.HasValue && adDetails.IsTradePricing.Value == true && adDetails.TradeMarginPercentage > 0)
         {
            adDetails.AdvertisedPrice = (int) (adDetails.AdvertisedPrice / (adDetails.TradeMarginPercentage ?? 1));
            adDetails.AdvertisedPrice += adDetails.TradeMarginAmount ?? 0;
         }

         var ourVehicle = new CompetitorVehicle(adDetails, competitorSummary.PricePosition, retailerId, retailerType);

         competitorSummary.CompetitorVehicles.Add(ourVehicle);

         return competitorSummary;
      }

    

      public static string ToTitleCase(string input)
      {
         if (string.IsNullOrWhiteSpace(input))
            return input;

         return string.Join(" ", input
             .ToLower()
             .Split(' ')
             .Select(word => char.ToUpper(word[0]) + word.Substring(1)));
      }

      public static int IntFromString(string factorItemValue)
      {
         return (int)Math.Round(Double.Parse(factorItemValue));
      }

      public static object GetPropertyValue(object obj, string propertyName)
      {
         if (obj == null || string.IsNullOrEmpty(propertyName))
         {
            return null;
         }

         // Get the type of the object
         Type type = obj.GetType();

         // Get the PropertyInfo for the specified property
         PropertyInfo propertyInfo = type.GetProperty(propertyName);

         if (propertyInfo != null)
         {
            // Get the value of the property
            return propertyInfo.GetValue(obj);
         }

         return null;
      }

      public static (int, int) ParseRange(string range)
      {
         if (string.IsNullOrEmpty(range))
         {
            throw new ArgumentException("The range string cannot be null or empty.", nameof(range));
         }

         // Split the string using the '-' character
         string[] parts = range.Split('-');

         if (parts.Length != 2)
         {
            throw new FormatException($"The range string given of {range} is not in the correct format.");
         }

         // Parse the parts into decimal values
         if (int.TryParse(parts[0], out int minValue) && int.TryParse(parts[1], out int maxValue))
         {
            return (minValue, maxValue);
         }
         else
         {
            throw new FormatException($"The range string given of {range} contains non-numeric values.");

         }
      }
   }
}
