//core angular
import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { ColDef, ColGroupDef, DomLayoutType } from 'ag-grid-community';
import { localeEs } from 'src/environments/locale.es.js';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
//model and cell renderers
import { FAndISiteRow, SiteVM } from '../../model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from '../../services/agGridMethods.service';
import { AutotraderService } from '../../services/autotrader.service';
//services
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';

//Angular things, non-standard
import { FAndISummaryService } from './fAndISummary.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { ColDefCph } from 'src/app/model/ColDefCPH';


type ColumnDef = ColDef | ColGroupDef;

@Component({
  selector: 'fAndITableSites',
  template: `
    <div id='gridHolder'>

    <!-- [domLayout]="domLayout"  -->
    <ag-grid-angular 
      id="FinanceAddOnsTable"
       class="ag-theme-balham" [gridOptions]="mainTableGridOptions"
      [pinnedBottomRowData]="totalRow"
      [rowData]="rowData"  [frameworkComponents]="frameworkComponents"  
      (gridReady)="onGridReady($event)" [getRowNodeId]="getRowNodeId" [animateRows]="false"
      > 
      
    </ag-grid-angular>
    <div  id="excelExport" (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>
  </div>
  `,
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
  styles: [
    `
      ag-grid-angular {
        max-width: 3000px;
      }

`
  ]
})



export class FandITableSitesComponent implements OnInit {

  @Input() public isSitesTable: boolean;
  @Input() public rowData: Array<FAndISiteRow>;
  @Input() public totalRow: Array<FAndISiteRow>;
  @Output() clickedSite = new EventEmitter<SiteVM>();
  

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  //main declarations
  //for agGrid
  showGrid = false;
  public gridApi;
  public importGridApi;
  public gridColumnApi;
  public getRowNodeId;

  amAlreadyUpdatingGrid: boolean = false;
  mainTableGridOptions: GridOptionsCph


  gridApiColumnDefinitions: any;
  // gridFaded: boolean;

  pinnedBottomRowData: Array<any>;
  flipCols: Array<string> = [];
  currentRowHeight: number;
  domLayout: DomLayoutType = 'autoHeight';
  frameworkComponents: { agColumnHeader: any; };



  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: FAndISummaryService
  ) {



  }

  ngOnDestroy() {

  }

  ngOnInit() {
    this.initParams();
  }

  initParams() {
    this.mainTableGridOptions = this.provideGridOptions();
  }

  

  provideGridOptions() {
    return {
      getMainMenuItems: (params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlightsSites),
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      domLayout: this.domLayout,
      getLocaleText: (params) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      onCellMouseOver: (params) => {
        //this.onCellMouseOver(params);
      },
      onCellClicked: (params) => {
        this.onCellClick(params);
      },

      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.isSitesTable ? this.service.topBottomHighlightsSites : [])
      },
      columnDefs: this.getColDefs(),
      getRowClass: (params) => {
        if (params.data.SiteId == 0) {
          return 'total';
        }
      }

    }
  }

  getColDefs()
  {
    if(this.constants.environment.fAndISummary_includeTargets)
    {
      return this.getColDefsVindis();
    }
    else
    {
      return this.getColDefsStandard();
    }
  }

 getColDefsStandard() {

  let colDefs: ColumnDef[] = 
  [
    { headerName: '', field: 'siteName', colId: 'label', width: 400, type: 'label', },
    { headerName: this.constants.translatedText.FinanceAddons_Deals, field: 'Deals', colId: 'dealCount', width: 106, type: 'number', },
    {
      headerName: this.constants.translatedText.Common_Finance, children: [
        { headerName: this.constants.translatedText.FinanceAddons_Units, field: 'UnitsOnFinance', colId: 'financeCount', width: 106, type: 'number', },
        { headerName: 'Pen.', field: 'PercentOfUnitsOnFinance', colId: 'financePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Common_PerUnit, field: 'FinancePerUnit', colId: 'financePU', width: 106, type: 'currencyWithFontColour', },
      ]
    },
    {
      headerName: this.constants.translatedText.Common_AddOns, children: [
        { headerName: this.constants.translatedText.DealDetails_Cosmetic, field: 'PercentOfDealsWithCosmeticInsurance', colId: 'cosmeticPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.FinanceAddons_PaintProtect, field: 'PercentOfDealsWithPaintProtection', colId: 'paintProtectionPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Common_Gap, field: 'PercentOfDealsWithGap', colId: 'gapInsurancePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Common_Tyre, field: 'PercentOfDealsWithTyre', colId: 'tyrePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Common_Alloy, field: 'PercentOfDealsWithAlloy', colId: 'alloyPen', width: 106, type: 'percent', hide: this.constants.environment.fAndISummary.hideAlloyColumn },
        { headerName: this.constants.translatedText.FinanceAddons_TyreAlloy, field: 'PercentOfDealsWithTyreAndAlloy', colId: 'tyreAlloyPen', width: 106, type: 'percent', },
        { headerName: 'WheelGuard', field: 'PercentOfDealsWithWheelGard', colId: 'wheelGuardPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.DealDetails_ServicePlan, field: 'PercentOfDealsWithServicePlan', colId: 'servicePlanPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Common_Warranty, field: 'PercentOfDealsWithWarranty', colId: 'warrantyPen', width: 106, type: 'percent', },
        { headerName: 'PPU', field: 'ProductsPerUnit', colId: 'productsPU', width: 106, type: 'number1dp', },
        { headerName: 'PPU', field: 'AddonProfitPerUnit', colId: 'AddOnProfitPU', width: 106, type: 'currencyWithFontColour', },
      ]
    },
    {
      headerName: this.constants.translatedText.FinanceAddons_TotalFinanceAndAddon, children: [
        { headerName: this.constants.translatedText.Common_PerUnit, field: 'FinanceAndAddonProfitPerUnit', colId: 'AddonProfitPerUnit', width: 106, type: 'currencyWithFontColour', },
        { headerName: this.constants.translatedText.Common_Total, field: 'TotalFinanceAndAddonProfit', colId: 'totalProfit', width: 106, type: 'currencyWithFontColour', },
      ]
    },
    {
      headerName: this.constants.translatedText.Common_TotalProfit, children: [
        { headerName: this.constants.translatedText.Common_PerUnit, field: 'TotalNLProfitPerUnit', colId: 'totalNLProfitPU', width: 140, type: 'currencyWithFontColour', },
      ]
    },

  ]

  return colDefs;
 }

 getColDefsVindis() {

  let colDefs: ColumnDef[] = 
  [
    { headerName: '', field: 'siteName', colId: 'label', width: 400, type: 'label', },
    { headerName: this.constants.translatedText.FinanceAddons_Deals, field: 'Deals', colId: 'dealCount', width: 106, type: 'number', },
    {
      headerName: this.constants.translatedText.Common_Finance, children: [
        { headerName: this.constants.translatedText.FinanceAddons_Units, field: 'UnitsOnFinance', colId: 'financeCount', width: 106, type: 'number', },
        { headerName: 'Pen.', field: 'PercentOfUnitsOnFinance', colId: 'financePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Common_PerUnit, field: 'FinancePerUnit', colId: 'financePU', width: 106, type: 'currencyWithFontColour', },
      ]
    },
    {
      headerName: this.constants.translatedText.Common_AddOns, children: [
        { headerName: this.constants.translatedText.DealDetails_Cosmetic, field: 'PercentOfDealsWithCosmeticInsurance', colId: 'cosmeticPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.FinanceAddons_PaintProtect, field: 'PercentOfDealsWithPaintProtection', colId: 'paintProtectionPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Common_Gap, field: 'PercentOfDealsWithGap', colId: 'gapInsurancePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.DealDetails_ServicePlan, field: 'PercentOfDealsWithServicePlan', colId: 'servicePlanPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Common_Warranty, field: 'PercentOfDealsWithWarranty', colId: 'warrantyPen', width: 106, type: 'percent', },
        { headerName: 'PPU', field: 'ProductsPerUnit', colId: 'productsPU', width: 106, type: 'number1dp', },
        { headerName: 'PPU', field: 'AddonProfitPerUnit', colId: 'AddOnProfitPU', width: 106, type: 'currencyWithFontColour', },
      ]
    },
    {
      headerName: 'F&I Target', children: [
        { headerName: this.constants.translatedText.Common_PerUnit, field: 'FinanceAndAddonProfitPerUnitTarget', colId: 'FinanceAndAddonProfitPerUnitTarget', width: 106, type: 'currencyWithFontColour', },
        { headerName: this.constants.translatedText.Common_Total, field: 'TotalFinanceAndAddonProfitTarget', colId: 'totalProfit', width: 106, type: 'currencyWithFontColour', },
      ]
    },
    {
      headerName: 'F&I Actual', children: [
        { headerName: this.constants.translatedText.Common_PerUnit, field: 'FinanceAndAddonProfitPerUnit', colId: 'AddonProfitPerUnit', width: 106, type: 'currencyWithFontColour', },
        { headerName: this.constants.translatedText.Common_Total, field: 'TotalFinanceAndAddonProfit', colId: 'totalProfit', width: 106, type: 'currencyWithFontColour', },
      ]
    },
    {
      headerName: 'F&I Vs', children: [
        { headerName: this.constants.translatedText.Common_PerUnit, field: 'FinanceAndAddonProfitPerUnitVsTarget', colId: 'AddonProfitPerUnit', width: 106, type: 'currencyWithFontColour', },
        { headerName: this.constants.translatedText.Common_Total, field: 'TotalFinanceAndAddonProfitVsTarget', colId: 'totalProfit', width: 106, type: 'currencyWithFontColour', },
      ]
    },
    {
      headerName: this.constants.translatedText.Common_TotalProfit, children: [
        { headerName: this.constants.translatedText.Common_PerUnit, field: 'TotalNLProfitPerUnit', colId: 'totalNLProfitPU', width: 106, type: 'currencyWithFontColour', },
      ]
    },

  ]

  return colDefs;
 }

  onCellClick(params) {

    // No deals - don't show modal
    if (params.data.Deals < 1) { return; }

    this.clickedSite.next(params.node.data);
    this.gridApi.resetRowHeights();
  }


  onGridReady(params): void {
    //this.gridHelpers.topBottomHighlights = [];
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);
    this.gridApi.setRowData(this.rowData);
    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();
  }

  private resizeGrid(): void { if (this.gridApi) this.gridApi.sizeColumnsToFit(); }

  clearHighlighting(colDef: any) { this.gridHelpersService.clearHighlighting(colDef, this.gridApi) }

  removeHighlighting() {
    this.mainTableGridOptions.columnDefs.forEach(colDef => {
      this.clearHighlighting(colDef);
    })
  }

  excelExport(): void {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Finance and AddOns', 1, 1);
  }

}
