<div class="tile-inner">
    <div class="tile-header">Vehicle Details</div>
    <div class="tile-body">
        <table>
            <tr>
                <td>
                    
                    <!-- <img *ngIf="data.ImageUrl !== ''" id="vehicleImage" [src]="data.ImageUrl" alt="Vehicle image"
                        width="100%" (error)="updateUrl()"> -->

                    <div class="image-container">
                        <autotraderImage
                        [src]="data.ImageUrl"
                        [widthpx]="300"
                        [scale]="0.9"
                        [alt]="'Vehicle Image'"
                        (error)="updateUrl()"
                        ></autotraderImage>
                    </div>


                </td>
                <td class="align-top">
                    <table>
                        <tbody>
                            <tr>
                                <td colspan="2">
                                    <div class="d-flex align-items-center">
                                        <h3 [ngbPopover]="popContent" placement="top" popoverClass="vehiclePopUpImage"
                                            container="body" triggers="mouseenter:mouseleave" id="vehiclePrice">{{
                                            data.AdvertisedPrice | cph:'currency':0 }}</h3>
                                        <div class="priceIndicatorLozenge"
                                            [ngClass]="data.PriceIndicatorRatingAtCurrentSelling">
                                            {{ data.PriceIndicatorRatingAtCurrentSelling | titlecase }}
                                        </div>
                                        <span>&nbsp;({{data.AdvertisedPrice /
                                            data.RelevantValuation|cph:'percent1dp':1}})</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <h5 id="vehicleMakeModel">{{ data.Make }} {{ data.Model }}</h5>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <span id="vehicleDerivative">{{ data.Derivative }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td id="keyDetailsCell" colspan="2">
                                    {{ data.FirstRegisteredDate | cph:'year':0 }} | {{ data.VehicleType }} | {{
                                    data.OdometerReading |
                                    cph:'number':0 }} miles | {{ data.EngineCapacityCC }}cc | {{ data.TransmissionType
                                    }} | {{
                                    data.FuelType }}
                                </td>
                            </tr>
                            <tr>
                                <td>{{ data.ImagesCount }} images</td>
                                <td>Video: {{ data.HasVideo ? 'Yes' : 'No' }}</td>
                            </tr>
                            <tr>
                                <td colspan="2">{{ data.AdSiteName }}</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <br>
            <tr>
                <td colspan="2">
                    AutoTrader Advert Status: {{ data.AutotraderAdvertStatus }}
                    <a *ngIf="data.AutotraderAdvertStatus === 'PUBLISHED'" [href]="buildAdUrl()" target="_blank">
                        (Go to advert)
                    </a>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    Advertiser Advert Status: {{ data.AdvertiserAdvertStatus }}
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    Lifecycle Status: {{ data.LifecycleStatus }}
                </td>
            </tr>
            <br>
            <tr>
                <td>Reg</td>
                <td>
                    <div class="d-flex justify-content-start align-items-center">
                    <div title="#{{data.AdId}}" class="regPlate">
                        {{data.VehicleReg|cph:'numberPlate':0}}
                    </div>
                    <div id="copyReg" title="Copy Reg" (click)="copyReg()">
                        <i class="fas fa-copy"></i>
                    </div>
                </div>
                </td>
            </tr>
            <tr>
                <td>Attention Grabber</td>
                <td class="bold">{{ data.AttentionGrabber }}</td>
            </tr>
            <tr>
                <td>Colour</td>
                <td >{{ data.SpecificColour }} ({{data.Colour}})</td>
            </tr>
            <tr>
                <td>Age and Owners</td>
                <td >{{ ageAndOwnersString  }} </td>
            </tr>
            <tr>
                <td class="align-top">Description</td>
                <td>
                    <span class="clickableNoBold" (click)="launchDescriptionModal()" [title]="description">{{
                        data.Description | slice:0:150 }}...</span>
                </td>
            </tr>
            <tr>
                <td>Retail Rating</td>
                <td>
                    <autoTraderRetailRating [params]="getRetailRatingParams(data)"></autoTraderRetailRating>
                </td>
            </tr>
            <tr>
                <td>Live Mkt Supply</td>
                <td [ngbPopover]="MarketSupplyPopupText" placement="top" popoverClass="infoPopover" container="body"
                    [ngClass]="{ 'text-danger': data.RetailSupply > 0, 'text-success': data.RetailSupply < 0 }"
                    triggers="mouseenter:mouseleave" id="retailSupply">
                    <span>{{ data.RetailSupply | cph:'percent':0:true }}</span>
                    &nbsp;
                    <span *ngIf="data.RetailSupply > 0">{{ '(' + 'higher supply than normal' + ')' }}</span>
                    <span *ngIf="data.RetailSupply < 0">{{ '(' + 'lower supply than normal' + ')' }}</span>
                </td>
            </tr>
            <tr>
                <td>Live Mkt Demand</td>
                <td [ngbPopover]="MarketDemandPopupText" placement="top" popoverClass="infoPopover" container="body"
                    [ngClass]="{ 'text-success': data.RetailDemand > 0, 'text-danger': data.RetailDemand < 0 }"
                    triggers="mouseenter:mouseleave" id="retailDemand">
                    <span>{{ data.RetailDemand | cph:'percent':0:true }}</span>
                    &nbsp;
                    <span *ngIf="data.RetailDemand > 0">{{ '(' + 'higher demand than normal' + ')' }}</span>
                    <span *ngIf="data.RetailDemand < 0">{{ '(' + 'lower demand than normal' + ')' }}</span>
                </td>
            </tr>
            <tr>
                <td>Live Mkt Condition</td>
                <td [ngbPopover]="popContentMarketCondition" placement="top" popoverClass="infoPopover" container="body"
                    [ngClass]="{ 'text-success': data.RetailMarketCondition > 0, 'text-danger': data.RetailMarketCondition < 0 }"
                    triggers="mouseenter:mouseleave" id="retailMktCondition">
                    <span>{{ data.RetailMarketCondition | cph:'percent':0:true }}</span>
                    &nbsp;
                    <span *ngIf="data.RetailMarketCondition > 0">{{ '(' + 'higher than normal' + ')' }}</span>
                    <span *ngIf="data.RetailMarketCondition < 0">{{ '(' + 'lower than normal' + ')' }}</span>
                </td>
            </tr>
            <tr *ngIf="!constantsService.environment.isSingleSiteGroup && bestMoveDetails">
                <td>Site transfer</td>
                <td>
                    Moving vehicle to {{ bestMoveDetails.location }} would increase strategy price by
                    {{ bestMoveDetails.priceChange | cph:'currency':0 }} and reduce days to sell by {{ bestMoveDetails.daysToSellChange }}
                    <br>
                    <button class="btn btn-success" (click)="goToSiteTransfer()">Go to site transfer</button>
                </td>
            </tr>
            <tr>
                <td *ngIf="data.VehicleAdvertPortalOptions" colspan="2">
                    Spec:
                    <ul>
                        <li *ngFor="let item of data.VehicleAdvertPortalOptions">
                            {{ item }}
                        </li>
                    </ul>
                </td>
                <td *ngIf="!data.VehicleAdvertPortalOptions">Spec not selected</td>
            </tr>
            <tr>
                <td colspan="2">
                    <autoTraderPerformanceRating [score]="service.modalItem?.AdvertDetail.PerfRatingScore"
                    [rating]="service.modalItem?.AdvertDetail.PerfRating"></autoTraderPerformanceRating>
                </td>
            </tr>
        </table>
    </div>
</div>



<ng-template class="popover" #popContent>
    <table id="popTable">
        <tbody>
            <tr>
                <td>Supplied Price</td>
                <td>{{data.SuppliedPrice|cph:'currency':0}}</td>
            </tr>
            <tr>
                <td>Admin Fee</td>
                <td>{{data.AdminFee|cph:'currency':0}}</td>
            </tr>
        </tbody>
    </table>

</ng-template>


<ng-template class="infoPopover" #popContentMarketCondition>
    <span>
        {{NationalRetailMktConditionPopupTextPart1}}
        <br /><br />
        {{NationalRetailMktConditionPopupTextPart2}}
    </span>
</ng-template>