import { Component, ElementRef, <PERSON>Init, ViewChild } from "@angular/core";
import { UntypedFormControl } from "@angular/forms";
import { ColDef, ColGroupDef, ColumnApi, GridApi, GridOptions } from "ag-grid-community";
import { Subscription } from "rxjs";

import { RegPlateComponent } from "src/app/_cellRenderers/regPlate.component";
import { OnRRGSiteComponent } from "src/app/_cellRenderers/stockDetailsIcon.component";
import { StockTakePhotoComponent } from "src/app/_cellRenderers/stockTakePhoto.component";
import { StockItemModalComponent } from "src/app/components/stockItemModal/stockItemModal.component";
import { TranslatedText } from "src/app/model/translations.model";
import { localeEs } from "src/environments/locale.es.js";
import { SiteVM } from "../../model/main.model";

import { StockListParams } from "./stockList.model";
import { StockListRow } from "./StockListRow";
import { StockListService } from "./stockList.service";
import { FleetOrderbookField } from "src/app/model/FleetOrderbookField";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { OldCustomHeader } from "src/app/components/oldCustomHeader/oldCustomHeader.component";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { BuildTotalAndAverageRowsParams } from "src/app/model/BuildTotalAndAverageRowsParams";

@Component({
   selector: "app-stockList",
   templateUrl: "./stockList.component.html",
   styleUrls: ["./stockList.component.scss", "../../../styles/components/_agGrid.scss"],
})
export class StockListComponent implements OnInit {
   @ViewChild("saveColumnDefinitionsModal", { static: true }) saveColumnDefinitionsModal: ElementRef;
   @ViewChild("reportNameModal", { static: true }) reportNameModal: ElementRef;
   @ViewChild("openColumnDefinitionsModal", { static: true }) openColumnDefinitionsModal: ElementRef;

   public gridApi: GridApi;
   public gridColumnApi: ColumnApi;

   mySubscription: Subscription;

   filter = new UntypedFormControl("");

   //filterState: any;
   mainTableGridOptions: GridOptions;
   //frameworkComponents: FrameworkComponents;
   //columnSets: ColumnSets;

   public components: {
      [p: string]: any;
   } = {
      agColumnHeader: OldCustomHeader,
   };

   constructor(
      public service: StockListService,
      public columnTypeService: ColumnTypesService,
      public customHeader: OldCustomHeader,
      public gridHelpersService: AGGridMethodsService
   ) {
      // this.frameworkComponents = {
      //   agColumnHeader: CustomHeaderComponent
      // };
   }

   ngOnInit() {
      this.initParams();
      this.getData();
   }

   initParams() {
      if (!this.service.stockList) {
         this.service.initiateStockList();
      }

      this.mySubscription = this.filter.valueChanges.subscribe((searchTerm) => {
         this.service.waitUntil = new Date(new Date().getTime() + 600);
         setTimeout(() => {
            if (new Date().getTime() > this.service.waitUntil.getTime()) {
               //we waited, and didn't get any more keyboard input, so let's search
               this.search(searchTerm);
               this.recalcDisplayedVehiclesCounter();
            }
         }, 620);
      });
   }

   ngOnDestroy() {
      if (this.service.stockList) {
         this.service.stockList.showGrid = false;
         this.service.stockList.storedGridState.cols = this.gridColumnApi.getColumnState();
         //this.service.stockList.storedGridState.filters = this.filterState
         this.service.stockList.storedGridState.sorts = this.gridApi.getModel();
         this.service.stockList.storedGridState.siteIds = this.service.stockList.sitesIds;
         if (!!this.mySubscription) {
            this.mySubscription.unsubscribe();
         }
      }
   }

   getData(): void {
      this.service.selections.triggerSpinner.emit({
         show: true,
         message: this.service.constants.translatedText.Loading,
      });

      let params: StockListParams = {
         SiteIds: this.service.selections.selectedSites
            ? this.service.selections.selectedSites.map((x) => x.SiteId)
            : this.service.selections.userSites.map((x) => x.SiteId),
         VehicleTypeTypes: this.service.stockList.storedGridState.highLevelFilters.vehicleTypes,
         Franchises: this.service.stockList.storedGridState.highLevelFilters.franchises,
         Models: this.service.stockList.storedGridState.highLevelFilters.models,
         Columns: this.service.constants.environment.stockList_tableColumns,
      };

      this.service.dataMethods.getStockListRows(params).subscribe((res: string[]) => {
         let dateCols: string[] = [
            "RegDate",
            "StockDate",
            "ReservedDate",
            "LastUpdatedDate",
            "WebSiteCreatedDate",
            "StockcheckScanDate",
         ];
         let boolCols: string[] = ["ShouldBePrepped", "IsPrepped", "IsOnWebsite", "SeenAtLatestStkchk"];

         let colIdsAndType: FleetOrderbookField[] = [];

         this.service.constants.environment.stockList_tableColumns.forEach((colId) => {
            colIdsAndType.push({
               FieldName: colId,
               FieldIsDate: dateCols.includes(colId),
               FieldIsBool: boolCols.includes(colId),
               FieldIsNumber: false,
               FieldIsNullable: false,
            });
         });

         let expandedResult: StockListRow[] = this.service.constants.expandTransmittedStrings(res, colIdsAndType);

         this.service.stockList.stocks = expandedResult;

         if (!!this.gridApi) this.gridApi.setRowData(this.service.stockList.stocks);
         this.mainTableGridOptions = this.provideGridOptions();
         this.service.stockList.showGrid = true;
         this.recalcDisplayedVehiclesCounter();
         this.service.selections.triggerSpinner.emit({ show: false });
      });
   }

   onGridReady(params): void {
      this.gridApi = params.api;
      this.gridColumnApi = params.columnApi;
      this.gridApi.sizeColumnsToFit();
      this.service.selections.triggerSpinner.emit({ show: false });
   }

   provideGridOptions(): GridOptions {
      return {
         onFirstDataRendered: () => this.onFirstDataRendered,
         onFilterChanged: () => this.onFilterChanged(),
         rowBuffer: 0,
         getLocaleText: (params) =>
            this.service.constants.currentLang == "es"
               ? localeEs[params.key] || params.defaultValue
               : params.defaultValue,
         rowData: this.service.stockList.stocks,
         getRowHeight: (params) => {
            //return 22;
            let normalHeight = Math.min(
               24,
               Math.max(21, Math.round((24 * this.service.selections.screenHeight) / 960))
            );
            if (params.node.rowPinned) {
               return this.gridHelpersService.getRowPinnedHeight();
            } else {
               return this.gridHelpersService.getStandardHeight();
            }
         },
         rowSelection: "multiple",
         context: { thisComponent: this },
         groupHeaderHeight: 30,
         getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
         pinnedBottomRowData: this.providePinnedBottomRowData(),
         onRowDoubleClicked: (params) => {
            this.loadStockItemModalOnRowClick(params.data);
         },
         onSelectionChanged: (params) => this.onRowSelectionChange(),
         onGridReady: (params) => this.onGridReady(params),
         defaultColDef: {
            filterParams: {
               newRowsAction: "keep",
               applyButton: false,
               clearButton: true,
               applyMiniFilterWhileTyping: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            //autoHeight: true,
            resizable: true,
            sortable: true,
            hide: false,
            floatingFilter: true,
         },
         headerHeight: this.gridHelpersService.getHeaderHeight(),
         floatingFiltersHeight: this.gridHelpersService.getRowHeight(30),
         columnTypes: {
            ...this.columnTypeService.provideColTypes([]),
         },
         columnDefs: this.getColumnDefs(),
      };
   }

   getColumnDefs(): (ColDef | ColGroupDef)[] {
      switch (this.service.constants.environment.customer) {
         case "RRGSpain":
            return this.provideSpainCols();
         case "RRGUK":
            return this.provideColsRRG();
         case "Vindis":
            return this.provideColsVindis();
         case "Jardine":
            return this.provideColsVindis();
         default:
            throw new Error("Unknown dealerGroup");
      }
   }

   onRowSelectionChange(): void {
      if (!this.gridApi) {
         return;
      }
      this.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
   }

   providePinnedBottomRowData(): any[] {
      const params: BuildTotalAndAverageRowsParams = {
         colsToSkipAverageIfZero: [],
         colsToTotalOrAverage: ["DaysInStock", "DaysAtBranch", "Selling", "PricedProfit", "CarryingValue"],
         colsToTotal: ["Selling", "PricedProfit", "CarryingValue"], // ['DIS','DIB','Selling','PricedProfit','CarryingValue']
         selectedCountFieldName: "StockNumberFull",
         labelFieldName: "SiteDescription",
         colsToSetToTrue: [],
         itemName: "stock",
         api: this.gridApi,
         includeTotalRow: false,
         showTotalInAverage: false,
      };

      return this.gridHelpersService.buildTotalAndAverageRows(params);
   }

   provideColsRRG(): (ColDef | ColGroupDef)[] {
      return [
         {
            headerName: this.service.constants.translatedText.Site,
            field: "SiteDescription",
            colId: "SiteDescription",
            filter: "agSetColumnFilter",
            type: "label",
            width: 80,
            pinned: "left",
         },
         {
            headerName: this.service.constants.translatedText.StockNumber,
            field: "StockNumberFull",
            colId: "StockNumberFull",
            type: "label",
            width: 80,
            pinned: "left",
         },
         {
            headerName: this.service.constants.translatedText.StockItemModal_Registration,
            field: "Reg",
            colId: "Reg",
            cellRendererParams: { fontSize: 1.1 },
            cellRenderer: RegPlateComponent,
            type: "special",
            width: 80,
            pinned: "left",
         },
         {
            headerName: "Progress Code",
            field: "ProgressCode",
            filter: "agSetColumnFilter",
            colId: "ProgressCode",
            type: "label",
            width: 120,
            pinned: "left",
         },
         {
            headerName: "DIS",
            field: "DaysInStock",
            colId: "DaysInStock",
            type: "number",
            width: 40,
            cellClass: "agAlignCentre",
            pinned: "left",
         },
         {
            headerName: "DIB",
            field: "DaysAtBranch",
            colId: "DaysAtBranch",
            type: "number",
            width: 40,
            cellClass: "agAlignCentre",
            pinned: "left",
         },
         // Vehicle stuff
         {
            headerName: "Vehicle",
            children: [
               {
                  headerName: "Make",
                  field: "Make",
                  colId: "Make",
                  filter: "agSetColumnFilter",
                  type: "label",
                  width: 120,
               },
               {
                  headerName: "Model",
                  field: "Model",
                  colId: "Model",
                  filter: "agSetColumnFilter",
                  type: "label",
                  width: 120,
               },
               { headerName: "Model Year", field: "ModelYear", colId: "ModelYear", type: "label", width: 80 },
               { headerName: "Description", field: "Description", colId: "Description", type: "label", width: 200 },
               {
                  headerName: "Vehicle Type",
                  field: "VehicleType",
                  filter: "agSetColumnFilter",
                  colId: "VehicleType",
                  width: 120,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "V. Code",
                  field: "VehicleTypeCode",
                  filter: "agSetColumnFilter",
                  colId: "VehicleTypeCode",
                  width: 60,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "V. SuperType",
                  field: "VehicleSuperType",
                  filter: "agSetColumnFilter",
                  colId: "VehicleSuperType",
                  width: 80,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Variant Class",
                  field: "VariantClass",
                  filter: "agSetColumnFilter",
                  colId: "VariantClass",
                  width: 80,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Colour",
                  field: "Colour",
                  filter: "agSetColumnFilter",
                  colId: "Colour",
                  width: 120,
                  type: "special",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Mileage",
                  field: "Mileage",
                  colId: "Mileage",
                  width: 60,
                  type: "number",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Fuel",
                  field: "Fuel",
                  filter: "agSetColumnFilter",
                  colId: "Fuel",
                  width: 40,
                  type: "special",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Doors",
                  field: "Doors",
                  colId: "Doors",
                  width: 40,
                  type: "number",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Transmission",
                  field: "Transmission",
                  filter: "agSetColumnFilter",
                  colId: "Transmission",
                  width: 40,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Options",
                  field: "Options",
                  colId: "Options",
                  width: 200,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Disposal Route",
                  field: "DisposalRoute",
                  filter: "agSetColumnFilter",
                  colId: "DisposalRoute",
                  type: "label",
                  width: 100,
                  columnGroupShow: "open",
               },
               {
                  headerName: "PU",
                  field: "PreviousUseCode",
                  filter: "agSetColumnFilter",
                  colId: "PreviousUseCode",
                  type: "label",
                  width: 40,
                  columnGroupShow: "open",
               },
               {
                  headerName: "Should Be Prepped",
                  field: "ShouldBePrepped",
                  colId: "ShouldBePrepped",
                  width: 80,
                  valueGetter: (params) => (params.data["ShouldBePrepped"] ? "ShouldBePrepped" : ""),
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "No Prep",
                  field: "IsPrepped",
                  colId: "IsPrepped",
                  width: 60,
                  valueGetter: (params) => (params.data["IsPrepped"] ? "" : "NoPrep"),
                  type: "label",
                  columnGroupShow: "open",
               },
            ],
         },
         // Cost stuff
         {
            headerName: "Cost",
            children: [
               {
                  headerName: "Purchased",
                  field: "Purchased",
                  colId: "Purchased",
                  width: 60,
                  type: "currency",
                  columnGroupShow: "open",
               },
               { headerName: "Selling", field: "Selling", colId: "Selling", width: 60, type: "currency" },
               {
                  headerName: "Option Costs",
                  field: "OptionCosts",
                  colId: "OptionCosts",
                  width: 60,
                  type: "currency",
                  columnGroupShow: "open",
               },
               {
                  headerName: "NR Costs",
                  field: "NonRecoverableCosts",
                  colId: "NonRecoverableCosts",
                  width: 60,
                  type: "currency",
                  columnGroupShow: "open",
               },
               {
                  headerName: "DFA",
                  field: "DealerFitAccessories",
                  colId: "DealerFitAccessories",
                  width: 60,
                  type: "currency",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Price Changes",
                  field: "PriceChanges",
                  colId: "PriceChanges",
                  type: "number",
                  width: 60,
                  columnGroupShow: "open",
               },
               {
                  headerName: "Priced Profit",
                  field: "PricedProfit",
                  colId: "PricedProfit",
                  width: 60,
                  type: "currency",
               },
               {
                  headerName: "Carrying Value",
                  field: "CarryingValue",
                  colId: "CarryingValue",
                  width: 80,
                  type: "currency",
               },
               {
                  headerName: "CAP Provn",
                  field: "CapProvision",
                  colId: "CapProvision",
                  width: 60,
                  type: "currency",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Cap Value",
                  field: "CapValue",
                  colId: "CapValue",
                  width: 60,
                  type: "currency",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Cap ID",
                  field: "CapID",
                  colId: "CapID",
                  width: 60,
                  type: "number",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Cap Notes",
                  field: "CapNotes",
                  colId: "CapNotes",
                  width: 100,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Cap Code",
                  field: "CapCode",
                  colId: "CapCode",
                  width: 100,
                  type: "label",
                  columnGroupShow: "open",
               },
               { headerName: "SIV", field: "Siv", colId: "Siv", width: 60, type: "currency", columnGroupShow: "open" },
               {
                  headerName: "VAT Qualifying?",
                  field: "IsVatQ",
                  cellClass: "agAlignCentre",
                  colId: "IsVatQ",
                  width: 60,
                  valueGetter: (params) => (params.data["IsVatQ"] ? "Q" : "N"),
                  columnGroupShow: "open",
               },
            ],
         },
         //stockcheck
         {
            headerName: "Stockcheck Photo",
            field: "SeenAtLatestStkchk",
            colId: "SeenAtLatestStkchk",
            cellRenderer: (params) => this.stockTakePhotoSimple(params),
            type: "special",
            width: 60,
         },
         {
            headerName: "Stockcheck Location",
            filter: "agSetColumnFilter",
            field: "StockcheckLocation",
            colId: "StockcheckLocation",
            type: "label",
            width: 100,
         },
         //on website
         {
            headerName: "Web",
            children: [
               //always show
               {
                  headerName: "On Web?",
                  field: "IsOnWebsite",
                  colId: "IsOnWebsite",
                  cellRenderer: OnRRGSiteComponent,
                  width: 70,
                  type: "special",
               },

               {
                  headerName: "Atten. Grabber",
                  filter: "agSetColumnFilter",
                  columnGroupShow: "open",
                  field: "AttentionGrabber",
                  colId: "AttentionGrabber",
                  width: 150,
                  type: "label",
               },
               {
                  headerName: "Created",
                  columnGroupShow: "open",
                  field: "WebSiteCreatedDate",
                  colId: "WebSiteCreatedDate",
                  width: 90,
                  type: "dateLongYear",
               },
               { headerName: "Price", field: "WebsitePrice", colId: "WebsitePrice", width: 90, type: "currency" },
               {
                  headerName: "P. Extra",
                  columnGroupShow: "open",
                  field: "PriceExtraLine",
                  colId: "PriceExtraLine",
                  width: 90,
                  type: "label",
               },
               { headerName: "Dol", field: "DaysOnLine", colId: "DaysOnLine", width: 70, type: "number" },
               {
                  headerName: "Images",
                  columnGroupShow: "open",
                  field: "ImagesCount",
                  colId: "ImagesCount",
                  width: 70,
                  type: "number",
               },
               {
                  headerName: "Videos",
                  columnGroupShow: "open",
                  field: "VideosCount",
                  colId: "VideosCount",
                  width: 70,
                  type: "number",
               },
            ],
         },
      ];
   }

   // RRG
   stockTakePhotoSimple(params: any) {
      var seenAtLatestStkchk: boolean = params.data.SeenAtLatestStkchk;

      if (seenAtLatestStkchk) {
         return `
          <i class="fas fa-camera"></i>
      `;
      } else {
         return `

      `;
      }
      //throw new Error('Method not implemented.');
   }

   provideColsVindis(): (ColDef | ColGroupDef)[] {
      return [
         {
            headerName: "Site",
            field: "SiteDescShort",
            colId: "SiteDescShort",
            type: "label",
            width: 80,
            pinned: "left",
         },
         {
            headerName: "Stock Number",
            field: "StockNumberFull",
            colId: "StockNumberFull",
            type: "label",
            width: 80,
            pinned: "left",
         },
         {
            headerName: "Registration",
            field: "Reg",
            colId: "Reg",
            cellRendererParams: { fontSize: 1.1 },
            cellRenderer: RegPlateComponent,
            type: "special",
            width: 80,
            pinned: "left",
         },
         {
            headerName: "Progress Code",
            field: "ProgressCode",
            colId: "ProgressCode",
            type: "label",
            width: 120,
            pinned: "left",
         },
         {
            headerName: "DIS",
            field: "DaysInStock",
            colId: "DaysInStock",
            type: "number",
            width: 40,
            cellClass: "agAlignCentre",
            pinned: "left",
         },
         // Vehicle stuff
         {
            headerName: "Vehicle",
            children: [
               { headerName: "Make", field: "Make", colId: "Make", type: "label", width: 120 },
               { headerName: "Model", field: "Model", colId: "Model", type: "label", width: 300 },
               { headerName: "Model Year", field: "ModelYear", colId: "ModelYear", type: "label", width: 80 },
               { headerName: "Description", field: "Description", colId: "Description", type: "label", width: 360 },
               {
                  headerName: "VehicleType",
                  field: "VehicleType",
                  colId: "VehicleType",
                  width: 120,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "V.Code",
                  field: "VehicleTypeCode",
                  colId: "VehicleTypeCode",
                  width: 60,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "V.SuperType",
                  field: "VehicleSuperType",
                  colId: "VehicleSuperType",
                  width: 80,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "VariantClass",
                  field: "VariantClass",
                  colId: "VariantClass",
                  width: 80,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Mileage",
                  field: "Mileage",
                  colId: "Mileage",
                  width: 60,
                  type: "number",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Fuel",
                  field: "Fuel",
                  colId: "Fuel",
                  width: 40,
                  type: "special",
                  columnGroupShow: "open",
               },
               {
                  headerName: "Disposal Route",
                  field: "DisposalRoute",
                  colId: "DisposalRoute",
                  type: "label",
                  width: 100,
                  columnGroupShow: "open",
               },
               {
                  headerName: "ShouldBePrepped",
                  field: "ShouldBePrepped",
                  colId: "ShouldBePrepped",
                  width: 80,
                  cellRenderer: (params) => (params.value ? "ShouldBePrepped" : ""),
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "NoPrep",
                  field: "IsPrepped",
                  colId: "IsPrepped",
                  width: 60,
                  cellRenderer: (params) => (params.value ? "" : "NoPrep"),
                  type: "label",
                  columnGroupShow: "open",
               },
            ],
         },
         // Cost stuff
         {
            headerName: "Cost",
            children: [
               { headerName: "Selling", field: "Selling", colId: "Selling", width: 60, type: "currency" },
               {
                  headerName: "PricedProfit",
                  field: "PricedProfit",
                  colId: "PricedProfit",
                  width: 60,
                  type: "currency",
               },
               {
                  headerName: "Carrying Value",
                  field: "CarryingValue",
                  colId: "CarryingValue",
                  width: 80,
                  type: "currency",
               },
               { headerName: "SIV", field: "Siv", colId: "Siv", width: 60, type: "currency", columnGroupShow: "open" },
               {
                  headerName: "CapCode",
                  field: "CapCode",
                  colId: "CapCode",
                  width: 100,
                  type: "label",
                  columnGroupShow: "open",
               },
               {
                  headerName: "VAT Qualifying?",
                  field: "IsVatQ",
                  cellClass: "agAlignCentre",
                  colId: "IsVatQ",
                  width: 60,
                  filter: "agSetColumnFilter",
                  cellRenderer: (params) => {
                     const result = params.data.IsVatQ === "True" ? "Y" : "N";
                     return result;
                  },
                  filterParams: {
                     values: ["Y", "N"],
                  },
                  valueGetter: (params) => {
                     const result = params.data.IsVatQ === "True" ? "Y" : "N";
                     return result;
                  },
               },
            ],
         },
         {
            headerName: "Stockcheck Location",
            field: "StockcheckLocation",
            colId: "StockcheckLocation",
            type: "label",
            width: 160,
         },
         {
            headerName: "Stockcheck Photo",
            field: "SeenAtLatestStkchk",
            colId: "SeenAtLatestStkchk",
            cellRenderer: StockTakePhotoComponent,
            type: "special",
            width: 80,
         },
      ];
   }

   provideSpainCols(): (ColDef | ColGroupDef)[] {
      let translation: TranslatedText = this.service.constants.translatedText;

      return [
         // Site things
         {
            headerName: translation.Region,
            field: "Zona",
            colId: "Zona",
            width: 30,
            pinned: "left",
            type: "label",
         },
         { headerName: "Site Code", field: "SiteCode", colId: "SiteCode", pinned: "left", type: "label", width: 20 },
         //{ headerName: 'Site Description', field: 'SiteDescription', colId: 'SiteDescription', pinned: 'left', type: 'label', width: 30 },
         {
            headerName: "Concesionario",
            field: "Concesionario",
            pinned: "left",
            colId: "Concesionario",
            width: 40,
            columnGroupShow: "open",
            type: "label",
         },

         // Vehicle id
         {
            headerName: translation.StockNumber,
            field: "StockNumber",
            colId: "StockNumber",
            pinned: "left",
            type: "label",
            width: 30,
         },
         {
            headerName: translation.StockItemModal_Registration,
            field: "Reg",
            colId: "Reg",
            pinned: "left",
            cellRendererParams: { fontSize: 1.1 },
            cellRenderer: RegPlateComponent,
            type: "special",
            width: 40,
         },
         {
            headerName: translation.StockItemModal_Chassis,
            field: "Chassis",
            colId: "Chassis",
            pinned: "left",
            type: "label",
            width: 60,
         },

         // Type
         { headerName: "V.Code", field: "VehicleTypeCode", colId: "VehicleTypeCode", width: 20, type: "label" },
         {
            headerName: translation.Description,
            field: "VehicleType",
            colId: "VehicleType",
            width: 30,
            type: "label",
         },

         // Vehicle details
         { headerName: translation.Make, field: "Make", colId: "Make", width: 30, type: "labelSetFilter" },
         { headerName: translation.Model, field: "Model", colId: "Model", width: 60, type: "label" },
         { headerName: "IdFamily", field: "IdFamily", colId: "IdFamily", width: 40, type: "label" },
         { headerName: "IdBrand", field: "IdBrand", colId: "IdBrand", width: 20, type: "label" },
         {
            headerName: translation.Description,
            field: "Description",
            colId: "Description",
            width: 80,
            type: "label",
         },
         { headerName: translation.ModelYear, field: "ModelYear", colId: "ModelYear", width: 40, type: "label" },

         {
            headerName: this.service.constants.translatedText.Detail,
            children: [
               {
                  headerName: translation.StockItemModal_Fuel,
                  field: "Fuel",
                  colId: "Fuel",
                  cellRenderer: (params) => this.addEnergyTypeIcon(params),
                  type: "label",
                  width: 30,
               },
               {
                  headerName: translation.Colour,
                  field: "Colour",
                  colId: "Colour",
                  width: 120,
                  columnGroupShow: "open",
                  type: "special",
               },
               {
                  headerName: translation.StockItemModal_Mileage,
                  field: "Mileage",
                  colId: "Mileage",
                  width: 60,
                  columnGroupShow: "open",
                  type: "number",
               },
               {
                  headerName: translation.StockItemModal_Doors,
                  field: "Doors",
                  colId: "Doors",
                  width: 40,
                  columnGroupShow: "open",
                  type: "number",
               },
               {
                  headerName: translation.StockItemModal_Transmission,
                  field: "Transmission",
                  colId: "Transmission",
                  width: 80,
                  columnGroupShow: "open",
                  type: "label",
               },
               { headerName: "C02", field: "C02", colId: "C02", width: 40, columnGroupShow: "open", type: "number" },
               {
                  headerName: translation.StockItemModal_DisposalRoute,
                  field: "DisposalRoute",
                  colId: "DisposalRoute",
                  columnGroupShow: "open",
                  width: 80,
                  type: "label",
               },
               {
                  headerName: "Ubicacion",
                  field: "Ubicacion",
                  colId: "Ubicacion",
                  width: 50,
                  columnGroupShow: "open",
                  type: "label",
               },
               //{ headerName: 'Physical Location', field: 'PhysicalLocation', colId: 'PhysicalLocation',columnGroupShow: 'open', type: 'label', width: 50 },
               {
                  headerName: "IdLocation",
                  field: "IdLocation",
                  colId: "IdLocation",
                  width: 50,
                  type: "label",
                  columnGroupShow: "open",
               },
            ],
         },

         // Vehicle dates
         {
            headerName: translation.DealDetails_RegisteredDate,
            field: "RegDate",
            colId: "RegDate",
            width: 40,
            type: "dateShortYear",
            hide: true,
         },
         {
            headerName: translation.DealDetails_StockDate,
            field: "StockDate",
            colId: "StockDate",
            width: 40,
            type: "dateShortYear",
         },

         {
            headerName: translation.StockList_DateFactoryTransportation,
            field: "DateFactoryTransportation",
            colId: "DateFactoryTransportation",
            width: 40,
            type: "dateShortYear",
         },
         {
            headerName: translation.StockList_DateVehicleRecondition,
            field: "DateVehicleRecondition",
            colId: "DateVehicleRecondition",
            width: 40,
            type: "dateShortYear",
         },
         {
            headerName: translation.StockList_DateSiteTransportation,
            field: "DateSiteTransportation",
            colId: "DateSiteTransportation",
            width: 40,
            type: "dateShortYear",
         },
         {
            headerName: translation.StockList_DateSiteArrival,
            field: "DateSiteArrival",
            colId: "DateSiteArrival",
            width: 40,
            type: "dateShortYear",
         },

         {
            headerName: "Reserved Date",
            field: "ReservedDate",
            colId: "ReservedDate",
            width: 40,
            type: "dateShortYear",
         },
         {
            headerName: translation.Orderbook_DaysInStockAbbreviation,
            field: "DaysInStock",
            colId: "DaysInStock",
            width: 20,
            type: "number",
         },
         {
            headerName: "Antiguedad Stock",
            field: "Antiguedad_Stock",
            colId: "Antiguedad_Stock",
            width: 40,
            columnGroupShow: "open",
            type: "label",
         },
         {
            headerName: translation.Dashboard_WipReport_Ageing,
            colId: "ageing",
            valueGetter: (params) => this.workOutAgeing(params.data),
            type: "number",
            hide: true,
         },
         {
            headerName: "Edad Vehiculo",
            field: "Edad_Vehiculo",
            colId: "Edad_Vehiculo",
            width: 40,
            columnGroupShow: "open",
            type: "label",
         },

         // Id
         {
            headerName: "IdDataSource",
            field: "IdDataSource",
            colId: "IdDataSource",
            width: 30,
            type: "label",
            columnGroupShow: "open",
         },
      ];
   }

   addEnergyTypeIcon(params: any): string {
      let icon: string;
      let icon2: string;

      if (params.value == "D") icon = "fas fa-gas-pump";
      if (params.value == "E") icon = "fas fa-charging-station electric-green";
      if (params.value == "G") icon = "fas fa-gas-pump petrol-green";
      if (params.value == "L") icon = "fas fa-burn blue";
      if (params.value == "H" || params.value == "I" || params.value == "GL") {
         icon = "fas fa-plug blue";
         icon2 = "fas fa-gas-pump petrol-green";
      }

      return `<span class="cell-with-icon">${params.value}<span><i class="${icon}"></i><i class="${icon2}"></i></span></span>`;
   }

   workOutAgeing(data: any): number {
      if (data) {
         switch (data.Antiguedad_Stock) {
            case "90 - 120":
            case "120 - 150":
               return 90;
            case "150 -180":
               return 150;
            case "180 - 365":
            case ">365":
               return 180;
         }
      }
   }

   search(text: string): StockListRow[] {
      if (!this.gridApi) return;
      this.gridApi.setQuickFilter(text);
   }

   recalcDisplayedVehiclesCounter(): void {
      setTimeout(() => {
         if (!this.gridApi) return 0;
         this.service.stockList.displayedRowsLength = this.gridApi.getDisplayedRowCount();
      }, 100);
   }

   // TEMPLATE FUNCTIONS
   onFilterChanged(): void {
      this.service.stockList.storedGridState.filters = this.gridApi.getFilterModel();
      this.recalcDisplayedVehiclesCounter();
      if (!this.gridApi) {
         return;
      }
      this.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
   }

   onFirstDataRendered(grid) {
      if (!!this.service.stockList.filterModel) grid.api.setFilterModel(this.service.stockList.filterModel);
   }

   public excelExportOnClick() {
      let tableModel: any = this.gridApi.getModel();

      tableModel = this.removeHiddenColumnHeaders(tableModel);

      //console.log(tableModel.columnModel.primaryColumnTree, "tableModel.columnModel.primaryColumnTree!");

      if (tableModel == null) {
         throw new Error("stockList - service.excelExport: Attempting to export null table model.");
      }
      this.service.excel.createSheetObject(tableModel, this.service.constants.translatedText.StockList_Title, 1.5);
   }

   // Currently createSheetObject produces headers even if data not expanded
   // This function primaryColumnTree depending on whether or not the parent is expanded
   private removeHiddenColumnHeaders(tableModel: any) {
      tableModel.columnModel.primaryColumnTree.forEach((colGroup) => {
         if (colGroup.expandable) {
            if (!colGroup.expanded) {
               colGroup.children.forEach((child) => {
                  if (child.colDef.columnGroupShow == "open") {
                     child.visible = false;
                  }
               });
            } else {
               colGroup.children.forEach((child) => {
                  child.visible = true;
               });
            }
         }
      });

      return tableModel;
   }

   onUpdateSitesOnClick(sites: SiteVM[]) {
      this.service.selections.selectedSites = sites;
      this.service.selections.selectedSitesIds = [];

      this.service.selections.selectedSites.forEach((site) => {
         this.service.selections.selectedSitesIds.push(site.SiteId);
      });

      this.getData();
   }

   public onUpdateVehicleTypesOnClick(vehicleTypes: string[]): void {
      this.service.stockList.storedGridState.highLevelFilters.vehicleTypes = vehicleTypes;
      this.getData();
   }

   public onUpdateFranchisesOnClick(franchises: string[]): void {
      this.service.stockList.storedGridState.highLevelFilters.franchises = franchises;
      this.getData();
   }

   public onUpdateModelsOnClick(models: string[]): void {
      this.service.stockList.storedGridState.highLevelFilters.models = models;
      this.getData();
   }

   loadStockItemModalOnRowClick(stock: StockListRow): void {
      if (!this.service.constants.environment.stockList_hideStockDetailsModal) {
         this.service.dataMethods.getStockListRowItem(stock.Id).subscribe((res: StockListRow[]) => {
            const modalRef = this.service.modalService.open(StockItemModalComponent, { keyboard: true, size: "lg" });
            modalRef.componentInstance.givenStockItem = res[0];
            modalRef.result.then((result) => {});
         });
      }
   }
}
