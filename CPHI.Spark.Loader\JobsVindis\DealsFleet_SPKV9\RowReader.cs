﻿using CPHI.Spark.Model;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Loader
{
    public partial class DealsFleetVindisJob : IJob
    {

        public class ProfitLookup
        {
            public string profitElement;
            public int[] profitColumns;
        }
        
        public class NameAndSite
        {
            public string name;
            public int siteId;
        }


        public int SiteConverter(string siteName)
        {
            //create a sites dictionary
            Dictionary<string, int> siteLookup = new Dictionary<string, int>()
                    {
                {"Bedford Audi", 1},
                {"Cambridge Audi", 2},
                {"Huntingdon Audi", 3},
                {"Northampton Audi", 4},
                {"Peterborough Audi", 5},
                {"DHL Peterborough", 5},
                {"Bedford Volkswagen", 6},
                {"Cambridge Volkswagen", 7},
                {"VW Fakenham", 8},
                {"Fakenham Volkswagen", 8},
                {"Huntingdon Volkswagen", 9},
                {"Huntingdon Van Centre", 10},
                {"Northampton Van Centre", 11},
                {"Northampton VWCV", 11 }, // Added 01/04/2025
                {"Skoda Bury", 12},
                {"Bury St Edmunds Skoda", 12},
                {"Cambridge Skoda", 13},
                {"Milton Keynes SEAT", 14},
                {"Milton Keynes CUPRA", 14},
                {"Ducati Cambridge", 15},
                {"Bentley Cambridge", 16},
                {"Three 10 Automotive", 16},
                {"Three 10 Automotive Bentley Cambridge", 16},
                {"AutoNow Bury", 17},
                {"AutoNow Cambridge", 18},
                {"AutoNow Fakenham", 19},
                {"AutoNow Sawston", 20},
                {"Fleet Alconbury", 22},
                {"Vindis Group", 22},
                {"Vindis Group Fleet", 22 }
            };

            return siteLookup[siteName];

        }


        public List<Person> ReadNames(List<List<string>> rows, List<string> headers)
        {
            List<NameAndSite> namesAndSites = new List<NameAndSite>();
            List<Person> newPeople = new List<Person>();

            int namesColumnIndex = headers.IndexOf("Salesperson");
            int siteColumnIndex = headers.IndexOf("Delivery site");

            foreach (var row in rows)
            {
                string name = row[namesColumnIndex];

                if (name == "") continue;

                if (namesAndSites.Select(x => x.name).Contains(name)) continue;

                string siteName = row[siteColumnIndex];
                int siteId = SiteConverter(siteName);

                NameAndSite nameAndSite = new NameAndSite();
                nameAndSite.name = name;
                nameAndSite.siteId = siteId;

                if (!dbPeople.Select(x => x.Name).Contains(name)) namesAndSites.Add(nameAndSite);
            }

            foreach (var nameAndSite in namesAndSites)
            {
                Person person = new Person();

                person.Id = 0;
                person.Name = nameAndSite.name;
                person.CurrentSite_Id = nameAndSite.siteId;
                person.SellsUsed = false;
                person.SellsNew = true;
                person.LastUpdateDate = DateTime.UtcNow;
                person.JobTitle = "Fleet Sales Exec";
                person.JobRole = "Fleet Sales Exec";
                person.IsRemoved = false;
                person.IsUpdated = false;
                person.HasLeft = false;
                person.HasSales = true;
                person.Sites = nameAndSite.siteId.ToString();
                person.DoNotRemove = false;
                person.AccessAllSites = false;
                person.Email = "ADDED BY FLEET DEALS LOADER";
                person.DealerGroup_Id = 3;
                person.CurrentRetailerSite_Id = nameAndSite.siteId;

                newPeople.Add(person);
            }

            return newPeople;
        }



        public List<Deal> ReadDealRows(List<List<string>> rows, List<string> headers, string fileNameIn)
        {

            List<Deal> incomingDeals = new List<Deal>();
            int incomingProcessCount = 0;

            //create a franchises dictionary
            Dictionary<string, string> franchiseLookup = new Dictionary<string, string>()
                    {
                        {"AUDI", "Audi"},
                        {"VOLKSWAGEN", "VW"},
                        {"SEAT", "SEAT"},
                        {"SKODA", "Skoda"},
                    };


            //create the lookup indices
            int deliverySiteIndex = headers.IndexOf("Delivery site"); //done
            int deliveryDateIndex = headers.IndexOf("Delivery date"); //done
            int taxRequestedFromIndex = headers.IndexOf("Tax requested from"); //ignore
            int customerSignedDateIndex = headers.IndexOf("Customer signed date"); //done
            int salespersonIndex = headers.IndexOf("Salesperson"); //done
            int orderNoIndex = headers.IndexOf("Order no."); //done
            int brandIndex = headers.IndexOf("Brand"); //done
            int saleTypeIndex = headers.IndexOf("Sale type"); //done
            int customerIndex = headers.IndexOf("Customer"); //done
            int invoiceContactIndex = headers.IndexOf("Invoice contact"); //done
            int endUserIndex = headers.IndexOf("End user"); //done
            int vehicleDescriptionIndex = headers.IndexOf("Vehicle description"); //done
            int regNoIndex = headers.IndexOf("Reg No."); //done
            int buildWeekIndex = headers.IndexOf("Build week"); //ignore
            int vehicleStatusIndex = headers.IndexOf("Vehicle status");
            int basicAndFactOptsIndex = headers.IndexOf("Basic & f. opts");
            int totalAdjustmentIndex = headers.IndexOf("Total Adjustment");
            int subtotalMarginIndex = headers.IndexOf("Subtotal margin");
            int tacticalIndex = headers.IndexOf("Tactical");
            int brokerCommissionIndex = headers.IndexOf("Broker commission");
            int financeIncomeIndex = headers.IndexOf("Finance income");
            int vbRebateIndex = headers.IndexOf("VB rebate");
            int dfaSaleIndex = headers.IndexOf("DFA sale");
            int dfaCostIndex = headers.IndexOf("DFA cost");
            int fuelCostIndex = headers.IndexOf("Fuel cost");
            int onwardDeliveryCostIndex = headers.IndexOf("Onward delivery cost");
            int pdiCostIndex = headers.IndexOf("PDI cost");
            int deliveryProfitIndex = headers.IndexOf("Delivery profit");
            int backendBonusIndex = headers.IndexOf("Backend bonus");
            int financeSubsidyIndex = headers.IndexOf("Finance subsidy");
            int basicPriceIndex = headers.IndexOf("Basic price");
            int tactical2Index = headers.IndexOf("Tactical 2");
            int tactical3Index = headers.IndexOf("Tactical 3");
            int valetCostIndex = headers.IndexOf("Valet cost");
            int backendBonus2Index = headers.IndexOf("Backend bonus 2");
            int backendBonus3Index = headers.IndexOf("Backend bonus 3");



            foreach (var row in rows)
            {
                incomingProcessCount++;
                try
                {
                    if (row.Count == 0 || row[orderNoIndex] == "") { continue; } //skip empties


                    if (row.Count != headers.Count)
                    {
                        //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                        logMessage.FailNotes = logMessage.FailNotes + $"{row[orderNoIndex]}: Skipped rowCol as had {row.Count} rowCols and needed {headers.Count}";
                        errorCount++;
                        continue;
                    }


                    //order date
                    string orderDateString = row[customerSignedDateIndex];
                    if (orderDateString == "") { continue; }

                    DateTime orderDate = DateTime.ParseExact(orderDateString, "dd/MM/yyyy HH:mm:ss", null).Date;

                    //delivery date
                    string deliveryDateString = row[deliveryDateIndex];
                    string invoiceDateString = row[taxRequestedFromIndex];
                    if (deliveryDateString == "") { continue; }

                    //if the tax date is blank, use the delivery date, else use the tax date
                    DateTime deliveryDate = invoiceDateString == "" ? DateTime.ParseExact(deliveryDateString, "dd/MM/yyyy HH:mm:ss", null).Date : DateTime.ParseExact(invoiceDateString, "dd/MM/yyyy HH:mm:ss", null).Date; ;

                    // orderDate should never be ahead of deliveryDate -
                    // Update orderDate to be deliveryDate if it is ahead of deliveryDate
                    if (orderDate > deliveryDate)
                    {
                        orderDate = deliveryDate;
                    }

                    //invoice date
                    DateTime invoiceDate;
                    if (invoiceDateString == "") { invoiceDate = deliveryDate; }
                    else { invoiceDate = DateTime.ParseExact(invoiceDateString, "dd/MM/yyyy HH:mm:ss", null).Date; }

                    //delivery site
                    int deliverySiteId = SiteConverter(row[deliverySiteIndex]);

                    //sales exec id
                    string salesExecString = row[salespersonIndex];
                    int? salesExecId = null;
                    try { salesExecId = dbPeople.Find(x => x.Name == salesExecString).Id; } catch { }

                    //vehicleType
                    int vehicleTypeId = vehicleTypes.First(x => x.Description == "New").Id;

                    //franchise
                    int defaultFranchiseId = standingValues.Find(x => x.Description == "Non-Franchise").Id;
                    string franchiseString = row[brandIndex];
                    int franchiseId = defaultFranchiseId;
                    try { franchiseId = standingValues.Find(x => x.Description == franchiseLookup[franchiseString]).Id; } catch { }

                    //orderNumber
                    string orderNumber = string.Empty;
                    try { orderNumber = row[orderNoIndex]; } catch { continue; };
                    
                    if (incomingDeals.Select(x=>x.EnquiryNumber).Contains(orderNumber)) { continue; }

                    //orderType
                    string orderTypeString = row[saleTypeIndex];
                    int orderTypeId;
                    try { orderTypeId = dbOrderTypes.Find(x => x.Code2 == orderTypeString).Id; } catch { continue; }

                    //site
                    int siteId = sites.Find(s => s.Description == "Group Fleet").Id;

                    //special rule for Tom Kelland
                    if (salesExecString == "Tom Kelland") { 
                        siteId = sites.Find(s => s.Description == "VW CV Northampton").Id;
                        orderTypeId = dbOrderTypes.Find(x => x.Code2 == "New Retail").Id;
                    }

                    //isDelivered
                    bool isDelivered = deliveryDate < DateTime.Today;

                    //sales and costs
                    decimal sale = decimal.Parse(row[basicAndFactOptsIndex]);
                    decimal totalAdjustment= decimal.Parse(row[totalAdjustmentIndex]);
                    decimal subtotalMargin = decimal.Parse(row[subtotalMarginIndex]);
                    decimal tactical = decimal.Parse(row[tacticalIndex]);
                    decimal brokerCommission = decimal.Parse(row[brokerCommissionIndex]);
                    decimal financeIncome = decimal.Parse(row[financeIncomeIndex]);
                    decimal vbRebate = decimal.Parse(row[vbRebateIndex]);
                    decimal dfaSale = decimal.Parse(row[dfaSaleIndex]);
                    decimal dfaCost = decimal.Parse(row[dfaCostIndex]);
                    decimal fuelCost = decimal.Parse(row[fuelCostIndex]);
                    decimal onwardDeliveryCost = decimal.Parse(row[onwardDeliveryCostIndex]);
                    decimal pdiCost = decimal.Parse(row[pdiCostIndex]);
                    decimal deliveryProfit = decimal.Parse(row[deliveryProfitIndex]);
                    decimal backendBonus = decimal.Parse(row[backendBonusIndex]);
                    decimal backendBonus2 = 0;
                    decimal backendBonus3 = 0;
                    decimal financeSubsidy = decimal.Parse(row[financeSubsidyIndex]);
                    decimal basicPrice = decimal.Parse(row[basicPriceIndex]);
                    decimal tactical2 = decimal.Parse(row[tactical2Index]);
                    decimal tactical3 = decimal.Parse(row[tactical3Index]);
                    decimal valetCost = decimal.Parse(row[valetCostIndex]);


                    //zero out some of the values for certain franchise-sale type combos
                    if (franchiseString == "AUDI" && orderTypeString == "New Agency" ||
                        franchiseString == "AUDI" && orderTypeString == "New LM Agency"
                        )
                    {
                        totalAdjustment = 0;
                        subtotalMargin = 0;
                        financeIncome = 0;
                        deliveryProfit = 0;
                        tactical2 = 0;
                        tactical3 = 0;
                    }

                    else if (franchiseString == "AUDI" && orderTypeString == "New VWFS Agency")
                    {
                        totalAdjustment = 0;
                        subtotalMargin = 0;
                        tactical = 0;
                    }

                    else if (franchiseString == "AUDI" && orderTypeString == "New National  Account")
                    {
                        totalAdjustment = 0;
                        subtotalMargin = 0;
                        tactical = 0;
                        brokerCommission = 0;
                        financeIncome = 0;
                        dfaSale = 0;
                        dfaCost = 0;
                        deliveryProfit = 0;
                        financeSubsidy = 0;
                        tactical2 = 0;
                        tactical3 = 0;
                    }

                    else if ((franchiseString == "SKODA" || franchiseString == "SEAT") && 
                        (orderTypeString == "New Agency" || orderTypeString == "New LM Agency" || orderTypeString == "New VWFS Agency" || orderTypeString == "New National  Account")
                        )
                    {
                        totalAdjustment = 0;
                        subtotalMargin = 0;
                        deliveryProfit = 0;
                        tactical2 = 0;
                        tactical3 = 0;
                    }

                    else if (franchiseString == "VOLKSWAGEN" &&
                                (orderTypeString == "New Agency" ||
                                    orderTypeString == "New VWFS Agency" ||
                                    orderTypeString == "New National  Account" ||
                                    orderTypeString == "New LM Agency" ||
                                    orderTypeString == "New RFO Fleet" ||
                                    orderTypeString == "New RFO Rental"
                                )
                            )
                    {
                        totalAdjustment = 0;
                        subtotalMargin = 0;
                        deliveryProfit = 0;
                    }

                    else if (franchiseString == "VOLKSWAGEN" && orderTypeString == "New Direct")
                    {
                        tactical = 0;
                        tactical2 = 0;
                        tactical3 = 0;

                        backendBonus2 = decimal.Parse(row[backendBonus2Index]);
                        backendBonus3 = decimal.Parse(row[backendBonus3Index]);
                    }




                    decimal totalProfit = totalAdjustment + subtotalMargin + tactical + brokerCommission + financeIncome + vbRebate + dfaSale + dfaCost + fuelCost + onwardDeliveryCost + pdiCost
                        + deliveryProfit + backendBonus + financeSubsidy + tactical2 + tactical3 + valetCost + backendBonus2 + backendBonus3;

                    string vehicleDescription = row[vehicleDescriptionIndex].Substring(0, Math.Min(row[vehicleDescriptionIndex].Length, 50));

                    string financeCo = row[invoiceContactIndex].Substring(0, Math.Min(row[invoiceContactIndex].Length, 50));

                    string customer;
                    if (row[customerIndex] == row[endUserIndex]) { customer = row[customerIndex]; } else { customer = row[customerIndex] + " (" + row[endUserIndex] + ")"; }

                    string reg = row[regNoIndex];

                    //location & stockdate
                    string lastPhysicalLocation = row[vehicleStatusIndex] ;
                    DateTime? today = DateTime.Today;
                    DateTime? stockDate = null;
                    int vehicleAge = 0;

                    //first set the location, stockdate and vehicle age to the existing values in deals
                    if (!string.IsNullOrEmpty(orderNumber))
                    {
                        try
                        {
                            stockDate = dbDeals.Find(x => x.EnquiryNumber == orderNumber.ToString()).StockDate.Value;
                            vehicleAge = dbDeals.Find(x => x.EnquiryNumber == orderNumber.ToString()).VehicleAge;
                        }
                        catch { };
                    }

                    if (reg != "" && reg != null)
                    {
                        try
                        {
                            stockDate = dbStocks.Find(x => x.Reg == reg).StockDate;
                            vehicleAge = (today - stockDate).Value.Days;
                        }
                        catch { };
                    }

                    if (orderNumber.ToString() != "" && orderNumber.ToString() != null)
                    {
                        try
                        {
                            stockDate = dbStocks.Find(x => x.StockNumberFull == orderNumber.ToString()).StockDate;
                            vehicleAge = (today - stockDate).Value.Days;
                        }
                        catch { };
                    }



                    Deal d = new Deal(); //initialise new one

                    d.OrderDate = orderDate;
                    d.ActualDeliveryDate = deliveryDate;
                    d.InvoiceDate = invoiceDate;
                    d.EnquiryNumber = orderNumber.ToString(); 
                    d.Site_Id = siteId;
                    d.Customer = customer;
                    d.Salesman_Id = salesExecId;
                    d.Reg = reg;
                    d.Description = vehicleDescription;
                    d.Model = vehicleDescription;
                    d.VariantTxt = vehicleDescription;
                    d.OrderType_Id = orderTypeId;
                    d.VehicleType_Id = vehicleTypeId;
                    d.Franchise_Id = franchiseId;
                    d.StockNumber = orderNumber.ToString();
                    d.IsFinanced = true;
                    d.AccessoriesSale = dfaSale;
                    d.AccessoriesCost = dfaCost;
                    d.NewBonus1 = tactical + backendBonus + backendBonus2 + backendBonus3 + tactical2 + tactical3;
                    d.NewBonus2 = 0;
                    d.FinanceCommission = financeIncome + vbRebate;
                    d.FinanceSubsidy = financeSubsidy;
                    //d.FAndIProfit = financeIncome + vbRebate + financeSubsidy;
                    d.IntroCommission = brokerCommission;
                    d.Sale = sale;
                    d.BodyPrep = valetCost;
                    //d.TotalVehicleProfit = totalProfit;
                    d.TotalNLProfit = totalProfit;
                    d.PDICost = pdiCost;
                    d.OemDeliverySale = deliveryProfit;
                    d.OemDeliveryCost = onwardDeliveryCost;
                    d.FuelCost = fuelCost;
                    d.CoS = sale * -1 + totalAdjustment + subtotalMargin;
                    d.IsDelivered = isDelivered;
                    d.IsRemoved = false;
                    d.RemovedDate = null;
                    d.OriginalSource = fileNameIn;
                    d.LastUpdated = DateTime.UtcNow;
                    d.Units = 1;
                    d.AccountingDate = deliveryDate;
                    d.CreatedDate = DateTime.UtcNow;
                    //d.IsCancelled = false;
                    d.WhenNew = DateTime.UtcNow;
                    d.HandoverDate = deliveryDate;
                    d.IsInvoiced = true;
                    d.FinanceCo = financeCo;
                    d.DeliverySite_Id = deliverySiteId;
                    d.VehicleClass_Id = 207; //need to update
                    d.StockDate = stockDate;
                    d.VehicleAge = vehicleAge;
                    d.LastPhysicalLocation = lastPhysicalLocation;
                    d.HasWheelGuard = false;

                    incomingDeals.Add(d);

                    Console.WriteLine(incomingProcessCount);
                }

                catch (Exception err)
                {

                    if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                    errorCount++;
                    continue;
                }


            }

           return incomingDeals;


        }
    }
}
