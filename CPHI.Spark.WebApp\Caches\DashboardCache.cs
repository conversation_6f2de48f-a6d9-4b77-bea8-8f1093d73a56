﻿using CPHI.Spark.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.DataAccess;

namespace CPHI.Spark.WebApp
{
    public interface IDashboardCache
    {
        void EmptyAllCachesStartingWith(string cacheNameStart);
        void EmptyCache(string cacheName);
        Task<IEnumerable<ActivityLevelsSummaryItem>> GetActivityLevels(int[] siteIds, DateTime startDate, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<DailyNetOrder>> GetDailyNetOrders(DateTime weekStart, int[] siteIds, string deptName, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<DailyNetOrdersCancellationsCount>> GetDailyNetOrdersCancellations(DateTime weekStart, int[] siteIds, string deptName, Model.DealerGroupName dealerGroup);
        Task<DepartmentDealBreakdown> GetDepartmentDealBreakdown(int[] siteIds, Model.DealerGroupName dealerGroup);
        Task<DepartmentProfitPerUnits> GetDepartmentProfitPerUnits(int[] siteIds, string timePeriodName, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<OverdueSummaryItem>> GetOverdueSummary(int[] siteIds, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<DonutMonthlyData>> GetPerformanceForMonthDonuts(int[] siteIds, DateTime monthStart, List<string> franchiseCodes, List<string> orderTypeTypes, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<DonutData>> GetPerformanceForWTDDonuts(int[] siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<DonutData>> GetPerformanceForYesterdayDonuts(int[] siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup);
        Task<UsedStockMerchandising> GetUsedStockMerchandising(int[] siteIds, Model.DealerGroupName dealerGroup);
        Task<UsedStockMerchandisingBySite> GetUsedStockMerchandisingBySite(int[] siteIds, Model.DealerGroupName dealerGroup);
    }


    public class DashboardCache : IDashboardCache
    {
        private readonly IDashboardDataAccess dashboardDataAccess;
        private readonly IDealService dealService;
        private readonly ITodayNewUsedOrdersCache todayNewUsedOrdersCache;
        private readonly IUserService userService;
        IConfiguration configuration;


        public DashboardCache(IDashboardDataAccess dashboardDataAccess, IDealService dealService, ITodayNewUsedOrdersCache todayNewUsedOrdersCache, IUserService userService, IConfiguration configurationIn)
        {
            this.configuration = configurationIn;
            this.dashboardDataAccess = dashboardDataAccess;
            this.dealService = dealService;
            this.todayNewUsedOrdersCache = todayNewUsedOrdersCache;
            this.userService = userService;

        }

        public async Task<UsedStockMerchandisingBySite> GetUsedStockMerchandisingBySite(int[] siteIds, Model.DealerGroupName dealerGroup)
        {
            //Check if already have
            string cacheName = $"{dealerGroup}|DashboardUsedStockMerchandisingBySite";
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                IEnumerable<UsedStockMerchandisingBySiteItem> usedStockMerchandisingBySiteItems = await dashboardDataAccess.GetUsedStockMerchandisingBySite(dealerGroup);
                FillCache(cacheName, usedStockMerchandisingBySiteItems, DateTime.UtcNow.AddHours(1).AddMinutes(50));
            }

            //Get cache for all sites
            IEnumerable<UsedStockMerchandisingBySiteItem> allSites = (IEnumerable<UsedStockMerchandisingBySiteItem>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the user's sites
            UsedStockMerchandisingBySite result = new UsedStockMerchandisingBySite(allSites.Where(x => siteIds.Contains(x.SiteId)).ToList());


            return result;
        }

        public async Task<UsedStockMerchandising> GetUsedStockMerchandising(int[] siteIds, Model.DealerGroupName dealerGroup)
        {
            //Check if already have
            string cacheName = $"{dealerGroup}|DashboardUsedStockMerchandising";
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                IEnumerable<UsedStockMerchandising> usedStockMerchandising = await dashboardDataAccess.GetUsedStockMerchandising(dealerGroup);
                FillCache(cacheName, usedStockMerchandising, DateTime.UtcNow.AddHours(1).AddMinutes(50));
            }

            //Get cache for all sites
            IEnumerable<UsedStockMerchandising> allSites = (IEnumerable<UsedStockMerchandising>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the user's sites
            UsedStockMerchandising result = ReduceUsedStockMerchForSites(siteIds, allSites);
            return result;
        }



        public async Task<IEnumerable<ActivityLevelsSummaryItem>> GetActivityLevels(int[] siteIds, DateTime startDate, Model.DealerGroupName dealerGroup)
        {
            //Check if already have
            string cacheName = $"{dealerGroup.ToString()}|ActivityLevels{startDate.ToString("yy-MM-dd")}";
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                IEnumerable<ActivityLevelsSummaryItem> activityLevels = await dashboardDataAccess.GetActivityLevels(startDate, dealerGroup);
                FillCache(cacheName, activityLevels, DateTime.UtcNow.AddMinutes(50));
            }

            //Get cache for all sites
            IEnumerable<ActivityLevelsSummaryItem> allSites = (IEnumerable<ActivityLevelsSummaryItem>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the user's sites
            List<ActivityLevelsSummaryItem> results = ReduceActivityLevelsForSites(siteIds, startDate, allSites);

            return results;
        }



        public async Task<DepartmentDealBreakdown> GetDepartmentDealBreakdown(int[] siteIds, Model.DealerGroupName dealerGroup)
        {
            //Check if already have
            string cacheName = $"{dealerGroup.ToString()}|DepartmentDealBreakdowns";
            if (!MemoryCache.Default.Contains(cacheName))
            {

                IEnumerable<DepartmentDealBreakdown> breakdowns = await dashboardDataAccess.GetDepartmentDealBreakdowns(dealerGroup);
                FillCache(cacheName, breakdowns, DateTime.UtcNow.AddMinutes(50));
            }

            //Get cache for all sites
            IEnumerable<DepartmentDealBreakdown> allSites = (IEnumerable<DepartmentDealBreakdown>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the user's sites
            DepartmentDealBreakdown result = ReduceDeptDealBreakdownsForSites(siteIds, allSites);
            return result;
        }



        public async Task<IEnumerable<DonutMonthlyData>> GetPerformanceForMonthDonuts(int[] siteIds, DateTime monthStart, List<string> franchiseCodes,
            List<string> orderTypeTypes, Model.DealerGroupName dealerGroup)
        {
            string timePeriodName = "Month";
            List<DonutMonthlyData> results = await GetDonutMonthlyData(siteIds, timePeriodName, monthStart, franchiseCodes, orderTypeTypes, dealerGroup);
            return FillEmptyMonthlyDonuts(results);
        }

        public async Task<IEnumerable<DonutData>> GetPerformanceForWTDDonuts(int[] siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup)
        {
            string timePeriodName = "WTD";
            DateTime monthStart = DateTime.UtcNow; //not relevant
            List<DonutData> results = await GetDonutData(siteIds, timePeriodName, monthStart, franchiseCodes, dealerGroup);
            return FillEmptyDonuts(results);
        }

        public async Task<IEnumerable<DonutData>> GetPerformanceForYesterdayDonuts(int[] siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup)
        {
            string timePeriodName = "Yesterday";
            DateTime monthStart = DateTime.UtcNow; //not relevant
            List<DonutData> results = await GetDonutData(siteIds, timePeriodName, monthStart, franchiseCodes, dealerGroup);
            return FillEmptyDonuts(results);
        }

        // Ensure we always return donuts for Fleet/New/Used
        private List<DonutData> FillEmptyDonuts(List<DonutData> donuts)
        {

            if (donuts.Find(x => x.Department == "Fleet") == null)
            {
                donuts.Add(CreateEmptyDonut("Fleet"));
            }

            if (donuts.Find(x => x.Department == "Used") == null)
            {
                donuts.Add(CreateEmptyDonut("Used"));
            }

            if (donuts.Find(x => x.Department == "New") == null)
            {
                donuts.Add(CreateEmptyDonut("New"));
            }

            return donuts;
        }

        private List<DonutMonthlyData> FillEmptyMonthlyDonuts(List<DonutMonthlyData> donuts)
        {
            AddMonthlyDonutIfRequired(donuts, "Fleet");
            AddMonthlyDonutIfRequired(donuts, "Used");
            AddMonthlyDonutIfRequired(donuts, "New");
            return donuts;
        }


        private DonutData CreateEmptyDonut(string departmentName)
        {
            return new DonutData() { Department = departmentName };
        }

        private void AddMonthlyDonutIfRequired(List<DonutMonthlyData> donuts, string departmentName)
        {
            if (donuts.Find(x => x.Department == departmentName) == null)
            {
                donuts.Add(new DonutMonthlyData()
                {
                    Department = departmentName,
                    ActualMargin = 0,
                    ActualUnits = 0,
                    ActualUnitsLastMonth = 0,
                    ActualUnitsLastYear = 0,
                    SiteId = 0,
                    TargetMargin = 0,
                    TargetUnits = 0,
                });
            }


        }

        public async Task<DepartmentProfitPerUnits> GetDepartmentProfitPerUnits(int[] siteIds, string timePeriodName, Model.DealerGroupName dealerGroup)
        {
            string cacheName = $"{dealerGroup.ToString()}|DepartmentProfitPerUnits{timePeriodName}";

            //Check if already have
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                IEnumerable<DepartmentProfitPerUnits> splits = await dealService.GetDepartmentProfitPerUnits(timePeriodName, dealerGroup);
                FillCache(cacheName, splits, DateTime.UtcNow.AddMinutes(50));
            }


            //Get cache for all sites
            IEnumerable<DepartmentProfitPerUnits> allSites = (IEnumerable<DepartmentProfitPerUnits>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the user's sites
            DepartmentProfitPerUnits result = ReduceDepartmentPPUs(siteIds, allSites);

            return result;
        }


        public async Task<IEnumerable<DailyNetOrdersCancellationsCount>> GetDailyNetOrdersCancellations(DateTime weekStart, int[] siteIds, string deptName, Model.DealerGroupName dealerGroup)
        {
            //Check if already have data
            string cacheName = $"{dealerGroup.ToString()}|DailyNetOrdersCancellations{weekStart.ToString("yy-MM-dd")}";
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                List<DailyNetOrdersCancellationsCount> ordersList = await GenerateDailyNetOrdersCancellationData(weekStart, dealerGroup);

                FillCache(cacheName, ordersList, DateTime.UtcNow.AddMinutes(50));
            }

            //Get cache for all sites
            IEnumerable<DailyNetOrdersCancellationsCount> allSitesAllDepts = (IEnumerable<DailyNetOrdersCancellationsCount>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the chosen sites
            List<DailyNetOrdersCancellationsCount> resultsForDays = ReduceDailyNetOrdersCancellationsToChosenSitesAndDept(weekStart, siteIds, deptName, allSitesAllDepts);

            return resultsForDays;
        }





        public async Task<IEnumerable<DailyNetOrder>> GetDailyNetOrders(DateTime weekStart, int[] siteIds, string deptName, Model.DealerGroupName dealerGroup)
        {
            //Check if already have
            string cacheName = $"{dealerGroup.ToString()}|DailyNetOrders{weekStart.ToString("yy-MM-dd")}";
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                IEnumerable<DailyNetOrder> orders = await dashboardDataAccess.GetDailyNetOrders(weekStart, dealerGroup);
                FillCache(cacheName, orders, DateTime.UtcNow.AddMinutes(50));
            }

            //Get cache for all sites
            IEnumerable<DailyNetOrder> allSitesAllDepts = (IEnumerable<DailyNetOrder>)MemoryCache.Default.GetCacheItem(cacheName).Value;


            //filter for chosen sites and user sites and dept
            List<DailyNetOrder> results = ReduceDailyNetOrders(siteIds, deptName, allSitesAllDepts);

            return results;
        }



        public async Task<IEnumerable<OverdueSummaryItem>> GetOverdueSummary(int[] siteIds, Model.DealerGroupName dealerGroup)
        {
            //Check if already have
            string cacheName = $"{dealerGroup.ToString()}|OverdueSummary";
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                IEnumerable<OverdueSummaryItem> overDues = await dashboardDataAccess.GetOverdueSummary(dealerGroup);
                FillCache(cacheName, overDues, new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59));
            }

            //Get cache for all sites
            IEnumerable<OverdueSummaryItem> allSites = (IEnumerable<OverdueSummaryItem>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the user's sites
            return ReduceOverdueSummary(siteIds, allSites);
        }



        //---------------------------------------------------------------------------------------------------------------
        // Private Methods
        //---------------------------------------------------------------------------------------------------------------




        private static DepartmentDealBreakdown ReduceDeptDealBreakdownsForSites(int[] siteIds, IEnumerable<DepartmentDealBreakdown> allSites)
        {
            IEnumerable<DepartmentDealBreakdown> filtered = allSites.Where(x => Array.BinarySearch(siteIds, x.SiteId) > -1);
            DepartmentDealBreakdown result = new DepartmentDealBreakdown();
            foreach (var item in filtered)
            {
                result.NewDoneInMonth += item.NewDoneInMonth;
                result.NewBroughtIn += item.NewBroughtIn;
                result.UsedCoreUsed += item.UsedCoreUsed;
                result.UsedDemo += item.UsedDemo;
                result.UsedExDemo += item.UsedExDemo;
                result.UsedExManagement += item.UsedExManagement;
                result.UsedTactical += item.UsedTactical;
                result.FleetFleet += item.FleetFleet;
                result.FleetFleetlocal += item.FleetFleetlocal;
                result.FleetFleetSt += item.FleetFleetSt;
                result.FleetNewAgency += item.FleetNewAgency;
                result.FleetNewDirect += item.FleetNewDirect;
                result.FleetNewFleet += item.FleetNewFleet;
                result.FleetNewLMAgency += item.FleetNewLMAgency;
                result.FleetNewNationalAccount += item.FleetNewNationalAccount;
                result.FleetNewRFOFleet += item.FleetNewRFOFleet;
                result.FleetNewRFORental += item.FleetNewRFORental;
                result.FleetNewVWFSAgency += item.FleetNewVWFSAgency;
                result.FleetAgencyVWFSAgency += item.FleetAgencyVWFSAgency;
                result.FleetCorporate += item.FleetCorporate;
                result.FleetCommercial += item.FleetCommercial;
            }

            return result;
        }

        private static List<ActivityLevelsSummaryItem> ReduceActivityLevelsForSites(int[] siteIds, DateTime startDate, IEnumerable<ActivityLevelsSummaryItem> allSites)
        {
            IEnumerable<ActivityLevelsSummaryItem> filtered = allSites.Where(x => Array.BinarySearch(siteIds, x.SiteId) > -1);
            List<ActivityLevelsSummaryItem> results = new List<ActivityLevelsSummaryItem>();
            ILookup<DateTime, ActivityLevelsSummaryItem> byDay = filtered.ToLookup(x => x.Day);
            DateTime thisDay = startDate;
            for (int i = 0; i < 7; i++)
            {
                bool haveFigures = byDay.Contains(thisDay);
                if (haveFigures)
                {
                    results.Add(new ActivityLevelsSummaryItem()
                    {
                        Day = thisDay,
                        Enquiries = byDay[thisDay].Select(x => x.Enquiries).Sum(),
                        Appointments = byDay[thisDay].Select(x => x.Appointments).Sum(),
                        TestDrives = byDay[thisDay].Select(x => x.TestDrives).Sum(),
                        Orders = byDay[thisDay].Select(x => x.Orders).Sum()
                    });
                }
                else
                {
                    //no figures.  Add null return so chart still draws 7 points
                    results.Add(new ActivityLevelsSummaryItem()
                    {
                        Day = thisDay
                    });
                }
                thisDay = thisDay.AddDays(1);
            }

            return results;
        }


        private static UsedStockMerchandising ReduceUsedStockMerchForSites(int[] siteIds, IEnumerable<UsedStockMerchandising> allSites)
        {
            var filtered = allSites.Where(x => Array.BinarySearch(siteIds, x.SiteId) > -1);
            UsedStockMerchandising result = new UsedStockMerchandising();

            foreach (var item in filtered)
            {
                result.Listed += item.Listed;
                result.NotListed_Under7 += item.NotListed_Under7;
                result.NotListed_Under14 += item.NotListed_Under14;
                result.NotListed_Over14 += item.NotListed_Over14;
                result.OnOrder += item.OnOrder;
                result.Prepped += item.Prepped;

                result.InStockAwaitingPrep += item.InStockAwaitingPrep;
                result.SoldAwaitingPrep += item.SoldAwaitingPrep;
                result.Stock += item.Stock;

                //result.UnPreppedOver7 += item.UnPreppedOver7;
                //result.UnPreppedUnder7 += item.UnPreppedUnder7;

                result.OnRRGSiteNotInstock += item.OnRRGSiteNotInstock;
            }

            return result;
        }



        private async Task<List<DonutData>> GetDonutData(int[] siteIds, string timePeriodName, DateTime monthStart, List<string> franchiseCodes, Model.DealerGroupName dealerGroup)
        {
            string yearMonth = monthStart.ToString("yyyyMM");
            string cacheName = $"{dealerGroup.ToString()}|DonutPerformanceFor{timePeriodName}{yearMonth}";

            //Check if already have
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                IEnumerable<DonutData> breakdowns;
                if (timePeriodName == "WTD")
                {
                    breakdowns = await dealService.GetDonutPerformanceWTD(dealerGroup);
                }
                else if (timePeriodName == "Yesterday")
                {
                    breakdowns = await dealService.GetDonutPerformanceYesterday(dealerGroup);
                }
                else
                {
                    throw new Exception("Invalid time period");
                }

                FillCache(cacheName, breakdowns, DateTime.UtcNow.AddMinutes(50));
            }

            //Get cache for all sites
            IEnumerable<DonutData> allSites = (IEnumerable<DonutData>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the user's sites
            IEnumerable<DonutData> filtered = allSites.Where(x => Array.BinarySearch(siteIds, x.SiteId) > -1);
            if (franchiseCodes != null)
            {
                filtered = filtered.Where(x => franchiseCodes.Contains(x.FranchiseCode));
            }

            List<DonutData> results = new List<DonutData>();
            var tst = filtered.ToLookup(x => x.Department);
            foreach (var deptGrouping in filtered.ToLookup(x => x.Department))
            {
                DonutData result = new DonutData();
                result.Department = deptGrouping.Key;
                foreach (var item in deptGrouping)
                {
                    result.ActualUnits += item.ActualUnits;
                    result.ActualMargin += item.ActualMargin;
                    result.TargetUnits += item.TargetUnits;
                    result.TargetMargin += item.TargetMargin;
                    result.ActualUnitsLastYear += item.ActualUnitsLastYear;
                    result.ActualUnitsLastMonth += item.ActualUnitsLastMonth;
                }
                results.Add(result);
            }

            return results;
        }




        private async Task<List<DonutMonthlyData>> GetDonutMonthlyData(
            int[] siteIds,
            string timePeriodName,
            DateTime monthStart,
            List<string> franchiseCodes,
            List<string> orderTypeTypes,
            Model.DealerGroupName dealerGroup)
        {
            string yearMonth = monthStart.ToString("yyyyMM");
            string cacheName = $"{dealerGroup.ToString()}|DonutPerformanceFor{timePeriodName}{yearMonth}";

            // Check if already have
            if (!MemoryCache.Default.Contains(cacheName))
            {
                //Do not have so generate
                string defaultOrderTypeType = (dealerGroup == Model.DealerGroupName.RRGSpain) ? "Particulares" : "Retail";
                IEnumerable<DonutMonthlyData> breakdowns = await dashboardDataAccess.GetPerformanceForTheMonthDonuts(monthStart, defaultOrderTypeType, dealerGroup);

                if (dealerGroup == Model.DealerGroupName.RRGSpain)
                {
                    var orderTypeToIgnore = new List<string>()
                    {
                        "Cesión",
                        "Cessiones",
                        "Cesiones",
                        "Desguaces"
                    };

                    breakdowns = breakdowns.Where(x => !orderTypeToIgnore.Contains(x.OrderTypeType));
                };

                FillCache(cacheName, breakdowns, DateTime.UtcNow.AddMinutes(50));
            };


            //Get cache for all sites
            IEnumerable<DonutMonthlyData> allSites = (IEnumerable<DonutMonthlyData>)MemoryCache.Default.GetCacheItem(cacheName).Value;

            //Filter and reduce to one item, for the user's sites
            IEnumerable<DonutMonthlyData> filtered = allSites.Where(x => Array.BinarySearch(siteIds, x.SiteId) > -1);

            if (dealerGroup != Model.DealerGroupName.RRGSpain && franchiseCodes != null)
            {
                filtered = filtered.Where(x => franchiseCodes.Contains(x.FranchiseCode));
            }

            //Filter for the chosen orderTypes, if any
            List<DonutMonthlyData> resultsForOTypes = new List<DonutMonthlyData>();
            if (orderTypeTypes != null)
            {
                foreach (var res in filtered)
                {
                    if (orderTypeTypes.Contains(res.OrderTypeType))
                    {
                        resultsForOTypes.Add(res);
                    }
                }
            }
            else
            {
                resultsForOTypes = filtered.ToList();
            }




            //Filter for the chosen department
            List<DonutMonthlyData> results = new List<DonutMonthlyData>();

            foreach (var deptGrouping in resultsForOTypes.ToLookup(x => x.Department))
            {
                DonutMonthlyData result = new DonutMonthlyData();
                result.Department = deptGrouping.Key;
                foreach (var item in deptGrouping)
                {
                    result.ActualUnits += item.ActualUnits;
                    result.ActualMargin += item.ActualMargin;
                    result.TargetUnits += item.TargetUnits;
                    result.TargetMargin += item.TargetMargin;
                    result.ActualUnitsLastYear += item.ActualUnitsLastYear;
                    result.ActualUnitsLastMonth += item.ActualUnitsLastMonth;
                }
                results.Add(result);
            }



            return results;
        }











        public void EmptyCache(string cacheName)
        {
            //foreach (var item in MemoryCache.Default.AsEnumerable())
            //{
            MemoryCache.Default.Remove(cacheName);
            //}
        }

        public void EmptyAllCachesStartingWith(string cacheNameStart)
        {
            foreach (var item in MemoryCache.Default.AsEnumerable())
            {
                if (item.Key.StartsWith(cacheNameStart))
                {
                    MemoryCache.Default.Remove(item.Key);
                }
            }
        }






        private static List<DailyNetOrdersCancellationsCount> ReduceDailyNetOrdersCancellationsToChosenSitesAndDept(DateTime weekStart, int[] siteIds, string deptName, IEnumerable<DailyNetOrdersCancellationsCount> allSitesAllDepts)
        {
            IEnumerable<DailyNetOrdersCancellationsCount> filtered = allSitesAllDepts.Where(x => x.DepartmentName == deptName && Array.BinarySearch(siteIds, x.SiteId) > -1);
            List<DailyNetOrdersCancellationsCount> forSiteAndDept = new List<DailyNetOrdersCancellationsCount>();
            foreach (var dayGrouping in filtered.ToLookup(x => x.DayDate))
            {
                DailyNetOrdersCancellationsCount result = new DailyNetOrdersCancellationsCount();
                result.DayDate = dayGrouping.Key;
                foreach (var item in dayGrouping)
                {
                    result.Orders += item.Orders;
                    result.Cancellations += item.Cancellations;
                }
                forSiteAndDept.Add(result);
            }

            List<DailyNetOrdersCancellationsCount> resultsForDays = new List<DailyNetOrdersCancellationsCount>();
            for (int i = 0; i < 7; i++)
            {
                DateTime thisDay = weekStart.AddDays(i);
                var match = forSiteAndDept.FirstOrDefault(x => x.DayDate.Date == thisDay.Date);
                if (match != null)
                {
                    resultsForDays.Add(match);
                }
                else
                {
                    resultsForDays.Add(new DailyNetOrdersCancellationsCount()
                    {
                        DepartmentName = deptName,
                        DayDate = thisDay,
                    });
                }
            }

            return resultsForDays;
        }

        private async Task<List<DailyNetOrdersCancellationsCount>> GenerateDailyNetOrdersCancellationData(DateTime weekStart, Model.DealerGroupName dealerGroup)
        {
            IEnumerable<DailyNetOrdersCancellationsCount> orders = await dashboardDataAccess.GetDailyNetOrdersCancellations(weekStart, dealerGroup);

            // Do not pull through the orders/cancs for Liverpool/Wirral/Nissan liverpool on 04/12/2024 and 5th (day the sites were sold)
            if (dealerGroup == DealerGroupName.RRGUK)
            {
                orders = orders.Where(x => !(
                    x.DayDate.Year == 2024 &&
                    x.DayDate.Month == 12 &&
                    (x.DayDate.Day == 4 || x.DayDate.Day == 5) &&
                    (x.SiteId == 20 || x.SiteId == 19 || x.SiteId == 30)
                    )
                    );
            }

            List<DailyNetOrdersCancellationsCount> ordersList = orders.ToList();

            // If today is in this week
            int todayVsWeekStart = (DateTime.UtcNow.Date - weekStart.Date).Days;

            // If most recent date in daily orders is the same as todays date
            var mostRecentDateInDailyOrders = DateTime.Today.AddDays(-1);

            if (orders != null && orders.Count() > 0)
            {
                mostRecentDateInDailyOrders = orders.OrderByDescending(o => o.DayDate).First().DayDate;
            }

            var currentDate = DateTime.UtcNow.Date;
            var mostRecentDateInDailyOrdersIsToday = DateTime.Equals(mostRecentDateInDailyOrders, currentDate);

            // Add todays orders if today is in this week AND the most recent date in daily orders is less than today
            if (todayVsWeekStart < 7 && todayVsWeekStart >= 0 && !mostRecentDateInDailyOrdersIsToday)
            {
                string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
                string _connectionString = configuration.GetConnectionString(dgName);
                var siteDataAccess = new SiteDataAccess(_connectionString);
                IEnumerable<SiteVM> allSites = await siteDataAccess.GetAllSites(dealerGroup);
                int[] salesSiteIds = allSites.Where(x => x.IsSales && x.IsActive).Select(x => x.SiteId).ToArray();

                IEnumerable<TodayNewUsedOrder> allSiteTodayOrders = await todayNewUsedOrdersCache.GetTodayNewUsedOrdersCacheRows(dealerGroup);
                //TodayNewUsedOrder todayOrders = dealService.GetTodayNewUsedOrders(salesSiteIds);
                foreach (var todayOrderRow in allSiteTodayOrders)
                {
                    ordersList.Add(new DailyNetOrdersCancellationsCount()
                    {
                        DayDate = DateTime.UtcNow.Date,
                        IsToday = true,
                        SiteId = todayOrderRow.SiteId,
                        DepartmentName = "Used",
                        Orders = todayOrderRow.UsedOrders,
                        Cancellations = 0
                    });

                    ordersList.Add(new DailyNetOrdersCancellationsCount()
                    {

                        DayDate = DateTime.UtcNow.Date,
                        IsToday = true,
                        SiteId = todayOrderRow.SiteId,
                        DepartmentName = "New",
                        Orders = todayOrderRow.NewOrders,
                        Cancellations = 0
                    });

                    ordersList.Add(new DailyNetOrdersCancellationsCount()
                    {
                        DayDate = DateTime.UtcNow.Date,
                        IsToday = true,
                        SiteId = todayOrderRow.SiteId,
                        DepartmentName = "Fleet",
                        Orders = todayOrderRow.FleetOrders,
                        Cancellations = 0
                    });
                };
            }

            return ordersList;
        }


        private static DepartmentProfitPerUnits ReduceDepartmentPPUs(int[] siteIds, IEnumerable<DepartmentProfitPerUnits> allSites)
        {
            IEnumerable<DepartmentProfitPerUnits> filtered = allSites.Where(x => Array.BinarySearch(siteIds, x.SiteId) > -1);

            var result = new DepartmentProfitPerUnits();

            foreach (var item in filtered)
            {
                result.NewDealCount += item.NewDealCount;
                result.NewAddOn += item.NewAddOn;
                result.NewChassis += item.NewChassis;
                result.NewFinance += item.NewFinance;

                result.UsedDealCount += item.UsedDealCount;
                result.UsedAddOn += item.UsedAddOn;
                result.UsedChassis += item.UsedChassis;
                result.UsedFinance += item.UsedFinance;
            }

            result.NewAddOnPU = result.NewDealCount > 0 ? result.NewAddOn / result.NewDealCount : 0;
            result.NewChassisPU = result.NewDealCount > 0 ? result.NewChassis / result.NewDealCount : 0;
            result.NewFinancePU = result.NewDealCount > 0 ? result.NewFinance / result.NewDealCount : 0;

            result.UsedAddOnPU = result.UsedDealCount > 0 ? result.UsedAddOn / result.UsedDealCount : 0;
            result.UsedChassisPU = result.UsedDealCount > 0 ? result.UsedChassis / result.UsedDealCount : 0;
            result.UsedFinancePU = result.UsedDealCount > 0 ? result.UsedFinance / result.UsedDealCount : 0;
            return result;
        }


        private List<DailyNetOrder> ReduceDailyNetOrders(int[] siteIds, string deptName, IEnumerable<DailyNetOrder> allSitesAllDepts)
        {
            IEnumerable<int> userSiteIds = userService.GetUserSiteIds();
            var siteIdsToUse = userSiteIds.Intersect(siteIds).ToArray();
            Array.Sort(siteIdsToUse);
            IEnumerable<DailyNetOrder> filtered = allSitesAllDepts.Where(x => x.DepartmentName == deptName && Array.BinarySearch(siteIdsToUse, x.SiteId) > -1);

            //Filter and reduce to one item, for the user's sites
            List<DailyNetOrder> results = new List<DailyNetOrder>();
            foreach (var dayGrouping in filtered.ToLookup(x => x.DayDate))
            {
                DailyNetOrder result = new DailyNetOrder();
                result.DayDate = dayGrouping.Key;
                foreach (var item in dayGrouping)
                {
                    result.OrderCount += item.OrderCount;
                }
                results.Add(result);
            }

            return results;
        }


        private static IEnumerable<OverdueSummaryItem> ReduceOverdueSummary(int[] siteIds, IEnumerable<OverdueSummaryItem> allSites)
        {
            IEnumerable<OverdueSummaryItem> filtered = allSites.Where(x => Array.BinarySearch(siteIds, x.SiteId) > -1);
            List<OverdueSummaryItem> results = new List<OverdueSummaryItem>();
            foreach (var ageingGrouping in filtered.ToLookup(x => x.Ageing))
            {
                OverdueSummaryItem result = new OverdueSummaryItem();
                result.Ageing = ageingGrouping.Key;
                foreach (var item in ageingGrouping)
                {
                    result.Count += item.Count;
                }
                results.Add(result);
            }
            return results;
        }




        private void FillCache(string cacheName, object contents, DateTime expiry)
        {
            lock (MemoryCache.Default)
            {
                MemoryCache.Default.Add(new CacheItem(cacheName, contents), new CacheItemPolicy() { AbsoluteExpiration = expiry });
            }
        }

    }




}

