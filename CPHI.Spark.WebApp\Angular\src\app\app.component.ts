import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { NavigationEnd, Router, RouterStateSnapshot } from '@angular/router';
import { NgbDropdownConfig, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import packagejson from '../../package.json';
import { ConfirmModalComponent } from './components/confirmModal.component';
import { DealInputModalComponent } from './components/dealInputModal/dealInputModal.component';
import { LocationsModalComponent } from './components/locationsModal/locationsModal.component';
import { Department, Languages, MenuItem, OrderbookTimePeriod } from './model/main.model';
import { OrderBookService } from './pages/orderBook/orderBook.service';
import { AppConfigService } from './services/appConfig.service';
import { AppStartService } from './services/appStart.service';
//non-standard
import { ConstantsService } from './services/constants.service';
import { EnvironmentService } from './services/environment.service';
import { GetDataMethodsService } from './services/getDataMethods.service';
import { SelectionsService } from './services/selections.service';
import { InputModalComponent } from './components/inputModal.component';
import { AutoPriceSettingsModalComponent } from './components/autopriceSettingsModal.component';
import { DealerGroupSelectionModalComponent } from './components/dealerGroupSelectionModal/dealerGroupSelectionModal.component';
import { UserPreferenceService } from './services/userPreference.service';
import { PreferenceKey } from './model/UserPreference';
import { Chart } from 'chart.js';



@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {


  hoveredMenuItem: any = null;
  showSideMenu: boolean = false;
  public version: string;

  connection: any;
  messages: any[];
  timeoutHandler: any;
  subscription: Subscription;



  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public dropdownConfig: NgbDropdownConfig,
    public router: Router,
    public environmentService: EnvironmentService,
    public appConfigService: AppConfigService,
    public getDataMethodsService: GetDataMethodsService,
    public appStartService: AppStartService,
    public orderBookService: OrderBookService,
    public modalService: NgbModal,
    public userPrefsService:UserPreferenceService

  ) {

  }
  

  
  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (window.innerWidth >= 1910) {
      this.selections.chartFontsize = 14;
    } else {
      this.selections.chartFontsize = 11;
    }

    this.constants.gridRedraw$.next();
    Chart.defaults.font.size = this.selections.getChartFontSize();
  }
  @ViewChild('alertModal', { static: true }) alertModal: ElementRef;

  @ViewChild('confirmModal', { static: true }) confirmModal: ConfirmModalComponent;
  @ViewChild('inputModal', { static: true }) inputModal: InputModalComponent;
  @ViewChild('autoPriceSettingsModal', { static: true }) autoPriceSettingsModal: AutoPriceSettingsModalComponent;

  ngOnInit() {
    this.appStartService.subscribeToSpinnerEvents();

     this.version = packagejson.version;
     this.constants.environment = this.environmentService.get(); //this sets the default/empty env.
     this.constants.backEndBaseURL = this.appConfigService.backEndBaseURL;
     this.dropdownConfig.autoClose = 'outside';
     this.referenceModals(this.confirmModal, this.alertModal, this.inputModal, this.autoPriceSettingsModal);
     

    if (window.location.pathname != '/resetpassword'){
    //this.router.navigateByUrl('/'); //commented: App was loading twice.
    let token = localStorage.getItem('accessToken');
    const userDealerGroupsString = localStorage.getItem('userDealerGroups');
    if(userDealerGroupsString){
      const userDealerGroups = JSON.parse(userDealerGroupsString);
      this.selections.userDealerGroups = userDealerGroups;
    }
    this.appStartService.startApp(token);
    }

    //Reload the App at midnight
    let msUntilMidnight = this.msUntilMidnight();
    //Adding 1 minute
    msUntilMidnight += 60000
    
    setTimeout(() => {
      window.location.reload();
    }, msUntilMidnight)

   
  }





  msUntilMidnight() {
    var midnight = new Date();
    midnight.setHours(24);
    midnight.setMinutes(0);
    midnight.setSeconds(0);
    midnight.setMilliseconds(0);
    return ( midnight.getTime() - new Date().getTime() );
  }

  public referenceModals(confirmModal: ConfirmModalComponent, alertModal: ElementRef, inputModel: InputModalComponent, autoPriceSettingsModal: AutoPriceSettingsModalComponent) {
    this.constants.autoPriceSettingsModal = autoPriceSettingsModal;
    this.constants.inputModal = inputModel;
    this.constants.confirmModal = confirmModal;
    this.constants.alertModal = {
      elementRef: alertModal,
      title: '',
      message: '',
    };
  }

  OkToShowRouterOutlet(){
    return this.constants.initialStartupComplete || this.constants.urlsToBeSkippedToRefesh.includes(this.router.url.split('?')[0])
  }






  public openSideMenu() { this.showSideMenu = true; }
  public hideSideMenu() { this.showSideMenu = false; }
  public toggleSideMenu() { this.showSideMenu = !this.showSideMenu; }


  showSpinnerIfNotThisPage(menuItem: MenuItem) {
    if (this.router.url !== menuItem.link) {
      // this.constants.menu.forEach((section: MenuItem[]) => {
      //   section.forEach((item: MenuItem) => {
      //     if (item.name == menuItem.name) {
      //       item.isActive = true;
      //     } else {
      //       item.isActive = false;
      //     }
      //   })
      // })
      this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.LoadingPage })
    }
  }










  showOrderBookDealsToday(isNew: boolean) {

    let currentRoute = this.router.url.substring(1);

    let departmentName: string = isNew ? 'New' : 'Used';
    const department: Department = this.constants.departments.find(x => x.ShortName == departmentName);

    let orderTypes: string[] = department.OrderTypeTypes.filter(x => x !== 'Trade' && x !== 'Auction');
    let vehicleTypeTypes: string[] = this.constants.clone(department.VehicleTypeTypes);

    this.orderBookService.initOrderbook();

    this.selections.selectedSites = this.constants.sitesActive;
    this.orderBookService.orderTypeTypes = orderTypes;
    this.orderBookService.vehicleTypeTypes = vehicleTypeTypes;

    this.orderBookService.orderDate.startDate = this.constants.todayStart;
    this.orderBookService.orderDate.endDate = this.constants.todayEnd;
    this.orderBookService.orderDate.lastChosenDay = this.constants.todayStart;
    this.orderBookService.orderDate.timePeriod = OrderbookTimePeriod.Day

    
    this.orderBookService.accountingDate.endDate = this.constants.addYears(this.constants.appStartTime, 2)
    this.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Anytime



    if (currentRoute === 'orderBook') {
      //already on orderbook, just change choices and fetch
      this.selections.clickedTodayOrders.emit();
    } else {
      this.orderBookService.showOrderbook();
    }
  }

  onLanguageChange(newLang: string) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.RestartingMessage })
    this.getDataMethodsService.updateUserPrefLanguage(newLang).subscribe((res) => {
      window.location.reload();
    });
  }


  userSettings(state?: RouterStateSnapshot) {
    this.router.navigateByUrl('manage');
  }


  appWrapperClassProvider() {
    let result: string[] = [this.constants.environment.customer]
    if (this.constants.amLoggedIn) { result.push('amLoggedIn') };
    if (this.userPrefsService.getPreference(PreferenceKey.KeepMenuFixed)) { result.push('fixSideMenu') };
    return result;
  }

  launchDealInputModal() {
    const modalRef = this.modalService.open(DealInputModalComponent);
    modalRef.result.then((result) => { });
  }
}

