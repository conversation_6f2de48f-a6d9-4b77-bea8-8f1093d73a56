using Microsoft.Extensions.DependencyInjection;
using System;
using System.Net.Http;

namespace CPHI.Spark.WebScraper.Services
{
    public static class HttpClientFactoryService
    {
        public static IHttpClientFactory HttpClientFactory;

        public static void Initialize(IServiceProvider serviceProvider)
        {
            if (HttpClientFactory != null) return; // Already initialized
            if (serviceProvider == null) throw new ArgumentNullException(nameof(serviceProvider));
            HttpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();
            if (HttpClientFactory == null) throw new InvalidOperationException("IHttpClientFactory could not be resolved.");
        }
    }
}
