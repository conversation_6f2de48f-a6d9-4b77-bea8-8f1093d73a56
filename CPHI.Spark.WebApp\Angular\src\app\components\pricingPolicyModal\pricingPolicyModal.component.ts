import {Component, OnInit} from "@angular/core";
import {NgbActiveModal, NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {CphPipe} from "src/app/cph.pipe";
import {FactorItemHorizontalBand, StrategyFactorItemVM} from "src/app/model/StrategyFactorItemVM";
import {StrategyFactorName} from "src/app/model/StrategyFactorName";
import {StrategyFactorVM} from "src/app/model/StrategyFactorVM";
import {StrategyVersionVM} from 'src/app/model/StrategyVersionVM';
import {SiteSettingsService} from "src/app/pages/autoprice/siteSettings/siteSettings.service";
import {ConstantsService} from "src/app/services/constants.service";
import {GetDataMethodsService} from "src/app/services/getDataMethods.service";
import {SelectionsService} from "src/app/services/selections.service";
import {NewFactorModalComponent} from "./newFactorModal/newFactorModal.component";
import {PricingPolicyModalService} from "./pricingPolicyModal.service";
import {MaybeDeleteFactorModalComponent} from "./maybeDeleteFactorModal/maybeDeleteFactorModal.component";
import {EnvironmentService} from "../../services/environment.service";
import {AutotraderService} from "src/app/services/autotrader.service";


@Component({
   selector: 'pricingPolicyModal',
   templateUrl: './pricingPolicyModal.component.html',
   styleUrls: ['./pricingPolicyModal.component.scss']
})

export class PricingPolicyBuilderModalComponent implements OnInit {
   public StrategyFactorName = StrategyFactorName;

   public chosenStrategyClone: StrategyVersionVM;

   public instructionMessage: string = `The adjustment layers below show how this pricing policy will build up a price for each vehicle.   The policy will start with the retail valuation then apply each subsequent adjustment layer in turn.`
   public newStategyInstructionMessage: string = `Click 'Add Factor' to add an adjustment layer to this pricing policy.`

   addFactorModalRef: NgbModalRef;
   // allStrategyFactors: StrategyFactorVM[] //for choosing new factor

   ageCategories: string[] = ['<1yrs', '1-3yrs', '3-5yrs', '5-10yrs', '>10yrs'];
   previousOwners: string[] = ['1', '2', '3', '4+'];

   get additionalFactors() {
      const existingNames = this.service.chosenPolicy.StrategyFactors.map(x => x.Name);
      const allFactors = this.service.allStrategyFactors
         .filter(x => !existingNames.includes(x.Name))
         .filter(x => x.Name !== StrategyFactorName.WholesaleAdjustment ||
            this.environmentService.get().showWholesaleAdjustmentOption === true)
      ;

      return allFactors;
   }

   get fuelTypes() {
      return AutotraderService.getFuelTypes
   }

   get getAgeBands() {
      return AutotraderService.getSortOrderForAgeBand
   }


   constructor(
      public selections: SelectionsService,
      public constants: ConstantsService,
      public siteSettingsService: SiteSettingsService,
      private modalService: NgbModal,
      private getDataService: GetDataMethodsService,
      private cphPipe: CphPipe,
      private activeModal: NgbActiveModal,
      public service: PricingPolicyModalService,
      public environmentService: EnvironmentService
   ) {
   }

   ngOnInit(): void {

      this.service.chosenPolicy?.StrategyFactors.forEach(strategyFactor => {

         if (strategyFactor.Name == StrategyFactorName.AgeAndOwners) {
            strategyFactor.StrategyFactorItems.forEach(factorItem => {
               [factorItem.selectedAgeCategory, factorItem.selectedPreviousOwners] =
                  factorItem.Label?.split('|') || [this.ageCategories[0], this.previousOwners[0]];
            });
         } else if (strategyFactor.Name == StrategyFactorName.MakeFuelType) {
            strategyFactor.StrategyFactorItems.forEach(factorItem => {
               [factorItem.selectedMake, factorItem.selectedFuelType] =
                  factorItem.Label?.split('|') || ['Make', 'Fuel Type'];
            });
         } else if (strategyFactor.Name == StrategyFactorName.MakeAgeBand) {
            strategyFactor.StrategyFactorItems.forEach(factorItem => {
               [factorItem.selectedMake, factorItem.selectedAgeBand] =
                  factorItem.Label?.split('|') || ['Make', '<1yrs'];
            });
         }


      });


   }

   select(event: any) {
      event.target.select();
   }

   setStrategyValue(strategyFactor: StrategyFactorItemVM, event: any) {
      if (!event.target || (event.target && event.target.value == '')) return;
      let priceFactor: number = parseFloat(event.target.value);
      strategyFactor.Value = (priceFactor / 100);
   }


   setCommentValue(strategyFactor: StrategyFactorItemVM, event: any) {
      if (!event.target || (event.target && event.target.value == '')) return;
      strategyFactor.Comment = event.target.value;
   }


   showNewStrategyModal() {
      let message = 'Enter pricing policy name'
      this.constants.inputModal.inputModalHeader = message;


      let mySubscription = this.selections.inputModalEmitter.subscribe(text => {
         if (text) {
            this.createNewStrategy(text);
         }

         mySubscription.unsubscribe();
      })


      this.constants.inputModal.showModal();

   }

   createNewStrategy(name: string) {
      const sv = {Name: name} as StrategyVersionVM
      this.siteSettingsService.createStrategyVersion(sv);
   }

   async deletePolicy() {
      await this.getDataService.deleteStrategyVersion(this.service.chosenPolicy).toPromise();
      await this.constants.sleep(300);
      this.dismissModal(true);
   }


   savePolicy() {
      //ensure competitor item values have been reassembled into the value
      let chosenPolicyToPersist: StrategyVersionVM = this.constants.clone(this.service.chosenPolicy);

      chosenPolicyToPersist.StrategyFactors.map(factor => {


         if (factor.Name === StrategyFactorName.MatchCheapestCompetitor || factor.Name === StrategyFactorName.AchieveMarketPositionScore) {

            const newStrategyFactorItems: StrategyFactorItemVM[] = [];
            factor.StrategyFactorItems.map(item => {
               const newValue: number = (item.BoolValue != null) ? item.BoolValue ? 1 : 0 : item.Value;
               let newFactorItem = new StrategyFactorItemVM(null, item.Label, newValue);
               newFactorItem.Comment = item.Comment;
               newStrategyFactorItems.push(newFactorItem)
            })

            factor.StrategyFactorItems = newStrategyFactorItems
         }


         if (factor.Name === StrategyFactorName.RR_DL_Matrix) {
            //need to convert the values into updated labels
            //RR20|DL20
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map(item => {

                  const daysListed: string = horizontalLabel.value.toString();
                  const newLabel = `RR${item.Label}|DL${daysListed}`
                  const newValue = item.horizontalBands[labelIndex].value;
                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem)
               })
            })

            factor.StrategyFactorItems = newStrategyFactorItems
         } else if (factor.Name === StrategyFactorName.RR_DS_Matrix) {
            //need to convert the values into updated labels
            //RR20|DS20
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map(item => {

                  const daysInStock: string = horizontalLabel.value.toString();
                  const newLabel = `RR${item.Label}|DS${daysInStock}`
                  const newValue = item.horizontalBands[labelIndex].value;
                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem)
               })
            })

            factor.StrategyFactorItems = newStrategyFactorItems
         } else if (factor.Name === StrategyFactorName.DTS_DL_Matrix) {
            //need to convert the values into updated labels
            //DTS20|DL20
            const newStrategyFactorItems: StrategyFactorItemVM[] = [];

            factor.horizontalBandLabels.forEach((horizontalLabel, labelIndex) => {
               factor.StrategyFactorItems.map(item => {

                  const daysListed: string = horizontalLabel.value.toString();
                  const newLabel = `DTS${item.Label}|DL${daysListed}`
                  const newValue = item.horizontalBands[labelIndex].value;
                  let newFactorItem = new StrategyFactorItemVM(null, newLabel, newValue);
                  newFactorItem.Comment = item.Comment;
                  newStrategyFactorItems.push(newFactorItem)
               })
            })

            factor.StrategyFactorItems = newStrategyFactorItems
         } else if (factor.Name == StrategyFactorName.AgeAndOwners) {
            factor.StrategyFactorItems.map(item => {
               item.Label = item.selectedAgeCategory + '|' + item.selectedPreviousOwners;
            })
         } else if (factor.Name == StrategyFactorName.MakeFuelType) {
            factor.StrategyFactorItems.map(item => {
               item.Label = item.selectedMake + '|' + item.selectedFuelType;
            })
         } else if (factor.Name == StrategyFactorName.MakeAgeBand) {
            factor.StrategyFactorItems.map(item => {
               item.Label = item.selectedMake + '|' + item.selectedAgeBand;
            })
         }


      })

      this.selections.triggerSpinner.next({show: true, message: 'Saving...'});

      this.getDataService.saveStrategyVersion(chosenPolicyToPersist).subscribe((res: number | null) => {

         this.constants.toastSuccess('Saved pricing policy');
         this.closeModal(res);
         //this.selections.triggerSpinner.next({ show: false });

      }, (error: any) => {
         console.error('Failed to save poricing policy factor items', error);
         //this.selections.triggerSpinner.next({ show: false });
      });


   }


   public toggleFactorItemBoolValue(factorItem: StrategyFactorItemVM) {
      factorItem.BoolValue = !factorItem.BoolValue
   }


   strategyTextSummary(strategy: StrategyVersionVM) {
      if (!strategy) {
         console.trace;
      }
      const firstUsed: string = strategy.FirstUsed != null ? this.cphPipe.transform(new Date(strategy.FirstUsed), 'shortDate', 0) : 'never';
      const lastUsed: string = strategy.LastUsed != null ? this.cphPipe.transform(new Date(strategy.LastUsed), 'shortDate', 0) : 'never';
      return `Strategy #${strategy.Id}: ${strategy.Name} created ${this.cphPipe.transform(new Date(strategy.CreatedDate), 'dateMed', 0)} by ${strategy.CreatedByName} first used ${firstUsed}, last used ${lastUsed}`
   }


   public showAddFactorModal() {

      const modalRef = this.addFactorModalRef = this.modalService.open(NewFactorModalComponent, {
         size: 'md',
         keyboard: false,
         ariaLabelledBy: 'modal-basic-title'
      });

      //console.log("ADDFAC ", this.additionalFactors);

      modalRef.componentInstance.additionalFactors = this.additionalFactors;

      this.addFactorModalRef.result.then(() => {

      }, () => {
         modalRef.close();
      });
   }

   deleteFactor(factor: StrategyFactorVM) {
      this.service.chosenPolicy.StrategyFactors = this.service.chosenPolicy.StrategyFactors.filter(x => x.Name !== factor.Name);
   }


   changesMade() {
      return this.isDifferent(this.service.chosenPolicy, this.chosenStrategyClone)
   }

   canDeletePolicy() {
      return this.service.chosenPolicy.FirstUsed == null
   }

   showDeleteButton() {
      if (this.service.chosenPolicy.Id == null) {
         return false;
      }
      return true;
   }

   isDifferent(obj1: any, obj2: any): boolean {
      // If both are not objects or are null, compare them directly
      if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
         return obj1 !== obj2;
      }

      // Get the keys of both objects
      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);

      // If the number of keys is different, objects are different
      if (keys1.length !== keys2.length) {
         return true;
      }

      // Check if any key name is different
      for (const key of keys1) {
         if (!(key in obj2)) {
            return true;
         }
      }

      // Recursively compare the values of the keys
      for (const key of keys1) {
         if (this.isDifferent(obj1[key], obj2[key])) {
            return true;
         }
      }

      // If none of the above checks returned true, objects are the same
      return false;
   }

   closeModal(res: number | null) {
      this.activeModal.close(res);
   }

   public dismissModal(didWeDelete: boolean) {
      this.activeModal.dismiss(didWeDelete);
   }

   strategyChoiceName(factor: StrategyFactorVM) {
      if (factor.Name === StrategyFactorName.MatchCheapestCompetitor || factor.Name === StrategyFactorName.AchieveMarketPositionScore) {
         return 'Position Number'
      }
      if (factor.Name === StrategyFactorName.DaysToSell) {
         return 'No. of Days'
      }
      return 'Impact %'
   }

   pasteMatrixNote(): string {
      return `It is possible to copy and paste directly from excel into this matrix, firstly ensure the matrix is setup here to have the correct number of columns and rows, then copy the whole area in excel, right click on this input box and choose 'Paste'.`
   }

   pasteEditableLabelNote(): string {
      return `It is possible to copy and paste directly from excel into this list, simply right click on this input box and choose 'Paste'.`
   }

   radiusNote(): string {
      return 'The number of miles around the dealer in which to search for competitors.'
   }

   plateStepsNote(): string {
      return 'The number of plate steps either side of the advert to search, e.g. 1 plate either way from a 20 would search between 69 and 70.'
   }

   mileageStepsNote(): string {
      return 'The amount of odometer miles to search either side of the advert.'
   }

   rankingToAchieveNote(): string {
      return 'The desired position you would like to achieve amongst the competitor set.   e.g. 2 would set the strategy price to make you the 2nd cheapest seller.    If, for example, you chose 5 and there are only 2 competitors, this strategy layer will have no effect.'
   }

   marketPositionScoreToAchieveNote(): string {
      return 'The desired market position you would like to achieve amongst the competitor set.   For example if there are 50 competitors and you choose to achieve 90 this strategy layer will impact the strategy price down to ensure you are the 5th cheapest seller.'
   }

   sellerTypeNote(): string {
      return 'Whether to include each of these seller channels when searching for competitors.'
   }

   wholesaleAdjustmentPctNote(): string {
      return 'Amount to adjust strategy price. For example entering 94% the strategy price will be multiplied by 94%'
   }

   wholesaleAdjustmentValueNote(): string {
      return 'Amount to adjust strategy price. For example entering 700 the strategy price will be increased by £700'
   }

   makeFuelTypeNote(): string {
      return `The make and fuel type of the vehicle that the strategy is being applied to.`
   }

   makeAgeBandNote(): string {
      return `The make and age band of the vehicle that the strategy is being applied to.`
   }


   getTableEditableColumnCount(factor: StrategyFactorVM): number {
      if (factor.Name === StrategyFactorName.DaysListed) {
         return 3
      }
      return 1;
   }

   updateLabel(factorItem: any): void {
      factorItem.Label = `${factorItem.selectedAgeCategory}|${factorItem.selectedPreviousOwners}`;
   }

   updateAgeAndOwnersLabelForAge(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      const selectedText = selectElement.options[selectElement.selectedIndex].text;
      factorItem.selectedAgeCategory = selectedText;
      factorItem.Label = `${factorItem.selectedAgeCategory}|${factorItem.selectedPreviousOwners}`
   }


   updateAgeAndOwnersLabelForOwners(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      const selectedText = selectElement.options[selectElement.selectedIndex].text;
      factorItem.selectedPreviousOwners = selectedText;
      factorItem.Label = `${factorItem.selectedAgeCategory}|${factorItem.selectedPreviousOwners}`
   }

   updateFuelType(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      const selectedText = selectElement.options[selectElement.selectedIndex].text;
      factorItem.selectedFuelType = selectedText;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedFuelType}`
   }

   updateAgeBand(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      const selectedText = selectElement.options[selectElement.selectedIndex].text;
      factorItem.selectedAgeBand = selectedText;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedAgeBand}`
   }

   updateMakeForMakeAndFuelType(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      factorItem.selectedMake = selectElement.value;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedFuelType}`
   }

   updateMakeForMakeAndAgeBand(event: Event, factorItem: StrategyFactorItemVM) {
      const selectElement = event.target as HTMLSelectElement;
      factorItem.selectedMake = selectElement.value;
      factorItem.Label = `${factorItem.selectedMake}|${factorItem.selectedAgeBand}`
   }

   currency() {
      return this.environmentService.get().displayCurrency;
   }


   public maybeDeleteFactor(event: MouseEvent, factor: StrategyFactorVM) {
      event.stopPropagation();

      const modalRef = this.modalService.open(MaybeDeleteFactorModalComponent, {
         size: 'md',
         keyboard: false,
         ariaLabelledBy: 'modal-basic-title'
      });
      const modalComponent: MaybeDeleteFactorModalComponent = modalRef.componentInstance;
      modalComponent.title = 'Are you sure you want to remove this adjustment layer?'

      modalRef.result.then(() => {
         //chose ok:
         this.deleteFactor(factor)
      }, () => {
         //chosen cancel:
         //modalRef.nativeElement.close()
         //this.modalService.dismissAll();
      });
   }

   public maybeDeleteFactorItem(event: MouseEvent, factor: StrategyFactorVM, index: number) {
      event.stopPropagation();
      const modalRef = this.modalService.open(MaybeDeleteFactorModalComponent, {
         size: 'md',
         keyboard: false,
         ariaLabelledBy: 'modal-basic-title'
      });
      const modalComponent: MaybeDeleteFactorModalComponent = modalRef.componentInstance;
      modalComponent.title = 'Are you sure you want to remove this item?'

      modalRef.result.then(() => {
         this.deleteFactorItem(factor, index)
      }, () => {
         //modalRef.nativeElement.close()
         //this.modalService.dismissAll();
      });
   }

   public maybeDeleteHorizontalBand(event: MouseEvent, factor: StrategyFactorVM, index: number) {
      event.stopPropagation();
      //let modalRef = this.maybeDeleteModal;
      const modalRef = this.modalService.open(MaybeDeleteFactorModalComponent, {
         size: 'md',
         keyboard: false,
         ariaLabelledBy: 'modal-basic-title'
      })
      const modalComponent: MaybeDeleteFactorModalComponent = modalRef.componentInstance;
      modalComponent.title = 'Are you sure you want to remove this band?'
      modalRef.result.then(() => {
         this.deleteHorizontalBand(factor, index)
      }, () => {
         //modalRef.nativeElement.close()
         //this.modalService.dismissAll();
      });
   }

   deleteFactorItem(factor: StrategyFactorVM, index: number) {
      factor.StrategyFactorItems.splice(index, 1);
   }

   deleteHorizontalBand(factor: StrategyFactorVM, index: number) {
      factor.horizontalBandLabels.splice(index, 1);
      factor.StrategyFactorItems.map(item => {
         item.horizontalBands.splice(index, 1)
      })
   }

   addHorizontalBand(event: MouseEvent, factor: StrategyFactorVM, index: number) {
      const lastVal = factor.horizontalBandLabels[index - 1].value;
      let nextVal = lastVal + 10;
      if (index == factor.horizontalBandLabels.length) {
         nextVal = 999
      }
      factor.horizontalBandLabels.splice(index, 0, {value: nextVal})
      factor.StrategyFactorItems.map(item => {
         item.horizontalBands.splice(index, 0, {id: null, value: 100})
      })
   }


   addFactorItem(event: MouseEvent, factor: StrategyFactorVM, index: number) {
      if (factor.Name === StrategyFactorName.RR_DL_Matrix || factor.Name === StrategyFactorName.RR_DS_Matrix || factor.Name === StrategyFactorName.DTS_DL_Matrix) {

         const newBandingValues: FactorItemHorizontalBand[] = [];
         factor.horizontalBandLabels.forEach(label => {
            newBandingValues.push({id: null, value: 100})
         })

         //workout the label
         const lastLabel = parseInt(factor.StrategyFactorItems[index - 1].Label);
         let nextLabel = lastLabel + 10;
         if (index == factor.StrategyFactorItems.length) {
            nextLabel = 100;
         }

         const newItem: StrategyFactorItemVM = new StrategyFactorItemVM(factor.Id, nextLabel.toString(), 100, newBandingValues);
         factor.StrategyFactorItems.splice(index, 0, newItem)

         factor.limitMaximumFactorItem();

      } else {
         const newItem: StrategyFactorItemVM = new StrategyFactorItemVM(factor.Id, null, 100);
         factor.StrategyFactorItems.push(newItem)
         factor.limitMaximumFactorItem();
      }
   }


   showWarningWrongFinishPoint(factor: StrategyFactorVM) {
      return factor.horizontalBandLabels[factor.horizontalBandLabels.length - 1].value !== 999;
   }


   // Method to handle the paste event
   handlePasteMatrix(event: ClipboardEvent, factor: StrategyFactorVM) {
      event.preventDefault();

      // Get the clipboard data as a string
      const {gridData, expectedRows, expectedColumns} = this.interpretDroppedData(event, factor);

      // Validate the size of the pasted data
      if (gridData.length !== expectedRows || gridData[0].length !== expectedColumns) {
         this.constants.toastDanger('It was not possible to update the grid based on the pasted in data');
         return;
      }

      // Replace the grid data with the pasted data
      for (let i = 0; i < expectedRows; i++) {
         for (let j = 0; j < expectedColumns; j++) {
            if (factor.StrategyFactorItems[i].horizontalBands) {
               factor.StrategyFactorItems[i].horizontalBands[j].value = parseFloat(gridData[i][j]);
            } else {
               factor.StrategyFactorItems[i].Value = parseFloat(gridData[i][j]);
            }
         }
      }

      this.constants.toastSuccess('Grid percentages have been updated');
   }

   // Method to handle the paste event
   handlePasteEditableLabel(event: ClipboardEvent, factor: StrategyFactorVM) {
      event.preventDefault();

      // Get the clipboard data as a string
      const clipboardData = event.clipboardData; // || window.clipboardData;
      const pastedText = clipboardData.getData('text');

      // Split the clipboard data into rows and cells
      const rows = pastedText.split('\n').filter(row => row.trim().length > 0);
      const gridData = rows.map(row => row.split('\t'));

      // Validate the size of the pasted data
      factor.StrategyFactorItems = [];

      gridData.forEach(row => {
         factor.StrategyFactorItems.push(new StrategyFactorItemVM(factor.Id, row[0], parseFloat(row[1])));
      })


      this.constants.toastSuccess('Rows have been updated');
   }


   private interpretDroppedData(event: ClipboardEvent, factor: StrategyFactorVM) {
      const clipboardData = event.clipboardData; // || window.clipboardData;
      const pastedText = clipboardData.getData('text');

      // Split the clipboard data into rows and cells
      const rows = pastedText.split('\n').filter(row => row.trim().length > 0);
      const gridData = rows.map(row => row.split('\t'));

      const expectedRows = factor.StrategyFactorItems.length;
      const expectedColumns = factor.StrategyFactorItems[0]?.horizontalBands?.length || 1;
      return {gridData, expectedRows, expectedColumns};
   }

   disableAddFactorButtonForEditableFactorItem(factorItem: StrategyFactorItemVM) {
      try {

         const factorMax = factorItem.Label.split('-')[1];
         if (factorMax == '999') {
            return true;
         }
      } catch (error) {
      }

      return false;
   }

   colourHorizontalBandRed(factor: StrategyFactorVM, bandLabelIndex: number, labelValue: number) {
      if (bandLabelIndex == 0) {
         return false;
      }
      if (isNaN(labelValue)) {
         return true;
      }
      const lastLabelValue = factor.horizontalBandLabels[bandLabelIndex - 1].value;
      return lastLabelValue >= labelValue
   }

   colourVerticalBandRed(factor: StrategyFactorVM, bandLabelIndex: number, labelValue: string) {
      if (bandLabelIndex == 0) {
         return false;
      }
      const labelAsNumber = parseInt(labelValue);
      // if(this.constants.hasAnyNonNumericCharacter(labelValue)){return true;}
      if (isNaN(labelAsNumber)) {
         return true;
      }
      const lastLabelValue = parseInt(factor.StrategyFactorItems[bandLabelIndex - 1].Label);
      return lastLabelValue >= labelAsNumber
   }

}
