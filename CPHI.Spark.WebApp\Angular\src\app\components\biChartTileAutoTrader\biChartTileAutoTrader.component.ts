import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, ViewChild } from "@angular/core";
import { <PERSON>rid<PERSON>pi, ValueGetterParams } from "ag-grid-community";
import { Subscription } from "rxjs";
import { CphPipe } from "src/app/cph.pipe";
import { ConstantsService } from "src/app/services/constants.service";
import { localeEs } from 'src/environments/locale.es.js';
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { VNTileTableRow } from "src/app/model/VNTileTableRow";
import { DashboardMeasure } from "src/app/model/DashboardMeasure";
import { VNTileParams } from "src/app/model/VNTileParams";
import { Router } from "@angular/router";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";

export enum BIChartTileDataType{
    day = "day",
    weekly = "weekly",
    month = "month",
    label = "label"
}


@Component({
    selector: 'biChartTileAutoTrader',
    templateUrl: './biChartTileAutoTrader.component.html',
    styleUrls: ['./biChartTileAutoTrader.component.scss']
})

export class BIChartTileAutoTraderComponent implements OnInit, OnDestroy {


    tableRows: VNTileTableRow[]
    chartRefreshEmitter: EventEmitter<boolean>

   // @Input() public isDateBased: boolean
   @Input() public dataType:BIChartTileDataType
    @Input() public fieldName: string
    @Input() public title: string
    @Input() public itemsToExclude: string[]
    @Input() public tileType: 'Table' | 'VerticalBar' | 'HorizontalBar' | 'VerticalBarPercent' | 'DonutChart' | 'DonutChartFlex'
    @Input() public pageParams: VNTileParams;
    @Input() public labelWidth: number;
    //@Input() public isWeekly: boolean;

    isStockInsightPage: boolean;
    isLeavingVehiclePage: boolean;

    subscription: Subscription;
    @ViewChild('contentHolder', { static: true }) contentHolder: ElementRef;


    public gridApi: GridApi;
    mainTableGridOptions: GridOptionsCph
    maxValue: number;


    constructor(
        public constants: ConstantsService,
        public cphPipe: CphPipe,
        public gridHelpers: AGGridMethodsService,
        public router: Router,
    ) { }

    ngOnInit(): void {
        this.chartRefreshEmitter = new EventEmitter();
        
        if (this.router.url === '/stockDashboard') {
            this.isStockInsightPage = true;
            this.isLeavingVehiclePage = false
        } else if (this.router.url === '/soldDashboard') {
            this.isStockInsightPage = false;
            this.isLeavingVehiclePage = true;
        }



        this.buildTableRows();
        
        if(this.tileType=='Table' || this.tileType === 'DonutChart'){
            this.initGrid();
        }
        
        this.subscription = this.pageParams.updateThisTile.subscribe(res => {
            this.buildTableRows();
            this.updateGrid();
            this.chartRefreshEmitter.emit(true)
        })

    }

    ngOnDestroy() {
        if (!!this.subscription) { this.subscription.unsubscribe() }
    }

    isHighlightsChosen() {
        let choice = this.userChoice();
        if(!choice){
            console.error('failed finding ',this.fieldName)
        }
        return this.userChoice().ChosenValues.length > 0
    }

    clearHighlights() {
        let choice = this.pageParams.highlightChoices.find(x => x.FieldName === this.fieldName);
        choice.ChosenValues = [];
        this.pageParams.highlightChoiceHasBeenMade.emit();
    }

    isFilterChosen() {
        let choice = this.filterChoice();
        if (!choice) {
            console.error('failed finding ',this.fieldName)
        }
        return this.filterChoice().ChosenValues.length > 0
    }

    clearFilter() {
        let choice = this.pageParams.filterChoices.find(x => x.FieldName === this.fieldName);
        choice.ChosenValues = [];
        this.pageParams.filterChoiceHasBeenMade.emit();
    }

    buildTableRows() {

        this.tableRows = this.pageParams.parentMethods.buildRows(this.fieldName, this.dataType, this.tileType)

        if(this.itemsToExclude?.length>0){
            this.tableRows = this.tableRows.filter(x=>!this.itemsToExclude.includes(x.Label))
        }

        this.maxValue = this.tableRows.map(x => x.FilteredTotal).sort((a, b) => b - a)[0];

        if(this.title == this.constants.translatedText.OriginType)
        {
            this.tableRows.forEach(element => {
                if(element.Label == 'Book')
                {
                    element.Label = this.constants.translatedText.Book;
                }
            });

        }  

        //if percent, now go over again and turn into percents
        let filteredTotal = this.constants.sum(this.tableRows.map(x => x.FilteredTotal));
        let highlightedTotal = this.constants.sum(this.tableRows.map(x => x.HighlightedTotal));
        if (this.tileType === 'VerticalBarPercent') {
            this.tableRows.map(row => {
                row.FilteredTotal = this.constants.div(row.FilteredTotal, filteredTotal)
                row.HighlightedTotal = this.constants.div(row.HighlightedTotal, highlightedTotal)
            })

            this.maxValue = 1
        
        }

    // Order by specific order for advances
        if(this.fieldName == 'Advance')
        {
            var sortOrder = ['PURG', 'ARA', 'TCON','CI','EXPE','MADC','FAB','FICE','M','M+1','M+2','AVAF','NAF'];
            this.applyCustomSort(this.tableRows,sortOrder);
        }

        if(this.fieldName == 'AgeBandAtEom')
        {
            var sortOrder = ['180+','90-180','60-90','30-60','0-30'];
            this.applyCustomSort(this.tableRows,sortOrder);
        }

    }

    private applyCustomSort(tableRows: VNTileTableRow[], sortOrder: string[]) {
        var ordering = {}; // map for efficient lookup of sortIndex
        for (var i = 0; i < sortOrder.length; i++) {
            ordering[sortOrder[i]] = i;
        }
        tableRows = tableRows.sort(function (a, b) {
            return (ordering[a.Label] - ordering[b.Label]) || a.Label.localeCompare(b.Label);
        });
    }

    isItemSelected(item: string) {
        return this.userChoice().ChosenValues.length === 0 || this.userChoice().ChosenValues.includes(item)
    }

    userChoice(): DashboardMeasure {
        let choice = this.pageParams.highlightChoices.find(x => x.FieldName === this.fieldName);
        if(!choice){
            console.error('error finding ',this.fieldName)
        }
        return choice;
    }

    filterChoice(): DashboardMeasure {
        let choice = this.pageParams.filterChoices.find(x => x.FieldName === this.fieldName);
        if(!choice){
            console.error('error finding ',this.fieldName)
        }
        return choice;
    }



    highlightRow(row: VNTileTableRow) {
        this.pageParams.parentMethods.highlightRow(row, this.fieldName);
        this.pageParams.highlightChoiceHasBeenMade.emit();
    }

    updateGrid() {
        if (!!this.gridApi) {
            setTimeout(() => {
                this.gridApi.setRowData(this.tableRows)
            }, 60)
        }
    }

    initGrid() {
        const gridScaleValue = this.contentHolder.nativeElement.clientWidth / 210

        this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpers.getContextMenuItems(params),
            getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
            suppressPropertyNamesCheck: true,
            context: { thisComponent: this },
            getRowHeight: (params) => {
                return 25
            },
            rowClassRules: { 'highlighted': (params) => this.isHighlighted(params) },
            onRowClicked: (params) => { this.highlightRow(params.data) },
            //onAnimationQueueEmpty:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
            defaultColDef: {
                resizable: true,
                sortable: true,
                filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpers.getFilterListItemHeight() }, autoHeight: true,
                floatingFilter:true,
            },
            columnTypes: {
                date: { cellClass: "agAlignCenter", cellRenderer: (params) => this.cphPipe.transform(params.value, "dateMed", 0), },
                label: { cellClass: "agAlignLeft", filter: "agTextColumnFilter" },
                number: { cellClass: "ag-right-aligned-cell", filter: "agNumberColumnFilter", cellRenderer: (params) => this.cphPipe.transform(params.value, "number", 0) },
                percent: { cellClass: "ag-right-aligned-cell", filter: "agNumberColumnFilter", cellRenderer: (params) => this.cphPipe.transform(params.value, "percent", 0) },
                boolean: { cellClass: "agAlignCenter", filter: "agTextColumnFilter" },
            },
            //domLayout: 'autoHeight',

            rowData: this.tableRows,
            columnDefs: [],
            getRowClass: (params) => {
                if (params.data.Description == 'Total Sites') {
                    return 'total';
                }
            }

        }

        this.mainTableGridOptions.columnDefs = [

            { headerName: "", colId: "Label", field: 'Label', width: 50 * gridScaleValue, type: "label", },
            //for normal 
            { headerName: "Total", colId: "FilteredTotal", hide: this.tileType == 'DonutChart' || this.tileType == 'DonutChartFlex', field: 'FilteredTotal', width: 75 * gridScaleValue, type: "number", },
            { headerName: this.constants.translatedText.Selected, colId: "HighlightedTotal", hide: this.tileType == 'DonutChart' || this.tileType == 'DonutChartFlex', field: 'HighlightedTotal', width: 75 * gridScaleValue, type: "number", },
            //for when in donut tile
            { headerName: "% total", colId: "Percentage", hide: this.tileType !== 'DonutChart' && this.tileType !== 'DonutChartFlex', valueGetter: (params) => this.getFilterPercentage(params), width: 50 * gridScaleValue, type: "percent", },
            { headerName: "% " + this.constants.translatedText.Highlighted.toLowerCase(), colId: "Percentage", hide: this.tileType !== 'DonutChart' && this.tileType !== 'DonutChartFlex', valueGetter: (params) => this.getHighlightPercentage(params), width: 50 * gridScaleValue, type: "percent", },

        ]
    }
    getHighlightPercentage(params: ValueGetterParams): any {
        let row: VNTileTableRow = params.data;
        return this.constants.div(row.HighlightedTotal, this.constants.sum(this.tableRows.map(x => x.HighlightedTotal)))
    }
    getFilterPercentage(params: ValueGetterParams): any {
        let row: VNTileTableRow = params.data;
        return this.constants.div(row.FilteredTotal, this.constants.sum(this.tableRows.map(x => x.FilteredTotal)))
    }

    isHighlighted(params: any): boolean {
        let row: VNTileTableRow = params.data;
        return this.userChoice().ChosenValues.includes(row.Label);
    }


   

    onGridReady(params) {
        this.gridApi = params.api;
        if (this.gridApi.getDisplayedRowCount() == 0) {
            setTimeout(() => {
                this.updateGrid()
            }, 100)
        }
        this.gridApi.sizeColumnsToFit();
    }

    barHeight() {
        return this.constants.div((this.contentHolder.nativeElement.clientHeight), this.tableRows.length) - 3; // -3 compensate margin between rows
    }

    barWidth() {
        return this.constants.div((this.contentHolder.nativeElement.clientWidth - 80), this.tableRows.length);
    }

    tableRowValues() {
        return this.tableRows.map(x => x.HighlightedTotal)
    }

    tableRowLabels() {
        return this.tableRows.map(x => x.Label)
    }

    trackByFunction(index: number) { return index; }
}
