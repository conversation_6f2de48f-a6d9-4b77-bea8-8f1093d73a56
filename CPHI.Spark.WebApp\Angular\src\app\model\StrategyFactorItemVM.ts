

export class StrategyFactorItemVM {

    constructor(strategyFactorId:number, label:string,value:number,horizontalBands?:FactorItemHorizontalBand[], isReadOnly?:boolean) {
        this.Id=null;
        this.StrategyFactorId=strategyFactorId;
        this.Comment = null;
        this.Label = label;
        this.Value = value;

        if(horizontalBands){this.horizontalBands = [];
            horizontalBands.forEach(band=>{
                this.horizontalBands.push(band)
            })
        }
        this.IsReadOnly = isReadOnly;
        // if(competitorRadius){this.competitorRadius = competitorRadius}
        // if(competitorCount){this.competitorCount = competitorCount}
    }


    Id: number;
    StrategyFactorId: number;
    Comment: string;
    Label: string;
    Value: number;
    BoolValue: boolean | null = null;


    horizontalBands: FactorItemHorizontalBand[];
    IsReadOnly: boolean | null = null;

    //competitorRadius:number;
    //competitorCount:number;
    
    // achieved:number;
    // impact:number;

    
    //Just used during reconstructing the matrix from a flat list of items
    retailRating?:number;
    retailRatingString?:string;
    daysListed?:number
    daysListedString?:string

    //other things we select which we will combine later to store
    selectedPreviousOwners?: string;
    selectedAgeCategory?: string;
    selectedMake?: string;
    selectedFuelType?: string;
    selectedAgeBand?: string;



}

export interface FactorItemHorizontalBand{
    id:number;
    value:number
}
