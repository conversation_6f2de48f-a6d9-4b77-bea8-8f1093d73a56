﻿using CPHI.Spark.WebScraper.Jobs;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Quartz;
using Quartz.Impl;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;






namespace CPHI.Spark.WebScraper.Scheduling
{
    public class SchedulerService : BackgroundService
    {


        private readonly ILogger<SchedulerService> _logger;
        private StdSchedulerFactory _schedulerFactory;
        private CancellationToken _stopppingToken;
        private IScheduler _scheduler;


        public SchedulerService(ILogger<SchedulerService> logger)
        {
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await StartJobs();
            _stopppingToken = stoppingToken;
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
            await _scheduler.Shutdown();
        }



        private async Task StartJobs()
        {
            _schedulerFactory = new StdSchedulerFactory();

            _scheduler = await _schedulerFactory.GetScheduler();
            await _scheduler.Start();


            if (ConfigService.ForceRunThisJobNowOnly != "")
            {

                string hours = DateTime.Now.AddSeconds(5).Hour.ToString();
                string minutes = DateTime.Now.AddSeconds(5).Minute.ToString();
                string seconds = DateTime.Now.AddSeconds(5).Second.ToString();
                string runIn5SecondsSchedule = $"CUSTOM {seconds} {minutes} {hours} ? * * *";

                Type job = Type.GetType($"CPHI.Spark.WebScraper.Jobs.{ConfigService.ForceRunThisJobNowOnly}");
                _logger.LogInformation($"Running job {ConfigService.ForceRunThisJobNowOnly} now, not scheduling any jobs");
                await scheduleJob(JobBuilder.Create(job).Build(), runIn5SecondsSchedule);

            }
            else
            {
                await ScheduleRegularJobs();
            }
        }


        private async Task ScheduleRegularJobs()
        {
            //RRG
            await scheduleJob(JobBuilder.Create<VoCScrapeJob>().Build(), ConfigService.RRGVocSchedule);

            //Vindis
            await scheduleJob(JobBuilder.Create<DealScrapeIntradayJob>().Build(), ConfigService.VindisDealsIntradaySchedule);
            await scheduleJob(JobBuilder.Create<DealScrapeDailyJob>().Build(), ConfigService.VindisDealsDailySchedule);
            await scheduleJob(JobBuilder.Create<ActivityScrapeJob>().Build(), ConfigService.VindisActivitySchedule);
            await scheduleJob(JobBuilder.Create<GDPRScrapeJob>().Build(), ConfigService.VindisGDPRSchedule);
            await scheduleJob(JobBuilder.Create<EnquiriesScrapeJob>().Build(), ConfigService.EnquiriesScrapeJob);
            await scheduleJob(JobBuilder.Create<AuditScrapeJob>().Build(), ConfigService.VindisAuditSchedule);
            await scheduleJob(JobBuilder.Create<ModixUpdatePricesJob>().Build(), ConfigService.VindisModixSchedule);

            //Spain
            await scheduleJob(JobBuilder.Create<OrdersScrapeJob>().Build(), ConfigService.SpainOrdersSchedule);
            await scheduleJob(JobBuilder.Create<AlcopaScrapeJob>().Build(), ConfigService.SpainAlcopaSchedule);
            await scheduleJob(JobBuilder.Create<EserpubliScrapeJob>().Build(), ConfigService.SpainEserpubliSchedule);

            //SpainPhysical
            await scheduleJob(JobBuilder.Create<DistrinetScrapeJob>().Build(), ConfigService.DistrinetSchedule);

            //Pinewood
            await scheduleJob(JobBuilder.Create<PinewoodUpdatePriceJob>().Build(), ConfigService.PinewoodSchedule);
        }

        private async Task scheduleJob(IJobDetail job, string requestedSchedule)
        {
            try
            {

                if (requestedSchedule == "DO NOT RUN") { return; }


                //if we do something custom
                else if(requestedSchedule.StartsWith("CUSTOM "))
                {
                    string cronSchedule = requestedSchedule.Replace("CUSTOM ", "");
                    await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(cronSchedule).Build(), _stopppingToken);
                    return;
                }

                //if we want to run regularly
                else if (requestedSchedule.StartsWith("REPEAT EVERY"))
                {
                    string cronSchedule = "0 10 * ? * * *"; //set to 10mins by default, should never use this
                    if (requestedSchedule.Contains("MINUTES"))
                    {
                        int minutes = int.Parse(new String(requestedSchedule.Where(Char.IsDigit).ToArray()));
                        cronSchedule = $"0 0/{minutes} * ? * * *";
                    }
                    else if (requestedSchedule.Contains("SECONDS"))
                    {
                        int seconds = int.Parse(new String(requestedSchedule.Where(Char.IsDigit).ToArray()));
                        cronSchedule = $"0/{seconds} * * ? * * *";
                    }
                    await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(cronSchedule).Build(), _stopppingToken);
                    return;
                }

                //if we chose a certain time
                else if (requestedSchedule.StartsWith("RUN AT"))
                {
                    string timeChosen = requestedSchedule.Substring(7, requestedSchedule.Length - 7);
                    string hours = timeChosen.Split(':')[0];
                    string minutes = timeChosen.Split(':')[1];
                    string seconds = timeChosen.Split(':')[2];
                    string cronSchedule = $"{seconds} {minutes} {hours} ? * * *";
                    await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(cronSchedule).Build(), _stopppingToken);
                    return;
                }

            }
            catch
            {
                { }
            }
        }



        

    }
}
