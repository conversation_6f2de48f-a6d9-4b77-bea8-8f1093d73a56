import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { DatePickerModalComponent } from 'src/app/components/datePickerModal/datePickerModal.component';
import { DealDetailsComponent } from 'src/app/components/dealDetails/dealDetails.component';
import { DealInputModalComponent } from 'src/app/components/dealInputModal/dealInputModal.component';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { localeEs } from 'src/environments/locale.es.js';
import { OrderbookRow } from "../../model/OrderbookRow";
import { Day, ExcelChoices, ExcelReportNames, Month, OrderbookTimePeriod, ProfilePicSize, SiteVM, Week } from '../../model/main.model';
import { LateCostOption, OrderOption } from '../../model/sales.model';
import { OrderBookService } from './orderBook.service';
import { OrderBookColumnService } from './orderBookColumn.service';
import { OrderBookExcelExportService } from './orderBookExcelExport.service';
import { OrderBookGetDataService } from './orderBookGetData.service';
import { GetContextMenuItemsParams, GetMainMenuItemsParams, MenuItemDef } from 'ag-grid-community';
import { StockPricingSitesOverview } from 'src/app/model/StockPricingSitesOverview';
import { CustomiseColumnsModalComponent } from 'src/app/components/customiseColumnsModal/customiseColumnsModal.component';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';


interface quickSearch {
  value: string;
  description: string;
}

@Component({
  selector: 'app-orderBook',
  templateUrl: './orderBook.component.html',
  styleUrls: ['./orderBook.component.scss']
})


export class OrderBookComponent implements OnInit {
  @ViewChild('topSection', { static: true }) topSection: ElementRef;
  @ViewChild('gridHolder', { static: true }) gridHolder: ElementRef;
  timePeriod = OrderbookTimePeriod; //this is a reference to an enum, cannot be removed

  months: Array<Month>;
  weeks: Array<Week>;
  days: Array<Day>;
  quickSearches: quickSearch[];
  viewPortHeight: number;
  gridOptions: GridOptionsCph;
  gridHeight: number;
  deliveryOptions: string[];
  searchTerm: UntypedFormControl;
  profilePicSize: ProfilePicSize;
  subscriptionComments: Subscription;
  subscriptionToTodayDealsClick: Subscription;

  constructor(
    public service: OrderBookService,
    private orderBookColServie: OrderBookColumnService,
    public excelExportService: OrderBookExcelExportService,
    private getDataService: OrderBookGetDataService,
    private agGridMethods: AGGridMethodsService
  ) {}


  ngOnInit() {

    this.profilePicSize = ProfilePicSize.small;

    //set height of grid
    this.viewPortHeight = this.getScreenSize();
    this.gridHeight = this.viewPortHeight - this.topSection.nativeElement.offsetHeight - 40;
    

    // If service isn't already initiated, do that
    if (!this.service.accountingDate) {
      this.service.initOrderbook()
    }


    this.initParams()
    this.buildSubscriptions();
    this.getDataService.getData()

    this.service.orderBookComponent = this;
  }

  ngOnDestroy() {
    if (this.subscriptionComments) { this.subscriptionComments.unsubscribe() }
    if (this.subscriptionToTodayDealsClick) { this.subscriptionToTodayDealsClick.unsubscribe() }
    if(this.service.tableLayoutManagementParams){
      this.service.tableLayoutManagementParams.gridApi = null;
      this.service.tableLayoutManagementParams.gridColumnApi = null;
      this.service.tableLayoutManagementParams.originalColDefs = null;
    }
    this.service.rows = null;
  }


  @HostListener('window:resize', ['$event'])
  getScreenSize(event?) {
    return window.innerHeight;
  }



  buildSubscriptions() {
    this.subscriptionComments = this.service.selections.commentsChanged.subscribe(res => {
      this.getDataService.getData();
    })

    this.subscriptionToTodayDealsClick = this.service.selections.clickedTodayOrders.subscribe(res => {
      this.getDataService.getData();
    })
  }


  onResize(event) {
    this.service.selections.screenWidth = window.innerWidth;
    this.service.selections.screenHeight = window.innerHeight;
    if (this.service.tableLayoutManagementParams.gridApi) {
      this.service.tableLayoutManagementParams.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  openDatePickerModal(isOrderDates: boolean) {
    const modalRef = this.service.modalService.open(DatePickerModalComponent);
    //I give to modal
    modalRef.componentInstance.heading = `Choose ${isOrderDates ? 'Order' : 'Delivery'} Dates`;

    if (isOrderDates ) {
      modalRef.componentInstance.fromDate = this.service.orderDate.startDate.toISOString().split('T')[0]
      modalRef.componentInstance.toDate = this.service.orderDate.endDate.toISOString().split('T')[0]
    }

    if (!isOrderDates ) {
      modalRef.componentInstance.fromDate = this.service.accountingDate.startDate.toISOString().split('T')[0]
      modalRef.componentInstance.toDate = this.service.accountingDate.endDate.toISOString().split('T')[0]
    }

    modalRef.result.then((result) => { //I get back from modal
      if (result) {
        if (isOrderDates) {
          this.service.orderDate.startDate = result.startDate;
          this.service.orderDate.endDate = result.endDate;
          this.service.orderDate.timePeriod = OrderbookTimePeriod.Custom;
          this.getDataService.getData();
        } else {
          this.service.accountingDate.startDate = result.startDate;
          this.service.accountingDate.endDate = result.endDate;
          this.service.accountingDate.timePeriod = OrderbookTimePeriod.Custom;
          this.getDataService.getData();
        }
      }
    });
  }


  resizeGrid() {
    let width = this.gridHolder.nativeElement.offsetWidth;

    if (this.service.tableLayoutManagementParams.gridApi) {
      this.service.tableLayoutManagementParams.gridColumnApi.sizeColumnsToFit(width - 20);
    }
  }

  initParams() {

    this.deliveryOptions = ['All', 'Delivered', 'Undelivered']
    this.searchTerm = new UntypedFormControl();

    if (!this.service.selections.selectedSites) {
      this.service.selections.selectedSites = this.service.constants.Sites
    }


    this.quickSearches = [
      { value: 'highMargin', description: this.service.constants.translatedText.Orderbook_OrdersOver2k },
      { value: 'losingMoney', description: this.service.constants.translatedText.Orderbook_OrdersLosingMoney },
      { value: null, description: this.service.constants.translatedText.Orderbook_EmailForSupport },
    ]
  }

  public updateFilterWith(quickSearch: quickSearch) {
    if (!quickSearch.value) { return }
    this.service.specialFiltersMenuItemChosen = quickSearch.value;
    this.getDataService.getData();
  }

  public clearFilter() {
    this.service.specialFiltersMenuItemChosen = '';
    this.getDataService.getData();
  }

  public clearSearchTerm() {
    this.searchTerm.setValue("");
    this.service.tableLayoutManagementParams.gridApi.setQuickFilter(this.searchTerm.value)

  }

  public setDeliveryFilterTo(option: string) {
    this.service.deliveryOption = option;
    this.getDataService.getData();
  }

  // refilterData() {
  //   this.service.rowsFiltered = this.service.filterForUserChoices(this.service.rows);
  // }







  public getDetailedExcel() {
    let dealIds: number[] = [];
    this.service.tableLayoutManagementParams.gridApi.forEachNodeAfterFilter(node => {
      if (!!node.data) {
        dealIds.push(node.data.DealId)
      }
    })

    let params: ExcelChoices = {
      DealIds: dealIds,
      SheetName: "Orderbook",
      WeekStart: null
    }

    this.getDataService.getDataMethods.getDetailedExcel(ExcelReportNames.DetailedOrderbook, params)
  }

  public highlightIfDateTypeIs(type: string) {
    return this.service.accountingDate?.dateType == type;// 'accounting'
  }

 



  initiateGrid() {

    //let baseHeight = window.innerWidth > 1550 ? 4 : 0
    let gridScaleValue = this.gridHolder.nativeElement.clientWidth / 1920; //actual measurement 1310.  Added 40 for padding.

    this.gridOptions = {
      getContextMenuItems: (params) => this.getContextMenuItems(params),
      //getMainMenuItems:(params)=>this.getMainMenuItems(params),
      getLocaleText: (params) =>  this.service.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      onCellDoubleClicked: (params) => this.showDealDetails(params),
      onFilterChanged: () => this.onFilterChanged(),
      rowData: this.service.rowsFiltered,
      getRowHeight: (params) => { return this.agGridMethods.getStandardHeight()},
      
      onGridReady: (params) => this.onGridReady(params),
      defaultColDef: {
        resizable: true,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.agGridMethods.getFilterListItemHeight() },
      },
      columnTypes: this.service.columnTypesService.provideColTypes([]) as any,
      columnDefs: this.orderBookColServie.provideColDefs(gridScaleValue),
      

    }

  }
  getMainMenuItems(params: GetMainMenuItemsParams<any, any>): (string | MenuItemDef)[] {
    return []
  }
 

  getContextMenuItems(params: GetContextMenuItemsParams<any, any>): (string | MenuItemDef)[] {
    const selectedNodeIds: string[] = this.service.tableLayoutManagementParams.gridApi.getSelectedNodes().map(x => x.id);
    const row: StockPricingSitesOverview = params.node.data;
    if (!selectedNodeIds.includes(params.node.id)) {
      this.service.tableLayoutManagementParams.gridApi.forEachLeafNode(node => {
        node.setSelected(node.id === params.node.id);
      })
    }

    let result = this.service.agGridMethodsService.getContextMenuItems(params, false);
    result = result.concat(this.service.tableLayoutManagementService.getTableContextMenuItems());
    result.push('separator')
    result.push(
      {
        name: 'Customise Columns',
        action: () => {
          this.openCustomiseColumnModal()
        }
      }
    )

    

    return result;
  }
  

    openCustomiseColumnModal() {
      const modal = this.service.modalService.open(CustomiseColumnsModalComponent, { size: 'lg' });
      const modalComponent:CustomiseColumnsModalComponent = modal.componentInstance;
      modalComponent.gridApi = this.service.tableLayoutManagementParams.gridApi;
      modalComponent.originalColDefs = this.service.tableLayoutManagementParams.originalColDefs;
      modalComponent.sectionOrdering  = [
        'Site',
        'Vehicle Information',
        'Order Information',
        'Dates',
        'Progress',
        'Profit'
      ];
  
      modal.result.then(res => {
        const allCols = this.service.tableLayoutManagementParams.gridColumnApi.getAllGridColumns();
        const updatedCols = allCols.map(col => {
          const isVisible = !!res.find(x => x.colId === col.getColId());
          return { ...col.getColDef(), hide: !isVisible };
        });
  
        this.service.tableLayoutManagementParams.gridApi.setColumnDefs(updatedCols);
  
        const state = res.map((col, index) => {
          return {
            colId: col.colId,
            order: index
          };
        });
  
        this.service.tableLayoutManagementParams.gridColumnApi.applyColumnState({
          state: state,
          applyOrder: true
        });
  
  
      }, () => {
  
      });
    }
  


 


  onFilterChanged(): void {
    this.service.summariseDeals();
  }


  onGridReady(params) {

    this.service.tableLayoutManagementParams.gridApi = params.api;
    this.service.tableLayoutManagementParams.gridColumnApi = params.columnApi;
    // this.service.tableLayoutManagement.gridApi.sizeColumnsToFit();
    this.gridOptions.context = { thisComponent: this };

    //reapply table col state
    if (this.service.tableLayoutManagementParams.loadedTableState) {
      this.service.tableLayoutManagementParams.gridColumnApi.applyColumnState({ state: this.service.tableLayoutManagementParams.loadedTableState });
      this.service.tableLayoutManagementParams.gridApi.setFilterModel(this.service.tableLayoutManagementParams.filterModel)
    }

    //reapply filter model
    if (this.service.tableLayoutManagementParams.filterModel) {
      this.service.tableLayoutManagementParams.gridApi.setFilterModel(this.service.tableLayoutManagementParams.filterModel)
      this.service.tableLayoutManagementParams.gridApi.onFilterChanged();
    }else {
      //this.service.tableLayoutManagementParams.gridApi.setFilterModel(this.service.defaultFilterState)
      this.service.tableLayoutManagementParams.gridApi.onFilterChanged();
    }

    this.service.tableLayoutManagementService.parent = this.service.tableLayoutManagementParams;
    this.service.tableLayoutManagementService.initParams();


    this.service.tableLayoutManagementParams.gridApi = params.api;
    this.service.tableLayoutManagementParams.gridColumnApi = params.columnApi;
    this.searchTerm.valueChanges.subscribe(value => {
      this.service.tableLayoutManagementParams.gridApi.setQuickFilter(this.searchTerm.value)
    })

    let gridScaleValue = this.gridHolder.nativeElement.clientWidth / 1920; //actual measurement 1310.  Added 40 for padding.
    
    this.service.tableLayoutManagementService.initParams()
    
    this.service.summariseDeals()
    this.resizeGrid();
    this.service.tableLayoutManagementParams.originalColDefs = this.orderBookColServie.provideColDefs(gridScaleValue)
  }




  showDealDetails(params: any): void {

    if (params.colDef.headerName == this.service.constants.translatedText.Comments) { return }

    this.service.selections.triggerSpinner.next({ show: true, message: this.service.constants.translatedText.Loading })

    setTimeout(() => {
      const modalRef = this.service.modalService.open(DealDetailsComponent);
      //I give to modal
      modalRef.componentInstance.givenDealId = params.data.DealId;
      modalRef.result.then((result) => { //I get back from modal
        if (result) {
          //thing
        }
      });
    }, 10)
  }


  updateAccountingDatesIfNeeded() {
    if (this.service.accountingDate.timePeriod === OrderbookTimePeriod.Anytime) {
      this.service.accountingDate.startDate = this.service.constants.addYears(this.service.orderDate.startDate, -1);
      this.service.accountingDate.endDate = this.service.constants.addYears(this.service.constants.todayStart, 2);
    }
  }


  updateOrderDatesIfAnytimeChosen() {
    if (this.service.orderDate.timePeriod === OrderbookTimePeriod.Anytime) {
      this.service.orderDate.startDate = this.service.constants.addYears(this.service.constants.todayStart, -4);// this.service.deliveryDate.startDate;
      this.service.orderDate.endDate = this.service.constants.endOfDay(new Date()) ;// this.service.constants.addYears(this.service.constants.todayStart, 5);
    }
  }

  //Order Date Stuff
  public selectOrderAnytime() {
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Anytime;
    this.updateOrderDatesIfAnytimeChosen();
    this.getDataService.getData();
  }

  public selectOrderMonth(date: Date) {
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Month;
    this.service.orderDate.lastChosenMonthStart = date;
    this.service.orderDate.startDate = date;
    this.service.orderDate.endDate = this.service.constants.endOfMonth(date)

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getDataService.getData();
  }

  public selectOrderWeek(date: Date) {
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Week;
    this.service.orderDate.lastChosenWeekStart = date;
    this.service.orderDate.startDate = date;
    this.service.orderDate.endDate = this.service.constants.endOfWeek(date)

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getDataService.getData();
  }

  public selectOrderDay(date: Date) {
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Day;
    this.service.orderDate.lastChosenDay = date;
    this.service.orderDate.startDate = date;
    this.service.orderDate.endDate = date;

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getDataService.getData();
  }

  public changeOrderDay(changeAmount: number) {
    this.service.orderDate.startDate = this.service.constants.addDays(this.service.orderDate.lastChosenDay, changeAmount);
    this.service.orderDate.lastChosenDay = this.service.orderDate.startDate;
    this.service.orderDate.endDate = this.service.orderDate.startDate;
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Day;

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getDataService.getData();
  }

  public changeOrderWeek(changeAmount: number) {
    this.service.orderDate.startDate = this.service.constants.addDays(this.service.orderDate.lastChosenWeekStart, changeAmount * 7);
    this.service.orderDate.endDate = this.service.constants.endOfWeek(this.service.orderDate.startDate);
    this.service.orderDate.lastChosenWeekStart = this.service.orderDate.startDate;
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Week;

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getDataService.getData();
  }

  public changeOrderMonth(changeAmount: number) {
    this.service.orderDate.startDate = this.service.constants.addMonths(this.service.orderDate.lastChosenMonthStart, changeAmount);
    this.service.orderDate.endDate = this.service.constants.endOfMonth(this.service.orderDate.startDate);
    this.service.orderDate.lastChosenMonthStart = this.service.orderDate.startDate;
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Month;

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getDataService.getData();
  }



  //Delivery Date Stuff
  public selectAccountingAnytime() {
    this.service.accountingDate.timePeriod = OrderbookTimePeriod.Anytime;
    this.updateAccountingDatesIfNeeded();
    this.getDataService.getData();
  }

  public selectAccountingMonth(date: Date, name: string) {

    if (name != this.service.constants.translatedText.FinanceAddons_YearToDate && name != this.service.constants.translatedText.FinanceAddons_FullPriorYear) {
      this.service.accountingDate.timePeriod = OrderbookTimePeriod.Month;
      this.service.accountingDate.lastChosenMonthStart = date;
      this.service.accountingDate.lastChosenMonthName = this.service.cphPipe.transform(date, "month", 0);
      this.service.accountingDate.startDate = date;
      this.service.accountingDate.endDate = this.service.constants.endOfMonth(date)
    }
    else {

      if (name == this.service.constants.translatedText.FinanceAddons_YearToDate) {
        this.service.accountingDate.lastChosenMonthStart = date;
        this.service.accountingDate.lastChosenMonthName = name;
        this.service.accountingDate.startDate = date;
        this.service.accountingDate.endDate = new Date();
      }
      else {
        this.service.accountingDate.lastChosenMonthStart = date;
        this.service.accountingDate.lastChosenMonthName = name;
        this.service.accountingDate.startDate = date;
        this.service.accountingDate.endDate = new Date(new Date().getFullYear(), 0, 1)
      }

    }


    //also have to change the orderDate if anytime chosen
    this.updateOrderDatesIfAnytimeChosen();
    this.getDataService.getData();

  }

  public selectAccountingDay(date: Date) {
    this.service.accountingDate.timePeriod = OrderbookTimePeriod.Day;
    this.service.accountingDate.lastChosenDay = date;
    this.service.accountingDate.startDate = date;
    this.service.accountingDate.endDate = date;

    //also have to change the orderDate if anytime chosen
    this.updateOrderDatesIfAnytimeChosen();
    this.getDataService.getData();
  }

  public changeAccountingDay(changeAmount: number) {

    this.service.accountingDate.startDate = this.service.constants.addDays(this.service.accountingDate.lastChosenDay, changeAmount);
    this.service.accountingDate.lastChosenDay = this.service.accountingDate.startDate;

    this.service.accountingDate.endDate = this.service.accountingDate.startDate
    this.service.accountingDate.timePeriod = OrderbookTimePeriod.Day;

    //also have to change the orderDate if anytime chosen
    this.updateOrderDatesIfAnytimeChosen();
    this.getDataService.getData();
  }

  public changeAccountingMonth(changeAmount: number) {
    this.service.accountingDate.startDate = this.service.constants.addMonths(this.service.accountingDate.lastChosenMonthStart, changeAmount);
    this.service.accountingDate.endDate = this.service.constants.endOfMonth(this.service.accountingDate.startDate);
    this.service.accountingDate.lastChosenMonthStart = this.service.accountingDate.startDate;
    this.service.accountingDate.lastChosenMonthName = this.service.cphPipe.transform(this.service.accountingDate.startDate, "month", 0);
    this.service.accountingDate.timePeriod = OrderbookTimePeriod.Month;

    //also have to change the orderDate if anytime chosen
    this.updateOrderDatesIfAnytimeChosen();
    this.getDataService.getData();
  }


  groupBy(arr, prop) {
    const map = new Map(Array.from(arr, obj => [obj[prop], []]));
    arr.forEach(obj => map.get(obj[prop]).push(obj));
    return Array.from(map.values());
  }


  public generatePeopleSummary(isSalesExec: boolean): void {

    let groupId = ''
    let groupName = ''
    let summaryName = ''
    if (isSalesExec) { groupId = 'SalesmanId', groupName = 'SalesmanName', summaryName = 'salesExecSummary' }
    else {groupId = 'ManagerId', groupName = 'ManagerName', summaryName = 'salesManagerSummary'}

    let groupedByExec: OrderbookRow[][] = this.groupBy(this.service.rowsFiltered, groupName);
    
    this.service[summaryName] = []
    groupedByExec.forEach(item => {
      let unitCount = this.service.constants.sum(item.map(x => x.Units));
      let productsCount = this.service.constants.sum(item.map(x => x.TotalProductCount));
      let profit = this.service.constants.sum(item.map(x => x.TotalProfit));
      this.service[summaryName].push({
        name: item[0][groupName],
        salesmanId: item[0][groupId],
        dealCount: item.length,
        products: productsCount,
        units: unitCount,
        profit: profit,
        productsPU: this.service.constants.div(productsCount, unitCount),
        profitPU: this.service.constants.div(profit, unitCount)
      })
    })

    this.service[summaryName].sort((a, b) => a.name.localeCompare(b.name));


  }

  public onUpdateSites(sites: SiteVM[]) {
    // Global stuff
    this.service.selections.selectedSites = sites;
    this.service.selections.selectedSitesIds = [];

    sites.forEach(site => {
      this.service.selections.selectedSitesIds.push(site.SiteId);
    });

    this.getDataService.getData();
  }



  public onUpdateOrderTypes(orderTypes: string[]) {
    this.service.orderTypeTypes = orderTypes;
    this.getDataService.getData();
  }
  public onUpdateFranchises(franchises: string[]) {
    this.service.franchises = franchises
    this.getDataService.getData();
  }

  public onUpdateVehicleTypes(vehicleTypes: string[]) {
    this.service.vehicleTypeTypes = vehicleTypes
    this.getDataService.getData();
  }

  public onUpdateVehicleTypesSpain(vehicleType: string) {
    if (this.service.vehicleTypeTypes.find(x => x == vehicleType)) {
      this.service.vehicleTypeTypes = this.service.vehicleTypeTypes.filter(x => x != vehicleType);
    } else {
      this.service.vehicleTypeTypes.push(vehicleType);
    }

    this.getDataService.getData();
  }


  public selectLateCostOption(lateCostOption: LateCostOption) {
    this.service.lateCostOption = lateCostOption;
    this.getDataService.getData();
  }

  public selectOrderOption(orderOption: OrderOption) {
    this.service.orderOption = orderOption;
    this.getDataService.getData();
  }

  public selectSalesExec(salesExecName: string, salesExecId: number) {
    this.service.salesExecName = salesExecName;
    this.service.salesExecId = salesExecId
    this.getDataService.getData();
  }

  public selectManagerExec(salesExecName: string, salesExecId: number) {
    this.service.salesManagerName = salesExecName;
    this.service.salesManagerId = salesExecName == 'UNASSIGNED' ? -1 : salesExecId;
    this.getDataService.getData();
  }

  public makeMonths() {
    this.months = this.service.constants.makeMonths(0, 0)

    let YTD: Month = {
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(),
      name: this.service.constants.translatedText.FinanceAddons_YearToDate
    }

    let FullPriorYear: Month = {
      startDate: new Date(new Date().getFullYear() - 1, 0, 1),
      endDate: new Date(new Date().getFullYear(), 0, 1),
      name: this.service.constants.translatedText.FinanceAddons_FullPriorYear
    }

    this.months.push(YTD);
    this.months.push(FullPriorYear);
  }

  public makeWeeks() {
    this.weeks = this.service.constants.makeWeeks(0, 0);
  }

  public makeDays() {
    this.days = this.service.constants.makeDays(0, -8, 4, 0);
  }

  public selectAccountingDateType(dateType: string) {
    this.service.accountingDate.dateType = dateType;
    this.getDataService.getData()
  }
  public launchDealInputModal() {
    const modalRef = this.service.modalService.open(DealInputModalComponent);
    modalRef.result.then((result) => { });
  }
}
