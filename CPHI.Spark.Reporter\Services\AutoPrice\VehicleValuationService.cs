﻿using CPHI.Repository;
using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Reporter.Jobs.AutoPrice;
using CPHI.Spark.WebApp.DataAccess;
using Datadog.Trace;
using log4net;
using OfficeOpenXml.FormulaParsing.LexicalAnalysis;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class VehicleValuationService
   {
      private readonly IHttpClientFactory _httpClientFactory;
      private readonly HttpClient _httpClient;

      public VehicleValuationService(IHttpClientFactory httpClientFactory)
      {
         this._httpClient = httpClientFactory.CreateClient();
         this._httpClientFactory = httpClientFactory;
      }

      public async Task ValueVehicles(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         using (var parentScope = Tracer.Instance.StartActive("Start Vehicle Valuation Job"))
         {

            using (var childScope = Tracer.Instance.StartActive($"Check if anyone is valuing"))
            {
               bool anyoneValuing = await CheckIfAnyoneValuing(logger, dealerGroups);
               if (anyoneValuing)
               {
                  return;
               }

            }

            //nobody is valuing, so carry on
            using (var childScope = Tracer.Instance.StartActive($"Search for batches to value"))
            {
               foreach (Model.DealerGroupName dealerGroup in dealerGroups)
               {
                  try
                  {
                     await ValueVehiclesThisDealerGroup(logger, dealerGroup);
                  }
                  catch (Exception ex) { await EmailerService.LogException(ex, logger, "ValueVehicles"); }
               }
            }
         }
      }

      private async Task<bool> CheckIfAnyoneValuing(ILog logger, List<DealerGroupName> dealerGroups)
      {
         bool rrgIsValuing = await CheckIfValuing(ConfigService.GetConnectionString(DealerGroupName.RRGUK));
         if (rrgIsValuing)
         {
            return true;
         }


         bool vindisIsValuing = await CheckIfValuing(ConfigService.GetConnectionString(DealerGroupName.Vindis));
         if (vindisIsValuing)
         {
            return true;
         }


         bool autoPriceIsValuing = await CheckIfValuing(ConfigService.GetConnectionString(DealerGroupName.BrindleyGroup));
         if (autoPriceIsValuing)
         {
            return true;
         }


         return false;
      }

      private static async Task<bool> CheckIfValuing(string connStringRRG)
      {
         var rrgValuationsDA = new ValuationsDataAccess(connStringRRG);
         var rrgIsValuing = await rrgValuationsDA.CheckVehicleValuationsBatchInProgress();
         return rrgIsValuing;
      }

      private async Task ValueVehiclesThisDealerGroup(ILog logger, DealerGroupName dealerGroup)
      {
         //1. Setup
         string _connectionString = ConfigService.GetConnectionString(dealerGroup);

         var valuationsDataAccess = new ValuationsDataAccess(_connectionString);
         var valuationAuctionFeesDataAccess = new ValuationAuctionFeesDataAccess(_connectionString);
         var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var globalParamsDataAccess = new GlobalParamDataAccess(_connectionString);


         //4. Get first batch rows to process
         VehicleValuationBatch vehicleValuationBatchToProcess = await valuationsDataAccess.GetOldestVehicleValuationsBatchToProcess(dealerGroup);


         //5. Early return if no batches
         if (vehicleValuationBatchToProcess == null)
         {
            return;
         }



         ConcurrentDictionary<int, List<ATNewVehicleGet_Feature>> valuationIdOptions = new ConcurrentDictionary<int, List<ATNewVehicleGet_Feature>>();
         ConcurrentBag<VehicleValuationRatingBySite> vehicleValuationRatingBySites = new ConcurrentBag<VehicleValuationRatingBySite>();
         ConcurrentBag<VehicleValuationError> failedVehicleValuations = new ConcurrentBag<VehicleValuationError>();
         bool valueVehicleAtAllSites = false;
         IUserDataAccess userDataAccess = new UserDataAccess(_connectionString);
         int userId = vehicleValuationBatchToProcess.LastRunBy_Id; //person who made the request/ uploaded the file
         var userEmail = await userDataAccess.GetUserEmailAddress(userId, dealerGroup);
         var userRetailerSiteIds = await userDataAccess.GetUserRetailSiteIds(userId, dealerGroup);
         string userRetailerSitesIdsString = string.Join(',', userRetailerSiteIds);
         int attemptedCount = vehicleValuationBatchToProcess.VehicleValuations.Count();


         using (var childScope = Tracer.Instance.StartActive($"Carrying out valuation batch {vehicleValuationBatchToProcess.Id} for {dealerGroup}"))
         {

            //6. Else continue with the batch
            logger.Info("");
            logger.Info("----------------------------------------------------------------------------");
            logger.Info($"Carrying out valuation batch for {dealerGroup.ToString()}, starting...");

            valueVehicleAtAllSites = await globalParamsDataAccess.ValueVehicleAtAllSites(dealerGroup);

            //set batch to valuing
            await valuationsDataAccess.UpdateVehicleValuationsBatchStatus(vehicleValuationBatchToProcess.Id, BatchStatus.Valuing.ToString());

            DateTime runDate = DateTime.Now;
            List<RetailerSite> retailersWithStrategies = (await retailerSitesDataAccess.GetRetailerSitesWithBuyingStrategies(runDate, dealerGroup)).Where(x => x.IsActive).ToList();
            retailersWithStrategies = retailersWithStrategies.Where(x => userRetailerSiteIds.Contains(x.Id) && x.SkipBulkValuation == false).ToList();

            FetchAutoTraderService fetchAutoTraderService = new FetchAutoTraderService(HttpClientFactoryService.HttpClientFactory);
            AutoTraderFutureValuationsClient atFutureValuationsClient = new AutoTraderFutureValuationsClient(HttpClientFactoryService.HttpClientFactory,
               ConfigService.AutotraderApiKey,
               ConfigService.AutotraderApiSecret,
               ConfigService.AutotraderBaseURL);



            PerformValuationService performValuationService = new PerformValuationService(logger, _httpClient);


            int processCount = 1;
            int chunkSize = ConfigService.ValuationChunkSize;
            TokenResponse tokenResponse = await performValuationService.GenerateToken();
            logger.Info($"Processsing batch #{vehicleValuationBatchToProcess.Id}: {vehicleValuationBatchToProcess.VehicleValuations.Count()} item(s) to process");
            List<FixedCostWarranty> fixedCostWarranties = await retailerSitesDataAccess.GetDealerGroupFixedCostWarranties((int)dealerGroup);
            IOrderedEnumerable<FixedCostPrep> fixedCostPreps = (await retailerSitesDataAccess.GetFixedCostPreps((int)dealerGroup)).OrderBy(x => x.FromMonths);


            IEnumerable<VehicleValuation[]> vehicleValuationsChunks = vehicleValuationBatchToProcess.VehicleValuations.Chunk(chunkSize);

            foreach (var vehicleValuationsChunk in vehicleValuationsChunks)
            {
               List<Task> tasks = new List<Task>();

               processCount = vehicleValuationBatchToProcess.VehicleValuations.Where(v => v.HasBeenValued == true).Count();
               logger.Info($"Processing item {processCount} of {vehicleValuationBatchToProcess.VehicleValuations.Count()} (will only log every {chunkSize} items)");
               tokenResponse = await performValuationService.CheckExpiryAndRegenerate(tokenResponse);


               RetailerSite retailer = retailersWithStrategies.First();
               PerformValuationBatchParams parms = new PerformValuationBatchParams()
               {
                  bearerToken = tokenResponse,
                  applyPriceScenarios = vehicleValuationBatchToProcess.ApplyPriceScenarios,
                  failedVehicleValuations = failedVehicleValuations,
                  retailerSiteRetailerId = retailer.RetailerId,
                  retailerSitePostcode = retailer.Postcode,
                  retailerSite = retailer
               };


               foreach (var vehicleValuation in vehicleValuationsChunk)
               {
                  try
                  {
                     tasks.Add(
                         StartValuation(
                           tokenResponse,
                           parms,
                           performValuationService,
                           vehicleValuation,
                           logger,
                           valuationIdOptions,
                           retailer.BuyingCostsSets,
                           vehicleValuationBatchToProcess.TemplateType,
                           retailersWithStrategies,
                           vehicleValuationRatingBySites,
                           runDate,
                           valueVehicleAtAllSites,
                           dealerGroup,
                           valuationsDataAccess,
                           retailerSitesDataAccess, fixedCostWarranties, fixedCostPreps, atFutureValuationsClient
                         ));
                  }
                  catch (Exception ex)
                  {
                     logger.Error($"Failed chunk during future valuation task.   Re-run future valuations for {dealerGroup}");
                  }
               }

               await Task.WhenAll(tasks); //execute all items within the chunk of items at once in aprallel
               tasks.Clear();
            }


         }

         using (var childScope = Tracer.Instance.StartActive($"Save valuation batch results"))
         {
            await SaveValuationFeatures(logger, valuationsDataAccess, vehicleValuationBatchToProcess, valuationIdOptions.ToDictionary());
            await UpdateVehicleValuationsWithRecallStatus(vehicleValuationBatchToProcess.VehicleValuations, logger);


            //save the API response result
            vehicleValuationBatchToProcess.Status = BatchStatus.Complete.ToString();
            vehicleValuationBatchToProcess.ValuationCompleteDate = DateTime.UtcNow;

            TruncateStringProps(logger, vehicleValuationBatchToProcess);

            //Batch processing is complete /update batch and valuation
            await valuationsDataAccess.UpdateVehicleValuationBatchAndVehicleValuations(vehicleValuationBatchToProcess);
            logger.Info($"Batch has been valued successfully");

            //update valuation rating by site
            await valuationsDataAccess.CreateVehicleValuationRatingBySite(vehicleValuationRatingBySites.ToList());
            logger.Info($"Valuations by site done");

            //create excel summarising the results
            var getValuationBatchResultsParams = new GetValuationBatchResultsParams()
            {
               BatchIds = vehicleValuationBatchToProcess.Id.ToString(),
               OnlyShowBestLocation = !valueVehicleAtAllSites,
            };


            string path = string.Empty;
            if (vehicleValuationBatchToProcess.ApplyPriceScenarios)
            {
               IEnumerable<ValuationPriceScenarioBatchResult> valuationBatchResults = await valuationsDataAccess.GetValuationPriceScenarioBatchResults(getValuationBatchResultsParams, dealerGroup);
               path = ValuationExcelSheetMakerService.MakeValuationPriceScenarioBatch(valuationBatchResults, vehicleValuationBatchToProcess.Id, dealerGroup, vehicleValuationBatchToProcess.VehicleValuations.FirstOrDefault().Condition);
            }
            else
            {
               IEnumerable<ValuationBatchResult> valuationBatchResults = await valuationsDataAccess.GetValuationBatchResults(getValuationBatchResultsParams, userRetailerSitesIdsString);
               string fileName = vehicleValuationBatchToProcess.Name;
               string whenAndWho = $"Uploaded {vehicleValuationBatchToProcess.LastRunDate.ToString("dd/MM/yyyy h:mm tt")} by {vehicleValuationBatchToProcess.LastRunBy.Name}";
               var showBulkValuationResultsAtHomeSiteClaim = await userDataAccess.GetUserClaim(dealerGroup, userId, "ShowBulkValuationResultsAtHomeSite");
               bool showBulkValuationResultsAtHomeSite = showBulkValuationResultsAtHomeSiteClaim == null ? false : bool.Parse(showBulkValuationResultsAtHomeSiteClaim.ClaimValue);
               int userHomeSiteId = await userDataAccess.GetUserCurrentSiteId(userId, dealerGroup);
               List<FeeLookupItems> feeLookupItems = await valuationAuctionFeesDataAccess.GetFeeLookupItems(dealerGroup, vehicleValuationBatchToProcess.TemplateType);
               path = ValuationExcelSheetMakerService.MakeValuationsResultSpreadsheet(valuationBatchResults, vehicleValuationBatchToProcess.Id, fileName, whenAndWho, valueVehicleAtAllSites,
                   showBulkValuationResultsAtHomeSite, userHomeSiteId, feeLookupItems);
            }

            //send email

            await SendValuationCompleteEmail(userEmail, path, attemptedCount, failedVehicleValuations.ToList(), logger);
            logger.Info($"Email sent to {userEmail}.   Job complete.");
            logger.Info("----------------------------------------------------------------------------");
            logger.Info("");
         }
      }



      private static void TruncateStringProps(ILog logger, VehicleValuationBatch vehicleValuationBatchToProcess)
      {
         //ensure no valuations exceed the allowed column length limit
         foreach (var item in vehicleValuationBatchToProcess.VehicleValuations)
         {
            try
            {
               item.TruncateStringPropsAsRequired();
            }
            catch (ArgumentOutOfRangeException ex)
            {
               logger.Error(ex.Message);
            }
         }
      }

      private async Task StartValuation(
          TokenResponse tokenResponse,
          PerformValuationBatchParams parms,
          PerformValuationService performValuationService,
          VehicleValuation vehicleValuation,
          ILog logger,
          ConcurrentDictionary<int, List<ATNewVehicleGet_Feature>> valuationIdOptions,
          ICollection<BuyingCostsSet> buyingCostsSets,
          BulkUploadPredefinedTemplateType templateType,
          List<RetailerSite> retailersWithStrategies,
          ConcurrentBag<VehicleValuationRatingBySite> vehicleValuationRatingBySites,
          DateTime runDate,
          bool valueVehicleAtAllSites,
          DealerGroupName dealerGroup,
          ValuationsDataAccess valuationsDataAccess,
          RetailerSitesDataAccess retailerSitesDataAccess,
          List<FixedCostWarranty> fixedCostWarranties,
          IOrderedEnumerable<FixedCostPrep> fixedCostPreps,
          AutoTraderFutureValuationsClient atFutureValuationsClient
          )
      {
         try
         {
            ValuationResult valuationResult = await performValuationService.PerformValuation(parms, vehicleValuation, logger, "Start Valuation");
            if (valuationResult.vehicle?.vehicle?.oem == null)
            {
               valuationResult.vehicle.vehicle.oem = new ATNewVehicleGet_Oem() { colour = vehicleValuation.SpecificColour };
            }
            else
            {
               valuationResult.vehicle.vehicle.oem.colour = vehicleValuation.SpecificColour;
            }

            // Thread-safe addition to valuationIdOptions
            valuationIdOptions[vehicleValuation.Id] = valuationResult.vehicleOptions.Where(x => x.factoryFitted == true).ToList();

            UpdateWithBuyingCostsSet(vehicleValuation, buyingCostsSets, templateType, valuationResult);
            if (fixedCostPreps.Count() > 0)
            {
               UpdateFixedCostPrep(vehicleValuation, fixedCostPreps);
            }
            if (fixedCostWarranties.Count() > 0)
            {
               UpdateFixedCostWarranty(parms, vehicleValuation, fixedCostWarranties);
            }



            await performValuationService.CreateVehicleValuationsForAllSites(
                          parms,
                          retailersWithStrategies,
                          vehicleValuationRatingBySites,
                          valuationResult,
                          vehicleValuation,
                          valueVehicleAtAllSites,
                          tokenResponse,
                          runDate,
                          dealerGroup,
                          fixedCostWarranties
                        );

         }
         catch (Exception ex)
         {
            logger.Error($"Perform valuation: Error on {vehicleValuation.VehicleReg} {ex.Message}");
         }

      }

      private static void UpdateWithBuyingCostsSet(VehicleValuation vehicleValuation, ICollection<BuyingCostsSet> buyingCostsSets, BulkUploadPredefinedTemplateType templateType, ValuationResult valuationResult)
      {
         var retailerBuyingCosts = buyingCostsSets
                 .Where(x => x.TemplateType == templateType)
                 .OrderBy(x => x.ValuationUpTo)
                 .FirstOrDefault(x => x.ValuationUpTo >= valuationResult.retailValuation);

         if (retailerBuyingCosts != null)
         {
            vehicleValuation.AdditionalMech = retailerBuyingCosts.IsPrepPercent ?
                (int)Math.Round(retailerBuyingCosts.PrepAmount * valuationResult.retailValuation, 0, MidpointRounding.AwayFromZero) :
                (int)Math.Round(retailerBuyingCosts.PrepAmount, 0, MidpointRounding.AwayFromZero);

            vehicleValuation.Fee = retailerBuyingCosts.IsAuctionFeePercent ?
                    (int)Math.Round(retailerBuyingCosts.AuctionFeeAmount * valuationResult.retailValuation, 0, MidpointRounding.AwayFromZero) :
                    (int)Math.Round(retailerBuyingCosts.AuctionFeeAmount, 0, MidpointRounding.AwayFromZero);

            vehicleValuation.Profit = retailerBuyingCosts.IsMarginPercent ?
                    (int)Math.Round(retailerBuyingCosts.MarginAmount * valuationResult.retailValuation, 0, MidpointRounding.AwayFromZero) :
                    (int)Math.Round(retailerBuyingCosts.MarginAmount, 0, MidpointRounding.AwayFromZero);
         }
      }

      private static void UpdateFixedCostWarranty(PerformValuationBatchParams parms, VehicleValuation vehicleValuation, List<FixedCostWarranty> fixedCostWarranties)
      {
         var fixedWarrantyCost = WarrantyCostService.GetFixedCostWarrantyFee(
                        parms.retailerSite.DealerGroup_Id,
                        parms.retailerSite.Id,
                        vehicleValuation.FirstRegistered,
                        vehicleValuation.Make,
                        vehicleValuation.Model,
                        fixedCostWarranties);

         vehicleValuation.Warranty = (int)(fixedWarrantyCost ?? 0);
      }

      private static void UpdateFixedCostPrep(VehicleValuation vehicleValuation, IOrderedEnumerable<FixedCostPrep> fixedCostPreps)
      {
         if (vehicleValuation.FirstRegistered.HasValue)
         {

            DateTime today = DateTime.Today;
            int ageInMonths = 0;
            DateTime startDate = vehicleValuation.FirstRegistered.Value;
            DateTime endDate = DateTime.Now;

            // Calculate the total months based on year and month difference
            ageInMonths = (endDate.Year - startDate.Year) * 12 + endDate.Month - startDate.Month;
            FixedCostPrep fixedPrepCost = fixedCostPreps.LastOrDefault(x => x.Model == vehicleValuation.Model && x.FromMonths <= ageInMonths);
            if (fixedPrepCost != null)
            {
               vehicleValuation.AdditionalMech = (int)Math.Round(fixedPrepCost.Fee, 0, MidpointRounding.AwayFromZero);
            }
         }
      }

      private static async Task SaveValuationFeatures(ILog logger, ValuationsDataAccess valuationsDataAccess, VehicleValuationBatch vehicleValuationBatchToProcess, Dictionary<int, List<ATNewVehicleGet_Feature>> valuationIdOptions)
      {
         //Save features
         //Upsert all the optional features received
         List<VehicleOption> vehicleOptions = new List<VehicleOption>();
         var allFeatures = valuationIdOptions.SelectMany(a => a.Value).Distinct();
         foreach (var feature in allFeatures)
         {
            vehicleOptions.Add(new VehicleOption(feature));
         }
         //now we have a list of all possible vehicle options we have, should firstly upsert them.
         var dbOptions = await valuationsDataAccess.InsertOptionsIfRequired(vehicleOptions);

         //then go through the adverts and ensure that we add entries into VehicleValuationOptions mappings table
         List<VehicleValuationOption> vehicleValuationOptions = new List<VehicleValuationOption>();
         List<int> vehicleValuationIds = vehicleValuationBatchToProcess.VehicleValuations.Select(x => x.Id).ToList();
         foreach (var valuation in valuationIdOptions)
         {
            foreach (var feature in valuation.Value)
            {
               var option = new VehicleOption(feature);
               var dbOption = dbOptions.FirstOrDefault(x => x.Name == option.Name && x.Category == option.Category && x.OriginalPrice == option.OriginalPrice);
               if (dbOption == null)
               {
                  logger.Error($"Failed to find vehicle option {option.Name} for valuation #{valuation.Key}");
                  continue;
               }
               vehicleValuationOptions.Add(new VehicleValuationOption()
               {
                  VehicleValuation_Id = valuation.Key,
                  VehicleOption_Id = dbOption.Id,
                  IsChosen = true,
                  IsStandard = false //we actually don't know this but don't use it so leave for now
               });
            }
         }
         await valuationsDataAccess.DeleteExistingOptionMappings(vehicleValuationIds);
         await valuationsDataAccess.AddVehicleValuationOptions(vehicleValuationOptions);
      }

      public async Task ValueAndSaveVehiclesInStockNotInAT(ILog logger, List<DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0)
         { return; }
         logger.Info("----------------------------------------------------------");
         logger.Info("InStockNotOnPortal");


         foreach (DealerGroupName dealerGroup in dealerGroups)
         {
            logger.Info($"InStockNotOnPortal: {dealerGroup}");
            var logMessage = LoggingService.InitLogMessage();
            try
            {
               string _connectionString = ConfigService.GetConnectionString(dealerGroup);
               RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
               List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

               if (retailers.Count > 0)
               {
                  await ValueAndSaveVehiclesInStockNotInATThisDealerGroup(logger, _connectionString, dealerGroup, retailers);
               }
               else
               {
                  logger.Info($"InStockNotOnPortal: {dealerGroup}: No retailers found");
               }

            }
            catch (Exception ex)
            {
               LoggingService.AddErrorLogMessage(logMessage, ex.Message);
               logger.Error($"Error encountered InStockNotOnPortal: {dealerGroup}", ex);
            }
            finally
            {
               await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "InStockNotOnPortal");
            }
         }

         logger.Info("InStockNotOnPortal: Completed");
         logger.Info("----------------------------------------------------------");
      }



      public async Task ValueAndSaveVehiclesInStockNotInATThisDealerGroup(ILog logger, string _connectionString, DealerGroupName dealerGroup, List<RetailerSite> retailers)
      {
         //int firstRetailerId = retailers.First().RetailerId;

         //Get all InStock Not In AT Vehicles
         StockDataAccess stockDataAccess = new StockDataAccess(_connectionString);

         //find all adverts that exist now for this dealerGroup
         VehicleAdvertsDataAccess vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         VehicleAdvertsService vehicleAdvertsService = new VehicleAdvertsService(_connectionString);
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var bandings = await retailerSitesDataAccess.GetRetailerStrategyBandings();

         var lifecycles = AutoPriceHelperService.GetAllLifecycleStatuses();
         IEnumerable<VehicleAdvertWithRating> allExistingAdsOnAT =
             (await vehicleAdvertsService.FetchVehicleAdvertsWithRatingsFromMainDbTables(DateTime.Now, dealerGroup, lifecycles, null))
                 .Where(x => x.AutotraderAdvertStatus != "IN STOCK NOT ON PORTAL")
                 .ToList();

         //get all stocks for this dealerGroup
         IEnumerable<StockListRow> stockItems = await stockDataAccess.GetStockListRows(dealerGroup);


         //walk through stockItems, find match based on REG
         List<StockListRow> stocksNotInAT = new List<StockListRow>();
         foreach (var item in stockItems)
         {
            //skip these disposal routes
            if (item.DisposalRoute == "Auction" || item.DisposalRoute == "Trade")
            {
               continue;
            }

            //skip new
            if (item.VehicleSuperType.ToUpper() == "NEW")
            {
               continue;
            }

            //skip sold
            if (item.ProgressCode == "Sold")
            {
               continue;
            }

            //skip those without a reg or mileage
            if (item.Reg == null || item.Reg == string.Empty || item.Mileage == 0)
            {
               continue;
            }

            var matchOnReg = allExistingAdsOnAT.FirstOrDefault(x => x.VehicleReg == item.Reg);

            if (matchOnReg == null)
            {
               stocksNotInAT.Add(item);
            }
         }


         List<VehicleAdvert> existingFakeAdverts = await vehicleAdvertsDataAccess.GetExistingFakeVehicleAdverts(dealerGroup);

         PerformValuationService performValServ = new PerformValuationService(logger, _httpClient);
         AutoTraderFutureValuationsClient atFutureValuationsClient = new AutoTraderFutureValuationsClient(_httpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);

         var tokenResponse = await performValServ.GenerateToken();

         List<VehicleAdvert> advertsTobeAdded = new List<VehicleAdvert>();
         List<VehicleAdvertSnapshot> snapshotsToBeAdded = new List<VehicleAdvertSnapshot>();
         List<VehicleAdvert> updatedAds = new List<VehicleAdvert>();

         // Use thread-safe collections to avoid concurrency issues
         ConcurrentBag<VehicleAdvert> advertsToBeAddedConcurrent = new();
         ConcurrentBag<VehicleAdvertSnapshot> snapshotsToBeAddedConcurrent = new();
         ConcurrentBag<VehicleAdvert> updatedAdsConcurrent = new();

         // Set the maximum degree of parallelism.   We are limited by future vals which can only take 30 calls / s.   We should aim to spread them across the seconds though
         int rateLimit = 8; // 
         int delayMs = 1000 / rateLimit; // Delay in milliseconds between tasks
         //logger.Info($"Processing {stocksNotInAT.Count} vehicles with rate limiter and {delayMs}ms delay between tasks");

         Stopwatch sw = new Stopwatch();
         sw.Start();
         long lastIterationTime = sw.ElapsedMilliseconds;

         //---------------------------------------
         //NEW APPROACH
         //---------------------------------------
         List<Task> runningTasks = new List<Task>();
         for (int i = 0; i < stocksNotInAT.Count; i++)
         {
            var stock = stocksNotInAT[i];

            // Log progress periodically
            //if (i % 50 == 0 || i == stocksNotInAT.Count - 1)
            //{
            //   //logger.Info($"Queuing item {i + 1} of {validItems.Count}");
            //   logger.Info($"Trying item {i + 1} of {stocksNotInAT.Count}, elapsed time: {sw.ElapsedMilliseconds}ms.   Only logs every 50 items.");
            //}

            //turn it into a valuation so we can feed it through the valuation routine
            var vehicleValuation = new VehicleValuation() { VehicleReg = stock.Reg, Mileage = stock.Mileage, Condition = "Excellent" };
            var thisRetailer = retailers.FirstOrDefault(x => x.Site_Id == stock.SiteId);

            if (thisRetailer == null)
            {
               //logger.Info($"Skipping {stock.Reg} as it is at site #{stock.SiteId} {stock.SiteDescription}, could not find match within active retailer sites.");
               continue;
            }

            PerformValuationBatchParams valuationBatchParams = new PerformValuationBatchParams()
            {
               //we don't set the site here, we do it later in the loop
               bearerToken = tokenResponse,
               applyPriceScenarios = false,
               //retailers = retailers,
               failedVehicleValuations = new ConcurrentBag<VehicleValuationError>(),
               retailerSite = thisRetailer,
               retailerSitePostcode = thisRetailer.Postcode,
               retailerSiteRetailerId = thisRetailer.RetailerId
            };

            //run the task, without awaiting
            Task task = Task.Run(() => UnitOfWork(logger, dealerGroup, stock, existingFakeAdverts, performValServ, atFutureValuationsClient, tokenResponse, advertsToBeAddedConcurrent, snapshotsToBeAddedConcurrent, updatedAdsConcurrent, vehicleValuation, thisRetailer, valuationBatchParams));
            runningTasks.Add(task);

            //more reliable, but cpu intense, way of delaying
            while (sw.ElapsedMilliseconds - lastIterationTime < delayMs)
            {
               Thread.SpinWait(100); // Avoid tight spinning — tune the value as needed
            }
            lastIterationTime = sw.ElapsedMilliseconds;

         }
         await Task.WhenAll(runningTasks);



         // Convert concurrent collections to lists
         advertsTobeAdded = advertsToBeAddedConcurrent.ToList();
         snapshotsToBeAdded = snapshotsToBeAddedConcurrent.ToList();
         updatedAds = updatedAdsConcurrent.ToList();

         //2.5 truncate the reg and chassis
         int maxRegLength = 7;
         int maxChassisLength = 20;
         foreach (var ad in advertsTobeAdded)
         {
            if (ad.VehicleReg != null)
            {
               ad.VehicleReg = ad.VehicleReg.Length > maxRegLength ? ad.VehicleReg.Substring(0, maxRegLength) : ad.VehicleReg;
            }
            if (ad.Chassis != null)
            {
               ad.Chassis = ad.Chassis.Length > maxChassisLength ? ad.Chassis.Substring(0, maxChassisLength) : ad.Chassis;
            }
         }


         //3. Save the new adverts 
         await vehicleAdvertsDataAccess.SaveNewVehicleAdverts(advertsTobeAdded);

         //3.1 Save the updated ads
         await vehicleAdvertsDataAccess.SaveNewOdometerAndEngineBadgeReadingForAdverts(updatedAds);

         foreach (var item in snapshotsToBeAdded.Where(x => x.VehicleAdvert != null))
         {
            item.VehicleAdvert_Id = item.VehicleAdvert.Id;
            item.VehicleAdvert = null;
         }


         //4. save the new snapshots
         var vehicleSnapshotsDataAccess = new VehicleAdvertSnapshotsDataAccess(_connectionString);
         await vehicleSnapshotsDataAccess.SaveNewSnapshots(snapshotsToBeAdded);

         //5. update the vehicleAdvert_Id reference within stocks table
         await stockDataAccess.UpdateStocksVehicleAdvertId(dealerGroup);


         //6. Clean up the old ads
         //await vehicleAdvertsDataAccess.CleanUpAdvertVehicles(dealerGroup);

         logger.Info($"ProcessInStockNotInAT: {dealerGroup}: Saved {snapshotsToBeAdded.Count} stock items as snapshots");

      }

      private static async Task UnitOfWork(ILog logger, DealerGroupName dealerGroup, StockListRow stock, List<VehicleAdvert> existingFakeAdverts, PerformValuationService performValServ, AutoTraderFutureValuationsClient atFutureValuationsClient, TokenResponse tokenResponse, ConcurrentBag<VehicleAdvert> advertsToBeAddedConcurrent, ConcurrentBag<VehicleAdvertSnapshot> snapshotsToBeAddedConcurrent, ConcurrentBag<VehicleAdvert> updatedAdsConcurrent, VehicleValuation vehicleValuation, RetailerSite thisRetailer, PerformValuationBatchParams valuationBatchParams)
      {

         //get valuation result
         ValuationResult valuationResult = await performValServ.PerformValuation(valuationBatchParams, vehicleValuation, logger, "ValueAndSaveVehiclesInStockNotInAT");


         //new - get future valuations
         (decimal? valuationMonthPlus1, decimal? valuationMonthPlus2, decimal? valuationMonthPlus3) =
            await GetFutureValuations(atFutureValuationsClient, tokenResponse, vehicleValuation, thisRetailer, valuationResult, logger);


         //find out if it already exists as a fake advert or not
         var existingAdvert = existingFakeAdverts.FirstOrDefault(x => x.VehicleReg == vehicleValuation.VehicleReg && x.RetailerSite_Id == thisRetailer.Id);
         VehicleAdvert advertForSnapshot = existingAdvert;

         if (existingAdvert != null)
         {
            //We have an existing advert, check for change props
            if (existingAdvert.OdometerReading != vehicleValuation.Mileage || existingAdvert.BadgeEngineSizeLitres != vehicleValuation.BadgeEngineSizeLitres || existingAdvert.CompetitorLink != vehicleValuation.CompetitorLink)
            {
               existingAdvert.OdometerReading = vehicleValuation.Mileage;
               existingAdvert.BadgeEngineSizeLitres = vehicleValuation.BadgeEngineSizeLitres;
               existingAdvert.CompetitorLink = vehicleValuation.CompetitorLink;
               updatedAdsConcurrent.Add(existingAdvert);
            }
            //Then add the snapshot
            CreateSnapshotAndAdd(dealerGroup, stock, snapshotsToBeAddedConcurrent, vehicleValuation, valuationResult, valuationMonthPlus1, valuationMonthPlus2, valuationMonthPlus3, existingAdvert);

         }
         else
         {
            VehicleAdvert newVehicleAdvert = new VehicleAdvert(vehicleValuation, thisRetailer.RetailerId, stock.StockNumberFull, thisRetailer.Id, stock);
            advertsToBeAddedConcurrent.Add(newVehicleAdvert);
            CreateSnapshotAndAdd(dealerGroup, stock, snapshotsToBeAddedConcurrent, vehicleValuation, valuationResult, valuationMonthPlus1, valuationMonthPlus2, valuationMonthPlus3, newVehicleAdvert);
         }
      }

      private static void CreateSnapshotAndAdd(DealerGroupName dealerGroup, StockListRow stock, ConcurrentBag<VehicleAdvertSnapshot> snapshotsToBeAddedConcurrent, VehicleValuation vehicleValuation, ValuationResult valuationResult, decimal? valuationMonthPlus1, decimal? valuationMonthPlus2, decimal? valuationMonthPlus3, VehicleAdvert advert)
      {
         VehicleAdvertSnapshot vehicleAdvertSnapShot = new VehicleAdvertSnapshot(vehicleValuation, advert, 0, valuationResult.retailRating, valuationResult.vehicle, stock, (int)dealerGroup);
         vehicleAdvertSnapShot.ValuationMonthPlus1 = valuationMonthPlus1;
         vehicleAdvertSnapShot.ValuationMonthPlus2 = valuationMonthPlus2;
         vehicleAdvertSnapShot.ValuationMonthPlus3 = valuationMonthPlus3;
         snapshotsToBeAddedConcurrent.Add(vehicleAdvertSnapShot);
      }

      private static async Task<(decimal? valuationMonthPlus1, decimal? valuationMonthPlus2, decimal? valuationMonthPlus3)> GetFutureValuations(AutoTraderFutureValuationsClient atFutureValuationsClient, TokenResponse tokenResponse, VehicleValuation vehicleValuation, RetailerSite thisRetailer, ValuationResult valuationResult, ILog? logger)
      {
         List<DateTime> futureValnPoints = new List<DateTime>()
            {
               DateTime.Now.AddMonths(1),
               DateTime.Now.AddMonths(2),
               DateTime.Now.AddMonths(3),
            };


         decimal? valuationMonthPlus1 = null;
         decimal? valuationMonthPlus2 = null;
         decimal? valuationMonthPlus3 = null;

         if (vehicleValuation.FirstRegistered != null)
         {

            GetFutureValuationsParams futureValParms = new GetFutureValuationsParams()
            {
               futureValuationPoints = futureValnPoints,
               derivativeId = vehicleValuation.DerivativeId,
               firstRegistrationDate = (DateTime)vehicleValuation.FirstRegistered,
               odometerReading = vehicleValuation.Mileage,
               retailerId = thisRetailer.RetailerId,
               currentValuation = valuationResult.retailValuation,
               currentAdjustedValuation = valuationResult.retailValuation
            };
            List<FutureValuationPoint> futureVals = await atFutureValuationsClient.GetFutureValuation(futureValParms, tokenResponse, logger);
            valuationMonthPlus1 = futureVals[0]?.RetailValue;
            valuationMonthPlus2 = futureVals[1]?.RetailValue;
            valuationMonthPlus3 = futureVals[2]?.RetailValue;
         }

         return (valuationMonthPlus1, valuationMonthPlus2, valuationMonthPlus3);
      }

      private async Task SendValuationCompleteEmail(string userEmail, string fileToAttachPath, int attemptedCount, List<VehicleValuationError> failedVehicleValuations, ILog logger)
      {
         int failedCount = failedVehicleValuations.Count();
         int successCount = attemptedCount - failedVehicleValuations.Count();
         string vehicleText = attemptedCount > 0 ? "vehicles" : "vehicle";

         string messageTitle = $"Your file submitted for vehicle valuation has been processed successfully. Attempted to value {attemptedCount} {vehicleText}, {successCount} succeeded and {failedCount} failed.";
         string messageBody = string.Empty;
         string message = string.Empty;

         if (failedCount > 0)
         {
            var html = new StringBuilder();
            html.Append("<br><br>Failures are:</br><table class='my-table'><thead><tr><th>REG</th><th>Mileage</th><th>Condition</th><th>Fail Notes</th></tr></thead><tbody>");

            foreach (var valuation in failedVehicleValuations.Select((r, i) => new { Row = r, Index = i }))
            {
               var bgColor = valuation.Index % 2 == 0 ? "#fff" : "#f2f2f2"; // Alternate row colors
               html.Append($"<tr style='background-color:{bgColor}'>");
               html.Append($"<td>{valuation.Row.VehicleValuation.VehicleReg}</td><td>{valuation.Row.VehicleValuation.Mileage}</td><td>{valuation.Row.VehicleValuation.Condition}</td><td>{valuation.Row.errorMessage}</td></tr>");
            }
            html.Append("</tbody></table>");

            var css = "<style>.my-table { border-collapse: collapse; } .my-table th, .my-table td { border: 1px solid #ddd; padding: 8px; text-align: left; } .my-table th { background-color: #4CAF50; color: white; }</style>";

            messageBody += $"{css}{html.ToString()}";
         }
         message = messageTitle + messageBody;

         List<string> tos = new List<string>() { userEmail };
         List<string> ccs = new List<string>();

         if (failedCount > 0)
         {
            ccs.Add("<EMAIL>");
         }

         if (failedCount == successCount)
         {

            ccs.AddRange(ConfigService.ValuationErrorEmailTo.ToList());
            message = messageTitle;
            fileToAttachPath = null;
         }

         await EmailerService.SendMail(Model.DealerGroupName.RRGUK, $"Vehicle Valuation - Completed", message, fileToAttachPath, logger, tos, ccs);
      }


      private async Task UpdateVehicleValuationsWithRecallStatus(ICollection<VehicleValuation> vehicleValuations, ILog logger)
      {
         //Recall info
         // logger.Info($"Recall information - Start");
         var dVSAApiClient = new DVSAApiClient(HttpClientFactoryService.HttpClientFactory);

         string baseURL = ConfigService.DVSABaseURLForRecallApi;
         string apiKey = ConfigService.DVSAApiKey;
         string tokenURL = ConfigService.DVSATokenURL;
         string clientId = ConfigService.DVSAClientId;
         string clientSecret = ConfigService.DVSAClientSecret;
         string scope = ConfigService.DVSAScope;
         var token = await dVSAApiClient.GetToken(tokenURL, clientId, clientSecret, scope);


         IEnumerable<VehicleValuation[]> chunks = vehicleValuations.Chunk(20);
         foreach (var chunk in chunks)
         {
            List<Task> tasks = new List<Task>();
            foreach (var vv in chunk)
            {
               tasks.Add(GetVehicleRecallStatus(dVSAApiClient, vv, baseURL, apiKey, token));
            }

            await Task.WhenAll(tasks); //execute all items within the chunk of items at once in aprallel
            tasks.Clear();
         }
         logger.Info($"Recall information - End");
      }



      private async Task GetVehicleRecallStatus(DVSAApiClient dVSAApiClient, VehicleValuation vehicleValuation, string baseURL, string apiKey, DVSATokenResponse token)
      {
         var recallStatus = VehicleRecallStatus.Unavailable;
         try
         {
            //throw new Exception("Test");
            recallStatus = await dVSAApiClient.GetRecallInformation(vehicleValuation.VehicleReg, baseURL, apiKey, token);
         }
         catch (Exception ex)
         {
            Console.WriteLine($"Failed getting Vehicle Recall Status for {vehicleValuation.VehicleReg}| {ex.Message}");
         }

         vehicleValuation.RecallStatus = recallStatus.ToString();
      }







   }
}
