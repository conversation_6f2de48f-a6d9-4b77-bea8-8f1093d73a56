﻿using Quartz;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Data;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Loader.Comparers;
using System.Diagnostics;

namespace CPHI.Spark.Loader
{
   public partial class DealsVindisJob : IJob
   {
      private static readonly ILog Logger = LogManager.GetLogger(typeof(DealsVindisJob));
      private static DateTime LastLoggedNoFiles = DateTime.UtcNow;
      private List<Deal> dbDeals;
      private List<Site> sites;
      private List<StandingValue> standingValues;
      private List<VehicleType> vehicleTypes;
      private List<GlobalParam> dbGlobalParams;
      private List<Person> dbPeople;
      private List<OrderType> dbOrderTypes;
      private LogMessage logMessage;
      private int errorCount;
      private int changedCount;

      private List<Diff> diffs;
      private List<Stock> dbStocks;
      private const string fileSearch = "*SPKV1.xlsx";
      /*
| Column                   | Example                                           | Sensitive           |
|--------------------------|---------------------------------------------------|---------------------|
| Order Date               | 44946.72153                                       | No                  |
| Handover Date            | 45035.41667                                       | No                  |
| Invoice Date             | 45035                                             | No                  |
| Order No                 | 17492691                                          | No                  |
| Keyloop DMS Magic Number |                                                   | No                  |
| Dealership               | Vindis Audi Bedford                               | No                  |
| Customer Name            | Mr Jack Dormer                                    | YES - customer name |
| Customer Scheme          |                                                   | No                  |
| Sales Exec               | Michael White                                     | No                  |
| Reg No                   | KP23JNX                                           | No                  |
| Make                     | Audi                                              | No                  |
| Range                    | A4                                                | No                  |
| Model                    | A4 DIESEL AVANT                                   | No                  |
| Derivative               | 35 TDI Black Edition 5dr S Tronic [Comfort+Sound] | No                  |
| Vin Number               | WAUZZZF44PA062613                                 | No                  |
| New/Used                 | New                                               | No                  |
| StockNumber              |                                                   | No                  |
| Vehicle Without VAT      | 36704.17                                          | No                  |
| Vehicle                  | 44045                                             | No                  |
| Factory Options          | 0                                                 | No                  |
| SIV                      | -                                                 | No                  |
| Recondition Cost         | -                                                 | No                  |
| VAT (Used)               | -                                                 | No                  |
| Contract Hire Base       | 1275                                              | No                  |
| "Finance Subsidy "       | 0                                                 | No                  |
| RCH Profit Adjustment    | 0                                                 | No                  |
| Incentives               | 0                                                 | No                  |
| Over Allowance           | 0                                                 | No                  |
| Finance Type             | Personal Contract Hire                            | No                  |
| Finance Income           | 365                                               | No                  |
| Total Profit             | 1640                                              | No                  |
| Deal Closed              |                                                   | No                  |
| Manufacturer Order No    | 23AUQ2005927                                      | No                  |
| Quality 50%              | 0                                                 | No                  |
| Investor 50%             | 0                                                 | No                  |
| Total Bonus              | 0                                                 | No                  |

*/


      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         //---------------------------------------------------------------------------------------------------
         //Firstly look for files
         string[] allMatchingFiles = Directory.GetFiles(ConfigService.incomingRoot, fileSearch);

         //check for presence of lock, if so, return as already running
         if (LocksService.VindisDeals) { return; }


         //Check if no files, and if not logged for a while, log that didn't find anything
         if (allMatchingFiles.Length == 0)
         {
            //nothing found
            TimeSpan age = DateTime.UtcNow - LastLoggedNoFiles;
            if (age.Minutes > 120)
            {
               LastLoggedNoFiles = DateTime.UtcNow;
               Logger.Info($@"[{DateTime.UtcNow}] | No files found matching pattern *SPKV1.xlsx");
            }
            return;
         }

         //we are doing this
         LocksService.VindisDeals = true;

         if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
         {
            System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
         }

         DateTime start = DateTime.UtcNow;


         using (var db = new CPHIDbContext())
         {
            // Create lock to prevent other instances running
            try
            {

               //0. Set up Lists
               DateTime twentyfourMonthsAgo = DateTime.UtcNow.AddDays(-1).AddMonths(-24);
               try { dbDeals = db.Deals.Where(x => x.OrderDate >= twentyfourMonthsAgo).ToList(); } catch { };

               dbStocks = db.Stocks.Where(s => s.StockDate > twentyfourMonthsAgo).ToList();
               dbGlobalParams = db.GlobalParams.ToList();
               sites = db.Sites.ToList();
               standingValues = db.StandingValues.ToList();
               vehicleTypes = db.VehicleTypes.ToList();
               dbPeople = db.People.ToList();
               dbOrderTypes = db.OrderTypes.ToList();

               // This loader will only process one file at a time
               string fileToProcess = allMatchingFiles[0];

               // Try opening the file, if fail, return (common problem is loader trying to open file whilst scraper is saving it).
               try { FileStream fs = File.Open(fileToProcess, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None); fs.Close(); }
               catch (IOException) { throw; }

               logMessage = new LogMessage();
               logMessage.DealerGroup_Id = 3;


               int openingDbCount = dbDeals.Where(d => !d.IsRemoved).Count();
               int removedCount = 0;
               int newCount = 0;

               errorCount = 0;
               changedCount = 0;
               diffs = new List<Diff>();

               try
               {

                  logMessage.SourceDate = DateTime.UtcNow;
                  logMessage.Job = this.GetType().Name;
                  Logger.Info($@"[{DateTime.UtcNow}] New file found at {ConfigService.incomingRoot}.  Starting {fileToProcess}");   //update logger 

                  //---------------------------------------------------------------------------------------------------
                  //1.  Set File to -p to indicate is being processed and get filename details
                  if (File.Exists(fileToProcess.Replace(".xlsx", "-p.xlsx")))
                  {
                     //already processing a file of this type, skip
                     Logger.Error($@"Could not interpret {fileToProcess}, -p file already found ");
                     logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                  }

                  string fileName = Path.GetFileName(fileToProcess);
                  var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                  logMessage.SourceDate = fileDate;

                  File.Move(fileToProcess, fileToProcess.Replace(".xlsx", "-p.xlsx")); //append _processing to the file to prevent any other instances also processing these files
                  var newFilepath = fileToProcess.Replace(".xlsx", "-p.xlsx");


                  //---------------------------------------------------------------------------------------------------
                  //2.  Interpret file
                  List<Deal> incomingDeals = new List<Deal>(10000);  //preset the list size (slightly quicker than growing it each time)
                  List<Deal> deliveredDeals = new List<Deal>(1000);
                  List<Person> newPeople = new List<Person>(10);

                  HeadersAndRows rowsFromHelper = GetDataFromFilesService.GetExcelFileContents(newFilepath, 0, 1, null);

                  List<string> headers = rowsFromHelper.headers;
                  List<List<string>> rows = rowsFromHelper.rows;

                  int orderTypeId = dbOrderTypes.Find(x => x.Description == "Retail").Id;
                  if (fileName.Contains("MOTABILITY")) { orderTypeId = dbOrderTypes.Find(x => x.Description == "Motability").Id; }
                  else if (fileName.Contains("TRADE")) { orderTypeId = dbOrderTypes.Find(x => x.Description == "Trade").Id; }
                  else if (fileName.Contains("FLEET")) { orderTypeId = dbOrderTypes.Find(x => x.Description == "Fleet").Id; }
                  else if (fileName.Contains("COMMERCIAL")) { orderTypeId = dbOrderTypes.Find(x => x.Description == "Commercial").Id; }
                  else if (fileName.Contains("CORPORATE")) { orderTypeId = dbOrderTypes.Find(x => x.Description == "Corporate").Id; }

                  int vehicleTypeId = vehicleTypes.Find(x => x.Description == "New").Id;
                  if (fileName.Contains("USED")) { vehicleTypeId = vehicleTypes.Find(x => x.Description == "Used").Id; }


                  int?[] siteIdsBatchA = { 8, 1, 2 };
                  int?[] siteIdsBatchB = { 3, 4, 5 };
                  int?[] siteIdsBatchC = { 17, 18, 19 };
                  int?[] siteIdsBatchD = { 20, 16, 15 };
                  int?[] siteIdsBatchE = { 14, 12, 13 };
                  int?[] siteIdsBatchF = { 10, 11, 6 };
                  int?[] siteIdsBatchG = { 7, 9 };

                  List<int?> sitesInFile = new List<int?>();
                  if (fileName.Contains("ORDERS_A")) { sitesInFile = siteIdsBatchA.ToList(); }
                  else if (fileName.Contains("ORDERS_B")) { sitesInFile = siteIdsBatchB.ToList(); }
                  else if (fileName.Contains("ORDERS_C")) { sitesInFile = siteIdsBatchC.ToList(); }
                  else if (fileName.Contains("ORDERS_D")) { sitesInFile = siteIdsBatchD.ToList(); }
                  else if (fileName.Contains("ORDERS_E")) { sitesInFile = siteIdsBatchE.ToList(); }
                  else if (fileName.Contains("ORDERS_F")) { sitesInFile = siteIdsBatchF.ToList(); }
                  else if (fileName.Contains("ORDERS_G")) { sitesInFile = siteIdsBatchG.ToList(); }
                  else sitesInFile = siteIdsBatchA.Concat(siteIdsBatchB).Concat(siteIdsBatchC).Concat(siteIdsBatchD).Concat(siteIdsBatchE).Concat(siteIdsBatchF).Concat(siteIdsBatchG).ToList();

                  newPeople = ReadNames(rows, headers);

                  if (newPeople.Count > 0)
                  {
                     try
                     {
                        db.People.AddRange(newPeople);
                        db.SaveChanges();

                        // Get list that includes this new person
                        dbPeople = db.People.ToList();

                     }
                     catch (Exception err)
                     {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed adding new people range {err.ToString()}";
                        errorCount++;
                     }

                  }

                  incomingDeals = ReadDealRows(rows, headers, fileName, orderTypeId);
                  DateTime finishedInterpetFile = DateTime.UtcNow;




                  //---------------------------------------------------------------------------------------------------
                  //3. Find New items and add them to db

                  // If rows == empty, will be empty
                  var newItems = incomingDeals.Except(dbDeals, new DealEnquiryNoComp());

                  if (fileName.Contains("ORDERS") && incomingDeals.Count > 0)
                  {

                     newCount = newItems.Count();

                     foreach (Deal deal in newItems)
                     {
                        if (dbDeals.Select(x => x.StockNumber).Contains(deal.StockNumber)) { deal.StockNumber = deal.StockNumber + "!"; };
                     }

                     try
                     {
                        db.Deals.AddRange(newItems);  //add them all in one go
                     }
                     catch (Exception err)
                     {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed addingNewDeals range {err.ToString()}";
                        errorCount++;
                     }

                  }

                  //---------------------------------------------------------------------------------------------------
                  //4.  Find any removed ones and set to removed, plus generate a diff

                  if (fileName.Contains("ORDERS") && incomingDeals.Count > 0)
                  {
                     List<Deal> removed = new List<Deal>();

                     var today8am = DateTime.Today.AddHours(8);

                     if (fileName.Contains("DAILY"))
                     {

                        DateTime earliestIncomingDealOrderDate = incomingDeals.OrderBy(x => x.OrderDate).First().OrderDate;
                        DateTime startDate = new DateTime(earliestIncomingDealOrderDate.Year, earliestIncomingDealOrderDate.Month, 1);
                        DateTime endDate = startDate.AddMonths(1).AddDays(-1);

                        removed = dbDeals.Where(r => !r.IsRemoved &&
                            orderTypeId == r.OrderType_Id && //only look at deals in the db for the order type in the file
                            ((fileName.Contains("NEW") && r.VehicleType.Type == "New") || (fileName.Contains("USED") && r.VehicleType.Type != "New") || (!fileName.Contains("NEW") && !fileName.Contains("USED"))) &&
                            r.OrderDate >= startDate && r.OrderDate <= endDate &&
                            !(r.LastUpdated.Date == DateTime.Today && r.LastUpdated < today8am) // exclude deals updated today before 8am (dealt with by FleetLoader)
                            ).Except(incomingDeals, new DealEnquiryNoComp()).ToList();
                     }
                     else
                     {
                        removed = dbDeals.Where(r => !r.IsRemoved &&
                            sitesInFile.Contains(r.Site_Id) && // only look at deals in the db for the sites in the file
                            orderTypeId == r.OrderType_Id && // only look at deals in the db for the order type in the file
                            ((fileName.Contains("NEW") && r.VehicleType.Type == "New") ||
                             (fileName.Contains("USED") && r.VehicleType.Type != "New") ||
                             (!fileName.Contains("NEW") && !fileName.Contains("USED"))) &&
                            r.OrderDate > fileDate.AddMonths(-12).AddDays(1) && // only consider removing deals whose order date is in the last 12 months
                            !(r.LastUpdated.Date == DateTime.Today && r.LastUpdated < today8am) // exclude deals updated today before 8am (dealt with by FleetLoader)
                        ).Except(incomingDeals, new DealEnquiryNoComp()).ToList();

                     }


                     foreach (var deal in removed)
                     {
                        try
                        {
                           deal.IsRemoved = true;
                           deal.RemovedDate = fileDate;
                           deal.LastUpdated = fileDate;
                           removedCount++;

                           //generate a diff also
                           Diff newDiff = new Diff()
                           {
                              Model = "Deal",
                              ModelIdent = deal.EnquiryNumber.ToString(),
                              Key = "IsRemoved",
                              OldValue = "False",
                              NewValue = "True",
                              UpdateDate = fileDate,
                           };

                           diffs.Add(newDiff);
                        }

                        catch (Exception err)
                        {
                           logMessage.FailNotes = logMessage.FailNotes + $" failed on removing item {deal.EnquiryNumber}" + err.ToString();
                           errorCount++;
                        }

                     }

                     if (removed.Count > 100) throw new Exception("Too many removed!");

                  }




                  //---------------------------------------------------------------------------------------------------
                  //5. Use differ to update changed items and create diffs

                  List<Deal> sameItems = incomingDeals.Except(newItems, new DealEnquiryNoComp()).ToList(); //quick piece to ensure we don't bother trying to diff Deals we already know are new

                  List<Diff> changedDiffs = DiffAndUpdate(sameItems);

                  changedCount = changedDiffs.Count();

                  //add diffs to db
                  try
                  {
                     foreach (Diff diff in changedDiffs)
                     {
                        diff.UpdateDate = fileDate;
                     }
                     List<Diff> diffsToPersist = changedDiffs.ToList();
                     db.Diffs.AddRange(diffsToPersist);  //add them all in one go
                  }
                  catch (Exception err)
                  {
                     logMessage.FailNotes = logMessage.FailNotes + $"Failed adding diffs to DB {err.ToString()}";
                     errorCount++;
                  }

                  //5.1 Detect undelivered deals

                  if (fileName.Contains("DELIVERED") && incomingDeals.Count > 0)
                  {

                     DateTime firstDayOfReport = incomingDeals.Select(x => x.ActualDeliveryDate).Min() ?? DateTime.Today.AddYears(999);

                     List<Deal> alreadyDelivered = dbDeals.Where(d => d.IsDelivered == true &&
                         d.ActualDeliveryDate >= firstDayOfReport &&
                         sitesInFile.Contains(d.Site_Id) &&
                         orderTypeId == d.OrderType_Id &&
                         vehicleTypeId == d.VehicleType_Id
                         ).ToList();

                     List<Deal> unDeliveredDeals = alreadyDelivered.Except(incomingDeals, new DealEnquiryNoComp()).ToList();


                     for (int i = 0; i < unDeliveredDeals.Count(); i++)
                     {
                        unDeliveredDeals[i].IsDelivered = false;
                     }


                     List<Diff> undeliveredDiffs = DiffAndUpdate(unDeliveredDeals);

                     changedCount += unDeliveredDeals.Count();


                     foreach (var deal in unDeliveredDeals)
                     {
                        try
                        {
                           Diff newDiff = new Diff()
                           {
                              Model = "Deal",
                              ModelIdent = deal.EnquiryNumber.ToString(),
                              Key = "IsDelivered",
                              OldValue = "True",
                              NewValue = "False",
                              UpdateDate = fileDate,
                           };

                           diffs.Add(newDiff);
                        }

                        catch (Exception err)
                        {
                           logMessage.FailNotes = logMessage.FailNotes + $" failed on removing item {deal.EnquiryNumber}" + err.ToString();
                           errorCount++;
                        }

                     }
                  }

                  //---------------------------------------------------------------------------------------------------
                  //6. update globalParams with last updatedDate
                  DateTime updateDate = DateTime.UtcNow;

                  try
                  {
                     //GlobalParam ordersUpdateDate = dbGlobalParams.FirstOrDefault(x => x.Description == "ordersUpdateDate");
                     //ordersUpdateDate.DateFrom = fileDate;
                     //updateDate = fileDate;

                     //also record the last time we saved this type
                     string ordersType = fileName.Substring(16, fileName.Length - 21).Replace("_DAILY", "");
                     GlobalParam orderTypeUpdateDate = dbGlobalParams.FirstOrDefault(x => x.Description == $"{ordersType}UpdateDate");
                     orderTypeUpdateDate.DateFrom = fileDate;
                  }
                  catch (Exception err)
                  {
                     logMessage.FailNotes = logMessage.FailNotes + $"Failed to update globalParam updateDate {err.ToString()}";
                     errorCount++;
                  }


                  //---------------------------------------------------------------------------------------------------
                  //6.1 Update dailystats
                  //DealSummaryStatsService dss = new DealSummaryStatsService();
                  //dss.UpdateWebAppFiguresForNewDeals(); //note requires Latest Accounting Date to be accurate within GlobalParams



                  //---------------------------------------------------------------------------------------------------
                  //7. Update log message
                  logMessage.FinishDate = DateTime.UtcNow;
                  logMessage.ProcessedCount = incomingDeals.Count;
                  logMessage.AddedCount = newCount;
                  logMessage.RemovedCount = removedCount;
                  logMessage.ChangedCount = changedCount;
                  logMessage.IsCompleted = true;
                  logMessage.ErrorCount = errorCount;
                  logMessage.StartCount = openingDbCount;
                  int closingDbCount = 0;





                  //---------------------------------------------------------------------------------------------------
                  //8. Save changes
                  try
                  {
                     db.SaveChanges();
                     closingDbCount = 0;
                     logMessage.FinishCount = closingDbCount;
                  }
                  catch (Exception err)
                  {
                     logMessage.FailNotes = logMessage.FailNotes + "Failed to save to DB " + err.ToString();
                     errorCount++;
                  }
                  DateTime finishedUpdateDb = DateTime.UtcNow;



                  //---------------------------------------------------------------------------------------------------
                  //9. Log result
                  Logger.Info($"[{DateTime.UtcNow}] | Result: Started with {openingDbCount} item(s), interpreted {incomingDeals.Count} item(s),  found {newCount} new, {removedCount} removed and {changedCount} changes.  Closed with {closingDbCount} item(s)");





                  //---------------------------------------------------------------------------------------------------
                  //10. Move file
                  try
                  {
                     File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.xlsx", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xlsx"));

                     if (errorCount > 0)
                     {
                        //we have errors so use the reporter
                        logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                        await CentralLoggingService.ReportError("Deals", logMessage, true);
                     }
                     else
                     {
                        //completed succesfully, save log and remove lockfile
                        logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                        logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                        logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;
                        logMessage.DealerGroup_Id = 3;
                        db.LogMessages.Add(logMessage);
                        db.SaveChanges();
                     }
                  }

                  catch (Exception err)
                  {
                     logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                     errorCount++;
                     await CentralLoggingService.ReportError("Deals", logMessage);
                     //
                  }
               }


               //---------------------------------------------------------------------------------------------------
               //Global error catcher
               catch (Exception err)
               {
                  await EmailerService.SendErrorMail("Vindis deals loader failed", err.StackTrace);
                  logMessage.FailNotes = logMessage.FailNotes + $"General failure " + err.ToString();
                  errorCount++;
                  logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                  await CentralLoggingService.ReportError("Deals", logMessage);
               }


               stopwatch.Stop();

            }
            catch (Exception err)
            {
               stopwatch.Stop();
               errorMessage = err.ToString();

            }
            finally
            {
               db.ChangeTracker.Clear();

               dbDeals = null;
               sites = null;
               standingValues = null;
               vehicleTypes = null;
               dbGlobalParams = null;
               dbPeople = null;
               dbOrderTypes = null;
               diffs = null;
               dbStocks = null;

               LocksService.VindisDeals = false;

               Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
               {
                  Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                  Project = "Loader",
                  Customer = "Vindis",
                  Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                  Task = this.GetType().Name,
                  StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                  EndDate = DateTime.UtcNow,
                  Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                  Notes = errorMessage,
                  HTML = string.Empty
               };




               await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }



            //---------------------------------------------------------------------------------------------------
            //11. trigger cache rebuild
            await UpdateWebAppService.Trigger("Deals");
            await UpdateWebAppService.Trigger("StandingData");
         }
         return;
      }











   }


}
