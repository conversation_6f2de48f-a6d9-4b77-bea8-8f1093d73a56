//core angular
import { Component, OnInit } from '@angular/core';
//services
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
//pipes and interceptors
import { Month, selectableItem, DateSelectionObject, ServiceSalesSiteRow } from '../../model/main.model';
import { ServiceSummaryService } from './serviceSummary.service';

@Component({
  selector: 'app-serviceSummary',
  templateUrl: './serviceSummary.component.html',
  styleUrls: ['./serviceSummary.component.scss']
})

export class ServiceSummaryComponent implements OnInit {
  months: Array<Month>;
  monthsOffsetNumber: number = 0;
  channelsDropdownList: selectableItem[];
  showAllChannels: boolean = true;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: ServiceSummaryService
  ) { }

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });
    this.getDefaultSelectedChannels();
    this.selections.initiateServiceSummary();
    this.service.getSalesDataForAllSites();
  }

  makeMonths(offset?: number) {
    this.months = this.constants.makeMonths(this.monthsOffsetNumber, offset);
  }

  selectMonth(object: DateSelectionObject, date: Date) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });

    this.constants.selectMonth(object, date);
    this.selections.serviceSummary.month.name = this.selections.serviceSummary.month.startDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
    this.getData()
  
  }

  getData(){
    if (this.selections.serviceSummary.showDailyView){
      this.service.getServiceDailySales(true)
    } else{
      this.service.getSalesDataForAllSites(true);
    if (this.selections.serviceSummary.chosenSite) {
      this.service.getSalesDataForSite(true);
    }
  }
  }

  
  changeMonth(object: DateSelectionObject, changeAmount: number) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });

    let startDate: Date = this.constants.addMonths(object.startDate,changeAmount);
    this.constants.selectMonth(object, startDate);
    this.selections.serviceSummary.month.name = this.selections.serviceSummary.month.startDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });

    this.getData();

   
  }

  selectSite(site: ServiceSalesSiteRow) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });

    this.selections.serviceSummary.chosenSite = site.Label;

    if (site.IsRegion) {
      this.selections.serviceSummary.sitesIds = this.service.serviceSalesSitesRows.filter(x => x.RegionDescription == site.Label).map(x => x.SiteId);
    } else if (site.IsTotal) {
      this.selections.serviceSummary.sitesIds = this.service.serviceSalesSitesRows.filter(x => x.SiteId != 0).map(x => x.SiteId);
    } else {
      this.selections.serviceSummary.sitesIds = [site.SiteId];
    }

    this.service.getSalesDataForSite();
  }

  getDefaultSelectedChannels(): selectableItem[]
  {
    this.channelsDropdownList = [];

    if(this.constants.environment.customer == 'Vindis')
    {
      this.constants.environment.serviceChannels.filter(x => !x.isTotal).forEach(f => {
        this.channelsDropdownList.push(
          { label: f.displayName, isSelected: f.isLabour }
        )
      })
    }
    else
    {

      this.constants.environment.serviceChannels.filter(x => !x.isTotal).forEach(f => {
        this.channelsDropdownList.push(
          { label: f.displayName, isSelected: this.showAllChannels }
        )
      })

    }

    return this.channelsDropdownList;

  }
  generateChannelList() {

    //this.channelsDropdownList = this.getDefaultSelectedChannels();

    // If we already have some channels selected
    this.channelsDropdownList.forEach(f => {
      if (this.selections.serviceSummary.channelNames.indexOf(f.label) > -1) {
        f.isSelected = true;
      }
    })

  }

  selectChannels() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });
    this.selections.serviceSummary.channelNames = this.channelsDropdownList.filter(e => e.isSelected).map(e => e.label);
    this.getData()
  }

  selectTimeOption(timeOption: string) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });
    this.selections.serviceSummary.timeOption = timeOption;
    this.getData()
  }

  channelChosenLabel() {
    if (this.selections.serviceSummary.channelNames.length == 1) {
      return this.selections.serviceSummary.channelNames[0];
    } else if (this.selections.serviceSummary.channelNames.length == 0) {
      return 'None';
    } else {
      return 'Channels';
    }
  }

  toggleItem(toggleTotal: boolean, item?: selectableItem) {

    if (!toggleTotal) {
      this.showAllChannels = false;
      return item.isSelected = !item.isSelected;
    }

    this.showAllChannels = !this.showAllChannels;
    this.channelsDropdownList.forEach(channel => {
      channel.isSelected = this.showAllChannels;
    })
  }
  
  chooseTableType(type:string){
    if(type == 'Daily')
    {
      this.selections.serviceSummary.showDailyView = true;
      this.service.getServiceDailySales(true)
    }
    else{
      this.selections.serviceSummary.showDailyView = false;
      this.service.getSalesDataForAllSites(true)
    }

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading})
    this.selections.serviceSummary.tableType=type;
  }

}
