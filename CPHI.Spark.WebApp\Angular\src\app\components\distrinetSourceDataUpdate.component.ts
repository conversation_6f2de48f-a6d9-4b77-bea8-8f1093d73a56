import { Component, Input } from '@angular/core';
import { ConstantsService } from '../services/constants.service';

@Component({
  selector: 'distrinetSourceDataUpdate',
  template: `
    
      <span class="hoverOver" 
      [ngbPopover]="popoverContent" 
      triggers="mouseenter:mouseleave" 
      popoverTitle="{{constants.translatedText.LastUpdated}}" 
      placement="bottom" 
      [openDelay]="300" 
      [closeDelay]="500" 
      container="body">
    {{ type }}
    </span>

    <ng-template #popoverContent>
    {{constants.translatedText.DataLastUpdated}} <br><br>
    Madrid: {{ updateTimeMadrid | cph:'shortTimeAM':0 }} {{ updateTimeMadrid | cph:'date':0 }} <br>
    Valencia: {{ updateTimeValencia | cph:'shortTimeAM':0 }} {{ updateTimeValencia | cph:'date':0 }}
    </ng-template>
  `,
    styles: [`
    
    .hoverOver{padding:15px;} 


    `]
})

export class DistrinetSourceDataUpdateComponent {
  @Input() public type: string;

  public updateTimeValencia: string | Date;
  public updateTimeMadrid: string | Date;

  constructor(
    public constants: ConstantsService

  ) {

  }

  ngOnInit()
  {
    this.setValues(this.type);
  }

  setValues(type: string)
  {
    if(type == this.constants.translatedText.Book)
    {
      this.updateTimeMadrid = this.constants.LastUpdatedDates.DistrinetBookMadrid;
      this.updateTimeValencia = this.constants.LastUpdatedDates.DistrinetBookValencia;
    }
    else if (type == this.constants.translatedText.Orders_Capitals)
    {
      this.updateTimeMadrid = this.constants.LastUpdatedDates.DistrinetOrdersMadrid;
      this.updateTimeValencia = this.constants.LastUpdatedDates.DistrinetOrdersValencia;
    }
    else if (type == this.constants.translatedText.StockAsStock)
    {
      this.updateTimeMadrid = this.constants.LastUpdatedDates.DistrinetStockMadrid;
      this.updateTimeValencia = this.constants.LastUpdatedDates.DistrinetStockValencia;
    }

  }
  
}

