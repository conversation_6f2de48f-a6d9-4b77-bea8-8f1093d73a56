import { Component, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ColDef, GridApi, GridOptions } from 'ag-grid-community';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { SiteVM } from '../../../model/main.model';
import { BookingsBarComponent } from '../../../_cellRenderers/bookingsBar.component';
import { ServiceBookingsService } from '../serviceBookings.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';




@Component({
  selector: 'serviceBookingsTable',
  templateUrl: "./serviceBookingsTable.component.html",
  styleUrls: ['./../../../../styles/components/_agGrid.scss'],
})



export class ServiceBookingsTableComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  @Input() public isRegional: boolean;
  @Output() clickedSite = new EventEmitter<SiteVM>();
  @HostListener("window:resize", [])
  private onresize(event) {
    this.service.selections.screenWidth = window.innerWidth;
    this.service.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  currentRowHeight: number;

  showGrid: boolean;

  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;


  mainTableGridOptions: GridOptions;

  flipCols: Array<string>;


  constructor(
    public service: ServiceBookingsService,
    private gridHelpersService: AGGridMethodsService

  ) { }


  ngOnInit() {
    //launch control

    this.initParams();

    //subscribe to bookings summary update
    this.service.selections.bookingsSummary.updateEventEmitter
      .pipe(takeUntil(this.destroy$))
      .subscribe((result: boolean) => {

        if (this.gridApi) {

          let colDefs = this.provideColDefs()// this.mainTableGridOptions.columnDefs

          // Ensures the header names update when date is changed
          this.gridApi.setColumnDefs(null);
          this.gridApi.setColumnDefs(colDefs)
          this.gridApi.setRowData(this.getRowData())
          this.gridApi.setPinnedBottomRowData(this.getPinnedBottomRowData())
          this.resizeGrid();
        }
      })
  }


  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }



  initParams() {

    this.flipCols = [];
    //this.service.agGrid.topBottomHighlights = [];

    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.service.agGrid.getContextMenuItems(params),
      getLocaleText: (params: any) => this.service.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.getRowData(),
      pinnedBottomRowData: this.getPinnedBottomRowData(),
      onGridReady: (params) => this.onGridReady(params),
      domLayout: 'autoHeight',
      getMainMenuItems: (params) => this.service.agGrid.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlights),
      onFirstDataRendered: () => { this.showGrid = true; this.service.selections.triggerSpinner.next({ show: false }); },
      onCellMouseOver: (params) => {
      },
      onCellClicked: (params) => {
        this.onCellClick(params);
      },
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.service.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        cellClass: 'ag-right-aligned-cell',
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.service.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },

      columnDefs: this.provideColDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }

  }

  getPinnedBottomRowData() {
    return this.service.selections.bookingsSummary.sites.filter(s => s.IsTotal === true)
  }

  getRowData() {

    if (this.isRegional) {
      return this.service.selections.bookingsSummary.sites.filter(s => s.IsRegion === true)
    }
    else {
      return this.service.selections.bookingsSummary.sites.filter(s => s.IsRegion === false && s.IsTotal === false)
    }
  }


  provideColDefs(): ColDef[] {

    let columnDefs = []

    columnDefs.push({ headerName: '', field: 'Label', colId: 'Label', width: 120, type: 'label', })

    for (let i = 0; i < 7; i++) {

      let dayLabel: string;

      if (this.service.selections.bookingsSummary.sites[0].DailyBookingSummaries[i]) {
        dayLabel = this.service.cphPipe.transform(this.service.selections.bookingsSummary.sites[0].DailyBookingSummaries[i].DayDate, 'day', 0)
      }
      else {
        continue;
      }

      if (!dayLabel.includes('Sun')) {
        columnDefs.push(
          {
            headerName: dayLabel, colId: `${dayLabel}day`, width: 90, type: "number", children:
              [
                {
                  headerName: 'Jobs', children: [
                    { headerName: "", field: `DailyBookingSummaries.${i}.JobCount`, colId: `${dayLabel}JobCount`, width: 50, type: "number", },]
                },

                {
                  headerName: "Hours", children: [
                    { headerName: 'Booked', colId: `${dayLabel}HoursBooked`, width: 40, type: "numberDeepField", field: `DailyBookingSummaries.${i}.HoursBooked` },
                    { headerName: 'Capacity', colId: `${dayLabel}HoursCapacity`, width: 40, type: "numberDeepField", field: `DailyBookingSummaries.${i}.HoursCapacity` }
                  ],
                },
                {
                  headerName: 'Booked', children: [
                    { headerName: "", cellRenderer: BookingsBarComponent, field: `DailyBookingSummaries.${i}.BookedPercent`, colId: `${dayLabel}BookedPercent`, width: 60, type: "labelPercent", },
                  ]
                }

              ]
          }
        )
      }

    }

    columnDefs.push(
      {
        headerName: '7 Day Total', colId: 'sevenDayTotal', width: 140, cellClass: 'greyPanel', type: "number", children:
          [
            { headerName: "Jobs", cellClass: 'greyPanel', field: `SevenDaySummary.JobCount`, colId: "SevenDaySummary.JobCount", width: 45, type: "number", },
            {
              headerName: "Hours", children: [
                { headerName: 'Booked', cellClass: 'greyPanel', field: `SevenDaySummary.HoursBooked`, colId: "SevenDaySummary.HoursBooked", width: 45, type: "number", },
                { headerName: 'Capacity', cellClass: 'greyPanel', field: `SevenDaySummary.HoursCapacity`, colId: "SevenDaySummary.HoursCapacity", width: 45, type: "number", },
              ]
            },
            { headerName: "Booked", cellClass: 'greyPanel', cellRenderer: BookingsBarComponent, field: `SevenDaySummary.BookedPercent`, colId: "SevenDaySummary.BookedPercent", width: 60, type: "labelPercent", },
          ]
      }
    )

    // if (this.service.constants.environment.serviceBookingsTable.showPrepHours) columnDefs.push(
    //   { headerName: "Prep Hours", cellClass: 'greyPanel', field: ``, colId: "", width: 50, type: "number", },
    // )

    return columnDefs
  }

  shouldHideCol(): boolean {
    return true;
  }






  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }
  }


  onCellClick(params) {
    if (this.service.constants.environment.serviceBookingsTable.clickSiteEnable) {
      this.clickedSite.next(params.data);
    }
  }





  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);

    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods

    this.service.selections.triggerSpinner.next({ show: false });



  }





  highlightTopBottom(colDef: any, topBottomN: number) {
    this.service.agGrid.highlightTopBottom(colDef, topBottomN, this.flipCols, this.gridApi)
  }


  clearHighlighting(colDef: ColDef) {
    this.service.agGrid.clearHighlighting(colDef, this.gridApi);

  }


  refreshCells() {
    if (this.gridApi) {
      this.gridApi.refreshCells();
    }
  }

  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()

    this.service.excel.createSheetObject(tableModel, 'Service Bookings', 1, 1);
  }




}
