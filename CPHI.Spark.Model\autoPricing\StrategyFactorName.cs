﻿namespace CPHI.Spark.Model
{
   public enum StrategyFactorName
   {
      RetailRatingBand,
      DaysListedBand,
      OnBrandCheck,
      RetailerName,
      MatchCheapestCompetitor,
      DaysToSell,
      MinimumProfit,
      RoundToNearest,
      RoundToPriceBreak,
      RR_DL_Matrix,
      DaysListed,

      //SPK-4670 use days in stock
      DaysInStock,
      DaysInStockBand,
      RR_DS_Matrix,
      RR_DB_Matrix,
      DTS_DL_Matrix,

      //SPK-4793 new layer, change to DTS
      ValuationChangeUntilSell,

      //SPK-5168
      SpecificColour,
      AgeAndOwners,

      //SPK-5200
      AchieveMarketPositionScore,

      //SPK-5387
      WholesaleAdjustment,
      RetailRating10sBand,
      MakeFuelType,
      MakeAgeBand,

      Brand,
      ModelName,
      Mileage,

      //New factor for performance rating score
      PerformanceRatingScore,

      //New factor for minimum price position
      MinimumPricePosition
   }

}
