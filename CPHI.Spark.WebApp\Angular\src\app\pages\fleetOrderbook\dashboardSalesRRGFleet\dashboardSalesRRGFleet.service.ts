
import { EventEmitter, Injectable } from '@angular/core';
import { FleetOrderbookSummaryRow } from 'src/app/model/FleetOrderbookSummaryRow';
import { VNTileParams } from "../../../model/VNTileParams";
import { FleetOrderbookService } from '../fleetOrderbook.service';
import { VNTileTableRow } from 'src/app/model/VNTileTableRow';
import { BIChartTileDataType, TileType } from 'src/app/components/biChartTile/biChartTile.component';

@Injectable({
  providedIn: 'root'
})


export class DashboardSalesRRGFleetService {



  isStocksSelected: boolean = true;
  isBookSelected: boolean = true;

  chosenMonth: Date;


  //months: Date[]

  highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();




  constructor(

    public service: FleetOrderbookService

  ) { }



  initParams() {


    this.service.dashboardFilterChoices = [
      { FieldName: 'AgeAtEom', FieldNameTranslation: 'Age', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'FonDescription', FieldNameTranslation: 'Fon Description', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'Status', FieldNameTranslation: 'Statuses', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'Customer', FieldNameTranslation: 'Customers', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },  //customer
      { FieldName: 'DeliveryMonth', FieldNameTranslation: 'Delivery Months', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'Model', FieldNameTranslation: 'Models', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'OwningMainDealer', FieldNameTranslation: 'Owning Main Dealers', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'AgeBandAtEom', FieldNameTranslation: 'AgeBandAtEom', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'IsInvoicedInMonth', FieldNameTranslation: 'IsInvoicedInMonth', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'IsRegisteredInMonth', FieldNameTranslation: 'IsRegisteredInMonth', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'IsMatchedInMonth', FieldNameTranslation: 'IsMatchedInMonth', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },


    ];

    this.service.dashboardHighlightChoices = [
      { FieldName: 'AgeAtEom', FieldNameTranslation: 'Age', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'FonDescription', FieldNameTranslation: 'Fon Description', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'Status', FieldNameTranslation: 'Statuses', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'Customer', FieldNameTranslation: 'Customers', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },  //customer
      { FieldName: 'DeliveryMonth', FieldNameTranslation: 'Delivery Months', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'Model', FieldNameTranslation: 'Models', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'OwningMainDealer', FieldNameTranslation: 'Owning Main Dealers', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'AgeBandAtEom', FieldNameTranslation: 'AgeBandAtEom', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'IsInvoicedInMonth', FieldNameTranslation: 'IsInvoicedInMonth', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'IsRegisteredInMonth', FieldNameTranslation: 'IsRegisteredInMonth', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
      { FieldName: 'IsMatchedInMonth', FieldNameTranslation: 'IsMatchedInMonth', DataType: BIChartTileDataType.label, IsDate: false, ChosenValues: [] },
    ];

  }



  getPageParams(): VNTileParams {
    return {
      highlightChoices: this.service.dashboardHighlightChoices,
      filterChoices: this.service.dashboardFilterChoices,
      //rawData: this.rawData,
      //rawDataFiltered: this.rawDataFiltered,
      //rawDataHighlighted: this.rawDataHighlighted,
      filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
      highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
      updateThisPicker: this.service.dashboardRefreshFilterListsEmitter,
      updateThisTile: this.service.dashboardRefreshTileEmitter,
      parentMethods: {
        buildRows: (fieldName, dataType, tileType) => this.buildTableRows(fieldName, dataType, tileType, this.service.dashboardRawDataFiltered, this.service.dashboardRawDataHighlighted),
        highlightRow: (row, fieldName) => this.highlightRow(row, fieldName),
        provideItemsList: (fieldName, isDateField) => this.provideItemsList(fieldName, isDateField),
      }
    }
  }



  highlightRow(row: VNTileTableRow, fieldName: string) {

    let userChoice = this.service.dashboardHighlightChoices.find(x => x.FieldName === fieldName);

    let isItemSelected = userChoice.ChosenValues.length === 0 || userChoice.ChosenValues.includes(row.Label);
    if (userChoice.ChosenValues.length === 0) {
      this.service.dashboardHighlightChoices.find(x => x.FieldName === fieldName).ChosenValues = [row.Label];
    } else if (isItemSelected) {
      userChoice.ChosenValues = userChoice.ChosenValues.filter(x => x !== row.Label)
    } else {
      userChoice.ChosenValues.push(row.Label)
    }

  }



  isHighlightFiltersOn() {
    let isHighlights = false;
    let i = 0;
    while (!isHighlights && i < this.service.dashboardHighlightChoices.length) {
      isHighlights = this.service.dashboardHighlightChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isHighlights;
  }

  isFiltersOn() {
    if (!this.service.dashboardFilterChoices) { return false; }
    let isFilters = false;
    let i = 0;
    while (!isFilters && i < this.service.dashboardFilterChoices.length) {
      isFilters = this.service.dashboardFilterChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isFilters;
  }



  clearHighlights() {
    this.service.dashboardHighlightChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.highlightItems();
    this.service.dashboardRefreshTileEmitter.emit();
  }
  clearFilters() {
    this.service.dashboardFilterChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.filterItems();
    this.service.dashboardRefreshTileEmitter.emit();
  }






  filterItems() {
    //have chosen ok from a dropdown picker
    this.service.dashboardRawDataFiltered = this.service.filterData(this.service.dashboardRawData, this.service.dashboardFilterChoices)
    this.highlightItems()
  }

  highlightItems() {
    //have clicked a row in a tile.
    this.service.dashboardRawDataHighlighted = this.service.filterData(this.service.dashboardRawDataFiltered, this.service.dashboardHighlightChoices)
    this.service.dashboardRefreshTileEmitter.emit()
  }






  provideItemsList(fieldName: string, isDateField: boolean) {
    if (!this.service.dashboardRawData) { return [] }

    if (isDateField) {
      return [...new Set(this.service.dashboardRawData.map(x => this.service.cphPipe.transform(x[fieldName], 'date', 0)))]
    } else {
      return [...new Set(this.service.dashboardRawData.map(x => x[fieldName]))]
    }
  }

  buildTableRows(fieldName: string, dataType: BIChartTileDataType, tileType: TileType, dataFiltered: FleetOrderbookSummaryRow[], dataHighlighted: FleetOrderbookSummaryRow[]) {

    let tableRows: VNTileTableRow[] = []
    if (dataType === BIChartTileDataType.month) { tableRows = this.buildTableRowsMonthsBasis(fieldName, dataFiltered, dataHighlighted) }
    //else if(tileType === 'BigNumber'){tableRows = this.buildTableRowsBigNumberBasis(fieldName,dataFiltered,dataHighlighted)}
    else if (fieldName == 'StockCategory') { tableRows = this.buildTableRowsAgeEOMByCategory(dataFiltered, dataHighlighted) }
    else { tableRows = this.buildTableRowsNonDatesBasis(fieldName, dataFiltered, dataHighlighted) }

    return tableRows;//.slice(0, 31);
  }




  buildTableRowsAgeEOMByCategory(dataFiltered: FleetOrderbookSummaryRow[], dataHighlighted: FleetOrderbookSummaryRow[]): VNTileTableRow[] {
    let tableRows: VNTileTableRow[] = [];

    //go through filteredData to find unique labels and countup
    let labels: string[] = [];
    dataFiltered.forEach(item => {
      const itemLabel = item['StockCategory'];

      let labelsIndex = labels.findIndex(x => x === itemLabel);
      if (labelsIndex === -1) {
        //don't already have it so create new
        labels.push(itemLabel)
        tableRows.push({ Label: itemLabel, FilteredTotal: item.Count, filteredTotalValue: item.AgeAtEom, highlightedTotalValue: 0, HighlightedTotal: 0 })
      } else {
        tableRows[labelsIndex].FilteredTotal += item.AgeBandAtEom == 'No Invoice Date' ? 0 : item.Count;  //if not invoiced, don't total up units
        tableRows[labelsIndex].filteredTotalValue += item.AgeAtEom;
      }
    })

    //find out values to show
    dataHighlighted.forEach(item => {
      const itemLabel = item['StockCategory'];
      const indexInLabels = labels.findIndex(x => x === itemLabel);
      //should exist or something has gone wrong as orderDataHighlighted should be subset of orderDataFiltered
      tableRows[indexInLabels].HighlightedTotal += item.AgeBandAtEom == 'No Invoice Date' ? 0 : item.Count;
      tableRows[indexInLabels].highlightedTotalValue += item.AgeAtEom;
    })

    //divide TOTAL figures to become averages
    tableRows.map(row => {
      row.FilteredTotal = row.FilteredTotal > 0 ? row.filteredTotalValue / row.FilteredTotal : 0;
      row.HighlightedTotal = row.HighlightedTotal > 0 ? row.highlightedTotalValue / row.HighlightedTotal : 0;
    })

    tableRows = tableRows.sort((a, b) => b.FilteredTotal - a.FilteredTotal)

    return tableRows;
  }



  buildTableRowsNonDatesBasis(fieldName: string, dataFiltered: FleetOrderbookSummaryRow[], dataHighlighted: FleetOrderbookSummaryRow[]): VNTileTableRow[] {
    let tableRows: VNTileTableRow[] = [];
    if(fieldName==='StockCategory'){
      console.log('here')
    }

    //go through filteredData to find unique labels and countup
    let labels: string[] = [];
    dataFiltered.forEach(item => {
      const itemLabel = item[fieldName];
      let labelsIndex = labels.findIndex(x => x === itemLabel);
      if (labelsIndex === -1) {
        //don't already have it so create new
        labels.push(itemLabel)
        tableRows.push({ Label: itemLabel, FilteredTotal: item.Count, HighlightedTotal: 0 })
      } else {
        tableRows[labelsIndex].FilteredTotal += item.Count;
      }
    })

    //find out values to show
    dataHighlighted.forEach(item => {
      const itemLabel = item[fieldName];
      const indexInLabels = labels.findIndex(x => x === itemLabel);
      //should exist or something has gone wrong as orderDataHighlighted should be subset of orderDataFiltered
      tableRows[indexInLabels].HighlightedTotal += item.Count;
    })

    tableRows = tableRows.sort((a, b) => b.FilteredTotal - a.FilteredTotal)

    return tableRows;

  }




  buildTableRowsMonthsBasis(fieldName: string, filteredRows: FleetOrderbookSummaryRow[], highlightedRows: FleetOrderbookSummaryRow[]): VNTileTableRow[] {
    //similar approach to non-dates, but looks odd if we have gaps in the days, so we find the earliest date then iterate every day at a time since then even if it has no data

    let tableRows: VNTileTableRow[] = [];
    //update these new rows with orderData
    const filteredRowsByDate = filteredRows.sort((a, b) => {
      let aTime = a[fieldName] ? a[fieldName].getTime() : Number.MAX_VALUE;
      let bTime = b[fieldName] ? b[fieldName].getTime() : Number.MAX_VALUE;
      // Perform the sort
      return aTime - bTime;
    });

    let currentTableRowIndex: number = 0;
    filteredRowsByDate.forEach(item => {
      let monthLabel = item[fieldName] ? this.service.cphPipe.transform(item[fieldName], 'monthNoGap', 0) : 'None';
      let existingRow = tableRows.find(x => x.Label === monthLabel)
      if (existingRow) {
        existingRow.FilteredTotal += item.Count;
      } else {
        //is new
        tableRows.push({ Label: monthLabel, FilteredTotal: item.Count, HighlightedTotal: 0 })
      }

    })



    //update with highlighted data
    const highlightedDataSorted = highlightedRows.sort((a, b) => {
      let aTime = a[fieldName] ? a[fieldName].getTime() : Number.MAX_VALUE;
      let bTime = b[fieldName] ? b[fieldName].getTime() : Number.MAX_VALUE;
      // Perform the sort
      return aTime - bTime;
    });


    currentTableRowIndex = 0;
    highlightedDataSorted.forEach(item => {
      let monthLabel = item[fieldName] ? this.service.cphPipe.transform(item[fieldName], 'monthNoGap', 0) : 'None';
      while (!!tableRows[currentTableRowIndex] && monthLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++;
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].HighlightedTotal += item.Count; }
    })

    return tableRows

  }

  public navigateToOrderbook() {

    let filterModel: any = this.createFilterModelForChosenTiles();

    this.service.currentFilterState = filterModel;
    //this.service.pivotMode = false;
    //this.service.skipNextFilterStateApplication = true;
    this.service.showTiles = false;
  }



  public navigateToOrderbookMatchedInMonth() {
    let filterModel: any = this.createFilterModelForChosenTiles();
    filterModel.IsMatchedInMonth = { filterType: 'set', values: ['true'] }
    this.service.currentFilterState = filterModel;
    //this.service.pivotMode = false;
    this.service.showTiles = false;
  }


  private createFilterModelForChosenTiles() {
    let filterModel: any = {};
    this.service.dashboardHighlightChoices.forEach(choice => {
      if (choice.ChosenValues.length > 0) {
        filterModel[choice.FieldName] = { filterType: 'set', values: choice.ChosenValues };
      }
    });
    return filterModel;
  }

  private intersectChoices(filterFranchises: string[], highlightFranchises: string[]) {
    let chosenFranchises = [];
    if (filterFranchises.length !== 0) {
      if (highlightFranchises.length !== 0) {
        chosenFranchises = filterFranchises.filter(x => highlightFranchises.includes(x));
      } else {
        chosenFranchises = filterFranchises;
      }
    } else {
      if (highlightFranchises.length !== 0) {
        chosenFranchises = highlightFranchises;
      } else {
        chosenFranchises = [];
      }
    }
    return chosenFranchises;
  }

  saveFilterState() {
    this.service.currentFilterState = this.createFilterModelForChosenTiles();

    if (this.service.tableLayoutManagement && this.service.tableLayoutManagement.lastTableState) {
      this.service.tableLayoutManagement.lastTableState.FilterModel = this.service.currentFilterState;
    } else {
      this.service.tableLayoutManagement.lastTableState = {
        FilterModel: this.service.currentFilterState
      }
    }
  }
}
