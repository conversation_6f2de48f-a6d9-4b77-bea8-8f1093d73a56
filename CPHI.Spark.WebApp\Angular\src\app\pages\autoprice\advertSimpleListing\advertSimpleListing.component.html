<nav class="navbar">

    <nav class="generic">
        <h4 id="pageTitle">

            Stock Quick Search&nbsp;

            <sourceDataUpdate *ngIf="!constants.environment.showLatestSnapshotDate"
                [chosenDate]="constants.LastUpdatedDates.AutotraderAdverts"></sourceDataUpdate>



        </h4>

        <input class="mx-2" type="date" [value]="service.chosenDate" (change)="setChosenDate($event)">



        <!-- Show / hide new vehicles -->
        <sliderSwitch text="New Vehicles" (toggle)="toggleIncludeNewVehicles()"
            [defaultValue]="service.includeNewVehicles">
        </sliderSwitch>

        <!-- Include unpublished -->
        <sliderSwitch text="Un-published Vehicles" (toggle)="toggleIncludeUnPublishedAds()"
            [defaultValue]="service.includeUnPublishedAds">
        </sliderSwitch>


        <!-- Use Test Strategy (only if they have access) -->
        <sliderSwitch
            *ngIf="service.showTestStrategySlider"
            text="Use Test Strategy"
            (toggle)="toggleUseTestStrategy()"
            [defaultValue]="service.useTestStrategy">
        </sliderSwitch>

        <!-- <input type="text" class="ms-3" placeholder="Search..." [(ngModel)]="searchTerm" (input)="onSearch()" /> -->
        <div id="searchBox" class="visibleAboveSm">
            <i class="searchBoxIcon fas fa-search"></i>
            <input type="text" placeholder="Search..." [(ngModel)]="service.searchTerm" (input)="service.onSearch()" />
            <div *ngIf="service.searchTerm" (click)="clearSearchTerm()" id="searchBarClearButton">
                <i class="fas fa-times-circle"></i>
            </div>
        </div>

        <!-- Vehicle types picker -->
        <multiPickerWithCount *ngIf="constants.autopriceEnvironment.vehicleTypes != null" [label]="'Vehicle Types'"
        [menuItems]="constants.autopriceEnvironment.vehicleTypes" [chosenItems]="service.chosenVehicleTypes"
        [itemCount]="provideVehTypeCount.bind(this)"
        [onChosenItemsChange]="onChosenVehTypesChange.bind(this)"></multiPickerWithCount>

        <!-- Lifecycle status picker -->
        <multiPickerWithCount
        [label]="'Lifecycle Statuses'"
        [menuItems]="service.allLifecycleStatuses" [chosenItems]="service.chosenLifecycleStatuses"
        [itemCount]="provideLifecyleCount.bind(this)"
        [onChosenItemsChange]="onChosenLifeCycleStatusesChange.bind(this)"></multiPickerWithCount>

        <!-- <filterSortManagement></filterSortManagement> -->

    </nav>

    <nav class="pageSpecific">




    </nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
    <div class="content-new">
        <div class="content-inner-new">
            <div class="d-flex flex-column h-100">

                <div id="panelHolder">
                    <div id="leftPanel">
                        <ng-container *ngIf="service.rowDataFiltered">
                            <div *ngIf="service.rowDataFiltered" class="autotrader-tile" id="accordionHeader">
                                <div>
                                    <span id="resultsCount">{{ service.rowDataFiltered?.length }} results</span><br>
                                    <span id="filtersCount">{{ service.filterSortManagementParams.filterModel.length }} filters selected</span>
                                </div>
                                <div class="d-flex flex-column">
                                    <button class="btn btn-primary mb-1" [disabled]="service.filterSortManagementParams.filterModel.length === 0" (click)="clearFilters()">
                                        <i class="fas fa-filter"></i>
                                        Clear filters
                                    </button>

                                </div>
                            </div>



                            <div class="autotrader-tile" id="filters">
                                <ng-container *ngIf="!constants.environment.isSingleSiteGroup">
                                    <div class="filterSectionHeader">Site Details</div>
                                    <app-accordion [type]="'checkbox'" [params]="generateParams('RetailerSiteName', 'Retailer')"></app-accordion>
                                    <app-accordion [type]="'dropdown'" [params]="generateParams('SiteBrand', 'Site Brand')"></app-accordion>
                                </ng-container>

                                <div class="filterSectionHeader topMargin">Vehicle Details</div>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('VehicleType','Vehicle Type')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('BodyType','Body Type')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('FuelType', 'Fuel Type')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('TransmissionType', 'Transmission')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('Colour','Colour')"></app-accordion>
                                <app-accordion [type]="'dropdown'" [params]="generateParams('Make','Make')"></app-accordion>
                                <app-accordion [type]="'dropdown'" [params]="generateParams('Model','Model')"></app-accordion>
                                <app-accordion [type]="'dropdown'" [params]="generateParams('Trim','Trim')"></app-accordion>
                                <app-accordion [type]="'dropdown'" [params]="generateParams('Derivative','Derivative')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('AgeBand','Age')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('MileageBand','Mileage')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('ValueBand','Value')"></app-accordion>

                                <div class="filterSectionHeader topMargin">Vehicle Price</div>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('VsStrategyBanding','Vs Strategy')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('PriceIndicatorRatingAtCurrentSelling','Price Indicator')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('PricedProfitBanding','Profit')"></app-accordion>

                                <div class="filterSectionHeader topMargin">Vehicle metrics</div>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('RetailRatingBand','Retail Rating')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('DaysToSellBanding','Days To Sell')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('PerformanceRatingScoreBand','Performance Rating')"></app-accordion>

                                <div class="filterSectionHeader topMargin">Advert Status</div>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('AutotraderAdvertStatus', 'Advert Status')"></app-accordion>
                                <app-accordion  [type]="'checkbox'" [params]="generateParams('DaysListedBand','Days Listed')"></app-accordion>
                                <app-accordion *ngIf="!service.hideDISSelector" [type]="'checkbox'" [params]="generateParams('DaysInStockBand','Days In Stock')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('ImagesBand','Images Count')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('NoVideo','Missing Video')"></app-accordion>
                                <app-accordion [type]="'checkbox'" [params]="generateParams('NoAttentionGrabber','No Attention Grabber')"></app-accordion>

                            </div>


                            <!-- need to add a bunch more of these see brief -->


                        </ng-container>
                    </div>

                    <div id="mainPanel">
                        <paginatedList *ngIf="service.rowDataFiltered" [updatedData]="service.updatedDataEmitter" [rowDataFiltered]="service.rowDataFiltered"></paginatedList>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
