﻿//namespace MadDevs.Abstractions.EventHub
//{
//    public class AppointmentEventBody : BaseAppointment
//    {
//        public bool Completed { get; set; }

//        public string? Note { get; set; }

//        public string? CompletionNote { get; set; }

//        public bool PostHandover { get; set; }

//        public bool HandoverDone { get; set; }

//        public bool TestDriveDone { get; set; }

//        public Guid? LeadId { get; set; }

//        public Guid? EnquiryId { get; set; }
//    }
//}
