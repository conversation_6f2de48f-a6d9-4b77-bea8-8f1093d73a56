﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.IO;
using System.Linq;
using Xunit;
using System.Diagnostics;
using System.Threading.Tasks;
using OfficeOpenXml;
using System.Data;
using HtmlAgilityPack;
using System.Collections.Generic;

namespace CPHI.Spark.WebScraper.Jobs
{

    public  class EnquiriesScrapeJob : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(EnquiriesScrapeJob));
        private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
        private string customerName;
        private string fileDestination;
        //private string fileDestinationDev;


        public void Execute() { }
        public async Task Execute(IJobExecutionContext context)
        {

            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();


            fileDestination = ConfigService.FileDestination;
            //fileDestinationDev = ConfigService.FileDestinationDev;
            fileDestination = fileDestination.Replace("{destinationFolder}", "vindis");
            //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "vindis");
            customerName = "Vindis";


            try
            {
                // KillChrome();
                ScraperMethodsService.ClearDownloadsFolder();

                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";

                ChromeOptions options = ScraperMethodsService.SetChromeOptions("VindisEnquiries", 9222);

                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

                await GetEnquiries();

                _driver.Quit();
                _driver.Dispose();
                stopwatch.Stop();

            }
            catch (Exception e)
            {
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");

                stopwatch.Stop();
                errorMessage = e.ToString();

                //Emailer.SendMail("Vindis activity scraper failed", $"{e}");

                logger.Error($"Problem {e.ToString()}");

                _driver.Quit();
                _driver.Dispose();

                //Process[] chromeInstances = Process.GetProcessesByName("chrome");
                //foreach (Process p in chromeInstances) p.Kill();
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WebScraper",
                    Customer = "Vindis",
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };
                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }

        private void KillChrome()
        {
            Process[] chromeInstances;
            Process[] chromeDriverInstances;
            //kill all chrome instances
            chromeInstances = Process.GetProcessesByName("chrome");
            foreach (Process p in chromeInstances) p.Kill();

            chromeDriverInstances = Process.GetProcessesByName("chromedriver");
            foreach (Process p in chromeDriverInstances)
            {
                p.Kill();
            }
        }


        public async Task GetEnquiries()
        {

            try
            {
                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
                DateTime start = DateTime.Now;

                LoginToEnquiryMax(wait);

                NavigateToReport(wait);

                await DownloadReport(start);

            }
            catch (Exception e)
            {
                throw e;
            }
        }


        private void LoginToEnquiryMax(WebDriverWait wait)
        {

            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

            //go to login page
            _driver.Navigate().GoToUrl("https://enquirymax.net/account/logon");

            //wait for it to appear

            System.Threading.Thread.Sleep(1000);

            IWebElement loginButton = wait.Until(ExpectedConditions.ElementExists(By.Id("UserName")));

            System.Threading.Thread.Sleep(1000);

            Assert.Equal("Login - enquiryMAX", _driver.Title);

            System.Threading.Thread.Sleep(1000);

            WaitAndFind("//input [@Id='UserName']", false).SendKeys("james.smith");
            System.Threading.Thread.Sleep(1000);
            //WaitAndFind("//input [@Id='NextButton']", true);
            WaitAndFind("//input [@Id='UserName']", false).SendKeys(Keys.Tab);
            System.Threading.Thread.Sleep(1000);
            WaitAndFind("//input [@Id='Password']", false).SendKeys(ConfigService.EnquiryMAXPassword);
            System.Threading.Thread.Sleep(1000);
            WaitAndFind("//input [@Id='SubmitButton']", true);
            System.Threading.Thread.Sleep(1000);

            IWebElement dashboardLink = wait.Until(ExpectedConditions.ElementExists(By.ClassName("top-navbar")));

        }

        private void NavigateToReport(WebDriverWait wait)
        {

            WaitAndFind("/html/body/div[1]/header/div[1]/div/nav/ul/li[5]", true);
            System.Threading.Thread.Sleep(1000);

            // Navigate to Reports -> Sales
            WaitAndFind("/html/body/div[1]/header/div[1]/div/nav/ul/li[5]/ul/a[1]/li", true);
            System.Threading.Thread.Sleep(1000);

            // Navigate to Reports -> Sales -> Sales Activity
            WaitAndFind("/html/body/div[1]/div[2]/div[2]/div[1]/div/ul/li[2]/a", true);
            System.Threading.Thread.Sleep(3000);

            IWebElement customerDropdown = WaitAndFind("//body//select[@id='CustomerTypeSelectBoxSalesActivity']", false);
            SelectElement selectFromCustomerDropdown = new SelectElement(customerDropdown);
            selectFromCustomerDropdown.SelectByText("Motability");
            selectFromCustomerDropdown.SelectByText("Retail");

            // Filter for Motab & Retail
            System.Threading.Thread.Sleep(3000);

            // On report page now, click search button
            WaitAndFind("/html/body/div[1]/div[2]/div[2]/div[4]/div[1]/div[6]/input", true);
            System.Threading.Thread.Sleep(6000);

            IWebElement datatableWrapper = wait.Until(ExpectedConditions.ElementExists(By.ClassName("repohead-title")));
        }

        private async Task DownloadReport(DateTime start)
        {
           
            WaitAndFind("//input [@value='Export']", true);

            ScraperMethodsService.WaitUntilFileDownloaded("SalesActivity");
            logger.Info($"Succesfully saved down export file for report Enquiries");

            await MoveReportToInbound(start);
        }

        private async Task MoveReportToInbound(DateTime start)
        {

            string downloadPath = ConfigService.FileDownloadLocation;

            DirectoryInfo directory = new DirectoryInfo(downloadPath);

            FileInfo generatedFile = directory.GetFiles().Where(x => x.LastWriteTime > start && x.Name.Contains("SalesActivity")).First();

            var fullfileName = generatedFile.FullName;
            string htmlCode = await File.ReadAllTextAsync(generatedFile.FullName);
            HtmlDocument doc = new HtmlDocument();
            doc.LoadHtml(htmlCode);
            var rowCount = doc.DocumentNode.SelectNodes("//tr").Count;
            var colCount = 36;
            DataTable table = new DataTable();
            for (int col = 0; col < colCount; col++)
            {
                table.Columns.Add(col.ToString());
            }

            for (int rowIndex = 1; rowIndex < rowCount; rowIndex++)
            {
                List<object> colValues = new List<object>();

                for (int colIndex = 0; colIndex < colCount; colIndex++)
                {
                    colValues.Add(doc.DocumentNode.SelectNodes($"//tr")[rowIndex].ChildNodes[colIndex].InnerText.Trim());
                }
                table.Rows.Add(colValues.ToArray());
                
            }

            string convertedFilePath = downloadPath + @"\" + "ConvertedEnquiriesFile" + ".xlsx";

            using (ExcelPackage pck = new ExcelPackage())
            {
                ExcelWorksheet ws = pck.Workbook.Worksheets.Add("Worksheet");
                ws.Cells["A1"].LoadFromDataTable(table, true);
                
                pck.SaveAs(new FileInfo(convertedFilePath));
            }


            string newFilePathAndName = downloadPath + @"\" + "Enquiries_SPKV81" + ".xlsx";

            //rename the file
            File.Move(convertedFilePath, newFilePathAndName);
            logger.Info($"Succesfully changed name from {generatedFile.FullName} to {newFilePathAndName}");

            //move to the incoming folder
            MoveFile("Enquiries_SPKV81" + ".xlsx");

            File.Delete(generatedFile.FullName);
        }

        private IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "EnquiryScrape", findXPath, andClick);
            
            return result;
        }

        private void MoveFile(string fileName)
        {
            string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
            string newFilePathAndName = $@"{fileDestination}{prefix}{fileName}";
            //string newFilePathAndNameDev = $@"{fileDestinationDev}{prefix}{fileName}";

            string path = ConfigService.FileDownloadLocation;
            string oldLocation = path + @"\" + fileName;
            
            File.Move(oldLocation, newFilePathAndName); //move them to incoming
            //File.Copy(newFilePathAndName, newFilePathAndNameDev); //copy to dev

            logger.Info($"Moved file from {oldLocation} to {newFilePathAndName}");
        }





    }
}
