import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe } from 'src/app/cph.pipe';
import { LateCostOption, OrderOption } from 'src/app/model/sales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDealDataService } from 'src/app/services/getDeals.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { OrderBookService } from '../orderBook/orderBook.service';
import { DealsDoneForTheMonthParams, DealsDoneForTheMonthWeekAnalysisParams,  WeekAnalysis, WeeklyDealBreakdown } from './dealsForTheMonth.model';

@Injectable({
  providedIn: 'root'
})
export class DealsForTheMonthService {

  weeklyDealBreakdown: WeeklyDealBreakdown[];
  weekAnalyses: WeekAnalysis[];
  chosenWeek: WeekAnalysis;

  vehicleTypeTypes: string[];
  orderTypeTypes: string[];
  franchises: string[];

  lateCostOption: LateCostOption;
  orderOption: OrderOption;

  deliveryDate: Date;

  showPopOut: boolean = false;
  showLates: boolean = true;

  onlyLates: number;
  excludeLates: number;
  onlyOrders: number;
  excludeOrders: number;

  months: Date[];

  MtdTitle: string = 'MTD';
  BroughtInTitle: string = 'Brought In';

  weekDays: string[];
  translatedWeekDays: string[];

  constructor(
    public constants: ConstantsService,
    public orderBookService: OrderBookService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public getDealData: GetDealDataService,
    public cphPipe: CphPipe

  ) {

  }

  initParams(): void {
    this.vehicleTypeTypes = this.constants.clone(this.constants.vehicleTypeTypes);
    this.orderTypeTypes = this.constants.clone(this.constants.orderTypeTypesNoTrade);
    this.franchises = this.constants.clone(this.constants.FranchiseCodes);
    this.orderOption = this.constants.orderOptions[0],
    this.lateCostOption = this.constants.lateCostOptions[1],
    this.weekAnalyses = [];
    this.deliveryDate = this.constants.thisMonthStart;

    if(this.constants.translatedText.LocaleCode != 'en-gb')
    {
      this.weekDays = this.getWeekDays('en-gb');
      this.translatedWeekDays = this.getWeekDays(this.constants.translatedText.LocaleCode);
    }
  }

  public getDeals(): void {

    this.showPopOut = false;

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    this.setLateAndOrderFilters();

    let parms: DealsDoneForTheMonthParams = {
      StartDate: this.deliveryDate.toISOString(),
      OrderTypes: this.orderTypeTypes.toString(),
      VehicleTypes: this.vehicleTypeTypes.toString(),
      Franchises: this.franchises.toString(),
      SiteIds: this.selections.selectedSitesIds.toString(),
      OnlyLates: this.onlyLates == 1 ? true: false,
      ExcludeLates: this.excludeLates == 1 ? true: false,
      OnlyOrders: this.onlyOrders == 1 ? true: false,
      ExcludeOrders: this.excludeOrders == 1 ? true: false,
    }

    // Trigger the API call to produce the main buckets
    this.getDealData.getDealsForTheMonth(parms).subscribe(res => {

      // this.dealBuckets = res;
      this.weekAnalyses = res;

    }, error => {

      console.error("ERROR: ", error);

    }, () => {

      setTimeout(() => {
        this.selections.triggerSpinner.next({show:false});
      }, 20)

    })

  }

  public getWeeklyAnalysis(weekAnalysis: WeekAnalysis): void {

    let mondaysInMonth: Date[] = this.getMondays(new Date(this.deliveryDate.getTime()))
    let startDate: Date = this.constants.deductTimezoneOffset(this.selectStartDateForWeek(weekAnalysis.WeekName, mondaysInMonth));

    let parms: DealsDoneForTheMonthWeekAnalysisParams = {
      StartDate: startDate.toISOString(),
      OrderTypes: this.orderTypeTypes.toString(),
      VehicleTypes: this.vehicleTypeTypes.toString(),
      Franchises: this.franchises.toString(),
      SiteIds: this.selections.selectedSitesIds.toString(),
      OnlyLates: this.onlyLates == 1 ? true: false,
      ExcludeLates: this.excludeLates == 1 ? true: false,
      OnlyOrders: this.onlyOrders == 1 ? true: false,
      ExcludeOrders: this.excludeOrders == 1 ? true: false,
      BroughtIn: this.isBroughtIn(weekAnalysis.WeekName) == 1 ? true: false,
      MTD: this.isMtd(weekAnalysis.WeekName) == 1 ? true: false,
    }

    this.getDealData.getWeeklyBreakDown(parms).subscribe(res => {

      this.weeklyDealBreakdown = res;

    }, e => {

      console.error("Error getting weekly breakdown: ", e);
      
    }, () => {

      this.selections.triggerSpinner.next({show:false});

  })

  }

  // Get all mondays in a month (returns array of Dates)
  private getMondays(date: Date): Date[] {
    let month = date.getMonth();
    let mondays = [];

    date.setDate(1);

    // Get the first Monday in the month
    while (date.getDay() !== 1) {
        date.setDate(date.getDate() + 1);
    }

    // Get all the other Mondays in the month
    while (date.getMonth() === month) {
        mondays.push(new Date(date.getTime()));
        date.setDate(date.getDate() + 7);
    }

    return mondays;
  }

  private setLateAndOrderFilters(): void {

    // Selection booleans
    this.onlyLates = 0;
    this.excludeLates = 0;
    this.onlyOrders = 0;
    this.excludeOrders = 0;

    // Set selection criteria for API call
    if(this.lateCostOption.includeLateCosts && !this.lateCostOption.includeNormalCosts){
      this.onlyLates = 1;
    }

    if(!this.lateCostOption.includeLateCosts && this.lateCostOption.includeNormalCosts){
      this.excludeLates = 1;
    }

    if(this.orderOption.includeOrders && !this.orderOption.includeInvoiced){
      this.onlyOrders = 1;
    }

    if(!this.orderOption.includeOrders && this.orderOption.includeInvoiced){
      this.excludeOrders = 1;
    }
  }

  private selectStartDateForWeek(WeekName: string, mondaysInMonth: any[]): Date {

    if(WeekName ==  this.BroughtInTitle || WeekName == this.MtdTitle){
      return new Date(this.deliveryDate.getFullYear(), this.deliveryDate.getMonth(), 1);
    }

    let firstDayOfMonth = new Date(this.deliveryDate.getFullYear(), this.deliveryDate.getMonth(), 1);

    var weekNumber: number = ( WeekName
    .match(/\d+\.\d+|\d+\b|\d+(?=\w)/g) || [] )
    .map(function (v) {return +v;}).pop();

    if(weekNumber == 1){ return firstDayOfMonth; }
    else { 

      if(firstDayOfMonth.getDay() == 1)
      {
        return mondaysInMonth[weekNumber-1];
      }
      else 
      {
        return mondaysInMonth[weekNumber-2];
      }
      
    }
  }

  private isBroughtIn(WeekName: string): number { return WeekName == this.BroughtInTitle ? 1 : 0; } 

  private isMtd(WeekName: string): number { return WeekName ==  this.MtdTitle ? 1 : 0; } 

  private getWeekDays(localeCode: string)
  {
    var baseDate = new Date(Date.UTC(2017, 0, 2)); // just a Monday
    var weekDays = [];
    let i = 0;

    for(i = 0; i < 7; i++)
    {       
        weekDays.push(baseDate.toLocaleDateString(localeCode, { weekday: 'long' }));
        baseDate.setDate(baseDate.getDate() + 1);       
    }
    return weekDays;
}

}
