/*
<PERSON><PERSON><PERSON> to add the ShowLCVToggle GlobalParam for Enterprise DealerGroup
This parameter will be used to control the visibility of the LCV toggle in the UI
*/

-- Check if the parameter already exists to avoid duplicates
IF NOT EXISTS (SELECT 1 FROM GlobalParams WHERE Description = 'ShowLCVToggle' AND DealerGroup_Id = 28)
BEGIN
    -- Add the ShowLCVToggle parameter for Enterprise (DealerGroup_Id = 28)
    INSERT INTO GlobalParams
    (DateFrom, DateTo, Value, TextValue, Description, DealerGroup_Id)
    VALUES
    (GETDATE(), NULL, 0, 'True', 'ShowLCVToggle', 28)
    
    PRINT 'ShowLCVToggle GlobalParam added for Enterprise (DealerGroup_Id = 28)'
END
ELSE
BEGIN
    PRINT 'ShowLCVToggle GlobalParam already exists for Enterprise (DealerGroup_Id = 28)'
END
