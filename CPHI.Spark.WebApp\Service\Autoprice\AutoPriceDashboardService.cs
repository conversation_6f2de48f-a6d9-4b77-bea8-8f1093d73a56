﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using System.Linq;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;


namespace CPHI.Spark.WebApp.Service.AutoPrice
{
    public interface IAutoPriceDashboardService
    {
        Task<DashboardDataSet> GetDashboardDataSet(GetPricingDashboardParams parms);
        Task<List<DashboardDataSetForSite>> GetDashboardDataSetBySite(List<int> chosenRetailerSiteIds);
    }
    public class AutoPriceDashboardService: IAutoPriceDashboardService
    {
        private readonly IUserService userService;
        private readonly IConfiguration configuration;
        private readonly string _connectionString;
        private readonly DealerGroupName dealerGroup;

        public AutoPriceDashboardService(
            IUserService userService
, IConfiguration configuration)
        {
            this.userService = userService;
            this.configuration = configuration;
            this.dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
            _connectionString = this.configuration.GetConnectionString(dgName);
        }


        //AutoPriceDashboard.  THIS IS NOT USED
        public async Task<List<DashboardDataSetForSite>> GetDashboardDataSetBySite(List<int> chosenRetailerSiteIds)
        {

            //workout sites
            IEnumerable<int> userRetailerSiteIds =  userService.GetUserRetailSiteIds();
            userRetailerSiteIds = new List<int>() { 15, 11, 5, 14, 18, 10, 7, 4, 13, 8, 2, 12, 19, 1, 9, 17, 6, 3, 16 };
            var retailerSiteIds = userRetailerSiteIds.Intersect(chosenRetailerSiteIds);

            //get all data
            var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
            var allAds = await vehicleAdvertsDataAccess.GetDashboardAdverts(retailerSiteIds.ToList(), false);
            var adsByRetailer = allAds.ToLookup(x => x.RetailerSiteName);
            var leavingItemsParams = new GetLeavingPriceItemsParams()
            {
                ChosenRetailerSiteIds = chosenRetailerSiteIds,
                EligibleSiteIds = userRetailerSiteIds.ToList(),
                StartDate = DateTime.Today.AddDays(-7),
                EndDate = DateTime.Now
            };

            var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
            var autoPriceDataAccess = new AutoPriceDataAccess(_connectionString);
            var allLeavingItems = await leavingPricesDataAccess.GetLeavingPriceBasicItems(leavingItemsParams);
            var allRecentViews = await autoPriceDataAccess.GetDashboardRecentViews(retailerSiteIds.ToList());
            var eightWeekViews = await autoPriceDataAccess.GetDashboardRecentViews8Week(retailerSiteIds.ToList());
            var adsWithStrategyBanding = await autoPriceDataAccess.GetVehiclesWithStrategyBanding(retailerSiteIds.ToList(), ConstantsCache.ProvideBandingsDictionary(DealerGroupName.RRGUK));
            var offStrategyVehicles = await vehicleAdvertsDataAccess.GetOffStrategyAdverts(retailerSiteIds.ToList(), ConstantsCache.ProvideBandingsDictionary(DealerGroupName.RRGUK));

            var leavingItemsByRetailer = allLeavingItems.ToLookup(x => x.RetailerSiteName);
            var recentViewsByRetailer = allRecentViews.ToLookup(x => x.RetailerSiteName);
            var recentViewsBySnapshotDate = allRecentViews.ToLookup(x => x.SnapshotDate);
            var eightWeekViewsByRetailer = eightWeekViews.ToLookup(x => x.RetailerSiteName);
            var adsWithStrategyBandingByRetailer = adsWithStrategyBanding.ToLookup(x => x.RetailerSiteName);

            var offStrategyVehiclesBySite = offStrategyVehicles.ToLookup(x => x.RetailerSiteName);

            //build results
            List<DashboardDataSetForSite> results = new List<DashboardDataSetForSite>();
            foreach (var adsSet in adsByRetailer)
            {
                results.Add(new DashboardDataSetForSite()
                {
                    RetailerSiteId = adsSet.First().RetailerSiteId,
                    RetailerSiteName = adsSet.Key,
                    PositionSummary = BuildPositionSummary(adsSet),
                    RecentViews = recentViewsByRetailer[adsSet.Key].ToList(),
                    EightWeekViews = eightWeekViewsByRetailer[adsSet.Key].ToList(),
                    StrategyPriceBreakdown = new StrategyPriceBreakdown(adsWithStrategyBandingByRetailer[adsSet.Key].ToList()),
                    LeavingItems = leavingItemsByRetailer[adsSet.Key].ToList(),
                    OffStrategyAdverts = offStrategyVehiclesBySite[adsSet.Key].ToList(),
                });
            }

            results.Add(new DashboardDataSetForSite()
            {
                RetailerSiteId = 0,
                RetailerSiteName = "Total",
                PositionSummary = BuildPositionSummary(allAds),
                RecentViews = BuildRecentViews(allRecentViews),
                EightWeekViews = eightWeekViews.ToList(),
                StrategyPriceBreakdown = new StrategyPriceBreakdown(adsWithStrategyBanding.ToList()),
                LeavingItems = allLeavingItems.ToList(),
                OffStrategyAdverts = offStrategyVehicles.ToList(),
            });



            return results;
        }

        private List<DashboardRecentViews> BuildRecentViews(IEnumerable<DashboardRecentViews> allRecentViews)
        {
            List<DashboardRecentViews> dashboardRecentViews = new List<DashboardRecentViews>();
            var allRecentViewsBySnapshotDate = allRecentViews.ToLookup(a => a.SnapshotDate);
            foreach (var recentViewsBySnapshotDate in allRecentViewsBySnapshotDate)
            {
                dashboardRecentViews.Add(new DashboardRecentViews
                {
                    RetailerSiteId = 0,
                    RetailerSiteName = "Total",
                    SnapshotDate = recentViewsBySnapshotDate.Key,
                    AdvertCount = recentViewsBySnapshotDate.Sum(a => a.AdvertCount),
                    AdvertViews = recentViewsBySnapshotDate.Sum(a => a.AdvertViews),
                    LeavingCount = recentViewsBySnapshotDate.Sum(a => a.LeavingCount),
                    PerfRatingScore = recentViewsBySnapshotDate.Sum(a => a.PerfRatingScore),
                    RetailRating = recentViewsBySnapshotDate.Sum(a => a.RetailRating),
                    SearchViews = recentViewsBySnapshotDate.Sum(a => a.SearchViews)
                });
            }

            return dashboardRecentViews;
        }

        public async Task<DashboardDataSet> GetDashboardDataSet(GetPricingDashboardParams parms)
        {
            //workout sites
            IEnumerable<int> userRetailerSiteIds =  userService.GetUserRetailSiteIds();
            DealerGroupName userDealerGroup = userService.GetUserDealerGroupName();

            var retailerSiteIds = userRetailerSiteIds.ToList();

            //get all data
            var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
            IEnumerable<DashboardAdvert> ads = await vehicleAdvertsDataAccess.GetDashboardAdverts(retailerSiteIds, parms.includeNewVehicles);

            var leavingItemsParams = new GetLeavingPriceItemsParams()
            {
                ChosenRetailerSiteIds = parms.chosenRetailerSiteIds,
                EligibleSiteIds = userRetailerSiteIds.ToList(),
                StartDate = DateTime.Today.AddDays(-7),
                EndDate = DateTime.Now
            };

            var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
            var autoPriceDataAccess = new AutoPriceDataAccess(_connectionString);
            IEnumerable<LeavingPriceBasicItem> leavingItems = await leavingPricesDataAccess.GetLeavingPriceBasicItems(leavingItemsParams);
            IEnumerable<DashboardRecentViews> recentViewsAll = await autoPriceDataAccess.GetDashboardRecentViews(retailerSiteIds);
            IEnumerable<DashboardRecentViews8Week> eightWeekViewsAll = await autoPriceDataAccess.GetDashboardRecentViews8Week(retailerSiteIds);
            IEnumerable<VehicleAdWithStrategyBanding> adsWithStrategyBanding = await autoPriceDataAccess.GetVehiclesWithStrategyBanding(retailerSiteIds, ConstantsCache.ProvideBandingsDictionary(userDealerGroup));
            IEnumerable<OffStrategyVehicleSummaryItem> offStrategyVehicles = await vehicleAdvertsDataAccess.GetOffStrategyAdverts(retailerSiteIds, ConstantsCache.ProvideBandingsDictionary(userDealerGroup));

            //consolidate recentViews
            List<DashboardRecentViews> recentViews = new List<DashboardRecentViews>();
            recentViews = recentViewsAll.GroupBy(r => r.SnapshotDate)
                .Select(group => new DashboardRecentViews
                {
                    SnapshotDate = group.Key,
                    AdvertCount = group.Sum(g => g.AdvertCount),
                    AdvertViews = group.Sum(g => g.AdvertViews),
                    LeavingCount = group.Sum(g => g.LeavingCount),
                    SearchViews = group.Sum(g => g.SearchViews)
                }).ToList();

            //consolicate eightWeekViewsAll
            List<DashboardRecentViews8Week> eightWeekViews = new List<DashboardRecentViews8Week>();
            eightWeekViews = eightWeekViewsAll.GroupBy(e => e.WeekStart)
                .Select(group => new DashboardRecentViews8Week
                {
                    WeekStart = group.Key,
                    VeryLow = group.Sum(g => g.VeryLow),
                    Low = group.Sum(g => g.Low),
                    OK = group.Sum(g => g.OK),
                    High = group.Sum(g => g.High),
                    VeryHigh = group.Sum(g => g.VeryHigh),
                    NoValuation = group.Sum(g => g.NoValuation),
                    AdvertViews = group.Sum(g => g.AdvertViews),
                    LeavingCount = group.Sum(g => g.LeavingCount),
                    SearchViews = group.Sum(g => g.SearchViews),
                }).ToList();

            //process data
            DashboardPositionSummary positionSummary = BuildPositionSummary(ads);
            StrategyPriceBreakdown strategyPriceBreakdown = new StrategyPriceBreakdown(adsWithStrategyBanding.ToList());

            return new DashboardDataSet()
            {
                PositionSummary = positionSummary,
                RecentViews = recentViews.ToList(),
                EightWeekViews = eightWeekViews.ToList(),
                StrategyPriceBreakdown = strategyPriceBreakdown,
                LeavingItems = leavingItems.ToList(),
                OffStrategyAdverts = offStrategyVehicles,
            };
        }



        private static DashboardPositionSummary BuildPositionSummary(IEnumerable<DashboardAdvert> ads)
        {
            DashboardPositionSummary positionSummary = new DashboardPositionSummary();
            int retailRatingCount = 0;
            int perfRatingCount = 0;
            int totalValuation = 0;
            int totalSellingThoseWithValuation = 0;
            int totalAvDaysToSellCount = 0;

            int vehicleCountWithSIV = 0;
            int totalProfit = 0;

            foreach (var ad in ads)
            {
                //easy stuff
                positionSummary.MissingImagesCount += ad.MissingImages ? 1 : 0;
                positionSummary.NoAttentionGrabber += ad.NoAttentionGrabber ? 1 : 0;
                positionSummary.NoVideo += ad.HasVideo ? 0 : 1;
                positionSummary.LowQuality += ad.IsLowQuality ? 1 : 0;

                if (ad.PerformanceRatingRating == "BELOW_AVERAGE") { positionSummary.PerfRatingBelowAverage++; }
                if (ad.PerformanceRatingRating == "NONE" || ad.PerformanceRatingRating == null) { positionSummary.PerfRatingNone++; }
                if (ad.PerformanceRatingRating == "LOW") { positionSummary.PerfRatingLow++; }
                if (ad.PerformanceRatingRating == "EXCELLENT") { positionSummary.PerfRatingExcellent++; }
                if (ad.PerformanceRatingRating == "ABOVE_AVERAGE") { positionSummary.PerfRatingAboveAverage++; }

                if (ad.PriceIndicatorRatingAtCurrentSelling == "GOOD") { positionSummary.PriceIndicatorGood++; }
                if (ad.PriceIndicatorRatingAtCurrentSelling == "NOANALYSIS") { positionSummary.PriceIndicatorNotAvailable++; }
                if (ad.PriceIndicatorRatingAtCurrentSelling == "FAIR") { positionSummary.PriceIndicatorFair++; }
                if (ad.PriceIndicatorRatingAtCurrentSelling == "LOW") { positionSummary.PriceIndicatorLow++; }
                if (ad.PriceIndicatorRatingAtCurrentSelling == "HIGH") { positionSummary.PriceIndicatorHigh++; }
                if (ad.PriceIndicatorRatingAtCurrentSelling == "GREAT") { positionSummary.PriceIndicatorGreat++; }


                //hard stuff
                if (ad.DaysToSellAtCurrentSelling != null && ad.DaysToSellAtCurrentSelling != 0) { totalAvDaysToSellCount++; }
                if (ad.RetailRating != null && ad.RetailRating != 0) { retailRatingCount++; }
                if (ad.PerformanceRatingScore != null && ad.PerformanceRatingScore != 0) { perfRatingCount++; }

                if (ad.ValuationMktAvRetail != null && ad.ValuationMktAvRetail > 0)
                {
                    totalSellingThoseWithValuation += ad.AdvertisedPrice;
                    totalValuation += (int)ad.ValuationMktAvRetail;
                }

                if (ad.PricedProfit != null)
                {
                    vehicleCountWithSIV++;
                    totalProfit += (int)ad.PricedProfit;
                }

                positionSummary.TotalAdverts++;
                positionSummary.AverageDaysListed += ad.DaysListed;
                positionSummary.AverageDaysToSell += (int)Math.Round(ad.DaysToSellAtCurrentSelling ?? 0);
                positionSummary.AverageRetailRating += (int)Math.Round(ad.RetailRating ?? 0);
                positionSummary.AveragePerformanceRating += (ad.PerformanceRatingScore ?? 0);

            }
            positionSummary.AverageDaysListed = positionSummary.TotalAdverts > 0 ? positionSummary.AverageDaysListed / positionSummary.TotalAdverts : 0;
            positionSummary.AverageDaysToSell = totalAvDaysToSellCount > 0 ? positionSummary.AverageDaysToSell / totalAvDaysToSellCount : 0;
            positionSummary.AverageRetailRating = retailRatingCount > 0 ? positionSummary.AverageRetailRating / retailRatingCount : 0;
            positionSummary.AveragePerformanceRating = retailRatingCount > 0 ? positionSummary.AveragePerformanceRating / perfRatingCount : 0;
            positionSummary.AveragePricePosition = totalValuation > 0 ? totalSellingThoseWithValuation / (decimal)totalValuation : 0;
            positionSummary.AverageProfitPerVehicle = vehicleCountWithSIV != 0 ? (decimal)totalProfit / vehicleCountWithSIV : 0;
            return positionSummary;
        }


    }
}