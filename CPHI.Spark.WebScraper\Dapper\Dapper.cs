﻿using CPHI.Spark.Model;
using CPHI.Spark.Repository;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;


namespace CPHI.Spark.WebScraper
{
    public class Dapperr : IDapper
    {
        //private readonly IConfiguration _config;

        //private string Connectionstring = "DefaultConnection";

        public Dapperr()
        {
            //_config = config;
        }
        public void Dispose()
        {

        }
        public async Task<DataTable> GetDataTableAsync(DealerGroupName customer, string query, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            var dataTable = new DataTable();
            using SqlConnection conn = new SqlConnection(ConfigService.GetConnectionString(customer));
            using SqlCommand cmd = new SqlCommand(query, conn);
            cmd.CommandType = commandType;
            using SqlDataAdapter da = new SqlDataAdapter(cmd);
            await Task.Run(() => da.Fill(dataTable));
            return dataTable;
        }

        //public async Task<DealsAndComments> GetMultipleAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        //{
        //    DealsAndComments dealsAndComments = new DealsAndComments();
        //    using IDbConnection db = new SqlConnection(_config.GetConnectionString(Connectionstring));
        //    using var result = await db.QueryMultipleAsync(sp, parms, commandType: commandType);
        //    dealsAndComments.deals = result.Read<>

        //}

        public async Task<int> ExecuteAsync(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            using IDbConnection db = new SqlConnection(ConfigService.GetConnectionString(customer));
            return await db.ExecuteAsync(sp, parms, commandType: commandType, commandTimeout: 300);
        }

        public T Get<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.Text)
        {
            using IDbConnection db = new SqlConnection(ConfigService.GetConnectionString(customer));
            return db.Query<T>(sp, parms, commandType: commandType).FirstOrDefault();
        }

        public async Task<T> GetAsync<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            using IDbConnection db = new SqlConnection(ConfigService.GetConnectionString(customer));
            return await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType);
        }


        public IEnumerable<T> GetAll<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            using IDbConnection db = new SqlConnection(ConfigService.GetConnectionString(customer));
            return db.Query<T>(sp, parms, commandType: commandType);
        }

        public async Task<IEnumerable<T>> GetAllAsync<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            using IDbConnection db = new SqlConnection(ConfigService.GetConnectionString(customer));
            return await db.QueryAsync<T>(sp, parms, commandType: commandType);
        }



        public DbConnection GetDbconnection(DealerGroupName customer)
        {
            return new SqlConnection(ConfigService.GetConnectionString(customer));
        }

        public async Task<T> InsertAsync<T>(string sp, DynamicParameters parms, IDbTransaction tran, IDbConnection db, CommandType commandType = CommandType.StoredProcedure)
        {
            return await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType, transaction: tran);
        }

        public T Insert<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            T result;
            using IDbConnection db = new SqlConnection(ConfigService.GetConnectionString(customer));
            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = db.Query<T>(sp, parms, commandType: commandType, transaction: tran).FirstOrDefault();
                    tran.Commit();
                }
                catch (Exception)
                {
                    tran.Rollback();
                    throw;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }
        public async Task<T> InsertAsync<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            T result;
            using IDbConnection db = new SqlConnection(ConfigService.GetConnectionString(customer));
            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType, transaction: tran);
                    tran.Commit();
                }
                catch (Exception)
                {
                    tran.Rollback();
                    throw;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }

        public T Update<T>(DealerGroupName customer, string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            T result;
            using IDbConnection db = new SqlConnection(ConfigService.GetConnectionString(customer));
            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = db.Query<T>(sp, parms, commandType: commandType, transaction: tran).FirstOrDefault();
                    tran.Commit();
                }
                catch (Exception)
                {
                    tran.Rollback();
                    throw;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }
    }
}
