<nav class="navbar">
  <nav class="generic" >
    <h4 id="pageTitle">
      <div >
        <span *ngIf="showingAllSites">Winnings for all sites</span>
        <span *ngIf="showingSite">Winnings for {{ currentSite.title }}</span>
        <span *ngIf="showingSalesman">Winnings for {{ currentSalesman }}</span>
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Deals"></sourceDataUpdate>
      </div>
    </h4>

    <!-- Reveal all scratchcards for all sites -->
    <ng-container *ngIf="showingAllSites">
      <button 
        *ngIf="!sitesRevealed; else hideAll" 
        class="btn btn-primary"
        (click)="sitesRevealed = true"
      >
        Reveal all scratchcards
      </button>
      <ng-template #hideAll>
        <button class="btn btn-secondary" (click)="sitesRevealed = false">Hide all scratchcards</button>
      </ng-template>
    </ng-container>

    <!-- Menu for when drilled down to a particular site -->
    <ng-container *ngIf="showingSite">
      <button class="btn btn-primary" (click)="backToSites()">Return to sites</button>

      <!-- Reveal all scratchcards for this site -->
      <button
        *ngIf="!salesmanRevealed; else hideAll" 
        class="btn btn-primary"
        (click)="salesmanRevealed = true"
      >
        Reveal all scratchcards
      </button>
      <ng-template #hideAll>
        <button class="btn btn-secondary" (click)="salesmanRevealed = false">Hide all scratchcards</button>
      </ng-template>

      <!-- <div class="buttonGroup">
        <ng-container *ngFor="let week of weeks">
          <button 
            class="btn btn-primary" 
            [ngClass]="{ 'active': selectedWeek === week.week }"
            (click)="getSalesForWeek(week.week)"
          >
            {{ week.text }}
          </button>
        </ng-container>
      </div> -->
    </ng-container>

    <!-- Menu for when drilled down to a particular salesman -->
    <ng-container *ngIf="showingSalesman">
      <button class="btn btn-primary" (click)="backToSite()">Return to site</button>
      
      <button 
        *ngIf="!allWindowsRevealed; else hideAll" 
        class="btn btn-primary"
        (click)="allWindowsRevealed = true"
      >
        Reveal all windows
      </button>
      <ng-template #hideAll>
        <button class="btn btn-secondary btn-lg" (click)="allWindowsRevealed = false">Hide all windows</button>
      </ng-template>
    </ng-container>
  </nav>

  <nav class="pageSpecific" >
    <button (click)="refresh()" class="btn btn-primary">
      {{constants.translatedText.Refresh}}
    </button>
  </nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new" [ngClass]="theme">
    <!-- Required for the fireworks background -->
    <div class="fireworks-container">
      <div class="fireworks">
        <div class="before"></div>
        <div class="after"></div>
      </div>
    </div>

    <div class="content-inner-new">

      <!-- Event logo image -->
      <div class="row">
        <div class="col" align="center">
          <h1 class="scratchcardHeading large" (click)="incrementExcelDownloadClicks()">
            April Used Car Event
          </h1>
        </div>
      </div>

      <!-- Sales for all sites -->
      <ng-container *ngIf="sites && showingAllSites">
        <div class="row">
          <div *ngFor="let site of sites" class="col-3" align="center">
            <siteCard [site]="site" [isRevealed]="sitesRevealed" (siteSales)="getSalesForSite($event.site, $event.byWeek)" [theme]="theme"></siteCard>
          </div>
        </div>
        <div *ngIf="sitesRevealed" class="row totalScratchcards" align="center">
          <div class="col">
            <h1>Total Scratchcards Claimed: {{ totalClaimed() }}</h1>
          </div>
        </div>
      </ng-container>

      <!-- Sales for chosen site -->
      <ng-container *ngIf="salesForAllStaff && showingSite">
        <div class="row">
          <div *ngFor="let salesman of salesForAllStaff" class="col-3" align="center">
            <siteCard [salesman]="salesman" [isRevealed]="salesmanRevealed" (salesmanSales)="getSalesForSalesman($event)" [theme]="theme"></siteCard>
          </div>
        </div>
        <managerCard [site]="currentSite.title" [sales]="salesForSite"></managerCard>
      </ng-container>
      <h2 *ngIf="salesForAllStaff.length < 1 && showingSite">No sales to show for this week.</h2>

      <!-- Sales for specific salesman -->
      <ng-container *ngIf="salesForSalesman && showingSalesman">
        <div class="row">
          <div class="col-12" align="center">
            <salesmanCard [salesman]="currentSalesman" [sales]="salesForSalesman" [isRevealed]="allWindowsRevealed" [theme]="theme"></salesmanCard>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<div *ngIf="showLoadingScreen" id="loading-screen" [ngClass]="theme">
  <div id="title-container">
    <h1 *ngIf="theme !== 'custom'">
      <span id="title-1">{{ theme | uppercase }}</span>
      <span id="title-2">SCRATCH</span>
    </h1>
    <h1 *ngIf="theme === 'custom'">
      <span id="title-1">APRIL USED</span>
      <span id="title-2">CAR EVENT</span>
    </h1>
  </div>
  <div class="progress-bar">
    <div class="progress-bar-inner"></div>
  </div>
</div>