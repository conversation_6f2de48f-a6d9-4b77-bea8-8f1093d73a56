//core angular
import { <PERSON><PERSON><PERSON>, On<PERSON>nit, HostL<PERSON>ener, On<PERSON><PERSON>roy } from '@angular/core';
//model and cell renderers
import { DebtsDebtsSummaryRow, LabelAndAmount, LabelAndAmountAndAbbreviation } from '../../../model/main.model';
import { DebtCommentsComponent } from '../../../_cellRenderers/debtComments.component';
//services
import { ConstantsService } from '../../../services/constants.service';
import { SelectionsService } from '../../../services/selections.service';
//pipes and interceptors
import { CphPipe } from '../../../cph.pipe';
//Angular things, non-standard
import { ExcelExportService } from '../../../services/excelExportService';
import { localeEs } from 'src/environments/locale.es.js';
import { GridApi, GridOptions, IFilterComp, IRowModel } from 'ag-grid-community';
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'debtsTable',
  template: `
  <div id="debtsContainer">
    <div id="topArea">
      <div id="buttonsArea">
        <!-- For choosing debt type -->
        <div class="buttonGroup filterChoiceArea">
          <button
            class="btn btn-primary allButton"
            [ngClass]="{ 'active': !selections.debtsTable.buttonSelections.debtType }"
            (click)="doFiltering('DebtType', 'debtType')"
          >
            <div class="buttonContent">{{ this.constants.translatedText.AllTypes }}</div>
          </button>  
          <button
            *ngFor="let debtType of selections.debtsTable.debtTypes"
            class="btn btn-primary"
            [ngClass]="{ 'active': debtType.label == selections.debtsTable.buttonSelections.debtType?.label }"
            (click)="doFiltering('DebtType', 'debtType', debtType)"
          >
            <div class="buttonContent wideButton" [ngbPopover]="debtType.label" placement="bottom" container="body" triggers="mouseenter:mouseleave">
              <div>{{ debtType.label }}</div>
              <div>{{ debtType.amount | cph:'currency':0 }}</div>
            </div>
          </button>
        </div>

        <!-- For choosing department -->
        <div class="buttonGroup filterChoiceArea">
          
          <button
          class="btn btn-primary allButton"
          [ngClass]="{ 'active': !selections.debtsTable.buttonSelections.department }"
          (click)="doFiltering('Department', 'department')"
          >
          <div class="buttonContent">{{ this.constants.translatedText.AllDepartments }}</div>
        </button>  
        <!-- Each button -->
          <button
            *ngFor="let department of selections.debtsTable.departments"
            class="btn btn-primary"
            [ngClass]="{ 'active': department.label == selections.debtsTable.buttonSelections.department?.label }"
            (click)="doFiltering('Department', 'department', department)"
          >
            <div class="buttonContent wideButton">
               <div *ngIf="!!department.label">D{{ department.label }}</div> 
              <div>{{ department.amount | cph:'currency':0 }}</div>
            </div>
          </button>
        </div>

        <!-- For choosing ageing time period -->
        <div class="buttonGroup filterChoiceArea">
          <button
            class="btn btn-primary allButton"
            [ngClass]="{ 'active': !selections.debtsTable.buttonSelections.ageing }"
            (click)="doFiltering('Ageing', 'ageing')"
          >
            <div class="buttonContent">{{ constants.translatedText.AllAges }}</div>          
          </button>  
          <button
            *ngFor="let ageing of selections.debtsTable.ageings"
            class="btn btn-primary"
            [ngClass]="{ 'active': ageing.label == selections.debtsTable.buttonSelections.ageing?.label }"
            (click)="doFiltering('Ageing', 'ageing', ageing)"
          >
            <div class="buttonContent wideButton">
              <div>{{ ageing.label }}</div>
              <div>{{ ageing.amount | cph:'currency':0 }}</div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <instructionRow  [message]="'Click on the expand icon &gt; to open up column groups to see more detail'"></instructionRow>

    <div id="gridHolder">
      <!-- Top Total -->
    <div id="topTotal">
        {{ constants.translatedText.TotalOf }} {{ constants.pluralise(selections.debtsTable.totalDebtCount,'debt', 'debts') }}, 
        {{ constants.translatedText.TotalValue }}: {{ selections.debtsTable.totalDebtBalance | cph:'currency':0 }}
      </div>

      <div id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>

      <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions"></ag-grid-angular>
            
      <!-- Bottom total for selected rows -->
      <div *ngIf="haveSelectedCells" id="bottomTotal" class="animated slideInRight">
        Selected {{ constants.pluralise(selections.debtsTable.selectedDebtCount, 'debt', 'debts') }}, 
        total value: {{ selections.debtsTable.selectedDebtBalance | cph:'currency':0 }}
      </div>
         
      
    </div>
</div>
  `
  ,
  //styleUrls: ["./../../../styles/components/_agGrid.scss"],
  styles: [
    `
    ag-grid-angular.hidden{opacity:0}
    #topArea{width: 100%;display:flex;justify-content:space-between;margin-bottom:0.5em;}
    #buttonsArea{display:flex;justify-content:space-between;width:100%;margin:0em auto;}
    #buttonsArea .btn {padding:0.2em 0.6em;min-width:6em;}
    
    .filterChoiceArea .buttonContent{display:flex;flex-direction:column;align-items:center;min-height:3em;justify-content:center;}

    .filterChoiceArea{margin:0px!important}
    
    #topTotal{position:absolute;top:-2em;right:0em;font-weight:700;display: inline-block;  height:2em;    background: var(--brightColourLight);border-radius: 0.2em;padding:0.5em 1em;display:flex;align-items:center;}
    #bottomTotal{animation-duration:0.2s;position:absolute;bottom:0em;right:0em;font-weight:700;display: inline-block;  height:2em;    background: var(--brightColourLight);border-radius: 0.2em;padding:0.5em 1em;display:flex;align-items:center;}
    #hint{position:absolute;top:-2em;left:0em;}
    
    @media (min-width: 0px) and (max-width: 1920px) and (hover:none){
      #buttonsArea{display:block;}
      #buttonsArea .buttonGroup{margin: 0.3em 1em;        min-width: 59em;        display: inline-block;}
    }
    

    #gridHolder{ position:relative;  width: 100%;    height:100%;margin: 0em auto;margin-top: 2em; }
      @media (min-width: 0px) and (max-width: 1920px) { 
        #buttonsArea .btn{min-width:5.5em;}
        #gridHolder{margin-top:2em;height:100%}
      }

       ag-grid-angular{width:100%;height:100%;     }

        #debtsContainer {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
    
  
     
  `
  ]
})

export class DebtsTableComponent implements OnInit,OnDestroy {
  private destroy$ = new Subject<void>();
  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  mainTableGridOptions: GridOptions;
  haveSelectedCells: boolean;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public columnTypeService: ColumnTypesService,
  ) { }

  
  ngOnInit() {
    this.initialiseDebtsTable();
    this.summariseDebts(this.selections.debts.debtsFiltered);
    this.setGridOptions();
    
    this.selections.debts.debtsDataChangedEmitter
    .pipe(takeUntil(this.destroy$))
    .subscribe(() => {
      if (this.gridApi) {
        this.gridApi.setRowData(this.selections.debts.debtsFiltered);
        this.summariseDebts(this.selections.debts.debtsFiltered);
      }
    })
  }
  ngOnDestroy(): void {
    this.selections.debtsTable.gridSelections.sortState = this.gridApi.getModel();
    this.destroy$.next();
    this.destroy$.complete();
}

  initialiseDebtsTable() {
    let debtAgeings = [
      { description: 'Current', field: 'current', over: -999, under: 1 },
      { description: '1-7 days', field: 'oCurrent', over: 0, under: 8 },
      { description: '8-20 days', field: 'o7', over: 7, under: 21 },
      { description: '21-30 days', field: 'o20', over: 20, under: 31 },
      { description: '31-60 days', field: 'o30', over: 30, under: 61 },
      { description: 'over 60 days', field: 'o60', over: 60, under: 99999999 },
    ];

    if (!this.selections.debtsTable) {
      let ageings: string[] = debtAgeings.map(x => x.description);
      let ageingsFormatted: LabelAndAmount[] = [];
      let departments: number[] = [...new Set(this.selections.debts.debtsFiltered.map(x => x.Department))].sort((a, b) => a - b);
      let departmentsFormatted: LabelAndAmount[] = [];
      let debts: string[] = [...new Set(this.selections.debts.debtsFiltered.map(x => x.DebtType))];
      let debtsFormatted: LabelAndAmountAndAbbreviation[] = [];

      ageings.forEach(ageing => {
        ageingsFormatted.push({ label: ageing, amount: 0 });
      })

      departments.forEach(department => {
        departmentsFormatted.push({ label: department.toString(), amount: 0 });
      })

      debts.forEach(debt => {
        debtsFormatted.push({ label: debt, amount: 0, abbreviation: debt.split(' ')[0] });
      })

      this.selections.debtsTable = {
        totalDebtBalance: 0,
        totalDebtCount: 0,
        selectedDebtBalance: 0,
        selectedDebtCount: 0,
        buttonSelections: {
          ageing: null,
          department: null,
          debtType: null,
        },
        gridSelections: {
          filterState: null,
          sortState: null,
        },
        ageings: ageingsFormatted,
        departments: departmentsFormatted,
        debtTypes: debtsFormatted
      }
    }
  }

  summariseDebts(debts: DebtsDebtsSummaryRow[]) {
    //totaldebt
    this.selections.debtsTable.totalDebtBalance = this.constants.sum(debts.map(x => x.Value));
    this.selections.debtsTable.totalDebtCount = debts.length;

    //ageings
    this.selections.debtsTable.ageings.forEach(ageing => {
      let ageingDebts = debts.filter(x => x.Ageing == ageing.label);
      ageing.amount = this.constants.sum(ageingDebts.map(x => x.Value));
    });

    //departments
    this.selections.debtsTable.departments.forEach(dept => {
      let deptDebts = debts.filter(x => x.Department.toString() == dept.label);
      dept.amount = this.constants.sum(deptDebts.map(x => x.Value));
    });

    //debtTypes
    this.selections.debtsTable.debtTypes.forEach(debtType => {
      let debtTypeDebts = debts.filter(x => x.DebtType == debtType.label);
      debtType.amount = this.constants.sum(debtTypeDebts.map(x => x.Value));
    });
  }

  setGridOptions() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.selections.debts.debtsFiltered,
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onSelectionChanged: (params) => this.onSelectionChanged(),
      onFilterChanged: (params) => this.onFilterChanged(),
      onGridReady: (params) => this.onGridReady(params),
      rowSelection: 'multiple',
      //rowDeselection: true,
      animateRows: false,
      defaultColDef: {
        resizable: true,
        sortable: true,
        filter: true,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
        floatingFilter: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      onColumnGroupOpened:(params)=>this.gridApi.sizeColumnsToFit(),
      columnDefs: this.getColumnDefs()
    }
  }

  getColumnDefs() {
    let colDefs = [
      { headerName: this.constants.translatedText.Site, field: 'SiteDescription', colId: 'SiteDescription', type: 'labelSetFilter' },
      { headerName: 'Dept', field: 'Department', colId: 'Department', type: 'labelSetFilter' },
      { headerName: 'Type', field: 'DebtType', colId: 'DebtType', type: 'labelSetFilter' },
      { headerName: 'Account', field: 'AccountNo', colId: 'AccountNo', type: 'label' },
      { headerName: 'Account Name', field: 'AccountName', colId: 'AccountName', type: 'labelSetFilter' },
      { headerName: 'T', field: 'Type', colId: 'Type', type: 'labelSetFilter' },
      { headerName: 'S', field: 'Source', colId: 'Source', type: 'labelSetFilter' },
      {
        headerName: 'Debt details', children: [
          { headerName: 'Invoiced To', field: 'Invoicedto', colId: 'Invoicedto', type: 'labelSetFilter' },
          { headerName: 'Name', field: 'Name', colId: 'Name', type: 'labelSetFilter', columnGroupShow: 'open' },
          { headerName: this.constants.translatedText.Customer, field: 'Customer', colId: 'Customer', type: 'labelSetFilter', columnGroupShow: 'open' },
          { headerName: 'Exec', field: 'Exec', colId: 'Exec', type: 'labelSetFilter', columnGroupShow: 'open' },
          { headerName: 'Stock No', field: 'StockNo', colId: 'StockNo', type: 'label', columnGroupShow: 'open' },
          { headerName: 'Customer Ref', field: 'CustomerRef', colId: 'CustomerRef', type: 'label', columnGroupShow: 'open' },
          { headerName: 'Misc Ref', field: 'MiscRef', colId: 'MiscRef', type: 'label', columnGroupShow: 'open' },
          { headerName: 'Doc No', field: 'DocNo', colId: 'DocNo', type: 'label', columnGroupShow: 'open' },
          { headerName: 'Doc Date', field: 'DocumentDate', colId: 'DocumentDate', type: 'dateLongYear', columnGroupShow: 'open' },
          { headerName: 'Due Date', field: 'DueDate', colId: 'DueDate', type: 'dateLongYear', columnGroupShow: 'open' },
          { headerName: this.constants.translatedText.Age, field: 'Age', colId: 'Age', type: 'number', columnGroupShow: 'open' },
          { headerName: 'Ageing', field: 'Ageing', colId: 'Ageing', type: 'labelSetFilter', columnGroupShow: 'open' },
          // { headerName: '', cellRenderer: DebtCommentsComponent }
        ],
      },
      { headerName: 'Outstanding', field: 'Value', colId: 'Value', type: 'currency',  },
    ]

    this.gridHelpersService.workoutColWidths(this.selections.debts.debtsFiltered, colDefs, 10, 8);
    return colDefs;
  }

  doFiltering(fieldName: string, choiceTypeName: string, choice?: LabelAndAmount) {
    let filterComponent: IFilterComp = this.gridApi.getFilterInstance(fieldName);
    if (!choice) {
      filterComponent.setModel(null);
      this.gridApi.onFilterChanged();
      this.selections.debtsTable.buttonSelections[choiceTypeName] = null;
      return;
    }
    filterComponent.setModel({
      type: "contains",
      filter: choice.label
    })
    this.selections.debtsTable.buttonSelections[choiceTypeName] = choice;
    this.gridApi.onFilterChanged();
  }

  onFilterChanged() {
    //store the filter state
    this.selections.debtsTable.gridSelections.filterState = this.gridApi.getFilterModel();
    //run the summary numbers
    let displayedRows: DebtsDebtsSummaryRow[] = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      displayedRows.push(node.data)
    })
    this.summariseDebts(displayedRows);
  }

  resizeGrid() {
    // if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };
    this.selections.triggerSpinner.next({ show: false });
  }

  onSelectionChanged() {
    let selectedRows: any[] = this.gridApi.getSelectedRows();
    this.haveSelectedCells = selectedRows.length > 0;
    this.selections.debtsTable.selectedDebtCount = selectedRows.length;
    this.selections.debtsTable.selectedDebtBalance = this.constants.sum(selectedRows.map(x => x.Value));
  }

  excelExport() {
    let tableModel: IRowModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, this.constants.translatedText.Debts, 1.5);
  }
}
