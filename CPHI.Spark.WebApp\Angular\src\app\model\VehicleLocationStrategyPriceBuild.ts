import { RetailerSite } from "./RetailerSite";
import { StrategyPriceBuildUpItem } from "./StrategyPriceBuildUpItem";



export class VehicleLocationStrategyPriceBuild {

    constructor(itemIn: VehicleLocationStrategyPriceBuild) {
        this.RetailerSite = new RetailerSite(itemIn.RetailerSite);
        this.RetailRating = itemIn.RetailRating;
        this.DaysToSell = itemIn.DaysToSell;
        this.StrategyPrice = itemIn.StrategyPrice;
        this.BuildUpItems = itemIn.BuildUpItems;
    }
    RetailerSite: RetailerSite;
    RetailRating: number;
    DaysToSell: number;
    StrategyPrice: number;
    BuildUpItems: StrategyPriceBuildUpItem[];
}
