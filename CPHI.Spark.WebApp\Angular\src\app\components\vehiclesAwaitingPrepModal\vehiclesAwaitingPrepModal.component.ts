import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { StockListRow } from "src/app/pages/stockList/StockListRow";
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { CphPipe } from '../../cph.pipe';
import { StockModalMerchRow } from '../../model/sales.model';
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
import { StockItemModalComponent } from '../stockItemModal/stockItemModal.component';

import { throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { GridOptions } from 'ag-grid-community';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { localeEs } from 'src/environments/locale.es.js';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDealDataService } from 'src/app/services/getDeals.service';

import { DealDetailsComponent } from '../dealDetails/dealDetails.component';
import { CheckboxInCellComponent } from 'src/app/_cellRenderers/checkbox.component';

@Component({
  selector: 'app-vehiclesAwaitingPrepModal',
  templateUrl: './vehiclesAwaitingPrepModal.component.html',
  styleUrls: ['./vehiclesAwaitingPrepModal.component.scss']
})


export class VehiclesAwaitingPrepModalComponent implements OnInit {

  @Input() public data: StockModalMerchRow[];
  @ViewChild('vehiclesAwaitingPrepModal', { static: true }) vehiclesAwaitingPrepModal: ElementRef;
  
  public gridApi;
  public gridColumnApi;

  mainTableGridOptions: GridOptions;

  gridApiColumnDefinitions: any;

  frameworkComponents: { agColumnHeader: any; };

  showGrid = false;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public getData: GetDataMethodsService,
    public getDealData: GetDealDataService,
    public gridHelpers: AGGridMethodsService,
    public columnTypeService: ColumnTypesService,
    public excel: ExcelExportService

  ) {



  }

  ngOnDestroy() { }



  ngOnInit() {
    this.initParams();
    this.launchModal();
  }

  initParams() {

    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpers.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      suppressPropertyNamesCheck: true,
      context: { thisComponent: this },
      onCellMouseOver: (params) => {
        //this.onCellMouseOver(params);
      },
      onCellDoubleClicked: (params) => {
        this.onCellDblClick(params);
      },
      getRowHeight: (params) => {
        return 25
      },
      onFirstDataRendered:()=>{this.showGrid = true; this.selections.triggerSpinner.next({ show: false });},
      defaultColDef: {
        floatingFilter: true,
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpers.getFilterListItemHeight() }, autoHeight: true,
        //suppressColumnMoveAnimation: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
        checkbox: { cellClass: "agAlignCentre ag-right-aligned-cell" }
      },
      columnDefs: [],
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }

    this.mainTableGridOptions.columnDefs = [
      { headerName: '#', cellRenderer: (params) => params.rowIndex + 1, field: '', width: 15, type: 'label', },
      { headerName: this.constants.translatedText.Site, field: 'SiteName', width: 30, type: 'label', },
      { headerName: 'Reg', field: 'Reg', width: 30, type: 'label', },
      { headerName: this.constants.translatedText.StockNumber, field: 'StockNumber', cellRenderer: (params) => params.data.StockNumber == '0' ? '' : params.data.StockNumber, width: 30, type: 'label', },
      { headerName: 'Customer', field: 'Customer', width: 30, type: 'label', },
      { headerName: 'Type', sort:'desc', field: 'Type', width: 20, type: 'label', },
      { headerName: this.constants.translatedText.Age, sort:'asc', field: 'Age', width: 15, type: 'number', },
      { headerName: this.constants.translatedText.Description, field: 'Description', width: 80, type: 'label', },
      { headerName: 'SIV', field: 'SIV', width: 50, type: 'currency', },
      { headerName: 'Selling Price', field: 'SellingPrice', width: 50, type: 'currency', },
      { headerName: 'Over 3?', field: 'Over3', width: 20, cellRendererFramework: CheckboxInCellComponent, type: 'checkbox' },
      { headerName: 'No Line?', field: 'NoLine', width: 20, cellRendererFramework: CheckboxInCellComponent, type: 'checkbox'  },
    ]
  }
 
  launchModal(){
     //launch modal
     this.selections.triggerSpinner.next({ show: false });

     this.modalService.open(this.vehiclesAwaitingPrepModal, { windowClass: 'stockReportModal', size: 'lg', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
       //'okd'
       this.passBack()
     },
       //closed
       (reason) => {
         //cancelled, so no passback      
         this.activeModal.close()
         //this.modalService.dismissAll();

     });
  }

  passBack() {
    let thingToPassBack = 'foo';
    this.activeModal.close(thingToPassBack); //<--put things in here
  }



  loadVehicle(stock: StockModalMerchRow) {

    // Already sold vehicle - get DealDetailModal
    if(stock.Type === 'Sold')
    { 

      this.getDealDetailModal(stock.Id);
    }
    // InStock - get StockModal
    else
    {
      this.getStockModal(stock.Id);
    }

  }

  getStockModal(stockId: number): void
  {
    this.getData.getStockListRowItem(stockId).pipe(

      catchError((error) => {
        this.constants.toastDanger('Failed to get stock item.')

        setTimeout(() => {
          this.selections.triggerSpinner.next({ show: false });
        }, 20)

        return throwError(error);
      })
    ).subscribe((res: StockListRow[]) => {

      // If we don't return anything for the stock
      if(res.length === 0){ 
        this.constants.toastDanger('Failed to get stock item.')
        
        setTimeout(() => {
          this.selections.triggerSpinner.next({ show: false });
        }, 20)

        return;
      }

      const modalRef = this.modalService.open(StockItemModalComponent, { keyboard: true, size: 'lg' });

      modalRef.componentInstance.givenStockItem = res[0];

      modalRef.result.then((result) => { });
    });
  }

  getDealDetailModal(dealId: number): void
  {
    const modalRef = this.modalService.open(DealDetailsComponent, { keyboard: true, size: 'lg' });

    modalRef.componentInstance.givenDealId = dealId;

    modalRef.result.then((result) => { });
  }

  onCellDblClick(params) {
    this.loadVehicle(params.data);
  }


  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);
    this.gridApi.setRowData(this.data);
    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();
  }


  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }

  }



  refreshCells() {
    if (this.gridApi) {
      this.gridApi.refreshCells();
    }
  }


  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, this.selections.vehiclesAwaitingPrepModal.excelExportName, 1.3);
  }



















}
