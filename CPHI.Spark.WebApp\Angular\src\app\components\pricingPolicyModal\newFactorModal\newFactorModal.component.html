<div class="modal-header">
   <h4 class="modal-title">Choose New Factor</h4>
   <button class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
   </button>
</div>

<div class="modal-body">

   <div class="newFactorTable">

      <div class="d-flex align-items-center justify-content-between newFactorRow"
           *ngFor="let factor of additionalFactors">
         <div class="icon ">
            <i *ngIf="factor.Name == StrategyFactorName.RR_DL_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RR_DS_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RR_DB_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DTS_DL_Matrix" class="fas fa-grid"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RetailRatingBand" class="fas fa-tachometer-average"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysListedBand" class="fas fa-calendar-day"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysListed" class="fas fa-calendar-day"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysInStockBand" class="fas fa-calendar-day"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysInStock" class="fas fa-calendar-day"></i>
            <i *ngIf="factor.Name == StrategyFactorName.OnBrandCheck" class="fas fa-shield-check"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RetailerName" class="fas fa-map-marker"></i>
            <i *ngIf="factor.Name == StrategyFactorName.Brand" class="fas fa-car"></i>
            <i *ngIf="factor.Name == StrategyFactorName.ModelName" class="fas fa-car"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MatchCheapestCompetitor" class="fas fa-ranking-star"></i>
            <i *ngIf="factor.Name == StrategyFactorName.AchieveMarketPositionScore" class="fas fa-ranking-star"></i>
            <i *ngIf="factor.Name == StrategyFactorName.DaysToSell" class="fas fa-calendar-exclamation"></i>
            <i *ngIf="factor.Name == StrategyFactorName.ValuationChangeUntilSell"
               class="fas fa-calendar-exclamation"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MinimumProfit" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MinimumPricePosition" class="fas fa-percentage"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundToNearest" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RoundToPriceBreak" class="fas fa-sterling-sign"></i>
            <i *ngIf="factor.Name == StrategyFactorName.SpecificColour" class="fas fa-palette"></i>
            <i *ngIf="factor.Name == StrategyFactorName.AgeAndOwners" class="fas fa-users"></i>
            <i *ngIf="factor.Name == StrategyFactorName.WholesaleAdjustment" class="fa fa-circle-sterling"></i>
            <i *ngIf="factor.Name == StrategyFactorName.RetailRating10sBand" class="fa fa-tachometer-average"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MakeFuelType" class="fa fa-gas-pump"></i>
            <i *ngIf="factor.Name == StrategyFactorName.MakeAgeBand" class="fa fa-clock-three"></i>
            <i *ngIf="factor.Name == StrategyFactorName.Mileage" class="fa fa-tachometer-average"></i>
            <i *ngIf="factor.Name == StrategyFactorName.PerformanceRatingScore" class="fas fa-eyes"></i>
         </div>
         <div class="name">
            {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
         </div>
         <div class="explanation">
            <div class="newFactorExplanation">{{ service.factorExplanation(factor) }}</div>
         </div>
         <div class="chooseFactor">
            <button class="btn btn-success" (click)="chooseNewFactor(factor)">Add factor</button>
         </div>
      </div>
   </div>

</div>
<div class="modal-footer">
   <button class="btn btn-primary" (click)="closeModal()">Close</button>
</div>
