<div class="dashboard-tile-inner">
    <div [ngClass]="{'clickable':isClickableHeader()}" class="tileHeader" (click)="navigateToDealsForTheMonth()">
      <div class="headerWords">
        <h5>
          <span *ngIf="departmentName=='New'"> {{constants.translatedText.New}}</span>
          <span *ngIf="departmentName=='Used'"> {{constants.translatedText.Used}} </span>
          <span *ngIf="departmentName=='Fleet'"> {{constants.translatedText.Fleet}} </span>
  
          <span class="visibleAboveSm"> {{constants.translatedText.Performance}} {{constants.translatedText.For}}</span>
          
          <span *ngIf="timePeriod=='Month'"> {{constants.translatedText.TheMonth}} </span>
          <span *ngIf="timePeriod=='Week'"> {{constants.translatedText.TheWeek}} </span>
          <span *ngIf="timePeriod=='Yesterday'"> {{constants.translatedText.Yesterday}} </span>
  
        </h5>
  
      </div>
    </div>

    <div class="headRow">
        <div class="dataUnit"><span class="title">{{constants.translatedText.Dashboard_InvoicedDonut_LastMonth}}</span><span>botton</span></div>
        <div class="dataUnit"><span class="title">{{constants.translatedText.Dashboard_InvoicedDonut_LastYear}}</span><span>botton</span></div>
    </div>
    <div class="headRow centre">
        <div class="dataUnit"><span class="title">{{constants.translatedText.Dashboard_InvoicedDonut_Invoices}}</span><span>botton</span></div>
    </div>
  
  
    <div id="innerContentHolder">
      <div id="chartHolder">
        <div (click)="navigateToOrderBook()" id="actualUnits">{{donutData?.ActualUnits|cph:'number':0}}</div>
        <div id="budgetUnits">/{{donutData.TargetUnits|cph:'number':0}}</div>
        <canvas id="chartCanvas" #myChart></canvas>
      </div>
  
      <div id="bars">
        <div id="labels">
          <div class="amountActual clickable" (click)="navigateToOrderBook()">
            {{donutData.ActualUnits/1000|cph:'currency':0}}k</div>
          <div class="amountBudget">{{donutData.TargetUnits/1000|cph:'currency':0}}k</div>
        </div>
        <div id="barRange">
          <div id="actualBar" (click)="navigateToOrderBook()" class="bar" [ngStyle]="{'width.%':actualWidth}"></div>
          <div id="budgetBar" class="bar" [ngStyle]="{'width.%':budgetWidth}"></div>
        </div>
      </div>

    </div>
  </div>