﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.WebApp.DataAccess;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Model.ViewModels.RRG;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Exchange.WebServices.Data;
using CPHI.Spark.Model.ViewModels.Vindis;


namespace CPHI.Spark.WebApp.Service
{
   public interface IDealService
   {

      Task<IEnumerable<DealDoneThisWeek>> GetDealsDoneThisWeek(DealsDoneThisWeekParams parms, int userId);
      Task<IEnumerable<DealsForTheMonthWeek>> GetDealsForTheMonth(DealsDoneForTheMonthParams parms, int userId);
      Task<IEnumerable<WeeklyDealBreakdown>> GetWeeklyDealBreakdown(DealsDoneForTheMonthWeekAnalysisParams parms, int userId);
      Task<IEnumerable<DealPopover>> GetDealPopover(int dealId, int userId);
      Task<IEnumerable<CommentVM>> GetDealComments(int dealId, int userId);
      Task<IEnumerable<WhiteboardDealsForSalesExec>> GetWhiteboard(DateTime startDate, int userId, string sites, string orderTypes, string vehicleTypes, string franchises,
          int onlyLates, int excludeLates, int? managerId);
      Task<IEnumerable<FinanceAndAddonSiteRow>> GetFinanceAddons(FinanceAddonsParams parms, int userId);
      Task<IEnumerable<FinanceAndAddonSalesExecRow>> GetFinanceAddonsForSite(FinanceAddonsForSiteParams parms, int userId);
      Task<IEnumerable<PerformanceLeagueSummaryItem>> GetPerformanceLeague(string yearMonths, string departments, bool isSalesExecView, int userId);
      Task<IEnumerable<SalesPerformanceRow>> GetSalesPerformanceNew(SalesPerformanceParams parms, int userId);
      Task<IEnumerable<SalesPerformanceOrderRateRow>> GetSalesPerformanceOrderRate(SalesPerformanceOrderRateParams parms, int userId);
      Task<IEnumerable<HandoverDiaryItem>> GetHandoverDiary(HandoverDiaryParams parms, int userId);
      Task<DateTime> GetOldestAllowableDealCache();
      Task<IEnumerable<CommentVM>> GetCommentsForDeal(int dealId, int userId);
      Task<TodayNewUsedOrder> GetTodayNewUsedOrders(int[] siteIds, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<SalesActivityVM>> GetSalesActivities(string vehicleType, string monthYear, int userId);
      Task<IEnumerable<SalesActivityVM>> GetSalesActivitiesForSite(string siteIds, string vehicleType, string monthYear, int? salesManagerId, int userId);
      Task<IEnumerable<GDPRVMRowVM>> GetGDPRs(string orderTypeIds, string monthYear, int userId);
      Task<IEnumerable<GDPRVMRowVM>> GetGDPRsForSite(string siteIds, string orderTypeIds, string monthYear, int? salesManagerId, int userId);
      Task<IEnumerable<DailyOrdersSiteDetail>> GetDailyOrdersSiteDetails(DailyOrdersSiteDetailParams parms);
      Task<IEnumerable<DailyOrderDetailItem>> GetDailyOrdersOrderDetails(DailyOrderDetailItemParams parms);
      Task<IEnumerable<DonutData>> GetDonutPerformanceWTD(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<DonutData>> GetDonutPerformanceYesterday(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<DepartmentProfitPerUnits>> GetDepartmentProfitPerUnits(string timePeriod, DealerGroupName dealerGroup);
      Task<List<SuperCupMatchRow>> GetSuperCupMatchRows();
      Task<List<SuperCupMatchRow>> GetSuperCupTwoMatchRows();
      Task<IEnumerable<DealfileSentDate>> GetDealfileSentDate(string stockNumber, int userId);
      Task<int> UpdateDealfileSentDate(UpdateDealfileSentParams parms);
      Task<RunRateData> GetRunRateData(RunRateParams parms, int userId);
      Task<IEnumerable<PerformanceTrendsVM>> GetPerformanceTrends(PerformanceTrendsParams parms, int userId);
      Task<IEnumerable<PerformanceTrendsVM>> GetPerformanceTendsForSite(PerformanceTrendsParams parms, int userId);
      Task<DealModalWithStockItem> GetDealDetailModal(int siteId, int userId);
      Task<int> UpdateQualifyingPartEx(UpdateQualifyingPartExParams parms, int userId);
      Task<int> UpdateSalesmanId(UpdateSalesmanIdParams parms, int userId);
      Task<List<PersonAndDealCount>> GetOrdersForSalesIncentive(int userId);
      Task<IEnumerable<SalesmanWithId>> GetVindisExecsForSiteForMonth(SalesmanWithIdParams parms);
   }

   public class DealService : SalesPerformanceCalculationService, IDealService
   {

      //private readonly IDealDataAccess dealDataAccess;
      private readonly IUserService userService;
      private readonly ITodayNewUsedOrdersCache todayNewUsedOrdersCache;
      private readonly IFinancialLinesDataAccess financialLinesDataAccess;
      private readonly IRegistrationsService registrationsService;
      private readonly IConfiguration configuration;



      public DealService(
          IUserService userService,
          ITodayNewUsedOrdersCache todayNewUsedOrdersCache,
          IFinancialLinesDataAccess financialLinesDataAccess,
          IRegistrationsService registrationsService,
          IConfiguration configuration
          )
      {
         this.userService = userService;
         this.todayNewUsedOrdersCache = todayNewUsedOrdersCache;
         this.financialLinesDataAccess = financialLinesDataAccess;
         this.registrationsService = registrationsService;
         this.configuration = configuration;

         //DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         //string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         //string _connectionString = configuration.GetConnectionString(dgName);
         //dealDataAccess = new DealDataAccess(_connectionString);
      }


      private DealDataAccess constructDealDataAccess()
      {
         DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var dealDataAccess = new DealDataAccess(_connectionString);
         return dealDataAccess;
      }

      public async Task<List<SuperCupMatchRow>> GetSuperCupMatchRows()
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         IEnumerable<SuperCupRow> sourceData = await dealDataAccess.GetSuperCupRows(dealerGroup);
         var dataLookup = sourceData.ToDictionary(x => x.SiteDescription);

         List<SuperCupMatchRow> results = SuperCupCalculationService.CreateSuperCupResults(sourceData, dataLookup);

         return results;

      }

      public async Task<List<SuperCupMatchRow>> GetSuperCupTwoMatchRows()
      {
         var lmtos = await registrationsService.GetOemOrdersSiteRows(new DateTime(2022, 7, 1), "LMTOrders", false, true);
         var zoes = await registrationsService.GetOemOrdersSiteRows(new DateTime(2022, 7, 1), "LMTZoeOrders", false, true);

         List<SuperCupMatchRow> results = SuperCupCalculationService.BuildUpSuperCupTwo(lmtos, zoes);
         return results;

      }

      public async Task<IEnumerable<DealDoneThisWeek>> GetDealsDoneThisWeek(DealsDoneThisWeekParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDealsDoneThisWeek(parms, userId);
      }

      public async Task<DateTime> GetOldestAllowableDealCache()
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         return await dealDataAccess.GetOldestAllowableDealCache(dealerGroup);
      }

      public async Task<IEnumerable<DealsForTheMonthWeek>> GetDealsForTheMonth(DealsDoneForTheMonthParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         var results = await dealDataAccess.GetDealsForTheMonth(parms, userId);
         List<DealsForTheMonthWeek> resultsThisMonth = DealsForTheMonthService.BuildUpDealsForTheMonth(parms, results);
         return resultsThisMonth;
      }

      // GetWeeklyDealBreakdown
      public async Task<IEnumerable<WeeklyDealBreakdown>> GetWeeklyDealBreakdown(DealsDoneForTheMonthWeekAnalysisParams parms, int userId)
      {
         List<WeeklyDealBreakdown> result = new List<WeeklyDealBreakdown>();
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<WeeklyDealBreakdownDeal> allDealsForWeek = await dealDataAccess.GetWeeklyDealBreakdown(parms, userId);
         DealsForTheMonthService.
                     GenerateWeeklyDealBreakdown(parms, result, allDealsForWeek);

         return result;
      }

      public async Task<IEnumerable<SalesmanWithId>> GetVindisExecsForSiteForMonth(SalesmanWithIdParams parms)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetVindisExecsForSiteForMonth(parms.SiteId, parms.Year, parms.Month);
      }

      public async Task<IEnumerable<DealPopover>> GetDealPopover(int dealId, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDealPopover(dealId, userId);
      }

      public async Task<IEnumerable<CommentVM>> GetDealComments(int dealId, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         bool byStockNumber = isVindis == true ? false : true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDealComments(dealId, byStockNumber, userId);
      }



      public async Task<IEnumerable<WhiteboardDealsForSalesExec>> GetWhiteboard(DateTime startDate, int userId, string sites, string orderTypes, string vehicleTypes, string franchises,
          int onlyLates, int excludeLates, int? managerId)
      {

         List<WhiteboardDealsForSalesExec> whiteboardRows = new List<WhiteboardDealsForSalesExec>();
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         bool byStockNumber = isVindis == true ? false : true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<Whiteboard> x = await dealDataAccess.GetWhiteboard(startDate, userId, sites, orderTypes, vehicleTypes, franchises, onlyLates, excludeLates, byStockNumber, managerId);

         List<Whiteboard> SortedBySalesman = x.OrderBy(o => o.Salesman_Id).ToList();

         ILookup<int, Whiteboard> dealsBySalesmen = SortedBySalesman.ToLookup(x => x.Salesman_Id);

         if (SortedBySalesman.Count < 1) { return whiteboardRows; }

         foreach (var each in dealsBySalesmen)
         {
            int thisSalesmanId = each.Key;
            List<Whiteboard> thisSalesmanDeals = each.ToList();

            foreach (Whiteboard eachSalesman in thisSalesmanDeals)
            {
               eachSalesman.Customer = eachSalesman.Customer.Replace("Mrs ", "")
                   .Replace("Mr ", "")
                   .Replace("Ms ", "")
                   .Replace("Miss ", "");


               if (eachSalesman.Customer.Split(' ').Length > 1)
               {
                  eachSalesman.Customer = eachSalesman.Customer.Split(' ').Skip(1).FirstOrDefault();
               }

            }

            whiteboardRows.Add(MakeRow(thisSalesmanId, thisSalesmanDeals));
         }

         return whiteboardRows;
      }

      private WhiteboardDealsForSalesExec MakeRow(int thisSalesmanId, List<Whiteboard> thisSalesmanDeals)
      {
         WhiteboardDealsForSalesExec dealsForExec = new WhiteboardDealsForSalesExec();
         dealsForExec.Salesman_Id = thisSalesmanId;
         dealsForExec.SalesmanName = thisSalesmanDeals.First().Salesman;
         dealsForExec.Deals = thisSalesmanDeals;
         return dealsForExec;
      }

      public async Task<IEnumerable<FinanceAndAddonSiteRow>> GetFinanceAddons(FinanceAddonsParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<FinanceAndAddonItem> financeItems = await dealDataAccess.GetFinanceAddons(parms, userId);

         if (parms.IncludeTargets)
         {
            IEnumerable<FinanceInsuranceTargetVM> targets = await dealDataAccess.GetFinanceInsuranceTargets(parms.YearMonths, parms.IncludeNewTargets, parms.IncludeUsedTargets, userId);
            return FinanceAddOnsService.BuildUpSitesFISummary(parms, isVindis, financeItems, targets);
         }
         else
         {
            return FinanceAddOnsService.BuildUpSitesFISummary(parms, isVindis, financeItems, null);
         }
      }

      public async Task<IEnumerable<FinanceAndAddonSalesExecRow>> GetFinanceAddonsForSite(FinanceAddonsForSiteParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         bool groupByManager = false;
         if (isVindis && !parms.SalesManagerId.HasValue) groupByManager = true;
         DealDataAccess dealDataAccess = constructDealDataAccess();

         if (parms.IncludeTargets)
         {
            IEnumerable<FinanceInsuranceTargetVM> targets = await dealDataAccess.GetFinanceInsuranceTargets(parms.YearMonths, parms.IncludeNewTargets, parms.IncludeUsedTargets, userId);

            IEnumerable<FinanceAndAddonItem> results = await dealDataAccess.GetFinanceAddonsForSite(parms, userId);

            return FinanceAddOnsService.BuildUpRows(isVindis, groupByManager, results, targets);
         }
         else
         {
            IEnumerable<FinanceAndAddonItem> results = await dealDataAccess.GetFinanceAddonsForSite(parms, userId);

            return FinanceAddOnsService.BuildUpRows(isVindis, groupByManager, results, null);
         }

      }

      public async Task<IEnumerable<PerformanceLeagueSummaryItem>> GetPerformanceLeague(string yearMonths, string departments, bool isSalesExecView, int userId)
      {
         int includeFleetSites = 0;
         int? excludeGroupFleetSite = null;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         if (isVindis)
         {
            includeFleetSites = 1;
            excludeGroupFleetSite = 1;
         }

         departments = departments.Replace("VN", "New");
         departments = departments.Replace("Flotas", "Fleet");
         departments = departments.Replace("VO", "Used");
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetPerformanceLeague(yearMonths, departments, includeFleetSites, excludeGroupFleetSite, isSalesExecView, userId);
      }


      public async Task<IEnumerable<SalesPerformanceOrderRateRow>> GetSalesPerformanceOrderRate(SalesPerformanceOrderRateParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         // IEnumerable<SiteVM> sites = (await sitesDataAccess.GetSites(userId, dealerGroup,isVindis)).Where(x => x.IsSales && x.IsActive && x.RegionDescription != "Midlands").OrderBy(x => x.SortOrder);
         // IEnumerable<SalesPerformanceOrderRateMeasure> doneBySite = await dealDataAccess.GetSalesPerformanceOrderRate(parms, userId);
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var siteDataAccess = new SiteDataAccess(_connectionString);
         IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, isVindis)).Where(x => x.IsSales && x.IsActive && x.RegionDescription != "Midlands").OrderBy(x => x.SortOrder);
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<SalesPerformanceOrderRateMeasure> doneBySite = await dealDataAccess.GetSalesPerformanceOrderRate(parms, userId);

         List<SalesPerformanceOrderRateRow> results = new List<SalesPerformanceOrderRateRow>();

         MakeSiteRowsOrderRate(sites, doneBySite, results);
         MakeRegionRowsOrderRate(sites, doneBySite, results);
         MakeTotalRowOrderRate(doneBySite, results);

         return results;
      }

      public async Task<IEnumerable<SalesPerformanceRow>> GetSalesPerformanceNew(SalesPerformanceParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         bool isRRG = dealerGroup == Model.DealerGroupName.RRGUK;

         //get sites
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);

         //RRGUKConnection
         var siteDataAccess = new SiteDataAccess(_connectionString);
         IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, isVindis)).Where(x => x.IsSales && x.IsActive).OrderBy(x => x.SortOrder);
         IEnumerable<int> siteIds = sites.Select(x => x.SiteId);


         //get target by site
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<SalesPerformanceMeasure> targetsBySite;

         IEnumerable<SalesPerformanceMeasure> doneBySite = await GetSalesPerformanceDone(parms, dealerGroup, siteIds, userId);

         // If VsBudget get the Targets from FL
         if (parms.IsVsBudget)
         {
            targetsBySite = (await dealDataAccess.GetSalesPerformanceTargets(parms, userId)).Where(x => siteIds.Contains(x.SiteId));
         }
         // Otherwise get the numbers from last years Deal table
         else
         {
            SalesPerformanceParams lastYrParms = new SalesPerformanceParams(parms);

            lastYrParms.DeliveryDateStart = parms.DeliveryDateStart.AddYears(-1);
            lastYrParms.DeliveryDateEnd = parms.DeliveryDateEnd.AddYears(-1);

            targetsBySite = await GetSalesPerformanceDone(lastYrParms, dealerGroup, siteIds, userId);
         }

         List<SalesPerformanceRow> results = new List<SalesPerformanceRow>();

         MakeSiteRows(sites, targetsBySite, doneBySite, results, parms.DeliveryDateStart, isVindis);
         MakeRegionRows(sites, targetsBySite, doneBySite, results, parms.DeliveryDateStart, isVindis);
         MakeTotalRow(targetsBySite, doneBySite, results, parms.DeliveryDateStart, isVindis);

         return results;


      }

      private async Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceDone(SalesPerformanceParams parms, Model.DealerGroupName dealerGroup, IEnumerable<int> siteIds, int userId)
      {

         if (dealerGroup == Model.DealerGroupName.RRGUK || dealerGroup == Model.DealerGroupName.Carco)
         {
            DealDataAccess dealDataAccess = constructDealDataAccess();
            return (await dealDataAccess.GetSalesPerformanceDone(parms, userId)).Where(x => siteIds.Contains(x.SiteId));
         }
         else if (dealerGroup == Model.DealerGroupName.Vindis)
         {
            DealDataAccess dealDataAccess = constructDealDataAccess();
            return (await dealDataAccess.GetSalesPerformanceDoneVindis(parms, userId)).Where(x => siteIds.Contains(x.SiteId));
         }
         else if (dealerGroup == Model.DealerGroupName.RRGSpain)
         {
            DealDataAccess dealDataAccess = constructDealDataAccess();
            return (await dealDataAccess.GetSalesPerformanceDoneSpain(parms, userId)).Where(x => siteIds.Contains(x.SiteId));
         }
         else
         {
            return Enumerable.Empty<SalesPerformanceMeasure>();
         }

      }

      public async Task<IEnumerable<DailyOrdersSiteDetail>> GetDailyOrdersSiteDetails(DailyOrdersSiteDetailParams parms)
      {
         int userId = userService.GetUserId();
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDailyOrdersSiteDetails(parms, userId);
      }

      public async Task<IEnumerable<DailyOrderDetailItem>> GetDailyOrdersOrderDetails(DailyOrderDetailItemParams parms)
      {
         int userId = userService.GetUserId();
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDailyOrdersOrderDetails(parms, userId);
      }


      public async Task<TodayNewUsedOrder> GetTodayNewUsedOrders(int[] siteIds, Model.DealerGroupName dealerGroup)
      {
         IEnumerable<TodayNewUsedOrder> allSiteItems = await todayNewUsedOrdersCache.GetTodayNewUsedOrdersCacheRows(dealerGroup);

         Array.Sort(siteIds);
         int newOrders = 0;
         int usedOrders = 0;
         int fleetOrders = 0;

         foreach (var item in allSiteItems)
         {
            if (Array.BinarySearch(siteIds, item.SiteId) > -1)
            {
               newOrders += item.NewOrders;
               usedOrders += item.UsedOrders;
               fleetOrders += item.FleetOrders;
            }
         }
         return new TodayNewUsedOrder()
         {
            NewOrders = newOrders,
            UsedOrders = usedOrders,
            FleetOrders = fleetOrders
         };
      }






    

      public async Task<IEnumerable<DonutData>> GetDonutPerformanceWTD(Model.DealerGroupName dealerGroup)
      {
         DateTime startDate = StaticHelpersService.StartOfWeek(DateTime.UtcNow, DayOfWeek.Monday);
         DateTime endDate = startDate.AddDays(7).Date;

         return await CreateDonutData(startDate, endDate, dealerGroup);
      }



      public async Task<IEnumerable<DepartmentProfitPerUnits>> GetDepartmentProfitPerUnits(string timePeriod, DealerGroupName dealerGroup)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<DepartmentProfitPerUnitsDataItem> dataItems = await dealDataAccess.GetDepartmentProfitPerUnits(timePeriod, dealerGroup);



         List<DepartmentProfitPerUnits> results = new List<DepartmentProfitPerUnits>();
         foreach (var siteGrouping in dataItems.Where(x => x.DepartmentName == "New").ToLookup(x => x.SiteId))
         {
            results.Add(aggregateUpItem(siteGrouping, true));
         }

         foreach (var siteGrouping in dataItems.Where(x => x.DepartmentName == "Used").ToLookup(x => x.SiteId))
         {
            var matchingItem = results.FirstOrDefault(x => x.SiteId == siteGrouping.Key);

            if (matchingItem != null)
            {
               foreach (var item in siteGrouping)
               {
                  matchingItem.UsedChassis += item.ChassisProfit;
                  matchingItem.UsedFinance += item.FinanceProfit;
                  matchingItem.UsedAddOn += item.AddOnProfit;
                  matchingItem.UsedDealCount += item.DealCount;
               }
            }
            else
            {
               results.Add(aggregateUpItem(siteGrouping, false));
            }
         }

         return results;
      }

      private static DepartmentProfitPerUnits aggregateUpItem(IGrouping<int, DepartmentProfitPerUnitsDataItem> siteGrouping, bool isNewDeptItem)
      {
         var newItem = new DepartmentProfitPerUnits();
         newItem.SiteId = siteGrouping.Key;

         if (isNewDeptItem)
         {

            foreach (var item in siteGrouping)
            {
               newItem.NewDealCount += item.DealCount;
               newItem.NewChassis += item.ChassisProfit;
               newItem.NewFinance += item.FinanceProfit;
               newItem.NewAddOn += item.AddOnProfit;
            }
         }
         else
         {
            foreach (var item in siteGrouping)
            {
               newItem.UsedDealCount += item.DealCount;
               newItem.UsedChassis += item.ChassisProfit;
               newItem.UsedFinance += item.FinanceProfit;
               newItem.UsedAddOn += item.AddOnProfit;
            }
         }

         return newItem;
      }

      public async Task<IEnumerable<DonutData>> GetDonutPerformanceYesterday(Model.DealerGroupName dealerGroup)
      {
         DateTime yesterdayDate = DateTime.UtcNow.AddDays(-1).Date;
         return await CreateDonutData(yesterdayDate, yesterdayDate, dealerGroup);
      }

      private async Task<List<DonutData>> CreateDonutData(DateTime startDate, DateTime endDate, Model.DealerGroupName dealerGroup)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         var actuals = await dealDataAccess.GetOrdersSummaryBySite(startDate, endDate, dealerGroup);
         var targets = await financialLinesDataAccess.GetThisMonthLastMonthTargetsBySite(dealerGroup);
         List<DonutData> results = DonutService.BuildUpDonutResults(startDate, endDate, actuals, targets);

         return results;
      }

      public async Task<RunRateData> GetRunRateData(RunRateParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<RunRateChartDTO> data = !isVindis ? await dealDataAccess.GetRunRateData(parms, userId) : await dealDataAccess.GetRunRateDataVindis(parms);

         return SalesPerformanceCalculationService.CalculateRunRateData(parms, isVindis, data);
      }

      public async Task<IEnumerable<HandoverDiaryItem>> GetHandoverDiary(HandoverDiaryParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetHandoverDiary(parms, userId);
      }

      public async Task<DealModalWithStockItem> GetDealDetailModal(int dealId, int userId)
      {
         //return await dealDataAccess.GetDealDetailModal(siteId, userId);
         IEnumerable<CommentVM> comments = await GetCommentsForDeal(dealId, userId);
         DealDataAccess dealDataAccess = constructDealDataAccess();
         var dealDetailModal = await dealDataAccess.GetDealDetailModal(dealId, userId);
         //now get stock detail
         if (dealDetailModal.StockItemId != null)
         {
            var userDG = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(userDG);
            string _connectionString = configuration.GetConnectionString(dgName);
            StockDataAccess stockDataAccess = new StockDataAccess(_connectionString);
            var stockData = (await stockDataAccess.GetStockListRowItem((int)dealDetailModal.StockItemId, userDG)).First();

            return new DealModalWithStockItem()
            {
               DealDetailModal = dealDetailModal,
               StockListRow = stockData,
               Comments = comments
            };
         }
         else
         {
            return new DealModalWithStockItem()
            {
               DealDetailModal = dealDetailModal,
               Comments = comments
            };
         }

      }

      public async Task<IEnumerable<CommentVM>> GetCommentsForDeal(int dealId, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         bool byStockNumber = isVindis == true ? false : true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetCommentsForDeal(dealId, byStockNumber, userId);
      }

      public async Task<IEnumerable<SalesActivityVM>> GetSalesActivities(string vehicleType, string monthYear, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetSalesActivities(vehicleType, monthYear, userId);
      }

      public async Task<IEnumerable<SalesActivityVM>> GetSalesActivitiesForSite(string siteIds, string vehicleType, string monthYear, int? salesManagerId, int userId)
      {
         bool groupByManager = false;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         if (isVindis && !salesManagerId.HasValue) groupByManager = true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         var results = (await dealDataAccess.GetSalesActivitiesForSite(siteIds, vehicleType, monthYear, salesManagerId, groupByManager, userId)).ToList();

         if (isVindis && !salesManagerId.HasValue)
         {
            results.ForEach(r => r.Label = r.SalesManagerName);
         }
         else
         {
            results.ForEach(r => r.Label = r.SalesExecName);
         }

         results = results.OrderBy(x => x.Label).ToList();

         var totalled = new SalesActivityVM()
         {
            Label = "Total",
            Telephone = 0,
            WalkIn = 0,
            ServiceEnq = 0,
            TestDrive = 0,
            Other = 0,
            OfferSent = 0,
            Ordered = 0,
            LostOppReq = 0,
            ConfirmedOrders = 0,
            Delivered = 0,
            LostOpp = 0
         };

         foreach (var result in results)
         {
            totalled.Telephone += result.Telephone;
            totalled.WalkIn += result.WalkIn;
            totalled.ServiceEnq += result.ServiceEnq;
            totalled.TestDrive += result.TestDrive;
            totalled.Other += result.Other;
            totalled.OfferSent += result.OfferSent;
            totalled.Ordered += result.Ordered;
            totalled.LostOppReq += result.LostOppReq;
            totalled.ConfirmedOrders += result.ConfirmedOrders;
            totalled.Delivered += result.Delivered;
            totalled.LostOpp += result.LostOpp;
         }

         results.Add(totalled);

         return results;
      }





      public async Task<IEnumerable<GDPRVMRowVM>> GetGDPRs(string orderTypeIds, string monthYear, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<GDPRVM> x = await dealDataAccess.GetGDPRs(orderTypeIds, monthYear, userId);
         List<GDPRVM> vmList = x.ToList();
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var siteDataAccess = new SiteDataAccess(_connectionString);
         IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, true)).Where(x => x.IsSales && x.IsActive).OrderBy(x => x.SortOrder);
         List<GDPRVMRowVM> newList = GDPRRowCalculationService.BuildUpGDPRRows(sites, vmList);

         return newList;
      }






      public async Task<IEnumerable<GDPRVMRowVM>> GetGDPRsForSite(string siteIds, string orderTypeIds, string monthYear, int? salesManagerId, int userId)
      {
         bool groupByManager = false;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         if (isVindis && !salesManagerId.HasValue) groupByManager = true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<GDPRVM> x = await dealDataAccess.GetGDPRsForSite(siteIds, orderTypeIds, monthYear, salesManagerId, groupByManager, userId);

         List<GDPRVMRowVM> newList = GDPRRowCalculationService.BuildUpGDPRRowsForSite(salesManagerId, isVindis, x);

         return newList;
      }


      public async Task<IEnumerable<DealfileSentDate>> GetDealfileSentDate(string stockNumber, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDealfileSentDate(stockNumber, userId);
      }

      public async Task<int> UpdateDealfileSentDate(UpdateDealfileSentParams parms)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.UpdateDealfileSentDate(parms, userService.GetUserDealerGroupName());
      }

      public async Task<int> UpdateQualifyingPartEx(UpdateQualifyingPartExParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.UpdateQualifyingPartEx(parms, userId);
      }

      public async Task<int> UpdateSalesmanId(UpdateSalesmanIdParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.UpdateSalesmanId(parms, userId);
      }

      public async Task<IEnumerable<PerformanceTrendsVM>> GetPerformanceTrends(PerformanceTrendsParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();

         var results = await dealDataAccess.GetPerformanceTrends(parms, DateTime.UtcNow.AddMonths(-12), DateTime.UtcNow, userId);

         List<PerformanceTrendsVM> returnList = PerformanceTrendsService.BuildUpPerfTrendResults(parms, results);

         return returnList;
      }

      public async Task<IEnumerable<PerformanceTrendsVM>> GetPerformanceTendsForSite(PerformanceTrendsParams parms, int userId)
      {
         bool groupByManager = false;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         DealDataAccess dealDataAccess = constructDealDataAccess();

         if (isVindis && !parms.salesManagerId.HasValue)
         {
            groupByManager = true;
         }

         // We won't have ExecManagerMappings for Service data
         if (isVindis && (parms.measure == PerformanceTrendsMeasure.RedWorkId || parms.measure == PerformanceTrendsMeasure.RedWorkSold || parms.measure == PerformanceTrendsMeasure.AmberWorkId ||
                          parms.measure == PerformanceTrendsMeasure.AmberWorkSold) || parms.measure == PerformanceTrendsMeasure.CitNowSent || parms.measure == PerformanceTrendsMeasure.CitNowViewed)
         {
            groupByManager = false;
         }

         var results = await dealDataAccess.GetPerformanceTrendsForSite(parms, DateTime.UtcNow.AddMonths(-12), DateTime.UtcNow, userId, groupByManager);
         List<PerformanceTrendsVM> returnList = PerformanceTrendsService.BuildUpPerfTrendsForSite(parms, groupByManager, isVindis, results);

         return returnList;
      }

      public async Task<List<PersonAndDealCount>> GetOrdersForSalesIncentive(int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetOrdersForSalesIncentive(userId);
      }

   }
}
