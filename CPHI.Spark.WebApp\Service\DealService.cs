using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.WebApp.DataAccess;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.Model.ViewModels.Vindis;
using CPHI.Spark.Model.ViewModels.Vindis.EventHub;
using System.Text.Json;
using CPHI.Spark.BusinessLogic.Vindis.Comparers;
using System.Text.Json.Serialization;
using Dapper;


namespace CPHI.Spark.WebApp.Service
{
   public interface IDealService
   {

      Task<IEnumerable<DealDoneThisWeek>> GetDealsDoneThisWeek(DealsDoneThisWeekParams parms, int userId);
      Task<IEnumerable<DealsForTheMonthWeek>> GetDealsForTheMonth(DealsDoneForTheMonthParams parms, int userId);
      Task<IEnumerable<WeeklyDealBreakdown>> GetWeeklyDealBreakdown(DealsDoneForTheMonthWeekAnalysisParams parms, int userId);
      Task<IEnumerable<DealPopover>> GetDealPopover(int dealId, int userId);
      Task<IEnumerable<CommentVM>> GetDealComments(int dealId, int userId);
      Task<IEnumerable<WhiteboardDealsForSalesExec>> GetWhiteboard(DateTime startDate, int userId, string sites, string orderTypes, string vehicleTypes, string franchises,
          int onlyLates, int excludeLates, int? managerId);
      Task<IEnumerable<FinanceAndAddonSiteRow>> GetFinanceAddons(FinanceAddonsParams parms, int userId);
      Task<IEnumerable<FinanceAndAddonSalesExecRow>> GetFinanceAddonsForSite(FinanceAddonsForSiteParams parms, int userId);
      Task<IEnumerable<PerformanceLeagueSummaryItem>> GetPerformanceLeague(string yearMonths, string departments, bool isSalesExecView, int userId);
      Task<IEnumerable<SalesPerformanceRow>> GetSalesPerformanceNew(SalesPerformanceParams parms, int userId);
      Task<IEnumerable<SalesPerformanceOrderRateRow>> GetSalesPerformanceOrderRate(SalesPerformanceOrderRateParams parms, int userId);
      Task<IEnumerable<HandoverDiaryItem>> GetHandoverDiary(HandoverDiaryParams parms, int userId);
      Task<DateTime> GetOldestAllowableDealCache();
      Task<IEnumerable<CommentVM>> GetCommentsForDeal(int dealId, int userId);
      Task<TodayNewUsedOrder> GetTodayNewUsedOrders(int[] siteIds, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<SalesActivityVM>> GetSalesActivities(string vehicleType, string monthYear, int userId);
      Task<IEnumerable<SalesActivityVM>> GetSalesActivitiesForSite(string siteIds, string vehicleType, string monthYear, int? salesManagerId, int userId);
      Task<IEnumerable<GDPRVMRowVM>> GetGDPRs(string orderTypeIds, string monthYear, int userId);
      Task<IEnumerable<GDPRVMRowVM>> GetGDPRsForSite(string siteIds, string orderTypeIds, string monthYear, int? salesManagerId, int userId);
      Task<IEnumerable<DailyOrdersSiteDetail>> GetDailyOrdersSiteDetails(DailyOrdersSiteDetailParams parms);
      Task<IEnumerable<DailyOrderDetailItem>> GetDailyOrdersOrderDetails(DailyOrderDetailItemParams parms);
      Task<IEnumerable<DonutData>> GetDonutPerformanceWTD(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<DonutData>> GetDonutPerformanceYesterday(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<DepartmentProfitPerUnits>> GetDepartmentProfitPerUnits(string timePeriod, DealerGroupName dealerGroup);
      Task<List<SuperCupMatchRow>> GetSuperCupMatchRows();
      Task<List<SuperCupMatchRow>> GetSuperCupTwoMatchRows();
      Task<IEnumerable<DealfileSentDate>> GetDealfileSentDate(string stockNumber, int userId);
      Task<int> UpdateDealfileSentDate(UpdateDealfileSentParams parms);
      Task<RunRateData> GetRunRateData(RunRateParams parms, int userId);
      Task<IEnumerable<PerformanceTrendsVM>> GetPerformanceTrends(PerformanceTrendsParams parms, int userId);
      Task<IEnumerable<PerformanceTrendsVM>> GetPerformanceTendsForSite(PerformanceTrendsParams parms, int userId);
      Task<DealModalWithStockItem> GetDealDetailModal(int siteId, int userId);
      Task<int> UpdateQualifyingPartEx(UpdateQualifyingPartExParams parms, int userId);
      Task<int> UpdateSalesmanId(UpdateSalesmanIdParams parms, int userId);
      Task<List<PersonAndDealCount>> GetOrdersForSalesIncentive(int userId);
      Task<IEnumerable<SalesmanWithId>> GetVindisExecsForSiteForMonth(SalesmanWithIdParams parms);
      Task InsertDealQuotation(DealerGroupName dealerGroupName, string url, string quotationString);
   }

   public class DealService : SalesPerformanceCalculationService, IDealService
   {

      //private readonly IDealDataAccess dealDataAccess;
      private readonly IUserService userService;
      private readonly ITodayNewUsedOrdersCache todayNewUsedOrdersCache;
      private readonly IFinancialLinesDataAccess financialLinesDataAccess;
      private readonly IRegistrationsService registrationsService;
      private readonly IConfiguration configuration;
      private readonly IClientAppStartDataAccess clientAppStartDataAccess;
      private readonly IStandingValuesDataAccess standingValuesDataAccess;


      public DealService(
          IUserService userService,
          ITodayNewUsedOrdersCache todayNewUsedOrdersCache,
          IFinancialLinesDataAccess financialLinesDataAccess,
          IRegistrationsService registrationsService,
          IConfiguration configuration,
          IClientAppStartDataAccess clientAppStartDataAccess,
          IStandingValuesDataAccess standingValuesDataAccess)
      {
         this.userService = userService;
         this.todayNewUsedOrdersCache = todayNewUsedOrdersCache;
         this.financialLinesDataAccess = financialLinesDataAccess;
         this.registrationsService = registrationsService;
         this.configuration = configuration;
         this.clientAppStartDataAccess = clientAppStartDataAccess;
         this.standingValuesDataAccess = standingValuesDataAccess;

         //DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         //string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         //string _connectionString = configuration.GetConnectionString(dgName);
         //dealDataAccess = new DealDataAccess(_connectionString);
      }


      private DealDataAccess constructDealDataAccess(DealerGroupName? dealerGroupName = null)
      {
         DealerGroupName dealerGroup;
         if (dealerGroupName.HasValue) dealerGroup = dealerGroupName.Value; else dealerGroup = userService.GetUserDealerGroupName();

         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var dealDataAccess = new DealDataAccess(_connectionString);
         return dealDataAccess;
      }

      private ApiCallLogsDataAccess constructApiCallLogsDataAccess(DealerGroupName dealerGroup)
      {
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var dataAccess = new ApiCallLogsDataAccess(_connectionString);
         return dataAccess;
      }

      public async Task<List<SuperCupMatchRow>> GetSuperCupMatchRows()
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         IEnumerable<SuperCupRow> sourceData = await dealDataAccess.GetSuperCupRows(dealerGroup);
         var dataLookup = sourceData.ToDictionary(x => x.SiteDescription);

         List<SuperCupMatchRow> results = SuperCupCalculationService.CreateSuperCupResults(sourceData, dataLookup);

         return results;

      }

      public async Task<List<SuperCupMatchRow>> GetSuperCupTwoMatchRows()
      {
         var lmtos = await registrationsService.GetOemOrdersSiteRows(new DateTime(2022, 7, 1), "LMTOrders", false, true);
         var zoes = await registrationsService.GetOemOrdersSiteRows(new DateTime(2022, 7, 1), "LMTZoeOrders", false, true);

         List<SuperCupMatchRow> results = SuperCupCalculationService.BuildUpSuperCupTwo(lmtos, zoes);
         return results;

      }

      public async Task<IEnumerable<DealDoneThisWeek>> GetDealsDoneThisWeek(DealsDoneThisWeekParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDealsDoneThisWeek(parms, userId);
      }

      public async Task<DateTime> GetOldestAllowableDealCache()
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         return await dealDataAccess.GetOldestAllowableDealCache(dealerGroup);
      }

      public async Task<IEnumerable<DealsForTheMonthWeek>> GetDealsForTheMonth(DealsDoneForTheMonthParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         var results = await dealDataAccess.GetDealsForTheMonth(parms, userId);
         List<DealsForTheMonthWeek> resultsThisMonth = DealsForTheMonthService.BuildUpDealsForTheMonth(parms, results);
         return resultsThisMonth;
      }

      // GetWeeklyDealBreakdown
      public async Task<IEnumerable<WeeklyDealBreakdown>> GetWeeklyDealBreakdown(DealsDoneForTheMonthWeekAnalysisParams parms, int userId)
      {
         List<WeeklyDealBreakdown> result = new List<WeeklyDealBreakdown>();
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<WeeklyDealBreakdownDeal> allDealsForWeek = await dealDataAccess.GetWeeklyDealBreakdown(parms, userId);
         DealsForTheMonthService.
                     GenerateWeeklyDealBreakdown(parms, result, allDealsForWeek);

         return result;
      }

      public async Task<IEnumerable<SalesmanWithId>> GetVindisExecsForSiteForMonth(SalesmanWithIdParams parms)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetVindisExecsForSiteForMonth(parms.SiteId, parms.Year, parms.Month);
      }

      public async Task<IEnumerable<DealPopover>> GetDealPopover(int dealId, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDealPopover(dealId, userId);
      }

      public async Task<IEnumerable<CommentVM>> GetDealComments(int dealId, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         bool byStockNumber = isVindis == true ? false : true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDealComments(dealId, byStockNumber, userId);
      }



      public async Task<IEnumerable<WhiteboardDealsForSalesExec>> GetWhiteboard(DateTime startDate, int userId, string sites, string orderTypes, string vehicleTypes, string franchises,
          int onlyLates, int excludeLates, int? managerId)
      {

         List<WhiteboardDealsForSalesExec> whiteboardRows = new List<WhiteboardDealsForSalesExec>();
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         bool byStockNumber = isVindis == true ? false : true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<Whiteboard> x = await dealDataAccess.GetWhiteboard(startDate, userId, sites, orderTypes, vehicleTypes, franchises, onlyLates, excludeLates, byStockNumber, managerId);

         List<Whiteboard> SortedBySalesman = x.OrderBy(o => o.Salesman_Id).ToList();

         ILookup<int, Whiteboard> dealsBySalesmen = SortedBySalesman.ToLookup(x => x.Salesman_Id);

         if (SortedBySalesman.Count < 1) { return whiteboardRows; }

         foreach (var each in dealsBySalesmen)
         {
            int thisSalesmanId = each.Key;
            List<Whiteboard> thisSalesmanDeals = each.ToList();

            foreach (Whiteboard eachSalesman in thisSalesmanDeals)
            {
               eachSalesman.Customer = eachSalesman.Customer.Replace("Mrs ", "")
                   .Replace("Mr ", "")
                   .Replace("Ms ", "")
                   .Replace("Miss ", "");


               if (eachSalesman.Customer.Split(' ').Length > 1)
               {
                  eachSalesman.Customer = eachSalesman.Customer.Split(' ').Skip(1).FirstOrDefault();
               }

            }

            whiteboardRows.Add(MakeRow(thisSalesmanId, thisSalesmanDeals));
         }

         return whiteboardRows;
      }

      private WhiteboardDealsForSalesExec MakeRow(int thisSalesmanId, List<Whiteboard> thisSalesmanDeals)
      {
         WhiteboardDealsForSalesExec dealsForExec = new WhiteboardDealsForSalesExec();
         dealsForExec.Salesman_Id = thisSalesmanId;
         dealsForExec.SalesmanName = thisSalesmanDeals.First().Salesman;
         dealsForExec.Deals = thisSalesmanDeals;
         return dealsForExec;
      }

      public async Task<IEnumerable<FinanceAndAddonSiteRow>> GetFinanceAddons(FinanceAddonsParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<FinanceAndAddonItem> financeItems = await dealDataAccess.GetFinanceAddons(parms, userId);

         if (parms.IncludeTargets)
         {
            IEnumerable<FinanceInsuranceTargetVM> targets = await dealDataAccess.GetFinanceInsuranceTargets(parms.YearMonths, parms.IncludeNewTargets, parms.IncludeUsedTargets, userId);
            return FinanceAddOnsService.BuildUpSitesFISummary(parms, isVindis, financeItems, targets);
         }
         else
         {
            return FinanceAddOnsService.BuildUpSitesFISummary(parms, isVindis, financeItems, null);
         }
      }

      public async Task<IEnumerable<FinanceAndAddonSalesExecRow>> GetFinanceAddonsForSite(FinanceAddonsForSiteParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         bool groupByManager = false;
         if (isVindis && !parms.SalesManagerId.HasValue) groupByManager = true;
         DealDataAccess dealDataAccess = constructDealDataAccess();

         if (parms.IncludeTargets)
         {
            IEnumerable<FinanceInsuranceTargetVM> targets = await dealDataAccess.GetFinanceInsuranceTargets(parms.YearMonths, parms.IncludeNewTargets, parms.IncludeUsedTargets, userId);

            IEnumerable<FinanceAndAddonItem> results = await dealDataAccess.GetFinanceAddonsForSite(parms, userId);

            return FinanceAddOnsService.BuildUpRows(isVindis, groupByManager, results, targets);
         }
         else
         {
            IEnumerable<FinanceAndAddonItem> results = await dealDataAccess.GetFinanceAddonsForSite(parms, userId);

            return FinanceAddOnsService.BuildUpRows(isVindis, groupByManager, results, null);
         }

      }

      public async Task<IEnumerable<PerformanceLeagueSummaryItem>> GetPerformanceLeague(string yearMonths, string departments, bool isSalesExecView, int userId)
      {
         int includeFleetSites = 0;
         int? excludeGroupFleetSite = null;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         if (isVindis)
         {
            includeFleetSites = 1;
            excludeGroupFleetSite = 1;
         }

         departments = departments.Replace("VN", "New");
         departments = departments.Replace("Flotas", "Fleet");
         departments = departments.Replace("VO", "Used");
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetPerformanceLeague(yearMonths, departments, includeFleetSites, excludeGroupFleetSite, isSalesExecView, userId);
      }


      public async Task<IEnumerable<SalesPerformanceOrderRateRow>> GetSalesPerformanceOrderRate(SalesPerformanceOrderRateParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         // IEnumerable<SiteVM> sites = (await sitesDataAccess.GetSites(userId, dealerGroup,isVindis)).Where(x => x.IsSales && x.IsActive && x.RegionDescription != "Midlands").OrderBy(x => x.SortOrder);
         // IEnumerable<SalesPerformanceOrderRateMeasure> doneBySite = await dealDataAccess.GetSalesPerformanceOrderRate(parms, userId);
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var siteDataAccess = new SiteDataAccess(_connectionString);
         IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, isVindis)).Where(x => x.IsSales && x.IsActive && x.RegionDescription != "Midlands").OrderBy(x => x.SortOrder);
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<SalesPerformanceOrderRateMeasure> doneBySite = await dealDataAccess.GetSalesPerformanceOrderRate(parms, userId);

         List<SalesPerformanceOrderRateRow> results = new List<SalesPerformanceOrderRateRow>();

         MakeSiteRowsOrderRate(sites, doneBySite, results);
         MakeRegionRowsOrderRate(sites, doneBySite, results);
         MakeTotalRowOrderRate(doneBySite, results);

         return results;
      }

      public async Task<IEnumerable<SalesPerformanceRow>> GetSalesPerformanceNew(SalesPerformanceParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         bool isRRG = dealerGroup == Model.DealerGroupName.RRGUK;

         //get sites
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);

         //RRGUKConnection
         var siteDataAccess = new SiteDataAccess(_connectionString);
         IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, isVindis)).Where(x => x.IsSales && x.IsActive).OrderBy(x => x.SortOrder);
         IEnumerable<int> siteIds = sites.Select(x => x.SiteId);


         //get target by site
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<SalesPerformanceMeasure> targetsBySite;

         IEnumerable<SalesPerformanceMeasure> doneBySite = await GetSalesPerformanceDone(parms, dealerGroup, siteIds, userId);

         // If VsBudget get the Targets from FL
         if (parms.IsVsBudget)
         {
            targetsBySite = (await dealDataAccess.GetSalesPerformanceTargets(parms, userId)).Where(x => siteIds.Contains(x.SiteId));
         }
         // Otherwise get the numbers from last years Deal table
         else
         {
            SalesPerformanceParams lastYrParms = new SalesPerformanceParams(parms);

            lastYrParms.DeliveryDateStart = parms.DeliveryDateStart.AddYears(-1);
            lastYrParms.DeliveryDateEnd = parms.DeliveryDateEnd.AddYears(-1);

            targetsBySite = await GetSalesPerformanceDone(lastYrParms, dealerGroup, siteIds, userId);
         }

         List<SalesPerformanceRow> results = new List<SalesPerformanceRow>();

         MakeSiteRows(sites, targetsBySite, doneBySite, results, parms.DeliveryDateStart, isVindis);
         MakeRegionRows(sites, targetsBySite, doneBySite, results, parms.DeliveryDateStart, isVindis);
         MakeTotalRow(targetsBySite, doneBySite, results, parms.DeliveryDateStart, isVindis);

         return results;


      }

      private async Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceDone(SalesPerformanceParams parms, Model.DealerGroupName dealerGroup, IEnumerable<int> siteIds, int userId)
      {

         if (dealerGroup == Model.DealerGroupName.RRGUK || dealerGroup == Model.DealerGroupName.Carco)
         {
            DealDataAccess dealDataAccess = constructDealDataAccess();
            return (await dealDataAccess.GetSalesPerformanceDone(parms, userId)).Where(x => siteIds.Contains(x.SiteId));
         }
         else if (dealerGroup == Model.DealerGroupName.Vindis)
         {
            DealDataAccess dealDataAccess = constructDealDataAccess();
            return (await dealDataAccess.GetSalesPerformanceDoneVindis(parms, userId)).Where(x => siteIds.Contains(x.SiteId));
         }
         else if (dealerGroup == Model.DealerGroupName.RRGSpain)
         {
            DealDataAccess dealDataAccess = constructDealDataAccess();
            return (await dealDataAccess.GetSalesPerformanceDoneSpain(parms, userId)).Where(x => siteIds.Contains(x.SiteId));
         }
         else
         {
            return Enumerable.Empty<SalesPerformanceMeasure>();
         }

      }

      public async Task<IEnumerable<DailyOrdersSiteDetail>> GetDailyOrdersSiteDetails(DailyOrdersSiteDetailParams parms)
      {
         int userId = userService.GetUserId();
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDailyOrdersSiteDetails(parms, userId);
      }

      public async Task<IEnumerable<DailyOrderDetailItem>> GetDailyOrdersOrderDetails(DailyOrderDetailItemParams parms)
      {
         int userId = userService.GetUserId();
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDailyOrdersOrderDetails(parms, userId);
      }


      public async Task<TodayNewUsedOrder> GetTodayNewUsedOrders(int[] siteIds, Model.DealerGroupName dealerGroup)
      {
         IEnumerable<TodayNewUsedOrder> allSiteItems = await todayNewUsedOrdersCache.GetTodayNewUsedOrdersCacheRows(dealerGroup);

         Array.Sort(siteIds);
         int newOrders = 0;
         int usedOrders = 0;
         int fleetOrders = 0;

         foreach (var item in allSiteItems)
         {
            if (Array.BinarySearch(siteIds, item.SiteId) > -1)
            {
               newOrders += item.NewOrders;
               usedOrders += item.UsedOrders;
               fleetOrders += item.FleetOrders;
            }
         }
         return new TodayNewUsedOrder()
         {
            NewOrders = newOrders,
            UsedOrders = usedOrders,
            FleetOrders = fleetOrders
         };
      }








      public async Task<IEnumerable<DonutData>> GetDonutPerformanceWTD(Model.DealerGroupName dealerGroup)
      {
         DateTime startDate = StaticHelpersService.StartOfWeek(DateTime.UtcNow, DayOfWeek.Monday);
         DateTime endDate = startDate.AddDays(7).Date;

         return await CreateDonutData(startDate, endDate, dealerGroup);
      }



      public async Task<IEnumerable<DepartmentProfitPerUnits>> GetDepartmentProfitPerUnits(string timePeriod, DealerGroupName dealerGroup)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<DepartmentProfitPerUnitsDataItem> dataItems = await dealDataAccess.GetDepartmentProfitPerUnits(timePeriod, dealerGroup);



         List<DepartmentProfitPerUnits> results = new List<DepartmentProfitPerUnits>();
         foreach (var siteGrouping in dataItems.Where(x => x.DepartmentName == "New").ToLookup(x => x.SiteId))
         {
            results.Add(aggregateUpItem(siteGrouping, true));
         }

         foreach (var siteGrouping in dataItems.Where(x => x.DepartmentName == "Used").ToLookup(x => x.SiteId))
         {
            var matchingItem = results.FirstOrDefault(x => x.SiteId == siteGrouping.Key);

            if (matchingItem != null)
            {
               foreach (var item in siteGrouping)
               {
                  matchingItem.UsedChassis += item.ChassisProfit;
                  matchingItem.UsedFinance += item.FinanceProfit;
                  matchingItem.UsedAddOn += item.AddOnProfit;
                  matchingItem.UsedDealCount += item.DealCount;
               }
            }
            else
            {
               results.Add(aggregateUpItem(siteGrouping, false));
            }
         }

         return results;
      }

      private static DepartmentProfitPerUnits aggregateUpItem(IGrouping<int, DepartmentProfitPerUnitsDataItem> siteGrouping, bool isNewDeptItem)
      {
         var newItem = new DepartmentProfitPerUnits();
         newItem.SiteId = siteGrouping.Key;

         if (isNewDeptItem)
         {

            foreach (var item in siteGrouping)
            {
               newItem.NewDealCount += item.DealCount;
               newItem.NewChassis += item.ChassisProfit;
               newItem.NewFinance += item.FinanceProfit;
               newItem.NewAddOn += item.AddOnProfit;
            }
         }
         else
         {
            foreach (var item in siteGrouping)
            {
               newItem.UsedDealCount += item.DealCount;
               newItem.UsedChassis += item.ChassisProfit;
               newItem.UsedFinance += item.FinanceProfit;
               newItem.UsedAddOn += item.AddOnProfit;
            }
         }

         return newItem;
      }

      public async Task<IEnumerable<DonutData>> GetDonutPerformanceYesterday(Model.DealerGroupName dealerGroup)
      {
         DateTime yesterdayDate = DateTime.UtcNow.AddDays(-1).Date;
         return await CreateDonutData(yesterdayDate, yesterdayDate, dealerGroup);
      }

      private async Task<List<DonutData>> CreateDonutData(DateTime startDate, DateTime endDate, Model.DealerGroupName dealerGroup)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         var actuals = await dealDataAccess.GetOrdersSummaryBySite(startDate, endDate, dealerGroup);
         var targets = await financialLinesDataAccess.GetThisMonthLastMonthTargetsBySite(dealerGroup);
         List<DonutData> results = DonutService.BuildUpDonutResults(startDate, endDate, actuals, targets);

         return results;
      }

      public async Task<RunRateData> GetRunRateData(RunRateParams parms, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<RunRateChartDTO> data = !isVindis ? await dealDataAccess.GetRunRateData(parms, userId) : await dealDataAccess.GetRunRateDataVindis(parms);

         return SalesPerformanceCalculationService.CalculateRunRateData(parms, isVindis, data);
      }

      public async Task<IEnumerable<HandoverDiaryItem>> GetHandoverDiary(HandoverDiaryParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetHandoverDiary(parms, userId);
      }

      public async Task<DealModalWithStockItem> GetDealDetailModal(int dealId, int userId)
      {
         //return await dealDataAccess.GetDealDetailModal(siteId, userId);
         IEnumerable<CommentVM> comments = await GetCommentsForDeal(dealId, userId);
         DealDataAccess dealDataAccess = constructDealDataAccess();
         var dealDetailModal = await dealDataAccess.GetDealDetailModal(dealId, userId);
         //now get stock detail
         if (dealDetailModal.StockItemId != null)
         {
            var userDG = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(userDG);
            string _connectionString = configuration.GetConnectionString(dgName);
            StockDataAccess stockDataAccess = new StockDataAccess(_connectionString);
            var stockData = (await stockDataAccess.GetStockListRowItem((int)dealDetailModal.StockItemId, userDG)).First();

            return new DealModalWithStockItem()
            {
               DealDetailModal = dealDetailModal,
               StockListRow = stockData,
               Comments = comments
            };
         }
         else
         {
            return new DealModalWithStockItem()
            {
               DealDetailModal = dealDetailModal,
               Comments = comments
            };
         }

      }

      public async Task<IEnumerable<CommentVM>> GetCommentsForDeal(int dealId, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         bool byStockNumber = isVindis == true ? false : true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetCommentsForDeal(dealId, byStockNumber, userId);
      }

      public async Task<IEnumerable<SalesActivityVM>> GetSalesActivities(string vehicleType, string monthYear, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetSalesActivities(vehicleType, monthYear, userId);
      }

      public async Task<IEnumerable<SalesActivityVM>> GetSalesActivitiesForSite(string siteIds, string vehicleType, string monthYear, int? salesManagerId, int userId)
      {
         bool groupByManager = false;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         if (isVindis && !salesManagerId.HasValue) groupByManager = true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         var results = (await dealDataAccess.GetSalesActivitiesForSite(siteIds, vehicleType, monthYear, salesManagerId, groupByManager, userId)).ToList();

         if (isVindis && !salesManagerId.HasValue)
         {
            results.ForEach(r => r.Label = r.SalesManagerName);
         }
         else
         {
            results.ForEach(r => r.Label = r.SalesExecName);
         }

         results = results.OrderBy(x => x.Label).ToList();

         var totalled = new SalesActivityVM()
         {
            Label = "Total",
            Telephone = 0,
            WalkIn = 0,
            ServiceEnq = 0,
            TestDrive = 0,
            Other = 0,
            OfferSent = 0,
            Ordered = 0,
            LostOppReq = 0,
            ConfirmedOrders = 0,
            Delivered = 0,
            LostOpp = 0
         };

         foreach (var result in results)
         {
            totalled.Telephone += result.Telephone;
            totalled.WalkIn += result.WalkIn;
            totalled.ServiceEnq += result.ServiceEnq;
            totalled.TestDrive += result.TestDrive;
            totalled.Other += result.Other;
            totalled.OfferSent += result.OfferSent;
            totalled.Ordered += result.Ordered;
            totalled.LostOppReq += result.LostOppReq;
            totalled.ConfirmedOrders += result.ConfirmedOrders;
            totalled.Delivered += result.Delivered;
            totalled.LostOpp += result.LostOpp;
         }

         results.Add(totalled);

         return results;
      }





      public async Task<IEnumerable<GDPRVMRowVM>> GetGDPRs(string orderTypeIds, string monthYear, int userId)
      {
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<GDPRVM> x = await dealDataAccess.GetGDPRs(orderTypeIds, monthYear, userId);
         List<GDPRVM> vmList = x.ToList();
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var siteDataAccess = new SiteDataAccess(_connectionString);
         IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, true)).Where(x => x.IsSales && x.IsActive).OrderBy(x => x.SortOrder);
         List<GDPRVMRowVM> newList = GDPRRowCalculationService.BuildUpGDPRRows(sites, vmList);

         return newList;
      }






      public async Task<IEnumerable<GDPRVMRowVM>> GetGDPRsForSite(string siteIds, string orderTypeIds, string monthYear, int? salesManagerId, int userId)
      {
         bool groupByManager = false;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         if (isVindis && !salesManagerId.HasValue) groupByManager = true;
         DealDataAccess dealDataAccess = constructDealDataAccess();
         IEnumerable<GDPRVM> x = await dealDataAccess.GetGDPRsForSite(siteIds, orderTypeIds, monthYear, salesManagerId, groupByManager, userId);

         List<GDPRVMRowVM> newList = GDPRRowCalculationService.BuildUpGDPRRowsForSite(salesManagerId, isVindis, x);

         return newList;
      }


      public async Task<IEnumerable<DealfileSentDate>> GetDealfileSentDate(string stockNumber, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetDealfileSentDate(stockNumber, userId);
      }

      public async Task<int> UpdateDealfileSentDate(UpdateDealfileSentParams parms)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.UpdateDealfileSentDate(parms, userService.GetUserDealerGroupName());
      }

      public async Task<int> UpdateQualifyingPartEx(UpdateQualifyingPartExParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.UpdateQualifyingPartEx(parms, userId);
      }

      public async Task<int> UpdateSalesmanId(UpdateSalesmanIdParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.UpdateSalesmanId(parms, userId);
      }

      public async Task<IEnumerable<PerformanceTrendsVM>> GetPerformanceTrends(PerformanceTrendsParams parms, int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();

         var results = await dealDataAccess.GetPerformanceTrends(parms, DateTime.UtcNow.AddMonths(-12), DateTime.UtcNow, userId);

         List<PerformanceTrendsVM> returnList = PerformanceTrendsService.BuildUpPerfTrendResults(parms, results);

         return returnList;
      }

      public async Task<IEnumerable<PerformanceTrendsVM>> GetPerformanceTendsForSite(PerformanceTrendsParams parms, int userId)
      {
         bool groupByManager = false;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;

         DealDataAccess dealDataAccess = constructDealDataAccess();

         if (isVindis && !parms.salesManagerId.HasValue)
         {
            groupByManager = true;
         }

         // We won't have ExecManagerMappings for Service data
         if (isVindis && (parms.measure == PerformanceTrendsMeasure.RedWorkId || parms.measure == PerformanceTrendsMeasure.RedWorkSold || parms.measure == PerformanceTrendsMeasure.AmberWorkId ||
                          parms.measure == PerformanceTrendsMeasure.AmberWorkSold) || parms.measure == PerformanceTrendsMeasure.CitNowSent || parms.measure == PerformanceTrendsMeasure.CitNowViewed)
         {
            groupByManager = false;
         }

         var results = await dealDataAccess.GetPerformanceTrendsForSite(parms, DateTime.UtcNow.AddMonths(-12), DateTime.UtcNow, userId, groupByManager);
         List<PerformanceTrendsVM> returnList = PerformanceTrendsService.BuildUpPerfTrendsForSite(parms, groupByManager, isVindis, results);

         return returnList;
      }

      public async Task<List<PersonAndDealCount>> GetOrdersForSalesIncentive(int userId)
      {
         DealDataAccess dealDataAccess = constructDealDataAccess();
         return await dealDataAccess.GetOrdersForSalesIncentive(userId);
      }



      public async Task InsertDealQuotation(DealerGroupName dealerGroupName, string url, string quotationString)
      {
         //Store calls in DB
         var apiCallLogsDataAccess = constructApiCallLogsDataAccess(dealerGroupName);
         var newApiCallLog = await apiCallLogsDataAccess.AddDeals(dealerGroupName, url, quotationString);

         await ProcessIncomingDealQuotation(apiCallLogsDataAccess, quotationString, newApiCallLog, dealerGroupName);


         //Update the status of the call
         newApiCallLog.Status = "Processed";
         await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
      }

      private async Task ProcessIncomingDealQuotation(ApiCallLogsDataAccess apiCallLogsDataAccess, string quotationString, ApiCallLog newApiCallLog, DealerGroupName dealerGroupName)
      {
         //----------------------------------Deserialize
         APIPostBody aPIPostBody = new APIPostBody();
         QuotationEventBody q = new QuotationEventBody();

         try
         {
            var options = new JsonSerializerOptions
            {
               Converters = { new JsonStringEnumConverter() }
            };

            aPIPostBody = JsonSerializer.Deserialize<APIPostBody>(quotationString, options);
            q = aPIPostBody.Body;

         }
         catch (Exception err)
         {
            newApiCallLog.ErrorNotes = $"Failed Deserializing ApiCall {err.Message} | {err.StackTrace}";
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception($"Failed Deserializing ApiCall: {err.Message}");
            //Send email to cphi support?
         }

         //Validate if Mandatory properties are present
         if (string.IsNullOrEmpty(q.DealerName))
         {
            string errMessage = $"Failed: DealerName property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }
         else if (SiteConverter(q.DealerName) == 0)
         {
            string errMessage = $"Failed: DealerName {q.DealerName} is not linked to a Site in DB";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }


         if (string.IsNullOrEmpty(q.SaleType))
         {
            string errMessage = $"Failed: SaleType property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }

         //////////////////////////////////////////////////////////////
         if (q.Vehicles == null || q.Vehicles.Any() == false)
         {
            string errMessage = $"Failed: Vehiles property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }
         else if (q.Vehicles[0].VehicleType == null)
         {
            string errMessage = $"Failed: Vehicles.VehicleType property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }
         else if (string.IsNullOrEmpty(q.Vehicles[0].Id))
         {
            string errMessage = $"Failed: Vehicles.Id property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }

         //Make
         if (string.IsNullOrEmpty(q.Vehicles[0].Make))
         {
            string errMessage = $"Failed: Vehicles.Make property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }
         else
         {
            var standingValuesInDB = await standingValuesDataAccess.GetStandingValuesForFranchise(dealerGroupName);
            var make = standingValuesInDB.FirstOrDefault(x => string.Equals(x.Description, q.Vehicles[0].Make, StringComparison.OrdinalIgnoreCase));
            if (make == null)
            {

               string errMessage = $"Failed: Vehicles.Make {q.Vehicles[0].Make} not found in DB";
               newApiCallLog.ErrorNotes = errMessage;
               newApiCallLog.Status = "Failed";
               await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
               throw new Exception(errMessage);

            }
         }

         //Vehicle desc length check, max 150 chars
         if (q.Vehicles[0].VehicleDescription.Length > 150)
         {
            string errMessage = $"Failed: VehicleDescription length is more than 150 chars";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }

         //TODO: Madatory Dates to Check: AccountingDate, OrderDate, WhenNew
         /*
        if (q.AccountingDate.HasValue == false)
        {
            string errMessage = $"Failed: AccountingDate property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
        }
        if (q.OrderDate.HasValue == false)
        {
            string errMessage = $"Failed: OrderDate property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
        }
        if (q.WhenNew.HasValue == false)
        {
            string errMessage = $"Failed: OrderDate property is blank";
            newApiCallLog.ErrorNotes = errMessage;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
        }
        */




         /*
         else if (string.IsNullOrEmpty(q.Vehicles[0].DmsStockNumber))
         {
             string errMessage = $"Failed: Vehicles.DmsStockNumber property is blank";
             newApiCallLog.ErrorNotes = errMessage;
             newApiCallLog.Status = "Failed";
             await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
             throw new Exception(errMessage);
         }
         else if (q.Vehicles[0].StockDate.HasValue == false)
         {
             string errMessage = $"Failed: Vehicles.StockDate property is blank";
             newApiCallLog.ErrorNotes = errMessage;
             newApiCallLog.Status = "Failed";
             await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
             throw new Exception(errMessage);
         }

         */



         //---------------------------------New Person -----------------------
         string name = q.AssignedToName;
         string siteName = q.DealerName;
         int siteId = SiteConverter(siteName);

         //check the DB if user exists
         var personInDB = await userService.GetPersonByName(name, dealerGroupName);

         if (personInDB == null)
         {
            Person person = new Person();
            person.Id = 0;
            person.Name = name;
            person.IsSalesExec = true;
            person.CurrentSite_Id = siteId;
            person.SellsUsed = true;
            person.SellsNew = true;
            person.LastUpdateDate = DateTime.UtcNow;
            person.JobTitle = "Sales Exec";
            person.JobRole = "Sales Exec";
            person.IsRemoved = false;
            person.IsUpdated = false;
            person.HasLeft = false;
            person.HasSales = true;
            person.Sites = siteId.ToString();
            person.DoNotRemove = false;
            person.AccessAllSites = false;
            person.Email = "ADDED BY DEALS LOADER";
            person.KnownAs = name;
            person.DealerGroup_Id = 3;
            person.CurrentRetailerSite_Id = siteId;

            await userService.CreatePerson(person, dealerGroupName);
            personInDB = person;
         }

         var dealDataAccess = constructDealDataAccess(dealerGroupName);

         //------------------- incoming Deal
         var incomingDeal = new Deal();
         incomingDeal.InvoiceDate = DateTime.UtcNow.Date; //TODO: check this is correct
         incomingDeal.AccountingDate = DateTime.UtcNow.Date; //TODO: check this is correct
         incomingDeal.OrderDate = DateTime.UtcNow.Date; //TODO: check this is correct
         incomingDeal.WhenNew = DateTime.UtcNow.Date; //TODO: check this is correct
         incomingDeal.VehicleClass_Id = 207; //Car //TODO: check this is correct

         try
         {
            await setIncomingDealProperties(q, dealDataAccess, incomingDeal, personInDB.Id, dealerGroupName);
         }
         catch (Exception ex)
         {
            string errMessage = $"Failed to create Deal object";
            newApiCallLog.ErrorNotes = errMessage + " | " + ex.StackTrace;
            newApiCallLog.Status = "Failed";
            await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
            throw new Exception(errMessage);
         }


         //-------------------existing Deal
         var existingDeal = await dealDataAccess.GetDealByEnquiryNumber(incomingDeal.EnquiryNumber);

         //Insert, Update or Delete Deal and generate Diff
         if (existingDeal == null) //Insert
         {
            try
            {
               await dealDataAccess.InsertNewDeal(incomingDeal);
            }
            catch (Exception err)
            {
               string errMessage = $"Failed inserting new Deal in DB";
               newApiCallLog.ErrorNotes = $"{err.Message} | {err.StackTrace}";
               newApiCallLog.Status = "Failed";
               await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
               throw new Exception(errMessage);
               //Send email to cphi support
               //return false;
            }
         }
         /*
         else if (incomingDeal.IsRemoved) //Delete
         {
             try
             {
                 existingDeal.IsRemoved = true;
                 existingDeal.RemovedDate = DateTime.UtcNow;
                 existingDeal.LastUpdated = DateTime.UtcNow;

                 //generate a diff also
                 Diff newDiff = new Diff()
                 {
                     Model = "Deal",
                     ModelIdent = existingDeal.EnquiryNumber.ToString(),
                     Key = "IsRemoved",
                     OldValue = "False",
                     NewValue = "True",
                     UpdateDate = DateTime.UtcNow,
                 };
             }

             catch (Exception err)
             {
                 newApiCallLog.ErrorNotes = $"Failed removing items {err.Message} | {err.StackTrace}";
                 newApiCallLog.Status = "Failed";
                 await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
                 //Send email to cphi support
                 //return false;
             }


         }
         else if (!incomingDeal.IsDelivered) //Un-Delivered
         {
             try
             {
                 existingDeal.IsDelivered = false;
                 existingDeal.LastUpdated = DateTime.UtcNow;

                 //generate a diff also
                 Diff newDiff = new Diff()
                 {
                     Model = "Deal",
                     ModelIdent = incomingDeal.EnquiryNumber.ToString(),
                     Key = "IsDelivered",
                     OldValue = "True",
                     NewValue = "False",
                     UpdateDate = DateTime.UtcNow,
                 };
             }

             catch (Exception err)
             {
                 newApiCallLog.ErrorNotes = $"Failed Un-Delivered items {err.Message} | {err.StackTrace}";
                 newApiCallLog.Status = "Failed";
                 await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
                 //Send email to cphi support
                 //return false;
             }


         }
         */
         else //Update
         {
            List<Diff> changedDiffs = DiffAndUpdate(incomingDeal, existingDeal);
            //add diffs to db
            try
            {
               foreach (Diff diff in changedDiffs)
               {
                  diff.UpdateDate = DateTime.UtcNow;
               }
               List<Diff> diffsToPersist = changedDiffs.ToList();
               await dealDataAccess.AddDiffs(diffsToPersist); //add them all in one go
               await dealDataAccess.UpdateDeal(existingDeal);
            }
            catch (Exception err)
            {
               newApiCallLog.ErrorNotes = $"Failed adding diffs to DB {err.Message} | {err.StackTrace}";
               newApiCallLog.Status = "Failed";
               await apiCallLogsDataAccess.UpdateApiCallLog(dealerGroupName, newApiCallLog);
               //Send email to cphi support
               //return false;

            }

         }



         //---------------------------------------------------------------------------------------------------
         //6. update globalParams with last updatedDate
         DateTime updateDate = DateTime.UtcNow;

         /* 
         try
         {
             //GlobalParam ordersUpdateDate = dbGlobalParams.FirstOrDefault(x => x.Description == "ordersUpdateDate");
             //ordersUpdateDate.DateFrom = fileDate;
             //updateDate = fileDate;

             //also record the last time we saved this type
             string ordersType = fileName.Substring(16, fileName.Length - 21).Replace("_DAILY", "");
             GlobalParam orderTypeUpdateDate = dbGlobalParams.FirstOrDefault(x => x.Description == $"{ordersType}UpdateDate");
             orderTypeUpdateDate.DateFrom = fileDate;
         }
         catch (Exception err)
         {
             logMessage.FailNotes = logMessage.FailNotes + $"Failed to update globalParam updateDate {err.ToString()}";
             errorCount++;
         }

         */


         //Finally run the DealMerge
         DynamicParameters paramList = new DynamicParameters();
         paramList.Add("AccountingDateStart", DateTime.UtcNow.Date.AddMonths(-1));  //TODO: check the dates
         paramList.Add("AccountingDateEnd", DateTime.UtcNow.Date.AddYears(100));
         paramList.Add("DealerGroupId", (int)dealerGroupName);

         //int include = shouldIncludeDuplicateStockNumbers();
         paramList.Add("IncludeDuplicates", 1);
         await dealDataAccess.MERGE_DealLatests(paramList);





      }

      private int SiteConverter(string siteName)
      {
         //create a sites dictionary
         Dictionary<string, int> siteLookup = new Dictionary<string, int>()
            {
                //From Email
                {"Test Site", 23},                      // {"Vindis Test Training", 23}, 
                {"Three 10 Automotive", 16},            // {"Three 10 Automotive", 16},
                {"Huntingdon Audi", 3},                 // {"Vindis Audi Huntingdon", 3},
                {"Northampton Audi", 4},                // {"Vindis Audi Northampton", 4},
                {"Cambridge Audi", 2},                  // {"Vindis Audi Cambridge", 2},
                {"Bedford Audi", 1},                    // {"Vindis Audi Bedford", 1}," 
                {"Peterborough Audi", 5},               // {"Vindis Audi Peterborough", 5},
                {"Ducati Cambridge", 15},               // {"Vindis Ducati Cambridge", 15},
                {"Ducati Peterborough", 15},            // {"Vindis Ducati Peterborough", 15},
                {"SKODA Bury St. Edmunds", 12},         // {"Vindis Skoda Bury St. Edmunds", 12},
                {"SKODA Cambridge", 13},                // {"Vindis Skoda Cambridge", 13},
                {"Vindis Van Centre Huntingdon", 10},   //{"Vindis Van Centre Huntingdon", 10},
                {"Vindis Van Centre Northampton",11 },  //{"Vindis Van Centre Northampton", 11},
                {"Bedford Volkswagen", 6 },             //{"Vindis VW Bedford", 6},
                {"Huntingdon Volkswagen", 9 },          //{"Vindis VW Huntingdon", 9},
                {"Cookes of Fakenham", 8 },             //{"Cookes Of Fakenham", 8},
                {"Cambridge Volkswagen", 7 },           //{"Vindis VW Cambridge", 7},
                {"Milton Keynes SEAT", 14 },            //{"Vindis SEAT Milton Keynes", 14},
                {"Autonow Sawston", 20 },               //{"Vindis AutoNow Sawston", 20},
                {"Autonow Fakenham", 19 },              //{"Vindis AutoNow Fakenham", 19},
                {"Autonow Cambridge", 18 },             //{"Vindis AutoNow Cambridge (read only)", 18},
                {"Autonow Bury St Edmunds", 17 },       //{"Vindis AutoNow Bury St. Edmunds", 17},

            };

         if (siteLookup.ContainsKey(siteName))
         {
            return siteLookup[siteName];
         }

         return 0;

      }


      public async Task setIncomingDealProperties(QuotationEventBody q, DealDataAccess dealDataAccess, Deal d, int salesmanId, DealerGroupName dealerGroupName)
      {

         /*
         //create a franchises dictionary
         Dictionary<string, string> franchiseLookup = new Dictionary<string, string>()
                 {
                     {"Audi", "Audi"},
                     {"Volkswagen", "VW"},
                     {"BENTLEY", "Bentley"},
                     {"DUCATI", "Ducati"},
                     {"SEAT", "SEAT"},
                     {"Skoda", "Skoda"},
                 };

         //create a profit calc field dictionary
         Dictionary<string, string> profitCalcLookup = new Dictionary<string, string>()
                 {
                     { "VEHICLE WITHOUT VAT" , "Turnover" },
                     { "FACTORY OPTIONS" , "Options" },
                     { "SIV" , "SIV" },
                     { "RECONDITION COST" , "MechPrep" },
                     { "VAT (USED)" , "VAT" },
                     { "WARRANTY COST" , "WarrantyCost" },
                     { "FRONT END MOTAB" , "Front End" },
                     { "LOYALTY MY AUDI MOTAB" , "Bonus" },
                     { "MOTAB BONUS BOX " , "Bonus" },
                     { "MOTAB DELIVERY MARGIN" , "Delivery" },
                     { "MOTAB HANDLING FEE" , "Handling Fee" },
                     { "MOTAB PDI" , "PDI" },
                     { "QUALITY A8 MOTAB" , "Bonus" },
                     { "QUALITY SUSTAINABILITY MOTAB" , "Bonus" },
                     { "INVESTOR FUND" , "Bonus" },
                     { "PDI" , "PDI" },
                     { "DELIVERY MARGIN" , "Delivery" },
                     { "FRONT END" , "Front End" },
                     { "LOYALTY - MY AUDI" , "Bonus" },
                     { "NEW CAR REG BONUS" , "Front End" },
                     { "QUALITY A8" , "Bonus" },
                     { "QUALITY SUSTAINABILITY" , "Bonus" },
                     { "QUALITY TRANSFORM" , "Bonus" },
                     { "QUALITY TRANSFORM MOTAB" , "Bonus" },
                     { "RETAIL BONUS BOX" , "Front End" },
                     { "VOLUME - NETWORK COUNTING" , "Bonus" },
                     { "FLEET BASE - MONTHLY" , "Bonus" },
                     { "FLEET BONUS BOX" , "Bonus" },
                     { "FLEET DELIVERY MARGIN" , "Delivery" },
                     { "FLEET MONTHLY CONSISTENCY" , "Bonus" },
                     { "FLEET PDI " , "PDI" },
                     { "FLEET QUALITY - QUARTERLY" , "Bonus" },
                     { "BONUS 2" , "Front End" },
                     { "BONUS 3" , "Front End" },
                     { "BONUS 1" , "Front End" },
                     { "DEL PROFIT" , "Delivery" },
                     { "MONTHLY" , "Bonus" },
                     { "ON CONSIGNMENT" , "Front End" },
                     { "QUARTERLY" , "Bonus" },
                     { "FLEET BONUS 1" , "Bonus" },
                     { "MOTAB BONUS 1" , "Bonus" },
                     { "MOTAB BONUS" , "Bonus" },
                     { "MOTAB DEL PROFIT" , "Delivery" },
                     { "MOTAB MONTHLY" , "Bonus" },
                     { "MOTABILITY MARGIN" , "Handling Fee" },
                     { "FLEET FLAT RATE BACK END" , "Bonus" },
                     { "PDI COST" , "PDI" },
                     { "EXCLUSIVITY BONUS" , "Bonus" },
                     { "MOBILITY BASE" , "Handling Fee" },
                     { "BASE" , "Front End" },
                     { "EV DEVELOPMENT" , "Bonus" },
                     { "RENEWALS LOYALTY BONUS" , "Bonus" },
                     { "MOTABILITY BASE" , "Front End" },
                     { "FLEET PDI" , "PDI" },
                     { "CONSIGNMENT" , "Front End" },
                     { "FLEET CONSIGNMENT" , "Front End" },
                     { "LEAD MARGIN" , "Bonus" },
                     { "VRB 100%" , "Bonus" },
                     { "MOTAB BONUS PAYMENT" , "Bonus" },
                     { "SEAT LOYALTY BONUS" , "Front End" },
                     { "CONSISTENCY BONUS" , "Bonus" },
                     { "CRAFTER BONUS" , "Bonus" },
                     { "FRONT END SALES VOLUME ACHIEVEMENT" , "Front End" },
                     { "MOTAB BONUS BOX" , "Bonus" },
                     { "MOTAB DELIVERY PROFIT" , "Delivery" },
                     { "ACCESSORIES" , "Accessories" },
                     { "RETAILER" , "Other" },
                     { "GAP" , "GAP" },
                     { "WARRANTY" , "Warranty" },
                     { "TYRE & ALLOY" , "Tyre & Alloy" },
                     { "PAINT & FABRIC" , "Paint & Fabric" },
                     { "ALLOY" , "Alloy" },
                     { "COSMETIC REPAIR" , "Cosmetic" },
                     { "FACTORY" , "Front End" },
                     { "SERVICE PLAN" , "Service Plan" },
                     { "TYRE AND ALLOY WHEEL 36 £300 LIMIT" , "Tyre & Alloy" },
                     { "TYRE COVER" , "Tyre" },
                     { "INCENTIVES" , "Incentives" },
                     { "OVER ALLOWANCE" , "Over-Allowance" },
                     { "FINANCE INCOME" , "Finance Income" },
                     { "INVESTOR FUND MOTAB" , "Bonus" },
                     { "VOLUME NETWORK COUNTING " , "Bonus" },
                     { "ALLOY WHEEL INSURANCE 36MTH £150 LIMIT" , "Alloy" },
                     { "HALF YEARLY" , "Bonus" },
                     { "FLEET DEL PROFIT" , "Delivery" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £10K-£20K £15K LIMIT" , "GAP" },
                     { "C.A.R.S. PLUS 36" , "Cosmetic" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £30K-£50K £25K LIMIT" , "GAP" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £20K-£30K £25K LIMIT" , "GAP" },
                     { "LEASE ASSET PROTECTION INDEMNIFIED £25K-£50K" , "GAP" },
                     { "ALLOY WHEEL INSURANCE 12" , "Alloy" },
                     { "ALLOY WHEEL INSURANCE 36" , "Alloy" },
                     { "LEASE ASSET PROTECTION INDEMNIFIED £25K-£50K £10K LIMIT" , "GAP" },
                     { "VPS ULTRA £20K-£50K" , "Paint & Fabric" },
                     { "VOLUME NETWORK COUNTING" , "Bonus" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £20K-£30K" , "GAP" },
                     { "TYRE AND ALLOY WHEEL 36" , "Tyre & Alloy" },
                     { "LCV RETAIL PRICE PROTECTION INDEMNIFIED £50K-£75K £25K LIMIT" , "GAP" },
                     { "CERAMIC PAINT PROTECTION" , "Paint & Fabric" },
                     { "MOTOR CX2 (VINDIS DUCATI)" , "Paint & Fabric" },
                     { "CONTRACT HIRE GAP" , "GAP" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £50K-£100K" , "GAP" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £50K-£100K £50K LIMIT" , "GAP" },
                     { "AGENCY" , "Handling Fee" },
                     { "VPS ULTRA + LEATHER GUARD £20K-£50K" , "Paint & Fabric" },
                     { "0% SUBSIDY" , "Front End" },
                     { "ALLOY WHEEL INSURANCE 12MTH £150 LIMIT" , "Alloy" },
                     { "ALLOY WHEEL INSURANCE 24" , "Alloy" },
                     { "ALLOY WHEEL INSURANCE 24 £150 LIMIT" , "Alloy" },
                     { "ALLOY WHEEL INSURANCE 36MTH" , "Alloy" },
                     { "BENTLEY CERAMIC PROTECTION" , "Paint & Fabric" },
                     { "C.A.R.S. PLUS 12" , "Cosmetic" },
                     { "C.A.R.S. PLUS 24" , "Cosmetic" },
                     { "CASH ALTERNATIVE OFFER" , "Bonus" },
                     { "CERAMIC PAINT PROTECTION + LEATHER GUARD" , "Paint & Fabric" },
                     { "CUPRA CONSIGNMENT" , "Front End" },
                     { "CUPRA FUND" , "Bonus" },
                     { "CUPRA VRB" , "Bonus" },
                     { "DEALER DISCOUNT" , "Front End" },
                     { "FCM INCOME" , "Service Plan Commission" },
                     { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £100K-£150K" , "GAP" },
                     { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £100K-£150K £50K LIMIT" , "GAP" },
                     { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £50K-£100K" , "GAP" },
                     { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £50K-£100K £50K LIMIT" , "GAP" },
                     { "FIXED PRICE SERVICE PLAN" , "Service Plan" },
                     { "FLEET GUARANTEED MARGIN" , "Handling Fee" },
                     { "FLEET INVESTOR FUND" , "Bonus" },
                     { "FLEET VRB 100%" , "Bonus" },
                     { "GUARANTEED MARGIN" , "Front End" },
                     { "LCV RETAIL PRICE PROTECTION INDEMNIFIED £30K-£50K MAX £25K" , "GAP" },
                     { "LEASE ASSET PROTECTION 4TH & 5TH YEAR £50K-£100K £50K LIMIT" , "GAP" },
                     { "LEASE ASSET PROTECTION INDEMNIFIED £0-£25K" , "GAP" },
                     { "LEASE ASSET PROTECTION INDEMNIFIED £0-£6K £6K LIMIT" , "GAP" },
                     { "LEASE ASSET PROTECTION INDEMNIFIED £50K-£100K" , "GAP" },
                     { "LEASE ASSET PROTECTION INDEMNIFIED £50K-£100K £50K LIMIT" , "GAP" },
                     { "LEASE ASSET PROTECTION INDEMNIFIED £6-£25K" , "GAP" },
                     { "LEASE ASSET PROTECTION INDEMNIFIED £6-£25K £10K LIMIT" , "GAP" },
                     { "Q6 BEV LEAD OPTIMISATION", "Bonus" },
                     { "Q6 ORDER BANK CONVERSION", "Bonus" },
                     { "Q6 MARGIN ADJUSTMENT", "Bonus" },
                     { "LEVEL 1 TAB" , "Bonus" },
                     { "LEVEL 2 TAB" , "Bonus" },
                     { "MARKETING PLAN ACTIVATION & AOI" , "Bonus" },
                     { "MOTORCYCLE RETAIL PRICE PROTECTION £10K-£15K £5K LIMIT" , "GAP" },
                     { "MOTORCYCLE RETAIL PRICE PROTECTION £15K-£20K £7.5K LIMIT" , "GAP" },
                     { "MOTORCYCLE RETAIL PRICE PROTECTION £5K-£10K £5K LIMIT" , "GAP" },
                     { "NEW REG BONUS" , "Front End" },
                     { "ONE CUSTOMER" , "Bonus" },
                     { "OPTION UPTAKE" , "Front End" },
                     { "ORDER BANK COVERAGE" , "Bonus" },
                     { "ORDER ENTRY SUBMISSION" , "Bonus" },
                     { "PAINT PROTECTION" , "Paint & Fabric" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £100K-£150K" , "GAP" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £100K-£150K £50K LIMIT" , "GAP" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £10K-£20K" , "GAP" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £150K-£200K £50K LIMIT" , "GAP" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £30K-£50K" , "GAP" },
                     { "SEAT TEST DRIVE INCOME" , "Front End" },
                     { "TAXI ASSET PROTECTION INDEMNIFIED £25K-£50K" , "GAP" },
                     { "TYRE AND ALLOY WHEEL - RUN FLAT 36 (£500 CLAIM LIMIT)" , "Tyre & Alloy" },
                     { "TYRE AND ALLOY WHEEL 12" , "Tyre & Alloy" },
                     { "TYRE AND ALLOY WHEEL 24" , "Tyre & Alloy" },
                     { "TYRE AND ALLOY WHEEL 24 £300 LIMIT" , "Tyre & Alloy" },
                     { "TYRE INSURANCE 12" , "Tyre" },
                     { "TYRE INSURANCE 24" , "Tyre" },
                     { "TYRE INSURANCE 36" , "Tyre" },
                     { "TYRE INSURANCE 36 £300" , "Tyre" },
                     { "VPS ULTRA £10K-£20K" , "Paint & Fabric" },
                     { "VPS ULTRA + LEATHER GUARD £10K-£20K" , "Paint & Fabric" },
                     { "WHEELGARD BLACK" , "WheelGard" },
                     { "WHEELGARD BLACK (WITH VPS OR CERAMIC)" , "WheelGard" },
                     { "WHEELGARD RED" , "WheelGard" },
                     { "WHEELGARD SILVER" , "WheelGard" },
                     { "WHEELGARD SILVER (WITH VPS OR CERAMIC)" , "WheelGard" },
                     { "PAINT REPAIR COSTS" , "BodyPrep" },
                     { "ADMINISTRATION FEE" , "Other" },
                     { "ADMIN" , "Other" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £6K-£10K £10K LIMIT" , "GAP" },
                     { "VPS ULTRA £0-£10K" , "Paint & Fabric" },
                     { "RETAIL PRICE PROTECTION INDEMNIFIED £0-£6K £6K LIMIT" , "GAP" },
                     { "SEAT WEB VOUCHER" , "Front End" },
                     { "SEAT WEB VOUCHER COST" , "Front End" },
                     { "SEAT VOUCHER COSTS" , "Front End" },
                     { "TEST DRIVE VOUCHER COST" , "Front End" },
                     { "PCP DEDUCTION" , "Bonus" },
                     { "SOFT PREP REPAIR COSTS" , "BodyPrep" },
                     { "TAXI ASSET PROTECTION INDEMNIFIED £10K-£25K £10K LIMIT" , "GAP" },
                     { "TAXI ASSET PROTECTION INDEMNIFIED £0-£10K £10K LIMIT" , "GAP" },
                     { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £20K-£30K" , "GAP" },
                     { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £0-£10K" , "GAP" },
                     { "LCV RETAIL PRICE PROTECTION INDEMNIFIED £20K-£30K £25K LIMIT" , "GAP" },
                     { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £10K-£20K" , "GAP" },
                     { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £30K-£50K" , "GAP" },
                     { "WARANTY COST" , "WarrantyCost" },
                     { "VPS ULTRA + LEATHER GUARD £0-£10K" , "Paint & Fabric" },
                     { "TYRE AND ALLOY WHEEL 12 £300 LIMIT" , "Tyre & Alloy" },
                     { "CONTRACT HIRE BASE" , "Bonus" },
                     { "FLEET RECONCILIATION % BOX" , "Bonus" },
                     { "FRONT END " , "Front End" },
                     { "2019 MARGIN ADJUSTMENT" , "Bonus" },
                     { "BONUS BOX" , "Bonus" },
                     { "TRANSFORM QUALITY MOTAB" , "Bonus" },
                     { "SUSTAINABILITY QUALITY MOTAB" , "Bonus" },
                     { "VOLUME TOTAL CONTRACT MOTAB" , "Bonus" },
                     { "TRANSFORM QUALITY" , "Bonus" },
                     { "SUSTAINABILITY QUALITY" , "Bonus" },
                     { "QUARTERLY PERFORMANCE BONUS" , "Bonus" },
                     { "CONSISTENCY" , "Bonus" },
                     { "BONUS" , "Bonus" },
                     { "DEALER DISCOUNT/FRONT END" , "Front End" },
                     { "SEAT STOCK SUPPORT COST" , "Bonus" },
                     { "SEAT STAR CAR BONUS" , "Bonus" },
                     { "FINANCE SUBSIDY" , "Finance Subsidy" },
                     { "SERVICE PLAN PENETRATION" , "Bonus" },
                     { "CONQUEST LEAD OPTIMISATION" , "Bonus" },
                     { "LOYALTY LEAD OPTIMISATION" , "Bonus" },
                     { "LEAD 360" , "Bonus" },
                     { "RETAILER MARGIN" , "Bonus" },
                     { "RCH PROFIT ADJUSTMENT" , "Front End" },
                     { "FIXED PRICE SERVICE PLAN PAID BY DIRECT DEBIT" , "Service Plan" },
                     { "FIXED COST MAINTENANCE PAID BY DIRECT DEBIT" , "Service Plan" },
                     { "FIXED COST MAINTENANCE PAID IN FULL" , "Service Plan" },
                     { "FIXED PRICE SERVICE PLAN FREE OF CHARGE" , "Service Plan" },
                     { "FIXED PRICE SERVICE PLAN PAID IN FULL" , "Service Plan" },
                     { "MARKETING PLAN ACTIVATION  AOI" , "Bonus" },
                     { "PAINT  FABRIC" , "Paint & Fabric" },
                     { "TYRE  ALLOY" , "Tyre & Alloy" },
                     { "SERVICE PLAN PENETRATION MOTAB" , "Bonus" },
                     { "CONQUEST LEAD OPTIMISATION MOTAB" , "Bonus" },
                     { "LOYALTY LEAD OPTIMISATION MOTAB" , "Bonus" },
                     { "QUALITY 50%" , "Bonus" },
                     { "INVESTOR 50%" , "Bonus" }
                 };

         //profit lookup indices
         List<int> TurnoverColumnIndices = new List<int>();
         List<int> OptionsColumnIndices = new List<int>();
         List<int> SivColumnIndices = new List<int>();
         List<int> MechPrepColumnIndices = new List<int>();
         List<int> BodyPrepColumnIndices = new List<int>();
         List<int> VATColumnIndices = new List<int>();
         List<int> WarrantyColumnIndices = new List<int>();
         List<int> WarrantyCostColumnIndices = new List<int>();
         List<int> FrontEndColumnIndices = new List<int>();
         List<int> BonusColumnIndices = new List<int>();
         List<int> DeliveryColumnIndices = new List<int>();
         List<int> HandlingFeeColumnIndices = new List<int>();
         List<int> PdiColumnIndices = new List<int>();
         List<int> AccessoriesColumnIndices = new List<int>();
         List<int> GapColumnIndices = new List<int>();
         List<int> TyreAndAlloyColumnIndices = new List<int>();
         List<int> PaintAndFabricColumnIndices = new List<int>();
         List<int> AlloyColumnIndices = new List<int>();
         List<int> CosmeticColumnIndices = new List<int>();
         List<int> WheelGardColumnIndices = new List<int>();
         List<int> ServicePlanColumnIndices = new List<int>();
         List<int> ServicePlanCommissionColumnIndices = new List<int>();
         List<int> TyreColumnIndices = new List<int>();
         List<int> ColumnIndices = new List<int>();
         List<int> IncentivesColumnIndices = new List<int>();
         List<int> OverAllowanceColumnIndices = new List<int>();
         List<int> FinanceIncomeColumnIndices = new List<int>();
         List<int> OtherColumnIndices = new List<int>();
         List<int> FinanceSubsidyColumnIndices = new List<int>();
        */


         /*
     foreach (var header in headers)
     {
         colCount++;
         string profitType;

         //skip all the non-profit columns
         if (
             header.ToUpper() == "ORDER DATE" ||
             header.ToUpper() == "HANDOVER DATE" ||
             header.ToUpper() == "INVOICE DATE" ||
             header.ToUpper() == "ORDER NO" ||
             header.ToUpper() == "KEYLOOP DMS MAGIC NUMBER" ||
             header.ToUpper() == "DEALERSHIP" ||
             header.ToUpper() == "CUSTOMER NAME" ||
             header.ToUpper() == "CUSTOMER SCHEME" ||
             header.ToUpper() == "SALES EXEC" ||
             header.ToUpper() == "REG NO" ||
             header.ToUpper() == "MAKE" ||
             header.ToUpper() == "RANGE" ||
             header.ToUpper() == "MODEL" ||
             header.ToUpper() == "DERIVATIVE" ||
             header.ToUpper() == "VIN NUMBER" ||
             header.ToUpper() == "NEW/USED" ||
             header.ToUpper() == "STOCKNUMBER" ||
             header.ToUpper() == "VEHICLE" ||
             header.ToUpper() == "FINANCE TYPE" ||
             header.ToUpper() == "TOTAL PROFIT" ||
             header.ToUpper() == "DEAL CLOSED" ||
             header.ToUpper() == "MANUFACTURER ORDER NO" ||
             header.ToUpper() == "TOTAL BONUS"
             )
         {
             continue;
         };

         //try to match the column header to the massive list of known mappings
         try
         {
             profitType = profitCalcLookup[headers[colCount].Replace("\"", "").Trim().ToUpper()];
         }
         //if we don't have a mapping shove it to other
         catch { profitType = "Other"; };


         switch (profitType)
         {
             case "Turnover": { TurnoverColumnIndices.Add(colCount); break; }
             case "Options": { OptionsColumnIndices.Add(colCount); break; }
             case "SIV": { SivColumnIndices.Add(colCount); break; }
             case "MechPrep": { MechPrepColumnIndices.Add(colCount); break; }
             case "BodyPrep": { BodyPrepColumnIndices.Add(colCount); break; }
             case "VAT": { VATColumnIndices.Add(colCount); break; }
             case "Warranty": { WarrantyColumnIndices.Add(colCount); break; }
             case "WarrantyCost": { WarrantyCostColumnIndices.Add(colCount); break; }
             case "Front End": { FrontEndColumnIndices.Add(colCount); break; }
             case "Bonus": { BonusColumnIndices.Add(colCount); break; }
             case "Delivery": { DeliveryColumnIndices.Add(colCount); break; }
             case "Handling Fee": { HandlingFeeColumnIndices.Add(colCount); break; }
             case "PDI": { PdiColumnIndices.Add(colCount); break; }
             case "Accessories": { AccessoriesColumnIndices.Add(colCount); break; }
             case "GAP": { GapColumnIndices.Add(colCount); break; }
             case "Tyre & Alloy": { TyreAndAlloyColumnIndices.Add(colCount); break; }
             case "Paint & Fabric": { PaintAndFabricColumnIndices.Add(colCount); break; }
             case "Alloy": { AlloyColumnIndices.Add(colCount); break; }
             case "Cosmetic": { CosmeticColumnIndices.Add(colCount); break; }
             case "WheelGard": { WheelGardColumnIndices.Add(colCount); break; }
             case "Service Plan": { ServicePlanColumnIndices.Add(colCount); break; }
             case "Service Plan Commission": { ServicePlanColumnIndices.Add(colCount); break; }
             case "Tyre": { TyreColumnIndices.Add(colCount); break; }
             case "Incentives": { IncentivesColumnIndices.Add(colCount); break; }
             case "Over-Allowance": { OverAllowanceColumnIndices.Add(colCount); break; }
             case "Finance Income": { FinanceIncomeColumnIndices.Add(colCount); break; }
             case "Other": { OtherColumnIndices.Add(colCount); break; }
             case "Finance Subsidy": { FinanceSubsidyColumnIndices.Add(colCount); break; }
         }
     }

       */

         /*
       //create the lookup indices
       int dealershipIndex = headers.IndexOf("Dealership");
       int orderDateIndex = headers.IndexOf("Order Date");
       int handoverDateIndex = headers.IndexOf("Handover Date");
       int invoiceDateIndex = headers.IndexOf("Invoice Date");
       int salesExecIndex = headers.IndexOf("Sales Exec");
       int newUsedIndex = headers.IndexOf("New/Used");
       int financeTypeIndex = headers.IndexOf("Finance Type");
       int orderNoIndex = headers.IndexOf("Order No");
       int customerNameIndex = headers.IndexOf("Customer Name");
       int regIndex = headers.IndexOf("Reg No");
       int makeIndex = headers.IndexOf("Make");
       int modelIndex = headers.IndexOf("Model");
       int derivativeIndex = headers.IndexOf("Derivative");
       int stockNumberIndex = headers.IndexOf("StockNumber");
       int totalProfitIndex = headers.IndexOf("Total Profit");
       int manufacturerOrderNoIndex = headers.IndexOf("Manufacturer Order No");
       int dealClosedIndex = headers.IndexOf("Deal Closed");
       int chassisIndex = headers.IndexOf("Vin Number");

       int defaultFranchiseId = standingValues.Find(x => x.Description == "Non-Franchise").Id;

       */

         try
         {
            /*
            //lookup objects required

            //site
            int siteId = 0;
            try { siteId = SiteConverter(q.DealerName); } catch { }



            //sales exec id
            string salesExecString = q.AssignedToName;
            int? salesExecId = null;

            try { salesExecId = dbPeople.Find(x => x.Name == salesExecString).Id; } catch { }

            // We have not found the exec
            if (salesExecId == null)
            {
                { }
            }

            //enquiry number
            string enquiryNumber = q.EnquiryId;// int.Parse(row[orderNoIndex]);

            string stockNumberToUse = q.Vehicles[0].StockNumber; //first set the stock number to the value in the file
            string stockNumberFirstChar = null;
            if (stockNumberToUse.Length > 0) { stockNumberFirstChar = stockNumberToUse.Substring(0, 1); } //grab the first char to test if it is good
            string chassis = q.Vehicles[0].Vin; //also grab the full chassis

            if (
                stockNumberFirstChar != "N" && stockNumberFirstChar != "U" && stockNumberFirstChar != "D" && stockNumberFirstChar != "Q" && //if we have a bad stock number (good ones start N, U or D)
                chassis != string.Empty //and we DO have a chassis
                )
            {
                Stock matchingStockItem = dbStocks.FirstOrDefault(x => x.Chassis == chassis); //then try to find the chassis in stocks and grab the stocknumberfull from there
                if (matchingStockItem != null) { stockNumberToUse = matchingStockItem.StockNumberFull; };
            };
            if (stockNumberToUse == null || stockNumberToUse == String.Empty) { stockNumberToUse = enquiryNumber.ToString(); }; //if we can't get a stock number then set to the enquirynumber








            if (stockNumberFirstChar == "D" && newOrUsed == "Used") { vehicleTypeId = vehicleTypes.First(x => x.Description == "Demo").Id; };

            //franchise
            string franchiseString = q.Vehicles[0].Make;
            int franchiseId = defaultFranchiseId;
            try { franchiseId = standingValues.Find(x => x.Description == franchiseLookup[franchiseString]).Id; } catch { }




            //finance type
            string financeType = q.pay;
            bool isFinanced = true;
            if (financeType == "Cash" || financeType == "") { isFinanced = false; };



            //new or used
            string newOrUsed = row[newUsedIndex];


            //closed
            string dealClosed = row[dealClosedIndex];
            bool isClosed = false;

            if (dealClosed == "Closed") { isClosed = true; };

            //the profic calc
            List<string> TurnoverColumnValues = row.Where((x, i) => TurnoverColumnIndices.Contains(i)).ToList();
            List<string> OptionsColumnValues = row.Where((x, i) => OptionsColumnIndices.Contains(i)).ToList();
            List<string> SivColumnValues = row.Where((x, i) => SivColumnIndices.Contains(i)).ToList();
            List<string> MechPrepColumnValues = row.Where((x, i) => MechPrepColumnIndices.Contains(i)).ToList();
            List<string> BodyPrepColumnValues = row.Where((x, i) => BodyPrepColumnIndices.Contains(i)).ToList();
            List<string> VATColumnValues = row.Where((x, i) => VATColumnIndices.Contains(i)).ToList();
            List<string> WarrantyColumnValues = row.Where((x, i) => WarrantyColumnIndices.Contains(i)).ToList();
            List<string> WarrantyCostColumnValues = row.Where((x, i) => WarrantyCostColumnIndices.Contains(i)).ToList();
            List<string> FrontEndColumnValues = row.Where((x, i) => FrontEndColumnIndices.Contains(i)).ToList();
            List<string> BonusColumnValues = row.Where((x, i) => BonusColumnIndices.Contains(i)).ToList();
            List<string> DeliveryColumnValues = row.Where((x, i) => DeliveryColumnIndices.Contains(i)).ToList();
            List<string> HandlingFeeColumnValues = row.Where((x, i) => HandlingFeeColumnIndices.Contains(i)).ToList();
            List<string> PdiColumnValues = row.Where((x, i) => PdiColumnIndices.Contains(i)).ToList();
            List<string> AccessoriesColumnValues = row.Where((x, i) => AccessoriesColumnIndices.Contains(i)).ToList();
            List<string> GapColumnValues = row.Where((x, i) => GapColumnIndices.Contains(i)).ToList();
            List<string> TyreAndAlloyColumnValues = row.Where((x, i) => TyreAndAlloyColumnIndices.Contains(i)).ToList();
            List<string> PaintAndFabricColumnValues = row.Where((x, i) => PaintAndFabricColumnIndices.Contains(i)).ToList();
            List<string> AlloyColumnValues = row.Where((x, i) => AlloyColumnIndices.Contains(i)).ToList();
            List<string> CosmeticColumnValues = row.Where((x, i) => CosmeticColumnIndices.Contains(i)).ToList();
            List<string> WheelGardColumnValues = row.Where((x, i) => WheelGardColumnIndices.Contains(i)).ToList();
            List<string> ServicePlanColumnValues = row.Where((x, i) => ServicePlanColumnIndices.Contains(i)).ToList();
            List<string> ServicePlanCommissionColumnValues = row.Where((x, i) => ServicePlanCommissionColumnIndices.Contains(i)).ToList();
            List<string> TyreColumnValues = row.Where((x, i) => TyreColumnIndices.Contains(i)).ToList();
            List<string> IncentivesColumnValues = row.Where((x, i) => IncentivesColumnIndices.Contains(i)).ToList();
            List<string> OverAllowanceColumnValues = row.Where((x, i) => OverAllowanceColumnIndices.Contains(i)).ToList();
            List<string> FinanceIncomeColumnValues = row.Where((x, i) => FinanceIncomeColumnIndices.Contains(i)).ToList();
            List<string> OtherColumnValues = row.Where((x, i) => OtherColumnIndices.Contains(i)).ToList();
            List<string> FinanceSubsidyColumnValues = row.Where((x, i) => FinanceSubsidyColumnIndices.Contains(i)).ToList();


            decimal turnover = 0; foreach (string value in TurnoverColumnValues) { { } turnover += decimal.Parse(value); };
            decimal options = 0; foreach (string value in OptionsColumnValues) { { } options += decimal.Parse(value); };
            decimal siv = 0; foreach (string value in SivColumnValues) { siv += decimal.Parse(value); };
            decimal mechPrep = 0; foreach (string value in MechPrepColumnValues) { mechPrep += decimal.Parse(value); };
            decimal bodyPrep = 0; foreach (string value in BodyPrepColumnValues) { bodyPrep += decimal.Parse(value); };
            decimal vat = 0; foreach (string value in VATColumnValues) { vat += decimal.Parse(value); };
            decimal warranty = 0; foreach (string value in WarrantyColumnValues) { warranty += decimal.Parse(value); };
            decimal warrantyCost = 0; foreach (string value in WarrantyCostColumnValues) { warrantyCost += decimal.Parse(value); };
            decimal frontEnd = 0; foreach (string value in FrontEndColumnValues) { frontEnd += decimal.Parse(value); };
            decimal bonus = 0; foreach (string value in BonusColumnValues) { bonus += decimal.Parse(value); };
            decimal delivery = 0; foreach (string value in DeliveryColumnValues) { delivery += decimal.Parse(value); };
            decimal handlingFee = 0; foreach (string value in HandlingFeeColumnValues) { handlingFee += decimal.Parse(value); };
            decimal pdi = 0; foreach (string value in PdiColumnValues) { pdi += decimal.Parse(value); };
            decimal accessories = 0; foreach (string value in AccessoriesColumnValues) { accessories += decimal.Parse(value); };
            decimal gap = 0; foreach (string value in GapColumnValues) { gap += decimal.Parse(value); };
            decimal tyreAndAlloy = 0; foreach (string value in TyreAndAlloyColumnValues) { tyreAndAlloy += decimal.Parse(value); };
            decimal paintAndFabric = 0; foreach (string value in PaintAndFabricColumnValues) { paintAndFabric += decimal.Parse(value); };
            decimal alloy = 0; foreach (string value in AlloyColumnValues) { alloy += decimal.Parse(value); };
            decimal cosmetic = 0; foreach (string value in CosmeticColumnValues) { cosmetic += decimal.Parse(value); };
            decimal wheelGard = 0; foreach (string value in WheelGardColumnValues) { wheelGard += decimal.Parse(value); };
            decimal servicePlan = 0; foreach (string value in ServicePlanColumnValues) { servicePlan += decimal.Parse(value); };
            decimal servicePlanCommission = 0; foreach (string value in ServicePlanCommissionColumnValues) { servicePlanCommission += decimal.Parse(value); };
            decimal tyre = 0; foreach (string value in TyreColumnValues) { tyre += decimal.Parse(value); };
            decimal incentives = 0; foreach (string value in IncentivesColumnValues) { incentives += decimal.Parse(value); };
            decimal overAllowance = 0; foreach (string value in OverAllowanceColumnValues) { overAllowance += decimal.Parse(value); };
            decimal financeIncome = 0; foreach (string value in FinanceIncomeColumnValues) { financeIncome += decimal.Parse(value); };
            decimal financeSubsidy = 0; foreach (string value in FinanceSubsidyColumnValues) { financeSubsidy += decimal.Parse(value); };

            OtherColumnValues = OtherColumnValues.Where(x => x != "").ToList();

            //add options to turnover, take the vat out first
            turnover = turnover + (options / 1.2m);

            decimal other = 0; foreach (string value in OtherColumnValues)
            {

                try { other += decimal.Parse(value); } catch { };

            };

            decimal PcpFinanceIncome = 0;
            decimal OtherFinanceIncome = 0;

            if (financeType == "Personal Contract Purchase") PcpFinanceIncome = financeIncome;
            else OtherFinanceIncome = financeIncome;

            //populate the bools
            bool hasServicePlan = (servicePlan != 0 || servicePlanCommission != 0);
            bool hasCosmetic = cosmetic > 0;
            bool hasWheelGuard = wheelGard > 0;
            bool hasGap = gap > 0;
            bool hasWarranty = warranty > 0;
            bool hasPaintAndFabric = paintAndFabric > 0;
            bool hasTyre = tyre > 0;
            bool hasAlloy = alloy > 0;
            bool hasTyreAndAlloy = tyreAndAlloy > 0;

            decimal error = 0;

            //and total product count
            // 11/02/22 removing service plan from total prod count as per Vinds request
            bool[] productBools = { hasCosmetic, hasGap, hasWarranty, hasPaintAndFabric, hasTyre, hasAlloy, hasTyreAndAlloy, hasWheelGuard };
            // int totalProductCount = productBools.Where(b => b).Count();

            //calculate the balancing figure to allocate to 'other'. calc is different for new and used.
            decimal calculatedProfit = 0;
            decimal totalProfit = decimal.Parse(row[totalProfitIndex]);

            string missingSiv = "";

            if (newOrUsed == "Used")
            {
                if (siv == 0)
                {
                    siv = totalProfit = 0;
                    missingSiv = " (MISSING SIV)";
                };

                calculatedProfit = turnover - siv - mechPrep + bodyPrep - vat + warranty + warrantyCost + frontEnd + bonus + delivery + handlingFee + pdi + accessories + gap + tyreAndAlloy + paintAndFabric + alloy + cosmetic + wheelGard + servicePlan + servicePlanCommission + tyre + incentives + overAllowance + financeIncome + financeSubsidy + other;
            }
            else
            {
                calculatedProfit = warranty + warrantyCost + frontEnd + bonus + delivery + handlingFee + pdi + accessories + gap + tyreAndAlloy + paintAndFabric + alloy + cosmetic + wheelGard + servicePlan + servicePlanCommission + tyre + incentives + overAllowance + financeIncome + financeSubsidy + other;
            }

            error = totalProfit - calculatedProfit;

            //calc the cost of sale differently for new and used
            decimal costOfSale = 0;

            if (newOrUsed == "Used")
            {
                costOfSale = 0 - siv;
            }
            else
            {
                costOfSale = 0 - turnover + frontEnd;
            }

            //the strings
            string derivative = row[derivativeIndex];
            if (derivative.Length > 50)
            {
                derivative = derivative.Substring(0, 50);
            }

            string description = row[makeIndex] + " " + row[modelIndex];
            if (description.Length > 50)
            {
                description = description.Substring(0, 50);
            }




            //if this deal exists then only update the stock number IF the currently held one is BAD
            string currentStockNumber = "";
            bool currentStockNumberGood = false;
            string currentStockNumberFirstChar = null;

            try { currentStockNumber = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).StockNumber; } catch { }; //find the stocknumber based on the enquirynumber if it exists

            if (currentStockNumber.Length > 0) { currentStockNumberFirstChar = currentStockNumber.Substring(0, 1); } //grab the first char to test if it is good

            if (
                currentStockNumberFirstChar == "N" || currentStockNumberFirstChar == "U" || currentStockNumberFirstChar == "D" || currentStockNumberFirstChar == "Q" //if we have a good stock number (good ones start N, U or D)
                )
            {
                currentStockNumberGood = true;
            }

            if (currentStockNumberGood)
            {
                stockNumberToUse = currentStockNumber;
            }



            string oemReference = null;
            if (manufacturerOrderNoIndex > -1) { oemReference = row[manufacturerOrderNoIndex]; };


            //if this is a duplicate stock number then append an ! at the end
            //if (incomingDeals.Select(x => x.StockNumber).Contains(stockNumberToUse)) { stockNumberToUse = stockNumberToUse + "!"; };

            //is delivered?
            bool isDelivered = false;

            if (fileNameIn.Contains("DELIVERED")) { isDelivered = true; }
            else try { isDelivered = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).IsDelivered; } catch { }

            //reg string
            string reg = row[regIndex];

            //location & stockdate
            string lastPhysicalLocation = null;
            DateTime? today = DateTime.Today;
            DateTime? stockDate = null;
            int vehicleAge = 0;

            //first set the location, stockdate and vehicle age to the existing values in deals
            if (enquiryNumber != 0)
            {
                try
                {
                    lastPhysicalLocation = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).LastPhysicalLocation;
                    stockDate = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).StockDate.Value;
                    vehicleAge = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).VehicleAge;
                }
                catch { };
            }

            if (lastPhysicalLocation == null || lastPhysicalLocation == "") lastPhysicalLocation = "Not found in SM";

            if (stockNumberToUse != "" && stockNumberToUse != null)
            {
                var stocksMatch = dbStocks.FirstOrDefault(x => x.StockNumberFull == stockNumberToUse);
                if (stocksMatch != null)
                {
                    lastPhysicalLocation = stocksMatch.PhysicalLocation;
                    stockDate = stocksMatch.StockDate;
                    vehicleAge = (today - stockDate).Value.Days;
                }
            }

            if (reg != "" && reg != null)
            {
                var stocksMatch = dbStocks.FirstOrDefault(x => x.Reg == reg);
                if (stocksMatch != null)
                {
                    lastPhysicalLocation = stocksMatch.PhysicalLocation;
                    stockDate = stocksMatch.StockDate;
                    vehicleAge = (today - stockDate).Value.Days;
                }
            }


            if (oemReference != "" && oemReference != null && oemReference.Length > 0)
            {
                var stocksMatch = dbStocks.FirstOrDefault(x => x.SupplierOrderNo == oemReference.ToString());
                if (stocksMatch != null)
                {
                    lastPhysicalLocation = stocksMatch.PhysicalLocation;
                    stockDate = stocksMatch.StockDate;
                    vehicleAge = (today - stockDate).Value.Days;
                }
            }

            if (financeType == "Hire Purchase") { financeType = "HP"; };
            if (financeType == "Personal Contract Purchase") { financeType = "PCP"; };
            if (financeType == "Personal Contract Hire") { financeType = "PCH"; };

            string originalSource = fileNameIn.Substring(0, Math.Min(fileNameIn.Length, 50));
            */

            //Options
            Dictionary<string, string> marginsDictionary = new Dictionary<string, string>()
                {
                    { "e3db6cd2-817d-4279-a28e-c5bde2e1ff39", "CosmeticInsuranceCommission" }, //     "title": "C.A.R.S. Plus and Alloy Wheel Protection Indemnified",
                    { "77076ef2-e14d-4072-8d99-d3a733b22cfd", "CosmeticInsuranceCommission" },   //   "title": "C.A.R.S. Plus and Alloy Wheel Protection Indemnified",
                    { "924ddb45-87dd-47a6-890a-bf3867e59b21", "CosmeticInsuranceCommission" },   //   "title": "C.A.R.S. Plus and Alloy Wheel Protection Indemnified",
                    { "0f276f8f-cb08-4c30-9c01-effa047f64be", "PaintProtectionAccessorySale" },   //  "title": "VPS Ultra GX1 (£0-£10k)",
                    { "fd9e0704-f0c3-4a5b-8655-fe88ce0e6c09", "PaintProtectionAccessorySale" },   //  "title": "VPS Ultra GX1 (£10-£20k)",
                    { "6a7afbc1-a30a-41e7-9d3f-7b6c92fda3a3", "PaintProtectionAccessorySale" },   //  "title": "VPS Ultra GX1 (£20-£50k)",},
                    { "85310a59-96bc-47c2-97fc-9171fb5795b1", "PaintProtectionAccessorySale" }, //    "title": "VPS Ultra GX1 + Leather Guard (£0-£10k)",
                    { "c63d94dc-0a47-4e03-9120-a4818cd994fa", "PaintProtectionAccessorySale" },//     "title": "VPS Ultra GX1 + Leather Guard (£10-£20k)",
                    { "32e15e25-c5ff-48ce-85ea-10f8f095c3ca", "PaintProtectionAccessorySale" }, //    "title": "VPS Ultra GX1 + Leather Guard (£20-£50k)",
                    { "e6404cd6-df4d-407b-a396-a3895f3ec885", "PaintProtectionAccessorySale" }, //    "title": "GX2 Graphene (Previously Ceramic)",
                    { "662f243a-b7d1-477d-ae70-706980f8dd0f", "PaintProtectionAccessorySale" }, //    "title": "GX2 Graphene + Leather Guard",
                    { "8dfc0ba0-a237-4142-b454-1c7c64d265cc", "PaintProtectionAccessorySale" },//     "title": "Elixogen (Bentley) inc Leather Gard",
                    //{ "7dd8ec0d-9bac-494d-8c27-789f06a62d5b", "Other" },                         //    "title": "First Registration Fee",           //Commented Other as logic is, if Not found then its Other

                    //Margins
                    { "04841b4e-c20f-434c-a7ed-4e964defe067", "CoS" }, // Front End Audi A1
                    { "c32f3c28-d984-41f5-b1b5-63f72838922d", "PDICost" }, // Audi PDI Cost
                    { "d2928f7a-5988-4c6b-95ab-364f487092ff", "CoS" }, // Front End Audi A3
                    { "9b2b303d-b30b-45ca-9be2-74c544c25fd5", "CoS" }, // Front End Audi A4
                    { "37a39c1e-4235-44bb-95db-b3a51e8f1c23", "NewBonus1" }, // Audi Conquest Lead Optimisation
                    { "35f12b93-cbc6-4c2e-b540-56b9d8d5b82c", "OemDeliverySale" }, // Audi Del Profit
                    { "2b949a59-cb8d-4371-9cfc-18ae2d682df0", "CoS" }, // Front End Audi A5
                    { "68c8a16b-e945-4412-b5f6-804e7fe79569", "CoS" }, // Front End Audi A6
                    { "3e82b0d6-3742-4eb4-8d74-4ad1f0d0c156", "CoS" }, // Front End Audi A7
                    { "f3d3a01e-9ad1-4b76-9e0f-24037f16830d", "CoS" }, // Front End Audi A8
                    { "08e3d7e7-16f2-4764-b3b0-fdbd2a145387", "CoS" }, // Front End Audi E-TRON GT
                    { "b7cf02d9-1ca5-4876-8d87-7e39cb80c3de", "CoS" }, // Front End Audi Q2
                    { "dc5fa56d-83bb-463b-9a4e-0f5fa4eaf75f", "CoS" }, // Front End Audi Q3
                    { "735c739e-20ad-45d0-bd9d-128039fd7b23", "CoS" }, // Front End Audi Q4
                    { "3eb22f87-88a4-4ec0-9884-6d3a5e648967", "CoS" }, // Front End Audi Q5
                    { "38cd8a3f-364d-474f-a8b0-8d5177b9e4ad", "CoS" }, // Front End Audi Q6
                    { "34c7b1cd-1242-4539-a571-c03d2ab2b36a", "CoS" }, // Front End Audi Q7
                    { "afad89b7-7e27-4fd1-98e4-bf897addd9a1", "CoS" }, // Front End Audi Q8
                    { "2c03869d-b5c3-4e0b-b3f5-49b0e3278c44", "CoS" }, // Front End Audi R8
                    { "5f318b3e-9e59-450f-9c99-e9c05702e679", "CoS" }, // Front End Audi RS 7
                    { "e0ca7885-0e7d-4d06-b051-042b4d812d5f", "CoS" }, // Front End Audi RS E-TRON GT
                    { "75b6c3b1-5de7-4d0b-84b4-51f7084b3e9d", "CoS" }, // Front End Audi RS Q3
                    { "1a6e49b9-3322-442e-9381-fb6ac2185b07", "CoS" }, // Front End Audi RS Q8
                    { "fd52b4d5-450f-4ac2-8fbd-4e8b4a1bc65b", "CoS" }, // Front End Audi RS3
                    { "5cbfa0ec-98d3-4b56-8a1e-2f4c0ca1ff4f", "CoS" }, // Front End Audi RS4
                    { "4bda1f8b-e82b-4b25-84d3-77e9a85ff968", "CoS" }, // Front End Audi RS5
                    { "af8a49d5-d238-4e63-b22f-bd99b22e6354", "CoS" }, // Front End Audi RS6
                    { "f34ea900-bf7a-4201-b22d-cac76f24a6b5", "NewBonus1" }, // Audi Loyalty Lead Optimisation
                    { "17b88c5a-44c0-4b47-bf6f-c9901c131f07", "NewBonus1" }, // Audi Volume - Network Counting
                    { "c1eaeb76-043b-475d-9ed9-37a1d68e4f5a", "NewBonus1" }, // Audi Investor Fund
                    { "ec25a10f-5b8b-42f7-b4a0-160f82fdc7e1", "CoS" }, // Front End Bentley Bentayga
                    { "3d6f7f07-09fa-42a4-9a1c-c9b47645a3b1", "CoS" }, // Front End Bentley Continental GT
                    { "7c6de4cd-0738-4e9e-803f-9750560840f0", "CoS" }, // Front End Bentley Continental GTC
                    { "235c6d3f-1e34-4a30-b37e-72812e242b80", "CoS" }, // Front End Bentley Flying Spur
                    { "a8a08413-e0a9-4d8d-a2d9-b9575d03591e", "CoS" }, // Bentley Front End Sales Volume Achievement
                    { "032b825f-6f68-4294-8151-93a6871c8550", "NewBonus1" }, // Bentley Marketing Plan Activation & AOI
                    { "c7c3f9d3-42fc-47f1-b5ac-b8c3db3ecaf4", "NewBonus1" }, // Bentley Order Entry Submission
                    { "22a92a35-d057-4e74-8eb9-3b7a5357b0db", "NewBonus1" }, // Bentley Order Bank Coverage
                    { "c1e983ba-23d6-4877-b62b-46943f6edee6", "NewBonus1" }, // Bentley Option Uptake
                    { "5d086728-4e67-4671-bfeb-38b850a1d04d", "OemDeliverySale" }, // Bentley Delivery Margin
                    { "c22be990-b2db-4849-9440-506b0e51aebd", "CoS" }, // Ducati Dealer Discount/Front End
                    { "26e2b585-bc4d-46f9-a5da-1c8a178fd365", "NewBonus1" }, // Ducati Exclusivity Bonus
                    { "5e5adf2b-249d-4a30-b75a-06a78c82d204", "StandardWarrantyCost" }, // Ducati Warranty Cost
                    { "9021b35e-1e9a-46b3-8bbd-46fdaebd6b0c", "NewBonus1" }, // Ducati Advertising Levy
                    { "72f66364-b9d9-4750-b430-d4d23c27f020", "NewBonus1" }, // Ducati Datatag
                    { "8d7b9e42-f906-4b68-8f56-1b285fa1e146", "NewBonus1" }, // Ducati Handbook
                    { "d4010729-ff75-4a59-b36a-cf3f5a85c820", "OemDeliverySale" }, // Ducati Delivery Margin
                    { "0b4fc7c7-e4b7-4d78-bd94-eaf2e3e849c9", "PDICost" }, // Ducati PDI Cost
                    { "6c3c209e-2175-4d9e-a890-e25cf0a209f7", "NewBonus1" }, // SEAT Motab Bonus Payment
                    { "2e4f1a84-c440-4b3c-b89f-d9c7ff354f34", "NewBonus1" }, // SEAT Consignment
                    { "bfc41c43-3285-47c7-b7f3-433f52b5e527", "NewBonus1" }, // SEAT VRB 100%
                    { "1de6533d-e169-478b-8649-773bb57516a5", "NewBonus1" }, // SEAT Guaranteed Margin
                    { "a8da7584-d89b-4721-9cfd-8a56988f9d0d", "NewBonus1" }, // SEAT Investor Fund
                    { "f89a0a0f-d5c7-4333-9b4c-c9e246a4580f", "NewBonus1" }, // SEAT CEM
                    { "9fd01c02-39d2-41c3-b57e-c16f3f8d35e2", "NewBonus1" }, // SEAT Cash Alternative Offer
                    //{ "0e0fcdad-3a8a-41b7-9548-19db0c1a0240", "Other" }, // SEAT Misc Cost                    //Commented Other as logic is, if Not found then its Other
                    { "4b622ed6-2679-404e-b020-518bc0e18ae5", "PDICost" }, // SEAT PDI Cost
                    { "b88e1c4e-89bb-4656-b3ef-cce63b8b08fa", "NewBonus1" }, // EAT Web Voucher
                    { "53f441e2-dc4f-48b2-8cdb-bc6b3a7a9ef8", "NewBonus1" }, // SEAT Loyalty Bonus
                    { "f7b019ef-bb64-4a7f-93c4-90871c98c288", "NewBonus1" }, // CUPRA Consignment
                    { "ad2c5be2-2825-4c77-9c7d-b4c0b4563f1e", "NewBonus1" }, // CUPRA VRB
                    { "f17a8b63-bfcd-46ed-8fae-cd9021ec1f92", "NewBonus1" }, // CUPRA Fund
                    { "c9bc09a1-ff5b-44de-b122-467a66a2151c", "NewBonus1" }, // CUPRA Motability
                    { "7b734fae-8539-40e2-a0f1-fd2cfb08c234", "NewBonus1" }, // SEAT Convert
                    { "41d81c4b-d1cf-4f42-bc93-e8a6b2050207", "NewBonus1" }, // CUPRA CEM
                    { "46b0c319-d3a1-4e12-9571-4690738d4b79", "CoS" }, // Front End SEAT Arona
                    { "b3a5f8a0-9611-456d-bf94-44ecbe61d415", "CoS" }, // Front End SEAT Ateca
                    { "92ad4b24-3bc4-43a3-a55e-b7ab837bd404", "CoS" }, // Front End SEAT Born
                    { "7d501911-8b97-4a4f-b79e-0c76adf3b5d5", "CoS" }, // Front End SEAT Formentor
                    { "f4a9f028-c712-4a8e-a8fe-8d515f9b22fd", "CoS" }, // Front End SEAT IBIZA
                    { "45b7b66f-c61d-4e1f-b0b7-2c9d5c5a90a7", "CoS" }, // Front End SEAT Leon
                    { "c205f3b8-56b0-4186-9e6b-19b5f285ca72", "CoS" }, // Front End SEAT Tarraco
                    { "2d5ef611-52da-4105-8e84-9a84d56f788a", "WarrantyCost"} // Ducati Warranty Cost
                };


            //vehicle type
            var vehicleTypeEnum = q.Vehicles[0].VehicleType; //should be an enum
            var vehicleTypesInDB = await dealDataAccess.GetVehicleTypes((int)dealerGroupName);
            int vehicleTypeId = vehicleTypesInDB.First(x => string.Equals(x.Description, vehicleTypeEnum.ToString(), StringComparison.OrdinalIgnoreCase)).Id;

            //order type
            var orderType = q.SaleType;
            var orderTypesInDB = await dealDataAccess.GetOrderTypes((int)dealerGroupName);
            int orderTypeId = orderTypesInDB.First(x => string.Equals(x.Code, orderType, StringComparison.OrdinalIgnoreCase)).Id;

            //site
            int siteId = SiteConverter(q.DealerName);

            //Make
            var make = q.Vehicles[0].Make;
            var standingValuesInDB = await standingValuesDataAccess.GetStandingValuesForFranchise(dealerGroupName);
            int makeId = standingValuesInDB.First(x => string.Equals(x.Description, make, StringComparison.OrdinalIgnoreCase)).Id;


            //Vehicle
            var v = q.Vehicles[0];



            //Setting the properties of the deal

            //Deal d = new Deal(); //initialise new one
            //from the excel

            d.EnquiryNumber = q.EnquiryId;
            d.VehicleType_Id = vehicleTypeId;
            d.Reg = v.Identifiers["registration"];
            d.Description = v.VehicleDescription;
            d.StockNumber = v.DmsStockNumber != null ? v.DmsStockNumber : ""; // Possibly just StockNumber
            d.StockDate = v.StockDate;
            SetIsRemovedRemovedDateAndIsDelivered(d, q);

            d.IsFinanced = q.PaymentType == PaymentType.Finance;
            d.FinanceType = q.AcceptedFinanceQuotation.ProductType;

            d.OrderType_Id = orderTypeId;
            d.Site_Id = siteId;
            d.Franchise_Id = makeId;
            d.Salesman_Id = salesmanId;
            d.Model = v.Model;
            d.Variant = v.Variant;
            d.RegisteredDate = v.DateOfFirstRegistration;

            if(q.AcceptedDate != null)
            {
               d.OrderDate = (DateTime) q.AcceptedDate;
            }
            
            d.Discount = q.Balances.TotalDiscount;
            d.AccessoriesSale = q.Balances.SupplementaryExtrasTotal;

            if (v.ProductionYear != null)
            {
               d.ModelYear = (int) v.ProductionYear;
            }
               
            if (q.Customer != null)
            {
               d.Customer = q.Customer.FirstName + " " + q.Customer.LastName;
            }

            var oemDelivery = q.Options.Where(x => x.Title == "OEM Delivery").FirstOrDefault();

            if (oemDelivery != null)
            {
               d.OemDeliverySale = oemDelivery.GrossPrice;
            }

            var m = q.Profit.Margins;

            var costs = m.Where(x => x.Name.Contains("Cost"));
            
            d.CosmeticInsuranceCommission = CalcualateMarginsPrice(q, marginsDictionary.Where(v => v.Value.Equals("CosmeticInsuranceCommission")).ToDictionary(v => v.Key, v => v.Value));
            d.HasCosmeticInsurance = d.CosmeticInsuranceCommission > 0 ? true : false;

            d.PaintProtectionAccessorySale = CalcualateMarginsPrice(q, marginsDictionary.Where(v => v.Value.Equals("PaintProtectionAccessorySale")).ToDictionary(v => v.Key, v => v.Value));
            d.HasPaintProtectionAccessory = d.PaintProtectionAccessorySale > 0 ? true : false;

            d.CoS = CalcualateMarginsPrice(q, marginsDictionary.Where(v => v.Value.Equals("CoS")).ToDictionary(v => v.Key, v => v.Value));
            d.PDICost = CalcualateMarginsPrice(q, marginsDictionary.Where(v => v.Value.Equals("PDICost")).ToDictionary(v => v.Key, v => v.Value));
            d.NewBonus1 = CalcualateMarginsPrice(q, marginsDictionary.Where(v => v.Value.Equals("NewBonus1")).ToDictionary(v => v.Key, v => v.Value));
            d.OemDeliverySale = CalcualateMarginsPrice(q, marginsDictionary.Where(v => v.Value.Equals("OemDeliverySale")).ToDictionary(v => v.Key, v => v.Value));
            d.StandardWarrantyCost = CalcualateMarginsPrice(q, marginsDictionary.Where(v => v.Value.Equals("StandardWarrantyCost")).ToDictionary(v => v.Key, v => v.Value));

            d.Other = CalcualateOtherPrice(q, marginsDictionary); //FYI, Other is part of Profit Margins and AVPM,EPM&BO excel mapping sheet

            d.Units = 1;
            d.ActualDeliveryDate = q.HandoverDate.HasValue ? q.HandoverDate.Value.Date : q.EstimatedHandoverDate.Value;
            d.CreatedDate = DateTime.UtcNow;
            d.LastUpdated = DateTime.UtcNow;

            
            

            /*
            d.OrderDate = q.Created; //orderDate;
            d.ActualDeliveryDate = q.HandoverDate;//handoverDate
            d.InvoiceDate = invoiceDate;
            d.HandoverDate = q.HandoverDate; //handoverDate
            d.EnquiryNumber = q.EnquiryId //enquiryNumber;

            d.Customer = $"{q.Customer.Title} {q.Customer.FirstName} {q.Customer.LastName}";
            d.Salesman_Id = salesExecId.Value;
            d.Description = q.Vehicles[0].VehicleDescription;
            d.Model = q.Vehicles[0].Model;
            d.VariantTxt = q.Vehicles[0].Variant;
            d.OrderType_Id = orderTypeId; //??
            d.Franchise_Id = franchiseId;
            d.IsFinanced = isFinanced;
            d.Discount = incentives;
            d.ServicePlanSale = servicePlan;
            d.OemDeliverySale = delivery;
            d.WarrantySale = warranty;
            d.StandardWarrantyCost = warrantyCost;
            d.AccessoriesSale = accessories;
            d.NewBonus1 = bonus;
            d.NewBonus2 = handlingFee;
            d.PartExOverAllowance1 = overAllowance;
            d.RCIFinanceCommission = PcpFinanceIncome;
            d.FinanceCommission = OtherFinanceIncome;
            //d.FAndIProfit = cosmetic + wheelGard + paintAndFabric + gap + servicePlan + warranty + tyre + alloy + tyreAndAlloy + financeIncome;
            d.GapInsuranceCommission = gap;
            d.Sale = turnover;
            // d.TotalVehicleProfit = totalProfit;
            d.TotalNLProfit = totalProfit;
            d.PDICost = pdi;
            d.CosmeticInsuranceCommission = cosmetic;
            d.WheelGuardCommission = wheelGard;
            d.TyreInsuranceCommission = tyre;
            d.AlloyInsuranceCommission = alloy;
            d.TyreAndAlloyInsuranceCommission = tyreAndAlloy;
            d.PaintProtectionAccessorySale = paintAndFabric;
            d.HasServicePlan = hasServicePlan;
            d.HasCosmeticInsurance = hasCosmetic;
            d.HasGapInsurance = hasGap;
            d.HasTyreInsurance = hasTyre;
            d.HasAlloyInsurance = hasAlloy;
            d.HasTyreAndAlloyInsurance = hasTyreAndAlloy;
            d.HasWheelGuard = hasWheelGuard;
            d.HasWarranty = hasWarranty;
            d.HasPaintProtectionAccessory = hasPaintAndFabric;
            d.Error = error;
            d.CoS = costOfSale;
            d.MechPrep = mechPrep * -1;
            d.BodyPrep = bodyPrep;
            // d.TotalProductCount = totalProductCount;
            d.IsDelivered = isDelivered;
            d.IsRemoved = false;
            d.RemovedDate = null;
            d.OriginalSource = originalSource;
            d.LastUpdated = DateTime.UtcNow;
            d.Units = 1;
            d.AccountingDate = handoverDate;
            d.CreatedDate = DateTime.UtcNow;
            //d.IsCancelled = false;
            // d.TotalVehicleProfit = totalProfit;
            d.WhenNew = DateTime.UtcNow;
            d.IsInvoiced = true;
            d.Other = other;
            d.FinanceCo = financeType;
            d.VatCost = vat * -1;
            d.DeliverySite_Id = siteId;
            d.OemReference = oemReference;
            d.LastPhysicalLocation = lastPhysicalLocation;
            d.VehicleClass_Id = 207; //need to update
            d.StockDate = stockDate;
            d.VehicleAge = vehicleAge;
            d.HasWheelGuard = false;
            d.FinanceType = financeType;
            d.IsClosed = isClosed;
            d.FinanceSubsidy = financeSubsidy;
            */

            //Console.WriteLine(incomingProcessCount);

         }

         catch (Exception err)
         {
            throw;
            //if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
         }
      }

      private decimal CalcualateMarginsPrice(QuotationEventBody q, Dictionary<string, string> dictionary)
      {
         decimal value = 0;

         foreach (var option in q.Profit.Margins)
         {
            if (dictionary.ContainsKey(option.Id))
            {
               value += option.Value;
            }
         }
         foreach (var option in q.Profit.AddedValueProductMargins)
         {
            if (dictionary.ContainsKey(option.Id))
            {
               value += option.Value;
            }
         }
         foreach (var option in q.Profit.ExtraProductMargins)
         {
            if (dictionary.ContainsKey(option.Id))
            {
               value += option.Value;
            }
         }

         return value;

      }


      private decimal CalcualateOtherPrice(QuotationEventBody q, Dictionary<string, string> dictionary)
      {
         //If NOT in the list then its Other
         decimal value = 0;

         foreach (var option in q.Profit.Margins)
         {
            if (!dictionary.ContainsKey(option.Id))
            {
               value += option.Value;
            }
         }
         foreach (var option in q.Profit.AddedValueProductMargins)
         {
            if (!dictionary.ContainsKey(option.Id))
            {
               value += option.Value;
            }
         }
         foreach (var option in q.Profit.ExtraProductMargins)
         {
            if (!dictionary.ContainsKey(option.Id))
            {
               value += option.Value;
            }
         }

         return value;

      }

      private void SetIsRemovedRemovedDateAndIsDelivered(Deal d, QuotationEventBody q)
      {
         switch (q.Status)
         {
            case QuotationStatus.New:
            case QuotationStatus.Sent:
            case QuotationStatus.Accepted:
            case QuotationStatus.PendingAmendments:
               d.IsRemoved = false;
               d.RemovedDate = null;
               d.IsDelivered = false;
               break;
            case QuotationStatus.Lost:
            case QuotationStatus.Deleted:
               d.IsRemoved = true;
               d.RemovedDate = null;
               d.IsDelivered = false;
               break;
            case QuotationStatus.Delivered:
               d.IsRemoved = false;
               d.RemovedDate = null;
               d.IsDelivered = true;
               break;
         }
      }

      public List<Diff> DiffAndUpdate(Deal incomingDeal, Deal existingDeal)
      {


         //List<Stock> changed = new List<Stock>(10000);
         List<Diff> changeDiffs = new List<Diff>();



         var DealDeepComparer = new DealDeepComp(); //instantiate



         try
         {
            var oldDeal = existingDeal;
            if (!DealDeepComparer.Equals(oldDeal, incomingDeal))
            {

               //they are not the same so..
               changeDiffs.AddRange(DealDeepComp.GetDiffs(oldDeal, incomingDeal, "", incomingDeal.EnquiryNumber.ToString())); //find the diffs and record

               //update the old item to pickup the new value for every property
               oldDeal.OrderDate = incomingDeal.OrderDate;
               oldDeal.ActualDeliveryDate = incomingDeal.ActualDeliveryDate;
               oldDeal.InvoiceDate = incomingDeal.InvoiceDate;
               oldDeal.EnquiryNumber = incomingDeal.EnquiryNumber;
               oldDeal.Site_Id = incomingDeal.Site_Id;
               oldDeal.Customer = incomingDeal.Customer;
               oldDeal.Salesman_Id = incomingDeal.IsDelivered ? oldDeal.Salesman_Id : incomingDeal.Salesman_Id;
               oldDeal.Reg = incomingDeal.Reg;
               oldDeal.Description = incomingDeal.Description;
               oldDeal.Model = incomingDeal.Model;
               oldDeal.VariantTxt = incomingDeal.VariantTxt;
               oldDeal.OrderType_Id = incomingDeal.OrderType_Id;
               oldDeal.StockNumber = incomingDeal.StockNumber;
               oldDeal.IsFinanced = incomingDeal.IsFinanced;
               oldDeal.Discount = incomingDeal.Discount;
               oldDeal.ServicePlanSale = incomingDeal.ServicePlanSale;
               oldDeal.OemDeliverySale = incomingDeal.OemDeliverySale;
               oldDeal.WarrantySale = incomingDeal.WarrantySale;
               oldDeal.AccessoriesSale = incomingDeal.AccessoriesSale;
               oldDeal.NewBonus1 = incomingDeal.NewBonus1;
               oldDeal.NewBonus2 = incomingDeal.NewBonus2;
               oldDeal.PartExOverAllowance1 = incomingDeal.PartExOverAllowance1;
               oldDeal.RCIFinanceCommission = incomingDeal.RCIFinanceCommission;
               oldDeal.FinanceCommission = incomingDeal.FinanceCommission;
               oldDeal.GapInsuranceCommission = incomingDeal.GapInsuranceCommission;
               oldDeal.Sale = incomingDeal.Sale;
               //oldDeal.TotalVehicleProfit = incomingDeal.TotalVehicleProfit;
               oldDeal.PDICost = incomingDeal.PDICost;
               oldDeal.CosmeticInsuranceCommission = incomingDeal.CosmeticInsuranceCommission;
               oldDeal.PaintProtectionSale = incomingDeal.PaintProtectionSale;
               oldDeal.PaintProtectionCost = incomingDeal.PaintProtectionCost;
               oldDeal.PaintProtectionAccessorySale = incomingDeal.PaintProtectionAccessorySale;
               oldDeal.PaintProtectionAccessoryCost = incomingDeal.PaintProtectionAccessoryCost;
               oldDeal.HasServicePlan = incomingDeal.HasServicePlan;
               oldDeal.HasCosmeticInsurance = incomingDeal.HasCosmeticInsurance;
               oldDeal.HasGapInsurance = incomingDeal.HasGapInsurance;
               oldDeal.HasWarranty = incomingDeal.HasWarranty;
               oldDeal.HasPaintProtection = incomingDeal.HasPaintProtection;
               oldDeal.HasPaintProtectionAccessory = incomingDeal.HasPaintProtectionAccessory;
               oldDeal.Error = incomingDeal.Error;
               oldDeal.CoS = incomingDeal.CoS;
               // oldDeal.TotalProductCount = incomingDeal.TotalProductCount;
               oldDeal.IsDelivered = incomingDeal.IsDelivered;
               oldDeal.IsRemoved = incomingDeal.IsRemoved;
               oldDeal.RemovedDate = incomingDeal.RemovedDate;
               oldDeal.LastUpdated = incomingDeal.LastUpdated;
               oldDeal.Units = incomingDeal.Units;
               oldDeal.AccountingDate = incomingDeal.AccountingDate;
               oldDeal.CreatedDate = incomingDeal.CreatedDate;
               oldDeal.Franchise_Id = incomingDeal.Franchise_Id;
               oldDeal.TotalNLProfit = incomingDeal.TotalNLProfit;
               //oldDeal.FAndIProfit = incomingDeal.FAndIProfit;
               oldDeal.WarrantyCost = incomingDeal.WarrantyCost;
               oldDeal.StandardWarrantyCost = incomingDeal.StandardWarrantyCost;
               oldDeal.BodyPrep = incomingDeal.BodyPrep;
               oldDeal.MechPrep = incomingDeal.MechPrep;
               oldDeal.VehicleType_Id = incomingDeal.VehicleType_Id;
               oldDeal.Other = incomingDeal.Other;
               oldDeal.FinanceCo = incomingDeal.FinanceCo;
               oldDeal.HandoverDate = incomingDeal.HandoverDate;
               oldDeal.VatCost = incomingDeal.VatCost;
               oldDeal.LastPhysicalLocation = incomingDeal.LastPhysicalLocation;

               oldDeal.HasTyreInsurance = incomingDeal.HasTyreInsurance;
               oldDeal.HasAlloyInsurance = incomingDeal.HasAlloyInsurance;
               oldDeal.HasTyreAndAlloyInsurance = incomingDeal.HasTyreAndAlloyInsurance;
               oldDeal.HasWheelGuard = incomingDeal.HasWheelGuard;

               oldDeal.TyreInsuranceSale = incomingDeal.TyreInsuranceSale;
               oldDeal.TyreInsuranceCost = incomingDeal.TyreInsuranceCost;
               oldDeal.TyreInsuranceCommission = incomingDeal.TyreInsuranceCommission;
               oldDeal.AlloyInsuranceSale = incomingDeal.AlloyInsuranceSale;
               oldDeal.AlloyInsuranceCost = incomingDeal.AlloyInsuranceCost;
               oldDeal.AlloyInsuranceCommission = incomingDeal.AlloyInsuranceCommission;
               oldDeal.TyreAndAlloyInsuranceSale = incomingDeal.TyreAndAlloyInsuranceSale;
               oldDeal.TyreAndAlloyInsuranceCost = incomingDeal.TyreAndAlloyInsuranceCost;
               oldDeal.TyreAndAlloyInsuranceCommission = incomingDeal.TyreAndAlloyInsuranceCommission;
               oldDeal.WheelGuardSale = incomingDeal.WheelGuardSale;
               oldDeal.WheelGuardCost = incomingDeal.WheelGuardCost;
               oldDeal.WheelGuardCommission = incomingDeal.WheelGuardCommission;
               // oldDeal.ServicePlanCommission = incomingDeal.ServicePlanCommission;
               oldDeal.OemReference = incomingDeal.OemReference;
               oldDeal.StockDate = incomingDeal.StockDate;
               oldDeal.VehicleAge = incomingDeal.VehicleAge;
               oldDeal.IsUpdated = true;
               oldDeal.LastUpdated = DateTime.UtcNow; //update the lastUpdated date
               oldDeal.FinanceType = incomingDeal.FinanceType;
               oldDeal.IsClosed = incomingDeal.IsClosed;
               oldDeal.FinanceSubsidy = incomingDeal.FinanceSubsidy;
            }

         }

         catch (Exception err)
         {
            throw;
         }



         return changeDiffs;
      }
   }
}
