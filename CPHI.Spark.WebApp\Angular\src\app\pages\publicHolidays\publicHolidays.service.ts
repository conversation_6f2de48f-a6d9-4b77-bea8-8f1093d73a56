import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { PublicHolidayInit, PublicHolidayRow, PublicHolidayRequest, PublicHolidayResponse } from './publicHolidays.model';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Injectable({
  providedIn: 'root'
})
export class PublicHolidaysService {

  public publicHolidays: PublicHolidayInit;
  private dataSubject = new BehaviorSubject<PublicHolidayInit>(null);
  public data$ = this.dataSubject.asObservable();

  constructor(
    private getDataService: GetDataMethodsService,
    private constants: ConstantsService,
    private selections: SelectionsService
  ) { }

  async initializeData(): Promise<void> {
    try {
      this.selections.triggerSpinner.emit({ show: true, message: 'Loading public holidays...' });
      
      // For now, we'll create mock data since we only have the Add endpoint
      // In a real implementation, you'd call the backend to get existing holidays and regions
      this.publicHolidays = {
        holidays: [],
        regions: [
          { id: 1, region: 'England' },
          { id: 2, region: 'Scotland' },
          { id: 3, region: 'Wales' },
          { id: 4, region: 'Northern Ireland' }
        ],
        dealerGroupId: this.selections.user.DealerGroupId,
        dealerGroupName: this.selections.user.DealerGroupName
      };

      this.dataSubject.next(this.publicHolidays);
    } catch (error) {
      console.error('Error loading public holidays:', error);
      this.constants.toastError('Failed to load public holidays');
    } finally {
      this.selections.triggerSpinner.emit({ show: false });
    }
  }

  async addPublicHoliday(request: PublicHolidayRequest): Promise<PublicHolidayResponse> {
    try {
      this.selections.triggerSpinner.emit({ show: true, message: 'Adding public holiday...' });
      
      const response = await this.getDataService.addPublicHoliday(request).toPromise();
      
      if (response.success) {
        // Add the new holiday to our local data
        const regionNames = this.publicHolidays.regions
          .filter(r => request.regionIds.includes(r.id))
          .map(r => r.region)
          .join(', ');

        const newHoliday: PublicHolidayRow = {
          id: response.id,
          date: response.date,
          dealerGroupId: response.dealerGroupId,
          dealerGroupName: this.publicHolidays.dealerGroupName,
          regions: regionNames,
          regionIds: response.regionIds
        };

        this.publicHolidays.holidays.push(newHoliday);
        this.dataSubject.next(this.publicHolidays);
        
        this.constants.toastSuccess('Public holiday added successfully');
      } else {
        this.constants.toastError(response.message || 'Failed to add public holiday');
      }
      
      return response;
    } catch (error) {
      console.error('Error adding public holiday:', error);
      this.constants.toastError('Failed to add public holiday');
      throw error;
    } finally {
      this.selections.triggerSpinner.emit({ show: false });
    }
  }

  removeHolidayFromLocal(holidayId: number): void {
    const index = this.publicHolidays.holidays.findIndex(h => h.id === holidayId);
    if (index > -1) {
      this.publicHolidays.holidays.splice(index, 1);
      this.dataSubject.next(this.publicHolidays);
    }
  }

  updateHolidayInLocal(updatedHoliday: PublicHolidayRow): void {
    const index = this.publicHolidays.holidays.findIndex(h => h.id === updatedHoliday.id);
    if (index > -1) {
      this.publicHolidays.holidays[index] = updatedHoliday;
      this.dataSubject.next(this.publicHolidays);
    }
  }
}
