<nav class="navbar">

  <nav class="generic" >
    
    
    
    <h4 id="pageTitle">
      <div >

        {{constants.translatedText.Dashboard_StockReport_Title}}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Stocks"></sourceDataUpdate>
        <span *ngIf="selections.stockReport.report?.name"> - {{selections.stockReport.report?.name}}</span>

      </div>
    </h4>


    <ng-container *ngIf="selections.stockReport">

      <!-- Report selector -->
      <div class="buttonGroup">
        <ng-container *ngFor="let report of selections.stockReport.reports">
          <button class="btn btn-primary" [ngClass]="{'active':report.name == selections.stockReport.report?.name}"
          [id]="report.name + 'Button'"
            *ngIf="report.showInMenu" (click)="chooseReport(report)">{{report.name}}</button>
        </ng-container>
      </div>

      <!-- Franchise selector -->
      <franchisePicker
        *ngIf="selections.stockReport && service.showFranchisePicker"
        [franchisesFromParent]="selections.stockReport.franchises"
        [buttonClass]="" (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>

      <!-- Inc Res Vehicles Slider -->
      <sliderSwitch 
        *ngIf="constants.environment.stockReport.includeReservedCarsOption"
        id="includeReservedCarsSlider" 
        [text]="'Include Reserved Vehicles?'" 
        (toggle)="toggleReservedCars()"
        [defaultValue]="includeReservedCarsOption">
      </sliderSwitch>

      <!-- Ageing and As At -->
      <div class="buttonGroup" *ngIf="selections.stockReport?.report?.name == constants.translatedText.Dashboard_PartsStock_OverageStock">
        <!-- Pick ageing button -->
        <div ngbDropdown class="d-inline-block" *ngIf="selections.stockReport.ageingOption" [autoClose]="true">

          <button class="buttonGroupLeft btn btn-primary" ngbDropdownToggle [id]="'AgeingButton'">{{constants.translatedText.Dashboard_StockReport_AgedOver}}
            {{selections.stockReport.ageingOption.description}}</button>

          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let ageingOption  of selections.stockReport.ageingOptions" 
              (click)="selectAgeing(ageingOption)" ngbDropdownItem [id]="ageingOption.ageCutoff + 'Option'">{{ageingOption.description}}</button>

          </div>
        </div>

        <!-- Pick as at button -->
        <div ngbDropdown class="d-inline-block" [autoClose]="true"
          *ngIf="selections.stockReport.asAt && selections.stockReport?.report?.name == constants.translatedText.Dashboard_PartsStock_OverageStock">
          <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>{{constants.translatedText.Dashboard_StockReport_AsAt}}
            {{selections.stockReport.asAt.description}}</button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let asAt  of selections.stockReport.asAts" (click)="selectAsAt(asAt)"
              ngbDropdownItem>{{asAt.description}}</button>

          </div>
        </div>

        <!-- Pick ageing type -->
        <div ngbDropdown class="d-inline-block" [autoClose]="true"
        *ngIf="selections.stockReport?.report?.name == constants.translatedText.Dashboard_PartsStock_OverageStock && this.constants.environment.stockReport.showAgePicker">
        <button class="buttonGroupRight btn btn-primary" ngbDropdownToggle>
          <span *ngIf="selections.stockReport.useBranchDays">{{constants.translatedText.Dashboard_StockReport_UsingDaysAtBranch}}</span>
          <span *ngIf="!selections.stockReport.useBranchDays">{{constants.translatedText.Dashboard_StockReport_UsingGroupDays}}</span>
        </button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button  (click)="selectUseBranchDays(true)"  ngbDropdownItem>{{constants.translatedText.Dashboard_StockReport_BranchDays}}</button>
            <button  (click)="selectUseBranchDays(false)"  ngbDropdownItem>{{constants.translatedText.Dashboard_StockReport_GroupDays}}</button>
          </div>
        </div>



      </div>

      <ng-container *ngIf="selections.stockReport?.report?.name == constants.translatedText.Dashboard_PartsStock_StockGraphs || selections.stockReport?.report?.name == constants.translatedText.Dashboard_PartsStock_StockByAge">
        
       
      </ng-container>

    </ng-container>



  </nav>

  <nav class="pageSpecific" >





  </nav>
</nav>



<!-- Main Page -->
<div class="content-new">
  <div *ngIf="selections.stockReport && selections.stockReport.report" class="content-inner-new">

      <!-- Used Stock Tables -->
        <usedStockTable
          *ngIf="selections.stockReport.report.name == constants.translatedText.Dashboard_PartsStock_UsedStock"
          [isRegionalTable]="false"
        >
        </usedStockTable>
     
        <usedStockTable
          *ngIf="selections.stockReport.report.name == constants.translatedText.Dashboard_PartsStock_UsedStock"
          [isRegionalTable]="true"
        >
        </usedStockTable>

      <!-- All Stock Tables -->
        <allStockTable
          *ngIf="selections.stockReport.report.name == constants.translatedText.Dashboard_PartsStock_AllStock"
          [isRegionalTable]="false"
        >
        </allStockTable>

        <allStockTable
          *ngIf="selections.stockReport.report.name == constants.translatedText.Dashboard_PartsStock_AllStock"
          [isRegionalTable]="true"
        >
        </allStockTable>

      <!-- Overage Stock Tables -->
        <overAgeStockTable
          *ngIf="selections.stockReport.report.name == constants.translatedText.Dashboard_PartsStock_OverageStock && service.overageRowData"
          [isRegionalTable]="false"
          id="OverageStockTableLoaded"
        >
        </overAgeStockTable>

        <overAgeStockTable
          *ngIf="selections.stockReport.report.name == constants.translatedText.Dashboard_PartsStock_OverageStock && service.overageRowData"
          [isRegionalTable]="true"
        >
        </overAgeStockTable>  

      <!-- Used stock merchandising tables -->
        <usedStockMerchTable
          *ngIf="selections.stockReport.report.name == constants.translatedText.Dashboard_PartsStock_UsedMerchandising && service.usedMerchRowData"
          [isRegionalTable]="false"
        >
        </usedStockMerchTable>

        <usedStockMerchTable
          *ngIf="selections.stockReport.report.name == constants.translatedText.Dashboard_PartsStock_UsedMerchandising && service.usedMerchRowData"
          [isRegionalTable]="true"
        >
        </usedStockMerchTable>
      

        
       
      </div>




</div>