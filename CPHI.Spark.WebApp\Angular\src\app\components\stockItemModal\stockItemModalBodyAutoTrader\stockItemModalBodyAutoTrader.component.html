<div class="d-flex flex-column">
    <div id="row-1" class="d-flex flex-row">
        <!-- Vehicle details -->
        <div id="vehicle-details-card" class="cph-card">
            <div class="card-header">
                {{ constants.translatedText.StockItemModal_VehicleDetails }}
                <div *ngIf="!service.givenStockItem.IsPrepped && service.givenStockItem.ShouldBePrepped"
                    class="notPrepped">
                    Not Prepped!
                </div>
            </div>
            <div class="card-body">
                <table class="cph">
                    <tbody>
                        <tr>
                            <td>{{ constants.translatedText.Site }}</td>
                            <td>{{ service.givenStockItem.SiteDescription }}</td>
                            <td>{{ constants.translatedText.Make }}</td>
                            <td>{{ service.givenStockItem.Make }}</td>
                            <td>{{ constants.translatedText.StockItemModal_CapId }}</td>
                            <td>{{ service.givenStockItem.CapID }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.StockItemModal_PreviousSite }}</td>
                            <td>{{ service.givenStockItem.PreviousSiteDescription }}</td>
                            <td>{{ constants.translatedText.Model }}</td>
                            <td>{{ service.givenStockItem.Model }}</td>
                            <td>{{ constants.translatedText.StockItemModal_CapCode }}</td>
                            <td>{{ service.givenStockItem.CapCode }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.Id }}</td>
                            <td>{{ service.givenStockItem.Id }}</td>
                            <td>{{ constants.translatedText.ModelYear }}</td>
                            <td>{{ service.givenStockItem.ModelYear }}</td>
                            <td>{{ constants.translatedText.StockItemModal_CapNotes }}</td>
                            <td>{{ service.givenStockItem.CapNotes }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.StockItemModal_Registration }}</td>
                            <td>{{ service.givenStockItem.Reg }}</td>
                            <td>{{ constants.translatedText.Description }}</td>
                            <td>{{ service.givenStockItem.Description }}</td>
                            <td>Purchased</td>
                            <td>{{ service.givenStockItem.Purchased | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.StockItemModal_Chassis }}</td>
                            <td>{{ service.givenStockItem.Chassis }}</td>
                            <td>{{ constants.translatedText.VariantClass }}</td>
                            <td>{{ service.givenStockItem.VariantClass }}</td>
                            <td>NR Costs</td>
                            <td>{{ service.givenStockItem.NonRecoverableCosts | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.StockItemModal_ProgressCode }}</td>
                            <td>{{ service.givenStockItem.ProgressCode }}</td>
                            <td>{{ constants.translatedText.Colour }}</td>
                            <td>{{ service.givenStockItem.Colour }}</td>
                            <td>SIV</td>
                            <td>{{ service.givenStockItem.Siv | cph:'currency':0 }}</td>
                        </tr>                        
                        <tr>
                            <td>{{ constants.translatedText.DealDetails_RegisteredDate }}</td>
                            <td>{{ service.givenStockItem.RegDate | cph:'date':0 }}</td>
                            <td>{{ constants.translatedText.StockItemModal_Mileage }}</td>
                            <td>{{ service.givenStockItem.Mileage }}</td>
                            <td>CAP Value</td>
                            <td>{{ service.givenStockItem.CapValue | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.DealDetails_StockDate }}</td>
                            <td>{{ service.givenStockItem.StockDate | cph:'date':0 }}</td>
                            <td>{{ constants.translatedText.StockItemModal_Fuel }}</td>
                            <td>{{ service.givenStockItem.Fuel }}</td>
                            <td>CAP Provision</td>
                            <td>{{ service.givenStockItem.CapProvision | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.StockItemModal_DaysInStock }}</td>
                            <td>{{ service.givenStockItem.DaysInStock | cph:'number':0 }}</td>
                            <td>{{ constants.translatedText.StockItemModal_Doors }}</td>
                            <td>{{ service.givenStockItem.Doors }}</td>
                            <td>Carrying Value </td>
                            <td>{{ service.givenStockItem.CarryingValue | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.StockItemModal_DaysAtBranch }}</td>
                            <td>{{ service.givenStockItem.DaysAtBranch | cph:'number':0 }}</td>
                            <td>{{ constants.translatedText.StockItemModal_Transmission }}</td>
                            <td>{{ service.givenStockItem.Transmission }}</td>
                            <td>DFA </td>
                            <td>{{ service.givenStockItem.DealerFitAccessories | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.VehicleType }}</td>
                            <td>{{ service.givenStockItem.VehicleType }}</td>
                            <td>{{ constants.translatedText.StockItemModal_Options }}</td>
                            <td>{{ service.givenStockItem.Options }}</td>
                            <td>Option Costs </td>
                            <td>{{ service.givenStockItem.OptionCosts | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.StockItemModal_DisposalRoute }}</td>
                            <td>{{ service.givenStockItem.DisposalRoute }}</td>
                            <td>{{ constants.translatedText.StockItemModal_PreviousUse }}</td>
                            <td>{{ service.givenStockItem.PreviousUse }}</td>
                            <td>Selling Price </td>
                            <td>{{ service.givenStockItem.Selling | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td>{{ constants.translatedText.StockItemModal_AccountStatus }}</td>
                            <td>{{ service.givenStockItem.AccountStatus }}</td>
                            <td>{{ constants.translatedText.Franchise }}</td>
                            <td>{{ service.givenStockItem.Franchise }}</td>
                            <td>Priced Profit </td>
                            <td>{{ service.givenStockItem.PricedProfit | cph:'currency':0 }}</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td></td>
                            <td>{{ constants.translatedText.StockItemModal_VatQualifying }}</td>
                            <td>{{ service.givenStockItem.IsVatQ ? 'Q' : 'N' }}</td>
                            <ng-container *ngIf="service.givenStockItem.StockcheckScanDate">
                                <td>Last StockChecked</td>
                                <td>{{ service.givenStockItem.StockcheckScanDate |cph:'date':0 }}</td>
                            </ng-container>
                            <ng-container *ngIf="!service.givenStockItem.StockcheckScanDate">
                                <td></td>
                                <td></td>
                            </ng-container>
                        </tr>
                        <tr *ngIf="service.givenStockItem.StockcheckLocation">
                            <td></td>
                            <td></td>
                            <td>Stockcheck Location</td>
                            <td>{{ service.givenStockItem.StockcheckLocation }}</td>
                            <td></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!-- Stock check photos -->
        <div id="stock-check-photos-card" class="cph-card">
            <div class="card-header">
                Stock Check Photos
            </div>
            <div class="card-body">
                <div *ngIf="service.lastThreeStockchecks && service.lastThreeStockchecks.length > 0" class="iphone-container">
                    <div class="change-stockcheck-buttons">
                        <button
                            *ngFor="let stockcheck of service.lastThreeStockchecks; index as i;"
                            class="btn btn-primary"
                            [ngClass]="{ 'active': service.lastThreeStockchecks[stockCheckPhotoToDisplay].Id === stockcheck.Id }"
                            (click)="stockCheckPhotoToDisplay = i">
                            {{ stockcheck.ScanDateTime | cph:'shortDate':0 }}
                        </button>
                    </div>
                    <div class="iphone">
                        <div class="screen">
                            <div class="camera-section"></div>
                            <div class="screen-header">{{ service.lastThreeStockchecks[stockCheckPhotoToDisplay].Location }}</div>
                            <img [src]="service.lastThreeStockchecks[stockCheckPhotoToDisplay].URL">
                            <div class="screen-footer">{{ service.lastThreeStockchecks[stockCheckPhotoToDisplay].Name }}</div>
                        </div>
                    </div>
                </div>
                <span *ngIf="!service.lastThreeStockchecks || service.lastThreeStockchecks && service.lastThreeStockchecks.length === 0">
                    No stock check photos to display.
                </span>
            </div>
        </div>
        <!-- Cost history chart -->
        <!-- <div id="cost-history-card" class="cph-card">
            <div class="card-header">
                Pricing and Cost History. Priced Profit
                {{ service.givenStockItem.PricedProfit | cph:'currency':0:true }}
            </div>
            <div class="card-body">
                <priceChart *ngIf="service.pricingChartData.dataSets && service.pricingChartData.dataSets.length > 0"
                    [dataPointSets]="service.pricingChartData.dataSets" [labels]="service.pricingChartData.labels"
                    [isAutoTraderModal]="true">
                </priceChart>
            </div>
        </div> -->
    </div>
    <div id="row-2" class="d-flex flex-row">
        <!-- Website card -->
        <div id="website-card" class="cph-card">
            <div class="card-header">
                RRG Website Details
            </div>
            <div class="card-body">
                <ng-container *ngIf="service.givenStockItem.IsOnWebsite">
                    <div class="vehicle-details">
                        <div class="slideshow-holder">
                            <div placement="right" container="body" [openDelay]="300" [closeDelay]="500"
                                [ngbPopover]="brokenImageLinks ? null : bigPicturePopover" triggers="mouseenter:mouseleave"
                                popoverTitle="">
                                <img id="rrgWebsiteImage" class="" (error)="updateUrl($event)"
                                    [src]="service.imageUrl">
                            </div>

                            <div class="navigation-buttons">
                                <button class="btn btn-primary" (click)="service.previousPicture()">
                                    <i class="fas fa-caret-circle-left"></i>
                                </button>
                                <div class="text-center">
                                    {{ constants.pluralise(service.imageUrls.length, 'image', 'images') }}
                                </div>
                                <button class="btn btn-primary" (click)="service.nextPicture()">
                                    <i class="fas fa-caret-circle-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="details-holder">
                            <div id="summary">
                                <div id="price">
                                    {{ service.givenStockItem.WebsitePrice | cph:'currency':0 }}
                                </div>
                                <div id="yearMadeModel">
                                    {{ service.givenStockItem.ModelYear }} {{ service.givenStockItem.Make }} {{
                                    service.givenStockItem.Model }}
                                </div>
                                <div id="derivative">
                                    {{ service.givenStockItem.Derivative }}
                                </div>
                                <div id="attentionGrabber">
                                    {{ service.givenStockItem.AttentionGrabber }}
                                </div>
                                <div id="site">
                                    {{ service.givenStockItem.SiteDescription }}
                                </div>
                                <div class="regPlate" id="reg">
                                    {{ service.givenStockItem.Reg | cph:'numberPlate':0 }}
                                </div>
                            </div>
                            <div id="vehicle-detail-table" class="d-flex">
                                <table class="cph">
                                    <tbody>
                                        <tr>
                                            <td>Mileage</td>
                                            <td>{{ service.givenStockItem.Mileage }}</td>
                                            <td>Body Type</td>
                                            <td>{{ service.givenStockItem.BodyType }}</td>
                                        </tr>
                                        <tr>
                                            <td>Fuel Type</td>
                                            <td>{{ service.givenStockItem.Fuel }}</td>
                                            <td>{{ constants.translatedText.Colour }}</td>
                                            <td>{{ service.givenStockItem.Colour }}</td>
                                        </tr>
                                        <tr>
                                            <td>Transmission</td>
                                            <td>{{ service.givenStockItem.Transmission }}</td>
                                            <td>Previous Owners</td>
                                            <td>{{ service.givenStockItem.PreviousOwners }}</td>
                                        </tr>
                                        <tr>
                                            <td>Doors</td>
                                            <td>{{ service.givenStockItem.Doors }}</td>
                                            <td>Engine Size</td>
                                            <td>{{ service.givenStockItem.EngineSize }}{{ service.givenStockItem.EngineSizeUnit }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div id="advert-description">
                        {{ service.givenStockItem.AdvertDescription1 }}
                    </div>
                </ng-container>
                <span *ngIf="!service.givenStockItem.IsOnWebsite">
                    This vehicle is not currently listed on the RRG website.
                </span>
            </div>
        </div>
        <!-- Web listing performance -->
        <div id="web-listing-performance-card" class="cph-card">
            <div class="card-header">
                AutoTrader Website Details
            </div>
            <div class="card-body">
                <div class="d-flex flex-column w-100">
                    <div *ngIf="vehicleAdvert">
                        <table class="cph">
                            <thead>
                                <tr>
                                    <th colspan="2"></th>
                                    <th class="table-spacer"></th>
                                    <th></th>
                                    <th>Last 7</th>
                                    <th>Yesterday</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="label">Retail supply</td>
                                    <td>{{ vehicleAdvert.RetailSupply | cph:'number':0 }}</td>
                                    <td class="table-spacer"></td>
                                    <td class="label">Search matches</td>
                                    <td>{{ vehicleAdvert.SearchViews7Day | cph:'number':0 }}</td>
                                    <td>{{ vehicleAdvert.SearchViewsYest | cph:'number':0 }}</td>
                                </tr>
                                <tr>
                                    <td class="label">Retail demand</td>
                                    <td>{{ vehicleAdvert.RetailDemand | cph:'number':0 }}</td>
                                    <td class="table-spacer"></td>
                                    <td class="label">Ad views</td>
                                    <td>{{ vehicleAdvert.AdvertViews7Day | cph:'number':0 }}</td>
                                    <td>{{ vehicleAdvert.AdvertViewsYest | cph:'number':0 }}</td>
                                </tr>
                                <tr>
                                    <td class="label">Retail market condition</td>
                                    <td>{{ vehicleAdvert.RetailMarketCondition | cph:'number':0 }}</td>
                                    <td class="table-spacer"></td>
                                    <td colspan="3"></td>
                                </tr>
                                <tr>
                                    <td class="label">Retail rating</td>
                                    <td>{{ vehicleAdvert.RetailRating |cph:'number':2 }}</td>
                                    <td class="table-spacer"></td>
                                    <td class="label">Performance rating</td>
                                    <td colspan="2">
                                        <span class="text-nowrap">
                                            {{ vehicleAdvert.PerfRating }} ({{ vehicleAdvert.PerfRatingScore }})
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label">Retail valuation</td>
                                    <td>{{ vehicleAdvert.ValuationMktAvRetail |cph:'currency':0 }}</td>
                                    <td class="table-spacer"></td>
                                    <td class="label">Days Listed</td>
                                    <td colspan="2">
                                        <span class="text-nowrap">
                                            {{ vehicleAdvert.DaysListed }} 
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="label">Current Price</td>
                                    <td>{{ vehicleAdvert.AdvertisedPrice |cph:'currency':0 }}</td>
                                    <td class="table-spacer"></td>
                                    <td class="label">Days to sell</td>
                                    <td colspan="2">
                                        <span class="text-nowrap">
                                            {{ vehicleAdvert.DaysToSellAtCurrentSelling }} 
                                        </span>
                                    </td>
                                </tr>

                                <!-- <tr >
                                    <td class="label">Pending Auto Price Change</td>
                                    <td>
                                        <span *ngIf="vehicleAdvert.PendingAutoChangeNowPrice">{{ vehicleAdvert.PendingAutoChangeNowPrice |cph:'currency':0 }}</span>
                                        <span *ngIf="!vehicleAdvert.PendingAutoChangeNowPrice">None</span>
                                    </td>
                                    <td class="table-spacer"></td>
                                    <td class="label">
                                        <span *ngIf="vehicleAdvert.PendingAutoChangeNowPrice">Change
                                        </span>
                                        </td>
                                    <td colspan="2">
                                        <span *ngIf="vehicleAdvert.PendingAutoChangeNowPrice" class="text-nowrap">
                                            {{ vehicleAdvert.PendingAutoChangeNowPrice - vehicleAdvert.PendingAutoChangeWasPrice|cph:'currency':0 }} 
                                        </span>
                                        <span *ngIf="!vehicleAdvert.PendingAutoChangeNowPrice" class="text-nowrap">
                                            
                                        </span>
                                    </td>
                                </tr> -->


                            </tbody>
                        </table>
                        <div id="autoTraderLink">
                            <span (click)="goToListing()">Direct link to autotrader advert <i class="fas fa-car"></i></span>
                        </div>
                        <div id="opt-out">
                            <button *ngIf="!vehicleOptOutStatus" [disabled] = "!selections.user.permissions.canActionStockPrices" class="btn btn-success" (click)="maybeOptOut()">
                                Opt-out of auto-pricing
                            </button>

                            <div *ngIf="vehicleOptOutStatus" class="d-flex flex-column">
                                <span>This vehicle is currently opted-out of auto-pricing. You can opt-in here.</span>

                                <button class="btn btn-success align-self-start" (click)="maybeOptIn()">
                                    Opt-in to auto-pricing
                                </button>

                                <span>Alternatively, you can manually enter a new price here.</span>
                                <div class="d-flex">
                                    <input type="text" class="w-50 me-2" [ngModel]="newPrice | cph:'currency':2"
                                        (blur)="setNewPrice($event)" (click)="select($event)">

                                    <button class="btn btn-success" [disabled]="!newPrice" (click)="maybeUpdatePrice()">
                                        OK
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <span *ngIf="!vehicleAdvert">
                        This vehicle is not currently listed on AutoTrader.
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<ng-template class="popover" #bigPicturePopover>
    <img id="rrgWebsiteImageBig" class="" [src]="service.imageUrl">
</ng-template>

<ng-template #optOutModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            Confirm opt-out
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <span class="hint">Choose a date to end auto-pricing opt-out. This can't be any more than 4 weeks from
            now.</span>
        <input type="date" [(ngModel)]="optOutEndDate" [min]="minDate" [max]="maxDate">
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-success" (click)="optOut()">Confirm</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
    </div>
</ng-template>