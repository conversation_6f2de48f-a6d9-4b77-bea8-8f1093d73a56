import { RunChaseDataPoint, TurnoverPerOperative } from "src/app/model/main.model";
import { OrdersSpainSummaryBySiteParams } from "../ordersBySite/ordersBySite.model";
import { DeliveredInTimeStat } from "./tiles/deliveryVsOrderDate/deliveryVsOrderDate.component";
import { UsedStockMerchandisingBySiteItem } from "../../model/UsedStockMerchandisingBySiteItem";



export enum DashboardDataItem {

  DepartmentDealBreakdown = "DepartmentDealBreakdown",  //1
  DonutDataSets = "DonutDataSets", //2
  DonutDataSetsWTD = "DonutDataSetsWTD",  //3
  DonutDataSetsYesterday = "DonutDataSetsYesterday",  //4

  DepartmentProfitPerUnitsMonth = "DepartmentProfitPerUnitsMonth",  //5
  DepartmentProfitPerUnitsWTD = "DepartmentProfitPerUnitsWTD", //6
  DepartmentProfitPerUnitsYesterday = "DepartmentProfitPerUnitsYesterday",  //7

  DailyNetOrdersNew = "DailyNetOrdersNew",  //8
  DailyNetOrdersFleet = "DailyNetOrdersFleet",  //9
  DailyNetOrdersUsed = "DailyNetOrdersUsed", //10

  DailyNetOrdersCancellationsNew = "DailyNetOrdersCancellationsNew",  //11
  DailyNetOrdersCancellationsFleet = "DailyNetOrdersCancellationsFleet",  //12
  DailyNetOrdersCancellationsUsed = "DailyNetOrdersCancellationsUsed",  //13

  FinanceAndAddOn = "FinanceAndAddOn",  //14
  Registrations = "Registrations",  //15
  UsedStockMerchandising = "UsedStockMerchandising",  //16
  UsedStockHealth = "UsedStockHealth",  //17

  OverageStockSummary = "OverageStockSummary",  //18
  ActivityLevelsAndOverdues = "ActivityLevelsAndOverdues",  //19
  DeliveredInTime = "DeliveredInTime",  //20




  //Aftersales
  WipAgeingSummary = "WipAgeingSummary",  //21
  ServiceGuageMonth = "ServiceGuageMonth",  //22
  ServiceGuageWTD = "ServiceGuageWTD",  //23
  ServiceGuageYesterday = "ServiceGuageYesterday",  //24
  PartsGuageMonth = "PartsGuageMonth",  //25
  PartsGuageWTD = "PartsGuageWTD",  //26
  PartsGuageYesterday = "PartsGuageYesterday",  //27
  EvhcDoneVsWips = "EvhcDoneVsWips", //RRG use this  //28
  EvhcQuotedAndSold = "EvhcQuotedAndSold", //Vindis use this  //29
  CitNowsVsWips = "CitNowsVsWips",  //30
  PartsStockOver1Yr = "PartsStockOver1Yr",  //31
  PartsStock6To12 = "PartsStock6To12",  //32
  VocNPS = "VocNPS",  //33
  ServiceBookings = "ServiceBookings",  //34
  ServiceBookingsNext5 = "ServiceBookingsNext5",  //35
  AgedWips = "AgedWips",  //36

  //SiteCompare
  SiteCompare = "SiteCompare",  //37
  TodayMap = "TodayMap",  //38


  //New Spain
  InvoicedDonutNew = "InvoicedDonutNew",  //39
  InvoicedDonutUsed = "InvoicedDonutUsed",  //40
  InvoicingPerformanceNew = "InvoicingPerformanceNew",  //41
  InvoicingPerformanceUsed = "InvoicingPerformanceUsed",  //42
  ReconditioningSummary = "ReconditioningSummary",  //43
  FixedAssetVehicles = "FixedAssetVehicles",  //44
  ScrapVehicles = "ScrapVehicles",  //45
  DataOriginsUpdates = "DataOriginsUpdates",  //46
  RatioOrdersNew = "RatioOrdersNew",  //47
  RatioOrdersUsed = "RatioOrdersUsed",  //48
  Alcopas = "Alcopas", // 49
  Commissions = "Commissions", // 50
  UsedStockMerchandisingBySite = "UsedStockMerchandisingBySite", //51
  UsedDelivered = "UsedDelivered"  //52


}

export enum DashboardTimePeriod {
  Today = "Today", 
  Yesterday = "Yesterday", 
  Month = "Month", 
  Week = "Week"
}


export interface DashboardDataParams {
  WeekStart?: Date | string;
  WeekStartActivitiesTile?: Date;
  WeekStartOrdersTileNew?: Date;
  WeekStartOrdersTileUsed?: Date;
  WeekStartOrdersTileFleet?: Date;
  SiteIds: string;
  DataItems: DashboardDataItem[];
  Department: string;
  IsMargin?: boolean;
  TimePeriod?: DashboardTimePeriod;
}

export interface DashboardDataSpainOverviewParams {
  SiteIds: string;
  MonthStart?: Date;
  OrderTypeTypesNew: string[];
  OrderTypeTypesUsed: string[];
  FranchiseCodes: string[];
  WeekStart: Date;
}

export interface DashboardDataSpainAftersalesParams {
  SiteIds: string;
  MonthStart?: Date;
}

export interface SiteCompareRow {
  SiteId: number;
  SiteDescription: string;
  Value: number;
  GeoX: number | null;
  GeoY: number | null;
}


export interface DonutData {
  Department: string;
  ActualUnits: number;
  ActualMargin: number;
  TargetUnits: number;
  TargetMargin: number;
  ActualUnitsLastYear: number;
  ActualUnitsLastMonth: number;
}

export interface DonutMonthlyData {
  SiteId: number;
  FranchiseCode: string;
  Department: string;
  OrderTypeType: string;

  ActualUnits: number;
  ActualMargin: number;

  TargetUnits: number;
  TargetMargin: number;

  ActualUnitsLastYear: number;
  ActualUnitsLastMonth: number;
}


export interface InvoicingPerformance {
  BreakdownByBrand: InvoicingPerformanceBrandData[];
}



export interface InvoicingPerformanceBrandData {
  Brand: string;
  InvoicedProfit: number;
  UnitInvoicingVsTgt: number;
}


export interface ReconditioningSummary {
  TotalCost: number;
  PerUnitCost: number;
}


export interface UnitsAndValue {
  Units: number;
  Value: number;
}

export interface AssignmentVM {
  Units: number;
  Value: number;
  Department: string;
}


export interface DepartmentDealBreakdown {

  TotalNew: number;
  TotalFleet: number;
  TotalUsed: number;

  SiteId: number;
  NewDoneInMonth: number;
  NewBroughtIn: number;
  UsedCoreUsed: number;
  UsedDemo: number;
  UsedExDemo: number;
  UsedExManagement: number;
  UsedTactical: number;
  FleetFleet: number;
  FleetFleetlocal: number;
  FleetFleetSt: number;
  FleetNewAgency: number;
  FleetNewDirect: number;
  FleetNewFleet: number;
  FleetNewLMAgency: number;
  FleetNewNationalAccount: number;
  FleetNewRFOFleet: number;
  FleetNewRFORental: number;
  FleetNewVWFSAgency: number;
  FleetAgencyVWFSAgency: number;
  FleetCorporate: number;
  FleetCommercial: number;

  // AgencyVWFSAgency: string;
  //       NewAgency: string;
  //       NewDirect: string;
  //       NewFleet: string;
  //       NewLMAgency: string;
  //       NewNationalAccount: string;
  //       NewRFOFleet: string;
  //       NewRFORental: string;
  //       NewVWFSAgency: string;
}




export interface FinanceAddOnSummary {
  New: number;
  Used: number;
  Total: number;
}

export interface UsedStockMerchandisingBySite {
  Items: UsedStockMerchandisingBySiteItem[]
  Total: UsedStockMerchandisingBySiteItem
}

export interface UsedStockHealth {
  CoreUsed: number;
  Tactical: number;
  ExDemo: number;
  ExManagement: number;
  Demo: number;
  Total: number;
}

export interface UsedStockHealth {
  CoreUsed: number;
  Tactical: number;
  ExDemo: number;
  ExManagement: number;
  Demo: number;
  Total: number;
}


export interface UsedStockMerchandising {
  Listed: number;
  NotListed_Under7: number;
  NotListed_Under14: number;
  NotListed_Over14: number;
  OnOrder: number;
  Prepped: number;

  OnRRGSiteNotInstock: number;
  NotListedTotal: number;
  ListedBarTotal: number;
  PreppedBarTotal: number;
  NotPrepped: number;

  InStockAwaitingPrep: number;
  SoldAwaitingPrep: number;
  Stock: number;

}




export class DailyNetOrder {

  constructor(x: DailyNetOrder) {
    this.DayDate = new Date(x.DayDate);
    this.OrderCount = x.OrderCount;
  }

  DayDate: Date;
  OrderCount: number;
}

export class DailyNetOrdersCancellationsCount {

  constructor(x: DailyNetOrdersCancellationsCount) {
    this.DayDate = new Date(x.DayDate);
    this.IsToday = new Date(x.DayDate).setHours(0, 0, 0, 0) == new Date().setHours(0, 0, 0, 0);
    this.Orders = x.Orders;
    this.Cancellations = x.Cancellations;
    this.netOrders = x.Orders - x.Cancellations
  }

  DayDate: Date;
  IsToday: boolean;
  Orders: number;
  Cancellations: number;
  netOrders: number;
}


export interface ActivityLevelsSummaryItem {
  Day: Date;
  Enquiries: number;
  TestDrives: number;
  Orders: number;
  Appointments: number;
}



export interface OverdueSummaryItem {
  Ageing: string;
  Count: number;
}


export interface RegistrationsSummary {

  TargetType: string;

  Target: number;

  Registered: number;
  QualifyingOrders: number;
  Achieved: number;

}


export interface OverageStockSummaryItem {
  Type: string;
  Department: string;
  AgedOver: number;
  VehicleCount: number;
}


export interface SpainDailyNetOrderItem {
  DayDate: Date | string;
  OrdersThisYear: number;
  OrdersLastYear: number;

}


export interface ActivityLevelsAndOverdues {
  ActivityLevels: ActivityLevelsSummaryItem[];
  Overdues: OverdueSummaryItem[];
}

export interface DashboardDataPackSpainOverview {
  OrdersDonutSpainNew: RatioOrdersTileData | null;
  OrdersDonutSpainUsed: RatioOrdersTileData | null;
  InvoicedDonutDataNew: DonutMonthlyData | null;
  InvoicedDonutDataUsed: DonutMonthlyData | null;
  InvoicingPerformanceNew: InvoicingPerformance | null;
  InvoicingPerformanceUsed: InvoicingPerformance | null;
  ScrapVehicles: UnitsAndValue | null;
  FixedAssetVehicles: UnitsAndValue | null;
  AssigmentVehicles: UnitsAndValue[] | null;
  ReconditioningSummary: ReconditioningSummary | null;


  //DonutDataSetsMonth: DonutData[] | null;
  OverageStockSummary: OverageStockSummaryItem[] | null;

  SpainDailyNetOrdersUsed: SpainDailyNetOrderItem[];
  SpainDailyNetOrdersNew: SpainDailyNetOrderItem[];

  Commissions: CommissionTileSummary;
  Alcopas: AlcopasSummary;

}

export interface DashboardDataPackSpainAftersales {
  ServiceGuageMonth: DashboardGauge;
  PartsGuageMonth: DashboardGauge;
  AgedWipLines: AgedWipLine[];
  WipSummaries: WipSummary[];
  Percentages: TitleAndPercentage[];
  TechControlAndConvRates: TechControlAndConvRate[];
  Bookings: ServiceBookingsTileSummary[];
}

export interface DashboardDataPackSpainAftersalesDetail {
  Parts: RunChaseDataPoint[];
  Service: RunChaseDataPoint[];
  ServiceGuageMonth: DashboardGauge;
  PartsGuageMonth: DashboardGauge;
  WipSummaries: WipSummary[];
  Percentages: TitleAndPercentage[];
  TechControlAndConvRates: TechControlAndConvRate[];
  TurnoverPerOperative: TurnoverPerOperative;
  Bookings: ServiceBookingsTileSummary[];
  ServiceDetailedMechanical: ServiceDetailedTileRow[];
  ServiceDetailedBodyshop: ServiceDetailedTileRow[];
}


export interface DashboardDataPack {
  DepartmentDealBreakdown: DepartmentDealBreakdown;
  DonutDataSetsMonth: DonutData[];
  DonutDataSetsWTD: DonutData[];
  DonutDataSetsYesterday: DonutData[];

  DepartmentProfitPerUnitsMonth: DepartmentProfitPerUnits | null;
  DepartmentProfitPerUnitsWTD: DepartmentProfitPerUnits | null;
  DepartmentProfitPerUnitsYesterday: DepartmentProfitPerUnits | null;


  DailyNetOrdersNew: DailyNetOrder[];
  DailyNetOrdersFleet: DailyNetOrder[];
  DailyNetOrdersUsed: DailyNetOrder[];
  DailyNetOrdersCancellationsNew: DailyNetOrdersCancellationsCount[];
  DailyNetOrdersCancellationsFleet: DailyNetOrdersCancellationsCount[];
  DailyNetOrdersCancellationsUsed: DailyNetOrdersCancellationsCount[];
  FinanceAddOnSummary: FinanceAddOnSummary;
  RegistrationSummaries: RegistrationsSummary[];
  UsedStockMerchandising: UsedStockMerchandising;
  UsedStockMerchandisingBySite: UsedStockMerchandisingBySite;
  UsedStockHealth: UsedStockHealth;
  OverageStockSummary: OverageStockSummaryItem;
  ActivityLevelsAndOverdues: ActivityLevelsAndOverdues;
  DeliveredInTime: DeliveredInTimeStat;


  //Aftersales
  CitNowVsWips: CitNowVsWips;
  EvhcQuotedAndSold: EvhcQuotedAndSold; // Vindis
  EvhcDoneVsWips: EvhcDoneVsWips;
  ServiceGuageMonth: DashboardGauge;
  ServiceGuageWTD: DashboardGauge;
  ServiceGuageYesterday: DashboardGauge;
  PartsGuageMonth: DashboardGauge;
  PartsGuageWTD: DashboardGauge;
  PartsGuageYesterday: DashboardGauge;
  PartsStockOver1Yr: PartsStockOver1Yr;
  PartsStock6To12: PartsStock6To12;
  AgedWipLines: AgedWipLine[];
  VocSummary: VocSummaryItem[];
  WipSummary: WipSummary;
  Bookings: ServiceBookingsTileSummary[],
  BookingsNext5: ServiceBookingsTileSummary[],
  SiteCompareRows: SiteCompareRow[];
  SiteTodayMapItems: SiteCompareRow[];
  DataOriginsUpdates: DataOriginsUpdate[];
  UsedDelivered: UsedDeliveredRow[];

}

export interface UsedDeliveredRow {
  SiteName: string;
  SiteId: number;
  Sold: number;
  DeliveredPercent: number;
  Undelivered: number;
  UndeliveredAfter5Days: number;
}


export interface ServiceBookingsTileSummary {
  DayDate: Date | string;
  BookedWithinCapacity: number;
  UnbookedCapacity: number;
  BookedOverCapacity: number;
}

export interface ServiceBookingsDetailTileSummary {
  Type: string;
  Label: string;
  Percentage: number;
  Max: number;
}

export interface ServiceDetailedTileRow {
  Label: string;
  Invoiced: number;
  Invested: number;
  Productivity: number;
  RevParts: number;
  MargParts: number;
  Entrances: number;
  MeanBill: number;
  HourlyRate: number;
}

export interface CommissionTileSummary {
  PeopleCount: number;
  TotalCommission: number;
  TotalDeals: number;

  CommissionPerPerson: number;
  CommissionPerUnit: number;

}

export interface AlcopasSummary {
  Yesterday: number;
  ThisMonth: number;
}


export interface RatioOrdersTileData {
  ThisMonth: number;
  LastMonth: number;
  ThisMonthLY: number;
  ThisMonthTgt: number;
  MonthVsLastMonth: number;
  MonthVsLastYr: number;

}

export interface DepartmentProfitPerUnits {
  SiteId: number;
  NewChassis: number;
  NewFinance: number;
  NewAddOn: number;
  UsedChassis: number;
  UsedFinance: number;
  UsedAddOn: number;
  NewChassisPU: number;
  NewFinancePU: number;
  NewAddOnPU: number;
  UsedChassisPU: number;
  UsedFinancePU: number;
  UsedAddOnPU: number;

}


export interface CitNowVsWips {
  CitNowCount: number;
  WipCount: number;
  Percentage: number;
}

export interface InvoicedDonut {
  ActualUnits: number;
  TargetUnits: number;
  ActualUnitsVsLastMonth: number;
  ActualUnitsVsLastYear: number;
}

export interface EvhcQuotedAndSold {
  EvhcCount: number;
  Quoted: number;
  QuotedPerEvhc: number;
  SoldPerEvhc: number;
  Converted: number;
  Sold: number;
}

export interface EvhcDoneVsWips {

  EvhcCount: number;
  WipCount: number;
  Percentage: number;

}

export interface DashboardGauge {
  ThisMonthTarget: number;

  LastMonthTarget: number;

  DoneInTimePeriod: number;

  TargetToDate: number;

  Vs: number;

  DaysElapsed: number;
  DaysTotal: number;

}

export interface PercentageData {
  Title: number;
  Percentage: number;
}


export interface PartsStockOver1Yr {
  TotalStockValue: number;
  StockValueOver1Yr: number;
  Percentage: number;
}


export interface PartsStock6To12 {
  TotalStockValue: number;
  StockValue6To12: number;
  Percentage: number;
}



export interface AgedWipLine {
  Customer: string;

  Reg: string;

  Amount: number;

  DaysOverDue: number;
  IsBad: boolean;


}



export interface VocSummaryItem {
  YearMonth: Date | string;
  SitesScore: number;
  NationalAverage: number;

}


export interface WipSummary {
  LessThanThirtyDays: number;
  ThirtyToSixtyDays: number;
  GreaterThanSixtyDays: number;
}

export interface TodayMapPlot extends SiteCompareRow {
  mapMarker?: any;
};

export interface DataOriginsUpdate {
  Id: number;
  DataOrigin: string;
  DataTable: string;
  UpdateType: string;
  UpdateTiming: string;
  LastUpdate: Date;
}

export interface TitleAndPercentage {
  Percentage: number;
  Title: string;
}

export interface TechControlAndConvRate {
  Numerator: number;
  NumeratorLabel: string;
  Denominator: number;
  DenominatorLabel: number;
  Percentage: number;
  PercentageLabel: string;
  Title: string;
}
