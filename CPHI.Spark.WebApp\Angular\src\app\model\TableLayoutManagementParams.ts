import { EventEmitter } from "@angular/core";
import { ColumnApi, ColumnState, GridApi } from "ag-grid-community";
import { AvailableTableStateDetails } from "./AvailableTableStateDetails";
import { UserSimple } from "./main.model";
import { SelectedTableState } from "./SelectedTableState";
import { SharedTableStateDetails } from "./SharedTableStateDetails";
import { StandardTableStateDetails } from "./StandardTableStateDetails";
import { TableStateDetails } from "./TableStateDetails";
import { CPHAutoPriceColDef } from "./CPHColDef";
import { SparkTableStateDetails } from "./SparkTableStateDetails";

export class TableLayoutManagementParams {

    constructor(pageName: string) {
        this.pageName = pageName;
        this.ownTableStates = null;
        this.standardTableStates = null;
        this.sharedTableStates = null;
        this.availableTableStates = null;
        this.selectedTableState = null;
        this.sparkTableStates = null;
        this.loadedTableState = null;
        this.usersToShareWith = null;
        this.gridApi = null;
        this.gridColumnApi = null;
        this.filterModel = null;
    }
    pageName: string;
    ownTableStates: TableStateDetails[];
    sparkTableStates: SparkTableStateDetails[];
    standardTableStates: StandardTableStateDetails[];
    sharedTableStates: SharedTableStateDetails[];
    availableTableStates: AvailableTableStateDetails[];
    selectedTableState: SelectedTableState;
    loadedTableState: ColumnState[];
    usersToShareWith: UserSimple[];
    gridApi: GridApi;
    gridColumnApi: ColumnApi;
    filterModel: any;
    isRenault?: boolean;
    refreshReportsListEmitter?: EventEmitter<void>;
    lastTableState?: any;
    updateColumnsForTypeaheadEmitter?: EventEmitter<void>;
    autoSize?: boolean;
    originalColDefs:CPHAutoPriceColDef[]
}
