import { LocationAndStrategyPrice } from "./LocationAndStrategyPrice.model";
import { ValuationPriceSet } from "./ValuationPriceSet.model";
import { VehicleSpecOption } from "./VehicleSpecOption.model";
import { ExtraPrepItem } from "./ExtraPrepItem";
import { ValuationCostingDTO } from "../components/autoPriceValuationModal/valuationCosting/ValuationCostingDTO";
import { CompetitorSummary } from "./CompetitorSummary";

export interface ValuationResultForNewVehicleToSave {
    ValuationId:number|null;
    VehicleReg: string;
    FirstRegistered: Date;
    Mileage: number;
    Condition: string;
    DerivativeId: string;
    RetailRating: number;
    //AverageSpecPrices: ValuationPriceSet;
    Valuation: ValuationPriceSet;
    VehicleSpecOptions: VehicleSpecOption[];
    SitesStrategy: LocationAndStrategyPrice[];
    ExtraPrepRows: ExtraPrepItem[];
    PrepCost: number;
    Costing: ValuationCostingDTO;
    //CompetitorSummary: CompetitorSummary;
}