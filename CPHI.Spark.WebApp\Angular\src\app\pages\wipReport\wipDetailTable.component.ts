//core angular
import { Component, OnInit, HostListener } from '@angular/core';
//model and cell renderers
import { WipAgeingComparator, WipDetailRow } from '../../model/main.model';
//services
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
//Angular things, non-standard
import { ExcelExportService } from '../../services/excelExportService';
import { localeEs } from 'src/environments/locale.es.js';
import { Subscription } from 'rxjs';
import { GridApi, IFilterComp, IRowModel } from 'ag-grid-community';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

@Component({
  selector: 'wipsDetailTable',
  template: `
  <div id="wipDetailContainer">
    <div id="topArea">
        <button class="btn btn-primary backButton text-nowrap class mr-4" (click)="selections.wipReport.showDetail = false">
            <i class="fa fa-undo"></i>
        </button>
        <div id="buttonsArea">
            <!-- For choosing ageing time period -->
            <div class="buttonGroup filterChoiceArea">
                <button class="btn btn-primary allButton" (click)="filterwipAgeing()">
                    <div class="buttonContent">
                        <div>{{ constants.translatedText.Common_AllAges }}</div>
                    </div>
                </button>
                <button 
                    *ngFor="let ageing of selections.wipsTable.ageings"
                    class="btn btn-primary"
                    [ngClass]="{ 'active': ageing.label == selections.wipsTable.buttonSelections.ageing?.label }"
                    (click)="filterwipAgeing(ageing)"
                >
                    <div class="buttonContent wideButton">
                        <div>{{ ageing.label }}</div>
                        <div>{{ ageing.value | cph:'currency':0 }}</div>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <div id="gridHolder">
        <div id="excelExport" (click)="excelExport()">
            <img [src]="constants.provideExcelLogo()">
        </div>
        
        <!-- Top Total -->
        <div id="topTotal">
            {{ constants.translatedText.Common_TotalOf }} {{ constants.pluralise(selections.wipsTable.totalWipCount, 'WIP', 'WIPs') }}, 
            {{ constants.translatedText.Common_TotalValue }}: {{ selections.wipsTable.totalWipBalance | cph:'currency':0 }}
        </div>
        
        <!-- The Grid Itself -->
        <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions"> </ag-grid-angular>



        <!-- Bottom total for selected rows -->
        <div *ngIf="haveSelectedCells" id="bottomTotal" class="animated slideInRight">
            Selected {{ constants.pluralise(selections.wipsTable.selectedWipCount, 'wip', 'wips') }}, 
            total value: {{ selections.wipsTable.selectedWipBalance | cph:'currency':0 }}
        </div>

        <!--Hint-->
        <hint id="hint" header="Debts table" content="debts"></hint>
    </div>
</div>
    `
  ,
  styles: [
    `
    
#topArea {
	width: 100%;
	display: flex;
	justify-content: space-between;
}
#buttonsArea {
	display: flex;
	justify-content: space-around;
	width: 100%;
	margin: 0 auto;
}
#buttonsArea .btn {
	padding: .2em .6em;
    min-width: 6em;
}
.filterChoiceArea .buttonContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    min-height: 3em;
    justify-content: center;
}
#topTotal {
	position: absolute;
	top: -2em;
	right: 0;
	font-weight: 700;
	
	display: inline-block;
	height: 2em;
	background: var(--brightColourLight);
	border-radius: .2em;
	padding: .5em 1em;
	display: flex;
	align-items: center;
}
#bottomTotal {
	animation-duration: .2s;
	position: absolute;
	bottom: 0;
	right: 0;
	font-weight: 700;
	
	display: inline-block;
	height: 2em;
	background: var(--brightColourLight);
	border-radius: .2em;
	padding: .5em 1em;
	display: flex;
	align-items: center;
}
#hint {
	position: absolute;
	top: -2em;
	left: 0;
}
#gridHolder {
	height: 100%;
	margin-top: 2em;
}
ag-grid-angular {
	width: 100%;
	height: 100%;
}
@media (min-width:0px) and (max-width:1920px) {
	#buttonsArea .btn {
		min-width: 5.5em;
	}
}
#wipDetailContainer {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
  `
  ],
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
})



export class WipDetailTableComponent implements OnInit {
    @HostListener("window:resize", [])

    private onresize(event) {
        this.selections.screenWidth = window.innerWidth;
        this.selections.screenHeight = window.innerHeight;
        if (this.gridApi) {
        this.gridApi.resetRowHeights();
        this.resizeGrid();
        }
    }

    public gridApi: GridApi;
    mainTableGridOptions: any;
    haveSelectedCells: boolean;
    subscription: Subscription;
    ageings: WipAgeingComparator[];

    constructor(
        public constants: ConstantsService,
        public selections: SelectionsService,
        public cphPipe: CphPipe,
        public excel: ExcelExportService,
        public columnTypeService: ColumnTypesService,
        public gridHelpersService: AGGridMethodsService
    ) { }

    ngOnInit() {
        this.calculateWipsSummary();
        this.summariseWips(this.selections.wipReport.wipsRowsFiltered);
        this.setGridOptions();

        this.subscription = this.selections.wipReport.wipsChangedEmitter.subscribe(() => {
            this.gridApi.setRowData(this.selections.wipReport.wipsRowsFiltered);
            this.summariseWips(this.selections.wipReport.wipsRowsFiltered);
        })
    }

    calculateWipsSummary() {
        let ageings: WipAgeingComparator[] = [
            { label: this.constants.translatedText.Common_Current, field: 'Under1Day', value: 0 },
            { label: '1-7 ' + this.constants.translatedText.Common_DaysLower, field: '1To7Days', value: 0 },
            { label: '8-20 ' + this.constants.translatedText.Common_DaysLower, field: '8To20Days', value: 0 },
            { label: '21-30 ' + this.constants.translatedText.Common_DaysLower, field: '21To30Days', value: 0 },
            { label: '31-60 ' + this.constants.translatedText.Common_DaysLower, field: '31To60Days', value: 0 },
            { label: this.constants.translatedText.Common_Over + ' 60 ' + this.constants.translatedText.Common_DaysLower, field: 'Over60Days', value: 0 }
        ]
        
        this.selections.wipsTable = {
            totalWipBalance: 0,
            totalWipCount: 0,
            selectedWipBalance: 0,
            selectedWipCount: 0,
            buttonSelections: {
                ageing: null,
            },
            gridSelections: {
                filterState: null,
                sortState: null,
            },
            ageings: ageings,
            departments: []
        }    
    }

    summariseWips(wips: WipDetailRow[]) {
        //totalwip
        this.selections.wipsTable.totalWipBalance = this.constants.sum(wips.map(x => x.Total));
        this.selections.wipsTable.totalWipCount = wips.length;

        this.selections.wipsTable.ageings.forEach(ageing => {
            let ageingwips: WipDetailRow[] = wips.filter(x => x.Ageing == ageing.field);
            ageing.value = this.constants.sum(ageingwips.map(x => x.Total));
        });
    }

    setGridOptions() {
        this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
            getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
            getRowHeight: (params) => {
                let normalHeight = Math.min(21, Math.max(15, Math.round(25 * this.selections.screenHeight / 960)));
                if (params.node.rowPinned) {
                    return this.gridHelpersService.getRowPinnedHeight();
                  } else {
                    return this.gridHelpersService.getStandardHeight();
                  }
            },
            onGridReady: (params) => this.onGridReady(params),
            onFilterChanged: (params) => this.onFilterChanged(),
            onSelectionChanged: (params) => this.onSelectionChanged(),
            rowSelection: "multiple",
            rowDeselection: true,
            rowData: this.selections.wipReport.wipsRowsFiltered,
            floatingFilter: true,
            defaultColDef: {
                resizable: true,
                sortable: true,
                filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
                autoHeight: true
            },
            columnTypes: {
                ...this.columnTypeService.provideColTypes([]),
            },
            columnDefs: [
                { headerName: this.constants.translatedText.Common_Site, field: 'SiteDescription', colId: 'siteDescription', width: 100, type: 'label' },
                { headerName: this.constants.translatedText.Dashboard_WipReport_BookingStatus, field: 'BookingStatus', colId: 'BookingStatus', width: 60, type: 'label', hide: this.constants.environment.wipsTable.hideBookingColumn },
                { headerName: this.constants.translatedText.Dashboard_WipReport_Department, field: 'DepartmentName', colId: 'DepartmentName', width: 60, type: 'label', hide: this.constants.environment.wipsTable.hideDepartmentColumn },
                { headerName: 'F', field: 'FranchiseCode', colId: 'FranchiseCode', width: 30, type: 'label' },
                { headerName: this.constants.translatedText.Dashboard_WipReport_WipNumber, field: 'LineCode', colId: 'LineCode', width: 60, type: 'label' },
                { headerName: this.constants.translatedText.Dashboard_WipReport_Account, field: 'Account', colId: 'Account', width: 60, type: 'label', hide: this.constants.environment.wipsTable.hideAccountColumn },
                { headerName: this.constants.translatedText.Common_Customer, field: 'Customer', colId: 'Customer', width: 180, type: 'label' },
                { headerName: 'Reg', field: 'Reg', colId: 'Reg', width: 80, type: 'label' },
                { headerName: this.constants.translatedText.Dashboard_WipReport_DueIn, field: 'DueDateIn', colId: 'DueDateIn', width: 70, type: 'dateLongYear' },
                { headerName: 'Due Out', field: 'DueDateOut', colId: 'DueDateOut', width: 70, type: 'dateLongYear', hide: this.constants.environment.wipsTable.hideDueDateOutColumn },
                { headerName: 'WL In', field: 'WDateIn', colId: 'WDateIn', width: 70, type: 'dateLongYear', hide: this.constants.environment.wipsTable.hideWDateInColumn },
                { headerName: 'WL Out', field: 'WDateOut', colId: 'WDateOut', width: 70, type: 'dateLongYear', hide: this.constants.environment.wipsTable.hideWDateOutColumn },
                { headerName: 'Parts', field: 'Parts', colId: 'Parts', width: 70, type: 'currency', hide: this.constants.environment.wipsTable.hidePartsColumn },
                { headerName: 'Oil', field: 'Oil', colId: 'Oil', width: 70, type: 'currency', hide: this.constants.environment.wipsTable.hideOilColumn },
                { headerName: 'Labour', field: 'Labour', colId: 'Labour', width: 70, type: 'currency', hide: this.constants.environment.wipsTable.hideLabourColumn },
                { headerName: 'Sublet', field: 'Sublet', colId: 'Sublet', width: 70, type: 'currency', hide: this.constants.environment.wipsTable.hideSubletColumn },
                { headerName: this.constants.translatedText.Common_Total, field: 'Total', colId: 'Total', width: 70, type: 'currency' },
                { headerName: this.constants.translatedText.Dashboard_WipReport_Provision, field: 'Provision', colId: 'Provision', width: 70, type: 'currency', hide: this.constants.environment.wipsTable.hideProvisionColumn },
                { headerName: this.constants.translatedText.Dashboard_WipReport_Created, field: 'Created', colId: 'Created', width: 70, type: 'dateLongYear', hide: this.constants.environment.wipsTable.hideCreatedColumn },
                { headerName: this.constants.translatedText.Dashboard_WipReport_Age, field: 'Age', colId: 'Age', width: 45, type: 'special', headerTooltip: 'Days since Date Due In' },
                { headerName: this.constants.translatedText.Dashboard_WipReport_Ageing, field: 'Ageing', colId: 'Ageing', width: 100, type: 'label', valueGetter: (params) => this.formatAgeingColumn(params.data.Ageing) },
                { headerName: this.constants.translatedText.Dashboard_WipReport_Notes, field: 'Notes', colId: 'Notes', width: 100, type: 'label', hide: this.constants.environment.wipsTable.hideNotesColumn },
            ]
        }
    }

    formatAgeingColumn(ageing: string) {
        return this.selections.wipsTable.ageings.find(x => x.field == ageing).label;
    }

    onFilterChanged() {
        //store the filter state
        this.selections.wipsTable.gridSelections.filterState = this.gridApi.getFilterModel();
        //run the summary numbers
        let model: IRowModel = this.gridApi.getModel();
        let deals = model['rowsToDisplay'].map(x => x.data);
        this.summariseWips(deals);
    }

    filterwipAgeing(ageing?: WipAgeingComparator) {
        let filterComponent: IFilterComp = this.gridApi.getFilterInstance('Ageing');
        if (!ageing) {
            filterComponent.setModel(null);
            this.gridApi.onFilterChanged();
            this.summariseWips(this.selections.wipReport.wipsRowsFiltered);
            this.selections.wipsTable.buttonSelections.ageing = null;
            return;
        }

        filterComponent.setModel({
            type: "contains",
            filter: ageing.label
        })

        this.selections.wipsTable.buttonSelections.ageing = ageing;
        this.gridApi.onFilterChanged();
    }

    resizeGrid() {
        if (this.gridApi) this.gridApi.sizeColumnsToFit();
    }

    onGridReady(params) {
        this.gridApi = params.api;
        this.gridApi.sizeColumnsToFit();
        this.mainTableGridOptions.context = { thisComponent: this };
    }
  
    onSelectionChanged() {
        let selectedRows: any[] = this.gridApi.getSelectedRows();
        this.haveSelectedCells = selectedRows.length > 0 ? true : false;
        this.selections.wipsTable.selectedWipCount = selectedRows.length;
        this.selections.wipsTable.selectedWipBalance = this.constants.sum(selectedRows.map(x => x.Total));
    }

    excelExport() {
        let tableModel: IRowModel = this.gridApi.getModel()
        this.excel.createSheetObject(tableModel, 'WIPs', 1.5);
    }
}
