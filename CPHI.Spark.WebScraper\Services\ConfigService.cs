﻿
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Repository;
using Microsoft.Extensions.Configuration;
using System;

namespace CPHI.Spark.WebScraper
{
   public static class ConfigService
   {

      //------------------------------------
      // Connection Strings
      //------------------------------------
      public static string RRGUKConnectionString { get; set; }
      public static string VindisConnectionString { get; set; }
      public static string RRGSpainConnectionString { get; set; }
      public static string AutoPriceConnectionString { get; set; }
      public static string SytnerConnectionString { get; set; }


      //public static ConfigValues config;
      private static IConfigurationBuilder configurationBuilder;

      public static string FileDestination { get; set; }
      public static string FileDestinationProcessed { get; set; }
      //public static string FileDestinationDev { get; set; }
      // public static string TriggerJobSenders { get; set; }
      // public static string MailIn { get; set; }
      // public static string MailArchive { get; set; }
      //public static string MailUser { get; set; }
      // public static string MailPwd { get; set; }
      public static string ServiceName { get; set; }

      public static string MailAppTenantId { get; set; }
      public static string MailAppId { get; set; }
      //public static string MailSecret { get; set; }
      public static string MailSecretValue { get; set; }
      public static string MailAccount { get; set; }


      public static string FileDownloadLocation { get; set; }
      public static string ForceRunThisJobNowOnly { get; set; }


      public static string EnquiryMAXPassword { get; set; }
      public static string SendEmailsTo { get; set; }

      //RRG specific
      public static string RRGSiteStockSchedule { get; set; }
      public static string RRGVocSchedule { get; set; }
      public static string RRGAutomatedUserTestSchedule { get; set; }
      public static string CheckForEmailRequestSchedule { get; set; }

      //Vindis specific
      public static string AuditScrapePassword { get; set; }
      public static string VindisDealsIntradaySchedule { get; set; }
      public static string VindisDealsDailySchedule { get; set; }
      public static string VindisActivitySchedule { get; set; }
      public static string VindisGDPRSchedule { get; set; }
      public static string EnquiriesScrapeJob { get; set; }
      public static string VindisAuditSchedule { get; set; }
      public static string VindisModixSchedule { get; set; }

      //Spain specific
      public static string AlcopaPassword { get; set; }
      public static string OrdersMadridPassword { get; set; }
      public static string OrdersValenciaPassword { get; set; }
      public static string SpainOrdersSchedule { get; set; }
      public static string SpainAlcopaSchedule { get; set; }
      public static string SpainEserpubliSchedule { get; set; }

      // NetDirector
      public static string NetDirectorPassword { get; set; }
      public static string NetDirectorSchedule { get; set; }

      // ClickDealer
      public static string ClickDealerPassword { get; set; }
      public static string ClickDealerSchedule { get; set; }
      public static string ClickDealerAutoTraderFee { get; set; }

      public static string DistrinetSchedule { get; set; }

      public static string FtpHostname { get; set; }
      public static string FtpUsername { get; set; }
      public static string FtpPassword { get; set; }


      public static string ModixPassword { get; set; }

      public static string BrindleyPinewoodSchedule { get; set; }
      public static string MantlesPinewoodSchedule { get; set; }


      public static string AutotraderApiKey { get; set; }
      public static string AutotraderApiSecret { get; set; }
      public static string AutotraderBaseURL { get; set; }

      static ConfigService()
      {

         var builder = new ConfigurationBuilder().AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
         configurationBuilder = builder;

         //TriggerJobSenders = Get("AppSettings", "triggerJobSenders");
         // MailIn = Get("AppSettings", "mailIn");
         //MailArchive = Get("AppSettings", "mailArchive");
         // MailUser = Get("AppSettings", "mailUser");
         // MailPwd = Get("AppSettings", "mailPwd");
         ServiceName = Get("AppSettings", "serviceName");

         MailAppTenantId = Get("EmailSettings", "mailAppTenantId");
         MailAppId = Get("EmailSettings", "mailAppId");
         //  MailSecret = Get("EmailSettings", "mailSecret");
         MailSecretValue = Get("EmailSettings", "mailSecretValue");
         MailAccount = Get("EmailSettings", "mailAccount");


         EnquiryMAXPassword = Get("AppSettings", "enquiryMAXPassword");

         FileDestination = Get("AppSettings", "fileDestination");
         FileDestinationProcessed = Get("AppSettings", "fileDestinationProcessed");
         //FileDestinationDev = Get("AppSettings", "fileDestinationDev");
         FileDownloadLocation = Get("AppSettings", "fileDownloadLocation");

         ForceRunThisJobNowOnly = Get("AppSettings", "forceRunThisJobNowOnly");
         SendEmailsTo = Get("AppSettings", "sendEmailsTo");

         //RRG schedules
         RRGSiteStockSchedule = Get("AppSettings", "RRGSiteStockSchedule");
         RRGVocSchedule = Get("AppSettings", "RRGVocSchedule");
         RRGAutomatedUserTestSchedule = Get("AppSettings", "AutomatedUserTestSchedule");
         CheckForEmailRequestSchedule = Get("AppSettings", "CheckForEmailRequestSchedule");

         //Vindis schedules
         AuditScrapePassword = Get("AppSettings", "auditScrapePassword");
         VindisDealsIntradaySchedule = Get("AppSettings", "VindisDealsIntradaySchedule");
         VindisDealsDailySchedule = Get("AppSettings", "VindisDealsDailySchedule");
         VindisActivitySchedule = Get("AppSettings", "VindisActivitySchedule");
         VindisGDPRSchedule = Get("AppSettings", "VindisGDPRSchedule");
         EnquiriesScrapeJob = Get("AppSettings", "EnquiriesScrapeJob");
         VindisAuditSchedule = Get("AppSettings", "VindisAuditSchedule");

         //Spain specific
         AlcopaPassword = Get("AppSettings", "AlcopaPassword");
         OrdersMadridPassword = Get("AppSettings", "OrdersMadridPassword");
         OrdersValenciaPassword = Get("AppSettings", "OrdersValenciaPassword");
         SpainOrdersSchedule = Get("AppSettings", "SpainOrdersSchedule");
         SpainAlcopaSchedule = Get("AppSettings", "SpainAlcopaSchedule");
         SpainEserpubliSchedule = Get("AppSettings", "SpainEserpubliSchedule");

         // Brindley NetDirector
         NetDirectorPassword = Get("AppSettings", "NetDirectorPassword");
         NetDirectorSchedule = Get("AppSettings", "NetDirectorSchedule");

         DistrinetSchedule = Get("AppSettings", "DistrinetSchedule");
         BrindleyPinewoodSchedule = Get("AppSettings", "BrindleyPinewoodSchedule");
         MantlesPinewoodSchedule = Get("AppSettings", "MantlesPinewoodSchedule");

         FtpHostname = Get("AppSettings", "ftpHostname");
         FtpUsername = Get("AppSettings", "ftpUsername");
         FtpPassword = Get("AppSettings", "ftpPassword");

         RRGUKConnectionString = Get("ConnectionStrings", "RRGUK");
         VindisConnectionString = Get("ConnectionStrings", "Vindis");
         RRGSpainConnectionString = Get("ConnectionStrings", "RRGSpain");
         AutoPriceConnectionString = Get("ConnectionStrings", "AutoPrice");
         SytnerConnectionString = Get("ConnectionStrings", "Sytner");

         //Vindis schedules
         AuditScrapePassword = Get("AppSettings", "auditScrapePassword");
         VindisDealsIntradaySchedule = Get("AppSettings", "VindisDealsIntradaySchedule");
         VindisDealsDailySchedule = Get("AppSettings", "VindisDealsDailySchedule");
         VindisActivitySchedule = Get("AppSettings", "VindisActivitySchedule");
         VindisGDPRSchedule = Get("AppSettings", "VindisGDPRSchedule");
         EnquiriesScrapeJob = Get("AppSettings", "EnquiriesScrapeJob");
         VindisAuditSchedule = Get("AppSettings", "VindisAuditSchedule");
         VindisModixSchedule = Get("AppSettings", "VindisModixSchedule");

         // ClickDealer settings
         ClickDealerSchedule = Get("AppSettings", "ClickDealerSchedule");
         ClickDealerPassword = Get("AppSettings", "ClickDealerPassword");
         ClickDealerAutoTraderFee = Get("AppSettings", "ClickDealerAutoTraderFee");

         AutotraderApiKey = Get("AutotraderSettings", "ApiKey");
         AutotraderApiSecret = Get("AutotraderSettings", "ApiSecret");
         AutotraderBaseURL = Get("AutotraderSettings", "BaseURL");

         ModixPassword = Get("AppSettings", "ModixPassword");

      }

      private static string Get(string sectionName, string propertyName)
      {
         return configurationBuilder.Build().GetSection(sectionName)[propertyName];
      }

      public static string GetConnectionString(DealerGroupName dealerGroup)
      {
         string dbConnectionName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         switch (dbConnectionName)
         {
            case "RRGUKConnection": return ConfigService.RRGUKConnectionString;
            case "VindisConnection": return ConfigService.VindisConnectionString;
            case "RRGSpainConnection": return ConfigService.RRGSpainConnectionString;
            case "AutoPriceConnection": return ConfigService.AutoPriceConnectionString;
            case "DefaultConnection": return ConfigService.AutoPriceConnectionString;
            case "SytnerConnection": return ConfigService.SytnerConnectionString;
            default:
               {
                  throw new Exception("Unknown dbConnectionName");
               }
         }
      }

   }
}

