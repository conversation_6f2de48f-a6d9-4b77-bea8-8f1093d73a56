﻿
using CPHI.Spark.Model;
using CPHI.Spark.Repository;
using Microsoft.Extensions.Configuration;
using System;

namespace CPHI.Spark.WebScraper
{
    public static class ConfigService
    {

        //------------------------------------
        // Connection Strings
        //------------------------------------
        public static string RRGUKConnectionString { get; set; }
        public static string VindisConnectionString { get; set; }
        public static string RRGSpainConnectionString { get; set; }
        public static string AutoPriceConnectionString { get; set; }


        //public static ConfigValues config;
        private static IConfigurationBuilder configurationBuilder;

        public static string FileDestination { get; set; }
        public static string FileDestinationProcessed { get; set; }
        //public static string FileDestinationDev { get; set; }
       // public static string TriggerJobSenders { get; set; }
       // public static string MailIn { get; set; }
       // public static string MailArchive { get; set; }
        //public static string MailUser { get; set; }
       // public static string MailPwd { get; set; }
        public static string ServiceName { get; set; }

        public static string MailAppTenantId { get; set; }
        public static string MailAppId { get; set; }
        //public static string MailSecret { get; set; }
        public static string MailSecretValue { get; set; }
        public static string MailAccount { get; set; }


        public static string FileDownloadLocation { get; set; }
        public static string ForceRunThisJobNowOnly { get; set; }
        

        public static string EnquiryMAXPassword { get; set; }
        public static string SendEmailsTo { get; set; }

        //RRG specific
        public static string RRGSiteStockSchedule { get; set; }
        public static string RRGVocSchedule { get; set; }
        public static string RRGAutomatedUserTestSchedule { get; set; }
        public static string CheckForEmailRequestSchedule { get; set; }

        //Vindis specific
        public static string AuditScrapePassword { get; set; }
        public static string VindisDealsIntradaySchedule { get; set; }
        public static string VindisDealsDailySchedule { get; set; }
        public static string VindisActivitySchedule { get; set; }
        public static string VindisGDPRSchedule { get; set; }
        public static string EnquiriesScrapeJob { get; set; }
        public static string VindisAuditSchedule { get; set; }
        public static string VindisModixSchedule { get; set; }

        //Spain specific
        public static string AlcopaPassword { get; set; }
        public static string OrdersMadridPassword { get; set; }
        public static string OrdersValenciaPassword { get; set; }
        public static string SpainOrdersSchedule { get; set; }
        public static string SpainAlcopaSchedule { get; set; }
        public static string SpainEserpubliSchedule { get; set; }

        public static string DistrinetSchedule { get; set; }
        public static string PinewoodSchedule { get; set; }

        public static string FtpHostname { get; set; }
        public static string FtpUsername { get; set; }
        public static string FtpPassword { get; set; }


        public static string ModixPassword { get; set; }

        static ConfigService()
        {

            var builder = new ConfigurationBuilder().AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
            configurationBuilder = builder;

            //TriggerJobSenders = Get("AppSettings", "triggerJobSenders");
           // MailIn = Get("AppSettings", "mailIn");
            //MailArchive = Get("AppSettings", "mailArchive");
           // MailUser = Get("AppSettings", "mailUser");
           // MailPwd = Get("AppSettings", "mailPwd");
            ServiceName = Get("AppSettings", "serviceName");

            MailAppTenantId = Get("EmailSettings", "mailAppTenantId");
            MailAppId = Get("EmailSettings", "mailAppId");
          //  MailSecret = Get("EmailSettings", "mailSecret");
            MailSecretValue = Get("EmailSettings", "mailSecretValue");
            MailAccount = Get("EmailSettings", "mailAccount");


            EnquiryMAXPassword = Get("AppSettings", "enquiryMAXPassword");
            ModixPassword = Get("AppSettings", "modixPassword");

            FileDestination = Get("AppSettings", "fileDestination");
            FileDestinationProcessed = Get("AppSettings", "fileDestinationProcessed");
            //FileDestinationDev = Get("AppSettings", "fileDestinationDev");
            FileDownloadLocation = Get("AppSettings", "fileDownloadLocation");

            ForceRunThisJobNowOnly = Get("AppSettings", "forceRunThisJobNowOnly");
            SendEmailsTo = Get("AppSettings", "sendEmailsTo");

            //RRG schedules
            RRGSiteStockSchedule = Get("AppSettings", "RRGSiteStockSchedule");
            RRGVocSchedule = Get("AppSettings", "RRGVocSchedule");
            RRGAutomatedUserTestSchedule = Get("AppSettings", "AutomatedUserTestSchedule");
            CheckForEmailRequestSchedule = Get("AppSettings", "CheckForEmailRequestSchedule");

            //Vindis schedules
            AuditScrapePassword = Get("AppSettings", "auditScrapePassword");
            VindisDealsIntradaySchedule = Get("AppSettings", "VindisDealsIntradaySchedule");
            VindisDealsDailySchedule = Get("AppSettings", "VindisDealsDailySchedule");
            VindisActivitySchedule = Get("AppSettings", "VindisActivitySchedule");
            VindisGDPRSchedule = Get("AppSettings", "VindisGDPRSchedule");
            EnquiriesScrapeJob = Get("AppSettings", "EnquiriesScrapeJob");
            VindisAuditSchedule = Get("AppSettings", "VindisAuditSchedule");
            VindisModixSchedule = Get("AppSettings", "VindisModixSchedule");

            //Spain specific
            AlcopaPassword = Get("AppSettings", "AlcopaPassword");
            OrdersMadridPassword = Get("AppSettings", "OrdersMadridPassword");
            OrdersValenciaPassword = Get("AppSettings", "OrdersValenciaPassword");
            SpainOrdersSchedule = Get("AppSettings", "SpainOrdersSchedule");
            SpainAlcopaSchedule = Get("AppSettings", "SpainAlcopaSchedule");
            SpainEserpubliSchedule = Get("AppSettings", "SpainEserpubliSchedule");

            DistrinetSchedule = Get("AppSettings", "DistrinetSchedule");
            PinewoodSchedule = Get("AppSettings", "PinewoodSchedule");

            FtpHostname = Get("AppSettings", "ftpHostname");
            FtpUsername = Get("AppSettings", "ftpUsername");
            FtpPassword = Get("AppSettings", "ftpPassword");

            RRGUKConnectionString = Get("ConnectionStrings", "RRGUK");
            VindisConnectionString = Get("ConnectionStrings", "Vindis");
            RRGSpainConnectionString = Get("ConnectionStrings", "RRGSpain");
            AutoPriceConnectionString = Get("ConnectionStrings", "AutoPrice");

        }

        private static string Get(string sectionName, string propertyName)
        {
            return configurationBuilder.Build().GetSection(sectionName)[propertyName];
        }

        public static string GetConnectionString(DealerGroupName dealerGroup)
        {
            string dbConnectionName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            switch (dbConnectionName)
            {
                case "RRGUKConnection": return ConfigService.RRGUKConnectionString;
                case "VindisConnection": return ConfigService.VindisConnectionString;
                case "RRGSpainConnection": return ConfigService.RRGSpainConnectionString;
                case "AutoPriceConnection": return ConfigService.AutoPriceConnectionString;
                default:
                    {
                        throw new Exception("Unknown dbConnectionName");
                    }
            }
        }


    }
}

