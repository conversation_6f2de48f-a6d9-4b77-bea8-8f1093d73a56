import {DatePipe} from '@angular/common';
import {Injectable} from '@angular/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {GridApi} from 'ag-grid-community';
import {Observable} from 'rxjs';
import {PricingPolicyBuilderModalComponent} from 'src/app/components/pricingPolicyModal/pricingPolicyModal.component';
import {StrategyBuilderModalComponent} from 'src/app/components/strategyBuilderModal/strategyBuilderModal.component';
import {CphPipe} from 'src/app/cph.pipe';
import {SaveSiteSettingsParams} from 'src/app/model/SaveSiteSettingsParams';
import {SiteSettings} from 'src/app/model/SiteSettings';
import {StrategyFactorName} from 'src/app/model/StrategyFactorName';
import {StrategyFull} from 'src/app/model/StrategyFull';
import {StrategyVersionVM} from 'src/app/model/StrategyVersionVM';
import {ColumnTypesService} from 'src/app/services/columnTypes.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {SiteSettingsComponent} from './siteSettings.component';
import {BasicInfoModalComponent} from 'src/app/components/basicInfoModal/basicInfoModal.component';

@Injectable({
  providedIn: 'root'
})
export class SiteSettingsService {

  StrategyFactorName = StrategyFactorName; // just a reference to be able to use enum
  siteSettingsRowData: SiteSettings[];  // the row data
  selectedSite: SiteSettings; // the chosen row
  gridApi: GridApi;
  pricingPolicies: StrategyVersionVM[]; // the strategies to choose from

  strategies: StrategyFull[];


  siteSettingsComponentRef: SiteSettingsComponent;

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public getDataService: GetDataMethodsService,
    public datePipe: DatePipe,
    public modalService: NgbModal,
    public cphPipe: CphPipe,
    public colTypesService: ColumnTypesService,
  ) {
  }


  // the ruleSet
  getAllStrategies() {
    this.getDataService.getAllStrategies().subscribe((res: StrategyFull[]) => {
      this.strategies = res.map(x => new StrategyFull(x));
    }, error => {
      console.error('Failed to retrieve strategies', error);
      this.selections.triggerSpinner.emit({show: false});
    });
  }

  async getAllStrategiesPromise() {
    return this.getDataService.getAllStrategies().toPromise().then((res) => {
      this.strategies = res.map(x => new StrategyFull(x));
    });
  }

  async recalculateTestStrategyPrices() {
    this.constants.toastSuccess('Recalculating test strategy prices...');
    await this.getDataService.recalculateTestStrategyPrices();
  }

  async recalculateTestStrategyDaysToSell() {
    this.constants.toastSuccess('Recalculating test strategy Days To Sell...');
    await this.getDataService.recalculateTestStrategyDaysToSell();
  }

  // the matrix etc.
  getAllPricingPolicies() {
    this.getDataService.getAllPricingPolicies().subscribe((res: StrategyVersionVM[]) => {
      this.pricingPolicies = res.map(x => new StrategyVersionVM(x));
    }, error => {
      console.error('Failed to retrieve strategies', error);
      this.selections.triggerSpinner.emit({show: false});
    });
  }


  saveSiteSettings() {
    this.selections.triggerSpinner.emit({show: true, message: 'Saving...'});

    //align these if we don't do a separate one
    // if (!this.constants.autopriceEnvironment.separateBuyingStrategy) {
    //   this.selectedSite.BuyingStrategySelectionRuleSetId = this.selectedSite.StrategySelectionRuleSetId;
    //   this.selectedSite.BuyingStrategySelectionRuleSetName = this.selectedSite.StrategySelectionRuleSetName;
    // }else{
    if (!this.selectedSite.BuyingStrategySelectionRuleSetId) {
      this.selectedSite.BuyingStrategySelectionRuleSetId = this.selectedSite.StrategySelectionRuleSetId;
    }
    if (!this.selectedSite.BuyingStrategySelectionRuleSetName) {
      this.selectedSite.BuyingStrategySelectionRuleSetName = this.selectedSite.StrategySelectionRuleSetName;
    }
    //}

    if (!this.constants.autopriceEnvironment.separateBuyingStrategy2) {

      this.selectedSite.BuyingStrategySelectionRuleSet2Id = this.selectedSite.StrategySelectionRuleSetId;
      this.selectedSite.BuyingStrategySelectionRuleSet2Name = this.selectedSite.StrategySelectionRuleSetName;
    } else {
      if (!this.selectedSite.BuyingStrategySelectionRuleSet2Id) {
        this.selectedSite.BuyingStrategySelectionRuleSet2Id = this.selectedSite.StrategySelectionRuleSetId;
      }
      if (!this.selectedSite.BuyingStrategySelectionRuleSet2Name) {
        this.selectedSite.BuyingStrategySelectionRuleSet2Name = this.selectedSite.StrategySelectionRuleSetName;
      }
    }

    const params: SaveSiteSettingsParams = {
      siteSettings: this.selectedSite
    };


    this.getDataService.saveSiteSettings(params).subscribe((res: SiteSettings) => {
      this.constants.toastSuccess('Saved site settings');
      if (this.gridApi) {
        this.gridApi.refreshCells();
        this.gridApi.redrawRows();
      }

      this.selections.triggerSpinner.emit({show: false});
    }, error => {
      this.constants.toastDanger('Failed to save site settings');
      console.error('Failed to save site settings', error);
      this.selections.triggerSpinner.emit({show: false});
    });
  }


  saveMultipleSiteSettings(site: SiteSettings): Observable<SiteSettings> {

    // if (!this.constants.autopriceEnvironment.separateBuyingStrategy) {
    //   site.BuyingStrategySelectionRuleSetId = site.StrategySelectionRuleSetId;
    //   site.BuyingStrategySelectionRuleSetName = site.StrategySelectionRuleSetName;
    // }

    if (!this.constants.autopriceEnvironment.separateBuyingStrategy2) {
      site.BuyingStrategySelectionRuleSet2Id = site.StrategySelectionRuleSetId;
      site.BuyingStrategySelectionRuleSet2Name = site.StrategySelectionRuleSetName;
    }

    const params: SaveSiteSettingsParams = {
      siteSettings: site
    };

    // Simply return the Observable from saveSiteSettings
    return this.getDataService.saveSiteSettings(params) as Observable<SiteSettings>;
  }


  resetModal() {
    // this.newStrategyName = null;
    // this.newStrategyEffectiveDate = this.datePipe.transform(new Date(), 'yyyy-MM-dd');
  }

  createStrategyVersion(sv: StrategyVersionVM) {
    this.getDataService.createStrategyVersion(sv).subscribe(() => {
      this.selections.triggerSpinner.next({show: false});
      // this.strategyVersionEmitter.emit(true);
    }, (error: any) => {
      console.error('Failed to create new strategy factory', error);
      this.selections.triggerSpinner.next({show: false});
    });
  }

  async openStrategy(strategy: StrategyFull) {

    const toUse: StrategyFull = new StrategyFull(strategy);

    const openStrategyModalRef = this.modalService.open(StrategyBuilderModalComponent, {size: 'md'});
    const instance: StrategyBuilderModalComponent = openStrategyModalRef.componentInstance;
    instance.setStrategy(toUse);

    try {

      const newStrategyId: number | null = await openStrategyModalRef.result; // .then(res => {

      await this.constants.sleep(500);

      // This is blocking to fetch new strategies
      await this.getAllStrategiesPromise();

      const messageModalRef = this.modalService.open(BasicInfoModalComponent, {size: 'md'});
      const instance: BasicInfoModalComponent = messageModalRef.componentInstance;

      if (newStrategyId) {

        // Fetch the new strategy to access the details

        const newStrategy = this.strategies.find(x => x.StrategyId == newStrategyId);

        // Created new strategy
        instance.header = 'New Pricing Strategy Created';

        if (strategy.StrategyId == null) {
          // we had chosen to create a new strategy so will be expecting a new strategy
          if (this.constants.environment.isSingleSiteGroup) {
            instance.body = `New pricing strategy #${newStrategy.UniqueIdForDg} has been created.  Ensure you now update your site to use this strategy.`;
          } else {
            instance.body = `New pricing strategy #${newStrategy.UniqueIdForDg} has been created.  Ensure you now update any sites that wish to use this strategy.`;
          }
        } else {
          // we were editing an existing strategy, might not expect to now have a new strategy Id
          if (this.constants.environment.isSingleSiteGroup) {
            instance.body = `Pricing strategy #${strategy.UniqueIdForDg} had previously been used to calculate prices so cannot be edited.  Instead, new pricing strategy #${newStrategy.UniqueIdForDg} has been created.   Ensure you now pick this from the dropdown for your site.`;
          } else {
            instance.body = `Pricing strategy #${strategy.UniqueIdForDg} had previously been used to calculate prices so cannot be edited.  Instead, new pricing strategy #${newStrategy.UniqueIdForDg} has been created.   Ensure you now pick this from the dropdown for any sites that wish to use it.`;
          }
        }
      } else {
        instance.header = 'Pricing Strategy Updated';
        instance.body = `Pricing strategy #${strategy.UniqueIdForDg} has been updated.`;
      }

    } catch (error) {
      // we disimissed
      if (error) {
        // we did  delete a strategy
        await this.getAllStrategies();
        const messageModalRef = this.modalService.open(BasicInfoModalComponent, {size: 'md'});
        const instance: BasicInfoModalComponent = messageModalRef.componentInstance;
        instance.header = 'Pricing Strategy Deleted';
        instance.body = `Pricing strategy #${strategy.UniqueIdForDg} has been deleted.`;
      }

    }
  }


  openPricingPolicy(policy: StrategyVersionVM) {

    const toUse: StrategyVersionVM = new StrategyVersionVM(policy);

    const openStrategyModalRef = this.modalService.open(PricingPolicyBuilderModalComponent);
    const instance: PricingPolicyBuilderModalComponent = openStrategyModalRef.componentInstance;
    instance.service.chosenPolicy = toUse;
    instance.chosenStrategyClone = this.constants.clone(toUse);

    openStrategyModalRef.result.then(res => {
      // yay
      this.getSitesSettings();
      this.getAllPricingPolicies();
    });
  }


  getSitesSettings() {
    this.getDataService.getSitesSettings().subscribe((res: SiteSettings[]) => {
      this.siteSettingsRowData = res.map(x => new SiteSettings(x));


      if (this.siteSettingsComponentRef) {
        if (this.gridApi) {
          this.gridApi.setRowData(this.siteSettingsRowData);
          this.gridApi.refreshCells();
          this.gridApi.redrawRows();
        } else {
          this.siteSettingsComponentRef.initialiseGrid();
        }
      }

    }, error => {
      console.error('Failed to retrieve site settings', error);
      this.selections.triggerSpinner.emit({show: false});
    });
  }


}
