using CPHI.Spark.Model.ViewModels.AutoPricing;

namespace CPHI.WebScraper.ViewModel
{
   public class ClickDealerPriceChange
   {
      // Constructor from PricingChangeNew
      public ClickDealerPriceChange(PricingChangeNew pr)
      {
         Reg = pr.VehicleReg;
         Price = pr.NewPrice;
         RetailerSiteId = pr.RetailerSiteId;
         ClickDealerFee = pr.ClickDealerFee;
         PriceChangeId = pr.PriceChangeId;
      }

      // Constructor for test items
      public ClickDealerPriceChange(string reg, int price)
      {
         Reg = reg;
         Price = price;
      }


      public string Reg { get; set; }
      public int Price { get; set; }
      public int RetailerSiteId { get; set; }
      public int? ClickDealerFee { get; set; }

      public bool PriceChanged { get; set; } = false;

      // Properties for SaveChangeResult
      public int PriceChangeId { get; set; }
      public bool IsAutoPriceChange { get; set; } = true; // Believe this should always be true
      public bool SavedOk { get; set; }
      public string SaveError { get; set; }
   }
}
