import { Component, OnInit } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { debounceTime, groupBy, mergeMap } from 'rxjs/operators';
import { DayToSellAndPriceIndicator } from "src/app/model/DayToSellAndPriceIndicator";
import { LocationAndStrategyPrice } from 'src/app/model/LocationAndStrategyPrice.model';
import { RetailRatingParams } from 'src/app/model/RetailRatingParams';
import { PreferenceKey } from 'src/app/model/UserPreference';
import { ValuationPriceSet } from 'src/app/model/ValuationPriceSet.model';
import { ValuationResultForNewVehicleToSave } from 'src/app/model/ValuationResultForNewVehicleToSave.model';
import { VehicleSpecOption } from 'src/app/model/VehicleSpecOption.model';
import { GetEstimatedDayToSellAndPriceIndicatorParams } from 'src/app/pages/performanceTrends/GetEstimatedDayToSellAndPriceIndicatorParams';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { StrategyPriceBuildUpLayersParams } from '../strategyPriceBuildUpLayers/strategyPriceBuildUpLayers.component';
import { ValuationCostingFieldEnum } from './valuationCosting/ValuationCostingDTO';
import { ValuationModalParams } from './valuationCosting/ValuationModalParams';
import { ValuationCosting } from './valuationCosting/valuationCosting';
import { VehicleValuationService, newValue } from './vehicleValuation.service';
import { ThisWeekOrdersService } from 'src/app/pages/dashboard/tiles/thisWeekOrders/thisWeekOrders.service';



@Component({
  selector: 'autoPriceValuationModal',
  templateUrl: './autoPriceValuationModal.component.html',
  styleUrls: ['./autoPriceValuationModal.component.scss']
})
export class AutoPriceValuationModalComponent implements OnInit {
  openPanels: string[];
  //vehicleConditions: string[] = ['Poor', 'Fair', 'Good', 'Great', 'Excellent'];
  valCostingNewValueSub: Subscription;

  constructor(
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public getDataService: GetDataMethodsService,
    public modalService: NgbModal,
    public service: VehicleValuationService

  ) { }



  ngOnInit() {
    //this.initParams(null);



    this.valCostingNewValueSub = this.service.valuationCostingChangeEmitter.pipe(
      groupBy((val: newValue) => val.field),  // Group by fieldName
      mergeMap(group => group.pipe(
        debounceTime(600)  // Apply debounce to each group individually
      ))
    ).subscribe(res => {
      this.onNewCostingsValue(res);
    });



  }



  ngOnDestroy() {
    this.service.optionsModalRef = null;
    this.service.strategyPriceBySiteModalRef = null;
    this.service.optionsTableRef = null;
    this.service.optionsGridApi = null;

    if (this.valCostingNewValueSub) { this.valCostingNewValueSub.unsubscribe() }

  }





  initParams(params: ValuationModalParams, modalRef: NgbModalRef) {

    this.service.valuationModalResultNew = null;
    this.openPanels = this.service.userPrefsService.getPreference(PreferenceKey.ValuationModalOpenPanels) ?? [];

    //if we have params
    if (params) {
      this.service.vehicleReg = params.reg;
      this.service.mileage = params.mileage;
      this.service.vehicleCondition = params.condition;
      this.service.valuationId = params.valuationId;
      this.service.colour = params.colour;
    }


    //this.service.strategyBySite = [];
    this.service.valuationModalResultNew = null;
    this.service.optionsModalOptions = null;
    this.service.optionsModalValuation = null;

    this.service.valuationCosting = new ValuationCosting(this.service);
    this.service.chosenRetailerSite = null;


    this.service.getVehicleInformationAndValuation(modalRef);
    //this.service.getVehicleInformationAndValuation();
  }



  onNewCostingsValue(val: newValue) {
    const fieldName:string = ValuationCostingFieldEnum[val.field].toString();
    this.service.valuationCosting[fieldName] = val.value
    //console.log('costing value changed:', val)

    if (val.field === ValuationCostingFieldEnum.Sales) {
      //we have changed the selling price
      const params = this.service.updateCompetitorAnalysisForOurNewPrice();
      this.service.competitorAnalysisService.dealWithNewParams(params)
      this.getEstimatedDayToSellAndPriceIndicator(this.service.valuationCosting.sales)
    }

    if (val.field == ValuationCostingFieldEnum.PricePosition) {
      //get new days to sell and price indicator
      setTimeout(() => {
        const params = this.service.updateCompetitorAnalysisForOurNewPrice();
        this.service.competitorAnalysisService.dealWithNewParams(params)
        this.getEstimatedDayToSellAndPriceIndicator(this.service.valuationCosting.sales)
      }, 100)

    }
  }



  getEstimatedDayToSellAndPriceIndicator(newPrice: number) {

    const newPricePosition = newPrice / this.service.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle;

    if (newPricePosition > 1.1 || newPricePosition < 0.9) {
      this.dealWithUnavailablePriceIndicator()
      return;
    }



    const parms: GetEstimatedDayToSellAndPriceIndicatorParams = {
      AdvertiserIds: this.service.valuationModalResultNew.LocationStrategyPrices.map(x => x.RetailerSite.RetailerId),
      DerivativeId: this.service.valuationModalResultNew.VehicleInformation.DerivativeId,
      FirstRegisteredDate: this.service.valuationModalResultNew.VehicleInformation.FirstRegistered,
      Mileage: this.service.valuationModalResultNew.VehicleInformation.Mileage,
      StrategyPrice: Math.round(newPrice),
      VehicleHasOptionsSpecified: !!this.service.valuationModalResultNew.VehicleInformation.ChosenOptions && this.service.valuationModalResultNew.VehicleInformation.ChosenOptions.length > 0,
      VehicleAdvertPortalOptions: this.service.valuationModalResultNew.VehicleInformation.ChosenOptions,

      AverageValuation: this.service.valuationModalResultNew.ValuationPriceSet.RetailAverageSpec,
      AdjustedValuation: this.service.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle
    }

    this.service.amCalculating = true;
    this.getDataService.getAutoPriceEstimatedDayToSellAndPriceIndicator(parms)
      .subscribe((res: DayToSellAndPriceIndicator) => {
        this.service.amCalculating = false;
        if (res.PriceIndicator) {

          if (res.DaysToSellResults.length > 0) {
            this.service.chosenVehicleLocationStrategyPriceBuild.DaysToSell = res.DaysToSellResults.find(x => x.RetailerSiteRetailerId === this.service.chosenRetailerSite.RetailerId).DaysToSell;
          }

          //update all the days to sell results for each site
          res.DaysToSellResults.forEach(res => {
            const existingLocationStrategyPrice = this.service.valuationModalResultNew.LocationStrategyPrices.find(x => x.RetailerSite.RetailerId === res.RetailerSiteRetailerId);
            existingLocationStrategyPrice.DaysToSell = res.DaysToSell
          })
          //update price indicator
          this.service.valuationModalResultNew.ValuationPriceSet.PriceIndicator = res.PriceIndicator;
          //tell the trended valuation thing to refresh
          if(this.service.trendedValuationInstance){
            this.service.trendedValuationInstance.dealWithNewData(this.service.valuationModalResultNew.TrendedValuations)
          }
        } else {
          this.dealWithUnavailablePriceIndicator()
        }


      }, error => {
        this.service.amCalculating = false;
        console.error('Failed to retrieve estimated Day to Sell & Price Indicator', error);
      })
  }

  dealWithUnavailablePriceIndicator() {
    this.service.constantsService.toastSuccess("Not possible to work out")
    this.service.valuationModalResultNew.ValuationPriceSet.PriceIndicator = "Unknown";
    this.service.chosenVehicleLocationStrategyPriceBuild.DaysToSell = 0;
    this.service.valuationModalResultNew.LocationStrategyPrices.map(x => x.DaysToSell = 0);
    if(this.service.trendedValuationInstance){
      this.service.trendedValuationInstance.dealWithNewData(this.service.valuationModalResultNew.TrendedValuations)
    }
  }



  // async getValuationForAdvert() {
  //   this.service.batchId ? await this.getExistingValuationForAdvert(this.service.batchId) : await this.service.getNewValuationForAdvert();
  // }

  async getExistingValuationForAdvert(batchId: number) {
    await this.getDataService.getExistingValuationForAdvert(batchId).then(async (valuation: ValuationPriceSet) => {
      //wait this.service.onValuationLoad(valuation);
    }, error => {
      console.error('Error returning valuation price set');
      this.selectionsService.triggerSpinner.emit({ show: false });
    })
  }



  saveValuation() {
    this.selectionsService.triggerSpinner.emit({ show: true, message: 'Saving...' });

    //this.setChosenOptions();

    const vehicleSpecOptions: VehicleSpecOption[] = [];
    if(this.service.valuationModalResultNew.VehicleInformation.ChosenOptions){
      this.service.valuationModalResultNew.VehicleInformation.ChosenOptions.forEach(option => {
        const fullOption: VehicleSpecOption = new VehicleSpecOption(this.service.valuationModalResultNew.SpecOptions.find(x => x.Name == option));
        fullOption.IsChosen = true;
        vehicleSpecOptions.push(fullOption);
      })
    }

    const params: ValuationResultForNewVehicleToSave = {
      ValuationId: this.service.valuationId,
      VehicleReg: this.service.vehicleReg.toUpperCase(),
      FirstRegistered: new Date(this.service.valuationModalResultNew.VehicleInformation.FirstRegistered),
      Mileage: this.service.mileage,
      Condition: this.service.vehicleCondition,
      DerivativeId: this.service.valuationModalResultNew.VehicleInformation.DerivativeId,
      RetailRating: this.service.chosenVehicleLocationStrategyPriceBuild.RetailRating,
      Valuation: this.service.valuationModalResultNew.ValuationPriceSet,
      VehicleSpecOptions: vehicleSpecOptions,
      SitesStrategy: this.service.valuationModalResultNew.LocationStrategyPrices.map(x => new LocationAndStrategyPrice(x)),
      PrepCost: 0,//this.showNormalPrepCost,
      ExtraPrepRows: [],
      Costing: this.service.valuationCosting.getState(),
      SpecificColour: this.service.colour
    }

    this.getDataService.saveVehicleValuation(params).subscribe(() => {
      this.constantsService.toastSuccess('Saved valuation');
      //this.service.batchId = res;
      this.selectionsService.triggerSpinner.emit({ show: false });
    }, error => {
      console.error('Error saving valuation');
      this.constantsService.toastDanger('Failed to save valuation');
      this.selectionsService.triggerSpinner.emit({ show: false });
    })
  }

  closeModal() {
    this.modalService.dismissAll();
  }


  updateCondition(condition: string) {
    this.service.vehicleCondition = condition;
    //this.service.batchId = null;
    this.service.valuationId = null;
    this.service.getVehicleInformationAndValuation(null);
  }

  getRetailRatingParams(): RetailRatingParams {
    return {
      Make: this.service.valuationModalResultNew?.VehicleInformation.Make,
      RetailRating: this.service.valuationModalResultNew.LocationStrategyPrices.find(x => x.RetailerSite.Id == this.service.chosenRetailerSite.Id)?.RetailRating,
      NationalRetailRating: this.service.valuationModalResultNew?.VehicleInformation.NationalRetailRating
    }
  }




  buildTheBuildUpLayers(): StrategyPriceBuildUpLayersParams {
    return {
      buildUpData: this.service.chosenVehicleLocationStrategyPriceBuild?.BuildUpItems,
      retailRating: this.service.chosenVehicleLocationStrategyPriceBuild?.RetailRating,
      daysToSell: this.service.chosenVehicleLocationStrategyPriceBuild?.DaysToSell,
      daysBookedIn: 0,
      daysListed: 0,
      daysInStock: 0,
      make: this.service.valuationModalResultNew.VehicleInformation.Make,
      specificColour: this.service.valuationModalResultNew.VehicleInformation.SpecificColour,
      ageAndOwners: this.service.valuationModalResultNew.VehicleInformation.AgeAndOwners,
      odometer: this.service.valuationModalResultNew.VehicleInformation.Mileage,
      performanceRating:null
    }
  }





  onPanelChange(event: any, panelId: string): void {
    const panelActive = event.nextState;

    if (panelActive) {
      // Panel is being opened
      if (!this.openPanels.includes(panelId)) {
        this.openPanels.push(panelId);
      }
    } else {
      // Panel is being closed
      this.openPanels = this.openPanels.filter(id => id !== panelId);
    }

    this.service.userPrefsService.setPreference(PreferenceKey.ValuationModalOpenPanels, this.openPanels);

  }


  stockCover() {
    return Math.round(this.service.valuationModalResultNew.StockLevelAndCover.find(x => x.IsTotal).Cover);
  }


  soldLast6mMessage() {
    const recentlySold = this.service.valuationModalResultNew.RecentlySoldThisModel;
    if (recentlySold.length == 0) { return '' }
    const vehicleCount = this.service.constantsService.pluralise(recentlySold.length, 'vehicle', 'vehicles');
    const daysListed = this.constantsService.sum(recentlySold.map(x => x.DaysListed)) / recentlySold.length;
    const finalPP = this.constantsService.sum(recentlySold.map(x => x.LastPP)) / recentlySold.length;

    return `${vehicleCount}, average days listed: ${this.service.cphPipe.transform(daysListed, 'number', 0)}, final price position: ${this.service.cphPipe.transform(finalPP, 'percent', 1)}`
  }


  vehicleDetailsString() {
    if (!this.service.valuationModalResultNew) { return '' }

    const val = this.service.valuationModalResultNew;

    return ` | ${val.VehicleInformation.VehicleReg} | ${val.VehicleInformation.Make} ${val.VehicleInformation.Model} ${val.VehicleInformation.Derivative} | ${this.service.constantsService.pluralise(val.VehicleInformation.Mileage, 'mile', 'miles')} `
  }

}
