﻿using OpenQA.Selenium;
using System;

public static class ByExtensions
{
    public static string ToReadableString(this By by)
    {
        if (by == null) throw new ArgumentNullException(nameof(by));

        string byString = by.ToString();

        // Handle various By types
        if (byString.StartsWith("By.Id:"))
        {
            return $"by Id: {byString.Replace("By.Id: ", "")}";
        }
        else if (byString.StartsWith("By.XPath:"))
        {
            return $"by XPath: {byString.Replace("By.XPath: ", "")}";
        }
        else if (byString.StartsWith("By.ClassName:"))
        {
            return $"by ClassName: {byString.Replace("By.ClassName: ", "")}";
        }
        else if (byString.StartsWith("By.CssSelector:"))
        {
            return $"by CssSelector: {byString.Replace("By.CssSelector: ", "")}";
        }
        else if (byString.StartsWith("By.TagName:"))
        {
            return $"by TagName: {byString.Replace("By.TagName: ", "")}";
        }
        else if (byString.StartsWith("By.LinkText:"))
        {
            return $"by LinkText: {byString.Replace("By.LinkText: ", "")}";
        }
        else if (byString.StartsWith("By.PartialLinkText:"))
        {
            return $"by PartialLinkText: {byString.Replace("By.PartialLinkText: ", "")}";
        }
        else if (byString.StartsWith("By.Name:"))
        {
            return $"by Name: {byString.Replace("By.Name: ", "")}";
        }
        else
        {
            return byString;
        }
    }
}
