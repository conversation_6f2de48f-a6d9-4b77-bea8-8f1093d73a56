﻿using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Threading.Tasks;
using System.Data;
using System.Diagnostics;
using CPHI.Spark.Model.ViewModels;

namespace CPHI.Spark.Loader
{
    public partial class AuditVindisJob : IJob
    {
        private const string fileExt = ".csv";

        private static readonly ILog Logger = LogManager.GetLogger(typeof(AuditVindisJob));
        private static DateTime LastLoggedNoFiles = DateTime.UtcNow;

        private Dictionary<string, int> headerColIndex;
        private string[] headers;

        private List<Deal> allDeals;
        private int modificationCount;

        private LogMessage logMessage;
        private const string fileSearch = "*Audit.csv";

        private List<VehicleType> vehicleTypes;
        /*
| Column         | Example                                                 | Sensitive           |
|----------------|---------------------------------------------------------|---------------------|
| Deal File Type | New Retail Order - Corporate                            | No                  |
| Vehicle        | Audi A1 A1 SPORTBACK - 2018 25 TFSI S Line 5dr S Tronic | No                  |
| Reg No         | AE72WYX                                                 | No                  |
| Chassis No     | WAUZZZGB5NR044066                                       | No                  |
| Order No       | 32305376                                                | No                  |
| Stock No       |                                                         | No                  |
| LMS Order Code | 16150762                                                | No                  |
| Customer Name  | Mr Russell JACKSON                                      | YES - customer name |
| Created By     |                                                         | No                  |
| Current Owner  | Lee Stubbings                                           | No                  |
| Retailer       | Huntingdon Audi                                         | No                  |
| Status         | Closed                                                  | No                  |
| CustomStatus   | Archived Audit Fail                                     | No                  |
| Created Date   | 44768.51111                                             | No                  |
| Invoice Date   | 44833                                                   | No                  |
| Delivery Date  | 44939.33333                                             | No                  |

        ...

        plus lots more dates and bools on tasks

        ...

*/


        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            //---------------------------------------------------------------------------------------------------
            // Firstly look for files
            string[] allMatchingFiles = await Task.Run(() => Directory.GetFiles(ConfigService.incomingRoot, fileSearch));

            // Check for presence of lock, if so, return as already running
            if (LocksService.VindisAudit) { return; }

            // Check if no files, and if not logged for a while, log that didn't find anything
            if (allMatchingFiles.Length == 0) { HelpersService.NoFileFoundMessage(Logger, LastLoggedNoFiles, fileSearch); return;  }

            // Console.WriteLine(siteId);
            string fileToProcess = allMatchingFiles[0];

            // Try opening the file, if fail, return (common problem is loader trying to open file whilst scraper is saving it).
            try { FileStream fs = File.Open(fileToProcess, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None); fs.Close(); }
            catch (IOException) { return; }

            // Create lock to prevent other instances running
            LocksService.VindisAudit = true;

            logMessage = new LogMessage();
            logMessage.DealerGroup_Id = 3;
            logMessage.SourceDate = DateTime.UtcNow;
            logMessage.Job = this.GetType().Name;

            HelpersService.NewFileFoundMessage(Logger, fileToProcess);

            //---------------------------------------------------------------------------------------------------
            //1.  Set File to -p to indicate is being processed and get filename details
            if (File.Exists(fileToProcess.Replace(fileExt, "-p" + fileExt)))
            {
                //already processing a file of this type, skip
                Logger.Error($@"Could not interpret {fileToProcess}, -p file already found ");
                logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
            }

            string fileName = Path.GetFileName(fileToProcess);
            var fileDate = File.GetCreationTimeUtc(fileToProcess);
            logMessage.SourceDate = fileDate;
            File.Move(fileToProcess, fileToProcess.Replace(fileExt, "-p" + fileExt)); //append _processing to the file to prevent any other instances also processing these files
            var newFilepath = fileToProcess.Replace(fileExt, "-p" + fileExt);

            //---------------------------------------------------------------------------------------------------
            if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
            {
                System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
            }
            
            DateTime start = DateTime.UtcNow;

            using (var db = new CPHIDbContext())

            {
                try
                {

                    SetDbLists(db);

                    // Get row data
                    HeadersAndRows rawData = GetDataFromFilesService.GetRowsCsvNew(newFilepath, 0);
                    List<List<string>> rows = rawData.rows;

                    // Get headers
                    headers = rawData.headers.ToArray();

                    headerColIndex = SetHeaderColIndexDict();
                    DateTime finishedInterpetFile = DateTime.UtcNow;
                    AmendDeals(rows);
                    try
                    {
                        db.SaveChanges();
                        Logger.Info($"[{DateTime.UtcNow}]  | Result: Audit loader: {modificationCount} deals amended.");
                    }
                    catch (Exception e)
                    {
                        Logger.Error($@"Audit loader: failed to save changes to DB.");
                        throw e;
                    }

                    DateTime finishedUpdateDb = DateTime.UtcNow;

                    logMessage.FinishDate = finishedUpdateDb;
                    logMessage.IsCompleted = true;
                    logMessage.ChangedCount = modificationCount;
                    logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                    logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                    logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;
                    logMessage.DealerGroup_Id = 3;
                    db.LogMessages.Add(logMessage);
                    db.SaveChanges();

                    RemoveUnwantedData(headers, newFilepath);
                    HelpersService.MoveFileToProcessed(newFilepath, fileExt);
                    stopwatch.Stop();
                }
                catch (Exception e)
                {
                    stopwatch.Stop();
                    errorMessage = e.ToString();

                    throw e;
                }
                finally
                {
                    db.ChangeTracker.Clear();

                    LocksService.VindisAudit = false;

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "Vindis",
                        Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };
                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }

                
            }

        }

        private void AmendDeals(List<List<string>> rows)
        {

            modificationCount = 0;
            int rowCount = 0;

            foreach (List<string> incomingRow in rows)
            {
                int enquiryNumber;
                rowCount++;

                try
                {
                    enquiryNumber = Int32.Parse(incomingRow[headerColIndex["LMS ORDER CODE"]].ToString());
                }
                catch
                {
                    // Some issue interpreting number - skip.
                    continue;
                }

                try
                {

                    // Do Audit Pass
                    string auditPassStr = incomingRow[headerColIndex["CUSTOMSTATUS"]].ToString();

                    // Cannot find a deal with that enquiry number, unable to update anything - move to next deal
                    if (allDeals.Where(x => x.EnquiryNumber == enquiryNumber).ToList().Count < 1) { continue; }

                    bool? auditPass = GetAuditPassBoolean(auditPassStr);

                    if (allDeals.Where(x => x.EnquiryNumber == enquiryNumber).First().AuditPass != auditPass)
                    {
                        allDeals.Where(x => x.EnquiryNumber == enquiryNumber).First().AuditPass = auditPass;
                        modificationCount += 1;
                    }

                    // Also - do Stock Number: this should be more up to date than EM
                    string stockNumber = incomingRow[headerColIndex["STOCK NO"]].ToString();

                    if (allDeals.Where(x => x.EnquiryNumber == enquiryNumber).First().StockNumber != stockNumber)
                    {
                        allDeals.Where(x => x.EnquiryNumber == enquiryNumber).First().StockNumber = stockNumber;

                        if (stockNumber.Length > 0)
                        { // IF D* then set to demo
                            if (stockNumber.Substring(0,1) == "D") { allDeals.Find(x => x.EnquiryNumber == enquiryNumber).VehicleType_Id = vehicleTypes.First(x => x.Description == "Demo").Id; };
                        }

                        modificationCount += 1;
                    }

                    Console.WriteLine("Processed row " + rowCount);

                }
                catch
                {
                    logMessage.ErrorCount++;
                    logMessage.FailNotes += $"Failed on {enquiryNumber.ToString()}";
                }

            }

        }

        private bool? GetAuditPassBoolean(string auditPassStr)
        {
            if (auditPassStr == "Audit Fail – Exception") {  return true; } // Vindis Request from Matt 22/07/24
            if (auditPassStr == "Auto Audit Pass" || auditPassStr == "Audit Pass" || auditPassStr == "Group Audit Pass") { return true; }
            if (auditPassStr == "Audit Fail" || auditPassStr == "Group Audit Fail" || auditPassStr == "Archived Audit Fail")  { return false; }
            return null;
        }

        private void SetDbLists(CPHIDbContext db)
        {//x => !x.IsRemoved & x.OrderDate.Year >= 2021
            DateTime sixMonthsAgo = DateTime.Today.AddMonths(-6);
            allDeals = db.Deals.Where(x => !x.IsRemoved & x.ActualDeliveryDate >= sixMonthsAgo).ToList();
            vehicleTypes = db.VehicleTypes.ToList();
        }

        private string[] GetHeaders(List<DataRow> rows)
        {
            DataRow headerRow = rows.Skip(1).First();
            headers = headerRow.ItemArray.ToList().Select(x => x.ToString()).ToArray();
            headers = headers.Select(s => s.ToUpper()).ToArray();
            return headers;
        }

        private Dictionary<string, int> SetHeaderColIndexDict()
        {

            try
            {

                Dictionary<string, int> newHeaderColIndex = new Dictionary<string, int>()
                {
                    {"LMS ORDER CODE", Array.IndexOf(headers, "LMS ORDER CODE")},
                    {"STOCK NO", Array.IndexOf(headers, "STOCK NO")},
                    {"CUSTOMSTATUS", Array.IndexOf(headers, "CUSTOMSTATUS")},

                };

                return newHeaderColIndex;

            }

            catch (Exception err)
            {
                throw err;
            }
        }

        private void RemoveUnwantedData(string[] headers, string filepath)
        {
            int toDelete1 = Array.IndexOf(headers,"CUSTOMER NAME");

            int[] columnIndicesToDelete = { toDelete1 };

            HelpersService.DeleteColumnsAndSaveCSV(filepath, filepath, columnIndicesToDelete, 1);

        }













    }


}
