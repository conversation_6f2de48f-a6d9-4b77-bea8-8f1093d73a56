<div class="tile-inner">
    <div class="tile-header">
        {{ tileName }}
    </div>
    <div class="tile-body">
        <!-- Vehicle Details -->
        <div *ngIf="tileName === 'Vehicle Details'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <img src="../../../../../assets/imgs/autoTrader/palceholder-car.png" class="placeholder col-3"
                style="height: 100px;" style="object-fit: contain">
            <span class="placeholder col-6"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-3"></span>
            <span class="placeholder col-3"></span>
            <span class="placeholder col-9"></span>
            <span class="placeholder col-3"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- Pricing Details -->
        <div *ngIf="tileName === 'Pricing Details'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- Pricing Chart -->
        <div *ngIf="tileName === 'Pricing Chart'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- Pricing History -->
        <div *ngIf="tileName === 'Pricing History'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- Profit -->
        <div *ngIf="tileName === 'Profit'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
        </div>

        <!-- Notes -->
        <div *ngIf="tileName === 'Notes'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- Direct Links -->
        <div *ngIf="tileName === 'Direct Links'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-8"></span>
            <span class="placeholder col-8"></span>
        </div>

        <!-- Competitor Analysis -->
        <div *ngIf="tileName === 'Competitor Analysis'" class="placeholder-container placeholder-glow"
            aria-hidden="true">
            <span class="placeholder col-3"></span>
            <span class="placeholder col-3"></span>
            <span class="placeholder col-3"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- Other Models in Group -->
        <div *ngIf="tileName === 'Other Models in Group'" class="placeholder-container placeholder-glow"
            aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- Site Transfer -->
        <div *ngIf="tileName === 'Site Transfer'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- DMS Stock Information -->
        <div *ngIf="tileName === 'DMS Stock Information'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
        </div>

        <!-- Pricing Status and History -->
        <div *ngIf="tileName === 'Pricing Status and History'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
        </div>

        <!-- Pricing Scenarios -->
        <div *ngIf="tileName === 'Pricing Scenarios'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-6"></span>
        </div>

        <!-- Strategy Price Build-up -->
        <div *ngIf="tileName === 'Strategy Price Build-up'" class="placeholder-container placeholder-glow" aria-hidden="true">
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-12"></span>
            <span class="placeholder col-6"></span>
        </div>

        <!-- Historic Prices -->
        <div *ngIf="tileName === 'Historic Prices'" class="placeholder-container placeholder-glow h-100" aria-hidden="true">
            <span class="placeholder col-12 h-100"></span>
        </div>

        <!-- Search & Advert Views -->
        <div *ngIf="tileName === 'Search & Advert Views'" class="placeholder-container placeholder-glow h-100" aria-hidden="true">
            <span class="placeholder col-12 h-25"></span>
            <span class="placeholder col-12 h-25"></span>
            <span class="placeholder col-12 h-25"></span>
        </div>
    </div>
</div>