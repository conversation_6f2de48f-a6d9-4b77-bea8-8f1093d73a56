import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SimplePickerItem } from 'src/app/components/pickerSimple.component';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { CphPipe } from '../../cph.pipe';
import { AgeingOption, AsAt, StockReport, StockMerchSiteRow } from '../../model/sales.model';
import { AutotraderService } from '../../services/autotrader.service';
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';
import { StockReportService } from './stockReport.service';

export interface StockSiteRow {

        SiteId: number;
        SiteDescription: string;
        RegionDescription: string;
        IsSite:boolean;
        IsRegion:boolean;
        IsTotal:boolean;
        Label:string;


        New: number;
        Demo: number;
        CoreUsed: number;
        Used: number;
        ExManagement: number;
        Tactical: number;
        ExDemo: number;
        Trade: number;
        TotalUsed: number;
        TotalUsedIncTrade: number;
        Total: number;
    }



@Component({
  selector: 'app-stockReport',
  templateUrl: './stockReport.component.html',
  styleUrls: ['./stockReport.component.scss']
})



export class StockReportComponent implements OnInit {

 
  showOver30Bars: boolean;

  includeReservedCarsOption: boolean

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public excel: ExcelExportService,

    public getData: GetDataMethodsService,
    public analysis: AutotraderService,
    public service: StockReportService,

  ) {}



  ngOnDestroy() { }



  ngOnInit() {

    //launch control
    this.initParams();
  }

  initParams() {
   
    this.includeReservedCarsOption = !this.constants.environment.stockReport.includeReservedCarsOption;

    this.selections.initiateStockReport();

    //if there's an incoming chosen report, apply it then set to null
    if (this.selections.stockReport.incomingReportNameChoice) {
      this.selections.stockReport.report = this.selections.stockReport.reports.find(x => x.name == this.selections.stockReport.incomingReportNameChoice)
      this.selections.stockReport.incomingReportNameChoice = null;
    }

    this.getRowData();

    this.selectAgeing(this.selections.stockReport.ageingOption)
    
  }


  getRowData(): void {

    
    // Get data
    if (
      this.service.reportName == this.constants.translatedText.Dashboard_PartsStock_UsedStock ||
      this.service.reportName == this.constants.translatedText.Dashboard_PartsStock_AllStock
    )
    {

      this.getData.getStockReport(this.selections.stockReport.franchises.toString(), 0, 'now', 0, this.constants.thisMonthStart, this.includeReservedCarsOption).subscribe((res:StockSiteRow[]) => {

        this.service.stockRowData = res

        setTimeout(() => {
          this.selections.triggerSpinner.next({show:false});
        }, 20)
  
        this.service.filterUpdated.next(true);
        this.service.showFranchisePicker = true;

      }, error => {
        console.error("ERROR: ", error);
      })
    }

    if(this.selections.stockReport.report.name == this.constants.translatedText.Dashboard_PartsStock_UsedMerchandising)
    {
      this.getData.getStockMerchandisingRows(this.selections.stockReport.franchises.toString()).subscribe((res:StockMerchSiteRow[]) => {

        this.service.usedMerchRowData = res;
        setTimeout(() => {
          this.selections.triggerSpinner.next({show:false});
        }, 20)

        this.service.filterUpdated.next(true);
        this.service.showFranchisePicker = true;
        // this.createData();
      }, error => {
        console.error("ERROR: ", error);

      })
    }

    if(this.selections.stockReport.report.name == this.constants.translatedText.Dashboard_PartsStock_OverageStock)
    {

      var useGroupDaysInt: number = this.selections.stockReport.useBranchDays ? 0 : 1;

      this.getData.getStockReport(this.selections.stockReport.franchises.toString(),this.selections.stockReport.ageingOption.ageCutoff, this.selections.stockReport.asAt.param, useGroupDaysInt,this.constants.thisMonthStart, this.includeReservedCarsOption).subscribe(res => {
        //this.rawData = res;
        this.service.overageRowData = res;
        setTimeout(() => {
          this.selections.triggerSpinner.next({show:false});
        }, 20)
  
        this.service.filterUpdated.next(true);
        this.service.showFranchisePicker = true;
      }, error => {
        console.error("ERROR: ", error);
      });
    }

    

  }


  toggleReservedCars()
  {
    this.includeReservedCarsOption = !this.includeReservedCarsOption;
    this.getRowData();
  }

  selectAgeing(ageingOption: AgeingOption) {
    this.selections.stockReport.ageingOption = ageingOption; 
    this.getRowData();
  }

  selectAsAt(asAt: AsAt) {
    this.selections.stockReport.asAt = asAt;
    this.getRowData();
  }

  selectUseBranchDays(should: boolean): void {
    this.selections.stockReport.useBranchDays = should;
    this.getRowData();
  }

  chooseReport(report: StockReport) {
    this.selections.stockReport.report = report;
    this.service.reportName = report.name;
    this.getRowData();
  }


  onUpdateFranchises(franchises: string[]) {
    this.selections.stockReport.franchises = franchises
    this.getRowData();
  }

 

}
