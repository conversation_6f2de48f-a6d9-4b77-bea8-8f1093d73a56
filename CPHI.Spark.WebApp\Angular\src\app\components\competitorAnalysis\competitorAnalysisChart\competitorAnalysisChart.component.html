<div id="competitorAnalysisCanvasContainer" [ngClass]="{ 'largeChart': autoPriceInsightsService.isSmallScreen }">
   <canvas id="competitorAnalysisCanvas" #competitorAnalysisCanvas></canvas>
</div>

<div *ngIf="segmentSummary" id="competitorAnalysisKey" [ngClass]="{ 'largeChart': autoPriceInsightsService.isSmallScreen }">
   <span *ngFor="let segment of segmentSummary">
      <i class="fas fa-circle" [ngClass]="segment.name"></i>
      {{ segment.name }} ({{ segment.averagePP | cph:'percent':1 }})
   </span>
</div>

<div id="customTooltipContainer" [ngClass]="{ 'largeChart': autoPriceInsightsService.isSmallScreen }">
   <div *ngIf="hoverLabel" id="customTooltip" [ngClass]="autoPriceInsightsService.isSmallScreen ? 'h4' : 'popoverFont'">
      <div id="tooltipTitle">
         PP%: {{ hoverLabel.newPP }}
      </div>
      <div id="tooltipTitle">
         Our vehicle @ this PP%: {{ hoverLabel.newPriceAndChange }}
      </div>
   </div>

   <div *ngIf="hoveredItem" id="customTooltipDetailed"
        [ngClass]="autoPriceInsightsService.isSmallScreen ? 'h4' : 'popoverFont'">

    

      <autotraderImage
      *ngIf="hoveredItem.ImageURL"
      [src]="hoveredItem.ImageURL"
      [widthpx]="150"
      [alt]="'Competitor vehicle image'"
      ></autotraderImage>

      <span>Name: {{ hoveredItem.CompetitorName ? hoveredItem.CompetitorName : 'Private seller' }} </span>
      <span>Reg: {{ hoveredItem.VehicleReg }}</span>
      <span>Year: {{ hoveredItem.Year }}</span>
      <span>Mileage: {{ hoveredItem.Mileage | cph:'number':0 }}</span>
      <span>Derivative: {{ hoveredItem.Derivative }}</span>

      <ng-container *ngIf="hoveredItem.IsOurVehicle && hoveredItem.IsTradeAdjusted; else stdPrice">
         <span>Trade Price: {{ hoveredItem.TradePrice | cph:'currency':0 }}</span>
         <span>&nbsp; Wsale % Adj {{ tradeAdjustPctWholeNumber(hoveredItem.TradeAdjustmentPercentage)  }}: {{tradeAdjustPctImpact(hoveredItem.TradeAdjustmentPercentage, hoveredItem.TradePrice) }}  </span>
         <span>&nbsp; Wsale £ Adj {{ hoveredItem.TradeAdjustmentAmount | cph:'currency':0:true }} </span>
         <span>Est. Retail Price: {{ hoveredItem.AdvertisedPrice | cph:'currency':0 }}</span>
         <span>Price Position: {{ hoveredItem.PricePosition | cph:'percent':1 }}</span>
      </ng-container>

      <ng-template #stdPrice>
         <span>Price: {{ hoveredItem.AdvertisedPrice | cph:'currency':0 }}</span>
         <span>Price Position: {{ hoveredItem.PricePosition | cph:'percent':1 }}</span>
      </ng-template>

      <span>Distance: {{ hoveredItem.Distance }} miles</span>
      <span *ngIf="!hoveredItem.IsOurVehicle">Total Adverts: {{ getTotalAdvertsCount(hoveredItem) }}</span>
   </div>
</div>
