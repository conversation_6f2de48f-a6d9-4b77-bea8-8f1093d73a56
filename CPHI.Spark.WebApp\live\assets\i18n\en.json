{"AccountSettings": "Account <PERSON><PERSON>", "LocaleCode": "en-gb", "DashboardPages": "Dashboard Pages", "GettingDeals": "Getting Deals", "Release": "Release", "PerformanceEnhancements": "Performance Enhancements", "BugFixes": "Bug Fixes", "TapToStart": "Tap To Start", "SitesPerformanceTables": "Sites Performance Tables", "AreYouSure": "Are You Sure", "WipsAgingSummary": "Wips Aging Summary", "Common_Loading": "Loading...", "Common_Achievement": "Acheivement", "Common_Actual": "Actual", "Common_AddOnCount": "Add-on Product Count", "Common_AddOnProfit": "Add-on Profit", "Common_AddOns": "Add-Ons", "Common_Aftersales": "Aftersales", "Common_All": "All", "Common_AllAges": "All Ages", "Common_Anytime": "Anytime", "Common_AreYouSure": "Are you sure?", "Common_Between": "Between", "Common_Bonuses": "Bonuses", "Common_Cancel": "Cancel", "Common_Clear": "Clear", "Common_Close": "Close", "Common_Colour": "Colour", "Common_Comments": "Comments", "Common_Commission": "Commission", "Common_Complete": "Complete", "Common_CoreUsed": "CoreUsed", "Common_CoS": "CoS", "Common_Cost": "Cost", "Common_Cumulative": "Cumulative", "Common_Custom": "Custom", "Common_Customer": "Customer", "Common_Daily": "Daily", "Common_Date": "Date", "Common_Dates": "Dates", "Common_DayLower": "day", "Common_Days": "Days", "Common_DaysLower": "days", "Common_Deal": "deal", "Common_Deals": "deals", "Common_Debts": "Debts", "Common_DeliveryDate": "Delivery Date", "Common_Demo": "Demo", "Common_Description": "Description", "Common_Detail": "Detail", "Common_Discount": "Discount", "Common_Done": "Done", "Common_Error": "Error", "Common_ExcludeLateCosts": "Exclude Late Costs", "Common_ExcludeOrders": "Exclude Orders", "Common_Finance": "Finance", "Common_FinanceProfit": "Finance Profit", "Common_Fleet": "Fleet", "Common_Franchise": "Franchise", "Common_Franchises": "Franchises", "Common_Friday": "Friday", "Common_FullMonthTarget": "Full Month Target", "Common_Gap": "Gap", "Common_GettingDeals": "Getting Deals...", "Common_GP": "GP", "Common_GPU": "GPU", "Common_Id": "Id", "Common_IncludeLateCosts": "Include Late Costs", "Common_IncludeOrders": "Include Late Orders", "Common_LastWeek": "Last Week", "Common_Make": "Make", "Common_Margin": "<PERSON><PERSON>", "Common_Metal": "Metal", "Common_MetalProfit": "Metal Profit", "Common_Model": "Model", "Common_ModelYear": "Model Year", "Common_Monday": "Monday", "Common_Month": "Month", "Common_MonthTarget": "Month Target", "Common_Name": "Name", "Common_Net": "Net", "Common_New": "New", "Common_NextWeek": "Next Week", "Common_No": "No", "Common_NoFranchises": "No Franchises", "Common_NoLower": "no", "Common_NoOrderType": "No Order Type", "Common_NoVehicleType": "No Vehicle Type", "Common_Now": "Now", "Common_Of": "of", "Common_Ok": "Ok", "Common_OnlyLateCosts": "Only Late Costs", "Common_OnlyOrders": "Only Orders", "Common_Order": "Order", "Common_OrderRate": "Order Rate", "Common_Orders": "orders", "Common_OrderType": "Order Type", "Common_Other": "Other", "Common_OtherProfit": "Other Profit", "Common_Over": "Over", "Common_PerUnit": "Per Unit", "Common_Products": "Products", "Common_Profit": "Profit", "Common_Reconnect": "Reconnect", "Common_Refresh": "Refresh", "Common_Reg": "Reg", "Common_Registered": "Registered", "Common_Relogin": "Relogin", "Common_Required": "Required", "Common_RestartingMessage": "Restarting Spark...", "Common_Sale": "Sale", "Common_Sales": "Sales", "Common_SalesExec": "Exec", "Common_Saturday": "Saturday", "Common_Search": "Search", "Common_ServicePlan": "Service Plan", "Common_Site": "Site", "Common_Sites": "Sites", "Common_SitesOverview": "Sites Overview", "Common_SortBy": "Sort By", "Common_StepUp": "Step Up", "Common_StockNumber": "Stock Number", "Common_Summary": "Summary", "Common_Sunday": "Sunday", "Common_Tactical": "Tactical", "Common_Target": "Target", "Common_ThisWeek": "This Week", "Common_Thursday": "Thursday", "Common_Today": "Today", "Common_ToGo": "To Go", "Common_Tomorrow": "Tomorrow", "Common_Total": "Total", "Common_TotalOf": "Total of", "Common_TotalProfit": "Total Profit", "Common_TotalSite": "Total Site", "Common_TotalValue": "Total Value", "Common_Trade": "Trade", "Common_Tuesday": "Tuesday", "Common_Unit": "Unit", "Common_Units": "Units", "Common_Used": "Used", "Common_Variant": "<PERSON><PERSON><PERSON>", "Common_VariantClass": "Variant Class", "Common_Vehicle": "Vehicle", "Common_VehicleAge": "Vehicle Age", "Common_VehicleClass": "Vehicle Class", "Common_Vehicles": "Vehicles", "Common_VehicleType": "Vehicle Type", "Common_vsBudget": "vs Budget", "Common_vsLastMonth": "vs Last Month", "Common_vsLastYear": "vs Last Year", "Common_vsThisMonth": "vs This Month", "Common_Warranty": "Warranty", "Common_Wednesday": "Wednesday", "Common_Week": "Week", "Common_WelcomeMessage": "Welcome to Spark...", "Common_WheelGuard": "WheelGuard", "Common_Wips": "WIPs", "Common_With": "With", "Common_Year": "Year", "Common_Yesterday": "Yesterday", "Dashboard_SalesByType": "Sales By Type", "Dashboard_ThisWeeksOrders": "This Weeks Orders", "Dashboard_NewRetail": "New Retail", "Dashboard_FinanceAndAddons": "Finance And Addons", "Dashboard_ActivityLevels_Over1Day": "Over 1 Day", "Dashboard_ActivityLevels_Over30Days": "Over 30 Day", "Dashboard_ActivityLevels_Over60Days": "Over 60 Day", "Dashboard_ActivityLevels_OverAWeek": "Over a Week", "Dashboard_ActivityLevels_Overdues": "Overdues", "Dashboard_ActivityLevels_Title": "Activity Levels w/c ", "Dashboard_ActivityLevels_TotalOverdue": "Total Overdue", "Dashboard_AgedWips": "Aged WIPs", "Dashboard_CitNow_ofSalesEnquiries": "of Sales Enquiries", "Dashboard_Debtors_BonusesAging": "Bonus Aging", "Dashboard_Debtors_DebtsAging": "Debts Aging", "Dashboard_Debtors_OfWhichOver30Days": "Of which: Over 30 days", "Dashboard_Debtors_OfWhichOver60Days": "Of which: Over 60 days", "Dashboard_Debtors_Title": "Debtors", "Dashboard_Debtors_TotalBonuses": "Total Bonuses", "Dashboard_Debtors_TotalDebts": "Total Debts", "Dashboard_Debtors_TotalWips": "Total Wips", "Dashboard_Debtors_WipsAging": "Wips Aging", "Dashboard_Donut_FleetPerformanceForTheMonth": "Fleet Performance For The Month", "Dashboard_Donut_NewPerformanceForTheMonth": "New Performance For The Month", "Dashboard_Donut_UsedPerformanceForTheMonth": "Used Performance For The Month", "Dashboard_Evhc_RedWork": "Red Work", "Dashboard_Evhc_Title": "Electronic Vehicle Health Check", "Dashboard_FinanceAddOnPerformance": "Finance AddOn Performance", "Dashboard_FinanceAddons_Title": "Finance Addons", "Dashboard_FleetDealsByType": "Fleet Deals By Type", "Dashboard_NewDealsBreakdown": "New Deals Breakdown", "Dashboard_NoAgedWips": "No Aged Wips", "Dashboard_PartsSales_Title": "Parts Sales", "Dashboard_PartsSales_PartsSalesVsTarget": "Parts Sales Vs Target", "Dashboard_PartsStock_OfTotalStock": "of Total Stock", "Dashboard_PartsStock_OverageStock": "Overage Stock", "Dashboard_PartsStock_StockCover": "Stock Cover", "Dashboard_PartsStock_Title": "Parts Stock", "Dashboard_PartsStock_TotalValue": "Total Value", "Dashboard_PartsStockValue": "Parts Stock Value", "Dashboard_Registrations_Title": "Registrations", "Dashboard_Registrations_Dacia": "Registrations Dacia", "Dashboard_Registrations_Renault": "Registrations Renault", "Dashboard_SalesmanEfficiency_Title": "Salesman Efficiency", "Dashboard_SalesPerformance_ExcludingTradeUnits": "Excluding Trade Units", "Dashboard_SalesPerformance_ForDeliveryInMonth": "For Delivery In Month", "Dashboard_SalesPerformance_IncludingTradeUnits": "Including Trade Units", "Dashboard_SalesPerformance_OrderRateAndProjection": "Order Rate And Projection", "Dashboard_SalesPerformance_ProjectedFinish": "Projected Finish", "Dashboard_SalesPerformance_ProjectedVs": "Projected Vs", "Dashboard_SalesPerformance_Title": "Sales Performance", "Dashboard_SalesPerformance_TotalVehicles": "Total Vehicles", "Dashboard_SalesPerformance_UnitsVsTarget": "Units Vs Target", "Dashboard_SalesPerformance_VehiclesOrdered": "Vehicles Ordered", "Dashboard_ServiceBookings_Title": "Service Bookings", "Dashboard_ServiceSales_SalesPerDay": "Sales Per Day", "Dashboard_ServiceSales_SalesVsTarget": "Sales Vs Target", "Dashboard_ServiceSales_Title": "Service Sales", "Dashboard_ServiceSales_WorkInProgress": "Work In Progress", "ServiceSalesVsTarget": "Service Sales Vs Target", "SiteCompare": "Site Compare", "Dashboard_SitePerformanceLeague_FleetUnits": "Fleet Units", "Dashboard_SitePerformanceLeague_NewMargin": "New Margin", "Dashboard_SitePerformanceLeague_NewUnits": "New Units", "Dashboard_SitePerformanceLeague_PartsSales": "Parts Sales", "Dashboard_SitePerformanceLeague_ServiceSales": "Service Sales", "Dashboard_SitePerformanceLeague_Title": "Site Performance League", "Dashboard_SitePerformanceLeague_UsedMargin": "Used Margin", "Dashboard_SitePerformanceLeague_UsedUnits": "Used Units", "Dashboard_StockOverage_NewVehiclesGreaterThan": "New Vehicles > ", "Dashboard_StockOverage_TradeVehiclesGreaterThan": "Trade Vehicles > ", "Dashboard_StockOverage_UsedVehiclesGreaterThan": "Used Vehicles > ", "Dashboard_StockReport_AgedOver": "Aged Over", "Dashboard_StockReport_AsAt": "As At", "Dashboard_StockReport_BranchDays": "Branch Days", "Dashboard_StockReport_GroupDays": "Group Days", "Dashboard_StockReport_Title": "Stock Report", "Dashboard_StockReport_UsingDaysAtBranch": "Using Days At Branch", "Dashboard_StockReport_UsingGroupDays": "Using Group Days", "Dashboard_ThisWeeksOrders_FleetOrdersTaken": "Fleet Orders Taken", "Dashboard_ThisWeeksOrders_NewOrdersTaken": "New Orders Taken", "Dashboard_ThisWeeksOrders_UsedOrdersTaken": "Used Orders Taken", "Dashboard_Title": "Dashboard", "Dashboard_UsedDealsByType": "Used Deals By Type", "Dashboard_UsedStockHealth": "Used Stock Health", "Dashboard_UsedStockMerchandising": "Used Stock Merchandising", "Dashboard_VoC_Title": "Voice of the Customer", "Dashboard_WipReport_Account": "Account", "Dashboard_WipReport_Age": "Age", "Dashboard_WipReport_Ageing": "Ageing", "Dashboard_WipReport_AsAtMonthEnd": "As At: Month End", "Dashboard_WipReport_AsAtNow": "As At: Now", "Dashboard_WipReport_BookingStatus": "Booking Status", "Dashboard_WipReport_Created": "Created", "Dashboard_WipReport_Department": "Department", "Dashboard_WipReport_DueIn": "Due In", "Dashboard_WipReport_MonthEnd": "Month End", "Dashboard_WipReport_Notes": "Notes", "Dashboard_WipReport_Provision": "Provision", "Dashboard_WipReport_Title": "WIP Report", "Dashboard_WipReport_WipNumber": "WIP Number", "Dashboard_WithPrepCost": "With Prep Cost", "DealDetails_Accessories": "Accessories", "DealDetails_AccountingDate": "Accounting Date", "DealDetails_BodyPrep": "Body Prep", "DealDetails_BrokerCost": "Broker <PERSON>st", "DealDetails_CosmeticInsurance": "Cosmetic Insurance", "DealDetails_DealRemoved": "Deal Removed", "DealDetails_Delivery": "Delivery", "DealDetails_FactoryBonus": "Factory Bonus", "DealDetails_FinanceAddOnProfit": "Finance AddOn Profit", "DealDetails_FinanceCo": "Finance Co", "DealDetails_FinanceCommission": "Finance Commission", "DealDetails_FinanceSubsidy": "Finance Subsidy", "DealDetails_Fuel": "Fuel", "DealDetails_GapInsurance": "Gap Insurance", "DealDetails_IntroCommission": "Intro Commission", "DealDetails_InvoiceDate": "Invoice Date", "DealDetails_IsDelivered": "Is Delivered?", "DealDetails_IsLateCost": "Is Late Cost?", "DealDetails_IsOnFinance": "Is On Finance?", "DealDetails_MechanicalPrep": "Mechanical Prep", "DealDetails_OrderDate": "Order Date", "DealDetails_PaintProtection": "Paint Protection", "DealDetails_PartExchange": "Part Exchange", "DealDetails_Pdi": "PDI", "DealDetails_ProPlusCommision": "Pro Plus Commision", "DealDetails_RciFinanceCommission": "Rci Finance Commission", "DealDetails_RegBonus": "Reg Bonus", "DealDetails_RegisteredDate": "Registered Date", "DealDetails_SelectCommission": "Select Commission", "DealDetails_StandardsCommission": "Standards Commission", "DealDetails_ProPlusCommission": "Pro Plus Commission", "DealDetails_StockDate": "Stock Date", "DealDetails_TinProfit": "Tin Profit", "DealDetails_Title": "Deal Details", "DealDetails_TyreAlloyInsurance": "<PERSON>re <PERSON>oy Insurance", "DealDetails_TyreInsurance": "Tyre Insurance", "DealDetails_Variant": "<PERSON><PERSON><PERSON>", "DealDetails_VariantText": "Variant Text", "DealDetails_VehicleUnit": "Vehicle Unit", "DealsDoneThisMonth_BroughtIn": "<PERSON><PERSON>t In", "DealsDoneThisMonth_Mtd": "MTD", "DealsDoneThisMonth_Title": "Deals Done This Month", "DealsDoneThisMonth_ProductsPerUnit": "Products Per Unit", "DealsDoneThisWeek_FuelSale": "Fuel Sale", "DealsDoneThisWeek_Title": "Deals Done This Week", "DealsDoneThisWeek_DealsDoneEachDay": "Deals Done Each Day", "Debts_AgedDebtsOn": "Aged Debts On", "Debts_AsAt": "As At", "Debts_DocDate": "Doc Date", "Debts_DueDate": "Due Date", "Debts_MonthEnd": "Month End", "DisconnectedMessage": "Disconnected from app.  Try Reconnect, if that fails, try Re-Login.", "FamilyPicker_AllFamilyCodes": "All Family Codes", "FamilyPicker_FamilyCodes": "Family Codes", "FamilyPicker_NoFamilyCodes": "No Family Codes", "FinanceAddons_Title": "Finance Addons", "HandoverDiary_Handover": "handover", "HandoverDiary_Handovers": "handovers", "HandoverDiary_Title": "Handover Diary", "HandoverDiary_NoHandovers": "No Handovers", "HandoverDiary_UpcomingHandovers": "Upcoming Handovers", "LoadingPage": "Loading Page...", "ManualReloadMessage": "Spark automatically updates with latest data as you change pages, but you can click here to manually reload all data from the server.", "Orderbook_Del": "Del", "Orderbook_IsDelivered": "D", "Orderbook_L": "L", "Orderbook_LateCost": "Late Cost.  Filter by using equals true.", "Orderbook_LoadingOrderbook": "Loading Orderbook...", "Orderbook_OrdersApprovedBetween": "Orders approved between", "Orderbook_Q": "Q", "Orderbook_Title": "Order book", "PartsStockAgeing_Title": "Parts Stock Ageing", "PerformanceLeague_Advisor": "Advisor", "PerformanceLeague_Bronze": "Bronze", "PerformanceLeague_Gold": "Gold", "PerformanceLeague_IncLeavers": "Include Leavers", "PerformanceLeague_Rank": "Pos.", "PerformanceLeague_Role": "Role", "PerformanceLeague_ShowAllSites": "Show All Sites", "PerformanceLeague_ShowDelivered": "Show Delivered", "PerformanceLeague_ShowProfit": "Show Profit", "PerformanceLeague_Silver": "Silver", "PerformanceLeague_Title": "Performance League", "PerformanceLeague_YearToDate": "Year To Date", "PerformanceLeague_League": "League", "PerformanceLeague_SortProfit": "Sort Profit", "ReportPortal_Title": "Report Portal", "SitePicker_AllSitesSelected": "All Sites Selected", "SitePicker_NoSitesSelected": "No Sites Selected", "SitePicker_MultipleSitesSelected": "Multiple Sites Selected", "StockItemModal_AccountStatus": "Account Status", "StockItemModal_CapCode": "CAP Code", "StockItemModal_CapId": "CAP Id", "StockItemModal_CapNotes": "CAP Notes", "StockItemModal_CapProven": "CAP Proven", "StockItemModal_CarryingValue": "Carrying Value", "StockItemModal_Chassis": "<PERSON><PERSON><PERSON>", "StockItemModal_DaysAtBranch": "Days At Branch", "StockItemModal_DaysInStock": "Days In Stock", "StockItemModal_DisposalRoute": "Disposal Route", "StockItemModal_Doors": "Doors", "StockItemModal_Fuel": "Fuel", "StockItemModal_Mileage": "Mileage", "StockItemModal_Options": "Options", "StockItemModal_PreviousSite": "Previous Site", "StockItemModal_PreviousUse": "Previous Use", "StockItemModal_ProgressCode": "Progress Code", "StockItemModal_Registration": "Registration", "StockItemModal_StockcheckLocation": "Stockcheck Location", "StockItemModal_StockcheckPhoto": "Stockcheck Photo", "StockItemModal_StockcheckTime": "Stockcheck Time", "StockItemModal_Title": "Stock Item <PERSON>", "StockItemModal_Transmission": "Transmission", "StockItemModal_VatQualifying": "VAT Qualifying", "StockItemModal_VehicleDetails": "Vehicle Details", "StockLanding_Title": "Stock Landing", "OrderTypePicker": "Choose Order Types", "DailyOrders_Title": "Daily Orders", "DailyOrders_ClickASiteToSeeDeals": "Click a Site To See Deals", "StockList_ChooseFields": "<PERSON><PERSON>", "StockList_ChooseNewReportName": "<PERSON><PERSON> New Report Name", "StockList_HelpText": "Click each report field to toggle whether it appears in the table. Re-arrange columns by dragging them within the table.", "StockList_LastSaved": "Last Saved", "StockList_OpenReport": "Open Report", "StockList_ReportName": "Report Name", "StockList_SaveAsNewReport": "Save As New Report", "StockList_SaveReport": "Save Report", "StockList_ThisWillOverwriteReport": "This will overwrite report:", "StockList_Title": "Stock List", "SuperCup_Title": "<PERSON>cia Duster Cup", "TodayMap_Title": "Today's Deals", "UpdateProfilePicture": "Update Profile Picture", "UserMaintenance_Title": "User Maintenance", "DeliveryDateChoices_ChooseDeliveryDates": "Choose Delivery Dates", "DeliveryDateChoices_DeliveryWeek": "Delivery Week", "DeliveryDateChoices_DeliveryMonth": "Delivery Month", "DeliveryDateChoices_DeliveryDay": "Delivery Day", "FranchisePicker_AllFranchises": "All Franchises", "FranchisePicker_NoFranchisesSelected": "No Franchises Selected", "DealsByDay_Title": "Deals By Day", "DealOptions_VehicleTypes": "Vehicle Types", "DealOptions_OrderTypes": "Order Types", "DealOptions_LateCosts": "Late Costs", "DealOptions_Orders": "Orders", "DealOptions_ResetChoices": "Reset Choices", "SalesSplit_NewDealsForTheMonthBreakdown": "New Deals For The Month Breakdown", "VehicleTypePicker_UsedVehicleTypes": "Used Vehicle Types", "VehicleTypePicker_AllVehicleTypes": "All Vehicle Types", "VehicleTypePicker_NoVehicleType": "No Vehicle Type", "VehicleTypePicker_VehicleType": "Vehicle Type", "Whiteboard_Delivered": "delivered", "Whiteboard_Title": "Whiteboard", "Whiteboard_ToDo": "To Do", "Whiteboard_Profit": "Profit"}