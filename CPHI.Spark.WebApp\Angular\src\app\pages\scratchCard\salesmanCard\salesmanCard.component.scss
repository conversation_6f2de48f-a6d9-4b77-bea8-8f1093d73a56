// Scratched effect
@mixin saw-tooth($width: 12px, $bg: #A9A9A9) {
    background-image:
      linear-gradient(135deg, $bg 50%, transparent 50%),
      linear-gradient(225deg, $bg 50%, transparent 50%),
      linear-gradient(45deg, $bg 50%, transparent 50%),
      linear-gradient(-45deg, $bg 50%, transparent 50%),
      linear-gradient(135deg, $bg 50%, transparent 50%),
      linear-gradient(45deg, $bg 50%, transparent 50%),
      linear-gradient(-135deg, $bg 50%, transparent 50%),
      linear-gradient(-45deg, $bg 50%, transparent 50%);
    background-position:
      top left, top left,
      bottom left, bottom left,
      top left, top left,
      top right, top right;
    background-size: $width $width;
    background-repeat: repeat-x, repeat-x, repeat-x, repeat-x, repeat-y, repeat-y, repeat-y, repeat-y;
}

// Payout spin
@keyframes spin {
    from { transform: rotateY(0deg); }
    to { transform: rotateY(-360deg); }
}
  
@keyframes depth {
    0% { text-shadow: 0 0 #44F05C; }
    25% { text-shadow: 1px 0 #44F05C, 2px 0 #44F05C, 3px 0 #44F05C, 4px 0 #44F05C, 5px 0 #44F05C; }
    50% { text-shadow: 0 0 #44F05C; }
    75% { text-shadow: -1px 0 #44F05C, -2px 0 #44F05C, -3px 0 #44F05C, -4px 0 #44F05C, -5px 0 #44F05C; }
    100% { text-shadow: 0 0 #44F05C; }
}

@keyframes tremble {
    0% { transform: translate(1px, 1px) rotate(-45deg) scale(1.6); }
    10% { transform: translate(-1px, -2px) rotate(-44deg) scale(1.6); }
    20% { transform: translate(-3px, 0px) rotate(-46deg) scale(1.6); }
    30% { transform: translate(3px, 2px) rotate(-45deg) scale(1.6); }
    40% { transform: translate(1px, -1px) rotate(-46deg) scale(1.6); }
    50% { transform: translate(-1px, 2px) rotate(-44deg) scale(1.6); }
    60% { transform: translate(-3px, 1px) rotate(-45deg) scale(1.6); }
    70% { transform: translate(3px, 1px) rotate(-44deg) scale(1.6); }
    80% { transform: translate(-1px, -1px) rotate(-46deg) scale(1.6); }
    90% { transform: translate(1px, 2px) rotate(-45deg) scale(1.6); }
    100% { transform: translate(1px, -2px) rotate(-46deg) scale(1.6); }
}

@keyframes blastOff {
    0% { transform: rotate(-45deg) scale(1.6); }
    100% { transform: translateY(-1000px) rotate(-45deg) scale(1.6); }
}

@keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
  }

.scratchCard {
    font-family: 'ScratchCard';
    width: 80%;
    margin: 10px;
    padding: 25px;
    border-radius: 20px;
    border: 3px solid #000;
    background-image: url('../../../../assets/imgs/events/scratchcard/diamondsScattered.png'), linear-gradient(to top left, var(--mainAppColourVeryDark) 0%, var(--mainAppColourExtremelyLight) 100%);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);

    .cardHeader, 
    .cardFooter {
        color: #fff;

        h1, h3 {
            text-shadow: 0.05em 0 black, 0 0.05em black, -0.05em 0 black, 0 -0.05em black, -0.05em -0.05em black, -0.05em 0.05em black, 0.05em -0.05em black, 0.05em 0.05em black; 
        }
    }

    .cardHeader {
        h1 {
            
            margin: 0;
        }

        h3 {
            
            margin: 0.5em 0;
        }
    }

    .cardFooter {
        h1 {
            
        }
    }

    .row {
        .col-4 {
            margin: 7.5px 0px;

            .scratcher {
                min-height: 90px;
                padding: 20px 10px;
                border-radius: 5px;
                border: 2px solid #000;
                background-color: #A9A9A9;
                background-position: center;
                background-repeat: no-repeat;
                background-size: 35px;
                cursor: pointer;
                position: relative;
                overflow: hidden;

                .ribbon {
                    margin: 0;
                    background: #44F05C;
                    color:black;
                    padding: 0.3em 0;
                    position: absolute;
                    top: 0;
                    right: 0;
                    transform: translateX(30%) translateY(0%) rotate(45deg);
                    transform-origin: top left;
                }

                .ribbon:before,
                .ribbon:after {
                    content: '';
                    position: absolute;
                    top: 0;
                    margin: 0 -1px;
                    width: 100%;
                    height: 100%;
                    background: #44F05C;
                }

                .ribbon:before {
                    right: 100%;
                }
                
                .ribbon:after {
                    left: 100%;
                }

                .row {
                    .col {
                        margin: 7.5px 0px;
                    }

                    .col,
                    .col-4 {
                        h1,
                        h3 {
                            margin: 0;
                        }
                    }

                    .col-4 {
                        h1.payout {
                            
                        }
                    }
                }
            }

            .scratcher.hidden {
                color: transparent;

                .ribbon {
                    display: none;
                }
            }

            .scratcher.hidden.rocket {
                background-image: url('../../../../assets/imgs/events/scratchcard/rocket.svg');
            }

            .scratcher.hidden.car {
                background-image: url('../../../../assets/imgs/events/scratchcard/car.svg');
            }

            .scratcher.hidden.pickup {
                background-image: url('../../../../assets/imgs/events/scratchcard/pickup.svg');
            }

            .scratcher.hidden.van {
                background-image: url('../../../../assets/imgs/events/scratchcard/van.svg');
            }

            .scratcher.hidden.truck {
                background-image: url('../../../../assets/imgs/events/scratchcard/truck.svg');
            }
        
            .scratcher.revealed {
                background-color: #fff;
                color: black;
                @include saw-tooth();

                .row {
                    .col-4 {
                        h1.payout {
                            color: #44F05C;
                            -webkit-text-stroke: black 2px;
                            transform: scale(1.4);
                            animation: spin 0.5s linear 0.1s, depth 0.1s linear 0.1s;
                        }
                    }
                }
            }

            .scratcher.revealed.rocket {
                .row {
                    .col-4 {
                        h1.payout {
                            opacity: 0;
                            animation: fadeIn 2s linear 2s forwards, spin 1s linear 5s, depth 1s linear 5s;
                        }
                    }
                }

                .rocket {
                    position: absolute;
                    left: 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    background-image: url('../../../../assets/imgs/events/scratchcard/rocket.svg');
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-position: center;
                    transform: rotate(-45deg) scale(0.5);
                    animation: tremble 1s none 1s, blastOff 4s none 2s;
                    animation-fill-mode: forwards;
                    z-index: 9999;
                }
            }

            .scratcher.locked {
                background-image: url('../../../../assets/imgs/events/scratchcard/lock.svg');
            }

            .multiply {
                
                opacity: 0;
                animation: fadeIn 2s linear 2s forwards, spin 1s linear 5s, depth 1s linear 5s;
            }
        }
        
        .col-4.doubleBonus {
            .scratcher {
                background-color: #d4af37;
            }
        
            .scratcher.revealed {
                background-color: #fff;
                @include saw-tooth(12px, #d4af37);

                .row {
                    .col-4 {
                        h1.payout {
                            color: #ffd700;
                        }
                    }
                }
            }
        }
    }

    .cardFooter {
        img {
            width: 75px;
        }
    }
}

.scratchCard.summer {
    font-family: 'ScratchCard2';
    background-image: url('../../../../assets/imgs/events/scratchcard/sun.png'), linear-gradient(to top left, #FAB65F 0%, #FF8600 100%);
    background-position: right center;
}

.scratchCard.autumn {
    font-family: 'ScratchCard2';
    background-image: linear-gradient(to top left, #91d944 0%, #68a234 100%);
}

.scratchCard.spring {
    font-family: 'ScratchCard2';
    background-image: url('../../../../assets/imgs/events/scratchcard/spring.png'), linear-gradient(to top left, #8eade7 0%, #5F8ADB 100%);
    background-position: center bottom;
}

.scratchCard.custom {
    font-family: 'ScratchCard2';
    background-image: url('../../../../assets/imgs/events/scratchcard/treasure-2.png'), linear-gradient(to top left, #1A2D6E 0%, #060434 100%);
    background-position: center bottom;
    background-size: auto;
}

#fade-in {
    transition: all 0.5s;
}