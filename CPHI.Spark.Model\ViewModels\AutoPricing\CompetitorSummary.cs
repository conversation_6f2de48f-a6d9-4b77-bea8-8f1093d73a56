﻿using CPHI.Spark.Model.Services;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
    public class CompetitorSummary
    {
        public CompetitorSummary() { }
        public CompetitorSummary(List<VehicleValuationCompetitorAnalysis> competitorAnalyses, int advertisedPrice, int valuation, int minPlate, int maxPLate, int radius) {
            decimal pricePosition = (decimal)valuation != 0 ? advertisedPrice / (decimal)valuation : 0;

            CompetitorVehicles = competitorAnalyses.ConvertAll(x => new CompetitorVehicle(x));
            var priceRankItem = CompetitorVehicles.OrderBy(p => p.AdvertisedPrice)
                           .Select((product, index) => new { product.AdvertisedPrice, Index = index })
                           .Where(x => x.AdvertisedPrice < advertisedPrice)
                           .LastOrDefault();
            int priceRank = priceRankItem != null ? priceRankItem.Index + 2 : 1;


            var valueRankItem = CompetitorVehicles.OrderBy(p => p.PricePosition)
                           .Select((product, index) => new { product.PricePosition, Index = index })
                           .Where(x => x.PricePosition < pricePosition)
                           .LastOrDefault();

            int valueRank = valueRankItem != null ? valueRankItem.Index + 2 : 1;
            PricePosition = pricePosition;
            CompetitorVehicleCount = CompetitorVehicles.Count + 1;
            PriceRank = priceRank;
            ValueRank = valueRank;
            MinPlate = minPlate;
            MaxPlate = maxPLate;
            Radius = radius;
        }
      


        public CompetitorSummary(
           VehicleListing itemIn,
           decimal homeLong,
           decimal homeLat,
           int advertisedPrice,
           int valuation,
           int radius,
           List<string> competitorTypesToUse,
           bool hasOurVehicle)
        {
            decimal pricePosition = (decimal)valuation != 0 ? advertisedPrice / (decimal)valuation : 0;

            if (itemIn.results != null)
            {
                CompetitorVehicles = itemIn.results.ConvertAll(x => new CompetitorVehicle(x, homeLong, homeLat));
                if (competitorTypesToUse != null)
                {
                    CompetitorVehicles = CompetitorVehicles.Where(x => competitorTypesToUse.Contains(x.Segment)).ToList();
                }
            }
            else
            {
                CompetitorVehicles = new List<CompetitorVehicle>();
            }

            var priceRankItem = CompetitorVehicles.OrderBy(p => p.AdvertisedPrice)
                           .Select((product, index) => new { product.AdvertisedPrice, Index = index })
                           .Where(x => x.AdvertisedPrice < advertisedPrice)
                           .LastOrDefault();
            int priceRank = priceRankItem != null ? priceRankItem.Index + 2 : 1;


            var valueRankItem = CompetitorVehicles.OrderBy(p => p.PricePosition)
                           .Select((product, index) => new { product.PricePosition, Index = index })
                           .Where(x => x.PricePosition < pricePosition)
                           .LastOrDefault();

            int valueRank = valueRankItem != null ? valueRankItem.Index + 2 : 1;
            PricePosition = pricePosition;
            CompetitorVehicleCount = CompetitorVehicles.Count + ((hasOurVehicle) ? 1 : 0);
            PriceRank = priceRank;
            ValueRank = valueRank;




            Radius = radius;
        }
        public List<CompetitorVehicle> CompetitorVehicles { get; set; }
        public decimal PricePosition { get; set; }
        public int CompetitorVehicleCount { get; set; }
        public int PriceRank { get; set; }
        public int ValueRank { get; set; }
        public int MinPlate { get; set; }
        public int MaxPlate { get; set; }
        public int Radius { get; set; }
    }
}
