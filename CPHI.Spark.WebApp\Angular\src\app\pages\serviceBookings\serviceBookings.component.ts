import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { GridApi } from 'ag-grid-community';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { DailyBookingSummary } from "../../model/DailyBookingSummary";
import { ServiceBookingsSiteRow } from "../../model/ServiceBookingsSiteRow";
import { Day, SiteVM, } from "../../model/main.model";
import { GetDataMethodsService } from '../../services/getDataMethods.service';
import { ServiceBookingsService } from "./serviceBookings.service";

@Component({
  selector: "app-serviceBookings",
  templateUrl: "./serviceBookings.component.html",
  styleUrls: ["./serviceBookings.component.scss", "./../../../styles/components/_agGrid.scss"],
})
export class ServiceBookingsComponent implements OnInit {
  @Input() public sites: SiteVM[];
  @Output() rowClicked = new EventEmitter();

  columnDefs: any[];
  rowData: SiteVM[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;

  pinnedBottom: SiteVM;
  days: Day[];

  constructor(
    public getDataMethods: GetDataMethodsService,
    public service: ServiceBookingsService
  ) { }




  ngOnInit() {
    this.service.selections.triggerSpinner.next({ show: true, message: 'Loading...' });
    this.initParams();
    this.service.selections.bookingsSummary.site = null; //always load the page on group view
    this.getData()

  }


  initParams() {

    if (!this.service.selections.bookingsSummary) {
      this.service.selections.bookingsSummary = {
        showtables: false,
        updateEventEmitter: new EventEmitter(),
        startDate: this.service.constants.todayStart,
        sites: [],
        sitesIds: [],// this.service.constants.Sites.filter(s => s.IsActive).map(s => s.Id),
        site: null,
        serviceBookingDetailItems: []
      }
    }

  }




  getData() {

    this.getDataMethods.getServiceBookingSiteRows(this.service.selections.bookingsSummary.startDate).subscribe((results: any[]) => {


      this.service.selections.bookingsSummary.sites = results
      // console.log(this.service.selections.bookingsSummary.sites , "this.service.selections.bookingsSummary.serviceBookingDetailItems!")

      if(this.service.selections.bookingsSummary.sites)
      {
        this.service.selections.bookingsSummary.sites.forEach(x => {

          x.DailyBookingSummaries.sort((a: DailyBookingSummary, b: DailyBookingSummary) => {
              return a.DayDate.getTime() - b.DayDate.getTime();  
            });

        })
      }

      //ok have finished
      this.service.selections.bookingsSummary.updateEventEmitter.next(true)
      this.service.selections.bookingsSummary.showtables = true;
      this.service.selections.triggerSpinner.next({ show: false })
    })


  }






  selectSite(site: ServiceBookingsSiteRow) {
    if (site.IsRegion) return;

    //stuff
    this.service.selections.triggerSpinner.next({ show: true, message: this.service.constants.translatedText.Loading })

    var siteIds: number[] = [];
    if (site.IsTotal === false) {
      siteIds.push(site.SiteId);
    }
    else {
      siteIds = this.service.selections.bookingsSummary.sites.map(x => x.SiteId);
    }

    this.getDataMethods.getServiceBookingsDetail(siteIds).subscribe((results: any[]) => {


      this.service.selections.bookingsSummary.serviceBookingDetailItems = results

      

      this.service.selections.bookingsSummary.site = site

      //ok have finished
      this.service.selections.bookingsSummary.updateEventEmitter.next(true)
      this.service.selections.bookingsSummary.showtables = true;
      this.service.selections.triggerSpinner.next({ show: false })
    })


  }


  cancelChosenSite() {
    this.service.selections.triggerSpinner.next({ show: true, message: this.service.constants.translatedText.Loading })
    this.service.selections.bookingsSummary.site = null;
    this.getData()
  }

  changeDay(amount: number) {

    this.service.selections.bookingsSummary.startDate = this.service.constants.addDays(this.service.selections.bookingsSummary.startDate, amount)
    this.getData()
  }

  makeDays() {
    this.days = this.service.constants.makeDays(0, -8, 19, 0);
  }

  selectDay(date: Date) {
    this.service.selections.bookingsSummary.startDate = date;
    this.getData()
  }

}