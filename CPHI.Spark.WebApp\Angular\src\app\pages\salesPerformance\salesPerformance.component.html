<nav class="navbar">

  <nav class="generic">
    <h4 id="pageTitle">
      <div>

        {{constants.translatedText.Dashboard_SalesPerformance_Title}}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>


    <ng-container *ngIf="service" class="buttonGroup topDropdownButtons">

      <!-- Report Type selector -->
      <div class="buttonGroup comparativeSelect">
        <button class=" btn btn-primary" *ngFor="let reportType of salesPerformanceReportTypes"
          (click)="selectReportType(reportType)" [ngClass]="{'active':reportType==service.salesPerformanceReportType}">
          {{reportTypeTranslated(reportType)}}

        </button>
      </div>

      <!-- Franchise selector -->
      <franchisePicker *ngIf="constants.environment.salesPerformance_showFranchisePicker"
        [franchisesFromParent]="service?.franchises" [buttonClass]="'buttonGroupLeft'"
        (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>


      <!-- Late Costs -->
      <div *ngIf="constants.environment.lateCostPicker" ngbDropdown dropright class="d-inline-block">
        <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>{{service?.lateCostOption?.name}}</button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1" *ngIf="constants.environment.lateCostPicker">
          <button *ngFor="let lateCostOption of constants.lateCostOptions"
            (click)="selectLateCostOption(lateCostOption)" ngbDropdownToggle class="manualToggleCloseItem"
            ngbDropdownItem>{{lateCostOption.name}}</button>

        </div>
      </div>

      <!-- Orders options -->
      <div ngbDropdown dropright class="d-inline-block"
        *ngIf="constants.environment.salesPerformance_showIncludeExcludeOrders">
        <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>{{service?.orderOption?.name}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <button *ngFor="let orderOption of constants.orderOptions" (click)="selectOrderOption(orderOption)"
            ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>{{orderOption.name}}</button>

        </div>
      </div>


      <!-- Include trade units -->
      <div ngbDropdown dropright class="d-inline-block" *ngIf="constants.environment.salesPerformance_showTradeUnitButtons">

        <button [ngClass]="{
          'buttonGroupCenter': constants.environment.salesPerformance_showMotabilityButtons,
          'buttonGroupRight': !constants.environment.salesPerformance_showMotabilityButtons
        }" 
        class="btn btn-primary" ngbDropdownToggle>

          <span *ngIf="service.includeTradeUnits">{{
            constants.translatedText.Dashboard_SalesPerformance_IncludingTradeUnits }}</span>
          <span *ngIf="!service.includeTradeUnits">{{
            constants.translatedText.Dashboard_SalesPerformance_ExcludingTradeUnits }}</span>

        </button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <button (click)="includeTradeUnits(true)" ngbDropdownToggle class="manualToggleCloseItem"
            ngbDropdownItem>{{constants.translatedText.Dashboard_SalesPerformance_IncludingTradeUnits}}</button>
          <button (click)="includeTradeUnits(false)" ngbDropdownToggle class="manualToggleCloseItem"
            ngbDropdownItem>{{constants.translatedText.Dashboard_SalesPerformance_ExcludingTradeUnits}}</button>

        </div>
      </div>

      <!-- Include Motability -->
      <div ngbDropdown dropright class="d-inline-block"
        *ngIf="constants.environment.salesPerformance_showMotabilityButtons">
        <button class=" buttonGroupRight btn btn-primary" ngbDropdownToggle>

          <span *ngIf="service.includeMotabUnits">{{
            constants.translatedText.Dashboard_SalesPerformance_IncludingMotability }}</span>
          <span *ngIf="!service.includeMotabUnits && !service.onlyMotabUnits">{{
            constants.translatedText.Dashboard_SalesPerformance_ExcludingMotability }}</span>
          <span *ngIf="!service.includeMotabUnits && service.onlyMotabUnits">
            {{constants.translatedText.Dashboard_SalesPerformance_OnlyMotability}}
          </span>

        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <button (click)="includeMotabUnits(true)" ngbDropdownToggle class="manualToggleCloseItem"
            ngbDropdownItem>{{constants.translatedText.Dashboard_SalesPerformance_IncludingMotability}}</button>
          <button (click)="includeMotabUnits(false)" ngbDropdownToggle class="manualToggleCloseItem"
            ngbDropdownItem>{{constants.translatedText.Dashboard_SalesPerformance_ExcludingMotability}}</button>
          <button (click)="onlyMotabUnits(true)" ngbDropdownToggle class="manualToggleCloseItem"
          ngbDropdownItem>{{constants.translatedText.Dashboard_SalesPerformance_OnlyMotability}}</button>
        </div>
      </div>

    </ng-container>



  </nav>

  <nav class="pageSpecific"></nav>
</nav>



<!-- Main Page -->
<div class="content-new">
  <div class="content-inner-new">

    <div class="d-flex justify-content-between align-items-start">

      <!-- ##################################################### -->
      <!-- A. The choice buttons on the left -->
      <!-- ##################################################### -->

      <!-- 1. + 2. If we are choosing vsLastYr or vsBudget -->

      <div id="dateSelectionTables"
        *ngIf="service.salesPerformanceReportType!=='Custom' && service.salesPerformanceReportType!=='OrderRate' ">

      <!-- Department -->
      <div class="buttonGroup" id="departmentChoiceButtonsArea">
        <button class="btn btn-primary" 
          *ngFor="let department of constants.departments"
          [id]="department.Name + 'DepButton'" 
          [ngClass]="{'active': department.Name == service.department.Name}"
          (click)="selectDepartment(department)">
          
          <!-- Conditional Text Display -->
          <ng-container *ngIf="constants.environment.salesPerformance_showRetailSalesTranslation && department.ShortName == 'New'; else defaultText">
            {{ constants.translatedText.Dashboard_SalesPerformance_RetailSales }}
          </ng-container>
          <ng-template #defaultText>
            {{ department.translation }}
          </ng-template>

        </button>
      </div>



        <!-- Month picker -->
        <table id="" class="deliveryDate dateTable">

          <tbody>
            <tr>

              <td>
                <div class=" simpleDateChoice">

                  {{constants.translatedText.Dashboard_SalesPerformance_ForDeliveryInMonth}}

                </div>

                <!-- FOR SELECTING MONTH -->
                <div class="buttonGroup" >
                  <!-- previousMonth -->
                  <button class="btn btn-primary" (click)="changeMonth(service.deliveryDate,-1)"><i
                      class="fas fa-caret-left"></i></button>
                  <!-- direct select month -->
                  <button class="btn btn-primary directSelect" *ngIf="!service?.deliveryDate?.amSelectingMonth"
                    (click)="selectMonth(service.deliveryDate, service.deliveryDate.lastSelectedMonthStartDate)">
                    <span *ngIf="!service?.deliveryDate?.monthName">&nbsp;</span>
                    {{service?.deliveryDate?.startDate | cph:'month':0 | titlecase}}</button>
                  <!-- dropdownMonth -->
                  <div ngbDropdown *ngIf="service?.deliveryDate?.amSelectingMonth" class="d-inline-block"
                    [autoClose]="true">
                    <button (click)="makeMonths(0)" class="btn btn-primary centreButton"
                      ngbDropdownToggle>{{service.deliveryDate.startDate | cph:'month':0 |
                      titlecase}}</button>
                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                      <!-- the ngFor buttons -->
                      <button *ngFor="let month of months" (click)="selectMonth(service.deliveryDate, month.startDate)"
                        ngbDropdownItem>{{month.startDate | cph:'month':0 | titlecase}}</button>

                    </div>
                  </div>
                  <!-- nextMonth -->
                  <button class="btn btn-primary" (click)="changeMonth(service.deliveryDate,1)"
                    ><i
                      class="fas fa-caret-right"></i></button>
                </div>

              </td>



            </tr>
          </tbody>
        </table>

      </div>

      <!-- 3. If we are choosing order-rate -->
      <div id="dateSelectionTables" *ngIf="service.salesPerformanceReportType==='OrderRate'">

        <!-- Department -->
        <div class="buttonGroup" id="departmentChoiceButtonsArea">
          <button class=" btn btn-primary" *ngFor="let department of constants.departments"
            [id]="department.Name + 'DepButton'" [ngClass]="{'active':department.Name==service.department.Name}"
            (click)="selectDepartment(department)">{{department.translation}}</button>
        </div>


        <!-- Month picker -->
        <table id="" class="deliveryDate dateTable">

          <tbody>
            <tr>

              <td>
                <div class=" simpleDateChoice">

                  {{constants.translatedText.Dashboard_SalesPerformance_ForDeliveryInMonth}}

                </div>

                <!-- FOR SELECTING MONTH -->
                <div class="buttonGroup" >
                  <!-- previousMonth -->
                  <button class="btn btn-primary" (click)="changeMonth(service.deliveryDate,-1)"><i
                      class="fas fa-caret-left"></i></button>
                  <!-- direct select month -->
                  <button class="btn btn-primary directSelect" *ngIf="!service?.deliveryDate?.amSelectingMonth"
                    (click)="selectMonth(service.deliveryDate, service.deliveryDate.lastSelectedMonthStartDate)">
                    <span *ngIf="!service?.deliveryDate?.monthName">&nbsp;</span>
                    {{service?.deliveryDate?.startDate | cph:'month':0 | titlecase}}</button>
                  <!-- dropdownMonth -->
                  <div ngbDropdown *ngIf="service?.deliveryDate?.amSelectingMonth" class="d-inline-block"
                    [autoClose]="true">
                    <button (click)="makeMonths(0)" class="btn btn-primary centreButton"
                      ngbDropdownToggle>{{service.deliveryDate.startDate | cph:'month':0 |
                      titlecase}}</button>
                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                      <!-- the ngFor buttons -->
                      <button *ngFor="let month of months" (click)="selectMonth(service.deliveryDate, month.startDate)"
                        ngbDropdownItem>{{month.startDate | cph:'month':0 | titlecase}}</button>

                    </div>
                  </div>
                  <!-- nextMonth -->
                  <button class="btn btn-primary" (click)="changeMonth(service.deliveryDate,1)"
                    ><i
                      class="fas fa-caret-right"></i></button>
                </div>

              </td>



            </tr>
          </tbody>
        </table>

      </div>

      <!-- 4. If we are choosing custom.  -->
      <div id="dateSelectionTables" class="wide" *ngIf="service.salesPerformanceReportType==='Custom'">

        <!-- Department -->
        <div class="buttonGroup" id="departmentChoiceButtonsArea">
          <button class=" btn btn-primary" 
            *ngFor="let department of constants.departments"
            [id]="department.Name + 'DepButton'" 
            [ngClass]="{'active':department.Name==service.department.Name}"
            (click)="selectDepartment(department)">

                <!-- Conditional Text Display -->
                <ng-container *ngIf="constants.environment.salesPerformance_showRetailSalesTranslation && department.ShortName == 'New'; else defaultText">
                  {{ constants.translatedText.Dashboard_SalesPerformance_RetailSales }}
                </ng-container>
                <ng-template #defaultText>
                  {{ department.translation }}
                </ng-template>
                
          </button>
        </div>


        <!-- Choose Order date -->
        <table id="orderDate" class="dateTable">
          <thead>
            <tr>
              <th colspan="5" class="orderApprovedDate">

                <div class="dateAreaHeader">

                  <span>{{constants.environment.salesPerformance_description}}</span>

                  <span *ngIf="service.orderDate.startDate && service.orderDate.endDate">
                    {{service.orderDate.startDate|cph:'date':0}} and
                    {{service.orderDate.endDate|cph:'date':0}}
                  </span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <!-- Anytime -->
                <button class="btn btn-primary anyTimeButton" 
                  (click)="selectAnytime(service.orderDate,false)"
                  [disabled]="service.deliveryDate.amSelectingAnytime"
                  [ngClass]="{'active':service?.orderDate?.amSelectingAnytime}">Anytime</button>
              </td>
              <td>
                <!-- FOR SELECTING MONTH -->
                <div class="buttonGroup" [ngClass]="{'active':service?.orderDate?.amSelectingMonth}">
                  <!-- previousMonth -->
                  <button class="btn btn-primary" (click)="changeMonth(service.orderDate,-1)"><i
                      class="fas fa-caret-left"></i></button>
                  <!-- direct select month -->
                  <button class="btn btn-primary directSelect" *ngIf="!service?.orderDate?.amSelectingMonth"
                    (click)="selectMonth(service.orderDate, service.orderDate.lastSelectedMonthStartDate)">
                    <span *ngIf="!service?.orderDate?.monthName">&nbsp;</span>
                    {{service?.orderDate?.startDate | cph:'month':0 | titlecase}}
                  </button>
                  <!-- dropdownMonth -->
                  <div ngbDropdown *ngIf="service?.orderDate?.amSelectingMonth" class="d-inline-block">
                    <button (click)="makeMonths(0)" class="btn btn-primary centreButton" ngbDropdownToggle>
                      {{service.orderDate.startDate | cph:'month':0 | titlecase}}</button>
                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                      <!-- the ngFor buttons -->
                      <button *ngFor="let month of months" (click)="selectMonth(service.orderDate, month.startDate)"
                        ngbDropdownItem>{{month.startDate | cph:'month':0 | titlecase }}</button>

                    </div>
                  </div>
                  <!-- nextMonth -->
                  <button class="btn btn-primary" (click)="changeMonth(service.orderDate,1)"
                    ><i
                      class="fas fa-caret-right"></i></button>
                </div>
              </td>
              <td>
                <!-- FOR SELECTING WEEK -->
                <div class="buttonGroup" [ngClass]="{'active':service?.orderDate?.amSelectingWeek}">
                  <!-- previousWeek -->
                  <button class="btn btn-primary" (click)="changeWeek(service.orderDate,-1)"><i
                      class="fas fa-caret-left"></i></button>
                  <!-- direct select week -->
                  <button class="btn btn-primary directSelect weekButton" *ngIf="!service?.orderDate?.amSelectingWeek"
                    (click)="selectWeek(service.orderDate, service.orderDate.lastSelectedWeekStartDate)">
                    <span *ngIf="!service?.orderDate?.weekName">&nbsp;</span>
                    {{service?.orderDate?.weekName}}
                  </button>
                  <!-- dropdownWeek -->
                  <div ngbDropdown *ngIf="service?.orderDate?.amSelectingWeek" class="d-inline-block">
                    <button (click)="makeWeeks(0)" class="btn btn-primary centreButton weekButton"
                      ngbDropdownToggle>{{service.orderDate.weekName}}</button>
                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                      <!-- the ngFor buttons -->
                      <button *ngFor="let week of weeks" (click)="selectWeek(service.orderDate, week.startDate)"
                        ngbDropdownItem>{{week.name}}</button>

                    </div>
                  </div>
                  <!-- nextWeek -->
                  <button class="btn btn-primary" (click)="changeWeek(service.orderDate,1)"
                    ><i
                      class="fas fa-caret-right"></i></button>
                </div>
              </td>
              <td>
                <!-- FOR SELECTING DAY -->
                <div class="buttonGroup" [ngClass]="{'active':service?.orderDate?.amSelectingDay}">
                  <!-- previousDay -->
                  <button class="btn btn-primary" (click)="changeDay(service.orderDate,-1)"><i
                      class="fas fa-caret-left"></i></button>
                  <!-- direct select day -->
                  <button class="btn btn-primary directSelect dayButton" *ngIf="!service?.orderDate?.amSelectingDay"
                    (click)="selectDay(service.orderDate, service.orderDate.lastSelectedDayStartDate)">
                    <span *ngIf="!service?.orderDate?.dayName">&nbsp;</span>
                    {{service?.orderDate?.dayName}}</button>
                  <!-- dropdownDay -->
                  <div ngbDropdown *ngIf="service?.orderDate?.amSelectingDay" class="d-inline-block">
                    <button (click)="makeDays(0)" class="btn btn-primary centreButton dayButton"
                      ngbDropdownToggle>{{service.orderDate.dayName}}</button>
                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                      <!-- the ngFor buttons -->
                      <button *ngFor="let day of days" (click)="selectDay(service.orderDate, day.startDate)"
                        ngbDropdownItem>{{day.name}}</button>

                    </div>
                  </div>
                  <!-- nextDay -->
                  <button class="btn btn-primary" (click)="changeDay(service.orderDate,1)"
                    ><i
                      class="fas fa-caret-right"></i></button>
                </div>
              </td>
              <td>
                <button class="btn btn-primary" (click)="openDatePickerModal(true)"
                  [ngClass]="{'active':service?.orderDate?.amSelectingCustom}">
                  {{constants.translatedText.Custom}}
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Full set of choices for delivery-->
        <table id="" class="deliveryDate dateTable" *ngIf="service.salesPerformanceReportType=='Custom'">
          <thead>
            <tr>
              <th colspan="5">



                <div class="buttonGroup" *ngIf="service">
                  With
                  <button class="btn btn-primary deliveryDateVsInvoiceDate" *ngFor="let dateType of dateTypes"
                    [ngClass]="{'active': dateType == service.deliveryDate.dateType}"
                    (click)="setDeliveryDateType(dateType)"> {{dateType}}
                  </button>

                  between
                  <span *ngIf="service.deliveryDate.startDate && service.deliveryDate.endDate">
                    {{service.deliveryDate.startDate|cph:'date':0}} and
                    {{service.deliveryDate.endDate|cph:'date':0}}
                  </span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <!-- For selecting anytime -->
                <button class="btn btn-primary anyTimeButton" [disabled]="service.orderDate.amSelectingAnytime"
                  (click)="selectAnytime(service.deliveryDate,true)"
                  [ngClass]="{'active':service?.deliveryDate?.amSelectingAnytime}">Anytime</button>
              </td>
              <td>
                <!-- FOR SELECTING MONTH -->
                <div class="buttonGroup" [ngClass]="{'active':service?.deliveryDate?.amSelectingMonth}">
                  <!-- previousMonth -->
                  <button class="btn btn-primary" (click)="changeMonth(service.deliveryDate,-1)"><i
                      class="fas fa-caret-left"></i></button>
                  <!-- direct select month -->
                  <button class="btn btn-primary directSelect" *ngIf="!service?.deliveryDate?.amSelectingMonth"
                    (click)="selectMonth(service.deliveryDate, service.deliveryDate.lastSelectedMonthStartDate)">
                    <span *ngIf="!service?.deliveryDate?.monthName">&nbsp;</span>
                    {{service?.deliveryDate?.startDate | cph:'month':0 | titlecase}}</button>
                  <!-- dropdownMonth -->
                  <div ngbDropdown *ngIf="service?.deliveryDate?.amSelectingMonth" class="d-inline-block">
                    <button (click)="makeMonths(0)" class="btn btn-primary centreButton"
                      ngbDropdownToggle>{{service.deliveryDate.startDate | cph:'month':0 |
                      titlecase}}</button>
                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                      <!-- the ngFor buttons -->
                      <button *ngFor="let month of months" (click)="selectMonth(service.deliveryDate, month.startDate)"
                        ngbDropdownItem>{{month.startDate
                        | cph:'month':0 | titlecase}}</button>

                    </div>
                  </div>
                  <!-- nextMonth -->
                  <button class="btn btn-primary" (click)="changeMonth(service.deliveryDate,1)"
                    [ngClass]="{'active':service?.deliveryDate?.amSelectingMonth}"><i
                      class="fas fa-caret-right"></i></button>
                </div>
              </td>

              <td>
                <!-- FOR SELECTING DAY -->
                <div class="buttonGroup" [ngClass]="{'active':service?.deliveryDate?.amSelectingDay}">
                  <!-- previousDay -->
                  <button class="btn btn-primary" (click)="changeDay(service.deliveryDate,-1)"><i
                      class="fas fa-caret-left"></i></button>
                  <!-- direct select day -->
                  <button class="btn btn-primary directSelect" *ngIf="!service?.deliveryDate?.amSelectingDay"
                    (click)="selectDay(service.deliveryDate, service.deliveryDate.lastSelectedDayStartDate)">
                    <span *ngIf="!service?.deliveryDate?.dayName">&nbsp;</span>
                    {{service?.deliveryDate?.dayName}}</button>
                  <!-- dropdownDay -->
                  <div ngbDropdown *ngIf="service?.deliveryDate?.amSelectingDay" class="d-inline-block">
                    <button (click)="makeDays(0)" class="btn btn-primary centreButton"
                      ngbDropdownToggle>{{service.deliveryDate.dayName}}</button>
                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                      <!-- the ngFor buttons -->
                      <button *ngFor="let day of days" (click)="selectDay(service.deliveryDate, day.startDate)"
                        ngbDropdownItem>{{day.name}}</button>

                    </div>
                  </div>
                  <!-- nextDay -->
                  <button class="btn btn-primary" (click)="changeDay(service.deliveryDate,1)"
                    [ngClass]="{'active':service?.deliveryDate?.amSelectingDay}"><i
                      class="fas fa-caret-right"></i></button>
                </div>
              </td>
              <td>
                <button class="btn btn-primary" (click)="openDatePickerModal(false)"
                  [ngClass]="{'active':service?.deliveryDate?.amSelectingCustom}">Custom</button>
              </td>
            </tr>
          </tbody>
        </table>

      </div>











      <!-- ##################################################### -->
      <!-- B. The grids -->
      <!-- ##################################################### -->



      <!-- Vs Budget or Vs Last Year -->
      <div id="gridHolder"
        *ngIf="service.salesPerformanceReportType=='vsBudget' || service.salesPerformanceReportType=='vsLastYear'">
        <salesPerformanceTable *ngIf="service.siteRows" (clickedSite)="service.selectSite($event)" [reportType]="'sites'">
        </salesPerformanceTable>
        <div class="tableSpacer"></div>
        <salesPerformanceTable *ngIf="service.regionRows" (clickedSite)="service.selectSite($event)" [reportType]="'regions'">
        </salesPerformanceTable>
      </div>


      <!-- Order Rate -->
      <div id="gridHolder" *ngIf="service.salesPerformanceReportType=='OrderRate'">

        <salesPerformanceOrderRateTable *ngIf="service.orderRateSiteRows" (clickedSite)=" service.selectSite($event)" [reportType]="'sites'">
        </salesPerformanceOrderRateTable>
        <div class="tableSpacer"></div>
        <salesPerformanceOrderRateTable *ngIf="service.orderRateRegionRows" (clickedSite)="service.selectSite($event)" [reportType]="'regions'">
        </salesPerformanceOrderRateTable>

      </div>

      <!-- Custom -->
      <div id="gridHolder" class="narrow" *ngIf="service.salesPerformanceReportType=='Custom'">
        <salesPerformanceCustomTable *ngIf="service.siteRows" (clickedSite)="service.selectSite($event)" [reportType]="'sites'">
        </salesPerformanceCustomTable>
        <div class="tableSpacer"></div>
        <salesPerformanceCustomTable *ngIf="service.regionRows" (clickedSite)="service.selectSite($event)" [reportType]="'regions'">
        </salesPerformanceCustomTable>
      </div>


    </div>
  </div>
</div>