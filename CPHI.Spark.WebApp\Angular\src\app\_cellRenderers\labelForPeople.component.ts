import {Component} from "@angular/core";
import {ICellRendererAngularComp} from "ag-grid-angular";
import { FAndISalesExecRow } from "../model/main.model";
import { FandIPerformanceSummary } from '../model/sales.model';
import { ProfilePictureService } from "../services/profilePicture.service";
import { ProfilePicSize } from "../model/main.model";
import { ConstantsService } from "../services/constants.service";

@Component({
    selector: 'label-cellForPeople',
    template:    `

    <div *ngIf="!salesExecRow.IsTotal && salesExecRow.DmsId">
        <profilePicImage [personId]="salesExecRow.SalesmanId" [size]="profilePicSize"></profilePicImage>
         {{salesExecRow.SalesExecName}} | {{salesExecRow.DmsId}}
    </div>

    <div *ngIf="!salesExecRow.IsTotal && !salesExecRow.DmsId">
        <profilePicImage [personId]="salesExecRow.SalesmanId" [size]="profilePicSize"></profilePicImage>
         {{salesExecRow.SalesExecName}}
    </div>

    <span *ngIf="salesExecRow.IsTotal">{{ constants.translatedText.Total }}</span>

    `
    ,
    styles: [
        `
        img{width:5em;height:5em;border-radius:50%;margin-right:0.5em}
            `
    ]
})
export class LabelForPeopleComponent implements ICellRendererAngularComp {
    
    params: any ;
    salesExecRow:FAndISalesExecRow;
    public profilePicSize: ProfilePicSize;
    public profilePictureService: ProfilePictureService;

    constructor(
        public constants: ConstantsService
    ) { }

    agInit(params: any): void {
            this.profilePicSize = ProfilePicSize.medium;
            this.salesExecRow = params.node.data;
     }

    refresh(): boolean {
        return false;
    }
}


