import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe } from "src/app/cph.pipe";
import { MenuItemNew, OrderbookTimePeriod } from "src/app/model/main.model";
import { Deal } from "src/app/model/sales.model";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { OrderBookService } from "../../../orderBook/orderBook.service";
import { DailyNetOrder, DashboardDataItem, DashboardDataPack, DashboardDataParams } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";




@Component({
  selector: 'thisWeekOrdersSimple',
  templateUrl: './thisWeekOrdersSimple.component.html',
  styleUrls: ['./thisWeekOrdersSimple.component.scss'],
})




export class ThisWeekOrdersSimpleComponent implements OnInit {

  @Input() public dept: string;
  @Input() dailyOrders: DailyNetOrder[];
  @Input() public initialStart: Date;


  @ViewChild('dealBlobs', { static: true }) dealBlobs: ElementRef;

  weekStart:Date;
  blobHeight: number;
  maxDealCount: number;
  dealCount:number;

  constructor(
    public modalService: NgbModal,
    public constants: ConstantsService,
    public selections: SelectionsService,
    private router: Router,
    public cphPipe: CphPipe,
    public getDataService: GetDataMethodsService,
    public service: DashboardService,
    public orderBookService: OrderBookService
  ) {

  }



  ngOnInit(): void {;

    this.initParams();

  }



  initParams() {

    this.weekStart = new Date(this.initialStart)
    this.remakeTile();
  }
  
  remakeTile(){
    this.dealCount = this.constants.sum(this.dailyOrders.map(x=>x.OrderCount))
    this.calcBlobHeights();
  }
  
  trackByFunction(index: number, deal: Deal) { return index; }
  
  changeWeek(days: number) {
    this.weekStart = this.constants.addDays(this.weekStart, days);


    if(this.dept == 'New')
    {
      this.service.thisWeeksOrdersStartDateNew = this.weekStart;
    }

    if(this.dept == 'Fleet')
    {
      this.service.thisWeeksOrdersStartDateFleet = this.weekStart;
    }

    if(this.dept == 'Used')
    {
      this.service.thisWeeksOrdersStartDateUsed = this.weekStart;
    }

    this.getData();
  }
  
  
  getData(){
    let dataItems = [];
    
    let parms: DashboardDataParams = {
      SiteIds: this.service.chosenSites.map(x=>x.SiteId).join(','),
      DataItems: dataItems,
      Department:''
    }

    if (this.dept == 'New') {
      dataItems.push(DashboardDataItem.DailyNetOrdersNew); 
      parms.WeekStartOrdersTileNew = this.service.thisWeeksOrdersStartDateNew;  
    }

    if (this.dept == 'Fleet') {
        dataItems.push(DashboardDataItem.DailyNetOrdersFleet);
        parms.WeekStartOrdersTileFleet = this.service.thisWeeksOrdersStartDateFleet; 
    }

    if (this.dept == 'Used') {
        dataItems.push(DashboardDataItem.DailyNetOrdersUsed);
        parms.WeekStartOrdersTileUsed = this.service.thisWeeksOrdersStartDateUsed; 
    }
    
    this.getDataService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {
      
      if (this.dept == 'New') { Object.assign(this.dailyOrders, res.DailyNetOrdersNew) }
      if (this.dept == 'Fleet') { Object.assign(this.dailyOrders, res.DailyNetOrdersFleet) }
      if (this.dept == 'Used') { Object.assign(this.dailyOrders, res.DailyNetOrdersUsed) }
      this.remakeTile();
    })
  }



  barHeight(dayData: DailyNetOrder) {
    let netOrders = dayData.OrderCount;
    let displayHeight = this.constants.div(Math.abs(netOrders), this.maxDealCount) * 10;
    return displayHeight;
  }


 




  calcBlobHeights() {
    this.maxDealCount = 0;
    this.dailyOrders.forEach(dailyNetOrder => {
      if (Math.abs(dailyNetOrder.OrderCount) > this.maxDealCount) { this.maxDealCount = Math.abs(dailyNetOrder.OrderCount) }
    })

    if (this.dealBlobs) {
      let height = this.dealBlobs.nativeElement.clientHeight
      if (this.maxDealCount > 0) {
        this.blobHeight = Math.min(20, height / this.maxDealCount)
      } else {
        this.blobHeight = 20
      }
    }
  }



  





  navigateToOrderBook(day: DailyNetOrder, loadWholeWeek:boolean) {

    this.orderBookService.initOrderbook();

    const endDate = loadWholeWeek ? this.constants.addDays(day.DayDate,7) : day.DayDate

    let department = this.constants.departments.find(x=>x.ShortName === this.dept)

    this.orderBookService.orderDate.startDate = day.DayDate;
    this.orderBookService.orderDate.endDate = endDate;
    this.orderBookService.orderDate.timePeriod = OrderbookTimePeriod.Day;
    this.orderBookService.orderDate.lastChosenDay = day.DayDate;
    
    this.orderBookService.accountingDate.dateType = this.constants.environment.orderBook.defaultDateType;
    this.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Anytime;
    this.orderBookService.accountingDate.startDate = day.DayDate;
    this.orderBookService.accountingDate.endDate = this.constants.addYears(this.constants.appStartTime,2);

    this.orderBookService.orderTypeTypes = this.constants.clone(department.OrderTypeTypes);
    this.orderBookService.vehicleTypeTypes = this.constants.clone(department.VehicleTypeTypes);

    this.orderBookService.salesExecName = null;
    this.orderBookService.salesExecId = null;

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Orderbook_LoadingOrderbook })
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/orderBook');
    if (menuItem) { this.constants.navigateByUrl(menuItem); }  //, 'operationreports'

  }





}


