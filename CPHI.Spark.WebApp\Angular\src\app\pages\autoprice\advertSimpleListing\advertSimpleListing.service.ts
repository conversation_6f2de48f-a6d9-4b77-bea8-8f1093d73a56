import { DatePipe } from "@angular/common";
import { EventEmitter, Injectable } from "@angular/core";
import { CphPipe } from "src/app/cph.pipe";
import { GetVehicleAdvertsWithRatingsParams } from "src/app/model/GetVehicleAdvertWithRatingsParams";
import { VehicleAdvertWithRating, VehicleAdvertWithRatingDTO } from "src/app/model/VehicleAdvertWithRating";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { PriceBoardsService } from "src/app/services/priceBoards.service";
import { SelectionsService } from "src/app/services/selections.service";
import { VehicleAdvertFilterResult } from "../../../model/VehicleAdvertFilterResult";
import { FilterChoice } from "src/app/model/FilterChoice";
import { SortField } from "./advertSimpleListing.component";
import { GlobalParamsService } from "src/app/services/globalParams.service";
import { GlobalParamKey } from "src/app/model/GlobalParam";
import { TableLayoutManagementService} from "../../../components/tableLayoutManagement/tableLayoutManagement.service";
import { TableLayoutManagementParams} from "../../../model/TableLayoutManagementParams";


@Injectable({
  providedIn: 'root'
})


export class AdvertSimpleListingService {

  private static instance: AdvertSimpleListingService;

  allLifecycleStatuses: string[] = null;
  chosenLifecycleStatuses: Set<string>;

  rowData: VehicleAdvertWithRating[]; // this is the original data we got from the back end.   We don't change this.
  get rowDataAfterNavbarFilters(): VehicleAdvertWithRating[] {  // this is a mid layer of data.  represents the core unchanging rowData, but filtered for the navbar filters
    return this.rowData.filter(
      x => (this.chosenVehicleTypes == null || this.chosenVehicleTypes.has(x.VehicleTypeDesc)) &&
        (this.chosenLifecycleStatuses == null || this.chosenLifecycleStatuses.has(x.LifecycleStatus))
    );
  }
  rowDataFiltered: VehicleAdvertWithRating[];  // this is the lowest level, transient list which is used to show the cards
  chosenDate: string; //2023, 11, 12

  filterModel: any;

  includeNewVehicles: boolean;
  includeUnPublishedAds: boolean;
  useTestStrategy: boolean = false;
  showTestStrategySlider: boolean;


  newChoicesMade: EventEmitter<FilterChoice> = new EventEmitter();
  updatedDataEmitter: EventEmitter<VehicleAdvertFilterResult> = new EventEmitter();

  filterSortManagementParams: TableLayoutManagementParams;
  searchTerm: string = '';

  chosenVehicleTypes: Set<string>;
  hideDISSelector: boolean;

  constructor(
    private datePipe: DatePipe,
    public selectionsService: SelectionsService,
    private constantsService: ConstantsService,
    public agGridMethodsService: AGGridMethodsService,
    public cphPipe: CphPipe,
    public priceBoardsService: PriceBoardsService,
    private getDataMethodsService: GetDataMethodsService,
    public filterSortManagementService: TableLayoutManagementService,
    private globalParmsService: GlobalParamsService
  ) {
    // Store instance for access from other components
    AdvertSimpleListingService.instance = this;
  }

  // Static method to get the instance
  public static getInstance(): AdvertSimpleListingService {
    return AdvertSimpleListingService.instance;
  }

  initParams() {

    this.initialiseFilterSortManagement();

    if (!this.includeUnPublishedAds) {
      this.includeUnPublishedAds =
         (this.globalParmsService.getGlobalParam(GlobalParamKey.webAppShowDefaultShowUnPublishedVehicles) as boolean);
    }
    if (!this.includeNewVehicles) {
      this.includeNewVehicles = this.constantsService.autopriceEnvironment.defaultShowNewVehicles;
    }

    if (!this.chosenDate) { this.chosenDate = this.datePipe.transform(this.constantsService.todayStart, 'yyyy-MM-dd'); }

    if (!this.chosenVehicleTypes) {
      this.resetChosenVehTypes();
    }

    if (!this.chosenLifecycleStatuses) {
      this.resetChosenLifecycleStatues();
    }

  }

  resetChosenVehTypes() {
    if (this.constantsService.autopriceEnvironment.defaultVehicleTypes) {
      this.chosenVehicleTypes = new Set(this.constantsService.autopriceEnvironment.defaultVehicleTypes); //to make them regenerate
    }
    else {
      this.chosenVehicleTypes = null;
    }
  }

  filterByVehicleAds() {

    this.rowDataFiltered = this.rowDataFiltered.filter(x => this.chosenVehicleTypes.has(x.VehicleTypeDesc));

    let filterResult: VehicleAdvertFilterResult = {
      filterModel: this.filterModel,
      rowData: this.rowDataFiltered
    }

    this.updatedDataEmitter.emit(filterResult);
    if (!this.chosenLifecycleStatuses) {
      this.resetChosenLifecycleStatues();
    }
  }


  initialiseFilterSortManagement() {

    if (this.filterSortManagementParams) {
       // Restore menu which is overwritten if we navigated to Stock Report
       this.filterSortManagementService.parent = this.filterSortManagementParams;
       return;
    }

    this.filterSortManagementParams = {
      paginatedList: true, // Used to save different template state
      pageName: 'advertSimpleListing',
      ownTableStates: null,
      standardTableStates: null,
      sparkTableStates: null,
      sharedTableStates: null,
      availableTableStates: null,
      selectedTableState: null,
      loadedTableState: null,
      usersToShareWith: null,
      filterModel: [],
      refreshReportsListEmitter: new EventEmitter<void>(),
      refreshResultsListEmitter: new EventEmitter<void>(),
      chosenSortField: null,

      // DB: 5446
      gridApi: null,
      gridColumnApi: null,
      originalColDefs: null,
    };
    // filtersInPlace: [],
    this.filterSortManagementService.parent = this.filterSortManagementParams;
  }



  async getData() {

    this.selectionsService.triggerSpinner.next({ message: 'Loading...', show: true });

    const params: GetVehicleAdvertsWithRatingsParams = {
      Reg: null,
      Vin: null,
      RetailerSiteIds: null,
      EffectiveDate: this.chosenDate,
      UserEligibleSites: null,
      IncludeNewVehicles: this.includeNewVehicles,
      IncludeUnPublishedAdverts: this.includeUnPublishedAds,
      LifecycleStatuses: null,// ["DUE_IN","FORECOURT","IN STOCK NOT ON PORTAL","SALE_IN_PROGRESS"]
      VehicleTypes: null,
      UseTestStrategy: this.useTestStrategy,
    };

    try {

      const res: VehicleAdvertWithRatingDTO[] = await this.getDataMethodsService.getVehicleAdvertWithRatings(params)
      const adverts = res.map(x => new VehicleAdvertWithRating(x));

      this.rowData = adverts.sort((a, b) => a.AdId - b.AdId);
      this.rowDataFiltered = adverts.sort((a, b) => a.AdId - b.AdId);

      let disMapped: number[] = this.rowData.map(x => x.DaysInStock); //Map all DIS
      let disUnique: number[] = [...new Set(disMapped)]; //Get unique values

      if (disUnique.length === 1 && disUnique[0] === 0) {//If only 1 unique value && is 0, hide the DIS selector
        this.hideDISSelector = true;
      }

      //populate all statuses
      const allStatuses = new Set(this.rowData.map(x => x.LifecycleStatus));
      this.allLifecycleStatuses = [...allStatuses];

      this.showTestStrategySlider = this.showTestStrategySliderCheck();
      this.reFilterItems(false);

      if (this.searchTerm !== '') {
        this.onSearch();
      }

      if (this.filterSortManagementParams.filterModel) {
        this.reFilterItems(false);

        if (this.filterSortManagementParams.chosenSortField) {
          this.sortBy(this.filterSortManagementParams.chosenSortField);
        }
      }

      // if(this.chosenVehicleTypes && this.rowDataFiltered)
      // {
      //   this.filterByVehicleAds();
      // }

      this.selectionsService.triggerSpinner.next({ show: false });
    }
    catch (error: any) {
      console.error('Failed to retrieve vehicle adverts', error);
      this.selectionsService.triggerSpinner.next({ show: false });
    };

  }


  showTestStrategySliderCheck(): boolean {
    return (
      this.constantsService.userRetailerSites.some(site => site.AllowTestStrategy) && // At least one site with it enabled
      this.rowData?.some(row => row.TestStrategyPrice > 0) // At least one item with a price
    );
  }


  onNewChoicesMade(filterChoice: FilterChoice) {
    if (filterChoice.chosenValues.length == 0) {
      this.filterSortManagementParams.filterModel = this.filterSortManagementParams.filterModel.filter(x => x.field !== filterChoice.field)
    } else {
      const existingFilterChoice = this.filterSortManagementParams.filterModel.find(x => x.field === filterChoice.field);
      if (existingFilterChoice) {
        existingFilterChoice.chosenValues = filterChoice.chosenValues;
      } else {
        this.filterSortManagementParams.filterModel.push(filterChoice);
      }
    }
    this.reFilterItems(false);
  }

  reFilterItems(reset: boolean) {

    let filterModel;
    let result = this.rowDataAfterNavbarFilters; //always start from here
    if (this.searchTerm != null && this.searchTerm != '') {
      result = result.filter(item => {
        return Object.values(item).some(value =>
          String(value).toLowerCase().includes(this.searchTerm.toLowerCase())
        );
      });
    }


    ({ filterModel, result } = this.generateFilterResult(result, this.filterSortManagementParams));

    this.rowDataFiltered = result;
    this.filterModel = filterModel;

    const filterResult: VehicleAdvertFilterResult = {
      filterModel: filterModel,
      rowData: result,
      resetAllFilters: reset
    }


    this.updatedDataEmitter.emit(filterResult);
  }




  public generateFilterResult(result: VehicleAdvertWithRating[], filterSortManagementParams: TableLayoutManagementParams) {
    const filterModel = {};
    filterSortManagementParams.filterModel.forEach(filter => {
      let lostThisFilter = [];
      let passThisFilter = [];
      result.forEach(item => {
        if (filter.chosenValues.some(x => x === item[filter.field])) {
          passThisFilter.push(item);
        } else {
          //item does not pass filter
          lostThisFilter.push(item);
        }
      });
      result = passThisFilter;

      //now lost this filter reflects which rows were lost for this particular filter.   but need to filter them down to only those that would also have passed the other filters
      const otherFiltersInPlace = filterSortManagementParams.filterModel.filter(x => x.field !== filter.field);
      otherFiltersInPlace.forEach(otherFilter => {
        lostThisFilter = lostThisFilter.filter(x => otherFilter.chosenValues.some(y => y === x[otherFilter.field]));
      });
      filterModel[filter.field] = lostThisFilter;
    });

    return { filterModel, result };
  }

  getImage(imageURL: string) {
    return `<img style="height: 60px; width: 80px;" src=${imageURL} />`;
  }

  lastCommentGetter(row: VehicleAdvertWithRating): any {
    if (!row.LastCommentText || !row.LastCommentName) { return '' }
    return row.LastCommentText;
  }

  formatStrategyBandLabel(params) {
    if (!params.data) { return '' }
    if (params.data.VsStrategyBanding === 'OnStrategyPrice') return 'On Strategy';
    if (params.data.VsStrategyBanding === 'UnderPriced') return 'Underpriced';
    if (params.data.VsStrategyBanding === 'VeryUnderPriced') return 'V. Underpriced';
    if (params.data.VsStrategyBanding === 'OverPriced') return 'Overpriced';
    if (params.data.VsStrategyBanding === 'VeryOverPriced') return 'V. Overpriced';
    if (params.data.VsStrategyBanding === 'NoValuation') return 'No Valuation';
    if (params.data.VsStrategyBanding === 'NoStrategyPrice') return 'No Strat Price';
    if (params.data.VsStrategyBanding === 'NoAdvertisedPrice') return 'No Price';
    return params.data.VsStrategyBanding;
  }




  customAdLinkRenderer(autoTraderListingIdentifier: string, vehicleType: string) {

    let url: string = this.constantsService.buildAdUrl(autoTraderListingIdentifier, vehicleType);
    return `<a  id="iconWrapper" target="_blank" href="${url}">
    <i class="fas fa-car"></i></div>`;
  }

  onSearch() {

    this.reFilterItems(false);

    let filterResult: VehicleAdvertFilterResult = {
      filterModel: this.filterModel,
      rowData: this.rowDataFiltered
    }

    this.updatedDataEmitter.emit(filterResult);
  }

  sortBy(field: SortField) {
    this.filterSortManagementParams.chosenSortField = field;

    const customSorts: string[] = ['Age'];

    if (customSorts.includes(field.pretty)) {
      // Custom sorts
      if (field.pretty === 'Age') {
        this.sortAge(field);
      }
    } else {
      // Generic sorts
      if (field.sortAscending) {
        if (field.type === 'string') {
          this.rowData = this.rowData.sort((a, b) => a[field.ugly] ? a[field.ugly].localeCompare(b[field.ugly]) : null);
          this.rowDataFiltered = this.rowDataFiltered.sort((a, b) => a[field.ugly] ? a[field.ugly].localeCompare(b[field.ugly]) : null);
        }

        if (field.type === 'int') {
          this.rowData = this.rowData.sort((a, b) => a[field.ugly] - b[field.ugly]);
          this.rowDataFiltered = this.rowDataFiltered.sort((a, b) => a[field.ugly] - b[field.ugly]);
        }
      } else {
        if (field.type === 'string') {
          this.rowData = this.rowData.sort((a, b) => a[field.ugly] ? b[field.ugly].localeCompare(a[field.ugly]) : null);
          this.rowDataFiltered = this.rowDataFiltered.sort((a, b) => a[field.ugly] ? b[field.ugly].localeCompare(a[field.ugly]) : null);
        }

        if (field.type === 'int') {
          this.rowData = this.rowData.sort((a, b) => b[field.ugly] - a[field.ugly]);
          this.rowDataFiltered = this.rowDataFiltered.sort((a, b) => b[field.ugly] - a[field.ugly]);
        }
      }
    }

    let filterResult: VehicleAdvertFilterResult = {
      filterModel: this.filterModel,
      rowData: this.rowDataFiltered
    }

    this.updatedDataEmitter.emit(filterResult);
  }

  sortAge(field: SortField) {
    if (field.sortAscending) {
      this.rowData = this.rowData.sort((a, b) => new Date(a.FirstRegisteredDate).getTime() - new Date(b.FirstRegisteredDate).getTime());
      this.rowDataFiltered = this.rowDataFiltered.sort((a, b) => new Date(a.FirstRegisteredDate).getTime() - new Date(b.FirstRegisteredDate).getTime());
    } else {
      this.rowData = this.rowData.sort((a, b) => new Date(b.FirstRegisteredDate).getTime() - new Date(a.FirstRegisteredDate).getTime());
      this.rowDataFiltered = this.rowDataFiltered.sort((a, b) => new Date(b.FirstRegisteredDate).getTime() - new Date(a.FirstRegisteredDate).getTime());
    }
  }


  resetChosenLifecycleStatues() {
    this.chosenLifecycleStatuses = new Set(this.constantsService.autopriceEnvironment.lifecycleStatusDefault); //to make them regenerate
  }

  // Method to reset all filters and selections
  resetAllFilters() {
    // Reset vehicle types
    this.resetChosenVehTypes();

    // Reset lifecycle statuses
    this.resetChosenLifecycleStatues();

    // Clear the filter model

    this.filterSortManagementParams.filterModel.map(x => {
       x.chosenValues = [];
       this.newChoicesMade.emit(x);
    });

    this.filterSortManagementParams.filterModel = [];

    // Reset the search term
    this.searchTerm = '';

    // Re-filter items
    this.reFilterItems(true);
  }


}
