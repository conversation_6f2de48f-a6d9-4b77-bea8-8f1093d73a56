import { Injectable } from "@angular/core";

import { CphPipe } from "src/app/cph.pipe";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { CommissionSiteRow, CommissionPayoutPersonSummary, CommissionQualificationParams, BonusForPeopleRowsParams } from "./salesCommissionVindis.model";
import { CommissionPeopleTableVindisComponent } from "./tables/commissionPeopleTableVindis.component";
import { FormattedPeopleData } from "./tables/FormattedPeopleData";
import { CommissionSitesTableVindisComponent } from "./tables/commissionSitesTableVindis.component";
import { CommissionStatementTableVindisComponent } from "./tables/commissionStatementTableVindis/commissionStatementTableVindis.component";
import { TopBottomHighlightRule } from "src/app/model/TopBottomHighlightRule";





@Injectable({
  providedIn: 'root'
})


export class SalesCommissionVindisService {

  topBottomHighlightsSitesTable:TopBottomHighlightRule[]=[]
  topBottomHighlightsPeopleTable:TopBottomHighlightRule[]=[]

  chosenMonth: Date;

  siteRows: CommissionSiteRow[]
  peopleRows: CommissionPayoutPersonSummary[]

  chosenSite: CommissionSiteRow;
  chosenPerson: CommissionPayoutPersonSummary;

  mainTableRef: CommissionSitesTableVindisComponent;
  mainTableRefRegions: CommissionSitesTableVindisComponent;
  peopleTableRef: CommissionPeopleTableVindisComponent;
  statementTableRef: CommissionStatementTableVindisComponent
  showTables: boolean;

  showHalfYearlyBonus: boolean = false;
  showQtrlyBonus: boolean = false;

  ignoreAuditPass: boolean;

  bonusThresholdsH1: string[];
  bonusThresholdsH2: string[]; 
  bonusThresholdsFY: string[];
  bonusThresholdsFYPerc: string[];

  bonusPayouts: string[];

  criteriaMetOptionsHY: string[];
  criteriaMetOptionsQtrly: string[];
  criteriaMetOptionsAnnual: string[];

  isReviewer: boolean;

  showAnnualBonus: boolean;
  
  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataMethods: GetDataMethodsService,
    public cphPipe: CphPipe,
  ) {

  }


  initParams() {
    this.chosenMonth = this.constants.lastMonthStart;
  }

  getData() {

    if (this.selections.user.permissions.selfOnlyCommission) {

      this.getPeopleRows(null, true, this.selections.user.SiteId);

    } else {

      if (!!this.chosenPerson) {
        this.getPeopleRows(this.chosenSite, true);
      }
      if (!!this.chosenSite) {
        this.getPeopleRows(this.chosenSite)
      } else {
        this.getSiteRows()
      }

    }
  }


  getSiteRows() : void {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    this.getDataMethods.getCommissionSiteRows(this.chosenMonth, this.ignoreAuditPass).subscribe((res: CommissionSiteRow[]) => {

      this.siteRows = res

      // Populate the bonus column if on months Jun/Dec
      if(this.isBonusMonth(this.chosenMonth.getMonth()))
      {
        this.refreshBonuses('sites');
      }
      else
      {
        if (!!this.mainTableRef) { this.mainTableRef.dealWithNewData(); }
        if (!!this.mainTableRefRegions) { this.mainTableRefRegions.dealWithNewData(); }
      }

      this.selections.triggerSpinner.next({ show: false });
      this.showTables = true;
    }, () => {
    })
  }

  getPeopleRows(chosenSite?: CommissionSiteRow, updateStatementView?: boolean, siteId?: number) : void {

    let siteIds: number[] = [];

    if (siteId) {
      siteIds = [siteId];
    } else {

      if (chosenSite.IsRegion) {
        siteIds = this.constants.sitesActive.filter(x => x.IsSales && x.RegionDescription === chosenSite.Label).map(x => x.SiteId);
      } else if (this.chosenSite.IsTotal) {
        siteIds = this.constants.sitesActive.filter(x => x.IsSales).map(x => x.SiteId);
      } else {
        siteIds = [chosenSite.RowId]
      }

    }

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    this.getDataMethods.getCommissionPeopleRows(this.chosenMonth, siteIds,false, this.ignoreAuditPass).subscribe((res: CommissionPayoutPersonSummary[]) => {
      res.map(x=>{
        x.CommissionItems.map(y=>{
          if(!!y.DataItem.OrderDate){ y.DataItem.OrderDate = new Date(y.DataItem.OrderDate)}
          if(!!y.DataItem.AccountingDate){ y.DataItem.AccountingDate = new Date(y.DataItem.AccountingDate)}
        })
        x.Adjustments.map(adj=>{
          adj.CreatedDate = new Date(adj.CreatedDate)
        })
      })
      
      this.peopleRows = res;
      
      // Populate the bonus column if on months Jun/Dec
      if (!this.selections.user.permissions.selfOnlyCommission) {
        if (this.isBonusMonth(this.chosenMonth.getMonth())) {
          this.refreshBonuses('people');
        } else {
          if (!!this.peopleTableRef) { this.peopleTableRef.dealWithNewData(); }
        }
      }

      if (updateStatementView) this.chosenPerson = this.peopleRows.find(x => this.selections.user.permissions.selfOnlyCommission ? x.SalesmanName == this.selections.user.Name : x.SalesmanName == this.chosenPerson.SalesmanName);
      this.selections.triggerSpinner.next({ show: false });
      this.showTables = true;
    }, () => {
    })


  }


  onSiteTableClick(row: CommissionSiteRow) {
    this.showTables = false;
    //we are the sites table
    this.chosenSite = row;
    this.getData();
  }

  onPeopleTableClick(row: FormattedPeopleData) {
    //we are the people table
    this.chosenPerson = this.peopleRows.find(x => x.SalesmanName == row.Label);
    this.setCriteriaOptionsHY();
    this.setCriteriaOptionsQtrly();
    this.setCriteriaOptionsAnnual();
  }

  // FY bonus - 2024 onwards
  public getAnnualBonusSummaryForPerson(month: Date, personId: number): void
  {

    let parms: CommissionQualificationParams = {
        CriteriaMet : null,
        Month : month,
        PersonId : personId,
        IgnoreAuditPass: this.ignoreAuditPass,
        BonusType: 'FY'
    };

    this.getDataMethods.getBonusSummaryForPerson(parms).subscribe((res) => {
      
      this.chosenPerson.BonusSummary = res;
      this.setCriteriaOptionsAnnual();

    }, () => {

    })

  }


  public getBonusSummaryForPerson(month: Date, personId: number, bonusType: string): void
  {

    let parms: CommissionQualificationParams = {
        CriteriaMet : null,
        Month : month,
        PersonId : personId,
        IgnoreAuditPass: this.ignoreAuditPass,
        BonusType: bonusType
    };

    this.getDataMethods.getBonusSummaryForPerson(parms).subscribe((res) => {
      
      this.chosenPerson.BonusSummary = res;
      this.bonusThresholdsH1 = this.createBonusThresholdArrayForYear(this.chosenPerson.BonusSummary.H1Target);
      this.bonusThresholdsH2 = this.createBonusThresholdArrayForYear(this.chosenPerson.BonusSummary.H2Target);
      this.bonusThresholdsFY = this.createBonusThresholdArrayForYear(this.chosenPerson.BonusSummary.FYTarget);
      
      this.setCriteriaOptionsHY();
      this.setCriteriaOptionsQtrly();
      this.setCriteriaOptionsAnnual();
      

    }, () => {

    })

  }

  private createBonusThresholdArrayForYear(minThreshold: number): string[]
  {
    let thresholdArray = [];

    if(this.chosenMonth.getFullYear() == 2022)
    {
       thresholdArray = [1.0750, 1.1500, 1.2250];
    }

    if(this.chosenMonth.getFullYear() == 2023)
    {
       thresholdArray = [1.1000, 1.1750, 1.2500];
    }

    return this.createBonusThresholdArray(minThreshold, thresholdArray);
  }

  private createBonusThresholdArray(minThreshold: number, thresholdArray: number[]): string[]
  {
    let bonusThresholds = [];

    let band0 = '< ' + minThreshold;
    bonusThresholds.push(band0)

    let band1 = minThreshold + ' - ' + Math.round(minThreshold * thresholdArray[0] - 1);
    bonusThresholds.push(band1)

    let band2 = Math.round(minThreshold * thresholdArray[0])  + ' - ' + Math.round(minThreshold * thresholdArray[1] - 1);
    bonusThresholds.push(band2)

    let band3 = Math.round(minThreshold * thresholdArray[1]) + ' - ' + Math.round(minThreshold * thresholdArray[2]);
    bonusThresholds.push(band3)

    let band4 = '> ' + Math.round(minThreshold * thresholdArray[2]);
    bonusThresholds.push(band4)

    return bonusThresholds;
  }

  public setCommissionQualificationForPerson(month: Date, personId: number, criteriaMet: boolean, bonusType: string): void
  {

    let parms: CommissionQualificationParams = {
        CriteriaMet : criteriaMet,
        Month : month,
        PersonId : personId,
        IgnoreAuditPass: this.ignoreAuditPass,
        BonusType: bonusType
    };

    this.getDataMethods.setCommissionQualificationForPerson(parms).subscribe((res: boolean) => {
      
      this.getBonusSummaryForPerson(parms.Month, parms.PersonId, bonusType);
      
    }, () => {

    })

  }

  public setCriteriaOptionsHY(): void
  {
    const h1CriteriaMet = this.chosenPerson.BonusSummary.H1CriteriaMet;
    const h2CriteriaMet = this.chosenPerson.BonusSummary.H2CriteriaMet;
    
    // Jun
    if (this.chosenMonth.getMonth() === 5) {
      this.criteriaMetOptionsHY = h1CriteriaMet ? ['Yes', 'No'] : ['No', 'Yes'];
    // Dec
    } else if (this.chosenMonth.getMonth() === 11) {
      this.criteriaMetOptionsHY = h2CriteriaMet ? ['Yes', 'No'] : ['No', 'Yes'];
    } 
    
  }

  public setCriteriaOptionsQtrly(): void
  {
    const qtrlyCriteriaMet = this.chosenPerson.BonusSummary.QtrCriteriaMet;
    this.criteriaMetOptionsQtrly = qtrlyCriteriaMet ? ['Yes', 'No'] : ['No', 'Yes'];
  }

  public setCriteriaOptionsAnnual(): void
  {
    const annualCriteriaMet = this.chosenPerson.BonusSummary.FYCriteriaMet;
    this.criteriaMetOptionsAnnual = annualCriteriaMet ? ['Yes', 'No'] : ['No', 'Yes'];
  }

  // If a user changes bonus qualification, need to recalculate and amend rows without resetting cache
  public refreshBonuses(sitesOrPeople: string): void {
    switch (sitesOrPeople) {
      case 'people':
        this.refreshBonusesForPeopleRows();
        break;
      case 'sites':
        this.refreshBonusesForSiteRows();
        break;
    }
  }

  private refreshBonusesForPeopleRows() : void
  {

    let siteIds: number[] = [];

    //if(this.chosenSite.SiteDescription.includes('AutoNow')){ return; }

    if (this.chosenSite.IsSite) {
      siteIds = [this.chosenSite.SiteId];
    } else {

      if (this.chosenSite.IsRegion) {
        siteIds = this.constants.sitesActive.filter(x => x.IsSales && x.RegionDescription === this.chosenSite.Label).map(x => x.SiteId);
      } else if (this.chosenSite.IsTotal) {
        siteIds = this.constants.sitesActive.filter(x => x.IsSales).map(x => x.SiteId);
      } else {
        siteIds = [this.chosenSite.RowId]
      }

    };

    let parms: BonusForPeopleRowsParams = {
      Month : this.chosenMonth,
      SiteIds : siteIds,
      IgnoreAuditPass: this.ignoreAuditPass
    };

    let bonusSummaryForPeople;

    // This is not cached because it can be dynamically changed by reviewers
    this.getDataMethods.getBonusSummaryForPeopleRows(parms).subscribe((res) => {
      
      bonusSummaryForPeople = res;
       
      this.peopleRows.forEach(row => {

        if(this.chosenSite.SiteDescription)
        {
          if(!this.chosenSite.SiteDescription.includes('AutoNow'))
            {
              row.BonusSummary.HalfYearlyPayout = bonusSummaryForPeople.find(x => (x.PersonId == row.SalesmanId)) != null ? bonusSummaryForPeople.find(x => x.PersonId == row.SalesmanId).Bonus : 0;
            }
        }

        row.BonusSummary.QtrBonus = bonusSummaryForPeople.find(x => (x.PersonId == row.SalesmanId)) != null ? bonusSummaryForPeople.find(x => x.PersonId == row.SalesmanId).QtrBonus : 0;

      });

      if (!!this.peopleTableRef) { this.peopleTableRef.dealWithNewData(); }

    }, () => {

    })
  }

  private refreshBonusesForSiteRows() : void
  {

    // For this call only relevant parameter is Month
    let parms: CommissionQualificationParams = {
      CriteriaMet : null,
      Month : this.chosenMonth,
      PersonId : 0,
      IgnoreAuditPass: this.ignoreAuditPass,
      BonusType: 'HY'
    };

    // This is not cached because it can be dynamically changed by reviewers
    this.getDataMethods.getBonusSummaryForSites(parms).subscribe((res) => {
      
      let bonusSummaryForSites = res;

      this.siteRows.forEach(row => {

        row.Bonus = bonusSummaryForSites.find(x => (row.IsSite == true && x.SiteId == row.SiteId) || (row.IsRegion == true && row.RegionDescription == x.RegionName) || (row.IsTotal == true && 'Total' == x.RegionName) ) != null 
          ? bonusSummaryForSites.find(x => (row.IsSite == true && x.SiteId == row.SiteId) || (row.IsRegion == true && row.RegionDescription == x.RegionName) || (row.IsTotal == true && 'Total' == x.RegionName) ).Bonus : 0;
      
        row.QtrBonus = bonusSummaryForSites.find(x => (row.IsSite == true && x.SiteId == row.SiteId) || (row.IsRegion == true && row.RegionDescription == x.RegionName) || (row.IsTotal == true && 'Total' == x.RegionName) ) != null 
        ? bonusSummaryForSites.find(x => (row.IsSite == true && x.SiteId == row.SiteId) || (row.IsRegion == true && row.RegionDescription == x.RegionName) || (row.IsTotal == true && 'Total' == x.RegionName) ).QtrBonus : 0;
    
        row.Total += row.Bonus;
        row.Total += row.QtrBonus;
      });

      if (!!this.mainTableRef) { this.mainTableRef.dealWithNewData(); }
      if (!!this.mainTableRefRegions) { this.mainTableRefRegions.dealWithNewData(); }

    }, () => {

    })

  }

  private isBonusMonth(month: number) : boolean
  {
    // Jan/Mar/Jun/Sep/Dec
    let bonusMonths = [0, 2, 5, 8, 11];
    return bonusMonths.includes(month); 
  }

}