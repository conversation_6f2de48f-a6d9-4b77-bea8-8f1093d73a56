<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        <span>{{constants.translatedText.Dashboard_Evhc_Title}}</span>
        <span *ngIf="service.chosenSiteRow">: {{service.chosenSiteRow.Label}}</span>
      </div>
    </h4>

    <!-- FOR SELECTING MONTH -->
    <div class="buttonGroup">
      <!-- previousMonth -->
      <button class="btn btn-primary" (click)="changeMonth(-1)"><i class="fas fa-caret-left"></i></button>

      <!-- dropdownMonth -->
      <div ngbDropdown class="d-inline-block" [autoClose]="true">
        <button (click)="makeMonths()" class="btn btn-primary centreButton"
          ngbDropdownToggle>{{service.monthStartDate|cph:'month':0}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <!-- the ngFor buttons -->
          <button *ngFor="let month of months" (click)="selectMonth(month)" ngbDropdownItem>{{month.name}}</button>

        </div>
      </div>
      <!-- nextMonth -->
      <button class="btn btn-primary" (click)="changeMonth(1)"><i class="fas fa-caret-right"></i></button>
    </div>



    <!-- Work Types -->
    <div *ngIf="!constants.environment.evhc.eDynamixView" ngbDropdown dropright class="d-inline-block">
      <button class="btn btn-primary" ngbDropdownToggle>{{service.chosenWorkType}}</button>
      <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <button *ngFor="let workType of service.workTypes" (click)="selectWorkType(workType)"
          class="manualToggleCloseItem" ngbDropdownToggle ngbDropdownItem>{{workType}}</button>
      </div>
    </div>


  </nav>
</nav>

<div class="content-new">

  <div class="content-inner-new">

    <ng-container *ngIf="constants.environment.evhc.eDynamixView; else normalEVHCTables">

      <!-- If we want to see all sites -->
      <ng-container *ngIf="service.siteRowsEDynamix && !service.chosenSiteRow">
        <evhcSitesTableEDynamix (rowClicked)="selectSite($event)" [isPeopleRows]="false" [isRegional]="false"></evhcSitesTableEDynamix>
        <div class="tableSpacer"></div>
        <!-- Regional EVHC table -->
        <evhcSitesTableEDynamix (rowClicked)="selectSite($event)" [isPeopleRows]="false" [isRegional]="true"></evhcSitesTableEDynamix>
      </ng-container>

      <!-- If we have clicked a site -->
      <ng-container *ngIf="!!service.personRowsEDynamix">
        <button class="btn btn-primary" id="backButton" (click)="revertToSiteTable()"><</button>

        <div class="detailCard">
          <div *ngIf="constants.environment.evhc.showTechTable" class="tableContainer">
            <div class="titleHolder">By Technician</div>
            <evhcSitesTableEDynamix [isPeopleRows]="true" [isTech]="true"></evhcSitesTableEDynamix>
          </div>

          <div class="tableContainer">
            <div class="titleHolder">By Advisor</div>
            <evhcSitesTableEDynamix [isPeopleRows]="true" [isTech]="false"></evhcSitesTableEDynamix>
          </div>
        </div>

      </ng-container>

    </ng-container>

    <ng-template #normalEVHCTables>
      <!-- If we want to see all sites -->
      <ng-container *ngIf="service.siteRows && !service.chosenSiteRow">
        <evhcSitesTable (rowClicked)="selectSite($event)" [isRegional]=false></evhcSitesTable>
        <div class="tableSpacer"></div>
        <!-- Regional EVHC table -->
        <evhcSitesTable (rowClicked)="selectSite($event)" [isRegional]=true></evhcSitesTable>
      </ng-container>

      <!-- If we have clicked a site -->
      <ng-container *ngIf="!!service.personRows">

        <button class="btn btn-primary" id="backButton" (click)="revertToSiteTable()"><</button>

        <div class="detailCard">

          <div *ngIf="constants.environment.evhc.showTechTable" class="tableContainer">
            <div class="titleHolder">Technician Identification</div>
            <evhcTechsTable [excelTitle]="service.evhcTechTitle" [isTech]=true></evhcTechsTable>
          </div>

          <div class="tableContainer">
            <div class="titleHolder">Advisor Conversion</div>
            <evhcTechsTable [excelTitle]="service.evhcAdvisorTitle" [isTech]=false></evhcTechsTable>
          </div>

        </div>


      </ng-container>
    </ng-template>


  </div>


</div>