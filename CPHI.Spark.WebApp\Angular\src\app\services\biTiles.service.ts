import {Injectable} from '@angular/core';
import {VNTileTableRow} from '../model/VNTileTableRow';
import {ConstantsService} from './constants.service';
import {CphPipe} from '../cph.pipe';
import {DashboardMeasure} from '../model/DashboardMeasure';

@Injectable({
  providedIn: 'root'
})


export class BITilesService {

  constructor(
    private constants: ConstantsService,
    private cphPipe: CphPipe,
  ) {

  }

  // Returns a string with the first letter of each word in uppercase
  // upperFirst(inputString: string): string {
  //   return (inputString == "" || inputString == null) ? "" :
  //     inputString.toLowerCase().split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  // }

  buildTableRowsNonDatesBasis(fieldName: string, dataFiltered: any[], dataHighlighted: any[]): VNTileTableRow[] {
    let tableRows: VNTileTableRow[] = [];

    //go through filteredData to find unique labels and countup
    let labels: string[] = [];

    dataFiltered.forEach(item => {
      const itemLabel = item[fieldName];
      let labelsIndex = labels.findIndex(x => x === itemLabel);
      if (labelsIndex === -1) {
        //don't already have label, must be new
        labels.push(itemLabel)
        tableRows.push({Label: itemLabel, FilteredTotal: 1, HighlightedTotal: 0})
      } else {
        tableRows[labelsIndex].FilteredTotal += 1;
      }
    })

    //find out values to show

    dataHighlighted.forEach(item => {
      const itemLabel = item[fieldName];
      const indexInLabels = labels.findIndex(x => x === itemLabel);
      //should exist or something has gone wrong as orderDataHighlighted should be subset of orderDataFiltered
      tableRows[indexInLabels].HighlightedTotal += 1;
    })
    return tableRows;
  }


  buildTableRowsDatesBasis(fieldName: string, data: any[], isWeekly?: boolean): VNTileTableRow[] {

    //similar approach to non-dates, but looks odd if we have gaps in the days, so we find the earliest date then iterate every day at a time since then even if it has no data
    let earliestDate: Date = data.map(x => x[fieldName]).sort((a, b) => a.getTime() - b.getTime())[0];
    let latestDate: Date = data.map(x => x[fieldName]).sort((a, b) => b.getTime() - a.getTime())[0];

    let tableRows = [];
    let currentDate = new Date(earliestDate);
    //walk through creating tableRows, with zero values
    while (!this.constants.datesAreSame(currentDate, latestDate)) {
      const label = this.cphPipe.transform(currentDate, 'date', 0);
      tableRows.push({Label: label, FilteredTotal: 0, HighlightedTotal: 0})
      currentDate = this.constants.addDays(currentDate, 1);
    }

    //update these new rows with orderData
    const orderDataSorted = data.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    let currentTableRowIndex: number = 0;
    orderDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) {
        tableRows[currentTableRowIndex].FilteredTotal += 1;
      }
    })

    //update with highlighted data
    const highlightedDataSorted = data.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    currentTableRowIndex = 0;
    highlightedDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) {
        tableRows[currentTableRowIndex].HighlightedTotal += 1;
      }
    })

    let tableRowsWeekly: any[] = [];

    if (isWeekly) {
      let day: number = 1;
      let weekNumber: number = 0;

      tableRows.forEach(row => {
        if (!tableRowsWeekly[weekNumber]) {
          tableRowsWeekly[weekNumber] = {
            FilteredTotal: 0,
            HighlightedTotal: 0,
            Label: tableRows[7 * weekNumber].Label
          };
        }

        tableRowsWeekly[weekNumber].FilteredTotal = tableRowsWeekly[weekNumber].FilteredTotal + row.FilteredTotal;
        tableRowsWeekly[weekNumber].HighlightedTotal = tableRowsWeekly[weekNumber].HighlightedTotal + row.HighlightedTotal;


        weekNumber = day % 7 === 0 ? weekNumber + 1 : weekNumber;
        day++;
      })
    }

    return isWeekly ? tableRowsWeekly : tableRows;

  }


  public sortByNumericBand(tableRows: VNTileTableRow[]) {
    let lessThanTableRows = tableRows.filter(t => t.Label.substring(0, 1) == '<');
    let noLTorGTRows = tableRows.filter(t => !['<', '>'].includes(t.Label.substring(0, 1)));
    let greaterThanTableRow = tableRows.filter(t => t.Label.substring(0, 1) == '>');

    lessThanTableRows = lessThanTableRows.sort((a, b) => this.findFirstNumericValue(a.Label) - this.findFirstNumericValue(b.Label));
    noLTorGTRows = noLTorGTRows.sort((a, b) => this.findFirstNumericValue(a.Label) - this.findFirstNumericValue(b.Label));
    greaterThanTableRow = greaterThanTableRow.sort((a, b) => this.findFirstNumericValue(a.Label) - this.findFirstNumericValue(b.Label));

    return lessThanTableRows.concat(noLTorGTRows).concat(greaterThanTableRow);
  }


  findFirstNumericValue(inputString: string): number | null {
    // Use a regular expression to find the first numeric value in the string
    const match = inputString.match(/\d+/);

    // If a match is found, convert it to a number and return it
    if (match) {
      return parseInt(match[0], 10);
    }

    // If no match is found, return null
    return null;
  }


  filterData(dataIn: any[], stringFilterChoices: DashboardMeasure[]) {
    let results = [];
    dataIn.forEach(item => {
      //check all chosen strings
      let filterOutThisItem: boolean = false;
      stringFilterChoices.forEach(choice => {
        if (choice.IsDate) {
          let itemPropertyAsDateString = this.cphPipe.transform(item[choice.FieldName], 'date', 0);
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) {
            if (!choice.ChosenValues.includes(itemPropertyAsDateString)) {
              filterOutThisItem = true;
            }
          }
        } else {
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) {
            if (!choice.ChosenValues.includes(item[choice.FieldName])) {
              filterOutThisItem = true;
            }
          }
        }
      })


      if (!filterOutThisItem) {
        results.push(item)
      }
    })

    return results;
  }


  highlightRow(row: VNTileTableRow, fieldName: string, highlightChoices: DashboardMeasure[]) {
    let userChoice = highlightChoices.find(x => x.FieldName === fieldName);
    let isItemSelected = userChoice.ChosenValues.length === 0 || userChoice.ChosenValues.includes(row.Label);
    if (userChoice.ChosenValues.length === 0) {
      highlightChoices.find(x => x.FieldName === fieldName).ChosenValues = [row.Label];
    } else if (isItemSelected) {
      userChoice.ChosenValues = userChoice.ChosenValues.filter(x => x !== row.Label)
    } else {
      userChoice.ChosenValues.push(row.Label)
    }


  }

}
