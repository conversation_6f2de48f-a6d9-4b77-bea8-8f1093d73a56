.filter-column {
   flex-direction: column;
   grid-gap: 5px;
   display: flex;
   width: 20em;

   .filter-tile {
      max-height: 18em;
      overflow-y: auto;
      background-color: #fff;
      padding: 5px;
   }
}

.bars-column {
   flex-grow: 1;
   flex-basis: 100px;
   display: flex;
   flex-direction: column;
   grid-gap: 5px;

   .bar-container {
      background-color: #fff;
      height: 20em;
      max-height: 20em;
      padding: 5px;
      display: flex;
      flex-direction: column;
      width: 100%;
      flex-basis: 200px;

      .contentsHolder {
         flex-grow: 1;
         overflow-y: hidden;
      }
   }
}

.date-input {
   width: 9em;
}

#volumeChangeTile {
   margin-left: 1em;
}

.dashboard-grid {
   grid-gap: 4px !important;
}


.cursor-pointer {
   cursor: pointer;
}

.bigNumber {
   height: 100%;
   display: flex;
   align-items: center;
   justify-content: center;
}

nav .dashboard-grid {
   position: absolute;
}

#rightScrollable .dashboard-tile {
   display: flex;
   flex-direction: column;

   .contentsHolder {
      flex: 1;
      max-height: 200px;
   }
}

.skeleton-loader:empty {
   width: 100%;
   display: block;
   background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 80% ), lightgray;
   background-repeat: repeat-y;
   background-size: 50px 500px;
   background-position: 0 0;
   animation: shine 1s infinite;
   border-radius: 2px;
   opacity: 0.5;
}

@keyframes shine {
   to {
      background-position: 100% 0;
   }
}

