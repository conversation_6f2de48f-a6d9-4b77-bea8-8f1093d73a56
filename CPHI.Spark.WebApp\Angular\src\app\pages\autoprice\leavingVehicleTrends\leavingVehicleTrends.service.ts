import { EventEmitter, Injectable } from "@angular/core";
import { LeavingVehicleItem } from "src/app/model/LeavingVehicleItem";
import { LeavingVehicleBarChartSet } from "src/app/model/LeavingVehicleBarChartSet";
import { SelectionsService } from "src/app/services/selections.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { GetLeavingVehicleItemsParams } from "src/app/model/GetLeavingVehicleItemsParams";
import { RetailerSite } from "src/app/model/RetailerSite";
import { ConstantsService } from "src/app/services/constants.service";
import { DashboardMeasure } from "src/app/model/DashboardMeasure";
import { BIChartTileDataType, TileMetric } from "src/app/components/biChartTile/biChartTile.component";
import { VNTileParams } from "src/app/model/VNTileParams";
import { VNTileTableRow } from "src/app/model/VNTileTableRow";
import { BITilesService } from "src/app/services/biTiles.service";
import { BigLeavingChartParams } from "src/app/model/BigLeavingChartParams";
import { SummaryStats } from "./SummaryStats";
import { ExternalFiltersForLeavingVehicleAnalysis } from "src/app/model/ExternalFiltersForLeavingVehicleAnalysis";
@Injectable({
   providedIn: "root",
})
export class LeavingVehicleTrendsService {
   rawData: LeavingVehicleItem[];
   rawDataFiltered: LeavingVehicleItem[];
   rawDataHighlighted: LeavingVehicleItem[];

   smallChartSetData: LeavingVehicleBarChartSet;
   bigChartParams: BigLeavingChartParams;
   summaryStats: SummaryStats;

   //user choices
   chosenRetailerSites: RetailerSite[];
   displayChoice: "smallCharts" | "bigChart" = "smallCharts";
   showFirstPricePoint: boolean = false;
   showLastPricePoint: boolean = true;

   startDate: Date;
   endDate: Date;

   newSmallChartDataEmitter: EventEmitter<LeavingVehicleBarChartSet> = new EventEmitter();
   newBigChartDataEmitter: EventEmitter<BigLeavingChartParams> = new EventEmitter();
   filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
   highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
   refreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
   refreshTileEmitter: EventEmitter<void> = new EventEmitter();

   filterChoices: DashboardMeasure[];
   highlightChoices: DashboardMeasure[];
   externalFilterForLeavingVehicleAnalysis: ExternalFiltersForLeavingVehicleAnalysis;
   public metric: TileMetric = null;

   public metricChoices: TileMetric[] = [
      { value: "", label: "Units" },
      { value: "DaysListed", label: "Days Listed" },
      { value: "LastPP", label: "Final Price Position %", formatType: "percent1dp", autoScale: true },
   ];

   constructor(
      public constants: ConstantsService,
      public selections: SelectionsService,
      private getDataService: GetDataMethodsService,
      private biTilesService: BITilesService
   ) {}

   initParams() {
      if (!this.chosenRetailerSites) {
         this.chosenRetailerSites = this.constants.RetailerSites;
      }
      this.filterChoices = this.setChoices();
      this.highlightChoices = this.setChoices();

      if (!this.startDate) {
         this.startDate = this.constants.addMonths(this.constants.startOfMonth(new Date()), -6);
         this.endDate = this.constants.endOfMonth(new Date());
      }

      if (!this.metric) {
         this.metric = this.metricChoices[0];
      }

      this.externalFilterForLeavingVehicleAnalysis = {
         //AchievedSaleType: null,
         BodyType: null,
         DaysListedBand: null,
         FirstPPBand: null,
         FuelType: null,
         LastPPBand: null,
         LastPriceBand: null,
         LastPriceIndicator: null,
         Make: null,
         Mileage: null,
         Model: null,
         Region: null,
         RegYear: null,
         RetailRatingBand: null,
         RetailerSiteName: null,
         TransmissionType: null,
         VehicleType: null,
         OptedOutPctBand: null,
      };
   }

   setChoices() {
      let fields: string[] = [
         "Region",
         "RetailerSiteName",
         "FuelType",
         "Make",
         "BodyType",
         "TransmissionType",
         "Model",
         "RegYear",
         "DaysListedBand",
         "FirstPPBand",
         "LastPPBand",
         "RetailRatingBand",
         "MileageBand",
         "IsOnStrategy",
         "LastPriceBand",
         "LastPriceIndicator",
         "VehicleType",
         "OptedOutPctBand",
      ]; //'AchievedSaleType',
      let result = [];
      fields.forEach((field) => {
         result.push({ FieldName: field, FieldNameTranslation: field, IsDate: false, ChosenValues: [] });
      });
      //console.log(result)
      return result;
   }

   async getData() {
      try {
         const params: GetLeavingVehicleItemsParams = {
            StartDate: this.startDate,
            EndDate: this.endDate,
            IncludeNewVehicles: false,
            IncludeUsedVehicles: true,
            ChosenRetailerSiteIds: this.chosenRetailerSites.map((x) => x.Id),
         };

         const res = await this.getDataService.getLeavingVehicleItems(params);
         this.rawData = res;

         // this.rawData.forEach((vehicle) => {
         //   vehicle.BodyType = this.biTilesService.upperFirst(vehicle.BodyType);
         // })

         this.updateFilteredData();
         this.selections.triggerSpinner.emit({ show: false });
      } catch (error) {
         console.error("Error fetching leaving vehicle items", error);
         this.constants.toastDanger("Failed to load leaving vehicles");
         this.selections.triggerSpinner.emit({ show: false });
      }
   }

   analyseSmallChartsData(data: LeavingVehicleItem[]) {
      let smallChartSet: LeavingVehicleBarChartSet = new LeavingVehicleBarChartSet();
      smallChartSet.calculateSet(data);
      this.smallChartSetData = smallChartSet;
      this.newSmallChartDataEmitter.emit(this.smallChartSetData);
   }

   private analyseBigChartsData(filteredData: LeavingVehicleItem[], highlightedData: LeavingVehicleItem[]) {
      let bigChartParams = new BigLeavingChartParams();
      bigChartParams.calculateParams(filteredData, highlightedData, this.showFirstPricePoint, this.showLastPricePoint);
      this.bigChartParams = bigChartParams;
      this.newBigChartDataEmitter.emit(this.bigChartParams);
   }

   private recalcSummaryStats() {
      const all = this.calculateStats(this.rawDataFiltered);
      const highlighted = this.calculateStats(this.rawDataHighlighted);
      const highlightedKeys = this.constants.createHashMap(this.rawDataHighlighted, "FirstSnapId");
      const notHighlightedData = [];
      this.rawDataFiltered.forEach((item) => {
         if (!highlightedKeys[item.FirstSnapId]) {
            notHighlightedData.push(item);
         }
      });
      const notHighlighted = this.calculateStats(notHighlightedData);

      const vs = {
         vehicleCount: highlighted.vehicleCount - all.vehicleCount,
         firstPP: highlighted.firstPP - all.firstPP,
         lastPP: highlighted.lastPP - all.lastPP,
         changePP: highlighted.changePP - all.changePP,
         retailRating: highlighted.retailRating - all.retailRating,
         daysListed: highlighted.daysListed - all.daysListed,
      };
      const highlightedVsNot = {
         vehicleCount: highlighted.vehicleCount - notHighlighted.vehicleCount,
         firstPP: highlighted.firstPP - notHighlighted.firstPP,
         lastPP: highlighted.lastPP - notHighlighted.lastPP,
         changePP: highlighted.changePP - notHighlighted.changePP,
         retailRating: highlighted.retailRating - notHighlighted.retailRating,
         daysListed: highlighted.daysListed - notHighlighted.daysListed,
      };

      this.summaryStats = {
         all: all,
         highlighted: highlighted,
         vs: vs,
         notHighlighted: notHighlighted,
         highlightedVsNot: highlightedVsNot,
      };
   }

   private calculateStats(items: LeavingVehicleItem[]) {
      let volumeCum = 0;
      let daysListedCum = 0;
      let firstPPCum = 0;
      let lastPPCum = 0;
      let rrCum = 0;

      items.forEach((item) => {
         volumeCum++;
         daysListedCum += item.DaysListed;
         firstPPCum += item.FirstPP;
         lastPPCum += item.LastPP;
         rrCum += item.LastRetailRating;
      });
      const firstPP = this.constants.div(firstPPCum, volumeCum);
      const lastPP = this.constants.div(lastPPCum, volumeCum);
      const rr = this.constants.div(rrCum, volumeCum);
      const dl = this.constants.div(daysListedCum, volumeCum);

      const allResult = {
         vehicleCount: volumeCum,
         firstPP: firstPP,
         lastPP: lastPP,
         changePP: firstPP - lastPP,
         retailRating: rr,
         daysListed: dl,
      };
      return allResult;
   }

   reAnalyseBigChart() {
      setTimeout(() => {
         this.analyseBigChartsData(this.rawDataFiltered, this.rawDataHighlighted);
      }, 50); //to let the firstPP thing be set
   }

   toggleSetShowPricePoints(pricePointType: "first" | "last") {
      if (pricePointType == "first") {
         this.showFirstPricePoint = !this.showFirstPricePoint;
         this.reAnalyseBigChart();
      } else {
         this.showLastPricePoint = !this.showLastPricePoint;
         this.reAnalyseBigChart();
      }
   }



   updateFilteredData() {
      this.rawDataFiltered = this.biTilesService.filterData(this.rawData, this.filterChoices);
      this.updateHighlightedData();
   }
   updateHighlightedData() {
      this.rawDataHighlighted = this.biTilesService.filterData(this.rawDataFiltered, this.highlightChoices);
      this.analyseSmallChartsData(this.rawDataHighlighted);
      this.analyseBigChartsData(this.rawDataFiltered, this.rawDataHighlighted);
      this.recalcSummaryStats();

      this.selections.triggerSpinner.emit({ show: false });
      this.refreshTileEmitter.emit();
   }

   getPageParams(): VNTileParams {
      return {
         highlightChoices: this.highlightChoices,
         filterChoices: this.filterChoices,

         filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
         highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
         updateThisPicker: this.refreshFilterListsEmitter,
         updateThisTile: this.refreshTileEmitter,
         parentMethods: {
            buildRows: (fieldName, dataType) => this.buildTableRows(fieldName, dataType),
            highlightRow: (row, fieldName) => this.biTilesService.highlightRow(row, fieldName, this.highlightChoices),
            provideItemsList: (fieldName, isDateField) => this.provideItemsList(fieldName, isDateField),
         },
      };
   }

   provideItemsList(fieldName: string, isDateField: boolean) {
      if (!this.rawData) {
         return [];
      }
      return [...new Set(this.rawData.map((x) => x[fieldName]))];
   }

   buildTableRows(fieldName: string, dataType: BIChartTileDataType) {
      let tableRows: VNTileTableRow[] = [];
      if (dataType === BIChartTileDataType.day) {
         tableRows = this.biTilesService.buildTableRowsDatesBasis(fieldName, this.rawData, false);
      } else if (dataType === BIChartTileDataType.weekly) {
         tableRows = this.biTilesService.buildTableRowsDatesBasis(fieldName, this.rawData, true);
      } else {
         tableRows = this.biTilesService.buildTableRowsNonDatesBasis(
            fieldName,
            this.rawData,
            this.rawDataHighlighted,
            this.metric
         );
      }

      // limit to 20
      return tableRows; // .slice(0, 20);
   }

   // highlightRow(row: VNTileTableRow, fieldName: string) {
   //   let userChoice = this.highlightChoices.find(x => x.FieldName === fieldName);
   //   let isItemSelected = userChoice.ChosenValues.length === 0 || userChoice.ChosenValues.includes(row.Label);
   //   if (userChoice.ChosenValues.length === 0) {
   //     this.highlightChoices.find(x => x.FieldName === fieldName).ChosenValues = [row.Label];
   //   } else if (isItemSelected) {
   //     userChoice.ChosenValues = userChoice.ChosenValues.filter(x => x !== row.Label)
   //   } else {
   //     userChoice.ChosenValues.push(row.Label)
   //   }

   //   this.stockInsightFiltersForStockReport[fieldName] = userChoice.ChosenValues.length === 0 ? null : userChoice.ChosenValues;

   //   //this.pageParams.highlightChoiceHasBeenMade.emit(true);
   // }
}
