import { Component, ElementRef, EventEmitter, Input, On<PERSON><PERSON><PERSON>, OnInit, ViewChild } from "@angular/core";
import { Subscription } from "rxjs";
import { CphPipe } from "src/app/cph.pipe";
import { ConstantsService } from "src/app/services/constants.service";
import { DashboardMeasure } from "../../model/DashboardMeasure";
import { VNTileParams } from "../../model/VNTileParams";
import { BIChartTileDataType, TileType } from "../biChartTile/biChartTile.component";
import { VNTileTableRow } from "src/app/model/VNTileTableRow";



@Component({
    selector: 'biSlicerTile',
    templateUrl: './biSlicerTile.component.html',
    styleUrls: ['./biSlicerTile.component.scss']
})

export class BISlicerTileComponent implements OnInit, OnDestroy {


    tableRows: VNTileTableRow[]
    chartRefreshEmitter: EventEmitter<boolean>
    @Input() public dataType: BIChartTileDataType
    @Input() public isFixedSize: boolean
    @Input() public fieldName: string
    @Input() public tileType: TileType
    @Input() public title: string
    @Input() public pageParams: VNTileParams;
    @Input() public labelWidth: number;

    subscription: Subscription;
    @ViewChild('contentHolder', { static: true }) contentHolder: ElementRef;
    maxLabelLength: number;



    constructor(
        public constants: ConstantsService,
        public cphPipe: CphPipe
    ) { }

    ngOnInit(): void {
        this.buildTableRows();
        this.subscription = this.pageParams.updateThisTile.subscribe(res => {
            this.buildTableRows();
        })
    }

    ngOnDestroy() {
        if (!!this.subscription) { this.subscription.unsubscribe() }
    }

    isHighlightsChosen() {
        let choice = this.userChoice();
        if(!choice){
            console.error('failed finding ',this.fieldName)
        }
        return this.userChoice().ChosenValues.length > 0
    }

    clearHighlights() {
        let choice = this.pageParams.highlightChoices.find(x => x.FieldName === this.fieldName);
        choice.ChosenValues = [];
        this.pageParams.highlightChoiceHasBeenMade.emit();
    }


    buildTableRows() {

            this.tableRows = this.pageParams.parentMethods.buildRows(this.fieldName, this.dataType, this.tileType)
        this.maxLabelLength = 0;
        this.tableRows.forEach(row=>{
            if (row.Label){
            if(row.Label.length>this.maxLabelLength){this.maxLabelLength=row.Label.length}
            }
        })


    }


    rowLabel(row:VNTileTableRow){
        let label: string = row.Label;
        let labelTranslated: string;

        if (label === 'Non-franchise' && this.constants.currentLang === 'es') {
            labelTranslated = this.constants.translatedText.Common_ChooseFranchises;
          } else {
            labelTranslated = label;
          }

        if(this.isItemSelected(row.Label)){
            return `${labelTranslated} (${this.cphPipe.transform(row.HighlightedTotal,'number',0)})`
        }else{
            //not directly selected.  If any user choice made, just hide the number at the end of this label
            if(this.userChoice().ChosenValues.length===0){
                return `${labelTranslated} (${this.cphPipe.transform(row.FilteredTotal,'number',0)})`
            }else{
                return `${labelTranslated}`
            }

        }
    }







    isItemSelected(item: string) {
        return this.userChoice().ChosenValues.length === 0 || this.userChoice().ChosenValues.includes(item)
    }

    userChoice(): DashboardMeasure {
        let choice = this.pageParams.highlightChoices.find(x => x.FieldName === this.fieldName);
        if(!choice){
            console.error('error finding ',this.fieldName)
        }
        return choice;
    }



    public highlightRow(row: VNTileTableRow) {
        this.pageParams.parentMethods.highlightRow(row, this.fieldName);
        this.pageParams.highlightChoiceHasBeenMade.emit();
    }



    checkFieldName() {
        let largeTiles: string[] = ['Salesman', 'SiteDescription', 'ModelCode'];
        return !largeTiles.includes(this.fieldName) ? true : false;
    }
}
