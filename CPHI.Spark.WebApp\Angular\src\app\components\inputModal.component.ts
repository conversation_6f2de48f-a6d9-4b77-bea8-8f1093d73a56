import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConstantsService } from '../services/constants.service';
import { SelectionsService } from '../services/selections.service';



@Component({
  selector: 'inputModal',
  template: `
    
  <ng-template #modalRef let-modal>
      <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">{{inputModalHeader}}</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div [ngClass]="constants.environment.customer" class="modal-body alertModalBody lowHeight">
        <input id="inputBox" type="text" [(ngModel)]="inputText">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="modal.close()">{{constants.translatedText.OKUpper}}</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">{{constants.translatedText.Cancel}}</button>
      </div>
</ng-template>
    
   `,

  styles: [`
  #inputBox{margin: 2em auto; width:100%;}
  
    `]
})


export class InputModalComponent  {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;
  inputModalHeader:string;
  inputText: string;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,

  ) { }


  

  showModal() {

    this.modalService.open(this.modalRef, { windowClass: "inputModal", size: 'md', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //'ok'
      this.selections.inputModalEmitter.next(this.inputText)

    }, (reason) => {
      //cancel
      this.selections.inputModalEmitter.next(this.inputText)

    });





  }


}


