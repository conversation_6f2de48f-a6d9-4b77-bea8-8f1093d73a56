import { Component, Input, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AutopriceCheckboxParams } from 'src/app/model/AutopriceCheckboxParams';

@Component({
  selector: 'app-accordion',
  templateUrl: './accordion.component.html',
  styleUrls: ['./accordion.component.scss']
})
export class AccordionComponent implements OnInit {
  @Input() type: string;
  @Input() params: AutopriceCheckboxParams;

  choicesUpdatedSubscription: Subscription;
  selections: string;

  constructor() { }

  ngOnInit(): void {
    
    if(this.params.initialData.filterModel[this.params.field]?.length > 0){
      this.selections = [...new Set(this.params.initialData.rowData.map(x=>x[this.params.field]))].join(', ');
    }
    
    this.choicesUpdatedSubscription = this.params.newChoicesMade.subscribe(res => {
      if (res.field === this.params.field) {
        this.selections = res.chosenValues.join(', ');
      }
    })
  }

  ngOnDestroy(): void {
    if (this.choicesUpdatedSubscription) { this.choicesUpdatedSubscription.unsubscribe(); }
  }
}
