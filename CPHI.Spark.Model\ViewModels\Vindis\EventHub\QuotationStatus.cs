namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public enum QuotationStatus : int
    {
        /// <summary>
        /// A new quotation has been created.
        /// </summary>
        New,

        /// <summary>
        /// The quotation has been sent to the customer
        /// </summary>
        Sent,

        /// <summary>
        /// The quotation has been accepted by the customer
        /// </summary>
        Accepted,

        /// <summary>
        /// The quotation can been cancelled.
        /// </summary>
        Lost,

        /// <summary>
        /// The quotation has previously been accepted but is being amended.
        /// </summary>
        PendingAmendments,

        /// <summary>
        /// The quotation has delivered / handed over.
        /// </summary>
        Delivered,

        /// <summary>
        /// Quotation has been deleted.
        /// </summary>
        Deleted
    }

   
    
}