<ng-template #modalRef let-modal>

    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">Exec-Manager Mappings</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div [ngClass]="constants.environment.customer" class="modal-body alertModalBody lowHeight">
        <div class="buttonGroup">

            <!-- PrevYear -->
            <button
              class="btn btn-primary" 
              (click)="changeYear(-1)"
            >
              <i class="fas fa-caret-left"></i>
            </button>
            
            <button class="btn btn-primary">{{service.chosenYear}}</button>
  
            <!-- nextYear -->
            <button         
                class="btn btn-primary" 
                (click)="changeYear(1)"><i
                class="fas fa-caret-right"></i></button>
          </div>


          <div id="gridHolder">

            <div  id="excelExport" (click)="excelExport()">
                <img [src]="constants.provideExcelLogo()">
              </div>

              <ag-grid-angular
                id="execManagerMappingTable"
                class="ag-theme-balham"
                [gridOptions]="mainTableGridOptions"
                domLayout="autoHeight"
              >
              </ag-grid-angular>
         </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="modal.close()">OK</button>
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">{{constants.translatedText.Cancel}}</button>
    </div>

</ng-template>

<managerPickerModal #managerPickerModal></managerPickerModal>