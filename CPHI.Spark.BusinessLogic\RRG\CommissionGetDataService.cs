﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.Model.ViewModels.RRG;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.RRG
{
   class CommissionGetDataService
   {


      private string _connectionString;


      public CommissionGetDataService(string connString)
      {
         _connectionString = connString;
      }



      public async Task<IEnumerable<CommissionItem>> GetActualPayoutsForMonth(DateTime forMonth, DateTime lastPaidMonth)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);

         IEnumerable<CommissionItem> results = await commissionDataAccess.GetCommissionDataItemsWasForMonth(forMonth, lastPaidMonth);

         var tst = results.Where(x => x.StockNumber == "1016327/1");

         ///SPK-2824 temp.  We eliminate these to avoid facing into the problem that we previously overpaid people by failing
         ///to correctly record that we had paid them these items, then kept topping them up
         ///so to avoid clawing back we simply zero these out for anything before 2023
         if (forMonth.Year < 2023)
         {
            foreach (var item in results)
            {
               item.PayRateRoadsideAssist = 0;
               item.PayRateMot = 0;
               item.Mot12_Count = 0;
               item.Mot24_Count = 0;
               item.Mot36_Count = 0;
               item.RoadsideAssist12_Count = 0;
               item.RoadsideAssist24_Count = 0;
               item.RoadsideAssist36_Count = 0;
            }
         }


         //eliminate the people we wish to exclude
         List<int> skipExecList = SkipExecsListService.getSkipNames();
         return results.Where(x => !skipExecList.Contains(x.SalesmanId)).ToList().ConvertAll(x => new CommissionItem(x));
      }




      public async Task<IEnumerable<CommissionDataItem>> GetDataItemsForMonth(DateTime monthStart)
      {
         if (monthStart >= new DateTime(2024, 9, 1))
         {
            return await GetDataItemsSep2024On(monthStart);
         }
         else if (monthStart >= new DateTime(2024, 7, 1))
         {
            return await GetDataItemsJul2024On(monthStart);
         }
         else if (monthStart >= new DateTime(2023, 7, 1))
         {
            return await GetDataItemsJul2023On(monthStart);
         }
         else if (monthStart >= new DateTime(2023, 5, 1))
         {
            return await GetDataItemsMay2023On(monthStart);
         }
         else if (monthStart >= new DateTime(2023, 3, 1))
         {
            return await GetDataItemsMar2023On(monthStart);
         }
         else if (monthStart >= new DateTime(2022, 4, 1))
         {
            return await GetDataItemsApril2022On(monthStart);
         }
         //if (monthStart >= new DateTime(2022, 2, 1))
         //{
         //    return await GetDataItemsFebruary2022On(monthStart);
         //}
         else if (monthStart >= new DateTime(2021, 10, 1))
         {
            return await GetDataItemsOctober2021On(monthStart);
         }
         else
         {
            return await GetDataItemsPreOctober2021(monthStart);
         }
      }


      public async Task<IEnumerable<CommissionQuarterlyCatchupDataItem>> GETCommissionItemsQuarterlyCatchupData(DateTime monthStart)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         return await commissionDataAccess.GETCommissionItemsQuarterlyCatchupData(monthStart);
      }

      public async Task<Dictionary<int, bool>> GETUsedTargetAchievementBySalesman(DateTime monthStart)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         return await commissionDataAccess.GETUsedTargetAchievementBySalesman(monthStart);
      }

      public async Task<Dictionary<int, ElectricSalesQToDate>> GETElectricSalesQToDatePre2025(DateTime monthStart)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         return await commissionDataAccess.GETElectricSalesQToDatePre2025(monthStart);
      }

      public async Task<Dictionary<int, ElectricSalesQToDate>> GETElectricSalesQToDateDacia(DateTime monthStart)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         return await commissionDataAccess.GETElectricSalesQToDateDacia(monthStart);
      }

      public async Task<Dictionary<int, ElectricSalesQToDate>> GETElectricSalesQToDateRen(DateTime monthStart)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         return await commissionDataAccess.GETElectricSalesQToDateRenault(monthStart);
      }

      public DateTime GetMostRecentPaidMonth(Model.DealerGroupName dealerGroup)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         return commissionDataAccess.GetMostRecentPaidMonth(dealerGroup);
      }




      private async Task<IEnumerable<CommissionDataItem>> GetDataItemsApril2022On(DateTime monthStart)
      {
         //Logic recap:
         //Used cars always just pay on accounting date.
         //New cars:
         //Prior to 1 October 21, just pay on accounting date.
         //1 October 21 switched to instead pay entirely on order date.   So in October paid out full commission on all new car orders done in a month regardless of if delivered / invoiced
         //1 April 22 now using new logic which is that you earn £65 of the vehicle commission upon order.  Then when invoiced (we look at accounting date) get the remaining £10 + the add-ons commissions

         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         List<CommissionDataItem> results = new List<CommissionDataItem>();


         //-----------------------------------
         // New Vehicles
         //-----------------------------------

         //1. New vehicles ordered THIS month
         var orderedThisMonth = await commissionDataAccess.GetCommissionItems_New_OrderedPostOct(monthStart);

         foreach (var item in orderedThisMonth)
         {
            //if delivered in month, pays full
            if (item.AccountingDate.Month == monthStart.Month && item.AccountingDate.Year == monthStart.Year)
            {
               results.Add(item);
            }
            else
            //otherwise just pays the order element
            {
               item.OnlyPayOrderMoney = true;
               results.Add(item);
            }
         }

         // var tst = results.Where(x => x.StockNumber == "1025598/0");

         //1.5 New vehicles ordered April 22 onwards (so already paid an order commission for) now delivered so get the other bit
         var allNewDeliveredInMonth = await commissionDataAccess.GETCommissionItems_DeliveredOct21On_OrderedAnytime(monthStart, true);

         DateTime startApril = new DateTime(2022, 4, 1);

         foreach (var item in allNewDeliveredInMonth)
         {
            //if ordered prior to this month, get paid the invoice element now
            if (item.OrderDate < monthStart && item.OrderDate >= startApril)
            {
               item.OnlyPayInvoiceMoney = true;
               results.Add(item);
            }
            else
            {
               //else orderDate must be in month, so don't include it as is already paid out on in #1 above.
            }
         }

         //var tst1 = results.Where(x => x.StockNumber == "1025598/0");

         //2. New vehicles ordered pre start Oct delivered Oct onwards (ordered Pre start Oct delivered in month).  These get paid in full as not already paid order money.
         results.AddRange(await commissionDataAccess.GETCommissionItems_New_OrderedPreOctDeliveredPostOct(monthStart));

         //var tst2 = results.Where(x => x.StockNumber == "1025598/0");


         //-----------------------------------
         // Used Vehicles
         //-----------------------------------

         //3. Used vehicles delivered Oct onwards (ordered AnyTime)
         results.AddRange(await commissionDataAccess.GETCommissionItems_DeliveredOct21On_OrderedAnytime(monthStart, false));

         //var tst3 = results.Where(x => x.StockNumber == "1042013/1");


         //-----------------------------------
         // Leaver deals
         //-----------------------------------

         // now get leaver deal information.   If there's an entry in this list we can use it to tag the deal as only paying out a fixed handover amount
         IEnumerable<LeaverDealItem> leaverDeals = await commissionDataAccess.GETLeaverDealsForMonth(monthStart);

         //tag results as capped at handoverOnly
         foreach (var item in results)
         {
            var leaverItem = leaverDeals.FirstOrDefault(x => x.StockNumber == item.StockNumber);
            if (leaverItem != null && leaverItem.SalesmanId != item.SalesmanId)
            {
               item.IsCappedToHandoverRateOnly = true;
            }
         }

         List<int> skipExecList = SkipExecsListService.getSkipNames();
         return results.Where(x => !skipExecList.Contains(x.SalesmanId));
      }


      private async Task<IEnumerable<CommissionDataItem>> GetDataItemsOctober2021On(DateTime monthStart)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         List<CommissionDataItem> results = new List<CommissionDataItem>();
         //3 types of deal we can get

         //1. New vehicles ordered since start October
         results.AddRange(await commissionDataAccess.GetCommissionItems_New_OrderedPostOct(monthStart));

         //2. New vehicles ordered pre start Oct delivered Oct onwards (ordered Pre start Oct delivered pre Oct start already paid and done)
         results.AddRange(await commissionDataAccess.GETCommissionItems_New_OrderedPreOctDeliveredPostOct(monthStart));

         //3. Used vehicles delivered Oct onwards (ordered anytime)
         results.AddRange(await commissionDataAccess.GETCommissionItems_DeliveredOct21On_OrderedAnytime(monthStart, false));

         // also get leaver deal information.   If there's an entry in this list we can use it to tag the deal as only paying out a fixed handover amount
         IEnumerable<LeaverDealItem> leaverDeals = await commissionDataAccess.GETLeaverDealsForMonth(monthStart);

         //tag results as capped at handoverOnly
         foreach (var item in results)
         {
            var leaverItem = leaverDeals.FirstOrDefault(x => x.StockNumber == item.StockNumber);
            if (leaverItem != null && leaverItem.SalesmanId != item.SalesmanId)
            {
               item.IsCappedToHandoverRateOnly = true;
            }
         }

         List<int> skipExecList = SkipExecsListService.getSkipNames();
         return results.Where(x => !skipExecList.Contains(x.SalesmanId));


      }


      private async Task<IEnumerable<CommissionDataItem>> GetDataItemsPreOctober2021(DateTime monthStart)
      {

         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         List<CommissionDataItem> results = new List<CommissionDataItem>();

         results.AddRange(await commissionDataAccess.GETCommissionItems_PreOctober(monthStart));

         List<int> skipExecList = SkipExecsListService.getSkipNames();
         return results.Where(x => !skipExecList.Contains(x.SalesmanId));
      }


      private async Task<IEnumerable<CommissionDataItem>> GetDataItemsMay2023On(DateTime monthStart)
      {
         var items = await GetDataItemsMar2023On(monthStart);
         return items.Where(x => x.FranCode != 'A');
      }

      //New route for Sep 24 onwards, now everything is paid just on delivery
      private async Task<IEnumerable<CommissionDataItem>> GetDataItemsSep2024On(DateTime monthStart)
      {

         //New cars:
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         List<CommissionDataItem> results = new List<CommissionDataItem>();


         //-----------------------------------
         // New Vehicles
         //-----------------------------------

         //1. New vehicles ordered THIS month
         //IEnumerable<CommissionDataItem> orderedThisMonth = await commissionDataAccess.GetCommissionItems_New_FromJuly24Onwards(monthStart);  //ok


         //var tst = orderedThisMonth.ToList().Where(x => x.ServicePlan_Count > 0).ToList();

         //foreach (var item in orderedThisMonth)
         //{
         //    //if delivered in month, pays full
         //    if (item.AccountingDate.Month == monthStart.Month && item.AccountingDate.Year == monthStart.Year)
         //    {
         //        results.Add(item);
         //    }
         //    else
         //    //otherwise just pays the order element
         //    {
         //        item.OnlyPayOrderMoney = true;
         //        results.Add(item);
         //    }
         //}

         //1.5 New vehicles delivered Sep 24 onwards 
         var allNewDeliveredInMonth = await commissionDataAccess.GETCommissionItems_DeliveredJuly24On_OrderedAnytime(monthStart, true);



         //var tst1 = allNewDeliveredInMonth.ToList().Where(x => x.StockNumber == "1109201/0").ToList();

         DateTime startSeptember = new DateTime(2024, 9, 1);

         foreach (var item in allNewDeliveredInMonth)
         {
            //now we simply add all of these
            //item.OnlyPayInvoiceMoney = true; //which will mean that if Sep order, still pay 100%.  but if Aug pay less, etc.  (later method does this)
            item.OnlyPayInvoiceMoney = item.OrderDate < startSeptember;
            results.Add(item);
         }


         //2. New vehicles ordered pre start Oct delivered Oct onwards (ordered Pre start Oct delivered in month).  These get paid in full as not already paid order money.
         //results.AddRange(await commissionDataAccess.GETCommissionItems_New_OrderedPreOctDeliveredPostOct(monthStart));


         //-----------------------------------
         // Used Vehicles
         //-----------------------------------
         ///3. Used vehicles ordered AnyTime

         /// If March 23 onwards, return profit pot
         if (monthStart >= new DateTime(2024, 7, 1))
         {
            var usedMarch23On = await commissionDataAccess.GETCommissionItems_DeliveredJuly24On_OrderedAnytime(monthStart, false);
            //var tst5 = usedMarch23On.FirstOrDefault(x => x.StockNumber == "1026167/1");
            results.AddRange(usedMarch23On);
         }
         else if (monthStart >= new DateTime(2023, 3, 1))
         {
            var usedMarch23On = await commissionDataAccess.GETCommissionItems_DeliveredMar23On_OrderedAnytime(monthStart, false);
            //var tst5 = usedMarch23On.FirstOrDefault(x => x.StockNumber == "1026167/1");
            results.AddRange(usedMarch23On);
         }
         /// else prior to March 23 we don't worry about profit pot
         else
         {
            var usedPreMarch23 = await commissionDataAccess.GETCommissionItems_DeliveredOct21On_OrderedAnytime(monthStart, false);
            //var tst6 = usedPreMarch23.FirstOrDefault(x => x.StockNumber == "1026167/1");
            results.AddRange(usedPreMarch23);
         }
         //var tst4 = results.Where(x => x.StockNumber == "1026167/1");
         //-----------------------------------
         // Leaver deals
         //-----------------------------------

         // now get leaver deal information.   If there's an entry in this list we can use it to tag the deal as only paying out a fixed handover amount
         IEnumerable<LeaverDealItem> leaverDeals = await commissionDataAccess.GETLeaverDealsForMonth(monthStart);

         //tag results as capped at handoverOnly
         foreach (var item in results)
         {
            var leaverItem = leaverDeals.FirstOrDefault(x => x.StockNumber == item.StockNumber);

            if (leaverItem != null && leaverItem.SalesmanId != item.SalesmanId)
            {
               item.IsCappedToHandoverRateOnly = true;
            }
         }

         List<int> skipExecList = SkipExecsListService.getSkipNames();
         //var tst3 = results.Where(x => x.StockNumber == "1035312/0");
         return results.Where(x => !skipExecList.Contains(x.SalesmanId));
      }


      //New route for Jul 24 onwards, we now include if there is a qualifying partex
      private async Task<IEnumerable<CommissionDataItem>> GetDataItemsJul2024On(DateTime monthStart)
      {
         //Logic recap:
         ///Same as method below from Jul23.   But now include if each deal has a qualifying partex


         //New cars:
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         List<CommissionDataItem> results = new List<CommissionDataItem>();


         //-----------------------------------
         // New Vehicles
         //-----------------------------------

         //1. New vehicles ordered THIS month
         IEnumerable<CommissionDataItem> orderedThisMonth = await commissionDataAccess.GetCommissionItems_New_FromJuly24Onwards(monthStart);  //ok


         var tst = orderedThisMonth.ToList().Where(x => x.ServicePlan_Count > 0).ToList();

         foreach (var item in orderedThisMonth)
         {
            //if delivered in month, pays full
            if (item.AccountingDate.Month == monthStart.Month && item.AccountingDate.Year == monthStart.Year)
            {
               results.Add(item);
            }
            else
            //otherwise just pays the order element
            {
               item.OnlyPayOrderMoney = true;
               results.Add(item);
            }
         }

         //1.5 New vehicles ordered July 24 onwards (so already paid an order commission for) now delivered so get the other bit
         var allNewDeliveredInMonth = await commissionDataAccess.GETCommissionItems_DeliveredJuly24On_OrderedAnytime(monthStart, true);

         var tst1 = allNewDeliveredInMonth.ToList().Where(x => x.ServicePlan_Count > 0).ToList();

         DateTime startApril = new DateTime(2022, 4, 1);

         foreach (var item in allNewDeliveredInMonth)
         {
            //if ordered prior to this month, get paid the invoice element now
            if (item.OrderDate < monthStart && item.OrderDate >= startApril)
            {
               item.OnlyPayInvoiceMoney = true;
               results.Add(item);
            }
            else
            {
               //else orderDate must be in month, so don't include it as is already paid out on in #1 above.
            }
         }


         //2. New vehicles ordered pre start Oct delivered Oct onwards (ordered Pre start Oct delivered in month).  These get paid in full as not already paid order money.
         //results.AddRange(await commissionDataAccess.GETCommissionItems_New_OrderedPreOctDeliveredPostOct(monthStart));


         //-----------------------------------
         // Used Vehicles
         //-----------------------------------
         ///3. Used vehicles ordered AnyTime

         /// If March 23 onwards, return profit pot
         if (monthStart >= new DateTime(2024, 7, 1))
         {
            var usedMarch23On = await commissionDataAccess.GETCommissionItems_DeliveredJuly24On_OrderedAnytime(monthStart, false);
            //var tst5 = usedMarch23On.FirstOrDefault(x => x.StockNumber == "1026167/1");
            results.AddRange(usedMarch23On);
         }
         else if (monthStart >= new DateTime(2023, 3, 1))
         {
            var usedMarch23On = await commissionDataAccess.GETCommissionItems_DeliveredMar23On_OrderedAnytime(monthStart, false);
            //var tst5 = usedMarch23On.FirstOrDefault(x => x.StockNumber == "1026167/1");
            results.AddRange(usedMarch23On);
         }
         /// else prior to March 23 we don't worry about profit pot
         else
         {
            var usedPreMarch23 = await commissionDataAccess.GETCommissionItems_DeliveredOct21On_OrderedAnytime(monthStart, false);
            //var tst6 = usedPreMarch23.FirstOrDefault(x => x.StockNumber == "1026167/1");
            results.AddRange(usedPreMarch23);
         }
         //var tst4 = results.Where(x => x.StockNumber == "1026167/1");
         //-----------------------------------
         // Leaver deals
         //-----------------------------------

         // now get leaver deal information.   If there's an entry in this list we can use it to tag the deal as only paying out a fixed handover amount
         IEnumerable<LeaverDealItem> leaverDeals = await commissionDataAccess.GETLeaverDealsForMonth(monthStart);

         //tag results as capped at handoverOnly
         foreach (var item in results)
         {
            var leaverItem = leaverDeals.FirstOrDefault(x => x.StockNumber == item.StockNumber);

            if (leaverItem != null && leaverItem.SalesmanId != item.SalesmanId)
            {
               item.IsCappedToHandoverRateOnly = true;
            }
         }

         List<int> skipExecList = SkipExecsListService.getSkipNames();
         //var tst3 = results.Where(x => x.StockNumber == "1035312/0");
         return results.Where(x => !skipExecList.Contains(x.SalesmanId));
      }

      //New route for Jul onwards, we exclude only New Alpine cars
      private async Task<IEnumerable<CommissionDataItem>> GetDataItemsJul2023On(DateTime monthStart)
      {
         var items = await GetDataItemsMar2023On(monthStart);

         return items.Where(x => !(x.FranCode == 'A' && x.VehicleTypeId == 6));
      }


      // New route for new Used Commission scheme - Mar 2023 
      private async Task<IEnumerable<CommissionDataItem>> GetDataItemsMar2023On(DateTime monthStart)
      {

         //Logic recap:
         ///Used cars always just pay on accounting date.


         //New cars:
         //Prior to 1 October 21, just pay on accounting date.
         //1 October 21 switched to instead pay entirely on order date.   So in October paid out full commission on all new car orders done in a month regardless of if delivered / invoiced
         //1 April 22 now using new logic which is that you earn £65 of the vehicle commission upon order.  Then when invoiced (we look at accounting date) get the remaining £10 + the add-ons commissions

         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         List<CommissionDataItem> results = new List<CommissionDataItem>();


         //-----------------------------------
         // New Vehicles
         //-----------------------------------

         //1. New vehicles ordered THIS month
         var orderedThisMonth = await commissionDataAccess.GetCommissionItems_New_OrderedPostOct(monthStart);

         foreach (var item in orderedThisMonth)
         {
            //if delivered in month, pays full
            if (item.AccountingDate.Month == monthStart.Month && item.AccountingDate.Year == monthStart.Year)
            {
               results.Add(item);
            }
            else
            //otherwise just pays the order element
            {
               item.OnlyPayOrderMoney = true;
               results.Add(item);
            }
         }

         //1.5 New vehicles ordered April 22 onwards (so already paid an order commission for) now delivered so get the other bit
         var allNewDeliveredInMonth = await commissionDataAccess.GETCommissionItems_DeliveredOct21On_OrderedAnytime(monthStart, true);

         DateTime startApril = new DateTime(2022, 4, 1);

         foreach (var item in allNewDeliveredInMonth)
         {
            //if ordered prior to this month, get paid the invoice element now
            if (item.OrderDate < monthStart && item.OrderDate >= startApril)
            {
               item.OnlyPayInvoiceMoney = true;
               results.Add(item);
            }
            else
            {
               //else orderDate must be in month, so don't include it as is already paid out on in #1 above.
            }
         }

         //var tst1 = results.Where(x => x.StockNumber == "1026167/1");

         //2. New vehicles ordered pre start Oct delivered Oct onwards (ordered Pre start Oct delivered in month).  These get paid in full as not already paid order money.
         results.AddRange(await commissionDataAccess.GETCommissionItems_New_OrderedPreOctDeliveredPostOct(monthStart));

         //var tst2 = results.Where(x => x.StockNumber == "1026167/1");
         // var tst2 = results.Where(x => x.StockNumber == "1025598/0");

         //-----------------------------------
         // Used Vehicles
         //-----------------------------------
         ///3. Used vehicles ordered AnyTime

         /// If March 23 onwards, return profit pot
         if (monthStart >= new DateTime(2023, 3, 1))
         {
            var usedMarch23On = await commissionDataAccess.GETCommissionItems_DeliveredMar23On_OrderedAnytime(monthStart, false);
            //var tst5 = usedMarch23On.FirstOrDefault(x => x.StockNumber == "1026167/1");
            results.AddRange(usedMarch23On);
         }
         /// else prior to March 23 we don't worry about profit pot
         else
         {
            var usedPreMarch23 = await commissionDataAccess.GETCommissionItems_DeliveredOct21On_OrderedAnytime(monthStart, false);
            //var tst6 = usedPreMarch23.FirstOrDefault(x => x.StockNumber == "1026167/1");
            results.AddRange(usedPreMarch23);
         }
         //var tst4 = results.Where(x => x.StockNumber == "1026167/1");
         //-----------------------------------
         // Leaver deals
         //-----------------------------------

         // now get leaver deal information.   If there's an entry in this list we can use it to tag the deal as only paying out a fixed handover amount
         IEnumerable<LeaverDealItem> leaverDeals = await commissionDataAccess.GETLeaverDealsForMonth(monthStart);

         //tag results as capped at handoverOnly
         foreach (var item in results)
         {
            var leaverItem = leaverDeals.FirstOrDefault(x => x.StockNumber == item.StockNumber);

            if (leaverItem != null && leaverItem.SalesmanId != item.SalesmanId)
            {
               item.IsCappedToHandoverRateOnly = true;
            }
         }

         List<int> skipExecList = SkipExecsListService.getSkipNames();
         //var tst3 = results.Where(x => x.StockNumber == "1035312/0");
         return results.Where(x => !skipExecList.Contains(x.SalesmanId));
      }

      public async Task<Dictionary<string, CommissionOrderRatePaid>> GetOrderPaidRates(DateTime yearMonth)
      {
         CommissionDataAccess commissionDataAccess = new CommissionDataAccess(_connectionString);
         return await commissionDataAccess.GetOrderPaidRates(yearMonth);
      }

      public Dictionary<string, int> GetIncentiveDeals(DateTime yearMonth)
      {
         // Not applicable prior to May 2025
         if (yearMonth < new DateTime(2025, 5, 1))
         {
            return null;
         }
         else
         {
            // Combination of Registration // Payout to add
            var incentives = new Dictionary<string, int>
        {
            // Kangoo 200
            { "CA25GBZ", 200 }, { "LM25HSK", 200 }, { "LM25HSY", 200 }, { "LM25HTP", 200 },
            { "LM25HTT", 200 }, { "LM25HTD", 200 }, { "LM25HTX", 200 }, { "MT25ORG", 200 },
            { "MT25RBY", 200 }, { "MT25RCO", 200 }, { "MT25RCZ", 200 }, { "MT25RCY", 200 },
            { "MT25REU", 200 }, { "LM25HTZ", 200 }, { "LM25HZS", 200 }, { "LM25HZT", 200 },
            { "LM25HZW", 200 }, { "LM25HZZ", 200 }, { "LM25JAU", 200 }, { "LM25JBO", 200 },
            { "LV25XAN", 200 }, { "LV25XKS", 200 }, { "LV25XKT", 200 }, { "LV25XKU", 200 },
            { "LV25XKW", 200 }, { "CA25FGJ", 200 }, { "CA25GCF", 200 }, { "CA25GCU", 200 },
            { "CA25GCZ", 200 }, { "CA25GFV", 200 }, { "CA25GGJ", 200 }, { "LM25HTV", 200 },
            { "MT25RDY", 200 },

            // Scenic 100
            { "FL74WPF", 100 }, { "VN74RWX", 100 }, { "LV74YTH", 100 }, { "SD74KMK", 100 },
            { "UXZ5779", 100 }, { "DF74YLH", 100 },

            // Trafic 100
            { "CA25GHB", 100 }, { "LV25XLM", 100 }, { "LM25JCU", 100 }, { "LM25JCY", 100 },
            { "LM25JCZ", 100 }, { "LM25JDJ", 100 }, { "LM25JDO", 100 }, { "LM25JDK", 100 },
            { "LM25JCV", 100 }, { "LM25HXS", 100 }, { "LM25HXX", 100 }, { "LM25HXP", 100 },
            { "LM25HYG", 100 }, { "MT25RHA", 100 }, { "MT25RHK", 100 }, { "MT25RHF", 100 },
            { "MT25RJU", 100 }, { "MT25RJO", 100 }, { "MT25RKA", 100 }, { "LM25HXT", 100 },
            { "MT25RKJ", 100 }, { "LV25XLF", 100 }, { "LV25XLG", 100 }, { "LV25XLL", 100 },
            { "LV25XLJ", 100 }, { "LV25XLK", 100 }, { "CA25GGZ", 100 }, { "CA25GHK", 100 },
            { "CA25GHO", 100 }, { "CA25GHN", 100 }, { "CA25GHU", 100 }, { "CA25GHV", 100 },
        };

            return incentives;
         }
      }

   }
}
