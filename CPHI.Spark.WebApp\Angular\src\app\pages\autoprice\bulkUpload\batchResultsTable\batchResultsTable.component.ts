import {Component, ElementRef, HostListener, Input, OnInit, ViewChild} from "@angular/core";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {
   ColDef,
   ColGroupDef,
   ColumnApi,
   ColumnState,
   FilterChangedEvent,
   GetContextMenuItemsParams,
   GridApi,
   ICellRendererParams,
   IDateFilterParams,
   MenuItemDef,
   RowClickedEvent,
   RowDoubleClickedEvent,
   RowHeightParams,
   SideBarDef,
   ValueGetterParams
} from "ag-grid-community";
import {
   AutoTraderCellLozengeComponent
} from "src/app/_cellRenderers/auto-trader-cell-lozenge/auto-trader-cell-lozenge.component";
import {CphPipe, FormatType} from "src/app/cph.pipe";
import {AutoPriceTableStateParams} from "src/app/model/AutoPriceTableStateParams";
import {BuildTotalAndAverageRowsParams} from "src/app/model/BuildTotalAndAverageRowsParams";
import {GridOptionsCph} from "src/app/model/GridOptionsCph";
import {StockPricingSitesOverview} from "src/app/model/StockPricingSitesOverview";
import {VehicleAdvertDetail} from "src/app/model/VehicleAdvertDetail";
import {SimpleTextModalComponent} from "src/app/pages/fleetOrderbook/simpleTextModal/simpleTextModal.component";
import {AGGridMethodsService} from "src/app/services/agGridMethods.service";
import {ConstantsService} from "src/app/services/constants.service";
import {ExcelExportService} from "src/app/services/excelExportService";
import {SelectionsService} from "src/app/services/selections.service";
import {BulkUploadService} from "../bulkUpload.service";
import {CustomHeaderService} from "src/app/components/customHeader/customHeader.service";
import {TableState, ValuationBatchResult} from "src/app/model/ValuationBatchResult";
import {CustomHeaderNew} from "src/app/components/customHeader/customHeader.component";
import {AutopriceRendererService} from "src/app/services/autopriceRenderer.service";
import {ColumnTypesService} from "src/app/services/columnTypes.service";
import {TableLayoutManagementParams} from "src/app/model/TableLayoutManagementParams";
import {TableLayoutManagementService} from "src/app/components/tableLayoutManagement/tableLayoutManagement.service";
import {VehicleValuationService} from "src/app/components/autoPriceValuationModal/vehicleValuation.service";
import {VehicleValuationComponent} from "../../vehicleValuation/vehicleValuation.component";
import {
   AutoPriceValuationModalComponent
} from "src/app/components/autoPriceValuationModal/autoPriceValuationModal.component";
import {CPHAutoPriceColDef} from "src/app/model/CPHColDef";
import {ValuationModalParams} from "src/app/components/autoPriceValuationModal/valuationCosting/ValuationModalParams";

export interface AdvertListingColGroupDef extends ColGroupDef {
   shouldAverage?: boolean,
   shouldAverageIfValue?: boolean;
   shouldTotal?: boolean;
   children: AdvertListingColDef[]
}

export interface AdvertListingColDef extends ColDef {
   shouldAverage?: boolean;
   shouldAverageIfValue?: boolean;
   shouldTotal?: boolean;
}

@Component({
   selector: 'batchResultsTable',
   templateUrl: './batchResultsTable.component.html',
   styleUrls: ['./batchResultsTable.component.scss']

})
export class BatchResultsTableComponent implements OnInit {

   @Input() singleMode: boolean;

   allFilteredNodes: {};
   lastUpdatedFilteredNodes: Date;
   gridOptions: GridOptionsCph;
   tableLayout: string;
   // newBatchLoadedSubscription: Subscription;

   gridApi: GridApi;
   gridColumnApi: ColumnApi;


   @ViewChild('deleteTableStateModal', {static: true}) deleteTableStateModal: ElementRef;

   public sideBar: SideBarDef | string | string[] | boolean | null = {
      toolPanels: [

         {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel',
            minWidth: 100,
            width: 200,
            maxWidth: 200,
         },
         {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',

            toolPanel: 'agColumnsToolPanel',
            minWidth: 100,
            width: 200,
            maxWidth: 200,
            toolPanelParams: {
               suppressPivots: false,
               suppressPivotMode: false
            }

         },
      ],
      position: 'left',
      defaultToolPanel: '',

   };

   public components: {
      [p: string]: any;
   } = {
      agColumnHeader: CustomHeaderNew,
   };

   @HostListener("window:resize", [])
   private onresize(event) {
      this.selections.screenWidth = window.innerWidth;
      this.selections.screenHeight = window.innerHeight;
      if (this.gridApi) {
         this.gridApi.resetRowHeights();
      }
   }

   constructor(
      public selections: SelectionsService,
      public excel: ExcelExportService,
      public agGridMethodsService: AGGridMethodsService,
      public service: BulkUploadService,
      public constants: ConstantsService,
      public cphPipe: CphPipe,
      public modalService: NgbModal,
      private customHeader: CustomHeaderService,
      private autopriceRendererService: AutopriceRendererService,
      private columnTypeService: ColumnTypesService,
      public tableLayoutManagementService: TableLayoutManagementService,
      private valuationService: VehicleValuationService
   ) {
   }

   ngOnInit() {
      this.initialiseTableLayoutManagement();
      this.setGridOptions();

      this.service.searchTerm.valueChanges.subscribe(value => {
         this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value);
      });

      this.tableLayoutManagementService.defaultFilterState = null;
      this.tableLayoutManagementService.parent = this.service.tableLayoutManagement;

   }

   initialiseTableLayoutManagement() {
      if (this.service.tableLayoutManagement) {
         return;
      }
      this.service.tableLayoutManagement = {
         pageName: "valuations",
         ownTableStates: null,
         sparkTableStates: null,
         standardTableStates: null,
         sharedTableStates: null,
         availableTableStates: null,
         selectedTableState: null,
         loadedTableState: null,
         usersToShareWith: null,
         gridApi: null,
         gridColumnApi: null,
         filterModel: null,
         originalColDefs: null,
      };
   }

   ngOnDestroy() {
      this.service.tableLayoutManagement.gridApi = null;
      this.service.tableLayoutManagement.gridColumnApi = null;
      this.service.batchResultsTableComponent = null;
      // this.newBatchLoadedSubscription.unsubscribe();
   }

   setGridOptions() {
      this.gridOptions = {
         context: {thisComponent: this},
         suppressPropertyNamesCheck: true,
         showOpenedGroup: true,

         onGridReady: (params) => this.onGridReady(params),
         getRowHeight: (params) => {
            return this.calcRowHeight(params);
         },
         headerHeight: this.selections.getGridRowHeight(50),
         floatingFiltersHeight: this.selections.getGridRowHeight(30),
         defaultColDef: {
            resizable: true,
            sortable: true,
            hide: false,
            floatingFilter: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               applyMiniFilterWhileTyping: true,
               cellHeight: this.agGridMethodsService.getFilterListItemHeight()
            },
            cellClass: 'agAlignCentre',
            headerComponentParams: {
               showPinAndRemoveOptions: false
            },
            autoHeaderHeight: true
         },


         rowSelection: 'multiple',
         getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
         getContextMenuItems: (params) => this.getContextMenuItems(params),
         pinnedBottomRowData: this.providePinnedBottomRowData(),
         columnTypes: {...this.columnTypeService.provideColTypes([])},
         columnDefs: this.service.chosenVehicleValuationBatch.IsApplyPriceScenarios ?
            this.provideColumnDefsFullPricing() : this.provideColumnDefs(),
         getRowClass: (params) => {
            if (params.data?.Description === 'Total') return 'total';
         },
         onRowClicked: (params) => this.onRowClicked(params),
         onSelectionChanged: (params) => this.onRowSelectionChange(),
         // statusBar: {
         //   statusPanels: [
         //     { statusPanel: 'agTotalAndFilteredRowCountComponent', align: 'right' },
         //   ]
         // },
         onFilterChanged: (event) => {
            this.onFilterChanged(event)
         },
         onRowDoubleClicked: params => this.onRowDoubleClicked(params),
         sideBar: this.sideBar,
         pivotMode: false,
         autoGroupColumnDef: {
            sortable: true
         },
         onColumnVisible(event) {
            if (!event.api) {
               return
            }

            let normalRowHeight = 25;
            let groupRowHeight = 25;

            event.api.forEachNode(node => {
               if (!node.isRowPinned()) {
                  node.setRowHeight(node.data ? normalRowHeight : groupRowHeight);
               }
            })
            event.api.onRowHeightChanged()
         },
      }
   }

   onRowDoubleClicked(params: RowDoubleClickedEvent<any, any>): void {
      const row: ValuationBatchResult = params.node.data;
      if (!row) {
         return;
      }

      // launch single vehicle valuation modal
      const modalRef = this.service.modalService.open(AutoPriceValuationModalComponent, {
         keyboard: true,
         size: 'lg',
         windowClass: 'autotraderValuation'
      });
      const modalComponent = (modalRef.componentInstance as AutoPriceValuationModalComponent);

      let modalParams: ValuationModalParams = {
         reg: row.Reg,
         mileage: row.Mileage,
         condition: row.Condition ?? 'Excellent',
         valuationId: row.ValuationId
      };
      modalComponent.initParams(modalParams, null);


      modalRef.result.then(
         result => {
            // 'okd'
         },
         // closed
         reason => {
            // cancelled
         }
      );
   }


   redrawTable() {

      if (this.service.tableLayoutManagement.gridApi) {

         this.service.tableLayoutManagement.gridApi.setRowData(this.service.batchResultsRowData);

         // If IsApplyPriceScenarios, then we want a different column layout
         if (this.service.chosenVehicleValuationBatch.IsApplyPriceScenarios) {
            this.service.tableLayoutManagement.gridApi.setColumnDefs(this.provideColumnDefsFullPricing());
         } else {
            this.service.tableLayoutManagement.gridApi.setColumnDefs(this.provideColumnDefs());
         }
      }

   }

   onFilterChanged(event: FilterChangedEvent<any, any>) {
      event.api.redrawRows()
   }

   currencyRenderer(params: ICellRendererParams) {
      return this.cphPipe.transform(params.value, 'currency', 0)
   }

   numberRenderer(params: ICellRendererParams) {
      return this.cphPipe.transform(params.value, 'number', 0)
   }

   onRowClicked(params: RowClickedEvent<any, any>): void {
      if (params.api.isSideBarVisible()) {
         params.api.closeToolPanel()
      }
   }

   booleanGetter(params: ValueGetterParams<any>): any {
      const colId = params.column.getColId();
      if (params.data) {
         //is a cell
         return params.data[params.colDef.field];
      } else {
         let tot = 0;
         params.node.allLeafChildren.forEach(child => {
            if (child.data[params.colDef.field]) tot++;
         })
         const result: number = tot;
      }
   }

   calcRowHeight(params: RowHeightParams) {
      return this.agGridMethodsService.getStandardHeight();
   }

   provideColumnDefs(): CPHAutoPriceColDef[] {
      let defs: CPHAutoPriceColDef[] = [
         {
            headerName: 'Retailer',
            field: 'RetailerName',
            colId: 'RetailerName',
            width: 10,
            type: 'labelSetFilter',
            pinned: 'left',
            hide: this.shouldHideColumn('RetailerName'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Make',
            field: 'Make',
            colId: 'Make',
            width: 15,
            type: 'labelSetFilter',
            hide: this.shouldHideColumn('Make'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Model',
            field: 'Model',
            colId: 'Model',
            width: 20,
            type: 'labelSetFilter',
            hide: this.shouldHideColumn('Model'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Derivative',
            field: 'Derivative',
            colId: 'Derivative',
            width: 20,
            type: 'label',
            hide: this.shouldHideColumn('Derivative'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Reg',
            field: 'Reg',
            colId: 'Reg',
            width: 10,
            type: 'label',
            columnSection: 'Bulk Upload',
            cellRenderer: params => this.autopriceRendererService.regPlateRenderer(params, {noMargin: true}),
            hide: this.shouldHideColumn('Reg')
         },

         {
            headerName: 'Mileage',
            field: 'Mileage',
            colId: 'Mileage',
            width: 10,
            type: 'number',
            hide: this.shouldHideColumn('Mileage'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Age Band',
            field: 'AgeBand',
            colId: 'AgeBand',
            width: 10,
            type: 'label',
            cellRenderer: (params) => this.autopriceRendererService.autoTraderLozengeRenderer(params),
            hide: this.shouldHideColumn('AgeBand'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Chassis',
            field: 'Vin',
            colId: 'Vin',
            width: 25,
            type: 'label',
            hide: this.shouldHideColumn('Vin'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Vehicle',
            hide: true,
            field: 'VehicleType',
            colId: 'VehicleType',
            width: 10,
            type: 'label',
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Trim',
            hide: true,
            field: 'Trim',
            colId: 'Trim',
            width: 10,
            type: 'label',
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Body Type',
            hide: true,
            field: 'BodyType',
            colId: 'BodyType',
            width: 10,
            type: 'label',
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Fuel Type',
            field: 'FuelType',
            colId: 'FuelType',
            width: 10,
            type: 'special',
            cellRenderer: params => this.autopriceRendererService.addEnergyTypeIcon(params),
            hide: this.shouldHideColumn('FuelType'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Transmission Type',
            hide: true,
            field: 'TransmissionType',
            colId: 'TransmissionType',
            width: 10,
            type: 'TransmissionType',
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Registered Date',
            field: 'RegisteredDate',
            colId: 'RegisteredDate',
            width: 10,
            type: 'date',
            hide: this.shouldHideColumn('RegisteredDate'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Value Band',
            field: 'ValueBand',
            colId: 'ValueBand',
            width: 10,
            type: 'label',
            cellRenderer: (params) => this.autopriceRendererService.autoTraderLozengeRenderer(params),
            hide: this.shouldHideColumn('ValueBand'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Valn Mkt. Av. Retail',
            field: 'ValuationMktAvRetailThisVehicle',
            shouldAverage: true,
            colId: 'ValuationMktAvRetailThisVehicle',
            width: 10,
            type: 'currency',
            hide: this.shouldHideColumn('ValuationMktAvRetailThisVehicle'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Retail Rating',
            field: 'RetailRating',
            shouldAverage: true,
            colId: 'RetailRating',
            width: 10,
            type: 'number',
            hide: this.shouldHideColumn('RetailRating'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Retail Rating Band',
            field: 'RetailRatingBand',
            colId: 'RetailRatingBand',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('RetailRatingBand'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Price',
            field: 'StrategyPrice',
            colId: 'StrategyPrice',
            shouldAverage: true,
            width: 10,
            type: 'currency',
            hide: this.shouldHideColumn('PriceScenario'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Price Scenario',
            hide: true,
            field: 'PriceScenario',
            colId: 'PriceScenario',
            width: 10,
            type: 'percent',
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Ret Days To Sell',
            field: 'DaysToSellAtCurrentSelling',
            shouldAverage: true,
            colId: 'DaysToSellAtCurrentSelling',
            width: 10,
            type: 'number',
            hide: this.shouldHideColumn('DaysToSellAtCurrentSelling'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'SIV',
            field: 'SIV',
            colId: 'SIV',
            width: 10,
            type: 'currency',
            hide: this.shouldHideColumn('SIV'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Profit',
            colId: 'Profit',
            field: 'Profit',
            width: 10,
            type: 'currency',
            hide: this.shouldHideColumn('Profit'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Reference 1',
            field: 'Reference1',
            colId: 'Reference1',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('Reference1'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Reference 2',
            field: 'Reference2',
            colId: 'Reference1',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('Reference2'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Reference 3',
            field: 'Reference3',
            colId: 'Reference1',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('Reference3'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Reference 3',
            field: 'Reference3',
            colId: 'Reference1',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('Reference3'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Is Vat Qualifying',
            field: 'IsVatQualifying',
            colId: 'IsVatQualifying',
            width: 10,
            type: 'boolean',
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Requested By',
            hide: !this.singleMode,
            field: 'LastRunBy',
            colId: 'LastRunBy',
            width: 10,
            type: 'label',
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Requested Date',
            hide: !this.singleMode,
            field: 'LastRunDate',
            colId: 'LastRunDate',
            width: 10,
            type: 'dateLongYear',
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Cap Value',
            field: 'CapValue',
            colId: 'CapValue',
            width: 10,
            type: 'currency',
            hide: this.shouldHideColumn('CapValue'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Date of Last Service',
            field: 'DateOfLastService',
            colId: 'DateOfLastService',
            width: 10,
            type: 'date',
            hide: this.shouldHideColumn('DateOfLastService'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Event Date',
            field: 'EventDate',
            colId: 'EventDate',
            width: 10,
            type: 'date',
            hide: this.shouldHideColumn('EventDate'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Event Type',
            field: 'EventType',
            colId: 'EventType',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('EventType'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Imported',
            field: 'Imported',
            colId: 'Imported',
            width: 10,
            type: 'boolean',
            hide: this.shouldHideColumn('Imported'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Insurance Cat',
            field: 'InsuranceCat',
            colId: 'InsuranceCat',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('InsuranceCat'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Link',
            colId: 'Link',
            cellRenderer: this.customAdLinkRenderer.bind(this),
            width: 20,
            type: 'special',
            hide: this.shouldHideColumn('Link'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Location',
            field: 'Location',
            colId: 'Location',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('Location'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Lot Number',
            field: 'LotNumber',
            colId: 'LotNumber',
            width: 10,
            type: 'int',
            hide: this.shouldHideColumn('LotNumber'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Mileage Warranty',
            field: 'MileageWarranty',
            colId: 'MileageWarranty',
            width: 10,
            type: 'boolean',
            hide: this.shouldHideColumn('MileageWarranty'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Mot Expiry',
            field: 'MotExpiry',
            colId: 'MotExpiry',
            width: 10,
            type: 'date',
            hide: this.shouldHideColumn('MotExpiry'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Notes',
            field: 'Notes',
            colId: 'Notes',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('Notes'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Number of Keys',
            field: 'NumberOfKeys',
            colId: 'NumberOfKeys',
            width: 10,
            type: 'int',
            hide: this.shouldHideColumn('NumberOfKeys'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'On Finance',
            field: 'OnFinance',
            colId: 'OnFinance',
            width: 10,
            type: 'boolean',
            hide: this.shouldHideColumn('OnFinance'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Reserve or Buy It Now',
            field: 'ReserveOrBuyItNow',
            colId: 'ReserveOrBuyItNow',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('ReserveOrBuyItNow'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Seller',
            field: 'Seller',
            colId: 'Seller',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('Seller'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Service History',
            field: 'ServiceHistory',
            colId: 'ServiceHistory',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('ServiceHistory'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'Services',
            field: 'Services',
            colId: 'Services',
            width: 10,
            type: 'int',
            hide: this.shouldHideColumn('Services'),
            columnSection: 'Bulk Upload'
         },
         {
            headerName: 'V5 Status',
            field: 'V5Status',
            colId: 'V5Status',
            width: 10,
            type: 'label',
            hide: this.shouldHideColumn('V5Status'),
            columnSection: 'Bulk Upload'
         },
      ];
      this.agGridMethodsService.workoutColWidths(this.service.batchResultsRowData, defs, 14, 6);
      (defs.find(x => x.headerName == 'Reg') as ColDef).width = 100;

      return defs as CPHAutoPriceColDef[];

   }

   provideColumnDefsFullPricing(): (AdvertListingColGroupDef | AdvertListingColDef)[] {
      let defs: (AdvertListingColDef | AdvertListingColGroupDef)[] = [
         {
            headerName: 'Retailer',
            field: 'RetailerName',
            colId: 'RetailerName',
            width: 10,
            type: 'labelSetFilter',
            pinned: 'left'
         },
         {headerName: 'Make', field: 'Make', colId: 'Make', width: 15, type: 'labelSetFilter',},
         {headerName: 'Model', field: 'Model', colId: 'Model', width: 20, type: 'labelSetFilter',},
         {headerName: 'Derivative', field: 'Derivative', colId: 'Derivative', width: 20, type: 'label',},
         {headerName: 'Reg', field: 'Reg', colId: 'Reg', width: 10, type: 'label',},

         {headerName: 'Mileage', field: 'Mileage', colId: 'Mileage', width: 10, type: 'number',},
         {
            headerName: 'Age Band',
            field: 'AgeBand',
            colId: 'AgeBand',
            width: 10,
            type: 'label',
            cellRenderer: (params) => this.autopriceRendererService.autoTraderLozengeRenderer(params)
         },
         {headerName: 'Chassis', field: 'Vin', colId: 'Vin', width: 25, type: 'label',},
         {headerName: 'Vehicle', hide: true, field: 'VehicleType', colId: 'VehicleType', width: 10, type: 'label',},
         {headerName: 'Trim', hide: true, field: 'Trim', colId: 'Trim', width: 10, type: 'label',},
         {headerName: 'Body Type', hide: true, field: 'BodyType', colId: 'BodyType', width: 10, type: 'label',},
         {
            headerName: 'Fuel Type',
            field: 'FuelType',
            colId: 'FuelType',
            width: 10,
            type: 'special',
            cellRenderer: params => this.autopriceRendererService.addEnergyTypeIcon(params)
         },
         {
            headerName: 'Transmission Type',
            hide: true,
            field: 'TransmissionType',
            colId: 'TransmissionType',
            width: 10,
            type: 'labelSetFilter',
         },
         {headerName: 'Registered Date', field: 'RegisteredDate', colId: 'RegisteredDate', width: 10, type: 'date',},
         {
            headerName: 'Value Band',
            field: 'ValueBand',
            colId: 'ValueBand',
            width: 10,
            type: 'label',
            cellRenderer: (params) => this.autopriceRendererService.autoTraderLozengeRenderer(params)
         },
         {
            headerName: 'Valn Mkt. Av. Retail',
            field: 'ValuationMktAvRetailThisVehicle',
            shouldAverage: true,
            colId: 'ValuationMktAvRetailThisVehicle',
            width: 10,
            type: 'currency',
         },
         {
            headerName: 'Retail Rating',
            field: 'RetailRating',
            shouldAverage: true,
            colId: 'RetailRating',
            width: 10,
            type: 'number',
         },
         {
            headerName: 'Retail Rating Band',
            field: 'RetailRatingBand',
            colId: 'RetailRatingBand',
            width: 10,
            type: 'label',
         },

         // { headerName: 'Price', field: 'StrategyPrice', colId: 'StrategyPrice', shouldAverage: true, width: 10, type: 'currency', },
         // { headerName: 'Price Scenario', field: 'PriceScenario', colId: 'PriceScenario', width: 10, type: 'percent', },
         // { headerName: 'Ret Days To Sell', field: 'DaysToSellAtCurrentSelling', shouldAverage: true, colId: 'DaysToSellAtCurrentSelling', width: 10, type: 'number', },

         {headerName: 'SIV', field: 'SIV', colId: 'SIV', width: 10, type: 'currency',},
         {
            headerName: 'Profit',
            colId: 'Profit',
            valueGetter: (params) => this.profitGetter(params),
            width: 10,
            type: 'currency',
         },

         {
            headerName: "Reference 1",
            field: "Reference1",
            colId: "Reference1",
            width: 10,
            type: "label",
         },
         {
            headerName: "Reference 2",
            field: "Reference2",
            colId: "Reference1",
            width: 10,
            type: "label",
         },
         {
            headerName: "Reference 3",
            field: "Reference3",
            colId: "Reference1",
            width: 10,
            type: "label",
         },
         {
            headerName: "Mileage",
            field: "Mileage",
            colId: "Mileage",
            width: 10,
            type: "number",
         },

         {
            headerName: "Current PP",
            field: "CurrentPP",
            colId: "CurrentPP",
            width: 10,
            type: "percent",
         },
         {
            headerName: "Lowest PP",
            field: "LowestPP",
            colId: "LowestPP",
            width: 10,
            type: "percent",
         },
         {
            headerName: "2nd Lowest PP",
            field: "SecondLowestPP",
            colId: "SecondLowestPP",
            width: 10,
            type: "percent",
         },
         {
            headerName: "3rd Lowest PP",
            field: "ThirdLowestPP",
            colId: "ThirdLowestPP",
            width: 10,
            type: "percent",
         },

         {
            headerName: "Lowest PP Retailer",
            field: "LowestPPRetailer",
            colId: "LowestPPRetailer",
            width: 10,
            type: "label",
         },
         {
            headerName: "Lowest Vehicle Reg",
            field: "LowestPPVehicleReg",
            colId: "LowestPPVehicleReg",
            width: 10,
            type: "label",
         },
         {
            headerName: "2nd Lowest PP Retailer",
            field: "SecondLowestPPRetailer",
            colId: "SecondLowestPPRetailer",
            width: 10,
            type: "label",
         },
         {
            headerName: "2nd Lowest Vehicle Reg",
            field: "SecondLowestPPVehicleReg",
            colId: "SecondLowestPPVehicleReg",
            width: 10,
            type: "label",
         },
         {
            headerName: "3rd Lowest PP Retailer",
            field: "ThirdLowestPPRetailer",
            colId: "ThirdLowestPPRetailer",
            width: 10,
            type: "label",
         },
         {
            headerName: "3rd Lowest Vehicle Reg",
            field: "ThirdLowestPPVehicleReg",
            colId: "ThirdLowestPPVehicleReg",
            width: 10,
            type: "label",
         },

         {
            headerName: "Days To Sell",
            children: [
               {
                  headerName: "90%",
                  field: "D90",
                  colId: "D90",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "91%",
                  field: "D91",
                  colId: "D91",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "92%",
                  field: "D92",
                  colId: "D92",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "93%",
                  field: "D93",
                  colId: "D93",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "94%",
                  field: "D94",
                  colId: "D94",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "95%",
                  field: "D95",
                  colId: "D95",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "96%",
                  field: "D96",
                  colId: "D96",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "97%",
                  field: "D97",
                  colId: "D97",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "98%",
                  field: "D98",
                  colId: "D98",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "99%",
                  field: "D99",
                  colId: "D99",
                  width: 5,
                  type: "number",
               },
               {
                  headerName: "100%",
                  field: "D100",
                  colId: "D100",
                  width: 5,
                  type: "number",
               },
            ],
         },
         {
            headerName: "Adjusted Price",
            children: [
               {
                  headerName: "85%",
                  field: "P85",
                  colId: "P85",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "86%",
                  field: "P86",
                  colId: "P86",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "87%",
                  field: "P87",
                  colId: "P87",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "88%",
                  field: "P88",
                  colId: "P88",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "89%",
                  field: "P89",
                  colId: "P89",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "90%",
                  field: "P90",
                  colId: "P90",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "91%",
                  field: "P91",
                  colId: "P91",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "92%",
                  field: "P92",
                  colId: "P92",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "93%",
                  field: "P93",
                  colId: "P93",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "94%",
                  field: "P94",
                  colId: "P94",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "95%",
                  field: "P95",
                  colId: "P95",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "96%",
                  field: "P96",
                  colId: "P96",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "97%",
                  field: "P97",
                  colId: "P97",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "98%",
                  field: "P98",
                  colId: "P98",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "99%",
                  field: "P99",
                  colId: "P99",
                  width: 5,
                  type: "currency",
               },
               {
                  headerName: "100%",
                  field: "P100",
                  colId: "P100",
                  width: 5,
                  type: "currency",
               },
            ],
         },

         {
            headerName: "CAP Valuation",
            field: "CAPValuation",
            colId: "CAPValuation",
            width: 5,
            type: "currency",
         },
         //{ headerName: 'Is Vat Qualifying', field: 'IsVatQualifying', colId: 'IsVatQualifying', width: 10, type: 'label', },
      ];
      this.agGridMethodsService.workoutColWidths(this.service.batchResultsRowData, defs, 14, 6);

      (defs.find((x) => x.headerName == "Reg") as ColDef).width = 100;

      return defs as AdvertListingColGroupDef[];
   }

   profitGetter(params: ValueGetterParams<any>): any {
      const hasNoSIV: boolean = this.service.batchResultsRowData.map((x) => x.SIV).every((item) => item === null);
      if (hasNoSIV) {
         return 0;
      }
      return params.data.StrategyPrice - params.data.SIV;
   }

   onRowSelectionChange(): void {
      if (!this.service.tableLayoutManagement.gridApi) {
         return;
      }
      this.service.tableLayoutManagement.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
   }

   myBooleanRenderer(params: ICellRendererParams) {
      if (params.data) {
         return this.agGridMethodsService.booleanRenderer(params.value);
      } else {
         let tot = 0;
         params.node.allLeafChildren.forEach((child) => {
            if (child.data[params.colDef.field]) tot++;
         });
         const result: number = tot;
         return result;
      }
   }
   myCellRenderer(
      params: ICellRendererParams,
      pipeType: FormatType,
      decimalPlaces: number,
      aggregation: "sum" | "average" | "averageIfValue"
   ) {
      if (params.data) {
         return this.cphPipe.transform(params.value, pipeType, 0);
      } else {
         let filteredMap = {};
         params.api.forEachNodeAfterFilter((node) => {
            filteredMap[node.id] = true;
         });
         let tot = 0;
         let count = 0;
         let countWithValue = 0;
         //let column = params.api.getColumnDef(params.colDef.colId)
         params.node.allLeafChildren.forEach((child) => {
            if (filteredMap[child.id]) {
               tot += params.api.getValue(params.column, child); // child.data[params.colDef.field];
               count++;
            }
         });
         let result = 0;
         if (aggregation === "sum") {
            result = tot;
         } else if (aggregation === "average") {
            result = count > 0 ? tot / count : 0;
         } else if (aggregation === "averageIfValue") {
            result = countWithValue > 0 ? tot / countWithValue : 0;
         }
         return this.cphPipe.transform(result, pipeType, decimalPlaces);
      }
   }

   updateListOfVisibleNodes(api: GridApi) {
      this.allFilteredNodes = {};
      let i = 0;
      api.forEachNodeAfterFilter((node) => {
         i++;
         if (node.data) {
            this.allFilteredNodes[node.id] = true;
         }
      });
      this.lastUpdatedFilteredNodes = new Date();
   }


   myValueGetter(params: ValueGetterParams, aggregation: 'sum' | 'average' | 'averageIfValue') {
      // if(!this.lastUpdatedFilteredNodes ||  (new Date().getTime() - this.lastUpdatedFilteredNodes.getTime()) > 1000){
      //   this.updateListOfVisibleNodes(params.api)
      // }

      if (params.data) {
         return params.data[params.colDef.field];
      } else {
         let filteredMap = {};
         params.api.forEachNodeAfterFilter((node) => {
            filteredMap[node.id] = true;
         });
         let tot = 0;
         let count = 0;
         let countWithValue = 0;
         params.node.allLeafChildren.forEach((child) => {
            if (filteredMap[child.id]) {
               tot += child.data[params.colDef.field];
               count++;
               if (aggregation === "averageIfValue" && child.data[params.colDef.field]) {
                  countWithValue++;
               }
            }
         });
         if (aggregation === "sum") {
            return tot;
         } else if (aggregation === "average") {
            return count > 0 ? tot / count : 0;
         } else if (aggregation === "averageIfValue") {
            return countWithValue > 0 ? tot / countWithValue : 0;
         }
      }
   }

   providePinnedBottomRowData(): VehicleAdvertDetail[] {
      if (!this.service.tableLayoutManagement.gridApi) {
         return;
      }
      const params: BuildTotalAndAverageRowsParams = this.buildUpParamsForBottomRows();
      return this.agGridMethodsService.buildTotalAndAverageRows(params);
   }


   private buildUpParamsForBottomRows() {
      const params: BuildTotalAndAverageRowsParams = {
         colsToSkipAverageIfZero: [],
         colsToTotalOrAverage: [],
         colsToTotal: [],
         selectedCountFieldName: 'RetailerName',
         labelFieldName: 'Derivative',
         colsToSetToTrue: [],
         itemName: 'vehicle',
         api: this.service.tableLayoutManagement.gridApi,
         includeTotalRow: false,
         showTotalInAverage: false,
      }
      let colDefs = this.provideColumnDefs();
      colDefs.forEach(colDef => {
         if ((colDef as AdvertListingColGroupDef).children) {
            (colDef as AdvertListingColGroupDef).children.forEach(child => {
               if (child.shouldAverageIfValue) {
                  params.colsToTotalOrAverage.push(child.field)
               }
               if (child.shouldAverage) {
                  params.colsToTotalOrAverage.push(child.field);
               }
               if (child.shouldTotal) {
                  params.colsToTotal.push(child.field);
               }
            });
         } else {

            if (colDef.shouldAverageIfValue) {
               params.colsToTotalOrAverage.push((colDef as AdvertListingColDef).field);
            }
            if (colDef.shouldAverage) {
               params.colsToTotalOrAverage.push((colDef as AdvertListingColDef).field);
            }
            if (colDef.shouldTotal) {
               params.colsToTotal.push((colDef as AdvertListingColDef).field);
            }
         }
      });
      return params;
   }

   getContextMenuItems(params: GetContextMenuItemsParams<any, any>): (string | MenuItemDef)[] {
      const selectedNodeIds: string[] = this.service.tableLayoutManagement.gridApi.getSelectedNodes().map((x) => x.id);
      const row: StockPricingSitesOverview = params.node.data;
      if (!selectedNodeIds.includes(params.node.id)) {
         this.service.tableLayoutManagement.gridApi.forEachLeafNode((node) => {
            node.setSelected(node.id === params.node.id);
         });
      }

      let result: (string | MenuItemDef)[] = this.tableLayoutManagementService.getTableContextMenuItems();

      result = result.concat([
         'separator', 'cut', 'copy', 'copyWithHeaders', 'copyWithGroupHeaders'
      ]);


      return result;
   }

   // saveNewTableState() {
   //   if (!this.service.tableLayoutManagement.gridColumnApi) return;

   //   const tableState: ColumnState[] = this.service.tableLayoutManagement.gridColumnApi.getColumnState();

   //   const modalRef: NgbModalRef = this.modalService.open(SimpleTextModalComponent);
   //   modalRef.componentInstance.header = 'Choose label';
   //   modalRef.componentInstance.chosenLabel = `New layout created ${this.cphPipe.transform(new Date(), 'date', 0)}`;
   //   modalRef.componentInstance.placeholder = 'Enter layout label...';

   //   modalRef.result.then(res => {
   //     if (!res.chosenLabel) return this.constants.toastDanger('Please provide a layout label');

   //     let params: AutoPriceTableStateParams = {
   //       State: JSON.stringify(tableState),
   //       FilterModel: this.service.tableLayoutManagement.gridApi.isAnyFilterPresent() ? JSON.stringify(this.service.tableLayoutManagement.gridApi.getFilterModel()) : null,
   //       Label: res.chosenLabel,
   //       PageName: 'valuations'
   //     }

   //     this.service.saveNewTableState(params, res.chosenLabel);
   //   }, () => { })
   // }

   // saveExistingTableState() {
   //   const tableState: ColumnState[] = this.service.tableLayoutManagement.gridColumnApi.getColumnState();

   //   let params: AutoPriceTableStateParams = {
   //     State: JSON.stringify(tableState),
   //     FilterModel: this.service.tableLayoutManagement.gridApi.isAnyFilterPresent() ? JSON.stringify(this.service.tableLayoutManagement.gridApi.getFilterModel()) : null,
   //     Label: this.service.chosenTableState.label,
   //     PageName: 'valuations'
   //   }

   //   this.service.saveExistingTableState(params);
   // }


   // maybeDeleteTableState() {
   //   this.modalService.open(this.deleteTableStateModal, { size: 'md', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then(() => {
   //     this.service.deleteTableState();
   //   }, () => {
   //     this.modalService.dismissAll();
   //   });
   // }

   provideDefaultBottomRows(): any[] {
      return [
         {
            SiteName: "0 selected",
         } as any,
         {} as any,
      ];
   }

   onGridReady(params) {
      this.service.tableLayoutManagement.gridApi = params.api;
      this.service.tableLayoutManagement.gridColumnApi = params.columnApi;
      this.gridOptions.context = { thisComponent: this };

      //reapply table col state
      if (this.service.chosenTableState) {
         this.service.tableLayoutManagement.gridColumnApi.applyColumnState({
            state: this.service.chosenTableState.columnState,
         });
         this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.chosenTableState.filterModel);
      }

      //reapply filter model
      if (this.service.chosenTableState?.filterModel) {
         this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.chosenTableState.filterModel);
         this.service.tableLayoutManagement.gridApi.onFilterChanged();
      }

      // if (this.service.externalFilterModel) {
      //   this.detailTableGridApi.setFilterModel(this.service.externalFilterModel)
      //   this.detailTableGridApi.onFilterChanged();
      // }

      this.service.batchResultsTableComponent = this;
      if (this.service.batchResultsRowData.length === 0) {
         this.service.tableLayoutManagement.gridColumnApi.autoSizeAllColumns();
      }

      this.service.tableLayoutManagement.originalColDefs = this.provideColumnDefs();

      this.selections.triggerSpinner.emit({ show: false });
   }

   excelExport() {
      let tableModel = this.service.tableLayoutManagement.gridApi.getModel();
      this.excel.createSheetObject(tableModel, "Stock Pricing - Bulk Upload", 1, 1);
   }

   addEnergyTypeIcon(params: ICellRendererParams): string {
      if (!params.data) {
         return "";
      }
      if (params.node.isRowPinned()) {
         return "";
      }
      let icon: string;
      let icon2: string;

      if (params.value == "Diesel") icon = "fas fa-gas-pump";
      if (params.value == "Electric") icon = "fas fa-charging-station electric-green";
      if (params.value == "Petrol") icon = "fas fa-gas-pump petrol-green";
      if (params.value == "Petrol Hybrid" || params.value == "Petrol Plug-in Hybrid" || params.value == "Bi Fuel") {
         icon = "fas fa-plug blue";
         icon2 = "fas fa-gas-pump petrol-green";
      }

      return `${params.value}<i class="${icon}"></i><i class="${icon2}"></i>`;
   }

   updateState(chosenTableState: TableState) {
      this.service.tableLayoutManagement.gridColumnApi.applyColumnState({
         state: chosenTableState.columnState,
         applyOrder: true,
      });
      this.service.tableLayoutManagement.gridApi.setFilterModel(chosenTableState.filterModel);
   }

   resetState() {
      this.service.tableLayoutManagement.gridApi?.setFilterModel(null);
      this.service.tableLayoutManagement.gridColumnApi?.resetColumnState();
   }

   getDateFilterParams() {
      let params: IDateFilterParams = {
         comparator: (filterLocalDateAtMidnight: Date, cellValue: string) => {
            let dateAndTimeAsString: string = cellValue;

            if (dateAndTimeAsString === null) return -1;

            let dateOnly: string = dateAndTimeAsString.split('T')[0];
            let dateParts: string[] = dateOnly.split('-');
            let cellDate = new Date(
               Number(dateParts[0]),
               Number(dateParts[1]),
               Number(dateParts[2])
            )

            if (filterLocalDateAtMidnight.getTime() === cellDate.getTime()) return 0;

            if (cellDate < filterLocalDateAtMidnight) return -1;

            if (cellDate > filterLocalDateAtMidnight) return 1;

            return 0;
         },
      };

      return params;
   }

   clearSearchTerm() {
      this.service.searchTerm.setValue("");
      if (this.service.tableLayoutManagement.gridApi)
         this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value);
   }

   clearFilterModel() {
      this.service.chosenTableState.filterModel = null;
      this.service.tableLayoutManagement.gridApi.setFilterModel(null);
   }

   showFilterRemoveButton() {
      return this.service.tableLayoutManagement.gridApi?.isAnyFilterPresent();
   }

   shouldHideColumn(columnName: string) {
      let itemsInCol: any[] = this.service.batchResultsRowData.map((x) => x[columnName]);
      let areAllValuesNullOrEmpty: boolean = itemsInCol.every(
         (item) => item === "" || item === null || item === undefined
      );
      return areAllValuesNullOrEmpty;
   }

   customAdLinkRenderer(params) {
      const showIcon = params.data && !params.node.isRowPinned();
      const link = params.data?.Link;
      return `<a *ngIf="${showIcon}" id="iconWrapper" target="_blank" href="${link}">
    <i class="fas fa-car"></i></div>`;
   }
}
