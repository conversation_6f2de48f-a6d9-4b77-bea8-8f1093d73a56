/* eslint-disable no-useless-constructor */
import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core'
import { Router } from '@angular/router'
import { NgbModal } from '@ng-bootstrap/ng-bootstrap'
import { Grid<PERSON><PERSON> } from 'ag-grid-community'
import { Subscription } from 'rxjs'
import { DealDetailsComponent } from 'src/app/components/dealDetails/dealDetails.component'
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service'
import { localeEs } from 'src/environments/locale.es.js'
import { CphPipe } from '../../../../cph.pipe'
import { GridOptionsCph } from 'src/app/model/GridOptionsCph'
import { ConstantsService } from '../../../../services/constants.service'
import { ExcelExportService } from '../../../../services/excelExportService'
import { SelectionsService } from '../../../../services/selections.service'
import { SalesCommissionVindisService } from '../../salesCommissionVindis.service'
import { ColumnTypesService } from 'src/app/services/columnTypes.service'

@Component({
  selector: 'commissionStatementTableVindis',

  templateUrl: './commissionStatementTableVindis.component.html',
  styleUrls: ['./../../../../../styles/components/_agGrid.scss'],

})
export class CommissionStatementTableVindisComponent implements OnInit {
  @ViewChild('gridHolder', { static: true }) tableContainer: ElementRef;

  @HostListener('window:resize', [])
  private onresize (event) {
    this.selections.screenWidth = window.innerWidth
    this.selections.screenHeight = window.innerHeight
    if (this.gridApi) {
      this.gridApi.resetRowHeights()
      this.resizeGrid()
    }
  }

  columnDefs: any[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;
  subscription: Subscription;

  constructor (
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public columnTypeService: ColumnTypesService,
    public modalService: NgbModal,
    public router: Router,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: SalesCommissionVindisService
  ) { }

  onGridReady (params) {
    this.gridApi = params.api
    this.gridColumnApi = params.columnApi
    this.mainTableGridOptions.context = { thisComponent: this }
    this.gridApi.sizeColumnsToFit()
  }

  ngOnInit () {
    // this.sortRowData()
    this.initParams()
   
  }


  dealWithNewData(){
    this.gridApi.setRowData(this.getRowData())
    this.buildAndSetBottomRow();
  }

  

  getRowData (): any[] {
    return this.service.chosenPerson.CommissionItems
  }

  ngOnDestroy () {
    if (this.subscription) this.subscription.unsubscribe()
  }

  initParams () {
    // const baseHeight = window.innerWidth > 1550 ? 4 : 0
    const gridScaleValue = this.tableContainer.nativeElement.clientWidth / 1520 // actual measurement 1230.

    //this.gridMethods.topBottomHighlights = [];

    // table definitions
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang === 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      domLayout: 'autoHeight',
      suppressPropertyNamesCheck: true,
      onRowClicked: (params) => this.onRowClick(params),
      onGridReady: (params) => this.onGridReady(params),
      getRowHeight: (params) => {
        const normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)))
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      getMainMenuItems:(params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params,[]),
      defaultColDef: {
        resizable: true,
        sortable: true,
        floatingFilter:true
      },
      // onFilterChanged:(params)=>this.buildAndSetBottomRow(),
      
      getRowClass: (params) => params.data.isAdjustment ? 'adjustment' : 'deal',
      rowData: this.getRowData(),
      pinnedBottomRowData: this.buildAndSetBottomRow(),
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },

      columnDefs: [
        { headerName: '', valueGetter: (params) => params.node.rowPinned === 'bottom' ? '' : params.node.rowIndex + 1, colId: 'count', width: gridScaleValue * 30, type: 'label' },
        { headerName: 'Stock No.', field: 'StockNumber', colId: 'StockNumber', width: gridScaleValue * 90, type: 'labelCentre' },
        {
          headerName: 'Reg',
          field: 'Reg',
          cellRenderer: (params) => params.node.rowPinned === 'bottom' ? '' : `<div class="visibleAboveMd regPlate">    ${this.cphPipe.transform(params.value, 'numberPlate', 0)}</div>`,
          colId: 'Reg',
          width: gridScaleValue * 90,
          type: 'labelCentre'
        },
        { headerName: 'Customer', field: 'Customer', colId: 'Customer', width: gridScaleValue * 120, type: 'label' },
        {
          headerName: 'Type',
          valueGetter: (params) => params.node.rowPinned ? 'TOTAL' : params.data.isAdjustment ? 'Adjustment' : 'Deal',
          colId: 'Type',
          width: gridScaleValue * 60,
          type: 'label'
        },
        { headerName: 'Vehicle Type', field: 'VTypeType', colId: 'VTypeType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Order Type', field: 'OTypeType', colId: 'OTypeType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Comm Type', field: 'VehicleCommissionType', colId: 'VehicleCommissionType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Order date', valueGetter: (params) => params.node.rowPinned ? '' : params.data.OrderDate, field: 'OrderDate', colId: 'OrderDate', width: gridScaleValue * 80, type:'dateShort' },
        { headerName: 'Accounting date', valueGetter: (params) => params.node.rowPinned ? '' : params.data.AccountingDate, field: 'AccountingDate', colId: 'AccountingDate', width: gridScaleValue * 80, type:'dateShort' },

        {
          headerName: 'Units',
          children: [
            { headerName: 'Q', field: 'Units_Count', colId: 'Units_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: '', valueGetter: (params) => this.unitsGetter(params, ''), colId: '', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: 'Paint',
          children: [
            { headerName: 'Q', field: 'PaintProtection_Count', colId: 'PaintProtection_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRatePaint', valueGetter: (params) => this.productsGetter(params, 'PayRatePaint'), colId: 'PayRatePaint', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: this.constants.translatedText.Gap,
          children: [
            { headerName: 'Q', field: 'Gap_Count', colId: 'Gap_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateGap', valueGetter: (params) => this.productsGetter(params, 'PayRateGap'), colId: 'PayRateGap', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: 'Cosmetic',
          children: [
            { headerName: 'Q', field: 'Cosmetic_Count', colId: 'Cosmetic_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateCosmetic', valueGetter: (params) => this.productsGetter(params, 'PayRateCosmetic'), colId: 'PayRateCosmetic', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: 'Wheel',
          children: [
            { headerName: 'Q', field: 'TyreAlloy_Count', colId: 'TyreAlloy_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateTyreAlloy', valueGetter: (params) => this.productsGetter(params, 'PayRateTyreAlloy'), colId: 'PayRateTyreAlloy', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: 'WheelGuard',
          children: [
            { headerName: 'Q', field: 'WheelGuard_Count', colId: 'WheelGuard_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateWheelGuard', valueGetter: (params) => this.productsGetter(params, 'PayRateWheelGuard'), colId: 'PayRateWheelGuard', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: 'Warranty',
          children: [
            { headerName: 'Q', field: 'Warranty_Count', colId: 'Warranty_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateWarranty', valueGetter: (params) => this.productsGetter(params, 'PayRateWarranty'), colId: 'PayRateWarranty', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: 'Mot',
          children: [
            { headerName: 'Q', field: 'Mot_Count', colId: 'Mot_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateMot', valueGetter: (params) => this.productsGetter(params, 'PayRateMot'), colId: 'PayRateMot', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: 'R_Assist',
          children: [
            { headerName: 'Q', field: 'RoadsideAssist_Count', colId: 'RoadsideAssist_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateRoadsideAssist', valueGetter: (params) => this.productsGetter(params, 'PayRateRoadsideAssist'), colId: 'PayRateRoadsideAssist', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },
        {
          headerName: 'Finance',
          children: [
            { headerName: 'Q', field: 'Finance_Count', colId: 'Finance_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateFinance', valueGetter: (params) => this.productsGetter(params, 'PayRateFinance'), colId: 'PayRateFinance', width: gridScaleValue * 45, type: 'labelCentre' }
          ]
        },

        { headerName: 'Total Commission', field: 'TotalCommission', colId: 'TotalCommission', width: gridScaleValue * 90, type: 'currency' }

      ]

    }
  }

  unitsGetter (params, field: string) {
    if (!params.node.data) return
    if (!field) return
    if (params.node.rowPinned === 'bottom') return this.cphPipe.transform(`params.node.data.${field}`, 'currency', 0)
    return ' @' + this.cphPipe.transform(`params.node.data.${field}`, 'currency', 0)
  }

  productsGetter (params, field: string) {
    if (!params.node.data) return
    if (params.node.rowPinned === 'bottom') return this.cphPipe.transform(params.data[field], 'currency', 0)
    return params.data[field] !== 0 ? ' @' + this.cphPipe.transform(params.data[field], 'currency', 0) : '-'
  }

  cellHighlighter (params) {
    if (params.node.rowPinned === 'bottom') return 'agAlignCenter'
    if (!params.data) return 'agAlignCenter'
    const colId = params.colDef.colId
    let value = 0
    if (colId === 'Units') value = params.data.Units
    else if (colId === 'Gap') value = params.data.GapSold - params.data.Gap_Previous
    else if (colId === 'Paint') value = params.data.PaintProtectionSold - params.data.PaintProtectionPrevious
    else if (colId === 'Cosmetic') value = params.data.CosmeticSold - params.data.CosmeticPrevious
    else if (colId === 'Finance') value = params.data.FinanceSold - params.data.FinancePrevious

    if (value > 0) return 'good agAlignCenter'
    else if (value < 0) return 'bad agAlignCenter'
    else return 'agAlignCenter'
  }

  buildAndSetBottomRow () {
    return [this.service.chosenPerson]
  }

  onRowClick (params): void {
    if (params.node.rowPinned === 'bottom' || params.node.data.DealId === 0) {
      return
    }

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    setTimeout(() => {
      let modalRef

      if (this.constants.environment.dealDetailModal_componentName === 'DealDetailsRRGComponent') {
        modalRef = this.modalService.open(DealDetailsComponent)
      }

      if (this.constants.environment.dealDetailModal_componentName === 'DealDetailsComponent') {
        modalRef = this.modalService.open(DealDetailsComponent)
      }

      // I give to modal
      modalRef.componentInstance.givenDealId = params.data.DealId
      modalRef.result.then((result) => { // I get back from modal
        if (result) {
          // thing
        }
      })
    }, 10)
  }

 

  cellClassProviderWithColourFont (params) {
    if (params.colDef.goods && params.colDef.goods.indexOf(params.data.label) > -1) {
      return 'good ag-right-aligned-cell'
    } else if (params.colDef.bads && params.colDef.bads.indexOf(params.data.label) > -1) {
      return 'bad ag-right-aligned-cell'
    }
    // still here so no goods / bads so do number colour
    if (params.value < 0) {
      return 'badFont ag-right-aligned-cell'
    } else {
      return 'ag-right-aligned-cell'
    }
  }

  resizeGrid () {
    if (this.gridApi) this.gridApi.sizeColumnsToFit()
  }

  excelExport () {
    // get tableModel from ag-grid
    const tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Commission Statement', 1, 1)
  }
}
