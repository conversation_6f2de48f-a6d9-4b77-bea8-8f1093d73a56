<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        {{ constants.translatedText.OrderRate_OrdersBySite }}
      </div>
    </h4>


  </nav>

  <div>

    <!-- <vehicleTypePickerSpain
      *ngIf="constants.environment.customer == 'RRGSpain'"
      [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"
      (updateVehicleTypes)="onUpdateVehicleTypes($event)"
    >
    </vehicleTypePickerSpain> -->

    <!-- VehicleType selector -->
    <vehicleTypePicker *ngIf="constants.environment.customer !== 'RRGSpain'"
    [vehicleTypeTypesFromParent]="service.vehicleTypeTypes" [buttonClass]="'buttonGroupCenter'"
      (updateVehicleTypes)="onUpdateVehicleTypes($event)"></vehicleTypePicker>

    <!-- OrderType selector -->
    <!-- <orderTypePicker *ngIf="constants.environment.orderTypePicker" [orderTypeTypesFromParent]="service.orderTypeTypes"
      [buttonClass]="'buttonGroupCenter'" (updateOrderTypes)="onUpdateOrderTypes($event)"></orderTypePicker> -->

    <!-- Franchise selector -->
    <franchisePicker [franchisesFromParent]="service?.franchises" [buttonClass]="'buttonGroupCenter'"
      (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>


  </div>

  <nav class="pageSpecific">


    


  </nav>
</nav>

<!-- Main Page -->
<ng-container *ngIf="!!service.orders">
  <ng-container *ngIf="!service.ordersForSite; else showSiteBreakdown">
    <ordersBySiteTable [isRegionalTable]="false" *ngIf="!!service.orders"></ordersBySiteTable>
    <ordersBySiteTable [isRegionalTable]="true" *ngIf="!!service.orders"></ordersBySiteTable>
  </ng-container>
  <ng-template #showSiteBreakdown>
    <ratioOrdersForSiteTable></ratioOrdersForSiteTable>
  </ng-template>
  
</ng-container>