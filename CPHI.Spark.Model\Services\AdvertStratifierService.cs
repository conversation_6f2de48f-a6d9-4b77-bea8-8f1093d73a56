﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace CPHI.Spark.Model.AutoPrice
{
   public static class AdvertStratifierService
   {


      public static List<string> standardModels = new List<string>
        {
            // Add your standard models here in lowercase
            "clio", "arkana", "trafic", "master","megane","kangoo"
        };


      public static List<int> ATPriceBreaks =
      new List<int>()
          {
                3000,3500,4000,4500,5000,5500,6000,6500,7000,7500,8000,8500,9000,9500,10000,11000,12000,13000,14000,15000,16000,17000,18000,19000,20000,22500,25000,27500,30000,35000,40000,
                500,1000,1500,2000,2500,
                50000,55000,60000,65000,70000,75000,100000,250000,500000,1000000,2000000
          };

      public static string Stratify(decimal value, StrategyFactorName dimension)
      {
         if (dimension == StrategyFactorName.RetailRatingBand)
         { return CPHI.Spark.Model.AutoPrice.BandingsService.ProvideRetailRatingBanding(value); }
         if (dimension == StrategyFactorName.RetailRating10sBand)
         { return CPHI.Spark.Model.AutoPrice.BandingsService.ProvideRetailRating10sBanding(value); }
         if (dimension == StrategyFactorName.DaysListedBand)
         { return CPHI.Spark.Model.AutoPrice.BandingsService.ProvideDaysBanding(value); }
         if (dimension == StrategyFactorName.DaysInStockBand)
         { return CPHI.Spark.Model.AutoPrice.BandingsService.ProvideDaysBanding(value); }
         throw new Exception("Unknown dimension");
      }

      public static string Summarise(string value, string dimension)
      {
         if (dimension == "Colour")
         { return SummariseColour(value); }
         throw new Exception("Unknown dimension");
      }

      public static string StandardiseModelName(string model, string retailerMakes, string make)
      {
         try
         {

            if (retailerMakes == null || model == null || make == null)
            {
               return model;
            }
            if (!retailerMakes.Split(',').Any(x => x.ToLower() == make.ToLower()))
            {
               return "Non Franchise";
            }
            if (string.IsNullOrEmpty(model))
            {
               return model;
            }

            string lowerCaseModel = model.ToLowerInvariant();
            string standardModel = standardModels.FirstOrDefault(standard => lowerCaseModel.StartsWith(standard));

            if (standardModel != null)
            {
               return ToTitleCase(standardModel);
            }

            return ToTitleCase(lowerCaseModel); // Or return null or original model based on your requirement
         }
         catch (Exception ex)
         {
            return "";
         }
      }

      private static string ToTitleCase(string input)
      {
         TextInfo textInfo = CultureInfo.CurrentCulture.TextInfo;
         return textInfo.ToTitleCase(input);
      }


      private static string SummariseColour(string value)
      {
         Dictionary<string, string> colorLookup = new Dictionary<string, string>
{
    {"NULL", "Unknown"},
    {"Abyss Blue", "Blue"},
    {"Aconite", "Purple"},
    {"Additional metallic - Zanzibar Blue", "Blue"},
    {"Akatsuki Copper with Pearl Black Roof", "Brown"},
    {"Alpine Blue", "Blue"},
    {"arctic white", "White"},
    {"Arctic White with Diamond Black Roof", "White"},
    {"Arizona", "Orange"},
    {"Arizona Orange", "Orange"},
    {"Azur Blue", "Blue"},
    {"BEIGE", "Brown"},
    {"BLACK", "Black"},
    {"Black Tulip", "Black"},
    {"BLACK/CREAM", "Black"},
    {"BLACK/GREY", "Black"},
    {"BLACK/ORANGE", "Black"},
    {"BLACK/SILVER", "Black"},
    {"BLACK/WHITE", "Black"},
    {"Blue", "Blue"},
    {"BLUE/BLACK", "Blue"},
    {"BLUE/CREAM", "Blue"},
    {"BLUE/GREY", "Blue"},
    {"BLUE/SILVER", "Blue"},
    {"Bronze", "Brown"},
    {"BROWN", "Brown"},
    {"Burgundy", "Red"},
    {"Caddy Blue", "Blue"},
    {"Cedar Green", "Green"},
    {"Celadon Blue", "Blue"},
    {"ceramic grey", "Grey"},
    {"Ceramic Grey with Diamond Black Roof", "Grey"},
    {"Ceramic Grey with Pearl Black Roof", "Grey"},
    {"Copper/Bronze", "Brown"},
    {"CREAM/BLACK", "Black"},
    {"Deep Black", "Black"},
    {"Desert Orange", "Orange"},
    {"Diamond Black", "Black"},
    {"diamond black with shadow grey roof", "Black"},
    {"Dusty Grey", "Grey"},
    {"Dusty Khaki", "Green"},
    {"Emerald Green", "Green"},
    {"Fire Orange with Deep Black Contrasting Roof", "Orange"},
    {"Flame Red", "Red"},
    {"Flame Red with Diamond Black Roof", "Red"},
    {"France Blue", "Blue"},
    {"Fusion Red", "Red"},
    {"Glacier White", "White"},
    {"glacier white with diamond black roof", "White"},
    {"glacier white with shadow grey roof", "White"},
    {"Green", "Green"},
    {"Grey", "Grey"},
    {"GREY/BLACK", "Grey"},
    {"GREY/ORANGE", "Grey"},
    {"Gun Metallic", "Grey"},
    {"Highland Grey", "Grey"},
    {"highland grey with diamond black roof", "Grey"},
    {"ID Metallic - Amethyst", "Purple"},
    {"ID Metallic - Arizona", "Orange"},
    {"ID Metallic - Desert orange", "Orange"},
    {"ID Metallic - Flame red", "Red"},
    {"ID Metallic - Iron blue", "Blue"},
    {"ID Metallic - Pearl white", "White"},
    {"ID Metallic - Valencia orange", "Orange"},
    {"Iridescent White", "White"},
    {"iron blue", "Blue"},
    {"Iron Blue with Diamond Black Roof", "Blue"},
    {"Jet Black", "Black"},
    {"Lime Green", "Green"},
    {"Matt Racing Blue", "Blue"},
    {"matte shadow grey with diamond black roo", "Grey"},
    {"MAUVE/PURPLE", "Purple"},
    {"mercury", "Grey"},
    {"Metallic - Admiral blue", "Blue"},
    {"Metallic - Alabaster white", "White"},
    {"Metallic - Arctic white", "White"},
    {"Metallic - Azurite blue", "Blue"},
    {"Metallic - Blade Silver", "Silver"},
    {"Metallic - Cinder red", "Red"},
    {"Metallic - Cosmos blue", "Blue"},
    {"Metallic - Desert orange", "Orange"},
    {"Metallic - Diamond black", "Black"},
    {"Metallic - Dune Beige", "Brown"},
    {"Metallic - Fusion red", "Red"},
    {"Metallic - Gun metal", "Grey"},
    {"Metallic - Gun Metallic grey", "Grey"},
    {"Metallic - Highland grey", "Grey"},
    {"Metallic - Iron blue", "Blue"},
    {"Metallic - Mercury", "Grey"},
    {"Metallic - Metallic black", "Black"},
    {"Metallic - Metallic Grey", "Grey"},
    {"Metallic - Mink", "Brown"},
    {"Metallic - Moonstone grey", "Grey"},
    {"Metallic - Ocean blue", "Blue"},
    {"Metallic - Oyster grey", "Grey"},
    {"Metallic - Pearl black", "Black"},
    {"Metallic - Shadow grey", "Grey"},
    {"Metallic - Slate grey", "Grey"},
    {"Metallic - Stone", "Grey"},
    {"Metallic - Titanium", "Silver"},
    {"Metallic - Titanium grey", "Grey"},
    {"Metallic - Valencia orange", "Orange"},
    {"Metallic - Zanzibar Blue", "Blue"},
    {"metallic black", "Black"},
    {"metallic grey", "Grey"},
    {"Metallic Paint", "Grey"},
    {"midnight blue", "Blue"},
    {"midnight blue with diamond black roof", "Blue"},
    {"midnight blue with shadow grey roof", "Blue"},
    {"Mineral White", "White"},
    {"Montobello Grey", "Grey"},
    {"Moonstone Grey", "Grey"},
    {"Neptune Blue", "Blue"},
    {"Normandy Green", "Green"},
    {"ORANGE", "Orange"},
    {"ORANGE/BLACK", "Orange"},
    {"ORANGE/CREAM", "Orange"},
    {"Oyster Grey", "Grey"},
    {"Oyster Grey with Diamond Black Roof", "Grey"},
    {"Pearl - Ceramic grey", "Grey"},
    {"Pearl - Storm white", "White"},
    {"Pearl Black", "Black"},
    {"pearl white", "White"},
    {"Pearl White with Diamond Black Roof", "White"},
    {"Premium Paint", "Grey"},
    {"Purple", "Purple"},
    {"Quartz White", "White"},
    {"RED", "Red"},
    {"RED/BLACK", "Red"},
    {"Renault ID - Blue celadon", "Blue"},
    {"Renault ID - Flame red", "Red"},
    {"Renault ID - Iron blue", "Blue"},
    {"Renault ID - Orange valencia", "Orange"},
    {"Renault ID - Oural green", "Green"},
    {"Renault ID metallic - Iron blue", "Blue"},
    {"Renault pearl - Pearl black", "Black"},
    {"shadow grey", "Grey"},
    {"Shadow Grey with Diamond Black Roof", "Grey"},
    {"Sierra Blue", "Blue"},
    {"Silver", "Silver"},
    {"SILVER/BLACK", "Silver"},
    {"Slate Grey", "Grey"},
    {"Solid - Boston blue", "Blue"},
    {"Solid - Crystal white", "White"},
    {"Solid - Glacier white", "White"},
    {"Solid - Sunflower yellow", "Yellow"},
    {"Special Matt paint  - Lime Green", "Green"},
    {"Special Metallic - Aconite", "Purple"},
    {"Special metallic - Alpine blue", "Blue"},
        {"Special Metallic - Flame Red", "Red"},
    {"Special Metallic - Malbec black", "Black"},
    {"Special Metallic - Phantom black", "Black"},
    {"Special Metallic - Zircon blue", "Blue"},
    {"Special metallic paint - Mistral blue", "Blue"},
    {"Special Pearl - Belladonna purple", "Purple"},
    {"Special Pearl - Tectonic grey", "Grey"},
    {"Special solid - Flame red", "Red"},
    {"Special Solid - Ice white", "White"},
    {"Special Solid - Ultra blue", "Blue"},
    {"starlight silver", "Silver"},
    {"Storm Grey", "Grey"},
    {"storm white", "White"},
    {"Stylish Silver", "Silver"},
    {"Sunset Orange", "Orange"},
    {"Sunset Orange with Deep Black Contrasting Roof", "Orange"},
    {"Teal Blue", "Blue"},
    {"Titanium Grey", "Grey"},
    {"Tornado Blue", "Blue"},
    {"Tungsten", "Grey"},
    {"Two - coat metallic", "Grey"},
    {"Ultra Blue", "Blue"},
    {"Universal White", "White"},
    {"Valencia Orange", "Orange"},
    {"WHITE", "White"},
    {"WHITE/BLACK", "White"},
    {"WHITE/BLUE", "White"},
    {"WHITE/GREY", "White"},
    {"WHITE/ORANGE", "White"},
    {"YELLOW", "Yellow"},

{"Black","Black"},
{"BLUE","Blue"},
{"GREEN","Green"},
{"GREY","Grey"},
{"Metallic - Highland Grey","Grey"},
{"Orange","Orange"},
{"Red","Red"},
{"SILVER","Silver"},
{"Special metallic - Burgundy","Red"},
{"Special metallic - Midnight blue","Blue"},
{"Special Renault ID - Iron Blue","Blue"},
{"Special two-tone solid - Ceramic grey with diamond black roof","Grey"},
{"Two tone paint","Unknown"},
{"Two-tone metallic - Shadow grey with diamond black roof","Grey"},
{"Two-tone solid - Ceramic grey with diamond black roof","Grey"},
{"White","White"},
                { "Yellow","Yellow"},

};
         if (value == null)
         { return "Unknown"; }
         if (colorLookup.TryGetValue(value, out string result))
         {
            return result;
         }
         return "Unknown";

      }
   }

}
