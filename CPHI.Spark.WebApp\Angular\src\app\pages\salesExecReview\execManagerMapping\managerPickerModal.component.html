<ng-template #modalRef let-modal>

    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">Manager Picker</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div [ngClass]="constants.environment.customer" class="modal-body alertModalBody lowHeight">

      <table class="cph fullWidth">

        <tbody>

            <!-- Name -->
            <tr>
              <td class="rowHeader">Start Month:</td>
              <td>
                <td>{{service.chosenMonth}} onwards</td>
            </tr>

            <tr>
              <td class="rowHeader">Exec:</td>
              <td>
                <td>{{service.chosenExec}}</td>
            </tr>

          
            <tr>
              <td class="rowHeader">Current Manager:</td>
              <td>
                <td>{{currentManager}}</td>
            </tr>

            <tr>
              <td class="rowHeader">Select Manager:</td>
              <td></td>
              <td>
                <div ngbDropdown container="body" id="chooseNewRole" [autoClose]="true" >

                  <button class="btn btn-primary" ngbDropdownToggle>
                     {{service.chosenManager}}
                  </button>

                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <button (click)="chooseManager(manager)" *ngFor="let manager of service.managers" ngbDropdownItem>{{manager.Name}}</button>
                  </div>
          
                  </div>
                </td>
            </tr>

          </tbody>
        </table>



    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="modal.close()">Save</button>
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">{{constants.translatedText.Cancel}}</button>
    </div>

</ng-template>