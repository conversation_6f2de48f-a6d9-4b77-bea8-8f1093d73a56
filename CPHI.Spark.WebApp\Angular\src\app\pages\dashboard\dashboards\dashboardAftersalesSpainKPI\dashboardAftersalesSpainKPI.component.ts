import { Component, EventEmitter, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardDataPackSpainAftersales, DashboardDataSpainAftersalesParams } from '../../dashboard.model';
import { DashboardService } from '../../dashboard.service';
import { CitNowSiteSummary } from 'src/app/model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { GridApi, ValueGetterParams, RowClickedEvent, CellClickedEvent } from 'ag-grid-community';
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { CphPipe } from 'src/app/cph.pipe';
import { AftersalesKPIRow } from './dashboardAftersalesSpainKPI.model';
import { DashboardKPIService } from './dashboardAftersalesSpainKPI.service';
import { DashboardPageNew } from '../../dashboard.component';


@Component({
  selector: 'dashboardAftersalesSpainKPI',
  templateUrl: './dashboardAftersalesSpainKPI.component.html',
  styleUrls: ['./dashboardAftersalesSpainKPI.component.scss']
})


export class DashboardAftersalesSpainKPIComponent implements OnInit {

  weekStart: Date;
  rowData: AftersalesKPIRow[];
  sub: Subscription;
  mainTableGridOptions: GridOptionsCph;
  
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;

  newDataEmitter:EventEmitter<void>

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public dashboardService: DashboardService,
    public service: DashboardKPIService,
    public cphPipe: CphPipe,

  ) {


  }


  ngOnInit() {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.dashboardService.getSpainMonths();
    this.service.initParams();
    this.service.getData();

    this.sub = this.dashboardService.getNewDataTrigger.subscribe(res=> {
      this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
      this.service.getData();
    })

  }

  ngOnDestroy() {
    if(!!this.sub)this.sub.unsubscribe();
   }

   choosePage(page:DashboardPageNew){
    this.dashboardService.chosenPage = page;
    this.dashboardService.singleLineNav = false;
  }


}
