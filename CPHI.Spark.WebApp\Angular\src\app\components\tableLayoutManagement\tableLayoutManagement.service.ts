import { EventEmitter, Injectable } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { ColDef, ColumnState, MenuItemDef } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { CphPipe } from 'src/app/cph.pipe';
import { AutoPriceTableState } from 'src/app/model/AutoPriceTableState';
import { AutoPriceTableStateParams } from 'src/app/model/AutoPriceTableStateParams';
import { SharedTableStateDetails } from 'src/app/model/SharedTableStateDetails';
import { StandardTableStateDetails } from 'src/app/model/StandardTableStateDetails';
import { StandardTableStateParams, TableStateIdAndSortIndex } from 'src/app/model/StandardTableStateParams';
import { TableLayoutManagementParams } from 'src/app/model/TableLayoutManagementParams';
import { TableStateDetails } from 'src/app/model/TableStateDetails';
import { ConfirmationModalComponent } from 'src/app/pages/fleetOrderbook/confirmationModal/confirmationModal.component';
import { SimpleTextModalComponent } from 'src/app/pages/fleetOrderbook/simpleTextModal/simpleTextModal.component';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { CustomiseColumnsModalComponent } from '../customiseColumnsModal/customiseColumnsModal.component';
import { SelectedTableState } from '../../model/SelectedTableState';
import { AutoPriceTableStatePatch } from '../../model/AutoPriceTableStatePatch';

export interface UserProvidedColDef {
  colId: string;
  field: string;
  headerName: string;
  type: string;
  width: number;
}

@Injectable({
  providedIn: 'root'
})
export class TableLayoutManagementService {
  parent: TableLayoutManagementParams;
  shareReport: boolean = false;
  columnsForTypeahead: string[];
  allColumns: ColDef[];
  searchListUpdatedEmitter: EventEmitter<string[]> = new EventEmitter<string[]>();
  defaultFilterState: any;
  onlyApplyColumns: boolean;
  onlyApplyFilters: boolean;
  canModifyStandardReports: boolean = false;
  refreshReportsListSubscription: Subscription;

  constructor(
    public modalService: NgbModal,
    public cphPipe: CphPipe,
    public constantsService: ConstantsService,
    public apiAccessService: ApiAccessService,
    public selectionsService: SelectionsService,
    public agGridMethodsService: AGGridMethodsService
  ) {
  }

  setParent(parent: TableLayoutManagementParams) {
    this.parent = parent;
  }


  initParams() {
    this.setParent(this.parent);
    this.initialise();
    this.canModifyStandardReports = this.selectionsService.user.permissions.canSetStandardReports;// this.selectionsService.user.RoleName === 'Full Access' || this.selectionsService.user.RoleName === 'System Administrator';

    this.refreshReportsListSubscription = this.parent.refreshReportsListEmitter?.subscribe(() => {
      this.resetTableState();
      this.initialise();
    })
    this.setColumnsForTypeahead();

    if (this.parent.lastTableState) {
      this.parent.gridColumnApi.applyColumnState({
        state: this.parent.lastTableState.State,
        applyOrder: true
      });
      this.parent.gridApi.setFilterModel(this.parent.lastTableState.FilterModel);
    }
  }


  initialise() {
    this.getTableStateLabels();
    this.getSharedTableStateLabels();
    this.getStandardTableStateLabels();
    this.getAvailableTableStateLabels();
    this.getSparkTableStateLabels();
    this.setColumnsForTypeahead();
  }

  setDefaultFilterState(filterState: any) {
    this.defaultFilterState = filterState;
  }

  getTableStateLabels() {
    this.apiAccessService.get('api/AutoPrice', 'GetTableStateLabels', this.getTableStateParams()).subscribe((res: TableStateDetails[]) => {
      this.parent.ownTableStates = res;
    })
  }

  getSharedTableStateLabels() {
    this.apiAccessService.get('api/AutoPrice', 'GetSharedTableStateLabels', this.getTableStateParams()).subscribe((res: SharedTableStateDetails[]) => {
      this.parent.sharedTableStates = res;
    })
  }

  getStandardTableStateLabels() {
    this.apiAccessService.get('api/AutoPrice', 'GetStandardTableStateLabels', this.getTableStateParams()).subscribe((res: StandardTableStateDetails[]) => {
      this.parent.standardTableStates = res;
    })
  }

  getSparkTableStateLabels() {
    this.apiAccessService.get('api/AutoPrice', 'GetSparkTableStateLabels', this.getTableStateParams()).subscribe((res: StandardTableStateDetails[]) => {
      this.parent.sparkTableStates = res;
    })
  }

  getAvailableTableStateLabels() {
    this.apiAccessService.get('api/AutoPrice', 'GetAvailableTableStateLabels', this.getTableStateParams()).subscribe((res: StandardTableStateDetails[]) => {
      this.parent.availableTableStates = res;
    })
  }

  loadTableStateById(id: number, isSparkState: boolean, onlyColumns?: boolean, onlyFilters?: boolean) {
    this.apiAccessService.get('api/AutoPrice', 'GetTableStateById', [{
      key: 'id',
      value: id
    }]).subscribe((res: AutoPriceTableState) => {
      let loadedTableState = JSON.parse(res.State as string) as ColumnState[];

      loadedTableState.forEach((column => {
        column.width = this.agGridMethodsService.getRelativeColumnWidth(column.width);
      }))

      this.parent.loadedTableState = loadedTableState;

      this.parent.selectedTableState = {
        id: res.Id,
        label: res.Label,
        isSparkState: isSparkState
      }

      this.parent.filterModel = JSON.parse(res.FilterModel);

      // this.onlyApplyColumns = onlyColumns;
      // this.onlyApplyFilters = onlyFilters;

      if (!onlyFilters) {
        this.parent.gridColumnApi.applyColumnState({
          state: this.parent.loadedTableState,
          applyOrder: true
        });
        this.parent.gridApi.setFilterModel(null);
      }

      if (!onlyColumns) {
        this.parent.gridApi.setFilterModel(this.parent.filterModel);
        this.parent.gridColumnApi.applyColumnState({
          state: null
        });
      }
    })
  }

  saveExistingTableState() {
    let tableState: ColumnState[] = this.parent.gridColumnApi.getColumnState();

    tableState.forEach((column => {
      column.width = this.agGridMethodsService.getAbsoluteColumnWidth(column.width);
    }))

    let params: AutoPriceTableStateParams = {
      State: JSON.stringify(tableState),
      FilterModel: this.parent.gridApi.isAnyFilterPresent() ? JSON.stringify(this.parent.gridApi.getFilterModel()) : null,
      Label: this.parent.selectedTableState.label,
      PageName: this.parent.pageName,
      Id: this.parent.selectedTableState.id
    }

    this.apiAccessService.post('api/AutoPrice', 'SaveTableState', params).subscribe((res: AutoPriceTableState) => {
      this.constantsService.toastSuccess('Saved report');
    })
  }

  showRenameModal(tableState: SelectedTableState) {

    const label = tableState.label;

    const modalRef: NgbModalRef = this.modalService.open(SimpleTextModalComponent);
    modalRef.componentInstance.header = 'Choose name';
    modalRef.componentInstance.chosenLabel = label;
    modalRef.componentInstance.placeholder = 'Enter report name...';

    modalRef.result.then(res => {

      if (!res.chosenLabel) {
        return this.constantsService.toastDanger('Please provide a report');
      }

      modalRef.result.then(res => {

        if (!res.chosenLabel) {
          return this.constantsService.toastDanger('Please provide a report');
        }

        // Update the displayed label
        this.parent.selectedTableState.label = res.chosenLabel;

        // Update the label in the dropdown
        this.parent.ownTableStates.find(x => x.Id === tableState.id).StateLabel = res.chosenLabel;

        this.patchTableState(tableState.id, { Label: res.chosenLabel }).subscribe(() => {
          this.constantsService.toastSuccess('Report renamed')
        }, () => {
          this.constantsService.toastDanger('Failed to rename report')
        });
      }, () => {
      }
      );

    }, () => {
    });
  }

  maybeSaveNewTableState() {
    if (!this.parent.gridColumnApi) return;

    let tableState: ColumnState[] = this.parent.gridColumnApi.getColumnState();
    tableState.forEach((column => {
      column.width = this.agGridMethodsService.getAbsoluteColumnWidth(column.width);
    }))

    const modalRef: NgbModalRef = this.modalService.open(SimpleTextModalComponent);
    modalRef.componentInstance.header = 'Choose name';
    modalRef.componentInstance.chosenLabel = `New report created ${this.cphPipe.transform(new Date(), 'date', 0)}`;
    modalRef.componentInstance.placeholder = 'Enter report name...';

    modalRef.result.then(res => {
      if (!res.chosenLabel) return this.constantsService.toastDanger('Please provide a report');

      let params: AutoPriceTableStateParams = {
        State: JSON.stringify(tableState),
        FilterModel: this.parent.gridApi.isAnyFilterPresent() ? JSON.stringify(this.parent.gridApi.getFilterModel()) : null,
        Label: res.chosenLabel,
        PageName: this.parent.pageName,
        IsRenault: this.parent.isRenault
      }

      this.saveNewTableState(params);
    }, () => {
    })
  }

  saveNewTableState(params: AutoPriceTableStateParams) {
    this.apiAccessService.post('api/AutoPrice', 'SaveTableState', params).subscribe((result: AutoPriceTableState) => {
      this.constantsService.toastSuccess('Saved report');
      this.initialise();

      this.parent.selectedTableState = {
        id: result.Id,
        label: result.Label,
        isSparkState: false,
      };
    });
  }

  patchTableState(id, patch: AutoPriceTableStatePatch) {
    return this.apiAccessService.patch('api/AutoPrice', 'TableState/' + id, patch);
  }


  maybeDeleteTableState() {
    const modalRef: NgbModalRef = this.modalService.open(ConfirmationModalComponent, { size: 'sm' });
    modalRef.componentInstance.header = `Are you sure you want to delete the report '${this.parent.selectedTableState.label}'?`;
    modalRef.componentInstance.okButtonClass = 'btn-danger';

    if (this.parent.standardTableStates.findIndex(s => s.Id === this.parent.selectedTableState.id) > -1) {
      modalRef.componentInstance.body = "This report is currently set as a standard report.";
    }

    modalRef.result.then(res => {
      this.deleteTableState();
    }, () => {
    });
  }


  deleteTableState() {
    this.apiAccessService.deleteColumnState('api/AutoPrice', 'DeleteTableState', this.parent.selectedTableState.id, this.parent.pageName).subscribe((res) => {
      this.constantsService.toastSuccess('Deleted report')
      this.parent.ownTableStates = this.parent.ownTableStates.filter(x => x.Id !== this.parent.selectedTableState.id);
      this.resetTableState();
      this.initialise();
    })
  }

  resetTableState() {
    this.parent.selectedTableState = null;
    this.parent.loadedTableState = null;
    this.parent.gridApi?.setFilterModel(this.defaultFilterState);
    this.parent.gridColumnApi?.resetColumnState();
    if (this.parent.autoSize) {
      this.parent.gridApi?.sizeColumnsToFit();
    }
  }

  shareTableState() {
    const params = {
      tableStateId: this.parent.selectedTableState.id,
      isShared: this.parent.selectedTableState.isShared
    }

    this.apiAccessService.post('api/AutoPrice', 'ShareTableState', params).subscribe((res) => {
      const message: string = this.parent.selectedTableState.isShared ? 'Report shared' : 'Report no longer shared';
      this.getSharedTableStateLabels();
      this.constantsService.toastSuccess(message);
    })
  }

  setStandardTableState(tableStateIdsAndSortIndex: TableStateIdAndSortIndex[]) {
    const params: StandardTableStateParams = {
      tableStateIdsAndSortIndex: tableStateIdsAndSortIndex
    }

    this.apiAccessService.post('api/AutoPrice', 'SetStandardTableState', params).subscribe((res) => {
      this.getStandardTableStateLabels();
      this.getAvailableTableStateLabels();
      this.constantsService.toastSuccess('Updated standard reports');
    })
  }

  unsetStandardTableState(tableStateIds: number[]) {
    this.apiAccessService.post('api/AutoPrice', 'UnsetStandardTableState', tableStateIds).subscribe((res) => {
      this.getStandardTableStateLabels();
      this.getAvailableTableStateLabels();
      this.constantsService.toastSuccess('Updated standard reports');
    })
  }

  getTableContextMenuItems(): (string | MenuItemDef)[] {
    const result: (string | MenuItemDef)[] = [];
    const tableLayoutSubMenu: (string | MenuItemDef)[] = [];
    const myReportsSubMenu: (string | MenuItemDef)[] = [];
    const standardReportsSubMenu: (string | MenuItemDef)[] = [];
    const sharedReportsSubMenu: (string | MenuItemDef)[] = [];

    tableLayoutSubMenu.push({
      name: 'Overview report',
      action: () => {
        this.resetTableState()
      }
    })

    tableLayoutSubMenu.push('separator');

    // My reports sub menu items
    this.parent.ownTableStates.forEach(state => {
      myReportsSubMenu.push({
        name: state.StateLabel,
        action: () => {
          this.loadTableStateById(state.Id, false)
        }
      })
    })

    tableLayoutSubMenu.push({
      name: 'My reports',
      subMenu: this.parent.ownTableStates.length > 0 ? myReportsSubMenu : null
    })

    // Overview reports sub menu items
    this.parent.standardTableStates.forEach(state => {
      standardReportsSubMenu.push({
        name: `${state.StateLabel} created by ${state.CreatedBy}`,
        action: () => {
          this.loadTableStateById(state.Id, false)
        }
      })
    })

    tableLayoutSubMenu.push({
      name: 'Standard reports',
      subMenu: this.parent.standardTableStates.length > 0 ? standardReportsSubMenu : null
    })

    // Shared reports sub menu items
    this.parent.sharedTableStates.forEach(state => {
      sharedReportsSubMenu.push({
        name: `${state.StateLabel} shared by ${state.SharedBy}`,
        action: () => {
          this.loadTableStateById(state.Id, false)
        }
      })
    })

    tableLayoutSubMenu.push({
      name: 'Shared reports',
      subMenu: this.parent.sharedTableStates.length > 0 ? sharedReportsSubMenu : null
    })

    result.push('separator',
      {
        icon: '📂',
        name: 'Load report...',
        subMenu: tableLayoutSubMenu
      }
    );

    if (this.parent.selectedTableState) {
      result.push(
        {
          icon: '💾',
          name: `Save '${this.parent.selectedTableState.label}'`,
          action: () => {
            this.saveExistingTableState()
          }
        }
      )
    }
    ;

    result.push(
      {
        icon: '🖫',
        name: 'Save as new report...',
        action: () => {
          this.maybeSaveNewTableState()
        }
      }
    )

    if (this.parent.selectedTableState) {
      result.push(
        {
          icon: '🗑️',
          name: `Delete report '${this.parent.selectedTableState.label}'...`,
          cssClasses: ['redFont'],
          action: () => {
            this.maybeDeleteTableState()
          }
        }
      )
    }

    return result;
  }

  getTableStateParams() {
    const params = [
      { key: 'pageName', value: this.parent.pageName }
    ]

    if (this.parent.pageName === 'fleetOrderbook') {
      params.push({ key: 'isRenault', value: this.parent.isRenault ? 'true' : 'false' });
    }
    return params;
  }

  setColumnsForTypeahead() {
    this.allColumns = this.parent.gridColumnApi.getAllDisplayedColumns()?.map(x => x.getUserProvidedColDef());
    this.columnsForTypeahead = this.parent.gridColumnApi.getAllDisplayedColumns()?.map(x => x.getUserProvidedColDef()?.headerName);
    this.searchListUpdatedEmitter.emit(this.columnsForTypeahead);
  }

  openCustomiseColumnModal() {
    //console.log('col model is now:',this.parent.gridApi.getColumnDefs())
    const modal = this.modalService.open(CustomiseColumnsModalComponent, { size: 'lg' });
    const component: CustomiseColumnsModalComponent = modal.componentInstance;
    component.gridApi = this.parent.gridApi;
    const ordering: string[] = [
      'Site',
      'Vehicle',
      'Stock Information',
      'Advert Details',
      'Competitor Information',
      'Vehicle Metrics',
      'Costings',
      'Valuation',
      'Valuation - Average Spec',
      'Advertised Price',
      'Test Strategy',
      'Advert Performance',
      'Opt Outs',
      'Price Changes',
    ];

    if (this.constantsService.RetailerSites.some(x => x.UpdatePricesAutomatically)) {
      ordering.push('Price Changes - Manually Via Spark');
    }

    if (this.constantsService.environment.vehiclePricing_StockReport_showBcaColumns) {
      ordering.push('BCA Information');
    }

    ordering.push(
      `Today's Price Change`,
      'Future Valuation',
      'Analysis'
    )

    component.sectionOrdering = ordering
    component.originalColDefs = this.parent.originalColDefs;

    modal.result.then(res => {

      const allCols = this.parent.gridColumnApi.getAllGridColumns();
      const updatedCols = allCols.map(col => {
        const isVisible = !!res.find(x => x.colId === col.getColId());
        return { ...col.getColDef(), hide: !isVisible };
      });
      //console.log('updatedCols:',updatedCols)
      this.parent.gridApi.setColumnDefs(updatedCols);
      //console.log('col model after setting is:',this.parent.gridApi.getColumnDefs())

      const state = res.map((col, index) => {
        return {
          colId: col.colId,
          order: index
        };
      });

      this.parent.gridColumnApi.applyColumnState({
        state: state,
        applyOrder: true
      });


    }, () => {

    });
  }


}
