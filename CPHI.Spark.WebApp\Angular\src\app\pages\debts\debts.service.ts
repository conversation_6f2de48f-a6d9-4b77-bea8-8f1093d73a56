
import { Injectable } from '@angular/core';
import { TopBottomHighlightRule } from "src/app/model/TopBottomHighlightRule";
import { DebtsBonusesSummaryRow, DebtsDebtsSummaryRow, DebtsParams, DebtsSitesSummaryRow, DebtType } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Injectable({
  providedIn: 'root'
})

export class DebtsService {
    topBottomHighlights: TopBottomHighlightRule[]=[]
    debtTypes:DebtType[]
    constructor(
        public getData: GetDataMethodsService,
        public selections: SelectionsService,
        public constants: ConstantsService
    ) {

        let debtTypes: DebtType[] = [
            { description: 'Sites Overview', value: 'sites' },
            { description: 'Debts', value: 'debts' }
        ]

        if (this.constants.environment.debts_showBonusDebtType) {
            debtTypes.push({ description: 'Bonuses', value: 'bonuses' });
        }

        this.debtTypes = debtTypes;
     }




    getSitesSummary(ageAtMonthEnd: boolean, ageOnDueDate: boolean) {
        this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });

        let params: DebtsParams = {
            asAtMonthEnd: ageAtMonthEnd.toString(),
            ageOnDueDate: ageOnDueDate.toString()
        }

        this.getData.getDebtsSitesSummary(params).subscribe((res: DebtsSitesSummaryRow[]) => {
            this.selections.debts.siteSummaryRows = res;
        }, e => {
            console.error('Error retrieving sites summary rows: ' + JSON.stringify(e));
        }, () => {
            this.selections.debts.sitesDataChangedEmitter.emit(true);
            this.selections.triggerSpinner.emit({ show: false });
        })
    }

    getDebtsSummary(ageAtMonthEnd: boolean, ageOnDueDate: boolean, siteIds: number[]) {
        this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });

        let params: DebtsParams = {
            asAtMonthEnd: ageAtMonthEnd.toString(),
            ageOnDueDate: ageOnDueDate.toString(),
            siteIds: siteIds.toString()
        }

        this.getData.getDebtsDebtsDetailSummary(params).subscribe((res: DebtsDebtsSummaryRow[]) => {
            this.selections.debts.debts = res;
            this.selections.debts.debtsFiltered = res;
        }, e => {
            console.error('Error retrieving debts summary rows: ' + JSON.stringify(e));
        }, () => {
            this.selections.debts.debtsDataChangedEmitter.emit(true);
            this.selections.triggerSpinner.emit({ show: false });
        })
    }

    getBonusesSummary(ageAtMonthEnd: boolean, ageOnDueDate: boolean, siteIds: number[]) {
        this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });

        let params: DebtsParams = {
            asAtMonthEnd: ageAtMonthEnd.toString(),
            ageOnDueDate: ageOnDueDate.toString(),
            siteIds: siteIds.toString()
        }
        this.getData.getDebtsBonusDetailSummary(params).subscribe((res: DebtsBonusesSummaryRow[]) => {
            this.selections.debts.bonuses = res;
            this.selections.debts.bonusesFiltered = res;
        }, e => {
            console.error('Error retrieving bonuses summary rows: ' + JSON.stringify(e));
        }, () => {
            this.selections.debts.bonusesDataChangedEmitter.emit(true);
            this.selections.triggerSpinner.emit({ show: false });
        })
    }
}
