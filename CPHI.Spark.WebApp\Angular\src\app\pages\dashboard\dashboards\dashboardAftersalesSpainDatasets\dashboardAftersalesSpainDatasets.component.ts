import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AftersalesDatasetsParams } from 'src/app/model/afterSales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardPageNew } from '../../dashboard.component';
import { DashboardService } from '../../dashboard.service';
import { DashboardAftersalesSpainDatasetsService } from './dashboardAftersalesSpainDatasets.service';

@Component({
  selector: 'dashboardAftersalesSpainDatasets',
  templateUrl: './dashboardAftersalesSpainDatasets.component.html',
  styleUrls: ['./dashboardAftersalesSpainDatasets.component.scss']
})

export class DashboardAftersalesSpainDatasetsComponent implements OnInit {
  origins: string[];
  newDataSubscription: Subscription;
  unwantedColumns: string[];

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public dashboardService: DashboardService,
    public service: DashboardAftersalesSpainDatasetsService
  ) { }

  ngOnInit() {
    this.dashboardService.getSpainMonths();
    this.makeOriginsList();
    this.setUnwantedColumns();
    this.getData();

    this.newDataSubscription = this.dashboardService.getNewDataTrigger.subscribe(res => {
      this.getData(true);
    })
  }

  makeOriginsList() {
    this.origins = [
      'Operators', 'RepairOrders', 'WorkshopAppointment', 'WorkshopHours', 'WorkshopHoursPurchased','Ordenes','Revisiones'
    ]
  }

  setUnwantedColumns() {
    this.unwantedColumns = [
      'AccountCode', 'BrandCode', 'StatusDescription', 'AppointmentTime', 'IdPrimaryKey'
    ]
  }

  getData(refresh?: boolean) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    let parms: AftersalesDatasetsParams = {
      Dataset: this.service.chosenOrigin,
      Month: this.dashboardService.chosenMonthStart
    }

    this.getDataService.getAftersalesDatasets(parms).subscribe((res: any) => {
      this.service.tableRows = res;
      this.service.tableColumns = null;

      let tableColumns: any[] = [];
      let keys = Object.keys(res[0]);
      keys.forEach(element => {
        if (!this.unwantedColumns.find(x => x === element)) {
          tableColumns.push({ headerName: element.replace(/([A-Z])/g, ' $1').trim(), field: element, editable: false, filter: 'agTextColumnFilter' });
        }
      });

      this.service.tableColumns = tableColumns;

      if (refresh) {
        this.service.gridApi.setColumnDefs([]);
        this.service.gridApi.setRowData([]);
        
        setTimeout(() => {
          this.service.gridApi.setColumnDefs(this.service.tableColumns);
          this.service.gridApi.setRowData(this.service.tableRows);
        }, 100)
      }

      this.selections.triggerSpinner.next({ show: false });
    })
  }

  chooseOrigin(origin: string) {
    this.service.chosenOrigin = origin;
    this.getData(true);
  }

  choosePage(page: DashboardPageNew) {
    this.dashboardService.chosenPage = page;
    this.dashboardService.singleLineNav = false;
  }
}
