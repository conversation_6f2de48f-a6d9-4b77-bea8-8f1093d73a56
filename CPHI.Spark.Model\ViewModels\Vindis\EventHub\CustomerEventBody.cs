﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class CustomerEventBody
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? HomeTel { get; set; }
        public string? MobileTel { get; set; }
        public string? WorkTel { get; set; }
        public string? Email { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? IdentificationNumber { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string AddressLine3 { get; set; }
        public string Town { get; set; }
        public string County { get; set; }
        public string Postcode { get; set; }
        public string Country { get; set; }
        public string DmsId { get; set; }
        public IList<string>? Leads { get; set; }
        public IList<string>? Enquiries { get; set; }
        public EventDataProtectionModel? DataProtection { get; set; }
    }
}
