import { Component, EventEmitter, Input, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CellClickedEvent, GridApi } from 'ag-grid-community';
import { DealDetailsComponent } from "src/app/components/dealDetails/dealDetails.component";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from "../../../../cph.pipe";
import { SiteVM } from "../../../../model/main.model";
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from "../../../../services/constants.service";
import { ExcelExportService } from '../../../../services/excelExportService';
import { SelectionsService } from "../../../../services/selections.service";
import { DailyOrderDetailItem, DailyOrdersSiteDetail } from "./thisWeekOrders.model";
import { ThisWeekOrdersService } from "./thisWeekOrders.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";





@Component({
  selector: "thisWeekOrdersDetailedTable",


  template: `
    <div id="gridHolder">
      <button id="backButton" (click)="goBack()" class="btn btn-primary">
      <  
      </button>
    <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        class="ag-theme-balham"
        [gridOptions]="mainTableGridOptions"
        domLayout="autoHeight"
       
      >
      </ag-grid-angular>
    </div>

    
  `,



  styleUrls: ["./../../../../../styles/components/_agGrid.scss"],
  styles: [
    `


      ag-grid-angular.hidden{opacity:0}
      ag-grid-angular {
        width: 100%;
        margin: 0em auto;
        margin-top: 1em;
      }
      #gridHolder {
        position: relative;
        width: 90%;
    margin: 0em auto;
      }
    `,
  ],
})
export class ThisWeekOrdersDetailedTableComponent implements OnInit {
  @Input() public newDataForTableEmitter: EventEmitter<SiteVM>;

  columnDefs: any[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;
  showGrid: boolean;


  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public router: Router,
    public excel: ExcelExportService,
    public service: ThisWeekOrdersService,
    public gridHelpersService: AGGridMethodsService
  ) { }



  ngOnInit() {
    this.initParams();
    this.setColumnDefinitions();
    this.showGrid = true;
  }

  


  ngOnDestroy() {
  }

  initParams() {
  }


  goBack(){
    this.service.backToSites();
  }

  setColumnDefinitions() {
    // table definitions
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false, cellClass: "ag-right-aligned-cell",
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: false,
      },
      
      rowData: this.service.detailItems,
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
      getRowHeight: (params) => {
        let normalHeight = Math.min(23, Math.max(19, Math.round(23 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onCellClicked: (params) => {
        this.onCellClick(params)
      },
      onGridReady: (params) => this.onGridReady(params),
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: [
        { headerName: "", field: "SiteDescription", colId: "Description", width: 150, type: "label", },
        { headerName: "Original order date", field: "OrigOrderDate", colId: "OrigOrderDate", width: 100, type: "date", },
        { headerName: "Spark date", field: "SparkOrderOrCancelDate", colId: "SparkOrderOrCancelDate", width: 100, type: "date", },
        { headerName: "Customer",  field: "Customer", colId: "Customer", width: 100, type: "label", },
        { headerName: "Vehicle and Order type",  field: "VehicleOrderType", colId: "VehicleOrderType", width: 100, type: "label", },
        { headerName: "Profit",  field: "Profit", colId: "Profit", width: 150, type: "currency", },
      ]
    }
  }
  
  onCellClick(params: CellClickedEvent) {
    let item:DailyOrderDetailItem = params.data;
    this.showDeal(item.DealId);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.mainTableGridOptions.context = { thisComponent: this };

    this.gridApi.sizeColumnsToFit();
  }


  showDeal(dealId:number){


    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    setTimeout(() => {
      const modalRef = this.modalService.open(DealDetailsComponent);
      //I give to modal
      modalRef.componentInstance.givenDealId = dealId;
      modalRef.result.then((result) => { //I get back from modal
        if (result) {
          //thing
        }
      });
    }, 10)


  }





  refreshTable(): void {
    if (!this.gridApi) return

    let newSiteData: SiteVM[];
    this.gridApi.setRowData(newSiteData);
    //this.gridApi.setPinnedBottomRowData(this.selections.citNow.sites.filter(x => x.SiteId == 0))
  }


  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Spark orders', 1, 1);
  }
}
