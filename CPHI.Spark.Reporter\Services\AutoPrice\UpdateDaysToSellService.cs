﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Repository;
using log4net;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using Datadog.Trace;
using static CPHI.Spark.Model.ViewModels.AutoPricing.RetailCustomerLatestViewResponseResult;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class UpdateDaysToSellService
   {
      private readonly HttpClient httpClient;

      public UpdateDaysToSellService(HttpClient httpClient)
      {
         this.httpClient = httpClient;
      }

      public async Task UpdateDaysToSell(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0)
         { return; }
         try
         {
            logger.Info("----------------------------------------------------------");
            logger.Info("UpdateDaysToSell");

            using (var parentScope = Tracer.Instance.StartActive("UpdateDaysToSell"))
            {

               foreach (Model.DealerGroupName dealerGroup in dealerGroups)
               {

                  using (var childScope = Tracer.Instance.StartActive($"Begin dealergroup {dealerGroup}"))
                  {
                     childScope.Span.SetTag("DealerGroup", dealerGroup.ToString());

                     logger.Info($"UpdateDaysToSell: {dealerGroup}");
                     var logMessage = LoggingService.InitLogMessage();
                     try
                     {

                        RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                        List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

                        if (retailers.Count > 0)
                        {
                           try
                           {
                              await UpdateForThisDealerGroup(logger, dealerGroup);
                           }
                           catch (Exception ex)
                           {
                              logger.Error(ex);
                              await EmailerService.SendMailOnError(dealerGroup, "UpdateDaysToSell - Error", ex);
                              LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                           }
                        }
                        else
                        {
                           logger.Info($"UpdateDaysToSell: {dealerGroup}: No retailers");
                        }
                     }
                     catch (Exception ex)
                     {
                        await EmailerService.LogException(ex, logger, "UpdateDaysToSell");
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                     finally
                     {
                        await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "UpdateDaysToSell");
                     }
                  }

               }

               logger.Info("Completed UpdateDaysToSell");
               logger.Info("----------------------------------------------------------");
            }
         }

         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }

      private async Task UpdateForThisDealerGroup(ILog logger, Model.DealerGroupName dealerGroup)
      {
         string connString = ConfigService.GetConnectionString(dealerGroup);
         DateTime runDate = DateTime.Now; //new DateTime(2024, 1, 10, 7, 0, 0);  //


         List<int> siteIds = new List<int>();
         List<int> retailerSiteIds = new List<int>();

         await DataDogService.RunWithSpanAsync("Fetch sites", async () =>
         {
            {
               // get data
               RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(connString);
               List<RetailerSite> retailerSitesThisDG = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
               siteIds = retailerSitesThisDG.Select(x => x.Site_Id).Distinct().ToList();
               retailerSiteIds = retailerSitesThisDG.Select(x => x.Id).ToList();

            }
         });


         var atConfig = new AutoTraderConfig()
         {
            AutotraderApiKey = ConfigService.AutotraderApiKey,
            AutotraderApiSecret = ConfigService.AutotraderApiSecret,
            AutotraderBaseURL = ConfigService.AutotraderBaseURL
         };
         DaysToSellService daysToSellService = new DaysToSellService(connString, atConfig, HttpClientFactoryService.HttpClientFactory);
         VehicleAdvertSnapshotsDataAccess vehicleAdvertSnapshotsDataAccess = new VehicleAdvertSnapshotsDataAccess(connString);
         List<VehicleAdvertWithRating> adverts = new List<VehicleAdvertWithRating>();

         await  DataDogService.RunWithSpanAsync("Fetch adverts", async () =>
         {
            //load up all the snapshots for today
            VehicleAdvertsService vehicleAdvertsService = new VehicleAdvertsService(connString);

            GetVehicleAdvertsWithRatingsParams parms = new GetVehicleAdvertsWithRatingsParams()
            {
               Reg = null,
               Vin = null,
               RetailerSiteIds = string.Join(',', retailerSiteIds),
               EffectiveDate = runDate.Date,
               UserEligibleSites = string.Join(',', siteIds),
               IncludeNewVehicles = true,
               IncludeUnPublishedAdverts = true
            };

            var lifecycles = AutoPriceHelperService.GetAllLifecycleStatusesNoSoldOrWastebin();

            adverts = (await vehicleAdvertsService.FetchAndFilterVehicleAdvertsFromMainDbTables(parms, dealerGroup, siteIds, retailerSiteIds, lifecycles)).ToList();
         });

         logger.Info($"Working out days to sell for {adverts.Count()} adverts...");

         List<DaysToSellRequest> results = new List<DaysToSellRequest>();

         await  DataDogService.RunWithSpanAsync("Fetch days to sell", async () =>
         {
            List<DaysToSellRequest> daysToSellRequest = adverts.ConvertAll(x => new DaysToSellRequest(x));
            results = await daysToSellService.FindDaysToSell(daysToSellRequest, logger);
         });

         await  DataDogService.RunWithSpanAsync("Saving results", async () =>
         {
            //logger.Info("Saving updated adverts...");
            await vehicleAdvertSnapshotsDataAccess.SaveUpdatedDaysToSell(results);
            logger.Info("Update DTS for strategy price: Saved updated adverts");
         });


      }








      //private static List<VehicleAdvert> ExtractIncomingVehiclesList(List<AutoTraderVehicleListing> adverts,
      //                                                               Dictionary<string, LatestChassisAndStockNumber> chassisAndStocknumberDict,
      //                                                               Dictionary<int, RetailerSite> sitesDictionary,
      //                                                               DateTime snapshotDate)
      //{
      //   List<VehicleAdvert> incomingVehicles = new List<VehicleAdvert>();
      //   foreach (var vehicle in adverts)
      //   {
      //      string stockNumberFull = "Unknown";
      //      LatestChassisAndStockNumber stockNumberMatch = null;
      //      bool didFindInDictionary = vehicle.vehicle?.vin != null && chassisAndStocknumberDict.TryGetValue(vehicle.vehicle.vin, out stockNumberMatch);
      //      if (didFindInDictionary)
      //      { stockNumberFull = stockNumberMatch?.StockNumberFull; }

      //      int retailerId = int.Parse(vehicle.advertiser.advertiserId);
      //      incomingVehicles.Add(new VehicleAdvert(vehicle, stockNumberFull, sitesDictionary[retailerId].Id, snapshotDate));
      //   }

      //   return incomingVehicles;
      //}




   }
}
