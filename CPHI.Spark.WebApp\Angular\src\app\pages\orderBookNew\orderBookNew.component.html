<nav class="navbar" #navbar>

  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        <span *ngIf="service.constants.environment.languageSelection"> {{ service.constants.translatedText.Orderbook_TitleSpain }}</span>
        <span *ngIf="!service.constants.environment.languageSelection"> Order book</span>

        <sourceDataUpdate [chosenDate]="service.constants.LastUpdatedDates.Deals"></sourceDataUpdate>


      </div>
    </h4>

    <vehicleTypePickerSpain
      *ngIf="service.constants.environment.vehicleTypePicker_showVehicleTypePickerSpain"
      [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"
      (updateVehicleTypes)="onUpdateVehicleTypes($event)"
    >
    </vehicleTypePickerSpain>

    <div class="buttonGroup topDropdownButtons">

      <!-- Site selector -->
      <sitePickerRRG 
        *ngIf="service.constants.sitesActive && (service.constants.environment.dealDone_showRRGSitePicker)" 
        [allSites]="service.constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites" id="sitePicker"
        [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePickerRRG>

      <sitePicker 
      *ngIf="service.constants.sitesActive && (service.constants.environment.dealDone_showVindisSitePicker)" id="sitePicker"
      [allSites]="service.constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites"
      [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePicker>

      <!-- VehicleType selector -->
      <vehicleTypePicker *ngIf="!service.constants.environment.vehicleTypePicker_showVehicleTypePickerSpain"
      [vehicleTypeTypesFromParent]="service?.vehicleTypeTypes" [buttonClass]="'buttonGroupCenter'"
      (updateVehicleTypes)="onUpdateVehicleTypes($event)"></vehicleTypePicker>

      <!-- OrderType selector -->
      <orderTypePicker *ngIf="service.constants.environment.orderTypePicker" [orderTypeTypesFromParent]="service.orderTypeTypes"
        [buttonClass]="'buttonGroupCenter'" (updateOrderTypes)="onUpdateOrderTypes($event)"></orderTypePicker>

      <!-- Franchise selector -->
      <franchisePicker [franchisesFromParent]="service?.franchises" [buttonClass]="'buttonGroupCenter'"
        (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>

      <!-- Late Costs -->
      <div *ngIf="service.constants.environment.lateCostPicker" ngbDropdown dropright class="d-inline-block">
        <button class="buttonGroupCenter btn btn-primary"
          ngbDropdownToggle>{{service?.lateCostOption?.name}}</button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1" *ngIf="service.constants.environment.lateCostPicker">
          <button *ngFor="let lateCostOption of service.constants.lateCostOptions"
            (click)="selectLateCostOption(lateCostOption)" ngbDropdownToggle class="manualToggleCloseItem"
            ngbDropdownItem>{{lateCostOption.name}}</button>

        </div>
      </div>

      <!-- Orders options -->
      <div ngbDropdown dropright class="d-inline-block" *ngIf="this.service.constants.environment.orderBook_showOrderOptions">
        <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>{{service?.orderOption?.name}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <button *ngFor="let orderOption of service.constants.orderOptions" (click)="selectOrderOption(orderOption)"
            ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>{{orderOption.name}}</button>

        </div>
      </div>


      <!-- Sales Exec -->
      <div ngbDropdown dropright class="d-inline-block">

        <button (click)="generatePeopleSummary(true)" class="buttonGroupRight btn btn-primary" ngbDropdownToggle>
          <span *ngIf="!service.salesExecName"><i class="fas fa-user"></i></span>
          <span *ngIf="service.salesExecName">
            <profilePicImage [personId]="service.salesExecId" [size]="profilePicSize"></profilePicImage>
            {{service.salesExecName}}
          </span>
        </button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <button *ngFor="let salesExecSummaryItem of service?.salesExecSummary" ngbDropdownToggle
            (click)="selectSalesExec(salesExecSummaryItem?.name, salesExecSummaryItem?.salesmanId)" ngbDropdownItem
            class="personDropdownButton manualToggleCloseItem">
            <div class="spaceBetween">

              <profilePicImage [size]="profilePicSize" [personId]="salesExecSummaryItem?.salesmanId"></profilePicImage>
              <div class="name">{{salesExecSummaryItem?.name}} </div>
              <div class="dealsCount">{{salesExecSummaryItem?.units|cph:'number':0}} </div>
              <div class="productsPU">{{salesExecSummaryItem?.productsPU|cph:'number':1}} </div>
              <div class="profitPU">{{salesExecSummaryItem?.profitPU|cph:'currency':0}} </div>

            </div>
          </button>
          <button ngbDropdownItem *ngIf="service.salesExecName" class="personDropdownButton manualToggleCloseItem"
            (click)="selectSalesExec(null,0)" ngbDropdownToggle>
            <div class="spaceBetween">
              <div><i class="fas fa-user-slash"></i></div>
              <div>{{ service.constants.translatedText.Clear }}</div>
            </div>
          </button>

        </div>
      </div>


      <!-- Manager -->
      <div *ngIf="service.constants.environment.orderBook_showManagerSelector" ngbDropdown dropright class="d-inline-block">
        <button (click)="generatePeopleSummary(false)" class="buttonGroupRight btn btn-primary" ngbDropdownToggle>
          <span *ngIf="!service.salesManagerName"><i class="fas fa-user-tie"></i></span>
          <span *ngIf="service.salesManagerName">
            <profilePicImage [personId]="service.salesManagerId" [size]="profilePicSize"></profilePicImage>
            {{service.salesManagerName}}
          </span>
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <button *ngFor="let salesManagerSummaryItem of service?.salesManagerSummary" ngbDropdownToggle
            (click)="selectManagerExec(salesManagerSummaryItem?.name, salesManagerSummaryItem?.salesmanId)" ngbDropdownItem
            class="personDropdownButton manualToggleCloseItem">
            <div class="spaceBetween">

              <profilePicImage [size]="profilePicSize" [personId]="salesManagerSummaryItem?.salesmanId"></profilePicImage>
              <div class="name">{{salesManagerSummaryItem?.name}} </div>
              <div class="dealsCount">{{salesManagerSummaryItem?.units|cph:'number':0}} </div>
              <div class="productsPU">{{salesManagerSummaryItem?.productsPU|cph:'number':1}} </div>
              <div class="profitPU">{{salesManagerSummaryItem?.profitPU|cph:'currency':0}} </div>

            </div>
          </button>
          <button ngbDropdownItem *ngIf="service.salesManagerName" class="personDropdownButton manualToggleCloseItem"
            (click)="selectManagerExec(null,0)" ngbDropdownToggle>
            <div class="spaceBetween">
              <div><i class="fas fa-user-slash"></i></div>
              <div>{{ service.constants.translatedText.Clear }}</div>
            </div>
          </button>

        </div>
      </div>

    </div>

    <!-- Quick Search -->
    <div ngbDropdown dropright class="d-inline-block">
      <button class="btn btn-primary" ngbDropdownToggle>
        <i class="fas fa-search-plus"></i>
      </button>
      <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <button *ngFor="let quickSearch of quickSearches" (click)="updateFilterWith(quickSearch)" ngbDropdownToggle
          class="manualToggleCloseItem" ngbDropdownItem>{{quickSearch.description}}
        </button>
        <button *ngIf="!!service.specialFiltersMenuItemChosen" (click)="clearFilter()" ngbDropdownToggle class="manualToggleCloseItem"
          ngbDropdownItem>--{{service.constants.translatedText.Clear}}--
        </button>

      </div>

    </div>



    <!-- Search box -->
    <div id="searchBox" class="visibleAboveSm">
      <!-- searchBox icon with popover -->
      <i class="searchBoxIcon fas fa-search"></i>
      <form>
        <input placeholder="{{ service.constants.translatedText.Search }}" class="form-control ml-2" type="text"
          [formControl]="service.searchTerm" />
        <div *ngIf="!!service.searchTerm?.value" (click)="clearSearchTerm()" id="searchBarClearButton">
          <i class="fas fa-times-circle"></i>
        </div>
      </form>
    </div>

    


    <!-- Delivered options -->
    <div class="buttonGroup" *ngIf="service.constants.environment.orderBook_showDeliveryOptionButtons">
      <button *ngFor="let deliveryOption of deliveryOptions" [ngClass]="{'active':deliveryOption===service.deliveryOption}" 
      class="btn btn-primary" (click)="setDeliveryFilterTo(deliveryOption)">
        <span *ngIf="deliveryOption==='All'">{{ service.constants.translatedText.All }}</span>
        <span *ngIf="deliveryOption==='Delivered'">{{ service.constants.translatedText.Delivered }}</span>
        <span *ngIf="deliveryOption==='Undelivered'">{{ service.constants.translatedText.Undelivered }}</span>
      </button>
    </div>

 <!-- DealCount -->
  <!-- Detailed excel -->
  <button class="btn btn-primary" id="excelButton" (click)="getDetailedExcel()">
    <img [src]="service.constants.provideExcelLogo()">
  
 <span id="dealCounter"  *ngIf="service">
  {{service.constants.pluralise(service.summaryTable?.totals.units, this.service.constants.translatedText.Unit,
  this.service.constants.translatedText.Units)}}
 </span>
</button>


  <!-- New deal modal -->
  <button *ngIf="service.constants.environment.orderBook_showNewDealButton" class="btn btn-primary" (click)="launchDealInputModal()">New Deal</button>
  </nav>


</nav>



<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constants.environment.customer">
  <div class="content-new" >

    <div class="contentInner orderBookContent">

      <div id="topSection" #topSection class="stackOnSmallScreens">

        <div class="spaceBetween" *ngIf="service">

          <div id="dateSelectionTables">
            <table *ngIf="!service.constants.environment.orderBook_hideOrderDateSelection" id="orderDate" class="dateTable">
              <thead>
                <tr>
                  <th colspan="5" class="orderApprovedDate">
                    <div class="dateAreaHeader">
                      {{ service.constants.environment.orderBook_ordersDescription }}

                      <span *ngIf="service.orderDate?.startDate && service.orderDate?.endDate">
                        {{service.orderDate?.startDate|cph:'date':0}} -
                        {{service.constants.addTimezoneOffset(service.orderDate?.endDate)|cph:'date':0}}
                      </span>

                    </div>
                  </th>
                </tr>
              </thead>

              <!--############################# Order Date ############################# -->
              <tbody>
                <tr>
                  <td>
                    <!--############################# Anytime ############################# -->
                    <button class="btn btn-primary anyTimeButton"
                      [disabled]="service.accountingDate?.timePeriod ===  timePeriod.Anytime"
                      (click)="selectOrderAnytime()"
                      [ngClass]="{'active':service.orderDate?.timePeriod === timePeriod.Anytime}">{{
                      service.constants.translatedText.Anytime }}</button>

                  </td>
                  <td>
                    <!--############################# Month ############################# -->

                    <div class="buttonGroup" [ngClass]="{'active':service.orderDate?.timePeriod === timePeriod.Month}">

                      <!-- previousMonth -->
                      <button class="btn btn-primary" (click)="changeOrderMonth(-1)"><i
                          class="fas fa-caret-left"></i></button>

                      <!-- direct select month -->
                      <button class="btn btn-primary directSelect"
                        *ngIf="service.orderDate?.timePeriod !== timePeriod.Month"
                        (click)="selectOrderMonth(service.orderDate?.lastChosenMonthStart)">
                        {{service.orderDate?.lastChosenMonthStart|cph:'month':0}}
                      </button>

                      <!-- dropdownMonth -->
                      <div ngbDropdown *ngIf="service.orderDate?.timePeriod === timePeriod.Month" class="d-inline-block"
                        [autoClose]="true">
                        
                        <button (click)="makeMonths()" class="btn btn-primary centreButton" ngbDropdownToggle>
                          {{service.orderDate?.lastChosenMonthStart|cph:'month':0}}</button>
                        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                          <!-- the ngFor buttons -->
                          <button *ngFor="let month of months" (click)="selectOrderMonth(month.startDate)"
                            ngbDropdownItem>{{month.startDate|cph:'month':0}}</button>

                        </div>

                      </div>
                      <!-- nextMonth -->
                      <button class="btn btn-primary" (click)="changeOrderMonth(1)"
                        [ngClass]="{'active':service.orderDate?.timePeriod === timePeriod.Month}"><i
                          class="fas fa-caret-right"></i></button>
                    </div>
                  </td>
                  <td>
                    <!--############################# Week ############################# -->
                    <div class="buttonGroup" [ngClass]="{'active':service.orderDate?.timePeriod === timePeriod.Week}">
                      <!-- previousWeek -->
                      <button class="btn btn-primary" (click)="changeOrderWeek(-1)"><i
                          class="fas fa-caret-left"></i></button>
                      <!-- direct select week -->
                      <button class="btn btn-primary directSelect weekButton"
                        *ngIf="service.orderDate?.timePeriod !== timePeriod.Week"
                        (click)="selectOrderWeek(service.orderDate?.lastChosenWeekStart)">
                        {{service.orderDate?.lastChosenWeekStart|cph:'week':0}}
                      </button>
                      <!-- dropdownWeek -->
                      <div ngbDropdown *ngIf="service.orderDate?.timePeriod === timePeriod.Week" class="d-inline-block"
                        [autoClose]="true">
                        <button (click)="makeWeeks()" class="btn btn-primary centreButton weekButton" ngbDropdownToggle>
                          {{service.orderDate?.lastChosenWeekStart|cph:'week':0}}
                        </button>
                        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                          <!-- the ngFor buttons -->
                          <button *ngFor="let week of weeks" (click)="selectOrderWeek(week.startDate)"
                            ngbDropdownItem>{{week.startDate|cph:'week':0}}</button>

                        </div>
                      </div>
                      <!-- nextWeek -->
                      <button class="btn btn-primary" (click)="changeOrderWeek(1)"
                        [ngClass]="{'active':service.orderDate?.timePeriod === timePeriod.Week}"><i
                          class="fas fa-caret-right"></i></button>
                    </div>
                  </td>
                  <td>
                    <!--############################# Day ############################# -->
                    <div class="buttonGroup" [ngClass]="{'active':service.orderDate?.timePeriod === timePeriod.Day}">
                      <!-- previousDay -->
                      <button class="btn btn-primary" (click)="changeOrderDay(-1)"><i
                          class="fas fa-caret-left"></i></button>
                      <!-- direct select day -->
                      <button class="btn btn-primary directSelect dayButton"
                        *ngIf="service.orderDate?.timePeriod !== timePeriod.Day"
                        (click)="selectOrderDay(service.orderDate?.lastChosenDay)">
                        {{service.orderDate?.lastChosenDay|cph:'dayNew':0}}
                      </button>
                      <!-- dropdownDay -->
                      <div ngbDropdown *ngIf="service.orderDate?.timePeriod === timePeriod.Day" class="d-inline-block"
                        [autoClose]="true">
                        <button (click)="makeDays()" class="btn btn-primary centreButton dayButton" ngbDropdownToggle>
                          {{service.orderDate?.lastChosenDay|cph:'dayNew':0}}
                        </button>
                        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                          <!-- the ngFor buttons -->
                          <button *ngFor="let day of days" (click)="selectOrderDay(day.startDate)"
                            ngbDropdownItem>{{day.startDate|cph:'dayNew':0}}</button>

                        </div>
                      </div>
                      <!-- nextDay -->
                      <button class="btn btn-primary" (click)="changeOrderDay(1)"
                        [ngClass]="{'active':service.orderDate?.timePeriod === timePeriod.Day}"><i
                          class="fas fa-caret-right"></i></button>
                    </div>
                  </td>
                  <td>
                    <!--############################# Custom ############################# -->
                    <button class="btn btn-primary" (click)="openDatePickerModal(true)"
                      [ngClass]="{'active':service.orderDate?.timePeriod === timePeriod.Custom}">
                      {{service.constants.translatedText.Custom}}
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>

            <!--############################# Accounting Date ############################# -->
            <table id="deliveryDate" class="dateTable">
              <thead>
                <tr>
                  <th colspan="5">
                    <div id="dateHeaderAndButtons">
                      <div>{{ service.constants.translatedText.With + ' '}}</div>
                      <div class="buttonGroup">
                        <!-- Delivery -->
                        <button class="btn btn-primary" [ngClass]="{'active':highlightIfDateTypeIs('Delivery')}"
                          (click)="selectAccountingDateType('Delivery')">{{service.constants.translatedText.DeliveryDate}}</button>
                        <!-- Invoice -->
                        <button class="btn btn-primary" [ngClass]="{'active':highlightIfDateTypeIs('Invoice')}"
                          (click)="selectAccountingDateType('Invoice')">{{service.constants.translatedText.InvoiceDate}}</button>
                        <!-- Accounting -->
                        <button *ngIf="service.constants.environment.orderBook_showAccountingDateButton" class="btn btn-primary"
                          [ngClass]="{'active':highlightIfDateTypeIs('Accounting')}"
                          (click)="selectAccountingDateType('Accounting')">{{service.constants.translatedText.DealDetails_AccountingDate}}</button>
                      </div>

                      <div>
                        {{ service.constants.translatedText.Between }}
                        {{service.accountingDate?.startDate|cph:'dateShortYear':0}} and
                        {{service.constants.addTimezoneOffset(service.accountingDate?.endDate)|cph:'dateShortYear':0}}
                      </div>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <!--############################# Anytime  ############################# -->
                    <button class="btn btn-primary anyTimeButton"
                      [disabled]="service.orderDate?.timePeriod === timePeriod.Anytime" (click)="selectAccountingAnytime()"
                      [ngClass]="{'active':service.accountingDate?.timePeriod === timePeriod.Anytime}">
                      {{ service.constants.translatedText.Anytime }}</button>
                  </td>
                  <td>
                    <!--############################# Month  ############################# -->
                    <div class="buttonGroup"
                      [ngClass]="{'active':service.accountingDate?.timePeriod === timePeriod.Month}">
                      <!-- previousMonth -->
                      <button class="btn btn-primary" 
                        [disabled]="service.accountingDate?.lastChosenMonthName === this.service.constants.translatedText.FinanceAddons_YearToDate || service.accountingDate?.lastChosenMonthName === this.service.constants.translatedText.FinanceAddons_FullPriorYear"
                        (click)="changeAccountingMonth(-1)">
                        <i class="fas fa-caret-left"></i></button>

                      <!-- direct select month -->
                      <button class="btn btn-primary directSelect"
                        *ngIf="service.accountingDate?.timePeriod !== timePeriod.Month"
                        (click)="selectAccountingMonth(service.accountingDate?.lastChosenMonthStart,null)">
                        {{service.accountingDate?.lastChosenMonthName}}</button>

                      <!-- dropdownMonth -->
                      <div ngbDropdown *ngIf="service.accountingDate?.timePeriod === timePeriod.Month"
                        class="d-inline-block" [autoClose]="true">

                        <button (click)="makeMonths()" class="btn btn-primary centreButton"
                          ngbDropdownToggle>{{service.accountingDate?.lastChosenMonthName}}</button>
                          
                        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                          <!-- the ngFor buttons -->
                          <button *ngFor="let month of months" (click)="selectAccountingMonth(month.startDate, month.name)"
                            ngbDropdownItem>{{month.name}}</button>

                        </div>
                      </div>
                      
                      <!-- nextMonth -->
                      <button class="btn btn-primary" (click)="changeAccountingMonth(1)" 
                        [disabled]="service.accountingDate?.lastChosenMonthName === this.service.constants.translatedText.FinanceAddons_YearToDate || service.accountingDate?.lastChosenMonthName === this.service.constants.translatedText.FinanceAddons_FullPriorYear"
                        [ngClass]="{'active':service.accountingDate?.timePeriod === timePeriod.Month}"><i
                          class="fas fa-caret-right"></i></button>
                    </div>

                  </td>

                  <td>
                    <!--############################# Day  ############################# -->
                    <div class="buttonGroup" [ngClass]="{'active':service.accountingDate?.timePeriod === timePeriod.Day}">
                      <!-- previousDay -->
                      <button class="btn btn-primary" (click)="changeAccountingDay(-1)"><i
                          class="fas fa-caret-left"></i></button>
                      <!-- direct select day -->
                      <button class="btn btn-primary directSelect"
                        *ngIf="service.accountingDate?.timePeriod !== timePeriod.Day"
                        (click)="selectAccountingDay(service.accountingDate?.lastChosenDay)">
                        {{service.accountingDate?.lastChosenDay|cph:'dayNew':0}}</button>
                      <!-- dropdownDay -->
                      <div ngbDropdown *ngIf="service.accountingDate?.timePeriod === timePeriod.Day" class="d-inline-block"
                        [autoClose]="true">
                        <button (click)="makeDays()" class="btn btn-primary centreButton" ngbDropdownToggle>
                          {{service.accountingDate?.lastChosenDay|cph:'dayNew':0}}
                        </button>
                        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                          <!-- the ngFor buttons -->
                          <button *ngFor="let day of days" (click)="selectAccountingDay(day.startDate)"
                            ngbDropdownItem>{{day.startDate|cph:'dayNew':0}}</button>

                        </div>
                      </div>
                      <!-- nextDay -->
                      <button class="btn btn-primary" (click)="changeAccountingDay(1)"><i
                          class="fas fa-caret-right"></i></button>
                    </div>
                  </td>
                  <td>
                    <!--############################# Custom  ############################# -->
                    <button class="btn btn-primary" (click)="openDatePickerModal(false)"
                      [ngClass]="{'active':service.accountingDate?.timePeriod === timePeriod.Custom}">
                      {{service.constants.translatedText.Custom}}
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div id="resultsTableHolder">
            <table id="resultsTable">
              <thead>
                <tr>
                  <th class="visibleAboveMd"></th>
                  <th *ngIf="service.constants.environment.orderBook_showMetalSummary">{{ service.constants.translatedText.Metal }}
                  </th>
                  <th *ngIf="service.constants.environment.orderBook_showOtherSummary">{{ service.constants.translatedText.Other }}
                  </th>
                  <th *ngIf="service.constants.environment.orderBook_showFinanceSummary">{{
                    service.constants.translatedText.Finance }}</th>
                  <th *ngIf="service.constants.environment.orderBook_showInsuranceSummary">{{
                    service.constants.translatedText.AddOns }}</th>
                  <th>{{ service.constants.translatedText.Total }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="visibleAboveMd">{{service.constants.translatedText.PerUnit}}</td>
                  <td *ngIf="service.constants.environment.orderBook_showMetalSummary">
                    {{service.summaryTable?.perUnits.metalProfit|cph:'currency':0}}</td>
                  <td *ngIf="service.constants.environment.orderBook_showOtherSummary">
                    {{service.summaryTable?.perUnits.otherProfit|cph:'currency':0}}</td>
                  <td *ngIf="service.constants.environment.orderBook_showFinanceSummary">
                    {{service.summaryTable?.perUnits.financeProfit|cph:'currency':0}}</td>
                  <td *ngIf="service.constants.environment.orderBook_showInsuranceSummary">
                    {{service.summaryTable?.perUnits.insuranceProfit|cph:'currency':0}}</td>
                  <td>{{service.summaryTable?.perUnits.totalNLProfit|cph:'currency':0}}</td>
                </tr>
                <tr>
                  <td class="visibleAboveMd">{{service.constants.translatedText.Total}}</td>
                  <td *ngIf="service.constants.environment.orderBook_showMetalSummary">
                    {{service.summaryTable?.totals.metalProfit|cph:'currency':0}}</td>
                  <td *ngIf="service.constants.environment.orderBook_showMetalSummary">
                    {{service.summaryTable?.totals.otherProfit|cph:'currency':0}}</td>
                  <td *ngIf="service.constants.environment.orderBook_showMetalSummary">
                    {{service.summaryTable?.totals.financeProfit|cph:'currency':0}}</td>
                  <td *ngIf="service.constants.environment.orderBook_showInsuranceSummary">
                    {{service.summaryTable?.totals.insuranceProfit|cph:'currency':0}}</td>
                  <td>{{service.summaryTable?.totals.totalNLProfit|cph:'currency':0}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>


      
        <!-- <div id="excelExport" (click)="excelExportService.excelExport()">
          <img [src]="service.constants.provideExcelLogo()">
        </div> -->

        <orderBookNewTable *ngIf="service.rows"></orderBookNewTable>







    </div>

  </div>
</div>