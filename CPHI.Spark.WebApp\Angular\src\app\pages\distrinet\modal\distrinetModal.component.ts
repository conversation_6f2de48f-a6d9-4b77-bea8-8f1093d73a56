import { OnInit, ViewChild, ElementRef, Input, Component } from "@angular/core";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { DistrinetModal } from "../distrinet.model";

@Component({
    selector: 'distrinetModal',
    templateUrl: './distrinetModal.component.html',
    styleUrls: ['./distrinetModal.component.scss']
})

export class DistrinetModalComponent implements OnInit {
    @ViewChild('distrinetModal', { static: true }) distrinetModal: ElementRef;
    @Input() public data: DistrinetModal;

    constructor(
        public selections: SelectionsService,
        public constants: ConstantsService,
        public modalService: NgbModal,
        public activeModal: NgbActiveModal
    ) { }

    ngOnInit() {
        this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
        this.modalService.open(this.distrinetModal, { size: 'lg' }).result.then((result) =>
        {}, (reason) => {
            this.activeModal.close();
        });
        
        this.selections.triggerSpinner.emit({ show: false });
    }
}