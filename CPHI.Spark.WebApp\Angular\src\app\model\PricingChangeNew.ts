export interface PricingChangeNew {
    RetailerName: string;
    RetailerSiteId: number;
    VehicleReg: string;
    Make: string;
    Derivative: string;
    WebsiteStockIdentifier: string;
    WebSiteSearchIdentifier: string;
    DaysListed: number;
    WasPrice: number;
    SpecificColour: string;
    DaysInStock: number;
    FirstRegisteredDate: Date | string | null;
    Owners: string;
    AgeAndOwners: string;
    RetailerStockType: string;

    PriceIndicatorRatingAtCurrentSelling: string;
    DaysToSellAtCurrentSelling: number;
    ValuationMktAvRetail: number;
    ValuationAdjustedRetail:number;
    RetailRating: number;
    NewPrice: number;
    NewDaysToSell: number;
    DaysToSellChange:number;
    IsOptedOutOnDay: boolean;

    DateConfirmed: Date | string | null;

    ChangeValueUp:number;
    ChangePercentUp:number;
    ChangeValueDown:number;
    ChangePercentDown:number;


    TotalChangeValue:number;
    TotalChangePercent:number;
    Status:string;
    AdvertId:number;

    StrategyPrice: number;
    WasPriceVsStrategyBanding: string;

    RetailerAdminFee:number;
    NewPriceExclAdminFee: number;

    LifecycleStatus: string;

    net?: number;
    IsKeyChange?: boolean;
}

