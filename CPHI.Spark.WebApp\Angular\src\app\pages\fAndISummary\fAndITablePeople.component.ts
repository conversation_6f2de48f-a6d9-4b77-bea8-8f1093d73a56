//core angular
import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { SiteVM, FAndISalesExecRow, OrderbookTimePeriod } from 'src/app/model/main.model';
import { localeEs } from 'src/environments/locale.es.js';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
import { FandIPerformanceSummary } from '../../model/sales.model';
import { AutotraderService } from '../../services/autotrader.service';
//model and cell renderers
//services
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';

//Angular things, non-standard
import { DateComponent } from '../../_cellRenderers/date.component';
import { LabelForPeopleComponent } from '../../_cellRenderers/labelForPeople.component';
import { OrderBookService } from '../orderBook/orderBook.service';
import { FAndISummaryService } from './fAndISummary.service';
import { ColDef, ColGroupDef, DomLayoutType, GridApi, GridOptions, IRowModel } from 'ag-grid-community';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';


type ColumnDef = ColDef | ColGroupDef;

@Component({
  selector: 'fAndITablePeople',
  template:    `
    <div id='gridHolder'>
    <div  id="excelExport" (click)="excelExport()">
    <img [src]="constants.provideExcelLogo()">
  </div>

  <!-- [domLayout]="domLayout" -->
    <ag-grid-angular  class="ag-theme-balham" [gridOptions]="mainTableGridOptions"
      [pinnedBottomRowData]="totalRow"
      [rowData]="rowData"   [frameworkComponents]="frameworkComponents"  
      (gridReady)="onGridReady($event)" [getRowNodeId]="getRowNodeId" [animateRows]="false"
      > 
      
    </ag-grid-angular>
    </div>
    `
  ,
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
  styles: [
    `

    ag-grid-angular {
      max-width: 1927px;
    }
  `
  ]
})



export class FandITablePeopleComponent implements OnInit {

  @Input() managers?: boolean;
  @Input() public rowData: Array<FAndISalesExecRow>;
  @Input() public totalRow: Array<FAndISalesExecRow>;
  //@Input() public isSitesTable: boolean;
  //@Output() clickedSite = new EventEmitter<FandIPerformanceSummary>();
  @Output() clickedPeople = new EventEmitter<SiteVM>();


  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if(this.gridApi){
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  //main declarations
  //for agGrid
  showGrid = false;
  public gridApi:GridApi;
  public importGridApi;
  public gridColumnApi;
  public getRowNodeId;

  amAlreadyUpdatingGrid: boolean = false;

  mainTableGridOptions: any

  gridApiColumnDefinitions: any;
  // gridFaded: boolean;

  // highLowHighlight: {
  //   name: string;
  //   goodIds: Array<number>;
  //   badIds: Array<number>;
  // }

  pinnedBottomRowData: Array<FandIPerformanceSummary>;
  flipCols: Array<string>;
  currentRowHeight: number;
  domLayout: DomLayoutType;
  frameworkComponents: { agColumnHeader: any; };



  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public orderBookService: OrderBookService,
    public router: Router,
    public service: FAndISummaryService,
    public gridHelpers: AGGridMethodsService,
    public columnTypeService: ColumnTypesService,

  ) {


    this.domLayout= "autoHeight";

    this.flipCols = []

    this.mainTableGridOptions = {
      getMainMenuItems:(params) => this.gridHelpers.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlightsPeople),
      getContextMenuItems: (params) => this.gridHelpers.getContextMenuItems(params),
      domLayout: this.domLayout,
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context : { thisComponent: this },
      suppressPropertyNamesCheck : true,
      onCellMouseOver: (params) => {
        //this.onCellMouseOver(params);
      },
      onCellClicked: (params) => {
        this.onCellClicked(params);
      },
      onFirstDataRendered:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
      getRowHeight: (params)=> {
        let maxHeight = 50;

        let normalHeight = Math.min(maxHeight,Math.max(maxHeight,Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return normalHeight * 1.2;
        } else {
          return normalHeight;
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpers.getFilterListItemHeight() }, autoHeight: true,
        //suppressColumnMoveAnimation: true,
        
      },
      columnTypes: {
        ... this.columnTypeService.provideColTypes(this.service.topBottomHighlightsPeople),
      },
      columnDefs: this.getColDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }
    
    // this.mainTableGridOptions.columnDefs = this.getColDefs();
  }

  ngOnDestroy() { }

  ngOnInit() {
    
  }

  initParams() {
    
  }

  getColDefs()
  {
    if(this.constants.environment.fAndISummary_includeTargets)
    {
      return this.getColDefsVindis();
    }
    else
    {
      return this.getColDefsStandard();
    }
  }
 
  getColDefsVindis() {
    let colDefs: any[] = 
    [
      { headerName: '', cellRenderer:LabelForPeopleComponent, colId: '', width: 400, type: 'label', excelField: 'SalesExecName' }, 
      { headerName: this.constants.translatedText.Site, field: 'SiteName', colId: 'site', width: 200, type: 'label', },
      { headerName: this.constants.translatedText.FinanceAddons_Deals, field: 'Deals', colId: 'dealCount', width: 106, type: 'number', },
      {headerName: this.constants.translatedText.Finance,children:[
        { headerName: this.constants.translatedText.FinanceAddons_Units, field: 'UnitsOnFinance', colId: 'financeCount', width: 106, type: 'number', },
        { headerName: 'Pen.', field: 'PercentOfUnitsOnFinance', colId: 'financePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.PerUnit, field: 'FinancePerUnit', colId: 'financePU', width: 106, type: 'currencyWithFontColour', },
      ]},
      {headerName: this.constants.translatedText.AddOns,children:[
        { headerName: this.constants.translatedText.DealDetails_Cosmetic, field: 'PercentOfDealsWithCosmeticInsurance', colId: 'cosmeticPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.FinanceAddons_PaintProtect, field: 'PercentOfDealsWithPaintProtection', colId: 'paintProtectionPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Gap, field: 'PercentOfDealsWithGap', colId: 'gapInsurancePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.DealDetails_ServicePlan, field: 'PercentOfDealsWithServicePlan', colId: 'servicePlanPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Warranty, field: 'PercentOfDealsWithWarranty', colId: 'warrantyPen', width: 106, type: 'percent', },
        { headerName: 'Products PU', field: 'ProductsPerUnit', colId: 'productsPU', width: 106, type: 'number1dp', },
        { headerName: 'PPU', field: 'AddonProfitPerUnit', colId: 'AddOnProfitPU', width: 106, type: 'currencyWithFontColour', },
      ]},
      
      {
        headerName: 'F&I Target', children: [
          { headerName: this.constants.translatedText.PerUnit, field: 'FinanceAndAddonProfitPerUnitTarget', colId: 'FinanceAndAddonProfitPerUnitTarget', width: 106, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.Total, field: 'TotalFinanceAndAddonProfitTarget', colId: 'totalProfit', width: 106, type: 'currencyWithFontColour', },
        ]
      },

      {
        headerName: 'F&I Actual', children:[
        { headerName: this.constants.translatedText.PerUnit, field: 'FinanceAndAddonProfitPerUnit', colId: 'totalProfitPU', width: 106, type: 'currencyWithFontColour', },
        { headerName: this.constants.translatedText.Total, field: 'TotalFinanceAndAddonProfit', colId: 'totalProfit', width: 106, type: 'currency', },
      ]
      },

      {
        headerName: 'F&I Vs', children: [
          { headerName: this.constants.translatedText.PerUnit, field: 'FinanceAndAddonProfitPerUnitVsTarget', colId: 'AddonProfitPerUnit', width: 106, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.Total, field: 'TotalFinanceAndAddonProfitVsTarget', colId: 'totalProfit', width: 106, type: 'currencyWithFontColour', },
        ]
      },

      {headerName: this.constants.translatedText.TotalProfit,children:[
        { headerName: this.constants.translatedText.PerUnit, field: 'TotalNLProfitPerUnit', colId: 'totalNLProfitPU', width: 106, type: 'currencyWithFontColour', },
      ]},

    ]

    return colDefs;
  }

  getColDefsStandard() {

    let colDefs: any[] = 
    [
      { headerName: '', cellRenderer:LabelForPeopleComponent, colId: '', width: 400, type: 'label' }, // excelField: 'SalesExecName'
      { headerName: this.constants.translatedText.Site, field: 'SiteName', colId: 'site', width: 200, type: 'label', },
      { headerName: this.constants.translatedText.FinanceAddons_Deals, field: 'Deals', colId: 'dealCount', width: 106, type: 'number', },
      {headerName: this.constants.translatedText.Finance,children:[
        { headerName: this.constants.translatedText.FinanceAddons_Units, field: 'UnitsOnFinance', colId: 'financeCount', width: 106, type: 'number', },
        { headerName: 'Pen.', field: 'PercentOfUnitsOnFinance', colId: 'financePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.PerUnit, field: 'FinancePerUnit', colId: 'financePU', width: 106, type: 'currencyWithFontColour', },
      ]},
      {headerName: this.constants.translatedText.AddOns,children:[
        { headerName: this.constants.translatedText.DealDetails_Cosmetic, field: 'PercentOfDealsWithCosmeticInsurance', colId: 'cosmeticPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.FinanceAddons_PaintProtect, field: 'PercentOfDealsWithPaintProtection', colId: 'paintProtectionPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Gap, field: 'PercentOfDealsWithGap', colId: 'gapInsurancePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Tyre, field: 'PercentOfDealsWithTyre', colId: 'TyrePen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Alloy, field: 'PercentOfDealsWithAlloy', colId: 'alloyPen', width: 106, type: 'percent', hide: this.constants.environment.fAndISummary_hideAlloyColumn },
        { headerName: this.constants.translatedText.FinanceAddons_TyreAlloy, field: 'PercentOfDealsWithTyreAndAlloy', colId: 'tyreAlloyPen', width: 106, type: 'percent', },
        { headerName: 'WheelGuard', field: 'PercentOfDealsWithWheelGard', colId: 'wheelGuardPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.DealDetails_ServicePlan, field: 'PercentOfDealsWithServicePlan', colId: 'servicePlanPen', width: 106, type: 'percent', },
        { headerName: this.constants.translatedText.Warranty, field: 'PercentOfDealsWithWarranty', colId: 'warrantyPen', width: 106, type: 'percent', },
        { headerName: 'Products PU', field: 'ProductsPerUnit', colId: 'productsPU', width: 106, type: 'number1dp', },
        { headerName: 'PPU', field: 'AddonProfitPerUnit', colId: 'AddOnProfitPU', width: 106, type: 'currencyWithFontColour', },
      ]},


      {
        headerName: this.constants.translatedText.FinanceAddons_TotalFinanceAndAddon, children:[
        { headerName: this.constants.translatedText.PerUnit, field: 'FinanceAndAddonProfitPerUnit', colId: 'totalProfitPU', width: 106, type: 'currencyWithFontColour', },
        { headerName: this.constants.translatedText.Total, field: 'TotalFinanceAndAddonProfit', colId: 'totalProfit', width: 106, type: 'currency', },
      ]
      },

      {headerName: this.constants.translatedText.TotalProfit,children:[
        { headerName: this.constants.translatedText.PerUnit, field: 'TotalNLProfitPerUnit', colId: 'totalNLProfitPU', width: 140, type: 'currencyWithFontColour', },
      ]},

    ]

    return colDefs;
  }

  onGridReady(params) {
    //this.gridHelpers.topBottomHighlights = [];
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);
    this.gridApi.setRowData(this.rowData);
    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();

  }

  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }

  }



  public refreshCells(): void {
    if(this.gridApi){
      this.gridApi.refreshCells();
    }
  }


  public excelExport(): void {
    //get tableModel from ag-grid
    let tableModel:IRowModel = this.gridApi.getModel()
    
    
    this.excel.createSheetObject(tableModel, this.constants.translatedText.Dashboard_FinanceAndAddons, 1, 1);
  }


  // Click through to Orderbook if dealcount selected
  onCellClicked(rowData: any): void {

    if (this.managers) 
    {
      this.clickedPeople.emit(rowData.node.data);
      return;
    }

    this.orderBookService.initOrderbook();

    this.orderBookService.franchises = this.service.fAndISummary.franchises;
    this.orderBookService.vehicleTypeTypes = this.service.fAndISummary.vehicleTypeTypes;
    this.orderBookService.orderTypeTypes = this.service.fAndISummary.orderTypeTypes,
    
    this.orderBookService.accountingDate.startDate = this.service.fAndISummary.deliveryDate.startDate;
    this.orderBookService.accountingDate.endDate = this.service.fAndISummary.deliveryDate.endDate;
    this.orderBookService.accountingDate.endDate.setUTCHours(23, 59, 59, 999);

    if (this.service.months.length > 1) {
      this.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Custom;
    } else {
      this.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Month;
      // this.orderBookService.accountingDate.lastChosenMonthStart = this.constants.startOfMonth(new Date());
      // this.orderBookService.accountingDate.lastChosenDay = this.constants.todayStart;
    }
    
    this.orderBookService.accountingDate.lastChosenMonthName = this.service.fAndISummary.deliveryDate.monthName;

    this.orderBookService.orderDate.startDate = this.constants.earliestOrderDate;
    this.orderBookService.orderDate.endDate = this.constants.todayEnd;
    this.orderBookService.orderDate.timePeriod = OrderbookTimePeriod.Anytime;
    this.orderBookService.orderDate.lastChosenMonthStart = this.constants.startOfMonth(new Date());
    this.orderBookService.orderDate.lastChosenWeekStart = this.constants.startOfWeek(new Date());
    this.orderBookService.orderDate.lastChosenDay = this.constants.todayEnd;

    this.orderBookService.salesExecId = rowData.data.SalesmanId;
    this.orderBookService.salesExecName = rowData.data.SalesExecName;

    this.orderBookService.showOrderbook();
  }

}
