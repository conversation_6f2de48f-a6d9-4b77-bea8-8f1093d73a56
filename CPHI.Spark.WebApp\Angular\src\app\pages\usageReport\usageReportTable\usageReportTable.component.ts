import { ICellRendererParams } from "ag-grid-community";
import { Component, ComponentFactoryResolver, HostListener, OnInit } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CellClassParams, FilterChangedEvent, RowHeightParams, ValueFormatterParams, ValueGetterParams } from "ag-grid-community";
import { CphPipe } from "src/app/cph.pipe";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExportService";
import { SelectionsService } from "src/app/services/selections.service";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { CustomHeaderNew } from "src/app/components/customHeader/customHeader.component";
import { CustomHeaderService } from "src/app/components/customHeader/customHeader.service";
import { TableLayoutManagementService } from "src/app/components/tableLayoutManagement/tableLayoutManagement.service";
import { CPHAutoPriceColDef } from "src/app/model/CPHColDef";
import { UsageReportService } from "../usageReport.service";
import { Subscription } from "rxjs";
import { CPHAutoPriceColGroupDef, CPHColGroupDef } from "src/app/model/CPHColGroupDef";
import { UsageItem, UsageItemForExcel } from "src/app/model/UsageItem";
import moment from "moment";

@Component({
  selector: 'usageReportTable',
  templateUrl: './usageReportTable.component.html',
  styleUrls: ['./usageReportTable.component.scss']

})
export class UsageReportTableComponent implements OnInit {

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.service.tableLayoutManagement.gridApi) {
      this.service.tableLayoutManagement.gridApi.resetRowHeights();
    }
  }

  components: { [p: string]: any; } = {
    agColumnHeader: CustomHeaderNew,
  };
  gridOptions: GridOptionsCph;
  newDataSubscription: Subscription;

  totalRow: any[] = [];

  constructor(
    public selections: SelectionsService,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    private colTypesService: ColumnTypesService,
    private customHeader: CustomHeaderService,
    public tableLayoutManagementService: TableLayoutManagementService,
    public service: UsageReportService,
    
  ) { }

  ngOnInit(): void {
    this.initialiseGrid();

    this.newDataSubscription = this.service.newDataEmitter.subscribe(() => {
      if (this.service.tableLayoutManagement.gridApi) {
        this.service.tableLayoutManagement.gridApi.setRowData(this.service.usageReportRowData);
        if (!this.service.tableLayoutManagement.lastTableState) {
          this.service.tableLayoutManagement.gridColumnApi.autoSizeAllColumns();
        }
      }
    })

    this.service.searchTerm.valueChanges.subscribe(value => {
      this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value);
    })

    this.tableLayoutManagementService.defaultFilterState = null;
    this.tableLayoutManagementService.parent = this.service.tableLayoutManagement;
  }

  ngOnDestroy(): void {
    if (this.newDataSubscription) { this.newDataSubscription.unsubscribe(); }
    if (this.service.tableLayoutManagement?.gridApi) { this.service.tableLayoutManagement.gridApi = null; }
    if (this.service.tableLayoutManagement?.gridColumnApi) { this.service.tableLayoutManagement.gridColumnApi = null; }
  }

  initialiseGrid() {
    this.gridOptions = {
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.service.usageReportRowData,
      onGridReady: (params) => this.onGridReady(params),
      rowHeight: this.gridHelpersService.getRowHeight(30),
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      //floatingFiltersHeight: this.selections.getGridRowHeight(30),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        // cellClass: 'agAlignCentre',
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true,
        cellRendererParams: {
          suppressCount: true,
          suppressTotal: true
        }
      },
      rowSelection: 'multiple',
      getRowHeight: (params) => this.rowHeightGetter(params),
      columnDefs: this.provideColumnDefs(),
      getRowClass: (params) => {
        
        if (!params.node.group && params.node.level > 0) {
          return 'hidden-leaf-row';
        }
        if (params.node.level === 1) {
          return 'dataRow'
        }
        if (params.node.group) {
          return 'grouped';
        }
        if (params.data?.Description === 'Total') {
          return 'total';
        }
      },

      onFilterChanged: (event) => {
        this.onFilterChanged(event)
      },
      autoGroupColumnDef: {
        sortable: true,
        width: 300,
        field: 'Description',
        valueGetter: (params) => 'Total',
        cellRendererSelector: (params) => {
          if (params.node.rowPinned == "bottom") { return null; }
          if (params.node.field === 'Name') {
            return null;
          } else {
            return {
              component: 'agGroupCellRenderer',
            };
          }
        }
      },
      columnTypes: { ...this.colTypesService.provideColTypes([]) },
      suppressAggFuncInHeader: true,
      pinnedBottomRowData: [{ Name: '' } as UsageItem],
      //suppressExpandablePivotGroups: true,
      groupDefaultExpanded: 1,
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      //groupUseEntireRow:true,
      //suppressRowLeafNodes:true

    }
  }

  rowHeightGetter(params: RowHeightParams<any, any>): number {
    if (params.node.isRowPinned()) { return this.gridHelpersService.getRowPinnedHeight() }
    if (params.node.level == 0) {
      return this.gridHelpersService.getStandardHeight()
    }
    return this.gridHelpersService.getStandardHeight()
  }

  onFilterChanged(event: FilterChangedEvent<any, any>) {
    event.api.redrawRows()
  }

  provideColumnDefs(): CPHAutoPriceColDef[] {
    // console.log(this.service.usageReportRowData);

    //dynamically build up weeks and day cols
    const weekAndDayCols = this.buildDayCols();
    const weekCols = this.buildUpWeekTotalCols();

    //Build up the columns
    let defs: CPHAutoPriceColDef[] = [
      { headerName: "Site", rowGroup: true, field: "Site",  hide: true, suppressSizeToFit: true, minWidth: 200, width: 200, type: 'label', },
      { headerName: "Name", rowGroup: true, field: "Name", hide: true, suppressSizeToFit: true, cellClass: 'agAlignLeft', type: 'label', },
      ...weekAndDayCols,
      ...weekCols
    ];

    return defs as CPHAutoPriceColDef[];
  }

  private buildUpWeekTotalCols(): CPHColGroupDef[] {

    const weekCols: CPHAutoPriceColDef[] = [];

    this.service.weekLabels.forEach((weekLabel, index) => {
      weekCols.push(
        {
          headerName: weekLabel,
          maxWidth: 70,
          type: 'number',
          field: `Usage${index}`,
          valueGetter: (params) => this.weekTotalGetter(params,weekLabel),
          aggFunc: (params) => this.weekAvgAggFunc(params),
          headerValueGetter: (params: any) => {

            const twoWeeksAgo = moment().subtract(2, 'weeks').startOf('isoWeek');
            const threeWeeksAgo = moment().subtract(3, 'weeks').startOf('isoWeek');
            
            if(weekLabel == 'This week'){ return this.constants.translatedText.Common_TheWeek; }
            else if(weekLabel == 'Last week'){ return this.constants.translatedText.Common_LastWeek; }
            else if(params.colDef.field == 'Usage0')
            {
              return this.cphPipe.transform(threeWeeksAgo.toDate(), 'week', 0);
            }
            else if(params.colDef.field == 'Usage1')
            {
              return this.cphPipe.transform(twoWeeksAgo.toDate(), 'week', 0);
            }

          },
          cellClass: (params) => this.colourWeeklyAverageCells(params)
        }
      );
    });

    return [{ headerName: this.constants.translatedText.UsageReport_TotalDailyLoginsByWeek, children: weekCols }];
  }

  weekTotalGetter(params:ValueGetterParams,weekLabel:string){
    if (params.node.isRowPinned()) {
      return this.service.usageReportRowData.filter(x => x.weekLabel === weekLabel && x.Usage > 0).length;
    }
  }

  weekAvgAggFunc(params: any) {
    const weekLabel = params.colDef?.headerName;
    // console.log(params)
    if (params.rowNode?.isRowPinned()) {
      return this.service.usageReportRowData.filter(x => x.weekLabel === weekLabel).length;
    }

    //work out total
    let total = 0;
    params.rowNode.allLeafChildren.forEach(child => {
      if (child.data?.weekLabel == params.colDef.headerName) {
        total += (child.data.Usage > 0 ? 1 : 0);
      }
    })

    return total// / 5;
  }


  colourWeeklyAverageCells(params: CellClassParams) {
    //pinned bottom 
    if(params.node.isRowPinned()){ return 'ag-right-aligned-cell'}

    //people levels
    if (params.colDef && params.colDef.field.includes('Usage') && params.colDef.headerName !== 'This week' && params.node.uiLevel === 1) {
      return params.value >= 3  ? 'ag-right-aligned-cell greenCellBG' : 'ag-right-aligned-cell greyCellBG';
    }

    //site levels
    return 'ag-right-aligned-cell'
  }

  dayCellClass(params: CellClassParams) {
    
    //pinned bottom, nothing
    if(params.node.isRowPinned()){ return ''}

    //site groups, group
    if (params.node?.level == 0) { return 'group' }
    
    //else green if > 0
    return params.value > 0 ? 'greenCellBG' : 'greyCellBG' 

  }

  private buildDayCols(): CPHAutoPriceColDef[] {

    const weekAndDayCols = [];
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    let i = 0;

    this.service.weekLabels.forEach(weekLabel => {
      const dayCols: CPHAutoPriceColDef[] = [];
      days.forEach(day => {
        dayCols.push(
          {
            headerName: day,
            field: `${weekLabel}.${day}`,
            cellClass: (params) => this.dayCellClass(params),
            headerValueGetter: (params: any) => {
              if(day == 'Mon'){ return this.constants.translatedText.Common_Monday.substring(0, 3); }
              else if(day == 'Tue'){ return this.constants.translatedText.Common_Tuesday.substring(0, 3); }
              else if(day == 'Wed'){ return this.constants.translatedText.Common_Wednesday.substring(0, 3); }
              else if(day == 'Thu'){ return this.constants.translatedText.Common_Thursday.substring(0, 3); }
              else if(day == 'Fri'){ return this.constants.translatedText.Common_Friday.substring(0, 3); }
              else if(day == 'Sat'){ return this.constants.translatedText.Common_Saturday.substring(0, 3); }
              else if(day == 'Sun'){ return this.constants.translatedText.Common_Sunday.substring(0, 3); }
            },
            type: 'number',
            maxWidth: 40,
            valueGetter: (params) => this.dayColGetter(params, weekLabel, day),
            cellRenderer:(params)=>this.dayRenderer(params),
            aggFunc: 'sum'
          }
        );
      });

      
      weekAndDayCols.push(
        {
          headerName: weekLabel,
          headerValueGetter: (params: any) => {

          const twoWeeksAgo = moment().subtract(2, 'weeks').startOf('isoWeek');
          const threeWeeksAgo = moment().subtract(3, 'weeks').startOf('isoWeek');
          
          if(weekLabel == 'This week'){ return this.constants.translatedText.Common_TheWeek; }
          else if(weekLabel == 'Last week'){ return this.constants.translatedText.Common_LastWeek; }
          else if(i == 0)
          {
            i++;
            return this.cphPipe.transform(threeWeeksAgo.toDate(), 'week', 0);
          }
          else if(i == 1)
          {
            return this.cphPipe.transform(twoWeeksAgo.toDate(), 'week', 0);
          }
          
        },
        children: dayCols
        }
      );
    });
    return weekAndDayCols;
  }

  dayRenderer(params: ICellRendererParams<any, any>): string {

    //pinned bottom
    if (params.node.isRowPinned()) {
      return params.value;
    }

    // Summary row
    if(params.node.level == 0){
      return this.cphPipe.transform(params.value,'number',0)
    }

    // Blank if day is after today
    if(params.colDef.field.includes('This week'))
    {
      if(this.isAfterToday(params.colDef.field))
        {
          return null;
        }
    }

    return this.colTypesService.booleanValueRenderer(params.value===1);
  }

  isAfterToday(dayString: string): boolean {

    const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    const todayIndex = this.constants.todayStart.getDay(); 
    const todayDayShort = daysOfWeek[(todayIndex + 6) % 7]; // Convert to Mon-Sun format (shift Sunday)
  
    // Extract the day from the input string (e.g., "This week.Sun" -> "Sun")
    const dayPart = dayString.split('.')[1]; 
  
    const todayPosition = daysOfWeek.indexOf(todayDayShort); 
    const dayPosition = daysOfWeek.indexOf(dayPart); 
  
    return dayPosition > todayPosition;
  }

  dayColGetter(params: ValueGetterParams<any>, weekLabel: string, day: string): any {
    if (params.node.isRowPinned()) {
      return this.service.usageReportRowData.filter(x => x.weekLabel === weekLabel && x.dayLabel === day && x.Usage > 0).length;
    }
    const dayVal = params.data?.[weekLabel]?.[day] ?? 0;
    return dayVal > 0 ? 1 : 0;
  }

  onGridReady(event) {
    this.service.tableLayoutManagement.gridApi = event.api;
    this.service.tableLayoutManagement.gridColumnApi = event.columnApi;

    if (this.service.tableLayoutManagement.loadedTableState) {
      this.service.tableLayoutManagement.gridColumnApi.applyColumnState({ state: this.service.tableLayoutManagement.loadedTableState });
      this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.tableLayoutManagement.filterModel);
    }

    if (this.service.tableLayoutManagement.filterModel) {
      this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.tableLayoutManagement.filterModel);
      this.service.tableLayoutManagement.gridApi.onFilterChanged();
    }

    if (this.service.externalFilterModel) {
      this.service.tableLayoutManagement.gridApi.setFilterModel(this.service.externalFilterModel);
      this.service.tableLayoutManagement.gridApi.onFilterChanged();
    }

    this.service.tableLayoutManagement.originalColDefs = this.provideColumnDefs();
  }

  clearSearchTerm() {
    this.service.searchTerm.setValue("");
    if (this.service.tableLayoutManagement.gridApi) this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value);
  }

  excelExport() {
    this.selections.triggerSpinner.next({ show: true });
  
    const columnsToReveal = ['Site', 'Name'];
  
    // Temporarily show these columns
    columnsToReveal.forEach(colId => {
      this.service.tableLayoutManagement.gridColumnApi.setColumnVisible(colId, true);
      this.service.tableLayoutManagement.gridColumnApi.removeRowGroupColumn(colId);
    });
    
    this.service.usageReportRowDataForExcel = this.flattenRowsForExcel(this.service.usageReportRowData);

    // Order firstly by Site then by Name
    this.service.usageReportRowDataForExcel.sort((a, b) => {
      // Compare by Site
      if (a.Site < b.Site) return -1;
      if (a.Site > b.Site) return 1;
    
      // If Site is the same, compare by Name
      if (a.Name < b.Name) return -1;
      if (a.Name > b.Name) return 1;
    
      // If both Site and Name are the same, return 0
      return 0;
    });

    // Pass the amended usageReportRowData to the Excel
    this.service.tableLayoutManagement.gridApi.setRowData(this.service.usageReportRowDataForExcel);

    let tableModel: any = this.constants.clone(this.service.tableLayoutManagement.gridApi.getModel());

    // Set the fields to the relevant property on the flat object
    tableModel.columnModel.displayedColumns[2].colDef.field = 'ThreeWeeksAgo1';
    tableModel.columnModel.displayedColumns[3].colDef.field = 'ThreeWeeksAgo2';
    tableModel.columnModel.displayedColumns[4].colDef.field = 'ThreeWeeksAgo3';
    tableModel.columnModel.displayedColumns[5].colDef.field = 'ThreeWeeksAgo4';
    tableModel.columnModel.displayedColumns[6].colDef.field = 'ThreeWeeksAgo5';
    tableModel.columnModel.displayedColumns[7].colDef.field = 'ThreeWeeksAgo6';
    tableModel.columnModel.displayedColumns[8].colDef.field = 'ThreeWeeksAgo7';

    tableModel.columnModel.displayedColumns[9].colDef.field = 'TwoWeeksAgo1';
    tableModel.columnModel.displayedColumns[10].colDef.field = 'TwoWeeksAgo2';
    tableModel.columnModel.displayedColumns[11].colDef.field = 'TwoWeeksAgo3';
    tableModel.columnModel.displayedColumns[12].colDef.field = 'TwoWeeksAgo4';
    tableModel.columnModel.displayedColumns[13].colDef.field = 'TwoWeeksAgo5';
    tableModel.columnModel.displayedColumns[14].colDef.field = 'TwoWeeksAgo6';
    tableModel.columnModel.displayedColumns[15].colDef.field = 'TwoWeeksAgo7';

    tableModel.columnModel.displayedColumns[16].colDef.field = 'LastWeek1';
    tableModel.columnModel.displayedColumns[17].colDef.field = 'LastWeek2';
    tableModel.columnModel.displayedColumns[18].colDef.field = 'LastWeek3';
    tableModel.columnModel.displayedColumns[19].colDef.field = 'LastWeek4';
    tableModel.columnModel.displayedColumns[20].colDef.field = 'LastWeek5';
    tableModel.columnModel.displayedColumns[21].colDef.field = 'LastWeek6';
    tableModel.columnModel.displayedColumns[22].colDef.field = 'LastWeek7';

    tableModel.columnModel.displayedColumns[23].colDef.field = 'ThisWeek1';
    tableModel.columnModel.displayedColumns[24].colDef.field = 'ThisWeek2';
    tableModel.columnModel.displayedColumns[25].colDef.field = 'ThisWeek3';
    tableModel.columnModel.displayedColumns[26].colDef.field = 'ThisWeek4';
    tableModel.columnModel.displayedColumns[27].colDef.field = 'ThisWeek5';
    tableModel.columnModel.displayedColumns[28].colDef.field = 'ThisWeek6';
    tableModel.columnModel.displayedColumns[29].colDef.field = 'ThisWeek7';

    tableModel.columnModel.displayedColumns[30].colDef.field = 'ThreeWeeksAgoSum';
    tableModel.columnModel.displayedColumns[31].colDef.field = 'TwoWeeksAgoSum';
    tableModel.columnModel.displayedColumns[32].colDef.field = 'LastWeekSum';
    tableModel.columnModel.displayedColumns[33].colDef.field = 'ThisWeekSum';
    
    // Create and set the Total row
    let totalRow = this.createTotalRowForExcel(this.service.usageReportRowDataForExcel);
    tableModel.gridOptionsService.api.setPinnedBottomRowData([totalRow]);

    this.excel.createSheetObject(tableModel, this.constants.translatedText.UsageReport_Title, 1, 1);
  
    // Revert the previous changes so the grid displays properly again - important!
    columnsToReveal.forEach(colId => {
      this.service.tableLayoutManagement.gridColumnApi.setColumnVisible(colId, false);
      this.service.tableLayoutManagement.gridColumnApi.addRowGroupColumn(colId);
    });
  
    this.service.tableLayoutManagement.gridApi.setRowData(this.service.usageReportRowData);

    this.selections.triggerSpinner.next({ show: false });
  }
  
  createTotalRowForExcel(input: UsageItemForExcel[]): UsageItemForExcel
  {
    // Add a Total row
    const totalRow: UsageItemForExcel = {
      Site: "Total",
      Name: "",
      ThreeWeeksAgo1: 0, ThreeWeeksAgo2: 0, ThreeWeeksAgo3: 0, ThreeWeeksAgo4: 0, ThreeWeeksAgo5: 0, ThreeWeeksAgo6: 0, ThreeWeeksAgo7: 0,
      TwoWeeksAgo1: 0, TwoWeeksAgo2: 0, TwoWeeksAgo3: 0, TwoWeeksAgo4: 0, TwoWeeksAgo5: 0, TwoWeeksAgo6: 0, TwoWeeksAgo7: 0,
      LastWeek1: 0, LastWeek2: 0, LastWeek3: 0, LastWeek4: 0, LastWeek5: 0, LastWeek6: 0, LastWeek7: 0,
      ThisWeek1: 0, ThisWeek2: 0, ThisWeek3: 0, ThisWeek4: 0, ThisWeek5: 0, ThisWeek6: 0, ThisWeek7: 0,
      ThreeWeeksAgoSum: 0,
      TwoWeeksAgoSum: 0,
      LastWeekSum: 0,
      ThisWeekSum: 0,
    };

    // Sum all numeric columns for the Total row
    input.forEach(row => {
      totalRow.ThreeWeeksAgo1 += row.ThreeWeeksAgo1;
      totalRow.ThreeWeeksAgo2 += row.ThreeWeeksAgo2;
      totalRow.ThreeWeeksAgo3 += row.ThreeWeeksAgo3;
      totalRow.ThreeWeeksAgo4 += row.ThreeWeeksAgo4;
      totalRow.ThreeWeeksAgo5 += row.ThreeWeeksAgo5;
      totalRow.ThreeWeeksAgo6 += row.ThreeWeeksAgo6;
      totalRow.ThreeWeeksAgo7 += row.ThreeWeeksAgo7;

      totalRow.TwoWeeksAgo1 += row.TwoWeeksAgo1;
      totalRow.TwoWeeksAgo2 += row.TwoWeeksAgo2;
      totalRow.TwoWeeksAgo3 += row.TwoWeeksAgo3;
      totalRow.TwoWeeksAgo4 += row.TwoWeeksAgo4;
      totalRow.TwoWeeksAgo5 += row.TwoWeeksAgo5;
      totalRow.TwoWeeksAgo6 += row.TwoWeeksAgo6;
      totalRow.TwoWeeksAgo7 += row.TwoWeeksAgo7;

      totalRow.LastWeek1 += row.LastWeek1;
      totalRow.LastWeek2 += row.LastWeek2;
      totalRow.LastWeek3 += row.LastWeek3;
      totalRow.LastWeek4 += row.LastWeek4;
      totalRow.LastWeek5 += row.LastWeek5;
      totalRow.LastWeek6 += row.LastWeek6;
      totalRow.LastWeek7 += row.LastWeek7;

      totalRow.ThisWeek1 += row.ThisWeek1;
      totalRow.ThisWeek2 += row.ThisWeek2;
      totalRow.ThisWeek3 += row.ThisWeek3;
      totalRow.ThisWeek4 += row.ThisWeek4;
      totalRow.ThisWeek5 += row.ThisWeek5;
      totalRow.ThisWeek6 += row.ThisWeek6;
      totalRow.ThisWeek7 += row.ThisWeek7;

      totalRow.ThreeWeeksAgoSum += row.ThreeWeeksAgoSum;
      totalRow.TwoWeeksAgoSum += row.TwoWeeksAgoSum;
      totalRow.LastWeekSum += row.LastWeekSum;
      totalRow.ThisWeekSum += row.ThisWeekSum;
    });

    return totalRow;
  }


  flattenRowsForExcel(input: UsageItem[]): UsageItemForExcel[] {

    //console.log(input, "input");

    const result: { [key: string]: UsageItemForExcel } = {};
  
    input.forEach(item => {

      // Create a unique key for Site + Name
      const key = `${item.Site}_${item.Name}`;
  
      // Determine the week and day fields from UsageDate
      const weekLabel = this.getWeekLabel(item.UsageDate); // Maps to "ThreeWeeksAgo", "TwoWeeksAgo", etc.
      const dayField = this.getDayField(item.UsageDate);   // Maps to 1-7 for Mon-Sun
      const weekField = `${weekLabel}${dayField}`;         // Example: "ThreeWeeksAgo1"
  
      // Debugging to validate the weekField
      // console.log(`Processing item:`, item);
      // console.log(`WeekField: ${weekField}`);
  
      // Initialize a new UsageItemForExcel if the key doesn't exist
      if (!result[key]) {
        result[key] = {
          Site: item.Site,
          Name: item.Name,
  
          ThreeWeeksAgo1: 0, ThreeWeeksAgo2: 0, ThreeWeeksAgo3: 0, ThreeWeeksAgo4: 0, ThreeWeeksAgo5: 0, ThreeWeeksAgo6: 0, ThreeWeeksAgo7: 0,
          TwoWeeksAgo1: 0, TwoWeeksAgo2: 0, TwoWeeksAgo3: 0, TwoWeeksAgo4: 0, TwoWeeksAgo5: 0, TwoWeeksAgo6: 0, TwoWeeksAgo7: 0,
          LastWeek1: 0, LastWeek2: 0, LastWeek3: 0, LastWeek4: 0, LastWeek5: 0, LastWeek6: 0, LastWeek7: 0,
          ThisWeek1: 0, ThisWeek2: 0, ThisWeek3: 0, ThisWeek4: 0, ThisWeek5: 0, ThisWeek6: 0, ThisWeek7: 0,
  
          ThreeWeeksAgoSum: 0,
          TwoWeeksAgoSum: 0,
          LastWeekSum: 0,
          ThisWeekSum: 0,
        };
      }
  
      // Mark the corresponding day field with 1
      if (weekField in result[key]) {
        (result[key] as any)[weekField] = item.Usage > 0 ? 1 : 0; 
      }
  
      // Update the weekly total
      if (weekLabel === "ThreeWeeksAgo") {
        result[key].ThreeWeeksAgoSum += item.Usage > 0 ? 1 : 0;
      } else if (weekLabel === "TwoWeeksAgo") {
        result[key].TwoWeeksAgoSum += item.Usage > 0 ? 1 : 0;
      } else if (weekLabel === "LastWeek") {
        result[key].LastWeekSum += item.Usage > 0 ? 1 : 0;
      } else if (weekLabel === "ThisWeek") {
        result[key].ThisWeekSum += item.Usage > 0 ? 1 : 0;
      }
    });
  
  // Convert the result object back to an array
  return Object.values(result);
  }
  
  // Map a date to the appropriate week label
  getWeekLabel(date: Date): string {
    const startOfThisWeek = this.getStartOfWeek(new Date()); // Get the start of this week (Monday)
    const startOfLastWeek = new Date(startOfThisWeek);
    const startOfTwoWeeksAgo = new Date(startOfThisWeek);
    const startOfThreeWeeksAgo = new Date(startOfThisWeek);
  
    // Calculate week boundaries
    startOfLastWeek.setDate(startOfThisWeek.getDate() - 7);
    startOfTwoWeeksAgo.setDate(startOfThisWeek.getDate() - 14);
    startOfThreeWeeksAgo.setDate(startOfThisWeek.getDate() - 21);
  
    const endOfThisWeek = new Date(startOfThisWeek);
    endOfThisWeek.setDate(startOfThisWeek.getDate() + 6);
  
    // Check which week the date belongs to
    if (date >= startOfThisWeek && date <= endOfThisWeek) {
      return "ThisWeek";
    } else if (date >= startOfLastWeek && date < startOfThisWeek) {
      return "LastWeek";
    } else if (date >= startOfTwoWeeksAgo && date < startOfLastWeek) {
      return "TwoWeeksAgo";
    } else if (date >= startOfThreeWeeksAgo && date < startOfTwoWeeksAgo) {
      return "ThreeWeeksAgo";
    }
  
    throw new Error(`Date ${date.toISOString()} is outside the supported range`);
  }
  
  // Helper function to get the start of the current week (Monday)
  getStartOfWeek(date: Date): Date {
    const day = date.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const diff = day === 0 ? -6 : 1 - day; // Adjust for Monday as the first day of the week
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() + diff);
    startOfWeek.setHours(0, 0, 0, 0); // Reset to midnight
    return startOfWeek;
  }

  // Map a date to the corresponding day of the week (1 = Monday, 7 = Sunday)
  getDayField(date: Date): number {
    // Get the day of the week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    const dayOfWeek = date.getDay();
    return dayOfWeek === 0 ? 7 : dayOfWeek; // Map Sunday (0) to 7
  }

  
}