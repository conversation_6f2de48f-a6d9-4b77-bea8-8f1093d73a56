import { Component, HostListener, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { CellClickedEvent, GridApi } from "ag-grid-community";
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { localeEs } from "src/environments/locale.es.js";
import { CustomHeaderComponent } from "src/app/_cellRenderers/customHeader.component";
import { CphPipe } from "src/app/cph.pipe";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { StockReportModalComponent } from "src/app/components/stockReportModal/stockReportModal.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { StockModalRow } from "src/app/model/sales.model";
import { SpainDashboardUsedService } from "../dashboardUsedSpain.service";
import { SpainStockAgeingRow, SpainStockAgeingValueSet } from "../dashboardUsedSpain.model";
import { FormattedGridData, SpainUsedStockModalParams } from "src/app/pages/stockReport/stockReport.model";
import { StockReportService } from "src/app/pages/stockReport/stockReport.service";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";



@Component({
  selector: 'stockByAgeTable',
  template: `
    <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions"></ag-grid-angular>
  `,
  styleUrls: ['./../../../../../../styles/components/_agGrid.scss']
})

export class StockByAgeTableComponent implements OnInit,OnDestroy {
  @Input() tableHeader: string;
  @Input() data: SpainStockAgeingRow[];
  @Input() tableType:string;

  @HostListener("window:resize", [])

  private destroy$ = new Subject<void>();

  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  mainTableGridOptions: GridOptionsCph;
  formattedGridData: FormattedGridData[];

  public localeText: {
    [key: string]: string;
  } = localeEs;


  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public stockReportService: StockReportService,
    public cphPipe: CphPipe,
    public columnTypeService: ColumnTypesService,
    //public service: StockReportService,
    public getDataService: GetDataMethodsService,
    public modalService: NgbModal,
    public service: SpainDashboardUsedService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  ngOnInit(): void {
    this.formatData();
    this.setGridOptions();

    this.service.spainUsedStockAgeingSummaryChangedEmitter.pipe(takeUntil(this.destroy$)).subscribe(() => {
      if (this.gridApi) {
        let rowData: SpainStockAgeingRow[] = [];
        if (this.tableType === 'disposalRoute') {
          rowData = this.service.spainUsedStockAgeingSummary.ByDisposalRouteRows;
        } else if (this.tableType === 'vehicleAge') {
          rowData = this.service.spainUsedStockAgeingSummary.ByEdadDelVehiculoRows;
        } else if (this.tableType === 'originOfPurchase') {
          rowData = this.service.spainUsedStockAgeingSummary.ByVehicleSourceRows;
        } else if (this.tableType === 'make') {
          rowData = this.service.spainUsedStockAgeingSummary.ByMakeRows;
        }

        this.formatData(rowData);

        this.gridApi.setRowData(this.formattedGridData.filter(x => x.label != this.constants.translatedText.Total));
        this.gridApi.setPinnedBottomRowData(this.formattedGridData.filter(x => x.label == this.constants.translatedText.Total));
      }
    })
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
}

  formatData(rowData?: SpainStockAgeingRow[]) {
    let data: SpainStockAgeingRow[] = rowData ? rowData : this.data;
    let formattedGridData: FormattedGridData[] = [];

    data.forEach((row: SpainStockAgeingRow) => {
      let sets: SpainStockAgeingValueSet[] = row.AgeingValueSets;

      formattedGridData.push({
        label: row.RowLabel == 'Total' ? this.constants.translatedText.Total : row.RowLabel,
        agedUnder30: sets.find(x => x.Label == '<30') ? sets.find(x => x.Label == '<30').Units : 0,
        agedUnder30Improvement: sets.find(x => x.Label == '<30') ? sets.find(x => x.Label == '<30').Improvement : 0,
        aged30To60: sets.find(x => x.Label == '30 - 60') ? sets.find(x => x.Label == '30 - 60').Units : 0,
        aged30To60Improvement: sets.find(x => x.Label == '30 - 60') ? sets.find(x => x.Label == '30 - 60').Improvement : 0,
        aged60To90: sets.find(x => x.Label == '60 - 90') ? sets.find(x => x.Label == '60 - 90').Units : 0,
        aged60To90Improvement: sets.find(x => x.Label == '60 - 90') ? sets.find(x => x.Label == '60 - 90').Improvement : 0,
        aged90To120: sets.find(x => x.Label == '90 - 120') ? sets.find(x => x.Label == '90 - 120').Units : 0,
        aged90To120Improvement: sets.find(x => x.Label == '90 - 120') ? sets.find(x => x.Label == '90 - 120').Improvement : 0,
        aged120To150: sets.find(x => x.Label == '120 - 150') ? sets.find(x => x.Label == '120 - 150').Units : 0,
        aged120To150Improvement: sets.find(x => x.Label == '120 - 150') ? sets.find(x => x.Label == '120 - 150').Improvement : 0,
        aged150To180: sets.find(x => x.Label == '150 -180') ? sets.find(x => x.Label == '150 -180').Units : 0,
        aged150To180Improvement: sets.find(x => x.Label == '150 -180') ? sets.find(x => x.Label == '150 -180').Improvement : 0,
        aged180To365: sets.find(x => x.Label == '180 - 365') ? sets.find(x => x.Label == '180 - 365').Units : 0,
        aged180To365Improvement: sets.find(x => x.Label == '180 - 365') ? sets.find(x => x.Label == '180 - 365').Improvement : 0,
        agedOver365: sets.find(x => x.Label == '>365') ? sets.find(x => x.Label == '>365').Units : 0,
        agedOver365Improvement: sets.find(x => x.Label == '>365') ? sets.find(x => x.Label == '>365').Improvement : 0,
        total: sets.find(x => x.Label == 'Total') ? sets.find(x => x.Label == 'Total').Units : 0,
        changeVsLastMonth: sets.find(x => x.Label == 'Total') ? sets.find(x => x.Label == 'Total').Improvement : 0
      })
    })

    this.formattedGridData = formattedGridData;
  }

 

  setGridOptions() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      // getLocaleText: (key: string, defaultValue: string) => {
      //   this.constants.currentLang == 'es' ? localeEs[key] || defaultValue : defaultValue;
      // },
      //getLocaleText: this.localeText,
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onGridReady: (params) => {
        this.onGridReady(params);
      },
      onCellClicked:(params)=>this.onCellClicked(params),
      
      rowData: this.formattedGridData.filter(x => x.label != this.constants.translatedText.Total),
      pinnedBottomRowData: this.formattedGridData.filter(x => x.label == this.constants.translatedText.Total),
      domLayout: 'autoHeight',
      defaultColDef: {
        resizable: true,
        sortable: true,
        autoHeight: true
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: [
        { headerName: this.tableHeader, colId: 'label', field: 'label', width: 150, type: 'label' },
        { headerName: `<30 ${this.constants.translatedText.Days}`, colId: 'agedUnder30', field: 'agedUnder30', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'agedUnder30Improvement', field: 'agedUnder30Improvement', width: 40, type: 'percentWithColour' },
        { headerName: `30 - 60 ${this.constants.translatedText.Days}`, colId: 'aged30To60', field: 'aged30To60', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'aged30To60Improvement', field: 'aged30To60Improvement', width: 40, type: 'percentWithColour' },
        { headerName: `60 - 90 ${this.constants.translatedText.Days}`, colId: 'aged60To90', field: 'aged60To90', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'aged60To90Improvement', field: 'aged60To90Improvement', width: 40, type: 'percentWithColour' },
        { headerName: `90 - 120 ${this.constants.translatedText.Days}`, colId: 'aged90To120', field: 'aged90To120', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'aged90To120Improvement', field: 'aged90To120Improvement', width: 40, type: 'percentWithColour' },
        { headerName: `120 - 150 ${this.constants.translatedText.Days}`, colId: 'aged120To150', field: 'aged120To150', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'aged120To150Improvement', field: 'aged120To150Improvement', width: 40, type: 'percentWithColour' },
        { headerName: `150 - 180 ${this.constants.translatedText.Days}`, colId: 'aged150To180', field: 'aged150To180', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'aged150To180Improvement', field: 'aged150To180Improvement', width: 40, type: 'percentWithColour' },
        { headerName: `180 - 365 ${this.constants.translatedText.Days}`, colId: 'aged180To365', field: 'aged180To365', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'aged180To365Improvement', field: 'aged180To365Improvement', width: 40, type: 'percentWithColour' },
        { headerName: `>365 ${this.constants.translatedText.Days}`, colId: 'agedOver365', field: 'agedOver365', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'agedOver365Improvement', field: 'agedOver365Improvement', width: 40, type: 'percentWithColour' },
        { headerName: this.constants.translatedText.Total, colId: 'total', field: 'total', width: 40, type: 'number' },
        { headerName: 'Evol vs M-1', colId: 'changeVsLastMonth', field: 'changeVsLastMonth', width: 40, type: 'percentWithColour' }
      ]
    }
  }
  onCellClicked(params: CellClickedEvent): void {
    let rowData:FormattedGridData = params.data;
    let cleanedUpAntiguedad = params.colDef.headerName.replace(` ${this.constants.translatedText.Days}`,'')
    let parms: SpainUsedStockModalParams = {
      ConcesionarioList: this.service.spainUsedStockSummarySites.filter(x=>x.isSelected).map(x=>x.label).join(','),
      ZonasList: this.service.spainUsedStockSummaryRegions.filter(x=>x.isSelected).map(x=>x.label).join(','),
      Antiguedad: cleanedUpAntiguedad,
      DisposalRoute: this.tableType === 'disposalRoute' ? rowData.label : null,
      VehicleAge: this.tableType === 'vehicleAge' ? rowData.label : null,
      OriginOfPurchase: this.tableType === 'originOfPurchase' ? rowData.label : null,
      Make: this.tableType === 'make' ? rowData.label : null,
    }

    this.getDataService.getStockModalRowsSpain(parms).subscribe((res:StockModalRow[])=>{


      let header = `${res.length} ${params.colDef.headerName} ${this.constants.translatedText.Vehicles}`;
      if (this.selections.stockReport.report.name == 'Stock Ageing') { header = header + ' over ' + this.selections.stockReport.ageingOption.description + ' as at ' + this.selections.stockReport.asAt.description }
  
      let showUsedColumns = ['CoreUsed', 'Tactical', 'ExManagement', 'ExDemo'].includes(params.colDef.headerName);
  
      this.selections.initiateStockReportModal(true, res, [], header, showUsedColumns, params.data.Label);
      const modalRef = this.modalService.open(StockReportModalComponent, { keyboard: true, size: 'lg' });
  
      modalRef.result.then((result) => { //I get back from modal
        if (result) {
  
        }
      });



    })


  }

  getPercentCellClass(value: number) {
    let classList: string = 'ag-right-aligned-cell'
    if (value > 0) classList += ' badFont';
    if (value < 0) classList += ' goodFont';
    return classList;
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
  }

  resizeGrid() {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }
}
