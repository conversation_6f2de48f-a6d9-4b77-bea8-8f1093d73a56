import { Component, Input, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DomLayoutType, GridApi, GridOptions, GridReadyEvent, ICellRendererParams, ModelUpdatedEvent, RowClassParams, RowClickedEvent } from 'ag-grid-community';
import { CphPipe } from 'src/app/cph.pipe';
import { CPHColDef } from 'src/app/model/CPHColDef';
import { CPHColGroupDef } from 'src/app/model/CPHColGroupDef';
import { SameModelAdvert } from 'src/app/model/SameModelAdvert';
import { SimpleExampleItem } from 'src/app/model/SimpleExampleItem';
import { StatsVehicle } from 'src/app/model/StatsVehicle';
import { StockProfileItem } from 'src/app/model/StockProfileItem';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { localeEs } from 'src/environments/locale.es.js';
import { StockProfilerPageComponentType } from '../stockProfiler.component';

//This describes the properties that a service must commit to having, when it's used with this component.   
//In any service that wants to use this compnent, it must implement this interface using this syntax:
//   export class MyCoolService implements MyComponentParams  { etc.
export interface StockProfilerTableParams {
  gridRef: StockProfilerTableComponent;
  stockProfileItems: StockProfileItem[];
  dealWithFilteredItems: (filteredItems: StockProfileItem[], callingComponent: StockProfilerPageComponentType) => void;

}


@Component({
  selector: 'stockProfilerTable',
  templateUrl: './stockProfilerTable.component.html',
  styleUrls: ['./stockProfilerTable.component.scss']
})
export class StockProfilerTableComponent implements OnInit {

  @Input() tableParams: StockProfilerTableParams;
  gridOptions: GridOptions;
  gridApi: GridApi;
  indicateNewData: boolean;
  domLayout: DomLayoutType = "autoHeight";

  constructor(
    public gridHelpersService: AGGridMethodsService,
    public constantsService: ConstantsService,
    public cphPipe: CphPipe,
    private modalService: NgbModal,
    private columnTypesService: ColumnTypesService
  ) { }

  ngOnInit(): void {
    this.setGridDefinitions();
  }

  ngOnDestroy() {
    this.tableParams.gridRef = null;
  }

  setGridDefinitions() {
    this.gridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) => this.constantsService.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      defaultColDef: {
        resizable: true,
        sortable: true,
        filterParams: {
          applyButton: false,
          clearButton: true,
          cellHeight: this.gridHelpersService.getFilterListItemHeight()
        },
        autoHeight: true,
        floatingFilter: false
      },
      getRowClass: (params) => {
        return this.rowClassGetter(params)
      },
      columnTypes: {
        ...this.columnTypesService.provideColTypes([]),
      },
      onRowClicked: (params) => this.onRowClick(params),
      rowData: this.tableParams.stockProfileItems,//.filter(x=>x.RetailerSiteName == 'Volvo Cars Oxford'),
      columnDefs: this.provideColDefs(this.tableParams.stockProfileItems),
      onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
      onFilterChanged: (event) => this.onFilterChanged(event),
      onModelUpdated: (event: ModelUpdatedEvent) => this.gridApi?.sizeColumnsToFit(),
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
    }
  }


  provideColDefs(rowData: StockProfileItem[]): (CPHColDef|CPHColGroupDef)[] {
    console.log(rowData.filter(x=>x.RetailerSiteName=='Pentagon Sheffield'&&x.ModelClean=='Astra'))
    let maxAbove = 0;
    let maxBelow = 0;
    let lowerBias = 0;
    let highestBias = 0;
   
  const regularColsWidth = 100;
    const maxWidth=160;

    return [
      { headerName: 'Site', rowGroup: true, hide: true,  maxWidth: maxWidth, colId: 'RetailerSiteName', field: 'RetailerSiteName', type: 'labelSetFilter', width: 100 },
      { headerName: 'Model', rowGroup: true, hide: true, maxWidth: maxWidth, colId: 'ModelClean', field: 'ModelClean', type: 'labelSetFilter', width: regularColsWidth },
      { headerName: 'StockLevel', aggFunc: "sum", maxWidth: maxWidth, colId: 'StockLevel', field: 'StockLevel', type: 'number', width: regularColsWidth },
      
      //Logins this week

      
      
      
      
    ];
  }

  rowClassGetter(params: RowClassParams<any, any>): string[] {
    const item: SimpleExampleItem = params.data;
    return item?.isChosen ? ['brightHighlight'] : []
  }

  getImage(params: ICellRendererParams) {
    const row: SameModelAdvert = params.data;

    if (!row || !row?.ImageUrl) return '';
    return `<img style="height: 50px; width: 100%;" src=${row.ImageUrl} />`;
  }

  onGridReady(event: GridReadyEvent) {
    this.gridApi = event.api;
    //this.gridApi.sizeColumnsToFit();
    this.tableParams.gridRef = this;
  }

  dealWithNewData(data: StatsVehicle[]) {
    this.gridApi.setRowData(data);
    this.gridApi.sizeColumnsToFit();
    this.indicateNewData = true;
    setTimeout(() => { this.indicateNewData = false; }, 1000);
  }

  onFilterChanged(event: any) {
    // Get the filtered rows

    const filteredNodes = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      filteredNodes.push(node);
    })
    const filteredItems = filteredNodes.map(node => node.data); // Get data from each node

    // Call the parent service method and pass the filtered items
    this.tableParams.dealWithFilteredItems(filteredItems, StockProfilerPageComponentType.grid);
  }

  onRowClick(params: RowClickedEvent<any, any>): void {
    const item: SimpleExampleItem = params.data;
    item.isChosen = !item.isChosen;
    params.node.setData(item);
    this.tableParams.dealWithFilteredItems(this.tableParams.stockProfileItems, StockProfilerPageComponentType.grid);
  }
}
