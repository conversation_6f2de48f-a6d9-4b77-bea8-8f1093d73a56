import { CompetitorSummary } from "src/app/model/CompetitorSummary";
import { VehicleValuationService } from "../autoPriceValuationModal/vehicleValuation.service";
import { VehicleAdvertDetail } from "src/app/model/VehicleAdvertDetail";
import { TradePriceSetting } from "src/app/model/TradePriceSetting";


export interface CompetitorAnalysisParams {

  //either this if we are within an insights modal
  AdvertId?: number;
  RetailerSiteIdForPostcode:number;
  DerivativeId:string;
  WebSiteSearchIdentifier?: string;
  ImageURL: string;

  //or these
  Make: string;
  Model: string;
  Trim: string;
  TransmissionType: string;
  FuelType: string;
  BodyType: string;
  Drivetrain: string;
  Doors: number;
  AdvertDetail?:VehicleAdvertDetail;

  //plus always these:
  ParentType: 'insightsModal' | 'valuationModal';
  VehicleReg: string;
  OdometerReading: number;
  FirstRegisteredDate: Date;
  AdvertisedPrice: number;
  PricePosition:number;
  Valuation: number;
  RetailerSiteRetailerId: number;
  CompetitorSummary: CompetitorSummary; //list of competitors
  VehicleValuationService?: VehicleValuationService
  TradePriceSetting?: TradePriceSetting;
}
