<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        <span>{{ constants.translatedText.Dashboard_VoC_Title }}</span>
        <span *ngIf="siteStatCards && siteCurrentlyViewing"> - {{ siteCurrentlyViewing.Site }}</span>
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Voc"></sourceDataUpdate>
      </div>
    </h4>

    <!-- FOR SELECTING MONTH -->
    <div class="buttonGroup">
      <!-- previousMonth -->
      <button class="btn btn-primary" (click)="changeMonth(-1)"><i class="fas fa-caret-left"></i></button>

      <!-- dropdownMonth -->
      <div ngbDropdown class="d-inline-block" [autoClose]="true">
        <button (click)="makeMonths()" class="btn btn-primary centreButton"
          ngbDropdownToggle>{{selections.voc.month.name}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <!-- the ngFor buttons -->
          <button *ngFor="let month of months" (click)="selectMonth(month)" ngbDropdownItem>{{month.name}}</button>

        </div>
      </div>
      <!-- nextMonth -->
      <button class="btn btn-primary" (click)="changeMonth(1)"><i class="fas fa-caret-right"></i></button>
    </div>

    <div class="buttonGroup">
      <button class="btn btn-primary" (click)="chooseDepartment(true)" [ngClass]="{ active: selections.voc.isSales }">
        {{ constants.translatedText.Sales }}
      </button>
      <button class="btn btn-primary" (click)="chooseDepartment(false)" [ngClass]="{ active: !selections.voc.isSales }">
        {{ constants.translatedText.Aftersales }}
      </button>
    </div>
  </nav>
</nav>

<div class="content-new">
  
  <div class="content-inner-new">

    <div class="contentInnerSite" *ngIf="siteStatCards">
      <button class="btn btn-primary" id="backButton" (click)="siteStatCards = null"><</button>
      <ng-container>
        <div *ngFor="let card of siteStatCards" class="vocCardContainer">
          <vocCard [measure]="card"></vocCard>
        </div>
      </ng-container>
    </div>

    <div class="tableContainer" *ngIf="!siteStatCards">
      <vocTable *ngIf="selections.voc.sites" (rowClicked)="selectSite($event)"></vocTable>
    </div>

  </div>

</div>