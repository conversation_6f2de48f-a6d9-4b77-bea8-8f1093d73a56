import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { CphPipe } from "src/app/cph.pipe";
import { ServiceSummaryService } from "src/app/pages/serviceSummary/serviceSummary.service";
import { ConstantsService } from "src/app/services/constants.service";
import { RunChaseDataPoint } from "src/app/model/main.model";
import { PartsSummaryService } from "src/app/pages/partsSummary/partsSummary.service";
import { Subscription } from "rxjs";
import { RunChaseTileModalComponent } from "../runChaseTileModal/runChaseTileModal.component";

import { Chart, ChartConfiguration, ChartDataset, ChartDatasetCustomTypesPerDataset, registerables } from 'chart.js';
Chart.register(...registerables);

export interface ChartDataSet {
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  data: number[];
  hoverBackgroundColor: string;
  label: string;
  pointRadius: number;
  tension: number;
}

@Component({
    selector: "runChaseTile",
    template: `
      <div class="dashboard-tile-inner">

        <div *ngIf="!this.customTitle" class="dashboard-tile-header" [ngClass]="{ 'small-header': isSmallCard }">
          {{ constants.translatedText.Trend }} - {{ constants.translatedText.Total }}
          <div *ngIf="dataSource" class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
        </div>

        <div *ngIf="this.customTitle" class="dashboard-tile-header"  [ngClass]="{ 'small-header': isSmallCard }">
        {{ customTitle }} 
        <div *ngIf="dataSource" class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
        </div>

        <div class="dashboard-tile-body" (click)="onClick()">
          <div id="run-chase-chart">
            <canvas #chartCanvas></canvas>
          </div>
        </div>

      </div>

      <runChaseTileModal #runChaseTileModal [data]="data" [page]="customTitle"></runChaseTileModal>
    `,
    styles: [
      `
        #run-chase-chart { width: 90%; margin: 0 auto; height: 90%; cursor: pointer; }
        .dashboard-tile-header.small-card {  }
      `,
    ],
  })

  export class RunChaseTileComponent implements OnInit {
    @Input() page: string;
    @Input() dataSource: string;
    // Below optional inputs only for Spain
    @Input() customTitle?: string;
    @Input() data?: RunChaseDataPoint[];
    @Input() public newDataEmitter: EventEmitter<void>;
    @Input() public isSmallCard: boolean;

    @ViewChild('runChaseTileModal', { static: true }) runChaseTileModal: RunChaseTileModalComponent;
    
    @ViewChild('chartCanvas', { static: true }) chartCanvas: ElementRef;

    subscription: Subscription;

    constructor (
      public constants: ConstantsService,
      public serviceSummaryService: ServiceSummaryService,
      public partsSummaryService: PartsSummaryService,
      public pipe: CphPipe
    ) { }
    
    ngOnInit(): void {
      this.makeChart();

      this.subscription = this.newDataEmitter.subscribe(res => {
        this.makeChart();
      })
    }

    ngOnDestroy() {
      if (!!this.subscription) this.subscription.unsubscribe();
    }
  

    makeChart(): any {
      //console.log(this.data, "this.data!");

      let points: RunChaseDataPoint[] = this.data == null ? this[`${this.page}SummaryService`].runChaseDataPoints : this.data;
      let done: ChartDataSet;
      let target: ChartDataSet;

      done = {
        label: this.constants.translatedText.Done,
        backgroundColor: "rgba(255,255,255,0)",
        borderColor: this.constants.backgroundColours[3],
        borderWidth: 4,
        data: points.map(x => x.DoneCum),
        hoverBackgroundColor: this.constants.actualColour,
        pointRadius: 0,
        tension: 0.1
      }

      target = {
        label: this.constants.translatedText.Target,
        backgroundColor: "rgba(255,255,255,0)",
        borderColor: this.constants.backgroundColours[6],
        borderWidth: 4,
        data: points.map(x => x.TargetCum),
        hoverBackgroundColor: this.constants.backgroundColours[4],
        pointRadius: 0,
        tension: 0.1
      }

      let nonNullDataPoints: number[] = points.map(x => x.DoneCum).filter(x => x !== null);
      let projectedFinishBasedOnActuals: number = this.constants.div(points.map(x => x.DoneCum).length, nonNullDataPoints.length) * nonNullDataPoints[nonNullDataPoints.length - 1] * 1.1;
      let yAxisMax: number = Math.max(projectedFinishBasedOnActuals, points.map(x => x.TargetCum)[points.map(x => x.TargetCum).length - 1]);

      const self = this;

      let config : ChartConfiguration = {
        type: 'line',
        data: {
          labels: points.map(x => this.pipe.transform(x.Label, 'dayAndDayNumber', 0)),
          datasets: [done, target],
        },
        options: {
          maintainAspectRatio: false,
          responsive: true,
          plugins: {
            datalabels: {
              display: false
            },
            legend: { display: false, position: 'bottom' },
            title: {
              display: false,
              text: ''
            },
            tooltip: {
              //title: null,
              enabled: true,
              mode: 'index',
              //fontSize: 12,
              intersect: false,
              callbacks: {
                title: () => null, // Disables the title in the tooltip
                label: function(context) {
                  // Extracting the required information from the context
                  const datasetLabel = context.dataset.label || '';
                  const value = context.parsed.y; // Assuming y-axis value
                  // Transforming the value, assuming 'this.pipe' is properly defined and accessible
                  const transformedValue = self.pipe.transform(value, 'currency', 0);
                  return datasetLabel + ': ' + transformedValue.toString();
                }
              }
            },
          },
          hover: {
            mode: 'nearest',
            intersect: true
          },
          scales: {
            x: 
            {
              display: true,
              ticks: { 
                autoSkip: this.isSmallCard ? true : false, 
                font: {
                  size: 9
                }
              },
              title: {
                display: false,
                text: 'Month'
              }
            },
            y: 
            {
              min: 0,
              max: yAxisMax,
              ticks: {
                font: {
                  size: 12
                },
                callback: (value, index, values) => {
                  return value;
                }
              },
              display: false,
              title: {
                display: false,
                text: 'Value'
              }
            }
          }
        }
      };
  
      let context = this.chartCanvas.nativeElement.getContext('2d');
      return new Chart(context, config);
    }

    onClick()
    {
      if(this.constants.environment.showRunChaseTileModal)
      {
        this.runChaseTileModal.showModal();
      }
    }

  }