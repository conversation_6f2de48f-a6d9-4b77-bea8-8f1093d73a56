import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CphPipe } from "../../cph.pipe";
import { CitNowRegionRollingDataSet, SiteVM } from '../../model/main.model';
import { AutotraderService } from "../../services/autotrader.service";
import { ConstantsService } from "../../services/constants.service";
import { SelectionsService } from "../../services/selections.service";
import { CitNowService } from "./citNow.service";

import { Chart, ChartConfiguration, ChartDataset, ChartDatasetCustomTypesPerDataset, ChartOptions, registerables } from 'chart.js';
Chart.register(...registerables);

@Component({
  selector: "citNowwChart",

  template: `
    <div id="chartHolder">
      <canvas
        #lineChartCanvas
        id="citnowLine"
        width="400"
        height="300"
      ></canvas>
    </div>
  `,
  styles: [
    `
      #chartHolder {
        display: flex;
        width: 100%;
        height:300;
      }
    `,
  ],
})
export class CitNowwChartComponent implements OnInit {

  @Input() public regionDetails: CitNowRegionRollingDataSet
  @ViewChild("lineChartCanvas", { static: true }) lineChartCanvas: ElementRef;

  myChart: Chart;
  borderDashes: number[][];
  lineColours: string[];
  lineChartContext: any;
  subscription: any;

  maxYaxis: number;
  minYaxis: number = 1.2;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public analysis: AutotraderService,
    public router: Router,
    public service: CitNowService
  ) { }

  ngOnInit() {
    this.initParams();
  }

  initParams() {

    this.lineChartContext = this.lineChartCanvas.nativeElement.getContext("2d");

    this.lineColours = [
      "rgb(26, 188, 156)",
      "rgb(52, 152, 219)",
      "rgb(155, 89, 182)",
      "rgb(243, 156, 18)",
      "rgb(231, 76, 60)",
      "rgb(52, 73, 94)",
      "rgb(46, 204, 113)",
    ]

    this.borderDashes = [
      [2, 0],
      [2, 0],
      [2, 0],
      [2, 0],
      [2, 0],
      [2, 0],
      [2, 0],
    ]

    this.subscription = this.service.newDataEmitter.subscribe(res=>{
      this.regionDetails = this.service.regionalChartDetails.find(x=>x.RegionDescription===this.regionDetails.RegionDescription);
      this.refreshChart()
    })

    this.makeChart();

  }

  ngOnDestroy(){
    if(!!this.subscription){this.subscription.unsubscribe();}
 
  }


  makeChart(): void {

    this.myChart = new Chart(this.lineChartContext, {

      type: "line",
      data: {
        labels: this.provideLabels(),
        datasets: this.provideDataSets()
      },

      options: {
        plugins: {
          datalabels: {
            display: false
          },
          tooltip: {
            titleSpacing: 12,
            bodySpacing: 10,
            titleFont: {
              size: this.selections.chartFontsize * 12 / 12, // Set title font size
            },
            bodyFont: {
              size: this.selections.chartFontsize * 12 / 12, // Set body font size
            },
            titleColor: "white", // Title font color
            titleMarginBottom: 5,
            cornerRadius: 5,
            caretSize: 0,
            backgroundColor: "rgba(0,0,0,0.8)", // Tooltip background color
            // If you need to set padding, use the padding option
            padding: {
              x: 12, // xPadding equivalent
              y: 4  // yPadding equivalent
            }
          },
          legend: {
            display: true,
            position: "bottom",
            labels: {
              usePointStyle: true,
            }
          },
          title: {
            display: true,
            text: this.regionDetails.RegionDescription + ' - Seven day rolling average',
            position: 'top',
            font: {
              size: this.selections.chartFontsize * 14 / 12, // Set font size here
            },
            // Additional title options...
          }
        },

        scales: {
          y: {
            min: 0.0,
            max: this.maxYaxis,
            grid: {
              drawOnChartArea: true, // Draws grid lines inside the chart area
              drawTicks: false, // Prevents drawing ticks that extend beyond the chart area, effectively hiding the border
            },
            ticks: {
              callback: (value, index, values) => {
                return this.cphPipe.transform(value, 'percent', 0, false);
              },
              font: {
                size: this.selections.chartFontsize * 11 / 12
              },
              display: true,
            },
          },
          x: {
            min: -1,
            //max: 8,
            offset: true,
            ticks: {
              stepSize: 1,
              maxRotation: 50,
              minRotation: 0,
              autoSkip: false,
              font: {
                size: this.selections.chartFontsize * 9 / 12
              },
            },
            grid: {
              display: false,
            },
          },
        },
      },
    });
  }

  provideLabels(): string[]
  {
    return this.regionDetails.SiteData[0].DailyData.map(x => (new Date(x.Day).getDate()) + "/" + this.constants.sum([new Date(x.Day).getMonth(), 1]))
  }

  provideDataSets(): ChartDataset[]
  {
    let dataSets = []
    
    let rollingDataSets = this.regionDetails.SiteData

    this.maxYaxis = 1.2;

    rollingDataSets.forEach((set, i) => {

        // This will expand the Y axis a bit if there are any scores greater than 120%
        set.DailyData.forEach(x => {

          if(x.VideoSentPercentage > this.minYaxis && x.VideoSentPercentage > this.maxYaxis)
          {
            this.maxYaxis = x.VideoSentPercentage * 1.05;
          }
  
      })

      dataSets.push({
        data: set.DailyData.map(x => x.VideoSentPercentage),
        fill: false,
        lineTension: 0.0,
        borderColor: this.lineColours[i],
        borderDash: this.borderDashes[i],
        pointHitRadius: 20,
        pointHoverBorderWidth: 6,
        pointHoverBackgroundColor: this.lineColours[i],
        label: set.SiteDescription,
        pointStyle: 'line'
      })

    })


    let dataPointsCount = rollingDataSets[0].DailyData.length;

    let targets: number[] = [];

    for (let i = 0; i < dataPointsCount; i++) {
      targets.push(0.90);
    }

    dataSets.push({
      data: targets,
      fill: true,
      lineTension: 0.0,
      backgroundColor: 'hsla(210,13%,40%,0.15)',
      pointBorderWidth: 0,
      pointRadius: 0,
      pointBorderColor: 'rgba(255,255,255,0)',
      label: this.constants.translatedText.Target,
      pointStyle: 'rect',
      borderWidth: 0,
    })

    return dataSets;
  }

  refreshChart(): void {

    if (this.myChart)
    {
      this.myChart.destroy();
    } 

    this.makeChart();
  }

}
