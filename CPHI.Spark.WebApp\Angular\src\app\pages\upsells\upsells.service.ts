import { EventEmitter, Injectable } from '@angular/core';
import { MultiSelectMonth } from 'src/app/components/datePickerMultiSelect/datePickerMultiSelect.component';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { UpsellInit } from './UpsellInit';
import { UpsellsParams } from './UpsellsParams';
import { TopBottomHighlightRule } from 'src/app/model/TopBottomHighlightRule';


@Injectable({
  providedIn: 'root'
})

export class UpsellsService {
  upsells: UpsellInit;
  topBottomHighlights:TopBottomHighlightRule[] = [];

  constructor(
    public getData: GetDataMethodsService,
    public selections: SelectionsService,
    public constants: ConstantsService
  ) { }

  initiateUpsells() {
    if (!this.upsells) {
      let today: Date = this.constants.appStartTime;

      let currentMonth: MultiSelectMonth = {
        startDate: this.constants.deductTimezoneOffset(new Date(today.getFullYear(), today.getMonth(), 1)),
        isSelected: true
      }
      

      this.upsells = {
        months: [currentMonth],
        rowData: null,
        rowDataChangedEmitter: new EventEmitter(),
        siteIds: this.selections.userSiteIds
      }
    }
  }

  getRowData() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.getData.getUpsells(this.getParams()).subscribe((res: any[]) => {
      this.upsells.rowData = res;
    }, e => {
      console.error('Error retrieving Upsells data: ' + JSON.stringify(e));
    }, () => {
      this.upsells.rowDataChangedEmitter.emit(true);
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getParams(): UpsellsParams {
  
    return {
      siteIds: this.upsells.siteIds ? this.upsells.siteIds.toString() : null,
      monthYear: this.constants.formatMonthsForParams(this.upsells.months)
    };

  }

  setDefaultThresholds()
  {
    var barThresholds = {

      keyFobThreshold : {
        good: 150,
        bad: 2
      },
    
      airConDeoThreshold:  {
        good: 150,
        bad: 2
      },
    
      airConSerThreshold:  {
        good: 150,
        bad: 2
      },
    
      engineOilThreshold:  {
        good: 150,
        bad: 2
      },
    
      wiperThreshold:  {
        good: 150,
        bad: 2
      },
    
      allItemsThreshold:  {
        good: 150,
        bad: 2
      }
    }

    return barThresholds;
  }
}
