import { Component, HostListener, Input, OnInit } from '@angular/core';
import { CellClickedEvent, GridApi, RowClickedEvent } from 'ag-grid-community';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../cph.pipe';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from '../../services/agGridMethods.service';
import { AutotraderService } from '../../services/autotrader.service';
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { AlcopaSiteRow } from './alcopaSummary.model';
import { AlcopaService } from './alcopaSummary.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';




@Component({
  selector: 'alcopaTable',
  template: `
    <div id='gridHolder'>

    <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions"></ag-grid-angular>

    <div  id="excelExport" (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>
  </div>
  `,
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
  styles: [
    `
    #gridHolder {
      position: relative;
    }
    ag-grid-angular {
      width: 100%;
      margin: 0em auto;
      max-width: 3000px;
    }
`
  ]
})



export class AlcopaTableComponent implements OnInit {

  @Input() public rowData: AlcopaSiteRow[];
  @Input() public isRegionalTable: boolean;
  @Input() public isPersonTable: boolean;

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  public gridColumnApi;

  mainTableGridOptions: GridOptionsCph;
  subscription: any;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: AlcopaService

  ) {


  }

  ngOnDestroy() { 
    if(!!this.subscription){this.subscription.unsubscribe();}
  }

  ngOnInit() {
    this.initParams();
  }

  initParams() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      domLayout: 'autoHeight',
      animateRows: false,
      onCellMouseOver: (params) => {
        //this.onCellMouseOver(params);
      },
      onRowClicked: (params) => {
        this.onRowClick(params);
      },

      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      rowData: this.provideRowData(),
      onGridReady:(params)=>this.onGridReady(params),
      pinnedBottomRowData: this.provideBottomRowData(),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
    },
      columnDefs: this.provideColDefs(),
      getRowClass: (params) => {
        if (params.data.SiteId == 0) {
          return 'total';
        }
      }

    }
  }



  provideRowData() {
    if (this.isPersonTable) {
      return this.service.peopleRowData
    } else {

      if (this.isRegionalTable) {
        return this.service.sitesRowData.filter(x => x.IsRegion)
      } else {
        return this.service.sitesRowData.filter(x => x.IsSite)
      }
    }
  }

  provideBottomRowData() {
    if (this.isPersonTable) { return [] }
    return this.service.sitesRowData.filter(x => x.IsTotal)
  }


  // SiteId: number;
  // IsSite:boolean;
  // IsRegion:boolean;
  // IsTotal:boolean;
  // SiteDescription: string;
  // RegionDescription: string;
  // Label:string;


  provideColDefs() {
    let cols = [
      { headerName: '', field: this.isPersonTable ? 'PersonName' : 'Label', colId: 'Label', width: 70, type: 'label', },
    ];


    //add column for each day
    let dailyColsIndex: number = 1;
    let daysInMonth = new Date(this.service.chosenMonthStart.getFullYear(), this.service.chosenMonthStart.getMonth() + 1, 0).getDate()

    while (dailyColsIndex <= daysInMonth) {
      let field: string = `Day${dailyColsIndex}`
      cols.push({
        headerName: `${this.constants.translatedText.Day} ${dailyColsIndex}`, field: field, colId: field, width: 65, type: 'number',
      })
      dailyColsIndex++;
    }

    return cols;
  }

  onRowClick(params: RowClickedEvent) {
    if (this.isPersonTable) { return; }

    let siteRow: AlcopaSiteRow = params.data;
    this.service.chooseSiteRow(siteRow);

  }


  onGridReady(params): void {

    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    //this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();
    this.selections.triggerSpinner.next({ show: false });

    this.subscription = this.service.newDataEmitter.subscribe(res => {
      
      this.gridApi.setColumnDefs(this.provideColDefs());
      this.gridApi.setRowData(this.provideRowData());
      this.gridApi.setPinnedBottomRowData(this.provideBottomRowData());
      

      this.gridApi.sizeColumnsToFit();
    })

  }



  private resizeGrid(): void { if (this.gridApi) this.gridApi.sizeColumnsToFit(); }




  // private numberValueFormatting(params): string {
  //   return params && params.value < 0 ? 'badFont  ag-right-aligned-cell' : 'ag-right-aligned-cell'
  // }

  //getMainMenuItems() { return this.gridHelpers.getMainMenuItems() }

  clearHighlighting(colDef: any) { this.gridHelpersService.clearHighlighting(colDef, this.gridApi) }

  removeHighlighting() {
    this.mainTableGridOptions.columnDefs.forEach(colDef => {
      this.clearHighlighting(colDef);
    })
  }


  excelExport(): void {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Alcopa summary', 1, 1);
  }

}
