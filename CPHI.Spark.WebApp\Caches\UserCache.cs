﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Threading.Tasks;
using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Caching.Memory;

namespace CPHI.Spark.WebApp.Caches
{

    public interface IUserCache
    {
        Task<List<int>> GetUserDealerGroupByEmail(string email);
        Task<UserParamSet> GetUserParamSet(int userId, int dealerGroupId);
        Task<UserParamSet> GetUserParamSetByEmail(string email, int dealerGroupId);
        Task<string> GetUsersName(Model.DealerGroupName dealerGroup, int userId);
        Task<Dictionary<string, UserParamSet>> ReLoadUserSiteRoleCache();
    }


    public class UserCache : IUserCache
    {

        private readonly IConfiguration config;
        private readonly IMemoryCache memoryCache;

        public UserCache(IConfiguration config, IMemoryCache memoryCache)
        {
            this.config = config;
            this.memoryCache = memoryCache;
        }

        public async Task<Dictionary<string, UserParamSet>> ReLoadUserSiteRoleCache()
        {
            IEnumerable<UserParamSet> rrgUkResult = new List<UserParamSet>();
            IEnumerable<UserParamSet> vindisResult = new List<UserParamSet>();
            IEnumerable<UserParamSet> rrgSpainResult = new List<UserParamSet>();
            IEnumerable<UserParamSet> autoPriceResult = new List<UserParamSet>();

            string rrgConnString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(Model.DealerGroupName.RRGUK));
            string vindisConnString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(Model.DealerGroupName.Vindis));
            string rrgSpainConnString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(Model.DealerGroupName.RRGSpain));
            string autoPriceConnString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(Model.DealerGroupName.V12));

            var rrgUserDataAccess = new UserDataAccess(rrgConnString);
            var vindisUserDataAccess = new UserDataAccess(vindisConnString);
            var rrgSpainUserDataAccess = new UserDataAccess(rrgSpainConnString);
            var autoPriceUserDataAccess = new UserDataAccess(autoPriceConnString);

            rrgUkResult = await rrgUserDataAccess.GetUserSiteRole();
            vindisResult = await vindisUserDataAccess.GetUserSiteRole();
            rrgSpainResult = await rrgSpainUserDataAccess.GetUserSiteRole();
            autoPriceResult = await autoPriceUserDataAccess.GetUserSiteRole();

            IEnumerable<UserParamSet> allResult = rrgUkResult.Concat(vindisResult).Concat(rrgSpainResult).Concat(autoPriceResult);

            var cacheOptions = new MemoryCacheEntryOptions()
                .SetSize(1) // Total number of the objects that can be cached
                .SetAbsoluteExpiration(DateTime.UtcNow.AddDays(1));
            var userSiteRoleDictionary = allResult.ToDictionary(u => $"{u.DealerGroupId}|{u.UserName}", u => u);
            memoryCache.Set("UserSiteRole", userSiteRoleDictionary, cacheOptions);
            return userSiteRoleDictionary;
        }




        public async Task<UserParamSet> GetUserParamSetByEmail(string email, int dealerGroupId)
        {
            Dictionary<string, UserParamSet> userSiteRoleDictionary = await GetUserSiteRoleDictionary();

            UserParamSet paramSet = userSiteRoleDictionary.FirstOrDefault(u => u.Value.Email.Equals(email, StringComparison.OrdinalIgnoreCase) && u.Value.DealerGroupId.Equals(dealerGroupId)).Value;

            if (paramSet == null)
            {
                throw new Exception($"User {email} not found");
            }

            if (paramSet.RoleName == "Missing Role")
            {
                throw new Exception($"User {email} has not been assigned a role");
            }

            return paramSet;
        }


        public async Task<List<int>> GetUserDealerGroupByEmail(string email)
        {
            Dictionary<string, UserParamSet> userSiteRoleDictionary = await GetUserSiteRoleDictionary();

            var paramSets = userSiteRoleDictionary.Where(u => u.Value.Email.Equals(email, StringComparison.OrdinalIgnoreCase)).Select(s => s.Value).Select(d => d.DealerGroupId).ToList();

            if (paramSets == null)
            {
                throw new Exception($"User {email} not found");
            }
            return paramSets;
        }

        public async Task<UserParamSet> GetUserParamSet(int userId, int dealerGroupId)
        {
            Dictionary<string, UserParamSet> userSiteRoleDictionary = await GetUserSiteRoleDictionary();

            UserParamSet paramSet = userSiteRoleDictionary.FirstOrDefault(u => u.Value.UserId.Equals(userId) && u.Value.DealerGroupId.Equals(dealerGroupId)).Value;

            if (paramSet == null)
            {
                throw new Exception($"User {userId} not found");
            }

            if (paramSet.RoleName == "Missing Role")
            {
                throw new Exception($"User {userId} has not been assigned a role");
            }

            return paramSet;
        }


        public async Task<string> GetUsersName(Model.DealerGroupName dealerGroup, int userId)
        {
            Dictionary<string, UserParamSet> dictionary = await GetUserSiteRoleDictionary();

            // Assuming the dictionary values are of type UserParamSet
            var matchingUserParamSet = dictionary.Values
                .FirstOrDefault(x => x.DealerGroupId == (int)dealerGroup && x.UserId == userId);

            // If a match is found, return the Name, otherwise return a default value (e.g., null or an empty string)
            if (matchingUserParamSet != null)
            {
                return matchingUserParamSet?.Name;
            }
            else
            {
                throw new Exception("Users name not found");
            }
        }

        private async Task<Dictionary<string, UserParamSet>> GetUserSiteRoleDictionary()
        {

            if (!memoryCache.TryGetValue("UserSiteRole", out Dictionary<string, UserParamSet> cachedUserSiteRoleDictionary))
            {
                //Get fresh data from DB
                cachedUserSiteRoleDictionary = await ReLoadUserSiteRoleCache();
                return cachedUserSiteRoleDictionary;
            }

            return cachedUserSiteRoleDictionary;
        }


    }
}
