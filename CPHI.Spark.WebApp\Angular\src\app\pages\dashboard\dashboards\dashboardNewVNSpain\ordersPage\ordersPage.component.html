<!-- Main Page -->
<div class="dashboard-grid-container" *ngIf="!!service.rawDataHighlighted">
    <div id="spain-overview-grid" class="dashboard-grid cols-6 rows-7">

        <!-- Total tile -->
        <div class="dashboard-tile grid-col-1-2 grid-row-1-2 ">

            <div class="tileHeader ">
                <div class="headerWords">
                    <h4> {{constants.capitalise(constants.translatedText.Orders)}}
                    </h4>
                </div>

                <div class="interactionIconsHolder">
                    <!-- Icon that shows if you are filtering measures on this tile -->
                    <div *ngIf="service.isFiltersOn()" class="cancelFilterHolder clickable" (click)="service.clearFilters()">
                        <i class="fas fa-filter"></i>
                    </div>
                
                    <!-- Icon that shows if you are highlighting measures on this tile -->
                    <div *ngIf="service.isHighlightFiltersOn()" class="cancelHighlightsHolder clickable" (click)="service.clearHighlights()">
                        <i class="fas fa-highlighter"></i>
                    </div>
                </div>
            </div>

            <div class="contentsHolder ">
                <h1 class="bigNumber clickable" (click)="service.navigateToDistrinetPage()" id="totalCount">
                    <strong>{{service.rawDataHighlighted.length|cph:'number':0}}</strong>
                </h1>
            </div>

        </div>

        <!-- Cancellations tile -->
        <div class="dashboard-tile grid-col-2-3 grid-row-1-2">

            <div class="tileHeader ">
                <div class="headerWords">
                    <h4>{{constants.translatedText.Cancellations}}
                    </h4>
                </div>
            </div>

            <div class="contentsHolder">
                <h1 class="bigNumber" id="cancellations"><strong>{{cancellations()}}</strong></h1>
            </div>
        </div>

        <!-- Net total tile -->
        <div class="dashboard-tile grid-col-3-4 grid-row-1-2 ">

            <div class="tileHeader ">
                <div class="headerWords">
                    <h4>{{constants.translatedText.NetOrders}}
                    </h4>
                </div>
            </div>

            <div class="contentsHolder">
                <h1 class="bigNumber" id="totalCount"><strong>{{service.rawDataHighlighted.length -
                    cancellations()|cph:'number':0}}</strong></h1>
            </div>
        </div>

        <!-- Cancellations% tile -->
        <div class="dashboard-tile grid-col-4-5 grid-row-1-2 ">

            <div class="tileHeader ">
                <div class="headerWords">
                    <h4>% {{constants.translatedText.Cancellations}}
                    </h4>
                </div>
            </div>

            <div class="contentsHolder">
                <h1 class="bigNumber" id="cancellations"><strong>{{cancellations() /
                    service.rawDataHighlighted.length|cph:'percent':1}}</strong></h1>
            </div>
        </div>



        <!-- SiteDescription  -->
        <div class="dashboard-tile grid-col-1-3 grid-row-2-6 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Site" [tileType]="'VerticalBar'" [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'SiteDescription'">
            </biChartTile>
        </div>

        <!-- Make  -->
        <div class="dashboard-tile grid-col-3-4 grid-row-2-5 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Brand" [tileType]="'DonutChart'" [fieldName]="'Franchise'" 
                [pageParams]="service.getPageParams()">
            </biChartTile>
        </div>

        <!-- Advance  -->
        <div class="dashboard-tile grid-col-4-5 grid-row-2-6 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_Advance" [tileType]="'VerticalBarPercent'"  [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'Advance'" [customSort]="'Advance'">
            </biChartTile>
        </div>

        <!-- ModelCode  -->
        <div class="dashboard-tile grid-col-5-8 grid-row-1-4 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_ModelCode" [tileType]="'VerticalBar'"  [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'ModelCode'"></biChartTile>
        </div>

        <!-- CustomerType  -->
        <div class="dashboard-tile grid-col-5-8 grid-row-4-6 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_CustomerType" [tileType]="'HorizontalBar'" 
                [pageParams]="service.getPageParams()" [fieldName]="'CustomerType'"></biChartTile>
        </div>

        <!-- RegionDescription  -->
        <div class="dashboard-tile grid-col-3-4 grid-row-5-6 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Region" [tileType]="'VerticalBar'"  [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'RegionDescription'"></biChartTile>
        </div>

        <!-- EnergyType  -->
        <div class="dashboard-tile grid-col-1-3 grid-row-6-8 ">
            <biChartTile [dataType]="dataTypes.label" [title]="constants.translatedText.Distrinet_EnergyType" [tileType]="'VerticalBar'"  [labelWidth]="30"
                [pageParams]="service.getPageParams()" [fieldName]="'EnergyType'"></biChartTile>
        </div>

        <!-- OrderDate  -->
        <div class="dashboard-tile grid-col-3-8 grid-row-6-8 ">
            <biChartTile [dataType]="dataTypes.weekly"   [title]="constants.translatedText.OrderDate" [tileType]="'HorizontalBar'" 
                [pageParams]="service.getPageParams()" [fieldName]="'OrderDate'"></biChartTile>
        </div>