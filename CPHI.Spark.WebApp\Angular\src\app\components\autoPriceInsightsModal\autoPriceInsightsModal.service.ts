import {EventEmitter, Injectable} from '@angular/core';
import {GetLocationOptimiserAdvertsParams} from 'src/app/model/GetLocationOptimiserAdvertsParams';
import {LocationOptimiserAdvert} from 'src/app/model/LocationOptimiserAdvert';
import {VehicleDetailModalItem, VehicleDetailModalItemDTO} from "src/app/model/VehicleDetailModalItem";
import {LocationOptimiserService} from 'src/app/pages/autoprice/locationOptimiser/locationOptimiser.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {CompetitorAnalysisComponent} from '../competitorAnalysis/competitorAnalysis.component';
import {CompetitorAnalysisParams} from '../competitorAnalysis/CompetitorAnalysisParams';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {PricingChartComponent} from './pricingChart/pricingChart.component';
import {SearchAdvertViewsChartComponent} from './searchAdvertViewsChart/searchAdvertViewsChart.component';
import {SiteTransferComponent} from './siteTransfer/siteTransfer.component';
import {CphPipe} from 'src/app/cph.pipe';
import {PriceBoardsService} from 'src/app/services/priceBoards.service';
import {CompetitorAnalysisService} from '../competitorAnalysis/competitorAnalysis.service';
import {VehicleSpecOption} from 'src/app/model/VehicleSpecOption.model';
import {VehicleHistoryItem} from 'src/app/model/VehicleHistoryItem';
import {GetVehicleHistoryParams} from 'src/app/model/GetVehicleHistoryParams';
import {LeavingVehicleItem} from 'src/app/model/LeavingVehicleItem';
import {GetLeavingVehicleItemsForModalParams} from 'src/app/model/GetLeavingVehicleItemsForModalParams';
import {SameModelAdvert} from 'src/app/model/SameModelAdvert';
import {GetSameModelAdvertsParams} from 'src/app/model/GetSameModelAdvertsParams';
import {StockLevelAndCover} from 'src/app/model/StockLevelAndCover';

@Injectable({
   providedIn: 'root'
})
export class AutoPriceInsightsModalService {
   modalItem: VehicleDetailModalItem;
   locationOptimiserAdverts: LocationOptimiserAdvert[];
   costPerMile: number = 0.2;
   flatCostPerMove: number = 50;
   redrawRowsEmitter: EventEmitter<LocationOptimiserAdvert[]> = new EventEmitter<LocationOptimiserAdvert[]>();

   pricingStatusSliderTrigger: EventEmitter<void> = new EventEmitter();


   vehicleOptions: VehicleSpecOption[];
   selectedOptions: VehicleSpecOption[];

   //competitorAnalysisParams: CompetitorAnalysisParams;
   public adId: number;
   public allAdIds: number[];
   currentAdIndex: number;
   pricingChartRef: PricingChartComponent;
   searchAdViewsChartRef: SearchAdvertViewsChartComponent;
   competitorAnalysisRef: CompetitorAnalysisComponent;
   siteTransferTileRef: SiteTransferComponent;

   // For new x4 tabes
   vehicleHistoryItems: VehicleHistoryItem [];
   recentlySoldThisModel: LeavingVehicleItem [];

   sameModelAdverts: SameModelAdvert[]; // only multi-site
   stockLevelAndCover: StockLevelAndCover[] // only multi-site

   get competitorListCount() {
      return this.competitorAnalysisService.params.CompetitorSummary.CompetitorVehicleCount;
   }

   isSmallScreen: boolean = false;
   modalView: 'overview' | 'valuation' | 'transfer' | 'priceBoard' | 'testStrategy' | 'historicPrices' | 'stockCoverBySite' | 'stockInGroup' | 'lastSixMonths' | 'vehicleHistory' | 'competitorAnalysis' = 'overview';

   constructor(
      public getDataService: GetDataMethodsService,
      public selectionsService: SelectionsService,
      public locationOptimiserService: LocationOptimiserService,
      public modalService: NgbModal,
      private cphPipe: CphPipe,
      private priceBoardsService: PriceBoardsService,
      private competitorAnalysisService: CompetitorAnalysisService
   ) {

   }

   initialise(adId: number, allAdIds: number[]) {
      this.adId = adId;
      this.allAdIds = allAdIds;
   }

   setAdIndex() {
      if(this.allAdIds){
         this.currentAdIndex = this.allAdIds.findIndex(x => x === this.adId);
      }
   }

   getLocationOptimiserAdverts(advertId: number) {
      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading data...'});

    const params: GetLocationOptimiserAdvertsParams = {
      advertId: advertId,
      includeNewVehicles: true
    };
    this.getDataService.getLocationOptimiserAdverts(params).subscribe((res: LocationOptimiserAdvert[]) => {
      this.locationOptimiserService.costUpMoves(res, this.costPerMile, this.flatCostPerMove);
      this.locationOptimiserAdverts = res;
      this.selectionsService.triggerSpinner.emit({show: false});
    }, (error: any) => {
      this.selectionsService.triggerSpinner.next({show: false});
      console.error('Failed to retrieve location optimiser summary data', error);
    });
  }

   getVehicleHistory() {

      this.vehicleHistoryItems = null;

      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading data...'});

      let mileage: number = this.modalItem?.AdvertDetail.OdometerReading;

      const params: GetVehicleHistoryParams = {
         reg: this.modalItem.AdvertDetail.VehicleReg,
         mileage: mileage
      };

      this.getDataService.getVehicleHistory(params).subscribe((res: VehicleHistoryItem[]) => {

         this.vehicleHistoryItems = res;
         this.selectionsService.triggerSpinner.next({show: false});

      }, (error: any) => {
         this.selectionsService.triggerSpinner.next({show: false});
         console.error('Failed to retrieve location optimiser summary data', error);
      });


   }


   getLastSixMonths() {

      let model: string = this.modalItem?.AdvertDetail.Model;

      this.recentlySoldThisModel = null;

      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading data...'});

      const params: GetLeavingVehicleItemsForModalParams = {
         model: model
      };

      this.getDataService.getLeavingItemsLastSixMonths(params).subscribe((res: LeavingVehicleItem[]) => {

         this.recentlySoldThisModel = res;
         this.selectionsService.triggerSpinner.next({show: false});

      }, (error: any) => {
         this.selectionsService.triggerSpinner.next({show: false});
         console.error('Failed to retrieve location optimiser summary data', error);
      });


   }

   getSameModelAdverts() {

      let model: string = this.modalItem?.AdvertDetail.Model;

      this.sameModelAdverts = null;

      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading data...'});

      const params: GetSameModelAdvertsParams = {
         model: model
      };

      this.getDataService.getSameModelAdverts(params).subscribe((res: SameModelAdvert[]) => {

         this.sameModelAdverts = res;
         this.selectionsService.triggerSpinner.next({show: false});

      }, (error: any) => {
         this.selectionsService.triggerSpinner.next({show: false});
         console.error('Failed to retrieve location optimiser summary data', error);
      });

   }

   getStockCover() {

      let model: string = this.modalItem?.AdvertDetail.Model;

      this.stockLevelAndCover = null;

      this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading data...'});

      const params: GetSameModelAdvertsParams = {
         model: model
      };

      this.getDataService.getStockCover(params).subscribe((res: StockLevelAndCover[]) => {

         this.stockLevelAndCover = res;
         this.selectionsService.triggerSpinner.next({show: false});

      }, (error: any) => {
         this.selectionsService.triggerSpinner.next({show: false});
         console.error('Failed to retrieve location optimiser summary data', error);
      });

   }


   buildCompetitorAnalysisParams(): CompetitorAnalysisParams {
      const params: CompetitorAnalysisParams = {
         ParentType: 'insightsModal',
         RetailerSiteIdForPostcode: this.modalItem.RetailerSiteIdForPostcode,
         Make: this.modalItem.AdvertDetail.Make,
         Model: this.modalItem.AdvertDetail.Model,
         Trim: this.modalItem.AdvertDetail.Trim,
         TransmissionType: this.modalItem.AdvertDetail.TransmissionType,
         FuelType: this.modalItem.AdvertDetail.FuelType,
         BodyType: this.modalItem.AdvertDetail.BodyType,
         Drivetrain: this.modalItem.AdvertDetail.Drivetrain,
         Doors: this.modalItem.AdvertDetail.Doors,
         AdvertDetail: this.modalItem.AdvertDetail,
         AdvertId: this.modalItem.AdvertDetail.AdId,
         DerivativeId: this.modalItem.AdvertDetail.DerivativeId,
         WebSiteSearchIdentifier: this.modalItem.AdvertDetail.WebSiteSearchIdentifier,
         ImageURL: this.modalItem.AdvertDetail.ImageUrl,
         RetailerSiteRetailerId: this.modalItem.AdvertDetail.RetailerSiteRetailerId,
         VehicleReg: this.modalItem.AdvertDetail.VehicleReg,
         OdometerReading: this.modalItem.AdvertDetail.OdometerReading,
         FirstRegisteredDate: this.modalItem.AdvertDetail.FirstRegisteredDate,
         AdvertisedPrice: this.modalItem.AdvertDetail.AdvertisedPrice,
         PricePosition: this.modalItem.AdvertDetail.ValuationAdjRetail != 0 ? this.modalItem.AdvertDetail.AdvertisedPrice / this.modalItem.AdvertDetail.ValuationAdjRetail : 0,
         Valuation: this.modalItem.AdvertDetail.ValuationAdjRetail,// ValuationMktAvRetail,
         CompetitorSummary: this.modalItem.CompetitorSummary
      }
      //console.log(params);
      return params
   }


  async getData() {
    await this.getDataWithAdId(this.adId);
  }

  getDataWithAdId(adId: number): Promise<VehicleDetailModalItemDTO> {

    this.selectionsService.triggerSpinner.emit({show: true, message: 'Loading vehicle...'});

    return new Promise((resolve, reject) => {
      this.getDataService.getAutoPriceInsightModalForAdId(adId).subscribe(
        (res: VehicleDetailModalItemDTO) => {
          this.selectionsService.triggerSpinner.emit({show: false});
          this.doPostGetDataActions(res);
          resolve(res);
        },
        (error) => {
          console.error('Failed to load modal data', error);
          this.selectionsService.triggerSpinner.emit({show: false});
          reject(error);
        }
      );
    });
  }

   private doPostGetDataActions(res: VehicleDetailModalItemDTO) {

    this.modalItem = new VehicleDetailModalItem(res);

    if (this.modalItem.HasLeft == true) {
      this.modalItem.CompetitorSummary.CompetitorVehicles = this.modalItem.CompetitorSummary.CompetitorVehicles.filter(x => !x.IsOurVehicle);
      this.modalItem.CompetitorSummary.CompetitorVehicleCount = this.modalItem.CompetitorSummary.CompetitorVehicles.length;
    }

    this.competitorAnalysisService.params$.next(this.buildCompetitorAnalysisParams());

      this.adId = this.modalItem.AdvertDetail.AdId;
      this.locationOptimiserService.costUpMoves(res.LocationChanges, 0.2, 50);

      if (this.pricingChartRef) {
         this.pricingChartRef.drawPricingChart();
         this.pricingChartRef.createSearchMatchesChart();
         this.pricingChartRef.createAdvertViewsChart();
         this.pricingChartRef.createLeadsScoreChart();
         this.pricingChartRef.createAdvertPerformanceChart();
      }

      if (this.searchAdViewsChartRef) {
         this.searchAdViewsChartRef.drawCharts();
      }

      if (this.competitorAnalysisRef && !this.competitorAnalysisRef.showBlobs) {
         this.competitorAnalysisRef.redrawTable(this.modalItem.CompetitorSummary, this.competitorAnalysisService.params)
      }

      if (this.competitorAnalysisRef?.service?.chartRef && this.competitorAnalysisRef.showBlobs) {
         this.competitorAnalysisRef.service.chartRef.dealWithNewData(this.competitorAnalysisRef.service.params.CompetitorSummary)
      }

      if (this.siteTransferTileRef) {
         this.siteTransferTileRef.dealWithNewData(this.modalItem.LocationChanges)
      }

      this.priceBoardsService.generatePriceBoard(this.modalItem);


      setTimeout(() => {
         this.modalItem.AdvertDetail.showVsStrategyLozenge = true;
      }, 40) //some truly bizarre bug where if you don't do this the icons all show at once when you change strategy banding


      this.selectionsService.triggerSpinner.emit({show: false});
   }


   nextAdIsActive() {
      if (!this.allAdIds) {
         return false
      }
      if (this.allAdIds.length === 0) {
         return false
      }
      return this.currentAdIndex < this.allAdIds.length - 1
   }

   previousAdIdIsActive() {
      if (!this.allAdIds) {
         return false
      }
      if (this.allAdIds.length === 0) {
         return false
      }
      return this.currentAdIndex > 0
   }

   goToNextAd() {
      this.adId = this.allAdIds[this.currentAdIndex + 1];
      this.currentAdIndex++;
      this.getData();
   }

   goToPreviousId() {
      this.adId = this.allAdIds[this.currentAdIndex - 1];
      this.currentAdIndex--;
      this.getData();
   }


}
