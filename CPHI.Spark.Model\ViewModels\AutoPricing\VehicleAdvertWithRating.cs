﻿using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.autoPricing;
using CPHI.Spark.Model.Services;
using System;
using System.Collections.Generic;
using System.Reflection.Metadata.Ecma335;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{

   public class VehicleAdvertWithRating
   {

      public VehicleAdvertWithRating(VehicleAdvertWithRating existing, bool useTestStrategy)
      {
         AdId = existing.AdId;
         SnapshotId = existing.SnapshotId;
         RetailerSiteName = existing.RetailerSiteName;
         RetailerSiteId = existing.RetailerSiteId;
         SiteId = existing.SiteId;
         RetailerSiteRetailerId = existing.RetailerSiteRetailerId;
         RegionName = existing.RegionName;
         VehicleReg = existing.VehicleReg;
         Chassis = existing.Chassis;
         StockNumber = existing.StockNumber;
         RetailerIdentifier = existing.RetailerIdentifier;
         WebSiteStockIdentifier = existing.WebSiteStockIdentifier;
         WebSiteSearchIdentifier = existing.WebSiteSearchIdentifier;
         Make = existing.Make;
         Model = existing.Model;
         Drivetrain = existing.Drivetrain;
         Doors = existing.Doors;
         Derivative = existing.Derivative;
         DerivativeId = existing.DerivativeId;
         VehicleType = existing.VehicleType;
         Trim = existing.Trim;
         BodyType = existing.BodyType;
         FuelType = existing.FuelType;
         BadgeEngineSizeLitres = existing.BadgeEngineSizeLitres;
         EnginePowerBHP = existing.EnginePowerBHP;
         TransmissionType = existing.TransmissionType;
         OdometerReading = existing.OdometerReading;
         FirstRegisteredDate = existing.FirstRegisteredDate;
         Colour = existing.Colour;
         SpecificColour = existing.SpecificColour;
         DateOnForecourt = existing.DateOnForecourt;
         CreatedInSparkDate = existing.CreatedInSparkDate;
         AttentionGrabber = existing.AttentionGrabber;
         ForecourtPrice = existing.ForecourtPrice;
         AdminFee = existing.AdminFee;
         AdvertisedPrice = existing.AdvertisedPrice;
         SIV = existing.SIV;
         OriginalPurchasePrice = existing.OriginalPurchasePrice;
         IncludingVat = existing.IncludingVat;
         PriceIndicatorRatingAtCurrentSelling = existing.PriceIndicatorRatingAtCurrentSelling;
         DaysToSellAtCurrentSelling = existing.DaysToSellAtCurrentSelling;
         NationalRetailDaysToSell = existing.NationalRetailDaysToSell;
         ValuationMktAvRetail = existing.ValuationMktAvRetail;
         ValuationMktAvPartEx = existing.ValuationMktAvPartEx;
         ValuationMktAvPrivate = existing.ValuationMktAvPrivate;
         ValuationMktAvRetailExVat = existing.ValuationMktAvRetailExVat;
         ValuationMktTrade = existing.ValuationMktTrade;
         ValuationAdjRetail = existing.ValuationAdjRetail;
         ValuationAdjPartEx = existing.ValuationAdjPartEx;
         ValuationAdjPrivate = existing.ValuationAdjPrivate;
         ValuationAdjRetailExVat = existing.ValuationAdjRetailExVat;
         ValuationAdjTrade = existing.ValuationAdjTrade;
         //RelevantValuation = existing.RelevantValuation;
         RetailRating = existing.RetailRating;
         NationalRetailRating = existing.NationalRetailRating;
         PerfRatingScore = existing.PerfRatingScore;
         PerfRating = existing.PerfRating;
         SearchViewsYest = existing.SearchViewsYest;
         AdvertViewsYest = existing.AdvertViewsYest;
         SearchViews7Day = existing.SearchViews7Day;
         AdvertViews7Day = existing.AdvertViews7Day;
         RetailDemand = existing.RetailDemand;
         RetailSupply = existing.RetailSupply;
         RetailMarketCondition = existing.RetailMarketCondition;
         NationalRetailMarketCondition = existing.NationalRetailMarketCondition;
         StockItemId = existing.StockItemId;
         ImageURL = existing.ImageURL;
         AllImageURLs = existing.AllImageURLs;
         ImagesCount = existing.ImagesCount;
         IsMissingImages = existing.IsMissingImages;
         NoVideo = existing.NoVideo;
         NoAttentionGrabber = existing.NoAttentionGrabber;
         IsLowQuality = existing.IsLowQuality;
         TestStrategyPrice = existing.TestStrategyPrice;
         VsTestStrategyBanding = existing.VsTestStrategyBanding;
         StrategyPriceHasBeenCalculated = existing.StrategyPriceHasBeenCalculated;
         LastCommentName = existing.LastCommentName;
         LastCommentText = existing.LastCommentText;
         CurrentStrategyName = existing.CurrentStrategyName;
         ActualEndDate = existing.ActualEndDate;

         // Apply strategy conditionally
         if (useTestStrategy && existing.TestStrategyPrice > 0)
         {
            StrategyPrice = existing.TestStrategyPrice;
            VsStrategyBanding = existing.VsTestStrategyBanding;
         }
         else
         {
            StrategyPrice = existing.StrategyPrice;
            VsStrategyBanding = existing.VsStrategyBanding;
         }

         CompetitorCount = existing.CompetitorCount;
         OurPPRank = existing.OurPPRank;
         OurValueRank = existing.OurValueRank;
         CheapestSellerName = existing.CheapestSellerName;
         CheapestSellerType = existing.CheapestSellerType;
         CheapestVehicle = existing.CheapestVehicle;
         OnlyVehicle = existing.OnlyVehicle;
         AllPrices = existing.AllPrices;


         //update for these new 4:
         PPAverageFranchised = existing.PPAverageFranchised;
         PPAverageIndependents = existing.PPAverageIndependents;
         PPAverageSupermarkets = existing.PPAverageSupermarkets;
         PPAveragePrivates = existing.PPAveragePrivates;


         PriceUpMaintainRank = existing.PriceUpMaintainRank;
         PriceDownImproveRank = existing.PriceDownImproveRank;
         PriceToBeCheapest = existing.PriceToBeCheapest;
         DMSSellingPrice = existing.DMSSellingPrice;
         ValuationMonthPlus1 = existing.ValuationMonthPlus1;
         ValuationMonthPlus2 = existing.ValuationMonthPlus2;
         ValuationMonthPlus3 = existing.ValuationMonthPlus3;
         EngineCapacityCC = existing.EngineCapacityCC;
         PhysicalLocation = existing.PhysicalLocation;
         DailySearchViewsLast7 = existing.DailySearchViewsLast7;
         DailyAdvertViewsLast7 = existing.DailyAdvertViewsLast7;
         SiteBrand = existing.SiteBrand;
         SimpleBrand = existing.SimpleBrand;
         PrepCost = existing.PrepCost;
         StockSource = existing.StockSource;
         LastPriceChangeValue = existing.LastPriceChangeValue;
         WhenPriceLastManuallyChanged = existing.WhenPriceLastManuallyChanged;
         WhoLastManuallyChanged = existing.WhoLastManuallyChanged;
         TotalManualPriceChangesCount = existing.TotalManualPriceChangesCount;
         TotalManualPriceChangesValue = existing.TotalManualPriceChangesValue;
         IsVatQ = existing.IsVatQ;
         PricedProfit = existing.PricedProfit;
         VehicleTypeDesc = existing.VehicleTypeDesc;
         StockPrefix = existing.StockPrefix;
         LifecycleStatus = existing.LifecycleStatus;
         AdvertiserAdvertStatus = existing.AdvertiserAdvertStatus;
         OwnershipCondition = existing.OwnershipCondition;
         AutotraderAdvertStatus = existing.AutotraderAdvertStatus;
         VehicleHasOptionsSpecified = existing.VehicleHasOptionsSpecified;
         PortalOptions = existing.PortalOptions;
         DaysToSellAtNewPrice = existing.DaysToSellAtNewPrice;
         PriceIndicatorAtNewPrice = existing.PriceIndicatorAtNewPrice;
         NewPrice = existing.NewPrice;
         IsSmallPriceChange = existing.IsSmallPriceChange;
         IsKeyChange = existing.IsKeyChange;
         FirstPrice = existing.FirstPrice;
         DailyPriceMovesCount = existing.DailyPriceMovesCount;
         MostRecentDailyPriceMove = existing.MostRecentDailyPriceMove;
         MostRecentDailyPriceMoveDate = existing.MostRecentDailyPriceMoveDate;
         DaysSinceMostRecentPriceMove = existing.DaysSinceMostRecentPriceMove;
         ModelSellRate = existing.ModelSellRate;

         OptedOutBy = existing.OptedOutBy;
         WhenOptedOut = existing.WhenOptedOut;
         OptedOutUntil = existing.OptedOutUntil;
         AveragePP = existing.AveragePP;
         HighestPP = existing.HighestPP;
         LowestPP = existing.LowestPP;
         StockDate = existing.StockDate;
         DateBookedIn = existing.DateBookedIn;
         Owners = existing.Owners;
         RetailerType = existing.RetailerType;
         BcaVin = existing.BcaVin;
         BcaMileage = existing.BcaMileage;
         DaysOnAllSites = existing.DaysOnAllSites;
         NumberOfPreviousSales = existing.NumberOfPreviousSales;
         V5Status = existing.V5Status;
         ServiceHistory = existing.ServiceHistory;
         Runner = existing.Runner;
         SalesComment = existing.SalesComment;
         HoldDate = existing.HoldDate;
         HoldCode = existing.HoldCode;
         HoldDescription = existing.HoldDescription;
      }


      public VehicleAdvertWithRating() { }


      public int AdId { get; set; }
      public int SnapshotId { get; set; }
      public string RetailerSiteName { get; set; }
      public int RetailerSiteId { get; set; }
      public int SiteId { get; set; }
      public int RetailerSiteRetailerId { get; set; }
      public string RegionName { get; set; }
      public string VehicleReg { get; set; }
      public string Chassis { get; set; }
      public string StockNumber { get; set; }
      public int RetailerIdentifier { get; set; }
      public string WebSiteStockIdentifier { get; set; }
      public string WebSiteSearchIdentifier { get; set; }
      public string Make { get; set; }
      public string Model { get; set; }
      public string Drivetrain { get; set; }
      public int Doors { get; set; }
      public string Derivative { get; set; }
      public string DerivativeId { get; set; }
      public string VehicleType { get; set; }
      public string Trim { get; set; }
      public string BodyType { get; set; }
      public string FuelType { get; set; }
      public string BadgeEngineSizeLitres { get; set; }
      public string EnginePowerBHP { get; set; }
      public string TransmissionType { get; set; }
      public int? OdometerReading { get; set; }
      public DateTime? FirstRegisteredDate { get; set; }
      public string Colour { get; set; }
      public string SpecificColour { get; set; }


      public DateTime? DateOnForecourt { get; set; }
      public DateTime CreatedInSparkDate { get; set; }
      public string AttentionGrabber { get; set; }
      public int ForecourtPrice { get; set; }
      public int AdminFee { get; set; }
      public int AdvertisedPrice { get; set; }
      public int? SIV { get; set; }
      public int OriginalPurchasePrice { get; set; }
      public bool IncludingVat { get; set; }
      public string PriceIndicatorRatingAtCurrentSelling { get; set; }
      public decimal? DaysToSellAtCurrentSelling { get; set; }
      public decimal? NationalRetailDaysToSell { get; set; }
      public int? ValuationMktAvRetail { get; set; }
      public int? ValuationMktAvPartEx { get; set; }
      public int? ValuationMktAvPrivate { get; set; }
      public int? ValuationMktAvRetailExVat { get; set; }
      public int? ValuationMktTrade { get; set; }
      public int? ValuationAdjRetail { get; set; }
      public int? ValuationAdjPartEx { get; set; }
      public int? ValuationAdjPrivate { get; set; }
      public int? ValuationAdjRetailExVat { get; set; }
      public int? ValuationAdjTrade { get; set; }

      public int RelevantValuation {         get => GetRelevantRetailValuationAdj;      }
         


      public decimal? RetailRating { get; set; }
      public decimal? NationalRetailRating { get; set; }
      public decimal? PerfRatingScore { get; set; }
      public string PerfRating { get; set; }
      public int? SearchViewsYest { get; set; }
      public int? AdvertViewsYest { get; set; }
      public int? SearchViews7Day { get; set; }
      public int? AdvertViews7Day { get; set; }

      public decimal RetailDemand { get; set; }
      public decimal RetailSupply { get; set; }
      public decimal RetailMarketCondition { get; set; }

      public decimal NationalRetailMarketCondition { get; set; }
      public int? StockItemId { get; set; }

      public string ImageURL { get; set; }
      public string AllImageURLs { get; set; }
      public int ImagesCount { get; set; }


      //new
      public bool IsMissingImages { get; set; }
      public bool NoVideo { get; set; }


      public bool NoAttentionGrabber { get; set; }
      public bool IsLowQuality { get; set; }
      public bool StrategyPriceHasBeenCalculated { get; set; }
      public decimal StrategyPrice { get; set; }
      public decimal StrategyPricePP { get => (ValuationAdjRetail > 0 && StrategyPrice > 0) ? (decimal)StrategyPrice / (decimal)ValuationAdjRetail : 0; }
      public decimal TestStrategyPrice { get; set; }
      public decimal TestStrategyPricePP { get => (ValuationAdjRetail > 0 && StrategyPrice > 0) ? (decimal)TestStrategyPrice / (decimal)ValuationAdjRetail : 0; }
      public int? TestStrategyDaysToSell { get; set; }
      public string LastCommentName { get; set; }
      public string LastCommentText { get; set; }
      public string CurrentStrategyName { get; set; }
      public DateTime? ActualEndDate { get; set; }

      public string VsStrategyBanding { get; set; }//
      public string VsTestStrategyBanding { get; set; }//


      public List<int> DailySearchViewsLast7 { get; set; }
      public List<int> DailyAdvertViewsLast7 { get; set; }

      public string SiteBrand { get; set; }
      public string SimpleBrand { get; set; }

      public decimal? PrepCost { get; set; }
      public string StockSource { get; set; }
      public string OptedOutBy { get; set; }
      public bool IsOptedOut { get => OptedOutBy != null ? true : false; }
      public DateTime? WhenOptedOut { get; set; }
      public DateTime? OptedOutUntil { get; set; }
      public decimal? LastPriceChangeValue { get; set; }



      public DateTime? WhenPriceLastManuallyChanged { get; set; }
      public string WhoLastManuallyChanged { get; set; }
      public decimal? TotalManualPriceChangesCount { get; set; }
      public decimal? TotalManualPriceChangesValue { get; set; }
      public bool IsVatQ { get; set; }
      public int? PricedProfit { get; set; }
      public string VehicleTypeDesc { get; set; }
      public string StockPrefix { get; set; }
      public string LifecycleStatus { get; set; }
      public string? AdvertiserAdvertStatus { get; set; }
      public DateTime? StockDate { get; set; }
      public DateTime? DateBookedIn { get; set; }
      public string OwnershipCondition { get; set; }



      public string AutotraderAdvertStatus { get; set; }




      public bool VehicleHasOptionsSpecified { get; set; }
      public string PortalOptions { get; set; }


      public decimal DaysToSellAtNewPrice { get; set; }
      public string PriceIndicatorAtNewPrice { get; set; }
      public decimal NewPrice { get; set; }
      public bool IsSmallPriceChange { get; set; }
      public bool IsKeyChange { get; set; }


      //Daily price moves
      public int FirstPrice { get; set; }
      public int DailyPriceMovesCount { get; set; }
      public int MostRecentDailyPriceMove { get; set; }
      public DateTime? MostRecentDailyPriceMoveDate { get; set; }
      public int DaysSinceMostRecentPriceMove { get; set; }



      public int ModelSellRate { get; set; }
      public int CompetitorCount { get; set; }
      public decimal? AveragePP { get; set; }
      public decimal? HighestPP { get; set; }
      public decimal? LowestPP { get; set; }
      public int OurPPRank { get; set; }
      public int OurValueRank { get; set; }
      public string CheapestSellerName { get; set; }
      public string CheapestSellerType { get; set; }
      public bool CheapestVehicle { get; set; }
      public bool OnlyVehicle { get; set; }
      public string AllPrices { get; set; }

      public decimal PPAverageFranchised { get; set; }
      public decimal PPAverageIndependents { get; set; }
      public decimal PPAveragePrivates { get; set; }
      public decimal PPAverageSupermarkets { get; set; }

      public decimal PriceUpMaintainRank { get; set; }
      public decimal PriceDownImproveRank { get; set; }
      public decimal PriceToBeCheapest { get; set; }


      public decimal DMSSellingPrice { get; set; }


      public decimal ValuationMonthPlus1 { get; set; }
      public decimal ValuationMonthPlus2 { get; set; }
      public decimal ValuationMonthPlus3 { get; set; }
      public int? EngineCapacityCC { get; set; }

      public string PhysicalLocation { get; set; }

      //Getters
      public int AdvertisedPriceExclAdminFee { get => AdvertisedPrice - AdminFee; }
      public string ModelCleanedUp { get => AdvertStratifierService.StandardiseModelName(Model, SiteBrand, Make); }
      public string SummaryColour { get => AdvertStratifierService.Summarise(Colour, "Colour"); }
      public int? ThisVehicleValnVsAverage { get => ValuationAdjRetail - ValuationMktAvRetail; }
      public string ImagesBand { get => BandingsService.ProvideImagesBanding(ImagesCount); }
      public bool HasImages
      {
         get { return !IsMissingImages; }
         set { IsMissingImages = !value; }
      }

      public decimal VsStrategyPrice { get => StrategyPrice == 0 || AdvertisedPrice == 0 ? 0 : AdvertisedPrice - StrategyPrice; }
      public decimal VsTestStrategyPrice { get => TestStrategyPrice == 0 || AdvertisedPrice == 0 ? 0 : AdvertisedPrice - TestStrategyPrice; }
      public void UpdateVsStrategyBanding(Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions)
      {
         var defn = bandingDefinitions[RetailerSiteId];
         if (defn == null)
         {
            { }
         }
         

         VsStrategyBanding = BandingsService.ProvidePriceVsStrategyBanding(StrategyPrice, (int)AdvertisedPrice, GetRelevantRetailValuationAdj, defn);
         VsTestStrategyBanding = BandingsService.ProvidePriceVsStrategyBanding(TestStrategyPrice, (int)AdvertisedPrice, GetRelevantRetailValuationAdj, defn);

      }
      public string AgeBand { get => BandingsService.ProvideAgeBand((DateTime.Now - (DateTime)(FirstRegisteredDate ?? DateTime.Now)).Days / 365M); }
      public string AgeAndOwners { get => BandingsService.ProvideAgeAndOwners(FirstRegisteredDate, Owners); }
      public string RetailRatingBand { get => BandingsService.ProvideRetailRatingBanding(RetailRating ?? 0); }
      public string NationalRetailRatingBand { get => BandingsService.ProvideRetailRatingBanding(NationalRetailRating ?? 0); }
      public string PerformanceRatingScoreBand { get => BandingsService.ProvidePerformanceRatingBanding(PerfRatingScore ?? 0); }
      public string DaysListedBand { get => BandingsService.ProvideDaysBanding(DaysListed); }
      public string DaysInStockBand { get => BandingsService.ProvideDaysBanding(DaysInStock); }
      public string DaysBookedInBand { get => BandingsService.ProvideDaysBanding(DaysBookedIn); }

      public string ValueBand { get => BandingsService.ProvideValueBanding(AdvertisedPrice); }
      public bool OnBrand
      {
         get
         {
            return SimpleBrand != "Non Franchise";
         }
      }
      public decimal PricePosition
      {
         get
         {
            return BandingsService.CalculatePricePosition(GetRelevantRetailValuationAdj, AdvertisedPrice);
         }
      }
      public int DaysListed
      {
         get
         {
            return AutoPriceHelperService.CalculateDaysListed(DateOnForecourt, CreatedInSparkDate);
         }
      }
      public int? DaysSinceLastPriceChange { get => WhenPriceLastManuallyChanged != null ? (DateTime.Now - ((DateTime)WhenPriceLastManuallyChanged)).Days : null; }
      public int DaysInStock
      {
         get
         {
            // If StockDate is null, DateTime.Today will be used instead, negating the need for a ternary operator.
            var difference = DateTime.Today - (StockDate ?? DateTime.Today);
            return difference.Days;
         }
      }
      public int DaysBookedIn
      {
         get
         {
            var difference = DateTime.Today - (DateBookedIn ?? DateTime.Today);
            return difference.Days;
         }
      }
      public bool HaveProfit { get => PricedProfit != null; }
      public decimal NewPriceExAdminFee { get => NewPrice - AdminFee; }
      public decimal NewPP { get => (ValuationAdjRetail.HasValue && ValuationAdjRetail > 0) ? NewPrice / (int)ValuationAdjRetail : 0; }
      public int TotalPriceChanges { get => AdvertisedPrice - FirstPrice; }
      public decimal MarketPositionScore
      {
         get
         {
            var result = (((CompetitorCount > 0 ? (decimal)OurPPRank / (decimal)CompetitorCount : 0) * 100) - 100) * -1;
            if (result > 99M) { return 100; }
            if (result < 1) { return 1; }
            return result;
         }
      }
      public string OdometerBanding { get => BandingsService.ProvideMileageBanding(OdometerReading.HasValue ? (int)OdometerReading : 0); }
      public string PricedProfitBanding { get => BandingsService.ProvidePricedProfitBanding(PricedProfit); }
      public string DaysToSellBanding { get => BandingsService.ProvideDaysToSellBanding(DaysToSellAtCurrentSelling); }
      public string SearchesBanding { get => BandingsService.ProvideSearchesBanding(SearchViews7Day); }
      public string AdViewsBanding { get => BandingsService.ProvideAdViewsBanding(AdvertViews7Day); }
      public decimal VsDMSSellingPrice { get => DMSSellingPrice > 0 ? ((AdvertisedPrice - AdminFee) - DMSSellingPrice) : 0; }

      public int DaysToAdvertise
      {
         get
         {
            var difference = DateTime.Today - (StockDate ?? DateTime.Today);
            return difference.Days - AutoPriceHelperService.CalculateDaysListed(DateOnForecourt, CreatedInSparkDate);
         }
      }
      public string Owners { get; set; }
      public string RetailerType { get; set; }


      public string BcaVin { get; set; }
      public decimal BcaMileage { get; set; }
      public decimal DaysOnAllSites { get; set; }
      public decimal NumberOfPreviousSales { get; set; }
      public bool V5Status { get; set; }
      public bool ServiceHistory { get; set; }
      public string Runner { get; set; }
      public string SalesComment { get; set; }
      public DateTime? HoldDate { get; set; }
      public string HoldCode { get; set; }
      public string HoldDescription { get; set; }

      public bool? IsTradePricing { get; set; }
      public decimal? TradeMarginPercentage { get; set; }
      public int? TradeMarginAmount { get; set; }


      public int GetRelevantRetailValuationAdj
      {
         get
         {
            if (VehicleType == "Van")
            {
               return (int)(ValuationAdjRetailExVat ?? 0);
            }
            return (int)(ValuationAdjRetail ?? 0);
         }
      }
      public int GetRelevantRetailValuationMktAv
      {
         get
         {
            if (VehicleType == "Van")
            {
               return (int)(ValuationMktAvRetailExVat ?? 0);
            }
            return (int)(ValuationMktAvRetail ?? 0);
         }
      }

     

   }
}