﻿using System;

namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class EventContactPreference
    {
        public Guid? EntityId { get; set; }
        
        public bool? Post { get; set; }

        public bool? Phone { get; set; }

        public bool? Sms { get; set; }

        public bool? Email { get; set; }

        public bool? SocialMedia { get; set; }

        public DateTime? Updated { get; set; }

        public int? Version { get; set; }
    }
}
