#summaryStatsTable {
  table-layout: fixed;
  width: 100%;

  th:nth-of-type(1) {
    width: 40%;
  }

  td {
    height: 3em;
  }

  th {
    padding: 0.4em;
  }
}

.contentsHolder {
  height: calc(100% - 2em);
}

.bigNumber {
  text-align: center;
  width: 100%;
}

/* Date picker styles */
input[type="date"] {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  text-align: center;
  //width: 100px;
}

input[type="date"]:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.metricsButton{width:12em;}