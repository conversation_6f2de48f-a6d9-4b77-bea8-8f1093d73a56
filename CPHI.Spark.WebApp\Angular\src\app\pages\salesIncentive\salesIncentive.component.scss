@use "sass:math";

#overallHolder {
  background-image: url('../../../assets/imgs/events/salesIncentive/dacia-spring.png');
  background-size: cover;
}

.content-inner-new {
  display: flex;
}

.prize {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &#firstPrize {
    border: 5px solid #FFC828;

    .value {
      color: #FFC828;
    }

    .ribbon {
      border-top: 18px solid #FFC828;
    }
  }

  &#secondPrize {
    border: 5px solid #C2C2C2;

    .value {
      color: #C2C2C2;
    }

    .ribbon {
      border-top: 18px solid #C2C2C2;
    }
  }

  &#thirdPrize {
    border: 5px solid #DB993F;

    .value {
      color: #DB993F;
    }

    .ribbon {
      border-top: 20px solid #DB993F;
    }
  }

  .value {

    transform: translateY(-8px);
  }

  .ribbon {
    height: 0;
    position: absolute;
    bottom: 12px;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    width: 95px;

    div {
      transform: translateY(-16px);

      text-align: center;
      line-height: 1;
      color: #000000;
    }
  }
}

#podiumAndTableContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80%;
  margin: 0 auto;

  #podium {
    display: flex;
    align-items: flex-end;
    height: 275px;
    min-height: 275px;

    .podiumStep {
      position: relative;
      width: 250px;
      border: 5px solid #000;
      border-radius: 10px 10px 0 0;
      color: #FFF;
      background-color: var(--grey60);

      &.first {
        height: 175px;
      }

      &.second {
        height: 150px;
        transform: translateX(5px);
      }

      &.third {
        height: 125px;
        transform: translateX(-5px);
      }

      .trophy {
        position: absolute;
        height: 100px;
        left: 0;
        right: 0;
        margin: 0 auto;
        transform: translateY(-105px);
      }

      .position {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      }

      .nameAndCount {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 1em;
        background-color: #000;
        padding: 0.5em;

        .name {

        }

        .count {

        }
      }
    }
  }

  #table {
    margin-top: 75px;
    width: 740px;
    background: #FFF;
    padding: 1em;
    border-radius: 10px;

    table {
      table-layout: fixed;
      width: 100%;
      border-collapse: collapse;


      tr:nth-child(even) {
        background-color: #F3F3F3;
      }

      td,
      th {
        padding: 7.5px;
      }

      th:nth-of-type(3),
      td:nth-of-type(3) {
        text-align: right;
      }
    }
  }
}

// Start of fireworks animation stuff.
$particles: 50;
$width: 500;
$height: 500;

$box-shadow: (
);
$box-shadow2: (
);

@for $i from 0 through $particles {

  $box-shadow: $box-shadow,
  random($width) - math.div($width, 2) + px random($height) - math.div($height, 1.2) + px hsl(random(360), 100%, 50%);

  $box-shadow2: $box-shadow2,
  0 0 #fff
}

@mixin keyframes($animationName) {
  @-webkit-keyframes #{$animationName} {
    @content;
  }

  @-moz-keyframes #{$animationName} {
    @content;
  }

  @-o-keyframes #{$animationName} {
    @content;
  }

  @-ms-keyframes #{$animationName} {
    @content;
  }

  @keyframes #{$animationName} {
    @content;
  }
}

@mixin animation-delay($settings) {
  -moz-animation-delay: $settings;
  -webkit-animation-delay: $settings;
  -o-animation-delay: $settings;
  -ms-animation-delay: $settings;
  animation-delay: $settings;
}

@mixin animation-duration($settings) {
  -moz-animation-duration: $settings;
  -webkit-animation-duration: $settings;
  -o-animation-duration: $settings;
  -ms-animation-duration: $settings;
  animation-duration: $settings;
}

@mixin animation($settings) {
  -moz-animation: $settings;
  -webkit-animation: $settings;
  -o-animation: $settings;
  -ms-animation: $settings;
  animation: $settings;
}

@mixin transform($settings) {
  transform: $settings;
  -moz-transform: $settings;
  -webkit-transform: $settings;
  -o-transform: $settings;
  -ms-transform: $settings;
}

@include keyframes(bang) {
  to {
    box-shadow: $box-shadow;
  }
}

@include keyframes(gravity) {
  to {
    @include transform(translateY(200px));
    opacity: 0;
  }
}

@include keyframes(position) {

  0%,
  19.9% {
    margin-top: 10%;
    margin-left: 40%;
  }

  20%,
  39.9% {
    margin-top: 40%;
    margin-left: 30%;
  }

  40%,
  59.9% {
    margin-top: 20%;
    margin-left: 70%
  }

  60%,
  79.9% {
    margin-top: 30%;
    margin-left: 20%;
  }

  80%,
  99.9% {
    margin-top: 30%;
    margin-left: 80%;
  }
}

.fireworks-container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  pointer-events: none;

  .fireworks {

    .before,
    .after {
      position: absolute;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      box-shadow: $box-shadow2;
      @include animation((1s bang ease-out infinite backwards, 1s gravity ease-in infinite backwards, 5s position linear infinite backwards));
    }

    .after {
      @include animation-delay((1.25s, 1.25s, 1.25s));
      @include animation-duration((1.25s, 1.25s, 6.25s));
    }
  }
}

// End of fireworks animation stuff.
