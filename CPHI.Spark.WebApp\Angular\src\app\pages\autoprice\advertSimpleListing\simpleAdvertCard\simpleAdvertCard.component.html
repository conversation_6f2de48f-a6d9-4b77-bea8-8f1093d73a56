<div class="autoTraderVehicleCard" (click)="openModal()"> 
    <div id="images">

        <!-- Main large image to the left -->
        <div  id="mainImage">
            <autotraderImage
            *ngIf="advert.ImageURL"
            [src]="advert.ImageURL"
            [widthpx]="600"
            [alt]="'Example Car'"
          ></autotraderImage>
        </div>

        <div *ngIf="!advert.ImageURL" id="mainImage" [style.backgroundImage]="'url(../../../../../assets/imgs/autoTrader/placeholder-car.png)'">
        </div>

        <!-- x3 Mini Images -->
        <div *ngIf="advert.AllImageURLs" id="subImages">
            <div *ngFor="let image of splitImageURLs() | slice:1:4" class="subImage">
                <autotraderImage
                  [src]="image"
                  [widthpx]="200"
                  [alt]="'Sub Image'"
                ></autotraderImage>
              </div>              
        </div>

        <div *ngIf="!advert.AllImageURLs" id="subImages">
            <div *ngFor="let i of [1,2,3]" class="subImage" [style.backgroundImage]="'url(../../../../../assets/imgs/autoTrader/placeholder-car.png)'">
            </div>
        </div>

        <div id="floatingReg">
            <div class="regPlate">
                {{ advert.VehicleReg | cph:'numberPlate':0 }}
            </div>
        </div>
        <div id="floatingImagesVideoCount">
            {{constantsService.pluralise(advert.ImagesCount,'image','images') }}, {{advert.NoVideo ? 'no video': '1 video'}} 
        </div>
    </div>
    <div class="contentHolder">
        <div id="vehicleDetails">
            <div class="makeModel">
                <h3><strong>{{ advert.Make }} {{ advert.Model }}</strong></h3>

                <div class="d-flex align-items-middle">
                    <div class="priceIndicatorLozenge inTile" [ngClass]="advert.PriceIndicatorRatingAtCurrentSelling">
                        {{ advert.PriceIndicatorRatingAtCurrentSelling }}
                    </div>

                    <a class="btn openAdButton"
                        [href]="'https://www.autotrader.co.uk' + (advert.VehicleType === 'Van' ? '/van-details/' : '/car-details/') + advert.WebSiteSearchIdentifier"
                        target="_blank"
                        (click)="$event.stopPropagation()">
                        <i class="fas fa-car"></i>
                    </a>
                </div>
            </div>
            <span class="subTitle">
                {{ advert.Derivative }}
            </span>
            <span class="specs">
                {{ advert.FirstRegisteredDate | cph:'year':0 }} | {{ advert.VehicleType }} | {{ advert.OdometerReading |
                cph:'number':0 }} miles | {{ advert.EngineCapacityCC }}cc | {{ advert.TransmissionType }} | {{ advert.FuelType }}
            </span>
            <span class="attentionGrabber">
                {{ advert.AttentionGrabber }}
            </span>
            <span class="seller">
                {{ advert.RetailerSiteName }}
            </span>
            <div class="pricePositionRank">
                <div>Price Position Rank</div>
                <div><b>{{ advert.OurPPRank }}</b> / {{ advert.CompetitorCount }}</div>
            </div>
            <div class="marketPositionScore">
                <div>Market Position Score</div>
                <div><b>{{ advert.MarketPositionScore | cph:'number':0 }}</b> out of 100</div>
            </div>
            <div class="lastPriceChange">
                <div>Last Price Change</div>
                <div [ngClass]="advert.LastPriceChangeValue < 0 ? 'danger' : null">
                    {{ advert.LastPriceChangeValue | cph:'currency':0:true }}
                </div>
            </div>
            <div class="priceLastChanged">
                <div>Days Since Last Price Change</div>
                <div>{{ advert.DaysSinceLastPriceChange }}</div>
            </div>
        </div>
        <div id="vehicleDetailsContinued">
            <div class="labelAndValue">
                <div class="priceAndPosition"><h2>{{ advert.AdvertisedPrice | cph:'currency':0 }}</h2> ({{ advert.PricePosition | cph:'percent':1 }})</div>
                <div>{{ advert.VsStrategyPrice | cph:'currency':0:(advert.VsStrategyPrice > 0 ? true : false) }} vs Strategy Price</div>
            </div>
            <div class="labelAndValue">
                <div>Valuation</div>
                <div placement="bottom" popoverClass="adjustmentsPopover" container="body" triggers="mouseenter:mouseleave"
                    [ngbPopover]="adjustmentsPopover">
                    {{ advert.ValuationAdjRetail | cph:'currency':0 }} ({{ advertAdjustedVsAvValn| cph:'currency':0:true }})
                </div>
            </div>
            <div class="labelAndValue">
                <div>Retail Rating</div>
                <div><b>{{ advert.RetailRating | cph:'number':0 }}</b> out of 100</div>
            </div>
            <div class="labelAndValue">
                <div>Strategy Price</div>
                <div>{{ advert.StrategyPrice | cph:'currency':0 }} ({{ advert.StrategyPrice / advert.ValuationMktAvRetail | cph:'percent':1 }})</div>
            </div>
            <div *ngIf="advert.AgeDaysListedFrom!=='stockDate'" class="labelAndValue">
                <div>Days Listed</div>
                <div>{{ advert.DaysListed }} days</div>
            </div>
            <div *ngIf="advert.AgeDaysListedFrom==='stockDate'" class="labelAndValue">
                <div>Days In Stock</div>
                <div>{{ advert.DaysInStock }} days</div>
            </div>
            <div class="labelAndValue">
                <div>Days to Sell</div>
                <div>{{ advert.DaysToSellAtCurrentSelling | cph:'number':0 }} days</div>
            </div>
            <div class="labelAndValue">
                <div>Performance Rating</div>
                <div class="auto-trader-cell-lozenge" [ngClass]="advert.PerfRating | cph:'trimWhitespace':0">
                    {{ advert.PerfRating }}
                </div>
            </div>
            <div class="labelAndValue">
                <div>Searches Last 7 Days</div>
                <div>{{ advert.SearchViews7Day | cph:'number':0 }}</div>
            </div>
            <div class="labelAndValue">
                <div>Ad Views Last 7 Days</div>
                <div>{{ advert.AdvertViews7Day | cph:'number':0 }}</div>
            </div>
        </div>
        <div id="comments">
            <div id="priceRatingLozengeContainer">
                <div id="priceRatingLozenge" class="auto-trader-cell-lozenge inCard" [ngClass]="advert.VsStrategyBanding">
                    {{ advert.VsStrategyBanding }}
                </div>
            </div>

            <div id="comments-container">
                <span *ngIf="!advert.LastCommentText">No comment</span>
                <div *ngIf="advert.LastCommentText" class="comment-bubble-container">
                    <span class="comment-date">{{ '01/06/2024' | cph:'shortDateWithDayName':0 }}</span>
                    <div class="comment-bubble">
                        <span class="comment-text">{{ advert.LastCommentText }}</span>
                        <br>
                        <span class="comment-person">{{ advert.LastCommentName }}</span>
                    </div>
                </div>
            </div>
            <div id="add-new-comment">
                <textarea type="text" [(ngModel)]="newComment" placeholder="Add comment"
                    (keydown.ENTER)="onEnterPressed($event)" [ngClass]="{ 'canSave': newComment }" (click)="$event.stopPropagation()">
                </textarea>
                <button *ngIf="newComment" id="saveComment" (click)="$event.stopPropagation(); saveNewComment()">
                    <i class="fas fa-long-arrow-up"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<ng-template class="popover" style="max-width: 300px;" #adjustmentsPopover>
    This vehicle has a valuation of {{ advert.ValuationAdjRetail | cph:'currency':0 }} which is adjusted due to the options and upgrades 
    listed in the advert description, as well as any fitted features which have been selected in the portal. The valuation for this 
    derivative at this mileage with the average mix of features is {{ (advertAdjustedVsAvValn > 0  ? advertAdjustedVsAvValn: advertAdjustedVsAvValn  * -1) | cph:'currency':0:true }} 
    {{ advertAdjustedVsAvValn ? 'higher' : 'lower' }} at {{  advert.ValuationMktAvRetail | cph:'currency':0 }}.
</ng-template>