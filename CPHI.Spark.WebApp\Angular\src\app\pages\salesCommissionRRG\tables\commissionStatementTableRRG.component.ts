/* eslint-disable no-useless-constructor */
import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core'
import { Router } from '@angular/router'
import { NgbModal } from '@ng-bootstrap/ng-bootstrap'
import { ColDef, GridA<PERSON> } from 'ag-grid-community'
import { Subscription } from 'rxjs'
import { DealDetailsComponent } from 'src/app/components/dealDetails/dealDetails.component'
import { CommissionItem } from 'src/app/model/sales.model'
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service'
import { localeEs } from 'src/environments/locale.es.js'
import { CphPipe } from '../../../cph.pipe'
import { SheetToExtract } from '../../../model/main.model'
import { GridOptionsCph } from 'src/app/model/GridOptionsCph'
import { ConstantsService } from '../../../services/constants.service'
import { ExcelExportService } from '../../../services/excelExportService'
import { SelectionsService } from '../../../services/selections.service'
import { OrderbookRow } from "../../../model/OrderbookRow"
import { CommissionItemRRG, CommissionRowRRG } from '../salesCommissionRRG.model'
import { SalesCommissionServiceRRG } from '../salesCommissionRRG.service'
import { ColumnTypesService } from 'src/app/services/columnTypes.service'

@Component({
  selector: 'commissionStatementTableRRG',

  template: `
    <div id="gridHolder"  #gridHolder>
    <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        class="ag-theme-balham"
        [gridOptions]="mainTableGridOptions"
        domLayout="autoHeight"

      >
      </ag-grid-angular>

    </div>
  `,

  styleUrls: ['./../../../../styles/components/_agGrid.scss'],
  styles: [
    `


      ag-grid-angular {
        width: 100%;
        max-height:100%;
        margin: 0em auto;
        margin-top: 1em;
      }
      #gridHolder {
        position: relative;
        width:100%;

    /* overflow: hidden; */
      }

    `
  ]
})
export class CommissionStatementTableRRGComponent implements OnInit {
  @ViewChild('gridHolder', { static: true }) tableContainer: ElementRef;

  @HostListener('window:resize', [])
  private onresize (event) {
    this.selections.screenWidth = window.innerWidth
    this.selections.screenHeight = window.innerHeight
    if (this.gridApi) {
      this.gridApi.resetRowHeights()
      this.resizeGrid()
    }
  }

  columnDefs: ColDef[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;

  subscription: Subscription;

  constructor (
    public constants: ConstantsService,
    public columnTypeService: ColumnTypesService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public router: Router,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: SalesCommissionServiceRRG
  ) { }

  onGridReady (params) {
    this.gridApi = params.api
    this.gridColumnApi = params.columnApi
    this.mainTableGridOptions.context = { thisComponent: this }
    this.gridApi.sizeColumnsToFit()
  }

  ngOnInit () {
    // this.sortRowData()
    this.initParams()
   
  }


  dealWithNewData(){
    if(!!this.gridApi){
      this.gridApi.setRowData(this.provideRowData())
      this.gridApi.setPinnedBottomRowData(this.provideBottomRowData());
    }
  }

  ngOnDestroy () {
    if (this.subscription) this.subscription.unsubscribe();
  }

  initParams () {

    // table definitions
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang === 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      domLayout: 'autoHeight',
      suppressPropertyNamesCheck: true,
      onRowClicked: (params) => this.onRowClick(params),
      onGridReady: (params) => this.onGridReady(params),
      getRowHeight: (params) => {
        const normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)))
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      getMainMenuItems:(params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlightsExecs),
      defaultColDef: {
        resizable: true,
        sortable: true,
        floatingFilter: true,
      },
      // onFilterChanged:(params)=>this.buildAndSetBottomRow(),
      getRowClass: (params) => params.data.isAdjustment ? 'adjustment' : 'deal',
      rowData: this.provideRowData(),
      pinnedBottomRowData: this.provideBottomRowData(),
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsExecs),
      },
      columnDefs: this.getColDefs()
    }

    
  }

  getColDefs()
  {
    if(this.service.schemeToShow === 'LBDMs')
    {
      return this.getColDefsLBDM();
    }
    else
    {
      return this.getColDefsNormal();
    }
  }

  getColDefsLBDM()
  {
        // const baseHeight = window.innerWidth > 1550 ? 4 : 0
        const gridScaleValue = this.tableContainer.nativeElement.clientWidth / 1520 // actual measurement 1230.

      return [
        { headerName: '', valueGetter: (params) => params.node.rowPinned === 'bottom' ? '' : params.node.rowIndex + 1, colId: 'count', width: gridScaleValue * 30, type: 'label' },
        { headerName: 'Stock No.', field: 'StockNumber', colId: 'StockNumber', width: gridScaleValue * 90, type: 'labelCentre' },
        {
          headerName: 'Reg',
          field: 'Reg',
          cellRenderer: (params) => params.node.rowPinned === 'bottom' ? '' : `<div class="visibleAboveMd regPlate">    ${this.cphPipe.transform(params.value, 'numberPlate', 0)}</div>`,
          colId: 'Reg',
          width: gridScaleValue * 90,
          type: 'labelCentre'
        },
        { headerName: 'Customer', field: 'Customer', colId: 'Customer', width: gridScaleValue * 120, type: 'label' },
        {
          headerName: 'Type',
          valueGetter: (params) => params.node.rowPinned ? 'TOTAL' : params.data.isAdjustment ? 'Adjustment' : 'Deal',
          colId: 'Type',
          width: gridScaleValue * 60,
          type: 'label'
        },
        { headerName: 'Vehicle Type', field: 'VTypeType', colId: 'VTypeType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Order Type', field: 'OTypeType', colId: 'OTypeType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Comm Type', field: 'VehicleCommissionType', colId: 'VehicleCommissionType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Order date', valueGetter: (params) => params.node.rowPinned ? '' : params.data.OrderDate, field: 'OrderDate', colId: 'OrderDate', width: gridScaleValue * 80, type: 'dateLongYear' },
        { headerName: 'Accounting date', valueGetter: (params) => params.node.rowPinned ? '' : params.data.AccountingDate, field: 'AccountingDate', colId: 'AccountingDate', width: gridScaleValue * 80, type: 'dateLongYear' },

        {
          headerName: 'Units',
          children: [
            { headerName: 'Q', field: 'Units_Count', colId: 'Units_Count', width: gridScaleValue * 40, type: 'number' },
            { headerName: '£', field: 'PayRateVehicle', colId: '', width: gridScaleValue * 40, type: 'currency' }
          ],
        },

        {
          headerName: 'Service Plan',
          children: [
            { headerName: 'Q', field: 'ServicePlan_Count', colId: 'Service_Count', width: gridScaleValue * 40, type: 'number' },
            { headerName: '£', field: 'PayRateServicePlan', colId: '', width: gridScaleValue * 40, type: 'currency' }
          ],
        },

        {
          headerName: 'Paint',
          children: [
            { headerName: 'Q', field: 'PaintProtection_Count', colId: 'PaintProtection_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRatePaint', type: 'currency', colId: 'PayRatePaint', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'Cosmetic',
          children: [
            { headerName: 'Q', field: 'Cosmetic_Count', colId: 'Cosmetic_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateCosmetic', type: 'currency', colId: 'PayRateCosmetic', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'Tyre and Alloy',
          children: [
            { headerName: 'Q', field: 'TyreAlloy_Count', colId: 'TyreAlloy_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateTyreAlloy', type: 'currency', colId: 'PayRateCosmetic', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'Finance',
          children: [
            { headerName: 'Q', field: 'Finance_Count', colId: 'Finance_Count', width: gridScaleValue * 45, type: 'number', },
            { headerName: '£', field: 'PayRateFinance', type: 'currency', colId: 'PayRateFinance', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'Tyre',
          hide: this.service.schemeToShow === 'LBDMs',
          children: [
            { headerName: 'Q', field: 'Tyre_Count', colId: 'Tyre_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateTyre', type: 'currency', colId: 'PayRateTyre', width: gridScaleValue * 45, }
          ]
        },
        {
          headerName: 'WheelGuard',
          children: [
            { headerName: 'Q', field: 'WheelGuard_Count', colId: 'WheelGuard_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateWheelGuard', type: 'currency', colId: 'PayRateWheelGuard', width: gridScaleValue * 45,  }
          ]
        },

        { headerName: 'Total Commission', field: 'TotalCommission', colId: 'TotalCommission', width: gridScaleValue * 90, type: 'currency' }

      ] as ColDef[]
  }

  getColDefsNormal()
  {
        // const baseHeight = window.innerWidth > 1550 ? 4 : 0
        const gridScaleValue = this.tableContainer.nativeElement.clientWidth / 1520 // actual measurement 1230.
        
      return [
        { headerName: '', valueGetter: (params) => params.node.rowPinned === 'bottom' ? '' : params.node.rowIndex + 1, colId: 'count', width: gridScaleValue * 30, type: 'label' },
        { headerName: 'Stock No.', field: 'StockNumber', colId: 'StockNumber', width: gridScaleValue * 90, type: 'labelCentre' },
        {
          headerName: 'Reg',
          field: 'Reg',
          cellRenderer: (params) => params.node.rowPinned === 'bottom' ? '' : `<div class="visibleAboveMd regPlate">    ${this.cphPipe.transform(params.value, 'numberPlate', 0)}</div>`,
          colId: 'Reg',
          width: gridScaleValue * 90,
          type: 'labelCentre'
        },
        { headerName: 'Customer', field: 'Customer', colId: 'Customer', width: gridScaleValue * 120, type: 'label' },
        {
          headerName: 'Type',
          valueGetter: (params) => params.node.rowPinned ? 'TOTAL' : params.data.isAdjustment ? 'Adjustment' : 'Deal',
          colId: 'Type',
          width: gridScaleValue * 60,
          type: 'label'
        },
        { headerName: 'Vehicle Type', field: 'VTypeType', colId: 'VTypeType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Order Type', field: 'OTypeType', colId: 'OTypeType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Comm Type', field: 'VehicleCommissionType', colId: 'VehicleCommissionType', width: gridScaleValue * 80, type: 'labelCentre' },
        { headerName: 'Order date',  field: 'OrderDate', colId: 'OrderDate', width: gridScaleValue * 80, type: 'dateLongYear' },
        { headerName: 'Accounting date', valueGetter: (params) => params.node.rowPinned ? '' : params.data.AccountingDate, field: 'AccountingDate', colId: 'AccountingDate', width: gridScaleValue * 80, type: 'dateLongYear' },

        {
          headerName: 'Units',
          children: [
            { headerName: 'Q', field: 'Units_Count', colId: 'Units_Count', width: gridScaleValue * 40, type: 'number' },
            { headerName: '£', field: 'PayRateVehicle', valueGetter: (params) => this.payoutGetter(params, 'PayRateVehicle'), colId: '', width: gridScaleValue * 40, type: 'labelCentre' }
          ],
        },
        {
          headerName: 'Profit Pot',
          hide: this.service.schemeToShow === 'LBDMs',
          children: [
            { headerName: 'Deal Profit', field: 'EligibleProfitPot', valueGetter: (params) => this.payoutGetter(params, 'EligibleProfitPot'), colId: 'EligibleProfitPot', width: gridScaleValue * 80, type: 'currency',  },
            { headerName: '%', field: 'ProfitPotThresholdAchieved', colId: 'ProfitPotThresholdAchieved', width: gridScaleValue * 40, type: 'percent1dp',  },
            { headerName: 'Payout', field: 'ProfitPotPayout', valueGetter: (params) => this.payoutGetter(params, 'ProfitPotPayout'), colId: 'ProfitPotPayout', width: gridScaleValue * 80, type: 'currency', },
          ]
        },

        {
          headerName: 'Paint',
          children: [
            { headerName: 'Q', field: 'PaintProtection_Count', colId: 'PaintProtection_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRatePaint', valueGetter: (params) => this.payoutGetter(params, 'PayRatePaint'), type: 'currency', colId: 'PayRatePaint', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: this.constants.translatedText.Gap,
          children: [
            { headerName: 'Q', field: 'Gap_Count', colId: 'Gap_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateGap', valueGetter: (params) => this.payoutGetter(params, 'PayRateGap'), colId: 'PayRateGap', type: 'currency', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'Cosmetic',
          children: [
            { headerName: 'Q', field: 'Cosmetic_Count', colId: 'Cosmetic_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateCosmetic', valueGetter: (params) => this.payoutGetter(params, 'PayRateCosmetic'), type: 'currency', colId: 'PayRateCosmetic', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'Wheel',
          children: [
            { headerName: 'Q', 
              field: 'TyreAlloy_Count', 
              colId: 'TyreAlloy_Count', 
              valueGetter: (params) => {
                return this.tyreValueGetter(params);
            },
              width: gridScaleValue * 45, 
              type: 'number' 
            },
            { headerName: '£', 
              field: 'PayRateTyreAlloy', 
              valueGetter: (params) => {
                  return this.tyrePayoutGetter(params);
              },
              type: 'currency', 
              colId: 'PayRateTyreAlloy', 
              width: gridScaleValue * 45, }
          ]
        },
        {
          headerName: 'WheelGuard',
          children: [
            { headerName: 'Q', field: 'WheelGuard_Count', colId: 'WheelGuard_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateWheelGuard', valueGetter: (params) => this.payoutGetter(params, 'PayRateWheelGuard'), type: 'currency', colId: 'PayRateWheelGuard', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'Warranty',
          children: [
            { headerName: 'Q', field: 'Warranty_Count', valueGetter:(params)=>this.warrantCountGetter(params), colId: 'Warranty_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateWarranty', valueGetter: (params) => this.payoutGetter(params, 'PayRateWarranty'), type: 'currency', colId: 'PayRateWarranty', width: gridScaleValue * 45, }
          ]
        },
        {
          headerName: 'Mot',
          children: [
            { headerName: 'Q', field: 'Mot_Count', valueGetter:(params)=>this.motCountGetter(params), colId: 'Mot_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateMot', valueGetter: (params) => this.payoutGetter(params, 'PayRateMot'), type: 'currency', colId: 'PayRateMot', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'R_Assist',
          children: [
            { headerName: 'Q', field: 'RoadsideAssist_Count', valueGetter:(params)=>this.roadsideAssistCountGetter(params), colId: 'RoadsideAssist_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateRoadsideAssist', valueGetter: (params) => this.payoutGetter(params, 'PayRateRoadsideAssist'), type: 'currency', colId: 'PayRateRoadsideAssist', width: gridScaleValue * 45,  }
          ]
        },
        {
          headerName: 'Finance',
          children: [
            { headerName: 'Q', field: 'Finance_Count', colId: 'Finance_Count', width: gridScaleValue * 45, type: 'number', },
            { headerName: '£', field: 'PayRateFinance', valueGetter: (params) => this.payoutGetter(params, 'PayRateFinance'), type: 'currency', colId: 'PayRateFinance', width: gridScaleValue * 45,  }
          ]
        },


        {
          headerName: 'PartEx',
          children: [
            { headerName: 'Q', field: 'PartEx_Count', colId: 'PartEx_Count', width: gridScaleValue * 45, type: 'number', },
            { headerName: '£', field: 'PayRatePartEx', valueGetter: (params) => this.payoutGetter(params, 'PayRatePartEx'), type: 'currency', colId: 'PayRatePartEx', width: gridScaleValue * 45,  }
          ]
        },

        {
          headerName: 'Service',
          children: [
            { headerName: 'Q', field: 'ServicePlan_Count', colId: 'ServicePlan_Count', width: gridScaleValue * 45, type: 'number' },
            { headerName: '£', field: 'PayRateServicePlan', valueGetter: (params) => this.payoutGetter(params, 'PayRateServicePlan'), type: 'currency', colId: 'PayRateServicePlan', width: gridScaleValue * 45,  }
          ]
        },

        { headerName: 'Total Commission', field: 'TotalCommission', colId: 'TotalCommission', width: gridScaleValue * 90, type: 'currency' }

      ] as ColDef[]
  }

  unitsGetter (params, field: string) {
    if (!params.node.data) return
    if (!field) return
    if (params.node.rowPinned === 'bottom') return this.cphPipe.transform(`params.node.data.${field}`, 'currency', 0)
    return ' @' + this.cphPipe.transform(`params.node.data.${field}`, 'currency', 0)
  }

  tyrePayoutGetter (params) {
    if (!params.node.data) return
    var tyre = params.data['PayRateTyre'];
    var tyreAlloy = params.data['PayRateTyreAlloy'];
    var total = tyreAlloy + tyre;
    return total !== 0 ?  this.cphPipe.transform(total, 'currency', 0) : '-'
  }

  tyreValueGetter (params) {
    if (!params.node.data) return
    var tyre = params.data['TyreAlloy_Count'];
    var tyreAlloy = params.data['Tyre_Count'];
    var total = tyreAlloy + tyre;
    return this.cphPipe.transform(total, 'number',0);
  }

  payoutGetter (params, field: string) {
    if (!params.node.data) return
    if (params.node.rowPinned === 'bottom') return this.cphPipe.transform(params.data[field], 'currency', 0)
    return params.data[field] !== 0 ?  this.cphPipe.transform(params.data[field], 'currency', 0) : '-'
  }
  
  warrantCountGetter (params) {
    if (!params.node.data) return
    //if (params.node.rowPinned === 'bottom') return this.cphPipe.transform(params.data[field], 'currency', 0)
    let row: CommissionItemRRG = params.node.data;
    return (row.Warranty2Yr_Count+row.WarrantyLifetime_Count + row.Warranty_Count) > 0 ? 1 : 0
  }

  motCountGetter(params){
    if (!params.node.data) return
    const value = params.data.Mot12_Count + params.data.Mot24_Count + params.data.Mot36_Count;
    return this.cphPipe.transform(value, 'number',0);
  }

  roadsideAssistCountGetter(params){
    if (!params.node.data) return
    const value = params.data.RoadsideAssist12_Count + params.data.RoadsideAssist24_Count + params.data.RoadsideAssist36_Count;
    return this.cphPipe.transform(value, 'number',0);
  }

  provideRowData (): CommissionItemRRG[] {
    return this.service.commissionItems
  }

  provideBottomRowData ():CommissionItemRRG[] {

    //need to convert the row data into a total row
    let totalledRow:CommissionItemRRG = {
      Units_Count:0,
      PayRateVehicle:0,
      PaintProtection_Count:0,
      PayRatePaint:0,
      Gap_Count:0,
      PartEx_Count:0,
      PayRateGap:0,
      Cosmetic_Count:0,
      PayRateCosmetic:0,
      Tyre_Count:0,
      TyreAlloy_Count:0,
      PayRateTyreAlloy:0,
      PayRateTyre: 0,
      WheelGuard_Count:0,
      PayRateWheelGuard:0,
      Service_Count: 0,
      ServicePlan_Count: 0,
      Warranty_Count:0,
      Warranty2Yr_Count:0,
      WarrantyLifetime_Count:0,
      PayRateWarranty:0,
      PayRatePartEx:0,
      PayRateService:0,
      PayRateServicePlan:0,
      Mot12_Count:0,
      Mot24_Count:0,
      Mot36_Count:0,
      PayRateMot:0,
      RoadsideAssist12_Count:0,
      RoadsideAssist24_Count:0,
      RoadsideAssist36_Count:0,
      PayRateRoadsideAssist:0,
      Finance_Count:0,
      PayRateFinance:0,
      ProfitPotPayout: 0,
      EligibleProfitPot: 0,
      TotalCommission:0,
    } as CommissionItemRRG

    this.provideRowData().forEach(item=>{
      totalledRow.Units_Count += item.Units_Count;
      totalledRow.PayRateVehicle += item.PayRateVehicle;
      totalledRow.PaintProtection_Count += item.PaintProtection_Count;
      totalledRow.PayRatePaint += item.PayRatePaint;
      totalledRow.Gap_Count += item.Gap_Count;
      totalledRow.PartEx_Count += item.PartEx_Count;
      totalledRow.PayRateGap += item.PayRateGap;
      totalledRow.Cosmetic_Count += item.Cosmetic_Count;
      totalledRow.PayRateCosmetic += item.PayRateCosmetic;
      totalledRow.Tyre_Count += item.Tyre_Count;
      totalledRow.ServicePlan_Count += item.ServicePlan_Count;
      totalledRow.Service_Count += item.Service_Count;
      totalledRow.TyreAlloy_Count += item.TyreAlloy_Count;
      totalledRow.PayRateTyreAlloy += item.PayRateTyreAlloy;
      totalledRow.PayRateTyre += item.PayRateTyre;
      totalledRow.WheelGuard_Count += item.WheelGuard_Count;
      totalledRow.PayRateWheelGuard += item.PayRateWheelGuard;
      totalledRow.PayRatePartEx += item.PayRatePartEx;
      totalledRow.PayRateService += item.PayRateService;
      totalledRow.PayRateServicePlan += item.PayRateServicePlan;
      totalledRow.Warranty_Count += item.Warranty_Count;
      totalledRow.Warranty2Yr_Count += item.Warranty2Yr_Count;
      totalledRow.WarrantyLifetime_Count += item.WarrantyLifetime_Count;
      totalledRow.PayRateWarranty += item.PayRateWarranty;
      totalledRow.Mot12_Count += item.Mot12_Count;
      totalledRow.Mot24_Count += item.Mot24_Count;
      totalledRow.Mot36_Count += item.Mot36_Count;
      totalledRow.PayRateMot += item.PayRateMot;
      totalledRow.RoadsideAssist12_Count += item.RoadsideAssist12_Count;
      totalledRow.RoadsideAssist24_Count += item.RoadsideAssist24_Count;
      totalledRow.RoadsideAssist36_Count += item.RoadsideAssist36_Count;
      totalledRow.PayRateRoadsideAssist += item.PayRateRoadsideAssist;
      totalledRow.Finance_Count += item.Finance_Count;
      totalledRow.PayRateFinance += item.PayRateFinance;

      totalledRow.ProfitPotPayout += item.ProfitPotPayout != null ? item.ProfitPotPayout : 0;
      totalledRow.EligibleProfitPot += item.EligibleProfitPot != null ? item.EligibleProfitPot : 0;
      totalledRow.ProfitPotThresholdAchieved = item.ProfitPotThresholdAchieved;

      // For non-Used deals
      totalledRow.TotalCommission += item.TotalCommission;
    })

    
    return [totalledRow];
  }

  onRowClick (params): void {
    if (params.node.rowPinned === 'bottom' || params.node.data.DealId === 0) {
      return
    }

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    setTimeout(() => {
      let modalRef

      if (this.constants.environment.dealDetailModal_componentName === 'DealDetailsRRGComponent') {
        modalRef = this.modalService.open(DealDetailsComponent)
      }

      if (this.constants.environment.dealDetailModal_componentName === 'DealDetailsComponent') {
        modalRef = this.modalService.open(DealDetailsComponent)
      }

      // I give to modal
      modalRef.componentInstance.givenDealId = params.data.DealId
      modalRef.result.then((result) => { // I get back from modal
        if (result) {
          // thing
        }
      })
    }, 10)
  }


  resizeGrid () {
    if (this.gridApi) this.gridApi.sizeColumnsToFit()
  }

  excelExport () {
    // get tableModel from ag-grid
    const tableModel = this.gridApi.getModel()

    // Written a custom function to to this to get it to wo0rk.
    this.createSheetObject(tableModel, 'Commission Statement', 1.3, 1)
  }

  createSheetObject(tableModel, tableName: string, overrideWidthsFactor?: number, providedInitialColumnsToHighlight?: number, doNotExportJustReturn?: boolean) {

    let finalHeaders: Array<any[]> = []
    let finalRows: Array<any[]> = []
    let finalFooters: Array<any[]> = []

    // //the normal headings
    let columns = tableModel.columnModel.displayedColumnsCenter

    let columnTypes: string[] = []
    let columnWidths: number[] = []
    //let columnHeadings: string[] = []
    let columnFields: string[] = []

    columns.forEach(col => {
      if (col.colDef.type == 'labelPercent') columnTypes.push('percent');
      else if (col.colDef.type == 'percent1dp') columnTypes.push('percent');
      else columnTypes.push(col.colDef.type);
      let width = col.actualWidth / 8 //divide by 8 seems about right

      if (overrideWidthsFactor) width = overrideWidthsFactor * width
      columnWidths.push(width);
      //columnHeadings.push(col.colDef.headerName)
      let field = col.colDef.excelField || col.colDef.field
      columnFields.push(field)
    })

    let totalHeaderRows: number = tableModel.columnModel.primaryHeaderRowCount;

    //build headerRowsArray
    let headerRows: string[][] = []

    for (let i = 0; i < totalHeaderRows; i++) {
      headerRows.push([])
    }

    //now walk down the columnTree populating these
    let headingTree = tableModel.columnModel.displayedTreeCentre

    headingTree.forEach((row, i) => {
      //firstly for top row
      let label = ""
      if (!!row.userProvidedColDef && !!row.userProvidedColDef.headerName) label = row.userProvidedColDef.headerName
      headerRows[0].push(label);
      //for 2nd row
      if (totalHeaderRows > 1) {
        row.children.forEach((child, i) => {
          let label = ""
          if (!!child.userProvidedColDef && !!child.userProvidedColDef.headerName) label = child.userProvidedColDef.headerName
          headerRows[1].push(label);
          if (i > 0) headerRows[0].push('');
          //for 3rd row
          if (totalHeaderRows > 2) {
            child.children.forEach((grandchild, j) => {
              let label = ""
              if (!!grandchild.userProvidedColDef && !!grandchild.userProvidedColDef.headerName) label = grandchild.userProvidedColDef.headerName
              headerRows[2].push(label);
              if (j > 0) headerRows[1].push('');
              if (j > 0) headerRows[0].push('');
            })
          }
        })
      }
    })

    finalHeaders = headerRows

    //normal rows
    tableModel.rowsToDisplay.forEach(row => {
      let dataRow = []
      columnFields.forEach(field => {

        let dataValue = null;

        if (field && field.substr(0, 2) === "!!") {
          field = field.substr(2, field.length - 2);
          let val = this.constants.getNestedItem(row.data, field)
          dataValue = !!val;
        } else {
          if (field) { 

            dataValue = this.constants.getNestedItem(row.data, field);

              if(field == 'TyreAlloy_Count' && this.service.schemeToShow != 'LBDMs')
              {
                dataValue += this.constants.getNestedItem(row.data, "Tyre_Count");
              }
              else if(field == 'PayRateTyreAlloy' && this.service.schemeToShow != 'LBDMs')
              {
                dataValue += this.constants.getNestedItem(row.data, "PayRateTyre"); 
              }
          
          }
        }

        if (Array.isArray(dataValue)) dataValue = dataValue.length

        // if(dataValue == undefined){ dataValue = 0; }

        dataRow.push(dataValue)
      })

      finalRows.push(dataRow);
    })

    //pinned bottom rows
    tableModel.gridOptionsService.api.rowRenderer.pinnedRowModel.pinnedBottomRows.forEach(row => {
      let dataRow = []

      columnFields.forEach(field => {

        let dataValue = null;

        if (field) { 
          dataValue = this.constants.getNestedItem(row.data, field); 
          
          // fix weird bug with Nans and these two fields
          if( (field == 'RoadsideAssist_Count' || field == 'Mot_Count') && dataValue == undefined)
          {
            dataValue = 0;
          }
          // This particular field needs to be the sum of both Tyre & TyreAlloy
          else if(field == 'TyreAlloy_Count' && this.service.schemeToShow != 'LBDMs')
          {
            dataValue += this.constants.getNestedItem(row.data, "Tyre_Count");
          }
          else if(field == 'PayRateTyreAlloy' && this.service.schemeToShow != 'LBDMs')
          {
            dataValue += this.constants.getNestedItem(row.data, "PayRateTyre"); 
          }

        }

        if (Array.isArray(dataValue)) dataValue = dataValue.length
        
        dataRow.push(dataValue)
      })

      finalFooters.push(dataRow);
    })

    let initialColumnsToHighlight = providedInitialColumnsToHighlight || 1

    if(this.service.schemeToShow != 'LBDMs')
    {
      finalHeaders[0][10] = "Units";

      let i = 15;
      finalHeaders[0][i] = "Paint";
      finalHeaders[0][i+=2] = "GAP";
      finalHeaders[0][i+=2] = "Cosmetic";
      finalHeaders[0][i+=2] = "Wheel";
      finalHeaders[0][i+=2] = "WheelGuard";
      finalHeaders[0][i+=2] = "Warranty";
      finalHeaders[0][i+=2] = "Mot";
      finalHeaders[0][i+=2] = "R_Assist";
      finalHeaders[0][i+=2] = "Finance";
      finalHeaders[0][i+=2] = "PartEx";
      finalHeaders[0][i+=2] = "Service";

    }


    let sheetDataToExtract: SheetToExtract[] = [];
    sheetDataToExtract.push({ headers: finalHeaders, rows: finalRows, footers: finalFooters, tableName: tableName, columnWidths: columnWidths, columnTypes: columnTypes, initialColumnsToHighlight: initialColumnsToHighlight, notes: null });

    if (doNotExportJustReturn) {
      return sheetDataToExtract;
    } else {
      this.excel.exportSheetsToExcel(sheetDataToExtract)
    }

  }
}
