﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
    <OutputType>Exe</OutputType>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWPF>true</UseWPF>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
    <PublishChromeDriver>true</PublishChromeDriver>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="appsettings.json" />
    <None Remove="appsettings.rrgSpainPhysical.json" />
  </ItemGroup>
  <ItemGroup>
    <None Update="job_scheduling_data_2_0.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Update="log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="appsettings.rrgSpainPhysical.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <!--<ItemGroup>
    <Analyzer Include="..\packages\xunit.analyzers.0.10.0\analyzers\dotnet\cs\xunit.analyzers.dll" />
  </ItemGroup>-->
  <ItemGroup>
    <PackageReference Include="CPHI.Monitor.PostLogs" Version="1.0.1" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Figgle" Version="0.5.1" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.61" />
    <PackageReference Include="Microsoft.Exchange.WebServices.NETStandard" Version="1.1.3" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="Quartz" Version="3.10.0" />
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.421302">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageReference Include="System.Data.Odbc" Version="8.0.0" />
    <PackageReference Include="System.ServiceModel.Syndication" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aspose.Cells" Version="24.6.0" />
    <PackageReference Include="DotNetSeleniumExtras.WaitHelpers" Version="3.11.0" />
    <PackageReference Include="EPPlus" Version="*******" />
    <PackageReference Include="log4net" Version="2.0.17" />
    <PackageReference Include="Selenium.Support" Version="4.22.0" />
    <PackageReference Include="Selenium.WebDriver.ChromeDriver" Version="134.0.6998.3500" />
    <PackageReference Include="Selenium.WebDriver.GeckoDriver" Version="0.34.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="Topshelf" Version="4.3.0" />
    <PackageReference Include="WinSCP" Version="6.3.4" />
    <PackageReference Include="xunit" Version="2.8.1" />
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CPHI.Spark.Model\CPHI.Spark.Model.csproj" />
    <ProjectReference Include="..\CPHI.Spark.Repository\CPHI.Spark.Repository.csproj" />
  </ItemGroup>
</Project>