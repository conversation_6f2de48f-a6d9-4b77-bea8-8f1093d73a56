﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using Microsoft.Extensions.Caching.Memory;
using MoreLinq;
using StockPulse.WebApi.Model.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess;
using CPHI.Repository;
using Microsoft.Identity.Client;
using Microsoft.EntityFrameworkCore;

namespace CPHI.Spark.WebApp.DataAccess
{
   public interface IGlobalParamDataAccess
   {
      Task<IEnumerable<GlobalParam>> GetAll(Model.DealerGroupName dealerGroup);
      Task UpdateAutotraderDateToNow(DealerGroupName dealerGroup);
      Task<bool> ValueVehicleAtAllSites(DealerGroupName dealerGroup);
   }

   public class GlobalParamDataAccess : IGlobalParamDataAccess
   {
      private readonly string _connectionString;

      public GlobalParamDataAccess(string connectionString)
      {
         _connectionString = connectionString;
      }

      // public async Task TempClearDbDown()
      // {
      //     using (var dapper = new DADapperr(_connectionString))
      //     {
      //         DateTime startDate = new DateTime(2024, 2, 7);
      //         while (startDate < new DateTime(2024, 7, 1) && DateTime.Now < new DateTime(2024, 7, 27, 5, 00, 00))
      //         {
      //             await dapper.ExecuteAsync("CleardownValuations", new Dapper.DynamicParameters(new {startDate = startDate }), System.Data.CommandType.StoredProcedure);
      //             startDate = startDate.AddDays(1);
      //         }
      //     }
      // }

      //UnifiedDB - no update required
      public async Task<bool> ValueVehicleAtAllSites(DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var dbValue = await db.GlobalParams.FirstOrDefaultAsync(x => x.Description == "ValueVehicleAtAllSites" && x.DealerGroup_Id == (int)dealerGroup);
            return dbValue != null ? dbValue.TextValue == "True" : false;
         }
      }

      //UnifiedDB - no update required
      public async Task<IEnumerable<GlobalParam>> GetAll(Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            return await dapper.GetAllAsync<GlobalParam>(@$"

            SELECT [ID],
                  [DateFrom],
                  [DateTo],
                  [value] as Value,
                  [textValue] as TextValue,
                  [Description]
               FROM [dbo].[GlobalParams] 
               WHERE DealerGroup_Id = {(int)dealerGroup}
            ", null, System.Data.CommandType.Text);
         }
      }

      //UnifiedDB - no update required
      public async Task<LastUpdatedDates> GetLastUpdateDates(Model.DealerGroupName dealerGroupName)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var res = await dapper.GetAsync<LastUpdatedDates>("GET_LastUpdateDates", new Dapper.DynamicParameters(new { dealerGroupId = (int)dealerGroupName }), System.Data.CommandType.StoredProcedure);
            if (res == null)
            {
               return new LastUpdatedDates();
            }
            else
            {
               return res;
            }
         }
      }

      //UnifiedDB - no update required
      public async Task UpdateAutotraderDateToNow(DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var globalParamDescription = "autotraderAdvertsUpdateDate";
            var dbItem = db.GlobalParams.FirstOrDefault(x => x.Description == globalParamDescription && x.DealerGroup_Id == (int)dealerGroup);
            if (dbItem != null)
            {
               dbItem.DateFrom = DateTime.UtcNow;
               await db.SaveChangesAsync();
            }
            else
            {
               throw new Exception($"Global param not found for dealergroup {dealerGroup}");
            }
         }
      }
   }

}
