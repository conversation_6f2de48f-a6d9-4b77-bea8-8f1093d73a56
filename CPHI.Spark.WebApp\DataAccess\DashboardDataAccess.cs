﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using Dapper;
using Microsoft.Exchange.WebServices.Data;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.DataAccess
{
   public interface IDashboardDataAccess
   {
      Task<IEnumerable<ServiceChannelDataItem>> GetServiceChannelSummariesData(string siteIds, DateTime monthCommencing, int userId, bool showAllSites, Model.DealerGroupName dealerGroup);
      Task<decimal> GetServiceMonthlyTarget(string siteIds, DateTime monthCommencing, int userId, bool showAllSites, bool onlyLabour, Model.DealerGroupName dealerGroup);
      Task<decimal> GetServiceMonthlyTargetSpain(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<ServiceDailyDone>> GetServiceDailyDone(string siteIds, DateTime monthCommencing, int userId, bool showAllSites, bool onlyLabour, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<ServiceDailyDone>> GetServiceDailyDoneSpain(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<ServiceChannelSplit>> GetServiceChannelSplits(string siteIds, DateTime monthCommencing, int userId, bool showAllSites, Model.DealerGroupName dealerGroup);
      Task<WipSummary> GetWipSummary(string siteIds, int userId, bool showAllSites, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<WipSummaryBySite>> GetWipSummaryBySite(int userId, bool showAllSites, Model.DealerGroupName dealerGroup);
      Task<UsedStockHealth> GetUsedStockHealth(string siteIds, Model.DealerGroupName dealerGroup);
      //Task<IEnumerable<RegistrationsSummary>> GetRegistrationsSummary(string siteIds);
      Task<IEnumerable<UsedStockMerchandising>> GetUsedStockMerchandising(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<ActivityLevelsSummaryItem>> GetActivityLevels(DateTime startDate, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<OverdueSummaryItem>> GetOverdueSummary(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<DepartmentDealBreakdown>> GetDepartmentDealBreakdowns(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<SiteCompareRow>> GetSiteCompareSalesPerf(string department, bool showMargin, string timePeriod, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<SiteCompareRow>> GetSiteCompareAfterSalesPerf(string department, bool showMargin, string timePeriod, bool includeNonLabour, Model.DealerGroupName dealerGroup);
      Task<EvhcQuotedAndSold> GetEvhcSummaryRedAmberConverted(string siteIds, int userId, Model.DealerGroupName dealerGroup);
      Task<EvhcDoneVsWips> GetEvhcSummaryDoneVsWips(string siteIds, int userId, Model.DealerGroupName dealerGroup);
      Task<CitNowVsWips> GetCitNowSummary(string siteIds, int userId, Model.DealerGroupName dealerGroup);

      Task<IEnumerable<AgedWipLine>> GetAgedWipsSummary(string siteIds, int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<FinanceAddOnSummaryDataItem>> GetFinanceAddonSummary(string siteIds, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<SiteCompareRow>> GetSiteCompareTodayMap(string department, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<DailyNetOrdersCancellationsCount>> GetDailyNetOrdersCancellations(DateTime weekStart, Model.DealerGroupName dealerGroup);
      Task<DeliveredInTimeStat> GetDeliveredInTime(string siteIds, Model.DealerGroupName dealerGroup);
      Task<CitNowVsWips> GetCitNowSummaryVindis(string siteIds, int userId, Model.DealerGroupName dealerGroup);

      // Spain
      Task<IEnumerable<InvoicedProfit>> GetInvoicedProfit(string type, DateTime fromDate, DateTime toDate, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<UnitInvoicingVsTarget>> GetUnitInvoicingVsTarget(string type, DateTime fromDate, DateTime toDate, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<OverageStockSummaryItem>> GetOverageStockSummary(string siteIds, List<string> franchiseCodes, DateTime? chosenMonth, Model.DealerGroupName dealerGroup);
      Task<ReconditioningSummary> GetVehicleReconditioningTile(int userId, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup, DateTime? stockfileDate = null);
      Task<UnitsAndValue> GetFixedAssetVehicles(int userId, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup, DateTime? stockfileDate = null);
      Task<UnitsAndValue> GetScrapVehicles(int userId, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup, DateTime? stockfileDate = null);
      Task<IEnumerable<DailyNetOrder>> GetDailyNetOrders(DateTime weekStart, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<SpainDailyNetOrderItem>> GetSpainDailyNetOrdersUsed(List<string> orderTypeTypes, DateTime weekStart, IEnumerable<int> siteIdsInts, int userId, string[] franchises, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<SpainDailyNetOrderItem>> GetSpainDailyNetOrdersNew(List<string> orderTypeTypes, DateTime weekStart, IEnumerable<int> siteIdsInts, int userId, string[] franchises, Model.DealerGroupName dealerGroup);
      //Task<AlcopasSummary> GetAlcopaMonthSummary(AlcopaParams parms, int userId);
      Task<IEnumerable<DonutMonthlyData>> GetPerformanceForTheMonthDonuts(DateTime fromDate, string defaultOrderTypeType, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<OverageStockSummaryItem>> GetOverageStockSummarySpain(List<int> siteIdsInts, List<string> franchiseCodes, DateTime chosenMonth, int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<GlobalParamLastUpdated>> GetDataOriginUpdates(Model.DealerGroupName dealerGroup);

      Task<DateTime> GetMostRecentDateInDailyOrders(Model.DealerGroupName dealerGroup);

      Task<IEnumerable<TechControlAndConvRates>> GetPercentages(string siteIds, int userId, DateTime chosenMonth, Model.DealerGroupName dealerGroup);

      Task<IEnumerable<WipSummary>> GetWipSummariesAftersalesSpain(string siteIds, Model.DealerGroupName dealerGroup);

      Task<IEnumerable<AftersalesKPIRow>> GetDashboardDataSpainAftersalesKPIs(DashboardDataSpainAftersalesParams parms, Model.DealerGroupName dealerGroup);

      Task<IEnumerable<AftersalesRunRateSummary>> GetRunRatesSpainAftersales(List<int> siteIdsInts, List<string> franchiseCodes, DateTime chosenMonth, Model.DealerGroupName dealerGroup);
      Task<TurnoverPerOperative> GetTurnoverPerOperative(string siteIds, int userId, DateTime chosenMonth, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<AssignmentVM>> GetAssigmentVehicles(int userId, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup, DateTime? stockfileDate = null);
      Task<IEnumerable<UsedStockMerchandisingBySiteItem>> GetUsedStockMerchandisingBySite(Model.DealerGroupName dealerGroup);
      Task<IEnumerable<UsedDeliveredRow>> GetUsedDeliveredTile(string siteIds, Model.DealerGroupName dealerGroup);
   }

   public class DashboardDataAccess : IDashboardDataAccess
   {
      private readonly IDapperr dapper;
      private readonly IStandingValuesDataAccess standingValuesDataAccess;


      public DashboardDataAccess(IDapperr dapper, IStandingValuesDataAccess standingValuesDataAccess)
      {
         this.dapper = dapper;
         this.standingValuesDataAccess = standingValuesDataAccess;
      }

      // unifiedDB - TDOO: KpiTempTable table only exists in spain and is created via SP [GET_DashboardDataSpainAftersalesKPIs], seems like a odd way to build a table. Discuss. Make it dealergroup specific, or join with Site table here. 
      public async Task<IEnumerable<AftersalesKPIRow>> GetDashboardDataSpainAftersalesKPIs(DashboardDataSpainAftersalesParams parms, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<AftersalesKPIRow>($"SELECT * FROM KpiTempTable WHERE Year = {parms.MonthStart.Year} AND Month = {parms.MonthStart.Month}", null, dealerGroup, CommandType.Text);
      }


      //UnifiedDB - SP Updated, TODO: dont think we need sperate SPs for each dealer group
      public async Task<IEnumerable<DonutMonthlyData>> GetPerformanceForTheMonthDonuts(DateTime fromDate, string defaultOrderTypeType, Model.DealerGroupName dealerGroup)
      {
         if (dealerGroup == Model.DealerGroupName.Vindis)
         {
            return await dapper.GetAllAsync<DonutMonthlyData>("dbo.GET_PerformanceForTheMonthDonutsVindis", new DynamicParameters(new { fromDate, defaultOrderTypeType }), dealerGroup);
         }
         else if (dealerGroup == Model.DealerGroupName.RRGSpain)
         {
            return await dapper.GetAllAsync<DonutMonthlyData>("dbo.GET_PerformanceForTheMonthDonutsSpain", new DynamicParameters(new { fromDate, defaultOrderTypeType }), dealerGroup);
         }
         else
         {
            return await dapper.GetAllAsync<DonutMonthlyData>("dbo.GET_PerformanceForTheMonthDonuts", new DynamicParameters(new { fromDate, defaultOrderTypeType, dealerGroupId = (int)dealerGroup }), dealerGroup);

         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<UsedDeliveredRow>> GetUsedDeliveredTile(string siteIds, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<UsedDeliveredRow>("dbo.GET_UsedDeliveredTile", new DynamicParameters(new { siteIds, dealerGroupId = (int)dealerGroup }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<DepartmentDealBreakdown>> GetDepartmentDealBreakdowns(Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<DepartmentDealBreakdown>("dbo.GET_DepartmentDealBreakdown", new DynamicParameters(new { dealerGroupId = (int)dealerGroup }), dealerGroup);
      }


      //unifiedDB -on update required
      public async Task<IEnumerable<TechControlAndConvRates>> GetPercentages(string siteIds, int userId, DateTime chosenMonth, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<TechControlAndConvRates>("dbo.GET_TechControlAndConvRates", new DynamicParameters(new { siteIds, chosenMonth }), dealerGroup);
      }


      //unifiedDB -on update required
      public async Task<IEnumerable<FinanceAddOnSummaryDataItem>> GetFinanceAddonSummary(string siteIds, Model.DealerGroupName dealerGroup)
      {

         return await dapper.GetAllAsync<FinanceAddOnSummaryDataItem>("dbo.GET_FinanceAddOnSummary", new DynamicParameters(new { sites = siteIds }), dealerGroup);
      }


      //unifiedDB - updated
      public async Task<IEnumerable<DailyNetOrder>> GetDailyNetOrders(DateTime weekStart, Model.DealerGroupName dealerGroup)
      {
         var paramList = new DynamicParameters();

         paramList.Add("WeekStart", weekStart);
         paramList.Add("DealerGroupId", (int)dealerGroup);


         return await dapper.GetAllAsync<DailyNetOrder>("dbo.GET_DailyNetOrderByDepartmentSets", paramList, dealerGroup, CommandType.StoredProcedure);
      }

      //unifiedDB - updated
      public async Task<IEnumerable<DailyNetOrdersCancellationsCount>> GetDailyNetOrdersCancellations(DateTime weekStart, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<DailyNetOrdersCancellationsCount>("dbo.GET_DailyOrders", new DynamicParameters(new { startDate = weekStart, dealerGroupId = (int)dealerGroup }), dealerGroup);
      }

      //unifiedDB - updated
      public async Task<IEnumerable<UsedStockMerchandising>> GetUsedStockMerchandising(Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<UsedStockMerchandising>("dbo.GET_UsedStockMerchandising", new DynamicParameters(new { dealerGroupId = (int)dealerGroup }), dealerGroup);
      }

      //unifiedDB - updated
      public async Task<IEnumerable<UsedStockMerchandisingBySiteItem>> GetUsedStockMerchandisingBySite(Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<UsedStockMerchandisingBySiteItem>("dbo.GET_UsedMerchandisingBySite", new DynamicParameters(new { dealerGroupId = (int)dealerGroup }), dealerGroup);
      }

      //unifiedDB - updated
      public async Task<IEnumerable<OverdueSummaryItem>> GetOverdueSummary(Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<OverdueSummaryItem>("dbo.GET_OverdueSummary", new DynamicParameters(new { dealerGroupId = (int)dealerGroup }), dealerGroup);
      }


      //unifiedDB - updated
      public async Task<IEnumerable<ActivityLevelsSummaryItem>> GetActivityLevels(DateTime startDate, Model.DealerGroupName dealerGroup)
      {
         var paramList = new DynamicParameters();

         paramList.Add("WeekStart", startDate);
         paramList.Add("dealerGroupId", (int)dealerGroup);

         return await dapper.GetAllAsync<ActivityLevelsSummaryItem>("dbo.GET_ActivityLevels", paramList, dealerGroup, CommandType.StoredProcedure);
      }

      //unifiedDB - updated
      public async Task<IEnumerable<ServiceChannelDataItem>> GetServiceChannelSummariesData(string siteIds, DateTime monthCommencing, int userId, bool showAllSites, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<ServiceChannelDataItem>("dbo.GET_ServiceChannelSummariesData", new DynamicParameters(new { siteIdsString = siteIds, monthCommencing, userId, showAllSites }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<WipSummary> GetWipSummary(string siteIds, int userId, bool showAllSites, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<WipSummary>("dbo.GET_WipSummary", new DynamicParameters(new { siteIdsString = siteIds, userId, showAllSites }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<WipSummary>> GetWipSummariesAftersalesSpain(string siteIds, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<WipSummary>("dbo.GET_WipSummariesAftersalesSpain", new DynamicParameters(new { siteIds }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<WipSummaryBySite>> GetWipSummaryBySite(int userId, bool showAllSites, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<WipSummaryBySite>("dbo.GET_WipSummaryBySite", new DynamicParameters(new { userId, showAllSites }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<ServiceDailyDone>> GetServiceDailyDone(string siteIds, DateTime monthCommencing, int userId, bool showAllSites, bool onlyLabour, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<ServiceDailyDone>("dbo.GET_ServiceDailyDone", new DynamicParameters(new { siteIdsString = siteIds, monthCommencing, userId, showAllSites, onlyLabour }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<ServiceDailyDone>> GetServiceDailyDoneSpain(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<ServiceDailyDone>("dbo.GET_ServiceDailyDoneSpain", new DynamicParameters(new { siteIdsString = siteIds, monthCommencing }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<ServiceChannelSplit>> GetServiceChannelSplits(string siteIds, DateTime monthCommencing, int userId, bool showAllSites, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<ServiceChannelSplit>("dbo.GET_ServiceChannelSplits", new DynamicParameters(new { siteIdsString = siteIds, monthCommencing, userId, showAllSites }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<decimal> GetServiceMonthlyTarget(string siteIds, DateTime monthCommencing, int userId, bool showAllSites, bool onlyLabour, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<decimal>("dbo.GET_ServiceMonthlyTarget", new DynamicParameters(new { siteIdsString = siteIds, monthCommencing, userId, showAllSites, onlyLabour }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<decimal> GetServiceMonthlyTargetSpain(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<decimal>("dbo.GET_ServiceMonthlyTargetSpain", new DynamicParameters(new { siteIdsString = siteIds, monthCommencing }), dealerGroup);
      }

      //Unified: TODO in SP
      public async Task<UsedStockHealth> GetUsedStockHealth(string siteIds, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<UsedStockHealth>("dbo.GET_UsedStockHealth", new DynamicParameters(new { Sites = siteIds, environment = dealerGroup.ToString() }), dealerGroup);
      }

      /*
      public async Task<IEnumerable<RegistrationsSummary>> GetRegistrationsSummary(string siteIds)
      {
          var paramList = new DynamicParameters();

          paramList.Add("Sites", siteIds);

          //return new List<RegistrationsSummary>() { 
          //    new RegistrationsSummary() {  Brand = "Renault", Target = 0, Registered = 0 } ,
          //    new RegistrationsSummary() {  Brand = "Alpine", Target = 0, Registered = 0 } ,
          //    new RegistrationsSummary() {  Brand = "Nissan", Target = 0, Registered = 0 } ,
          //    new RegistrationsSummary() {  Brand = "Dacia", Target = 0, Registered = 0 } 

          //};

          return await dapper.GetAllAsync<RegistrationsSummary>("dbo.GET_RegistrationsSummary", paramList, dealerGroup, CommandType.StoredProcedure);
      }
      */

      //UnifiedDB - updated
      public async Task<IEnumerable<OverageStockSummaryItem>> GetOverageStockSummary(string siteIds, List<string> franchiseCodes, DateTime? chosenMonth, Model.DealerGroupName dealerGroup)
      {

         string clientConnectionName = string.Empty;

         switch (dealerGroup)
         {
            //case DealerGroupName.None:
            //    break;
            case Model.DealerGroupName.RRGUK:
               clientConnectionName = DealerGroupConnectionName.GetConnectionName(Model.DealerGroupName.RRGUK);
               break;
            case Model.DealerGroupName.RRGSpain:
               clientConnectionName = DealerGroupConnectionName.GetConnectionName(Model.DealerGroupName.RRGSpain);
               break;
            case Model.DealerGroupName.Vindis:
               clientConnectionName = DealerGroupConnectionName.GetConnectionName(Model.DealerGroupName.Vindis);
               break;
            default:
               break;
         }

         var allCustomerFranchises = await standingValuesDataAccess.GetFranchiseCodes(dealerGroup);

         var paramList = new DynamicParameters();

         string franCodesString = null;
         if (franchiseCodes != null)
         {
            franCodesString = string.Join(",", franchiseCodes);
         }
         paramList.Add("Sites", siteIds);
         paramList.Add("FranCodesString", franCodesString);
         paramList.Add("chosenMonth", chosenMonth);
         paramList.Add("allCustomerFranchises", allCustomerFranchises);
         paramList.Add("dealerGroupId", (int)dealerGroup);

         return await dapper.GetAllAsync<OverageStockSummaryItem>("dbo.GET_OverageStockSummary", paramList, dealerGroup, CommandType.StoredProcedure);
      }



      //UnifiedDB - TODO: SP does not exists !!
      public async Task<IEnumerable<AftersalesRunRateSummary>> GetRunRatesSpainAftersales(List<int> siteIdsInts, List<string> franchiseCodes, DateTime chosenMonth, Model.DealerGroupName dealerGroup)
      {
         string siteIds = string.Join(",", siteIdsInts);
         var paramList = new DynamicParameters();

         DateTime? monthToUse = null;

         if (chosenMonth.Month != DateTime.Now.Month || chosenMonth.Year != DateTime.Now.Year)
         {
            monthToUse = chosenMonth.Date;
         }

         string franCodesString = null;

         if (franchiseCodes != null)
         {
            franCodesString = string.Join(",", franchiseCodes);
         }

         paramList.Add("Sites", siteIds);
         paramList.Add("FranCodesString", franCodesString);
         paramList.Add("ChosenMonth", monthToUse);


         return await dapper.GetAllAsync<AftersalesRunRateSummary>("dbo.GET_RunRatesSpainAftersales", paramList, dealerGroup, CommandType.StoredProcedure);
      }

      //UnifiedDB - TODO: SP update required
      public async Task<IEnumerable<OverageStockSummaryItem>> GetOverageStockSummarySpain(List<int> siteIdsInts, List<string> franchiseCodes, DateTime chosenMonth, int userId, Model.DealerGroupName dealerGroup)
      {
         string siteIds = string.Join(",", siteIdsInts);
         var paramList = new DynamicParameters();

         DateTime? monthToUse = null;
         if (chosenMonth.Month != DateTime.Now.Month || chosenMonth.Year != DateTime.Now.Year)
         {
            monthToUse = chosenMonth.Date;
         }

         string franCodesString = null;
         if (franchiseCodes != null)
         {
            franCodesString = string.Join(",", franchiseCodes);
         }
         paramList.Add("Sites", siteIds);
         paramList.Add("FranCodesString", franCodesString);
         paramList.Add("ChosenMonth", monthToUse);


         return await dapper.GetAllAsync<OverageStockSummaryItem>("dbo.GET_OverageStockSummarySpain", paramList, dealerGroup, CommandType.StoredProcedure);
      }


      //UnifiedDB - no update required
      public async Task<IEnumerable<SpainDailyNetOrderItem>> GetSpainDailyNetOrdersUsed(List<string> orderTypeTypes, DateTime weekStart, IEnumerable<int> siteIdsInts, int userId, string[] franchises, Model.DealerGroupName dealerGroup)
      {
         string orderTypeTypesStr = string.Join(",", orderTypeTypes);
         string siteIds = string.Join(",", siteIdsInts);
         string franchiseStr = string.Join(",", franchises);
         return await dapper.GetAllAsync<SpainDailyNetOrderItem>("dbo.GET_DailyNetOrdersUsedSpain", new DynamicParameters(new { weekStart, siteIds, userId, franchises = franchiseStr, orderTypeTypes = orderTypeTypesStr }), dealerGroup, CommandType.StoredProcedure);
      }

      //UnifiedDB - no update required
      public async Task<IEnumerable<SpainDailyNetOrderItem>> GetSpainDailyNetOrdersNew(List<string> orderTypeTypes, DateTime weekStart, IEnumerable<int> siteIdsInts, int userId, string[] franchises, Model.DealerGroupName dealerGroup)
      {
         string orderTypeTypesStr = string.Join(",", orderTypeTypes);
         string siteIds = string.Join(",", siteIdsInts);
         string franchiseStr = string.Join(",", franchises);
         return await dapper.GetAllAsync<SpainDailyNetOrderItem>("dbo.GET_DailyNetOrdersNewSpain", new DynamicParameters(new { weekStart, siteIds, userId, franchises = franchiseStr, orderTypeTypes = orderTypeTypesStr }), dealerGroup, CommandType.StoredProcedure);
      }

      //UnifiedDB - no update required
      public async Task<DeliveredInTimeStat> GetDeliveredInTime(string siteIds, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<DeliveredInTimeStat>("dbo.GET_DeliveredInTimeStat", new DynamicParameters(new { siteIds }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<SiteCompareRow>> GetSiteCompareSalesPerf(string department, bool showMargin, string timePeriod, Model.DealerGroupName dealerGroup)
      {
         var paramList = new DynamicParameters();

         paramList.Add("Department", department);
         paramList.Add("ShowMargin", showMargin);
         paramList.Add("TimePeriod", timePeriod);
         paramList.Add("DealerGroupId", (int)dealerGroup);

         
         return await dapper.GetAllAsync<SiteCompareRow>("dbo.GET_SiteCompareSalesPerf", paramList, dealerGroup, CommandType.StoredProcedure);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<SiteCompareRow>> GetSiteCompareAfterSalesPerf(string department, bool showMargin, string timePeriod, bool includeNonLabour, Model.DealerGroupName dealerGroup)
      {
         var paramList = new DynamicParameters();

         paramList.Add("Department", department);
         paramList.Add("TimePeriod", timePeriod);
         paramList.Add("IncludeNonLabour", includeNonLabour);
         paramList.Add("DealerGroupId", (int)dealerGroup);

         return await dapper.GetAllAsync<SiteCompareRow>("dbo.GET_SiteCompareAfterSalesPerf", paramList, dealerGroup, CommandType.StoredProcedure);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<SiteCompareRow>> GetSiteCompareTodayMap(string department, Model.DealerGroupName dealerGroup)
      {
         var paramList = new DynamicParameters();

         paramList.Add("Department", department);
         paramList.Add("CompareYesterday", dealerGroup == Model.DealerGroupName.RRGSpain ? 1 : 0);
         paramList.Add("DealerGroupId", (int)dealerGroup);

         return await dapper.GetAllAsync<SiteCompareRow>("dbo.GET_SiteCompareTodayMap", paramList, dealerGroup, CommandType.StoredProcedure);
      }



      //UnifiedDB - no update required
      public async Task<EvhcQuotedAndSold> GetEvhcSummaryRedAmberConverted(string siteIds, int userId, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<EvhcQuotedAndSold>("dbo.GET_EvhcSummaryRedAmberConverted", new DynamicParameters(new { siteIds, userId }), dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<EvhcDoneVsWips> GetEvhcSummaryDoneVsWips(string siteIds, int userId, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<EvhcDoneVsWips>("dbo.GET_EvhcSummaryDoneVsWips", new DynamicParameters(new { siteIds, userId }), dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<CitNowVsWips> GetCitNowSummary(string siteIds, int userId, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<CitNowVsWips>("dbo.GET_CitNowSummary", new DynamicParameters(new { siteIds, userId }), dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<CitNowVsWips> GetCitNowSummaryVindis(string siteIds, int userId, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<CitNowVsWips>("dbo.GET_CitNowSummaryVindis", new DynamicParameters(new { siteIds, userId }), dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<IEnumerable<AgedWipLine>> GetAgedWipsSummary(string siteIds, int userId, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<AgedWipLine>("dbo.GET_AgedWipsSummary", new DynamicParameters(new { siteIds, userId }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<GlobalParamLastUpdated>> GetDataOriginUpdates(Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<GlobalParamLastUpdated>("dbo.GET_DataOriginUpdates", new DynamicParameters(new { dealerGroupId = (int)dealerGroup}), dealerGroup);
      }

      //UnifiedDB - updated: TODO, SP needs updating
      public async Task<IEnumerable<InvoicedProfit>> GetInvoicedProfit(string type, DateTime fromDate, DateTime toDate, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<InvoicedProfit>("dbo.GET_InvoicedProfit", new DynamicParameters(new { type, fromDate, toDate, siteIds, franCodesString = string.Join(",", franchiseCodes), dealerGroupId = (int)dealerGroup }), dealerGroup);
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<UnitInvoicingVsTarget>> GetUnitInvoicingVsTarget(string type, DateTime fromDate, DateTime toDate, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAllAsync<UnitInvoicingVsTarget>("dbo.GET_UnitInvoicingVsTarget", new DynamicParameters(new { type, fromDate, toDate, siteIds, franCodesString = string.Join(",", franchiseCodes), dealerGroupId = (int)dealerGroup }), dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<UnitsAndValue> GetScrapVehicles(int userId, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup, DateTime? firstDayOfMonth = null)
      {
         return await dapper.GetAsync<UnitsAndValue>("dbo.GET_ScrapVehiclesTile", new DynamicParameters(new { UserId = userId, sites = siteIds, franCodesString = string.Join(",", franchiseCodes), firstDayOfMonth }), dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<UnitsAndValue> GetFixedAssetVehicles(int userId, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup, DateTime? firstDayOfMonth = null)
      {
         return await dapper.GetAsync<UnitsAndValue>("dbo.GET_FixedAssetVehiclesTile", new DynamicParameters(new { UserId = userId, sites = siteIds, franCodesString = string.Join(",", franchiseCodes), firstDayOfMonth }), dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<IEnumerable<AssignmentVM>> GetAssigmentVehicles(int userId, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup, DateTime? firstDayOfMonth = null)
      {
         return await dapper.GetAllAsync<AssignmentVM>("dbo.GET_AssigmentVehiclesTile", new DynamicParameters(new { UserId = userId, sites = siteIds, franCodesString = string.Join(",", franchiseCodes), firstDayOfMonth }), dealerGroup);
      }

      //UnifiedDB - no update required
      public async Task<ReconditioningSummary> GetVehicleReconditioningTile(int userId, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup, DateTime? firstDayOfMonth = null)
      {
         return await dapper.GetAsync<ReconditioningSummary>("dbo.GET_VehicleReconditioningTile", new DynamicParameters(new { UserId = userId, sites = siteIds, franCodesString = string.Join(",", franchiseCodes), firstDayOfMonth }), dealerGroup);
      }

      //public async Task<AlcopasSummary> GetAlcopaMonthSummary(AlcopaParams parms, int userId)
      //{
      //    string siteIds = string.Join(',', parms.SiteIds);
      //    // userId = 7099;

      //    return await dapper.GetAsync<AlcopasSummary>("dbo.GET_AlcopaMonthSummary", new DynamicParameters(new { siteIds, userId }));
      //}


      //UnifiedDB - TODO: DailyOrders has no dealergroup reference 
      public async Task<DateTime> GetMostRecentDateInDailyOrders(Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<DateTime>("SELECT TOP 1 Day FROM DailyOrders ORDER BY Day DESC", null, dealerGroup, CommandType.Text);
      }

      //UnifiedDB - TODO: SP needs updating
      public async Task<TurnoverPerOperative> GetTurnoverPerOperative(string siteIds, int userId, DateTime chosenMonth, Model.DealerGroupName dealerGroup)
      {
         return await dapper.GetAsync<TurnoverPerOperative>("dbo.GET_TurnoverPerOperative", new DynamicParameters(new { siteIdsString = siteIds, userId, monthCommencing = chosenMonth }), dealerGroup);
      }

   }
}
