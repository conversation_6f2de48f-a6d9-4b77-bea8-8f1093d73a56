<div class="tile-inner">
  <div class="tile-header">
    Strategy Price Build-up
  </div>
  <div class="tile-body">
    <table style="margin-bottom: 7px;">

      <tr>
        <td colspan="3">AutoTrader Valuation
          <span>
                        ({{ service.modalItem.AdvertDetail.ThisVehicleValnVsAverage|cph:'currency':0:true }} vs avg vehicle)</span>
        </td>
        <td class="text-end">{{ service.modalItem.AdvertDetail.RelevantValuation | cph:'currency':0 }}</td>
      </tr>

      <!-- The various layers of build-up -->
    </table>

    <strategyPriceBuildUpLayers [params]="buildTheBuildUpLayers"></strategyPriceBuildUpLayers>

    <div style="margin-top: 5px; padding-bottom: 5px; border-bottom: 1px solid #ccc;"></div>

    <table>
      <!-- Strategy price row at bottom -->
      <tr>
        <td colspan="2">
          Strategy Price
        </td>
        <td class="percentage-column">
          <span>{{ strategyPrice /
          service.modalItem.AdvertDetail.RelevantValuation|cph:'percent1dp':1
            }}</span>

        </td>
        <td class="impact-column">
          {{ strategyPrice | cph:'currency':0 }}
        </td>
      </tr>
      <tr>
        <td colspan="4">&nbsp;</td>
      </tr>
      <tr *ngIf="!useTestStrategy">

        <td colspan="3">
          <autoTraderVsStrategyPrice *ngIf="service.modalItem.AdvertDetail.showVsStrategyLozenge"
                                     [vsStrategyBanding]="service.modalItem.AdvertDetail.VsStrategyBanding">
          </autoTraderVsStrategyPrice>
        </td>
        <td class="text-end font-weight-bold">
          <span class="fw-bold nowrap"
                      [ngClass]="{ 'text-danger': (service.modalItem.AdvertDetail.AdvertisedPrice - strategyPrice) < 0 }">
                        {{ service.modalItem.AdvertDetail.AdvertisedPrice - strategyPrice | cph:'currency':0 }}
                    </span>
        </td>
      </tr>

    </table>


  </div>
</div>

