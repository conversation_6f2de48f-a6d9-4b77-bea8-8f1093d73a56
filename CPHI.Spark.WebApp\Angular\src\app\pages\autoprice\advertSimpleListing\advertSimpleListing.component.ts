import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AutotraderService } from 'src/app/services/autotrader.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { AdvertSimpleListingService } from './advertSimpleListing.service';
import { AutopriceCheckboxParams } from 'src/app/model/AutopriceCheckboxParams';
import { VehicleAdvertFilterResult } from 'src/app/model/VehicleAdvertFilterResult';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

export interface SortField {
  pretty: string;
  ugly: string;
  type: string;
  sortAscending: boolean;
}

@Component({
  selector: 'advertSimpleListing',
  templateUrl: './advertSimpleListing.component.html',
  styleUrls: ['./advertSimpleListing.component.scss']
})
export class AdvertSimpleListingComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  constructor(
    public selections: SelectionsService,
    public service: AdvertSimpleListingService,
    public modalService: NgbModal,
    public constants: ConstantsService
  ) { }

  ngOnInit(): void {

    this.selections.triggerSpinner.next({ show: false });
    this.service.initParams();
    this.service.getData();

    this.service.newChoicesMade
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        this.service.onNewChoicesMade(res);
      });

    this.service.filterSortManagementParams.refreshRowsEmitter
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        this.service.reFilterItems(false);

        if (this.service.filterSortManagementParams.chosenSortField) {
          this.service.sortBy(this.service.filterSortManagementParams.chosenSortField);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  onChosenVehTypesChange() {
    this.clearFilters();
    this.service.filterByVehicleAds();
  }

  provideVehTypeCount(vehType: string) {

    // let temp = this.service.rowData.filter(x => x.VehicleTypeDesc != 'Used' && x.VehicleTypeDesc != 'Demonstrators');

    if (this.service.rowData) {
      return this.service.rowData.filter(x => x.VehicleTypeDesc == vehType).length;
    }
    else {
      return 0;
    }
  }

  toggleIncludeNewVehicles(): void {
    this.service.includeNewVehicles = !this.service.includeNewVehicles;
    this.service.getData();
  }

  toggleIncludeUnPublishedAds(): void {
    this.service.includeUnPublishedAds = !this.service.includeUnPublishedAds;
    this.service.getData();
  }

  toggleUseTestStrategy(): void
  {
    this.service.useTestStrategy = !this.service.useTestStrategy;
    this.service.getData();
  }

 

  generateParams(field: string, label: string): AutopriceCheckboxParams {

    let sortOrder: string[] = [];

    if (field == 'RetailRatingBand') { sortOrder = AutotraderService.getSortOrderForRetailRatingBand() }
    if (field == 'VsStrategyBanding') { sortOrder = AutotraderService.getSortOrderForVsStrategyBanding() }
    if (field == 'PriceIndicatorRatingAtCurrentSelling') { sortOrder = AutotraderService.getSortOrderForPriceIndicator() }
    if (field == 'PerformanceRatingScoreBand') { sortOrder = AutotraderService.getSortOrderForPerfRating() }
    if (field == 'ImagesBand') { sortOrder = AutotraderService.getSortOrderForImagesBand() }

    let result;
    let filterModel;
    ({ filterModel, result } = this.service.generateFilterResult(this.service.rowDataAfterNavbarFilters, this.service.filterSortManagementParams));

    const filterResult: VehicleAdvertFilterResult = {
      filterModel: filterModel,
      rowData: result,
      resetAllFilters: false
    }

    return {
      field: field,
      label: label,
      initialData: filterResult,
      newChoicesMade: this.service.newChoicesMade,
      updatedData: this.service.updatedDataEmitter,
      sortOrder: sortOrder
    }

  }


  setChosenDate(event: any) {
    this.service.chosenDate = event.target.value;
    this.service.getData();
  }

  clearFilters() {
    this.service.filterSortManagementParams.filtersInPlace.map(x => {
      x.chosenValues = []
      this.service.newChoicesMade.emit(x)
    })
    this.service.filterSortManagementParams.filtersInPlace = [];
    this.service.reFilterItems(true);
  }

  onChosenLifeCycleStatusesChange() {
    this.clearFilters();
  }

  provideLifecyleCount(vehType: string) {

    // let temp = this.service.rowData.filter(x => x.VehicleTypeDesc != 'Used' && x.VehicleTypeDesc != 'Demonstrators');

    if (this.service.rowData) {
      return this.service.rowData.filter(x => x.LifecycleStatus == vehType).length;
    }
    else {
      return 0;
    }
  }

  clearSort() {
    this.service.filterSortManagementParams.chosenSortField = null;

    this.service.rowData = this.service.rowData.sort((a, b) => a.AdId - b.AdId);
    this.service.rowDataFiltered = this.service.rowDataFiltered.sort((a, b) => a.AdId - b.AdId);

    let filterResult: VehicleAdvertFilterResult = {
      filterModel: this.service.filterModel,
      rowData: this.service.rowDataFiltered
    }

    this.service.updatedDataEmitter.emit(filterResult);
  }

  public clearSearchTerm() {
    this.service.searchTerm = '';
    this.service.onSearch();
  }
  
}
