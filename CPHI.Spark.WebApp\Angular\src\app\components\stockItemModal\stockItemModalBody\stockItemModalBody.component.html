<!-- Top half panel showing lots of details -->
<div class="stockPanel">
    <div class="header">
        <div>{{ constants.translatedText.StockItemModal_VehicleDetails}}</div>
        <div class="notPrepped" *ngIf="!service.givenStockItem.IsPrepped && service.givenStockItem.ShouldBePrepped">Not Prepped!</div>
    </div>

    <div class="stockPanelBody">
        <table>
            <tbody>
                <tr>
                    <td>{{ constants.translatedText.Site }}</td>
                    <td>{{ service.givenStockItem.SiteDescription }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_PreviousSite }}</td>
                    <td>{{ service.givenStockItem.PreviousSiteDescription }}</td>
                </tr>
                <tr class="spacer">
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.Id }}</td>
                    <td>{{ service.givenStockItem.Id }}</td>
                </tr>

                <tr>
                    <td>{{ constants.translatedText.StockItemModal_Registration }}</td>
                    <td>{{ service.givenStockItem.Reg }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_Chassis }}</td>
                    <td>{{ service.givenStockItem.Chassis }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_ProgressCode }}</td>
                    <td>{{ service.givenStockItem.ProgressCode }}</td>
                </tr>
                <tr class="spacer">
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.DealDetails_RegisteredDate }}</td>
                    <td>{{ service.givenStockItem.RegDate | cph:'date':0 }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.DealDetails_StockDate }}</td>
                    <td>{{ service.givenStockItem.StockDate | cph:'date':0 }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_DaysInStock }}</td>
                    <td>{{ service.givenStockItem.DaysInStock | cph:'number':0 }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_DaysAtBranch }}</td>
                    <td>{{ service.givenStockItem.DaysAtBranch | cph:'number':0 }}</td>
                </tr>
                <tr class="spacer">
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.VehicleType }}</td>
                    <td>{{ service.givenStockItem.VehicleType }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_DisposalRoute }}</td>
                    <td>{{ service.givenStockItem.DisposalRoute }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_AccountStatus }}</td>
                    <td>{{ service.givenStockItem.AccountStatus }}</td>
                </tr>
            </tbody>
        </table>

        <table>
            <tbody>
                <tr>
                    <td>{{ constants.translatedText.Make }}</td>
                    <td>{{ service.givenStockItem.Make }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.Model }}</td>
                    <td>{{ service.givenStockItem.Model }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.ModelYear }}</td>
                    <td>{{ service.givenStockItem.ModelYear }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.Description }}</td>
                    <td>{{ service.givenStockItem.Description }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.VariantClass }}</td>
                    <td>{{ service.givenStockItem.VariantClass }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.Colour }}</td>
                    <td>{{ service.givenStockItem.Colour }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_Mileage }}</td>
                    <td>{{ service.givenStockItem.Mileage }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_Fuel }}</td>
                    <td>{{ service.givenStockItem.Fuel }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_Doors }}</td>
                    <td>{{ service.givenStockItem.Doors }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_Transmission }}</td>
                    <td>{{ service.givenStockItem.Transmission }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_Options }}</td>
                    <td>{{ service.givenStockItem.Options }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_DisposalRoute }}</td>
                    <td>{{ service.givenStockItem.DisposalRoute }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_PreviousUse }}</td>
                    <td>{{ service.givenStockItem.PreviousUse }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.Franchise }}</td>
                    <td>{{ service.givenStockItem.Franchise }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_VatQualifying }}</td>
                    <td>{{ service.givenStockItem.IsVatQ ? 'Q' : 'N' }}</td>
                </tr>
            </tbody>
        </table>

        <table>
            <tbody>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_CapId }}</td>
                    <td>{{ service.givenStockItem.CapID }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_CapCode }}</td>
                    <td>{{ service.givenStockItem.CapCode }}</td>
                </tr>
                <tr>
                    <td>{{ constants.translatedText.StockItemModal_CapNotes }}</td>
                    <td>{{ service.givenStockItem.CapNotes }}</td>
                </tr>
                <tr class="spacer">
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>Purchased</td>
                    <td>{{ service.givenStockItem.Purchased | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>NR Costs</td>
                    <td>{{ service.givenStockItem.NonRecoverableCosts | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>SIV</td>
                    <td>{{ service.givenStockItem.Siv | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>CAP Value</td>
                    <td>{{ service.givenStockItem.CapValue | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>CAP Provision</td>
                    <td>{{ service.givenStockItem.CapProvision | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>Carrying Value </td>
                    <td>{{ service.givenStockItem.CarryingValue | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>DFA </td>
                    <td>{{ service.givenStockItem.DealerFitAccessories | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>Option Costs </td>
                    <td>{{ service.givenStockItem.OptionCosts | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>Selling Price </td>
                    <td>{{ service.givenStockItem.Selling | cph:'currency':0 }}</td>
                </tr>
                <tr>
                    <td>Priced Profit </td>
                    <td>{{ service.givenStockItem.PricedProfit | cph:'currency':0 }}</td>
                </tr>
                <tr *ngIf="service.givenStockItem.StockcheckScanDate">
                    <td>Last StockChecked</td>
                    <td>{{ service.givenStockItem.StockcheckScanDate |cph:'date':0 }}</td>
                </tr>
                <tr *ngIf="service.givenStockItem.StockcheckLocation">
                    <td>Stockcheck Location</td>
                    <td>{{ service.givenStockItem.StockcheckLocation }}</td>
                </tr>
            </tbody>
        </table>

        <table *ngIf="service.lastThreeStockchecks" id="stockCheck">
            <tbody>
                <!-- Photos -->
                <tr>
                    <td colspan="2">
                        <div class="spaceBetween">
                            <div *ngFor="let scan of service.lastThreeStockchecks" class="stockTakeCard boxShadow">
                                <div class="cardHeader">
                                    {{ scan.ScanDateTime | cph:'shortTimeAM':0 }} on {{ scan.ScanDateTime | cph:'dayAndDate':0
                                    }}
                                </div>
                                <img class="boxShadow" [src]="scan.URL">
                                <div class="who">{{ scan.Name }}</div>
                                <div class="where">{{ scan.Location }}</div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

</div>

<div class="spaceBetween bottomTwoPanelsHolder">
    <!-- Bottom left panel if vehicle has price history -->
    <div class="stockPanel" id="pricingHistory">
        <div class="header">Pricing and Cost History. Priced Profit {{ service.givenStockItem.PricedProfit |
            cph:'currency':0:true }}</div>

        <div class="stockPanelBody">
            <div id="chartHolder">
                <priceChart *ngIf="service.pricingChartData.dataSets && service.pricingChartData.dataSets.length>0" [dataPointSets]="service.pricingChartData.dataSets" [labels]="service.pricingChartData.labels">
                </priceChart>
            </div>
        </div>
    </div>

    <!-- Bottom right panel if vehicle is on RRG website -->
    <div *ngIf="service.givenStockItem.IsOnWebsite" class="stockPanel" id="onlineMerchandising">
        <div class="header">
            <div id="attentionGrabber">{{ service.givenStockItem.AttentionGrabber }}</div>
            <div id="site">{{ service.givenStockItem.SiteDescription }}</div>
            <div class="regPlate" id="reg">{{ service.givenStockItem.Reg | cph:'numberPlate':0 }}</div>
        </div>

        <div class="stockPanelBody" id="merchandisingHolder">
            <span *ngIf="service.givenStockItem.IsRemoved" class="text-danger">
                *This vehicle is no longer listed on the website
            </span>
            <div id="merchCard">
                <div id="merchCardBody">
                    <!-- Picture holder -->
                    <div id="pictureHolder">
                        <button class="btn btn-primary prevNextPic previousPic" (click)="service.previousPicture()">
                            <i class="fas fa-caret-circle-left"></i>
                        </button>

                        <button class="btn btn-primary prevNextPic nextPic" (click)="service.nextPicture()">
                            <i class="fas fa-caret-circle-right"></i>
                        </button>

                        <div placement="left" container="body" [openDelay]="300" [closeDelay]="500"
                            [ngbPopover]="brokenImageLinks ? null : bigPicturePopover" triggers="mouseenter:mouseleave" popoverTitle="">
                            <img id="rrgWebsiteImage" class="boxShadow" (error)="updateUrl($event)" [src]="service.imageUrl">
                        </div>

                        <div id="pictureBottomBar">
                            <div>{{ constants.pluralise(service.imageUrls.length, 'image','images')}}</div>
                        </div>
                    </div>

                    <!-- Details on right  -->
                    <div id="details">
                        <div id="topBit">
                            <div id="priceRow">
                                <div class="price">{{ service.givenStockItem.WebsitePrice | cph:'currency':0 }}</div>
                                <div class="priceExtra ">{{ service.givenStockItem.PriceExtraLine }}</div>
                            </div>
                            <div id="yearMadeModel">
                                <div>{{ service.givenStockItem.ModelYear }}</div> &nbsp;
                                <div>{{ service.givenStockItem.Make }}</div>&nbsp;
                                <div>{{ service.givenStockItem.Model }}</div>
                            </div>
                            <div id="derivative">
                                <div>{{ service.givenStockItem.Derivative }}</div>
                            </div>
                        </div>

                        <div class="mainDetailTablesHolder">
                            <div class="tableHolder">
                                <table class="cph">
                                    <tbody>
                                        <tr>
                                            <td>Mileage</td>
                                            <td>{{ service.givenStockItem.Mileage }}</td>
                                        </tr>
                                        <tr>
                                            <td>Fuel Type</td>
                                            <td>{{ service.givenStockItem.Fuel }}</td>
                                        </tr>
                                        <tr>
                                            <td>Transmission</td>
                                            <td>{{ service.givenStockItem.Transmission }}</td>
                                        </tr>
                                        <tr>
                                            <td>Doors</td>
                                            <td>{{ service.givenStockItem.Doors }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="tableHolder">
                                <table class="cph">
                                    <tbody>
                                        <tr>
                                            <td>Body Type</td>
                                            <td>{{ service.givenStockItem.BodyType }}</td>
                                        </tr>
                                        <tr>
                                            <td>{{ constants.translatedText.Colour }}</td>
                                            <td>{{ service.givenStockItem.Colour }}</td>
                                        </tr>
                                        <tr>
                                            <td>Previous Owners</td>
                                            <td>{{ service.givenStockItem.PreviousOwners }}</td>
                                        </tr>
                                        <tr>
                                            <td>Engine Size</td>
                                            <td>{{ service.givenStockItem.EngineSize }}{{ service.givenStockItem.EngineSizeUnit }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="marketingTextHolder">
                    <div class="marketingWords">
                        {{ service.givenStockItem.AdvertDescription1 }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div *ngIf="!service.givenStockItem.IsOnWebsite && constants.environment.stockItemModal_onlineMerchandising"
        id="onlineMerchandising" class="stockPanel">
        <div class="header">Vehicle Not Listed on RRG Site</div>

        <div id="merchandisingHolder" class="stockPanelBody">
            <div id="merchCard">
                <div id="merchCardBody" class="placeholder"></div>
            </div>
        </div>
    </div>
</div>

<ng-template class="popover" #bigPicturePopover>
    <img id="rrgWebsiteImageBig" class="" [src]="service.imageUrl">
</ng-template>