﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
  public interface ILeavingService
  {
    //Task<IEnumerable<LeavingPriceSummaryItem>> GetLeavingPriceItems(GetLeavingPriceItemsParams parms);
    Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItems(GetLeavingVehicleItemsParams parms);
  }
  public class LeavingService : ILeavingService
  {
    private readonly IUserService userService;
    private readonly IConfiguration configuration;
    private readonly string _connectionString;

    public LeavingService(
        IUserService userService, IConfiguration configuration
        )
    {
      this.userService = userService;
      this.configuration = configuration;
      DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
      string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
      _connectionString = configuration.GetConnectionString(dgName);
    }

    //Leaving
    //public async Task<IEnumerable<LeavingPriceSummaryItem>> GetLeavingPriceItems(GetLeavingPriceItemsParams parms)
    //{
    //    IEnumerable<int> userRetailerSiteIds = userService.GetUserRetailerSiteIds();

    //    var parmsForSP = new GetLeavingPriceItemsParams()
    //    {
    //        EligibleSiteIds = userService.GetUserSiteIds().ToList(),
    //        ChosenRetailerSiteIds = parms.ChosenRetailerSiteIds.Intersect(userRetailerSiteIds).ToList(),
    //        StartDate = parms.StartDate,
    //        EndDate = parms.EndDate,
    //        includeNewVehicles = parms.includeNewVehicles,
    //    };



    //    var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
    //    return (await leavingPricesDataAccess.GetLeavingPriceItemsNew(parmsForSP)).ToList();
    //}


    public async Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItems(GetLeavingVehicleItemsParams parms)
    {
      IEnumerable<int> userRetailerSiteIds = userService.GetUserRetailerSiteIds();
      var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
      var res = await leavingPricesDataAccess.GetLeavingVehicleItems(parms, userRetailerSiteIds);


      foreach (var item in res)
      {
        item.BodyType = ConstantMethodsService.ToTitleCase(item.BodyType);
      }

      // Filter out any leaving items where the first price position % or last price position % is > 130% or < 80%
      return res.Where(x => x.FirstPP <= 1.3M && x.FirstPP >= 0.8M && x.LastPP <= 1.3M && x.LastPP >= 0.8M);
    }
  }
}