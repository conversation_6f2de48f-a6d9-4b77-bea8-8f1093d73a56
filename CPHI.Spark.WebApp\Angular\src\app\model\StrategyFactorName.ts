export enum StrategyFactorName {
   RetailRatingBand = 'RetailRatingBand',
   DaysListedBand = 'DaysListedBand',
   OnBrandCheck = 'OnBrandCheck',
   RetailerName = 'RetailerName',
   MatchCheapestCompetitor = 'MatchCheapestCompetitor',
   DaysToSell = 'DaysToSell',
   ValuationChangeUntilSell = 'ValuationChangeUntilSell',
   MinimumProfit = 'MinimumProfit',
   RoundToNearest = 'RoundToNearest',
   RoundToPriceBreak = 'RoundToPriceBreak',
   RR_DL_Matrix = 'RR_DL_Matrix',
   DaysListed = 'DaysListed',

   // new ones SPK-4670
   DaysInStock = 'DaysInStock',
   DaysInStockBand = 'DaysInStockBand',
   RR_DS_Matrix = 'RR_DS_Matrix',
   RR_DB_Matrix = 'RR_DB_Matrix',
   DTS_DL_Matrix = 'DTS_DL_Matrix',

   // SPK-5168
   SpecificColour = 'SpecificColour',
   AgeAndOwners = 'AgeAndOwners',

   // SPK-5200
   AchieveMarketPositionScore = 'AchieveMarketPositionScore',
   WholesaleAdjustment = 'WholesaleAdjustment',
   RetailRating10sBand = 'RetailRating10sBand',
   MakeFuelType = 'MakeFuelType',
   MakeAgeBand = 'MakeAgeBand',
   Brand = 'Brand',

   // SPK-5443
   ModelName = 'ModelName',
   Mileage = 'Mileage'
}


