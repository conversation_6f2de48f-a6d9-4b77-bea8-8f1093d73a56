import { Component, Input, OnInit } from "@angular/core";
import { RunRate } from "src/app/model/main.model";
import { PartsSummaryService } from "src/app/pages/partsSummary/partsSummary.service";
import { ServiceSummaryService } from "src/app/pages/serviceSummary/serviceSummary.service";
import { ConstantsService } from "src/app/services/constants.service";

@Component({
    selector: "runRateAndRequirementTile",
    template: `
      <div class="dashboard-tile-inner">
        <div class="dashboard-tile-header">
          {{ constants.translatedText.Dashboard_ServiceSales_RunRateAndRequirement }}
        </div>
        <div class="dashboard-tile-body">
          <div id="run-rate-chart">
            <div class="bar done" [ngStyle]="{ 'height.%': donePerDayHeight }">
              <div class="bar-inner">
                <span>{{ data.TurnoverPerDayDone | cph:'currency':0 }}</span>
                <span>{{ constants.translatedText.Done }}</span>
              </div>
            </div>
            <div class="bar need" [ngStyle]="{ 'height.%': requirementPerDayHeight }">
              <div class="bar-inner">
                <span>{{ data.TurnoverPerDayNeed | cph:'currency':0}}</span>
                <span>{{ constants.translatedText.Need }}</span>
              </div>
            </div>
            <div class="axis"></div>
          </div>

          <table class="cph">
            <thead>
              <tr>
                <th></th>
                <th>{{ constants.translatedText.Done }}</th>
                <th>{{ constants.translatedText.Need }}</th>
                <th *ngIf="!constants.environment.runRateTile_hideBudget">{{ constants.translatedText.Budget }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{{ constants.translatedText.WorkingDays }}</td>
                <td>{{ data.WorkingDaysGone | cph:'number':1 }}</td>
                <td>{{ data.WorkingDaysRemaining | cph:'number':1 }}</td>
                <td *ngIf="!constants.environment.runRateTile_hideBudget">{{ data.WorkingDaysTarget | cph:'number':1 }}</td>
              </tr>
              <tr>
                <td>{{ constants.translatedText.Turnover }} {{ constants.translatedText.PerDay }}</td>
                <td>{{ data.TurnoverPerDayDone | cph:'currency':0 }}</td>
                <td>{{ data.TurnoverPerDayNeed | cph:'currency':0 }}</td>
                <td *ngIf="!constants.environment.runRateTile_hideBudget">{{ data.TurnoverPerDayTarget | cph:'currency':0 }}</td>
              </tr>
              <tr>
                <td>{{ constants.translatedText.Turnover }}</td>
                <td>{{ data.TurnoverDone | cph:'currency':0 }}</td>
                <td>{{ data.TurnoverNeed | cph:'currency':0 }}</td>
                <td *ngIf="!constants.environment.runRateTile_hideBudget">{{ data.TurnoverTarget | cph:'currency':0 }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    `,
    styles: [
      `
        #run-rate-chart { width: 60%; margin: 1em auto; position: relative; height: 50%; }

        .bar { width: 40%; border-radius: 0.3em 0.3em 0 0; position: absolute; bottom: 0; transition: ease all .5s; }
        .bar.done { background: var(--actualColour); left: 7%; color: #fff; }
        .bar.need { background: var(--targetColour); right: 7%; color: var(--mainAppColourDark); }

        .bar-inner { display: flex; align-items: center; justify-content: center; flex-direction: column; }

        .axis { width: 100%; position: absolute; border-bottom: 1px solid var(--grey70); bottom: 0; }

        table { width: 90%; margin: 1em auto; }
        table thead tr th { height: 2em !important; }
        table tbody tr td { line-height: 1.8em !important; }
      `,
    ],
  })

  export class RunRateAndRequirementTileComponent implements OnInit {
    @Input() page: string;
    
    data: RunRate;
    requirementPerDayHeight: number;
    donePerDayHeight: number;

    constructor (
      public constants: ConstantsService,
      public serviceSummaryService: ServiceSummaryService,
      public partsSummaryService: PartsSummaryService
    ) { }

    ngOnInit(): void {
      this.data = this[`${this.page}SummaryService`][`${this.page}RunRate`];
      let maxBlockHeight = Math.max(this.data.TurnoverPerDayDone, this.data.TurnoverPerDayNeed) * 1.1;
      this.requirementPerDayHeight = this.constants.div(this.data.TurnoverPerDayNeed, maxBlockHeight) * 100;
      this.donePerDayHeight = this.constants.div(this.data.TurnoverPerDayDone, maxBlockHeight) * 100;
    }

  }