﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess;

namespace CPHI.Spark.WebApp.Service
{
    public interface IStockService
    {
        Task<StockRRGSiteItemBasicDetails> GetRRGSiteItemBasicDetails(int stockItemId, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<StockModalRow>> GetStockModalMerchRows(StockReportMerchColumnName chosenReport, string siteIdsString, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<StockListRow>> GetStockListRowItem(int stockItemId);
        Task<IEnumerable<StockCheckScan>> GetLastThreeStockCheckScans(StockCheckScansParams parms, Model.DealerGroupName dealerGroup);
        Task<StockModalRowWithNext30> GetStockModalRows(StockModalRowParms parms, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<string>> GetStockListRows(StockListParams parms);
        Task<IEnumerable<StockMerchSiteRow>> GetStockMerchandisingRows(string franchises);
        Task<IEnumerable<DiffStockPrice>> GetDiffStockPrices(string modelIdent, Model.DealerGroupName dealerGroup);
        Task<SpainUsedStockSummary> GetSpainUsedStockSummary(SpainUsedStockParams parms);
        Task<SpainUsedStockAgeingSummary> GetSpainUsedStockAgeingSummary(SpainUsedStockParams parms);
        Task<IEnumerable<StockModalRow>> GetStockModalRowsSpain(SpainUsedStockModalParams parms, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<StockSnapshotChoices>> GetStockSnapshotChoices();
        Task<IEnumerable<VehiclesAwaitingPrepModalRow>> GetVehiclesAwaitingPrepModal(GetVehiclesAwaitingPrepModalParams parms);
        Task<IEnumerable<StockSiteRow>> GetStockSiteRows(StockSiteRowsParams parms, Model.DealerGroupName dealerGroup);
    }

    public class StockService : IStockService
    {
        private readonly IStockListCache stockListCache;
        private readonly IUserService userService;
        private readonly StockDataAccess stockDataAccess;
        private readonly int userId;
        private IConfiguration _configuration;

        public StockService(IStockListCache stockListCache, IUserService userService, IConfiguration configuration)
        {
            this._configuration = configuration;
            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);

            string _connectionString = configuration.GetConnectionString(dgName);
            this.stockDataAccess = new StockDataAccess(_connectionString);
            this.stockListCache = stockListCache;
            this.userService = userService;
            this.userId = userService.GetUserId();
        }

        public async Task<IEnumerable<StockSiteRow>> GetStockSiteRows(StockSiteRowsParams parms, Model.DealerGroupName dealerGroup)
        {
            
            IEnumerable<StockSiteRow> unordered = await stockDataAccess.GetStockSiteRows(parms,userId, dealerGroup);
            AddSubTotals(unordered);
            return unordered.OrderBy(x => x.SortOrder);
        }

        private void AddSubTotals(IEnumerable<StockSiteRow> siteRows)
        {
            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

            foreach (var s in siteRows)
            {
                if (dealerGroup == Model.DealerGroupName.Vindis)
                {
                    s.TotalUsed = s.CoreUsed + s.ExManagement + s.Tactical + s.ExDemo + s.Demo;
                    s.TotalUsedIncTrade = s.TotalUsed + s.Trade;
                    s.Total = s.TotalUsedIncTrade + s.New;
                }
                else
                {
                    s.TotalUsed = s.CoreUsed + s.ExManagement + s.Tactical + s.ExDemo;
                    s.TotalUsedIncTrade = s.TotalUsed + s.Trade;
                    s.Total = s.TotalUsedIncTrade + s.New + s.Demo;
                }


            }
        }


        public async Task<IEnumerable<StockMerchSiteRow>> GetStockMerchandisingRows(string franchises)
        {
            var unordered = await stockDataAccess.GetStockMerchandisingRows(franchises, userId, userService.GetUserDealerGroupName());
            return unordered.OrderBy(x => x.SortOrder);
        }


        public async Task<SpainUsedStockSummary> GetSpainUsedStockSummary(SpainUsedStockParams parms)
        {
            // All items
            IEnumerable<SpainUsedStockDataItem> dataItems = await stockDataAccess.GetSpainUsedStockDataItems(parms, parms.ChosenStockDate, userId, userService.GetUserDealerGroupName());



            // Return item
            SpainUsedStockSummary stockSummary = new SpainUsedStockSummary();
            stockSummary.SummaryByDisposalRoute = CreateSummaryByDisposalRoute(dataItems.ToList());
            stockSummary.SummaryByDisposalRoute = stockSummary.SummaryByDisposalRoute.OrderByDescending(x => x.TotalStockCount).ToList();
            return stockSummary;
        }

        public async Task<IEnumerable<StockSnapshotChoices>> GetStockSnapshotChoices()
        {
            var datesFromDb = await stockDataAccess.GetStockSnapshotChoices(userService.GetUserDealerGroupName());
            return datesFromDb;
        }



        public async Task<SpainUsedStockAgeingSummary> GetSpainUsedStockAgeingSummary(SpainUsedStockParams parms)
        {
            DateTime chosenMonthEnd = new DateTime(parms.ChosenStockDate.Year, parms.ChosenStockDate.Month, DateTime.DaysInMonth(parms.ChosenStockDate.Year, parms.ChosenStockDate.Month));
            IEnumerable<SpainUsedStockDataItem> dataItems = await stockDataAccess.GetSpainUsedStockDataItems(parms, chosenMonthEnd, userId, userService.GetUserDealerGroupName());

            DateTime monthBefore = parms.ChosenStockDate.AddMonths(-1);
            DateTime monthBeforeEnd = new DateTime(monthBefore.Year, monthBefore.Month, DateTime.DaysInMonth(monthBefore.Year, monthBefore.Month));
            var dataItemsLastMonth = await stockDataAccess.GetSpainUsedStockDataItems(parms, monthBeforeEnd, userId, userService.GetUserDealerGroupName());

            string[] ageingBrackets = dataItems.Select(f => f.Antiguedad_Stock).Distinct().ToArray();

            SpainUsedStockAgeingSummary result = CreateSpainUsedStockAgeingSummary(dataItems.ToList(), dataItemsLastMonth.ToList(), ageingBrackets);

            return result;
        }

        private SpainUsedStockAgeingSummary CreateSpainUsedStockAgeingSummary(List<SpainUsedStockDataItem> thisMonth, List<SpainUsedStockDataItem> lastMonth, string[] ageingBrackets)
        {
            SpainUsedStockAgeingSummary result = new SpainUsedStockAgeingSummary();
            result.ByDisposalRouteRows = new List<SpainStockAgeingRow>();
            result.ByMakeRows = new List<SpainStockAgeingRow>();
            result.ByEdadDelVehiculoRows = new List<SpainStockAgeingRow>();
            result.ByVehicleSourceRows = new List<SpainStockAgeingRow>();

            // Create row levels

            // ByDisposalRoute
            //var dispThis = ;
            var lastMonthByDisposalRoute = lastMonth.ToLookup(x => x.DisposalRoute);

            foreach (var groupingThisMonth in thisMonth.ToLookup(x => x.DisposalRoute))
            {
                IEnumerable<SpainUsedStockDataItem> matchLastMonth = new List<SpainUsedStockDataItem>();
                if (lastMonthByDisposalRoute[groupingThisMonth.Key] != null)
                {
                    matchLastMonth = lastMonthByDisposalRoute[groupingThisMonth.Key];
                }

                SpainStockAgeingRow newRow = new SpainStockAgeingRow();
                newRow.RowLabel = groupingThisMonth.First().DisposalRoute;
                newRow.AgeingValueSets = CreateAgeingColumns(groupingThisMonth, matchLastMonth, ageingBrackets);
                var totalCol = CreateAgeingValueSet(groupingThisMonth, matchLastMonth, true);
                newRow.AgeingValueSets.Add(totalCol);

                result.ByDisposalRouteRows.Add(newRow);
            }

            result.ByDisposalRouteRows.Add(CreateTotalRow(result.ByDisposalRouteRows, ageingBrackets));


            // ByMake
            var makeThis = thisMonth.ToLookup(x => x.Make);
            var makeLast = lastMonth.ToLookup(x => x.Make);

            foreach (var makeT in makeThis)
            {
                foreach (var makeL in makeLast)
                {
                    if (makeT.First().Make == makeL.First().Make)
                    {
                        SpainStockAgeingRow newRow = new SpainStockAgeingRow();
                        newRow.RowLabel = makeT.First().Make;
                        newRow.AgeingValueSets = CreateAgeingColumns(makeT, makeL, ageingBrackets);

                        var totalCol = CreateAgeingValueSet(makeT, makeL, true);
                        newRow.AgeingValueSets.Add(totalCol);

                        result.ByMakeRows.Add(newRow);
                    }
                }
            }

            result.ByMakeRows.Add(CreateTotalRow(result.ByMakeRows, ageingBrackets));

            // ByEdadDelVehiculoRows
            var edadThis = thisMonth.ToLookup(x => x.Edad_Vehiculo);
            var edadLast = lastMonth.ToLookup(x => x.Edad_Vehiculo);

            foreach (var edT in edadThis)
            {
                foreach (var edL in edadLast)
                {
                    if (edT.First().Edad_Vehiculo == edL.First().Edad_Vehiculo)
                    {
                        SpainStockAgeingRow newRow = new SpainStockAgeingRow();
                        newRow.RowLabel = edT.First().Edad_Vehiculo;
                        newRow.AgeingValueSets = CreateAgeingColumns(edT, edL, ageingBrackets);

                        var totalCol = CreateAgeingValueSet(edT, edL, true);
                        newRow.AgeingValueSets.Add(totalCol);

                        result.ByEdadDelVehiculoRows.Add(newRow);
                    }
                }
            }

            result.ByEdadDelVehiculoRows.Add(CreateTotalRow(result.ByEdadDelVehiculoRows, ageingBrackets));

            // ByVehicleSource
            var vehsourceThis = thisMonth.ToLookup(x => x.VehicleSource);
            var vehsourceLast = lastMonth.ToLookup(x => x.VehicleSource);

            foreach (var vehSoT in vehsourceThis)
            {
                foreach (var vehSoL in vehsourceLast)
                {
                    if (vehSoT.First().VehicleSource == vehSoL.First().VehicleSource)
                    {
                        SpainStockAgeingRow newRow = new SpainStockAgeingRow();
                        newRow.RowLabel = vehSoT.First().VehicleSource;
                        newRow.AgeingValueSets = CreateAgeingColumns(vehSoT, vehSoL, ageingBrackets);

                        var totalCol = CreateAgeingValueSet(vehSoT, vehSoL, true);
                        newRow.AgeingValueSets.Add(totalCol);

                        result.ByVehicleSourceRows.Add(newRow);
                    }
                }
            }

            result.ByVehicleSourceRows.Add(CreateTotalRow(result.ByVehicleSourceRows, ageingBrackets));

            return result;
        }

        private SpainStockAgeingRow CreateTotalRow(List<SpainStockAgeingRow> allRows, string[] ageingBrackets)
        {
            SpainStockAgeingRow totalRow = new SpainStockAgeingRow();
            totalRow.RowLabel = "Total";
            totalRow.AgeingValueSets = new List<SpainStockAgeingValueSet>();

            foreach (string bracket in ageingBrackets)
            {
                SpainStockAgeingValueSet temp = new SpainStockAgeingValueSet();
                temp.Label = bracket;

                foreach (SpainStockAgeingRow row in allRows)
                {
                    temp.Units += row.AgeingValueSets.Where(x => x.Label == bracket).Select(x => x.Units).Sum();
                    temp.Improvement += row.AgeingValueSets.Where(x => x.Label == bracket).Select(x => x.Improvement).Average();
                }

                temp.Improvement = (allRows.Count != 0) ? (temp.Improvement / allRows.Count - 1) : 0;
                totalRow.AgeingValueSets.Add(temp);
            }


            SpainStockAgeingValueSet grandTotal = new SpainStockAgeingValueSet();
            grandTotal.Label = "Total";

            foreach (SpainStockAgeingRow row in allRows)
            {
                if (row.RowLabel != "Total")
                {
                    grandTotal.Units += row.AgeingValueSets.Where(x => x.Label != "Total").Select(x => x.Units).Sum();
                    grandTotal.Improvement += row.AgeingValueSets.Where(x => x.Label != "Total").Select(x => x.Improvement).Average();
                }

            }

            grandTotal.Improvement = allRows.Count != 0 ? (grandTotal.Improvement / allRows.Count - 1) : 0;
            totalRow.AgeingValueSets.Add(grandTotal);

            return totalRow;
        }

        private List<SpainStockAgeingValueSet> CreateAgeingColumns(IEnumerable<SpainUsedStockDataItem> thisMonth, IEnumerable<SpainUsedStockDataItem> lastMonth, string[] ageingBrackets)
        {
            List<SpainStockAgeingValueSet> result = new List<SpainStockAgeingValueSet>();

            var byAgeLastMonth = lastMonth.ToLookup(x => x.Antiguedad_Stock);

            foreach (var item in thisMonth.ToLookup(x => x.Antiguedad_Stock))
            {
                IEnumerable<SpainUsedStockDataItem> matchLastMonth = new List<SpainUsedStockDataItem>();
                if (byAgeLastMonth[item.Key] != null)
                {
                    matchLastMonth = byAgeLastMonth[item.Key];
                }

                result.Add(CreateAgeingValueSet(item, matchLastMonth, false));
            }

            // Add empty items if no units in other brackets 
            List<SpainStockAgeingValueSet> emptyArray = new List<SpainStockAgeingValueSet>();

            string[] agesWithResults = result.Select(f => f.Label).Distinct().ToArray();

            foreach (string bracket in ageingBrackets)
            {
                if (agesWithResults.Contains(bracket))
                {
                    continue;
                }
                else
                {
                    emptyArray.Add(CreateEmptyValueSet(bracket));
                }
            }

            // Join lists and filter
            result.AddRange(emptyArray);
            result.OrderBy(x => x.Label);

            return result;
        }

        private SpainStockAgeingValueSet CreateEmptyValueSet(string label)
        {
            SpainStockAgeingValueSet result = new SpainStockAgeingValueSet();

            result.Label = label;
            result.Units = 0;
            result.Improvement = 0;

            return result;
        }

        private SpainStockAgeingValueSet CreateAgeingValueSet(IEnumerable<SpainUsedStockDataItem> thisMonth, IEnumerable<SpainUsedStockDataItem> lastMonth, bool isTotal)
        {

            SpainStockAgeingValueSet result = new SpainStockAgeingValueSet();

            int oldUnits = lastMonth.Sum(x => x.VehicleCount);
            int newUnits = thisMonth.Sum(x => x.VehicleCount);

            result.Label = !isTotal ? thisMonth.First().Antiguedad_Stock : "Total";
            result.Units = newUnits;
            result.Improvement = CalculateImprovementPercentage(oldUnits, newUnits);

            return result;
        }

        private decimal CalculateImprovementPercentage(int oldUnits, int newUnits)
        {
            return oldUnits != 0 ? ((decimal)(newUnits - oldUnits) / (decimal)oldUnits) * 100 : 0;
        }

        private List<SpainUsedStockStatSummary> CreateSummaryByDisposalRoute(List<SpainUsedStockDataItem> dataItems)
        {
            var byDisposalRoute = dataItems.ToLookup(x => x.DisposalRoute);
            List<SpainUsedStockStatSummary> result = new List<SpainUsedStockStatSummary>();

            foreach (var disposal in byDisposalRoute)
            {
                if (disposal.Key == "DESGUACE" || disposal.Key == "VD = KM0" || disposal.Key == "ANALIZAR") { continue; }
                result.Add(CreateUsedStockStatSummary(disposal.ToList(), disposal.First().DisposalRoute));
            }

            return result;
        }

        private SpainUsedStockStatSummary CreateUsedStockStatSummary(List<SpainUsedStockDataItem> dataItems, string label)
        {
            SpainUsedStockStatSummary summary = new SpainUsedStockStatSummary();
            summary.Label = label;
            summary.ByMake = new List<LabelAndValue>();
            summary.ByAllMakes = new List<LabelAndValue>();
            summary.ByModel = new List<LabelAndValue>();
            summary.ByVehicleSource = new List<LabelAndValue>();

            // By make
            var byMake = dataItems.ToLookup(x => x.Make);

            int totalStockCount = 0;

            foreach (var make in byMake)
            {
                totalStockCount += GetVehicleCount(make);
                LabelAndValue entry = MakeLabelAndValue(make, make.First().Make);
                summary.ByMake.Add(entry);
            }

            summary.ByMake.Sort((x, y) => y.Value.CompareTo(x.Value));

            // By all makes
            var byAllMakes = dataItems.ToLookup(x => x.MakeAll);

            foreach (var allMakes in byAllMakes)
            {
                LabelAndValue entry = MakeLabelAndValue(allMakes, allMakes.First().MakeAll);
                summary.ByAllMakes.Add(entry);
            }

            summary.ByAllMakes.Sort((x, y) => y.Value.CompareTo(x.Value));

            // By model
            var byModel = dataItems.ToLookup(x => x.Model);

            foreach (var model in byModel)
            {
                LabelAndValue entry = MakeLabelAndValue(model, model.First().Model);
                summary.ByModel.Add(entry);
            }

            summary.ByModel.Sort((x, y) => y.Value.CompareTo(x.Value));

            // By model
            var byVehicleSource = dataItems.ToLookup(x => x.VehicleSource);

            foreach (var vehSource in byVehicleSource)
            {
                LabelAndValue entry = MakeLabelAndValue(vehSource, vehSource.First().VehicleSource);
                summary.ByVehicleSource.Add(entry);
            }

            summary.ByVehicleSource.Sort((x, y) => y.Value.CompareTo(x.Value));

            summary.TotalStockCount = totalStockCount;

            return summary;
        }

        private int GetVehicleCount(IGrouping<string, SpainUsedStockDataItem> regionGrouping)
        {
            int total = 0;

            foreach (var item in regionGrouping)
            {
                total += item.VehicleCount;
            }

            return total;
        }

        private LabelAndValue MakeLabelAndValue(IGrouping<string, SpainUsedStockDataItem> regionGrouping, string label)
        {
            LabelAndValue entry = new LabelAndValue();
            entry.Label = label;
            entry.Value = GetVehicleCount(regionGrouping);
            return entry;
        }

        public async Task<IEnumerable<StockModalRow>> GetStockModalMerchRows(StockReportMerchColumnName chosenReport, string siteIdsString, Model.DealerGroupName dealerGroup)
        {
            return await stockDataAccess.GetStockModalMerchRows(chosenReport, siteIdsString, userId, dealerGroup);
        }

        public async Task<IEnumerable<VehiclesAwaitingPrepModalRow>> GetVehiclesAwaitingPrepModal(GetVehiclesAwaitingPrepModalParams parms)
        {
            return await stockDataAccess.GetVehiclesAwaitingPrepModal(parms.SiteIds, parms.Column, userService.GetUserDealerGroupName());
        }

        public async Task<StockModalRowWithNext30> GetStockModalRows(StockModalRowParms parms, Model.DealerGroupName dealerGroup)
        {
            IEnumerable<StockModalRow> overNow = await stockDataAccess.GetStockModalRows(parms.ChosenReport.ToString(), parms.SiteIds, parms.AgedOver, 0, parms.AgeFor.ToString(), parms.UseGroupDays, parms.Franchises, userId, dealerGroup);
            IEnumerable<StockModalRow> overIn30 = await stockDataAccess.GetStockModalRows(parms.ChosenReport.ToString(), parms.SiteIds, parms.AgedOver - 30, parms.AgedOver, parms.AgeFor.ToString(), parms.UseGroupDays, parms.Franchises, userId, dealerGroup);

            List<StockModalRow> overIn30List = overIn30.ToList();

            int days = 1;


            while (days <= 30)
            {
                int age = parms.AgedOver - days;

                StockModalRow blankRow = new StockModalRow
                {
                    Age = age
                };

                overIn30List.Add(blankRow);

                days++;
            }


            overIn30List = overIn30List.OrderBy(x => x.Age).ToList();

            var overIn30ByDay = overIn30List.ToLookup(x => x.Age);

            return new StockModalRowWithNext30()
            {
                AgedNow = overNow,
                AgedIn30 = overIn30ByDay
            };
        }


        public async Task<IEnumerable<StockModalRow>> GetStockModalRowsSpain(SpainUsedStockModalParams parms, Model.DealerGroupName dealerGroup)
        {
            return await stockDataAccess.GetStockModalRowsSpain(parms, userId, dealerGroup);
        }





        // Old method
        public async Task<IEnumerable<string>> GetStockListRows(StockListParams parms)
        {
            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            IEnumerable<int> userSites = userService.GetUserSiteIds();// await userService.GetUserSiteIdsFomDatabase(userId,dealerGroup);
            var resultAllFields = await stockListCache.GetStockListRows(dealerGroup);
            var forUserSites = resultAllFields.Where(x => userSites.Contains(x.SiteId));
            var tst = resultAllFields.Where(x => x.CapCode.Length > 4);
            if (parms.VehicleTypeTypes.Count > 0) { resultAllFields = resultAllFields.Where(x => parms.VehicleTypeTypes.Contains(x.VehicleType)); }
            if (parms.Franchises.Count > 0) { resultAllFields = resultAllFields.Where(x => parms.Franchises.Contains(x.Franchise)); }
            if (parms.Models.Count > 0) { resultAllFields = resultAllFields.Where(x => parms.Models.Contains(x.Model)); }
            if (parms.SiteIds.Count > 0) { resultAllFields = resultAllFields.Where(x => parms.SiteIds.Contains(x.SiteId)); }

            List<string> results = new List<string>();

            if (parms.Columns.Contains("Id") == false)
            {
                parms.Columns.Add("Id");
            }


            //determine columnTypes
            List<string> columnTypes = new List<string>();
            foreach (var column in parms.Columns)
            {
                var propType = typeof(StockListRow).GetProperty(column);

                if (propType == null)
                {
                    throw new Exception($"Invalid column requested: {column}");
                }
                if (propType.PropertyType == typeof(DateTime?) || propType.PropertyType == typeof(DateTime))
                {
                    columnTypes.Add("datetime");
                }
                else
                {
                    columnTypes.Add("notDateTime");
                }
            }


            //walk through items converting them to strings using columnTypes
            foreach (var item in resultAllFields)
            {
                if (item.StockNumberFull == "1067304/0")
                {
                    { }
                }
                List<string> values = new List<string>(parms.Columns.Count);
                int colIndex = 0;

                bool isLastCol = false;
                int totalColumns = parms.Columns.Count; // Assuming parms.Columns has a Count property

                foreach (var column in parms.Columns)
                {
                    isLastCol = (colIndex == totalColumns - 1);


                    if (column == "CapCode")
                    {
                        { }
                    }
                    var propType = columnTypes[colIndex];
                    if (propType == "datetime")
                    {
                        AddAsDate(item, values, column);
                    }
                    else
                    {
                        AddAsString(item, values, column, isLastCol);
                    }

                    colIndex++;
                }

                results.Add(string.Join('|', values));
            }



            return results;


        }

        private void AddAsString(StockListRow item, List<string> values, string column, bool isLastCol)
        {
            var value = typeof(StockListRow).GetProperty(column).GetValue(item);

            if (value != null)
            {
                if(isLastCol)
                {
                    values.Add(value.ToString());
                }
                else
                {
                    values.Add(value.ToString() + "|^");
                }
                
            }
            else
            {
                values.Add("|^");
            }
        }

        private void AddAsDate(StockListRow item, List<string> values, string column)
        {
            var value = typeof(StockListRow).GetProperty(column).GetValue(item);
            if (value != null)
            {
                values.Add(StaticHelpersService.dateToString((DateTime)value) + "|^");
            }
            else
            {
                values.Add("|^");
            }
        }

        public async Task<IEnumerable<StockListRow>> GetStockListRowItem(int stockItemId)
        {
            return await stockDataAccess.GetStockListRowItem(stockItemId, userService.GetUserDealerGroupName());
        }

        public async Task<IEnumerable<StockCheckScan>> GetLastThreeStockCheckScans(StockCheckScansParams parms, Model.DealerGroupName dealerGroup)
        {
            int dealerGroupId = DealerGroupStockpulseId.Get(dealerGroup);

            if (parms.Vin.Trim().Length > 0)
            {
                parms.Vin = parms.Vin.Substring(Math.Max(0, parms.Vin.Length - 7));
            }

            string spConnString = _configuration.GetConnectionString("StockTakePhotos");
            IEnumerable<StockCheckScan> result = await stockDataAccess.GetLastThreeStockCheckScans(dealerGroupId, parms, spConnString);

            return result.Reverse();
        }


        public async Task<StockRRGSiteItemBasicDetails> GetRRGSiteItemBasicDetails(int stockItemId, Model.DealerGroupName dealerGroup)
        {
            var res = await stockDataAccess.GetRRGSiteItemBasicDetails(stockItemId, dealerGroup);
            //have to string split to make the first url become 
            res.FirstImageUrl = res.FirstImageUrl.Split(".jpg")[0] + ".jpg";
            return res;
        }


        //public async Task<IEnumerable<ReportVM>> GetReports()
        //{
        //    return await stockDataAccess.GetReports(userId);
        //}

        public async Task<IEnumerable<DiffStockPrice>> GetDiffStockPrices(string modelIdent, Model.DealerGroupName dealerGroup)
        {
            return await stockDataAccess.GetDiffStockPrices(modelIdent, dealerGroup);
        }





    }
}