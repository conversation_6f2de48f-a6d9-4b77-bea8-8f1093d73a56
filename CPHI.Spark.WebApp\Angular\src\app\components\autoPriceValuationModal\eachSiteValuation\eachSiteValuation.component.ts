import { Component, Input, OnInit } from '@angular/core';
import { GetContextMenuItemsParams, GridApi, GridOptions, GridReadyEvent, RowClassParams, RowDoubleClickedEvent } from 'ag-grid-community';
import { CphPipe } from 'src/app/cph.pipe';
import { LeavingVehicleItem } from 'src/app/model/LeavingVehicleItem';
import { AutoTraderAdvertImage } from 'src/app/_cellRenderers/autoTraderAdvertImage.component';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { VehicleValuationService } from '../vehicleValuation.service';
import { VehicleLocationStrategyPriceBuild } from 'src/app/model/VehicleLocationStrategyPriceBuild';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';

@Component({
  selector: 'eachSiteValuation',
  templateUrl: './eachSiteValuation.component.html',
  styleUrls: ['./eachSiteValuation.component.scss']
})
export class EachSiteValuationComponent implements OnInit {
  @Input() data: LeavingVehicleItem[];
  gridOptions: GridOptions;
  gridApi: GridApi;
  resizeObserver: ResizeObserver;

  constructor(
    public cphPipe: CphPipe,
    private colTypesService: ColumnTypesService,
    private service: VehicleValuationService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  ngOnInit(): void {

    const table = document.getElementById('eachSiteValuationTable');

    this.resizeObserver = new ResizeObserver(entries => {
      if (this.gridApi) {
        setTimeout(() => {
          this.gridApi.sizeColumnsToFit();
        }, 250)
      }
    });

    this.resizeObserver.observe(table);

    
    this.setGridDefinitions();
  }


  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }

  setGridDefinitions() {

    
    const distanceHeader = `Distance from ${this.service.selectionsService.userRetailerSite.Name}`;

    this.gridOptions = {
      defaultColDef: {
        resizable: true,
        sortable: true,
        filterParams: {
          applyButton: false,
          clearButton: true,
          cellHeight: this.gridHelpersService.getFilterListItemHeight()
        },
        autoHeight: true,
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true
      },
      columnTypes:  {...this.colTypesService.provideColTypes([]) },
      onRowDoubleClicked: (params) => this.onRowDoubleClicked(params),
      // getRowClass:(params)=>this.provideRowClass(params),
      rowClassRules: {
        'brightHighlight': (params) => params.data.RetailerSite.Id===this.service.chosenVehicleLocationStrategyPriceBuild.RetailerSite.Id
      },
      getContextMenuItems: (params) => this.getContextMenuItems(params),
      rowData: this.generateRowData(this.service.valuationModalResultNew.LocationStrategyPrices ) ,
      columnDefs: [
        { headerName: 'Site', colId: 'RetailerSiteName', field: 'RetailerSite.Name', type: 'label', width: 100 },
        { headerName: 'Retail Rating', colId: 'RetailRating', field: 'RetailRating', type: 'number', width: 30 },
        { headerName: 'Days To Sell', colId: 'DaysToSell', field: 'DaysToSell', type: 'number', width: 30 },
        { headerName: 'Strategy Price', colId: 'StrategyPrice', field: 'StrategyPrice', type: 'currency', width: 30 },
        { headerName: distanceHeader, colId: 'Distance', field: 'Distance', type: 'miles', width: 30 },

      ],
      onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      }
    }
  }
  onRowDoubleClicked(params: RowDoubleClickedEvent<any, any>): void {
    const clickedItem:VehicleLocationStrategyPriceBuild = params.node.data;
    this.chooseNewSite(clickedItem);
  }
  generateRowData(itemIn: VehicleLocationStrategyPriceBuild[]): any[] {
    let results: VehicleLocationStrategyPriceBuild[]=[];

    itemIn.forEach(item=>{
      if(item.RetailerSite.Id === this.service.selectionsService.user.RetailerSiteId){
        const newItem = new VehicleLocationStrategyPriceBuild(item);
        newItem.RetailerSite.Name = newItem.RetailerSite.Name + ' (user home site)'
        results.push(newItem);
      }
      else{
        results.push(item);
      }
    })

    return results;
  }
  getContextMenuItems(params: GetContextMenuItemsParams<any, any>): (string | import("ag-grid-community").MenuItemDef)[] {
    const clickedItem:VehicleLocationStrategyPriceBuild = params.node.data;
    
    var menuOptions = [
      'copy',
      'copyWithHeaders',
      'separator',
      {
        icon: '🔗',
        name: `Choose ${clickedItem.RetailerSite.Name}`,
        cssClasses: ['bold'],
        action: () => {
          this.chooseNewSite(clickedItem);
        }
      }
    ]

    return menuOptions;
  }
  chooseNewSite(clickedItem: VehicleLocationStrategyPriceBuild) {
    this.service.chooseNewSite(clickedItem.RetailerSite);
    this.gridApi.redrawRows();
  }

  provideRowClass(params: RowClassParams<any, any>): string | string[] {
    const row:VehicleLocationStrategyPriceBuild = params.data;
    if(row.RetailerSite.Id===this.service.selectionsService.userRetailerSite.Id){
      return ['brightHighlight']
    }

  }




  onGridReady(event: GridReadyEvent) {
    this.gridApi = event.api;
    this.gridApi.sizeColumnsToFit();
  }
}
