import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { CellClassParams, ColumnApi, DomLayoutType, GridOptions, RowDataUpdatedEvent } from 'ag-grid-community';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { CphPipe } from '../../../cph.pipe';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from '../../../services/selections.service';
import { SalesPerformanceService } from '../salesPerformance.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

@Component({
  selector: 'salesPerformanceCustomTable',
  templateUrl: './salesPerformanceCustomTable.component.html',
  styleUrls: ['./../../../../styles/components/_agGrid.scss', './salesPerformanceCustomTable.component.scss'],
})


export class SalesPerformanceCustomTableComponent implements OnInit {
  @Input() public reportType: string;

  @Output() cellClicked = new EventEmitter();

  //for agGrid
  showGrid: boolean = true;
  domLayout: DomLayoutType = "autoHeight";
  flipCols: string[];
  mainTableGridOptions: GridOptions
  colDefinitions: any;
  //gridApiColumnDefinitions: any;

  //pinnedBottomRowData: any;

  frameworkComponents: { agColumnHeader: any; };

  //budgetHeader: string;
  //top: number;
  gridColumnApi: ColumnApi;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: SalesPerformanceService,
    public columnTypeService: ColumnTypesService
  ) { }

  ngOnInit() {
    this.setupGrid();
  }

  ngOnDestroy(){
    this.service.gridApiCustom = null;
    this.service.gridApiRegionsCustom = null;
  }
  ngOnChanges(changes: SimpleChanges): void {
    
    // if (this.service.gridApiCustom) {
    //   this.service.gridApiCustom.refreshCells();
    //   this.service.gridApiCustom.redrawRows();
    // }
  }

  setupGrid() {
    this.flipCols = [];
    

    this.mainTableGridOptions = {
      getMainMenuItems:(params)=>this.gridHelpersService.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlightsCustomTable),
      getContextMenuItems: (params) =>{return this.gridHelpersService.getContextMenuItems(params).concat(['separator','chartRange'])},
      context: { thisComponent: this },
      suppressPropertyNamesCheck : true,
      getRowId:(params)=>{return `${params.data.SiteId}_${params.data.RegionId}_${params.data.IsTotal}`},
      onCellClicked: (params) => {
        this.onCellClick(params);
      },
      onRowDataUpdated:(params)=>this.onRowDataUpdated(params),
      onFirstDataRendered:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
      frameworkComponents: this.frameworkComponents,
      //getMainMenuItems: this.getMainMenuItems(),
      rowData: this.reportType==='sites' ? this.service.siteRows : this.service.regionRows, 
      pinnedBottomRowData:this.service.totalRows,
      onGridReady: (params) => this.onGridReady(params),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
        //suppressColumnMoveAnimation: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsCustomTable),
      },
      columnDefs: this.getColumnDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }
    }
  }

  onRowDataUpdated(params: RowDataUpdatedEvent<any, any>): void {
    this.resizeGrid();
  }

  getColumnDefs() {
    let thisMonthName = this.constants.appStartTime.toLocaleString('default', { month: 'long' });
    
    return this.colDefinitions = [

      { headerName: this.constants.translatedText.Site, field: 'Label', colId: 'Label', width: 180, type: 'label', },

      {
        headerName: this.constants.translatedText.Dashboard_SalesPerformance_TotalVehicles,
        children: [
          //Run Rate cols
          { headerName: this.constants.translatedText.Units,  field: 'UnitsDone', colId: 'VehiclesOrderedThisMonth', width: 65, type: 'number', },
          { headerName: this.constants.translatedText.Dashboard_SalesPerformance_GPU,  field: 'ProfitPerUnitDone', colId: 'ProfitPerUnitDone', width: 65, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.Dashboard_SalesPerformance_GP,  field: 'ProfitDone', colId: 'ProfitDone', width: 65, type: 'currencyWithFontColour', },

        ]
      },
    ]
  }

  onGridReady(params) {
    if(this.reportType==='sites'){
      this.service.gridApiCustom = params.api;
    }else{
      this.service.gridApiRegionsCustom = params.api;
    }

    this.gridColumnApi = params.columnApi;
    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
   // to be able to then reference this things within custom menu methods
    
   if(this.reportType==='sites'){
    this.service.gridApiCustom.sizeColumnsToFit();
  }else{
    this.service.gridApiRegionsCustom.sizeColumnsToFit();
  }
   
    this.resizeGrid();
    this.selections.triggerSpinner.next({show:false});
  }



  resizeGrid() {
    setTimeout(() => {

      this.gridColumnApi?.autoSizeAllColumns();
      
      if (this.reportType==='sites') {
        this.service.gridApiCustom.sizeColumnsToFit();
      }else{
        this.service.gridApiRegionsCustom.sizeColumnsToFit();
      }
    }, 10)
  }





  onCellClick(site) {
    this.cellClicked.next(site);
  }

  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.reportType=== 'units' ? this.service.gridApiCustom.getModel() : this.service.gridApiRegionsCustom.getModel()
    let initialColsToColour = 1
    if (this.service.salesPerformanceReportType == 'vsLastYear' || this.service.salesPerformanceReportType == 'vsBudget') initialColsToColour = 2
    this.excel.createSheetObject(tableModel, 'Sales Performance', 1, initialColsToColour);
  }


}
