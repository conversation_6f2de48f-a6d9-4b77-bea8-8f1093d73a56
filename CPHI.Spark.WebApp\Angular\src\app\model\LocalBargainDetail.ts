export interface LocalBargainDetail {
  VehicleReg: string;

  FirstRegistered: Date | string;
  Mileage: number;
  StrategyPrice: number;
  ValuationMktAvRetailThisVehicle: number;
  ValuationMktAvTradeThisVehicle: number;
  ValuationMktAvPartExThisVehicle: number;
  ValuationMktAvPrivateThisVehicle: number;
  Condition: string;
  Vin: string;
  Make: string;
  Model: string;
  Derivative: string;
  VehicleType: string;
  Trim: string;
  BodyType: string;
  FuelType: string;
  TransmissionType: string;
  AdvertiserName: string;
  AdvertiserSegment: string;
  AdvertiserWebsite: string;
  AdvertiserPhone: string;
  AdvertTotalPrice: number;
  WebSiteSearchIdentifier: string;
  WebSiteStockIdentifier: string;
  RetailerSiteName: string;
  Opportunity: number;
  AgeBand: string;
  ValueBand: string;
  PricePosition: number;
  AdvertAttentionGrabber: string;
  AdvertPriceIndicator: string;
  AdvertAdvertisedPrice: number;
  AdvertForecourtPrice: number;
  AdvertFirstImage: string;
  DateOnForecourt: Date;
  DaysToSellLocal: number;
  DaysToSellNational: number;
  RetailRating: number;
  NationalRetailRating: number;
  Demand: number;
  Supply: number;
  MarketCondition: number;
  RetailRatingBand: string;
  DaysListed: number;
  Distance: number;
  isClosest?: boolean;
  BargainPriceChange?: number;
  BargainPriceChangeDaysAgo?: number;
}
