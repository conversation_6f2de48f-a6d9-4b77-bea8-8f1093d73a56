<div id="paginatedListHeader">
  <!-- Pagination at the top -->
  <div id="paginationButtons">

    <ngb-pagination [collectionSize]="rowDataFiltered.length" [(page)]="currentPage" [maxSize]="5" [boundaryLinks]="true"
    [rotate]="true" (pageChange)="setPage($event)"></ngb-pagination>
  </div>
    
  <div id="filterSortContainer">
    <filterSortManagement></filterSortManagement>
  </div>


  <div *ngIf="service.rowDataFiltered" id="sortDropdown" ngbDropdown class="d-inline-block">
    <button class="btn btn-primary" ngbDropdownToggle>
      {{ service.filterSortManagementParams.chosenSortField ? 'Sort by: ' +
      service.filterSortManagementParams.chosenSortField.pretty +
      (service.filterSortManagementParams.chosenSortField.sortAscending ? ' (asc)' : ' (desc)') :
      'Sort by: Default'}}
    </button>

    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
      <button *ngFor="let value of fieldsToSortBy" class="manualToggleCloseItem"
        [ngClass]="{ 'active': value.pretty === service.filterSortManagementParams.chosenSortField?.pretty }"
        ngbDropdownItem (click)="value.sortAscending = !value.sortAscending; service.sortBy(value)">
        <div class="d-flex justify-content-between">
          <div class="toggleNoCaret">{{ value.pretty }}</div>

          <ng-container
            *ngIf="value === service.filterSortManagementParams.chosenSortField && value.pretty !== 'Default'">
            <ng-container *ngIf="value.type === 'string'">
              <span *ngIf="value.sortAscending"><i class="far fa-sort-alpha-up"></i></span>
              <span *ngIf="!value.sortAscending"><i class="far fa-sort-alpha-down"></i></span>
            </ng-container>

            <ng-container *ngIf="value.type === 'int'">
              <span *ngIf="value.sortAscending"><i class="far fa-sort-numeric-up"></i></span>
              <span *ngIf="!value.sortAscending"><i class="far fa-sort-numeric-down"></i></span>
            </ng-container>
          </ng-container>
        </div>
      </button>

    </div>
  </div>
</div>

<div *ngIf="paginatedRows.length > 0" id="cardsContainer">

  <!-- This is where we will put a card -->
  <simpleAdvertCard *ngFor="let row of paginatedRows" [advert]="row"></simpleAdvertCard>


</div>