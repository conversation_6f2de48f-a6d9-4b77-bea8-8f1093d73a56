import { EventEmitter, Injectable } from "@angular/core";
import { UntypedFormControl } from "@angular/forms";
import { MultiSelectMonth } from "src/app/components/datePickerMultiSelect/datePickerMultiSelect.component";
import { RetailerSite } from "src/app/model/AutoPrice.model";
import { GetLeavingVehicleItemsParams } from "src/app/model/GetLeavingVehicleItemsParams";
import { LeavingVehicleItem } from "src/app/model/LeavingVehicleItem";
import { TableLayoutManagementParams } from "src/app/model/TableLayoutManagementParams";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";

@Injectable({
  providedIn: 'root'
})

export class LeavingVehicleDetailService {
  rowData: LeavingVehicleItem[] = [];
  chosenRetailerSites: RetailerSite[];
  tableLayoutManagement: TableLayoutManagementParams;
  searchTerm: UntypedFormControl = new UntypedFormControl();

  startDate: Date;
  endDate: Date;
  externalFilterModel: any;
  newDataEmitter: EventEmitter<void> = new EventEmitter();

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public getDataService: GetDataMethodsService
  ) {

  }

  initParams() {
    if (!this.chosenRetailerSites) {
      this.chosenRetailerSites = this.constants.RetailerSites
    }

    if (!this.startDate) { this.startDate = this.constants.addMonths(this.constants.startOfMonth(new Date()), -6); }
    if (!this.endDate) { this.endDate = this.constants.endOfMonth(new Date()); }
    this.initialiseTableLayoutManagement();
  }

  initialiseTableLayoutManagement() {
    if (this.tableLayoutManagement) { return; }
    this.tableLayoutManagement = {
      pageName: 'leavingVehiclesAnalysis',
      ownTableStates: null,
      standardTableStates: null,
      sharedTableStates: null,
      sparkTableStates: null,
      availableTableStates: null,
      selectedTableState: null,
      loadedTableState: null,
      usersToShareWith: null,
      gridApi: null,
      gridColumnApi: null,
      filterModel: null,
      originalColDefs:null
    }
  }

  async getData() {
    try {
      const params: GetLeavingVehicleItemsParams = {
        StartDate: this.startDate,
        EndDate: this.endDate,
        IncludeNewVehicles: false,
        IncludeUsedVehicles: true,
        ChosenRetailerSiteIds: this.chosenRetailerSites.map(x => x.Id)
      }

      const res: LeavingVehicleItem[] = await this.getDataService.getLeavingVehicleItems(params);
      this.rowData = res;
      this.newDataEmitter.emit();
      this.selections.triggerSpinner.emit({ show: false });
    }

    catch (error) {
      console.error('Error fetching leaving vehicle items', error);
      this.constants.toastDanger('Failed to load leaving vehicles');
      this.selections.triggerSpinner.emit({ show: false });
    }
  }

  setExternalFilterModel(model: any) {
    this.externalFilterModel = model;
  }
}