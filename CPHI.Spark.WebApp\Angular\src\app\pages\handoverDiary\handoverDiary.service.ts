import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { ExecPickerPerson, SiteVM } from 'src/app/model/main.model';

import { ConstantsService } from 'src/app/services/constants.service';
import { GetDealDataService } from 'src/app/services/getDeals.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { HandoverDiaryItem, HandoverDiaryParams, Session } from './handoverDiary.model';


@Injectable({
  providedIn: 'root'
})
export class HandoverDiaryService {

  deals: HandoverDiaryItem[];
  sites:SiteVM[];
  sitesIds: number[];
  vehicleTypeTypes: string[];
  orderTypeTypes: string[];
  franchises: string[];
  salesmanIds: number[];
  managerIds: number[];
  deliveryDate: Date;
  sessions: Session[];
  dayHeadings: { label: string, dealCount: number }[];

  //handoverDiary: HandoverDiary;
  orderTypeIds: number[];
  vehicleTypeIds: number[];
  currentExecs: ExecPickerPerson[] = [];
  currentManagers: ExecPickerPerson[] = [];
  parms: HandoverDiaryParams;
  weeks: Array<Date>;

  //mySubscription: Subscription;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public getDealData: GetDealDataService,
  ) {


  }



  initParams(): void {

    //if (!this.deals) {

      this.deals = [];
      this.sites =  [this.constants.clone(this.selections.userSite)];
      this.sitesIds =  [this.selections.userSite.SiteId];
        this.vehicleTypeTypes =  this.constants.vehicleTypeTypes;
        this.orderTypeTypes =  this.constants.clone(this.constants.orderTypeTypesNoTrade);
        this.franchises =  this.constants.clone(this.constants.FranchiseCodes);
        this.salesmanIds =  null;
        this.managerIds = null;
        this.deliveryDate =  new Date(this.constants.thisWeekStartDate);
        this.sessions =  [];
        this.dayHeadings =  [];
    //}
  }

  getDeals(): void {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    this.setFilters();
    
    this.parms = this.getParams();

    this.getDealData.getHandoverDiary(this.parms).subscribe(res => {

      this.deals = res;

      this.deals.forEach(element => {
        element.HandoverDate = new Date(element.HandoverDate);
      });

      let sessionNames = ['AM', 'PM', 'Eve'];
      let sessions: Session[] = [];
      let dayHeadings: { label: string, dealCount: number }[] = [];

      sessionNames.forEach((session, sessionIteration) => {

        //define days
        let days: any[] = []

        for (let i = 0; i < 7; i++) {

          let dayDate: Date = new Date(this.deliveryDate.getTime());
          dayDate.setDate(this.deliveryDate.getDate() + i);

          let dayName: string =
            dayDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { weekday: 'long' }) + ' ' +
            this.constants.ordinalSuffix(dayDate.getDate()) + ' ' +
            dayDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short' })

          let dealsForDay;

          switch(sessionIteration) { 
            case 0: { 
              dealsForDay = this.deals.filter(d => d.DayName == dayDate.toLocaleDateString('en-GB', { weekday: 'long' }) && d.IsAM);
               break; 
            } 
            case 1: { 
              dealsForDay = this.deals.filter(d => d.DayName == dayDate.toLocaleDateString('en-GB', { weekday: 'long' }) && d.IsPM);
               break; 
            } 
            case 2: { 
              dealsForDay = this.deals.filter(d => d.DayName == dayDate.toLocaleDateString('en-GB', { weekday: 'long' }) && d.IsEvening); 
              break; 
           } 
            default: { 
               break; 
            } 
         } 
          
          let dealsForDaySorted = dealsForDay.sort((a, b) => a.HandoverDate.getTime() - b.HandoverDate.getTime())

          dayName = this.translateDayName(dayName);

          //put deals into that day
          days.push({ label: dayName, deals: dealsForDaySorted, date: dayDate })

          if (sessionIteration == 0) {
            dayHeadings.push({ label: dayName, dealCount: dealsForDaySorted.length })
          } else {
            dayHeadings[i].dealCount += dealsForDaySorted.length;
          }

        }
        //put days into session
        sessions.push({ label: session, days: days })

      })

      this.sessions = sessions;
      this.dayHeadings = dayHeadings;

      this.selections.triggerSpinner.next({ show: false });

    }, e => {

      // Errors
      throw(e);
      
    }, () => {


    })

  }

  ngDestroy() {
    //if(!!this.mySubscription){ this.mySubscription.unsubscribe(); }
  }

  private getParams(): HandoverDiaryParams
  {
    return {
      StartDate: this.deliveryDate,
      Sites: this.sites.map(x=>x.SiteId).toString(),
      OrderTypes: this.orderTypeIds.toString(),
      VehicleTypes: [...new Set(this.vehicleTypeIds)].toString(),
      Franchises: encodeURI(this.franchises.toString()),
      SalesmanIds: this.salesmanIds ? this.salesmanIds.toString() : null,
      ManagerIds: this.managerIds ? this.managerIds.toString(): null
    };
  }

  private setFilters(): void
  {

    // Order Type filter
    this.orderTypeIds = [];

    if(this.orderTypeTypes)
    {

      this.orderTypeTypes.forEach(y => {
        this.constants.OrderTypes.forEach(x => {
  
          if(x.Type == y){
            this.orderTypeIds.push(x.Id);
          }
        });
      });

    }

    // Vehicle Type filter
    this.vehicleTypeIds = [];
    
    if(this.vehicleTypeTypes)
    {

      this.vehicleTypeTypes.forEach(y => {
        this.constants.VehicleTypes.forEach(x => {
  
          if(x.Type == y){
            this.vehicleTypeIds.push(x.Id);
          }
        });
      });

    }

    // Salesmen
    this.salesmanIds = [];
    
    if(this.currentExecs != null){
      
      this.currentExecs.forEach(element => {
        
        this.salesmanIds.push(element.Id);
        
      });
    }
    
    //Managers
    this.managerIds = [];
    if(this.currentManagers != null){

      this.currentManagers.forEach(element => {

        this.managerIds.push(element.Id);
  
      });
    }

  }

  private translateDayName(dayName: string): string {

    var splitted = dayName.split(" ", 3);
    var firstWord = splitted[0];

    if(firstWord == 'Monday'){
      firstWord = this.constants.translatedText.Monday;
    }

    if(firstWord == 'Tuesday'){
      firstWord = this.constants.translatedText.Tuesday;
    }

    if(firstWord == 'Wednesday'){
      firstWord = this.constants.translatedText.Wednesday;
    }

    if(firstWord == 'Thursday'){
      firstWord = this.constants.translatedText.Thursday;
    }

    if(firstWord == 'Friday'){
      firstWord = this.constants.translatedText.Friday;
    }

    if(firstWord == 'Saturday'){
      firstWord = this.constants.translatedText.Saturday;
    }

    if(firstWord == 'Sunday'){
      firstWord = this.constants.translatedText.Sunday;
    }


    return firstWord + " " + splitted[1] + " " + splitted[2];
  }





}
