import { Component, OnInit, ViewChild, ElementRef, Input } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DataOriginsUpdate } from 'src/app/pages/dashboard/dashboard.model';
import { ConstantsService } from '../../services/constants.service';

@Component({
  selector: 'dataOriginsUpdatesModal',
  template: `
  <ng-template #dataOriginsUpdatesModalRef let-modal>

    <div class="modal-header">
      <h4 id="modal-basic-title" class="modal-title">
        {{ constants.translatedText.Dashboard_DataOriginsUpdates }}
      </h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div [ngClass]="constants.environment.customer" class="modal-body">
      <div class="tableData">
        <table>
          <thead>
            <tr class="labelRow">
              <td colspan="2">Data Origin</td>
              <td colspan="2">Data Table</td>
              <td colspan="1">Update Type</td>
              <td colspan="1">Update Timing</td>
              <td colspan="2">Last Updated</td>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataOriginsUpdate of data" class="dataRow">
              <td colspan="2">{{ dataOriginsUpdate.DataOrigin }}</td>
              <td colspan="2">{{ dataOriginsUpdate.DataTable }}</td>
              <td colspan="1">{{ dataOriginsUpdate.UpdateType }}</td>
              <td colspan="1">{{ dataOriginsUpdate.UpdateTiming }}</td>
              <td colspan="2" [ngClass]="{ 'green': dataOriginsUpdate.LastUpdate, 'amber': !dataOriginsUpdate.LastUpdate }">
                <span *ngIf="dataOriginsUpdate.LastUpdate">
                {{dataOriginsUpdate.LastUpdate|cph:'dateAndTime':0}}
              </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">
        {{ constants.translatedText.Close }}
      </button>
    </div>

  </ng-template>
  `,
  styles: [
    `
    .modal-body {
      min-height: unset !important;
    }

    table { table-layout: fixed; width: 100%; height: 100%; }
    tr.dataRow td { border: 1px black solid; padding-left: 0.5em; }
    tr.labelRow td { vertical-align: top; font-weight: 500; }    
    .tableData { height: 100%; }
    .green { background: green; color: white; text-align: center; }
    .amber { background: orange; color: white; text-align: center; }
  `
  ]
})

export class DataOriginsUpdatesModalComponent implements OnInit {
  @ViewChild('dataOriginsUpdatesModalRef', { static: true }) dataOriginsUpdatesModalRef: ElementRef;

  @Input() data: DataOriginsUpdate[];

  constructor(
    public constants: ConstantsService,
    public modalService: NgbModal
  ) { }

  ngOnInit() {
    this.initParams();
  }

  initParams() {
    this.modalService.open(this.dataOriginsUpdatesModalRef, { size: 'md', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then(() => {
      this.modalService.dismissAll();
    }, () => {
      this.modalService.dismissAll();
    });
  }
}
