import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Subscription } from "rxjs";
import { OrderBookService } from "src/app/pages/orderBook/orderBook.service";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { UnitsAndValue } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";



@Component({
  selector: 'unitsAndValue',
  templateUrl: './unitsAndValue.component.html',
  styleUrls: ['./unitsAndValue.component.scss']
})
export class UnitsAndValueComponent implements OnInit {

  @ViewChild('myChart', { static: true }) myChart: ElementRef;
  @Input() public data: UnitsAndValue;

  @Input() public title: string;
  @Input() public newDataEmitter: EventEmitter<void>;
  @Input() public dataSource: string;

  actualWidth: number;
  budgetWidth: number;
  subscription: Subscription;

  constructor(
   
    public constants: ConstantsService,
    public selections: SelectionsService,
    public orderBookService: OrderBookService,
    public dashService: DashboardService,

  ) {

  }

  ngOnInit(): void {

    this.initParams();
    
    this.subscription = this.newDataEmitter.subscribe(res => {
      this.initParams();
    })

  }


  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
  }



  initParams() {

  }

  navigateToOrderBook()
  {
    let orderTypes: string[];

    this.orderBookService.initOrderbook();

    this.orderBookService.accountingDate.startDate = this.constants.thisMonthStart;
    this.orderBookService.accountingDate.endDate = this.constants.thisMonthEnd;

    this.orderBookService.salesExecName = null;
    this.orderBookService.salesExecId = null;

    this.selections.selectedSites = this.dashService.chosenSites;

    if(this.title == this.constants.translatedText.Common_FixedAsset)
    {
      orderTypes = this.constants.orderTypeTypes.filter(x => x == 'Inmovilizado');
    }

    if(this.title == this.constants.translatedText.Common_ScrapVehicles)
    {
      orderTypes = this.constants.orderTypeTypes.filter(x => x == 'Desguaces');
    }
    if(this.title == this.constants.translatedText.Common_Assignments)
    {
      orderTypes = this.constants.orderTypeTypes.filter(x => x == 'Cesión');
    }

    this.orderBookService.orderTypeTypes = orderTypes;
    this.orderBookService.showOrderbook();
  }

  isClickableHeader(){
    return true;
  }

}


