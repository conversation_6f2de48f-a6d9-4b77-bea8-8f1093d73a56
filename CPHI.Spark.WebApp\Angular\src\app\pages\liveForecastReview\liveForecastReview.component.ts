import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CphPipe } from 'src/app/cph.pipe';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { LiveForecastStatusService } from '../liveForecastStatus/liveForecastStatus.service';
import { LiveForecastReviewService } from './liveForecastReview.service';

@Component({
  selector: 'app-liveForecastReview',
  templateUrl: './liveForecastReview.component.html',
  styleUrls: ['./liveForecastReview.component.scss', './../../../styles/components/_agGrid.scss']
})

export class LiveForecastReviewComponent implements OnInit {
  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: LiveForecastReviewService,
    public cphPipe: CphPipe,
    public router: Router,
    public statusService: LiveForecastStatusService
  ) { }

  ngOnInit(): void {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    
    if (!this.service.forecastReview) {
      this.service.allForecastMonths = this.statusService.getMonthsForPicker();
      this.service.getAllAvailableForecasts();
    } else {
      this.selections.triggerSpinner.emit({ show: false });
    }
  }

  selectForecastMonth(month: string) {
    this.service.selectedMonth = month;
    this.service.getForecastReview();
  }

  refresh(){
    this.service.getForecastReview();
  }

  goToStatus() {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });

    if (this.service.selectedMonth != this.statusService.selectedMonth) {
      this.statusService.selectedMonth = this.service.selectedMonth;
      this.statusService.selectedForecast = null;
      this.statusService.selectedForecastId = null;
    }

    this.router.navigateByUrl('/liveForecastStatus');
  }
}
