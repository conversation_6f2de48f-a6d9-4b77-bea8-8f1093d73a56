import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Subscription } from "rxjs";
import { ConstantsService } from "src/app/services/constants.service";
import { ReconditioningSummary } from "../../dashboard.model";



@Component({
  selector: 'reconditioningTile',
  templateUrl: './reconditioningTile.component.html',
  styleUrls: ['./reconditioningTile.component.scss']
})
export class ReconditioningTileComponent implements OnInit {

  @ViewChild('myChart', { static: true }) myChart: ElementRef;
  @Input() public data: ReconditioningSummary;

  @Input() public title: string;
  @Input() public newDataEmitter: EventEmitter<void>;
  @Input() public dataSource: string;

  actualWidth: number;
  budgetWidth: number;
  subscription: Subscription;

  constructor(
    public constants: ConstantsService

  ) {

  }

  ngOnInit(): void {

    this.initParams();
    
    this.subscription = this.newDataEmitter.subscribe(res => {
      this.initParams();
    })

  }


  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
  }



  initParams() {

  }


  isClickableHeader(){
    return true;
  }

}


