// Scratched effect
@mixin saw-tooth($width: 12px, $bg: #A9A9A9) {
    background-image:
      linear-gradient(135deg, $bg 50%, transparent 50%),
      linear-gradient(225deg, $bg 50%, transparent 50%),
      linear-gradient(45deg, $bg 50%, transparent 50%),
      linear-gradient(-45deg, $bg 50%, transparent 50%),
      linear-gradient(135deg, $bg 50%, transparent 50%),
      linear-gradient(45deg, $bg 50%, transparent 50%),
      linear-gradient(-135deg, $bg 50%, transparent 50%),
      linear-gradient(-45deg, $bg 50%, transparent 50%);
    background-position:
      top left, top left,
      bottom left, bottom left,
      top left, top left,
      top right, top right;
    background-size: $width $width;
    background-repeat: repeat-x, repeat-x, repeat-x, repeat-x, repeat-y, repeat-y, repeat-y, repeat-y;
}

// Payout spin
@keyframes spin {
    from { transform: rotateY(0deg); }
    to { transform: rotateY(-360deg); }
}
  
@keyframes depth {
    0% { text-shadow: 0 0 #44F05C; }
    25% { text-shadow: 1px 0 #44F05C, 2px 0 #44F05C, 3px 0 #44F05C, 4px 0 #44F05C, 5px 0 #44F05C; }
    50% { text-shadow: 0 0 #44F05C; }
    75% { text-shadow: -1px 0 #44F05C, -2px 0 #44F05C, -3px 0 #44F05C, -4px 0 #44F05C, -5px 0 #44F05C; }
    100% { text-shadow: 0 0 #44F05C; }
}

.scratchCard {
    font-family: 'ScratchCard';
    width: 100%;
    margin: 15px 0;
    padding: 25px;
    border-radius: 20px;
    border: 3px solid #000;
    background-image: linear-gradient(to top left, var(--mainAppColourVeryDark) 0%, var(--mainAppColourExtremelyLight) 100%);
    -webkit-box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    -moz-box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    transition: all .3s ease-in-out;

    &:hover {
        -webkit-box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
        -moz-box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
        box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
        margin-top: -1px;
    }

    .cardHeader {
        color: #fff;
        text-shadow: 0.05em 0 black, 0 0.05em black, -0.05em 0 black, 0 -0.05em black, -0.05em -0.05em black, -0.05em 0.05em black, 0.05em -0.05em black, 0.05em 0.05em black; 
    
        h1 {
            margin: 0;
            
        }

        h3 {
            margin: 0.5em 0;
            
        }
    }

    .coinsContainer {
        background-image: url("../../../../assets/imgs/events/scratchcard/jewel.png");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }

    .scratcher {
        height: 67px;
        padding: 20px 10px;
        border-radius: 5px;
        border: 2px solid #000;
        background-color: #A9A9A9;
        color: transparent;
        cursor: pointer;
        @include saw-tooth();

        h1,
        h3 {
            margin: 0px;
        }
    }

    .scratcher.hidden {
        background-position: center;
        background-repeat: no-repeat;
        background-size: 35px;
        background-image: url('../../../../assets/imgs/events/scratchcard/car.svg');
    }

    .scratcher.revealed {
        background-color: #fff;
        color: black;
        transition: background-color 1s ease-in-out, background-image 1s ease-out, color 1s ease-in-out;

        .row {
            .col {
                h1.payout {
                    
                    color: #44F05C;
                    transition: color 0.5s ease-in-out;
                    animation-name: spin, depth;
                    animation-timing-function: linear;
                    animation-iteration-count: 1;
                    animation-duration: 0.5s;
                    animation-delay: 0.3s;
                    -webkit-text-stroke: black 2px;
                    transform: scale(1.4);
                }
            }
        }
    }
}

.scratchCard.summer {
    font-family: 'ScratchCard2';
    background-image: linear-gradient(to top left, #FAB65F 0%, #FF8600 100%);

    .coinsContainer {
        background-image: url("../../../../assets/imgs/events/scratchcard/pineapple.png");
    }
}

.scratchCard.autumn {
    font-family: 'ScratchCard2';
    background-image: linear-gradient(to top left, #91d944 0%, #68a234 100%);

    .coinsContainer {
        background-image: url("../../../../assets/imgs/events/scratchcard/leaf.png");
    }
}

.scratchCard.spring {
    font-family: 'ScratchCard2';
    background-image: linear-gradient(to top left, #8eade7 0%, #5F8ADB 100%);

    .coinsContainer {
        background-image: url("../../../../assets/imgs/events/scratchcard/daffodil.png");
    }
}

.scratchCard.custom {
    font-family: 'ScratchCard2';
    background-image: linear-gradient(to top left, #1A2D6E 0%, #060434 100%);

    .coinsContainer {
        background-image: url("../../../../assets/imgs/events/scratchcard/treasure.png");
    }
}