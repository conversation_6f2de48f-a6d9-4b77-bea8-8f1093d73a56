import {FactorItemHorizontalBand, StrategyFactorItemVM} from "./StrategyFactorItemVM";
import {StrategyFactorName} from "./StrategyFactorName";
import {StrategyFactorHorizontalBand, StrategyFactorVM} from "./StrategyFactorVM";
import {AutotraderService} from "../services/autotrader.service";


export class StrategyVersionVM {

   Id: number | null;
   CreatedByName: string;
   CreatedDate: Date | string;
   Name: string;
   Comment: string;
   StrategyFactors: StrategyFactorVM[];
   FirstUsed: Date | null;
   LastUsed: Date | null;

   SaveAsNew: boolean | null;

   constructor(itemIn?: Partial<StrategyVersionVM>) {

      if (itemIn) {

         Object.assign(this, itemIn); // assigns all properties from itemIn

         this.StrategyFactors = [];
         itemIn.StrategyFactors.forEach(factor => {
            const sortedItems = factor.StrategyFactorItems.sort((a, b) => a.Value - b.Value);
            factor.StrategyFactorItems = sortedItems;

            // Sort by label as integer for specific factor types
            if (factor.Name === StrategyFactorName.DaysListed ||
                factor.Name === StrategyFactorName.RetailRating ||
                factor.Name === StrategyFactorName.DaysInStock) {
               factor.StrategyFactorItems.sort((a, b) => {
                  const aLabel = parseInt(a.Label) || 0;
                  const bLabel = parseInt(b.Label) || 0;
                  return aLabel - bLabel;
               });
            }

            // Sort ValueBand using predefined order
            if (factor.Name === StrategyFactorName.ValueBand) {
               const order = AutotraderService.getSortOrderForValueBand();
               factor.StrategyFactorItems.sort((a, b) => {
                  const indexA = order.indexOf(a.Label);
                  const indexB = order.indexOf(b.Label);
                  return indexA - indexB;
               });
            }

            // Sort RegYear with <2010 first, then ascending years
            if (factor.Name === StrategyFactorName.RegYear) {
               factor.StrategyFactorItems.sort((a, b) => {
                  if (a.Label === '<2010') return -1;
                  if (b.Label === '<2010') return 1;
                  const yearA = parseInt(a.Label);
                  const yearB = parseInt(b.Label);
                  return yearA - yearB;
               });
            }

            // Sort LiveMarketCondition by numeric value (ascending order from -200 to +200)
            if (factor.Name === StrategyFactorName.LiveMarketCondition) {
               factor.StrategyFactorItems.sort((a, b) => {
                  const valueA = parseInt(a.Label);
                  const valueB = parseInt(b.Label);
                  return valueA - valueB;
               });
            }

            this.StrategyFactors.push(new StrategyFactorVM(null, null, factor));
         });

         // Ensure mileage factors sort despite < prefix

         let mileageFactors = this.StrategyFactors.find((x) => x.Name == StrategyFactorName.Mileage)?.StrategyFactorItems;

         if (mileageFactors) {
           mileageFactors.sort((a, b) => {
               const aLabel = Number(a.Label.replace('<','').trim());
               const bLabel = Number(b.Label.replace('<','').trim());
               return aLabel - bLabel;
           });
         }

         this.processStrategy();

      } else {
         this.StrategyFactors = [];
      }
      // this.StrategyFactors.map(factor=>{
      //     factor.isOpen = false;
      // })
   }


   get pricingPolicyTextSummary() {
      const options: Intl.DateTimeFormatOptions = {day: 'numeric', month: 'long', year: 'numeric'};
      const date = new Date(this.CreatedDate);
      const formattedDate = date.toLocaleDateString('en-GB', options);
      return `Pricing policy #${this.Id}: ${this.Name} updated ${formattedDate} by ${this.CreatedByName}`
   }


   private processStrategy() {

      this.StrategyFactors.map(factor => {

         if (factor.Name === StrategyFactorName.MatchCheapestCompetitor || factor.Name === StrategyFactorName.AchieveMarketPositionScore) { //convert the decimal value to bool property value
            const newItems: StrategyFactorItemVM[] = [];
            const competitorTypes: string[] = ['Independent', 'Franchise', 'Supermarket', 'Private'];
            factor.StrategyFactorItems.map(item => {
               if (competitorTypes.includes(item.Label)) {
                  item.BoolValue = item.Value == 1 ? true : false;
               }
            });

         } else if (factor.Name === StrategyFactorName.RR_DL_Matrix && !factor.horizontalBandLabels) { //!factor.horizontalBandLabels indicates it's not already been 'expanded' from the back end into a 2 dimensional array
            //need to convert into 2 dimensional type array
            const newItems: StrategyFactorItemVM[] = [];

            factor.StrategyFactorItems.map(item => {
               const [rrLabel, dlLabel] = item.Label.split('|');
               const rrString = rrLabel.replace('RR', '');
               const rrValue = parseInt(rrString);
               const dlString = dlLabel.replace('DL', '');
               const dlValue = parseInt(dlString);

               item.daysListedString = dlLabel;
               item.daysListed = dlValue;
               item.retailRatingString = rrString;
               item.retailRating = rrValue;

            });

            this.continueSettingUpFactor(factor, newItems);
         } else if (factor.Name === StrategyFactorName.RR_DS_Matrix && !factor.horizontalBandLabels) { //!factor.horizontalBandLabels indicates it's not already been 'expanded' from the back end into a 2 dimensional array
            //need to convert into 2 dimensional type array
            const newItems: StrategyFactorItemVM[] = [];

            factor.StrategyFactorItems.map(item => {
               const [rrLabel, dsLabel] = item.Label.split('|');
               const rrString = rrLabel.replace('RR', '');
               const rrValue = parseInt(rrString);
               const dsString = dsLabel.replace('DS', '');
               const dsValue = parseInt(dsString);

               item.daysListedString = dsLabel;
               item.daysListed = dsValue;
               item.retailRatingString = rrString;
               item.retailRating = rrValue;

            });

            this.continueSettingUpFactor(factor, newItems);
         } else if (factor.Name === StrategyFactorName.DTS_DL_Matrix && !factor.horizontalBandLabels) { //!factor.horizontalBandLabels indicates it's not already been 'expanded' from the back end into a 2 dimensional array
            //need to convert into 2 dimensional type array
            const newItems: StrategyFactorItemVM[] = [];

            factor.StrategyFactorItems.map(item => {
               const [dtsLabel, dlLabel] = item.Label.split('|');
               const dtsString = dtsLabel.replace('DTS', '');
               const dtsValue = parseInt(dtsString);
               const dlString = dlLabel.replace('DL', '');
               const dlValue = parseInt(dlString);

               item.daysListedString = dlLabel;
               item.daysListed = dlValue;
               item.retailRatingString = dtsString;
               item.retailRating = dtsValue;

            });

            this.continueSettingUpFactor(factor, newItems);
         } else if (factor.Name === StrategyFactorName.RR_DB_Matrix && !factor.horizontalBandLabels) { //!factor.horizontalBandLabels indicates it's not already been 'expanded' from the back end into a 2 dimensional array
            //need to convert into 2 dimensional type array
            const newItems: StrategyFactorItemVM[] = [];

            factor.StrategyFactorItems.map(item => {
               const [rrLabel, dsLabel] = item.Label.split('|');
               const rrString = rrLabel.replace('RR', '');
               const rrValue = parseInt(rrString);
               const dbString = dsLabel.replace('DB', '');
               const dbValue = parseInt(dbString);

               item.daysListedString = dsLabel;
               item.daysListed = dbValue;
               item.retailRatingString = rrString;
               item.retailRating = rrValue;

            });

            this.continueSettingUpFactor(factor, newItems);
         }
         ;
      })
   }


   private continueSettingUpFactor(factor: StrategyFactorVM, newItems: StrategyFactorItemVM[]) {
      factor.StrategyFactorItems = factor.StrategyFactorItems.sort((a, b) => {
         // First, compare by retailRating
         const ratingDifference = a.retailRating - b.retailRating;
         // If retailRating is different, use that result
         if (ratingDifference !== 0) {
            return ratingDifference;
         }
         // Otherwise, compare by daysListed
         return a.daysListed - b.daysListed;
      });

      //now can guarantee order, so walk through and convert
      factor.horizontalBandLabels = [];
      factor.StrategyFactorItems.forEach(item => {
         factor.horizontalBandLabels.push({value: item.daysListed});

         var existingFactorItem = newItems.find(x => x.Label == item.retailRatingString);
         if (existingFactorItem) {
            //we already have this strategyFactorItem, just add this item to it
            const newBand: FactorItemHorizontalBand = {id: item.Id, value: item.Value};
            existingFactorItem.horizontalBands.push(newBand);
         } else {
            //is a new strategySactorItem
            const newBand: FactorItemHorizontalBand = {id: item.Id, value: item.Value};
            const newItem: StrategyFactorItemVM = new StrategyFactorItemVM(null, item.retailRatingString, item.Value, [newBand]);
            newItems.push(newItem);
         }
      });


      factor.StrategyFactorItems = newItems;

      const uniqueItems: StrategyFactorHorizontalBand[] = [];
      const seenValues: Set<number> = new Set();

      for (const item of factor.horizontalBandLabels) {
         if (!seenValues.has(item.value)) {
            seenValues.add(item.value);
            uniqueItems.push(item);
         }
      }
      factor.horizontalBandLabels = uniqueItems;
   }
}


