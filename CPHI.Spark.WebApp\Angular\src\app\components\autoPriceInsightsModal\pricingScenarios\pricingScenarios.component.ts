import {DatePipe} from '@angular/common';
import {Component, Input, OnInit} from '@angular/core';
import {DayToSellAndPriceIndicator} from 'src/app/model/DayToSellAndPriceIndicator';
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {AutoPriceInsightsModalService} from '../autoPriceInsightsModal.service';
import {
   GetEstimatedDayToSellAndPriceIndicatorParams
} from 'src/app/pages/performanceTrends/GetEstimatedDayToSellAndPriceIndicatorParams';
import {DayToSellAndPriceIndicatorScenario} from 'src/app/model/DayToSellAndPriceIndicatorScenario';

@Component({
   selector: 'pricingScenarios',
   templateUrl: './pricingScenarios.component.html',
   styleUrls: ['./pricingScenarios.component.scss']
})
export class PricingScenariosComponent implements OnInit {

  @Input() hideCheck: boolean = false;

  newPriceCheckValue: number
  daysToSell: number;
  priceIndicator: string;
  checking:boolean = false;
  preventSave: boolean = false;

  private debounceTimeout: any;

   constructor(
      public constantsService: ConstantsService,
      private datePipe: DatePipe,
      private getDataService: GetDataMethodsService,
      public service: AutoPriceInsightsModalService
   ) {


   }

   ngOnInit(): void {

   }

   getEstimatedDayToSellAndPriceIndicator() {
      if (this.preventSave) {
         return;
      }
      this.checking = true;

      const parms: GetEstimatedDayToSellAndPriceIndicatorParams = {

         AdvertiserIds: [this.service.modalItem.AdvertDetail.RetailerSiteRetailerId],
         DerivativeId: this.service.modalItem.AdvertDetail.DerivativeId,
         FirstRegisteredDate: this.service.modalItem.AdvertDetail.FirstRegisteredDate,
         Mileage: this.service.modalItem.AdvertDetail.OdometerReading,
         StrategyPrice: this.newPriceCheckValue,
         VehicleHasOptionsSpecified: this.service.modalItem.AdvertDetail.VehicleHasOptionsSpecified,
         VehicleAdvertPortalOptions: this.service.modalItem.AdvertDetail.VehicleAdvertPortalOptions,
         AverageValuation: this.service.modalItem.AdvertDetail.ValuationMktAvRetail,
         AdjustedValuation: this.service.modalItem.AdvertDetail.ValuationAdjRetail

      };

      setTimeout(() => {
         this.checking = true;
         this.getDataService.getAutoPriceEstimatedDayToSellAndPriceIndicator(parms)
            .subscribe((res: DayToSellAndPriceIndicator) => {
               if (res.DaysToSellResults.length === 0) {
                  this.constantsService.toastDanger('Failed to save pricing scenario. Please try again.');
                  return;
               }
               this.daysToSell = res.DaysToSellResults.find(x =>
                  x.RetailerSiteRetailerId === this.service.modalItem.AdvertDetail.RetailerSiteRetailerId).DaysToSell;
               this.priceIndicator = res.PriceIndicator;
               this.checking = false;
            }, error => {
               this.checking = false;
               console.error('Failed to retrieve estimated Day to Sell & Price Indicator', error);
            });
      }, 200);
   }


   setNewPrice(event: any) {

      if (!event.target || (event.target && event.target.value == '')) return;

      this.newPriceCheckValue = parseFloat(event.target.value.replace(/[^\d.]/g, ''));

      // Debounce the checkAgainstValuation call
      clearTimeout(this.debounceTimeout);
      this.debounceTimeout = setTimeout(() => {
         this.checkAgainstValuation();
      }, 500);
   }

   checkAgainstValuation() {
      const advertisedPriceScenario: DayToSellAndPriceIndicatorScenario =
         this.service.modalItem.PricingScenarios.find(x => x.Label.includes('Advertised Price'));

      const advertisedPrice: number = parseFloat(advertisedPriceScenario.Label.replace(/\D/g, ''));

      if (this.newPriceCheckValue < 0.9 * advertisedPrice || this.newPriceCheckValue > 1.1 * advertisedPrice) {
         this.preventSave = true;
      } else {
         this.preventSave = false;
      }
   }
}
