import { DatePipe } from "@angular/common";
import { Component, Input, OnInit } from "@angular/core";
import { DayToSellAndPriceIndicator } from "src/app/model/DayToSellAndPriceIndicator";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { AutoPriceInsightsModalService } from "../autoPriceInsightsModal.service";
import { GetEstimatedDayToSellAndPriceIndicatorParams } from "src/app/pages/performanceTrends/GetEstimatedDayToSellAndPriceIndicatorParams";
import { DayToSellAndPriceIndicatorScenario } from "src/app/model/DayToSellAndPriceIndicatorScenario";
import { FormBuilder } from "@angular/forms";
import { below90Validator, over110Validator } from "../../formValidators";
import { debounceTime } from "rxjs/operators";

@Component({
   selector: "pricingScenarios",
   templateUrl: "./pricingScenarios.component.html",
   styleUrls: ["./pricingScenarios.component.scss"],
})
export class PricingScenariosComponent implements OnInit {
   @Input() hideCheck: boolean = false;

   newPriceCheckValue: number;
   daysToSell: number;
   priceIndicator: string;
   checking: boolean = false;
   preventSave: boolean = false;
   

   private debounceTimeout: any;

   constructor(
      public constantsService: ConstantsService,
      private datePipe: DatePipe,
      private getDataService: GetDataMethodsService,
      public service: AutoPriceInsightsModalService,
      private fb: FormBuilder
   ) {}

   form = this.fb.group({
      price: [null, []],  //we will do later
   });

   ngOnInit(): void {

      this.form.get('price')?.valueChanges
    .pipe(debounceTime(600)) // debounce delay in ms
    .subscribe(value => {
      const control = this.form.get('price');
      if (control) {
        const errors: any = {};

        const advertisedPrice = this.service.modalItem.AdvertDetail.ValuationAdjRetail; // or calculate it
        if (value != null) {
          if ((value as number) < 0.9 * advertisedPrice) {
            errors.tooLow = true;
          } else if ((value as number) > 1.1 * advertisedPrice) {
            errors.tooHigh = true;
          }
        }

        control.setErrors(Object.keys(errors).length ? errors : null);
      }
    });
    
   }

   getEstimatedDayToSellAndPriceIndicator() {
      if (this.preventSave) {
         return;
      }
      this.checking = true;

      const parms: GetEstimatedDayToSellAndPriceIndicatorParams = {
         AdvertiserIds: [this.service.modalItem.AdvertDetail.RetailerSiteRetailerId],
         DerivativeId: this.service.modalItem.AdvertDetail.DerivativeId,
         FirstRegisteredDate: this.service.modalItem.AdvertDetail.FirstRegisteredDate,
         Mileage: this.service.modalItem.AdvertDetail.OdometerReading,
         StrategyPrice: this.newPriceCheckValue,
         VehicleHasOptionsSpecified: this.service.modalItem.AdvertDetail.VehicleHasOptionsSpecified,
         VehicleAdvertPortalOptions: this.service.modalItem.AdvertDetail.VehicleAdvertPortalOptions,
         AverageValuation: this.service.modalItem.AdvertDetail.ValuationMktAvRetail,
         AdjustedValuation: this.service.modalItem.AdvertDetail.ValuationAdjRetail,
      };

      setTimeout(() => {
         this.checking = true;
         this.getDataService.getAutoPriceEstimatedDayToSellAndPriceIndicator(parms).subscribe(
            (res: DayToSellAndPriceIndicator) => {
               if (res.DaysToSellResults.length === 0) {
                  this.constantsService.toastDanger("Failed to save pricing scenario. Please try again.");
                  return;
               }
               this.daysToSell = res.DaysToSellResults.find(
                  (x) => x.RetailerSiteRetailerId === this.service.modalItem.AdvertDetail.RetailerSiteRetailerId
               ).DaysToSell;
               this.priceIndicator = res.PriceIndicator;
               this.checking = false;
            },
            (error) => {
               this.checking = false;
               console.error("Failed to retrieve estimated Day to Sell & Price Indicator", error);
            }
         );
      }, 200);
   }

  

   onSubmit(): void {
      this.newPriceCheckValue = this.form.controls.price.value;
      if(this.form.invalid){
         return;
      }
      this.getEstimatedDayToSellAndPriceIndicator();
   }
}
