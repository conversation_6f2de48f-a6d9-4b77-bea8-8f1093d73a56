import {Component, Input, OnInit} from '@angular/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {
   DomLayoutType,
   GridApi,
   GridOptions,
   GridReadyEvent,
   ICellRendererParams,
   ModelUpdatedEvent,
   RowClassParams,
   RowClickedEvent
} from 'ag-grid-community';
import {CphPipe} from 'src/app/cph.pipe';
import {SameModelAdvert} from 'src/app/model/SameModelAdvert';
import {SimpleExampleItem} from 'src/app/model/SimpleExampleItem';
import {AGGridMethodsService} from 'src/app/services/agGridMethods.service';
import {ColumnTypesService} from 'src/app/services/columnTypes.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {localeEs} from 'src/environments/locale.es.js';
import {StatsVehicle} from 'src/app/model/StatsVehicle';
import {StatsSiteDashboardPageComponentType} from '../sitesLeague.component';
import {StatsSiteDashboard} from 'src/app/model/StatsSiteDashboard';
import {
   PricingVsStrategyBarsComponent
} from 'src/app/_cellRenderers/pricingVsStrategyBars/pricingVsStrategyBars.component';
import {CPHColDef} from 'src/app/model/CPHColDef';
import {CPHColGroupDef} from 'src/app/model/CPHColGroupDef';
import {
   HorizontalDotBoxComponent,
   HorizontalDotBoxParams
} from 'src/app/_cellRenderers/horizontalDotBox/horizontalDotBox.component';
import {RetailRatingBandBarComponent} from 'src/app/_cellRenderers/retailRatingBandBar/retailRatingBandBar.component';
import {
   PerformanceRatingBarComponent
} from 'src/app/_cellRenderers/performanceRatingBar/performanceRatingBar.component';
import {StatsDashboardService} from '../../statsDashboard/statsDashboard.service';
import {Router} from '@angular/router';
import {MenuItemNew} from 'src/app/model/main.model';

// This describes the properties that a service must commit to having, when it's used with this component.
// In any service that wants to use this compnent, it must implement this interface using this syntax:
//   export class MyCoolService implements MyComponentParams  { etc.
export interface StatsSiteDashboardTableParams {
   gridRef: SitesLeagueTableComponent;
   sites: StatsSiteDashboard[];
   dealWithFilteredItems: (filteredItems: StatsSiteDashboard[], callingComponent: StatsSiteDashboardPageComponentType) => void;
   showDaysListed: boolean;
   showByRetailRating: boolean;
   showByPerformanceIndicator: boolean;
   showByLowImageCountIndicator: boolean;
}

@Component({
   selector: 'sitesLeagueTable',
   templateUrl: './sitesLeagueTable.component.html',
   styleUrls: ['./sitesLeagueTable.component.scss']
})
export class SitesLeagueTableComponent implements OnInit {

   @Input() tableParams: StatsSiteDashboardTableParams;
   gridOptions: GridOptions;
   gridApi: GridApi;
   indicateNewData: boolean;
   domLayout: DomLayoutType;
;
   rowHeight: number;
   headerHeight: number;

   constructor(
      public gridHelpersService: AGGridMethodsService,
      public constantsService: ConstantsService,
      public cphPipe: CphPipe,
      private modalService: NgbModal,
      private columnTypesService: ColumnTypesService,
      public statsDashboardService: StatsDashboardService,
      public router: Router
   ) {
   }

   ngOnInit(): void {
      this.rowHeight = this.gridHelpersService.getRowHeight(70);
      this.headerHeight = this.gridHelpersService.getRowHeight(40);
      this.workoutDomLayout();
      this.setGridDefinitions();
   }

   setGridDefinitions() {
      this.gridOptions = {
         getContextMenuItems: (params) =>
            this.gridHelpersService.getContextMenuItems(params),
         getLocaleText: (params: any) =>
            this.constantsService.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
         defaultColDef: {
            resizable: true,
            sortable: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight()
            },

            autoHeight: true,
            // autoHeaderHeight:true,
            floatingFilter: false
         },
         rowHeight: this.rowHeight,
         headerHeight: this.headerHeight,

         // getRowHeight: (params) => {
         //   return 70;
         // },
         // getRowClass: (params) => {
         //   return this.rowClassGetter(params)
         // },
         columnTypes: {
            ...this.columnTypesService.provideColTypes([]),
         },
         onRowDoubleClicked: (params) => this.onRowDoubleClick(params),
         rowData: this.tableParams.sites.filter(x => x.RetailerSiteName != 'Total'),//.filter(x=>x.RetailerSiteName == 'Volvo Cars Oxford'),
         pinnedBottomRowData: this.tableParams.sites.filter(x => x.RetailerSiteName == 'Total'),
         columnDefs: this.provideColDefs(this.tableParams.sites),
         onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
         onFilterChanged: (event) => this.onFilterChanged(event),
         //onModelUpdated: (event: ModelUpdatedEvent) => this.gridApi?.sizeColumnsToFit()
      };
   }


   provideColDefs(rowData: StatsSiteDashboard[]): (CPHColDef | CPHColGroupDef)[] {
      let maxAbove = 0;
      let maxBelow = 0;
      let lowerBias = 0;
      let highestBias = 0;
      rowData.filter(x => x.RetailerSiteName != 'Total').forEach(row => {
         const aboveAverage = row.VsStrategyPrice.VeryOverPriced + row.VsStrategyPrice.OverPriced + (row.VsStrategyPrice.OnStrategy / 2);
         const belowAverage = row.VsStrategyPrice.VeryUnderPriced + row.VsStrategyPrice.UnderPriced + (row.VsStrategyPrice.OnStrategy / 2);
         const bias = aboveAverage - belowAverage;
         if (bias > highestBias) {
            highestBias = bias;
         }
         if (bias < lowerBias) {
            lowerBias = bias;
         }

         if (aboveAverage > maxAbove) {
            maxAbove = aboveAverage;
         }
         if (belowAverage > maxBelow) {
            maxBelow = belowAverage;
         }
      })

      const range = maxAbove + maxBelow;
      const bias = maxAbove - maxBelow;
      // let shiftLeft = (highestBias - lowerBias)/2;


      let maxDays = 0;
      rowData.filter(x => x.RetailerSiteName != 'Total').forEach(row => {
         row.Vehicles.forEach(ad => {
            if (ad.DaysListed > maxDays) {
               maxDays = ad.DaysListed;
            }
         })
      })

      maxDays = 150;

      const regularColsStartingWidth = 60;
      const regularColsMaxWidth = 70;
      const openableColGroup = 100;


      return [
         {
            headerName: 'Site',
            pinned: 'left',
            maxWidth: 200,
            colId: 'RetailerSiteName',
            field: 'RetailerSiteName',
            type: 'labelSetFilter',
            width: 200
         },

         //Logins this week
         {
            headerName: 'Logins',
            maxWidth: regularColsMaxWidth,
            colId: 'LoginsThisWeek',
            field: 'Usage.UserLoginsThisWeek',
            type: 'number',
            width: regularColsStartingWidth
         },

         {
            headerName: 'Opt Outs',
            maxWidth: regularColsMaxWidth,
            colId: 'AdvertsOptedOut',
            field: 'PriceChanges.AdvertsOptedOut',
            type: 'number',
            width: regularColsStartingWidth
         },

         {
            headerName: '%',
            maxWidth: regularColsMaxWidth,
            colId: 'PercentageOptedOut',
            field: 'PercentageOptedOut',
            type: 'percent',
            width: regularColsStartingWidth
         },

         //Published
         {
            headerName: 'Total Adverts', children: [
               {
                  headerName: 'Total',
                  maxWidth: regularColsMaxWidth,
                  colId: 'Total',
                  field: 'AdCounts.RelevantTotal',
                  type: 'number',
                  width: regularColsStartingWidth
               },
               {
                  headerName: 'Price vs Strategy',
                  colId: 'PricingVsStrategy',
                  cellRenderer: PricingVsStrategyBarsComponent,
                  floatingFilter: false,
                  cellRendererParams: {range: range, shiftLeft: bias},
                  type: 'special',
                  width: 400,
                  minWidth: 400,
                  maxWidth: 400
               },

               {
                  headerName: '% on strategy', maxWidth: regularColsMaxWidth, colId: 'OnStrategy%',
                  valueGetter: (params) => this.constantsService.div(params.data.VsStrategyPrice.OnStrategy, params.data.AdCounts.RelevantTotal),
                  type: 'percent', width: 90
               },
               {
                  headerName: 'Average Gap',
                  maxWidth: regularColsMaxWidth,
                  colId: 'GapToStrategy',
                  field: 'Averages.AveragesGapToStrategyPrice',
                  type: 'currency',
                  width: 90
               },
            ]
         },


         // Advert Quality
         {
            headerName: 'Advert Quality', children: [
               {
                  headerName: 'Low Quality',
                  maxWidth: regularColsMaxWidth,
                  colId: 'LowQuality',
                  field: 'AdCounts.LowQuality',
                  type: 'number',
                  width: regularColsStartingWidth
               },
               {
                  headerName: 'Attn. Grabber',
                  maxWidth: regularColsMaxWidth,
                  colId: 'NoAttentionGrabber',
                  field: 'AdCounts.NoAttentionGrabber',
                  type: 'number',
                  width: 90
               },
               {
                  headerName: '<9 images',
                  maxWidth: regularColsMaxWidth,
                  colId: 'LessThan9Images',
                  field: 'AdCounts.LessThan9Images',
                  type: 'number',
                  width: regularColsStartingWidth
               },
               {
                  headerName: 'No Images',
                  maxWidth: regularColsMaxWidth,
                  colId: 'NoImages',
                  field: 'AdCounts.NoImages',
                  type: 'number',
                  width: regularColsStartingWidth
               },
               {
                  headerName: 'No Video',
                  maxWidth: regularColsMaxWidth,
                  colId: 'NoVideo',
                  field: 'AdCounts.NoVideo',
                  type: 'number',
                  width: regularColsStartingWidth
               },
            ]
         },

         // Days Listed
         {
            headerName: 'Days Listed', hide: !this.tableParams.showDaysListed, children: [
               {
                  headerName: 'Average',
                  hide: !this.tableParams.showDaysListed,
                  maxWidth: openableColGroup,
                  colId: 'DaysListed',
                  field: 'Averages.AveragesDaysListed',
                  type: 'number',
                  width: 90
               },
               {
                  headerName: '',
                  hide: !this.tableParams.showDaysListed,
                  colId: 'DaysListedDots',
                  cellRenderer: HorizontalDotBoxComponent,
                  floatingFilter: false,
                  cellRendererParams: {maxDays: maxDays},
                  type: 'special',
                  width: 400
               },
            ]
         },

         // Retail Rating bars
         {
            headerName: 'By Retail Rating', hide: !this.tableParams.showByRetailRating, children: [
               {
                  headerName: 'Average',
                  hide: !this.tableParams.showByRetailRating,
                  maxWidth: openableColGroup,
                  colId: 'RetailRating',
                  field: 'Averages.AveragesRetailRating',
                  type: 'number',
                  width: regularColsStartingWidth
               },
               {
                  headerName: '',
                  hide: !this.tableParams.showByRetailRating,
                  colId: 'RetailRatingBars',
                  cellRenderer: RetailRatingBandBarComponent,
                  floatingFilter: false,
                  cellRendererParams: {maxDays: maxDays},
                  type: 'special',
                  width: 400
               },
            ]
         },

         // Performance Rating bars
         {
            headerName: 'By Performance Indicator', hide: !this.tableParams.showByPerformanceIndicator, children: [
               {
                  headerName: 'Average',
                  hide: !this.tableParams.showByPerformanceIndicator,
                  maxWidth: openableColGroup,
                  colId: 'PerformanceRating',
                  field: 'Averages.AveragesPerformanceRating',
                  type: 'number',
                  width: 100
               },
               {
                  headerName: '',
                  hide: !this.tableParams.showByPerformanceIndicator,
                  colId: 'RetailRatingBars',
                  cellRenderer: PerformanceRatingBarComponent,
                  floatingFilter: false,
                  cellRendererParams: {maxDays: maxDays},
                  type: 'special',
                  width: 400
               },
            ]
         },
         // Performance Rating bars
         {
            headerName: 'By Image Count', hide: !this.tableParams.showByLowImageCountIndicator, children: [
               {
                  headerName: '<5 Images',
                  hide: !this.tableParams.showByLowImageCountIndicator,
                  maxWidth: openableColGroup,
                  colId: 'Under5Images',
                  type: 'percent',
                  width: 70,
                  valueGetter: params => this.calcPercentage(params.data.AdCounts.Under5Images, params.data.AdCounts)
               },
               {
                  headerName: '< 10 Images',
                  hide: !this.tableParams.showByLowImageCountIndicator,
                  colId: 'Under10Images',
                  floatingFilter: false,
                  type: 'percent',
                  width: 70,
                  valueGetter: params => this.calcPercentage(params.data.AdCounts.Under10Images, params.data.AdCounts)
               },
               {
                  headerName: '< 20 Images',
                  hide: !this.tableParams.showByLowImageCountIndicator,
                  colId: 'Under20Images',
                  floatingFilter: false,
                  type: 'percent',
                  width: 70,
                  valueGetter: params => this.calcPercentage(params.data.AdCounts.Under20Images, params.data.AdCounts)
               },
               {
                  headerName: '20+ Images',
                  hide: !this.tableParams.showByLowImageCountIndicator,
                  colId: 'Over20Images',
                  floatingFilter: false,
                  type: 'percent',
                  width: 70,
                  valueGetter: params => this.calcPercentage(params.data.AdCounts.Over20Images, params.data.AdCounts)
               },
            ]
         },
      ];
   }

   rowClassGetter(params: RowClassParams<any, any>): string[] {
      const item: SimpleExampleItem = params.data;
      return item.isChosen ? ['brightHighlight'] : []
   }

   getImage(params: ICellRendererParams) {
      const row: SameModelAdvert = params.data;

      if (!row || !row?.ImageUrl) return '';
      return `<img style="height: 50px; width: 100%;" src=${row.ImageUrl} />`;
   }

  onGridReady(event: GridReadyEvent) {
    this.gridApi = event.api;
    //this.gridApi.sizeColumnsToFit();
    this.tableParams.gridRef = this;
  }

  dealWithNewDataSitesLeague(data: StatsSiteDashboard[]) {
    this.gridApi.setRowData(data.filter(x => x.RetailerSiteName != 'Total'));
    this.gridApi.setPinnedBottomRowData(data.filter(x => x.RetailerSiteName == 'Total'));
    this.gridApi.sizeColumnsToFit();
    this.indicateNewData = true;
    setTimeout(() => { this.indicateNewData = false; }, 1000);
  }

   dealWithNewData(data: StatsVehicle[]) {
      this.gridApi.setRowData(data);
      this.gridApi.sizeColumnsToFit();
      this.indicateNewData = true;
      setTimeout(() => {
         this.indicateNewData = false;
      }, 1000);
   }

   dealWithNewColumnChoices() {
      const colDefs = this.provideColDefs(this.tableParams.sites);
      this.gridApi.setColumnDefs(colDefs);
   }

   onFilterChanged(event: any) {
      // Get the filtered rows

      const filteredNodes = [];
      this.gridApi.forEachNodeAfterFilter(node => {
         filteredNodes.push(node);
      })
      const filteredItems = filteredNodes.map(node => node.data); // Get data from each node

      // Call the parent service method and pass the filtered items
      this.tableParams.dealWithFilteredItems(filteredItems, StatsSiteDashboardPageComponentType.grid);
   }

   onRowDoubleClick(params: RowClickedEvent<any, any>): void {
      const item: SimpleExampleItem = params.data;
      if (params.node.isRowPinned()) {
         //we clicked total
         this.statsDashboardService.chosenSiteNames = new Set<string>(this.constantsService.RetailerSites.map(x => x.Name));
      } else {
         this.statsDashboardService.chosenSiteNames = new Set<string>([params.data.RetailerSiteName]);
      }

      let menuItem: MenuItemNew | undefined = this.constantsService.getMenuItemFromUrl('/statsDashboard');
      if (menuItem) {
         this.constantsService.navigateByUrl(menuItem);
      } //, 'vehiclepricing'
   }

   workoutDomLayout() {
      const contentNewHeight: number = document.getElementsByClassName('content-new')[0].clientHeight;
      const rowCount: number = this.tableParams.sites.length;

      // rowCount * rowHeight = total rows in pixels
      // factor in headerHeight in px (In this tables case there are 2 headers)
      // check if overall rowHeight + headerHeight is greater than content-new height
      if (rowCount * this.rowHeight + (this.headerHeight * 2) < contentNewHeight) {
         this.domLayout = 'autoHeight';
      } else {
         this.domLayout = 'normal';
      }
   }

   private calcPercentage(value: number, AdCounts) {
      return ((value / (AdCounts.Under5Images + AdCounts.Under10Images + AdCounts.Under20Images + AdCounts.Over20Images)));
   }
}
