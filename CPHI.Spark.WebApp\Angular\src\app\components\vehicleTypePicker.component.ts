import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { selectableItem } from '../model/main.model';
import { ConstantsService } from '../services/constants.service';



@Component({
  selector: 'vehicleTypePicker',
  template:    `
    <!--Vehicle Type Selector -->
    <div ngbDropdown dropright class="d-inline-block">
        <button [ngClass]="buttonClass" class=" btn btn-primary" (click)="generateVehicleTypesList()"
          ngbDropdownToggle>{{vehicleTypeChosenLabel()}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <!-- ngFor of buttons -->
          <button *ngFor="let vehicleType of vehicleTypeTypes" (click)="toggleItem(vehicleType)"
            [ngClass]="{'active':vehicleType.isSelected}" ngbDropdownItem>{{vehicleType.translation}}</button>

          <!-- quick select all used -->
          <button class="quickSelect" *ngIf="constants.environment.vehicleTypePicker_showUsed" (click)="quickSelectAllUsed()" ngbDropdownItem>{{constants.translatedText.Used}}</button>
          <button class="quickSelect" *ngIf="constants.environment.vehicleTypePicker_showNew" (click)="quickSelectAllNew()" ngbDropdownItem>{{constants.translatedText.New}}</button>
          <button class="quickSelect" *ngIf="constants.environment.vehicleTypePicker_showAll" (click)="quickSelectAll()" ngbDropdownItem>{{constants.translatedText.All}}</button>
          <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle
              (click)="selectVehicleTypes()">OK</button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
          </div>

        </div>
      </div>
    
    `
  ,
  styles: [`
  
    

    `]
})
export class VehicleTypePickerComponent implements OnInit {
  @Input() vehicleTypeTypesFromParent: string[];
  @Input() buttonClass: string;
  @Output() updateVehicleTypes = new EventEmitter<string[]>();

  public vehicleTypeTypes: selectableItem[];

  
  constructor(
    public constants: ConstantsService,
    
  ) {




  }


  ngOnInit(): void {
  }


  vehicleTypeChosenLabel() {
    if (this.vehicleTypeTypesFromParent?.length == 0) {
      return this.constants.translatedText.NoVehicleType;
    } else if (this.vehicleTypeTypesFromParent?.length == 1) {
      return this.translateType(this.vehicleTypeTypesFromParent[0])
    } else {
      return this.constants.translatedText.VehicleType; 
    }
  }

  generateVehicleTypesList() {
    //recreate local list
    this.vehicleTypeTypes = [];
    this.constants.vehicleTypeTypes.forEach(type => {
      if (!this.constants.environment.vehicleTypePicker_hiddenVehicleTypes.includes(type)) {
        this.vehicleTypeTypes.push(
          { translation: this.translateType(type), label: type, isSelected: false }
        )
      }
    })
    //tag if it's selected
    this.vehicleTypeTypes.forEach(s => {
      if (this.vehicleTypeTypesFromParent.indexOf(s.label) > -1) {
        s.isSelected = true;
      }
    })
  }

  translateType(type: string){

    if(type == 'Used'){
      return this.constants.translatedText.Used;
    }

    if(type == 'New'){
      return this.constants.translatedText.New;
    }

    return type;
  }

  
  toggleItem(item: any) {
    item.isSelected = !item.isSelected;
  }

  selectVehicleTypes() {
    this.updateVehicleTypes.emit(this.vehicleTypeTypes.filter(e => e.isSelected).map(e => e.label));
  }

  
  
  
  quickSelectAllUsed() {
    this.vehicleTypeTypes.forEach(s => {
      if (['ExManagement', 'Tactical', 'ExDemo', 'CoreUsed', 'Demo'].indexOf(s.label) > -1) {
        s.isSelected = true;
      } else {
        s.isSelected = false;
      }
    })
  }
  quickSelectAllNew() {
    this.vehicleTypeTypes.forEach(s => {
      if (['New'].indexOf(s.label) > -1) {
        s.isSelected = true;
      } else {
        s.isSelected = false;
      }
    })
  }
  
  quickSelectAll() {
    //if all selected, select none else select all
    if(this.vehicleTypeTypes.filter(x=>x.isSelected).length == this.vehicleTypeTypes.length){
      this.vehicleTypeTypes.forEach(s=>s.isSelected = false)
    }else{
      this.vehicleTypeTypes.forEach(s => {
          s.isSelected = true;
      })
    }
  }

 


}


