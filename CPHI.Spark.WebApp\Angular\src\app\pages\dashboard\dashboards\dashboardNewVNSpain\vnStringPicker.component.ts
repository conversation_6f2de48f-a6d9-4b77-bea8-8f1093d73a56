import { Component, EventEmitter, Input, OnInit } from "@angular/core";
import { ConstantsService } from "src/app/services/constants.service";
import { VNTileParams } from "../../../../model/VNTileParams";
import { DashboardMeasure } from "../../../../model/DashboardMeasure";
import { ListItem } from "src/app/model/ListItem";


@Component({
    selector: 'vnStringPicker',
    template: `
    <div class="d-inline-block" ngbDropdown dropright [container]="'body'">
        <button class="btn btn-primary centreButton" ngbDropdownToggle (click)="saveBackupOfChoices()" >
            {{fieldNameTranslation}}
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <div class="unique-buttons">
                <button
                    *ngFor="let item of list"
                    [ngClass]="{ 'active': item.IsSelected }"
                    ngbDropdownItem
                    (click)="selectItem(item)"
                >
                    <span *ngIf="fieldName != 'OriginType' || item.Label == 'Stock'">
                            {{ item.Label }}
                    </span>

                    <span *ngIf="fieldName == 'OriginType' && item.Label == 'Book'">
                            {{ constants.translatedText.Book }}
                    </span>

                </button>
            </div>
            <div class="common-buttons">
                <button class="quickSelect" ngbDropdownItem (click)="selectAllItems()">
                    {{ constants.translatedText.All }}
                </button>
                <div class="spaceBetween">
                    <button
                        class="dropdownBottomButton"
                        ngbDropdownItem
                        ngbDropdownToggle
                        (click)="chooseOk()"
                    >
                        {{ constants.translatedText.OKUpper }}
                    </button>
                    <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="restoreBackupOfChoices()">
                        {{ constants.translatedText.Cancel }}
                    </button>
                </div>
            </div>
        </div>
    </div>
  `,
  styles: [
    `
        .unique-buttons {
            max-height: 60em;
            overflow-y: auto;       
        }
    `
  ]
})

export class VNStringPickerComponent implements OnInit {

    pickerItems: string[];
    backup: string[];
    @Input() public fieldName:string;
    @Input() public fieldNameTranslation:string;
    @Input() public pageParams: VNTileParams;
    choice: DashboardMeasure;
    list: ListItem[];
    subscription: any;

    constructor(
        public constants: ConstantsService,
    ) { }

    ngOnInit(): void {
        this.choice = this.getUserChoice()
        this.list = this.itemsList();

        this.subscription = this.pageParams.updateThisPicker.subscribe(res=>{
            this.list = this.itemsList();
        })
    }

    ngOnDestroy(){
        if(!!this.subscription){this.subscription.unsubscribe();}
    }


    getUserChoice(): DashboardMeasure {
       // console.log('calling getUserChoice')
        return this.pageParams.filterChoices.find(x => x.FieldName === this.fieldName);
    }
    
    
    itemsList() {
       // console.log('calling getItemsList')
        const isDateField = this.pageParams.filterChoices.find(x=>x.FieldName===this.fieldName).IsDate;
        let stringList = this.pageParams.parentMethods.provideItemsList(this.fieldName, isDateField)
        let results: ListItem[]=[];


        stringList.forEach(item => { 
            results.push({Label:item, IsSelected:this.isItemSelected(item)})
        });

        return results;        
    }
    
    isItemSelected(item: string) {
       // console.log('calling isItemSelected.  I am ',this.fieldName)
        return this.choice.ChosenValues.length === 0 || this.choice.ChosenValues.includes(item)
    }

    selectItem(item: ListItem) {
        if (this.choice.ChosenValues.length === 0) {
            //switch off all 
            this.list.map(x=>x.IsSelected = false)
            //add this item to the array
            this.choice.ChosenValues = [item.Label];
        } else if (item.IsSelected) {
            this.choice.ChosenValues = this.choice.ChosenValues.filter(x => x !== item.Label)
        } else {
            this.choice.ChosenValues.push(item.Label)
        }
        item.IsSelected = !item.IsSelected;
    }

    selectAllItems() {
        this.choice.ChosenValues = [];
        this.list.map(x=>x.IsSelected = true)
    }


    saveBackupOfChoices() {
        this.backup = [];
            this.choice.ChosenValues.forEach(item => {
            this.backup.push(item)
        })
    }

    restoreBackupOfChoices() {
        this.choice.ChosenValues = [];
        this.backup.forEach(item => {
            this.choice.ChosenValues.push(item);
        })
    }


    chooseOk(){
        this.pageParams.filterChoiceHasBeenMade.emit();
    }
  

  
}
