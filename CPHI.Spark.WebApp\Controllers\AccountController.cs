﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.WebApp.Service;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess;
using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.WebApp.Caches;
using Microsoft.Extensions.DependencyInjection;
using CPHI.Repository;
using Microsoft.EntityFrameworkCore;


namespace CPHI.Spark.WebApp.Controllers
{

    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AccountController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> userManager;
        private readonly IRefreshTokenService refreshTokenService;
        private readonly IEmailSenderService emailSender;
        private readonly IUserService userService;
        private readonly IConfiguration config;
        private readonly IAccessTokenService accessTokenService;
        private readonly IUserCache userCache;
        private readonly IServiceProvider serviceProvider;
        private readonly string configSectionName = "WebApp";
        private readonly string configURL = "URL";

        public AccountController(
            UserManager<ApplicationUser> userManager,
            IEmailSenderService emailSender,
            IRefreshTokenService refreshTokenService,
            IUserService userService,
            IConfiguration config,
            IAccessTokenService accessTokenService,
            IUserCache userCache,
            IServiceProvider serviceProvider
            )
        {
            this.userManager = userManager;
            this.emailSender = emailSender;
            this.refreshTokenService = refreshTokenService;
            this.userService = userService;
            this.config = config;
            this.accessTokenService = accessTokenService;
            this.userCache = userCache;
            this.serviceProvider = serviceProvider;
        }

        [HttpPost]
        [Route("Register")]
        [Authorize(Roles = "SysAdministrator")]
        //POST: /api/Account/Register
        public async Task<object> RegisterUser(ApplicationUserModel model)
        {
            var username = userService.GetUserName();
            var dealerGroupId = userService.GetIdFromAccessToken("DealerGroupId");

            await userService.SetDBContextConnectionString(username, dealerGroupId);


            var applicationUser = new ApplicationUser()
            {
                UserName = model.UserName,
                Email = model.Email,
                LinkedPersonId = model.LinkedPersonId,
                EmailConfirmed = true
            };

            try
            {
                var result = await userManager.CreateAsync(applicationUser, model.Password);
                await userCache.ReLoadUserSiteRoleCache();

                return Ok(result);
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        [HttpGet]
        [Route("UserDealerGroup")]
        [AllowAnonymous]
        //POST: /api/Account/UserDealerGroup
        public async Task<IActionResult> UserDealerGroup(string username)
        {
            var dealerGroupIds = await this.userService.GetUserDealerGroupByEmail(username);

            if (!dealerGroupIds.Any())
            {
                return Ok(new { error = "Incorrect username or password." });
            }

            return Ok(dealerGroupIds);
        }

        [HttpGet]
        [Route("SwitchDealerGroup")]
        public async Task<IActionResult> SwitchDealerGroup(int newDealerGroupId)
        {
            int userId = userService.GetUserId(); //gets from existing token
            int dgIdExisting = (int)(userService.GetUserDealerGroupName());
            string email = await userService.GetUserEmailFromAspNetUsers(userId,dgIdExisting);

            if (email != "<EMAIL>")
            {
                return Ok(new { error = "Incorrect username or password." });
            }
            
            string aspNetUserId = await this.userService.GetAspNetUserId(email, newDealerGroupId);
            if (aspNetUserId == null)
            {
                //could not find this username for this dealerGroupId
                return Ok(new { error = "Incorrect username or password." });
            }

            await userService.SetDBContextConnectionString(email, newDealerGroupId);

            var user = await userManager.FindByIdAsync(aspNetUserId);
            if (user == null)
            {
                return Ok(new { error = "Incorrect username or password." });
            }
            if (user.LockoutEnd != null && user.LockoutEnd >= DateTime.Today)
            {
                return Ok(new { error = "Incorrect username or password." });
            }



            string accessToken = await accessTokenService.GenerateAccessToken(user, newDealerGroupId);
            string refreshToken = refreshTokenService.GenerateRefreshToken(user, newDealerGroupId);
            return Ok(new { accessToken, refreshToken });
        }


        [HttpPost]
        [Route("Login")]
        [AllowAnonymous]
        //POST: /api/Account/Login
        public async Task<IActionResult> Login(LoginModel model)
        {
            string aspNetUserId = await this.userService.GetAspNetUserId(model.UserName, model.DealerGroupId);
            if (aspNetUserId == null)
            {
                //could not find this username for this dealerGroupId
                return Ok(new { error = "Incorrect username or password." });
            }

            await userService.SetDBContextConnectionString(model.UserName, model.DealerGroupId);

            var user = await userManager.FindByIdAsync(aspNetUserId);
            if (user == null)
            {
                return Ok(new { error = "Incorrect username or password." });
            }
            if (user.LockoutEnd != null && user.LockoutEnd >= DateTime.Today)
            {
                return Ok(new { error = "Incorrect username or password." });
            }

            if (user != null && await userManager.CheckPasswordAsync(user, model.Password))
            {
                string accessToken = await accessTokenService.GenerateAccessToken(user, model.DealerGroupId);
                string refreshToken = refreshTokenService.GenerateRefreshToken(user, model.DealerGroupId);
                return Ok(new { accessToken, refreshToken });
            }
            else
            {
                return Ok(new { error = "Incorrect username or password." });
            }

        }

        [HttpPost]
        [Route("Forgotpassword")]
        [AllowAnonymous]
        //POST: /api/Account/ForgotPassword
        public async Task<IActionResult> ForgotPassword(ForgotPasswordModel model)
        {
            //Look for username in the cache.
            var dealerGroupIds = await this.userService.GetUserDealerGroupByEmail(model.Email);
            if (!dealerGroupIds.Any())
            {
                return Ok();
            }

            var dealerGroupId = dealerGroupIds.First().Id;

            string userName = await this.userService.GetUserName(model.Email, dealerGroupId);
            string aspNetUserId = await this.userService.GetAspNetUserId(model.Email, dealerGroupId);
            if (userName == null)
            {
                return Ok();
            }
            await userService.SetDBContextConnectionString(userName, dealerGroupId);

            var user = await userManager.FindByIdAsync(aspNetUserId);
            if (user == null || !(await userManager.IsEmailConfirmedAsync(user)))
            {
                // Don't reveal that the user does not exist or is not confirmed
                //return View("ForgotPasswordConfirmation");
                return Ok(); //Verify
            }
            try
            {
                // Send an email with this link
                string token = await userManager.GeneratePasswordResetTokenAsync(user);
                string WebURL = config[$"{configSectionName}:{configURL}"];

                var callbackUrl = $"{WebURL}/resetpassword?token={token}&email={user.Email}&dealerGroupId={dealerGroupId}";
                await emailSender.SendEmailAsync(model.Email, "Reset Password", "Please reset your password by clicking <a href=\"" + callbackUrl.ToString() + "\">here</a>");
            }
            catch (Exception ex)
            {
                throw;
            }
            return Ok();
        }

        [HttpPost]
        [Route("ResetPassword")]
        [AllowAnonymous]
        // POST: /api/Account/ResetPassword
        public async Task<IActionResult> ResetPassword(ResetPasswordModel model)
        {
            await userService.SetDBContextConnectionString(model.UserName, model.DealerGroupId);
            string aspNetUserId = await this.userService.GetAspNetUserId(model.UserName, model.DealerGroupId);
            var user = await userManager.FindByIdAsync(aspNetUserId);
            if (user == null)
            {
                // Don't reveal that the user does not exist
                //return RedirectToAction("ResetPasswordConfirmation", "Account");
                return Ok(); //Verify
            }

            // Validate the token manually
            var isTokenValid = await userManager.VerifyUserTokenAsync(user,
                userManager.Options.Tokens.PasswordResetTokenProvider,
                "ResetPassword", model.Token);

            if (!isTokenValid)
            {
                return BadRequest("Invalid token.");
            }


            string passwordHash = userManager.PasswordHasher.HashPassword(user, model.Password);

            var resultCount = await ChangePasswordAcrossAllDealerGroups(user.Email, passwordHash);


            if (resultCount > 0)
            {
                return Ok();
            }
            else
            {
                return BadRequest("Invalid Token"); // Verify
            }
        }

        [HttpPost]
        [Route("ChangePassword")]
        //[AllowAnonymous]
        // POST: /api/Account/ChangePassword
        public async Task<IActionResult> ChangePassword(ChangePasswordModel model)
        {
            var username = userService.GetLoggedInUsersUsername();
            var dealerGroupId = userService.GetIdFromAccessToken("DealerGroupId");

            await userService.SetDBContextConnectionString(username, dealerGroupId);

            var user = await userManager.FindByEmailAsync(username);

            //Validate current password
            bool isPasswordValid = await userManager.CheckPasswordAsync(user, model.CurrentPassword);

            if (!isPasswordValid)
            {
                return BadRequest("Incorrect Password");
            }

            if (user == null)
            {
                // Don't reveal that the user does not exist
                //return RedirectToAction("ResetPasswordConfirmation", "Account");
                return Ok(); //Verify
            }

            //Updating this way first as this will validate the password criteria
            var result = await userManager.ChangePasswordAsync(user, model.CurrentPassword, model.Password); 
            if (result.Succeeded)
            {
                //Once successful, update password for all dealergroups
                string passwordHash = userManager.PasswordHasher.HashPassword(user, model.Password);
                var resultCount = await ChangePasswordAcrossAllDealerGroups(user.Email, passwordHash);

                if (resultCount > 0)
                    return Ok();
                else
                    return BadRequest("Invalid Token");

            }
            else
            {
                return BadRequest("Invalid Token"); // Verify
            }
        }

        [HttpPost]
        [Route("RefreshToken")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken()
        {
            var refreshTokenCookie = Request.Headers["refresh_token"];
            var newUserRefreshToken = await refreshTokenService.RenewRefreshTokenAsync(refreshTokenCookie);

            if (newUserRefreshToken == null)
            {
                return Ok(new { error = "Invalid token" });
            }

            var user = await userManager.FindByIdAsync(newUserRefreshToken.UserId);

            string accessToken = await accessTokenService.GenerateAccessToken(user, newUserRefreshToken.DealerGroupId);


            return Ok(new { accessToken, refreshToken = newUserRefreshToken.RefreshToken });

        }

        private void setTokenCookie(RefreshToken refreshToken)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Expires = refreshToken.Expiration,
                Path = "/api/Account/RefreshToken"
            };
            Response.Cookies.Append("refreshToken", refreshToken.Token, cookieOptions);
        }

        private async Task<int> ChangePasswordAcrossAllDealerGroups(string email, string passwordHash)
        {

            //Update pwd for all dealergroups
            var dealerGroups = await this.userService.GetUserDealerGroupByEmail(email);
            var dealerGroupIds = dealerGroups.Select(x => x.Id).ToList();
            var updatePasswordSQL = $"UPDATE AspNetUsers Set PasswordHash = '{passwordHash}' WHERE Email = '{email}'";
            var db = serviceProvider.GetRequiredService<CPHIDbContext>();

            int resultCount = 0;
            //RRG
            if (dealerGroupIds.Contains((int)Model.DealerGroupName.RRGUK))
            {
                await userService.SetDBContextConnectionString(email, (int)Model.DealerGroupName.RRGUK);
                resultCount += await db.Database.ExecuteSqlRawAsync(updatePasswordSQL);
            }

            //RRG Spain 
            if (dealerGroupIds.Contains((int)Model.DealerGroupName.RRGSpain))
            {
                await userService.SetDBContextConnectionString(email, (int)Model.DealerGroupName.RRGSpain);
                resultCount += await db.Database.ExecuteSqlRawAsync(updatePasswordSQL);
            }

            //Vindis
            if (dealerGroupIds.Contains((int)Model.DealerGroupName.Vindis))
            {
                await userService.SetDBContextConnectionString(email, (int)Model.DealerGroupName.Vindis);
                resultCount += await db.Database.ExecuteSqlRawAsync(updatePasswordSQL);
            }


            //Autoprice
            var autoPriecDealerGroupIds = dealerGroupIds.Where(id => id != (int)Model.DealerGroupName.RRGUK && id != (int)Model.DealerGroupName.RRGSpain && id != (int)Model.DealerGroupName.Vindis).ToList();
            if (autoPriecDealerGroupIds.Any())
            {
                await userService.SetDBContextConnectionString(email, autoPriecDealerGroupIds.First()); //any autoprice dealergroup
                resultCount += await db.Database.ExecuteSqlRawAsync(updatePasswordSQL);
            }

            return resultCount;

        }

    }
}
