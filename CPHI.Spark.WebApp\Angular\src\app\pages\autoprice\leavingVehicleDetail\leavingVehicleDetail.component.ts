import { Component, OnInit } from "@angular/core";
import {
   ApplyColumnStateParams,
   FirstDataRenderedEvent,
   GridReadyEvent,
   RowDoubleClickedEvent,
   SideBarDef,
} from "ag-grid-community";
import { Subscription } from "rxjs";
import { CustomHeaderNew } from "src/app/components/customHeader/customHeader.component";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { AutopriceRendererService } from "src/app/services/autopriceRenderer.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExportService";
import { LeavingVehicleDetailService } from "./leavingVehicleDetail.service";
import { AutotraderService } from "src/app/services/autotrader.service";
import { CustomHeaderService } from "src/app/components/customHeader/customHeader.service";
import { LeavingVehicleItem } from "src/app/model/LeavingVehicleItem";
import { AutoPriceInsightsModalService } from "src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service";
import { AutoPriceInsightsModalComponent } from "src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { TableLayoutManagementService } from "src/app/components/tableLayoutManagement/tableLayoutManagement.service";
import { CPHAutoPriceColDef } from "src/app/model/CPHColDef";
import { CustomHeaderAdDetail } from "src/app/components/customHeaderAdDetail/customHeaderAdDetail.component";
import { SelectionsService } from "src/app/services/selections.service";
import { GlobalParamsService } from "src/app/services/globalParams.service";
import { GlobalParamKey } from "src/app/model/GlobalParam";

@Component({
   selector: "app-leavingVehicleDetail",
   templateUrl: "./leavingVehicleDetail.component.html",
   styleUrls: ["./leavingVehicleDetail.component.scss"],
})
export class LeavingVehicleDetailComponent implements OnInit {
   gridOptions: GridOptionsCph;
   newDataSubscription: Subscription;
   months: Date[];

   components: { [p: string]: any } = {
      agColumnHeader: CustomHeaderAdDetail,
   };

   sideBar: SideBarDef | string | string[] | boolean | null = {
      toolPanels: [
         {
            id: "filters",
            labelDefault: "Filters",
            labelKey: "filters",
            iconKey: "filter",
            toolPanel: "agFiltersToolPanel",
            minWidth: 100,
            width: 200,
            maxWidth: 200,
         },
         {
            id: "columns",
            labelDefault: "Columns",
            labelKey: "columns",
            iconKey: "columns",
            toolPanel: "agColumnsToolPanel",
            minWidth: 100,
            width: 200,
            maxWidth: 200,
            toolPanelParams: {
               suppressPivots: true,
               suppressPivotMode: true,
            },
         },
      ],
      position: "left",
      defaultToolPanel: "",
   };

   constructor(
      public service: LeavingVehicleDetailService,
      public constants: ConstantsService,
      public gridHelpersService: AGGridMethodsService,
      public columnTypesService: ColumnTypesService,
      public excelExportService: ExcelExportService,
      private autopriceRendererService: AutopriceRendererService,
      private customHeader: CustomHeaderService,
      private autoPriceInsightsModalService: AutoPriceInsightsModalService,
      private modalService: NgbModal,
      private tableLayoutManagementService: TableLayoutManagementService,
      private selectionsService: SelectionsService,
      public globalParmsService: GlobalParamsService
   ) {}

   ngOnInit(): void {
      this.service.initParams();
      this.service.getData();
      this.initialiseGrid();

      this.newDataSubscription = this.service.newDataEmitter.subscribe(() => {
         if (this.service.tableLayoutManagement.gridApi) {
            this.service.tableLayoutManagement.gridApi.setRowData(
               this.service.rowData
            );
            if (!this.service.tableLayoutManagement.lastTableState) {
               this.service.tableLayoutManagement.gridApi.setColumnDefs(
                  this.provideColDefs()
               );
            }
         }
      });

      // No need to manually set disabled state as it's handled by the template

      this.service.searchTerm.valueChanges.subscribe((value) => {
         this.service.tableLayoutManagement.gridApi.setQuickFilter(
            this.service.searchTerm.value
         );
      });

      this.tableLayoutManagementService.defaultFilterState = null;
      this.tableLayoutManagementService.parent =
         this.service.tableLayoutManagement;
   }

   ngOnDestroy(): void {
      if (this.newDataSubscription) {
         this.newDataSubscription.unsubscribe();
      }
      if (this.service.tableLayoutManagement?.gridApi) {
         this.service.tableLayoutManagement.gridApi = null;
      }
      if (this.service.tableLayoutManagement?.gridColumnApi) {
         this.service.tableLayoutManagement.gridColumnApi = null;
      }
   }


   get show30DaysSlider():boolean{
      return !!this.globalParmsService.getGlobalParam(GlobalParamKey.show30DayLeavingSlider)
   }

   onColumnRowGroupChanged() {
      // Get the current row group columns
      const rowGroupColumns =
         this.service.tableLayoutManagement.gridColumnApi.getRowGroupColumns();

      if (rowGroupColumns.length > 0) {
         // Retrieve the current column state
         let currentColumnState =
            this.service.tableLayoutManagement.gridColumnApi.getColumnState();

         // Find the maximum sort index
         let maxSortIndex = currentColumnState.reduce(
            (max, column) =>
               column.sortIndex !== null && column.sortIndex > max
                  ? column.sortIndex
                  : max,
            0
         );

         rowGroupColumns.forEach((rowGroupColumn) => {
            const firstRowGroupColumn = rowGroupColumn;

            // Increment the maxSortIndex for the next column
            maxSortIndex++;

            const sortModel: ApplyColumnStateParams = {
               state: [
                  {
                     colId: firstRowGroupColumn.getColId(),
                     sort: "asc", // or 'desc' for descending
                     sortIndex: maxSortIndex, // Set the next available sort index
                  },
               ],
            };

            this.service.tableLayoutManagement.gridColumnApi.applyColumnState(
               sortModel
            );
         });
      }
   }

   initialiseGrid() {
      this.gridOptions = {
         getContextMenuItems: (params) =>
            this.gridHelpersService.getContextMenuItems(params),
         getMainMenuItems: (params) =>
            this.customHeader.getMainMenuItems(params),
         defaultColDef: {
            resizable: true,
            sortable: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight(),
            },
            // autoHeight: true,
            floatingFilter: true,
            autoHeaderHeight: true,
            headerComponentParams: { showPinAndRemoveOptions: false },
            floatingFilterComponentParams: { suppressFilterButton: true },
         },
         sideBar: this.sideBar,
         autoGroupColumnDef: {
            sortable: true,
            valueGetter: (params) => {
               return "";
            },
         },
         context: { thisComponent: this },
         rowGroupPanelShow: "always",
         autoSizePadding: 0,
         onRowDoubleClicked: (params) => this.onRowDoubleClicked(params),
         onColumnRowGroupChanged: this.onColumnRowGroupChanged.bind(this),
         columnTypes: { ...this.columnTypesService.provideColTypes([]) },
         columnDefs: this.provideColDefs(),
         onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
         animateRows: false,
         suppressColumnMoveAnimation: true,
         getRowHeight: (params) => {
            if (params.node.rowPinned) {
               return this.gridHelpersService.getRowPinnedHeight();
            } else {
               return this.gridHelpersService.getStandardHeight();
            }
         },
         floatingFiltersHeight:
            this.gridHelpersService.getFloatingFilterHeight(),
         onFirstDataRendered: (params) => {
            this.onFirstDataRendered();
         },
         //groupUseEntireRow: true
      };
   }

   onRowDoubleClicked(params: RowDoubleClickedEvent) {
      const row: LeavingVehicleItem = params.data;
      this.openModal(row);
   }

   private openModal(row: LeavingVehicleItem) {
      if (row.AdvertId == 0) {
         return;
      }

      let allAdIds: number[] = [];
      this.service.tableLayoutManagement.gridApi.forEachNodeAfterFilterAndSort(
         (node) => {
            if (node.data && !node.isRowPinned()) {
               let nodeRow: LeavingVehicleItem = node.data;
               allAdIds.push(nodeRow.AdvertId);
            }
         }
      );

      this.autoPriceInsightsModalService.initialise(row.AdvertId, allAdIds);

      const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, {
         keyboard: true,
         size: "lg",
      });
      modalRef.result.then((result) => {});
   }

   private provideColDefs(): CPHAutoPriceColDef[] {
      // Check if the default retailer site has TrackLeavingVehicles enabled
      const showTrackLeavingVehiclesColumns = this.selectionsService.userRetailerSite?.TrackLeavingVehicles === true;

      const colDefs = [
         {
            headerName: "Division",
            colId: "Region",
            field: "Region",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Site",
            colId: "RetailerSiteName",
            field: "RetailerSiteName",
            type: "labelSetFilter",
            width: 30,
            columnSection: "Leaving Vehicles",
         },

         {
            headerName: "Reg",
            colId: "VehicleReg",
            field: "VehicleReg",
            minWidth:90,
            type: "special",
            cellRenderer: (params) =>
               this.autopriceRendererService.regPlateRenderer(params, {
                  extraParams: { noMargin: true },
               }),
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Reg Year",
            colId: "RegYear",
            field: "RegYear",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Make",
            colId: "Make",
            field: "Make",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Model",
            colId: "Model",
            field: "Model",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Derivative",
            colId: "Derivative",
            field: "Derivative",
            type: "label",
            width: 100,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Mileage",
            colId: "Mileage",
            field: "Mileage",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Mileage Band",
            colId: "MileageBand",
            field: "MileageBand",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForMileageBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            minWidth: 80,
         },
         {
            headerName: "Body Type",
            colId: "BodyType",
            field: "BodyType",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Fuel Type",
            colId: "FuelType",
            field: "FuelType",
            type: "labelSetFilter",
            // cellRenderer: (params) =>
            //    this.autopriceRendererService.addEnergyTypeIcon(params),
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Age band",
            colId: "AgeBand",
            field: "AgeBand",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "When Listed",
            colId: "ListedDate",
            field: "ListedDate",
            valueGetter: (params) =>
               !params.data ? null : this.getMonthName(params.data?.ListedDate),
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
            comparator: (valueA, valueB, nodeA, nodeB) => AutotraderService.getSortOrderWhenListed(nodeA, nodeB),
         },
         {
            headerName: "When Listed Date",
            colId: "ListedDate",
            field: "ListedDate",
            type: "date",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "When Removed",
            colId: "RemovedDate",
            field: "RemovedDate",
            valueGetter: (params) =>
               !params.data
                  ? null
                  : this.getMonthName(params.data?.RemovedDate),
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
            comparator: (valueA, valueB, nodeA, nodeB) => AutotraderService.getSortOrderRemovedDate(nodeA, nodeB),
         },
         {
            headerName: "When Removed Date",
            colId: "RemovedDate",
            field: "RemovedDate",

            type: "date",
            width: 10,
            columnSection: "Leaving Vehicles",
         },

         {
            headerName: "Days Listed",
            colId: "DaysListed",
            field: "DaysListed",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Days Listed Band",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForDaysBand()
               ),
            colId: "DaysListedBand",
            field: "DaysListedBand",
            type: "labelSetFilter",
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            width: 10,
         },
         {
            headerName: "Days On Strategy",
            colId: "DaysOnStrategy",
            field: "DaysOnStrategy",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Days Opted Out",
            colId: "DaysOptedOut",
            field: "DaysOptedOut",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Pct Days Opted Out",
            colId: "PercentDaysOptedOut",
            field: "PercentDaysOptedOut",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Opted Out Pct Band",
            colId: "OptedOutPctBand",
            field: "OptedOutPctBand",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Was On Strategy",
            colId: "IsOnStrategy",
            field: "IsOnStrategy",
            type: "boolean",
            width: 10,
            columnSection: "Leaving Vehicles",
         },

         {
            headerName: "First Price",
            colId: "FirstPrice",
            field: "FirstPrice",
            type: "currency",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "First Price Position",
            colId: "FirstPP",
            field: "FirstPP",
            type: "percent1dp",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "First Price Position Band",
            colId: "FirstPPBand",
            field: "FirstPPBand",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPPBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            minWidth: 100,
         },
         {
            headerName: "Last Price",
            colId: "LastPrice",
            field: "LastPrice",
            type: "currency",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Last Valuation",
            colId: "LastValuation",
            field: "LastValuation",
            type: "currency",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Last Price Band",
            colId: "LastPriceBand",
            field: "LastPriceBand",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForValueBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            minWidth: 100,
         },
         {
            headerName: "Last Price Position",
            colId: "LastPP",
            field: "LastPP",
            type: "percent1dp",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Last Price Position Band",
            colId: "LastPPBand",
            field: "LastPPBand",
            type: "label",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPPBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            minWidth: 100,
         },
         {
            headerName: "Price Indicator",
            colId: "LastPriceIndicator",
            field: "LastPriceIndicator",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForPriceIndicator()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderPriceIndicatorRenderer(
                  params
               ),
            minWidth: 100,
         },

         {
            headerName: "Retail Rating",
            colId: "LastRetailRating",
            field: "LastRetailRating",
            type: "number",
            minWidth: 50,
            // cellRenderer: (params) =>
            //    this.autopriceRendererService.autoTraderRetailRatingRenderer({
            //       params: { RetailRating: params.value, InTableCell: true },
            //    }),
            columnSection: "Leaving Vehicles",
         },
         {
            headerName: "Retail Rating Band",
            colId: "RetailRatingBand",
            field: "RetailRatingBand",
            type: "labelSetFilter",
            columnSection: "Leaving Vehicles",
            comparator: (valA, valB) =>
               this.customSort(
                  valA,
                  valB,
                  AutotraderService.getSortOrderForRetailRatingBand()
               ),
            cellRenderer: (params) =>
               this.autopriceRendererService.autoTraderLozengeRenderer(params),
            width: 10,
         },

         // Just for filters
         //{ headerName: 'Achieved Sale Type', colId: 'AchievedSaleType', field: 'AchievedSaleType', type: 'labelSetFilter', width: 10 ,columnSection: 'Leaving Vehicles'},
         {
            headerName: "Transmission Type",
            colId: "TransmissionType",
            field: "TransmissionType",
            type: "labelSetFilter",
            width: 10,
            columnSection: "Leaving Vehicles",
         },

         {
            headerName: "Initial days to sell estimate",
            colId: "FirstRetailDaysToSell",
            field: "FirstRetailDaysToSell",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },

         // Track Sold Vehicles columns - only visible if TrackLeavingVehicles is enabled
         {
            headerName: "Is Sold",
            colId: "RC_IsSold",
            field: "RC_IsSold",
            type: "boolean",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Seller Name",
            colId: "RC_SellerName",
            field: "RC_SellerName",
            type: "label",
            width: 20,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Days Listed",
            colId: "RC_DaysListed",
            field: "RC_DaysListed",
            type: "number",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Valuation",
            colId: "RC_Valuation",
            field: "RC_Valuation",
            type: "currency",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Price",
            colId: "RC_Price",
            field: "RC_Price",
            type: "currency",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer PP%",
            colId: "RC_PPPct",
            field: "RC_PPPct",
            type: "percent1dp",
            width: 10,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Retailer Sold Date",
            colId: "RC_SoldDate",
            field: "RC_SoldDate",
            type: "date",
            width: 15,
            columnSection: "Track Sold Vehicles",
            hide: !showTrackLeavingVehiclesColumns,
         },
         {
            headerName: "Days to sell vs initial estimate",
            colId: "DaysListedVsFirstRetailDaysToSell",
            field: "DaysListedVsFirstRetailDaysToSell",
            type: "number",
            width: 10,
            columnSection: "Leaving Vehicles",
         },
      ];

      this.gridHelpersService.workoutColWidths(
         this.service.rowData,
         colDefs,
         10,
         6
      );

      //colDefs.find(x=>x.colId=="VehicleReg").width=90;

      return colDefs;
   }

   onGridReady(event: GridReadyEvent) {
      this.service.tableLayoutManagement.gridApi = event.api;
      this.service.tableLayoutManagement.gridColumnApi = event.columnApi;

      this.service.tableLayoutManagement.originalColDefs =
         this.provideColDefs();
   }

   onFirstDataRendered() {
      if (this.service.tableLayoutManagement.loadedTableState) {
         this.service.tableLayoutManagement.gridColumnApi.applyColumnState({
            state: this.service.tableLayoutManagement.loadedTableState,
         });
         this.service.tableLayoutManagement.gridApi.setFilterModel(
            this.service.tableLayoutManagement.filterModel
         );
      }

      if (this.service.tableLayoutManagement.filterModel) {
         this.service.tableLayoutManagement.gridApi.setFilterModel(
            this.service.tableLayoutManagement.filterModel
         );
         this.service.tableLayoutManagement.gridApi.onFilterChanged();
      }

      if (this.service.externalFilterModel) {
         this.service.tableLayoutManagement.gridApi.setFilterModel(
            this.service.externalFilterModel
         );
         this.service.tableLayoutManagement.gridApi.onFilterChanged();
      }
   }

   clearSearchTerm() {
      this.service.searchTerm.setValue("");
      if (this.service.tableLayoutManagement.gridApi)
         this.service.tableLayoutManagement.gridApi.setQuickFilter(
            this.service.searchTerm.value
         );
   }

   excelExport() {
      let tableModel: any =
         this.service.tableLayoutManagement.gridApi.getModel();

      // Set these two columns to display as date in Excel
      const listedDate = tableModel.columnModel.displayedColumns.findIndex(
         (x) => x.colId === "ListedDate"
      );
      const removedDate = tableModel.columnModel.displayedColumns.findIndex(
         (x) => x.colId === "RemovedDate"
      );

      tableModel.columnModel.displayedColumns[listedDate].colDef.type = "date";
      tableModel.columnModel.displayedColumns[removedDate].colDef.type = "date";

      this.excelExportService.createSheetObject(
         tableModel,
         "Leaving Vehicle Analysis",
         1,
         1
      );
   }

   formatDateForInput(date: Date): string {
      // Format date as YYYY-MM-DD for input[type="date"]
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
   }

   onDateChange(event: Event, isStartDate: boolean) {
      // If rolling 30 days is enabled, disable it when manually selecting dates
      if (this.service.isRolling30Days) {
         this.service.isRolling30Days = false;
      }

      const dateValue = (event.target as HTMLInputElement).value;
      const newDate = new Date(dateValue);

      if (isStartDate) {
         this.service.startDate = newDate;
         // If new start date is greater than end date, update end date
         if (this.service.startDate.getTime() > this.service.endDate.getTime()) {
            this.service.endDate = newDate;
         }
      } else {
         this.service.endDate = newDate;
         // If new end date is less than start date, update start date
         if (this.service.endDate.getTime() < this.service.startDate.getTime()) {
            this.service.startDate = newDate;
         }
      }
      this.service.getData();
   }

   toggleRolling30Days() {
      this.service.isRolling30Days = !this.service.isRolling30Days;
      this.service.getData();
   }

   getMonthName(date: Date): string {
      date = new Date(date);
      return date.toLocaleDateString(this.constants.translatedText.LocaleCode, {
         month: "short",
         year: "2-digit",
      });
   }

   customSort(valueA: string, valueB: string, rangesOrder: string[]): number {
      return AutotraderService.customSort(valueA, valueB, rangesOrder);
   }
}
