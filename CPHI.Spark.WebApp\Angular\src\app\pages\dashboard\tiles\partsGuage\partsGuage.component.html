<div class="dashboard-tile-inner">
  <!-- Header -->
  <div class="tileHeader withLink" >
    <div *ngIf="dataSource" class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
    <div (click)="navigateToMainPage()" class="clickable headerWords">
      <h4>
        {{constants.translatedText.Parts}} 
        {{constants.translatedText.Sales}} 
        {{constants.translatedText.For}}
        <span *ngIf="tileType==='Month'"> {{constants.translatedText.TheMonth}} </span>
        <span *ngIf="tileType==='Week'"> {{constants.translatedText.TheWeek}} </span>
        <span *ngIf="tileType==='Yesterday'"> {{constants.translatedText.Yesterday}} </span>
      </h4>
    </div>
  </div>

  <div class="d-flex flex-column justify-content-center flex-grow-1">
    <!-- The chart itself -->
    <div id="guageHolder" *ngIf="showChart">
      <guageChart [daysElapsed]="data.DaysElapsed" [daysTotal]="data.DaysTotal" [measureDone]="data.DoneInTimePeriod"
        [measureTotal]="target()"></guageChart>
    </div>


    <!-- Done in x Days -->
    <div class="headlineFigure">
      {{constants.translatedText.Done}}&nbsp;<span class="bold"
        [ngClass]="{ 'badFont' : !onTarget() , 'goodFont' : onTarget() }">{{data.DoneInTimePeriod|cph:'currency':0}}</span>
        &nbsp;{{ constants.translatedText.InLower }} {{data.DaysElapsed|cph:'number':1}} {{
      constants.translatedText.DaysLower }}.
    </div>


    <!-- £3k behind target -->
    <ng-container *ngIf="includeCompareCommentary || tileType==='Month'">
    <div *ngIf="data.Vs < 0" class="headlineFigure">
      {{data.Vs * -1 |cph:'currency':0}}
      {{ constants.translatedText.BehindTargetOf }}
      {{data.TargetToDate|cph:'currency':0}}
    </div>

    <div *ngIf="data.Vs >= 0" class="headlineFigure">
      {{data.Vs |cph:'currency':0}}
      {{ constants.translatedText.AheadOfTarget }}
      {{data.TargetToDate|cph:'currency':0}}
    </div>
    </ng-container>


    <!-- Full month target £xxxx -->
    <div class="headlineFigure bold">

      <div *ngIf="tileType==='Month'"> 
        {{constants.translatedText.FullMonthTarget}}
        {{data.ThisMonthTarget|cph:'currency':0}}.
      </div>


      <div *ngIf="tileType==='Week'"> 
        {{constants.translatedText.ThisWeek}}  {{constants.translatedText.Target}} 
        {{data.TargetToDate|cph:'currency':0}}.
      </div>
      
      
      <div *ngIf="tileType==='Yesterday'"> 
        {{constants.translatedText.Yesterday}}  {{constants.translatedText.Target}} 
        {{data.TargetToDate|cph:'currency':0}}.
      </div>
    </div>
  </div>
</div>