CREATE OR ALTER PROCEDURE [autoprice].[GET_VehicleAdvertWithRatings]  
  
(  
  @effectiveDate Date = null,  
  @dealerGroupId INT  ,
  @advertIds varchar(500) =null
)  
    
AS    
BEGIN   
  
  
 IF @effectiveDate IS NULL   
 BEGIN SET @effectiveDate = CONVERT(date,getDate())  
 END  
 
 SET NOCOUNT ON;  
  
 DECLARE @today DATE = CAST(GETDATE() AS DATE);  
 DECLARE @effectiveDatePlus1 Date = DATEADD(DAY, 1, @effectiveDate);

 CREATE TABLE #AdIds (Id int);
IF @advertIds IS NOT NULL AND LEN(@advertIds) > 0
    BEGIN
        INSERT INTO #AdIds (Id)
        SELECT Value AS Id FROM STRING_SPLIT(@advertIds, ',');
    END
 
  
 ----------------------------------------------  
 --Sub table for today's adIds  
 ----------------------------------------------  
 SELECT  
 DISTINCT snaps.VehicleAdvert_Id as AdId  
 INTO #todayAdIds  
 FROM autoprice.VehicleAdvertSnapshots snaps   
 INNER JOIN autoprice.VehicleAdverts ads on ads.Id = snaps.VehicleAdvert_Id  
 INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id AND rs.DealerGroup_Id = @dealerGroupId  
 LEFT JOIN #adIds ai on ai.Id = ads.Id
 WHERE 
 snaps.DealerGroup_Id = @dealerGroupId
 AND snaps.SnapshotDate >= @effectiveDate 
 AND snaps.SnapshotDate < @effectiveDatePlus1
 AND rs.IsActive = 1
 --AND snaps.IsTodayLatestSnapshot = 1
 AND snaps.DealerGroup_Id = @dealerGroupId
 AND (ai.Id IS NOT NULL OR @advertIds IS NULL)
 ;  
  
 --------------------------------------------  
 --Sub table for most recent manual change per ad  
 --------------------------------------------  
 WITH manualsRowNum as  
 (  
  SELECT  
  TOP 10000  
  ads.Id as AdId,  
  prices.CreatedDate,  
  (prices.NowPrice - COALESCE(prices.WasPrice, 0)) as ChangeValue,  
  p.Name as ChangedBy,  
  ROW_NUMBER() OVER (PARTITION BY ads.Id ORDER BY prices.CreatedDate desc) AS RowNumber     
  FROM autoprice.PriceChangeManualItems prices   
  LEFT JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = prices.VehicleAdvertSnapshot_Id  AND snaps.DealerGroup_Id = @dealerGroupId
  INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id  
  INNER JOIN #todayAdIds tai on tai.AdId = ads.id  
  LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and CONVERT(date,opts.ActualEndDate) >= @effectiveDate  
  INNER JOIN People p on p.Id = prices.Person_Id  
  WHERE opts.Id IS NOT NULL  
  ORDER BY ads.Id  
 )  
 SELECT  
 AdId,  
 createdDate,  
 ChangeValue,  
 ChangedBy  
 INTO #lastManualChangePerAd  
 FROM manualsRowNum  
 WHERE RowNumber = 1;  
  
  
 --------------------------------------------  
 --Sub table for today auto price changes  
 --------------------------------------------  
 SELECT    
 VehicleAdvertSnapshot_Id,  
 NowPrice,  
 DaysToSell,  
 CASE  
  WHEN   
   (autos.NowPrice - COALESCE(autos.WasPrice, 0)) >= rs.MinimumAutoPriceIncrease AND  
   (  
    COALESCE(autos.WasPrice, 0) = 0 OR  
    (  
     COALESCE(autos.WasPrice, 0) <> 0 AND  
     (autos.NowPrice / NULLIF(COALESCE(autos.WasPrice, 0),0)) -1 >= rs.MinimumAutoPricePercentIncrease  
    )  
   )  
   THEN 0  
  WHEN   
   (autos.NowPrice - COALESCE(autos.WasPrice, 0)) <= rs.MinimumAutoPriceDecrease AND   
   (  
    COALESCE(autos.WasPrice, 0) = 0 OR  
    (  
     COALESCE(autos.WasPrice, 0) <> 0 AND  
     (autos.NowPrice / NULLIF(COALESCE(autos.WasPrice, 0),0)) -1 <= rs.MinimumAutoPricePercentDecrease  
    )  
   )  
   THEN 0  
  ELSE 1  
 END as IsSmallPriceChange,  
 PriceIndicator,  
 ROW_NUMBER() OVER (PARTITION BY VehicleAdvertSnapshot_Id ORDER BY CreatedDate desc) AS RowNumber     
 INTO #TodayChanges  
 FROM autoprice.PriceChangeAutoItems autos  
 INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = autos.VehicleAdvertSnapshot_Id  AND snaps.DealerGroup_Id = @dealerGroupId
 INNER JOIN #todayAdIds tai on tai.AdId = snaps.VehicleAdvert_Id  
 INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id  
 INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id  
 WHERE   
 MONTH(CreatedDate) = MONTH(@today) AND   
 YEAR(CreatedDate) = YEAR(@today) AND   
 DAY(CreatedDate) = DAY(@today) AND 
 rs.IsActive = 1
  
 ----------------------------------------------  
 --Sub table for most recent opt-out per ad  
 ----------------------------------------------  
 SELECT  
 opts.VehicleAdvert_Id,  
 opts.Person_Id,  
 opts.ActualEndDate,  
 opts.CreatedDate,  
 ROW_NUMBER() OVER (PARTITION BY opts.VehicleAdvert_Id ORDER BY opts.CreatedDate desc) AS RowNumber    
 INTO #mostRecentOptOutForAd  
 FROM autoprice.VehicleOptOuts opts  
 INNER JOIN #todayAdIds tai on tai.AdId =opts.VehicleAdvert_Id  
 WHERE CONVERT(date,opts.ActualEndDate) > @effectiveDate  
 AND CONVERT(date,opts.CreatedDate) < CONVERT(date,@effectiveDate)  
  
  
 ----------------------------------------------  
 --Sub table for latest snapshotIds  
 ----------------------------------------------  
 SELECT MAX(r.Id) as Id  
 INTO #veryLatestSnapshotIds  
 FROM autoprice.VehicleAdvertSnapshots r  
 INNER JOIN #todayAdIds tai on tai.AdId = r.VehicleAdvert_Id  
 WHERE CONVERT(date,r.SnapshotDate) = @effectiveDate  
 AND r.DealerGroup_Id = @dealerGroupId
 GROUP BY r.VehicleAdvert_Id;  
  
  
 ----------------------------------------------  
 -- Sub table for latest comments  
 ----------------------------------------------  
 SELECT  
 comm.Text as LastCommentText,  
 p.Name as LastCommentName,  
 comm.VehicleAdvert_Id as AdvertId,  
 ROW_NUMBER() OVER (PARTITION BY tai.AdId ORDER BY comm.Date desc ) AS RowNumber   
 INTO #latestComments  
 FROM autoprice.VehicleAdvertComments comm  
 INNER JOIN #todayAdIds tai on tai.AdId = comm.VehicleAdvert_Id  
 INNER JOIN people p on p.Id = comm.Person_Id  
 WHERE comm.IsRemoved = 0  
  
  
  
   
   
 --------------------------------------------  
 -- TOTAL CHANGES  
 --------------------------------------------  
 SELECT  
 snaps.VehicleAdvert_Id,  
 COUNT(manuals.Id) as Count,  
 SUM(manuals.NowPrice - COALESCE(manuals.WasPrice, 0)) as TotalChangeValue  
 INTO #totalChanges  
 FROM autoprice.PriceChangeManualItems manuals  
 INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = manuals.VehicleAdvertSnapshot_Id  AND snaps.DealerGroup_Id = @dealerGroupId
 INNER JOIN #todayAdIds tai on tai.AdId = snaps.VehicleAdvert_Id  
 GROUP BY snaps.VehicleAdvert_Id;  
  
  
  
 --------------------------------------------  
 -- Portal Options  
 --------------------------------------------  
 SELECT  
 tai.AdId,  
 STRING_AGG(CAST(po.OptionName AS NVARCHAR(MAX)), '||') as PortalOptions  
 INTO #portalOptions  
 FROM autoprice.VehicleAdvertPortalOptions vpo   
 INNER JOIN #todayAdIds tai on tai.AdId = vpo.VehicleAdvert_Id  
 LEFT JOIN autoprice.PortalOptions po on po.Id = vpo.PortalOption_Id  
 GROUP BY tai.AdId;


WITH RankedBranchStockItems AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY Registration ORDER BY CreatedDate DESC) AS RowNum
    FROM BcaBranchStockItems
    WHERE CreatedDate > DATEADD(MONTH, -6, GETDATE())
      AND DealerGroupId = @dealerGroupId
)
SELECT
    *
INTO #bcaLastSixM
FROM RankedBranchStockItems
WHERE RowNum = 1

--Create a non-clustered index on the temporary tables
CREATE NONCLUSTERED INDEX IX_portalOptions_AdId ON #portalOptions (AdId) INCLUDE (PortalOptions);
CREATE NONCLUSTERED INDEX IX_lastManualChangePerAd_AdId ON #lastManualChangePerAd (AdId);
CREATE NONCLUSTERED INDEX IX_veryLatestSnaps_Id ON #veryLatestSnapshotIds (Id);
CREATE NONCLUSTERED INDEX IX_todayAdIds_AdId ON #todayAdIds (AdId);
  
 --------------------------------------------  
 -- FINAL RESULT  
 --------------------------------------------  
  WITH filteredSnaps as (
	SELECT snaps.*
	FROM autoprice.VehicleAdvertSnapshots snaps
	INNER JOIN #veryLatestSnapshotIds latestSnaps on latestSnaps.Id = snaps.Id  
    WHERE snaps.DealerGroup_Id = @dealerGroupId
	--WHERE SnapshotDate >= @effectiveDate AND SnapshotDate < @effectiveDatePlus1
 )
 SELECT   
 ads.Id as AdId,  
 snaps.Id as SnapshotId,  
 rs.RetailerId,  
 s.Id as SiteId,  
 rs.Name as RetailerSiteName,  
 rs.RetailerId as RetailerSiteRetailerId,  
 rs.Makes as SiteBrand,  
 regi.Description as RegionName,  
 ads.RetailerSite_Id as RetailerSiteId,  
 rs.Site_Id as SiteId,  
 ads.VehicleReg,  
 ads.Chassis,  
 ads.StockNumber as StockNumber,
 stks.PhysicalLocation AS  PhysicalLocation,
 ads.RetailerIdentifier,  
 ads.WebSiteStockIdentifier,  
 ads.WebSiteSearchIdentifier,  
 ads.Make,  
 CASE   
 WHEN EXISTS   
  (SELECT 1 FROM STRING_SPLIT(rs.Makes, ',')  WHERE UPPER(value) = UPPER(ads.Make))   
 THEN ads.Make   
 ELSE 'Non Franchise'   
 END AS SimpleBrand,  
 ads.Model,  
 ads.Derivative,  
 ads.VehicleType,  
 snaps.OdometerReadingMiles as OdometerReading,  
 ads.EngineCapacityCC,  
 ads.BadgeEngineSizeLitres,
 ads.EnginePowerBHP,
 ads.OwnershipCondition,  
 snaps.LifecycleStatus,  
 ads.Trim,  
 ads.BodyType,  
 ads.FuelType,  
 ads.Drivetrain,  
 ads.TransmissionType,  
 ads.FirstRegisteredDate,  
 ads.Colour,  
 ads.SpecificColour,
 ads.Doors,
 ads.DerivativeId,  
 ads.DateOnForecourt,  
 ads.CreatedInSparkDate,  
 ads.AttentionGrabber,  
 opts.ActualEndDate, optoutperson.Name as OptOutBy,  
 opts.ActualEndDate as OptedOutUntil,  
 optoutperson.Name as OptedOutBy,  
 opts.CreatedDate as WhenOptedOut,  
 snaps.ForecourtPrice,  
 snaps.AdminFee,  
 snaps.TotalPrice as AdvertisedPrice,  
 tp.IsTradePricing,
 tp.MarginPercentage as TradeMarginPercentage,
 tp.MarginAmount as TradeMarginAmount,
 CASE  
  WHEN snaps.VatStatus IS NULL THEN 1  
  WHEN snaps.VatStatus = 'Ex VAT' THEN 0  
  ELSE 1  
 END as IncludingVat,  
 snaps.PriceIndicatorRatingAtCurrentSelling,  
 snaps.DaysToSellAtCurrentSelling,  
 snaps.NationalRetailDaysToSell,  
 snaps.ValuationMktAvRetail,  
 snaps.ValuationMktAvPartEx,  
 snaps.ValuationMktAvPrivate,  
 snaps.ValuationMktAvRetailExVat,  
 snaps.ValuationMktAvTrade,  
 snaps.ValuationAdjRetail,  
 snaps.ValuationAdjPartEx,  
 snaps.ValuationAdjPrivate,  
 snaps.ValuationAdjRetailExVat,  
 snaps.ValuationAdjTrade,  
 snaps.ValuationAdjRetail AS RelevantValuation,  
 snaps.RetailRating,  
 snaps.NationalRetailRating,  
  
 IIF(ads.ImageURLs IS NULL OR LEN(ads.ImageURLs)=0,1,0) as IsMissingImages,  
 IIF(ads.AttentionGrabber IS NULL OR LEN(ads.AttentionGrabber)=0,1,0) as NoAttentionGrabber ,  
 IIF(ads.VideoUrl IS NULL or LEN(ads.VideoUrl) = 0,1,0) as NoVideo,  
IIF(
  ads.AttentionGrabber IS NULL OR
  LEN(LTRIM(RTRIM(ads.AttentionGrabber))) = 0 OR -- Handles empty spaces as well
  ads.ImageURLs IS NULL OR
  (LEN(ads.ImageURLs) - LEN(REPLACE(ads.ImageURLs, '|', '')) + 1) < 9, -- Count images properly
  1, -- Flag as low quality
  0  -- Not low quality
) AS IsLowQuality,
 snaps.PerformanceRatingScore as PerfRatingScore,  
 snaps.StrategyPrice,  
 snaps.TestStrategyPrice,
 snaps.TestStrategyDaysToSell,
 snaps.StrategyPriceHasBeenCalculated,  
 snaps.PerformanceRatingRating as PerfRating,  
 snaps.PerformanceSearchViewsYest as SearchViewsYest,  
 snaps.PerformanceAdvertViewsYest as AdvertViewsYest,  
 snaps.PerformanceSearchViews7Day as SearchViews7Day,  
 snaps.PerformanceAdvertViews7Day as AdvertViews7Day,  
 snaps.RetailDemand,  
 snaps.RetailSupply,  
 snaps.RetailMktCondition as RetailMarketCondition,  
 snaps.NationalRetailMktCondition as NationalRetailMarketCondition,  
 stks.Id as StockItemId,  
 stks.siv as SIV,  
 stks.NonRecoverableCosts as PrepCost,  
 CASE   
        WHEN CHARINDEX('|', ads.ImageURLs) = 0 THEN ads.ImageURLs   
        ELSE LEFT(ads.ImageURLs, CHARINDEX('|', ads.ImageURLs) - 1)  
    END as ImageURL,  
 CASE   
        WHEN LEN(ads.ImageURLs) - LEN(REPLACE(ads.ImageURLs, '|', '')) < 4 THEN ads.ImageURLs  
        ELSE LEFT(ads.ImageURLs,   
                  CHARINDEX('|', ads.ImageURLs,   
                    CHARINDEX('|', ads.ImageURLs,   
                      CHARINDEX('|', ads.ImageURLs,   
                        CHARINDEX('|', ads.ImageURLs) + 1) + 1) + 1) - 1)  
    END as AllImageURLs,  
 CASE   
  WHEN LEN(ads.ImageURLs) = 0 THEN 0  
  ELSE LEN(ads.ImageUrls) - LEN(REPLACE(ads.ImageUrls,'|',''))+1  
 END   as ImagesCount,  
 CASE  
    -- New car, treat as VAT qualifying  
    WHEN vt.SuperType = 'New' AND stks.SIV <= 10 THEN null  -- no cost price, return null  
    WHEN vt.SuperType = 'New' AND stks.SIV > 10 THEN ROUND(snaps.TotalPrice * 5/6 - stks.SIV - stks.NonRecoverableCosts, 0)  
    -- Vat Qualifying car  
    WHEN stks.IsVatQ = 1 AND stks.SIV <= 10 THEN null  -- no cost price, return null  
    WHEN stks.IsVatQ = 1 AND stks.SIV > 10 THEN ROUND(snaps.TotalPrice * 5/6 - stks.SIV - stks.NonRecoverableCosts, 0)  
    -- Margin car  
    WHEN stks.SIV <=10 OR stks.OriginalPurchasePrice <= 10 THEN null -- no cost price, return null  
    ELSE ROUND(snaps.TotalPrice - stks.SIV - stks.NonRecoverableCosts - 
            CASE 
                WHEN ((snaps.TotalPrice - stks.OriginalPurchasePrice) / 6) > 0 
                THEN ((snaps.TotalPrice - stks.OriginalPurchasePrice) / 6) 
                ELSE 0 
            END, 
        0)
 END AS PricedProfit,  
  
 stks.OriginalPurchasePrice,  
 lastmanual.ChangeValue as LastPriceChangeValue,  
 lastmanual.CreatedDate as WhenPriceLastManuallyChanged,  
 lastmanual.ChangedBy as WhoLastManuallyChanged,  
 tc.Count as TotalManualPriceChangesCount,  
 tc.TotalChangeValue as TotalManualPriceChangesValue,  
 sv.Description as StockSource,  
 lc.LastCommentName,  
 lc.LastCommentText,  
 stks.IsVatQ,  
 COALESCE(vt.Description,'UnknownType') as VehicleTypeDesc,  
 CASE WHEN stks.StockNumberFull IS NOT NULL THEN LEFT(stks.StockNumberFull, 1) ELSE NULL END AS StockPrefix,  
 stks.StockDate,  
 CASE 
    WHEN stks.DateSiteArrival IS NULL THEN NULL
    ELSE TRY_CONVERT(DATETIME, CAST(stks.DateSiteArrival AS VARCHAR(8)), 112)
END AS DateBookedIn,
 todch.DaysToSell AS DaysToSellAtNewPrice,  
 todch.NowPrice AS NewPrice,  
 todch.IsSmallPriceChange,  
 CASE   
  WHEN todch.PriceIndicator IS NULL THEN NULL  
  WHEN UPPER(todch.PriceIndicator) = 'LOW' THEN 'Low'  
  WHEN UPPER(todch.PriceIndicator) = 'FAIR' THEN 'Fair'  
  WHEN UPPER(todch.PriceIndicator) = 'HIGH' THEN 'High'  
        WHEN UPPER(todch.PriceIndicator) = 'GOOD' THEN 'Good'  
        WHEN UPPER(todch.PriceIndicator) = 'GREAT' THEN 'Great'  
  WHEN UPPER(todch.PriceIndicator) = 'NOANALYSIS' THEN 'NoAnalysis'  
        ELSE todch.PriceIndicator  
    END AS PriceIndicatorAtNewPrice,  
 snaps.AutotraderAdvertStatus,  
 snaps.AdvertiserAdvertStatus,  
 snaps.AutotraderAdvertStatus,  
 ads.FirstPrice,  
 ads.DailyPriceMovesCount,  
 ads.MostRecentDailyPriceMove,  
 ads.MostRecentDailyPriceMoveDate,  
  
 snaps.AveragePP,  
 snaps.CheapestSellerName,  
 snaps.CheapestSellerType,  
 snaps.CheapestVehicle,  
 snaps.CompetitorCount,  
 snaps.HighestPP,  
 snaps.LowestPP,  
 snaps.OnlyVehicle,  
 snaps.OurPPRank,
 snaps.OurValueRank,    

 snaps.AllPrices,  
 snaps.PPAverageFranchised,
 snaps.PPAverageIndependents,
 snaps.PPAveragePrivates,
 snaps.PPAverageSupermarkets,

 stks.Selling as DMSSellingPrice,  
 snaps.PriceUpMaintainRank ,  
    snaps.PriceDownImproveRank,  
    snaps.PriceToBeCheapest,   
 bbsi.Vin AS BcaVin,
 bbsi.Mileage AS BcaMileage,
 bbsi.DaysOnAllSites,
 bbsi.NumberOfPreviousSales,
 bbsi.V5Status,
 bbsi.ServiceHistory,
 bbsi.Runner,
 bbsi.SalesComment,
 bbsi.HoldDate,
 bbsi.HoldCode,
 bbsi.HoldDescription,
 snaps.ValuationMonthPlus1 as ValuationMonthPlus1,  
 snaps.ValuationMonthPlus2 as ValuationMonthPlus2,  
 snaps.ValuationMonthPlus3 as ValuationMonthPlus3, 
CASE
    WHEN ads.Owners IS NULL THEN CAST('Unknown' AS VARCHAR(10))
    ELSE CAST(ads.Owners AS VARCHAR(10))
END AS Owners,
 po.PortalOptions  
 FROM  filteredSnaps snaps   
 INNER JOIN #veryLatestSnapshotIds latestSnaps on latestSnaps.Id = snaps.Id  
 INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id  
 INNER JOIN #todayAdIds tai on tai.AdId = ads.Id  
 INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id  
 LEFT JOIN #TodayChanges todch ON todch.VehicleAdvertSnapshot_Id = snaps.Id AND RowNumber = 1  
 LEFT JOIN #mostRecentOptOutForAd opts on opts.VehicleAdvert_Id = ads.id and opts.RowNumber = 1  
 LEFT JOIN people optoutperson on optoutperson.id = opts.Person_Id  
 LEFT JOIN Stocks stks ON  stks.Id = ads.Stock_Id   
 LEFT JOIN dbo.VehicleTypes vt on vt.Id = stks.VehicleType_Id  
  
 LEFT JOIN Sites s on s.Id = rs.Site_Id  
 LEFT JOIN Regions regi on regi.id = s.Region_Id  
 LEFT JOIN #lastManualChangePerAd lastmanual on lastmanual.AdId = ads.id  
 LEFT JOIN #totalChanges tc on tc.VehicleAdvert_Id = ads.Id  
 LEFT JOIN StandingValues sv ON sv.id = stks.PreviousUse_Id  
 LEFT JOIN #latestComments lc on lc.AdvertId = ads.Id AND lc.RowNumber = 1  
  
 LEFT JOIN #portalOptions po on po.AdId = tai.AdId  
 LEFT JOIN #bcaLastSixM bbsi ON bbsi.Registration = ads.VehicleReg
 LEFT JOIN autoprice.TradePriceSettings tp on tp.RetailerSiteId = ads.RetailerSite_Id
  
 WHERE snaps.SnapshotDate >= @effectiveDate AND snaps.SnapshotDate < @effectiveDatePlus1
 --AND snaps.LifecycleStatus <> 'WASTEBIN'  
 --AND snaps.LifecycleStatus <> 'SOLD'  
  
  
DROP TABLE #todayAdIds  
DROP TABLE #veryLatestSnapshotIds  
DROP TABLE #mostRecentOptOutForAd  
DROP TABLE #latestComments  
DROP TABLE #TodayChanges  
DROP TABLE #AdIds;
--DROP TABLE #portalOptions  
  
END
GO