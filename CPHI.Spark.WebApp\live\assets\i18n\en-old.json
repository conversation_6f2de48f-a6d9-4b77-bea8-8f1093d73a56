{"dashboadName": "Test EN", "ROOT": {"AccountSettings": "Account <PERSON><PERSON>", "Common": {"Achievement": "Acheivement", "Actual": "Actual", "AddOnCount": "Add-on Product Count", "AddOnProfit": "Add-on Profit", "AddOns": "Add-Ons", "Aftersales": "Aftersales", "All": "All", "AllAges": "All Ages", "Anytime": "Anytime", "AreYouSure": "Are you sure?", "Between": "Between", "Bonuses": "Bonuses", "Cancel": "Cancel", "Clear": "Clear", "Close": "Close", "Colour": "Colour", "Comments": "Comments", "Commission": "Commission", "CoreUsed": "Core Used", "CoS": "CoS", "Cost": "Cost", "Cumulative": "Cumulative", "Custom": "Custom", "Customer": "Customer", "Daily": "Daily", "Date": "Date", "Dates": "Dates", "DayLower": "day", "Days": "Days", "DaysLower": "days", "Deal": "deal", "Deals": "deals", "Debts": "Debts", "DeliveryDate": "Delivery Date", "Demo": "Demo", "Description": "Description", "Detail": "Detail", "Discount": "Discount", "Done": "Done", "Error": "Error", "ExcludeLateCosts": "Exclude Late Costs", "ExcludeOrders": "Exclude Orders", "Finance": "Finance", "FinanceProfit": "Finance Profit", "Fleet": "Fleet", "Franchise": "Franchise", "Franchises": "Franchises", "Friday": "Friday", "FullMonthTarget": "Full Month Target", "Gap": "Gap", "GettingDeals": "Getting Deals...", "GP": "GP", "GPU": "GPU", "Id": "Id", "IncludeLateCosts": "Include Late Costs", "IncludeOrders": "Include Orders", "LastWeek": "Last Week", "Make": "Make", "Margin": "<PERSON><PERSON>", "Metal": "Metal", "MetalProfit": "Metal Profit", "Model": "Model", "ModelYear": "Model Year", "Monday": "Monday", "Month": "Month", "MonthTarget": "Month Target", "Name": "Name", "Net": "Net", "New": "New", "NextWeek": "Next Week", "No": "No", "NoFranchises": "No Franchises", "NoLower": "no", "NoOrderType": "No Order Types", "NoVehicleType": "No Vehicle Types", "Now": "Now", "Of": "of", "Ok": "OK", "OnlyLateCosts": "Only Late Costs", "OnlyOrders": "Only Orders", "Order": "order", "OrderRate": "Order Rate", "Orders": "orders", "OrderType": "Order Type", "Other": "Other", "OtherProfit": "Other Profit", "Over": "Over", "PerUnit": "Per Unit", "Products": "Products", "Profit": "Profit", "Reconnect": "Reconnect", "Refresh": "Refresh", "Reg": "Reg", "Registered": "Registered", "Relogin": "Relogin", "Required": "Required", "RestartingMessage": "Restarting Spark...", "Sale": "Sale", "Sales": "Sales", "SalesExec": "Exec", "Saturday": "Saturday", "Search": "Search", "ServicePlan": "Service Plan", "Site": "Site", "Sites": "Sites", "SitesOverview": "Sites Overview", "SortBy": "Sort by", "StepUp": "Step-Up", "StockNumber": "Stock Number", "Summary": "Summary", "Sunday": "Sunday", "Tactical": "Tactical", "Target": "Target", "ThisWeek": "This Week", "Thursday": "Thursday", "Today": "Today", "ToGo": "To Go", "Tomorrow": "Tomorrow", "Total": "Total", "TotalOf": "Total of", "TotalProfit": "Total Profit", "TotalSite": "Total Site", "TotalValue": "Total Value", "Trade": "Trade", "Tuesday": "Tuesday", "Unit": "Unit", "Units": "Units", "Used": "Used", "Variant": "<PERSON><PERSON><PERSON>", "VariantClass": "Variant Class", "Vehicle": "Vehicle", "VehicleAge": "Vehicle Age", "VehicleClass": "Vehicle Class", "Vehicles": "Vehicles", "VehicleType": "Vehicle Type", "vsBudget": "vs Budget", "vsLastMonth": "vs Last Month", "vsLastYear": "vs Last Year", "vsThisMonth": "vs This Month", "Warranty": "Warranty", "Wednesday": "Wednesday", "Week": "Week", "WelcomeMessage": "Starting Spark...", "WheelGuard": "WheelGuard", "Wips": "WIPs", "With": "With", "Year": "Year", "Yesterday": "Yesterday"}, "Dashboard": {"ActivityLevels": {"Over1Day": "Over 1 day", "Over30Days": "Over 30 days", "Over60Days": "Over 60 days", "OverAWeek": "Over a week", "Overdues": "Overdues", "Title": "Activity Levels w/c", "TotalOverdue": "Total Overdue"}, "AgedWips": "Aged WIPs", "CitNow": {"ofSalesEnquiries": "of Sales Enquiries"}, "Debtors": {"BonusesAging": "Bonuses Aging", "DebtsAging": "Debts Aging", "OfWhichOver30Days": "Of which: Over 30 days", "OfWhichOver60Days": "Of which: Over 60 days", "Title": "Debtors", "TotalBonuses": "Total Bonuses", "TotalDebts": "Total Debts", "TotalWips": "Total Wips", "WipsAging": "Wips Aging"}, "Donut": {"FleetPerformanceForTheMonth": "Fleet Performance For The Month", "NewPerformanceForTheMonth": "New Performance For The Month", "UsedPerformanceForTheMonth": "Used Performance For The Month"}, "Evhc": {"RedWork": "Red Work", "Title": "Electronic Vehicle Health Check"}, "FinanceAddOnPerformance": "Finance Add-On Performance", "FinanceAddons": {"Title": "Finance Add-ons"}, "FleetDealsByType": "Fleet Deals By Type", "NewDealsBreakdown": "New Deals Breakdown", "NoAgedWips": "No Aged WIPs", "PartsSales": {"Title": "Parts Sales"}, "PartsSalesVsTarget": "Parts Sales vs Target", "PartsStock": {"OfTotalStock": "Of Total Stock", "OverageStock": "Overage Cover", "StockCover": "Stock Cover", "Title": "Parts Stock", "TotalValue": "Total Value"}, "PartsStockValue": "Parts Stock Value", "Registrations": {"Title": "Registrations"}, "RegistrationsDacia": "Registrations Dacia", "RegistrationsRenault": "Registrations Renault", "SalesmanEfficiency": {"Title": "Salesman Efficiency"}, "SalesPerformance": {"ExcludingTradeUnits": "Excluding Trade Units", "ForDeliveryInMonth": "For delivery in the month of", "IncludingTradeUnits": "Including Trade Units", "OrderRateAndProjection": "Order-Rate and Projection", "ProjectedFinish": "Projected Finish", "ProjectedVs": "Projected Vs", "Title": "Sales Performance", "TotalVehicles": "Total Vehicles", "UnitsVsTarget": "Units vs Target", "VehiclesOrdered": "Vehicles Ordered"}, "ServiceBookings": {"Title": "Service Bookings"}, "ServiceSales": {"SalesPerDay": "Sales Per Day", "SalesVsTarget": "Sales Vs Target", "Title": "Service Sales", "WorkInProgress": "Work In Progress"}, "ServiceSalesVsTarget": "Service Sales vs Target", "SiteCompare": "Site Compare", "SitePerformanceLeague": {"FleetUnits": "Fleet Units", "NewMargin": "New Margin", "NewUnits": "New Units", "PartsSales": "Parts Sales", "ServiceSales": "Service Sales", "Title": "Site Performance League", "UsedMargin": "Used Margin", "UsedUnits": "Used Units"}, "StockOverage": {"NewVehiclesGreaterThan": "New Vehicles >", "TradeVehiclesGreaterThan": "Trade Vehicles >", "UsedVehiclesGreaterThan": "Used Vehicles >"}, "StockReport": {"AgedOver": "Aged over", "AsAt": "As at", "BranchDays": "Branch Days", "GroupDays": "Group Days", "Title": "Stock Report", "UsingDaysAtBranch": "Using days at branch", "UsingGroupDays": "Using group days"}, "ThisWeeksOrders": {"FleetOrdersTaken": "Fleet Orders Taken", "NewOrdersTaken": "New Orders Taken", "UsedOrdersTaken": "Used Orders Taken"}, "Title": "Dashboard", "UsedDealsByType": "Used Deals By Type", "UsedStockHealth": "Used Stock Health", "UsedStockMerchandising": "Used Stock Merchandising", "VoC": {"Title": "Voice of the Customer"}, "WipReport": {"Account": "Account", "Age": "Age", "Ageing": "Ageing", "AsAtMonthEnd": "As At: Month End", "AsAtNow": "As At: Now", "BookingStatus": "Bookings Status", "Created": "Created", "Department": "Department", "DueIn": "Due In", "MonthEnd": "Month End", "Notes": "Notes", "Provision": "Provision", "Title": "WIP Report", "WipNumber": "WIP Number"}, "WithPrepCost": "With Prep Cost"}, "DealDetails": {"Accessories": "Accessories", "AccountingDate": "Accounting Date", "BodyPrep": "Body Prep", "BrokerCost": "Broker <PERSON>st", "CosmeticInsurance": "Cosmetic Insurance", "DealRemoved": "Deal Removed", "Delivery": "Delivery", "FactoryBonus": "Factory Bonus", "FinanceAddOnProfit": "Finance and Add-On Profit", "FinanceCo": "Finance Co.", "FinanceCommission": "Finance Commission", "FinanceSubsidy": "Finance Subsidy", "Fuel": "Fuel", "GapInsurance": "Gap Insurance", "IntroCommission": "Intro Commission", "InvoiceDate": "Invoice Date", "IsDelivered": "Is Delivered?", "IsLateCost": "Is Late Cost?", "IsOnFinance": "Is on Finance?", "MechanicalPrep": "Mechanical Prep", "OrderDate": "Order Date", "PaintProtection": "Paint Protection", "PartExchange": "Part Exchange", "Pdi": "PDI", "ProPlusCommision": "Pro Plus Commission", "RciFinanceCommission": "RCI Finance Commission", "RegBonus": "Reg Bonus", "RegisteredDate": "Registered Date", "SelectCommission": "Select Commission", "StandardsCommission": "Standards Commission", "StockDate": "Stock Date", "TinProfit": "Tin Profit", "Title": "Deal Details", "TyreAlloyInsurance": "<PERSON><PERSON> Ins.", "TyreInsurance": "Tyre Insurance", "Variant": "<PERSON><PERSON><PERSON>", "VariantText": "Variant Text"}, "DealsDoneThisMonth": {"BroughtIn": "<PERSON><PERSON>t In", "Mtd": "MTD", "Title": "Deals For The Month"}, "DealsDoneThisWeek": {"FuelSale": "Fuel Sale", "Title": "Deals Done This Week"}, "Debts": {"AgedDebtsOn": "Aged Debts On", "AsAt": "As At", "DocDate": "Doc Date", "DueDate": "Due Date", "MonthEnd": "Month End"}, "DisconnectedMessage": "Disconnected from app.  Try Reconnect, if that fails, try Re-Login.", "FamilyPicker": {"AllFamilyCodes": "All Family Codes", "FamilyCodes": "Family Codes", "NoFamilyCodes": "No Family Codes"}, "HandoverDiary": {"Handover": "handover", "Handovers": "handovers", "Title": "Handover Diary"}, "LoadingPage": "Loading Page...", "ManualReloadMessage": "Spark automatically updates with latest data as you change pages, but you can click here to manually reload all data from the server.", "Orderbook": {"Del": "Del", "IsDelivered": "D", "L": "L", "LateCost": "Late Cost.  Filter by using equals true.", "LoadingOrderbook": "Loading Orderbook...", "OrdersApprovedBetween": "Orders approved between", "Q": "Q", "Title": "Order Book"}, "PartsStockAgeing": {"Title": "Parts Stock Ageing"}, "PerformanceLeague": {"Advisor": "Advisor", "Bronze": "Bronze", "Gold": "Gold", "IncLeavers": "Inc Leavers", "Rank": "Pos.", "Role": "Role", "ShowAllSites": "Show All Sites", "ShowDelivered": "Show Delivered", "ShowProfit": "Show Profit", "Silver": "Silver", "Title": "Performance League"}, "ReportPortal": {"Title": "Report Portal"}, "SitePicker": {"AllSitesSelected": "All sites", "NoSitesSelected": "No sites selected"}, "StockItemModal": {"AccountStatus": "Account Status", "CapCode": "CAP Code", "CapId": "CAP Id", "CapNotes": "CAP Notes", "CapProven": "CAP Proven", "CarryingValue": "Carrying Value", "Chassis": "<PERSON><PERSON><PERSON>", "DaysAtBranch": "Days At Branch", "DaysInStock": "Days In Stock", "DisposalRoute": "Disposal Route", "Doors": "Doors", "Fuel": "Fuel", "Mileage": "Mileage", "Options": "Options", "PreviousSite": "Previous Site", "PreviousUse": "Previous Use", "ProgressCode": "Progress Code", "Registration": "Registration", "StockcheckLocation": "Stockcheck Location", "StockcheckPhoto": "Stockcheck Photo", "StockcheckTime": "Stockcheck Time", "Title": "Stock Item", "Transmission": "Transmission", "VatQualifying": "Vat Qualifying", "VehicleDetails": "Vehicle Details"}, "StockLanding": {"Title": "Stock Landing"}, "StockList": {"ChooseFields": "<PERSON><PERSON>", "ChooseNewReportName": "Choose a name for the new report", "HelpText": "Click each report field to toggle whether it appears in the table. Re-arrange columns by dragging them within the table.", "LastSaved": "Last Saved", "OpenReport": "Open Report", "ReportName": "Report Name", "SaveAsNewReport": "Save As New Report", "SaveReport": "Save Report", "ThisWillOverwriteReport": "This will overwrite report:", "Title": "Stock List"}, "SuperCup": {"Title": "<PERSON>cia Duster Cup"}, "TodayMap": {"Title": "Today's Deals"}, "UpdateProfilePicture": "Update Profile Picture", "UserMaintenance": {"Title": "User Maintenance"}, "Whiteboard": {"Delivered": "delivered", "Profit": "Profit", "Title": "Whiteboard", "ToDo": "to do"}}}