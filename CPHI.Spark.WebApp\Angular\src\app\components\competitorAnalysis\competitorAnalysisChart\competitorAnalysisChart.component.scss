#competitorAnalysisCanvasContainer {
   width: calc(100% - 15em);
   height: 90%;

   &.large<PERSON><PERSON> {
      width: calc(100% - 30em);
   }
}

#competitorAnalysisKey {
   left: 0;
   padding-left: 10px;
   bottom: 0;
   max-width: 100%;
   position: relative;
   text-align: center;
   display: flex;
   justify-content: space-between;
   column-gap: 1em;

   &.large<PERSON>hart {
      padding-right: 30em;
   }


   span svg {
      margin-right: 0.5em;
      color: rgba(20, 20, 20, 1);

      &.Independent {
         color: rgba(145, 110, 191, 0.5);
      }

      &.Franchise {
         color: rgba(255, 154, 0, 0.5);
      }

      &.Supermarket {
         color: rgba(255, 91, 79, 0.5);
      }

      &.Private {
         color: rgba(19, 83, 255, 0.5);
      }

      &.Other {
         color: rgba(91, 190, 91, 0.5);
      }
   }
}

#customTooltipContainer {
   position: absolute;
   top: 0;
   right: 0;
   bottom: 0;
   padding: 1px 0.25em 0 1em;
   width: 15em;
   overflow: auto;

   &.large<PERSON>hart {
      width: 30em;

      #customTooltipDetailed img {
         max-height: unset;
      }
   }
}

#customTooltip {
   box-shadow: 0 1px 3px rgba(63, 63, 68, 0.1490196078), 0 0 0 1px rgba(63, 63, 68, 0.0509803922);
   border-radius: 4px;
   padding: 0.3em;
   line-height: normal;
   margin-bottom: 1em;

   #tooltipTitle {
      font-weight: bold;
   }
}

#customTooltipDetailed {
   box-shadow: 0 1px 3px rgba(63, 63, 68, 0.1490196078), 0 0 0 1px rgba(63, 63, 68, 0.0509803922);
   border-radius: 4px;
   padding: 0.3em;
   line-height: normal;
   display: flex;
   flex-direction: column;

   img {
      max-width: 100%;
      max-height: 6em;
      object-fit: contain;
      margin-bottom: 0.5em;
   }
}
