﻿using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess.AutoPrice;
using System.Linq;
using System;
using CPHI.Spark.Model.ViewModels;

namespace CPHI.Spark.WebApp.Service.Autoprice
{
    public interface ISiteSettingsService
    {
        Task<IEnumerable<SiteSettings>> GetSitesSettings(DealerGroupName userDealerGroupName);
        Task<SiteSettings> SaveSiteSettings(SaveSiteSettingsParams parms, DealerGroupName userDealerGroupName,List<int> userRetailerSiteIds);
        //Task<RetailerSiteStrategyVersion> SaveSiteStrategy(RetailerSiteStrategyVersion retailerSiteStrategyVersion);
    }

    public class SiteSettingsService : ISiteSettingsService
    {
        private readonly IUserService userService;
        private readonly IConfiguration configuration;
        private readonly string _connectionString;
        private readonly DealerGroupName userDealerGroup;

        public SiteSettingsService(IUserService userService, IConfiguration configuration)
        {
            this.userService = userService;
            this.configuration = configuration;
            userDealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(userDealerGroup);
            _connectionString = configuration.GetConnectionString(dgName);
        }

        public async Task<IEnumerable<SiteSettings>> GetSitesSettings(DealerGroupName userDealerGroupName)
        {
            var siteSettingsDataAccess = new SiteSettingsDataAccess(_connectionString);
            int userId = this.userService.GetUserId();
            return (await siteSettingsDataAccess.GetSitesSettings(userDealerGroupName, userId));
        }

        public async Task<SiteSettings> SaveSiteSettings(SaveSiteSettingsParams parms, DealerGroupName userDealerGroupName, List<int> userRetailerSiteIds)
        {
            var siteSettingsDataAccess = new SiteSettingsDataAccess(_connectionString);

            if (!userRetailerSiteIds.Contains(parms.siteSettings.RetailerSiteId))
            {
                //user does not have access
                throw new Exception("User does not have access to this site");
            }

            var toReturn = await siteSettingsDataAccess.SaveSiteSettings(parms);

            return toReturn;
        }

        //public async Task<RetailerSiteStrategyVersion> SaveSiteStrategy(RetailerSiteStrategyVersion retailerSiteStrategyVersion)
        //{
        //    var siteSettingsDataAccess = new SiteSettingsDataAccess(_connectionString);
        //    return (await siteSettingsDataAccess.SaveSiteStrategy(retailerSiteStrategyVersion));
        //}
    }
}
