﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.Service
{
    public interface ISitesService
    {
        Task<IEnumerable<SiteVM>> GetAllSiteVMs(int userId, DealerGroupName dealerGroup);
        Task<IEnumerable<SiteVM>> GetSites(int userId, DealerGroupName dealerGroup, bool? showAllSites = false);
    }

    public class SitesService: ISitesService
    {
        //properties of the service
        private readonly IConfiguration configuration;

        //constructor
        public SitesService(IConfiguration configuration)
        {
            this.configuration = configuration;
        }







        public async Task<IEnumerable<SiteVM>> GetAllSiteVMs(int userId, DealerGroupName dealerGroup)
        {
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            return await siteDataAccess.GetSites(userId,dealerGroup, true);
        }



        public async Task<IEnumerable<SiteVM>> GetSites(int userId, DealerGroupName dealerGroup, bool? showAllSites = false)
        {
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            return await siteDataAccess.GetSites(userId,dealerGroup, showAllSites);
        }




    }
}
