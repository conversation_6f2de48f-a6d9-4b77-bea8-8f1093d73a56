import { Component, OnInit } from '@angular/core';
import { ConstantsService } from 'src/app/services/constants.service';
import { BIChartTileDataType } from 'src/app/components/biChartTile/biChartTile.component';
import { Subscription } from 'rxjs';
import { LeavingVehicleDetailService } from '../leavingVehicleDetail/leavingVehicleDetail.service';
import { Router } from '@angular/router';
import { ExternalFiltersForLeavingVehicleAnalysis } from 'src/app/model/ExternalFiltersForLeavingVehicleAnalysis';
import { CphPipe } from 'src/app/cph.pipe';
import { LeavingVehicleTrendsService } from './leavingVehicleTrends.service';
import { MenuItemNew } from 'src/app/model/main.model';
import { SelectionsService } from 'src/app/services/selections.service';

@Component({
  selector: 'app-leavingVehicleTrends',
  templateUrl: './leavingVehicleTrends.component.html',
  styleUrls: ['./leavingVehicleTrends.component.scss']
})
export class LeavingVehicleTrendsComponent implements OnInit {
  choiceEmitterSub: Subscription;
  months: Date[];
  constructor(
    public service: LeavingVehicleTrendsService,
    public trendsService: LeavingVehicleTrendsService,
    public constants: ConstantsService,
    public leavingVehicleDetailService: LeavingVehicleDetailService,
    public router: Router,
    private cphPipe: CphPipe,
    public selections: SelectionsService
  ) { }

  public dataTypes = BIChartTileDataType;

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: true, message: 'Loading...' });

    this.trendsService.initParams();
    this.trendsService.getData();
    this.choiceEmitterSub = this.trendsService.highlightChoiceMadeEmitter.subscribe(res => {
      this.trendsService.updateHighlightedData()
    })
    this.choiceEmitterSub = this.trendsService.filterChoiceMadeEmitter.subscribe(res => {
      this.trendsService.updateFilteredData()
    })
  }


  ngOnDestroy() {
    if (this.choiceEmitterSub) { this.choiceEmitterSub.unsubscribe() }
  }

  goToLeavingVehiclesAnalysis() {
    this.buildFilterParams();
    this.leavingVehicleDetailService.startDate = this.trendsService.startDate;
    this.leavingVehicleDetailService.endDate = this.trendsService.endDate;

    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/leavingVehicleDetail');
    if (menuItem) { this.constants.navigateByUrl(menuItem); }  //, 'vehiclepricing'
  }

  buildFilterParams() {
    let filterModel:{[key:string]:{filterType:string,values:string}} = {};
    const filters: ExternalFiltersForLeavingVehicleAnalysis = this.trendsService.externalFilterForLeavingVehicleAnalysis;

    this.trendsService.highlightChoices.forEach(choice => {
      filters[choice.FieldName] = choice.ChosenValues.length > 0 ? choice.ChosenValues.map(val => val.toString()) : null
    })

    let filterNames: string[] = [
      //'AchievedSaleType',
      'BodyType',
      'DaysListedBand',
      'RetailerSiteName',
      'FirstPPBand',
      'FuelType',
      'LastPPBand',
      'LastPriceBand',
      'LastPriceIndicator',
      'Make',
      'Mileage',
      'Model',
      'Region',
      'RegYear',
      'RetailRatingBand',
      'RetailerSiteName',
      'TransmissionType',
      'IsOnStrategy',
      'OptedOutPctBand'
    ];

    filterNames.forEach(name=>{
      if (filters[name]) {
        filterModel[name] = {
          filterType: 'set',
          values: filters[name]
        }
      }
    })


    this.leavingVehicleDetailService.setExternalFilterModel(filterModel);
  }

  daysListedTitle() {
    const isFiltered = this.trendsService.rawDataHighlighted.length != this.trendsService.rawDataFiltered.length;
    let result = `Days Listed (${this.cphPipe.transform(this.trendsService.summaryStats.highlighted.daysListed, 'number', 0)}`
    if (isFiltered) { result += ` vs ${this.cphPipe.transform(this.trendsService.summaryStats.all.daysListed, 'number', 0)}` }
    result += ')'
    return result;
  }
  retailRatingTitle() {
    const isFiltered = this.trendsService.rawDataHighlighted.length != this.trendsService.rawDataFiltered.length;
    let result = `Retail Rating (${this.cphPipe.transform(this.trendsService.summaryStats.highlighted.retailRating, 'number', 0)}`
    if (isFiltered) { result += ` vs ${this.cphPipe.transform(this.trendsService.summaryStats.all.retailRating, 'number', 0)}` }
    result += ')'
    return result;
  }
  firstPPTitle() {
    const isFiltered = this.trendsService.rawDataHighlighted.length != this.trendsService.rawDataFiltered.length;
    let result = `First Price Band (${this.cphPipe.transform(this.trendsService.summaryStats.highlighted.firstPP, 'percent', 1)}`
    if (isFiltered) { result += ` vs ${this.cphPipe.transform(this.trendsService.summaryStats.all.firstPP, 'percent', 1)}` }
    result += ')'
    return result;
  }
  lastPPTitle() {
    const isFiltered = this.trendsService.rawDataHighlighted.length != this.trendsService.rawDataFiltered.length;
    let result = `Last Price Band (${this.cphPipe.transform(this.trendsService.summaryStats.highlighted.lastPP, 'percent', 1)}`
    if (isFiltered) { result += ` vs ${this.cphPipe.transform(this.trendsService.summaryStats.all.lastPP, 'percent', 1)}` }
    result += ')'
    return result;
  }



  makeMonthsDropdownOnClick() {
    this.months = this.constants.makeMonthsNewV3(true);
  }

  selectMonthOnClick(chosenDate: Date, isStartDate?: boolean) {
    this.selections.triggerSpinner.next({ show: true, message: 'Loading...' });
    
    if (isStartDate) {
      this.trendsService.startDate = this.constants.startOfMonth(chosenDate);
      // If new start date is greater than end date, update end date
      if (this.trendsService.startDate.getTime() > this.trendsService.endDate.getTime()) {
        this.trendsService.endDate = this.constants.endOfMonth(this.trendsService.startDate);
      }
    } else {
      this.trendsService.endDate = this.constants.endOfMonth(chosenDate);
      // If new end date is less than start date, update start date
      if (this.trendsService.endDate.getTime() < this.trendsService.startDate.getTime()) {
        this.trendsService.startDate = this.constants.startOfMonth(this.trendsService.endDate);
      }
    }
    this.trendsService.getData();
  }

  getMonthName(date: Date): string {

    date = new Date(date);
    return date.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
  }

  pageIsFiltered() {
    return !this.service.highlightChoices.map(x => x.ChosenValues).every(innerArr => Array.isArray(innerArr) && innerArr.length === 0);
  }
}
