import { Component, OnInit, Input, ElementRef, ChangeDetectorRef } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Deal, DealPopover } from '../model/sales.model';
import { ConstantsService } from '../services/constants.service';
import { CommentEditorComponent } from './commentEditor.component';
import { GetDealDataService } from '../services/getDeals.service';
import { CommonModule } from "@angular/common";
import { forkJoin } from "rxjs";
import { SelectionsService } from "../services/selections.service";
import { ProfilePictureService } from "../services/profilePicture.service";
import { ProfilePicSize } from "../model/main.model";


@Component({
  selector: 'popoverContentRRG',
  template: `
                                                                                                                                                                                                                                                                                                   <div class="personAndPicture">
    <div>


              <profilePicImage *ngIf="deal" [personId]="deal.SalesmanId" [size]="profilePicSize"></profilePicImage>
              <profilePicImage *ngIf="!deal" [personId]="0" [size]="profilePicSize"></profilePicImage>

    </div>
    <div class="personName">{{deal.Salesman}}</div>
    </div>
    <table>
      <tbody>
        <tr>
          <td>{{constants.translatedText.Vehicle}}</td>
          <td>{{deal.Description}}</td>
        </tr>
        <tr>
          <td>{{constants.translatedText.DeliveryDate}}</td>
          <td>{{deal.ActualDeliveryDate|cph:'date':0}}</td>
        </tr>
        <tr>
          <td *ngIf="constants.environment.dealPopover_showMetalProfit">{{constants.translatedText.MetalProfit}}</td>
          <td *ngIf="constants.environment.dealPopover_showMetalProfit">{{deal.MetalProfit|cph:'currency':0}}</td>
        </tr>
        <tr>
          <td *ngIf="constants.environment.dealPopover_showOtherProfit">{{constants.translatedText.OtherProfit}}</td>
          <td *ngIf="constants.environment.dealPopover_showOtherProfit">{{deal.OtherProfit|cph:'currency':0}}</td>
        </tr>
        <tr>
          <td *ngIf="constants.environment.dealPopover_showFinanceProfit">{{constants.translatedText.FinanceProfit}}</td>
          <td *ngIf="constants.environment.dealPopover_showFinanceProfit">{{deal.FinanceProfit|cph:'currency':0}}</td>
        </tr>
        <tr>
          <td *ngIf="constants.environment.dealPopover_showAddons">{{constants.translatedText.Products}}</td>


          <div *ngIf="constants.environment.dealPopover_showAddons">
          <td>
          <div *ngIf="deal.HasCosmeticInsurance" title="Cosmetic Insurance" class="far fa-shield-check solid"></div>
          <div *ngIf="!deal.HasCosmeticInsurance" title="Cosmetic Insurance" class="far fa-shield-check"></div>

          <div *ngIf="deal.HasPaintProtection" title="Paint Protection" class="fas fa-claw-marks solid"></div>
          <div *ngIf="!deal.HasPaintProtection" title="Paint Protection" class="fas fa-claw-marks"></div>

          <div *ngIf="deal.HasGapInsurance" title="Gap Insurance" class="fas fa-car-crash solid"></div>
          <div *ngIf="!deal.HasGapInsurance" title="Gap Insurance" class="fas fa-car-crash"></div>

          <div *ngIf="deal.HasTyreInsurance" title="Tyre" class="fal fa-tire solid"></div>
          <div *ngIf="!deal.HasTyreInsurance" title="Tyre" class="fal fa-tire"></div>

          <div *ngIf="deal.HasTyreAndAlloyInsurance" title="TyreAlloy" class="fas fa-tire solid"></div>
          <div *ngIf="!deal.HasTyreAndAlloyInsurance" title="TyreAlloy" class="fas fa-tire"></div>

          <div *ngIf="deal.HasWheelGuard" title="WheelGuard" class="fal fa-circle solid"></div>
          <div *ngIf="!deal.HasWheelGuard" title="WheelGuard" class="fal fa-circle"></div>

          <div *ngIf="deal.HasServicePlan" title="Service Plan" class="fas fa-car-mechanic solid"></div>
          <div *ngIf="!deal.HasServicePlan" title="Service Plan" class="fas fa-car-mechanic"></div>

          <div *ngIf="deal.HasWarranty" title="Warranty" class="fas fa-file-certificate solid"></div>
          <div *ngIf="!deal.HasWarranty" title="Warranty" class="fas fa-file-certificate"></div>

          </td>
          </div>


        </tr>
        <tr *ngIf="constants.environment.dealPopover_showAddonProfit">
          <td>{{constants.translatedText.AddOnProfit}}</td>
          <td>{{deal.AddOnProfit|cph:'currency':0}}</td>
        </tr>
        <tr class="total">
          <td>{{constants.translatedText.TotalProfit}}</td>
          <td>{{deal.TotalNLProfit|cph:'currency':0}}</td>
        </tr>

        <tr  *ngFor="let comment of deal.comments">
          <td>
            <div class="openCommentEditor" (click)="openCommentEditor()">
              <i class="outlineComment fal fa-comment">  </i>
              <i class="solidComment fas fa-comment">  </i>
              {{comment.Date|cph:'date':0}}
            </div>
          </td>
          <td>
            <div class="commentHolder">
              <div class="commentText">  {{comment.Text}}</div>
             <div class="initials long">{{comment.Initials}} </div>
             </div>
          </td>
        </tr>
        <tr *ngIf="deal.comments.length<1">
          <td>
            <div class="openCommentEditor" (click)="openCommentEditor()">
              <i class="outlineComment fal fa-comment">  </i>
              <i class="solidComment fas fa-comment">  </i>
            </div>
          </td>
          <td></td>
        </tr>

      </tbody>
    </table>




    `
  ,
  styles: [`
    .personAndPicture{display:flex;justify-content:space-between;align-items:center;padding:0.1em 1em;width:100%;width:80%;margin:0.5em auto;}
    .personPicture{border-radius:50%;width:3em;}
    .personName{}
    table{ margin: 0.5em 0.1em;}

    table td{line-height:2em;white-space: nowrap;padding:0.2em 0.5em;}
    table td:nth-of-type(2){text-align:right;}
    table tr.total td{font-weight:700;}
    svg{opacity:0.2;color:var(--badColour);margin-right:0.3em;}
    svg.solid{opacity:1;color:var(--goodColour);}
    svg.solidComment{display:none;}
    .openCommentEditor svg{color:var(--brightColour);opacity:1!important; cursor: pointer;}
    .openCommentEditor:hover svg.outlineComment{display:none;cursor: pointer;}
    .openCommentEditor:hover svg.solidComment{display:inline-block;cursor: pointer;}
    .commentHolder{display:flex;width:100%;justify-content:flex-end;}
    .commentText{max-width: 20em;text-overflow: ellipsis;white-space: nowrap;         overflow: hidden;}
    .comment{width:85%;text-overflow: ellipsis;white-space: nowrap;         overflow: hidden;}
    .initials{width:15%;font-weight:700;overflow:hidden;color:var(--brightColourDark);text-align:right;}


    `]
})
export class DealPopoverRRGComponent implements OnInit {

  @Input() public dealId: number;

  public profilePicSize: ProfilePicSize;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public getDealData: GetDealDataService,
    private ref: ChangeDetectorRef,
    public profilePictureService: ProfilePictureService
  ) {


  }

  // change this
  deal: DealPopover;

  ngOnInit(): void {

    this.profilePicSize = ProfilePicSize.medium;

    this.selections.triggerSpinner.next({ show: true, message: '...' })

    let requests = []
    requests.push(this.getDealData.getDealPopover(this.dealId)) //0
    requests.push(this.getDealData.getDealComments(this.dealId)) //1

    forkJoin(requests).subscribe((res: any) => {

      // Deal Popover
      this.deal = res[0][0];

      // Comments For Popover
      this.deal.comments = res[1] ? res[1] : null;

    }, e => {

      //error
      console.error("Error generating popover:", e);

    }, () => {

      this.ref.detectChanges();

      setTimeout(() => {

        this.selections.triggerSpinner.next({ show: false })
      }, 20)

    })

  }

  openCommentEditor() {

    const modalRef = this.modalService.open(CommentEditorComponent, { keyboard: true, size: 'md' });

    //I give to modal
    modalRef.componentInstance.deal = this.deal;
    modalRef.componentInstance.dealStockNumber = this.deal.StockNumber;
    modalRef.componentInstance.comments = this.deal.comments;
    modalRef.componentInstance.dealId = this.dealId

    modalRef.result.then((result) => { //I get back from modal
      if (result) {

      }
    });

  }



}


