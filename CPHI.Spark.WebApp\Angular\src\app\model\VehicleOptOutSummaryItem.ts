
export interface VehicleOptOutSummaryItem {
  VehicleReg: string;
  Derivative: string;
  VehicleAdvertId: number;
  DaysListed: number;
  RetailRating: number;
  OptOutCreatedDate: Date | string;
  OptOutEndDate: Date | string;
  OptOutPerson: string;
  PriceFactor: number;
  MatrixPrice: number;
  LatestPrice: number;
  LastManualChange: Date | string | null;

  RatingsId: number;

  StockItemId: number;

  DaysOptedOutFor: number;
  DaysOptedOutToGo: number;
  LatestPriceVsMatrix: number;
}
