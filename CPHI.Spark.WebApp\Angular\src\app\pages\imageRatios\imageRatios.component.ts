import { Component, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { forkJoin } from 'rxjs';
import { CphPipe } from "../../cph.pipe";
import { Month } from '../../model/main.model';
import { AutotraderService } from "../../services/autotrader.service";
import { ConstantsService } from "../../services/constants.service";
import { GetDataMethodsService } from '../../services/getDataMethods.service';
import { SelectionsService } from "../../services/selections.service";
import { DashboardService } from "../dashboard/dashboard.service";
import { ImageRatiosService } from "./imageRatios.service";

@Component({
  selector: "app-imageRatios",
  templateUrl: "./imageRatios.component.html",
  styleUrls: ["./imageRatios.component.scss"],
})
export class ImageRatiosComponent implements OnInit {

  months: Array<Month>;
  myEventEmitter: EventEmitter<string>;
  mysubscription: any;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService, 

    public dataMethods: GetDataMethodsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public analysis: AutotraderService,
    public router: Router,
    public service: ImageRatiosService,
    public dashboardService: DashboardService
  ) { }

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading + '...' });
    if(!this.service.isInitialised) this.service.initiateImageRatios();
    let isSales: boolean = this.dashboardService.chosenSection.sectionName === 'Aftersales' ? false : true;
    this.chooseDepartment(isSales);
  }


  getData() {

    let requests = []

    requests.push(this.dataMethods.getImageRatiosRows()) //[0]
    requests.push(this.dataMethods.getImageRatioChartDataSets()) //[1]

    forkJoin(requests).subscribe((result: any) => {
      this.selections.triggerSpinner.next({ show: false });

      this.service.siteRows = result[0];

      this.service.chartDetails = result[1];

      this.service.newDataEmitter.emit();
    })



  }


  makeMonths() {
    this.months = this.constants.makeMonths(0, 0);
  }


  selectMonth(month: Month) {
    this.selections.triggerSpinner.next({ show: true });
    this.service.monthStartDate = month.startDate;
    this.getData()
  }

  trackByFunction(index: number) { return index; }


  changeMonth(changeAmount: number) {
    this.selections.triggerSpinner.next({ show: true });
    this.service.monthStartDate = this.constants.addMonths(this.service.monthStartDate,changeAmount);
    this.getData()
  }

  chooseDepartment(isSales: boolean) {
    this.service.showSales = isSales;
    this.getData();
  }




}
