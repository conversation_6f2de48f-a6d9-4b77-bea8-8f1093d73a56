import { Component, ElementRef, HostListener, Input, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ColDef, ColumnApi, GridApi } from 'ag-grid-community';
import { CphPipe } from 'src/app/cph.pipe';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { SelectionsService } from 'src/app/services/selections.service';
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { localeEs } from 'src/environments/locale.es.js';
import { PeopleSummaryByMonth, SEReviewMonthScore, SiteSummaryByMonth } from '../../salesExecReview.model';
import { SalesExecReviewService } from '../../salesExecReview.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

interface ByMonthSummary {
  Id: number;
  IsRegion: boolean;
  IsSite: boolean;
  IsTotal: boolean;
  Label: string;
  Months: Months;
}

interface Months {
  Month1: SEReviewMonthScore;
  Month2: SEReviewMonthScore;
  Month3: SEReviewMonthScore;
  Month4: SEReviewMonthScore;
  Month5: SEReviewMonthScore;
  Month6: SEReviewMonthScore;
  Month7: SEReviewMonthScore;
  Month8: SEReviewMonthScore;
  Month9: SEReviewMonthScore;
  Month10: SEReviewMonthScore;
  Month11: SEReviewMonthScore;
}

@Component({
  selector: 'salesExecReviewByMonthTable',
  templateUrl: './byMonth.component.html',
  styleUrls: ['./byMonth.component.scss', './../../../../../styles/components/_agGrid.scss']
})

export class SalesExecReviewByMonthTableComponent implements OnInit,OnDestroy {
  private destroy$ = new Subject<void>();
  @ViewChild('gridHolder', { static: true }) tableContainer: ElementRef;

  @HostListener('window:resize', [])

  @Input() isRegional: boolean;

  rowData: ByMonthSummary[];
  pinnedBottomRowData: ByMonthSummary[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public gridColumnApi: ColumnApi;

  constructor(
    public gridHelpersService: AGGridMethodsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public excel: ExcelExportService,
    public constants: ConstantsService,
    public service: SalesExecReviewService,
    public cphPipe: CphPipe
  ) { }

  ngOnInit() {

    //this.gridMethods.topBottomHighlights = [];
    
    this.formatRowData();
    this.setGridOptions();

    this.service.salesExecReview.byMonthDataChangeEmitter
    .pipe(takeUntil(this.destroy$))
    .subscribe(() => {
      this.refreshView();
    })
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
}

  formatRowData(refresh?: boolean) {
    let unformattedRows: (SiteSummaryByMonth | PeopleSummaryByMonth)[];

    let formattedRows: ByMonthSummary[] = [];
    if (this.service.salesExecReview.sitesView) {
      if (this.isRegional) {
        unformattedRows = this.service.salesExecReview.siteByMonthRows.filter(x => !x.IsSite);
      } else {
        unformattedRows = this.service.salesExecReview.siteByMonthRows.filter(x => !x.IsRegion);
      }
    } else {
      unformattedRows = this.service.salesExecReview.peopleByMonthRows;
    }

    unformattedRows.forEach(row => {
      let months: Months = {} as Months;

      row.MonthScores.forEach((score: SEReviewMonthScore, i: number) => {
        months[`Month${i+1}`] = score;
      })

      let formattedRow: ByMonthSummary = {
        Id: this.service.salesExecReview.sitesView ? row['SiteId'] : row['PersonId'],
        IsRegion: row.IsRegion,
        IsSite: row.IsSite,
        IsTotal: row.IsTotal,
        Label: row.Label,
        Months: months
      }

      if (row.IsTotal) this.pinnedBottomRowData = [formattedRow];
      else formattedRows.push(formattedRow);
    })

    this.rowData = formattedRows;

    if (refresh) {
      this.gridApi.setRowData(this.rowData);
      this.gridApi.setPinnedBottomRowData(this.pinnedBottomRowData);
    }
  }

  setGridOptions() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang === 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      domLayout: 'autoHeight',
      suppressPropertyNamesCheck: true,
      onRowClicked: (params) => this.onRowClick(params),
      onGridReady: (params) => this.onGridReady(params),
      getRowHeight: (params) => {
        const normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)))
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      getMainMenuItems:(params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlightsByMonth),
      defaultColDef: {
        resizable: true,
        sortable: true
      },
      rowData: this.rowData,
      pinnedBottomRowData: this.pinnedBottomRowData,
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsByMonth),
      },
      columnDefs: this.getColumnDefs()
    }
  }

  getColumnDefs() {
    let defs: ColDef[] = [
      { headerName: this.service.salesExecReview.sitesView ? 'Site' : 'Sales Exec', colId: 'Label', field: 'Label', width: 150, type: 'label' }
    ]
  
    let months: Months = this.rowData[0].Months;
    for (let month in months) {
      defs.push({
        headerName: this.cphPipe.transform(months[month].Month,'month', 0),
        colId: `Months.${month}.Score`,
        field: `Months.${month}.Score`,
        width: 80,
        type: 'percent',
        cellClass: 'ag-right-aligned-cell'
      })
    }

    return defs;
  }

  onRowClick(params): void {
    if (!this.service.salesExecReview.sitesView && (params.data.IsRegion || params.data.IsTotal)) return;


    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.salesExecReview.lastTableView = 'month';

    if (this.service.salesExecReview.sitesView) {
      let ids: number[];
      if (params.data.IsSite) ids = [params.data.Id];
      if (params.data.IsRegion) ids = this.constants.sitesActive.filter(x => x.RegionDescription == params.data.Label).map(x => x.SiteId);
      if (params.data.IsTotal) ids = this.constants.sitesActive.map(x => x.SiteId);

      this.service.salesExecReview.chosenSiteIds = ids;
      this.service.getPeopleByMonthRows();
    } else {
      this.service.getFormData(params.data.Id, params.data.Label);
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.mainTableGridOptions.context = { thisComponent: this }
    this.gridApi.sizeColumnsToFit();
  }

  excelExport() {
    const tableModel = this.gridApi.getModel();
    this.excel.createSheetObject(tableModel, 'Sales Exec Review - Sites', 1, 1);
  }

  backToSites() {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });

    if (this.service.salesExecReview.lastTableView == 'month') {
      this.service.salesExecReview.peopleView = false;
      this.service.salesExecReview.chosenSiteIds = this.constants.sitesActive.map(x => x.SiteId);
      this.service.salesExecReview.sitesView = true;
      this.refreshView();
    } else {
      this.service.salesExecReview.lastTableView = 'month';
      this.service.salesExecReview.byMeasure = true;
      this.selections.triggerSpinner.emit({ show: false });
    }
  }

  refreshView() {
    this.rowData = null;
    this.pinnedBottomRowData = null;
    this.formatRowData(true);
    this.selections.triggerSpinner.emit({ show: false });
  }

  private onresize(event) {
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.gridApi.sizeColumnsToFit();
    }
  }
}
