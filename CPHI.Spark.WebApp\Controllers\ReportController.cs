﻿using Microsoft.Data.SqlClient;
using CPHI.Spark.Model.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using System.Text.Json;
using CPHI.Spark.WebApp.Service;
using Microsoft.Exchange.WebServices.Data;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Controllers
{


    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ReportController : ControllerBase
    {
        private readonly IUserService userService;
        private readonly IReportService reportService;

        public ReportController(IUserService userService, IReportService reportService)
        {
            this.reportService = reportService;
            this.userService = userService;
            
        }

        [HttpGet]
        [Route("")]
        public IEnumerable<ReportCentreModel> GetAllReports()
        {
            ClaimsPrincipal cp = Request.HttpContext.User;

            string LinkedPersonId = cp.Claims.First(x => x.Type == "LinkedPersonId").Value;
            bool accessToReportCenter = cp.Claims.First(x => x.Type == "AccessReportCentre").Value == "true";

            //bool accessToReportCenter = true;

            var result = new List<ReportCentreModel>();

            if (accessToReportCenter)
            {
                string connString = Startup.Configuration.GetConnectionString(DealerGroupConnectionName.GetConnectionName(userService.GetUserDealerGroupName()));
                string query = @"SELECT * FROM ReportCentres ORDER BY Name ASC";

                SqlConnection conn = new SqlConnection(connString);
                SqlCommand cmd = new SqlCommand(query, conn);
                conn.Open();

                // create data adapter
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                // this will query your database and return the result to your datatable
                cmd.CommandTimeout = 90;
                using (SqlDataReader dr = cmd.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        string CreatedDate = dr["CreatedOn"]?.ToString();
                        DateTime? CreatedDateToUse;

                        if (string.IsNullOrWhiteSpace(CreatedDate))
                        {
                            CreatedDateToUse = null;
                        }
                        else
                        {
                            CreatedDateToUse = DateTime.Parse(CreatedDate);
                        }


                        result.Add(new ReportCentreModel()
                        {
                            Id = Int32.Parse(dr["Id"].ToString()),
                            //CreatedBy = dr["CreatedBy"].ToString(),
                            CreatedOn = CreatedDateToUse,
                            //ReportData = (dr["ReportData"].ToString()),
                            Name = (dr["Name"].ToString()),
                        });
                    }
                }
                //                
                conn.Close();
                da.Dispose();
            }

            return result;
        }

        [HttpGet]
        [Route("{reportId}")]
        public async Task<string> Get(int reportId)
        {

            ClaimsPrincipal cp = Request.HttpContext.User;
            string LinkedPersonId = cp.Claims.First(x => x.Type == "LinkedPersonId").Value;
            bool accessToReportCenter = cp.Claims.First(x => x.Type == "AccessReportCentre").Value == "true";


            if (accessToReportCenter)
            {
                Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
                string connString = Startup.Configuration.GetConnectionString(DealerGroupConnectionName.GetConnectionName(dealerGroup));
                string query = $"SELECT ReportData FROM ReportCentres WHERE Id = {reportId}";

                SqlConnection conn = new SqlConnection(connString);
                SqlCommand cmd = new SqlCommand(query, conn);
                conn.Open();

                var reportData = await cmd.ExecuteScalarAsync();

                conn.Close();

                await reportService.AddToLog(reportId, int.Parse(LinkedPersonId), dealerGroup);

                return JsonSerializer.Serialize(reportData.ToString());
            }
            else
            {
                return string.Empty;
            }
        }


        [Route("UploadReport")]
        [HttpPost]
        public void UploadReport(UploadReport report)
        {
            ClaimsPrincipal cp = Request.HttpContext.User;
            string LinkedPersonId = cp.Claims.First(x => x.Type == "LinkedPersonId").Value;
            bool accessToReportCenter = cp.Claims.First(x => x.Type == "AccessReportCentre").Value == "true";
            bool allowReportUpload = cp.Claims.First(x => x.Type == "AllowReportUpload").Value == "true";
            

            if (accessToReportCenter && allowReportUpload)
            {
                //Get the reportId and data
                var CreatedOn = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");

                string connString = Startup.Configuration.GetConnectionString(DealerGroupConnectionName.GetConnectionName(userService.GetUserDealerGroupName()));
                string query = $"UPDATE ReportCentres SET ReportData = '{report.ReportData}', CreatedBy = {LinkedPersonId}, CreatedOn = '{CreatedOn}' WHERE Id = {report.ReportId}";


                SqlConnection conn = new SqlConnection(connString);
                SqlCommand cmd = new SqlCommand(query, conn);
                conn.Open();

                cmd.ExecuteNonQuery();

                conn.Close();
            }
            else
            {
                throw new Exception("Access Denied");
            }

        }

    }

    public class UploadReport
    {
        public int ReportId { get; set; }
        public string ReportData { get; set; }
    }
}