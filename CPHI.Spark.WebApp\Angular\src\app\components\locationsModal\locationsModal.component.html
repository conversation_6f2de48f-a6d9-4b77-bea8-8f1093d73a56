<ng-template #dealInputModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            Choose Locations
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="d-flex justify-content-between">

            <ag-grid-angular
            class="ag-theme-balham"
            [gridOptions]="mainTableGridOptions"
            domLayout="autoHeight"
            (gridReady)="onGridReady($event)"
          >
          </ag-grid-angular>
           
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="addLocation()">
            Add Location
        </button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">
            {{ constantsService.translatedText.Close }}
        </button>
        <button type="button" class="btn btn-success" (click)="save()">
            Save
        </button>
    </div>
</ng-template>
