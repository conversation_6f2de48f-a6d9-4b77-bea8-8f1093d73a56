using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Quartz;
using Quartz.Impl;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using CPHI.Spark.Reporter.Jobs;
using CPHI.Spark.Reporter.Jobs.AutoPrice;
using System.Net.Http;
using CPHI.Spark.Reporter.Services;

namespace CPHI.Spark.Reporter
{
   public class SchedulerService : BackgroundService
   {
      private readonly ILogger<SchedulerService> _logger;
      private StdSchedulerFactory _schedulerFactory;
      private CancellationToken _stopppingToken;
      private IScheduler _scheduler;
      private IHttpClientFactory _httpClientFactory;


      public SchedulerService(ILogger<SchedulerService> logger, IHttpClientFactory httpClientFactory)
      {
         _logger = logger;
         _httpClientFactory = httpClientFactory;
      }

      protected override async Task ExecuteAsync(CancellationToken stoppingToken)
      {
         try
         {

            await StartJobs();
            _stopppingToken = stoppingToken;
            while (!stoppingToken.IsCancellationRequested)
            {
               // _logger.LogInformation("Worker running at: {time}", DateTimeOffset.Now);
               await Task.Delay(1000, stoppingToken);
            }
            await _scheduler.Shutdown();
         }
         catch
         {
            { }
         }

      }


      protected async Task StartJobs()
      {
         _schedulerFactory = new StdSchedulerFactory();
         _scheduler = await _schedulerFactory.GetScheduler();
         await _scheduler.Start();


         //if commission job is chosen in appsettings, we just run that now as a one-off
         if (ConfigService.RunSalesmanPdfsJobRRGNow) { await runOverrideJob(JobBuilder.Create<SalesmanStatement_RRG_Job>().Build()); }
         else if (ConfigService.RunSiteExcelsJobRRGNow) { await runOverrideJob(JobBuilder.Create<SiteStatement_RRG_Job>().Build()); }
         else if (ConfigService.RunLBDMExcelsJobRRGNow) { await runOverrideJob(JobBuilder.Create<LBDMExcels_RRG_Job>().Build()); }
         else if (ConfigService.RunLBDMPdfsJobRRGNow) { await runOverrideJob(JobBuilder.Create<LBDMPdfs_RRG_Job>().Build()); }
         else if (ConfigService.RunSalesmanPdfsJobVindisNow) { await runOverrideJob(JobBuilder.Create<SalesmanStatement_Vindis_Job>().Build()); }
         else if (ConfigService.RunSiteExcelsJobVindisNow) { await runOverrideJob(JobBuilder.Create<SiteStatement_Vindis_Job>().Build()); }
         else if (ConfigService.RunUsageJobNow) { await runOverrideJob(JobBuilder.Create<ReportUsageStatsJob>().Build()); }
         else if (ConfigService.RunGardXJobVindisPriorMonthNow) { await runOverrideJob(JobBuilder.Create<GardX_Vindis_Job>().WithIdentity("PriorMonth").Build()); }
         else if (ConfigService.RunGardXJobVindisCurrentMTDNow) { await runOverrideJob(JobBuilder.Create<GardX_Vindis_Job>().WithIdentity("CurrentMTD").Build()); }
         else if (ConfigService.RunFetchAutoTraderPricingNow) { await runOverrideJob(JobBuilder.Create<FetchAndGenerateAutoTraderPrices_Job>().Build()); }
         else if (ConfigService.RunFetchAutoTraderPricingSecondRunNow) { await runOverrideJob(JobBuilder.Create<FetchAndGenerateAutoTraderPricesSecondRun_Job>().Build()); }
         else if (ConfigService.RunUpdateWebsitePricesNow) { await runOverrideJob(JobBuilder.Create<UpdateAutoTraderPrices_Job>().Build()); }
         //else if (ConfigService.RunUpdateWebsitePrices4PMNow) { await runOverrideJob(JobBuilder.Create<UpdateAutoTraderPrices4PM_Job>().Build()); }
         //else if (ConfigService.RunUpdateWebsitePricesOnDemandNow) { await runOverrideJob(JobBuilder.Create<UpdateAutoTraderPricesOnDemand>().Build()); }
         else if (ConfigService.RunVehicleValuationNow) { await runOverrideJob(JobBuilder.Create<VehicleValuation_Job>().Build()); }
         else if (ConfigService.RunVehicleValuationCheckerNow) { await runOverrideJob(JobBuilder.Create<VehicleValuationChecker_Job>().Build()); }
         else if (ConfigService.RunLocalBargainNow) { await runOverrideJob(JobBuilder.Create<LocalBargain_Job>().Build()); }
         else if (ConfigService.RunDerivativeDownloadNow) { await runOverrideJob(JobBuilder.Create<DerivativesDownload_Job>().Build()); }
         else if (ConfigService.RunTemporaryJobNow) { await runOverrideJob(JobBuilder.Create<TemporaryJob>().Build()); }
         else if (ConfigService.RunEMGMantlesStockJobNow) { await runOverrideJob(JobBuilder.Create<EMGMantlesStock_Job>().Build()); }
         else
         {
            //not an instant run report, so go ahead and schedule regular jobs
            await ScheduleRegularJobs();
         }

      }

      private async Task runOverrideJob(IJobDetail job)
      {
         int startH = DateTime.Now.AddSeconds(2).Hour;
         int startM = DateTime.Now.AddSeconds(2).Minute;
         int startS = DateTime.Now.AddSeconds(2).Second;

         ITrigger nowTrigger = TriggerBuilder.Create()
              .WithDailyTimeIntervalSchedule(b =>
                b.StartingDailyAt(new TimeOfDay(startH, startM, startS)
                ).EndingDailyAfterCount(1).OnEveryDay()
                ).Build();


         await _scheduler.ScheduleJob(job, nowTrigger, _stopppingToken);
      }



      private async Task ScheduleRegularJobs()
      {

         //------------------
         //JOBS
         //------------------

         //----------------------------------------------------------------------
         //Vindis jobs = we run these when we deploy onto Vindis VM
         //----------------------------------------------------------------------

         //usage reports
         IJobDetail usageReportJob = JobBuilder.Create<ReportUsageStatsJob>().Build();
         await scheduleJob(usageReportJob, ConfigService.ReportUsageStatsJobCronSchedule);
         //gardX reports
         IJobDetail gardXReportJobPriorMonth = JobBuilder.Create<GardX_Vindis_Job>().WithIdentity("PriorMonth").Build();
         await scheduleJob(gardXReportJobPriorMonth, ConfigService.GardXJobCronSchedulePriorMonth);
         IJobDetail gardXReportJobCurrentMTD = JobBuilder.Create<GardX_Vindis_Job>().WithIdentity("CurrentMTD").Build();
         await scheduleJob(gardXReportJobCurrentMTD, ConfigService.GardXJobCronScheduleCurrentMonthToDate);




         //----------------------------------------------------------------------
         //AutoPrice jobs - we run these on RRG VM
         //----------------------------------------------------------------------

         //Fetch and Generate
         IJobDetail fetchAutoTraderPricing_Job = JobBuilder.Create<FetchAndGenerateAutoTraderPrices_Job>().Build();
         await scheduleJob(fetchAutoTraderPricing_Job, ConfigService.FetchAutoTraderPricingJobCronSchedule);
         //Update prices
         IJobDetail updateAutotraderPrices_Job = JobBuilder.Create<UpdateAutoTraderPrices_Job>().Build();
         await scheduleJob(updateAutotraderPrices_Job, ConfigService.UpdateAutoTraderPricesJobCronSchedule);
         //Vehicle Valuation
         IJobDetail vehicleValuation_Job = JobBuilder.Create<VehicleValuation_Job>().Build();
         await scheduleJob(vehicleValuation_Job, ConfigService.VehicleValuationJobCronSchedule);
         //Vehicle Valuation Checker
         IJobDetail vehicleValuationChecker_Job = JobBuilder.Create<VehicleValuationChecker_Job>().Build();
         await scheduleJob(vehicleValuationChecker_Job, ConfigService.VehicleValuationCheckerJobCronSchedule);
         //Local Bargains
         IJobDetail localBargain_Job = JobBuilder.Create<LocalBargain_Job>().Build();
         await scheduleJob(localBargain_Job, ConfigService.LocalBargainJobCronSchedule);
         //EMG Mantles Stock
         IJobDetail emgMantlesStock_Job = JobBuilder.Create<EMGMantlesStock_Job>().Build();
         await scheduleJob(emgMantlesStock_Job, ConfigService.EMGMantlesStockJobCronSchedule);


         //Second run of the morning, for Enterprise
         IJobDetail fetchAutoTraderPricingSecondRun_Job = JobBuilder.Create<FetchAndGenerateAutoTraderPricesSecondRun_Job>().Build();
         await scheduleJob(fetchAutoTraderPricingSecondRun_Job, ConfigService.FetchAutoTraderPricingSecondRunJobCronSchedule);

      }





      private async Task scheduleJob(IJobDetail job, string requestedSchedule)
      {
         try
         {

            if (requestedSchedule == "DO NOT RUN") { return; }

            //if we want to run regularly
            if (requestedSchedule.StartsWith("REPEAT EVERY"))
            {
               string cronSchedule = "0 10 * ? * * *"; //set to 10mins by default, should never use this
               if (requestedSchedule.Contains("MINUTES"))
               {
                  int minutes = int.Parse(new string(requestedSchedule.Where(char.IsDigit).ToArray()));
                  cronSchedule = $"0 0/{minutes} * ? * * *";
               }
               else if (requestedSchedule.Contains("SECONDS"))
               {
                  int seconds = int.Parse(new string(requestedSchedule.Where(char.IsDigit).ToArray()));
                  cronSchedule = $"0/{seconds} * * ? * * *";
               }
               await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(cronSchedule).Build(), _stopppingToken);
               return;
            }

            //if we chose a certain time
            if (requestedSchedule.StartsWith("RUN AT"))
            {
               string timeChosen = requestedSchedule.Substring(7, requestedSchedule.Length - 7);
               string hours = timeChosen.Split(':')[0];
               string minutes = timeChosen.Split(':')[1];
               string cronSchedule = $"0 {minutes} {hours} ? * * *";
               await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(cronSchedule).Build(), _stopppingToken);
               return;
            }

            if (requestedSchedule.StartsWith("CUSTOM "))
            {
               await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(requestedSchedule.Replace("CUSTOM ", "")).Build(), _stopppingToken);
               return;
            }

         }
         catch (Exception ex)
         {
            { }
         }
      }



   }


}
