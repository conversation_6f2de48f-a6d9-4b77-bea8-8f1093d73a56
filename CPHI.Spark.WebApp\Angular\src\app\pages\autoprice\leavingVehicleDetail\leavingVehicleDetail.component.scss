#gridHolder {
    height: 100%;
    width: 100%;
    position: relative;
    background-color: #F5F7F7;
    display: flex;
    flex-direction: column;
}

/* Date picker styles */
input[type="date"] {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    text-align: center;
    width: 100px;
}

input[type="date"]:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

input[type="date"]:disabled {
    background-color: #e9ecef;
    opacity: 1;
    cursor: not-allowed;
}
