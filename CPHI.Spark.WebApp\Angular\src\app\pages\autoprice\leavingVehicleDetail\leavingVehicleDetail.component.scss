#gridHolder {
    height: 100%;
    width: 100%;
    position: relative;
    background-color: #F5F7F7;
    display: flex;
    flex-direction: column;
}

// .searchBox {
//     margin-left: 2em;
//     width: 15em;
//     display: flex;
//     align-items: center;

//     svg.fa-magnifying-glass {
//         position: absolute;
//         transform: translate(1.3em);
//         color: gray;
//         z-index: 10;
//     }

//     form {
//         display: flex;
//         position: relative;

//         input {
//             height: 2em;
//             padding-left: 3em;
//         }

//         #searchBarClearButton {
//             right: 1em;
//             position: absolute;
//             color: var(--brightColourDark);
//             z-index: 100;
//             cursor: pointer;
//             top: 4px;
//         }
//     }
// }