.content-inner-new {
    display: flex;
    flex-direction: column;

    #gridHolder {
        display: flex;
        flex-direction: column;
        flex: 1;
        background-color: #F5F7F7;

        ag-grid-angular {
            flex: 1;
        }
    }
}


:host ::ng-deep .veryOverUnder {
    background: var(--spVeryBelow);
}


:host ::ng-deep .ag-cell.overUnder {
    background: var(--spBelow);
}


:host ::ng-deep .ag-cell.onStrategyPrice {
    background: var(--spOnPrice);
}