import { EventEmitter, Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe } from 'src/app/cph.pipe';

import { Router } from '@angular/router';
import { GridApi } from 'ag-grid-community';
import { GlobalParamKey } from 'src/app/model/GlobalParam';
import { GetStatsDashboardParams } from 'src/app/model/GetStatsDashboardParams';
import { MenuItemNew } from 'src/app/model/main.model';
import { StatsDashboard, StatsDashboardDTO } from 'src/app/model/StatsDashboard';
import { StatsDashboardFiltersForStockReport } from 'src/app/model/StatsDashboardFiltersForStockReport';
import { StatsVehicle } from 'src/app/model/StatsVehicle';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { GlobalParamsService } from 'src/app/services/globalParams.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { AdvertListingDetailService } from '../advertListingDetail/advertListingDetail.service';
import { StatsDashboardComponent, StatsDashboardPageComponentType } from './statsDashboard.component';
import { StatsDashboardTableComponent, StatsDashboardTableParams } from './statsDashboardTable/statsDashboardTable.component';

@Injectable({
  providedIn: 'root'
})
export class StatsDashboardService implements StatsDashboardTableParams {

  chosenSiteNames: Set<string> = new Set<string>();
  chosenRegionNames: Set<string> = new Set<string>();

  serviceHasBeenInitialised: boolean = false;

  useTestStrategy: boolean = false;
  showTestStrategySlider: boolean = false;


  //references to components
  gridRef: StatsDashboardTableComponent;
  pageRef: StatsDashboardComponent;


  newSmallChartDataEmitter: EventEmitter<any> = new EventEmitter();


  filters: StatsDashboardFiltersForStockReport;

  filterModel: any = {};

  includeUnPublishedAds: boolean = false;
  includeNewVehicles: boolean = false;
  includeLCVs: boolean = true;

  // Key for the ShowLCVToggle GlobalParam
  showLCVToggleKey: GlobalParamKey = GlobalParamKey.ShowLCVToggle;
  showLCVToggle: boolean = false;


  allLifecycleStatuses: string [] = [
    "DUE_IN",
    "FORECOURT",
    "IN STOCK NOT ON PORTAL",
    "SALE_IN_PROGRESS"
  ];

  chosenLifecycleStatuses: Set<string> = new Set();

  get vehicles() { return this.pageRef?.statsDashboard.Vehicles }
  gridApi: GridApi;

  constructor(
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public modalService: NgbModal,
    public getDataMethodsService: GetDataMethodsService,
    public cph: CphPipe,
    public router: Router,
    public advertListingDetailService: AdvertListingDetailService,
    public globalParamsService: GlobalParamsService
  ) {


  }


  get siteNames(): string[] {
    const chosenSitesForRegions = this.constantsService.Sites.filter(x => Array.from(this.chosenRegionNames).includes(x.RegionDescription)).map(x => x.SiteId);
    return this.constantsService.RetailerSites.filter(x => x.IsActive && chosenSitesForRegions.includes(x.Site_Id)).map(x => x.Name);
  };

  get regionNames(): string[] {
    return Array.from(new Set(this.constantsService.Sites.filter(x => x.IsActive).map(x => x.RegionDescription)));
  };



  initParams(): void {


    if (!this.serviceHasBeenInitialised) {
      this.chosenSiteNames = new Set<string>(this.constantsService.RetailerSites.filter(x => x.IsActive).map(x=>x.Name));
      this.chosenRegionNames = new Set<string>(this.constantsService.Sites.filter(x => x.IsActive).map(x => x.RegionDescription));
      this.chosenLifecycleStatuses = new Set<string>(this.allLifecycleStatuses);

      // Initialize includeLCVs based on GlobalParam
      const showLCVToggle = this.globalParamsService.getGlobalParam(this.showLCVToggleKey);

      if (showLCVToggle !== undefined) {
        this.showLCVToggle = showLCVToggle as boolean;
      }

      this.serviceHasBeenInitialised = true;
      this.initialiseFilters();
    }

  }
  async getData() {
    try {
      const parms: GetStatsDashboardParams = {
        RetailerSiteIds: this.constantsService.RetailerSites.filter(x => this.chosenSiteNames.has(x.Name)).map(x => x.Id),
        UseTestStrategy: this.useTestStrategy,
        IncludeNewVehicles: this.includeNewVehicles,
        IncludeUnPublishedAdverts: this.includeUnPublishedAds,
        LifecycleStatuses: Array.from(this.chosenLifecycleStatuses),
        IncludeLCVs: this.includeLCVs
      };
      const res: StatsDashboardDTO = await this.getDataMethodsService.getStatsDashboard(parms);
      const statsDashboard: StatsDashboard = new StatsDashboard(res);

      this.dealWithNewData(statsDashboard);
    } catch (error) {
      this.constantsService.toastDanger("Error fetching data");
    }
  }


  //do any triggering of the various components
  dealWithNewData(data: StatsDashboard) {

    this.pageRef.dealWithNewData(data);


  }

  dealWithFilteredItems(filteredItems: StatsVehicle[], callingComponent: StatsDashboardPageComponentType) {
    //todo
  }



  initialiseFilters() {
    this.filters = {
      PerformanceRating: false,
      RetailRating: false,
      DaysListed: false,
      PricePosition: false,
      IsLowQuality: false,
      NoAttentionGrabber: false,
      LessThan9Images: false,
      NoImages: false,
      NoVideo: false,
      VsStrategyBanding: false
    }
  }

  getDashboardParams() {
    return {
      parentMethods: {
        filterTable: (fieldName, dataType) => this.filterTableFromChartClick(fieldName, dataType)
      }
    }
  }

  enableFilter(field: string) {
    this.filters[field] = true;
    this.filter(field);
  }

  filterTableFromChartClick(label: any, dataType: any) {
    if (dataType === 'advertsByRetailRating') {
      this.filterForRetailRating(label);
    } else if (dataType === 'advertsByDaysListed') {
      this.filterForDaysListed(label);
    } else if (dataType === 'vsStrategyPrice') {
      this.filterForStrategyPrice(label);
    } else if (dataType === 'performanceRating') {
      this.filterForPerformanceRating(label);
    }
  }

  filterForRetailRating(label: string) {
    let filterRange: number[];

    switch (label) {
      case '<20':
        filterRange = [0, 19];
        break;
      case '20 to 39':
        filterRange = [20, 39];
        break;
      case '40 to 59':
        filterRange = [40, 59];
        break;
      case '60 to 79':
        filterRange = [60, 79];
        break;
      default:
        filterRange = [80];
        break;
    }

    this.filters.RetailRating = true;
    this.filter('RetailRating', filterRange);
  }

  filterForDaysListed(label: string) {
    let filterRange: number[];

    switch (label) {
      case '<20':
        filterRange = [0, 19];
        break;
      case '<40':
        filterRange = [20, 39];
        break;
      case '<60':
        filterRange = [40, 59];
        break;
      default:
        filterRange = [60];
        break;
    }

    this.filters.DaysListed = true;
    this.filter('DaysListed', filterRange);
  }

  filterForStrategyPrice(label: string) {
    this.filters.VsStrategyBanding = true;
    this.filter('VsStrategyBanding', null, label);
  }

  filterForPerformanceRating(label: string) {
    let filterRange: number[];

    switch (label) {
      case 'Low':
        filterRange = [1, 24];
        break;
      case '< Average':
        filterRange = [25, 49];
        break;
      case '> Average':
        filterRange = [50, 74];
        break;
      case 'Excellent':
        filterRange = [75];
        break;
      default:
        filterRange = [0, 0];
        break;
    }

    this.filters.PerformanceRating = true;
    this.filter('PerformanceRating', filterRange);
  }

  filter(field: string, filterRange?: number[], setFilterValue?: string) {
    if (filterRange) {
      // Number filter
      if (filterRange.length > 1) {
        this.filterModel[field] = {
          filterType: 'number',
          operator: 'AND',
          condition1: {
            type: 'greaterThanOrEqual',
            filter: filterRange[0]
          },
          condition2: {
            type: 'lessThanOrEqual',
            filter: filterRange[1]
          }
        }
      } else {
        this.filterModel[field] = {
          filterType: 'number',
          type: 'greaterThanOrEqual',
          filter: filterRange[0]
        }
      }
    } else {
      // Set filter
      this.filterModel[field] = {
        filterType: 'set',
        values: setFilterValue ? [setFilterValue] : ['true']
      }
    }

    this.gridApi.setFilterModel(this.filterModel);
  }

  clearFilter(fields: string[]) {
    fields.forEach(field => {
      this.filters[field] = false;
      delete this.filterModel[field];
    })

    this.gridApi.setFilterModel(this.filterModel);
  }

  clearAllFilters() {
    this.initialiseFilters();
    this.filterModel = {};
    this.gridApi.setFilterModel(this.filterModel);
  }

  goToStockReports() {
    this.advertListingDetailService.setExternalFilterModel(this.filterModel);
    let menuItem: MenuItemNew | undefined = this.constantsService.getMenuItemFromUrl('/stockReports');
    if (menuItem) { this.constantsService.navigateByUrl(menuItem); } //, 'operationreports'
  }
}
