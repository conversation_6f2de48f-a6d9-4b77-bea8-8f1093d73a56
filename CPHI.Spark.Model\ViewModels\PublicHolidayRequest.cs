using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace CPHI.Spark.Model.ViewModels
{
    public class PublicHolidayRequest
    {
        [Required]
        public DateTime Date { get; set; }

        [Required]
        public int DealerGroupId { get; set; }

        [Required]
        public List<int> RegionIds { get; set; }
    }

    public class PublicHolidayResponse
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public int DealerGroupId { get; set; }
        public List<int> RegionIds { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; }
    }
}
