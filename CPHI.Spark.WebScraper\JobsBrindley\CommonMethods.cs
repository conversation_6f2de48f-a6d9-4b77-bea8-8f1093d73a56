﻿using CPHI.Spark.Model.ViewModels;
using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.IE;
using OpenQA.Selenium.Interactions;
using OpenQA.Selenium.Support.UI;
using SeleniumExtras.WaitHelpers;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;

namespace CPHI.Spark.WebScraper.Jobs
{
    public  class CommonMethods
    {
        private readonly InternetExplorerDriver _driver;
        private readonly ILog logger;
        
        
        public CommonMethods(InternetExplorerDriver driver, ILog loggerIn)
        {
            this._driver = driver;
            logger = loggerIn;
        }

        public  void Sleep(int ms)
        {
            logger.Info($"Sleeping for {ms}ms");
            Thread.Sleep(ms);
        }

        public  void ClickButtonById(string id)
        {
            _driver.Manage().Timeouts().AsynchronousJavaScript = TimeSpan.FromSeconds(1);
            logger.Info($"Attempting to click button with: {id}");

            try
            {
                // Use XPath to directly find the element with the specified ID
                IWebElement button = _driver.FindElement(By.XPath($"//*[@id='{id}']"));


                if (button != null)
                {
                    IJavaScriptExecutor jsExecutor = (IJavaScriptExecutor)_driver;
                    jsExecutor.ExecuteScript("arguments[0].click();", button);
                }
            }
            catch (Exception ex)
            {
                // IdentifyWindows();
            }
            finally
            {
                _driver.Manage().Timeouts().AsynchronousJavaScript = TimeSpan.FromSeconds(30);
            }
        }

        public  IWebElement WaitAndFindElement(By by,  int waitPeriodSeconds = 10)
        {
            try
            {
                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(waitPeriodSeconds));
                IWebElement el = wait.Until(ExpectedConditions.ElementExists(by));
                return el;
            }
            catch (Exception ex)
            {
                return null;
            }
        }



        public  void IdentifyWindowsNew()
        {
            // Firstly clear up old ones
            CleanUpOldIdentifiedWindows("window");

            var windows = _driver.WindowHandles;
            int winIndex = 0;

            foreach (var handle in windows)
            {
                try
                {
                    _driver.SwitchTo().Window(handle);

                    //Create file of the window source
                    string pageSource = _driver.PageSource;
                    string fileName = $"window {winIndex}";
                    File.WriteAllText($@"C:\cphiroot\{fileName}.html", pageSource);

                    LogFrames(winIndex, fileName);
                }
                catch (Exception ex)
                {
                    logger.Error($"Error switching to window {winIndex}: {ex.Message}");
                }
                winIndex++;
            }
        }

        private  void LogFrames(int windowIndex, string fileNameIn)
        {
            try
            {
                // Check for nested frames within the current frame
                var frames = _driver.FindElements(By.TagName("iframe"));
                int frameIndex = 0;
                string fileName = fileNameIn;
                foreach (var frame in frames)
                {
                    try
                    {
                        fileName = fileNameIn + $" frame {frameIndex}";

                        _driver.SwitchTo().Frame(frame); // Switch to each inner frame
                        string frameSource = _driver.FindElement(By.TagName("body")).GetAttribute("innerHTML");
                        File.WriteAllText($@"C:\cphiroot\{fileName}.html", frameSource);


                        string frameSourceOuter = _driver.FindElement(By.TagName("html")).GetAttribute("outerHTML");
                        File.WriteAllText($@"C:\cphiroot\{fileName}OUTER.html", frameSourceOuter);

                        LogFrames(windowIndex, fileName);
                        frameIndex++;
                        _driver.SwitchTo().ParentFrame(); // Switch back to the parent frame
                    }
                    catch (Exception ex)
                    {
                        logger.Error($"Error switching to inner frame {frameIndex} whilst at {fileName}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Error logging frame whilst at {fileNameIn}: {ex.Message}");
            }
        }


        private  void CleanUpOldIdentifiedWindows(string prefix)
        {
            string folderPath = @"c:\cphiroot";
            string filePattern = $"{prefix}*.html";

            try
            {
                // Get all files that match the pattern
                string[] files = Directory.GetFiles(folderPath, filePattern);

                foreach (string file in files)
                {
                    try
                    {
                        // Delete the file
                        File.Delete(file);
                    }
                    catch (Exception ex)
                    {
                        // Log any exceptions that occur during deletion
                        logger.Error($"Error deleting file {file}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log any exceptions that occur during file search
                logger.Error($"Error accessing folder {folderPath}: {ex.Message}");
            }
        }


        public  void IdentifyDriverFocusContent( )
        {
            string currentSource = _driver.FindElement(By.TagName("body")).GetAttribute("innerHTML");
            string filePath = $@"C:\cphiroot\currentDriverLocation.html";
            File.WriteAllText(filePath, currentSource);
        }

        public  void SwitchWindowAndFrame(int winIndex, int frameIndex, string fallBackXPath, int? subFrameIndex = null, int? subSubFrameIndex = null, bool skipBrowserCheck = false)
        {
            try
            {


                //fast switch
                SwitchToWindowNew(winIndex);
                SwitchToFrameNew(frameIndex);
                if (subFrameIndex != null)
                {
                    SwitchToFrameNew((int)subFrameIndex);
                    logger.Info($"Switched to window {winIndex}, frame {frameIndex}, subFrame {subFrameIndex}");
                }
                if (subSubFrameIndex != null)
                {
                    SwitchToFrameNew((int)subSubFrameIndex);
                    logger.Info($"Switched to window {winIndex}, frame {frameIndex}, subFrame {subFrameIndex} subSubFrame {subSubFrameIndex}");
                }
            }
            catch (Exception ex)
            {
                //fallback
                string branchSelectionXPath = fallBackXPath;
                var element = NavigateAndReturnElementThroughWindowsAndFrames(By.XPath(branchSelectionXPath));
                logger.Info($"Switched to fallBackXPath {fallBackXPath}");

            }
        }

        public  void SwitchToWindowNew(int windowIndex)
        {
            try
            {
                //logger.Info($"Switching to window {windowIndex}");

                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                wait.Until(_driver =>
                {
                    ReadOnlyCollection<string> windows = _driver.WindowHandles;
                    return windows.Count > windowIndex;
                });

                ReadOnlyCollection<string> allWindows = _driver.WindowHandles;
                if (allWindows.Count > windowIndex)
                {
                    _driver.SwitchTo().Window(allWindows[windowIndex]);
                    //logger.Info($"Switched to window {windowIndex}");
                }
                else
                {
                    logger.Error($"Window index {windowIndex} out of range. Total windows: {allWindows.Count}");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Error switching to window {windowIndex}: {ex.Message}");
            }
        }

        public  void SwitchToFrameNew(int frameIndex)
        {
            try
            {
                //logger.Info($"Switching to frame {frameIndex}");

                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                wait.Until(_driver =>
                {
                    IList<IWebElement> allFrames = _driver.FindElements(By.XPath("//iframe"));
                    return allFrames.Count > frameIndex;
                });

                IList<IWebElement> frames = _driver.FindElements(By.XPath("//iframe"));
                if (frames.Count > frameIndex)
                {
                    _driver.SwitchTo().Frame(frames[frameIndex]);
                    //logger.Info($"Switched to frame {frameIndex}");
                }
                else
                {
                    logger.Error($"Frame index {frameIndex} out of range. Total frames: {frames.Count}");
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Error switching to frame {frameIndex}: {ex.Message}");
            }
        }


        public  bool IsBrowserResponsive()
        {
            try
            {
                _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(1);
                _driver.FindElement(By.TagName("body"));
                return true;
            }
            catch (Exception)
            {
                return false;
            }
            finally
            {
                _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(30); // Reset to original timeout
            }


        }

        public  void ClickElement(IWebElement el )
        {
            try
            {

                el.Click();
            }
            catch
            {
                try
                {
                    IJavaScriptExecutor ex = (IJavaScriptExecutor)_driver;
                    ex.ExecuteScript("arguments[0].click();", el);
                }
                catch
                {
                    { }
                }
            }
        }

        public  IWebElement NavigateAndReturnElementThroughWindowsAndFrames(By by)
        {
            var windows = _driver.WindowHandles;
            IWebElement element = null;
            int windowIndex = windows.Count() - 1;
            while (element == null && windowIndex >= 0)
            {
                var message = $"window {windowIndex}";
                ///Go to the window
                string currentWindow = windows[windowIndex];
                _driver.SwitchTo().Window(currentWindow);

                try
                {
                    var matches = _driver.FindElements(by);

                    //element = _driver.FindElement(by); ///search within the inner frame
                    if (matches.Count() > 0)
                    {
                        logger.Info($"Slow find success at {message}");
                        return element;
                    }
                    else
                    {
                        var framesMatch = SearchThroughFramesForElementForwards(by, message);
                        if (framesMatch != null)
                        {
                            //logger.Info($"Success at {message}");
                            return framesMatch;
                        }
                    }
                }
                catch (Exception ex)
                {
                    //logger.Info($"Did not find {CompilePathMessage(by, windowIndex)}");

                    var framesMatch = SearchThroughFramesForElementForwards(by, message);
                    if (framesMatch != null)
                    {
                        //logger.Info($"Success at {message}");
                        return framesMatch;
                    }
                }
                windowIndex--;
            }

            return null;

        }


        private  IWebElement SearchThroughFramesForElementBackwards(By by, string messageIn )
        {
            IList<IWebElement> frames = _driver.FindElements(By.TagName("iframe"));
            IWebElement element = null;
            int frameIndex = frames.Count() - 1;
            string message = messageIn;
            while (element == null && frameIndex >= 0)
            {
                message = messageIn + $" frame {frameIndex}";
                _driver.SwitchTo().Frame(frames[frameIndex]);

                try
                {
                    var elements = _driver.FindElements(by);
                    if (elements.Count() > 0)
                    {
                        //message = messageIn + $" frame {frameIndex}";
                        logger.Info($"Slow find success at {message}");
                        return elements.First();
                    }
                    else
                    {
                        element = SearchThroughFramesForElementBackwards(by, message);
                        if (element != null)
                        {
                            //message = message +$"frame {frameIndex} subFrame {frameIndex}";
                            //logger.Info($"Success at {message}");
                            return element;
                        }
                        logger.Info($"Did not find at {message}");
                        _driver.SwitchTo().ParentFrame();

                    }
                    //element = _driver.FindElement(by);
                }
                catch (NoSuchElementException)
                {
                }
                finally
                {

                }
                frameIndex--;
            }

            return null;
        }

        private  IWebElement SearchThroughFramesForElementForwards(By by, string messageIn)
        {
            IList<IWebElement> frames = _driver.FindElements(By.TagName("iframe"));
            IWebElement element = null;
            int frameIndex = 0;// frames.Count() - 1;
            string message = messageIn;
            while (element == null && frameIndex < frames.Count())
            {
                message = messageIn + $" frame {frameIndex}";
                _driver.SwitchTo().Frame(frames[frameIndex]);

                try
                {
                    var elements = _driver.FindElements(by);
                    if (elements.Count() > 0)
                    {
                        //message = messageIn + $" frame {frameIndex}";
                        logger.Info($"Slow find success at {message}");
                        return elements.First();
                    }
                    else
                    {
                        element = SearchThroughFramesForElementForwards(by, message);
                        if (element != null)
                        {
                            //message = message +$"frame {frameIndex} subFrame {frameIndex}";
                            //logger.Info($"Success at {message}");
                            return element;
                        }
                        logger.Info($"Did not find at {message}");
                        _driver.SwitchTo().ParentFrame();

                    }
                    //element = _driver.FindElement(by);
                }
                catch (NoSuchElementException)
                {
                }
                finally
                {

                }
                frameIndex++;
            }

            return null;
        }
    }




}
