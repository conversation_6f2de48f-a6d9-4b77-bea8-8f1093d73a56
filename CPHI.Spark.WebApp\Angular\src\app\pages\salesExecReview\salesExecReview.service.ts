import { EventEmitter, Injectable } from "@angular/core";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { GetSEReviewFormParams, PeopleSummaryByMeasure, PeopleSummaryByMonth, RefreshSEReviewFormResult, SaveSEReviewFormParams, SitesByMonthByRegion, SiteSummaryByMeasure, SiteSummaryByMonth, UpdateSEReviewFormApprovalStateParams } from "./salesExecReview.model";
import { NgbModal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import { ExecManagerMappingComponent } from "./execManagerMapping/execManagerMapping.component";
import { TopBottomHighlightRule } from "src/app/model/TopBottomHighlightRule";
import { SalesExecReviewForm } from "./form/SalesExecReviewForm";
import { SalesExecReviewInit } from "./SalesExecReviewInit";

@Injectable({
  providedIn: 'root'
})

export class SalesExecReviewService {
  salesExecReview: SalesExecReviewInit;
  topBottomHighlightsByMeasure:TopBottomHighlightRule[]=[]
  topBottomHighlightsByMonth:TopBottomHighlightRule[]=[]

  constructor(
    public constants: ConstantsService,
    public getData: GetDataMethodsService,
    public modalService: NgbModal,
    public selections: SelectionsService
  ) { }

  initParams() {
    if (!this.salesExecReview) {
      this.salesExecReview = {
        byMeasure: true,
        byMeasureDataChangeEmitter: new EventEmitter<boolean>(),
        byMonthDataChangeEmitter: new EventEmitter<boolean>(),
        chosenMeasure: null,
        chosenMonth: this.constants.thisMonthStart,
        chosenPersonId: null,
        chosenSalesmanName: null,
        chosenSiteIds: this.selections.user.permissions.salesExecReview == 'reviewer' ? null : [this.selections.user.site.SiteId],
        chosenSiteName: null,
        form: null,
        formClone:null,
        lockedPercentageThresholds: {
          good: 1,
          bad: 0.99
        },
        overallScoreThresholds: {
          good: 1,
          bad: 0.85
        },
        peopleByMeasureRows: null,
        peopleByMonthRows: null,
        peopleView: false,
        getFormEmitter: new EventEmitter<boolean>(),
        refreshFormEmitter: new EventEmitter<GetSEReviewFormParams>(),
        formRefreshedEmitter: new EventEmitter<boolean>(),
        saveFormEmitter: new EventEmitter<{ SaveSEReviewFormParams: SaveSEReviewFormParams, thenUpdateApprovalState?: boolean }>(),
        sitesByMonthByRegion: null,
        siteByMeasureRows: null,
        siteByMonthRows: null,
        sitesView: false,
        showExecManagerMappings: false,
        updateFormApprovalStateEmitter: new EventEmitter<UpdateSEReviewFormApprovalStateParams>(),
        newFormIdEmitter: new EventEmitter<number>(),
        lastTableView: null,
        approvalStateUpdatedEmitter: new EventEmitter<boolean>(),
        showNoFormMessage: false
      }

      if (this.selections.user.permissions.salesExecReview == 'reviewer') {
        this.getSiteByMeasureRows();
      } else {
        this.getFormData(this.selections.user.PersonId);
      }
    } else {
      this.selections.triggerSpinner.emit({ show: false });
    }
  }

  getSiteByMeasureRows(refresh?: boolean) {
    this.getData.getSEReviewSiteSummaryByMeasure(this.salesExecReview.chosenMonth).subscribe((res: { Results: SiteSummaryByMeasure[] }) => {
      this.salesExecReview.siteByMeasureRows = res.Results;
    }, e => {
      console.error('Error retrieving sales exec performance sites by measure rows: ' + JSON.stringify(e));
    }, () => {
      this.salesExecReview.peopleView = false;
      this.salesExecReview.sitesView = true;
      this.salesExecReview.byMeasure = true;
      if (refresh) this.salesExecReview.byMeasureDataChangeEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getSiteByMonthRows() {
    this.getData.getSEReviewSiteSummaryByMonth(this.salesExecReview.chosenMeasure).subscribe((res: { Results: SiteSummaryByMonth[] }) => {
      this.salesExecReview.siteByMonthRows = res.Results;
      this.splitSiteByMonthByRegion();
    }, e => {
      console.error('Error retrieving sales exec performance sites by month rows: ' + JSON.stringify(e));
    }, () => {
      this.salesExecReview.peopleView = false;
      this.salesExecReview.sitesView = true;
      this.salesExecReview.byMeasure = false;
      this.salesExecReview.byMonthDataChangeEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getPeopleByMeasureRows(refresh?: boolean, siteName?: string) {
    this.getData.getSEReviewPeopleSummaryByMeasure(this.salesExecReview.chosenMonth, this.salesExecReview.chosenSiteIds).subscribe((res: { Results: PeopleSummaryByMeasure[] }) => {
      this.salesExecReview.peopleByMeasureRows = res.Results;
    }, e => {
      console.error('Error retrieving sales exec performance sites by measure rows: ' + JSON.stringify(e));
    }, () => {
      this.salesExecReview.peopleView = true;
      this.salesExecReview.sitesView = false;
      this.salesExecReview.byMeasure = true;
      if (siteName) this.salesExecReview.chosenSiteName = siteName;
      if (refresh) this.salesExecReview.byMeasureDataChangeEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getPeopleByMonthRows() {
    this.getData.getSEReviewPeopleSummaryByMonth(this.salesExecReview.chosenMeasure, this.salesExecReview.chosenSiteIds).subscribe((res: { Results: PeopleSummaryByMonth[] }) => {
      this.salesExecReview.peopleByMonthRows = res.Results;
    }, e => {
      console.error('Error retrieving sales exec performance sites by month rows: ' + JSON.stringify(e));
    }, () => {
      this.salesExecReview.peopleView = true;
      this.salesExecReview.sitesView = false;
      this.salesExecReview.byMeasure = false;
      this.salesExecReview.byMonthDataChangeEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getFormData(salesmanId: number, salesmanName?: string, refresh?: boolean) {
    this.salesExecReview.chosenPersonId = salesmanId;
    
    let params: GetSEReviewFormParams = {
      Month: this.salesExecReview.chosenMonth,
      SalesmanId: salesmanId,
      SiteId: this.salesExecReview.chosenSiteIds[0]
    }

    this.getData.getSEReviewForm(params).subscribe((res: SalesExecReviewForm) => {
      console.log(res);
      this.salesExecReview.form = new SalesExecReviewForm(res);// Object.assign(new SalesExecReviewForm(), res);
      console.log(this.salesExecReview.form);
    }, e => {
      this.salesExecReview.showNoFormMessage = true;
      console.error('Error retrieving sales exec performance review form: ' + JSON.stringify(e));
    }, () => {
      this.salesExecReview.showNoFormMessage = false;
      this.salesExecReview.peopleView = false;
      this.salesExecReview.sitesView = false;
      if (salesmanName) this.salesExecReview.chosenSalesmanName = salesmanName;
      if (refresh) this.salesExecReview.getFormEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  splitSiteByMonthByRegion() {
    let regions: string[] = [...new Set(this.salesExecReview.siteByMonthRows.filter(x => x.IsSite).map(x => x.Region))];

    let sitesByMonthByRegion: SitesByMonthByRegion[] = [];
    regions.forEach(region => {
      sitesByMonthByRegion.push({
        region: region,
        sites: this.salesExecReview.siteByMonthRows.filter(x => x.Region == region)
      })
    })

    this.salesExecReview.sitesByMonthByRegion = sitesByMonthByRegion;
  }

  changeView(viewByMeasure?: boolean) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    if (viewByMeasure == this.salesExecReview.byMeasure) return;

    if (viewByMeasure == true) {
      if (this.salesExecReview.sitesView) return this.getSiteByMeasureRows();
      return this.getPeopleByMeasureRows();
    }
    if (this.salesExecReview.sitesView) return this.getSiteByMonthRows();
    this.getPeopleByMonthRows();
  }

  saveForm(params: SaveSEReviewFormParams, thenUpdateApprovalState?: boolean) {
    this.getData.saveSEReviewForm(params).subscribe((res: { FormId: number }) => {
      this.salesExecReview.newFormIdEmitter.emit(res.FormId);
      this.constants.toastSuccess('Form saved');
    }, e => {
      console.error('Error saving sales exec performance review form: ' + JSON.stringify(e));
      this.constants.toastDanger('Failed to save form');
    }, () => {
      if (thenUpdateApprovalState) {
        let updateApprovalStateParams: UpdateSEReviewFormApprovalStateParams = {
          ApprovalState: this.salesExecReview.form.ApprovalState,
          FormId: this.salesExecReview.form.FormId
        }

        this.updateFormApprovalState(updateApprovalStateParams);
      }

      if(params.getNewForm)
      {
        this.getFormData(this.salesExecReview.chosenPersonId, null);
      }
      else
      {
        this.selections.triggerSpinner.next({ show: false });
      }
      
    })
  }

  refreshForm(params) {
    this.getData.refreshSEReviewForm(params).subscribe((res: RefreshSEReviewFormResult) => {

      this.salesExecReview.form.NonScoringMeasures = res.NonScoringMeasures;
      this.salesExecReview.form.ScoringMeasures = res.ScoringMeasures;

      this.constants.toastSuccess('Form refreshed');
    }, e => {
      console.error('Error refreshing sales exec performance review form: ' + JSON.stringify(e));
      this.constants.toastDanger('Failed to refresh form');
    }, () => {
      this.salesExecReview.formRefreshedEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  updateFormApprovalState(params: UpdateSEReviewFormApprovalStateParams) {
    this.getData.updateSEReviewFormApprovalState(params).subscribe((res: RefreshSEReviewFormResult) => {
      this.constants.toastSuccess('Approval state updated');
    }, e => {
      console.error('Error updating sales exec performance review form approval state: ' + JSON.stringify(e));
      this.constants.toastDanger('Failed to update approval state');
    }, () => {
      this.salesExecReview.approvalStateUpdatedEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    })
  }
}
