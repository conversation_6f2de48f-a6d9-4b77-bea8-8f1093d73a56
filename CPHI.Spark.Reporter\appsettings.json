{
   //-------------------------------------------------x
   // THIS IS REPORTER
   //-------------------------------------------------x
   "ConnectionStrings": {
      "RRGUK": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkRRG;  Persist Security Info=True;User ID=SparkRRGUser; Password=****************; MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=180;",
      "Vindis": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkVindis;  Persist Security Info=True;User ID=SparkVindisUser; Password=***************; MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=180;",
      "RRGSpain": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkRRGSpain; Persist Security Info=true; User ID=sparkSpainUser; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=180;",
      "AutoPrice": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkAutoPrice;  Persist Security Info=True;User ID=SparkAutoPriceUser; Password=****************; MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=180;",
      "Sytner": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkSytner;  Persist Security Info=True;User ID=SparkSytnerUser; Password=****************; MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=180;"
   },
   "AppSettings": {
      //------------------------------------
      // Overrides.  ALL SHOULD BE FALSE WHEN DEPLOY
      //------------------------------------
      //AutoTrader
      "RunTemporaryJobNow": false,
      "RunFetchAutoTraderPricingNow": false,
      "RunFetchAutoTraderPricingSecondRunNow": false,
      "RunLocalBargainNow": false,
      "RunVehicleValuationNow": false,
      "RunVehicleValuationCheckerNow": false,
      "RunUpdateWebsitePricesNow": false,
      "RunUpdateWebsitePrices4PMNow": false,
      "RunDerivativeDownloadNow": false,
      // Mantles Stock
      "RunEMGMantlesStockJobNow": false,
      //RRG Commission
      "runSiteExcelsJobRRGNow": false, //for commission run this
      "runSalesmanPdfsJobRRGNow": false, //for commission run this.  but has to be on separate run
      //RRG Commission - LBDM Execs
      "runLBDMExcelsJobRRGNow": false, //for commission run this
      "runLBDMPdfsJobRRGNow": false, //for commission run this.  but has to be on separate run
      //Vindis commission
      "runSiteExcelsJobVindisNow": false,
      "runSalesmanPdfsJobVindisNow": false,
      //Usage jobs and GardX
      "runUsageJobNow": false,
      "runGardXJobVindisCurrentMTDNow": false,
      "runGardXJobVindisPriorMonthNow": false,
      //------------------------------------
      //App wide settings
      //------------------------------------
      "serviceName": "CPHI.Spark.Reporter",
      "timeZoneInfo": "UTC", //TimeZoneInfo --- Coordinated Universal Time, Central Standard Time, Eastern Standard Time, GMT Standard Time, Hawaiian Standard Time, Mountain Standard Time, Pacific Standard Time, West Pacific Standard Time
      "mailUser": "<EMAIL>",
      "mailPwd": "HepPLaFy9d5goA$3",
      "mailAppTenantId": "7def64f8-8b3f-400a-8ad0-e50eb7e77eef",
      "mailAppId": "9a44d66f-cfe2-4d19-b129-62b3e0dd28aa",
      "mailSecret": "Taylor Swift, definitely",
      "mailSecretValue": "****************************************",
      //------------------------------------
      // AutoTrader 
      //------------------------------------
      //Timings
      "fetchAutoTraderPricingJobCronSchedule": "CUSTOM 0 00 6 ? * *", //6:00AM everyday.    
      "fetchAutoTraderPricingSecondRunJobCronSchedule": "CUSTOM 0 0/2 7-11 ? * *", //8:00AM everyday repeat every 2 mins.    
      "vehicleValuationJobCronSchedule": "REPEAT EVERY 15 SECONDS", //every 15 seconds
      "vehicleValuationCheckerJobCronSchedule": "REPEAT EVERY 2 MINUTES", //
      "emgMantlesStockJobCronSchedule": "CUSTOM 0 30 19 ? * *", // 7.30pm every day
      "localBargainJobCronSchedule": "CUSTOM 0 15 7 ? * *",
      "updateAutoTraderPricesJobCronSchedule": "REPEAT EVERY 1 MINUTES", //6pm every day currently CUSTOM 0 0 18 ? * *
      //"updateAutoTraderPrices4PMJobCronSchedule": "CUSTOM 0 0 16 ? * *", //4pm every day currently
      //"updateAutoTraderPricesOnDemandJobCronSchedule": "REPEAT EVERY 1 MINUTES", //every 60 seconds
      //Keys
      "AutotraderApiKey": "CPHInsight-PricingBI-22-07-24", //"CPHInsightLtd-PricingBI-SB-21-06-23" , 
      "AutotraderApiSecret": "nyGQrMe4oUVTM7iGPZdgghrDP8nTeZKw", // "whlRByHnySVdOMVST1OzITUIU50AlCRr" ,
      "AutotraderBaseURL": "https://api.autotrader.co.uk", // "https://api-sandbox.autotrader.co.uk"
      "AutotraderBaseURLForChangingPrices": "https://api.autotrader.co.uk", //""  https://api-sandbox.autotrader.co.uk
      //DealerGroups for each job
      "FetchJob_FetchGroups": "RRGUK,Vindis,V12,BrindleyGroup,KCSOfSurrey,WaylandsGroup,PentagonGroup,HippoApproved,LMCOfFarnham,Startin,OakwoodMotorCo,SparshattsGroup,EMGGroup,AcornGroup,Lithia,FordsOfWinsford,LSH,Sytner,Nuneaton,SRMotorGroup", //ok
      "FetchJobSecondRun_FetchGroups": "Enterprise", //ok
      "FetchJob_InStockNotInATGroups": "RRGUK,Vindis,WaylandsGroup", //ok
      "FetchJob_InStockNotInATSecondRunGroups": "Enterprise", //ok
      "FetchJob_OptOutGroups": "RRGUK,Startin,Vindis", //ok
      "FetchJob_GenerateChangesGroupsWeekday": "RRGUK,Vindis,V12,BrindleyGroup,KCSOfSurrey,WaylandsGroup,PentagonGroup,HippoApproved,LMCOfFarnham,Startin,OakwoodMotorCo,SparshattsGroup,EMGGroup,AcornGroup,FordsOfWinsford,LSH,Sytner,Nuneaton,SRMotorGroup", //ok
      "FetchJob_EmailChangesGroupsWeekday": "RRGUK,Vindis,V12,BrindleyGroup,KCSOfSurrey,WaylandsGroup,PentagonGroup,HippoApproved,LMCOfFarnham,Startin,OakwoodMotorCo,SparshattsGroup,EMGGroup,AcornGroup,FordsOfWinsford,LSH,Sytner,Nuneaton,SRMotorGroup", //ok
      "FetchJob_EmailChangesGroupsWeekend": "RRGUK,WaylandsGroup", //ok
      "FetchJob_LocationOptimiserGroups": "RRGUK,Vindis,BrindleyGroup,WaylandsGroup,PentagonGroup,LSH", //ok
      "FetchJob_LocationOptimiserSecondRunGroups": "Enterprise", //ok
      "LocalBargainJob_LocalBargainGroup": "RRGUK,Vindis,BrindleyGroup,WaylandsGroup,PentagonGroup,LMCOfFarnham,Startin,KCSOfSurrey,EMGGroup,AcornGroup,LSH,Sytner",
      "ValuationJob_Groups": "RRGUK,Vindis,V12,BrindleyGroup,KCSOfSurrey,WaylandsGroup,PentagonGroup,HippoApproved,LMCOfFarnham,Startin,OakwoodMotorCo,SparshattsGroup,EMGGroup,AcornGroup,FordsOfWinsford,LSH,Sytner,Enterprise,Nuneaton,SRMotorGroup", //ok
      "ValuationCheckerJob_Groups": "RRGUK,Vindis,V12,BrindleyGroup,KCSOfSurrey,WaylandsGroup,PentagonGroup,HippoApproved,LMCOfFarnham,Startin,OakwoodMotorCo,SparshattsGroup,EMGGroup,AcornGroup,FordsOfWinsford,LSH,Sytner,Enterprise,Nuneaton,SRMotorGroup", //ok
      "UpdatePrices_UpdateGroup": "RRGUK,AcornGroup,Startin,SRMotorGroup", //ok
      // "UpdatePrices4PM_UpdateGroup": "Startin", //ok
      // "UpdatePricesOnDemand_UpdateGroup": "RRGUK,Startin", //ok
      //Autoprice Valuation
      "valuationErrorEmailTo": "<EMAIL>,<EMAIL>,<EMAIL>",
      "rebuildUrl": "https://sparkapi.cphi.co.uk/api/sourcedataupdate/updateApiFor?",
      //------------------------------------
      //RRG Commission Stuff
      //------------------------------------
      //Timings
      //Send only to me
      "rrgSendReportsOnlyToMe": false, //MUST BE SET TO FALSE WHEN DEPLOY REPORTER
      "rrgMonthToRunFor": "2025,2,1",
      "rrgIncludePriorMonthAdjustments": true,
      //For sending site excel statements.  USE THIS FOR RE-RUNNING REPORTS FOR PEOPLE
      "rrgSiteSummaryDoSites": false, //can both be true
      "rrgSiteSummaryDoTotal": true, //can both be true
      "rrgSiteSummaryLimitToSiteIds": "", //"19", //"23", // "1, 2, 3, 4, 5, 7  SiteEmailListService.cs contains site names and Ids
      "rrgSiteSummaryLimitToExecNames": "", // "Benjamin Robinson,Tom Smith",  Note no gap after the comma
      //For sending salesman pdf reports.   NEVER USE THIS FOR RE-RUNNING REPORTS AS IT DOESN'T SAVE THE PAYOUTS TO COMMISSIONPAYOUTS TABLE
      "rrgSalesmanReportLimitToSiteIds": "", // "1, 2, 3, 4, 5, 7,  13, 17, 18, 19, 20, 22, 23, 25, 30",  SiteEmailListService.cs contains site names and Ids
      "rrgSalesmanReportLimitToExecNames": "", //NOTE NO SPACE AFTER COMMA
      //------------------------------------
      //Vindis Commission Stuff
      //------------------------------------
      //Send only to me
      "vindisSendReportsOnlyToMe": false, //MUST BE SET TO FALSE WHEN DEPLOY REPORTER
      "vindisMonthToRunFor": "2023,8,1",
      "vindisIncludePriorMonthAdjustments": false,
      //For sending site excel statements.  USE THIS FOR RE-RUNNING REPORTS FOR PEOPLE
      "vindisSiteSummaryDoSites": false,
      "vindisSiteSummaryDoTotal": false,
      "vindisSiteSummaryLimitToSiteIds": "", //"19", //"23", // "1, 2, 3, 4, 5, 7,  13, 17, 18, 19, 20, 22, 23, 25, 30, 33", // "1, 2, 3, 4, 5, 7,  13, 17, 18, 19, 20, 22, 23, 25, 30",  //10, 12,
      "vindisSiteSummaryLimitToExecNames": "", // "Benjamin Robinson",
      //For sending salesman pdf reports.   NEVER USE THIS FOR RE-RUNNING REPORTS AS IT DOESN'T SAVE THE PAYOUTS TO COMMISSIONPAYOUTS TABLE
      "vindisSalesmanReportLimitToSiteIds": "", // "1, 2, 3, 4, 5, 7,  13, 17, 18, 19, 20, 22, 23, 25, 30",  SiteEmailListService.cs contains site names and Ids
      "vindisSalesmanReportLimitToExecNames": "", //NOTE NO SPACE AFTER COMMA
      // For Pinewood Mantles
      "pinewoodMantlesUsername": "<EMAIL>",
      "pinewoodMantlesPassword": "setQF&RL&99@d$QB",
      "pinewoodMantlesOrgId": "d8b55662-3d12-4c8f-a85c-bcbb77928f25",
      "pinewoodMantlesFilePath": "C:\\cphiRoot\\rrg\\inbound",
      //------------------------------------
      //Vindis GardX and Usage report
      //------------------------------------
      //Timings
      // Full prior month - 5th of every month
      "gardXJobCronSchedulePriorMonth": "CUSTOM 0 10 8 5 * ? *", // "CUSTOM 0 0 8 5 * ? *"
      // Current month - Every Monday at 8am
      "gardXJobCronScheduleCurrentMonthToDate": "CUSTOM 0 5 8 ? * MON", // "CUSTOM 0 0 8 ? * MON"
      "reportUsageStatsJobCronSchedule": "CUSTOM 0 0 8 ? * MON", // run 8:0 every Monday
      // GardX Report for Vindis 
      "gardXReportTo": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
      "gardXReportCc": "<EMAIL>,<EMAIL>,<EMAIL>",
      //Usage report
      "useageReportTo": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
      "useageReportCc": "<EMAIL>,<EMAIL>,<EMAIL>",
      //Error Email
      "errorEmailTo": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
      "enterpriseLoadReportTo": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
      "valuationChunkSize": "20"

   },
   "DVSASettings": {
      "TokenURL": "https://login.microsoftonline.com/a455b827-244f-4c97-b5b4-ce5d13b4d00c/oauth2/v2.0/token",
      "BaseURLForRecallApi": "https://history.mot.api.gov.uk/v1/trade/vehicles/registration/",
      "ApiKey": "VGJ4SuSrf768lAPZCtMhlLQPEkkjBPp6vii4NCH3",
      "ClientId": "ab4d91a5-990a-4424-8486-80692e12f82e",
      "ClientSecret": "****************************************",
      "Scope": "https://tapi.dvsa.gov.uk/.default"
   },
   "Logging": {
      "LogLevel": {
         "Default": "Information",
         "Microsoft": "Warning",
         "Microsoft.Hosting.Lifetime": "Information",

         "System.Net.Http.HttpClient": "Warning"
      }
   }
   //--------------//
   //RRG
   //--------------//
   //1: Romford
   //2: Enfield
   //3: Orpington
   //4: London West
   //5: Watford
   //7: Croydon
   //33: Staples Corner
   //34: Hayes
   //9: Central Parts Hub (Old BGM)
   //10: Solihull
   //11: Leicester
   //12: Wolverhampton
   //13: Cannock
   //14: Autoworld
   //15: Wolverhampton Parts Hub
   //16: Central Parts Stock D&D
   //17: Manchester
   //18: Bolton
   //19: Liverpool
   //20: Wirral
   //21: Northern Fleet
   //22: Cardiff
   //23: Swansea
   //25: Nissan London West
   //28: Nissan Leicester
   //30: Nissan Liverpool
   //32: Nissan Northern Fleet
}