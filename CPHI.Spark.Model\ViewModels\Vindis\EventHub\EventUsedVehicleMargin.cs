﻿namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class EventUsedVehicleMargin
    {
        public decimal GrossVehiclePrice { get; set; }

        public decimal NetVehiclePrice { get; set; }

        public decimal StandInValue { get; set; }

        public decimal PurchasePrice { get; set; }

        public decimal ReconditionCost { get; set; }

        public decimal MarginVehicleVat { get; set; }

        public decimal Profit { get; set; }
        
        public bool IsMarginVehicle { get; set; }
    }
}
