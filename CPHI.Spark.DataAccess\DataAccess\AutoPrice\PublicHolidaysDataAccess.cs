using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Microsoft.EntityFrameworkCore;
using Dapper;
using System.Data;
using CPHI.Spark.Model.Services;
using System.Drawing;
using System.Linq;

namespace CPHI.Spark.DataAccess.DataAccess.AutoPrice
{
    public interface IPublicHolidaysDataAccess
    {
        Task<bool> TodayIsABankHoliday(int regionId);
        Task<PublicHolidayResponse> AddPublicHoliday(PublicHolidayRequest request);
    }

    public class PublicHolidaysDataAccess : IPublicHolidaysDataAccess
    {


        private readonly string _connectionString;
        private readonly IDapperr dapper;

        public PublicHolidaysDataAccess(string connectionString)
        {
            this._connectionString = connectionString;
        }


      public async Task<bool> TodayIsABankHoliday(int regionId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var today = DateTime.Now.Date;

            var todayHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Date == today);

            // No holidays at any region
            if (todayHoliday == null)
               return false;

            return await db.PublicHolidayRegionMaps
                .AnyAsync(x => x.PublicHolidayId == todayHoliday.Id && x.PublicHolidayRegionId == regionId);
         }
      }

      public async Task<Dictionary<int, bool>> GetBankHolidayDictionaryForRetailerSites(int dealerGroupId, DateTime date)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {

            // Get today's public holiday for the dealer group
            var publicHoliday = await db.PublicHolidays
                .FirstOrDefaultAsync(x => x.Date.Date == date.Date && x.DealerGroup_Id == dealerGroupId);

            // If no holiday today, return all false
            if (publicHoliday == null)
            {
               return await db.RetailerSites
                   .Where(rs => rs.DealerGroup_Id == dealerGroupId)
                   .ToDictionaryAsync(rs => rs.Id, rs => false);
            }

            // Get regionIds that are observing today's holiday
            var activeRegionIds = await db.PublicHolidayRegionMaps
                .Where(x => x.PublicHolidayId == publicHoliday.Id)
                .Select(x => x.PublicHolidayRegionId)
                .ToListAsync();

            // Join RetailerSites -> Sites and determine if their region is in activeRegionIds
            var siteRegionMap = await (
                from rs in db.RetailerSites
                join s in db.Sites on rs.Site_Id equals s.Id
                where rs.DealerGroup_Id == dealerGroupId
                select new
                {
                   RetailerSiteId = rs.Id,
                   IsBankHoliday = activeRegionIds.Contains((int) s.PublicHolidayRegionId)
                }
            ).ToListAsync();

            return siteRegionMap.ToDictionary(x => x.RetailerSiteId, x => x.IsBankHoliday);
         }
      }
      public async Task<PublicHolidayResponse> AddPublicHoliday(PublicHolidayRequest request)
      {
         try
         {
            using (var db = new CPHIDbContext(_connectionString))
            {

               var publicHoliday = new PublicHoliday
               {
                  Date = request.Date.Date, // Ensure we only store the date part
                  DealerGroup_Id = request.DealerGroupId
               };

               // Add the public holiday
               db.PublicHolidays.Add(publicHoliday);
               await db.SaveChangesAsync();

               // Create the region mappings
               var regionMaps = new List<PublicHolidayRegionMap>();
               foreach (var regionId in request.RegionIds)
               {
                  regionMaps.Add(new PublicHolidayRegionMap
                  {
                     PublicHolidayId = publicHoliday.Id,
                     PublicHolidayRegionId = regionId
                  });
               }

               // Add the region mappings
               db.PublicHolidayRegionMaps.AddRange(regionMaps);
               await db.SaveChangesAsync();

               return new PublicHolidayResponse
               {
                  Id = publicHoliday.Id,
                  Date = publicHoliday.Date,
                  DealerGroupId = publicHoliday.DealerGroup_Id,
                  RegionIds = request.RegionIds,
                  Success = true,
                  Message = "Public holiday added successfully"
               };
            }
         }
         catch (Exception ex)
         {
            return new PublicHolidayResponse
            {
               Success = false,
               Message = $"Error adding public holiday: {ex.Message}"
            };
         }
      }







   }
}
