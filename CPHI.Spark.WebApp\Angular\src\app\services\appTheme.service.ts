import { Injectable } from "@angular/core";
import { ConstantsService } from "./constants.service";

interface CSSVariable {
    property: string;
    value: string;
}

@Injectable({
    providedIn: 'root'
})
export class AppThemeService {
    chosenThemeName: string;
    chosenTheme: CSSVariable[];

    constructor(
        private constants: ConstantsService
    ) { }

    getAndApplySavedTheme(): void {
        this.constants.savedTheme = localStorage.getItem('theme');
        this.chooseTheme(this.constants.savedTheme);
    }

    chooseTheme(theme: string): void {
        switch (theme) {
            case 'green':
                this.loadGreen();
                break;
            case 'dark':
                this.loadDark();
                break;
            case 'light':
                this.loadLight();
                break;
            case 'grey':
                this.loadGrey();
                break;
            default:
                this.loadGrey();
        }

    }

    loadGreen(): void {
        setTimeout(() => {
            const customer = this.constants.environment.dealershipBackgroundImageName;
            const imagePath = `./assets/imgs/customerBackgroundImages/dealership${customer}.jpg`;
            this.chosenTheme = [
                { property: '--mainAppColour', value: '#035359' },
                { property: '--mainAppColourVeryDark', value: '#023336' },
                { property: '--mainAppColourDark', value: '#023c40' },
                { property: '--mainAppColourLight', value: '#046a71' },
                { property: '--mainAppColourVeryLight', value: '#05818a' },
                { property: '--mainAppColourExtremelyLight', value: '#07c6d4' },
                { property: '--secondary', value: '#046a71' },
                { property: '--secondaryLight', value: '#05818a' },
                { property: '--secondaryLighter', value: '#2d939b' },
                { property: '--buttonColourActive', value: '#ffffff' },
                { property: '--cardBackground', value: '#ffffff' },
                { property: '--cardText', value: '#000000' },
                { property: '--actualColour', value: 'hsl(184, 93.3%, 18%)' },
                { property: '--targetColour', value: 'hsla(184, 93.3%, 32%, 0.5)' },

                { property: '--backgroundDealershipImage', value: `linear-gradient(rgba(226, 230, 233, 0.8), rgba(226, 230, 230, 0.8)), url('${imagePath}')` },
                { property: '--backgroundDealershipImageDark', value: `linear-gradient(rgba(30, 30, 30, 0.8), rgba(30, 30, 30, 0.8)), url('${imagePath}')` },
                { property: '--modalBackgroundDealershipImage', value: `linear-gradient(hsla(210, 13%, 90%, 0.80), hsla(210, 13%, 90%, 0.80)), url('${imagePath}')` },
            ]

            this.applyTheme();
        }, 200)
    }

    async loadGrey() {
        setTimeout(() => {


            const customer = this.constants.environment.dealershipBackgroundImageName;
            const imagePath = `./assets/imgs/customerBackgroundImages/dealership${customer}.jpg`;
            //console.log(imagePath)
            this.chosenTheme = [
                { property: '--mainAppColour', value: '#3f3e3c' },
                { property: '--mainAppColourVeryDark', value: '#252424' },
                { property: '--mainAppColourDark', value: '#323130' },
                { property: '--mainAppColourLight', value: '#3f3e3c' },
                { property: '--mainAppColourVeryLight', value: '#4c4b49' },
                { property: '--mainAppColourExtremelyLight', value: '#73716e' },
                { property: '--secondary', value: '#ffbf00' },
                { property: '--secondaryLight', value: '#ffc51a' },
                { property: '--secondaryLighter', value: '#ffcc33' },
                { property: '--buttonColourActive', value: '#000000' },
                { property: '--cardBackground', value: '#ffffff' },
                { property: '--cardText', value: '#000000' },
                { property: '--actualColour', value: '#3f3e3c' },
                { property: '--targetColour', value: '#ECECEC' },

                { property: '--backgroundDealershipImage', value: `linear-gradient(rgba(128, 128, 128, 0.9), rgba(128, 128, 128, 0.9)), url('${imagePath}')` },
                { property: '--backgroundDealershipImageDark', value: `linear-gradient(rgba(30, 30, 30, 0.9), rgba(30, 30, 30, 0.9)), url('${imagePath}')` },
                { property: '--modalBackgroundDealershipImage', value: `linear-gradient(hsla(210, 13%, 90%, 0.80), hsla(210, 13%, 90%, 0.80)), url('${imagePath}')` },
            ]
            
            this.applyTheme();
        }, 200)
    }
    
    loadDark(): void {
        
        setTimeout(() => {
            
            const customer = this.constants.environment.dealershipBackgroundImageName;
            const imagePath = `./assets/imgs/customerBackgroundImages/dealership${customer}.jpg`;
            this.chosenTheme = [
                { property: '--mainAppColour', value: '#323130' },
                { property: '--mainAppColourVeryDark', value: '#201f1f' },
                { property: '--mainAppColourDark', value: '#252424' },
                { property: '--mainAppColourLight', value: '#3f3e3c' },
                { property: '--mainAppColourVeryLight', value: '#4c4b49' },
                { property: '--mainAppColourExtremelyLight', value: '#73716e' },
                { property: '--secondary', value: '#ffbf00' },
                { property: '--secondaryLight', value: '#ffc51a' },
                { property: '--secondaryLighter', value: '#ffc51a' },
                { property: '--buttonColourActive', value: '#000000' },
                { property: '--cardBackground', value: '#201f1f' },
                { property: '--cardText', value: '#ffffff' },
                { property: '--actualColour', value: '#252424' },
                { property: '--targetColour', value: '#73716e' },

                
                { property: '--backgroundDealershipImage', value: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('${imagePath}')` },
                { property: '--backgroundDealershipImageDark', value: `linear-gradient(rgba(30, 30, 30, 0.9), rgba(30, 30, 30, 0.9)), url('${imagePath}')` },
                { property: '--modalBackgroundDealershipImage', value: `linear-gradient(hsla(210, 13%, 90%, 0.80), hsla(210, 13%, 90%, 0.80)), url('${imagePath}')` },
            ]

            this.applyTheme();
        }, 200)
    }

    loadLight(): void {

    }

    applyTheme(): void {
        this.chosenTheme.forEach((item: CSSVariable) => {
            document.documentElement.style.setProperty(item.property, item.value);
        })
    }
}