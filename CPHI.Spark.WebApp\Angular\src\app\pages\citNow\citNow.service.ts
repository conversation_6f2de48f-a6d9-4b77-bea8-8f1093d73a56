import {  EventEmitter, Injectable } from '@angular/core';
import { TopBottomHighlightRule } from 'src/app/model/TopBottomHighlightRule';
import { CitNowSalesVideoSummaryItem, CitNowDetail } from 'src/app/model/afterSales.model';
import { CitNowSiteSummary, Month, CitNowRegionRollingDataSet, CitNowSimpleDetail, CitNowPersonSummary } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Injectable({
  providedIn: 'root'
})
export class CitNowService {

  topBottomHighlights:TopBottomHighlightRule[]=[]
  topBottomHighlightsEdynamix:TopBottomHighlightRule[]=[]
  siteRows: CitNowSiteSummary[];
  chosenSiteRow: CitNowSiteSummary;
  monthStartDate: Date;
  showSales: boolean;
  regionalChartDetails: CitNowRegionRollingDataSet[];
  //last5WeeksVideos: CitNowSalesVideoSummaryItem[];
  //last5WeeksVideosAftersales: CitNowSalesVideoSummaryItem[];
  detailedTableRows: CitNowDetail[];
  simpleDetailedTableRows: CitNowSimpleDetail[];
  isInitialised: boolean;

  newDataEmitter:EventEmitter<void> = new EventEmitter();
  personRows: CitNowPersonSummary[];


  constructor(

    private constants: ConstantsService,
    private getData: GetDataMethodsService,
    private selections: SelectionsService
  ) {


  }




  initiateCitNow() {

    this.monthStartDate = this.constants.thisMonthStart
    this.isInitialised = true;


  }

  getEDynamixPeopleRows(siteIds: string) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.getData.getCitNowEDynamixData(this.monthStartDate, siteIds).subscribe((res: CitNowPersonSummary[]) => {
      this.personRows = res;
      this.newDataEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    }, e => {
      console.error('Failed to retrieve EDynamix people rows');
    })
  }


}
