﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.IO;
using System.Linq;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Threading;
using CPHI.Spark.Model.ViewModels;
using System.Collections.Generic;
using OpenQA.Selenium.Interactions;
using Azure;
using System.Windows.Documents;
using OfficeOpenXml.FormulaParsing.LexicalAnalysis;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.WebScraper.Services;

namespace CPHI.Spark.WebScraper.Jobs
{
   public class NetDirectorUpdatePriceJob : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(NetDirectorUpdatePriceJob));
      private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
      private const string customerName = "brindley";
      private string fileDestination;

      public void Execute() { }
      public async Task Execute(IJobExecutionContext context)
      {

         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         fileDestination = ConfigService.FileDestination;
         fileDestination = fileDestination.Replace("{destinationFolder}", "brindley");

         try
         {
            ScraperMethodsService.ClearDownloadsFolder();
            var service = ChromeDriverService.CreateDefaultService();
            service.HostName = "127.0.0.1";

            ChromeOptions options = ScraperMethodsService.SetChromeOptions("NetDirector", 9999);

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
            _driver.Manage().Cookies.DeleteAllCookies();

            await UpdatePrices();

            _driver.Manage().Cookies.DeleteAllCookies();
            Thread.Sleep(7000);

            _driver.Quit();
            _driver.Dispose();
            stopwatch.Stop();

         }
         catch (Exception e)
         {
            EmailerService eService = new EmailerService();
            await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");

            stopwatch.Stop();
            errorMessage = e.ToString();

            logger.Error($"Problem {e.ToString()}");

            _driver.Quit();
            _driver.Dispose();
         }
         finally
         {
            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "WebScraper",
               Customer = "brindley",
               Environment = "PROD",
               Task = this.GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };
            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }

      }


      public async Task UpdatePrices()
      {

         AutoTraderStockClient atStockClient = new AutoTraderStockClient(
                            HttpClientFactoryService.HttpClientFactory,
                            ConfigService.AutoPriceConnectionString,
                            ConfigService.AutotraderApiSecret,
                            ConfigService.AutotraderBaseURL
                            );

         AutoTraderApiTokenClient atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         var token = await atTokenClient.GetToken();

         try
         {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            DateTime start = DateTime.Now;

            Login(wait);

            GoToStockPage(wait, start);

            //string test = _driver.PageSource;

            GoToInventory(wait, start);

            var testChanges = new List<NetDirectorPriceChange>
                {
                    new NetDirectorPriceChange { Reg = "AK24KWG", NewPrice = 26494, WebsiteStockIdentifier = "Test", AdvertiserId = 0 },
                    new NetDirectorPriceChange { Reg = "AJ24KXE", NewPrice = 33994, WebsiteStockIdentifier = "Test", AdvertiserId = 0 },
                };

            // Example: Output the list to console
            foreach (var change in testChanges)
            {
               SearchText(change);

               if (!CheckForResults())
               {
                  logger.Info($"No results found for {change.Reg}. Skipping.");
                  GoToInventory(wait, start);
                  continue;
               }

               if (!CheckForMatchingReg(change.Reg))
               {
                  logger.Info($"Results found but no match found for {change.Reg}. Skipping.");
                  GoToInventory(wait, start);
                  continue;
               }

               // At this point, we have results and a matching reg
               await AmendPrice(change, token, atStockClient, atTokenClient);

               GoToInventory(wait, start);

            }

         }
         catch (Exception e)
         {
            throw e;
         }
      }

      private async Task AmendPrice(NetDirectorPriceChange change, TokenResponse token,AutoTraderStockClient atStockClient, AutoTraderApiTokenClient atTokenClient)
      {
         ClickIntoItem();

         ExpandPriceDetails();

         try
         {
            bool modified = ModifyInputFields(change);

            if (!modified)
            {
               logger.Info($"Not amending prices for {change.Reg} as both already match. Continuing.");
               return;
            }

            bool verified = VerifyChanges(change.NewPrice, false);

            if (!verified)
            {
               logger.Info($"Not amending list price for {change.Reg} as already matches.");
               return;
            }

            bool saved = SaveVehicleDetails();

            if (!saved)
            {
               logger.Info($"Failed attempting to save changes for {change.Reg}");
               return;
            }

            System.Threading.Thread.Sleep(2000);

            bool verifiedAfterSave = VerifyChanges(change.NewPrice, true);

            if (!saved)
            {
               logger.Info($"Could not verify changes saved correctly {change.Reg}");
               return;
            }

            //still here, must have been ok, now update Autotrader
            await UpdateAutoTraderForPriceChange(change.WebsiteStockIdentifier, change.NewPrice, change.AdvertiserId, ConfigService.AutotraderBaseURL, token, atStockClient, atTokenClient);
            logger.Info($"Updated price on AT and saved for stock number {change.Reg}, new price: {change.NewPrice}");

         }
         catch (Exception e)
         {
            { }
         }


      }

      private void ExpandPriceDetails()
      {
         try
         {
            var expandPriceDetail = WaitAndFind("//div[contains(@class, 'nd-widget-title-accordion') and @data-target='#widget-accordion-vehicle-prices']//a[contains(@class, 'accordion-toggle')]",
               true);
         }
         catch
         {
            { }
         }
      }


      private static async Task UpdateAutoTraderForPriceChange(string stockId, int newPrice, int advertiserId, string atBaseUrl, TokenResponse atToken, AutoTraderStockClient atStockClient, AutoTraderApiTokenClient tokenClient)
      {
         // This method updates the price on AutoTrader using the AutoTraderStockClient
         if (atStockClient != null)
         {
            try
            {
               // Create the UpdatePriceParams object required by the AutoTraderStockClient
               var updateParams = new UpdatePriceParams
               {
                  WebsiteStockIdentifier = stockId,
                  NewPrice = newPrice,
                  AdvertiserId = advertiserId
               };


               // Call the API to update the price
               atToken = await tokenClient.CheckExpiryAndRegenerate(atToken);
               string result = await atStockClient.UpdatePrice(updateParams, atToken.AccessToken, atBaseUrl);
               logger.Info($"AutoTrader price update result: {result}");
            }
            catch (Exception ex)
            {
               logger.Error($"Failed to update AutoTrader price: {ex.Message}");
            }
         }
         else
         {
            logger.Warn("AutoTrader client not initialized, skipping price update");
         }
      }
      private bool SaveVehicleDetails()
      {
         try
         {
            WaitAndFind("//a[@id='btn-vehicle-detail-save-top']", true);
            return true;
         }
         catch (Exception e)
         {
            return false;
         }
      }

      private bool ModifyInputFields(NetDirectorPriceChange change)
      {
         bool modified = false;

         // List Price
         var div = WaitAndFind("//div[contains(@class, 'control-group') and contains(., 'List Price')]", false);
         var input = div.FindElement(By.CssSelector("input[name='vehicle[clientdata-price-list]']"));

         // Get the current list price
         string curlistPriceStr = input.GetAttribute("value");
         decimal curlistPriceDec = decimal.Parse(curlistPriceStr);
         int curlistPriceInt = (int)curlistPriceDec;

         if (curlistPriceInt == change.NewPrice)
         {
            logger.Info($"Not amending List Price for {change.Reg} as already matches.");
         }
         else
         {
            // Clear the existing value
            input.Clear();

            // Set the new price
            input.SendKeys(change.NewPrice.ToString());

            // Simulate a Tab keypress to move focus away
            Actions actions = new Actions(_driver);
            actions.SendKeys(Keys.Tab).Perform();
            modified = true;
         }

         // Conditional Price
         div = WaitAndFind("//div[contains(@class, 'control-group') and contains(., 'Conditional Price')]", false);
         input = div.FindElement(By.CssSelector("input[name='vehicle[clientdata-price-siv]']"));

         // Get the current list price
         string sivPriceStr = input.GetAttribute("value");
         decimal sivPriceDec = decimal.Parse(sivPriceStr);
         int sivPriceInt = (int)sivPriceDec;

         if (sivPriceInt == change.NewPrice)
         {
            logger.Info($"Not amending Conditional Price for {change.Reg} as already matches.");
         }
         else
         {
            // Clear the existing value
            input.Clear();

            // Set the new price
            input.SendKeys(change.NewPrice.ToString());

            // Simulate a Tab keypress to move focus away
            Actions actions = new Actions(_driver);
            actions.SendKeys(Keys.Tab).Perform();
            modified = true;
         }

         return modified;
      }

      private bool VerifyChanges(int newPrice, bool expand)
      {
         if(expand)
         {
            ExpandPriceDetails();
         };

         var div = WaitAndFind("//div[contains(@class, 'control-group') and contains(., 'List Price')]", false);
         var input = div.FindElement(By.CssSelector("input[name='vehicle[clientdata-price-list]']"));

         // Get the current list price
         string curlistPriceStr = input.GetAttribute("value");
         decimal curlistPriceDec = decimal.Parse(curlistPriceStr);
         int curlistPriceInt = (int)curlistPriceDec;

         if (curlistPriceInt != newPrice)
         {
            return false;
         }

         // Conditional Price
         div = WaitAndFind("//div[contains(@class, 'control-group') and contains(., 'Conditional Price')]", false);
         input = div.FindElement(By.CssSelector("input[name='vehicle[clientdata-price-siv]']"));

         // Get the current list price
         string sivPriceStr = input.GetAttribute("value");
         decimal sivPriceDec = decimal.Parse(sivPriceStr);
         int sivPriceInt = (int)sivPriceDec;

         if (sivPriceInt != newPrice)
         {
            return false;
         }

         return true;
      }

      public void ClickIntoItem()
      {
         var link = _driver.FindElement(By.CssSelector("table tbody tr:first-of-type td.vehicle-make-model-col a"));

         link.Click();
      }

      private bool CheckForMatchingReg(string reg)
      {
         string firstItemReg = GetFirstVehicleReg();

         return reg == firstItemReg;
      }

      private string GetFirstVehicleReg()
      {
         try
         {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IWebElement stockTable = wait.Until(drv => drv.FindElement(By.Id("stockTable")));

            IWebElement tbody = stockTable.FindElement(By.TagName("tbody"));
            var rows = tbody.FindElements(By.TagName("tr"));

            // First row
            IWebElement firstRow = rows[0];

            // First cell (VRM)
            IWebElement regCell = firstRow.FindElement(By.XPath("td[1]"));

            if(regCell == null)
            {
               return "";
            }

            return regCell.Text.Replace(" ","").ToUpper().Trim();
         }
         catch (Exception ex)
         {
            return null;
         }
      }


      private static bool CheckForResults()
      {
         try
         {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IWebElement stockTable = wait.Until(drv => drv.FindElement(By.Id("stockTable")));

            IWebElement tbody = stockTable.FindElement(By.TagName("tbody"));

            // Get all rows inside tbody
            var rows = tbody.FindElements(By.TagName("tr"));

            return rows.Count > 0;
         }
         catch (NoSuchElementException)
         {
            return false;
         }
         catch (WebDriverTimeoutException)
         {
            return false;
         }
      }

      private void SearchText(NetDirectorPriceChange change)
      {
         // There seems to sometimes be issues waiting for this search box
         // even with the WaitAndFind
         System.Threading.Thread.Sleep(1000);

         IWebElement searchBox = WaitAndFind("//input[@id='keywordDetail']", false);

         System.Threading.Thread.Sleep(5000);

         // Clear any existing text
         searchBox.Clear();

         searchBox.SendKeys(change.Reg);

         searchBox.SendKeys(Keys.Enter);

         System.Threading.Thread.Sleep(5000);
      }

      private void Login(WebDriverWait wait)
      {

         IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

         _driver.Navigate().GoToUrl("https://netdirector.co.uk/");

         System.Threading.Thread.Sleep(1000);

         IWebElement loginButton = _driver.FindElement(By.CssSelector("button.login-form-btn.btnSubmit"));


         System.Threading.Thread.Sleep(1000);

         WaitAndFind("//input [@Id='email']", false).SendKeys("<EMAIL>");
         System.Threading.Thread.Sleep(1000);
         WaitAndFind("//input [@Id='password']", false).SendKeys(ConfigService.NetDirectorPassword);
         System.Threading.Thread.Sleep(1000);

         // Click login
         loginButton.Click();

         System.Threading.Thread.Sleep(5000);
      }

      private void GoToStockPage(WebDriverWait wait, DateTime start)
      {
         // Go to Software Select options
         WaitAndFind("//*[@data-original-title='Software Select']", true);

         System.Threading.Thread.Sleep(1000);

         string originalTab = _driver.CurrentWindowHandle;

         // Select Stock
         WaitAndFind("//span[@class='product-title' and text()='NetDirector Stock']", true);

         // This creates a new tab - close the original to use the new one
         // Wait for the new tab to open
         WebDriverWait waitForNewTab = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
         waitForNewTab.Until(driver => _driver.WindowHandles.Count > 1);

         // Find the new tab (the one that's not the original)
         string newTab = _driver.WindowHandles.First(h => h != originalTab);

         // Close the original tab
         _driver.Close();

         // Switch to the new tab
         _driver.SwitchTo().Window(newTab);
      }

      private void GoToInventory(WebDriverWait wait, DateTime start)
      {
         _driver.Navigate().GoToUrl("https://stock.netdirector.co.uk/vehicle-management");
      }

      public IWebElement WaitAndFind(string findXPath, bool andClick = false)
      {
         IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "NetDirectorUpdatePrice", findXPath, andClick);

         return result;
      }

      public static bool elementExists(By by)
      {
         try
         {
            _driver.FindElement(by);
            return true;
         }
         catch (NoSuchElementException)
         {
            return false;
         }
      }

      public static void waitForElement(By by)
      {
         for (int i = 0; i < 30; i++)
         {
            System.Threading.Thread.Sleep(10000); // 10 second wait matches the refresh time on iStoreDocs
            if (elementExists(by))
            {
               break;
            }
         }

      }

   }
}