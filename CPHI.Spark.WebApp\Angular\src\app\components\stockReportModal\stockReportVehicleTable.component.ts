//core angular
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Stock, StockModalRow } from 'src/app/model/sales.model';
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { localeEs } from 'src/environments/locale.es.js';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
//services
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';
import { GridOptions } from 'ag-grid-community';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';



@Component({
  selector: 'stockReportVehicleTable',
  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>
    
    <ag-grid-angular  class="ag-theme-balham" 
    [gridOptions]="mainTableGridOptions"
    
      [rowData]="rowData" 
       [frameworkComponents]="frameworkComponents"  
      (gridReady)="onGridReady($event)"
       [getRowNodeId]="getRowNodeId" 
       [animateRows]="true"
      > 
      
    </ag-grid-angular>
    </div>
    `
  ,
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
  styles: [
    `
    #gridHolder{position:relative;height:70vh;max-height:70vh;}
    ag-grid-angular.hidden{opacity:0}
    ag-grid-angular{  height: 72vh;  width: 100%;        max-width: 1927px;z-index:2;}



  `
  ]
})



export class StockReportVehicleTableComponent implements OnInit {

  @Input() public rowData: StockModalRow[];
  @Input() public showUsedCols: boolean;

  @Output() clickedRow = new EventEmitter<StockModalRow>();


  //main declarations
  //for agGrid
  showGrid = false;
  public gridApi;
  public importGridApi;
  public gridColumnApi;
  public getRowNodeId;

  mainTableGridOptions: GridOptions

  gridApiColumnDefinitions: any;

  frameworkComponents: { agColumnHeader: any; };

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpers: AGGridMethodsService
  ) {


  }

  ngOnDestroy() { }


  ngOnInit() {
    this.initParams();
  }


  initParams() {

    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpers.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      suppressPropertyNamesCheck: true,
      context: { thisComponent: this },
      onCellMouseOver: (params) => {
        //this.onCellMouseOver(params);
      },
      onCellDoubleClicked: (params) => {
        this.onCellDblClick(params);
      },
      getRowHeight: (params) => {
        return 25
      },
      onFirstDataRendered:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
      defaultColDef: {
        floatingFilter: true,
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpers.getFilterListItemHeight() }, autoHeight: true,
        //suppressColumnMoveAnimation: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: [],
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }

    this.mainTableGridOptions.columnDefs = [
      { headerName: '#', cellRenderer: (params) => params.rowIndex + 1, field: '', width: 15, type: 'label', },
      { headerName: this.constants.translatedText.Site, field: 'SiteNameShort', width: 30, type: 'label', },
      { headerName: 'Reg', field: 'Reg', width: 30, type: 'label', },
      { headerName: this.constants.translatedText.StockNumber, field: 'StockNumber', cellRenderer: (params) => params.data.StockNumber == '0' ? '' : params.data.StockNumber, width: 30, type: 'number', },
      { headerName: 'Stock Type', field: 'StockType', width: 60, type: 'label', },
      { headerName: this.constants.translatedText.Age, field: 'Age', width: 25, type: 'number', },
      
      { headerName: this.constants.translatedText.Description, field: 'Description', cellRenderer: (params) => params.data.Model + params.data.Description, width: 120, type: 'label', },
      
      { headerName: 'Prepped?', field: 'IsPrepped', width: 40, type: 'boolean', hide: !this.showUsedCols },
      { headerName: 'On RRG Site?', field: 'IsOnWebsite', width: 40, type: 'boolean', hide: !this.showUsedCols || this.constants.environment.stockReport_hideOnRRGSiteCol },
      { headerName: 'SIV', field: 'SIV', width: 50, type: 'currency', },
      { headerName: 'Selling Price', field: 'SellingPrice', width: 50, type: 'currency', },
    ]
  }
  

  // SiteNameShort: string;
  // Reg: string;
  // StockNumber: string;
  // StockType: string;
  // Age: number;
  // Description: string;
  // Model: string;
  // IsPrepped: boolean;
  // IsOnWebsite: boolean;
  // SIV: number;
  // SellingPrice: number;
  // Id: number;

  onCellDblClick(params) {
    this.clickedRow.next(params.data)
  }



  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);
    this.gridApi.setRowData(this.rowData);

    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();
  }


  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }

  }



  refreshCells() {
    if (this.gridApi) {
      this.gridApi.refreshCells();
    }
  }


  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, this.selections.stockReportModal.excelExportName, 1.3);
  }






}
