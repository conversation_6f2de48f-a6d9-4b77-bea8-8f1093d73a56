<div class="tileHeader clickable" (click)="goToStockPage()">
  <div class="headerWords">
    <h5>{{constants.translatedText.Dashboard_UsedStockMerchandising}}
    </h5>
  </div>
</div>

<div class="contentsHolder">

  <table>

    <thead>

      <tr id="daysHeader">
        <th></th>
        <th></th>
        <th>&#60;7 {{constants.translatedText.DaysLower}}</th>
        <th>&#60;14 {{constants.translatedText.DaysLower}}</th>
        <th>14 {{constants.translatedText.DaysLower}} +</th>
      </tr>
    </thead>

    <!-- The table body for on RRG Site -->
    <tbody>
      <tr>
        <!-- Label -->
        <td class="labelCell">
          <!-- thing on left   -->
          <div class="iconAndLabel">
            <div class="spaceBetween column">
              <i class="fas fa-globe"></i>
              <div class="label">On RRG Site</div>
              <div class="label">({{data.Listed + data.OnOrder|cph:'number':0 }})</div>
            </div>
          </div>
        </td>

        <!-- Bars -->
        <td>
          <div class="barHolder merchBars">

            <div class="onRRGSite bar" placement="auto" container="body" [openDelay]="0" [closeDelay]="0"
              [ngbPopover]="onRRGSiteBarPopover" triggers="mouseenter:mouseleave" popoverTitle=""
              (click)="loadVehicleDetails('Vehicles On RRG Site', 'ListedOnSite')"
              [ngStyle]="{'width.%': data.Listed  / data.ListedBarTotal * 100}">
              {{data.Listed|cph:'number':0 }}&nbsp;({{data.Listed/data.ListedBarTotal|cph:'percent':0:false}})
            </div>
            <div class="onRRGSiteNotInStock bar" placement="auto" container="body" [openDelay]="0" [closeDelay]="0"
              [ngbPopover]="onRRGSiteNotInStockPopover" triggers="mouseenter:mouseleave" popoverTitle=""
              (click)="loadVehicleDetails('On RRG Site not in stock', 'OnSiteNotInStock')"
              [ngStyle]="{'width.%': data.OnRRGSiteNotInstock / data.ListedBarTotal * 100}">
              {{data.OnRRGSiteNotInstock|cph:'number':0
              }}&nbsp;({{data.OnRRGSiteNotInstock/data.ListedBarTotal|cph:'percent':0:false}})
            </div>
            <div class="onOrderNotOnRRGSite bar" placement="auto" container="body" [openDelay]="0" [closeDelay]="0"
              [ngbPopover]="onOrderNotOnRRGSitePopover" triggers="mouseenter:mouseleave" popoverTitle=""
              (click)="loadVehicleDetails('Vehicles On Order Not On RRG Site','UnListedOnOrder')"
              [ngStyle]="{'width.%': data.OnOrder / data.ListedBarTotal * 100}">
              {{data.OnOrder|cph:'number':0 }}&nbsp;({{data.OnOrder/data.ListedBarTotal|cph:'percent':0:false}})
            </div>
            <div class="notOnRRGSite bar" placement="auto" container="body" [openDelay]="0" [closeDelay]="0"
              [ngbPopover]="notOnRRGSitePopover" triggers="mouseenter:mouseleave" popoverTitle=""
              (click)="loadVehicleDetails('Vehicles Not On RRG Site','UnListed')"
              [ngStyle]="{'width.%': data.NotListedTotal / data.ListedBarTotal * 100}">
              {{data.NotListedTotal|cph:'number':0}}
            </div>

          </div>
        </td>

        <!-- Under7 -->
        <td (click)="loadVehicleDetails('Vehicles Not On RRG Site <7 Days','UnListedUpTo7')" class="ageCell clickable">
          {{data.NotListed_Under7|cph:'number':0}}</td>

        <!-- Under14 -->
        <td (click)="loadVehicleDetails('Vehicles Not On RRG Site 70-14 Days','UnListedOver7UpTo14')"
          class="ageCell clickable"> {{data.NotListed_Under14|cph:'number':0}}</td>

        <!-- 14Plus -->
        <td (click)="loadVehicleDetails('Vehicles Not On RRG Site 14 Days+','UnListedOver14')"
          class="ageCell clickable"> {{data.NotListed_Over14|cph:'number':0}}</td>
      </tr>


    </tbody>

    <thead>

      <tr id="daysHeader">
        <th></th>
        <th></th>
        <th>{{constants.translatedText.Stock}}</th>
        <th>
          Stock awaiting prep
        </th>
        <th>
          <div placement="auto" container="body" [openDelay]="0" [closeDelay]="0"
            [ngbPopover]="'Sold, line pushed, awaiting prep'" triggers="mouseenter:mouseleave" popoverTitle="">
            Sold awaiting prep
          </div>
        </th>
      </tr>
    </thead>

    <tbody>

      <tr>
        <td class="labelCell">
          <!-- thing on left   -->
          <div class="iconAndLabel">
            <div class="spaceBetween column">
              <i class="fas fa-wrench"></i>

            </div>
          </div>
        </td>

        <td></td>
        <!-- Under 7 prep value -->
        <td class="ageCell"> {{data.Stock|cph:'number':0}}</td>

        <td class="ageCell"> {{data.InStockAwaitingPrep|cph:'number':0}}</td>

        <td class="ageCell"> {{data.SoldAwaitingPrep|cph:'number':0}}</td>

      </tr>
    </tbody>


  </table>

  <ng-template class="popover" #onRRGSiteBarPopover>
    On RRG Site {{data.Listed}}, {{data.Listed/data.ListedBarTotal|cph:'percent':0}}
  </ng-template>
  <ng-template class="popover" #onRRGSiteNotInStockPopover>
    On RRG Site Not In-Stock {{data.OnRRGSiteNotInstock}},
    {{data.OnRRGSiteNotInstock/data.ListedBarTotal|cph:'percent':0}}
  </ng-template>
  <ng-template class="popover" #onOrderNotOnRRGSitePopover>
    On Order Not On RRG Site {{data.OnOrder}}, {{data.OnOrder/data.ListedBarTotal|cph:'percent':0}}
  </ng-template>
  <ng-template class="popover" #notOnRRGSitePopover>
    Not on RRG Site {{data.NotListedTotal}}, {{data.NotListedTotal/data.ListedBarTotal|cph:'percent':0}}
  </ng-template>