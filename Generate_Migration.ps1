# Define the path to the appsettings.Development.json
$targetAppSettings = ".\CPHI.Spark.WebApp\appsettings.Development.json"
$projectPath = ".\CPHI.Spark.WebApp"
$repositoryProject = ".\CPHI.Spark.Repository"

# Step 1: Change DefaultConnection to MigrationConnection in appsettings.Development.json
Write-Host "Changing DefaultConnection to MigrationConnection..."
(Get-Content $targetAppSettings) -replace '"DefaultConnection"', '"MigrationConnection"' | Set-Content $targetAppSettings

# Step 2: Generate the migration
Read-Host -Prompt "Have you definitely saved all files?"
$description = Read-Host -Prompt "Enter migration description"
$description = $description -replace ' ', '_'  # Replace spaces with underscores
Write-Host "Adding migration: $description"
#& "EntityFrameworkCore\add-migration" $description -context 'CPHIDbContext'
dotnet ef migrations add $description --context CPHIDbContext --project $repositoryProject --startup-project $projectPath


# Step 3: Change MigrationConnection back to DefaultConnection
# Write-Host "Reverting MigrationConnection to DefaultConnection..."
(Get-Content $targetAppSettings) -replace '"MigrationConnection"', '"DefaultConnection"' | Set-Content $targetAppSettings

Write-Host "Migration created successfully. Please review the migration files before applying them."