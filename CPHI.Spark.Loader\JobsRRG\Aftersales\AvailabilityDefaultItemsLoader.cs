using Quartz;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using log4net;
using System.Threading.Tasks;
using CPHI.Spark.Model;
using CPHI.Repository;
using System.Diagnostics;

namespace CPHI.Spark.Loader
{
    public class AvailabilityDefaultJob : IJob
    {
        private readonly IDapper dapper = new Dapperr();
        private static readonly ILog Logger = LogManager.GetLogger(typeof(AvailabilityDefaultJob));
        private string fileSearch = "*SPK38.csv";
        /*
| Column      | Example | Sensitive |
|-------------|---------|-----------|
| Site ID     | 12      | No        |
| Description | foo     | No        |
| Monday      | 41      | No        |
| Tuesday     | 41      | No        |
| Wednesday   | 41      | No        |
| Thursday    | 41      | No        |
| Friday      | 41      | No        |
| Saturday    | 12      | No        |
| Sunday      | 0       | No        |

 */

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            string[] filePaths = Directory.GetFiles(ConfigService.incomingRoot, fileSearch);

            //check for presence of file, if so, return as already running
            if (LocksService.AvailabilityDefault) { CentralLoggingService.ReportLock("AvailabilityDefaultJob"); return; }

            if (filePaths.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.AvailabilityDefault;
                if (age.Minutes > 120)
                {
                    PulsesService.AvailabilityDefault = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *SPK38.csv");
                }
                return;
            }


            //define Lists
            List<Site> dbSites;
            List<StandingValue> dbStandingValues;

            using (var db = new CPHIDbContext())

            {
                var logMessage = new LogMessage();
                logMessage.DealerGroup_Id = 1;
                int errorCount = 0;
                
                try
                {
                    //set Lists
                    ////db.Configuration.ProxyCreationEnabled = false;

                    dbSites = db.Sites.ToList();
                    dbStandingValues = db.StandingValues.ToList();


                    logMessage.SourceDate = DateTime.UtcNow;
                    logMessage.Job = this.GetType().Name;

                    Logger.Info($@"[{DateTime.UtcNow}] {filePaths.Count()} file(s) found at {ConfigService.incomingRoot}");   //update logger with how many found

                    //Dictionary<string, string> departmentLookup = new Dictionary<string, string>();
                    //departmentLookup.Add("00", "D90");


                    //go through first file
                    string filePath = filePaths[0];


                    //define variables for use in processing this file
                    int incomingCount = 0;
                    int removedCount = 0;
                    int newCount = 0;
                    int changedCount = 0;

                    if (File.Exists(filePath.Replace(".csv", "-p.csv")))
                    {
                        //already processing a file of this type, skip
                        Logger.Info($@"[{DateTime.UtcNow}] Could not interpret {filePath}, -p file already found ");
                        logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                    }
                    File.Move(filePath, filePath.Replace(".csv", "-p.csv")); //append _p to the file to prevent any other instances also processing these files
                    var newFilepath = filePath.Replace(".csv", "-p.csv");
                    LocksService.AvailabilityDefault = true;

                    string fileName = Path.GetFileName(filePath);
                    var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                    logMessage.SourceDate = fileDate;

                    Logger.Info($@"[{DateTime.UtcNow}] Starting {filePath} ");

                    List<AvailabilityDefault> incomingAvailabilityDefault = new List<AvailabilityDefault>(1000);  //preset the list size (slightly quicker than growing it each time)

                    var allText = File.ReadAllText(newFilepath);
                    string allTextNoCR = allText.Replace("\r", "");
                    var rows = allTextNoCR.Split('\n');


                    var headers = rows.Skip(0).First().ToUpper().Split(',');

                    int defaultRtsTypeId = db.StandingValueTypes.ToList().Find(x => x.Description == "RTSType").Id;
                    int defaultRtsId = db.StandingValues.ToList().Find(x => x.Description == "Other" && x.StandingValueType_Id == defaultRtsTypeId).Id;

                    foreach (var row in rows.Skip(1))
                    {
                        if (!string.IsNullOrEmpty(row))
                        {
                            incomingCount++;


                            try
                            {
                                var cells = row.Split(',');

                                if (cells.Length != headers.Length)
                                {
                                    //something weird happened, not got enough cells for the number of headerCols, skip this record
                                    logMessage.FailNotes = logMessage.FailNotes + $"Skipped item no. {incomingCount}. Had {cells.Length} cells and needed {headers.Length}";
                                    errorCount++;
                                    continue;
                                }

                                //lookup objects required

                                //site
                                int branchId = int.Parse(cells[Array.IndexOf(headers, "SITE ID")]);
                                int siteId = dbSites.Find(x => x.Code == branchId).Id;

                                //rtsType
                                int rtsTypeId = defaultRtsId;
                                string skill = cells[Array.IndexOf(headers, "DESCRIPTION")];
                                try { rtsTypeId = dbStandingValues.Find(x => x.Description.ToUpper() == skill.ToUpper()).Id; } catch { }

                                //not pretty but it works
                                AvailabilityDefault a = new AvailabilityDefault();
                                AvailabilityDefault b = new AvailabilityDefault();
                                AvailabilityDefault c = new AvailabilityDefault();
                                AvailabilityDefault d = new AvailabilityDefault();
                                AvailabilityDefault e = new AvailabilityDefault();
                                AvailabilityDefault f = new AvailabilityDefault();
                                AvailabilityDefault g = new AvailabilityDefault();

                                a.Weekday = 1;
                                a.Hours = decimal.Parse(cells[Array.IndexOf(headers, "MONDAY")]);
                                a.RtsType_Id = rtsTypeId;
                                a.Site_Id = siteId;

                                incomingAvailabilityDefault.Add(a);

                                b.Weekday = 2;
                                b.Hours = decimal.Parse(cells[Array.IndexOf(headers, "TUESDAY")]);
                                b.RtsType_Id = rtsTypeId;
                                b.Site_Id = siteId;

                                incomingAvailabilityDefault.Add(b);

                                c.Weekday = 3;
                                c.Hours = decimal.Parse(cells[Array.IndexOf(headers, "WEDNESDAY")]);
                                c.RtsType_Id = rtsTypeId;
                                c.Site_Id = siteId;

                                incomingAvailabilityDefault.Add(c);

                                d.Weekday = 4;
                                d.Hours = decimal.Parse(cells[Array.IndexOf(headers, "THURSDAY")]);
                                d.RtsType_Id = rtsTypeId;
                                d.Site_Id = siteId;

                                incomingAvailabilityDefault.Add(d);

                                e.Weekday = 5;
                                e.Hours = decimal.Parse(cells[Array.IndexOf(headers, "FRIDAY")]);
                                e.RtsType_Id = rtsTypeId;
                                e.Site_Id = siteId;

                                incomingAvailabilityDefault.Add(e);

                                f.Weekday = 6;
                                f.Hours = decimal.Parse(cells[Array.IndexOf(headers, "SATURDAY")]);
                                f.RtsType_Id = rtsTypeId;
                                f.Site_Id = siteId;

                                incomingAvailabilityDefault.Add(f);

                                g.Weekday = 7;
                                g.Hours = decimal.Parse(cells[Array.IndexOf(headers, "SUNDAY")]);
                                g.RtsType_Id = rtsTypeId;
                                g.Site_Id = siteId;

                                incomingAvailabilityDefault.Add(g);
                            }

                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, VHC: item{incomingCount}  {err.ToString()}";  // <----- DON'T FORGET TO UPDATE!
                                errorCount++;
                                continue;
                            }

                        }
                    }

                    List<AvailabilityDefault> linesWithSkills = incomingAvailabilityDefault.Where(x => x.RtsType_Id != defaultRtsId).ToList();

                    foreach (var item in incomingAvailabilityDefault)
                    {
                        if (item.RtsType_Id == defaultRtsId)
                        {
                            List<AvailabilityDefault> skillLines = linesWithSkills.Where(x => x.Site_Id == item.Site_Id && x.Weekday == item.Weekday).ToList();
                            decimal totalSkillHours = 0;
                            foreach (var i in skillLines)
                            {
                                totalSkillHours += i.Hours;
                            }
                            item.Hours -= totalSkillHours;
                        }
                    }




                    var newItems = incomingAvailabilityDefault;
                    newCount = newItems.Count();

                    try
                    {
                        await dapper.ExecuteAsync($"TRUNCATE TABLE [AvailabilityDefaults]", null, System.Data.CommandType.Text);
                        //db.Database.ExecuteSqlCommand("TRUNCATE TABLE [AvailabilityDefaults]");//remove all old ones
                        db.AvailabilityDefaults.AddRange(newItems);  //add them all in one go
                        db.SaveChanges();
                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed add new  {err.ToString()}" + "\r\n\r\n";
                        errorCount++;
                    }

                    logMessage.FinishDate = DateTime.UtcNow;
                    logMessage.ProcessedCount = incomingAvailabilityDefault.Count;
                    logMessage.AddedCount = newCount;
                    logMessage.RemovedCount = removedCount;
                    logMessage.ChangedCount = changedCount;
                    logMessage.IsCompleted = true;
                    logMessage.ErrorCount = errorCount;


                    Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingAvailabilityDefault.Count} item(s) interpreted and loaded.");
                    try
                    {
                        File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                        if (errorCount > 0)
                        {
                            //we have errors so use the reporter
                            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                            await CentralLoggingService.ReportError("AvailabilityDefault", logMessage, true);
                            
                        }
                        else
                        {
                            //no errors so just save the log
                            logMessage.DealerGroup_Id = 1;
                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();
                            
                        }
                    }
                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                        errorCount++;

                        await CentralLoggingService.ReportError("AvailabilityDefault", logMessage, true);
                    }

                    //trigger cache rebuild
                    await UpdateWebAppService.Trigger("AvailabilityDefaults");
                    stopwatch.Stop();
                }
                catch (Exception err)
                {
                    stopwatch.Stop();
                    errorMessage = err.ToString();

                    logMessage.FailNotes = logMessage.FailNotes + $"General file " + err.ToString();
                    errorCount++;
                    logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                    
                    await CentralLoggingService.ReportError("AvailabilityDefault", logMessage);

                }
                finally
                {
                    db.ChangeTracker.Clear();

                    LocksService.AvailabilityDefault = false;

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "RRG",
                        Environment = ConfigService.isDev == true? "Dev": "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };
                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }




            }
        }



    }







}
