<nav class="navbar">

  <nav class="generic">
    <div id="pageTitle">
      <div>
       Site Dashboard
       <span *ngIf="service.chosenSiteNames.size === 1"> - {{ service.chosenSiteNames.values().next().value }}</span>
      </div>
    </div>

    <ng-container *ngIf="service">


      <div *ngIf="service.constantsService.RetailerSites.length > 1" class="buttonGroup topDropdownButtons">

        <!-- Show / hide new vehicles -->
        <sliderSwitch text="New Vehicles" (toggle)="toggleIncludeNewVehicles()"
            [defaultValue]="service.includeNewVehicles">
        </sliderSwitch>

        <!-- Include unpublished -->
        <sliderSwitch text="Un-published Vehicles"
            (toggle)="toggleIncludeUnPublishedAds()"
            [defaultValue]="service.includeUnPublishedAds">
        </sliderSwitch>

        <!-- Lifecycle status picker -->
        <multiPicker
            *ngIf="service.allLifecycleStatuses"
            [label]="'Lifecycle Statuses'"
            [menuItems]="service.allLifecycleStatuses"
            [chosenItems]="service.chosenLifecycleStatuses"
            [onChosenItemsChange]="onChosenLifecycleStatusesChange.bind(this)">
        </multiPicker>

        <!-- Include LCVs toggle - only shown if GlobalParam is available -->
        <sliderSwitch
            *ngIf="service.showLCVToggle"
            text="Include LCVs"
            (toggle)="toggleIncludeLCVs()"
            [defaultValue]="service.includeLCVs">
        </sliderSwitch>

        <multiPicker
          *ngIf="constants.environment.showRegionFilterOnSiteDashboard"
          [label]="'Choose Regions'"
          [menuItems]="service.regionNames"
          [chosenItems]="service.chosenRegionNames"
          [onChosenItemsChange]="onChosenRegionsChange.bind(this)"></multiPicker>

        <!-- Site selector for back end-->
        <multiPicker [label]="'Choose Sites'" [menuItems]="service.siteNames" [chosenItems]="service.chosenSiteNames"
          [onChosenItemsChange]="onChosenSitesChange.bind(this)"></multiPicker>

      </div>

      <!-- Use Test Strategy (only if they have access) -->
      <sliderSwitch *ngIf="service.showTestStrategySlider" text="Use Test Strategy" (toggle)="toggleUseTestStrategy()" [defaultValue]="service.useTestStrategy"></sliderSwitch>

    </ng-container>


  </nav>

  <nav class="pageSpecific">


  </nav>
</nav>



<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constantsService.environment.customer">
  <div class="content-new">

    <div *ngIf="statsDashboard" class="dashboard-grid cols-12">

      <!-- Row 1 -->
      <div class="autotrader-tile grid-row-1-5 grid-col-1-4">
        <div class="tile-inner">
          <div class="tile-header">
            <h4>Total AutoTrader Stock</h4>
          </div>
          <div class="tile-body">
            <div id="totalStockTileInner">
              <div class="text-center">
                <h1>{{ statsDashboard.AdCounts.AdsPublished |
                  cph:'number':0 }}</h1>
                <span>Published</span>
              </div>
              <div class="text-center">
                <h1>{{ statsDashboard.AdCounts.AdsUnPublished
                  | cph:'number':0 }}</h1>
                <span>Unpublished</span>
              </div>
              <div class="text-center">
                <h1>{{ (statsDashboard.AdCounts.AdsPublished
                  +
                  statsDashboard.AdCounts.AdsUnPublished) |
                  cph:'number':0 }}
                </h1>
                <span>Total</span>
              </div>
              <div *ngIf="statsDashboard.AdCounts.AdsDmsNotPortal > 0" id="adsNotInPortal">
                +{{ statsDashboard.AdCounts.AdsDmsNotPortal }} in DMS not on portal
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="autotrader-tile grid-row-1-5 grid-col-4-7">
        <div class="tile-inner">
          <div class="tile-header">
            <h4>Vehicle Averages</h4>
          </div>
          <div class="tile-body">
            <table class="w-100 h-60">
              <tbody>
                <tr>
                  <td>
                    <h4>Price position</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.Averages.PricePosition | cph:'percent':1 }}</h4>
                  </td>
                </tr>
                <tr>
                  <td>
                    <h4>Selling price</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.Averages.SellingPrice | cph:'currency':0 }}</h4>
                  </td>
                </tr>
                <tr *ngIf="statsDashboard?.Averages?.ProfitPerVehicle !== 0">
                  <td>
                    <h4>Profit / vehicle</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.Averages.ProfitPerVehicle | cph:'currency':0 }}</h4>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="autotrader-tile grid-row-1-5 grid-col-7-10">
        <div class="tile-inner">
          <div class="tile-header">
            <h4>Advert Quality</h4>
            <button *ngIf="filteringForAdvertQuality" class="clearFilter"
              (click)="service.clearFilter(advertQualityFilters)">
              <i class="fas fa-filter"></i>
            </button>
          </div>
          <div class="tile-body">
            <table class="w-100 h-100">
              <tbody>
                <tr class="clickable" (click)="service.enableFilter('IsLowQuality')">
                  <td>
                    <h4>Low quality</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.AdCounts.LowQuality | cph:'number':0 }}</h4>
                  </td>
                </tr>
                <tr class="clickable" (click)="service.enableFilter('NoAttentionGrabber')">
                  <td>
                    <h4>No attention grabber</h4>
                  </td>
                  <td class="text-end">
                    <h4> {{ statsDashboard.AdCounts.NoAttentionGrabber | cph:'number':0 }}</h4>
                  </td>
                </tr>
                <tr class="clickable" (click)="service.enableFilter('LessThan9Images')">
                  <td>
                    <h4>< 9 images</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.AdCounts.LessThan9Images | cph:'number':0 }}</h4>
                  </td>
                </tr>
                <tr class="clickable" (click)="service.enableFilter('NoImages')">
                  <td>
                    <h4>No images</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.AdCounts.NoImages | cph:'number':0 }}</h4>
                  </td>
                </tr>
                <tr class="clickable" (click)="service.enableFilter('NoVideo')">
                  <td>
                    <h4>No video</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.AdCounts.NoVideo | cph:'number':0 }}</h4>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="autotrader-tile grid-row-1-3 grid-col-10-13">
        <div class="tile-inner">
          <div class="tile-header">
            <h4>Spark Changes</h4>
          </div>
          <div class="tile-body">
            <table class="w-100 h-80">
              <tbody>
                <tr>
                  <td>
                    <h4>Today price changes</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.PriceChanges.TodayPriceChanges | cph:'number':0 }}</h4>
                  </td>
                </tr>
                <tr>
                  <td>
                    <h4>Adverts opted out</h4>
                  </td>
                  <td class="text-end">
                    <h4>{{ statsDashboard.PriceChanges.AdvertsOptedOut | cph:'number':0 }}</h4>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="autotrader-tile grid-row-3-5 grid-col-10-13">
        <div class="tile-inner">
          <div class="tile-header">
            <h4>Spark Usage - Logins</h4>
          </div>
          <div class="tile-body">
            <div id="sparkUsageLoginsTileInner">
              <div class="text-center">
                <h1>{{ statsDashboard.Usage.UserLoginsLastWeek | cph:'number':0 }}</h1>
                <span>Last week</span>
              </div>
              <div class="text-center">
                <h1>{{ statsDashboard.Usage.UserLoginsThisWeek | cph:'number':0 }}</h1>
                <span>This week</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Row 2 -->
      <div class="autotrader-tile grid-row-5-9 grid-col-1-5">
        <div class="tile-inner">
          <div class="tile-header">
            <h4> Published Adverts by Days Listed</h4>
            <button *ngIf="service.filters.DaysListed" class="clearFilter"
              (click)="service.clearFilter(['DaysListed'])">
              <i class="fas fa-filter"></i>
            </button>
          </div>
          <div class="tile-body">
            <barChart *ngIf="advertsByDaysListed" [params]="advertsByDaysListed"
              [dataType]="dataTypes.label" [tileType]="'HorizontalBar'" [dataKey]="'advertsByDaysListed'"
              [newDataEmitter]="service.newSmallChartDataEmitter" [doesFilter]="true"
              [dashboardParams]="service.getDashboardParams()">
            </barChart>
          </div>
        </div>
      </div>

      <div class="autotrader-tile grid-row-5-9 grid-col-5-9">
        <div class="tile-inner">
          <div class="tile-header">
            <h4>Published Adverts by Retail Rating</h4>
            <button *ngIf="service.filters.RetailRating" class="clearFilter"
              (click)="service.clearFilter(['RetailRating'])">
              <i class="fas fa-filter"></i>
            </button>
          </div>
          <div class="tile-body">
            <barChart *ngIf="advertsByRetailRating" [params]="advertsByRetailRating"
              [dataType]="dataTypes.label" [tileType]="'HorizontalBar'" [dataKey]="'advertsByRetailRating'"
              [newDataEmitter]="service.newSmallChartDataEmitter" [doesFilter]="true"
              [dashboardParams]="service.getDashboardParams()">
            </barChart>
          </div>
        </div>
      </div>

      <div class="autotrader-tile grid-row-5-9 grid-col-9-13">
        <div class="tile-inner">
          <div class="tile-header">
            <h4>Performance Rating</h4>
            <button *ngIf="service.filters.PerformanceRating" class="clearFilter"
              (click)="service.clearFilter(['PerformanceRating'])">
              <i class="fas fa-filter"></i>
            </button>
          </div>
          <div class="tile-body">
            <barChart *ngIf="performanceRating" [params]="performanceRating"
              [dataType]="dataTypes.label" [tileType]="'HorizontalBar'" [dataKey]="'performanceRating'"
              [newDataEmitter]="service.newSmallChartDataEmitter" [doesFilter]="true"
              [dashboardParams]="service.getDashboardParams()">
            </barChart>
          </div>
        </div>
      </div>

      <!-- Row 3 -->
      <div class="autotrader-tile grid-row-9-15 grid-col-1-5">
        <div class="tile-inner">
          <div class="tile-header">
            <h4>Vs Strategy Price</h4>
            <button *ngIf="service.filters.VsStrategyBanding" class="clearFilter"
              (click)="service.clearFilter(['VsStrategyBanding'])">
              <i class="fas fa-filter"></i>
            </button>
          </div>
          <div class="tile-body">
            <barChart *ngIf="vsStrategyPrice" [params]="vsStrategyPrice" [dataType]="dataTypes.label"
              [tileType]="'HorizontalBar'" [dataKey]="'vsStrategyPrice'"
              [newDataEmitter]="service.newSmallChartDataEmitter" [doesFilter]="true"
              [dashboardParams]="service.getDashboardParams()">
            </barChart>
          </div>
        </div>
      </div>

      <div class="autotrader-tile grid-row-9-15 grid-col-5-13">
        <div class="tile-inner">
          <div class="tile-header">
            <h4 class="d-flex">
              Vehicle Detail {{ getRowCount }}
              <button *ngIf="!isFiltered" class="clearFilter"
                (click)="service.clearAllFilters()">
                <i class="fas fa-filter"></i>
              </button>
            </h4>
            <button *ngIf="hasFilters()" class="btn btn-primary" (click)="service.goToStockReports()">
              Open Stock Report
            </button>
          </div>
          <div class="tile-body">
            <statsDashboardTable [tableParams]="tableParams"></statsDashboardTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>