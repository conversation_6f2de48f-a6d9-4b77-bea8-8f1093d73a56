import { Component, EventEmitter, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardDataPackSpainAftersales, DashboardDataPackSpainAftersalesDetail, DashboardDataSpainAftersalesParams } from '../../dashboard.model';
import { DashboardService } from '../../dashboard.service';
import { DashboardPageNew } from '../../dashboard.component';


@Component({
  selector: 'dashboardAftersalesSpainDetail',
  templateUrl: './dashboardAftersalesSpainDetail.component.html',
  styleUrls: ['./dashboardAftersalesSpainDetail.component.scss']
})


export class DashboardAftersalesSpainDetailComponent implements OnInit {

  weekStart: Date;
  dataPack: DashboardDataPackSpainAftersalesDetail;
  sub: Subscription;

  newDataEmitter:EventEmitter<void>

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public service: DashboardService

  ) {


  }



  ngOnDestroy() {
    if(!!this.sub)this.sub.unsubscribe();
   }


  ngOnInit() {

    this.service.getSpainMonths();

    this.initParams()
    this.getData();

    this.sub = this.service.getNewDataTrigger.subscribe(res=>{
      this.getData();
    })

  }

  isCurrentMonthSelected(): boolean {
    return this.service.chosenMonthStart.getMonth() == this.constants.todayStart.getMonth()
    &&
    this.service.chosenMonthStart.getFullYear() == this.constants.todayStart.getFullYear()
  }

  initParams() {
    this.service.hideReportButtons = true;
    this.newDataEmitter = new EventEmitter();
    this.weekStart = this.constants.startOfThisWeek();
  }


  getData() {

    this.service.currentMonthSelected = this.isCurrentMonthSelected();

    //console.log(this.service.chosenSites, "this.service.chosenSites for parms!");

    let parms: DashboardDataSpainAftersalesParams = {
      SiteIds: this.service.chosenSites.map(x => x.SiteId).join(','),
      MonthStart:this.service.chosenMonthStart,
    }

    this.getDataService.getDashboardDataSpainAftersalesDetail(parms).subscribe((res: DashboardDataPackSpainAftersalesDetail) => {

      this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

      if (!this.dataPack) {
        this.dataPack = res;
        console.log(this.dataPack, "this.dataPack!");
        
      } else {
        Object.assign(this.dataPack, res)
      }

      //in order for the template to update the object it passes through the input tag to the child tile.   else the child tile regenerates its data again from the old dataPack
      setTimeout(() => {
        this.newDataEmitter.emit();
        this.selections.triggerSpinner.next({ show: false });
      }, 50)
    })

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
  }


  choosePage(page:DashboardPageNew){
    this.service.chosenPage = page;
    this.service.singleLineNav = false;
  }




}
