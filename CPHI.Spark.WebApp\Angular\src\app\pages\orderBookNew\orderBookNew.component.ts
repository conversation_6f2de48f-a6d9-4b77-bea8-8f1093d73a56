import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { GetContextMenuItemsParams, GetMainMenuItemsParams, MenuItemDef, ValueGetterParams } from 'ag-grid-community';
import { Observable, Subscription } from 'rxjs';
import { AuditPassComponent } from 'src/app/_cellRenderers/auditPass.component';
import { CommentsCellComponent } from 'src/app/_cellRenderers/commentsCell.component';
import { ProductsHeaderComponent } from 'src/app/_cellRenderers/productsHeader.component';
import { CustomiseColumnsModalComponent } from 'src/app/components/customiseColumnsModal/customiseColumnsModal.component';
import { DatePickerModalComponent } from 'src/app/components/datePickerModal/datePickerModal.component';
import { DealDetailsComponent } from 'src/app/components/dealDetails/dealDetails.component';
import { DealInputModalComponent } from 'src/app/components/dealInputModal/dealInputModal.component';
import { CPHAutoPriceColDef } from 'src/app/model/CPHColDef';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { OrderbookParams } from 'src/app/model/OrderbookParams';
import { StockPricingSitesOverview } from 'src/app/model/StockPricingSitesOverview';

import { OrderbookRow } from "../../model/OrderbookRow";
import { Day, ExcelChoices, ExcelReportNames, Month, OrderbookTimePeriod, ProfilePicSize, SiteVM, Week } from '../../model/main.model';
import { CommentTextAndDeal, LateCostOption, OrderOption } from '../../model/sales.model';
import { OrderBookNewService } from './orderBookNew.service';


interface quickSearch {
  value: string;
  description: string;
}

@Component({
  selector: 'orderBookNew',
  templateUrl: './orderBookNew.component.html',
  styleUrls: ['./orderBookNew.component.scss']
})


export class OrderBookNewComponent implements OnInit {
  // 
  @ViewChild('topSection', { static: true }) topSection: ElementRef;
  @ViewChild('navbar', { static: true }) navbar: ElementRef;
  
  timePeriod = OrderbookTimePeriod; //this is a reference to an enum, cannot be removed

  months: Array<Month>;
  weeks: Array<Week>;
  days: Array<Day>;
  quickSearches: quickSearch[];
  
  deliveryOptions: string[];
  
  profilePicSize: ProfilePicSize;
  subscriptionComments: Subscription;
  subscriptionToTodayDealsClick: Subscription;

  constructor(
    public service: OrderBookNewService,
  ) {}


  ngOnInit() {

    this.profilePicSize = ProfilePicSize.small;
    //set height of grid
    //this.gridHeight =  window.innerHeight - this.topSection.nativeElement.offsetHeight - 40;
    
    

    // If service isn't already initiated, do that
    if (!this.service.accountingDate) {
      this.service.initOrderbook()
    }


    this.initParams()
    this.buildSubscriptions();
    this.getData()

    this.service.orderBookComponent = this;
  }

  ngOnDestroy() {
    if (this.subscriptionComments) { this.subscriptionComments.unsubscribe() }
    if (this.subscriptionToTodayDealsClick) { this.subscriptionToTodayDealsClick.unsubscribe() }
    if(this.service.tableLayoutManagementParams){
      this.service.tableLayoutManagementParams.gridApi = null;
      this.service.tableLayoutManagementParams.gridColumnApi = null;
      this.service.tableLayoutManagementParams.originalColDefs = null;
    }
    this.service.rows = null;
  }



  getScreenSize(event?) {
    return window.innerHeight;
  }



  buildSubscriptions() {
    this.subscriptionComments = this.service.selections.commentsChanged.subscribe(res => {
      this.getData();
    })

    this.subscriptionToTodayDealsClick = this.service.selections.clickedTodayOrders.subscribe(res => {
      this.getData();
    })
  }


  

  openDatePickerModal(isOrderDates: boolean) {
    const modalRef = this.service.modalService.open(DatePickerModalComponent);
    //I give to modal
    modalRef.componentInstance.heading = `Choose ${isOrderDates ? 'Order' : 'Delivery'} Dates`;

    if (isOrderDates && this.service.orderDate.timePeriod === OrderbookTimePeriod.Custom) {
      modalRef.componentInstance.fromDate = this.service.orderDate.startDate.toISOString().split('T')[0]
      modalRef.componentInstance.toDate = this.service.orderDate.endDate.toISOString().split('T')[0]
    }

    if (!isOrderDates && this.service.accountingDate.timePeriod === OrderbookTimePeriod.Custom) {
      modalRef.componentInstance.fromDate = this.service.accountingDate.startDate.toISOString().split('T')[0]
      modalRef.componentInstance.toDate = this.service.accountingDate.endDate.toISOString().split('T')[0]
    }

    modalRef.result.then((result) => { //I get back from modal
      if (result) {
        if (isOrderDates) {
          this.service.orderDate.startDate = result.startDate;
          this.service.orderDate.endDate = result.endDate;
          this.service.orderDate.timePeriod = OrderbookTimePeriod.Custom;
          this.getData();
        } else {
          this.service.accountingDate.startDate = result.startDate;
          this.service.accountingDate.endDate = result.endDate;
          this.service.accountingDate.timePeriod = OrderbookTimePeriod.Custom;
          this.getData();
        }
      }
    });
  }


 

  initParams() {

    this.deliveryOptions = ['All', 'Delivered', 'Undelivered']
    this.service.searchTerm = new UntypedFormControl();

    if (!this.service.selections.selectedSites) {
      this.service.selections.selectedSites = this.service.constants.Sites
    }


    this.quickSearches = [
      { value: 'highMargin', description: this.service.constants.translatedText.Orderbook_OrdersOver2k },
      { value: 'losingMoney', description: this.service.constants.translatedText.Orderbook_OrdersLosingMoney },
      { value: null, description: this.service.constants.translatedText.Orderbook_EmailForSupport },
    ]
  }

  public updateFilterWith(quickSearch: quickSearch) {
    if (!quickSearch.value) { return }
    this.service.specialFiltersMenuItemChosen = quickSearch.value;
    this.getData();
  }

  public clearFilter() {
    this.service.specialFiltersMenuItemChosen = '';
    this.getData();
  }

  public clearSearchTerm() {
    this.service.searchTerm.setValue("");
    this.service.tableLayoutManagementParams.gridApi.setQuickFilter(this.service.searchTerm.value)

  }

  public setDeliveryFilterTo(option: string) {
    this.service.deliveryOption = option;
    this.getData();
  }

  // refilterData() {
  //   this.service.rowsFiltered = this.service.filterForUserChoices(this.service.rows);
  // }







  public getDetailedExcel() {
    let dealIds: number[] = [];
    this.service.tableLayoutManagementParams.gridApi.forEachNodeAfterFilter(node => {
      if (!!node.data) {
        dealIds.push(node.data.DealId)
      }
    })

    let params: ExcelChoices = {
      DealIds: dealIds,
      SheetName: "Orderbook",
      WeekStart: null
    }

    this.service.getDataMethods.getDetailedExcel(ExcelReportNames.DetailedOrderbook, params)
  }

  public highlightIfDateTypeIs(type: string) {
    return this.service.accountingDate?.dateType == type;// 'accounting'
  }

 



  
  getMainMenuItems(params: GetMainMenuItemsParams<any, any>): (string | MenuItemDef)[] {
    return []
  }
 

  




 


 


 




 


  updateAccountingDatesIfNeeded() {
    if (this.service.accountingDate.timePeriod === OrderbookTimePeriod.Anytime) {
      this.service.accountingDate.startDate = this.service.constants.addYears(this.service.orderDate.startDate, -1);
      this.service.accountingDate.endDate = this.service.constants.addYears(this.service.constants.todayStart, 2);
    }
  }


  updateOrderDatesIfNeeded() {
    if (this.service.orderDate.timePeriod === OrderbookTimePeriod.Anytime) {
      this.service.orderDate.startDate = this.service.constants.addYears(this.service.constants.todayStart, -4);// this.service.deliveryDate.startDate;
      this.service.orderDate.endDate = new Date(Math.min(this.service.constants.todayStart.getTime(), this.service.accountingDate.endDate.getTime()));
    }
  }

  //Order Date Stuff
  public selectOrderAnytime() {
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Anytime;
    this.updateOrderDatesIfNeeded();
    this.getData();
  }
  public selectOrderMonth(date: Date) {
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Month;
    this.service.orderDate.lastChosenMonthStart = date;
    this.service.orderDate.startDate = date;
    this.service.orderDate.endDate = this.service.constants.endOfMonth(date)

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getData();
  }
  public selectOrderWeek(date: Date) {
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Week;
    this.service.orderDate.lastChosenWeekStart = date;
    this.service.orderDate.startDate = date;
    this.service.orderDate.endDate = this.service.constants.endOfWeek(date)

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getData();
  }
  public selectOrderDay(date: Date) {
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Day;
    this.service.orderDate.lastChosenDay = date;
    this.service.orderDate.startDate = date;
    this.service.orderDate.endDate = date;

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getData();
  }
  public changeOrderDay(changeAmount: number) {
    this.service.orderDate.startDate = this.service.constants.addDays(this.service.orderDate.lastChosenDay, changeAmount);
    this.service.orderDate.lastChosenDay = this.service.orderDate.startDate;
    this.service.orderDate.endDate = this.service.orderDate.startDate;
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Day;

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getData();
  }
  public changeOrderWeek(changeAmount: number) {
    this.service.orderDate.startDate = this.service.constants.addDays(this.service.orderDate.lastChosenWeekStart, changeAmount * 7);
    this.service.orderDate.endDate = this.service.constants.endOfWeek(this.service.orderDate.startDate);
    this.service.orderDate.lastChosenWeekStart = this.service.orderDate.startDate;
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Week;

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getData();
  }

  public changeOrderMonth(changeAmount: number) {
    this.service.orderDate.startDate = this.service.constants.addMonths(this.service.orderDate.lastChosenMonthStart, changeAmount);
    this.service.orderDate.endDate = this.service.constants.endOfMonth(this.service.orderDate.startDate);
    this.service.orderDate.lastChosenMonthStart = this.service.orderDate.startDate;
    this.service.orderDate.timePeriod = OrderbookTimePeriod.Month;

    //also have to change the orderDate if anytime chosen
    this.updateAccountingDatesIfNeeded();
    this.getData();
  }



  //Delivery Date Stuff
  public selectAccountingAnytime() {
    this.service.accountingDate.timePeriod = OrderbookTimePeriod.Anytime;
    this.updateAccountingDatesIfNeeded();
    this.getData();
  }

  public selectAccountingMonth(date: Date, name: string) {

    if (name != this.service.constants.translatedText.FinanceAddons_YearToDate && name != this.service.constants.translatedText.FinanceAddons_FullPriorYear) {
      this.service.accountingDate.timePeriod = OrderbookTimePeriod.Month;
      this.service.accountingDate.lastChosenMonthStart = date;
      this.service.accountingDate.lastChosenMonthName = this.service.cphPipe.transform(date, "month", 0);
      this.service.accountingDate.startDate = date;
      this.service.accountingDate.endDate = this.service.constants.endOfMonth(date)
    }
    else {

      if (name == this.service.constants.translatedText.FinanceAddons_YearToDate) {
        this.service.accountingDate.lastChosenMonthStart = date;
        this.service.accountingDate.lastChosenMonthName = name;
        this.service.accountingDate.startDate = date;
        this.service.accountingDate.endDate = new Date();
      }
      else {
        this.service.accountingDate.lastChosenMonthStart = date;
        this.service.accountingDate.lastChosenMonthName = name;
        this.service.accountingDate.startDate = date;
        this.service.accountingDate.endDate = new Date(new Date().getFullYear(), 0, 1)
      }

    }


    //also have to change the orderDate if anytime chosen
    this.updateOrderDatesIfNeeded();
    this.getData();

  }

  public selectAccountingDay(date: Date) {
    this.service.accountingDate.timePeriod = OrderbookTimePeriod.Day;
    this.service.accountingDate.lastChosenDay = date;
    this.service.accountingDate.startDate = date;
    this.service.accountingDate.endDate = date;

    //also have to change the orderDate if anytime chosen
    this.updateOrderDatesIfNeeded();
    this.getData();
  }

  public changeAccountingDay(changeAmount: number) {

    this.service.accountingDate.startDate = this.service.constants.addDays(this.service.accountingDate.lastChosenDay, changeAmount);
    this.service.accountingDate.lastChosenDay = this.service.accountingDate.startDate;

    this.service.accountingDate.endDate = this.service.accountingDate.startDate
    this.service.accountingDate.timePeriod = OrderbookTimePeriod.Day;

    //also have to change the orderDate if anytime chosen
    this.updateOrderDatesIfNeeded();
    this.getData();
  }

  public changeAccountingMonth(changeAmount: number) {
    this.service.accountingDate.startDate = this.service.constants.addMonths(this.service.accountingDate.lastChosenMonthStart, changeAmount);
    this.service.accountingDate.endDate = this.service.constants.endOfMonth(this.service.accountingDate.startDate);
    this.service.accountingDate.lastChosenMonthStart = this.service.accountingDate.startDate;
    this.service.accountingDate.lastChosenMonthName = this.service.cphPipe.transform(this.service.accountingDate.startDate, "month", 0);
    this.service.accountingDate.timePeriod = OrderbookTimePeriod.Month;

    //also have to change the orderDate if anytime chosen
    this.updateOrderDatesIfNeeded();
    this.getData();
  }


  groupBy(arr, prop) {
    const map = new Map(Array.from(arr, obj => [obj[prop], []]));
    arr.forEach(obj => map.get(obj[prop]).push(obj));
    return Array.from(map.values());
  }


  public generatePeopleSummary(isSalesExec: boolean): void {

    let groupId = ''
    let groupName = ''
    let summaryName = ''
    if (isSalesExec) { groupId = 'SalesmanId', groupName = 'SalesmanName', summaryName = 'salesExecSummary' }
    else {groupId = 'ManagerId', groupName = 'ManagerName', summaryName = 'salesManagerSummary'}

    let groupedByExec: OrderbookRow[][] = this.groupBy(this.service.rowsFiltered, groupName);
    
    this.service[summaryName] = []
    groupedByExec.forEach(item => {
      let unitCount = this.service.constants.sum(item.map(x => x.Units));
      let productsCount = this.service.constants.sum(item.map(x => x.TotalProductCount));
      let profit = this.service.constants.sum(item.map(x => x.TotalProfit));
      this.service[summaryName].push({
        name: item[0][groupName],
        salesmanId: item[0][groupId],
        dealCount: item.length,
        products: productsCount,
        units: unitCount,
        profit: profit,
        productsPU: this.service.constants.div(productsCount, unitCount),
        profitPU: this.service.constants.div(profit, unitCount)
      })
    })

    this.service[summaryName].sort((a, b) => a.name.localeCompare(b.name));


  }

  public onUpdateSites(sites: SiteVM[]) {
    // Global stuff
    this.service.selections.selectedSites = sites;
    this.service.selections.selectedSitesIds = [];

    sites.forEach(site => {
      this.service.selections.selectedSitesIds.push(site.SiteId);
    });

    this.getData();
  }



  public onUpdateOrderTypes(orderTypes: string[]) {
    this.service.orderTypeTypes = orderTypes;
    this.getData();
  }
  public onUpdateFranchises(franchises: string[]) {
    this.service.franchises = franchises
    this.getData();
  }

  public onUpdateVehicleTypes(vehicleTypes: string[]) {
    this.service.vehicleTypeTypes = vehicleTypes
    this.getData();
  }

  public onUpdateVehicleTypesSpain(vehicleType: string) {
    if (this.service.vehicleTypeTypes.find(x => x == vehicleType)) {
      this.service.vehicleTypeTypes = this.service.vehicleTypeTypes.filter(x => x != vehicleType);
    } else {
      this.service.vehicleTypeTypes.push(vehicleType);
    }

    this.getData();
  }


 




  public selectLateCostOption(lateCostOption: LateCostOption) {
    this.service.lateCostOption = lateCostOption;
    this.getData();
  }

  public selectOrderOption(orderOption: OrderOption) {
    this.service.orderOption = orderOption;
    this.getData();
  }

  public selectSalesExec(salesExecName: string, salesExecId: number) {
    this.service.salesExecName = salesExecName;
    this.service.salesExecId = salesExecId
    this.getData();
  }

  public selectManagerExec(salesExecName: string, salesExecId: number) {
    this.service.salesManagerName = salesExecName;
    this.service.salesManagerId = salesExecName == 'UNASSIGNED' ? -1 : salesExecId;
    this.getData();
  }

  public makeMonths() {
    this.months = this.service.constants.makeMonths(0, 0)

    let YTD: Month = {
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(),
      name: this.service.constants.translatedText.FinanceAddons_YearToDate
    }

    let FullPriorYear: Month = {
      startDate: new Date(new Date().getFullYear() - 1, 0, 1),
      endDate: new Date(new Date().getFullYear(), 0, 1),
      name: this.service.constants.translatedText.FinanceAddons_FullPriorYear
    }

    this.months.push(YTD);
    this.months.push(FullPriorYear);
  }

  public makeWeeks() {
    this.weeks = this.service.constants.makeWeeks(0, 0);
  }

  public makeDays() {
    this.days = this.service.constants.makeDays(0, -8, 4, 0);
  }

  public selectAccountingDateType(dateType: string) {
    this.service.accountingDate.dateType = dateType;
    this.getData()
  }
  public launchDealInputModal() {
    const modalRef = this.service.modalService.open(DealInputModalComponent);
    modalRef.result.then((result) => { });
  }


  getData() {

    this.service.selections.triggerSpinner.emit({ show: true, message: this.service.constants.translatedText.Loading })

    this.getOrderbookRowsDirectFromServer().subscribe((res: OrderbookRow[]) => {
      //now have got the data, get any comments

      this.service.getDataMethods.getComments(res.map(x => x.StockNumber), res.map(x => x.DealId)).subscribe((comments: CommentTextAndDeal[]) => {
        comments.map(x => x.Date = new Date(x.Date))
        comments.forEach(comment => {
          let row = res.find(x => x.StockNumber === comment.StockNumber);
          if (!!row) { row.comments.push(comment) }
        })

        this.service.rows = res;

        this.service.rowsFiltered = this.service.filterForUserChoices(res);
        if (this.service.tableLayoutManagementParams.gridApi) {
          this.service.dealWithNewRowData(this.service.rowsFiltered)
        } else {
          
          //this.service.tableComponent.initiateGrid();
        }

        this.service.selections.triggerSpinner.emit()

      })



    })
  }








  getOrderbookRowsDirectFromServer(): Observable<OrderbookRow[]> {

    let myObservable: Observable<OrderbookRow[]> = new Observable(observer => {

      let parms: OrderbookParams = {
        DeliveryDateStart: this.service.accountingDate.startDate,
        DeliveryDateEnd: this.service.accountingDate.endDate,
        DeliveryDateType: this.service.accountingDate.dateType,
        OrderDateStart: this.service.orderDate.startDate,
        OrderDateEnd: this.service.orderDate.endDate,
        SiteIds: this.service.selections.selectedSites.map(x => x.SiteId),
        VehicleTypeTypes: this.service.vehicleTypeTypes,
        OrderTypeTypes: this.service.orderTypeTypes,
        Franchises: this.service.franchises,
        IncludeLateCosts: this.service.lateCostOption.includeLateCosts,
        IncludeNormalCosts: this.service.lateCostOption.includeNormalCosts,
        IncludeInvoiced: this.service.orderOption.includeInvoiced,
        IncludeOrders: this.service.orderOption.includeOrders,
        SalesmanId: this.service.salesExecId,
        ManagerId: this.service.salesManagerId
      }

      this.service.getDataMethods.getOrderbookRows(parms).subscribe((dealsIn: string[]) => {

        let deals: OrderbookRow[] = [];
        dealsIn.forEach(deal=>{
          deals.push(new OrderbookRow(deal))
        })

        observer.next(deals)
        observer.complete();
      })

    })

    return myObservable;
  }


}
