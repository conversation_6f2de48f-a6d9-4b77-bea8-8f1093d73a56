import { Component, EventEmitter, Input, OnInit } from "@angular/core";
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AutotraderService } from "src/app/services/autotrader.service";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { WipReportService } from "../../../wipReport/wipReport.service";
import { WipSummary } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";


interface WipBar {
  label: string,
  value: number,
  height: number,
  name: string,
}

@Component({
  selector: 'wipBarChart',
  templateUrl: './wipPositionTile.component.html',
  styles: [
    `
    #blockGraphWip{height: calc(100% - 2em); width: 100%; position: absolute;}

.wipBlock{width:28%;border-radius: 0.0em 0.0em 0px 0px;position:absolute;bottom:0px;}
.wipBlock .blockInner{display:flex;position:relative;height:100%;width:100%;}
.wipBlock .blockInner        .amount{position:absolute;top:-1.4em;width:100%;text-align: center;}

.wipLabel{width:28%;border-radius: 0.3em 0.3em 0px 0px;position:absolute;bottom:-2em;text-align: center;}

.wipBlock#u30{left:4%; background:var(--goodColour)}
.wipBlock#thsi{left:36%; background:var(--brightColour)}
.wipBlock#o60{right:4%; background:var(--badColour)}


.wipLabel#u30{left:4%}
.wipLabel#thsi{left:36%}
.wipLabel#o60{right:4%}




.amount{}
.label{}

#axis{width:100%;position:absolute;border-bottom:1px solid var(--grey70);bottom: 0px;}

#chartContainer {
  flex: 1;
  position: relative;
  margin: 1em;
}

.chart-disabled { 
  background: #e6e6e6; 
}

.total {
  position: absolute;
    top: 0;
    right: 0;
    
    font-weight: bold;
}

  `
  ]
})


export class WipBarChartComponent implements OnInit {
  @Input() public amLocatedWithin: string;
  @Input() public dataSource: string;
  @Input() public title: string
  @Input() public newDataEmitter: EventEmitter<void>;
  @Input() public data: WipSummary;

  @Input() public disabled?: boolean;

  bars: WipBar[];
  dashboardSubscription: Subscription;


  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public analysis: AutotraderService,
    public router: Router,
    public service: DashboardService,
    public dashboardService: DashboardService,
    public wipService: WipReportService
  ) {

  }


  ngOnInit(): void {
    this.calculateFigures();
    this.setupSubscription();
  }


  private setupSubscription() {
    if (!!this.newDataEmitter) {
      this.dashboardSubscription = this.newDataEmitter.subscribe(res => {
        setTimeout(() => {
          this.calculateFigures();
        }, 50);
      });
    }
    else {
      this.dashboardSubscription = this.service.aftersalesDashboardRedraw.subscribe(res => {
        setTimeout(() => {
          this.calculateFigures();
        }, 50);
      });
    }
  }

  ngOnDestroy() {
    if (this.dashboardSubscription) this.dashboardSubscription.unsubscribe();
  }

  calculateFigures() {

    this.bars = []

    this.bars = [
      { label: '<30 ' + this.constants.translatedText.DaysLower, name: 'u30', value: this.data.LessThanThirtyDays, height: 0 },
      { label: '30-60 ' + this.constants.translatedText.DaysLower, name: 'thsi', value: this.data.ThirtyToSixtyDays, height: 0 },
      { label: '60+ ' + this.constants.translatedText.DaysLower, name: 'o60', value: this.data.GreaterThanSixtyDays, height: 0 },
    ]

    let maxValue = 0
    this.bars.forEach(bar => {
      if (bar.value > maxValue) { maxValue = bar.value }
    })
    maxValue = maxValue * 1.1;

    this.bars.forEach(bar => {
      bar.height = bar.value / maxValue * 100;
    })
  }





  // }

  goToWipReport() : void {

    this.wipService.initParams();
    this.dashboardService.chooseDashboardPage('Wip');

    // this.selections.initiateDebts(true);
    // this.selections.debts.incomingDebtType = 'WIP';
    // this.selections.debts.chosenSiteIds = sites.map(x => x.SiteId);

    // this.selections.dashboard.dashboardRouting.next(this.constants.DashboardPages.find(x => x.Description == 'Debtors & WIP'))
  }

}


