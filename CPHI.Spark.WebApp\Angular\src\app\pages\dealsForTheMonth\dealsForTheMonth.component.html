<nav class="navbar">

  <nav class="generic" >
    <h4 id="pageTitle">
      <div class="h4" >
        {{constants.translatedText.DealsDoneThisMonth_Title}}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <ng-container>

      <vehicleTypePickerSpain
        *ngIf="constants.environment.vehicleTypePicker_showVehicleTypePickerSpain"
        [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"
        (updateVehicleTypes)="onUpdateVehicleTypes($event)"
      >
      </vehicleTypePickerSpain>

      <div class="buttonGroup topDropdownButtons">

        <!-- Site selector -->
        <ng-container *ngIf="constants.environment.dealDone_showRRGSitePicker">
          <sitePickerRRG [allSites]="constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites"
            [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePickerRRG>
        </ng-container>
        
        <ng-container *ngIf="constants.environment.dealDone_showVindisSitePicker">
          <sitePicker [allSites]="constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites"
            [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePicker>
        </ng-container>

        <!-- VehicleType selector -->
        <vehicleTypePicker *ngIf="!service.constants.environment.vehicleTypePicker_showVehicleTypePickerSpain"
        [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"
          [buttonClass]="'buttonGroupCenter'"
          (updateVehicleTypes)="onUpdateVehicleTypes($event)"></vehicleTypePicker>

        <!-- OrderType selector -->
        <orderTypePicker *ngIf="constants.environment.orderTypePicker" [orderTypeTypesFromParent]="service.orderTypeTypes"
          [buttonClass]="'buttonGroupCenter'" (updateOrderTypes)="onUpdateOrderTypes($event)"></orderTypePicker>

        <!-- Franchise selector -->
          <franchisePicker *ngIf="constants.environment.franchisePicker" [franchisesFromParent]="service.franchises" 
           [buttonClass]="'buttonGroupCenter'" (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>

        <!-- Late Costs -->
        <div ngbDropdown dropright class="d-inline-block" *ngIf="constants.environment.dealsForTheMonth_showLateCostPicker">
          <button class="buttonGroupCenter btn btn-primary"
            ngbDropdownToggle>{{service.lateCostOption?.name}}</button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let lateCostOption of constants.lateCostOptions"
              (click)="selectLateCostOptionDropdownOnClick(lateCostOption)" class="manualToggleCloseItem" ngbDropdownToggle
              ngbDropdownItem>{{lateCostOption.name}}</button>

          </div>
        </div>

        <!-- Orders options -->
        <div ngbDropdown dropright class="d-inline-block" *ngIf="constants.environment.orderTypePicker && constants.environment.dealsForTheMonth_showIncludeExcludeOrders">
          <button class="buttonGroupRight btn btn-primary"
            ngbDropdownToggle>{{service.orderOption?.name}}</button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let orderOption of constants.orderOptions" (click)="selectOrderOptionDropdownOnClick(orderOption)"
              ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>{{orderOption.name}}</button>

          </div>
        </div>


        <!-- FOR SELECTING MONTH -->
        <div class="buttonGroup">
          <!-- previousMonth -->
          <button class="btn btn-primary" (click)="changeMonthOnClick(-1)"><i
              class="fas fa-caret-left"></i></button>
          
            <!-- dropdownMonth -->
          <div ngbDropdown class="d-inline-block" [autoClose]="true">

            <button (click)="makeMonthsDropdownOnClick()" class="btn btn-primary centreButton"
              ngbDropdownToggle>{{getMonthName(service.deliveryDate)}}</button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

              <!-- the ngFor buttons -->
              <button *ngFor="let month of service.months"
                (click)="selectMonthOnClick(month)"
                ngbDropdownItem>{{getMonthName(month)}}</button>

            </div>
          </div>
          <!-- nextMonth -->
          <button class="btn btn-primary" (click)="changeMonthOnClick(1)"><i
              class="fas fa-caret-right"></i></button>
        </div>


      </div>

    </ng-container>


  </nav>





  <nav class="pageSpecific" >



  </nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">

    <div class="content-inner-new">

      <div id="dealsForMonthContainer">

        <div id="dealsForTheMonth" class="yAxisLabels" [ngClass]="constants.environment.customer">
          <div class="units">{{constants.translatedText.Units}}</div>

          <div class="metal" *ngIf="constants.environment.dealsForTheMonth_showMetal">{{constants.translatedText.Metal}}</div>

          <div class="other" *ngIf="constants.environment.dealsForTheMonth_showOther">{{constants.translatedText.Other}}</div>

          <div class="finance" *ngIf="constants.environment.dealsForTheMonth_showFinance">{{constants.translatedText.Finance}}</div>

          <div class="addOns" *ngIf="constants.environment.dealsForTheMonth_showAddons">{{constants.translatedText.AddOns}}</div>

          <div class="gpu" *ngIf="constants.environment.dealsForTheMonth_showGpu">{{constants.translatedText.GPU}}</div>

          <div class="spacer" *ngIf="constants.environment.dealsForTheMonth_showSpainSpacing"></div>
          <div class="gp">{{constants.translatedText.GP}}</div>
        </div>

        <div id="weeksContainer">
          <div class="week" [ngClass]="{'active':service.chosenWeek?.WeekName==week.WeekName}"
            *ngFor="let week of service.weekAnalyses;trackBy:trackByFunction"
            (click)="selectWeekAnalysisOnClick(week)" [ngStyle]="{'width.%':100/service?.weekAnalyses.length}">

          <div class="title" *ngIf="week.WeekName == 'MTD'">{{ constants.translatedText.DealsDoneThisMonth_Mtd }} </div>
          <div class="title" *ngIf="week.WeekName == 'Brought In'">{{ constants.translatedText.DealsDoneThisMonth_BroughtIn }} </div>
          <div class="title" *ngIf="week.WeekName.includes('Week')">{{ constants.translatedText.Week }} {{week.WeekName.slice(-1)}} </div>

          <div class="subtitle" title="{{getWeekCommencingTitle(week)}}">
            <span *ngIf="week.WeekName!==constants.translatedText.DealsDoneThisMonth_BroughtIn">{{constants.pluralise(week.DaysElapsed,constants.translatedText.DayLower,constants.translatedText.DaysLower,true)}} {{constants.translatedText.Of}}
              {{week.NumberOfDaysInWeek}}</span>
          </div>
          <div class="dealCount h3">{{week.Units|cph:'number':0}}</div>
          <div class="profit" *ngIf="constants.environment.dealsForTheMonth_showMetal">{{week.MetalProfitPU|cph:'currency':0}}</div>
          <div class="profit" *ngIf="constants.environment.dealsForTheMonth_showOther">{{week.OtherProfitPU|cph:'currency':0}}</div>

            <div class="profit" *ngIf="constants.environment.dealsForTheMonth_showFinance">
              <div class="stat">{{week.UnitsOnFinancePU|cph:'percent':0}}</div>
              {{week.FinanceProfitPU|cph:'currency':0}}
            </div>

            <div class="profit" *ngIf="constants.environment.dealsForTheMonth_showAddons">
              <div class="stat">{{week.ProductsPerUnit|cph:'number':1}}</div>
              {{week.AddOnProfitPU|cph:'currency':0}}
            </div>

            <div class="profit totalPU" *ngIf="constants.environment.dealsForTheMonth_showGpu">{{week.GPU|cph:'currency':0}}</div>
            <div class="profit totalProfit">{{week.GP|cph:'currency':0}}</div>

          </div>
        </div>

      </div>


      <div id="popOutSection">

        <div class="inner" [ngClass]="{'shown': service.showPopOut}">
          <!-- <button class="btn btn-primary small" *ngIf="chosenWeek?.WeekName=='Brought In'" (click)="showLates = !showLates" [ngClass]="{'active':showLates}">
            Show Late Costs</button> -->
          <div class="dayContainer" *ngFor="let day of service.weeklyDealBreakdown">

          <div class="dayLabel">
            <span *ngIf="service.chosenWeek.WeekName.includes('Week'); else notWeek">{{ getDayName(day.Name) }} </span>
            <span *ngIf="service.chosenWeek.WeekName == 'MTD'">{{ constants.translatedText.DealsDoneThisMonth_Mtd }} </span>
            <span *ngIf="service.chosenWeek.WeekName == 'Brought In'">{{ constants.translatedText.DealsDoneThisMonth_BroughtIn }} </span>

            <span *ngIf="day.DealCount >= 1">({{getDayDealCount(day)}}) </span>

          </div>

            <ng-container *ngFor="let deal of day.Deals let i = index;">
              
              <div class="blob gridFont boxShadowSubtle" *ngIf="!service.showLates && !deal.IsLateCost || service.showLates"
                [ngbPopover]="popContent"
                [ngClass]="{'late':deal.IsLateCost && deal.Units>-1,'good':deal.isGood,'bad':deal.isBad, 'cancelled':deal.Units==-1}"
                container="body" placement="auto" [popoverTitle]="popoverTitle">

                <div class="label"> {{deal.Customer}} </div>
                <div class="profit">{{deal.TotalNLProfit|cph:'currency':0}}</div>

                <ng-template #popContent>

                  <ng-container *ngIf="constants.environment.dealDone_showRRGPopoverContent">
                    <popoverContentRRG [dealId]="deal.Id"></popoverContentRRG>
                  </ng-container>

                  <ng-container *ngIf="constants.environment.dealDone_showVindisPopoverContent">
                    <popoverContent [dealId]="deal.Id"></popoverContent>
                  </ng-container>

                </ng-template>

                <ng-template #popoverTitle>
                  <div class="popoverHeader" (click)="openDealDetailModalOnClick(deal)">
                    {{deal.Customer + ' - ' + deal.VehicleTypeCode + ': ' + deal.OrderTypeDescription +', '+
                    deal.SitesDescription}} </div>
                </ng-template>


              </div>
            </ng-container>
          </div>
        </div>
      </div>




    </div>

  </div>
</div>