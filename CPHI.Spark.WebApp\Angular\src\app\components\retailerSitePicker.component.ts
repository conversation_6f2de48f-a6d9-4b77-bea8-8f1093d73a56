import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { ConstantsService } from '../services/constants.service';
import { RetailerSiteVM } from "../model/RetailerSiteVM";



@Component({
  selector: 'retailerSitePicker',
  template:    `
    <!-- Site selector -->
    <div ngbDropdown dropright class="d-inline-block" id="siteDropdown">
        <button [disabled]="disabled" [ngClass]="buttonClass" class=" btn btn-primary" (click)="generateSitesList()"
          ngbDropdownToggle>{{siteChosenLabel()}}</button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

        <!-- ngFor buttons -->  
        <ng-container *ngFor="let retailerSite of retailerSites">
          
        <button *ngIf="retailerSite.Id!==0" (click)="toggleItem(retailerSite)" [ngClass]="{'active':retailerSite.IsSelected}" 
        ngbDropdownItem>{{retailerSite.Name}}</button>
        </ng-container>
        

        <!-- select Total -->  
        <button class="quickSelect" (click)="quickSelectTotal()" ngbDropdownItem>{{constants.translatedText.All}}</button>

        <!-- quick select -->
          <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="selectSites()" [disabled]="noRetailerSitesSelected" [ngClass]="{'disabled':noRetailerSitesSelected}" >{{constants.translatedText.OKUpper}}</button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
          </div>

        </div>
      </div>
    
    `
  ,
  styles: [`
  @media (min-width: 0px) and (max-width: 1920px) and (hover:none) {
    #siteDropdown .dropdown-menu{columns:2}

  }  

    `]
})


export class RetailerSitePickerComponent implements OnInit {
  @Input() retailerSitesFromParent: RetailerSiteVM[];
  @Input() buttonClass: string;
  @Input() disabled: boolean;
  @Input() allRetailerSites: RetailerSiteVM[];
  @Output() updateRetailerSites = new EventEmitter<RetailerSiteVM[]>();

  public retailerSites: RetailerSiteVM[];
  public retailerSiteIds: number[];


  private singleRetailerSite: number;
  public noRetailerSitesSelected: boolean = false;
  
  constructor(
    public constants: ConstantsService,
    
  ) { }


  ngOnInit(): void { 

    this.allRetailerSites = this.constants.userRetailerSites;
    
   }


  siteChosenLabel(): string {
    // console.log(this.sitesFromParent, "this.sitesFromParent!")
    if(!this.retailerSitesFromParent) return this.constants.translatedText.Sites;
    if (this.retailerSitesFromParent.length == 0) {
      return this.constants.translatedText.SitePicker_NoSitesSelected;
    } else if (this.retailerSitesFromParent.length == 1) {
      return this.retailerSitesFromParent[0].Name
    } else if (this.retailerSitesFromParent.length < 4) {
      let siteNames = ''
      this.retailerSitesFromParent.forEach((site, i) => {
        if (i > 0) { siteNames = siteNames + ',' } //leading comma for 2nd item onwards
        siteNames = siteNames + site.Name;
      })
      return siteNames
    } else if (this.retailerSitesFromParent.length == this.allRetailerSites.length-1){
      this.retailerSitesFromParent = this.allRetailerSites;//.sort((a, b) => (a.SortOrder > b.SortOrder) ? 1 : -1);
      return this.constants.translatedText.SitePicker_AllSitesSelected;
    }else{
      return this.constants.translatedText.Sites;
    }
  }

  generateSitesList(): void {

    if(this.retailerSitesFromParent){ 
      this.retailerSiteIds = this.retailerSitesFromParent.map(x => x.Id);
    }
    else{
      this.retailerSiteIds = this.allRetailerSites.map(x => (x.Id) );
    }

    //recreate local list
    //first time only
    if(!this.retailerSites){
      this.retailerSites = this.constants.clone(this.constants.userRetailerSites);
    }

    //tag if it's selected
    this.retailerSites.forEach(s => {
      if (this.retailerSiteIds.indexOf(s.Id) > -1 && s.Id > 0) {
        s.IsSelected = true;

        if(s.Id == this.singleRetailerSite)
        {
          s.IsSelected = false;
        }
      }
      else{
        s.IsSelected = false;
      }
    })
  
  }

  
  toggleItem(item: any): void {

    let selectionCount: number = 0;

    this.retailerSites.forEach(s => {

      if(item.Id == s.Id){
        s.IsSelected = !s.IsSelected;
      }

      if(s.IsSelected)
      {
        selectionCount++;
      }
      
    })
 
    this.noRetailerSitesSelected = selectionCount == 0 ? true : false;

  }

  selectSites(): void {

    let activeSites: number = this.retailerSites.filter(x=>x.IsSelected).length;
    let siteLength = this.retailerSites.length;

    // Fixes a weird issue where deselecting one site when all sites selected
    // does not reflect on next menu open
    if(activeSites == siteLength-1){ 
      let temp = this.retailerSites.filter(x=> x.IsSelected == false);
      this.singleRetailerSite = temp[0].Id;
    }
    else{
      this.singleRetailerSite = 0;
    }

    this.updateRetailerSites.emit(this.retailerSites.filter(e => e.IsSelected));
  }

 

  quickSelectTotal(): void {

    let activeSites: number = this.retailerSites.filter(x=>x.IsSelected).length;
    let siteLength = this.retailerSites.length;

    if(activeSites == siteLength){
      this.retailerSites.forEach(s=>s.IsSelected = false)
      this.noRetailerSitesSelected = true;
    } else {
      this.retailerSites.forEach(s=>s.IsSelected = true)
      this.noRetailerSitesSelected = false;
    }
  }

  
}


