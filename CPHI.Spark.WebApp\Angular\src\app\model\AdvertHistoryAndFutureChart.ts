import { RecentSaleDetailItem } from "./RecentSaleDetailItem";


export interface AdvertHistoryAndFutureChartDTO {
    Dates: string[];
    AdvertViews: number[];
    SearchViews: number[];
    PerformanceRatings: number[];
    Valuations: number[];
    //FutureValuations: number[];
    StrategyPrices: number[];
    AdvertisedPrices: number[];
    RecentSales: RecentSaleDetailItem[][];
    DaysToSell: number;
    DaysListed: number;
}



export class AdvertHistoryAndFutureChart {
    constructor(itemIn: AdvertHistoryAndFutureChartDTO) {
        this.Dates = itemIn.Dates.map(x => new Date(x));
        this.AdvertViews = itemIn.AdvertViews;
        this.SearchViews = itemIn.SearchViews;
        this.PerformanceRatings = itemIn.PerformanceRatings;
        this.Valuations = itemIn.Valuations;
        this.StrategyPrices = itemIn.StrategyPrices;
        this.AdvertisedPrices = itemIn.AdvertisedPrices;
        this.RecentSales = itemIn.RecentSales;
        this.DaysToSell = itemIn.DaysToSell;
        this.DaysListed = itemIn.DaysListed;
    }
    Dates: Date[];
    AdvertViews: number[];
    SearchViews: number[];
    PerformanceRatings: number[];
    Valuations: number[];
    StrategyPrices: number[];
    AdvertisedPrices: number[];
    RecentSales: RecentSaleDetailItem[][];
    DaysToSell: number;
    DaysListed: number;
}


