import { Component, OnInit, ViewChild, ElementRef, Input } from "@angular/core";
import { ConstantsService } from "../../services/constants.service";
import { SelectionsService } from "../../services/selections.service";
//import { Chart } from "./../../../../node_modules/chart.js/dist/Chart.bundle.min.js";
import { Subscription } from 'rxjs';
import { VocStatCard } from "src/app/model/main.model";

@Component({
  selector: "vocCard",
  template: `
    <div id="vocCard" [ngClass]="{'borderless' : borderless , 'border' : !borderless}">
      <div *ngIf="!hideTitle" id="vocMeasure">{{ measure.Label }}</div>
      
      <div id="donutAndLineContainer">
        <div id="donutHolder">

          <div id="vocDonutNumber">
            <span *ngIf="measure.Score == undefined">{{ "-" | cph:'number':'1':false }}</span>
            <span *ngIf="measure.Score != undefined && measure.Label != 'Measures Above National Average'">
              {{ measure.Score | cph:'number':'1':false }}
            </span>
            <span *ngIf="measure.Label == 'Measures Above National Average'">{{ starsFunction(measure.Score) }}</span>
          </div>

          <div [ngClass]="{'hide' : measure.Label != 'Return To Dealer'}">
            <canvas #barChartCanvas id="vocBar" width="150" height="150"></canvas>
          </div>

          <div [ngClass]="{'hide' : measure.Label == 'Return To Dealer' || measure.Label == 'Measures Above National Average'}">
            <canvas #donutChartCanvas id="vocDonut" width="150" height="150"></canvas>
          </div>

          <div
            *ngIf="measure.Score && measure.Label != 'Measures Above National Average'"
            id="vsAverage"
            [ngClass]="{
              'vsAverageDonut': measure.Label != 'Return To Dealer',
              'vsAverageBar': measure.Label == 'Return To Dealer'
            }"
          >
            <div class="inline">
              <span
                *ngIf="measure.Label != 'Return To Dealer' && measure.Label != 'Data Quality' && measure.Label != 'Interview Completion'"
                [ngClass]="{ 'greenFont' : vsNA >= 0, 'redFont' : vsNA < 0 }"
              >
                {{ vsNA | cph:'number':'1':true }}
              </span>
              <span
                *ngIf="measure.Label == 'Return To Dealer'"
                [ngClass]="{ 'greenFont' : vsNA < 0, 'redFont' : vsNA >= 0 }"
              >
                {{ vsNA | cph:'number':'1':true }}
              </span>
              <span *ngIf="measure.Label != 'Data Quality' && measure.Label != 'Interview Completion'">
                vs National Avg.
              </span>
            </div>
          </div>
              
        </div>
      

        <div id="lineHolder">
          <div>
            <canvas #lineChartCanvas id="vocLine" width="175" height="175"></canvas>
          </div>
          <div *ngIf="measure.Score" id="vsLastMonth">
            <div class="inline">
              <span
                *ngIf="measure.Label != 'Return To Dealer'"
                [ngClass]="{ 'greenFont' : vsLM >= 0, 'redFont' : vsLM < 0 }"
              >
                {{ vsLM | cph:'number':'1':true }}
              </span>
              <span
                *ngIf="measure.Label == 'Return To Dealer'"
                [ngClass]="{ 'greenFont' : vsLM < 0, 'redFont' : vsLM >= 0 }"
              >
                {{ vsLM | cph:'number':'1':true }}
              </span>
              {{ constants.translatedText.vsLastMonth }}
            </div>
          </div>
        </div>
      </div>
    </div>
  `,

  styles: [
    `
      #vocMeasure {
        margin: 0.5em 0em 0em 0.5em;
        
        text-align:left;
      }
      #vsAverage,
      #vsLastMonth {
        
        position: absolute;
        display: flex;
        justify-content: center;
        width: 100%;
        font-weight: 500;
      }
      .vsAverageDonut {
        margin-top: 1em;
      }
      .vsAverageBar {
        margin-top: 0.5em;
      }
      #vsLastMonth {
        margin-top: 0.4em;
      }
      #vocCard {
        width: 34em;
        background-color: white;
        border-radius: 0.0em;
      }
      
      #donutAndLineContainer{display:flex;justify-content:space-between;}

      #donutHolder{
        margin: auto;
        position: relative;
        padding: 0em 0em 1em 0em;
        width: 45%;
        height: 20em;
      }

      #lineHolder {
        margin: auto;
        position: relative;
        padding: 0em 0em 1em 0em;
        width: 42%;
        height: 20em;
      }

      #vocDonutNumber {
        display: flex;
        
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 85%;
        padding: 0em 0em 0.4em 0em;
      }
      @media (max-width: 1920px){
        #vocDonutNumber{height: 80%;}
        #vocCard{width:30em}
      }
      #vocLine{
        margin: 2em 0em 0em 0em;
      }
      #vocBar{
        margin: 2.4em 0em 0em 0em;
        height: 150px !important;
      }
      .inline {
        display: inline;
      }
      .greenFont{
        color: var(--goodColour);
      }
      .redFont{
        color: var(--badColour);
      }
      .hide{
        display: none;
      }
      .border{border: 1px var(--grey60) solid;}
      .borderless{border:none}
    `,
  ],
})
export class VocCardComponent implements OnInit {
  @ViewChild("lineChartCanvas", { static: true }) lineChartCanvas: ElementRef;
  @ViewChild("donutChartCanvas", { static: true }) donutChartCanvas: ElementRef;
  @ViewChild("barChartCanvas", { static: true }) barChartCanvas: ElementRef;

  @Input() public measure: VocStatCard;
  @Input() public hideTitle: boolean;
  @Input() public borderless?: boolean;

  vsNA: number;
  vsLM: number;
  vsNAColour: string;
  vsLMColour: string;
  donutChart: any;
  lineChart: any;
  barChart: any;
  lineMax: number;
  dashboardSub: Subscription;
  vocSub: Subscription;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService
  ) {}

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: false });
    this.formatData();
    this.makeCharts();
  }

  ngOnDestroy(){
    if(this.vocSub) this.vocSub.unsubscribe()
    if(this.dashboardSub)this.dashboardSub.unsubscribe()
  }

  formatData() {
    this.vsNA = this.measure.Score - this.measure.NationalAverage;
    this.vsLM = this.measure.VsLastMonth;
    this.lineMax = 100;

    if (this.measure.Label == "Return To Dealer") {
      this.vsNAColour = this.vsNA >= 0 ? "#dc3545" : "#28a745";
      this.vsLMColour = this.vsLM >= 0 ? "#dc3545" : "#28a745";
      this.lineMax = 20;
    } else {
      this.vsNAColour = this.vsNA >= 0 ? "#28a745" : "#dc3545";
      this.vsLMColour = this.vsLM >= 0 ? "#28a745" : "#dc3545";
    }

    if (this.measure.Label == "Measures Above National Average") {
      this.lineMax = 5;
    }

    if (this.measure.Label == "Data Quality" || this.measure.Label == "Interview Completion") {
      this.vsNAColour = "rgb(3, 83, 89)";
    }
  }
  
  makeCharts(){}
  // makeCharts():void{

  //   let lineChartContext = this.lineChartCanvas.nativeElement.getContext("2d");
  //   let donutChartContext = this.donutChartCanvas.nativeElement.getContext("2d");
  //   let barChartContext = this.barChartCanvas.nativeElement.getContext("2d");

  //   this.donutChart =  new Chart(donutChartContext, {
  //     type: "pie",
  //     data: {
  //       datasets: [
  //         {
  //           data: [this.measure.Score, 100 - this.measure.Score],
  //           backgroundColor: [this.vsNAColour, "rgba(255, 255, 255, 1)"],
  //           borderColor: ["rgba(255, 255, 255, 1)"],
  //           borderWidth: 2,
  //           borderAlign: "inner",
  //           hoverBorderColor: "rgba(255, 255, 255, 1)",
  //           hoverBackgroundColor: [this.vsNAColour, "rgba(255, 255, 255, 1)"],
  //           hoverBorderWidth: "1",
  //           weight: "5",
  //         },
  //         {
  //           data: [this.measure.NationalAverage, 100 - this.measure.NationalAverage],
  //           backgroundColor: [
  //             "hsla(210,13%,40%,0.5)",
  //             "rgba(255, 255, 255, 1)",
  //           ],
  //           borderColor: ["rgba(255, 255, 255, 1)"],
  //           borderWidth: 2,
  //           borderAlign: "inner",
  //           hoverBorderColor: "rgba(255, 255, 255, 1)",
  //           hoverBackgroundColor: [
  //             "hsla(210,13%,40%,0.5)",
  //             "rgba(255, 255, 255, 1)",
  //           ],
  //           hoverBorderWidth: "1",
  //           weight: "4",
  //         },
  //       ],
  //     },
  //     options: {
  //       cutoutPercentage: 50,
  //       tooltips: { enabled: true },
  //     },
  //   });

  //   this.lineChart = new Chart(lineChartContext, {
  //     type: "line",
  //     data: {
  //       labels: this.measure.MonthlyVocScores.map(x => x.Label),
  //       datasets: [
  //         {
  //           data: this.measure.MonthlyVocScores.map(x => x.Score),
  //           borderColor: this.vsLMColour,
  //           fill: false,
  //           backgroundColor: this.vsLMColour,
  //         },
  //         {
  //           data: this.measure.MonthlyVocScores.map(x => x.NationalAverage),
  //           borderColor: "hsla(210,13%,40%,0.0)",
  //           fill: true,
  //           backgroundColor: "hsla(210,13%,40%,0.5)",
  //           pointBackgroundColor: "rgba(0,0,0,0)",
  //           pointBorderColor: "rgba(0,0,0,0)",
  //         },
  //       ],
  //     },
  //     options: {
  //       layout: {
  //         padding: {
  //           top: 5
  //         }
  //       },
  //       tooltips: { enabled: true },
  //       legend: false,
  //       scales: {
  //         yAxes: [
  //           {
  //             gridLines: { drawBorder: false },
  //             ticks: {
  //               suggestedMin: 0,
  //               suggestedMax: this.lineMax,
  //               stepSize: this.constants.div(this.lineMax,10),
  //               display: false,
  //             },
  //           },
  //         ],
  //         xAxes: [
  //           {
  //             gridLines: {
  //               display: false,
  //             },
  //           },
  //         ],
  //       },
  //     },
  //   });
    
  //   this.barChart =  new Chart(barChartContext, {
  //     type: "bar",
      
  //     data: {
  //       datasets: [
  //         {data: [this.measure.Score],
  //         backgroundColor: this.vsNAColour},
  //         {data: [this.measure.NationalAverage],
  //         backgroundColor: "hsla(210,13%,40%,0.5)"}
  //   ]
  //   },

  //     options: {
  //       tooltips: { enabled: false },
  //       legend: false,
  //       scales: {
  //         yAxes: [
  //           {
  //             gridLines: { drawBorder: false },
  //             ticks: {
  //               suggestedMin: 0,
  //               suggestedMax: 20,
  //               stepSize: 2,
  //               display: false,
  //             },
  //           },
  //         ],
  //       },
  //     }

  //   });

  // }

  starsFunction(stars){
    let stringToReturn = null;
    switch (stars) {
      case 0 : stringToReturn = ''; break;
      case 1 : stringToReturn = '⭐'; break;
      case 2 : stringToReturn = '⭐⭐'; break;
      case 3 : stringToReturn = '⭐⭐⭐'; break;
      case 4 : stringToReturn = '⭐⭐⭐⭐'; break;
      case 5 : stringToReturn = '⭐⭐⭐⭐⭐'; break;
    }
    return stringToReturn;
  }
}
