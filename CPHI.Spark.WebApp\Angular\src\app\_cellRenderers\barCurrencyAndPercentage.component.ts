import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ConstantsService } from "../services/constants.service";

@Component({
    selector: 'bar-cell',
    template: `
        <div class="barHolder">
            <div class="barActual" [ngClass]="barClass" [ngStyle]="{ 'width.%': barPercentage * 100 }">
        </div>

        <div class="barLabel left" *ngIf="barPercentage>0">{{ barPercentage | cph:'percent':0 }}</div>
        <div class="barLabel left" *ngIf="barPercentage==0"> </div>
        <div class="barLabel right">{{ value | cph:'currency':0 }}</div>
    `
    ,
    styles: [`
        .barHolder {
            width: 90%;
            height: 80%;
            display: flex;
            overflow: hidden;
            background: var(--grey90);
            margin: 0 auto;
        }
        
        .barActual { position: relative; transition: ease all 0.3s; }
        .barLabel { position: absolute; width: 80%; display: flex; align-items: center; justify-content: flex-end; top: 0; bottom: 0; }

        .ok { background-color: var(--brightColour); }
        .good { background-color: var(--goodColour); }
        .bad { background-color: var(--badColour); }
        .default { background-color: lightskyblue; }

        .barLabel.left {
            left: 7%;
            justify-content: start;
            
        }

        .barLabel.right {
            right: 7%;
            justify-content: end;
            
        }
    `]
})
export class BarCurrencyAndPercentageComponent implements ICellRendererAngularComp {

    value: number;
    barClass: string;
    barPercentage: number;

    constructor(
        public constants: ConstantsService
    ) { }

    agInit(params: any): void {
        this.value = params.value;

        if (params.data.Label != this.constants.translatedText.Total) {

            let totalValue: number = params.data[params.column.userProvidedColDef.cellRendererParams.fieldForTotal];
            this.barPercentage = (this.value / totalValue);

            if(params.column.userProvidedColDef.cellRendererParams.barClass == null)
            {
                this.barClass = this.getBarClass();
            }
            else
            {
                this.barClass = params.column.userProvidedColDef.cellRendererParams.barClass;
            }
            
        }
    }

    refresh(): boolean {
        return false;
    }

    getBarClass(): string
    {
        if(this.barPercentage > 0.75)
        {
            return 'good';
        }

        if(this.barPercentage > 0.40)
        {
            return 'ok';
        }

        return 'bad';
    }
}
