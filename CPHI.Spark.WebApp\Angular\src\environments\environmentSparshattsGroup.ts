import { SparkEnvironment } from "src/app/services/environment.service";
import packagejson from '../../package.json';

export const environmentSparshattsGroup: SparkEnvironment = {
  customer: "SparshattsGroup",
  production: true,
  version: packagejson.version,
  languagePickerOnLogin: false,
  franchisePicker: true,
  stockGroupPicker: true,
  lateCostPicker: true,
  orderTypePicker: true,
  ageingOptions: false,
  displayCurrency: "GBP",
  displayCurrencySymbol: '£',
fAndISummary_includeTargets: false,
  // Better way for this?
  bookingsBar: {
    barStyle1: true,
    barStyle2: false,
  },

  dealDetails: {
    componentName: "DealDetailsRRGComponent",
    profitTableShowSale: true,
    profitTableShowCost: true,
    profitTableShowCommission: true,
    profitTableFinanceCommText: "Finance Comm.",
    showDescription: true,
    showVehicleAge: true,
    showPaintProtection: true,
    paintProtectionText: "Paint Protect",
    showPaintProtectionCost: true,
    showPaintProtectionSale: true,
    showPhysicalLocation: false,
    showIsDealClosed: true,
    showFinanceCo: true,
    showFinanceType: false,
    showWarrantyInfo: false,
    showRCIFinanceComm: true,
    showPCPFinanceComm: false,
    showStandardsCommission: true,
    showProPlusCommission: true,
    showSelectCommission: true,
    showGapInsurance: true,
    showTyreInsurance: true,
    showAlloyInsurance: true,
    showWheelGard: true,
    showServicePlan: true,
    showWarranty: true,
    showUnits: true,
    showDeliverySite: false,
    showAdditionalAddOnProfit: true,
    showCosmeticInsuranceSale: true,
    showCosmeticInsuranceCost: true,
    showCosmeticInsuranceCommission: true,
    showVATCost: true,
  },
  usedStockTable:
  {
    vindisFormatting: false,
    tactical: true,
    exManagementCount: true,
    exDemo: true
  },
  sideMenu:
  {
    oldStockPricing: false,
    pricingHome: true,
    dashboard: false,
    orderbook: false,
    fleetOrderbook: false,
    dealsDoneThisWeek: false,
    dealsForTheMonth: false,
    whiteboard: false,
    performanceLeague: false,
    performanceTrends: false,
    scratchCards: false,
    salesIncentive: false,
    supercup: false,
    supercup2: false,
    handoverDiary: false,
    distrinet: false,
    reportPortal: false,
    stockList: false,
    stockPricing: false,
    stockInsight: true,
    leavingVehicles: false,
    pricingDashboard: true,
    siteDetailDashboard: false,
    applyStrategy: false,
    strategyBuilder: false,
    locationOptimiser: false,
    vehicleValuation: false,
    salesCommission: false,
    localBargains: false,
    salesExecReview: false,
    stockLanding: false,
    liveForecast: false,
    userMaintenance: true,
    autoPriceSiteSettings: true,

    summaryDashboard: false,
    stockReports: true,
    bulkValuation: true,
    optOuts: false,
    todayPriceChanges: true,
    leavingVehicleDetail: true,
    leavingVehicleTrends: true,
  },
  fullSideMenu:
  {
    description: " Sparshatts Group"
  },
  citNoww:
  {
    tileHeader: "CitNOWs as a Proportion of Qualifying WIPs",
    moveNissanValuesToRenault: true,
    renaultRegions: true,
    vindisRegions: false,
    excludeAudi: false,
    pcOfSalesEnquiries: true,
    pcOfInvoicedExtWips: true,
    pcOfSalesEnquiriesText: "CitNOW Summary - Videos as a % of Sales Enquiries - ",
    pcOfInvoicedExtWipsText: "CitNOW Summary - Videos as a % of Invoiced External WIPs - ",
    showSimpleCitNowPersonDetail: false,
    showVideosViewed: false,
    eDynamixView: true
  },
  dealDetailModal:
  {
    showOtherProfit: true,
    showFinanceProfit: true,
    showAddOnProfit: true,
    showTotalProfit: true,
    showStockDetailButton: false,
    //  showStockWebsiteListingButton:false,
  },
  dashboard:
  {
    sections: null,
    canChooseMonth: false,
    showStockCover: true,
    includeExtraSpainMenuButtons: false,
    showZoeSales: true,
    showHandoverDiarySummary: true,
    showCashDebts: true,
    showBonusDebts: true,
    showRenaultRegistrations: true,
    showDaciaRegistrations: true,
    showFleetRegistrations: true,
    showUsedStockMerchandising: true,
    showCommissions: true,
    showFinanceAddonPerformance: true,
    showfAndIPerformanceRRG: true,
    showVocNPSTile: true,
    showActivityLevels: true,
    showActivityOverdues: true,
    showFleetPerformance: true,
    showVoC: true,
    showServiceBookings: true,
    showCitNow: true,
    showImageRatios: false,
    showSalesmanEfficiency: true,
    showEvhc: true,
    showFinanceAddons: true,
    showRegistrations: true,
    showSimpleDealsByDay: false,
    excludeTypesFromBreakDown: ["Used"],
    showFinanceAddonsAllSites: false,
    includeDemoStockInStockHealth: false,
  },
  debts:
  {
    agedDebts: true,
    includeBonuses: true,
    simpleSitesTable: true,
    showAgedOnPicker: true,
    showBonusDebtType: true
  },
  evhcTile: "Renault",
  allGroups: ['R', 'O', 'N', 'Z', 'T', 'D'],
  allFamilyCodes: ['AxsBonus', 'BodyGrowth', 'BodyHandling', 'Captive', 'Motrio', 'OeCompMech', 'OeCompOther', 'Oil', 'Other'],
  horizontalBar: {
    title: 'ExDemo',
    exDemo: 'params.data.ExDemo',
    forRenault: true,
    forVindis: false
  },
  stockItemModal:
  {
    onlineMerchandising: true
  },
  wipsTable:
  {
    hideBookingColumn: false,
    hideDepartmentColumn: false,
    hideAccountColumn: false,
    hideDueDateOutColumn: false,
    hideWDateInColumn: false,
    hideWDateOutColumn: false,
    hidePartsColumn: false,
    hideOilColumn: false,
    hideLabourColumn: false,
    hideSubletColumn: false,
    hideProvisionColumn: false,
    hideCreatedColumn: false,
    hideNotesColumn: false,
  },
  stockTable: {
    hideTacticalColumn: false,
    hideExManagementColumn: false,
    hideExDemoColumn: false,
  },
  stockList: {
    hideStockDetailsModal: false,
    tableColumns: [
      "Id",
      "SiteDescription",
      "StockNumberFull",
      "Reg",
      "VehicleType",
      "ProgressCode",
      "DaysInStock",
      "DaysAtBranch",
      "Make",
      "Model",
      "ModelYear",
      "Description",
      "DisposalRoute",
      "PreviousUseCode",
      "Siv",
      "CarryingValue",
      "IsVatQ",
      "CapProvision",
      "StockcheckLocation",
      "SeenAtLatestStkchk",
      "PriceChanges",
      "AttentionGrabber", "WebSiteCreatedDate", "WebsitePrice", "PriceExtraLine", "DaysOnLine", "ImagesCount", "VideosCount",
      "RRGSiteItemStockId",
      "CapValue", "IsOnWebsite", "PricedProfit",
      'VariantClass',
      'VehicleTypeCode',
      'VehicleSuperType',
      'AccountStatus',
      'Colour',
      'Mileage',
      'Fuel',
      'Doors',
      'Transmission',
      'Options',
      'ShouldBePrepped',
      'IsPrepped',
      'Purchased',
      'Selling',
      'NonRecoverableCosts',
      'DealerFitAccessories',
      'OptionCosts',
      'CapID',
      'CapNotes',
      'CapCode'
    ],
    franchises: ["R", "N", "D", "A", "Z"],
  },
  sitesLeague: {
    includeToday: true,
  },
  performanceLeague: {
    hideBadges: false,
    showDeliveredButton: true,
    incLeaversButton: true,
    showExecAndManagerSelector: false,
  },
  overAgeStockTable: {
    hideDemoColumn: false,
    hideTacticalColumn: false,
    hideExManagementColumn: false,
    hideExDemoColumn: false,
    hideTradeColumn: false,
    usedColumnName: "CoreUsed"
  },
  dealPopover: {
    showMetalProfit: true,
    showOtherProfit: true,
    showFinanceProfit: true,
    showAddons: true,
    showAddonProfit: true
  },
  orderBook: {
    showNewOrderbook:false,
    showNewDealButton: false,
    ordersDescription: 'Orders approved between',
    hideDeliverySiteColumn: true,
    hideVehicleTypeColumn: false,
    hideOemReferenceColumn: true,
    hideFinanceProfitColumn: false,
    hideVehClassColumn: false,
    hideModelColumn: false,
    hideModelYearColumn: false,
    hideVehicleSourceColumn: false,
    hideDaysToDeliverColumn: true,
    hideDaysToSaleColumn: true,
    hideLocationColumn: true,
    hideIsConfirmedColumn: true,
    hideVehTypeColumn: false,
    hideIsClosedColumn: true,
    hideUnitsColumn: false,
    hideFinanceTypeColumn: true,
    hideIsLateCostColumn: false,
    hideAddonsColumn: false,
    hideDiscountColumn: true,
    hideOtherProfitColumn: false,
    hideMetalColumn: false,
    hideSalesChannel: false,
    hideComments: false,
    hideOrderAllocationDate: false,
    hideChannelColumn: true,
    hideTypeColumn: true,
    includeAccgDate: true,
    customDateTypes: ["Delivery", "Invoice", "Accounting"],
    defaultDateType: "Accounting",
    showLateCost: true,
    showOrderOptions: true,
    showAccountingDateButton: true,
    showDeliveryOptionButtons: false,
    showMetalSummary: true,
    showOtherSummary: true,
    showFinanceSummary: true,
    showInsuranceSummary: true,
    siteColumnWidth: 80,
    customerColumnWidth: 130,
    vehicleClassColumnWidth: 30,
    salesExecColumnWidth: 100,
    descriptionColumnWidth: 200,
    hideOrderDateSelection: false,
    hideAuditColumn: true,
    showManagerSelector: false,
    hideDateFactoryTransportationColumn: true,
    hideDateVehicleReconditionColumn: true,
    hideDateSiteTransportationColumn: true,
    hideDateSiteArrivalColumn: true,
    hideReservedDateColumn: true,
  },
  partsSales:
  {
    showMarginColPerc: true,
    showMarginCol: true,
    includeMarginCols: true
  },
  handoverDiary:
  {
    includeCustomerName: true,
    includeLastPhysicalLocation: false,
    includeHandoverDate: true,
    isInvoiced: true,
    isConfirmed: false,
    futureHandoversGreyedOut: false,
    showManagerSelector: false
  },
  
  partsStockDetailedTable: {
    hideCreatedColumn: false,
    partStockBarCharts1: {
      headerName: "% >1 year",
      field: "PartsStockRRG.PercentOver1yr",
      colId: "PartsStockRRG.PercentOver1yr"
    },
    partStockBarCharts2: null,
    showPartStockAgeingColumnsForRRG: true,
    showPartStockAgeingColumnsForVindis: false,
    hideOfWhichColumn: false,
    hideDeadValueColumn: false,
    hideDormantValueColumn: false,
    hideDeadProvColumn: false,
    hideDormantProvColumn: false,
    setClassesForVindis: false,
    setClassesForRRG: true
  },
  salesPerformance:
  {
    description: "Orders approved between",
    showFranchisePicker: true,
    showLateCostButtons: true,
    showIncludeExcludeOrders: true,
    showTradeUnitButtons: true,
    showMotabilityButtons: false,
    showOrderRateReportType: true,
    showCustomReportType: true,
    showAllSites: false
  },
  selectionsService: {
    ageingOptions: [
      { description: "30 days", ageCutoff: 30 },
      { description: "45 days", ageCutoff: 45 },
      { description: "60 days", ageCutoff: 60 },
      { description: "90 days", ageCutoff: 90 },
      { description: "120 days", ageCutoff: 120 },
      { description: "150 days", ageCutoff: 150 },
      { description: "180 days", ageCutoff: 180 },
    ],
    ageingOption: { description: "60 days", ageCutoff: 60 },
    deliveryDateDateType: "Accounting",
    eligibleForCurrentUserCheck: true
  },
  serviceBookingsTable: {
    showPrepHours: true,
    clickSiteEnable: true
  },
  stockReport:
  {
    showAgePicker: true,
    hideOnRRGSiteCol: false,
    initialStockReport: "Dashboard_PartsStock_UsedStock",
    seeUsedStockReport: true,
    seeAllStockReport: true,
    seeUsedMerchandisingReport: true,
    seeOverageStockReport: true,
    seeStockGraphsReport: false,
    seeStockByAgeReport: false,
    includeReservedCarsOption: false
  },
  whiteboard: {
    showConfirmed: false,
    showNotConfirmed: false,
    showFinance: true,
    showAddons: true,
    showLateCostPicker: true,
    showManagerSelector: false
  },
  serviceChannels: [
    { displayName: "Retail", name: "Retail", channelTags: ["retail"], icon: "fas fa-wrench", hasHours: true, divideByChannelName: "Retail", isLabour: false },
    { displayName: "Internal", name: "Internal", channelTags: ["internal"], icon: "fas fa-car-wash", hasHours: true, divideByChannelName: "Internal", isLabour: false },
    { displayName: "Warranty", name: "Warranty", channelTags: ["warranty"], icon: "fas fa-engine-warning", hasHours: true, divideByChannelName: "Warranty", isLabour: false },
    { displayName: "Labour", name: "Labour", channelTags: ["retail", "internal", "warranty"], isTotal: true, icon: "", hasHours: true, divideByChannelName: "Labour", isLabour: false },
    { displayName: "Tyre", name: "Tyre", channelTags: ["tyre", "sublet"], icon: "fas fa-tire", hasHours: true, divideByChannelName: "Retail", isLabour: false },
    { displayName: "Oil", name: "Oil", channelTags: ["oilWarr", "oilExt", "oilInt", "oil"], icon: "fas fa-oil-can", hasHours: true, divideByChannelName: "Retail", isLabour: false },
    { displayName: "Total", name: "Total", channelTags: ["retail", "internal", "warranty", "tyre", "oilWarr", "oilExt", "oilInt", "oil", "sublet"], isTotal: true, icon: "", hasHours: true, divideByChannelName: "Labour", isLabour: false },
  ],

  partsChannels: [
    { displayName: "Retail", name: "Retail", channelTags: ["retail", "nonfran", "network", "trade"], icon: "fas fa-wrench", channelTag: "retail", hasHours: false, divideByChannelName: "Retail", isLabour: false }, //added network in on 28Aug20
    { displayName: "Internal", name: "Internal", channelTags: ["internal"], icon: "fas fa-car-wash", channelTag: "internal", hasHours: false, divideByChannelName: "Internal", isLabour: false },
    { displayName: "Workshop Internal", name: "Workshop Internal", channelTags: ["wshopInternal"], icon: "fas fa-car-wash", channelTag: "wshopInternal", hasHours: false, divideByChannelName: "Workshop Internal", isLabour: false },
    { displayName: "Workshop Retail", name: "Workshop Retail", channelTags: ["wshopRetail"], icon: "fas fas fa-tire ", channelTag: "wshopRetail", hasHours: false, divideByChannelName: "Workshop Retail", isLabour: false },
    { displayName: "Workshop Warranty", name: "Workshop Warranty", channelTags: ["wshopWarranty"], icon: "fas fa-engine-warning", channelTag: "wshopWarranty", hasHours: false, divideByChannelName: "Workshop Warranty", isLabour: false },
    { displayName: "Total", name: "Total", isTotal: true, channelTags: ["retail", "nonfran", "internal", "wshopInternal", "wshopRetail", "wshopWarranty"], icon: "", channelTag: "total", hasHours: false, divideByChannelName: "Total", isLabour: false },
  ],
  initialPageURL: "/stockReports",

  orderBookURL: "/stockReports",
  fleetOrderbookURL: "/stockReports",
  product:
  {
    tyreInsurance: "IsTyre",
    tyreAlloyInsurance: "IsTyreAlloy",
    showAlloyInsurance: false,
  },
  dealDone: {
    showVindisSitePicker: false,
    showRRGSitePicker: true,
    showRRGPopoverContent: true,
    showVindisPopoverContent: false,
  },
  evhc: {
    showTechTable: true,
    vehiclesCheckedPercent: 100,
    workQuoted: 205,
    workSoldPercent: 65,
    eDynamixView: true,
    redWorkSoldPercent: 65,
    amberWorkSoldPercent: 25
  },
  fAndISummary: {
    processTypeAndTypeAlloy: true,
    hideAlloyColumn: true
  },
  partsStock: {
    includeOfWhichColumns: true
  },
  dealsForTheMonth: {
    showMetal: true,
    showOther: true,
    showFinance: true,
    showAddons: true,
    showGpu: true,
    showBroughtInColumn: true,
    showLateCostPicker: true,
    showIncludeExcludeOrders: true
  },

  partsStockSitesCoverTable: {
    partStockName: "PartsStockRRG",
  },


  dealsDoneThisWeek: {
    showPlotOptions: true
  },
  orderTypePickerOptions: {
    showRetail: true,
    showFleet: true
  },
  todayMap: {
    defaultPositionLat: 52.698926,
    defaultPositionLong: -1.046534,
    defaultZoom: 7,
  },
  vehicleTypePicker:
  {
    showUsed: true,
    showNew: true,
    showAll: true,
    hiddenVehicleTypes: []
  },
  userSetup: {
    hideUploadReports: true,
    hideViewReports: true,
    hideCommReview: false,
    hideCommSelf: false,
    hideSerReviewer: true,
    hideSerSubmitter: true,
    hideStockLanding: false,
    hideSuperCup: false,
    hideIsSalesExec: false,
    hideAllowReportUpload: true,
    hideAllowReportCentre: true,
    hideLiveforecast: true,
    hideCanEditExecManagerMappings: true,
    hideSalesRoles: true,
    hideTMgr: true,
    allSalesRoles: ['New', 'Used', 'None', 'NewUsed', 'Fleet'],
    canReviewStockPrices: true,
    canActionStockPrices: true,
    canEditStockPriceMatrix: true
  },
  languageSelection: false,


  //   "CoreUsed", "Demo", "ExDemo", "ExManagement", "Tactical", "Total Used"
  // ],
  serviceSummary: {
    showTableTypeSelector: true,
    defaultTableType: 'Cumulative',
    tableTypes: ['Cumulative', 'Daily'],
    defaultTimeOption: "MTD",
    timeOptions: ["MTD", "WTD", "Yesterday"],
    showTechGroupColumns: false,
  },
  partsSummary: {
    showTableTypeSelector: true,
    defaultTableType: 'Cumulative',
    tableTypes: ['Cumulative', 'Daily'],
  },
  serviceSalesDashboard: {
    onlyLabour: false
  },
  dealDetailsModal: {
    currencyDP: 0,
    costColumnTranslation: 'Common_CoS',
    dealDetailsSection: {
      showVariant: false,
      showWebsiteDiscount: true,
      showFinanceType: false,
      showOEMReference: false,
      showQualifyingPartEx: false,
      showPhysicalLocation: false,
      showIsClosed: false,
      showFinanceCo: true,
      showDescription: true,
      showUnits: true,
      showVehicleAge: true,
      showIsLateCost: true,
      showAuditPass: false,
      showInvoiceNo: false
    },
    metalProfitSection: {
      headerTranslation: 'DealDetails_TinProfit',
      showVATCost: false
    },
    otherProfitSection: {
      showRegBonus: true,
      showIntroComm: true,
      showBrokerCost: true,
      showAccessories: true,
      showPaintProtectionAccessory: false,
      showFuel: true,
      showDelivery: true,
      showStandardWarranty: true,
      showPdi: true,
      showMechPrep: true,
      showBodyPrep: true,
      showOther: false,
      showError: true,
      showTotal: true
    },
    addonsSection: {
      showPaintProtection: true,
      showWarrantyForNewCar: true
    },
    datesSection: {
      showCustomerDestinationDeliveryDate: true,
      showEnterImportCentreDate: true,
      showShipDate: true,
      showExitImportCentreDate: true,
      showAllocationDate: true,
      showDateVehicleRecondition: false,
      showDateFactoryTransportation: false,
      showDateSiteArrival: false,
      showDateSiteTransportation: false
    },
    financeProfitSection: {
      show: true,
      rciFinanceCommissionText: 'DealDetails_RciFinanceCommission',
      financeCommissionText: 'DealDetails_FinanceCommission',
      showSelectCommission: true,
      showProPlusCommission: true,
      showStandardsCommission: true
    },
    showTotalProfitExludingFactoryBonusSection: false,
    showTotalProfitSection: true
  },
  donutShowLastYearUnits: false,
  
  showNewUsedSummaryBadges: false,
  showPrepCostsWhenValuing: false,
  isSingleSiteGroup: false,

  showRotationButton:false,
  showChangePriceNowInputAlways: false,
  showLatestSnapshotDate: true,
  showApproveAutoPrices:false,
  
  menuItems: {
    dashboard_HasDashboard: false,
    dashboard_Home: false,
    dashboard_Overview: false,
    dashboard_Sales: false,
    dashboard_NewKPIs: false,
    dashboard_UsedKPIs: false,
    dashboard_Aftersales: false,
    dashboard_SiteCompare: false,
    orderbook: false,
    orderbook_HasOrderbook: false,
    orderbook_Retail: false,orderbook_Distrinet:false,
    orderbook_Fleet: false,
    operationalReports_HasOperationReports: false,
    operationalReports_DealsWeek: false,
    operationalReports_DealsMonth: false,
    operationalReports_Whiteboard: false,
    operationalReports_HandoverDiary: false,
    operationalReports_TelephoneStats: false,
    operationalReports_StockLanding: false,
    operationalReports_PerformanceTrends: false,
    salesReports_HasSalesReports: false,
    salesReports_SalesPerformance: false,
    salesReports_Alcopas: false,
    salesReports_OrderRate: false,
    salesReports_Registrations: false,
    salesReports_FAndI: false,
    salesReports_StockReports: false,
    salesReports_StockList: false,
    salesReports_Debtors: false,
    salesReports_CitNOW: false,
    salesReports_ImageRatios: false,
    salesReports_GDPR: false,
    salesReports_Activities: false,
    reportPortal: false,
    aftersalesReports_HasAftersalesReports: false,
    aftersalesReports_ServiceSales: false,
    aftersalesReports_ServiceBookings: false,
    aftersalesReports_EVHC: false,
    aftersalesReports_Upsells: false,
    aftersalesReports_WIPReport: false,
    aftersalesReports_Debtors: false,
    aftersalesReports_CitNOW: false,
    aftersalesReports_PartsSales: false,
    aftersalesReports_PartsStock: false,
    peopleReports_HasPeopleReports: false,
    peopleReports_PerformanceLeague: false,
    peopleReports_SalespersonEffeciency: false,
    peopleReports_SalespersonCommission: false,
    peopleReports_Scratchcard: false,
    peopleReports_SalesExecReview: false,
    vehiclePricing_HasVehiclePricing: true,vehiclePricing_ShowDetailedMenu:true,
    vehiclePricing_Dashboard: true,vehiclePricing_SitesLeague:true,
    vehiclePricing_StockReport: true,
    vehiclePricing_TodaysPriceChanges: true,
    vehiclePricing_OptedOutVehicles: true,
    vehiclePricing_LocationOptimiser: true,
    vehiclePricing_VehicleValuation: true,vehiclePricing_Home:true,
    vehiclePricing_BuyingOpportunities: true,
    vehiclePricing_LeavingVehicleTrends: true,
    vehiclePricing_LeavingVehicleDetail: true,
    vehiclePricing_SiteSettings: true,
    vehiclePricing_StockQuickSearch: true,
    userMaintenance: true
  },
  showNestedSideMenu: false,
  // autoprice: {
  //   defaultShowUnpublishedAds:false,
  //   defaultShowNewVehicles: false,
  //   vehicleValuationShowCostingDetail: true,
  //   lifecycleStatusDefault: ['FORECOURT','SALE_IN_PROGRESS'],
  //   applyPriceScenarios: false,
  //   separateBuyingStrategy:false,separateBuyingStrategy2:false,
  //   allowChooseNewStrategy:true,allowTestStrategy:true,
  //   vehicleTypes: null,
  //   defaultVehicleTypes: null,
  //   stockReport: {
  //     showDMSSellingPrice_Col: false,
  //     showVsDMSSellingPrice_Col: false, 
  //     showPhysicalLocation_Col: false,

  //   },
  //   defaultToDaysInStock:false,
  // },


  vehiclePricing_StockReport_showBcaColumns: false,
  dealershipBackgroundImageName: 'SparshattsGroup',
  homeIsLandingPage: true,
  showRegionFilterOnSiteDashboard: false



};