import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConstantsService } from '../services/constants.service';
import { SelectionsService } from '../services/selections.service';



@Component({
  selector: 'autoPriceSettingsModal',
  template: `
    
  <ng-template #modalRef let-modal>
      <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">{{inputModalHeader}}</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div [ngClass]="constants.environment.customer" class="modal-body alertModalBody lowHeight">
        <table>
          <tr>
            <td colspan="2">When to action price changes</td><td><input type="datetime-local"></td>
          </tr>
          <tr *ngFor="let day of daysOfWeek; index as i">
            <td><span *ngIf="i === 0">Days of the week to run</span></td><td>{{day}}</td><td><input type="checkbox"></td>
          </tr>
          <tr>
          <td colspan="2">Include bank holidays</td><td><input type="checkbox"></td>
          </tr>
          <tr>
          <td colspan="2">Change threshold</td><td><input type="number"></td>
          </tr>
          <tr>
          <td colspan="3"><instructionRow [message]="instructionRowMessage"></instructionRow></td>
          </tr>

          <tr>
          <td colspan="2">Maximum opt-out days</td><td><input type="number"></td>
          </tr>

        </table>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" (click)="modal.dismiss('Cancelled')">{{constants.translatedText.Cancel}}</button>
        <button type="button" class="btn btn-primary" (click)="saveChanges()">Save Changes</button>
      </div>
</ng-template>
    
   `,

  styles: [`
  table {
    width: 100%;
    table-layout: fixed;

  }
  table tr td{line-height:3em;}
  input{border: 1px solid var(--grey80)}
  
    `]
})


export class AutoPriceSettingsModalComponent  {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;
  inputModalHeader:string = "AutoPrice Settings";

  daysOfWeek: string[] = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday','Sunday'];

  instructionRowMessage: string = "Spark will only action changes that increase or decrease price by £100 or more.";
  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,

  ) { 


  }


  

  showModal() {

    this.modalService.open(this.modalRef, { windowClass: "autoPriceSettingsModal", size: 'md', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //'ok'
      //this.selections.inputModalEmitter.next(this.inputText)

    }, (reason) => {
      //cancel
      //this.selections.inputModalEmitter.next(this.inputText)

    });





  }


  saveChanges(){

    setTimeout(() => {
      this.constants.toastSuccess("Changes saved")
    }, 1000);


  }


}


