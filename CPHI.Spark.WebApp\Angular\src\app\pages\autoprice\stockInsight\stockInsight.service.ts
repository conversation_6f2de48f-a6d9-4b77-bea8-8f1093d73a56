import { DatePipe } from "@angular/common";
import { EventEmitter, Injectable } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Observable, forkJoin, BehaviorSubject } from "rxjs";
import { AutoPriceInsightsModalComponent } from "src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component";
import { BIChartTileDataType } from "src/app/components/biChartTile/biChartTile.component";
import { BlobDimension, BlobItem } from "src/app/components/blobChart/blobItem";
import { CphPipe } from "src/app/cph.pipe";
import { RetailerSite } from "src/app/model/AutoPrice.model";
import { DashboardMeasure } from "src/app/model/DashboardMeasure";
import { GetVehicleAdvertsWithRatingsParams } from "src/app/model/GetVehicleAdvertWithRatingsParams";
import { VNTileParams } from "src/app/model/VNTileParams";
import { VNTileTableRow } from "src/app/model/VNTileTableRow";
import { AnalysisDimensionTypeEnum } from "src/app/model/AnalysisDimensionTypeEnum";
import { AnalysisDimensionColourBasisEnum } from "src/app/model/AnalysisDimensionColourBasisEnum";
import { AnalysisDimension } from "src/app/model/AnalysisDimension";
import { AnalysisDimensionPipeTypeEnum } from "src/app/model/AnalysisDimensionPipeTypeEnum";
import { VehicleAdvertWithRating } from "src/app/model/VehicleAdvertWithRating";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { VehicleValutionBatch } from "src/app/model/VehicleValuationBatch";
import { ValuationBatchResult } from "src/app/model/ValuationBatchResult";
import { StockInsightFiltersForStockReport } from "src/app/model/StockInsightFiltersForStockReport";
import { BITilesService } from "src/app/services/biTiles.service";
import { AutoPriceInsightsModalService } from "src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service";
import { AutotraderService } from "src/app/services/autotrader.service";
import { GlobalParamsService } from "src/app/services/globalParams.service";
import { GlobalParamKey } from "src/app/model/GlobalParam";


@Injectable({
  providedIn: 'root'
})

export class StockInsightService {

  popoverPosition: string;
  //chosenRetailerSiteIds: number[] = [18]
  userRetailerSites: RetailerSite[];
  showLeavingVehicles: boolean;

  filterChoices: DashboardMeasure[]
  highlightChoices: DashboardMeasure[];

  allLifecycleStatuses: string[] = null;
  chosenLifecycleStatuses: Set<string>;

  useTestStrategy: boolean = false;
  showTestStrategySlider: boolean = false;

  summaryStats: {
    vehicleCount: number;
    averagePerformanceRating: number;
    averageRetailRating: number;
    averageValuation: number;
    averagePrice: number;
    averageProfit: number;
    averageDaysListed: number;
    pricePosition: number;
    averageDaysInStock: number;
    prepCost: number;
  }

  rawData: VehicleAdvertWithRating[];
  get rawDataFilteredForNavbarChoices(): VehicleAdvertWithRating[] {
    return this.rawData.filter(
      x => (this.chosenVehicleTypes == null || this.chosenVehicleTypes.has(x.VehicleTypeDesc)) &&
      (this.chosenLifecycleStatuses == null || this.chosenLifecycleStatuses.has(x.LifecycleStatus))
    );
  };
  rawDataFiltered: VehicleAdvertWithRating[];
  rawDataHighlighted: VehicleAdvertWithRating[]; //subset of filtered.  

  get useDaysInStock(){
    const ageDaysListedFrom = this.globalParamsService.getGlobalParam(GlobalParamKey.ageDaysListedFrom);
      return ageDaysListedFrom=='stockDate'
  }
  // months: Date[];

  refreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
  refreshTileEmitter: EventEmitter<void> = new EventEmitter();
  highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  vehicleTableDataUpdatedEmitter: EventEmitter<BlobItem[]> = new EventEmitter();
  filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  newBlobsEmitter: EventEmitter<BlobItem[]> = new EventEmitter();
  blobItems: BlobItem[];

  xAnalysisDimension: AnalysisDimension;// = { Field: 'RetailRating', SortByNumericBand:false, Label: 'Retail Rating', Type: AnalysisDimensionTypeEnum.Number,ColourBasis:AnalysisDimensionColourBasisEnum.HigherBetter,PipeType:AnalysisDimensionPipeTypeEnum.Number0dp }
  yAnalysisDimension: AnalysisDimension;// = { Field: 'AdSiteName', SortByNumericBand:false, Label: 'Retailer Name', Type:  AnalysisDimensionTypeEnum.String, ColourBasis:AnalysisDimensionColourBasisEnum.None,PipeType:AnalysisDimensionPipeTypeEnum.None }

  analysisDimensions: AnalysisDimension[];
  //allDimensions: AnalysisDimension[]

  vehicleAdvertPopover: {
    title: string;
    vehicleAdvertDetails: VehicleAdvertWithRating;
    xDimension: BlobDimension;
    yDimension: BlobDimension;
  }

  viewByBulkUpload: boolean;
  vehicleValuationBatches: VehicleValutionBatch[];
  chosenVehicleValuationBatch: VehicleValutionBatch;
  vehicleValuationBatchResults: ValuationBatchResult[];
  vehicleValuationBatchResultsHighlighted: ValuationBatchResult[];
  vehicleValuationBatchResultsFiltered: ValuationBatchResult[];
  stockInsightFiltersForStockReport: StockInsightFiltersForStockReport;
  showInactiveBlobs: boolean = false;
  chosenEffectiveDate: string;

  includeNewVehicles: boolean;
  includeUnPublishedAdverts: boolean;

  chosenVehicleTypes: Set<string>;

  constructor(
    public constants: ConstantsService,
    public getDataService: GetDataMethodsService,
    public selections: SelectionsService,
    public gridHelpers: AGGridMethodsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    private apiAccessService: ApiAccessService,
    private datePipe: DatePipe,
    private biTilesService: BITilesService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService,
    private globalParamsService: GlobalParamsService
  ) { }



  initParams(rebuildFiltersForStockReport?: boolean) {

    this.stockInsightFiltersForStockReport = {
      PerformanceRatingScoreBand: null,
      DaysListedBand: null,
      ValueBand: null,
      RetailerSiteName: null,
      SiteBrand: null,
      AgeBand: null,
      Model: null,
      RetailRatingBand: null
    }

    if (rebuildFiltersForStockReport) {
      for (let key in this.stockInsightFiltersForStockReport) {
        let userChoice = this.highlightChoices.find(x => x.FieldName === key);
        this.stockInsightFiltersForStockReport[key] = userChoice.ChosenValues.length === 0 ? null : userChoice.ChosenValues;
      }
    }

    if (!this.chosenEffectiveDate) { this.chosenEffectiveDate = this.datePipe.transform(this.constants.todayStart, 'yyyy-MM-dd'); }

    if (!this.chosenVehicleTypes) {
      this.resetChosenVehTypes();
    }

    if (!this.chosenLifecycleStatuses) {
      this.resetChosenLifecycleStatues();
    }
  }

  resetChosenVehTypes() {

    if (this.constants.autopriceEnvironment.defaultVehicleTypes && !this.chosenVehicleTypes) {
      this.chosenVehicleTypes = new Set(this.constants.autopriceEnvironment.defaultVehicleTypes); //to make them regenerate 
    }
    else {
      this.chosenVehicleTypes = null;
    }
    
  }

  resetChosenLifecycleStatues() {
    this.chosenLifecycleStatuses = new Set(this.constants.autopriceEnvironment.lifecycleStatusDefault); //to make them regenerate 
  }

  buildChoicesObjects() {
    this.filterChoices = [];
    this.analysisDimensions.forEach(dimension => {
      this.filterChoices.push({ FieldName: dimension.Field, FieldNameTranslation: dimension.Field, IsDate: false, ChosenValues: [] })
    })

    this.highlightChoices = [];
    this.analysisDimensions.forEach(dimension => {
      this.highlightChoices.push({ FieldName: dimension.Field, FieldNameTranslation: dimension.Field, IsDate: false, ChosenValues: [] })
    })
  }




  getPageParams(): VNTileParams {
    return {
      highlightChoices: this.highlightChoices,
      filterChoices: this.filterChoices,

      filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
      highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
      updateThisPicker: this.refreshFilterListsEmitter,
      updateThisTile: this.refreshTileEmitter,
      parentMethods: {
        buildRows: (fieldName, dataType) => this.buildTableRows(fieldName, dataType),
        highlightRow: (row, fieldName) => this.biTilesService.highlightRow(row, fieldName, this.highlightChoices),
        provideItemsList: (fieldName, isDateField) => this.provideItemsList(fieldName, isDateField),
      }
    }
  }


  // highlightRow(row: VNTileTableRow, fieldName: string, highlightChoices: DashboardMeasure[]) {
  //   this.biTilesService.highlightRow(row,fieldName,highlightChoices);

  //   let userChoice = highlightChoices.find(x => x.FieldName === fieldName);
  //   this.stockInsightFiltersForStockReport[fieldName] = userChoice.ChosenValues.length === 0 ? null : userChoice.ChosenValues;
  // }


  getData() {

    this.selections.triggerSpinner.next({ message: 'Loading...', show: true });
    let requests = [this.getAnalysisDimensionsThenData(), this.getAdvertsData()]

    forkJoin(requests).subscribe(res => {

      // this.blobItems = this.makeBlobItems(this.rawData, this.analysisDimensions);
      // this.yAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Price Position')
      // this.xAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Days Listed')
      // // this.yAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Retail Rating')
      // // this.xAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Price Position')
      // this.buildChoicesObjects()

      // this.rawDataFiltered = this.biTilesService.filterData(this.rawData, this.filterChoices);
      // this.rawDataHighlighted = this.biTilesService.filterData(this.rawDataFiltered, this.highlightChoices);

      // const allStatuses = new Set(this.rawData.map(x => x.LifecycleStatus));
      // this.allLifecycleStatuses = [...allStatuses];

      // this.newBlobsEmitter.emit(this.blobItems)
      // this.refreshFilterListsEmitter.emit();
      // this.refreshTileEmitter.emit();
      // this.recalculateSummaryStats();
      // this.selections.triggerSpinner.next({ show: false });
    }, e => {
      console.error('failed getting data')
    })
  }


  getAnalysisDimensionsThenData() {

    const pageName: string = this.viewByBulkUpload ? 'StockInsightBulk' : 'StockInsight';
    this.selections.triggerSpinner.next({ message: 'Loading...', show: true });
    this.getDataService.getAnalysisDimensions(pageName).subscribe((allDimensions: AnalysisDimension[]) => {
      this.analysisDimensions = allDimensions;
      if (this.viewByBulkUpload) {
        this.getVehicleValuationBatches();
      } else {
        this.getAdvertsData();
      }
    }, e => {
      console.error('failed getting analysis dimensions')
    })

  }

  getAdvertsData() {

    const params: GetVehicleAdvertsWithRatingsParams = {
      Reg: null,
      Vin: null,
      RetailerSiteIds: null,
      EffectiveDate: this.chosenEffectiveDate,
      UserEligibleSites: null,
      IncludeNewVehicles: this.includeNewVehicles,
      IncludeUnPublishedAdverts: this.includeUnPublishedAdverts,
      VehicleTypes: null,
      LifecycleStatuses: null, // ["DUE_IN","FORECOURT","IN STOCK NOT ON PORTAL","SALE_IN_PROGRESS"]
      UseTestStrategy: this.useTestStrategy
    };


    this.apiAccessService.post('api/AutoPrice', 'GetVehicleAdvertsWithRatings', params).subscribe((res: VehicleAdvertWithRating[]) => {

      this.rawData = res;
      
      this.yAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Price Position')
      if(this.useDaysInStock){
        this.xAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Days In Stock')
      }else{
        this.xAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Days Listed')
      }

      const allStatuses = new Set(this.rawData.map(x => x.LifecycleStatus));
      this.allLifecycleStatuses = [...allStatuses];

      this.buildChoicesObjects()

      this.rawDataFiltered = this.biTilesService.filterData(this.rawDataFilteredForNavbarChoices, this.filterChoices);
      this.rawDataHighlighted = this.biTilesService.filterData(this.rawDataFilteredForNavbarChoices, this.highlightChoices);

      this.blobItems = this.makeBlobItems(this.rawDataFiltered, this.analysisDimensions);

      this.newBlobsEmitter.emit(this.blobItems)
      this.refreshFilterListsEmitter.emit();
      this.refreshTileEmitter.emit();
      this.recalculateSummaryStats();
      this.showTestStrategySlider = this.showTestStrategySliderCheck();
      this.selections.triggerSpinner.next({ show: false });
    }, (error: any) => {
      console.error('Failed to retrieve vehicle adverts', error);
      this.selections.triggerSpinner.next({ show: false });
    });
  }

  showTestStrategySliderCheck(): boolean {
    return (
      this.constants.userRetailerSites.some(site => site.AllowTestStrategy) && // At least one site with it enabled
      this.rawData.some(row => row.TestStrategyPrice > 0) // At least one item with a price
    );
  }

  convertSetToStringArr(inputSet: Set<string>): string[] {
    // Convert the set to an array and then join it into a single comma-separated string
    const commaSeparatedString = Array.from(inputSet);

    return commaSeparatedString;
  }

  makeBlobItems(rawData: VehicleAdvertWithRating[], analysisDimensions: AnalysisDimension[]): BlobItem[] {
    let result: BlobItem[] = [];
    rawData.forEach(item => {

      let newBlobItem: BlobItem = {
        Id: item.AdId,
        Valuation: item.ValuationMktAvRetail,
        Label: `${item.Make} ${item.Model} ${item.Derivative}`,
        Dimensions: [
        ],
        IsActive: true,
        VsStrategyBanding: item.VsStrategyBanding
      }

      analysisDimensions.forEach(plotField => {
        newBlobItem.Dimensions.push(
          {
            Name: plotField.Field,
            Type: plotField.Type,
            StringValue: plotField.Type === AnalysisDimensionTypeEnum.String ? item[plotField.Field] : null,
            NumberValue: plotField.Type === AnalysisDimensionTypeEnum.Number ? item[plotField.Field] : null,
            PipeType: plotField.PipeType
          }
        )
      })

      result.push(newBlobItem)
    })
    return result;
  }


  recalculateSummaryStats() {
    let vehicleCount = 0;
    let totalPerfRating = 0;
    let totalRetRating = 0;
    let totalProfit = 0;
    let vehicleCountThoseWithValuation = 0;
    let totalValuation = 0;
    let totalPriceThoseWithValuation = 0;
    let totalPrice = 0;
    let totalPrep = 0;
    let totalDaysListed = 0;
    //let vehicleCountWithSIV = 0;
    let totalDaysInStock = 0;
    let vehicleCountWithProfit = 0;

    let data = this.rawDataHighlighted ?? this.rawData;

    if (!data) {
      this.summaryStats = {
        vehicleCount: 0,
        averagePerformanceRating: 0,
        averageRetailRating: 0,
        averageProfit: 0,
        averageValuation: 0,
        averagePrice: 0,
        //averageFirstPrice: 0,
        averageDaysListed: 0,
        pricePosition: 0,

        //firstPricePosition: 0,
        averageDaysInStock: 0,
        prepCost: 0
      }
      return;
    }

    //console.log(data, "data!");

    data.forEach(item => {
      vehicleCount++;
      totalPerfRating += item.PerfRatingScore;
      totalRetRating += item.RetailRating;
      //totalProfit += item.Profit;
      // if(item.SIV>0){
      //   vehicleCountWithSIV++;
      //   totalProfit += item.PricedProfit;
      // }

      if (item.PricedProfit != null) {
        vehicleCountWithProfit++;
        totalProfit += item.PricedProfit
      }

      if (item.PricedProfit != null) {
        totalPrep += item.PrepCost
      }

      // if (item.ValuationMktAvRetail > 0) {
        totalValuation += item.ValuationMktAvRetail;
        totalPriceThoseWithValuation += item.AdvertisedPrice;
        vehicleCountThoseWithValuation++;

      // }

      totalPrice += item.AdvertisedPrice;
      totalDaysListed += item.DaysListed;

      // const forecourtDateVsNowInMs: number = new Date().getTime() - new Date(item.DateOnForecourt).getTime();
      // totalDaysInStock += Math.floor(forecourtDateVsNowInMs / (1000 * 60 * 60 * 24));
      totalDaysInStock += this.constants.differenceInDays(this.constants.todayStart, new Date(item.DateOnForecourt));
    })

    console.log("2");
    console.log(totalPriceThoseWithValuation, "totalPriceThoseWithValuation");
    console.log(totalValuation, "totalValuation");

    this.summaryStats = {
      vehicleCount: vehicleCount,
      averagePerformanceRating: totalPerfRating / vehicleCount,
      averageRetailRating: totalRetRating / vehicleCount,
      averageProfit: totalProfit / vehicleCountWithProfit,
      averageValuation: totalValuation / vehicleCountThoseWithValuation,
      averagePrice: totalPrice / vehicleCount,
      //averageFirstPrice: totalFirstPrice / vehicleCount,
      averageDaysListed: totalDaysListed / vehicleCount,
      pricePosition: totalPriceThoseWithValuation / totalValuation,
      //firstPricePosition: totalFirstPrice / totalValuation
      averageDaysInStock: totalDaysInStock / vehicleCount,
      prepCost: totalPrep / vehicleCountWithProfit
    }

    this.initParams(true);
  }


  selectSnapshot(snapshot: Date) {
    //this.chosenMonth = snapshot;
    //this.getData()
    this.getAnalysisDimensionsThenData()
  }


  isHighlightFiltersOn() {
    let isHighlights = false;
    let i = 0;
    while (!isHighlights && i < this.highlightChoices.length) {
      isHighlights = this.highlightChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isHighlights;
  }

  isFiltersOn() {
    if (!this.filterChoices) { return false; }
    let isFilters = false;
    let i = 0;
    while (!isFilters && i < this.filterChoices.length) {
      isFilters = this.filterChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isFilters;
  }

  clearHighlights() {
    this.highlightChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.highlightItems();
    this.initParams();
    this.refreshTileEmitter.emit();
    this.highlightChoiceMadeEmitter.emit();
  }

  clearFilters() {
    this.filterChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.filterItems();
    this.initParams();
    this.refreshTileEmitter.emit();
  }



  filterItems() {
    //have chosen ok from a dropdown picker
    if (this.viewByBulkUpload) {
      this.vehicleValuationBatchResultsFiltered = this.biTilesService.filterData(this.vehicleValuationBatchResults, this.filterChoices);
    } else {
      this.rawDataFiltered = this.biTilesService.filterData(this.rawData, this.filterChoices);
    }
    this.highlightItems()
  }

  highlightItems() {
    //have clicked a row in a tile.
    if (this.viewByBulkUpload) {
      this.vehicleValuationBatchResultsHighlighted = this.biTilesService.filterData(this.vehicleValuationBatchResultsFiltered, this.highlightChoices);
    } else {
      this.rawDataHighlighted = this.biTilesService.filterData(this.rawDataFiltered, this.highlightChoices);
    }
    this.refreshTileEmitter.emit()
  }

  provideItemsList(fieldName: string, isDateField: boolean) {
    if (!this.rawData) { return [] }
    return [...new Set(this.rawData.map(x => x[fieldName]))]
  }

  buildTableRows(fieldName: string, dataType: BIChartTileDataType) {
    let tableRows: VNTileTableRow[] = []
    const data = this.viewByBulkUpload ? this.vehicleValuationBatchResults : this.rawDataFiltered;
    const dataFiltered = this.viewByBulkUpload ? this.vehicleValuationBatchResultsFiltered : this.rawDataFiltered;
    const dataHighlighted = this.viewByBulkUpload ? this.vehicleValuationBatchResultsHighlighted : this.rawDataHighlighted;
    if (dataType === BIChartTileDataType.day) {
      tableRows = this.biTilesService.buildTableRowsDatesBasis(fieldName, data, false)
    } else if (dataType === BIChartTileDataType.weekly) {
      tableRows = this.biTilesService.buildTableRowsDatesBasis(fieldName, data, true)
    }
    else {
      tableRows = this.biTilesService.buildTableRowsNonDatesBasis(fieldName, dataFiltered, dataHighlighted)
      //sort
      if (this.analysisDimensions.filter(x => x.SortByNumericBand).map(x => x.Field).includes(fieldName)) {
        tableRows = this.biTilesService.sortByNumericBand(tableRows);
      }
      else {
        tableRows = this.sortByTotal(tableRows)
      }
    }

    if (fieldName === 'ImagesBand') {
      const sortOrderForImagesBand = AutotraderService.getSortOrderForImagesBand();
      tableRows = tableRows.sort((a, b) => sortOrderForImagesBand.indexOf(a.Label) - sortOrderForImagesBand.indexOf(b.Label));
    }

    if (fieldName === 'DaysToSellBanding') {
      const sortOrderForDaysToSellBanding = AutotraderService.getSortOrderForDaysToSellBanding();
      tableRows = tableRows.sort((a, b) => sortOrderForDaysToSellBanding.indexOf(a.Label) - sortOrderForDaysToSellBanding.indexOf(b.Label));
    }

    if (fieldName === 'VsStrategyBanding') {
      const sortOrderForVsStrategyBanding = AutotraderService.getSortOrderForVsStrategyBanding();
      tableRows = tableRows.sort((a, b) => sortOrderForVsStrategyBanding.indexOf(a.Label) - sortOrderForVsStrategyBanding.indexOf(b.Label));
    }

    //limit to 20
    return tableRows;//.slice(0, 20);
  }

  private sortByTotal(tableRows: VNTileTableRow[]) {
    tableRows = tableRows.sort((a, b) => b.FilteredTotal - a.FilteredTotal);
    return tableRows;
  }

  loadModalForBlob(blob: BlobItem) {
    this.autoPriceInsightsModalService.initialise(blob.Id, [])
    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });

  }


  loadVehicleAdvertDetails(blob: BlobItem) {
    const leavingPriceSummaryItem: VehicleAdvertWithRating = this.rawData.find(x => x.AdId === blob.Id);
    const params = [
      { key: 'webSiteStockIdentifier', value: leavingPriceSummaryItem.WebSiteStockIdentifier },
      { key: 'effectiveDate', value: this.datePipe.transform(this.constants.todayStart, 'yyyy-MM-dd') }
    ]

    this.vehicleAdvertPopover = {
      title: blob.Label,
      vehicleAdvertDetails: null,
      xDimension: blob.xDimension,
      yDimension: blob.yDimension
    }

    setTimeout(() => {
      this.apiAccessService.get('api/AutoPrice', 'GetVehicleAdvertDetails', params).subscribe((res: VehicleAdvertWithRating[]) => {
        this.vehicleAdvertPopover.vehicleAdvertDetails = res[0];
      })
    }, 10)
  }

  getVehicleValuationBatches() {
    this.apiAccessService.get('api/AutoPrice', 'GetVehicleValuationBatches').subscribe((res: VehicleValutionBatch[]) => {
      this.vehicleValuationBatches = res;
      this.chosenVehicleValuationBatch = res[0];
      this.getValuationBatchResults();
    }, (error: any) => {
      console.error('Failed to retrieve vehicle valuation batches', error);
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getValuationBatchResults() {
    const params = {
      batchIds: this.chosenVehicleValuationBatch.Id.toString(),
      onlyShowBestLocation: false
    }

    this.apiAccessService.post('api/AutoPrice', 'GetValuationBatchResults', params).subscribe((res: ValuationBatchResult[]) => {
      this.vehicleValuationBatchResults = res;
      this.blobItems = this.makeBlobItemsBulk(this.vehicleValuationBatchResults, this.analysisDimensions);
      this.yAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Value Band');
      this.xAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Make');
      this.buildChoicesObjects();
      this.vehicleValuationBatchResultsFiltered = this.biTilesService.filterData(res, this.filterChoices);
      this.vehicleValuationBatchResultsHighlighted = this.biTilesService.filterData(res, this.highlightChoices);

      this.newBlobsEmitter.emit(this.blobItems);
      this.refreshFilterListsEmitter.emit();
      this.refreshTileEmitter.emit();
      this.recalculateSummaryStatsBulkUpload();
      this.selections.triggerSpinner.next({ show: false });
    }, (error: any) => {
      console.error('Failed to retrieve vehicle valuation batches', error);
      this.selections.triggerSpinner.next({ show: false });
    });
  }

  revalueVehicleValuationBatch() {
    const params = {
      batchId: this.chosenVehicleValuationBatch.Id
    }

    this.apiAccessService.post('api/AutoPrice', 'RevalueVehicleValuationBatch', params).subscribe((res: any) => {
      console.log('Revalued batch');
    }, (error: any) => {
      console.error('Failed to revalue batch', error);
      this.selections.triggerSpinner.next({ show: false });
    });
  }

  recalculateSummaryStatsBulkUpload() {
    let vehicleCount = 0;
    let totalRetRating = 0;
    let vehicleCountThoseWithValuation = 0;
    let totalValuation = 0;
    let totalPriceThoseWithValuation = 0;
    let totalPrice = 0;

    let data = this.vehicleValuationBatchResultsHighlighted ?? this.vehicleValuationBatchResults;

    if (!data) {
      this.summaryStats = {
        vehicleCount: 0,
        averagePerformanceRating: 0,
        averageRetailRating: 0,
        averageProfit: 0,
        averageValuation: 0,
        averagePrice: 0,
        averageDaysListed: 0,
        pricePosition: 0,
        averageDaysInStock: 0,
        prepCost: 0
      }
      return;
    }

    data.forEach(item => {
      vehicleCount++;
      totalRetRating += item.RetailRating;

      if (item.ValuationMktAvRetailThisVehicle > 0) {
        totalValuation += item.ValuationMktAvRetailThisVehicle;
        totalPriceThoseWithValuation += item.StrategyPrice;
        vehicleCountThoseWithValuation++;
      }

      totalPrice += item.StrategyPrice;
    })

    // console.log("2");
    // console.log(totalPriceThoseWithValuation, "totalPriceThoseWithValuation");
    // console.log(totalValuation, "totalValuation");

    this.summaryStats = {
      vehicleCount: vehicleCount,
      averagePerformanceRating: 0,
      averageRetailRating: totalRetRating / vehicleCount,
      averageProfit: 0,
      averageValuation: totalValuation / vehicleCountThoseWithValuation,
      averagePrice: totalPrice / vehicleCount,
      averageDaysListed: 0,
      pricePosition: totalPriceThoseWithValuation / totalValuation,
      averageDaysInStock: 0,
      prepCost: 0
    }

  }

  makeBlobItemsBulk(vehicleValuationBatchResults: ValuationBatchResult[], analysisDimensions: AnalysisDimension[]): BlobItem[] {
    let result: BlobItem[] = [];
    vehicleValuationBatchResults.forEach(item => {

      let newBlobItem: BlobItem = {
        Id: item.ValuationId,
        Valuation: item.ValuationMktAvRetailThisVehicle,
        Label: item.Derivative,
        Dimensions: [],
        IsActive: true,
        VsStrategyBanding: ''
      }

      analysisDimensions.forEach(plotField => {
        newBlobItem.Dimensions.push(
          {
            Name: plotField.Field,
            Type: plotField.Type,
            StringValue: plotField.Type === AnalysisDimensionTypeEnum.String ? item[plotField.Field] : null,
            NumberValue: plotField.Type === AnalysisDimensionTypeEnum.Number ? item[plotField.Field] : null,
            PipeType: plotField.PipeType
          }
        )
      })

      result.push(newBlobItem)
    })
    return result;
  }
}