<div class="dashboard-tile-inner">
    <div class="tileHeader">
        <div class="headerWords">
            <h4 *ngIf="tileType == 'New'">{{ constants.translatedText.Common_New }} {{ constants.translatedText.Common_Orders }} </h4>
            <h4 *ngIf="tileType == 'Used'">{{ constants.translatedText.Common_Used }} {{ constants.translatedText.Common_Orders }}</h4>

            <div class="buttonGroup changeWeekButtons">
                <div class="invisibleButton" (click)="changeWeek(-7)">
                    <i class="fas fa-caret-circle-left"></i>
                </div>
                <div class="invisibleButton" (click)="changeWeek(7)">
                    <i class="fas fa-caret-circle-right"></i>
                </div>
            </div>

            <div *ngIf="dataSource" class="right-aligned">
                <div *ngIf="showWarning" class="warning"></div>
                <div class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
            </div>
            
        </div>
    </div>
    <div class="dashboard-tile-body">
        <div id="daily-orders-vs-last-year-chart">
            <canvas #chartCanvas></canvas>
        </div>
    </div>
</div>