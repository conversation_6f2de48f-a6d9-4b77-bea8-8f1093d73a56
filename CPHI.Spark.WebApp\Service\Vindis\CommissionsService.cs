﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.Vindis;
using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.WebApp.Vindis;
using CPHI.Spark.BusinessLogic.Vindis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Drawing;
using System.Security.Policy;
using System.ComponentModel;
using CPHI.Spark.Model.ViewModels;

namespace CPHI.Spark.WebApp.Service.Vindis
{
    public interface ICommissionsService
    {
        Task<IEnumerable<Model.ViewModels.Vindis.CommissionSiteRow>> GetCommissionSiteRows(DateTime monthStart, bool ignoreAuditPass);
        Task<IEnumerable<CommissionPayoutPersonSummary>> GetCommissionPersonRows(DateTime monthStart, IEnumerable<int> siteIds, bool ignoreAuditPass);
        Task LockCommissionMonth(DateTime chosenMonth);
        Task<BonusSummary> GetBonusSummary(CommissionQualificationParams parms);
        Task<IEnumerable<BonusSummaryForSite>> GetBonusSummariesForSite(CommissionQualificationParams parms);
        Task<IEnumerable<BonusSummaryForPerson>> GetBonusSummariesForPeople(BonusSummaryForPeopleParams parms);
        Task<int> SetCommissionQualificationForPerson(CommissionQualificationParams parms);
        //Task<IEnumerable<CommissionRow>> GetCommissionPersonRows(DateTime monthStart, IEnumerable<int> siteIds);
    }

    public class CommissionsService : ICommissionsService
    {
        private readonly ICommissionItemsCache commissionItemsCache;
        private readonly IUserService userService;
        private readonly ISitesService sitesService;
        private readonly ICommissionsDataAccess commissionsDataAccess;

        public CommissionsService(ICommissionItemsCache commissionItemsCache, IUserService userService, ISitesService sitesService, ICommissionsDataAccess commissionsDataAccess)
        {
            this.commissionItemsCache = commissionItemsCache;
            this.userService = userService;
            this.sitesService = sitesService;
            this.commissionsDataAccess = commissionsDataAccess;
        }

        public async Task<IEnumerable<Model.ViewModels.Vindis.CommissionSiteRow>> GetCommissionSiteRows(DateTime monthStart, bool ignoreAuditPass)
        {
            int userId = userService.GetUserId();

            IEnumerable<int> userSiteIds = userService.GetUserSiteIds();// await userService.GetUserSiteIdsFomDatabase(userId, userService.GetUserDealerGroupName());

            IEnumerable<CommissionPayoutPersonSummary> statements = await commissionItemsCache.GetCommissionItems(monthStart, ignoreAuditPass);

            IEnumerable<SiteVM> sites = (await sitesService.GetSites(userId, userService.GetUserDealerGroupName())).Where(x => x.IsActive && x.IsSales 
                                                      && (!x.SiteDescription.Contains("Ducati") || monthStart.Year >= 2024)
                                                      && !x.SiteDescription.Contains("Group Fleet")
                                                      && !x.SiteDescription.Contains("CV Huntingdon")
                                                       ).OrderBy(x => x.SortOrder);

            //have to summarise them by site
            List<CommissionSiteRow> siteResults = new List<CommissionSiteRow>();
            ILookup<int, CommissionPayoutPersonSummary> resultsBySite = statements.ToLookup(x => x.SalesmanSiteId);

            foreach (var site in sites)
            {
                IEnumerable<CommissionPayoutPersonSummary> statementsForSite = resultsBySite[site.SiteId];

                //aggregate up siteItems into a site row
                Model.ViewModels.Vindis.CommissionSiteRow siteRow = new Model.ViewModels.Vindis.CommissionSiteRow()
                {
                    IsSite = true,
                    RowId = site.SiteId,
                    RegionDescription = site.RegionDescription,
                    SiteId = site.SiteId,
                    ExecsCount = 0,
                    SiteDescription = site.SiteDescription
                };

                if (statementsForSite.Count() > 0)
                {
                    foreach (var statement in statementsForSite)
                    {
                        siteRow.ExecsCount++;
                        AccumulateItemsIntoSiteRow(statement, siteRow);
                    }
                }

                siteResults.Add(siteRow);
            }

            //add regions
            List<CommissionSiteRow> regionResults = new List<CommissionSiteRow>();

            foreach (var siteGroup in sites.ToLookup(x => x.RegionDescription))
            {
                IEnumerable<CommissionSiteRow> thisRegionSites = siteResults.Where(x => siteGroup.Select(x => x.SiteDescription).Contains(x.Label));
                CommissionSiteRow regionRow = new CommissionSiteRow()
                {
                    IsRegion = true,
                    RegionDescription = siteGroup.Key
                };
                
                AccumulateRowsIntoRow(thisRegionSites, regionRow);
                regionResults.Add(regionRow);
            }

            CommissionSiteRow totalRow = new CommissionSiteRow()
            {
                IsTotal = true,
            };

            AccumulateRowsIntoRow(regionResults, totalRow);
            
            //finish up
            List<CommissionSiteRow> results = siteResults.Concat(regionResults).ToList();
            results.Add(totalRow);

            if (monthStart >= new DateTime(2024, 10, 1) && monthStart < new DateTime(2025, 1, 1)) //remove site 16 Bentley/ Three10 Automotive’
            {
                results = results.Where(x => x.SiteId != 16).ToList();
            }


            return results;
        }



        public async Task<IEnumerable<CommissionPayoutPersonSummary>> GetCommissionPersonRows(DateTime monthStart, IEnumerable<int> siteIds, bool ignoreAuditPass)
        {
            int userId = userService.GetUserId();
            IEnumerable<int> userSiteIds = userService.GetUserSiteIds();// await userService.GetUserSiteIdsFomDatabase(userId, userService.GetUserDealerGroupName());
            IEnumerable<int> siteIdsToLimitTo = userSiteIds.Intersect(siteIds);

            IEnumerable<CommissionPayoutPersonSummary> allStatements = await commissionItemsCache.GetCommissionItems(monthStart, ignoreAuditPass);
            IEnumerable<CommissionPayoutPersonSummary> statementsForSites = allStatements.Where(x => siteIdsToLimitTo.Contains(x.SalesmanSiteId)).OrderBy(x => x.SalesmanName);

            return statementsForSites;
        }

        public async Task LockCommissionMonth(DateTime chosenMonth)
        {
            IEnumerable<System.Security.Claims.Claim> claims = userService.GetUserClaims();
            string userRole = claims.First(x => x.Type == "UserRole").Value;
            if(userRole!="System Administrator")
            {
                throw new Exception("Not authorised");
            }
            await commissionsDataAccess.LockCommissionMonth(chosenMonth, userService.GetUserDealerGroupName());
            return;
        }


        private static void AccumulateItemsIntoSiteRow(CommissionPayoutPersonSummary statement, CommissionSiteRow newItem)
        {
                
                newItem.NewValue += statement.ActualEarnings.NewValue;
                newItem.UsedValue += statement.ActualEarnings.UsedValue;
                newItem.MotabValue += statement.ActualEarnings.MotabValue;
                newItem.DemoValue += statement.ActualEarnings.DemoValue;
                newItem.Cosmetic += statement.ActualEarnings.Cosmetic;
                newItem.Gap += statement.ActualEarnings.Gap;
                newItem.Paint += statement.ActualEarnings.Paint;
                newItem.ServicePlan += statement.ActualEarnings.ServicePlan;
                newItem.CarWow += statement.ActualEarnings.CarWow;
                newItem.Warranty += statement.ActualEarnings.Warranty;
                newItem.ProfitPot += statement.ActualEarnings.ProfitPot;

                newItem.TotalTargetVolume += statement.Targets.TotalVolume;
                newItem.TotalAchievedVolume += statement.ActualPerformance.TotalVolume;
                newItem.Adjustments += statement.Adjustments.Sum(x => x.Value);
                newItem.NonFranchiseValue += statement.ActualEarnings.NonFranchiseValue;

                newItem.UsedFinanceCases += statement.ActualEarnings.UsedFinanceCases; // 2024 onwards
                newItem.InMonthForMonth += statement.ActualEarnings.InMonthForMonth; // 2024 onwards

                newItem.UsedHighValueUnits += statement.ActualEarnings.UsedHighValueUnits; // Three10 only Jan 25 onwards
        }

        private static void AccumulateRowsIntoRow(IEnumerable<CommissionSiteRow> items, CommissionSiteRow newItem)
        {
            foreach (var item in items)
            {
                newItem.NewValue += item.NewValue;
                newItem.UsedValue += item.UsedValue;
                newItem.MotabValue += item.MotabValue;
                newItem.DemoValue += item.DemoValue;
                newItem.Cosmetic += item.Cosmetic;
                newItem.Gap += item.Gap;
                newItem.Paint += item.Paint;
                newItem.ServicePlan += item.ServicePlan;
                newItem.CarWow += item.CarWow;
                newItem.Warranty += item.Warranty;
                newItem.ExecsCount += item.ExecsCount;
                newItem.ProfitPot += item.ProfitPot;

                newItem.TotalTargetVolume += item.TotalTargetVolume;
                newItem.TotalAchievedVolume += item.TotalAchievedVolume;

                newItem.Adjustments += item.Adjustments;
                newItem.NonFranchiseValue += item.NonFranchiseValue;
                newItem.UsedFinanceCases += item.UsedFinanceCases; // 2024 onwards
                newItem.InMonthForMonth += item.InMonthForMonth; // 2024 onwards

                newItem.UsedHighValueUnits += item.UsedHighValueUnits;
            }
        }

        // Functions for bonus feature
        public async Task<BonusSummary> GetBonusSummary(CommissionQualificationParams parms)
        {
            BonusSummaryVM vm = new BonusSummaryVM();
            
            // If 2024 onwards : FY only
            if(parms.Month.Year >= 2024)
            {
                if(parms.Month.Year == 2024 && parms.Month.Month == 1) // No annual bonus paid this year (as already paid on previous scheme
                {
                    { }
                }
                else
                {
                    vm = await commissionsDataAccess.GetBonusSummary2024Onw(parms, userService.GetUserDealerGroupName());
                }
                
            }
            // Prior to 2024 : HY only
            else
            {
                vm = await commissionsDataAccess.GetBonusSummaryPre2024(parms, userService.GetUserDealerGroupName());
            }
            
            BonusSummary result = BonusCalculationService.CreateBonusSummary(vm, parms.Month);
            return result;
        }

        public async Task<IEnumerable<BonusSummaryForPerson>> GetBonusSummariesForPeople(BonusSummaryForPeopleParams parms)
        {
            List<BonusSummaryForPerson> result = new List<BonusSummaryForPerson>();

            // Check if the month parameter is quarter/half, return empty list if it is not
            if (parms.Month.Month != 1 && parms.Month.Month != 6 && parms.Month.Month != 12 && parms.Month.Month != 3 && parms.Month.Month != 9) { return result; }

            // Get bonus summaries and filter by site ID
            IEnumerable<BonusSummaryVM> vms = null;
            
            if(parms.Month.Year < 2024)
            {
                vms = await commissionsDataAccess.GetBonusSummariesPre2024(parms.Month, parms.IgnoreAuditPass, userService.GetUserDealerGroupName());
            }
            else if (parms.Month.Year == 2024 & parms.Month.Month == 1) // No annual bonus paid this year (as already paid on previous scheme
            {
                vms = new List<BonusSummaryVM>();
            }
            else if(parms.Month.Year >= 2024)
            {
                vms = await commissionsDataAccess.GetBonusSummaries2024Onw(parms.Month, parms.IgnoreAuditPass, userService.GetUserDealerGroupName());
            }

            foreach (var v in vms)
            {
                v.QtrBonus = BonusCalculationService.GetQtrBonus(parms.Month, v.QtrCriteriaMet, v.IsAutoNow, v.IsBentley, v.IsDucati);
            };

            vms = vms.Where(x => parms.SiteIds.Contains(x.SiteId));

            // Group summaries by person ID and calculate the total bonus for each person
            IEnumerable<BonusSummaryForPerson> byPerson = new List<BonusSummaryForPerson>();

            // Half annual bonus for prior to 2024
            if (parms.Month.Year < 2024)
            {
                byPerson = vms.ToLookup(x => x.PersonId)
                      .Select(group => new BonusSummaryForPerson
                      {
                          PersonId = group.Key,
                          Bonus = group.Sum(vm => BonusCalculationService.CreateBonusSummary(vm, parms.Month).HalfYearlyPayout),
                          QtrBonus = group.Sum(x => x.QtrBonus)
                      });
            }
            // Only annual bonus if 2024 onwards
            else if (parms.Month.Year >= 2024)
            {
                byPerson = vms.ToLookup(x => x.PersonId)
                      .Select(group => new BonusSummaryForPerson
                      {
                          PersonId = group.Key,
                          Bonus = parms.Month.Year == 2024 ? 0 : group.Sum(vm => BonusCalculationService.CreateBonusSummary(vm, parms.Month).FYPayout),
                          QtrBonus = group.Sum(x => x.QtrBonus)
                      });
            }


            // Add the summaries for each person to the result list
            result.AddRange(byPerson);

            // Calculate the total bonus for all people and add it to the result list
            result.Add(new BonusSummaryForPerson
            {
                PersonId = 0,
                Bonus = result.Sum(x => x.Bonus),
                QtrBonus = result.Sum(x => x.QtrBonus)
            });

            return result;
        }

        public async Task<IEnumerable<BonusSummaryForSite>> GetBonusSummariesForSite(CommissionQualificationParams parms)
        {
            // Check if the month parameter is quarter/half, return empty list if it is not
            if (parms.Month.Month != 1 && parms.Month.Month != 6 && parms.Month.Month != 12 && parms.Month.Month != 3 && parms.Month.Month != 9) { return Enumerable.Empty<BonusSummaryForSite>(); }

            // Get the bonus summaries from the data access layer
            IEnumerable<BonusSummaryVM> vms = new List<BonusSummaryVM>();
            
            if(parms.Month.Year < 2024)
            {
                vms = await commissionsDataAccess.GetBonusSummariesPre2024(parms.Month, parms.IgnoreAuditPass, userService.GetUserDealerGroupName());
            }
            else if (parms.Month.Year == 2024 && parms.Month.Month == 1) // No annual bonus paid this year (as already paid on previous scheme
            {
                vms = new List<BonusSummaryVM>();
            }
            else if(parms.Month.Year >= 2024)
            {
                vms = await commissionsDataAccess.GetBonusSummaries2024Onw(parms.Month, parms.IgnoreAuditPass, userService.GetUserDealerGroupName());
            }

            var perPerson = vms.Select(vm => BonusCalculationService.CreateBonusSummary(vm, parms.Month)).ToList();
            var bySite = perPerson.ToLookup(x => x.SiteId);

            var result = bySite.Select(site => new BonusSummaryForSite
            {
                SiteId = (int)site.First().SiteId,
                Bonus = parms.Month.Year >= 2024 ? site.Sum(x => x.FYPayout) : site.Sum(x => x.HalfYearlyPayout),
                QtrBonus = site.Sum(x => x.QtrBonus)
            }).ToList();

            // Do Regions
            var regionNames = vms.Distinct().Select(x => x.RegionName).ToArray();

            result.AddRange(regionNames.Select(reg =>
            {
                var siteIdsForRegion = vms.Distinct().Where(x => x.RegionName == reg).Select(x => x.SiteId).ToArray();
                return new BonusSummaryForSite
                {
                    Bonus = result.Where(x => siteIdsForRegion.Contains(x.SiteId)).Sum(x => x.Bonus),
                    QtrBonus = result.Where(x => siteIdsForRegion.Contains(x.SiteId)).Sum(x => x.QtrBonus),
                    RegionName = reg
                };
            }));

            // Add Total site
            result.Add(new BonusSummaryForSite
            {
                Bonus = result.Where(x => x.SiteId != 0).Sum(x => x.Bonus),
                QtrBonus = result.Where(x => x.SiteId != 0).Sum(x => x.QtrBonus),
                RegionName = "Total"
            });

            return result;
        }

        public async Task<int> SetCommissionQualificationForPerson(CommissionQualificationParams parms)
        {
            return await commissionsDataAccess.SetCommissionQualificationForPerson(parms, userService.GetUserDealerGroupName());
        }
    }
}