﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class GetVehicleAdvertsWithRatingsParams
   {
      public string Reg { get; set; }
      public string Vin { get; set; }
      public string RetailerSiteIds { get; set; }
      public DateTime EffectiveDate { get; set; }
      public string UserEligibleSites { get; set; }
      public bool IncludeNewVehicles { get; set; }
      public bool IncludeUnPublishedAdverts { get; set; }
      public List<string> LifecycleStatuses { get; set; }
      public List<string> VehicleTypes { get; set; }
      public bool UseTestStrategy { get; set; }
      public bool IncludeLCV { get; set; }
   }
}

