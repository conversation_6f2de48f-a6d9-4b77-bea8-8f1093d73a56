﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
    internal static class ValuationExcelSheetColumnService
    {
        public static List<ValuationSheetColDef> CreateLookupColumnDefinitions()
        {
            List<ValuationSheetColDef> colDefs = new List<ValuationSheetColDef>();

            var baseHeader2Styles = new List<ValuationSheetStyleTag>
                {
                    ValuationSheetStyleTag.Wrapped,
                    ValuationSheetStyleTag.Centred,
                    ValuationSheetStyleTag.GreyBorderBottom,
                    ValuationSheetStyleTag.TopAlign
                };

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "From",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = "From",
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Other, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Fee",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = "Fee",
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            return colDefs;
        }

        public static List<ValuationSheetColDef> CreateColumnDefinitions(bool isMultiSiteGroup, bool hasFeeLookup)
        {
            List<ValuationSheetColDef> colDefs = new List<ValuationSheetColDef>();

            var baseHeader2Styles = new List<ValuationSheetStyleTag>
                {
                    ValuationSheetStyleTag.Wrapped,
                    ValuationSheetStyleTag.Centred,
                    ValuationSheetStyleTag.GreyBorderBottom,
                    ValuationSheetStyleTag.TopAlign
                };

            IdentificationSection(colDefs, baseHeader2Styles);
            VehicleTypeSection(colDefs, baseHeader2Styles);
            VehicleSpecificsSection(colDefs, baseHeader2Styles);
            ValuationSection(colDefs, baseHeader2Styles, isMultiSiteGroup);
            SellingPriceSection(colDefs, baseHeader2Styles, isMultiSiteGroup, hasFeeLookup);

            return colDefs;
        }

        private static void SellingPriceSection(List<ValuationSheetColDef> colDefs, List<ValuationSheetStyleTag> baseHeader2Styles, bool isMultiSiteGroup, bool hasFeeLookup)
        {
            /// --------------------------------------
            /// Selling price and costing Section
            /// --------------------------------------

            //StrategyPrice, 12.29

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = "Selling price and costing",
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange, ValuationSheetStyleTag.GreyBorderLeft, ValuationSheetStyleTag.BoldUnderline },
                Header2Label = "StrategyPrice",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange, ValuationSheetStyleTag.GreyBorderLeft, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.StrategyPrice),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.GreyBorderLeft },
                Width = 12.29
            });


            //Days to sell @ strategy, 10.57"

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Days to sell @ strategy",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.DaysToSellAtCurrentSelling),
                BodyStyles = new(),
                Width = 10.57
            });


            //Selling Price, 10.57
            string sellingFormula = isMultiSiteGroup ? "+CJ{rowIndex}" : "+CH{rowIndex}";

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Selling Price",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                Formula = sellingFormula,
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.57
            });

            //PP% , 8
            string ppFormula = isMultiSiteGroup ? "=+CL{rowIndex}/CE{rowIndex}" : "=+CJ{rowIndex}/CE{rowIndex}";

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "PP%",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = null,
                Formula = ppFormula,
                BodyStyles = new() { ValuationSheetStyleTag.Percent },
                Width = 8
            });


            //Competitor, 19.29

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Competitor Price Positions",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.CompetitorPricePositions),
                BodyStyles = new(),
                Width = 19.29
            });


            //Total competitors, 7.57

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Total competitors",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.CompetitorCount),
                BodyStyles = new(),
                Width = 7.57
            });


            //PP% Rank, 6.86

            string rankFormula = isMultiSiteGroup ?
                "= IFERROR(MATCH(CM{rowIndex}, SORT(VALUE(TEXTSPLIT(CN{rowIndex}, \",\"))), 1) + 1, 1)"
                :
                "= IFERROR(MATCH(CK{rowIndex}, SORT(VALUE(TEXTSPLIT(CL{rowIndex}, \",\"))), 1) + 1, 1)";


            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "PP% Rank",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                Formula = rankFormula,
                BodyStyles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.Percent },
                Width = 6.86
            });


            //Valet, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Valet",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Valet),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Spare Key, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Spare Key",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.SpareKey),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //MOT, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "MOT",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.MOT),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //MOT Advisory, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "MOT Advisory",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.MOTAdvisory),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Servicing, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Servicing",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Servicing),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Paint, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Paint",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Paint),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Tyres, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Tyres",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Tyres),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Warranty, 12.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Warranty",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Warranty),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 12.43
            });


            //Parts, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Parts",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Parts),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //AdditionalMech, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Additional Mech",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.AdditionalMech),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Fee, 10.43
            string feeFormula = "";
            if (hasFeeLookup)
            {

                feeFormula = isMultiSiteGroup ? "=VLOOKUP(+CL{rowIndex}-SUM(CQ{rowIndex}:CZ{rowIndex})-SUM(DC{rowIndex}:DC{rowIndex}),Lookups!$A$2:$B$5000,2,TRUE)" :
                       "=VLOOKUP(+CJ{rowIndex}-SUM(CO{rowIndex}:CX{rowIndex})-SUM(CZ{rowIndex}:DA{rowIndex}),Lookups!$A$2:$B$5000,2,TRUE)";
            }
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Fee",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Fee),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43,
                Formula = feeFormula
            });


            //Other, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Other",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Other),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });

            //VAT Cost , 10.43
            string vatCostFormula = isMultiSiteGroup ? "=IF(AT{rowIndex}=TRUE, MAX(((CL{rowIndex}-(CL{rowIndex}-SUM(CQ{rowIndex}:DB{rowIndex})-DD{rowIndex}))/5),0),0)" : "=IF(AT{rowIndex}=TRUE,MAX(((CJ{rowIndex}-(CJ{rowIndex}-SUM(CO{rowIndex}:CZ{rowIndex})-DB{rowIndex}))/5),0),0)";
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "VAT Cost",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = null,
                Formula = vatCostFormula,
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Profit, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Profit",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Profit),
                BodyStyles = new() { ValuationSheetStyleTag.Currency, ValuationSheetStyleTag.PaleYellow },
                Width = 10.43
            });


            //Max Buy Price, 10.43
            string maxBuyFormula = isMultiSiteGroup ? "=+CL{rowIndex}-SUM(CQ{rowIndex}:DD{rowIndex})" : "=+CJ{rowIndex}-SUM(CO{rowIndex}:DB{rowIndex})";

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Max Buy Price",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = null,
                Formula = maxBuyFormula,
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });



        }

        private static void ValuationSection(List<ValuationSheetColDef> colDefs, List<ValuationSheetStyleTag> baseHeader2Styles, bool isMultiSiteGroup)
        {
            /// --------------------------------------
            /// Valuation Section
            /// --------------------------------------


            //Value Band, 10.14

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = "Valuation",
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange, ValuationSheetStyleTag.GreyBorderLeft, ValuationSheetStyleTag.BoldUnderline },
                Header2Label = "Value Band",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange, ValuationSheetStyleTag.GreyBorderLeft, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ValueBand),
                BodyStyles = new() { ValuationSheetStyleTag.GreyBorderLeft },
                Width = 10.14
            });


            //PartEx, 9.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "PartEx",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.PartEx),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 9.29
            });



            //Trade Avg Spec Valn, 9.29

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Trade Avg Spec Valn",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ValuationMktAvTrade),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 9.29
            });



            //Retail Avg Spec Valn, 9.29

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Retail Avg Spec Valn",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ValuationMktAvRetail),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 9.29
            });



            //Trade Valn, 9.29

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Trade Valn",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Trade),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 9.29
            });


            //Private, 9.29

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Private",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.PrivateValue),
                BodyStyles = new(),
                Width = 9.29
            });


            //Retail Valuation, 9.29

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Retail Valuation",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Retail),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 9.29
            });


            if (isMultiSiteGroup)
            {

                //LocationRank, 7.71

                colDefs.Add(new ValuationSheetColDef()
                {
                    Header1Label = string.Empty,
                    Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                    Header2Label = "LocationRank",
                    Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                    FieldName = ValuationBatchResult.FieldPicker(x => x.LocationRank),
                    BodyStyles = new(),
                    Width = 7.71
                });


                //Site Name, 15

                colDefs.Add(new ValuationSheetColDef()
                {
                    Header1Label = string.Empty,
                    Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                    Header2Label = "Site Name",
                    Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                    FieldName = ValuationBatchResult.FieldPicker(x => x.SiteName),
                    BodyStyles = new(),
                    Width = 25
                });

            }

            //Retail Rating, 11.71

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Retail Rating",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.RetailRating),
                BodyStyles = new(),
                Width = 11.71
            });


            //RR Band, 8

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "RR Band",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.RetailRatingBand),
                BodyStyles = new(),
                Width = 8
            });
        }

        private static void VehicleSpecificsSection(List<ValuationSheetColDef> colDefs, List<ValuationSheetStyleTag> baseHeader2Styles)
        {
            /// --------------------------------------
            ///Vehicle Specifics Section
            /// --------------------------------------
            ///

            //Derivative, 87.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = "Vehicle Specifics",
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange, ValuationSheetStyleTag.GreyBorderLeft, ValuationSheetStyleTag.BoldUnderline },
                Header2Label = "Derivative",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange, ValuationSheetStyleTag.GreyBorderLeft }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Derivative),
                BodyStyles = new() { ValuationSheetStyleTag.GreyBorderLeft },
                Width = 87.29
            });

            // Previous Use, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange, ValuationSheetStyleTag.GreyBorderRight },
                Header2Label = "Previous Use",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.PreviousUse),
                BodyStyles = new(),
                Width = 28.57
            });



            //Trim, 17.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Trim",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Trim),
                BodyStyles = new(),
                Width = 17.57
            });

            //Spec, 77.14
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Spec",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.SpecOptions),
                BodyStyles = new(),
                Width = 77.14
            });

            //Generation, 23
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Generation",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Generation),
                BodyStyles = new(),
                Width = 23
            });

            //Doors, 5.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Doors",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Doors),
                BodyStyles = new(),
                Width = 5.57
            });

            //Gears, 5.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Gears",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Gears),
                BodyStyles = new(),
                Width = 5.57
            });

            //Seats, 5.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Seats",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Seats),
                BodyStyles = new(),
                Width = 5.57
            });

            //Engine, 8.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Engine",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Engine),
                BodyStyles = new(),
                Width = 8.29
            });

            //Engine Capacity CC, 8.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Engine Capacity CC",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.EngineCapacityCC),
                BodyStyles = new() { ValuationSheetStyleTag.Number },
                Width = 8.29
            });

            //Cylinders, 5.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Cylinders",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Cylinders),
                BodyStyles = new(),
                Width = 5.29
            });

            //Engine Power BHP, 6.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Engine Power BHP",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.EnginePowerBHP),
                BodyStyles = new(),
                Width = 6.43
            });

            //CO2 Emission GPKM, 6.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "CO2 Emission GPKM",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Co2EmissionGPKM),
                BodyStyles = new(),
                Width = 6.57
            });

            //Zero To Sixty MPH Seconds, 7.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Zero To Sixty MPH Seconds",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ZeroToSixtyMPHSeconds),
                BodyStyles = new(),
                Width = 7.43
            });

            //Battery Capacity KWH, 7.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Battery Capacity KWH",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.BatteryCapacityKWH),
                BodyStyles = new(),
                Width = 7.29
            });

            //Battery Range Miles, 6.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Battery Range Miles",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.BatteryRangeMiles),
                BodyStyles = new(),
                Width = 6.57
            });

            //VED Cost, 6.86
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "VED Cost",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.VEDCost),
                BodyStyles = new(),
                Width = 6.86
            });

            //FuelType, 18.86
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "FuelType",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.FuelType),
                BodyStyles = new(),
                Width = 18.86
            });

            //Transmission Type, 11.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Transmission Type",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.TransmissionType),
                BodyStyles = new(),
                Width = 11.57
            });

            //Colour, 9.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Colour",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Colour),
                BodyStyles = new(),
                Width = 9.29
            });

            //SpecificColour, 12.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Manufacturer Colour",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.SpecificColour),
                BodyStyles = new(),
                OnlyShowIfSomeValues = true,
                Width = 18.29
            });

            //Registered Date, 10.86
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Registered Date",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.RegisteredDate),
                BodyStyles = new() { ValuationSheetStyleTag.Date },
                Width = 10.86
            });


            //AgeBand, 7.86
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "AgeBand",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.AgeBand),
                BodyStyles = new(),
                Width = 7.86
            });


            //Mileage, 7
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Mileage",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Mileage),
                BodyStyles = new() { ValuationSheetStyleTag.Number },
                Width = 7
            });


            //Condition, 9.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Condition",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Condition),
                BodyStyles = new(),
                Width = 9.29
            });


            //Owners, 5.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Owners",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Owners),
                BodyStyles = new(),
                Width = 5.43
            });


            //Recall Status, 7.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Recall Status",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.RecallStatus),
                BodyStyles = new(),
                Width = 7.29
            });

            /// --------------------------------------
            ///Seller Information Section
            /// --------------------------------------

            //Event Type, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = "Seller Information",
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Event Type",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.EventType),
                BodyStyles = new(),
                Width = 10.43
            });


            //Event Date, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Event Date",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.EventDate),
                BodyStyles = new() { ValuationSheetStyleTag.Date },
                Width = 10.43
            });



            //Location, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Location",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Location),
                BodyStyles = new(),
                Width = 10.43
            });


            //Lot Number, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Lot Number",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.LotNumber),
                BodyStyles = new(),
                Width = 10.43
            });


            //Seller, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Seller",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Seller),
                BodyStyles = new(),
                Width = 10.43
            });



            //Link, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Link",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Link),
                BodyStyles = new(),
                Width = 10.43
            });


            //V5 Status, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "V5 Status",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.V5Status),
                BodyStyles = new(),
                Width = 10.43
            });





            //Is Vat Qualifying, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Is Vat Qualifying",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.IsVatQualifying),
                BodyStyles = new(),
                Width = 10.43
            });



            //Imported, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Imported",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Imported),
                BodyStyles = new(),
                Width = 10.43
            });


            //OnFinance, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "OnFinance",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.OnFinance),
                BodyStyles = new(),
                Width = 10.43
            });



            //Insurance Category, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Insurance Category",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.InsuranceCat),
                BodyStyles = new(),
                Width = 10.43
            });



            //Mileage Warranty, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Mileage Warranty",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.MileageWarranty),
                BodyStyles = new(),
                Width = 10.43
            });



            //Number Of Keys, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Number Of Keys",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.NumberOfKeys),
                BodyStyles = new(),
                Width = 10.43
            });



            //Mot Expiry, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Mot Expiry",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.MotExpiry),
                BodyStyles = new() { ValuationSheetStyleTag.Date },
                Width = 10.43
            });




            //Service History, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Service History",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ServiceHistory),
                BodyStyles = new(),
                Width = 10.43
            });


            //Services, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Services",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Services),
                BodyStyles = new(),
                Width = 10.43
            });



            //Date Of Last Service, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Date Of Last Service",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.DateOfLastService),
                BodyStyles = new() { ValuationSheetStyleTag.Date },
                Width = 10.43
            });


            //Notes, 42.14
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Notes",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Notes),
                BodyStyles = new(),
                Width = 42.14
            });

            ////

            //Notes 2, 42.14
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Notes 2",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Notes2),
                BodyStyles = new(),
                Width = 42.14
            });

            //Notes 3, 42.14
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Notes 3",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Notes3),
                BodyStyles = new(),
                Width = 42.14
            });

            //Notes 4, 42.14
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Notes 4",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Notes4),
                BodyStyles = new(),
                Width = 42.14
            });

            //Retail Price, 10.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Retail Price",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.RetailPrice),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                OnlyShowIfSomeValues = true,
                Width = 10.57
            });

            // ModelYear
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Model Year",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ModelYear),
                BodyStyles = new(),
                OnlyShowIfSomeValues = true,
                Width = 10.43
            });

            //Upholstery 50
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Upholstery",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Upholstery),
                BodyStyles = new(),
                OnlyShowIfSomeValues = true,
                Width = 50
            });

            // ServicedWithinSchedule, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Serviced Within Schedule",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ServicedWithinSchedule),
                BodyStyles = new(),
                Width = 10.43
            });

            // ApprovedUsed, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Approved Used",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ApprovedUsed),
                BodyStyles = new(),
                Width = 10.43
            });

            // Refurbished, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Refurbished",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Refurbished),
                BodyStyles = new(),
                Width = 10.43
            });

            ////

            //SIV, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "SIV",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.SIV),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });


            //Reserve Or Buy It Now, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Reserve Or Buy It Now",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ReserveOrBuyItNow),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });



            //CAP Average, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "CAP Average",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.CapAverage),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });



            //CAP Below, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "CAP Below",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.CapBelow),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });



            //CAP New, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "CAP New",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.CapNew),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });

            //CAP Retail, 10.43"
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "CAP Retail",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.CapRetail),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });


            //CAP Valuation, 10.43"
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "CAP Valuation",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.CapValuation),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });



            //Cap Value, 10.43"

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Cap Value",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.CapValue),
                BodyStyles = new() { ValuationSheetStyleTag.Currency },
                Width = 10.43
            });



            //Reference 1, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Reference 1",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Reference1),
                BodyStyles = new(),
                Width = 10.43
            });



            //Reference 2, 10.43

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Reference 2",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Reference2),
                BodyStyles = new(),
                Width = 10.43
            });





            //Reference 3, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Reference 3",
                OnlyShowIfSomeValues = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Reference3),
                BodyStyles = new(),
                Width = 10.43
            });
        }

        private static void VehicleTypeSection(List<ValuationSheetColDef> colDefs, List<ValuationSheetStyleTag> baseHeader2Styles)
        {
            /// --------------------------------------
            ///Vehicle Type Section
            /// --------------------------------------
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = "Vehicle Type",
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.BoldUnderline, ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.GreyBorderLeft, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Make",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderLeft, ValuationSheetStyleTag.LightOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Make),
                BodyStyles = new() { ValuationSheetStyleTag.GreyBorderLeft },
                Width = 14.29
            });

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Model",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Model),
                BodyStyles = new(),
                Width = 18.71
            });

            //Vehicle Ownership, 10.43
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Vehicle Ownership",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.VehicleOwnership),
                BodyStyles = new(),
                Width = 10.43
            });

            //Vehicle Type, 11.29
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Vehicle Type",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.VehicleType),
                BodyStyles = new(),
                Width = 11.29
            });

            //Drive Type, 9.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Drive Type",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.DriveType),
                BodyStyles = new(),
                Width = 9.57
            });

            //Drivetrain, 16.71
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Drivetrain",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Drivetrain),
                BodyStyles = new(),
                Width = 16.71
            });

            //Sector, 28.57
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Sector",
                Grouped = true,
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Sector),
                BodyStyles = new(),
                Width = 28.57
            });

            //BodyType, 10.86
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.LightOrange },
                Header2Label = "Body Type",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.LightOrange, ValuationSheetStyleTag.GreyBorderRight }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.BodyType),
                BodyStyles = new(),
                Width = 10.86
            });


        }

        private static void IdentificationSection(List<ValuationSheetColDef> colDefs, List<ValuationSheetStyleTag> baseHeader2Styles)
        {
            /// --------------------------------------
            ///Identification section
            /// --------------------------------------
            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = "Identification",
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.BoldUnderline, ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.GreyBorderLeft, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Valuation Id",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderLeft, ValuationSheetStyleTag.DarkOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.ValuationId),
                BodyStyles = new() { ValuationSheetStyleTag.GreyBorderLeft },
                Width = 12.43
            });

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Vehicle Reg",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.DarkOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Reg),
                BodyStyles = new(),
                Width = 11.43
            });

            colDefs.Add(new ValuationSheetColDef()
            {
                Header1Label = string.Empty,
                Header1Styles = new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderTop, ValuationSheetStyleTag.GreyBorderRight, ValuationSheetStyleTag.DarkOrange },
                Header2Label = "Vin",
                Header2Styles = baseHeader2Styles.Concat(new List<ValuationSheetStyleTag>() { ValuationSheetStyleTag.GreyBorderRight, ValuationSheetStyleTag.DarkOrange, }).ToList(),
                FieldName = ValuationBatchResult.FieldPicker(x => x.Vin),
                Grouped = true,
                BodyStyles = new(),
                Width = 22.71
            });
        }

    }
}