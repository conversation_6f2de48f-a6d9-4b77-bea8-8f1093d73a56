import { RetailerSite } from './RetailerSite';
import { GlobalParam } from './GlobalParam';
import { SiteVM, Department, LastUpdatedDates } from './main.model';
import { VehicleType, OrderType } from './sales.model';
import { UserPreference } from './UserPreference';



export interface StandingDataSet {
  FranchiseCodes: string[];
  Sites: SiteVM[];
  VehicleTypes: VehicleType[];
  OrderTypes: OrderType[];
  GlobalParams: GlobalParam[];
  DealerGroup:string;
  Departments: Department[];
  RetailerSites: RetailerSite[];
  LastUpdatedDates: LastUpdatedDates;
  Blobname: string;
  LatestSnapshotDate: Date;
  UserPreferences: UserPreference[];
}
