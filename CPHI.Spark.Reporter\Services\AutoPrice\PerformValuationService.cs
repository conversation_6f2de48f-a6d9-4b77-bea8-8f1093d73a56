﻿using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CPHI.Spark.BusinessLogic.AutoPrice;
using System.Net.Http;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using log4net;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Model.Services;
using static CPHI.Spark.Model.ViewModels.FutureValuationTrendsPayload;
using System.Collections.Concurrent;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{

   public class PerformValuationService
   {
      private readonly ILog logger;
      private FetchAutoTraderService fetchAutoTraderService;
      private AutoTraderVehiclesClient atVehiclesClient;
      private AutoTraderVehicleMetricsClient atMetricsClient;
      private AutoTraderFutureValuationsClient atFutureValsClient;
      private AutoTraderValuationsClient atValuationsClient;
      private AutoTraderApiTokenClient atTokenClient;
      private AutoTraderCompetitorClient autoTraderApiCompetitorClient;
      private readonly HttpClient httpClient;

      public PerformValuationService(ILog loggerIn, HttpClient httpClient)
      {
         logger = loggerIn;
         fetchAutoTraderService = new FetchAutoTraderService(HttpClientFactoryService.HttpClientFactory);
         //  HttpClient httpClient = new HttpClient();
         atVehiclesClient = new AutoTraderVehiclesClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         atValuationsClient = new AutoTraderValuationsClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         atFutureValsClient = new AutoTraderFutureValuationsClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         atMetricsClient = new AutoTraderVehicleMetricsClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         autoTraderApiCompetitorClient = new AutoTraderCompetitorClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         this.httpClient = httpClient;
      }

      public async Task<TokenResponse> GenerateToken()
      {
         fetchAutoTraderService = new FetchAutoTraderService(HttpClientFactoryService.HttpClientFactory);
         TokenResponse bearerToken = (await atTokenClient.GetToken());
         return bearerToken;
      }

      public async Task<TokenResponse> CheckExpiryAndRegenerate(TokenResponse tokenResponse)
      {
         tokenResponse = await atTokenClient.CheckExpiryAndRegenerate(tokenResponse);
         return tokenResponse;
      }

      public async Task<ValuationResult> PerformValuation(PerformValuationBatchParams parms, VehicleValuation vehicleValuation, ILog logger)
      {
         parms.bearerToken = await atTokenClient.CheckExpiryAndRegenerate(parms.bearerToken);

         ATNewVehicleGet apiValuationResult = new ATNewVehicleGet();
         decimal retailValuation = 0;
         decimal retailRating = 0;
         List<ATNewVehicleGet_Feature> vehicleOptions = new List<ATNewVehicleGet_Feature>();

         try
         {
            //call the api to get vehicle info  
            apiValuationResult = await atVehiclesClient.GetIndividualVehicle(vehicleValuation.VehicleReg, vehicleValuation.Mileage, parms.retailerSiteRetailerId, parms.bearerToken.AccessToken, ConfigService.AutotraderBaseURL);
            if (apiValuationResult.vehicle.derivativeId == null)
            {
               throw new Exception("No derivativeId found");
            }
            retailValuation = RetrieveRetailValuationBasedonVehicleType(apiValuationResult);
            retailRating = RetrieveRetailRatingBasedonVehicleType(apiValuationResult);

            vehicleOptions = apiValuationResult.features.ToList();
            bool isSpecKnown = IsAdvertSpecKnown(apiValuationResult);
            ValuationAPIResponseRoot newValn = null;
            if (isSpecKnown)
            {
               newValn = await AdjustValuationIfIsBrandWhereATKnowSpec(atValuationsClient, parms.bearerToken.AccessToken, parms.retailerSiteRetailerId, vehicleValuation, apiValuationResult, logger);
               //update retail valuation
               if (newValn != null)
               {
                  retailValuation = RetrieveRetailValuationBasedonVehicleType(newValn, apiValuationResult.vehicle.vehicleType);
               }
            }

            UpdateVehicleValuationProperties(vehicleValuation, apiValuationResult, isSpecKnown, newValn);

            if (parms.applyPriceScenarios)
            {
               List<AutoTraderVehicleListing> competitors = await GetLowestCompetitorAdvertsForDerivativeAndSite(
                   parms.bearerToken,
                   apiValuationResult.vehicle.firstRegistrationDate,
                   //apiValuationResult.vehicle.derivativeId,
                   logger,
                   parms.retailerSite,
                   apiValuationResult
                   );
               competitors = competitors.Where(x => x.advertiser.segment != "Private" && x.advertiser.segment != null).ToList();
               UpdateScenariosValuationForCompetitors(vehicleValuation, competitors);
            }
            else
            {
               List<AutoTraderVehicleListing> competitors = await GetLowestCompetitorAdvertsForDerivativeAndSite(
                   parms.bearerToken,
                   apiValuationResult.vehicle.firstRegistrationDate,
                   logger,
                   parms.retailerSite,
                   apiValuationResult
                   );
               competitors = competitors.Where(x => x.advertiser.segment != "Private" && x.advertiser.segment != null).ToList();
               UpdateRegularValuationForCompetitors(vehicleValuation, competitors);
            }
         }
         catch (Exception ex)
         {
            apiValuationResult.errorMessage = ex.Message;
            logger.Error($"Errored on item {vehicleValuation.VehicleReg}, {ex.Message}");

         }
         finally
         {
            if (apiValuationResult.errorMessage.Length > 0)
            {
               string errorMessage = apiValuationResult.errorMessage;
               errorMessage = GetUserFriendlyErrorMessage(errorMessage);
               parms.failedVehicleValuations.Add(new VehicleValuationError() { VehicleValuation = vehicleValuation, errorMessage = errorMessage });
            }

         }


         return new ValuationResult()
         {
            retailValuation = retailValuation,
            vehicle = apiValuationResult,
            retailRating = (int)retailRating,
            vehicleOptions = vehicleOptions,

         };

      }

      public async Task CreateVehicleValuationsForAllSites(
          PerformValuationBatchParams parms,
          List<RetailerSite> retailersWithStrategy,
          ConcurrentBag<VehicleValuationRatingBySite> vehicleValuationRatingBySites,
          ValuationResult valuationResult,
          VehicleValuation vehicleValuation,
          bool valueVehicleAtAllSites,
          TokenResponse tokenResponse,
          DateTime runDate,
          DealerGroupName dealerGroup,
          List<FixedCostWarranty> fixedCostWarranties)
      {

         //Get other site retail ratings
         VehicleMetricProcessedResponse otherSiteRetailRatings = new VehicleMetricProcessedResponse();
         try
         {
            string vehicleMake = valuationResult.vehicle.vehicle.make;
            List<RetailerSite> retailersToConsider = retailersWithStrategy.Where
                (site =>  //check this is not too slow
            valueVehicleAtAllSites ||
            site.RetailerId == parms.retailerSiteRetailerId ||
            site.Makes.ToUpper()
                      .Split(',')
                      .Contains(vehicleMake.ToUpper())
                 ).ToList();

            //only proceed if there are some other sites to consider
            if (retailersToConsider.Count > 0)
            {
               int valuationToUse = (int)Math.Round(valuationResult.retailValuation, 0, MidpointRounding.AwayFromZero);

               //find all their retail ratings in one go
               otherSiteRetailRatings = (await GetAllSiteRetailRatings(
                   fetchAutoTraderService,
                   atMetricsClient,
                   retailersToConsider,
                   vehicleValuation,
                   parms.retailerSiteRetailerId,
                   valuationResult.vehicle,
                   parms.bearerToken,
                   logger,
                   dealerGroup)).First();


               foreach (var retailerSite in retailersToConsider)
               {
                  bool isOnBrand = retailerSite.Makes.ToUpper()
                        .Split(',')
                        .Contains(vehicleMake.ToUpper());

                  VehicleMetricProcessedLocation thisRetailerRating = otherSiteRetailRatings.locations.First(x => x.RetailerSiteId == retailerSite.Id);


                  if (vehicleValuation.FirstRegistered.HasValue)
                  {
                     var vehicleInformation = new VehicleInformation()
                     {
                        Model = vehicleValuation.Model,
                        FirstRegistered = vehicleValuation.FirstRegistered.Value,
                     };

                     var fixedCostWarranty = WarrantyCostService.GetFixedCostWarrantyFee((int)dealerGroup, (int)retailerSite.Id,
                        vehicleInformation.FirstRegistered,
                        vehicleInformation.Make,
                        vehicleInformation.Model,
                        fixedCostWarranties);

                     if (fixedCostWarranty.HasValue)
                     {
                        vehicleValuation.Warranty = (int)fixedCostWarranty;
                     }
                  }

                  AdvertParamsForStrategyCalculator calcParams = new AdvertParamsForStrategyCalculator(
                     valuationResult.vehicle,
                     thisRetailerRating.rating,
                     vehicleValuation.Mileage,
                     valuationToUse,
                     isOnBrand,
                     thisRetailerRating.daysToSell,
                     retailerSite,
                     tokenResponse
                     );


                  //work out the strategy price and vvrsBySiteresult for buyingstrategy1.  maybe in future we could save these against the valuation
                  var ruleset = retailerSite.BuyingStrategySelectionRuleSet;
                  (decimal strategyPrice, VehicleValuationRatingBySite result) = await CalculateStrategyPriceForRuleset(vehicleValuation, tokenResponse, runDate, valuationToUse, retailerSite, thisRetailerRating.rating, thisRetailerRating.daysToSell, calcParams, ruleset);


                  decimal strategyPrice2 = 0;

                  //work out the strategy price and vvrsBySiteresult for buyingstrategy2
                  if (retailerSite.BuyingStrategySelectionRuleSet2 != null)
                  {
                     AdvertParamsForStrategyCalculator calcParams2 = new AdvertParamsForStrategyCalculator(
                   valuationResult.vehicle,
                   thisRetailerRating.rating,
                   vehicleValuation.Mileage,
                   valuationToUse,
                   isOnBrand,
                   thisRetailerRating.daysToSell,
                   retailerSite,
                   tokenResponse
                   );
                     var ruleset2 = retailerSite.BuyingStrategySelectionRuleSet2;
                     (strategyPrice2, VehicleValuationRatingBySite result2) = await CalculateStrategyPriceForRuleset(vehicleValuation, tokenResponse, runDate,
                         valuationToUse, retailerSite, thisRetailerRating.rating, thisRetailerRating.daysToSell, calcParams2, ruleset2);
                     result.StrategyPrice2 = strategyPrice2;
                  }



                  vehicleValuationRatingBySites.Add(result);

                  if (parms.applyPriceScenarios)
                  {
                     var multiPricingScenarios = await GetMultiPricingScenarios(
                         retailerSite.Id,
                          thisRetailerRating.rating,
                         retailerSite.RetailerId,
                         vehicleValuation,
                         valuationResult.vehicle,
                         strategyPrice,
                         strategyPrice2,
                         parms.bearerToken.AccessToken
                         );

                     foreach (var multiPricingScenario in multiPricingScenarios)
                     {
                        vehicleValuationRatingBySites.Add(multiPricingScenario);
                     }
                  }
               }
            }
            //}

         }
         catch (Exception ex)
         {
            logger.Error($"Errored on item {vehicleValuation.VehicleReg}, {vehicleValuation.VehicleReg}");
         }
         finally
         {
            if (otherSiteRetailRatings.errorMessage.Length > 0)
            {
               string errorMessage = otherSiteRetailRatings.errorMessage;
               errorMessage = GetUserFriendlyErrorMessage(errorMessage);
               parms.failedVehicleValuations.Add(new VehicleValuationError() { VehicleValuation = vehicleValuation, errorMessage = errorMessage });
            }

         }
      }


      private async Task<(decimal, VehicleValuationRatingBySite)> CalculateStrategyPriceForRuleset(
         VehicleValuation vehicleValuation,
         TokenResponse tokenResponse,
         DateTime runDate,
         int valuationToUse,
         RetailerSite retailerSite,
         decimal? retailRating,
         int daysToSellToUse,
         AdvertParamsForStrategyCalculator calcParams,
         StrategySelectionRuleSet ruleset)
      {
         List<StrategyPriceBuildUpItem> thisAdvertBuildUpItems = new List<StrategyPriceBuildUpItem>();
         await ApplyStrategyService.ApplyRuleSet(
             runDate,
             calcParams,
             ruleset,
             retailerSite,
             retailerSite.Site,
             thisAdvertBuildUpItems,
             tokenResponse,
             autoTraderApiCompetitorClient,
             ConfigService.AutotraderBaseURL,
             logger, null, atFutureValsClient, atMetricsClient);

         decimal strategyPrice = valuationToUse;
         thisAdvertBuildUpItems.ForEach(buildUpItem =>
         {
            strategyPrice = strategyPrice += buildUpItem.Impact;
         });
         var result = new VehicleValuationRatingBySite(vehicleValuation.Id, retailerSite.Id, retailRating, daysToSellToUse, strategyPrice, 1);
         return (strategyPrice, result);
      }

      private async Task<List<AutoTraderVehicleListing>> GetLowestCompetitorAdvertsForDerivativeAndSite(
                                                                                                             TokenResponse tokenResponse,
                                                                                                             DateTime firstRegistrationDate,
                                                                                                             ILog logger,
                                                                                                              RetailerSite retailerSite,
                                                                                                              ATNewVehicleGet aTNewVehicleGet
          )
      {
         int radius = 1000;

         var currentPlate = AutoPriceMinMaxPlateService.GetPlateFromFirstReg(firstRegistrationDate);
         (int minPlate, int maxPlate) firstLastPlates = AutoPriceMinMaxPlateService.GetMinMaxPlateNew(currentPlate, retailerSite.CompetitorPlateRange);
         //populate list of all valid number plates
         List<int> validNumberPlateYears = new List<int>();
         int thisPlate = firstLastPlates.minPlate;
         validNumberPlateYears.Add(thisPlate);
         while (thisPlate != firstLastPlates.maxPlate)
         {
            thisPlate = AutoPriceMinMaxPlateService.NextHigherPlate(thisPlate);
            validNumberPlateYears.Add(thisPlate);
         }

         List<AutoTraderVehicleListing> vehicleListingResult = new List<AutoTraderVehicleListing>();



         //returns max 20 in one go
         CompetitorSearchParams searchparams = new CompetitorSearchParams(aTNewVehicleGet, retailerSite, tokenResponse, radius);
         var competitorVehicles = await autoTraderApiCompetitorClient.GetCompetitorNew(searchparams, logger);
         //var competitorVehicles = await atClient.GetCompetitorVehicle(1, retailerSite.RetailerId, derivative, radius, retailerSite.Postcode, firstLastPlates.minPlate, firstLastPlates.maxPlate, token, ConfigService.AutotraderBaseURL);
         vehicleListingResult.AddRange(competitorVehicles.results);
         List<AutoTraderVehicleListing> competitorsOrderedByPricePosition = vehicleListingResult.OrderBy(c => (c.valuations.adjusted.retail.amountGBP.HasValue && c.valuations.adjusted.retail.amountGBP.Value > 0) ? c.adverts.retailAdverts.totalPrice.amountGBP.Value / (decimal)c.valuations.adjusted.retail.amountGBP : 0).ToList();
         return competitorsOrderedByPricePosition;
      }



      private decimal RetrieveRetailValuationBasedonVehicleType(ATNewVehicleGet advert)
      {
         switch (advert.vehicle.vehicleType.ToUpper())
         {
            case "CAR":
            case "BIKE":
               return (advert.valuations?.retail?.amountGBP ?? 0);
            case "VAN":
               return (advert.valuations?.retail?.amountExcludingVatGBP ?? 0);
            case "CROSSOVER":
               return (advert.valuations?.retail?.amountGBP ?? advert.valuations?.retail?.amountExcludingVatGBP ?? 0);
            default:
               return (advert.valuations?.retail?.amountGBP ?? 0);
         }

      }

      private decimal RetrieveRetailValuationBasedonVehicleType(ValuationAPIResponseRoot valuation, string vehicleType)
      {
         switch (vehicleType.ToUpper())
         {
            case "CAR":
            case "BIKE":
               return (valuation.valuations?.retail?.amountGBP ?? 0);
            case "VAN":
               return (valuation.valuations?.retail?.amountExcludingVatGBP ?? 0);
            case "CROSSOVER":
               return (valuation.valuations?.retail?.amountGBP ?? valuation.valuations?.retail?.amountExcludingVatGBP ?? 0);
            default:
               return (valuation.valuations?.retail?.amountGBP ?? 0);
         }

      }


      private async Task<VehicleMetricAPI_Location> SafeExecuteTask(Task<VehicleMetricAPI_Location> task, string vehicleReg, decimal decimalVal, int amount)
      {
         try
         {
            return await task;
         }
         catch (Exception ex)
         {
            logger.Error($"{ex.Message} - Reg:{vehicleReg}, decimalVal:{decimalVal}, amount:{amount}");
            return null; // or a default VehicleMetricAPI_Location with zero values if necessary
         }
      }




      private async Task<List<VehicleValuationRatingBySite>> GetMultiPricingScenarios(int retailSiteId,
                                                                                      decimal? retailRating,
                                                                                      int userRetailerId,
                                                                                      VehicleValuation vehicleValuation,
                                                                                      ATNewVehicleGet advert,
                                                                                      decimal strategyPrice,
                                                                                      decimal strategyPrice2,
                                                                                      string _bearerToken
                                                                                      )
      {
         int vehicleValuationId = vehicleValuation.Id;

         string derivativeId = vehicleValuation.DerivativeId;
         DateTime? firstRegistrationDate = vehicleValuation.FirstRegistered;
         int odometerReadingMiles = vehicleValuation.Mileage;

         decimal[] decimalsArray = { 0.901m, 0.91m, 0.94m, 0.97m };

         var VehicleMetricAPI_LocationTasks = new List<Task<VehicleMetricAPI_Location>>();
         List<VehicleValuationRatingBySite> multiPricingScenarios = new List<VehicleValuationRatingBySite>();

         bool isSpecKnown = IsAdvertSpecKnown(advert);

         foreach (var decimalVal in decimalsArray)
         {
            GetAdvertPriceAdjustedDaysToSellParams daysToSellParams = new GetAdvertPriceAdjustedDaysToSellParams()
            {
               AutotraderBaseURL = ConfigService.AutotraderBaseURL,
               AdvertiserId = userRetailerId,
               DerivativeId = derivativeId,
               FirstRegistrationDate = firstRegistrationDate,
               OdometerReadingMiles = odometerReadingMiles,
               Amount = (int)(decimalVal * (isSpecKnown ? vehicleValuation.ValuationAdjRetail : vehicleValuation.ValuationMktAvRetail)),
               UseSpecificOptions = isSpecKnown,
               SpecificOptionNames = isSpecKnown ? SummariseFactoryFitOptions(advert) : new List<string>(),
               AverageValuation = vehicleValuation.ValuationMktAvRetail,
               AdjustedValuation = vehicleValuation.ValuationAdjRetail,
            };

            VehicleMetricAPI_LocationTasks.Add(SafeExecuteTask(atMetricsClient.GetAdvertPriceAdjustedDaysToSellAndRating(daysToSellParams, _bearerToken), vehicleValuation.VehicleReg, decimalVal, daysToSellParams.Amount));
         }

         VehicleMetricAPI_Location[] apiResults = await Task.WhenAll(VehicleMetricAPI_LocationTasks);

         foreach (var res in VehicleMetricAPI_LocationTasks)
         {
            if (res.Exception != null)
            {
               logger.Error(res.Exception);
            }
         }
         int resultIndex = 0;
         foreach (var apiResult in apiResults)
         {
            var daysToSell = (apiResult != null) ? apiResult.daysToSell.value.HasValue ? apiResult.daysToSell.value.Value : decimal.Zero : decimal.Zero;
            var newPriceScenario = new VehicleValuationRatingBySite(vehicleValuationId, retailSiteId, retailRating, daysToSell, strategyPrice, strategyPrice2, decimalsArray[resultIndex]);

            multiPricingScenarios.Add(newPriceScenario);
            resultIndex++;
         }

         return multiPricingScenarios;
      }


      private decimal RetrieveRetailRatingBasedonVehicleType(ATNewVehicleGet vehicle)
      {
         return (vehicle.vehicleMetrics?.retail?.rating?.value ?? 0);
      }

      private static async Task<ValuationAPIResponseRoot> AdjustValuationIfIsBrandWhereATKnowSpec(AutoTraderValuationsClient atValuationsClient, string token, int advertiserId, VehicleValuation vehicleValuation, ATNewVehicleGet advert, ILog logger)
      {
         //we have to revalue using the actual options
         IEnumerable<string> factoryFittedOptions = SummariseFactoryFitOptions(advert);//  advert.features.Where(x => x.type == StandardOrOptional.Optional && x.factoryFitted == true);
         ValuationAPIParam parm = new ValuationAPIParam()
         {
            advertiserId = advertiserId.ToString(),
            conditionRating = "Excellent",
            vehicle = new VehicleMetricAPI_Vehicle()
            {
               derivativeId = advert.vehicle.derivativeId,
               odometerReadingMiles = vehicleValuation.Mileage,
               firstRegistrationDate = advert.vehicle.firstRegistrationDate.ToString("yyyy-MM-dd")
            },
            features = factoryFittedOptions.Select(x => new ValuationAPI_Feature() { name = x }).ToList()
         };
         var newValn = await atValuationsClient.GetValuation(parm, ConfigService.AutotraderBaseURL, token);
         if (newValn.errorMessage != null && newValn.errorMessage != string.Empty)
         {
            logger.Error(newValn.errorMessage);
         }
         if (advert.valuations != null)
         {
            return newValn;


         }
         else
         {
            return null;
         }
      }


      private static List<string> SummariseFactoryFitOptions(ATNewVehicleGet advert)
      {
         return advert.features.Where(x => x.type == StandardOrOptional.Optional && x.factoryFitted == true).Select(x => x.name).ToList();
      }

      private static bool IsAdvertSpecKnown(ATNewVehicleGet advert)
      {
         var totalFeaturesCount = advert.features.Where(x => x.type == StandardOrOptional.Optional).Count();
         var nullFactoryFit = advert.features.Where(x => x.type == StandardOrOptional.Optional && x.factoryFitted == null).Count();

         return (totalFeaturesCount == nullFactoryFit) ? false : true;
      }

      private int GetRelevantValuationFromATResponse(ATNewVehicleGet_Valuation valuationSet, string vehicleType)
      {
         if (valuationSet == null) { return 0; }
         switch (vehicleType)
         {
            case "CAR":
            case "BIKE":
               return (valuationSet.amountGBP ?? 0);
            case "VAN":
               return (valuationSet.amountExcludingVatGBP ?? 0);
            case "CROSSOVER":
               return (valuationSet.amountGBP ?? valuationSet.amountExcludingVatGBP ?? 0);
            default:
               return (valuationSet.amountGBP ?? 0);
         }
      }

      private int GetRelevantValuationFromValuation(ValuationWithExVat valuationSet, string vehicleType)
      {
         if (valuationSet == null) { return 0; }
         switch (vehicleType)
         {
            case "CAR":
            case "BIKE":
               return (valuationSet.amountGBP ?? 0);
            case "VAN":
               return (valuationSet.amountExcludingVatGBP ?? 0);
            case "CROSSOVER":
               return (valuationSet.amountGBP ?? valuationSet.amountExcludingVatGBP ?? 0);
            default:
               return (valuationSet.amountGBP ?? 0);
         }
      }

      private void UpdateVehicleValuationProperties(VehicleValuation vehicleValuation, ATNewVehicleGet individualVehicleResult, bool isSpeckKnown, ValuationAPIResponseRoot newValn)
      {
         try
         {
            string vehicleType = individualVehicleResult.vehicle.vehicleType.ToUpper();

            //set the average spec valuations
            if (individualVehicleResult.valuations != null)
            {
               vehicleValuation.ValuationMktAvTrade = GetRelevantValuationFromATResponse(individualVehicleResult.valuations.trade, vehicleType);//?.amountGBP ?? 0;
               vehicleValuation.ValuationMktAvPartEx = GetRelevantValuationFromATResponse(individualVehicleResult.valuations.partExchange, vehicleType);// individualVehicleResult.valuations?.partExchange.amountGBP ?? 0;
               vehicleValuation.ValuationMktAvPrivate = GetRelevantValuationFromATResponse(individualVehicleResult.valuations.privateRetail, vehicleType); //individualVehicleResult.valuations?.privateRetail.amountGBP ?? 0;
               vehicleValuation.ValuationMktAvRetail = GetRelevantValuationFromATResponse(individualVehicleResult.valuations.retail, vehicleType);// individualVehicleResult.valuations?.retail.amountGBP ?? 0;
            }

            //set the this vehicle valuations
            if (newValn != null)
            {
               if (newValn.valuations?.retail?.amountGBP != null)
               {
                  vehicleValuation.ValuationAdjRetail = GetRelevantValuationFromValuation(newValn.valuations.retail, vehicleType); //(int)newValn.valuations.retail.amountGBP;
               }
               else
               {
                  vehicleValuation.ValuationAdjRetail = vehicleValuation.ValuationMktAvRetail;
               }
               if (newValn.valuations?.partExchange?.amountGBP != null)
               {
                  vehicleValuation.ValuationAdjPartEx = GetRelevantValuationFromValuation(newValn.valuations.partExchange, vehicleType); //(int)newValn.valuations.partExchange.amountGBP; 
               }
               else
               {
                  vehicleValuation.ValuationAdjPartEx = vehicleValuation.ValuationMktAvPartEx;
               }
               if (newValn.valuations?.trade?.amountGBP != null)
               {
                  vehicleValuation.ValuationAdjTrade = GetRelevantValuationFromValuation(newValn.valuations.trade, vehicleType); //(int)newValn.valuations.trade.amountGBP;
               }
               else
               {
                  vehicleValuation.ValuationAdjTrade = vehicleValuation.ValuationMktAvTrade;
               }
               if (newValn.valuations?.privateProperty?.amountGBP != null)
               {
                  vehicleValuation.ValuationAdjPrivate = GetRelevantValuationFromValuation(newValn.valuations.privateProperty, vehicleType); // (int)newValn.valuations.privateProperty.amountGBP; 
               }
               else
               {
                  vehicleValuation.ValuationAdjPrivate = vehicleValuation.ValuationMktAvPrivate;
               }
            }
            else
            {
               vehicleValuation.ValuationAdjRetail = vehicleValuation.ValuationMktAvRetail;
               vehicleValuation.ValuationAdjPartEx = vehicleValuation.ValuationMktAvPartEx;
               vehicleValuation.ValuationAdjTrade = vehicleValuation.ValuationMktAvTrade;
               vehicleValuation.ValuationAdjPrivate = vehicleValuation.ValuationMktAvPrivate;
            }

            if (individualVehicleResult.links?.competitors?.href != null)
            {
               vehicleValuation.CompetitorLink = individualVehicleResult.links?.competitors?.href;
            }

            //ExVat.  TODO do we need a thisvehicle approach for exVat?
            vehicleValuation.ValuationAdjTradeExVat = individualVehicleResult.valuations?.trade.amountExcludingVatGBP ?? 0;
            vehicleValuation.ValuationAdjRetailExVat = individualVehicleResult.valuations?.retail.amountExcludingVatGBP ?? 0;


            vehicleValuation.HasBeenValued = true;

            vehicleValuation.BadgeEngineSizeLitres = individualVehicleResult.vehicle.badgeEngineSizeLitres;
            vehicleValuation.BatteryCapacityKWH = individualVehicleResult.vehicle.batteryCapacityKWH;
            vehicleValuation.BatteryRangeMiles = individualVehicleResult.vehicle.batteryRangeMiles;
            vehicleValuation.BodyType = individualVehicleResult.vehicle.bodyType;
            vehicleValuation.Co2EmissionGPKM = individualVehicleResult.vehicle.co2EmissionGPKM;
            vehicleValuation.Colour = individualVehicleResult.vehicle.colour;
            vehicleValuation.Cylinders = individualVehicleResult.vehicle.cylinders;
            vehicleValuation.Derivative = individualVehicleResult.vehicle.derivative;
            vehicleValuation.DerivativeId = individualVehicleResult.vehicle.derivativeId;
            vehicleValuation.Doors = individualVehicleResult.vehicle.doors;
            vehicleValuation.Drivetrain = individualVehicleResult.vehicle.drivetrain;
            vehicleValuation.DriveType = individualVehicleResult.vehicle.driveType;
            vehicleValuation.EngineCapacityCC = individualVehicleResult.vehicle.engineCapacityCC;
            vehicleValuation.EnginePowerBHP = individualVehicleResult.vehicle.enginePowerBHP;
            vehicleValuation.BadgeEngineSizeLitres = individualVehicleResult.vehicle.badgeEngineSizeLitres;
            vehicleValuation.FirstRegistered = individualVehicleResult.vehicle.firstRegistrationDate;
            vehicleValuation.FuelType = individualVehicleResult.vehicle.fuelType;
            vehicleValuation.Gears = individualVehicleResult.vehicle.gears;
            vehicleValuation.Generation = individualVehicleResult.vehicle.generation;
            vehicleValuation.Make = individualVehicleResult.vehicle.make;
            vehicleValuation.Model = individualVehicleResult.vehicle.model;
            vehicleValuation.Owners = individualVehicleResult.vehicle.owners;
            vehicleValuation.OwnershipCondition = individualVehicleResult.vehicle.ownershipCondition;
            vehicleValuation.Seats = individualVehicleResult.vehicle.seats;
            vehicleValuation.sector = individualVehicleResult.vehicle.sector;
            vehicleValuation.StartStop = individualVehicleResult.vehicle.startStop;
            vehicleValuation.TopSpeedMPH = individualVehicleResult.vehicle.topSpeedMPH;
            vehicleValuation.TransmissionType = individualVehicleResult.vehicle.transmissionType;
            vehicleValuation.Trim = individualVehicleResult.vehicle.trim;
            vehicleValuation.VehicleExciseDutyWithoutSupplementGBP = individualVehicleResult.vehicle.vehicleExciseDutyWithoutSupplementGBP;
            vehicleValuation.VehicleType = individualVehicleResult.vehicle.vehicleType;
            vehicleValuation.Vin = individualVehicleResult.vehicle.vin;
            vehicleValuation.ZeroToSixtyMPHSeconds = individualVehicleResult.vehicle.zeroToSixtyMPHSeconds;

            vehicleValuation.IsSpecKnown = isSpeckKnown;


            vehicleValuation.RetailRating = (int)Math.Round(individualVehicleResult?.vehicleMetrics?.retail?.rating?.value ?? 0);


         }
         catch (Exception ex)
         {
            { }
         }

      }

      private static void UpdateScenariosValuationForCompetitors(VehicleValuation vehicleValuation, List<AutoTraderVehicleListing> competitors)
      {
         if (vehicleValuation.VehicleReg == "EK20WXG")
         {
            { }
         }
         if (competitors.Any())
         {
            vehicleValuation.LowestPPPrice = competitors[0].adverts.retailAdverts.totalPrice.amountGBP;
            vehicleValuation.LowestPPValuation = competitors[0].valuations.adjusted.retail.amountGBP;
            vehicleValuation.LowestPPVehicleReg = competitors[0].vehicle.registration;
            vehicleValuation.LowestPPMileage = competitors[0].vehicle.odometerReadingMiles;
            vehicleValuation.LowestPPRetailer = competitors[0].advertiser.name;
         }

         if (competitors.Skip(1).Any())
         {
            vehicleValuation.SecondLowestPPPrice = competitors[1].adverts.retailAdverts.totalPrice.amountGBP;
            vehicleValuation.SecondLowestPPValuation = competitors[1].valuations.adjusted.retail.amountGBP;
            vehicleValuation.SecondLowestPPVehicleReg = competitors[1].vehicle.registration;
            vehicleValuation.SecondLowestPPMileage = competitors[1].vehicle.odometerReadingMiles;
            vehicleValuation.SecondLowestPPRetailer = competitors[1].advertiser.name;
         }

         if (competitors.Skip(2).Any())
         {
            vehicleValuation.ThirdLowestPPPrice = competitors[2].adverts.retailAdverts.totalPrice.amountGBP;
            vehicleValuation.ThirdLowestPPValuation = competitors[2].valuations.adjusted.retail.amountGBP;
            vehicleValuation.ThirdLowestPPVehicleReg = competitors[2].vehicle.registration;
            vehicleValuation.ThirdLowestPPMileage = competitors[2].vehicle.odometerReadingMiles;
            vehicleValuation.ThirdLowestPPRetailer = competitors[2].advertiser.name;
         }
      }

      private static void UpdateRegularValuationForCompetitors(VehicleValuation vehicleValuation, List<AutoTraderVehicleListing> competitors)
      {

         List<decimal> pricePositions = new();
         int competitorCount = 0;
         foreach (var competitor in competitors)
         {
            decimal valuation = competitor.valuations.adjusted.retail.amountGBP ?? 0;
            decimal price = competitor.adverts.retailAdverts.totalPrice.amountGBP ?? 0;
            if (valuation == 0 || price == 0)
            {
               continue;
            }
            competitorCount++;
            decimal pricePosition = Math.Round(price / valuation, 3);
            pricePositions.Add(pricePosition);
         }

         vehicleValuation.CompetitorPricePositions = string.Join(',', pricePositions);
         vehicleValuation.CompetitorCount = competitorCount + 1;
      }





      private string GetUserFriendlyErrorMessage(string errorMessage)
      {
         switch (errorMessage)
         {
            case "No derivativeId found":
               return "Registration not found";
            default:
               return "Unknown error - CPHI Support have been alerted";
         }


      }


      private static async Task<List<VehicleMetricProcessedResponse>> GetAllSiteRetailRatings(
        FetchAutoTraderService fetchAutoTraderService,
        AutoTraderVehicleMetricsClient atMetricsClient,
        List<RetailerSite> retailers,
        VehicleValuation vehicleValuation,
        int retailerSiteRetailerId,
        ATNewVehicleGet individualVehicleResult,
        TokenResponse _bearerToken,
        ILog logger, DealerGroupName dealerGroup
        )
      {
         VehicleAdvertWithRetailRating vehicleAdvertWithRetailRating = new VehicleAdvertWithRetailRating()
         {
            Derivative = individualVehicleResult.vehicle.derivativeId,
            FirstRegisteredDate = vehicleValuation.FirstRegistered,
            OdometerReading = vehicleValuation.Mileage,
            RetailerIdentifier = retailerSiteRetailerId,
         };

         bool useLongLats = dealerGroup == DealerGroupName.Enterprise;
         List<VehicleMetricParams> vehicleMetricAPIParams = new List<VehicleMetricParams>() {
                    fetchAutoTraderService.CreateParamforVehicleMetricApi(vehicleAdvertWithRetailRating, retailers,false, useLongLats)
                };

         List<VehicleMetricProcessedResponse> response = await atMetricsClient.GetAdvertsWithRetailRatingAtMultipleLocationsNEW(vehicleMetricAPIParams, ConfigService.AutotraderBaseURL, _bearerToken);
         var badItems = response.Where(x => x.errorMessage != null && x.errorMessage.Length > 0);
         if (badItems.Count() > 0)
         {
            foreach (var item in badItems)
            {
               logger.Error($"Failed on GetAllSiteRetailRatings for valuation Id #{vehicleValuation.Id}: {item.errorMessage}");
            }
         }
         return response;

      }



   }
}
