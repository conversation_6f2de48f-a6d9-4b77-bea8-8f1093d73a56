import { Component, ElementRef, Input, OnInit, SimpleChanges, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
//import { Chart } from "chart.js/dist/Chart.bundle.min";
import { CphPipe, FormatType } from "src/app/cph.pipe";
import { DashboardRecentViews8Week } from "src/app/model/DashboardRecentViews8Week";
import { DashboardRecentViews } from "src/app/model/DashboardRecentViews";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { Chart, registerables } from 'chart.js';
Chart.register(...registerables);




@Component({
  selector: "recentViewChart",

  template: `
    <div id="chartHolder">
      <canvas
        #lineChartCanvas
        id="citnowLine"
        width="400"
        height="200"
      ></canvas>
    </div>
  `,
  styles: [
    `
      #chartHolder {
        display: flex;
        width: 100%;
        height:300;
        padding:3em;
      }
    `,
  ],
})
export class RecentViewChartComponent implements OnInit {
  @Input() public viewDetails: DashboardRecentViews[] | DashboardRecentViews8Week[];
  @Input() public pipeType: FormatType;
  @ViewChild("lineChartCanvas", { static: true }) lineChartCanvas: ElementRef;
  myChart: any;
  borderDashes: number[][];
  lineColours: string[];
  lineChartContext: any;
  subscription: any;
  maxYaxis: number;
  minYaxis: number = 1.2;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    //public analysis: AnalysisService,
    public router: Router,
    //public service: CitNowService
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    this.ngOnInit();
  }
  ngOnInit() {
    if (this.myChart) this.myChart.destroy();
    
    // Chart.Legend.prototype.afterFit = function () {
    //   this.width = 200;
    // };

    // Set the canvas height
    const canvas = this.lineChartCanvas.nativeElement;
    canvas.height = 250;
    const ctx = canvas.getContext('2d');

    // Extract the required data for the chart (AdvertViews, LeavingCount, SearchViews)
    const labels = (this.viewDetails[0]['SnapshotDate'] !== undefined)? 
              this.viewDetails.map(entry => this.cphPipe.transform(new Date(entry.SnapshotDate), 'shortDateWithDayName',0)) : 
              this.viewDetails.map(entry => this.cphPipe.transform(new Date(entry.WeekStart), 'shortDateWithDayName',0) )
    const advertViewsData = this.viewDetails.map(entry => entry.AdvertViews);
    const leavingCountData = this.viewDetails.map(entry => entry.LeavingCount);
    const searchViewsData = this.viewDetails.map(entry => entry.SearchViews);

    // this.myChart = new Chart(ctx, {
    //   type: 'bar',
    //   data: {
    //     labels: labels,
    //     datasets: [
    //       {
    //         label: 'Av. Search Views',
    //         data: searchViewsData,
    //         type: 'line',//recentViewChart
    //         borderColor: '#FFC500',// 'rgba(255, 148, 112,1 )', // Customize the color as needed
    //         fill: false,
    //         borderWidth: 2,
    //         tension:0,
    //         yAxisID: 'y-axis-1',
    //         pointRadius: 0
    //       },
    //       {
    //         label: 'Av. Advert Views',
    //         type:'line',
    //         data: advertViewsData,
    //         //backgroundColor: 'rgba(165, 55, 253, 1)', // Customize the color as needed
    //         fill: false,
    //         borderColor: '#5970F4',// 'rgba(165, 55, 253, 1)', // Customize the color as needed
    //         borderWidth: 2,
    //         tension:0,
    //         yAxisID: 'y-axis-1',
    //         pointRadius: 0
    //       },
    //       {
    //         label: 'Leaving Count',
    //         data: leavingCountData,
    //         backgroundColor: 'darkGreen', //this.constants.autotraderBlue, // Customize the color as needed
    //         borderColor: 'darkGreen', // this.constants.autotraderBlue,// 'rgba(227, 61, 148, 1)', // Customize the color as needed
    //         borderWidth: 1,
    //         yAxisID: 'y-axis-1',
    //         barPercentage: 0.5
    //       },
    //     ],
    //   },
    //   options: {
    //     interaction:{
    //       mode:'index',
    //     },
    //     scales: {
    //       yAxes: [
    //         {
    //           type: 'linear',
    //           display: true,
    //           position: 'left',
    //           id: 'y-axis-1',
    //         },
    //         // {
    //         //   type: 'linear',
    //         //   display: true,
    //         //   position: 'right',
    //         //   id: 'y-axis-2',
    //         // },
    //       ],
    //       xAxes: [
    //         {
    //           ticks: {
    //             callback: (value, data) => {
    //               let label: string = '';
    //               if (this.pipeType === 'dayMonth') label += 'w/c ';
    //               return label += this.cphPipe.transform(value, this.pipeType, 0);
    //             }
    //           },
    //         }
    //       ]
    //     },
    //     legend: {
    //       position: "bottom"
    //     },
    //     tooltips: {
    //       mode: 'index',
    //       position: 'nearest',
    //       intersect: false,
    //       callbacks: {
    //         label: (tooltipItem, data) => {
    //           // let labels: string[] = []; 

    //           // data.datasets.forEach(dataset => {
    //           //   console.log(dataset)
    //           //   labels.push(dataset[tooltipItem.index])
    //           // })

    //           // console.log(data.datasets[tooltipItem.datasetIndex], tooltipItem.datasetIndex, tooltipItem)
    //           return `${data.datasets[tooltipItem.datasetIndex].label}: ${this.cphPipe.transform(tooltipItem.value, 'number', 0)}`;
    //         }
    //       }
    //     },
    //   },
    // });
  }

  /*
  initParams() {
    this.lineChartContext = this.lineChartCanvas.nativeElement.getContext("2d");

    this.lineColours = [
      "rgb(26, 188, 156)",
      "rgb(52, 152, 219)",
      "rgb(155, 89, 182)",
      "rgb(243, 156, 18)",
      "rgb(231, 76, 60)",
      "rgb(52, 73, 94)",
      "rgb(46, 204, 113)",
    ]

    this.borderDashes = [
      [2, 0],
      [2, 0],
      [2, 0],
      [2, 0],
      [2, 0],
      [2, 0],
      [2, 0],
    ]

  


    Chart.Legend.prototype.afterFit = function () {
      this.width = this.width + 50;
    };

    this.makeChart();

  }
  */

  ngOnDestroy(){
    if(!!this.subscription){this.subscription.unsubscribe();}
  }

  /*
  makeChart() {

    //console.log(this.regionDetails, "this.regionDetails!")
    let labels = this.regionDetails.SiteData[0].DailyData.map(x => (new Date(x.Day).getDate()) + "/" + this.constants.sum([new Date(x.Day).getMonth(), 1]))
    let dataSets = []
    
    let rollingDataSets = this.regionDetails.SiteData

    this.maxYaxis = 1.2;

    //build up each dataset
    // https://www.chartjs.org/docs/latest/charts/line.html


    rollingDataSets.forEach((set, i) => {

        // This will expand the Y axis a bit if there are any scores greater than 120%
        set.DailyData.forEach(x => {

          if(x.VideoSentPercentage > this.minYaxis && x.VideoSentPercentage > this.maxYaxis)
          {
            this.maxYaxis = x.VideoSentPercentage * 1.05;
          }
  
      })

      dataSets.push({
        data: set.DailyData.map(x => x.VideoSentPercentage),
        fill: false,
        lineTension: 0.0,
        borderColor: this.lineColours[i],
        borderDash: this.borderDashes[i],
        pointHitRadius: 20,
        pointHoverBorderWidth: 6,
        pointHoverBackgroundColor: this.lineColours[i],
        label: set.SiteDescription,
        pointStyle: 'line'
      })

    })


    let dataPointsCount = rollingDataSets[0].DailyData.length;

    let targets: number[] = [];
    for (let i = 0; i < dataPointsCount; i++) {
      targets.push(0.90);
    }
    //push in target
    dataSets.push({
      data: targets,
      fill: true,
      lineTension: 0.0,
      backgroundColor: 'hsla(210,13%,40%,0.15)',
      pointBorderWidth: 0,
      pointRadius: 0,
      pointBorderColor: 'rgba(255,255,255,0)',
      label: this.constants.translatedText.Target,
      pointStyle: 'rect',
      borderWidth: 0,
    })

    this.myChart = new Chart(this.lineChartContext, {
      type: "line",
      data: {
        labels: labels,
        datasets: dataSets
      },

      options: {
        title: {
          //text: this.regionDetails.RegionDescription + ' - Seven day rolling average',
          position: 'top',
          display: true,
          fontSize: this.selections.chartFontsize * 14 / 12,
        },
        tooltips: {
          enabled: true,
          mode: 'label',
          fontSize: this.selections.chartFontsize * 10 / 12,
          titleSpacing: 12,
          bodySpacing: 10,
          xPadding: 12,
          yPadding: 4,
          bodyFontSize: this.selections.chartFontsize * 12 / 12,
          titleFontSize: this.selections.chartFontsize * 12 / 12,
          titleFontColor: "white",
          titleMarginBottom: 5,
          cornerRadius: 5,
          caretSize: 0,
          backgroundColor: "rgba(0,0,0,0.8)"
        },
        legend: {
          display: true,
          position: "bottom",
          labels: {
            usePointStyle: true,
            lineWidth:3
          }
        },
        scales: {
          yAxes: [
            {

              gridLines: { drawBorder: false },
              ticks: {
                callback: (value, index, values) => {
                  let suffix = ''
                  return this.cphPipe.transform(value, 'percent', 0, false);
                },
                min: 0.0,
                max: this.maxYaxis,
                fontSize: this.selections.chartFontsize * 11 / 12,
                display: true,
              },
            },
          ],
          xAxes: [
            {
              offset: true,
              ticks: {
                min: -1,
                max: 8,
                stepSize: 1,
                fixedStepSize: 1,
                maxRotation: 50,
                minRotation: 0,
                fontSize: this.selections.chartFontsize * 9 / 12,
              },
              gridLines: {
                display: false,
              },
            },
          ],
        },
      },
    });
  }


  refreshChart() {
    if (this.myChart) this.myChart.destroy();
    this.makeChart();
  }
  */
}
