import { DatePipe } from "@angular/common";
import { Injectable } from "@angular/core";
import { GridApi } from "ag-grid-community";
import { VehicleOptOutSummaryItem } from "src/app/model/VehicleOptOutSummaryItem";

@Injectable({
    providedIn: 'root'
})

export class OptOutsService {
    constructor(
        private datePipe: DatePipe
    ) { }

    optOutsRowData: VehicleOptOutSummaryItem[];
    chosenRetailerSiteIds: number[];
    chosenDate: Date = new Date();
    includeNewVehicles: boolean = false;
    gridApi: GridApi;

    get chosenDateAsString(): string {
        return this.datePipe.transform(this.chosenDate, 'yyyy-MM-dd');
    }
}