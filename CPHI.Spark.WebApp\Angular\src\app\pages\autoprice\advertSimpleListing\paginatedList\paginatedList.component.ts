import { Component, OnInit, Input, EventEmitter } from '@angular/core';
import { Subscription } from 'rxjs';
import { VehicleAdvertFilterResult } from 'src/app/model/VehicleAdvertFilterResult';
import { VehicleAdvertWithRating } from 'src/app/model/VehicleAdvertWithRating';
import { SortField } from '../advertSimpleListing.component';
import { AdvertSimpleListingService } from '../advertSimpleListing.service';

@Component({
  selector: 'paginatedList',
  templateUrl: './paginatedList.component.html',
  styleUrls: ['./paginatedList.component.scss']
})
export class PaginatedListComponent implements OnInit {
  @Input() rowDataFiltered: VehicleAdvertWithRating[];
  @Input() updatedData: EventEmitter<VehicleAdvertFilterResult>
  paginatedRows: VehicleAdvertWithRating[] = [];
  pageSize = 10;
  currentPage = 1;
  totalPages: number[] = [];
  subscription: Subscription;

  fieldsToSortBy: SortField[] = [
    { pretty: 'Default', ugly: 'AdId', type: 'int', sortAscending: true },
    { pretty: 'Ad Clicks Last 7', ugly: 'AdvertViews7Day', type: 'int', sortAscending: true },
    { pretty: 'Age', ugly: 'FirstRegisteredDate', type: 'int', sortAscending: true },
    { pretty: 'Body Type', ugly: 'BodyType', type: 'string', sortAscending: true },
    { pretty: 'Colour', ugly: 'Colour', type: 'string', sortAscending: true },
    { pretty: 'Days Listed', ugly: 'DaysListed', type: 'int', sortAscending: true },
    { pretty: 'Days In Stock', ugly: 'DaysInStock', type: 'int', sortAscending: true },
    { pretty: 'Days To Sell', ugly: 'DaysToSellAtCurrentSelling', type: 'int', sortAscending: true },
    { pretty: 'Fuel Type', ugly: 'FuelType', type: 'string', sortAscending: true },
    { pretty: 'Gearbox', ugly: 'TransmissionType', type: 'string', sortAscending: true },
    { pretty: 'Make', ugly: 'Make', type: 'string', sortAscending: true },
    { pretty: 'Market Position Score', ugly: 'MarketPositionScore', type: 'int', sortAscending: true },
    { pretty: 'Mileage', ugly: 'OdometerReading', type: 'int', sortAscending: true },
    { pretty: 'Model', ugly: 'Model', type: 'string', sortAscending: true },
    { pretty: 'Performance Rating', ugly: 'PerfRatingScore', type: 'int', sortAscending: true },
    { pretty: 'Price', ugly: 'AdvertisedPrice', type: 'int', sortAscending: true },
    { pretty: 'Price Indicator', ugly: 'PriceIndicatorRatingAtCurrentSelling', type: 'string', sortAscending: true },
    { pretty: 'Profit', ugly: 'PricedProfit', type: 'int', sortAscending: true },
    { pretty: 'Retail Rating', ugly: 'RetailRating', type: 'int', sortAscending: true },
    { pretty: 'Searches Last 7', ugly: 'SearchViews7Day', type: 'int', sortAscending: true },
    { pretty: 'Site', ugly: 'RetailerSiteName', type: 'string', sortAscending: true },
    { pretty: 'Site Brand', ugly: 'SiteBrand', type: 'string', sortAscending: true },
    { pretty: 'Strategy Price', ugly: 'StrategyPrice', type: 'int', sortAscending: true },
    { pretty: 'Vehicle Type', ugly: 'VehicleType', type: 'string', sortAscending: true },
    { pretty: 'Vs Strategy Price', ugly: 'VsStrategyPrice', type: 'int', sortAscending: true }
  ]

  constructor(
    public service: AdvertSimpleListingService
  ) {

  }

  ngOnInit() {
    this.updateEverything();
    this.subscription = this.updatedData.subscribe(res => {
      this.rowDataFiltered = res.rowData;
      this.updateEverything();
    })
  }

  ngOnDestroy() {
    if (!!this.subscription) {this.subscription.unsubscribe();}
  }

  updateEverything() {

    this.calculateTotalPages();
    this.updatePaginatedData();
  }

  calculateTotalPages() {
    const pageCount = Math.ceil(this.rowDataFiltered.length / this.pageSize);
    this.totalPages = Array(pageCount).fill(0).map((x, i) => i);
  }

  setPage(page: number) {
    console.log(page)
    this.currentPage = page;
    this.updatePaginatedData();
  }

  updatePaginatedData() {
    const startIndex = (this.currentPage-1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedRows = this.rowDataFiltered.slice(startIndex, endIndex);
  }
}
