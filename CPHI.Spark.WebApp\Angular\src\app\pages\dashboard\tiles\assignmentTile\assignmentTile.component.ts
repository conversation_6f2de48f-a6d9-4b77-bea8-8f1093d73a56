import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Subscription } from "rxjs";
import { ConstantsService } from "src/app/services/constants.service";
import { AssignmentVM } from "../../dashboard.model";
import { SelectionsService } from "src/app/services/selections.service";
import { OrderBookService } from "src/app/pages/orderBook/orderBook.service";
import { DashboardService } from "../../dashboard.service";



@Component({
  selector: 'assignmentTile',
  templateUrl: './assignmentTile.component.html',
  styleUrls: ['./assignmentTile.component.scss']
})
export class AssignmentTileComponent implements OnInit {

  @ViewChild('myChart', { static: true }) myChart: ElementRef;
  @Input() public data: AssignmentVM[];

  @Input() public title: string;
  @Input() public newDataEmitter: EventEmitter<void>;
  @Input() public dataSource: string;

  actualWidth: number;
  budgetWidth: number;
  subscription: Subscription;

  new: AssignmentVM;
  used: AssignmentVM;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public orderBookService: OrderBookService,
    public dashService: DashboardService,

  ) {

  }

  ngOnInit(): void {

    this.initParams();
    
    this.subscription = this.newDataEmitter.subscribe(res => {
      this.initParams();
    })

  }


  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
  }



  initParams() {
    this.new = this.data.filter(x => x.Department == 'New')[0];
    this.used = this.data.filter(x => x.Department == 'Used')[0];
  }


  isClickableHeader(){
    return true;
  }

  navigateToOrderBook()
  {
    let orderTypes: string[];

    this.orderBookService.initOrderbook();

    this.orderBookService.accountingDate.startDate = this.constants.thisMonthStart;
    this.orderBookService.accountingDate.endDate = this.constants.thisMonthEnd;

    this.orderBookService.salesExecName = null;
    this.orderBookService.salesExecId = null;

    this.selections.selectedSites = this.dashService.chosenSites;

    orderTypes = this.constants.orderTypeTypes.filter(x => x == 'Cesión');

    this.orderBookService.orderTypeTypes = orderTypes;
    this.orderBookService.showOrderbook();
  }

}


