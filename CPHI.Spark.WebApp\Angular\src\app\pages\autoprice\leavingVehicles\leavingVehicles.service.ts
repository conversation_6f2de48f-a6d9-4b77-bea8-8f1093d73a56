import { DatePipe } from "@angular/common";
import { EventEmitter, Injectable } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Observable, forkJoin } from "rxjs";
import { AutoPriceInsightsModalComponent } from "src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component";
import { BIChartTileDataType } from "src/app/components/biChartTile/biChartTile.component";
import { BlobDimension, BlobItem } from "src/app/components/blobChart/blobItem";
import { CphPipe } from "src/app/cph.pipe";
import { RetailerSite } from "src/app/model/RetailerSite";
import { DashboardMeasure } from "src/app/model/DashboardMeasure";
import { GetLeavingPriceItemsParams } from "src/app/model/GetLeavingPriceItemsParams";
import { LeavingPriceSummaryItem } from "src/app/model/LeavingPriceSummaryItem";
import { VNTileParams } from "src/app/model/VNTileParams";
import { VNTileTableRow } from "src/app/model/VNTileTableRow";
import { AnalysisDimensionTypeEnum } from "src/app/model/AnalysisDimensionTypeEnum";
import { AnalysisDimensionColourBasisEnum } from "src/app/model/AnalysisDimensionColourBasisEnum";
import { AnalysisDimension } from "src/app/model/AnalysisDimension";
import { AnalysisDimensionPipeTypeEnum } from "src/app/model/AnalysisDimensionPipeTypeEnum";
import { VehicleAdvertWithRating } from "src/app/model/VehicleAdvertWithRating";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { AutoPriceInsightsModalService } from "src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service";

// export interface AnalysisDimension {
//   field: string;
//   label: string;
//   type: 'string' | 'number';
//   sortByNumericBand: boolean;
//   colourBasis: 'higherBetter'|'lowerBetter'|'none' | 'zeroBetter',
//   pipeType: 'number0dp'|'number1dp'|'none'|'percent'|'percent1dp'|'currency'
// }


@Injectable({
  providedIn: 'root'
})

export class LeavingVehiclesService {
  popoverPosition: string;
  chosenRetailerSiteIds: number[] = [18]
  userRetailerSites: RetailerSite[];
  showLeavingVehicles: boolean;

  filterChoices: DashboardMeasure[]
  highlightChoices: DashboardMeasure[]

  summaryStats: {
    vehicleCount: number;
    averagePerformanceRating: number;
    averageRetailRating: number;
    averageProfit: number;
    averageValuation: number;
    averageFinalPrice: number;
    averageFirstPrice: number;
    averageDaysListed: number;
    finalPricePosition: number;
    firstPricePosition: number;
  }
  //chosenMonth: Date;

  rawData: LeavingPriceSummaryItem[];
  rawDataFiltered: LeavingPriceSummaryItem[];
  rawDataHighlighted: LeavingPriceSummaryItem[]; //subset of filtered.
  // months: Date[];

  refreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
  refreshTileEmitter: EventEmitter<void> = new EventEmitter();
  highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  newBlobsEmitter: EventEmitter<BlobItem[]> = new EventEmitter();
  blobItems: BlobItem[];

  xAnalysisDimension: AnalysisDimension;// = { Field: 'RetailRating',SortByNumericBand:false, Label: 'Retail Rating', Type: AnalysisDimensionTypeEnum.Number,ColourBasis:AnalysisDimensionColourBasisEnum.HigherBetter,PipeType: AnalysisDimensionPipeTypeEnum.Number0dp }
  yAnalysisDimension: AnalysisDimension;// = { Field: 'RetailerSiteName',SortByNumericBand: false, Label: 'Retailer Name', Type: AnalysisDimensionTypeEnum.String, ColourBasis:AnalysisDimensionColourBasisEnum.None,PipeType:AnalysisDimensionPipeTypeEnum.None }

  analysisDimensions: AnalysisDimension[];
  //allDimensions: AnalysisDimension[]

  vehicleAdvertPopover: {
    title: string;
    vehicleAdvertDetails: VehicleAdvertWithRating;
    xDimension: BlobDimension;
    yDimension: BlobDimension;
  }

  fromDate: string;
  toDate: string;
  showInactiveBlobs: boolean=true;

  includeNewVehicles: boolean = false;

  constructor(
    public constants: ConstantsService,
    public getDataService: GetDataMethodsService,
    public selections: SelectionsService,
    public gridHelpers: AGGridMethodsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    private datePipe: DatePipe,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService
  ) { }



  initParams() {
    let lastMonth: Date = new Date();
    lastMonth.setMonth(new Date().getMonth() - 1);

    this.toDate = this.constants.endOfMonth(lastMonth).toISOString().split('T')[0];

    let minus2Months: Date = new Date();
    minus2Months.setMonth(new Date().getMonth() - 2);

    this.fromDate = this.constants.startOfMonth(minus2Months).toISOString().split('T')[0];
  }

  buildChoicesObjects() {
    this.filterChoices = [];
    this.analysisDimensions.forEach(dimension => {
      this.filterChoices.push({ FieldName: dimension.Field, FieldNameTranslation: dimension.Field, IsDate: false, ChosenValues: [] })
    })

    this.highlightChoices = [];
    this.analysisDimensions.forEach(dimension => {
      this.highlightChoices.push({ FieldName: dimension.Field, FieldNameTranslation: dimension.Field, IsDate: false, ChosenValues: [] })
    })
  }



  getPageParams(): VNTileParams {
    return {
      highlightChoices: this.highlightChoices,
      filterChoices: this.filterChoices,

      filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
      highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
      updateThisPicker: this.refreshFilterListsEmitter,
      updateThisTile: this.refreshTileEmitter,
      parentMethods: {
        buildRows: (fieldName, dataType) => this.buildTableRows(fieldName, dataType),
        highlightRow: (row, fieldName) => this.highlightRow(row, fieldName),
        provideItemsList: (fieldName, isDateField) => this.provideItemsList(fieldName, isDateField),
      }
    }
  }



  highlightRow(row: VNTileTableRow, fieldName: string) {
    let userChoice = this.highlightChoices.find(x => x.FieldName === fieldName);
    let isItemSelected = userChoice.ChosenValues.length === 0 || userChoice.ChosenValues.includes(row.Label);
    if (userChoice.ChosenValues.length === 0) {
      this.highlightChoices.find(x => x.FieldName === fieldName).ChosenValues = [row.Label];
    } else if (isItemSelected) {
      userChoice.ChosenValues = userChoice.ChosenValues.filter(x => x !== row.Label)
    } else {
      userChoice.ChosenValues.push(row.Label)
    }

    //this.pageParams.highlightChoiceHasBeenMade.emit(true);
  }



  getData() {

    //THIS PAGE IS TO BE REMOVED
    const requests = [this.getVehicleData(), this.getAnalysisDimensions()];

    forkJoin(requests).subscribe(res => {
      this.yAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Retail Rating Band')
      this.xAnalysisDimension = this.analysisDimensions.find(x => x.Label === 'Final Price Position')
      this.blobItems = this.makeBlobItems(this.rawData, this.analysisDimensions);
      this.buildChoicesObjects();
      this.rawDataFiltered = this.filterData(this.rawData, this.filterChoices);
      this.rawDataHighlighted = this.filterData(this.rawDataFiltered, this.highlightChoices)
      //this.newBlobsEmitter.emit(this.blobItems)
      this.refreshFilterListsEmitter.emit();
      this.refreshTileEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });

      this.recalculateSummaryStats();

      this.selections.triggerSpinner.next({ show: false });
    })


  }

  getVehicleData(): Observable<void> {
    let myObservable: Observable<void> = new Observable(observer => {

      const params: GetLeavingPriceItemsParams = {
        ChosenRetailerSiteIds: this.constants.userRetailerSites.map(x => x.Id),
        StartDate: this.fromDate,
        EndDate: this.toDate,
        includeNewVehicles: this.includeNewVehicles
      }

      // this.getDataService.getLeavingPriceItems(params).subscribe((res: LeavingPriceSummaryItem[]) => {
      //   res.map(item => {
      //     if (item.FinallySeenDate) { item.FinallySeenDate = new Date(item.FinallySeenDate) }
      //   })

      //   this.rawData = res;


      //   observer.next();
      //   observer.complete();


      // }, (error: any) => {
      //   observer.error();
      //   observer.complete();
      //   console.error('Failed to retrieve data', error);
      //   this.selections.triggerSpinner.next({ show: false });
      // });

    })

    return myObservable;
  }


  getAnalysisDimensions(): Observable<void> {
    let myObservable: Observable<void> = new Observable(observer => {

      this.getDataService.getAnalysisDimensions('LeavingVehicles').subscribe((allDimensions: AnalysisDimension[]) => {
        this.analysisDimensions = allDimensions;


        observer.next();
        observer.complete();
      }, e => {
        console.error('failed getting analysis dimensions')
        observer.error();
        observer.complete();
      })

    })

    return myObservable;
  }



  makeBlobItems(rawData: LeavingPriceSummaryItem[], plotFields: AnalysisDimension[]): BlobItem[] {
    let result: BlobItem[] = [];
    rawData.forEach(item => {

      let newBlobItem: BlobItem = {
        Id: item.VehicleAdvertId,
        Valuation: item.RetailValuation,
        Label: item.Derivative,
        Dimensions: [
        ],
        IsActive: true,
        VsStrategyBanding: ''
      }

      plotFields.forEach(plotField => {
        newBlobItem.Dimensions.push(
          {
            Name: plotField.Field,
            Type: plotField.Type,
            StringValue: plotField.Type === AnalysisDimensionTypeEnum.String ? item[plotField.Field] : null,
            NumberValue: plotField.Type === AnalysisDimensionTypeEnum.Number ? item[plotField.Field] : null,
            PipeType: plotField.PipeType
          }
        )
      })

      result.push(newBlobItem)
    })
    return result;
  }


  recalculateSummaryStats() {
    let vehicleCount = 0;
    let totalPerfRating = 0;
    let totalRetRating = 0;
    let totalProfit = 0;
    let totalValuation = 0;
    let totalFinalPrice = 0;
    let totalFirstPrice = 0;
    let totalDaysListed = 0;


    let data = this.rawDataHighlighted ?? this.rawData;

    if (!data) {
      this.summaryStats = {
        vehicleCount: 0,
        averagePerformanceRating: 0,
        averageRetailRating: 0,
        averageProfit: 0,
        averageValuation: 0,
        averageFinalPrice: 0,
        averageFirstPrice: 0,
        averageDaysListed: 0,
        finalPricePosition: 0,
        firstPricePosition: 0,
      }
      return;
    }

    data.forEach(item => {
      vehicleCount++;
      totalPerfRating += item.PerformanceRatingScore;
      totalRetRating += item.RetailRating;
      totalProfit += item.Profit;
      totalValuation += item.RetailValuation;
      totalFinalPrice += item.FinalPrice;
      totalFirstPrice += item.FirstPrice;
      totalDaysListed += item.DaysListed;
    })

    this.summaryStats = {
      vehicleCount: vehicleCount,
      averagePerformanceRating: totalPerfRating / vehicleCount,
      averageRetailRating: totalRetRating / vehicleCount,
      averageProfit: totalProfit / vehicleCount,
      averageValuation: totalValuation / vehicleCount,
      averageFinalPrice: totalFinalPrice / vehicleCount,
      averageFirstPrice: totalFirstPrice / vehicleCount,
      averageDaysListed: totalDaysListed / vehicleCount,
      finalPricePosition: totalFinalPrice / totalValuation,
      firstPricePosition: totalFirstPrice / totalValuation
    }

  }


  selectSnapshot(snapshot: Date) {
    //this.chosenMonth = snapshot;
    this.getData()
  }


  isHighlightFiltersOn() {
    let isHighlights = false;
    let i = 0;
    while (!isHighlights && i < this.highlightChoices.length) {
      isHighlights = this.highlightChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isHighlights;
  }

  isFiltersOn() {
    if (!this.filterChoices) { return false; }
    let isFilters = false;
    let i = 0;
    while (!isFilters && i < this.filterChoices.length) {
      isFilters = this.filterChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isFilters;
  }






  clearHighlights() {
    this.highlightChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.highlightItems();
    this.refreshTileEmitter.emit();
  }
  clearFilters() {
    this.filterChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.filterItems();
    this.refreshTileEmitter.emit();
  }

  loadModalForBlob(blob: BlobItem) {
    this.autoPriceInsightsModalService.initialise(blob.Id, [])
    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });

  }

  filterData(dataIn: LeavingPriceSummaryItem[], stringFilterChoices: DashboardMeasure[]): LeavingPriceSummaryItem[] {
    let results = [];
    dataIn.forEach(item => {
      //check all chosen strings
      let filterOutThisItem: boolean = false;
      stringFilterChoices.forEach(choice => {
        if (choice.IsDate) {
          let itemPropertyAsDateString = this.cphPipe.transform(item[choice.FieldName], 'date', 0);
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(itemPropertyAsDateString)) { filterOutThisItem = true; } }
        } else {
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(item[choice.FieldName])) { filterOutThisItem = true; } }
        }
      })


      if (!filterOutThisItem) { results.push(item) }
    })

    return results;
  }




  filterItems() {
    //have chosen ok from a dropdown picker
    this.rawDataFiltered = this.filterData(this.rawData, this.filterChoices)
    this.highlightItems()
  }

  highlightItems() {
    //have clicked a row in a tile.
    this.rawDataHighlighted = this.filterData(this.rawDataFiltered, this.highlightChoices)
    this.refreshTileEmitter.emit()
  }







  provideItemsList(fieldName: string, isDateField: boolean) {
    if (!this.rawData) { return [] }
    return [...new Set(this.rawData.map(x => x[fieldName]))]
  }

  buildTableRows(fieldName: string, dataType: BIChartTileDataType) {
    let tableRows: VNTileTableRow[] = []
    if (dataType === BIChartTileDataType.day) {
      tableRows = this.buildTableRowsDatesBasis(fieldName, false)
    } else if (dataType === BIChartTileDataType.weekly) {
      tableRows = this.buildTableRowsDatesBasis(fieldName, true)
    }
    else {
      tableRows = this.buildTableRowsNonDatesBasis(fieldName)
      //sort
      const sortByNumericBand = this.analysisDimensions.filter(x => x.SortByNumericBand).map(x => x.Field).includes(fieldName);
      tableRows = sortByNumericBand ? this.sortByNumericBand(tableRows) : this.sortByTotal(tableRows)
    }


    return tableRows;
  }

  buildTableRowsNonDatesBasis(fieldName: string): VNTileTableRow[] {
    let tableRows: VNTileTableRow[] = [];

    //go through filteredData to find unique labels and countup
    let labels: string[] = [];
    this.rawDataFiltered.forEach(item => {
      const itemLabel = item[fieldName];
      let labelsIndex = labels.findIndex(x => x === itemLabel);
      if (labelsIndex === -1) {
        //don't already have label, must be new
        labels.push(itemLabel)
        tableRows.push({ Label: itemLabel, FilteredTotal: 1, HighlightedTotal: 0 })
      } else {
        tableRows[labelsIndex].FilteredTotal += 1;
      }
    })

    //find out values to show
    this.rawDataHighlighted.forEach(item => {
      const itemLabel = item[fieldName];
      const indexInLabels = labels.findIndex(x => x === itemLabel);
      //should exist or something has gone wrong as orderDataHighlighted should be subset of orderDataFiltered
      tableRows[indexInLabels].HighlightedTotal += 1;
    });
    return tableRows;
  }



  private sortByTotal(tableRows: VNTileTableRow[]) {
    tableRows = tableRows.sort((a, b) => b.FilteredTotal - a.FilteredTotal);
    return tableRows;
  }

  private sortByNumericBand(tableRows: VNTileTableRow[]) {
    let lessThanTableRows = tableRows.filter(t => t.Label.substring(0, 1) == '<');
    let noLTorGTRows = tableRows.filter(t => !['<', '>'].includes(t.Label.substring(0, 1)));
    let greaterThanTableRow = tableRows.filter(t => t.Label.substring(0, 1) == '>');

    lessThanTableRows = lessThanTableRows.sort((a, b) => this.findFirstNumericValue(a.Label) - this.findFirstNumericValue(b.Label));
    noLTorGTRows = noLTorGTRows.sort((a, b) => this.findFirstNumericValue(a.Label) - this.findFirstNumericValue(b.Label));
    greaterThanTableRow = greaterThanTableRow.sort((a, b) => this.findFirstNumericValue(a.Label) - this.findFirstNumericValue(b.Label));

    return lessThanTableRows.concat(noLTorGTRows).concat(greaterThanTableRow);
  }

  findFirstNumericValue(inputString: string): number | null {
    // Use a regular expression to find the first numeric value in the string
    const match = inputString.match(/\d+/);

    // If a match is found, convert it to a number and return it
    if (match) {
      return parseInt(match[0], 10);
    }

    // If no match is found, return null
    return null;
  }




  buildTableRowsDatesBasis(fieldName: string, isWeekly?: boolean): VNTileTableRow[] {
    //similar approach to non-dates, but looks odd if we have gaps in the days, so we find the earliest date then iterate every day at a time since then even if it has no data
    let earliestDate: Date = this.rawDataFiltered.map(x => x[fieldName]).sort((a, b) => a.getTime() - b.getTime())[0];
    let latestDate: Date = this.rawDataFiltered.map(x => x[fieldName]).sort((a, b) => b.getTime() - a.getTime())[0];

    let tableRows = [];
    let currentDate = new Date(earliestDate);
    //walk through creating tableRows, with zero values
    while (!this.constants.datesAreSame(currentDate, latestDate)) {
      const label = this.cphPipe.transform(currentDate, 'date', 0);
      tableRows.push({ Label: label, FilteredTotal: 0, HighlightedTotal: 0 })
      currentDate = this.constants.addDays(currentDate, 1);
    }

    //update these new rows with orderData
    const orderDataSorted = this.rawDataFiltered.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    let currentTableRowIndex: number = 0;
    orderDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].FilteredTotal += 1; }
    })

    //update with highlighted data
    const highlightedDataSorted = this.rawDataHighlighted.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    currentTableRowIndex = 0;
    highlightedDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].HighlightedTotal += 1; }
    })

    let tableRowsWeekly: any[] = [];

    if (isWeekly) {
      let day: number = 1;
      let weekNumber: number = 0;

      tableRows.forEach(row => {
        if (!tableRowsWeekly[weekNumber]) {
          tableRowsWeekly[weekNumber] = {
            FilteredTotal: 0,
            HighlightedTotal: 0,
            Label: tableRows[7 * weekNumber].Label
          };
        }

        tableRowsWeekly[weekNumber].FilteredTotal = tableRowsWeekly[weekNumber].FilteredTotal + row.FilteredTotal;
        tableRowsWeekly[weekNumber].HighlightedTotal = tableRowsWeekly[weekNumber].HighlightedTotal + row.HighlightedTotal;


        weekNumber = day % 7 === 0 ? weekNumber + 1 : weekNumber;
        day++;
      })
    }

    return isWeekly ? tableRowsWeekly : tableRows;

  }





  loadVehicleAdvertDetails(blob: BlobItem) {
    const leavingPriceSummaryItem: LeavingPriceSummaryItem = this.rawData.find(x => x.VehicleAdvertId === blob.Id);
    const params = [
      { key: 'webSiteStockIdentifier', value: leavingPriceSummaryItem.WebSiteStockIdentifier },
      { key: 'effectiveDate', value: this.datePipe.transform(leavingPriceSummaryItem.FinallySeenDate, 'yyyy-MM-dd') }
    ]

    this.vehicleAdvertPopover = {
      title: blob.Label,
      vehicleAdvertDetails: null,
      xDimension: blob.xDimension,
      yDimension: blob.yDimension
    }

    // setTimeout(()=>{
    //   this.apiAccessService.get('api/AutoPrice', 'GetVehicleAdvertDetails', params).subscribe((res: VehicleAdvertWithRating[]) => {
    //     this.vehicleAdvertPopover.vehicleAdvertDetails = res[0];
    //   })
    // },10)
  }



}
