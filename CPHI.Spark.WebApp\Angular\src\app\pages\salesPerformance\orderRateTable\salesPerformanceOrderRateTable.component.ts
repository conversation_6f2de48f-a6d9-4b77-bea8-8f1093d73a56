import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { CellClassParams, ColumnApi, DomLayoutType, GridOptions, RowDataUpdatedEvent } from 'ag-grid-community';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { CphPipe } from '../../../cph.pipe';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from '../../../services/selections.service';
import { SalesPerformanceService } from '../salesPerformance.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

@Component({
  selector: 'salesPerformanceOrderRateTable',
  templateUrl: './salesPerformanceOrderRateTable.component.html',
  styleUrls: ['./../../../../styles/components/_agGrid.scss', './salesPerformanceOrderRateTable.component.scss'],
})

export class SalesPerformanceOrderRateTableComponent implements OnInit {
  @Input() public reportType: string;

  @Output() cellClicked = new EventEmitter();

  domLayout: DomLayoutType = "autoHeight";
  flipCols: string[];
  mainTableGridOptions: GridOptions
  frameworkComponents: { agColumnHeader: any; };
  gridColumnApi: ColumnApi;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: SalesPerformanceService,
    public columnTypeService: ColumnTypesService
  ) { }

  ngOnInit() {
    this.setupGrid();
  }

  ngOnDestroy(){
    this.service.gridApiOrderRate = null;
    this.service.gridApiRegionsOrderRate = null;
  }


  ngOnChanges(changes: SimpleChanges): void {
  }

  setupGrid() {
    this.flipCols = [];
    

    this.mainTableGridOptions = {
      getMainMenuItems:(params)=>this.gridHelpersService.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlightsOrderRateTable),
      getContextMenuItems: (params) =>{return this.gridHelpersService.getContextMenuItems(params).concat(['separator','chartRange'])},
      context: { thisComponent: this },
      suppressPropertyNamesCheck : true,
      getRowId:(params)=>{return `${params.data.SiteId}_${params.data.RegionId}_${params.data.IsTotal}`},
      onCellClicked: (params) => {
        this.onCellClick(params);
      },
      onFirstDataRendered:()=>{this.selections.triggerSpinner.next({ show: false });},
      frameworkComponents: this.frameworkComponents,
      //getMainMenuItems: this.getMainMenuItems(),
      onGridReady: (params) => this.onGridReady(params),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
        //suppressColumnMoveAnimation: true,
      },
      onRowDataUpdated:(params)=>this.onRowDataUpdated(params),
      rowData:  this.reportType==='sites' ? this.service.orderRateSiteRows : this.service.orderRateRegionRows,
      pinnedBottomRowData: this.service.orderRateTotalRows,
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsOrderRateTable),
      },
      columnDefs: this.getColumnDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }
    }
  }

  onRowDataUpdated(params: RowDataUpdatedEvent<any, any>): void {
    this.resizeGrid();
  }

  // cellClassProvider(params: CellClassParams){
  //   if (this.reportType !== 'sites') {
  //     return params?.value < 0 ? 'badFont ag-right-aligned-cell' : 'ag-right-aligned-cell';
  //   } else {
  //     return this.gridHelpers.cellClassProviderWithColourFontNew(params,this.service.topBottomHighlights);
  //   }
  // }

  getColumnDefs() {
    let thisMonthName = this.constants.appStartTime.toLocaleString('default', { month: 'long' })
    return  [

      { headerName: this.constants.translatedText.Site, field: 'Label', colId: 'label', width: 150, type: 'label', },

      {
        headerName: this.constants.translatedText.Dashboard_SalesPerformance_VehiclesOrdered,
        children: [
          //Run Rate cols
          { headerName: `${this.constants.translatedText.InCapitalised} ${thisMonthName}`,  field: 'UnitsThisMonth', colId: 'UnitsThisMonth', width: 95, type: 'number', },
          { headerName: this.constants.translatedText.LastWeek,  field: 'UnitsLastWeek', colId: 'SalesPerformance.RunRate.Units.LW', width: 95, type: 'number', },
          { headerName: this.constants.translatedText.ThisWeek,  field: 'UnitsThisWeek', colId: 'SalesPerformance.RunRate.Units.TW', width: 95, type: 'number', },
          { headerName: this.constants.translatedText.Yesterday,  field: 'UnitsYesterday', colId: 'SalesPerformance.RunRate.Units.Yesterday', width: 95, type: 'number', },
          { headerName: this.constants.translatedText.Today,  field: 'UnitsToday', colId: 'SalesPerformance.RunRate.Units.Today', width: 95, type: 'number', },
        ]
      },

      {
        headerName: 'GPU ',
        children: [

          { headerName: `${this.constants.translatedText.InCapitalised} ${thisMonthName}`,  field: 'ProfitThisMonthPerUnit',type: 'currencyWithFontColour', colId: 'SalesPerformance.RunRate.MarginPU.MTD', width: 95,  },
          { headerName: this.constants.translatedText.LastWeek,  field: 'ProfitLastWeekPerUnit', colId: 'SalesPerformance.RunRate.MarginPU.LW', width: 95, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.ThisWeek,  field: 'ProfitThisWeekPerUnit', colId: 'SalesPerformance.RunRate.MarginPU.TW', width: 95, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.Yesterday,  field: 'ProfitYesterdayPerUnit', colId: 'SalesPerformance.RunRate.MarginPU.Yesterday', width: 95, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.Today,  field: 'ProfitTodayPerUnit', colId: 'SalesPerformance.RunRate.MarginPU.Today', width: 95, type: 'currencyWithFontColour', },
        ]
      },

      {
        headerName: 'GP ',
        children: [

          { headerName: `${this.constants.translatedText.InCapitalised} ${thisMonthName}`,  field: 'ProfitThisMonth', colId: 'ProfitThisMonth', width: 95, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.LastWeek,  field: 'ProfitLastWeek', colId: 'SalesPerformance.RunRate.Margin.LW', width: 95, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.ThisWeek,  field: 'ProfitThisWeek', colId: 'SalesPerformance.RunRate.Margin.TW', width: 95, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.Yesterday,  field: 'ProfitYesterday', colId: 'SalesPerformance.RunRate.Margin.Yesterday', width: 95, type: 'currencyWithFontColour', },
          { headerName: this.constants.translatedText.Today,  field: 'ProfitToday', colId: 'SalesPerformance.RunRate.Margin.Today', width: 95, type: 'currencyWithFontColour', },
        ]
      }

    ]
  }

 

  onGridReady(params) {
    if(this.reportType==='sites'){
      this.service.gridApiOrderRate = params.api;
    }else{
      this.service.gridApiRegionsOrderRate = params.api;
    }


    this.gridColumnApi = params.columnApi;

    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
   // to be able to then reference this things within custom menu methods
   if(this.reportType==='sites'){
    this.service.gridApiOrderRate.sizeColumnsToFit();
  }else{
    this.service.gridApiRegionsOrderRate.sizeColumnsToFit();
  }

    this.resizeGrid();
    this.selections.triggerSpinner.next({show:false});
  }


  // getMainMenuItems() {

  //   let embellishedMenu = [];

  //   embellishedMenu.push({
  //     name: "Top / bottom 1",
  //     action: 'highlightTopBottom',
  //     count: 1,
  //     isButton: true,

  //   });

  //   embellishedMenu.push({
  //     name: "Top / bottom 3",
  //     action: 'highlightTopBottom',
  //     count: 3,
  //     isButton: true,
  //   });

  //   embellishedMenu.push({
  //     name: "Top / bottom 5",
  //     action: 'highlightTopBottom',
  //     count: 5,
  //     isButton: true,
  //   });


  //   embellishedMenu.push({
  //     name: 'divider',
  //     isButton: false,
  //   });

  //   embellishedMenu.push({
  //     name: "Clear Highlighting",
  //     action: 'clearHighlighting',
  //     isButton: true,
  //   });

  //   embellishedMenu.push({
  //     name: 'divider',
  //     isButton: false,
  //   });

  //   return embellishedMenu

  // }

  resizeGrid() {
    setTimeout(() => {

      this.gridColumnApi?.autoSizeAllColumns();
      if(this.reportType==='sites'){
        this.service.gridApiOrderRate?.sizeColumnsToFit();
      }else{
        this.service.gridApiRegionsOrderRate?.sizeColumnsToFit();
      }

    }, 10)
  }

 

  onCellClick(site) {
    this.cellClicked.next(site);
  }

  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.reportType === 'sites' ? this.service.gridApiOrderRate.getModel() : this.service.gridApiRegionsOrderRate.getModel();
    let initialColsToColour = 1
    if (this.service.salesPerformanceReportType == 'vsLastYear' || this.service.salesPerformanceReportType == 'vsBudget') initialColsToColour = 2
    this.excel.createSheetObject(tableModel, 'Sales Performance', 1, initialColsToColour);
  }


}
