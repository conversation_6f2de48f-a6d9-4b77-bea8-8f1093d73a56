<!-- Header -->
<div class="modal-header">
  <div class="spaceBetween">

    <h4 class="modal-title" id="modal-basic-title">
      {{service.siteNameForModal}} {{constants.translatedText.Dashboard_SalesPerformance_OrderRateAndProjection}}
    </h4>
    <div class="buttonGroup">
      <button class="btn btn-primary" (click)="service.selectMeasure(measure.measure)"
        [ngClass]="{'active':measure.measure == service.chosenMeasure}" *ngFor="let measure of service.measures">
        {{measure.translation}}
      </button>
    </div>
  </div>
  <button type="button" class="close" aria-label="Close">
    <span aria-hidden="true" (click)="onCancelButtonClick()" id="salesPerfModalCloseButton">&times;</span>
  </button>
</div>


<!-- Body -->
<div [ngClass]="constants.environment.customer" class="modal-body">

  <div id="chartAndPerUnitTableHolder">

    <div id="chartHolder">
      <projectedChart [MeasureName]="service.chosenMeasure" [chosenSite]="service.chosenSite"
        [IsCurrentMonth]="service.IsCurrentMonth">
      </projectedChart>
    </div>

    <div id="perUnitTableHolder">
      <table class="cph noStripes fullWidth">

        <thead>
          <tr>
            <th class="noBottomBorder"></th>
            <th>{{ constants.translatedText.Target }}</th>
            <th class="thin"></th>
            <th colspan="5">
              {{constants.translatedText.Done}} ({{service.Days.elapsed | number: '1.0-0' }}
              {{constants.translatedText.DaysLower}} {{constants.translatedText.Elapsed}})
            </th>
            <th *ngIf="service.IsCurrentMonth" class="thin"></th>
            <th *ngIf="service.IsCurrentMonth" colspan="3">
              {{ constants.translatedText.OrderRate }} ({{ service.Days.remaining | number: '1.0-0' }}
              {{ constants.translatedText.DaysLower }} {{ constants.translatedText.ToGo | lowercase }})
            </th>
          </tr>
          <tr>
            <th></th>
            <th></th>
            <th></th>
            <th>{{constants.translatedText.BroughtIn}}</th>
            <th>
              {{ constants.translatedText.OrderedIn }}
              <span *ngIf="service.deliveryDate.amSelectingMonth">
                {{ service.deliveryDate.monthName.substring(0,3) }}
              </span>

            </th>
            <th>{{constants.translatedText.Done}}</th>
            <th>%</th>
            <th>
              <span *ngIf="service.chosenSite.gap>0" class="goodFont">{{ constants.translatedText.AheadBy
                }}</span>
              <span *ngIf="service.chosenSite.gap==0">{{ constants.translatedText.OnTarget }}</span>
              <span *ngIf="service.chosenSite.gap<0" class="badFont">{{ constants.translatedText.ShortBy
                }}</span>
            </th>
            <th *ngIf="service.IsCurrentMonth"></th>
            <th *ngIf="service.IsCurrentMonth">{{ constants.translatedText.Projection }}</th>
            <th *ngIf="service.IsCurrentMonth">%</th>
            <th *ngIf="service.IsCurrentMonth">
              <span *ngIf="service.chosenSite.projectedGap>0" class="goodFont">{{
                constants.translatedText.AheadBy }}</span>
              <span *ngIf="service.chosenSite.projectedGap==0">{{ constants.translatedText.OnTarget }}</span>
              <span *ngIf="service.chosenSite.projectedGap<0" class="badFont">{{ constants.translatedText.ShortBy
                }}</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td *ngIf="'Units' == service.chosenMeasure">{{ constants.translatedText.Units }}</td>
            <td *ngIf="'Margin' == service.chosenMeasure">{{ constants.translatedText.Margin }}</td>

            <td>{{service.chosenSite.target |cph:'number':0}}</td>
            <td></td>
            <td>{{service.chosenSite.broughtIn|cph:'number':0}}</td>
            <td>{{service.chosenSite.retailInMonth|cph:'number':0}}</td>
            <td>{{service.chosenSite.done|cph:'number':0}}</td>
            <td>{{service.chosenSite.done/service.chosenSite.target|cph:'percent':0}}</td>
            <td>
              <span *ngIf="service.chosenSite.gap>0" class="goodFont">
                {{abs(service.chosenSite.gap)|cph:'number':0}}</span>
              <span *ngIf="service.chosenSite.gap==0" class="">
                {{abs(service.chosenSite.gap)|cph:'number':0}}</span>
              <span *ngIf="service.chosenSite.gap<0" class="badFont">
                {{abs(service.chosenSite.gap)|cph:'number':0}}</span>
            </td>
            <td *ngIf="service.IsCurrentMonth"></td>
            <td *ngIf="service.IsCurrentMonth">
              {{service.chosenSite.projection|cph:'number':0}}</td>
            <td *ngIf="service.IsCurrentMonth">
              {{service.chosenSite.projection/service.chosenSite.target|cph:'percent':0}}</td>
            <td *ngIf="service.IsCurrentMonth">
              <span *ngIf="service.chosenSite.projectedGap>0" class="goodFont">
                {{abs(service.chosenSite.projectedGap)|cph:'number':0}}</span>
              <span *ngIf="service.chosenSite.projectedGap==0">
                {{abs(service.chosenSite.projectedGap)|cph:'number':0}}</span>
              <span *ngIf="service.chosenSite.projectedGap<0" class="badFont">
                {{abs(service.chosenSite.projectedGap)|cph:'number':0}}</span>
            </td>
          </tr>
          <tr>
            <td>{{ constants.translatedText.PerDay }}</td>
            <td></td>
            <td></td>
            <td></td>
            <td>{{service.chosenSite.retailInMonth/service.Days.elapsed|cph:'number':1}}</td>
            <td></td>
            <td></td>
            <td>
              <span *ngIf="service.chosenSite.gap>0" class="goodFont">
                -{{abs(service.chosenSite.gap)/service.Days.remaining|cph:'number':1}}</span>
              <span *ngIf="service.chosenSite.gap==0">
                {{abs(service.chosenSite.gap)/service.Days.remaining|cph:'number':1}}</span>
              <span *ngIf="service.chosenSite.gap<0" class="badFont">
                {{abs(service.chosenSite.gap)/service.Days.remaining|cph:'number':1}}</span>
            </td>
            <td *ngIf="service.IsCurrentMonth"></td>
            <td *ngIf="service.IsCurrentMonth"></td>
            <td *ngIf="service.IsCurrentMonth"></td>
            <td *ngIf="service.IsCurrentMonth"></td>
          </tr>
        </tbody>

      </table>
    </div>

  </div>

</div>




<!-- Footer -->
<div class="modal-footer">
  <button type="button" class="btn btn-primary"
    (click)="onCancelButtonClick()">{{constants.translatedText.Close}}</button>
</div>