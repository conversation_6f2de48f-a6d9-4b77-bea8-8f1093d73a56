﻿using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;

public class VehicleDetailModalItem
{

   public VehicleAdvertDetail AdvertDetail { get; set; }
   public IEnumerable<StrategyPriceBuildUp> PriceBuildUp { get; set; }
   public IEnumerable<SameModelAdvert> SameModelAdverts { get; set; }
   public AdvertHistoryAndFutureChart Chart { get; set; }
   public CompetitorSummary CompetitorSummary { get; set; }
   public List<VehicleAdComment> Comments { get; set; }

   public ValuationSummary VehicleValuationDetail { get; set; }

   public List<DayToSellAndPriceIndicatorScenario> PricingScenarios { get; set; }
   public List<LocationOptimiserAdvert> LocationChanges { get; set; }
   public PriceChangeToday PriceChangeToday { get; set; }
   public LeavingPriceBasicItem LeavingPrice { get; set; }
   public int RetailerSiteIdForPostcode { get; set; }
}
