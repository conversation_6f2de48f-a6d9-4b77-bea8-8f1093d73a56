﻿using log4net;
using OpenQA.Selenium;

using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CPHI.Spark.WebScraper.Jobs
{

   public class AlcopaScrapeJob : IJob
   {
      private DateTime startTime = DateTime.Now;
      private ChromeDriver _driver;
      private static readonly ILog logger = LogManager.GetLogger(typeof(AlcopaScrapeJob));

      private const string username = "<EMAIL>";
      private const string password = "q4bi3WrZE53aE5Y";

      //private const string jobName = "AlcopaScrape";

      private const string incomingFilePattern = "-quote";
      private const string incomingFileExt = ".zip";

      private const string outgoingFileName = "Alcopa";
      private const string outgoingFileExt = ".xlsx";

      private string customerName;
      private string fileDestination;
      //private string fileDestinationDev;

      public void Execute() { }
      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         fileDestination = ConfigService.FileDestination;
         //fileDestinationDev = ConfigService.FileDestinationDev;
         fileDestination = fileDestination.Replace("{destinationFolder}", "spain");
         //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "spain");
         customerName = "Spain";


         try
         {
            ScraperMethodsService.ClearDownloadsFolder();
            var service = ChromeDriverService.CreateDefaultService();
            service.HostName = "127.0.0.1";

            ChromeOptions options = ScraperMethodsService.SetChromeOptions("RRGSpainAlcopa", 80);

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
            _driver.Manage().Cookies.DeleteAllCookies();

            Login();
            logger.Info("Succesfully logged in");
            GetReport();
            logger.Info("Succesfully got report");
            DownloadReport();
            logger.Info("Succesfully downloaded report");

            _driver.Manage().Cookies.DeleteAllCookies();
            _driver.Quit();
            _driver.Dispose();

            logger.Info("Finished");
            stopwatch.Stop();
         }

         catch (Exception e)
         {
            stopwatch.Stop();
            errorMessage = e.ToString();
            logger.Error($"General Problem {e.ToString()}");
            EmailerService eService = new EmailerService();
            await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");
         }
         finally
         {
            // Completely delete the chrome profile
            var dir = new DirectoryInfo("C:\\cphiRoot\\ChromeProfiles\\RRGSpainAlcopa");
            dir.Attributes = dir.Attributes & ~FileAttributes.ReadOnly;
            dir.Delete(true);

            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "WebScraper",
               Customer = "RRG Spain",
               Environment = "PROD",
               Task = this.GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };
            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }
      }

      private void Login()
      {
         //go to login page
         _driver.Navigate()
             .GoToUrl("https://es.rrg-cotations.com/");

         try
         {
            //wait for it to appear
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));

            IWebElement loginExpired = null;

            // Login expired message - go to login page again
            try
            {
               loginExpired = WaitAndFind("/html/body/div/div[3]/div/div[2]/div/a/span[1]", 100, false);

               if (loginExpired != null)
               {
                  loginExpired.Click();
                  System.Threading.Thread.Sleep(2000);
               }
            }
            catch
            {

            }

            string loginButtonPath = "//button[@type='submit' and .//span[text()='Log In']]";
            IWebElement loginButton = wait.Until(SeleniumExtras.WaitHelpers.ExpectedConditions.ElementExists(By.XPath(loginButtonPath)));
            Assert.Equal("Log In", loginButton.Text);
            //Assert.Contains("Welcome to Groupe Renault", _driver.PageSource);

            _driver.FindElement(By.XPath("/html/body/div/div/div/div/form/div[1]/div/input")).SendKeys(username);
            _driver.FindElement(By.XPath("/html/body/div/div/div/div/form/div[2]/div/input")).SendKeys(password);
            _driver.FindElement(By.XPath(loginButtonPath)).Click();

            IWebElement dashboardTitle = wait.Until(ExpectedConditions.ElementExists(By.XPath("//body//header/div/div[1]/h1")));
         }
         catch
         {

         }


      }

      private void GetReport()
      {
         // Quotes Monitoring
         WaitAndFind("/html/body/div/div[2]/div/div/div/div[2]/button/span[1]/span", 150, true);

         // Quotes Monitoring -> Open filters
         WaitAndFind("/html/body/div/div[2]/div/div[2]/div[1]/div/div[1]/div[2]/span", 150, true);

         // Quotes Monitoring -> Open filters -> Select all groups  
         WaitAndFind("/html/body/div/div[2]/div/div[3]/div/div/form/div/div[1]/div/div/details/summary/div/label/span[1]/span[1]/input", 100, true);

         // /html/body/div/div[2]/div/div[3]/div/div/form/div/div[1]/div/div/details/summary/div/label/span[1]/span[1]/input

         DateTime date = DateTime.Today;
         var sevenDaysAgo = date.Date.AddDays(-7);

         //var firstDayOfMonth = new DateTime(date.Year, date.Month, 1);
         //var firstDayOfMonthOneYearAgo = firstDayOfMonth.AddYears(-1);
         //var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

         //find out which date format this machine is using. the VM uses american format.
         string systemDateFormat = CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern;

         // Quotes Monitoring -> Open filters -> Set dates: 1st and last of month
         _driver.FindElement(By.Name("dateAfter")).SendKeys(sevenDaysAgo.ToString(systemDateFormat));
         System.Threading.Thread.Sleep(200);
         _driver.FindElement(By.Name("dateBefore")).SendKeys(date.ToString(systemDateFormat));

         // Quotes Monitoring -> Open filters -> Export
         WaitAndFind("/html/body/div/div[2]/div/div[2]/div[3]/div/div[1]/div[2]/span", 100, true);

         // Loop to check all the boxes 
         // Redundant with new Global Export button
         //TickAllBoxes();

         // Show empty columns
         WaitAndFind("/html/body/div/div[2]/div/div[3]/div/div/div/div[4]/div/label[1]/span[1]/span[1]/input", 100, true);

         // Select Global Export and go to Download page
         WaitAndFind("/html/body/div/div[2]/div/div[3]/div/div/div/div[5]/div[2]/button/span[1]", 100, true);

         // Go to Download page
         //  WaitAndFind("/html/body/div/div[2]/div/div[3]/div/div/div/div[5]/div[1]/button/span[1]", 200, true);

      }

      private void DownloadReport()
      {
         System.Threading.Thread.Sleep(2000);

         string downloadLinkText = "";
         string linkReadyText = "";

         // Will basically keep refreshing the page until link is active
         while (downloadLinkText.Trim() != "Download" && linkReadyText.Trim() != "Ready")
         {
            _driver.Navigate()
                .GoToUrl("https://es.rrg-cotations.com/export-list");
            downloadLinkText = CheckDownloadLink(downloadLinkText);
            linkReadyText = CheckReadyLink(linkReadyText);
         }

         System.Threading.Thread.Sleep(8000);

         // Click Download
         WaitAndFind("/html/body/div/div[2]/div/table/tbody/tr[1]/td[6]/button/span[1]/span", 100, true);

         DateTime start = DateTime.Now;

         FileInfo[] recentlySavedFiles = FindFiles(incomingFilePattern);
         TimeSpan waitTime = DateTime.Now - start;

         // Check files have been received
         while (recentlySavedFiles.Length == 0 && waitTime.Seconds < 120)
         {
            recentlySavedFiles = FindFiles(incomingFilePattern);
            System.Threading.Thread.Sleep(200);
            waitTime = DateTime.Now - start;
         }

         string filePath = recentlySavedFiles[0].FullName;

         FileInfo[] extractedDir = ExtractFromZip(filePath);
         MoveFile(extractedDir[0].FullName, outgoingFileName + outgoingFileExt);

         File.Delete(filePath); // Delete zip file
         Directory.Delete(filePath + "Uncompressed"); // Delete the now empty directory

      }

      private FileInfo[] ExtractFromZip(string filePath)
      {

         try
         {
            using (ZipArchive archive = ZipFile.OpenRead(filePath))
            {
               //ZipFile.CreateFromDirectory(pathNoFile, filePath);
               ZipFile.ExtractToDirectory(filePath, filePath + "Uncompressed"); //unzips the zip file in arg1 to a new directory named in arg2

               FileInfo[] extractedDir = new DirectoryInfo(filePath + "Uncompressed").EnumerateFiles().ToArray();

               archive.Dispose();

               return extractedDir;

            }
         }
         catch (Exception e)
         {
            logger.Error($"Failed to extract .zip file. Problem {e.ToString()}");
            throw e;
         }

      }

      private string CheckReadyLink(string readyLinkText)
      {
         System.Threading.Thread.Sleep(500);

         try
         {
            IWebElement readyLink = WaitAndFind("/html/body/div/div[2]/div/table/tbody/tr[1]/td[5]", 100) != null ?
                WaitAndFind("/html/body/div/div[2]/div/table/tbody/tr[1]/td[5]", 100) : null;

            if (readyLink != null)
            {
               readyLinkText = readyLink.Text;
            }

         }
         catch { return ""; }

         return readyLinkText;
      }

      private string CheckDownloadLink(string downloadLinkText)
      {
         System.Threading.Thread.Sleep(500);

         try
         {
            IWebElement downloadLink = WaitAndFind("//body/div/div[2]/div/table/tbody/tr[1]/td[6]/button/span[1]/span", 100) != null ? WaitAndFind("//body/div/div[2]/div/table/tbody/tr[1]/td[6]/button/span[1]/span", 100) : null;

            if (downloadLink != null)
            {
               downloadLinkText = downloadLink.Text;
            }

         }
         catch { return ""; }

         return downloadLinkText;
      }

      private void TickAllBoxes()
      {

         // Loop to check all the boxes
         for (int i = 1; i < 16; i++)
         {
            IWebElement chkbox = _driver.FindElement(By.XPath($"/html/body/div/div[2]/div/div[3]/div/div/div/div[2]/div[{i}]/label/span[1]/span[1]/input"));
            System.Threading.Thread.Sleep(16);

            if (!chkbox.Selected) { chkbox.Click(); }

         }

      }

      public void MoveFile(string oldFilePath, string fileName)
      {
         string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
         string newFilePathAndName = $@"{fileDestination}{prefix}{fileName}";
         //string newFilePathAndNameDev = $@"{fileDestinationDev}{prefix}{fileName}";

         string oldLocation = oldFilePath;

         File.Move(oldLocation, newFilePathAndName); //move them to incoming
                                                     //File.Copy(newFilePathAndName, newFilePathAndNameDev); //copy to dev

         TimeSpan duration = DateTime.Now - startTime;
         logger.Info($"Moved file from {oldLocation} to {newFilePathAndName}. Ran for {duration.Seconds} seconds");
      }

      private static FileInfo[] FindFiles(string filenameFragment)
      {

         FileInfo[] recentlySavedFiles = new DirectoryInfo(ConfigService.FileDownloadLocation).EnumerateFiles().Select(x =>
         {
            x.Refresh();
            return x;
         }).Where(x => x.Name.Contains(filenameFragment) && x.Extension == incomingFileExt).ToArray();
         return recentlySavedFiles;
      }

      private IWebElement WaitAndFind(string target, int sleepTime, bool andClick = false)
      {
         IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "AlcopaScrape", target, andClick);

         System.Threading.Thread.Sleep(sleepTime);

         return result;
      }


   }
}

