import { EventEmitter, Injectable } from "@angular/core";
import { LeavingVehicleItem } from "src/app/model/LeavingVehicleItem";
import { LeavingVehicleBarChartSet } from "src/app/model/LeavingVehicleBarChartSet";
import { SelectionsService } from "src/app/services/selections.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { GetLeavingVehicleItemsParams } from "src/app/model/GetLeavingVehicleItemsParams";
import { RetailerSite } from "src/app/model/RetailerSite";
import { ConstantsService } from "src/app/services/constants.service";
import { DashboardMeasure } from "src/app/model/DashboardMeasure";
import { BIChartTileDataType } from "src/app/components/biChartTile/biChartTile.component";
import { VNTileParams } from "src/app/model/VNTileParams";
import { VNTileTableRow } from "src/app/model/VNTileTableRow";
import { BITilesService } from "src/app/services/biTiles.service";
import { BigLeavingChartParams } from "src/app/model/BigLeavingChartParams";
import { SummaryStats } from "./SummaryStats";
import { ExternalFiltersForLeavingVehicleAnalysis } from "src/app/model/ExternalFiltersForLeavingVehicleAnalysis";
import { forkJoin } from "rxjs";
@Injectable({
  providedIn: 'root'
})

export class LeavingVehicleTrendsService {
  rawDataWas: LeavingVehicleItem[];
  rawDataNow: LeavingVehicleItem[];
  rawDataFilteredWas: LeavingVehicleItem[];
  rawDataFilteredNow: LeavingVehicleItem[];
  rawDataHighlighted: LeavingVehicleItem[];
  rawDataHighlightedWas: LeavingVehicleItem[];
  smallChartSetData: LeavingVehicleBarChartSet;
  smallChartSetDataWas: LeavingVehicleBarChartSet;
  smallChartSetDataVariance: LeavingVehicleBarChartSet;
  summaryStats: SummaryStats;

  //user choices
  chosenRetailerSites: RetailerSite[];
  startDateWas: Date;
  endDateWas: Date;
  startDateNow: Date;
  endDateNow: Date;

  newSmallChartDataEmitter: EventEmitter<LeavingVehicleBarChartSet> = new EventEmitter();
  newSmallChartDataEmitterWas: EventEmitter<LeavingVehicleBarChartSet> = new EventEmitter();
  newSmallChartDataEmitterVariance: EventEmitter<LeavingVehicleBarChartSet> = new EventEmitter();
  filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  refreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
  refreshTileEmitter: EventEmitter<void> = new EventEmitter();

  filterChoices: DashboardMeasure[]
  highlightChoices: DashboardMeasure[]
  externalFilterForLeavingVehicleAnalysis: ExternalFiltersForLeavingVehicleAnalysis;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    private getDataService: GetDataMethodsService,
    private biTilesService: BITilesService
  ) {

  }

  initParams() {
    if (!this.chosenRetailerSites) {
      this.chosenRetailerSites = this.constants.RetailerSites
    }
    this.filterChoices = this.setChoices();
    this.highlightChoices = this.setChoices();

    if (!this.startDateWas) {
      const lastQuarter = this.constants.getLastQuarterRange();
      this.startDateWas = lastQuarter.start;
      this.endDateWas = lastQuarter.end;

      this.startDateNow = this.constants.getThisQuarterStart();
      this.endDateNow = this.constants.endOfMonth(new Date());
    }

    this.externalFilterForLeavingVehicleAnalysis = {
      //AchievedSaleType: null,
      BodyType: null,
      DaysListedBand: null,
      FirstPPBand: null,
      FuelType: null,
      LastPPBand: null,
      LastPriceBand: null,
      LastPriceIndicator: null,
      Make: null,
      Mileage: null,
      Model: null,
      Region: null,
      RegYear: null,
      RetailRatingBand: null,
      RetailerSiteName: null,
      TransmissionType: null,
      VehicleType: null ,
      OptedOutPctBand:null
    }
  }

  setChoices() {
    let fields: string[] = ['Region',  'RetailerSiteName', 'FuelType', 'Make', 'BodyType', 'TransmissionType', 'Model',
      'RegYear', 'DaysListedBand', 'FirstPPBand', 'LastPPBand', 'RetailRatingBand', 'MileageBand', 'LastPriceBand', 'LastPriceIndicator','IsOnStrategy','OptedOutPctBand'] //'AchievedSaleType',
    let result = []
    fields.forEach(field => {
      result.push({ FieldName: field, FieldNameTranslation: field, IsDate: false, ChosenValues: [] },)
    })
    return result;
  }

  async getData() {
    try {
      let requests = [];

      const paramsWas: GetLeavingVehicleItemsParams = {
        StartDate: this.startDateWas,
        EndDate: this.endDateWas,
        IncludeNewVehicles: false,
        IncludeUsedVehicles: true,
        ChosenRetailerSiteIds: this.chosenRetailerSites.map(x => x.Id)
      }

      requests.push(this.getDataService.getLeavingVehicleItems(paramsWas));

      const paramsNow: GetLeavingVehicleItemsParams = {
        StartDate: this.startDateNow,
        EndDate: this.endDateNow,
        IncludeNewVehicles: false,
        IncludeUsedVehicles: true,
        ChosenRetailerSiteIds: this.chosenRetailerSites.map(x => x.Id)
      }

      requests.push(this.getDataService.getLeavingVehicleItems(paramsNow));

      await forkJoin(requests).subscribe((res: any) => {
        this.rawDataWas = res[0];
        this.rawDataNow = res[1];
        this.updateFilteredData();
        this.selections.triggerSpinner.emit({ show: false });
      }, e => {
        console.error('Some requests failed');
      })
    }

    catch (error) {
      console.error('Error fetching leaving vehicle items', error);
      this.constants.toastDanger('Failed to load leaving vehicles');
      this.selections.triggerSpinner.emit({ show: false });
    }
  }

  analyseSmallChartsData(data: LeavingVehicleItem[]) {
    let smallChartSet: LeavingVehicleBarChartSet = new LeavingVehicleBarChartSet;

    smallChartSet.calculateSet(data);

    this.smallChartSetData = smallChartSet;

    this.newSmallChartDataEmitter.emit(this.smallChartSetData);
  }

  analyseSmallChartsDataWas(data: LeavingVehicleItem[]) {
    let smallChartSet: LeavingVehicleBarChartSet = new LeavingVehicleBarChartSet();
    smallChartSet.calculateSet(data);
    this.smallChartSetDataWas = smallChartSet;
    this.newSmallChartDataEmitterWas.emit(this.smallChartSetDataWas);
  }

  updateFilteredData() {
    this.rawDataFilteredWas = this.biTilesService.filterData(this.rawDataWas, this.filterChoices);
    this.rawDataFilteredNow = this.biTilesService.filterData(this.rawDataNow, this.filterChoices);

    this.updateHighlightedData();
  }

  updateHighlightedData() {

    this.rawDataHighlighted = this.biTilesService.filterData(this.rawDataFilteredNow, this.highlightChoices);
    this.analyseSmallChartsData(this.rawDataHighlighted);

    this.rawDataHighlightedWas = this.biTilesService.filterData(this.rawDataFilteredWas, this.highlightChoices);
    this.analyseSmallChartsDataWas(this.rawDataHighlightedWas);

    this.getWasAndNowVariance();
    this.getChartsYMax();

    this.recalcSummaryStats();

    this.selections.triggerSpinner.emit({ show: false });
    this.refreshTileEmitter.emit();
  }

  getPageParams(): VNTileParams {
    return {
      highlightChoices: this.highlightChoices,
      filterChoices: this.filterChoices,
      filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
      highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
      updateThisPicker: this.refreshFilterListsEmitter,
      updateThisTile: this.refreshTileEmitter,
      parentMethods: {
        buildRows: (fieldName, dataType) => this.buildTableRows(fieldName, dataType),
        highlightRow: (row, fieldName) => this.biTilesService.highlightRow(row, fieldName, this.highlightChoices),
        provideItemsList: (fieldName, isDateField) => this.provideItemsList(fieldName, isDateField)
      }
    };
  }

  provideItemsList(fieldName: string, isDateField: boolean) {
    if (!this.rawDataNow) { return [] }
    return [...new Set(this.rawDataNow.map(x => x[fieldName]))];
  }

  buildTableRows(fieldName: string, dataType: BIChartTileDataType) {
    let tableRows: VNTileTableRow[] = [];
    if (dataType === BIChartTileDataType.day) {
      tableRows = this.biTilesService.buildTableRowsDatesBasis(fieldName, this.rawDataNow, false);
    } else if (dataType === BIChartTileDataType.weekly) {
      tableRows = this.biTilesService.buildTableRowsDatesBasis(fieldName, this.rawDataNow, true);
    }
    else {
      tableRows = this.biTilesService.buildTableRowsNonDatesBasis(fieldName, this.rawDataNow, this.rawDataHighlighted);
    }

    return tableRows;//.slice(0, 20);
  }

  private recalcSummaryStats() {

    const all = this.calculateStats(this.rawDataFilteredNow);
    const highlighted = this.calculateStats(this.rawDataHighlighted);
    const highlightedKeys = this.constants.createHashMap(this.rawDataHighlighted, 'FirstSnapId')
    const notHighlightedData = [];
    this.rawDataFilteredNow.forEach(item => {
      if (!highlightedKeys[item.FirstSnapId]) {
        notHighlightedData.push(item)
      }
    })
    const notHighlighted = this.calculateStats(notHighlightedData);

    const vs = {
      vehicleCount: highlighted.vehicleCount - all.vehicleCount,
      firstPP: highlighted.firstPP - all.firstPP,
      lastPP: highlighted.lastPP - all.lastPP,
      changePP: highlighted.changePP - all.changePP,
      retailRating: highlighted.retailRating - all.retailRating,
      daysListed: highlighted.daysListed - all.daysListed,
    }
    const highlightedVsNot = {
      vehicleCount: highlighted.vehicleCount - notHighlighted.vehicleCount,
      firstPP: highlighted.firstPP - notHighlighted.firstPP,
      lastPP: highlighted.lastPP - notHighlighted.lastPP,
      changePP: highlighted.changePP - notHighlighted.changePP,
      retailRating: highlighted.retailRating - notHighlighted.retailRating,
      daysListed: highlighted.daysListed - notHighlighted.daysListed,
    }

    this.summaryStats = {
      all: all,
      highlighted: highlighted,
      vs: vs,
      notHighlighted: notHighlighted,
      highlightedVsNot: highlightedVsNot
    }
  }

  private calculateStats(items: LeavingVehicleItem[]) {
    let volumeCum = 0;
    let daysListedCum = 0;
    let firstPPCum = 0;
    let lastPPCum = 0;
    let rrCum = 0;

    items.forEach(item => {
      volumeCum++;
      daysListedCum += item.DaysListed;
      firstPPCum += item.FirstPP;
      lastPPCum += item.LastPP;
      rrCum += item.LastRetailRating;
    });
    const firstPP = this.constants.div(firstPPCum, volumeCum);
    const lastPP = this.constants.div(lastPPCum, volumeCum);
    const rr = this.constants.div(rrCum, volumeCum);
    const dl = this.constants.div(daysListedCum, volumeCum);

    const allResult = {
      vehicleCount: volumeCum,
      firstPP: firstPP,
      lastPP: lastPP,
      changePP: firstPP - lastPP,
      retailRating: rr,
      daysListed: dl
    };
    return allResult;
  }

  getWasAndNowVariance() {
    this.smallChartSetDataVariance = this.constants.clone(this.smallChartSetDataWas);

    for (let key in this.smallChartSetDataVariance) {
      let variance = this.smallChartSetDataVariance[key];
      let now = this.smallChartSetData[key];

      variance.change = 0;
      variance.percentageValuesWas = [];
      variance.percentageValuesNow = [];

      let totalWas: number = this.smallChartSetDataWas[key].Values.reduce((sum, num) => sum + num, 0);
      let totalNow: number = this.smallChartSetData[key].Values.reduce((sum, num) => sum + num, 0);

      variance.Values.forEach((value, i) => {
        variance.Values[i] = now.Values[i] - value;
        variance.percentageValuesWas.push(value / totalWas);
        variance.percentageValuesNow.push(now.Values[i] / totalNow);
        variance.change = key === 'rrSoldVolume' ? (totalNow - totalWas) : this.smallChartSetData[key].average - this.smallChartSetDataWas[key].average;
      })
    }

    this.newSmallChartDataEmitterVariance.emit(this.smallChartSetDataVariance);
  }

  getChartsYMax() {
     for (const key in this.smallChartSetData) {
        if (this.smallChartSetData.hasOwnProperty(key)) {
           const current: number[] = this.smallChartSetData[key].Values;
           const previous: number[] = this.smallChartSetDataWas[key].Values;

           const max = Math.max(...current, ...previous);

           this.smallChartSetData[key].yMax = max;
           this.smallChartSetDataWas[key].yMax = max;
        }
     }
  }
}
