import {EventEmitter, Injectable, OnDestroy} from "@angular/core";
import {LeavingVehicleItem} from "src/app/model/LeavingVehicleItem";
import {LeavingVehicleBarChartSet} from "src/app/model/LeavingVehicleBarChartSet";
import {SelectionsService} from "src/app/services/selections.service";
import {GetDataMethodsService} from "src/app/services/getDataMethods.service";
import {GetLeavingVehicleItemsParams} from "src/app/model/GetLeavingVehicleItemsParams";
import {RetailerSite} from "src/app/model/RetailerSite";
import {ConstantsService} from "src/app/services/constants.service";
import {DashboardMeasure} from "src/app/model/DashboardMeasure";
import {BIChartTileDataType} from "src/app/components/biChartTile/biChartTile.component";
import {VNTileParams} from "src/app/model/VNTileParams";
import {VNTileTableRow} from "src/app/model/VNTileTableRow";
import {BITilesService} from "src/app/services/biTiles.service";
import {BigLeavingChartParams} from "src/app/model/BigLeavingChartParams";
import {SummaryStats} from "./SummaryStats";
import {ExternalFiltersForLeavingVehicleAnalysis} from "src/app/model/ExternalFiltersForLeavingVehicleAnalysis";
import {forkJoin} from "rxjs";
import {WasNowEnum} from "../leavingVehicles/leavingVehicleWasNowEnum";
import {VNTileParamsWasNow} from "../../../model/VNTileParamsWasNow";
import moment from "moment";

@Injectable({
   providedIn: 'root'
})

export class LeavingVehicleTrendsService implements OnDestroy {

   rawData: { [key in WasNowEnum]: LeavingVehicleItem[] } = {[WasNowEnum.Now]: [], [WasNowEnum.Was]: []};
   rawDataFiltered: { [key in WasNowEnum]: LeavingVehicleItem[] } = {[WasNowEnum.Now]: [], [WasNowEnum.Was]: []};
   rawDataHighlighted: { [key in WasNowEnum]: LeavingVehicleItem[] } = {[WasNowEnum.Now]: [], [WasNowEnum.Was]: []};
   smallChartSetData: { [key in WasNowEnum]: LeavingVehicleBarChartSet } = {
      [WasNowEnum.Now]: null,
      [WasNowEnum.Was]: null
   };

   smallChartSetDataVariance: LeavingVehicleBarChartSet;
   summaryStats: { [key in WasNowEnum]: SummaryStats } = {[WasNowEnum.Now]: null, [WasNowEnum.Was]: null};

   chosenRetailerSites: RetailerSite[];
   startDate: { [key in WasNowEnum]: Date } = {[WasNowEnum.Now]: null, [WasNowEnum.Was]: null};
   endDate: { [key in WasNowEnum]: Date } = {[WasNowEnum.Now]: null, [WasNowEnum.Was]: null};
   syncSelections = true;

   newSmallChartDataEmitterWas: EventEmitter<LeavingVehicleBarChartSet> = new EventEmitter();
   newSmallChartDataEmitterNow: EventEmitter<LeavingVehicleBarChartSet> = new EventEmitter();
   newSmallChartDataEmitterVariance: EventEmitter<LeavingVehicleBarChartSet> = new EventEmitter();
   filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
   highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
   refreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
   refreshTileEmitter: EventEmitter<void> = new EventEmitter();

   filterChoices: { [wasNow in WasNowEnum]: DashboardMeasure[] } = {[WasNowEnum.Now]: [], [WasNowEnum.Was]: []};
   highlightChoices: { [wasNow in WasNowEnum]: DashboardMeasure[] } = {[WasNowEnum.Now]: [], [WasNowEnum.Was]: []};
   externalFilterForLeavingVehicleAnalysis: ExternalFiltersForLeavingVehicleAnalysis;
   syncPeriods = false;

   constructor(
      public constants: ConstantsService,
      public selections: SelectionsService,
      private getDataService: GetDataMethodsService,
      private biTilesService: BITilesService
   ) {

   }

   initParams() {
      if (!this.chosenRetailerSites) {
         this.chosenRetailerSites = this.constants.RetailerSites
      }

      [WasNowEnum.Was, WasNowEnum.Now].forEach(wasNow => {
         this.filterChoices[wasNow] = this.setChoices();
         this.highlightChoices[wasNow] = this.setChoices();
      });

      if (!this.startDate[WasNowEnum.Was]) {
         this.initDateRanges();
      }

      this.externalFilterForLeavingVehicleAnalysis = {
         BodyType: null,
         DaysListedBand: null,
         FirstPPBand: null,
         FuelType: null,
         LastPPBand: null,
         LastPriceBand: null,
         LastPriceIndicator: null,
         Make: null,
         Mileage: null,
         Model: null,
         Region: null,
         RegYear: null,
         RetailRatingBand: null,
         RetailerSiteName: null,
         TransmissionType: null,
         VehicleType: null,
         OptedOutPctBand: null
      }
   }

   setChoices() {
      let fields: string[] = ['Region', 'RetailerSiteName', 'FuelType', 'Make', 'BodyType', 'TransmissionType', 'Model',
         'RegYear', 'DaysListedBand', 'FirstPPBand', 'LastPPBand', 'RetailRatingBand', 'MileageBand', 'LastPriceBand', 'LastPriceIndicator', 'IsOnStrategy', 'OptedOutPctBand'] //'AchievedSaleType',
      let result = []
      fields.forEach(field => {
         result.push({FieldName: field, FieldNameTranslation: field, IsDate: false, ChosenValues: []});
      })
      return result;
   }

   async getData() {

      this.selections.triggerSpinner.emit({show: true, message: 'Loading...'});

      try {

         const requests = [];

         const paramsWas: GetLeavingVehicleItemsParams = {
            StartDate: this.startDate[WasNowEnum.Was],
            EndDate: this.endDate[WasNowEnum.Was],
            IncludeNewVehicles: false,
            IncludeUsedVehicles: true,
            ChosenRetailerSiteIds: this.chosenRetailerSites.map(x => x.Id)
         };

         requests.push(this.getDataService.getLeavingVehicleItems(paramsWas));

         const paramsNow: GetLeavingVehicleItemsParams = {
            StartDate: this.startDate[WasNowEnum.Now],
            EndDate: this.endDate[WasNowEnum.Now],
            IncludeNewVehicles: false,
            IncludeUsedVehicles: true,
            ChosenRetailerSiteIds: this.chosenRetailerSites.map(x => x.Id)
         };

         requests.push(this.getDataService.getLeavingVehicleItems(paramsNow));

         return new Promise<void>((resolve, reject) => {
            forkJoin(requests).subscribe((res: any) => {
               this.rawData[WasNowEnum.Was] = res[0];
               this.rawData[WasNowEnum.Now] = res[1];
               this.selections.triggerSpinner.emit({show: false});
               this.updateFilteredData();
               resolve();
            }, error => {
               console.error('Some requests failed', error);
               reject(error);
            });
         });
      } catch (error) {
         console.error('Error fetching leaving vehicle items', error);
         this.constants.toastDanger('Failed to load leaving vehicles');
         this.selections.triggerSpinner.emit({show: false});
      }
   }

   analyseSmallChartsData(data: LeavingVehicleItem[], wasNow: WasNowEnum) {
      let smallChartSet: LeavingVehicleBarChartSet = new LeavingVehicleBarChartSet;

      smallChartSet.calculateSet(data);
      this.smallChartSetData[wasNow] = smallChartSet;

      if (wasNow === WasNowEnum.Now) {
         this.newSmallChartDataEmitterNow.emit(this.smallChartSetData[WasNowEnum.Now]);
      } else {
         this.newSmallChartDataEmitterWas.emit(this.smallChartSetData[WasNowEnum.Was]);
      }
   }

   updateFilteredData() {

      [WasNowEnum.Was, WasNowEnum.Now].forEach(wn => {
         this.rawDataFiltered[wn] = this.biTilesService.filterData(this.rawData[wn], this.filterChoices[wn]);
      });

      this.updateHighlightedData();
      this.getWasAndNowVariance();
      this.getChartsYMax();
      this.recalcSummaryStats(WasNowEnum.Now);
      this.recalcSummaryStats(WasNowEnum.Was);

      this.refreshTileEmitter.emit();
      this.selections.triggerSpinner.emit({show: false});
   }

   updateHighlightedData() {

      if (this.syncSelections) {
         this.highlightChoices[WasNowEnum.Was].forEach((choice, i) => {
            this.highlightChoices[WasNowEnum.Now][i].ChosenValues = choice.ChosenValues;
         });
      }

      [WasNowEnum.Was, WasNowEnum.Now].forEach(wn => {
         this.rawDataHighlighted[wn] = this.biTilesService.filterData(this.rawDataFiltered[wn], this.highlightChoices[wn]);
         this.analyseSmallChartsData(this.rawDataHighlighted[wn], wn);
      });

      this.getWasAndNowVariance();
      this.refreshTileEmitter.emit();
   }

   getPageParams(wasNow: WasNowEnum): VNTileParamsWasNow {
      return {
         highlightChoices: this.highlightChoices[wasNow],
         filterChoices: this.filterChoices[wasNow],
         filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
         highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
         updateThisPicker: this.refreshFilterListsEmitter,
         updateThisTile: this.refreshTileEmitter,
         parentMethods: {
            buildRows: (fieldName, dataType, tt, wn) => this.buildTableRows(fieldName, dataType, wn),
            highlightRow: (row, fieldName, wn) => {
               console.log("HR", row);
               this.biTilesService.highlightRow(row, fieldName, this.highlightChoices[wasNow]);
               if (this.syncSelections) {
                  this.biTilesService.highlightRow(row, fieldName, this.highlightChoices[WasNowEnum.Now]);
               }
            },
            provideItemsList: (fieldName, isDateField) => this.provideItemsList(fieldName, isDateField, wasNow)
         }
      };
   }

   provideItemsList(fieldName: string, isDateField: boolean, wn: WasNowEnum) {
      if (!this.rawData[wn]) {
         return [];
      }
      return [...new Set(this.rawData[wn].map(x => x[fieldName]))];
   }

   buildTableRows(fieldName: string, dataType: BIChartTileDataType, wasNow: WasNowEnum) {

      if (!this.rawData[wasNow]) {
         return [];
      }

      let tableRows: VNTileTableRow[] = [];

      if (dataType === BIChartTileDataType.day) {
         tableRows = this.biTilesService.buildTableRowsDatesBasis(fieldName, this.rawData[wasNow], false);
      } else if (dataType === BIChartTileDataType.weekly) {
         tableRows = this.biTilesService.buildTableRowsDatesBasis(fieldName, this.rawData[wasNow], true);
      } else {
         tableRows = this.biTilesService.buildTableRowsNonDatesBasis(fieldName, this.rawData[wasNow], this.rawDataHighlighted[wasNow]);
      }

      return tableRows;
   }

   private recalcSummaryStats(wasNow: WasNowEnum) {

      const all = this.calculateStats(this.rawDataFiltered[wasNow]);
      const highlighted = this.calculateStats(this.rawDataHighlighted[wasNow]);
      const highlightedKeys = this.constants.createHashMap(this.rawDataHighlighted[wasNow], 'FirstSnapId');
      const notHighlightedData = [];
      this.rawDataFiltered[wasNow].forEach(item => {
         if (!highlightedKeys[item.FirstSnapId]) {
            notHighlightedData.push(item);
         }
      });
      const notHighlighted = this.calculateStats(notHighlightedData);

      const vs = {
         vehicleCount: highlighted.vehicleCount - all.vehicleCount,
         firstPP: highlighted.firstPP - all.firstPP,
         lastPP: highlighted.lastPP - all.lastPP,
         changePP: highlighted.changePP - all.changePP,
         retailRating: highlighted.retailRating - all.retailRating,
         daysListed: highlighted.daysListed - all.daysListed,
      };

      const highlightedVsNot = {
         vehicleCount: highlighted.vehicleCount - notHighlighted.vehicleCount,
         firstPP: highlighted.firstPP - notHighlighted.firstPP,
         lastPP: highlighted.lastPP - notHighlighted.lastPP,
         changePP: highlighted.changePP - notHighlighted.changePP,
         retailRating: highlighted.retailRating - notHighlighted.retailRating,
         daysListed: highlighted.daysListed - notHighlighted.daysListed,
      };

      this.summaryStats[wasNow] = {all, highlighted, vs, notHighlighted, highlightedVsNot};
   }

   private calculateStats(items: LeavingVehicleItem[]) {
      let volumeCum = 0;
      let daysListedCum = 0;
      let firstPPCum = 0;
      let lastPPCum = 0;
      let rrCum = 0;

      items.forEach(item => {
         volumeCum++;
         daysListedCum += item.DaysListed;
         firstPPCum += item.FirstPP;
         lastPPCum += item.LastPP;
         rrCum += item.LastRetailRating;
      });
      const firstPP = this.constants.div(firstPPCum, volumeCum);
      const lastPP = this.constants.div(lastPPCum, volumeCum);
      const rr = this.constants.div(rrCum, volumeCum);
      const dl = this.constants.div(daysListedCum, volumeCum);

      const allResult = {
         vehicleCount: volumeCum,
         firstPP: firstPP,
         lastPP: lastPP,
         changePP: firstPP - lastPP,
         retailRating: rr,
         daysListed: dl
      };
      return allResult;
   }

   getWasAndNowVariance() {

      this.smallChartSetDataVariance = this.constants.clone(this.smallChartSetData[WasNowEnum.Was]);

      for (const key in this.smallChartSetDataVariance) {
         const variance = this.smallChartSetDataVariance[key];
         const now = this.smallChartSetData[WasNowEnum.Now][key];

         variance.change = 0;
         variance.percentageValuesWas = [];
         variance.percentageValuesNow = [];

         const totalWas: number = this.smallChartSetData[WasNowEnum.Was][key].Values.reduce((sum, num) => sum + num, 0);
         const totalNow: number = this.smallChartSetData[WasNowEnum.Now][key].Values.reduce((sum, num) => sum + num, 0);

         variance.Values.forEach((value, i) => {
            variance.Values[i] = now.Values[i] - value;
            variance.percentageValuesWas.push(value / totalWas);
            variance.percentageValuesNow.push(now.Values[i] / totalNow);
            variance.change = key === 'rrSoldVolume' ? (totalNow - totalWas) :
               this.smallChartSetData[WasNowEnum.Now][key].average - this.smallChartSetData[WasNowEnum.Was][key].average;
         });
      }

      this.newSmallChartDataEmitterVariance.emit(this.smallChartSetDataVariance);
   }

   getChartsYMax() {

      for (const key in this.smallChartSetData[WasNowEnum.Was]) {

         const chartValues: number[] = this.smallChartSetData[WasNowEnum.Now][key].Values;
         const chartWasValues: number[] = this.smallChartSetData[WasNowEnum.Was][key].Values;
         const combined: number[] = [...new Set(chartValues.concat(chartWasValues))];

         this.smallChartSetData[WasNowEnum.Now][key].yMax = Math.max(...combined);
         this.smallChartSetData[WasNowEnum.Was][key].yMax = Math.max(...combined);
      }
   }

   toggleSyncSelections() {
      this.syncSelections = !this.syncSelections;
   }

   toggleSyncPeriods() {
      this.syncPeriods = !this.syncPeriods;

      if (!this.syncPeriods) {
         this.initDateRanges();
      } else {
         this.startDate[WasNowEnum.Now] = this.startDate[WasNowEnum.Was];
         this.endDate[WasNowEnum.Now] = this.endDate[WasNowEnum.Was];
      }
      this.getData().then(() => {
      });
   }

   initDateRanges() {

      const lastQuarter = this.constants.getLastQuarterRange();

      this.startDate[WasNowEnum.Was] = lastQuarter.start;
      this.endDate[WasNowEnum.Was] = lastQuarter.end;

      const thisQuarter = this.constants.getThisQuarterRange();
      this.startDate[WasNowEnum.Now] = thisQuarter.start;
      this.endDate[WasNowEnum.Now] = thisQuarter.end;
   }

   ngOnDestroy() {
      // Complete all EventEmitters
      this.highlightChoiceMadeEmitter.complete();
      this.refreshFilterListsEmitter.complete();
      this.refreshTileEmitter.complete();
      this.newSmallChartDataEmitterVariance.complete();
   }
}
