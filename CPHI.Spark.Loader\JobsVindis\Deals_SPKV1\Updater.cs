﻿using CPHI.Spark.Model;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using CPHI.Spark.Loader.Comparers;
using CPHI.Spark.BusinessLogic.Vindis.Comparers;

namespace CPHI.Spark.Loader
{
    public partial class DealsVindisJob : IJob
    {

              

        public List<Diff> DiffAndUpdate(List<Deal> dealsToCompare)
        {
            
            
            //List<Stock> changed = new List<Stock>(10000);
            List<Diff> changeDiffs = new List<Diff>();

           

            var DealDeepComparer = new DealDeepComp(); //instantiate


            foreach (var deal in dealsToCompare)
            {
                try
                {
                    var oldDeal = dbDeals.First(s => s.EnquiryNumber == deal.EnquiryNumber);
                    if (!DealDeepComparer.Equals(oldDeal, deal))
                    {

                        //they are not the same so..
                        changeDiffs.AddRange(DealDeepComp.GetDiffs(oldDeal, deal, "", deal.EnquiryNumber.ToString())); //find the diffs and record

                        //update the old item to pickup the new value for every property
                        oldDeal.OrderDate = deal.OrderDate;
                        oldDeal.ActualDeliveryDate = deal.ActualDeliveryDate;
                        oldDeal.InvoiceDate = deal.InvoiceDate;
                        oldDeal.EnquiryNumber = deal.EnquiryNumber;
                        oldDeal.Site_Id = deal.Site_Id;
                        oldDeal.Customer = deal.Customer;
                        oldDeal.Salesman_Id = deal.IsDelivered ? oldDeal.Salesman_Id : deal.Salesman_Id;
                        oldDeal.Reg = deal.Reg;
                        oldDeal.Description = deal.Description;
                        oldDeal.Model = deal.Model;
                        oldDeal.VariantTxt = deal.VariantTxt;
                        oldDeal.OrderType_Id = deal.OrderType_Id;
                        oldDeal.StockNumber = deal.StockNumber;
                        oldDeal.IsFinanced = deal.IsFinanced;
                        oldDeal.Discount = deal.Discount;
                        oldDeal.ServicePlanSale = deal.ServicePlanSale;
                        oldDeal.OemDeliverySale = deal.OemDeliverySale;
                        oldDeal.WarrantySale = deal.WarrantySale;
                        oldDeal.AccessoriesSale = deal.AccessoriesSale;
                        oldDeal.NewBonus1 = deal.NewBonus1;
                        oldDeal.NewBonus2 = deal.NewBonus2;
                        oldDeal.PartExOverAllowance1 = deal.PartExOverAllowance1;
                        oldDeal.RCIFinanceCommission = deal.RCIFinanceCommission;
                        oldDeal.FinanceCommission = deal.FinanceCommission;
                        oldDeal.GapInsuranceCommission = deal.GapInsuranceCommission;
                        oldDeal.Sale = deal.Sale;
                        //oldDeal.TotalVehicleProfit = deal.TotalVehicleProfit;
                        oldDeal.PDICost = deal.PDICost;
                        oldDeal.CosmeticInsuranceCommission = deal.CosmeticInsuranceCommission;
                        oldDeal.PaintProtectionSale = deal.PaintProtectionSale;
                        oldDeal.PaintProtectionCost = deal.PaintProtectionCost;
                        oldDeal.PaintProtectionAccessorySale = deal.PaintProtectionAccessorySale;
                        oldDeal.PaintProtectionAccessoryCost = deal.PaintProtectionAccessoryCost;
                        oldDeal.HasServicePlan = deal.HasServicePlan;
                        oldDeal.HasCosmeticInsurance = deal.HasCosmeticInsurance;
                        oldDeal.HasGapInsurance = deal.HasGapInsurance;
                        oldDeal.HasWarranty = deal.HasWarranty;
                        oldDeal.HasPaintProtection = deal.HasPaintProtection;
                        oldDeal.HasPaintProtectionAccessory = deal.HasPaintProtectionAccessory;
                        oldDeal.Error = deal.Error;
                        oldDeal.CoS = deal.CoS;
                       // oldDeal.TotalProductCount = deal.TotalProductCount;
                        oldDeal.IsDelivered = deal.IsDelivered;
                        oldDeal.IsRemoved = deal.IsRemoved;
                        oldDeal.RemovedDate = deal.RemovedDate;
                        oldDeal.LastUpdated = deal.LastUpdated;
                        oldDeal.Units = deal.Units;
                        oldDeal.AccountingDate = deal.AccountingDate;
                        oldDeal.CreatedDate = deal.CreatedDate;
                        oldDeal.Franchise_Id = deal.Franchise_Id;
                        oldDeal.TotalNLProfit = deal.TotalNLProfit;
                        //oldDeal.FAndIProfit = deal.FAndIProfit;
                        oldDeal.WarrantyCost = deal.WarrantyCost;
                        oldDeal.StandardWarrantyCost = deal.StandardWarrantyCost;
                        oldDeal.BodyPrep = deal.BodyPrep;
                        oldDeal.MechPrep = deal.MechPrep;
                        oldDeal.VehicleType_Id = deal.VehicleType_Id;
                        oldDeal.Other = deal.Other;
                        oldDeal.FinanceCo = deal.FinanceCo;
                        oldDeal.HandoverDate = deal.HandoverDate;
                        oldDeal.VatCost = deal.VatCost;
                        oldDeal.LastPhysicalLocation = deal.LastPhysicalLocation;

                        oldDeal.HasTyreInsurance = deal.HasTyreInsurance;
                        oldDeal.HasAlloyInsurance = deal.HasAlloyInsurance;
                        oldDeal.HasTyreAndAlloyInsurance = deal.HasTyreAndAlloyInsurance;
                        oldDeal.HasWheelGuard = deal.HasWheelGuard;

                        oldDeal.TyreInsuranceSale = deal.TyreInsuranceSale;
                        oldDeal.TyreInsuranceCost = deal.TyreInsuranceCost;
                        oldDeal.TyreInsuranceCommission = deal.TyreInsuranceCommission;
                        oldDeal.AlloyInsuranceSale = deal.AlloyInsuranceSale;
                        oldDeal.AlloyInsuranceCost = deal.AlloyInsuranceCost;
                        oldDeal.AlloyInsuranceCommission = deal.AlloyInsuranceCommission;
                        oldDeal.TyreAndAlloyInsuranceSale = deal.TyreAndAlloyInsuranceSale;
                        oldDeal.TyreAndAlloyInsuranceCost = deal.TyreAndAlloyInsuranceCost;
                        oldDeal.TyreAndAlloyInsuranceCommission = deal.TyreAndAlloyInsuranceCommission;
                        oldDeal.WheelGuardSale = deal.WheelGuardSale;
                        oldDeal.WheelGuardCost = deal.WheelGuardCost;
                        oldDeal.WheelGuardCommission = deal.WheelGuardCommission;
                       // oldDeal.ServicePlanCommission = deal.ServicePlanCommission;
                        oldDeal.OemReference = deal.OemReference;
                        oldDeal.StockDate = deal.StockDate;
                        oldDeal.VehicleAge = deal.VehicleAge;
                        oldDeal.IsUpdated = true;
                        oldDeal.LastUpdated = DateTime.UtcNow; //update the lastUpdated date
                        oldDeal.FinanceType = deal.FinanceType;
                        oldDeal.IsClosed = deal.IsClosed;
                        oldDeal.FinanceSubsidy = deal.FinanceSubsidy;

                        changedCount++;
                    }

                }

                catch (Exception err)
                {
                    logMessage.FailNotes = logMessage.FailNotes + $" failed on making change to item {deal.EnquiryNumber}" + err.ToString();
                    errorCount++;
                }
            }




            return changeDiffs;







        }
    }
}
