import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { ConstantsService } from '../services/constants.service';

export interface SimplePickerItem {
    isSelected?: boolean;
    label: string;
}

@Component({
  selector: 'pickerSimple',
  template: `
    <div class="d-inline-block" ngbDropdown dropright>
        <button class="btn btn-primary centreButton" ngbDropdownToggle (click)="getSelectedPickerItems()">
            {{ getButtonLabel() }}
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button
                *ngFor="let item of pickerItems"
                [ngClass]="{ 'active': item.isSelected }"
                ngbDropdownItem
                (click)="selectItem(item)"
            >
                {{ item.label }}
            </button>
            <button class="quickSelect" ngbDropdownItem (click)="selectAllItems()">
                {{ constants.translatedText.All }}
            </button>
            <div class="spaceBetween">
                <button
                    class="dropdownBottomButton"
                    ngbDropdownItem
                    ngbDropdownToggle
                    (click)="confirmSelection()"
                >
                    {{ constants.translatedText.OKUpper }}
                </button>
                <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>
                    {{ constants.translatedText.Cancel }}
                </button>
            </div>
        </div>
    </div>
  `
})

export class PickerSimpleComponent implements OnInit {
    @Input() pickerItemsFromParent: SimplePickerItem[];
    @Input() pickerLabel: string;
    @Output() selectedPickerItems = new EventEmitter<SimplePickerItem[]>();

    pickerItems: SimplePickerItem[];
  
    constructor(
        public constants: ConstantsService
    ) { }

    ngOnInit(): void {
        this.pickerItems = this.constants.clone(this.pickerItemsFromParent);
    }

    getSelectedPickerItems() {
        this.pickerItems.forEach(v => {
            v.isSelected = this.pickerItemsFromParent.find(x => x.label == v.label).isSelected ? true : false;
        })
    }

    selectItem(item: SimplePickerItem) {
        item.isSelected = !item.isSelected;
    }

    selectAllItems() {
        if (this.pickerItems.filter(v => v.isSelected).length == this.pickerItems.length) {
            return this.pickerItems.forEach(v => v.isSelected = false);
        }
        this.pickerItems.forEach(v => v.isSelected = true);
    }

    confirmSelection() {
        this.selectedPickerItems.emit(this.pickerItems.filter(x => x.isSelected));
    }

    getButtonLabel() {
        if (this.pickerItems.filter(x => x.isSelected).length == 1) {
            return this.pickerItems.find(x => x.isSelected).label;
        }
        if (this.pickerItems.filter(x => x.isSelected).length > 1) {
            return this.pickerLabel[0].toUpperCase() + this.pickerLabel.slice(1);
        }
        return `${this.constants.translatedText.No} ${this.pickerLabel} ${this.constants.translatedText.Selected}`;
    }
}
