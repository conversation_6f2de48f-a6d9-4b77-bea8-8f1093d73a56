﻿using Microsoft.AspNetCore.Mvc;
using System;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using CPHI.Spark.WebApp.Service;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.Services;
using CPHI.Spark.WebApp.Service.AutoPrice;
using CPHI.Spark.WebApp.Service.Autoprice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model;
using CPHI.Spark.BusinessLogic.AutoPrice;
using System.Net.Http;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model.ViewModels.AutoPricing.Taxonomy;
using System.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;

namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AutoPriceTaxonomyController : ControllerBase
    {
        private readonly TaxonomyService taxonomyService;
        private readonly IUserService userService;

        public AutoPriceTaxonomyController(IUserService userServiceIn     )
        {
           HttpClient httpClient = new HttpClient();
            string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
            string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
            string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
            taxonomyService = new TaxonomyService(httpClient, apiKey,apiSecret, baseURL);
            userService = userServiceIn;
        }





        [HttpGet]
        [Route("GetVehicleTypes")] 
        public async Task<List<string>> GetVehicleTypes()
        {
            int advertiserId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");
            return await taxonomyService.GetVehicleTypes( advertiserId);
        }

        [HttpGet]
        [Route("GetAllFacets")]
        public async Task<List<TaxonomyFacetAndChoices>> GetAllFacets(string generationId)
        {
            int advertiserId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");

            // Create a list of tasks to run in parallel
            var tasks = Enum.GetValues(typeof(TaxonomyFacet))
                   .Cast<TaxonomyFacet>()
                   .Select(async facet => new TaxonomyFacetAndChoices
                   {
                       Facet = facet,
                       Choices = await taxonomyService.GetFacetOptions(facet, advertiserId, generationId)
                   }).ToList();


            // Await all tasks to complete
            var results = await Task.WhenAll(tasks);

            return results.ToList();
        }


        [HttpGet]
        [Route("GetMakes")]
        public async Task<List<AtMakeItem>> GetMakes(string vehicleType)
        {
            int advertiserId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");
            return await taxonomyService.GetMakes(advertiserId, vehicleType);
        }
        [HttpGet]
        [Route("GetModels")]
        public async Task<List<AtModelItem>> GetModels(string makeId)
        {
            int advertiserId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");
            return await taxonomyService.GetModels(advertiserId, makeId);
        }
        [HttpGet]
        [Route("GetGenerations")]
        public async Task<List<AtGenerationItem>> GetGenerations(string modelId)
        {
            int advertiserId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");
            return await taxonomyService.GetGenerations(advertiserId, modelId);
        }




        [HttpPost]
        [Route("GetDerivatives")]
        public async Task<List<AtDerivativeItem>> GetDerivatives(GetDerivativesParams parms)
        {
            int advertiserId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");
            return await taxonomyService.GetDerivatives(advertiserId, parms);
        }




    }
}
