<table>
  <tbody>

  <ng-container *ngFor="let row of params.buildUpData">
    <!-- Extra row at top if we have a rule set comment -->
    <!-- Unified Row Template -->
    <tr>

      <!-- Matrix type row -->
      <ng-container *ngIf="row.FactorName === 'RR_DL_Matrix'">

        <!-- Factor Name -->
        <td>
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          Retail Rating: {{ params.retailRating|cph:'number':0:false }}, Days
          Listed: {{ params.daysListed|cph:'number':0:false }}

        </td>
        <td class="percentage-column">
          {{ row.FactorItemValue / 100 |cph:'percent':1 }}
        </td>


        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>


      <ng-container *ngIf="row.FactorName === 'RR_DS_Matrix'">

        <!-- Factor Name -->
        <td>
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          Retail Rating: {{ params.retailRating|cph:'number':0:false }}, Days
          In Stock: {{ params.daysInStock|cph:'number':0:false }}:
        </td>
        <td class="percentage-column">{{ row.FactorItemValue / 100 |cph:'percent':1 }}</td>
        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <ng-container *ngIf="row.FactorName === 'DTS_DL_Matrix'">

        <!-- Factor Name -->
        <td>
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          Days to sell: {{ params.daysToSell|cph:'number':0:false }}, Days
          Listed: {{ params.daysListed|cph:'number':0:false }}:
        </td>
        <td class="percentage-column">
          {{ row.FactorItemValue / 100 |cph:'percent':1 }}
        </td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <ng-container *ngIf="row.FactorName === 'SpecificColour'">

        <!-- Factor Name -->
        <td>
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          Manufacturer Colour: {{ params.specificColour }}
        </td>
        <td class="percentage-column">{{ row.FactorItemValue / 100 |cph:'percent':1 }}</td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <ng-container *ngIf="row.FactorName === 'AgeAndOwners'">

        <!-- Factor Name -->
        <td>
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          Age and owners: {{ ageAndOwnersString }}
        </td>
        <td class="percentage-column">{{ row.FactorItemValue / 100 |cph:'percent':1 }}</td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>


      <!-- Days to sell type row -->
      <ng-container *ngIf="row.FactorName === 'DaysToSell'">

        <!-- Factor Name -->
        <td colspan="2">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ measuredValue(row) }}
        </td>

        <!-- Impact -->
        <td class="impact-column">
          <span [ngClass]="{'text-danger': row.Impact < 0}">{{ row.Impact | cph:'currency':0:true }}</span>
        </td>

      </ng-container>

      <!-- Minimum profit type row -->
      <ng-container *ngIf="row.FactorName === 'MinimumProfit'">

        <!-- Factor Name -->
        <td colspan="2">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ measuredValue(row) }}
        </td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <!-- Round to nearest row -->
      <ng-container *ngIf="row.FactorName === 'RoundToNearest'">

        <!-- Factor Name -->
        <td colspan="2">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ measuredValue(row) }}
        </td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <!-- Round to price break row -->
      <ng-container *ngIf="row.FactorName === 'RoundToPriceBreak'">

        <!-- Factor Name -->
        <td colspan="2">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ measuredValue(row) }}
        </td>

        <!-- Impact -->
        <td class="textRight">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <!-- Limit to competitor type row -->
      <ng-container
        *ngIf="['MatchCheapestCompetitor', 'AchieveMarketPositionScore'].includes(row.FactorName) ">

        <!-- Factor Name -->
        <td class="factorNameCol">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ row.FactorName }}
        </td>

        <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
        <td class="text-center">{{ measuredValue(row) }}</td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <!-- OnBrand row -->
      <ng-container *ngIf="row.FactorName === 'OnBrandCheck'">

        <!-- Factor Name -->
        <td class="factorNameCol">

          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>

          {{ row.FactorName }}
          ({{ params.make }}):
          {{ measuredValue(row) }}
        </td>


        <!-- Factor Item Value (for non-'Match Cheapest Competitor' rows) -->
        <td class="percentage-column">{{ row.FactorItemValue / 100 | cph:'percent':1 }}</td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>


      <!-- RetailerName row -->
      <ng-container *ngIf="row.FactorName === 'RetailerName'">

        <!-- Factor Name -->
        <td class="factorNameCol">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ row.FactorName }}:
          {{ measuredValue(row) }}
        </td>


        <!-- Factor Item Value (for non-'Match Cheapest Competitor' rows) -->
        <td class="percentage-column">{{ row.FactorItemValue / 100 | cph:'percent':1 }}</td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>


     


      <!-- Brand row -->
      <ng-container *ngIf="['Brand','MakeFuelType','MakeAgeBand','ModelName'].includes(row.FactorName) ">

        <!-- Factor Name -->
        <td class="factorNameCol">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ row.FactorName }}:
          {{ measuredValue(row) }}
        </td>


        <!-- Factor Item Value (for non-'Match Cheapest Competitor' rows) -->
        <td class="percentage-column">{{ row.FactorItemValue / 100 | cph:'percent':1 }}</td>

        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <!-- Retail Rating or Days Listed Type row -->
      <ng-container
        *ngIf="['RetailRatingBand', 'RetailRating10sBand', 'DaysListedBand', 'DaysInStockBand'].includes(row.FactorName) ">

        <!-- Factor Name -->
        <td class="factorNameCol">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ row.FactorName }}

          <!-- Additional Information for Specific Rows -->
          <span *ngIf="row.FactorName === 'RetailRating10sBand'">
                            ({{ params.retailRating | cph:'number':0:false }})
                        </span>
          <span *ngIf="row.FactorName === 'RetailRatingBand'">
                            ({{ params.retailRating | cph:'number':0:false }})
                        </span>
          <span *ngIf="row.FactorName === 'DaysListedBand'">
                            ({{ params.daysListed | cph:'number':0:false }})
                        </span>
          <span *ngIf="row.FactorName === 'DaysInStockBand'">
                            ({{ params.daysInStock | cph:'number':0:false }})
                        </span>
        </td>

        <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
        <td class="percentage-column"> {{ row.FactorItemLabel }}: {{ row.FactorItemValue / 100 | cph:'percent':1 }}
        </td>


        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

      <!--  Days Listed Type row -->
      <ng-container *ngIf="['DaysListed', 'DaysInStock'].includes(row.FactorName) ">

        <!-- Factor Name -->
        <td class="factorNameCol">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          {{ row.FactorName }}
          <span *ngIf="row.FactorName==='DaysListed'">({{
              params.daysListed | cph:'number':0:false
            }})</span>
          <span *ngIf="row.FactorName==='DaysInStock'">({{
              params.daysInStock | cph:'number':0:false
            }})</span>
          {{ row.FactorItemLabel }}:
        </td>

        <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
        <td class="percentage-column">
          {{ row.FactorItemValue / 100 | cph:'percent':1 }}
        </td>


        <!-- Impact -->
        <td class="impact-column"><span [ngClass]="{'text-danger': row.Impact < 0}">{{
            row.Impact | cph:'currency':0:true
          }}</span>
        </td>

      </ng-container>


      <!--  Days Listed Type row -->
      <ng-container *ngIf="row.FactorName == 'WholesaleAdjustment'">

        <!-- Factor Name -->
        <td class="factorNameCol">
          <ng-container *ngTemplateOutlet="strategyPopover; context: { $implicit: row }"></ng-container>
          Wholesale adjustment
          {{ row.FactorItemLabel }}
        </td>

        <!-- Factor Item Label or Measured Value for 'Match Cheapest Competitor' -->
        <td class="percentage-column">
          {{ row.FactorItemValue / 100 | cph:'percent':1 }}
        </td>


        <!-- Impact -->
        <td class="impact-column">
                        <span [ngClass]="{'text-danger': row.Impact < 0}">{{
                            row.Impact | cph:'currency':0:true
                          }}</span>
        </td>

      </ng-container>

    </tr>

  </ng-container>
  </tbody>
</table>

<ng-template #strategyPopover let-row>
  <div style="display: inline-block" placement="auto"
       popoverClass="infoPopover"
       triggers="mouseenter:mouseleave"
       container="body"
       (shown)="hoverInfo = row"
       [ngbPopover]="dynamicPopoverContent">
    <i class="fa fa-circle-info strategy-info"></i>
  </div>
</ng-template>


<ng-template #dynamicPopoverContent>
  <table>
    <tr>
      <td>Factor:</td>
      <td>{{ hoverInfo.FactorName }}</td>
    </tr>
    <tr>
      <td>Label:</td>
      <td>{{ hoverInfo.FactorItemLabel }}</td>
    </tr>
    <tr>
      <td>Value:</td>
      <td>{{ (hoverInfo.FactorItemValue / 100) | percent }}</td>
    </tr>
    <tr>
      <td>Impact:</td>
      <td>{{ hoverInfo.Impact | cph:'currency':0:true }}</td>
    </tr>
    <tr>
      <td>Version Name:</td>
      <td>{{ hoverInfo.VersionName }}
    </tr>
  </table>
</ng-template>
