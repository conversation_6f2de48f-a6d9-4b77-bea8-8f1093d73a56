import {Injectable} from '@angular/core';
import {UntypedFormControl} from '@angular/forms';
import {ColumnApi, ColumnState, GridApi} from 'ag-grid-community';
import {AutoPriceTableState} from 'src/app/model/AutoPriceTableState';
import {AutoPriceTableStateParams} from 'src/app/model/AutoPriceTableStateParams';
import {ImportMask, VehicleToImport} from "src/app/model/ImportMask";
import {TableState, ValuationBatchResult} from 'src/app/model/ValuationBatchResult';
import {VehicleValutionBatch} from 'src/app/model/VehicleValuationBatch';
import {AGGridMethodsService} from 'src/app/services/agGridMethods.service';
import {ApiAccessService} from 'src/app/services/apiAccess.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {BulkUploadDestinationTableComponent} from './bulkUploadDestinationTable/destinationTable.component';
import {BulkUploadExcelSourceTableComponent} from './excelSourceTable/excelSourceTable.component';
import {BatchResultsTableComponent} from './batchResultsTable/batchResultsTable.component';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {TableLayoutManagementParams} from 'src/app/model/TableLayoutManagementParams';
import {BulkUploadPredefinedTemplate} from './BulkUploadPredefinedTemplate';

@Injectable({
   providedIn: 'root'
})
export class BulkUploadService {

   chosenImportMask: ImportMask;

   //source table
   sourceTableComponent: BulkUploadExcelSourceTableComponent;
   sourceDataArray: string[][];
   sourceTableRowData: any[];
   sourceTablePinnedTopRows: any[];
   sourceTableAllRows: any[];
   excelColumnLetters: string[];//need these when building sourceTableRowData


   // destination table
   destinationTableComponent: BulkUploadDestinationTableComponent;
   destinationTableRowData: VehicleToImport[];


   // for main results table
   batchResultsTableComponent: BatchResultsTableComponent;
   batchResultsRowData: ValuationBatchResult[];  // these are the full vehicle details for a particular batch
   vehicleValuationBatches: VehicleValutionBatch[];  // this is just a list of the batches to choose from
   vehicleValuationSingles: VehicleValutionBatch[];  // this is just a list of the single valuations to choose from

   chosenVehicleValuationBatch: VehicleValutionBatch;


   searchTerm: UntypedFormControl = new UntypedFormControl();

   // to do with table state
   tableStateLabels: string[];
   chosenTableState: TableState

   limitToBest: boolean = true;
   tableLayoutManagement: TableLayoutManagementParams;
   chosenPredefinedCompany: BulkUploadPredefinedTemplate;

   constructor(
      private apiAccessService: ApiAccessService,
      public constantsService: ConstantsService,
      public agGridMethodsService: AGGridMethodsService,
      public constants: ConstantsService,
      public selections: SelectionsService,
      public modalService: NgbModal
   ) {
   }


   parseImportedReport(sourceDataArray: string[][], chosenImportMask: ImportMask, toastRef?: any): VehicleToImport[] {

      try {
         let vehiclesToImport: VehicleToImport[] = [];
         sourceDataArray.forEach((line, index) => {
            let reg = this.provideData(line, chosenImportMask.regColumnIndexes);
            if (reg) {
               reg = reg.replace(/ /g, ''); //first slash is beginning of term to look for, up until next slash, then global
            }

            let vehicleToImport: VehicleToImport = {
               vehicleReg: reg,
               mileage: this.provideData(line, chosenImportMask.mileageColumnIndexes),
               condition: this.provideData(line, chosenImportMask.conditionColumnIndexes),
               siv: this.maybeSanitiseNumericValue(this.provideData(line, chosenImportMask.sIVColumnIndexes)),
               reference1: this.provideData(line, chosenImportMask.reference1ColumnIndexes),
               reference2: this.provideData(line, chosenImportMask.reference2ColumnIndexes),
               reference3: this.provideData(line, chosenImportMask.reference3ColumnIndexes),
               isVatQualifying: this.provideData(line, chosenImportMask.isVatQualifyingColumnIndexes),
               currentPrice: this.maybeSanitiseNumericValue(this.provideData(line, chosenImportMask.currentPriceColumnIndexes)),
               capClean: this.maybeSanitiseNumericValue(this.provideData(line, chosenImportMask.cAPCleanColumnIndexes)),
            };

            vehiclesToImport.push(vehicleToImport);
         })

         if (toastRef) {
         }
         this.selections.triggerSpinner.emit({show: false});
         return vehiclesToImport;

      } catch (e) {
         this.constants.toastDanger('Failed to import, did you use the right mask?');
         this.selections.triggerSpinner.emit({show: false});
      }
   }

   maybeSanitiseNumericValue(value: string): string {
      if (!value) return value;

      // Try converting from string to int
      let valueAsInt: number = parseInt(value);
      if (isNaN(valueAsInt)) {
         return "0";
      } else {
         return value;
      }
   }

   provideData(line: string[], columnIndexes: number[]): string {
      // Check for undefined or empty columnIndexes
      if (!columnIndexes || columnIndexes.length === 0 || columnIndexes[0] === -1) {
         return '';
      }

      // Map the indexes to their corresponding values from the line
      // Adjust indexes to be zero-based and handle null or undefined values
      const values = columnIndexes.map(index => {
         const value = line[index - 1];
         return value === undefined || value === null ? '' : value;
      });

      // Join the values with ' | ' separator
      return values.join(' | ');
   }


   redrawSourceAndDestinationTables() {
      this.generatePinnedAndMainRowsForSourceTable(this.sourceTableAllRows)
      this.redrawSourceTable();

      setTimeout(() => {
         this.provideDestinationTableRowsAndRedraw()
      }, 100)
   }


   redrawSourceTable() {
      if (this.sourceTableComponent) {
         this.sourceTableComponent.updateGrid()
      }
   }


   generatePinnedAndMainRowsForSourceTable(sourceData: any[]) {
      let headerRowSkip = 1;
      if (this.chosenImportMask) {
         headerRowSkip = this.chosenImportMask.TopRowsToSkip
      }
      this.sourceTablePinnedTopRows = sourceData.filter((x, i) => i < headerRowSkip)
      this.sourceTableRowData = sourceData.filter((x, i) => i >= headerRowSkip);
   }

   lettersToIndexes(letters: string): number[] {
      const result: number[] = []
      letters.split(',').forEach(letter => {
         result.push(this.convertLetterToNumber(letter))
      })
      return result;
   }


   convertLetterToNumber(str) {
      let result = 0;
      for (let i = 0; i < str.length; i++) {
         result *= 26;
         result += (str[i].toUpperCase().charCodeAt(0) - 'A'.charCodeAt(0) + 1);
      }
      return result;
   }

   convertNumberToLetter(num) {
      let result = '';
      while (num > 0) {
         let remainder = (num - 1) % 26;
         result = String.fromCharCode(65 + remainder) + result;
         num = Math.floor((num - 1) / 26);
      }
      return result;
   }


   updateColumnMapping(field: string, letters: string) {
      let lettersParsed: string[] = letters.split(',');
      let numbers = this.columnLettersToNumbers(lettersParsed);
      let fieldNumbers = this.firstCharToLower(field.replace('Columns', 'ColumnIndexes'));
      if (letters !== '') {

         this.chosenImportMask[field] = lettersParsed;
         this.chosenImportMask[fieldNumbers] = numbers;
      } else {
         this.chosenImportMask[field] = [];
         this.chosenImportMask[fieldNumbers] = [];
      }

   }

   columnLettersToNumbers(letters: string[]): number[] {
      let numbers: number[] = [];

      if (letters) {

         letters.forEach(letter => {
            numbers.push(this.columnLetterStringToNumber(letter));
         })
      }

      return numbers;
   }


   columnLetterStringToNumber(letters: string): number {
      function fromLetters(str) {
         "use strict";
         var out = 0, len = str.length, pos = len;
         while (--pos > -1) {
            out += (str.charCodeAt(pos) - 64) * Math.pow(26, len - 1 - pos);
         }
         return out;
      }

      return fromLetters(letters);
   }


   firstCharToLower(str) {
      if (!str || str.length === 0) {
         return str;
      }
      return str.charAt(0).toLowerCase() + str.slice(1);
   }


   provideDestinationTableRowsAndRedraw() {
      let rowData = [];
      if (this.sourceTableComponent) {
         this.sourceTableComponent.gridApi.forEachNodeAfterFilter(node => {
            rowData.push(node.data);
         });
         let rowStrings: string[][] = []
         rowData.forEach(row => {
            let rowString = [];
            this.excelColumnLetters.forEach(letter => {
               rowString.push(row[letter])
            })
            rowStrings.push(rowString);
         })
         this.destinationTableRowData = this.parseImportedReport(rowStrings, this.chosenImportMask)
      }
      if (this.destinationTableComponent) {
         this.destinationTableComponent.updateGrid()
      }
   }


   loadTableState(stateLabel: string) {
      //this.chosenTableStateLabel = stateLabel;
      const params = [
         {key: 'label', value: stateLabel},
         {key: 'pageName', value: 'valuations'}
      ]

      this.apiAccessService.get('api/AutoPrice', 'GetTableState', params).subscribe((res: AutoPriceTableStateParams) => {
         this.chosenTableState = {
            columnState: JSON.parse(res.State as string) as ColumnState[],
            filterModel: JSON.parse(res.FilterModel),
            label: stateLabel
         }

         this.batchResultsTableComponent.updateState(this.chosenTableState);

      })
   }


   saveExistingTableState(params: AutoPriceTableStateParams) {
      this.apiAccessService.post('api/AutoPrice', 'SaveTableState', params).subscribe((res: AutoPriceTableState) => {
         this.constantsService.toastSuccess('Saved column layout');
      })
   }

   saveNewTableState(params: AutoPriceTableStateParams, chosenLabel: string) {
      this.apiAccessService.post('api/AutoPrice', 'SaveTableState', params).subscribe((result: AutoPriceTableState) => {
         this.constantsService.toastSuccess('Saved column layout');
         this.chosenTableState.label = chosenLabel;
         this.tableStateLabels.push(chosenLabel);
      })
   }

   // deleteTableState() {
   //   this.apiAccessService.deleteColumnState('api/AutoPrice', 'DeleteTableState', this.chosenTableState.label, 'valuations').subscribe((res) => {
   //     this.constantsService.toastSuccess('Deleted column layout')
   //     this.tableStateLabels = this.tableStateLabels.filter(x => x !== this.chosenTableState.label);
   //     this.resetTableState();
   //   })
   // }

   resetTableState() {
      this.chosenTableState = null;
      this.batchResultsTableComponent.resetState();

   }

   toggleLimitToBest() {
      this.limitToBest = !this.limitToBest;
      this.updateGrid(this.batchResultsRowData.filter(x => !this.limitToBest || x.isBestMove));
   }

   updateGrid(newRows: ValuationBatchResult[]) {
      this.tableLayoutManagement.gridApi.setRowData(newRows);
      this.tableLayoutManagement.gridApi.redrawRows();
   }
}
