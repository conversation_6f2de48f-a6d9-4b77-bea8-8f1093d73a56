
import { DatePipe, DecimalPipe } from '@angular/common';
import { ElementRef, EventEmitter, Injectable } from '@angular/core';
//import lodash from 'lodash';
//import clonedeep from 'lodash'  ;
import cloneDeep from 'lodash/cloneDeep';
import startCase from 'lodash/startCase';
import moment from 'moment';
//import * as moment from 'moment';
import { Subject, Subscription } from 'rxjs';
import * as XLSX from 'xlsx';
import { AutoPriceSettingsModalComponent } from '../components/autopriceSettingsModal.component';
import { ConfirmModalComponent } from '../components/confirmModal.component';
import { MultiSelectMonth } from '../components/datePickerMultiSelect/datePickerMultiSelect.component';
import { FileSentDate } from '../components/dealDetails/dealDetailsFileSentDates.component';
import { InputModalComponent } from '../components/inputModal.component';
import { AppUserRole } from '../model/AppUserRole';
import { RetailerSiteVM } from "../model/RetailerSiteVM";
import { BroadcastMessage } from '../model/BroadcastMessage';
import { ClaimTypeAndValues } from '../model/ClaimTypeAndValues';
import { FleetOrderbookField } from '../model/FleetOrderbookField';
import {
  Comparative, DashboardPage, DateSelectionObject, Day, Department,
  LastUpdatedDates, MenuItem, MenuItemNew, MenuSection, Month,
  SiteVM,
  UseLogItem, Week
} from '../model/main.model';
import { LateCostOption, OrderOption, OrderType, TodayNewUsedOrder, VehicleType } from '../model/sales.model';
import { TranslatedText } from '../model/translations.model';
import { SparkEnvironment } from './environment.service';
import * as utilities from "./utilityFunctions";
import { Router } from '@angular/router';
import { AutoPrice } from '../model/AutoPriceEnv';
import { RetailerSite } from '../model/RetailerSite';

@Injectable({
  providedIn: 'root'
})
export class ConstantsService {
  public gridRedraw$ = new Subject<void>();
  broadcastMessages: BroadcastMessage[];
  //menu: MenuItem[][];
  menuSections: MenuSection[];
  amLoggedIn: boolean;
  backEndBaseURL: string
  newFileSentDateEmitter: EventEmitter<FileSentDate> = new EventEmitter()
  LastUpdatedDates: LastUpdatedDates = {
    Deals: null,
    Stocks: null,
    FinancialLines: null,
    FleetOrderbook: null,
    Registrations: null,
    Debtors: null,
    CitNow: null,
    Voc: null,
    Bookings: null,
    PartsStock: null,
    EVHC: null,
    Wip: null,
    DistrinetBookMadrid: null,
    DistrinetBookValencia: null,
    DistrinetStockMadrid: null,
    DistrinetStockValencia: null,
    DistrinetOrdersMadrid: null,
    DistrinetOrdersValencia: null,

    BCAStock: null,
    EbbonOrders: null,
    NissanData: null,
    AOLReport: null,
    InventoryData: null,

    AutotraderAdverts: null
  }

  useLogBuffer: Array<UseLogItem> = [];

  alertModal: {
    elementRef: ElementRef;
    title: string;
    message: string;
  }

  userRetailerSites: RetailerSiteVM[];
  Roles: AppUserRole[]
  ClaimTypes: ClaimTypeAndValues[]
  confirmModal: ConfirmModalComponent;
  inputModal: InputModalComponent;
  autoPriceSettingsModal: AutoPriceSettingsModalComponent;

  stockMonths: Array<{ name: string; startDate?: Date }>
  appStartTime: Date;
  todayStart: Date;
  todayEnd: Date;
  thisMonthStart: Date;
  thisMonthEnd: Date;
  thisYearStart: Date;
  yesterdayStart: Date;
  tomorrowStart: Date;
  lastWeekStartDate: Date;
  thisWeekStartDate: Date;
  thisWeekEndDate: Date;
  nextWeekStartDate: Date;
  firstOrderDate: Date;
  lateCostOptions: Array<LateCostOption>;
  orderOptions: Array<OrderOption>;
  departments: Department[]
  comparatives: Comparative[];
  toastMessage: string;
  toastIcon: string;
  isToastSuccess: boolean;
  showToast: boolean;
  sitesActive: SiteVM[];
  sitesActiveSales: SiteVM[];
  Sites: SiteVM[];
  RetailerSites: RetailerSite[];
  VehicleTypes: Array<VehicleType>;
  OrderTypes: Array<OrderType>;
  //GlobalParams: Array<GlobalParam>;
  DashboardPages: Array<DashboardPage>;
  allGroups: string[] = ['R', 'O', 'N', 'Z', 'T', 'D']
  allFamilyCodes: string[] = ['AxsBonus', 'BodyGrowth', 'BodyHandling', 'Captive', 'Motrio', 'OeCompMech', 'OeCompOther', 'Oil', 'Other']
  orderTypeTypes: string[];
  vehicleTypeTypes: string[];
  FranchiseCodes: string[];
  backgroundColours: Array<string>;


  autotraderPrimary: string = '#222B5E'
  autotraderSecondary: string = '#535A67';
  autotraderBlue: string = '#4871D9';
  autotraderBlueLight: string = '#5E82dE';
  autotraderBlueLighter: string = '#7695E2';


  borderColours: Array<string>;
  hoverColours: Array<string>;
  //vehicleModalRef: ElementRef;
  actualColour: string;
  renaultColour: string;
  nissanColour: string;
  backgroundColoursTranslucent: string[];
  borderColoursForTranslucentAreas: string[];
  orderTypeTypesNoTrade: string[];
  lastMonthStart: Date;
  nextMonthStart: Date;

  earliestOrderDate: Date;
  environment: SparkEnvironment;
  autopriceEnvironment: AutoPrice;

  translatedText: TranslatedText


  currentLang: string //= 'en'
  TodayNewUsedOrders: TodayNewUsedOrder;
  routeChangeSub: Subscription;
  uniqueIdForProfilePic: string;

  Blobname: string;
  refreshOptionsGridEmitter: EventEmitter<void> = new EventEmitter<void>();
  initialStartupComplete: boolean;
  urlsToBeSkippedToRefesh: string[] = ['/', '/login', '/noWebAccess', '/forgotpassword', '/resetpassword', '/changePassword']

  savedTheme: string | null;
  LatestSnapshotDate: Date;
  //fixSideMenu: boolean = false;

  chosenPage: MenuItemNew;
  constructor(
    private decimalPipe: DecimalPipe,
    private datePipe: DatePipe,
    private router: Router

  ) {

    this.setUniqueIdForProfilePic();
    this.translatedText = new TranslatedText();
  }


  // ------------------------------
  // Dates Stuff
  // ------------------------------



  //------------------------------------------
  //Making time periods
  makeMonths(monthsOffsetNumber: number, offset: number): Month[] {
    let months: Month[] = [];
    monthsOffsetNumber = monthsOffsetNumber + offset;
    for (let i = -12; i < 4; i++) {
      months.push(this.generateMonth(this.appStartTime, monthsOffsetNumber + i));
    }

    return months;
  }

  makeMonthsNewV3(excludeFutureMonths?: boolean): Date[] {

    let months: Date[] = []
    let monthsInFuture: number = excludeFutureMonths ? 1 : 4;

    for (let i = -12; i < monthsInFuture; i++) {
      months.push(this.addMonths(this.thisMonthStart, i))
    };

    return months;
  }

  makeDistrinetMonths() {

    let months = [];
    let currentMonth = this.deductTimezoneOffset(new Date(2022, 2, 1))

    while (currentMonth.getTime() <= this.thisMonthStart.getTime()) {
      months.push(currentMonth);
      currentMonth = this.addMonths(currentMonth, 1);
    }

    return months;
  }

  sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getCssVariableValue(variableName: string): null | number {
    // Fetches the root element (or any other element where the variable is defined)
    const root = document.documentElement;
    // Gets the value of the CSS variable
    const value = getComputedStyle(root).getPropertyValue(variableName).trim();
    return value ? parseFloat(value) : null;
  }


  makeMonthsForBusinessManagersCommissions(monthsOffsetNumber: number, offset: number): Month[] {
    // Don't show months prior to 03/2023
    let march2023: Date = new Date(2023, 2, 1);
    let now: Date = new Date();

    let startingMonth: number = 12;

    if (now.getFullYear() - march2023.getFullYear() === 0 && now.getMonth() - march2023.getMonth() <= 11) {
      startingMonth = now.getMonth() - march2023.getMonth();
    }

    let months: Month[] = [];
    monthsOffsetNumber = monthsOffsetNumber + offset;
    for (let i = -startingMonth; i < 4; i++) {
      months.push(this.generateMonth(this.appStartTime, monthsOffsetNumber + i));
    }

    return months;
  }

  makeQuarters(): Date[] {
    let quarters: Date[] = [];
    let thisQuarter = new Date(2022, 0, 1);
    while (thisQuarter.getFullYear() <= this.appStartTime.getFullYear()) {
      quarters.push(thisQuarter);
      thisQuarter = this.addQuarters(thisQuarter, 1);
    }
    return quarters;
  }



  makeDays(daysOffsetNumber: number, daysBack: number, daysForward: number, offset?: number): Day[] {
    let days = [];
    daysOffsetNumber = daysOffsetNumber + offset;
    for (let i = daysBack; i < daysForward; i++) {
      days.push(this.generateDay(this.todayStart, 1 * (daysOffsetNumber + i)));
    }

    return days
  }

  generateMonth(baseDate: Date, offset: number) {

    let month: Month = {
      name: '',
      startDate: this.deductTimezoneOffset(new Date(baseDate.getFullYear(), baseDate.getMonth() + offset, 1)),
      endDate: new Date(baseDate.getFullYear(), baseDate.getMonth() + offset + 1, 0, 23, 59),
    }
    month.name = month.startDate.toLocaleDateString(this.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
    return month;
  }

  capitalise(value: string) {
    let first = value.substring(0, 1);
    let rest = value.substring(1);
    return `${first.toUpperCase()}${rest}`
  }


  generateWeek(baseDate: Date, offset: number) {

    let newStartDay = this.addDays(baseDate, offset * 7);
    let week: Week = {
      name: '',
      startDate: newStartDay,
      endDate: this.endOfWeek(newStartDay),
    }
    week.name = 'w/c ' + this.ordinalSuffix(week.startDate.getDate()) + ' ' + week.startDate.toLocaleDateString(this.translatedText.LocaleCode, { month: 'short' });
    if (week.startDate.getTime() == this.thisWeekStartDate.getTime()) { week.name = this.translatedText.Common_ThisWeek }
    if (week.startDate.getTime() == this.lastWeekStartDate.getTime()) { week.name = this.translatedText.Common_LastWeek }
    if (week.startDate.getTime() == this.nextWeekStartDate.getTime()) { week.name = this.translatedText.Common_NextWeek }
    return week;
  }

  generateDay(baseDate: Date, offset: number) {

    let day: Day = {
      name: '',
      startDate: this.addDays(baseDate, offset),
    }
    day.name = this.datePipe.transform(day.startDate, 'd MMM');//this.cphPipe.transform(day.startDate, 'shortDate', 0)
    if (day.startDate.getTime() == this.todayStart.getTime()) { day.name = this.translatedText.Common_Today };
    if (day.startDate.getTime() == this.yesterdayStart.getTime()) { day.name = this.translatedText.Common_Yesterday };
    if (day.startDate.getTime() == this.tomorrowStart.getTime()) { day.name = this.translatedText.Common_Tomorrow };
    return day;
  }

  makeWeeksFromAnalysisService(thisMonthStart: Date, thisMonthEnd: Date): Week[] {

    //walk through each day of the month, putting days into weeks
    let weeks: Week[] = [];
    let cumMondays = 0;

    let monthEndDay = this.addTimezoneOffset(thisMonthEnd).getDate();

    for (let i = 0; i < monthEndDay; i++) {
      let dayDate = this.addDays(thisMonthStart, i);

      //at start, push in a week
      if (i == 0) {
        let daysLeftInThisWeek = 8 - dayDate.getDay();
        if (dayDate.getDay() == 0) {
          daysLeftInThisWeek = 1;
        }
        weeks.push({
          startDate: dayDate,
          endDate: new Date(Math.min(thisMonthEnd.getTime(), this.addDays(dayDate, daysLeftInThisWeek).getTime() - 1)),
          days: [],
          numberOfDays: 0,
        })
      } else {
        //else if Monday, start new week
        if (dayDate.getDay() == 1) {
          cumMondays++
          weeks.push({
            startDate: dayDate,
            endDate: new Date(Math.min(thisMonthEnd.getTime(), this.addDays(dayDate, 7).getTime() - 1)),
            days: [],
            numberOfDays: 0,
          })
        }
      }

      weeks[cumMondays].days.push({
        startDate: dayDate
      })
      weeks[cumMondays].numberOfDays++
    }

    return weeks;

  }

  makeWeeks(weeksOffsetNumber: number, offset?: number) {
    let weeks = [];
    weeksOffsetNumber = weeksOffsetNumber + offset;
    for (let i = -8; i < 4; i++) {
      weeks.push(this.generateWeek(this.thisWeekStartDate, 7 * (weeksOffsetNumber + i)));
    }

    return weeks;
  }


  makeWeeksSimple() {
    let weeks = [];
    let startWeek: Date = this.thisWeekStartDate;
    for (let i = -8; i < 4; i++) {
      weeks.push(this.addWeeks(startWeek, i));
    }

    return weeks;
  }



  //------------------------------------------
  //Selecting time periods
  selectMonth(object: DateSelectionObject, date: Date) {
    object.startDate = date;
    object.endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 0, 0),
      object.monthName = date.toLocaleDateString(this.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
  }

  selectDay(object: DateSelectionObject, date: Date) {
    object.startDate = date;
    object.endDate = this.deductTimezoneOffset(new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59));
    object.dayName = this.datePipe.transform(object.startDate, 'd MMM');// this.cphPipe.transform(object.startDate, 'shortDate', 0)
    object.lastSelectedDayStartDate = object.startDate;
    if (object.startDate.getTime() == this.todayStart.getTime()) { object.dayName = this.translatedText.Common_Today };
    if (object.startDate.getTime() == this.yesterdayStart.getTime()) { object.dayName = this.translatedText.Common_Yesterday };
    if (object.startDate.getTime() == this.tomorrowStart.getTime()) { object.dayName = this.translatedText.Common_Tomorrow };
  }


  selectWeek(object: DateSelectionObject, date: Date) {
    object.startDate = date;
    object.endDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 6, 23, 59, 59),
      object.weekName = 'w/c Mon ' + this.ordinalSuffix(date.getDate()) + ' ' + date.toLocaleDateString(this.translatedText.LocaleCode, { month: 'short' });
    object.lastSelectedWeekStartDate = object.startDate;
    if (object.startDate.getTime() == this.thisWeekStartDate.getTime()) { object.weekName = this.translatedText.Common_ThisWeek }
    if (object.startDate.getTime() == this.lastWeekStartDate.getTime()) { object.weekName = this.translatedText.Common_LastWeek }
    if (object.startDate.getTime() == this.nextWeekStartDate.getTime()) { object.weekName = this.translatedText.Common_NextWeek }
  }










  //--------------------------------------------
  //Start and end of time periods
  startOfWeek(date: Date) {
    const momentDate = moment.utc(date);
    return momentDate.startOf('isoWeek').toDate();
  }



  endOfWeek(date: Date) {
    const momentDate = moment.utc(date);
    return momentDate.endOf('isoWeek').toDate();
  }

  startOfMonth(date: Date) {
    const momentDate = moment.utc(date);
    return momentDate.startOf('month').toDate();
  }

  endOfMonth(date: Date) {
    const momentDate = moment(date);
    return momentDate.endOf('month').toDate();
  }

  startOfToday() {
    return utilities.startOfToday();
  }

  endOfDay(day: Date) {
    const momentDate = moment.utc(day);
    return momentDate.endOf('day').toDate();
  }

  dateNoTime(day: Date) {
    return moment(day).format('L');
  }

  startTimeOfDay(day: Date) {
    return utilities.startTimeOfDay(day);
  }

  mostRecentMonday = (date: Date) => {
    let dayOfWeek = date.getDay()
    let adjustByDays = dayOfWeek - 1
    if (dayOfWeek < 1) {
      adjustByDays = 6
    }
    let mostRecentMon = new Date(date.getFullYear(), date.getMonth(), date.getDate() - adjustByDays)
    return this.deductTimezoneOffset(mostRecentMon);
  }


  startOfThisWeek() {
    let today = new Date()
    var begin = moment.utc().startOf('isoWeek');

    if (today.getDay() == 1 && today.getHours() < 12) {
      begin.add(-7, 'days');
    }

    return begin.toDate();
  }







  //---------------------------------------------------
  //changing time periods

  changeMonth(object: DateSelectionObject, changeAmount: number) {
    if (!object.amSelectingMonth) {
      object.startDate = object.lastSelectedMonthStartDate
    }
    object.startDate = this.addMonths(object.startDate, changeAmount);
    object.endDate = this.endOfMonth(object.startDate);// this.addMonths(object.endDate,changeAmount);
    object.monthName = object.startDate.toLocaleDateString(this.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
    object.lastSelectedMonthStartDate = object.startDate;

  }

  changeWeek(object: DateSelectionObject, changeAmount: number) {
    if (!object.amSelectingWeek) {
      object.startDate = object.lastSelectedWeekStartDate
    }
    object.startDate = this.addDays(object.startDate, changeAmount * 7);// new Date(object.startDate.getFullYear(), object.startDate.getMonth(), object.startDate.getDate() + (changeAmount * 7));
    object.endDate = this.addDays(object.endDate, changeAmount * 7);//new Date(object.startDate.getFullYear(), object.startDate.getMonth(), object.startDate.getDate() + 6, 23, 59, 59); //startDate is correct
    object.weekName = 'w/c Mon ' + this.ordinalSuffix(object.startDate.getDate()) + ' ' + object.startDate.toLocaleDateString(this.translatedText.LocaleCode, { month: 'short' });
    object.lastSelectedWeekStartDate = object.startDate;

    if (object.startDate.getTime() == this.thisWeekStartDate.getTime()) { object.weekName = this.translatedText.Common_ThisWeek }
    if (object.startDate.getTime() == this.lastWeekStartDate.getTime()) { object.weekName = this.translatedText.Common_LastWeek }
    if (object.startDate.getTime() == this.nextWeekStartDate.getTime()) { object.weekName = this.translatedText.Common_NextWeek }
  }

  changeDay(object: DateSelectionObject, changeAmount: number) {
    if (!object.amSelectingDay) {
      object.startDate = object.lastSelectedDayStartDate
    }
    object.startDate = this.addDays(object.startDate, changeAmount);// new Date(object.startDate.getFullYear(), object.startDate.getMonth(), object.startDate.getDate() + changeAmount);
    object.endDate = this.addDays(object.endDate, changeAmount);// object.startDate;
    object.dayName = this.datePipe.transform(object.startDate, 'd MMM');// this.cphPipe.transform(object.startDate, 'shortDate', 0)
    object.lastSelectedDayStartDate = object.startDate;

    if (object.startDate.getTime() == this.todayStart.getTime()) { object.dayName = this.translatedText.Common_Today };
    if (object.startDate.getTime() == this.yesterdayStart.getTime()) { object.dayName = this.translatedText.Common_Yesterday };
    if (object.startDate.getTime() == this.tomorrowStart.getTime()) { object.dayName = this.translatedText.Common_Tomorrow };
  }

  provideWeekLabel(date: Date) {
    if (date.getTime() == this.thisWeekStartDate.getTime()) { return this.translatedText.Common_ThisWeek }
    if (date.getTime() == this.lastWeekStartDate.getTime()) { return this.translatedText.Common_LastWeek }
    if (date.getTime() == this.nextWeekStartDate.getTime()) { return this.translatedText.Common_NextWeek }
    return `${this.provideWeekPrefix()}${this.ordinalSuffix(date.getDate())} ${date.toLocaleDateString(this.translatedText.LocaleCode, { month: 'short' })}`;
  }


  provideWeekPrefix(): string {
    //switch on language chosen.  TO DO
    return 'w/c Mon ';
  }

  addYears(date: Date, yearsCount: number) {
    const momentDate = moment.utc(date);
    return momentDate.add(yearsCount, 'years').toDate();
  }

  addMonths(baseDate: Date, adjustment: number) {
    // 8 June 2022.  Finally think we've found the canonical way of changing a date... RP.
    const momentDate = moment.utc(baseDate);  //activate utc mode
    return momentDate.add(adjustment, 'months').toDate()
  }

  addQuarters(baseDate: Date, adjustment: number) {
    const momentDate = moment.utc(baseDate);
    return momentDate.add(adjustment * 3, 'months').toDate();
  }

  addWeeks(baseDate: Date, adjustment: number) {
    const momentDate = moment.utc(baseDate);
    return momentDate.add(adjustment, 'weeks').toDate();   // 8 June 2022.  Finally think we've found the canonical way of changing a date... RP.
  }

  addDays(baseDate: Date, daysToAdjust: number) {
    const momentDate = moment.utc(baseDate);
    return momentDate.add(daysToAdjust, 'days').toDate();
  }


  addMinutes(date: Date, adjustment: number) {
    const momentDate = moment.utc(date);
    return momentDate.add(adjustment, 'minutes').toDate();
  }





  //----------------------------------------
  //Date comparisons
  differenceInDays(biggerDate: Date, smallerDate: Date): number {
    if (typeof (biggerDate) == 'undefined' || typeof (!smallerDate) == 'undefined') {
      return 0
    }

    if (biggerDate == null || smallerDate == null) {
      return 0
    }


    try {
      var endDate = moment(biggerDate); //todays date
      var startDate = moment(smallerDate); // another date
      var duration = moment.duration(endDate.diff(startDate, 'days'), 'days');
      return duration.asDays();

    }
    catch {
      return 0
    }

  }


  datesAreSame(date1, date2) {
    return (
      date1.getFullYear() == date2.getFullYear() &&
      date1.getMonth() == date2.getMonth() &&
      date1.getDate() == date2.getDate()
    )
  }




  //-----------------------------------
  //Other date stuff
  daysInMonth(date) {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  }

  deductTimezoneOffset(dateGiven: Date): Date {
    return utilities.deductTimezoneOffset(dateGiven);
  }

  deductTimezoneOffsetTest(dateGiven: Date): Date {
    const momentDate = moment.utc(dateGiven);
    return momentDate.toDate()
    // let adjustmentInMinutes = dateGiven.getTimezoneOffset();
    // return new Date(dateGiven.getFullYear(), dateGiven.getMonth(), dateGiven.getDate(), dateGiven.getHours(), dateGiven.getMinutes() - adjustmentInMinutes)
  }


  addTimezoneOffset(dateGiven: Date): Date {
    return utilities.addTimezoneOffset(dateGiven);
  }







  // ------------------------------
  // Non-Dates Stuff
  // ------------------------------




  getLangs() {
    return ['en', 'es']
  }

  setUniqueIdForProfilePic() {
    this.uniqueIdForProfilePic = this.datePipe.transform(new Date(), 'hhmmss')
  }



  exportExcel(data: any[], title?: string) {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    const workbook: XLSX.WorkBook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
    if (title == null) { title = 'Deals Extract.xls'; }
    XLSX.writeFile(workbook, title, { bookType: 'xls', type: 'buffer' });
  }


  pluralise(number: number, singular: string, plural: string, returnZero?: boolean, lowerCaseNo?: boolean) {
    if (number == 0) {
      if (returnZero) { return '0 ' + plural; }
      if (lowerCaseNo) { return this.translatedText.Common_NoLower + " " + plural; }
      return this.translatedText.Common_No + " " + plural;
    } else if (number == 1) {
      return number + ' ' + singular
    } else {
      return this.decimalPipe.transform(number, '1.' + 0 + '-' + 0) + ' ' + plural
    }

  }

  clone(object: any): any {
    let clone = cloneDeep(object)
    return clone
  }




  getNestedItem = (object, pathToField) => {  //apparently this method is more robust although functionally identical to getNestedValue
    try {
      pathToField = pathToField.replace(/\[(\w+)\]/g, '.$1'); // convert indexes to properties
      pathToField = pathToField.replace(/^\./, '');           // strip a leading dot
      var a = pathToField.split('.');
      for (var i = 0, n = a.length; i < n; ++i) {
        var k = a[i];
        if (k in object) {
          object = object[k];
        } else {
          return;
        }
      }
      return object;
    }
    catch (e) { return null }
  }


  getNestedValue(obj, path) {
    return path.split('.').reduce((acc, part) => acc && acc[part], obj);
  };

  ordinalSuffix = (i) => {
    if (this.environment.noDateSuffix) return `${i} `;
    var j = i % 10,
      k = i % 100;
    if (j == 1 && k != 11) {
      return i + "st";
    }
    if (j == 2 && k != 12) {
      return i + "nd";
    }
    if (j == 3 && k != 13) {
      return i + "rd";
    }
    return i + "th";
  }

  sum = (values: Array<number>) => {
    return utilities.sum(values);
  }
  average = (values: Array<number>) => {
    if (values.length == 0) { return 0 }
    const sum = this.sum(values);
    return sum / values.length;
  }





  div(numerator: number, denominator: number) {
    return utilities.div(numerator, denominator);
  }




  getSiteIdsForRegion(region: string): number[] {
    let siteIds: number[] = [];
    this.Sites.forEach(x => {
      if ((x.RegionDescription == region) || (region == "Total")) {
        siteIds.push(x.SiteId);
      }
    })
    return siteIds
  }



  makeInitials(personName: string): string {
    let names = personName.split(' ');
    let initials = '';
    names.forEach(name => {
      initials = initials + name.substring(0, 1);
    })
    return initials
  }





  customerNameShortNoTitle(fullName: string) {

    try {

      let customerNoTitle = fullName;
      ['Mr ', 'Mrs ', 'Miss ', 'Ms '].forEach(removeTerm => {
        customerNoTitle = customerNoTitle.replace(removeTerm, '');
      })
      return customerNoTitle.substring(0, 10);
    }
    catch {
      debugger
    }

  }

  toastSuccess(message: string) {
    //this.toastService.show(message, { classname: 'bg-success text-light', delay: 7000 });
    this.toastMessage = message
    this.toastIcon = 'fas fa-check-square';
    this.isToastSuccess = true;
    this.showToast = true;

    setTimeout(() => {
      this.showToast = false;
    }, 2000)

  }



  toastDanger(message: string) {
    //this.toastService.show(message, { classname: 'bg-danger text-light', delay: 10000 });
    this.toastMessage = message
    //this.toastIcon = 'fas fa-check-square';
    this.isToastSuccess = false;
    this.showToast = true;

    setTimeout(() => {
      this.showToast = false;
    }, 2000)

  }



  binarySearch(array: number[], targetVal: number): number {
    let lowBoundary: number = 0
    let highBoundary: number = array.length - 1
    let tryPosition: number;

    while (lowBoundary <= highBoundary) {
      tryPosition = Math.floor((lowBoundary + highBoundary) / 2);    //go midway between the two boundaries
      if (array[tryPosition] === targetVal) return tryPosition;  //we found it!  so return the position
      else if (array[tryPosition] < targetVal) lowBoundary = tryPosition + 1; //this point in the array is too low for the target value so move the lowBoundary to tryPosition +1
      else highBoundary = tryPosition - 1;  //didn't find it and we weren't too low so must have been too high, so move the highBoundary to tryPosition -1
    }
    return -1; //never found a match so return -1
  }


  removeDuplicatesFromArray(array: any[]) {
    return [...new Set(array)];
  }

  splitPascalCase(sourceString: string) {
    return startCase(sourceString);
  }

  dateIsValid(date: any) {
    var isNanTest = !isNaN(date);
    return date instanceof Date && isNanTest;
  }



  public expandTransmittedStrings(stringRes: string[], colIdsAndType: FleetOrderbookField[]) {
    let res: any[] = []
    stringRes.forEach((stringItem: string) => {
      const splitOut: string[] = stringItem.split('|^|');
      let rowItem: any = {};
      colIdsAndType.forEach((colIdAndType, i: number) => {
        let val: Date | string = splitOut[i];

        if (colIdAndType.FieldIsDate) {
          if (!!val) {
            val = new Date(val);
            rowItem[colIdAndType.FieldName] = val;
          } else {
            rowItem[colIdAndType.FieldName] = null;
          }
        }
        else if (colIdAndType.FieldIsNumber) {
          if (colIdAndType.FieldIsNullable) {
            if (val === '') {
              val = null
            } else {
              rowItem[colIdAndType.FieldName] = parseFloat((val as string));
            }
          } else {
            rowItem[colIdAndType.FieldName] = parseFloat((val as string));
          }
        }
        else if (colIdAndType.FieldIsBool) {
          rowItem[colIdAndType.FieldName] = (val === 'True');
        }
        else {
          rowItem[colIdAndType.FieldName] = val;
        }
      });
      res.push(rowItem);
    });
    return res;
  }

  getSiteNameFromSiteId(siteId: number): string {

    let siteName = ""

    this.Sites.forEach(site => {

      if (site.SiteId == siteId) {
        siteName = site.SiteDescription;
      }

    });

    return siteName;
  }


  provideExcelLogo() {
    let result = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABmJLR0QA/wD/AP+gvaeTAAADpElEQVRoge2Zf2hVZRjHP++5u8ctp86mi41Q29TZflj3nsjGJl6VVoaTLf1HiszGEiESQZZpg8GIQJuKYRLZH00tS1BbzbnRRMWpaeyPaeQiC6QoRtvwKmy13fP2x8WT695zPWfnXu/E84EL932f933e5/ue9+F9z3vAxcXFJZmIRDn21ftyQrrQAA2JJkG73NCVE+9xUpw6CNQHUgZCA/kSJRwsogDwhUJk3tkuUTNlS0B+bemkVO/QE7oiCwSyEND6Q0ENPKkJiu+umAoo3lw8VfGohVKggdSkQBMMzpOghGczYavPFqYChNfbL5H/leMw2PKjlfLurSKRQna2VH5VFs2mOAvp3iCkKDWz3RcCYuEKSDaO9wE7fF151Phfcawqou7/3G4TiwfnCbxUspraZZuM8ubDW2m9fMIoZzyUwfGNzUycMBGAxradNHUeiGOo0bEs4NB3X1Llr2TOI7MBeD1QTduVdnSpA7AuUGMEf633Gp9dOBThI9qSsLJMYmF5CYX0ENta3zfKudNzWTg3vLfkZOSw6qkXAZBS8l7LNkZCI44Cs4qtJL74yyVO/niKJY8HAKhZVM3pnjO8sXQ9aooKQEv3cS79+n3U/uMiiRtP7ODvkX8AKH60iPKiZ1k2/3kAbg7dZEfbLrsuHWFbwG8Dv9PUud8o11VsQRFhN3s69tJ3qz9+0VnA9Iw2v85vevBKU9NofvMIWZOzjLqrf/Sw+qOX0XXddLAZ/hljjZNvqo5FjXVM+4BH8UTUTUmbjOpRx+LOEWPaiTeWbzBm/69bfUxLzyQ7I5vqhWvZc3Kvab9xkcTaLD8rtbDjoeEhGprfNWyvlr3CzMyZdl06wpYANUXlnYotCBFejgfPf86pq6c59/N5w/728rfiH2UMbC2hmkXV5E5/DIDgYJBPO5sA2NW+m2fyFqAIhZK8BTxXVE7blfaI/kndiWdn5bG2bI1R/vjMJ9wYDALQ8+dPfPtDh2GrfWET6anpjgKziqUnoAiFuhVb8Xq8APQGe/ni4uFRbT7o+JClBUvwKB6mpWeyfvE6trc2jmqTiCS2JECXOmv2vRazzfW+6/jrn7biLq48OO8D8SCpSTxecQUkm/tFwFkzg2kSy+HhhyMvd5mHA9FmR2In2HKYX1s6SZ0wOPf21Xr4JzTA0vV6d0NXcgVE484PHEIoBVLKQqAERn/ggHEqwMxvYb0/T9GFT6D7kOJJwNfd0JWdoPFcXFxcksS/44wNWFr5JHcAAAAASUVORK5CYII='
    return result
  }


  public formatMonthsForParams(selectedMonths: MultiSelectMonth[]): string {
    let selectedMonthsFormatted: string[] = [];

    selectedMonths.forEach(m => {
      // Formats month as "YYYYMM"
      selectedMonthsFormatted.push(`${m.startDate.getFullYear()}${m.startDate.getMonth() + 1}`);
    });

    return selectedMonthsFormatted.toString();
  }


  provideGenericImage() {
    return 'data:image/jpeg;base64,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'
  }

  getBrandImage(brand: string) {
    switch (brand) {
      case 'Alpine':
        return 'data:image/jpeg;base64,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';
      case 'Dacia':
        return 'data:image/jpeg;base64,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'
      case 'Renault':
        return 'data:image/jpeg;base64,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';

    }
  }

  getGridClass() {
    const savedTheme: string | null = this.savedTheme;
    return savedTheme && savedTheme === 'dark' ? 'ag-theme-balham-dark' : 'ag-theme-balham';
  }

  createHashMap(items: any[], field: string) {
    return utilities.createHashMap(items, field)
  }

  buildPortalUrl(identifier: string) {
    return `https://portal.autotrader.co.uk/portal/#/edit/${identifier}/vehicle-insight`;
  }

  buildAdUrl(identifier: string, vehicleType: string) {
    if (!vehicleType) { return null }
    return `https://www.autotrader.co.uk/${vehicleType.toLowerCase()}-details/${identifier}`;
  }

  getCSSVariable(property: string): string {
    return getComputedStyle(document.body).getPropertyValue(property);
  }

  getCSSVariableArray(property: string): string[] {
    let propertyString: string = getComputedStyle(document.body).getPropertyValue(property);
    if (propertyString.includes('rgba')) {
      return propertyString.split('),').map(item => item.trim() + ')');
    } else {
      return propertyString.split(',');
    }
  }

  getMenuItemFromUrl(url: string): MenuItemNew | undefined {
    const foundItem: MenuItemNew | undefined = this.quickFindMenuItem(url,'link');
    return foundItem;
  }

  // getMenuItemFromPageNameTranslation(menuItemName: string): MenuItemNew | undefined {
  //   const foundItem: MenuItemNew | undefined = this.quickFindMenuItem(menuItemName,'pageName');
  //   return foundItem;
  // }

  public quickFindMenuItem(menuItemName: string, field:'pageName'|'link') {
    let foundItem: MenuItemNew | undefined = undefined;
    let menuItemIndex: number = 0;
    while (!foundItem && menuItemIndex < this.menuSections.length) {
      const currentSection = this.menuSections[menuItemIndex];
      let sectionIndex = 0;
      while (!foundItem && sectionIndex < currentSection.subItems.length) {
        const currentSubItem = currentSection.subItems[sectionIndex];
        if (currentSubItem[field] === menuItemName) {
          foundItem = currentSubItem;
        }
        sectionIndex++;
      }
      menuItemIndex++;
    }
    return foundItem;
  }

  navigateByUrlForDashboard(menuItem: MenuItemNew) {
    this.router.navigateByUrl(menuItem.link);
    this.highlightActiveMenu(menuItem.pageName);
  }

  navigateByUrl(menuItem: MenuItemNew) {
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => this.router.navigateByUrl(menuItem.link));
    this.highlightActiveMenu(menuItem.pageName);
  }

  highlightActiveMenu(pageName: string) {
    if (!this.menuSections) { return; }

    // Reset current active
    this.menuSections.forEach(menuSection => {
      menuSection.subItems.forEach(subItem => {
        subItem.isActive = false;
      })
    })

    const foundItem: MenuItemNew | undefined = this.quickFindMenuItem(pageName,'pageName');
    foundItem.isActive = true;
  }

  highlightActiveMenuBasedOnUrl(){
    let url = this.router.url;

    const foundItem: MenuItemNew | undefined = this.getMenuItemFromUrl(url);
    foundItem.isActive = true;
  }

  hasAnyNonNumericCharacter(labelValue: string): boolean {
    for (const char of labelValue) {
      // '0' has char code 48, '9' has char code 57.
      // Alternatively: if (char < '0' || char > '9') ...
      const code = char.charCodeAt(0);
      if (code < 48 || code > 57) {
        return true; // Found a non-digit
      }
    }
    return false; // All were digits
  }

  getLastQuarterRange() {
    const now = moment();
  
    // Determine the current quarter
    const currentQuarter = now.quarter();
  
    // Calculate the last quarter
    const lastQuarter = currentQuarter - 1;
    const year = lastQuarter === 0 ? now.year() - 1 : now.year();
  
    // Start and end of the last quarter
    const startOfLastQuarter = moment().year(year).quarter(lastQuarter === 0 ? 4 : lastQuarter).startOf('quarter');
    const endOfLastQuarter = moment().year(year).quarter(lastQuarter === 0 ? 4 : lastQuarter).endOf('quarter');
  
    return { start: startOfLastQuarter.toDate(), end: endOfLastQuarter.toDate() };
  }

  getThisQuarterStart() {
    const now = moment();
  
    // Determine the current quarter
    const currentQuarter = now.quarter();
    const year = currentQuarter === 0 ? now.year() - 1 : now.year();
  
    // Start and end of the last quarter
    return moment().year(year).quarter(currentQuarter === 0 ? 4 : currentQuarter).startOf('quarter').toDate();
  }
}

