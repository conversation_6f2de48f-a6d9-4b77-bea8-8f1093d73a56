﻿using Quartz;
using System;
using System.IO;
using log4net;
using WinSCP;
using System.Threading.Tasks;
using CPHI.Spark.FTPScraper.Infrastructure;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using System.Linq;

namespace CPHI.Spark.FTPScraper.Jobs.Stockpulse.Jardine
{
   [DisallowConcurrentExecution]
   public class StockpulseDriveFetch : IJob
   {
      private static readonly ILog Logger = LogManager.GetLogger(typeof(StockpulseDriveFetch));

      private readonly FtpScraperSettings _settings;
      private readonly IConfiguration _configuration;
      private string driveFileDestination;
      private string customerName;
      private TransferOptions transferOptions;

      public StockpulseDriveFetch(IOptions<FtpScraperSettings> settings, IConfiguration config)
      {
         _settings = settings.Value;
         _configuration = config;
      }


      private async Task<int> SFTPGet()
      {
         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         driveFileDestination = _settings.DriveFileDestination;

         customerName = "drive";

         try
         {
            // Setup session options
            /// Setup session options
            SessionOptions sessionOptions = new SessionOptions
            {
               Protocol = Protocol.Sftp,
               HostName = _settings.HostnameCphi,
               UserName = _settings.UsernameCphi,
               Password = _settings.PasswordCphi,
            };

            sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;

            using (Session session = new Session())
            {
               // Connect
               session.Open(sessionOptions);
               transferOptions = new TransferOptions();
               transferOptions.TransferMode = TransferMode.Binary;

               string[] fileNames = new string[]
               {
                        "DriveStock",
                        "NLPulse",
                        "PulseWIP",
                        "StockPulseLoan"
               };

               int maxWait = 20;
               int waitCounter = 0;

               // Keep waiting until we have all the files
               while (!CheckAllFilesRecieved(session, fileNames))
               {
                  // We have waited for ten minutes -  return
                  if (maxWait == waitCounter) { return 0; }

                  System.Threading.Thread.Sleep(30000); // 30 second wait
                  waitCounter++;
               }

               System.Threading.Thread.Sleep(5000);

               // Get Files
               await GetFile(session, "DriveStock", false);
               await GetFile(session, "NLPulse", false);
               await GetFile(session, "PulseWIP", false);
               await GetFile(session, "StockPulseLoan", false);
            }
            stopwatch.Stop();
            return 0;
         }


         catch (Exception e)
         {
            stopwatch.Stop();
            errorMessage = e.ToString();
            Console.WriteLine("Error: {0}", e);

            return 1;
         }
         finally
         {
            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "FTPScraper",
               Customer = customerName,
               Environment = "PROD",
               Task = GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };

            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }
      }


      private bool CheckAllFilesRecieved(Session session, string[] fileNames)
      {

         foreach (string fileName in fileNames)
         {
            // RemoteDirectoryInfo root = session.ListDirectory("/");

            // foreach (RemoteFileInfo fileInfo in root.Files)
            // {
            //    Console.WriteLine(fileInfo.Name);
            // }

            RemoteDirectoryInfo directoryInfo = session.ListDirectory($"/drive/");

            // Filter files to include only those in the top directory
            var filesToTransfer = directoryInfo.Files
                .Where(file => file.IsDirectory == false && file.FullName.StartsWith($"/drive/{fileName}") && !file.FullName.Contains("/archive/"))
                .Select(file => file.FullName)
                .ToList();

            if (filesToTransfer.Count == 0) { return false; }
            else { Logger.Info($"CheckAllFilesRecieved: File found: {filesToTransfer[0]}"); }
         }

         return true;
      }

      private async Task GetFile(Session session, string fileToFind, bool isXLSX)
      {
         // Define transfer options if not already defined
         TransferOptions transferOptions = new TransferOptions();
         transferOptions.FileMask = "*" + fileToFind + "*";

         // Get all items in the directory including subdirectories
         RemoteDirectoryInfo directoryInfo = session.ListDirectory($"/drive/");

         // Filter files to include only those in the top directory
         var filesToTransfer = directoryInfo.Files
             .Where(file => file.IsDirectory == false &&
                            file.FullName.Contains(fileToFind) && // contains keyphrase
                            file.FullName.StartsWith($"/drive/") && // is in drive folder
                            !file.FullName.Contains("/archive/")) // is not in archive folder 
             .Select(file => file.FullName)
             .ToList();

         // Rename and Transfer each file individually
         foreach (var file in filesToTransfer)
         {
            string timePrefix = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss") + "-";
            string newNameMainPart = fileToFind;

            // Create new file name with timestamp prefix
            string newFileNameFTP = isXLSX ? $"{timePrefix}{newNameMainPart}.xlsx" : $"{timePrefix}{newNameMainPart}.csv";
            string newFilePathFTP = $"/drive/{newFileNameFTP}";

            try
            {
               // Rename the file on the FTP before downloading it
               session.MoveFile(file, newFilePathFTP);

               // Transfer the renamed file from FTP to local machine
               TransferOperationResult transferResult = session.GetFiles(newFilePathFTP, driveFileDestination, false, transferOptions);
               transferResult.Check(); // Throw on any error

               foreach (TransferEventArgs transfer in transferResult.Transfers)
               {
                  if (await TransferService.WaitForFileToCompleteAsync(session, newFilePathFTP))
                  {
                     // Local destination file with the same renamed name from FTP
                     string localNewFileName = Path.Combine(driveFileDestination, Path.GetFileName(newFileNameFTP));

                     try
                     {
                        // Move the file to the archive folder on the FTP server after transfer
                        string dateStamp = DateTime.UtcNow.ToString("ddMMyyyyHHmmss");
                        string archiveFileName = Path.GetFileNameWithoutExtension(newFileNameFTP) + "-" + dateStamp;

                        archiveFileName += isXLSX ? ".xlsx" : ".csv";

                        string archivePath = $"/drive/archive/{archiveFileName}";
                        session.MoveFile(newFilePathFTP, archivePath);

                        Logger.Info($"Pulled file: {newFilePathFTP} to {localNewFileName}");

                        System.Threading.Thread.Sleep(1000);
                     }
                     catch (Exception ex)
                     {
                        Logger.Info($"Error processing file {newFilePathFTP}: {ex.Message}");
                     }
                  }
               }
            }
            catch (Exception ex)
            {
               Logger.Info($"Error renaming file {file}: {ex.Message}");
            }
         }
      }


      public async Task Execute(IJobExecutionContext context)
      {
         try
         {

            int result = await SFTPGet();

            switch (result)
            {
               case 0:
                  Logger.Info($"[{DateTime.UtcNow}] FTPScraper Executed, succesfully pulled down files.");
                  break;
               case 1:
                  Logger.Info($"[{DateTime.UtcNow}] FTPScraper Executed, encountered error.");
                  //Emailer.SendMail("FTP Scrape had a problem", $"Failed.", _settings);
                  break;
               case 2:
                  Logger.Info($"[{DateTime.UtcNow}] FTPScraper Executed, already got all files for today.");
                  break;
               default:
                  break;
            }


         }
         catch (Exception e)
         {
            Logger.Error(e);
         }
      }



   }
}
