import { AppUserRole } from './AppUserRole';
import { RetailerSite } from './AutoPrice.model';
import { ClaimVM } from './ClaimVM';
import { UserClaim } from './UserClaim';
import { SalesRole, SiteVM } from './main.model';


export class UserAndLogin {


  constructor(existing?:UserAndLogin){
    if(existing){
      Object.assign(this,existing);
    }else{
      this.SalesRoles = [];
      this.Claims=[];
      this.chosenSites=[]

    }
  }

  AppUserId?: string;
  PersonId?: number;
  Name: string;
  //NameShort: string;
  UserName: string;  
  RoleName: string; //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
  Email: string;
  Sites: string;
  SiteId: number;
  IsSalesExec: boolean;
  AllowReportUpload: boolean; 
  AccessReportCentre: boolean; 
  AccessAllSites: boolean; //from people table
  JobTitle: string; //from people table
  SalesRoles: SalesRole[];
  IsTMgr: boolean;
  RetailerSiteId: number;
  Claims: UserClaim[];
  

  site?:SiteVM;
  chosenRetailerSite: RetailerSite;
  chosenSites: SiteVM[];
  role:AppUserRole

  initialise(sites:SiteVM[], retailerSites:RetailerSite[],appUserRoles:AppUserRole[]){
    this.site = sites.find(x=>x.SiteId==this.SiteId)
    this.chosenSites = this.getSites(this.Sites,sites);
    this.chosenRetailerSite = this.RetailerSiteId ? retailerSites.find(x => x.Id === this.RetailerSiteId) : null;
    this.role = appUserRoles.find(x=>x.Name===this.RoleName);
  }

  getSites(siteIds:string,sites:SiteVM[]): SiteVM[]
  {
    if(siteIds){

      var siteArray: number[] = siteIds.split(',').map(x => parseInt(x));
      let result:SiteVM[]=[];
      siteArray.forEach(siteId=>{
        result.push(sites.find(x=>x.SiteId===siteId))
      })
      return result;
    }else{
      return []
    }
    
  }


  convertChoicesBackToIds(claims: UserClaim[]){
    this.Claims = claims.filter(x=>x.ClaimValue!=="false");
    this.SiteId = this.site.SiteId
    this.RetailerSiteId = this.chosenRetailerSite.Id;
    this.RoleName = this.role.Name
  }

  
  
  //currentSalesRole: string; //sales roles table
  
  // canSeeStockLanding: boolean;
  // canSeeSuperCup: boolean;
  // canEditExecManagerMappings: boolean;
  // canLogin: boolean;
  //commAuthoritySelfOnly: boolean;
  //commAuthorityReview: boolean;
  //salesExecReviewSubmitter: boolean;
  //salesExecReviewReviewer: boolean;
  //liveForecastApprover: boolean;
  //liveForecastSubmitter: boolean;
  //liveForecastInputter: boolean;
  //canReviewStockPrices: boolean;
  //canActionStockPrices: boolean;
  //canEditStockPriceMatrix: boolean;
  
  
  hasChanged?: boolean;
}
