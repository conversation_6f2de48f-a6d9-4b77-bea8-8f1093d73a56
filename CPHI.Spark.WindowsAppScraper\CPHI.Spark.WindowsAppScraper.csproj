<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="CPHI.Monitor.PostLogs" Version="1.0.1" />
    <PackageReference Include="FlaUI.UIA3" Version="4.0.0" />
    <PackageReference Include="Interop.UIAutomationClient" Version="10.19041.0" />
    <PackageReference Include="log4net" Version="2.0.17" />
    <PackageReference Include="Microsoft.Exchange.WebServices.NETStandard" Version="1.1.3" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.10.0" />
    <PackageReference Include="WinSCP" Version="6.3.4" />
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.421302">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.0" />
    <PackageReference Include="System.Management" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CPHI.Spark.Model\CPHI.Spark.Model.csproj" />
    <ProjectReference Include="..\CPHI.Spark.Repository\CPHI.Spark.Repository.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>