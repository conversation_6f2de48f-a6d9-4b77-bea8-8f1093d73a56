import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GridApi } from 'ag-grid-community';
import { PartsStockCoverSiteRow } from "src/app/model/afterSales.model";
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from "../../cph.pipe";
import { SiteVM } from '../../model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from "../../services/constants.service";
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from "../../services/selections.service";
import { CustomHeaderComponent } from "../../_cellRenderers/customHeader.component";
import { PartsStockBarComponent } from '../../_cellRenderers/partsStockBar.component';
import { PartsStockService } from "./partsStock.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";

@Component({
  selector: "partsStockSitesCoverTable",

  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        class="ag-theme-balham"
        [gridOptions]="mainTableGridOptions"
        domLayout="autoHeight"
        
      >
      </ag-grid-angular>
    </div>
  `,

  styleUrls: ["./../../../styles/components/_agGrid.scss"],
  styles: [
    `
    `,
  ],
})
export class PartsStockTableCoverTableComponent implements OnInit {
  @Input() public isRegionalTable: boolean;
  @Output() rowClicked = new EventEmitter();

  columnDefs: any[];
  rowData: SiteVM[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;
  showGrid: boolean;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public router: Router,
    public excel: ExcelExportService,
    public service: PartsStockService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.mainTableGridOptions.context = { thisComponent: this };
    this.gridApi.sizeColumnsToFit();
    // this.selections.triggerSpinner.next({ show: false });

    if (this.gridApi) {
      this.gridApi.refreshCells();
    }
  }

  ngOnInit() {
    this.initParams();
    this.showGrid = false;

    this.service.filterUpdated.subscribe(value => {      
      this.setRowData();
    });

  }

  initParams() {
    let columnDefsField: string = this.constants.environment.partsStockSitesCoverTable.partStockName;

    // table definitions
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false, cellClass: "ag-right-aligned-cell",
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
      },
      
      rowData: this.provideRowData(),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onRowClicked: (params) => {
        this.onRowClick(params.data);
      },
      onFirstDataRendered:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
      onGridReady: (params) => this.onGridReady(params),
      pinnedBottomRowData: this.service.partsStockCoverSitesRows.filter(x=>x.IsTotal),
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsSiteCover),
      },
      columnDefs: [
        { headerName: "", field: "Label", colId: "Description", width: 100, type: "label", },
        {
          headerName: "Parts Stock Cover", children:
            [
              { headerName: "<1 month cover", field: 'Under1', colId: `${columnDefsField}ForwardCoverUnder1m`, width: 50, type: "currency", },
              { headerName: "<2 months cover", field: 'Under2', colId: `${columnDefsField}.ForwardCoverUnder2m`, width: 50, type: "currency", },
              { headerName: "<3 months cover", field: 'Under3', colId: `${columnDefsField}.ForwardCoverUnder3m`, width: 50, type: "currency", },
              { headerName: "<6 months cover", field: 'Under6', colId: `${columnDefsField}.ForwardCoverUnder6m`, width: 50, type: "currency", },
              { headerName: "<1 year cover", field: 'Under12', colId: `${columnDefsField}.ForwardCoverUnder12m`, width: 50, type: "currency", },
              { headerName: ">1 year cover", field: 'Over12', colId: `${columnDefsField}.ForwardCover12mPlus`, width: 50, type: "currency", },
            ],
        },
        { headerName: "Total", field: 'Total', colId: `${columnDefsField}.Total`, width: 90, type: "currency", },
        { headerName: "% >1 year cover", children:[{headerName:'', cellRenderer: PartsStockBarComponent, cellRendererParams:170, 
        field: 'PercentOver1Year', colId: `${columnDefsField}.ForwardCover12mPlusPercent`, width: 100, type: "percent", }]},
        
      ]
    }
  }
  provideRowData(): PartsStockCoverSiteRow[] {
    return this.isRegionalTable ? this.service.partsStockCoverSitesRows.filter(x=>x.IsRegion) : this.service.partsStockCoverSitesRows.filter(x=>x.IsSite)
  }

  onRowClick(site: SiteVM): void {
    this.rowClicked.next(site);
  }


  setRowData(): void
  {
    if(this.gridApi)
    {
      this.gridApi.setRowData(this.provideRowData())
      this.gridApi.setPinnedBottomRowData(this.service.partsStockCoverSitesRows.filter(x=>x.IsTotal));
    }

  }

  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Parts Stock Cover', 1, 1);
  }

}
