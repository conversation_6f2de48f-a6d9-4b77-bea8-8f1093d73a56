/// <reference types="@types/googlemaps" /> 
import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from "@angular/core";
import { Router } from '@angular/router';
import { MenuItemNew, SiteVM } from "src/app/model/main.model";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { DashboardDataItem, DashboardDataPack, DashboardDataParams, SiteCompareRow, TodayMapPlot } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";


@Component({
  selector: 'todayMap',
  templateUrl: './todayMap.component.html',
  styles: [
    `
    .flex-card-contents {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 0.5em;
      display: flex;
      flex-direction: column;
    }

    .header-with-buttons {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border-bottom: 1px solid var(--grey90);
    }

    .header-with-buttons h5 {
    }

    #map {
      display: flex;
      flex: 1;
      margin-top: 1em;
    }
    #newUsed{
      position: absolute;
      top: 4.5em;
      right: 0.5em;
    }
  `
  ]
})
export class TodayMapComponent implements OnInit {
  showFigures: boolean;

  @Output() chooseSite = new EventEmitter<SiteVM>();

  @Input() public initialDepartment: string;
  @Input() public data: TodayMapPlot[];
  @ViewChild('gmap', { static: true }) gmapElement: any;
  map: google.maps.Map;
  department: string;


  constructor(

    public selections: SelectionsService,
    public constants: ConstantsService,
    public router: Router,
    public getDataService: GetDataMethodsService,
    public service: DashboardService

  ) {
  }

  params: Date;

  ngOnInit(): void {
    this.department = this.initialDepartment;
    this.drawMap(this.data);
  }

  getData() {
    this.selections.triggerSpinner.emit({show:true,message:'Loading sites...'})
    let dataItems: DashboardDataItem[] = [DashboardDataItem.TodayMap]
    let parms: DashboardDataParams = {
      SiteIds: this.service.chosenSites.map(x => x.SiteId).join(','),
      DataItems: dataItems,
      Department: this.department
    }

    this.getDataService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {

      this.data = res.SiteTodayMapItems
      this.drawMap(this.data);
      //this.setMarkerValues();
      this.selections.triggerSpinner.emit({show:false})

    })

  }

  chooseDepartment(department: string) {
    this.department = department
    this.getData();

  }




  drawMap(data:TodayMapPlot[] ) {

    let mapStyling: google.maps.MapTypeStyle[] = [
      {
        featureType: 'administrative',
        elementType: 'all',
        stylers: [{ saturation: -100 }]
      },
      {
        featureType: "administrative.province",
        elementType: "all",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "landscape",
        elementType: "all",
        stylers: [{ saturation: -100 }, { lightness: 65 }, { visibility: "on" }]
      },
      {
        featureType: "poi",
        elementType: "all",
        stylers: [{ saturation: -100 }, { lightness: 50 }, { visibility: "simplified" }]
      },
      {
        featureType: "poi",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "road",
        elementType: "all",
        stylers: [{ saturation: -100 }]
      },
      {
        featureType: "road.highway",
        elementType: "all",
        stylers: [{ visibility: "simplified" }]
      },
      {
        featureType: "road.highway",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "road.arterial",
        elementType: "all",
        stylers: [{ lightness: 30 }]
      },
      {
        featureType: "road.arterial",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "road.local",
        elementType: "all",
        stylers: [{ lightness: 40 }]
      },
      {
        featureType: "administrative.locality",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "transit",
        elementType: 'all',
        stylers: [
          { saturation: -100 },
          { visibility: "simplified" }
        ]
      },
      {
        featureType: "water",
        elementType: "geometry",
        stylers: [{ "color": this.constants.savedTheme === 'green' ? '#91d0d3' : '#73716e' },]
      },
      {
        featureType: "water",
        elementType: "labels",
        stylers: [
          { lightness: 100 },
          { saturation: 100 }
        ]
      },
      {
        featureType: "administrative.country",
        elementType: "labels",
        stylers: [
          { visibility: "off" }
        ]
      }
    ];


    //define properties
    var mapProp: google.maps.MapOptions = {

      // This needs to be an env variable
      center: new google.maps.LatLng(this.constants.environment.todayMap.defaultPositionLat, this.constants.environment.todayMap.defaultPositionLong),     // lat, long.   Increase Lat = move map up.  Increase Long = move map right
      zoom: this.constants.environment.todayMap.defaultZoom,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      styles: mapStyling,
      mapTypeControl: false,
      maxZoom: 8,
      minZoom: 2,
      controlSize: 1,
      zoomControl: true,
      gestureHandling: 'cooperative',
    };

    //draw map
    this.map = new google.maps.Map(this.gmapElement.nativeElement, mapProp);

    data.forEach(item => {
      let fillColour = this.constants.renaultColour;

      let lat: number = item.GeoX;
      let lng: number = item.GeoY;

      if (!!item.SiteDescription && item.SiteDescription.includes("Nissan")) {
        fillColour = this.constants.nissanColour
        lng = lng - 0.2;
      }

      item.mapMarker = new google.maps.Marker({
        position: new google.maps.LatLng(lat, lng),
        map: this.map,
        title: item.SiteDescription,
        label: {
          text: "v",
          fontSize: "10px",
        },
        clickable: true,

        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          scale: 9,
          fillColor: fillColour,
          fillOpacity: 0.8,
          strokeWeight: 0.4
        },
      });

      item.mapMarker.addListener('click', this.navigateToSite.bind(this), true)

    })

    this.setMarkerValues(data);

  }

  setMarkerValues(data: TodayMapPlot[]) {

    data.forEach(item => {

      var label

      if (!!item.mapMarker) {
        label = item.mapMarker.getLabel();
      }
      else {
        label = { text: '0', fontSize: '10px' };
      }

      label.text = item.Value.toString();

      if (!!item.mapMarker) {
        item.mapMarker.setLabel(label)
      }

    })

  }

  navigateToSite(marker) {

    // this.navigateToOrderBook(marker.ya.currentTarget.title);

    //this.chooseSite.emit(clickedSite)
  }

  navigateToOrderBook(siteDescription: string) {

    //this.selections.initiateOrderBook(true);

    // if(siteDescription=='Total'){
    //   this.selections.orderBook.sites = this.constants.sitesActiveSales;
    //   this.selections.orderBook.sitesIds = this.selections.orderBook.sites.map(x=>x.Id);
    // }else{
    //   this.selections.orderBook.sites = [this.constants.Sites.find(x=>x.Description == siteDescription)];
    //   this.selections.orderBook.sitesIds = this.selections.orderBook.sites.map(x=>x.Id);
    // }

    // let vehicleTypeTypes = this.constants.vehicleTypeTypesJustUsed;
    // let orderTypeTypes = this.constants.orderTypeTypesNoTrade;

    // //set new or used link
    // if(this.IsNew){
    //   vehicleTypeTypes = this.constants.vehicleTypeTypesJustNew
    //   orderTypeTypes = this.constants.orderTypeTypesNotFleet
    // }


    // //update the order book's time periods and vehicle types and order types.
    // this.selections.orderBook.orderDate.startDate = this.constants.todayStart;
    // this.selections.orderBook.orderDate.endDate = this.constants.todayEnd;
    // this.selections.orderBook.deliveryDate.startDate = this.constants.todayStart;
    // this.selections.orderBook.deliveryDate.endDate = new Date(this.constants.today.getFullYear() + 1, 12, 0);
    // this.selections.orderBook.vehicleTypeTypes = this.constants.clone(vehicleTypeTypes);
    // this.selections.orderBook.orderTypeTypes = this.constants.clone(orderTypeTypes);
    // this.selections.orderBook.deliveryDate.dateType = 'Accg Date';
    // this.selections.orderBook.deliveryDate.amSelectingMonth = false;
    // this.selections.orderBook.deliveryDate.amSelectingAnytime = true;
    // //this.constants.selectDay(this.selections.orderBook.orderDate, this.constants.today); 
    // this.selections.orderBook.orderDate.amSelectingAnytime = false;
    // this.selections.orderBook.orderDate.amSelectingDay = true;
    // this.selections.orderBook.lateCostOption = this.constants.lateCostOptions[1];
    // this.selections.orderBook.orderOption = this.constants.orderOptions[0];
    // this.selections.orderBook.franchises = this.constants.clone(this.constants.franchises);

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Orderbook_LoadingOrderbook })
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/orderBook');
    if (menuItem) { this.constants.navigateByUrl(menuItem); } //, 'operationreports'
  }



  //set marker values
 




}


