﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using System;
using System.Collections.Generic;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Reflection;

namespace CPHI.Spark.WebApp.Service
{
    public static class ConstantsCache
    {
        //all the dates live in here:
        //private static LastUpdatedDates lastUpdatedDates;
        //public static LastUpdatedDates rrgLastUpdatedDates;
        //public static LastUpdatedDates spainLastUpdatedDates;
        //public static LastUpdatedDates vindisLastUpdatedDates;
        //public static LastUpdatedDates lmcLastUpdatedDates;
        //public static LastUpdatedDates v12LastUpdatedDates;
        //public static LastUpdatedDates mjMotorCoLastUpdatedDates;
        //public static LastUpdatedDates brindleyGroupLastUpdatedDates;
        //public static LastUpdatedDates kcsOfSurreyLastUpdatedDates;
        //public static LastUpdatedDates waylandsGroupLastUpdatedDates;
        //public static LastUpdatedDates sturgessGroupLastUpdatedDates;
        //public static LastUpdatedDates jjPremiumCarsLastUpdatedDates;
        //public static LastUpdatedDates pentagonGroupLastUpdatedDates;
        //public static LastUpdatedDates hippoApprovedLastUpdatedDates;
        //public static LastUpdatedDates lmcOfFarnhamLastUpdatedDates;
        //public static LastUpdatedDates startinLastUpdatedDates;

        public static Dictionary<DealerGroupName, LastUpdatedDates> LastUpdatedDatesDictionary = new Dictionary<DealerGroupName, LastUpdatedDates>();

        //public static string FranchiseString { get; set; }
        public static string rrgUKFranchiseString { get; set; }
        public static string rrgSpainFranchiseString { get; set; }
        public static string vindisFranchiseString { get; set; }
        //public static string autoPriceFranchiseString { get; set; }

        public static Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitionsRRG { get; set; }
        public static Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitionsVindis { get; set; }
        public static Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitionsAutoprice { get; set; }

        public static Dictionary<int, RetailerSiteStrategyBandingDefinition> ProvideBandingsDictionary(DealerGroupName dgName)
        {
            if (dgName == DealerGroupName.RRGUK) { return bandingDefinitionsRRG; }
            else if (dgName == DealerGroupName.Vindis) { return bandingDefinitionsVindis; }
            else { return bandingDefinitionsAutoprice; }
        }


        public static DateTime GetSpecificLastUpdateDate(Model.DealerGroupName dealerGroup, string dateName)
        {
            var updateDates = GetLastUpdateDates(dealerGroup);
            PropertyInfo property = updateDates.GetType().GetProperty(dateName);
            if (property != null && property.PropertyType == typeof(DateTime))
            {
                return (DateTime)property.GetValue(updateDates);
            }
            throw new Exception("Date not found");
        }


        public static LastUpdatedDates GetLastUpdateDates(Model.DealerGroupName dealerGroup)
        {
            LastUpdatedDates dates = LastUpdatedDatesDictionary[dealerGroup];
            return dates;
            
            //switch (dealerGroup)
            //{
            //    case DealerGroupName.RRGUK: return rrgLastUpdatedDates;
            //    case DealerGroupName.RRGSpain: return spainLastUpdatedDates;
            //    case DealerGroupName.Vindis: return vindisLastUpdatedDates;
            //    case DealerGroupName.LMC: return lmcLastUpdatedDates;
            //    case DealerGroupName.V12:return v12LastUpdatedDates;
            //    case DealerGroupName.MJMotorCo:return mjMotorCoLastUpdatedDates;
            //    case DealerGroupName.BrindleyGroup:return brindleyGroupLastUpdatedDates;
            //    case DealerGroupName.WaylandsGroup:return waylandsGroupLastUpdatedDates;
            //    case DealerGroupName.KCSOfSurrey:return kcsOfSurreyLastUpdatedDates;
            //    case DealerGroupName.SturgessGroup: return sturgessGroupLastUpdatedDates;
            //    case DealerGroupName.JJPremiumCars: return jjPremiumCarsLastUpdatedDates;
            //    case DealerGroupName.PentagonGroup: return pentagonGroupLastUpdatedDates;
            //    case DealerGroupName.HippoApproved: return hippoApprovedLastUpdatedDates;
            //    case DealerGroupName.LMCOfFarnham: return lmcOfFarnhamLastUpdatedDates;
            //    case DealerGroupName.Startin: return startinLastUpdatedDates;
            //    default: return null;
            //}
        }





        public static void SetSpecificDateToNow(string prop, Model.DealerGroupName dealerGroup)
        {
            var target = LastUpdatedDatesDictionary[dealerGroup];
            
            var thisType = target.GetType();
            var property = thisType.GetProperty(prop);

            property.SetValue(target, DateTime.Now);
        


            //LastUpdatedDates targetInstance = dealerGroup switch
            //{
            //    Model.DealerGroupName.RRGUK => rrgLastUpdatedDates,
            //    Model.DealerGroupName.RRGSpain => spainLastUpdatedDates,
            //    Model.DealerGroupName.Vindis => vindisLastUpdatedDates,
            //    Model.DealerGroupName.LMC => lmcLastUpdatedDates,
            //    Model.DealerGroupName.V12 => v12LastUpdatedDates,
            //    Model.DealerGroupName.MJMotorCo => mjMotorCoLastUpdatedDates,
            //    Model.DealerGroupName.BrindleyGroup => brindleyGroupLastUpdatedDates,
            //    Model.DealerGroupName.WaylandsGroup => waylandsGroupLastUpdatedDates,
            //    Model.DealerGroupName.KCSOfSurrey => kcsOfSurreyLastUpdatedDates,
            //    Model.DealerGroupName.SturgessGroup => sturgessGroupLastUpdatedDates,
            //    Model.DealerGroupName.JJPremiumCars => jjPremiumCarsLastUpdatedDates,
            //    Model.DealerGroupName.PentagonGroup => pentagonGroupLastUpdatedDates,
            //    Model.DealerGroupName.HippoApproved => hippoApprovedLastUpdatedDates,
            //    Model.DealerGroupName.LMCOfFarnham => lmcOfFarnhamLastUpdatedDates,
            //    Model.DealerGroupName.Startin => startinLastUpdatedDates,
            //    _ => throw new ArgumentException("Invalid dealer group", nameof(dealerGroup))
            //};

            //property.SetValue(targetInstance, DateTime.UtcNow);

        }


    }
}
