import {Component, Input, OnInit} from '@angular/core';
import {ConstantsService} from 'src/app/services/constants.service';
import {StrategyPriceBuildUp} from 'src/app/model/StrategyPriceBuildUp';
import {AutopriceRendererService} from 'src/app/services/autopriceRenderer.service';
import {DomSanitizer} from "@angular/platform-browser";

export interface StrategyPriceBuildUpLayersParams {

  buildUpData: StrategyPriceBuildUp[];
  daysToSell: number;
  daysBookedIn: number;
  retailRating: number;
  daysListed: number;
  daysInStock: number;
  make: string;
  specificColour: string;
  ageAndOwners: string;
  odometer: number;
  performanceRating: number;
}


@Component({
  // tslint:disable-next-line:component-selector
  selector: 'strategyPriceBuildUpLayers',
  templateUrl: './strategyPriceBuildUpLayers.component.html',
  styleUrls: ['./strategyPriceBuildUpLayers.component.scss']
})
export class StrategyPriceBuildUpLayersComponent implements OnInit {

  @Input() public params: StrategyPriceBuildUpLayersParams;
  hoverInfo: {
    VersionName?: string;
    FactorName?: string;
    FactorItemLabel?: string;
    FactorItemValue?: number;
    RuleSetComment?: string;
    Impact?: number;
    SourceValue?: string;
    ExtendedNotes?: string;
    IsRelatedToTestStrategy?: boolean;
    impact?: number;
  } = {};

  constructor(
    private constants: ConstantsService,
    private autopriceRendererService: AutopriceRendererService
  ) {
  }

  ngOnInit(): void {
  }

  measuredValue(row: StrategyPriceBuildUp) {

    if (row.ExtendedNotes) {
      return row.ExtendedNotes;
    } else {
      return row.SourceValue;
    }

  }


  get ageAndOwnersString() {
    return this.autopriceRendererService.provideAgeAndOwnersString(this.params.ageAndOwners);
  }
}
