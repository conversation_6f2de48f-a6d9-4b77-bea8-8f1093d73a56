<div id="tableLayoutManagementContainer">
  <div class="buttons">


    <!-- Open reports -->
    <div *ngIf="service.parent.ownTableStates" ngbDropdown>

      <!-- The button -->
      <button id="dropdownButton" class="btn btn-primary chooseReport" ngbDropdownToggle>


        <!-- If chosen -->
        <div id="reportName" *ngIf="service.parent.selectedTableState"
          [ngbPopover]="service.parent.selectedTableState.label" triggers="mouseenter:mouseleave" popoverClass="nowrap">
          <div class="d-flex align-items-center justify-content-between">
            <div class="reportNameLabel">{{ service.parent.selectedTableState.label }}</div>
            <i class="fa-solid fa-folder-open"></i>
          </div>
        </div>
        <!-- If default -->
        <div id="reportName" *ngIf="!service.parent.selectedTableState">
          <div class="d-flex align-items-center justify-content-between">
            <div class="reportNameLabel">Default layout</div>
            <i class="fa-solid fa-folder-open"></i>
          </div>
        </div>


      </button>


      <!-- The Menu -->
      <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

        <!-- Default report -->
        <button ngbDropdownToggle ngbDropdownItem (click)="service.resetTableState()">
          Reset to default layout
          <i class="fa-solid fa-rotate-left"></i>
        </button>

        <!-- Divider -->
        <div class="dropdown-divider"></div>


        <!-- My reports -->
        <button class="loadReport" ngbDropdownItem disabled>My reports ({{service.parent.ownTableStates?.length}})</button>
        <button *ngFor="let report of service.parent.ownTableStates" ngbDropdownToggle class="loadReport"
          ngbDropdownItem
          [ngClass]="{ 'active': report.Id === service.parent.selectedTableState?.id && !service.onlyApplyColumns && !service.onlyApplyFilters }"
          (click)="loadTableStateById(report.Id, 'own')">
          {{ report.StateLabel }}
        </button>

        <!-- Divider -->
        <div class="dropdown-divider"></div>

        <!-- Spark reports -->
        <button class="loadReport" ngbDropdownItem disabled>Spark reports ({{service.parent.sparkTableStates?.length}})</button>
        <button *ngFor="let report of service.parent.sparkTableStates" class="buttonWithColumnAndFilterOptions"
          ngbDropdownToggle class="loadReport d-flex align-items-center justify-content-between"
          ngbDropdownItem
          [ngClass]="{ 'active': report.Id === service.parent.selectedTableState?.id && !service.onlyApplyColumns && !service.onlyApplyFilters }"
          (click)="loadTableStateById(report.Id, 'spark')">
          <div class="label">
            {{ report.StateLabel }}
          </div>
        </button>

        <!-- Divider -->
        <div class="dropdown-divider"></div>

        <!-- Standard reports -->
        <button class="loadReport" ngbDropdownItem disabled>Standard reports ({{service.parent.standardTableStates?.length}})</button>
        <button *ngFor="let report of service.parent.standardTableStates" class="buttonWithColumnAndFilterOptions"
          ngbDropdownToggle class="loadReport d-flex align-items-center justify-content-between containsProfilePic"
          ngbDropdownItem
          [ngClass]="{ 'active': report.Id === service.parent.selectedTableState?.id && !service.onlyApplyColumns && !service.onlyApplyFilters }"
          (click)="loadTableStateById(report.Id, 'standard')">
          <div class="label">
            {{ report.StateLabel }}
          </div>
          <!-- The person's picture and name -->
          <div class="d-flex align-items-center">
            <profilePicImage [personId]="report.CreatedById" [size]="profilePicSize">
            </profilePicImage>
            <div class="sharedByName ms-2">
              {{ report.CreatedBy }}
            </div>
          </div>
        </button>


        <!-- Manage standard reports -->
        <button (click)="openStandardReportsModal()" ngbDropdownItem id="manageStandardReports">
          Manage standard reports...
        </button>


        <!-- Divider -->
        <div class="dropdown-divider"></div>

        <!-- Shared reports -->
        <button class="loadReport" ngbDropdownItem disabled>Shared reports ({{service.parent.sharedTableStates?.length}})</button>
        <button *ngFor="let report of service.parent.sharedTableStates" ngbDropdownToggle
          class="loadReport d-flex align-items-center justify-content-between containsProfilePic" ngbDropdownItem
          [ngClass]="{ 'active': report.Id === service.parent.selectedTableState?.id && !service.onlyApplyColumns && !service.onlyApplyFilters }"
          (click)="loadTableStateById(report.Id, 'shared')">
          <div class="label">
            {{ report.StateLabel }}
          </div>
          <!-- The person's picture and name -->
          <div class="d-flex align-items-center">
            <profilePicImage [personId]="report.SharedById" [size]="profilePicSize">
            </profilePicImage>
            <div class="sharedByName ms-2">
              {{ report.SharedBy }}
            </div>
          </div>
        </button>

      </div>


    </div>


    <!-- Rename -->
    <div [ngbPopover]="renameMessage"
      triggers="mouseenter:mouseleave">
      <button id="renameButton" class="btn btn-primary" [disabled]="!canRenameLayout"
        (click)="service.showRenameModal(service.parent.selectedTableState)">
        <i class="fa-solid fa-edit"></i>
      </button>
    </div>

    <!-- Save -->
    <button id="saveButton" class="btn btn-primary"
      [ngbPopover]="'Save \'' + service.parent.selectedTableState?.label + '\''" triggers="mouseenter:mouseleave"
      [disabled]="!service.parent.selectedTableState || service.parent.selectedTableState.isSparkState" 
      (click)="service.saveExistingTableState()">
      <i class="fa-solid fa-floppy-disk"></i>
    </button>

    <!-- Save as -->
    <button id="saveAsButton" class="btn btn-primary" [ngbPopover]="'Save as new report...'"
      triggers="mouseenter:mouseleave" (click)="service.maybeSaveNewTableState()">
      <i class="fa-solid fa-floppy-disks"></i>
    </button>

    <!-- Share -->
    <button id="shareButton" class="btn btn-primary" [ngbPopover]="'Share report...'" triggers="mouseenter:mouseleave"
      [disabled]="!service.parent.selectedTableState" (click)="maybeShareTableState()">
      <i class="fa-solid fa-share-from-square"></i>
    </button>

    <!-- Delete report -->
    <button id="deleteButton" class="btn btn-primary"
      [ngbPopover]="'Delete report \'' + service.parent.selectedTableState?.label + '\'...'"
      [disabled]="!service.parent.selectedTableState || service.parent.selectedTableState.isSparkState" 
      triggers="mouseenter:mouseleave" [disabled]="!service.parent.selectedTableState"
      (click)="service.maybeDeleteTableState()">
      <i class="fa-solid fa-trash "></i>
    </button>
  </div>

  <typeaheadAndDropdown *ngIf="service.columnsForTypeahead" [widthEm]="14" [placeholder]="'Find column'"
    [searchList]="service.columnsForTypeahead" [searchListUpdatedEmitter]="service.searchListUpdatedEmitter"
    [clearInputOnChoiceMade]="true" [customClasses]="['autoHeight']" (chosenItemEmitter)="columnToScrollTo($event)">
  </typeaheadAndDropdown>

  <!-- Column picker -->
  <button id="customiseColumns" class="btn btn-primary" *ngIf="showCustomiseColumns"
    (click)="service.openCustomiseColumnModal()">
    Customise
  </button>
</div>

<!-- Share modal -->
<ng-template #shareTableStateModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Share report '{{ service.parent.selectedTableState.label }}'</h4>
    <button class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="d-flex justify-content-between">
      <span>Share report?</span>
      <button class="custom-checkbox" [ngClass]="{ 'checked': service.parent.selectedTableState.isShared }"
        (click)="service.parent.selectedTableState.isShared = !service.parent.selectedTableState.isShared;">

        <span *ngIf="service.parent.selectedTableState.isShared">
          <i class="fa fa-check"></i>
        </span>
      </button>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-success" (click)="modal.close('OK')">Save</button>
    <button class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>
</ng-template>

<!-- Set standard modal -->
<ng-template #setStandardTableStateModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Standard Reports</h4>
    <button class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div id="setStandardTableStatesModal" class="modal-body">
    <instructionRow
      [message]="'Move reports to the left column to set them to standard. Move them to the right column to unset them.'">
    </instructionRow>
    <div class="d-flex flex-grow-1">
      <div class="dragListContainer">
        <p class="columnHeader">Standard reports</p>
        <span *ngIf="!standardTableStatesCopy || standardTableStatesCopy?.length === 0">
          No standard reports
        </span>
        <div cdkDropList #standard="cdkDropList" [cdkDropListConnectedTo]="[nonStandard]"
          [cdkDropListData]="standardTableStatesCopy" (cdkDropListDropped)="drop($event)">
          <div *ngFor="let state of standardTableStatesCopy" class="tableStateItem standard" cdkDrag>
            <div class="reportAndProfilePic">
              <div class="reportName w-50">
                <span>{{ state.StateLabel }}</span>
              </div>
              <div class="profilePicAndName">
                <profilePicImage [personId]="state.CreatedById" [size]="profilePicSize">
                </profilePicImage>
                <span class="ms-2">
                  {{ state.CreatedBy }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dragListContainer">
        <p class="columnHeader">Available reports</p>
        <div cdkDropList #nonStandard="cdkDropList" [cdkDropListConnectedTo]="[standard]"
          [cdkDropListData]="availableTableStatesCopy" (cdkDropListDropped)="drop($event)">
          <div *ngFor="let state of availableTableStatesCopy" class="tableStateItem" cdkDrag>
            <div class="reportAndProfilePic">
              <div class="reportName w-50">
                <span>{{ state.StateLabel }}</span>
              </div>
              <div class="profilePicAndName">
                <profilePicImage [personId]="state.CreatedById" [size]="profilePicSize">
                </profilePicImage>
                <span class="ms-2">
                  {{ state.CreatedBy }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-success" (click)="modal.close('OK')">Save</button>
    <button class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>
</ng-template>