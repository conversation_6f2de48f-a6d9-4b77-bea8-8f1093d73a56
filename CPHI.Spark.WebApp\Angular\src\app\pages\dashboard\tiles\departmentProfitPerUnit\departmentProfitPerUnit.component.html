<div class="dashboard-tile-inner">
  <div class="tileHeader">
    <div class="headerWords">
      <h4>
        {{constants.translatedText.Profit}}
        {{constants.translatedText.For}}
        <span *ngIf="timePeriod==='Month'">{{ constants.translatedText.TheMonth }}</span>
        <span *ngIf="timePeriod==='WTD'">{{ constants.translatedText.TheWeek }}</span>
        <span *ngIf="timePeriod==='Yesterday'">{{ constants.translatedText.Yesterday }}</span>
      </h4>
    </div>
  </div>
  
  <div class="bodyContainer">
    <div class="section">
      <p class="clickable" (click)="navigateToOrderBook('NewChassis')">
        New Chassis <span>{{ data.NewChassisPU | cph:'currency':0 }}</span>
      </p>
      <p class="clickable" (click)="navigateToOrderBook('NewFinance')">
        New Finance <span>{{ data.NewFinancePU | cph:'currency':0 }}</span>
      </p>
      <p class="clickable" (click)="navigateToOrderBook('NewAddOn')">
        New Add-on <span>{{ data.NewAddOnPU | cph:'currency':0 }}</span>
      </p>
    </div>
    <br>
    <div class="section">
      <p class="clickable" (click)="navigateToOrderBook('UsedChassis')">
        Used Chassis <span>{{ data.UsedChassisPU | cph:'currency':0 }}</span>
      </p>
      <p class="clickable" (click)="navigateToOrderBook('UsedFinance')">
        Used Finance <span>{{ data.UsedFinancePU | cph:'currency':0 }}</span>
      </p>
      <p class="clickable" (click)="navigateToOrderBook('UsedAddOn')">
        Used Add-on <span>{{ data.UsedAddOnPU | cph:'currency':0 }}</span>
      </p>
    </div>
  </div>
</div>