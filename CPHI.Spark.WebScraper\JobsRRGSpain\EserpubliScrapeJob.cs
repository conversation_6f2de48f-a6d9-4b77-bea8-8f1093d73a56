﻿using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using Quartz;
using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using log4net;

namespace CPHI.Spark.WebScraper.Jobs
{
    public class EserpubliScrapeJob : IJob
    {

        private string customerName;
        private string fileDestination;
        //private string fileDestinationDev;
        private string token;
        private string jsonToSave;
        //private string revisiones;

        //some basic setup
        private static IWebDriver _driver;

        private static readonly ILog logger = LogManager.GetLogger(typeof(EserpubliScrapeJob));

        public void Execute() { }
        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;

            stopwatch.Start();

            fileDestination = ConfigService.FileDestination;
            //fileDestinationDev = ConfigService.FileDestinationDev;
            fileDestination = fileDestination.Replace("{destinationFolder}", "spain");
            //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "spain");
            customerName = "Spain";

            token = null;

            try
            {
                logger.Info("Starting job..");

                //set chrome options
                ChromeOptions options = ScraperMethodsService.SetChromeOptions("RRGSpainEserpubli", 49152);

                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";
                
                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
                _driver.Manage().Cookies.DeleteAllCookies();

                logger.Info("Started chrome..");

                try
                {
                    await GetToken();

                    IWebElement tokenElement = WaitAndFind("/html/body");

                    token = tokenElement.Text;

                    DateTime today = DateTime.Now;

                    // Use this loop if we want to scrape more data
                    // for (int i = 0; i < 10; i++)
                    // {
                        DateTime yesterdayDate = today.AddDays(-1);

                        string yesterday = today.AddDays(-1).ToString("yyyy-MM-dd");
                        string weekAgo = yesterdayDate.AddDays(-6).ToString("yyyy-MM-dd");

                        await GetOrdenes(yesterday, weekAgo);

                        SaveFile("ordenes");

                        await GetRevisiones(yesterday, weekAgo);

                        SaveFile("revisiones");

                    //     today = today.AddDays(-7);
                    // }

                    _driver.Quit();
                    _driver.Dispose();
                    logger.Info("Succesfully completed");
                    stopwatch.Stop();
                }
                catch (Exception e)
                {
                    stopwatch.Stop();
                    errorMessage = e.ToString();
                    EmailerService eService = new EmailerService();
                    await eService.SendMail("RRG Spain Eserpubli scraper failed", $"{e.StackTrace}");
                }


            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                EmailerService eService = new EmailerService();
                await eService.SendMail("RRG Spain Eserpubli scraper failed", $"{e.StackTrace}");

                logger.Error($"Problem {e.ToString()}");
                _driver.Quit();
                _driver.Dispose();
                //KillChrome();
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WebScraper",
                    Customer = "RRG Spain",
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };

                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }


        private async Task GetToken()
        {

            //WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            //IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;


            _driver.Navigate().GoToUrl("https://apivtbi.talleractivo.net/login?username=CphiSpark&password=CphiSpark2023"); //navigate to site


        }

        private async Task GetOrdenes(string today, string weekAgo)
        {

            string url = $"apivtbi.talleractivo.net/superget?token={token}&tabla=ordenes&desde={weekAgo}&hasta={today}";
            _driver.Navigate().GoToUrl("https://" + url); //navigate to site

        }


        private async Task GetRevisiones(string today, string weekAgo)
        {
            string url = $"apivtbi.talleractivo.net/superget?token={token}&tabla=revisiones&desde={weekAgo}&hasta={today}";
            _driver.Navigate().GoToUrl("https://" + url); //navigate to site

        }


        private IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "EserpubliScrape", findXPath, andClick);

            return result;
        }


        private void SaveFile(string fileName)
        {
            IWebElement body = WaitAndFind("/html/body");

            jsonToSave = body.Text;

            string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
            string newFilePathAndName = $@"{fileDestination}{prefix}{fileName}.json";
            //string newFilePathAndNameDev = $@"{fileDestinationDev}{prefix}{fileName}.json";

            File.WriteAllText(newFilePathAndName, jsonToSave);
            //File.WriteAllText(newFilePathAndNameDev, jsonToSave);
        }


    }
}
