import {Component, OnInit} from '@angular/core';
import {
   GetContextMenuItemsParams,
   GridApi,
   GridOptions,
   GridReadyEvent,
   ICellRendererParams,
   RowDoubleClickedEvent,
   ValueFormatterParams,
   ValueGetterParams
} from 'ag-grid-community';
import {AutoTraderAdvertImage} from 'src/app/_cellRenderers/autoTraderAdvertImage.component';
import {CphPipe} from 'src/app/cph.pipe';
import {CompetitorVehicle, CompetitorVehicleDisplay} from "src/app/model/CompetitorVehicle";
import {AGGridMethodsService} from 'src/app/services/agGridMethods.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {localeEs} from 'src/environments/locale.es.js';
import {CompetitorAnalysisParams} from '../CompetitorAnalysisParams';
import {CompetitorAnalysisService} from '../competitorAnalysis.service';
import {ColumnTypesService} from 'src/app/services/columnTypes.service';


@Component({
   selector: 'competitorAnalysisTable',
   templateUrl: './competitorAnalysisTable.component.html',
   styleUrls: ['./competitorAnalysisTable.component.scss']
})
export class CompetitorAnalysisTableComponent implements OnInit {

   gridOptions: GridOptions;
   gridApi: GridApi;

   constructor(
      public gridHelpersService: AGGridMethodsService,
      public constantsService: ConstantsService,
      public cphPipe: CphPipe,
      public service: CompetitorAnalysisService,
      private columnTypesService: ColumnTypesService
   ) {
   }

   ngOnInit(): void {
      this.gridOptions = this.setGridDefinitions();
   }

   ngOnDestroy() {
      this.service.tableRef = null;
   }

   setGridDefinitions(): GridOptions<any> {


      const standardColTypes = this.columnTypesService.provideColTypes([]);

      return {
         getContextMenuItems: (params) => this.getContextMenuItems(params),
         getLocaleText: (params: any) => this.constantsService.currentLang == 'es' ?
            localeEs[params.key] || params.defaultValue : params.defaultValue,
         rowClassRules: {'highlighted': (params) => this.isHighlighted(params)},
         getRowHeight: (params) => {
            return params.node.isRowPinned() ? this.gridHelpersService.getRowHeight(30) : this.gridHelpersService.getHeaderHeight()
         },
         defaultColDef: {
            resizable: true,
            sortable: true,
            filterParams: {
               applyButton: false,
               clearButton: true,
               cellHeight: this.gridHelpersService.getFilterListItemHeight()
            },
            autoHeight: false,
            floatingFilter: true
         },
         columnTypes: {
            ...standardColTypes,
            price: {
               ...standardColTypes.number,
               valueGetter: (params) => params.data['AdvertisedPrice'],
               valueFormatter: (params: ValueFormatterParams) => params.node.isRowPinned() ?
                  params.value : this.cphPipe.transform(params.value, 'currency', 0)
            },
            pricePosition: {
               ...standardColTypes.number,
               valueGetter: (params) => params.data['PricePosition'],
               valueFormatter: (params: ValueFormatterParams) => params.node.isRowPinned() ?
                  params.value : this.cphPipe.transform(params.value, 'percent', 1)
            }
         },

         columnDefs: [
            {
               headerName: '',
               minWidth: 25,
               maxWidth: 25,
               valueGetter: (params) => this.indexColGetter(params),
               width: 8
            },
            {
               headerName: 'Photo',
               maxWidth: 100,
               minWidth: 100,
               colId: 'ImageURLs',
               field: 'ImageURL',
               type: 'special',
               cellRenderer: AutoTraderAdvertImage,
               width: 100
            },
            {
               headerName: 'Name',
               colId: 'CompetitorName',
               cellRenderer: (params) => this.nameRenderer(params),
               field: 'CompetitorName',
               type: 'label',
               width: 75
            },
            {
               headerName: 'Type',
               colId: 'Segment',
               cellRenderer: (params) => this.segmentRenderer(params),
               field: 'Segment',
               type: 'labelSetFilter',
               minWidth: 15,
               width: 10
            },
            {headerName: 'Year', colId: 'Year', field: 'Year', type: 'label', width: 25},
            {headerName: 'Mileage', colId: 'Mileage', field: 'Mileage', type: 'number', width: 30},
            {
               headerName: 'Price',
               colId: 'AdvertisedPrice',
               field: 'AdvertisedPrice',
               type: 'price',
               width: 60,
               minWidth: 50
            },
            {
               headerName: 'Price Position',
               colId: 'PricePosition',
               field: 'PricePosition',
               type: 'pricePosition',
               width: 40,
               minWidth: 60,
               sort: 'asc'
            },
            {headerName: 'Distance', colId: 'Distance', field: 'Distance', type: 'number', width: 35}
         ],
         onGridReady: (event: GridReadyEvent) => this.onGridReady(event),
         onRowDoubleClicked: (event: RowDoubleClickedEvent) => this.goToListing(event),
         pinnedTopRowData: this.getPinnedTopRowData()
      }
   }

   indexColGetter(params: ValueGetterParams<any>): any {
      if (params.node.isRowPinned()) {
         return '';
      }
      return params.node.rowIndex + 1;
   }

   nameRenderer(params: ICellRendererParams) {
      const segment = params.data.Segment;
      return params.value ?? 'Private seller';
   }

   segmentRenderer(params: ValueGetterParams) {
      const segment = params.data.Segment;
//    if(segment=='Our Vehicle'){return null;}
      return (!!segment && segment.length > 0) ? segment[0] : null;
   }

  dealWithNewData(params: CompetitorAnalysisParams) {
    if (this.gridApi) {
      this.gridApi.setRowData(params.CompetitorSummary.CompetitorVehicles);
      this.gridApi.setPinnedTopRowData(this.getPinnedTopRowData())
    }
  }


  isHighlighted(params: any): boolean {
    const isOurVehicle = params.data?.IsOurVehicle;
    return isOurVehicle
  }

   getImage(params: ICellRendererParams) {
      const row: CompetitorVehicle = params.data;

      if (!row || !row?.ImageURL) return '';
      return `<img style="height: 50px; width: 100%;" src=${row.ImageURL} />`;
   }

   onGridReady(event: GridReadyEvent) {
      this.gridApi = event.api;
      this.service.tableRef = this;
      setTimeout(() => {
         this.gridApi.sizeColumnsToFit();
      }, 200);
   }

   goToListing(event: any) {
      const websiteSearchIdentifier: string = event.data.WebsiteSearchIdentifier;
      const url: string = this.constantsService.buildAdUrl(websiteSearchIdentifier, event.data.VehicleType);
      window.open(url, '_blank').focus();
   }

   getContextMenuItems(params: GetContextMenuItemsParams<any, any>) {
      const clickedItem: CompetitorVehicle = params.node.data;

      var menuOptions = [
         'copy',
         'copyWithHeaders',
         'separator',
         {
            icon: '🔗',
            name: 'View advert (opens in new tab)',
            cssClasses: ['bold'],
            action: () => {
               this.goToListing(params.node);
            }
         }
      ]

      if (this.service.params.ParentType == 'valuationModal') {
         menuOptions.push(
            {
               icon: '£',
               cssClasses: [],
               name: 'Beat this price',
               action: () => {
                  this.service.params.VehicleValuationService.setSellingPriceTo((clickedItem.AdvertisedPrice as number) - 1)
               }
            },
            {
               icon: '%',
               cssClasses: [],
               name: 'Beat this price position',
               action: () => {
                  const newPrice: number = this.service.params.VehicleValuationService.valuationModalResultNew.ValuationPriceSet.RetailThisVehicle * ((clickedItem.PricePosition as number) - 0.0001)
                  this.service.params.VehicleValuationService.setSellingPriceTo(newPrice)
               }
            }
         );
      }

      return menuOptions;
   }

   getPinnedTopRowData() {
      let ourAdDetail = this.buildUpOurAdDetail();
      //console.log(ourAdDetail);
      return [ourAdDetail];
   }


   public buildUpOurAdDetail(): CompetitorVehicleDisplay {
      //return this.service.params.CompetitorSummary.CompetitorVehicles.find(x=>x.IsOurVehicle);
      return {
         ImageURL: '',
         CompetitorName: 'This advert',
         Year: this.cphPipe.transform(this.service.params.FirstRegisteredDate, 'year', 0),
         Mileage: this.service.params.OdometerReading,
         AdvertisedPrice: `${this.cphPipe.transform(this.service.params.AdvertisedPrice, 'currency', 0)} (${this.service.params.CompetitorSummary.PriceRank}/${this.service.params.CompetitorSummary.CompetitorVehicleCount})`,
         PricePosition: `${this.cphPipe.transform(this.service.params.PricePosition, 'percent', 1)} (${this.service.params.CompetitorSummary.ValueRank}/${this.service.params.CompetitorSummary.CompetitorVehicleCount})`,
         Distance: 0
      } as CompetitorVehicleDisplay;
   }
}
