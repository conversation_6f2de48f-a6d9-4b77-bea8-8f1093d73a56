﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
    public class GetPriceChangesNewParams
    {
        public DateTime? ChosenDate { get; set; }
        public string RetailerSiteIds { get; set; }
        public bool IncludeNewVehicles { get; set; }
        public bool IncludeSmallChanges { get; set; }
        public int DealerGroupId { get; set; }
        public Dictionary<int, RetailerSiteStrategyBandingDefinition> BandingsDict { get; set; }
        public bool IncludeUnPublishedAds { get; set; }
        public bool IncludeUnPublishedAdBasedOnSiteSetting { get; set; }
        public bool IncludeNewVehiclesBasedOnSiteSetting { get; set; }
        public bool IncludeLifecycleStatusesBasedOnSiteSetting { get; set; }
        public bool FilterDaysInStockBasedOnSiteSetting { get; set; }
        public bool IncludeVehicleTypesBasedOnSiteSetting { get; set; }
        public bool OnlyKeyChanges { get; set; }
        public List<string> LifeCycleStatuses { get; set; }
    }
}
