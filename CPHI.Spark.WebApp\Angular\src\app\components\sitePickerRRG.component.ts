import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { SiteVM,  } from '../model/main.model';
import { ConstantsService } from '../services/constants.service';



@Component({
  selector: 'sitePickerRRG',
  template:    `
    <!-- Site selector -->
    <div ngbDropdown dropright class="d-inline-block" id="siteDropdown">
        <button [disabled]="disabled" [ngClass]="buttonClass" class=" btn btn-primary" (click)="generateSitesList()"
          ngbDropdownToggle>{{siteChosenLabel()}}</button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

        <!-- ngFor buttons -->  
        <ng-container *ngFor="let site of sites">
          
        <button *ngIf="site.SiteId!==0" (click)="toggleItem(site)" [ngClass]="{'active':site.isSelected}" 
        ngbDropdownItem>{{site.SiteDescription}}</button>
        </ng-container>
        

        <!-- select Total -->  
        <button class="quickSelect" (click)="quickSelectTotal()" ngbDropdownItem>{{constants.translatedText.Total}}</button>

        <!-- quick select -->
          <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="selectSites()" [disabled]="noSitesSelected" [ngClass]="{'disabled':noSitesSelected}" >{{constants.translatedText.OKUpper}}</button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
          </div>

        </div>
      </div>
    
    `
  ,
  styles: [`
  @media (min-width: 0px) and (max-width: 1920px) and (hover:none) {
    #siteDropdown .dropdown-menu{columns:2}

  }  

    `]
})


export class SitePickerRRGComponent implements OnInit {
  @Input() sitesFromParent: SiteVM[];
  @Input() buttonClass: string;
  @Input() disabled: boolean;
  @Input() allSites: SiteVM[];
  @Output() updateSites = new EventEmitter<SiteVM[]>();

  public sites: SiteVM[];
  public siteIds: number[];

  private singleSite: number;
  public noSitesSelected: boolean = false;
  
  constructor(
    public constants: ConstantsService,
    
  ) { }


  ngOnInit(): void { 

    this.allSites = this.allSites.sort((a, b) => (a.SortOrder > b.SortOrder) ? 1 : -1).filter(x => x.IsEligibleForUser);
    
    
   }


  siteChosenLabel(): string {
    // console.log(this.sitesFromParent, "this.sitesFromParent!")
    if(!this.sitesFromParent) return this.constants.translatedText.Sites;
    if (this.sitesFromParent.length == 0) {
      return this.constants.translatedText.SitePicker_NoSitesSelected;
    } else if (this.sitesFromParent.length == 1) {
      return this.sitesFromParent[0].SiteDescription
    } else if (this.sitesFromParent.length < 4) {
      let siteNames = ''
      this.sitesFromParent.forEach((site, i) => {
        if (i > 0) { siteNames = siteNames + ',' } //leading comma for 2nd item onwards
        siteNames = siteNames + site.SiteDescShort;
      })
      return siteNames
    } else if (this.sitesFromParent.length == this.allSites.length-1){
      this.sitesFromParent = this.allSites.sort((a, b) => (a.SortOrder > b.SortOrder) ? 1 : -1);
      return this.constants.translatedText.SitePicker_AllSitesSelected;
    }else{
      return this.constants.translatedText.Sites;
    }
  }

  generateSitesList(): void {

    if(this.sitesFromParent){ 
      this.siteIds = this.sitesFromParent.map(x => x.SiteId);
    }
    else{
      this.siteIds = this.allSites.map(x => (x.SiteId) );
    }

    //recreate local list
    //first time only
    if(!this.sites){
      this.sites = this.constants.clone(this.allSites.filter(x => x.IsEligibleForUser));
    }

    //tag if it's selected
    this.sites.forEach(s => {
      if (this.siteIds.indexOf(s.SiteId) > -1 && s.SiteId > 0) {
        s.isSelected = true;

        if(s.SiteId == this.singleSite)
        {
          s.isSelected = false;
        }
      }
      else{
        s.isSelected = false;
      }
    })
  
  }

  
  toggleItem(item: any): void {

    let selectionCount: number = 0;

    this.sites.forEach(s => {

      if(item.SiteId == s.SiteId){
        s.isSelected = !s.isSelected;
      }

      if(s.isSelected)
      {
        selectionCount++;
      }
      
    })
 
    this.noSitesSelected = selectionCount == 0 ? true : false;

  }

  selectSites(): void {

    let activeSites: number = this.sites.filter(x=>x.isSelected).length;
    let siteLength = this.sites.length;

    // Fixes a weird issue where deselecting one site when all sites selected
    // does not reflect on next menu open
    if(activeSites == siteLength-1){ 
      let temp = this.sites.filter(x=> x.isSelected == false);
      this.singleSite = temp[0].SiteId;
    }
    else{
      this.singleSite = 0;
    }

    this.updateSites.emit(this.sites.filter(e => e.isSelected));
  }

 

  quickSelectTotal(): void {

    let activeSites: number = this.sites.filter(x=>x.isSelected).length;
    let siteLength = this.sites.length;

    if(activeSites == siteLength){
      this.sites.forEach(s=>s.isSelected = false)
      this.noSitesSelected = true;
    } else {
      this.sites.forEach(s=>s.isSelected = true)
      this.noSitesSelected = false;
    }
  }

  
}


