//core angular
import { Component, OnInit, Output, EventEmitter, HostListener, Input } from '@angular/core';
//model and cell renderers
import { ServiceSalesSiteRow } from '../../model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
//services
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
//Angular things, non-standard
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { ExcelExportService } from '../../services/excelExportService';
import { localeEs } from 'src/environments/locale.es.js';
import { AGGridMethodsService } from '../../services/agGridMethods.service';
import { ServiceSummaryService } from './serviceSummary.service';
import { CellClassParams, ColDef } from 'ag-grid-community';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

interface ColDefWithChildren extends ColDef {
  children?: ColDef[];
}

@Component({
  selector: 'serviceTable',
  template: `
    <div id="gridHolder">
      <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        id="ServiceSalesTable"
        class="ag-theme-balham" 
        [gridOptions]="mainTableGridOptions" 
      > 
      </ag-grid-angular>
    </div>
  `
  ,
  styles: [
    `

     
  `

  ],
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
})

export class ServiceTableComponent implements OnInit {
  @Input() isRegional: boolean;
  @Output() clickedSite = new EventEmitter<ServiceSalesSiteRow>();

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  showGrid = false;
  public gridApi;
  public gridColumnApi;
  mainTableGridOptions: GridOptionsCph;
  filterBy: string;

  constructor(
    public columnTypeService: ColumnTypesService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    public service: ServiceSummaryService
  ) { }


  ngOnInit() {
    this.filterBy = this.isRegional ? 'Regions' : 'Sites';
    this.initParams();

    this.selections.serviceSummary.summaryHasChangedEvent.subscribe(() => {
      if (this.gridApi) { this.updateGrid(); }
    })
  }

  updateGrid() {
    this.gridApi.setColumnDefs([]);
    this.gridApi.setColumnDefs(this.getColumnDef())
    this.gridApi.setRowData(this.getRowData());
    this.gridApi.setPinnedBottomRowData(this.getPinnedBottomRowData());
    this.gridApi.refreshCells({ force: true });
    this.gridApi.sizeColumnsToFit();
  }

  getPinnedBottomRowData(): any {
    //if (this.isRegional) return []

    if (this.selections.serviceSummary.showDailyView) {
      return this.service.serviceDailySalesTotalRows
    } else {
      return this.service.serviceSalesTotalRow
    }

  }
  getColumnDef(): any {

    if (this.selections.serviceSummary.showDailyView) {

      return this.provideDailyColDefs()

    }
    else {
      return this.provideCumulativeColDefs()
    }

  }
  getRowData(): any {


    if (this.selections.serviceSummary.showDailyView) {
      //return this.service.serviceDailySalesSitesRows;
      // console.log(this.service[`serviceDailySales${this.filterBy}Rows`], "this.service[`serviceDailySales${this.filterBy}Rows`]!")
      return this.service[`serviceDailySales${this.filterBy}Rows`]
    } else {
      return this.service[`serviceSales${this.filterBy}Rows`]
    }
  }

  initParams() {
    this.mainTableGridOptions = {
      getMainMenuItems:(params)=>this.gridHelpersService.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlights),
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      // getLocaleText: (key: string, defaultValue: string) => {
      //   this.constants.currentLang == 'es' ? localeEs[key] || defaultValue : defaultValue
      // },
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.getRowData(),
      pinnedBottomRowData: this.getPinnedBottomRowData(),
      onGridReady: (params) => this.onGridReady(params),
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
      domLayout: 'autoHeight',
      onCellClicked: (params) => { this.onCellClick(params); },
      
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: this.getColumnDef(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }
    }



  }

  // cellClassProvider(params: CellClassParams){
  //   if (this.isRegional) {
  //     return params?.value < 0 ? 'badFont ag-right-aligned-cell' : 'ag-right-aligned-cell';
  //   } else {
  //     return this.agGrid.cellClassProviderWithColourFontNew(params,this.service.topBottomHighlights);
  //   }
  // }

  private provideDailyColDefs() {

    let siteLabel: ColDefWithChildren[] = [
      { headerName: '', field: 'Label', colId: 'Label', width: 200, type: 'label', },
    ]

    let dailyCols: ColDefWithChildren[] = this.buildUpDailyCols();

    let mtdCols: ColDefWithChildren[] = [
      {
        headerName: 'Sales vs Target (£)',
        children: [
          { headerName: 'Tgt', field: 'Target', colId: 'Target', width: 70, type: 'currency', },
          { headerName: 'Act', field: 'Actual', colId: 'Actual', width: 70, type: 'currency', },
          { headerName: 'Vs', field: 'ActualVsTarget', colId: 'ActualVsTarget', width: 70, type: 'currency', },
          { headerName: '%', field: 'DonePercent', colId: 'DonePercent', width: 70, type: 'percent', },
        ],
      },

      {
        headerName: 'Avg.', children: [
          { headerName: '£', field: 'AverageDone', colId: 'AverageDone', width: 70, type: 'currency', },
        ]
      },]

      
      let techCols: ColDefWithChildren[] = [
      //new cols
      {
        headerName: 'Techs', children: [
          { headerName: 'FTE', field: 'TechHeadCount', colId: 'TechHeadCount', width: 70, type: 'number', },
          { headerName: 'Tgt', field: 'TechTgtPerDay', colId: 'TechTgtPerDay', width: 70, type: 'currency', },
          { headerName: 'Act', field: 'ActualPerTechPerDay', colId: 'ActualPerTechPerDay', width: 70, type: 'currency', },
          { headerName: 'Vs', field: 'PerTechPerDayVsTarget', colId: 'PerTechPerDayVsTarget', width: 70, type: 'currencyWithPlusMinus', },
        ]
      },
    ];



    let combined = []

    siteLabel.map(x => combined.push(x))
    dailyCols.map(x => combined.push(x))
    mtdCols.map(x => combined.push(x))

    if (this.selections.serviceSummary.showTechGroupColumns){
      techCols.map(x => combined.push(x))
    }

    // console.log(combined, "combined coldefs!")
    return combined
  }

  buildUpDailyCols() {
    
    let yesterday = new Date(new Date().setDate(new Date().getDate() - 1))
    let dailyCols: ColDefWithChildren[] = [];

    for (let i = 0; i < this.selections.serviceSummary.month.endDate.getDate(); i++) {

      let date = new Date(this.selections.serviceSummary.month.startDate.getFullYear(), this.selections.serviceSummary.month.startDate.getMonth(), i + 1);
      let isWeekend = [0,6].includes(date.getDay())
      let isSunday = date.getDay() === 0;

      if (isSunday) continue;

      //let dayLabel = date.toLocaleString(this.constants.translatedText.LocaleCode, { weekday: 'short' }) + date.toLocaleString(this.constants.translatedText.LocaleCode, { day: '2-digit' });
      let dayLabelSmall = date.getDate().toString();

      let columnWidth = 50;
      let hidden = true;
      if (date.getTime() < yesterday.getTime()) { columnWidth = 70, hidden = false }

      dailyCols.push(
        {
          headerName: dayLabelSmall,
          cellStyle: { 'background': isWeekend ? '#ECF0F1' : '#FFFFFF' },
          field: `d${dayLabelSmall}`, colId: `d${dayLabelSmall}`, width: columnWidth, type: 'currency', hide: hidden
        }
      );

    }

    let mainCol = { headerName: 'Daily Figures (£)', children: dailyCols }

    return [mainCol];
  }

  provideCumulativeColDefs(): (import("ag-grid-community").ColDef | import("ag-grid-community").ColGroupDef)[] {
    return [
      { headerName: '', field: 'Label', colId: 'Label', width: 425, type: 'label' },
      { headerName: this.constants.translatedText.MonthTarget, field: 'MonthTarget', colId: 'MonthTarget', width: 115, type: 'currency' },
      {
        headerName: this.constants.translatedText.Dashboard_ServiceSales_SalesVsTarget,
        children: [
          { headerName: this.constants.translatedText.Target, field: 'TargetToDate', colId: 'TargetToDate', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.Actual, field: 'DoneToDate', colId: 'DoneToDate', width: 115, type: 'currency' },
          { headerName: 'Vs', field: 'VsToDate', colId: 'VsToDate', width: 115, type: 'currencyWithFontColour' },
          { headerName: this.constants.translatedText.Achievement, field: 'AchievementToDate', colId: 'AchievementToDate', width: 115, type: 'percent' },
        ],
      },
      {
        headerName: this.constants.translatedText.Dashboard_ServiceSales_SalesPerDay,
        children: [
          { headerName: this.constants.translatedText.Target, field: 'TargetPerDay', colId: 'TargetPerDay', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.Done, field: 'DonePerDay', colId: 'DonePerDay', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.Required, field: 'RequiredPerDay', colId: 'RequiredPerDay', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.StepUp, field: 'StepUpPerDay', colId: 'StepUpPerDay', width: 115, type: 'currencyFlipColour' },
        ],
      },
      {
        headerName: this.constants.translatedText.Dashboard_ServiceSales_WorkInProgress,
        children: [
          { headerName: '< 30', field: 'LessThanThirtyDays', colId: 'LessThanThirtyDays', width: 115, type: 'currency' },
          { headerName: '30 - 60 ', field: 'ThirtyToSixtyDays', colId: 'ThirtyToSixtyDays', width: 115, type: 'currency' },
          { headerName: '60 + ', field: 'GreaterThanSixtyDays', colId: 'GreaterThanSixtyDays', width: 115, type: 'currency' }
        ],
      },
    ]
  }



  resizeGrid() {
    if (this.gridApi) { this.gridApi.sizeColumnsToFit(); }
  }

  onCellClick(params) {
    if (params.data.IsSite) {
      this.clickedSite.next(this.service.serviceSalesSitesRows.find(s => s.Label == params.data.Label));
    } else if (params.data.IsRegion) {
      this.clickedSite.next(this.service.serviceSalesRegionsRows.find(s => s.Label == params.data.Label));
    } else {
      this.clickedSite.next(this.service.serviceSalesTotalRow[0]);
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);
    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };
  }

  excelExport() {
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Service Dept - ' + this.selections.serviceSummary.timeOption, 1, 1);
  }
}
