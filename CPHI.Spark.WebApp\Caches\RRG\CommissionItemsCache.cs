﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.BusinessLogic.RRG;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.RRG
{
    public interface ICommissionItemsCache
    {
        void EmptyCache(string dealerGroupName);
        Task<IEnumerable<CommissionStatement>> GetCommissionItems(DateTime startOfMonth);
    }


    public class CommissionItemsCache : ICommissionItemsCache
    {
        private readonly IConfiguration config;

        public CommissionItemsCache(IConfiguration config)
        {
            this.config = config;
        }



        public async Task<IEnumerable<CommissionStatement>> GetCommissionItems(DateTime startOfMonth)
        {
            string cacheName = $"{DealerGroupName.RRGUK.ToString()}|commissionItems{startOfMonth.Year}{startOfMonth.Month}";

            if (!MemoryCache.Default.Contains(cacheName))
            {
                IEnumerable<CommissionStatement> rows = await GetCommission(startOfMonth, cacheName);
                FillCache(cacheName, rows);
            }

            return (IEnumerable<CommissionStatement>)MemoryCache.Default.GetCacheItem(cacheName).Value;
        }


        public void EmptyCache(string dealerGroupName)
        {
            foreach (var item in MemoryCache.Default.AsEnumerable())
            {
                if (item.Key.StartsWith($"{dealerGroupName}|commissionItems"))
                {
                    MemoryCache.Default.Remove(item.Key);
                }
            }
        }







        private async Task<IEnumerable<CommissionStatement>> GetCommission(DateTime startOfMonth, string cacheName)
        {
            string connectionString = config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(DealerGroupName.RRGUK));
            WebAppCommissionLogic logic = new WebAppCommissionLogic(connectionString);
            return await logic.CalcCommissions(startOfMonth.Date, DealerGroupName.RRGUK);
        }




        private void FillCache(string cacheName, object contents)
        {
            lock (MemoryCache.Default)
            {
                DateTime expiry = DateTime.Now.AddMinutes(50);
                MemoryCache.Default.Add(new CacheItem(cacheName, contents), new CacheItemPolicy() { AbsoluteExpiration = expiry });
            }
        }


    }




}

