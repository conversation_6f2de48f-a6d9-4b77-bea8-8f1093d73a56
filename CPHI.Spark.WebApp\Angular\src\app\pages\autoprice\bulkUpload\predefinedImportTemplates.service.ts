import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ColDef, ColumnApi, GridApi, ValueGetterParams } from 'ag-grid-community';
import moment from 'moment';
import { BasicInfoModalComponent } from 'src/app/components/basicInfoModal/basicInfoModal.component';
import { BulkUploadService } from './bulkUpload.service';
import { BulkUploadPredefinedTemplateType } from './BulkUploadPredefinedTemplateType';
import { ParsedImportFileRow } from './bulkUpload.model';


@Injectable({
    providedIn: 'root'
})
export class PredefinedImportTemplatesService {
    rowData: ParsedImportFileRow[];
    gridApi: GridApi;
    columnApi: ColumnApi;

    constructor(
        private modalService: NgbModal,
        private bulkUploadService: BulkUploadService
    ) { }


    
    provideColumnDefs(sourceReportType:string): ColDef[] {

        const showCol = (sourcesValidFor:string[])=>{
            return sourcesValidFor.includes(sourceReportType);
        }

        return [
            { headerName: 'Event Type', hide: !showCol(['Carwow', 'Manheim', 'Motorway', 'Motorway 2', 'Motorway 3', 'BCA', 'BCA 3', 'Motability']), field: 'eventType', colId: 'EventType', type: 'labelSetFilter', width: 10 },
            { headerName: 'Event Date', hide: !showCol(['Aston Barclay', 'Manheim', 'BCA', 'BCA 3']), field: 'eventDate', colId: 'EventDate', type: 'dateLongYear', width: 10 },
            { headerName: 'Location', hide: !showCol(['Aston Barclay', 'Carwow', 'Manheim', 'Motorway', 'Motorway 2', 'Motorway 3', 'BCA', 'BCA Approved', 'BCA 3', 'Motability', 'UCaRS']), field: 'location', colId: 'Location', type: 'labelSetFilter', width: 10 },
            { headerName: 'Lot Number', hide: !showCol(['Aston Barclay', 'Carwow', 'Manheim', 'Shoreham Vehicle Auctions','The Fleet Auction Group', 'BCA', 'BCA 3', 'Santander Live', 'VCRS Live']), field: 'lotNumber', 
            colId: 'LotNumber', type: 'label', width: 10 },
            { headerName: 'Seller', hide: !showCol(['Aston Barclay', 'Manheim', 'Shoreham Vehicle Auctions', 'The Fleet Auction Group', 'BCA', 'BCA 3']), field: 'seller',
             colId: 'Seller', type: 'labelSetFilter', width: 10 },
             { headerName: 'Link', hide: !showCol(['Carwow', 'Motorway', 'BCA Approved', 'Motorway 2', 'Motorway 3']), field: 'link', colId: 'Link', type: 'htmlLink', width: 10 },
            { headerName: 'Condition', hide: !showCol(['Carwow', 'Motorway', 'Motorway 2', 'Motorway 3', 'Manheim', 'Shoreham Vehicle Auctions', 'The Fleet Auction Group', 'BCA', 'BCA Approved', 'BCA 3', 'Motability', 'Santander Live', 'VCRS Live']), field: 'condition', colId: 'Condition', type: 'labelSetFilter', width: 10 },
            { headerName: 'V5 Status', hide: !showCol(['Aston Barclay', 'Manheim', 'Shoreham Vehicle Auctions', 'The Fleet Auction Group']), field: 'v5Status', colId: 'V5Status',
                 type: this.bulkUploadService.chosenPredefinedCompany.Type === BulkUploadPredefinedTemplateType.AstonBarclay ? 'boolean' : 'labelSetFilter', width: 10 },
            { headerName: 'MOT Expiry', hide: !showCol(['Aston Barclay', 'Manheim', 'Shoreham Vehicle Auctions', 'The Fleet Auction Group']), field: 'motExpiry', colId: 'MOTExpiry', type: 'dateLongYear', width: 10 },
            { headerName: 'VAT Qualifying', hide: !showCol(['Aston Barclay', 'Manheim', 'Shoreham Vehicle Auctions', 'The Fleet Auction Group', 'BCA', 'BCA Approved', 'BCA 3', 'Santander Live', 'VCRS Live']), field: 'isVatQualifying', colId: 'VATQualifying', type: 'boolean', width: 10 },
            { headerName: 'Mileage', field: 'mileage', colId: 'Mileage', type: 'number', width: 10 },
            { headerName: 'Mileage Warranted', hide: !showCol(['Aston Barclay', 'Manheim', 'BCA Approved']), field: 'mileageWarranty', colId: 'MileageWarranty', type: 'boolean', width: 10 },
            { headerName: 'Former Keepers', hide: !showCol(['Aston Barclay', 'Carwow', 'Manheim', 'Motorway', 'BCA Approved', 'Motorway 2', 'Motorway 3']), field: 'formerKeepers', colId: 'FormerKeepers', type: 'number', width: 10 },
            { headerName: 'Service History', hide: !showCol(['Carwow', 'Motorway', 'Motorway 2', 'Motorway 3', 'BCA', 'BCA 3', 'Santander Live']), field: 'serviceHistory', colId: 'ServiceHistory', type: 'labelSetFilter', width: 10 },
            { headerName: 'Services',hide: !showCol(['Manheim', 'BCA', 'BCA 3']), field: 'services', colId: 'Services', type: 'number', width: 10 },
            { headerName: 'Date of Last Service',hide: !showCol(['Manheim']), field: 'dateOfLastService', colId: 'DateOfLastService', type: 'dateLongYear', width: 10 },
            { headerName: 'Insurance Cat',hide: !showCol(['Manheim']), field: 'insuranceCat', colId: 'InsuranceCat', type: 'labelSetFilter', width: 10 },
            { headerName: 'No. of Keys',hide: !showCol(['Manheim', 'BCA Approved']), field: 'numberOfKeys', colId: 'NumberOfKeys', type: 'labelSetFilter', width: 10 },
            { headerName: 'On Finance',hide: !showCol(['Motorway', 'Motorway 2', 'Motorway 3']), field: 'onFinance', colId: 'OnFinance', type: 'boolean', width: 10 },
            { headerName: 'Imported', hide: !showCol(['Carwow']), field: 'imported', colId: 'Imported', type: 'boolean', width: 10 },
            { headerName: 'Vehicle Type',hide: !showCol(['Carwow']), field: 'vehicleType', colId: 'VehicleType', type: 'labelSetFilter', width: 10 },
            { headerName: 'Registration', field: 'vehicleReg', colId: 'Registration', type: 'label', width: 10 },
            { headerName: 'VIN',hide: !showCol(['Motorway', 'Motorway 3', 'BCA Approved', 'UCaRS']), field: 'vin', colId: 'VIN', type: 'label', width: 10 },
            { headerName: 'Make',hide: !showCol(['Carwow', 'Motorway', 'Manheim', 'Shoreham Vehicle Auctions','Motorway 2','Motorway 3', 'The Fleet Auction Group', 'BCA', 'BCA Approved', 'BCA 3', 'Motability']), field: 'make', colId: 'Make', type: 'labelSetFilter', width: 10 },
            { headerName: 'Model',hide: !showCol(['Carwow', 'Motorway', 'Manheim', 'Shoreham Vehicle Auctions','Motorway 2', 'Motorway 3','The Fleet Auction Group', , 'UCaRS', 'BCA', 'BCA Approved', 'BCA 3', 'Motability','Santander Live', 'VCRS Live']), field: 'model', colId: 'Model', type: 'labelSetFilter', width: 10 },
            { headerName: 'Body', hide: !showCol(['Motorway', 'Motorway 3', 'Manheim', 'Shoreham Vehicle Auctions', 'The Fleet Auction Group', 'BCA', 'BCA 3', 'UCaRS']), field: 'body', colId: 'Body', type: 'labelSetFilter', width: 10 },
            { headerName: 'No. Doors', hide: !showCol(['BCA', 'BCA 3']), field: 'doors', colId: 'doors', type: 'number', width: 10 },
            { headerName: 'Transmission',hide: !showCol(['Carwow', 'Motorway','Motorway 2','Motorway 3', 'Manheim', 'Shoreham Vehicle Auctions', 'The Fleet Auction Group', 'BCA', 'BCA 3', 'Motability', 'Santander Live', 'VCRS Live', 'UCaRS']), field: 'transmission', colId: 'Transmission', type: 'labelSetFilter', width: 10 },
            { headerName: 'Description', hide: !showCol(['Aston Barclay', 'Manheim', 'Motability', 'Santander Live', 'VCRS Live']), field: 'description', colId: 'Description', type: 'label', width: 10 },
            { headerName: 'Colour', field: 'colour', colId: 'Colour', type: 'labelSetFilter', width: 10 },
            { headerName: 'Fuel Type',hide: !showCol(['Carwow', 'Motorway', 'Motorway 3', 'Manheim', 'Shoreham Vehicle Auctions', , 'UCaRS', 'The Fleet Auction Group', 'BCA', 'BCA Approved', 'BCA 3', 'Motability']),  field: 'fuelType', colId: 'FuelType', type: 'labelSetFilter', width: 10 },
            { headerName: 'CO2',hide: !showCol(['Manheim']), field: 'co2', colId: 'CO2', type: 'number', width: 10 },
            { headerName: 'Registration Date', hide: !showCol(['Aston Barclay', 'Carwow', , 'UCaRS', 'Manheim', 'Shoreham Vehicle Auctions', 'The Fleet Auction Group', 'BCA', 'BCA Approved', 'BCA 3', 'Santander Live', 'VCRS Live']), field: 'registrationDate', colId: 'RegistrationDate', type: 'dateLongYear', width: 10 },
            { headerName: 'Notes', hide: !showCol(['Aston Barclay', 'Carwow', 'Manheim', 'Motorway', 'Motorway 3', 'BCA', 'BCA Approved', 'BCA 3', 'Motability', 'Santander Live', 'VCRS Live']), field: 'notes', colId: 'Notes', type: 'label', width: 10 },
            { headerName: 'Notes 2', hide: !showCol(['Santander Live', 'VCRS Live']), field: 'notes2', colId: 'Notes2', type: 'label', width: 10 },
            { headerName: 'Notes 3', hide: !showCol(['Santander Live', 'VCRS Live']), field: 'notes3', colId: 'Notes3', type: 'label', width: 10 },
            { headerName: 'Notes 4', hide: !showCol(['Santander Live']), field: 'notes4', colId: 'Notes4', type: 'label', width: 10 },

            { headerName: 'Model Year', hide: !showCol(['Santander Live', 'VCRS Live', 'Motability','Motorway','Motorway 2','Motorway 3','Carwow']), field: 'modelYear', colId: 'ModelYear', type: 'label', width: 10 },
            { headerName: 'Upholstery',hide: !showCol(['Santander Live', 'VCRS Live', 'UCaRS']),  field: 'upholstery', colId: 'Upholstery', type: 'labelSetFilter', width: 10 },
            { headerName: 'Retail Price', hide: !showCol(['Santander Live', 'VCRS Live', 'UCaRS']), field: 'retailPrice', colId: 'RetailPrice', type: 'currency', width: 10 },
            { headerName: 'Serv. Within Sch?', hide: !showCol(['Santander Live']), field: 'servicedWithinSchedule', colId: 'ServicedWithinSchedule', type: 'boolean', width: 10 },
            { headerName: 'Approved Used?', hide: !showCol(['Santander Live']), field: 'approvedUsed', colId: 'ApprovedUsed', type: 'boolean', width: 10 },
            { headerName: 'Refurbished?', hide: !showCol(['Santander Live']), field: 'refurbished', colId: 'Refurbished', type: 'boolean', width: 10 },

            { headerName: 'CAP Id', hide: !showCol(['Motability']), field: 'capId', colId: 'CAPId', type: 'number', width: 10 },
            { headerName: 'CAP Value', hide: !showCol(['Aston Barclay', 'Carwow', 'Manheim', 'Motorway', 'Motorway 2', 'Motorway 3', 'BCA', 'BCA Approved', 'BCA 3', 'Santander Live', 'VCRS Live']), field: 'capValue', colId: 'CAPValue', type: 'currency', width: 10 },
            { headerName: 'CAP New', hide: !showCol(['BCA', 'BCA 3']), field: 'capNew', colId: 'CAPNew', type: 'currency', width: 10 },
            { headerName: 'CAP Retail', hide: !showCol(['BCA', 'BCA 3']), field: 'capRetail', colId: 'CAPRetail', type: 'currency', width: 10 },
            { headerName: 'CAP Av.', hide: !showCol(['BCA', 'BCA 3']), field: 'capAverage', colId: 'CAPAverage', type: 'currency', width: 10 },
            { headerName: 'CAP Below', hide: !showCol(['BCA', 'BCA 3']), field: 'capBelow', colId: 'CAPBelow', type: 'currency', width: 10 },

            { headerName: 'Reserve or BuyItNow',hide: !showCol(['Carwow', 'Manheim', 'Motorway', 'BCA', 'BCA 3', 'Motability', 'Motorway 2', 'Motorway 3']), field: 'reserveOrBuyItNow', colId: 'ReserveOrBuyItNow', type: 'currency', width: 10 }
        ]
      }

    convertBcaFile(rows: any[]) {

        // Verify file format
        const expectedHeaders: string[] = [
            "Start Date",
            "Sale Location",
            "Channel",
            "Sale Type",
            "Lot Number",
            "Registration Number",
            "Make",
            "Model",
            "Derivative",
            "Engine Size",
            "Body",
            "Colour",
            "Number Of Doors",
            "Transmission",
            "Fuel",
            "Grade",
            "Mileage",
            "Registration Date",
            "Service History Present",
            "Number of Services",
            "Main Dealer Services",
            "Last Service Date",
            "Last Service Mileage",
            "DVSA Mileage",
            "Additional Service Notes",
            "MOT Expiry",
            "Declarations",
            "Equipment",
            "VAT Type",
            "Buy Now Price",
            "Guide Price Header" // The subheaders of this contain Cap values
        ];
        
        
        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];

        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        // Skip the two sub header rows
        for (let i = 3; i < rows.length; i++) {

            let serviceHistory: string = this.getServiceHistoryString(rows[i].S, rows[i].T, rows[i].U, rows[i].V, rows[i].W, rows[i].Y);

            rowData.push({
                eventDate: this.convertToCurrentYearDate(rows[i].A),
                location: rows[i].B,
                eventType: rows[i].C,
                seller: rows[i].D,
                lotNumber: rows[i].E,
                vehicleReg: this.removeAllSpaces(rows[i].F),
                make: rows[i].G,
                model: rows[i].H,
                description: rows[i].I,
                //engine: rows[i].J,
                bodyType: rows[i].K,
                colour: rows[i].L,
                doors: parseFloat(rows[i].M),
                transmission: rows[i].N,
                fuelType: rows[i].O,
                condition: rows[i].P,
                mileage: parseFloat(rows[i].Q.replace(',', '')),
                registrationDate: this.formatDate(rows[i].R),
                serviceHistory: serviceHistory, // Need to do a customer function here to combine these into Service History
                services: parseFloat(rows[i].T),
                mainDealerServices: rows[i].U,
                dateOfLastService: this.formatDate(rows[i].V),
                lastServiceMileage: rows[i].W,

                // dvsaMileage: rows[i].X,
                additionalServiceNotes: rows[i].Y,
                motExpiry: this.formatDate(rows[i].Z),

                // declarations: rows[i].AA,
                notes: rows[i].AB,
                isVatQualifying: rows[i].AC,
                reserveOrBuyItNow: this.formatNumber(rows[i].AD),
                capValue: this.formatNumber(rows[i].AG),
                
                capNew: this.formatNumber(rows[i].AE),
                capRetail: this.formatNumber(rows[i].AF),  
                capAverage: this.formatNumber(rows[i].AH),
                capBelow: this.formatNumber(rows[i].AI),  
            })
        }

        this.rowData = rowData;
        // console.log(this.rowData, "this.rowData bca!")
    }

    convertBca3File(rows: any[]) {

        // Verify file format
        const expectedHeaders: string[] = [
            "Start Date",
            "Sale Location",
            "Channel",
            "Sale Type",
            "Lot Number",
            "Registration Number",
            "Make",
            "Model",
            "Derivative",
            "Engine Size",
            "Body",
            "Colour",
            "Number Of Doors",
            "Transmission",
            "Fuel",
            "Grade",
            "Mileage",
            "Registration Date",
            "Service History Present",
            "Number of Services",
            "Main Dealer Services",
            "Last Service Date",
            "Last Service Mileage",
            "DVSA Mileage",
            "Additional Service Notes",
            "MOT Expiry",
            "Declarations",
            "Equipment",
            "Additional Information",
            "VAT Type",
            "Buy Now Price",
            "Guide Price Header" // The subheaders of this contain Cap values
        ];
        
        
        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];

        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        // Skip the two sub header rows
        for (let i = 3; i < rows.length; i++) {

            let serviceHistory: string = this.getServiceHistoryString(rows[i].S, rows[i].T, rows[i].U, rows[i].V, rows[i].W, rows[i].Y);

            rowData.push({
                eventDate: this.convertToCurrentYearDate(rows[i].A),
                location: rows[i].B,
                eventType: rows[i].C,
                seller: rows[i].D,
                lotNumber: rows[i].E,
                vehicleReg: this.removeAllSpaces(rows[i].F),
                make: rows[i].G,
                model: rows[i].H,
                description: rows[i].I,
                engine: rows[i].J,
                bodyType: rows[i].K,
                colour: rows[i].L,
                doors: parseFloat(rows[i].M),
                transmission: rows[i].N,
                fuelType: rows[i].O,
                condition: rows[i].P,
                mileage: parseFloat(rows[i].Q.replace(',', '')), //Removes the comma from the mileage
                registrationDate: this.formatDate(rows[i].R),
                serviceHistory: serviceHistory, // Need to do a customer function here to combine these into Service History
                services: parseFloat(rows[i].T),
                mainDealerServices: rows[i].U,
                dateOfLastService : this.formatDate(rows[i].V),
                lastServiceMileage: rows[i].W,

                // dvsaMileage: rows[i].X,
                additionalServiceNotes: rows[i].Y,
                motExpiry: this.formatDate(rows[i].Z),

                // declarations: rows[i].AA,
                notes: rows[i].AC,
                isVatQualifying: this.getVatQualifyingBcaApproved3(rows[i].AD),
                reserveOrBuyItNow: this.formatNumber(rows[i].AE),
                capValue: this.formatNumber(rows[i].AG),
                
                capNew: this.formatNumber(rows[i].AF),
                capRetail: this.formatNumber(rows[i].AG),  
                capAverage: this.formatNumber(rows[i].AI),
                capBelow: this.formatNumber(rows[i].AJ),  
            })
        }

        this.rowData = rowData;
    }

    convertBcaApprovedFile(rows: any[]) {

        // Verify file format
        const expectedHeaders: string[] = [
            "Branch",
            "Registration",
            "DOR",
            "VIN",
            "Make",
            "Model",
            "Derivative",
            "Engine",
            "B",
            "D",
            "F",
            "G",
            "Colour",
            "VAT",
            "Mileage",
            "Vendor Name",
            "Next Sale Date",
            "Next Sale Number",
            "Next Lot Number",
            "V5 Status",
            "V5 Branch",
            "MOT",
            "MOT Expiry",
            "Service History", // X
            "CAP ID",
            "CAP Type",
            "CAP Value",
            "Appraisal Grade",
            "Assured Type",
            "Number of Keys",
            "Mileage Warranted",
            "ADD Options",
            "ZSH Options",
            "Other Options",
            "Keepers",
            "Mech Gradde",
            "Comments", // AK
            "Link to Appraisal",
            "Link to Cosmetic Appraisal",
            "N/S/F",
            "N/S/R",
            "O/S/F",
            "O/S/R",
            
        ];
        
        
        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];

        for (let i = 0; i < expectedHeaders.length; i++) {
            if (!this.removeLineBreaks(actualHeaders[i]).includes(expectedHeaders[i])) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        // Skip the two sub header rows
        for (let i = 1; i < rows.length; i++) {

            rowData.push({

                location: rows[i].A,
                vehicleReg: this.removeAllSpaces(rows[i].B),
                registrationDate: this.formatRegistrationDateBCAApproved(rows[i].C),
                vin: this.removeAllSpaces(rows[i].D),
                make: rows[i].E,
                model: rows[i].F,
                description: rows[i].G,
                engine: rows[i].H,
                seller: rows[i].P,
                colour: rows[i].M,
                mileage: this.formatNumber(rows[i].O),
                isVatQualifying: this.getVatQualifyingBcaApproved(rows[i].N),
                lotNumber: rows[i].S,
                numberOfKeys: rows[i].AD,
                motExpiry: this.formatRegistrationDateBCAApproved(rows[i].W),
                capValue: this.formatNumber(rows[i].AA),
                condition: this.removeNonNumericAndReturnNumber(rows[i].AB).toString(),
                
                // Added 23/08 
                formerKeepers: parseFloat(rows[i].AI),
                link: rows[i].AL,
                fuelType: rows[i].AC,
                mileageWarranty: this.getMileageWarranty(rows[i].AE),
                notes: rows[i].AH, // Other Options

            })
        }

        //console.log(rowData, "rowData!");

        this.rowData = rowData;
    }

    getMileageWarranty(input: string) : boolean {
        if(!input)
        { 
            return false; 
        }
        if(input.trim().toUpperCase() == "WARRANTED") 
        {
            return true;
        }
    }

    removeLineBreaks(input: string): string {
        return input.replace(/[\r\n]+/g, ' ');
    }

    removeNonNumericAndReturnNumber(input: string): number {
        // Remove non-numeric characters
        const numericString = input.replace(/\D/g, '');
        // Convert to number
        return Number(numericString);
    }

    getVatQualifyingBcaApproved(input: string): boolean
    {
        if(input == "Q"){ return true; }
        return false;
    }

    getVatQualifyingBcaApproved3(input: string): boolean
    {
        if(input == "VAT Qualifying"){ return true; }
        return false;
    }

    getVatQualifyingSantander(input: string): boolean
    {
        try
        {
            if(input.trim().toLowerCase() == "yes")
            { 
                return true; 
            }
            else
            {
                return false;
            }

        }
        catch
        {
            return false;
        }
    }

    getBoolSantander(input: string): boolean
    {
        if(input == null){ return false; }
        if(input.trim() == "Y"){ return true; }
        return false;
    }

    getServiceHistoryString (
        ServiceHistoryPresent: string,
        NumberOfServices: number,
        MainDealerServices: number,
        LastServiceDate: string, // Expecting date format as 'dd/MM/yyyy'
        LastServiceMileage: number,
        AdditionalServiceNotes?: string
    ): string {
        let formattedDate = '';
    
        // Check if the date is '01/01/0001'
        if (LastServiceDate === '01/01/0001' || LastServiceDate === null) {
            formattedDate = 'Last service date not recorded.';
        } else {
            // Parse LastServiceDate from 'dd/MM/yyyy' format
            const dateParts = LastServiceDate.split('/');
            const day = parseInt(dateParts[0], 10);
            const month = parseInt(dateParts[1], 10) - 1; // Months are zero-based in JavaScript
            const year = parseInt(dateParts[2], 10);
            const dateObject = new Date(year, month, day);
    
            // Format the date as "dd MMM yy"
            const dateOptions = { day: '2-digit', month: 'short', year: '2-digit' } as const;
            formattedDate = dateObject.toLocaleDateString('en-GB', dateOptions);
        }
    
        let serviceHistoryString = `Service history: ${ServiceHistoryPresent}, ${NumberOfServices} services, of which ${MainDealerServices} main dealer.`;
        
        if (formattedDate === 'Last service date not recorded.') {
            serviceHistoryString += ` ${formattedDate}`;
        } else {
            serviceHistoryString += ` Last service on ${formattedDate} at ${LastServiceMileage} miles.`;
        }
    
        // Add additional service notes if present
        if (AdditionalServiceNotes && AdditionalServiceNotes.trim() !== '') {
            serviceHistoryString += ` Notes: ${AdditionalServiceNotes}`;
        }
    
        return serviceHistoryString;
    }


    convertAstonBarclayFile(rows: any[]) {
        // Verify file format
        const expectedHeaders: string[] = [
            "Sale Date", "Location", "Lot Number", "Colour", "Description", "Vehicle Detail", "Date First Registered", "Registration Number", "Mileage", "Mileage Warranty", "V5", "MOT Date", "VAT Qualified", "Seller", "Former Keepers", "Grade", "NAMA", "CAP Value", "Auto Trader Trade Value", "Auto Trader Retail Value"
        ]
        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo" && /^[A-Z]$/.test(key))
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 1; i < rows.length; i++) {
            rowData.push({
                eventDate: rows[i].A,
                location: rows[i].B,
                lotNumber: rows[i].C,
                seller: rows[i].N,
                v5Status: rows[i].K === 'Y' ? true : false,
                motExpiry: this.formatDate(rows[i].L),
                isVatQualifying: rows[i].M === 'Y' ? true : false,
                mileage: parseFloat(rows[i].I),
                mileageWarranty: rows[i].J === 'Y' ? true : false,
                formerKeepers: parseFloat(rows[i].O),
                vehicleReg: rows[i].H,
                description: rows[i].E,
                colour: rows[i].D,
                registrationDate: this.formatDate(rows[i].G),
                notes: rows[i].F,
                capValue: this.formatNumber(rows[i].R)
            })
        }

        this.rowData = rowData;
    }

    convertToCurrentYearDate(dateString: string): Date {
        // Split the date and time parts
        const [datePart, timePart] = dateString.split(' ');
    
        // Split the date part into day and month
        const [day, month] = datePart.split('/').map(Number);
    
        // Get the current year
        const currentYear = new Date().getFullYear();
    
        // Split the time part into hours and minutes
        const [hours, minutes] = timePart.split(':').map(Number);
    
        // Create a new Date object using the extracted parts
        const date = new Date(currentYear, month - 1, day, hours, minutes);
    
        return date;
    }

    convertCarwowFile(rows: any[]) {
        // Verify file format
        const expectedHeaders: string[] = [
            "SALES_TYPE", "LISTING_ID", "LISTING_URL", "REG", "DATE_APPROVED_BY_CARWOW", "MAKE", "MODEL", "MILEAGE", "CAR_AGE_YEARS", "FIRST_REGISTERED", "CAR_YEAR", "CAP_CLEAN", "RESERVE_OR_BUY_NOW_PRICE", "PREVIOUS_OWNERS_COUNT", "CONDITION_GRADE", "SERVICE_HISTORY", "ENGINE", "FUEL_TYPE", "BODYCOLOUR", "TRANSMISSION", "SELLER_NOTES", "LISTING_REGION", "LISTING_CITY", "VEHICLE_TYPE", "VAT_APPLICABLE", "IMPORTED"
        ]
        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo" && /^[A-Z]$/.test(key))
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 1; i < rows.length; i++) {
            rowData.push({
                eventType: rows[i].A,
                location: rows[i].V,
                lotNumber: rows[i].B,
                link: rows[i].C,
                condition: rows[i].O,
                isVatQualifying: rows[i].Y === 'Yes' ? true : false,
                mileage: parseFloat(rows[i].H),
                formerKeepers: parseFloat(rows[i].N),
                serviceHistory: rows[i].P,
                imported: rows[i].Z === 'Yes' ? true : false,
                vehicleType: rows[i].X,
                vehicleReg: rows[i].D,
                make: rows[i].F,
                model: rows[i].G,
                engine: rows[i].Q,
                transmission: rows[i].T,
                colour: rows[i].S,
                fuelType: rows[i].R,
                registrationDate: this.formatDate(rows[i].J),
                modelYear: rows[i].K,
                notes: rows[i].U,
                capValue: this.formatNumber(rows[i].L),
                reserveOrBuyItNow: this.formatNumber(rows[i].M)
            })
        }

        this.rowData = rowData;
    }

    removeAllSpaces(input: string): string {
        return input.replace(/\s+/g, '');
    }

    convertManheimFile(rows: any[]) {
        // Verify file format
        const expectedHeaders: string[] = [
            "Lot No", "Location", "Event date", "Event type", "Simulcast vehicle?", "Manufacturer", "Model", "Derivative", "Reg Date", "Reg No.", "Engine", "Body", "Colour (Trim)", "Odometer", "Odometer unit", "Odometer warranted", "Fuel", "Trans", "VAT", "V5", "VCar", "Vehicle Grade", "MOT Expiry", "Number of Keepers", "Services", "Date of last Service (odometer)", "Vendor", "Buy Now Price", "CAP Clean", "CAP Average", "MMP", "MMP Av.", "MMP Av.", "Number Of", "Specification", "CO2"
        ]
        const headerRowIndex: number = 1;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 3; i < rows.length; i++) {
            rowData.push({
                eventType: rows[i].D,
                eventDate: this.formatDate(rows[i].C),
                location: rows[i].B,
                lotNumber: rows[i].A,
                seller: rows[i].AA,
                condition: this.conditionMapping(rows[i].V),
                v5Status: rows[i].T,
                motExpiry: this.formatDate(rows[i].W),
                isVatQualifying: rows[i].S === 'Margin' ? true : false,
                mileage: this.formatNumber(rows[i].N),
                mileageWarranty: rows[i].P === '(Warranted)' ? true : false,
                formerKeepers: parseFloat(rows[i].X),
                services: parseFloat(rows[i].Y),
                dateOfLastService: this.formatDate(rows[i].Z),
                insuranceCat: rows[i].U,
                numberOfKeys: rows[i].AH,
                vehicleReg: rows[i].J,
                make: rows[i].F,
                model: rows[i].G,
                engine: rows[i].K,
                bodyType: rows[i].L,
                transmission: rows[i].R,
                description: rows[i].H,
                colour: rows[i].M,
                fuelType: rows[i].Q,
                co2: parseFloat(rows[i].AJ),
                registrationDate: this.formatRegistrationDate(rows[i].I),
                notes: rows[i].AI,
                capValue: this.formatNumber(rows[i].AC),
                reserveOrBuyItNow: this.formatNumber(rows[i].AB)
            })
        }

        this.rowData = rowData;
    }

    convertMotorwayFile(rows: any[]) {
        // Verify file format
        const expectedHeaders: string[] = [
            "Buying type", "VRM", "Make", "Model", "Year", "Mileage", "Number of owners", "Service history", "Exterior grade", "VIN", "Colour", "Body type", "Fuel", "Transmission", "Engine size", "On finance", "Equipment", "Additional specifications", "Reserve price", "CAP clean value", "Location", "Motorway vehicle link"
        ]
        const headerRowIndex: number = 0;
        let headerRow: any = rows[headerRowIndex];
        if(headerRow.A == 'ï»¿"Buying type'){
            headerRow.A = 'Buying type'
        }

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 1; i < rows.length; i++) {
            rowData.push({
                eventType: rows[i].A,
                location: rows[i].U,
                link: rows[i].V,
                modelYear: rows[i].E,
                condition: this.conditionMapping(rows[i].I, true),
                mileage: parseFloat(rows[i].F),
                formerKeepers: parseFloat(rows[i].G),
                serviceHistory: rows[i].H,
                onFinance: rows[i].P === 'Yes' ? true : false,
                vehicleReg: rows[i].B,
                vin: rows[i].J,
                make: rows[i].C,
                model: rows[i].D,
                engine: rows[i].O,
                bodyType: rows[i].L,
                transmission: rows[i].N,
                colour: rows[i].K,
                fuelType: rows[i].M,
                notes: rows[i].Q,
                capValue: this.formatNumber(rows[i].T),
                reserveOrBuyItNow: this.formatNumber(rows[i].S)
            })
        }

        this.rowData = rowData;
    }

    convertMotorway3File(rows: any[]) : void {

        // Verify file format
        const expectedHeaders: string[] = [
            "Buying type", "VRM", "Make", "Model", "Year", "Mileage", "Number of owners", "Location", "Service history",
            "Exterior grade", "VIN", "Colour", "Body type", "Fuel", "Transmission", "Engine size", "Equipment", "On finance",
            "Additional specifications", "Additional condition and damage information", "Mechanical issues disclosed by seller",
            "Additional mechanical information","Reserve price", "CAP clean value", "Motorway vehicle link"
        ];
        
        const headerRowIndex: number = 0;

        let headerRow: any = rows[headerRowIndex];
        
        if(headerRow.A == 'ï»¿"Buying type'){
            headerRow.A = 'Buying type'
        }

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: any[] = [];

        try
        {
            for (let i = 1; i < rows.length; i++) {
                rowData.push({
                    eventType: rows[i].A,
                    vehicleReg: rows[i].B,
                    make: rows[i].C,
                    model: rows[i].D,
                    modelYear: this.formatNumber(rows[i].E),
                    mileage: this.formatNumber(rows[i].F),
                    formerKeepers: parseFloat(rows[i].G),
                    location: rows[i].H,
                    serviceHistory: rows[i].I,
                    condition: this.conditionMapping(rows[i].J, true),
                    vin: rows[i].K,
                    colour: rows[i].L,
                    body: rows[i].M,
                    fuelType: rows[i].N,
                    transmission: rows[i].O,
                    engine: rows[i].P,
                    equipment: rows[i].Q,
                    onFinance: rows[i].R === 'Yes' ? true : false,
                    notes: rows[i].S,
                    reserveOrBuyItNow: this.formatNumber(rows[i].W),
                    capValue: this.formatNumber(rows[i].X),
                    link: rows[i].Z,
                })
            }
        }
        catch(e)
        {
            console.log(e, "error!")
        }


        this.rowData = rowData;

        //console.log(this.rowData, "this.rowData");
    }

    convertMotorway2File(rows: any[]) : void {

        // Verify file format
        const expectedHeaders: string[] = [
            "Buying type", "VRM", "Make", "Model", "Year", "Mileage", "Number of owners", "Location", "Service history",
            "Exterior grade", "VIN", "Colour", "Body type", "Fuel", "Transmission", "Engine size", "Equipment", "On finance",
            "Additional specifications", "Reserve price", "CAP clean value", "Motorway vehicle link"
        ];
        
        const headerRowIndex: number = 0;

        let headerRow: any = rows[headerRowIndex];
        
        if(headerRow.A == 'ï»¿"Buying type'){
            headerRow.A = 'Buying type'
        }

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: any[] = [];

        try
        {
            for (let i = 1; i < rows.length; i++) {
                rowData.push({
                    eventType: rows[i].A,
                    vehicleReg: rows[i].B,
                    make: rows[i].C,
                    model: rows[i].D,
                    modelYear: this.formatNumber(rows[i].E),
                    mileage: this.formatNumber(rows[i].F),
                    formerKeepers: parseFloat(rows[i].G),
                    location: rows[i].H,
                    serviceHistory: rows[i].I,
                    condition: this.conditionMapping(rows[i].J, true),
                    vin: rows[i].K,
                    colour: rows[i].L,
                    body: rows[i].M,
                    fuelType: rows[i].N,
                    transmission: rows[i].O,
                    engine: rows[i].P,
                    equipment: rows[i].Q,
                    onFinance: rows[i].R === 'Yes' ? true : false,
                    notes: rows[i].S,
                    reserveOrBuyItNow: this.formatNumber(rows[i].T),
                    capValue: this.formatNumber(rows[i].U),
                    link: rows[i].V,
                })
            }
        }
        catch(e)
        {
            console.log(e, "error!")
        }


        this.rowData = rowData;

        //console.log(this.rowData, "this.rowData");
    }

    convertUCaRSFile(rows: any[]) : void {

        // Verify file format
        const expectedHeaders: string[] = [
            "Registration plate", //
            "Mileage", //
            "Model type", //
            "Paint", //
            "Previous use", //
            "Reg. date", //
            "Offer price (excl. VAT)", //
            "Offer price (incl. VAT)", //
            "VIN", //
            "Class", //
            "Body", //
            "Upholstery", //
            "Fuel type", //
            "Transmission", //
            "Location" //
        ];
        

        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        // Remove anything nonalphanumeric except full stops, brackets and spaces
        // There seems to be weird chars in file?
        const actualHeaders = Object.keys(headerRow)
        .filter(key => key !== "RowNo")
        .map(key => headerRow[key].replace(/[^a-zA-Z0-9 .()]/g, ""));
    
        let badColumns: string[] = [];

        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 1; i < rows.length; i++) {

            if (!rows[i].A || rows[i].A.trim() === "") {
                continue;
            }

            rowData.push({
                vehicleReg: rows[i].A,
                mileage: rows[i].B,
                model: rows[i].C,
                colour: rows[i].D,
                previousUse: rows[i].E,
                registrationDate: this.formatDate(rows[i].F),
                retailPrice: parseFloat(rows[i].H), // OfferPrice Inc. Vat
                vin: rows[i].I,
                bodyType: rows[i].K,
                upholstery: rows[i].L,
                fuelType: rows[i].M,
                transmission: rows[i].N,
                location: rows[i].O,
            })
        }

        this.rowData = rowData;
    }

    convertVCRSFile(rows: any[]) : void {

        // Verify file format
        const expectedHeaders: string[] = [
            "Lot", //
            "Grading", //
            "RegNo", // 
            "Model", //
            "Model Desc", //
            "Variant", //
            "Gearbox",
            "Current Mileage", //
            "Model Year", //
            "Reg Date",
            "Colour", //
            "Upholstery", //
            "Fleet Group", //
            "Options", //
            "Packs", //
            "Vat Qualified", //
            "RetailPrice", //
            "CAP Clean"
        ]
        

        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 1; i < rows.length; i++) {

            if (!rows[i].A || rows[i].A.trim() === "") {
                continue;
            }

            rowData.push({

                lotNumber: rows[i].A,
                condition: this.conditionMapping(rows[i].B, false, true),
                vehicleReg: rows[i].C,
                model: rows[i].E,
                description: rows[i].F,
                transmission: rows[i].G,
                mileage: rows[i].H,
                modelYear: rows[i].I,
                registrationDate: this.formatDate(rows[i].J),
                colour: rows[i].K,
                upholstery: rows[i].L,
                notes3: rows[i].M,
                notes: rows[i].N,
                notes2: rows[i].O,
                isVatQualifying: this.getVatQualifyingSantander(rows[i].P),
                retailPrice: parseFloat(rows[i].Q),
                capValue: parseFloat(rows[i].R),
            })
        }

        this.rowData = rowData;
    }

    convertSantanderFile(rows: any[]) : void {

        // Verify file format
        const expectedHeaders: string[] = [
            "Lot", //
            "Grading", //
            "RegNo", // 
            "Model", //
            "Model Desc", //
            "Variant", //
            "Gearbox",
            "Current Mileage", //
            "Model Year", //
            "Reg Date",
            "Colour", //
            "Upholstery", //
            "Fleet Group", //
            "Options", //
            "Packs", //
            "Vat Qualified", //
            "RetailPrice", //
            "CAP Clean", //
            "Service History", //
            "TYRE TREAD DEPTH NSF",
            "TYRE TREAD DEPTH NSR",
            "TYRE TREAD DEPTH OSF",
            "TYRE TREAD DEPTH OSR",
            "Refurbished?",
            "Serviced within Schedule?", //
            "Selekt Programme (Y/N)",
        ]
        
        // Zero index
        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 1; i < rows.length; i++) {
            rowData.push({

                lotNumber: rows[i].A,
                condition: this.conditionMapping(rows[i].B, false, true),
                vehicleReg: rows[i].C,
                model: rows[i].E,
                description: rows[i].F,
                transmission: rows[i].G,
                mileage: rows[i].H,
                modelYear: rows[i].I,
                registrationDate: this.formatDate(rows[i].J),
                colour: rows[i].K,
                upholstery: rows[i].L,
                notes3: rows[i].M,
                notes: rows[i].N,
                notes2: rows[i].O,
                isVatQualifying: this.getVatQualifyingSantander(rows[i].P),
                retailPrice: parseFloat(rows[i].Q),
                capValue: parseFloat(rows[i].R),
                serviceHistory: rows[i].S,
                notes4: 'NSF ' + rows[i].T + ', NSR ' + rows[i].U + ', OSF ' + rows[i].V + ', OSR ' + rows[i].W,
                refurbished: this.getBoolSantander(rows[i].X),
                servicedWithinSchedule: this.getBoolSantander(rows[i].Y),
                approvedUsed: this.getBoolSantander(rows[i].Z),          

            })
        }

        this.rowData = rowData;
    }

    convertMotabilityFile(rows: any[]) : void {

        // Verify file format
        const expectedHeaders: string[] = [
            "Reg No.", 
            "Type", 
            "Condition", 
            "Buy Now Price", 
            "Make", 
            "Model", 
            "Variant", 
            "CAP ID", 
            "CAP %", 
            "Transmission", 
            "Fuel", 
            "Mileage", 
            "Colour", 
            "Year", 
            "Location", 
            "Maintained", 
            "Refurb Centre"
        ]
        

        const headerRowIndex: number = 0;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 1; i < rows.length; i++) {
            rowData.push({
                vehicleReg: rows[i].A,
                eventType: rows[i].B,
                condition: rows[i].C,
                reserveOrBuyItNow: this.formatNumber(rows[i].D),
                make: rows[i].E,
                model: rows[i].F,
                description: rows[i].G,
                capId: rows[i].H,
                transmission: rows[i].J,
                fuelType: rows[i].K,
                mileage: parseFloat(rows[i].L),
                colour: rows[i].M,
                modelYear: rows[i].N,
                location: rows[i].O,
                notes: rows[i].P,
            })
        }

        this.rowData = rowData;
    }

    convertShorehamOrFleetAuctionGroupFile(rows: any[]) {
        // Verify file format
        const expectedHeaders: string[] = [
            "Lot", "Reg", "Make", "Model", "Colour", "Body", "Fuel", "Trans", "DOR", "V5", "Mileage", "MOT", "VAT", "Grade", "Seller"
        ]
        const headerRowIndex: number = 1;
        const headerRow: Object = rows[headerRowIndex];

        const actualHeaders = Object.keys(headerRow)
            .filter(key => key !== "RowNo")
            .map(key => headerRow[key]);

        let badColumns: string[] = [];
        for (let i = 0; i < expectedHeaders.length; i++) {
            if (expectedHeaders[i] !== actualHeaders[i]) {
                badColumns.push(`Cell ${this.numberToLetter(i)}${headerRowIndex+1} expected "${expectedHeaders[i]}" but got "${actualHeaders[i]}".`);
            }
        }

        if (badColumns.length > 0) {
            this.showErrorModal(badColumns.join('\n'));
            return;
        }

        // Convert to agGrid column defs format
        let rowData: ParsedImportFileRow[] = [];

        for (let i = 2; i < rows.length; i++) {
            rowData.push({
                lotNumber: rows[i].A,
                seller: rows[i].O,
                condition: this.conditionMapping(rows[i].N),
                v5Status: rows[i].J,
                motExpiry: this.formatDate(rows[i].L, true),
                isVatQualifying: rows[i].M === 'Q' ? true : false,
                mileage: parseFloat(rows[i].K),
                vehicleReg: rows[i].B,
                make: rows[i].C,
                model: rows[i].D,
                bodyType: rows[i].F,
                transmission: rows[i].H,
                colour: rows[i].E,
                fuelType: rows[i].G,
                registrationDate: this.formatDate(rows[i].I)
            })
        }

        this.rowData = rowData;
    }

    private numberToLetter(number) {
        if (number <= 25) {
            return String.fromCharCode(65 + number); // 65 is A
        } else {
            return `A${String.fromCharCode(65 + number-26)}`
        }
    }

    private showErrorModal(errors: string) {
        const modalRef = this.modalService.open(BasicInfoModalComponent, { size: 'md' });
        modalRef.componentInstance.header = 'Failed to open file';
        modalRef.componentInstance.body = `Unable to upload file as the following errors were found:\n${errors}`;

        modalRef.result.then(res => {
            return;
        })
    }

    private formatDate(date: string, motExpiry?: boolean) {
        if (!date || date === "") return null;
        
        // Special case for MOT expiry for these 2 as isn't formatted like the rest
        if (motExpiry) {
          //  console.log(date)
            const momentDate = moment(date, 'MM/YYYY');
            return new Date(momentDate.format('MM/DD/YYYY'));
        }

        const dateNoTime: string = date.split(' ')[0];
        const momentDate = moment(dateNoTime, 'YYYY-MM-DD');
        return new Date(momentDate.format('MM/DD/YYYY'));
    }

    private formatRegistrationDate(date: string) {
        if (!date || date === "") return null;
        
        const dateNoTime: string = date.split(' ')[0];
        const momentDate = moment(dateNoTime, 'DD-MM-YYYY');
        return new Date(momentDate.format('MM/DD/YYYY'));
    }


    private formatRegistrationDateBCAApproved(date: string) {
        if (!date || date === "") return null;
    
        // Check if the date is in Excel serial format
        if (!isNaN(Number(date))) {
            const excelDate = Number(date);
            const jsDate = new Date(Math.round((excelDate - 25569) * 86400 * 1000));
            return jsDate;
        }
    
        // Assume the date is in 'DD/MM/YYYY' format
        const dateNoTime: string = date.split(' ')[0];
        const momentDate = moment(dateNoTime, 'DD/MM/YYYY');
        
        if (!momentDate.isValid()) {
            console.error(`Invalid date: ${dateNoTime}`);
            return null;
        }
        
        const formattedDate = momentDate.format('MM/DD/YYYY');
        console.log(`Formatted date: ${formattedDate}`);
        return new Date(formattedDate);
    }
    
    
    
    private formatNumber(value: string) {
        if (!value || value === "") return null;
        return parseInt(value.replace(/,/g, ''));
    }

    private conditionMapping(condition: string, isMotorway?: boolean, isSantander?: boolean) {

        if (!isMotorway && !isSantander) 
        {
            switch (condition) {
                case '1':
                    return 'Excellent (Grade 1)';
                case '2':
                    return 'Great (Grade 2)';
                case '3':
                    return 'Good (Grade 3)';
                case '4':
                    return 'Average (Grade 4)';
                case '5':
                    return 'Poor (Grade 5)';
                default:
                    return 'Unclassified';
            }
        } 
        else if (isMotorway) 
        {
            switch (condition) {
                case 'gold':
                    return 'Excellent (Grade 1)';
                case 'silver':
                    return 'Good (Grade 3)';
                case 'bronze':
                    return 'Poor (Grade 5)';
                default:
                    return 'Unclassified';
            }
        }
        else if (isSantander) 
        {
            switch (condition) {
                case 'Grade 1':
                    return 'Excellent (Grade 1)';
                case 'Grade 2':
                    return 'Great (Grade 2)';
                case 'Grade 3':
                    return 'Good (Grade 3)';
                case 'Grade 4':
                    return 'Average (Grade 4)';
                case 'Grade 5':
                    return 'Poor (Grade 5)';
                case 'Wholesale':
                    return 'Wholesale';
                default:
                    return 'Unclassified';
            }
        }
    }
}
