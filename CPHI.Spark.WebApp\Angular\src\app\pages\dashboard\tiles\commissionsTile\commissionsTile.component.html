<div class="dashboard-tile-inner">
  <div class="tileHeader">
    <div class="headerWords">
      <h4>{{ title }}</h4>
      <div class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
    </div>
  </div>

  <div class="numberHolder">
    <table>
      <tbody>
        <tr class="numberRow">
          <td>
            <h2 class="spaceBetween column value"><strong>{{ data.CommissionPerPerson | cph:'number':0 }}</strong></h2>
          </td>
          <td>
            <h2 class="spaceBetween column value"><strong>{{ data.CommissionPerUnit | cph: 'number':0 }}</strong></h2>
          </td>
        </tr>

        <tr class="labelRow">
          <td>
            <div class="spaceBetween column label">{{ constants.translatedText.PerPerson }}</div>
          </td>
          <td>
            <div class="spaceBetween column label">{{ constants.translatedText.PerVehicle }}</div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>