<!-- <table id="vsCompetitorsTable">
                <tr>
                    <td>Price Position</td>
                    <td class="text-end" [colSpan]="2">{{ data ? (data.PricePosition | cph:'percent':0) : '-' }}</td>
                </tr>
                <tr>
                    <td>Price Rank</td>
                    <td *ngIf="data" class="text-end" [colSpan]="2">{{ data.PriceRank }} / {{ data.CompetitorVehicleCount }}</td>
                    <td *ngIf="!data">-</td>
                </tr>
                <tr>
                    <td>Value Rank</td>
                    <td *ngIf="data" class="text-end" [colSpan]="2">{{ data.ValueRank }} / {{ data.CompetitorVehicleCount }}</td>
                    <td *ngIf="!data">-</td>
                </tr>
            </table> -->
<div id="competitorsTable" *ngIf="service.params" [ngStyle]="{'height':getHeight()}">

   <div class="d-flex align-items-center mb-2">
      <span class="me-2">From</span>
      <div ngbDropdown dropright class="d-inline-block" container="body">
         <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>
            {{ selectedMinPlate }} reg
         </button>
         <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let plate of plateChoices" ngbDropdownToggle class="manualToggleCloseItem"
                    ngbDropdownItem (click)="selectMinPlate(plate)">
               {{ plate }} reg
            </button>
         </div>
      </div>

      <span class="mx-2">To</span>
      <div ngbDropdown dropright class="d-inline-block me-4" container="body">
         <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>
            {{ selectedMaxPlate }} reg
         </button>
         <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let plate of plateChoices" ngbDropdownToggle class="manualToggleCloseItem"
                    ngbDropdownItem (click)="selectMaxPlate(plate)">
               {{ plate }} reg
            </button>
         </div>
      </div>

      <div ngbDropdown dropright class="d-inline-block me-4" container="body">
         <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>
            {{ getDistanceLabel() }}
         </button>
         <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let radius of distances" ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem
                    [ngClass]="{ 'active': selectedRadius === radius }" (click)="selectRadius(radius)">
               Within {{ radius }} {{ radius === 1 ? 'mile' : 'miles' }}
            </button>
            <button ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem
                    [ngClass]="{ 'active': selectedRadius === 1000 }" (click)="selectRadius(1000)">
               National
            </button>
         </div>
      </div>

      <span
         *ngIf="!amWithinInsightsModal">&nbsp; ({{ constants.pluralise(service.params.CompetitorSummary.CompetitorVehicleCount, 'advert', 'adverts') }}
         )</span>
      <!-- <div ngbDropdown dropright class="d-inline-block" container="body">
                      <button class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>
                          Change view
                      </button>
                      <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                          <button ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem
                              [ngClass]="{ 'active': !showBlobs }" (click)="showBlobs = false">
                              Table
                          </button>
                          <button *ngFor="let field of chartXYFields" ngbDropdownToggle class="manualToggleCloseItem"
                              ngbDropdownItem [ngClass]="{ 'active': chartData === field && showBlobs }"
                              (click)="showChartView(field)">
                              Chart - {{ field.xPretty }} and {{ field.yPretty }}
                          </button>
                      </div>
                  </div> -->

      <!-- Choose table or blobs buttons, only if we are needing to choose between then -->
      <div class="buttonGroup" *ngIf="!showBlobsAndTableTogether()">
         <button class="btn btn-primary" [ngClass]="{ 'active': !showBlobs }" ngbPopover="Table"
                 triggers="mouseenter:mouseleave" container="body" (click)="showBlobs = false">
            <i class="fas fa-table"></i>
         </button>
         <button *ngFor="let field of chartXYFields" class="btn btn-primary"
                 [ngbPopover]="'Chart - ' + field.xPretty + ' and ' + field.yPretty" triggers="mouseenter:mouseleave"
                 container="body" [ngClass]="{ 'active': chartData === field && showBlobs }"
                 (click)="showChartView(field)">
            <i class="fas fa-chart-scatter"></i>
         </button>
      </div>

      <button *ngIf="!showBlobs" class="btn btn-primary" (click)="scrollToThisVehicle()">
         <i class="fas fa-down-to-line"></i>
      </button>
   </div>

   <span *ngIf="!service.params.CompetitorSummary">No competitor data to display</span>
   <!-- Table view -->
   <ng-container *ngIf="service.params.CompetitorSummary">

      <ng-container *ngIf="showBlobsAndTableTogether()">

         <!-- We want to see the table AND the blobs at once -->

         <!-- Table -->
         <competitorAnalysisTable></competitorAnalysisTable>


         <!-- Blob type selector -->
         <div class="buttonGroup mx-0 my-2">

            <button *ngFor="let field of chartXYFields" class="btn btn-primary"
                    [ngClass]="{ 'active': showActiveChartButton(field)}"
                    (click)="showChartView(field)">
               <i class="fas fa-chart-scatter"></i>
               <span>&nbsp;{{ field.xPretty + ' and ' + field.yPretty }}</span>
            </button>
         </div>


         <div id="blobChartWrapper">
            <!-- Blobs -->
            <competitorAnalysisChart [selectedXYFields]="chartData">
            </competitorAnalysisChart>
         </div>

      </ng-container>


      <ng-container *ngIf="!showBlobsAndTableTogether()">

         <!-- More compact view, either table OR blobs -->

         <competitorAnalysisTable *ngIf="!showBlobs">
         </competitorAnalysisTable>

         <competitorAnalysisChart [selectedXYFields]="chartData"
                                  *ngIf="showBlobs"></competitorAnalysisChart>

      </ng-container>


   </ng-container>
</div>
