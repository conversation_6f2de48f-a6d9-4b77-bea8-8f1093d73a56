using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public abstract class BaseVehicleModel //: IVehicleModel, IIncluded, ILastModified, ISoftDelete
    {
        public string Id { get; set; }

        public bool IsIncluded { get; set; }

        public bool IsVirtual { get; set; }

        public VehicleTypeEnum VehicleType { get; set; }

        public DateTime? LastModified { get; set; }

        public UserModel? LastModifiedBy { get; set; }

        public string? StockNumber { get; set; }
        //{
        //    get => this.GetVehicleIdentifier("ndStockId");
        //    set => this.AddOrUpdateVehicleIdentifier("ndStockId", value);
        //}

        public string? DmsStockNumber { get; set; }
        //{
        //    get => this.GetVehicleIdentifier("dmsStockNumber");
        //    set => this.AddOrUpdateVehicleIdentifier("dmsStockNumber", value);
        //}

        public string? Vrm { get; set; }
        //{
        //    get => this.GetVehicleIdentifier("registration");
        //    set => this.AddOrUpdateVehicleIdentifier("registration", value);
        //}

        public string? Vin { get; set; }
        //{
        //    get => this.GetVehicleIdentifier("vin");
        //    set => this.AddOrUpdateVehicleIdentifier("vin", value);
        //}

        public string? Make { get; set; }

        public string? Range { get; set; }

        public string? Model { get; set; }

        public string? Derivative { get; set; }

        public string? Variant { get; set; }

        public string? BodyStyle { get; set; }

        public string? Transmission { get; set; }

        public string? FuelType { get; set; }

        public int? Doors { get; set; }

        public int? Owners { get; set; }

        public int? ProductionYear { get; set; }

        public int? RegistrationYear { get; set; }

        public int? Co2 { get; set; }

        public int? Mpg { get; set; }

        public int? EngineSizeLitres { get; set; }

        public int? EngineSizeCc { get; set; }

        public DateTime? DateOfFirstRegistration { get; set; }

        public DateTime? ManufacturedDate { get; set; }

        public string? Colour { get; set; }

        //public OdometerModel? Odometer { get; set; }

        //public List<VehicleMediaModel>? Media { get; set; }

        public string? VehicleDescription { get; set; }

        public DateTime? Deleted { get; set; }

        public UserModel? DeletedBy { get; set; }

        //public VehicleLocation? Location { get; set; }

        //public VehicleEmissions? Emissions { get; set; }

        public string? Trim { get; set; }

        public string? BaumusterCode { get; set; }

        public DateTime? StockDate { get; set; }

        public string? VsbNumber { get; set; }

        public string? DaysInStock { get; set; }

        public bool IsDeleted => this.Deleted.HasValue;

        public IDictionary<string, string> Identifiers { get; set; }
    }
}