﻿namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class QuotationEventBalances
    {
        public decimal CashDeposit { get; set; }
        
        public decimal DepositTotal { get; set; }
        
        public decimal VehiclesTotal { get; set; }
        
        public decimal OptionsTotal { get; set; }
        
        public decimal DeliveryTotal { get; set; }
        
        public decimal FactoryOptionsTotal { get; set; }
        
        public decimal AddedValueProductsTotal { get; set; }

        public decimal ExtrasTotal { get; set; }
        
        public decimal TotalTax { get; set; }
        
        public decimal TotalNet => this.TotalGross - this.TotalTax;
        
        public decimal OnTheRoadPrice { get; set; }
        
        public decimal BalanceToFinance { get; set; }
        
        public decimal BalanceToChange { get; set; }

        public decimal SupplementaryAddedValueProductsTotal { get; set; }

        public decimal SupplementaryExtrasTotal { get; set; }
        
        public decimal FinancableTotal { get; set; }
        
        public decimal TotalGross { get; set; }
        
        public decimal SupplementaryOptionsTotal { get; set; }
        
        public decimal TradeInsTotal { get; set; }
        
        public decimal CashBack { get; set; }
        
        public decimal PaymentsTotal { get; set; }

        public decimal VehicleDiscount { get; set; }
        public decimal OptionsDiscount { get; set; }
        public decimal TotalDiscount { get; set; }
    }
}
