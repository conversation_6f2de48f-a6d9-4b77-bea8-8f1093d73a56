using CPHI.Spark.Model.ViewModels.AutoPricing;

namespace CPHI.WebScraper.ViewModel
{
    public class SalesmasterVehicle
    {
        public string Reg { get; set; }
        public int Price { get; set; }
        public int RetailerSiteId { get; set; }
        public bool PriceChanged { get; set; } = false;
        
        public SalesmasterVehicle(PricingChangeNew pr)
        {
            Reg = pr.VehicleReg;
            Price = pr.NewPrice;
            RetailerSiteId = pr.RetailerSiteId;
        }
        
        // Constructor for test items
        public SalesmasterVehicle(string reg, int price, int retailerSiteId)
        {
            Reg = reg;
            Price = price;
            RetailerSiteId = retailerSiteId;
        }
    }
}
