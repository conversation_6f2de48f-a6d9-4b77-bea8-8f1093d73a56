import { Component, OnInit } from '@angular/core';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { DealDetailsComponent } from 'src/app/components/dealDetails/dealDetails.component';
import { ConstantsService } from 'src/app/services/constants.service';
import { CphPipe, FormatType } from '../../cph.pipe';
import { DateSelectionObject, OrderbookTimePeriod, SiteVM, Week } from '../../model/main.model';
import { Deal } from '../../model/sales.model';
import { PlotOption, DayAndDeals } from './dealsDoneThisWeek.model';
import { DealsDoneThisWeekService } from './dealsDoneThisWeek.service';


@Component({
  selector: 'app-dealsDoneThisWeek',
  templateUrl: './dealsDoneThisWeek.component.html',
  styleUrls: ['./dealsDoneThisWeek.component.scss']
})


export class DealsDoneThisWeekComponent implements OnInit {

  weeks: Array<Date>;
  plotOptions: Array<PlotOption>;

  constructor(
    public constants: ConstantsService,
    public service: DealsDoneThisWeekService,
    public cphPipe: CphPipe

  ) {

    this.plotOptions = [
      { label: this.constants.translatedText.MetalProfit, field: 'MetalProfit', numberType: 'currency', dps: 0 },
      { label: this.constants.translatedText.OtherProfit, field: 'OtherProfit', numberType: 'currency', dps: 0 },
      { label: this.constants.translatedText.FinanceProfit, field: 'FinanceProfit', numberType: 'currency', dps: 0 },
      { label: this.constants.translatedText.AddOnProfit, field: 'AddOnProfit', numberType: 'currency', dps: 0 },
      { label: this.constants.translatedText.TotalProfit, field: 'TotalNLProfit', numberType: 'currency', dps: 0 },
      { label: this.constants.translatedText.AddOnCount, field: 'TotalProductCount', numberType: 'number', dps: 0, },
      { label: this.constants.translatedText.DealsDoneThisWeek_FuelSale, field: 'FuelSale', numberType: 'currency', dps: 0 },
      
    ]

    

  }



  ngOnInit() {

    if(this.constants.environment.dealDetailModal_dealDetailsSection_showWebsiteDiscount){
      this.plotOptions.push({ label: this.constants.translatedText.DealDetails_WebsitePrice + ' '+this.constants.translatedText.Discount, field: 'DiscountToWebsite', numberType: 'currency', dps: 0 },)
    }

    this.service.initParams()
    this.service.getDeals();


  }



  makeBlobLabel(deal: Deal): string {
    let result = deal.Customer.substr(deal.Customer.indexOf(" ") + 1);
    return result.slice(0, 8);
  }

  dayButtonName(day: DayAndDeals): string {
    return '\r\n(' + this.service.constants.pluralise(this.service.constants.sum(day.deals.map(x => x.Units)), this.service.constants.translatedText.Deal, this.service.constants.translatedText.Deals) + ')'
  }

  zoom(zoomIn: boolean): void {

    this.service.days.forEach(d => {
      d.deals.forEach(deal => {
        let heightFromCentre = deal.height - 0.5
        let newHeight = zoomIn ? heightFromCentre * 1.7 : heightFromCentre / 1.7
        deal.height = (0.5 + newHeight)
      })
    })
  }

  changeWeek(object: DateSelectionObject, changeAmount: number): void {
    this.service.constants.changeWeek(object, changeAmount);
    this.service.getDeals();
  }

  //--------------------------------------
  //Template stuff
  //--------------------------------------

  // Go to the Orderbook for this particular day
  onDayButtonClick(day: DayAndDeals): void {

    this.service.orderBookService.initOrderbook();

    this.service.orderBookService.vehicleTypeTypes = this.service.vehicleTypeTypes;
    this.service.orderBookService.orderTypeTypes = this.service.orderTypeTypes;
    this.service.orderBookService.franchises = this.service.franchises;

    this.service.orderBookService.lateCostOption = this.service.lateCostOption;
    
    //set order dates
    this.service.orderBookService.orderDate.timePeriod = OrderbookTimePeriod.Day,
    this.service.orderBookService.orderDate.startDate = day.date;
    this.service.orderBookService.orderDate.lastChosenDay = day.date;
    this.service.orderBookService.orderDate.endDate = day.date;
    
    //set accounting dates
    this.service.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Anytime;
    this.service.orderBookService.accountingDate.startDate = this.constants.addYears(this.service.orderBookService.orderDate.startDate,-1);
    this.service.orderBookService.accountingDate.endDate = this.constants.addYears(this.constants.todayStart, 2);

    this.service.orderBookService.salesExecName = null;
    this.service.orderBookService.salesExecId = null;

    this.service.orderBookService.showOrderbook();
  }


  workoutDealHeights(field: string) {
    this.service.maxValue = 0
    this.service.minValue = 0
    this.service.days.forEach(day => {
      day.deals.forEach(deal => {
        if (deal[field] > this.service.maxValue) this.service.maxValue = deal[field] 
        if (deal[field] < this.service.minValue) this.service.minValue = deal[field] 
      })
    })

    let range = this.service.maxValue - this.service.minValue;
    this.service.days.forEach(day => {
      day.deals.forEach(deal => {
        deal.heightNew = this.constants.div((deal[field] - this.service.minValue), range)
        deal.subLabel = this.cphPipe.transform(deal[this.service.chosenPlotOption.field], this.service.chosenPlotOption.numberType as FormatType, this.service.chosenPlotOption.dps);

        //blob colours
        let percentageOfGreen = 0;
        let percentageOfRed = 0;
        deal.heightNew < 0.5 ?  percentageOfGreen = deal.heightNew * 2 : percentageOfGreen = 1 ;
        deal.heightNew < 0.5 ?  percentageOfRed = 1 : percentageOfRed = (1 - deal.heightNew) * 2 ;

        deal.green = percentageOfGreen * 255 + 0;
        deal.red = percentageOfRed * 255 + 0;

      })
    })

    //now apply the new heights
    this.service.days.forEach(day=>{
      day.deals.forEach(deal=>{
        deal.height = deal.heightNew
      })
    })

    
    this.service.showTable = true;

    this.service.orderBookService.showOrderbook();
  }

  onChangeWeekClick(changeAmount: number): void {
    this.service.chosenWeekStart = this.constants.addWeeks(this.service.chosenWeekStart,changeAmount)
    this.service.getDeals();
  }

  onZoomPlusClick() {
    this.zoom(true);
  }

  onZoomMinusClick() {
    this.zoom(false)
  }

  onDropdownWeekClick() {
    this.weeks = this.constants.makeWeeksSimple();
  }

  onWeekClick(weekStart: Date): void {
    this.service.chosenWeekStart = weekStart;
    this.service.getDeals();
  }


  onUpdateSites(sites: SiteVM[]): void {
    this.service.selections.selectedSites = sites;
    this.service.selections.selectedSitesIds = [];
    this.service.selections.selectedSites.forEach(site => {
      this.service.selections.selectedSitesIds.push(site.SiteId);
    });

    this.service.getDeals();
  }

  onUpdateOrderTypes(orderTypes: string[]): void {
    this.service.orderTypeTypes = orderTypes;
    this.service.getDeals();
  }

  onUpdateVehicleTypes(vehicleTypes: string[]): void {
    this.service.vehicleTypeTypes = vehicleTypes
    this.service.getDeals();
  }

  onUpdateFranchises(franchises: string[]): void {
    this.service.franchises = franchises
    this.service.getDeals();
  }

  onPlotOptionClick(plotOption: PlotOption): void {
    this.service.chosenPlotOption = plotOption;
    this.service.workoutDealHeights(plotOption.field);
  }

  // Open the modal for the deal
  onOpenDealModalClick(deal: Deal): void {

    let modalRef: NgbModalRef;

    if (this.constants.environment.dealDetailModal_componentName == 'DealDetailsRRGComponent') {
      modalRef = this.service.modalService.open(DealDetailsComponent);
    }

    if (this.constants.environment.dealDetailModal_componentName == 'DealDetailsComponent') {
      modalRef = this.service.modalService.open(DealDetailsComponent);
    }

    //I give to modal
    modalRef.componentInstance.givenDealId = deal.Id;

    modalRef.result.then((result) => { //I get back from modal
      if (result) {
        //thing
      }
    });
  }

  trackByFunction(index: number, deal: Deal): number {
    return deal.Id;
  }

}

