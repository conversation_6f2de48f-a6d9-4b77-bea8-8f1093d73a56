import { Component, HostListener, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DataOriginsUpdatesModalComponent } from 'src/app/components/dataOriginsUpdatesModal/dataOriginsUpdatesModal.component';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';

import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
import { DashboardDataItem, DashboardDataPack, DashboardDataParams } from './dashboard.model';
import { DashboardService } from './dashboard.service';



export interface DashboardSection{
  sectionName:string,
  translatedTextField:string,
  translatedTextValue:string,
  pageName:string,
  pages: DashboardPageNew[],
  enableSitesSelector:boolean
}


export interface DashboardPageNew {
  pageName:string,
  translatedTextField:string,
  translatedTextValue:string,
}


@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})


export class DashboardComponent implements OnInit {


  sections:DashboardSection[]


  @HostListener('window:resize', ['$event'])
  onResize(event) {
    this.checkIfNavOverlaps();
  }

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public service: DashboardService,
    public modalService: NgbModal

  ) {


  }


 


  ngOnDestroy() { }


  ngOnInit() {

    //launch control
    this.initParams()

  }

  initParams() {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText ? this.constants.translatedText.Loading : 'Loading'});
    this.sections = this.constants.environment.dashboard_sections

    // ESP
    //console.log(this.selections.user.permissions.salesOnly, "this.selections.user.permissions.salesOnly!")
    //console.log(this.selections.user.permissions.aftersalesOnly, "this.selections.user.permissions.aftersalesOnly!")
    //console.log(this.selections.user.RoleName, 'this.selections.user.RoleName!');

    // Below three statements are only for RRG Spain custom roles
    // Sales only
    if(this.selections.user.RoleName == 'VNVO Chief Sales')
    {
      this.sections =  this.sections.filter(x => x.sectionName != 'DashboardAftersalesSpain');
    }

    // Aftersales only
    if(this.selections.user.RoleName == 'Aftersales Chief')
    {
      this.sections =  this.sections.filter(x => x.sectionName == 'DashboardAftersalesSpain');
    }

    // if (this.selections.user.permissions.canReviewStockPrices) {
    //   this.constants.menu[2].push({ name: 'Stock Pricing', icon: "fas fa-barcode-read fa-fw", isWide: false, link: '/stockOverviewBySite', isActive: false });
    // }

    // If do not have claim, can't see data sets page
    if(!this.selections.user.permissions.canSeeDataSets && this.constants.environment.dashboard_showAftersalesDatasets)
    {
      let aftersalesIndex =  this.sections.findIndex(x => x.sectionName == 'DashboardAftersalesSpain');

      // For VN users, they won't have this section
      if(aftersalesIndex != -1)
      {
        this.sections[aftersalesIndex].pages = this.sections[aftersalesIndex].pages.filter(x => x.pageName != 'AftersalesDatasets')
      }
      
    }

    if(!this.service.chosenSites){
      this.service.chosenSites = this.constants.sitesActive
    }

    if(!this.service.chosenSection){this.chooseSection(this.sections[0])}

    this.service.orderTypeTypes = this.constants.clone(this.constants.orderTypeTypesNoTrade);

    if(!this.service.franchises){ this.service.franchises = this.constants.clone(this.constants.FranchiseCodes); }
    
    if(this.constants.environment.dashboard_canChooseMonth){
      this.service.months = this.constants.makeMonthsNewV3() 

      // If month not already initalised or previously selected
      if(!this.service.chosenMonthStart)
      {
        this.service.chosenMonthStart = this.constants.deductTimezoneOffset(new Date(this.constants.thisMonthStart))
        //this.service.chosenMonthStart = this.constants.addMonths(this.service.chosenMonthStart, -1);
      }
      
    }

    this.getDataService.getMostRecentDateInDailyOrders().subscribe((res: string) => {
      this.service.mostRecentDateInDailyOrders = new Date(res);
    })

  }

  checkIfNavOverlaps() {
    /*
      Gets the value in pixels of the far right of the main nav and far left of the notifications nav holder.
      When the pixel location of the notifications holder is less than the main nav, we know the 2 have overlapped.
      At this point, we switch to showing the dropdown version of the menu rather than row of buttons.
    */

    if (this.service.minScreenWidthForFullMenu && this.service.minScreenWidthForFullMenu > window.innerWidth) return;

    const navbar: HTMLElement = document.getElementById('dashboard-nav');
    const notificationsArea: HTMLElement = document.getElementById('notificationsArea');

    const navbarRight: number = navbar.getBoundingClientRect().right;
    const notificationsAreaLeft: number = notificationsArea.getBoundingClientRect().left;

    if (notificationsAreaLeft < navbarRight) {
      this.service.showMenuItemsAsDropdown = true;
      this.service.minScreenWidthForFullMenu = window.innerWidth;
    } else {
      this.service.showMenuItemsAsDropdown = false;
      this.service.minScreenWidthForFullMenu = null;
    }
  }


  public reportsLabel():string{

    let salesReports:string[]=['SalesRRG','SalesVindis','DashboardNewSpain','DashboardUsedSpain', 'Overview','DashboardOverviewSpain', 'DashboardAftersalesSpain']
    
    if(salesReports.includes(this.service.chosenSection?.sectionName)){
      return `${this.constants.translatedText.Sales} ${this.constants.translatedText.Reports}:`
    }

    if(this.service.chosenSection?.sectionName==='Aftersales'){
      return `${this.constants.translatedText.Aftersales} ${this.constants.translatedText.Reports}:`
    }
    
  }

  reportsDropdownLabel() {
    if (this.service.chosenPage.pageName.includes('dashboard')) return 'Choose report';
    return this.service.chosenPage.translatedTextValue;
  }
 
  public chooseSection(section: DashboardSection){
    //this.getDataService.getTodayNewUsedOrders();
    this.service.chosenSection = section;

    this.service.chosenPage = {
      pageName: section.pageName,
      translatedTextField: section.translatedTextField,
      translatedTextValue: section.translatedTextValue
    } ;

    if(this.service.chosenSection.sectionName != 'DashboardAftersalesSpain')
    {
      this.service.hideReportButtons = false;
    }

    this.service.singleLineNav = !['DashboardOverviewSpain','DashboardNewSpain', 'DashboardUsedSpain', 'DashboardAftersalesSpain'].includes(this.service.chosenSection.sectionName)

    setTimeout(() => {
      if (!this.service.showMenuItemsAsDropdown) {
        this.checkIfNavOverlaps();
      }
    }, 250)
  }

  public choosePage(page:DashboardPageNew){
    this.service.chosenPage = page;
    //this.getDataService.getTodayNewUsedOrders();
    this.service.singleLineNav = page.pageName.includes('dashboard');
  }



  // isMainDashboard(section:DashboardSection){
  //   const mainDashboards:string[]=['SalesRRGFleet','SalesRRG','AutoPricing', 'SalesVindis','DashboardServiceSpain', 'Aftersales','SpainDashboard', 'Overview',  'DashboardOverviewSpain', 'DashboardNewSpain', 'DashboardUsedSpain','DashboardInvoicedDealsSpain', 'DashboardAftersalesSpain', 'SiteCompare' ];
  //   return mainDashboards.includes(section.sectionName)
  // }

  onUpdateSites(sites: any){
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.service.chosenSites = sites;
    this.selections.selectedSites = sites;
    this.service.getNewDataTrigger.emit(null);

  }

  onUpdateOrderTypes(orderTypeTypes: string[]) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.service.orderTypeTypes = orderTypeTypes;
    this.service.getNewDataTrigger.emit(null);
  }

 
  openDataOriginsUpdatesModal() {
    let params: DashboardDataParams = {
      SiteIds:  this.service.chosenSites.map(x => x.SiteId).join(','),
      WeekStart: this.constants.startOfThisWeek(),
      DataItems: [DashboardDataItem.DataOriginsUpdates],
      Department: ''
    }

    this.getDataService.getDashboardData(params).subscribe((res: DashboardDataPack) => {
      let data = res.DataOriginsUpdates;
      data.map(x=>{
       if(x.LastUpdate){ x.LastUpdate =new Date(x.LastUpdate)}
      }
        )
      const modalRef = this.modalService.open(DataOriginsUpdatesModalComponent);
      modalRef.componentInstance.data = data;
    })
  }



  // overallHolderClasses():string{
  //   let result:string[]=[]
  //   result.push(this.constants.environment.customer)
  //   if(this.service.singleLineNav){
  //     result.push('single-line-nav')
  //   }else{
  //     result.push('multi-line-nav')
  //   }
  //   if(this.service.chosenPage.pageName==='FleetOrderbook'){
  //     result.push('fleetOrderbook')
  //   }
    
  //   return result.join(',');
  // }

}
