<div class="tileHeader ">

  <!-- The header -->
  <div class="headerWords">
    <h4>{{title}}</h4>
  </div>

  <div class="interactionIconsHolder" [ngClass]="{ 'stockDashboard': isStockInsightPage, 'leavingVehicle': isLeavingVehiclePage }">
    <!-- Icon that shows if you are filtering measures on this tile -->
    <div *ngIf="isFilterChosen()" [ngClass]="{'clickable':!supressFilterClick}" class="cancelFilterHolder" (click)="clearFilter()">
      <i class="fas fa-filter"></i>
    </div>

    <!-- Icon that shows if you are highlighting measures on this tile -->
    <div *ngIf="isHighlightsChosen()" [ngClass]="{'clickable':!supressFilterClick}" class="cancelHighlightsHolder" (click)="clearHighlights()">
      <i class="fas fa-filter"></i>
    </div>
  </div>
</div>

<div id="contentHolder" [ngClass]="{'scroll':tileType==='VerticalBar', 'stockDashboard': isStockInsightPage, 'leavingVehicle': isLeavingVehiclePage  }" #contentHolder>


  <!-- If we're showing as a BigNumber -->
  <ng-container *ngIf="tileType==='BigNumber'">
    <div class="contentsHolder h1">
      <div  *ngIf="tableRows.length > 0" id="bigNumber"  [ngClass]="{'clickable':!supressFilterClick}"  (click)="onBigNumberClick()" >
          {{tableRows[0].HighlightedTotal|cph:'number':0}}</div>

          <div *ngIf="tableRows.length == 0" id="bigNumber" [ngClass]="{'clickable':!supressFilterClick}"  (click)="onBigNumberClick()" >
    {{0|cph:'number':0}}</div>
  </div> 
  </ng-container>



  <!-- If we're showing as a Table -->
  <ng-container *ngIf="tileType==='Table'">
    <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions" (gridReady)="onGridReady($event)"
      [animateRows]="true">
    </ag-grid-angular>
  </ng-container>


  <!-- If we're showing as a VerticalBar -->
  <ng-container *ngIf="tileType==='VerticalBar' ">

    <div *ngIf="!isAutoTrader || isAutoTrader && title !== 'Performance Rating'" id="verticalBarsHolder" class="d-flex h-100">

      <div class="labels">
        <div class="gridFont labelHolder" title="{{row.Label}}" [ngStyle]="{'height.px': barHeight()}" [ngClass]="{'clickable':!supressFilterClick}"
        *ngFor="let row of tableRows; trackBy:trackByFunction">
          {{row.Label}}
      </div>
      </div>

      <div class="bars">

        <div class="gridFont barHolder" (click)="onBarClick(row)" [ngStyle]="{'height.px': barHeight()}" [ngClass]="{'clickable':!supressFilterClick}"
        *ngFor="let row of tableRows; trackBy:trackByFunction">

          <!-- The highlight bar -->
          <div  class="bar highlightBar" [ngStyle]="{'width.%':  constants.div(row.HighlightedTotal, maxValue) * 100}" >
          </div>

          <!-- The total bar.  -->
          <div *ngIf="row.HighlightedTotal !== row.FilteredTotal" class="bar filterBar" [ngStyle]="{'width.%': constants.div(row.FilteredTotal, maxValue) * 100}">  

          </div>
          
          <div class="barValue">
            <span >{{row.HighlightedTotal|cph:'number':0}}</span>
         </div>
        </div>
      </div>

    </div>

    <ng-container *ngIf="isAutoTrader && title === 'Performance Rating'">
      <autoTraderPerformanceRatingChart *ngIf="autoTraderPerformanceRatingChartParams" [params]="autoTraderPerformanceRatingChartParams" [isSmallTile]="true"></autoTraderPerformanceRatingChart>
    </ng-container>

  </ng-container>

  <!-- If we're showing as simple labels -->
  <ng-container *ngIf=" tileType==='SimpleLabels'">

    <div id="simpleLabelsHolder">
      <div class="label" (click)="onBarClick(row)"  [ngClass]="getSimpleLabelClass(row)"
        *ngFor="let row of tableRows; trackBy:trackByFunction">
          {{row.Label}} ({{row.HighlightedTotal|cph:'number':0}} )
        </div>
    </div>
    
  </ng-container> 

  <!-- If we're showing as a VerticalBar Percent -->
  <ng-container *ngIf=" tileType==='VerticalBarPercent'">

    <div id="verticalBarsHolder">

      <div class="barAndLabelHolder" [ngStyle]="{'height.px': barHeight()}" [ngClass]="{'clickable':!supressFilterClick}"
        *ngFor="let row of tableRows; trackBy:trackByFunction">

        <div class="gridFont labelHolder" title="{{row.Label}}" [ngStyle]="{'width.%':labelWidth}">
          {{row.Label}}
        </div>

        <div class="barHolder" (click)="onBarClick(row)"  [ngStyle]="{'width.%':100-labelWidth-10}">

          <!-- The total bar -->
          <div *ngIf="row.HighlightedTotal !== row.FilteredTotal" class="bar filterBar" [ngStyle]="{'width.%': constants.div(row.FilteredTotal, maxValue) * 100}">

          </div>

          <!-- The highlight bar -->
          <div  class="bar highlightBar narrow" [ngStyle]="{'width.%':  constants.div(row.HighlightedTotal, maxValue) * 100}" [ngClass]="{'narrow':row.HighlightedTotal !== row.FilteredTotal}" >

          </div>

          <div class="barValue">
            <span >{{row.HighlightedTotal|cph:'percent':1}}</span>
         </div>
         
        </div>
      </div>

    </div>

  </ng-container>

  

  <!-- If we're showing as a HorizontalBar -->
  <ng-container *ngIf="tileType==='HorizontalBar'">

    <div id="horizontalBarsHolder">

      <div class="barAndLabelHolder" [ngStyle]="{'width.px': barWidth()}" [ngClass]="{'clickable':!supressFilterClick}"
        *ngFor="let row of tableRows; trackBy:trackByFunction">



        <div class="barHolder" [style.height]="horizontalBarHolderHeight()" (click)="onBarClick(row)">

          <!-- The total bar -->
          <div class="bar filterBar" [ngStyle]="{'height.%': constants.div(row.FilteredTotal, maxValue) * 100}">

          </div>

          <!-- The highlight bar -->
          <div class="bar highlightBar" [ngStyle]="{'height.%':  constants.div(row.HighlightedTotal, maxValue) * 100}">

            <div class="barValue">
              {{row.HighlightedTotal|cph:'number':0}}
            </div>
          </div>
        </div>

        <!-- Label at bottom -->
        <div class="labelHolder gridFont" title="{{row.Label}}" [ngStyle]="{'height.px': getLabelHeight()}" [ngClass]="{ 'breakWord': title === 'Model'}">
          {{ this.truncateLabel(row.Label) }}
        </div>
      </div>

    </div>

  </ng-container>


  <!-- If we're showing as a donut chart -->
  <ng-container *ngIf="tileType==='DonutChart'">
    <div id="donutHolder">
    <donut [values]="tableRowValues()" [labels]="tableRowLabels()" [refreshTrigger]="chartRefreshEmitter" [isAutoTrader]="true"></donut>
  </div>

    <!-- just slam table in as well for now until work out how to show donut labels -->
    <div id="tableContainer">
      <table>
        <thead>
          <tr>
            <th></th>
            <th class="gridFont">Total</th>
            <th class="gridFont">Highlighted</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of tableRows">
            <td>{{ row.Label }}</td>
            <td class="align-right">{{ getFilterPercentage(row) | cph:'percent':0 }}</td>
            <td class="align-right">{{ getHighlightPercentage(row) | cph:'percent':0 }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- <ag-grid-angular class="withDonut ag-theme-balham" [gridOptions]="mainTableGridOptions" (gridReady)="onGridReady($event)"
      [animateRows]="true">
    </ag-grid-angular> -->

  </ng-container>

  <!-- If we're showing as a donut chart (Flex style) -->
  <ng-container *ngIf="tileType == 'DonutChartFlex'">
    <div id="donutAndGridFlexContainer">
      <div id="donutContainer">
        <donut [values]="tableRowValues()" [labels]="tableRowLabels()" [refreshTrigger]="chartRefreshEmitter"></donut>
      </div>

      <div id="tableContainer">
        <table>
          <thead>
            <tr>
              <th></th>
              <th>Total</th>
              <th>Highlighted</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of tableRows">
              <td>{{ row.Label }}</td>
              <td class="align-right">{{ getFilterPercentage(row) }}</td>
              <td class="align-right">{{ getHighlightPercentage(row) }}</td>
            </tr>
          </tbody>
        </table>
        <!-- <ag-grid-angular class="withDonut ag-theme-balham" [gridOptions]="mainTableGridOptions" (gridReady)="onGridReady($event)"
          [animateRows]="true">
        </ag-grid-angular> -->
      </div>
    </div>
  </ng-container>






</div>