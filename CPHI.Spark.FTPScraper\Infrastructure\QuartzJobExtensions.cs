﻿using CPHI.Spark.FTPScraper.Jobs.Spark.RRG;
using CPHI.Spark.FTPScraper.Jobs.Spark.RRGSpain;
using CPHI.Spark.FTPScraper.Jobs.Spark.Vindis;
using CPHI.Spark.FTPScraper.Jobs.Stockpulse.Jardine;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using Quartz.Util;
using System;

namespace CPHI.Spark.FTPScraper.Infrastructure
{
    public static class QuartzJobExtensions
    {
        public static void AddQuartzJobs(this IServiceCollection services, IConfiguration config)
        {
            services.AddQuartz(q => {
                q.UseMicrosoftDependencyInjectionJobFactory();
                
                //Spark
                   //RRG
                q.AddJobAndTrigger<SparkRRGCphiSiteFetch>(config);
                q.AddJobAndTrigger<SparkRRGEmacSiteFetch>(config);

                //  //RRGSpain
                q.AddJobAndTrigger<SparkRRGSpainCphiSiteFetch>(config);

                //  //Vindis
                q.AddJobAndTrigger<SparkVindisCphiSiteFetch>(config);

                //Stockpulse
                //Jardine
                q.AddJobAndTrigger<StockpulseJardineFetch>(config);

                // MMG
                q.AddJobAndTrigger<StockpulseMMGFetch>(config);

                // Lithia
                q.AddJobAndTrigger<StockpulseLithiaFetch>(config);

                // Cphi Generic - New Ftp
                q.AddJobAndTrigger<Spark_devVM_Fetch>(config);

                //Cleanup
                q.AddJobAndTrigger<CleanupJob>(config);
            });

            services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);
        }

        public static void AddJobAndTrigger<T>(this IServiceCollectionQuartzConfigurator quartz, IConfiguration config) where T : IJob
        {
            string jobName = typeof(T).Name;

            var configKey = $"Quartz:{jobName}";
            string cronSchedule = config[configKey];
            string overrideRunJobName = config["FtpScraperSettings:overrideRunJobNow"];

            if (!overrideRunJobName.IsNullOrWhiteSpace())
            {
                if (jobName.Equals(overrideRunJobName, StringComparison.OrdinalIgnoreCase))
                {
                    DateTime startTime = DateTime.Now.AddSeconds(5);
                    cronSchedule = $"{startTime.Second} {startTime.Minute} {startTime.Hour} * * ?";
                }
                else
                {
                    //All other job will come here, do nothing
                    cronSchedule = "0 0 0 1 1 ? 2099";
                }
            }
           

            var jobKey = jobName;
            quartz.AddJob<T>(opts => opts.WithIdentity(jobKey));

            quartz.AddTrigger(opts => opts
                .ForJob(jobKey)
                .WithIdentity(jobName + "-trigger")
                .WithCronSchedule(cronSchedule));
        }
        
    }
}
