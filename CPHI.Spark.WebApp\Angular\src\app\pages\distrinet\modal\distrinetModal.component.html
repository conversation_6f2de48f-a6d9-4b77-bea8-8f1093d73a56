<ng-template #distrinetModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title">
            {{ data.Franchise }} {{ data.Model }} {{ data.Version }} - {{ data.Customer }}
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div [ngClass]="constants.environment.customer" class="modal-body">
        <div class="cards-container">
            <!-- Left side -->
            <div class="left-side">
                <!-- Deal details -->
                <div class="section-card">
                    <div class="card-header">
                        {{constants.translatedText.DealDetails_DealDetails}}
                    </div>
                    <div class="card-body">
                        <table id="deal-details-table">
                            <tbody>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_SiteDescription }}</td>
                                    <td class="value">{{ data.SiteDescription }}</td>
                                    <td>{{ constants.translatedText.Distrinet_SiteDMSId }}</td>
                                    <td class="value">{{ data.SiteId }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_SalesmanName }}</td>
                                    <td class="value">{{ data.SalesmanName }}</td>
                                    <td>{{ constants.translatedText.Distrinet_SalesmanCode }}</td>
                                    <td class="value">{{ data.SalesmanCode }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_Customer }}</td>
                                    <td class="value">{{ data.Customer }}</td>
                                    <td>{{ constants.translatedText.Distrinet_CustomerType }}</td>
                                    <td class="value">{{ data.CustomerType }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_OriginType }}</td>
                                    <td class="value">{{ data.OriginType }}</td>
                                    <td>{{ constants.translatedText.Distrinet_RequestNumber }}</td>
                                    <td class="value">{{ data.RequestNumber }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_BillingStatus }}</td>
                                    <td class="value">{{ data.BillingStatus }}</td>
                                    <td>{{ constants.translatedText.Distrinet_OrderStatus }}</td>
                                    <td class="value">{{ data.OrderStatus }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_SubPropAccount }}</td>
                                    <td class="value">{{ data.SubPropAccount }}</td>
                                    <td>{{ constants.translatedText.Distrinet_RecipientAccount }}</td>
                                    <td class="value">{{ data.RecipientAccount }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_Flexibility }}</td>
                                    <td class="value">{{ data.Flexibility }}</td>
                                    <td>{{ constants.translatedText.Distrinet_RemainingFranchise }}</td>
                                    <td class="value">{{ data.RemainingFranchise }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_MADACommitment }}</td>
                                    <td class="value">{{ data.MADACommitment }}</td>
                                    <td>{{ constants.translatedText.Distrinet_DRType }}</td>
                                    <td class="value">{{ data.DRType }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_CommunityReceptionNo }}</td>
                                    <td class="value">{{ data.CommunityReceptionNo }}</td>
                                    <td>{{ constants.translatedText.Distrinet_Countermark }}</td>
                                    <td class="value">{{ data.Countermark }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_OrderNumber }}</td>
                                    <td class="value">{{ data.OrderNumber }}</td>
                                    <td>{{ constants.translatedText.Distrinet_OrdenNumber }}</td>
                                    <td class="value">{{ data.OrdenNumber }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_PgeoBCV }}</td>
                                    <td class="value">{{ data.PgeoBCV }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Vehicle details -->
                <div class="section-card">
                    <div class="card-header">
                        {{constants.translatedText.DealDetails_VehicleDetails}}
                    </div>
                    <div class="card-body">
                        <table id="vehicle-details-table">
                            <tbody>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_Franchise }}</td>
                                    <td class="value">{{ data.Franchise }}</td>
                                    <td>{{ constants.translatedText.Distrinet_Model }}</td>
                                    <td class="value">{{ data.Model }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_ModelCode }}</td>
                                    <td class="value">{{ data.ModelCode }}</td>
                                    <td>{{ constants.translatedText.Distrinet_Version }}</td>
                                    <td class="value">{{ data.Version }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_VersionCode }}</td>
                                    <td class="value">{{ data.VersionCode }}</td>
                                    <td>{{ constants.translatedText.Distrinet_Chassis }}</td>
                                    <td class="value">{{ data.Chassis }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_EngineNumber }}</td>
                                    <td class="value">{{ data.EngineNumber }}</td>
                                    <td>{{ constants.translatedText.Distrinet_OptionsCode }}</td>
                                    <td class="value">{{ data.OptionsCode }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_Paint }}</td>
                                    <td class="value">{{ data.Paint }}</td>
                                    <td>{{ constants.translatedText.Distrinet_Colour }}</td>
                                    <td class="value">{{ data.Colour }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_Interior }}</td>
                                    <td class="value">{{ data.Interior }}</td>
                                    <td>{{ constants.translatedText.Distrinet_Interior1 }}</td>
                                    <td class="value">{{ data.Interior1 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_Harmony }}</td>
                                    <td class="value">{{ data.Harmony }}</td>
                                    <td>{{ constants.translatedText.Distrinet_Harmony1 }}</td>
                                    <td class="value">{{ data.Harmony1 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_Acquisition }}</td>
                                    <td class="value">{{ data.Acquisition }}</td>
                                    <td>{{ constants.translatedText.Distrinet_Antiquity }}</td>
                                    <td class="value">{{ data.Antiquity }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_Options }}</td>
                                    <td class="value">{{ data.Options }}</td>
                                    <td>{{ constants.translatedText.Distrinet_OptionsCode }}</td>
                                    <td class="value">{{ data.OptionsCode }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_EnergyType }}</td>
                                    <td class="value">{{ data.EnergyType }}</td>
                                    <td>{{ constants.translatedText.Distrinet_MountedAccessories }}</td>
                                    <td class="value">{{ data.MountedAccessories }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Right side -->
            <div class="right-side">
                <!-- Dates -->
                <div class="section-card">
                    <div class="card-header">
                        {{constants.translatedText.Dates}}
                    </div>
                    <div class="card-body">
                        <table id="dates-table">
                            <tbody>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_DateOfOrderCreation }}</td>
                                    <td class="value">{{ data.DateOfOrderCreation | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_CustomerDeliveryControlDate }}</td>
                                    <td class="value">{{ data.CustomerDeliveryControlDate | cph:'date':0 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_AgreedCustomerDeliveryDate }}</td>
                                    <td class="value">{{ data.AgreedCustomerDeliveryDate | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_ActualDeliveryDate }}</td>
                                    <td class="value">{{ data.ActualDeliveryDate | cph:'date':0 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_InvoiceDate }}</td>
                                    <td class="value">{{ data.InvoiceDate | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_ReleaseDate }}</td>
                                    <td class="value">{{ data.ReleaseDate | cph:'date':0 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_EntryDateCI }}</td>
                                    <td class="value">{{ data.EntryDateCI | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_DepartureDateCI }}</td>
                                    <td class="value">{{ data.DepartureDateCI | cph:'date':0 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_OrangeFlexEndDate }}</td>
                                    <td class="value">{{ data.OrangeFlexEndDate | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_GreenFlexEndDate }}</td>
                                    <td class="value">{{ data.GreenFlexEndDate | cph:'date':0 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_DADDate }}</td>
                                    <td class="value">{{ data.DADDate | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_TestDate }}</td>
                                    <td class="value">{{ data.TestDate | cph:'date':0 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_ActualAffectationDate }}</td>
                                    <td class="value">{{ data.ActualAffectationDate | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_ActualAnnulmentDate }}</td>
                                    <td class="value">{{ data.ActualAnnulmentDate | cph:'date':0 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_ActualARADate }}</td>
                                    <td class="value">{{ data.ActualARADate | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_ActualAvailableDate }}</td>
                                    <td class="value">{{ data.ActualAvailableDate | cph:'date':0 }}</td>
                                </tr>

                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_StockDate }}</td>
                                    <td class="value">{{ data.StockDate | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_PurseDate }}</td>
                                    <td class="value">{{ data.PurseDate | cph:'date':0 }}</td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_PredictedMADC }}</td>
                                    <td class="value">{{ data.PredictedMADC | cph:'date':0 }}</td>
                                    <td>{{ constants.translatedText.Distrinet_ActualMADC }}</td>
                                    <td class="value">{{ data.ActualMADC | cph:'date':0 }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Lock status -->
                <div class="section-card">
                    <div class="card-header">
                        {{constants.translatedText.DealDetails_LockStatus}}
                    </div>
                    <div class="card-body">
                        <table id="dates-table">
                            <tbody>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_QualityLocked }}</td>
                                    <td class="value">
                                        <i
                                            [ngClass]="data.QualityLocked ? 'fad fa-lock red' : 'fad fa-unlock green'"></i>
                                    </td>
                                    <td>{{ constants.translatedText.Distrinet_TradeLocked }}</td>
                                    <td class="value">
                                        <i [ngClass]="data.TradeLocked ? 'fad fa-lock red' : 'fad fa-unlock green'"></i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_FinancialLocked }}</td>
                                    <td class="value">
                                        <i
                                            [ngClass]="data.FinancialLocked ? 'fad fa-lock red' : 'fad fa-unlock green'"></i>
                                    </td>
                                    <td>{{ constants.translatedText.Distrinet_TransportLocked }}</td>
                                    <td class="value">
                                        <i
                                            [ngClass]="data.TransportLocked ? 'fad fa-lock red' : 'fad fa-unlock green'"></i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{ constants.translatedText.Distrinet_TransportLockReason }}</td>
                                    <td class="value">{{ data.TransportLockReason }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">
            {{ constants.translatedText.Close }}
        </button>
    </div>
</ng-template>