<div class="dashboard-tile-inner">
  <div class="tileHeader">
    <div class="headerWords">  
      <h4>{{ cardTitle }}</h4>
      <div *ngIf="dataSource" class="right-aligned">
        <div *ngIf="showWarning" class="warning"></div>
        <div class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
      </div>
  </div>
</div>

<div class="numberHolder">
  <table>
    <tbody>
      <tr class="numberRow">
        <td>
          <h2 id="overageCarCountToday" class="spaceBetween column clickable" (click)="goToPageOnClick('now')">{{overageCarsToday|cph:'number':0}}</h2>
        </td>
        <td>
          <h2 *ngIf="!constants.environment.stockOverageTile_excludeEOM" id="overageCarCountEOM" class="spaceBetween column clickable" (click)="goToPageOnClick('monthEnd')">{{overageCarsEOM|cph: 'number' :0}}</h2>
        </td>
      </tr>

      <tr class="labelRow">
        <td>
          <div id="overageCarLabel" class="spaceBetween column ">{{constants.translatedText.Today}}</div>
        </td>
        <td>
          <div *ngIf="!constants.environment.stockOverageTile_excludeEOM" id="overageCarLabel" class="spaceBetween column">{{constants.thisMonthEnd|cph: 'shortDate':0}}</div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
</div>