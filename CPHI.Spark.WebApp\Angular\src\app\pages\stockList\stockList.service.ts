import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { StockList } from './stockList.model';
import { CphPipe } from 'src/app/cph.pipe';

@Injectable({
  providedIn: 'root'
})

export class StockListService {
  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public modalService: NgbModal,
    public excel: ExcelExportService,
    public dataMethods: GetDataMethodsService,
    public cphPipe: CphPipe
  ) { }

  stockList: StockList;
  waitUntil: Date;

  initiateStockList() {
    if (!this.stockList) {
      this.stockList = {
        sites: this.constants.clone(this.selections.userSites),
        sitesIds: this.constants.clone(this.selections.userSiteIds),
        displayedRowsLength: 0,
        stocks: [],
        reportName: null,
        showGrid: false,
        storedGridState: {
          cols: null,
          filters: null,
          sorts: null,
          siteIds: null,
          highLevelFilters: {
            vehicleTypes:  this.constants.VehicleTypes.map(x => x.Type) ,
            models: [],
            franchises: this.constants.environment.stockList.franchises,
          }
        },
        filterModel: null
      }
    }
  }


  
}
