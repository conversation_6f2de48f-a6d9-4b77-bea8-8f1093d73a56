<nav class="navbar">
  <nav  class="generic">
    <h4 class="title">{{ constants.translatedText.Dashboard_SalesActivity }}</h4>
    
    <ng-container *ngIf="service.salesActivity">
      <div class="buttonGroup topDropdownButtons">
        <!-- Date selector -->
        <datePickerMultiSelect
          [monthsFromParent]="service.salesActivity.months"
          (selectMonths)="updateMonths($event)"
          [includeYTD]="false"
          [includeLastYear]="false"
          [includeThisYear]="false"
        >
        </datePickerMultiSelect>
        <!-- Vehicle type selector -->
        <pickerSimple
          [pickerItemsFromParent]="service.salesActivity.vehicleTypes"
          [pickerLabel]="'vehicle types'"
          (selectedPickerItems)="updateVehicleTypes($event)"
        >
        </pickerSimple>
      </div>
    </ng-container>
  </nav>
</nav>

<div class="content-new">
  <div *ngIf="service.salesActivity" class="content-inner-new">
    <button *ngIf="service.salesActivity.peopleData && service.salesActivity.salesManagerId == null" class="btn btn-primary backButton" (click)="backToSites()">
      <i class="fas fa-undo"></i>
    </button>
    <button *ngIf="service.salesActivity.peopleData && constants.environment.customer == 'Vindis' && service.salesActivity.salesManagerId != null" class="btn btn-primary backButton" (click)="backToManagers()">
      <i class="fas fa-undo"></i>
    </button>

    <ng-container *ngIf="service.salesActivity.sitesData && !service.salesActivity.peopleData">
      <salesActivityTable [sites]="true" (clickedSite)="selectSite($event)" id="ActivitiesTable"></salesActivityTable>
      <div class="tableSpacer"></div>
      <salesActivityTable [sites]="true" [regional]="true" (clickedSite)="selectSite($event)"></salesActivityTable>
    </ng-container>
    
    <salesActivityTable *ngIf="service.salesActivity.peopleData && constants.environment.customer != 'Vindis'"></salesActivityTable>
    <salesActivityTable [managers]="service.salesActivity.salesManagerId == null" (clickedSite)="selectManager($event)" *ngIf="service.salesActivity.peopleData && constants.environment.customer == 'Vindis'"></salesActivityTable>
  </div>
</div>