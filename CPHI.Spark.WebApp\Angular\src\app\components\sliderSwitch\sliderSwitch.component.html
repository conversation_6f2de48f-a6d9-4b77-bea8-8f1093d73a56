<!-- [ngClass]="{ 'active': defaultValue && !supressActiveColour  }"  -->
<button class="btn btn-primary buttonWithSlider" (click)="toggleDefaultValue()" [disabled]="disabled"
    [class]="styleClass"
    [ngStyle]="{ 'width.px': width || null }">

    <span *ngIf="text" [ngStyle]="{ 'color': blackFont ? 'black' : null }" class="sliderText">{{ text }}</span>
    <div class="switch">
        <div class="toggle" [ngClass]="{ 'active': defaultValue }"></div>
        <div class="slider"></div>
    </div>
   <span *ngIf="rightText" [ngStyle]="{ 'color': blackFont ? 'black' : null }" class="sliderTextRight">{{ rightText }}</span>
</button>
