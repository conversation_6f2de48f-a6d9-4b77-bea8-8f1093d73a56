<!-- [ngClass]="{ 'active': defaultValue && !supressActiveColour  }"  -->
<button class="btn btn-primary buttonWithSlider {{ styleClass }}" (click)="toggleDefaultValue()" [disabled]="disabled"
    [ngStyle]="{ 'width.px': width || null }">
    
    <span *ngIf="text" [ngStyle]="{ 'color': blackFont ? 'black' : null }" class="sliderText">{{ text }}</span>
    <div class="switch">
        <div class="toggle" [ngClass]="{ 'active': defaultValue }"></div>
        <div class="slider"></div>
    </div>
</button>
