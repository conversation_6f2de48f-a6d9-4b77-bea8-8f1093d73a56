
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CphPipe } from 'src/app/cph.pipe';
import { MenuItemNew, OrderbookTimePeriod } from 'src/app/model/main.model';
import { LateCostOption, OrderOption, SalesmanStat } from 'src/app/model/sales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { OrderbookRow } from "../../model/OrderbookRow";
import { OrderbookSummaryTable } from "../../model/OrderbookSummaryTable";
import { GridApi, ColumnApi } from 'ag-grid-community';
import { OrderBookNewComponent } from './orderBookNew.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { TableLayoutManagementParams } from 'src/app/model/TableLayoutManagementParams';
import { TableLayoutManagementService } from 'src/app/components/tableLayoutManagement/tableLayoutManagement.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { UntypedFormControl } from '@angular/forms';
import { OrderBookNewTableComponent } from './table/orderBookNewTable.component';







@Injectable({
  providedIn: 'root'
})
export class OrderBookNewService {
  searchTerm: UntypedFormControl;
  //sites: SiteVM[];
  vehicleTypeTypes: string[];
  orderTypeTypes: string[];
  franchises: string[];
  lateCostOption: LateCostOption;
  orderOption: OrderOption;
  deliveryOption: string;
  salesExecName: string;
  salesExecId: number;
  salesExecSummary: SalesmanStat[];
  salesManagerName: string;
  salesManagerId: number;
  salesManagerSummary: SalesmanStat[];
  rowsFiltered: OrderbookRow[];

  orderDate: {
    startDate: Date,
    endDate: Date,
    timePeriod: OrderbookTimePeriod,

    lastChosenMonthStart: Date,
    lastChosenWeekStart: Date,
    lastChosenDay: Date,

  };

  accountingDate: {
    dateType: string,
    startDate: Date,
    endDate: Date,
    //type:string,
    timePeriod: OrderbookTimePeriod
    lastChosenMonthName: string,
    lastChosenMonthStart: Date,
    lastChosenDay: Date,
  };




  summaryTable: OrderbookSummaryTable

  chosenDealIds: number[];

  specialFiltersMenuItemChosen: string = null;

  //gridApi: GridApi;
  //gridColumnApi: ColumnApi;
  rows: OrderbookRow[];
  orderBookComponent: OrderBookNewComponent;
  tableComponent: OrderBookNewTableComponent;
  
  tableLayoutManagementParams: TableLayoutManagementParams = new TableLayoutManagementParams('orderbook')
  topSectionHeight: number;
  navbarHeight: number;

  constructor(
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public router: Router,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public agGridMethodsService: AGGridMethodsService,
    public getDataMethods: GetDataMethodsService,
    public columnTypesService:ColumnTypesService,
    public tableLayoutManagementService: TableLayoutManagementService,
    
    
  ) {
  }


 

  showOrderbook() {
    // Go to Orderbook
    let navLocn: string = this.constants.environment.orderBookURL;
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl(navLocn);
    if (menuItem) { this.constants.navigateByUrl(menuItem); }  //, 'operationreports'
  }


  initOrderbook() {

    //this.sites = this.constants.sitesActiveSales.filter(x => x.IsEligibleForUser);
    this.vehicleTypeTypes = this.constants.clone(this.constants.vehicleTypeTypes);
    this.orderTypeTypes = this.constants.clone(this.constants.orderTypeTypesNoTrade);
    this.franchises = this.constants.clone(this.constants.FranchiseCodes);
    
    if(this.constants.lateCostOptions){
      this.lateCostOption = this.constants.lateCostOptions[1];
    }
      
    this.orderOption = this.constants.orderOptions[0];

    this.accountingDate = {
      dateType: this.constants.environment.orderBook.defaultDateType,
      startDate: this.constants.startOfMonth(new Date()),
      endDate: this.constants.endOfMonth(new Date()),
      timePeriod: OrderbookTimePeriod.Month,
      lastChosenMonthStart: this.constants.startOfMonth(new Date()),
      lastChosenMonthName: this.cphPipe.transform(this.constants.startOfMonth(new Date()), "month", 0),
      lastChosenDay: this.constants.todayStart,
    };

    this.orderDate = {
      startDate: this.constants.addYears(this.constants.todayStart, -4),
      endDate: this.constants.endOfMonth(new Date()),
      timePeriod: OrderbookTimePeriod.Anytime,
      lastChosenMonthStart: this.constants.startOfMonth(new Date()),
      lastChosenWeekStart: this.constants.startOfWeek(new Date()),
      lastChosenDay: this.constants.todayStart
    };

    this.deliveryOption = 'All';






    this.summaryTable = {
      totals: {
        units: 0,
        metalProfit: 0,
        otherProfit: 0,
        financeProfit: 0,
        insuranceProfit: 0,
        totalNLProfit: 0,
      },
      perUnits: {
        units: 0,
        metalProfit: 0,
        otherProfit: 0,
        financeProfit: 0,
        insuranceProfit: 0,
        totalNLProfit: 0,
      }
    }

  }

dealWithNewRowData(rows: OrderbookRow[]): void {
    this.tableLayoutManagementParams.gridApi.setRowData(rows);
    this.summariseDeals();
  }



  filterForUserChoices(res: OrderbookRow[]): OrderbookRow[] {

    let results = res;
    //walk through the various choices filtering if required.

    // Filter for deal Ids
    if (this.chosenDealIds) {
      return results.filter(x => this.chosenDealIds.includes(x.DealId));
    }



    if(this.deliveryOption !=='All'){
      results = results.filter(x=>x.IsDelivered === (this.deliveryOption==='Delivered'))
    }

    //Vehicle Types
    if (this.vehicleTypeTypes.length !== this.constants.vehicleTypeTypes.length) {
      let vehicleTypeCodes = this.constants.VehicleTypes.filter(x => this.vehicleTypeTypes.includes(x.Type)).map(x => x.Code)
      results = results.filter(x => vehicleTypeCodes.includes(x.VehicleTypeCode))
    }

    //Order Types
    if (this.orderTypeTypes.length !== this.constants.OrderTypes.length) {
      let orderTypeCodes = this.constants.OrderTypes.filter(x => this.orderTypeTypes.includes(x.Type)).map(x => x.Code)
      results = results.filter(x => orderTypeCodes.includes(x.OrderTypeCode))
    }

    //Franchises
    if (this.franchises.length !== this.constants.FranchiseCodes.length) {
      results = results.filter(x => this.franchises.includes(x.Franchise));
    }

    if (this.specialFiltersMenuItemChosen == 'highMargin') { results = results.filter(x => x.TotalProfit > 2000) }
    if (this.specialFiltersMenuItemChosen == 'losingMoney') { results = results.filter(x => x.TotalProfit < 0) }
    if (this.specialFiltersMenuItemChosen == 'soldByNonExec') { results = results.filter(x => x.SoldBySalesExec == false) }


    return results;

  }



  selectAnytimeForOrders() {
    this.orderDate.timePeriod = OrderbookTimePeriod.Anytime;
  }



  selectAnytimeForDelivery() {
    this.accountingDate.timePeriod = OrderbookTimePeriod.Anytime;
  }

  selectOrderDay(date: Date) {
    this.orderDate.timePeriod = OrderbookTimePeriod.Day
    this.orderDate.startDate = date;
    this.orderDate.endDate = date;
    this.orderDate.lastChosenDay = date;
  }

  selectDeliveryDay(date: Date) {
    this.accountingDate.timePeriod = OrderbookTimePeriod.Day
    this.accountingDate.startDate = date;
    this.accountingDate.endDate = date;
    this.accountingDate.lastChosenDay = date;
  }

  summariseDeals(): void {

    let deals: OrderbookRow[] = [];
    if (!this.tableLayoutManagementParams.gridApi) return;

    this.tableLayoutManagementParams.gridApi.forEachNodeAfterFilter(x => deals.push(x.data));
    this.summaryTable.totals.units = deals.length;
    let count = this.constants.sum(deals.map(e => e.Units));

    this.summaryTable.totals.units = count;
    this.summaryTable.totals.metalProfit = this.constants.sum(deals.map(e => e.MetalProfit))
    this.summaryTable.totals.otherProfit = this.constants.sum(deals.map(e => e.OtherProfit))
    this.summaryTable.totals.insuranceProfit = this.constants.sum(deals.map(e => e.AddOnProfit))
    this.summaryTable.totals.financeProfit = this.constants.sum(deals.map(e => e.FinanceProfit))
    this.summaryTable.totals.totalNLProfit = this.constants.sum(deals.map(e => e.TotalProfit))

    this.summaryTable.perUnits.metalProfit = this.constants.div(this.constants.sum(deals.map(e => e.MetalProfit)), count)
    this.summaryTable.perUnits.otherProfit = this.constants.div(this.constants.sum(deals.map(e => e.OtherProfit)), count)
    this.summaryTable.perUnits.insuranceProfit = this.constants.div(this.constants.sum(deals.map(e => e.AddOnProfit)), count)
    this.summaryTable.perUnits.financeProfit = this.constants.div(this.constants.sum(deals.map(e => e.FinanceProfit)), count)
    this.summaryTable.perUnits.totalNLProfit = this.constants.div(this.constants.sum(deals.map(e => e.TotalProfit)), count)

  }




}




