import { Component, OnInit } from '@angular/core';
import { SelectionsService } from 'src/app/services/selections.service';
import { PricingDashboardService } from './pricingDashboard.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { CellClassParams, ColDef, ColumnApi, GridApi, GridOptions, RowClickedEvent, ValueGetterParams } from 'ag-grid-community';
import { RegPlateComponent } from 'src/app/_cellRenderers/regPlate.component';
import { TitleCasePipe } from '@angular/common';
import { CphPipe } from 'src/app/cph.pipe';
import { Subscription } from 'rxjs';
import { AdvertListingDetailService } from '../advertListingDetail/advertListingDetail.service';
import { Router } from '@angular/router';
import { VehicleAdvertWithRating } from 'src/app/model/VehicleAdvertWithRating';
import { LeavingPriceBasicItem } from 'src/app/model/LeavingPriceBasicItem';
import { OffStrategyVehicleSummaryItem } from 'src/app/model/OffStrategyVehicleSummaryItem';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AutoPriceInsightsModalComponent } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import { RetailerSiteVM } from "src/app/model/RetailerSiteVM";
import { AutoTraderAdvertImage } from 'src/app/_cellRenderers/autoTraderAdvertImage.component';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { CustomHeaderService } from 'src/app/components/customHeader/customHeader.service';
import { AutopriceRendererService } from 'src/app/services/autopriceRenderer.service';
import { AutoPriceInsightsModalService } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service';
import { MenuItemNew } from 'src/app/model/main.model';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { AutotraderImageCellComponent } from 'src/app/components/autotraderImageCell/autotraderImageCell.component';

@Component({
  selector: 'app-pricingDashboard',
  templateUrl: './pricingDashboard.component.html',
  styleUrls: ['./pricingDashboard.component.scss']
})
export class PricingDashboardComponent implements OnInit {
  public components: { [p: string]: any; } = {
    agColumnHeader: CustomHeaderNew,
  };

  searchesBarColour: string = '#FFBF00';
  viewsBarColour: string = '#E6AC00';
  soldBarColour: string = '#0534FF';

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: PricingDashboardService,
    public cphPipe: CphPipe,
    public titleCasePipe: TitleCasePipe,
    public advertListingDetailService: AdvertListingDetailService,
    public router: Router,
    private modalService: NgbModal,
    private customHeader: CustomHeaderService,
    private autopriceRendererService: AutopriceRendererService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService,
    private gridHelpersService: AGGridMethodsService
  ) { }

  ngOnInit(): void {
    

    this.initParams();
    this.service.getData().subscribe(res => {


      this.service.recentlySold = {
        gridApi: new GridApi,
        gridColumnApi: new ColumnApi,
        gridHelpers: {},
        gridOptions: {}
      }

      this.service.vAbovevBelow = {
        gridApi: new GridApi,
        gridColumnApi: new ColumnApi,
        gridHelpers: {},
        gridOptions: {}
      }


      this.initialiseRecentlySoldGrid()
      this.initialiseVeryAboveVeryBelowGrid();
      this.selections.triggerSpinner.next({ show: false });
    }, e => {
      this.selections.triggerSpinner.next({ show: false });
    });

    //this.selections.triggerSpinner.next({ show: false });
  }

  ngOnDestroy() {
    //this.subscriptions.forEach(s => s.unsubscribe());
  }


  initParams() {

    if (!this.service.chosenRetailerSites) {
      this.service.chosenRetailerSites = this.constants.clone(this.constants.userRetailerSites);
    }
  }

  onUpdateRetailerSites(retailerSites: RetailerSiteVM[]) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.service.chosenRetailerSites = retailerSites;
    this.service.getData().subscribe(res => {
      this.selections.triggerSpinner.next({ show: false });
    }, e => {
      this.selections.triggerSpinner.next({ show: false });
    });
  }

  toggleIncludeNewVehicles() {
    this.service.includeNewVehicles = !this.service.includeNewVehicles;
    this.onUpdateRetailerSites(this.service.chosenRetailerSites);
  }

  //////////////////////////////////////////////////// Recently Sold////////////////////////////
  initialiseRecentlySoldGrid() {
    this.service.recentlySold.gridOptions = {
      getContextMenuItems: (params) => this.service.recentlySold.gridHelpers.getContextMenuItems(params),
      getLocaleText: (params) => params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      animateRows: false,
      onRowClicked: (params) => this.onRowClick(params),
      getRowHeight: (params) => {
        return this.gridHelpersService.getRowHeight(60); // Or whatever height we want the image thumbnails to be
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return normalHeight * 1.5;
        } else {
          return normalHeight;
        }
      },

      rowData: this.service.rawData.LeavingItems,
      onGridReady: (params) => this.onGridReadyRecentlySold(params),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
        cellStyle: {
          display: 'flex',
          'align-items': 'center'
        },
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true
      },
      columnTypes: {
        number: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true },
        label: { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter', sortable: true },
        date: { cellClass: 'ag-right-aligned-cell', filter: 'agDateColumnFilter', valueFormatter: (params) => params.value ? this.cphPipe.transform(params.value, "dateMed", 0) : "" },
        special: { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' },
        currency: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true, cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 0) } },
        percent: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true, cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'percent', 1) } }
      },
      columnDefs: this.provideColDefsRecentlySold(),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params, 'service.recentlySold')
    }
  }

  onGridReadyRecentlySold(params: any) {
    this.service.recentlySold.gridApi = params.api;
    this.service.recentlySold.gridColumnApi = params.columnApi;
    this.service.recentlySold.gridApi.sizeColumnsToFit();
  }

  provideColDefsRecentlySold(): ColDef[] {
    return [

      {
        headerName: '',
        colId: 'ImageUrl',
        field: 'ImageUrl',
        type: 'image',
        cellRendererFramework: AutotraderImageCellComponent,
        cellRendererParams: { width: 60 },
        width: 25
      },   
      { headerName: 'When', sort: 'desc', colId: 'LeavingDate', field: 'LeavingDate', type: 'date', hide: true }, // Just for sort
      { headerName: 'Vehicle Details', colId: 'VehicleDetails', valueGetter: (params) => this.buildUpVehicleDetails(params), type: 'label', width: 70, cellStyle: { 'white-space': 'pre', 'line-height': 'normal' } },
      { headerName: 'Sale Details', colId: 'SaleDetails', valueGetter: (params) => this.buildUpSaleDetails(params), type: 'label', width: 30, cellStyle: { 'white-space': 'pre', 'line-height': 'normal' } },
    ];
  }

  buildUpVehicleDetails(params: any) {
    return `${params.data.RetailerSiteName}\n${params.data.Reg}\n${params.data.Make} ${params.data.Model} ${params.data.Derivative}`;
  }

  buildUpSaleDetails(params: any) {
    const leavingDate: string = this.cphPipe.transform(params.data.LeavingDate, 'dateMed', 0);
    const priceSoldAt: string = this.cphPipe.transform(params.data.PriceSoldAt, 'currency', 0);
    const finalPricePosition: string = this.cphPipe.transform(params.data.FinalPricePosition, 'percent', 0);
    const daysListed: number = this.constants.differenceInDays(params.data.FinalSnapshotDate, params.data.DateOnForecourt);
    return `Sold ${leavingDate}\nAt ${priceSoldAt} (${finalPricePosition})\nDays Listed: ${daysListed} days`;
  }

  getImage(params) {
    const row: LeavingPriceBasicItem = params.data;
    if (!row) { return '' }
    if (!row.ImageUrl) { return '' }

    return `<img style="height: 60px; width: 80px;" src=${row.ImageUrl} />`;
  }








  //////////////////////////////////////////////////// Very above very below ////////////////////////////

  initialiseVeryAboveVeryBelowGrid() {
    this.service.vAbovevBelow.gridOptions = {
      getContextMenuItems: (params) => this.service.vAbovevBelow.gridHelpers.getContextMenuItems(params),
      getLocaleText: (params) => params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      animateRows: false,
      getRowHeight: (params) => {
        return this.gridHelpersService.getHeaderHeight(); // Or whatever height we want the image thumbnails to be
      },
      onRowClicked: (params) => this.onRowClick(params),
      rowData: this.service.rawData.OffStrategyAdverts,
      onGridReady: (params) => this.onGridReadyvAbovevBelow(params),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true,
        cellStyle: {
          display: 'flex',
          'align-items': 'center'
        },
        headerComponentParams: {
          showPinAndRemoveOptions: false
        },
        autoHeaderHeight: true

      },

      columnTypes: {
        number: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true },
        label: { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter', sortable: true },
        special: { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' },
        currency: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true, cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 0) } },
        percent: { cellClass: 'ag-right-aligned-cell', filter: 'agNumberColumnFilter', sortable: true, cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'percent', 0) } }
      },
      columnDefs: this.provideColDefsvAbovevBelow(),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params, 'service.vAbovevBelow')
    }
  }

  onRowClick(params: RowClickedEvent<any, any>): void {
    if (!params.data) return;
    const row: OffStrategyVehicleSummaryItem = params.data;

    let allAdIds: number[] = []
    this.service.vAbovevBelow.gridApi.forEachNodeAfterFilter(node => {
      if (node.data && !node.isRowPinned()) {
        let nodeRow: OffStrategyVehicleSummaryItem = node.data;
        allAdIds.push(nodeRow.AdvertId)
      }
    })



    this.autoPriceInsightsModalService.initialise(row.AdvertId, allAdIds)
    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });
  }

  onGridReadyvAbovevBelow(params: any) {
    this.service.vAbovevBelow.gridApi = params.api;
    this.service.vAbovevBelow.gridColumnApi = params.columnApi;
    this.service.vAbovevBelow.gridApi.sizeColumnsToFit();
  }

  provideColDefsvAbovevBelow(): ColDef[] {
    return [
      { headerName: '', 
        colId: 'ImageUrl', 
        field: 'ImageUrl', 
        type: 'image', 
        cellRendererFramework: AutotraderImageCellComponent,
        cellRendererParams: { width: 90 },
        width: 40 },
      { headerName: 'Site', colId: 'RetailerSiteName', field: 'RetailerSiteName', type: 'label', width: 60 },
      { headerName: 'Reg', colId: 'Reg', field: 'Reg', type: 'special', cellRenderer: (params) => this.autopriceRendererService.regPlateRenderer(params, { fontSize: 1.1, noMargin: true }), width: 40 },
      { headerName: 'DaysListed', colId: 'DaysListed', field: 'DaysListed', type: 'number', width: 30 },
      { headerName: 'Derivative', colId: 'Derivative', field: 'Derivative', valueGetter: (params) => this.titleCasePipe.transform(params.data.Derivative), type: 'label', width: 80 },
      { headerName: 'StrategyPrice', colId: 'StrategyPrice', field: 'StrategyPrice', type: 'currency', width: 40 },
      { headerName: 'AdvertisedPrice', colId: 'SuppliedPrice', field: 'AdvertisedPrice', type: 'currency', width: 40 },
      { headerName: '', colId: 'vsIndicator', cellRenderer: (params) => this.vsIndicatorGetter(params), width: 5 },
      { headerName: 'Vs', sort: 'desc', sortIndex: 1, colId: 'Vs', field: 'Vs', valueGetter: (params) => this.vsGetter(params), type: 'currency', width: 30 },
    ];
  }
  vsIndicatorGetter(params) {
    if (!params.data) return '';
    const row: OffStrategyVehicleSummaryItem = params.data;
    const vs = row.AdvertisedPrice - row.StrategyPrice;

    return vs > 0 ? '&#9195;' : '&#9196;'
  }
  vsGetter(params: ValueGetterParams<any>): any {
    if (!params.data) return '';
    const row: OffStrategyVehicleSummaryItem = params.data;
    return row.AdvertisedPrice - row.StrategyPrice
  }

  getFirstImageOnlyvAbovevBelow(params) {
    if (!params.data.ImageURLs) return '';

    const firstImage: string = params.data.ImageURLs.split('|', 1)[0];
    return `<img style="height: 50px; width: 100%;" src=${firstImage} />`;
  }
  ////////////////////////////////////////////////////  Very above very below - END////////////////////////////



  setFilterAndNavigateToAdvertListing(view: 'Total Adverts' | 'Missing Images' | 'No attention grabber' | 'No Video' | 'Low Quality') {

    let model = null;

    if (view === 'Total Adverts') {
      model = {};
    } else if (view == 'Missing Images') {
      model = {
        IsMissingImages: {
          filterType: "set",
          values: ['true']
        }
      };
    }
    else if (view === 'No attention grabber') {
      model = {
        NoAttentionGrabber: {
          filterType: "set",
          values: ['true']
        }
      };
    }
    else if (view === 'No Video') {
      model = {
        NoVideo: {
          filterType: "set",
          values: ['false']
        }
      };
    }
    else if (view === 'Low Quality') {
      model = {
        IsLowQuality: {
          filterType: "set",
          values: ['true']
        }
      };
    }


    this.advertListingDetailService.setExternalFilterModel(model);
    this.navigateToAdvertListing();
  }

  navigateToAdvertListing() {
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/stockReports');
    if (menuItem) { this.constants.navigateByUrl(menuItem); }


  }
}
