﻿using CPHI.Spark.Model.ViewModels.RRG;
using log4net;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace CPHI.Spark.BusinessLogic.RRG
{
   public static class PersonCalculator11_May25Onwards
   {
      // Salesrole == NewUsed
      public static List<CommissionItem> CostUpDealsNewUsedPeople(IEnumerable<CommissionDataItem> itemsIn, DateTime yearMonth, CommissionPayoutRateSet payoutRates,
          Dictionary<string, CommissionOrderRatePaid> orderPaidRates, ILog logger, int electricQ4, int electricQRenault, int electricQDacia,
          Dictionary<string, int> incentiveDeals, Dictionary<string, decimal> latestViewOfOrderEarnRates)
      {
         int usedDealsCountIncrement = 0;  //start it at 0.  we will increment each time we encounter a valid deal. we use this to know what to pay the current unit at


         //if (itemsIn.First() != null && itemsIn.First().SalesmanName == "<PERSON>")
         //{
         //    { }
         //}


         List<CommissionItem> results = new List<CommissionItem>();

         //new car rate
         int relevantNewDealCount = WorkoutRelevantNewCarCountNewUsedPeople(itemsIn, yearMonth);



         foreach (var item in itemsIn)
         {
            CommissionItem result = new CommissionItem(item);
            //if (item.StockNumber == "1108895/0")
            //{
            //    { }
            //}


            if (item.Units_Count != 0)
            {
               //is a unit.  now check a few other things to work out if we should increment the achieved payout rate
               bool fullyCountsAsUnit = determineIfFullyCountsAsUnit(yearMonth, item);
               if (fullyCountsAsUnit && item.VTypeType != "New") { usedDealsCountIncrement++; }
               int newCarEarnRate = payoutRates.NewCarThresholdsAndPayouts.Last(x => relevantNewDealCount >= x.Key).Value;
               int usedCarEarnRate = payoutRates.UsedCarThresholdsAndPayouts.Last(x => usedDealsCountIncrement >= x.Key).Value;


               if (DealClassifier.isNewDeal(result))
               {
                  newCarCalc(result, payoutRates, newCarEarnRate, orderPaidRates, logger, electricQ4, electricQRenault, electricQDacia, latestViewOfOrderEarnRates);
                  IndividualDealCoster.addOnsNew(result, payoutRates.NewCarProductRates.Finance, payoutRates);
               }
               else if (isUsedDeal(result))
               {
                  result.VehicleCommissionType = "Used";
                  bool isNormalDeal = item.Units_Count == 1 && !item.IsLateCost;
                  usedCarCalc(result, usedCarEarnRate);

                  // Is potentially eligible for one-off incentive topup
                  if (item.AccountingDate >= new DateTime(2025, 5, 7) && incentiveDeals != null)
                  {
                     // If so, top up the earn rate by the incentive payment
                     if (incentiveDeals.TryGetValue(item.Reg, out var incentivePayment))
                     {
                        usedCarEarnRate += incentivePayment;
                     }
                  }

                  IndividualDealCoster.addOnsUsed(result, payoutRates.UsedCarProductRates.Finance, payoutRates);
               }
               else if (isFleetDeal(result))
               {
                  fleetCarCalc(result, payoutRates, orderPaidRates, logger);
                  IndividualDealCoster.addOnsNew(result, payoutRates.NewCarProductRates.Finance, payoutRates);
               }
            }

            results.Add(result);
         }
         ;
         return results;
      }

      // Salesrole == New or Used
      //seems that currently Nissan is exactly the same as New car scheme.   Once it changes we would have to create additional set of payoutrates
      public static List<CommissionItem> CostUpDealsNonNewUsedPeople(IEnumerable<CommissionDataItem> itemsIn, DateTime yearMonth,
          string role, CommissionPayoutRateSet payoutRates, Dictionary<string, CommissionOrderRatePaid> orderPaidRates, ILog logger,
          int electricQ4, int electricQRenault, int electricQDacia, Dictionary<string, decimal> latestViewOfOrderEarnRates,
          Dictionary<string, int> incentiveDeals, bool wasUsedTargetForSiteHit = false)
      {

         int countingDealsValue = 0;  //start it at 0.  we will increment each time we encounter a valid deal. we use this to know what to pay the current unit at

         List<CommissionItem> results = new List<CommissionItem>();

         //new car rate
         int relevantNewDealCount = WorkoutRelevantNewCarCountNonNewUsedPeople(itemsIn, yearMonth);

         foreach (var item in itemsIn)
         {
            //if (item.StockNumber == "1106722/0")
            //{
            //    { }
            //}
            CommissionItem result = new CommissionItem(item);

            //is a unit.  now check a few other things to work out if we should increment the achieved payout rate
            bool fullyCountsAsUnit = determineIfFullyCountsAsUnit(yearMonth, item);
            if (fullyCountsAsUnit) { countingDealsValue++; }
            int newCarEarnRate = payoutRates.NewCarThresholdsAndPayouts.Last(x => relevantNewDealCount >= x.Key).Value;
            int usedCarEarnRate = payoutRates.UsedCarThresholdsAndPayouts.Last(x => countingDealsValue >= x.Key).Value;

            if (DealClassifier.isNewDeal(result))
            {
               //usual new car stuff
               newCarCalc(result, payoutRates, newCarEarnRate, orderPaidRates, logger, electricQ4, electricQRenault, electricQDacia, latestViewOfOrderEarnRates);
               IndividualDealCoster.addOnsNew(result, payoutRates.NewCarProductRates.Finance, payoutRates);
            }
            else if (isUsedDeal(result)) //now we define used as not including Fleet orderType
            {
               result.VehicleCommissionType = "Used";

               if (role == "New")
               {
                  //new role people
                  bool isNormalDeal = item.Units_Count == 1 && !item.IsLateCost;
                  result.EligibleProfitPot = CommonMethods.CalculateUsedProfitPot(isNormalDeal, item.ChassisProfitForMonth, item.LifeTimeChassisProfit);
                  result.ProfitPotThresholdAchieved = 0.1M; //locked at 10% per Bob
                  result.PopulateProfitPot();
                  IndividualDealCoster.addOnsNew(result, payoutRates.NewCarProductRates.Finance, payoutRates);  //we still do add-ons also
               }
               // Used role people
               else
               {
                  //used people get paid on their achieved payout rate, and no add-ons
                  bool isNormalDeal = item.Units_Count == 1 && !item.IsLateCost;

                  // If the target for the site has been hit and max banding achieved, payout will be 350
                  if (wasUsedTargetForSiteHit && usedCarEarnRate == 250)
                  {
                     usedCarEarnRate += 100;
                  }

                  // Is potentially eligible for one-off incentive topup
                  if (item.AccountingDate >= new DateTime(2025, 5, 7) && incentiveDeals != null)
                  {
                     // If so, top up the earn rate by the incentive payment
                     if (incentiveDeals.TryGetValue(item.Reg, out var incentivePayment))
                     {
                        usedCarEarnRate += incentivePayment;
                     }
                  }

                  usedCarCalc(result, usedCarEarnRate);

                  IndividualDealCoster.addOnsUsed(result, payoutRates.UsedCarProductRates.Finance, payoutRates);
               }
            }
            else if (isFleetDeal(result))
            {
               //usual fleet stuff (although notice order type fleet, vehicle type used, now is a 'fleet' car
               fleetCarCalc(result, payoutRates, orderPaidRates, logger);
               IndividualDealCoster.addOnsNew(result, payoutRates.NewCarProductRates.Finance, payoutRates);
            }

            if (result.PayRatePartEx == 0)
            {
               result.PartEx_Count = 0;
            }

            if (result.PayRateFinance == 0)
            {
               result.Finance_Count = 0;
            }

            if (result.PayRateServicePlan == 0)
            {
               result.ServicePlan_Count = 0;
            }

            results.Add(result);
         }
         ;

         //var tst = results.Where(x => x.StockNumber == "1106722/0");
         return results;
      }



      public static void usedCarCalc(CommissionItem item, int usedCarPayoutRate)
      {
         item.VehicleCommissionType = "Used";
         item.Units_Used = item.Units_Count;
         item.PayRateUsed = item.Units_Count * usedCarPayoutRate;
      }

      public static IEnumerable<CommissionItem> CostUpDealsLBDM(IEnumerable<CommissionDataItem> itemsIn, DateTime yearMonth, CommissionPayoutRateSet payoutRates, ILog logger)
      {
         //if (itemsIn.First() != null && itemsIn.First().SalesmanName == "Peter Doerr")
         //{
         //    { }
         //}

         List<CommissionItem> results = new List<CommissionItem>();
         foreach (var item in itemsIn)
         {
            //if (item.StockNumber == "898522/1")
            //{
            //    { }
            //}
            CommissionItem result = new CommissionItem(item);


            if (item.VTypeType.ToUpper() == "NEW")
            {
               newCarCalc_LBDM(result, payoutRates, logger);
               IndividualDealCoster.addOnsNew(result, payoutRates.NewCarProductRates.Finance, payoutRates);
            }
            else
            {
               usedCarCalc_LBDM(result, payoutRates, logger);
               IndividualDealCoster.addOnsUsed(result, payoutRates.UsedCarProductRates.Finance, payoutRates);
            }

            results.Add(result);
         }
         ;
         return results;

      }

      public static void newCarCalc_LBDM(CommissionItem item, CommissionPayoutRateSet payoutRates, ILog logger)
      {

         if (item.StockNumber == "1100349/0")
         {
            { }
         }
         if (item.OrderTypeId == 3) //Core fleet
         {
            item.Units_FleetCar = item.Units_Count;
            //core fleet
            item.PayRateRenault = item.Units_Count > 0 ? payoutRates.CoreFleet : payoutRates.CoreFleet * -1;
            item.VehicleCommissionType = "Core Fleet";
         }
         else if (item.OrderTypeId == 4 || item.OrderTypeId == 5)
         {
            if (item.FranCode.ToLower() == "r")
            {
               item.Units_Renault = item.Units_Count;
               //Renault stuff
               if (item.VClassDesc == "Van")
               {
                  //Van
                  if (item.IsElectric)
                  {
                     int payRate = payoutRates.FleetVan + 100;
                     item.PayRateRenault = item.Units_Count > 0 ? payRate : payRate * -1;
                     item.VehicleCommissionType = "Renault Van EV";
                  }
                  else
                  {
                     item.PayRateRenault = item.Units_Count > 0 ? payoutRates.FleetVan : payoutRates.FleetVan * -1;
                     item.VehicleCommissionType = "Renault Van";
                  }
               }
               else
               {
                  //Car
                  if (item.IsElectric)
                  {
                     int payRate = payoutRates.FleetCar + 100;
                     item.PayRateRenault = item.Units_Count > 0 ? payRate : payRate * -1;
                     item.VehicleCommissionType = "Renault Car EV";
                  }
                  else
                  {
                     item.PayRateRenault = item.Units_Count > 0 ? payoutRates.FleetCar : payoutRates.FleetCar * -1;
                     item.VehicleCommissionType = "Renault Car";
                  }
               }
            }
            else if (item.FranCode.ToLower() == "d")
            {
               item.Units_Renault = item.Units_Count;
               //Dacia stuff
               item.PayRateDacia = item.Units_Count > 0 ? payoutRates.Dacia : payoutRates.Dacia * -1;
               item.VehicleCommissionType = "Dacia";
            }
         }
         else
         {
            //no pay, not fleet

         }


      }

      public static void usedCarCalc_LBDM(CommissionItem item, CommissionPayoutRateSet payoutRates, ILog logger)
      {
         item.Units_Used = item.Units_Count;
         if (item.VClassDesc == "Van")
         {
            item.PayRateUsed = item.Units_Count > 0 ? payoutRates.UsedVan : payoutRates.UsedVan * -1;
            item.VehicleCommissionType = "Used vans";
         }
         else
         {
            item.PayRateUsed = item.Units_Count > 0 ? payoutRates.UsedCar : payoutRates.UsedCar * -1;
            item.VehicleCommissionType = "Used cars";
         }
      }






      private static bool determineIfFullyCountsAsUnit(DateTime yearMonth, CommissionDataItem item)
      {
         bool fullyCountsAsUnit = (
                     item.Units_Count > 0 &&
                     !item.Customer.StartsWith("Motability Quarterly") &&
                     !item.Customer.StartsWith("Quarterly catchup") &&
                     !item.Customer.StartsWith("Impact of") &&
                     (
                         (item.VTypeType != "New") ||  //if it's a used vehicle
                                                       //OR
                         (char.ToLower(item.FranCode) != 'r' || item.VClassDesc != "Van") &&   //not renault van
                         item.OrderDate.Month == yearMonth.Month && // and must be ordered in the month
                         item.OrderDate.Year == yearMonth.Year
                     ) &&
                     item.OTypeType.ToLower() != "motability"   //not a motability otype
                     );
         return fullyCountsAsUnit;
      }


      private static int WorkoutRelevantNewCarCountNewUsedPeople(IEnumerable<CommissionDataItem> itemsIn, DateTime yearMonth)
      {
         var relevantItems = itemsIn.Where(x => !x.Customer.StartsWith("Motability Quarterly") &&
                                                 !x.Customer.StartsWith("Quarterly catchup") &&
                                                 !x.Customer.StartsWith("Impact of") &&
                                                 x.OTypeType.ToLower() != "motability" &&
                                                 x.OrderDate >= new DateTime(2024, 9, 1) &&
                                                 (
                                                     x.VTypeType != "New" ||
                                                     (
                                                         (x.FranCode == 'N' || x.FranCode == 'R') &&
                                                         x.VClassDesc != "Van"
                                                     )
                                                 )
                                                 );

         return relevantItems.Count() > 0 ? relevantItems.Select(x => x.Units_Count).Sum() : 0;
      }

      private static int WorkoutRelevantNewCarCountNonNewUsedPeople(IEnumerable<CommissionDataItem> itemsIn, DateTime yearMonth)
      {
         var relevantItems = itemsIn.Where(x => !x.Customer.StartsWith("Motability Quarterly") && !x.Customer.StartsWith("Quarterly catchup") && !x.Customer.StartsWith("Impact of") &&
                                                 x.OTypeType.ToLower() != "motability" &&
                                                 x.OrderDate >= new DateTime(2024, 9, 1) &&
                                                 x.VTypeType == "New" &&
                                                 (
                                                     (
                                                         (x.FranCode == 'N' || x.FranCode == 'R') &&
                                                         x.VClassDesc != "Van"
                                                     )
                                                 )
                                                 );
         return relevantItems.Count() > 0 ? relevantItems.Select(x => x.Units_Count).Sum() : 0;
      }


      private static void newCarCalc(CommissionItem item, CommissionPayoutRateSet payoutRates, int achievedNewCarPayoutRate, Dictionary<string, CommissionOrderRatePaid> orderPaidRates, ILog logger,
          int electricQ, int electricQRenault, int electricQDacia, Dictionary<string, decimal> latestViewOfOrderEarnRates)
      {

         // Halsall ; MF74XVZ: EV Motability : Paul Ormerod // should be £250 total
         //if (item.Reg == "MF74XVZ")
         //{
         //    { }
         //}

         //Motability deals
         if (item.OTypeType.ToLower() == "motability")
         {
            item.VehicleCommissionType = "Motability";
            item.Units_Motability = item.Units_Count;
            int payRate = payoutRates.Motability;
            payRate = IncreasePayRateIfElectric(item, payRate, electricQ, electricQRenault, electricQDacia);
            payRate = reducePayRateIfApplicable(item, payRate, orderPaidRates, logger, latestViewOfOrderEarnRates);
            item.PayRateMotability = item.Units_Count * payRate; // as the unit count might sometimes be -1
         }
         //Renault deals
         else if (item.FranCode.ToLower() == "r")
         {
            if (item.VClassDesc == "Van")
            {
               item.Units_Renault = item.Units_Count;
               item.VehicleCommissionType = "Renault";
               int payRate = payoutRates.FleetVan;
               payRate = IncreasePayRateIfElectric(item, payRate, electricQ, electricQRenault, electricQDacia);
               payRate = reducePayRateIfApplicable(item, payRate, orderPaidRates, logger, latestViewOfOrderEarnRates);
               item.PayRateRenault = item.Units_Count * payRate;
            }
            else
            {
               item.Units_Renault = item.Units_Count;
               item.VehicleCommissionType = "Renault";
               int payRate = achievedNewCarPayoutRate;
               payRate = IncreasePayRateIfElectric(item, payRate, electricQ, electricQRenault, electricQDacia);
               payRate = reducePayRateIfApplicable(item, payRate, orderPaidRates, logger, latestViewOfOrderEarnRates);
               item.PayRateRenault = item.Units_Count * payRate;
               item.PayRateRenault += GetRenaultRetailBonus(item.OrderDate, item.AccountingDate, item.Description);
            }

         }
         //Nissan deals
         else if (item.FranCode.ToLower() == "n")
         {
            item.Units_Nissan = item.Units_Count;
            item.VehicleCommissionType = "Nissan";
            int payRate = achievedNewCarPayoutRate;
            payRate = IncreasePayRateIfElectric(item, payRate, electricQ, electricQRenault, electricQDacia);
            payRate = reducePayRateIfApplicable(item, payRate, orderPaidRates, logger, latestViewOfOrderEarnRates);
            item.PayRateNissan = item.Units_Count * payRate;
         }
         //Dacia deals
         else if (item.FranCode.ToLower() == "d")
         {
            item.Units_Dacia = item.Units_Count;
            item.VehicleCommissionType = "Dacia";
            int payRate = payoutRates.Dacia;
            payRate = IncreasePayRateIfElectric(item, payRate, electricQ, electricQRenault, electricQDacia);
            // payRate = IncreasePayRateIfSpring(item, payRate);
            payRate = reducePayRateIfApplicable(item, payRate, orderPaidRates, logger, latestViewOfOrderEarnRates);
            item.PayRateDacia = item.Units_Count * payRate;
         }
      }

      // Ad hoc bonus requested by RRG
      private static int GetRenaultRetailBonus(DateTime orderDate, DateTime accountingDate, string model)
      {
         // Ordered & registered between March 7th - 31st
         DateTime start = new DateTime(2025, 3, 7);   // March 7th
         DateTime end = new DateTime(2025, 3, 31);    // March 31st

         bool isInRange = orderDate >= start && orderDate <= end
                       && accountingDate <= end;

         //not in time range, no bonus
         if (!isInRange)
         {
            return 0;
         }

         // If it is a Megane, apply higher bonus
         if (model.ToLower().Contains("megane"))
         {
            return 200;
         }

         //otherwise, normal bonus
         return 50;
      }


      private static int IncreasePayRateIfElectric(CommissionItem item, int payRate, int electricQ, int electricQRenault, int electricQDacia)
      {
         //SPK-4334 extra step
         List<string> nonEVQualifyingOrderTypes = new List<string>() { "ECOP", "Loan", "Wholesale" };

         if (item.StockNumber == "1126381/0")
         {
            { }
         }

         // Ensure it is a qualifying order type
         if (!nonEVQualifyingOrderTypes.Contains(item.OTypeType))
         {
            bool isMotability = item.OTypeType.ToLower() == "motability";

            // For deals ordered prior to quarter, but delivered in 2025 (not new scheme)
            if (item.IsElectric && !item.OnlyPayInvoiceMoney && item.OrderDate.Year < 2025)
            {
               int motabilityTopUp = (electricQ > 6) ? 100 : 50;
               int retailTopUp = (electricQ > 6) ? 300 : 150;
               return payRate + (isMotability ? motabilityTopUp : retailTopUp);
            }
            else if (item.IsElectric && !item.OnlyPayInvoiceMoney && item.FranchiseId == 204) // Dacia
            {
               int motabilityTopUp = (electricQDacia > 2) ? 100 : 50;
               int retailTopUp = (electricQDacia > 2) ? 200 : 100;
               return payRate + (isMotability ? motabilityTopUp : retailTopUp);
            }
            else if (item.IsElectric && !item.OnlyPayInvoiceMoney && item.FranchiseId == 202) // Renault
            {
               int motabilityTopUp = (electricQRenault > 4) ? 100 : 50;
               int retailTopUp = (electricQRenault > 4) ? 300 : 150;
               return payRate + (isMotability ? motabilityTopUp : retailTopUp);
            }
            else
            {
               return payRate;
            }
         }
         else
         {
            return payRate;
         }
      }


      public static int reducePayRateIfApplicable(CommissionItem item, int payRateIn, Dictionary<string, CommissionOrderRatePaid> orderPaidRates, ILog logger, Dictionary<string, decimal> latestViewOfOrderEarnRates)
      {
         //for orders Sep 24 onwards, get everything on delivery
         //for orders Jul 24 onwards, the logic is that you get 25% on order 75% on delivery
         //for all previous orders (going way back into May 2024) the logic is that you got everything 50%/50%

         if (item.OrderDate < new DateTime(2023, 5, 1))
         {
            if (item.OnlyPayInvoiceMoney)
            {
               return 10;  //we only ever held back £10 on old orders
            }
            else if (item.OnlyPayOrderMoney)
            {
               throw new Exception("Unexpected new car payout");
               //return payRateIn / 2;
            }
            else
            {
               //still here.  Could be ok.  It means we are dealing with a used fleet car
               return payRateIn;
               //throw new Exception("Unexpected new car payout");
            }
         }
         else if (item.OrderDate < new DateTime(2024, 7, 1))
         {
            if (item.OnlyPayInvoiceMoney)
            {
               return (int)Math.Ceiling(payRateIn / 2.0);  //we are now paying what we held back before
            }
            else if (item.OnlyPayOrderMoney)
            {
               throw new Exception("Unexpected new car payout");
               //return payRateIn / 2;
            }
            else
            {
               //still here.  Could be ok.  It means we are dealing with a used fleet car
               return payRateIn;
            }
         }
         else if (item.OrderDate < new DateTime(2024, 9, 1))
         {
            //order from Jul 2024 onwards
            if (item.OnlyPayInvoiceMoney)
            {
               string lookupValue = $"{item.StockNumber}|{item.SalesmanId}";

               // TODO Add additional condition to check dictionary to see if lookupvalue has been recalculated 
               // Get value from new adjustment month (for order month)

               if (latestViewOfOrderEarnRates.TryGetValue(lookupValue, out var payRateLatest))
               {
                  return (int)payRateLatest * 3;
               }
               else
               {
                  if (logger != null)
                  {
                     logger.Warn($"Did not find latestViewOfOrderEarnRates for {lookupValue}");
                  }
                  if (orderPaidRates.TryGetValue(lookupValue, out var payRate))
                  {
                     return payRate.UnitPayInOrderMonth * 3;  //we paid 25% in order month, now have to pay 75% on delivery, so x by 3
                  }
                  else
                  {
                     //strange, didn't find what this salesman had previously earned at order time on this stocknumber, this is unexpected.
                     if (logger != null)
                     {
                        logger.Warn($"{item.OrderDate} Could not find previously earned order rate for {item.StockNumber} sold by {item.SalesmanName}");
                     }
                     return (int)Math.Ceiling(payRateIn * 0.75); //we pay 75% of the payRate.   Couldn't find the order rate value, so just do 75% of this month's rate.
                  }
               }

            }
            else if (item.OnlyPayOrderMoney)
            {
               return (int)Math.Ceiling(payRateIn * 0.25);
            }
            else
            {
               return payRateIn;
            }
         }

         else
         {
            //really simple, no restriction needed, as from 1 Sep onwards we pay all on delivery
            return payRateIn;
         }

      }

      private static int IncreasePayRateIfSpring(CommissionItem item, int payRate)
      {
         //SPK-4525 extra step

         if (item.Description != null && item.Description.ToLower().Contains("spring") && item.OTypeType != "Motability")
         {
            //is a qualifying order type, check if electric
            payRate = 150;
            return payRate;
         }
         else
         {
            return payRate;
         }
      }

      public static bool isUsedDeal(CommissionItem item)
      {
         return new List<string>() { "auction", "distance", "group", "loan", "retail", "staff", "trade", "fleet" }
                         .Contains(item.OTypeType.ToLower())
                         && new List<string>() { "coreused", "demo", "exdemo", "exmanagement", "tactical" }
                         .Contains(item.VTypeType.ToLower());
      }


      public static bool isFleetDeal(CommissionItem item)
      {
         return new List<string>() { "fleet" }
                         .Contains(item.OTypeType.ToLower())
                         && new List<string>() { "coreused", "demo", "exdemo", "exmanagement", "tactical", "new" }
                         .Contains(item.VTypeType.ToLower());
      }

      public static void fleetCarCalc(CommissionItem item, CommissionPayoutRateSet payoutRates, Dictionary<string, CommissionOrderRatePaid> orderPaidRates, ILog logger)
      {


         if (item.VClassDesc == "Car")
         {
            item.Units_FleetCar = item.Units_Count;
            item.VehicleCommissionType = "FleetCar";
            int payRate = reducePayRateIfApplicable(item, payoutRates.FleetCar, orderPaidRates, logger);
            item.PayRateFleetCar = item.Units_Count * payRate;
         }
         else
         {
            item.Units_FleetVan = item.Units_Count;
            item.VehicleCommissionType = "FleetVan";
            int payRate = reducePayRateIfApplicable(item, payoutRates.FleetVan, orderPaidRates, logger);
            item.PayRateFleetVan = item.Units_Count * payRate;
         }

      }

      public static int reducePayRateIfApplicable(CommissionItem item, int payRateIn, Dictionary<string, CommissionOrderRatePaid> orderPaidRates, ILog logger)
      {
         //for orders May 23 onwards, the logic is that you get 50% on order 50% on delivery
         //for all previous orders (going way back into 2021) the logic is that you got everything less £10 on order and £10 on handover

         if (item.OrderDate < new DateTime(2023, 5, 1))
         {
            if (item.OnlyPayInvoiceMoney)
            {
               return 10;  //we only ever held back £10 on old orders
            }
            else if (item.OnlyPayOrderMoney)
            {
               throw new Exception("Unexpected new car payout");
               //return payRateIn / 2;
            }
            else
            {
               //still here.  Could be ok.  It means we are dealing with a used fleet car
               return payRateIn;
               //throw new Exception("Unexpected new car payout");
            }
         }
         else
         {
            //order from May 2023 onwards
            if (item.OnlyPayInvoiceMoney)
            {
               string lookupValue = $"{item.StockNumber}|{item.SalesmanId}";
               if (orderPaidRates.TryGetValue(lookupValue, out var payRate))
               {
                  return payRate.UnitPayInOrderMonth;
               }
               else
               {
                  //strange, didn't find what this salesman had previously earned at order time on this stocknumber, this is unexpected.
                  if (logger != null)
                  {
                     logger.Warn($"{item.OrderDate} Could not find previously earned order rate for {item.StockNumber} sold by {item.SalesmanName}");
                  }
                  return (int)Math.Ceiling(payRateIn / 2.0); //do the old method
               }
            }
            else if (item.OnlyPayOrderMoney)
            {
               return (int)Math.Ceiling(payRateIn / 2.0);
            }
            else
            {
               return payRateIn;
            }
            //if (item.OnlyPayOrderMoney || item.OnlyPayInvoiceMoney) { return (int)Math.Ceiling(payRateIn / 2.0); }
            //else { return payRateIn; }
         }

      }

   }

}
