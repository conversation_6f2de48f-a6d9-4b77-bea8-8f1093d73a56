import { Component, HostListener, OnInit } from '@angular/core';
import { GridApi, GridOptions } from 'ag-grid-community';
import { TranslatedText } from 'src/app/model/translations.model';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../cph.pipe';
import { AGGridMethodsService } from '../../services/agGridMethods.service';
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';
import { OrdersBySiteService } from './ordersBySite.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

@Component({
  selector: 'ratioOrdersForSiteTable',
  template: `
    <div id="gridHolder">
      <button id="backButton" class="btn btn-primary" (click)="service.ordersForSite = null"><</button>
      <ag-grid-angular 
        class="ag-theme-balham" 
        [gridOptions]="mainTableGridOptions"
      >    
      </ag-grid-angular>
      <div id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
    </div>
  `,
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
  styles: [`
    #backButton {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      
    }
    #excelExport {
      right: 10px;
    }
    #gridHolder {
      position: relative;
      margin: 0.5em;
    }
    ag-grid-angular {
      width: 100%;
    }
  `]
})

export class RatioOrdersForSiteTableComponent implements OnInit {

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  public gridColumnApi;
  mainTableGridOptions: GridOptions

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public service: OrdersBySiteService,
    public columnTypeService: ColumnTypesService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  ngOnDestroy() {
    this.service.ordersForSite = null;
  }

  ngOnInit() {
    this.setGridOptions();
  }

  setGridOptions() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.service.ordersForSite,
      onGridReady: (params) => this.onGridReady(params),
      domLayout: 'autoHeight',  
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: this.provideColDefs()
    }
  }

  provideColDefs() {
    let translation: TranslatedText = this.constants.translatedText;

    return [
      { headerName: translation.Ratio_Branch, field: 'Branch', colId: 'Branch', width: 100, type: 'label' },
      { headerName: translation.Ratio_BranchCode, field: 'BranchCode', colId: 'BranchCode', width: 20, type: 'label' },
      { headerName: translation.Ratio_Contacts, field: 'Contacts', colId: 'Contacts', width: 70, type: 'label' },
      { headerName: translation.Franchise, field: 'Franchise', colId: 'Franchise', width: 20, type: 'label' },
      { headerName: translation.Model, field: 'Model', colId: 'Model', width: 70, type: 'label' },
      // { headerName: translation.Chassis, field: 'Chassis', colId: 'Chassis', width: 30, type: 'label' },
      // { headerName: translation.Ratio_Origin, field: 'Origin', colId: 'Origin', width: 50, type: 'label' },
      { headerName: translation.Year, field: 'Year', colId: 'Year', width: 20, type: 'label' },
      { headerName: translation.Reg, field: 'Registration', colId: 'Registration', width: 25, type: 'label' },
      { headerName: translation.Ratio_SaleDate, field: 'SaleDate', colId: 'SaleDate', width: 40, type: 'label' },
      // { headerName: translation.Ratio_SalePrice, field: 'SalePrice', colId: 'SalePrice', width: 20, type: 'currency' },
      { headerName: translation.Ratio_SalesPerson, field: 'SalesPerson', colId: 'SalesPerson', width: 40, type: 'label' }
    ]
  }

  resizeGrid() {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  onGridReady(params): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
  }

  excelExport(): void {
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Orders By Site', 1, 1);
  }
}
