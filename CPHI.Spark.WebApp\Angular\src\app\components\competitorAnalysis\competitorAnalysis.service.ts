import {Injectable} from "@angular/core";
import {CompetitorAnalysisChartComponent} from "./competitorAnalysisChart/competitorAnalysisChart.component";
import {CompetitorAnalysisParams} from "./CompetitorAnalysisParams";
import {CompetitorAnalysisTableComponent} from "./competitorAnalysisTable/competitorAnalysisTable.component";
import {BehaviorSubject} from "rxjs";

@Injectable({
   providedIn: 'root'
})

export class CompetitorAnalysisService {
   // params: CompetitorAnalysisParams;
   tableRef: CompetitorAnalysisTableComponent;
   chartRef: CompetitorAnalysisChartComponent;
   params$: BehaviorSubject<CompetitorAnalysisParams> = new BehaviorSubject<CompetitorAnalysisParams>(null);

   get params() {
      return this.params$.value;
   }

   constructor(
      // public service: AutoPriceInsightsModalService,
   ) {

      this.params$.subscribe(params => {
         this.dealWithNewParams(params);
      });
   }

   dealWithNewParams(params: CompetitorAnalysisParams) {
      if (this.tableRef) {
         this.tableRef.dealWithNewData(this.params$.value);
      }
      if (this.chartRef) {
         this.chartRef.dealWithNewData(this.params$.value.CompetitorSummary);
      }
   }
}
