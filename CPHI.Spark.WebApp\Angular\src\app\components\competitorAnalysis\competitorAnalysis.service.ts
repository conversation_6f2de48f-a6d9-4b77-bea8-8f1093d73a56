import {Injectable} from "@angular/core";
import {CompetitorAnalysisChartComponent} from "./competitorAnalysisChart/competitorAnalysisChart.component";
import {CompetitorAnalysisParams} from "./CompetitorAnalysisParams";
import {CompetitorAnalysisTableComponent} from "./competitorAnalysisTable/competitorAnalysisTable.component";

@Injectable({
   providedIn: 'root'
})

export class CompetitorAnalysisService {
   params: CompetitorAnalysisParams;
   tableRef: CompetitorAnalysisTableComponent;
   chartRef: CompetitorAnalysisChartComponent;

   constructor(
      // public service: AutoPriceInsightsModalService,
   ) {

   }

   dealWithNewParams(params: CompetitorAnalysisParams) {
      this.params = params;
      if (this.tableRef) {
         this.tableRef.dealWithNewData(this.params);
      }
      if (this.chartRef) {
         this.chartRef.dealWithNewData(this.params.CompetitorSummary);
      }
   }
}
