table {
    width: 100%;
    table-layout: fixed;

    td:first-of-type {
        width: 32%;
    }

    td:nth-of-type(2) {
        padding-left: 1em;
    }
}

td#priceIndicatorCell{
    img{width:10em;}
}

td#priceIndicatorCell,
td#keyDetailsCell {
    padding: 0.5em 0;
}

#vehiclePrice {
    
    font-weight: 700;
    color: var(--atPrimary);
    margin-right: 1em;
}

#vehicleMakeModel {
    
    font-weight: 500;
    color: var(--atPrimary);
}

span#vehicleDerivative {
    
    color: var(--atPrimary);
}

.no-analysis-indicator {
    background-color: var(--grey90);
    width: fit-content;
    padding: 0.5em 1em;
    
    border-radius: 10px 0 10px 0;
    font-weight: 700;
}

.regPlate{width:10em;}

#linksContainer {
    display: flex;
    justify-content: space-around;
}

#copyReg{
    color:var(--grey80);
    transition: all 0.2s ease;
    cursor: pointer
}
#copyReg:hover{
    color:var(--brightColour);
    transform:scale(1.2);

}