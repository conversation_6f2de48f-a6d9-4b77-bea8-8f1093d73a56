# Node.js with Angular
# Build a Node.js project that uses Angular.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript


trigger:

  tags:
    include:
    - webapp*
    - all*

# resources:
#   pipelines:
#     - pipeline: 'Spark Test Pipeline'
#       source: 'Spark Test Pipeline'
#       branch: /* #latest branch
#       trigger:
#        branches:
#         - /* #latest branch

pool:
  default

jobs:
- job: BuildAndPublishAngularSparkClient
  displayName: 'Build And Publish Angular Spark Client'

  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '16.14.x'
    displayName: 'Install Node.js'


  - script: |
      npm install -g @angular/cli
      npm install -g brotli
      npm install
    displayName: 'Install Angular CLI'

  - task: Npm@1
    displayName: 'Install npm packages'
    inputs:
      command: 'install'
      workingDir: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/Angular'


      
  - task: Npm@1
    displayName: 'Build rrgProd'
    env:
      NODE_OPTIONS: '--max-old-space-size=8192'
    inputs:
      command: 'custom'
      workingDir: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/Angular'
      customCommand: 'run rrgProd'



      
  - task: ArchiveFiles@2
    displayName: 'Zip rrgProd'
    inputs:
      rootFolderOrFile: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/live'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/rrgProd_$(Build.BuildId).zip'
      replaceExistingArchive: true




  - task: PublishBuildArtifacts@1
    displayName: 'Publish rrgProd'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/rrgProd_$(Build.BuildId).zip'
      ArtifactName: 'RrgProd'
      publishLocation: 'Container'

  - task: DeleteFiles@1
    displayName: 'Delete output folder'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/live'
      Contents: '*' 
      RemoveSourceFolder: true
