



trigger:
  tags:
    include:
    - webapp*
    - all*
    

pool:
  default

jobs:
- job: BuildAndPublishAngularSparkClient
  displayName: 'Build And Publish Angular Spark Client'

  steps:

  - checkout: self
    fetchDepth: 1
    fetchTags: false # Don't fetch unnecessary tags
    clean: true # Ensures a fresh checkout
    persistCredentials: true

  - task: NodeTool@0
    inputs:
      versionSpec: '16.14.x'
    displayName: 'Install Node.js'

  - script: |
      echo "##[group]Node, NPM & Angular CLI Versions"
      node -v
      npm -v
      npx ng version
      echo "##[endgroup]"
      
      echo "##[group]Environment Variables"
      set
      echo "##[endgroup]"

      echo "##[group]NPM Global List"
      npm list -g --depth=0
      echo "##[endgroup]"
    displayName: 'Environment Diagnostics'

    # - task: Cache@2
    # inputs:
    #   key: 'npm | "$(Agent.OS)" | package-lock.json'
    #   restoreKeys: |
    #     npm | "$(Agent.OS)"
    #   path: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/Angular/node_modules'
    # displayName: 'Cache NPM packages'

  - script: npm ci
    workingDirectory: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/Angular'
    displayName: 'Install dependencies using npm ci'
    env:
      NODE_OPTIONS: '--max-old-space-size=8192'

  - task: Npm@1
    displayName: 'Build rrgProd'
    env:
      NODE_OPTIONS: '--max-old-space-size=8192'
    inputs:
      command: 'custom'
      workingDir: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/Angular'
      customCommand: 'run rrgProd'


  - script: |
      echo Compressing build output with Brotli...
      for /r $(Build.SourcesDirectory)\CPHI.Spark.WebApp\live %%f in (*) do npx brotli-cli compress -q 11 "%%f"
      del /s /q $(Build.SourcesDirectory)\CPHI.Spark.WebApp\live\config\*.br 2> nul
    displayName: 'Brotli compress build output'
    condition: succeededOrFailed()

  - task: ArchiveFiles@2
    displayName: 'Zip rrgProd'
    inputs:
      rootFolderOrFile: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/live'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/rrgProd_$(Build.BuildId).zip'
      replaceExistingArchive: true

  - task: PublishBuildArtifacts@1
    displayName: 'Publish rrgProd'
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/rrgProd_$(Build.BuildId).zip'
      ArtifactName: 'RrgProd'
      publishLocation: 'Container'

  - task: DeleteFiles@1
    displayName: 'Delete output folder'
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)/CPHI.Spark.WebApp/live'
      Contents: '*'
      RemoveSourceFolder: true
