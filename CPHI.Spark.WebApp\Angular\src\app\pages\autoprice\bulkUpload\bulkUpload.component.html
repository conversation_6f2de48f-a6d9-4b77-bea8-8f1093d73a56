<nav class="navbar">
   <nav class="generic">
      <h4 id="pageTitle">Vehicle Valuation</h4>

      <button *ngIf="stage !== 'start'" class="btn btn-primary" (click)="backToStart()">
         Back
      </button>

      <!-- If at start -->
      <ng-container *ngIf="stage=='viewExistingBatch'">

         <!-- If no valuations at all, perhaps new customer -->
         <ng-container *ngIf="!service.vehicleValuationBatches || service.vehicleValuationBatches.length==0">
            <button class="btn btn-primary" disabled>No vehicles have yet been
               valued. Click Upload to choose a file.
            </button>
         </ng-container>

         <!-- If we have some previous batch valuations -->
         <ng-container *ngIf="service.vehicleValuationBatches?.length>0 && !singleMode">
            <div ngbDropdown class="d-inline-block">
               <button class="btn btn-primary" ngbDropdownToggle (click)="getVehicleValuationBatches()">
                  {{ service.chosenVehicleValuationBatch.Name }}
               </button>

               <div id="valuationsDropdown" ngbDropdownMenu aria-labelledby="valuationsDropdown">
                  <!-- While fetching -->
                  <div *ngIf="fetchingBatches" class="lds-ring">
                     <div></div>
                     <div></div>
                     <div></div>
                     <div></div>
                  </div>

                  <!-- Once loaded -->
                  <ng-container *ngIf="!fetchingBatches">
                     <button *ngFor="let batch of service.vehicleValuationBatches" ngbDropdownItem
                             [ngClass]="{ 'active': batch.Id === service.chosenVehicleValuationBatch.Id }"
                             ngbDropdownToggle
                             class="manualToggleCloseItem" (click)="chooseBatch(batch)">
                        {{ batch.Name }}
                        uploaded {{ batch.LastRunDate | cph:'shortDateWithDayName':0 }} by {{ batch.LastRunBy }} -
                        {{ constants.pluralise(batch.TotalVehicles, 'vehicle', 'vehicles') }} -
                        valuation uploaded at {{ batch.LastRunDate | cph:'shortTimeAM':0 }},
                        {{
                           batch.Status === 'Complete' ? ' completed at ' + (batch.ValuationCompleteDate | cph:'shortTimeAM':0)
                              : ' in progress'
                        }}
                     </button>
                  </ng-container>
               </div>
            </div>

         </ng-container>



      <!-- Limit to best match -->
      <sliderSwitch text="Limit to best location" (toggle)="service.toggleLimitToBest()"
        [defaultValue]="service.limitToBest">
      </sliderSwitch>
    </ng-container>


      <!-- For controlling upload process -->
      <div class="topRowButtons">
         <div class="d-flex align-items-center">






        <!-- If have chosen a file -->
        <ng-container *ngIf="fileName && stage == 'fileChosen'">
          <span class="fileName">File: {{ fileName }}</span>
          <!-- <button class="btn btn-primary" (click)="chooseDifferentFile()">Choose Different File</button> -->


               <!-- Dropdown to choose import mask -->
               <ng-container *ngIf="stage=='fileChosen' && !service.chosenPredefinedCompany">
                  <div ngbDropdown id="inputMaskChoose" class="d-inline-block">


                     <button class="btn btn-primary" id="" ngbDropdownToggle>
                        {{ service.chosenImportMask?.Name }}
                     </button>


                     <div id="importMaskDropdown" ngbDropdownMenu aria-labelledby="dropdownBasic1">

                        <div class="dropdown-divider"></div>

                        <button (click)="chooseImportMask(importMask)" *ngFor="let importMask of importMasks"
                                ngbDropdownItem
                                ngbDropdownToggle class="manualToggleCloseItem">
                           <div class="spaceBetween">
                              <div>{{ importMask.Name }}</div>
                           </div>
                        </button>
                     </div>

                  </div>


                  <button id="saveMask" class="btn btn-success" (click)="possiblySaveImportMap()">
                     <i class="fas fa-save"></i>
                     Save import map
                  </button>
               </ng-container>

          <button type="button" class="btn btn-success upload" [ngClass]="{ 'disable': validateImport() }"
            [ngbPopover]="missingFieldsPopover" [disablePopover]="missingFieldsFromImport == null"
            triggers="mouseenter:mouseleave" (click)="maybeGiveBatchName()">
            <div class="flex">
              <i class="fas fa-files-medical"></i>
              Upload
            </div>
          </button>


            </ng-container>


         </div>
      </div>


   </nav>
</nav>


<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
   <div class="content-new">
      <div class="content-inner-new">

         <!-- DO NOT DELETE -->
         <!--
         <button class="btn h3 btn-primary buttonBlob" (click)="showNewVehicleLoadNodal()">
           New Vehicle Valuation
         </button> -->

         <!-- Landing page -->
         <div *ngIf="stage === 'start'" class="buttonsContainer">

            <div>
               <div>
                  <button class="btn h3 btn-primary buttonBlob" (click)="showVehicleParamsModal()">
                     Single Vehicle Valuation
                  </button>
               </div>

               <div class="mt-3">
                  <button class="btn h3 btn-primary halfButtonBlob" (click)="goToExisting(true)">
                     Previous Single Valuations
                  </button>
               </div>
            </div>

            <div>

               <div>
                  <button class="btn h3 btn-primary buttonBlob" (click)="goToImport()">
                     Bulk Vehicle Valuation
                  </button>
               </div>

               <div class="mt-3">
                  <button class="btn h3 btn-primary halfButtonBlob" (click)="goToExisting(false)">
                     Previous Bulk Valuations
                  </button>
               </div>
            </div>

         </div>

         <!-- Bulk upload page -->
         <ng-container *ngIf="stage=='doBulkUpload'">

            <div style="width: 80%; margin-left: 10%;">
               <instructionRow [message]="pickTypeMessage()"></instructionRow>
            </div>

            <div class="buttonsContainer">


               <button *ngFor="let predefinedImport of predefinedImports" class="btn btn-primary buttonBlob">
                  <div class="uploadFileWrapper">
                     <input class="chooseFileInput" id="{{predefinedImport.Name}}" type="file"
                            (change)="onFileChange($event, predefinedImport)"/>
                     <label for="{{predefinedImport.Name}}">
                        <span *ngIf="predefinedImport.ImageName"> <img src="{{imageSource(predefinedImport)}}"></span>

                        <span class="h1" *ngIf="!predefinedImport.ImageName"> {{ predefinedImport.Name }}</span>
                     </label>
                  </div>
               </button>


            </div>
         </ng-container>


         <!-- When we have chosen a predefined source report -->
         <ng-container
            *ngIf="stage === 'fileChosen' &&
             service.chosenPredefinedCompany "
            id="container">

            <instructionRow [message]="messageIfHaveChosenSpecificAuctionReport()">
            </instructionRow>
            <app-bulkUploadTable *ngIf="predefinedImportTemplatesService.rowData"></app-bulkUploadTable>

         </ng-container>

         <!-- When we are mapping out own report using 'other' -->
         <ng-container
            *ngIf="stage === 'fileChosen' &&
              !service.chosenPredefinedCompany "
            id="container">

            <!-- Header row count choice-->
            <div id="headerRowPicker">

               <!-- Choose header rows skip -->
               <instructionRow [message]="introMessage() "></instructionRow>
               <instructionRow [message]="step1Message() "></instructionRow>

               <div class="label">Header Rows to skip:</div>
               <button class="btn btn-primary" (click)="changeHeaderRow(-1)">
                  <i class="fas fa-minus-circle"></i>
               </button>
               <input [ngModel]="service.chosenImportMask?.TopRowsToSkip" class="lightYellow">

               <button class="btn btn-primary" (click)="changeHeaderRow(1)">
                  <i class="fas fa-plus-circle"></i>
               </button>
            </div>

            <!-- Source Table -->
            <div id="excelTableHolderHolder">
               <!-- <instructionRow [message]="chooseImportMapMessage()">
               </instructionRow> -->
               <instructionRow [message]="columnLetterPickMessage() ">
               </instructionRow>

               <div id="excelTableHolder">
                  <bulkUploadExcelSourceTable #bulkUploadExcelMockTable></bulkUploadExcelSourceTable>
               </div>
            </div>

            <div class="tablesHolder">
               <!-- Destination table -->
               <div id="tableHolder">
                  <instructionRow [message]="bottomTableMessage()"></instructionRow>
                  <destinationTable *ngIf="service.destinationTableRowData" #bulkUploadTable></destinationTable>
               </div>
            </div>
         </ng-container>

         <!-- Reports page -->
         <ng-container *ngIf="stage=='viewExistingBatch'">

            <ng-container *ngIf="service.chosenVehicleValuationBatch">
               <div>
                 <instructionRow [message]="welcomMessageIfReportLoaded()"></instructionRow>
               </div>

            </ng-container>

            <ng-container>
               <div class="d-flex flex-column h-100">
                  <batchResultsTable *ngIf="service.batchResultsRowData" [singleMode]="singleMode"></batchResultsTable>
               </div>
            </ng-container>

         </ng-container>
      </div>
   </div>
</div>


<!-- Save Import Mask Modal -->

<ng-template #saveMaskChoiceModal let-modal>

   <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">
         Save Import Map
      </h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
         <span aria-hidden="true">&times;</span>
      </button>
   </div>
   <div class="modal-body ">
      <table class="cph glowRows saveTable ">
         <h4>Choose Map To Replace</h4>

         <tbody>

         <tr (click)="maybeOverwriteImportMask(service.chosenImportMask)">

            <td>
               <i class="fas fa-plus-circle"></i>
               Save as New Map
            </td>

            <td>
               <div>
                  <i class="fas fa-save"></i>
               </div>
            </td>
         </tr>

         <!-- Existing maps -->
         <tr *ngFor="let importMask of importMasks" (click)="maybeOverwriteImportMask(importMask)">
            <td>
               {{ importMask.Name }}
            </td>

            <td>
               <div>
                  <i class="fas fa-save"></i>
               </div>
            </td>
         </tr>


         </tbody>

      </table>


   </div>
   <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
   </div>

</ng-template>

<!-- New map name modal -->
<ng-template #saveMaskNameModal let-modal>

   <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">
         Choose Map Name
      </h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
         <span aria-hidden="true">&times;</span>
      </button>
   </div>
   <div class="modal-body ">

      <input class="mapName" ngbAutofocus [(ngModel)]="service.chosenImportMask.Name"/>


   </div>
   <div class="modal-footer">
      <button type="button" class="btn btn-success" (click)="modal.close()">Save</button>
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
   </div>

</ng-template>


<ng-template #missingFieldsPopover>
   You are missing the following fields:

   <p *ngFor="let field of missingFieldsFromImport">- {{ field }}</p>
</ng-template>


<ng-template #setBatchNameModal let-modal>
   <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">
         Choose a name for this bulk valuation
      </h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
         <span aria-hidden="true">&times;</span>
      </button>
   </div>
   <div class="modal-body">
      <input id="newBatchName" ngbAutofocus [(ngModel)]="newBatchName"/>

      <label *ngIf="constants.autopriceEnvironment.applyPriceScenarios" for="applyPriceScenarios">Apply different price
         scenarios
         <input type="checkbox" id="applyPriceScenarios" [(ngModel)]="applyPriceScenarios"/>
      </label>

      <ng-container *ngIf="needToSetCondition">
         <p class="mt-4 mb-2">Condition defaulted to excellent. You can choose a different condition from the dropdown
            below.</p>

         <div ngbDropdown class="d-inline-block" container="body">
            <button class="btn btn-primary" ngbDropdownToggle>
               {{ selectedCondition }}
            </button>

            <div id="conditionsDropdown" ngbDropdownMenu aria-labelledby="conditionsDropdown">
               <button *ngFor="let condition of conditions" ngbDropdownItem
                       [ngClass]="{ 'active': condition === selectedCondition }" ngbDropdownToggle
                       class="manualToggleCloseItem"
                       (click)="selectedCondition = condition">
                  {{ condition }}
               </button>
            </div>
         </div>
      </ng-container>
   </div>
   <div class="modal-footer">

      <button type="button" class="btn btn-success" (click)="modal.close()">
         OK
      </button>
   </div>

</ng-template>

<ng-template #setVehicleParamsModal let-modal>
   <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">
         Vehicle <span (click)="loadTest()">d</span>etails
      </h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
         <span aria-hidden="true">&times;</span>
      </button>
   </div>
   <div class="modal-body">
      <table id="regMileageConditionTable">
         <tbody>
         <tr>
            <td>Reg</td>
            <td>
               <!-- <input id="newVehicleReg"  [(ngModel)]="newVehicleReg" /> -->
               <input id="regInput" type="text" ngbAutofocus placeholder="Enter reg" class="regInput"
                      (keydown.enter)="handleEnter(modal)" [(ngModel)]="newVehicleReg" maxlength="8">

            </td>
         </tr>
         <tr>
            <td>Mileage</td>
            <td>
               <input id="newVehicleMileage" type="number" placeholder="000000" class="mileageInput"
                      (keydown.enter)="handleEnter(modal)" [(ngModel)]="newVehicleMileage">
            </td>
         </tr>
         <tr>
            <td>Condition</td>
            <td>
               <div ngbDropdown class="d-inline-block" container="body">
                  <button class="btn btn-primary" ngbDropdownToggle>
                     {{ newVehicleCondition }}
                  </button>

                  <div id="conditionsDropdown" ngbDropdownMenu aria-labelledby="conditionsDropdown">
                     <button *ngFor="let condition of conditions" ngbDropdownItem
                             [ngClass]="{ 'active': condition === newVehicleCondition }" ngbDropdownToggle
                             class="manualToggleCloseItem"
                             (click)="newVehicleCondition = condition">
                        {{ condition }}
                     </button>
                  </div>
               </div>
            </td>
         </tr>
         </tbody>
      </table>
   </div>
   <div class="modal-footer">
      <button type="button" class="btn btn-success" (click)="showModal(null);">
         OK
      </button>
      <button type="button" class="btn btn-primary mr-2" (click)="modal.close()">
         Cancel
      </button>
   </div>

</ng-template>
