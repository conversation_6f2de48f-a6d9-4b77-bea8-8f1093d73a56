﻿//using CPHI.Spark.Model.ViewModels;
//using CPHI.Spark.WebApp.DataAccess;
//using System.Collections.Generic;
//using System.Threading.Tasks;

//namespace CPHI.Spark.WebApp.Service
//{
//    public interface IStockListReportService
//    {
//        Task<IEnumerable<ReportVM>> GetReports();
//        Task<int> SaveReport(ReportVM report);
//        Task<int> UpdateReport(ReportVM report);
//        Task<int> DeleteReport(ReportVM report);
//    }

//    public class StockListReportService : IStockListReportService
//    {
//        private readonly IUserService userService;
//        private readonly IStockListReportDataAccess stockListReportDataAccess;
//        private readonly int userId;

//        public StockListReportService(IStockListReportDataAccess stockListReportDataAccess, IUserService userService)
//        {
//            this.stockListReportDataAccess = stockListReportDataAccess;
//            this.userService = userService;
//            this.userId = userService.GetUserId();
//        }

//        public async Task<IEnumerable<ReportVM>> GetReports()
//        {
//            return await stockListReportDataAccess.GetReports(userId, userService.GetUserDealerGroupName());
//        }

//        public async Task<int> SaveReport(ReportVM report)
//        {
//            return await stockListReportDataAccess.SaveReport(report, userId, userService.GetUserDealerGroupName());
//        }

//        public async Task<int> UpdateReport(ReportVM report)
//        {
//            return await stockListReportDataAccess.UpdateReport(report, userId, userService.GetUserDealerGroupName());
//        }

//        public async Task<int> DeleteReport(ReportVM report)
//        {
//            return await stockListReportDataAccess.DeleteReport(report, userService.GetUserDealerGroupName());
//        }

//    }
//}