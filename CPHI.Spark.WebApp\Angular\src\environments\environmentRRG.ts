import { SparkEnvironment } from "src/app/services/environment.service";
import packagejson from '../../package.json';

export const environmentRRG:SparkEnvironment = {
  customer: "RRGUK",
  production: true,
  version: packagejson.version,
  languagePickerOnLogin: false,
  franchisePicker: true,
  stockGroupPicker: true,
  lateCostPicker: true,
  orderTypePicker: true,
  ageingOptions: false,
  displayCurrency: "GBP",
  displayCurrencySymbol: '£',
fAndISummary_includeTargets: false,
  // Better way for this?
  bookingsBar:{
    barStyle1: true,
    barStyle2: false,
  },

  dealDetails:{
    componentName: "DealDetailsRRGComponent",
    profitTableShowSale: true,
    profitTableShowCost: true,
    profitTableShowCommission: true,
    profitTableFinanceCommText: "Finance Comm.",
    showDescription: true,
    showVehicleAge: true,
    showPaintProtection: true,
    paintProtectionText: "Paint Protect",
    showPaintProtectionCost: true,
    showPaintProtectionSale: true,
    showPhysicalLocation: false,
    showIsDealClosed: true,
    showFinanceCo: true,
    showFinanceType: false,
    showWarrantyInfo: false,
    showRCIFinanceComm: true,
    showPCPFinanceComm: false,
    showStandardsCommission: true,
    showProPlusCommission: true,
    showSelectCommission: true,
    showGapInsurance: true,
    showTyreInsurance: true,
    showAlloyInsurance: true,
    showWheelGard: true,
    showServicePlan: true,
    showWarranty: true,
    showUnits: true,
    showDeliverySite: false,
    showAdditionalAddOnProfit: true,
    showCosmeticInsuranceSale: true,
    showCosmeticInsuranceCost: true,
    showCosmeticInsuranceCommission: true,
    showVATCost: true,
  },
  usedStockTable: 
  { 
    vindisFormatting: false,
    tactical: true,
    exManagementCount: true,
    exDemo: true 
  },
  sideMenu:
  { 
    oldStockPricing:false,
    pricingHome:false,
    dashboard: true,
    orderbook: true,
    fleetOrderbook: true,
    dealsDoneThisWeek: true,
    dealsForTheMonth: true,
    whiteboard: true,
    performanceLeague: true,
    performanceTrends: false,
    scratchCards: true,
    salesIncentive: true,
    supercup:false,
    supercup2:false,
    handoverDiary: true,
    distrinet:false,
    reportPortal: false,
    stockList:  true, 
    stockPricing: false,
    stockInsight: true,
    leavingVehicles:false,
    pricingDashboard:false,
    siteDetailDashboard: false,
    localBargains:true,
    applyStrategy: false,
    strategyBuilder:false,
    locationOptimiser:true,
    vehicleValuation:false,
    salesCommission: true,
    salesExecReview: false,
    stockLanding: true,
    liveForecast: false,
    userMaintenance: true,
    autoPriceSiteSettings: true,

    summaryDashboard:false,
    stockReports:true, //this is the main report
    bulkValuation:true,
    optOuts:true,
    todayPriceChanges:true,
    leavingVehicleDetail:true,
    leavingVehicleTrends:true,
  },
  fullSideMenu: 
  { 
    description: " RRG UK Ltd" 
  },
  citNoww: 
  { 
    tileHeader: "CitNOWs as a Proportion of Qualifying WIPs",
    moveNissanValuesToRenault: true, 
    renaultRegions: true, 
    vindisRegions: false, 
    excludeAudi: false,
    pcOfSalesEnquiries: true,
    pcOfInvoicedExtWips: true,
    pcOfSalesEnquiriesText: "CitNOW Summary - Videos as a % of Sales Enquiries - ",
    pcOfInvoicedExtWipsText: "CitNOW Summary - Videos as a % of Invoiced External WIPs - ",
    showSimpleCitNowPersonDetail: false,
    showVideosViewed: false,
    eDynamixView: true
  },
  dealDetailModal: 
  { 
    showOtherProfit: true, 
    showFinanceProfit: true, 
    showAddOnProfit: true,
    showTotalProfit: true ,
    showStockDetailButton:true,
    //  showStockWebsiteListingButton:false,
  },
  dashboard: 
  { 
    sections:[

     

      //Sales dashboard
      {sectionName:"SalesRRG",translatedTextField:"Common_Sales", translatedTextValue:"",  pageName: "dashboardSalesRRG",enableSitesSelector:false, pages:[
        
        {pageName:"SalesPerformance", translatedTextField: "Dashboard_SalesPerformance_Title",translatedTextValue:"",},
        {pageName:"Registrations", translatedTextField: "Dashboard_Registrations_Title",translatedTextValue:""},
        {pageName:"SalesmanEfficiency", translatedTextField: "Dashboard_SalesmanEfficiency_Title",translatedTextValue:""},
        {pageName:"FinanceAddOns", translatedTextField: "Dashboard_FinanceAddons_Title",translatedTextValue:""},
        {pageName:"StockReport", translatedTextField: "Dashboard_StockReport_Title",translatedTextValue:""},
        {pageName:"Debtors", translatedTextField: "Dashboard_Debtors_Title",translatedTextValue:""},
        {pageName:"CitNow", translatedTextField: "Dashboard_CitNow_Title",translatedTextValue:""},
        // {pageName:"Voc", translatedTextField: "Dashboard_VoC_TitleShort",translatedTextValue:""},
      ]},

      //Pricing dashboard - New
      // {sectionName:"AutoPricing",translatedTextField:"Common_Pricing", translatedTextValue:"",  pageName: "dashboardPricing",enableSitesSelector:false, pages:[
      //   {pageName:"stockInsight", translatedTextField: "Dashboard_StockInsight",translatedTextValue:"",},
      //   {pageName:"siteDetailDashboard", translatedTextField: "Dashboard_SiteBySite",translatedTextValue:""},
      //   {pageName:"leavingVehicles", translatedTextField: "Dashboard_VehiclesSold",translatedTextValue:""},
      //   {pageName:"strategyBuilder", translatedTextField: "Dashboard_StrategyBuilder",translatedTextValue:""},
      //   {pageName:"applyStrategy", translatedTextField: "Dashboard_ApplyStrategy",translatedTextValue:""},
      //   {pageName:"locationOptimiser", translatedTextField: "Dashboard_LocationOptimiser",translatedTextValue:""},
      //   {pageName:"vehicleValuation", translatedTextField: "Dashboard_VehicleValuation",translatedTextValue:""},
      //   {pageName:"localBargains", translatedTextField: "Dashboard_LocalBargains",translatedTextValue:""},
      // ]},


      //Aftersales dashboard
      {sectionName:"Aftersales",translatedTextField:"Common_Aftersales", translatedTextValue:"",  pageName: "dashboardAfterSalesRRG",enableSitesSelector:false, pages:[
        {pageName:"ServiceSales", translatedTextField: "Dashboard_ServiceSales_Title",translatedTextValue:""},
        {pageName:"PartsSales", translatedTextField: "Dashboard_PartsSales_Title",translatedTextValue:""},
        {pageName:"ServiceBookings", translatedTextField: "Dashboard_ServiceBookings_Title",translatedTextValue:""},
        {pageName:"PartsStock", translatedTextField: "Dashboard_PartsStock_Title",translatedTextValue:""},
        {pageName:"EVHC", translatedTextField: "Dashboard_Evhc_Title",translatedTextValue:""},
        {pageName:"Debtors", translatedTextField: "Dashboard_Debtors_Title",translatedTextValue:""},
        {pageName:"CitNow", translatedTextField: "Dashboard_CitNow_Title",translatedTextValue:""},
        // {pageName:"Voc", translatedTextField: "Dashboard_VoC_TitleShort",translatedTextValue:""},
        {pageName:"Wip", translatedTextField: "Dashboard_WipReport_Title",translatedTextValue:""},
        {pageName:"Upsells", translatedTextField: "Dashboard_Upsells",translatedTextValue:""},
      ]},
      //Site compare dashboard
      {sectionName:"SiteCompare",translatedTextField:"SiteCompare", translatedTextValue:"",  pageName: "dashboardSiteCompare",enableSitesSelector:false, pages:[
        {pageName:"TelephoneStats", translatedTextField: "Dashboard_TelephoneStats",translatedTextValue:""},

      ]}
     
      
    ],
    canChooseMonth:false,
    showStockCover: true,
    includeExtraSpainMenuButtons:false,
    showZoeSales: true,
    showHandoverDiarySummary: true,
    showCashDebts: true,
    showBonusDebts: true,
    showRenaultRegistrations: true,
    showDaciaRegistrations: true,
    showFleetRegistrations: true,
    showUsedStockMerchandising: true,
    showCommissions: true,
    showFinanceAddonPerformance: true,
    showfAndIPerformanceRRG: true,
    showVocNPSTile: true,
    showActivityLevels: true,
    showActivityOverdues:true,
    showFleetPerformance: true,
    showVoC: true,
    showServiceBookings: true,
    showCitNow: true,
    showImageRatios: false,
    showSalesmanEfficiency: true,
    showEvhc: true,
    showFinanceAddons: true,
    showRegistrations: true,
    showSimpleDealsByDay: false,
    excludeTypesFromBreakDown: ["Used"],
    showFinanceAddonsAllSites: false,
    includeDemoStockInStockHealth:false,
  },
  debts: 
  { 
    agedDebts: true,
    includeBonuses: true,
    simpleSitesTable: true,
    showAgedOnPicker: true,
    showBonusDebtType: true
  },
  evhcTile: "Renault",
  allGroups: ['R', 'O', 'N', 'Z', 'T', 'D'],
  allFamilyCodes: ['AxsBonus', 'BodyGrowth', 'BodyHandling', 'Captive', 'Motrio', 'OeCompMech', 'OeCompOther', 'Oil', 'Other'],
  horizontalBar:{
    title: 'ExDemo',
    exDemo: 'params.data.ExDemo',
    forRenault: true,
    forVindis: false
  },
  stockItemModal: 
  { 
    onlineMerchandising: true 
  },
  wipsTable: 
  { 
    hideBookingColumn: false,
    hideDepartmentColumn: false,
    hideAccountColumn: false,
    hideDueDateOutColumn: false,
    hideWDateInColumn: false,
    hideWDateOutColumn: false,
    hidePartsColumn: false,
    hideOilColumn: false,
    hideLabourColumn: false,
    hideSubletColumn: false,
    hideProvisionColumn: false,
    hideCreatedColumn: false,
    hideNotesColumn: false,
  },
  stockTable: {
    hideTacticalColumn: false,
    hideExManagementColumn: false,
    hideExDemoColumn: false,
  },
  stockList: {
    hideStockDetailsModal: false,
    tableColumns: [
      "Id",
      "SiteDescription",
      "StockNumberFull",
      "Reg",
      "VehicleType",
      "ProgressCode",
      "DaysInStock",
      "DaysAtBranch",
      "Make",
      "Model",
      "ModelYear",
      "Description",
      "DisposalRoute",
      "PreviousUseCode",
      "Siv",
      "CarryingValue",
      "IsVatQ",
      "CapProvision",
      "StockcheckLocation",
      "SeenAtLatestStkchk",
      "PriceChanges",
      "AttentionGrabber","WebSiteCreatedDate","WebsitePrice","PriceExtraLine","DaysOnLine","ImagesCount","VideosCount",
      "RRGSiteItemStockId",
      "CapValue","IsOnWebsite","PricedProfit",
      'VariantClass',
      'VehicleTypeCode',
      'VehicleSuperType',
      'AccountStatus',
      'Colour',
      'Mileage',
      'Fuel',
      'Doors',
      'Transmission',
      'Options',
      'ShouldBePrepped',
      'IsPrepped',
      'Purchased',
      'Selling',
      'NonRecoverableCosts',
      'DealerFitAccessories',
      'OptionCosts',
      'CapID',
      'CapNotes',
      'CapCode'
    ],
    franchises: ["R", "N", "D", "A", "Z"],
  },
  sitesLeague:{
    includeToday: true,
  },
  performanceLeague: {
    hideBadges: false,
    showDeliveredButton: true,
    incLeaversButton: true,
    showExecAndManagerSelector: false,
  },
  overAgeStockTable: {
    hideDemoColumn: false,
    hideTacticalColumn: false,
    hideExManagementColumn: false,
    hideExDemoColumn: false,
    hideTradeColumn: false,
    usedColumnName: "CoreUsed"
  },
  dealPopover:{
    showMetalProfit: true,
    showOtherProfit: true,
    showFinanceProfit: true,
    showAddons: true,
    showAddonProfit: true
  },
  orderBook:  {
    showNewOrderbook:false,
    showNewDealButton:false,
    ordersDescription: 'Orders approved between',
    hideDeliverySiteColumn: true,
    hideVehicleTypeColumn:false,
    hideOemReferenceColumn: true,
    hideFinanceProfitColumn:false,
    hideVehClassColumn: false,
    hideModelColumn: false,
    hideModelYearColumn: false,
    hideVehicleSourceColumn: false,
    hideDaysToDeliverColumn: true,
    hideDaysToSaleColumn: true,
    hideLocationColumn: true,
    hideIsConfirmedColumn: true,
    hideVehTypeColumn: false,
    hideIsClosedColumn: true,
    hideUnitsColumn: false,
    hideFinanceTypeColumn: true,
    hideIsLateCostColumn: false,
    hideAddonsColumn: false,
    hideDiscountColumn: true,
    hideOtherProfitColumn: false,
    hideMetalColumn: false,
    hideSalesChannel: false,
    hideComments: false,
    hideOrderAllocationDate: false,
    hideChannelColumn: true,
    hideTypeColumn: true,
    includeAccgDate: true,
    customDateTypes: ["Delivery", "Invoice", "Accounting"],
    defaultDateType: "Accounting",
    showLateCost: true,
    showOrderOptions: true,
    showAccountingDateButton:true,
    showDeliveryOptionButtons:false,
    showMetalSummary: true,
    showOtherSummary: true,
    showFinanceSummary: true,
    showInsuranceSummary: true,
    siteColumnWidth: 80,
    customerColumnWidth: 130,
    vehicleClassColumnWidth: 30,
    salesExecColumnWidth: 100,
    descriptionColumnWidth: 200,
    hideOrderDateSelection: false,
    hideAuditColumn: true,
    showManagerSelector: false,
    hideDateFactoryTransportationColumn: true,
    hideDateVehicleReconditionColumn: true,
    hideDateSiteTransportationColumn: true,
    hideDateSiteArrivalColumn: true,
    hideReservedDateColumn: true,
  },
  partsSales: 
  { 
      showMarginColPerc: true,
      showMarginCol: true,
      includeMarginCols: true
  },
  handoverDiary: 
  { 
    includeCustomerName: true,
    includeLastPhysicalLocation: false,
    includeHandoverDate: true,
    isInvoiced: true,
    isConfirmed: false,
    futureHandoversGreyedOut: false,
    showManagerSelector: false
  },
  
  partsStockDetailedTable:{
    hideCreatedColumn: false,
    partStockBarCharts1: {
      headerName: "% >1 year",
      field: "PartsStockRRG.PercentOver1yr",
      colId: "PartsStockRRG.PercentOver1yr"
    },
    partStockBarCharts2: null,
    showPartStockAgeingColumnsForRRG: true,
    showPartStockAgeingColumnsForVindis: false,
    hideOfWhichColumn: false,
    hideDeadValueColumn: false,
    hideDormantValueColumn: false,
    hideDeadProvColumn: false,
    hideDormantProvColumn: false,
    setClassesForVindis:false,
    setClassesForRRG:true
  },
  salesPerformance: 
  { 
      description: "Orders approved between",
      showFranchisePicker: true,
      showLateCostButtons: true,
      showIncludeExcludeOrders: true,
      showTradeUnitButtons: true,
      showMotabilityButtons: true,
      showOrderRateReportType:true,
      showCustomReportType: true,
      showAllSites: false
  },
  selectionsService: {
    ageingOptions: [
      { description: "30 days", ageCutoff: 30 },
      { description: "45 days", ageCutoff: 45 },
      { description: "60 days", ageCutoff: 60 },
      { description: "90 days", ageCutoff: 90 },
      { description: "120 days", ageCutoff: 120 },
      { description: "150 days", ageCutoff: 150 },
      { description: "180 days", ageCutoff: 180 },
    ],
    ageingOption: { description: "60 days", ageCutoff: 60 },
    deliveryDateDateType: "Accounting",
    eligibleForCurrentUserCheck: true
  },
  serviceBookingsTable:{
    showPrepHours: true,
    clickSiteEnable: true
  },
  stockReport:
  { 
    showAgePicker: true,
    hideOnRRGSiteCol: false,
    initialStockReport: "Dashboard_PartsStock_UsedStock",
    seeUsedStockReport: true,
    seeAllStockReport: true,
    seeUsedMerchandisingReport: true,
    seeOverageStockReport: true,
    seeStockGraphsReport: false,
    seeStockByAgeReport: false,
    includeReservedCarsOption: true
  },
  whiteboard:{
    showConfirmed: false,
    showNotConfirmed: false,
    showFinance: true,
    showAddons: true,
    showLateCostPicker: true,
    showManagerSelector: false
  },
  serviceChannels:[
    { displayName: "Retail", name: "Retail", channelTags: ["retail"], icon: "fas fa-wrench", hasHours: true, divideByChannelName: "Retail", isLabour:false},
    { displayName: "Internal", name: "Internal", channelTags: ["internal"], icon: "fas fa-car-wash", hasHours: true, divideByChannelName: "Internal", isLabour:false},
    { displayName: "Warranty", name: "Warranty", channelTags: ["warranty"], icon: "fas fa-engine-warning", hasHours: true, divideByChannelName: "Warranty", isLabour:false},
    { displayName: "Labour", name: "Labour", channelTags: ["retail", "internal", "warranty"], isTotal: true, icon: "", hasHours: true, divideByChannelName: "Labour", isLabour:false},
    { displayName: "Tyre", name: "Tyre", channelTags: ["tyre", "sublet"], icon: "fas fa-tire", hasHours: true, divideByChannelName: "Retail", isLabour:false},
    { displayName: "Oil", name: "Oil", channelTags: ["oilWarr", "oilExt", "oilInt", "oil"], icon: "fas fa-oil-can", hasHours: true, divideByChannelName: "Retail", isLabour:false},
    { displayName: "Total", name: "Total", channelTags: ["retail", "internal", "warranty", "tyre", "oilWarr", "oilExt", "oilInt", "oil", "sublet"], isTotal: true, icon: "", hasHours: true, divideByChannelName: "Labour", isLabour:false},
  ],

  partsChannels:[
    {displayName: "Retail",  name: "Retail", channelTags: ["retail", "nonfran", "network", "trade"], icon: "fas fa-wrench", channelTag: "retail", hasHours: false, divideByChannelName: "Retail", isLabour: false}, //added network in on 28Aug20
    {displayName: "Internal",  name: "Internal", channelTags: ["internal"], icon: "fas fa-car-wash", channelTag: "internal", hasHours: false, divideByChannelName: "Internal", isLabour: false},
    {displayName: "Workshop Internal", name: "Workshop Internal", channelTags: ["wshopInternal"], icon: "fas fa-car-wash", channelTag: "wshopInternal", hasHours: false, divideByChannelName: "Workshop Internal", isLabour: false},
    {displayName: "Workshop Retail", name: "Workshop Retail", channelTags: ["wshopRetail"], icon: "fas fas fa-tire ", channelTag: "wshopRetail", hasHours: false, divideByChannelName: "Workshop Retail", isLabour: false},
    {displayName: "Workshop Warranty", name: "Workshop Warranty", channelTags: ["wshopWarranty"], icon: "fas fa-engine-warning", channelTag: "wshopWarranty", hasHours: false, divideByChannelName: "Workshop Warranty", isLabour: false},
    {displayName: "Total",  name: "Total", isTotal: true, channelTags: ["retail", "nonfran", "internal", "wshopInternal", "wshopRetail", "wshopWarranty"], icon: "", channelTag: "total", hasHours: false, divideByChannelName: "Total", isLabour: false},
  ],
  initialPageURL:"/dashboard",

  orderBookURL: "/orderBook",
  fleetOrderbookURL: "/fleetOrderbook",
  product:
  { 
    tyreInsurance: "IsTyre",
    tyreAlloyInsurance: "IsTyreAlloy",
    showAlloyInsurance: false,
  },
  dealDone:{
    showVindisSitePicker: false,
    showRRGSitePicker: true,
    showRRGPopoverContent: true,
    showVindisPopoverContent: false,
  },
  evhc:{
    showTechTable: true,
    vehiclesCheckedPercent: 100,
    workQuoted: 205,
    workSoldPercent: 65,
    eDynamixView: true,
    redWorkSoldPercent: 65,
    amberWorkSoldPercent: 25
  },
  fAndISummary:{
    processTypeAndTypeAlloy: true,
    hideAlloyColumn: true
  },
  partsStock:{
    includeOfWhichColumns: true
  },
  dealsForTheMonth:{
    showMetal: true,
    showOther: true,
    showFinance: true,
    showAddons: true,
    showGpu: true,
    showBroughtInColumn: true,
    showLateCostPicker: true,
    showIncludeExcludeOrders: true
  }, 
  partsStockSitesCoverTable: {
    partStockName: "PartsStockRRG",
  },


  dealsDoneThisWeek:{
    showPlotOptions: true
  },
  orderTypePickerOptions:{
    showRetail: true,
    showFleet: true
  },
  todayMap:{
    defaultPositionLat: 52.698926,
    defaultPositionLong: -1.046534,
    defaultZoom: 7,
  },
  vehicleTypePicker:
  { 
    showUsed: true,
    showNew: true,
    showAll: true,
    hiddenVehicleTypes: []
  },
  userSetup:{
    hideUploadReports: true,
    hideViewReports: true,
    hideCommReview: false,
    hideCommSelf: false,
    hideSerReviewer: true,
    hideSerSubmitter: true,
    hideStockLanding: false,
    hideSuperCup: false,
    hideIsSalesExec: false,
    hideAllowReportUpload: true,
    hideAllowReportCentre: true,
    hideLiveforecast: true,
    hideCanEditExecManagerMappings: true,
    hideSalesRoles: false,
    hideTMgr: true,
    allSalesRoles: ['New', 'Used', 'None', 'NewUsed', 'Fleet'],
    canReviewStockPrices: true,
    canActionStockPrices: true,
    canEditStockPriceMatrix: true
  },
  languageSelection: false,


  //   "CoreUsed", "Demo", "ExDemo", "ExManagement", "Tactical", "Total Used"
  // ],
  serviceSummary: {
    showTableTypeSelector: true,
    defaultTableType: 'Cumulative',
    tableTypes: ['Cumulative','Daily'],
    defaultTimeOption: "MTD",
    timeOptions: ["MTD", "WTD", "Yesterday"],
    showTechGroupColumns: false,
  },
  partsSummary: {
    showTableTypeSelector: true,
    defaultTableType: 'Cumulative',
    tableTypes: ['Cumulative','Daily'],
  },
  serviceSalesDashboard: {
    onlyLabour: false
  },
  dealDetailsModal: {
    currencyDP: 0,
    costColumnTranslation: 'Common_CoS',
    dealDetailsSection: {
      showVariant:false,
      showWebsiteDiscount:true,
      showFinanceType: false,
      showOEMReference: false,
      showQualifyingPartEx: true,
      showPhysicalLocation: false,
      showIsClosed: false,
      showFinanceCo: true,
      showDescription: true,
      showUnits: true,
      showVehicleAge: true,
      showIsLateCost: true,
      showAuditPass: false,
      showInvoiceNo: false,
    },
    metalProfitSection: {
      headerTranslation: 'DealDetails_TinProfit',
      showVATCost: false
    },
    otherProfitSection: {
      showRegBonus:true,
      showIntroComm:true,
      showBrokerCost:true,
      showAccessories:true,
      showPaintProtectionAccessory: false,
      showFuel:true,
      showDelivery:true,
      showStandardWarranty:true,
      showPdi:true,
      showMechPrep:true,
      showBodyPrep:true,
      showOther: false,
      showError:true,
      showTotal:true
    },
    addonsSection:{
      showPaintProtection:true,
      showWarrantyForNewCar:true
    },
    datesSection: {
      showCustomerDestinationDeliveryDate: true,
      showEnterImportCentreDate: true,
      showShipDate: true,
      showExitImportCentreDate: true,
      showAllocationDate: true,
      showDateVehicleRecondition: false,
      showDateFactoryTransportation: false,
      showDateSiteArrival: false,
      showDateSiteTransportation: false
    },
    financeProfitSection: {
      show: true,
      rciFinanceCommissionText: 'DealDetails_RciFinanceCommission',
      financeCommissionText: 'DealDetails_FinanceCommission',
      showSelectCommission: true,
      showProPlusCommission: true,
      showStandardsCommission: true
    },
    showTotalProfitExludingFactoryBonusSection: false,
    showTotalProfitSection: true
  },
  donutShowLastYearUnits: false,
  showNewUsedSummaryBadges: true,
  showPrepCostsWhenValuing: true,
  isSingleSiteGroup: false,

  showChangePriceNowInputAlways:false,
  menuItems: {
    dashboard_HasDashboard: true,
    dashboard_Home: false,
    dashboard_Overview: false,
    dashboard_Sales: true,
    dashboard_NewKPIs: false,
    dashboard_UsedKPIs: false,
    dashboard_Aftersales: true,
    dashboard_SiteCompare: true,
    orderbook: false,
    orderbook_HasOrderbook: true,
    orderbook_Retail: true,orderbook_Distrinet:false,
    orderbook_Fleet: true,
    operationalReports_HasOperationReports: true,
    operationalReports_DealsWeek: true,
    operationalReports_DealsMonth: true,
    operationalReports_Whiteboard: true,
    operationalReports_HandoverDiary: true,
    operationalReports_TelephoneStats: true,
    operationalReports_StockLanding: true,
    operationalReports_PerformanceTrends: false,
    salesReports_HasSalesReports: true,
    salesReports_SalesPerformance: true,
    salesReports_Alcopas: false,
    salesReports_OrderRate: false,
    salesReports_Registrations: true,
    salesReports_FAndI: true,
    salesReports_StockReports: true,
    salesReports_StockList: true,
    salesReports_Debtors: true,
    salesReports_CitNOW: true,
    salesReports_ImageRatios: false,
    salesReports_GDPR: false,
    salesReports_Activities: false,
    reportPortal: false,
    aftersalesReports_HasAftersalesReports: true,
    aftersalesReports_ServiceSales: true,
    aftersalesReports_ServiceBookings: true,
    aftersalesReports_EVHC: true,
    aftersalesReports_Upsells: true,
    aftersalesReports_WIPReport: true,
    aftersalesReports_Debtors: true,
    aftersalesReports_CitNOW: true,
    aftersalesReports_PartsSales: true,
    aftersalesReports_PartsStock: true,
    peopleReports_HasPeopleReports: true,
    peopleReports_PerformanceLeague: true,
    peopleReports_SalespersonEffeciency: true,
    peopleReports_SalespersonCommission: true,
    peopleReports_Scratchcard: true,
    peopleReports_SalesExecReview: false,
    vehiclePricing_HasVehiclePricing: true,vehiclePricing_ShowDetailedMenu:false,
    vehiclePricing_Dashboard: true,vehiclePricing_SitesLeague:true,
    vehiclePricing_StockReport: true,
    vehiclePricing_TodaysPriceChanges: true,
    vehiclePricing_OptedOutVehicles: true,
    vehiclePricing_LocationOptimiser: true,
    vehiclePricing_VehicleValuation: true,vehiclePricing_Home:true,
    vehiclePricing_BuyingOpportunities: true,
    vehiclePricing_LeavingVehicleTrends: true,
    vehiclePricing_LeavingVehicleDetail: true,
    vehiclePricing_SiteSettings: true,
    vehiclePricing_StockQuickSearch: true,
    userMaintenance: true
  },
  showRotationButton:false,
  showLatestSnapshotDate: false,
  showApproveAutoPrices:true,
  showNestedSideMenu: true,
  // autoprice: {
  //   defaultShowUnpublishedAds:false,
  //   defaultShowNewVehicles: false,
  //   vehicleValuationShowCostingDetail: true,
  //   lifecycleStatusDefault: ['FORECOURT','SALE_IN_PROGRESS'],
  //   allowChooseNewStrategy:true,
  //   allowTestStrategy:false,
  //   vehicleTypes: null,
  //   defaultVehicleTypes: null,
  //   separateBuyingStrategy:false,separateBuyingStrategy2:false,
  //   applyPriceScenarios: false,
  //   stockReport: {
  //     showDMSSellingPrice_Col: false,
  //     showVsDMSSellingPrice_Col: false, 
  //     showPhysicalLocation_Col: false,

  //   },
  //   defaultToDaysInStock:false,
  // },

  vehiclePricing_StockReport_showBcaColumns: true,
  dealershipBackgroundImageName: 'RRGUK',
  homeIsLandingPage: false,
  showRegionFilterOnSiteDashboard: false

  };