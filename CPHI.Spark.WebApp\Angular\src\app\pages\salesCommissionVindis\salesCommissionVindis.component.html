<nav class="navbar">

  <nav class="generic">
    <h4 id="pageTitle">
      <div>

        {{ constants.translatedText.Sales }} {{ constants.translatedText.Commission }}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <ng-container>



        <!-- FOR SELECTING MONTH -->
        <div class="buttonGroup">
          <!-- previousMonth -->
          <button class="btn btn-primary" (click)="changeMonth(-1)"><i class="fas fa-caret-left"></i></button>

          <!-- dropdownMonth -->
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button (click)="makeMonths(0)" class="btn btn-primary centreButton"
              ngbDropdownToggle>{{service.chosenMonth|cph:'month':0}}</button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

              <!-- the ngFor buttons -->
              <button *ngFor="let month of months" (click)="selectMonth(month.startDate)"
                ngbDropdownItem>{{month.name}}</button>

            </div>
          </div>
          <!-- nextMonth -->
          <button class="btn btn-primary" (click)="changeMonth(1)"><i class="fas fa-caret-right"></i></button>
        </div>



      <div *ngIf="selections.user.permissions.reviewCommission && !service.chosenPerson" class="buttonGroup">
        <button class="btn btn-primary" (click)="openAdjustmentsModal()">Review Adjustments</button>
      </div>

      <div class="buttonGroup">
        <button class="btn btn-primary" [ngClass]="{ 'active': service.ignoreAuditPass }" (click)="toggleAuditPass()">
          Include Audit Incomplete/Fails
        </button>
      </div>
      
      
      <div class="buttonGroup" *ngIf="service.selections.user.RoleName==='System Administrator'">
        <button class="btn btn-danger"  [disabled]="!enableLockMonthButton()" id="chooseLockMonth" (click)="onChooseLockMonthClick()">
          Lock {{service.chosenMonth|cph:'monthName':0}} Commissions (!)
        </button>
      </div>



      <!-- <div class="buttonGroup">

        <button [ngClass]="{'active':timePeriod.label == selections.salesmanEfficiency.timePeriod.label}" *ngFor="let timePeriod of selections.salesmanEfficiency.timePeriods" 
        (click)="chooseTimePeriod(timePeriod)" class="btn btn-primary">{{timePeriod.label}}</button>
      </div> -->





    </ng-container>



  </nav>

  <nav class="pageSpecific">


  </nav>
</nav>



<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">
    <div class="content-inner-new">


      <div id="gridHolder">


        <ng-container *ngIf="selections.user.permissions.selfOnlyCommission; else seeAllSites;">
          <!-- Person Statement -->
          <ng-container *ngIf="service.chosenPerson; else noStatement;">
            <commissionReport [statement]="service.chosenPerson"></commissionReport>
          </ng-container>

          <ng-template #noStatement>
            <span>No statement available for the chosen month</span>
          </ng-template>
        </ng-container>

        <ng-template #seeAllSites>
          <!-- Sites Table -->
          <div class="tableContainer" *ngIf="!!service.siteRows && !service.chosenSite && !!service.showTables">
            <commissionSitesTableVindis id="salesCommission" [isRegionsTable]="false" [rowData]="service.siteRows"></commissionSitesTableVindis>
            <div class="tableSpacer"></div>
            <commissionSitesTableVindis [isRegionsTable]="true" [rowData]="service.siteRows"></commissionSitesTableVindis>
          </div>


          <!-- People Table -->
          <div class="tableContainer"
            *ngIf="service.peopleRows && !!service.chosenSite && !service.chosenPerson && !!service.showTables">
            <button class="btn btn-primary backButton" (click)="backToSites()">
              < </button>

                <!-- We will use this when it is done.  For now we just do a simple hack -->
                <commissionPeopleTableVindis [rowData]="service.peopleRows" [pinnedBottomData]="service.chosenSite"></commissionPeopleTableVindis>
          </div>

          <!-- Person Statement -->
          <ng-container *ngIf="(!!service.siteRows && !!service.chosenPerson && !!service.showTables) || selections.user.permissions.selfOnlyCommission">
            <commissionReport [statement]="service.chosenPerson"></commissionReport>
          </ng-container>
        </ng-template>

      </div>





    </div>


  </div>
</div>