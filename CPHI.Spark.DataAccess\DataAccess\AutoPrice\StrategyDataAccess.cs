﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IStrategyDataAccess
   {
      Task CreateNewFactor(StrategyFactor newFactor, int userId);
      Task CreateNewFactorItems(List<StrategyFactorItem> newFactorItems, int userId);
      Task CreateStrategyVersion(StrategyVersion strategyVersion, int userId);
      Task<List<StrategyVersionVM>> GetAllPricingPolicies(DealerGroupName dealerGroup);

      Task<StrategyVersion> GetStrategyVersion(int strategyVersionId, DealerGroupName dealerGroupName);
      Task<bool> HasStrategyFactorBeenUsedToGenerateStrategyPrices(int strategyFactorId);
      Task<bool> HasStrategyFactorItemBeenUsedToGenerateStrategyPrices(int strategyFactorItemId);
      Task RemoveStrategyFactors(List<StrategyFactor> factorsToRemove, int userId);
      Task SaveStrategyFactors(List<StrategyFactor> newFactors, int userId);
      Task UpdateFactorItems(List<StrategyFactorItem> changedFactorItems, int userId);
      Task UpdateStrategyVersion(StrategyVersionVM incomingStrategyVersion, int userId);
      Task<bool> HasStrategySelectionRuleSetBeenUsedToGenerateStrategyPrices(int rulesetId);
      Task<List<StrategyFieldName>> GetStrategyFieldNames();
      Task<List<StrategyFull>> GetAllStrategies(DealerGroupName dealerGroup);
      Task DeleteStrategy(DealerGroupName dealerGroup, int strategyId, int userId);
      Task DeleteStrategyVersion(DealerGroupName dealerGroup, int strategyVersionId, int userId);
      Task DeleteFactorItems(List<StrategyFactorItem> removeItems, int userId);
   }

   public class StrategyDataAccess : IStrategyDataAccess
   {

      private readonly string _connectionString;
      public StrategyDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }



      public async Task<IEnumerable<StrategyPriceBuildUp>> GetStrategyPriceBuildUp(int advertId, DateTime snapshotDate)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("advertId", advertId);
            paramList.Add("snapshotDate", snapshotDate);

            return await dapper.GetAllAsync<StrategyPriceBuildUp>("autoprice.GET_StrategyPriceBuildUp", paramList, commandType: System.Data.CommandType.StoredProcedure);
         }
      }



      public async Task<StrategyVersion> GetStrategyVersion(int strategyVersionId, DealerGroupName dealerGroupName)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var res = await db.StrategyVersions
                .Include(x => x.StrategyFactors)
                .ThenInclude(x => x.StrategyFactorItems)
                .FirstOrDefaultAsync(x => x.CreatedBy.DealerGroup_Id == (int)dealerGroupName && x.Id == strategyVersionId);
            if (res == null)
            {
               throw new Exception("Not found");
            }
            else
            {
               return res;
            }
         }
      }

      public async Task<List<StrategyFull>> GetAllStrategies(DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var dbItems = await db.StrategySelectionRuleSets
                                 .Where(x => x.CreatedBy.DealerGroup_Id == (int)dealerGroup)
                                 .Include(x => x.StrategySelectionRules)
                                    .ThenInclude(x => x.StrategyVersion)
                                 .Include(x => x.StrategySelectionRules)
                                     .ThenInclude(y => y.CreatedBy)
                                 .Include(x => x.StrategySelectionRules)
                                     .ThenInclude(y => y.StrategySelectionCriterias)
                                         .ThenInclude(z => z.StrategyFieldName)
                                 .Include(x => x.DefaultStrategyVersion)
                                     .ThenInclude(x => x.CreatedBy)
                                 .Select(x => new
                                 {
                                    RuleSet = x,
                                    HasVehicleAdvertSnapshots = db.StrategyFactorItemVehicleWebsiteRatings
                                                                  .Any(vas => vas.StrategySelectionRuleSet_Id == x.Id)
                                 })
                                 .AsNoTracking() // No point locking the records, increases performance
                                 .ToListAsync();

            return dbItems.Select(x => new StrategyFull(x.RuleSet, x.HasVehicleAdvertSnapshots)).ToList();
         }
      }


      public async Task DeleteStrategy(DealerGroupName dealerGroup, int strategyId, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Find the StrategySelectionRuleSet
            var dbItem = await db.StrategySelectionRuleSets
                                 .Include(x => x.StrategySelectionRules)
                                 .ThenInclude(r => r.StrategySelectionCriterias)
                                 .FirstOrDefaultAsync(x => x.Id == strategyId && x.CreatedBy.DealerGroup_Id == (int)dealerGroup);

            if (dbItem == null)
            {
               throw new KeyNotFoundException("StrategySelectionRuleSet not found.");
            }

            // Loop through each rule and delete its associated criterias
            foreach (var rule in dbItem.StrategySelectionRules)
            {
               db.StrategySelectionCriterias.RemoveRange(rule.StrategySelectionCriterias);
            }

            // Delete the rules
            db.StrategySelectionRules.RemoveRange(dbItem.StrategySelectionRules);

            // Delete the StrategySelectionRuleSet
            db.StrategySelectionRuleSets.Remove(dbItem);

            // Save changes to the database
            await db.SaveChangesWithAuditAsync(userId);
         }
      }

      public async Task DeleteStrategyVersion(DealerGroupName dealerGroup, int strategyVersionId, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Find the StrategySelectionRuleSet
            var dbItem = await db.StrategyVersions
                                 .Include(x => x.StrategyFactors)
                                 .ThenInclude(r => r.StrategyFactorItems)
                                 .FirstOrDefaultAsync(x => x.Id == strategyVersionId && x.CreatedBy.DealerGroup_Id == (int)dealerGroup);

            if (dbItem == null)
            {
               throw new KeyNotFoundException("StrategySelectionRuleSet not found.");
            }

            // Loop through each factor and delete its associated factorItems
            foreach (var strategyFactor in dbItem.StrategyFactors)
            {
               db.StrategyFactorItems.RemoveRange(strategyFactor.StrategyFactorItems);
            }

            // Delete the factors
            db.StrategyFactors.RemoveRange(dbItem.StrategyFactors);

            // Delete the StrategyVersion
            db.StrategyVersions.Remove(dbItem);

            // Save changes to the database
            await db.SaveChangesWithAuditAsync(userId);
         }
      }



      public async Task<List<StrategyVersionVM>> GetAllPricingPolicies(DealerGroupName dealerGroup)
      {
         List<StrategyVersionVM> strategyVersionVMs = new List<StrategyVersionVM>();

         List<StrategyVersionUsageItem> usages = new List<StrategyVersionUsageItem>();
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("dealerGroupId", (int)dealerGroup);
            var res = await dapper.GetAllAsync<StrategyVersionUsageItem>($"[autoprice].[GET_StrategyVersionUsage]", paramList, System.Data.CommandType.StoredProcedure);
            usages = res.ToList();
         }


         using (var db = new CPHIDbContext(_connectionString))
         {
            List<StrategyVersion> results = await db.StrategyVersions
            .Include(s => s.CreatedBy)
            .Include(s => s.StrategyFactors)
                .ThenInclude(x => x.StrategyFactorItems)
            .Where(x => x.CreatedBy.DealerGroup_Id == (int)dealerGroup)

            .ToListAsync();
            //.Select(g => g.OrderByDescending(x => x.Id).FirstOrDefault())

            // Create a dictionary to map StrategyVersionId to usage details
            var usageDictionary = usages.ToDictionary(
                u => u.StrategyVersionId,
                u => new { u.FirstUsed, u.LastUsed });

            List<StrategyVersionVM> toReturn = new();
            foreach (var result in results)
            {
               var usage = usageDictionary.TryGetValue(result.Id, out var details) ? details : null;
               toReturn.Add(new StrategyVersionVM(result, usage?.FirstUsed, usage?.LastUsed));
            }
            return toReturn;

         }
      }



      public async Task<bool> HasStrategyFactorBeenUsedToGenerateStrategyPrices(int strategyFactorId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var usageCount = await db.StrategyFactorItemVehicleWebsiteRatings
               .Where(x => x.StrategyFactorItem.StrategyFactor.Id == strategyFactorId)
               .Where(x => x.IsRelatedToTestStrategy == false)
               .CountAsync();

            //perhaps we should be saving the steps to build up valuations, then we could query here if there are any

            return usageCount > 0;
         }
      }

      public async Task<bool> HasStrategyFactorItemBeenUsedToGenerateStrategyPrices(int strategyFactorItemId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.StrategyFactorItemVehicleWebsiteRatings
                   .AnyAsync(x => x.StrategyFactorItem.Id == strategyFactorItemId);
         }
      }

      public async Task<bool> HasStrategySelectionRuleSetBeenUsedToGenerateStrategyPrices(int rulesetId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.StrategyFactorItemVehicleWebsiteRatings
                   .AnyAsync(x => x.StrategySelectionRuleSet_Id == rulesetId && x.IsRelatedToTestStrategy == false);

         }
      }

      public async Task<List<StrategyFieldName>> GetStrategyFieldNames()
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.StrategyFieldNames.ToListAsync();
         }
      }

      public async Task CreateStrategyVersion(StrategyVersion strategyVersion, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.StrategyVersions.AddAsync(strategyVersion);
            await db.SaveChangesWithAuditAsync(userId);
         }
      }



      public async Task SaveStrategyFactors(List<StrategyFactor> newFactors, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.StrategyFactors.AddRangeAsync(newFactors);
            await db.SaveChangesWithAuditAsync(userId);
         }
      }





      public async Task CreateNewFactor(StrategyFactor newFactor, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.StrategyFactors.AddAsync(newFactor);
            await db.SaveChangesWithAuditAsync(userId);
         }
      }

      public async Task CreateNewFactorItems(List<StrategyFactorItem> newFactorItems, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.StrategyFactorItems.AddRangeAsync(newFactorItems);
            await db.SaveChangesWithAuditAsync(userId);
         }
      }

      public async Task DeleteFactorItems(List<StrategyFactorItem> removeItems, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            //firstly remove any associated factorItemVehicleWebsiteRatings
            List<int> removeItemIds = removeItems.Select(x => x.Id).ToList();
            List<StrategyFactorItemVehicleWebsiteRating> ratingItemsToRemove = db.StrategyFactorItemVehicleWebsiteRatings.Where(x => removeItemIds.Contains((int)x.StrategyFactorItem_Id)).ToList();
            db.StrategyFactorItemVehicleWebsiteRatings.RemoveRange(ratingItemsToRemove);
            db.StrategyFactorItems.RemoveRange(removeItems);
            await db.SaveChangesWithAuditAsync(userId);
         }
      }


      public async Task UpdateFactorItems(List<StrategyFactorItem> changedFactorItems, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            foreach (var item in changedFactorItems)
            {
               var existing = db.StrategyFactorItems.First(x => x.Id == item.Id);
               existing.Comment = item.Comment;
               existing.Value = item.Value;
            }
            await db.SaveChangesWithAuditAsync(userId);
         }
      }





      public async Task RemoveStrategyFactors(List<StrategyFactor> factorsToRemove, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            db.StrategyFactors.RemoveRange(factorsToRemove);   //strange there's no async method
            await db.SaveChangesWithAuditAsync(userId);
         }
      }


      public async Task UpdateStrategyVersion(StrategyVersionVM incomingStrategyVersion, int userId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var dbStrategy = db.StrategyVersions.First(x => x.Id == incomingStrategyVersion.Id);
            dbStrategy.Comment = incomingStrategyVersion.Comment;
            dbStrategy.Name = incomingStrategyVersion.Name;
            dbStrategy.LastUpdatedDate = DateTime.Now;
            dbStrategy.LastUpdatedBy_Id = userId;
            await db.SaveChangesWithAuditAsync(userId);
         }
      }
   }
}