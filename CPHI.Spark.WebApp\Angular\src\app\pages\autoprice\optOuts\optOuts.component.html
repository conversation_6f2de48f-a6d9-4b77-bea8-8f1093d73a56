<nav class="navbar">
    <nav class="generic">
        <h4 id="pageTitle">Opt-Outs</h4>

        <!-- Choose effective date -->
        <div id="chooseEffectiveDate" class="me-4">
            <span class="me-2">Choose effective date:</span>
            <input type="date" #inputElem (change)="onChosenNewEffectiveDate(inputElem.value)"
                [ngModel]="service.chosenDateAsString">
        </div>

        <!-- Show / hide new vehicles -->
        <sliderSwitch text="Include New Vehicles" (toggle)="toggleIncludeNewVehicles()"
            [defaultValue]="service.includeNewVehicles">
        </sliderSwitch>
    </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
    <div class="content-new">
        <div class="content-inner-new">
            <instructionRow *ngIf="service.optOutsRowData && service.optOutsRowData.length === 0" [isDanger]="true"
                [message]="'No opt-outs to display.'">
            </instructionRow>

            <instructionRow *ngIf="!service.optOutsRowData  || service.optOutsRowData.length !== 0"
                [message]="'Double click any row to see further details.'">
            </instructionRow>

            <div *ngIf="service.optOutsRowData && service.optOutsRowData.length > 0" id="gridHolder">
                <statusBar *ngIf="service.gridApi" (excelExportClick)="excelExport()" [gridColumnApi]="gridColumnApi"
                    [gridApi]="service.gridApi" [gridOptions]="gridOptions"></statusBar>

                <ag-grid-angular [components]="components" [class]="constants.getGridClass()"
                    [gridOptions]="gridOptions"></ag-grid-angular>
            </div>
        </div>
    </div>
</div>