//core angular
import { Component, Input, OnInit } from '@angular/core';
import { localeEs } from 'src/environments/locale.es.js';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
import { PartsStockDetailedLine } from '../../model/afterSales.model';
//model and cell renderers
import { SiteVM } from '../../model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from '../../services/agGridMethods.service';
import { AutotraderService } from '../../services/autotrader.service';
//services
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';
//Angular things, non-standard
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';




@Component({
  selector: 'partsStockDetailedTable',
  template: `
    <div id="gridHolder">
      <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular 
      class="ag-theme-balham" 
      [gridOptions]="mainTableGridOptions" 
      
             > 
      </ag-grid-angular>
    </div>
    `
  ,
  styles: [
    `
    #gridHolder{height:71vh;width:96%;margin:1em auto;}
    ag-grid-angular{ height:100%; margin-top: 3em; max-width: 1927px;}

     
  `
  ],
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
})



export class PartsStockDetailedTableComponent implements OnInit {

  @Input() public rowData: Array<any>;
  

  currentRowHeight: number;
  totalRow:PartsStockDetailedLine;


  //main declarations
  //for agGrid
  showGrid = false;
  public gridApi;
  public importGridApi;
  public gridColumnApi;


  mainTableGridOptions: GridOptionsCph;



  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,

  ) { }


  ngOnInit() {
    //launch control
    this.initParams();
   
  }

  initParams() {

    //this.agGrid.topBottomHighlights = [];

    this.buildTotalRow()
    
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      onGridReady: (params) => this.onGridReady(params),
      onFirstDataRendered:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
      //domLayout: 'autoHeight',
      getMainMenuItems:(params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params, []),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {        resizable: true,        sortable: true,        hide: false,        filterParams: { applyButton: false, clearButton: true,
        cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      rowBuffer:0,
      columnDefs: [],
      getRowClass: (params) => {
        if (params.data.Label == 'Total') {
          return 'total';
        }
      },
      rowData: this.rowData,
      pinnedBottomRowData: [this.totalRow],

    }


    this.mainTableGridOptions.columnDefs = [

      { headerName: this.constants.translatedText.Site, field: 'Label', colId: 'Site', width: 80, type: 'label', },
      { headerName: 'Part Number', field: 'PartNumber', colId: 'Number', width: 60, type: 'label', },
      { headerName: this.constants.translatedText.Description, field: 'Description', colId: 'Description', width: 100, type: 'label', },
      { headerName: 'Created', field: 'Created', colId: 'Created', width: 50, type: 'dateShort', hide: this.constants.environment.partsStockDetailedTable_hideCreatedColumn },
      { headerName: 'Last Purch.', field: 'LastPurchased', colId: 'LastPurch', width: 50, type: 'dateShort', },
      { headerName: 'Last Sold', field: 'LastSold', colId: 'LastSold', width: 50, type: 'dateShort', },
      { headerName: 'Ageing', field: 'Ageing', colId: 'Ageing', width: 40, type: 'label', },
      { headerName: 'Grouping', field: 'Grouping', colId: 'Grouping', width: 40, type: 'label', },
      { headerName: 'Family Group', field: 'FamilyCodeGroup', colId: 'FamilyGroup', width: 50, type: 'label', },
      { headerName: 'Quantity', field: 'Quantity', colId: 'Quantity', width: 50, type: 'number', },
      { headerName: 'Cost', field: 'Cost', colId: 'Cost', width: 50, type: 'currency', },
      { headerName: 'Value', field: 'Value', colId: 'Value', width: 50, type: 'currency', },

    ]

    
    

  }



  buildTotalRow(){

    this.totalRow = {
      Number: '',
      Description:'',
      Grouping: '',
      Quantity: this.constants.sum(this.rowData.map(x=>x.Quantity)),
      Cost: this.constants.sum(this.rowData.map(x=>x.Cost)),
      Value: this.constants.sum(this.rowData.map(x=>x.Value)),
      Created: null,
      LastPurchased: null,
      LastSold: null,
      FamilyCodeGroup: '',
      AgeBand: '',
      Site_Id:0,
      site:{
        SiteDescription: 'Total'
      } as SiteVM
    }  
      

  }





  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }
  }




  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);

    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods

  }





  refreshCells() {
    if (this.gridApi) {
      this.gridApi.refreshCells();
    }
  }

  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Parts Detailed Stock', 1, 1);
  }




}
