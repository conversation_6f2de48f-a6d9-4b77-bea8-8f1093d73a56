export class SiteSettings {
  constructor(itemIn: SiteSettings) {
    Object.assign(this, itemIn);
  }

  public get strategyName(): string {
    if (!this.StrategySelectionRuleSetUniqueIdForDg) {
      return 'None selected';
    }
    return `#${this.StrategySelectionRuleSetUniqueIdForDg} ${this.StrategySelectionRuleSetName}`;
  }

  public get buyingStrategyName(): string {
    if (!this.BuyingStrategySelectionRuleSetId) {
      return 'None selected';
    }
    return `#${this.BuyingStrategySelectionRuleSetUniqueIdForDg} ${this.BuyingStrategySelectionRuleSetName}`;
  }

  public get buyingStrategy2Name(): string {
    if (!this.BuyingStrategySelectionRuleSet2Id) {
      return 'None selected';
    }
    return `#${this.BuyingStrategySelectionRuleSet2UniqueIdForDg} ${this.BuyingStrategySelectionRuleSet2Name}`;
  }

  public get testStrategyName(): string {
    if (!this.TestStrategySelectionRuleSetId) {
      return 'None selected';
    }
    return `#${this.TestStrategySelectionRuleSetUniqueIdForDg} ${this.TestStrategySelectionRuleSetName}`;
  }

  RetailerSiteId: number;
  Name: string;
  StrategySelectionRuleSetName: string;
  BuyingStrategySelectionRuleSetName: string;
  BuyingStrategySelectionRuleSet2Name: string;
  TestStrategySelectionRuleSetName: string;
  StrategySelectionRuleSetId: number;
  StrategySelectionRuleSetUniqueIdForDg: number | null;
  BuyingStrategySelectionRuleSetId: number;
  BuyingStrategySelectionRuleSetUniqueIdForDg: number;
  BuyingStrategySelectionRuleSet2Id: number | null;
  BuyingStrategySelectionRuleSet2UniqueIdForDg: number | null;
  TestStrategySelectionRuleSetId: number | null;
  TestStrategySelectionRuleSetUniqueIdForDg: number | null;
  LocalBargainThreshold: number;
  LocationMoveFixedCostPerMove: number;
  LocationMovePoundPerMile: number;
  UpdatePricesAutomatically: boolean;
  MinimumAutoPriceDecrease: number;
  MinimumAutoPriceIncrease: number;
  MinimumAutoPricePercentDecrease: number;
  MinimumAutoPricePercentIncrease: number;
  IncludeUnPublishedAdsInEmailReport: boolean;
  UpdatePricesMon: boolean;
  UpdatePricesTue: boolean;
  UpdatePricesWed: boolean;
  UpdatePricesThu: boolean;
  UpdatePricesFri: boolean;
  UpdatePricesSat: boolean;
  UpdatePricesSun: boolean;
  UpdatePricesPubHolidays: boolean;
  WhenToActionChangesEachDay: number;
  MaximumOptOutDays: number;
  CompetitorPlateRange: number;
  LocalBargainsSearchRadius: number;
  LocalBargainsMinRetailRating: number;

  TargetMargin: number;
  TargetAdditionalMech: number;
  TargetPaintPrep: number;
  TargetAuctionFee: number;
  TargetDelivery: number;
  TargetOtherCost: number;
}
