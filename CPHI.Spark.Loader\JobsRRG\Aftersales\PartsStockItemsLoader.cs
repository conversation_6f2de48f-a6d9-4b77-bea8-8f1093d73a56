﻿using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Reflection.Metadata.Ecma335;

namespace CPHI.Spark.Loader
{
    public class PartsStockItemsJob : IJob
    {

        private readonly IDapper dapper = new Dapperr();
        private static readonly ILog Logger = LogManager.GetLogger(typeof(PartsStockItemsJob));
        private string fileSearch = "*SPK32.csv";
        /*
| Column               | Example     | Sensitive |
|----------------------|-------------|-----------|
| Part Number          | N0112100Q8L | No        |
| Description          | BOLT        | No        |
| Product Group        | N           | No        |
| Discount Code        | J           | No        |
| Family Code          |             | No        |
| Total Stock Quantity | 0           | No        |
| Average Cost         | 5.32        | No        |
| Date Raised          | 43447       | No        |
| Date Last Purchased  | 43448       | No        |
| Date Last Sold       | 43448       | No        |
| Branch               | Autoworld   | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |
| Demand Pattern       | 0           | No        |

*/

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();


            string[] filePaths = Directory.GetFiles(ConfigService.incomingRoot, fileSearch);

            //check for presence of file, if so, return as already running
            if (LocksService.PartsStockItems) { CentralLoggingService.ReportLock("PartsStockItemsJob"); return; }

            if (filePaths.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.PartsStockItems;
                if (age.Minutes > 120)
                {
                    PulsesService.PartsStockItems = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *SPK32.csv");
                }
                return;
            }


            //define Lists
            List<Site> dbSites;
            List<PartFamilyCode> dbPartFamilyCodes;

            using (var db = new CPHIDbContext())

            {
                int errorCount = 0;
                LogMessage logMessage = new LogMessage();
                logMessage.DealerGroup_Id = 1;

                try
                {

                    dbSites = db.Sites.ToList();
                    dbPartFamilyCodes = db.PartFamilyCodes.ToList();


                    logMessage.SourceDate = DateTime.UtcNow;
                    logMessage.Job = this.GetType().Name;

                    Logger.Info($@"[{DateTime.UtcNow}] {filePaths.Count()} file(s) found at {ConfigService.incomingRoot}");   //update logger with how many found

                    //go through first file
                    string filePath = filePaths[0];


                    //define variables for use in processing this file
                    int incomingCount = 0;
                    int removedCount = 0;
                    int newCount = 0;
                    int changedCount = 0;

                    if (File.Exists(filePath.Replace(".csv", "-p.csv")))
                    {
                        //already processing a file of this type, skip
                        Logger.Info($@"[{DateTime.UtcNow}] Could not interpret {filePath}, -p file already found ");
                        logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                    }
                    File.Move(filePath, filePath.Replace(".csv", "-p.csv")); //append _p to the file to prevent any other instances also processing these files
                    var newFilepath = filePath.Replace(".csv", "-p.csv");
                    LocksService.PartsStockItems = true;

                    string fileName = Path.GetFileName(filePath);
                    var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                    logMessage.SourceDate = fileDate;

                    Logger.Info($@"[{DateTime.UtcNow}] Starting {filePath} ");

                    List<Part> incomingPartsStockItems = new List<Part>(100000);  //preset the list size (slightly quicker than growing it each time)

                    var allText = File.ReadAllText(newFilepath);
                    string allTextNoCR = allText.Replace("\r", "");
                    var rows = allTextNoCR.Split('\n');

                    var headers = rows.Skip(0).First().ToUpper().Split(',');

                    int familyTypeId = db.StandingValueTypes.ToList().Find(x => x.Description == "FamilyCodeType").Id;
                    int defaultFamilyCodeId = db.StandingValues.ToList().Find(x => x.Description == "Other" && x.StandingValueType_Id == familyTypeId).Id;

                    foreach (var row in rows.Skip(1))
                    {
                        if (!string.IsNullOrEmpty(row))
                        {
                            incomingCount++;


                            try
                            {
                                string rowNoCR = row.Replace("\r\n", "");

                                var cells = Regex.Matches(rowNoCR, "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))")
                                    .Cast<Match>()
                                    .Select(m => m.Value)
                                    .ToArray();

                                if (cells.Length != headers.Length)
                                {
                                    //something weird happened, not got enough cells for the number of headerCols, skip this record
                                    logMessage.FailNotes = logMessage.FailNotes + $"Skipped item no. {incomingCount}. Had {cells.Length} cells and needed {headers.Length}";
                                    errorCount++;
                                    continue;
                                }

                                //site
                                string branchName = cells[Array.IndexOf(headers, "BRANCH")].ToUpper();

                                // Skip do not use sites
                                if (branchName.ToUpper().Contains("DO NOT USE"))
                                {
                                    continue;
                                }

                                //lookup objects required
                                string familyCode = cells[Array.IndexOf(headers, "FAMILY CODE")].ToUpper();

                                int familyCodeId = defaultFamilyCodeId;

                                var familyCod = dbPartFamilyCodes.FirstOrDefault(x => x.FamilyCode == familyCode);
                                if (familyCod != null)
                                {
                                    familyCodeId = familyCod.FamilyCodeTypeId;
                                }

                                int siteId = dbSites.Find(x => x.Description.ToUpper() == branchName).Id;

                                //dates

                                DateTime dateLastPurch = DateTime.ParseExact(cells[Array.IndexOf(headers, "DATE RAISED")], "dd/MM/yyyy", null);
                                DateTime dateLastSold = DateTime.ParseExact(cells[Array.IndexOf(headers, "DATE RAISED")], "dd/MM/yyyy", null);

                                DateTime dateRaised = DateTime.ParseExact(cells[Array.IndexOf(headers, "DATE RAISED")], "dd/MM/yyyy", null);
                                if (cells[Array.IndexOf(headers, "DATE LAST PURCHASED")] != "--/--/----") { dateLastPurch = DateTime.ParseExact(cells[Array.IndexOf(headers, "DATE LAST PURCHASED")], "dd/MM/yyyy", null); }
                                if (cells[Array.IndexOf(headers, "DATE LAST SOLD")] != "--/--/----") { dateLastSold = DateTime.ParseExact(cells[Array.IndexOf(headers, "DATE LAST SOLD")], "dd/MM/yyyy", null); }

                                if (decimal.Parse(cells[Array.IndexOf(headers, "TOTAL STOCK QUANTITY")]) == 0) { continue; }

                                Part e = new Part(); //initialise new one
                                e.Number = cells[Array.IndexOf(headers, "PART NUMBER")];
                                e.Description = cells[Array.IndexOf(headers, "DESCRIPTION")];
                                e.Grouping = cells[Array.IndexOf(headers, "PRODUCT GROUP")];
                                e.DiscountCode = cells[Array.IndexOf(headers, "DISCOUNT CODE")];
                                e.FamilyCode = cells[Array.IndexOf(headers, "FAMILY CODE")];
                                e.Quantity = decimal.Parse(cells[Array.IndexOf(headers, "TOTAL STOCK QUANTITY")]);
                                e.Cost = decimal.Parse(cells[Array.IndexOf(headers, "AVERAGE COST")]);
                                e.Created = dateRaised;
                                e.LastPurchased = dateLastPurch;
                                e.LastSold = dateLastSold;
                                e.Site_Id = siteId;
                                e.SaleM1 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 0]);
                                e.SaleM2 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 1]);
                                e.SaleM3 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 2]);
                                e.SaleM4 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 3]);
                                e.SaleM5 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 4]);
                                e.SaleM6 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 5]);
                                e.SaleM7 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 6]);
                                e.SaleM8 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 7]);
                                e.SaleM9 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 8]);
                                e.SaleM10 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 9]);
                                e.SaleM11 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 10]);
                                e.SaleM12 = decimal.Parse(cells[Array.IndexOf(headers, "DEMAND PATTERN") + 11]);
                                e.FamilyCodeType_Id = familyCodeId;
                                incomingPartsStockItems.Add(e);

                            }

                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Part: item{incomingCount}  {err.ToString()}";  // <----- DON'T FORGET TO UPDATE!
                                errorCount++;
                                continue;
                            }

                        }
                    }



                    var newItems = incomingPartsStockItems;
                    newCount = newItems.Count();

                    try
                    {
                        await dapper.ExecuteAsync("TRUNCATE TABLE [Parts]", null, System.Data.CommandType.Text); //remove all old ones
                        db.Parts.AddRange(newItems);  //add them all in one go
                        Console.WriteLine("Loading to db");
                        db.SaveChanges();
                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $"Failed add new  {err.ToString()}" + "\r\n\r\n";
                        errorCount++;
                    }

                    logMessage.FinishDate = DateTime.UtcNow;
                    logMessage.ProcessedCount = incomingPartsStockItems.Count;
                    logMessage.AddedCount = newCount;
                    logMessage.RemovedCount = removedCount;
                    logMessage.ChangedCount = changedCount;
                    logMessage.IsCompleted = true;
                    logMessage.ErrorCount = errorCount;


                    Logger.Info($"[{DateTime.UtcNow}]  | Result: {incomingPartsStockItems.Count} item(s) interpreted, added {newCount}");
                    try
                    {
                        File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                        if (errorCount > 0)
                        {
                            //we have errors so use the reporter
                            logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                            LocksService.PartsStockItems = false;
                            await CentralLoggingService.ReportError("PartsStockItems", logMessage, true);
                        }
                        else
                        {
                            //no errors so just save the log
                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();
                            LocksService.PartsStockItems = false;
                        }
                    }
                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                        errorCount++;
                        LocksService.PartsStockItems = false;
                            await CentralLoggingService.ReportError("PartsStockItems", logMessage, true);
                    }

                    //trigger cache rebuild
                    await UpdateWebAppService.Trigger("PartsStockItems");
                    stopwatch.Stop();
                }
                catch (Exception err)
                {
                    stopwatch.Stop();
                    errorMessage = err.ToString();
                    logMessage.FailNotes = logMessage.FailNotes + $"General file " + err.ToString();
                    errorCount++;
                    logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                    LocksService.PartsStockItems = false;
                    await CentralLoggingService.ReportError("PartsStockItems", logMessage);
                }
                finally
                {
                    db.ChangeTracker.Clear();

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "RRG",
                        Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                        Task = this.GetType().Name,
                        StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                        EndDate = DateTime.UtcNow,
                        Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                        Notes = errorMessage,
                        HTML = string.Empty
                    };
                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                }



            }
        }



    }


}
