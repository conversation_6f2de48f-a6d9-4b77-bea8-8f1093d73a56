{


  //-------------------------------------------------x
  // THIS IS LOADER
  //-------------------------------------------------x


  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },

   "ConnectionStrings": {
      "DefaultConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=sparkRRGdev;Persist Security Info=True; User ID=sparkRRGUserDev;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=90;",
      "AutoPriceConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=sparkAutoPricedev; Persist Security Info=true; User ID=SparkAutoPriceUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=180;",
      "SytnerConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=sparkSytnerdev; Persist Security Info=true; User ID=SparkSytnerUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;"
   },
  "EmailSettings": {
    "mailAppTenantId": "7def64f8-8b3f-400a-8ad0-e50eb7e77eef",
    "mailAppId": "9a44d66f-cfe2-4d19-b129-62b3e0dd28aa",
    "mailSecretValue": "****************************************",
    "mailAccountInbound": "<EMAIL>",
    "mailAccountOutbound": "<EMAIL>",
    "mailAccountToCheckDraftfFolder": "<EMAIL>"
  },

   "AppSettings": {
      "AllowedHosts": "*",
      "ClientSettingsProvider.ServiceUri": "",
      "timeZoneInfo": "UTC", //TimeZoneInfo --- Coordinated Universal Time, Central Standard Time, Eastern Standard Time, GMT Standard Time, Hawaiian Standard Time, Mountain Standard Time, Pacific Standard Time, West Pacific Standard Time

      "isDev": "true",

      "overrideRunJobNow": null, //put the job name in here if you wish to force run it  e.g. "PeopleJob".   Or null (without quotes).  Only ever change appsettings.json not the underlying rrgDev file
      "customerName": "RRGUK",
      "incomingRoot": "C:\\cphiRoot\\rrgDev\\inbound",
      "rebuildUrl": "https://sparkrrgdevapi.cphi.co.uk/api/sourcedataupdate/updateApiFor?dealerGroup=1&cacheName=",
      "serviceName": "CPHI.Spark.RRGLoaderDev",

      "usedUnitThresholdAchievedDaily": 100,
      "usedUnitThresholdAchievedWeeklyLow": 400,
      "usedUnitThresholdAchievedWeeklyHigh": 500,

      "emailsToConfirmFleetOrderbookLoadsTo": "<EMAIL>,<EMAIL>,<EMAIL>",
      "emailsToSendMidOutputsTo": "<EMAIL>,<EMAIL>,<EMAIL>",

      "voCSalesScoressJobSchedule": "REPEAT EVERY 10 MINUTES",
      "voCServiceScoressJobSchedule": "REPEAT EVERY 10 MINUTES",
      "voCServiceDataQualityJobSchedule": "REPEAT EVERY 10 MINUTES",
      "voCSalesDataQualityJobSchedule": "REPEAT EVERY 10 MINUTES",
      "availabilityDefaultJobSchedule": "REPEAT EVERY 10 MINUTES",
      "availabilityOverrideJobSchedule": "REPEAT EVERY 10 MINUTES",
      "vhcJobSchedule": "REPEAT EVERY 10 MINUTES", //At 04:30:00am every day "0 30 4 ? * * *"
      "partsTransactionsJobSchedule": "DO NOT RUN", //dead
      "citNowJobSchedule": "REPEAT EVERY 10 MINUTES",
      "serviceTransactionsJobSchedule": "REPEAT EVERY 10 MINUTES",
      "bookingsJobSchedule": "REPEAT EVERY 10 MINUTES",
      "rtsCodesJobSchedule": "REPEAT EVERY 10 MINUTES", //Every 10 minutes starting at :00 minute after the hour // chatGPT can decode cron job schedules so long as you mention it's Quartz cron
      "partsStockItemsJobSchedule": "REPEAT EVERY 10 MINUTES",
      "techDailyHoursCountJobSchedule": "REPEAT EVERY 10 MINUTES",
      "peopleJobSchedule": "REPEAT EVERY 10 MINUTES",
      "activitiesJobSchedule": "REPEAT EVERY 1 MINUTES", //Every minute starting at :00 minute after the hour   See https://www.freeformatter.com/cron-expression-generator-quartz.html
      "partsStocksJobSchedule": "RUN AT 5:20",
      "wipLinesJobSchedule": "REPEAT EVERY 10 MINUTES",
      "debtsJobSchedule": "REPEAT EVERY 10 MINUTES",
      "bonusesJobSchedule": "REPEAT EVERY 10 MINUTES",
      "salesRolesJobSchedule": "REPEAT EVERY 3 MINUTES",
      "sarsLinesJobSchedule": "REPEAT EVERY 3 MINUTES",
      "sarsLinesIntraDayJobSchedule": "REPEAT EVERY 3 MINUTES",
      "stocksJobSchedule": "REPEAT EVERY 1 MINUTES",
      "specLinesJobSchedule": "REPEAT EVERY 1 MINUTES",
      "stockRRGSiteItemsJobSchedule": "REPEAT EVERY 12 MINUTES",
      "registrationsJobSchedule": "REPEAT EVERY 4 MINUTES",
      "dealsJobSchedule": "REPEAT EVERY 1 MINUTES",
      "financialLinesJobSchedule": "REPEAT EVERY 8 MINUTES",
      "balanceSheetJobSchedule": "REPEAT EVERY 7 MINUTES",
      "statsBPC07JobSchedule": "REPEAT EVERY 8 MINUTES",
      "statsBPC07bJobSchedule": "DO NOT RUN", //dead
      "statsBPC08JobSchedule": "REPEAT EVERY 10 MINUTES",
      "hoursJobSchedule": "REPEAT EVERY 6 MINUTES",
      "otherFilesClearDownJobSchedule": "REPEAT EVERY 30 SECONDS",
      "diffsCopierJobSchedule": "RUN AT 22:25",
      "updateDailyOrdersTableJobSchedule": "RUN AT 21:52",
      "updateStockLandingStockSoldJobSchedule": "RUN AT 22:50",
      "upsellJobSchedule": "REPEAT EVERY 10 MINUTES",
      "oemOrdersJobSchedule": "REPEAT EVERY 10 MINUTES",
      "oemStockItemsJobSchedule": "REPEAT EVERY 10 MINUTES",
      "customerVisitsJobSchedule": "REPEAT EVERY 10 MINUTES",
      "oemCancelledOrdersJobSchedule": "REPEAT EVERY 10 MINUTES",
      "vehiclesAwaitingPrepJobSchedule": "REPEAT EVERY 10 MINUTES",

      "bcaStockJobSchedule": "REPEAT EVERY 1 MINUTES",
      "ebbonOrdersJobSchedule": "REPEAT EVERY 1 MINUTES",
      "fonDataJobSchedule": "REPEAT EVERY 1 MINUTES",
      "grantDataJobSchedule": "REPEAT EVERY 1 MINUTES",
      "nissanOrdersJobSchedule": "REPEAT EVERY 1 MINUTES",
      "renaultOrdersJobSchedule": "REPEAT EVERY 1 MINUTES",
      "rrgCleanupJobSchedule": "RUN AT 6:00",
      "VehicleNumbersToVinJobSchedule": "REPEAT EVERY 5 MINUTES",
      "nissanUploadJobSchedule": "REPEAT EVERY 1 MINUTES",
      "renaultUploadJobSchedule": "REPEAT EVERY 1 MINUTES",
      "renaultTeleStatsJobSchedule": "REPEAT EVERY 1 MINUTES",
      "midJobSchedule": "REPEAT EVERY 1 MINUTES",
      "plyJobSchedule": "REPEAT EVERY 1 MINUTES",
      "carCoPartsJobSchedule": "REPEAT EVERY 1 MINUTES",
      "carCoServiceJobSchedule": "REPEAT EVERY 1 MINUTES",
      "enterpriseSalesReportJobSchedule": "REPEAT EVERY 1 MINUTES",
      "genericStockJobSchedule": "REPEAT EVERY 1 MINUTES",
      "genericDealsJobSchedule": "REPEAT EVERY 1 MINUTES",
      "sendEmailFromDraftJobSchedule": "REPEAT EVERY 2 MINUTES"





   },
  "Monitor": {
    "AddLogMessageURL": "https://cphimonitorapi.azurewebsites.net/api/monitor/",
    "AppKey": "7"
  }




}
