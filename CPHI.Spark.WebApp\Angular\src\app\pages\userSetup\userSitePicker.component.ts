import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { SiteVM } from "src/app/model/main.model";
import { ConstantsService } from "src/app/services/constants.service";





@Component({
  selector: 'userSitePicker',
  template: `
    <!-- Site selector -->
    <div ngbDropdown  container="body" dropright class="d-inline-block" id="siteDropdown">
        <button [ngStyle]="{'width.px':width}" [disabled]="disabled" id="mainButton" [ngClass]="buttonClass" class=" btn btn-primary" (click)="generateSitesList()"
          ngbDropdownToggle>{{label}}
        </button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <div id="buttonHolder">  
          <div id="scrollableArea">
        <!-- ngFor buttons -->  
          <ng-container    *ngFor="let site of sites">
            <button *ngIf="site.SiteId!==0" class="btn btn-primary dropdownButtonNoToggle" (click)="toggleItem(site)" [ngClass]="{'active':site.isSelected}"
              >{{site.SiteDescription}}
            </button>
            
          </ng-container>
         
          <button class="btn btn-primary dropdownButtonNoToggle" (click)="selectAllSites()">
          {{constants.translatedText.All}}
          </button>
          
        </div>
          <div id="bottomSection" class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="selectSites()">OK</button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
          </div>
        </div>

        </div>
    </div>
    
    `
  ,
  styles: [`


  #buttonHolder{
    width:100%;
  }   
  
  #scrollableArea{
    max-height: 40vh;
    overflow-y:auto;

  }

  #bottomSection{width:100%;}
  
  .dropdown-menu{        text-align: center;    box-shadow:5px 5px 10px var(--shadowColour); }
  .dropdown-menu .dropdown-item:hover{background-color: lighten(hsl(184,93.3%,18%),10%);color:white;}
  .dropdown-menu{z-index:10;background-color: var(--mainAppColour)!important;}
  .dropdown-menu .btn{margin: 0.0em auto;}
  .dropdown-item{text-align: left;color:white !important;    line-height: 2em;}
  .dropdown-item.active, .dropdown-item:active{background-color: var(--mainAppColourLight) !important}
  
  .dropdownButtonNoToggle{
    display: block ;
  width: 100%;
  border-radius: 0px;
  }

  .dropdown-menu {
    
    text-align: center;
    box-shadow: 5px 5px 10px var(--shadowColour);
  }

  .btn{
    line-height: 1.5;

  }

  
  @media (min-width: 0px) and (max-width: 1920px) and (hover:none) {
    #siteDropdown .dropdown-menu{columns:2}

    #mainButton{display: flex;
    justify-content: space-between;
    align-items: center;}
  }  
    `]
})



export class UserSitePickerComponent implements OnInit {
  @Input() sitesFromParent: SiteVM[];
  @Input() buttonClass: string;
  @Input() width: number;
  @Input() onlyOneSite: boolean;
  @Input() disabled: boolean;
  @Output() updateSites = new EventEmitter<SiteVM[]>();

  public sites: SiteVM[];
  label: string;

  constructor(
    public constants: ConstantsService,

  ) { }


  ngOnInit(): void {
    this.siteChosenLabel()
  }


  siteChosenLabel() {
    if (!this.sitesFromParent) this.label = 'Sites'
    else if (this.sitesFromParent.length == 0) {
      this.label = 'No sites selected'
    } else if (this.sitesFromParent.length == 1) {
      this.label = this.sitesFromParent[0].SiteDescription
    } else if (this.sitesFromParent.length < 4) {
      let siteNames = ''
      this.sitesFromParent.forEach((site, i) => {
        if (i > 0) { siteNames = siteNames + ',' } //leading comma for 2nd item onwards
        siteNames = siteNames + site.SiteDescription;
      })
      this.label = siteNames
    } else if (this.sitesFromParent.length == this.constants.Sites.length - 1) {
      this.label = 'All Sites'
    } else {
      this.label = 'Sites'
    }
  }

  generateSitesList() : void {

    this.sites = this.constants.clone(this.constants.Sites.filter(x=> x.IsActive || this.constants.environment.userModal_userSitePicker_includeInactive));

    if (this.sitesFromParent) {
      const siteIds = this.sitesFromParent.map(x => x.SiteId);

      //tag if it's selected
      this.sites.map(s => {
        s.isSelected = siteIds.includes(s.SiteId)
      })

    }

  }


  toggleItem(item: any) {

    item.isSelected = !item.isSelected
    this.sitesFromParent = this.sites.filter(x => x.isSelected)
    this.siteChosenLabel()

  }

  selectAllSites() {

    this.sites.forEach((s) => {
      s.isSelected = true;
    })

    this.sitesFromParent = this.sites.filter(x => x.isSelected)

    this.siteChosenLabel()
  }


  selectSites() {
    this.updateSites.emit(this.sites.filter(e => e.isSelected));
  }





}


