using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using Quartz;
using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using log4net;
using CPHI.Spark.WebScraper.Services;
using OpenQA.Selenium.Support.UI;
using System.Threading;
using CPHI.WebScraper.ViewModel;
using System.Collections.Generic;
using System.Linq;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.BusinessLogic.AutoPrice;
using SeleniumExtras.WaitHelpers;

namespace CPHI.Spark.WebScraper.Jobs
{
    public class ClickDealerScrapeJob : IJob
    {
        // Enum to define the operation type for vehicle processing function
        private enum VehicleProcessOperation
        {
            Update,
            Verify
        }
        
        private static readonly ILog logger = LogManager.GetLogger(typeof(ClickDealerScrapeJob));
        private static IWebDriver _driver;
        private string customerName;
        private string fileDestination;
        private const string homePageLink = "https://myclickdealer.co.uk/dealer_interface_login.php";
        private DealerGroupName dealerGroup;
        
        public void Execute() { }
        
        public async Task Execute(IJobExecutionContext context)
        {
            logger.Info("");
            logger.Info("========================= Starting ClickDealerScrapeJob =====================================");
            
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            fileDestination = ConfigService.FileDestination;
            fileDestination = fileDestination.Replace("{destinationFolder}", "clickdealer");
            
            // Set dealer group - this would typically be determined by configuration
            dealerGroup = DealerGroupName.EMG;

            try
            {
                logger.Info("Starting ClickDealer scrape job...");
                
                // Set up Chrome driver
                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";
                ScraperMethodsService.ClearDownloadsFolder();
                
                ChromeOptions options = ScraperMethodsService.SetChromeOptions("ClickDealerScrape", 9250);
                
                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
                
                // Get the list of vehicles that need price updates
                List<ClickDealerVehicle> vehicles = await GetPriceChangesToProcess(dealerGroup);
                
                // Login to ClickDealer
                bool loginSuccessful = await LoginAsync();
                
                if (loginSuccessful)
                {
                    logger.Info($"Login OK, continuing with {vehicles.Count} vehicles to update...");
                    
                    // Update prices
                    UpdateClickDealer(vehicles);
                    
                    // Verify changes
                    VerifyAllChanges(vehicles);
                }
                else
                {
                    logger.Info($"Failed to login, quitting...");
                }
                
                // Clean up
                _driver.Quit();
                _driver.Dispose();
                stopwatch.Stop();
                
                logger.Info("========================= Completed ClickDealerScrapeJob =====================================");
            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                logger.Error($"Problem with ClickDealer scrape job: {e}");
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name}", $"{e.StackTrace}");
                throw;
            }
        }
        
        public async Task<List<ClickDealerVehicle>> GetPriceChangesToProcess(DealerGroupName dealerGroup)
        {
            PriceChangesService priceChangesService = new PriceChangesService(ConfigService.GetConnectionString(dealerGroup));
            RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
            List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

            bool includeUnPublished = retailers.First().IncludeUnPublishedAds;
            GetPriceChangesNewParams parms = await priceChangesService.CreateParmsToGetChanges(dealerGroup, retailers, includeUnPublished);
            var todayChangesFirstPass = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);
            
            // There are some changes, so now re-run the vehicle opt-out updater
            var optOutsDataAccess = new OptOutsDataAccess(ConfigService.GetConnectionString(dealerGroup));
            await optOutsDataAccess.CreateDailyOptOuts(dealerGroup);

            // Run again in case we just made some optouts
            GetTodayChangesResponse todayChangesResponse = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

            string _connectionString = ConfigService.GetConnectionString(dealerGroup);
            var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);

            List<ClickDealerVehicle> result = todayChangesResponse.totalChanges.ConvertAll(x => new ClickDealerVehicle(x));
            return result;
        }
        
        private async Task<bool> LoginAsync()
        {
            logger.Info("Attempting to log in to ClickDealer...");
            
            try
            {
                // Navigate to login page
                _driver.Navigate().GoToUrl(homePageLink);
                
                // Wait for username field and enter username
                IWebElement usernameField = WaitAndFind("//input[@id='username']");
                usernameField.SendKeys("Johnf@the19fam"); // Replace with actual username from config
                
                // Wait for password field and enter password
                IWebElement passwordField = WaitAndFind("//input[@id='password']");
                passwordField.SendKeys(ConfigService.ClickDealerPassword);
                
                // Click login button
                IWebElement loginButton = WaitAndFind("//button[@type='submit']", true);
                
                // Wait for login to complete
                Thread.Sleep(3000);
                
                // Verify login was successful
                try
                {
                    // Check for an element that would only be present after successful login
                    IWebElement dashboardElement = WaitAndFind("//a[contains(text(), 'Dashboard')]", false);
                    logger.Info("Login successful");
                    return true;
                }
                catch (Exception)
                {
                    logger.Error("Login verification failed - dashboard element not found");
                    return false;
                }
            }
            catch (Exception e)
            {
                logger.Error($"Login failed: {e.Message}");
                TakeScreenshot(_driver, "LoginFailure");
                return false;
            }
        }
        
        private void UpdateClickDealer(List<ClickDealerVehicle> vehicles)
        {
            // Call the generic method with Update operation
            ProcessVehicles(vehicles, VehicleProcessOperation.Update, "Update ClickDealer");
        }
        
        private void VerifyAllChanges(List<ClickDealerVehicle> vehicles)
        {
            // Call the generic method with Verify operation
            List<ClickDealerVehicle> amendedVehs = vehicles.Where(x => x.PriceChanged).ToList();

            if (amendedVehs.Count > 0)
            {
                logger.Info($"VerifyAllChanges: {amendedVehs.Count} price changes to verify.");
                ProcessVehicles(amendedVehs, VehicleProcessOperation.Verify, "Verifying Changes ClickDealer");
            }
            else
            {
                logger.Info($"VerifyAllChanges: No price changes to verify.");
            }
        }
        
        // Generic method to process vehicles for either updating or verifying
        private void ProcessVehicles(List<ClickDealerVehicle> vehicles, VehicleProcessOperation operation, string operationDescription)
        {
            try
            {
                logger.Info($"Starting {operationDescription}");

                // For verify operation, navigate to home page first
                if (operation == VehicleProcessOperation.Verify)
                {
                    _driver.Navigate().GoToUrl("https://myclickdealer.co.uk/onetrue_list.php");
                }

                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
                DateTime start = DateTime.Now;

                // Group vehicles by retailer site
                var vehiclesBySite = vehicles.ToLookup(x => x.RetailerSiteId);

                // Process each site group
                foreach (IGrouping<int, ClickDealerVehicle> siteGroup in vehiclesBySite)
                {
                    logger.Info($"Processing site {siteGroup.Key} with {siteGroup.Count()} vehicles");
                    
                    // Navigate to stock list
                    _driver.Navigate().GoToUrl("https://myclickdealer.co.uk/onetrue_list.php");
                    Thread.Sleep(2000);
                    
                    // Process each vehicle in the group
                    foreach (ClickDealerVehicle veh in siteGroup)
                    {
                        bool foundItem = SearchInventory(wait, veh);

                        if (foundItem)
                        {
                            // Perform the appropriate action based on operation type
                            if (operation == VehicleProcessOperation.Update)
                            {
                                logger.Info($"Found item: {veh.Reg}");
                                AmendPrices(veh);
                            }
                            else // Verify operation
                            {
                                VerifyPrices(wait, veh);
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                logger.Error($"Error in ProcessVehicles: {e.Message}");
                TakeScreenshot(_driver, "ProcessVehiclesFailure");
            }
        }
        
        private bool SearchInventory(WebDriverWait wait, ClickDealerVehicle veh)
        {
            try
            {
                logger.Info($"Searching for vehicle: {veh.Reg}");
                
                // Find the search box
                IWebElement searchBox = wait.Until(ExpectedConditions.ElementExists(By.Id("search_box")));
                
                // Clear search box
                searchBox.Clear();
                Thread.Sleep(500);
                
                // Enter registration and search
                searchBox.SendKeys(veh.Reg);
                Thread.Sleep(500);
                searchBox.SendKeys(Keys.Enter);
                
                // Wait for search results
                Thread.Sleep(2000);
                
                // Check if vehicle was found
                try
                {
                    // Look for the vehicle in the results
                    IWebElement vehicleRow = WaitAndFind($"//td[contains(text(), '{veh.Reg}')]", false);
                    return true;
                }
                catch (Exception)
                {
                    logger.Info($"Vehicle not found: {veh.Reg}");
                    return false;
                }
            }
            catch (Exception e)
            {
                logger.Error($"Error searching for vehicle {veh.Reg}: {e.Message}");
                return false;
            }
        }
        
        private void AmendPrices(ClickDealerVehicle veh)
        {
            logger.Info($"Amending price for: {veh.Reg}");
            
            try
            {
                // Click the edit button for this vehicle
                WaitAndFind($"//td[contains(text(), '{veh.Reg}')]/following-sibling::td//a[contains(@href, 'edit')]", true);
                Thread.Sleep(2000);
                
                // Find the price field
                IWebElement priceField = WaitAndFind("//input[@id='price']", false);
                
                // Get current price
                string currentPriceStr = priceField.GetAttribute("value");
                int currentPrice = int.Parse(currentPriceStr);
                
                // Set new price
                priceField.Clear();
                priceField.SendKeys(veh.Price.ToString());
                
                // Save changes
                WaitAndFind("//button[@type='submit']", true);
                Thread.Sleep(2000);
                
                logger.Info($"Price updated for {veh.Reg} from {currentPrice} to {veh.Price}");
                veh.PriceChanged = true;
            }
            catch (Exception e)
            {
                logger.Error($"Error amending price for {veh.Reg}: {e.Message}");
            }
        }
        
        private void VerifyPrices(WebDriverWait wait, ClickDealerVehicle veh)
        {
            logger.Info($"Verifying price for: {veh.Reg}");
            
            try
            {
                // Click the edit button for this vehicle
                WaitAndFind($"//td[contains(text(), '{veh.Reg}')]/following-sibling::td//a[contains(@href, 'edit')]", true);
                Thread.Sleep(2000);
                
                // Find the price field
                IWebElement priceField = WaitAndFind("//input[@id='price']", false);
                
                // Get current price
                string currentPriceStr = priceField.GetAttribute("value");
                int currentPrice = int.Parse(currentPriceStr);
                
                // Verify price matches expected value
                if (currentPrice == veh.Price)
                {
                    logger.Info($"Price verification successful for {veh.Reg}: {currentPrice}");
                }
                else
                {
                    logger.Error($"Price verification failed for {veh.Reg}. Expected: {veh.Price}, Actual: {currentPrice}");
                }
                
                // Go back to the list
                _driver.Navigate().Back();
                Thread.Sleep(1000);
            }
            catch (Exception e)
            {
                logger.Error($"Error verifying price for {veh.Reg}: {e.Message}");
            }
        }
        
        public IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "ClickDealerScrape", findXPath, andClick);
            return result;
        }
        
        private void TakeScreenshot(IWebDriver driver, string screenshotName)
        {
            try
            {
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string screenshotPath = Path.Combine(ConfigService.FileDownloadLocation, $"{timestamp}_{screenshotName}.png");
                
                Screenshot screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                screenshot.SaveAsFile(screenshotPath, ScreenshotImageFormat.Png);
                
                logger.Info($"Screenshot saved to {screenshotPath}");
            }
            catch (Exception e)
            {
                logger.Error($"Failed to take screenshot: {e.Message}");
            }
        }
    }
}
