import { Injectable } from '@angular/core';
import { CellClassParams, ColDef, IRowNode, ISetFilterParams, ValueFormatterParams, ValueGetterParams } from 'ag-grid-community';
import { CphPipe } from '../cph.pipe';
import { ColDefCph } from '../model/ColDefCPH';
import { TopBottomHighlightRule } from "../model/TopBottomHighlightRule";
import { DatePicker } from '../pages/fleetOrderbook/datePicker';
import { ConstantsService } from './constants.service';
import * as utilities from "./utilityFunctions";
import { CPHColDef } from '../model/CPHColDef';
import { AGGridMethodsService } from './agGridMethods.service';

export interface CphStandardColTypes {
  boolean: ColDefCph;
  booleanWithFloatingFilter: ColDefCph;
  booleanEditable: ColDefCph;
  numberDeepField: ColDefCph;
  number: ColDefCph;
  numberWithColour: ColDefCph;
  numberWithPlusMinus: ColDefCph;
  numberWithPlusMinusAndColour: ColDefCph;
  numberWithPlusMinusAndColourSwitch: ColDefCph;
  number1dp: ColDefCph;
  number2dp: ColDefCph;
  numberEditable: ColDefCph;
  numberEditableNoCommas: ColDefCph;

  currency: ColDefCph;
  currencySetFilter: ColDefCph;
  currency2dp: ColDefCph;
  currencyLiveForecast: ColDefCph;
  currencyWithPlusMinus: ColDefCph;
  currencyWithFontColour: ColDefCph;
  currencyFlipColour: ColDefCph;
  dateShort: ColDefCph;
  dateYearAndTime: ColDefCph;
  dateLongYear: ColDefCph;
  dateShortYear: ColDefCph;
  date: ColDefCph;
  dateEditable: ColDefCph;
  label: ColDefCph;
  labelWrap: ColDefCph;
  labelSetFilter: ColDefCph;
  labelLowPad: ColDefCph;
  labelNoZeros: ColDefCph;
  percent: ColDefCph;
  percentWithPlusMinus: ColDefCph;
  percentWithColour: ColDefCph;
  percent1dp: ColDefCph;
  percent2dp: ColDefCph;
  special: ColDefCph;
  image: ColDefCph;
  htmlLink: ColDefCph;
  days:ColDefCph;
  miles:ColDefCph;
  // ... other keys if needed
}

@Injectable({
  providedIn: 'root'
})
export class ColumnTypesService {

  constructor(
    private cphPipe: CphPipe,
    private constants: ConstantsService,
    private agGridMethodsService: AGGridMethodsService
  ) { }


  public provideColTypes(topBottomHighlights: TopBottomHighlightRule[]): CphStandardColTypes {

    return {

      //------------------------------
      // boolean 
      //------------------------------
      boolean: {
        ...this.standardBooleanProps(),
      },

      booleanWithFloatingFilter: {  //eg IsOnLatestDownload
        ...this.standardBooleanProps(),
        floatingFilter: true,
      },

      booleanEditable: { //eg DriverPackRequired
        ...this.standardBooleanProps(),
        floatingFilter: true,

        //specific stuff
        editable: true,
        cellClass: ['agAlignCentre', 'green'],  //override to add green
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          cellHeight: 30,
          valueFormatter: (params: ValueFormatterParams) => this.booleanRenderer(params),
          values: [true, false],
        },
      },

      //------------------------------
      //numbers
      //------------------------------

      numberDeepField: { //used when we are retrieving data from the row.data object using deeply nested field e.g. 'Monday.Booked' i.e. has a dot in it
        filter: 'agNumberColumnFilter',
        filterParams: {
          defaultOption: 'greaterThanOrEqual'
        },
        sortable: true,
        comparator: (num1, num2) => { return num1 - num2 },
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => this.numberValueGetter(params, 0, false),
      },
      number: {
        ...this.standardNumberProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => this.numberValueGetter(params, 0, false),
      },
      numberWithPlusMinus: {
        ...this.standardNumberProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => this.numberValueGetter(params, 0, true),
      },

      numberWithColour: {
        ...this.standardNumberProps(),
        cellClass: (params) => this.cellClassProviderWithColourFont(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => this.numberValueGetter(params, 0, false),
      },

      numberWithPlusMinusAndColour: {
        ...this.standardNumberProps(),
        cellClass: (params) => {
          return this.cellClassProviderWithColourFont(params, topBottomHighlights, 'ag-right-aligned-cell bold')
        },
        valueFormatter: (params) => this.numberValueGetter(params, 0, true),
      },

      numberWithPlusMinusAndColourSwitch: {
        ...this.standardNumberProps(),
        cellClass: (params) => {
          let backgroundClass = this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell bold')
          //still here so no goods / bads so do number colour
          if (params.value > 0) {
            return `${backgroundClass} badFont`
          } else {
            return `${backgroundClass} goodFont`  //this is good
          }
        },
        valueFormatter: (params) => this.numberValueGetter(params, 0, true),
      },


      number1dp: {
        ...this.standardNumberProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => this.numberValueGetter(params, 1, false),
      },

      number2dp: {
        ...this.standardNumberProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => this.numberValueGetter(params, 2, false),
      },


      numberEditable: {
        ...this.standardNumberProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => this.numberValueGetter(params, 0, false),
        editable: true,
        enableValue: true,
      },

      numberEditableNoCommas: {
        ...this.standardNumberProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueGetter: (params) => this.constants.getNestedValue(params.data, params.colDef.field),
        editable: true,
        enableValue: true,
      },

      // numberAverage: {
      //   aggFunc: 'sum', cellClass: 'agAlignCentre  ag-right-aligned-cell', filter: 'agNumberColumnFilter',
      //   valueGetter: (params) => this.myValueGetter(params, 'average'), cellRenderer: (params) => this.cphPipe.transform(params.value, 'number', 0)
      // },
      // numberAverageIfValue: {
      //   aggFunc: 'sum', cellClass: 'agAlignCentre  ag-right-aligned-cell', filter: 'agNumberColumnFilter',
      //   valueGetter: (params) => this.myValueGetter(params, 'average'), cellRenderer: (params) => this.cphPipe.transform(params.value, 'number', 0)
      // },


      //------------------------------
      // currency 
      //------------------------------
      currency: {
        ...this.standardCurrencyProps(topBottomHighlights),
        valueFormatter: (params: ValueFormatterParams) => { return this.cphPipe.transform(params.value, 'currency', 0) },
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },
      currencySetFilter: {
        ...this.standardCurrencyProps(topBottomHighlights),
        filter: 'agSetColumnFilter',
        valueFormatter: (params: ValueFormatterParams) => { return this.cphPipe.transform(params.value, 'currency', 0) },
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },
      currency2dp: {
        ...this.standardCurrencyProps(topBottomHighlights),
        valueFormatter: (params: ValueFormatterParams) => { return this.cphPipe.transform(params.value, 'currency', 2) },
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },


      currencyLiveForecast: {
        ...this.standardCurrencyProps(topBottomHighlights),
        valueFormatter: (params: ValueFormatterParams) => { return this.cphPipe.transform(params.value, params.data.RowNumberFormat ?? 'currency', 0) },
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },

      currencyWithPlusMinus: {
        ...this.standardCurrencyProps(topBottomHighlights),
        valueFormatter: (params: ValueFormatterParams) => { return this.cphPipe.transform(params.value, 'currency', 0, true) },
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },

      currencyWithFontColour: {
        ...this.standardCurrencyProps(topBottomHighlights),
        valueFormatter: (params: ValueFormatterParams) => { return this.cphPipe.transform(params.value, 'currency', 0, false) },
        cellClass: (params) => this.cellClassProviderWithColourFont(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },

      currencyFlipColour: {
        ...this.standardCurrencyProps(topBottomHighlights),
        valueFormatter: (params: ValueFormatterParams) => { return this.cphPipe.transform(params.value, 'currency', 0) },
        cellClass: (params) => this.cellClassProviderWithColourFontFlipGood(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },
      // currencyAverage: {
      //   cellClass: 'agAlignCentre  ag-right-aligned-cell', filter: 'agNumberColumnFilter',
      //   valueGetter: (params) => this.myValueGetter(params, 'average'), cellRenderer: (params) => this.cphPipe.transform(params.value,'currency',0)
      // },
      // currencyAverageIfValue: {
      //   cellClass: 'agAlignCentre  ag-right-aligned-cell', filter: 'agNumberColumnFilter',
      //   valueGetter: (params) => this.myValueGetter(params, 'averageIfValue'), cellRenderer: (params) => this.cphPipe.transform(params.value,'currency',0)
      // },







      //------------------------------
      // dates 
      //------------------------------

      dateShort: { //tested
        ...this.standardDateProperties(),
        filterValueGetter: (params) => this.formatDateForFilter(params),
        valueFormatter: (params) => { return this.cphPipe.transform(params.value, 'shortDate', 0) },  //"8 Nov"
      },

      dateYearAndTime: {  // tested
        ...this.standardDateProperties(),
        valueFormatter: (params) => this.cphPipe.transform(params.value, "dateAndTime", 0),  // "8 Nov 2023 7:00 AM"
      },


      dateLongYear: {  //tested
        ...this.standardDateProperties(),
        filterValueGetter: (params) => this.formatDateForFilter(params),
        valueFormatter: (params) => { return this.cphPipe.transform(params.value, 'dateLongYear', 0); },  //"08/11/2023"
      },

      dateShortYear: {  //tested
        ...this.standardDateProperties(),
        filterValueGetter: (params) => this.formatDateForFilter(params),
        valueFormatter: (params) => { return this.cphPipe.transform(params.value, 'dateShortYear', 0); },  //"08/11/2023"
      },


      date: {  //tested
        ...this.standardDateProperties(),
        filterValueGetter: (params) => this.formatDateForFilter(params),
        valueFormatter: (params) => {
          try {

            return this.cphPipe.transform(params.value, 'date', 0);
          }
          catch (error) {
            console.error(error);
            return 'err'
          }
        }, //"8 Nov 2023"
      },


      dateEditable: {  //tested
        ...this.standardDateProperties(),  //had to remove due to issue with tree structure breaking dates
        filterValueGetter: (params) => this.formatDateForFilter(params),
        valueFormatter: (params) => { return this.cphPipe.transform(params.value, 'date', 0); }, //"8 Nov 2023"
        cellEditor: DatePicker,
        editable: true,
      },










      //------------------------------
      // labels 
      //------------------------------
      label: {
        ...this.standardLabelProps(),
        filter: 'agTextColumnFilter',
      },
      labelWrap: {
        ...this.standardLabelProps(),
        cellClass: 'wrapText',
        filter: 'agTextColumnFilter',
      },
      labelSetFilter: {
        ...this.standardLabelProps(),
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        }
      },


      labelLowPad: {
        ...this.standardLabelProps(),
        cellClass: 'agAlignLeft lowPad',
        filter: 'agTextColumnFilter',
      },

      labelNoZeros: {
        ...this.standardLabelProps(),
        filter: 'agSetColumnFilter',
        valueFormatter: (params) => { return this.removeZeros(params.value) }
      },




      //------------------------------
      // percent 
      //------------------------------
      percent: {
        ...this.standardPercentProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => { return (isNaN(params.value)) ? '' : this.cphPipe.transform(params.value, 'percent', 0) },
      },
      percentWithPlusMinus: {
        ...this.standardPercentProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => { return (isNaN(params.value)) ? '' : this.cphPipe.transform(params.value, 'percent', 0, true) },
      },
      percentWithColour: {
        ...this.standardPercentProps(),
        cellClass: (params) => this.cellClassProviderWithColourFont(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => { return (isNaN(params.value)) ? '' : this.cphPipe.transform(params.value, 'percent', 0) },
      },
      percent1dp: {
        ...this.standardPercentProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => { return (isNaN(params.value)) ? '' : this.cphPipe.transform(params.value, 'percent1dp', 1) },
      },
      percent2dp: {
        ...this.standardPercentProps(),
        cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
        valueFormatter: (params) => { return (isNaN(params.value)) ? '' : this.cphPipe.transform(params.value, 'percent2dp', 1) },
      },




      //------------------------------
      // other
      //------------------------------

      days:{
          ...this.standardNumberProps(),
          valueGetter: (params) => {
            return `${this.numericNumberGetter(params)} days`
          },
          cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },
      miles:{
          ...this.standardNumberProps(),
          valueGetter: (params) => {
            const result = this.numericNumberGetter(params);
            if(result==''){
              return ''
            }
            else if(!result){
              return ''
            }
            else{
              return this.constants.pluralise(result,'mile','miles') 
            }
          },
          cellClass: (params) => this.cellClassProvider(params, topBottomHighlights, 'ag-right-aligned-cell'),
      },
      special: {
        cellClass: 'agAlignCentre',
        filter: 'agTextColumnFilter',
      },

      image: {
        cellClass: 'agAlignCentre',
        filter: 'agTextColumnFilter',
      },


      htmlLink: {
        cellClass: 'agAlignCenter',
        filter: 'agTextColumnFilter',
        cellRenderer: (params) => {
          if (!params.value) {  // Check if the value is null or an empty string
            return '';  // Return empty string to do nothing
          }

          // Return HTML string with link. The onclick attribute uses window.open to open in a new tab.
          return `<a href="${params.value}" target="_blank" class="custom-link">${params.value}</a>`;
        }
      }

      // payout: {
      //   filter: 'agNumberColumnFilter',
      //   cellClass: (params) => this.cellHighlighter(params)
      // },
      //text: { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter', },

    }
  }

  // For CommissionPayouts




  standardDateProperties() {
    return {
      cellClass: 'agAlignCentre',
      enableRowGroup: true,
      filter: 'agSetColumnFilter',
      filterParams: {
        //buttons: ['clear', 'apply'],
        treeList: true,
        treeListFormatter: (pathKey, level, parentPathKeys) => {
          if (level === 1 && pathKey) {
            try {

              return `${this.cphPipe.transform(pathKey, 'intToMonth', 0)}`
            }
            catch (error) {
              return 'err'
            }
          }
          return pathKey;
        }
      } as ISetFilterParams<any, Date>,

    }
  }

  // formatDateForFilter(params: ValueGetterParams) {
  //   const value: string = params.data[params.colDef.field];
  //   if (!value) { return null; }
  //   return new Date(value.split('T')[0]);
  // }


  formatDateForFilter(params: ValueGetterParams) {
    const value: string = params.data[params.colDef.field];
    if (!value) {
      return null;
    }
    try {
      // Ensure the date string is in a proper format without time
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date');
      }
      // Format the date to "YYYY-MM-DD"
      const formattedDate = date.toISOString().split('T')[0];
      return new Date(formattedDate);
    } catch (error) {
      console.error('Error formatting date for filter:', error, 'with value:', value);
      return null;
    }
  }

  removeZeros(value: any) {
    return value == 0 ? "" : value;
  }

  standardNumberProps() {
    return {
      valueGetter: (params) => {
        return this.numericNumberGetter(params)
      },
      filter: 'agNumberColumnFilter',
      filterParams: {
        defaultOption: 'equals'
      },
      sortable: true,
      comparator: (num1, num2) => { return num1 - num2 },
    }
  }


  standardCurrencyProps(topBottomHighlights: TopBottomHighlightRule[]) {
    return {
      valueGetter: (params) => {
        return this.numericNumberGetter(params)
      },
      comparator: (num1, num2) => { return num1 - num2 },
      filter: 'agNumberColumnFilter',
      sortable: true,
    }
  }



  numericNumberGetter(params: ValueGetterParams) {

    let fields: string[] = params.colDef.field.split('.');
    if (fields.length === 1) {
      if (!params.data) {
        //is a grouped node
        let userProvidedColDefs = params.column.getUserProvidedColDef() as CPHColDef;
        if (userProvidedColDefs.shouldTotal) {
          //should average but only if has value
          let field = params.colDef.field;
          let total = 0;

          const resultingLeafs = [];
          this.agGridMethodsService.aggregateUnfilteredLeafs(params.node, resultingLeafs);
          resultingLeafs.forEach(leaf => {
            if (leaf.data[field] > 0) {
              
              total += leaf.data[field]
            }
          })
          return total;
        }
        else if (userProvidedColDefs.shouldAverageIfValue) {
          //should average but only if has value
          let field = params.colDef.field;
          let denominator = 0;
          let numerator = 0;

          const resultingLeafs = [];
          this.agGridMethodsService.aggregateUnfilteredLeafs(params.node, resultingLeafs);
          resultingLeafs.forEach(leaf => {
            if (leaf.data[field] > 0 || leaf.data[field] < 0) {
              denominator++;
              numerator += leaf.data[field]
            }
          })
          return utilities.div(numerator, denominator);
        }
        else if (userProvidedColDefs.shouldAverage) {
          //should average
          let field = params.colDef.field;
          let denominator = 0;
          let numerator = 0;


          //build up children
          const resultingLeafs = [];
          this.agGridMethodsService.aggregateUnfilteredLeafs(params.node, resultingLeafs);

          resultingLeafs.forEach(leaf => {
            denominator++;
            numerator += parseInt(leaf.data[field])
          })
          return utilities.div(numerator, denominator);
        } else {
          //just total them

          let total = 0;

          const resultingLeafs = [];
          this.agGridMethodsService.aggregateUnfilteredLeafs(params.node, resultingLeafs);


          resultingLeafs.forEach(leaf => {
            total += Number(leaf.data[fields[0]])
          })
          return total;
        }
      }
      return Number(params.data[fields[0]]);
    }
    if (fields.length === 2) {
      if (!params.data) { return '' }
      return Number(params.data[fields[0]][fields[1]]);
    }
  }



  standardLabelProps() {
    return {
      cellClass: 'agAlignLeft',
      enableRowGroup: true,
      enablePivot: true,
      sortable: true,

    }
  }



  standardBooleanProps() {
    return {
      enablePivot: true,
      cellClass: 'agAlignCentre',
      filter: 'agSetColumnFilter',
      filterParams: {
        cellRenderer: (params: ValueFormatterParams) => this.booleanRenderer(params)
      },
      cellRenderer: (params: ValueFormatterParams) => this.booleanRenderer(params),
    }
  }




  booleanRenderer(params: ValueFormatterParams) {
    console.log(params);
    if(!params){return ''}
    if (params.node.isRowPinned()) { return null; }
    if (params.node && params.node.group && params.node.allLeafChildren) {
      // Count true values among all leaf children in the group.
      const trueCount = params.node.allLeafChildren.reduce((acc: number, child: any) => {
        const childValue = child.data[params.colDef.field];
        return acc + ((childValue === true || childValue === 'true') ? 1 : 0);
      }, 0);
      return trueCount.toString();
    }
    
    const value = params.value;
    return this.booleanValueRenderer(value);
  }

  booleanValueRenderer(value:boolean|string){
    if (value === '(Select All)') { return value }
    else if (value === true || value === 'true') {
      return '&#x2714;'
    } else {
      return '&#x2717;' //'&square;'
    }
  }

  numberValueGetter(params: ValueFormatterParams, decimalPlaces: number, prefix: boolean) {
    //if (!params.colDef.field) { return '' }
    //if (!params.data) { return '' }
    //const val = this.constants.getNestedValue(params.data, params.colDef.field);
    return (isNaN(params.value)) ? '' : this.cphPipe.transform(params.value, 'number', decimalPlaces, prefix)
  }



  standardPercentProps() {
    return {
      filter: 'agNumberColumnFilter',
      valueGetter: (params) => {
        return this.numericNumberGetter(params)
      },
      filterParams: {
        numberParser: value => {
          return value == null ? null : value / 100;
        },
        numberFormatter: value => {
          return value == null ? null : value * 100;
        },
        defaultOption: 'greaterThanOrEqual'
      }
    }
  }



  cellClassProvider(params: CellClassParams, highlights: TopBottomHighlightRule[], baseClass: string): string {
    let cellClass: string = baseClass;//'ag-right-aligned-cell';
    if (highlights.length === 0) { return cellClass }
    let goodOrBad: string = this.provideGoodOrBadForCellValue(params, highlights);

    if (!goodOrBad) { return cellClass }

    cellClass += ` ${goodOrBad}`;
    return cellClass

  }

  cellClassProviderWithColourFont(params: CellClassParams, highlights: TopBottomHighlightRule[], baseClass: string) {

    let backgroundClass = this.cellClassProvider(params, highlights, baseClass)
    //still here so no goods / bads so do number colour
    if (params.value < 0) {
      return `${backgroundClass} badFont`
    } else {
      return `${backgroundClass} goodFont`  //this is good
    }
  }

  cellClassProviderWithColourFontFlipGood(params: CellClassParams, highlights: TopBottomHighlightRule[], baseClass: string) {

    let backgroundClass = this.cellClassProvider(params, highlights, baseClass)
    //still here so no goods / bads so do number colour
    if (params.value < 0) {
      return backgroundClass  //this is good
    } else {
      return `${backgroundClass} badFont`
    }
  }



  private provideGoodOrBadForCellValue(params: CellClassParams, highlights: TopBottomHighlightRule[]): string {

    if (!params.colDef.field) { return }
    const rulesThisCol = highlights.find(x => x.field === params.colDef.field)
    if (!rulesThisCol) { return null; }

    if (rulesThisCol.goodColIds.includes(params.node.id)) {
      return 'good';
    } else if (rulesThisCol.badColIds.includes(params.node.id)) {
      return 'bad';
    }

    return null;
  }






  myValueGetter(params: ValueGetterParams, aggregation: 'sum' | 'average' | 'averageIfValue') {
    // if(!this.lastUpdatedFilteredNodes ||  (new Date().getTime() - this.lastUpdatedFilteredNodes.getTime()) > 1000){
    //   this.updateListOfVisibleNodes(params.api)
    // }


    if (params.data) {
      return params.data[params.colDef.field];
    } else {
      let filteredMap = {}
      params.api.forEachNodeAfterFilter(node => {
        filteredMap[node.id] = true
      })
      let tot = 0;
      let count = 0;
      let countWithValue = 0;
      params.node.allLeafChildren.forEach(child => {
        if (filteredMap[child.id]) {
          tot += child.data[params.colDef.field];
          count++;
          if (aggregation === 'averageIfValue' && child.data[params.colDef.field]) {
            countWithValue++;
          }
        }
      })
      if (aggregation === 'sum') { return tot; }
      else if (aggregation === 'average') { return count > 0 ? tot / count : 0; }
      else if (aggregation === 'averageIfValue') { return countWithValue > 0 ? tot / countWithValue : 0; }
    }
  }

}
