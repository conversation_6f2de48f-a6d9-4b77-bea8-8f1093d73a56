import { Component, Input, OnInit } from '@angular/core';
import { StrategyPriceBuildUp } from "src/app/model/StrategyPriceBuildUp";
import { ConstantsService } from 'src/app/services/constants.service';
import { StrategyPriceBuildUpLayersParams } from '../../strategyPriceBuildUpLayers/strategyPriceBuildUpLayers.component';
import { AutoPriceInsightsModalService } from '../autoPriceInsightsModal.service';

@Component({
  selector: 'pricingDetails',
  templateUrl: './pricingDetails.component.html',
  styleUrls: ['./pricingDetails.component.scss']
})
export class PricingDetailsComponent implements OnInit {


  @Input() useTestStrategy:boolean;


  get buildUpData(): StrategyPriceBuildUp[] {
    return  this.service.modalItem.PriceBuildUp.filter(x=>x.IsRelatedToTestStrategy === this.useTestStrategy);
  }


  get totalImpact(){
    return this.constantsService.sum(this.buildUpData.map(x => x.Impact))
  };

  get strategyPrice(){
    return this.useTestStrategy ? this.service.modalItem.AdvertDetail.TestStrategyPrice : this.service.modalItem.AdvertDetail.StrategyPrice
  } ;

  get buildTheBuildUpLayers():StrategyPriceBuildUpLayersParams{
    return {
      buildUpData: this.buildUpData,
      retailRating: this.service.modalItem.AdvertDetail.RetailRating,
      daysToSell: this.service.modalItem.AdvertDetail.DaysToSellAtCurrentSelling,
      daysBookedIn: this.service.modalItem.AdvertDetail.DaysBookedIn,
      daysListed: this.service.modalItem.AdvertDetail.DaysListed,
      daysInStock: this.service.modalItem.AdvertDetail.DaysInStock,
      make: this.service.modalItem.AdvertDetail.Make,
      specificColour: this.service.modalItem.AdvertDetail.SpecificColour,
      ageAndOwners: this.service.modalItem.AdvertDetail.AgeAndOwners,
      odometer: this.service.modalItem.AdvertDetail.OdometerReading,
      performanceRating: this.service.modalItem.AdvertDetail.PerfRatingScore,
      fuelType: this.service.modalItem.AdvertDetail.FuelType || '',
      valueBand: this.determineValueBand(this.service.modalItem.AdvertDetail.ValuationAdjRetail || 0),
      regYear: this.service.modalItem.AdvertDetail.FirstRegisteredDate ? new Date(this.service.modalItem.AdvertDetail.FirstRegisteredDate).getFullYear().toString() : new Date().getFullYear().toString(),
      liveMarketCondition: this.service.modalItem.AdvertDetail.RetailMarketCondition ? (this.service.modalItem.AdvertDetail.RetailMarketCondition * 100).toString() + '%' : '0%'
    }
  }


  constructor(
    public constantsService: ConstantsService,
    public service: AutoPriceInsightsModalService
  ) { }

  ngOnInit(): void {

    //this.totalImpact = this.constantsService.sum(this.buildUpData.map(x => x.Impact));

  }







  showRuleSetComment(strategyPriceBuildUp:StrategyPriceBuildUp[]){
    return strategyPriceBuildUp.some(x=>x.RuleSetComment!='')
  }

  ruleSetCommentToShow(strategyPriceBuildUp:StrategyPriceBuildUp[]){
    return strategyPriceBuildUp.find(x=>x.RuleSetComment!='').RuleSetComment
  }

  private determineValueBand(price: number): string {
    if (price < 5000) return "£5k-£10k";
    if (price < 10000) return "£5k-£10k";
    if (price < 15000) return "£10k-£15k";
    if (price < 20000) return "£15k-£20k";
    if (price < 30000) return "£20k-£30k";
    if (price < 40000) return "£30k-£40k";
    if (price < 50000) return "£40k-£50k";
    return ">£50k";
  }

}
