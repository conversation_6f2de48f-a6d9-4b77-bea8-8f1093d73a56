import { Component, Input, OnInit } from '@angular/core';
import { StrategyPriceBuildUp } from "src/app/model/StrategyPriceBuildUp";
import { ConstantsService } from 'src/app/services/constants.service';
import { StrategyPriceBuildUpLayersParams } from '../../strategyPriceBuildUpLayers/strategyPriceBuildUpLayers.component';
import { AutoPriceInsightsModalService } from '../autoPriceInsightsModal.service';

@Component({
  selector: 'pricingDetails',
  templateUrl: './pricingDetails.component.html',
  styleUrls: ['./pricingDetails.component.scss']
})
export class PricingDetailsComponent implements OnInit {


  @Input() useTestStrategy:boolean;


  get buildUpData(): StrategyPriceBuildUp[] {
    return  this.service.modalItem.PriceBuildUp.filter(x=>x.IsRelatedToTestStrategy === this.useTestStrategy);
  }


  get totalImpact(){
    return this.constantsService.sum(this.buildUpData.map(x => x.Impact))
  };

  get strategyPrice(){
    return this.useTestStrategy ? this.service.modalItem.AdvertDetail.TestStrategyPrice : this.service.modalItem.AdvertDetail.StrategyPrice
  } ;

  get buildTheBuildUpLayers():StrategyPriceBuildUpLayersParams{
    return {
      buildUpData: this.buildUpData,
      retailRating: this.service.modalItem.AdvertDetail.RetailRating,
      daysToSell: this.service.modalItem.AdvertDetail.DaysToSellAtCurrentSelling,
      daysBookedIn: this.service.modalItem.AdvertDetail.DaysBookedIn,
      daysListed: this.service.modalItem.AdvertDetail.DaysListed,
      daysInStock: this.service.modalItem.AdvertDetail.DaysInStock,
      make: this.service.modalItem.AdvertDetail.Make,
      specificColour: this.service.modalItem.AdvertDetail.SpecificColour,
      ageAndOwners: this.service.modalItem.AdvertDetail.AgeAndOwners,
      odometer: this.service.modalItem.AdvertDetail.OdometerReading,
      performanceRating: this.service.modalItem.AdvertDetail.PerfRatingScore
    }
  }

  
  constructor(
    public constantsService: ConstantsService,
    public service: AutoPriceInsightsModalService
  ) { }

  ngOnInit(): void {
    
    //this.totalImpact = this.constantsService.sum(this.buildUpData.map(x => x.Impact));
    
  }







  showRuleSetComment(strategyPriceBuildUp:StrategyPriceBuildUp[]){
    return strategyPriceBuildUp.some(x=>x.RuleSetComment!='')
  }

  ruleSetCommentToShow(strategyPriceBuildUp:StrategyPriceBuildUp[]){
    return strategyPriceBuildUp.find(x=>x.RuleSetComment!='').RuleSetComment
  }




}
