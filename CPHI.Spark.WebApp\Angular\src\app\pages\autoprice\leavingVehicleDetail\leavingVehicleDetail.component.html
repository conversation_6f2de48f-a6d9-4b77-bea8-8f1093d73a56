<nav class="navbar">
    <nav class="generic">
        <h4 id="pageTitle">
            Leaving Vehicle Detail &nbsp;
        </h4>

        <div class="d-flex align-items-center">
            From:
            <div class="mx-2">
                <input type="date" class="form-control"
                       [value]="formatDateForInput(service.startDate)"
                       (change)="onDateChange($event, true)"
                       [disabled]="service.isRolling30Days">
            </div>
            To:
            <div class="mx-2">
                <input type="date" class="form-control"
                       [value]="formatDateForInput(service.endDate)"
                       (change)="onDateChange($event, false)"
                       [disabled]="service.isRolling30Days">
            </div>
        </div>

        <!-- Rolling 30 day toggle -->
        <div class="ps-3" *ngIf="show30DaysSlider">
            <sliderSwitch text="Rolling 30 day" (toggle)="toggleRolling30Days()"
                [defaultValue]="service.isRolling30Days">
            </sliderSwitch>
        </div>
    </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
    <div class="content-new">
        <div class="content-inner-new">
            <div id="gridHolder">

                <div id="gridHeader">
                    <statusBar (excelExportClick)="excelExport()"
                    
                        [gridColumnApi]="service.tableLayoutManagement.gridColumnApi"
                        [gridApi]="service.tableLayoutManagement.gridApi" [gridOptions]="gridOptions"></statusBar>
                    <tableLayoutManagement *ngIf="service.tableLayoutManagement.gridApi" 
                        ></tableLayoutManagement>

                    <div class="searchBox autoHeight">
                        <div class="searchBoxIconContainer">
                            <i class="searchBoxIcon fas fa-search"></i>
                        </div>
                        <form>
                            <input placeholder="{{ constants.translatedText.Common_Search }}" class="form-control ml-2"
                                type="text" [formControl]="service.searchTerm" />
                            <div *ngIf="!!service.searchTerm.value" (click)="clearSearchTerm()"
                                id="searchBarClearButton">
                                <i class="fas fa-times-circle"></i>
                            </div>
                        </form>
                    </div>
                </div>
                <ag-grid-angular class="ag-theme-balham h-100" [gridOptions]="gridOptions" [components]="components"></ag-grid-angular>
            </div>
        </div>
    </div>
</div>