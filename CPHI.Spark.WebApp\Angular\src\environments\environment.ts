import { SparkEnvironment } from "src/app/services/environment.service";
import packagejson from '../../package.json';

export const environment = {
  customer: 'Vindis',
  production: true,
  //webURL: 'https://vindisspark.cphi.co.uk',
  //backEndBaseURL: 'https://vindissparkapi.cphi.co.uk',
  version: packagejson.version,
  franchisePicker: false,
  stockGroupPicker: false,
  lateCostPicker: false,
  orderTypePicker: true,
  ageingOptions: true,
  displayCurrency: 'GBP',
  todayMap:{
    defaultPositionLat: 52.698926,
    defaultPositionLong: -1.046534,
    defaultZoom: 7,
  },
  // Better way for this?
  bookingsBar:{
    barStyle1: false,
    barStyle2: true,
  },
  constantsService:{
    returnDealsWithoutDuplicates: false,
  },
  dealDetails:{
    componentName: 'DealDetailsComponent',
    profitTableShowSale: false,
    profitTableShowCost: false,
    profitTableShowCommission: false,
    profitTableFinanceCommText: 'HP & Other Fin. Comm.',
    showDescription: false,
    showVehicleAge: false,
    showPaintProtection: true,
    paintProtectionText: 'Paint & Fabric',
    showPaintProtectionCost: false,
    showPaintProtectionSale: false,
    showPhysicalLocation: true,
    showIsDealClosed: true,
    showFinanceCo: false,
    showFinanceType: true,
    showWarrantyInfo: true,
    showRCIFinanceComm: false,
    showPCPFinanceComm: true,
    showStandardsCommission: false,
    showProPlusCommission: false,
    showSelectCommission: false,
    showGapInsurance: false,
    showTyreInsurance: false,
    showAlloyInsurance: false,
    showWheelGard: false,
    showServicePlan: false,
    showWarranty: false,
    showUnits: false,
    showDeliverySite: true,
    showAdditionalAddOnProfit: false,
    showCosmeticInsuranceSale: false,
    showCosmeticInsuranceCost: false,
    showCosmeticInsuranceCommission: false,
    showVATCost: true,
  },
  usedStockTable: {
    vindisFormatting: true,
    tactical: false,
    exManagementCount: false,
    exDemo: false
  },
  sideMenu: {
    reportPortal: true,
    scratchCards: false,
    stockLanding: false,
    stockList:  false,
    userMaintenance: true,
  },
  fullSideMenu: {
    description: ' Vindis Group Ltd',
  },
  citNoww: {
    tileHeader: 'CitNOWs Viewed as a Proportion of Qualifying WIPs',
    moveNissanValuesToRenault: false,
    renaultRegions: false,
    vindisRegions: true,
    excludeAudi: true,
    pcOfSalesEnquiries: true,
    pcOfInvoicedExtWips: true,
    pcOfSalesEnquiriesText: 'CitNOW Summary - Videos Viewed as a % of Sales Enquiries - ',
    pcOfInvoicedExtWipsText: 'CitNOW Summary - Videos Viewed as a % of Invoiced External WIPs - ',
    showCitNowVideoBreakdownModal: true,
    showVideosViewed: true
  },
  dealDetailModal:
  {
    showOtherProfit: true,
    showFinanceProfit: true,
    showAddOnProfit: true,
    showTotalProfit: true
  },
  allGroups: ['R', 'O', 'N', 'Z', 'T', 'D'],
  allFamilyCodes: ['Franchise', 'Non-Franchise', 'Tyres', 'Oil', 'Exchange'],
  dashboard: {
    sections: [

       //Overview dashboard
       {sectionName:"Overview",translatedTextField:"Common_Overview", translatedTextValue:"",  pageName: "dashboardOverview",enableSitesSelector:true, pages:[

        {pageName:"SalesPerformance", translatedTextField: "Dashboard_SalesPerformance_Title",translatedTextValue:""},
        {pageName:"FinanceAddOns", translatedTextField: "Dashboard_FinanceAddons_Title",translatedTextValue:""},
        {pageName:"StockReport", translatedTextField: "Dashboard_StockReport_Title",translatedTextValue:""},
        {pageName:"CitNow", translatedTextField: "Dashboard_CitNow_Title",translatedTextValue:""}
      ]},


      //Sales dashboard
      {sectionName:"SalesVindis",translatedTextField:"Common_Sales", translatedTextValue:"",  pageName: "dashboardSalesVindis",enableSitesSelector:true, pages:[

        {pageName:"SalesPerformance", translatedTextField: "Dashboard_SalesPerformance_Title",translatedTextValue:""},
        {pageName:"FinanceAddOns", translatedTextField: "Dashboard_FinanceAddons_Title",translatedTextValue:""},
        {pageName:"StockReport", translatedTextField: "Dashboard_StockReport_Title",translatedTextValue:""},
        {pageName:"Debtors", translatedTextField: "Dashboard_Debtors_Title",translatedTextValue:""},
        {pageName:"CitNow", translatedTextField: "Dashboard_CitNow_Title",translatedTextValue:""},
        {pageName:"ImageRatios", translatedTextField: "Dashboard_ImageRatios_Title", translatedTextValue: "" },
      ]},

      //Aftersales dashboard
      {sectionName:"Aftersales",translatedTextField:"Common_Aftersales", translatedTextValue:"",  pageName: "dashboardAfterSales",enableSitesSelector:true, pages:[
        {pageName:"ServiceSales", translatedTextField: "Dashboard_ServiceSales_Title",translatedTextValue:""},
        {pageName:"PartsSales", translatedTextField: "Dashboard_PartsSales_Title",translatedTextValue:""},
        {pageName:"ServiceBookings", translatedTextField: "Dashboard_ServiceBookings_Title",translatedTextValue:""},
        {pageName:"PartsStock", translatedTextField: "Dashboard_PartsStock_Title",translatedTextValue:""},
        {pageName:"EVHC", translatedTextField: "Dashboard_Evhc_Title",translatedTextValue:""},
        {pageName:"Debtors", translatedTextField: "Dashboard_Debtors_Title",translatedTextValue:""},
        {pageName:"CitNow", translatedTextField: "Dashboard_CitNow_Title",translatedTextValue:""},
        {pageName:"Wip", translatedTextField: "Dashboard_WipReport_Title",translatedTextValue:""}
      ]},
      //Site compare dashboard
      {sectionName:"SiteCompare",translatedTextField:"SiteCompare", translatedTextValue:"",  pageName: "dashboardSiteCompare",enableSitesSelector:true, pages:[
      ]}
    ],
    showStockCover: true,
    showZoeSales: false,
    showHandoverDiarySummary: false,
    showCashDebts: false,
    showBonusDebts: false,
    showRenaultRegistrations: false,
    showDaciaRegistrations: false,
    showFleetRegistrations: false,
    showUsedStockMerchandising: false,
    showCommissions: true,
    showFinanceAddonPerformance: true,
    showfAndIPerformanceRRG: false,
    showVocNPSTile: false,
    showActivityLevels: true,
    showActivityOverdues:false,
    showFleetPerformance: true,
    showVoC: true,
    showServiceBookings: true,
    showSalesmanEfficiency: true,
    showCitNow: true,
    showImageRatios: true,
    showEvhc: true,
    showFinanceAddons: true,
    showRegistrations: true,
    showSimpleDealsByDay: true,
    excludeTypesFromBreakDown: ['Fleet', 'Commercial', 'Corporate'],
    showFinanceAddonsAllSites: true,
    includeDemoStockInStockHealth:true,
  },
  debts:
  {
    agedDebts: true,
    includeBonuses: true,
    simpleSitesTable: false,
    showAgedOnPicker: false,
    showBonusDebtType: false
  },
  evhcTile: 'Vindis',
  horizontalBar:{
    title: 'Demo',
    exDemo: 'params.data.Stock.StockBreakdown.Demo',
    forRenault: false,
    forVindis: true
  },
  stockItemModal: {
    onlineMerchandising: false
  },
  wipsTable: {
    hideBookingColumn: true,
    hideDepartmentColumn: true,
    hideAccountColumn: true,
    hideDueDateOutColumn: true,
    hideWDateInColumn: true,
    hideWDateOutColumn: true,
    hidePartsColumn: true,
    hideOilColumn: true,
    hideLabourColumn: true,
    hideSubletColumn: true,
    hideProvisionColumn: true,
    hideCreatedColumn: true,
    hideNotesColumn: false,
  },
  stockTable: {
    hideTacticalColumn: true,
    hideExManagementColumn: true,
    hideExDemoColumn: true,
  },
  stockList: {
    hideAttentionGrabberColumn: true,
    hideCreatedDateColumn: true,
    hidePriceColumn: true,
    hidePriceExtracolumn: true,
    hideDolColumn: true,
    hideImageCountColumn: false,
    hideVideoCountColumn: true,
    hideOnRRGSiteColumn: true,
    hideImagesColumn: false,
    hideModelPicker: false,
    vehicleTypes: ['New', 'Used', 'Demo']
  },
  performanceLeague: {
    hideBadges: false,
    showDeliveredButton: true,
    incLeaversButton: false
  },
  sitesLeague:{
    includeToday: true,
  },
  overAgeStockTable: {
    hideDemoColumn: false,
    hideTacticalColumn: true,
    hideExManagementColumn: true,
    hideExDemoColumn: true,
    hideTradeColumn: false,
    usedColumnName: 'CoreUsed'
  },
  dealPopover:{
    showMetalProfit: true,
    showOtherProfit: true,
    showFinanceProfit: true,
    showAddons: true,
    showAddonProfit: true
  },
  orderBook:  {
    showNewOrderbook:false,
    showNewDealButton:false,
    hideDeliverySiteColumn: false,
    hideOemReferenceColumn: false,
    hideFinanceProfitColumn:false,
    hideVehicleTypeColumn:false,
    hideVehClassColumn: true,
    hideModelColumn: true,
    hideModelYearColumn: true,
    hideVehicleSourceColumn: true,
    hideDaysToDeliverColumn: false,
    hideLocationColumn: false,
    hideIsConfirmedColumn: false,
    hideIsClosedColumn: false,
    hideUnitsColumn: true,
    hideFinanceTypeColumn: false,
    hideSalesChannel: true,
    hideOrderAllocationDate: true,
    hideVehTypeColumn: false,
    hideIsLateCostColumn: true,
    hideOtherProfitColumn: false,
    hideMetalColumn: true,
    hideAddonsColumn: false,
    hideDiscountColumn: false,
    includeAccgDate: true,
    customDateTypes: [],
    showMetalSummary: true,
    showOtherSummary: true,
    showFinanceSummary: true,
    showInsuranceSummary: true,
    showAccountingDateButton:false,
    showDeliveryOptionButtons:true,
    defaultDateType: 'Delivery',
    siteColumnWidth: 80,
    customerColumnWidth: 130,
    vehicleClassColumnWidth: 30,
    salesExecColumnWidth: 100,
    descriptionColumnWidth: 200,
    showLateCost: true,
    showOrderOptions: false,
    hideOrderDateSelection: false,
    hideAuditColumn: true,
  },
  partsSales:
  {
      showMarginColPerc: true,
      showMarginCol: true,
      includeMarginCols: true
  },
  handoverDiary: {
    includeCustomerName: true,
    includeLastPhysicalLocation: true,
    includeHandoverDate: false,
    isInvoiced: false,
    isConfirmed: true,
    futureHandoversGreyedOut: false,
  },

  partsStockDetailedTable:{
    hideCreatedColumn: true,
    partStockBarCharts1: {
      headerName: '% > 6 months',
      field: 'PercentOver6months',
      colId: 'PercentOver6months'
    },
    partStockBarCharts2: {
      headerName: '% 6 - 12 months',
      field: 'Percent6to12Months',
      colId: 'Percent6to12Months'
    },
    showPartStockAgeingColumnsForRRG: false,
    showPartStockAgeingColumnsForVindis: true,
    hideOfWhichColumn: true,
    hideDeadValueColumn: true,
    hideDormantValueColumn: true,
    hideDeadProvColumn: true,
    hideDormantProvColumn: true,
    setClassesForVindis:true,
    setClassesForRRG:false
  },
  salesPerformance:{
    description: 'Orders created between',
    showFranchisePicker: true,
    showLateCostButtons: true,
    showIncludeExcludeOrders: true,
    showTradeUnitButtons: true,
    showMotabilityButtons: true,
    showCustomReportType: true,
    showAllSites: true
  },
  selectionsService: {
    ageingOptions: [
      { description: '90 days', ageCutoff: 90 },
      { description: '120 days', ageCutoff: 120 },
      { description: '180 days', ageCutoff: 180 },
      { description: '270 days', ageCutoff: 270 },
      { description: '1 Year', ageCutoff: 365 },
      { description: '2 Years', ageCutoff: 730 },
      { description: '2+ Years', ageCutoff: 10000 }, // Big number which should never really be reached
    ],
    ageingOption: { description: '90 days', ageCutoff: 90 },
    deliveryDateDateType: 'Delivery',
    eligibleForCurrentUserCheck: false
  },
  stockReport: {
    showAgePicker: false,
    hideOnRRGSiteCol: true,
    initialStockReport: 'Used Stock',
    seeUsedStockReport: true,
    seeAllStockReport: true,
    seeUsedMerchandisingReport: false,
    seeOverageStockReport: true
  },
  serviceBookingsTable:{
    showPrepHours: false,
    clickSiteEnable: false,
  },
  whiteboard:{
    showConfirmed: true,
    showNotConfirmed: true,
    showFinance: true,
    showAddons: true,
    showLateCostPicker: false,
  },
  serviceChannels: [
    { displayName: 'Retail', name: 'Retail', channelTags: ['retail'], icon: 'fas fa-wrench', hasHours: true, divideByChannelName: 'Retail', isLabour: true },
    { displayName: 'MOT', name: 'MOT', channelTags: ['mot'], icon: 'fas fa-wrench', hasHours: true, divideByChannelName: 'MOT', isLabour: true },
    { displayName: 'Internal', name: 'Internal', channelTags: ['internal'], icon: 'fas fa-car-wash', hasHours: true, divideByChannelName: 'Internal', isLabour: true },
    { displayName: 'Warranty', name: 'Warranty', channelTags: ['warranty'], icon: 'fas fa-engine-warning', hasHours: true, divideByChannelName: 'Warranty', isLabour: true },
    { displayName: 'Labour', name: 'Labour', channelTags: ['retail', 'internal', 'warranty', 'mot'], isTotal: true, icon: '', hasHours: true, divideByChannelName: 'Labour', isLabour: false },
    { displayName: 'Tyre/Sublet', name: 'Tyre/Sublet', channelTags: ['tyre', 'sublet'], icon: 'fas fa-tire', hasHours: true, divideByChannelName: 'Retail', isLabour: false },
    { displayName: 'Oil', name: 'Oil', channelTags: ['oilWarr', 'oilExt', 'oilInt', 'oil'], icon: 'fas fa-oil-can', hasHours: true, divideByChannelName: 'Retail', isLabour: false },
    { displayName: 'Other', name: 'Other', channelTags: ['other','sundry'], icon: 'fal fa-circle', hasHours: true, divideByChannelName: 'Retail', isLabour: false },
    { displayName: 'Total', name: 'Total', channelTags: ['retail', 'mot', 'internal', 'warranty', 'tyre', 'oilWarr', 'oilExt', 'oilInt', 'oil', 'sublet','other','sundry', 'mot'], isTotal: true, icon: '', hasHours: true, divideByChannelName: 'Labour', isLabour: false },
  ],
  partsChannels:[
    {displayName: 'Retail',  name: 'Retail', channelTags: ['retail', 'nonfran', 'network', 'trade'], icon: 'fas fa-wrench', channelTag: 'retail', hasHours: false, divideByChannelName: 'Retail', isLabour: false}, //added network in on 28Aug20
    {displayName: 'Internal',  name: 'Internal', channelTags: ['internal'], icon: 'fas fa-car-wash', channelTag: 'internal', hasHours: false, divideByChannelName: 'Internal', isLabour: false},
    {displayName: 'Workshop Internal', name: 'Workshop Internal', channelTags: ['wshopInternal'], icon: 'fas fa-car-wash', channelTag: 'wshopInternal', hasHours: false, divideByChannelName: 'Workshop Internal', isLabour: false},
    {displayName: 'Workshop Retail', name: 'Workshop Retail', channelTags: ['wshopRetail'], icon: 'fas fas fa-tire ', channelTag: 'wshopRetail', hasHours: false, divideByChannelName: 'Workshop Retail', isLabour: false},
    {displayName: 'Workshop Warranty', name: 'Workshop Warranty', channelTags: ['wshopWarranty'], icon: 'fas fa-engine-warning', channelTag: 'wshopWarranty', hasHours: false, divideByChannelName: 'Workshop Warranty', isLabour: false},
    {displayName: 'Total',  name: 'Total', isTotal: true, channelTags: ['retail', 'nonfran', 'internal', 'wshopInternal', 'wshopRetail', 'wshopWarranty'], icon: '', channelTag: 'total', hasHours: false, divideByChannelName: 'Total', isLabour: false},
  ],
  initialPageURL:"/dashboard",

  orderBookURL: "/orderBook",
  fleetOrderbookURL: "/fleetOrderbook",
  product:
  {
    tyreInsurance: 'HasTyreInsurance',
    tyreAlloyInsurance: 'HasTyreAndAlloyInsurance',
    showAlloyInsurance: true,
  },
  dealDone:{
    showVindisSitePicker: true,
    showRRGSitePicker: false,
    showRRGPopoverContent: false,
    showVindisPopoverContent: true,
  },
  evhc:{
    showTechTable: false
  },
  fAndISummary:{
    processTypeAndTypeAlloy: false,
    hideAlloyColumn: false
  },
  partsStock:{
    includeOfWhichColumns: true
  },
  getStockService:{
    disCalulation: true
  },
  dealsForTheMonth:{
    showMetal: true,
    showOther: true,
    showFinance: true,
    showAddons: true,
    showGpu: true,
    showBroughtInColumn: true,
    showIncludeExcludeOrders: true
  },
  partsStockSitesCoverTable: {
    partStockName: 'PartsStock',
  },

  dealsDoneThisWeek:{
    showPlotOptions: true
  },
  orderTypePickerOptions:{
    showRetail: true,
    showFleet: true
  },
  vehicleTypePicker:
  {
    showUsed: true,
    showNew: true,
    showAll: true,
    hiddenVehicleTypes: []
  },
  userSetup:{
    hideUploadReports: false,
    hideViewReports: false,
    hideCommReview: true,
    hideCommSelf: true,
    hideSerReviewer: true,
    hideSerSubmitter: true,
    hideStockLanding: true,
    hideSuperCup: true,
    hideIsSalesExec: false,
    hideAllowReportUpload: false,
    hideAllowReportCentre: false,
    hideLiveforecast: true,
  },
  languageSelection: false,


  serviceSummary: {
    showTableTypeSelector: true,
    defaultTableType: 'Cumulative',
    tableTypes: ['Cumulative','Daily'],
    defaultTimeOption: 'MTD',
    timeOptions: ['MTD', 'WTD', 'Yesterday'],
    showTechGroupColumns: true,
  },
  serviceSalesDashboard: {
    onlyLabour: false
  },
  isSingleSiteGroup: false,
  showRotationButton:false,
  vehiclePricing_StockReport_showBcaColumns: false,
  };
