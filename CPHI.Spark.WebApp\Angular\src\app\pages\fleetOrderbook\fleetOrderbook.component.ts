import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { ColumnState, IRowNode } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { FleetOrderTableState } from 'src/app/model/FleetOrderTableState';
import { FleetOrderbookPropertyUpdateType } from 'src/app/model/FleetOrderbookPropertyUpdateType';
import { FleetOrderbookRow } from 'src/app/model/FleetOrderbookRow';
import { FleetOrderbookTableStateParams } from 'src/app/model/FleetOrderbookTableStateParams';
import { FleetOrderbookUpdatePropsParams } from 'src/app/model/FleetOrderbookUpdatePropsParams';
import { FleetOrderbookUpdatedRow } from 'src/app/model/FleetOrderbookUpdatedRow';
import { FleetOrderbookService } from './fleetOrderbook.service';
import { SimpleTextModalComponent } from './simpleTextModal/simpleTextModal.component';
import { DecimalPipe } from '@angular/common';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { ExcelChoices, ExcelReportNames } from 'src/app/model/main.model';

interface UpdateDate{
  Label:string;
  Date:Date;
}


@Component({
  selector: 'app-fleetOrderbook',
  templateUrl: './fleetOrderbook.component.html',
  styleUrls: ['./fleetOrderbook.component.scss']
})



export class FleetOrderbookComponent implements OnInit {
  @ViewChild('topSection', { static: true }) topSection: ElementRef;
  @ViewChild('deleteTableStateModal', { static: true }) deleteTableStateModal: ElementRef;

  viewPortHeight: number;
  gridHeight: number;
  subscriptionComments: Subscription;
  subscriptionToTodayDealsClick: Subscription;
  propertyUpdateTypes = FleetOrderbookPropertyUpdateType

  fonNamesUpdatedSubscription: Subscription;
  lastUpdateDates:UpdateDate[]

  constructor(
    public service: FleetOrderbookService,
    public getData: GetDataMethodsService,
    private decimalPipe: DecimalPipe,
    public constants: ConstantsService

  ) {



  }


  ngOnInit() {
    this.initParams();
    this.service.initialiseTableLayoutManagement();
    this.viewPortHeight = this.getScreenSize();

    if (!this.service.rowData) {
      this.service.getData(this.service.isRenault, this.service.includeHidden, this.service.includeRemovedSince, true);
      this.getEditableOptionsData()
    } else {
      this.service.selections.triggerSpinner.emit({ show: false })
    }
    this.clearSearchTerm();

  }

  initParams(){

    // this.getData.getFleetOrderBookLastUpdated().subscribe((res: UpdateDate[]) => {
    //   this.lastUpdateDates = res;
    // }, () => {
    // })

    // const lastUpdateDates:UpdateDate[] = [];
    // console.log(this.constants.LastUpdatedDates, "this.constants.LastUpdatedDates!");
    // lastUpdateDates.push({Label:'BCA Stock',Date: new Date(this.constants.LastUpdatedDates['BCAStock'])})
    // lastUpdateDates.push({Label:'Ebbon Orders',Date: new Date(this.constants.LastUpdatedDates['EbbonOrders'])})
    // lastUpdateDates.push({Label:'Nissan Data',Date: new Date(this.constants.LastUpdatedDates['NissanData'])})
    // lastUpdateDates.push({Label:'AOL Report',Date: new Date(this.constants.LastUpdatedDates['AOLReport'])})
    // lastUpdateDates.push({Label:'Inventory Data',Date: new Date(this.constants.LastUpdatedDates['InventoryData'])})
    // lastUpdateDates.push({Label:'Vehicle Numbers to Vins',Date: new Date(this.constants.LastUpdatedDates['VehicleNumbersToVins'])})
    // lastUpdateDates.push({Label:'CDK invoiced deals',Date: new Date(this.constants.LastUpdatedDates['Deals'])})
    // this.lastUpdateDates = lastUpdateDates;
  }


  getLastUpdatedDates()
  {
    this.getData.getFleetOrderBookLastUpdated().subscribe((res: UpdateDate[]) => {
      this.lastUpdateDates = res;
    }, () => {
    })
  }

  refresh(){

      this.service.getData(this.service.isRenault, this.service.includeHidden, this.service.includeRemovedSince, false);
      this.getEditableOptionsData();
      setTimeout(() => {
        this.service.getTableState(this.service.chosenTableStateLabel, this.service.isRenault,true,true);
      }, 500)
  }


  ngOnDestroy() {
    if (this.subscriptionComments) { this.subscriptionComments.unsubscribe() }
    if (this.subscriptionToTodayDealsClick) { this.subscriptionToTodayDealsClick.unsubscribe() }

  }

  onResize(event) {
    this.service.selections.screenWidth = window.innerWidth;
    this.service.selections.screenHeight = window.innerHeight;
    if (this.service.tableLayoutManagement.gridApi) {
      this.service.tableLayoutManagement.gridApi.resetRowHeights();
      //this.resizeGrid();
    }
  }

  getEditableOptionsData() {
    this.service.apiAccess.get('api/FleetOrderbook', 'GetEditableOptions').subscribe(res => {
      this.service.editableOptions = res;
    })
  }

  @HostListener('window:resize', ['$event'])
  getScreenSize(event?) {
    return window.innerHeight;
  }



  public excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.service.tableLayoutManagement.gridApi.getModel()
    this.service.excel.createSheetObject(tableModel, 'Fleet Orderbook', 1.3);
  }

 
  public setIsRenaultTo(isRenault: boolean) {
    this.service.isRenault = isRenault;
    this.service.tableLayoutManagement.isRenault = isRenault;
    this.service.tableLayoutManagement.refreshReportsListEmitter.emit();
    this.service.currentColumnState = null;
    this.service.chosenTableStateLabel = null;

    // Orderbook
    if (!this.service.showTiles) {
      //const includeRemovedSince = this.service.includeRemoved ? this.service.includeRemovedSince : null;
      this.resetTableState();
      this.service.getData(isRenault, this.service.includeHidden, this.service.includeRemovedSince, true);
    // Tiles
    } else {
      this.service.getDashboardData();
    }
  }

  clearSearchTerm() {
    this.service.searchTerm.setValue("");
    if (this.service.tableLayoutManagement.gridApi) { this.service.tableLayoutManagement.gridApi.setQuickFilter(this.service.searchTerm.value) }

  }


  showUnHideRowsButton() {
    return !this.service.showTiles && this.service.chosenRowNodes?.length === 1 && this.service.chosenRowNodes[0]?.data?.IsHidden
  }


 

  setRowsMessage() {
    const headerName = this.service.lastClickedCell?.column.getUserProvidedColDef().headerName;
    if (headerName === 'Comments') {
      return `Add a comment to ${this.service.constants.pluralise(this.service.chosenRowNodes?.length, 'row', 'rows')}:`
    } else {
      return `Set the ${headerName} for ${this.service.constants.pluralise(this.service.chosenRowNodes?.length, 'row', 'rows')}:`
    }
  }

  areEditedRows() {
    return this.service.editedNodeIds?.length > 0
  }


  saveChangesMessage() {
    return `Save changes to ${this.service.constants.pluralise(this.service.editedNodeIds.length, 'row', 'rows')}?`
  }

  public saveEditedRows() {

    let editedNodes: IRowNode[] = []
    
    this.service.tableLayoutManagement.gridApi.forEachNode(node => {
      if (this.service.editedNodeIds.includes(node.id)) {
        editedNodes.push(node)
      }
    })

    //convert editedNodeIds

    let updateRows = editedNodes.map(x => new FleetOrderbookUpdatedRow(x.data));

    let updateParams: FleetOrderbookUpdatePropsParams = {
      UpdatedRows: updateRows,
      IncludeHidden: this.service.includeHidden,
      IncludeArchivedSince: this.service.includeRemovedSince,
      IsRenault: this.service.isRenault,
      FilterChoices: null
    };

    this.saveThenUpdateGrid(updateParams, editedNodes)
  }




  private saveThenUpdateGrid(updateParams: FleetOrderbookUpdatePropsParams, rowNodes: IRowNode[]) {
    this.service.apiAccess.post('api/FleetOrderbook', 'UpdateProperties', updateParams).subscribe(res => {
     
      //clear the fact that they are edited
      this.service.editedNodeIds = [];
      this.service.tableLayoutManagement.gridApi.redrawRows({ rowNodes: rowNodes })
      this.service.tableLayoutManagement.gridApi.flashCells({rowNodes: rowNodes});
      this.service.chosenRowNodes = [];
      this.service.tableLayoutManagement.gridApi.clearRangeSelection()
      this.service.tableLayoutManagement.gridApi.deselectAll();
      this.service.updateCounts();

      this.service.chosenRowNodes = null;
      this.service.lastClickedCell = null;
      this.service.constants.toastSuccess(`Updated ${this.service.constants.pluralise(rowNodes.length, 'row', 'rows')}.  Press refresh to update the grid.`)
    });
  }



  saveExistingTableState() {

    const tableState = this.service.tableLayoutManagement.gridColumnApi.getColumnState()
    const isPivoted = this.service.tableLayoutManagement.gridColumnApi.isPivotMode();

    let payload: FleetOrderbookTableStateParams = {
      State: JSON.stringify(tableState),
      IsPivoted: isPivoted,
      FilterModel:  this.service.tableLayoutManagement.gridApi.isAnyFilterPresent() ? JSON.stringify(this.service.tableLayoutManagement.gridApi.getFilterModel()) : null,
      Label: this.service.chosenTableStateLabel,
      IsRenault: this.service.isRenault
    }


    this.service.apiAccess.post('api/FleetOrderbook', 'SaveTableState', payload).subscribe((res: FleetOrderTableState) => {
      this.service.constants.toastSuccess('Saved column layout');
    })


  }


  saveNewTableState() {
    if (!this.service.tableLayoutManagement.gridColumnApi) {
      return;
    }
    const tableState = this.service.tableLayoutManagement.gridColumnApi.getColumnState();
    const isPivoted = this.service.tableLayoutManagement.gridColumnApi.isPivotMode();

    const modalRef = this.service.modalService.open(SimpleTextModalComponent);
    modalRef.componentInstance.header = 'Choose label';
    modalRef.componentInstance.chosenLabel = `New layout created ${this.service.cphPipe.transform(new Date(),'date',0)}`
    modalRef.componentInstance.placeholder = 'Enter layout label...';

    modalRef.result.then(res=>{
      if(!res.chosenLabel){
        this.service.constants.toastDanger('Please provide a layout label')
        return;
      }

      //successfully chose
      let payload: FleetOrderbookTableStateParams = {
        State: JSON.stringify(tableState),
        IsPivoted: isPivoted,
        FilterModel:  this.service.tableLayoutManagement.gridApi.isAnyFilterPresent() ? JSON.stringify(this.service.tableLayoutManagement.gridApi.getFilterModel()) : null,
        Label: res.chosenLabel,
        IsRenault: this.service.isRenault
      }

      this.service.apiAccess.post('api/FleetOrderbook', 'SaveTableState', payload).subscribe((result: FleetOrderTableState) => {
        this.service.constants.toastSuccess('Saved column layout');
        this.service.chosenTableStateLabel = res.chosenLabel;
        this.service.currentColumnState = tableState
        this.service.tableStateLabels.push(res.chosenLabel)
      })
    },()=>{
      //dismiss chosen
    })
    

  }

  rowCount() {
    if (!this.service.tableLayoutManagement.gridApi) { return 0; }
    if(this.service.tableLayoutManagement.gridApi.getDisplayedRowCount() == 0){ return `(${this.service.constants.pluralise(this.service.tableLayoutManagement.gridApi.getDisplayedRowCount(), 'order', 'orders')})`; }
    return `(${this.service.constants.pluralise(this.service.tableLayoutManagement.gridApi.getDisplayedRowCount(), 'order', 'orders')}, of which `;
  }

  newRowCount(){
    return this.service.newItemsCount ? `${this.decimalPipe.transform(this.service.newItemsCount, '1.' + 0 + '-' + 0)} new)` : `0 new)`
  }

  itemsNotOnLatestDownloadCount(){
    return `${this.decimalPipe.transform(this.service.itemsNotOnLatestDownloadCount, '1.' + 0 + '-' + 0)}`
  }

  deleteTableState(isRenault: boolean) {
    if (!this.service.chosenTableStateLabel) {
      return;
    }
    this.service.apiAccess.deleteByString('api/FleetOrderbook', 'DeleteTableState', this.service.chosenTableStateLabel).subscribe((res) => {
      this.service.constants.toastSuccess('Deleted report')
      this.service.tableStateLabels = this.service.tableStateLabels.filter(x => x !== this.service.chosenTableStateLabel);
      this.resetTableState();
    })
  }

  downloadTemplate(report: string){

    if(report == 'Renault')
    { 
      let params: ExcelChoices = 
      {
        SheetName: "Renault Upload Template"
      };

      this.getData.getFleetOrderbookTemplate(ExcelReportNames.RenaultUploadTemplate, params); 
    }
    else 
    { 
      let params: ExcelChoices = 
      {
        SheetName: "Nissan Upload Template"
      };

      this.getData.getFleetOrderbookTemplate(ExcelReportNames.NissanUploadTemplate, params); 
    }



    
  }

  resetTableState() {
    this.service.chosenTableStateLabel = null;
    this.service.currentColumnState = null;
    this.service.tableLayoutManagement.gridColumnApi.setPivotMode(false);
    this.service.tableLayoutManagement.gridApi.setFilterModel(null);
    //this.service.pivotMode = false;
    this.service.tableLayoutManagement.gridColumnApi.resetColumnState()
    this.service.tableLayoutManagement.gridApi.setPinnedBottomRowData(null)
  }


  maybeDeleteTableState(isRenault: boolean) {
    this.service.modalService.open(this.deleteTableStateModal, { size: 'md', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then(() => {
      this.deleteTableState(isRenault);
    }, () => {
      this.service.modalService.dismissAll();
    });
  }

 


}
