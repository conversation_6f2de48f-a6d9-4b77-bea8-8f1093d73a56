﻿using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using CPHI.Spark.Repository.Migrations;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Client;
using System.Linq;
using System.Text.RegularExpressions;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface IValuationsDataAccess
   {


      Task<IEnumerable<ValuationBatchResult>> GetValuationBatchResults(GetValuationBatchResultsParams parms, string userSiteIds);
      Task<ValuationSummary> GetLatestVehicleValuationSummaryForAdvert(int advertId);
      Task<ValuationResultForAdvert> GetPreviousValuationResultForAdvert(int advertId);
      Task<List<VehicleValuation>> GetAllVehicleValuations(int vehicleValuationId);
      Task<VehicleValuation> CreateVehicleValuation(VehicleValuation vehicleValuation);
      Task<VehicleValuationBatch> CreateVehicleValuationBatch(VehicleValuationBatch vehicleValuationBatch);
      Task AddVehicleValuationOptions(List<VehicleValuationOption> vehicleValuationOptionsToAdd);
      Task<VehicleValuationBatch> GetOldestVehicleValuationsBatchToProcess(Model.DealerGroupName dealerGroup);
      Task UpdateVehicleValuationsBatchStatus(int id, string status, DateTime? completeDate = null);
      Task<VehicleValuationBatch> UpdateVehicleValuationBatchAndVehicleValuations(VehicleValuationBatch vehicleValuationBatch);
      Task CreateVehicleValuationRatingBySite(List<VehicleValuationRatingBySite> vehicleValuationRatingBySites);
      Task<bool> CheckVehicleValuationsBatchInProgress();
      Task DeleteVehicleSpecBuildForAdvert(int advertId);
      Task<IEnumerable<PrepCostItem>> GetPrepCosts(string model);
      Task<IEnumerable<StockLevelAndCover>> GetStockLevelAndCover(string model, int retailerSiteId);
      Task<IEnumerable<ValuationPriceScenarioBatchResult>> GetValuationPriceScenarioBatchResults(GetValuationBatchResultsParams parms, DealerGroupName dealerGroup);
      Task<IEnumerable<VehicleSpecOption>> GetVehicleSpecOptions(int batchId);
      Task<VehicleAdvertSnapshot> GetMarketAndAdjustPricesForAdvert(int advertId);
      Task<VehicleValuationWithStrategyPrice> LoadVehicleValuation(int valuationId, DealerGroupName userDealerGroup);
      Task RemoveVehicleValuationRatingBySite(int vehicleValuationId);
      Task UpdateVehicleValuationCostings(int valuationId, ValuationCostingDTO costing);
      Task RemoveVehicleValuationOptionMappingsAsync(int vehicleValuationId);
      Task<List<VehicleValuationBatchDTO>> GetVehicleValuationBatches(DealerGroupName dealerGroup, bool? single);
      Task<List<OverdueValuationBatch>> GetOverDueBatches(DealerGroupName dealerGroup);
      Task<List<VehicleOption>> InsertOptionsIfRequired(List<VehicleOption> vehicleOptions);
      Task DeleteExistingOptionMappings(List<int> vehicleValuationIds);
      Task<List<TradePriceSetting>> GetTradePriceSettings(int dealerGroupid);
      Task<TradePriceSetting?> GetTradePriceSettingsForRetailerSite(int retailerSiteId);
   }

   public class ValuationsDataAccess : IValuationsDataAccess
   {
      private readonly string _connectionString;
      public ValuationsDataAccess(string connectionString)
      {
         this._connectionString = connectionString;
      }


      public async Task<IEnumerable<ValuationBatchResult>> GetValuationBatchResults(GetValuationBatchResultsParams parms, string userRetailerSiteIds)  //note Adverts not Advert like the method above
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            int onlyShowBestLocation = parms.OnlyShowBestLocation == true ? 1 : 0;
            var spParams = new DynamicParameters(new { parms.BatchIds, onlyShowBestLocation, userEligibleRetailerSites = userRetailerSiteIds });
            return await dapper.GetAllAsync<ValuationBatchResult>("autoprice.GET_ValuationBatchResults", spParams, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      public async Task<IEnumerable<ValuationPriceScenarioBatchResult>> GetValuationPriceScenarioBatchResults(GetValuationBatchResultsParams parms, DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            int dealerGroupId = (int)dealerGroup;

            var spParams = new DynamicParameters(new { BatchId = parms.BatchIds, dealerGroupId });
            return await dapper.GetAllAsync<ValuationPriceScenarioBatchResult>("autoprice.GET_ValuationPriceScenarioBatchResult", spParams, commandType: System.Data.CommandType.StoredProcedure);
         }
      }




      public async Task<List<VehicleValuation>> GetAllVehicleValuations(int vehicleValuationId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            return await db.VehicleValuations.Where(v => v.Id == vehicleValuationId).ToListAsync();
         }
      }

      public async Task<VehicleValuation> CreateVehicleValuation(VehicleValuation vehicleValuation)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.VehicleValuations.AddAsync(vehicleValuation);
            await db.SaveChangesAsync();
            return vehicleValuation;
         }
      }

      public async Task<VehicleValuationWithStrategyPrice> LoadVehicleValuation(int valuationId, DealerGroupName userDealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            VehicleValuationRatingBySite? best = await db.VehicleValuationRatingBySites
              .Include(x => x.VehicleValuation)
              .ThenInclude(x => x.VehicleValuationBatch)
              .ThenInclude(x => x.LastRunBy)
              .Include(x => x.RetailerSite)
              .Where(x => x.VehicleValuation.Id == valuationId)
              .OrderByDescending(x => x.StrategyPrice)
              .FirstOrDefaultAsync();

            if (best == null)
            {
               throw new KeyNotFoundException();
            }

            if (best.VehicleValuation.Paint == null) { best.VehicleValuation.Paint = (int)Math.Round(best.RetailerSite.TargetPaintPrep, 0, MidpointRounding.AwayFromZero); }
            if (best.VehicleValuation.AdditionalMech == null) { best.VehicleValuation.AdditionalMech = (int)Math.Round(best.RetailerSite.TargetAdditionalMech, 0, MidpointRounding.AwayFromZero); }
            if (best.VehicleValuation.Fee == null) { best.VehicleValuation.Fee = (int)Math.Round(best.RetailerSite.TargetAuctionFee, 0, MidpointRounding.AwayFromZero); }
            if (best.VehicleValuation.Delivery == null) { best.VehicleValuation.Delivery = (int)Math.Round(best.RetailerSite.TargetDelivery, 0, MidpointRounding.AwayFromZero); }
            if (best.VehicleValuation.Other == null) { best.VehicleValuation.Other = (int)Math.Round(best.RetailerSite.TargetOtherCost, 0, MidpointRounding.AwayFromZero); }

            return new VehicleValuationWithStrategyPrice(best.VehicleValuation, (int)Math.Round(best.StrategyPrice, 0, MidpointRounding.AwayFromZero));


         }
      }




      public async Task RemoveVehicleValuationRatingBySite(int vehicleValuationId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Fetch the VehicleValuationOptions for the given VehicleValuationId
            var itemsToRemove = await db.VehicleValuationRatingBySites
                .Where(vvo => vvo.VehicleValuation_Id == vehicleValuationId)
                .ToListAsync();

            if (itemsToRemove.Any())
            {
               // Remove the fetched VehicleValuationOptions
               db.VehicleValuationRatingBySites.RemoveRange(itemsToRemove);
               await db.SaveChangesAsync();
            }
         }
      }



      public async Task CreateVehicleValuationRatingBySite(List<VehicleValuationRatingBySite> vehicleValuationRatingBySites)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.VehicleValuationRatingBySites.AddRangeAsync(vehicleValuationRatingBySites);
            await db.SaveChangesAsync();
         }
      }

      public async Task UpdateVehicleValuationCostings(int valuationId, ValuationCostingDTO costing)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var r = await db.VehicleValuations.FirstAsync(x => x.Id == valuationId);
            if (r == null)
            {
               throw new KeyNotFoundException();
            }
            r.Sales = costing.Sales;
            r.Valet = costing.Valet;
            r.Cost = costing.Cost;
            r.SpareKey = costing.SpareKey;
            r.MOT = costing.MOT;
            r.MOTAdvisory = costing.MOTAdvisory;
            r.Servicing = costing.Servicing;
            r.Paint = costing.Paint;
            r.Tyres = costing.Tyres;
            r.Warranty = costing.Warranty;
            r.Parts = costing.Parts;
            r.AdditionalMech = costing.AdditionalMech;
            r.Fee = costing.Fee;
            r.Delivery = costing.Delivery;
            r.Other = costing.Other;
            r.Valuation = costing.Valuation;
            r.Profit = costing.Profit;
            r.IsVatQualifying = costing.IsVatQualifying;
            r.VatCost = costing.VatCost;

            await db.SaveChangesAsync();
         }
      }




      public async Task<VehicleValuationBatch> CreateVehicleValuationBatch(VehicleValuationBatch vehicleValuationBatch)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            try
            {

               await db.VehicleValuationBatches.AddAsync(vehicleValuationBatch);
               await db.SaveChangesAsync();
               return vehicleValuationBatch;
            }
            catch (Exception ex)
            {
               throw;
            }
         }
      }

      public async Task<VehicleValuationBatch> UpdateVehicleValuationBatchAndVehicleValuations(VehicleValuationBatch vehicleValuationBatch)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            db.VehicleValuationBatches.Update(vehicleValuationBatch);
            await db.SaveChangesAsync();
            return vehicleValuationBatch;
         }
      }


      public async Task<ValuationSummary> GetLatestVehicleValuationSummaryForAdvert(int advertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            //we will get the latest snapshot
            var latestSnapshot = await db.VehicleAdvertSnapshots.Where(x => x.VehicleAdvert_Id == advertId).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
            if (latestSnapshot == null)
            {
               throw new Exception($"No vehicle snapshot found for advertId {advertId}, this was unexpected");
            }
            var specBuild = await db.VehicleAdvertSpecBuilds.Where(x => x.VehicleAdvert_Id == advertId).FirstOrDefaultAsync();
            //got a snapshot, now find the spec options (might well not be)
            List<VehicleSpecOption> specOptions = await db.VehicleAdvertSpecBuildOptions
                .Include(x => x.VehicleOption)
                .Include(x => x.VehicleAdvertSpecBuild)
                .Where(x => x.VehicleAdvertSpecBuild.VehicleAdvert_Id == advertId)
                .Select(x => new VehicleSpecOption()
                {
                   Category = x.VehicleOption.Category,
                   //Impact = x.Impact,  //cannot include impact as this was merely the impact when we created this specBuild, it may have changed since
                   IsChosen = x.IsChosen,
                   IsStandard = x.IsStandard,
                   Name = x.VehicleOption.Name,
                   OriginalPrice = x.VehicleOption.OriginalPrice
                }).ToListAsync();

            return new ValuationSummary(latestSnapshot, specOptions, specBuild != null);
         }

      }


      public async Task<ValuationResultForAdvert> GetPreviousValuationResultForAdvert(int advertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            VehicleAdvertSpecBuild specBuild = await db.VehicleAdvertSpecBuilds.Where(x => x.VehicleAdvert_Id == advertId).Include(x => x.LastUpdatedBy).FirstOrDefaultAsync();

            //find the spec options
            List<VehicleSpecOption> specOptions = await db.VehicleAdvertSpecBuildOptions
                .Include(x => x.VehicleOption)
                .Include(x => x.VehicleAdvertSpecBuild)
                .Where(x => x.VehicleAdvertSpecBuild.VehicleAdvert_Id == advertId)
                .Select(x => new VehicleSpecOption()
                {
                   Category = x.VehicleOption.Category,
                   Impact = x.Impact,
                   IsChosen = x.IsChosen,
                   IsStandard = x.IsStandard,
                   Name = x.VehicleOption.Name,
                   OriginalPrice = x.VehicleOption.OriginalPrice
                }).ToListAsync();

            if (specBuild != null)
            {
               return new ValuationResultForAdvert(specBuild, specOptions);
            }
            return null;
         }
      }


      public async Task AddVehicleValuationOptions(List<VehicleValuationOption> vehicleValuationOptionsToAdd)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            await db.VehicleValuationOptions.AddRangeAsync(vehicleValuationOptionsToAdd);
            await db.SaveChangesAsync();
         }
      }

      public async Task RemoveVehicleValuationOptionMappingsAsync(int vehicleValuationId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            // Fetch the VehicleValuationOptions for the given VehicleValuationId
            var optionsToRemove = await db.VehicleValuationOptions
                .Where(vvo => vvo.VehicleValuation_Id == vehicleValuationId)
                .ToListAsync();

            if (optionsToRemove.Any())
            {
               // Remove the fetched VehicleValuationOptions
               db.VehicleValuationOptions.RemoveRange(optionsToRemove);
               await db.SaveChangesAsync();
            }
         }
      }


      public async Task<VehicleValuationBatch> GetOldestVehicleValuationsBatchToProcess(Model.DealerGroupName dealerGroup)
      {
         try
         {

            using (var db = new CPHIDbContext(_connectionString))
            {
               var result = await db.VehicleValuationBatches
                   .Include(x => x.LastRunBy)
                   .Where(x => x.LastRunBy.DealerGroup_Id == (int)dealerGroup)
                .Include(v => v.VehicleValuations.Where(x => x.HasBeenValued == false))
                .Where(v => v.ValuationCompleteDate == null)
                .OrderBy(v => v.Id)
                .FirstOrDefaultAsync();

               return result;
            }
         }
         catch (Exception ex)
         {
            throw new Exception(ex.Message);
         }
      }

      public async Task<bool> CheckVehicleValuationsBatchInProgress()
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var result = await db.VehicleValuationBatches.Where(v => v.Status == "Valuing").FirstOrDefaultAsync();
            return result != null;
         }
      }

      public async Task UpdateVehicleValuationsBatchStatus(int id, string status, DateTime? completeDate = null)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var item = await db.VehicleValuationBatches.Where(x => x.Id == id).FirstOrDefaultAsync();
            item.Status = status;
            item.ValuationCompleteDate = completeDate.HasValue ? completeDate : null;
            await db.SaveChangesAsync();
         }
      }


      public async Task<List<VehicleValuationBatchDTO>> GetVehicleValuationBatches(DealerGroupName dealerGroup, bool? single)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var query = db.VehicleValuationBatches
              .AsNoTracking()
              .Include(x => x.VehicleValuations)
              .Include(x => x.LastRunBy)
              .Join(db.People,
                  v => v.LastRunBy_Id,
                  p => p.Id,
                  (v, p) => new { ValuationBatch = v, Person = p }
              )
              .Where(x => x.Person.DealerGroup_Id == (int)dealerGroup)
              .Select(x => new VehicleValuationBatchDTO()
              {
                 Id = x.ValuationBatch.Id,
                 LastRunBy = x.Person.Name,
                 LastRunDate = x.ValuationBatch.LastRunDate,
                 TotalVehicles = x.ValuationBatch.VehicleValuations.Count(),
                 IsSingle = x.ValuationBatch.IsSingle,
                 VehicleReg = x.ValuationBatch.VehicleValuations.First().VehicleReg,
                 IsApplyPriceScenarios = x.ValuationBatch.ApplyPriceScenarios,
                 Status = x.ValuationBatch.Status,
                 ValuationCompleteDate = x.ValuationBatch.ValuationCompleteDate,
                 LastRunById = x.Person.Id,
                 Name = x.ValuationBatch.Name,
                 LastRunBySiteId = x.Person.CurrentSite_Id
              })
              .OrderByDescending(x => x.Id)
              .AsQueryable();

            if (single != null)
            {
               query = query.Where(x => x.IsSingle == single.Value);
            }

            return await query.ToListAsync();
         }
      }

      public async Task DeleteVehicleSpecBuildForAdvert(int advertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var buildItem = db.VehicleAdvertSpecBuilds.FirstOrDefault(x => x.VehicleAdvert_Id == advertId);
            var buildItemOptions = db.VehicleAdvertSpecBuildOptions.Where(x => x.VehicleAdvertSpecBuild_Id == buildItem.Id);

            db.VehicleAdvertSpecBuildOptions.RemoveRange(buildItemOptions);
            db.VehicleAdvertSpecBuilds.Remove(buildItem);

            await db.SaveChangesAsync();
            return;
         }
      }

      public async Task<IEnumerable<PrepCostItem>> GetPrepCosts(string model)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var spParams = new DynamicParameters(new { model });
            return await dapper.GetAllAsync<PrepCostItem>("autoprice.GET_PrepCosts", spParams, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      public async Task<decimal?> GetFixedCostPrep(int dealerGroupId, VehicleInformation vehicle)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            DateTime today = DateTime.Today;
            var ageInMonths = (today.Year - vehicle.FirstRegistered.Year) * 12 + today.Month - vehicle.FirstRegistered.Month;
            var spParams = new DynamicParameters(new { dealerGroupId, model = vehicle.Model, ageInMonths = ageInMonths });

            return await dapper.GetAsync<decimal?>("autoprice.GET_FixedCostsPrep", spParams, commandType: System.Data.CommandType.StoredProcedure);
         }
      }



      public async Task<IEnumerable<StockLevelAndCover>> GetStockLevelAndCover(string model, int retailerSiteId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var spParams = new DynamicParameters(new { model, retailerSiteId });
            return await dapper.GetAllAsync<StockLevelAndCover>("autoprice.GET_StockLevelAndCover", spParams, commandType: System.Data.CommandType.StoredProcedure);
         }
      }



      public async Task<IEnumerable<VehicleSpecOption>> GetVehicleSpecOptions(int batchId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string query = $"select vvo.IsStandard, vo.Category, vo.Name, vo.OriginalPrice, vvo.IsChosen, vvo.Impact  from autoprice.VehicleValuationOptions vvo  INNER JOIN autoprice.VehicleOptions vo on vo.Id = vvo.VehicleOption_Id INNER JOIN autoprice.VehicleValuations vv on vv.Id = vvo.VehicleValuation_Id WHERE vv.VehicleValuationBatch_Id = {batchId}";
            return await dapper.GetAllAsync<VehicleSpecOption>(query, null, commandType: System.Data.CommandType.Text);
         }
      }

      public async Task<VehicleAdvertSnapshot> GetMarketAndAdjustPricesForAdvert(int advertId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var result = await db.VehicleAdvertSnapshots.Where(v => v.VehicleAdvert_Id == advertId).OrderByDescending(v => v.SnapshotDate).FirstAsync();
            return result;
         }

      }

      public async Task<List<OverdueValuationBatch>> GetOverDueBatches(DealerGroupName dealerGroup)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            try
            {

               DateTime thresholdTime = DateTime.UtcNow.AddMinutes(-15);

               var result = await db.VehicleValuationBatches
                .Where(x => x.LastRunDate <= thresholdTime && x.Status != "Complete")
                .Where(x => x.LastRunBy.DealerGroup_Id == (int)dealerGroup)
                .Include(x => x.LastRunBy)
                .Select(x => new OverdueValuationBatch(x))
                .ToListAsync();

               return result;
            }
            catch (Exception ex)
            {
               throw ex;
            }
         }

      }



      public async Task<List<VehicleOption>> InsertOptionsIfRequired(List<VehicleOption> vehicleOptions)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            try
            {
               var dbVehicleOptions = new List<VehicleOption>();

               foreach (var option in vehicleOptions)
               {
                  // Check if the vehicle option already exists in the database
                  var existingOption = await db.VehicleOptions
                      .FirstOrDefaultAsync(o => o.Name == option.Name
                                                && o.Category == option.Category
                                                && o.OriginalPrice == option.OriginalPrice);

                  if (existingOption == null)
                  {
                     // If the option does not exist, insert it and track it for later use
                     db.VehicleOptions.Add(option);
                     dbVehicleOptions.Add(option); // Track the new option
                  }
                  else
                  {
                     // If it exists, add the existing one to the result list
                     dbVehicleOptions.Add(existingOption);
                  }
               }

               // Save any new records that were added to the database
               await db.SaveChangesAsync();

               // Return the full list of database versions of the options
               return dbVehicleOptions;
            }
            catch (Exception ex)
            {
               // Log or handle the exception
               throw new Exception("An error occurred while inserting vehicle options.", ex);
            }
         }
      }

      public async Task DeleteExistingOptionMappings(List<int> vehicleValuationIds)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var existingMappings = db.VehicleValuationOptions
                .Where(x => vehicleValuationIds.Contains(x.VehicleValuation_Id))
                .ToList();

            if (existingMappings.Any())
            {
               db.VehicleValuationOptions.RemoveRange(existingMappings);
               await db.SaveChangesAsync();
            }
         }
      }

      public async Task<List<TradePriceSetting>> GetTradePriceSettings(int dealerGroupId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var tradePriceSettings = await db.TradePriceSettings
                .Where(x => x.RetailerSite.DealerGroup_Id == dealerGroupId)
                .AsNoTracking()
                .ToListAsync();

            return tradePriceSettings;
         }
      }
      public async Task<TradePriceSetting?> GetTradePriceSettingsForRetailerSite(int retailerSiteId)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var tradePriceSetting = await db.TradePriceSettings
                .Where(x => x.RetailerSiteId == retailerSiteId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            return tradePriceSetting;
         }
      }
   }
}
