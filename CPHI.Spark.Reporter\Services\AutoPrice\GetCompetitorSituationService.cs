﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Repository;
using log4net;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Security.Policy;
using System.Threading.RateLimiting;
using System.Threading;
using System.Threading.Tasks;
using static log4net.Appender.ColoredConsoleAppender;
using Datadog.Trace;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class GetCompetitorSituationService
   {
      private readonly HttpClient httpClient;

      public GetCompetitorSituationService(HttpClient httpClient)
      {
         this.httpClient = httpClient;
      }

      public async Task GetCompetitorInformationAndSave(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0)
         { return; }
         try
         {
            logger.Info("----------------------------------------------------------");
            logger.Info("CompetitorInformation");

            //using (var parentScope = Tracer.Instance.StartActive("CompetitorInformation"))
            //{
            foreach (Model.DealerGroupName dealerGroup in dealerGroups)
            {
               //using (var childScope = Tracer.Instance.StartActive($"Begin dealergroup {dealerGroup}"))
               //{
               //   childScope.Span.SetTag("DealerGroup", dealerGroup.ToString());

               logger.Info($"CompetitorInformation: {dealerGroup}");
               var logMessage = LoggingService.InitLogMessage();
               try
               {
                  RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                  List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

                  if (retailers.Count > 0)
                  {
                     try
                     {
                        await GetForThisDealerGroup(logger, dealerGroup);
                     }
                     catch (Exception ex)
                     {
                        logger.Error(ex);
                        await EmailerService.SendMailOnError(dealerGroup, "GetCompetitorSituationService - Error", ex);
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                  }
                  else
                  {
                     logger.Info($"CompetitorInformation: {dealerGroup}: No retailers");
                  }
               }
               catch (Exception ex)
               {
                  await EmailerService.LogException(ex, logger, "CompetitorInformation");
                  LoggingService.AddErrorLogMessage(logMessage, ex.Message);
               }
               finally
               {
                  await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "CompetitorInformation");
               }
               // }
            }

            logger.Info("Completed CompetitorInformation");
            logger.Info("----------------------------------------------------------");
            //}
         }
         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }

      private async Task GetForThisDealerGroup(ILog logger, Model.DealerGroupName dealerGroup)
      {
         string connString = ConfigService.GetConnectionString(dealerGroup);
         DateTime runDate = DateTime.Now;

         //get the ads with snapshots
         var retailerSitesDataAccess = new RetailerSitesDataAccess(connString);
         var sitesDataAccess = new SiteDataAccess(connString);
         List<RetailerSite> retailerSitesThisDG = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
         IEnumerable<SiteVM> sites = await sitesDataAccess.GetAllSites(dealerGroup);

         var siteIds = retailerSitesThisDG.Select(x => x.Site_Id).Distinct().ToList();
         var retailerSiteIds = retailerSitesThisDG.Select(x => x.Id).ToList();

         //load up all the snapshots for today
         VehicleAdvertSnapshotsDataAccess vehicleAdvertSnapshotsDataAccess = new VehicleAdvertSnapshotsDataAccess(connString);
         VehicleAdvertsService vehicleAdvertsService = new VehicleAdvertsService(connString);
         GetVehicleAdvertsWithRatingsParams parms = new GetVehicleAdvertsWithRatingsParams()
         {
            Reg = null,
            Vin = null,
            RetailerSiteIds = string.Join(',', retailerSiteIds),
            EffectiveDate = runDate.Date,
            UserEligibleSites = string.Join(',', siteIds),
            IncludeNewVehicles = true,
            IncludeUnPublishedAdverts = true
         };
         var lifecycles = AutoPriceHelperService.GetAllLifecycleStatusesNoSoldOrWastebin();
         List<VehicleAdvertWithRating> adverts = (await vehicleAdvertsService.FetchAndFilterVehicleAdvertsFromMainDbTables(parms, dealerGroup, siteIds, retailerSiteIds, lifecycles)).ToList();

         //New! load up the competitor links
         List<int> advertIds = adverts.Select(x => x.AdId).ToList();
         List<AdvertIdAndCompetitorLink> competitorLinksPerAd = await vehicleAdvertsService.GetAdvertsAndCompetitorLinks(dealerGroup, advertIds);
         Dictionary<int, string> competitorLinks = competitorLinksPerAd.ToDictionary(x => x.AdvertId, x => x.CompetitorLink);


         var atCompetitorClient = new AutoTraderCompetitorClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         var atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);

         //get a token
         var _bearerToken = (await atTokenClient.GetToken());

         Stopwatch stopwatch = new Stopwatch();
         stopwatch.Start();

         var chunks = adverts.Chunk(200);
         var chunkIndex = 0;
         Stopwatch sw = new Stopwatch();
         logger.Info($"CompetitorInformation: Total adverts is {adverts.Count()}");
         foreach (var chunk in chunks)
         {
            List<VehicleAdvertCompetitorSummary> results = new List<VehicleAdvertCompetitorSummary>();
            chunkIndex++;

            //------------------------------------------------------------------
            //Step 1: Get the first page results for every advert
            //------------------------------------------------------------------
            var firstPageTasks = new List<Func<Task<VehicleListing>>>();

            List<VehicleListing> firstPageResults = new();
            sw.Start();

            foreach (var advert in chunk)
            {
               _bearerToken = await atTokenClient.CheckExpiryAndRegenerate(_bearerToken);

               //we will first get all their first page results
               int pageNumber = 1;
               var newTask = GenerateGetAdsWithPageNumberTask1(logger, retailerSitesThisDG, sites, competitorLinks, atCompetitorClient, _bearerToken, advert, pageNumber);
               firstPageTasks.Add(newTask);
            }
            // }

            var thisChunkResults = await Task.WhenAll(firstPageTasks.Select(t => t()));
            logger.Info($"Chunk {chunkIndex}: of {chunks.Count()} elapsed is {sw.ElapsedMilliseconds}ms");
            firstPageResults.AddRange(thisChunkResults);
            firstPageTasks.Clear();
            await Tracer.Instance.ForceFlushAsync();

            //logger.Info($"Step 1 done, fetched first page tasks elapsed time is {stopwatch.ElapsedMilliseconds}ms");

            //------------------------------------------------------------------
            // Step 2: Work out the subsequent fetches we need for any ads with > 20 competitors
            //------------------------------------------------------------------
            var nextPageRequests = new List<(Func<Task<VehicleListing>> tasks, int advertId)>();

            //now can work out the subsequent page results required and build the requests list
            int resultIndex = 0;
            foreach (var firstPageResult in firstPageResults)
            {
               if (firstPageResult != null && firstPageResult.totalResults > 20) // Check if there are more pages
               {
                  var advert = firstPageResult.Advert;// thisChunkAdverts[resultIndex];
                  int totalPages = (int)Math.Ceiling((double)firstPageResult.totalResults / 20);
                  if (totalPages > 10)
                  {
                     //can never get more than 10 pages
                     totalPages = 10;
                  }

                  // Queue up additional pages for this advert
                  for (int nextPageNumber = 2; nextPageNumber <= totalPages; nextPageNumber++)
                  {
                     Func<Task<VehicleListing>> subsequentTask = GenerateGetAdsWithPageNumberTask1(logger, retailerSitesThisDG, sites, competitorLinks, atCompetitorClient, _bearerToken, advert, nextPageNumber);
                     nextPageRequests.Add((subsequentTask, advert.AdId));
                  }
               }
               resultIndex++;
            }

            var firstPageResultsDict = firstPageResults.Where(x => x != null && x.Advert != null).ToDictionary(x => x.Advert.AdId);
            //logger.Info($"Chunk {chunkIndex}: Step 2 done, worked out subsequent page tasks elapsed time is {stopwatch.ElapsedMilliseconds}ms");


            //------------------------------------------------------------------
            // Step 3: Go and get the subsequent pages
            //------------------------------------------------------------------
            var subsequentPageResults = await Task.WhenAll(nextPageRequests.Select(t => t.tasks()));
            foreach (var subsequentPageResult in subsequentPageResults)
            {
               if (subsequentPageResult != null)
               {
                  if (firstPageResultsDict.TryGetValue(subsequentPageResult.Advert.AdId, out var firstPageResult))
                  {
                     //add in the subsequent page results to the list of results for this first page result
                     firstPageResult.results.AddRange(subsequentPageResult.results);
                  }
               }
            }

            //logger.Info($"Chunk {chunkIndex}: Step 3  done in {sw.ElapsedMilliseconds}ms");
            subsequentPageResults = null;

            nextPageRequests.Clear();
            //logger.Info($"Step 3 done, fetched subsequent page tasks elapsed time is {stopwatch.ElapsedMilliseconds}ms");


            //------------------------------------------------------------------
            // Step 4: Generate the resulting advertCompetitorSummaries and add to results
            //------------------------------------------------------------------

            //now each firstPage should have all subsequent pages populated
            foreach (var firstPageResult in firstPageResults.Where(x => x != null && x.Advert != null))
            {
               //simply build the final result for each advert
               var advert = firstPageResult.Advert;
               var retailerSite = retailerSitesThisDG.First(x => x.Id == advert.RetailerSiteId);
               var site = sites.First(x => x.SiteId == retailerSite.Site_Id);
               VehicleAdvertCompetitorSummary result = new VehicleAdvertCompetitorSummary(firstPageResult, advert, site, 1000,retailerSite.Postcode);
               results.Add(result);
            }
            //logger.Info($"Chunk {chunkIndex}: Step 4 done, summaries generated elapsed time is {stopwatch.ElapsedMilliseconds}ms");


            //update the snapshots and save
            var dataToSave = results.ToList();
            await vehicleAdvertSnapshotsDataAccess.SaveCompetitorInfo(dataToSave);

            firstPageResults = null;
            firstPageResultsDict = null;
            results = null;
            await Tracer.Instance.ForceFlushAsync();

         }//end of chunk





         stopwatch.Stop();


         logger.Info($"Final step done, ads updated and saved elapsed time is {stopwatch.ElapsedMilliseconds}ms");
      }




      private Func<Task<VehicleListing>> GenerateGetAdsWithPageNumberTask1(
    ILog logger,
    List<RetailerSite> retailerSitesThisDG,
    IEnumerable<SiteVM> sites,
    Dictionary<int, string> competitorLinks,
    AutoTraderCompetitorClient atCompetitorClient,
    TokenResponse _bearerToken,
    VehicleAdvertWithRating advert,
    int pageNumber)
      {
         return async () =>
         {
            var retailerSite = retailerSitesThisDG.First(x => x.Id == advert.RetailerSiteId);
            var site = sites.First(x => x.SiteId == retailerSite.Site_Id);

            string competitorLink = competitorLinks[advert.AdId];
            try
            {

               CompetitorSearchParams competitorParams = new CompetitorSearchParams(advert, retailerSite, _bearerToken, 1000, competitorLink);

               return await atCompetitorClient.GetCompetitorWithPageNumberNew(competitorParams, pageNumber, logger, advert);
            }
            catch (Exception ex)
            {
               logger.Error($"GetCompetitors: {ex.Message} on advert {advert.AdId}");
               return new VehicleListing()
               {
                  totalResults = 0,
                  results = new List<AutoTraderVehicleListing>(),
                  errorMessage = $"Error {ex.Message}"
               };
            }
         };
      }





   }
}
