﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>$(DefineConstants)TRACE;_PUBLISH_CHROMEDRIVER</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>$(DefineConstants)TRACE;_PUBLISH_CHROMEDRIVER</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="appsettings.json" />
    <None Remove="test.runsettings" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="test.runsettings">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
    <PackageReference Include="CPHI.Monitor.PostLogs" Version="1.0.1" />
    <PackageReference Include="DotNetSeleniumExtras.WaitHelpers" Version="3.11.0" />
    <PackageReference Include="log4net" Version="2.0.17" />
    <PackageReference Include="Microsoft.Exchange.WebServices.NETStandard" Version="1.1.3" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.10.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.4.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.4.3" />
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Selenium.WebDriver" Version="4.22.0" />
    <PackageReference Include="Selenium.WebDriver.ChromeDriver" Version="137.0.7151.5500" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
  </ItemGroup>
</Project>