﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Threading.Tasks;
using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.Model;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using Microsoft.Extensions.FileSystemGlobbing.Internal.PathSegments;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.WebApp.Service;
using System.Configuration;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.Model.Services;

namespace CPHI.Spark.WebApp
{
   public interface IAutoPriceCache
   {
      Task<IEnumerable<VehicleAdvertWithRating>> GetAutoPriceAdverts(DateTime chosenDate, DealerGroupName dealerGroup, List<string> lifecycleStatuses, bool UseTestStrategy = false);
      Task RefreshAdvertsCache(DealerGroupName dealerGroup);
      void RefreshCachesFireAndForget(DealerGroupName dealerGroup);
   }


   public class AutoPriceCache : IAutoPriceCache
   {
      private readonly IUserService userService;
      private readonly IConfiguration configuration;
      private readonly string _connectionString;
      private readonly DealerGroupName dealerGroup;

      public AutoPriceCache(IUserService userService, IConfiguration configuration)
      {
         this.userService = userService;
         this.configuration = configuration;
         try
         {

            dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            _connectionString = configuration.GetConnectionString(dgName);
         }
         catch (Exception ex)
         {
            _connectionString = "unknown";
         }
      }



      public async Task<IEnumerable<VehicleAdvertWithRating>> GetAutoPriceAdverts(DateTime chosenDate, Model.DealerGroupName dealerGroup, List<string> lifecycleStatuses, bool UseTestStrategy = false)
      {
         string cacheName = BuildCacheName(chosenDate, dealerGroup);

         if (!MemoryCache.Default.Contains(cacheName))
         {
            await FillCache(chosenDate, dealerGroup);
         }

         var cacheItems = (IEnumerable<VehicleAdvertWithRating>)MemoryCache.Default.GetCacheItem(cacheName).Value;
         var toReturn = cacheItems.ToList().ConvertAll(x => new VehicleAdvertWithRating(x, UseTestStrategy));

         if (lifecycleStatuses != null)
         {
            return toReturn.Where(x => lifecycleStatuses.Contains(x.LifecycleStatus)).ToList();
         }
         else
         {
            return toReturn;
         }
      }

      public void RefreshCachesFireAndForget(DealerGroupName dealerGroup)
      {
         RefreshAdvertsCache(dealerGroup);  //deliberately not awaiting as 'FireAndForget'
      }

      public async Task RefreshAdvertsCache(DealerGroupName dealerGroup)
      {
         string cacheName = BuildCacheName(DateTime.Now, dealerGroup);
         MemoryCache.Default.Remove(cacheName);

         await FillCache(DateTime.Now.Date, dealerGroup);
      }




      //--------- Private Methods


      private async Task FillCache(DateTime chosenDate, DealerGroupName dealerGroup)
      {

         var vehicleAdvertsService = new VehicleAdvertsService(_connectionString);
         //List<string> lifecycles = new List<string>() { "DUE_IN", "FORECOURT", "IN STOCK NOT ON PORTAL", "SALE_IN_PROGRESS", "SOLD", "WASTEBIN" };
         List<string> lifecycles = AutoPriceHelperService.GetAllLifecycleStatuses();
         var adverts = await vehicleAdvertsService.FetchVehicleAdvertsWithRatingsFromMainDbTables(chosenDate, dealerGroup, lifecycles, null);
         string cacheName = BuildCacheName(chosenDate, dealerGroup);

         var orderedAdverts = adverts.OrderBy(x => x.DaysListed);
         lock (MemoryCache.Default)
         {
            DateTime tonight = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);
            MemoryCache.Default.Add(new CacheItem(cacheName, adverts), new CacheItemPolicy() { AbsoluteExpiration = tonight });
         }

      }

      private static string BuildCacheName(DateTime chosenDate, DealerGroupName dealerGroup)
      {
         string dateName = chosenDate.ToString("yyyyMMdd");
         string cacheName = $"{dealerGroup}|{dateName}|AutoPriceRows";
         return cacheName;
      }














   }




}

