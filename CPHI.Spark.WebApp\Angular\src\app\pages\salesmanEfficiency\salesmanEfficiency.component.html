<nav class="navbar">

  <nav class="generic" >
    <h4 id="pageTitle">
      <div >
        {{ constants.translatedText.Dashboard_SalesmanEfficiency_Title }}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <ng-container *ngIf="!!service.chosenMonthStartDateLabel">

      <vehicleTypePickerSpain
        *ngIf="constants.environment.customer == 'RRGSpain'"
        [vehicleTypeTypesFromParent]="service.chosenVehicleTypeTypes"
        (updateVehicleTypes)="onUpdateVehicleTypes($event)"
      >
      </vehicleTypePickerSpain>

      <div class="buttonGroup topDropdownButtons">

        <!-- VehicleType selector -->
        <vehicleTypePicker *ngIf="constants.environment.customer != 'RRGSpain'"
        [vehicleTypeTypesFromParent]="service.chosenVehicleTypeTypes" 
          [buttonClass]="'buttonGroupLeft'" (updateVehicleTypes)="onUpdateVehicleTypes($event)"></vehicleTypePicker>

        <!-- OrderType selector -->
        <orderTypePicker 
          [orderTypeTypesFromParent]="service.chosenOrderTypeTypes" [buttonClass]="'buttonGroupCenter'"
          (updateOrderTypes)="onUpdateOrderTypes($event)"></orderTypePicker>

        <!-- Franchise selector -->
        <franchisePicker [franchisesFromParent]="service.chosenFranchises" 
          [buttonClass]="'buttonGroupRight'" (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>

      </div>

     

      <!-- FOR SELECTING MONTH -->
      <div class="buttonGroup">

         <!-- previousMonth -->
         <button class="btn btn-primary" (click)="changeMonth(-1)"><i
          class="fas fa-caret-left"></i></button>


        <!-- dropdownMonth -->
        <div ngbDropdown class="d-inline-block" [autoClose]="true">
          <button class="btn btn-primary centreButton"
            ngbDropdownToggle>{{service.chosenMonthStartDateLabel}}</button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

            <!-- the ngFor buttons -->
            <button *ngFor="let month of service.months" (click)="selectMonth(month)"
              ngbDropdownItem>{{month.name}}</button>

          </div>
        </div>

         <!-- nextMonth -->
         <button class="btn btn-primary" (click)="changeMonth(1)"><i
          class="fas fa-caret-right"></i></button>

      </div>

    </ng-container>


  </nav>


</nav>

<!-- Main Page -->
<div class="content-new">

    <div class="content-inner-new" *ngIf="service.siteTableRows">
      <button class="btn btn-primary" id="backToSites" (click)="backToSites()"
        *ngIf="!!service.chosenRow"><i class="fas fa-undo"></i></button>
      

      <!-- Site tables -->
      <ng-container *ngIf="!service.chosenRow">
        <salesmanEfficiencyTable [tableType]="'sites'"></salesmanEfficiencyTable>
        <div class="tableSpacer"></div>
        <salesmanEfficiencyTable [tableType]="'regions'"></salesmanEfficiencyTable>
      </ng-container>

      <!-- Person table -->
      <ng-container *ngIf="!!service.chosenRow && !!service.peopleTableRows">
        <salesmanEfficiencyTable  [tableType]="'people' "></salesmanEfficiencyTable>
      </ng-container>

    </div>

</div>