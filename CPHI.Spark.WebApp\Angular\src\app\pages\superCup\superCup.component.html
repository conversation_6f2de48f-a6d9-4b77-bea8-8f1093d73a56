<nav class="navbar">

  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        {{ service.constants.translatedText.SuperCup_Title }}
        <sourceDataUpdate [chosenDate]="service.constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <div class="buttonGroup topDropdownButtons">

    </div>



  </nav>

  <nav class="pageSpecific">


    <button (click)="refresh()" class="btn btn-primary">
      {{service.constants.translatedText.Common_Refresh}}
    </button>

  </nav>
</nav>



<!-- Main Page -->
<div *ngIf="false" id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new superCupBackground">

    <div class="content-inner-new" *ngIf="service.matches">



      <!-- ******************************************************** -->
      <!-- The various matches -->
      <!-- ******************************************************** -->
      <div id="matchesContainer">

        <div id="confettiHolder">
          <confetti></confetti>
        </div>


        <div id="header" class="boxShadow">

          <!-- Logo -->
          <!-- <div id="magic" class="magicLogoHolder">
            <img [src]="service.constants.provideMagicLogo()">
          </div> -->
          <div class="headline">

            <div class="words">August Used Car Super-Cup</div>

          </div>
          <!-- Logo -->
          <div id="duster" class="magicLogoHolder">
            <img src="./assets/imgs/events/superCup/rrgSupercupCar.png">
          </div>
          <div class="tagLine">
            <div class="words">August Used Department vs Target</div>
          </div>
        </div>


        <div id="matches">

          <div class="match" *ngFor="let match of service.matches" (click)="onSelectMatch(match)">
            <div class="homeTeam team">
              <div class="teamInner">
                <div class="managerMoney boxShadow pulsing" *ngIf="match.HomeTeam.Score>match.AwayTeam.Score">£ Prize</div>
                <div class="name"> {{match.HomeTeam.Label}} </div>
                <div class="score">{{match.HomeTeam.Score | cph:'percent':0}}</div>
              </div>
            </div>

            <div class="awayTeam team">

              <div class="teamInner">
                <div class="managerMoney pulsing boxShadow zoomIn" *ngIf="match.HomeTeam.Score < match.AwayTeam.Score">£
                  Prize</div>
                <div class="name"> {{match.AwayTeam.Label}} </div>
                <div class="score">{{match.AwayTeam.Score | cph:'percent':0}}</div>
              </div>
            </div>
          </div>

          <!-- Total Row at bottom -->
          <!-- <div class="match" id="totalRow">
            <div class="homeTeam team">
              <div class="teamInner">
                <div class="score">{{service.totalHomeTeamScore | cph:'percent':0}}</div>
              </div>
            </div>

            <div class="awayTeam team">

              <div class="teamInner">
                <div class="score">{{service.totalAwayTeamScore | cph:'percent':0}}</div>
              </div>
            </div>
          </div> -->



        </div>
      </div>



      <!-- ******************************************************** -->
      <!-- the players for the chosen match -->
      <!-- ******************************************************** -->
      <div id="matchContainer" *ngIf="service.chosenMatch">

        <div id="confettiHolder">
          <confetti></confetti>
        </div>

        <button id="backButton" class="btn btn-primary" *ngIf="service.chosenMatch" (click)="onUnChooseMatchClick()"><i
            class="fas fa-undo"></i></button>

        <div class="match boxShadow">
          <div class="homeTeam team">
            <div class="teamInner">
              <div class="managerMoney pulsing boxShadow"
                *ngIf="service.chosenMatch.HomeTeam.Score>service.chosenMatch.AwayTeam.Score">£
                500</div>
              <div class="name"> {{service.chosenMatch.HomeTeam.Label}} </div>
              <div class="score">{{service.chosenMatch.HomeTeam.Score}}</div>
            </div>
          </div>

          <div class="awayTeam team">

            <div class="teamInner">
              <div class="managerMoney pulsing boxShadow"
                *ngIf="service.chosenMatch.HomeTeam.Score < service.chosenMatch.AwayTeam.Score">£
                500</div>
              <div class="name"> {{service.chosenMatch.AwayTeam.Label}} </div>
              <div class="score">{{service.chosenMatch.AwayTeam.Score}}</div>
            </div>
          </div>
        </div>

        <div id="peopleContainersArea">


          <div id="homeContainer" class="teamContainer">
            <div class="teamContainerInner">

              <div class="chipHolder" *ngFor="let person of service.chosenMatchHomeTeamPeople; let i = index"
                [ngClass]="{'ownRow':ownRow(i)}">
                <!-- The actual chip for the person -->
                <div class="personChip" [ngClass]="{'fullSalesman':person.IsActuallyASalesman}">
                  <div class="personChipInner">

                    <profilePicImage [personId]="person.Person.Id" [size]="profilePicSize"></profilePicImage>
                    <div class="name">
                      <div class="actualName">{{person.Person.Name}}</div>
                      <div [ngClass]="{'topUp':service.chosenMatch.HomeTeam.TopUp}" *ngIf="person.IsActuallyASalesman"
                        class="payoutCalc">
                        <!-- No topup -->
                        <div class="payoutRow" *ngIf="!service.chosenMatch.HomeTeam.TopUp">
                          <div class="calcBit">
                            £25 x {{person.Score}} =
                          </div>
                          <div class="normalPayout">{{25 * person.Score|cph:'currency':0}}</div>
                        </div>
                        <!-- Topup -->
                        <div class="payoutRow" *ngIf="service.chosenMatch.HomeTeam.TopUp">
                          <div class="calcBit">
                            £50 x {{person.Score}} =
                          </div>
                          <div class="superPayout">{{50 * person.Score|cph:'currency':0}}</div>
                        </div>
                      </div>
                    </div>
                    <div class="score">
                      <span *ngIf="person.Deals.length>0">
                        <dealListPopover [deals]="person.Deals" [score]="person.Score"></dealListPopover>
                      </span>
                      <span *ngIf="person.Deals.length===0">
                        {{person.Score}}
                      </span>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>



          <div id="awayContainer" class="teamContainer">
            <div class="teamContainerInner">

              <div class="chipHolder" *ngFor="let person of service.chosenMatchAwayTeamPeople; let i = index"
                [ngClass]="{'ownRow':ownRow(i)}">
                <!-- The actual chip for the person -->
                <div class="personChip" [ngClass]="{'fullSalesman':person.IsActuallyASalesman}">
                  <div class="personChipInner">

                    <profilePicImage [personId]="person.Person.Id" [size]="profilePicSize"></profilePicImage>
                    <div class="name">
                      <div class="actualName">{{person.Person.Name}}</div>
                      <div [ngClass]="{'topUp':service.chosenMatch.AwayTeam.TopUp}" *ngIf="person.IsActuallyASalesman"
                        class="payoutCalc">
                        <!-- No topup -->
                        <div class="payoutRow" *ngIf="!service.chosenMatch.AwayTeam.TopUp">
                          <div class="calcBit">
                            £25 x {{person.Score}} =
                          </div>
                          <div class="normalPayout">{{25 * person.Score|cph:'currency':0}}</div>
                        </div>
                        <!-- Topup -->
                        <div class="payoutRow" *ngIf="service.chosenMatch.AwayTeam.TopUp">
                          <div class="calcBit">
                            £50 x {{person.Score}} =
                          </div>
                          <div class="superPayout">{{50 * person.Score|cph:'currency':0}}</div>
                        </div>
                      </div>
                    </div>
                    <div class="score">
                      <span *ngIf="person.Deals.length>0">
                        <dealListPopover [deals]="person.Deals" [score]="person.Score"></dealListPopover>
                      </span>
                      <span *ngIf="person.Deals.length===0">
                        {{person.Score}}
                      </span>

                    </div>
                  </div>
                </div>
              </div>





            </div>
          </div>

        </div>

      </div>


    </div>
  </div>
</div>

<!-- Star Wars theme -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new star-wars-background">

    <div *ngIf="service.matches" class="content-inner-new">
      <div id="matchesContainer">

        <div class="header-container">
          <span class="event-title">SALES WARS</span>
          <span class="event-sub-title">September Used Department vs Target</span>
        </div>

        <div class="score-board">
          <table>
            <thead>
              <tr>
                <th>GALACTIC EMPIRE</th>
                <th colspan="2">SCORE</th>
                <th>REBEL ALLIANCE</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let match of service.matches">
                <td>
                  <div *ngIf="match.HomeTeam.Score > match.AwayTeam.Score" class="winner-symbol home"></div>
                  {{ match.HomeTeam.Label }}
                </td>
                <td [ngClass]="{ 'winner': match.HomeTeam.Score > match.AwayTeam.Score }">
                  {{ match.HomeTeam.Score | cph:'percent':0 }}
                  <span *ngIf="match.HomeTeam.Score - match.AwayTeam.Score <= 0.03 && match.HomeTeam.Score - match.AwayTeam.Score > 0" class="close-match home">
                    A narrow victory!
                  </span>
                </td>
                <td [ngClass]="{ 'winner': match.HomeTeam.Score < match.AwayTeam.Score }">
                  {{ match.AwayTeam.Score | cph:'percent':0 }}
                </td>
                <td>
                  <div *ngIf="match.HomeTeam.Score < match.AwayTeam.Score" class="winner-symbol away"></div>
                  {{ match.AwayTeam.Label }}
                  <span *ngIf="match.AwayTeam.Score - match.HomeTeam.Score <= 0.03 && match.AwayTeam.Score - match.HomeTeam.Score > 0" class="close-match away">
                    A narrow victory!
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="overall-result">
          VICTORY FOR THE {{ service.totalHomeTeamScore > service.totalAwayTeamScore ? 'GALACTIC EMPIRE' : 'REBEL ALLIANCE' }}
        </div>
    </div>
  </div>
</div>

<div *ngIf="service.showLoadingScreen" class="loading-screen">
  <div class="stars"></div>
  
  <div class="fighter-jet-container">
    <div class="fighter-jet">
      <div class="fighter-jet-wing left"></div>
      <div class="fighter-jet-wing-to-body left"></div>
      <div class="fighter-jet-body">
        <div class="inset-circle">
          <div class="light left"></div>
          <div class="light right"></div>
        </div>
      </div>
      <div class="fighter-jet-wing-to-body right"></div>
      <div class="fighter-jet-wing right"></div> 
    </div>
  </div>

  <div class="loading-text">{{ loadingText }}...</div>
</div>