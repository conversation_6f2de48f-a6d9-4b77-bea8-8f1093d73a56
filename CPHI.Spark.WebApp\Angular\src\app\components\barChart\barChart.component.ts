import {
   Component,
   ElementRef,
   EventEmitter,
   Input,
   OnChanges,
   OnDestroy,
   OnInit,
   SimpleChanges,
   ViewChild
} from '@angular/core';
import { Chart, ChartConfiguration } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import Annotation from 'chartjs-plugin-annotation';
import { CphPipe, FormatType } from 'src/app/cph.pipe';
import { BarChartParams } from 'src/app/model/BarChartParams';
import { ChartDataSet } from '../aftersalesTiles/runChaseTile.component';
import { LeavingVehicleBarChartSet } from 'src/app/model/LeavingVehicleBarChartSet';
import { VNTileParams } from 'src/app/model/VNTileParams';
import { VNTileTableRow } from 'src/app/model/VNTileTableRow';
import { BIChartTileDataType, TileType } from '../biChartTile/biChartTile.component';
import { AutotraderService } from "src/app/services/autotrader.service";
import { DashboardMeasure } from 'src/app/model/DashboardMeasure';
import { SelectionsService } from 'src/app/services/selections.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'barChart',
  templateUrl: './barChart.component.html',
  styleUrls: ['./barChart.component.scss']
})
export class BarChartComponent implements OnInit, OnDestroy, OnChanges {
  private destroy$ = new Subject<void>();
  @Input() params: BarChartParams;
  @Input() yMax?: number;
  @Input() newDataEmitter: EventEmitter<LeavingVehicleBarChartSet>;
  @Input() dataKey: string;
  @Input() doesFilter: boolean;
  @Input() public pageParams: VNTileParams;
  @Input() public dataType: BIChartTileDataType;
  @Input() public fieldName: string;
  @Input() public tileType: TileType;
  @Input() public dashboardParams: any;
  @Input() public showPercentageOfTotal: boolean;
  @ViewChild('barChart', { static: true }) chartCanvas: ElementRef;
  chart: Chart;
  tableRows: VNTileTableRow[];
  maxValue: number;

  constructor(
    public cphPipe: CphPipe,
    public selectionsService: SelectionsService
  ) { }

  ngOnInit(): void {
    this.buildChart();
    if (this.doesFilter) {
      this.buildTableRows();
    }

    this.newDataEmitter
      .pipe(takeUntil(this.destroy$)) // Ensures it unsubscribes when component is destroyed
      .subscribe(res => {
        let newData = res[this.dataKey];
        this.params = newData;
        this.redrawChart();
      });



  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.chart) {
      this.chart.destroy();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
     if (changes.yMax && this.chart != null) {
        console.log("CHANGES ", changes);
        this.chart.options.scales.y.max = changes.yMax.currentValue;
        this.chart.update();
     }
  }

  redrawChart() {
    if (this.chart) {
      this.chart.destroy();
    }

    this.buildChart();
  }

  buildChart() {
    //console.log(this.params)
    const config: ChartConfiguration = {
      type: 'bar',
      data: {
        labels: this.params.Labels,
        datasets: [{
          data: this.params.Values,
          backgroundColor: this.params.Colours,
          categoryPercentage: 0.95,
        }]
      },
      options: {
        onClick: (c, i) => {
          if (!this.doesFilter) { return; }
          if (this.dashboardParams) {
            this.dashboardParams.parentMethods.filterTable(
              this.chart.data.labels[i[0].index],
              this.dataKey
            );
          }
          if (this.pageParams) {
            this.pageParams.parentMethods.highlightRow(this.tableRows[i[0].index], this.fieldName);
            this.pageParams.highlightChoiceHasBeenMade.emit();
          }
        },
        onHover: (event, chartElement) => {
          const nativeEvent = event.native!;
          const target = nativeEvent.target as HTMLElement;
          if (chartElement.length) {
            target.style.cursor = 'pointer';
          } else {
            target.style.cursor = 'default';
          }
        },
        responsive: true,
        maintainAspectRatio: false,
        layout: {
          padding: {
            top: this.showPercentageOfTotal ? 45 : 15
          }
        },
        hover: {
          mode: this.doesFilter ? 'nearest' : null
        },
        plugins: {
          tooltip: {
            enabled: false,
          },
          title: {
            display: false,
            // text: this.params.Title,
            // padding: {
            //   top: 20,
            //   bottom: 20
            // }
          },
          legend: {
            display: false
          },
          datalabels: {
            anchor: (context) => {
              if (context.dataset.data[context.dataIndex] >= 0) {
                return 'end';
              } else {
                return 'start';
              }
            },
            align: 'center',
            textAlign: 'center',
            formatter: (value, context) => {
              let label: string;
              if (context.dataset.data[context.dataIndex] >= 0) {
                label = this.cphPipe.transform(value, this.params.DataFormat, 0) + '\n';
              } else {
                label = '\n' + this.cphPipe.transform(value, this.params.DataFormat, 0);
              }

              if (this.showPercentageOfTotal) {
                const total: number = this.params.Values.reduce((sum, num) => sum + num, 0);
                const percent: number = (value / total);
                label += `${this.cphPipe.transform(percent, 'percent', 0)}\n\n`;
              }
              return label;
            }
          }
        },
        scales: {
          y: {
            ticks: {
              display: false
            },
            grid: {
              display: true
            },
            min: this.params.yMin,
            max: this.params.yMax
          },
          x: {
            grid: {
              display: false
            },
            ticks: {
              maxRotation: 0,
              autoSkip: false
            }
          }
        }
      },
      plugins: []
    };


    if (this.params.AverageLineValue) {
      config.options.plugins.annotation = {
        annotations: {
          line1: {
            type: 'line',
            yMin: this.params.AverageLineValue,
            yMax: this.params.AverageLineValue,
            xMin: 0,
            xMax: this.params.Values.length - 1,
            borderColor: 'black',
            borderWidth: 2,
            borderDash: [2]
          }
        }
      };
    }

    if (this.chart) {
      this.chart.destroy();
    }

    Chart.defaults.font.size = this.selectionsService.getChartFontSize();
    this.chart = new Chart(this.chartCanvas.nativeElement, config);

  }

  buildTableRows() {
    if (this.pageParams) {
      this.tableRows = this.pageParams.parentMethods.buildRows(this.fieldName, this.dataType, this.tileType);
      this.maxValue = this.tableRows.map(x => x.FilteredTotal).sort((a, b) => b - a)[0];
      const sortOrder = AutotraderService.getSortOrderForDaysBand();
      this.applyCustomSort(this.tableRows, sortOrder);
    }
  }

  private applyCustomSort(tableRows: VNTileTableRow[], sortOrder: string[]) {
    var ordering = {}; // map for efficient lookup of sortIndex
    for (var i = 0; i < sortOrder.length; i++) {
      ordering[sortOrder[i]] = i;
    }
    tableRows = tableRows.sort(function (a, b) {
      return (ordering[a.Label] - ordering[b.Label]) || a.Label.localeCompare(b.Label);
    });
  }

  isHighlightsChosen() {
    if (!this.doesFilter || !this.pageParams) { return; }
    let choice = this.userChoice();
    if (!choice) {
      console.error('failed finding ', this.fieldName)
    }
    return this.userChoice().ChosenValues.length > 0
  }

  clearHighlights() {
    let choice = this.pageParams.highlightChoices.find(x => x.FieldName === this.fieldName);
    choice.ChosenValues = [];
    this.pageParams.highlightChoiceHasBeenMade.emit();
  }

  userChoice(): DashboardMeasure {
    let choice = this.pageParams?.highlightChoices?.find(x => x.FieldName === this.fieldName);
    if (!choice) {
      console.error('error finding ', this.fieldName)
    }
    return choice;
  }
}
