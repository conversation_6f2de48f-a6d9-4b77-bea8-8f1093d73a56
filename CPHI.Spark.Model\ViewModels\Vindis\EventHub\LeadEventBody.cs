﻿//namespace MadDevs.Abstractions.EventHub
//{
//    public class LeadEventBody
//    {
//        public string Id { get; set; }

//        public DateTime Created { get; set; }
        
//        public string? DealerName { get; set; }
        
//        public string? Status { get; set; }

//        public string? LeadType { get; set; }
        
//        public string? Provider { get; set; }

//        public DateTime? ProviderCreated { get; set; }

//        public string? ProviderLeadId { get; set; }

//        public string? ProviderLeadTypeCode { get; set; }

//        public string? ProviderLeadTypeDescription { get; set; }

//        public string? MethodOfContact { get; set; }

//        public string? SourceOfEnquiry { get; set; }

//        public DateTime? Allocated { get; set; }

//        public string? AllocatedBy { get; set; }

//        public string? AllocatedByName { get; set; }

//        public string? AllocatedTo { get; set; }

//        public string? AllocatedName { get; set; }

//        public DateTime? Assigned { get; set; }

//        public string? AssignedTo { get; set; }

//        public DateTime? LockedUntil { get; set; }
        
//        public DateTime? Closed { get; set; }

//        public DateTime? Junked { get; set; }

//        public string? CustomerId { get; set; }

//        public string? CustomerName { get; set; }

//        public string? CustomerPostcode { get; set; }

//        public string? CustomerEmailAddress { get; set; }

//        public string? CustomerHomePhone { get; set; }

//        public string? CustomerMobilePhone { get; set; }

//        public string? CustomerWorkPhone { get; set; }

//        public string? CustomerVehicle { get; set; }

//        public string? CustomerVehicleVrm { get; set; }

//        public string? CustomerVehicleCondition { get; set; }

//        public decimal? CustomerVehicleValuation { get; set; }
        
//        public string? EnquiryVehicle { get; set; }

//        public string? EnquiryVehicleMake { get; set; }

//        public string? EnquiryVehicleRange { get; set; }

//        public string? EnquiryVehicleModel { get; set; }

//        public string? EnquiryVehicleDerivative { get; set; }

//        public string? EnquiryVehicleTypeId { get; set; }

//        public string? EnquiryVehicleVrm { get; set; }

//        public string? EnquiryVehicleCode { get; set; }

//        public string? EnquiryVehicleCodeType { get; set; }

//        public decimal? EnquiryVehiclePrice { get; set; }

//        public decimal? EnquiryVehiclePriceDiscount { get; set; }

//        public string? EnquiryVehicleFinanceType { get; set; }
        
//        public DateTime? ReservedUntil { get; set; }

//        public string? DepositReference { get; set; }

//        public decimal? DepositPaid { get; set; }

//        public bool? AllowPhone { get; set; }

//        public bool? AllowPost { get; set; }

//        public bool? AllowEmail { get; set; }

//        public bool? AllowSms { get; set; }

//        public string? AssignedToName { get; set; }

//        public DateTime? FirstResponse { get; set; }

//        public string? FirstResponseName { get; set; }

//        public string? FirstResponseMethod { get; set; }

//        public string? RespondedBy { get; set; }

//        public string? RespondedByName { get; set; }

//        public string? RejectedBy { get; set; }

//        public string? RejectedByName { get; set; }

//        public string? RejectedReason { get; set; }

//        public DateTime? Rejected { get; set; }
        
//        public DateTime? ThirdPartyAllocated { get; set; }

//        public DateTime? EmailResponseOpened { get; set; }
        
//        public DateTime? UnattendedLeadEmailSent { get; set; }

//        public DateTime? LeadAppointment { get; set; }

//        public DateTime? LeadAppointed { get; set; }

//        public string? LeadAppointedBy { get; set; }

//        public bool IsOem { get; set; }
        
//        public long? ResponseTimeActualSeconds { get; set; }
        
//        public long? ResponseTimeWorkingSeconds { get; set; }
        
//        public DateTime? EnquiryCreated { get; set; }

//        public string? EmailMessageId { get; set; }

//        public string? ThirdPartyCustomerId { get; set; }
        
//        public string? CampaignCode1 { get; set; }

//        public string? CampaignCode2 { get; set; }

//        public string? CampaignCode3 { get; set; }

//        public string? CampaignCode4 { get; set; }

//        public string? CampaignCode5 { get; set; }

//        public DateTime? LostSale { get; set; }

//        public DateTime? TestDrive { get; set; }

//        public DateTime? TestDriveDone { get; set; }

//        public DateTime? OfferPresented { get; set; }

//        public DateTime? OfferAccepted { get; set; }

//        public DateTime? Delivered { get; set; }

//        public DateTime? Appointed { get; set; }

//        public DateTime? VisitAppointment { get; set; }
        
//        public DateTime? Deleted { get; set; }
        
//        public string? EnquiryId { get; set; }

//        public string? DeletedByName { get; set; }
        
//        public string? TransactionId { get; set; }

//        public string? EnquiryVehicleStockNumber { get; set; }
        
//        public string? SoldVehicle { get; set; }

//        public string? SoldVehicleMake { get; set; }

//        public string? SoldVehicleRange { get; set; }

//        public string? SoldVehicleModel { get; set; }

//        public string? SoldVehicleDerivative { get; set; }

//        public string? SoldVehicleVrm { get; set; }

//        public string? SoldVehicleCode { get; set; }

//        public string? SoldVehicleCodeType { get; set; }

//        public double? SoldVehiclePrice { get; set; }

//        public string? SoldVehicleType { get; set; }
        
//        public string? Origin { get; set; }

//        public string? LostSaleReason { get; set; }
//    }
//}
