#contentHolder {
   overflow: auto;
   flex: 1;
   // height: calc(100% - 2.3em);
   position: relative;
}

.disabled-tile {

   .labels, .bars, .headerWords {
      opacity: 0.3
   }


}

.tileHeader {
   position: relative;
}


.skeleton-loader:empty {
   width: 100%;
   display: block;
   background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 80% ), lightgray;
   background-repeat: repeat-y;
   background-size: 50px 500px;
   background-position: 0 0;
   animation: shine 1s infinite;
   border-radius: 2px;
   opacity: 0.5;
}

@keyframes shine {
   to {
      background-position: 100% 0;
   }
}


#contentHolder.HorizontalBar,
#contentHolder.VerticalBar {
   overflow: auto;
}

#contentHolder.BigNumber {
   flex: 1;
   // height: calc(100% - 3em);
}

.contentsHolder {
   height: 100%;
}

table {
   width: 90%;
   margin: 0em 5%;
}

.interactionIconsHolder {
   position: absolute;
   top: 0em;
   right: 0.6em;

   color: var(--brightColourLight);
   display: flex;

   // &.autoTrader {
   //   color: var(--atBlueLight);
   // }
}


//grid stuff

ag-grid-angular {
   height: 100%;
   width: 98%;
}

//donut
ag-grid-angular.withDonut {
   height: 48%;
}

#donutHolder {
   margin: 5% 0em;
   height: 40%;
}

#simpleLabelsHolder {
   .label {
      padding: 2px 5px;
      border-radius: 4px;
      background: var(--brightColourLightest);
      margin: 3px 0px;
   }

   .label.highlighted {
      background: var(--brightColourDark);
   }
}

#verticalBarsHolder {

   // .barAndLabelHolder {
   //   height:1.1em;
   //   min-height: 1.1em;
   //   transition: ease all 0.3s;
   //   display: flex;
   //   margin: 3px 0px;
   //   align-items: center;

   .labelHolder {

      text-align: right;
      // width: 50%;
      // min-width:12em;
      // height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-align: right;
      min-height: 1.3em;
   }

   .gridFont.labelHolder {
      padding-right: 1em;
   }

   .barHolder {
      position: relative;
      // height: 100%;
      min-height: 1.3em;
      // height:1.1em;
      display: flex;
      align-items: center;

      .bar {
         // position: absolute;
         left: 0px;
         height: 1.1em;
         transition: ease all 0.3s;
         overflow: visible;

         // .barValue {
         //   transform: translateX(3.2em);
         //   min-width: 3em;
         //   text-align: left;
         // }
      }

      .barValue {
         margin-left: 0.1em;
         white-space: nowrap;
      }

      .barValue span {
         position: relative;
         padding-left: 0.2em;
      }

      .filterBar {
         background: var(--brightColourLightest);
      }

      .highlightBar {
         background: var(--brightColourDark);
         display: flex;
         justify-content: flex-end;
         align-items: center;
      }

      .highlightBar.narrow {
         height: 50%;
      }
   }

   // }
}

#horizontalBarsHolder {

   height: 100%;
   display: flex;
   flex-direction: row;

   .barAndLabelHolder {
      min-width: 2em;
      transition: ease all 0.3s;
      margin: 17px 4.1px 0px 4.1px;
      align-items: center;

      .labelHolder {
         //
         text-align: center;
         width: 100%;
         overflow: hidden;
         line-height: 1.2;
         //text-overflow: ellipsis;
         // word-wrap: break-word;

         &.breakWord {
            // word-wrap: break-word;
         }
      }

      .barHolder {
         position: relative;
         height: calc(100% - 5em);
         width: 100%;
         min-width: 2em;

         .bar {
            position: absolute;
            bottom: 0px;
            width: 100%;
            //
            transition: ease all 0.3s;

            .barValue {
               width: 100%;
               text-align: center;
               transform: translateY(-1.3em);
            }
         }

         .filterBar {
            background: var(--brightColourLightest);
         }

         .highlightBar {
            background: var(--brightColourDark);
            display: flex;
            justify-content: flex-end;
            align-items: flex-start;
         }
      }
   }
}


#donutAndGridFlexContainer {
   display: flex;
   height: 100%;

   div#donutContainer {
      width: 40%;
      height: calc(100% - 30px);
      margin: 15px 0;
   }

   div#tableContainer {
      width: 60%;
      height: 100%;

      ag-grid-angular.withDonut {
         height: 100%;
         width: 100%;
      }
   }
}

th,
td {
   padding: 0.5em;
}

th {
   background-color: #F5F7F7;
}

th,
td.align-right {
   text-align: right;
}


#contentHolder.stockInsight {
   .filterBar {
      background-color: var(--atBlueLighter) !important;
   }

   .highlightBar {
      background-color: var(--atBlue) !important;
   }
}

#contentHolder.leavingVehicle {
   .filterBar {
      background-color: var(--atGreenLighter) !important;
   }

   .highlightBar {
      background-color: var(--atGreen) !important;
   }
}


#bigNumber {

   height: 100%;
   display: flex;
   align-items: center;
   justify-content: center;
}

.bars {
   width: 100%;
}

.clickable:hover {
   font-weight: initial;
}
