import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { GridApi, ValueGetterParams } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from "../../cph.pipe";
import { CitNowSiteSummary } from "../../model/main.model";
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from "../../services/constants.service";
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from "../../services/selections.service";
import { CitNowService } from "./citNow.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";


@Component({
  selector: "citNowwTable",

  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        id="CitNowTable"
        class="ag-theme-balham"
        [gridOptions]="mainTableGridOptions"
        domLayout="autoHeight"
        [ngClass]="{ 'isSales':service.showSales}"
      >
      </ag-grid-angular>
    </div>
  `,



  styleUrls: ["./../../../styles/components/_agGrid.scss"],
})
export class CitNowwTableComponent implements OnInit {
  //@Input() public sites: CitNowServiceSiteSummary[];
  @Input() public isRegional: boolean;
  @Output() rowClicked = new EventEmitter();

  columnDefs: any[];
  //rowData: SiteExtended[];
  mainTableGridOptions: GridOptionsCph;
  public gridApi: GridApi;
  public importGridApi;
  public gridColumnApi;
  showGrid: boolean;
  subscription: Subscription;


  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public router: Router,
    public excel: ExcelExportService,
    public service: CitNowService,
    public columnTypeService: ColumnTypesService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.mainTableGridOptions.context = { thisComponent: this };
    this.resizeGrid();
  }

  ngOnInit() {
    this.initParams();
    this.showGrid = false;
    this.subscription = this.service.newDataEmitter.subscribe(res => {
      this.refreshTable();
    })
  }

  ngOnDestroy() {
    if(!!this.subscription){this.subscription.unsubscribe();}
  }

  initParams() {

    // table definitions
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false, cellClass: "ag-right-aligned-cell",
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: false,
      },
      rowData: this.getRowData(),
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onRowClicked: (params) => {
        this.onRowClick(params.data);
      },
      onGridReady: (params) => this.onGridReady(params),
      //pinnedTopRowData: this.pinnedTop,
      pinnedBottomRowData: this.getPinnedBottomRowData(),
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },
      columnDefs: this.getColumnDefs(),
    }

  }

  resizeGrid() {
    setTimeout(() => {

      if (this.gridColumnApi) {
        this.gridColumnApi.autoSizeColumns();
      }
      if (this.gridApi) {
        this.gridApi.sizeColumnsToFit();
      }
    }, 100)
  }

  getPinnedBottomRowData(): any[] {
    return this.service.siteRows.filter(x=>x.IsTotal)
  }


  getRowData(): any[] {
    //console.log(this.service.siteRows, "this.service.siteRows!")
    return this.service.siteRows.filter(x=>x.IsRegion && this.isRegional || x.IsSite && !this.isRegional)
  }
  
  getColumnDefs(): (import("ag-grid-community").ColDef | import("ag-grid-community").ColGroupDef)[] {

    // console.log(this.constants.environment.customer, "this.constants.environment.customer!!")

    if (this.service.showSales) {

      if(this.constants.environment.citNoww_showVideosViewed)
      {
        return [
          { headerName: "", children: [
            { headerName: "", valueGetter:(params)=>this.labelGetter(params), colId: this.isRegional ? "RegionDescription" : "SiteDescription", width: 150, type: "label", },
            {
              headerName: this.constants.translatedText.Sales, width: 50, minWidth: 40, colId: 'SalesGroup', children: [
                { headerName: "Enquiries", field: "TargetCount", colId: "TargetCount", width: 80, minWidth: 50, type: "number", },
                { headerName: "Sent", field: "VideosSentCount", colId: "VideosSentCount", width: 80, minWidth: 50, type: "number", },
                { headerName: "%", field: "Percent", colId: "Percent", width: 80, minWidth: 50, type: "percent", },
                { headerName: "Viewed", field: "VideosViewedCount", colId: "VideosViewedCount", width: 80, minWidth: 50, type: "number", },
                { headerName: "%", field: "ViewedPercent", colId: "ViewedPercent", width: 80, minWidth: 50, type: "percent", },
              ]
            }
          ]}
        ]
      }
      else 
      {
        return [
          { headerName: "", children: [
            { headerName: "", valueGetter:(params)=>this.labelGetter(params), colId: this.isRegional ? "RegionDescription" : "SiteDescription", width: 150, type: "label", },
            {
              headerName: this.constants.translatedText.Sales, colId: 'SalesGroup', children: [
                { headerName: "Enquiries", field: "TargetCount", colId: "TargetCount", width: 130, type: "number", },
                { headerName: "#", field: "VideosSentCount", colId: "VideosSentCount", width: 130, type: "number", },
                { headerName: "%", field: "Percent", colId: "Percent", width: 130, type: "percent", },
              ]
            }
          ] }
        ]
      }


    }
    else {

      if(this.constants.environment.citNoww_showVideosViewed)
      {
        return [
          { headerName: "", children: [
            { headerName: "", valueGetter:(params)=>this.labelGetter(params), colId: this.isRegional ? "RegionDescription" : "SiteDescription", width: 150,  minWidth: 150, type: "label", },
            {
              headerName: this.constants.translatedText.Service, colId: 'ServiceGroup', width: 50, minWidth: 40, children: [
                { headerName: "Qualifying WIPs", field: "TargetCount", colId: "TargetCount", width: 80, minWidth: 50, type: "number", },
                { headerName: "Sent", field: "VideosSentCount", colId: "VideosSentCount", width: 50, minWidth: 50, type: "number", },
                { headerName: "%", field: "Percent", colId: "Percent", width: 50, minWidth: 50, type: "percent", },
                { headerName: "Viewed", field: "VideosViewedCount", colId: "VideosViewedCount", width: 50, minWidth: 50, type: "number", },
                { headerName: "%", field: "ViewedPercent", colId: "ViewedPercent", width: 50, minWidth: 50, type: "percent", },
              ]
            },
          ] }
        ]
      }
      else 
      {
        return [
          { headerName: "", children: [
            { headerName: "", valueGetter:(params)=>this.labelGetter(params),  colId: this.isRegional ? "RegionDescription" : "SiteDescription", width: 150, type: "label", },
            {
              headerName: this.constants.translatedText.Service, colId: 'ServiceGroup', children: [
                { headerName: "Qualifying WIPs", field: "TargetCount", colId: "TargetCount", width: 130, type: "number", },
                { headerName: "#", field: "VideosSentCount", colId: "VideosSentCount", width: 130, type: "number", },
                { headerName: "%", field: "Percent", colId: "Percent", width: 130, type: "percent", },
              ]
            },
          ] }
        ]
      }


    }
  }
  
  labelGetter(params: ValueGetterParams): any {
    let row: CitNowSiteSummary = params.data;
    if(row.IsTotal){return this.constants.translatedText.Total};
    return row.IsRegion ? row.RegionDescription : row.SiteDescription
  }



  qualifyingWipsGetter(params) {

    // Vindis currently want Audi results excluded
    // from WIPs
    if (!this.constants.environment.citNoww_excludeAudi) {
      if (!!params.data) {
        return params.data.CitNow.SitePerformance.ServiceOpportunities
      } else {
        return ' - ';
      }
    } else {
      return (params.data.Brand && params.data.Brand.Description.includes('Audi')) ? 0 : this.cphPipe.transform(params.data.CitNow.SitePerformance.ServiceOpportunities, "number", 0);
    }
  }

  wipsCountGetter(params) {

    // Vindis currently want Audi results excluded
    // from WIPs
    if (!this.constants.environment.citNoww_excludeAudi) {
      if (!!params.data) {
        return params.data.CitNow.SitePerformance.ServiceVideos
      } else {
        return ' - ';
      }
    } else {
      return (params.data.Brand && params.data.Brand.Description.includes('Audi')) ? 0 : this.cphPipe.transform(params.data.CitNow.SitePerformance.ServiceVideos, "number", 0);
    }
  }




  onRowClick(site: CitNowSiteSummary): void {
    if (site.SiteId === 0) { return }
    this.rowClicked.next(site);
  }


  refreshTable(): void {
    if (!this.gridApi) return
    this.gridApi.setColumnDefs(this.getColumnDefs());
    this.gridApi.setRowData(this.getRowData());
    this.gridApi.setPinnedBottomRowData(this.getPinnedBottomRowData());

    setTimeout(()=> {
      this.gridApi.sizeColumnsToFit();
    }, 50);
    
  }


  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'CitNOW', 1, 1);
  }
}
