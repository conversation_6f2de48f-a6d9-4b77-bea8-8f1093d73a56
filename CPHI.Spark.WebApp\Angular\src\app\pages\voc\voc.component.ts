import { Component, OnInit } from "@angular/core";
import { ConstantsService } from "../../services/constants.service";
import { SelectionsService } from "../../services/selections.service";
import { Month, VocSiteRow, VocStatCard } from '../../model/main.model';
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { CphPipe } from "src/app/cph.pipe";

@Component({
  selector: "app-voc",
  templateUrl: "./voc.component.html",
  styleUrls: ["./voc.component.scss"],
})
export class VocComponent implements OnInit {
  months: Array<Month>;
  salesData: VocSiteRow[];
  aftersalesData: VocSiteRow[];
  siteStatCards: VocStatCard[];
  siteCurrentlyViewing: VocSiteRow;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getData: GetDataMethodsService,
    public pipe: CphPipe
  ) { }

  ngOnInit() {
    this.selections.triggerSpinner.next({ show: false });
    this.selections.initiateVoc();
    this.getVocData();
    this.makeMonths();
  }

  getVocData() {
    this.getData.getVocSiteRows(this.selections.voc.month.startDate, this.selections.voc.isSales).subscribe((res: VocSiteRow[]) => {
      this.formatSites(res);
    });
  }

  formatSites(rows: VocSiteRow[]) {
    let nationalAverage: VocSiteRow = rows.find(x => x.Site == 'National Average');
    let rrgAverage: VocSiteRow = rows.find(x => x.Site == 'RRG Average');

    // Remove the National Average and RRG Average ready to push to the end later
    rows = rows.filter(x => x.Site != nationalAverage.Site);
    rows = rows.filter(x => x.Site != rrgAverage.Site);

    // Push RRG Average and National Average to the end
    rows.push(rrgAverage);
    rows.push(nationalAverage);

    this.selections.voc.isSales ? this.salesData = rows : this.aftersalesData = rows;
    this.selections.voc.sites = rows;
    
    this.selections.voc.numbersHaveChanged.next(true);
  }

  chooseDepartment(isSales: boolean): void {
    if (this.selections.voc.isSales == isSales) return;

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });
    this.selections.voc.isSales = isSales;

    if (this.siteStatCards) {
      this.selectSite(this.siteCurrentlyViewing);
    }

    if (isSales) {
      if (this.salesData) { this.selections.voc.sites = this.salesData; }
      else {
        return this.getVocData();
      }
    } else {
      if (this.aftersalesData) { this.selections.voc.sites = this.aftersalesData; }
      else {
        return this.getVocData();
      }
    }

    this.selections.voc.numbersHaveChanged.next(true);
  }

  selectSite(site: VocSiteRow): void {
    this.siteCurrentlyViewing = site;
    this.getData.getVocStatCards(site.SiteId, this.selections.voc.month.startDate, this.selections.voc.isSales).subscribe((stats: VocStatCard[]) => {
      let sortOrder: number[];

      // Set sort order for tiles depending if sales or aftersales selected
      if (this.selections.voc.isSales) {
        sortOrder = [1, 2, 4, 5, 3, 7, 8, 6];
      } else {
        sortOrder = [6, 7, 1, 2, 4, 3, 5];
      }

      for (let i = 0; i < stats.length; i++) {
        // Set the sort order
        stats[i].SortOrder = sortOrder[i];
        
        // Split the headings except NPS
        if (stats[i].Label != 'NPS') {
          stats[i].Label = this.pipe.transform(stats[i].Label, 'splitPascalCase', 0);
        }

        // Rename some labels
        if (stats[i].Label == 'Keep In Touch') {
          stats[i].Label = 'Let\'s Keep In Touch';
        }
        if (stats[i].Label == 'Interviews Completed') {
          stats[i].Label = 'Interview Completion';
        }
      }

      this.siteStatCards = stats.sort((a, b) => (a.SortOrder > b.SortOrder) ? 1 : -1);
    });
  }

  makeMonths() {
    this.months = this.constants.makeMonths(0, -3).sort((a, b) => a.startDate.getTime() - b.startDate.getTime());
    this.months = this.months.filter(x=>x.startDate.getFullYear() == 2021)
    this.months = this.months.filter(x=>x.startDate.getMonth() >= 9)
  }

  selectMonth(month: Month) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });
    this.selections.voc.month = month;
    this.getVocData();
    if (this.siteStatCards) {
      this.selectSite(this.siteCurrentlyViewing);
    }
  }

  changeMonth(changeAmount: number) {

    let currentIndex = this.months.findIndex(x=>x.name===this.selections.voc.month.name);
    if(currentIndex === this.months.length-1 && changeAmount > 0){
      return ;// already at end, do nothing
    }

    if(currentIndex == 0 && changeAmount < 0){
      return;  //already at start, do nothing
    }
    

      this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });

      this.selections.voc.month.startDate = this.constants.addMonths(this.selections.voc.month.startDate, changeAmount);
      var lastDayOfMonth = new Date(this.selections.voc.month.startDate.getFullYear(), this.selections.voc.month.startDate.getMonth() + 1, 0);

      this.selections.voc.month.endDate = lastDayOfMonth;
      this.selections.voc.month.name = this.selections.voc.month.startDate.toLocaleDateString('en-gb', { month: 'short', year: '2-digit' });  
      
      this.getVocData();
      if (this.siteStatCards) {
        this.selectSite(this.siteCurrentlyViewing);
      }

  }
}
