import {DatePipe} from "@angular/common";
import {HttpClient, HttpEventType} from "@angular/common/http";
import {Component, ElementRef, OnInit, ViewChild,} from "@angular/core";
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {
   AutoPriceValuationModalComponent
} from "src/app/components/autoPriceValuationModal/autoPriceValuationModal.component";
import {ValuationModalParams} from "src/app/components/autoPriceValuationModal/valuationCosting/ValuationModalParams";
import {VehicleValuationService} from "src/app/components/autoPriceValuationModal/vehicleValuation.service";
import {LocationsModalComponent} from "src/app/components/locationsModal/locationsModal.component";
import {CphPipe} from "src/app/cph.pipe";
import {ImportMask, ImportMaskToSave} from "src/app/model/ImportMask";
import {ValuationBatchResult} from "src/app/model/ValuationBatchResult";
import {VehicleValutionBatch} from "src/app/model/VehicleValuationBatch";
import {ApiAccessService} from "src/app/services/apiAccess.service";
import {ConstantsService} from "src/app/services/constants.service";
import {GetDataMethodsService} from "src/app/services/getDataMethods.service";
import {SelectionsService} from "src/app/services/selections.service";
import * as XLSX from 'xlsx';
import {BulkUploadService} from "./bulkUpload.service";
import {BulkUploadPredefinedTemplate} from "./BulkUploadPredefinedTemplate";
import {BulkUploadPredefinedTemplateType} from "./BulkUploadPredefinedTemplateType";
import {PredefinedImportTemplatesService} from "./predefinedImportTemplates.service";
import {
   AutoPriceNewVehicleValuationModalComponent
} from "src/app/components/autoPriceNewVehicleValuationModal/autoPriceNewVehicleValuationModal.component";
import {
   ChooseNewVehicleModalComponent
} from "src/app/components/autoPriceNewVehicleValuationModal/chooseNewVehicleModal/chooseNewVehicleModal.component";
import {
   NewVehicleValuationModalService
} from "src/app/components/autoPriceNewVehicleValuationModal/newVehicleValuationModal.service";
import {AtDerivativeItem} from "src/app/model/AtDerivativeItem";
import {GetNewVehicleModalParams} from "src/app/model/GetNewVehicleModalParams";
import {NewVehicleValuationModal} from "src/app/model/NewVehicleValuationModal";
import {CompetitorAnalysisParams} from "src/app/components/competitorAnalysis/CompetitorAnalysisParams";
import {CompetitorAnalysisService} from "src/app/components/competitorAnalysis/competitorAnalysis.service";
import {ActivatedRoute} from "@angular/router";

@Component({
   selector: 'bulkUpload',
   templateUrl: './bulkUpload.component.html',
   styleUrls: ['./bulkUpload.component.scss']
})

export class BulkUploadComponent implements OnInit {
   @ViewChild('saveMaskChoiceModal', {static: true}) saveMaskChoiceModal: ElementRef;
   @ViewChild('saveMaskNameModal', {static: true}) saveMaskNameModal: ElementRef;
   @ViewChild('filterRowsModal', {static: true}) filterRowsModal: ElementRef;
   @ViewChild('setBatchNameModal', {static: true}) setBatchNameModal: ElementRef;
   @ViewChild('setVehicleParamsModal', {static: true}) setVehicleParamsModal: ElementRef;


   stage: 'start' | 'viewExistingBatch' | 'doBulkUpload' | 'fileChosen' = 'start';
   previousStage: 'start' | 'viewExistingBatch' | 'doBulkUpload' | 'fileChosen';

   missingFieldsFromImport: string[];
   importMasks: ImportMask[];

   //file stuff
   fileName: string = '';
   file: File;
   importFile: string;
   newBatchName: string;
   applyPriceScenarios: boolean = false;
   needToSetCondition: boolean = false;
   conditions: string[] = ['Excellent', 'Great', 'Good', 'OK', 'Poor'];
   selectedCondition: string = 'Excellent';

   failedToUploadCount: number;
   fetchingBatches: boolean;
   rowDataForPredefinedImports: any[];

   newVehicleReg: string;
   newVehicleMileage: number;
   newVehicleCondition: string = 'Excellent';
   newVehicleColour: string;

   predefinedImports: BulkUploadPredefinedTemplate[] = [
      {Name: 'Aston Barclay', Type: BulkUploadPredefinedTemplateType.AstonBarclay, ImageName: 'AstonBarclay.png'},
      {Name: 'Carwow', Type: BulkUploadPredefinedTemplateType.CarWow, ImageName: 'Carwow.png'},
      {Name: 'Manheim', Type: BulkUploadPredefinedTemplateType.Manheim, ImageName: 'Manheim.png'},
      {Name: 'Motorway', Type: BulkUploadPredefinedTemplateType.Motorway, ImageName: 'Motorway.png'},
      {Name: 'Motorway 2', Type: BulkUploadPredefinedTemplateType.Motorway2, ImageName: null},
      {
         Name: 'Shoreham Vehicle Auctions',
         Type: BulkUploadPredefinedTemplateType.Shoreham,
         ImageName: 'ShorehamVehicleAuctions.png'
      },
      {
         Name: 'The Fleet Auction Group',
         Type: BulkUploadPredefinedTemplateType.FleetAuctionGroup,
         ImageName: 'TheFleetAuctionGroup.png'
      },
      {Name: 'BCA', Type: BulkUploadPredefinedTemplateType.BCA, ImageName: 'BCA.png'},
      {Name: 'BCA Approved', Type: BulkUploadPredefinedTemplateType.BCA, ImageName: 'BCAApproved.png'},
      {Name: 'BCA 3', Type: BulkUploadPredefinedTemplateType.BCA, ImageName: null},
      {Name: 'Motability', Type: BulkUploadPredefinedTemplateType.Motability, ImageName: 'Motability.png'},
      {Name: 'Santander Live', Type: BulkUploadPredefinedTemplateType.SantanderLive, ImageName: 'SantanderLive.png'},
      {Name: 'VCRS Live', Type: BulkUploadPredefinedTemplateType.VCRSLive, ImageName: null},
      {Name: 'UCaRS', Type: BulkUploadPredefinedTemplateType.UCaRS, ImageName: null},
      {Name: 'Other...', Type: BulkUploadPredefinedTemplateType.Other, ImageName: null},
   ];
   singleMode: boolean;

   constructor(
      public selections: SelectionsService,
      public constants: ConstantsService,
      public modalService: NgbModal,
      public cphPipe: CphPipe,
      public apiAccessService: ApiAccessService,
      public service: BulkUploadService,
      public getDataService: GetDataMethodsService,
      private http: HttpClient,
      public predefinedImportTemplatesService: PredefinedImportTemplatesService,
      private newVehicleValuationModalService: NewVehicleValuationModalService,
      private competitorAnalysisService: CompetitorAnalysisService,
      private route: ActivatedRoute
   ) {

   }

   ngOnDestroy() {
   }

   ngOnInit(): void {
      this.selections.triggerSpinner.emit({show: false});

      this.route.paramMap.subscribe(params => {
         const id = Number(params.get('id')) || 0;
         const reg = params.get('reg');
         const milage = Number(params.get('mileage')) || 0;
         const condition = params.get('condition');

         //Get data for reg number
         if (reg) {
            this.openValuationModal(Number(id), reg, milage, condition);
         }
      });
   }


   openValuationModal(id: number, reg: string, mileage: number, condition: string, colour?: string): void {

      if (!reg) {
         return;
      }

      //launch single vehicle valuation modal
      const modalRef = this.service.modalService.open(AutoPriceValuationModalComponent, {
         keyboard: true,
         size: 'md',
         windowClass: 'autotraderValuation'
      });
      const modalComponent = (modalRef.componentInstance as AutoPriceValuationModalComponent);

      let modalParams: ValuationModalParams = {
         reg: reg,
         mileage: mileage,
         condition: condition ?? 'Excellent',
         valuationId: id,
         colour: colour
      }
      modalComponent.initParams(modalParams, null);


      modalRef.result.then(
         result => {
            //'okd'
         },
         //closed
         reason => {
            //cancelled
         }
      );
   }

   getTableStateLabels() {
      //this retrieves any previous layout changes the user has made to the main report table that you see at first
      this.apiAccessService.get('api/AutoPrice', 'GetTableStateLabels', [{
         key: 'pageName',
         value: 'valuations'
      }]).subscribe((res: string[]) => {
         this.service.tableStateLabels = res;
      })
   }


   chooseDifferentFile() {
      this.importFile = null;
      this.file = null;
      this.stage = 'doBulkUpload';
   }

   cancelImport() {
      this.resetImportSettings()
      //this.service.showImportProcess = false;
   }

   imageSource(predefinedImport: BulkUploadPredefinedTemplate) {
      return `./assets/imgs/autoTrader/brands/${predefinedImport.ImageName}`;
   }

   getImportMasks() {
      this.apiAccessService.get('api/ImportMasks', 'GetImportMasks').subscribe((res: ImportMask[]) => {

         res.map(mask => {
            mask.regColumnIndexes = mask.RegColumns ? this.service.lettersToIndexes(mask.RegColumns) : []
            mask.mileageColumnIndexes = mask.MileageColumns ? this.service.lettersToIndexes(mask.MileageColumns) : []
            mask.conditionColumnIndexes = mask.ConditionColumns ? this.service.lettersToIndexes(mask.ConditionColumns) : []
            mask.sIVColumnIndexes = mask.SIVColumns ? this.service.lettersToIndexes(mask.SIVColumns) : []
            mask.reference1ColumnIndexes = mask.Reference1Columns ? this.service.lettersToIndexes(mask.Reference1Columns) : []
            mask.reference2ColumnIndexes = mask.Reference2Columns ? this.service.lettersToIndexes(mask.Reference2Columns) : []
            mask.reference3ColumnIndexes = mask.Reference3Columns ? this.service.lettersToIndexes(mask.Reference3Columns) : []
            mask.isVatQualifyingColumnIndexes = mask.IsVatQualifyingColumns ? this.service.lettersToIndexes(mask.IsVatQualifyingColumns) : []
            mask.currentPriceColumnIndexes = mask.CurrentPriceColumns ? this.service.lettersToIndexes(mask.CurrentPriceColumns) : []
            mask.cAPCleanColumnIndexes = mask.CAPCleanColumns ? this.service.lettersToIndexes(mask.CAPCleanColumns) : []
         })

         this.importMasks = res;
         this.selections.triggerSpinner.emit({show: false});
      })
   }

   showModal(batchId: number) {
      //this.vehicleValuationService.batchId = batchId;

      let modalParams: ValuationModalParams;

      if (!batchId) {
         modalParams = {
            reg: this.newVehicleReg.replace(' ', '').toUpperCase(),
            mileage: this.newVehicleMileage,
            condition: this.newVehicleCondition,
            valuationId: null,
            colour: this.newVehicleColour
         };
      }

      const modalRef = this.service.modalService.open(AutoPriceValuationModalComponent, {
         keyboard: true,
         size: 'md',
         windowClass: 'autotraderValuation'
      });
      (modalRef.componentInstance as AutoPriceValuationModalComponent).initParams(modalParams, modalRef);

      modalRef.result.then(
         result => {
            // 'okd'
            this.getVehicleValuationBatches({single: true});
         },
         // closed
         reason => {
            // cancelled
            this.getVehicleValuationBatches({single: true});
         }
      );
   }

   uploadVehiclesForValuation() {
      this.selections.triggerSpinner.emit({show: true, message: 'Uploading...'});

      let finalItemsToUpload;

      if (!this.service.chosenPredefinedCompany) {
         finalItemsToUpload = this.service.destinationTableRowData.filter(i => i.condition.toUpperCase() != 'U' && !isNaN(parseInt(i.mileage)));
         finalItemsToUpload.map(item => {
            item.mileage = item.mileage.replace(',', '');
            item.siv = item.siv.replace(',', '');
            item.currentPrice = item.currentPrice.replace(',', '');
            item.capClean = item.capClean.replace(',', '');
         })
      } else {
         finalItemsToUpload = []
         const rowCount = this.predefinedImportTemplatesService.gridApi.getDisplayedRowCount()

         for (let i = 0; i < rowCount; i++) {
            const rowNode = this.predefinedImportTemplatesService.gridApi.getDisplayedRowAtIndex(i);
            finalItemsToUpload.push(rowNode.data);
         }
      }


      const formData = new FormData();
      formData.append('file', this.file);
      formData.append('vehiclesForValuationParams', JSON.stringify(finalItemsToUpload));
      formData.append('batchName', this.newBatchName);
      formData.append('applyPriceScenarios', this.applyPriceScenarios.toString());
      if (!this.service.chosenPredefinedCompany) {
         formData.append('bulkUploadPredefinedTemplateType', 'Other');
      } else {
         formData.append('bulkUploadPredefinedTemplateType', this.service.chosenPredefinedCompany.Type.toString());
      }

      // if (finalItemsToUpload.length < this.service.destinationTableRowData.length) {
      //   this.failedToUploadCount = this.service.destinationTableRowData.length - finalItemsToUpload.length;
      // }

      this.apiAccessService.postWithOptions('api/VehicleValuation', 'UploadVehiclesForValuation', formData, {
         reportProgress: true,
         observe: 'events'
      }).subscribe((event) => {
         if (event.type === HttpEventType.Response) {
            const timeToEmail: number = (15 + (finalItemsToUpload.length * 0.6)) * 1000; // 15 seconds for listener event, 6 seconds for each vehicle, in ms
            this.constants.alertModal.title = 'Upload successful';
            this.constants.alertModal.message = this.generateWaitTimeMessage(timeToEmail);// `Your file has been uploaded. Please look out for an email in the next ${ this.date.transform(timeToEmail, 'mm:ss')} ${ timeToEmail < 60000 ? 'seconds' : 'minutes' }`;
            this.modalService.open(this.constants.alertModal.elementRef, {
               size: "sm",
               keyboard: false,
               ariaLabelledBy: "modal-basic-title"
            });
            this.constants.toastSuccess('Upload successful');
            this.resetImportSettings();
            //this.service.bulkItemsLoaded.emit(true);
            //this.service.showImportProcess = false;
            this.selections.triggerSpinner.emit({show: false});
         }
      }, e => {
         this.constants.toastDanger('Failed to upload');
         console.error('Failed to upload: ' + JSON.stringify(e));
         this.selections.triggerSpinner.emit({show: false});
      });
   }

   generateWaitTimeMessage(timeToEmail: number) {
      let message: string;
      if (timeToEmail < 90000) { // Less than 1.5 minutes
         message = "Your file has been uploaded. Please look out for an email in the next minute.";
      } else if (timeToEmail < 120000) { // Less than 2 minutes
         message = "Your file has been uploaded. Please look out for an email in the next 2 minutes.";
      } else if (timeToEmail < 300000) { // Less than 5 minutes
         message = "Your file has been uploaded. Please look out for an email in the next few minutes.";
      } else {
         let roundedMinutes = Math.round(timeToEmail / 60000); // Convert milliseconds to minutes and round
         message = `Your file has been uploaded. Please look out for an email, estimated processing time is c. ${roundedMinutes} minutes.`;
      }

      if (this.failedToUploadCount) {
         message += ` ${this.failedToUploadCount} row(s) failed to upload due to badly formatted mileage fields. Please review these and upload seperately.`
      }
      return message;
   }

   resetImportSettings() {
      this.service.sourceDataArray = [];
      this.createNewImportMask();
      this.importFile = null;
      this.fileName = null;
      this.file = null;
      this.stage = 'start';
      this.needToSetCondition = false;
   }

   onFileChange(evt: any, reportCompany: BulkUploadPredefinedTemplate) {

      // Clear any existing data
      this.service.destinationTableRowData = null;
      this.missingFieldsFromImport = null;

      if (evt.target.value) {
         this.fileName = /[^\\]*$/.exec(evt.target.value)[0];
      }

      /* wire up file reader */
      const target: DataTransfer = <DataTransfer>(evt.target);
      let inputFileType = target['value'].split(/\.(?=[^\.]+$)/)[1];

      if (target.files.length !== 1) {
         return alert('Please select only 1 file');
      }

      this.file = target.files[0];

      const reader: FileReader = new FileReader();
      reader.onload = (e: any) => {
         try {
            /* read workbook */
            this.importFile = e.target.result;

            if (inputFileType.includes('xl')) {
               this.service.sourceDataArray = this.openExcelFile(this.importFile);
            } else if (inputFileType.includes('csv')) {
               this.service.sourceDataArray = this.openCsvFile(this.importFile);
            } else {
               this.constants.toastDanger('Invalid file type - Please upload an Excel or CSV file');
               this.selections.triggerSpinner.emit({show: false});
            }

            this.extractBodyAndHeadersFromExcel(this.service.sourceDataArray, reportCompany);

            if (reportCompany.Name == 'Other...') {
               this.createNewImportMask()
               this.service.redrawSourceAndDestinationTables();
               setTimeout(() => {
                  if (this.service.sourceTableComponent) {
                     this.service.sourceTableComponent.updateGrid()
                  }
               }, 100)
               this.service.chosenPredefinedCompany = null;
            } else {
               this.service.chosenPredefinedCompany = reportCompany;
            }

            if (reportCompany.Name != 'Other...' && !this.predefinedImportTemplatesService.rowData) {
               return;
            }
            this.stage = 'fileChosen'

         } catch (error) {
            this.selections.triggerSpinner.emit({show: false});
            this.constants.toastDanger('Problem loading file');
         }
      };

      setTimeout(() => {
         reader.readAsBinaryString(target.files[0]);
      }, 50)
   }

   openExcelFile(file: string): any[][] {
      const wb: XLSX.WorkBook = XLSX.read(file, {type: 'binary'});

      /* grab first sheet */
      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      /* save data */
      return <any[][]>(XLSX.utils.sheet_to_json(ws, {header: 1, blankrows: true}));
   }


   openCsvFile(file: string): any[][] {
      let result: Array<string[]> = [];
      let currentRow: string = '';
      let inQuotes: boolean = false;

      // Iterate over each character in the file
      for (let char of file) {
         if (char === '"' && currentRow.slice(-1) !== '\\') {
            // Flip the inQuotes state when encountering a quote that is not escaped
            inQuotes = !inQuotes;
         } else if (char === '\n' && !inQuotes) {
            // Only consider a newline as a row delimiter if outside quotes
            result.push(this.processRow(currentRow));
            currentRow = '';
            continue;
         }
         currentRow += char;
      }

      // Process the last row if it's not empty
      if (currentRow.length > 0) {
         result.push(this.processRow(currentRow));
      }

      return result;
   }


   processRow(row: string): string[] {
      // Use the sophisticated split logic
      return this.splitOn(row, ',');
   }


   splitOn(row: string, delimiter: string): string[] {
      // A placeholder for a more sophisticated split logic that respects quoted delimiters
      let cells: string[] = [];
      let currentCell = '';
      let inQuotes = false;

      for (let char of row) {
         if (char === '"' && currentCell.slice(-1) !== '\\') {
            inQuotes = !inQuotes;
         } else if (char === delimiter && !inQuotes) {
            cells.push(currentCell);
            currentCell = '';
            continue;
         }
         currentCell += char;
      }

      cells.push(currentCell); // Add the last cell

      return cells.map(cell => cell.trim().replace(/^"|"$/g, '')); // Trim and remove surrounding quotes
   }


   private createExcelVisualisationTableData(sourceDataArray: string[][]): any {
      const excelColumnLetters = [];
      const excelFirstxRows = sourceDataArray.slice(0, 99999);

      // Create column letters
      const maxCols = Math.max(...excelFirstxRows.map(row => row.length));
      for (let i = 0; i < maxCols; i++) {
         excelColumnLetters.push(this.columnNumberToColumnLetterString(i + 1));
      }

      // Prepare the body data
      const excelBodyData: any[] = excelFirstxRows.map((row, rowIndex) => {
         const rowData: { [key: string]: string } = {"RowNo": (rowIndex + 1).toString()};
         row.forEach((cell, colIndex) => {
            const colLetter = excelColumnLetters[colIndex];
            rowData[colLetter] = this.clearFormatting(cell.toString() || ''); // Ensure every cell has a value
            rowData[colLetter] = this.clearCurrencySymbols(cell.toString() || ''); // Ensure every cell has a value
         });

         // Fill in any blank cells at the end
         for (let i = row.length; i < maxCols; i++) {
            rowData[excelColumnLetters[i]] = '';
         }

         return rowData;
      });

      return {mockExcelBodyData: excelBodyData, excelColumnLetters};
   }

   private clearFormatting(s: string): string {
      //remove tabs /t
      return s.replace(/\t/g, '');
   }

   private clearCurrencySymbols(s: string): string {
      // This regex targets strings that start with £ or Â£ and removes those symbols
      return s.replace(/^(Â£|£)/, '');
   }


   changeHeaderRow(change: number) {
      this.service.chosenImportMask.TopRowsToSkip += change;
      this.extractBodyAndHeadersFromExcel(this.service.sourceDataArray, null);
      this.service.redrawSourceAndDestinationTables()
   }


   extractBodyAndHeadersFromExcel(sourceDataArray: string[][], reportCompany: BulkUploadPredefinedTemplate) {

      let {mockExcelBodyData, excelColumnLetters} = this.createExcelVisualisationTableData(sourceDataArray);
      this.service.sourceTableAllRows = JSON.parse(JSON.stringify(mockExcelBodyData));

      switch (reportCompany?.Name) {
         case 'BCA':
            this.predefinedImportTemplatesService.convertBcaFile(this.service.sourceTableAllRows);
            break;
         case 'BCA Approved':
            this.predefinedImportTemplatesService.convertBcaApprovedFile(this.service.sourceTableAllRows);
            break;
         case 'BCA 3':
            this.predefinedImportTemplatesService.convertbca3File(this.service.sourceTableAllRows);
            break;
         case 'Aston Barclay':
            this.predefinedImportTemplatesService.convertAstonBarclayFile(this.service.sourceTableAllRows);
            break;
         case 'Carwow':
            this.predefinedImportTemplatesService.convertCarwowFile(this.service.sourceTableAllRows);
            break;
         case 'Manheim':
            this.predefinedImportTemplatesService.convertManheimFile(this.service.sourceTableAllRows);
            break;
         case 'Motorway':
            this.predefinedImportTemplatesService.convertMotorwayFile(this.service.sourceTableAllRows);
            break;
         case 'Motorway 2':
            this.predefinedImportTemplatesService.convertMotorway2File(this.service.sourceTableAllRows);
            break;
         case 'Shoreham Vehicle Auctions':
            this.predefinedImportTemplatesService.convertShorehamOrFleetAuctionGroupFile(this.service.sourceTableAllRows);
            break;
         case 'The Fleet Auction Group':
            this.predefinedImportTemplatesService.convertShorehamOrFleetAuctionGroupFile(this.service.sourceTableAllRows);
            break;
         case 'Motability':
            this.predefinedImportTemplatesService.convertMotabilityFile(this.service.sourceTableAllRows);
            break;
         case 'Santander Live':
            this.predefinedImportTemplatesService.convertSantanderFile(this.service.sourceTableAllRows);
            break;
         case 'VCRS Live':
            this.predefinedImportTemplatesService.convertVCRSFile(this.service.sourceTableAllRows);
            break;
         case 'UCaRS':
            this.predefinedImportTemplatesService.convertUCaRSFile(this.service.sourceTableAllRows);
            break;
         default:
            this.service.generatePinnedAndMainRowsForSourceTable(this.service.sourceTableAllRows);
            this.service.excelColumnLetters = excelColumnLetters;
            break;
      }
   }


   possiblySaveImportMap() {
      this.modalService.open(this.saveMaskChoiceModal, {
         windowClass: 'saveModal',
         size: 'md',
         keyboard: true,
         ariaLabelledBy: 'modal-basic-title'
      }).result.then((result) => {
         //have chosen to 'OK' selections
      }, (reason) => {
         //dismissed
      });
   }

   maybeOverwriteImportMask(importMask: ImportMask | null) {
      //this.service.chosenImportMask.Id = importMask ? importMask.Id : null;
      //this.newMapName = this.service.chosenImportMask.Name;


      this.modalService.open(this.saveMaskNameModal, {
         size: 'md',
         keyboard: true,
         ariaLabelledBy: 'modal-basic-title'
      }).result.then((result) => {
         //have chosen to 'OK' selections

         importMask.RegColumns = importMask.regColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.MileageColumns = importMask.mileageColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.ConditionColumns = importMask.conditionColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.SIVColumns = importMask.sIVColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.Reference1Columns = importMask.reference1ColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.Reference2Columns = importMask.reference2ColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.Reference3Columns = importMask.reference3ColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.IsVatQualifyingColumns = importMask.isVatQualifyingColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.CurrentPriceColumns = importMask.currentPriceColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.CAPCleanColumns = importMask.cAPCleanColumnIndexes.map(x => this.service.convertNumberToLetter(x)).join(',');
         importMask.FilterState = JSON.stringify(this.service.sourceTableComponent.gridApi.getFilterModel());

         let importMaskToSave: ImportMaskToSave = {
            Id: importMask.Id,
            UserId: importMask.UserId,
            UserName: importMask.UserName,
            Name: importMask.Name,
            TopRowsToSkip: importMask.TopRowsToSkip,
            FilterState: importMask.FilterState,
            RegColumns: importMask.RegColumns,
            MileageColumns: importMask.MileageColumns,
            ConditionColumns: importMask.ConditionColumns,
            SIVColumns: importMask.SIVColumns,
            Reference1Columns: importMask.Reference1Columns,
            Reference2Columns: importMask.Reference2Columns,
            Reference3Columns: importMask.Reference3Columns,
            IsVatQualifyingColumns: importMask.IsVatQualifyingColumns,
            CurrentPriceColumns: importMask.CurrentPriceColumns,
            CAPCleanColumns: importMask.CAPCleanColumns
         }

         this.getDataService.saveImportMask(importMaskToSave).subscribe(res => {
            this.getImportMasks();
            this.constants.toastSuccess('Import map saved');
            this.modalService.dismissAll();
         }, e => {
            this.constants.toastDanger('Failed to save import map');
            console.error('failed to save import mask ' + JSON.stringify(e));
         })
      }, (reason) => {
         //chose to cancel, do nothing
      });
   }

   chooseImportMask(importMask: ImportMask) {
      let toastRef: any = this.selections.triggerSpinner.emit({show: true, message: 'Loading import map...'});

      setTimeout(() => {
         this.service.chosenImportMask = importMask;
         let haveUpdatedGridCols = false;
         if (this.service.destinationTableComponent) {
            if (importMask.ConditionColumns.length > 0) {
               this.service.destinationTableComponent.showCondition = true;
               haveUpdatedGridCols = true;
            }
            if (importMask.SIVColumns.length > 0) {
               this.service.destinationTableComponent.showSiv = true;
               haveUpdatedGridCols = true;
            }
            if (importMask.Reference1Columns.length > 0) {
               this.service.destinationTableComponent.showReference1 = true;
               haveUpdatedGridCols = true;
            }
            if (importMask.Reference2Columns.length > 0) {
               this.service.destinationTableComponent.showReference2 = true;
               haveUpdatedGridCols = true;
            }
            if (importMask.Reference3Columns.length > 0) {
               this.service.destinationTableComponent.showReference3 = true;
               haveUpdatedGridCols = true;
            }
            //if (importMask.Reference3Columns.length > 0) { this.service.destinationTableComponent.showOptionalSpec = true; haveUpdatedGridCols = true; }
            if (importMask.IsVatQualifyingColumns.length > 0) {
               this.service.destinationTableComponent.showIsVATQualifying = true;
               haveUpdatedGridCols = true;
            }
            if (importMask.CurrentPriceColumns.length > 0) {
               this.service.destinationTableComponent.showCurrentPrice = true;
               haveUpdatedGridCols = true;
            }
            if (importMask.CAPCleanColumns.length > 0) {
               this.service.destinationTableComponent.showCAPClean = true;
               haveUpdatedGridCols = true;
            }

            if (haveUpdatedGridCols) {
               this.service.destinationTableComponent.updatedColDefsAndRedrawTable();
            }
         }

         this.service.redrawSourceAndDestinationTables();
      }, 250)
   }

   createNewImportMask() {
      //let toastRef: any = this.selections.triggerSpinner.emit({ show: true, message: 'Creating import map...' });

      this.service.chosenImportMask = {
         Id: null,
         Name: `New Import Map Created ${this.cphPipe.transform(new Date(), 'dateAndTime', 0)}`,
         TopRowsToSkip: 1,
         UserId: this.selections.user.PersonId,
         UserName: this.selections.user.Name,

         FilterState: null,
         RegColumns: null, //comma separated numbers e.g. 1 would mean column A.     1,2 => A and B
         MileageColumns: null,
         ConditionColumns: null,
         SIVColumns: null,
         Reference1Columns: null,
         Reference2Columns: null,
         Reference3Columns: null,
         IsVatQualifyingColumns: null,
         CurrentPriceColumns: null,
         CAPCleanColumns: null,


         //client side props
         regColumnIndexes: [],
         mileageColumnIndexes: [],
         conditionColumnIndexes: [],
         sIVColumnIndexes: [],
         reference1ColumnIndexes: [],
         reference2ColumnIndexes: [],
         reference3ColumnIndexes: [],
         isVatQualifyingColumnIndexes: [],
         currentPriceColumnIndexes: [],
         cAPCleanColumnIndexes: [],
      }
   }


   validateImport() {
      if (!this.service.destinationTableRowData) {
         return false;
      }
      let rows: any[] = this.service.destinationTableRowData;
      let missingFields: string[] = [];

      if (rows.filter(x => x.vehicleReg === '').length === rows.length) missingFields.push('Reg');
      if (rows.filter(x => x.mileage === '').length === rows.length) missingFields.push('Mileage');

      if (missingFields.length) {
         this.missingFieldsFromImport = missingFields;
         return true;
      }

      this.missingFieldsFromImport = null;
      return false;
   }

   instructionRowMessage() {
      if (!this.importFile) {
         return 'Choose an excel file to upload';
      }
      if (this.importFile && !this.service.chosenImportMask) {
         return 'Table shows data from excel file. Choose which import map to use to import this data, or create a new one.'
      }
      if (this.service.chosenImportMask) {
         return `Complete the 3 steps below. When you are happy, if you wish to save the import map choose 'Save import map'. Then choose 'Import Vehicle Details' on the right.`
      }
   }


   columnNumberToColumnLetterString(num: number): string {
      function toLetters(num) {
         "use strict";
         var mod = num % 26;
         var pow = num / 26 | 0;
         var out = mod ? String.fromCharCode(64 + mod) : (pow--, 'Z');

         let result = pow ? toLetters(pow) + out : out;
         if (result == '?Z') result = ''
         return result
      }

      return toLetters(num);
   }


   columnNumbersToLetters(numbers: number[]): string[] {
      let letters: string[] = [];

      if (numbers) {
         numbers.forEach(num => {
            letters.push(this.columnNumberToColumnLetterString(num));
         })
      }

      return letters;
   }


   maybeGiveBatchName() {
      if (this.missingFieldsFromImport) {
         return;
      }
      this.newBatchName = this.constants.clone(this.fileName);

      if (!this.service.chosenPredefinedCompany) {
         if (this.service.destinationTableRowData.filter(x => x.condition === '').length === this.service.destinationTableRowData.length) {
            this.needToSetCondition = true;
         } else {
            this.needToSetCondition = false;
         }
      } else {
         if (this.service.chosenPredefinedCompany.Name === 'Aston Barclay') {
            this.needToSetCondition = true;
         }
      }

      this.modalService.open(this.setBatchNameModal, {
         size: 'sm',
         keyboard: true,
         ariaLabelledBy: 'modal-basic-title'
      }).result.then((result) => {
         if (!this.service.chosenPredefinedCompany) {
            if (this.needToSetCondition) {
               this.service.destinationTableRowData.forEach(item => {
                  item.condition = this.selectedCondition;
               })
            }
         } else {
            this.rowDataForPredefinedImports = [];

            const rowCount = this.predefinedImportTemplatesService.gridApi.getDisplayedRowCount()
            for (let i = 0; i < rowCount; i++) {
               const rowNode = this.predefinedImportTemplatesService.gridApi.getDisplayedRowAtIndex(i);
               this.rowDataForPredefinedImports.push(rowNode.data);
            }

            if (this.needToSetCondition) {
               this.rowDataForPredefinedImports.forEach(item => {
                  item.condition = this.selectedCondition;
               })
            }
         }

         this.uploadVehiclesForValuation();
      }, (reason) => {
         //this.uploadVehiclesForValuation();
      });
   }

   downloadTemplate() {
      const url = 'assets/bulkUploadTemplate.xlsx';
      return this.http.get(url, {responseType: 'blob'}).subscribe((data: any) => {
         const blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
         const link = document.createElement('a');
         link.href = window.URL.createObjectURL(blob);
         link.download = 'bulkUploadTemplate.xlsx';
         link.click();
      })
   }

   openLocationsModal() {
      const modalRef = this.modalService.open(LocationsModalComponent);
      modalRef.result.then((result) => {
      });
   }

   public welcomMessageIfReportLoaded() {
      return `The table below details batch upload results. You can use the menu on the left to change the report.  To upload new vehicles click on the 'Upload a file for bulk valuation' button.`
   }

   public messageIfHaveChosenSpecificAuctionReport() {
      return `The table below shows the contents of the report.  Please use the filters to select the rows you would like to bulk upload, then choose the 'Upload' button when you are done.`
   }

   public chooseFileMessage() {
      return `You now need to upload an excel file containing a list of vehicles to value.   For each vehicle, the file should show the Reg, mileage and condition.   If you have an excel file click on the 'Choose File' button above.  If you need a template, click on the 'Download Template' button.`
   }

   public introMessage() {
      return `The table below shows the excel file contents.  Either load a previous import map using the button above or continue with steps 1 through 3 below.  Once you have done this, click on 'Save import map' if you are going to be importing a file like this again.`
   }

   public step1Message() {
      return '1. Use the plus and minus buttons below to choose how many rows in the source excel file are header rows. Spark will skip over these when finding vehicles to import.'
   }

   public columnLetterPickMessage() {
      return '2. Use the filter icons to remove any unwanted rows for example blank rows at the end of the file, if required.'
   }

   public bottomTableMessage() {
      return `3. Drag columns (e.g. 'A') from the grid above and drop them onto the relevant column header in the grid below to map Reg and Mileage.   Click the button below to choose to map other optional columns.  When you are happy, click on 'Upload' to submit the file for valuation.`
   }

   public chooseImportMapMessage() {
      return `The table below represents the file contents.  Please choose an existing or new Import Map using the 'Choose Import Map' button above`
   }


   getVehicleValuationBatches({single}: { single?: boolean } = {single: false}) {

      this.fetchingBatches = true;
      this.singleMode = single;

      this.apiAccessService.get('api/AutoPrice', 'GetVehicleValuationBatches', [{
         key: 'single',
         value: single
      }]).subscribe((res: VehicleValutionBatch[]) => {

         this.service.vehicleValuationBatches = res.sort((a, b) => b.Id - a.Id);

         const vehicleValuationBatches = this.service.vehicleValuationBatches
            .filter(r => r.IsSingle === false);

         // What sites does the current user have access to ?
         const userSites = this.selections.userSites.map(s => s.SiteId);

         // Fetch single valuations run by users in sites the current user has access to
         const vehicleValuationSingles = this.service.vehicleValuationBatches
            .filter(r => r.IsSingle === true)
            .filter(r => userSites.includes(r.LastRunBySiteId));

         if (single) {
            this.service.chosenVehicleValuationBatch = vehicleValuationSingles[0];
         } else {
            this.service.chosenVehicleValuationBatch = vehicleValuationBatches[0];
         }


         if (this.service.vehicleValuationBatches && this.service.vehicleValuationBatches?.length >= 0) {

            let batchIds = '';

            if (single) {
               batchIds = vehicleValuationSingles.map(s => s.Id).join(',');
            } else {
               batchIds = this.service.chosenVehicleValuationBatch?.Id.toString();
            }

            if (batchIds?.length > 0) {
               this.getVehicleValuationRows(batchIds, this.service.chosenVehicleValuationBatch.IsApplyPriceScenarios);
            } else {
               this.service.batchResultsRowData = [];
            }

            this.fetchingBatches = false;
            this.selections.triggerSpinner.next({show: false});

         } else {
            this.fetchingBatches = false;
            this.selections.triggerSpinner.next({show: false});
         }
      }, (error: any) => {
         this.fetchingBatches = false;
         console.error('Failed to retrieve vehicle valuation batches', error);
         this.selections.triggerSpinner.next({show: false});
      });
   }

   getVehicleValuationRows(batchIds: string, isApplyPriceScenarios: boolean) {

      const params = {
         batchIds,
         // We apply filtering after
         // onlyShowBestLocation: this.service.limitToBest,
         onlyShowBestLocation: false,
         IsApplyPriceScenarios: isApplyPriceScenarios
      };

      this.apiAccessService.post('api/AutoPrice', 'GetVehicleValuationRows', params).subscribe((res: ValuationBatchResult[]) => {

         this.service.batchResultsRowData = res;

         this.addIsBestFlagToRowData(res);



         if (this.service.batchResultsTableComponent) {
            this.service.batchResultsTableComponent.redrawTable();
         }

         this.selections.triggerSpinner.next({show: false});
      }, (error: any) => {
         console.error('Failed to retrieve vehicle valuation batches', error);
         this.selections.triggerSpinner.next({show: false});
      });
   }

   public addIsBestFlagToRowData(rowData: ValuationBatchResult[]) {
      rowData.forEach(ad => ad.isBestMove = false);

      const reducedObject = rowData.reduce((acc, curr) => {
         if (!acc[curr.Vin] || curr.StrategyPrice > acc[curr.Vin].StrategyPrice) {
            acc[curr.Vin] = curr;
         }
         return acc;
      }, {});

      const highestBenefitPerVehicle: ValuationBatchResult[] = Object.values(reducedObject);
      highestBenefitPerVehicle.map(ad => ad.isBestMove = true);
      return rowData;
   }

   // getSingleValuationBatchResults() {

   // }

   goToImport() {
      this.selections.triggerSpinner.emit({show: true, message: 'Loading...'});
      this.getImportMasks();
      //this.showUploadView = true;
      this.previousStage = this.stage;
      this.stage = 'doBulkUpload';
      //this.service.vehicleValuationBatches = null;
      //this.service.chosenVehicleValuationBatch = null;
      //this.service.batchResultsRowData = null;

   }

   exitImport() {
      this.resetImportSettings();
      this.stage = this.previousStage;
      this.getVehicleValuationBatches();
      //this.showUploadView = false;
   }

   chooseBatch(batch: VehicleValutionBatch) {


      this.selections.triggerSpinner.emit({show: true, message: 'Loading...'});
      this.service.chosenVehicleValuationBatch = batch;
      this.getVehicleValuationRows(batch.Id.toString(), batch.IsApplyPriceScenarios);
   }

   // chooseSingle(batch: VehicleValutionBatch) {
   //   this.showModal(batch.Id);
   // }

   goToExisting(single: boolean) {
      this.selections.triggerSpinner.emit({show: true, message: 'Loading...'});
      this.getTableStateLabels();
      this.getVehicleValuationBatches({single});
      this.stage = 'viewExistingBatch';
   }

   backToStart() {
      if (this.stage === 'doBulkUpload') {
         this.resetImportSettings();
      }

      if (this.stage === 'fileChosen') {
         this.stage = 'doBulkUpload';
         this.predefinedImportTemplatesService.rowData = null;
      } else {
         this.stage = 'start';
      }
   }


   pickTypeMessage() {
      return `Choose the type of file you would like to upload or 'Other' if your file type is not shown.   If you would like a particular source file adding to the list below please email <NAME_EMAIL>`
   }

   showVehicleParamsModal() {
      this.newVehicleReg = null;
      this.newVehicleMileage = null;
      this.newVehicleCondition = 'Excellent';
      this.newVehicleColour = null;

      this.modalService.open(this.setVehicleParamsModal, {
         size: 'sm',
         keyboard: true,
         ariaLabelledBy: 'modal-basic-title'
      }).result.then((result) => {
         // Only show the modal if it was closed with the OK button or Enter key
         // and not dismissed with the Cancel button or X
         this.showModal(null);
      }, (_reason) => {
         // Do nothing when modal is dismissed
      });
   }

   showNewVehicleLoadNodal() {
      const modalRef = this.modalService.open(ChooseNewVehicleModalComponent, {
         size: 'xl',
         keyboard: true,
         ariaLabelledBy: 'modal-basic-title'
      });
      const modalInstance: ChooseNewVehicleModalComponent = modalRef.componentInstance;
      //modalInstance.loadFacets();

      modalRef.result.then(
         //closed
         (chosenDerivative: AtDerivativeItem) => {
            //'okd'

            this.loadUpNewVehicleModal(chosenDerivative)

         },
         reason => {
            //cancelled
         }
      );
   }


   public handleEnter(modal: any): void {
      if (this.newVehicleReg && this.newVehicleMileage) {
         // When Enter is pressed, directly show the modal
         // This is needed because modal.close() doesn't seem to trigger the result handler properly
         modal.dismiss('enter-key');
         // Add a small delay to ensure the modal is fully dismissed before showing the new one
         setTimeout(() => {
            this.showModal(null);
         }, 50);
      }
   }


   async loadUpNewVehicleModal(chosenDerivative: AtDerivativeItem) {

      this.selections.triggerSpinner.emit({show: true, message: 'Loading...'});
      this.newVehicleValuationModalService.chosenDerivative = chosenDerivative;
      const parms: GetNewVehicleModalParams = {
         derivativeId: this.newVehicleValuationModalService.chosenDerivative.derivativeId
      }
      try {
         const data = await this.getDataService.getNewValuationModal(parms);
         this.newVehicleValuationModalService.modalData = new NewVehicleValuationModal(data);
         this.setupCompetitorAnalysis(this.newVehicleValuationModalService.modalData);

         this.selections.triggerSpinner.emit({show: false});
      } catch (error) {
         console.log(error);
         this.selections.triggerSpinner.emit({show: false});
      }

      const newVehicleModal = this.modalService.open(AutoPriceNewVehicleValuationModalComponent, {
         size: 'lg',
         keyboard: true,
         ariaLabelledBy: 'modal-basic-title'
      });
      const newVehicleModalRef: AutoPriceNewVehicleValuationModalComponent = newVehicleModal.componentInstance;
   }

   setupCompetitorAnalysis(newVehicleValuationModal: NewVehicleValuationModal) {
      const lastYear = new Date();
      lastYear.setFullYear(new Date().getFullYear() - 1);

      const competitorAnalysisParams: CompetitorAnalysisParams = {

         Make: newVehicleValuationModal.VehicleInformation.Make,
         Model: newVehicleValuationModal.VehicleInformation.Model,
         Trim: newVehicleValuationModal.VehicleInformation.Trim,
         TransmissionType: newVehicleValuationModal.VehicleInformation.Transmission,
         FuelType: newVehicleValuationModal.VehicleInformation.FuelType,
         BodyType: newVehicleValuationModal.VehicleInformation.BodyType,
         Drivetrain: newVehicleValuationModal.VehicleInformation.Drivetrain,
         Doors: parseInt(newVehicleValuationModal.VehicleInformation.Doors),

         //plus always these:
         VehicleReg: '',
         OdometerReading: 0,
         FirstRegisteredDate: lastYear,
         AdvertisedPrice: 0,
         PricePosition: 0,
         Valuation: 0,

         RetailerSiteRetailerId: this.constants.RetailerSites[0].RetailerId,
         CompetitorSummary: newVehicleValuationModal.CompetitorCheckResult,
         ParentType: 'valuationModal',
         ImageURL: null,
         // VehicleValuationService: this
      }

      this.competitorAnalysisService.params = competitorAnalysisParams;
   }

   loadTest() {
      this.newVehicleReg = 'AJ65UDT';
      this.newVehicleCondition = 'Excellent';
      this.newVehicleMileage = 35000;
      this.newVehicleColour = 'Blue';
      this.showModal(null);
   }
}
