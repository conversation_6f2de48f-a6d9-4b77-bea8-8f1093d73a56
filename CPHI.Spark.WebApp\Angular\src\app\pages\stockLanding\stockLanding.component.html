<nav class="navbar">

  <nav class="generic" >
    <h4 id="pageTitle">
      <div>
        {{constants.translatedText.StockLanding_Title}}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Stocks"></sourceDataUpdate>

      </div>
    </h4>




  </nav>

  <nav class="pageSpecific" >



  </nav>
</nav>



<!-- Main Page -->
<div *ngIf="!stockLandings" id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">
    <tbody>
      <h3>No stock landings returned.</h3>
    </tbody>
  </div>
</div>

<div *ngIf="stockLandings" id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">


    <table>
      <tbody>
        <tr>
          <!-- top left -->
          <td>
            <div class="panelHolder">
              <stockLandingPanel *ngIf="!!newCarDataSet" [dataSet]="newCarDataSet"></stockLandingPanel>
            </div>
          </td>

          <!-- top right -->
          <td>
            <div class="panelHolder">
              <stockLandingPanel *ngIf="!!usedCarDataSet"  [dataSet]="usedCarDataSet"></stockLandingPanel>
            </div>
          </td>
        </tr>

        <tr>
          <!-- bottom left -->
          <td>
            <div class="panelHolder">
              <stockLandingPanel *ngIf="!!partsDataSet"  [dataSet]="partsDataSet"></stockLandingPanel>
            </div>
          </td>

          <!-- bottom right -->
          <td>
            <div class="panelHolder">
              <stockLandingPanel *ngIf="!!otherDataSet"  [dataSet]="otherDataSet"></stockLandingPanel>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <div id="stockLanding">
      {{constants.translatedText.Total}}: {{totalActual|cph:'currencyK':0:false}} which is 
      <ng-container *ngIf="totalVs>0"> <span class="badFont bold">{{totalVs|cph:'currencyK':0:false}}</span> above target.</ng-container>
      <ng-container *ngIf="totalVs<0"><span class="goodFont bold"> {{totalVs*-1|cph:'currencyK':0:false}} </span> below target.</ng-container>
      <ng-container *ngIf="totalVs==0">in-line with target.</ng-container>  
    </div>




  </div>
</div>