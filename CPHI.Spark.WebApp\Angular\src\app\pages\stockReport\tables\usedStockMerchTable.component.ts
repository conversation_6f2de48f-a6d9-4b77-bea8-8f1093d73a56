import { Component, Input, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CellClickedEvent, ColDef, DomLayoutType } from 'ag-grid-community';
import { StockReportModalComponent } from 'src/app/components/stockReportModal/stockReportModal.component';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { StockModalRow, StockModalRowWithNext30 } from 'src/app/model/sales.model';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../../cph.pipe';
import { AGGridMethodsService } from '../../../services/agGridMethods.service';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from '../../../services/selections.service';
import { BarsOnRRGSiteComponent } from '../../../_cellRenderers/barsOnRRGSite.component';
import { BarsPreppedComponent } from '../../../_cellRenderers/barsPrepped.component';
import { StockReportService } from '../stockReport.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';


@Component({
  selector: 'usedStockMerchTable',
  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>
    
    <!-- [domLayout]="domLayout" -->
    <ag-grid-angular  class="ag-theme-balham" id="UsedMerchStockReportTable"
    [gridOptions]="mainTableGridOptions"
   
      
      (gridReady)="onGridReady($event)"
     
      > 
      
    </ag-grid-angular>
    </div>
    <div *ngIf="!isRegionalTable" class="tableSpacer"></div>
    `
  ,
  styleUrls: ['./../../../../styles/components/_agGrid.scss'],
  styles: [
    `
    ag-grid-angular {
      max-width: 1927px;
    }
  `
  ]
})



export class UsedStockMerchTableComponent implements OnInit {

  @Input() public showUsedCols: boolean;
  @Input() public isRegionalTable: boolean;
  showGrid = false;
  public gridApi;
  public gridColumnApi;

  mainTableGridOptions: GridOptionsCph

  gridApiColumnDefinitions: any;

  flipCols: Array<string>;

  domLayout: DomLayoutType;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public modalService: NgbModal,
    public agGridMethods: AGGridMethodsService,
    public getData: GetDataMethodsService,
    public service: StockReportService,
  ) {
  }

  ngOnDestroy() { }


  ngOnInit() {

    this.initParams();

    this.service.filterUpdated.subscribe(value => {

      this.setRowData();
      
    });

  }


  initParams() {

    this.domLayout= "autoHeight";

    this.flipCols = ['ExDemoOverage', 'ExManagementOverage', 'TacticalOverage', 'CoreUsedOverage']

    this.mainTableGridOptions = {
      getMainMenuItems:(params)=>this.agGridMethods.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlightsMerch),
      getContextMenuItems: (params) => this.agGridMethods.getContextMenuItems(params),
      domLayout: this.domLayout,
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      onCellDoubleClicked: (params) => {
        this.onCellDblClick(params);
      },
      getRowHeight: (params) => {
        return params.node.rowPinned == "bottom" ? 40 : 25
      },
      onFirstDataRendered:()=>{this.showGrid = true;this.selections.triggerSpinner.next({ show: false });},
      rowData: this.provideRowData(),
      pinnedBottomRowData: this.service.usedMerchRowData ? this.service.usedMerchRowData.filter(x=>x.Label == 'Total') : null,
      
      animateRows: true,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.agGridMethods.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsMerch),
      },
      getRowClass: (params) => {
        if (params.data.Label == 'Total') {
          return 'total';
        }
      },

      columnDefs: [
        { headerName: "", children: [
          //site name
          { headerName: this.constants.translatedText.Site, field: 'Label', colId: 'Site', width: 80, type: 'label' },

          { headerName: 'Total Used Stock', field: 'TotalUsedStock', colId: 'TotalUsed', width: 40, type: 'number',cellClass: 'ag-right-aligned-cell',  },

          // //Listed On RRG
          { headerName: 'Used Vehicles Listed on RRG Site', suppressSizeToFit: true, field: 'ListedOnSite', colId: 'ListedOnSiteBar', 
          cellRenderer: BarsOnRRGSiteComponent, width: 300, type: 'special' },

          {
            headerName: 'UnListed Vehicles By Age', children: [
              { headerName: '<7', field: 'UnListedUpTo7', colId: 'UnListedUpTo7', width: 25, type: 'number', cellClass: 'ag-right-aligned-cell', },
              { headerName: '<14', field: 'UnListedOver7UpTo14', colId: 'UnListedOver7UpTo14', width: 25, type: 'number', cellClass: 'ag-right-aligned-cell', },
              { headerName: '14+', field: 'UnlistedOver14', colId: 'UnlistedOver14', width: 25, type: 'number', cellClass: 'ag-right-aligned-cell', },
            ]

            
          },

          //Prepped
          { headerName: 'Used Vehicles With Prep Costs (UVQ,UVN,UMQ only)', suppressSizeToFit: true, field: 'UnPreppedUpTo7', colId: 'UnPreppedBar', 
          cellRenderer: BarsPreppedComponent, width: 300, type: 'special' },

          {
            headerName: 'No Prep Cost Vehicles By Age', children: [
              { headerName: '<7', field: 'UnPreppedUpTo7',  colId: 'UnPreppedUpTo7', width: 25, type: 'number', cellClass: 'ag-right-aligned-cell', },
              { headerName: '7+', field: 'UnPreppedOver7', colId: 'UnPreppedOver7', width: 25, type: 'number', cellClass: 'ag-right-aligned-cell', },
            ]

            
          },
        ]}

      ]


    }


  }



  provideRowData() {
    return this.isRegionalTable ? this.service.usedMerchRowData.filter(x=>x.IsRegion) : this.service.usedMerchRowData.filter(x=>x.IsSite)
  }


  setRowData()
  {
    if(this.gridApi)
    {

      this.gridApi.setPinnedBottomRowData(this.service.usedMerchRowData.filter(x=>x.Label == 'Total'));

      this.gridApi.setRowData(this.provideRowData());
      
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();
    // this.selections.triggerSpinner.next({ show: false });

  }



  highlightTopBottom(colDef: any, topBottomN: number) {
    this.agGridMethods.highlightTopBottom(colDef, topBottomN, this.flipCols, this.gridApi)
  }

  onCellDblClick(params: CellClickedEvent, skipProcessingGoingOver?: boolean) {
    if(params.colDef.colId==='ListedOnSiteBar'){return;}
    if(params.colDef.colId==='UnPreppedBar'){return;}


    let noClickBars:string[] = [
      'SplitByType',
      'Site',
      'VehiclesOnRRGSiteBar',
      'VehiclesWithPrepCostBar',
      'UnPrepped'
    ]
    
    if (noClickBars.includes(params.colDef.colId)) return;

    let siteIds: number[] = [];

    if(params.data.IsRegion || params.data.IsTotal)
    {
      siteIds = this.constants.getSiteIdsForRegion(params.data.Label);
    }
    else 
    {
      siteIds.push(params.data.SiteId);
    }


    let chosenReport:string = params.colDef.colId;


    if(chosenReport == "TotalUsed"){

      this.getData.getStockModalRows(chosenReport, 'now', siteIds.toString(), 0, false, this.selections.stockReport.franchises.toString()).subscribe((res:StockModalRowWithNext30) => {

        setTimeout(() => {
          this.selections.triggerSpinner.next({show:false});
        }, 20)
  
        if (params.colDef.colId == 'SplitByType') return
        if (params.colDef.colId.indexOf('_') > -1) { skipProcessingGoingOver = true }
    
        let header = params.data.Label + ': ' + (res.AgedNow.length) + ' ' + params.colDef.headerName + ' vehicles';
        if (this.selections.stockReport.report.name == 'Stock Ageing') { header = header + ' over ' + this.selections.stockReport.ageingOption.description + ' as at ' + this.selections.stockReport.asAt.description }
    
        let showUsedColumns = ['CoreUsed', 'Tactical', 'ExManagement', 'ExDemo'].includes(params.colDef.headerName);
        this.selections.initiateStockReportModal(skipProcessingGoingOver, res.AgedNow, res.AgedIn30, header, showUsedColumns, params.data.Label);
        //open modal
    
        const modalRef = this.modalService.open(StockReportModalComponent, { keyboard: true, size: 'lg' });
        modalRef.result.then((result) => { //I get back from modal
          if (result) {
    
          }
        });


  
      }, error => {
  
        console.error("ERROR: ", error);
  
      }, () => {
  
       
  
      });

    }
    else{
      
      this.getData.getUsedMerchStockModalRows(chosenReport, siteIds.toString()).subscribe((res:StockModalRow[]) => {
  
        setTimeout(() => {
          this.selections.triggerSpinner.next({show:false});
        }, 20)
  
        if (params.colDef.colId == 'SplitByType') return
        if (params.colDef.colId.indexOf('_') > -1) { skipProcessingGoingOver = true }
    
        let header = params.data.Label + ': ' + res.length + ' ' + params.colDef.headerName + ' vehicles';
        if (this.selections.stockReport.report.name == 'Stock Ageing') { header = header + ' over ' + this.selections.stockReport.ageingOption.description + ' as at ' + this.selections.stockReport.asAt.description }
    
        let showUsedColumns = ['CoreUsed', 'Tactical', 'ExManagement', 'ExDemo'].includes(params.colDef.headerName);
        this.selections.initiateStockReportModal(skipProcessingGoingOver, res, [], header, showUsedColumns, params.data.Label);
    
        const modalRef = this.modalService.open(StockReportModalComponent, { keyboard: true, size: 'lg' });
        modalRef.result.then((result) => { //I get back from modal
          if (result) {
    
          }
        });

  
      }, error => {
  
        console.error("ERROR: ", error);
  
      }, () => {
  
       
  
      });
    }

  }


  showAgeingColumns(params) {
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[0].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[1].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[2].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[3].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[4].name, true)
    this.resizeGrid();
  }


  resizeGrid() {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  clearHighlighting(colDef:ColDef) {
    this.agGridMethods.clearHighlighting(colDef, this.gridApi);
    
  }



  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Used Stock Merchandising');
  }






}
