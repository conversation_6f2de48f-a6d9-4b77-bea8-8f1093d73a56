﻿using System;

namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class EventPaymentModel
    {
        public string Id { get; set; }

        public string? Reference { get; set; }

        public string? PaymentMethod { get; set; }

        public decimal Amount { get; set; }

        public string? Currency { get; set; }

        public bool Refund { get; set; }

        public DateTime Created { get; set; }
        
        public string? Hash { get; set; }
        
        public bool Deposit { get; set; }
        
        public bool External { get; set; }
    }
}
