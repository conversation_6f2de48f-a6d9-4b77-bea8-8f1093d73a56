﻿using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class CompetitorSearchUserChoices
   {
      
      public int MinPlate { get; set; }
      public int MaxPlate { get; set; }
      public int Radius { get; set; }
      public string Postcode { get; set; }

      public List<string> BodyType { get; set; }
      public List<string> FuelType { get; set; }
      public List<string> TransmissionType { get; set; }
      public List<string> Trim { get; set; }
      public List<string> Drivetrain { get; set; }
      public List<int> Doors { get; set; }

   }
}
