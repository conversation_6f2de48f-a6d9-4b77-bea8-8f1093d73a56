<div class="d-inline-block" ngbDropdown dropright>
  <button class="btn btn-primary centreButton" ngbDropdownToggle (click)="makeMonths()">
      {{ getButtonLabel() }}
  </button>
  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

      <div class="scrollSection"> 
        <button
            *ngFor="let month of months; index as i"
            [ngClass]="{ 'active': month.isSelected, 'buttonToScrollTo': i+1 === months.length }"
            ngbDropdownItem
            (click)="selectMonth(month)"
        >
            {{ month.startDate | cph:'month': 0 }}
        </button>
      </div>

      <div class="fixedSection">

        <hr class="solid">

        <button class="quickSelect" *ngIf="includeYTD" (click)="selectYTD()"
            ngbDropdownItem 
        >
        {{ constants.translatedText.YTD }}
        </button>

        <button class="quickSelect" *ngIf="includeLastYear" (click)="selectLastYear()"
            ngbDropdownItem
        >
          {{ constants.translatedText.LastYear }}
        </button>

        <button class="quickSelect" *ngIf="includeThisYear" (click)="selectThisYear()"
            ngbDropdownItem
        >
          {{ constants.translatedText.ThisYear }}
        </button>

        <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="confirmSelection()">
                {{ constants.translatedText.OKUpper }}
            </button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>
                {{ constants.translatedText.Cancel }}
            </button>
        </div>

      </div>

  </div>
</div>