<nav class="navbar">

  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        Q3 New Car Head to Head
        <sourceDataUpdate [chosenDate]="service.constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <div class="buttonGroup topDropdownButtons">

    </div>



  </nav>

  <nav class="pageSpecific">


    <button (click)="refresh()" class="btn btn-primary">
      {{service.constants.translatedText.Refresh}}
    </button>

  </nav>
</nav>



<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new superCupBackground" >

    <div class="content-inner-new" *ngIf="service.matches">



      <!-- ******************************************************** -->
      <!-- The various matches -->
      <!-- ******************************************************** -->
      <div id="matchesContainer">

       


        <!-- Used cup -->
          <div id="header" class="boxShadow dark">
            <!-- <div class="image left">
              <img src="./assets/imgs/renaultCaptur.png">
            </div> -->
            <div class="headline">
              <div class="words">Q3 New Car Super-Cup</div>
            </div>
            <!-- <div class="image right">
              <img src="./assets/imgs/renaultClio.png">
            </div> -->
            <div class="tagLine">
              <div class="words">Q3 LMT Order Rate vs Target</div>
            </div>
          </div>


        <div  id="matches">
          <div *ngFor="let match of service.matches" class="match-stacked">
            <div class="team">
              <div class="team-name">
                {{ match.HomeTeam.Label }}
              </div>
              <div class="score">
                {{ match.HomeTeam.Score | cph:'percent':0 }}
              </div>
              <div *ngIf="match.HomeTeam.Score > match.AwayTeam.Score" class="winner">
                <i class="fa fa-trophy-alt"></i>
              </div>
            </div>
            <div class="team">
              <div class="team-name">
                {{ match.AwayTeam.Label }}
              </div>
              <div class="score">
                {{ match.AwayTeam.Score | cph:'percent':0 }}
              </div>
              <div *ngIf="match.HomeTeam.Score < match.AwayTeam.Score" class="winner">
                <i class="fa fa-trophy-alt"></i>
              </div>
            </div>
          </div>
        </div>

        <ng-template #newMatches>
          
        </ng-template>

          <!-- Total Row at bottom -->
          <!-- <div class="match" id="totalRow">
            <div class="homeTeam team">
              <div class="teamInner">
                <div class="score">{{service.totalHomeTeamScore | cph:'percent':0}}</div>
              </div>
            </div>

            <div class="awayTeam team">

              <div class="teamInner">
                <div class="score">{{service.totalAwayTeamScore | cph:'percent':0}}</div>
              </div>
            </div>
          </div> -->



      <!-- ******************************************************** -->
      <!-- the players for the chosen match -->
      <!-- ******************************************************** -->
      <div id="matchContainer" *ngIf="service.chosenMatch">

        <div id="confettiHolder">
          <confetti></confetti>
        </div>

        <button id="backButton" class="btn btn-primary" *ngIf="service.chosenMatch" (click)="onUnChooseMatchClick()"><i
            class="fas fa-undo"></i></button>

        <div class="match boxShadow">
          <div class="homeTeam team">
            <div class="teamInner">
              <div class="managerMoney pulsing boxShadow"
                *ngIf="service.chosenMatch.HomeTeam.Score>service.chosenMatch.AwayTeam.Score">£
                500</div>
              <div class="name"> {{service.chosenMatch.HomeTeam.Label}} </div>
              <div class="score">{{service.chosenMatch.HomeTeam.Score}}</div>
            </div>
          </div>

          <div class="awayTeam team">

            <div class="teamInner">
              <div class="managerMoney pulsing boxShadow"
                *ngIf="service.chosenMatch.HomeTeam.Score < service.chosenMatch.AwayTeam.Score">£
                500</div>
              <div class="name"> {{service.chosenMatch.AwayTeam.Label}} </div>
              <div class="score">{{service.chosenMatch.AwayTeam.Score}}</div>
            </div>
          </div>
        </div>

        <div id="peopleContainersArea">


          <div id="homeContainer" class="teamContainer">
            <div class="teamContainerInner">

              <div class="chipHolder" *ngFor="let person of service.chosenMatchHomeTeamPeople; let i = index"
                [ngClass]="{'ownRow':ownRow(i)}">
                <!-- The actual chip for the person -->
                <div class="personChip" [ngClass]="{'fullSalesman':person.IsActuallyASalesman}">
                  <div class="personChipInner">

                    <profilePicImage [personId]="person.Person.Id" [size]="profilePicSize"></profilePicImage>
                    <div class="name">
                      <div class="actualName">{{person.Person.Name}}</div>
                      <div [ngClass]="{'topUp':service.chosenMatch.HomeTeam.TopUp}" *ngIf="person.IsActuallyASalesman"
                        class="payoutCalc">
                        <!-- No topup -->
                        <div class="payoutRow" *ngIf="!service.chosenMatch.HomeTeam.TopUp">
                          <div class="calcBit">
                            £25 x {{person.Score}} =
                          </div>
                          <div class="normalPayout">{{25 * person.Score|cph:'currency':0}}</div>
                        </div>
                        <!-- Topup -->
                        <div class="payoutRow" *ngIf="service.chosenMatch.HomeTeam.TopUp">
                          <div class="calcBit">
                            £50 x {{person.Score}} =
                          </div>
                          <div class="superPayout">{{50 * person.Score|cph:'currency':0}}</div>
                        </div>
                      </div>
                    </div>
                    <div class="score">
                      <span *ngIf="person.Deals.length>0">
                        <dealListPopover [deals]="person.Deals" [score]="person.Score"></dealListPopover>
                      </span>
                      <span *ngIf="person.Deals.length===0">
                        {{person.Score}}
                      </span>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>



          <div id="awayContainer" class="teamContainer">
            <div class="teamContainerInner">

              <div class="chipHolder" *ngFor="let person of service.chosenMatchAwayTeamPeople; let i = index"
                [ngClass]="{'ownRow':ownRow(i)}">
                <!-- The actual chip for the person -->
                <div class="personChip" [ngClass]="{'fullSalesman':person.IsActuallyASalesman}">
                  <div class="personChipInner">

                    <profilePicImage [personId]="person.Person.Id" [size]="profilePicSize"></profilePicImage>
                    <div class="name">
                      <div class="actualName">{{person.Person.Name}}</div>
                      <div [ngClass]="{'topUp':service.chosenMatch.AwayTeam.TopUp}" *ngIf="person.IsActuallyASalesman"
                        class="payoutCalc">
                        <!-- No topup -->
                        <div class="payoutRow" *ngIf="!service.chosenMatch.AwayTeam.TopUp">
                          <div class="calcBit">
                            £25 x {{person.Score}} =
                          </div>
                          <div class="normalPayout">{{25 * person.Score|cph:'currency':0}}</div>
                        </div>
                        <!-- Topup -->
                        <div class="payoutRow" *ngIf="service.chosenMatch.AwayTeam.TopUp">
                          <div class="calcBit">
                            £50 x {{person.Score}} =
                          </div>
                          <div class="superPayout">{{50 * person.Score|cph:'currency':0}}</div>
                        </div>
                      </div>
                    </div>
                    <div class="score">
                      <span *ngIf="person.Deals.length>0">
                        <dealListPopover [deals]="person.Deals" [score]="person.Score"></dealListPopover>
                      </span>
                      <span *ngIf="person.Deals.length===0">
                        {{person.Score}}
                      </span>

                    </div>
                  </div>
                </div>
              </div>





            </div>
          </div>

        </div>

      </div>


    </div>
  </div>
</div>