﻿using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class QuotationEventProfit
    {
        public decimal MarginsTotal { get; set; }
        public decimal VapsTotal { get; set; }
        public decimal ExtrasTotal { get; set; }
        public decimal OptionsTotal { get; set; }
        public decimal UsedMarginTotal { get; set; }
        public decimal VehicleSubTotal { get; set; }
        public decimal Total { get; set; }
        public bool Reconciled { get; set; }
        public EventUsedVehicleMargin? UsedVehicleMargin { get; set; }
        public List<EventProfitMargin> Margins { get; set; }
        public List<EventProfitMargin> AddedValueProductMargins { get; set; }
        public List<EventProfitMargin> ExtraProductMargins { get; set; }
    }
}