import { Component, Input, OnInit } from '@angular/core';
import { ColDef, DomLayoutType, GridOptions, ICellRendererParams, LineSparklineOptions } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { CustomHeaderService } from 'src/app/components/customHeader/customHeader.service';
import { CphPipe } from 'src/app/cph.pipe';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { AGGridMethodsService } from '../../../services/agGridMethods.service';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from '../../../services/selections.service';
import { TeleStatRowRenderer } from '../teleStatRowRenderer/teleStatRowRenderer.component';
import { TeleStatsService } from '../teleStats.service';


@Component({
  selector: 'teleStatsTable',
  styleUrls: ['./teleStatsTable.component.scss'],
  templateUrl:'./teleStatsTable.component.html',
  
})



export class TeleStatsTableComponent implements OnInit {

  @Input() public isSiteTable: boolean;
  
  showGrid = false;

  public gridApi;
  public gridColumnApi;

  gridOptions: GridOptions

  gridApiColumnDefinitions: any;

  domLayout: DomLayoutType;

  subscription: Subscription;

  public components: {
    [p: string]: any;
  } = {
      agColumnHeader: CustomHeaderNew,
    };

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public agGridMethods: AGGridMethodsService,
    public getData: GetDataMethodsService,
    public service: TeleStatsService,
    private customHeader: CustomHeaderService,

  ) {


  }

  ngOnDestroy() { 
    if(!!this.subscription){this.subscription.unsubscribe();}
  }


  ngOnInit() {

    // Set table params
    this.selections.triggerSpinner.next({show:true});

    this.initParams();

    this.setRowData();

  }


  initParams() {
    this.domLayout= "autoHeight";
    this.service.fourPriorWeeks = this.createWeeks(this.service.selectedWeek);
    this.setGridOptions();
  }

  setGridOptions() 
  {
    this.gridOptions = {
      getContextMenuItems: (params) => this.agGridMethods.getContextMenuItems(params),
      // domLayout: this.domLayout,
      components: {
        customGroupCellRenderer: TeleStatRowRenderer,
      },
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      autoGroupColumnDef: {
        headerName: '',
        cellRenderer: 'customGroupCellRenderer',
      },
      rowClassRules: {
        'hide-row': function(params) {
          return (params.node.key == 'All Lines' || params.node.key == 'All Departments' || params.node.key == 'All Sites');
        },
      },
      getRowHeight: (params) => {
        return this.setRowHeight(params);
      },
      onRowGroupOpened: (params) => this.onRowGroupOpened(params),
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
      rowData: this.provideRowData(),
      pinnedBottomRowData: this.providePinnedRowData(),
      animateRows: true,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        //floatingFilter: true,
        filterParams: { applyButton: false, clearButton: true, applyMiniFilterWhileTyping: true, cellHeight: this.agGridMethods.getFilterListItemHeight() },
        cellClass: 'agAlignCentreVertically',
        headerComponentParams: { showPinAndRemoveOptions: false },
        autoHeaderHeight: true
      },
      columnTypes: this.getColTypes(),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      columnDefs: this.service.provideColDefs(this.isSiteTable)
    }
  }


  provideRowData(){
    return this.service.provideRowData(this.isSiteTable);
  }

  getColTypes(): any{
    return this.columnTypeService.provideColTypes([]);
  }

  setRowHeight(params): number {

    if(params.node.rowPinned == "bottom")
    { 
      return 40;
    }
    else if(params.node.key == 'All Lines' || params.node.key == 'All Departments' || params.node.key == 'All Sites')
    {
      return 0;
    }

    return 25;
  }

  onRowGroupOpened(event): void {
    let groupLevel = event.node.level; // `level` is 0-based

    if (groupLevel === 2 && this.isSiteTable) { // Adjust this based on the level you want to restrict
      if (event.node.expanded) {
        setTimeout(() => event.node.setExpanded(false), 0); // Defer closing the group to avoid Angular errors
      }
    }

    if (groupLevel === 3 && !this.isSiteTable) { 
      if (event.node.expanded) {
        setTimeout(() => event.node.setExpanded(false), 0); 
      }
    }

    this.gridApi.sizeColumnsToFit();
  }

  createWeeks(weekStart): Date[] {
    var fourPriorWeeks = [];
    for (var i = 1; i <= 4; i++) {
        var previousWeek = new Date(weekStart);
        previousWeek.setDate(weekStart.getDate() - 7 * i);
        fourPriorWeeks.push(previousWeek);
    }
    return fourPriorWeeks;
  }

  customCellRenderer(params) {
    if (params.data.IsDepartmentTotal) {
      // This cell belongs to a department total row
      // You can return custom content or style the cell differently
      return `<span class="department-total">${params.value}</span>`;
    } else {
      // Regular cell content
      return params.value;
    }
  }

  setRowData(): void
  {
    if(this.gridApi && this.service.rowData)
    {
      this.gridApi.setRowData(this.provideRowData())
      this.gridApi.setPinnedBottomRowData(this.service.rowData.filter(x=>x.IsTotal));
      this.gridApi.sizeColumnsToFit();
    }
  }




  private providePinnedRowData() {

    if(this.service.rowData) 
    {
      return this.service.rowData.filter(x=> x.IsTotal);
    }
    
    return;
  }


  onGridReady(params) {

    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridOptions.context = { thisComponent: this }; 

    this.subscription = this.service.newDataEmitter.subscribe(res => {

      this.service.fourPriorWeeks = this.createWeeks(this.service.selectedWeek);
      this.gridApi.setColumnDefs(this.service.provideColDefs(this.isSiteTable));
      this.gridApi.setRowData(this.provideRowData());
      this.gridApi.setPinnedBottomRowData(this.providePinnedRowData());
      this.gridApi.sizeColumnsToFit();
    })

    setTimeout(() => {
      this.gridApi.sizeColumnsToFit();

    }, 10)

    this.selections.triggerSpinner.next({ show: false });
  }


  resizeGrid() {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  clearHighlighting(colDef: ColDef) {
    this.agGridMethods.clearHighlighting(colDef, this.gridApi);

  }

  excelExport() {
    this.selections.triggerSpinner.next({ show:true });

    this.gridApi.setRowData(this.service.rowData.filter(x => x.Line != 'All Lines'))

    // Assuming you want to include hidden columns "Sites", "Lines", "Departments"
    const columnsToReveal = ['SiteName', 'Department', 'Line'];
    const columnsToHide = ['Last5'];

    // Temporarily show these columns
    columnsToReveal.forEach(colId => {
      // Corrected to use the appropriate Grid API method
      this.gridColumnApi.setColumnVisible(colId, true);
    });

    // Temporarily hide these columns
    columnsToHide.forEach(colId => {
      // Corrected to use the appropriate Grid API method
      this.gridColumnApi.setColumnVisible(colId, false);
    });

    this.gridApi.setRowData(this.service.rowData.filter(x => x.Line != 'All Lines For Department'))

    // Now get the updated table model
    let tableModel = this.gridApi.getModel();

    // Log or use tableModel as needed, for example:
    this.excel.createSheetObject(tableModel, 'Tele Stats');

    // Optionally, hide the columns again after the operation
    columnsToReveal.forEach(colId => {
      this.gridColumnApi.setColumnVisible(colId, false);
    });

    // Temporarily hide these columns
    columnsToHide.forEach(colId => {
      // Corrected to use the appropriate Grid API method
      this.gridColumnApi.setColumnVisible(colId, true);
    });

    this.gridApi.setRowData(this.provideRowData());

    this.selections.triggerSpinner.next({show:false});
    this.gridApi.setPinnedBottomRowData(this.providePinnedRowData());
  }


  public instructionRowMessage(){
    return 'Table below show telephone statistics for the most recent week. Answer rate is shown in green where it is above 70%. Abandon rate is shown in red when above 10%.  Click on a site to expand to department and line.'  
  }





}
