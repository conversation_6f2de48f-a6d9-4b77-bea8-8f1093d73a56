<nav class="navbar">

  <nav class="generic">
    <h4 id="pageTitle">
      <div>

        {{ constants.translatedText.Sales }} {{ constants.translatedText.Commission }}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <ng-container>

      <div class="d-flex">

        <div class="buttonGroup">
          <button class="btn btn-primary" [ngClass]="{ active: service.schemeToShow === 'Execs' }"
            (click)="changeView('Execs')">
            Sales Exec
          </button>
          <button class="btn btn-primary" [ngClass]="{ active: service.schemeToShow === 'LBDMs' }"
            (click)="changeView('LBDMs')">
            LBDMs
          </button>
          <button class="btn btn-primary" [ngClass]="{ active: service.schemeToShow === 'BMs' }"
            (click)="changeView('BMs')">
            Business Managers
          </button>
        </div>

        <!-- *ngIf="selections.user.permissions.CanUpdateSiteBusinessManagersTable" -->



        <!-- FOR SELECTING MONTH -->
        <div class="buttonGroup">
          <!-- previousMonth -->
          <button class="btn btn-primary" (click)="changeMonth(-1)"><i class="fas fa-caret-left"></i></button>

          <!-- dropdownMonth -->
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button (click)="makeMonths(0)" class="btn btn-primary centreButton"
              ngbDropdownToggle>{{service.chosenMonth|cph:'month':0}}</button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

              <!-- the ngFor buttons -->
              <button *ngFor="let month of months" (click)="selectMonth(month.startDate)"
                ngbDropdownItem>{{month.name}}</button>

            </div>
          </div>
          <!-- nextMonth -->
          <button class="btn btn-primary" (click)="changeMonth(1)"><i class="fas fa-caret-right"></i></button>
        </div>


      </div>




    </ng-container>



  </nav>

  <nav class="pageSpecific">


  </nav>
</nav>



<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">
    <div class="content-inner-new">


      <!-- If we are showing sales execs -->
      <div *ngIf="service.schemeToShow === 'Execs'" id="gridHolder">

        <!-- Sites Table -->
        <div class="tableContainer" *ngIf="!!service.siteRows && !service.chosenSite && !!service.showTables">
          <commissionSitesTableRRG id="salesCommission" [isRegionsTable]="false" [rowData]="service.siteRows">
          </commissionSitesTableRRG>
          <div class="tableSpacer"></div>
          <commissionSitesTableRRG [isRegionsTable]="true" [rowData]="service.siteRows"></commissionSitesTableRRG>
        </div>


        <!-- People Table -->
        <div class="tableContainer"
          *ngIf="!!service.peopleRows && !!service.chosenSite && !service.chosenPerson && !!service.showTables">
          <button class="btn btn-primary backButton" (click)="backToSites()">
            &lt; </button>
          <commissionSitesTableRRG [rowData]="service.peopleRows"></commissionSitesTableRRG>
        </div>


        <!-- Person Statement -->
        <ng-container *ngIf="!!service.commissionItems && !!service.chosenPerson && !!service.showTables">
          <div class="spaceBetween headerCard">

            <!-- Person image -->
            <div class="personImageHolder">
              <div class="personImage">
                <profilePicImage [personId]="service.chosenPerson.RowId" [size]="profilePicSize"></profilePicImage>
              </div>
            </div>

            <div>
              <!-- Person name -->
              <div class="personName">
                {{service.chosenPerson.Label}}
              </div>

              <div class="personSite">
                <!-- {{selections.salesCommission.selectedSiteName}}
                ({{selections.salesCommission.selectedExec.name}}) -->
              </div>

            </div>
          </div>

          <div class="tableContainer">

            <button class="btn btn-primary backButton" (click)="backToPeopleTable()">
              &lt; </button>
            <commissionStatementTableRRG></commissionStatementTableRRG>
          </div>
        </ng-container>

      </div>


      <!-- If we are showing Business Managers -->
      <businessManagersCommission *ngIf="service.schemeToShow === 'BMs' && service.businessManagerRows">
      </businessManagersCommission>


      <!-- If we are showing LBDMs -->
      <div *ngIf="service.schemeToShow === 'LBDMs' && service.lbdmRows">

          <!-- People Table -->
          <div class="tableContainer" *ngIf="!!service.lbdmRows && !service.chosenPerson && !!service.showTables">
            <commissionSitesTableRRG [rowData]="service.lbdmRows"></commissionSitesTableRRG>
          </div>

          <!-- Person Statement -->
          <ng-container *ngIf="!!service.commissionItems && !!service.chosenPerson && !!service.showTables">
            <div class="spaceBetween headerCard">

              <!-- Person image -->
              <div class="personImageHolder">
                <div class="personImage">
                  <profilePicImage [personId]="service.chosenPerson.RowId" [size]="profilePicSize"></profilePicImage>
                </div>
              </div>

              <div>
                <!-- Person name -->
                <div class="personName">
                  {{service.chosenPerson.Label}}
                </div>

                <div class="personSite">
                  <!-- {{selections.salesCommission.selectedSiteName}}
                  ({{selections.salesCommission.selectedExec.name}}) -->
                </div>

              </div>
            </div>

            <div class="tableContainer">

              <button class="btn btn-primary backButton" (click)="backToPeopleTable()">
                &lt; </button>
              <commissionStatementTableRRG></commissionStatementTableRRG>
            </div>
          </ng-container>

      </div>

    </div>


  </div>
</div>