import { Component, OnInit } from "@angular/core";
import { ServiceChannelSplits } from "src/app/model/main.model";
import { ServiceSummaryService } from "src/app/pages/serviceSummary/serviceSummary.service";
import { ConstantsService } from "src/app/services/constants.service";

@Component({
    selector: "serviceSalesByTypeTile",
    template: `
      <div class="dashboard-tile-inner">
        <div class="dashboard-tile-header">
          {{ constants.translatedText.Dashboard_SalesByType }}
        </div>
        <div class="dashboard-tile-body">
          <table class="cph">
            <thead>
              <tr>
                <th></th>
                <th></th>
                <th>{{ constants.translatedText.Hours }} / {{ constants.translatedText.Day }}</th>
                <th>{{ constants.translatedText.Dashboard_ServiceSales_SoldHours }}</th>
                <th>{{ constants.translatedText.Dashboard_ServiceSales_RecoveryRate }}</th>
                <th>{{ constants.translatedText.Turnover }}</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let row of data; let i = index">
                <tr
                  *ngIf="row.IsLabour || (!row.IsLabour && !constants.environment.serviceSalesDashboard_onlyLabour)"
                  [ngClass]="{ 'totalRow': row.IsTotal }"
                >
                  <td>
                    <i *ngIf="!row.IsTotal" [ngClass]="icons[row.Channel]" class="h3"></i>
                  </td>
                  <td>{{ row.Channel }}</td>
                  <td>{{ row.HoursPerDay | cph:'number':0 }}</td>
                  <td *ngIf="row.IsLabour || (!row.IsLabour && row.IsTotal)">{{ row.SoldHours | cph:'number':0 }}</td>
                  <td *ngIf="row.IsLabour || (!row.IsLabour && row.IsTotal)">{{ row.RecoveryRate | cph:'currency':2 }}
                  </td>
                  <td *ngIf="!row.IsLabour && !row.IsTotal" colspan="2">
                    {{ row.RecoveryRate | cph:'currency':2 }} {{ constants.translatedText.Dashboard_ServiceSales_PerRetailHour }}</td>
                  <td>{{ row.Turnover | cph:'currency':0 }}</td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>
      </div>
    `,
    styles: [
      `
        table { width: 90%; margin: 0 auto;  }
        table thead tr th { height: 2em !important; }
        table tbody tr.totalRow td { background-color: var(--grey95); font-weight: 700; }
        table tbody tr td svg { color: var(--grey40); }
      `,
    ],
  })

  export class ServiceSalesByTypeTileComponent implements OnInit {
    data: ServiceChannelSplits[];
    icons: { [key: string]: string; }

    constructor (
      public constants: ConstantsService,
      public service: ServiceSummaryService
    ) { }
    
    ngOnInit(): void {
      this.icons = {
        Retail: 'fa fa-wrench',
        Internal: 'fa fa-car-wash',
        Warranty: 'fa fa-engine-warning',
        Tyre: 'fa fa-tire',
        Oil: 'fa fa-oil-can',
        MOT: 'fa fa-wrench',
        Other: 'fa fa-circle',
        Mechanical: 'fa fa-wrench',
        RMS: 'fa fa-car-wash',
        Bodyshop: 'fa fa-engine-warning'
      }

      this.data = this.service.serviceChannelSplits;
    }

  }