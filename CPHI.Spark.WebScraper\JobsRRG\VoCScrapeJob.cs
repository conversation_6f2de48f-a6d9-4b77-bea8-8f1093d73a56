﻿using log4net;
using OpenQA.Selenium;

using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CPHI.Spark.WebScraper.Jobs
{

    public class VoCScrapeJob : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(VoCScrapeJob));
        private DateTime startTime;
        private  ChromeDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
        string password = "Y&&8EP@dHpFS6Lg9";

        private string customerName;
        private string fileDestination;
        //private string fileDestinationDev;

        public void Execute() { }

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();


            fileDestination = ConfigService.FileDestination;
            //fileDestinationDev = ConfigService.FileDestinationDev;
            fileDestination = fileDestination.Replace("{destinationFolder}", "rrg");
            //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "rrg");
            customerName = "RRG";


            try
            {

                //Process[] chromeInstances = Process.GetProcessesByName("chrome");
                //foreach (Process p in chromeInstances) p.Kill();

                //Process[] chromeDriverInstances = Process.GetProcessesByName("chromedriver");
                //foreach (Process p in chromeDriverInstances) p.Kill();

                ScraperMethodsService.ClearDownloadsFolder();

                logger.Info($"Starting Voc Scrape job");

                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";
                ChromeOptions options = new ChromeOptions();

                options.Proxy = null;
                options.AddArguments("--start-maximized");
                options.AddArgument("no-sandbox");
                options.AddUserProfilePreference("profile.default_content_setting_values.automatic_downloads", 1);

                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(2));

                logger.Info("Starting GetVoCFiles");
                startTime = DateTime.Now;
                Login();
                GetVoCFiles();
                GetDataQualityFiles();

                CheckIfGotVocFiles();
                _driver.Quit();
                _driver.Dispose();
                stopwatch.Stop();
            }

            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                logger.Error($"Problem {e.ToString()}");
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WebScraper",
                    Customer = customerName,
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };
                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }


        private async void CheckIfGotVocFiles()
        {
            DateTime today = DateTime.Now.Date; 
            FileInfo[] todaysFiles = new DirectoryInfo(@"c:\cphiRoot\downloads").EnumerateFiles().Select(x =>
            {
                x.Refresh();
                return x;
            }).Where(x => (x.CreationTime.Date == today) && x.Name.Contains("VoC") && x.Extension == ".xls").ToArray();



            logger.Info($@"[{DateTime.UtcNow}]  | Now have got {todaysFiles.Length} VOC file(s) in downloads");
            TimeSpan duration = DateTime.Now - startTime;

            if (todaysFiles.Count() > 1)
            {
                EmailerService eService = new EmailerService();
                await eService.SendMail("VoC File Success 😀", $"Ran for {duration.Seconds} seconds");

                foreach (var file in todaysFiles)
                {
                    string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
                    string filePath = $@"{fileDestination}\{prefix}{file.Name}";
                    File.Move(file.FullName, filePath); //move them to incoming
                    //File.Copy(filePath, filePath.Replace(fileDestination, fileDestinationDev));
                }
            }
            else
            {
                EmailerService eService = new EmailerService();
                await eService.SendMail("VoC File Failed 😟", $"Ran for {duration.Seconds} seconds");
            }


        }




        public void WaitUntilFileDownloaded(string filenameFragment)
        {
            DateTime start = DateTime.Now;

            FileInfo[] recentlySavedFiles = new DirectoryInfo(@"c:\cphiRoot\downloads").EnumerateFiles().Select(x =>
            {
                x.Refresh();
                return x;
            }).Where(x => (x.CreationTime.Date > start) && x.Name.Contains(filenameFragment) && x.Extension == "xls").ToArray();

            TimeSpan waitTime = DateTime.Now - start;
            while (recentlySavedFiles.Length == 0 && waitTime.Seconds < 30)
            {
                System.Threading.Thread.Sleep(200);
                waitTime = DateTime.Now - start;
                recentlySavedFiles = new DirectoryInfo(@"c:\cphiRoot\downloads").EnumerateFiles().Select(x =>
                {
                    x.Refresh();
                    return x;
                }).Where(x => (x.CreationTime >= start) && x.Name.Contains(filenameFragment) && x.Extension == ".xls").ToArray();
            }

            //emerged so can now continue

        }


        public void Login()
        {
            //go to login page
            _driver.Navigate()
                .GoToUrl("https://www.voc-grouperenault.com/groupeRenault/index.html#/login");
            //wait for it to appear
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IWebElement loginButton = wait.Until(SeleniumExtras.WaitHelpers.ExpectedConditions.ElementExists(By.ClassName("btn-login")));
            Assert.Equal("VoC Groupe Renault", _driver.Title);
            Assert.Contains("Welcome to Groupe Renault", _driver.PageSource);
            _driver.FindElement(By.Id("name")).SendKeys("<EMAIL>");
            _driver.FindElement(By.Id("password")).SendKeys(password);
            _driver.FindElement(By.ClassName("btn-login")).Click();
            //wait for links to show
            IWebElement dashboardLink = wait.Until(ExpectedConditions.ElementExists(By.ClassName("bubbleLink")));
        }


        private IWebElement WaitAndFind(string target, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind( _driver, "VoCScrape", target, andClick);
            return result;
        }


        public void GetVoCFiles()
        {
            DateTime today = DateTime.Now;


            Dictionary<int, string> monthLookup = new Dictionary<int, string>()
                    {
                        { 1,"Jan" },
                        { 2,"Feb" },
                        { 3,"March" },
                        { 4,"Apr" },
                        { 5,"May" },
                        { 6,"Jun" },
                        { 7,"Jul" },
                        { 8,"Aug" },
                        { 9,"Sep" },
                        { 10,"Oct" },
                        { 11,"Nov" },
                        { 12,"Dec" },
                    };

            int currentMonth = today.Month;
            int lastMonth;
            if (currentMonth != 1) { lastMonth = currentMonth - 1; } else { lastMonth = 12; };
            int currentMonthYear = today.Year;
            int lastMonthYear;
            if (currentMonth != 1) { lastMonthYear = currentMonthYear; } else { lastMonthYear = currentMonthYear - 1; };

            List<string> monthStrings = new List<string>();

            monthStrings.Add(monthLookup[lastMonth] + " " + lastMonthYear);
            monthStrings.Add(monthLookup[currentMonth] + " " + currentMonthYear);

            _driver.Navigate()
                .GoToUrl("https://www.voc-grouperenault.com/groupeRenault/index.html#/home/<USER>");
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            //IWebElement datatableWrapper = wait.Until(ExpectedConditions.ElementExists(By.Id("DataTables_Table_0_wrapper")));



            try
            {

                foreach (var monthString in monthStrings)
                {
                    IWebElement filterButton = WaitAndFind("//div [contains(@class, 'filtro-boton')]");

                    ClickElement(filterButton);

                    //survey
                    WaitAndFind("//span [contains(text(),'Survey')]", true); //click grey PERIOD expander

                    //Primary Sales
                    IWebElement projectBox = WaitAndFind("//div [@id='s2id_Project']", true);//find and click survey button
                    System.Threading.Thread.Sleep(100);
                    ClickElement(projectBox);
                    IWebElement surveyInput = WaitAndFind("//div [contains(@class, 'select2-with-searchbox') and contains(@style, 'display: block')] / * / input [contains(@class, 'select2-input')]"); //find active input
                    surveyInput.Clear();
                    surveyInput.SendKeys("VoC Sales Renault");
                    surveyInput.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(200);

                    //secondary Sales
                    IWebElement secondaryProjectBox = WaitAndFind("//div [@id='s2id_SecondaryProject']", true);//find and click survey button
                    ClickElement(secondaryProjectBox);
                    System.Threading.Thread.Sleep(200);

                    IWebElement secondarySurvey = WaitAndFind("//input [contains(@class, 'select2-focused')] "); //find input
                    secondarySurvey.Clear();
                    secondarySurvey.SendKeys("VoC Sales Dacia");
                    secondarySurvey.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(200);

                    //wave input
                    IWebElement waveBox = WaitAndFind("//div [@Id='s2id_Wave']");
                    ClickElement(waveBox);

                    IWebElement waveInput = WaitAndFind("//input [contains(@class, 'select2-focused')] ", true); //find and click wave box

                    waveInput.SendKeys(monthString);

                    //see if we can find the month we want in the dropdown list
                    IWebElement monthWeWant = WaitAndFind("//span [contains(@class, 'select2-match') and contains(text(),'" + monthString + "')]", false);

                    //if we can't find the month we want then it's too early - skip this month
                    if (monthWeWant == null) continue;

                    waveInput.SendKeys(Keys.Return);

                    //period
                    WaitAndFind("//span [contains(text(),'Period')]", true); //click grey PERIOD expander

                    IWebElement timePeriodBox = WaitAndFind("//div [@Id='s2id_Period'] / * / span [contains(@class, 'select2-chosen')]", true); //find and click time period box
                    //timePeriodBox.SendKeys(Keys.Enter);
                    ClickElement(timePeriodBox);
                    

                    IWebElement periodInput = WaitAndFind("//div [contains(@class, 'select2-with-searchbox') and contains(@style, 'display: block')] / * / input [contains(@class, 'select2-input')]"); //fill in chosen period
                    periodInput.Clear();
                    periodInput.SendKeys("3FM");
                    periodInput.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(1000);

                    WaitAndFind("//button [contains(@class, 'btn-filtro-apply')]", true); //apply
                    WaitAndFind("//div [@Id='ascrail2000']"); //wait for side scroll bar to appear showing filter panel has closed
                    IWebElement excelButton = WaitAndFind("//*[contains(text(),'EXCEL')]", false); //wait for excel button download
                    System.Threading.Thread.Sleep(2000);
                    ClickElement(excelButton);
                    WaitUntilFileDownloaded("VoC");
                    //--------------------------------
                    //Now switch to Service
                    ClickElement(filterButton);//click filters
                    System.Threading.Thread.Sleep(500);
                    //Primary Service
                    WaitAndFind("//span [contains(text(),'Survey')]", true); //click grey SURVEY expander
                    IWebElement projectBoxAgain = WaitAndFind("//div [@id='s2id_Project']", true);//find and click survey button
                    ClickElement(projectBoxAgain);
                    System.Threading.Thread.Sleep(100);
                    surveyInput.Clear();
                    surveyInput.SendKeys("VoC Service Renault");
                    surveyInput.SendKeys(Keys.Return);
                    System.Threading.Thread.Sleep(200);

                    //secondary Service
                    //IWebElement secondaryProjectBox2 = WaitAndFind("//div [@id='s2id_SecondaryProject']", true);//find and click survey button
                    //ClickElement(secondaryProjectBox2);
                    //System.Threading.Thread.Sleep(200);

                    //IWebElement secondarySurvey2 = WaitAndFind("//input [contains(@class, 'select2-focused')] "); //find input
                    //secondarySurvey2.Clear();
                    //secondarySurvey2.SendKeys("VoC Sales Dacia");
                    //secondarySurvey2.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(1000);

                    //wave input
                    waveBox = WaitAndFind("//div [@Id='s2id_Wave']");
                    ClickElement(waveBox);
                    
                    waveInput = WaitAndFind("//input [contains(@class, 'select2-focused')] ", true); //find and click wave box
                    waveInput.SendKeys(monthString);
                    waveInput.SendKeys(Keys.Return);


                    //period
                    WaitAndFind("//span [contains(text(),'Period')]", true); //click grey PERIOD expander

                    IWebElement timePeriodBox2 = WaitAndFind("//div [@Id='s2id_Period'] / * / span [contains(@class, 'select2-chosen')]", true); //find and click time period box
                    //timePeriodBox.SendKeys(Keys.Enter);
                    ClickElement(timePeriodBox2);


                    IWebElement periodInput2 = WaitAndFind("//div [contains(@class, 'select2-with-searchbox') and contains(@style, 'display: block')] / * / input [contains(@class, 'select2-input')]"); //fill in chosen period
                    periodInput2.Clear();
                    periodInput2.SendKeys("3FM");
                    periodInput2.SendKeys(Keys.Return);


                    System.Threading.Thread.Sleep(1000);
                    WaitAndFind("//button [contains(@class, 'btn-filtro-apply')]", true); //apply
                    WaitAndFind("//div [@Id='ascrail2000']"); //wait for side scroll bar to appear showing filter panel has closed

                    //wait.Until(x => x.FindElements(By.XPath("//*[contains(text(),'EXCEL')]")).Count == 0); //wait until old excel gone

                    System.Threading.Thread.Sleep(1000); //wait x secs until new repot there
                    WaitAndFind("//*[contains(text(),'EXCEL')]", true); //wait for new excel button download

                    WaitUntilFileDownloaded("VoC");

                    { }

                }
            }
            catch (Exception e)
            {

                //hit a snag
               logger.Error($@"[{DateTime.UtcNow}]  | problem {e.InnerException.ToString()}");
            }


        }


        public void ClickElement(IWebElement el)
        {
            try
            {
                el.Click();
            }
            catch
            {
                IJavaScriptExecutor ex = (IJavaScriptExecutor)_driver;
                ex.ExecuteScript("arguments[0].click();", el);
            }
        }




        public void GetDataQualityFiles()
        {

            DateTime today = DateTime.Now;
            Dictionary<int, string> monthLookup = new Dictionary<int, string>()
                    {
                        { 1,"Jan" },
                        { 2,"Feb" },
                        { 3,"March" },
                        { 4,"Apr" },
                        { 5,"May" },
                        { 6,"Jun" },
                        { 7,"Jul" },
                        { 8,"Aug" },
                        { 9,"Sep" },
                        { 10,"Oct" },
                        { 11,"Nov" },
                        { 12,"Dec" },
                    };

            int currentMonth = today.Month;
            int lastMonth;
            if (currentMonth != 1) { lastMonth = currentMonth - 1; } else { lastMonth = 12; };
            int currentMonthYear = today.Year;
            int lastMonthYear;
            if (currentMonth != 1) { lastMonthYear = currentMonthYear; } else { lastMonthYear = currentMonthYear - 1; };

            List<string> monthStrings = new List<string>();

            monthStrings.Add(monthLookup[lastMonth] + " " + lastMonthYear);
            monthStrings.Add(monthLookup[currentMonth] + " " + currentMonthYear);

            //return;
            //go to login page

            _driver.Navigate()
                .GoToUrl("https://www.voc-grouperenault.com/groupeRenault/index.html#/home/<USER>");

            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));

            IWebElement datatableWrapper = WaitAndFind("//table [contains(@class, 'dataTable')]", true);


            try
            {
                foreach (var monthString in monthStrings)
                {
                    System.Threading.Thread.Sleep(1000);
                    IWebElement filterButton = WaitAndFind("//div [contains(@class, 'filtro-boton')]");
                    System.Threading.Thread.Sleep(1000);
                    ClickElement(filterButton);

                    //survey
                    WaitAndFind("//span [contains(text(),'Survey')]", true); //click grey PERIOD expander

                    //Primary Sales
                    IWebElement projectBox = WaitAndFind("//div [@id='s2id_Project']", true); //find and click survey button
                    System.Threading.Thread.Sleep(1000);
                    ClickElement(projectBox);
                    { }
                    IWebElement surveyInput = WaitAndFind("//div [contains(@class, 'select2-with-searchbox') and contains(@style, 'display: block')] / * / input [contains(@class, 'select2-input')]"); //find active input
                    surveyInput.Clear();
                    surveyInput.SendKeys("VoC Sales Renault");
                    surveyInput.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(200);

                    //secondary Sales
                    IWebElement secondaryProjectBox = WaitAndFind("//div [@id='s2id_SecondaryProject']", true);//find and click survey button
                    ClickElement(secondaryProjectBox);
                    System.Threading.Thread.Sleep(200);

                    IWebElement secondarySurvey = WaitAndFind("//input [contains(@class, 'select2-focused')] "); //find input
                    secondarySurvey.Clear();
                    secondarySurvey.SendKeys("VoC Sales Dacia");
                    secondarySurvey.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(200);

                    //wave input
                    IWebElement waveBox = WaitAndFind("//div [@Id='s2id_Wave']");
                    ClickElement(waveBox);

                    IWebElement waveInput = WaitAndFind("//input [contains(@class, 'select2-focused')] ", true); //find and click wave box

                    waveInput.SendKeys(monthString);

                    //see if we can find the month we want in the dropdown list
                    IWebElement monthWeWant = WaitAndFind("//span [contains(@class, 'select2-match') and contains(text(),'" + monthString + "')]", false);

                    //if we can't find the month we want then it's too early - skip this month
                    if (monthWeWant == null) continue;

                    waveInput.SendKeys(Keys.Return);

                    //period
                    WaitAndFind("//span [contains(text(),'Period')]", true); //click grey PERIOD expander

                    IWebElement timePeriodBox = WaitAndFind("//div [@Id='s2id_Period'] / * / span [contains(@class, 'select2-chosen')]", true); //find and click time period box
                    ClickElement(timePeriodBox);

                    IWebElement periodInput = WaitAndFind("//div [contains(@class, 'select2-with-searchbox') and contains(@style, 'display: block')] / * / input [contains(@class, 'select2-input')]"); //fill in chosen period
                    periodInput.Clear();
                    periodInput.SendKeys("3FM");
                    periodInput.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(1000);
                    WaitAndFind("//button [contains(@class, 'btn-filtro-apply')]", true); //apply
                    WaitAndFind("//div [@Id='ascrail2000']"); //wait for side scroll bar to appear showing filter panel has closed


                    IWebElement excelButton = WaitAndFind("//*[contains(text(),'EXCEL')]", true); //wait for excel button download
                    if (excelButton == null)
                    {
                        //data not available, move back to last month
                        filterButton = WaitAndFind("//div [contains(@class, 'filtro-boton')]", true);
                        WaitAndFind("//span [contains(text(),'Survey')]", true); //click grey  expander
                        IWebElement wave = WaitAndFind("//div [@id='s2id_Wave']", true);//find and click survey button
                    ClickElement(wave);
                        System.Threading.Thread.Sleep(200);
                        waveInput = WaitAndFind("//input [contains(@class, 'select2-focused')] "); //find input
                        waveInput.SendKeys(Keys.Up);
                        waveInput.SendKeys(Keys.Enter);
                        System.Threading.Thread.Sleep(500);
                        WaitAndFind("//button [contains(@class, 'btn-filtro-apply')]", true); //apply
                        System.Threading.Thread.Sleep(500);
                        WaitAndFind("//div [@Id='ascrail2000']"); //wait for side scroll bar to appear showing filter panel has closed
                    }

                    excelButton = WaitAndFind("//*[contains(text(),'EXCEL')]", true); //wait for excel button download
                    if (excelButton == null)
                    {
                        throw new Exception("Failed to find excel button");
                    }
                    WaitUntilFileDownloaded("VoC");
                    //--------------------------------
                    //Now switch to Service
                    ClickElement(filterButton);//click filters
                    System.Threading.Thread.Sleep(500);
                    //Primary Service
                    WaitAndFind("//span [contains(text(),'Survey')]", true); //click grey SURVEY expander
                    IWebElement projectBoxAgain = WaitAndFind("//div [@id='s2id_Project']", true);//find and click survey button
                    ClickElement(projectBoxAgain);//click filters
                    System.Threading.Thread.Sleep(100);
                    surveyInput.Clear();
                    surveyInput.SendKeys("VoC Service Renault");
                    surveyInput.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(1000);

                    //wave input
                    waveBox = WaitAndFind("//div [@Id='s2id_Wave']");
                    ClickElement(waveBox);//click filters
                    waveInput = WaitAndFind("//input [contains(@class, 'select2-focused')] ", true); //find and click wave box
                    waveInput.SendKeys(monthString);
                    waveInput.SendKeys(Keys.Return);

                    System.Threading.Thread.Sleep(1000);
                    WaitAndFind("//button [contains(@class, 'btn-filtro-apply')]", true); //apply
                    WaitAndFind("//div [@Id='ascrail2000']"); //wait for side scroll bar to appear showing filter panel has closed


                    excelButton = WaitAndFind("//*[contains(text(),'EXCEL')]", true); //wait for new excel button download
                    if (excelButton == null)
                    {
                        //data not available, move back to last month
                        filterButton = WaitAndFind("//div [contains(@class, 'filtro-boton')]", true);
                        WaitAndFind("//span [contains(text(),'Survey')]", true); //click grey  expander
                        IWebElement wave = WaitAndFind("//div [@id='s2id_Wave']", true);//find and click survey button
                    ClickElement(wave);
                        System.Threading.Thread.Sleep(200);
                        waveInput = WaitAndFind("//input [contains(@class, 'select2-focused')] "); //find input
                        waveInput.SendKeys(Keys.Up);
                        waveInput.SendKeys(Keys.Enter);
                        WaitAndFind("//button [contains(@class, 'btn-filtro-apply')]", true); //apply
                        System.Threading.Thread.Sleep(1000);
                        WaitAndFind("//div [@Id='ascrail2000']"); //wait for side scroll bar to appear showing filter panel has closed
                    }

                    excelButton = WaitAndFind("//*[contains(text(),'EXCEL')]", true); //wait for new excel button download
                    if (excelButton == null) throw new Exception("Failed to find excel button");

                    System.Threading.Thread.Sleep(1000); //wait x secs until new repot there

                    WaitUntilFileDownloaded("VoC");

                    { }

                }
            }
            catch (Exception e)
            {

                //hit a snag
               logger.Error($@"[{DateTime.UtcNow}]  | problem {e.ToString()}");
            }

        }

    }
}

