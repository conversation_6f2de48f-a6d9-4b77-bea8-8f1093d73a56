import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ICellRendererParams } from "ag-grid-community";

@Component({
  selector: 'autoTraderAdvertImage-cell',
  template: `
    <img *ngIf="showImage" class="advertImageInCell" [src]="imageUrl"  />
  `,
  styles: [`
    .advertImageInCell {
      max-width: 70px;
      height: auto;
      /* height: unset;
      max-width:unset; */
    }

    @media (max-width: 1680px) {
      .advertImageInCell {
        max-width: 45px;
      }
    }



  `]
})
export class AutoTraderAdvertImage implements ICellRendererAngularComp {
  imageUrl: string = '/assets/imgs/autoTrader/placeholder-car.png';
  showImage:boolean = true;
  imageMaxHeight: number;

  constructor() {

  }

  agInit(params: ICellRendererParams): void {
    let ratio: number = Math.round(window.devicePixelRatio * 100);
    this.imageMaxHeight = (60 / ratio) * 100;
    if(!params.data || params.node.rowPinned){this.showImage = false;}
    if (params.data?.ImageURL) { this.imageUrl = params.data?.ImageURL; }
    if (params.data?.ImageUrl) { this.imageUrl = params.data?.ImageUrl; }
    if (params.data?.AdvertFirstImage) { this.imageUrl = params.data?.AdvertFirstImage; }
    if (!params.data?.ImageURL && !params.data?.ImageUrl && !params.data?.AdvertFirstImage) { this.imageUrl = '/assets/imgs/autoTrader/placeholder-car.png'; }
  }

  refresh(): boolean {
    return false;
  }
}
