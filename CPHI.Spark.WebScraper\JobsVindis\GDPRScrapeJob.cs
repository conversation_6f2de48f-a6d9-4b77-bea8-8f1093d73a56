﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.IO;
using System.Linq;
using Xunit;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Text.RegularExpressions;

namespace CPHI.Spark.WebScraper.Jobs
{

    public class GDPRScrapeJob : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(GDPRScrapeJob));
        private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
        private string fileDestination;
        //private string fileDestinationDev;
        public void Execute() { }
        public async Task Execute(IJobExecutionContext context)
        {

            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            fileDestination = ConfigService.FileDestination;
            //fileDestinationDev = ConfigService.FileDestinationDev;
            fileDestination = fileDestination.Replace("{destinationFolder}", "vindis");
            //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "vindis");

            try
            {

                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";
                ScraperMethodsService.ClearDownloadsFolder();

                ChromeOptions options = ScraperMethodsService.SetChromeOptions("VindisGDPR", 9226);

                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

                GetGDPR();

                _driver.Quit();
                _driver.Dispose();
                stopwatch.Stop();

            }
            catch (Exception e)
            {
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");

                stopwatch.Stop();
                errorMessage = e.ToString();
                //Emailer.SendMail("Vindis activity scraper failed", $"{e}");

                logger.Error($"Problem {e.ToString()}");

                _driver.Quit();
                _driver.Dispose();

                //Process[] chromeInstances = Process.GetProcessesByName("chrome");
                //foreach (Process p in chromeInstances) p.Kill();
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WebScraper",
                    Customer = "Vindis",
                    //Environment = env,
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };
                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
            
        }


        public void GetGDPR()
        {

            try
            {
                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
                DateTime start = DateTime.Now;

                // Login Loop
                int maxAttempts = 5;
                int attempt = 0;
                bool isLoggedIn = false;

                while (attempt < maxAttempts)
                {
                    attempt++;
                    logger.Info($"Login attempt {attempt} of {maxAttempts}");

                    isLoggedIn = LoginToEnquiryMax(wait);

                    if (isLoggedIn)
                    {
                        logger.Info($"Login successful.");
                        TakeScreenshot(_driver, "LoginSuccess");
                        break; // Exit loop once logged in
                    }

                    if (attempt < maxAttempts)
                    {
                        int sleepTime = (int)Math.Pow(2, attempt) * 1000;
                        logger.Info($"Login failed. Retrying in {sleepTime / 1000} seconds...");
                        System.Threading.Thread.Sleep(sleepTime);
                    }
                }

                if (!isLoggedIn)
                {
                    logger.Info("Failed to log in after maximum attempts.");
                    TakeScreenshot(_driver, "LoginFailure");
                    throw new Exception("Unable to login");
                }


                NavigateToReport(wait);

                DownloadReport(start);

            }
            catch (Exception e)
            {
                logger.Info($"Error in GetGDPR function: {e.StackTrace}");
                throw e;
            }
        }


        private bool LoginToEnquiryMax(WebDriverWait wait)
        {

            try
            {
                IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

                //go to login page
                _driver.Navigate().GoToUrl("https://enquirymax.net/account/logon");

                System.Threading.Thread.Sleep(3000);

                IWebElement loginButton = wait.Until(ExpectedConditions.ElementExists(By.Id("UserName")));

                System.Threading.Thread.Sleep(1000);

                Assert.Equal("Login - enquiryMAX", _driver.Title);

                System.Threading.Thread.Sleep(1000);

                WaitAndFind("//input [@Id='UserName']", false).SendKeys("james.smith");
                System.Threading.Thread.Sleep(1000);
                //WaitAndFind("//input [@Id='NextButton']", true);
                WaitAndFind("//input [@Id='UserName']", false).SendKeys(Keys.Tab);
                System.Threading.Thread.Sleep(1000);
                WaitAndFind("//input [@Id='Password']", false).SendKeys(ConfigService.EnquiryMAXPassword);
                System.Threading.Thread.Sleep(1000);
                WaitAndFind("//input [@Id='SubmitButton']", true);
                System.Threading.Thread.Sleep(1000);

                IWebElement dashboardLink = wait.Until(ExpectedConditions.ElementExists(By.ClassName("top-navbar")));

                return true;
            }
            catch(Exception e)
            {
                logger.Info($"Error in GetGDPR function LoginToEnquiryMax: {e.StackTrace}");
                return false;
            }


        }

        private void NavigateToReport(WebDriverWait wait)
        {

            WaitAndFind("/html/body/div[1]/header/div[1]/div/nav/ul/li[5]", true);
            System.Threading.Thread.Sleep(1000);

            // Navigate to Reports -> GDPR
            WaitAndFind("/html/body/div[1]/header/div[1]/div/nav/ul/li[5]/ul/a[7]/li", true);
            System.Threading.Thread.Sleep(2000);

            // Navigate to Reports -> GDPR -> GDPR Capture
            WaitAndFind("/html/body/div[1]/div[2]/div[2]/div[1]/ui-view/div[1]/div/ul/li[1]/a", true);
            System.Threading.Thread.Sleep(3000);

            // Open Drop Down
            WaitAndFind("/html/body/div[1]/div[2]/div[2]/div[1]/ui-view/div[2]/div/div[1]/div[1]/div[4]/div/select", true);
            System.Threading.Thread.Sleep(3000);

            IWebElement timescaleDropdown = _driver.FindElement(By.XPath("/html/body/div[1]/div[2]/div[2]/div[1]/ui-view/div[2]/div/div[1]/div[1]/div[4]/div/select"));
            
            SelectElement SelectTimeScale = new SelectElement(timescaleDropdown);
            SelectTimeScale.SelectByIndex(2);

            System.Threading.Thread.Sleep(3000);

            // On report page now, click search button
            WaitAndFind("/html/body/div[1]/div[2]/div[2]/div[1]/ui-view/div[2]/div/div[1]/div[4]/div[1]/input", true);
            System.Threading.Thread.Sleep(6000);

            IWebElement datatableWrapper = wait.Until(ExpectedConditions.ElementExists(By.ClassName("repohead-title")));
        }

        private void DownloadReport(DateTime start)
        {
           
            WaitAndFind("//input [@value='Export']", true);

            ScraperMethodsService.WaitUntilFileDownloaded("ConsentCaptureExport");
            logger.Info($"Succesfully saved down export file for report GDPR");

            MoveReportToInbound(start);
        }

        private void MoveReportToInbound(DateTime start)
        {
            
            string downloadPath = ConfigService.FileDownloadLocation;

            DirectoryInfo directory = new DirectoryInfo(downloadPath);

            // Get latest file with file name
            FileInfo generatedFile = directory.GetFiles().Where(x => x.LastWriteTime > start && x.Name.Contains("ConsentCapture")).First();

            string newFilePathAndName = downloadPath + @"\" + "GDPR_SPKV82" + ".xlsx";

            //rename the file
            File.Move(generatedFile.FullName, newFilePathAndName);
            logger.Info($"Succesfully changed filename from {generatedFile.FullName} to {newFilePathAndName}");
            //move to the incoming folder
            moveFile("GDPR_SPKV82" + ".xlsx");
        }


        public IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "GDPRScrape", findXPath, andClick);
            
            return result;
        }


        public void moveFile(string fileName)
        {
            string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
            string newFilePathAndName = $@"{fileDestination}{prefix}{fileName}";
            //string newFilePathAndNameDev = $@"{fileDestinationDev}{prefix}{fileName}";

            string path = ConfigService.FileDownloadLocation;
            string oldLocation = path + @"\" + fileName;
            
            File.Move(oldLocation, newFilePathAndName); //move them to incoming
            //File.Copy(newFilePathAndName, newFilePathAndNameDev); //copy to dev

            logger.Info($"Moved file from {oldLocation} to {newFilePathAndName}");
        }

        private static void TakeScreenshot(IWebDriver driver, string error)
        {
            try
            {
                // Generate a timestamped file name
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string screenshotPath = $@"C:\cphiRoot\GDPRScrapeImages\screenshot_{timestamp}_{error}.png";

                // Take a screenshot using Selenium
                Screenshot screenshot = ((ITakesScreenshot)driver).GetScreenshot();

                // Save screenshot manually using byte array (avoiding ScreenshotImageFormat)
                File.WriteAllBytes(screenshotPath, screenshot.AsByteArray);

                logger.Info($"Screenshot saved at: {screenshotPath}");
            }
            catch (Exception ex)
            {
                logger.Error($"Failed to capture screenshot: {ex.Message}");
            }
        }



    }
}
