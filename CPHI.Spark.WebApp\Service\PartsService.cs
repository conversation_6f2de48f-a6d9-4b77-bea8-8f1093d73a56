﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Spark.DataAccess;

namespace CPHI.Spark.WebApp.Service
{



   public interface IPartsService
   {
      public Task<IEnumerable<PartsSiteRow>> GetPartsSiteRows(DateTime monthCommencing, AftersalesTimePeriod timePeriod, string channelsString, int userId, Model.DealerGroupName dealerGroup);
      public Task<AftersalesVsTarget> GetPartsVsTarget(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup);
      public Task<List<AftersalesDailyDoneVsTarget>> GetPartsDailyDoneVsTarget(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup);
      public Task<AftersalesRunRateSummary> GetPartsRunRate(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup);
      public Task<List<PartsChannelSplit>> GetPartsChannelSplits(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup);
      public Task<DashboardGuage> GetPartsSummary(AftersalesTimePeriod timePeriod, string siteIds, int userId, DateTime? monthCommencing, Model.DealerGroupName dealerGroup);
      Task<List<ServiceDailyChaseItem>> GetPartsDailyChaseChartData(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup);
      Task<IEnumerable<PartsDailySalesSiteRow>> GetPartsDailySales(DateTime startDate, string channels, int userId, Model.DealerGroupName dealerGroup);

      Task<List<ServiceDailyChaseItem>> GetPartsDailyChaseChartDataSpain(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup);
   }

   public class PartsService : IPartsService
   {
      private readonly IPartsDataAccess partsDataAccess;
      private readonly IWorkingDaysService workingDaysService;
      private readonly ITranslationService translationService;
      private readonly IConfiguration configuration;

      public PartsService(IPartsDataAccess partsDataAccess, IWorkingDaysService workingDaysService, ITranslationService translationService, IConfiguration configuration)
      {
         this.partsDataAccess = partsDataAccess;
         this.workingDaysService = workingDaysService;
         this.translationService = translationService;
         this.configuration = configuration;
      }

      private async Task<List<SiteElapsedWorkDaysAndTargetParts>> BuildoutDaysAndTargetForSites(DateTime timePeriodStart, DateTime monthStart, AftersalesTimePeriod timePeriod, string channelsString, int userId, List<int> siteIds, bool showAllSites, Model.DealerGroupName dealerGroup)
      {
         IEnumerable<PartsSiteRowDataItem> doneSoFar = await partsDataAccess.GetPartsSiteRows(timePeriodStart, timePeriod, channelsString, userId, showAllSites, dealerGroup);
         IEnumerable<PartsSiteRowDataItem> doneAndTargetFullMonth = await partsDataAccess.GetPartsSiteRows(monthStart, AftersalesTimePeriod.MTD, channelsString, userId, showAllSites, dealerGroup);
         IEnumerable<SiteAndElapsedWorkDays> daysPerSite = await workingDaysService.GetElapsedDaysForSites(timePeriodStart, timePeriod, siteIds, userId, showAllSites, dealerGroup);

         List<SiteElapsedWorkDaysAndTargetParts> results = new List<SiteElapsedWorkDaysAndTargetParts>();

         foreach (var item in doneSoFar)
         {

            var thisSiteDays = daysPerSite.FirstOrDefault(x => x.SiteId == item.SiteId);
            var thisSiteFullMonthTarget = doneAndTargetFullMonth.FirstOrDefault(x => x.SiteId == item.SiteId);

            if (thisSiteDays == null) { continue; }

            results.Add(new SiteElapsedWorkDaysAndTargetParts()
            {
               SiteId = item.SiteId,
               WorkingDaysElapsed = thisSiteDays.WorkingDaysElapsed,
               WorkingDaysInMonth = thisSiteDays.WorkingDaysInMonth,
               TargetPerDay = thisSiteDays.WorkingDaysInMonth != 0 ? thisSiteFullMonthTarget.SalesTarget / thisSiteDays.WorkingDaysInMonth : 0,
               TotalDone = item.SalesActual,
               TargetMarginPerDay = thisSiteDays.WorkingDaysInMonth != 0 ? thisSiteFullMonthTarget.MarginTarget / thisSiteDays.WorkingDaysInMonth : 0,
               TotalMarginDone = item.MarginActual,
               FullMonthTarget = thisSiteFullMonthTarget.SalesTarget,
               FullMonthMarginTarget = thisSiteFullMonthTarget.MarginTarget
            });
         }

         return results;
      }


      public async Task<IEnumerable<PartsSiteRow>> GetPartsSiteRows(DateTime monthCommencing, AftersalesTimePeriod timePeriod, string channelsStringIn, int userId, Model.DealerGroupName dealerGroup)
      {
         try
         {
            bool isVindis = dealerGroup == Model.DealerGroupName.Vindis;
            string channelsString = ConvertChannels(channelsStringIn, isVindis);
            bool showAllSites = isVindis;

            //data items
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, showAllSites)).OrderBy(x => x.SortOrder).Where(x => x.IsService && x.IsActive);
            var siteIds = sites.Where(x => x.IsActive).Select(x => x.SiteId).ToList();

            List<SiteElapsedWorkDaysAndTargetParts> perSiteDaysAndTarget = new List<SiteElapsedWorkDaysAndTargetParts>();

            if (timePeriod == AftersalesTimePeriod.MTD)
            {
               perSiteDaysAndTarget.AddRange(await BuildoutDaysAndTargetForSites(monthCommencing, monthCommencing, timePeriod, channelsString, userId, siteIds, showAllSites, dealerGroup));
            }
            else if (timePeriod == AftersalesTimePeriod.Yesterday)
            {
               DateTime yesterdayMonthCommencing = new DateTime(DateTime.UtcNow.AddDays(-1).Year, DateTime.UtcNow.AddDays(-1).Month, 1);
               perSiteDaysAndTarget.AddRange(await BuildoutDaysAndTargetForSites(yesterdayMonthCommencing, monthCommencing, timePeriod, channelsString, userId, siteIds, showAllSites, dealerGroup));
            }

            else if (timePeriod == AftersalesTimePeriod.WTD)
            {
               //special approach as is possible that the WTD could span two months.
               DateTime wtdStart = StaticHelpersService.StartOfWeek(DateTime.Now, DayOfWeek.Monday);
               DateTime wtdEnd = wtdStart.AddDays(6);

               if (wtdStart.Month != wtdEnd.Month)
               {
                  //complicated.  have to get targets for each month and working days for each month
                  //results for part of week that's last month
                  DateTime monthCommencingFirstMonth = new DateTime(wtdStart.Year, wtdStart.Month, 1);
                  DateTime monthCommencingSecondMonth = new DateTime(wtdStart.Year, wtdStart.Month, 1);

                  IEnumerable<PartsSiteRowDataItem> doneSoFar = await partsDataAccess.GetPartsSiteRows(wtdStart, AftersalesTimePeriod.WTD, channelsString, userId, showAllSites, dealerGroup);

                  IEnumerable<PartsSiteRowDataItem> doneAndTargetFirstMonth = await partsDataAccess.GetPartsSiteRows(monthCommencingFirstMonth, AftersalesTimePeriod.MTD, channelsString, userId, showAllSites, dealerGroup);
                  IEnumerable<PartsSiteRowDataItem> doneAndTargetSecondMonth = await partsDataAccess.GetPartsSiteRows(monthCommencingSecondMonth, AftersalesTimePeriod.MTD, channelsString, userId, showAllSites, dealerGroup);

                  IEnumerable<SiteAndElapsedWorkDays> daysPerSiteFirstMonth = await workingDaysService.GetElapsedDaysForSites(monthCommencingFirstMonth, timePeriod, siteIds, userId, showAllSites, dealerGroup);
                  IEnumerable<SiteAndElapsedWorkDays> daysPerSiteSecondMonth = await workingDaysService.GetElapsedDaysForSites(monthCommencingSecondMonth, timePeriod, siteIds, userId, showAllSites, dealerGroup);

                  IEnumerable<PartsSiteRowDataItem> doneAndTargetFullMonth = await partsDataAccess.GetPartsSiteRows(monthCommencing, AftersalesTimePeriod.MTD, channelsString, userId, showAllSites, dealerGroup);

                  foreach (var doneAndTargetFirstMonthThisSite in doneAndTargetFirstMonth)
                  {
                     var done = doneSoFar.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);
                     var daysThisSiteFirstMonth = daysPerSiteFirstMonth.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);
                     var doneAndTargetSecondMonthThisSite = doneAndTargetSecondMonth.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);
                     var fullMonthTarget = doneAndTargetFullMonth.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);
                     var daysThisSiteSecondMonth = daysPerSiteSecondMonth.First(x => x.SiteId == doneAndTargetFirstMonthThisSite.SiteId);

                     //work out, and add together
                     perSiteDaysAndTarget.Add(new SiteElapsedWorkDaysAndTargetParts()
                     {
                        SiteId = doneAndTargetFirstMonthThisSite.SiteId,
                        WorkingDaysElapsed = daysThisSiteFirstMonth.WorkingDaysElapsed + daysThisSiteSecondMonth.WorkingDaysElapsed,
                        WorkingDaysInMonth = daysThisSiteFirstMonth.WorkingDaysInMonth + daysThisSiteSecondMonth.WorkingDaysInMonth,
                        TotalDone = doneAndTargetFirstMonthThisSite.SalesActual + doneAndTargetSecondMonthThisSite.SalesActual,
                        TotalMarginDone = doneAndTargetFirstMonthThisSite.MarginActual + doneAndTargetSecondMonthThisSite.MarginActual,

                        TargetPerDay = (daysThisSiteFirstMonth.WorkingDaysInMonth != 0 ? daysThisSiteFirstMonth.WorkingDaysElapsed / daysThisSiteFirstMonth.WorkingDaysInMonth * doneAndTargetFirstMonthThisSite.SalesTarget : 0) +
                                         (daysThisSiteSecondMonth.WorkingDaysInMonth != 0 ? daysThisSiteSecondMonth.WorkingDaysElapsed / daysThisSiteSecondMonth.WorkingDaysInMonth * doneAndTargetSecondMonthThisSite.SalesTarget : 0),
                        TargetMarginPerDay = (daysThisSiteFirstMonth.WorkingDaysInMonth != 0 ? daysThisSiteFirstMonth.WorkingDaysElapsed / daysThisSiteFirstMonth.WorkingDaysInMonth * doneAndTargetFirstMonthThisSite.MarginTarget : 0) +
                         (daysThisSiteSecondMonth.WorkingDaysInMonth != 0 ? daysThisSiteSecondMonth.WorkingDaysElapsed / daysThisSiteSecondMonth.WorkingDaysInMonth * doneAndTargetSecondMonthThisSite.MarginTarget : 0),
                        FullMonthTarget = fullMonthTarget.SalesTarget,
                        FullMonthMarginTarget = fullMonthTarget.MarginTarget
                     });
                  }
               }
               else
               {
                  //not complicated, start and end all in same month
                  DateTime monthCommencingForWTDStart = new DateTime(wtdStart.Year, wtdStart.Month, 1);
                  perSiteDaysAndTarget.AddRange(await BuildoutDaysAndTargetForSites(wtdStart, monthCommencing, timePeriod, channelsString, userId, siteIds, showAllSites, dealerGroup));

               }
            }


            //walk through sites making results
            List<PartsSiteRow> results = new List<PartsSiteRow>();

            foreach (var site in sites)
            {
               //var thisSiteWip = wips.FirstOrDefault(x => x.SiteId == site.SiteId);
               SiteElapsedWorkDaysAndTargetParts thisSiteStats = perSiteDaysAndTarget.FirstOrDefault(x => x.SiteId == site.SiteId);

               //This site done
               decimal thisSiteDone = thisSiteStats != null ? thisSiteStats.TotalDone : 0;
               decimal thisSiteDoneMargin = thisSiteStats != null ? thisSiteStats.TotalMarginDone : 0;
               decimal thisSiteTarget = thisSiteStats != null ? thisSiteStats.WorkingDaysInMonth * thisSiteStats.TargetPerDay : 0;
               decimal thisSiteTargetMargin = thisSiteStats != null ? thisSiteStats.WorkingDaysInMonth * thisSiteStats.TargetMarginPerDay : 0;
               decimal thisSiteFullMonthTarget = thisSiteStats != null ? thisSiteStats.FullMonthTarget : 0;
               decimal thisSiteFullMonthMarginTarget = thisSiteStats != null ? thisSiteStats.FullMonthMarginTarget : 0;

               //This site working days
               decimal thisSiteWorkingDaysElapsed = thisSiteStats != null ? thisSiteStats.WorkingDaysElapsed : 0;
               decimal thisSiteWorkingDaysInMonth = thisSiteStats != null ? thisSiteStats.WorkingDaysInMonth : 0;
               decimal thisSiteWorkingDaysRemaining = thisSiteWorkingDaysInMonth - thisSiteWorkingDaysElapsed;

               //Remaining
               decimal thisSiteRemainingTargetToGo = thisSiteTarget - thisSiteDone;
               decimal thisSiteTargetToDate = thisSiteWorkingDaysInMonth != 0 ? thisSiteTarget * thisSiteWorkingDaysElapsed / thisSiteWorkingDaysInMonth : 0;
               decimal thisSiteTargetToDateMargin = thisSiteWorkingDaysInMonth != 0 ? thisSiteTargetMargin * thisSiteWorkingDaysElapsed / thisSiteWorkingDaysInMonth : 0;

               //calcs
               decimal donePerDay = thisSiteWorkingDaysElapsed > 0 ? thisSiteDone / thisSiteWorkingDaysElapsed : 0;
               decimal requiredPerDay = thisSiteWorkingDaysRemaining > 0 ? thisSiteRemainingTargetToGo / thisSiteWorkingDaysRemaining : 0;


               results.Add(new PartsSiteRow()
               {
                  RegionDescription = site.RegionDescription,
                  SiteId = site.SiteId,
                  RegionId = (int)site.RegionId,
                  Label = site.SiteDescription,
                  IsSite = true,
                  IsRegion = false,
                  IsTotal = false,

                  MonthTarget = Math.Round(thisSiteFullMonthTarget, 1, MidpointRounding.AwayFromZero),
                  TargetToDate = Math.Round(thisSiteTargetToDate, 1, MidpointRounding.AwayFromZero),
                  DoneToDate = Math.Round(thisSiteDone, 1, MidpointRounding.AwayFromZero),
                  VsToDate = Math.Round(thisSiteDone - thisSiteTargetToDate, 1, MidpointRounding.AwayFromZero),
                  AchievementToDate = Math.Round(thisSiteTargetToDate > 0 ? thisSiteDone / thisSiteTargetToDate : 0, 3, MidpointRounding.AwayFromZero),

                  MonthTargetMargin = Math.Round(thisSiteFullMonthMarginTarget, 1, MidpointRounding.AwayFromZero),
                  TargetToDateMargin = Math.Round(thisSiteTargetToDateMargin, 1, MidpointRounding.AwayFromZero),
                  DoneToDateMargin = Math.Round(thisSiteDoneMargin, 1, MidpointRounding.AwayFromZero),
                  VsToDateMargin = Math.Round(thisSiteDoneMargin - thisSiteTargetToDateMargin, 1, MidpointRounding.AwayFromZero),
                  AchievementToDateMargin = Math.Round(thisSiteTargetToDateMargin > 0 ? thisSiteDoneMargin / thisSiteTargetToDateMargin : 0, 3, MidpointRounding.AwayFromZero),

                  TargetPerDay = Math.Round(thisSiteWorkingDaysElapsed > 0 ? thisSiteTargetToDate / thisSiteWorkingDaysElapsed : 0, 1, MidpointRounding.AwayFromZero),
                  DonePerDay = Math.Round(thisSiteWorkingDaysElapsed > 0 ? thisSiteDone / thisSiteWorkingDaysElapsed : 0, 1, MidpointRounding.AwayFromZero),
                  RequiredPerDay = Math.Round(requiredPerDay, 1, MidpointRounding.AwayFromZero),
                  StepUpPerDay = Math.Round(requiredPerDay - donePerDay, 1, MidpointRounding.AwayFromZero),

               });


            }

            //add total
            decimal doneToDate = results.Select(x => x.DoneToDate).Sum();
            decimal targetToDate = results.Select(x => x.TargetToDate).Sum();


            var totalItem = new PartsSiteRow()
            {
               SiteId = 0,
               RegionId = 0,
               Label = await translationService.GetTranslationForProperty(userId, "Common_TotalSite"),
               IsSite = false,
               IsRegion = false,
               IsTotal = true,
            };

            foreach (var item in results.Where(x => x.IsSite))
            {
               AccumulateItemIntoNewItem(totalItem, item);
            }

            totalItem.AchievementToDate = Math.Round(totalItem.TargetToDate > 0 ? totalItem.DoneToDate / totalItem.TargetToDate : 0, 3, MidpointRounding.AwayFromZero);
            totalItem.AchievementToDateMargin = Math.Round(totalItem.TargetToDateMargin > 0 ? totalItem.DoneToDateMargin / totalItem.TargetToDateMargin : 0, 3, MidpointRounding.AwayFromZero);

            results.Add(totalItem);

            // Add regions
            var byRegion = results.Where(x => x.IsSite).ToLookup(x => x.RegionDescription);

            foreach (var regionGrouping in byRegion)
            {

               doneToDate = regionGrouping.Select(x => x.DoneToDate).Sum();
               targetToDate = regionGrouping.Select(x => x.TargetToDate).Sum();

               var regionItem = new PartsSiteRow()
               {
                  SiteId = 0,
                  RegionId = 0,
                  Label = regionGrouping.First().RegionDescription,
                  IsSite = false,
                  IsRegion = true,
                  IsTotal = false,
               };

               foreach (var item in regionGrouping)
               {
                  AccumulateItemIntoNewItem(regionItem, item);
               }
               ;

               regionItem.AchievementToDate = Math.Round(regionItem.TargetToDate > 0 ? regionItem.DoneToDate / regionItem.TargetToDate : 0, 3, MidpointRounding.AwayFromZero);
               regionItem.AchievementToDateMargin = Math.Round(regionItem.TargetToDateMargin > 0 ? regionItem.DoneToDateMargin / regionItem.TargetToDateMargin : 0, 3, MidpointRounding.AwayFromZero);

               results.Add(regionItem);

            }

            //not sure why the normal regions work doesn't pick this up.   leave until test Vindis.

            //bool isVindis = Startup.Configuration[$"{ configAppSettingsSectionName }:{configEnv}"] == "vindis";

            //if(isVindis)
            //{
            //    // Add the few extra brands for this table
            //    List<ServiceSiteRow> bentley = results.Where(x => x.IsSite && x.Label.Contains("Bentley")).ToList();
            //    results = SumNewBrand(bentley, results, "Bentley", 5);

            //    List<ServiceSiteRow> ducati = results.Where(x => x.IsSite && x.Label.Contains("Ducati")).ToList();
            //    results = SumNewBrand(ducati, results, "Ducati", 6);

            //    List<ServiceSiteRow> seat = results.Where(x => x.IsSite && x.Label.Contains("SEAT")).ToList();
            //    results = SumNewBrand(seat, results, "SEAT", 7);

            //    List<ServiceSiteRow> autonow = results.Where(x => x.IsSite && x.Label.Contains("AutoNow")).ToList();
            //    results = SumNewBrand(autonow, results, "AutoNow", 8);

            //    List<ServiceSiteRow> group = results.Where(x => x.IsSite && x.Label.Contains("Group")).ToList();
            //    results = SumNewBrand(group, results, "Group", 9);
            //}


            return results;

         }
         catch (Exception e)
         {

            throw new Exception(e.Message);
         }

      }


      private static void AccumulateItemIntoNewItem(PartsSiteRow totalItem, PartsSiteRow item)
      {
         totalItem.MonthTarget += item.MonthTarget;
         totalItem.TargetToDate += item.TargetToDate;
         totalItem.DoneToDate += item.DoneToDate;
         totalItem.VsToDate += item.VsToDate;
         totalItem.AchievementToDate += item.AchievementToDate;

         totalItem.TargetPerDay += item.TargetPerDay;
         totalItem.DonePerDay += item.DonePerDay;
         totalItem.RequiredPerDay += item.RequiredPerDay;
         totalItem.StepUpPerDay += item.StepUpPerDay;

         totalItem.TargetToDateMargin += item.TargetToDateMargin;
         totalItem.AchievementToDateMargin += item.AchievementToDateMargin;
         totalItem.VsToDateMargin += item.VsToDateMargin;
         totalItem.DoneToDateMargin += item.DoneToDateMargin;
      }



      //public async Task<IEnumerable<PartsSiteRow>> GetPartsSiteRows(AftersalesTimePeriod timePeriod, string channelsString, DateTime monthCommencing, int userId)
      //{

      //    //days stuff
      //    var holidaysInMonth = workingDaysService.GetPublicHolidays(monthCommencing, monthCommencing.AddMonths(1).Date).Result;
      //    IEnumerable<SiteWithOpening> siteWeekendOpening = workingDaysService.GetSiteWeekendOpening(userId);

      //    //data items
      //    string newChannelsString = ConvertChannels(channelsString);

      //    IEnumerable<PartsSiteRowDataItem> doneAndTarget = await partsDataAccess.GetPartsSiteRows(newChannelsString, timePeriod, monthCommencing, userId);
      //    IEnumerable<SiteVM> sites = siteDataAccess.GetSites(userId).Result.OrderBy(x => x.SortOrder);
      //    var siteIds = sites.Select(x => x.SiteId).ToList();


      //    Dictionary<int, SiteAndElapsedWorkDays> daysPerSite = workingDaysService.GetElapsedDaysForSites(monthCommencing, timePeriod, siteIds, userId).Result.ToDictionary(x=>x.SiteId);

      //    //walk through sites making results
      //    List<PartsSiteRow> results = new List<PartsSiteRow>();

      //    foreach (var site in sites)
      //    {
      //        // For Sale (Not Margin)
      //        var thisSiteDoneAndTarget = doneAndTarget.FirstOrDefault(x => x.SiteId == site.SiteId && x.MeasureType == "PartsSal");
      //        var thisSiteWeekendOpening = siteWeekendOpening.FirstOrDefault(x => x.SiteId == site.SiteId);
      //        if (thisSiteWeekendOpening == null)
      //        {
      //            //don't have opening for this site, maybe user doesn't have access to it, continue;
      //            continue;
      //        }

      //        //This site done
      //        decimal thisSiteRestOfMonth = thisSiteDoneAndTarget != null ? thisSiteDoneAndTarget.RestOfMonth : 0;  //stored proc puts days into yesterday, rest of week, rest of month.
      //        decimal thisSiteRestOfWeek = thisSiteDoneAndTarget != null ? thisSiteDoneAndTarget.RestofWeek : 0;
      //        decimal thisSiteYesterday = thisSiteDoneAndTarget != null ? thisSiteDoneAndTarget.Yesterday : 0;
      //        decimal thisSiteTarget = thisSiteDoneAndTarget != null ? thisSiteDoneAndTarget.Target : 0;
      //        decimal thisSiteDoneInMonth = thisSiteRestOfMonth + thisSiteRestOfWeek + thisSiteYesterday;
      //        decimal thisSiteDoneInWtd = thisSiteRestOfWeek + thisSiteYesterday;
      //        decimal thisSiteDoneInTimePeriod = 0;

      //        if (timePeriod == AftersalesTimePeriod.MTD) { thisSiteDoneInTimePeriod = thisSiteDoneInMonth; }
      //        else if (timePeriod == AftersalesTimePeriod.WTD) { thisSiteDoneInTimePeriod = thisSiteDoneInWtd; }
      //        else { thisSiteDoneInTimePeriod = thisSiteYesterday; }


      //        //This site working days
      //        decimal thisSiteWorkingDaysElapsed = daysPerSite[site.SiteId].WorkingDaysElapsed;
      //        decimal thisSiteWorkingDaysInMonth = daysPerSite[site.SiteId].WorkingDaysInMonth;
      //        decimal thisSiteWorkingDaysRemaining = thisSiteWorkingDaysInMonth - thisSiteWorkingDaysElapsed;


      //        //This site target
      //        decimal thisSiteRemainingTargetToGo = thisSiteTarget - thisSiteDoneInMonth;
      //        decimal thisSiteTargetToDate = thisSiteTarget * thisSiteWorkingDaysElapsed / thisSiteWorkingDaysInMonth;

      //        //calcs
      //        decimal donePerDay = thisSiteWorkingDaysElapsed > 0 ? thisSiteDoneInTimePeriod / thisSiteWorkingDaysElapsed : 0;
      //        decimal requiredPerDay = thisSiteWorkingDaysRemaining > 0 ? thisSiteRemainingTargetToGo / thisSiteWorkingDaysRemaining : 0;

      //        // My code -- Do Margin now
      //        var thisSiteDoneAndTargetMargin = doneAndTarget.FirstOrDefault(x => x.SiteId == site.SiteId && x.MeasureType == "PartsMarg");

      //        decimal thisSiteRestOfMonthMargin = thisSiteDoneAndTargetMargin != null ? thisSiteDoneAndTargetMargin.RestOfMonth : 0;  //stored proc puts days into yesterday, rest of week, rest of month.
      //        decimal thisSiteRestOfWeekMargin = thisSiteDoneAndTargetMargin != null ? thisSiteDoneAndTargetMargin.RestofWeek : 0;
      //        decimal thisSiteYesterdayMargin = thisSiteDoneAndTargetMargin != null ? thisSiteDoneAndTargetMargin.Yesterday : 0;
      //        decimal thisSiteTargetMargin = thisSiteDoneAndTargetMargin != null ? thisSiteDoneAndTargetMargin.Target : 0;
      //        decimal thisSiteDoneInMonthMargin = thisSiteRestOfMonthMargin + thisSiteRestOfWeekMargin + thisSiteYesterdayMargin;
      //        decimal thisSiteDoneInWtdMargin = thisSiteRestOfWeekMargin + thisSiteYesterdayMargin;

      //        decimal thisSiteDoneInTimePeriodMargin = 0;
      //        if (timePeriod == AftersalesTimePeriod.MTD) { thisSiteDoneInTimePeriodMargin = thisSiteDoneInMonthMargin; }
      //        else if (timePeriod == AftersalesTimePeriod.WTD) { thisSiteDoneInTimePeriodMargin = thisSiteDoneInWtdMargin; }
      //        else { thisSiteDoneInTimePeriodMargin = thisSiteYesterdayMargin; }

      //        //This site target
      //        decimal thisSiteRemainingTargetToGoMargin = thisSiteTargetMargin - thisSiteDoneInMonthMargin;
      //        decimal thisSiteTargetToDateMargin = thisSiteTargetMargin * thisSiteWorkingDaysElapsed / thisSiteWorkingDaysInMonth;

      //        results.Add(new PartsSiteRow()
      //        {
      //            RegionDescription = site.RegionDescription,
      //            SiteId = site.SiteId,
      //            RegionId = (int)site.RegionId,
      //            Label = site.SiteDescription,
      //            IsSite = true,
      //            IsRegion = false,
      //            IsTotal = false,

      //            MonthTarget = Math.Round(thisSiteTarget, 1, MidpointRounding.AwayFromZero),
      //            TargetToDate = Math.Round(thisSiteTargetToDate, 1, MidpointRounding.AwayFromZero),
      //            DoneToDate = Math.Round(thisSiteDoneInTimePeriod, 1, MidpointRounding.AwayFromZero),
      //            VsToDate = Math.Round(thisSiteDoneInTimePeriod - thisSiteTargetToDate, 1, MidpointRounding.AwayFromZero),
      //            AchievementToDate = Math.Round(thisSiteTargetToDate > 0 ? thisSiteDoneInTimePeriod / thisSiteTargetToDate : 0, 3, MidpointRounding.AwayFromZero),

      //            TargetPerDay = Math.Round(thisSiteWorkingDaysElapsed > 0 ? thisSiteTargetToDate / thisSiteWorkingDaysElapsed : 0, 1, MidpointRounding.AwayFromZero),
      //            DonePerDay = Math.Round(thisSiteWorkingDaysElapsed > 0 ? thisSiteDoneInTimePeriod / thisSiteWorkingDaysElapsed : 0, 1, MidpointRounding.AwayFromZero),
      //            RequiredPerDay = Math.Round(requiredPerDay, 1, MidpointRounding.AwayFromZero),
      //            StepUpPerDay = Math.Round(requiredPerDay - donePerDay, 1, MidpointRounding.AwayFromZero),

      //            MonthTargetMargin = Math.Round(thisSiteTargetMargin, 1, MidpointRounding.AwayFromZero),
      //            TargetToDateMargin = Math.Round(thisSiteTargetToDateMargin, 1, MidpointRounding.AwayFromZero),
      //            DoneToDateMargin = Math.Round(thisSiteDoneInTimePeriodMargin, 1, MidpointRounding.AwayFromZero),
      //            VsToDateMargin = Math.Round(thisSiteDoneInTimePeriodMargin - thisSiteTargetToDateMargin, 1, MidpointRounding.AwayFromZero),
      //            AchievementToDateMargin = Math.Round(thisSiteTargetToDateMargin > 0 ? thisSiteDoneInTimePeriodMargin / thisSiteTargetToDateMargin : 0, 3, MidpointRounding.AwayFromZero),

      //        });

      //    }

      //    //add total
      //    results.Add(AggregateItems(results, false, 0, true, translationService.GetTranslationForProperty(userId, "Common_TotalSite").Result));

      //    //add regions
      //    foreach (var regionItems in results.Where(x => x.IsSite).ToLookup(x => x.RegionId))
      //    {
      //        results.Add(AggregateItems(regionItems.ToList(), true, regionItems.First().RegionId, false, regionItems.First().RegionDescription));
      //    }

      //    return results;
      //}

      private static string ConvertChannels(string channelsString, bool isVindis)
      {

         if (channelsString == null && isVindis) { return "retail,internal,wshopInternal,wshopRetail,wshopWarranty,nonfran,trade"; }
         if (channelsString == null) { return "retail,internal,wshopInternal,wshopRetail,wshopWarranty,nonfran"; }

         List<string> channelsForStoredProc = new List<string>();

         foreach (var requestedChan in channelsString.Split(","))
         {
            if (requestedChan.Contains("Total") && isVindis) { channelsForStoredProc.Add("trade"); }
            if (requestedChan == "Retail") { channelsForStoredProc.Add("retail,nonfran"); }
            if (requestedChan == "Internal") { channelsForStoredProc.Add("internal"); }
            if (requestedChan == "Workshop Internal") { channelsForStoredProc.Add("wshopInternal"); }
            if (requestedChan == "Workshop Retail") { channelsForStoredProc.Add("wshopRetail"); }
            if (requestedChan == "Workshop Warranty") { channelsForStoredProc.Add("wshopWarranty"); }
         }
         string newChannelsString = string.Join(",", channelsForStoredProc);
         return newChannelsString;
      }

      private static PartsSiteRow AggregateItems(List<PartsSiteRow> itemsToAggregate, bool isRegion, int regionId, bool isTotal, string label)
      {
         //initially just populate the top level things, all the decimals and ints will default to zero
         PartsSiteRow newRow = new PartsSiteRow()
         {
            SiteId = 0,
            RegionId = regionId,
            Label = label,
            IsRegion = isRegion,
            IsTotal = isTotal
         };

         //now just do 1 loop through the source list, aggregating up the values as you go.  This will be many times faster.
         foreach (var item in itemsToAggregate)
         {
            newRow.MonthTarget += item.MonthTarget;
            newRow.TargetToDate += item.TargetToDate;
            newRow.DoneToDate += item.DoneToDate;
            newRow.VsToDate += item.VsToDate;

            newRow.TargetPerDay += item.TargetPerDay;
            newRow.DonePerDay += item.DonePerDay;
            newRow.RequiredPerDay += item.RequiredPerDay;
            newRow.StepUpPerDay += item.StepUpPerDay;
            newRow.MonthTargetMargin += item.MonthTargetMargin;
            newRow.TargetToDateMargin += item.TargetToDateMargin;
            newRow.DoneToDateMargin += item.DoneToDateMargin;
            newRow.VsToDateMargin += item.VsToDateMargin;
         }

         newRow.AchievementToDate = Math.Round(newRow.TargetToDate > 0 ? newRow.DoneToDate / newRow.TargetToDate : 0, 3, MidpointRounding.AwayFromZero);
         newRow.AchievementToDateMargin = Math.Round(newRow.TargetToDateMargin > 0 ? newRow.DoneToDateMargin / newRow.TargetToDateMargin : 0, 3, MidpointRounding.AwayFromZero);

         return newRow;
      }



      public async Task<AftersalesVsTarget> GetPartsVsTarget(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup)
      {
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         string _connectionString = configuration.GetConnectionString(dgName);
         var siteDataAccess = new SiteDataAccess(_connectionString);

         IEnumerable<PartChannelDataItem> dataItems = (await partsDataAccess.GetPartsChannelSummariesData(siteIds, monthCommencing, userId, showAllSites, dealerGroup)).OrderBy(x => x.Order);
         IEnumerable<SiteWithOpening> siteOpeningHours = await siteDataAccess.GetSitesWithOpeningForUser(userId, showAllSites, dealerGroup);
         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();

         var elapsedWorkingDays = await workingDaysService.GetWorkingDaysElapsedInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);
         var workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         List<ChannelSummary> channelSummaries = new List<ChannelSummary>();

         DateTime date = DateTime.Now;
         var firstDayOfMonth = new DateTime(date.Year, date.Month, 1);

         //add the rows below labour
         foreach (var item in dataItems)
         {
            channelSummaries.Add(new ChannelSummary()
            {
               Label = item.Channel,
               IsTotal = false,
               Target = item.Target * elapsedWorkingDays / workingDaysInMonth,
               Actual = item.Actual
            });
         }

         //add  total
         channelSummaries.Add(new ChannelSummary()
         {
            Label = "Total",
            IsTotal = true,
            Target = dataItems.Select(x => x.Target).Sum() * elapsedWorkingDays / workingDaysInMonth,
            Actual = dataItems.Select(x => x.Actual).Sum()
         });

         //build object to return
         return new AftersalesVsTarget()
         {
            ChannelStats = channelSummaries,
            Done = dataItems.Select(x => x.Actual).Sum(),
            MonthTarget = dataItems.Select(x => x.Target).Sum(),
            ElapsedDays = elapsedWorkingDays,
            TotalDays = workingDaysInMonth
         };
      }

      public async Task<List<AftersalesDailyDoneVsTarget>> GetPartsDailyDoneVsTarget(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup)
      {
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         //get data
         decimal target = await partsDataAccess.GetPartsMonthlyTarget(siteIds, monthCommencing, userId, showAllSites, dealerGroup);
         IEnumerable<ServiceDailyDone> done = await partsDataAccess.GetPartsDailyDone(siteIds, monthCommencing, userId, showAllSites, dealerGroup);

         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         workingDaysService.GetWeekendOpening(siteIdsAsList, userId, out decimal saturdayOpening, out decimal sundayOpening);
         IEnumerable<DayAndWorkingDayValue> dailyWorkingDays = await workingDaysService.GetDailyWorkingDaysForMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         decimal workingDaysInMonth = dailyWorkingDays.Select(x => x.WorkingDayValue).Sum();

         //walk through days building up results
         List<AftersalesDailyDoneVsTarget> results = new List<AftersalesDailyDoneVsTarget>();
         DateTime currentDay = monthCommencing.Date;
         decimal vsCum = 0;
         while (currentDay.Month == monthCommencing.Month)
         {
            var thisDayDoneItem = done.FirstOrDefault(x => x.Day == currentDay);
            decimal thisDayDone = thisDayDoneItem != null ? thisDayDoneItem.Done : 0;
            var thisDayTarget = currentDay.Date > DateTime.Now.Date.AddDays(-1) ? 0 : dailyWorkingDays.First(x => x.DayDate == currentDay).WorkingDayValue / workingDaysInMonth * target;
            vsCum += thisDayDone - thisDayTarget;
            results.Add(new AftersalesDailyDoneVsTarget()
            {
               Day = currentDay,
               Target = Math.Round(thisDayTarget, MidpointRounding.AwayFromZero),
               Done = Math.Round(thisDayDone, 2, MidpointRounding.AwayFromZero),
               VsTarget = Math.Round(thisDayDone - thisDayTarget, MidpointRounding.AwayFromZero),
               VsTargetCum = Math.Round(vsCum, MidpointRounding.AwayFromZero),
               IsWeekendOrPublicHoliday = (currentDay.DayOfWeek == DayOfWeek.Saturday || currentDay.DayOfWeek == DayOfWeek.Sunday),
               IsAPastDay = currentDay.Date < DateTime.UtcNow.Date
            });

            currentDay = currentDay.AddDays(1);
         }

         decimal totalDone = results.Select(x => x.Done).Sum();
         decimal totalTarget = results.Select(x => x.Target).Sum();

         //add WTD
         decimal wtdTarget = 0;
         decimal wtdDone = 0;
         if (DateTime.Now.Month == monthCommencing.Month && DateTime.Now.Year == monthCommencing.Year)
         {
            //we are looking at this month so work out WTD
            currentDay = DateTime.Now.Date.AddDays(-1);
            while (currentDay.DayOfWeek != DayOfWeek.Sunday && currentDay.Month == DateTime.Now.Month)
            {
               var thisDayDoneItem = done.FirstOrDefault(x => x.Day == currentDay);
               decimal thisDayDone = thisDayDoneItem != null ? thisDayDoneItem.Done : 0;
               var thisDayTarget = dailyWorkingDays.First(x => x.DayDate == currentDay).WorkingDayValue / workingDaysInMonth * target;
               wtdDone += thisDayDone;
               wtdTarget += thisDayTarget;
               currentDay = currentDay.AddDays(-1);
            }

            results.Add(new AftersalesDailyDoneVsTarget()
            {
               IsWTD = true,
               Target = Math.Round(wtdTarget, MidpointRounding.AwayFromZero),
               Done = Math.Round(wtdDone, MidpointRounding.AwayFromZero),
               VsTarget = Math.Round(wtdDone - wtdTarget, MidpointRounding.AwayFromZero),
               VsTargetCum = 0,
               IsAPastDay = true
            });
         }

         //add Total
         results.Add(new AftersalesDailyDoneVsTarget()
         {
            IsTotal = true,
            Target = Math.Round(totalTarget, MidpointRounding.AwayFromZero),
            Done = Math.Round(totalDone, MidpointRounding.AwayFromZero),
            VsTarget = Math.Round(vsCum, MidpointRounding.AwayFromZero),
            VsTargetCum = Math.Round(vsCum, MidpointRounding.AwayFromZero),
            IsWTD = false,
            IsAPastDay = true
         });


         return results;
      }

      public async Task<AftersalesRunRateSummary> GetPartsRunRate(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup)
      {
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         //get data
         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         decimal daysElapsed = await workingDaysService.GetWorkingDaysElapsedInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);
         decimal daysInMonth = await workingDaysService.GetWorkingDaysInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);
         decimal daysRemaining = daysInMonth - daysElapsed;

         var doneByDay = await partsDataAccess.GetPartsDailyDone(siteIds, monthCommencing, userId, showAllSites, dealerGroup);
         var doneTotal = doneByDay.Select(x => x.Done).Sum();
         var targetFullMonth = await partsDataAccess.GetPartsMonthlyTarget(siteIds, monthCommencing, userId, showAllSites, dealerGroup);

         return new AftersalesRunRateSummary()
         {
            WorkingDaysGone = daysElapsed,
            WorkingDaysTarget = daysInMonth,
            WorkingDaysRemaining = daysRemaining,

            TurnoverDone = doneTotal,
            TurnoverTarget = targetFullMonth,
            TurnoverNeed = targetFullMonth - doneTotal,

            TurnoverPerDayDone = Math.Round(daysElapsed > 0 ? doneTotal / daysElapsed : 0, 2, MidpointRounding.AwayFromZero),
            TurnoverPerDayTarget = Math.Round(targetFullMonth / daysInMonth, 2, MidpointRounding.AwayFromZero),
            TurnoverPerDayNeed = Math.Round(daysRemaining > 0 ? ((targetFullMonth - doneTotal) / daysRemaining) : 0, 2, MidpointRounding.AwayFromZero)
         };

      }

      public async Task<List<PartsChannelSplit>> GetPartsChannelSplits(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup)
      {
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         //get data
         IEnumerable<PartsChannelSplit> done = (await partsDataAccess.GetPartsChannelSplits(siteIds, monthCommencing, userId, showAllSites, dealerGroup)).OrderBy(x => x.Order);

         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         var daysElapsed = await workingDaysService.GetWorkingDaysElapsedInMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         List<PartsChannelSplit> results = new List<PartsChannelSplit>();
         //walk through to add recovery rate and hours per day

         foreach (var item in done)
         {
            results.Add(new PartsChannelSplit()
            {
               Channel = item.Channel,
               Turnover = Math.Round(item.Turnover, 2, MidpointRounding.AwayFromZero),
               Margin = Math.Round(item.Margin, 2, MidpointRounding.AwayFromZero),
               TurnoverPerDay = Math.Round(daysElapsed > 0 ? item.Turnover / daysElapsed : 0, 2, MidpointRounding.AwayFromZero),
               IsTotal = false,
            });
         }

         decimal totalTurnover = done.Select(x => x.Turnover).Sum();
         decimal totalMargin = done.Select(x => x.Margin).Sum();

         results.Add(new PartsChannelSplit()
         {
            Channel = "Total",
            Turnover = Math.Round(totalTurnover, 2, MidpointRounding.AwayFromZero),
            Margin = Math.Round(totalMargin, 2, MidpointRounding.AwayFromZero),
            TurnoverPerDay = Math.Round(daysElapsed > 0 ? totalTurnover / daysElapsed : 0, 2, MidpointRounding.AwayFromZero),
            IsTotal = true,
         });

         return results;
      }

      public async Task<DashboardGuage> GetPartsSummary(AftersalesTimePeriod timePeriod, string siteIds, int userId, DateTime? monthCommencing, Model.DealerGroupName dealerGroup)
      {

         bool includeNonLabour = dealerGroup != Model.DealerGroupName.Vindis;
         bool isSpain = dealerGroup == Model.DealerGroupName.RRGSpain;


         DashboardGuage result;

         if (isSpain)
         {
            result = await partsDataAccess.GetPartsSummarySpain(siteIds, userId, (DateTime)monthCommencing, dealerGroup);
         }
         else
         {
            result = await partsDataAccess.GetPartsSummary(timePeriod, siteIds, userId, dealerGroup);
         }
         ;

         DateTime today = DateTime.Now;

         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();

         // MONTH TO DATE
         if (timePeriod == AftersalesTimePeriod.MTD)
         {
            decimal workingDaysInMonth;
            decimal elapsedWorkingDays;

            if (monthCommencing != null)
            {
               DateTime monthCommencingNonNull = (DateTime)monthCommencing;

               // Current month, use today
               if (monthCommencingNonNull.Year == today.Year && monthCommencingNonNull.Month == today.Month)
               {
                  workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup); //112ms
                  elapsedWorkingDays = await workingDaysService.GetWorkingDaysElapsedInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup);
               }
               // Previous month use month commencing
               else
               {
                  workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(new DateTime(monthCommencingNonNull.Year, monthCommencingNonNull.Month, 1), siteIdsAsList, userId, dealerGroup); //112ms
                  elapsedWorkingDays = await workingDaysService.GetWorkingDaysElapsedInMonth(new DateTime(monthCommencingNonNull.Year, monthCommencingNonNull.Month, 1), siteIdsAsList, userId, dealerGroup);
               }

            }
            else
            {
               workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup); //112ms
               elapsedWorkingDays = await workingDaysService.GetWorkingDaysElapsedInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup);
            }
            ;

            result.DaysTotal = workingDaysInMonth;
            result.DaysElapsed = elapsedWorkingDays;
            result.TargetToDate = result.ThisMonthTarget * (workingDaysInMonth != 0 ? elapsedWorkingDays / workingDaysInMonth : 0);
            result.Vs = result.DoneInTimePeriod - result.TargetToDate;
         }

         // WEEK
         if (timePeriod == AftersalesTimePeriod.WTD)
         {
            WeekToDateWorkingDays wtdDays = await workingDaysService.DetermineWorkingDaysForCurrentWeek(userId, today, siteIdsAsList);

            decimal dailyTargetThisMonth = wtdDays.ThisMonth != 0 ? result.ThisMonthTarget / wtdDays.ThisMonth : 0;
            decimal dailyTargetLastMonth = wtdDays.LastMonth != 0 ? result.LastMonthTarget / wtdDays.LastMonth : 0;

            result.DaysTotal = wtdDays.LastMonthThisWeek + wtdDays.ThisMonthThisWeek;
            result.DaysElapsed = wtdDays.LastMonthThisWeekElapsed + wtdDays.ThisMonthThisWeekElapsed;
            result.TargetToDate = wtdDays.LastMonthThisWeek * dailyTargetLastMonth + wtdDays.ThisMonthThisWeek * dailyTargetThisMonth;
            result.Vs = result.DoneInTimePeriod - result.TargetToDate;
         }



         //YESTERDAY
         if (timePeriod == AftersalesTimePeriod.Yesterday)
         {
            decimal workingDaysYesterday = await workingDaysService.CountWorkingDaysBetweenAndIncluding(today.AddDays(-1), today.AddDays(-1), siteIdsAsList, userId);

            if (today.Day > 1)
            {
               decimal workingDaysInMonth = await workingDaysService.GetWorkingDaysInMonth(new DateTime(today.Year, today.Month, 1), siteIdsAsList, userId, dealerGroup);
               result.DaysTotal = workingDaysYesterday;
               result.DaysElapsed = workingDaysYesterday;
               result.TargetToDate = workingDaysYesterday * (workingDaysInMonth != 0 ? result.ThisMonthTarget / workingDaysInMonth : 0);
               result.Vs = result.DoneInTimePeriod - result.TargetToDate;
            }
            else
            {
               decimal workingDaysLastMonth = await workingDaysService.GetWorkingDaysInMonth(DateTime.Now.AddMonths(-1), siteIdsAsList, userId, dealerGroup);
               result.DaysTotal = workingDaysYesterday;
               result.DaysElapsed = workingDaysYesterday;

               if (workingDaysYesterday != 0)
               {
                  result.TargetToDate = workingDaysLastMonth != 0 ? result.LastMonthTarget / workingDaysLastMonth : 0;
               }
               else
               {
                  result.TargetToDate = 0;
                  result.DoneInTimePeriod = 0;
               }

               result.Vs = result.DoneInTimePeriod - result.TargetToDate;
            }
         }

         return result;
      }





      public async Task<List<ServiceDailyChaseItem>> GetPartsDailyChaseChartData(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup)
      {
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         //get data
         decimal target = await partsDataAccess.GetPartsMonthlyTarget(siteIds, monthCommencing, userId, showAllSites, dealerGroup);
         IEnumerable<ServiceDailyDone> done = await partsDataAccess.GetPartsDailyDone(siteIds, monthCommencing, userId, showAllSites, dealerGroup);
         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         workingDaysService.GetWeekendOpening(siteIdsAsList, userId, out decimal saturdayOpening, out decimal sundayOpening);
         IEnumerable<DayAndWorkingDayValue> dailyWorkingDays = await workingDaysService.GetDailyWorkingDaysForMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         decimal workingDaysInMonth = dailyWorkingDays.Select(x => x.WorkingDayValue).Sum();

         //walk through days building up results
         List<ServiceDailyChaseItem> results = new List<ServiceDailyChaseItem>();
         DateTime currentDay = monthCommencing.Date;
         decimal doneCum = 0;
         decimal tgtCum = 0;
         while (currentDay.Month == monthCommencing.Month)
         {
            var thisDayDoneItem = done.FirstOrDefault(x => x.Day == currentDay);
            decimal thisDayDone = thisDayDoneItem != null ? thisDayDoneItem.Done : 0;
            doneCum += thisDayDone;
            var thisDayTarget = dailyWorkingDays.First(x => x.DayDate == currentDay).WorkingDayValue / workingDaysInMonth * target;
            tgtCum += thisDayTarget;

            decimal? doneCumToAdd = null;
            if (currentDay.Date <= DateTime.Now.Date.AddDays(-1)) { doneCumToAdd = Math.Round(doneCum, MidpointRounding.AwayFromZero); }
            results.Add(new ServiceDailyChaseItem()
            {
               Label = currentDay,
               DoneCum = doneCumToAdd,
               TargetCum = Math.Round(tgtCum, MidpointRounding.AwayFromZero),
            });

            currentDay = currentDay.AddDays(1);
         }

         return results;
      }

      public async Task<List<ServiceDailyChaseItem>> GetPartsDailyChaseChartDataSpain(string siteIds, DateTime monthCommencing, int userId, Model.DealerGroupName dealerGroup)
      {
         decimal target = await partsDataAccess.GetPartsMonthlyTargetSpain(siteIds, monthCommencing, dealerGroup);
         IEnumerable<ServiceDailyDone> done = await partsDataAccess.GetPartsDailyDoneSpain(siteIds, monthCommencing, dealerGroup);

         List<int> siteIdsAsList = siteIds.Split(",").Select(x => int.Parse(x)).ToList();
         workingDaysService.GetWeekendOpening(siteIdsAsList, userId, out decimal saturdayOpening, out decimal sundayOpening);
         IEnumerable<DayAndWorkingDayValue> dailyWorkingDays = await workingDaysService.GetDailyWorkingDaysForMonth(monthCommencing, siteIdsAsList, userId, dealerGroup);

         decimal workingDaysInMonth = dailyWorkingDays.Select(x => x.WorkingDayValue).Sum();

         List<ServiceDailyChaseItem> results = new List<ServiceDailyChaseItem>();
         DateTime currentDay = monthCommencing.Date;
         decimal doneCum = 0;
         decimal tgtCum = 0;

         while (currentDay.Month == monthCommencing.Month)
         {
            var thisDayDoneItem = done.FirstOrDefault(x => x.Day == currentDay);
            decimal thisDayDone = thisDayDoneItem != null ? thisDayDoneItem.Done : 0;
            doneCum += thisDayDone;
            var thisDayTarget = dailyWorkingDays.First(x => x.DayDate == currentDay).WorkingDayValue / workingDaysInMonth * target;
            tgtCum += thisDayTarget;

            decimal? doneCumToAdd = null;
            if (currentDay.Date <= DateTime.Now.Date.AddDays(-1)) { doneCumToAdd = Math.Round(doneCum, MidpointRounding.AwayFromZero); }
            results.Add(new ServiceDailyChaseItem()
            {
               Label = currentDay,
               DoneCum = doneCumToAdd,
               TargetCum = Math.Round(tgtCum, MidpointRounding.AwayFromZero),
            });

            currentDay = currentDay.AddDays(1);
         }

         return results;
      }



      public async Task<IEnumerable<PartsDailySalesSiteRow>> GetPartsDailySales(DateTime startDate, string channels, int userId, Model.DealerGroupName dealerGroup)
      {
         bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

         string channelsString = ConvertChannels(channels, showAllSites);

         //get the underlying data
         string chanString = channelsString;
         IEnumerable<PartsDailySalesSiteRow> dailyRows = await partsDataAccess.GetPartsDailySales(startDate, chanString, userId, dealerGroup);

         if (!dailyRows.Any()) return new List<PartsDailySalesSiteRow>();


         List<int> siteIds = dailyRows.Select(x => x.SiteId).Distinct().ToList();

         //get working days stuff
         var holidaysInMonth = await workingDaysService.GetPublicHolidays(startDate, startDate.AddMonths(1).Date);
         IEnumerable<SiteWithOpening> siteWeekendOpening = await workingDaysService.GetSiteWeekendOpening(userId, showAllSites, dealerGroup);

         Dictionary<int, SiteAndElapsedWorkDays> daysPerSite = (await workingDaysService.GetElapsedDaysForSites(startDate, AftersalesTimePeriod.MTD, siteIds, userId, showAllSites, dealerGroup)).ToDictionary(x => x.SiteId);

         //add working days to sites
         foreach (var row in dailyRows)
         {
            if (row.IsSite)
            {
               SiteWithOpening thisSiteWeekendOpening = siteWeekendOpening.FirstOrDefault(x => x.SiteId == row.SiteId);
               decimal satOpening = thisSiteWeekendOpening != null ? thisSiteWeekendOpening.SatOpening : 0;
               decimal sunOpening = thisSiteWeekendOpening != null ? thisSiteWeekendOpening.SunOpening : 0;
               row.WorkingDaysElapsed = daysPerSite[row.SiteId].WorkingDaysElapsed;
               row.WorkingDaysInMonth = daysPerSite[row.SiteId].WorkingDaysInMonth;
            }
         }

         dailyRows = dailyRows.OrderBy(x => x.SiteId);

         //sum working days for regions
         foreach (var row in dailyRows)
         {
            if (row.IsRegion)
            {
               row.WorkingDaysElapsed = dailyRows.Where(x => x.RegionDescription == row.RegionDescription).Select(x => x.WorkingDaysElapsed).Sum();
               row.WorkingDaysInMonth = dailyRows.Where(x => x.RegionDescription == row.RegionDescription).Select(x => x.WorkingDaysInMonth).Sum();
            }
         }

         //add working days to total row
         dailyRows.First(x => x.IsTotal).WorkingDaysElapsed = dailyRows.Where(x => x.IsRegion).Select(x => x.WorkingDaysElapsed).Sum();
         dailyRows.First(x => x.IsTotal).WorkingDaysInMonth = dailyRows.Where(x => x.IsRegion).Select(x => x.WorkingDaysInMonth).Sum();

         //add ratios to all rows
         foreach (var row in dailyRows)
         {
            if (row.SiteId == 33)
            {
               { }
            }
            row.Target = row.FullMonthTarget / row.WorkingDaysInMonth * row.WorkingDaysElapsed;
            row.ActualVsTarget = row.Actual - row.Target;
            row.AverageDone = row.WorkingDaysElapsed != 0 ? row.Actual / row.WorkingDaysElapsed : 0;

            if (!row.IsTotal)
            {
               if (!row.IsRegion)
               {
                  row.ActualPerTechPerDay = row.TechHeadCount != 0 ? (row.Actual / row.TechHeadCount) / row.WorkingDaysElapsed : 0;
               }
            }
            else
            {
               row.ActualPerTechPerDay = row.TechHeadCount != 0 ? (row.Actual / row.TechHeadCount) : 0;
            }

            row.PerTechPerDayVsTarget = row.ActualPerTechPerDay - row.TechTgtPerDay;
         }

         decimal totalFTE = dailyRows.Where(x => x.IsSite).Sum(x => x.TechHeadCount);
         decimal totalTargetBySite = dailyRows.Where(x => x.IsSite).Sum(x => (x.TechTgtPerDay * x.TechHeadCount));
         decimal totalActualBySite = dailyRows.Where(x => x.IsSite).Sum(x => (x.ActualPerTechPerDay * x.TechHeadCount));
         decimal totalAverageDone = dailyRows.Where(x => x.IsSite).Sum(x => x.AverageDone);

         // Total techcounts regions/total
         foreach (var row in dailyRows)
         {
            if (row.IsRegion)
            {
               decimal regFTE = dailyRows.Where(x => x.IsSite && x.RegionDescription == row.Label).Sum(x => x.TechHeadCount);
               decimal regTargetBySite = dailyRows.Where(x => x.IsSite && x.RegionDescription == row.Label).Sum(x => (x.TechTgtPerDay * x.TechHeadCount));
               decimal regActualBySite = dailyRows.Where(x => x.IsSite && x.RegionDescription == row.Label).Sum(x => (x.ActualPerTechPerDay * x.TechHeadCount));
               decimal regAverageDone = dailyRows.Where(x => x.IsSite && x.RegionDescription == row.Label).Sum(x => x.AverageDone);

               row.TechTgtPerDay = regTargetBySite > 0 ? regTargetBySite / regFTE : 0;
               row.ActualPerTechPerDay = regActualBySite > 0 ? regActualBySite / regFTE : 0;
               row.PerTechPerDayVsTarget = row.ActualPerTechPerDay - row.TechTgtPerDay;
               row.AverageDone = regAverageDone;

            }

            if (row.IsTotal)
            {
               row.TechTgtPerDay = totalFTE > 0 ? totalTargetBySite / totalFTE : 0;
               row.ActualPerTechPerDay = totalFTE > 0 ? totalActualBySite / totalFTE : 0;
               row.PerTechPerDayVsTarget = row.ActualPerTechPerDay - row.TechTgtPerDay;
               row.AverageDone = totalAverageDone;
            }

         }
         ;

         return dailyRows;

      }

   }
}
