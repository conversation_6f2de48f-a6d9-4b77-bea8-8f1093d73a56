<ng-template #datePickerModalRef let-modal>

   <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">{{ heading }}</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
         <span aria-hidden="true">&times;</span>
      </button>
   </div>

   <div [ngClass]="constants.environment.customer" class="modal-body ">
      <div class="date-pickers-container">
         <div class="date-picker">
            <label for="fromDate">From</label>
            <input type="date" id="fromDate" name="fromDate" [value]="fromDate" [min]="minDate" [max]="maxDate"
                   (change)="setDate($event, 'from')">
         </div>
         <div class="date-picker">
            <label for="toDate">To</label>
            <input type="date" id="toDate" name="toDate" [value]="toDate" [min]="minDate" [max]="maxDate"
                   (change)="setDate($event, 'to')">
         </div>
      </div>
   </div>

   <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="modal.close('OKd')">OK</button>
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">
         {{ constants.translatedText.Common_Close }}
      </button>
   </div>
</ng-template>
