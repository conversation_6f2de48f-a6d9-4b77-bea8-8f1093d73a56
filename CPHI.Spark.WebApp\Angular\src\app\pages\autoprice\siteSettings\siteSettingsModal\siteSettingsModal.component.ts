import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SiteSettingsService } from '../siteSettings.service';
import { StrategyVersionVM } from 'src/app/model/StrategyVersionVM';
import { StrategyFull } from 'src/app/model/StrategyFull';
import { SiteSettings } from 'src/app/model/SiteSettings';

@Component({
  selector: 'siteSettingsModal',
  templateUrl: './siteSettingsModal.component.html',
  styleUrls: ['./siteSettingsModal.component.scss']
})
export class SiteSettingsModalComponent implements OnInit {

  minDate: string;

  constructor(
    public activeModal: NgbActiveModal,
    public service: SiteSettingsService
  ) { }

  ngOnInit(): void {
    this.minDate = this.service.datePipe.transform(this.service.constants.addDays(new Date(), 1), 'yyyy-MM-dd');
    // console.log(this.service.constants.autopriceEnvironment.allowChooseNewStrategy)
    // console.log(this.service.selections.user.permissions.canEditPricingStrategy)
  }

  closeModal() {
    console.log('Closing modal without saving');
    // Reset the modal and restore original site settings
    this.service.resetModal();
    this.activeModal.dismiss('Close button clicked');
  }

  togglePricesOnDay(dayShort: string) {
    if (this.service.selectedSite) {
      console.log(`Toggling UpdatePrices${dayShort} from ${this.service.selectedSite[`UpdatePrices${dayShort}`]} to ${!this.service.selectedSite[`UpdatePrices${dayShort}`]}`);
      this.service.selectedSite[`UpdatePrices${dayShort}`] = !this.service.selectedSite[`UpdatePrices${dayShort}`];
    }
  }

  save() {
    console.log('Saving site settings');

    // Save the site settings and subscribe to the result
    this.service.getDataService.saveSiteSettings({
      siteSettings: this.service.selectedSite
    }).subscribe(
      (res: any) => {
        console.log('Site settings saved successfully', res);

        // Update the row data with the saved settings
        const siteIndex = this.service.siteSettingsRowData.findIndex(
          site => site.RetailerSiteId === this.service.selectedSite.RetailerSiteId
        );

        if (siteIndex !== -1) {
          // Update the row data with the saved settings
          this.service.siteSettingsRowData[siteIndex] = new SiteSettings(res);

          // Refresh the grid
          if (this.service.gridApi) {
            this.service.gridApi.setRowData(this.service.siteSettingsRowData);
            this.service.gridApi.refreshCells();
            this.service.gridApi.redrawRows();
          }
        }

        // Show success message
        this.service.constants.toastSuccess('Saved site settings');

        // Close the modal with a result to indicate it was saved
        this.activeModal.close('Saved');
      },
      (error) => {
        console.error('Failed to save site settings', error);
        this.service.constants.toastDanger('Failed to save site settings');
      }
    );
  }


  chooseNewStrategy(strategy: StrategyFull) {
    this.service.selectedSite.StrategySelectionRuleSetId = strategy.StrategyId;
    this.service.selectedSite.StrategySelectionRuleSetUniqueIdForDg = strategy.UniqueIdForDg;
    this.service.selectedSite.StrategySelectionRuleSetName = strategy.Name;
  }
  chooseNewBuyingStrategy(strategy: StrategyFull) {
    this.service.selectedSite.BuyingStrategySelectionRuleSetId = strategy.StrategyId;
    this.service.selectedSite.BuyingStrategySelectionRuleSetUniqueIdForDg = strategy.UniqueIdForDg;
    this.service.selectedSite.BuyingStrategySelectionRuleSetName = strategy.Name;
  }
  chooseNewBuying2Strategy(strategy: StrategyFull) {
    this.service.selectedSite.BuyingStrategySelectionRuleSet2Id = strategy.StrategyId;
    this.service.selectedSite.BuyingStrategySelectionRuleSet2UniqueIdForDg = strategy.UniqueIdForDg;
    this.service.selectedSite.BuyingStrategySelectionRuleSet2Name = strategy.Name;
  }

  chooseNewTestStrategy(strategy: StrategyFull) {
    this.service.selectedSite.TestStrategySelectionRuleSetId = strategy.StrategyId;
    this.service.selectedSite.TestStrategySelectionRuleSetUniqueIdForDg = strategy.UniqueIdForDg;
    this.service.selectedSite.TestStrategySelectionRuleSetName = strategy.Name;
  }

  async reviewStrategy(strategyId: number) {
    const strategy = this.service.strategies.find(x => x.StrategyId == strategyId);
    await this.service.openStrategy(strategy);
  }
}
