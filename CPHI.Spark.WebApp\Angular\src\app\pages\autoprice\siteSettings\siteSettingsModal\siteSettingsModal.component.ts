import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SiteSettingsService } from '../siteSettings.service';
import { StrategyVersionVM } from 'src/app/model/StrategyVersionVM';
import { StrategyFull } from 'src/app/model/StrategyFull';

@Component({
  selector: 'siteSettingsModal',
  templateUrl: './siteSettingsModal.component.html',
  styleUrls: ['./siteSettingsModal.component.scss']
})
export class SiteSettingsModalComponent implements OnInit {

  minDate: string;

  constructor(
    public activeModal: NgbActiveModal,
    public service: SiteSettingsService
  ) { }

  ngOnInit(): void {
    this.minDate = this.service.datePipe.transform(this.service.constants.addDays(new Date(), 1), 'yyyy-MM-dd');
    // console.log(this.service.constants.autopriceEnvironment.allowChooseNewStrategy)
    // console.log(this.service.selections.user.permissions.canEditPricingStrategy)
  }

  closeModal() {
    // Reset the modal and restore original site settings
    this.service.resetModal();
    this.activeModal.dismiss('Close button clicked');
  }

  togglePricesOnDay(dayShort: string) {
    this.service.selectedSite[`UpdatePrices${dayShort}`] = !this.service.selectedSite[`UpdatePrices${dayShort}`];
  }

  save() {
    this.service.saveSiteSettings();
    // Close the modal with a result to indicate it was saved
    this.activeModal.close('Saved');
    // No need to reset the modal since we're saving the changes
  }


  chooseNewStrategy(strategy: StrategyFull) {
    this.service.selectedSite.StrategySelectionRuleSetId = strategy.StrategyId;
    this.service.selectedSite.StrategySelectionRuleSetUniqueIdForDg = strategy.UniqueIdForDg;
    this.service.selectedSite.StrategySelectionRuleSetName = strategy.Name;
  }
  chooseNewBuyingStrategy(strategy: StrategyFull) {
    this.service.selectedSite.BuyingStrategySelectionRuleSetId = strategy.StrategyId;
    this.service.selectedSite.BuyingStrategySelectionRuleSetUniqueIdForDg = strategy.UniqueIdForDg;
    this.service.selectedSite.BuyingStrategySelectionRuleSetName = strategy.Name;
  }
  chooseNewBuying2Strategy(strategy: StrategyFull) {
    this.service.selectedSite.BuyingStrategySelectionRuleSet2Id = strategy.StrategyId;
    this.service.selectedSite.BuyingStrategySelectionRuleSet2UniqueIdForDg = strategy.UniqueIdForDg;
    this.service.selectedSite.BuyingStrategySelectionRuleSet2Name = strategy.Name;
  }

  chooseNewTestStrategy(strategy: StrategyFull) {
    this.service.selectedSite.TestStrategySelectionRuleSetId = strategy.StrategyId;
    this.service.selectedSite.TestStrategySelectionRuleSetUniqueIdForDg = strategy.UniqueIdForDg;
    this.service.selectedSite.TestStrategySelectionRuleSetName = strategy.Name;
  }

  async reviewStrategy(strategyId: number) {
    const strategy = this.service.strategies.find(x => x.StrategyId == strategyId);
    await this.service.openStrategy(strategy);
  }
}
