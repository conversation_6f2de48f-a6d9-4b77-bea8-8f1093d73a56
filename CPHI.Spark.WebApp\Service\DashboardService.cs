﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.WebApp.Service.RRG;
using Microsoft.Extensions.Configuration;
using MoreLinq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace CPHI.Spark.WebApp.Service
{
    public interface IDashboardService
    {
        Task<WipSummary> GetWipSummary(string siteIds, int userId);
        Task<DashboardDataPack> GetDashboardData(DashboardDataParams parms);
        Task<DashboardDataPackSpainOverview> GetDashboardDataSpainOverview(DashboardDataSpainOverviewParams parms, int userId);
        Task<IEnumerable<SpainDailyNetOrderItem>> GetSpainDailyNetOrders(List<string> orderTypeTypesNew, List<string> orderTypeTypesUsed, DateTime weekStart, IEnumerable<int> siteIdsInts, bool isNew, int userId, string[] franchises, Model.DealerGroupName dealerGroup);
        Task<DashboardDataPackSpainAftersales> GetDashboardDataSpainAftersales(DashboardDataSpainAftersalesParams parms);
        Task<IEnumerable<AftersalesKPIRow>> GetDashboardDataSpainAftersalesKPIs(DashboardDataSpainAftersalesParams parms);
        Task<DashboardDataPackSpainAftersalesDetail> GetDashboardDataSpainAftersalesDetail(DashboardDataSpainAftersalesParams parms);
        Task<DateTime> GetMostRecentDateInDailyOrders();
    }

    public class DashboardService : IDashboardService
    {

        private readonly IDashboardDataAccess dashboardDataAccess;
        private readonly IDashboardCache dashboardCache;
        private readonly IServiceService serviceService;
        private readonly IPartsService partsService;
        private readonly IPartsStockService partsStockService;
        private readonly IVocService vocService;
        private readonly IUserService userService;
        private readonly IBookingsService bookingsService;
        private readonly IOrdersSpainService ratioOrdersService;
        private readonly ICommissionsService commissionService;
        private readonly IAlcopasDataAccess alcopasDataAccess;
        private readonly IDistrinetDataAccess distrinetDataAccess;
        private readonly IRegistrationsDataAccess registrationsDataAccess;

        private readonly int userId;


        public DashboardService
            (
            IDashboardDataAccess dashboardDataAccess,
            IDashboardCache dashboardCache,
            IServiceService serviceService,
            IPartsService partsService,
            IPartsStockService partsStockService,
            IVocService vocService,
            IUserService userService,
            ICommissionsService commissionService,
            IBookingsService bookingsService,
            IOrdersSpainService ratioOrdersService,
            IAlcopasDataAccess alcopasDataAccess,
            IDistrinetDataAccess distrinetDataAccess,
            IRegistrationsDataAccess registrationsDataAccess)
        {
            this.dashboardDataAccess = dashboardDataAccess;
            this.dashboardCache = dashboardCache;
            this.serviceService = serviceService;
            this.partsService = partsService;
            this.partsStockService = partsStockService;
            this.vocService = vocService;
            this.commissionService = commissionService;
            this.userService = userService;

            this.userId = userService.GetUserId();
            this.bookingsService = bookingsService;
            this.ratioOrdersService = ratioOrdersService;

            this.alcopasDataAccess = alcopasDataAccess;
            this.distrinetDataAccess = distrinetDataAccess;
            this.registrationsDataAccess = registrationsDataAccess;
        }



        public async Task<WipSummary> GetWipSummary(string siteIds, int userId)
        {
            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

            bool showAllSites = dealerGroup == Model.DealerGroupName.Vindis;

            return await dashboardDataAccess.GetWipSummary(siteIds, userId, showAllSites, dealerGroup);
        }


        public async Task<DashboardDataPackSpainAftersales> GetDashboardDataSpainAftersales(DashboardDataSpainAftersalesParams parms)
        {
            //work out parameters
            IEnumerable<int> userSites = userService.GetUserSiteIds();
            List<int> siteIdsInts = new List<int>() { };

            if (parms.SiteIds.Length > 0)
            {
                siteIdsInts = parms.SiteIds.Split(',').Select(x => int.Parse(x)).Intersect(userSites).OrderBy(x => x).ToList();
            }

            var firstDayOfMonth = new DateTime(parms.MonthStart.Year, parms.MonthStart.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            DateTime thisMonth = DateTime.UtcNow;

            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

            DashboardDataPackSpainAftersales result = new DashboardDataPackSpainAftersales();

            // Populate the result
            result.ServiceGuageMonth = await serviceService.GetServiceSummary(AftersalesTimePeriod.MTD, parms.SiteIds, userId, firstDayOfMonth); // Temp table if cur month
            result.PartsGuageMonth = await partsService.GetPartsSummary(AftersalesTimePeriod.MTD, parms.SiteIds, userId, firstDayOfMonth, dealerGroup); // Temp table if cur month
            result.TechControlAndConvRates = await dashboardDataAccess.GetPercentages(parms.SiteIds, userId, firstDayOfMonth, dealerGroup); // Temp table if cur month

            // If not current month, do not return data for these tiles
            if (parms.MonthStart.Year == thisMonth.Year && parms.MonthStart.Month == thisMonth.Month)
            {
                //result.Bookings = await bookingsService.GetServiceBookingsTileDataSpain(parms.SiteIds, userId, 16); // ,6759
                result.Bookings = await bookingsService.GetServiceBookingsDetailTileDataSpain(parms.SiteIds, userId, 14, dealerGroup);
                result.WipSummaries = await dashboardDataAccess.GetWipSummariesAftersalesSpain(parms.SiteIds, dealerGroup); // Temp table
            }
            else
            {
                result.WipSummaries = new List<WipSummary>() { new WipSummary() };
                result.Bookings = new List<ServiceBookingsDetailTileSummary> { new ServiceBookingsDetailTileSummary() };
            }


            return result;
        }


        public async Task<IEnumerable<AftersalesKPIRow>> GetDashboardDataSpainAftersalesKPIs(DashboardDataSpainAftersalesParams parms)
        {
            IEnumerable<AftersalesKPIRow> result = await dashboardDataAccess.GetDashboardDataSpainAftersalesKPIs(parms, userService.GetUserDealerGroupName());

            DateTime thisMonth = DateTime.UtcNow;

            if (parms.MonthStart.Year == thisMonth.Year && parms.MonthStart.Month == thisMonth.Month)
            {

            }
            else
            {
                foreach (var item in result)
                {
                    item.WIPParts1 = 0;
                    item.WIPParts2 = 0;
                    item.WIPParts3 = 0;
                    item.Labour1 = 0;
                    item.Labour2 = 0;
                    item.Labour3 = 0;
                }
            }


            List<AftersalesKPIRow> sortedList = result.OrderBy(obj => !obj.IsSite).ToList();

            return sortedList;
        }

        public async Task<DashboardDataPackSpainAftersalesDetail> GetDashboardDataSpainAftersalesDetail(DashboardDataSpainAftersalesParams parms)
        {
            //work out parameters
            IEnumerable<int> userSites = userService.GetUserSiteIds();
            List<int> siteIdsInts = new List<int>() { };

            if (parms.SiteIds.Length > 0)
            {
                siteIdsInts = parms.SiteIds.Split(',').Select(x => int.Parse(x)).Intersect(userSites).OrderBy(x => x).ToList();
            }

            var firstDayOfMonth = new DateTime(parms.MonthStart.Year, parms.MonthStart.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            DateTime thisMonth = DateTime.UtcNow;
            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

            DashboardDataPackSpainAftersalesDetail result = new DashboardDataPackSpainAftersalesDetail();

            result.Parts = await partsService.GetPartsDailyChaseChartDataSpain(parms.SiteIds, firstDayOfMonth, userId, dealerGroup);
            result.Service = await serviceService.GetServiceDailyChaseChartDataSpain(parms.SiteIds, firstDayOfMonth, userId);
            result.ServiceGuageMonth = await serviceService.GetServiceSummary(AftersalesTimePeriod.MTD, parms.SiteIds, userId, firstDayOfMonth); // 26,606
            result.PartsGuageMonth = await partsService.GetPartsSummary(AftersalesTimePeriod.MTD, parms.SiteIds, userId, firstDayOfMonth, dealerGroup); // 1757
            result.TechControlAndConvRates = await dashboardDataAccess.GetPercentages(parms.SiteIds, userId, firstDayOfMonth, dealerGroup); // 1,050
            result.TurnoverPerOperative = await dashboardDataAccess.GetTurnoverPerOperative(parms.SiteIds, userId, firstDayOfMonth, dealerGroup); // 1,050

            result.ServiceDetailedMechanical = await bookingsService.GetServiceDetailedTilesSpain(parms.MonthStart, parms.SiteIds, false);
            result.ServiceDetailedBodyshop = await bookingsService.GetServiceDetailedTilesSpain(parms.MonthStart, parms.SiteIds, true);

            // await bookingsService.GetServiceDetailedTilesSpain(parms.SiteIds, userId, "CARROCERIA");

            // If not current month, do not return data for these tiles
            if (parms.MonthStart.Year == thisMonth.Year && parms.MonthStart.Month == thisMonth.Month)
            {
                result.WipSummaries = await dashboardDataAccess.GetWipSummariesAftersalesSpain(parms.SiteIds, dealerGroup); // 25,768
                result.Bookings = await bookingsService.GetServiceBookingsDetailTileDataSpain(parms.SiteIds, userId, 14, dealerGroup);
            }
            else
            {
                result.WipSummaries = new List<WipSummary>() { new WipSummary(), new WipSummary() };
                result.Bookings = new List<ServiceBookingsDetailTileSummary> { new ServiceBookingsDetailTileSummary() };
            }

            return result;
        }

        public async Task<DashboardDataPackSpainOverview> GetDashboardDataSpainOverview(DashboardDataSpainOverviewParams parms, int userId)
        {
            //work out parameters
            IEnumerable<int> userSites = userService.GetUserSiteIds();
            List<int> siteIdsInts = new List<int>() { };
            if (parms.SiteIds.Length > 0)
            {
                siteIdsInts = parms.SiteIds.Split(',').Select(x => int.Parse(x)).Intersect(userSites).OrderBy(x => x).ToList();
            }

            var firstDayOfMonth = new DateTime(parms.MonthStart.Year, parms.MonthStart.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            DateTime thisMonth = DateTime.UtcNow;

            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

            //create result and return
            DashboardDataPackSpainOverview result = new DashboardDataPackSpainOverview();
            result.OrdersDonutSpainNew = await GetOrdersDonutSpainNew(parms.OrderTypeTypesNew, parms.FranchiseCodes, siteIdsInts, firstDayOfMonth, dealerGroup);
            result.OrdersDonutSpainUsed = await GetOrdersDonutSpainUsed(parms.OrderTypeTypesUsed, parms.FranchiseCodes, siteIdsInts, firstDayOfMonth, dealerGroup);
            result.InvoicedDonutDataNew = await GetInvoicedDonutNew(siteIdsInts, firstDayOfMonth, parms.FranchiseCodes, parms.OrderTypeTypesNew, dealerGroup);// keep null OTs for now parms.OrderTypeTypesNew);
            result.InvoicedDonutDataUsed = await GetInvoicedDonutUsed(siteIdsInts, firstDayOfMonth, parms.FranchiseCodes, parms.OrderTypeTypesUsed, dealerGroup);// keep null OTs for now parms.OrderTypeTypesNew);
            result.InvoicingPerformanceNew = await GetInvoicingPerformance("New", firstDayOfMonth, lastDayOfMonth, parms.SiteIds, parms.FranchiseCodes, dealerGroup);
            result.InvoicingPerformanceUsed = await GetInvoicingPerformance("Used", firstDayOfMonth, lastDayOfMonth, parms.SiteIds, parms.FranchiseCodes, dealerGroup);
            result.ScrapVehicles = await dashboardDataAccess.GetScrapVehicles(userId, parms.SiteIds, parms.FranchiseCodes, dealerGroup, firstDayOfMonth);
            //result.FixedAssetVehicles = await dashboardDataAccess.GetFixedAssetVehicles(userId, parms.SiteIds, parms.FranchiseCodes, firstDayOfMonth);
            result.AssigmentVehicles = await dashboardDataAccess.GetAssigmentVehicles(userId, parms.SiteIds, parms.FranchiseCodes, dealerGroup, firstDayOfMonth);
            result.ReconditioningSummary = await dashboardDataAccess.GetVehicleReconditioningTile(userId, parms.SiteIds, parms.FranchiseCodes, dealerGroup, firstDayOfMonth);
            result.OverageStockSummary = await CreateOverageStockSummarySpain(parms, siteIdsInts, dealerGroup);
            result.SpainDailyNetOrdersUsed = await dashboardDataAccess.GetSpainDailyNetOrdersUsed(parms.OrderTypeTypesUsed, parms.WeekStart, siteIdsInts, userId, parms.FranchiseCodes.ToArray(), dealerGroup);
            result.SpainDailyNetOrdersNew = await dashboardDataAccess.GetSpainDailyNetOrdersNew(parms.OrderTypeTypesNew, parms.WeekStart, siteIdsInts, userId, parms.FranchiseCodes.ToArray(), dealerGroup);
            result.Commissions = await commissionService.GetCommissionSummarySpain(firstDayOfMonth, siteIdsInts, dealerGroup);
            result.Alcopas = await alcopasDataAccess.GetAlcopaMonthSummary(parms.SiteIds, userId, firstDayOfMonth, dealerGroup);

            return result;
        }

        

        private async Task<IEnumerable<OverageStockSummaryItem>> CreateOverageStockSummarySpain(DashboardDataSpainOverviewParams parms, List<int> siteIdsInts, Model.DealerGroupName dealerGroup)
        {
            return await dashboardDataAccess.GetOverageStockSummarySpain(siteIdsInts, parms.FranchiseCodes, parms.MonthStart, userId, userService.GetUserDealerGroupName());
        }

        private async Task<IEnumerable<AftersalesRunRateSummary>> GetRunRatesSpainAftersales(DashboardDataSpainOverviewParams parms, List<int> siteIdsInts)
        {
            return await dashboardDataAccess.GetRunRatesSpainAftersales(siteIdsInts, parms.FranchiseCodes, parms.MonthStart, userService.GetUserDealerGroupName());
        }


        public async Task<DashboardDataPack> GetDashboardData(DashboardDataParams parms)
        {
            DashboardDataPack result = new DashboardDataPack();
            IEnumerable<int> userSites = userService.GetUserSiteIds();
            int[] siteIdsInts = new int[] { };
            if (parms.SiteIds.Length > 0)
            {
                siteIdsInts = parms.SiteIds.Split(',').Select(x => int.Parse(x)).Intersect(userSites).ToArray();
            }

            Array.Sort(siteIdsInts);
            DateTime weekStart = DateTime.Now;

            DateTime relevantMonth = DateTime.UtcNow;
            var firstDayOfMonth = new DateTime(relevantMonth.Year, relevantMonth.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

            if (parms.WeekStart.HasValue) { weekStart = ((DateTime)(parms.WeekStart)).Date; }
            DateTime weekStartOrdersTileNew = weekStart;
            DateTime weekStartOrdersTileUsed = weekStart;
            DateTime weekStartOrdersTileFleet = weekStart;
            DateTime weekStartOrdersActivitiesTile = weekStart;
            if (parms.WeekStartActivitiesTile.HasValue) { weekStartOrdersActivitiesTile = ((DateTime)(parms.WeekStartActivitiesTile)).Date; }
            if (parms.WeekStartOrdersTileNew.HasValue) { weekStartOrdersTileNew = ((DateTime)(parms.WeekStartOrdersTileNew)).Date; }
            if (parms.WeekStartOrdersTileUsed.HasValue) { weekStartOrdersTileUsed = ((DateTime)(parms.WeekStartOrdersTileUsed)).Date; }
            if (parms.WeekStartOrdersTileFleet.HasValue) { weekStartOrdersTileFleet = ((DateTime)(parms.WeekStartOrdersTileFleet)).Date; }

            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();

            if (parms.DataItems.Contains(DashboardDataItem.ActivityLevelsAndOverdues)) { result.ActivityLevelsAndOverdues = await GetActivityLevelsAndOverdues(siteIdsInts, weekStartOrdersActivitiesTile, dealerGroup); } //160
            if (parms.DataItems.Contains(DashboardDataItem.DepartmentDealBreakdown)) { result.DepartmentDealBreakdown = await dashboardCache.GetDepartmentDealBreakdown(siteIdsInts, dealerGroup); }  //1  92

            if (parms.DataItems.Contains(DashboardDataItem.DonutDataSets)) { result.DonutDataSetsMonth = await dashboardCache.GetPerformanceForMonthDonuts(siteIdsInts, firstDayOfMonth, null, null, dealerGroup); }  //we don't use fran codes
            if (parms.DataItems.Contains(DashboardDataItem.DonutDataSetsWTD)) { result.DonutDataSetsWTD = await dashboardCache.GetPerformanceForWTDDonuts(siteIdsInts, null, dealerGroup); }  //we don't use fran codes
            if (parms.DataItems.Contains(DashboardDataItem.DonutDataSetsYesterday)) { result.DonutDataSetsYesterday = await dashboardCache.GetPerformanceForYesterdayDonuts(siteIdsInts, null, dealerGroup); }  //we don't use fran codes

            if (parms.DataItems.Contains(DashboardDataItem.DepartmentProfitPerUnitsMonth)) { result.DepartmentProfitPerUnitsMonth = await dashboardCache.GetDepartmentProfitPerUnits(siteIdsInts, "Month", dealerGroup); }  //
            if (parms.DataItems.Contains(DashboardDataItem.DepartmentProfitPerUnitsWTD)) { result.DepartmentProfitPerUnitsWTD = await dashboardCache.GetDepartmentProfitPerUnits(siteIdsInts, "WTD", dealerGroup); }  //
            if (parms.DataItems.Contains(DashboardDataItem.DepartmentProfitPerUnitsYesterday)) { result.DepartmentProfitPerUnitsYesterday = await dashboardCache.GetDepartmentProfitPerUnits(siteIdsInts, "Yesterday", dealerGroup); }  //

            if (parms.DataItems.Contains(DashboardDataItem.DailyNetOrdersNew)) { result.DailyNetOrdersNew = await dashboardCache.GetDailyNetOrders(weekStartOrdersTileNew, siteIdsInts, "New", dealerGroup); } //60
            if (parms.DataItems.Contains(DashboardDataItem.DailyNetOrdersFleet)) { result.DailyNetOrdersFleet = await dashboardCache.GetDailyNetOrders(weekStartOrdersTileFleet, siteIdsInts, "Fleet", dealerGroup); } //60
            if (parms.DataItems.Contains(DashboardDataItem.DailyNetOrdersUsed)) { result.DailyNetOrdersUsed = await dashboardCache.GetDailyNetOrders(weekStartOrdersTileUsed, siteIdsInts, "Used", dealerGroup); } //60

            if (parms.DataItems.Contains(DashboardDataItem.DailyNetOrdersCancellationsNew)) { result.DailyNetOrdersCancellationsNew = await dashboardCache.GetDailyNetOrdersCancellations(weekStartOrdersTileNew, siteIdsInts, "New", dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.DailyNetOrdersCancellationsUsed)) { result.DailyNetOrdersCancellationsUsed = await dashboardCache.GetDailyNetOrdersCancellations(weekStartOrdersTileUsed, siteIdsInts, "Used", dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.DailyNetOrdersCancellationsFleet)) { result.DailyNetOrdersCancellationsFleet = await dashboardCache.GetDailyNetOrdersCancellations(weekStartOrdersTileFleet, siteIdsInts, "Fleet", dealerGroup); }

            if (parms.DataItems.Contains(DashboardDataItem.FinanceAndAddOn)) { result.FinanceAddOnSummary = await GetFinanceAddonSummary(parms.SiteIds); } //26
            if (parms.DataItems.Contains(DashboardDataItem.Registrations)) { result.RegistrationSummaries = await GetRegistrationsSummary(parms.SiteIds, dealerGroup); } //230
            if (parms.DataItems.Contains(DashboardDataItem.UsedStockMerchandising)) { result.UsedStockMerchandising = await dashboardCache.GetUsedStockMerchandising(siteIdsInts, dealerGroup); }   //500
            if (parms.DataItems.Contains(DashboardDataItem.UsedStockMerchandisingBySite)) { result.UsedStockMerchandisingBySite = await dashboardCache.GetUsedStockMerchandisingBySite(siteIdsInts, dealerGroup); } // 300  1300
            if (parms.DataItems.Contains(DashboardDataItem.OverageStockSummary)) { result.OverageStockSummary = await dashboardDataAccess.GetOverageStockSummary(parms.SiteIds, null, null, dealerGroup); } //260  1100
            if (parms.DataItems.Contains(DashboardDataItem.DeliveredInTime)) { result.DeliveredInTime = await dashboardDataAccess.GetDeliveredInTime(parms.SiteIds, dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.UsedStockHealth)) { result.UsedStockHealth = await dashboardDataAccess.GetUsedStockHealth(parms.SiteIds, dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.UsedDelivered)) { result.UsedDelivered = await GetUsedDelivered(parms.SiteIds, dealerGroup); }


            //Aftersales
            if (parms.DataItems.Contains(DashboardDataItem.CitNowsVsWips)) { result.CitNowVsWips = await GetCitNowSummary(parms.SiteIds, dealerGroup, userId); }
            if (parms.DataItems.Contains(DashboardDataItem.EvhcDoneVsWips)) { result.EvhcDoneVsWips = await dashboardDataAccess.GetEvhcSummaryDoneVsWips(parms.SiteIds, userId, dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.EvhcQuotedAndSold)) { result.EvhcQuotedAndSold = await dashboardDataAccess.GetEvhcSummaryRedAmberConverted(parms.SiteIds, userId, dealerGroup); }

            if (parms.DataItems.Contains(DashboardDataItem.ServiceGuageMonth)) { result.ServiceGuageMonth = await serviceService.GetServiceSummary(AftersalesTimePeriod.MTD, parms.SiteIds, userId, null); } //250ms
            if (parms.DataItems.Contains(DashboardDataItem.ServiceGuageWTD)) { result.ServiceGuageWTD = await serviceService.GetServiceSummary(AftersalesTimePeriod.WTD, parms.SiteIds, userId, null); } //250ms
            if (parms.DataItems.Contains(DashboardDataItem.ServiceGuageYesterday)) { result.ServiceGuageYesterday = await serviceService.GetServiceSummary(AftersalesTimePeriod.Yesterday, parms.SiteIds, userId, null); } //250ms

            if (parms.DataItems.Contains(DashboardDataItem.PartsGuageMonth)) { result.PartsGuageMonth = await partsService.GetPartsSummary(AftersalesTimePeriod.MTD, parms.SiteIds, userId, null, dealerGroup); } //250ms
            if (parms.DataItems.Contains(DashboardDataItem.PartsGuageWTD)) { result.PartsGuageWTD = await partsService.GetPartsSummary(AftersalesTimePeriod.WTD, parms.SiteIds, userId, null, dealerGroup); } //250ms
            if (parms.DataItems.Contains(DashboardDataItem.PartsGuageYesterday)) { result.PartsGuageYesterday = await partsService.GetPartsSummary(AftersalesTimePeriod.Yesterday, parms.SiteIds, userId, null, dealerGroup); } //250ms

            if (parms.DataItems.Contains(DashboardDataItem.PartsStockOver1Yr)) { result.PartsStockOver1Yr = await partsStockService.GetPartsStockOver1Yr(parms.SiteIds, userId, dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.PartsStock6To12)) { result.PartsStock6To12 = await partsStockService.GetPartsStock6To12(parms.SiteIds, dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.AgedWips)) { result.AgedWipLines = await dashboardDataAccess.GetAgedWipsSummary(parms.SiteIds, userId, dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.VocNPS)) { result.VocSummary = await vocService.GetVocSummary(parms.SiteIds, userId); }
            if (parms.DataItems.Contains(DashboardDataItem.WipAgeingSummary)) { result.WipSummary = await dashboardDataAccess.GetWipSummary(parms.SiteIds, userId, false, dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.ServiceBookings)) { result.Bookings = await bookingsService.GetServiceBookingsTileData(siteIdsInts, userId, 14, dealerGroup); }
            if (parms.DataItems.Contains(DashboardDataItem.ServiceBookingsNext5)) { result.BookingsNext5 = await bookingsService.GetServiceBookingsTileData(siteIdsInts, userId, 5, dealerGroup); }

            //Site compare
            if (parms.DataItems.Contains(DashboardDataItem.SiteCompare)) { result.SiteCompareRows = await GetSiteCompare(parms.Department, (bool)parms.IsMargin, (DashboardTimePeriod)parms.TimePeriod); }
            if (parms.DataItems.Contains(DashboardDataItem.TodayMap)) { result.SiteTodayMapItems = await GetSiteCompareTodayMap(parms.Department.ToString(), dealerGroup); }


            //Spain new ones
            if (parms.DataItems.Contains(DashboardDataItem.DataOriginsUpdates)) { result.DataOriginsUpdates = await GetDataOriginsUpdates(dealerGroup); }





            return result;
        }

        private async Task<IEnumerable<RegistrationsSummary>> GetRegistrationsSummary(string siteIds, Model.DealerGroupName dealerGroup)
        {
            List<RegistrationsSummary> registrationsSummarys = new List<RegistrationsSummary>();
            var siteIdArray = siteIds.Split(',').ToArray();

            DateTime startOfCurrentQuater = HelperService.GetQuarterStartingDate(DateTime.UtcNow);

            var daciaOrderResult = await registrationsDataAccess.GetOemOrdersSiteRows(startOfCurrentQuater, "DaciaOrders", userId, false, dealerGroup);
            var lMTOrdersResult = await registrationsDataAccess.GetOemOrdersSiteRows(startOfCurrentQuater, "LMTOrders", userId, false, dealerGroup);

            daciaOrderResult = daciaOrderResult.Where(d => d.IsSite == true && siteIdArray.Contains(d.SiteId.ToString())).ToList();
            registrationsSummarys.Add(new RegistrationsSummary()
            {
                TargetType = "DaciaRegs",
                Target = daciaOrderResult.Sum(d => d.Target),
                Registered = daciaOrderResult.Sum(d => d.NetOrders)
            });

            lMTOrdersResult = lMTOrdersResult.Where(d => d.IsSite == true && siteIdArray.Contains(d.SiteId.ToString())).ToList();
            registrationsSummarys.Add(new RegistrationsSummary()
            {
                TargetType = "LMTRegs",
                Target = lMTOrdersResult.Sum(d => d.Target),
                Registered = lMTOrdersResult.Sum(d => d.NetOrders),
            });

            return registrationsSummarys;
        }



        private async Task<DonutMonthlyData> GetInvoicedDonutNew(List<int> siteIdsInts, DateTime monthStart, List<string> franchiseCodes, List<string> orderTypes, Model.DealerGroupName dealerGroup)
        {
            var invoicedDonut = new DonutMonthlyData();
            //Current month Run
            IEnumerable<DonutMonthlyData> allDepartmentsData = await dashboardCache.GetPerformanceForMonthDonuts(siteIdsInts.ToArray(), monthStart, franchiseCodes, orderTypes, dealerGroup);
            return DonutService.BuildInvoicedDonutNew(allDepartmentsData);

        }

        private async Task<DonutMonthlyData> GetInvoicedDonutUsed(List<int> siteIdsInts, DateTime monthStart, List<string> franchiseCodes, List<string> orderTypes, Model.DealerGroupName dealerGroup)
        {
            var invoicedDonut = new DonutMonthlyData();
            //Current month Run
            IEnumerable<DonutMonthlyData> allDepartmentsData = await dashboardCache.GetPerformanceForMonthDonuts(siteIdsInts.ToArray(), monthStart, franchiseCodes, orderTypes, dealerGroup);
            return DonutService.BuildInvoicedDonutUsed(allDepartmentsData);

        }

       

        private async Task<InvoicingPerformance> GetInvoicingPerformance(string type, DateTime fromDate, DateTime toDate, string siteIds, List<string> franchiseCodes, Model.DealerGroupName dealerGroup)
        {

            var invoicedProfits = await dashboardDataAccess.GetInvoicedProfit(type, fromDate, toDate, siteIds, franchiseCodes, dealerGroup);
            IEnumerable<UnitInvoicingVsTarget> unitActualsAndTgt = await dashboardDataAccess.GetUnitInvoicingVsTarget(type, fromDate, toDate, siteIds, franchiseCodes, dealerGroup);

            IList<InvoicingPerformanceBrandData> invoicingPerformanceBrandDatas = new List<InvoicingPerformanceBrandData>();

            foreach (var invoicedProfit in invoicedProfits)
            {
                var thisBrandUnitActualAndTgt = unitActualsAndTgt.Where(u => u.Franchise == invoicedProfit.Franchise).FirstOrDefault();
                decimal unitAchievementThisBrand = 0;
                if (thisBrandUnitActualAndTgt != null)
                {
                    unitAchievementThisBrand = thisBrandUnitActualAndTgt.AchievementPercent;
                }
                invoicingPerformanceBrandDatas.Add(new InvoicingPerformanceBrandData()
                {
                    Brand = invoicedProfit.Franchise,
                    InvoicedProfit = invoicedProfit.MarginPercent,
                    UnitInvoicingVsTgt = unitAchievementThisBrand
                });
            }


            string[] order = { "Alpine", "Renault", "Dacia", "Non-franchise" };
            var ordered = invoicingPerformanceBrandDatas.OrderBy(item =>
            {
                int index = Array.IndexOf(order, item.Brand);
                if (index == -1) return int.MaxValue;
                return index;
            });


            var result = new InvoicingPerformance()
            {
                BreakdownByBrand = ordered
            };


            return result;

        }

        private async Task<OrdersDonutSpainData> GetOrdersDonutSpainNew(List<string> orderTypeTypes, List<string> franchiseCodes, List<int> siteIds, DateTime monthStart, Model.DealerGroupName dealerGroup)
        {
            OrdersDonutSpainParams parms = new OrdersDonutSpainParams()
            {
                OrderTypeTypes = orderTypeTypes,
                SiteIds = siteIds,
                FranchiseCodes = franchiseCodes,
                MonthStart = monthStart
            };

            return await distrinetDataAccess.GetOrdersDonutSpainNew(parms, userId, dealerGroup);
        }

        private async Task<OrdersDonutSpainData> GetOrdersDonutSpainUsed(List<string> orderTypeTypes, List<string> franchiseCodes, List<int> siteIds, DateTime monthStart, Model.DealerGroupName dealerGroup)
        {
            OrdersDonutSpainParams parms = new OrdersDonutSpainParams()
            {
                OrderTypeTypes = orderTypeTypes,
                SiteIds = siteIds,
                FranchiseCodes = franchiseCodes,
                MonthStart = monthStart
            };

            return await ratioOrdersService.GetOrdersDonutSpainUsed(parms, userId, dealerGroup);
        }


        private DateTime? getDateOrNull(IEnumerable<GlobalParamLastUpdated> lastUpdateds, string searchValue)
        {
            DateTime? result = null;
            var match = lastUpdateds.FirstOrDefault(x => x.DataOrigin == searchValue);
            if (match != null)
            {
                result = match.LastUpdated;
            }
            return result;
        }

        private async Task<IEnumerable<DataOriginsUpdate>> GetDataOriginsUpdates(Model.DealerGroupName dealerGroup)
        {
            IEnumerable<GlobalParamLastUpdated> lastUpdateds = await dashboardDataAccess.GetDataOriginUpdates(dealerGroup);
            List<DataOriginsUpdate> results = new List<DataOriginsUpdate>()
            {
                new DataOriginsUpdate(){DataOrigin="Quiter",DataTable = "Quiter", UpdateType="Automatic", UpdateTiming="Cada 24H",LastUpdate=getDateOrNull(lastUpdateds,"quiterUpdateDate")},
                new DataOriginsUpdate(){DataOrigin="RATIO",DataTable = "RATIO", UpdateType="Automatic", UpdateTiming="Cada 30 Min.",LastUpdate=getDateOrNull(lastUpdateds,"ordersUpdateDate")} ,
                new DataOriginsUpdate(){DataOrigin="BPC",DataTable = "Email", UpdateType="Manual", UpdateTiming="Bi-annual",LastUpdate=getDateOrNull(lastUpdateds,"bpcUpdateDate")} ,
                new DataOriginsUpdate(){DataOrigin="Distrinet Cartera Madrid",DataTable = "VN_DISTRINET_CARTERA", UpdateType="Automatic", UpdateTiming="1H",LastUpdate=getDateOrNull(lastUpdateds,"distrinetBookUpdateDateMadrid")} ,
                new DataOriginsUpdate(){DataOrigin="Distrinet Cartera Valencia",DataTable = "VN_DISTRINET_CARTERA", UpdateType="Automatic", UpdateTiming="1H",LastUpdate=getDateOrNull(lastUpdateds,"distrinetBookUpdateDateValencia")} ,

                new DataOriginsUpdate(){DataOrigin="Distrinet Stock Madrid",DataTable = "VN_DISTRINET_STOCK", UpdateType="Automatic", UpdateTiming="1H",LastUpdate=getDateOrNull(lastUpdateds,"distrinetStockUpdateDateMadrid")} ,
                new DataOriginsUpdate(){DataOrigin="Distrinet Stock Valencia",DataTable = "VN_DISTRINET_STOCK", UpdateType="Automatic", UpdateTiming="1H",LastUpdate=getDateOrNull(lastUpdateds,"distrinetStockUpdateDateValencia")} ,

                new DataOriginsUpdate(){DataOrigin="Distrinet Pedidos Madrid",DataTable = "VN_DISTRINET_PEDIDOS", UpdateType="Automatic", UpdateTiming="1H",LastUpdate=getDateOrNull(lastUpdateds,"distrinetOrdersUpdateDateMadrid")} ,
                new DataOriginsUpdate(){DataOrigin="Distrinet Pedidos Valencia",DataTable = "VN_DISTRINET_PEDIDOS", UpdateType="Automatic", UpdateTiming="1H",LastUpdate=getDateOrNull(lastUpdateds,"distrinetOrdersUpdateDateValencia")} ,

                new DataOriginsUpdate(){DataOrigin="ALCOPA",DataTable = "ALCOPA", UpdateType="Automatic", UpdateTiming="Cada 24H",LastUpdate=getDateOrNull(lastUpdateds,"alcopaUpdateDate")} ,
                new DataOriginsUpdate(){DataOrigin="Comisiones SAP-BPU",DataTable = "COMISIONES", UpdateType="Manual", UpdateTiming="Mensual",LastUpdate=getDateOrNull(lastUpdateds,"commissionUpdateDate")} ,

                new DataOriginsUpdate(){DataOrigin="Eserpubli", DataTable = "Eserpubli WS", UpdateType="Automatic", UpdateTiming="Cada 24H", LastUpdate=getDateOrNull(lastUpdateds,"eserpubliUpdateDate")} ,

                
            };
            return results;
        }

        //Combines two things into one
        private async Task<ActivityLevelsAndOverdues> GetActivityLevelsAndOverdues(int[] siteIdsInts, DateTime startDate, Model.DealerGroupName dealerGroup)
        {
            return new ActivityLevelsAndOverdues()
            {
                ActivityLevels = await dashboardCache.GetActivityLevels(siteIdsInts, startDate, dealerGroup),
                Overdues = await dashboardCache.GetOverdueSummary(siteIdsInts, dealerGroup)
            };
        }

        private async Task<CitNowVsWips> GetCitNowSummary(string siteIds, Model.DealerGroupName dealerGroup, int userId)
        {
            if (dealerGroup == Model.DealerGroupName.Vindis)
            {
                return await dashboardDataAccess.GetCitNowSummaryVindis(siteIds, userId, dealerGroup);
            }
            else
            {
                return await dashboardDataAccess.GetCitNowSummary(siteIds, userId, dealerGroup);

            }
        }


        private async Task<IEnumerable<UsedDeliveredRow>> GetUsedDelivered(string siteIds, Model.DealerGroupName dealerGroup)
        {
            IEnumerable<UsedDeliveredRow> rows = await dashboardDataAccess.GetUsedDeliveredTile(siteIds, dealerGroup);

            rows = rows.OrderBy(row => row.SiteId == 0 ? 1 : 0); // If SiteId is 0, it gets a value of 1 which is higher, hence it moves to the end

            return rows;
        }

        private async Task<FinanceAddOnSummary> GetFinanceAddonSummary(string siteIds)
        {
            IEnumerable<FinanceAddOnSummaryDataItem> dataItems = await dashboardDataAccess.GetFinanceAddonSummary(siteIds, userService.GetUserDealerGroupName());
            var newDept = dataItems.FirstOrDefault(x => x.Department == "New");
            var usedDept = dataItems.FirstOrDefault(x => x.Department == "Used");
            decimal newUnits = newDept != null ? newDept.Units : 0;
            decimal newProfit = newDept != null ? newDept.Profit : 0;
            decimal usedUnits = usedDept != null ? usedDept.Units : 0;
            decimal usedProfit = usedDept != null ? usedDept.Profit : 0;
            return new FinanceAddOnSummary()
            {
                New = newUnits != 0 ? newProfit / newUnits : 0,
                Used = usedUnits != 0 ? usedProfit / usedUnits : 0,
                Total = (newUnits + usedUnits) != 0 ? (newProfit + usedProfit) / (newUnits + usedUnits) : 0,
            };
        }



        private async Task<IEnumerable<SiteCompareRow>> GetSiteCompare(string department, bool showMargin, DashboardTimePeriod timePeriod)
        {

            Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            if (department == "Used" || department == "New" || department == "Fleet")
            {
                IEnumerable<SiteCompareRow> result = await dashboardDataAccess.GetSiteCompareSalesPerf(department, showMargin, timePeriod.ToString(), dealerGroup);
                return result;
            }

            if (department == "Parts" || department == "Service")
            {

                bool includeNonLabour = dealerGroup != Model.DealerGroupName.Vindis;

                IEnumerable<SiteCompareRow> result = await dashboardDataAccess.GetSiteCompareAfterSalesPerf(department, showMargin, timePeriod.ToString(), includeNonLabour, dealerGroup);
                return result;
            }

            return null;
        }


        private async Task<IEnumerable<SiteCompareRow>> GetSiteCompareTodayMap(string department, Model.DealerGroupName dealerGroup)
        {
            return await dashboardDataAccess.GetSiteCompareTodayMap(department, dealerGroup);
        }


        public async Task<IEnumerable<SpainDailyNetOrderItem>> GetSpainDailyNetOrders(List<string> orderTypeTypesNew, List<string> orderTypeTypesUsed, DateTime weekStart, IEnumerable<int> siteIdsInts, bool isNew, int userId,
            string[] franchises, Model.DealerGroupName dealerGroup)
        {
            if (isNew)
            {
                return await dashboardDataAccess.GetSpainDailyNetOrdersNew(orderTypeTypesNew, weekStart, siteIdsInts, userId, franchises, dealerGroup);
            }

            return await dashboardDataAccess.GetSpainDailyNetOrdersUsed(orderTypeTypesUsed, weekStart, siteIdsInts, userId, franchises, dealerGroup);
        }


        public async Task<DateTime> GetMostRecentDateInDailyOrders()
        {
            return await dashboardDataAccess.GetMostRecentDateInDailyOrders(userService.GetUserDealerGroupName());
        }
    }

}
