﻿using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Security.Claims;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;
using CPHI.Spark.Model.Services;
using StockPulse.WebApi.Model.ViewModels;
using CPHI.Spark.DataAccess;
using System.Configuration;
using Microsoft.Extensions.Configuration;
using System.Linq;

namespace CPHI.Spark.WebApp.Service
{
    public interface IGlobalParamService
    {
        Task<IEnumerable<GlobalParam>> GetAll(Model.DealerGroupName dealerGroup);
      Task<string> GetSpecificParamTextValue(DealerGroupName dealerGroup, string description);
      Task<decimal?> GetSpecificParamValue(DealerGroupName dealerGroup, string description);
   }




    public class GlobalParamService: IGlobalParamService
    {
        private readonly IConfiguration _configuration;

        public GlobalParamService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        


        public async Task<IEnumerable<GlobalParam>> GetAll(Model.DealerGroupName dealerGroup)
        {
            string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
            string _connectionString = _configuration.GetConnectionString(dgName);
            var globalParamDataAccess = new GlobalParamDataAccess(_connectionString);

            return await globalParamDataAccess.GetAll(dealerGroup);
        }

      public async Task<decimal?> GetSpecificParamValue(Model.DealerGroupName dealerGroup, string description)
      {
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         string _connectionString = _configuration.GetConnectionString(dgName);
         var globalParamDataAccess = new GlobalParamDataAccess(_connectionString);
         var allParams = await globalParamDataAccess.GetAll(dealerGroup);
         var param = allParams.FirstOrDefault(x => x.Description == description);
         return param?.Value;
      }
      public async Task<string> GetSpecificParamTextValue(Model.DealerGroupName dealerGroup, string description)
      {
         string dgName = DealerGroupConnectionNameService.GetConnectionName(dealerGroup);
         string _connectionString = _configuration.GetConnectionString(dgName);
         var globalParamDataAccess = new GlobalParamDataAccess(_connectionString);
         var allParams = await globalParamDataAccess.GetAll(dealerGroup);
         var param = allParams.FirstOrDefault(x => x.Description == description);
         return param?.TextValue;
      }

   }
}
