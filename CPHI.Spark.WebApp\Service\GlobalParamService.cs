﻿using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Security.Claims;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using StockPulse.WebApi.Model.ViewModels;
using CPHI.Spark.DataAccess;
using System.Configuration;
using Microsoft.Extensions.Configuration;

namespace CPHI.Spark.WebApp.Service
{
    public interface IGlobalParamService
    {
        Task<IEnumerable<GlobalParam>> GetAll(Model.DealerGroupName dealerGroup);
    }




    public class GlobalParamService: IGlobalParamService
    {
        private readonly IConfiguration _configuration;

        public GlobalParamService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        


        public async Task<IEnumerable<GlobalParam>> GetAll(Model.DealerGroupName dealerGroup)
        {
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = _configuration.GetConnectionString(dgName);
            var globalParamDataAccess = new GlobalParamDataAccess(_connectionString);

            return await globalParamDataAccess.GetAll(dealerGroup);
        }

      
    }
}
