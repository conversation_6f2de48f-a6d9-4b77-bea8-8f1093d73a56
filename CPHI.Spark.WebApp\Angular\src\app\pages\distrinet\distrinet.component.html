<nav class="navbar">

  <nav class="generic">


    <h4 id="pageTitle">
      <div>

        {{constants.translatedText.Distrinet_Title}}
        
      </div>
    </h4>


    <!-- Site selector -->
    <div class="buttonGroup">
      <ng-container *ngIf="constants.environment.dealDone_showRRGSitePicker">
        <sitePickerRRG [allSites]="constants.sitesActiveSales" [sitesFromParent]="selections.selectedSites"
          [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePickerRRG>
      </ng-container>

      <ng-container *ngIf="constants.environment.dealDone_showVindisSitePicker">
        <sitePicker [allSites]="constants.sitesActiveSales" [sitesFromParent]="selections.selectedSites"
          [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePicker>
      </ng-container>


      <!-- Franchise selector -->
      <franchisePicker *ngIf="this.constants.environment.franchisePicker"
        [franchisesFromParent]="service.chosenFranchises" [buttonClass]="'buttonGroupCenter'"
        (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>


    </div>


    <!-- RP rough thing to pick Origins -->
    <div class="buttonGroup">
      <button class="btn btn-primary" *ngFor="let each of service.originTypes" (click)="toggleOriginType(each)"
        [ngClass]="{'active':isOriginActive(each)}">

        <span *ngIf="each=='Book'"><distrinetSourceDataUpdate [type]="constants.translatedText.Book"></distrinetSourceDataUpdate></span>
        <span *ngIf="each=='Orders'"><distrinetSourceDataUpdate [type]="constants.translatedText.Orders_Capitals"></distrinetSourceDataUpdate></span>
        <span *ngIf="each=='Stock'"><distrinetSourceDataUpdate [type]="constants.translatedText.StockAsStock"></distrinetSourceDataUpdate></span>

        <!-- <span *ngIf="each=='Book'">{{constants.translatedText.Book}}</span> -->
        <!-- <span *ngIf="each=='Orders'">{{constants.translatedText.Orders_Capitals}}</span>
        <span *ngIf="each=='Stock'">{{constants.translatedText.StockAsStock}}</span> -->

        <span *ngIf="isOriginActive(each); else unchecked;">&#9745;</span>
        <ng-template #unchecked> <span>&#9746;</span> </ng-template>
      </button>
    </div>


    <!-- Pick SnapshotDate -->
    <!-- SnapshotChoice -->
    <!-- <div ngbDropdown class="d-inline-block language-select">
      <button class="btn btn-primary" id="dropdownBasic1" ngbDropdownToggle>
        <span>{{service.chosenSnapshot|cph:'month':0}}</span>
      </button>
      <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <button *ngFor="let snapshot of months" ngbDropdownItem (click)="onUpdateChosenDate(snapshot)">
          <span>{{snapshot|cph:'month':0}}</span>
        </button>
      </div>
    </div> -->
  </nav>


  <!-- <button (click)="removeDisFilterOnCellClick()" *ngIf="service.chosenStockAge && service.chosenStockAge!==-1" class="btn btn-danger active">
    {{constants.translatedText.VehicleAge}} {{service.chosenStockAge}} {{constants.translatedText.Days}}
    &times;
  </button> -->



  <nav class="pageSpecific" *ngIf="service">
    <!-- Search -->
    <div class="searchBox">
      <form>
        <i class="fas fa-search"></i> <input placeholder="search.." class="form-control ml-2" type="text"
          [formControl]="filter" />
      </form>
    </div>





  </nav>
</nav>



<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">

    <div class="content-inner-new" *ngIf="service">



      <div id="gridHolder" *ngIf="!!service.showGrid">
        <!-- <div id="counterArea">
          {{constants.pluralise(service.displayedRowsLength,this.constants.translatedText.Vehicle,this.constants.translatedText.Vehicles)}}
        </div> -->
        <div id="excelExport" (click)="excelExport()">
          <img [src]="constants.provideExcelLogo()">
        </div>
        <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions"
          (firstDataRendered)="onFirstDataRendered($event)" [frameworkComponents]="frameworkComponents"
          (filterChanged)="onFilterChanged()">
        </ag-grid-angular>
      </div>



    </div>

  </div>
</div>