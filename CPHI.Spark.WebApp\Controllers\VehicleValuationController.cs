﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.WebApp.Service.AutoPrice;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]

    public class VehicleValuationController : ControllerBase
    {

        private IVehicleValuationService vehicleValuationService;
        private IUserService userService;


        public VehicleValuationController(IVehicleValuationService vehicleValuationService, IUserService userService)
        {
            this.vehicleValuationService = vehicleValuationService;
            this.userService = userService;
        }




        [HttpGet]
        //Tells us when we stacked up the options for an existing advert, what the valuation was at that time.   I don't expect we will use this endpoint much.
        [Route("GetPreviousValuationResultForAdvert")]
        public async Task<ValuationResultForAdvert> GetPreviousValuationResultForAdvert(int advertId)
        {
            return await vehicleValuationService.GetPreviousValuationResultForAdvert(advertId);
        }


        [HttpGet]
        //Gives us the very latest detail on how we value this car.   Not 100% sure when we need to hit this from the client as mainly display this info in vehicle
        //modal which already has its own route
        [Route("GetLatestVehicleValuationSummaryForAdvert")]
        public async Task<ValuationSummary> GetLatestVehicleValuationSummaryForAdvert(int advertId)
        {
            return await vehicleValuationService.GetLatestVehicleValuationSummaryForAdvert(advertId);
        }



        [HttpGet]
        [Route("GetExistingValuationForAdvert")]
        public async Task<ValuationPriceSet> GetExistingValuationForAdvert(int batchId, int vehicleValuationId)
        {
            return await vehicleValuationService.GetExistingValuationForAdvert(batchId, vehicleValuationId);
        }

        [HttpPost]
        [Route("GetOptionChangeImpact")]
        public async Task<ValuationAndOptionChangeImpact> GetOptionChangeImpact(GetValuationPriceSetChangeParams parms)
        {
            return await vehicleValuationService.GetOptionChangeImpact(parms);
        }


        [HttpPost]
        [Route("GetNewValuationForAdvert")]
        //used when we are updating spec, quickly gives you a new valuation
        public async Task<ValuationPriceSet> GetNewValuationForAdvert(GetValuationPriceSetParams parms)
        {
            return await vehicleValuationService.GetNewValuationForAdvert(parms); //Nimish ticketed
        }



        [HttpGet]
        [Route("GetVehicleInformation")]
        //used to get origin data (derivativeId etc.) for a vehicleReg.   Used when we are looking to value a car that's new to us e.g. a potential purchase
        public async Task<VehicleValuationInformation> GetVehicleInformation(string vehicleReg, int mileage, int? batchId, int? vehicleValuationId)
        {
            return await vehicleValuationService.GetVehicleInformation(vehicleReg, mileage, vehicleValuationId);   //Nimish ticketed
        }

        [HttpGet]
        [Route("GetVehicleRecallStatus")]
        public async Task<VehicleRecallStatus> GetVehicleRecallStatus(string reg)
        {
            return await vehicleValuationService.GetVehicleRecallStatus(reg);
        }

        [HttpPost]
        [Route("GetValuationModalNew")]
        public async Task<ValuationModalNew> GetValuationModalNew(GetValuationModalNewParams parms)
        {
            return await vehicleValuationService.GetValuationModalNew(parms);
        }


        [HttpPost]
        [Route("GetNewValuationModal")]
        public async Task<NewVehicleValuationModal> GetNewValuationModal(GetNewVehicleModalParams parms)
        {
            return await vehicleValuationService.GetNewValuationModal(parms);
        }



        [HttpPost]
        [Route("GetValuationModalCompetitorAnalysis")]
        public async Task<CompetitorSummary> GetValuationModalCompetitorAnalysis(GetValuationModalCompetitorAnalysisParams parms)
        {
            return await vehicleValuationService.GetValuationModalCompetitorAnalysis(parms.SearchParams, parms.OurVehicleParams, userService.GetUserDealerGroupName());
        }



        [HttpPost]
        [Route("GetVehicleSpecOptions")]
        [AllowAnonymous]
        //used when user is reviewing the valuation of a vehicle and wants to tailor it by choosing the features that are fitted
        public async Task<List<VehicleSpecOption>> GetVehicleSpecOptions(GetVehicleSpecOptionsParams parms) //Nimish ticketed
        {
            return await vehicleValuationService.GetVehicleSpecOptions(parms);
        }





        [HttpPost]
        [Route("SaveVehicleSpecBuildForAdvert")]

        public async Task SaveVehicleSpecBuildForAdvert(ValuationResultForAdvertToSave valuation)
        {
            await vehicleValuationService.SaveVehicleSpecBuildForAdvert(valuation); //Nimish ticketed
        }

        [HttpPost]
        [Route("SaveVehicleValuation")]

        public async Task SaveVehicleValuation(ValuationResultForNewVehicleToSave parms)
        {
            await vehicleValuationService.SaveVehicleValuation(parms);
        }

        [HttpPost]
        [Route("UploadVehiclesForValuation")]
        public async Task UploadVehiclesForValuation(
            [FromForm(Name = "file")] IFormFile file,
            [FromForm(Name = "vehiclesForValuationParams")] string vehiclesForValuationParams,
            [FromForm(Name = "batchName")] string? batchName,
            [FromForm(Name = "applyPriceScenarios")] bool applyPriceScenarios,
            [FromForm(Name = "bulkUploadPredefinedTemplateType")] BulkUploadPredefinedTemplateType bulkUploadPredefinedTemplateType
            )
        {
            try
            {
                await vehicleValuationService.ParseAndUploadVehiclesForValuation(file, vehiclesForValuationParams, batchName, applyPriceScenarios, bulkUploadPredefinedTemplateType);
            }
            catch (Exception ex)
            {
                { }
                throw new Exception("failed");
            }
        }



        [HttpGet]
        [Route("DeleteVehicleSpecBuildForAdvert")]
        public async Task DeleteVehicleSpecBuildForAdvert(int advertId)
        {
            await vehicleValuationService.DeleteVehicleSpecBuildForAdvert(advertId);
            return;
        }



        [HttpGet]
        [Route("GetStockLevelAdjustment")]
        public async Task<IEnumerable<StockLevelAndCover>> GetStockLevelAdjustment(string model, int retailerSiteId)
        {
            return await vehicleValuationService.GetStockLevelAndCover(model, retailerSiteId);
        }


        [HttpPost]
        [Route("GetVehicleHistory")]
        public async Task<IEnumerable<VehicleHistoryItem>> GetVehicleHistory(GetVehicleHistoryParams parms)
        {
            return await vehicleValuationService.GetVehicleHistory(parms);
        }


        [HttpPost]
        [Route("GetLeavingVehicleItemsForModel")]
        public async Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItemsForModel(GetLeavingVehicleItemsForModalParams parms)
        {
            return await vehicleValuationService.GetLeavingVehicleItemsForModel(parms);
        }



        [HttpPost]
        [Route("GetSameModelAdverts")]
        public async Task<IEnumerable<SameModelAdvert>> GetSameModelAdverts(GetSameModelAdvertsParams parms)
        {
            return await vehicleValuationService.GetSameModelAdverts(parms);
        }

        [HttpPost]
        [Route("GetStockCover")]
        public async Task<IEnumerable<StockLevelAndCover>> GetStockCover(GetSameModelAdvertsParams parms)
        {
            return await vehicleValuationService.GetStockCover(parms);
        }
    }
}
