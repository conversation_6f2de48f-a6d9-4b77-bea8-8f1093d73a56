import { Component, Input } from '@angular/core';

@Component({
  selector: 'autotraderImage',
  template: `
    <img
      [src]="formattedUrl"
      [alt]="alt"
      [width]="widthpx"
      [style.transform]="'scale(' + scale + ')'"
      style="transform-origin: top left; display: block; max-width: 100%; object-fit: contain; height: auto;"
    />
  `,
})
export class AutotraderImageComponent {
  @Input() src!: string;
  @Input() widthpx!: number;
  @Input() alt: string = '';
  @Input() scale: number = 1; // optional

  get formattedUrl(): string {
    if (!this.src || !this.widthpx) {
      return '../../../../../assets/imgs/autoTrader/placeholder-car.png';
    }
    return this.src.replace('{resize}', `w${this.widthpx}`);
  }
}
