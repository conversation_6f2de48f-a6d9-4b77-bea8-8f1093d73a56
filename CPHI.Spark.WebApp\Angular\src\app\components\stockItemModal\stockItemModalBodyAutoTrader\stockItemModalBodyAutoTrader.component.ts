import { DatePipe } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NgbActiveModal, NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { VehicleOptOutParams } from "src/app/model/VehicleOptOutParams";
import { VehicleOptOutStatus } from "src/app/model/VehicleOptOutStatus";
import { UpdatePriceParams } from "src/app/model/UpdatePriceParams";
import { VehicleAdvertWithRating, VehicleAdvertWithRatingDTO } from 'src/app/model/VehicleAdvertWithRating';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { ConstantsService } from '../../../services/constants.service'
import { SelectionsService } from '../../../services/selections.service'
import { StockItemModalService } from '../stockItemModal.service';
import { GetVehicleAdvertsWithRatingsParams } from 'src/app/model/GetVehicleAdvertWithRatingsParams';

@Component({
  selector: 'app-stockItemModalBodyAutoTrader',
  templateUrl: './stockItemModalBodyAutoTrader.component.html',
  styleUrls: ['./stockItemModalBodyAutoTrader.component.scss']
})

export class stockItemModalBodyAutoTraderComponent implements OnInit {
  @ViewChild('optOutModal', { static: true }) optOutModal: ElementRef;

  brokenImageLinks: boolean = false;
  minDate: string;
  maxDate: string;
  optOutEndDate: string;
  vehicleAdvert: VehicleAdvertWithRating;
  vehicleOptOutStatus: VehicleOptOutStatus;
  newPrice: number;
  stockCheckPhotoToDisplay: number = 0;
  optOutModalRef: NgbModalRef;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: StockItemModalService,
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public datePipe: DatePipe,
    public getDataMethods: GetDataMethodsService
  ) { }

  ngOnInit(): void {
    this.getAutoTraderData();
  }

  async getAutoTraderData() {
    let params: GetVehicleAdvertsWithRatingsParams = {
      Reg: this.service.givenStockItem.Reg,
      Vin: this.service.givenStockItem.Chassis,
      RetailerSiteIds: null,
      UserEligibleSites: [this.service.givenStockItem.SiteId].join(','),
      EffectiveDate: this.constants.todayStart.toISOString(),
      IncludeNewVehicles: true,
      IncludeUnPublishedAdverts: true,
      LifecycleStatuses: null,// ["DUE_IN","FORECOURT","IN STOCK NOT ON PORTAL","SALE_IN_PROGRESS"],
      VehicleTypes: null,
      UseTestStrategy: false
    }
    if (this.service.givenRetailerSiteId) {
      params.RetailerSiteIds = [this.service.givenRetailerSiteId].join(',')
    }

    try {

      const res: VehicleAdvertWithRatingDTO[] = await this.getDataMethods.getVehicleAdvertWithRatings(params)
      if (res.length > 0) {
        this.vehicleAdvert = new VehicleAdvertWithRating( res[res.length - 1]);
        this.newPrice = this.vehicleAdvert.AdvertisedPrice;
      }

      if (this.vehicleAdvert) {
        this.getDataMethods.getVehicleOptOutStatus(this.vehicleAdvert.AdId).subscribe((res: VehicleOptOutStatus) => {
          if (res && new Date(res.EndDate) < new Date()) {
            this.vehicleOptOutStatus = null;
          } else {
            this.vehicleOptOutStatus = res;
          }
        }, error => {
          console.error('Failed to retrieve vehicle opt-out status', error);
        })
      }
    }
    catch (error)  {
      console.error('Failed to retrieve vehicle advert', error);
    }
  }

  updateUrl(event: any) {
    let imageElement: HTMLImageElement = document.getElementById('rrgWebsiteImage') as HTMLImageElement;
    imageElement.src = '/assets/imgs/brokenImageUrlPlaceholder.jpg';
    this.brokenImageLinks = true;
  }

  maybeOptOut() {
    this.minDate = this.datePipe.transform(new Date(), 'yyyy-MM-dd');
    this.maxDate = this.datePipe.transform(new Date().setDate(new Date().getDate() + 28), 'yyyy-MM-dd');
    this.optOutEndDate = this.datePipe.transform(new Date(), 'yyyy-MM-dd');

    this.optOutModalRef = this.modalService.open(this.optOutModal, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' });
    this.optOutModalRef.result.then((result) => {
      // Modal close
    }, (reason) => {
      this.activeModal.close();
    });
  }

  optOut() {
    this.selections.triggerSpinner.emit({ show: true, message: 'Opting out...' });

    let params: VehicleOptOutParams = {
      RetailerSiteId: this.vehicleAdvert.RetailerSiteId,
      VehicleAdvertId: this.vehicleAdvert.AdId,
      Reg: this.vehicleAdvert.VehicleReg,
      Vin: this.vehicleAdvert.Chassis,
      EndDate: this.optOutEndDate
    }

    this.getDataMethods.optOut(params).subscribe(() => {
      this.constants.toastSuccess('Opt-out successful');
      this.optOutModalRef.close();
      this.selections.triggerSpinner.emit({ show: false });
      this.getAutoTraderData();
    }, error => {
      this.constants.toastDanger('Failed to opt-out')
      console.error('Failed to opt-out', error);
      this.selections.triggerSpinner.emit({ show: false });
    })
  }

  maybeOptIn() {
    this.constants.confirmModal.showModal('Confirm opt-in', null);

    const confirmModalSubscription: Subscription = this.selections.confirmModalEmitter.subscribe(res => {
      if (res) this.optIn();
      confirmModalSubscription.unsubscribe();
    })
  }

  optIn() {
    this.selections.triggerSpinner.emit({ show: true, message: 'Opting in...' });

    let params: VehicleOptOutParams = {
      RetailerSiteId: this.vehicleAdvert.RetailerSiteId,
      VehicleAdvertId: this.vehicleAdvert.AdId,
      Reg: this.vehicleAdvert.VehicleReg,
      Vin: this.vehicleAdvert.Chassis,
      EndDate: new Date().toISOString()
    }

    this.getDataMethods.optOut(params).subscribe(() => {
      this.constants.toastSuccess('Opt-in successful');
      this.selections.triggerSpinner.emit({ show: false });
      this.getAutoTraderData();
    }, error => {
      this.constants.toastDanger('Failed to opt-in');
      console.error('Failed to opt-in', error);
      this.selections.triggerSpinner.emit({ show: false });
    })
  }

  setNewPrice(event: any) {
    if (!event.target || (event.target && event.target.value == '')) return;
    this.newPrice = parseFloat(event.target.value.replace('£', '').replace(',', ''));

  }

  select(event: any) {
    event.target.select();
  }

  maybeUpdatePrice() {
    let modalResultSubscription: Subscription = this.selections.confirmModalEmitter.subscribe(res => {
      if (res) { this.updatePrice(); }
      modalResultSubscription.unsubscribe();
    })

    this.constants.confirmModal.showModal('Are you sure? This will update the live AutoTrader price.', null);
  }

  updatePrice() {
    this.selections.triggerSpinner.emit({ show: true, message: 'Setting price...' });

    let params: UpdatePriceParams = {
      vehicleAdvertId: this.vehicleAdvert.AdId,
      rrgSiteItemStockId: this.service.givenStockItem.RRGSiteItemStockId,
      newPrice: this.newPrice,
    }

    this.getDataMethods.updateStockPrice(params).subscribe((res) => {
      //console.log(res);
      this.constants.toastSuccess('Successfully set price');
      this.selections.triggerSpinner.emit({ show: false });
    }, error => {
      this.constants.toastDanger('Failed to set price')
      console.error('Failed to set price', error);
      this.selections.triggerSpinner.emit({ show: false });
    })
  }

  viewScan(increment?: boolean) {
    this.stockCheckPhotoToDisplay = increment ? this.stockCheckPhotoToDisplay + 1 : this.stockCheckPhotoToDisplay - 1;
  }

  goToListing() {
    let url: string = this.constants.buildAdUrl(this.vehicleAdvert.WebSiteSearchIdentifier, this.vehicleAdvert.VehicleType);
    window.open(url, '_blank').focus();
  }
}
