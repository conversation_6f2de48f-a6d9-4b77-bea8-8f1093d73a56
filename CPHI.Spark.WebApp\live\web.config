<configuration> 
<system.webServer>
  <staticContent>
    <remove fileExtension=".json"/>
    <mimeMap fileExtension=".json" mimeType="application/json"/>
  </staticContent>
  <rewrite> 
    <rules> 
      <rule name="Angular Routes 1" stopProcessing="true"> 
        <match url=".*" /> 
          <conditions logicalGrouping="MatchAll"> 
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" /> 
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" /> 
          </conditions> 
        <action type="Rewrite" url="/" /> 
      </rule> 
    </rules> 
  </rewrite> 
</system.webServer> 
</configuration>
