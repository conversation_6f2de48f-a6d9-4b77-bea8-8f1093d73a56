#regMileageCondition {
   display: flex;
   width: 100%;
   justify-content: space-between;

   span {
      margin-bottom: 0.5em;
   }
}

#valuationTable {
   margin-top: 1em;
   width: 100%;

   td {
      vertical-align: bottom;
   }
}

.flexInputArea {
   display: flex;
   align-items: center;

   .flexLabel {
      margin-right: 1em;
   }
}


.cardBody {

   .tileWorkingsArea {
      table {
         width: 100%;
      }
   }

   .tileWorkingsArea {
      width: calc(100% - 10em);
   }

   .tileRightHandResultArea {
      width: 10em;
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
   }
}

.buttonsHolder {
   display: flex;
   justify-content: flex-end;
}

.fullWidthTable {
   width: 100%;
}

.evenSizedTable {
   table-layout: fixed;
}


.regInput {
   background-color: var(--numberPlate);
   border-radius: 5px;
   border: 1px solid;
   text-align: center;
   font-weight: 700;
   font-family: 'NumberPlate';

   text-transform: uppercase;
   float: right;
   padding: 0 1em;
}

.mileageInput {
   background-color: #000000;
   color: #FFFFFF;
   padding: 0;
   border: 1px solid;
   text-align: right;
   letter-spacing: 0.75em;
   float: right;

}

.autoTraderButtonGroup button {
   min-width: 65px;
   border: none;
   padding: 0.5em;
   background-color: #C6E0B4;

   &.active {
      background-color: #00B050;
   }
}


.dashboard-grid {
   height: 100%;
}

#leftSide,
#rightSide {
   width: calc(50% - 1em);
   display: flex;
   flex-direction: column;
}

#vehicleDetailsContainer {
   padding: 2em;
   margin-bottom: 2em;

   table {
      width: 100%;

      tr {
         height: 35px;

         td:nth-of-type(2) {
            text-align: right;
         }


      }
   }
}

#optionsTableContainer {
   flex: 1;
}

#vehicleValuationContainer {
   display: flex;
   flex-direction: column;
   margin-bottom: 2em;

   #saveValuationContainer {
      display: flex;
      justify-content: flex-end;
   }
}

#optimiserInputs {
   display: flex;
   align-items: center;
   margin-bottom: 1em;

   input {
      border: none;
      margin-left: 1em;
      padding-left: 0.5em;
      text-align: right;
      max-width: 75px;
   }
}

locationOptimiserTable {
   height: 100%;
}

#pdfPreviewBoard {
   position: relative;
   background-color: #FFFFFF;
   width: 100%;
   padding: 3em;
   aspect-ratio: 1/0.59;
   border: 2px solid black;

   #downloadBoard {
      position: absolute;
      top: 3em;
      right: 3em;
   }

   #boardPrice {

      font-family: Arial, Helvetica, sans-serif;
      font-weight: 700;
      width: 100%;
      height: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
   }

   #boardReg {
      font-weight: 500;
      position: absolute;
      left: 3em;
      bottom: 3em;
   }
}

.autotraderCard {
   margin-bottom: 1em;
}

.autotraderCard:last-of-type {
   margin-bottom: 0;
}

.subTotal {

   font-weight: 700;
   text-align: right;
   padding-top: 0.5em;
}

.chartContainer {
   // Just placeholder for now
   width: 100%;
   background: #FFFFFF;
   display: flex;
   justify-content: center;
   align-items: center;
   margin-top: 0.5em;

   &#projectedPrice {
      height: 35vh;
   }

   &#prepCosts {
      height: 15vh;
   }
}

td {
   vertical-align: top;
}

.buttonGroup {
   margin: 0;
}

#retailRatingScore {
   display: flex;
   align-items: center;
   padding: 1em 0;
   width: 300px;

   span {
      text-wrap: nowrap;
      margin-right: 1em;
   }

   div {
      width: 100%;
   }
}

#prepItemsTable {
   margin-top: 2em;
}

#prepItemsTable,
#valuationAdjustmentTable {
   table-layout: fixed;
   width: 100%;

   td {
      text-align: left;
   }

   td:last-of-type {
      text-align: right;
   }
}

#stockAndCoverTable {
   width: 100%;
   margin-bottom: 3em;
   //table-layout: fixed;

   th {
      text-align: center;
   }

   td.wide {
      width: 30%
   }

   td.narrow {
      width: 2%
   }

   td {
      text-align: right;
   }

   td:nth-of-type(1),
   td:nth-of-type(2) {
      text-align: left;
   }
}

vehicleoptions,
strategypricebysite {
   flex: 1;
}

.modal-body {
   // display: flex;
   // justify-content: space-between;

   .column {
      width: calc(50% - 0.5em);
      overflow-y: auto;
   }
}

#regInput,
#mileageInput {
   max-width: 150px;
}

#conditionsButtons button {
   line-height: unset !important;
}

projectedpricechart,
prepCostsChart {
   width: 100%;
   height: 100%;
}


// #provideOptionsDetail{margin-bottom:2em;}

@media (max-width: 1600px) {

   #regInput,
   #mileageInput {
      max-width: 125px;
   }

   #conditionsButtons button {
      padding: 0.5em;
   }
}

.modal-body.specOptions {
   min-height: 50vh;
   max-height: 50vh;
   display: flex;
}

#competitorAnalysisTileBody {
   display: flex;
   min-height: 500px;

   competitoranalysis {
      flex: 1;
      display: flex;
   }
}

.allSitesModalBody {
   height: 60vh;
}

#optionsButtonsPlaceholder {
   width: 15em;
   height: 3em;
}


.costingsTable {
   width: 100%;
}


// SPK-4393 modal changes
#modalContentContainer {
   display: flex;
   justify-content: space-between;
   max-height: 80vh;
}

#leftScroll {
   width: 65%;
   min-height: 100%;
   max-height: 100%;
   overflow: auto;
   padding: 1em;
   background: rgba(250, 250, 250, 0.8);
   border-radius: 0.5em 0 0 0.5em;

   table {
      width: 100%;

      td[colspan="2"] {
         text-align: left;
      }
   }


   #valuationAndMetrics {

      #retailRatingAndLiveMetricsContainer {
         display: flex;

         #retailRating {
            width: 40%;
            padding: 1em;
         }

         #liveMetrics {
            display: flex;
            flex-direction: column;
            width: 60%;
            padding: 1em;
         }
      }
   }

   #sellingProspects {

      #competitorAnalysisTableContainer {
         width: 100%;
         height: 100%;
      }

      #competitorAnalysisChartContainer {
         position: relative;
         width: 100%;
      }
   }

   #prepCosts {
      display: flex;
      flex-direction: column;
   }
}

#rightFixed {
   width: 35%;
   margin-left: 1em;
   background-color: #FFFFFF;
   padding: 1em;
   height: max-content;

   table {
      width: 100%;
      table-layout: fixed;

      td:nth-of-type(even) {
         text-align: right;

         input {
            width: 100%;
            line-height: auto;
         }
      }
   }
}

.undoRedoButton {
   border: none;
   background-color: transparent;
   color: var(--grey50);

   &:hover {
      color: var(--secondary);
   }
}


.placeholder-glow {
   height: 20vh
}

#optionsTableHolder {
   height: 500px;
}

#summaryTablesHolder {
   width: 100%;
   display: flex;
   justify-content: space-between;

   .summaryChartHolder {
      width: 30%;
   }

   table {
      width: 100%;
      table-layout: auto;
   }
}
