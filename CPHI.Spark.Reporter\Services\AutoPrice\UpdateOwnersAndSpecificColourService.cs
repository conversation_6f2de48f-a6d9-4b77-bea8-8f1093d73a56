﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Repository;
using log4net;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Tokens;
using System.Diagnostics;
using System.Threading;
using Datadog.Trace;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class UpdateOwnersAndSpecificColourService
   {
      public UpdateOwnersAndSpecificColourService()
      {
      }

      public async Task UpdateOwnersAndSpecificColour(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0)
         { return; }
         try
         {
            logger.Info("----------------------------------------------------------");
            logger.Info("OwnerAndSpecificColour");

            using (var parentScope = Tracer.Instance.StartActive("UpdateDaysToSell"))
            {


               foreach (Model.DealerGroupName dealerGroup in dealerGroups)
               {
                  logger.Info($"OwnerAndSpecificColour: {dealerGroup}");

                  using (var childScope = Tracer.Instance.StartActive($"Begin dealergroup {dealerGroup}"))
                  {
                     var logMessage = LoggingService.InitLogMessage();
                     try
                     {

                        RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                        List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

                        if (retailers.Count > 0)
                        {
                           try
                           {
                              await UpdateForThisDealerGroup(logger, dealerGroup);
                           }
                           catch (Exception ex)
                           {
                              logger.Error(ex);
                              await EmailerService.SendMailOnError(dealerGroup, "OwnerAndSpecificColour - Error", ex);
                              LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                           }
                        }
                        else
                        {
                           logger.Info($"OwnerAndSpecificColour: {dealerGroup}: No retailers");
                        }
                     }
                     catch (Exception ex)
                     {
                        await EmailerService.LogException(ex, logger, "OwnerAndSpecificColour");
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                     finally
                     {
                        await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "OwnerAndSpecificColour");
                     }
                  }

               }

               logger.Info("Completed OwnerAndSpecificColour");
               logger.Info("----------------------------------------------------------");
            }
         }

         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }

      private static async Task UpdateForThisDealerGroup(ILog logger, Model.DealerGroupName dealerGroup)
      {
         string connString = ConfigService.GetConnectionString(dealerGroup);
         DateTime runDate = DateTime.Now; //new DateTime(2024, 1, 10, 7, 0, 0);  //

         var retailerSitesDataAccess = new RetailerSitesDataAccess(connString);
         VehicleAdvertsService vehicleAdvertsService = new VehicleAdvertsService(connString);
         VehicleAdvertsDataAccess vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(connString);

         // get data
         List<VehicleAdvertWithRating> allExistingAdsOnATWithMissingOwnersOrSpecificColour = new List<VehicleAdvertWithRating>();
         await DataDogService.RunWithSpanAsync("Fetch data", async () =>
         {

            List<RetailerSite> retailerSitesThisDG = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
            var siteIds = retailerSitesThisDG.Select(x => x.Site_Id).Distinct().ToList();
            var retailerSiteIds = retailerSitesThisDG.Select(x => x.Id).ToList();

            //load up all the snapshots for today
            GetVehicleAdvertsWithRatingsParams parms = new GetVehicleAdvertsWithRatingsParams()
            {
               Reg = null,
               Vin = null,
               RetailerSiteIds = string.Join(',', retailerSiteIds),
               EffectiveDate = runDate.Date,
               UserEligibleSites = string.Join(',', siteIds),
               IncludeNewVehicles = true,
               IncludeUnPublishedAdverts = true
            };

            var lifecycles = AutoPriceHelperService.GetAllLifecycleStatuses();
            allExistingAdsOnATWithMissingOwnersOrSpecificColour =
                (await vehicleAdvertsService.FetchVehicleAdvertsWithRatingsFromMainDbTables(DateTime.Now, dealerGroup, lifecycles, null))
                    .Where(x => x.SpecificColour.IsNullOrEmpty() || x.Owners.IsNullOrEmpty())
                    .ToList();
            logger.Info($"Getting owners and colour for {allExistingAdsOnATWithMissingOwnersOrSpecificColour.Count} adverts...");
         });

         await DataDogService.RunWithSpanAsync("FindOwnersAndSpecificColour", async () =>
         {
            await FindOwnersAndSpecificColour(allExistingAdsOnATWithMissingOwnersOrSpecificColour, logger);
         });



         logger.Info("Saving updated adverts...");

         await  DataDogService.RunWithSpanAsync("SaveOwnersAndSpecificColour", async () =>
         {
            await vehicleAdvertsDataAccess.SaveOwnersAndSpecificColour(allExistingAdsOnATWithMissingOwnersOrSpecificColour);
         });
         logger.Info("Saved updated adverts");
      }


      


      private static async Task FindOwnersAndSpecificColour(List<VehicleAdvertWithRating> items, ILog logger)
      {
         AutoTraderVehiclesClient atVehiclesClient = new AutoTraderVehiclesClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         var atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);

         var bearerToken = await atTokenClient.GetToken();

         // Filter out invalid items
         var validItems = items.Where(item =>
            item.VehicleReg != null &&
            item.VehicleReg != string.Empty &&
            item.OdometerReading != null
         ).ToList();

         int totalCount = validItems.Count;

         if (totalCount == 0)
         {
            logger.Info("No valid vehicles to process");
            return;
         }

         // Calculate delay between tasks based on rate limit
         int rateLimit = 45; // Slightly below the 50/s limit for safety
         int delayMs = 1000 / rateLimit; // Delay in milliseconds between tasks
         logger.Info($"Processing {totalCount} vehicles with rate limiter and {delayMs}ms delay between tasks");

         Stopwatch sw = new Stopwatch();
         sw.Start();
         long lastIterationTime = sw.ElapsedMilliseconds;

         //iterate over, in a controlled, spaced out way using the delayMS
         List<Task> processingTasks = new List<Task>();
         for (int i = 0; i < validItems.Count; i++)
         {
            var item = validItems[i];

            // Log progress periodically
            if (i % 100 == 0 || i == validItems.Count - 1)
            {
               //logger.Info($"Queuing item {i + 1} of {validItems.Count}");
               logger.Info($"Trying item {i + 1} of {validItems.Count}, elapsed time: {sw.ElapsedMilliseconds}ms");
            }

            var task = Task.Run(() => ProcessItemWithRetry(item, atVehiclesClient, bearerToken, logger));  // Task.Run seems to immediately offload the taks to another thread, increasing overall speed.  we don't await it
            processingTasks.Add(task);

            //more reliable, but cpu intense, way of delaying
            while (sw.ElapsedMilliseconds - lastIterationTime < delayMs)
            {
               Thread.SpinWait(100); // Avoid tight spinning — tune the value as needed
            }
            lastIterationTime = sw.ElapsedMilliseconds;

         }
         
         await Task.WhenAll(processingTasks);
         logger.Info($"Completed processing {totalCount} vehicles with owners and specific colour data");
      }



      private static async Task ProcessItemWithRetry(
         VehicleAdvertWithRating item,
         AutoTraderVehiclesClient atVehiclesClient,
         TokenResponse bearerToken,
         ILog logger)
      {
         const int maxRetries = 3;
         int retryCount = 0;
         int baseDelayMs = 1000; // Start with 1 second delay

         while (true) // Will break where we use 'break' below.   (on success or after max retries)
         {
            try
            {
               ATNewVehicleGet result = await atVehiclesClient.GetIndividualVehicleForOwnerAndColour(
                  item.VehicleReg,
                  item.OdometerReading.Value,
                  item.RetailerSiteRetailerId,
                  bearerToken.AccessToken,
                  ConfigService.AutotraderBaseURL
                  );

               if (result != null)
               {
                  item.Owners = result.vehicle.owners.ToString();

                  // Handle the case where oem or colour might be null
                  if (result.vehicle.oem != null)
                  {
                     item.SpecificColour = result.vehicle.oem.colour;
                  }
                  else if (!string.IsNullOrEmpty(item.Colour))
                  {
                     // Use the existing color as a fallback if AutoTrader returns null
                     item.SpecificColour = item.Colour;
                  }
               }

               // Success - exit the retry loop
               break;
            }
            catch (TimeoutException ex) when (ex.Message.Contains("Rate limit") && retryCount < maxRetries)
            {
               // Handle rate limit exceptions specifically
               retryCount++;
               int delayMs = baseDelayMs * (int)Math.Pow(2, retryCount - 1); // Exponential backoff
               logger.Warn($"Rate limit exceeded for reg {item.VehicleReg}. Retry {retryCount}/{maxRetries} after {delayMs}ms delay.");
               await Task.Delay(delayMs);
            }
            catch (HttpRequestException ex) when ((ex.Message.Contains("429") || ex.Message.Contains("TooManyRequests")) && retryCount < maxRetries)
            {
               // Handle HTTP 429 Too Many Requests
               retryCount++;
               int delayMs = baseDelayMs * (int)Math.Pow(2, retryCount - 1); // Exponential backoff
               logger.Warn($"Too many requests for reg {item.VehicleReg}. Retry {retryCount}/{maxRetries} after {delayMs}ms delay.");
               await Task.Delay(delayMs);
            }
            catch (Exception ex)
            {
               if (retryCount < maxRetries)
               {
                  // Retry on other exceptions too, but log them differently
                  retryCount++;
                  int delayMs = baseDelayMs * (int)Math.Pow(2, retryCount - 1); // Exponential backoff
                  logger.Warn($"Error for reg {item.VehicleReg}: {ex.Message}. Retry {retryCount}/{maxRetries} after {delayMs}ms delay.");
                  await Task.Delay(delayMs);
               }
               else
               {
                  // Log and give up after max retries
                  logger.Error($"Failed on reg {item.VehicleReg} after {maxRetries} retries. Error: {ex.Message}. Continuing.");
                  break;
               }
            }
         }
      }










   }
}
