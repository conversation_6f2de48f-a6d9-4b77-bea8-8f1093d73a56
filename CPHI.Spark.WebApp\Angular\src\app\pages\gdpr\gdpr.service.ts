import { EventEmitter, Injectable } from '@angular/core';
import { MultiSelectMonth } from 'src/app/components/datePickerMultiSelect/datePickerMultiSelect.component';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { TopBottomHighlightRule } from 'src/app/model/TopBottomHighlightRule';
import { GDPRRow } from './GDPRRow';
import { GDPRParams } from './GDPRParams';
import { GDPRInit } from './GDPRInit';

@Injectable({
  providedIn: 'root'
})

export class GDPRService {
  gdpr: GDPRInit;
  topBottomHighlights:TopBottomHighlightRule[]=[]

  constructor(
    public getData: GetDataMethodsService,
    public selections: SelectionsService,
    public constants: ConstantsService
  ) { }

  initiateGDPR() {
    if (!this.gdpr) {
      let today: Date = this.constants.appStartTime;
      let currentMonth: MultiSelectMonth = {
        startDate: this.constants.deductTimezoneOffset(new Date(today.getFullYear(), today.getMonth(), 1)),
        isSelected: true
      }
      

      this.gdpr = {
        barThresholds: {
          good: 0.6,
          bad: 0.3
        },
        months: [currentMonth],
        peopleData: null,
        peopleDataChangedEmitter: new EventEmitter(),
        sitesData: null,
        sitesDataChangedEmitter: new EventEmitter(),
        siteIds: null,
        orderTypes: [
          { isSelected: true, label: 'Commercial' },
          { isSelected: true, label: 'Corporate' },
          { isSelected: true, label: 'Fleet' },
          { isSelected: true, label: 'Motability' },
          { isSelected: true, label: 'Retail' },
          { isSelected: true, label: 'Trade' }
        ],
        salesManagerId: null
      }
    }
  }

  getSitesData() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.getData.getGDPR(this.getParams()).subscribe((res: GDPRRow[]) => {
      this.gdpr.sitesData = res;
    }, e => {
      console.error('Error retrieving GDPR sites data: ' + JSON.stringify(e));
    }, () => {
      this.gdpr.sitesDataChangedEmitter.emit(true);
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getPeopleData() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    this.getData.getGDPRForSite(this.getParams()).subscribe((res: GDPRRow[]) => {
      this.gdpr.peopleData = res;
    }, e => {
      console.error('Error retrieving GDPR people data: ' + JSON.stringify(e));
    }, () => {
      this.gdpr.peopleDataChangedEmitter.emit(true);
      this.selections.triggerSpinner.next({ show: false });
    })
  }

  getParams() : GDPRParams {

    // Get order type ids
    let selectedOrderTypes: string[] = this.gdpr.orderTypes.filter(x => x.isSelected == true).map(x => x.label);
    let selectedOrderTypeIds: number[] = [];
    
    this.constants.OrderTypes.forEach(o => {
      if (selectedOrderTypes.includes(o.Code)) selectedOrderTypeIds.push(o.Id);
    })

    let params: GDPRParams = {
      siteIds: this.gdpr.siteIds ? this.gdpr.siteIds.toString() : null,
      monthYear: this.constants.formatMonthsForParams(this.gdpr.months),
      orderTypeIds: selectedOrderTypeIds.toString(),
      salesManagerId: (this.gdpr.salesManagerId == null) ? '' : this.gdpr.salesManagerId.toString()
    }

    return params;
  }

}
