﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Ser;
using CPHI.Spark.Model.ViewModels.Vindis;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DiscussionSummaryAction = CPHI.Spark.Model.Ser.DiscussionSummaryAction;
using Microsoft.Exchange.WebServices.Data;
using CPHI.Spark.Model.ViewModels;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Information;

namespace CPHI.Spark.WebApp.Service
{
    public interface ISalesExecReviewService
    {
        Task<SEReviewGetFormResult> GetForm(SEReviewGetFormParams parms, int userId);
        Task<SEReviewRefreshFormResult> RefreshForm(SEReviewRefreshFormParams parms, int userId);
        Task<SEReviewSaveFormResult> SaveForm(SEReviewSaveFormParams parms, int userId);
        Task<int> UpdateApprovalState(SEReviewUpdateApprovalStateParams parms, int userId);
        Task<SEReviewGetSiteSummaryByMeasureResults> GetSiteSummaryByMeasure(SEReviewGetSiteSummaryByMeasureParams parms, int userId, DealerGroupName dealerGroup);
        Task<SEReviewGetSiteSummaryByMonthResults> GetSiteSummaryByMonth(SEReviewGetSiteSummaryByMonthParams parms, int userId, DealerGroupName dealerGroup);
        Task<SEReviewGetPersonSummaryByMeasureResults> GetPersonSummaryByMeasure(SEReviewGetPersonSummaryByMeasureParams parms, int userId, DealerGroupName dealerGroup);
        Task<SEReviewGetPersonSummaryByMonthResults> GetPersonSummaryByMonth(SEReviewGetPersonSummaryByMonthParams parms, int userId);
        Task<int> AddNewDiscussions(SEReviewSaveFormParams parms, int formId);

    }

    public class SalesExecReviewService : ISalesExecReviewService
    {
        private readonly ISalesExecReviewDataAccess salesExecReviewDataAccess;
        private readonly IUserService userService;
        private readonly IConfiguration configuration;


        // Total months to go back for the Monthly views
        private const int monthsToGoBack = 12;

        // The label displayed for the total row
        private const string summaryRowTitle = "Average";

        // For the scoring Profit Pot measure, a percentage above this cannot be achieved.
        private const decimal profitPotLimit = 1.1M;

        // For the scoring Units measures, a combined percentage (both New & Used)
        // must be achieved before they can score for these objectives
        private const decimal unitsThreshold = 0.75000M;

        public SalesExecReviewService(ISalesExecReviewDataAccess salesExecReviewDataAccess, IUserService userService, IConfiguration configuration)
        {
            this.salesExecReviewDataAccess = salesExecReviewDataAccess;
            this.userService = userService;
            this.configuration = configuration;
        }

        // FORM //
        public async Task<SEReviewGetFormResult> GetForm(SEReviewGetFormParams parms, int userId)
        {
            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, false)).Where(x => x.IsSales && x.IsActive).OrderBy(x => x.SortOrder);
            if (!sites.Select(x => x.SiteId).ToList().Contains(parms.SiteId))
            {
                throw new Exception("Not permitted");
            }

            SEReviewGetFormDetails details = await salesExecReviewDataAccess.GetFormDetails(parms.SalesmanId, parms.Month, dealerGroup);

            SEReviewGetFormResult result = new SEReviewGetFormResult
            {
                Month = parms.Month
            };

            int[] siteArray = new int[] { parms.SiteId };

            try
            {
                result.MonthScores = new List<SEReviewMonthScore>();
                IEnumerable<SEReviewScoringMeasure> currentMeasuresM0 = await salesExecReviewDataAccess.GetCurrentActuals(parms.SalesmanId, parms.Month, dealerGroup);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresM1 = await salesExecReviewDataAccess.GetCurrentActuals(parms.SalesmanId, parms.Month.AddMonths(-1), dealerGroup);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresM2 = await salesExecReviewDataAccess.GetCurrentActuals(parms.SalesmanId, parms.Month.AddMonths(-2), dealerGroup);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresM3 = await salesExecReviewDataAccess.GetCurrentActuals(parms.SalesmanId, parms.Month.AddMonths(-3), dealerGroup);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresM4 = await salesExecReviewDataAccess.GetCurrentActuals(parms.SalesmanId, parms.Month.AddMonths(-4), dealerGroup);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresM5 = await salesExecReviewDataAccess.GetCurrentActuals(parms.SalesmanId, parms.Month.AddMonths(-5), dealerGroup);
                result.MonthScores.Add(CalculatePreviousMonthChart(currentMeasuresM5, parms.Month.AddMonths(-5)));
                result.MonthScores.Add(CalculatePreviousMonthChart(currentMeasuresM4, parms.Month.AddMonths(-4)));
                result.MonthScores.Add(CalculatePreviousMonthChart(currentMeasuresM3, parms.Month.AddMonths(-3)));
                result.MonthScores.Add(CalculatePreviousMonthChart(currentMeasuresM2, parms.Month.AddMonths(-2)));
                result.MonthScores.Add(CalculatePreviousMonthChart(currentMeasuresM1, parms.Month.AddMonths(-1)));
                result.MonthScores.Add(CalculatePreviousMonthChart(currentMeasuresM0, parms.Month));

            }
            catch
            {

            }


            // If there is an entry in the table, get form details and actuals from actuals table
            if (details.FormId != null)
            {
                IEnumerable<SEReviewScoringMeasure> currentMeasures = await salesExecReviewDataAccess.GetCurrentActuals(parms.SalesmanId, parms.Month, dealerGroup);

                result.ScoringMeasures = currentMeasures.Where(x => x.Weighting > 0);
                // Populate measures
                CalculateActualPercentage(result.ScoringMeasures);


                result.OverallScore = result.ScoringMeasures.Sum(x => x.Score);

                result.NonScoringMeasures = currentMeasures.Where(x => x.Weighting == 0);

                foreach (SEReviewNonScoringMeasure measure in result.NonScoringMeasures)
                {
                    measure.ActualPercentage = measure.ObjectiveThisMonth != 0 ? measure.Actual / measure.ObjectiveThisMonth : 0;
                }
                ;

                // Populate details
                result.FormId = details.FormId;
                result.LastRefreshed = details.LastRefreshed;
                result.ApprovalState = details.ApprovalState;
                result.SalesmanName = details.SalesmanName;
                result.SalesmanRoleSiteName = details.SalesmanRoleSiteName;

                int reviewDateYear = details.ReviewedDate.Year;

                // If no review date, set to null.
                if (reviewDateYear == 1) { result.ReviewedDate = null; }
                else
                {
                    result.ReviewedDate = details.ReviewedDate;
                    result.ReviewedByName = details.ReviewedByName;
                }

                result.DiscussionSummaryActions = await salesExecReviewDataAccess.GetDiscussionSummaryActions((int)details.FormId, dealerGroup);
            }

            // Otherwise get fixed from various other tables
            else
            {
                IEnumerable<SEReviewObjectiveVM> objectives = await salesExecReviewDataAccess.GetObjectives(siteArray, parms.Month, parms.Month.AddMonths(1), dealerGroup);
                IEnumerable<SEReviewObjectiveVM> objectivesForThisMonth = objectives.Where(x => x.Month == parms.Month);
                IEnumerable<SEReviewObjectiveVM> objectivesForNextMonth = objectives.Where(x => x.Month == parms.Month.AddMonths(1));
                IEnumerable<SEReviewMeasure> fixedMeasuresList = await GetFixedActuals(parms.SalesmanId, parms.Month);

                List<SEReviewScoringMeasure> scoringMeasures = new List<SEReviewScoringMeasure>();
                List<SEReviewNonScoringMeasure> nonScoringMeasures = new List<SEReviewNonScoringMeasure>();

                foreach (SEReviewObjectiveVM objective in objectivesForThisMonth)
                {
                    SEReviewMeasure fixedMeasureForObjective = fixedMeasuresList.Where(x => x.MeasureName == objective.MeasureName).FirstOrDefault();
                    SEReviewObjectiveVM objectiveForNextMonthVM = objectivesForNextMonth.Where(x => x.MeasureName == objective.MeasureName).FirstOrDefault();

                    decimal actual = fixedMeasureForObjective != null ? fixedMeasureForObjective.Actual : 0;
                    decimal objectiveForNextMonth = objectiveForNextMonthVM != null ? objectiveForNextMonthVM.Value : 0;
                    decimal percentage = objective.Value != 0 ? actual / objective.Value : 0;

                    // Conditions for exceptions
                    if (objective.MeasureName == "Used & Demo Units" || objective.MeasureName == "New Units")
                    {
                        SEReviewMeasure usedM = fixedMeasuresList.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                        SEReviewMeasure newM = fixedMeasuresList.Where(x => x.MeasureName == "New Units").FirstOrDefault();
                        SEReviewObjectiveVM usedO = objectivesForThisMonth.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                        SEReviewObjectiveVM newO = objectivesForThisMonth.Where(x => x.MeasureName == "New Units").FirstOrDefault();

                        decimal totalVolAch = 0;

                        if (usedM != null && newM != null)
                        {
                            totalVolAch = usedM.Actual + newM.Actual;
                        }
                        if (usedM == null && newM != null)
                        {
                            totalVolAch = 0 + newM.Actual;
                        }
                        if (usedM != null && newM == null)
                        {
                            totalVolAch = usedM.Actual + 0;
                        }

                        decimal totalVolTar = usedO.Value + newO.Value;
                        decimal percentageAch = totalVolTar > 0 ? totalVolAch / totalVolTar : 0;

                        if (percentageAch < 0.75000M)
                        {
                            percentage = 0;
                        }
                    }

                    if (objective.MeasureName.Contains("New CEM") || objective.MeasureName.Contains("Used CEM"))
                    {
                        percentage = CalculateCEM(actual, objective.Value);
                    }

                    if (objective.MeasureName.Contains("Pot") && percentage > profitPotLimit) { percentage = profitPotLimit; }

                    if (objective.MeasureName.Contains("Pot") && objective.Value == 0) { percentage = 1; }

                    SEReviewScoringMeasure measureToAdd = new SEReviewScoringMeasure
                    {
                        MeasureName = objective.MeasureName,
                        ObjectiveThisMonth = objective.Value,
                        Weighting = objective.Weighting,
                        Actual = actual,
                        ActualPercentage = percentage,
                        ObjectiveNextMonth = objectiveForNextMonth
                    };
                    if (objective.IsScoring) { scoringMeasures.Add(measureToAdd); }
                    else { nonScoringMeasures.Add((SEReviewNonScoringMeasure)measureToAdd); }

                }

                result.ScoringMeasures = scoringMeasures;
                result.NonScoringMeasures = nonScoringMeasures;
                result.OverallScore = result.ScoringMeasures.Sum(x => x.Score);
            }

            // Ensure this month score always matches form score
            result.MonthScores.Where(x => x.Month == parms.Month).First().Score = result.OverallScore;
            return result;
        }

        private SEReviewMonthScore CalculatePreviousMonthChart(IEnumerable<SEReviewScoringMeasure> currentMeasures, DateTime currentMonth)
        {
            CalculateActualPercentage(currentMeasures);
            return new SEReviewMonthScore() { Month = currentMonth, Score = currentMeasures.Sum(x => x.Score) };
        }

        private void CalculateActualPercentage(IEnumerable<SEReviewScoringMeasure> currentMeasures)
        {
            currentMeasures = currentMeasures.Where(x => x.Weighting > 0).ToList();

            foreach (SEReviewScoringMeasure measure in currentMeasures)
            {
                decimal percentage = measure.ObjectiveThisMonth != 0 ? measure.Actual / measure.ObjectiveThisMonth : 0;

                measure.ActualPercentage = percentage;

                if (measure.MeasureName == "Used & Demo Units" || measure.MeasureName == "New Units")
                {
                    SEReviewScoringMeasure usedM = currentMeasures.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                    SEReviewScoringMeasure newM = currentMeasures.Where(x => x.MeasureName == "New Units").FirstOrDefault();

                    decimal totalVolAch = (usedM?.Actual ?? 0) + (newM?.Actual ?? 0);
                    decimal totalVolTar = (usedM?.ObjectiveThisMonth ?? 0) + (newM?.ObjectiveThisMonth ?? 0);
                    decimal percentageAch = HelperService.DivideByDecimal(totalVolAch, totalVolTar);

                    if (percentageAch < unitsThreshold) { measure.ActualPercentage = 0; }
                }


                if (measure.MeasureName.Contains("New CEM") || measure.MeasureName.Contains("Used CEM"))
                {
                    measure.ActualPercentage = CalculateCEM(measure.Actual, measure.ObjectiveThisMonth);
                }

                if (measure.MeasureName.Contains("Pot") && percentage > profitPotLimit) { measure.ActualPercentage = profitPotLimit; }
            }

        }

        public async Task<SEReviewRefreshFormResult> RefreshForm(SEReviewRefreshFormParams parms, int userId)
        {
            SEReviewRefreshFormResult result = new SEReviewRefreshFormResult();
            int[] siteArray = new int[] { parms.SiteId };
            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            SEReviewGetFormDetails details = await salesExecReviewDataAccess.GetFormDetails(parms.SalesmanId, parms.Month, dealerGroup);

            IEnumerable<SEReviewObjectiveVM> objectives = await salesExecReviewDataAccess.GetObjectives(siteArray, parms.Month, parms.Month.AddMonths(1), dealerGroup);
            IEnumerable<SEReviewObjectiveVM> objectivesForThisMonth = objectives.Where(x => x.Month == parms.Month);
            IEnumerable<SEReviewObjectiveVM> objectivesForNextMonth = objectives.Where(x => x.Month == parms.Month.AddMonths(1));
            IEnumerable<SEReviewMeasure> fixedMeasures = await GetFixedActuals(parms.SalesmanId, parms.Month);
            IEnumerable<SEReviewScoringMeasure> currentMeasures = await salesExecReviewDataAccess.GetCurrentActuals(parms.SalesmanId, parms.Month, dealerGroup);
            List<SEReviewMeasure> fixedMeasuresList = fixedMeasures.ToList();

            List<SEReviewScoringMeasure> scoringMeasures = new List<SEReviewScoringMeasure>();
            List<SEReviewNonScoringMeasure> nonScoringMeasures = new List<SEReviewNonScoringMeasure>();

            foreach (SEReviewObjectiveVM obj in objectivesForThisMonth)
            {
                SEReviewMeasure fixedMeasureForObjective = fixedMeasuresList.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault();
                SEReviewObjectiveVM objectiveForNextMonthVM = objectivesForNextMonth.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault();

                decimal actual;
                string[] fieldsUserCouldHaveSaved = { "Deals with CEM Response %", "New Renewals", "Used Renewals", "Martec Right Words Score", "New CEM (Rolling 3 months)",
                    "Used CEM (Rolling 3 months)", "Telephone Appointment Ratio" };

                if (fieldsUserCouldHaveSaved.Contains(obj.MeasureName) && currentMeasures.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault() != null)
                {
                    actual = currentMeasures.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault().Actual;
                }
                else
                {
                    actual = fixedMeasureForObjective != null ? fixedMeasureForObjective.Actual : 0;
                }

                decimal objectiveForNextMonth = objectiveForNextMonthVM != null ? objectiveForNextMonthVM.Value : 0;
                decimal percentage = obj.Value != 0 ? actual / obj.Value : 0;

                // Calculate exceptions
                if (obj.MeasureName == "Used & Demo Units" || obj.MeasureName == "New Units")
                {
                    SEReviewMeasure usedM = fixedMeasuresList.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                    SEReviewMeasure newM = fixedMeasuresList.Where(x => x.MeasureName == "New Units").FirstOrDefault();
                    SEReviewObjectiveVM usedO = objectivesForThisMonth.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                    SEReviewObjectiveVM newO = objectivesForThisMonth.Where(x => x.MeasureName == "New Units").FirstOrDefault();

                    decimal totalVolAch = (usedM is null ? 0 : usedM.Actual) + (newM is null ? 0 : newM.Actual);
                    decimal totalVolTar = (usedO is null ? 0 : usedO.Value) + (newO is null ? 0 : newO.Value);
                    decimal percentageAch = totalVolTar > 0 ? totalVolAch / totalVolTar : 0;

                    if (percentageAch < unitsThreshold) { percentage = 0; }

                }


                if (obj.MeasureName.Contains("New CEM") || obj.MeasureName.Contains("Used CEM"))
                {
                    // If data already saved
                    if (currentMeasures.Count() > 0 && currentMeasures.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault() != null)
                    {
                        actual = currentMeasures.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault().Actual;
                    }
                    //try
                    //{
                    percentage = CalculateCEM(actual, obj.Value);
                    //}
                    //catch(Exception e)
                    //{

                    //}

                }

                // Profit Pot % achieved cannot be above this %
                if (obj.MeasureName.Contains("Pot") && percentage > profitPotLimit) { percentage = profitPotLimit; }

                if (obj.MeasureName.Contains("Pot") && obj.Value == 0) { percentage = 1; }

                SEReviewScoringMeasure measureToAdd = new SEReviewScoringMeasure
                {
                    MeasureName = obj.MeasureName,
                    ObjectiveThisMonth = obj.Value,
                    Weighting = obj.Weighting,
                    Actual = actual,
                    ActualPercentage = percentage,
                    ObjectiveNextMonth = objectiveForNextMonth
                };

                if (obj.IsScoring) { scoringMeasures.Add(measureToAdd); }
                else { nonScoringMeasures.Add((SEReviewNonScoringMeasure)measureToAdd); }

            }

            result.ScoringMeasures = scoringMeasures;
            result.NonScoringMeasures = nonScoringMeasures;
            result.OverallScore = result.ScoringMeasures.Sum(x => x.Score);
            result.LastRefreshed = DateTime.Now;
            return result;
        }

        public async Task<SEReviewSaveFormResult> SaveForm(SEReviewSaveFormParams parms, int userId)
        {
            int formId;
            SEReviewSaveFormResult result = new SEReviewSaveFormResult();
            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            Form formToUpdate = await salesExecReviewDataAccess.GetFormToUpdate(parms.FormId, dealerGroup);

            // Form already exists - update existing actuals if already there
            if (formToUpdate != null)
            {
                IEnumerable<Actual> actualsForForm = await this.salesExecReviewDataAccess.GetActualsForForm(parms, dealerGroup);
                List<Actual> actualsToUpdate = actualsForForm.ToList();
                formId = (int)parms.FormId;

                foreach (Actual actual in actualsToUpdate)
                {
                    Measure measure = await this.salesExecReviewDataAccess.GetMeasureForId(actual.MeasureId, dealerGroup);
                    decimal? newValue = parms.SEReviewMeasures.Where(x => x.MeasureName == measure.Name).FirstOrDefault().Actual;

                    // If new value has been sent, update it.
                    if (newValue != null) { actual.Value = (decimal)newValue; }
                    ;
                }

                formToUpdate.LastSaved = DateTime.Now;
                formToUpdate.LastSavedById = userId;

                IEnumerable<DiscussionSummaryAction> discussionsToUpdate = await this.salesExecReviewDataAccess.GetDiscussionsForForm((int)parms.FormId, dealerGroup);

                await salesExecReviewDataAccess.UpdateForm(formToUpdate, dealerGroup);
                await salesExecReviewDataAccess.UpdateActuals(actualsToUpdate, dealerGroup);
                await salesExecReviewDataAccess.DeleteDiscussions(discussionsToUpdate.ToList(), dealerGroup);
                await AddNewDiscussions(parms, (int)parms.FormId);
            }
            // Form does not currently exist - add new form and actuals
            else
            {

                formId = await salesExecReviewDataAccess.AddNewForm(CreateNewForm(parms, userId), parms.Month, parms.SalesmanId, dealerGroup);

                List<Actual> newActualsToAdd = new List<Actual>();

                foreach (SEReviewMeasure m in parms.SEReviewMeasures)
                {
                    Measure measure = await this.salesExecReviewDataAccess.GetMeasureForName(m.MeasureName, dealerGroup);

                    newActualsToAdd.Add(new Actual
                    {
                        MeasureId = measure.Id,
                        Value = m.Actual,
                        Month = parms.Month,
                        PersonId = parms.SalesmanId
                    });
                }

                await salesExecReviewDataAccess.AddNewActuals(newActualsToAdd, dealerGroup);

                await AddNewDiscussions(parms, formId);

            }

            result.FormId = formId;
            return result;
        }

        public async Task<int> UpdateFormApprovedBy(SEReviewUpdateApprovalStateParams parms, int userId)
        {
            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            Form formToUpdate = await salesExecReviewDataAccess.GetFormToUpdate(parms.FormId, dealerGroup);
            formToUpdate.ApprovedById = userId;
            formToUpdate.ApprovedDate = DateTime.Now;
            return await salesExecReviewDataAccess.UpdateForm(formToUpdate, dealerGroup);
        }

        public async Task<int> AddNewDiscussions(SEReviewSaveFormParams parms, int formId)
        {
            List<DiscussionSummaryAction> discSumActionToAdd = new List<DiscussionSummaryAction>();

            foreach (Model.ViewModels.Vindis.DiscussionSummaryAction discSumAction in parms.DiscussionSummaryActions)
            {
                discSumActionToAdd.Add(new DiscussionSummaryAction
                {
                    FormId = formId,
                    Text = discSumAction.Text,
                    Topic = discSumAction.Topic
                });
            }

            return await salesExecReviewDataAccess.AddNewDiscussions(discSumActionToAdd, userService.GetUserDealerGroupName());
        }

        public async Task<int> UpdateApprovalState(SEReviewUpdateApprovalStateParams parms, int userId)
        {
            if (parms.ApprovalState == "Locked" || parms.ApprovalState == "Final")
            {
                int updateFormApproved = await UpdateFormApprovedBy(parms, userId);
            }
            ;

            return await salesExecReviewDataAccess.UpdateApprovalState(parms, userId, userService.GetUserDealerGroupName());
        }


        // SITE SUMMARY //
        public async Task<SEReviewGetSiteSummaryByMeasureResults> GetSiteSummaryByMeasure(SEReviewGetSiteSummaryByMeasureParams parms, int userId, DealerGroupName dealerGroup)
        {
            SEReviewGetSiteSummaryByMeasureResults output = new SEReviewGetSiteSummaryByMeasureResults();
            List<SEReviewSiteMeasureRow> results = new List<SEReviewSiteMeasureRow>();
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, false)).Where(x => x.IsSales && x.IsActive).OrderBy(x => x.SortOrder);
            int[] eligibleSiteIdsForUser = sites.Select(x => x.SiteId).Distinct().ToArray();
            IEnumerable<SEReviewGetSiteSummaryByMeasureVM> siteSummaryByMeasureVMs = await salesExecReviewDataAccess.GetSiteSummaryByMeasure(eligibleSiteIdsForUser, parms.Month, userService.GetUserDealerGroupName());
            string[] measureNames = siteSummaryByMeasureVMs.Select(x => x.MeasureName).Distinct().ToArray();

            // To calculate EPR
            IEnumerable<SEReviewScoringMeasure> currentMeasures = await salesExecReviewDataAccess.GetCurrentActualsForSites(eligibleSiteIdsForUser, parms.Month, userId, dealerGroup);
            IEnumerable<SEReviewScoringMeasure> currentMeasuresMinus1 = await salesExecReviewDataAccess.GetCurrentActualsForSites(eligibleSiteIdsForUser, parms.Month.AddMonths(-1), userId, dealerGroup);
            IEnumerable<SEReviewScoringMeasure> currentMeasuresMinus2 = await salesExecReviewDataAccess.GetCurrentActualsForSites(eligibleSiteIdsForUser, parms.Month.AddMonths(-2), userId, dealerGroup);

            // Make changes to VMs for where Vindis has made custom requests
            foreach (SEReviewGetSiteSummaryByMeasureVM summary in siteSummaryByMeasureVMs)
            {
                if (summary.MeasureName != null)
                {
                    if (summary.MeasureName == "Used & Demo Units" || summary.MeasureName == "New Units")
                    {
                        int salesmanId = summary.PersonId;
                        IEnumerable<SEReviewGetSiteSummaryByMeasureVM> toCalc = siteSummaryByMeasureVMs.Where(x => x.PersonId == salesmanId && (x.MeasureName == "Used & Demo Units" || x.MeasureName == "New Units"));
                        SEReviewGetSiteSummaryByMeasureVM usedUnitsMeasure = toCalc.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                        SEReviewGetSiteSummaryByMeasureVM newUnitsMeasure = toCalc.Where(x => x.MeasureName == "New Units").FirstOrDefault();

                        decimal totalVolAch = usedUnitsMeasure.ActualValue + newUnitsMeasure.ActualValue;
                        decimal totalVolTar = usedUnitsMeasure.Target + newUnitsMeasure.Target;
                        decimal percentageAch = totalVolTar > 0 ? totalVolAch / totalVolTar : 0;

                        if (percentageAch < unitsThreshold) { summary.Actual = 0; }
                    }

                    if (summary.MeasureName.Contains("New CEM") || summary.MeasureName.Contains("Used CEM"))
                    {
                        summary.Actual = CalculateCEM(summary.ActualValue, summary.Target);
                    }

                    if (summary.MeasureName.Contains("Pot") && summary.Actual > profitPotLimit) { summary.Actual = profitPotLimit; }
                }

            }

            const decimal threshold = 0.85M;

            // Create the Site rows //
            foreach (int siteId in eligibleSiteIdsForUser)
            {
                int eprCount = 0;

                IEnumerable<SEReviewScoringMeasure> currentMeasuresForSite = currentMeasures.Where(x => x.SiteId == siteId);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresMinus1ForSite = currentMeasuresMinus1.Where(x => x.SiteId == siteId);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresMinus2ForSite = currentMeasuresMinus2.Where(x => x.SiteId == siteId);

                var commonPersonIds = currentMeasuresForSite
                    .Select(x => x.PersonId)
                    .Intersect(currentMeasuresMinus1ForSite.Select(x => x.PersonId))
                    .Intersect(currentMeasuresMinus2ForSite.Select(x => x.PersonId))
                    .Distinct();

                foreach (int personId in commonPersonIds)
                {
                    var scores = new[]
                    {
                        CalculatePreviousMonthChart(currentMeasuresMinus2ForSite.Where(x => x.PersonId == personId), parms.Month.AddMonths(-2)).Score,
                        CalculatePreviousMonthChart(currentMeasuresMinus1ForSite.Where(x => x.PersonId == personId), parms.Month.AddMonths(-1)).Score,
                        CalculatePreviousMonthChart(currentMeasuresForSite.Where(x => x.PersonId == personId), parms.Month).Score
                    };

                    int lowScoreCount = scores.Count(score => score < threshold);

                    if (lowScoreCount >= 2)
                    {
                        eprCount++;
                    }
                }

                IEnumerable<SEReviewGetSiteSummaryByMeasureVM> vmsForSite = siteSummaryByMeasureVMs.Where(x => x.SiteId == siteId);
                IEnumerable<SEReviewGetSiteSummaryByMeasureVM> scoringMeasures = vmsForSite.Where(x => x.MeasureName != null & x.Weighting > 0);
                IEnumerable<SEReviewGetSiteSummaryByMeasureVM> nonScoringMeasures = vmsForSite.Where(x => x.MeasureName != null & x.Weighting == 0);

                int uniqueSalesmanCount = vmsForSite.Select(x => x.PersonId).Distinct().Count();

                SEReviewSiteMeasureRow siteRow = new SEReviewSiteMeasureRow
                {
                    // Make this more comprehensible?
                    Label = vmsForSite.FirstOrDefault() != null ? vmsForSite.First().Label : sites.Where(x => x.SiteId == siteId).First().SiteDescription,
                    SiteId = siteId,
                    IsTotal = false,
                    LockedPercentage = vmsForSite.FirstOrDefault() != null && uniqueSalesmanCount > 0 ? vmsForSite.First().LockCount / uniqueSalesmanCount : 0,
                    OverallActualPercentage = uniqueSalesmanCount > 0 ? vmsForSite.Select(x => x.Score).Sum() / uniqueSalesmanCount : 0,
                    NonScoringMeasureActualPercentages = new List<SEReviewMeasure>(),
                    ScoringMeasureActualPercentages = new List<SEReviewMeasure>(),
                };

                foreach (string measure in measureNames)
                {
                    SEReviewMeasure measureToAdd = new SEReviewMeasure
                    {
                        MeasureName = measure
                    };

                    if (scoringMeasures.Where(x => x.MeasureName == measure).FirstOrDefault() != null)
                    {
                        decimal actual = SumMeasureAndAverageBySalesman(uniqueSalesmanCount, scoringMeasures, measure);

                        measureToAdd.Actual = actual;

                        if (measure == "Profit Pot")
                        {
                            measureToAdd.Actual = actual > profitPotLimit ? profitPotLimit : actual;
                        }

                        siteRow.ScoringMeasureActualPercentages.Add(measureToAdd);
                    }
                    else
                    {
                        measureToAdd.Actual = SumMeasureAndAverageBySalesman(uniqueSalesmanCount, nonScoringMeasures, measure);
                        siteRow.NonScoringMeasureActualPercentages.Add(measureToAdd);
                    }

                }

                siteRow.EPR = eprCount;
                results.Add(siteRow);
            }

            int siteResultsCount = results.Where(x => x.IsSite).Count();



            // Create Region rows
            var byRegion = siteSummaryByMeasureVMs.ToLookup(x => x.RegionName);

            foreach (var regionGrouping in byRegion)
            {
                IEnumerable<SEReviewGetSiteSummaryByMeasureVM> scoringMeasures = regionGrouping.Where(x => x.MeasureName != null & x.Weighting > 0);
                IEnumerable<SEReviewGetSiteSummaryByMeasureVM> nonScoringMeasures = regionGrouping.Where(x => x.MeasureName != null & x.Weighting == 0);

                int uniqueSalesmanCountForRegion = regionGrouping.Select(x => x.PersonId).Distinct().Count();
                int[] siteIdsForRegion = regionGrouping.Select(x => (int)x.SiteId).Distinct().ToArray();

                SEReviewSiteMeasureRow regionRow = new SEReviewSiteMeasureRow
                {
                    Label = regionGrouping.First().RegionName,
                    LockedPercentage = siteResultsCount > 0 ?
                        results.Where(x => x.IsSite && siteIdsForRegion.Contains(x.SiteId)).Select(x => x.LockedPercentage).Sum() / siteResultsCount : 0,
                    OverallActualPercentage = siteResultsCount > 0 ?
                        results.Where(x => x.IsSite && siteIdsForRegion.Contains(x.SiteId)).Select(x => x.OverallActualPercentage).Sum() / siteResultsCount : 0,
                    NonScoringMeasureActualPercentages = new List<SEReviewMeasure>(),
                    ScoringMeasureActualPercentages = new List<SEReviewMeasure>(),
                    EPR = siteResultsCount > 0 ?
                        results.Where(x => x.IsSite && siteIdsForRegion.Contains(x.SiteId)).Select(x => x.EPR).Sum() : 0,
                };

                foreach (string measure in measureNames)
                {
                    SEReviewMeasure measureToAdd = new SEReviewMeasure
                    {
                        MeasureName = measure
                    };

                    if (scoringMeasures.Where(x => x.MeasureName == measure).FirstOrDefault() != null)
                    {
                        measureToAdd.Actual = uniqueSalesmanCountForRegion > 0 ? scoringMeasures.Where(x => x.MeasureName == measure).Select(x => x.Actual).Sum() / uniqueSalesmanCountForRegion : 0;
                        regionRow.ScoringMeasureActualPercentages.Add(measureToAdd);
                    }
                    else
                    {
                        measureToAdd.Actual = uniqueSalesmanCountForRegion > 0 ? nonScoringMeasures.Where(x => x.MeasureName == measure).Select(x => x.Actual).Sum() / uniqueSalesmanCountForRegion : 0;
                        regionRow.NonScoringMeasureActualPercentages.Add(measureToAdd);
                    }

                }

                results.Add(regionRow);
            }

            // Create Total row //
            int totalUniqueSalesmanCount = siteSummaryByMeasureVMs.Select(x => x.PersonId).Distinct().Count();

            //int siteResultsCount = results.Where(x => x.IsSite).Count();
            SEReviewSiteMeasureRow totalRow = new SEReviewSiteMeasureRow
            {
                Label = summaryRowTitle,
                SiteId = 0,
                IsTotal = true,
                LockedPercentage = siteResultsCount > 0 ? results.Where(x => x.IsSite).Select(x => x.LockedPercentage).Sum() / siteResultsCount : 0,
                OverallActualPercentage = siteResultsCount > 0 ? results.Where(x => x.IsSite).Select(x => x.OverallActualPercentage).Sum() / siteResultsCount : 0,
                NonScoringMeasureActualPercentages = new List<SEReviewMeasure>(),
                ScoringMeasureActualPercentages = new List<SEReviewMeasure>(),
                EPR = siteResultsCount > 0 ? results.Where(x => x.IsSite).Select(x => x.EPR).Sum() : 0,
            };

            foreach (string measure in measureNames)
            {
                SEReviewMeasure measureToAdd = new SEReviewMeasure();
                measureToAdd.MeasureName = measure;

                if (siteSummaryByMeasureVMs.Where(x => x.MeasureName == measure && x.Weighting > 0).FirstOrDefault() != null)
                {
                    measureToAdd.Actual = siteSummaryByMeasureVMs.Where(x => x.MeasureName == measure).Select(x => x.Actual).Sum() / totalUniqueSalesmanCount;
                    totalRow.ScoringMeasureActualPercentages.Add(measureToAdd);
                }
                else
                {
                    measureToAdd.Actual = siteSummaryByMeasureVMs.Where(x => x.MeasureName == measure).Select(x => x.Actual).Sum() / totalUniqueSalesmanCount;
                    totalRow.NonScoringMeasureActualPercentages.Add(measureToAdd);
                }

            }

            results.Add(totalRow);

            output.Results = results.ToList();

            return output;
        }

        public async Task<SEReviewGetSiteSummaryByMonthResults> GetSiteSummaryByMonth(SEReviewGetSiteSummaryByMonthParams parms, int userId, DealerGroupName dealerGroup)
        {
            SEReviewGetSiteSummaryByMonthResults output = new SEReviewGetSiteSummaryByMonthResults();
            List<SEReviewSiteMonthRow> results = new List<SEReviewSiteMonthRow>();
            int monthCounter;

            DateTime endMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            DateTime startMonth = endMonth.AddMonths(-monthsToGoBack);
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, false)).Where(x => x.IsSales && x.IsActive).OrderBy(x => x.SortOrder);
            int[] eligibleSiteIdsForUser = sites.Select(x => x.SiteId).Distinct().ToArray();

            IEnumerable<SEReviewGetSiteSummaryByMonthVM> allVms = await salesExecReviewDataAccess.GetSiteSummaryByMonth(eligibleSiteIdsForUser, startMonth, endMonth, userService.GetUserDealerGroupName());

            // Create Site rows
            foreach (SiteVM site in sites)
            {
                SEReviewSiteMonthRow siteRow = new SEReviewSiteMonthRow();
                IEnumerable<SEReviewGetSiteSummaryByMonthVM> forSite = allVms.Where(x => x.SiteId == site.SiteId);
                siteRow.Label = site.SiteDescription;
                siteRow.Region = site.RegionDescription;
                siteRow.MonthScores = new List<SEReviewMonthScore>();
                siteRow.SiteId = site.SiteId;

                monthCounter = 0;

                startMonth = endMonth.AddMonths(-monthsToGoBack);

                while (monthCounter <= monthsToGoBack)
                {
                    IEnumerable<SEReviewGetSiteSummaryByMonthVM> forSiteForMonth = forSite.Where(x => x.SiteId == site.SiteId && x.Month == startMonth);
                    int totalUniqueSalesmenForSiteForMonth = forSiteForMonth.Select(x => x.PersonId).Distinct().Count();
                    int[] salesmanIds = forSiteForMonth.ToList().Select(x => x.PersonId).Distinct().ToArray();

                    SEReviewMonthScore measureToAdd = new SEReviewMonthScore();

                    measureToAdd.Month = startMonth;

                    // Add certain custom requirements here
                    foreach (SEReviewGetSiteSummaryByMonthVM item in forSiteForMonth)
                    {


                        if (item.MeasureName != null)
                        {
                            if (item.MeasureName == "Used & Demo Units" || item.MeasureName == "New Units")
                            {
                                SEReviewGetSiteSummaryByMonthVM usedM = forSiteForMonth.Where(x => x.MeasureName == "Used & Demo Units" && x.PersonId == item.PersonId).FirstOrDefault();
                                SEReviewGetSiteSummaryByMonthVM newM = forSiteForMonth.Where(x => x.MeasureName == "New Units" && x.PersonId == item.PersonId).FirstOrDefault();

                                decimal totalVolAch = usedM.ActualValue + newM.ActualValue;
                                decimal totalVolTar = usedM.Target + newM.Target;
                                decimal percentageAch = HelperService.DivideByDecimal(totalVolAch, totalVolTar);

                                if (percentageAch < unitsThreshold)
                                {
                                    item.Actual = 0;
                                }
                            }

                            if (parms.MeasureName == "Profit Pot" && item.Actual > profitPotLimit)
                            {
                                item.Actual = profitPotLimit;
                            }

                            if (item.MeasureName == "New CEM (Rolling 3 months)" || item.MeasureName == "Used CEM (Rolling 3 months)")
                            {
                                item.Actual = CalculateCEM(item.Actual, item.Target);
                            }
                        }
                    }

                    // This will be the value being displayed to the grid
                    if (forSiteForMonth.Count() > 0)
                    {
                        if (forSiteForMonth.First().SiteId == 6 && forSiteForMonth.First().Month.Month == 10)
                        {

                        }
                        ;

                        if (parms.MeasureName == "Overall Score")
                        {
                            measureToAdd.Score = totalUniqueSalesmenForSiteForMonth > 0 ? forSiteForMonth.Select(x => x.Score).Sum() / totalUniqueSalesmenForSiteForMonth : 0;
                        }
                        else
                        {
                            measureToAdd.Score = totalUniqueSalesmenForSiteForMonth > 0 ? forSiteForMonth.Where(x => x.MeasureName == parms.MeasureName).Select(x => x.Actual).Sum() / totalUniqueSalesmenForSiteForMonth : 0;
                        }
                    }

                    siteRow.MonthScores.Add(measureToAdd);
                    startMonth = startMonth.AddMonths(1);
                    monthCounter += 1;
                }

                results.Add(siteRow);
            }

            // Create Regional rows
            var byRegion = allVms.ToLookup(x => x.RegionName);

            foreach (var regionGrouping in byRegion)
            {
                SEReviewSiteMonthRow regionRow = new SEReviewSiteMonthRow
                {
                    Label = regionGrouping.First().RegionName,
                    MonthScores = new List<SEReviewMonthScore>()
                };

                int[] siteIdsForRegion = regionGrouping.Select(x => (int)x.SiteId).Distinct().ToArray();

                monthCounter = 0;

                startMonth = endMonth.AddMonths(-monthsToGoBack);

                while (monthCounter <= monthsToGoBack)
                {
                    SEReviewMonthScore measureToAdd = new SEReviewMonthScore();

                    IEnumerable<SEReviewGetSiteSummaryByMonthVM> forRegionForMonth = regionGrouping.Where(x => x.Month == startMonth);
                    int totalUniqueSalesmenForRegionForMonth = forRegionForMonth.Select(x => x.PersonId).Distinct().Count();

                    measureToAdd.Month = startMonth;

                    if (forRegionForMonth.Count() > 0)
                    {
                        if (parms.MeasureName != "Overall Score")
                        {
                            measureToAdd.Score = totalUniqueSalesmenForRegionForMonth > 0 ? forRegionForMonth.Where(x => x.MeasureName == parms.MeasureName).Select(x => x.Actual).Sum() / totalUniqueSalesmenForRegionForMonth : 0;
                        }
                        else
                        {
                            measureToAdd.Score = totalUniqueSalesmenForRegionForMonth > 0 ? forRegionForMonth.Select(x => x.Score).Sum() / totalUniqueSalesmenForRegionForMonth : 0;
                        }
                    }

                    regionRow.MonthScores.Add(measureToAdd);
                    startMonth = startMonth.AddMonths(1);
                    monthCounter += 1;
                }

                results.Add(regionRow);
            }

            // Create Total row 
            SEReviewSiteMonthRow totalRow = new SEReviewSiteMonthRow
            {
                IsTotal = true,
                Label = summaryRowTitle,
                MonthScores = new List<SEReviewMonthScore>()
            };

            monthCounter = 0;

            startMonth = endMonth.AddMonths(-monthsToGoBack);

            while (monthCounter <= monthsToGoBack)
            {
                IEnumerable<SEReviewGetSiteSummaryByMonthVM> allForMonth = allVms.Where(x => x.Month == startMonth);
                int totalUniqueSalesmenForMonth = allForMonth.Select(x => x.PersonId).Distinct().Count();

                SEReviewMonthScore measureToAdd = new SEReviewMonthScore
                {
                    Month = startMonth
                };

                if (allForMonth.Count() > 0)
                {
                    if (parms.MeasureName != "Overall Score")
                    {
                        measureToAdd.Score = totalUniqueSalesmenForMonth > 0 ?
                            allForMonth.Where(x => x.MeasureName == parms.MeasureName).Select(x => x.Actual).Sum() / totalUniqueSalesmenForMonth : 0;
                    }
                    else
                    {
                        measureToAdd.Score = totalUniqueSalesmenForMonth > 0 ?
                            allForMonth.Select(x => x.Score).Sum() / totalUniqueSalesmenForMonth : 0;
                    }
                }

                totalRow.MonthScores.Add(measureToAdd);
                startMonth = startMonth.AddMonths(1);
                monthCounter += 1;
            }

            results.Add(totalRow);

            output.Results = results;
            return output;
        }


        // PERSON SUMMARY //
        public async Task<SEReviewGetPersonSummaryByMeasureResults> GetPersonSummaryByMeasure(SEReviewGetPersonSummaryByMeasureParams parms, int userId, DealerGroupName dealerGroup)
        {
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = configuration.GetConnectionString(dgName);
            var siteDataAccess = new SiteDataAccess(_connectionString);
            IEnumerable<SiteVM> sites = (await siteDataAccess.GetSites(userId, dealerGroup, false)).Where(x => x.IsSales && x.IsActive).OrderBy(x => x.SortOrder);

            int[] siteIds = parms.SiteIds.Intersect(sites.Select(x => x.SiteId)).ToArray();

            SEReviewGetPersonSummaryByMeasureResults output = new SEReviewGetPersonSummaryByMeasureResults
            {
                Results = new List<SEReviewPersonMeasureRow>()
            };

            IEnumerable<SEReviewObjectiveVM> objectives = await salesExecReviewDataAccess.GetObjectives(siteIds, parms.Month, parms.Month, dealerGroup);
            IEnumerable<SEReviewMeasureForSalesman> allFormData = await salesExecReviewDataAccess.GetPersonSummaryByMeasure(siteIds, parms.Month, dealerGroup);


            // To calculate EPR
            IEnumerable<SEReviewScoringMeasure> currentMeasures = await salesExecReviewDataAccess.GetCurrentActualsForSites(siteIds, parms.Month, userId, dealerGroup);
            IEnumerable<SEReviewScoringMeasure> currentMeasuresMinus1 = await salesExecReviewDataAccess.GetCurrentActualsForSites(siteIds, parms.Month.AddMonths(-1), userId, dealerGroup);
            IEnumerable<SEReviewScoringMeasure> currentMeasuresMinus2 = await salesExecReviewDataAccess.GetCurrentActualsForSites(siteIds, parms.Month.AddMonths(-2), userId, dealerGroup);

            const decimal threshold = 0.85M;

            // Do People rows
            foreach (int siteId in siteIds)
            {
                IEnumerable<SEReviewObjectiveVM> objectivesForSite = objectives.Where(x => x.SiteId == siteId);
                int[] salesmanIdsForSite = allFormData.Where(x => x.SiteId == siteId).ToList().Select(x => x.PersonId).Distinct().ToArray();

                IEnumerable<SEReviewScoringMeasure> currentMeasuresForSite = currentMeasures.Where(x => x.SiteId == siteId);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresMinus1ForSite = currentMeasuresMinus1.Where(x => x.SiteId == siteId);
                IEnumerable<SEReviewScoringMeasure> currentMeasuresMinus2ForSite = currentMeasuresMinus2.Where(x => x.SiteId == siteId);

                var commonPersonIds = currentMeasuresForSite
                    .Select(x => x.PersonId)
                    .Intersect(currentMeasuresMinus1ForSite.Select(x => x.PersonId))
                    .Intersect(currentMeasuresMinus2ForSite.Select(x => x.PersonId))
                    .Distinct();

                // Create a row for each salesman 
                foreach (int salesmanId in salesmanIdsForSite)
                {

                    SEReviewPersonMeasureRow personRow = new SEReviewPersonMeasureRow();
                    List<SEReviewMeasure> scoringMeasures = new List<SEReviewMeasure>();
                    List<SEReviewMeasure> nonScoringMeasures = new List<SEReviewMeasure>();

                    List<SEReviewMeasureForSalesman> allFormDataForSalesman = allFormData.Where(x => x.PersonId == salesmanId).ToList();

                    personRow.PersonId = salesmanId;
                    personRow.Label = allFormDataForSalesman.FirstOrDefault() != null ? allFormDataForSalesman.FirstOrDefault().Label : "Unknown Salesman";

                    // Check if EPR
                    if (commonPersonIds.Contains(salesmanId))
                    {
                        var scores = new[]
                        {
                            CalculatePreviousMonthChart(currentMeasuresMinus2ForSite.Where(x => x.PersonId == salesmanId), parms.Month.AddMonths(-2)).Score,
                            CalculatePreviousMonthChart(currentMeasuresMinus1ForSite.Where(x => x.PersonId == salesmanId), parms.Month.AddMonths(-1)).Score,
                            CalculatePreviousMonthChart(currentMeasuresForSite.Where(x => x.PersonId == salesmanId), parms.Month).Score
                        };

                        int lowScoreCount = scores.Count(score => score < threshold);

                        if (lowScoreCount >= 2)
                        {
                            personRow.EPR = "Yes";
                        }
                    }

                    decimal overall = 0;

                    foreach (SEReviewObjectiveVM obj in objectivesForSite)
                    {
                        SEReviewMeasure measureToAdd = new SEReviewMeasure();
                        SEReviewMeasureForSalesman formMeasure = allFormDataForSalesman.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault();

                        measureToAdd.MeasureName = obj.MeasureName;
                        decimal actual = formMeasure != null ? formMeasure.Actual : 0;

                        // Default condition
                        measureToAdd.Actual = obj.Value != 0 ? (actual / obj.Value) : 0;

                        // Special conditions as per Vindis requests
                        if (obj.MeasureName == "Used & Demo Units" || obj.MeasureName == "New Units")
                        {
                            SEReviewMeasureForSalesman usedM = allFormDataForSalesman.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                            SEReviewMeasureForSalesman newM = allFormDataForSalesman.Where(x => x.MeasureName == "New Units").FirstOrDefault();

                            SEReviewObjectiveVM usedO = objectivesForSite.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                            SEReviewObjectiveVM newO = objectivesForSite.Where(x => x.MeasureName == "New Units").FirstOrDefault();

                            decimal totalVolAch = 0;

                            if (usedM != null && newM != null)
                            {
                                totalVolAch = usedM.Actual + newM.Actual;
                            }

                            if (usedM == null && newM != null)
                            {
                                totalVolAch = 0 + newM.Actual;
                            }

                            if (usedM != null && newM == null)
                            {
                                totalVolAch = usedM.Actual + 0;
                            }

                            decimal totalVolTar = usedO.Value + newO.Value;
                            decimal percentageAch = totalVolTar > 0 ? totalVolAch / totalVolTar : 0;

                            if (percentageAch < unitsThreshold) { measureToAdd.Actual = 0; }

                        }

                        if (obj.MeasureName == "Profit Pot")
                        {
                            if (obj.Value == 0) { measureToAdd.Actual = 0; }
                            else { measureToAdd.Actual = (actual / obj.Value) > profitPotLimit ? profitPotLimit : (actual / obj.Value); }
                        }

                        if (obj.IsScoring && obj.MeasureName.Contains("CEM"))
                        {
                            measureToAdd.Actual = CalculateCEM(actual, obj.Value); ;
                        }
                        ;


                        if (obj.IsScoring)
                        {
                            scoringMeasures.Add(measureToAdd);
                            overall += measureToAdd.Actual * obj.Weighting;
                        }
                        else
                        {
                            nonScoringMeasures.Add(measureToAdd);
                        }

                    }

                    personRow.ScoringMeasureActualPercentages = scoringMeasures;
                    personRow.NonScoringMeasureActualPercentages = nonScoringMeasures;
                    personRow.OverallActualPercentage = overall;

                    personRow.ApprovalState = allFormDataForSalesman != null && allFormDataForSalesman.Count > 0 ?
                        allFormDataForSalesman.First().ApprovalState : "Draft";

                    output.Results.Add(personRow);
                }

            }

            output.Results = output.Results.OrderBy(x => x.Label).ToList();

            int uniqueSalesmancount = allFormData.ToList().Select(x => x.PersonId).Distinct().Count();

            // Do Total row
            SEReviewPersonMeasureRow totalRow = new SEReviewPersonMeasureRow
            {
                IsTotal = true,
                PersonId = 0,
                Label = summaryRowTitle,
                ScoringMeasureActualPercentages = new List<SEReviewMeasure>(),
                NonScoringMeasureActualPercentages = new List<SEReviewMeasure>(),
                OverallActualPercentage = uniqueSalesmancount > 0 ? output.Results.Select(x => x.OverallActualPercentage).Sum() / uniqueSalesmancount : 0,
                EPR = ""
            };

            foreach (SEReviewObjectiveVM obj in objectives)
            {
                SEReviewMeasure measureToAdd = new SEReviewMeasure();
                decimal totalForMeasure = 0;

                foreach (SEReviewPersonMeasureRow row in output.Results)
                {
                    if (obj.IsScoring)
                    {
                        if (row.ScoringMeasureActualPercentages.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault() != null)
                        {
                            totalForMeasure += row.ScoringMeasureActualPercentages.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault().Actual;
                        }
                    }
                    else
                    {
                        if (row.NonScoringMeasureActualPercentages.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault() != null)
                        {
                            totalForMeasure += row.NonScoringMeasureActualPercentages.Where(x => x.MeasureName == obj.MeasureName).FirstOrDefault().Actual;
                        }
                    }

                }

                if (uniqueSalesmancount > 0) { totalForMeasure /= uniqueSalesmancount; }

                measureToAdd.Actual = totalForMeasure;
                measureToAdd.MeasureName = obj.MeasureName;

                if (obj.IsScoring)
                {
                    totalRow.ScoringMeasureActualPercentages.Add(measureToAdd);
                }
                else
                {
                    totalRow.NonScoringMeasureActualPercentages.Add(measureToAdd);
                }

            }

            output.Results.Add(totalRow);

            return output;
        }

        public async Task<SEReviewGetPersonSummaryByMonthResults> GetPersonSummaryByMonth(SEReviewGetPersonSummaryByMonthParams parms, int userId)
        {
            SEReviewGetPersonSummaryByMonthResults output = new SEReviewGetPersonSummaryByMonthResults();
            List<SEReviewPersonMonthRow> results = new List<SEReviewPersonMonthRow>();

            DateTime startMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(-monthsToGoBack);

            IEnumerable<SEReviewGetPersonSummaryByMonthVM> personSummaryByMonthVMs = await salesExecReviewDataAccess.GetPersonSummaryByMonth(parms.SiteIds, startMonth, new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1), userService.GetUserDealerGroupName());
            int[] salesmanIds = personSummaryByMonthVMs.ToList().Select(x => x.SalesmanId).Distinct().ToArray();

            int monthCounter = 0;

            // Do Salesman rows
            while (monthCounter <= monthsToGoBack)
            {
                int salesmanCountForMonth = personSummaryByMonthVMs.Where(x => x.Month == startMonth)
                                                                    .Select(x => x.SalesmanId)
                                                                    .Distinct().Count();

                List<SEReviewGetPersonSummaryByMonthVM> vmsForMonthAndMeasure;

                if (parms.MeasureName != "Overall Score")
                {
                    vmsForMonthAndMeasure = personSummaryByMonthVMs.Where(x => x.Month == startMonth
                                                && x.MeasureName == parms.MeasureName
                                                    ).ToList();
                }
                else
                {
                    vmsForMonthAndMeasure = personSummaryByMonthVMs.Where(x => x.Month == startMonth).ToList();
                }

                foreach (int salesman in salesmanIds)
                {

                    // Create empty rows to begin with
                    if (monthCounter == 0)
                    {
                        results.Add(new SEReviewPersonMonthRow
                        {
                            Label = personSummaryByMonthVMs.Where(x => x.SalesmanId == salesman).FirstOrDefault() != null
                                ? personSummaryByMonthVMs.Where(x => x.SalesmanId == salesman).First().Label : "Unknown Salesman",
                            PersonId = salesman,
                            Month = startMonth,
                            MonthScores = new List<SEReviewMonthScore>()
                        });
                    }

                    List<SEReviewGetPersonSummaryByMonthVM> vmsForMonthAndSalesman =
                        vmsForMonthAndMeasure.Where(x => x.SalesmanId == salesman).ToList();

                    SEReviewMonthScore newMonthScore = new SEReviewMonthScore();
                    newMonthScore.Month = startMonth;

                    foreach (SEReviewGetPersonSummaryByMonthVM item in vmsForMonthAndSalesman)
                    {

                        if (item.MeasureName != null)
                        {
                            if (item.MeasureName == "Used & Demo Units" || item.MeasureName == "New Units")
                            {
                                SEReviewGetPersonSummaryByMonthVM usedM = vmsForMonthAndSalesman.Where(x => x.MeasureName == "Used & Demo Units").FirstOrDefault();
                                SEReviewGetPersonSummaryByMonthVM newM = vmsForMonthAndSalesman.Where(x => x.MeasureName == "New Units").FirstOrDefault();

                                decimal totalVolAch = usedM.ActualValue + newM.ActualValue;
                                decimal totalVolTar = usedM.Target + newM.Target;
                                decimal percentageAch = HelperService.DivideByDecimal(totalVolAch, totalVolTar);

                                if (percentageAch < unitsThreshold) { item.Actual = 0; }
                            }

                            if (item.MeasureName.Contains("Pot") && item.Actual > profitPotLimit) { item.Actual = profitPotLimit; }

                            if (item.MeasureName == "New CEM (Rolling 3 months)" || item.MeasureName == "Used CEM (Rolling 3 months)")
                            {
                                item.Actual = CalculateCEM(item.Actual, item.Target);
                            }
                        }
                    }

                    // If we have any VMs for this salesman for this month, calculate
                    if (vmsForMonthAndSalesman.Count > 0)
                    {

                        if (parms.MeasureName != "Overall Score")
                        {
                            newMonthScore.Score = vmsForMonthAndSalesman.Select(x => x.Actual).Sum();
                        }
                        else
                        {
                            newMonthScore.Score = vmsForMonthAndSalesman.Select(x => x.Score).Sum();
                        }
                    }

                    results.Where(x => x.PersonId == salesman).First().MonthScores.Add(newMonthScore);
                }

                startMonth = startMonth.AddMonths(1);
                monthCounter += 1;
            }

            // Do Total row
            SEReviewPersonMonthRow totalRow = new SEReviewPersonMonthRow
            {
                Label = summaryRowTitle,
                IsTotal = true,
                MonthScores = new List<SEReviewMonthScore>()
            };

            // Reset counters
            monthCounter = 0;
            startMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1).AddMonths(-monthsToGoBack);

            while (monthCounter <= monthsToGoBack)
            {
                IEnumerable<SEReviewGetPersonSummaryByMonthVM> allForMonth = personSummaryByMonthVMs.Where(x => x.Month == startMonth);

                SEReviewMonthScore measureToAdd = new SEReviewMonthScore
                {
                    Month = startMonth
                };

                int totalUniqueSalesmenForMonth = personSummaryByMonthVMs.Where(x => x.Month == startMonth)
                                                .Select(x => x.SalesmanId)
                                                .Distinct().Count();

                if (allForMonth.Count() > 0)
                {
                    if (parms.MeasureName == "Overall Score")
                    {
                        measureToAdd.Score = totalUniqueSalesmenForMonth > 0 ?
                            allForMonth.Select(x => x.Score).Sum() / totalUniqueSalesmenForMonth : 0;
                    }
                    else
                    {
                        measureToAdd.Score = totalUniqueSalesmenForMonth > 0 ?
                            allForMonth.Where(x => x.MeasureName == parms.MeasureName).Select(x => x.Actual).Sum() / totalUniqueSalesmenForMonth : 0;
                    }
                }

                totalRow.MonthScores.Add(measureToAdd);
                startMonth = startMonth.AddMonths(1);
                monthCounter += 1;
            }

            results.Add(totalRow);

            output.Results = results.OrderBy(x => x.Label).ToList();
            return output;
        }


        // HELPER FUNCTIONS //
        private decimal CalculateCEM(decimal actual, decimal target)
        {
            decimal cemActualMinusTarget = actual - target;

            if (cemActualMinusTarget <= -0.06M) { return 0; }
            if (cemActualMinusTarget <= -0.04M) { return 0.40M; }
            if (cemActualMinusTarget <= -0.02M) { return 0.70M; }
            if (cemActualMinusTarget > -0.02M && cemActualMinusTarget < 0.02M) { return 1.00M; }
            if (cemActualMinusTarget >= 0.1M) { return 1.40M; }
            if (cemActualMinusTarget >= 0.08M) { return 1.32M; }
            if (cemActualMinusTarget >= 0.06M) { return 1.24M; }
            if (cemActualMinusTarget >= 0.04M) { return 1.16M; }
            if (cemActualMinusTarget >= 0.02M) { return 1.08M; }

            return 0;
        }

        private Form CreateNewForm(SEReviewSaveFormParams parms, int userId)
        {
            return new Form
            {
                ApprovalStateId = 1,
                Month = parms.Month,
                PersonId = parms.SalesmanId,
                LastSaved = DateTime.Now,
                LastSavedById = userId
            };
        }

        private decimal SumMeasureAndAverageBySalesman(int uniqueSalesmanCount, IEnumerable<SEReviewGetSiteSummaryByMeasureVM> measureList, string measureName)
        {
            if (uniqueSalesmanCount < 1) { return 0; }
            return measureList.Where(x => x.MeasureName == measureName).Select(x => x.Actual).Sum() / uniqueSalesmanCount;
        }

        private async Task<List<SEReviewMeasure>> GetFixedActuals(int salesmanId, DateTime monthStart)
        {
            IEnumerable<SEReviewMeasure> fixedMeasures = await salesExecReviewDataAccess.GetFixedActuals(salesmanId, monthStart, userService.GetUserDealerGroupName());
            List<SEReviewMeasure> fixedMeasuresList = fixedMeasures.ToList();

            //SPK-2410 - Use data from logic written for Commision
            string connectionString = Startup.Configuration.GetConnectionString(DealerGroupConnectionName.GetConnectionName(userService.GetUserDealerGroupName()));
            Spark.BusinessLogic.Vindis.CommissionLogicService cl = new Spark.BusinessLogic.Vindis.CommissionLogicService(connectionString);
            var allRecords = await cl.GetFullStatementForMonth(monthStart, true, salesmanId);
            var salesmanRecord = allRecords.FirstOrDefault(s => s.SalesmanId == salesmanId);
            decimal profitPotValue = 0;

            if (salesmanRecord != null)
            {
                if (salesmanRecord.CommissionItems != null && salesmanRecord.CommissionItems.ToList().Count() > 0)
                {
                    profitPotValue = salesmanRecord.CommissionItems.Sum(c => c.DataItem.ChassisProfit + c.DataItem.FinanceProfit);
                }
            }

            fixedMeasuresList.FirstOrDefault(f => f.MeasureName == "Profit Pot").Actual = profitPotValue;
            //-----------------------------------------------------

            return fixedMeasuresList;
        }


    }
}