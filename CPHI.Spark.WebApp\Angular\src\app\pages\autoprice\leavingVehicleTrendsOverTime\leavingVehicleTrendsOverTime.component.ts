import {ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {ConstantsService} from 'src/app/services/constants.service';
import {BIChartTileDataType} from 'src/app/components/biChartTile/biChartTile.component';
import {Subscription} from 'rxjs';
import {LeavingVehicleDetailService} from '../leavingVehicleDetail/leavingVehicleDetail.service';
import {Router} from '@angular/router';
import {ExternalFiltersForLeavingVehicleAnalysis} from 'src/app/model/ExternalFiltersForLeavingVehicleAnalysis';
import {CphPipe} from 'src/app/cph.pipe';
import {LeavingVehicleTrendsService} from './leavingVehicleTrendsOverTime.service';
import {MenuItemNew} from 'src/app/model/main.model';
import {SelectionsService} from 'src/app/services/selections.service';
import {WasNowEnum} from '../leavingVehicles/leavingVehicleWasNowEnum';
import {MultiSelectMonth} from '../../../components/datePickerMultiSelect/datePickerMultiSelect.component';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import moment from 'moment';

@Component({
  selector: 'app-leavingVehicleTrendsOverTime',
  templateUrl: './leavingVehicleTrendsOverTime.component.html',
  styleUrls: ['./leavingVehicleTrendsOverTime.component.scss'],
//  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LeavingVehicleTrendsOverTimeComponent implements OnInit, OnDestroy {
  filterChoiceEmitterSub: Subscription;
  highlightChoiceEmitterSub: Subscription;
  wasNowEnum = WasNowEnum;
  public dataTypes = BIChartTileDataType;
  thisMonth: MultiSelectMonth[];
  months: Date[];
  fromDate: any;
  minDate: any;
  maxDate: any;
  toDate: any;

  displayStartDate(wasNow: WasNowEnum) {
     return moment(this.service.startDate[wasNow]).format('YYYY-MM-DD');
  }

  displayEndDate(wasNow: WasNowEnum) {
     return moment(this.service.endDate[wasNow]).format('YYYY-MM-DD');
  }

  constructor(
    public service: LeavingVehicleTrendsService,
    public modalService: NgbModal,
    public trendsService: LeavingVehicleTrendsService,
    public constants: ConstantsService,
    public leavingVehicleDetailService: LeavingVehicleDetailService,
    public router: Router,
    private cphPipe: CphPipe,
    public selections: SelectionsService
  ) {

  }

  async ngOnInit() {
    this.selections.triggerSpinner.next({ show: true, message: 'Loading...' });

    this.trendsService.initParams();
    await this.trendsService.getData();

    this.highlightChoiceEmitterSub = this.trendsService.highlightChoiceMadeEmitter.subscribe(res => {
      this.trendsService.updateHighlightedData();
    });

    this.filterChoiceEmitterSub = this.trendsService.filterChoiceMadeEmitter.subscribe(res => {
      this.trendsService.updateFilteredData();
    });
  }

  ngOnDestroy() {
    if (this.highlightChoiceEmitterSub) {
       this.highlightChoiceEmitterSub.unsubscribe();
    }
    if (this.filterChoiceEmitterSub) {
       this.filterChoiceEmitterSub.unsubscribe();
    }
  }

  goToLeavingVehiclesAnalysis(wasNow: WasNowEnum) {
    this.buildFilterParams(wasNow);
    this.leavingVehicleDetailService.startDate = this.trendsService.startDate[wasNow];
    this.leavingVehicleDetailService.endDate = this.trendsService.endDate[wasNow];

    const menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/leavingVehicleDetail');
    if (menuItem) { this.constants.navigateByUrl(menuItem); }
  }

  buildFilterParams(wasNow: WasNowEnum) {

    const filterModel = {};

    const filters: ExternalFiltersForLeavingVehicleAnalysis = this.trendsService.externalFilterForLeavingVehicleAnalysis;

    this.trendsService.highlightChoices[wasNow].forEach(choice => {
      filters[choice.FieldName] = choice.ChosenValues.length > 0 ? choice.ChosenValues.map(val => val.toString()) : null;
    });

    const filterNames: string[] = [
      'BodyType',
      'DaysListedBand',
      'RetailerSiteName',
      'FirstPPBand',
      'FuelType',
      'LastPPBand',
      'LastPriceBand',
      'LastPriceIndicator',
      'Make',
      'Mileage',
      'Model',
      'Region',
      'RegYear',
      'RetailRatingBand',
      'RetailerSiteName',
      'TransmissionType',
      'IsOnStrategy',
      'OptedOutPctBand'
    ];

    filterNames.forEach(name => {
      if (filters['name]']) {
        filterModel[name] = {
          filterType: 'set',
          values: filters['name']
        };
      }
    });

    this.leavingVehicleDetailService.setExternalFilterModel(filterModel);
  }

  firstPPTitle(wasNow: WasNowEnum) {
    if (this.trendsService.rawData[wasNow] == null) { return ''; }
    if (this.trendsService.summaryStats[wasNow] == null) { return ''; }
    const isFiltered = this.trendsService.rawDataHighlighted[wasNow]?.length != this.trendsService.rawData[wasNow]?.length;
    let result = `First Price Band (${this.cphPipe.transform(this.trendsService.summaryStats[wasNow].highlighted.firstPP, 'percent', 1)}`
    if (isFiltered) { result += ` vs ${this.cphPipe.transform(this.trendsService.summaryStats[wasNow].all.firstPP, 'percent', 1)}` }
    result += ')';
    return result;
  }

  lastPPTitle(wasNow: WasNowEnum) {
    if (this.trendsService.rawData[wasNow] == null) { return ''; }
    if (this.trendsService.summaryStats[wasNow] == null) { return ''; }
    const isFiltered = this.trendsService.rawDataHighlighted[wasNow].length != this.trendsService.rawData[wasNow].length;
    let result = `Last Price Band (${this.cphPipe.transform(this.trendsService.summaryStats[wasNow].highlighted.lastPP, 'percent', 1)}`;
    if (isFiltered) { result += ` vs ${this.cphPipe.transform(this.trendsService.summaryStats[wasNow].all.lastPP, 'percent', 1)}`; }
    result += ')';
    return result;
  }

   setDate(event: any, isStart: boolean, wasNow: WasNowEnum) {

     if (isStart) {
      this.service.startDate[wasNow] = event;
     }
     else {
        this.service.endDate[wasNow] = event;
     }
     this.service.getData().then(() => {
     });
   }
}
