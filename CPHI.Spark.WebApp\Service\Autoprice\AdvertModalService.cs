﻿using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using System.Linq;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using System.Globalization;
using CPHI.Spark.Model.Services;
using StockPulse.WebApi.Model;
using System.Diagnostics;
using Microsoft.AspNetCore.DataProtection;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Net;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
   public interface IAdvertModalService
   {
      Task<int> GetTotalAdvertsForAdvertiser(string advertiserName, int advertiserId);
      Task<VehicleDetailModalItem> GetAdvertModal(int advertId, DealerGroupName dealerGroup);
      Task<CompetitorSummary> GetAdvertModalCompetitorAnalysis(GetCompetitorAnalysisParams parms, DealerGroupName dealerGroup);

      Task<List<DayToSellAndPriceIndicatorScenario>> GetEstimatedDayToSellAndPriceIndicatorForScenarios(int advertiserId,
          string derivativeId, DateTime? firstRegisteredDate, int meterReading, decimal currentStrategyPrice, int currentAdvertisedPrice,
          bool vehicleHasOptionsSpecified, List<string> vehicleAdvertPortalOptions, decimal averageValuation, decimal adjustedValuation);
   }
   public class AdvertModalService : IAdvertModalService
   {
      private readonly AutoTraderValuationsClient atValuationsClient;
      private readonly AutoTraderFutureValuationsClient atFutureValsClient;
      private readonly AutoTraderVehicleMetricsClient atMetricsClient;
      private readonly AutoTraderApiTokenClient atTokenClient;
      private readonly AutoTraderCompetitorClient competitorClient;
      private readonly IUserService userService;
      private readonly IConfiguration configuration;
      private readonly string _connectionString;
      private readonly Model.DealerGroupName dealerGroup;
      private readonly IVehicleValuationService vehicleValuationService;
      private readonly HttpClient httpClient;

      public AdvertModalService(
          //AutoTraderApiClient atClient,
          IUserService userService,
           IConfiguration configuration
  ,
           IVehicleValuationService vehicleValuationService,
           IHttpClientFactory httpClientFactory)
      {
         this.httpClient = httpClientFactory.CreateClient();
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         atValuationsClient = new AutoTraderValuationsClient(httpClientFactory, apiKey, apiSecret, baseURL);
         atMetricsClient = new AutoTraderVehicleMetricsClient(httpClientFactory, apiKey, apiSecret, baseURL);
         atFutureValsClient = new AutoTraderFutureValuationsClient(httpClientFactory, apiKey, apiSecret, baseURL);
         atTokenClient = new AutoTraderApiTokenClient(httpClientFactory, apiKey, apiSecret, baseURL);
         competitorClient = new AutoTraderCompetitorClient(httpClientFactory, apiKey, apiSecret, baseURL);


         this.userService = userService;
         this.configuration = configuration;

         dealerGroup = userService.GetUserDealerGroupName();
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         _connectionString = this.configuration.GetConnectionString(dgName);
         this.vehicleValuationService = vehicleValuationService;
         this.httpClient = httpClient;
      }


      public async Task<CompetitorSummary> GetAdvertModalCompetitorAnalysis(GetCompetitorAnalysisParams parms, DealerGroupName dealerGroup)
      {
         var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         var taskAdvert = vehicleAdvertsDataAccess.GetVehicleAdvertDetails(parms.AdvertId, null, ConstantsCache.ProvideBandingsDictionary(dealerGroup), dealerGroup);
         var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var taskRetailerSites = retailerSitesDataAccess.GetRetailerSites(dealerGroup);
         await Task.WhenAll(taskAdvert, taskRetailerSites);

         VehicleAdvertDetail advert = taskAdvert.Result;
         var retailerSites = taskRetailerSites.Result;
         var postcode = retailerSites.First(x => x.Id == advert.RetailerSiteId).Postcode;
         var plateRange = retailerSites.First(x => x.Id == advert.RetailerSiteId).CompetitorPlateRange;

         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         string token = (await atTokenClient.GetToken()).AccessToken;

         VehicleListing competitors = await competitorClient.GetCompetitorAnalysis(parms.RetailerSiteRetailerId, parms.WebsiteSearchIdentifier, parms.UserChoiceParams.MinPlate,
             parms.UserChoiceParams.MaxPlate, parms.UserChoiceParams.Radius, parms.OurVehicleParams.Postcode, token, null);


         CompetitorSummary competitorSummary = new CompetitorSummary(competitors, parms.OurVehicleParams.SiteGeoY, parms.OurVehicleParams.SiteGeoX,
         parms.OurVehicleParams.AdvertisedPrice, parms.OurVehicleParams.Valuation, parms.UserChoiceParams.Radius, null, true);

         decimal pricePosition = (decimal)parms.OurVehicleParams.AdvertisedPrice / parms.OurVehicleParams.Valuation;
         competitorSummary.CompetitorVehicles.Add(new CompetitorVehicle(
             parms.OurVehicleParams.SiteName,
             parms.OurVehicleParams.VehicleReg,
             parms.OurVehicleParams.VehicleMileage,
             parms.OurVehicleParams.VehicleYear,
             parms.OurVehicleParams.AdvertisedPrice,
             pricePosition, parms.OurVehicleParams.ImageURL
             ));
         // }

         return competitorSummary;
      }





      public async Task<VehicleDetailModalItem> GetAdvertModal(int advertId, DealerGroupName dealerGroup)
      {
         // Need to know if it has left, before fetching date related info
         var leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
         var leavingPricesItem = await leavingPricesDataAccess.GetLeavingPriceForAdvertId(advertId);
         //also have to work out if advert is still here in which case we DO NOT want to load the leaving version of it
         VehicleAdvertsDataAccess vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         bool isThereATodayLatestSnapshot = await vehicleAdvertsDataAccess.IsThereATodaySnapshotForAdId(advertId);

         bool includingLeavingDetail = leavingPricesItem != null && !isThereATodayLatestSnapshot;

         DateTime? valuationDate = includingLeavingDetail ? leavingPricesItem.LeavingDate : null;
         DateTime strategyDate = includingLeavingDetail ? leavingPricesItem.LeavingDate : DateTime.Today;

         //setup data accesses
         StrategyDataAccess strategyDataAccess = new StrategyDataAccess(_connectionString);
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         VehicleAdvertCommentsDataAccess vehicleAdvertCommentsDataAccess = new VehicleAdvertCommentsDataAccess(_connectionString);
         PriceChangesDataAccess priceChangeDataAccess = new PriceChangesDataAccess(_connectionString);
         // 1. Create and fetch first set of tasks
         Task<VehicleAdvertDetail> taskAdvert = vehicleAdvertsDataAccess.GetVehicleAdvertDetails(advertId, valuationDate, ConstantsCache.ProvideBandingsDictionary(dealerGroup), dealerGroup);
         Task<IEnumerable<StrategyPriceBuildUp>> taskPriceBuildUp = strategyDataAccess.GetStrategyPriceBuildUp(advertId, strategyDate);
         Task<IEnumerable<SameModelAdvert>> taskSameModelAdverts = vehicleAdvertsDataAccess.GetSameModelAdvertsForAdvertId(advertId, dealerGroup);
         Task<IEnumerable<AdvertPriceAndViewsHistoryItem>> taskPriceHistory = vehicleAdvertsDataAccess.GetAdvertPriceAndViewsHistory(advertId);
         Task<List<RetailerSite>> taskRetailerSites = retailerSitesDataAccess.GetRetailerSites(dealerGroup);
         Task<PriceChangeToday> todayPriceChangeTask = priceChangeDataAccess.GetTodayAutoPriceChangeForAd(advertId);

         await Task.WhenAll(taskAdvert, taskPriceBuildUp, taskSameModelAdverts, taskPriceHistory, taskRetailerSites, todayPriceChangeTask);
         VehicleAdvertDetail advert = taskAdvert.Result;

         IEnumerable<StrategyPriceBuildUp> priceBuildUp = taskPriceBuildUp.Result;
         IEnumerable<SameModelAdvert> sameModelAdverts = taskSameModelAdverts.Result;
         IEnumerable<AdvertPriceAndViewsHistoryItem> priceHistory = taskPriceHistory.Result;
         List<RetailerSite> retailerSites = taskRetailerSites.Result;   //1.5

         //Get portalOptions if specified. 
         if (advert.VehicleHasOptionsSpecified)
         {
            var optionsResult = await vehicleAdvertsDataAccess.GetVehicleAdvertsPortalOptions(new List<int>() { advert.AdId }, dealerGroup);
            advert.VehicleAdvertPortalOptions = optionsResult.Select(o => o.PortalOption.OptionName).ToList();
         }

         // 1.5. history may be incomplete for example if we've just onboarded a customer.
         List<AdvertPriceAndViewsHistoryItem> newHistory = FillInBlanksInHistory(advert, priceHistory);

         // 2. Create and fetch next set of tasks
         int userId = userService.GetUserId();

         DateTime adFirstRegdDate;
         int mileageToUse;
         ConstantMethodsService.GetFirstRegisteredDateAndMileage(advert.FirstRegisteredDate, advert.DateOnForecourt, advert.OdometerReading, out adFirstRegdDate, out mileageToUse);

         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         TokenResponse tokenResponse = await atTokenClient.GetToken();

         Task<List<FutureValuationPoint>> taskFutureValuations = CreateTaskForFutureValuations(
             mileageToUse,
             advert.DerivativeId,
             advert.DaysToSellAtCurrentSelling,
             adFirstRegdDate,
             advert.RetailerSiteRetailerId,
             advert.ValuationMktAvRetail,
             advert.ValuationAdjRetail,
             tokenResponse
             );

         var retailerSite = retailerSites.First(x => x.Id == advert.RetailerSiteId);

         bool adjustToEquivalentRetailPrice = advert.IsTradePricing == true && advert.TradeMarginPercentage > 0;

         int retailPriceEquivalent = (int)advert.AdvertisedPrice;

         if (adjustToEquivalentRetailPrice)
         {
            retailPriceEquivalent = (int)(retailPriceEquivalent / advert.TradeMarginPercentage.Value);
            retailPriceEquivalent = (int)(retailPriceEquivalent + (advert.TradeMarginAmount ?? 0));
         }



         if (includingLeavingDetail)
         {
            if (leavingPricesItem.LastVehicleAdvertSnapshot != null && leavingPricesItem.FirstVehicleAdvertSnapshot != null)
            {
               advert.DaysListed = (leavingPricesItem.LastVehicleAdvertSnapshot.SnapshotDate.Date - leavingPricesItem.FirstVehicleAdvertSnapshot.SnapshotDate.Date).Days;
            }

            advert.LeavingSnapshotDate = leavingPricesItem.FinalSnapshotDate;
         }


         int radius = 1000;
         CompetitorSearchParams searchParams = new CompetitorSearchParams(advert, retailerSite, tokenResponse, radius);
         var taskCompetitors = competitorClient.GetCompetitorNew(searchParams, null);

         Task<List<VehicleAdComment>> taskComments = vehicleAdvertCommentsDataAccess.GetCommentsForAdvertId(advert.AdId, userId);

         var taskScenarios = GetEstimatedDayToSellAndPriceIndicatorForScenarios(
             advert.RetailerSiteRetailerId,
             advert.DerivativeId,
             advert.FirstRegisteredDate,
             mileageToUse,
             advert.StrategyPrice,
             retailPriceEquivalent,
             advert.VehicleHasOptionsSpecified,
             advert.VehicleAdvertPortalOptions,
             advert.ValuationMktAvRetail,
             advert.ValuationAdjRetail);

         var taskLocationChanges = vehicleAdvertsDataAccess.GetLocationOptimiserAdverts(DateTime.Now, advert.AdId, true, userId);

         // Awaiting them in parallel
         await Task.WhenAll(taskFutureValuations, taskCompetitors, taskComments, taskScenarios, taskLocationChanges);

         List<FutureValuationPoint> futureValuations = taskFutureValuations.Result;
         VehicleListing competitors = taskCompetitors.Result;

         //var tst = competitors.results.FirstOrDefault(x => x.vehicle.registration == "NL23MZG");

         List<LocationOptimiserAdvert> locationChanges = taskLocationChanges.Result;
         List<VehicleAdComment> comments = taskComments.Result;
         List<DayToSellAndPriceIndicatorScenario> scenarios = taskScenarios.Result;





         // 3. Create competitor summary
         var advertSite = retailerSites.First(x => x.Id == advert.RetailerSiteId);

         AdvertDetailsForCompetitorList details = new AdvertDetailsForCompetitorList(advert);

         CompetitorSummary competitorSummary = ConstantMethodsService.BuildCompetitorSummary(
             details,
             retailPriceEquivalent,
             (int)advert.ValuationAdjRetail,
             decimal.Parse(advertSite.Site.GeoY),
             decimal.Parse(advertSite.Site.GeoX),
             competitors,
             radius,
             advertSite.RetailerId,
             advertSite.RetailerType);   //0.8

         //3.1 Tag any competitors as vehicles we sold
         if (dealerGroup == DealerGroupName.Enterprise)
         {
            List<string> competitorRegs = competitorSummary.CompetitorVehicles.Select(x => x.VehicleReg).ToList();
            List<string> regsWeWold = await leavingPricesDataAccess.ConfirmWhichRegsWeSold(competitorRegs);
            foreach (var item in competitorSummary.CompetitorVehicles)
            {
               if (regsWeWold.Contains(item.VehicleReg))
               {
                  item.IsVehicleWeSold = true;
               }
            }
         }

         // 4. Build chart
         AdvertHistoryAndFutureChart historyChart = BuildChart(advert, newHistory, futureValuations);

         var toReturn = new VehicleDetailModalItem()
         {
            AdvertDetail = advert,
            LeavingPrice = includingLeavingDetail ? leavingPricesItem : null,
            PriceBuildUp = priceBuildUp,
            SameModelAdverts = sameModelAdverts,
            Comments = comments,
            CompetitorSummary = competitorSummary,
            Chart = historyChart,
            PricingScenarios = scenarios,
            LocationChanges = locationChanges,
            PriceChangeToday = todayPriceChangeTask.Result
         };

         return toReturn;
      }



      public async Task<int> GetTotalAdvertsForAdvertiser(string advertiserName, int advertiserId)
      {
         int AdvertCount = 0;
         try
         {
            var url = $"https://www.autotrader.co.uk/dealers/{advertiserName}-{advertiserId}";

            httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            httpClient.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
            httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.5");
            httpClient.DefaultRequestHeaders.Add("Referer", "https://www.google.com/");

            var response = await httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
               string content = await response.Content.ReadAsStringAsync();

               string pattern = @"<button.*?data-testid=""contact-view-stock"".*?>View\s+(\d+)\s+cars</button>";
               Match match = Regex.Match(content, pattern);

               if (match.Success)
               {
                  int.TryParse(match.Groups[1].Value, out AdvertCount); // Extract the number (689 in this case)
               }
               else
               {
                  //
               }
            }
            else
            {
               //
            }

         }
         catch (Exception ex)
         {
            //
         }

         return AdvertCount;

      }

      //private async Task SetTotalAdvertsForAdvertisers(List<AutoTraderVehicleListing> autoTraderVehicleListings, List<AdvertiserAdvertCount> advertiserAdvertCounts)
      //{
      //   var totalAdvertApiCallTasks = new List<Task>();

      //   foreach (var advertiser in advertiserAdvertCounts)
      //   {

      //      totalAdvertApiCallTasks.Add(Task.Run(async () =>
      //      {
      //         try
      //         {
      //            var url = $"https://www.autotrader.co.uk/dealers/{advertiser.Name}-{advertiser.Id}";
      //               var response = await httpClient.GetAsync(url);
      //               if (response.IsSuccessStatusCode)
      //               {
      //                  string content = await response.Content.ReadAsStringAsync();

      //                  string pattern = @"<button.*?data-testid=""contact-view-stock"".*?>View\s+(\d+)\s+cars</button>";
      //                  Match match = Regex.Match(content, pattern);

      //                  if (match.Success)
      //                  {

      //                     int.TryParse(match.Groups[1].Value, out int AdvertCount); // Extract the number (689 in this case)
      //                     advertiser.AdvertCount = AdvertCount;
      //                  }
      //               }
      //               else
      //               {
      //                  //
      //               }

      //         }
      //         catch (Exception ex)
      //         {
      //            //
      //         }
      //      }));

      //   }

      //   await Task.WhenAll(totalAdvertApiCallTasks);

      //   foreach (var competitor in autoTraderVehicleListings)
      //   {
      //      competitor.advertiser.totalAdvertCount = advertiserAdvertCounts.FirstOrDefault(a => a.Id == competitor.advertiser.advertiserId)?.AdvertCount;
      //   }
      //}



      private Task<List<FutureValuationPoint>> CreateTaskForFutureValuations(int mileage, string derivativeId, decimal daysToSell, DateTime firstRegisteredDate, int retailerId, decimal averageValuation, decimal adjustedValuation, TokenResponse tokenResponse)
      {
         List<DateTime> futureValnPoints = new List<DateTime>();//

         int finalPoint = Math.Max(45, (int)daysToSell);

         futureValnPoints.Add(DateTime.Now.AddDays(5));
         futureValnPoints.Add(DateTime.Now.AddDays(10));
         futureValnPoints.Add(DateTime.Now.AddDays(20));
         futureValnPoints.Add(DateTime.Now.AddDays(finalPoint));

         GetFutureValuationsParams parms = new GetFutureValuationsParams()
         {
            futureValuationPoints = futureValnPoints,
            derivativeId = derivativeId,
            firstRegistrationDate = firstRegisteredDate,
            odometerReading = mileage,
            retailerId = retailerId,
            currentValuation = averageValuation,
            currentAdjustedValuation = adjustedValuation
         };
         var taskFutureValuations = atFutureValsClient.GetFutureValuation(parms, tokenResponse, null);
         return taskFutureValuations;
      }





      private static List<AdvertPriceAndViewsHistoryItem> FillInBlanksInHistory(VehicleAdvertDetail advert, IEnumerable<AdvertPriceAndViewsHistoryItem> priceHistory)
      {
         // Assuming priceHistory is a collection and SnapshotDate is of type DateTime
         DateTime minDateInPriceHistory = priceHistory.Min(x => x.SnapshotDate);
         DateTime earliestDate = advert.DateOnForecourt < minDateInPriceHistory ? advert.DateOnForecourt : minDateInPriceHistory;

         //DateTime historyFrom = advert.DateOnForecourt;
         if (earliestDate == DateTime.MinValue)
         {
            earliestDate = priceHistory.OrderBy(p => p.SnapshotDate).First().SnapshotDate;
         }

         int daysHistoryNeeded = ((DateTime.Now - earliestDate).Days);// + 1; //10 

         var firstCurrentAdvertisedPrice = priceHistory.Where(x => x.AdvertisedPrice > 0).FirstOrDefault();
         int currentAdvertisedPrice = firstCurrentAdvertisedPrice?.AdvertisedPrice ?? 0;
         decimal currentValuationMktAvRetail = priceHistory.First().ValuationMktAvRetail;
         decimal currentValuationAdjRetail = priceHistory.First().ValuationAdjRetail;

         int? performanceAdvertViewsYest = 0;
         int? performanceSearchViewsYest = 0;
         decimal? performanceRatingScore = 0;
         string performanceRatingRating = "None";
         int? leadCountRating = 0;
         int? searchViewRating = 0;
         int? advertViewRating = 0;

         decimal? currentStrategyPrice = 0;
         if (priceHistory.Where(x => x.StrategyPrice != 0).Count() > 0)
         {
            currentStrategyPrice = priceHistory.Where(x => x.StrategyPrice != 0).First().StrategyPrice;
         }


         DateTime currentDate = DateTime.Now.AddDays(daysHistoryNeeded * -1);

         List<AdvertPriceAndViewsHistoryItem> newHistory = new List<AdvertPriceAndViewsHistoryItem>();
         while (currentDate.Date <= DateTime.Now.Date)
         {
            var currentHistoryItem = priceHistory.FirstOrDefault(x => x.SnapshotDate.Date == currentDate.Date);
            if (currentHistoryItem == null)
            {
               //don't have this one, have to fill it in best we can
               newHistory.Add(new AdvertPriceAndViewsHistoryItem()
               {
                  SnapshotDate = currentDate.Date,
                  AdvertisedPrice = currentAdvertisedPrice,
                  ValuationMktAvRetail = currentValuationMktAvRetail,
                  ValuationAdjRetail = currentValuationAdjRetail,
                  StrategyPrice = currentStrategyPrice,
                  PerformanceAdvertViewsYest = performanceAdvertViewsYest,
                  PerformanceSearchViewsYest = performanceSearchViewsYest,
                  PerformanceRatingScore = performanceRatingScore,
                  PerformanceRatingRating = performanceRatingRating,
                  LeadCountRating = leadCountRating,
                  SearchViewRating = searchViewRating,
                  AdvertViewRating = advertViewRating
               });
            }
            else
            {
               //newHistory.Add(currentHistoryItem);
               if (currentHistoryItem.AdvertisedPrice > 0)
               {
                  currentAdvertisedPrice = currentHistoryItem.AdvertisedPrice;
               }


               if (currentHistoryItem.ValuationMktAvRetail != 0)
               {
                  currentValuationMktAvRetail = currentHistoryItem.ValuationMktAvRetail;
               }
               if (currentHistoryItem.ValuationAdjRetail != 0)
               {
                  currentValuationAdjRetail = currentHistoryItem.ValuationAdjRetail;
               }
               if (currentHistoryItem.StrategyPrice != 0)
               {
                  currentStrategyPrice = currentHistoryItem.StrategyPrice;
               }

               performanceAdvertViewsYest = currentHistoryItem.PerformanceAdvertViewsYest ?? 0;
               performanceSearchViewsYest = currentHistoryItem.PerformanceSearchViewsYest ?? 0;
               performanceRatingScore = currentHistoryItem.PerformanceRatingScore ?? 0;
               performanceRatingRating = currentHistoryItem.PerformanceRatingRating;
               leadCountRating = currentHistoryItem.LeadCountRating ?? 0;
               searchViewRating = currentHistoryItem.SearchViewRating ?? 0;
               advertViewRating = currentHistoryItem.AdvertViewRating ?? 0;

               //currentValuationMktAvRetail = currentValuationMktAvRetail;
               //currentStrategyPrice = currentHistoryItem.StrategyPrice;

               newHistory.Add(new AdvertPriceAndViewsHistoryItem()
               {
                  SnapshotDate = currentDate.Date,
                  AdvertisedPrice = currentAdvertisedPrice,
                  ValuationMktAvRetail = currentValuationMktAvRetail,
                  ValuationAdjRetail = currentValuationAdjRetail,
                  StrategyPrice = currentStrategyPrice,
                  PerformanceAdvertViewsYest = performanceAdvertViewsYest,
                  PerformanceSearchViewsYest = performanceSearchViewsYest,
                  PerformanceRatingScore = performanceRatingScore,
                  PerformanceRatingRating = performanceRatingRating,
                  LeadCountRating = leadCountRating,
                  SearchViewRating = searchViewRating,
                  AdvertViewRating = advertViewRating
               });
            }

            currentDate = currentDate.AddDays(1);
         }

         return newHistory;
      }







      public async Task<List<DayToSellAndPriceIndicatorScenario>> GetEstimatedDayToSellAndPriceIndicatorForScenarios(
          int advertiserId,
          string derivativeId,
          DateTime? firstRegisteredDate,
          int odometerReading,
          decimal currentStrategyPrice,
          int currentAdvertisedPrice,
          bool vehicleHasOptionsSpecified,
          List<string> vehicleAdvertPortalOptions
      , decimal averageValuation, decimal adjustedValuation)
      {
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];


         List<DayToSellAndPriceIndicatorParam> scenarios = new List<DayToSellAndPriceIndicatorParam>()
            {
                    new DayToSellAndPriceIndicatorParam(){Label=$"{(currentAdvertisedPrice-500).ToString("C0", CultureInfo.GetCultureInfo("en-GB"))} (advertised -£500)", Value = currentAdvertisedPrice-500},
                    new DayToSellAndPriceIndicatorParam(){Label=$"{(currentAdvertisedPrice-200).ToString("C0", CultureInfo.GetCultureInfo("en-GB"))} (advertised -£200)", Value = currentAdvertisedPrice-200},
                    new DayToSellAndPriceIndicatorParam(){Label=$"{(currentAdvertisedPrice-100).ToString("C0", CultureInfo.GetCultureInfo("en-GB"))} (advertised -£100)", Value = currentAdvertisedPrice-100},
            };


         scenarios.Add(new DayToSellAndPriceIndicatorParam() { Label = $"{currentAdvertisedPrice.ToString("C0", CultureInfo.GetCultureInfo("en-GB"))}: Advertised Price", Value = (int)currentAdvertisedPrice });
         scenarios.Add(new DayToSellAndPriceIndicatorParam() { Label = $"{currentStrategyPrice.ToString("C0", CultureInfo.GetCultureInfo("en-GB"))}: Strategy Price", Value = (int)currentStrategyPrice });

         scenarios.Add(new DayToSellAndPriceIndicatorParam() { Label = $"{(currentAdvertisedPrice + 100).ToString("C0", CultureInfo.GetCultureInfo("en-GB"))} (advertised +£100)", Value = currentAdvertisedPrice + 100 });
         scenarios.Add(new DayToSellAndPriceIndicatorParam() { Label = $"{(currentAdvertisedPrice + 200).ToString("C0", CultureInfo.GetCultureInfo("en-GB"))} (advertised +£200)", Value = currentAdvertisedPrice + 200 });
         scenarios.Add(new DayToSellAndPriceIndicatorParam() { Label = $"{(currentAdvertisedPrice + 500).ToString("C0", CultureInfo.GetCultureInfo("en-GB"))} (advertised +£500)", Value = currentAdvertisedPrice + 500 });

         var stringTasks = new List<Task<string>>();
         string _bearerToken = (await atTokenClient.GetToken()).AccessToken;
         decimal adjustmentFactorForPriceIndicator = await vehicleValuationService.WorkoutAdjustmentFactor(derivativeId, firstRegisteredDate, odometerReading, vehicleAdvertPortalOptions, adjustedValuation);

         foreach (var scenario in scenarios)
         {
            var parms = new GetAdvertPriceAdjustedDaysToSellParams()
            {
               AutotraderBaseURL = baseURL,
               AdvertiserId = advertiserId,
               DerivativeId = derivativeId,
               FirstRegistrationDate = firstRegisteredDate,
               OdometerReadingMiles = odometerReading,
               Amount = scenario.Value,
               UseSpecificOptions = vehicleHasOptionsSpecified,
               SpecificOptionNames = vehicleAdvertPortalOptions,

               AverageValuation = averageValuation,
               AdjustedValuation = adjustedValuation,
            };





            stringTasks.Add(Task.Run(async () =>
            {
               try
               {
                  return await atValuationsClient.GetAdvertPriceIndicator(parms, _bearerToken, adjustmentFactorForPriceIndicator);
               }
               catch (Exception ex)
               {
                  // Handle or log the exception as needed
                  return $"Error: {ex.Message}";
               }
            }));

            stringTasks.Add(Task.Run(async () =>
            {
               try
               {
                  return (await atMetricsClient.GetAdvertPriceAdjustedDaysToSell(parms, _bearerToken, null)).ToString();
               }
               catch (Exception ex)
               {
                  // Handle or log the exception as needed
                  return $"Error: {ex.Message}";
               }
            }));
         }

         string[] apiResults = await Task.WhenAll(stringTasks);

         List<DayToSellAndPriceIndicatorScenario> results = new List<DayToSellAndPriceIndicatorScenario>();

         for (int i = 0; i < scenarios.Count; i++)
         {
            var priceIndex = i * 2;
            var daysToSellIndex = priceIndex + 1;

            results.Add(new DayToSellAndPriceIndicatorScenario()
            {
               Price = scenarios[i].Value,
               Label = PriceStrategyClassifierService.ProvidePriceIndicatorName(scenarios[i].Label),
               DaysToSell = GetDaysToSellResult(daysToSellIndex, apiResults),
               PriceIndicator = GetPriceIndicatorResult(priceIndex, apiResults)
            });
         }

         foreach (var r in results)
         {
            r.PriceIndicator = PriceStrategyClassifierService.ProvidePriceIndicatorName(r.PriceIndicator);
         }

         return results.OrderBy(r => r.Price).ToList();



      }



      private decimal GetDaysToSellResult(int resultIndex, string[] apiResults)
      {
         var res = apiResults[resultIndex];
         if (res != null && !res.StartsWith("Error"))
         {
            return Math.Ceiling(decimal.Parse(res));
         }
         else
         {
            return 0;
         }
      }

      private string GetPriceIndicatorResult(int resultIndex, string[] apiResults)
      {
         var res = apiResults[resultIndex];
         if (res != null && !res.StartsWith("Error"))
         {
            return res;
         }
         else
         {
            return null;
         }
      }

      private static AdvertHistoryAndFutureChart BuildChart(
          VehicleAdvertDetail advert,
          IEnumerable<AdvertPriceAndViewsHistoryItem> priceHistory,
          List<FutureValuationPoint> futureValuations)
      {
         //walk through price history building the chart data that we want
         AdvertHistoryAndFutureChart historyChart = new AdvertHistoryAndFutureChart();
         DateTime currentDate = DateTime.Today.AddDays((priceHistory.Count() - 1) * -1).Date;
         AdvertPriceAndViewsHistoryItem mostRecentItem = null;
         priceHistory = priceHistory.OrderBy(x => x.SnapshotDate).ToList();
         decimal mostRecentItemStrategyPrice = 0;
         decimal mostRecentValuationPrice = 0;

         foreach (var thisDayItem in priceHistory)
         {
            mostRecentItem = thisDayItem;

            if (mostRecentItem.StrategyPrice != 0 && mostRecentItem.StrategyPrice != null)
            {
               mostRecentItemStrategyPrice = (decimal)mostRecentItem.StrategyPrice;
            }
            mostRecentValuationPrice = mostRecentItem.ValuationAdjRetail;
            historyChart.Dates.Add(currentDate);

            if (mostRecentItem != null)
            {
               historyChart.AdvertViews.Add(mostRecentItem.PerformanceAdvertViewsYest);
               historyChart.SearchViews.Add(mostRecentItem.PerformanceSearchViewsYest);
               historyChart.AdvertisedPrices.Add(mostRecentItem.AdvertisedPrice);
               historyChart.PerformanceRatings.Add(mostRecentItem.PerformanceRatingScore);
               historyChart.Valuations.Add(mostRecentItem.ValuationAdjRetail);
               historyChart.StrategyPrices.Add(mostRecentItemStrategyPrice);
               historyChart.LeadsScores.Add(mostRecentItem.LeadCountRating);
               historyChart.SearchViewRatings.Add(mostRecentItem.SearchViewRating);
               historyChart.AdvertViewRatings.Add(mostRecentItem.AdvertViewRating);
            }
            else
            {
               historyChart.AdvertViews.Add(null);
               historyChart.SearchViews.Add(null);
               historyChart.AdvertisedPrices.Add(null);
               historyChart.PerformanceRatings.Add(null);
               historyChart.Valuations.Add(null);
               historyChart.StrategyPrices.Add(null);
               historyChart.RecentSales.Add(new List<RecentSaleDetailItem>());
               historyChart.LeadsScores.Add(null);
               historyChart.SearchViewRatings.Add(null);
               historyChart.AdvertViewRatings.Add(null);
            }

            currentDate = currentDate.AddDays(1);
         }


         PopulateFutureValuationItemsOnChart(mostRecentValuationPrice, futureValuations, historyChart, currentDate, advert);

         historyChart.DaysToSell = advert.DaysToSellAtCurrentSelling;
         historyChart.DaysListed = advert.DaysListed;

         return historyChart;
      }

      private static void PopulateFutureValuationItemsOnChart(
          decimal valuationPrice,
          List<FutureValuationPoint> futureValuations,
          AdvertHistoryAndFutureChart historyChart,
          DateTime currentDate,
          VehicleAdvertDetail advert)
      {
         decimal cumValuationPrice = valuationPrice;
         //now go forwards to future points
         int totalDaysForward = 0;
         int futurePointIndex = 0;
         //int totalDaysToNextPoint = 5;
         //decimal valueAtStartOfBanding = valuationPrice;
         int finalPoint = Math.Max(45, (int)advert.DaysToSellAtCurrentSelling);
         List<int> futurePoints = new List<int>() { 5, 10, 20, finalPoint };


         int lastFuturePointDaysElapsed = 0;
         decimal futurePointStartingValue = cumValuationPrice;
         int futurePointDaysElapsed = 0;
         decimal dailyIncrement = 0;

         //update the 
         futurePointDaysElapsed = futurePoints[futurePointIndex];
         dailyIncrement = (futureValuations[0].RetailValue - futurePointStartingValue) / (futurePointDaysElapsed - lastFuturePointDaysElapsed);

         while (totalDaysForward < finalPoint)
         {
            try
            {

               if (futurePoints.Contains(totalDaysForward))
               {
                  //we are at a next future time point
                  cumValuationPrice = futureValuations[futurePointIndex].RetailValue;
                  historyChart.Valuations.Add(cumValuationPrice);

                  lastFuturePointDaysElapsed = futurePointDaysElapsed;
                  futurePointIndex++;
                  futurePointDaysElapsed = futurePoints[futurePointIndex];
                  futurePointStartingValue = cumValuationPrice;
                  dailyIncrement = (decimal)(futureValuations[futurePointIndex].RetailValue - futureValuations[futurePointIndex - 1].RetailValue) / (decimal)(futurePointDaysElapsed - lastFuturePointDaysElapsed);

                  totalDaysForward++;

               }
               else
               {

                  cumValuationPrice += dailyIncrement;
                  historyChart.Valuations.Add(cumValuationPrice);
                  totalDaysForward++;
               }


            }
            catch (Exception ex)
            {
               {
                  { }
               }
            }
         }
         historyChart.Valuations.Add(futureValuations[3].RetailValue);


         for (int i = 0; i <= finalPoint; i++)
         {
            //add a blank into here to keep them a constant length
            AddBlankItemToOtherSeries(historyChart, currentDate, i);
         }
      }

      private static void AddBlankItemToOtherSeries(AdvertHistoryAndFutureChart historyChart, DateTime currentDate, int dayIndex)
      {

         historyChart.Dates.Add(currentDate.AddDays(dayIndex));
         historyChart.AdvertViews.Add(null);
         historyChart.SearchViews.Add(null);
         historyChart.AdvertisedPrices.Add(null);
         historyChart.PerformanceRatings.Add(null);
         //historyChart.Valuations.Add(null);
         historyChart.StrategyPrices.Add(null);
         historyChart.RecentSales.Add(new List<RecentSaleDetailItem>());
      }
   }
}