﻿using Dapper;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.Model.ViewModels.Vindis;
using System.Data.SqlClient;
using System.Linq;
using CPHI.Spark.Model.ViewModels;
using System.Data;

namespace CPHI.Spark.DataAccess
{
   public class CommissionDataAccess
   {

      private string _connectionString;

      public CommissionDataAccess(string connString)
      {
         _connectionString = connString;
      }




      //UnifiedDB - TODO: The table used in the SP exists on in RRG DB. 
      public async Task<IEnumerable<Model.ViewModels.RRG.CommissionItem>> GetCommissionDataItemsWasForMonth(DateTime forMonth, DateTime lastPaidMonth)
      {

         using (var conn = new SqlConnection(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("AccountingMonth", forMonth);
            paramList.Add("AfterPaidOnMonth", lastPaidMonth);

            var result = await conn.QueryAsync<Model.ViewModels.RRG.CommissionItem>("dbo.GET_CommissionItemsWas", paramList, commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);

            foreach (var item in result)
            {
               item.PopulateProfitPot();
            }

            return result;
         }
      }

      //UnifiedDB - SP updated. Only Used in Vindis
      public async Task<IEnumerable<CommissionAdjustmentItem>> GetCommissionAdjustments(DateTime monthStart, int? salesmanId)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<CommissionAdjustmentItem>("dbo.GET_CommissionAdjustmentItems", new DynamicParameters(new { ChosenMonth = monthStart, SalesmanId = salesmanId }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public DateTime GetMostRecentPaidMonth(Model.DealerGroupName dealerGroup)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return conn.QueryFirst<DateTime>("dbo.GET_MostRecentCommissionPaidMonth", new DynamicParameters(new { dealerGroupId = (int)dealerGroup}), commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<Model.ViewModels.RRG.CommissionDataItem>> GetCommissionItems_New_FromJuly24Onwards(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.RRG.CommissionDataItem>("dbo.GET_CommissionItemsNew_July24Onwards", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }


      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<Model.ViewModels.RRG.CommissionDataItem>> GetCommissionItems_New_OrderedPostOct(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.RRG.CommissionDataItem>("dbo.GET_CommissionItemsNewOrderedPostOct", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<Model.ViewModels.RRG.CommissionDataItem>> GETCommissionItems_New_OrderedPreOctDeliveredPostOct(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.RRG.CommissionDataItem>("dbo.GET_CommissionItemsNewOrderedPreOctDeliveredPostOct", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<CommissionQuarterlyCatchupDataItem>> GETCommissionItemsQuarterlyCatchupData(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            if (monthStart >= new DateTime(2025, 3, 1))
            {
               return await conn.QueryAsync<Model.ViewModels.RRG.CommissionQuarterlyCatchupDataItem>("dbo.GET_CommissionItemsQuarterlyCatchupDataMar25On", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
            }
            else if (monthStart >= new DateTime(2024, 3, 1))
            {
               return await conn.QueryAsync<Model.ViewModels.RRG.CommissionQuarterlyCatchupDataItem>("dbo.GET_CommissionItemsQuarterlyCatchupDataMar24On", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
            }
            else
            {
               return await conn.QueryAsync<Model.ViewModels.RRG.CommissionQuarterlyCatchupDataItem>("dbo.GET_CommissionItemsQuarterlyCatchupData", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);

            }
         }
      }


      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<Model.ViewModels.RRG.CommissionDataItem>> GETCommissionItems_DeliveredMar23On_OrderedAnytime(DateTime monthStart, bool IsNew)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.RRG.CommissionDataItem>("dbo.GET_CommissionItems_DeliveredMar23On_OrderedAnytime", new DynamicParameters(new { ChosenMonth = monthStart, IsNew = IsNew ? 1 : 0 }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<Model.ViewModels.RRG.CommissionDataItem>> GETCommissionItems_DeliveredJuly24On_OrderedAnytime(DateTime monthStart, bool IsNew)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.RRG.CommissionDataItem>("dbo.GET_CommissionItems_DeliveredJul24On_OrderedAnytime", new DynamicParameters(new { ChosenMonth = monthStart, IsNew = IsNew ? 1 : 0 }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<Model.ViewModels.RRG.CommissionDataItem>> GETCommissionItems_DeliveredOct21On_OrderedAnytime(DateTime monthStart, bool IsNew)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.RRG.CommissionDataItem>("dbo.GET_CommissionItems_DeliveredOct21On_OrderedAnytime", new DynamicParameters(new { ChosenMonth = monthStart, IsNew = IsNew ? 1 : 0 }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<Model.ViewModels.RRG.CommissionDataItem>> GETCommissionItems_PreOctober(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.RRG.CommissionDataItem>("dbo.GET_CommissionItemsPreOctober", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<IEnumerable<LeaverDealItem>> GETLeaverDealsForMonth(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<LeaverDealItem>("dbo.GET_LeaverDealsForMonth", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in Vindis
      public async Task<IEnumerable<CommissionMeasuresWithSite>> GetTargetsBySiteVindis(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<CommissionMeasuresWithSite>("dbo.GET_TargetsBySiteVindis", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in Vindis
      public async Task<IEnumerable<ProfitPotMonthPhasingBySite>> GetProfitPotMonthPhasings(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<ProfitPotMonthPhasingBySite>("dbo.GET_ProfitPotMonthPhasingsBySite", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in Vindis
      public async Task<IEnumerable<Model.ViewModels.Vindis.CommissionDataItem>> GetCommissionItemsVindisMar2022On(DateTime monthStart, bool ignoreAuditPass, int? salesmanId)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.Vindis.CommissionDataItem>("dbo.GET_CommissionItemsVindisMar2022On", new DynamicParameters(new { ChosenMonth = monthStart, IgnoreAuditPass = ignoreAuditPass, SalesmanId = salesmanId }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in Vindis
      public async Task<IEnumerable<Model.ViewModels.Vindis.CommissionDataItem>> GetCommissionItemsVindisJan2024On(DateTime monthStart, bool ignoreAuditPass, int? salesmanId)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            return await conn.QueryAsync<Model.ViewModels.Vindis.CommissionDataItem>("dbo.GET_CommissionItemsVindisJan2024On", new DynamicParameters(new { ChosenMonth = monthStart, IgnoreAuditPass = ignoreAuditPass, SalesmanId = salesmanId }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<Dictionary<string, CommissionOrderRatePaid>> GetOrderPaidRates(DateTime yearMonth)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            var spResponse = await conn.QueryAsync<CommissionOrderRatePaid>("dbo.GET_CommissionOrderRatesPaid", new DynamicParameters(new { ChosenMonth = yearMonth }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
            return spResponse.ToDictionary(x => x.LookupKey);
         }
      }


      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<Dictionary<int, bool>> GETUsedTargetAchievementBySalesman(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            var spResponse = await conn.QueryAsync<UsedTargetHitResult>(
                "dbo.GET_UsedTargetHitForExec",
                new DynamicParameters(new { ChosenMonth = monthStart }),
                commandTimeout: 70,
                commandType: System.Data.CommandType.StoredProcedure
            );

            // Convert response to Dictionary<int, bool>
            var result = spResponse.ToDictionary(
                row => (int)row.SalesmanId,
                row => (bool)row.ExceededTarget
            );

            return result;
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<Dictionary<int, ElectricSalesQToDate>> GETElectricSalesQToDatePre2025(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            var spResponse = await conn.QueryAsync<ElectricSalesQToDate>("dbo.GET_ElectricSalesQToDate", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
            return spResponse.ToDictionary(x => x.SalesmanId);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<Dictionary<int, ElectricSalesQToDate>> GETElectricSalesQToDateRenault(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            var spResponse = await conn.QueryAsync<ElectricSalesQToDate>("dbo.GET_ElectricSalesQToDate2025OnwardsRenault", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
            return spResponse.ToDictionary(x => x.SalesmanId);
         }
      }

      //UnifiedDB - SP updated. Only Used in RRG
      public async Task<Dictionary<int, ElectricSalesQToDate>> GETElectricSalesQToDateDacia(DateTime monthStart)
      {
         using (var conn = new SqlConnection(_connectionString))
         {
            var spResponse = await conn.QueryAsync<ElectricSalesQToDate>("dbo.GET_ElectricSalesQToDate2025OnwardsDacia", new DynamicParameters(new { ChosenMonth = monthStart }), commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);
            return spResponse.ToDictionary(x => x.SalesmanId);
         }
      }
   }
}
