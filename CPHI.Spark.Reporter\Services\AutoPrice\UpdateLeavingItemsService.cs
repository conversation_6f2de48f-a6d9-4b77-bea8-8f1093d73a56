﻿using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Repository;
using log4net;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class UpdateLeavingItemsService
   {
      public async Task PopulateLeavingItems(ILog logger, List<DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0)
         { return; }
         logger.Info("----------------------------------------------------------");
         logger.Info("LeavingItems");
         try
         {

            foreach (DealerGroupName dealerGroup in dealerGroups)
            {
               logger.Info($"Starting LeavingItems: {dealerGroup}");
               var logMessage = LoggingService.InitLogMessage();

               try
               {

                  RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                  List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
                  bool includeUnPublished = retailers.First().LeavingVehicles_IncludeUnPublished;

                  if (retailers.Count > 0)
                  {
                     try
                     {
                        var leavingPricesDataAccess = new LeavingPricesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                        DateTime leavingOnOrAfter = DateTime.Now.Date.AddDays(-7);
                        DateTime leavingBefore = DateTime.Now.Date;


                        const int maxRetries = 3;
                        int attempt = 0;
                        Stopwatch sw = new Stopwatch();
                        sw.Start();
                        while (attempt < maxRetries)
                        {
                           attempt++;
                           try
                           {
                              await leavingPricesDataAccess.UpdateLeavingPriceItems(leavingOnOrAfter, leavingBefore, dealerGroup, includeUnPublished);
                              await leavingPricesDataAccess.UpdateLeavingPriceItemDaysOnStrategy(dealerGroup);
                              await leavingPricesDataAccess.UpdateLeavingPriceItemDaysOptedOut(dealerGroup);
                              // If the call succeeded, break out of the loop
                              logger.Info($"LeavingItems succeeded for {dealerGroup}, in total it took {sw.Elapsed}");
                              break;
                           }
                           catch (Exception ex)
                           {
                              logger.Error($"LeavingItems attempt #{attempt} failed for {dealerGroup}: {ex.Message}.  Total elapsed is {sw.Elapsed}", ex);


                              // If we've reached the final attempt, rethrow or handle it
                              if (attempt == maxRetries)
                              {
                                 // Log and send the email
                                 //await EmailerService.SendMailOnError(dealerGroup, "LeavingItems - Error", ex);
                                 //LoggingService.AddErrorLogMessage(logMessage, ex.Message);

                                 // Optionally, rethrow or return if you want the error to bubble up
                                 throw;

                                 // If you don't rethrow, code continues (but the SP call failed)
                              }
                              else
                              {
                                 // Wait a short delay before retrying
                                 logger.Info("Sleeping for 1 minute before next attempt");
                                 await Task.Delay(60000);
                              }
                           }
                        }


                     }

                     catch (Exception ex)
                     {
                        logger.Error(ex);
                        await EmailerService.SendMailOnError(dealerGroup, "LeavingItems - Error", ex);
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                  }
               }
               catch (Exception ex)
               {
                  await EmailerService.LogException(ex, logger, "LeavingItems");
                  LoggingService.AddErrorLogMessage(logMessage, ex.Message);
               }
               finally
               {
                  await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "LeavingItems");
               }

            }


            logger.Info("Completed LeavingItems");
            logger.Info("----------------------------------------------------------");
         }
         catch (Exception ex)
         {
            { }
            logger.Error(ex);
            throw new Exception(ex.Message, ex);
         }

      }


   }
}
