﻿using CPHI.Spark.Model;
using System;
using System.Collections.Generic;

namespace CPHI.Spark.BusinessLogic.Vindis.Comparers
{
    public class DealDeepComp : IEqualityComparer<Deal>
    {

        public bool Equals(Deal x, Deal y)

        {

            List<Diff> diffs = GetDiffs(x, y, "Foo", "Bar");

            return diffs.Count == 0;
        }

        public static List<Diff> GetDiffs(Deal x, Deal y, string sourceData, string modelIdentifier)
        {
            List<Diff> diffs = new List<Diff>();  //initiate list

            if (x.Description != y.Description) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Description", x.Description, y.Description)); }
            if (x.Reg != y.Reg) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Reg", x.Reg, y.Reg)); }
            if (x.StockDate != y.StockDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "StockDate", x.StockDate, y.StockDate)); }
            if (x.RegisteredDate != y.RegisteredDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "RegisteredDate", x.RegisteredDate, y.RegisteredDate)); }
            if (x.Customer != y.Customer) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Customer", x.Customer, y.Customer)); }
            if (x.OrderDate != y.OrderDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "OrderDate", x.OrderDate, y.OrderDate)); }
            if (x.ActualDeliveryDate != y.ActualDeliveryDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "ActualDeliveryDate", x.ActualDeliveryDate, y.ActualDeliveryDate)); }
            if (x.IsDelivered != y.IsDelivered) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "IsDelivered", x.IsDelivered, y.IsDelivered)); }
            if (x.HasServicePlan != y.HasServicePlan) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasServicePlan", x.HasServicePlan, y.HasServicePlan)); }
            if (x.IsFinanced != y.IsFinanced) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "IsFinanced", x.IsFinanced, y.IsFinanced)); }
            if (x.HasCosmeticInsurance != y.HasCosmeticInsurance) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasCosmeticInsurance", x.HasCosmeticInsurance, y.HasCosmeticInsurance)); }
            if (x.HasGapInsurance != y.HasGapInsurance) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasGapInsurance", x.HasGapInsurance, y.HasGapInsurance)); }
            //if (x.TotalProductCount != y.TotalProductCount) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TotalProductCount", x.TotalProductCount, y.TotalProductCount)); }
            if (x.Discount != y.Discount) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Discount", x.Discount, y.Discount)); }
            if (x.ServicePlanSale != y.ServicePlanSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "ServicePlanSale", x.ServicePlanSale, y.ServicePlanSale)); }
            if (x.ServicePlanCost != y.ServicePlanCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "ServicePlanCost", x.ServicePlanCost, y.ServicePlanCost)); }
            if (x.FuelSale != y.FuelSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "FuelSale", x.FuelSale, y.FuelSale)); }
            if (x.FuelCost != y.FuelCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "FuelCost", x.FuelCost, y.FuelCost)); }
            if (x.OemDeliverySale != y.OemDeliverySale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "OemDeliverySale", x.OemDeliverySale, y.OemDeliverySale)); }
            if (x.OemDeliveryCost != y.OemDeliveryCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "OemDeliveryCost", x.OemDeliveryCost, y.OemDeliveryCost)); }
            if (x.WarrantySale != y.WarrantySale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "WarrantySale", x.WarrantySale, y.WarrantySale)); }
            if (x.WarrantyCost != y.WarrantyCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "WarrantyCost", x.WarrantyCost, y.WarrantyCost)); }
            if (x.StandardWarrantyCost != y.StandardWarrantyCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "StandardWarrantyCost", x.StandardWarrantyCost, y.StandardWarrantyCost)); }
            if (x.AccessoriesSale != y.AccessoriesSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "AccessoriesSale", x.AccessoriesSale, y.AccessoriesSale)); }
            if (x.AccessoriesCost != y.AccessoriesCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "AccessoriesCost", x.AccessoriesCost, y.AccessoriesCost)); }
            if (x.NewBonus1 != y.NewBonus1) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "NewBonus1", x.NewBonus1, y.NewBonus1)); }
            if (x.NewBonus2 != y.NewBonus2) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "NewBonus2", x.NewBonus2, y.NewBonus2)); }
            if (x.PartExOverAllowance1 != y.PartExOverAllowance1) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "PartExOverAllowance1", x.PartExOverAllowance1, y.PartExOverAllowance1)); }
            if (x.FinanceCommission != y.FinanceCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "FinanceCommission", x.FinanceCommission, y.FinanceCommission)); }
            if (x.FinanceSubsidy != y.FinanceSubsidy) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "FinanceSubsidy", x.FinanceSubsidy, y.FinanceSubsidy)); }
            if (x.CosmeticInsuranceSale != y.CosmeticInsuranceSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "CosmeticInsuranceSale", x.CosmeticInsuranceSale, y.CosmeticInsuranceSale)); }
            if (x.CosmeticInsuranceCost != y.CosmeticInsuranceCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "CosmeticInsuranceCost", x.CosmeticInsuranceCost, y.CosmeticInsuranceCost)); }
            if (x.GapInsuranceSale != y.GapInsuranceSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "GapInsuranceSale", x.GapInsuranceSale, y.GapInsuranceSale)); }
            if (x.GapInsuranceCost != y.GapInsuranceCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "GapInsuranceCost", x.GapInsuranceCost, y.GapInsuranceCost)); }
            if (x.GapInsuranceCommission != y.GapInsuranceCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "GapInsuranceCommission", x.GapInsuranceCommission, y.GapInsuranceCommission)); }
            //if (x.FAndIProfit != y.FAndIProfit) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "FAndIProfit", x.FAndIProfit, y.FAndIProfit)); }
            if (x.Site_Id != y.Site_Id) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Site_Id", x.Site_Id, y.Site_Id)); }
            if (x.Salesman_Id != y.Salesman_Id) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Salesman_Id", x.Salesman_Id, y.Salesman_Id)); }
            if (x.StockNumber != y.StockNumber) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "StockNumber", x.StockNumber, y.StockNumber)); }
            if (x.VehicleAge != y.VehicleAge) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "VehicleAge", x.VehicleAge, y.VehicleAge)); }
            if (x.InvoiceDate != y.InvoiceDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "InvoiceDate", x.InvoiceDate, y.InvoiceDate)); }
            if (x.OrigOrderDate != y.OrigOrderDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "OrigOrderDate", x.OrigOrderDate, y.OrigOrderDate)); }
            if (x.OrigDeliveryDate != y.OrigDeliveryDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "OrigDeliveryDate", x.OrigDeliveryDate, y.OrigDeliveryDate)); }
            if (x.IsLateCost != y.IsLateCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "IsLateCost", x.IsLateCost, y.IsLateCost)); }
            if (x.BrokerCost != y.BrokerCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "BrokerCost", x.BrokerCost, y.BrokerCost)); }
            if (x.IntroCommission != y.IntroCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "IntroCommission", x.IntroCommission, y.IntroCommission)); }
            if (x.SelectCommission != y.SelectCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "SelectCommission", x.SelectCommission, y.SelectCommission)); }
            if (x.StandardsCommission != y.StandardsCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "StandardsCommission", x.StandardsCommission, y.StandardsCommission)); }
            if (x.RCIFinanceCommission != y.RCIFinanceCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "RCIFinanceCommission", x.RCIFinanceCommission, y.RCIFinanceCommission)); }
            if (x.VehicleType_Id != y.VehicleType_Id) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "VehicleType_Id", x.VehicleType_Id, y.VehicleType_Id)); }
            if (x.ProPlusCommission != y.ProPlusCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "ProPlusCommission", x.ProPlusCommission, y.ProPlusCommission)); }
            if (x.Model != y.Model) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Model", x.Model, y.Model)); }
            if (x.Variant != y.Variant) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Variant", x.Variant, y.Variant)); }
            if (x.VariantTxt != y.VariantTxt) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "VariantTxt", x.VariantTxt, y.VariantTxt)); }
            if (x.ModelYear != y.ModelYear) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "ModelYear", x.ModelYear, y.ModelYear)); }
            if (x.IsInvoiced != y.IsInvoiced) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "IsInvoiced", x.IsInvoiced, y.IsInvoiced)); }
            if (x.FinanceCo != y.FinanceCo) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "FinanceCo", x.FinanceCo, y.FinanceCo)); }
            if (x.Sale != y.Sale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Sale", x.Sale, y.Sale)); }
            if (x.CoS != y.CoS) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "CoS", x.CoS, y.CoS)); }
            if (x.MechPrep != y.MechPrep) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "MechPrep", x.MechPrep, y.MechPrep)); }
            if (x.BodyPrep != y.BodyPrep) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "BodyPrep", x.BodyPrep, y.BodyPrep)); }
            if (x.Error != y.Error) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Error", x.Error, y.Error)); }
            if (x.Franchise_Id != y.Franchise_Id) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Franchise_Id", x.Franchise_Id, y.Franchise_Id)); }
            if (x.OrderType_Id != y.OrderType_Id) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "OrderType_Id", x.OrderType_Id, y.OrderType_Id)); }
            if (x.VehicleClass_Id != y.VehicleClass_Id) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "VehicleClass_Id", x.VehicleClass_Id, y.VehicleClass_Id)); }
            //if (x.TotalVehicleProfit != y.TotalVehicleProfit) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TotalVehicleProfit", x.TotalVehicleProfit, y.TotalVehicleProfit)); }
            if (x.TotalNLProfit != y.TotalNLProfit) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TotalNLProfit", x.TotalNLProfit, y.TotalNLProfit)); }
            if (x.PDICost != y.PDICost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "PDICost", x.PDICost, y.PDICost)); }
            if (x.CosmeticInsuranceCommission != y.CosmeticInsuranceCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "CosmeticInsuranceCommission", x.CosmeticInsuranceCommission, y.CosmeticInsuranceCommission)); }
            if (x.PaintProtectionSale != y.PaintProtectionSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "PaintProtectionSale", x.PaintProtectionSale, y.PaintProtectionSale)); }
            if (x.PaintProtectionCost != y.PaintProtectionCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "PaintProtectionCost", x.PaintProtectionCost, y.PaintProtectionCost)); }
            if (x.PaintProtectionAccessorySale != y.PaintProtectionAccessorySale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "PaintProtectionAccessorySale", x.PaintProtectionAccessorySale, y.PaintProtectionAccessorySale)); }
            if (x.PaintProtectionAccessoryCost != y.PaintProtectionAccessoryCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "PaintProtectionAccessoryCost", x.PaintProtectionAccessoryCost, y.PaintProtectionAccessoryCost)); }
            if (x.HasPaintProtection != y.HasPaintProtection) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasPaintProtection", x.HasPaintProtection, y.HasPaintProtection)); }
            if (x.HasPaintProtectionAccessory != y.HasPaintProtectionAccessory) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasPaintProtectionAccessory", x.HasPaintProtectionAccessory, y.HasPaintProtectionAccessory)); }
            if (x.HasWarranty != y.HasWarranty) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasWarranty", x.HasWarranty, y.HasWarranty)); }
            if (x.HasShortWarranty != y.HasShortWarranty) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasShortWarranty", x.HasShortWarranty, y.HasShortWarranty)); }
            if (x.Units != y.Units) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Units", x.Units, y.Units)); }
            if (x.HandoverDate != y.HandoverDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HandoverDate", x.HandoverDate, y.HandoverDate)); }
            if (x.EnquiryNumber != y.EnquiryNumber) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "EnquiryNumber", x.EnquiryNumber, y.EnquiryNumber)); }
            if (x.AccountingDate != y.AccountingDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "AccountingDate", x.AccountingDate, y.AccountingDate)); }
            if (x.VehicleSource != y.VehicleSource) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "VehicleSource", x.VehicleSource, y.VehicleSource)); }


            if (x.HasTyreInsurance != y.HasTyreInsurance) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasTyreInsurance", x.HasTyreInsurance, y.HasTyreInsurance)); }
            if (x.TyreInsuranceSale != y.TyreInsuranceSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreInsuranceSale", x.TyreInsuranceSale, y.TyreInsuranceSale)); }
            if (x.TyreInsuranceCost != y.TyreInsuranceCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreInsuranceCost", x.TyreInsuranceCost, y.TyreInsuranceCost)); }
            if (x.HasAlloyInsurance != y.HasAlloyInsurance) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasAlloyInsurance", x.HasAlloyInsurance, y.HasAlloyInsurance)); }
            if (x.AlloyInsuranceSale != y.AlloyInsuranceSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "AlloyInsuranceSale", x.AlloyInsuranceSale, y.AlloyInsuranceSale)); }
            if (x.AlloyInsuranceCost != y.AlloyInsuranceCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "AlloyInsuranceCost", x.AlloyInsuranceCost, y.AlloyInsuranceCost)); }
            if (x.HasTyreAndAlloyInsurance != y.HasTyreAndAlloyInsurance) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasTyreAndAlloyInsurance", x.HasTyreAndAlloyInsurance, y.HasTyreAndAlloyInsurance)); }
            if (x.TyreAndAlloyInsuranceSale != y.TyreAndAlloyInsuranceSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreAndAlloyInsuranceSale", x.TyreAndAlloyInsuranceSale, y.TyreAndAlloyInsuranceSale)); }
            if (x.TyreAndAlloyInsuranceCost != y.TyreAndAlloyInsuranceCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreAndAlloyInsuranceCost", x.TyreAndAlloyInsuranceCost, y.TyreAndAlloyInsuranceCost)); }
            if (x.HasWheelGuard != y.HasWheelGuard) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasWheelGuard", x.HasWheelGuard, y.HasWheelGuard)); }
            if (x.WheelGuardSale != y.WheelGuardSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "WheelGuardSale", x.WheelGuardSale, y.WheelGuardSale)); }
            if (x.WheelGuardCost != y.WheelGuardCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "WheelGuardCost", x.WheelGuardCost, y.WheelGuardCost)); }

            if (x.IsRemoved != y.IsRemoved) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "IsRemoved", x.IsRemoved, y.IsRemoved)); }
            if (x.RemovedDate != y.RemovedDate) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "RemovedDate", x.RemovedDate, y.RemovedDate)); }

            if (x.Other != y.Other) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "Other", x.Other, y.Other)); }
            if (x.LastPhysicalLocation != y.LastPhysicalLocation) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "LastPhysicalLocation", x.LastPhysicalLocation, y.LastPhysicalLocation)); }
            if (x.HasTyreInsurance != y.HasTyreInsurance) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasTyreInsurance", x.HasTyreInsurance, y.HasTyreInsurance)); }
            if (x.HasAlloyInsurance != y.HasAlloyInsurance) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasAlloyInsurance", x.HasAlloyInsurance, y.HasAlloyInsurance)); }
            if (x.HasTyreAndAlloyInsurance != y.HasTyreAndAlloyInsurance) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasTyreAndAlloyInsurance", x.HasTyreAndAlloyInsurance, y.HasTyreAndAlloyInsurance)); }
            if (x.HasWheelGuard != y.HasWheelGuard) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "HasWheelGuard", x.HasWheelGuard, y.HasWheelGuard)); }
            if (x.TyreInsuranceSale != y.TyreInsuranceSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreInsuranceSale", x.TyreInsuranceSale, y.TyreInsuranceSale)); }
            if (x.TyreInsuranceCost != y.TyreInsuranceCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreInsuranceCost", x.TyreInsuranceCost, y.TyreInsuranceCost)); }
            if (x.TyreInsuranceCommission != y.TyreInsuranceCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreInsuranceCommission", x.TyreInsuranceCommission, y.TyreInsuranceCommission)); }
            if (x.AlloyInsuranceSale != y.AlloyInsuranceSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "AlloyInsuranceSale", x.AlloyInsuranceSale, y.AlloyInsuranceSale)); }
            if (x.AlloyInsuranceCost != y.AlloyInsuranceCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "AlloyInsuranceCost", x.AlloyInsuranceCost, y.AlloyInsuranceCost)); }
            if (x.AlloyInsuranceCommission != y.AlloyInsuranceCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "AlloyInsuranceCommission", x.AlloyInsuranceCommission, y.AlloyInsuranceCommission)); }
            if (x.TyreAndAlloyInsuranceSale != y.TyreAndAlloyInsuranceSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreAndAlloyInsuranceSale", x.TyreAndAlloyInsuranceSale, y.TyreAndAlloyInsuranceSale)); }
            if (x.TyreAndAlloyInsuranceCost != y.TyreAndAlloyInsuranceCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreAndAlloyInsuranceCost", x.TyreAndAlloyInsuranceCost, y.TyreAndAlloyInsuranceCost)); }
            if (x.TyreAndAlloyInsuranceCommission != y.TyreAndAlloyInsuranceCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "TyreAndAlloyInsuranceCommission", x.TyreAndAlloyInsuranceCommission, y.TyreAndAlloyInsuranceCommission)); }
            if (x.WheelGuardSale != y.WheelGuardSale) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "WheelGuardSale", x.WheelGuardSale, y.WheelGuardSale)); }
            if (x.WheelGuardCost != y.WheelGuardCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "WheelGuardCost", x.WheelGuardCost, y.WheelGuardCost)); }
            if (x.WheelGuardCommission != y.WheelGuardCommission) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "WheelGuardCommission", x.WheelGuardCommission, y.WheelGuardCommission)); }
            if (x.VatCost != y.VatCost) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "VatCost", x.VatCost, y.VatCost)); }
            if (x.DeliverySite_Id != y.DeliverySite_Id) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "DeliverySite_Id", x.DeliverySite_Id, y.DeliverySite_Id)); }

            if (x.FinanceType != y.FinanceType) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "FinanceType", x.FinanceType, y.FinanceType)); }
            if (x.IsClosed != y.IsClosed) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "IsClosed", x.IsClosed, y.IsClosed)); }

            if (x.OemReference != y.OemReference) { diffs.Add(NewDiff("Deal" + sourceData, modelIdentifier, "OemReference", x.OemReference, y.OemReference)); }


            return diffs;
        }

        private static Diff NewDiff(string model, string modelIdentifier, string key, object oldval, object newval)
        {
            return new Diff()
            {
                Model = model,
                ModelIdent = modelIdentifier,
                Key = key,
                OldValue = oldval?.ToString() ?? "null",
                NewValue = newval?.ToString() ?? "null",


            };
        }

        public int GetHashCode(Deal obj)
        {
            return obj.StockNumber.GetHashCode();

        }

        public static void updateExistingDeal(Deal newDeal, Deal oldDeal)
        {
            oldDeal.Description = newDeal.Description;
            oldDeal.Reg = newDeal.Reg;
            oldDeal.StockDate = newDeal.StockDate;
            oldDeal.RegisteredDate = newDeal.RegisteredDate;
            oldDeal.Customer = newDeal.Customer;
            oldDeal.OrderDate = newDeal.OrderDate;
            oldDeal.ActualDeliveryDate = newDeal.ActualDeliveryDate;
            oldDeal.IsDelivered = newDeal.IsDelivered;
            oldDeal.HasServicePlan = newDeal.HasServicePlan;
            oldDeal.IsFinanced = newDeal.IsFinanced;
            oldDeal.HasCosmeticInsurance = newDeal.HasCosmeticInsurance;
            oldDeal.HasPaintProtection = newDeal.HasPaintProtection;
            oldDeal.HasPaintProtectionAccessory = newDeal.HasPaintProtectionAccessory;
            oldDeal.HasGapInsurance = newDeal.HasGapInsurance;
            //oldDeal.TotalProductCount = newDeal.TotalProductCount;
            oldDeal.Discount = newDeal.Discount;
            oldDeal.ServicePlanSale = newDeal.ServicePlanSale;
            oldDeal.ServicePlanCost = newDeal.ServicePlanCost;
            oldDeal.FuelSale = newDeal.FuelSale;
            oldDeal.FuelCost = newDeal.FuelCost;
            oldDeal.OemDeliverySale = newDeal.OemDeliverySale;
            oldDeal.OemDeliveryCost = newDeal.OemDeliveryCost;
            oldDeal.WarrantySale = newDeal.WarrantySale;
            oldDeal.WarrantyCost = newDeal.WarrantyCost;
            oldDeal.StandardWarrantyCost = newDeal.StandardWarrantyCost;
            oldDeal.AccessoriesSale = newDeal.AccessoriesSale;
            oldDeal.AccessoriesCost = newDeal.AccessoriesCost;
            oldDeal.NewBonus1 = newDeal.NewBonus1;
            oldDeal.NewBonus2 = newDeal.NewBonus2;
            oldDeal.PartExOverAllowance1 = newDeal.PartExOverAllowance1;
            oldDeal.FinanceCommission = newDeal.FinanceCommission;
            oldDeal.FinanceSubsidy = newDeal.FinanceSubsidy;
            oldDeal.CosmeticInsuranceSale = newDeal.CosmeticInsuranceSale;
            oldDeal.CosmeticInsuranceCost = newDeal.CosmeticInsuranceCost;
            oldDeal.PaintProtectionSale = newDeal.PaintProtectionSale;
            oldDeal.PaintProtectionCost = newDeal.PaintProtectionCost;
            oldDeal.PaintProtectionAccessorySale = newDeal.PaintProtectionAccessorySale;
            oldDeal.PaintProtectionAccessoryCost = newDeal.PaintProtectionAccessoryCost;
            oldDeal.GapInsuranceSale = newDeal.GapInsuranceSale;
            oldDeal.GapInsuranceCost = newDeal.GapInsuranceCost;
            oldDeal.GapInsuranceCommission = newDeal.GapInsuranceCommission;

            oldDeal.TyreInsuranceSale = newDeal.TyreInsuranceSale;
            oldDeal.TyreInsuranceCost = newDeal.TyreInsuranceCost;
            oldDeal.AlloyInsuranceSale = newDeal.AlloyInsuranceSale;
            oldDeal.AlloyInsuranceCost = newDeal.AlloyInsuranceCost;
            oldDeal.TyreAndAlloyInsuranceSale = newDeal.TyreAndAlloyInsuranceSale;
            oldDeal.TyreAndAlloyInsuranceCost = newDeal.TyreAndAlloyInsuranceCost;
            oldDeal.WheelGuardSale = newDeal.WheelGuardSale;
            oldDeal.WheelGuardCost = newDeal.WheelGuardCost;
            oldDeal.HasWheelGuard = newDeal.HasWheelGuard;
            //oldDeal.HasTyreAlloy = newDeal.HasTyreAlloy;
            oldDeal.HasTyreInsurance = newDeal.HasTyreInsurance;
            oldDeal.HasTyreAndAlloyInsurance = newDeal.HasTyreAndAlloyInsurance;

            //oldDeal.FAndIProfit = newDeal.FAndIProfit;
            oldDeal.Site_Id = newDeal.Site_Id;
            oldDeal.Salesman_Id = newDeal.Salesman_Id;
            oldDeal.StockNumber = newDeal.StockNumber;
            oldDeal.LastUpdated = newDeal.LastUpdated;
            oldDeal.IsUpdated = true;
            oldDeal.InvoiceDate = newDeal.InvoiceDate;
            oldDeal.OrigOrderDate = newDeal.OrigOrderDate;
            oldDeal.OrigDeliveryDate = newDeal.OrigDeliveryDate;
            oldDeal.IsLateCost = newDeal.IsLateCost;
            oldDeal.BrokerCost = newDeal.BrokerCost;
            oldDeal.IntroCommission = newDeal.IntroCommission;
            oldDeal.SelectCommission = newDeal.SelectCommission;
            oldDeal.StandardsCommission = newDeal.StandardsCommission;
            oldDeal.RCIFinanceCommission = newDeal.RCIFinanceCommission;
            oldDeal.VehicleType_Id = newDeal.VehicleType_Id;
            oldDeal.ProPlusCommission = newDeal.ProPlusCommission;
            oldDeal.Model = newDeal.Model;
            oldDeal.Variant = newDeal.Variant;
            oldDeal.VariantTxt = newDeal.VariantTxt;
            oldDeal.ModelYear = newDeal.ModelYear;
            oldDeal.IsInvoiced = newDeal.IsInvoiced;
            oldDeal.FinanceCo = newDeal.FinanceCo;
            oldDeal.Sale = newDeal.Sale;
            oldDeal.CoS = newDeal.CoS;
            oldDeal.MechPrep = newDeal.MechPrep;
            oldDeal.BodyPrep = newDeal.BodyPrep;
            oldDeal.Error = newDeal.Error;
            oldDeal.Franchise_Id = newDeal.Franchise_Id;
            oldDeal.OrderType_Id = newDeal.OrderType_Id;
            oldDeal.VehicleClass_Id = newDeal.VehicleClass_Id;
            //oldDeal.TotalVehicleProfit = newDeal.TotalVehicleProfit;
            oldDeal.TotalNLProfit = newDeal.TotalNLProfit;
            oldDeal.PDICost = newDeal.PDICost;
            oldDeal.CosmeticInsuranceCommission = newDeal.CosmeticInsuranceCommission;
            oldDeal.HasWarranty = newDeal.HasWarranty;
            oldDeal.HasShortWarranty = newDeal.HasShortWarranty;
            oldDeal.VehicleAge = newDeal.VehicleAge;
            oldDeal.Description = newDeal.Description;
            oldDeal.Units = newDeal.Units;
            oldDeal.HandoverDate = newDeal.HandoverDate;
            oldDeal.AccountingDate = newDeal.AccountingDate;

            oldDeal.OemReference = newDeal.OemReference;
            oldDeal.LastUpdated = DateTime.UtcNow; //update the lastUpdated date

        }
    }
}


