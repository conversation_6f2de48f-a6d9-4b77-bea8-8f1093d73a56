import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CommissionAdjustmentVM } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Component({
  selector: 'app-commission-adjustment',
  templateUrl: './commissionAdjustment.component.html',
  styleUrls: ['./commissionAdjustment.component.scss']
})
export class CommissionAdjustmentComponent implements OnInit {
  @ViewChild('commissionAdjustmentModalRef', { static: true }) commissionAdjustmentModalRef: ElementRef;

  @Input() public salesExecId: string;
  @Input() public month: Date;
  @Input() public readOnly: boolean;

  commissionAdjustmentsOriginal: CommissionAdjustmentVM[];
  commissionAdjustments: CommissionAdjustmentVM[] = [];
  newDescription: string;
  newValue: number;
  amAddingAdjustment: boolean = false;
  adjustmentsUpdated: boolean = false;
  totalAdjustmentsValue: number;

  constructor(
    public getDataMethods: GetDataMethodsService,
    public activeModal: NgbActiveModal,
    public selections: SelectionsService,
    public constants: ConstantsService,
    public modalService: NgbModal
    ) { }

  ngOnInit() {
    this.getData();
  }

  getData() {
    this.getDataMethods.getCommissionAdjustments(this.salesExecId, this.month.toISOString()).subscribe(res => {
      this.commissionAdjustmentsOriginal = this.constants.clone(res);
      this.commissionAdjustments = res;
      this.totalAdjustmentsValue = this.constants.sum(this.commissionAdjustments.map(x => x.Value));
      
      this.modalService.open(this.commissionAdjustmentModalRef, { size: 'md', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
        this.modalService.dismissAll();
      },
      (reason) => {
        this.activeModal.close(this.commissionAdjustments);
      });
    })
    
  }

  update(){
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })
    this.getDataMethods.updateCommissionAdjustment(this.commissionAdjustments).subscribe(res => {
      this.commissionAdjustments = res;
      this.selections.triggerSpinner.next({ show: false})
    })
    

  }
  delete(commAdjId){
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })
    this.getDataMethods.deleteCommissionAdjustment(commAdjId).subscribe(res => {
      this.commissionAdjustments = this.commissionAdjustments.filter(x => x.Id !- commAdjId);
      this.selections.triggerSpinner.next({ show: false})
    })
    
  }
  cancel() {
    this.newDescription = null;
    this.newValue = null;
    this.amAddingAdjustment = false;
  }

  create(){
    this.amAddingAdjustment = false;

    var c  = {} as CommissionAdjustmentVM;
    c.Description = this.newDescription,
    c.Value = this.newValue
    c.SalesExecId = parseInt(this.salesExecId)
    c.Month = this.month

    
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })
    this.getDataMethods.createCommissionAdjustment(c).subscribe(res => {
      this.commissionAdjustments = res;
      this.newDescription = null;
      this.newValue = null;
      this.selections.triggerSpinner.next({ show: false })
    })
    

  }

  checkIfUpdated() {
    this.commissionAdjustments.forEach(x => x.Value = Number(x.Value));

    let original: string = JSON.stringify(this.commissionAdjustmentsOriginal);
    let current: string = JSON.stringify(this.commissionAdjustments);

    this.adjustmentsUpdated = original != current ? true : false;
  }

  cancelAllChanges() {
    this.commissionAdjustments = this.constants.clone(this.commissionAdjustmentsOriginal);
    this.adjustmentsUpdated = false;
  }
}

