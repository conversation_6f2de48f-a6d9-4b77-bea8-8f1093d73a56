import { Component, HostListener, Input, OnInit } from '@angular/core';
import { CellClickedEvent, ColDef, ColGroupDef, ColumnApi, GridApi, GridOptions, IRowNode, RowNode } from 'ag-grid-community';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../../cph.pipe';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SeedDataService } from '../../../services/seedData.service';
import { SelectionsService } from '../../../services/selections.service';
import { PersonPhotoComponent } from '../../../_cellRenderers/personPhoto';
import { HorizontalBarTgtActComponent } from '../horizontalBarTgtAct.component';
import { ProductivityComponent } from '../productivity';
import { SalesmanEfficiencyService } from '../salesmanEfficiency.service';
import { SalesmanEfficiencyRow } from '../SalesmanEfficiencyRow';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';





type ColumnDef = ColDef | ColGroupDef;

@Component({
  selector: 'salesmanEfficiencyTable',
  templateUrl: './salesmanEfficiencyTable.component.html',
  styleUrls: ['./../../../../styles/components/_agGrid.scss'],
})



export class SalesmanEfficiencyTableComponent implements OnInit {


  @Input() tableType: string;

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  public gridColumnApi: ColumnApi;

  mainTableGridOptions: GridOptions
  gridApiColumnDefinitions: any;
  currentRowHeight: number;
  frameworkComponents: { agColumnHeader: any; };



  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public seed: SeedDataService,
    public excel: ExcelExportService,
    public service: SalesmanEfficiencyService,
    public gridHelpersService: AGGridMethodsService


  ) {


  }


  ngOnInit() {
    //this.gridHelpers.topBottomHighlights = [];
    this.mainTableGridOptions = this.getGridDefinitions();
  }


  ngOnDestroy() {
    if (this.tableType == 'people') { this.service.peopleGrid = null; }
    else if (this.tableType == 'sites') { this.service.siteGrid = null; }
    else { this.service.regionsGrid = null; }
  }


  initParams() {

  }


  getGridDefinitions() {
    return {
      getMainMenuItems:(params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlights),
      onGridReady: (params) => this.onGridReady(params),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      onCellMouseOver: (params) => {
        //this.onCellMouseOver(params);
      },
      onCellClicked: (params) => {
        this.onCellClick(params);
      },

      getRowHeight: (params) => {

        //let normalHeight = !!this.service.chosenRow ? Math.min(60, Math.max(60, Math.round(35 * this.selections.screenHeight / 960))) : Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true

      },
      rowData: this.getRowData(),
      pinnedBottomRowData: this.getPinnedBottomData(),
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },
      columnDefs: this.getColumnDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }
  }

  getRowData(): SalesmanEfficiencyRow[] {
    if (this.tableType !== 'people') {
      if (this.tableType == 'sites') {
        return this.service.siteTableRows.filter(x => x.IsSite)
      } else {
        return this.service.siteTableRows.filter(x => x.IsRegion)
      }
    } else {
      return this.service.peopleTableRows.filter(x=>!x.IsTotal);
    }

  }

  getPinnedBottomData(): SalesmanEfficiencyRow[] {
    if (!!this.service.chosenRow) {
      //have chosen a row, so now are looking at people.  Will have to workout a total row
      //return [this.service.chosenRow]
      return this.service.peopleTableRows.filter(x => x.PersonId === 0)
    } else {
      return this.service.siteTableRows.filter(x=>x.IsTotal)
    }
  }


  updateGrid(data: SalesmanEfficiencyRow[]) {
    
    this.gridApi.setRowData(this.getRowData());
    this.gridApi.setPinnedBottomRowData(this.getPinnedBottomData())
  }

  getColumnDefs() {
    let colDefs: ColumnDef[] = [
      { headerName: '', cellRenderer: PersonPhotoComponent, colId: 'personPhoto', width: 100, type: 'label', hide: !this.service.chosenRow, },
      { headerName: '', field: 'Label', width: 200, type: 'label', },
      { headerName: 'Role', field: 'Role', width: 100, type: 'label', hide: !this.service.chosenRow, }, //the person's role i.e. New / Used etc.
      { headerName: 'Execs', field: 'ExecCount', width: 100, type: 'number', hide: !!this.service.chosenRow },
      { headerName: 'Site', field: 'Site', width: 100, type: 'label', hide: !this.service.chosenRow },

      {
        headerName: 'Efficiency', children: [
          { headerName: this.constants.translatedText.Target, field: 'Target', width: 106, type: 'number' }, 
          { headerName: 'Deals', field: 'totalDealCount', width: 106, type: 'number' }, 
          { headerName: 'Efficiency', field: 'Efficiency', width: 106, type: 'percent' }, 
          { headerName: '', colId: 'efficiencyBar', cellRenderer: HorizontalBarTgtActComponent, width: 150, type: 'label', },
        ]
      },
      {
        headerName: 'Compliance', children: [
          { headerName: 'Fin Pen New', field: 'FinancePenNew', width: 106, type: 'percent' },
          { headerName: 'Fin Pen Used', field: 'FinancePenUsed', width: 106, type: 'percent' },
          { headerName: 'Cosmetic', field: 'CosmeticPen', width: 106, type: 'percent' },
          { headerName: 'Paint Protect', field: 'PaintPen', width: 106, type: 'percent' },
          { headerName: this.constants.translatedText.Gap, field: 'GapPen', width: 106, type: 'percent' },
          { headerName: 'Tyre', field: 'TyrePen', width: 106, type: 'percent' },
          { headerName: 'TyreAlloy', field: 'TyreAlloyPen', width: 106, type: 'percent' },
          { headerName: 'WheelGuard', field: 'WheelGuardPen', width: 106, type: 'percent' },
          { headerName: 'Warranty', field: 'WarrantyPen', width: 106, type: 'percent' },
          { headerName: 'Compliance', cellRenderer: ProductivityComponent, field: 'Productivity', colId: 'Productivity', width: 106, type: 'percentNoRenderer' }, 

          { headerName: '', colId: 'productivityBar', cellRenderer: HorizontalBarTgtActComponent, width: 150, type: 'label', },
        ]
      },
    ]

    return colDefs;
  }

  onCellClick(params: CellClickedEvent) {

    if (params.colDef.colId == 'Productivity') return;
    if (!this.service.chosenRow) {
      this.service.onClickedSite(params.data)
    } else {
      this.service.onClickedPerson(params.data)
    }
  }


  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    if (this.tableType == 'people') { this.service.peopleGrid = this; }
    else if (this.tableType === 'sites') { this.service.siteGrid = this; }
    else { this.service.regionsGrid = this; }


    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();
    this.selections.triggerSpinner.next({ show: false });
  }


  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }
  }


  refreshCells() {
    if (this.gridApi) {
      this.gridApi.refreshCells();
    }
  }


  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Efficiency and Productivity');
  }

  clearHighlighting(colDef: any) {    this.gridHelpersService.clearHighlighting(colDef, this.gridApi)  }

  removeHighlighting() {
    this.mainTableGridOptions.columnDefs.forEach(colDef => {
      this.clearHighlighting(colDef);
    })
  }




}
