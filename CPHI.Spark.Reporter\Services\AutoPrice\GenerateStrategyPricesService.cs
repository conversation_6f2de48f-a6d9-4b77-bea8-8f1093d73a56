﻿using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Datadog.Trace;
using log4net;
using MoreLinq.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{


   public interface IGenerateStrategyPricesService
   {
      Task CalcStrategyPriceOtherSiteLocations(ILog logger, List<Model.DealerGroupName> dealerGroups);
      Task GenerateStrategyPricesAndSave(ILog loggerIn, List<DealerGroupName> dealerGroups);
   }



   public class GenerateStrategyPricesService : IGenerateStrategyPricesService
   {
      private ILog logger;
      private readonly HttpClient httpClient;
      private readonly IHttpClientFactory _httpClientFactory;

      public GenerateStrategyPricesService(IHttpClientFactory httpClientFactory)
      {
         this.httpClient = httpClientFactory.CreateClient();
         this._httpClientFactory = httpClientFactory;
      }

      public async Task GenerateStrategyPricesAndSave(ILog loggerIn, List<Model.DealerGroupName> dealerGroups)
      {
         logger = loggerIn;
         if (dealerGroups.Count == 0) { return; }
         try
         {
            logger.Info("----------------------------------------------------------");
            logger.Info("StrategyPrices");

            using (var parentScope = Tracer.Instance.StartActive("UpdateDaysToSell"))
            {

               foreach (Model.DealerGroupName dealerGroup in dealerGroups)
               {

                  using (var childScope = Tracer.Instance.StartActive($"Begin dealergroup {dealerGroup}"))
                  {
                     childScope.Span.SetTag("DealerGroup", dealerGroup.ToString());


                     string connString = ConfigService.GetConnectionString(dealerGroup);
                     CalculateStrategyPricesService calculateStrategyPricesService = new CalculateStrategyPricesService(connString, _httpClientFactory);
                     var logMessage = LoggingService.InitLogMessage();
                     try
                     {

                        logger.Info($"StrategyPrices: {dealerGroup}");

                        RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(connString);
                        List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

                        if (retailers.Count > 0)
                        {
                           try
                           {
                              Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingsDict = await retailerSitesDataAccess.GetRetailerStrategyBandings();
                              await calculateStrategyPricesService.CalculateStrategyPricesForTodayAdverts(dealerGroup, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
                                 ConfigService.AutotraderBaseURL, logger, false, bandingsDict);

                           }
                           catch (Exception ex)
                           {
                              logger.Error(ex);
                              await EmailerService.SendMailOnError(dealerGroup, "StrategyPrices - Error", ex);
                              LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                           }
                        }
                        else
                        {
                           logger.Info($"StrategyPrices: {dealerGroup}: No retailers");
                        }
                     }
                     catch (Exception ex)
                     {
                        await EmailerService.LogException(ex, logger, "StrategyPrices");
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                     finally
                     {
                        await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "StrategyPrices");
                     }
                  }

               }

               logger.Info("Completed StrategyPrices");
               logger.Info("----------------------------------------------------------");

            }
         }

         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }

      public async Task CalcStrategyPriceOtherSiteLocations(ILog logger, List<Model.DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0) { return; }
         logger.Info("--------------------------------------------------------------------");
         logger.Info("Started LocationOptimiser: CalcStrategy");

         foreach (Model.DealerGroupName dealerGroup in dealerGroups)
         {
            logger.Info($"LocationOptimiser: CalcStrategy: Starting {dealerGroup}");
            var logMessage = LoggingService.InitLogMessage();
            try
            {

               RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
               List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

               if (retailers.Count > 0)
               {
                  try
                  {

                     await CalcStrategyPriceOtherSiteLocationsForThisDealerGroup(dealerGroup, logger);

                  }

                  catch (Exception ex)
                  {
                     logger.Error(ex);
                     await EmailerService.SendMailOnError(dealerGroup, "LocationOptimiser: CalcStrategy - Error", ex);
                     LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                  }
               }
            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "LocationOptimiser: CalcStrategy");
               LoggingService.AddErrorLogMessage(logMessage, ex.Message);
            }
            finally
            {
               await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "LocationOptimiser: CalcStrategy");
            }

         }

         logger.Info("Completed LocationOptimiser: CalcStrategy");
         logger.Info("----------------------------------------------------------------------");
      }













      private  async Task CalcStrategyPriceOtherSiteLocationsForThisDealerGroup(DealerGroupName dealerGroup, ILog logger)
      {
         string _connectionString = ConfigService.GetConnectionString(dealerGroup);
         DateTime runDate = DateTime.Now;

         //get data we will need

         /// retailer sites
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSitesWithStrategies(runDate, dealerGroup);
         var retailerLookup = retailers.ToDictionary(x => x.Id);

         /// all existing adverts
         AutoPriceDataAccess autoPriceDataAccess = new AutoPriceDataAccess(_connectionString);
         var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         VehicleAdvertsService vehicleAdvertsService = new VehicleAdvertsService(_connectionString);
         GetVehicleAdvertsWithRatingsParams parms = new GetVehicleAdvertsWithRatingsParams()
         {
            EffectiveDate = DateTime.Today,
            RetailerSiteIds = string.Join(",", retailers.Select(x => x.Id).Distinct()),
            UserEligibleSites = string.Join(",", retailers.Select(x => x.Site_Id).Distinct()),
            IncludeUnPublishedAdverts = true
         };

         Dictionary<int, RetailerSiteStrategyBandingDefinition> bandings = await retailerSitesDataAccess.GetRetailerStrategyBandings();
         List<string> lifecycles = AutoPriceHelperService.GetAllLifecycleStatusesNoSoldOrWastebin();
         IEnumerable<VehicleAdvertWithRating> vehicleAdverts = await vehicleAdvertsService.GetVehicleAdvertsWithRatings(
             parms,
             bandings,
             dealerGroup,
             retailers.Select(x => x.Id).Distinct().ToList(),
             retailers.Select(x => x.Site_Id).Distinct().ToList(),
             lifecycles
             );
         var advertsLookup = vehicleAdverts.ToDictionary(x => x.AdId);
         List<int> advertIds = vehicleAdverts.Select(x => x.AdId).ToList();
         var competitorLinks = await vehicleAdvertsService.GetAdvertsAndCompetitorLinks(dealerGroup, advertIds);
         var linksLookup = competitorLinks.ToDictionary(x => x.AdvertId, x => x.CompetitorLink);

         /// other site ratings today
         VehicleAdvertRatingBySitesDataAccess vehicleAdvertRatingBySitesDataAccess = new VehicleAdvertRatingBySitesDataAccess(_connectionString);
         IEnumerable<VehicleAdvertRatingBySite> todayLocationOptimiserRatings = await vehicleAdvertRatingBySitesDataAccess.GetTodayRatings(dealerGroup);
         var locationOptimiserRatingsBySite = todayLocationOptimiserRatings.ToLookup(x => x.RetailerSite_Id);

         /// get token
         var atFutureValsClient = new AutoTraderFutureValuationsClient(_httpClientFactory,
         ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
         ConfigService.AutotraderBaseURL);
         var atMetricsClient = new AutoTraderVehicleMetricsClient(_httpClientFactory,
          ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret,
          ConfigService.AutotraderBaseURL);
         AutoTraderCompetitorClient competitorClient = new AutoTraderCompetitorClient(_httpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         AutoTraderApiTokenClient atTokenClient = new AutoTraderApiTokenClient(_httpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         var tokenResponse = await atTokenClient.GetToken();
         List<VehicleAdvertRatingBySite> updatedRatings = new List<VehicleAdvertRatingBySite>();


         /// iterate over sites then ratings for that site.  workout strategy
         foreach (var siteLocationOptimiserRatingsSet in locationOptimiserRatingsBySite)
         {
            int retailerId = siteLocationOptimiserRatingsSet.Key;
            var retailerWithStrategy = retailerLookup[retailerId];
            var thisSiteLocationOptimiserRatings = siteLocationOptimiserRatingsSet.ToList();

            foreach (var rating in thisSiteLocationOptimiserRatings)
            {
               VehicleAdvertWithRating advert;
               bool haveAdvert = advertsLookup.TryGetValue(rating.VehicleAdvertSnapshot.VehicleAdvert_Id, out advert);
               if (!haveAdvert)
               {
                  continue;
               }
               string competitorLink = linksLookup[advert.AdId];
               AdvertParamsForStrategyCalculator calcParams = new AdvertParamsForStrategyCalculator(
                  advert, retailerWithStrategy, tokenResponse, competitorLink);
               tokenResponse = await atTokenClient.CheckExpiryAndRegenerate(tokenResponse);
               List<StrategyPriceBuildUpItem> thisAdvertBuildUpItems = new List<StrategyPriceBuildUpItem>();
               await ApplyStrategyService.ApplyRuleSet(
                   runDate,
                   calcParams,
                   retailerWithStrategy.StrategySelectionRuleSet,
                   retailerWithStrategy,
                   retailerWithStrategy.Site,
                   thisAdvertBuildUpItems,
                   tokenResponse,
                   competitorClient,
                   ConfigService.AutotraderBaseURL,
                   logger, null,
                   atFutureValsClient,
                    atMetricsClient);

               //if we have made some changes, update the rating
               if (thisAdvertBuildUpItems.Count > 0)
               {
                  rating.StrategyPrice = (decimal)advert.ValuationAdjRetail;
                  foreach (var item in thisAdvertBuildUpItems)
                  {
                     rating.StrategyPrice += item.Impact;
                  }
                  updatedRatings.Add(rating);
               }

            }


         }

         await vehicleAdvertRatingBySitesDataAccess.SaveUpdatedRatings(updatedRatings);
      }
   }
}
