<div  class="tileHeader clickable" (click)="navigateToMainPage()">
  <div class="headerWords">
    <h5>{{constants.translatedText.Dashboard_PartsStockValue}} > 1 {{constants.translatedText.Common_Year}}
    </h5>
  </div>
</div>


  <div class="spaceAround">
    <div class="spaceBetween column">
      <h1>
          {{data.StockValueOver1Yr | cph:'currency':'0'}}  
      </h1>
    <div class="label">
    {{ constants.translatedText.Dashboard_PartsStock_TotalValue }}
    </div>
    </div>

    <div class="spaceBetween column" title="Total Stock is {{data.TotalStockValue|cph:'currency':0}}">
    <h1 class="percentage" [ngClass]="{'goodFont' : data.Percentage <= 0.10 , 'okFont' : data.Percentage > 0.10 && data.Percentage <= 0.20 , 'badFont' : data.Percentage > 0.20}">
        {{data.Percentage|cph:'percent':'0'}}  
      </h1>
    <div class="label">
    {{ constants.translatedText.Dashboard_PartsStock_OfTotalStock }}
    </div>
    </div>

   
 </div>