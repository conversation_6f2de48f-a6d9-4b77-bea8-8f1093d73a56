-- Check if the parameter already exists to avoid duplicates
IF NOT EXISTS (SELECT 1 FROM GlobalParams WHERE Description = 'UseUserPostcode' AND DealerGroup_Id = 28)
BEGIN
    -- Add the ShowLCVToggle parameter for Enterprise (DealerGroup_Id = 28)
    INSERT INTO GlobalParams
    (DateFrom, DateTo, Value, TextValue, Description, DealerGroup_Id)
    VALUES
    (GETDATE(), NULL, 0, 'True', 'UseUserPostcode', 28)
    
    PRINT 'UseUserPostcode GlobalParam added for Enterprise (DealerGroup_Id = 28)'
END
ELSE
BEGIN
    PRINT 'UseUserPostcode GlobalParam already exists for Enterprise (DealerGroup_Id = 28)'
END