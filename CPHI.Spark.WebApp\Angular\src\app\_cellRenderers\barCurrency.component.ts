import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ConstantsService } from "../services/constants.service";

export interface Thresholds {
    good: number;
    bad: number;
}

export interface BarClasses {
    good: boolean;
    ok: boolean;
    bad: boolean;
}

@Component({
    selector: 'bar-cell',
    template:  `
        <div class="barHolder">
            <div class="barActual" [ngClass]="barClass" [ngStyle]="{ 'width.%': width * 100 }">
        </div>
        <div class="barLabel">{{ value | cph:'currency':0 }}</div>
    `
    ,
    styles: [`
        .barHolder {
            width: 90%;
            height: 80%;
            display: flex;
            overflow: hidden;
            background: var(--grey90);
            margin: 0 auto;
        }
        
        .barActual { position: relative; transition: ease all 0.3s; }
        .barLabel { position: absolute; width: 80%; display: flex; align-items: center; justify-content: flex-end; top: 0; bottom: 0; }

        .ok { background-color: var(--brightColour); }
        .good { background-color: var(--goodColour); }
        .bad { background-color: var(--badColour); }
    `]
})
export class BarCurrencyComponent implements ICellRendererAngularComp {
    thresholds: Thresholds;
    value: number;
    barClass: BarClasses;
    width: number;

    constructor(
        public constants: ConstantsService
    ) { }

    agInit(params: any): void {

        this.value = params.value;

        if(params.data.Label != this.constants.translatedText.Total)
        {
            this.thresholds = {
                good: params.column.userProvidedColDef.cellRendererParams.good,
                bad: params.column.userProvidedColDef.cellRendererParams.bad
            }
    
            this.barClass = {
                good: this.value >= this.thresholds.good,
                ok: this.value < this.thresholds.good && this.value >= this.thresholds.bad,
                bad: this.value < this.thresholds.bad
            }
            
            setTimeout(() => {
                this.width = this.value / this.thresholds.good;
            }, 150)
        }


    }

    refresh(): boolean {
        return false;
    }
}
