﻿using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
    [Table("BuyingCostsSets", Schema = "autoprice")]
    public class BuyingCostsSet
    {
        public int Id { get; set; }
        public int RetailerSite_Id { get; set; }
        [ForeignKey("RetailerSite_Id")]
        public RetailerSite RetailerSite { get; set; }

        public BulkUploadPredefinedTemplateType TemplateType { get; set; }
        public decimal ValuationUpTo { get; set; }
        public bool IsMarginPercent { get; set; } //does the following property represent a % amount or £ amount
        public decimal MarginAmount { get; set; }  //e.g. 0.05 meaning we aim to make 5% of the valuation as margin.   Or £2000.
        public bool IsAuctionFeePercent { get; set; } //does the following property represent a % amount or £ amount
        public decimal AuctionFeeAmount { get; set; } 
        public bool IsPrepPercent { get; set; } //does the following property represent a % amount or £ amount
        public decimal PrepAmount { get; set; }
    }


    public enum BulkUploadPredefinedTemplateType
    {
        AstonBarclay,
        CarWow,
        Manheim,
        Motorway,
        Motorway2,
        Motorway3,
        Shoreham,
        FleetAuctionGroup,
        BCA,
        Motability,
        SantanderLive,
        VCRSLive,
        UCaRS,
        Other

    }
}
