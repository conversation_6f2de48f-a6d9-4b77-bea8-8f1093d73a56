import { Component, Input, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CellClassParams, ColDef, DomLayoutType, GridOptions } from 'ag-grid-community';
import { StockReportModalComponent } from 'src/app/components/stockReportModal/stockReportModal.component';
import { StockModalRowWithNext30 } from 'src/app/model/sales.model';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../../cph.pipe';
import { AGGridMethodsService } from '../../../services/agGridMethods.service';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from '../../../services/selections.service';
import { CustomHeaderComponent } from '../../../_cellRenderers/customHeader.component';
import { StockSiteRow } from '../stockReport.component';
import { StockReportService } from '../stockReport.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

@Component({
  selector: 'overAgeStockTable',
  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>
    <!-- [domLayout]="domLayout" -->
    <ag-grid-angular class="ag-theme-balham" 
    [gridOptions]="mainTableGridOptions"
   
      
      (gridReady)="onGridReady($event)"
     
      > 
      
    </ag-grid-angular>
    </div>
    <div *ngIf="!isRegionalTable" class="tableSpacer"></div>
    `
  ,
  styleUrls: ['./../../../../styles/components/_agGrid.scss'],
  styles: [
    `
    ag-grid-angular {
      max-width: 1927px;
    }
  `
  ]
})



export class OverAgeStockTableComponent implements OnInit {


  @Input() public showUsedCols: boolean;
  @Input() public isRegionalTable: boolean;

  showGrid = false;
  public gridApi;
  public gridColumnApi;


  mainTableGridOptions: GridOptions

  gridApiColumnDefinitions: any;


  highLowHighlight: {
    name: string;
    goodIds: Array<number>;
    badIds: Array<number>;
  }

  flipCols: Array<string>;
  domLayout: DomLayoutType;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public modalService: NgbModal,
    public agGridMethods: AGGridMethodsService,
    public getData: GetDataMethodsService,
    public service: StockReportService,
  ) {


  }

  ngOnDestroy() { }


  ngOnInit() {

    this.initParams();

    this.service.filterUpdated.subscribe(value => {
      this.setRowData();
    });

  }

  initParams() {

    this.selections.triggerSpinner.next({ show: true });

    this.domLayout = "autoHeight";

    this.flipCols = ['ExDemoOverage', 'ExManagementOverage', 'TacticalOverage', 'CoreUsedOverage']


    this.mainTableGridOptions = {
      getMainMenuItems:(params)=>this.agGridMethods.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlightsOverAge),
      getContextMenuItems: (params) => this.agGridMethods.getContextMenuItems(params),
      domLayout: this.domLayout,
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      suppressPropertyNamesCheck: true,
      context: { thisComponent: this },
      onCellMouseOver: (params) => {
        //this.onCellMouseOver(params);
      },
      onCellDoubleClicked: (params) => {
        this.onCellDblClick(params);
      },
      getRowHeight: (params) => {
        return params.node.rowPinned == "bottom" ? 40 : 25
      },
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },

      rowData: this.provideRowData(),
      pinnedBottomRowData: this.service.overageRowData.filter(x=>x.IsTotal),
      
      animateRows: true,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.agGridMethods.getFilterListItemHeight() }, autoHeight: true,
        //suppressColumnMoveAnimation: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsOverAge),
      },

      getRowClass: (params) => {
        if (params.data.Label == 'Total') {
          return 'total';
        }
      },

      columnDefs: [
        { headerName: "", children: [
          //site name
          { headerName: this.constants.translatedText.Site, field: 'Label', colId: 'Site', width: 220, type: 'label' },

          //overAge columns
          { headerName: this.constants.translatedText.New, field: 'New', colId: 'NewOverage', width: 100, type: 'number',  }, //menu: this.agGridMethods.getMainMenuItems('')
          { headerName: this.constants.translatedText.Demo, field: 'Demo', colId: 'DemoOverage', width: 100, type: 'number',  hide: this.constants.environment.overAgeStockTable_hideDemoColumn },
          { headerName: this.constants.translatedText.CoreUsed, field: `${this.constants.environment.overAgeStockTable_usedColumnName}`, colId: 'CoreUsedOverage', width: 100, type: 'number',  },
          { headerName: this.constants.translatedText.Tactical, field: 'Tactical', colId: 'TacticalOverage', width: 100, type: 'number',  hide: this.constants.environment.overAgeStockTable_hideTacticalColumn },
          { headerName: this.constants.translatedText.ExManagement, field: 'ExManagement', colId: 'ExManagementOverage', width: 100, type: 'number',  hide: this.constants.environment.overAgeStockTable_hideExManagementColumn },
          { headerName: this.constants.translatedText.ExDemo, field: 'ExDemo', colId: 'ExDemoOverage', width: 100, type: 'number',  hide: this.constants.environment.overAgeStockTable_hideExDemoColumn },
          { headerName: this.constants.translatedText.Trade, field: 'Trade', colId: 'TradeOverage', width: 100, type: 'number',  hide: this.constants.environment.overAgeStockTable_hideTradeColumn },
          { headerName: this.constants.translatedText.Total, field: 'Total', colId: 'TotalAgedOverage', width: 100, type: 'number', },
        ]}

      ]


    }


  }

  // cellClassProvider(params: CellClassParams){
  //   if (this.isRegionalTable) {
  //     return params?.value < 0 ? 'badFont ag-right-aligned-cell' : 'ag-right-aligned-cell';
  //   } else {
  //     return this.agGridMethods.cellClassProviderWithColourFontNew(params,this.service.topBottomHighlights);
  //   }
  // }
  
  provideRowData() {
   return  this.isRegionalTable ? this.service.overageRowData.filter(x=>x.IsRegion) : this.service.overageRowData.filter(x=>x.IsSite)
  }


  setRowData(): void {

    if (this.gridApi) {
      this.gridApi.setRowData(this.provideRowData())
      this.gridApi.setPinnedBottomRowData(this.service.overageRowData.filter(x=>x.IsTotal));
    }

  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();
    this.selections.triggerSpinner.next({ show: false });

  }


  highlightTopBottom(colDef: any, topBottomN: number) {
    this.agGridMethods.highlightTopBottom(colDef, topBottomN, this.flipCols, this.gridApi)
  }

  onCellDblClick(params: any, skipProcessingGoingOver?: boolean): void {

    //if (params.data.IsRegion || params.data.IsTotal) { return; }
    let siteIds = [];
    let clickedSiteRow: StockSiteRow = params.data;
    if(clickedSiteRow.IsSite){
      siteIds = [clickedSiteRow.SiteId]
    }
    else if(clickedSiteRow.IsRegion){
      siteIds = [...new Set(this.service.overageRowData.filter(x=>x.RegionDescription===clickedSiteRow.RegionDescription).map(x=>x.SiteId))]
    }
    else{
      siteIds = [...new Set(this.service.overageRowData.filter(x=>x.SiteId).map(x=>x.SiteId))]
    }

    this.getData.getStockModalRows(params.colDef.field, this.selections.stockReport.asAt.param, siteIds.join(',') , this.selections.stockReport.ageingOption.ageCutoff, !this.selections.stockReport.useBranchDays,
      this.selections.stockReport.franchises.toString()).subscribe((res: StockModalRowWithNext30) => {


        setTimeout(() => {
          this.selections.triggerSpinner.next({ show: false });
        }, 20)

        if (params.colDef.colId == 'SplitByType') return
        if (params.colDef.colId.indexOf('_') > -1) { skipProcessingGoingOver = true }

        //let dataPath: string = params.colDef.field.replace('count', 'vehicles').replace('Count', '');


        let header = params.data.Label + ': ' + res.AgedNow.length + ' ' + params.colDef.headerName + ' vehicles';
        if (this.selections.stockReport.report.name == 'Stock Ageing') { header = header + ' over ' + this.selections.stockReport.ageingOption.description + ' as at ' + this.selections.stockReport.asAt.description }

        let showUsedColumns = ['CoreUsed', 'Tactical', 'ExManagement', 'ExDemo'].includes(params.colDef.headerName);

        this.selections.initiateStockReportModal(skipProcessingGoingOver, res.AgedNow, res.AgedIn30, header, showUsedColumns, params.data.Label);
        //open modal

        const modalRef = this.modalService.open(StockReportModalComponent, { keyboard: true, size: 'lg' });
        //I give to modal
        //modalRef.componentInstance.givenStockItem = this.stockItem;

        modalRef.result.then((result) => { //I get back from modal
          if (result) {

          }
        });


      }, error => {

        console.error("ERROR: ", error);

      }, () => {



      });


  }


  showAgeingColumns(params) {
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[0].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[1].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[2].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[3].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[4].name, true)
    this.resizeGrid();
  }


  resizeGrid() {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  clearHighlighting(colDef: ColDef) {
    this.agGridMethods.clearHighlighting(colDef, this.gridApi);

  }


  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, this.constants.translatedText.Dashboard_StockReport_AgedOver);
  }






}
