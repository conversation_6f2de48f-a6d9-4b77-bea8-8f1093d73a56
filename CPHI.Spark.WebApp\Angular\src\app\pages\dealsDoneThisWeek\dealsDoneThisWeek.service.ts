import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe, FormatType } from 'src/app/cph.pipe';
import { DealsDoneThisWeekParams, SiteVM } from 'src/app/model/main.model';
import { LateCostOption } from 'src/app/model/sales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDealDataService } from 'src/app/services/getDeals.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { OrderBookService } from '../orderBook/orderBook.service';
import { DayAndDeals, DealsForTheWeekDeal, PlotOption } from './dealsDoneThisWeek.model';

@Injectable({
  providedIn: 'root'
})
export class DealsDoneThisWeekService {


  deals: Array<DealsForTheWeekDeal>;
  sites: SiteVM[];
  sitesIds: number[];
  vehicleTypeTypes: string[];
  orderTypeTypes: string[];
  franchises: string[];
  lateCostOption: LateCostOption;
  maxDealsCount: number;

  days: Array<DayAndDeals>

  monthsOffsetNumber: number;
  weeksOffsetNumber: number;
  daysOffsetNumber: number;

  chosenPlotOption: PlotOption;

  maxValue: number;
  minValue: number;
  showTable: boolean;
  chosenWeekStart: Date;

  constructor(
    public constants: ConstantsService,
    public orderBookService: OrderBookService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public getDealData: GetDealDataService,
    public cphPipe: CphPipe

  ) {

    this.showTable = false;
    this.monthsOffsetNumber = 0;
    this.weeksOffsetNumber = 0;
    this.daysOffsetNumber = 0;
  }


  initParams(): void {

    //if (!this.deals) {

      this.chosenPlotOption = { label: this.constants.translatedText.TotalProfit, field: 'TotalNLProfit', numberType: 'currency', dps: 0 };

      this.deals = [];
      this.sites = this.selections.userSites;
      this.sitesIds = this.constants.clone(this.selections.userSiteIds);
      this.vehicleTypeTypes = this.constants.vehicleTypeTypes;
      this.orderTypeTypes = [...new Set(this.constants.clone(this.constants.orderTypeTypesNoTrade) as string[])];
      this.franchises = this.constants.clone(this.constants.FranchiseCodes);
      this.lateCostOption = this.constants.lateCostOptions[1];
      this.maxDealsCount = 0;
      this.chosenWeekStart = this.constants.startOfThisWeek();

    //}

  }

  summariseDeals(): void {

    //knock into a 7 day shape
    this.deals.forEach(d => d.dayName =
      new Date(d.OrderDate).toLocaleDateString(this.constants.translatedText.LocaleCode, { weekday: 'short' }) +
      this.constants.ordinalSuffix(new Date(d.OrderDate).getDate()) +
      new Date(d.OrderDate).toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short' })
    )

    //create summary dates
    this.days = []


    for (let i = 0; i < 7; i++) {
      // let dayDate: Date = this.constants.deductTimezoneOffset(new Date(this.chosenWeekStart.getTime()));
      // dayDate.setDate(this.chosenWeekStart.getDate() + i);
      const dayDate = this.constants.addDays(this.chosenWeekStart,i)

      let dayName: string =
        dayDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { weekday: 'short' }) +
        this.constants.ordinalSuffix(dayDate.getDate()) +
        dayDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short' })


      let dealsForDay = this.deals.filter(d => d.dayName == dayName);
      let dealsForDaySorted = dealsForDay.sort((a, b) => new Date(a.HandoverDate).getTime() - new Date(b.HandoverDate).getTime())

      dayName = this.formatDateLabel(dayName);

      this.days.push({
        date: dayDate, label: dayName, deals: dealsForDaySorted
      })

    }
  }

  formatDateLabel(dayName: string): string {

    // Capitalise first letter
    dayName = dayName.charAt(0).toUpperCase() + dayName.slice(1);
    dayName = dayName.slice(0, 3) + ", " + dayName.slice(3, dayName.length);

    dayName = dayName.replace("nd", "nd ");
    dayName = dayName.replace("st", "st ");
    dayName = dayName.replace("rd", "rd ");
    dayName = dayName.replace("th", "th ");

    let byWord = dayName.split(" ");
    let month = dayName.split(" ").pop();
    month = month.charAt(0).toUpperCase() + month.slice(1);
    dayName = byWord[0] + " " + byWord[1] + " " + month;

    return dayName;
  }

  workoutDealHeights(field: string): void {
    this.maxValue = 0
    this.minValue = 0
    this.days.forEach(day => {
      day.deals.forEach(deal => {
        if (deal[field] > this.maxValue) this.maxValue = deal[field]
        if (deal[field] < this.minValue) this.minValue = deal[field]
      })
    })

    let range = this.maxValue - this.minValue;
    this.days.forEach(day => {
      day.deals.forEach(deal => {
        deal.heightNew = this.constants.div((deal[field] - this.minValue), range)
        deal.subLabel = this.cphPipe.transform(deal[this.chosenPlotOption.field], this.chosenPlotOption.numberType as FormatType, this.chosenPlotOption.dps);

        //blob colours
        let percentageOfGreen = 0;
        let percentageOfRed = 0;
        deal.heightNew < 0.5 ? percentageOfGreen = deal.heightNew * 2 : percentageOfGreen = 1;
        deal.heightNew < 0.5 ? percentageOfRed = 1 : percentageOfRed = (1 - deal.heightNew) * 2;

        deal.green = percentageOfGreen * 255 + 0;
        deal.red = percentageOfRed * 255 + 0;

      })
    })

    //now apply the new heights
    this.days.forEach(day => {
      day.deals.forEach(deal => {
        deal.height = deal.heightNew
      })
    })


    this.showTable = true;

  }

  getDeals(): void {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    let parms: DealsDoneThisWeekParams = {
      StartDay: this.chosenWeekStart,
      Sites: this.selections.selectedSitesIds,
      OrderTypes: this.orderTypeTypes.toString(),
      VehicleTypes: this.vehicleTypeTypes.toString(),
      Franchises: this.franchises.toString()
    }

    this.getDealData.getDealsDoneThisWeek(parms).subscribe((res: any) => {

      this.deals = res;

    }, e => {

      console.error("ERROR: ", e);

    }, () => {

      this.selections.triggerSpinner.next({ show: false });

      this.summariseDeals();

      this.workoutDealHeights(this.chosenPlotOption.field);

      setTimeout(() => {
        this.selections.triggerSpinner.next({ show: false });
      }, 20)

    })


  }






}
