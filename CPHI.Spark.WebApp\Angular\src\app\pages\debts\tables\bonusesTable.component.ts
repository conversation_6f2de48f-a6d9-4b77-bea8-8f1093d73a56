//core angular
import { <PERSON>mpo<PERSON>, OnInit, HostL<PERSON>ener, <PERSON><PERSON><PERSON>roy } from '@angular/core';
//model and cell renderers
import { BonusAgeing, DebtsBonusesSummaryRow } from '../../../model/main.model';
//services
import { ConstantsService } from '../../../services/constants.service';
import { SelectionsService } from '../../../services/selections.service';
//pipes and interceptors
import { CphPipe } from '../../../cph.pipe';
//Angular things, non-standard
import { ExcelExportService } from '../../../services/excelExportService';
import { localeEs } from 'src/environments/locale.es.js';
import { GridApi, IFilterComp, IRowModel } from 'ag-grid-community';
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'bonusesTable',
  template: `
  <div id="bonusesContainer">
    <div id="topArea">
      <div id="buttonsArea">
        <!-- For choosing ageing time period  -->
        <div class="buttonGroup filterChoiceArea">
          <button
            class="btn btn-primary allButton"
            [ngClass]="{ 'active': !selections.bonusesTable.buttonSelections.ageing }"
            (click)="filterbonusAgeing()"
          >
            <div class="buttonContent">{{ constants.translatedText.AllAges }}</div>
          </button>
          <button
            *ngFor="let ageing of selections.bonusesTable.ageings"
            class="btn btn-primary"
            [ngClass]="{ 'active': ageing.description == selections.bonusesTable.buttonSelections.ageing?.description }"
            (click)="filterbonusAgeing(ageing)"
          >
            <div class="buttonContent wideButton">
              <div>{{ ageing.description }}</div>
              <div>{{ ageing.value |cph:'currency':0 }}</div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <instructionRow  [message]="'Click on the expand icon &gt; to open up column groups to see more detail'"></instructionRow>

    <div id="gridHolder">
      <div id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      
      <!-- Top Total -->
      <div id="topTotal">
        {{ constants.translatedText.TotalOf }} {{ constants.pluralise(selections.bonusesTable.totalBonusCount, 'bonus', 'bonuses') }}, 
        {{ constants.translatedText.TotalValue }}: {{ selections.bonusesTable.totalBonusBalance | cph:'currency':0 }}
      </div>
      <!-- The Grid Itself -->
      <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions"> </ag-grid-angular>
            
      <!-- Bottom total for selected rows -->
      <div *ngIf="haveSelectedCells" id="bottomTotal" class="animated slideInRight">
        Selected {{ constants.pluralise(selections.bonusesTable.selectedBonusCount, 'bonus', 'bonuses') }}, 
        total value: {{ selections.bonusesTable.selectedBonusBalance | cph:'currency':0 }}
      </div>
            
    </div>
</div>
  `
  ,
  //styleUrls: ["./../../../styles/components/_agGrid.scss"],
  styles: [
    `
    ag-grid-angular.hidden{opacity:0}
    #topArea{width: 100%;display:flex;justify-content:space-between;}
    #buttonsArea{display:flex;justify-content:space-around;width:100%;margin:0em auto;
    }
    #buttonsArea .btn {padding:0.2em 0.6em;min-width:6em;}
    
    @media (min-width: 0px) and (max-width: 1920px) { #buttonsArea .btn{min-width:5.5em;}}
    .filterChoiceArea .buttonContent{display:flex;flex-direction:column;align-items:center;min-height:3em;justify-content:center;}
    
    #topTotal{position:absolute;top:-2em;right:0em;font-weight:700;display: inline-block;  height:2em;    background: var(--brightColourLight);border-radius: 0.2em;padding:0.5em 1em;display:flex;align-items:center;}
    #bottomTotal{animation-duration:0.2s;position:absolute;bottom:0em;right:0em;font-weight:700;display: inline-block;  height:2em;    background: var(--brightColourLight);border-radius: 0.2em;padding:0.5em 1em;display:flex;align-items:center;}
    #hint{position:absolute;top:-2em;left:0em;}
    #gridHolder{ position:relative;  width: 100%;    height: 100%;margin: 0em auto;margin-top: 2em; }
    ag-grid-angular{width:100%;height:100%;     }
    
     #bonusesContainer {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
  `
  ]
})

export class BonusesTableComponent implements OnInit,OnDestroy {
  private destroy$ = new Subject<void>();
  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  mainTableGridOptions: any;
  haveSelectedCells: boolean;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  
  ngOnInit() {
    this.initialiseBonusesTable();
    this.summariseBonuses(this.selections.debts.bonusesFiltered);
    this.setGridOptions();
    
    this.selections.debts.bonusesDataChangedEmitter
    .pipe(takeUntil(this.destroy$))
    .subscribe(() => {
      if (this.gridApi) {
        this.gridApi.setRowData(this.selections.debts.bonusesFiltered);
        this.summariseBonuses(this.selections.debts.bonusesFiltered);
      }
    })
  }
  ngOnDestroy(): void {
    this.selections.bonusesTable.gridSelections.sortState = this.gridApi.getModel();
    this.destroy$.next();
    this.destroy$.complete();
}

  initialiseBonusesTable() {
    if (!this.selections.bonusesTable) {
      this.selections.bonusesTable = {
        totalBonusBalance: 0,
        totalBonusCount: 0,
        selectedBonusBalance: 0,
        selectedBonusCount: 0,
        buttonSelections: {
          ageing: null,
        },
        gridSelections: {
          filterState: null,
          sortState: null,
        },
        ageings: [
          { description: 'Current', field: 'current', over: -999, under: 1 },
          { description: '1-7 days', field: 'oCurrent', over: 0, under: 8 },
          { description: '8-20 days', field: 'o7', over: 7, under: 21 },
          { description: '21-30 days', field: 'o20', over: 20, under: 31 },
          { description: '31-60 days', field: 'o30', over: 30, under: 61 },
          { description: 'over 60 days', field: 'o60', over: 60, under: 99999999 },
        ],
        departments: []
      }
    }
  }

  summariseBonuses(bonuses: DebtsBonusesSummaryRow[]) {
    //totalbonus
    this.selections.bonusesTable.totalBonusBalance = this.constants.sum(bonuses.map(x => x.Value));
    this.selections.bonusesTable.totalBonusCount = bonuses.length;

    //ageings
    this.selections.bonusesTable.ageings.forEach(ageing => {
      let ageingbonuses: DebtsBonusesSummaryRow[] = bonuses.filter(x => x.Ageing == ageing.description);
      ageing.value = this.constants.sum(ageingbonuses.map(x => x.Value));
    });
  }

  setGridOptions() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params: any) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      suppressPropertyNamesCheck: true,
      context: null,
      suppressMovable: true,
      rowData: this.selections.debts.bonusesFiltered,
      getRowHeight: (params) => {
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onSelectionChanged: (params) => this.onSelectionChanged(),
      onFilterChanged: (params) => this.onFilterChanged(),
      onGridReady: (params) => this.onGridReady(params),
      rowSelection: 'multiple',
      rowDeselection: true,
      animateRows: false,
      defaultColDef: {
        resizable: true,
        sortable: true,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        autoHeight: true
      },
      floatingFilter: true,
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: this.getColumnDefs(),
      onColumnGroupOpened:(params)=>this.gridApi.sizeColumnsToFit(),
    }
  }

  getColumnDefs() {
    let colDefs = [
      { headerName: this.constants.translatedText.Site, field: 'SiteDescription', colId: 'SiteDescription', type: 'labelSetFilter' },
      { headerName: this.constants.translatedText.Description, field: 'Description', colId: 'Description', type: 'labelSetFilter' },
      { headerName: 'Expense', field: 'Expense', colId: 'Expense', type: 'labelSetFilter' },
      { headerName: 'Suffix', field: 'Suffix', colId: 'Suffix', type: 'labelSetFilter' },
      { headerName: 'CustomerRef', field: 'CustomerRef', colId: 'CustomerRef', type: 'label' },
      { headerName: 'Narrative', field: 'Narrative', colId: 'Narrative', type: 'label' },
      { headerName: 'JournalRef', field: 'JournalRef', colId: 'JournalRef', type: 'labelSetFilter' },
      { headerName: 'Co', field: 'Co', colId: 'Co', type: 'labelSetFilter' },
      { headerName: 'Module', field: 'Module', colId: 'Module', type: 'labelSetFilter' },
      {
        headerName: '', children: [
          { headerName: this.constants.translatedText.Customer, field: 'Customer', colId: 'Customer', type: 'labelSetFilter' },
          { headerName: 'SalesPerson', field: 'SalesPerson', colId: 'SalesPerson', type: 'labelSetFilter', columnGroupShow: 'open' },
          { headerName: 'StockNo', field: 'StockNo', colId: 'StockNo', type: 'label', columnGroupShow: 'open' },
          { headerName: 'InvoiceDate', field: 'InvoiceDate', colId: 'InvoiceDate', type: 'dateShort', columnGroupShow: 'open' },
          { headerName: 'PostingDate', field: 'PostingDate', colId: 'PostingDate', type: 'dateShort', columnGroupShow: 'open' },
          { headerName: 'Franchise', field: 'Franchise', colId: 'Franchise', type: 'labelSetFilter', columnGroupShow: 'open' },
          { headerName: 'Ageing', field: 'Ageing', colId: 'Ageing', type: 'labelSetFilter', columnGroupShow: 'open' },
          { headerName: this.constants.translatedText.Age, field: 'Age', colId: 'Age', type: 'number', columnGroupShow: 'open' },
        ]
      },
      { headerName: 'Value', field: 'Value', colId: 'Value', type: 'currency',  }
    ]

    this.gridHelpersService.workoutColWidths(this.selections.debts.bonusesFiltered, colDefs, 10, 8);
    return colDefs;
  }

  filterbonusAgeing(ageing?: BonusAgeing) {

    let filterComponent: IFilterComp = this.gridApi.getFilterInstance('Ageing');
    if (!ageing) {
      filterComponent.setModel(null);
      this.gridApi.onFilterChanged();
      this.selections.bonusesTable.buttonSelections.ageing = null;
      return;
    }
    filterComponent.setModel({
      type: "contains",
      filter: ageing.description
    })
    this.selections.bonusesTable.buttonSelections.ageing = ageing;
    this.gridApi.onFilterChanged();
  }

  onFilterChanged() {
    //store the filter state
    this.selections.bonusesTable.gridSelections.filterState = this.gridApi.getFilterModel();
    //run the summary numbers
    let displayedRows: DebtsBonusesSummaryRow[] = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      displayedRows.push(node.data)
    })
    this.summariseBonuses(displayedRows);
  }

  resizeGrid() {
    // if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };
    this.selections.triggerSpinner.next({ show: false });
  }

  onSelectionChanged() {
    let selectedRows: any[] = this.gridApi.getSelectedRows();
    this.haveSelectedCells = selectedRows.length > 0;
    this.selections.bonusesTable.selectedBonusCount = selectedRows.length;
    this.selections.bonusesTable.selectedBonusBalance = this.constants.sum(selectedRows.map(x => x.Value));


  }

  excelExport() {
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, this.constants.translatedText.Bonuses, 1.5);
  }
}
