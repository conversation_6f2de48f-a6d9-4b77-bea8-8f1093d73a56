import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { GridApi } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { CphPipe } from 'src/app/cph.pipe';
import { FcstForecast, ForecastStatus } from 'src/app/model/liveForecast.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { LiveForecastInputService } from '../liveForecastInput/liveForecastInput.service';
import { LiveForecastReviewService } from '../liveForecastReview/liveForecastReview.service';
import { ApproveForecastButtonComponent } from './cellRenderers/approveForecastButton';
import { ClearForecastButtonComponent } from './cellRenderers/clearForecastButton';
import { RejectForecastButtonComponent } from './cellRenderers/rejectForecastButton';
import { LiveForecastStatusService } from './liveForecastStatus.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';

@Component({
  selector: 'app-liveForecastStatus',
  templateUrl: './liveForecastStatus.component.html',
  styleUrls: ['./liveForecastStatus.component.scss', './../../../styles/components/_agGrid.scss']
})

export class LiveForecastStatusComponent implements OnInit {
  gridOptions: GridOptionsCph;
  gridApi: GridApi;
  updateForecastApprovalStateEmitter: Subscription;
  clearForecastEmitter: Subscription;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getData: GetDataMethodsService,
    public service: LiveForecastStatusService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public inputService: LiveForecastInputService,
    public router: Router,
    public reviewService: LiveForecastReviewService,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService
  ) { }

  ngOnInit(): void {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.allForecastMonths = this.service.getMonthsForPicker();
    this.service.getAllAvailableForecasts();
    this.setGridOptions();

    this.updateForecastApprovalStateEmitter = this.service.updateForecastApprovalState.subscribe((res: number) => {
      if (res == 3) this.service.approve();
      if (res == 4) this.service.reject();
    })

    this.clearForecastEmitter = this.service.clearForecast.subscribe((res: number) => {
      this.service.clear(res);
    })
  }

  ngOnDestroy(): void {
    if (this.updateForecastApprovalStateEmitter) this.updateForecastApprovalStateEmitter.unsubscribe();
  }

  setGridOptions() {
    this.gridOptions = {
      onCellClicked: (params) => {
        this.onCellClick(params, params.data);
      },
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onGridReady: (params) => {
        this.onGridReady(params);
      },
      
      domLayout: 'autoHeight',
      defaultColDef: {
        resizable: true,
        sortable: true,
        autoHeight: true
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: [
        { headerName: 'Site', colId: 'SiteName', field: 'SiteName', width: 100, type: 'label' },
        { headerName: 'Version', colId: 'Version', field: 'Version', width: 75, type: 'label' },
        { headerName: 'Status', colId: 'Status', field: 'Status', width: 50, type: 'label' },
        { headerName: 'Last Saved By', colId: 'LastSavedBy', field: 'LastSavedBy', width: 50, type: 'label' },
        { headerName: 'Last Saved', colId: 'LastSavedDate', field: 'LastSavedDate', width: 50, type: 'dateYearAndTime' },
        { headerName: 'Submitted By', colId: 'Submitter', field: 'Submitter', width: 80, type: 'label' },
        { headerName: 'Last Submitted', colId: 'SubmittedDate', field: 'SubmittedDate', width: 50, type: 'dateYearAndTime' },
        { headerName: 'Net Profit', colId: 'DirectorTotalNet', field: 'DirectorTotalNet', width: 50, type: 'currencyLiveForecast', cellClass: (params) => this.cellClassProviderNetProfit(params.value) },
        { headerName: 'Last Forecast', colId: 'PriorForecastValue', field: 'PriorForecastValue', width: 50, type: 'currency' },
        { headerName: 'vs LF', colId: 'PriorForecastVarianceValue', field: 'PriorForecastVarianceValue', width: 50, type: 'currencyLiveForecast' },
        { headerName: this.service.budgetLabel ? this.service.budgetLabel : 'Budget', colId: 'BudgetValueNet', field: 'BudgetValueNet', width: 50, type: 'currency' },
        { headerName: 'vs Bud', colId: 'BudgetVarianceValue', field: 'BudgetVarianceValue', width: 50, type: 'currencyLiveForecast' },
        { headerName: 'Last Year', colId: 'LastYearValueNet', field: 'LastYearValueNet', width: 50, type: 'currency' },
        { headerName: 'vs LY', colId: 'LastYearVarianceValue', field: 'LastYearVarianceValue', width: 50, type: 'currencyLiveForecast' },
        { headerName: 'Approve', colId: 'Approve', cellRenderer: ApproveForecastButtonComponent, cellRendererParams: (params) => params, width: 35 },
        { headerName: 'Reject', colId: 'Reject', cellRenderer: RejectForecastButtonComponent, cellRendererParams: (params) => params, width: 35 },
        { headerName: 'Clear', colId: 'Clear', cellRenderer: ClearForecastButtonComponent, cellRendererParams: (params) => params, width: 35, hide: this.selections.user.permissions.liveForecast != 'approver' }
      ]
    }
  }

  private cellClassProviderWithColourFont(value: number): string {
    return value < 0 ? 'badFont ag-right-aligned-cell' : 'goodFont ag-right-aligned-cell';
  }

  private cellClassProviderNetProfit(value: number): string {
    return value < 0 ? 'badFont ag-right-aligned-cell bold' : 'ag-right-aligned-cell bold';
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
  }

  onCellClick(params: any, data: ForecastStatus) {
    if (params.colDef.colId != "Approve" && params.colDef.colId != "Reject" && params.colDef.colId != "Clear" && params.data.SiteId != 0) {
      this.inputService.getAllDepartments(params.data.SiteId);

      setTimeout(() => {
        this.inputService.selectedSiteId = data.SiteId;
        this.inputService.selectedSite = data.SiteName;
        this.inputService.selectedMonth = this.service.selectedMonth; 
        this.inputService.selectedForecastId = this.service.selectedForecastId;
        this.inputService.selectedForecastVersionId = data.VersionId;
        this.inputService.selectedForecast = this.service.selectedForecast;
        this.inputService.showBackButton = true;
        this.inputService.isNavigated = true;
        this.router.navigateByUrl('/liveForecastInput');
      }, 100)
    }
  }

  selectForecastMonth(month: string) {
    this.service.selectedMonthsForecasts = this.service.allForecastRows.filter(f => f.Month.split('T')[0] == month.split('T')[0]).sort((a,b) => a.OrderWithinMonth - b.OrderWithinMonth);

    this.service.selectedMonth = month;
    var forecastWithVersion = this.service.selectedMonthsForecasts.filter(s => s.HasForecastVersion == true).sort((a,b) => b.OrderWithinMonth - a.OrderWithinMonth)[0]; //desc
    //console.log('forecastWithVersion',forecastWithVersion);
    
    if (forecastWithVersion != null) {
      //console.log('not null');
       this.selectForecast(forecastWithVersion);
     } else {
      //console.log('null');
      this.selectForecast(this.service.selectedMonthsForecasts[0]);
    }
  }

  selectForecast(forecast: FcstForecast) {
    this.service.selectedForecastId = forecast.Id;
    this.service.selectedForecast = forecast;
    this.service.getForecastStatus();
  }

  refresh(){
    this.service.getForecastStatus();
  }

  goToReview() {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });

    this.reviewService.selectedMonth = this.service.selectedMonth;
    this.reviewService.getForecastReview();

    this.router.navigateByUrl('/liveForecastReview');
  }

  excelExport() {
    let tableModel: any = this.constants.clone(this.gridApi.getModel());

    // Remove last 3 cols - The interaction buttons
    let columnHeaders: any[] = tableModel.columnController.primaryColumns;
    columnHeaders = columnHeaders.splice(columnHeaders.length - 3, 3).pop();
    
    let columnHeaderTree: any[] = tableModel.columnController.primaryColumnTree;
    columnHeaderTree = columnHeaderTree.splice(columnHeaderTree.length - 3, 3).pop();

    this.excel.createSheetObject(tableModel, `Live Forecast Status - ${this.service.selectedForecast.Label}`, 1.3);
  }

  downloadDetailExcelOnClick()
  {
    this.service.getExcelDownloadDetail();
  }
}
