<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      Leaving Trends Change&nbsp;
      <sourceDataUpdate *ngIf="!constants.environment.showLatestSnapshotDate"
                        [chosenDate]="constants.LastUpdatedDates.AutotraderAdverts"></sourceDataUpdate>
    </h4>

    <!-- Picker buttons -->
    <div class="dashboard-grid" [ngStyle]="stylePickerButtons()">
      <!-- Was date picker -->
      <div class="d-flex align-items-center grid-col-1-3">
        Was from:
        <div class="buttonGroup">
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button (click)="makeMonthsDropdownOnClick()" class="btn btn-primary centreButton"
                    ngbDropdownToggle>{{ getMonthName(service.startDateWas) }}
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
              <button *ngFor="let month of months" (click)="selectMonthOnClickWas(month, true)"
                      ngbDropdownItem>{{ getMonthName(month) }}
              </button>
            </div>
          </div>
        </div>
        To:
        <div class="buttonGroup">
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button (click)="makeMonthsDropdownOnClick()" class="btn btn-primary centreButton"
                    ngbDropdownToggle>{{ getMonthName(service.endDateWas) }}
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
              <button *ngFor="let month of months" (click)="selectMonthOnClickWas(month)"
                      ngbDropdownItem>{{ getMonthName(month) }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Now date picker -->
      <div class="d-flex align-items-center grid-col-3-5">
        Now from:
        <div class="buttonGroup">
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button (click)="makeMonthsDropdownOnClick()" class="btn btn-primary centreButton"
                    ngbDropdownToggle>{{ getMonthName(service.startDateNow) }}
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
              <button *ngFor="let month of months" (click)="selectMonthOnClickNow(month, true)"
                      ngbDropdownItem>{{ getMonthName(month) }}
              </button>
            </div>
          </div>
        </div>
        To:
        <div class="buttonGroup">
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button (click)="makeMonthsDropdownOnClick()" class="btn btn-primary centreButton"
                    ngbDropdownToggle>{{ getMonthName(service.endDateNow) }}
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
              <button *ngFor="let month of months" (click)="selectMonthOnClickNow(month)"
                      ngbDropdownItem>{{ getMonthName(month) }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">
    <div class="content-inner-new">

      <div id="leftScrollable">
        <div class="dashboard-grid">
          <!-- Total -->
          <div class="dashboard-tile grid-col-1-3 grid-row-1-3">
            <div class="dashboard-tile-inner" *ngIf="service.rawDataHighlighted">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  Total Vehicles
                </div>
              </div>
              <div class="dashboard-tile-body">
                <h1 class="bigNumber clickable" id="totalCount" (click)="goToLeavingVehiclesAnalysis()">
                  <strong>{{ service.rawDataHighlighted.length | cph:'number':0 }}</strong>
                </h1>
              </div>
            </div>
          </div>

          <!-- Region -->
          <div class="dashboard-tile grid-col-3-5 grid-row-1-3">
            <biChartTile *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'Region'"
                         [tileType]="'VerticalBar'" [labelWidth]="50" [pageParams]="service.getPageParams()"
                         [fieldName]="'Region'">
            </biChartTile>
          </div>

          <!-- Retailer Site Name -->
          <div class="dashboard-tile grid-col-1-5 grid-row-3-5">
            <biChartTile *ngIf="service.rawDataNow" [dataType]="dataTypes.label"
                         [title]="'Retailer Site Name'" [tileType]="'VerticalBar'" [labelWidth]="60"
                         [pageParams]="service.getPageParams()" [fieldName]="'RetailerSiteName'">
            </biChartTile>
          </div>

          <!-- RegYear -->
          <div class="dashboard-tile grid-col-1-3 grid-row-5-7">
            <biChartTile *ngIf="service.rawDataNow" [dataType]="dataTypes.label"
                         [title]="'Registration Year'" [tileType]="'VerticalBar'" [labelWidth]="30"
                         [pageParams]="service.getPageParams()" [fieldName]="'RegYear'" [customSort]="'RegYear'">
            </biChartTile>
          </div>

          <!-- BodyType -->
          <div class="dashboard-tile grid-col-3-5 grid-row-5-7">
            <biChartTile
              *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'Body Type'"
              [customSort]="'HighToLow'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'BodyType'">
            </biChartTile>
          </div>

          <!-- FuelType -->
          <div class="dashboard-tile grid-col-1-3 grid-row-7-9">
            <biChartTile
              *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'Fuel Type'"
              [customSort]="'HighToLow'"
              [tileType]="'VerticalBar'" [labelWidth]="40" [pageParams]="service.getPageParams()"
              [fieldName]="'FuelType'">
            </biChartTile>
          </div>

          <!-- TransmissionType -->
          <div class="dashboard-tile grid-col-3-5 grid-row-7-9">
            <biChartTile
              *ngIf="service.rawDataNow" [dataType]="dataTypes.label"
              [customSort]="'HighToLow'"
              [title]="'TransmissionType'" [tileType]="'VerticalBar'" [labelWidth]="30"
              [pageParams]="service.getPageParams()" [fieldName]="'TransmissionType'">
            </biChartTile>
          </div>

          <!-- MileageBand -->
          <div class="dashboard-tile grid-col-1-3 grid-row-9-11">
            <biChartTile
              *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'Mileage'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'MileageBand'" [customSort]="'Mileage'">
            </biChartTile>
          </div>

          <!-- LastPriceBand -->
          <div class="dashboard-tile grid-col-3-5 grid-row-9-11">
            <biChartTile
              *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'Price Band'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'LastPriceBand'" [customSort]="'LastPriceBand'">
            </biChartTile>
          </div>

          <!-- Make -->
          <div class="dashboard-tile grid-col-1-3 grid-row-11-13">
            <biChartTile
              *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'Make'"
              [customSort]="'HighToLow'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'Make'">
            </biChartTile>
          </div>

          <!-- Model -->
          <div class="dashboard-tile grid-col-3-5 grid-row-11-15">
            <biChartTile
              *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'Model'"
              [customSort]="'HighToLow'"
              [tileType]="'VerticalBar'" [labelWidth]="50" [pageParams]="service.getPageParams()"
              [fieldName]="'Model'">
            </biChartTile>
          </div>

          <!-- AchievedSaleType -->
          <!-- <div class="dashboard-tile grid-col-1-3 grid-row-13-15">
            <biChartTile
              [customSort]="'HighToLow'"
              *ngIf="service.rawDataNow" [dataType]="dataTypes.label"
              [title]="'Achieved Sale Type'" [tileType]="'VerticalBar'" [labelWidth]="30"
              [pageParams]="service.getPageParams()" [fieldName]="'AchievedSaleType'">
            </biChartTile>
          </div> -->

           <!-- IsOnStrategy -->
           <div class="dashboard-tile grid-col-1-3 grid-row-13-15">
            <biChartTile *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'On Strategy'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'IsOnStrategy'" >
            </biChartTile>
          </div>
           <!-- OptedOutPctBand -->
           <div class="dashboard-tile grid-col-1-3 grid-row-17-19">
            <biChartTile *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="'OptedOutPctBand'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'OptedOutPctBand'" >
            </biChartTile>
          </div>

          <!-- FirstPPBand -->
          <div class="dashboard-tile grid-col-1-3 grid-row-15-17">
            <biChartTile *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="firstPPTitle()"
                         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
                         [fieldName]="'FirstPPBand'" [customSort]="'PPBand'">
            </biChartTile>
          </div>

          <!-- LastPPBand -->
          <div class="dashboard-tile grid-col-3-5 grid-row-15-17">
            <biChartTile *ngIf="service.rawDataNow" [dataType]="dataTypes.label" [title]="lastPPTitle()"
                         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
                         [fieldName]="'LastPPBand'" [customSort]="'PPBand'">
            </biChartTile>
          </div>
        </div>
      </div>

      <div id="rightScrollable">
        <div class="dashboard-grid cols-6">
          <!-- #### WAS ####-->

          <!-- Sold volume by retail rating -->
          <div class="dashboard-tile grid-col-1-3 grid-row-1-3">
            <ng-container *ngIf="service.smallChartSetDataWas">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetDataWas.rrSoldVolume.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrSoldVolume'"
                          [newDataEmitter]="service.newSmallChartDataEmitterWas"
                          [params]="service.smallChartSetDataWas.rrSoldVolume"
                          [doesFilter]="true"
                          [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                          [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"
                          [showPercentageOfTotal]="true">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- Days to sell by Retail Rating -->
          <div class="dashboard-tile grid-col-1-3 grid-row-3-5">
            <ng-container *ngIf="service.smallChartSetDataWas">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetDataWas.rrDaysListed.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrDaysListed'"
                          [newDataEmitter]="service.newSmallChartDataEmitterWas"
                          [params]="service.smallChartSetDataWas.rrDaysListed">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- First price position -->
          <div class="dashboard-tile grid-col-1-3 grid-row-5-7">
            <ng-container *ngIf="service.smallChartSetDataWas">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetDataWas.rrFirstPP.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrFirstPP'" [newDataEmitter]="service.newSmallChartDataEmitterWas"
                          [params]="service.smallChartSetDataWas.rrFirstPP">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- Final PP -->
          <div class="dashboard-tile grid-col-1-3 grid-row-7-9">
            <ng-container *ngIf="service.smallChartSetDataWas">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetDataWas.rrLastPP.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrLastPP'" [newDataEmitter]="service.newSmallChartDataEmitterWas"
                          [params]="service.smallChartSetDataWas.rrLastPP">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- PP change -->
          <div class="dashboard-tile grid-col-1-3 grid-row-9-11">
            <ng-container *ngIf="service.smallChartSetDataWas">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetDataWas.rrChangedPP.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrChangedPP'"
                          [newDataEmitter]="service.newSmallChartDataEmitterWas"
                          [params]="service.smallChartSetDataWas.rrChangedPP">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- Sold volume by Days Listed -->
          <div class="dashboard-tile grid-col-1-3 grid-row-11-13">
            <ng-container *ngIf="service.smallChartSetDataWas">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetDataWas.dlSoldVolume.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'dlSoldVolume'"
                          [newDataEmitter]="service.newSmallChartDataEmitterWas"
                          [params]="service.smallChartSetDataWas.dlSoldVolume" [doesFilter]="true"
                          [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                          [tileType]="'HorizontalBar'" [fieldName]="'DaysListedBand'" [showPercentageOfTotal]="true">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- Days listed final PP -->
          <div class="dashboard-tile grid-col-1-3 grid-row-13-15">
            <ng-container *ngIf="service.smallChartSetDataWas">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetDataWas.dlLastPP.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'dlLastPP'" [newDataEmitter]="service.newSmallChartDataEmitterWas"
                          [params]="service.smallChartSetDataWas.dlLastPP">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- #### NOW ####-->

          <!-- Sold volume by retail rating -->
          <div class="dashboard-tile grid-col-3-5 grid-row-1-3">
            <ng-container *ngIf="service.smallChartSetData">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetData.rrSoldVolume.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrSoldVolume'" [newDataEmitter]="service.newSmallChartDataEmitter"
                          [params]="service.smallChartSetData.rrSoldVolume" [doesFilter]="true"
                          [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                          [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'" [showPercentageOfTotal]="true">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- Days to sell by Retail Rating -->
          <div class="dashboard-tile grid-col-3-5 grid-row-3-5">
            <ng-container *ngIf="service.smallChartSetData">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetData.rrDaysListed.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrDaysListed'" [newDataEmitter]="service.newSmallChartDataEmitter"
                          [params]="service.smallChartSetData.rrDaysListed">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- First price position -->
          <div class="dashboard-tile grid-col-3-5 grid-row-5-7">
            <ng-container *ngIf="service.smallChartSetData">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetData.rrFirstPP.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrFirstPP'" [newDataEmitter]="service.newSmallChartDataEmitter"
                          [params]="service.smallChartSetData.rrFirstPP">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- Final PP -->
          <div class="dashboard-tile grid-col-3-5 grid-row-7-9">
            <ng-container *ngIf="service.smallChartSetData">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetData.rrLastPP.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrLastPP'" [newDataEmitter]="service.newSmallChartDataEmitter"
                          [params]="service.smallChartSetData.rrLastPP">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- PP change -->
          <div class="dashboard-tile grid-col-3-5 grid-row-9-11">
            <ng-container *ngIf="service.smallChartSetData">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetData.rrChangedPP.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'rrChangedPP'" [newDataEmitter]="service.newSmallChartDataEmitter"
                          [params]="service.smallChartSetData.rrChangedPP">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- Sold volume by Days Listed -->
          <div class="dashboard-tile grid-col-3-5 grid-row-11-13">
            <ng-container *ngIf="service.smallChartSetData">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetData.dlSoldVolume.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'dlSoldVolume'" [newDataEmitter]="service.newSmallChartDataEmitter"
                          [params]="service.smallChartSetData.dlSoldVolume" [doesFilter]="true"
                          [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                          [tileType]="'HorizontalBar'" [fieldName]="'DaysListedBand'" [showPercentageOfTotal]="true">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- Days listed final PP -->
          <div class="dashboard-tile grid-col-3-5 grid-row-13-15">
            <ng-container *ngIf="service.smallChartSetData">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  {{ service.smallChartSetData.dlLastPP.Title }}
                </div>
              </div>
              <div class="contentsHolder">
                <barChart [dataKey]="'dlLastPP'" [newDataEmitter]="service.newSmallChartDataEmitter"
                          [params]="service.smallChartSetData.dlLastPP">
                </barChart>
              </div>
            </ng-container>
          </div>

          <!-- #### VARIANCE ####-->

          <!-- Sold volume by retail rating -->
          <div class="dashboard-tile grid-col-5-7 grid-row-1-3">
            <ng-container *ngIf="service.smallChartSetDataVariance">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  Volume change: {{ service.smallChartSetDataVariance.rrSoldVolume.change | cph:'number':0:true }}
                </div>
              </div>
              <div class="contentsHolder">
                <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.rrSoldVolume"
                                          [pipeType]="'number'"
                                          [showPercentageDifference]="true"></app-chartVarianceSummary>
              </div>
            </ng-container>
          </div>

          <!-- Days to sell by Retail Rating -->
          <div class="dashboard-tile grid-col-5-7 grid-row-3-5">
            <ng-container *ngIf="service.smallChartSetDataVariance">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  Days listed change : {{
                    service.smallChartSetDataVariance.rrDaysListed.change |
                      cph:'number':0:true
                  }}
                </div>
              </div>
              <div class="contentsHolder">
                <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.rrDaysListed"
                                          [pipeType]="'number'" [prefixIfPositive]="true"></app-chartVarianceSummary>
              </div>
            </ng-container>
          </div>

          <!-- First price position -->
          <div class="dashboard-tile grid-col-5-7 grid-row-5-7">
            <ng-container *ngIf="service.smallChartSetDataVariance">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  First price position change : {{
                    service.smallChartSetDataVariance.rrFirstPP.change
                      | cph:'percent':1:true
                  }}
                </div>
              </div>
              <div class="contentsHolder">
                <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.rrFirstPP"
                                          [pipeType]="'percent'" [prefixIfPositive]="true"></app-chartVarianceSummary>
              </div>
            </ng-container>
          </div>

          <!-- Final PP -->
          <div class="dashboard-tile grid-col-5-7 grid-row-7-9">
            <ng-container *ngIf="service.smallChartSetDataVariance">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  Final price position change : {{
                    service.smallChartSetDataVariance.rrLastPP.change |
                      cph:'percent':1:true
                  }}
                </div>
              </div>
              <div class="contentsHolder">
                <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.rrLastPP"
                                          [pipeType]="'percent'"></app-chartVarianceSummary>
              </div>
            </ng-container>
          </div>

          <!-- PP change -->
          <div class="dashboard-tile grid-col-5-7 grid-row-9-11">
            <ng-container *ngIf="service.smallChartSetDataVariance">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  Average price position change : {{
                    service.smallChartSetDataVariance.rrChangedPP.change | cph:'percent':1:true
                  }}
                </div>
              </div>
              <div class="contentsHolder">
                <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.rrChangedPP"
                                          [pipeType]="'percent'"></app-chartVarianceSummary>
              </div>
            </ng-container>
          </div>

          <!-- Sold volume by Days Listed -->
          <div class="dashboard-tile grid-col-5-7 grid-row-11-13">
            <ng-container *ngIf="service.smallChartSetDataVariance">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  Average days change : {{
                    service.smallChartSetDataVariance.dlSoldVolume.change |
                      cph:'number':0:true
                  }}
                </div>
              </div>
              <div class="contentsHolder">
                <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.dlSoldVolume"
                                          [pipeType]="'number'"
                                          [showPercentageDifference]="true"></app-chartVarianceSummary>
              </div>
            </ng-container>
          </div>

          <!-- Days listed final PP -->
          <div class="dashboard-tile grid-col-5-7 grid-row-13-15">
            <ng-container *ngIf="service.smallChartSetDataVariance">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  Final price position change : {{
                    service.smallChartSetDataVariance.dlLastPP.change | cph:'percent':1:true
                  }}
                </div>
              </div>
              <div class="contentsHolder">
                <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.dlLastPP"
                                          [pipeType]="'percent'"></app-chartVarianceSummary>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
