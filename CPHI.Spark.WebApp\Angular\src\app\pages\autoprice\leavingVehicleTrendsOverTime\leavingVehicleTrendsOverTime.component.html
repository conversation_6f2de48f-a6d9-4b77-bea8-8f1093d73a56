<nav class="navbar">
   <nav class="generic">
      <h4 id="pageTitle">
         Leaving Trends Change&nbsp;
         <sourceDataUpdate *ngIf="!constants.environment.showLatestSnapshotDate"
                           [chosenDate]="constants.LastUpdatedDates.AutotraderAdverts"></sourceDataUpdate>

      </h4>

      <sliderSwitch text="Sync Dates"
                    (toggle)="service.toggleSyncPeriods()"
                    [defaultValue]="service.syncPeriods"></sliderSwitch>

      <sliderSwitch
         text="Sync Selections" (toggle)="service.toggleSyncSelections()"
         [defaultValue]="service.syncSelections"></sliderSwitch>
   </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer" style="padding: 1em">
   <div style="width: 100%">
      <div class="" style="display: flex; flex-direction: row; grid-gap: 5px; width: 100%;">

         <div class="filter-column">
            <ng-container *ngTemplateOutlet="horizontalBars; context: { wasNow: wasNowEnum.Was }"></ng-container>
         </div>

         <!-- #### WAS ####-->
         <div class="bars-column">
            <!-- Sold volume by retail rating -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Was]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Was].rrSoldVolume.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrSoldVolume'"
                        [wasNow]="wasNowEnum.Was"
                        [newDataEmitter]="service.newSmallChartDataEmitterWas"
                        [params]="service.smallChartSetData[wasNowEnum.Was].rrSoldVolume"
                        [yMax]="service.smallChartSetData[wasNowEnum.Was].rrSoldVolume.yMax"
                        [doesFilter]="true"
                        [pageParams]="service.getPageParams(wasNowEnum.Was)" [dataType]="dataTypes.label"
                        [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"
                        [showPercentageOfTotal]="true">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- Days to sell by Retail Rating -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Was]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Was].rrDaysListed.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrDaysListed'"
                        [wasNow]="wasNowEnum.Was"
                        [yMax]="service.smallChartSetData[wasNowEnum.Was].rrDaysListed.yMax"
                        [newDataEmitter]="service.newSmallChartDataEmitterWas"
                        [params]="service.smallChartSetData[wasNowEnum.Was].rrDaysListed">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- First price position -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Was]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Was].rrFirstPP.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrFirstPP'" [newDataEmitter]="service.newSmallChartDataEmitterWas"
                        [wasNow]="wasNowEnum.Was"
                        [yMax]="service.smallChartSetData[wasNowEnum.Was].rrFirstPP.yMax"
                        [params]="service.smallChartSetData[wasNowEnum.Was].rrFirstPP">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- Final PP -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Was]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Was].rrLastPP.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrLastPP'" [newDataEmitter]="service.newSmallChartDataEmitterWas"
                        [wasNow]="wasNowEnum.Was"
                        [yMax]="service.smallChartSetData[wasNowEnum.Was].rrLastPP.yMax"
                        [params]="service.smallChartSetData[wasNowEnum.Was].rrLastPP">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- PP change -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Was]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Was].rrChangedPP.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrChangedPP'"
                        [wasNow]="wasNowEnum.Was"
                        [newDataEmitter]="service.newSmallChartDataEmitterWas"
                        [yMax]="service.smallChartSetData[wasNowEnum.Was].rrChangedPP.yMax"
                        [params]="service.smallChartSetData[wasNowEnum.Was].rrChangedPP">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- Sold volume by Days Listed -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Was]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Was].dlSoldVolume.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'dlSoldVolume'"
                        [wasNow]="wasNowEnum.Was"
                        [newDataEmitter]="service.newSmallChartDataEmitterWas"
                        [params]="service.smallChartSetData[wasNowEnum.Was].dlSoldVolume"
                        [doesFilter]="true"
                        [pageParams]="service.getPageParams(wasNowEnum.Was)"
                        [yMax]="service.smallChartSetData[wasNowEnum.Was].dlSoldVolume.yMax"
                        [dataType]="dataTypes.label"
                        [tileType]="'HorizontalBar'" [fieldName]="'DaysListedBand'"
                        [showPercentageOfTotal]="true">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- Days listed final PP -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Was]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Was].dlLastPP.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart [dataKey]="'dlLastPP'" [newDataEmitter]="service.newSmallChartDataEmitterWas"
                               [wasNow]="wasNowEnum.Was"
                               [params]="service.smallChartSetData[wasNowEnum.Was].dlLastPP">
                     </barChart>
                  </div>
               </ng-container>
            </div>
         </div>

         <div class="filter-column">
            <ng-container
               *ngTemplateOutlet="horizontalBars; context: { wasNow: wasNowEnum.Now, disabled: service.syncSelections }"></ng-container>
         </div>

         <div class="bars-column">
            <!-- #### NOW ####-->
            <!-- Sold volume by retail rating -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Now]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Now].rrSoldVolume.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrSoldVolume'"
                        [wasNow]="wasNowEnum.Now"
                        [newDataEmitter]="service.newSmallChartDataEmitterNow"
                        [params]="service.smallChartSetData[wasNowEnum.Now].rrSoldVolume"
                        [yMax]="service.smallChartSetData[wasNowEnum.Now].rrSoldVolume.yMax"
                        [doesFilter]="true"
                        [pageParams]="service.getPageParams(wasNowEnum.Now)"
                        [dataType]="dataTypes.label"
                        [tileType]="'HorizontalBar'"
                        [fieldName]="'RetailRatingBand'"
                        [showPercentageOfTotal]="true">
                     </barChart>

                  </div>
               </ng-container>
            </div>

            <!-- Days to sell by Retail Rating -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Now]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Now]?.rrDaysListed.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrDaysListed'"
                        [newDataEmitter]="service.newSmallChartDataEmitterNow"
                        [wasNow]="wasNowEnum.Now"
                        [yMax]="service.smallChartSetData[wasNowEnum.Now].rrDaysListed.yMax"
                        [params]="service.smallChartSetData[wasNowEnum.Now]?.rrDaysListed">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- First price position -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Now]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Now]?.rrFirstPP.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrFirstPP'"
                        [newDataEmitter]="service.newSmallChartDataEmitterNow"
                        [wasNow]="wasNowEnum.Now"
                        [yMax]="service.smallChartSetData[wasNowEnum.Now].rrFirstPP.yMax"
                        [params]="service.smallChartSetData[wasNowEnum.Now]?.rrFirstPP">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- Final PP -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Now]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Now]?.rrLastPP.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrLastPP'" [newDataEmitter]="service.newSmallChartDataEmitterNow"
                        [wasNow]="wasNowEnum.Now"
                        [yMax]="service.smallChartSetData[wasNowEnum.Now].rrLastPP.yMax"
                        [params]="service.smallChartSetData[wasNowEnum.Now]?.rrLastPP">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- PP change -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Now]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Now]?.rrChangedPP.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'rrChangedPP'"
                        [newDataEmitter]="service.newSmallChartDataEmitterNow"
                        [wasNow]="wasNowEnum.Now"
                        [yMax]="service.smallChartSetData[wasNowEnum.Now].rrChangedPP.yMax"
                        [params]="service.smallChartSetData[wasNowEnum.Now]?.rrChangedPP">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- Sold volume by Days Listed -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Now]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Now]?.dlSoldVolume.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'dlSoldVolume'"
                        [newDataEmitter]="service.newSmallChartDataEmitterNow"
                        [wasNow]="wasNowEnum.Now"
                        [params]="service.smallChartSetData[wasNowEnum.Now]?.dlSoldVolume"
                        [yMax]="service.smallChartSetData[wasNowEnum.Now].dlSoldVolume.yMax"
                        [doesFilter]="true"
                        [pageParams]="service.getPageParams(wasNowEnum.Now)" [dataType]="dataTypes.label"
                        [tileType]="'HorizontalBar'"
                        [fieldName]="'DaysListedBand'"
                        [showPercentageOfTotal]="true">
                     </barChart>
                  </div>
               </ng-container>
            </div>

            <!-- Days listed final PP -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetData[wasNowEnum.Now]">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        {{ service.smallChartSetData[wasNowEnum.Now]?.dlLastPP.Title }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <barChart
                        [dataKey]="'dlLastPP'"
                        [newDataEmitter]="service.newSmallChartDataEmitterNow"
                        [wasNow]="wasNowEnum.Now"
                        [yMax]="service.smallChartSetData[wasNowEnum.Now].dlLastPP.yMax"
                        [params]="service.smallChartSetData[wasNowEnum.Now]?.dlLastPP">
                     </barChart>
                  </div>
               </ng-container>
            </div>
         </div>
         <div class="bars-column">
            <!-- #### VARIANCE ####-->

            <!-- Sold volume by retail rating -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetDataVariance">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        Volume
                        change: {{ service.smallChartSetDataVariance.rrSoldVolume.change | cph:'number':0:true }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.rrSoldVolume"
                                               [pipeType]="'number'"
                                               [showPercentageDifference]="true"></app-chartVarianceSummary>
                  </div>
               </ng-container>
            </div>

            <!-- Days to sell by Retail Rating -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetDataVariance">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        Days listed change : {{
                           service.smallChartSetDataVariance.rrDaysListed.change |
                              cph:'number':0:true
                        }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <app-chartVarianceSummary
                        [params]="service.smallChartSetDataVariance.rrDaysListed"
                        [pipeType]="'number'">
                     </app-chartVarianceSummary>
                  </div>
               </ng-container>
            </div>

            <!-- First price position -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetDataVariance">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        First price position change : {{
                           service.smallChartSetDataVariance.rrFirstPP.change
                              | cph:'percent':1:true
                        }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <app-chartVarianceSummary
                        [params]="service.smallChartSetDataVariance.rrFirstPP"
                        [pipeType]="'percent'">
                     </app-chartVarianceSummary>
                  </div>
               </ng-container>
            </div>

            <!-- Final PP -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetDataVariance">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        Final price position change : {{
                           service.smallChartSetDataVariance.rrLastPP.change |
                              cph:'percent':1:true
                        }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.rrLastPP"
                                               [pipeType]="'percent'"></app-chartVarianceSummary>
                  </div>
               </ng-container>
            </div>

            <!-- PP change -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetDataVariance">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        Average price position change : {{
                           service.smallChartSetDataVariance.rrChangedPP.change | cph:'percent':1:true
                        }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.rrChangedPP"
                                               [pipeType]="'percent'"></app-chartVarianceSummary>
                  </div>
               </ng-container>
            </div>

            <!-- Sold volume by Days Listed -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetDataVariance">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        Average days change : {{
                           service.smallChartSetDataVariance.dlSoldVolume.change |
                              cph:'number':0:true
                        }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.dlSoldVolume"
                                               [pipeType]="'number'"
                                               [showPercentageDifference]="true"></app-chartVarianceSummary>
                  </div>
               </ng-container>
            </div>

            <!-- Days listed final PP -->
            <div class="bar-container">
               <ng-container *ngIf="service.smallChartSetDataVariance">
                  <div class="tileHeader">
                     <div class="h4 headerWords">
                        Final price position change : {{
                           service.smallChartSetDataVariance.dlLastPP.change | cph:'percent':1:true
                        }}
                     </div>
                  </div>
                  <div class="contentsHolder">
                     <app-chartVarianceSummary [params]="service.smallChartSetDataVariance.dlLastPP"
                                               [pipeType]="'percent'"></app-chartVarianceSummary>
                  </div>
               </ng-container>
            </div>
         </div>
      </div>
   </div>
</div>


<ng-template #horizontalBars let-wasNow="wasNow" let-disabled="disabled">

   <!-- Total -->
   <div class="filter-tile">
      <div *ngIf="service.rawDataHighlighted[wasNow]">
         <div class="tileHeader">
            <div class="d-flex">
               <div class="h4 headerWords">
                  Total Vehicles
               </div>
            </div>
         </div>
         <div class="">
            <h1 class="bigNumber clickable" id="totalCount" (click)="goToLeavingVehiclesAnalysis(wasNow)">
               <strong>{{ service.rawDataHighlighted[wasNow]?.length | cph:'number':0 }}</strong>
            </h1>
         </div>
      </div>
   </div>

   <div class="filter-tile">

      <div class="d-flex">
         <div class="flex-grow-1">
            <h4>
               <div class="mt-1">Date Range</div>
            </h4>
         </div>
      </div>

      <div class="d-flex mt-1">
         <div>
            <input class="date-input" type="date" name="fromDate" [ngModel]="displayStartDate(wasNow)" [min]="minDate"
                   [max]="maxDate"
                   (ngModelChange)="setDate($event, true, wasNow)">
         </div>
         <div style="padding-left: 0.5em; padding-right: 0.5em; padding-top: 0.5em">-</div>
         <div>
            <input class="date-input" type="date" name="toDate" [ngModel]="displayEndDate(wasNow)" [min]="minDate"
                   [max]="maxDate"
                   (ngModelChange)="setDate($event, false, wasNow)">
         </div>
      </div>
   </div>
   <!-- Region -->
   <div class="filter-tile">

      <biChartTile
         *ngIf="service.rawData[wasNow]"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [dataType]="dataTypes.label" [title]="'Region'"
         [tileType]="'VerticalBar'" [labelWidth]="50"
         [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'Region'">
      </biChartTile>
   </div>

   <!-- Retailer Site Name -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]"
         [dataType]="dataTypes.label"
         [wasNow]="wasNow"
         [title]="'Retailer Site Name'"
         [tileType]="'VerticalBar'"
         [labelWidth]="60"
         [disabled]="disabled"
         [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'RetailerSiteName'">
      </biChartTile>
   </div>

   <!-- RegYear -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [title]="'Registration Year'" [tileType]="'VerticalBar'" [labelWidth]="30"
         [pageParams]="service.getPageParams(wasNow)" [fieldName]="'RegYear'" [customSort]="'RegYear'">
      </biChartTile>
   </div>

   <!-- BodyType -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="'Body Type'"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [customSort]="'HighToLow'"
         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'BodyType'">
      </biChartTile>
   </div>

   <!-- FuelType -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="'Fuel Type'"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [customSort]="'HighToLow'"
         [tileType]="'VerticalBar'" [labelWidth]="40" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'FuelType'">
      </biChartTile>
   </div>

   <!-- TransmissionType -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [customSort]="'HighToLow'"
         [title]="'TransmissionType'" [tileType]="'VerticalBar'" [labelWidth]="30"
         [pageParams]="service.getPageParams(wasNow)" [fieldName]="'TransmissionType'">
      </biChartTile>
   </div>

   <!-- MileageBand -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="'Mileage'"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'MileageBand'" [customSort]="'Mileage'">
      </biChartTile>
   </div>

   <!-- LastPriceBand -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="'Price Band'"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'LastPriceBand'" [customSort]="'LastPriceBand'">
      </biChartTile>
   </div>

   <!-- Make -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="'Make'"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [customSort]="'HighToLow'"
         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'Make'">
      </biChartTile>
   </div>

   <!-- Model -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="'Model'"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [customSort]="'HighToLow'"
         [tileType]="'VerticalBar'" [labelWidth]="50" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'Model'">
      </biChartTile>
   </div>

   <!-- IsOnStrategy -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="'On Strategy'"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'IsOnStrategy'">
      </biChartTile>
   </div>
   <!-- OptedOutPctBand -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="'OptedOutPctBand'"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'OptedOutPctBand'">
      </biChartTile>
   </div>

   <!-- FirstPPBand -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="firstPPTitle(wasNow)"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'FirstPPBand'" [customSort]="'PPBand'">
      </biChartTile>
   </div>

   <!-- LastPPBand -->
   <div class="filter-tile">
      <biChartTile
         *ngIf="service.rawData[wasNow]" [dataType]="dataTypes.label" [title]="lastPPTitle(wasNow)"
         [wasNow]="wasNow"
         [disabled]="disabled"
         [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams(wasNow)"
         [fieldName]="'LastPPBand'" [customSort]="'PPBand'">
      </biChartTile>
   </div>
</ng-template>
