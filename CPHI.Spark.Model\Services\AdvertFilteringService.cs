﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Model.AutoPrice
{
    public static class AdvertFilteringService
    {


        public static List<VehicleAdvertWithRating> FilterAdverts(IEnumerable<VehicleAdvertWithRating> adverts, 
            GetVehicleAdvertsWithRatingsParams parms, List<int> retailerSiteIds, List<int> siteIds)
        {


            adverts = adverts
               .Where(x => parms.Reg == null || x.VehicleReg == parms.Reg)
               .Where(x => parms.Vin == null || x.Chassis == parms.Vin)
               .Where(x => retailerSiteIds.Contains(x.RetailerSiteId))
               .Where(x => siteIds.Contains(x.SiteId))
               .Where(x => parms.VehicleTypes == null || parms.VehicleTypes.Contains(x.VehicleTypeDesc)) // Updated line
               .Where(x => parms.IncludeNewVehicles || x.OwnershipCondition != "New")
               .Where(x => parms.IncludeUnPublishedAdverts || x.AutotraderAdvertStatus == "PUBLISHED")
               .Where(x => parms.IncludeLCV || x.VehicleType != "Van");

            return adverts.ToList();
        }

    }



        

}
