
import { Injectable } from '@angular/core';
import { SheetToExtract } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { Product } from 'src/app/services/environment.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { SelectionsService } from 'src/app/services/selections.service';
import { OrderbookRow } from "../../model/OrderbookRow";
import { OrderBookService } from './orderBook.service';







@Injectable({
  providedIn: 'root'
})
export class OrderBookExcelExportService {

  


  constructor(
    public constants: ConstantsService,
    private selections: SelectionsService,
    private excel: ExcelExportService,
    private service: OrderBookService
  ) {
  }




  excelExport() {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_GeneratingExcel })

    setTimeout(() => {

      //get tableModel from ag-grid
      let tableModel = this.service.tableLayoutManagementParams.gridApi.getModel()

      //don't save just return object
      let sheetDataToExtract: SheetToExtract[] = this.excel.createSheetObject(tableModel, 'OrderBook', 1.8, 1, true);


      //update the index on the first col
      tableModel.forEachNode((row, i) => {
        if (sheetDataToExtract[0].rows[i]) {
          sheetDataToExtract[0].rows[i][0] = i + 1;
        }
      })

      let addOnsIndexPosition: number;

      // Ensure certain columns have the correct column type
      for (let i = 0; i < sheetDataToExtract[0].headers[0].length; i++) {

        if (sheetDataToExtract[0].headers[0][i] == 'Add-Ons') {
          addOnsIndexPosition = i;
          sheetDataToExtract[0].headers[0][i] = 'Add-Ons Count';
          sheetDataToExtract[0].headers[0][i + 1] = 'Add-Ons Profit';
          sheetDataToExtract[0].columnTypes[i] = 'number';
          sheetDataToExtract[0].columnWidths[i] = 10.8;
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Orderbook_Del)
        {
          sheetDataToExtract[0].columnTypes[i] = 'dateShortYear';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Orderbook_InvoiceDateAbbreviation)
        {
          sheetDataToExtract[0].columnTypes[i] = 'dateShortYear';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Common_AddOnProfit)
        {
          sheetDataToExtract[0].columnTypes[i] = 'currencyWithFontColour';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Common_Profit)
        {
          sheetDataToExtract[0].columnTypes[i] = 'currencyWithFontColour';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Orderbook_Q)
        {
          sheetDataToExtract[0].columnTypes[i] = 'number';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Orderbook_OrderTypeCodeAbbreviation)
        {
          sheetDataToExtract[0].columnTypes[i] = 'label';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Orderbook_SourceAbbreviation)
        {
          sheetDataToExtract[0].columnTypes[i] = 'label';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Common_Description)
        {
          sheetDataToExtract[0].columnTypes[i] = 'label';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Common_SalesExec)
        {
          sheetDataToExtract[0].columnTypes[i] = 'label';
        }
        else if(sheetDataToExtract[0].headers[0][i] == this.constants.translatedText.Orderbook_ChannelAbbreviation)
        {
          sheetDataToExtract[0].columnTypes[i] = 'label';
        }
        else if(sheetDataToExtract[0].headers[0][i] == 'DealId')
        {
          sheetDataToExtract[0].columnTypes[i] = 'number';
        }
      }

      let addOnProductCountAllRows: number[] = [];
      let envProduct: Product = this.constants.environment.product;

      // Loop every visible row, check if value for each product and increment the add-on count
      this.service.rowsFiltered.forEach(r => {
        let addOnCount: number = 0;

        if (r.IsFinanced) addOnCount++;
        if (r.IsCosmetic) addOnCount++;
        if (r.IsPaint) addOnCount++;
        if (r.IsGap) addOnCount++;
        if (envProduct.tyreInsurance && r[envProduct.tyreInsurance]) addOnCount++;
        if (envProduct.tyreAlloyInsurance && r[envProduct.tyreAlloyInsurance]) addOnCount++;
        if (r.IsWheelGuard) addOnCount++;
        if (r.IsServicePlan) addOnCount++;
        if (r.IsWarranty) addOnCount++;

        addOnProductCountAllRows.push(addOnCount);
      })

      let rows = sheetDataToExtract[0].rows;

      for (let i = 0; i < rows.length; i++) {
        // Update each add-on count field with its corresponding value from the add on count array
        rows[i][addOnsIndexPosition] = addOnProductCountAllRows[i];
      }

      // For Spain only - add extras on end
      if (!this.constants.environment.customer.includes("Spain")) {

        sheetDataToExtract[0].headers[0].push('RegisteredDate');
        sheetDataToExtract[0].headers[0].push('IsFinanced');
        sheetDataToExtract[0].headers[0].push('HasCosmetic');
        sheetDataToExtract[0].headers[0].push('HasPaintProtect');
        sheetDataToExtract[0].headers[0].push('HasGap');
        sheetDataToExtract[0].headers[0].push('HasTyre');
        sheetDataToExtract[0].headers[0].push('HasTyreAlloy');
        sheetDataToExtract[0].headers[0].push('HasWheelG');
        sheetDataToExtract[0].headers[0].push('HasServPlan');
        sheetDataToExtract[0].headers[0].push('HasWarranty');
        sheetDataToExtract[0].headers[0].push('DealId');


        tableModel.forEachNode((row, i) => {
          if (sheetDataToExtract[0].rows[i]) {
            let deal: OrderbookRow = row.data;
            let regDate = null;
            if (!!deal.RegisteredDate) {
              regDate = new Date(deal.RegisteredDate)
            }
            sheetDataToExtract[0].rows[i].push(regDate)
            sheetDataToExtract[0].rows[i].push(deal.IsFinanced ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.IsCosmetic ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.IsPaint ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.IsGap ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.IsTyre ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.IsTyreAlloy ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.IsWheelGuard ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.IsServicePlan ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.IsWarranty ? 1 : 0)
            sheetDataToExtract[0].rows[i].push(deal.DealId)

          }
        })

        sheetDataToExtract[0].columnTypes.push('date');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');
        sheetDataToExtract[0].columnTypes.push('number');

        sheetDataToExtract[0].columnWidths.push(15);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
        sheetDataToExtract[0].columnWidths.push(10);
      }

      this.excel.exportSheetsToExcel(sheetDataToExtract)
      this.selections.triggerSpinner.next({ show: false })
    }, 10)

  }


}




