<sidenav *ngIf="constants.amLoggedIn && constants.menuSections"></sidenav>


<!-- <newDropdown *ngIf="constants.amLoggedIn && constants.menuNew" ></newDropdown> -->

<div id="appHolder">




  <section id="appWrapper" [ngClass]="appWrapperClassProvider()">
    <router-outlet *ngIf="OkToShowRouterOutlet()"></router-outlet>
  </section>


  <!-- Spinner -->
  <!--  -->
  <spinner *ngIf="selections.spinner && selections.spinner.show" ></spinner>
  <div [ngClass]="{'showBackdrop':selections.showSpinnerBackdrop}" id="spinnerBackdrop"></div>





  <!-- Initial Load Modal -->
  <initialLoadModal *ngIf="constants.amLoggedIn"></initialLoadModal>


  <!-- Navbar notifications section top right -->
  <div id="notificationsAreaHolder">

    <div id="notificationsArea" class="" *ngIf="constants.amLoggedIn">

      <app-screenRotationModal *ngIf="constants.environment.showRotationButton"></app-screenRotationModal>


      <!-- The New and Used buttons -->
      <ng-container *ngIf="constants.environment.showNewUsedSummaryBadges">
        <div class="counter visibleOnlyOnNonTouchDevice" (click)="showOrderBookDealsToday(true)">
          <h4 class="counterText">
            <div class="spaceBetween">
              <div class="label" onError="New">{{constants.translatedText?.New}}</div>
              <div *ngIf="!!constants.TodayNewUsedOrders" class="count">{{constants.TodayNewUsedOrders.NewOrders}}</div>
            </div>
          </h4>
          <div class="top"></div>
          <div class="bottom"></div>
        </div>

        <div class="counter visibleOnlyOnNonTouchDevice" (click)="showOrderBookDealsToday(false)">
          <h4 class="counterText">
            <div class="spaceBetween">
              <div class="label">{{constants.translatedText?.Used}}</div>
              <div *ngIf="!!constants.TodayNewUsedOrders" class="count">{{constants.TodayNewUsedOrders.UsedOrders}}
              </div>
            </div>
          </h4>
          <div class="top"></div>
          <div class="bottom"></div>
        </div>
      </ng-container>



      <!-- Language toggle -->
      <div *ngIf="constants.environment.languageSelection && constants.currentLang" ngbDropdown
        class="d-inline-block language-select">
        <button class="btn btn-primary language-select-button" id="dropdownBasic1" ngbDropdownToggle>
          <img [src]="'/assets/imgs/flags/' + constants.currentLang + '.png'" alt="Language" height="14px" class="mr-2">
          {{ constants.currentLang | uppercase }}
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <button *ngFor="let lang of constants.getLangs()" ngbDropdownItem class="language-select-item"
            (click)="onLanguageChange(lang)">
            <img [src]="'/assets/imgs/flags/' + lang + '.png'" alt="Language" height="14px" class="mr-2">
            {{ lang | uppercase }}
          </button>
        </div>
      </div>

      <!-- Last snapshot (AutoPrice) -->
      <!-- <span *ngIf="constants.environment.showLatestSnapshotDate && constants.LatestSnapshotDate" class="text-white"
        placement="bottom" popoverClass="vehiclePopUpImage" container="body" triggers="mouseenter:mouseleave"
        [ngbPopover]="lastUpdatePopContent">
        Autotrader data last updated: {{ constants.LatestSnapshotDate | cph:'day':0 }} {{ constants.LatestSnapshotDate |
          cph:'shortTimeAM':0 }}
        </span> -->
        
        
        <div ngbDropdown class="lastUpdatedDropdown" *ngIf="constants.environment.showLatestSnapshotDate && constants.LastUpdatedDates?.AutotraderAdverts" container="body">
          <button class="btn btn-primary" type="button" id="accountDropdownToggle" ngbDropdownToggle>
            Autotrader data last updated: {{ constants.LastUpdatedDates.AutotraderAdverts  | cph:'day':0 }} {{ constants.LastUpdatedDates.AutotraderAdverts  |
              cph:'shortTimeAM':0 }}
        </button>
        <div ngbDropdownMenu>
          <span disabled ngbDropdownItem>Advert data {{ constants.LastUpdatedDates.AutotraderAdverts  |cph:'day':0 }} {{ constants.LastUpdatedDates.AutotraderAdverts  |
            cph:'shortTimeAM':0 }}</span>
          <span disabled ngbDropdownItem>DMS Stocks data {{ constants.LastUpdatedDates.Stocks|cph:'day':0 }} {{ constants.LastUpdatedDates.Stocks |
            cph:'shortTimeAM':0 }}</span>

        </div>
      </div>

      <!-- Account dropdown -->
      <accountDropdown></accountDropdown>
    </div>


  </div>






  <!-- Toast component -->
  <toast *ngIf="constants.showToast" [message]="constants.toastMessage" [icon]="constants.toastIcon"
    [isSuccess]="constants.isToastSuccess"></toast>



  <!-- Shared Modals -->
  <ng-template #alertModal let-modal>
    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">{{constants.alertModal.title}}</h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body alertModalBody lowHeight" [innerHTML]="constants.alertModal.message">

    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-primary"
        (click)="modal.close('Save click')">{{constants.translatedText?.OKUpper}}</button>
      <button type="button" class="btn btn-primary"
        (click)="modal.dismiss('Cancelled')">{{constants.translatedText?.Cancel}}</button>
    </div>
  </ng-template>

  <ng-template class="popover" #popContent>
    <div class="popContentReload">{{constants.translatedText?.ManualReloadMessage}}</div>
  </ng-template>

</div>

<confirmModal #confirmModal></confirmModal>
<inputModal #inputModal></inputModal>
<autoPriceSettingsModal #autoPriceSettingsModal></autoPriceSettingsModal>
<dealerGroupSelectionModal #dealerGroupSelectionModal></dealerGroupSelectionModal>