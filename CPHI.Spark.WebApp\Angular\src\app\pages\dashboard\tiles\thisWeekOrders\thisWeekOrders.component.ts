import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe } from 'src/app/cph.pipe';
import { MenuItemNew, OrderbookTimePeriod } from "src/app/model/main.model";
import { OrderBookService } from "src/app/pages/orderBook/orderBook.service";
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { Deal } from '../../../../model/sales.model';
import { ConstantsService } from '../../../../services/constants.service';
import { SelectionsService } from '../../../../services/selections.service';
import { DailyNetOrdersCancellationsCount, DashboardDataItem, DashboardDataPack, DashboardDataParams } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";
import { ThisWeekOrdersService } from "./thisWeekOrders.service";
import { ThisWeekOrdersModalComponent } from './thisWeekOrdersModal.component';





@Component({
  selector: 'thisWeekOrders',
  templateUrl: './thisWeekOrders.component.html',
  styleUrls: ['./thisWeekOrders.component.scss']
})



export class ThisWeekOrdersComponent implements OnInit {

  @Input() public dept: string;
  @Input() dailyNetOrders: DailyNetOrdersCancellationsCount[];
  @Input() public initialStart: Date;
  @Input() public newDataEmitter: EventEmitter<void>;


  @ViewChild('dealBlobs', { static: true }) dealBlobs: ElementRef;
  @ViewChild('thisWeekOrdersModal', { static: true }) thisWeekOrdersModal: ThisWeekOrdersModalComponent;

  weekStart: Date;
  blobHeight: number;
  maxDealCount: number;
  dealCount: number;
  subscription: any;

  constructor(
    public modalService: NgbModal,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public orderBookService: OrderBookService,
    public cphPipe: CphPipe,
    public getDataService: GetDataMethodsService,
    public dashboardService: DashboardService,
    public service: ThisWeekOrdersService,
    public router: Router
  ) {

  }



  ngOnInit(): void {

    this.weekStart = this.initialStart;

    //if(!this.isValidDate(this.weekStart)){ this.weekStart = this.constants.startOfThisWeek(); }

    this.remakeTile();

    this.subscription = this.newDataEmitter.subscribe(res => {
      //this.getData();
      this.remakeTile();
    })
  }

  ngOnDestroy() {
    if (!!this.subscription) this.subscription.unsubscribe();
  }

  remakeTile() {
    this.dealCount = this.constants.sum(this.dailyNetOrders.map(x => x.Orders - x.Cancellations))
    this.calcBlobHeights();
  }

  trackByFunction(index: number, deal: Deal) { return index; }

  changeWeek(days: number) {

    this.weekStart = this.constants.addDays(this.weekStart, days);

    if(this.dept == 'New')
    {
      this.dashboardService.thisWeeksOrdersStartDateNew = this.weekStart;
    }

    if(this.dept == 'Fleet')
    {
      this.dashboardService.thisWeeksOrdersStartDateFleet = this.weekStart;
    }

    if(this.dept == 'Used')
    {
      this.dashboardService.thisWeeksOrdersStartDateUsed = this.weekStart;
    }
    
    this.getData();
  }


  getData() {

    let dataItems = [];

    let parms: DashboardDataParams = {
      SiteIds: this.dashboardService.chosenSites.map(x => x.SiteId).join(','),
      DataItems: dataItems,
      Department: '',
    }


    if (this.dept == 'New') { 
        dataItems.push(DashboardDataItem.DailyNetOrdersCancellationsNew); 
        parms.WeekStartOrdersTileNew = this.dashboardService.thisWeeksOrdersStartDateNew;  
    }

    if (this.dept == 'Fleet') { 
        dataItems.push(DashboardDataItem.DailyNetOrdersCancellationsFleet);
        parms.WeekStartOrdersTileFleet = this.dashboardService.thisWeeksOrdersStartDateFleet; 
    }

    if (this.dept == 'Used') { 
        dataItems.push(DashboardDataItem.DailyNetOrdersCancellationsUsed);
        parms.WeekStartOrdersTileUsed = this.dashboardService.thisWeeksOrdersStartDateUsed; 
    }

   

    this.getDataService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {

      if (this.dept == 'New') { Object.assign(this.dailyNetOrders, res.DailyNetOrdersCancellationsNew) }
      if (this.dept == 'Fleet') { Object.assign(this.dailyNetOrders, res.DailyNetOrdersCancellationsFleet) }
      if (this.dept == 'Used') { Object.assign(this.dailyNetOrders, res.DailyNetOrdersCancellationsUsed) }
      this.remakeTile();
    })
  }

  barHeight(dayData: DailyNetOrdersCancellationsCount) {
    let netOrders = dayData.Orders - dayData.Cancellations;
    let displayHeight = this.constants.div(Math.abs(netOrders), this.maxDealCount) * 100;
    return displayHeight;
  }


  calcBlobHeights() {
    this.maxDealCount = 0;
    this.dailyNetOrders.forEach(dailyNetOrder => {
      dailyNetOrder.DayDate = dailyNetOrder.DayDate;
      if (Math.abs(dailyNetOrder.Orders - dailyNetOrder.Cancellations) > this.maxDealCount) { this.maxDealCount = Math.abs(dailyNetOrder.Orders - dailyNetOrder.Cancellations) }
    })

    if (this.dealBlobs) {
      let height = this.dealBlobs.nativeElement.clientHeight
      if (this.maxDealCount > 0) {
        this.blobHeight = Math.min(20, height / this.maxDealCount)
      } else {
        this.blobHeight = 20
      }
    }
  }



  showSiteDetails(dayAndDeals: DailyNetOrdersCancellationsCount, userHasClickedWeek:boolean) {
    if (userHasClickedWeek) {
      //user has chosen to show whole week
      this.service.modalHeader = `Week Commencing ${dayAndDeals.DayDate.toISOString()}`;
      this.thisWeekOrdersModal.showModal();

      this.service.startDate = dayAndDeals.DayDate;
      this.service.endDate = this.constants.endOfWeek(dayAndDeals.DayDate);
      this.service.department = this.dept;
      this.service.siteIds = this.dashboardService.chosenSites.map(x => x.SiteId);
      this.service.showSiteDetailForDay();
    }
    else if (dayAndDeals.IsToday && this.dashboardService.mostRecentDateInDailyOrders.getTime() != dayAndDeals.DayDate.getTime()) {
      //user has clicked today
      this.navigateToOrderBook(dayAndDeals)
    } else {
      //user has clicked a day before today

      this.service.modalHeader = dayAndDeals.DayDate.toISOString();
      this.thisWeekOrdersModal.showModal();

      this.service.startDate = dayAndDeals.DayDate;
      this.service.endDate = dayAndDeals.DayDate;
      this.service.department = this.dept;
      this.service.siteIds = this.dashboardService.chosenSites.map(x => x.SiteId);
      this.service.showSiteDetailForDay();

    }
  }







  navigateToOrderBook(day: DailyNetOrdersCancellationsCount) {

    this.orderBookService.initOrderbook();

    let department = this.constants.departments.find(x => x.ShortName === this.dept)

    this.orderBookService.orderDate.startDate = day.DayDate;
    this.orderBookService.orderDate.endDate = day.DayDate;
    this.orderBookService.orderDate.timePeriod = OrderbookTimePeriod.Day;

    this.orderBookService.accountingDate.dateType = this.constants.environment.orderBook.defaultDateType;
    this.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Anytime;
    this.orderBookService.accountingDate.startDate = day.DayDate;
    this.orderBookService.accountingDate.endDate = this.constants.addYears(this.constants.appStartTime, 2);

    this.orderBookService.orderTypeTypes = this.constants.clone(department.OrderTypeTypes);
    this.orderBookService.vehicleTypeTypes = this.constants.clone(department.VehicleTypeTypes);

    this.orderBookService.salesExecName = null;
    this.orderBookService.salesExecId = null;

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Orderbook_LoadingOrderbook })
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/orderBook');
    if (menuItem) { this.constants.navigateByUrl(menuItem); } //, 'operationreports'
  }

  //isValidDate(date): boolean { return date instanceof Date && !isNaN(date.valueOf()); }





}


