﻿

using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
   [Table("PriceChangeAutoItems", Schema = "autoprice")]
   public class PriceChangeAutoItem
   {
      public PriceChangeAutoItem() { }
      public PriceChangeAutoItem(PriceChangeAutoItemWithDates itemIn)
      {
         VehicleAdvertSnapshot_Id = itemIn.VehicleAdvertSnapshot_Id;
         CreatedDate = itemIn.CreatedDate;
         WasPrice = itemIn.WasPrice;
         NowPrice = itemIn.NowPrice;
         IsApproved = itemIn.IsApproved;
         WasOptedOutOfWhenGenerated = itemIn.WasOptedOutOfWhenGenerated;
         CouldGenerateNewPrice = itemIn.CouldGenerateNewPrice;
         CouldNotGenerateNoRuleSet = itemIn.CouldNotGenerateNoRuleSet;
         CouldNotGenerateNoRetailRating = itemIn.CouldNotGenerateNoRetailRating;
         CouldNotGenerateNoDaysAdvertised = itemIn.CouldNotGenerateNoDaysAdvertised;
         CouldNotGenerateNoValuation = itemIn.CouldNotGenerateNoValuation;
         IsKeyChange = itemIn.IsKeyChange;
      }
      public int Id { get; set; }



      //The daily rating itself that this price change relates to
      public int VehicleAdvertSnapshot_Id { get; set; }
      [ForeignKey("VehicleAdvertSnapshot_Id")]
      public VehicleAdvertSnapshot VehicleAdvertSnapshot { get; set; }


      public DateTime CreatedDate { get; set; }
      [Column(TypeName = "decimal(9, 2)")] //up to 9,999,999.99   i.e. up to £9.999m car
      public decimal? WasPrice { get; set; }
      [Column(TypeName = "decimal(9, 2)")] //up to 9,999,999.99   i.e. up to £9.999m car
      public decimal NowPrice { get; set; }
      public bool IsApproved { get; set; }
      public DateTime? DateSent { get; set; }
      public DateTime? DateConfirmed { get; set; }
      [MaxLength(500)]
      public string SaveResult { get; set; }
      public bool WasOptedOutOfWhenGenerated { get; set; }
      public bool CouldGenerateNewPrice { get; set; }
      public bool CouldNotGenerateNoRuleSet { get; set; }
      public bool CouldNotGenerateNoRetailRating { get; set; }
      public bool CouldNotGenerateNoDaysAdvertised { get; set; }
      public bool CouldNotGenerateNoValuation { get; set; }
      public decimal? DaysToSell { get; set; }
      [Column(TypeName = "varchar(250)")]
      public string PriceIndicator { get; set; }
      public DateTime? ApprovedDate { get; set; }
      public int? ApprovedBy_Id { get; set; }
      [ForeignKey("ApprovedBy_Id")]
      public Person Person { get; set; }
      //public DateTime? PriceChangedDate { get; set; }
      public bool ApproverHasBeenEmailed { get; set; }
      public bool IsKeyChange { get; set; }

   }



}
