<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        {{ constants.translatedText.Dashboard_ServiceSales_Title }}
        <span *ngIf="selections.serviceSummary.chosenSite">: {{ selections.serviceSummary.chosenSite }}</span>
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.FinancialLines"></sourceDataUpdate>
      </div>
    </h4>

    <ng-container *ngIf="selections.serviceSummary">

      <!-- Month selector -->
      <div class="buttonGroup">
        <!-- Previous -->
        <button class="btn btn-primary" (click)="changeMonth(selections.serviceSummary.month, -1)">
          <i class="fas fa-caret-left"></i>
        </button>

        <!-- Dropdown -->
        <div class="d-inline-block" [autoClose]="true" ngbDropdown>
          <button class="btn btn-primary centreButton" ngbDropdownToggle (click)="makeMonths(0)">
            {{ selections.serviceSummary.month.name | titlecase }}
          </button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <!-- the ngFor buttons -->
            <button *ngFor="let month of months" ngbDropdownItem
              (click)="selectMonth(selections.serviceSummary.month, month.startDate)">
              {{ month.startDate | cph:'month':0 | titlecase }}
            </button>
          </div>
        </div>

        <!-- Next -->
        <button class="btn btn-primary" (click)="changeMonth(selections.serviceSummary.month, 1)">
          <i class="fas fa-caret-right"></i>
        </button>
      </div>

      <ng-container *ngIf="!selections.serviceSummary.chosenSite">
    
        <!-- Table type selector -->
        <div *ngIf="constants.environment.serviceSummary_showTableTypeSelector" class="buttonGroup">
          <button *ngFor="let tableType of selections.serviceSummary.tableTypes" class="btn btn-primary"
            [ngClass]="{ 'active': tableType == selections.serviceSummary.tableType }"
            (click)="chooseTableType(tableType)">
            {{ tableType }}
          </button>
        </div>

        <!-- Time selector -->
        <div class="buttonGroup" *ngIf="!this.selections.serviceSummary.showDailyView">
          <button *ngFor="let timeOption of selections.serviceSummary.timeOptions" class="btn btn-primary"
            [ngClass]="{ 'active': selections.serviceSummary.timeOption == timeOption }"
            (click)="selectTimeOption(timeOption)">
            {{ timeOption }}
          </button>
        </div>
    
        <!-- Channel selector -->
        <div class="d-inline-block" ngbDropdown dropright>
          <button class="btn btn-primary" [ngClass]="{ 'active': !showAllChannels }" ngbDropdownToggle
            (click)="generateChannelList()">
            {{ channelChosenLabel() }}
          </button>

          <div ngbDropdownMenu>
            <button *ngFor="let channel of channelsDropdownList" [ngClass]="{ 'active': channel.isSelected }"
              ngbDropdownItem (click)="toggleItem(false, channel)">
              {{ channel.label }}
            </button>

            <button class="quickSelect" ngbDropdownItem (click)="toggleItem(true)">{{
              constants.translatedText.Total }}</button>
            <div class="spaceBetween">
              <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle
                (click)="selectChannels()">OK</button>
              <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{
                constants.translatedText.Cancel }}</button>
            </div>
          </div>
        </div>

      </ng-container>
    </ng-container>
  </nav>

  <nav class="pageSpecific"></nav>
</nav>

<!-- Main Page -->
<div *ngIf="selections.serviceSummary" class="content-new">
  <div class="content-inner-new">
    <!-- Sites overview (Tables) -->
    <div *ngIf="!selections.serviceSummary.chosenSite && service.serviceSalesSitesRows" id="tableHolder">
      <serviceTable (clickedSite)="selectSite($event)"></serviceTable>
      <div class="tableSpacer"></div>
      <serviceTable [isRegional]="true" (clickedSite)="selectSite($event)"></serviceTable>
    </div>
    <!-- Site overview (Tiles) -->
    <div *ngIf="selections.serviceSummary.chosenSite && selections.serviceSummary" class="dashboard-grid-container">
      <!-- Floating back button -->
      <button class="btn btn-primary back-to-sites" (click)="selections.serviceSummary.chosenSite = null">
        <i class="fas fa-undo"></i>
      </button>
      <!-- The tiles -->
      <div class="dashboard-grid">
        <div class="dashboard-tile grid-col-1-1 grid-row-1-6">
          <salesPositionTile *ngIf="service.serviceVsTarget" [page]="'service'"></salesPositionTile>
        </div>
        <div class="dashboard-tile grid-col-2-2 grid-row-1-9">
          <salesByDayTile *ngIf="service.serviceDailyDoneVsTarget" [page]="'service'"></salesByDayTile>
        </div>
        <div class="dashboard-tile grid-col-3-5 grid-row-1-5">
          <serviceSalesByTypeTile *ngIf="service.serviceChannelSplits"></serviceSalesByTypeTile>
        </div>
        <div class="dashboard-tile grid-col-1-1 grid-row-6-9">
          <wipAgeingSummaryTile *ngIf="service.wipSummary" [page]="'service'"></wipAgeingSummaryTile>
        </div>
        <div class="dashboard-tile grid-col-3-3 grid-row-5-9">
          <runChaseTile *ngIf="service.runChaseDataPoints" [page]="'service'"></runChaseTile>
        </div>
        <div class="dashboard-tile grid-col-4-4 grid-row-5-9">
          <runRateAndRequirementTile *ngIf="service.serviceRunRate" [page]="'service'"></runRateAndRequirementTile>
        </div>
      </div>
    </div>
  </div>
</div>