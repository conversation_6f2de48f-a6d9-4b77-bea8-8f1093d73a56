import {Component, OnInit} from '@angular/core';
import {StatsSiteDashboardService} from './sitesLeague.service';

export enum StatsSiteDashboardPageComponentType {
  grid, page
}

export enum StatsSiteDashboardTableColumnChoices {
  DaysListed, RetailRating, PerformanceIndicator
}


@Component({
  selector: 'sitesLeague',
  templateUrl: './sitesLeague.component.html',
  styleUrls: ['./sitesLeague.component.scss']
})


export class SitesLeagueComponent implements OnInit {
  indicateNewData: boolean;


  constructor(
    public service: StatsSiteDashboardService,
  ) {


  }

  // get countChosenItems() {
  //   return this.service.simpleExampleItemsFiltered.filter(x => x.isChosen).length;
  // }


  async ngOnInit() {
    this.service.selectionsService.triggerSpinner.emit({show: true, message: 'Loading Site League'});
    this.service.initParams();
    this.service.pageRef = this;
    await this.service.getData();

  }

  toggleDaysListed() {
    this.service.showDaysListed = !this.service.showDaysListed;
    this.service.gridRef.dealWithNewColumnChoices();
  }

  toggleRetailRatingCol() {
    this.service.showByRetailRating = !this.service.showByRetailRating;
    this.service.gridRef.dealWithNewColumnChoices();
  }

  togglePerformanceIndicator() {
    this.service.showByPerformanceIndicator = !this.service.showByPerformanceIndicator;
    this.service.gridRef.dealWithNewColumnChoices();
  }

  toggleUseTestStrategy(): void
  {
    this.service.useTestStrategy = !this.service.useTestStrategy;
    this.service.getData();
  }
  toggleLowImageCountIndicator() {
    this.service.showByLowImageCountIndicator = !this.service.showByLowImageCountIndicator;
    this.service.gridRef.dealWithNewColumnChoices();
  }

  toggleIncludeNewVehicles(): void {
    this.service.includeNewVehicles = !this.service.includeNewVehicles;
    //this.service.resetChosenLifecycleStatues()
    this.service.getData();
  }

  toggleIncludeUnPublishedAds(): void {
    this.service.includeUnPublishedAds = !this.service.includeUnPublishedAds;
    //this.service.resetChosenLifecycleStatues()
    this.service.getData();
  }

  public async onChosenSitesChange() {
    await this.service.getData();
  }

  dealWithNewData(data: any): void {
    this.indicateNewData = true;
    setTimeout(() => {
      this.indicateNewData = false;
    }, 1000);
  }

  onChosenLifecycleStatusesChange() {
    this.service.getData();
  }

}

