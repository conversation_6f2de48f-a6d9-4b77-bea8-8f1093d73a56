<nav class="navbar">

  <nav class="generic">
    <div id="pageTitle">
      <div>
        Sites League
      </div>
    </div>

    <ng-container *ngIf="service">

      <!-- Show / hide new vehicles -->
      <sliderSwitch text="New Vehicles" (toggle)="toggleIncludeNewVehicles()"
          [defaultValue]="service.includeNewVehicles">
      </sliderSwitch>

      <!-- Include unpublished -->
      <sliderSwitch text="Un-published Vehicles"
          (toggle)="toggleIncludeUnPublishedAds()"
          [defaultValue]="service.includeUnPublishedAds">
      </sliderSwitch>

      <!-- Lifecycle status picker -->
      <multiPicker
          *ngIf="service.allLifecycleStatuses"
          [label]="'Lifecycle Statuses'"
          [menuItems]="service.allLifecycleStatuses"
          [chosenItems]="service.chosenLifecycleStatuses"
          [onChosenItemsChange]="onChosenLifecycleStatusesChange.bind(this)">
      </multiPicker>

      <!-- Include LCVs toggle - only for Enterprise -->
      <sliderSwitch
          *ngIf="service.constantsService.environment.customer === 'Enterprise'"
          text="Include LCVs"
          (toggle)="toggleIncludeLCVs()"
          [defaultValue]="service.includeLCVs">
      </sliderSwitch>


      <sliderSwitch [text]="'Show by days listed'" [defaultValue]="service.showDaysListed"
                    (toggle)="toggleDaysListed()">
      </sliderSwitch>


      <sliderSwitch [text]="' Show by retail rating band'" [defaultValue]="service.showByRetailRating"
                    (toggle)="toggleRetailRatingCol()">
      </sliderSwitch>


      <sliderSwitch [text]="'Show by performance indicator'" [defaultValue]="service.showByPerformanceIndicator"
                    (toggle)="togglePerformanceIndicator()">
      </sliderSwitch>

      <sliderSwitch [text]="'Show by low image count'" [defaultValue]="service.showByLowImageCountIndicator"
                    (toggle)="toggleLowImageCountIndicator()">
      </sliderSwitch>

        <!-- Use Test Strategy (only if they have access) -->
        <sliderSwitch *ngIf="service.showTestStrategySlider" text="Use Test Strategy" (toggle)="toggleUseTestStrategy()" [defaultValue]="service.useTestStrategy"></sliderSwitch>

      <!-- </div> -->

    </ng-container>


  </nav>

  <nav class="pageSpecific">


  </nav>
</nav>


<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constantsService.environment.customer">
  <div class="content-new">

    <!-- <div class="dashboard-grid cols-12">

      <div class="autotrader-tile  grid-row-1-6 grid-col-1-13">
        <div>
          <div class="tile-inner">
            <div class="tile-header">
              Sites dashboard
            </div>
            <div class="tile-body">

              <statsSiteDashboardTable *ngIf="service.sites" [tableParams]="service">
              </statsSiteDashboardTable>

            </div>
          </div>
        </div>
      </div>

      <br>




    </div> -->

    <sitesLeagueTable *ngIf="service.sites" [tableParams]="service">
    </sitesLeagueTable>


  </div>
</div>
