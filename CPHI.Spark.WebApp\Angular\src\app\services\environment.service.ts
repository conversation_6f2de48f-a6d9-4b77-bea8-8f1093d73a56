import {Injectable} from '@angular/core';
import {environmentRRG} from 'src/environments/environmentRRG';
import {environmentSpain} from 'src/environments/environmentSpain';
import {environmentVindis} from 'src/environments/environmentVindis';
import {environmentAutoprice} from 'src/environments/environmentAutoprice';
import {Channel} from '../model/sales.model';
import {DashboardSection} from '../pages/dashboard/dashboard.component';
import {AppConfigService} from './appConfig.service';
import {environmentV12} from 'src/environments/environmentV12';
import {environmentMJMotorCo} from 'src/environments/environmentMJMotorCo';
import {environmentBrindley} from 'src/environments/environmentBrindley';
import {environmentKCSOfSurrey} from 'src/environments/environmentKCSOfSurrey';
import {environmentWaylands} from 'src/environments/environmentWaylandsGroup';
import {environmentSturgessGroup} from 'src/environments/environmentSturgessGroup';
import {environmentJJPremiumCars} from 'src/environments/environmentJJPremiumCars';
import {environmentPentagonGroup} from 'src/environments/environmentPentagonGroup';
import {environmentHippoApproved} from 'src/environments/environmentHippoApproved';
import {environmentLMCOfFarnham} from 'src/environments/environmentLMCOfFarnham';
import {environmentStartin} from 'src/environments/environmentStartin';
import {environmentOakwoodMotorCo} from 'src/environments/environmentOakwoodMotorCo';
import {environmentSparshattsGroup} from 'src/environments/environmentSparshattsGroup';
import {environmentThrifty} from 'src/environments/environmentThrifty';
import {environmentBellsCrossgar} from 'src/environments/environmentBellsCrossgar';
import {environmentCroxdale} from 'src/environments/environmentCroxdale';
import {environmentEMG} from 'src/environments/environmentEMGGroup';
import {environmentAcorn} from 'src/environments/environmentAcornGroup';
import {environmentLithia} from 'src/environments/environmentLithia';
import {environmentFordsOfWinsford} from 'src/environments/environmentFordsOfWinsford';
import {environmentLSH} from 'src/environments/environmentLSH';
import {environmentSytner} from 'src/environments/environmentSytner';
import {environmentEnterprise} from 'src/environments/environmentEnterprise';
import {environmentSandicliffe} from 'src/environments/environmentSandicliffe';
import {environmentNuneaton} from 'src/environments/environmentNuneaton';
import { environmentCarco } from 'src/environments/environmentCarco';

export interface SparkEnvironment {
   customer: string;
   production: boolean;
   version: string;
   languagePickerOnLogin: boolean;
   franchisePicker: boolean;
   stockGroupPicker: boolean;
   lateCostPicker: boolean;
   orderTypePicker: boolean;
   ageingOptions: boolean;
   displayCurrency: string;
   displayCurrencySymbol: string;
   bookingsBar: BookingsBar;
   dealDetails: DealDetails;
   usedStockTable: UsedStockTable;
   sideMenu: SideMenu;
   fullSideMenu: FullSideMenu;
   citNoww: CitNoww;
   dealDetailModal: DealDetailModal;
   dashboard: Dashboard;
   debts: Debts;
   evhcTile: string;
   horizontalBar: HorizontalBar;
   stockItemModal: StockItemModal;
   wipsTable: WipsTable;
   stockTable: StockTable;
   stockList: StockList;
   sitesLeague: SitesLeague;
   performanceLeague: PerformanceLeague;
   overAgeStockTable: OverAgeStockTable;
   dealPopover: DealPopover;
   orderBook: OrderBook;
   partsSales: PartsSales;
   handoverDiary: HandoverDiary;
   partsStockDetailedTable: PartsStockDetailedTable;
   salesPerformance: SalesPerformance;
   selectionsService: SelectionsServiceEnvironment;
   serviceBookingsTable: ServiceBookingsTable;
   stockReport: StockReport;
   whiteboard: Whiteboard;
   serviceChannels?: (Channel)[] | null;
   partsChannels?: (Channel)[] | null;
   initialPageURL: string;
   orderBookURL: string;
   fleetOrderbookURL: string;
   product: Product;
   dealDone: DealDone;
   evhc: Evhc;
   fAndISummary: FAndISummary;
   partsStock: PartsStock;
   dealsForTheMonth: DealsForTheMonth;
   partsStockSitesCoverTable: PartsStockRegionalTableCoverOrPartsStockSitesCoverTable;
   dealsDoneThisWeek: DealsDoneThisWeek;
   orderTypePickerOptions: OrderTypePickerOptions;
   todayMap: TodayMap;
   vehicleTypePicker: VehicleTypePicker;
   userSetup: UserSetup;
   languageSelection: boolean;
   serviceSummary: ServiceSummary;
   partsSummary: PartsSummary;
   allGroups: string[];
   allFamilyCodes: string[];
   serviceSalesDashboard: {
      onlyLabour: boolean;
   };
   dealDetailsModal: DealDetailModalNew;
   donutShowLastYearUnits: boolean;
   showNewUsedSummaryBadges: boolean;
   showPrepCostsWhenValuing: boolean;
   isSingleSiteGroup: boolean; //

   showChangePriceNowInputAlways: boolean;
   menuItems: MenuItems;
   showApproveAutoPrices: boolean;
   showRotationButton: boolean;
   showLatestSnapshotDate: boolean;
   showNestedSideMenu: boolean;

   dealershipBackgroundImageName: string;
   homeIsLandingPage: boolean;
   vehiclePricing_StockReport_showBcaColumns: boolean;
   showRegionFilterOnSiteDashboard: boolean;
   fAndISummary_includeTargets: boolean;
   showWholesaleAdjustmentOption?: boolean;
}

export interface AutoPrice {
   defaultShowUnpublishedAds: boolean;
   defaultShowNewVehicles: boolean;
   // vehicleValuationShowCostingDetail: boolean
   stockReport: AutoPriceStockReport;
   applyPriceScenarios: boolean;
   // allowChooseNewStrategy: boolean;
   separateBuyingStrategy: boolean;
   separateBuyingStrategy2: boolean;
   lifecycleStatusDefault: string[];
   allowTestStrategy: boolean;
   vehicleTypes: string[];
   defaultVehicleTypes: string[];
   defaultToDaysInStock: boolean;
   showWholesaleAdjustmentOption?: boolean;
}

export interface AutoPriceStockReport {
   showDMSSellingPrice_Col: boolean;
   showVsDMSSellingPrice_Col: boolean;
   showPhysicalLocation_Col: boolean;
}

export interface BookingsBar {
   barStyle1: boolean;
   barStyle2: boolean;
}

export interface DealDetails {
   componentName: string;
   profitTableShowSale: boolean;
   profitTableShowCost: boolean;
   profitTableShowCommission: boolean;
   profitTableFinanceCommText: string;
   showDescription: boolean;
   showVehicleAge: boolean;
   showPaintProtection: boolean;
   paintProtectionText: string;
   showPaintProtectionCost: boolean;
   showPaintProtectionSale: boolean;
   showPhysicalLocation: boolean;
   showIsDealClosed: boolean;
   showFinanceCo: boolean;
   showFinanceType: boolean;
   showWarrantyInfo: boolean;
   showRCIFinanceComm: boolean;
   showPCPFinanceComm: boolean;
   showStandardsCommission: boolean;
   showProPlusCommission: boolean;
   showSelectCommission: boolean;
   showGapInsurance: boolean;
   showTyreInsurance: boolean;
   showAlloyInsurance: boolean;
   showWheelGard: boolean;
   showServicePlan: boolean;
   showWarranty: boolean;
   showUnits: boolean;
   showDeliverySite: boolean;
   showAdditionalAddOnProfit: boolean;
   showCosmeticInsuranceSale: boolean;
   showCosmeticInsuranceCost: boolean;
   showCosmeticInsuranceCommission: boolean;
   showVATCost: boolean;
}

export interface UsedStockTable {
   vindisFormatting: boolean;
   tactical: boolean;
   exManagementCount: boolean;
   exDemo: boolean;
}

export interface SideMenu {
   dashboard: boolean;
   pricingDashboard: boolean;
   orderbook: boolean;
   fleetOrderbook: boolean;
   dealsDoneThisWeek: boolean;
   dealsForTheMonth: boolean;
   whiteboard: boolean;
   performanceLeague: boolean;
   performanceTrends: boolean;
   scratchCards: boolean;
   salesIncentive: boolean;
   supercup: boolean;
   supercup2: boolean;
   handoverDiary: boolean;
   distrinet: boolean;
   reportPortal: boolean;
   stockList: boolean;

   // autoprice
   pricingHome: boolean;
   stockInsight: boolean;
   summaryDashboard: boolean; // new
   siteDetailDashboard: boolean;
   strategyBuilder: boolean;
   applyStrategy: boolean;
   stockReports: boolean; // new
   locationOptimiser: boolean;
   leavingVehicles: boolean;
   vehicleValuation: boolean;
   bulkValuation: boolean; // new
   localBargains: boolean;
   optOuts: boolean;
   todayPriceChanges: boolean;
   autoPriceSiteSettings: boolean;
   stockPricing: boolean;
   oldStockPricing: boolean;
   leavingVehicleDetail: boolean;
   leavingVehicleTrends: boolean;


   salesCommission: boolean;
   salesExecReview: boolean;
   stockLanding: boolean;

   liveForecast: boolean;
   userMaintenance: boolean;
}

export interface FullSideMenu {
   description: string;
}

export interface CitNoww {
   tileHeader: string;
   moveNissanValuesToRenault: boolean;
   renaultRegions: boolean;
   vindisRegions: boolean;
   excludeAudi: boolean;
   pcOfSalesEnquiries: boolean;
   pcOfInvoicedExtWips: boolean;
   pcOfSalesEnquiriesText: string;
   pcOfInvoicedExtWipsText: string;
   showSimpleCitNowPersonDetail: boolean;
   showVideosViewed: boolean;
   eDynamixView: boolean;
}

export interface DealDetailModal {
   showOtherProfit: boolean;
   showFinanceProfit: boolean;
   showAddOnProfit: boolean;
   showTotalProfit: boolean;
   showStockDetailButton: boolean;
   // showStockWebsiteListingButton: boolean;
}

export interface Dashboard {
   canChooseMonth: boolean;
   sections: DashboardSection[];
   showStockCover: boolean;
   includeExtraSpainMenuButtons: boolean;
   showZoeSales: boolean;
   showHandoverDiarySummary: boolean;
   showCashDebts: boolean;
   showBonusDebts: boolean;
   showRenaultRegistrations: boolean;
   showDaciaRegistrations: boolean;
   showFleetRegistrations: boolean;
   showUsedStockMerchandising: boolean;
   showCommissions: boolean;
   showFinanceAddonPerformance: boolean;
   showfAndIPerformanceRRG: boolean;
   showVocNPSTile: boolean;
   showActivityLevels: boolean;
   showActivityOverdues: boolean;
   showFleetPerformance: boolean;
   showVoC: boolean;
   showServiceBookings: boolean;
   showCitNow: boolean;
   showImageRatios: boolean;
   showSalesmanEfficiency: boolean;
   showEvhc: boolean;
   showFinanceAddons: boolean;
   showRegistrations: boolean;
   showSimpleDealsByDay: boolean;
   excludeTypesFromBreakDown: string[];
   showFinanceAddonsAllSites: boolean;
   includeDemoStockInStockHealth: boolean;
}

export interface Debts {
   agedDebts: boolean;
   includeBonuses: boolean;
   simpleSitesTable: boolean;
   showAgedOnPicker: boolean;
   showBonusDebtType: boolean;
}

export interface HorizontalBar {
   title: string;
   exDemo: string;
   forRenault: boolean;
   forVindis: boolean;
}

export interface StockItemModal {
   onlineMerchandising: boolean;
}

export interface WipsTable {
   hideBookingColumn: boolean;
   hideDepartmentColumn: boolean;
   hideAccountColumn: boolean;
   hideDueDateOutColumn: boolean;
   hideWDateInColumn: boolean;
   hideWDateOutColumn: boolean;
   hidePartsColumn: boolean;
   hideOilColumn: boolean;
   hideLabourColumn: boolean;
   hideSubletColumn: boolean;
   hideProvisionColumn: boolean;
   hideCreatedColumn: boolean;
   hideNotesColumn: boolean;
}

export interface StockTable {
   hideTacticalColumn: boolean;
   hideExManagementColumn: boolean;
   hideExDemoColumn: boolean;
}

export interface StockList {
   // hideAttentionGrabberColumn: boolean;
   // hideCreatedDateColumn: boolean;
   // hidePriceColumn: boolean;
   // hidePriceExtracolumn: boolean;
   // hideDolColumn: boolean;
   // hideImageCountColumn: boolean;
   // hideVideoCountColumn: boolean;
   // hideOnRRGSiteColumn: boolean;
   // hideImagesColumn: boolean;
   // hideProgressCodeColumn: boolean;
   // hideStockcheckLocationColumn: boolean;
   // hideStockcheckPhotoColumn: boolean;
   // hideCarryingValueColumn: boolean;
   // hideIsVatQColumn: boolean;
   // hideCapProvisionColumn: boolean;
   // hideModelPicker: boolean;
   // vehicleTypes?: (string)[] | null;
   hideStockDetailsModal: boolean;
   tableColumns: string[];
   franchises: string[];
   // hidePUColumn: boolean;
   // hideDaysAtBranchColumn: boolean;
   // hideSiteDescriptionColumn: boolean;
   // hideSiteDescriptionShortColumn: boolean;
}

export interface SitesLeague {
   includeToday: boolean;
}

export interface PerformanceLeague {
   hideBadges: boolean;
   showDeliveredButton: boolean;
   incLeaversButton: boolean;
   showExecAndManagerSelector: boolean;
}

export interface OverAgeStockTable {
   hideDemoColumn: boolean;
   hideTacticalColumn: boolean;
   hideExManagementColumn: boolean;
   hideExDemoColumn: boolean;
   hideTradeColumn: boolean;
   usedColumnName: string;
}

export interface DealPopover {
   showMetalProfit: boolean;
   showOtherProfit: boolean;
   showFinanceProfit: boolean;
   showAddons: boolean;
   showAddonProfit: boolean;
}

export interface OrderBook {
   showNewOrderbook: boolean;
   showNewDealButton: boolean;
   ordersDescription: string;
   hideDeliverySiteColumn: boolean;
   hideVehicleTypeColumn: boolean;
   hideOemReferenceColumn: boolean;
   hideVehClassColumn: boolean;
   hideModelColumn: boolean;
   hideModelYearColumn: boolean;
   hideChannelColumn: boolean;
   hideTypeColumn: boolean;
   hideVehicleSourceColumn: boolean;
   hideDaysToDeliverColumn: boolean;
   hideDaysToSaleColumn: boolean;
   hideLocationColumn: boolean;
   hideIsConfirmedColumn: boolean;
   hideVehTypeColumn: boolean;
   hideIsClosedColumn: boolean;
   hideUnitsColumn: boolean;
   hideFinanceTypeColumn: boolean;
   hideIsLateCostColumn: boolean;
   hideAddonsColumn: boolean;
   includeAccgDate: boolean;
   customDateTypes?: (string)[] | null;
   defaultDateType: string;
   showLateCost: boolean;
   showOrderOptions: boolean;
   showAccountingDateButton: boolean;
   showDeliveryOptionButtons: boolean;
   hideDiscountColumn: boolean;
   hideFinanceProfitColumn: boolean;
   hideOtherProfitColumn: boolean;
   hideMetalColumn: boolean;
   hideSalesChannel: boolean;
   hideComments: boolean;
   hideOrderAllocationDate: boolean;
   showMetalSummary: boolean;
   showOtherSummary: boolean;
   showFinanceSummary: boolean;
   showInsuranceSummary: boolean;
   siteColumnWidth: number;
   customerColumnWidth: number;
   vehicleClassColumnWidth: number;
   salesExecColumnWidth: number;
   descriptionColumnWidth: number;
   hideOrderDateSelection: boolean;
   hideAuditColumn: boolean;
   showManagerSelector: boolean;
   hideDateFactoryTransportationColumn: boolean;
   hideDateVehicleReconditionColumn: boolean;
   hideDateSiteTransportationColumn: boolean;
   hideDateSiteArrivalColumn: boolean;
   hideReservedDateColumn: boolean;
}

export interface PartsSales {
   showMarginColPerc: boolean;
   showMarginCol: boolean;
   includeMarginCols: boolean;
}

// Unused ??
export interface GetDealsServiceEnvironment {
   includeDeliverySite: boolean;
   includeLastPhysicalLocation: boolean;
}

export interface HandoverDiary {
   includeCustomerName: boolean;
   includeLastPhysicalLocation: boolean;
   includeHandoverDate: boolean;
   isInvoiced: boolean;
   isConfirmed: boolean;
   futureHandoversGreyedOut: boolean;
   showManagerSelector: boolean;
}

export interface PartsStockDetailedTable {
   hideCreatedColumn: boolean;
   partStockBarCharts1: PartStockBarCharts1;
   partStockBarCharts2?: any;
   showPartStockAgeingColumnsForRRG: boolean;
   showPartStockAgeingColumnsForVindis: boolean;
   hideOfWhichColumn: boolean;
   hideDeadValueColumn: boolean;
   hideDormantValueColumn: boolean;
   hideDeadProvColumn: boolean;
   hideDormantProvColumn: boolean;
   setClassesForVindis: boolean;
   setClassesForRRG: boolean;
}

export interface PartStockBarCharts1 {
   headerName: string;
   field: string;
   colId: string;
}

export interface SalesPerformance {
   description: string;
   showFranchisePicker: boolean;
   showLateCostButtons: boolean;
   showIncludeExcludeOrders: boolean;
   showTradeUnitButtons: boolean;
   showMotabilityButtons: boolean;
   showOrderRateReportType: boolean;
   showCustomReportType: boolean;
   showAllSites: boolean;
}

// Unused ??
export interface SalesSplitFleet {
   className: string;
}

export interface SelectionsServiceEnvironment {
   ageingOptions?: (AgeingOptionsEntityOrAgeingOption)[] | null;
   ageingOption: AgeingOptionsEntityOrAgeingOption;
   deliveryDateDateType: string;
   eligibleForCurrentUserCheck: boolean;
}

export interface AgeingOptionsEntityOrAgeingOption {
   description: string;
   ageCutoff: number;
}

export interface ServiceBookingsTable {
   showPrepHours: boolean;
   clickSiteEnable: boolean;
}

export interface StockReport {
   showAgePicker: boolean;
   hideOnRRGSiteCol: boolean;
   initialStockReport: string;
   seeUsedStockReport: boolean;
   seeAllStockReport: boolean;
   seeUsedMerchandisingReport: boolean;
   seeOverageStockReport: boolean;
   seeStockGraphsReport: boolean;
   seeStockByAgeReport: boolean;
   includeReservedCarsOption: boolean;
}

// Unused ??
export interface ThisWeeksOrders {
   showDealCount: boolean;
   initialise: boolean;
}

export interface Whiteboard {
   showConfirmed: boolean;
   showNotConfirmed: boolean;
   showFinance: boolean;
   showAddons: boolean;
   showLateCostPicker: boolean;
   showManagerSelector: boolean;
}

// Unused ??
export interface ServiceChannelsEntity {
   displayName: string;
   name: string;
   channelTags?: (string)[] | null;
   icon: string;
   hasHours: boolean;
   divideByChannelName: string;
   isLabour: boolean;
   isTotal?: boolean | null;
}

// Unused ??
export interface PartsChannelsEntity {
   displayName: string;
   name: string;
   channelTags?: (string)[] | null;
   icon: string;
   channelTag: string;
   hasHours: boolean;
   divideByChannelName: string;
   isLabour: boolean;
   isTotal?: boolean | null;
}

export interface Product {
   tyreInsurance: string;
   tyreAlloyInsurance: string;
   showAlloyInsurance: boolean;
}

export interface DealDone {
   showVindisSitePicker: boolean;
   showRRGSitePicker: boolean;
   showRRGPopoverContent: boolean;
   showVindisPopoverContent: boolean;
}

export interface Evhc {
   showTechTable: boolean;
   vehiclesCheckedPercent: number;
   workQuoted: number;
   workSoldPercent: number;
   redWorkSoldPercent: number;
   amberWorkSoldPercent: number;
   eDynamixView: boolean;
}

export interface FAndISummary {
   processTypeAndTypeAlloy: boolean;
   hideAlloyColumn: boolean;
}

export interface PartsStock {
   includeOfWhichColumns: boolean;
}

export interface DealsForTheMonth {
   showMetal: boolean;
   showOther: boolean;
   showFinance: boolean;
   showAddons: boolean;
   showGpu: boolean;
   showBroughtInColumn: boolean;
   showIncludeExcludeOrders: boolean;
   showLateCostPicker: boolean;
}

export interface PartsStockRegionalTableCoverOrPartsStockSitesCoverTable {
   partStockName: string;
}

export interface DealsDoneThisWeek {
   showPlotOptions: boolean;
}

export interface OrderTypePickerOptions {
   showRetail: boolean;
   showFleet: boolean;
}

export interface TodayMap {
   defaultPositionLat: number;
   defaultPositionLong: number;
   defaultZoom: number;
}

export interface VehicleTypePicker {
   showUsed: boolean;
   showNew: boolean;
   showAll: boolean;
   hiddenVehicleTypes: string[];
}

export interface UserSetup {
   hideUploadReports: boolean;
   hideViewReports: boolean;
   hideCommReview: boolean;
   hideCommSelf: boolean;
   hideSerReviewer: boolean;
   hideSerSubmitter: boolean;
   hideStockLanding: boolean;
   hideSuperCup: boolean;
   hideCanEditExecManagerMappings: boolean;
   hideIsSalesExec: boolean;
   hideAllowReportUpload: boolean;
   hideAllowReportCentre: boolean;
   hideLiveforecast: boolean;
   hideSalesRoles: boolean;
   hideTMgr: boolean;
   allSalesRoles: string[];
   canReviewStockPrices: boolean;
   canActionStockPrices: boolean;
   canEditStockPriceMatrix: boolean;
}

export interface ServiceSummary {
   showTableTypeSelector: boolean;
   defaultTableType: string;
   tableTypes: string[];
   defaultTimeOption: string;
   timeOptions: string[];
   showTechGroupColumns: boolean;
}

export interface PartsSummary {
   showTableTypeSelector: boolean;
   defaultTableType: string;
   tableTypes: string[];
}

export interface DealDetailModalNew {
   currencyDP: number;
   costColumnTranslation: string;
   dealDetailsSection: {
      showVariant: boolean;
      showWebsiteDiscount: boolean;
      showFinanceType: boolean;
      showOEMReference: boolean;
      showQualifyingPartEx: boolean;
      showPhysicalLocation: boolean;
      showIsClosed: boolean;
      showFinanceCo: boolean;
      showDescription: boolean;
      showUnits: boolean;
      showVehicleAge: boolean;
      showIsLateCost: boolean;
      showAuditPass: boolean;
      showInvoiceNo: boolean;
   };
   metalProfitSection: {
      headerTranslation: string;
      showVATCost: boolean;
   };
   otherProfitSection: {
      showRegBonus: boolean;
      showIntroComm: boolean;
      showBrokerCost: boolean;
      showAccessories: boolean;
      showPaintProtectionAccessory: boolean;
      showFuel: boolean;
      showDelivery: boolean;
      showStandardWarranty: boolean;
      showPdi: boolean;
      showMechPrep: boolean;
      showBodyPrep: boolean;
      showOther: boolean;
      showError: boolean;
      showTotal: boolean;
   };
   datesSection: {
      showCustomerDestinationDeliveryDate: boolean;
      showEnterImportCentreDate: boolean;
      showShipDate: boolean;
      showExitImportCentreDate: boolean;
      showAllocationDate: boolean;
      showDateVehicleRecondition: boolean;
      showDateFactoryTransportation: boolean;
      showDateSiteArrival: boolean;
      showDateSiteTransportation: boolean;
   };
   financeProfitSection: {
      show: boolean;
      rciFinanceCommissionText: string;
      financeCommissionText: string;
      showSelectCommission: boolean;
      showProPlusCommission: boolean;
      showStandardsCommission: boolean;
   };
   addonsSection: {
      showPaintProtection: boolean;
      showWarrantyForNewCar: boolean;
   };
   showTotalProfitExludingFactoryBonusSection: boolean;
   showTotalProfitSection: boolean;

}

export interface MenuItems {
   dashboard_HasDashboard: boolean;
   dashboard_Home: boolean;
   dashboard_Overview: boolean;
   dashboard_Sales: boolean;
   dashboard_NewKPIs: boolean;
   dashboard_UsedKPIs: boolean;
   dashboard_Aftersales: boolean;
   dashboard_SiteCompare: boolean;
   orderbook: boolean;
   orderbook_HasOrderbook: boolean;
   orderbook_Retail: boolean;
   orderbook_Distrinet: boolean;
   orderbook_Fleet: boolean;
   operationalReports_HasOperationReports: boolean;
   operationalReports_DealsWeek: boolean;
   operationalReports_DealsMonth: boolean;
   operationalReports_Whiteboard: boolean;
   operationalReports_HandoverDiary: boolean;
   operationalReports_TelephoneStats: boolean;
   operationalReports_StockLanding: boolean;
   operationalReports_PerformanceTrends: boolean;
   salesReports_HasSalesReports: boolean;
   salesReports_SalesPerformance: boolean;
   salesReports_Alcopas: boolean;
   salesReports_OrderRate: boolean;
   salesReports_Registrations: boolean;
   salesReports_FAndI: boolean;
   salesReports_StockReports: boolean;
   salesReports_StockList: boolean;
   salesReports_Debtors: boolean;
   salesReports_CitNOW: boolean;
   salesReports_ImageRatios: boolean;
   salesReports_GDPR: boolean;
   salesReports_Activities: boolean;
   reportPortal: boolean;
   aftersalesReports_HasAftersalesReports: boolean;
   aftersalesReports_ServiceSales: boolean;
   aftersalesReports_ServiceBookings: boolean;
   aftersalesReports_EVHC: boolean;
   aftersalesReports_Upsells: boolean;
   aftersalesReports_WIPReport: boolean;
   aftersalesReports_Debtors: boolean;
   aftersalesReports_CitNOW: boolean;
   aftersalesReports_PartsSales: boolean;
   aftersalesReports_PartsStock: boolean;
   peopleReports_HasPeopleReports: boolean;
   peopleReports_PerformanceLeague: boolean;
   peopleReports_SalespersonEffeciency: boolean;
   peopleReports_SalespersonCommission: boolean;
   peopleReports_Scratchcard: boolean;
   peopleReports_SalesExecReview: boolean;
   vehiclePricing_HasVehiclePricing: boolean;
   vehiclePricing_ShowDetailedMenu: boolean;
   vehiclePricing_Dashboard: boolean;
   vehiclePricing_SitesLeague: boolean;
   vehiclePricing_StockReport: boolean;
   vehiclePricing_TodaysPriceChanges: boolean;
   vehiclePricing_OptedOutVehicles: boolean;
   vehiclePricing_LocationOptimiser: boolean;
   vehiclePricing_VehicleValuation: boolean;
   vehiclePricing_Home: boolean;
   vehiclePricing_BuyingOpportunities: boolean;
   vehiclePricing_LeavingVehicleTrends: boolean;
   vehiclePricing_LeavingVehicleDetail: boolean;
   vehiclePricing_StockQuickSearch: boolean;
   vehiclePricing_SiteSettings: boolean;
   userMaintenance: boolean;
}

@Injectable({
   providedIn: 'root'
})
export class EnvironmentService {

   private env: SparkEnvironment;

   constructor(
      // Unused ?
      public appConfigService: AppConfigService) {
   }

   get(): SparkEnvironment {
      if (!this.env) {
         this.loadEnvFile('dev');
      }
      return this.env;
   }


   loadEnvFile(env: string): SparkEnvironment {
      switch (env) {  // note env has been toLower() 'd
         case 'rrguk':
         case 'rrglocal':
         case 'rrgdev':
         case 'rrgtest':
         case 'rrgstage':
         case 'rrgprod': {
            this.env = environmentRRG;
            return;
         }
         case 'rrgspain':
         case 'spainlocal':
         case 'spaindev':
         case 'spaintest':
         case 'spainstage':
         case 'spainprod': {
            this.env = environmentSpain;
            return;
         }
         case 'vindis':
         case 'vindislocal':
         case 'vindisdev':
         case 'vindistest':
         case 'vindisstage':
         case 'vindisprod': {
            this.env = environmentVindis;
            return;
         }
         case 'dev': {
            this.env = environmentSpain;
            return;
         }
         case 'pinkstones':
         case 'jardine':
         case 'lmc':
         case 'autoprice':
         case 'forduk':
         case 'berry': {
            this.env = environmentAutoprice;
            return;
         }
         case 'mjmotorco': {
            this.env = environmentMJMotorCo;
            return;
         }
         case 'v12': {
            this.env = environmentV12;
            return;
         }
         case 'brindleygroup': {
            this.env = environmentBrindley;
            return;
         }
         case 'kcsofsurrey': {
            this.env = environmentKCSOfSurrey;
            return;
         }
         case 'waylandsgroup': {
            this.env = environmentWaylands;
            return;
         }
         case 'sturgessgroup': {
            this.env = environmentSturgessGroup;
            return;
         }
         case 'jjpremiumcars': {
            this.env = environmentJJPremiumCars;
            return;
         }
         case 'pentagongroup': {
            this.env = environmentPentagonGroup;
            return;
         }
         case 'hippoapproved': {
            this.env = environmentHippoApproved;
            return;
         }
         case 'lmcoffarnham': {
            this.env = environmentLMCOfFarnham;
            return;
         }
         case 'startin': {
            this.env = environmentStartin;
            return;
         }
         case 'oakwoodmotorco': {
            this.env = environmentOakwoodMotorCo;
            return;
         }
         case 'sparshattsgroup': {
            this.env = environmentSparshattsGroup;
            return;
         }

         case 'thrifty': {
            this.env = environmentThrifty;
            return;
         }

         case 'bellscrossgar': {
            this.env = environmentBellsCrossgar;
            return;
         }
         case 'croxdalegroup': {
            this.env = environmentCroxdale;
            return;
         }
         case 'emggroup': {
            this.env = environmentEMG;
            return;
         }
         case 'acorngroup': {
            this.env = environmentAcorn;
            return;
         }
         case 'lithia': {
            this.env = environmentLithia;
            return;
         }
         case 'fordsofwinsford': {
            this.env = environmentFordsOfWinsford;
            return;
         }
         case 'lsh': {
            this.env = environmentLSH;
            return;
         }
         case 'sytner': {
            this.env = environmentSytner;
            return;
         }
         case 'enterprise': {
            this.env = environmentEnterprise;
            return;
         }
         case 'sandicliffe': {
            this.env = environmentSandicliffe;
            return;
         }
         case 'nuneaton': {
            this.env = environmentNuneaton;
            return;
         }
         case 'carco': {
            this.env = environmentCarco;
            return;
         }

         default: {
            alert(`environment ${env} not found`);
         }
      }

   }
}
