import { EventEmitter, Injectable } from '@angular/core';
import { TopBottomHighlightRule } from 'src/app/model/TopBottomHighlightRule';
import { EvhcPersonRow, EvhcPersonRowRRG, EvhcSiteRow, EvhcSiteRowRRG, Month } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Injectable({
  providedIn: 'root'
})
export class EhvcService {
  topBottomHighlights:TopBottomHighlightRule[]=[]
  topBottomHighlightsEdynamix:TopBottomHighlightRule[]=[]
  topBottomHighlightsTechs:TopBottomHighlightRule[]=[]
  chosenWorkType: string;
  siteRows: EvhcSiteRow[];
  siteRowsEDynamix: EvhcSiteRowRRG[];
  chosenSiteRow: EvhcSiteRow | EvhcSiteRowRRG;
  monthStartDate: Date;
  numbersHaveChanged: EventEmitter<void>;
  personRows: EvhcPersonRow[];
  personRowsEDynamix: EvhcPersonRowRRG[];
  isInitiated: boolean;
  isAmberSelected: boolean;
  isRedSelected: boolean;
  evhcTechTitle: string;
  evhcAdvisorTitle: string;
  workTypes: string[];

  constructor(
    private constants: ConstantsService,
    private selections: SelectionsService,
    private getDataService: GetDataMethodsService
  ) { }

  initiateEvhc() {
    this.monthStartDate = new Date(this.constants.thisMonthStart)
    this.chosenWorkType = 'Red Work';
    this.numbersHaveChanged = new EventEmitter();
    this.isAmberSelected = false;
    this.isRedSelected = true;
    this.isInitiated = true;
    this.evhcTechTitle = 'Vehicle Health Checks by Tech';
    this.evhcAdvisorTitle = 'Vehicle Health Checks by Advisor';
    this.workTypes = ['Red Work', 'Amber Work', 'Red and Amber Work'];
  }

  getData(): void {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    if (this.constants.environment.evhc_eDynamixView) {
      this.getDataService.getGetEvhcSiteRowsRRG(this.monthStartDate.toISOString()).subscribe((result: EvhcSiteRowRRG[]) => {
        this.siteRowsEDynamix = result;
        this.selections.triggerSpinner.next({ show: false });
        this.numbersHaveChanged.emit();
      })
    } else {
      this.getDataService.getGetEvhcSiteRows(this.monthStartDate.toISOString(), this.isRedSelected, this.isAmberSelected).subscribe((result: EvhcSiteRow[]) => {
        this.siteRows = result;
        this.selections.triggerSpinner.next({ show: false });
        this.numbersHaveChanged.emit();
      })
    }
  }

  getSiteData(siteRow: EvhcSiteRow | EvhcSiteRowRRG) {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    let siteIds: number[] = [];
    if (siteRow.IsTotal) {
      siteIds = this.siteRows.map(x => x.SiteId);
    } else if (siteRow.IsRegion) {
      siteIds = this.constants.getSiteIdsForRegion(siteRow.RegionDescription);
    } else {
      siteIds = [siteRow.SiteId];
    }

    if (this.constants.environment.evhc_eDynamixView) {
      this.getDataService.GetEvhcPeopleRowsRRG(this.monthStartDate.toISOString(), siteIds).subscribe((result: EvhcPersonRowRRG[]) => {
        this.personRowsEDynamix = result;
        this.chosenSiteRow = siteRow;
        this.numbersHaveChanged.emit();
        this.selections.triggerSpinner.next({ show: false });
      })
    } else {
      this.getDataService.GetEvhcPeopleRows(this.monthStartDate.toISOString(), this.isRedSelected, this.isAmberSelected, siteIds).subscribe((result: EvhcPersonRow[]) => {
        this.personRows = result;
        this.chosenSiteRow = siteRow;
        this.numbersHaveChanged.emit();
        this.selections.triggerSpinner.next({ show: false });
      })
    }
  }
}
