import {AdvertHistoryAndFutureChart, AdvertHistoryAndFutureChartDTO} from "./AdvertHistoryAndFutureChart";
import {DayToSellAndPriceIndicatorScenario} from "./DayToSellAndPriceIndicatorScenario";
import {LocationOptimiserAdvert} from "./LocationOptimiserAdvert";
import {SameModelAdvert} from "./SameModelAdvert";
import {StrategyPriceBuildUp} from "./StrategyPriceBuildUp";
import {VehicleAdvertDetail, VehicleAdvertDetailDTO} from "./VehicleAdvertDetail";
import {Comment} from "./Comment";
import {CompetitorSummary} from "./CompetitorSummary";
import {PriceChangeToday} from "./PriceChangeToday";


export interface VehicleDetailModalItemDTO {
   AdvertDetail: VehicleAdvertDetailDTO;
   PriceBuildUp: StrategyPriceBuildUp[];
   SameModelAdverts: SameModelAdvert[];
   Chart: AdvertHistoryAndFutureChartDTO;
   CompetitorSummary: CompetitorSummary;
   Comments: Comment[];
   PricingScenarios: DayToSellAndPriceIndicatorScenario[];
   LocationChanges: LocationOptimiserAdvert[];
   PriceChangeToday: PriceChangeToday;
   //
}


export class VehicleDetailModalItem {
   constructor(itemIn: VehicleDetailModalItemDTO) {
      this.AdvertDetail = new VehicleAdvertDetail(itemIn.AdvertDetail);
      this.PriceBuildUp = itemIn.PriceBuildUp;
      this.SameModelAdverts = itemIn.SameModelAdverts;
      this.Chart = new AdvertHistoryAndFutureChart(itemIn.Chart);
      this.CompetitorSummary = itemIn.CompetitorSummary;
      this.Comments = itemIn.Comments.map(x => new Comment(x));
      this.PricingScenarios = itemIn.PricingScenarios;
      this.LocationChanges = itemIn.LocationChanges;
      this.PriceChangeToday = itemIn.PriceChangeToday;
   }

   AdvertDetail: VehicleAdvertDetail;
   PriceBuildUp: StrategyPriceBuildUp[];
   SameModelAdverts: SameModelAdvert[];
   Chart: AdvertHistoryAndFutureChart;
   CompetitorSummary: CompetitorSummary;
   Comments: Comment[];
   PricingScenarios: DayToSellAndPriceIndicatorScenario[];
   LocationChanges: LocationOptimiserAdvert[];
   PriceChangeToday: PriceChangeToday;
   //
}
