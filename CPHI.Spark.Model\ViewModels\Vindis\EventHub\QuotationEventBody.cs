﻿using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class QuotationEventBody
    {
        public string Id { get; set; }

        public string? LeadId { get; set; }

        public string? EnquiryId { get; set; }
        
        public string? ReferenceNumber { get; set; }
        
        public string? TransactionId { get; set; }
        
        public string DealerName { get; set; }    //List provided
        
        public CustomerEventBody? Customer { get; set; }

        public List<EventVehicleModel> Vehicles { get; set; } 

        public List<EventVehicleModel> TradeInVehicles { get; set; } 

        public List<EventOptionModel> Options { get; set; }

        public List<EventPaymentModel> Payments { get; set; }
        
        public Dictionary<string, string> Misc { get; set; }

        public string SaleType { get; set; }  //SaleType Enum
        
        public DateTime Created { get; set; }

        public DateTime? LastModified { get; set; }

        public UserModel? LastModifiedBy { get; set; }  
        
        public QuotationStatus Status { get; set; }            //QuotationStatus Enum

        public DateTime? EstimatedHandoverDate { get; set; }

        public DateTime? HandoverDate { get; set; }
        
        public PaymentType? PaymentType { get; set; }    //PaymentType Enum

        public DateTime? Presented { get; set; }
        
        public string? PresentedMethod { get; set; }
        
        public DateTime? AcceptedDate { get; set; }
        
        public EventFinanceModel? AcceptedFinanceQuotation { get; set; }
        
        public string? FinanceCompanyId { get; set; }

        public string? FinanceCompanyName { get; set; }
        
        public QuotationEventBalances Balances { get; set; }
        
        public DateTime? Deleted { get; set; }
        
        public string? LostSaleReason { get; set; }

        public DateTime? LostSale { get; set; }
        
        public string? PaymentMethod { get; set; }
        
        public string? ReferrerUrl { get; set; }
        
        public string AssignedTo { get; set; }

        public string AssignedToName { get; set; }
        
        public QuotationEventProfit? Profit { get; set; }

        public bool? VatQualifying { get; set; }
        public string SiteName { get; set; }
    }

  


}
