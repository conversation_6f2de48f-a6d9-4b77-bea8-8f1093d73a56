using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.Model.ViewModels;

namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PublicHolidaysController : ControllerBase
    {
        private readonly IPublicHolidaysService publicHolidaysService;

        public PublicHolidaysController(IPublicHolidaysService publicHolidaysService)
        {
            this.publicHolidaysService = publicHolidaysService;
        }

        [HttpPost]
        [Route("AddPublicHoliday")]
        public async Task<PublicHolidayResponse> AddPublicHoliday([FromBody] PublicHolidayRequest request)
        {
            return await publicHolidaysService.AddPublicHoliday(request);
        }


    }
}
