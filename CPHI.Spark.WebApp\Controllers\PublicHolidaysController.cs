using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PublicHolidaysController : ControllerBase
    {
        private readonly IPublicHolidaysService publicHolidaysService;
        private readonly IUserService userService;

        public PublicHolidaysController(IPublicHolidaysService publicHolidaysService, IUserService userService)
        {
            this.publicHolidaysService = publicHolidaysService;
            this.userService = userService;
        }

        [HttpPost]
        [Route("AddPublicHoliday")]
        public async Task<PublicHolidayResponse> AddPublicHoliday([FromBody] PublicHolidayRequest request)
        {
            return await publicHolidaysService.AddPublicHoliday(request);
        }


    }
}
