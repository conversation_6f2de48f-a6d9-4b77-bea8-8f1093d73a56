
import { EventEmitter, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CphPipe } from 'src/app/cph.pipe';
import { DashboardDataVNParams } from 'src/app/model/DashboardDataVNParams';
import { DashboardVNStock } from 'src/app/model/DashboardVNStock';
import { VNTileTableRow } from 'src/app/model/VNTileTableRow';
import { DistrinetService } from 'src/app/pages/distrinet/distrinet.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardMeasure } from "../../../../../model/DashboardMeasure";
import { VNTileParams } from "../../../../../model/VNTileParams";
import { DashboardService } from '../../../dashboard.service';
import { SpainDashboardNewVNService } from '../dashboardNewVNSpain.service';
import { BIChartTileDataType } from 'src/app/components/biChartTile/biChartTile.component';
import { MenuItemNew } from 'src/app/model/main.model';

@Injectable({
  providedIn: 'root'
})


export class SpainDashboardNewVNStocksPageService {



  filterChoices: DashboardMeasure[]
  highlightChoices: DashboardMeasure[]
  isStocksSelected: boolean=true;
  isBookSelected: boolean=true;

  chosenMonth: Date;

  rawData: DashboardVNStock[];
  rawDataFiltered: DashboardVNStock[];
  rawDataHighlighted: DashboardVNStock[]; //subset of filtered.  
  months: Date[]

  refreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
  refreshTileEmitter: EventEmitter<void> = new EventEmitter();
  highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();




  constructor(
    public getDataService: GetDataMethodsService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public dashboardService: DashboardService,
    public cphPipe: CphPipe,
    public distrinetPageService: DistrinetService,
    private router: Router,
    public spainDashboardService: SpainDashboardNewVNService
  ) { }



  initParams() {


    this.filterChoices = [
      { FieldName: 'Franchise', FieldNameTranslation: this.constants.translatedText.Franchises, IsDate: false, ChosenValues: [] },
      { FieldName: 'RegionDescription',FieldNameTranslation: this.constants.translatedText.Region + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Customer',FieldNameTranslation: this.constants.translatedText.Customer, IsDate: false, ChosenValues: [] },
      { FieldName: 'Ubicacion',FieldNameTranslation: 'Ubicacion', IsDate: false, ChosenValues: [] },
      { FieldName: 'ModelCode',FieldNameTranslation: this.constants.translatedText.Distrinet_ModelCode + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'EnergyType',FieldNameTranslation: this.constants.translatedText.Distrinet_EnergyType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'OriginType',FieldNameTranslation: this.constants.translatedText.Distrinet_OriginType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Version',FieldNameTranslation: this.constants.translatedText.Distrinet_Version + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'CustomerType',FieldNameTranslation: this.constants.translatedText.Distrinet_CustomerType + 's',  IsDate: false, ChosenValues: [] },
      { FieldName: 'CentroVendedor',FieldNameTranslation: 'CentroVendedor', IsDate: false, ChosenValues: [] },
      { FieldName: 'Antiquity',FieldNameTranslation: this.constants.translatedText.Distrinet_Antiquity, IsDate: false, ChosenValues: [] },
      { FieldName: 'Bloqueos',FieldNameTranslation: 'Bloqueos', IsDate: false, ChosenValues: [] },
      { FieldName: 'MasLlegada',FieldNameTranslation: 'Mes Llegada', IsDate: false, ChosenValues: [] },
      { FieldName: 'SiteDescription',FieldNameTranslation: this.constants.translatedText.Sites, IsDate: false, ChosenValues: [] },
      { FieldName: 'InvoiceDate',FieldNameTranslation: this.constants.translatedText.InvoiceDate, IsDate: true, ChosenValues: [] },
      { FieldName: 'Advance',FieldNameTranslation: this.constants.translatedText.Distrinet_Advance + 's', IsDate:false, ChosenValues: [] },
    ];

    this.highlightChoices = [
      { FieldName: 'Franchise', FieldNameTranslation: this.constants.translatedText.Franchises, IsDate: false, ChosenValues: [] },
      { FieldName: 'RegionDescription',FieldNameTranslation: this.constants.translatedText.Region + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Customer',FieldNameTranslation: this.constants.translatedText.Customer, IsDate: false, ChosenValues: [] },
      { FieldName: 'Ubicacion',FieldNameTranslation: 'Ubicacion', IsDate: false, ChosenValues: [] },
      { FieldName: 'ModelCode',FieldNameTranslation: this.constants.translatedText.Distrinet_ModelCode + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'EnergyType',FieldNameTranslation: this.constants.translatedText.Distrinet_EnergyType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'OriginType',FieldNameTranslation: this.constants.translatedText.Distrinet_OriginType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Version',FieldNameTranslation: this.constants.translatedText.Distrinet_Version + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'CustomerType',FieldNameTranslation: this.constants.translatedText.Distrinet_CustomerType + 's',  IsDate: false, ChosenValues: [] },
      { FieldName: 'CentroVendedor',FieldNameTranslation: 'CentroVendedor', IsDate: false, ChosenValues: [] },
      { FieldName: 'Antiquity',FieldNameTranslation: 'Antiquity', IsDate: false, ChosenValues: [] },
      { FieldName: 'Bloqueos',FieldNameTranslation: 'Bloqueos', IsDate: false, ChosenValues: [] },
      { FieldName: 'MasLlegada',FieldNameTranslation: 'Mes Llegada', IsDate: false, ChosenValues: [] },
      { FieldName: 'SiteDescription',FieldNameTranslation: this.constants.translatedText.Sites, IsDate: false, ChosenValues: [] },
      { FieldName: 'InvoiceDate',FieldNameTranslation: this.constants.translatedText.InvoiceDate, IsDate: true, ChosenValues: [] },
      { FieldName: 'Advance',FieldNameTranslation: this.constants.translatedText.Distrinet_Advance + 's', IsDate:false, ChosenValues: [] },
    ];

  }



  getPageParams(): VNTileParams {
    return {
      highlightChoices: this.highlightChoices,
      filterChoices: this.filterChoices,
      //rawData: this.rawData,
      //rawDataFiltered: this.rawDataFiltered,
      //rawDataHighlighted: this.rawDataHighlighted,
      filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
      highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
      updateThisPicker: this.refreshFilterListsEmitter,
      updateThisTile: this.refreshTileEmitter,
      parentMethods: {
        buildRows: (fieldName, dataType) => this.buildTableRows(fieldName, dataType),
        highlightRow: (row, fieldName) => this.highlightRow(row, fieldName),
        provideItemsList: (fieldName, isDateField) => this.provideItemsList(fieldName, isDateField),
      }
    }
  }



  highlightRow(row: VNTileTableRow, fieldName: string) {
    
    let userChoice = this.highlightChoices.find(x => x.FieldName === fieldName);
    if (fieldName == 'Antiquity'){
      let isItemSelected = userChoice.ChosenValues.length === 0 || userChoice.ChosenValues.includes(row.Label);
      if (userChoice.ChosenValues.length === 0) {
        this.highlightChoices.find(x => x.FieldName === fieldName).ChosenValues = row.HighlightsRowValuesToFilter;
      } else if (isItemSelected) {
        userChoice.ChosenValues = userChoice.ChosenValues.filter(x => !row.HighlightsRowValuesToFilter.includes(x))
      } else {
        userChoice.ChosenValues = userChoice.ChosenValues.concat(row.HighlightsRowValuesToFilter)
      }
    }
    else{
      let isItemSelected = userChoice.ChosenValues.length === 0 || userChoice.ChosenValues.includes(row.Label);
      if (userChoice.ChosenValues.length === 0) {
        this.highlightChoices.find(x => x.FieldName === fieldName).ChosenValues = [row.Label];
      } else if (isItemSelected) {
        userChoice.ChosenValues = userChoice.ChosenValues.filter(x => x !== row.Label)
      } else {
        userChoice.ChosenValues.push(row.Label)
      }
    }
    //this.pageParams.highlightChoiceHasBeenMade.emit(true);
  }

  getData(): void {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading })
    let params: DashboardDataVNParams = this.getParms(this.chosenMonth)
    this.getDataService.getDashboardVNStocksAndBook(params).subscribe((res: DashboardVNStock[]) => {
      res.map(x => {
        x.InvoiceDate = new Date(x.InvoiceDate);
      })
      let limit = 99999;
      this.rawData = res.slice(0, limit);
      this.rawDataFiltered = this.filterData(this.rawData, this.filterChoices);
      this.rawDataHighlighted = this.filterData(this.rawDataFiltered, this.highlightChoices)
      this.refreshFilterListsEmitter.emit();
      this.refreshTileEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    }, error => {
      console.error("ERROR: ", error);
    });


  }

  chooseMonth(month: Date) {
    this.chosenMonth = month;
    this.getData()
  }

 
  toggleShowStocks(){
    this.isStocksSelected = !this.isStocksSelected;
    this.getData()
  }
  toggleShowBook(){
    this.isBookSelected = !this.isBookSelected;
    this.getData()
  }

  isHighlightFiltersOn() {
    let isHighlights = false;
    let i = 0;
    while (!isHighlights && i < this.highlightChoices.length) {
      isHighlights = this.highlightChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isHighlights;
  }

  isFiltersOn() {
    if (!this.filterChoices) { return false; }
    let isFilters = false;
    let i = 0;
    while (!isFilters && i < this.filterChoices.length) {
      isFilters = this.filterChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isFilters;
  }


  private getParms(snapShot: Date): DashboardDataVNParams {
    return {
      SiteIds: this.dashboardService.chosenSites.map(x => x.SiteId),
      SnapshotDate: snapShot,
      IsBookSelected: this.isBookSelected,
      IsStocksSelected: this.isStocksSelected
    };
  }

  clearHighlights() {
    this.highlightChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.highlightItems();
    this.refreshTileEmitter.emit();
  }
  clearFilters() {
    this.filterChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.filterItems();
    this.refreshTileEmitter.emit();
  }


  filterData(dataIn: DashboardVNStock[], stringFilterChoices: DashboardMeasure[]): DashboardVNStock[] {
    let results = [];
    dataIn.forEach(item => {
      //check all chosen strings
      let filterOutThisItem: boolean = false;
      stringFilterChoices.forEach(choice => {
        if (choice.IsDate) {
          let itemPropertyAsDateString = this.cphPipe.transform(item[choice.FieldName], 'date', 0);
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(itemPropertyAsDateString)) { filterOutThisItem = true; } }
        } else {
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(item[choice.FieldName])) { filterOutThisItem = true; } }
        }
      })
      //check all chosen dates
      // if (!filterOutThisItem) {
      //   Object.keys(dateFilterChoices).forEach(key => {
      //     if (!filterOutThisItem && dateFilterChoices[key].length !== 0) { if (!dateFilterChoices[key].includes(item[key])) { filterOutThisItem = true; } }
      //   })
      // }

      if (!filterOutThisItem) { results.push(item) }
    })

    return results;
  }




  filterItems() {
    //have chosen ok from a dropdown picker
    this.rawDataFiltered = this.filterData(this.rawData, this.filterChoices)
    this.highlightItems()
  }

  highlightItems() {
    //have clicked a row in a tile.  
    this.rawDataHighlighted = this.filterData(this.rawDataFiltered, this.highlightChoices)
    this.refreshTileEmitter.emit()
  }





  navigateToDistrinetPage() {
    //initialise distrinet page
    this.distrinetPageService.initParams()
    this.distrinetPageService.chosenOriginTypes = [];
    if (this.isBookSelected){
      this.distrinetPageService.chosenOriginTypes.push('Book')
    }
    if (this.isStocksSelected){
      this.distrinetPageService.chosenOriginTypes.push('Stock')
    }
    this.distrinetPageService.chosenSnapshot = this.chosenMonth;

    //set franchise codes on distrinet page
    let filterFranchises = this.filterChoices.find(x => x.FieldName === 'Franchise').ChosenValues;
    let highlightFranchises = this.highlightChoices.find(x => x.FieldName === 'Franchise').ChosenValues;
    let chosenFranchises = this.intersectChoices(filterFranchises, highlightFranchises);
    if (chosenFranchises.length === 0) {
      this.distrinetPageService.chosenFranchises = this.constants.FranchiseCodes;
    } else {
      this.distrinetPageService.chosenFranchises = this.highlightChoices.find(x => x.FieldName === 'Franchise').ChosenValues.map(x => x.substring(0, 1).toUpperCase());
    }


    //build up filter model to set on the distrinet page
    let filterModel = {};
    //let filterChoicesNoFran = this.filterChoices.filter(x => x.FieldName !== 'Franchise');
    //let highlightChoicesNoFran = this.highlightChoices.filter(x => x.FieldName !== 'Franchise');
    this.filterChoices.forEach((choice, i) => {
      let filterChoices = choice.ChosenValues;
      let highlightChoices = this.highlightChoices[i].ChosenValues;
      let chosenItems = this.intersectChoices(filterChoices, highlightChoices);
      if (chosenItems.length > 1) {
        if (choice.IsDate){
          filterModel[choice.FieldName] = { filterType: 'date', operator: 'OR' }
        } else{
        filterModel[choice.FieldName] = { filterType: 'text', operator: 'OR' }
        }
        let i = 0;
        chosenItems.forEach(value => {
          if (choice.IsDate){
            filterModel[choice.FieldName][`condition${i + 1}`] = {
              filterType: 'date', type: 'equals', dateFrom: this.constants.deductTimezoneOffset(new Date(this.cphPipe.transform(value, 'date', 0))).toISOString().split('T')[0], dateTo: null
            }
          }
          else{
          filterModel[choice.FieldName][`condition${i + 1}`] = {
            filterType: 'text', type: 'contains', filter: value
          }
        }
          i++;
        })
      } else if (chosenItems.length > 0) {
        if (choice.IsDate){
          filterModel[choice.FieldName] = { filterType: 'date', type: 'equals', dateFrom: this.constants.deductTimezoneOffset(new Date(this.cphPipe.transform(chosenItems[0], 'date', 0))).toISOString().split('T')[0] , dateTo: null }  
        }
        else{
          filterModel[choice.FieldName] = { filterType: 'text', type: 'contains', filter: chosenItems[0] }
        }
      }
    })

    this.distrinetPageService.filterModel = filterModel;
    
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/distrinet');
    if (menuItem) { this.constants.navigateByUrl(menuItem); } //, 'operationreports'
  }

  provideItemsList(fieldName: string, isDateField: boolean) {
    if (!this.rawData) { return [] }
    
    if(isDateField){
      return [...new Set(this.rawData.map(x =>this.cphPipe.transform(x[fieldName],'date',0) ))]  
    }else{
      return [...new Set(this.rawData.map(x => x[fieldName]))]
    }
  }

  buildTableRows(fieldName: string, dataType: BIChartTileDataType) {
    let tableRows: VNTileTableRow[] = []
    if (dataType===BIChartTileDataType.day) {
      tableRows = this.buildTableRowsDatesBasis(fieldName)
    } else if (fieldName == 'Antiquity'){
      tableRows = this.buildTableRowsNonDatesBasisForAntiquity(fieldName)
    }
     else {
      tableRows = this.buildTableRowsNonDatesBasis(fieldName)
    }

    //limit to 31
    return tableRows.slice(0, 31);
  }

  buildTableRowsNonDatesBasisForAntiquity(fieldName: string): VNTileTableRow[] {

    let tableRows: VNTileTableRow[] = [];

    let labels: string[] = [];

    this.rawDataFiltered.forEach(item => {
      
      if (item.Antiquity == null) item.Antiquity = "0.00";
      let antiquityValue = parseInt(item.Antiquity);

      let itemLabel = '';
      let sortOrder = 0;
      if ( antiquityValue < 90) {itemLabel =  this.constants.translatedText.LessThan90Days; sortOrder = 0}
      else if (antiquityValue >= 90 && antiquityValue < 120 ) {itemLabel = this.constants.translatedText.GreaterThan90Days; sortOrder = 1}
      else if (antiquityValue >= 120 && antiquityValue < 150 ) {itemLabel = this.constants.translatedText.GreaterThan120Days; sortOrder = 2}
      else if (antiquityValue >= 150 && antiquityValue < 180 ) {itemLabel = this.constants.translatedText.GreaterThan150Days; sortOrder = 3}
      else if (antiquityValue >= 180 && antiquityValue < 365 ) {itemLabel = this.constants.translatedText.GreaterThan180Days; sortOrder = 4}
      else if (antiquityValue >= 365 ) {itemLabel = this.constants.translatedText.GreaterThan365Days; sortOrder = 5}

      let labelsIndex = labels.findIndex(x => x === itemLabel);
      if (labelsIndex === -1) {
        labels.push(itemLabel)
        tableRows.push({ Label: itemLabel, FilteredTotal: 1, HighlightedTotal: 0, SortOrder: sortOrder, HighlightsRowValuesToFilter: [item.Antiquity] })
      } else {
        tableRows[labelsIndex].FilteredTotal++;
        tableRows[labelsIndex].HighlightsRowValuesToFilter.push(item.Antiquity);
      }
    })

    //find out values to show
    this.rawDataHighlighted.forEach(item => {
      if (item.Antiquity == null) item.Antiquity = "0";

      let itemLabel = '';
      let sortOrder = 0;
      let antiquityValue = parseInt(item.Antiquity);
      if (antiquityValue == null) {antiquityValue = 0};


      if ( antiquityValue < 90) {itemLabel = this.constants.translatedText.LessThan90Days; sortOrder = 0}
      else if (antiquityValue >= 90 && antiquityValue < 120 ) {itemLabel = this.constants.translatedText.GreaterThan90Days; sortOrder = 1}
      else if (antiquityValue >= 120 && antiquityValue < 150 ) {itemLabel = this.constants.translatedText.GreaterThan120Days; sortOrder = 2}
      else if (antiquityValue >= 150 && antiquityValue < 180 ) {itemLabel = this.constants.translatedText.GreaterThan150Days; sortOrder = 3}
      else if (antiquityValue >= 180 && antiquityValue < 365 ) {itemLabel = this.constants.translatedText.GreaterThan180Days; sortOrder = 4}
      else if (antiquityValue >= 366 ) {itemLabel = this.constants.translatedText.GreaterThan365Days; sortOrder = 5}

      const indexInLabels = labels.findIndex(x => x === itemLabel);
      //should exist or something has gone wrong as orderDataHighlighted should be subset of orderDataFiltered
      tableRows[indexInLabels].HighlightedTotal++;
    })

    
    tableRows = tableRows.sort((a, b) => a.SortOrder - b.SortOrder)

    return tableRows;

  }

  buildTableRowsNonDatesBasis(fieldName: string): VNTileTableRow[] {
    let tableRows: VNTileTableRow[] = [];

    //go through filteredData to find unique labels and countup
    let labels: string[] = [];
    this.rawDataFiltered.forEach(item => {
      const itemLabel = item[fieldName];
      let labelsIndex = labels.findIndex(x => x === itemLabel);
      if (labelsIndex === -1) {
        //don't already have it so create new
        labels.push(itemLabel)
        tableRows.push({ Label: itemLabel, FilteredTotal: 1, HighlightedTotal: 0 })
      } else {
        tableRows[labelsIndex].FilteredTotal++;
      }
    })

    //find out values to show
    this.rawDataHighlighted.forEach(item => {
      const itemLabel = item[fieldName];
      const indexInLabels = labels.findIndex(x => x === itemLabel);
      //should exist or something has gone wrong as orderDataHighlighted should be subset of orderDataFiltered
      tableRows[indexInLabels].HighlightedTotal++;
    })

    tableRows = tableRows.sort((a, b) => b.FilteredTotal - a.FilteredTotal)

    return tableRows;

  }




  buildTableRowsDatesBasis(fieldName: string): VNTileTableRow[] {
    //similar approach to non-dates, but looks odd if we have gaps in the days, so we find the earliest date then iterate every day at a time since then even if it has no data
    let earliestDate: Date = this.rawDataFiltered.map(x => x[fieldName]).sort((a, b) => a.getTime() - b.getTime())[0];
    let latestDate: Date = this.rawDataFiltered.map(x => x[fieldName]).sort((a, b) => b.getTime() - a.getTime())[0];

    let tableRows = [];
    let currentDate = new Date(earliestDate);
    //walk through creating tableRows, with zero values
    while (!this.constants.datesAreSame(currentDate, latestDate)) {
      const label = this.cphPipe.transform(currentDate, 'date', 0);
      tableRows.push({ Label: label, FilteredTotal: 0, HighlightedTotal: 0 })
      currentDate = this.constants.addDays(currentDate, 1);
    }

    //update these new rows with orderData
    const orderDataSorted = this.rawDataFiltered.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    let currentTableRowIndex: number = 0;
    orderDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].FilteredTotal++; }
    })

    //update with highlighted data
    const highlightedDataSorted = this.rawDataHighlighted.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    currentTableRowIndex = 0;
    highlightedDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].HighlightedTotal++; }
    })
    return tableRows

  }



  private intersectChoices(filterFranchises: string[], highlightFranchises: string[]) {
    let chosenFranchises = [];
    if (filterFranchises.length !== 0) {
      if (highlightFranchises.length !== 0) {
        chosenFranchises = filterFranchises.filter(x => highlightFranchises.includes(x));
      } else {
        chosenFranchises = filterFranchises;
      }
    } else {
      if (highlightFranchises.length !== 0) {
        chosenFranchises = highlightFranchises;
      } else {
        chosenFranchises = [];
      }
    }
    return chosenFranchises;
  }
}
