﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using Dapper;
using System.Data;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.Model.ViewModels.Vindis;
using CPHI.Repository;
using Microsoft.EntityFrameworkCore;

namespace CPHI.Spark.DataAccess
{
   public interface IDealDataAccess
   {
      Task<IEnumerable<CommentVM>> GetCommentsForDeal(int dealId, bool byStockNumber, int userId);
      Task<IEnumerable<DailyOrderDetailItem>> GetDailyOrdersOrderDetails(DailyOrderDetailItemParams parms, int userId);
      Task<IEnumerable<DailyOrdersSiteDetail>> GetDailyOrdersSiteDetails(DailyOrdersSiteDetailParams parms, int userId);
      Task<IEnumerable<CommentVM>> GetDealComments(int dealId, bool byStockNumber, int userId);
      Task<DealDetailModal> GetDealDetailModal(int dealId, int userId);
      Task<IEnumerable<DealfileSentDate>> GetDealfileSentDate(string stockNumber, int userId);
      Task<IEnumerable<DealPopover>> GetDealPopover(int dealId, int userId);
      Task<IEnumerable<DealDoneThisWeek>> GetDealsDoneThisWeek(DealsDoneThisWeekParams parms, int userId);
      Task<IEnumerable<DealsForTheMonthWeek>> GetDealsForTheMonth(DealsDoneForTheMonthParams parms, int userId);
      Task<IEnumerable<DepartmentProfitPerUnitsDataItem>> GetDepartmentProfitPerUnits(string timePeriod, DealerGroupName dealerGroup);
      Task<IEnumerable<FinanceAndAddonItem>> GetFinanceAddons(FinanceAddonsParams parms, int userId);
      Task<IEnumerable<FinanceAndAddonItem>> GetFinanceAddonsForSite(FinanceAddonsForSiteParams parms, int userId);
      Task<IEnumerable<GDPRVM>> GetGDPRs(string orderTypeIds, string yearMonths, int userId);
      Task<IEnumerable<GDPRVM>> GetGDPRsForSite(string siteIds, string orderTypeIds, string yearMonths, int? salesManagerId, bool groupByManager, int userId);
      Task<IEnumerable<HandoverDiaryItem>> GetHandoverDiary(HandoverDiaryParams parms, int userId);
      Task<DateTime> GetOldestAllowableDealCache(DealerGroupName dealerGroup);
      Task<IEnumerable<OrdersSummaryBySite>> GetOrdersSummaryBySite(DateTime startDate, DateTime endDate, DealerGroupName dealerGroup);
      Task<IEnumerable<PerformanceLeagueSummaryItem>> GetPerformanceLeague(string yearMonths, string departments, int includeFleetSites, int? excludeGroupFleetSite, bool isSalesExecView, int userId);
      Task<IEnumerable<PerformanceTrendsDTO>> GetPerformanceTrends(PerformanceTrendsParams parms, DateTime fromYearMonth, DateTime toYearMonth, int userId);
      Task<IEnumerable<PerformanceTrendsDTO>> GetPerformanceTrendsForSite(PerformanceTrendsParams parms, DateTime fromYearMonth, DateTime toYearMonth, int userId, bool groupByManager);
      Task<IEnumerable<RunRateChartDTO>> GetRunRateData(RunRateParams parms, int userId);
      Task<IEnumerable<RunRateChartDTO>> GetRunRateDataVindis(RunRateParams parms);
      Task<IEnumerable<SalesActivityVM>> GetSalesActivities(string vehicleType, string yearMonths, int userId);
      Task<IEnumerable<SalesActivityVM>> GetSalesActivitiesForSite(string siteIds, string vehicleType, string yearMonths, int? salesManagerId, bool groupByManager, int userId);
      Task<IEnumerable<SalesmanEfficiencyDeal>> GetSalesmanEfficiencyDeals(DateTime monthStart, DateTime nextMonthStart, List<string> vehicletypetypes, List<string> ordertypetypes, List<string> franchises, DealerGroupName dealerGroup);
      Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceDone(SalesPerformanceParams parms, int userId);
      Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceDoneSpain(SalesPerformanceParams parms, int userId);
      Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceDoneVindis(SalesPerformanceParams parms, int userId);
      Task<IEnumerable<SalesPerformanceOrderRateMeasure>> GetSalesPerformanceOrderRate(SalesPerformanceOrderRateParams parms, int userId);
      Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceTargets(SalesPerformanceParams parms, int userId);
      Task<IEnumerable<SiteModelSellRate>> GetSiteModelSellRates(DealerGroupName dealerGroup);
      Task<IEnumerable<SuperCupRow>> GetSuperCupRows(DealerGroupName dealerGroup);
      Task<IEnumerable<WeeklyDealBreakdownDeal>> GetWeeklyDealBreakdown(DealsDoneForTheMonthWeekAnalysisParams parms, int userId);
      Task<IEnumerable<Whiteboard>> GetWhiteboard(DateTime startDay, int userId, string sites, string orderTypes, string vehicleTypes, string franchises, int onlyLates, int excludeLates, bool byStockNumber, int? managerId);
      Task<int> UpdateDealfileSentDate(UpdateDealfileSentParams parms, DealerGroupName dealerGroup);
      Task<List<PersonAndDealCount>> GetOrdersForSalesIncentive(int userId);
      Task<IEnumerable<SalesmanWithId>> GetVindisExecsForSiteForMonth(int siteId, int year, int month);
      Task<int> UpdateSalesmanId(UpdateSalesmanIdParams parms, int userId);
      Task<IEnumerable<FinanceInsuranceTargetVM>> GetFinanceInsuranceTargets(string yearMonths, bool includeNewTargets, bool includeUsedTargets, int userId);
        Task<Deal?> GetDealByEnquiryNumber(string enquiryNumber);
        Task InsertNewDeal(Deal incomingDeal);
        Task AddDiffs(List<Diff> diffsToPersist);
        Task<List<VehicleType>> GetVehicleTypes(int dealerGroupId);
        Task<List<OrderType>> GetOrderTypes(int dealerGroupId);
        Task<List<Site>> GetSites(int dealerGroupId);
        Task UpdateDeal(Deal deal);
   }

   public class DealDataAccess : IDealDataAccess
   {
      private readonly string _connectionString;


      public DealDataAccess(string connectionString)
      {
         _connectionString = connectionString;
      }

      //UnifiedDB - SP updated
      public async Task<IEnumerable<SalesmanWithId>> GetVindisExecsForSiteForMonth(int siteId, int year, int month)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("siteId", siteId);
            paramList.Add("year", year);
            paramList.Add("month", month);

            return await dapper.GetAllAsync<SalesmanWithId>("dbo.GET_VindisExecsForSiteForMonth", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - no update required
      public async Task<IEnumerable<SiteModelSellRate>> GetSiteModelSellRates(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("DealerGroupId", (int)dealerGroup);

            return await dapper.GetAllAsync<SiteModelSellRate>("autoprice.GET_SiteModelSellRates", paramList, CommandType.StoredProcedure);
         }
      }


      //UnifiedDB - SP updated
      public async Task<IEnumerable<DealDoneThisWeek>> GetDealsDoneThisWeek(DealsDoneThisWeekParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("StartDay", parms.StartDay);
            paramList.Add("UserId", userId);
            paramList.Add("Sites", parms.Sites);
            paramList.Add("OrderTypes", parms.OrderTypes);
            paramList.Add("VehicleTypes", parms.VehicleTypes);
            paramList.Add("Franchises", parms.Franchises);

            return await dapper.GetAllAsync<DealDoneThisWeek>("dbo.GET_DealsDoneThisWeek", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<DateTime> GetOldestAllowableDealCache(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("DealerGroupId", (int)dealerGroup);

            var res = await dapper.GetAsync<DateTime>("dbo.GET_OldestAllowableDealCache", paramList);
            return res;
         }
      }

      //UnifiedDB - SP updated
      public async Task<IEnumerable<DealsForTheMonthWeek>> GetDealsForTheMonth(DealsDoneForTheMonthParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("StartDay", parms.StartDate);
            paramList.Add("UserId", userId);
            paramList.Add("Sites", parms.SiteIds);
            paramList.Add("OrderTypes", parms.OrderTypes);
            paramList.Add("VehicleTypes", parms.VehicleTypes);
            paramList.Add("Franchises", parms.Franchises);
            paramList.Add("OnlyLates", parms.OnlyLates == true ? 1 : 0);
            paramList.Add("ExcludeLates", parms.ExcludeLates == true ? 1 : 0);
            paramList.Add("OnlyOrders", parms.OnlyOrders == true ? 1 : 0);
            paramList.Add("ExcludeOrders", parms.ExcludeOrders == true ? 1 : 0);

            return await dapper.GetAllAsync<DealsForTheMonthWeek>("dbo.GET_DealsDoneThisMonth", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<SuperCupRow>> GetSuperCupRows(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("DealerGroupId", (int)dealerGroup);

            return await dapper.GetAllAsync<SuperCupRow>("dbo.GET_SuperCup", paramList, CommandType.StoredProcedure);
         }
      }

      //public async Task<IEnumerable<SuperCupRow>> GetSuperCupTwoRows()
      //{
      //    return await dapper.GetAllAsync<SuperCupRow>("dbo.GET_SuperCupTwo", null, CommandType.StoredProcedure);
      //}

      //UnifiedDB - SP Updated
      public async Task<IEnumerable<WeeklyDealBreakdownDeal>> GetWeeklyDealBreakdown(DealsDoneForTheMonthWeekAnalysisParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("StartDay", parms.StartDate);
            paramList.Add("UserId", userId);
            paramList.Add("Sites", parms.SiteIds);
            paramList.Add("OrderTypes", parms.OrderTypes);
            paramList.Add("VehicleTypes", parms.VehicleTypes);
            paramList.Add("Franchises", parms.Franchises);
            paramList.Add("OnlyLates", parms.OnlyLates == true ? 1 : 0);
            paramList.Add("ExcludeLates", parms.ExcludeLates == true ? 1 : 0);
            paramList.Add("OnlyOrders", parms.OnlyOrders == true ? 1 : 0);
            paramList.Add("ExcludeOrders", parms.ExcludeOrders == true ? 1 : 0);
            paramList.Add("BroughtIn", parms.BroughtIn == true ? 1 : 0);
            paramList.Add("MTD", parms.Mtd == true ? 1 : 0);

            return await dapper.GetAllAsync<WeeklyDealBreakdownDeal>("dbo.GET_WeeklyDealBreakDown", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<DealPopover>> GetDealPopover(int dealId, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("DealId", dealId);
            paramList.Add("UserId", userId);
            return await dapper.GetAllAsync<DealPopover>("dbo.GET_DealPopover", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<CommentVM>> GetDealComments(int dealId, bool byStockNumber, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("DealId", dealId);
            paramList.Add("ByStockNumber", byStockNumber);
            paramList.Add("UserId", userId);
            return await dapper.GetAllAsync<CommentVM>("dbo.GET_DealComments", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<OrdersSummaryBySite>> GetOrdersSummaryBySite(DateTime startDate, DateTime endDate, DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            return await dapper.GetAllAsync<OrdersSummaryBySite>("dbo.GET_OrdersSummaryBySite", new DynamicParameters(new { startDate, endDate, dealerGroupId = (int)dealerGroup }));
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<DepartmentProfitPerUnitsDataItem>> GetDepartmentProfitPerUnits(string timePeriod, DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            return await dapper.GetAllAsync<DepartmentProfitPerUnitsDataItem>("dbo.GET_DepartmentProfitPerUnits", new DynamicParameters(new { timePeriod, dealerGroupId = (int)dealerGroup }));
         }
      }

      //UnifiedDB - SP updated
      public async Task<IEnumerable<Whiteboard>> GetWhiteboard(DateTime startDay, int userId, string sites, string orderTypes, string vehicleTypes,
          string franchises, int onlyLates, int excludeLates, bool byStockNumber, int? managerId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("StartDay", startDay);
            paramList.Add("UserId", userId);
            paramList.Add("Sites", sites);
            paramList.Add("OrderTypes", orderTypes);
            paramList.Add("VehicleTypes", vehicleTypes);
            paramList.Add("Franchises", franchises);
            paramList.Add("OnlyLates", onlyLates);
            paramList.Add("ExcludeLates", excludeLates);
            paramList.Add("ByStockNumber", byStockNumber);
            paramList.Add("ManagerId", managerId);

            return await dapper.GetAllAsync<Whiteboard>("dbo.GET_Whiteboard", paramList, CommandType.StoredProcedure);
         }
      }


      //UnifiedDB - SP updated
      public Task<IEnumerable<DailyOrdersSiteDetail>> GetDailyOrdersSiteDetails(DailyOrdersSiteDetailParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string siteIds = string.Join(",", parms.SiteIds);
            return dapper.GetAllAsync<DailyOrdersSiteDetail>("dbo.GET_DailyOrdersSiteDetail", new DynamicParameters(new
            {
               startDate = parms.StartDate,
               endDate = parms.EndDate.Date,
               departmentName = parms.Department,
               siteIds,
               userId
            }));
         }
      }

      //UnifiedDB - updated
      public Task<IEnumerable<DailyOrderDetailItem>> GetDailyOrdersOrderDetails(DailyOrderDetailItemParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string siteIds = string.Join(",", parms.SiteIds);
            return dapper.GetAllAsync<DailyOrderDetailItem>("dbo.GET_DailyOrdersOrderDetail", new DynamicParameters(new
            {
               startDate = parms.StartDate,
               endDate = parms.EndDate.Date,
               isOrder = parms.IsOrder,
               siteIds,
               department = parms.Department,
               includeOrdersAndCancellations = parms.IncludeOrdersAndCancellations,
               userId
            }));
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<FinanceInsuranceTargetVM>> GetFinanceInsuranceTargets(string yearMonth, bool includeNewTargets, bool includeUsedTargets, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {

            var paramList = new DynamicParameters();

            paramList.Add("IncludeNewTargets", includeNewTargets);
            paramList.Add("IncludeUsedTargets", includeUsedTargets);
            paramList.Add("YearMonths", yearMonth);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<FinanceInsuranceTargetVM>("dbo.GET_FinanceInsuranceAndUnitTargets", paramList, CommandType.StoredProcedure);
         }
      }



      //UnifiedDB - SP updated
      public async Task<IEnumerable<FinanceAndAddonItem>> GetFinanceAddons(FinanceAddonsParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {

            var paramList = new DynamicParameters();

            string vehicleTypes = string.Join(",", parms.VehicleTypeIds);
            string orderTypes = string.Join(",", parms.OrderTypeIds);

            paramList.Add("YearMonths", parms.YearMonths);
            paramList.Add("UserId", userId);
            paramList.Add("Sites", parms.Sites);
            paramList.Add("OrderTypes", orderTypes);
            paramList.Add("VehicleTypes", vehicleTypes);
            paramList.Add("Franchises", parms.Franchises);
            paramList.Add("ShowAllSites", parms.ShowAllSites);

            // Excludes Service Plan & Warranty from the PPU 
            //paramList.Add("Franchises", excludeServicePlanAndWarranty);

            return await dapper.GetAllAsync<FinanceAndAddonItem>("dbo.GET_FinanceAddons", paramList, CommandType.StoredProcedure);
         }
      }


      //UnifiedDB - SP updated
      public async Task<IEnumerable<FinanceAndAddonItem>> GetFinanceAddonsForSite(FinanceAddonsForSiteParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            string vehicleTypes = string.Join(",", parms.VehicleTypeIds);
            string orderTypes = string.Join(",", parms.OrderTypeIds);

            paramList.Add("YearMonths", parms.YearMonths);
            paramList.Add("UserId", userId);
            paramList.Add("SiteIds", parms.Sites);
            paramList.Add("OrderTypes", orderTypes);
            paramList.Add("VehicleTypes", vehicleTypes);
            paramList.Add("Franchises", parms.Franchises);
            paramList.Add("SalesManagerId", parms.SalesManagerId);

            return await dapper.GetAllAsync<FinanceAndAddonItem>("dbo.GET_FinanceAddonsForSite", paramList, CommandType.StoredProcedure);

         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<PerformanceLeagueSummaryItem>> GetPerformanceLeague(string yearMonths, string departments, int includeFleetSites, int? excludeGroupFleetSite, bool isSalesExecView, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("YearMonths", yearMonths);
            paramList.Add("Departments", departments);
            paramList.Add("IncludeFleetSites", includeFleetSites);
            paramList.Add("ExcludeGroupFleetSite", excludeGroupFleetSite != null ? excludeGroupFleetSite : null);
            paramList.Add("IsSalesExecView", isSalesExecView);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<PerformanceLeagueSummaryItem>("dbo.GET_PerformanceLeague", paramList, CommandType.StoredProcedure);
         }
      }

      private string GetDepartment(string reportName)
      {
         if (reportName == "New Retail") { return "New"; }
         if (reportName == "New") { return "New"; }
         if (reportName == "Corporate 1") { return "Fleet"; }
         if (reportName == "Fleet") { return "Fleet"; }
         if (reportName == "Used Retail 1") { return "Used"; }
         if (reportName == "Used") { return "Used"; }

         return "All";

      }

      //UnifiedDB - updated.
      public async Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceTargets(SalesPerformanceParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            DateTime DeliveryDateStartAmended = parms.IsVsBudget ? parms.DeliveryDateStart : parms.DeliveryDateStart.AddYears(-1);
            //DateTime DeliveryDateEndAmended = parms.IsVsBudget ? parms.DeliveryDateEnd : parms.DeliveryDateEnd.AddYears(-1);

            string franchiseCsv = string.Join(",", parms.Franchises);

            paramList.Add("Department", parms.Department);
            paramList.Add("IncludeMotab", parms.IncludeMotab);
            paramList.Add("IsVsBudget", parms.IsVsBudget);
            paramList.Add("DeliveryDateStart", DeliveryDateStartAmended.Date);
            paramList.Add("Franchises", franchiseCsv);
            paramList.Add("UserId", userId);

            var res = await dapper.GetAllAsync<SalesPerformanceMeasure>("dbo.GET_SalesPerformanceTargets", paramList, CommandType.StoredProcedure);
            return res;
         }
      }

      //UnifiedDB - updated.
      public async Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceDone(SalesPerformanceParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            string franchiseCsv = string.Join(",", parms.Franchises);
            int includeTrade = parms.IncludeTrade ? 1 : 0;
            int includeMotab = parms.IncludeMotab ? 1 : 0;
            int onlyMotab = parms.OnlyMotab ? 1 : 0;

            paramList.Add("Department", parms.Department);
            paramList.Add("OrderDateStart", parms.OrderDateStart);
            paramList.Add("OrderDateEnd", parms.OrderDateEnd);
            paramList.Add("DeliveryDateStart", parms.DeliveryDateStart);
            paramList.Add("DeliveryDateEnd", parms.DeliveryDateEnd.Date);
            paramList.Add("DeliveryDateType", parms.DeliveryDateType);
            paramList.Add("Franchises", franchiseCsv);
            paramList.Add("IncludeLateCosts", parms.IncludeLateCosts ? 1 : 0);
            paramList.Add("IncludeNonLateCosts", parms.IncludeNonLateCosts ? 1 : 0);
            paramList.Add("IncludeOrders", parms.IncludeOrders ? 1 : 0);
            paramList.Add("IncludeNonOrders", parms.IncludeNonOrders ? 1 : 0);
            paramList.Add("IncludeTrade", includeTrade);
            paramList.Add("IncludeMotab", includeMotab);
            paramList.Add("OnlyMotab", onlyMotab);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<SalesPerformanceMeasure>("dbo.GET_SalesPerformanceDone", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated.
      public async Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceDoneSpain(SalesPerformanceParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            string franchiseCsv = string.Join(",", parms.Franchises);

            int includeTrade = parms.IncludeTrade ? 1 : 0;

            paramList.Add("Department", parms.Department);
            paramList.Add("OrderDateStart", parms.OrderDateStart);
            paramList.Add("OrderDateEnd", parms.OrderDateEnd);
            paramList.Add("DeliveryDateStart", parms.DeliveryDateStart);
            paramList.Add("DeliveryDateEnd", parms.DeliveryDateEnd.Date);
            paramList.Add("DeliveryDateType", parms.DeliveryDateType);
            paramList.Add("Franchises", franchiseCsv);
            paramList.Add("IncludeLateCosts", parms.IncludeLateCosts ? 1 : 0);
            paramList.Add("IncludeNonLateCosts", parms.IncludeNonLateCosts ? 1 : 0);
            paramList.Add("IncludeOrders", parms.IncludeOrders ? 1 : 0);
            paramList.Add("IncludeNonOrders", parms.IncludeNonOrders ? 1 : 0);
            paramList.Add("IncludeTrade", includeTrade);

            return await dapper.GetAllAsync<SalesPerformanceMeasure>("dbo.GET_SalesPerformanceDoneSpain", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated.
      public async Task<IEnumerable<SalesPerformanceMeasure>> GetSalesPerformanceDoneVindis(SalesPerformanceParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            string franchiseCsv = string.Join(",", parms.Franchises);

            int includeTrade = parms.IncludeTrade ? 1 : 0;
            int includeMotab = parms.IncludeMotab ? 1 : 0;
            int onlyMotab = parms.OnlyMotab ? 1 : 0;

            if (onlyMotab == 1)
            {
               includeMotab = 1;
            }

            paramList.Add("Department", parms.Department);
            paramList.Add("OrderDateStart", parms.OrderDateStart);
            paramList.Add("OrderDateEnd", parms.OrderDateEnd);
            paramList.Add("DeliveryDateStart", parms.DeliveryDateStart);
            paramList.Add("DeliveryDateEnd", parms.DeliveryDateEnd.Date);
            paramList.Add("DeliveryDateType", parms.DeliveryDateType);
            paramList.Add("Franchises", franchiseCsv);
            paramList.Add("IncludeLateCosts", parms.IncludeLateCosts ? 1 : 0);
            paramList.Add("IncludeNonLateCosts", parms.IncludeNonLateCosts ? 1 : 0);
            paramList.Add("IncludeOrders", parms.IncludeOrders ? 1 : 0);
            paramList.Add("IncludeNonOrders", parms.IncludeNonOrders ? 1 : 0);
            paramList.Add("IncludeTrade", includeTrade);
            paramList.Add("IncludeMotab", includeMotab);
            paramList.Add("OnlyMotab", onlyMotab);

            return await dapper.GetAllAsync<SalesPerformanceMeasure>("dbo.GET_SalesPerformanceDoneVindis", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<SalesPerformanceOrderRateMeasure>> GetSalesPerformanceOrderRate(SalesPerformanceOrderRateParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            string department = GetDepartment(parms.Department);
            string franchiseCsv = string.Join(",", parms.Franchises);

            int includeTrade = parms.IncludeTrade ? 1 : 0;
            int includeMotab = parms.IncludeMotab ? 1 : 0;

            paramList.Add("Department", department);
            paramList.Add("OrderMonthStart", new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1));
            paramList.Add("DeliveryMonthStart", parms.DeliveryMonthStart.Date);
            paramList.Add("Franchises", franchiseCsv);
            paramList.Add("IncludeLateCosts", parms.IncludeLateCosts ? 1 : 0);
            paramList.Add("IncludeNonLateCosts", parms.IncludeNonLateCosts ? 1 : 0);
            paramList.Add("IncludeOrders", parms.IncludeOrders ? 1 : 0);
            paramList.Add("IncludeNonOrders", parms.IncludeNonOrders ? 1 : 0);
            paramList.Add("IncludeTrade", includeTrade);
            paramList.Add("IncludeMotab", includeMotab);

            return await dapper.GetAllAsync<SalesPerformanceOrderRateMeasure>("dbo.GET_SalesPerformanceOrderRate", paramList, CommandType.StoredProcedure);
         }
      }


      //UnifiedDB - updated
      public async Task<IEnumerable<RunRateChartDTO>> GetRunRateData(RunRateParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("IsUnits", parms.IsUnits);
            paramList.Add("IsVsLastYr", parms.IsVsLastYr);
            paramList.Add("IncludeTrade", parms.IncludeTrade);
            paramList.Add("IncludeLateCosts", parms.IncludeLateCosts);
            paramList.Add("IncludeOrders", parms.IncludeOrders);
            paramList.Add("SiteIds", string.Join(',', parms.SiteIds));
            paramList.Add("AccountingMonthStart", parms.AccountingMonthStart);
            paramList.Add("Department", parms.Department);
            paramList.Add("FranchiseCodes", string.Join(',', parms.FranchiseCodes));
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<RunRateChartDTO>("dbo.GET_RunRateData", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - sp updated
      public async Task<IEnumerable<RunRateChartDTO>> GetRunRateDataVindis(RunRateParams parms)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("IsUnits", parms.IsUnits);
            paramList.Add("IsVsLastYr", parms.IsVsLastYr);
            paramList.Add("IncludeTrade", parms.IncludeTrade);
            paramList.Add("IncludeLateCosts", parms.IncludeLateCosts);
            paramList.Add("IncludeOrders", parms.IncludeOrders);
            paramList.Add("SiteIds", string.Join(',', parms.SiteIds));
            paramList.Add("AccountingMonthStart", parms.AccountingMonthStart);
            paramList.Add("Department", parms.Department);
            paramList.Add("FranchiseCodes", string.Join(',', parms.FranchiseCodes));
            return await dapper.GetAllAsync<RunRateChartDTO>("dbo.GET_RunRateDataVindis", paramList, CommandType.StoredProcedure);
         }
      }


      //UnifiedDB - updated
      public async Task<IEnumerable<SalesmanEfficiencyDeal>> GetSalesmanEfficiencyDeals(DateTime monthStart, DateTime nextMonthStart, List<string> vehicletypetypes, List<string> ordertypetypes, List<string> franchises, DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string vTypesString = string.Join(',', vehicletypetypes.Distinct());
            string otypesString = string.Join(',', ordertypetypes);
            string fransString = string.Join(',', franchises);


            return await dapper.GetAllAsync<SalesmanEfficiencyDeal>("dbo.GET_SalesmanEfficiencyDeals", new DynamicParameters(new { monthStart, nextMonthStart, vehicletypetypes = vTypesString, ordertypetypes = otypesString, franchises = fransString, dealerGroupId = (int)dealerGroup }));
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<HandoverDiaryItem>> GetHandoverDiary(HandoverDiaryParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("UserId", userId);
            paramList.Add("StartDate", parms.StartDate);
            paramList.Add("OrderTypes", parms.OrderTypes);
            paramList.Add("VehicleTypes", parms.VehicleTypes);
            paramList.Add("Franchises", parms.Franchises);
            paramList.Add("Sites", parms.Sites);

            if (parms.SalesmanIds.Length > 0)
            {
               paramList.Add("Salesmen", parms.SalesmanIds);
            }
            else
            {
               paramList.Add("Salesmen", null);
            }

            if (parms.ManagerIds?.Length > 0)
            {
               paramList.Add("Manager", parms.ManagerIds);
            }
            else
            {
               paramList.Add("Manager", null);
            }


            return await dapper.GetAllAsync<HandoverDiaryItem>("dbo.GET_HandoverDiary", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - SP updated
      public async Task<DealDetailModal> GetDealDetailModal(int dealId, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            return await dapper.GetAsync<DealDetailModal>("dbo.GET_DealDetailModal", new DynamicParameters(new { dealId, userId }));
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<CommentVM>> GetCommentsForDeal(int dealId, bool byStockNumber, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            return await dapper.GetAllAsync<CommentVM>("dbo.GET_CommentsForDeal", new DynamicParameters(new { dealId, byStockNumber, userId }));
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<SalesActivityVM>> GetSalesActivities(string vehicleType, string yearMonths, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string vehicleTypeIdStr = "";

            if (vehicleType == "New") { vehicleTypeIdStr = "1"; }
            else if (vehicleType == "Used") { vehicleTypeIdStr = "2"; }
            else if (vehicleType == "NewUsed") { vehicleTypeIdStr = "1,2"; }

            var paramList = new DynamicParameters();

            paramList.Add("vehicleTypeIds", vehicleTypeIdStr);
            paramList.Add("YearMonths", yearMonths);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<SalesActivityVM>("dbo.GET_SalesActivities", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<SalesActivityVM>> GetSalesActivitiesForSite(string siteIds, string vehicleType, string yearMonths, int? salesManagerId, bool groupByManager, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            string vehicleTypeIdStr = "";

            if (vehicleType == "New") { vehicleTypeIdStr = "1"; }
            else if (vehicleType == "Used") { vehicleTypeIdStr = "2"; }
            else if (vehicleType == "NewUsed") { vehicleTypeIdStr = "1,2"; }

            var paramList = new DynamicParameters();

            paramList.Add("vehicleTypeIds", vehicleTypeIdStr);
            paramList.Add("YearMonths", yearMonths);
            paramList.Add("siteIds", siteIds);
            paramList.Add("SalesManagerId", salesManagerId);
            paramList.Add("GroupByManager", groupByManager);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<SalesActivityVM>("dbo.GET_SalesActivitiesForSite", paramList, CommandType.StoredProcedure);
         }

      }

      //UnifiedDB - updated
      public async Task<IEnumerable<GDPRVM>> GetGDPRs(string orderTypeIds, string yearMonths, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("orderTypeIds", orderTypeIds);
            paramList.Add("YearMonths", yearMonths);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<GDPRVM>("dbo.GET_GDPRs", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<GDPRVM>> GetGDPRsForSite(string siteIds, string orderTypeIds, string yearMonths, int? salesManagerId, bool groupByManager, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("orderTypeIds", orderTypeIds);
            paramList.Add("YearMonths", yearMonths);
            paramList.Add("siteIds", siteIds);
            paramList.Add("SalesManagerId", salesManagerId);
            paramList.Add("GroupByManager", groupByManager);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<GDPRVM>("dbo.GET_GDPRsForSite", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - updated
      public async Task<IEnumerable<DealfileSentDate>> GetDealfileSentDate(string stockNumber, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("stockNumber", stockNumber);
            paramList.Add("userId", userId);

            return await dapper.GetAllAsync<DealfileSentDate>("dbo.GET_DealfileSentDate", paramList, CommandType.StoredProcedure);
         }
      }

      //UnifiedDB - TODO: SP update required
      public async Task<int> UpdateDealfileSentDate(UpdateDealfileSentParams parms, Model.DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            paramList.Add("stockNumber", parms.StockNumber);
            paramList.Add("dateTime", parms.FileDate);

            return await dapper.GetAsync<int>("dbo.UPDATE_DealfileSentDate", paramList, System.Data.CommandType.StoredProcedure);
         }

      }

      //UnifiedDB - TODO: SP update required
      public async Task<int> UpdateQualifyingPartEx(UpdateQualifyingPartExParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            DateTime now = DateTime.UtcNow;

            paramList.Add("dealId", parms.DealId);
            paramList.Add("stockNumber", parms.StockNumber);
            paramList.Add("userId", userId);
            paramList.Add("isQualifying", parms.IsQualifying);
            paramList.Add("dateTime", now);

            return await dapper.GetAsync<int>("dbo.UPDATE_QualifyingPartEx", paramList, System.Data.CommandType.StoredProcedure);
         }

      }


      //UnifiedDB - updated
      public async Task<int> UpdateSalesmanId(UpdateSalesmanIdParams parms, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();

            DateTime now = DateTime.UtcNow;

            paramList.Add("dealId", parms.DealId);
            paramList.Add("newSalesmanId", parms.NewSalesmanId);
            paramList.Add("userId", userId);

            return await dapper.GetAsync<int>("dbo.UPDATE_SalesmanIdForDeal", paramList, System.Data.CommandType.StoredProcedure);
         }

      }

      //UnifiedDB - SP updated
      public async Task<IEnumerable<PerformanceTrendsDTO>> GetPerformanceTrends(PerformanceTrendsParams parms, DateTime fromYearMonth, DateTime toYearMonth, int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {

            var paramList = new DynamicParameters();
            paramList.Add("fromYearMonth", new DateTime(fromYearMonth.Year, fromYearMonth.Month, 1));
            paramList.Add("toYearMonth", new DateTime(toYearMonth.Year, toYearMonth.Month, 1).AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd 23:59:59"));
            //paramList.Add("sites", parms.Sites);
            paramList.Add("vehicleTypes", parms.VehicleTypes);
            paramList.Add("orderTypes", parms.OrderTypes);
            paramList.Add("ShowAllSites", parms.ShowAllSites);
            paramList.Add("userId", userId);
            paramList.Add("orders", parms.measure == PerformanceTrendsMeasure.Order);
            paramList.Add("delivery", parms.measure == PerformanceTrendsMeasure.Delivery);
            paramList.Add("chassisProfit", parms.measure == PerformanceTrendsMeasure.ChassisProfit);
            paramList.Add("chassisProfitPU", parms.measure == PerformanceTrendsMeasure.ChassisProfitPU);
            paramList.Add("financeProfit", parms.measure == PerformanceTrendsMeasure.FinanceProfit);
            paramList.Add("financeProfitPU", parms.measure == PerformanceTrendsMeasure.FinanceProfitPU);
            paramList.Add("financePT", parms.measure == PerformanceTrendsMeasure.FinancePT);

            paramList.Add("addOnProfit", parms.measure == PerformanceTrendsMeasure.AddOnProfit);
            paramList.Add("addOnProfitPU", parms.measure == PerformanceTrendsMeasure.AddOnProfitPU);

            paramList.Add("cosmeticSales", parms.measure == PerformanceTrendsMeasure.CosmeticSales);
            paramList.Add("cosmeticSalesPU", parms.measure == PerformanceTrendsMeasure.CosmeticSalesPU);
            paramList.Add("cosmeticSalesPT", parms.measure == PerformanceTrendsMeasure.CosmeticSalesPT);

            paramList.Add("paintProtectionSales", parms.measure == PerformanceTrendsMeasure.PaintProtectionSales);
            paramList.Add("paintProtectionSalesPU", parms.measure == PerformanceTrendsMeasure.PaintProtectionSalesPU);
            paramList.Add("paintProtectionSalesPT", parms.measure == PerformanceTrendsMeasure.PaintProtectionSalesPT);

            paramList.Add("gapSales", parms.measure == PerformanceTrendsMeasure.GapSales);
            paramList.Add("gapSalesPU", parms.measure == PerformanceTrendsMeasure.GapSalesPU);
            paramList.Add("gapSalesPT", parms.measure == PerformanceTrendsMeasure.GapSalesPT);

            paramList.Add("servicePlansSales", parms.measure == PerformanceTrendsMeasure.ServicePlansSales);
            paramList.Add("servicePlansSalesPU", parms.measure == PerformanceTrendsMeasure.ServicePlansSalesPU);
            paramList.Add("servicePlansSalesPT", parms.measure == PerformanceTrendsMeasure.ServicePlansSalesPT);

            paramList.Add("productsPU", parms.measure == PerformanceTrendsMeasure.ProductsPerUnit);

            paramList.Add("ads", parms.measure == PerformanceTrendsMeasure.AvgDaysToSales);
            paramList.Add("add", parms.measure == PerformanceTrendsMeasure.AvgDaysToDeliver);

            paramList.Add("redWorkId", parms.measure == PerformanceTrendsMeasure.RedWorkId);
            paramList.Add("redWorkSold", parms.measure == PerformanceTrendsMeasure.RedWorkSold);
            paramList.Add("amberWorkId", parms.measure == PerformanceTrendsMeasure.AmberWorkId);
            paramList.Add("amberWorkSold", parms.measure == PerformanceTrendsMeasure.AmberWorkSold);
            paramList.Add("citNowSent", parms.measure == PerformanceTrendsMeasure.CitNowSent);
            paramList.Add("citNowViewed", parms.measure == PerformanceTrendsMeasure.CitNowViewed);
            paramList.Add("labourHoursSold", parms.measure == PerformanceTrendsMeasure.LabourHoursSold);
            paramList.Add("labourSales", parms.measure == PerformanceTrendsMeasure.LabourSales);
            paramList.Add("retailRecoveryRate", parms.measure == PerformanceTrendsMeasure.RetailRecoveryRate);


            return await dapper.GetAllAsync<PerformanceTrendsDTO>("dbo.GET_PerformanceTrends", paramList, CommandType.StoredProcedure);
         }

      }

      //UnifiedDB - SP updated
      public async Task<IEnumerable<PerformanceTrendsDTO>> GetPerformanceTrendsForSite(PerformanceTrendsParams parms, DateTime fromYearMonth, DateTime toYearMonth, int userId, bool groupByManager)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("fromYearMonth", new DateTime(fromYearMonth.Year, fromYearMonth.Month, 1));
            paramList.Add("toYearMonth", new DateTime(toYearMonth.Year, toYearMonth.Month, 1).AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd 23:59:59"));
            paramList.Add("siteIds", parms.SiteIds);
            paramList.Add("vehicleTypes", parms.VehicleTypes);
            paramList.Add("orderTypes", parms.OrderTypes);
            paramList.Add("userId", userId);
            paramList.Add("salesManagerId", parms.salesManagerId);
            paramList.Add("groupByManager", groupByManager);
            paramList.Add("orders", parms.measure == PerformanceTrendsMeasure.Order);
            paramList.Add("delivery", parms.measure == PerformanceTrendsMeasure.Delivery);
            paramList.Add("chassisProfit", parms.measure == PerformanceTrendsMeasure.ChassisProfit);
            paramList.Add("chassisProfitPU", parms.measure == PerformanceTrendsMeasure.ChassisProfitPU);
            paramList.Add("financeProfit", parms.measure == PerformanceTrendsMeasure.FinanceProfit);
            paramList.Add("financeProfitPU", parms.measure == PerformanceTrendsMeasure.FinanceProfitPU);
            paramList.Add("financePT", parms.measure == PerformanceTrendsMeasure.FinancePT);

            paramList.Add("addOnProfit", parms.measure == PerformanceTrendsMeasure.AddOnProfit);
            paramList.Add("addOnProfitPU", parms.measure == PerformanceTrendsMeasure.AddOnProfitPU);

            paramList.Add("cosmeticSales", parms.measure == PerformanceTrendsMeasure.CosmeticSales);
            paramList.Add("cosmeticSalesPU", parms.measure == PerformanceTrendsMeasure.CosmeticSalesPU);
            paramList.Add("cosmeticSalesPT", parms.measure == PerformanceTrendsMeasure.CosmeticSalesPT);

            paramList.Add("paintProtectionSales", parms.measure == PerformanceTrendsMeasure.PaintProtectionSales);
            paramList.Add("paintProtectionSalesPU", parms.measure == PerformanceTrendsMeasure.PaintProtectionSalesPU);
            paramList.Add("paintProtectionSalesPT", parms.measure == PerformanceTrendsMeasure.PaintProtectionSalesPT);

            paramList.Add("gapSales", parms.measure == PerformanceTrendsMeasure.GapSales);
            paramList.Add("gapSalesPU", parms.measure == PerformanceTrendsMeasure.GapSalesPU);
            paramList.Add("gapSalesPT", parms.measure == PerformanceTrendsMeasure.GapSalesPT);

            paramList.Add("servicePlansSales", parms.measure == PerformanceTrendsMeasure.ServicePlansSales);
            paramList.Add("servicePlansSalesPU", parms.measure == PerformanceTrendsMeasure.ServicePlansSalesPU);
            paramList.Add("servicePlansSalesPT", parms.measure == PerformanceTrendsMeasure.ServicePlansSalesPT);

            paramList.Add("productsPU", parms.measure == PerformanceTrendsMeasure.ProductsPerUnit);

            paramList.Add("ads", parms.measure == PerformanceTrendsMeasure.AvgDaysToSales);
            paramList.Add("add", parms.measure == PerformanceTrendsMeasure.AvgDaysToDeliver);

            paramList.Add("redWorkId", parms.measure == PerformanceTrendsMeasure.RedWorkId);
            paramList.Add("redWorkSold", parms.measure == PerformanceTrendsMeasure.RedWorkSold);
            paramList.Add("amberWorkId", parms.measure == PerformanceTrendsMeasure.AmberWorkId);
            paramList.Add("amberWorkSold", parms.measure == PerformanceTrendsMeasure.AmberWorkSold);
            paramList.Add("citNowSent", parms.measure == PerformanceTrendsMeasure.CitNowSent);
            paramList.Add("citNowViewed", parms.measure == PerformanceTrendsMeasure.CitNowViewed);

            return await dapper.GetAllAsync<PerformanceTrendsDTO>("dbo.GET_PerformanceTrendsForSite", paramList, CommandType.StoredProcedure);
         }
      }

        
        //UnifiedDB - updated
      public async Task<List<PersonAndDealCount>> GetOrdersForSalesIncentive(int userId)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("userId", userId);

            var res = await dapper.GetAllAsync<PersonAndDealCount>("dbo.GET_DaciaSpringOrders", paramList, CommandType.StoredProcedure);
            return res.ToList();
         }
      }

        public async Task<Deal?> GetDealByEnquiryNumber(string enquiryNumber)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                return await db.Deals.Where(d => d.EnquiryNumber == enquiryNumber).FirstOrDefaultAsync();
            }
        }

        public async Task InsertNewDeal(Deal incomingDeal)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                try
                {
                    await db.Deals.AddAsync(incomingDeal);
                    await db.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
        }

        public async Task UpdateDeal(Deal deal)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                try
                {
                    db.Deals.Update(deal);
                    await db.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
        }

        public async Task AddDiffs(List<Diff> diffsToPersist)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                await db.Diffs.AddRangeAsync(diffsToPersist);
                await db.SaveChangesAsync();
            }
        }

        public async Task<List<VehicleType>> GetVehicleTypes(int dealerGroupId)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                return await db.VehicleTypes.Where(x => x.DealerGroup_Id == dealerGroupId).AsNoTracking().ToListAsync();
            }
        }

        public async Task<List<OrderType>> GetOrderTypes(int dealerGroupId)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                return await db.OrderTypes.Where(x => x.DealerGroup_Id == dealerGroupId).AsNoTracking().ToListAsync();
            }
        }

        public async Task<List<Site>> GetSites(int dealerGroupId)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                return await db.Sites.Where(x => x.DealerGroup_Id == dealerGroupId).AsNoTracking().ToListAsync();
            }
        }

        public async Task MERGE_DealLatests(DynamicParameters paramList)
        {
            using (var dapper = new DADapperr(_connectionString))
            {
                await dapper.ExecuteAsync("MERGE_DealLatests", paramList, System.Data.CommandType.StoredProcedure);
            }
        }
    }


}


