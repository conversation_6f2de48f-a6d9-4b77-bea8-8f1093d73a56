CREATE OR ALTER PROCEDURE [autoprice].[GET_CheckChangesToKeyIntoDMSNotRepeated]
(
	@DealerGroupId int
)




  
AS  
BEGIN  

-- Set the first day of the week to Monday
    SET DATEFIRST 1;


-------------------------------------
-- Get Auto price changes
-------------------------------------
DECLARE @today Date = CONVERT(date,getDate());
DECLARE @yesterday Date = DATEADD(day,-1,@today);
DECLARE @tomorrow Date = DATEADD(day,1,@today);

DECLARE @todayIsAPubHoliday Bit;
IF EXISTS (SELECT 1 FROM PublicHolidays WHERE Date = CONVERT(date, GETDATE()))
BEGIN
    SET @todayIsAPubHoliday = 1;
END
ELSE
BEGIN
    SET @todayIsAPubHoliday = 0;
END
DECLARE @yesterdayIsPubHoliday Bit;
IF EXISTS (SELECT 1 FROM PublicHolidays WHERE Date = DATEADD(day,-1,CONVERT(date, GETDATE())))
BEGIN
    SET @yesterdayIsPubHoliday = 1;
END
ELSE
BEGIN
    SET @yesterdayIsPubHoliday = 0;
END

DECLARE @now datetime = GETDATE();
DECLARE @todaysUTCDate datetime = GETUTCDATE();
DECLARE @todaysDateMinus48Hours date = DATEADD(HOUR,-48, @now);



--Prepare yesterdayPriceChanges
SELECT
st.StockNumberFull,autos.NowPrice
INTO #yesterdayChanges
FROM autoprice.PriceChangeAutoItems autos
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = autos.VehicleAdvertSnapshot_Id
INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
INNER JOIN Stocks st on st.id = ads.Stock_Id
WHERE snaps.SnapshotDate >= @yesterday AND snaps.SnapshotDate < @today
AND autos.CreatedDate >= @yesterday AND autos.CreatedDate < @today 
AND rs.DealerGroup_Id = @DealerGroupId -- must be the chosen DG
AND rs.IsActive = 1
--AND ads.AutotraderAdvertStatus = 'PUBLISHED' -- must be a published ad
AND autos.NowPrice <> 0 -- NOT 0 PRICE
AND ads.CreatedInSparkDate < @todaysDateMinus48Hours 
AND 
(
	(
		(autos.NowPrice - COALESCE(autos.WasPrice, 0)) > rs.MinimumAutoPriceIncrease AND 
		(
			COALESCE(autos.WasPrice, 0) = 0 OR
			(
				COALESCE(autos.WasPrice, 0) <> 0 AND
				(autos.NowPrice / NULLIF(COALESCE(autos.WasPrice, 0),0)) -1 >= rs.MinimumAutoPricePercentIncrease
			)
		)
	)  --price change must be sufficiently large
	OR
	(
		(autos.NowPrice - COALESCE(autos.WasPrice, 0)) < rs.MinimumAutoPriceDecrease AND 
		(
			COALESCE(autos.WasPrice, 0) = 0 OR
			(
				COALESCE(autos.WasPrice, 0) <> 0 AND
				(autos.NowPrice / NULLIF(COALESCE(autos.WasPrice, 0),0)) -1 <= rs.MinimumAutoPricePercentDecrease
			)
		)
	)
)
AND ads.VehicleReg IS NOT NULL; -- must have a vehicle reg

WITH latestOptOuts as
(
	SELECT
	optouts.VehicleAdvert_Id,
	optouts.Id,
	ROW_NUMBER() OVER (PARTITION BY optouts.VehicleAdvert_Id ORDER BY optouts.Id desc) AS RowNumber   
	FROM autoprice.VehicleOptOuts optouts
	INNER JOIN autoprice.VehicleAdverts ads on ads.id = optouts.VehicleAdvert_Id
	INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
	WHERE 
	rs.DealerGroup_Id = @DealerGroupId
	AND optouts.CreatedDate > @today
	AND optouts.ActualEndDate > @now
	AND rs.IsActive = 1
)

--Final result
SELECT
rs.Name,
st.StockNumberFull,
autos.NowPrice as TodayNowPrice,
yc.NowPrice as YesterdayNowPrice,
autos.NowPrice - yc.NowPrice as DailyDifference,
rs.MinimumAutoPriceDecrease,
rs.MinimumAutoPriceIncrease,
rs.Id

FROM autoprice.PriceChangeAutoItems autos
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = autos.VehicleAdvertSnapshot_Id
INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
INNER JOIN Stocks st on st.id = ads.Stock_Id
LEFT JOIN latestOptOuts opts on opts.VehicleAdvert_Id = ads.Id and opts.RowNumber = 1
LEFT JOIN #yesterdayChanges yc on yc.StockNumberFull = st.StockNumberFull
WHERE snaps.SnapshotDate >= @yesterday AND snaps.SnapshotDate < @today
AND autos.CreatedDate >= @today AND autos.CreatedDate < @tomorrow 
AND rs.DealerGroup_Id = @DealerGroupId -- must be the chosen DG
AND rs.IsActive = 1
--AND ads.AutotraderAdvertStatus = 'PUBLISHED' -- must be a published ad
AND autos.NowPrice <> 0 -- NOT 0 PRICE
AND ads.CreatedInSparkDate < @todaysDateMinus48Hours -- should only be updatable if in AT for more than 2 days
AND 
(
	(
		(autos.NowPrice - COALESCE(autos.WasPrice, 0)) > rs.MinimumAutoPriceIncrease AND 
		(
			COALESCE(autos.WasPrice, 0) = 0 OR
			(
				COALESCE(autos.WasPrice, 0) <> 0 AND
				(autos.NowPrice / NULLIF(COALESCE(autos.WasPrice, 0),0)) -1 >= rs.MinimumAutoPricePercentIncrease
			)
		)
	)  --price change must be sufficiently large
	OR
	(
		(autos.NowPrice - COALESCE(autos.WasPrice, 0)) < rs.MinimumAutoPriceDecrease AND 
		(
			COALESCE(autos.WasPrice, 0) = 0 OR
			(
				COALESCE(autos.WasPrice, 0) <> 0 AND
				(autos.NowPrice / NULLIF(COALESCE(autos.WasPrice, 0),0)) -1 <= rs.MinimumAutoPricePercentDecrease
			)
		)
	)
)
AND ads.VehicleReg IS NOT NULL -- must have a vehicle reg
AND opts.Id IS NULL -- must not be a relevant opt-out
--AND autos.DateConfirmed IS NULL -- must not already be confirmed
AND rs.UpdatePricesAutomatically = 1  --must be set to auto update prices
AND (rs.UpdatePricesPubHolidays = 1 OR @todayIsAPubHoliday = 0)
AND autos.WasOptedOutOfWhenGenerated = 0 --must not have been opted out when generated
AND (   --Must be set to do them on this day of the week
	(DATEPART(weekday, @now) = 1 AND rs.UpdatePricesMon=1) OR
	(DATEPART(weekday, @now) = 2 AND rs.UpdatePricesTue=1) OR
	(DATEPART(weekday, @now) = 3 AND rs.UpdatePricesWed=1) OR
	(DATEPART(weekday, @now) = 4 AND rs.UpdatePricesThu=1) OR
	(DATEPART(weekday, @now) = 5 AND rs.UpdatePricesFri=1) OR
	(DATEPART(weekday, @now) = 6 AND rs.UpdatePricesSat=1) OR
	(DATEPART(weekday, @now) = 7 AND rs.UpdatePricesSun=1)
	)
AND (
	(autos.NowPrice - yc.NowPrice) < rs.MinimumAutoPriceIncrease
	AND
	(autos.NowPrice - yc.NowPrice) > rs.MinimumAutoPriceDecrease
)
AND @todayIsAPubHoliday = 0
AND @yesterdayIsPubHoliday = 0
DROP TABLE #yesterdayChanges
END
GO