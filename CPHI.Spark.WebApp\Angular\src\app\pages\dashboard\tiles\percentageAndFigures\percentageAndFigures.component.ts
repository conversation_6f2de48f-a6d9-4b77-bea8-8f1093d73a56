import { Component, Input, OnInit } from "@angular/core";
import { Router } from '@angular/router';
import { AutotraderService } from "src/app/services/autotrader.service";
import { ConstantsService } from "src/app/services/constants.service";
import { SelectionsService } from "src/app/services/selections.service";
import { TechControlAndConvRate } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";




@Component({
  selector: 'percentageAndFigures',
  templateUrl: './percentageAndFigures.component.html',
  styles: [
    `
    .label{font-weight: 400;  margin-top: 0.75em; text-align: center;}
    .value{font-weight: 200;  text-align: center;}
    .percentage{font-weight: 400 !important; }
    .total{font-weight: 400}

    .small-tile .label { margin-top: 0; }
  `
  ]
})


export class PercentageAndFiguresComponent implements OnInit {

  @Input() public data: TechControlAndConvRate
  @Input() public tileClass: string;

  @Input() public dataSource: string;
  @Input() public dataSource2: string;

  goodThreshold: number = 0.9;

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public analysis: AutotraderService,
    public router: Router,
    public service: DashboardService
  ) {

  }


  ngOnInit(): void {
    this.initParams();
  }

  ngOnChanges()
  {
    this.translate();

  }

  initParams() {
    this.goodThreshold = this.calculateGoodThreshold(this.data.Title);
    this.translate();
  }


  navigateToMainPage() {
    //this.service.chooseDashboardPage('CitNow')
  }

  translate()
  {
    if(this.constants.environment.customer === 'RRGSpain' && this.constants.currentLang == 'es')
    {
      const titleTranslations = {
        'Productivity': 'Dashboard_Aftersales_Productivity',
        'Efficiency': 'Dashboard_Aftersales_Efficiency',
        'Technical Control': 'Dashboard_Aftersales_TechnicalControl',
        'Conversion Rate': 'Dashboard_Aftersales_ConversionRate',
        'Future Sales': 'Dashboard_Aftersales_FutureSales',
        'CCR Upsell': 'Dashboard_Aftersales_CCRUpsell'
      };
      
      if (titleTranslations.hasOwnProperty(this.data.Title)) {
        this.data.Title = this.constants.translatedText[titleTranslations[this.data.Title]];
      }
  
      // First field
      const numTranslations = {
        'Time Invoiced': 'Dashboard_Aftersales_TimeInvoiced'
      };
      
      if (numTranslations.hasOwnProperty(this.data.NumeratorLabel)) {
        this.data.NumeratorLabel = this.constants.translatedText[numTranslations[this.data.NumeratorLabel]];
      }
  
      // Second field
      const denomTranslations = {
        'Time Invested': 'Dashboard_Aftersales_TimeInvested',
        'Quantity': 'Dashboard_Aftersales_Quantity',
        'All Revs' : 'Dashboard_Aftersales_AllRevs',
      };
      
      if (denomTranslations.hasOwnProperty(this.data.DenominatorLabel)) {
        this.data.DenominatorLabel = this.constants.translatedText[denomTranslations[this.data.DenominatorLabel]];
      }
    }

  }

  calculateGoodThreshold(title: string)
  {
    let result: number;

    switch (title) {
      case "Efficiency":
        result = 0.85;
        break;
      case "Productivity":
        result = 1;
        break;
      case "Technical Control":
        result = 0.90;
        break;
      case "Conversion Rate":
        result = 0.65;
        break;
      case "Future Sales":
        result = 0.05;
        break;
      case "CCR Upsell":
        result = 0.2;
        break;
      default:
        result = 0.9;
        break;
    }
    return result;
  }
}


