import { Location } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { SiteVM } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { FcstDepartment, FcstForecast, ForecastRow, ForecastRowGroup, ForecastVersionVM } from '../../model/liveForecast.model';
import { LiveForecastReviewService } from '../liveForecastReview/liveForecastReview.service';
import { LiveForecastStatusService } from '../liveForecastStatus/liveForecastStatus.service';
import { LiveForecastInputService } from './liveForecastInput.service';

@Component({
  selector: 'app-liveForecastInput',
  templateUrl: './liveForecastInput.component.html',
  styleUrls: ['./liveForecastInput.component.scss']
})

export class LiveForecastInputComponent implements OnInit {
  @ViewChild('saveNewVersionModal', { static: true }) saveNewVersionModal: ElementRef;
  @ViewChild('renameVersionModal', { static: true }) renameVersionModal: ElementRef;

  forecastMonthsLoadedSubscription: Subscription;
  forecastVersionsLoadedSubscription: Subscription;
  changeDepartmentSubscription: Subscription;

  debounce: any;
  debounceTime: number = 500;
  isEditingInProgress: boolean = false;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getData: GetDataMethodsService,
    public service: LiveForecastInputService,
    public modal: NgbModal,
    public location: Location,
    public statusService: LiveForecastStatusService,
    public reviewService: LiveForecastReviewService,
    public liveForecastStatusService: LiveForecastStatusService
  ) { }

  ngOnInit(): void {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });

    if (!this.service.selectedSiteId) this.selectSite(this.selections.user.site);
    this.service.allForecastMonths = this.liveForecastStatusService.getMonthsForPicker();
    this.service.getAllAvailableForecasts();
    this.service.getAllDepartments(this.service.selectedSiteId);

    this.forecastMonthsLoadedSubscription = this.service.forecastMonthsLoaded.subscribe(() => {
      this.selectForecastMonth(this.service.selectedMonth);
    })

    this.forecastVersionsLoadedSubscription = this.service.forecastVersionsLoaded.subscribe(() => {
      this.service.isNavigated = false;

      if (this.service.allForecastVersions.length > 1 && this.service.selectedForecastVersionId == 0) {
        return this.service.getLiveForecast();
      }

      if (this.service.allForecastVersions.length < 1) {
        return this.createNewVersion();
      };

      if (this.service.selectedForecastVersionId) {
        this.selectForecastVersion(this.service.allForecastVersions.find(x => x.Id == this.service.selectedForecastVersionId));
      } else {
        this.selectForecastVersion(this.service.allForecastVersions[this.service.allForecastVersions.length - 1]);
      }
    })

    this.changeDepartmentSubscription = this.service.changeDepartment.subscribe((department: FcstDepartment) => {

      this.selectDepartment(department);
    })


  }

  ngOnDestroy(): void {
    if (this.forecastMonthsLoadedSubscription) this.forecastMonthsLoadedSubscription.unsubscribe();
    if (this.forecastVersionsLoadedSubscription) this.forecastVersionsLoadedSubscription.unsubscribe();
    if (this.changeDepartmentSubscription) this.changeDepartmentSubscription.unsubscribe();
    this.service.allLiveForecasts = null;
  }

  selectSite(site: SiteVM) {
    if (!this.canContinueIfEditing()) {
      return;
    } else {
      this.isEditingInProgress = false;
    }


    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.selectedSiteId = site.SiteId;
    this.service.selectedSite = site.SiteDescription;
    this.service.getAllAvailableForecasts();
    this.service.getAllDepartments(site.SiteId);
  }

  selectForecastMonth(month: string) {
    if (!this.canContinueIfEditing()) {
      return;
    } else {
      this.isEditingInProgress = false;
    }

    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.selectedMonthsForecasts = this.service.allForecastRows.filter(f => f.Month.split('T')[0] == month.split('T')[0]);
    this.service.selectedMonth = month;

    if (this.service.isNavigated && this.service.selectedForecastId) {
      this.selectForecast(this.service.selectedForecastId);
    } else {
      this.selectForecast(this.service.selectedMonthsForecasts[this.service.selectedMonthsForecasts.length - 1].Id);
    }
  }

  selectForecast(forecastId: number) {
    if (!this.canContinueIfEditing()) {
      return;
    } else {
      this.isEditingInProgress = false;
    }


    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.selectedForecastId = forecastId;
    this.service.selectedForecast = this.service.selectedMonthsForecasts.find(x => x.Id == forecastId);
    this.service.getForecastVersions();
  }

  selectForecastVersion(forecastVersion: ForecastVersionVM) {
    if (!this.canContinueIfEditing()) {
      return;
    } else {
      this.isEditingInProgress = false;
    }
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.selectedForecastVersionId = forecastVersion.Id;
    this.service.selectedVersion = forecastVersion;
    this.service.getLiveForecast();
  }

  selectDepartment(department: FcstDepartment) {
    if (!this.canContinueIfEditing()) {
      return;
    } else {
      this.isEditingInProgress = false;
    }

    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.selectedDepartmentId = department.Id;
    this.service.selectedDepartment = department.Name;
    //this.service.getAllAvailableForecasts();
    //if (this.service.selectedForecastVersionId > 0){
    this.service.getLiveForecast();
    //}
  }

  canContinueIfEditing(): boolean {
    //check if editing in progress.
    if (this.isEditingInProgress) {
      let selection = confirm("Editing in progress: You will lose data if you switch department");
      if (selection == false) return false;
    }
    return true;
  }

  updateApprovalState(state: string) {
    this.selections.triggerSpinner.emit({ show: true, message: 'Updating approval state...' });
    switch (state) {
      case 'Draft':
        // ToDo
        return;
      case 'Submitted':
        this.service.submit();
        return;
      case 'Approved':
        this.service.approve();
        return;
    }
  }

  increase(field: string, row: ForecastRow, value: number) {
    let changeBy: number = 1;

    if (row.AccountLabel == 'Per Unit') changeBy = 10;
    if (row.RowNumberFormat == 'currency' && row.AccountLabel != 'Per Unit') changeBy = 100;

    value = value + changeBy;
    row[field] = value;

    clearTimeout(this.debounce);
    this.debounce = setTimeout(() => {
      this.recalculateLiveForecasts(field, row, value);
    }, this.debounceTime)
  }

  decrease(field: string, row: ForecastRow, value: number) {
    let changeBy: number = 1;

    if (row.AccountLabel == 'Per Unit') changeBy = 10;
    if (row.RowNumberFormat == 'currency' && row.AccountLabel != 'Per Unit') changeBy = 100;

    value = value - changeBy;
    row[field] = value;

    clearTimeout(this.debounce);
    this.debounce = setTimeout(() => {
      this.recalculateLiveForecasts(field, row, value);
    }, this.debounceTime)
  }

  updateValue(field: string, row: ForecastRow, event: any) {

    if (!event.sourceCapabilities || !event.target) return;

    let value: number;

    if (event.target && (event.target.value == '' || event.target.value == '-')) {
      value = 0;
    } else {

      let removeTrailingHyphens = event.target.value[0].toString() + event.target.value.slice(1).replace("-", "");
      let removeUnxpectedChars = removeTrailingHyphens.replace(/[^0-9\.-]+/g, "");
      value = Number(removeUnxpectedChars);

      // Deal with percentages
      if (row.AccountLabel == 'Labour Margin') {
        value = value / 100;
      }
    }

    this.recalculateLiveForecasts(field, row, value);
  }

  recalculateLiveForecasts(field: string, row: ForecastRow, value: number) {
    switch (field) {
      case 'SiteForecast':
        row.SiteForecast = value;
        break;
      case 'SiteTotal':
        row.SiteForecast = value - row.BroughtIn - row.DoneInMonth;
        row.SiteTotal = value;
        break;
      case 'DirectorAdj':
        row.DirectorAdj = value;
        break;
      case 'DirectorTotal':
        row.DirectorAdj = value - row.SiteTotal;
        row.DirectorTotal = value
        break;
    }

    this.isEditingInProgress = true;


    this.service.recalculateLiveForecasts(field, row);
  }

  shouldDisplayMonths(forecast: ForecastRowGroup) {
    let forecastMonths: string[] = [...new Set(Array.prototype.concat(...forecast.ForecastRows.map(x => x.ForecastMonths.map(x => x.Month))))];
    if (forecastMonths.length < 1) return false;
    return true;
  }

  createNewVersion() {
    this.selections.triggerSpinner.emit({ show: true, message: 'Creating new version' });
    this.service.createNewVersion();
  }

  openVersionNameModal() {
    if (this.service.selectedForecastVersionId != 0) return this.saveNewVersion();
    this.modal.open(this.saveNewVersionModal, { keyboard: false, ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-sm' });
  }

  openVersionRenameModal() {
    this.modal.open(this.renameVersionModal, { keyboard: false, ariaLabelledBy: 'modal-basic-title', windowClass: 'modal-sm' });
  }
  public cellClassProviderWithColourFont(value: number): string {
    return value < 0 ? 'badFont ag-right-aligned-cell' : 'ag-right-aligned-cell';
  }

  saveNewVersion() {
    this.selections.triggerSpinner.emit({ show: true, message: 'Saving...' });
    this.service.save();
    this.modal.dismissAll();
    this.isEditingInProgress = false;
  }

  changeComparative(comparative: string) {
    this.service.selectedComparative = comparative;
    this.service.comparativeChanged.emit();
  }

  returnToPrevious() {
    this.service.showBackButton = false;
    
    this.statusService.selectedMonth = this.service.selectedMonth;
    this.statusService.selectedMonthsForecasts = this.service.selectedMonthsForecasts;
    this.statusService.selectedForecast = this.service.selectedMonthsForecasts.find(x => x.Id == this.service.selectedForecastId);
    this.statusService.selectedForecastId = this.service.selectedForecastId;
    this.statusService.selectedForecastVersionId = this.service.selectedForecastVersionId;

    this.reviewService.selectedMonth = this.service.selectedMonth;
    this.reviewService.selectedMonthsForecasts = this.service.selectedMonthsForecasts;
    this.reviewService.selectedForecast = this.service.selectedMonthsForecasts.find(x => x.Id == this.service.selectedForecastId);
    this.reviewService.selectedForecastId = this.service.selectedForecastId;

    this.location.back();
  }

  isCreateNewVersionButtonVisible() {
    return this.service.allForecastVersions.length == 0 ? true : false;
  }

  isSubmitButtonVisible() {
    //	Only show if aspnetuserclaim is submitter or approver (not inputter). Only show if approval state is draft.
    let forecastVersion: ForecastVersionVM = this.service.allForecastVersions.find(a => a.Id == this.service.selectedForecastVersionId);

    if (forecastVersion == null) return false;

    if (this.selections.user.permissions.liveForecast == 'submitter') {
      if (!this.selections.user.eligibleSiteIds.includes(this.service.selectedSiteId)) return false;
    }

    if (forecastVersion.ApprovalStateId == 1) return true;

    if (forecastVersion && (forecastVersion.ApprovalStateId == 2 || forecastVersion.ApprovalStateId == 3)) return false;

    return false;
  }

  isRejectButtonVisible() {
    //Only show if aspnetuserclaim is approver, and if approval state is submitted or approved.
    if (this.selections.user.permissions.liveForecast != 'approver') return false;

    let forecastVersion: ForecastVersionVM = this.service.allForecastVersions.find(a => a.Id == this.service.selectedForecastVersionId);

    if (forecastVersion == null) return false;

    if (forecastVersion.ApprovalStateId == 2 || forecastVersion.ApprovalStateId == 3) return true;

    return false;
  }

  isSaveButtonVisible() {
    //Only show if aspnetuserclaim is submitter or approver (not inputter). Only show if approval state is draft
    let forecastVersion: ForecastVersionVM = this.service.allForecastVersions.find(a => a.Id == this.service.selectedForecastVersionId)

    if (!this.selections.user.eligibleSiteIds.includes(this.service.selectedSiteId)) return false;

    if (forecastVersion == null) return true;

    if (forecastVersion.ApprovalStateId == 1) return true;

    return false;
  }

  isForecastVersionEditable(isPlusAndMinusToggle?: boolean) {
    let isTrue: boolean = true;

    let forecastVersion: ForecastVersionVM = this.service.allForecastVersions.find(a => a.Id == this.service.selectedForecastVersionId)

    if (forecastVersion && (forecastVersion.ApprovalStateId == 2 || forecastVersion.ApprovalStateId == 3)) isTrue = false;

    if (isPlusAndMinusToggle && isTrue == true) {
      isTrue = window.innerWidth < 1800 ? false : true;
    }

    return isTrue;
  }

  renameVersion() {
    this.service.renameVersion();
    this.modal.dismissAll();
  }

  isRenameVersionButtonVisible() {
    if(!(this.service.selectedForecastVersionId > 0)){ return false; }
    let forecastVersion: ForecastVersionVM = this.service.allForecastVersions.find(a => a.Id == this.service.selectedForecastVersionId)
    if (forecastVersion && (forecastVersion.ApprovalStateId == 2 || forecastVersion.ApprovalStateId == 3)) return false;
    return true;
  }

  submit() {
    this.selections.triggerSpinner.emit({ show: true, message: 'Submitting...' });
    this.service.save(true);
  }
}
