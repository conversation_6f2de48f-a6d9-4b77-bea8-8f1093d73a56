CREATE OR ALTER PROCEDURE [autoprice].[CREATE_VehicleOptOuts]
(
	@DealerGroupId int
)

AS
BEGIN

SET NOCOUNT ON

DECLARE @systemUserId int = (SELECT SystemUser_Id from DealerGroups WHERE DealerGroupId = @DealerGroupId)


-------------------------------
--  RRG
-------------------------------

-- RRG remove optouts for Un-Reserved cars
UPDATE opts
SET opts.ActualEndDate = GETDATE()
FROM autoprice.VehicleOptOuts opts
INNER JOIN stocks st on st.VehicleAdvert_Id = opts.VehicleAdvert_id
INNER JOIN StandingValues prog on prog.id = st.ProgressCode_Id
	WHERE 
	opts.AutoOptOutType = 'Reserved'
	AND st.Id IN (
		SELECT
		MAX(st.Id)
		FROM autoprice.VehicleOptOuts opts 
		INNER JOIN stocks st on st.VehicleAdvert_Id = opts.VehicleAdvert_id
		INNER JOIN StandingValues prog on prog.id = st.ProgressCode_Id
		INNER JOIN autoprice.VehicleAdverts ads on ads.id = opts.VehicleAdvert_Id
		INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
		WHERE rs.DealerGroup_Id = @dealerGroupId 
		AND 
			(
				(prog.Description != 'Reserved' AND st.IsRemoved = 0) --Is in stock, and is not reserved any longer
				--OR
				--(st.RemovedDate > opts.CreatedDate AND st.IsRemoved = 1) --Is not in stock, and was removed after we made this opt-out
			)
		AND opts.AutoOptOutType = 'Reserved'
		GROUP BY st.VehicleAdvert_Id
	)
AND opts.ActualEndDate > getDate()
AND @dealerGroupId = 1 --We only do this for Renault



--RRG optouts
INSERT INTO autoprice.VehicleOptOuts 
( 
	Person_Id,
	CreatedDate,
	OriginalEndDate,
	VehicleAdvert_Id,
	ActualEndDate,
	AutoOptOutType
)
(
	SELECT
	@systemUserId,  
	getDate(),
	DATEADD(day,28,getDate()),
	ads.id,
	DATEADD(day,28,getDate()),
	'AlpineCar'
	FROM autoprice.VehicleAdvertSnapshots rat
	INNER JOIN autoprice.VehicleAdverts ads on ads.id =rat.VehicleAdvert_Id
	INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
	LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and opts.ActualEndDate >= getDate()
	WHERE opts.id is null 
	AND rs.DealerGroup_Id = @dealerGroupId
	and CONVERT(date,rat.SnapshotDate) = CONVERT(date,getDate())
	and 	ads.Make = 'Alpine'	
	AND @dealerGroupId = 1 --We only do this for Renault
	
	UNION ALL 

	SELECT
	@systemUserId,  
	getDate(),
	DATEADD(day,28,getDate()),
	ads.id,
	DATEADD(day,28,getDate()),
	'Reserved'
	FROM autoprice.VehicleAdvertSnapshots rat
	INNER JOIN autoprice.VehicleAdverts ads on ads.id =rat.VehicleAdvert_Id
	INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
	INNER JOIN stocks st on st.VehicleAdvert_Id = ads.id
	INNER JOIN StandingValues prog on prog.id = st.ProgressCode_Id
	LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and opts.ActualEndDate >= getDate()
	WHERE opts.id is null and rs.DealerGroup_Id = @dealerGroupId and st.IsRemoved = 0
	and CONVERT(date,rat.SnapshotDate) = CONVERT(date,getDate())
	and prog.Description = 'Reserved'
	AND @dealerGroupId = 1 --We only do this for Renault
)



-------------------------------
--  Startin
-------------------------------

INSERT INTO autoprice.VehicleOptOuts 
( 
	Person_Id,
	CreatedDate,
	OriginalEndDate,
	VehicleAdvert_Id,
	ActualEndDate,
	AutoOptOutType
)
(
	SELECT
	@systemUserId,  
	getDate(),
	DATEADD(day,28,getDate()),
	ads.id,
	DATEADD(day,28,getDate()),
	'Demo'
	FROM autoprice.VehicleAdvertSnapshots rat
	INNER JOIN autoprice.VehicleAdverts ads on ads.id =rat.VehicleAdvert_Id
	INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
	INNER JOIN Stocks st on st.Id = ads.Stock_Id
	INNER JOIN VehicleTypes vt on vt.Id = st.VehicleType_Id
	LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and opts.ActualEndDate >= getDate()
	WHERE opts.id is null 
	AND rs.DealerGroup_Id = @dealerGroupId
	AND CONVERT(date,rat.SnapshotDate) = CONVERT(date,getDate())
	AND vt.Description = 'Demo'
	AND @dealerGroupId = 17 --We only do this for Startin
	AND rs.Id IN (83,109)
)


-------------------------------
--  Vindis
-------------------------------

--Firstly delete any no longer required
SELECT
DISTINCT opts.Id
INTO #optsToRemove
FROM autoprice.VehicleAdvertSnapshots snaps
INNER JOIN autoprice.VehicleAdverts ads on ads.id =snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
INNER JOIN Stocks st on st.Id = ads.Stock_Id
INNER JOIN StandingValues prog on prog.Id = st.ProgressCode_Id
LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and opts.ActualEndDate > getDate()
INNER JOIN People p on p.id = opts.Person_Id

WHERE 
rs.DealerGroup_Id = 3
AND CONVERT(date,snaps.SnapshotDate) = CONVERT(date,getDate())
AND prog.Description  NOT IN ('Sold Awaiting Processing','Sold')
AND opts.Id IS NOT NULL
AND p.name = 'Richard Procter'
;

DELETE
FROM autoprice.VehicleOptOuts 
WHERE Id IN (SELECT * FROM #optsToRemove)

DROP TABLE #optsToRemove;



--Now create any new ones we need

INSERT INTO autoprice.VehicleOptOuts 
( 
	Person_Id,
	CreatedDate,
	OriginalEndDate,
	VehicleAdvert_Id,
	ActualEndDate,
	AutoOptOutType
)
(
	SELECT
	@systemUserId,  
	getDate(),  --CreatedDate
	DATEADD(day,28,getDate()),  --OriginalEndDate
	ads.id,  --AdvertId
	DATEADD(day,28,getDate()),  --ActualEndDate
	prog.Description  --OptOutType
	FROM autoprice.VehicleAdvertSnapshots snaps
	INNER JOIN autoprice.VehicleAdverts ads on ads.id =snaps.VehicleAdvert_Id
	INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
	INNER JOIN Stocks st on st.Id = ads.Stock_Id
	INNER JOIN StandingValues prog on prog.Id = st.ProgressCode_Id
	LEFT JOIN autoprice.VehicleOptOuts opts on opts.VehicleAdvert_Id = ads.id and opts.ActualEndDate >= getDate()
	WHERE opts.id is null 
	AND rs.DealerGroup_Id = 3
	AND CONVERT(date,snaps.SnapshotDate) = CONVERT(date,getDate())
	AND prog.Description  IN ('Sold Awaiting Processing','Sold')
	AND @dealerGroupId = 3 --we only do this for Vindis
)
END

GO