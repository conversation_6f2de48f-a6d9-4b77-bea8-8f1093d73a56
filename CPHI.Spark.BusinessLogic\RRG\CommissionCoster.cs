﻿using CPHI.Spark.Model.ViewModels.RRG;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.RRG
{
   public class CommissionCoster
   {
      public ILog logger;
      public CommissionCoster(ILog loggerIn)
      {
         logger = loggerIn;
      }

      public CommissionCoster() { }

      //-------------------------------------------------------------------------------------------
      // This is the main method used to get commission calculation for a month, used by both
      //  webApp and reporter
      //-------------------------------------------------------------------------------------------

      public async Task<IEnumerable<CommissionItem>> GetCostedCommissionsForMonth(DateTime yearMonth, DateTime currentCommissionMonth, string connectionString, Dictionary<string, decimal> latestViewOfOrderEarnRates)
      {
         // --------------------------------
         // 1. Get the data Items
         // --------------------------------


         CommissionGetDataService commissionGetDataService = new CommissionGetDataService(connectionString);
         List<CommissionItem> results = new List<CommissionItem>();
         IEnumerable<CommissionDataItem> dataItems = await commissionGetDataService.GetDataItemsForMonth(yearMonth);
         dataItems = dataItems.Where(x => x.Role != "Fleet"); //exclude LBDMs

         // var tst = dataItems.Where(x => x.StockNumber == "1109201/0").ToList();
         // if(yearMonth==new DateTime(2024, 9, 1))
         // {
         //     { }
         // }

         bool getQuarterlyCatchups = yearMonth >= new DateTime(2023, 6, 1) && yearMonth.Month % 3 == 0;

         ILookup<int, CommissionQuarterlyCatchupDataItem> quarterlyDataLookupMotab = null;
         ILookup<int, CommissionQuarterlyCatchupDataItem> quarterlyDataLookupNonMotab = null;
         if (getQuarterlyCatchups)
         {
            IEnumerable<CommissionQuarterlyCatchupDataItem> catchupItems = await commissionGetDataService.GETCommissionItemsQuarterlyCatchupData(yearMonth);
            quarterlyDataLookupMotab = catchupItems.Where(x => x.IsMotability).ToLookup(x => x.SalesmanId);
            quarterlyDataLookupNonMotab = catchupItems.Where(x => !x.IsMotability).ToLookup(x => x.SalesmanId);
         }

         //var tst = dataItems.FirstOrDefault(x => x.StockNumber == "1091893/0");

         ///SPK-2824 temp.  We eliminate these to avoid facing into the problem that we previously overpaid people by failing
         ///to correctly record that we had paid them these items, then kept topping them up
         ///so to avoid clawing back we simply zero these out for anything before 2023
         if (yearMonth.Year < 2023)
         {
            foreach (var item in dataItems)
            {
               item.HasMot12 = false;
               item.HasMot24 = false;
               item.HasMot36 = false;
               item.HasRoadsideAssist12 = false;
               item.HasRoadsideAssist24 = false;
               item.HasRoadsideAssist36 = false;
            }
         }

         //eliminate fleet people
         dataItems = dataItems.Where(x => x.Role != "Fleet");

         //Get electric quarter to date sales
         Dictionary<int, ElectricSalesQToDate> electricSalesQToDateBySalesmanPre2025 = new Dictionary<int, ElectricSalesQToDate>();

         Dictionary<int, ElectricSalesQToDate> electricSalesQToDateBySalesmanRen = new Dictionary<int, ElectricSalesQToDate>();
         Dictionary<int, ElectricSalesQToDate> electricSalesQToDateBySalesmanDacia = new Dictionary<int, ElectricSalesQToDate>();

         Dictionary<int, bool> usedTargetAchievementDict = new Dictionary<int, bool>();

         // If 2025, get the stats for Q4 of 2024 (for orders in 2024, delivered in 2025)
         if (yearMonth.Year >= 2025)
         {
            electricSalesQToDateBySalesmanPre2025 = await commissionGetDataService.GETElectricSalesQToDatePre2025(new DateTime(2024, 12, 31));
            electricSalesQToDateBySalesmanRen = await commissionGetDataService.GETElectricSalesQToDateRen(yearMonth);
            electricSalesQToDateBySalesmanDacia = await commissionGetDataService.GETElectricSalesQToDateDacia(yearMonth);

            if (yearMonth >= new DateTime(2025, 3, 1))
            {
               usedTargetAchievementDict = await commissionGetDataService.GETUsedTargetAchievementBySalesman(yearMonth);
            }
         }
         else if (yearMonth > new DateTime(2024, 4, 1))
         {
            electricSalesQToDateBySalesmanPre2025 = await commissionGetDataService.GETElectricSalesQToDatePre2025(yearMonth);
         }


         // --------------------------------
         // 2. Get role payouts for month
         // --------------------------------
         Dictionary<string, CommissionPayoutRateSet> rolePayoutLookup = BrandPayoutsService.GetRolePayouts(yearMonth);
         Dictionary<string, CommissionPayoutRateSet> nissanLiverpoolRolePayoutLookup = BrandPayoutsService.GetRolePayoutsNissanLiverpool(yearMonth);


         // -----------------------------------------------------------
         // 2.5 Retrieve what has already been paid for order rate
         // -----------------------------------------------------------
         Dictionary<string, CommissionOrderRatePaid> orderPaidRates = await commissionGetDataService.GetOrderPaidRates(yearMonth);

         // -----------------------------------------------------------
         // 3 Get incentive deals if necessary
         // -----------------------------------------------------------
         Dictionary<string, int> incentiveDeals = commissionGetDataService.GetIncentiveDeals(yearMonth);

         //have to also consider what the latest view is on the order values, not just what we have recorded in the database
         //issue:
         //person sold motab.  we only did order rate of 50.  should have been 50 + 100 = 150.   then we took 1/4 of it.  now we try



         // --------------------------------
         // 3. walk through salespeople and
         // calculate their statement
         // --------------------------------

         foreach (var salesmanDealsGroup in dataItems.ToLookup(x => x.SalesmanId))
         {

            // Salesrole 
            string role = salesmanDealsGroup.First().Role;

            // Role Payouts
            CommissionPayoutRateSet payoutRates = salesmanDealsGroup.First().SalesmanSiteDescription == "Nissan Liverpool" ? nissanLiverpoolRolePayoutLookup[role] : rolePayoutLookup[role];

            // EV Sales for the Quarter (Prior to 2025) 
            // This is not seperated out into Renault/Dacia
            electricSalesQToDateBySalesmanPre2025.TryGetValue(salesmanDealsGroup.Key, out ElectricSalesQToDate personSalesToDate);
            int electricQPre2025 = personSalesToDate != null ? personSalesToDate.SaleCount : 0;

            // If prior to 2025, do not check for seperate Renault/Dacia EV
            if (yearMonth.Year >= 2025)
            {
               electricSalesQToDateBySalesmanRen.TryGetValue(salesmanDealsGroup.Key, out ElectricSalesQToDate personSalesToDateRenault);
               int electricQRenault = personSalesToDateRenault != null ? personSalesToDateRenault.SaleCount : 0;

               electricSalesQToDateBySalesmanDacia.TryGetValue(salesmanDealsGroup.Key, out ElectricSalesQToDate personSalesToDateDacia);
               int electricQDacia = personSalesToDateDacia != null ? personSalesToDateDacia.SaleCount : 0;

               if (yearMonth >= new DateTime(2025, 3, 1))
               {
                  usedTargetAchievementDict.TryGetValue(salesmanDealsGroup.Key, out bool hasUsedTargetForSiteAch);
                  results.AddRange(CostUpPersonsDeals(salesmanDealsGroup, yearMonth, role, currentCommissionMonth, payoutRates, orderPaidRates, electricQPre2025, electricQRenault, electricQDacia, latestViewOfOrderEarnRates, hasUsedTargetForSiteAch, incentiveDeals));

               }
               else
               {
                  results.AddRange(CostUpPersonsDeals(salesmanDealsGroup, yearMonth, role, currentCommissionMonth, payoutRates, orderPaidRates, electricQPre2025, electricQRenault, electricQDacia, latestViewOfOrderEarnRates, false));
               }

            }
            else
            {
               results.AddRange(CostUpPersonsDeals(salesmanDealsGroup, yearMonth, role, currentCommissionMonth, payoutRates, orderPaidRates, electricQPre2025, 0, 0, latestViewOfOrderEarnRates, false));
            }

         }





         // --------------------------------
         // 3.5 Add quarterly catchup adjustment 
         // if relevant
         // --------------------------------
         if (getQuarterlyCatchups)
         {
            foreach (var salesmanDealsGroup in dataItems.ToLookup(x => x.SalesmanId))
            {
               var exampleDataItem = salesmanDealsGroup.First();

               // if(exampleDataItem.SalesmanName.Contains("Tonge") && yearMonth.Year == 2025)
               // {
               //    { }
               // }


               //work out unit top-up pay
               IEnumerable<CommissionQuarterlyCatchupDataItem> thisSalesmanQuarterlyDataItemsNonMotab = quarterlyDataLookupNonMotab[salesmanDealsGroup.Key];
               CommissionItem quarterlyCatchup = CalculateQuarterlyCommissionCatchup(yearMonth, thisSalesmanQuarterlyDataItemsNonMotab, salesmanDealsGroup.First().Role, rolePayoutLookup, exampleDataItem);

               if (quarterlyCatchup != null)
               {
                  results.Add(quarterlyCatchup);
               }

               //work out money for 30 motabs
               IEnumerable<CommissionQuarterlyCatchupDataItem> thisSalesmanQuarterlyDataItemsMotab = quarterlyDataLookupMotab[salesmanDealsGroup.Key];
               int motabCount = thisSalesmanQuarterlyDataItemsMotab.Select(x => x.OrderCount).Sum();
               int motabTarget = yearMonth == new DateTime(2023, 1, 1) ? 20 : 30;
               if (motabCount >= motabTarget)
               {
                  var exampleItem = thisSalesmanQuarterlyDataItemsNonMotab.First();

                  CommissionItem motabMoney = new CommissionItem()
                  {
                     SalesmanId = exampleItem.SalesmanId,
                     SalesmanName = exampleDataItem.SalesmanName,
                     Role = exampleDataItem.Role,
                     SalesmanSiteId = exampleDataItem.SalesmanSiteId,
                     AccountingDate = yearMonth,
                     OrderDate = yearMonth,
                     FranchiseId = 202, //Renault
                     OrderTypeId = 7, //Motability
                     VehicleTypeId = 6, //New
                     VehClassId = 1, //Car
                     OTypeType = "Motability",
                     VTypeType = "New",
                     SalesmanSiteDescription = exampleDataItem.SalesmanSiteDescription,
                     FranCode = "R",
                     Customer = $"Motability Quarterly Payout for achieving >= 30 units.   Achieved {motabCount}.  Congratulations!",
                     StockNumber = "MotabilityCatch",
                     PayRateRenault = 500
                  };
                  results.Add(motabMoney);
               }
            }
         }

         //var tst = results.Where(x => x.StockNumber == "1035312/0");

         return results;
      }


      private CommissionItem CalculateQuarterlyCommissionCatchup(DateTime yearMonth, IEnumerable<CommissionQuarterlyCatchupDataItem> quarterlyDataItems, string role, Dictionary<string, CommissionPayoutRateSet> rolePayoutLookup, CommissionDataItem exampleDataItem)
      {
         //get thresholds.    work out what they must have been paid on.   work out what quarter as a whole looks like.   if is higher, make an adjustment and return it
         CommissionPayoutRateSet payoutRates = rolePayoutLookup[role];

         if (quarterlyDataItems.Count() == 0)
         {
            return null;
         }

         CommissionQuarterlyCatchupDataItem exampleItem = quarterlyDataItems.First();

         int monthsCount = 3;
         if (yearMonth == new DateTime(2023, 6, 1)) { monthsCount = 2; }

         int month1Count = 0;
         int month1EarnRate = 0;
         int month1Earn = 0;
         if (monthsCount > 2)
         {
            month1Count = quarterlyDataItems.Where(x => x.Month == yearMonth.AddMonths(-2)).Select(x => x.OrderCount).Sum();
            month1EarnRate = payoutRates.NewCarThresholdsAndPayouts.Last(x => month1Count >= x.Key).Value;
            month1Earn = month1Count * month1EarnRate;
         }

         int month2Count = quarterlyDataItems.Where(x => x.Month == yearMonth.AddMonths(-1)).Select(x => x.OrderCount).Sum();
         int month2EarnRate = payoutRates.NewCarThresholdsAndPayouts.Last(x => month2Count >= x.Key).Value;
         int month2Earn = month2Count * month2EarnRate;

         int month3Count = quarterlyDataItems.Where(x => x.Month == yearMonth).Select(x => x.OrderCount).Sum();
         int month3EarnRate = payoutRates.NewCarThresholdsAndPayouts.Last(x => month3Count >= x.Key).Value;
         int month3Earn = month3Count * month3EarnRate;

         //work out quarterly achievement
         Dictionary<decimal, int> quarterlyNewCarThresholdsAndPayouts = new Dictionary<decimal, int>();
         foreach (var item in payoutRates.NewCarThresholdsAndPayouts)
         {
            quarterlyNewCarThresholdsAndPayouts.Add(item.Key * monthsCount, item.Value);
         }

         // Work out what they would have been paid for all deals
         // based on their average ach threshold for the quarter
         int quarterlyCount = month1Count + month2Count + month3Count;
         int quarterlyEarnRate = quarterlyNewCarThresholdsAndPayouts.Last(x => quarterlyCount >= x.Key).Value;
         int quarterlyEarn = quarterlyCount * quarterlyEarnRate;

         // If what they would have earned based on the average threshold attained 
         // is more than what they were actually paid - give them a catchup payment
         if (quarterlyEarn > (month1Earn + month2Earn + month3Earn))
         {
            //we are in catchup land.
            string month1Details = "";
            if (monthsCount == 3)
            {
               month1Details = $"{yearMonth.AddMonths(-2).ToString("MMM")} earned {month1Count}@{month1EarnRate}={month1Earn},";
            }
            string month2Details = $"{yearMonth.AddMonths(-1).ToString("MMM")} earned {month2Count}@{month2EarnRate}={month2Earn},";
            string month3Details = $"{yearMonth.ToString("MMM")} earned {month3Count}@{month3EarnRate}={month3Earn}";

            return new CommissionItem()
            {
               SalesmanId = exampleItem.SalesmanId,
               SalesmanName = exampleDataItem.SalesmanName,
               Role = role,
               SalesmanSiteDescription = exampleDataItem.SalesmanSiteDescription,
               SalesmanSiteId = exampleDataItem.SalesmanSiteId,
               AccountingDate = yearMonth,
               OrderDate = yearMonth,
               FranchiseId = 202, //Renault
               OrderTypeId = 10, //Retail
               VehicleTypeId = 6, //New
               VehClassId = 1, //Car
               FranCode = "R",
               OTypeType = "Retail",
               VTypeType = "New",
               Customer = $"Quarterly catchup ({month1Details} {month2Details} {month3Details}.  Quarter earned {quarterlyCount}@{quarterlyEarnRate}={quarterlyEarn})",
               StockNumber = "QuarterlyCatch",
               PayRateRenault = quarterlyEarn - month1Earn - month2Earn - month3Earn
            };
         }
         else
         {
            return null;
         }


      }


      //-------------------------------------------------------------------------------------------
      // The main way of adding the payout detail to each set of deals for a salesman
      //-------------------------------------------------------------------------------------------
      private List<CommissionItem> CostUpPersonsDeals(IEnumerable<CommissionDataItem> itemsIn, DateTime yearMonth, string role, DateTime currentCommissionMonth,
          CommissionPayoutRateSet payoutRates, Dictionary<string, CommissionOrderRatePaid> orderPaidRates, int electricQ, int electricQRenault, int electricQDacia,
          Dictionary<string, decimal> latestViewOfOrderEarnRates, bool wasUsedTargetForSiteHit, Dictionary<string, int> incentiveDeals = null)
      {
         //= rolePayoutLookup[role];
         //calc all comms
         List<CommissionItem> results = new List<CommissionItem>();

         var exampleDeal = itemsIn.First();

         if (exampleDeal.SalesmanName == "Christopher Gray")
         {
            { }
         }

         itemsIn = itemsIn.OrderBy(x => x.StockNumber).ToList();

         //Nissan Liverpool, they don't go onto the new scheme for May 2023, they remain on the previous one.
         if (exampleDeal.SalesmanSiteDescription == "Nissan Liverpool")
         {
            if (yearMonth >= new DateTime(2024, 10, 1))
            {
               results.AddRange(PersonCalculator8_Oct24Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2024, 9, 1))
            {
               results.AddRange(PersonCalculator7_Sep24Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2024, 7, 1))
            {
               results.AddRange(PersonCalculator6_Jul24Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ));
            }
            else if (yearMonth >= new DateTime(2023, 3, 1))
            {
               results.AddRange(PersonCalculator2_Mar23Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates));
            }
            else
            {
               results.AddRange(PersonCalculator0_Legacy.CostUpDeals(itemsIn, yearMonth, role, payoutRates));
            }
         }
         else if (role == "NewUsed")
         {
            if (yearMonth >= new DateTime(2025, 5, 1))
            {
               results.AddRange(PersonCalculator11_May25Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger, electricQ, electricQRenault, electricQDacia, incentiveDeals, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2025, 3, 1))
            {
               results.AddRange(PersonCalculator10_Mar25Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger, electricQ, electricQRenault, electricQDacia, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2025, 1, 1))
            {
               results.AddRange(PersonCalculator9_Jan25Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger, electricQ, electricQRenault, electricQDacia, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2024, 10, 1))
            {
               results.AddRange(PersonCalculator8_Oct24Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger, electricQ, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2024, 9, 1))
            {
               results.AddRange(PersonCalculator7_Sep24Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger, electricQ, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2024, 7, 1))
            {
               results.AddRange(PersonCalculator6_Jul24Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger, electricQ));
            }
            else if (yearMonth >= new DateTime(2024, 5, 1))
            {
               results.AddRange(PersonCalculator5_May24Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger, electricQ));
            }
            else if (yearMonth >= new DateTime(2024, 4, 1))
            {
               results.AddRange(PersonCalculator4_April24Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger, electricQ));
            }
            //special technique for Watford people who will have role 'NewUsed'
            else if (yearMonth >= new DateTime(2023, 5, 1))
            {
               results.AddRange(PersonCalculator3_May23Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, payoutRates, orderPaidRates, logger));
            }
            else if (yearMonth >= new DateTime(2023, 3, 1))
            {
               results.AddRange(PersonCalculator2_Mar23Onwards.CostUpDealsNewUsedPeople(itemsIn, yearMonth, role, payoutRates));

            }
            else if (yearMonth >= new DateTime(2021, 10, 1))
            {
               results.AddRange(PersonCalculator1_Oct21Onwards.CostUpDeals(itemsIn, yearMonth, role, payoutRates));
            }
            else
            {
               results.AddRange(PersonCalculator0_Legacy.CostUpDeals(itemsIn, yearMonth, role, payoutRates));
            }
         }
         else
         {
            //Not NewUsed people
            if (yearMonth >= new DateTime(2025, 5, 1))
            {
               results.AddRange(PersonCalculator11_May25Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ, electricQRenault, electricQDacia, latestViewOfOrderEarnRates, incentiveDeals, wasUsedTargetForSiteHit));
            }
            else if (yearMonth >= new DateTime(2025, 3, 1))
            {
               results.AddRange(PersonCalculator10_Mar25Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ, electricQRenault, electricQDacia, latestViewOfOrderEarnRates, wasUsedTargetForSiteHit));
            }
            else if (yearMonth >= new DateTime(2025, 1, 1))
            {
               results.AddRange(PersonCalculator9_Jan25Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ, electricQRenault, electricQDacia, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2024, 10, 1))
            {
               results.AddRange(PersonCalculator8_Oct24Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2024, 9, 1))
            {
               results.AddRange(PersonCalculator7_Sep24Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ, latestViewOfOrderEarnRates));
            }
            else if (yearMonth >= new DateTime(2024, 7, 1))
            {
               results.AddRange(PersonCalculator6_Jul24Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ));
            }
            else if (yearMonth >= new DateTime(2024, 4, 1)) //per Spencer / Richard new technique from May 2024 to treat used fleet car as used NOT fleet
            {
               results.AddRange(PersonCalculator5_May24Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ));
            }
            else if (yearMonth >= new DateTime(2024, 4, 1))
            {
               results.AddRange(PersonCalculator4_April24Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger, electricQ));
            }
            else if (yearMonth >= new DateTime(2023, 5, 1))
            {
               results.AddRange(PersonCalculator3_May23Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates, orderPaidRates, logger));
            }

            else if (yearMonth >= new DateTime(2023, 3, 1))
            {
               results.AddRange(PersonCalculator2_Mar23Onwards.CostUpDealsNonNewUsedPeople(itemsIn, yearMonth, role, payoutRates));
            }
            else
            {
               results.AddRange(PersonCalculator0_Legacy.CostUpDeals(itemsIn, yearMonth, role, payoutRates));
            }
         }

         foreach (var item in results)
         {
            CommonMethods.AdjustIfIsOrderOnlyDeal(item);
            CommonMethods.AddNoteIfIsDeliveryMoneyOnly(item);
            CommonMethods.AdjustPayoutsDownForCappedDeals(currentCommissionMonth, item);
         }

         return results;
      }


   }
}
