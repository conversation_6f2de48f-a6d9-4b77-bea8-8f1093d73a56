{
  "ConnectionStrings": {
    "DefaultConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkVindis;Persist Security Info=True; User ID=SparkVindisLoader;Password=******************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=90;"
  },
  "ftpHostname": "access832882062.webspace-data.io",
  "ftpUsername": "u101337964",
  "ftpPassword": "oN3yXNX[mRk?Lpk",
  "mailUser": "<EMAIL>",
  "mailPwd": "X4KkGrS46MKHqEXR",
  "mailIn": "Inbox",
  "mailArchive": "processed",
  "mailError": "error",
  "fileStream": "c:\\temp\\",
  "fileDestinationTemp": "c:\\cphiRoot\\POWER\\",
  "fileDestinationLive": "c:\\cphiRoot\\vindis\\inbound\\",
  //"fileDestinationDev": "c:\\cphiRoot\\vindisDev\\inbound\\",
  "fileDestinationStage": "c:\\cphiRoot\\vindisStage\\inbound\\",
  "fileDownloadLocation": "c:\\cphiRoot\\downloads",
  "serviceName": "CPHI.Spark.WindowsAppScraper",
  "chattyMode": "true",
  "customerName": "Vindis",
  "speedRatio": "1.2",
  "alsoDoStage": "false",
  "powerPassword": "marzoMAP",
  "overrideRunJobNow": null, //put the job name in here if you wish to force run it  e.g. "PeopleJob"
  //"ClientSettingsProvider.ServiceUri": "",
  "Quartz": {
    "SetReportsTBAndDebtJob": "0 30 19 * * ?",
    "CollectReportsJob-1": "0 30 20 * * ?",
    "SetReportsWIPJob": "0 0 21 * * ?",
    "SetReportsPartsStockJob": "0 0 22 * * ?",
    "CollectReportsJob-2": "0 0 23 * * ?"
  },
  "ManualStartDelays": { // delay in minutes from the start of the first job.
    "SetReportsTBAndDebtJob": "0",
    "CollectReportsJob-1": "30",
    "SetReportsWIPJob": "60",
    "SetReportsPartsStockJob": "120",
    "CollectReportsJob-2": "180"
  },

  "Monitor": {
    "AddLogMessageURL": "https://cphimonitorapi.azurewebsites.net/api/monitor/",
    "AppKey": "3"
  }
}