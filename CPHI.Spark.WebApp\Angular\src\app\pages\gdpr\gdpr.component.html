<nav class="navbar">
  <nav  class="generic">
    <h4 class="title">{{ constants.translatedText.Dashboard_GDPRCapture }}</h4>
    
    <ng-container *ngIf="service.gdpr">
      <div class="buttonGroup topDropdownButtons">
        <!-- Date selector -->
        <datePickerMultiSelect
          [monthsFromParent]="service.gdpr.months"
          (selectMonths)="updateMonths($event)"
          [includeYTD]="false"
          [includeLastYear]="false"
          [includeThisYear]="false"
        >
        </datePickerMultiSelect>
        <!-- Order type selector -->
        <pickerSimple
          [pickerItemsFromParent]="service.gdpr.orderTypes"
          [pickerLabel]="'order types'"
          (selectedPickerItems)="updateOrderTypes($event)"
        >
        </pickerSimple>
      </div>
    </ng-container>
  </nav>
</nav>

<div class="content-new">
  <div *ngIf="service.gdpr" class="content-inner-new">
    <button *ngIf="service.gdpr.peopleData && service.gdpr.salesManagerId == null" class="btn btn-primary backButton" (click)="backToSites()">
      <i class="fas fa-undo"></i>
    </button>
    <button *ngIf="service.gdpr.peopleData && constants.environment.customer == 'Vindis' && service.gdpr.salesManagerId != null" class="btn btn-primary backButton" (click)="backToManagers()">
      <i class="fas fa-undo"></i>
    </button>

    <ng-container *ngIf="service.gdpr.sitesData && !service.gdpr.peopleData">
      <gdprTable [sites]="true" (clickedSite)="selectSite($event)" id="GDPRTable"></gdprTable>
      <div class="tableSpacer"></div>
      <gdprTable [sites]="true" [regional]="true" (clickedSite)="selectSite($event)"></gdprTable>
    </ng-container>
    
    <gdprTable *ngIf="service.gdpr.peopleData  && constants.environment.customer != 'Vindis'" id="GDPRPeopleTable"></gdprTable>
    <gdprTable [managers]="service.gdpr.salesManagerId == null" (clickedSite)="selectManager($event)" *ngIf="service.gdpr.peopleData && constants.environment.customer == 'Vindis'" id="GDPRPeopleTable"></gdprTable>
  </div>
</div>