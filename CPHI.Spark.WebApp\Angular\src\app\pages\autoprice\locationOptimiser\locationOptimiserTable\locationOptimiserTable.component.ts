import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {GridOptions, GridReadyEvent, RowClassParams, RowDoubleClickedEvent} from 'ag-grid-community';
import {
  AutoPriceInsightsModalComponent
} from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import {CphPipe} from 'src/app/cph.pipe';
import {LocationOptimiserAdvert} from 'src/app/model/LocationOptimiserAdvert';
import {AGGridMethodsService} from 'src/app/services/agGridMethods.service';
import {ConstantsService} from 'src/app/services/constants.service';
import {ExcelExportService} from 'src/app/services/excelExportService';
import {SelectionsService} from 'src/app/services/selections.service';
import {LocationOptimiserService} from '../locationOptimiser.service';
import {BuildTotalAndAverageRowsParams} from 'src/app/model/BuildTotalAndAverageRowsParams';
import {CustomHeaderService} from 'src/app/components/customHeader/customHeader.service';
import {AutopriceRendererService} from 'src/app/services/autopriceRenderer.service';
import {AutoPriceInsightsModalService} from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service';
import {TableLayoutManagementService} from 'src/app/components/tableLayoutManagement/tableLayoutManagement.service';
import {CPHAutoPriceColDef} from 'src/app/model/CPHColDef';
import {CustomHeaderAdDetail} from 'src/app/components/customHeaderAdDetail/customHeaderAdDetail.component';
import { AutotraderImageCellComponent } from 'src/app/components/autotraderImageCell/autotraderImageCell.component';

@Component({
  selector: 'locationOptimiserTable',
  templateUrl: './locationOptimiserTable.component.html',
  styleUrls: ['./locationOptimiserTable.component.scss']
})
export class LocationOptimiserTableComponent implements OnInit {

  @Input() rowData: LocationOptimiserAdvert[];
  @Input() inTile: boolean;

  gridOptions: GridOptions;
  //redrawRowsSubscription: Subscription;
  public components: {
    [p: string]: any;
  } = {
    agColumnHeader: CustomHeaderAdDetail,
  };

  constructor(
    public service: LocationOptimiserService,
    public gridHelpersService: AGGridMethodsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public selectionsService: SelectionsService,
    public excelExportService: ExcelExportService,
    public constantsService: ConstantsService,
    private customHeader: CustomHeaderService,
    private autopriceRendererService: AutopriceRendererService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService,
    private tableLayoutManagementService: TableLayoutManagementService
  ) {
  }

  ngOnInit(): void {
    this.initialiseGrid();
  }

  initialiseGrid() {
    this.gridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      context: {thisComponent: this},
      getLocaleText: (params) => params.defaultValue,
      onRowDoubleClicked: (event) => this.openAutoTraderModal(event),
      onGridReady: (event) => this.onGridReady(event),
      onSelectionChanged: (params) => this.onRowSelectionChange(),
      onFilterChanged: (params) => this.onRowSelectionChange(),
      animateRows: false,
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      groupHeaderHeight: this.gridHelpersService.getHeaderHeight(),
      getRowClass: (params) => this.rowClassProvider(params),
      getRowHeight: (params) => { 
        if (params.node.isRowPinned() || this.inTile) {
          return this.gridHelpersService.getStandardHeight();
        } else {
          return this.gridHelpersService.getHeaderHeight();
        }
      },
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      pinnedBottomRowData: this.providePinnedBottomRowData(),
      rowSelection: 'single',
      rowGroupPanelShow: 'never',
      defaultColDef: {
        resizable: true,
        sortable: true,
        floatingFilter: this.inTile ? false : true,
        filterParams: {
          applyButton: false,
          clearButton: true,
          cellHeight: this.gridHelpersService.getFilterListItemHeight()
        },
        cellStyle: {
          display: 'flex',
          'align-items': 'center'
        },
        headerComponentParams: {showPinAndRemoveOptions: false},
        autoHeaderHeight: true,
        floatingFilterComponentParams: {suppressFilterButton: true}
      },
      columnTypes: {...this.service.columnTypesService.provideColTypes([])},
      columnDefs: this.provideColDefs()
    }
  }

  rowClassProvider(params: RowClassParams<any, any>): string | string[] {
    if (!this.inTile) {
      return;
    }
    if (params.node.data?.benefit > 0) {
      return 'greenHighlight'
    }
    ;
  }

  provideColDefs(): CPHAutoPriceColDef[] {
    if (this.inTile) {
      return [
        {
          headerName: 'Name',
          colId: 'RetailerSiteNameNew',
          field: 'RetailerSiteNameNew',
          type: 'label',
          width: 60,
          columnSection: 'Location optimiser'
        },
        {
          headerName: 'Retail Rating',
          colId: 'RetailerSiteRetailRatingNew',
          field: 'RetailerSiteRetailRatingNew',
          type: 'number',
          width: 40,
          columnSection: 'Location optimiser'
        },
        {
          headerName: 'Days to Sell',
          colId: 'RetailerSiteDaysToSellNew',
          field: 'RetailerSiteDaysToSellNew',
          type: 'number',
          width: 40,
          columnSection: 'Location optimiser'
        },
        {
          headerName: 'Strategy Price',
          colId: 'StrategyPriceNew',
          field: 'StrategyPriceNew',
          type: 'currency',
          width: 40,
          columnSection: 'Location optimiser'
        },
        {
          headerName: 'Net Benefit',
          colId: 'benefit',
          field: 'benefit',
          type: 'currencyWithPlusMinus',
          width: 40,
          columnSection: 'Location optimiser'
        }
      ]
    } else {
      return [
        {

          headerName: 'Vehicle', columnSection: 'Location optimiser',children: [
             {
            headerName: "Image",
            field: "ImageUrl",
            colId: "ImageUrl",
            type: "image",
            cellRendererFramework: AutotraderImageCellComponent,
                        cellRendererParams: { width: 120 },
            width: 100,
            maxWidth: 100
         },
            { headerName: 'Reg', colId: 'Reg', field: 'Reg', type: 'label', width: 60 },
            { headerName: 'Derivative', colId: 'Derivative', field: 'Derivative', type: 'label', width: 60 },
            { headerName: 'Days Listed', colId: 'DaysListed', field: 'DaysListed', type: 'number', width: 40 },
          ]

        },
        
        {
          headerName: 'Current Site', columnSection: 'Location optimiser',children: [
            { headerName: 'Name', colId: 'RetailerSiteNameCurrent', field: 'RetailerSiteNameCurrent', type: 'labelSetFilter', width: 60 },
            { headerName: 'Retail Rating', colId: 'RetailerSiteRetailRatingCurrent', field: 'RetailerSiteRetailRatingCurrent', type: 'number', width: 40 },
            { headerName: 'Days to Sell', colId: 'RetailerSiteDaysToSellCurrent', field: 'RetailerSiteDaysToSellCurrent', type: 'number', width: 40 },
            { headerName: 'Strategy Price', colId: 'StrategyPriceCurrent', field: 'StrategyPriceCurrent', type: 'currency', width: 40 },
          ]
        },
        {
          headerName: 'New Site', columnSection: 'Location optimiser', children: [
            {
              headerName: 'Name',
              colId: 'RetailerSiteNameNew',
              field: 'RetailerSiteNameNew',
              type: 'labelSetFilter',
              width: 60
            },
            {
              headerName: 'Retail Rating',
              colId: 'RetailerSiteRetailRatingNew',
              field: 'RetailerSiteRetailRatingNew',
              type: 'number',
              width: 40
            },
            {
              headerName: 'Days to Sell',
              colId: 'RetailerSiteDaysToSellNew',
              field: 'RetailerSiteDaysToSellNew',
              type: 'number',
              width: 40
            },
            {
              headerName: 'Strategy Price',
              colId: 'StrategyPriceNew',
              field: 'StrategyPriceNew',
              type: 'currency',
              width: 40
            }
          ]
        },
        {
          headerName: 'Vs', columnSection: 'Location optimiser', children: [
            {
              headerName: 'Retail Rating', colId: 'RetailerSiteRetailRatingVs', field: 'retailerSiteRetailRatingVs',
              type: 'numberWithPlusMinusAndColour', width: 40
            },
            {
              headerName: 'Days to Sell', colId: 'RetailerSiteDaysToSellVs', field: 'retailerSiteDaysToSellVs',
              type: 'numberWithPlusMinusAndColourSwitch', width: 40
            },
            {
              headerName: 'Strategy Price',
              colId: 'StrategyPriceVs',
              field: 'vs',
              type: 'numberWithPlusMinusAndColour',
              width: 40
            }
          ]
        },
        {
          headerName: 'Distance', columnSection: 'Location optimiser', children: [
            {headerName: 'Miles', colId: 'Distance', field: 'Distance', type: 'number', width: 30},
            {headerName: 'Cost', colId: 'cost', field: 'cost', type: 'currency', width: 30},
            {
              headerName: 'Net Benefit',
              colId: 'benefit',
              field: 'benefit',
              type: 'currencyWithPlusMinus',
              width: 40,
              sort: 'desc',
              sortIndex: 1
            }
          ]
        }
      ]
    }
  }


  onRowSelectionChange(): void {
    if (!this.service.tableLayoutManagement.gridApi) {
      return
    }
    this.service.tableLayoutManagement.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData())
  }

  providePinnedBottomRowData(): any[] {

    const params: BuildTotalAndAverageRowsParams = {
      colsToSkipAverageIfZero: [],
      colsToTotalOrAverage: ['RetailerSiteRetailRatingNew', 'RetailerSiteDaysToSellNew', 'StrategyPriceCurrent', 'StrategyPriceNew', 'vs', 'Distance', 'cost', 'benefit'],
      colsToTotal: ['cost', 'benefit', 'vs'],
      selectedCountFieldName: 'Derivative',
      labelFieldName: 'RetailerSiteNameCurrent',
      colsToSetToTrue: [],
      itemName: 'vehicle',
      api: this.service.tableLayoutManagement.gridApi,
      includeTotalRow: false,
      showTotalInAverage: false
    }

    return this.gridHelpersService.buildTotalAndAverageRows(params);
  }


  getImage(params): string {
    const row: LocationOptimiserAdvert = params.data;

    if (!row) return '';
    if (!row.ImageUrl) return '';

    return `<img style="height: 60px; width: 80px;" src=${row.ImageUrl} />`;
  }

  openAutoTraderModal(event: RowDoubleClickedEvent): void {
    const row: LocationOptimiserAdvert = event.data;
    if (!row) {
      return;
    }
    if (!row.AdvertId) {
      return;
    }

    let allAdIds: number[] = []
    this.service.tableLayoutManagement.gridApi.forEachNodeAfterFilter(node => {
      if (node.data && !node.isRowPinned()) {
        let nodeRow: LocationOptimiserAdvert = node.data;
        allAdIds.push(nodeRow.AdvertId)
      }
    })

    this.autoPriceInsightsModalService.initialise(row.AdvertId, allAdIds);

    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, {keyboard: true, size: 'lg'});
    modalRef.result.then((result) => {
    });
  }

  onGridReady(event: GridReadyEvent): void {
    this.service.tableLayoutManagement.gridApi = event.api;
    this.service.tableLayoutManagement.gridColumnApi = event.columnApi;
    this.service.tableLayoutManagement.gridApi.sizeColumnsToFit();
    this.selectionsService.triggerSpinner.next({show: false});
    this.service.tableLayoutManagement.originalColDefs = this.provideColDefs();
  }

  async excelExport() {
    const tableModel = this.service.tableLayoutManagement.gridApi.getModel() as any;

    // Strip out image URL column
    tableModel.columnModel.primaryColumns.shift();
    tableModel.columnModel.primaryColumnTree[0].children.shift();

    tableModel.rowsToDisplay.forEach(row => {
      row.data.Vs = row.data?.StrategyPriceNew - row.data?.StrategyPriceCurrent;
      row.data.Cost = (this.service.costPerMile * row.data?.Distance) + this.service.flatCostPerMove;
    })

    this.excelExportService.createSheetObject(tableModel, 'Location Optimiser - Suggested Vehicle Moves', 1, 0, false);
  }
}
