<nav class="navbar">

 <nav class="generic" >
    <h4 id="pageTitle">
      <div >
        {{service.constants.translatedText.HandoverDiary_Title}}
        <sourceDataUpdate [chosenDate]="service.constants.LastUpdatedDates.Deals"></sourceDataUpdate>
      </div>
    </h4>

    <ng-container *ngIf="service">

    <vehicleTypePickerSpain
      *ngIf="service.constants.environment.customer == 'RRGSpain'"
      [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"
      (updateVehicleTypes)="onUpdateVehicleTypes($event)"
    >
    </vehicleTypePickerSpain>
      
    <div class="buttonGroup topDropdownButtons" >
      <!-- Site selector -->
      <ng-container *ngIf="service.constants.environment.dealDone.showRRGSitePicker">
      <sitePickerRRG [allSites]="service.constants.sitesActiveSales" [sitesFromParent]="service.sites" 
      [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePickerRRG>
      </ng-container>
      
      <ng-container *ngIf="service.constants.environment.dealDone.showVindisSitePicker">
      <sitePicker [allSites]="service.constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites" [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePicker>
      </ng-container>

      <!-- VehicleType selector -->
      <vehicleTypePicker *ngIf="service.constants.environment.customer != 'RRGSpain'"
      [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"  [buttonClass]="'buttonGroupCenter'" (updateVehicleTypes)="onUpdateVehicleTypes($event)"></vehicleTypePicker>

      <!-- OrderType selector -->
      <orderTypePicker *ngIf="service.constants.environment.orderTypePicker" [orderTypeTypesFromParent]="service.orderTypeTypes"  [buttonClass]="'buttonGroupCenter'" (updateOrderTypes)="onUpdateOrderTypes($event)"></orderTypePicker>

      <!-- Franchise selector -->
      <franchisePicker *ngIf="service.constants.environment.franchisePicker" [franchisesFromParent]="service.franchises"  [buttonClass]="'buttonGroupCenter'" 
      (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>
      
      <!-- Exec selector -->
      <execPicker [buttonClass]="'buttonGroupCenter'" (updateExecs)="onUpdateExecs($event, true)" [IsManager]="false" [IsExec]="true"></execPicker>

      <!-- Manager selector -->
      <execPicker *ngIf="service.constants.environment.handoverDiary.showManagerSelector" [buttonClass]="'buttonGroupCenter'" (updateExecs)="onUpdateExecs($event, false)" [IsManager]="true" [IsExec]="false"></execPicker>



    </div>

    <!-- FOR SELECTING WEEK -->
    <div class="buttonGroup">
      <!-- previousWeek -->
      <button class="btn btn-primary" (click)="changeWeekOnClick(-1)"><i
          class="fas fa-caret-left"></i></button>

      <!-- dropdownWeek -->
      <div ngbDropdown class="d-inline-block" [autoClose]="true">
        <button (click)="makeWeeksOnClick()" class="btn btn-primary weekDropdown centreButton weekButton"
          ngbDropdownToggle>{{service.deliveryDate|cph:'week':0}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

          <!-- the ngFor buttons -->
          <button *ngFor="let week of service.weeks" (click)="selectWeekOnClick(week)"
            ngbDropdownItem>{{week|cph:'week':0}}</button>

      </div>
      </div>
      <!-- nextWeek -->
      <button class="btn btn-primary" (click)="changeWeekOnClick(1)"><i
          class="fas fa-caret-right"></i></button>
    </div>

  </ng-container>


  </nav>

  <nav class="pageSpecific"  >







  </nav>
</nav>



<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constants.environment.customer">
  <div class="content-new">


    <div class="tableHolder" *ngIf="service" id="handoverDiary">
      <ng-container >
        <!-- Big buttons -->
        <button class="btn btn-primary bigbutton" id="bigButtonLeft"
          (click)="changeWeekOnClick(-1)"><i class="fas fa-caret-left"></i></button>

        <button class="btn btn-primary bigbutton" id="bigButtonRight"
          (click)="changeWeekOnClick(1)"><i class="fas fa-caret-right"></i></button>
      </ng-container>

      <table id="mainTable">

        <thead>
          <tr>
            <th></th>
            <th *ngFor="let day of service.dayHeadings">
              <div class="spaceBetween column">
                <div>{{day.label}}</div>
                <div class="subHeading">({{service.constants.pluralise(day.dealCount, service.constants.translatedText.HandoverDiary_Handover, service.constants.translatedText.HandoverDiary_Handovers)}})</div>
              </div>
              
              
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let session of service.sessions">

            <td>{{session.label}}</td>

            <td [ngClass]="getDayClass(day)" *ngFor="let day of session.days">

              <div class="cardHolder">
                <!-- The cards -->
                <div [ngClass]="getCardClass(deal)" class="card" *ngFor="let deal of day.deals" 
                [ngbPopover]="popContent" [popoverTitle]="popoverTitle" placement="auto">

                      <ng-template #popContent>
                      
                        <ng-container *ngIf="service.constants.environment.dealDone.showRRGPopoverContent">
                          <popoverContentRRG [dealId]="deal.Id"></popoverContentRRG>
                        </ng-container>

                        <ng-container *ngIf="service.constants.environment.dealDone.showVindisPopoverContent">
                          <popoverContent [dealId]="deal.Id"></popoverContent>
                        </ng-container>

                      </ng-template>
      
                      <ng-template #popoverTitle>
                          <div class="popoverHeader" (click)="openDealOnClick(deal)">
                            {{deal.Customer + ' - ' + deal.VehicleTypeCode + ': ' + deal.OrderTypeDescription +', '+ deal.SiteDescription}} </div>
                        </ng-template>

                      
                  <ng-container *ngIf="service.constants.environment.handoverDiary.includeCustomerName && !service.constants.environment.handoverDiary.includeLastPhysicalLocation">
                    <div > {{deal.Customer}}</div>
                  </ng-container>

                  <ng-container *ngIf="service.constants.environment.handoverDiary.includeCustomerName && service.constants.environment.handoverDiary.includeLastPhysicalLocation">
                    <div>
                      <div id="customer"> {{deal.Customer}}</div>
                      <div id ="location"> {{deal.Location}}</div>
                    </div>
                  </ng-container>
                  
                  <div *ngIf="deal.isConfirmed || service.constants.environment.handoverDiary.includeHandoverDate" id="time"> {{deal.HandoverDate|cph:'shortTimeAM':0}}</div>
                  <div *ngIf="!deal.isConfirmed && !service.constants.environment.handoverDiary.includeHandoverDate" id="time"></div>

                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>



  </div>
</div>