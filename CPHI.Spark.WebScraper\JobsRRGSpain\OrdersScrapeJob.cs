﻿using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using OpenQA.Selenium.Interactions;
using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Diagnostics;
using CPHI.WebScraper.ViewModel;
using System.Threading.Tasks;
using log4net;
using CPHI.Spark.Model;
using CPHI.Repository;
using System.Linq;

namespace CPHI.Spark.WebScraper.Jobs
{
    // This is Ratio
    public  class OrdersScrapeJob : IJob
    {

        private string customerName;
        private string fileDestination;
        //private string fileDestinationDev;

        //some basic setup
        private static IWebDriver _driver;

        private List<OrderSelection> orderSelections = new List<OrderSelection>
            {
                new OrderSelection { Company = "Madrid" , Username = "ps11415" , Password = ConfigService.OrdersMadridPassword },
                new OrderSelection { Company = "Valencia" , Username = "ps11415V" , Password = ConfigService.OrdersValenciaPassword }
            };

        private static readonly ILog logger = LogManager.GetLogger(typeof(OrdersScrapeJob));

        public void Execute() { }
        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            fileDestination = ConfigService.FileDestination;
            //fileDestinationDev = ConfigService.FileDestinationDev;
            fileDestination = fileDestination.Replace("{destinationFolder}", "spain");
            //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "spain");
            customerName = "Spain";

            try
            {
                logger.Info("Starting job..");
                //delete old files

                ScraperMethodsService.ClearDownloadsFolder();

                // KillChrome();
                logger.Info("Killed existing chromes..");

                //set chrome options
                ChromeOptions options = ScraperMethodsService.SetChromeOptions("RRGSpainOrders", 9191);

                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";
                
                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));
                _driver.Manage().Cookies.DeleteAllCookies();

                logger.Info("Started chrome..");

                try
                {
                    await GetOrders();

                    _driver.Quit();
                    _driver.Dispose();
                    logger.Info("Succesfully completed");
                    stopwatch.Stop();
                }
                catch (Exception e)
                {
                    stopwatch.Stop();
                    errorMessage = e.ToString();
                    EmailerService eService = new EmailerService();
                    await eService.SendMail("RRG Spain orders scraper failed", $"{e.StackTrace}");
                }


            }
            catch (Exception e)
            {
                stopwatch.Stop();
                errorMessage = e.ToString();
                EmailerService eService = new EmailerService();
                await eService.SendMail("RRG Spain orders scraper failed", $"{e.StackTrace}");

               logger.Error($"Problem {e.ToString()}");
                _driver.Quit();
                _driver.Dispose();
                //KillChrome();
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WebScraper",
                    Customer = "RRG Spain",
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };

                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }


        private async Task GetOrders()
        {


            foreach (OrderSelection selection in orderSelections)
            {

                try
                {

                    WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                    IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

                    Actions keyPresser = new Actions(_driver);



                    ////////////////////////////////////////////////
                    ///////////////// FIRST LOG IN /////////////////
                    ////////////////////////////////////////////////

                    _driver.Navigate().GoToUrl("https://renault.imaweb.net"); //navigate to site

                    WaitAndFind("//input [@Type='text']", false).SendKeys(selection.Username); //enter username

                    WaitAndFind("//input [@Type='password']", false).SendKeys(selection.Password); //enter password

                    WaitAndFind("//button [@Id='loginButton']", true); //enter to site

                    WaitAndFind("//li [@Id='outletListItem0']", true); //click into the first site


                    // ScraperMethodsService.TakeScreenshot(_driver);


                    //////////////////////////////////////////////
                    /////////////// GET NEW ORDERS ///////////////
                    //////////////////////////////////////////////

                    WaitAndFind("//button [@Id='vertical-menu-trigger']", true); //click burger menu

                    WaitAndFind("//a [contains(text(), 'Informes')]", true); //click reports menu option

                    WaitAndFind("//a [contains(text(), 'Pedidos VN')]", true); //click new orders report

                    IWebElement imaMainFrame = WaitAndFind("//iframe [@name='imaMainFrame']", false); //find new iframe
                    _driver.SwitchTo().Frame(imaMainFrame); //switch to correct iframe

                    DateTime startDate = DateTime.UtcNow;

                    IWebElement noOrdersCheck = NoResultsCheck();

                    if (noOrdersCheck != null)
                    {
                        string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";

                        NoOrdersLogMessage("RatioNewOrdersJob");

                        // Just add a monitor message if no 
                        Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                        {
                            Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                            Project = "Loader",
                            Customer = "RRG Spain",
                            Environment = "Prod",
                            Task = "RatioNewOrdersJob",
                            StartDate = startDate,
                            EndDate = DateTime.UtcNow,
                            Status = "Pass",
                            Notes = "No ratio orders for New" + selection.Company,
                            HTML = string.Empty
                        };

                        await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                    }
                    else
                    {
                        WaitAndFind("//a [contains(@href, 'javascript:toxl')]", true); //hit excel download button

                        ScraperMethodsService.WaitUntilFileDownloaded("Report_PedidosVN");

                        MoveFile($@"{selection.Company}_newOrders");

                        _driver.SwitchTo().DefaultContent();
                    }
                    
                    logger.Info($"'Got new orders for {selection.Company}");


                    ////////////////////////////////////////////////
                    ///////////////// GET USED ORDERS //////////////
                    ////////////////////////////////////////////////
                    _driver.SwitchTo().DefaultContent();

                    WaitAndFind("//button [@Id='vertical-menu-trigger']", true); //click burger menu

                    WaitAndFind("//a [contains(text(), 'Informes')]", true); //click management reports

                    WaitAndFind("//a [contains(text(), 'Ventas VO')]", true);

                     imaMainFrame = WaitAndFind("//iframe [@name='imaMainFrame']", false); //find new iframe
                    _driver.SwitchTo().Frame(imaMainFrame); //switch to correct iframe

                    // If the no orders text ("No hay perdidos VO/VN") is present, just add Monitor message
                    noOrdersCheck = NoResultsCheck();

                    if (noOrdersCheck != null)
                    {

                        string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";

                        NoOrdersLogMessage("RatioUsedOrdersJob");

                        // Just add a monitor message if no 
                        Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                        {
                            Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                            Project = "Loader",
                            Customer = "RRG Spain",
                            Environment = "Prod",
                            Task = "RatioNewOrdersJob",
                            StartDate = startDate,
                            EndDate = DateTime.UtcNow,
                            Status = "Pass",
                            Notes = "No ratio orders for New" + selection.Company,
                            HTML = string.Empty
                        };

                        await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                    }
                    else
                    {
                        IWebElement table = WaitAndFind("//body/div[2]/div/div[2]/table", true);

                        WaitAndFind("//body/div[2]/div/div[2]/div/a", true); //hit excel download button

                        ScraperMethodsService.WaitUntilFileDownloaded("Report_ventasvo");

                        MoveFile($@"{selection.Company}_usedOrders");
                    }

                    ////////////////////////////////////////////////
                    ///////////////// LOG OUT //////////////////////
                    ////////////////////////////////////////////////

                    _driver.SwitchTo().DefaultContent(); //get the hell out of this iframe
                    WaitAndFind("//button [@Id='vertical-menu-trigger']", true); //click burger menu
                    WaitAndFind("//a [contains(text(), 'Salir')]", true); //click log out menu option
                    WaitAndFind("//button [contains(text(), 'SI')]", true); //yes log out

                }
                catch (Exception e)
                {
                    EmailerService eService = new EmailerService();
                    await eService.SendMail("RRG Spain orders scraper failed", $"{e.StackTrace}");
                    logger.Info("Row122");
                    logger.Error(e.Message);
                    throw e;
                }

            }


        }

        private static void NoOrdersLogMessage(string type)
        {
            DealerGroupName dealerGroup = DealerGroupName.RRGSpain;
            string connString = ConfigService.GetConnectionString(dealerGroup);

            using (var db = new CPHIDbContext(connString))
            {
                // GlobalParams
                GlobalParam lastUpdateDate = db.GlobalParams.First(x => x.Description == "ordersUpdateDate");
                lastUpdateDate.DateFrom = DateTime.UtcNow; 
                lastUpdateDate.TextValue = DateTime.UtcNow.ToLongDateString();

                // LogMessage for morning summary
                LogMessage logMessage = new LogMessage();
                logMessage.IsCompleted = true;
                logMessage.SourceDate = DateTime.UtcNow;
                logMessage.FinishDate = DateTime.UtcNow;
                logMessage.DealerGroup_Id = (int)dealerGroup;
                logMessage.Job = type;

                db.LogMessages.Add(logMessage);

                db.SaveChanges();
            }
        }

        private IWebElement NoResultsCheck()
        {
            IWebElement noOrdersCheck;

            try
            {
                noOrdersCheck = WaitAndFind("/html/body/div[2]/div//*[contains(text(),'No hay')]");
            }
            catch
            {
                noOrdersCheck = null;
            }

            if (noOrdersCheck == null)
            {
                try
                {
                    noOrdersCheck = WaitAndFind("//body/div[3]/div//*[contains(text(),'No se encontraron')]");
                }
                catch
                {
                    noOrdersCheck = null;
                }
            }

            return noOrdersCheck;
        }


        private List<string> GetSiteNames()
        {
            List<string> siteNames = new List<string>();

            for (int i = 1; i < 11; i++)
            {
                IWebElement optGroup = WaitAndFind($"/html/body/div[2]/div/div[1]/div/form/div[4]/select/optgroup[{i}]");
                siteNames.Add(optGroup.GetAttribute("label"));
            }

            return siteNames;
        }


        private IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "OrderScrape", findXPath, andClick);

            return result;
        }






        private void MoveFile(string fileName)
        {
            string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
            string newFilePathAndName = $@"{fileDestination}{prefix}{fileName}.xlsx";
            //string newFilePathAndNameDev = $@"{fileDestinationDev}{prefix}{fileName}.xlsx";

            string downloadedFile = Directory.GetFiles(ConfigService.FileDownloadLocation, "Report_*")[0];

            string oldLocation = downloadedFile;

            System.Threading.Thread.Sleep(1000);

            File.Move(oldLocation, newFilePathAndName); //move them to incoming
            //File.Copy(newFilePathAndName, newFilePathAndNameDev); //copy to dev

            logger.Info($"Moved file from {oldLocation} to {newFilePathAndName}");

            System.Threading.Thread.Sleep(1000);
        }






        private void KillChrome()
        {
            Process[] chromeInstances;
            Process[] chromeDriverInstances;
            //kill all chrome instances
            chromeInstances = Process.GetProcessesByName("chrome");
            foreach (Process p in chromeInstances) p.Kill();

            chromeDriverInstances = Process.GetProcessesByName("chromedriver");
            foreach (Process p in chromeDriverInstances)
            {
                p.Kill();
            }
        }

    }
}
