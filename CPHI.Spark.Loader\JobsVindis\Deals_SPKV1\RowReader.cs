﻿using CPHI.Spark.Model;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Loader
{
    public partial class DealsVindisJob : IJob
    {

        public class ProfitLookup
        {
            public string profitElement;
            public int[] profitColumns;
        }

        public class NameAndSite
        {
            public string name;
            public int siteId;
        }


        public int SiteConverter(string siteName)
        {
            //create a sites dictionary
            Dictionary<string, int> siteLookup = new Dictionary<string, int>()
                    {
                        {"Cookes Of Fakenham", 8},
                        {"Vindis Audi Bedford", 1},
                        {"Vindis Audi Cambridge", 2},
                        {"Vindis Audi Huntingdon", 3},
                        {"Vindis Audi Northampton", 4},
                        {"Vindis Audi Peterborough", 5},
                        {"Vindis AutoNow Bury St. Edmunds", 17},
                        {"Vindis AutoNow Cambridge (read only)", 18},
                        {"Vindis AutoNow Fakenham", 19},
                        {"Vindis AutoNow Sawston", 20},
                        {"Vindis Bentley Cambridge", 16},
                        {"Three 10 Automotive", 16},
                        {"Three 10 Automotive Bentley Cambridge", 16},
                        {"Vindis Ducati Cambridge", 15},
                        {"Vindis Ducati Peterborough", 15},
                        {"Vindis SEAT Milton Keynes", 14},
                        {"Vindis SEAT Motorcycles Milton Keynes", 14},
                        {"Vindis Skoda Bury St. Edmunds", 12},
                        {"Vindis Skoda Cambridge", 13},
                        {"Vindis Van Centre Huntingdon", 10},
                        {"Vindis Van Centre Northampton", 11},
                        {"Vindis VW Bedford", 6},
                        {"Vindis VW Cambridge", 7},
                        {"Vindis VW Huntingdon", 9},
                        {"Vindis Test Training", 23},
                    };

            return siteLookup[siteName];

        }


        public List<Person> ReadNames(List<List<string>> rows, List<string> headers)
        {
            List<NameAndSite> namesAndSites = new List<NameAndSite>();
            List<Person> newPeople = new List<Person>();

            int namesColumnIndex = headers.IndexOf("Sales Exec");
            int siteColumnIndex = headers.IndexOf("Dealership");

            foreach (var row in rows)
            {
                string name = row[namesColumnIndex];

                if (name == "") continue;

                if (namesAndSites.Select(x => x.name).Contains(name)) continue;

                string siteName = row[siteColumnIndex];
                int siteId = SiteConverter(siteName);

                NameAndSite nameAndSite = new NameAndSite();
                nameAndSite.name = name;
                nameAndSite.siteId = siteId;

                if (!dbPeople.Select(x => x.Name).Contains(name)) namesAndSites.Add(nameAndSite);
            }

            foreach (var nameAndSite in namesAndSites)
            {
                Person person = new Person();

                person.Id = 0;
                person.Name = nameAndSite.name;
                person.IsSalesExec = true;
                person.CurrentSite_Id = nameAndSite.siteId;
                person.SellsUsed = true;
                person.SellsNew = true;
                person.LastUpdateDate = DateTime.UtcNow;
                person.JobTitle = "Sales Exec";
                person.JobRole = "Sales Exec";
                person.IsRemoved = false;
                person.IsUpdated = false;
                person.HasLeft = false;
                person.HasSales = true;
                person.Sites = nameAndSite.siteId.ToString();
                person.DoNotRemove = false;
                person.AccessAllSites = false;
                person.Email = "ADDED BY DEALS LOADER";
                person.KnownAs = nameAndSite.name;
                person.DealerGroup_Id = 3;
                person.CurrentRetailerSite_Id = nameAndSite.siteId;

                newPeople.Add(person);
            }

            return newPeople;
        }



        public List<Deal> ReadDealRows(List<List<string>> rows, List<string> headers, string fileNameIn, int orderTypeId)
        {

            List<Deal> incomingDeals = new List<Deal>();
            int incomingProcessCount = 0;

            //create a franchises dictionary
            Dictionary<string, string> franchiseLookup = new Dictionary<string, string>()
                    {
                        {"Audi", "Audi"},
                        {"Volkswagen", "VW"},
                        {"BENTLEY", "Bentley"},
                        {"DUCATI", "Ducati"},
                        {"SEAT", "SEAT"},
                        {"Skoda", "Skoda"},
                    };

            //create a profit calc field dictionary
            Dictionary<string, string> profitCalcLookup = new Dictionary<string, string>()
                    {
                        { "VEHICLE WITHOUT VAT" , "Turnover" },
                        { "FACTORY OPTIONS" , "Options" },
                        { "SIV" , "SIV" },
                        { "RECONDITION COST" , "MechPrep" },
                        { "VAT (USED)" , "VAT" },
                        { "WARRANTY COST" , "WarrantyCost" },
                        { "FRONT END MOTAB" , "Front End" },
                        { "LOYALTY MY AUDI MOTAB" , "Bonus" },
                        { "MOTAB BONUS BOX " , "Bonus" },
                        { "MOTAB DELIVERY MARGIN" , "Delivery" },
                        { "MOTAB HANDLING FEE" , "Handling Fee" },
                        { "MOTAB PDI" , "PDI" },
                        { "QUALITY A8 MOTAB" , "Bonus" },
                        { "QUALITY SUSTAINABILITY MOTAB" , "Bonus" },
                        { "INVESTOR FUND" , "Bonus" },
                        { "PDI" , "PDI" },
                        { "DELIVERY MARGIN" , "Delivery" },
                        { "FRONT END" , "Front End" },
                        { "LOYALTY - MY AUDI" , "Bonus" },
                        { "NEW CAR REG BONUS" , "Front End" },
                        { "QUALITY A8" , "Bonus" },
                        { "QUALITY SUSTAINABILITY" , "Bonus" },
                        { "QUALITY TRANSFORM" , "Bonus" },
                        { "QUALITY TRANSFORM MOTAB" , "Bonus" },
                        { "RETAIL BONUS BOX" , "Front End" },
                        { "VOLUME - NETWORK COUNTING" , "Bonus" },
                        { "FLEET BASE - MONTHLY" , "Bonus" },
                        { "FLEET BONUS BOX" , "Bonus" },
                        { "FLEET DELIVERY MARGIN" , "Delivery" },
                        { "FLEET MONTHLY CONSISTENCY" , "Bonus" },
                        { "FLEET PDI " , "PDI" },
                        { "FLEET QUALITY - QUARTERLY" , "Bonus" },
                        { "BONUS 2" , "Front End" },
                        { "BONUS 3" , "Front End" },
                        { "BONUS 1" , "Front End" },
                        { "DEL PROFIT" , "Delivery" },
                        { "MONTHLY" , "Bonus" },
                        { "ON CONSIGNMENT" , "Front End" },
                        { "QUARTERLY" , "Bonus" },
                        { "FLEET BONUS 1" , "Bonus" },
                        { "MOTAB BONUS 1" , "Bonus" },
                        { "MOTAB BONUS" , "Bonus" },
                        { "MOTAB DEL PROFIT" , "Delivery" },
                        { "MOTAB MONTHLY" , "Bonus" },
                        { "MOTABILITY MARGIN" , "Handling Fee" },
                        { "FLEET FLAT RATE BACK END" , "Bonus" },
                        { "PDI COST" , "PDI" },
                        { "EXCLUSIVITY BONUS" , "Bonus" },
                        { "MOBILITY BASE" , "Handling Fee" },
                        { "BASE" , "Front End" },
                        { "EV DEVELOPMENT" , "Bonus" },
                        { "RENEWALS LOYALTY BONUS" , "Bonus" },
                        { "MOTABILITY BASE" , "Front End" },
                        { "FLEET PDI" , "PDI" },
                        { "CONSIGNMENT" , "Front End" },
                        { "FLEET CONSIGNMENT" , "Front End" },
                        { "LEAD MARGIN" , "Bonus" },
                        { "VRB 100%" , "Bonus" },
                        { "MOTAB BONUS PAYMENT" , "Bonus" },
                        { "SEAT LOYALTY BONUS" , "Front End" },
                        { "CONSISTENCY BONUS" , "Bonus" },
                        { "CRAFTER BONUS" , "Bonus" },
                        { "FRONT END SALES VOLUME ACHIEVEMENT" , "Front End" },
                        { "MOTAB BONUS BOX" , "Bonus" },
                        { "MOTAB DELIVERY PROFIT" , "Delivery" },
                        { "ACCESSORIES" , "Accessories" },
                        { "RETAILER" , "Other" },
                        { "GAP" , "GAP" },
                        { "WARRANTY" , "Warranty" },
                        { "TYRE & ALLOY" , "Tyre & Alloy" },
                        { "PAINT & FABRIC" , "Paint & Fabric" },
                        { "ALLOY" , "Alloy" },
                        { "COSMETIC REPAIR" , "Cosmetic" },
                        { "FACTORY" , "Front End" },
                        { "SERVICE PLAN" , "Service Plan" },
                        { "TYRE AND ALLOY WHEEL 36 £300 LIMIT" , "Tyre & Alloy" },
                        { "TYRE COVER" , "Tyre" },
                        { "INCENTIVES" , "Incentives" },
                        { "OVER ALLOWANCE" , "Over-Allowance" },
                        { "FINANCE INCOME" , "Finance Income" },
                        { "INVESTOR FUND MOTAB" , "Bonus" },
                        { "ALLOY WHEEL INSURANCE 36MTH £150 LIMIT" , "Alloy" },
                        { "HALF YEARLY" , "Bonus" },
                        { "FLEET DEL PROFIT" , "Delivery" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £10K-£20K £15K LIMIT" , "GAP" },
                        { "C.A.R.S. PLUS 36" , "Cosmetic" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £30K-£50K £25K LIMIT" , "GAP" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £20K-£30K £25K LIMIT" , "GAP" },
                        { "LEASE ASSET PROTECTION INDEMNIFIED £25K-£50K" , "GAP" },
                        { "ALLOY WHEEL INSURANCE 12" , "Alloy" },
                        { "ALLOY WHEEL INSURANCE 36" , "Alloy" },
                        { "LEASE ASSET PROTECTION INDEMNIFIED £25K-£50K £10K LIMIT" , "GAP" },
                        { "VPS ULTRA £20K-£50K" , "Paint & Fabric" },
                        { "RPM" , "Bonus" }, // Volume Network Counting to RPM - 7th April 2025
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £20K-£30K" , "GAP" },
                        { "TYRE AND ALLOY WHEEL 36" , "Tyre & Alloy" },
                        { "LCV RETAIL PRICE PROTECTION INDEMNIFIED £50K-£75K £25K LIMIT" , "GAP" },
                        { "CERAMIC PAINT PROTECTION" , "Paint & Fabric" },
                        { "MOTOR CX2 (VINDIS DUCATI)" , "Paint & Fabric" },
                        { "CONTRACT HIRE GAP" , "GAP" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £50K-£100K" , "GAP" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £50K-£100K £50K LIMIT" , "GAP" },
                        { "AGENCY" , "Handling Fee" },
                        { "VPS ULTRA + LEATHER GUARD £20K-£50K" , "Paint & Fabric" },
                        { "0% SUBSIDY" , "Front End" },
                        { "ALLOY WHEEL INSURANCE 12MTH £150 LIMIT" , "Alloy" },
                        { "ALLOY WHEEL INSURANCE 24" , "Alloy" },
                        { "ALLOY WHEEL INSURANCE 24 £150 LIMIT" , "Alloy" },
                        { "ALLOY WHEEL INSURANCE 36MTH" , "Alloy" },
                        { "BENTLEY CERAMIC PROTECTION" , "Paint & Fabric" },
                        { "C.A.R.S. PLUS 12" , "Cosmetic" },
                        { "C.A.R.S. PLUS 24" , "Cosmetic" },
                        { "CASH ALTERNATIVE OFFER" , "Bonus" },
                        { "CERAMIC PAINT PROTECTION + LEATHER GUARD" , "Paint & Fabric" },
                        { "CUPRA CONSIGNMENT" , "Front End" },
                        { "CUPRA FUND" , "Bonus" },
                        { "CUPRA VRB" , "Bonus" },
                        { "DEALER DISCOUNT" , "Front End" },
                        { "FCM INCOME" , "Service Plan Commission" },
                        { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £100K-£150K" , "GAP" },
                        { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £100K-£150K £50K LIMIT" , "GAP" },
                        { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £50K-£100K" , "GAP" },
                        { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £50K-£100K £50K LIMIT" , "GAP" },
                        { "FIXED PRICE SERVICE PLAN" , "Service Plan" },
                        { "FLEET GUARANTEED MARGIN" , "Handling Fee" },
                        { "FLEET INVESTOR FUND" , "Bonus" },
                        { "FLEET VRB 100%" , "Bonus" },
                        { "GUARANTEED MARGIN" , "Front End" },
                        { "LCV RETAIL PRICE PROTECTION INDEMNIFIED £30K-£50K MAX £25K" , "GAP" },
                        { "LEASE ASSET PROTECTION 4TH & 5TH YEAR £50K-£100K £50K LIMIT" , "GAP" },
                        { "LEASE ASSET PROTECTION INDEMNIFIED £0-£25K" , "GAP" },
                        { "LEASE ASSET PROTECTION INDEMNIFIED £0-£6K £6K LIMIT" , "GAP" },
                        { "LEASE ASSET PROTECTION INDEMNIFIED £50K-£100K" , "GAP" },
                        { "LEASE ASSET PROTECTION INDEMNIFIED £50K-£100K £50K LIMIT" , "GAP" },
                        { "LEASE ASSET PROTECTION INDEMNIFIED £6-£25K" , "GAP" },
                        { "LEASE ASSET PROTECTION INDEMNIFIED £6-£25K £10K LIMIT" , "GAP" },
                        { "Q6 BEV LEAD OPTIMISATION", "Bonus" },
                        { "Q6 ORDER BANK CONVERSION", "Bonus" },
                        { "Q6 MARGIN ADJUSTMENT", "Bonus" },
                        { "LEVEL 1 TAB" , "Bonus" },
                        { "LEVEL 2 TAB" , "Bonus" },
                        { "MARKETING PLAN ACTIVATION & AOI" , "Bonus" },
                        { "MOTORCYCLE RETAIL PRICE PROTECTION £10K-£15K £5K LIMIT" , "GAP" },
                        { "MOTORCYCLE RETAIL PRICE PROTECTION £15K-£20K £7.5K LIMIT" , "GAP" },
                        { "MOTORCYCLE RETAIL PRICE PROTECTION £5K-£10K £5K LIMIT" , "GAP" },
                        { "NEW REG BONUS" , "Front End" },
                        { "ONE CUSTOMER" , "Bonus" },
                        { "OPTION UPTAKE" , "Front End" },
                        { "ORDER BANK COVERAGE" , "Bonus" },
                        { "ORDER ENTRY SUBMISSION" , "Bonus" },
                        { "PAINT PROTECTION" , "Paint & Fabric" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £100K-£150K" , "GAP" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £100K-£150K £50K LIMIT" , "GAP" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £10K-£20K" , "GAP" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £150K-£200K £50K LIMIT" , "GAP" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £30K-£50K" , "GAP" },
                        { "SEAT TEST DRIVE INCOME" , "Front End" },
                        { "TAXI ASSET PROTECTION INDEMNIFIED £25K-£50K" , "GAP" },
                        { "TYRE AND ALLOY WHEEL - RUN FLAT 36 (£500 CLAIM LIMIT)" , "Tyre & Alloy" },
                        { "TYRE AND ALLOY WHEEL 12" , "Tyre & Alloy" },
                        { "TYRE AND ALLOY WHEEL 24" , "Tyre & Alloy" },
                        { "TYRE AND ALLOY WHEEL 24 £300 LIMIT" , "Tyre & Alloy" },
                        { "TYRE INSURANCE 12" , "Tyre" },
                        { "TYRE INSURANCE 24" , "Tyre" },
                        { "TYRE INSURANCE 36" , "Tyre" },
                        { "TYRE INSURANCE 36 £300" , "Tyre" },
                        { "VPS ULTRA £10K-£20K" , "Paint & Fabric" },
                        { "VPS ULTRA + LEATHER GUARD £10K-£20K" , "Paint & Fabric" },
                        { "WHEELGARD BLACK" , "WheelGard" },
                        { "WHEELGARD BLACK (WITH VPS OR CERAMIC)" , "WheelGard" },
                        { "WHEELGARD RED" , "WheelGard" },
                        { "WHEELGARD SILVER" , "WheelGard" },
                        { "WHEELGARD SILVER (WITH VPS OR CERAMIC)" , "WheelGard" },
                        { "PAINT REPAIR COSTS" , "BodyPrep" },
                        { "ADMINISTRATION FEE" , "Other" },
                        { "ADMIN" , "Other" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £6K-£10K £10K LIMIT" , "GAP" },
                        { "VPS ULTRA £0-£10K" , "Paint & Fabric" },
                        { "RETAIL PRICE PROTECTION INDEMNIFIED £0-£6K £6K LIMIT" , "GAP" },
                        { "SEAT WEB VOUCHER" , "Front End" },
                        { "SEAT WEB VOUCHER COST" , "Front End" },
                        { "SEAT VOUCHER COSTS" , "Front End" },
                        { "TEST DRIVE VOUCHER COST" , "Front End" },
                        { "PCP DEDUCTION" , "Bonus" },
                        { "SOFT PREP REPAIR COSTS" , "BodyPrep" },
                        { "TAXI ASSET PROTECTION INDEMNIFIED £10K-£25K £10K LIMIT" , "GAP" },
                        { "TAXI ASSET PROTECTION INDEMNIFIED £0-£10K £10K LIMIT" , "GAP" },
                        { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £20K-£30K" , "GAP" },
                        { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £0-£10K" , "GAP" },
                        { "LCV RETAIL PRICE PROTECTION INDEMNIFIED £20K-£30K £25K LIMIT" , "GAP" },
                        { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £10K-£20K" , "GAP" },
                        { "FINANCE ASSET PROTECTION 4TH & 5TH YEAR £30K-£50K" , "GAP" },
                        { "WARANTY COST" , "WarrantyCost" },
                        { "VPS ULTRA + LEATHER GUARD £0-£10K" , "Paint & Fabric" },
                        { "TYRE AND ALLOY WHEEL 12 £300 LIMIT" , "Tyre & Alloy" },
                        { "CONTRACT HIRE BASE" , "Bonus" },
                        { "FLEET RECONCILIATION % BOX" , "Bonus" },
                        { "FRONT END " , "Front End" },
                        { "2019 MARGIN ADJUSTMENT" , "Bonus" },
                        { "BONUS BOX" , "Bonus" },
                        { "TRANSFORM QUALITY MOTAB" , "Bonus" },
                        { "SUSTAINABILITY QUALITY MOTAB" , "Bonus" },
                        { "VOLUME TOTAL CONTRACT MOTAB" , "Bonus" },
                        { "TRANSFORM QUALITY" , "Bonus" },
                        { "SUSTAINABILITY QUALITY" , "Bonus" },
                        { "QUARTERLY PERFORMANCE BONUS" , "Bonus" },
                        { "CONSISTENCY" , "Bonus" },
                        { "BONUS" , "Bonus" },
                        { "DEALER DISCOUNT/FRONT END" , "Front End" },
                        { "SEAT STOCK SUPPORT COST" , "Bonus" },
                        { "SEAT STAR CAR BONUS" , "Bonus" },
                        { "FINANCE SUBSIDY" , "Finance Subsidy" },
                        { "SERVICE PLAN PENETRATION" , "Bonus" },
                        { "CONQUEST LEAD OPTIMISATION" , "Bonus" },
                        { "LOYALTY LEAD OPTIMISATION" , "Bonus" },
                        { "ZEV MANDATE" , "Bonus" }, // Amended from LEAD 360 to ZEV MANDATE 07/04/2025
                        { "RETAILER MARGIN" , "Bonus" },
                        { "RCH PROFIT ADJUSTMENT" , "Front End" },
                        { "FIXED PRICE SERVICE PLAN PAID BY DIRECT DEBIT" , "Service Plan" },
                        { "FIXED COST MAINTENANCE PAID BY DIRECT DEBIT" , "Service Plan" },
                        { "FIXED COST MAINTENANCE PAID IN FULL" , "Service Plan" },
                        { "FIXED PRICE SERVICE PLAN FREE OF CHARGE" , "Service Plan" },
                        { "FIXED PRICE SERVICE PLAN PAID IN FULL" , "Service Plan" },
                        { "MARKETING PLAN ACTIVATION  AOI" , "Bonus" },
                        { "PAINT  FABRIC" , "Paint & Fabric" },
                        { "TYRE  ALLOY" , "Tyre & Alloy" },
                        { "SERVICE PLAN PENETRATION MOTAB" , "Bonus" },
                        { "CONQUEST LEAD OPTIMISATION MOTAB" , "Bonus" },
                        { "LOYALTY LEAD OPTIMISATION MOTAB" , "Bonus" },
                        { "QUALITY 50%" , "Bonus" },
                        { "INVESTOR 50%" , "Bonus" },

                        // Added 23/12/2024 - FD #23873
                        { "E-TRON GT BEV LEAD OPTIMISATION" , "Bonus" },
                        { "E-TRON GT MARGIN ADJUSTMENT" , "Bonus" },
                        { "E-TRON GT RS BEV LEAD OPTIMISATION" , "Bonus" },
                        { "E-TRON GT RS ORDER BANK CONVERSION" , "Bonus" },
                        { "E-TRON GT RS MARGIN ADJUSTMENT" , "Bonus" }
                    };

            //profit lookup indices
            List<int> TurnoverColumnIndices = new List<int>();
            List<int> OptionsColumnIndices = new List<int>();
            List<int> SivColumnIndices = new List<int>();
            List<int> MechPrepColumnIndices = new List<int>();
            List<int> BodyPrepColumnIndices = new List<int>();
            List<int> VATColumnIndices = new List<int>();
            List<int> WarrantyColumnIndices = new List<int>();
            List<int> WarrantyCostColumnIndices = new List<int>();
            List<int> FrontEndColumnIndices = new List<int>();
            List<int> BonusColumnIndices = new List<int>();
            List<int> DeliveryColumnIndices = new List<int>();
            List<int> HandlingFeeColumnIndices = new List<int>();
            List<int> PdiColumnIndices = new List<int>();
            List<int> AccessoriesColumnIndices = new List<int>();
            List<int> GapColumnIndices = new List<int>();
            List<int> TyreAndAlloyColumnIndices = new List<int>();
            List<int> PaintAndFabricColumnIndices = new List<int>();
            List<int> AlloyColumnIndices = new List<int>();
            List<int> CosmeticColumnIndices = new List<int>();
            List<int> WheelGardColumnIndices = new List<int>();
            List<int> ServicePlanColumnIndices = new List<int>();
            List<int> ServicePlanCommissionColumnIndices = new List<int>();
            List<int> TyreColumnIndices = new List<int>();
            List<int> ColumnIndices = new List<int>();
            List<int> IncentivesColumnIndices = new List<int>();
            List<int> OverAllowanceColumnIndices = new List<int>();
            List<int> FinanceIncomeColumnIndices = new List<int>();
            List<int> OtherColumnIndices = new List<int>();
            List<int> FinanceSubsidyColumnIndices = new List<int>();


            //set a counter so we know where we are
            int colCount = -1;



            foreach (var header in headers)
            {
                colCount++;
                string profitType;

                //skip all the non-profit columns
                if (
                    header.ToUpper() == "ORDER DATE" ||
                    header.ToUpper() == "HANDOVER DATE" ||
                    header.ToUpper() == "INVOICE DATE" ||
                    header.ToUpper() == "ORDER NO" ||
                    header.ToUpper() == "KEYLOOP DMS MAGIC NUMBER" ||
                    header.ToUpper() == "DEALERSHIP" ||
                    header.ToUpper() == "CUSTOMER NAME" ||
                    header.ToUpper() == "CUSTOMER SCHEME" ||
                    header.ToUpper() == "SALES EXEC" ||
                    header.ToUpper() == "REG NO" ||
                    header.ToUpper() == "MAKE" ||
                    header.ToUpper() == "RANGE" ||
                    header.ToUpper() == "MODEL" ||
                    header.ToUpper() == "DERIVATIVE" ||
                    header.ToUpper() == "VIN NUMBER" ||
                    header.ToUpper() == "NEW/USED" ||
                    header.ToUpper() == "STOCKNUMBER" ||
                    header.ToUpper() == "VEHICLE" ||
                    header.ToUpper() == "FINANCE TYPE" ||
                    header.ToUpper() == "TOTAL PROFIT" ||
                    header.ToUpper() == "DEAL CLOSED" ||
                    header.ToUpper() == "MANUFACTURER ORDER NO" ||
                    header.ToUpper() == "TOTAL BONUS"
                    ) {
                    continue;
                };

                //try to match the column header to the massive list of known mappings
                try
                {
                    profitType = profitCalcLookup[headers[colCount].Replace("\"", "").Trim().ToUpper()];
                }
                //if we don't have a mapping shove it to other
                catch { profitType = "Other"; };


                switch (profitType)
                {
                    case "Turnover": { TurnoverColumnIndices.Add(colCount); break; }
                    case "Options": { OptionsColumnIndices.Add(colCount); break; }
                    case "SIV": { SivColumnIndices.Add(colCount); break; }
                    case "MechPrep": { MechPrepColumnIndices.Add(colCount); break; }
                    case "BodyPrep": { BodyPrepColumnIndices.Add(colCount); break; }
                    case "VAT": { VATColumnIndices.Add(colCount); break; }
                    case "Warranty": { WarrantyColumnIndices.Add(colCount); break; }
                    case "WarrantyCost": { WarrantyCostColumnIndices.Add(colCount); break; }
                    case "Front End": { FrontEndColumnIndices.Add(colCount); break; }
                    case "Bonus": { BonusColumnIndices.Add(colCount); break; }
                    case "Delivery": { DeliveryColumnIndices.Add(colCount); break; }
                    case "Handling Fee": { HandlingFeeColumnIndices.Add(colCount); break; }
                    case "PDI": { PdiColumnIndices.Add(colCount); break; }
                    case "Accessories": { AccessoriesColumnIndices.Add(colCount); break; }
                    case "GAP": { GapColumnIndices.Add(colCount); break; }
                    case "Tyre & Alloy": { TyreAndAlloyColumnIndices.Add(colCount); break; }
                    case "Paint & Fabric": { PaintAndFabricColumnIndices.Add(colCount); break; }
                    case "Alloy": { AlloyColumnIndices.Add(colCount); break; }
                    case "Cosmetic": { CosmeticColumnIndices.Add(colCount); break; }
                    case "WheelGard": { WheelGardColumnIndices.Add(colCount); break; }
                    case "Service Plan": { ServicePlanColumnIndices.Add(colCount); break; }
                    case "Service Plan Commission": { ServicePlanColumnIndices.Add(colCount); break; }
                    case "Tyre": { TyreColumnIndices.Add(colCount); break; }
                    case "Incentives": { IncentivesColumnIndices.Add(colCount); break; }
                    case "Over-Allowance": { OverAllowanceColumnIndices.Add(colCount); break; }
                    case "Finance Income": { FinanceIncomeColumnIndices.Add(colCount); break; }
                    case "Other": { OtherColumnIndices.Add(colCount); break; }
                    case "Finance Subsidy": { FinanceSubsidyColumnIndices.Add(colCount); break; }
                }
            }



            //create the lookup indices
            int dealershipIndex = headers.IndexOf("Dealership");
            int orderDateIndex = headers.IndexOf("Order Date");
            int handoverDateIndex = headers.IndexOf("Handover Date");
            int invoiceDateIndex = headers.IndexOf("Invoice Date");
            int salesExecIndex = headers.IndexOf("Sales Exec");
            int newUsedIndex = headers.IndexOf("New/Used");
            int financeTypeIndex = headers.IndexOf("Finance Type");
            int orderNoIndex = headers.IndexOf("Order No");
            int customerNameIndex = headers.IndexOf("Customer Name");
            int regIndex = headers.IndexOf("Reg No");
            int makeIndex = headers.IndexOf("Make");
            int modelIndex = headers.IndexOf("Model");
            int derivativeIndex = headers.IndexOf("Derivative");
            int stockNumberIndex = headers.IndexOf("StockNumber");
            int totalProfitIndex = headers.IndexOf("Total Profit");
            int manufacturerOrderNoIndex = headers.IndexOf("Manufacturer Order No");
            int dealClosedIndex = headers.IndexOf("Deal Closed");
            int chassisIndex = headers.IndexOf("Vin Number");

            int defaultFranchiseId = standingValues.Find(x => x.Description == "Non-Franchise").Id;


            foreach (var row in rows)
            {
                incomingProcessCount++;

                try
                {
                    if (row.Count == 0 || row[orderNoIndex] == "") { continue; } //skip empties

                    for (int i = 0; i < row.Count; i++)
                    {
                        if (row[i] == "-") { row[i] = "0"; };
                    }

                    if (row.Count != headers.Count)
                    {
                        //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                        logMessage.FailNotes = logMessage.FailNotes + $"{row[orderNoIndex]}: Skipped rowCol as had {row.Count} rowCols and needed {headers.Count}";
                        errorCount++;
                        continue;
                    }


                    //lookup objects required

                    //site
                    int siteId = 0;
                    string siteName = row[dealershipIndex];
                    try { siteId = SiteConverter(siteName); } catch { continue; }

                    //order date
                    string orderDateString = row[orderDateIndex];
                    DateTime orderDate = DateTime.ParseExact(orderDateString, "dd/MM/yyyy HH:mm:ss", null).Date;


                    //invoice date
                    string invoiceDateString = row[invoiceDateIndex];
                    DateTime invoiceDate = DateTime.ParseExact(invoiceDateString, "dd/MM/yyyy HH:mm:ss", null);

                    //handover date (invoice date if null)
                    string handoverDateString = row[handoverDateIndex];
                    DateTime handoverDate = invoiceDate;
                    try { handoverDate = DateTime.ParseExact(handoverDateString, "dd/MM/yyyy HH:mm:ss", null); } catch { };

                    //sales exec id
                    string salesExecString = row[salesExecIndex];
                    int? salesExecId = null;
                    
                    try { salesExecId = dbPeople.Find(x => x.Name == salesExecString).Id; } catch { }

                    // We have not found the exec
                    if(salesExecId == null) 
                    {
                        { }
                    }

                    //franchise
                    string franchiseString = row[makeIndex];
                    int franchiseId = defaultFranchiseId;
                    try { franchiseId = standingValues.Find(x => x.Description == franchiseLookup[franchiseString]).Id; } catch { }

                    //new or used
                    string newOrUsed = row[newUsedIndex];

                    //finance type
                    string financeType = row[financeTypeIndex];
                    bool isFinanced = true;
                    if (financeType == "Cash" || financeType == "") { isFinanced = false; };

                    //enquiry number
                    int enquiryNumber = int.Parse(row[orderNoIndex]);

                    //closed
                    string dealClosed = row[dealClosedIndex];
                    bool isClosed = false;

                    if (dealClosed == "Closed") { isClosed = true; };

                    //the profic calc
                    List<string> TurnoverColumnValues = row.Where((x, i) => TurnoverColumnIndices.Contains(i)).ToList();
                    List<string> OptionsColumnValues = row.Where((x, i) => OptionsColumnIndices.Contains(i)).ToList();
                    List<string> SivColumnValues = row.Where((x, i) => SivColumnIndices.Contains(i)).ToList();
                    List<string> MechPrepColumnValues = row.Where((x, i) => MechPrepColumnIndices.Contains(i)).ToList();
                    List<string> BodyPrepColumnValues = row.Where((x, i) => BodyPrepColumnIndices.Contains(i)).ToList();
                    List<string> VATColumnValues = row.Where((x, i) => VATColumnIndices.Contains(i)).ToList();
                    List<string> WarrantyColumnValues = row.Where((x, i) => WarrantyColumnIndices.Contains(i)).ToList();
                    List<string> WarrantyCostColumnValues = row.Where((x, i) => WarrantyCostColumnIndices.Contains(i)).ToList();
                    List<string> FrontEndColumnValues = row.Where((x, i) => FrontEndColumnIndices.Contains(i)).ToList();
                    List<string> BonusColumnValues = row.Where((x, i) => BonusColumnIndices.Contains(i)).ToList();
                    List<string> DeliveryColumnValues = row.Where((x, i) => DeliveryColumnIndices.Contains(i)).ToList();
                    List<string> HandlingFeeColumnValues = row.Where((x, i) => HandlingFeeColumnIndices.Contains(i)).ToList();
                    List<string> PdiColumnValues = row.Where((x, i) => PdiColumnIndices.Contains(i)).ToList();
                    List<string> AccessoriesColumnValues = row.Where((x, i) => AccessoriesColumnIndices.Contains(i)).ToList();
                    List<string> GapColumnValues = row.Where((x, i) => GapColumnIndices.Contains(i)).ToList();
                    List<string> TyreAndAlloyColumnValues = row.Where((x, i) => TyreAndAlloyColumnIndices.Contains(i)).ToList();
                    List<string> PaintAndFabricColumnValues = row.Where((x, i) => PaintAndFabricColumnIndices.Contains(i)).ToList();
                    List<string> AlloyColumnValues = row.Where((x, i) => AlloyColumnIndices.Contains(i)).ToList();
                    List<string> CosmeticColumnValues = row.Where((x, i) => CosmeticColumnIndices.Contains(i)).ToList();
                    List<string> WheelGardColumnValues = row.Where((x, i) => WheelGardColumnIndices.Contains(i)).ToList();
                    List<string> ServicePlanColumnValues = row.Where((x, i) => ServicePlanColumnIndices.Contains(i)).ToList();
                    List<string> ServicePlanCommissionColumnValues = row.Where((x, i) => ServicePlanCommissionColumnIndices.Contains(i)).ToList();
                    List<string> TyreColumnValues = row.Where((x, i) => TyreColumnIndices.Contains(i)).ToList();
                    List<string> IncentivesColumnValues = row.Where((x, i) => IncentivesColumnIndices.Contains(i)).ToList();
                    List<string> OverAllowanceColumnValues = row.Where((x, i) => OverAllowanceColumnIndices.Contains(i)).ToList();
                    List<string> FinanceIncomeColumnValues = row.Where((x, i) => FinanceIncomeColumnIndices.Contains(i)).ToList();
                    List<string> OtherColumnValues = row.Where((x, i) => OtherColumnIndices.Contains(i)).ToList();
                    List<string> FinanceSubsidyColumnValues = row.Where((x, i) => FinanceSubsidyColumnIndices.Contains(i)).ToList();


                    decimal turnover = 0; foreach (string value in TurnoverColumnValues) { { } turnover += decimal.Parse(value); };
                    decimal options = 0; foreach (string value in OptionsColumnValues) { { } options += decimal.Parse(value); };
                    decimal siv = 0; foreach (string value in SivColumnValues) { siv += decimal.Parse(value); };
                    decimal mechPrep = 0; foreach (string value in MechPrepColumnValues) { mechPrep += decimal.Parse(value); };
                    decimal bodyPrep = 0; foreach (string value in BodyPrepColumnValues) { bodyPrep += decimal.Parse(value); };
                    decimal vat = 0; foreach (string value in VATColumnValues) { vat += decimal.Parse(value); };
                    decimal warranty = 0; foreach (string value in WarrantyColumnValues) { warranty += decimal.Parse(value); };
                    decimal warrantyCost = 0; foreach (string value in WarrantyCostColumnValues) { warrantyCost += decimal.Parse(value); };
                    decimal frontEnd = 0; foreach (string value in FrontEndColumnValues) { frontEnd += decimal.Parse(value); };
                    decimal bonus = 0; foreach (string value in BonusColumnValues) { bonus += decimal.Parse(value); };
                    decimal delivery = 0; foreach (string value in DeliveryColumnValues) { delivery += decimal.Parse(value); };
                    decimal handlingFee = 0; foreach (string value in HandlingFeeColumnValues) { handlingFee += decimal.Parse(value); };
                    decimal pdi = 0; foreach (string value in PdiColumnValues) { pdi += decimal.Parse(value); };
                    decimal accessories = 0; foreach (string value in AccessoriesColumnValues) { accessories += decimal.Parse(value); };
                    decimal gap = 0; foreach (string value in GapColumnValues) { gap += decimal.Parse(value); };
                    decimal tyreAndAlloy = 0; foreach (string value in TyreAndAlloyColumnValues) { tyreAndAlloy += decimal.Parse(value); };
                    decimal paintAndFabric = 0; foreach (string value in PaintAndFabricColumnValues) { paintAndFabric += decimal.Parse(value); };
                    decimal alloy = 0; foreach (string value in AlloyColumnValues) { alloy += decimal.Parse(value); };
                    decimal cosmetic = 0; foreach (string value in CosmeticColumnValues) { cosmetic += decimal.Parse(value); };
                    decimal wheelGard = 0; foreach (string value in WheelGardColumnValues) { wheelGard += decimal.Parse(value); };
                    decimal servicePlan = 0; foreach (string value in ServicePlanColumnValues) { servicePlan += decimal.Parse(value); };
                    decimal servicePlanCommission = 0; foreach (string value in ServicePlanCommissionColumnValues) { servicePlanCommission += decimal.Parse(value); };
                    decimal tyre = 0; foreach (string value in TyreColumnValues) { tyre += decimal.Parse(value); };
                    decimal incentives = 0; foreach (string value in IncentivesColumnValues) { incentives += decimal.Parse(value); };
                    decimal overAllowance = 0; foreach (string value in OverAllowanceColumnValues) { overAllowance += decimal.Parse(value); };
                    decimal financeIncome = 0; foreach (string value in FinanceIncomeColumnValues) { financeIncome += decimal.Parse(value); };
                    decimal financeSubsidy = 0; foreach (string value in FinanceSubsidyColumnValues) { financeSubsidy += decimal.Parse(value); };

                    OtherColumnValues = OtherColumnValues.Where(x => x != "").ToList();

                    //add options to turnover, take the vat out first
                    turnover = turnover + (options / 1.2m);

                    decimal other = 0; foreach (string value in OtherColumnValues)
                    {

                        try { other += decimal.Parse(value); } catch { };

                    };

                    decimal PcpFinanceIncome = 0;
                    decimal OtherFinanceIncome = 0;

                    if (financeType == "Personal Contract Purchase") PcpFinanceIncome = financeIncome;
                    else OtherFinanceIncome = financeIncome;

                    //populate the bools
                    bool hasServicePlan = (servicePlan != 0 || servicePlanCommission != 0);
                    bool hasCosmetic = cosmetic > 0;
                    bool hasWheelGuard = wheelGard > 0;
                    bool hasGap = gap > 0;
                    bool hasWarranty = warranty > 0;
                    bool hasPaintAndFabric = paintAndFabric > 0;
                    bool hasTyre = tyre > 0;
                    bool hasAlloy = alloy > 0;
                    bool hasTyreAndAlloy = tyreAndAlloy > 0;

                    decimal error = 0;

                    //and total product count
                    // 11/02/22 removing service plan from total prod count as per Vinds request
                    bool[] productBools = { hasCosmetic, hasGap, hasWarranty, hasPaintAndFabric, hasTyre, hasAlloy, hasTyreAndAlloy, hasWheelGuard };
                    // int totalProductCount = productBools.Where(b => b).Count();

                    //calculate the balancing figure to allocate to 'other'. calc is different for new and used.
                    decimal calculatedProfit = 0;
                    decimal totalProfit = decimal.Parse(row[totalProfitIndex]);

                    string missingSiv = "";

                    if (newOrUsed == "Used")
                    {
                        if (siv == 0)
                        {
                            siv = totalProfit = 0;
                            missingSiv = " (MISSING SIV)";
                        };

                        calculatedProfit = turnover - siv - mechPrep + bodyPrep - vat + warranty + warrantyCost + frontEnd + bonus + delivery + handlingFee + pdi + accessories + gap + tyreAndAlloy + paintAndFabric + alloy + cosmetic + wheelGard + servicePlan + servicePlanCommission + tyre + incentives + overAllowance + financeIncome + financeSubsidy + other;
                    }
                    else
                    {
                        calculatedProfit = warranty + warrantyCost + frontEnd + bonus + delivery + handlingFee + pdi + accessories + gap + tyreAndAlloy + paintAndFabric + alloy + cosmetic + wheelGard + servicePlan + servicePlanCommission + tyre + incentives + overAllowance + financeIncome + financeSubsidy + other;
                    }

                    error = totalProfit - calculatedProfit;

                    //calc the cost of sale differently for new and used
                    decimal costOfSale = 0;

                    if (newOrUsed == "Used")
                    {
                        costOfSale = 0 - siv;
                    }
                    else
                    {
                        costOfSale = 0 - turnover + frontEnd;
                    }

                    //the strings
                    string derivative = row[derivativeIndex];
                    if (derivative.Length > 50)
                    {
                        derivative = derivative.Substring(0, 50);
                    }

                    string description = row[makeIndex] + " " + row[modelIndex];
                    if (description.Length > 50)
                    {
                        description = description.Substring(0, 50);
                    }

                    string stockNumberToUse = row[stockNumberIndex]; //first set the stock number to the value in the file
                    string stockNumberFirstChar = null;
                    if (stockNumberToUse.Length > 0) { stockNumberFirstChar = stockNumberToUse.Substring(0,1); } //grab the first char to test if it is good
                    string chassis = row[chassisIndex]; //also grab the full chassis

                    if (
                        stockNumberFirstChar != "N" && stockNumberFirstChar != "U" && stockNumberFirstChar != "D" && stockNumberFirstChar != "Q" && //if we have a bad stock number (good ones start N, U or D)
                        chassis != string.Empty //and we DO have a chassis
                        )
                    {
                        Stock matchingStockItem = dbStocks.FirstOrDefault(x => x.Chassis == chassis); //then try to find the chassis in stocks and grab the stocknumberfull from there
                        if (matchingStockItem != null) { stockNumberToUse = matchingStockItem.StockNumberFull; };
                    };
                    if (stockNumberToUse == null || stockNumberToUse == String.Empty) { stockNumberToUse = enquiryNumber.ToString(); }; //if we can't get a stock number then set to the enquirynumber



                    //if this deal exists then only update the stock number IF the currently held one is BAD
                    string currentStockNumber = "";
                    bool currentStockNumberGood = false;
                    string currentStockNumberFirstChar = null;
                    
                    try { currentStockNumber = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).StockNumber; } catch { }; //find the stocknumber based on the enquirynumber if it exists

                    if (currentStockNumber.Length > 0) { currentStockNumberFirstChar = currentStockNumber.Substring(0, 1); } //grab the first char to test if it is good

                    if (
                        currentStockNumberFirstChar == "N" || currentStockNumberFirstChar == "U" || currentStockNumberFirstChar == "D" || currentStockNumberFirstChar == "Q" //if we have a good stock number (good ones start N, U or D)
                        )
                    {
                        currentStockNumberGood = true;
                    }

                    if (currentStockNumberGood)
                    {
                        stockNumberToUse = currentStockNumber;
                    }



                    string oemReference = null;
                    if (manufacturerOrderNoIndex > -1) { oemReference = row[manufacturerOrderNoIndex]; };

                    //vehicle type
                    string vehicleType = row[newUsedIndex];
                    int vehicleTypeId = vehicleTypes.First(x => x.Description == vehicleType).Id;
                    if (stockNumberFirstChar == "D" && newOrUsed == "Used") { vehicleTypeId = vehicleTypes.First(x => x.Description == "Demo").Id; };

                    //if this is a duplicate stock number then append an ! at the end
                    //if (incomingDeals.Select(x => x.StockNumber).Contains(stockNumberToUse)) { stockNumberToUse = stockNumberToUse + "!"; };

                    //is delivered?
                    bool isDelivered = false;

                    if (fileNameIn.Contains("DELIVERED")) { isDelivered = true; }
                    else try { isDelivered = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).IsDelivered; } catch { }

                    //reg string
                    string reg = row[regIndex];

                    //location & stockdate
                    string lastPhysicalLocation = null;
                    DateTime? today = DateTime.Today;
                    DateTime? stockDate = null;
                    int vehicleAge = 0;

                    //first set the location, stockdate and vehicle age to the existing values in deals
                    if (enquiryNumber != 0)
                    {
                        try
                        {
                            lastPhysicalLocation = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).LastPhysicalLocation;
                            stockDate = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).StockDate.Value;
                            vehicleAge = dbDeals.Find(x => x.EnquiryNumber == enquiryNumber).VehicleAge;
                        }
                        catch { };
                    }

                    if (lastPhysicalLocation == null || lastPhysicalLocation == "") lastPhysicalLocation = "Not found in SM";

                    if (stockNumberToUse != "" && stockNumberToUse != null)
                    {
                        var stocksMatch = dbStocks.FirstOrDefault(x => x.StockNumberFull == stockNumberToUse);
                        if(stocksMatch != null)
                        {
                            lastPhysicalLocation = stocksMatch.PhysicalLocation;
                            stockDate = stocksMatch.StockDate;
                            vehicleAge = (today - stockDate).Value.Days;
                        }
                    }

                    if (reg != "" && reg != null)
                    {
                        var stocksMatch = dbStocks.FirstOrDefault(x => x.Reg == reg);
                        if (stocksMatch != null)
                        {
                            lastPhysicalLocation = stocksMatch.PhysicalLocation;
                            stockDate = stocksMatch.StockDate;
                            vehicleAge = (today - stockDate).Value.Days;
                        }
                    }

                    // Ensure it is not null/empty, numeric and less than 50 
                    if (!string.IsNullOrEmpty(oemReference) && oemReference.Length <= 50 && oemReference.All(char.IsDigit))
                    {
                        var stocksMatch = dbStocks.FirstOrDefault(x => x.SupplierOrderNo == oemReference.ToString());
                        if(stocksMatch != null)
                        {
                            lastPhysicalLocation = stocksMatch.PhysicalLocation;
                            stockDate = stocksMatch.StockDate;
                            vehicleAge = (today - stockDate).Value.Days;
                        }
                    }
                    else
                    {
                        oemReference = null;
                    }

                    if (financeType == "Hire Purchase") { financeType = "HP"; };
                    if (financeType == "Personal Contract Purchase") { financeType = "PCP"; };
                    if (financeType == "Personal Contract Hire") { financeType = "PCH"; };

                    string originalSource = fileNameIn.Substring(0, Math.Min(fileNameIn.Length, 50));

                    Deal d = new Deal(); //initialise new one

                    d.OrderDate = orderDate;
                    d.ActualDeliveryDate = handoverDate;
                    d.InvoiceDate = invoiceDate;
                    d.HandoverDate = handoverDate;
                    d.EnquiryNumber = enquiryNumber;
                    d.Site_Id = siteId;
                    d.Customer = row[customerNameIndex] + missingSiv;
                    d.Salesman_Id = salesExecId.Value;
                    d.Reg = reg;
                    d.Description = description;
                    d.Model = row[modelIndex];
                    d.VariantTxt = derivative;
                    d.OrderType_Id = orderTypeId;
                    d.VehicleType_Id = vehicleTypeId;
                    d.Franchise_Id = franchiseId;
                    d.StockNumber = stockNumberToUse;
                    d.IsFinanced = isFinanced;
                    d.Discount = incentives;
                    d.ServicePlanSale = servicePlan;
                    d.OemDeliverySale = delivery;
                    d.WarrantySale = warranty;
                    d.StandardWarrantyCost = warrantyCost;
                    d.AccessoriesSale = accessories;
                    d.NewBonus1 = bonus;
                    d.NewBonus2 = handlingFee;
                    d.PartExOverAllowance1 = overAllowance;
                    d.RCIFinanceCommission = PcpFinanceIncome;
                    d.FinanceCommission = OtherFinanceIncome;
                    //d.FAndIProfit = cosmetic + wheelGard + paintAndFabric + gap + servicePlan + warranty + tyre + alloy + tyreAndAlloy + financeIncome;
                    d.GapInsuranceCommission = gap;
                    d.Sale = turnover;
                    // d.TotalVehicleProfit = totalProfit;
                    d.TotalNLProfit = totalProfit;
                    d.PDICost = pdi;
                    d.CosmeticInsuranceCommission = cosmetic;
                    d.WheelGuardCommission = wheelGard;
                    d.TyreInsuranceCommission = tyre;
                    d.AlloyInsuranceCommission = alloy;
                    d.TyreAndAlloyInsuranceCommission = tyreAndAlloy;
                    d.PaintProtectionAccessorySale = paintAndFabric;
                    d.HasServicePlan = hasServicePlan;
                    d.HasCosmeticInsurance = hasCosmetic;
                    d.HasGapInsurance = hasGap;
                    d.HasTyreInsurance = hasTyre;
                    d.HasAlloyInsurance = hasAlloy;
                    d.HasTyreAndAlloyInsurance = hasTyreAndAlloy;
                    d.HasWheelGuard = hasWheelGuard;
                    d.HasWarranty = hasWarranty;
                    d.HasPaintProtectionAccessory = hasPaintAndFabric;
                    d.Error = error;
                    d.CoS = costOfSale;
                    d.MechPrep = mechPrep * -1;
                    d.BodyPrep = bodyPrep;
                    // d.TotalProductCount = totalProductCount;
                    d.IsDelivered = isDelivered;
                    d.IsRemoved = false;
                    d.RemovedDate = null;
                    d.OriginalSource = originalSource;
                    d.LastUpdated = DateTime.UtcNow;
                    d.Units = 1;
                    d.AccountingDate = handoverDate;
                    d.CreatedDate = DateTime.UtcNow;
                    //d.IsCancelled = false;
                    // d.TotalVehicleProfit = totalProfit;
                    d.WhenNew = DateTime.UtcNow;
                    d.IsInvoiced = true;
                    d.Other = other;
                    d.FinanceCo = financeType;
                    d.VatCost = vat * -1;
                    d.DeliverySite_Id = siteId;
                    d.OemReference = oemReference;
                    d.LastPhysicalLocation = lastPhysicalLocation;
                    d.VehicleClass_Id = 207; //need to update
                    d.StockDate = stockDate;
                    d.VehicleAge = vehicleAge;
                    d.HasWheelGuard = false;
                    d.FinanceType = financeType;
                    d.IsClosed = isClosed;
                    d.FinanceSubsidy = financeSubsidy;

                    incomingDeals.Add(d);

                    //Console.WriteLine(incomingProcessCount);
                }

                catch (Exception err)
                {

                    if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                    errorCount++;
                    continue;
                }


            }

            return incomingDeals;


        }
    }
}
