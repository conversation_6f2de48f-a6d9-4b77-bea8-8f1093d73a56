using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using Quartz;
using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using log4net;
using CPHI.Spark.WebScraper.Services;
using OpenQA.Selenium.Support.UI;
using System.Threading;
using CPHI.WebScraper.ViewModel;
using System.Collections.Generic;
using System.Linq;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.BusinessLogic.AutoPrice;
using SeleniumExtras.WaitHelpers;
using System.Globalization;

namespace CPHI.Spark.WebScraper.Jobs
{
   [DisallowConcurrentExecution]
   public class SalesmasterUpdatePricesJob : IJob
   {
      // Enum to define the operation type for vehicle processing function
      private enum VehicleProcessOperation
      {
         Update,
         Verify
      }

      private static readonly ILog logger = LogManager.GetLogger(typeof(SalesmasterUpdatePricesJob));
      private static IWebDriver _driver;
      private const string homePageLink = "https://vindis.salesmaster.co.uk";
      private DealerGroupName dealerGroup;

      public void Execute() { }

      // Note: Only for Three10/AutoNow sites
      public async Task Execute(IJobExecutionContext context)
      {
         logger.Info("");
         logger.Info("========================= Starting SalesmasterUpdatePricesJob =====================================");

         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         // Set dealer group
         dealerGroup = DealerGroupName.Vindis;

         try
         {
            logger.Info("Starting Salesmaster update prices job...");

            // Get the list of vehicles that need price updates
            List<SalesmasterVehicle> vehicles = await GetPriceChangesToProcess(dealerGroup);

            // Do not run if no changes
            if(vehicles.Count == 0) { return; }

            // Set up Chrome driver
            var service = ChromeDriverService.CreateDefaultService();
            service.HostName = "127.0.0.1";

            ChromeOptions options = ScraperMethodsService.SetChromeOptions("SalesmasterUpdate", 9229);

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

            //var vehicles = new List<SalesmasterVehicle>
            //{
            //      new SalesmasterVehicle("EU20KWW", 13200, 101), // 13200
            //      new SalesmasterVehicle("AK17UHS", 11701, 102), // 11700
            //};

            // Login to Salesmaster
            bool loginSuccessful = await LoginAsync();

            if (loginSuccessful)
            {
               logger.Info($"Login OK, continuing with {vehicles.Count} vehicles to update...");

               // Update prices
               UpdateSalesmaster(vehicles);

               // Verify changes
               VerifyAllChanges(vehicles);
            }
            else
            {
               logger.Info($"Failed to login, quitting...");
            }

            // Clean up
            _driver.Quit();
            _driver.Dispose();
            stopwatch.Stop();

            logger.Info("========================= Completed SalesmasterUpdatePricesJob =====================================");
         }
         catch (Exception e)
         {
            stopwatch.Stop();
            errorMessage = e.ToString();
            logger.Error($"Problem with Salesmaster update prices job: {e}");
            EmailerService eService = new EmailerService();
            await eService.SendMail($"❌ FAILURE {this.GetType().Name}", $"{e.StackTrace}");
            throw;
         }
      }

      public async Task<List<SalesmasterVehicle>> GetPriceChangesToProcess(DealerGroupName dealerGroup)
      {
         PriceChangesService priceChangesService = new PriceChangesService(ConfigService.GetConnectionString(dealerGroup));
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

         bool includeUnPublished = retailers.First().IncludeUnPublishedAds;
         GetPriceChangesNewParams parms = await priceChangesService.CreateParmsToGetChanges(dealerGroup, retailers, includeUnPublished);
         var todayChangesFirstPass = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

         // Early return if none
         if (todayChangesFirstPass.totalChanges.Count == 0)
         {
            return new List<SalesmasterVehicle>();
         }

         // There are some changes, so now re-run the vehicle opt-out updater
         var optOutsDataAccess = new OptOutsDataAccess(ConfigService.GetConnectionString(dealerGroup));
         await optOutsDataAccess.CreateDailyOptOuts(dealerGroup);

         // Run again in case we just made some optouts
         GetTodayChangesResponse todayChangesResponse = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

         string _connectionString = ConfigService.GetConnectionString(dealerGroup);
         var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);

         List<SalesmasterVehicle> result = todayChangesResponse.totalChanges.ConvertAll(x => new SalesmasterVehicle(x));
         return result;
      }

      private async Task<bool> LoginAsync()
      {
         string loginVerifyPath = "//*[@id='sm-header']/div[3]/a"; // This is the email icon in the top header
         const int maxAttempts = 5;
         const int delayBetweenAttemptsMs = 10000;

         for (int attempt = 1; attempt <= maxAttempts; attempt++)
         {
            logger.Info($"Login attempt {attempt} of {maxAttempts}...");

            try
            {
               // Navigate to login page
               _driver.Navigate().GoToUrl(homePageLink);

               try
               {
                  IWebElement vehiclesMenuButton = WaitAndFind(loginVerifyPath, false);
                  logger.Info("Login successful - already logged in.");
                  return true;
               }
               catch
               {
                  
               }

               // Wait for username field and enter username
               IWebElement usernameField = WaitAndFind("//input[@id='user_email']");
               usernameField.SendKeys("<EMAIL>"); // Replace with actual username from config

               // Wait for password field and enter password
               IWebElement passwordField = WaitAndFind("//input[@id='user_password']");
               passwordField.SendKeys(ConfigService.SalesmasterPassword);

               // Click login button
               IWebElement loginButton = WaitAndFind("//input[@id='submit']", true);

               // Wait for login to complete
               Thread.Sleep(3000);

               // Verify login was successful
               try
               {
                  IWebElement vehiclesMenuButton = WaitAndFind(loginVerifyPath, false);
                  logger.Info("Login successful");
                  return true;
               }
               catch
               {
                  logger.Warn("Login verification failed - dashboard element not found");
               }
            }
            catch (Exception e)
            {
               logger.Warn($"Login failed on attempt {attempt}: {e.Message}");
            }

            if (attempt < maxAttempts)
            {
               logger.Info($"Retrying in {delayBetweenAttemptsMs / 1000} seconds...");
               await Task.Delay(delayBetweenAttemptsMs);
            }
         }

         logger.Error("All login attempts failed.");
         return false;
      }


      private void UpdateSalesmaster(List<SalesmasterVehicle> vehicles)
      {
         // Call the generic method with Update operation
         ProcessVehicles(vehicles, VehicleProcessOperation.Update, "Update Salesmaster");
      }

      private void VerifyAllChanges(List<SalesmasterVehicle> vehicles)
      {
         // Call the generic method with Verify operation
         List<SalesmasterVehicle> amendedVehs = vehicles.Where(x => x.PriceChanged).ToList();

         if (amendedVehs.Count > 0)
         {
            logger.Info($"VerifyAllChanges: {amendedVehs.Count} price changes to verify.");
            ProcessVehicles(amendedVehs, VehicleProcessOperation.Verify, "Verifying Changes Salesmaster");
         }
         else
         {
            logger.Info($"VerifyAllChanges: No price changes to verify.");
         }
      }

      // Generic method to process vehicles for either updating or verifying
      private void ProcessVehicles(List<SalesmasterVehicle> vehicles, VehicleProcessOperation operation, string operationDescription)
      {
         try
         {
            logger.Info($"Starting {operationDescription}");

            // For verify operation, navigate to home page first
            if (operation == VehicleProcessOperation.Verify)
            {
               _driver.Navigate().GoToUrl("https://vindis.salesmaster.co.uk/vehicles");
            }

            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            DateTime start = DateTime.Now;

            // Group vehicles by retailer site
            var vehiclesBySite = vehicles.ToLookup(x => x.RetailerSiteId);

            // Process each site group
            foreach (IGrouping<int, SalesmasterVehicle> siteGroup in vehiclesBySite)
            {
               logger.Info($"Processing site {siteGroup.Key} with {siteGroup.Count()} vehicles");

               // Navigate to inventory page
               _driver.Navigate().GoToUrl("https://vindis.salesmaster.co.uk/vehicles");

               Thread.Sleep(2000);

               // Process each vehicle in the group
               foreach (SalesmasterVehicle veh in siteGroup)
               {
                  bool foundItem = SearchInventory(wait, veh);

                  if (foundItem)
                  {
                     // Perform the appropriate action based on operation type
                     if (operation == VehicleProcessOperation.Update)
                     {
                        logger.Info($"Found item: {veh.Reg}");
                        AmendPrice(veh);
                     }
                     else // Verify operation
                     {
                        VerifyPrices(wait, veh);
                     }
                  }
               }
            }
         }
         catch (Exception e)
         {
            logger.Error($"Error in ProcessVehicles: {e.Message}");
            //TakeScreenshot(_driver, "ProcessVehiclesFailure");
         }
      }

      
      private bool SearchInventory(WebDriverWait wait, SalesmasterVehicle veh)
      {
         try
         {
            logger.Info($"Searching for vehicle: {veh.Reg}");

            // Find the search box
            IWebElement searchBox = wait.Until(ExpectedConditions.ElementExists(By.Id("token-input-search")));

            // Clear search box
            searchBox.Clear();
            Thread.Sleep(500);

            // Enter registration and search
            searchBox.SendKeys(veh.Reg);
            Thread.Sleep(500);
            searchBox.SendKeys(Keys.Enter);

            // Wait for search results
            Thread.Sleep(2000);

            // Check if vehicle was found
            try
            {
               // Look for the vehicle in the results
               var vehicleRows = _driver.FindElements(By.CssSelector("#vehicle-rows > tr"));

               if (vehicleRows.Count > 1)
               {
                  logger.Info($"More than one row found; aborting: {veh.Reg}");
                  return false;
               }
               else if (vehicleRows.Count == 1)
               {
                  WaitAndFind("//tbody[@id='vehicle-rows']/tr[1]", true); // Click and open modal
                  return true;
               }
               else
               {
                  logger.Info($"No rows found: {veh.Reg}");
                  return false;
               }

            }
            catch (Exception)
            {
               logger.Info($"Vehicle not found: {veh.Reg}");
               return false;
            }
         }
         catch (Exception e)
         {
            logger.Error($"Error searching for vehicle {veh.Reg}: {e.Message}");
            return false;
         }
      }

      private void AmendPrice(SalesmasterVehicle veh)
      {
         logger.Info($"Amending price for: {veh.Reg}");

         try
         {
            // Click the Edit Vehicle button on the modal
            WaitAndFind($"//*[@id='edit-btn']", true);

            Thread.Sleep(2000);

            // Find the basic price field
            IWebElement basicPriceField = WaitAndFind("//input[@id='vehicle_basic_price']", false);

            // Get current basic price
            string currentPriceStr = basicPriceField.GetAttribute("value");

            // Remove commas, convert to decimal, then cast to int
            int currentPrice = (int)decimal.Parse(currentPriceStr.Replace(",", ""), CultureInfo.InvariantCulture);

            if(currentPrice != veh.Price)
            {
               // Set new basic price - for web
               basicPriceField.Clear();
               basicPriceField.SendKeys(veh.Price.ToString());
               Thread.Sleep(500);
               basicPriceField.SendKeys(Keys.Tab);

               // Save changes
               WaitAndFind("//button[@id='save-btn']", true);

               // Wait for modal to reopen
               Thread.Sleep(5000);

               logger.Info($"Basic price updated for {veh.Reg} from {currentPrice} to {veh.Price}");

               veh.PriceChanged = true;
            }
            else
            {
               logger.Info($"Basic price already matches for {veh.Reg} ({currentPrice}). No change required.");

               // Save changes
               WaitAndFind("//button[@id='save-btn']", true);

               // Wait for modal to reopen
               Thread.Sleep(5000);
            }

            // Go back to edit again
            WaitAndFind($"//*[@id='edit-btn']", true);

            Thread.Sleep(5000);

            // Go to third party listing tag
            WaitAndFind("//*[@id='third-party-integrations-link']/a", true);

            // Find the basic price field
            IWebElement autotraderPriceField = WaitAndFind("//input[@id='vehicle_autotrader_retail_price']", false);

            // Get current basic price
            string currentAutoTraderPriceStr = autotraderPriceField.GetAttribute("value");

            // Remove commas, convert to decimal, then cast to int
            int currentAutoTraderPrice = (int)decimal.Parse(currentAutoTraderPriceStr.Replace(",", ""), CultureInfo.InvariantCulture);

            if(currentAutoTraderPrice != veh.Price)
            {
               // Set new autotrader price
               autotraderPriceField.Clear();
               autotraderPriceField.SendKeys(veh.Price.ToString());
               Thread.Sleep(500);
               autotraderPriceField.SendKeys(Keys.Tab);

               // Save changes
               WaitAndFind("//button[@id='save-btn']", true);

               // Wait for modal to reopen
               Thread.Sleep(5000);

               logger.Info($"Autotrader price updated for {veh.Reg} from {currentPrice} to {veh.Price}");

               Thread.Sleep(2000);
            }
            else
            {
               logger.Info($"AT price already matches for {veh.Reg} ({currentPrice}). No change required.");

               // Save changes
               WaitAndFind("//button[@id='save-btn']", true);

               // Wait for modal to reopen
               Thread.Sleep(5000);
            }

            WaitAndFind("//a[contains(@class, 'fancybox-close') and @title='Close']", true);

         }
         catch (Exception e)
         {
            logger.Error($"Error amending price for {veh.Reg}: {e.Message}");

            // Go back to move to next item
            _driver.Navigate().GoToUrl("https://vindis.salesmaster.co.uk/vehicles");
         }
      }

      private void VerifyPrices(WebDriverWait wait, SalesmasterVehicle veh)
      {
         logger.Info($"Verifying prices for: {veh.Reg}");

         try
         {
            // Click the edit button for this vehicle
            WaitAndFind($"//*[@id='edit-btn']", true);

            Thread.Sleep(2000);

            // BASIC PRICE CHECK
            IWebElement basicPriceField = WaitAndFind("//input[@id='vehicle_basic_price']", false);
            string currentBasicPriceStr = basicPriceField.GetAttribute("value");
            int currentBasicPrice = (int)decimal.Parse(currentBasicPriceStr.Replace(",", ""), CultureInfo.InvariantCulture);

            if (currentBasicPrice == veh.Price)
            {
               logger.Info($"Basic price verification successful for {veh.Reg}: {currentBasicPrice}");
            }
            else
            {
               logger.Error($"Basic price verification failed for {veh.Reg}. Expected: {veh.Price}, Actual: {currentBasicPrice}");
            }

            // GO TO THIRD-PARTY LISTING TAB
            WaitAndFind("//*[@id='third-party-integrations-link']/a", true);

            // AUTOTRADER PRICE CHECK
            IWebElement autotraderPriceField = WaitAndFind("//input[@id='vehicle_autotrader_retail_price']", false);
            string currentAutoTraderPriceStr = autotraderPriceField.GetAttribute("value");
            int currentAutoTraderPrice = (int)decimal.Parse(currentAutoTraderPriceStr.Replace(",", ""), CultureInfo.InvariantCulture);

            if (currentAutoTraderPrice == veh.Price)
            {
               logger.Info($"Autotrader price verification successful for {veh.Reg}: {currentAutoTraderPrice}");
            }
            else
            {
               logger.Error($"Autotrader price verification failed for {veh.Reg}. Expected: {veh.Price}, Actual: {currentAutoTraderPrice}");
            }

            Thread.Sleep(2000);

            // CLOSE MODAL
            WaitAndFind("//a[contains(@class, 'fancybox-close') and @title='Close']", true);
         }
         catch (Exception e)
         {
            logger.Error($"Error verifying prices for {veh.Reg}: {e.Message}");
            _driver.Navigate().GoToUrl("https://vindis.salesmaster.co.uk/vehicles");
         }
      }

      public IWebElement WaitAndFind(string findXPath, bool andClick = false)
      {
         IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "SalesmasterUpdate", findXPath, andClick);
         return result;
      }

      //private void TakeScreenshot(IWebDriver driver, string screenshotName)
      //{
      //    try
      //    {
      //        string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
      //        string screenshotPath = Path.Combine(ConfigService.FileDownloadLocation, $"{timestamp}_{screenshotName}.png");

      //        Screenshot screenshot = ((ITakesScreenshot)driver).GetScreenshot();
      //        screenshot.SaveAsFile(screenshotPath, ScreenshotImageFormat.Png);

      //        logger.Info($"Screenshot saved to {screenshotPath}");
      //    }
      //    catch (Exception e)
      //    {
      //        logger.Error($"Failed to take screenshot: {e.Message}");
      //    }
      //}
   }
}
