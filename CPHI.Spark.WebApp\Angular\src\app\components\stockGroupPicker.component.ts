import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { ConstantsService } from '../services/constants.service';



@Component({
  selector: 'stockGroupPicker',
  template:    `
    <!-- Site selector -->
    <div ngbDropdown dropright class="d-inline-block" id="siteDropdown">
        <button [ngClass]="buttonClass" class=" btn btn-primary" (click)="generateGroupsList()"
          ngbDropdownToggle>{{groupChosenLabel()}}</button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <!-- ngFor buttons -->  
        <ng-container    *ngFor="let group of groups">
        <button  (click)="toggleItem(group)" [ngClass]="{'active':group.isSelected}"
        ngbDropdownItem>{{group.Description}}</button>
        </ng-container>
        <!-- select Total -->  
        <button class="quickSelect" (click)="quickSelectTotal()" ngbDropdownItem>Total</button>
        <!-- quick select -->
          <div class="spaceBetween">
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="selectGroups()">OK</button>
            <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
          </div>

        </div>
      </div>
    
    `
  ,
  styles: [`
  @media (min-width: 0px) and (max-width: 1920px) and (hover:none) {
    #siteDropdown .dropdown-menu{columns:2}

  }  

    `]
})


export class StockGroupPickerComponent implements OnInit {
  @Input() groupsFromParent: string[];
  @Input() buttonClass: string;
  @Input() onlyOneSite: boolean;
  @Output() updateGroups = new EventEmitter<string[]>();

  public groups: {Description: string, isSelected:boolean}[];
  public groupSelectedDescriptions: string[];
  
  constructor(
    public constants: ConstantsService,
    
  ) { }


  ngOnInit(): void { 
    
   }




  groupChosenLabel() {
    if(!this.groupsFromParent)return 'Groups'
    if (this.groupsFromParent.length == 0) {
      return 'No groups selected'
    } else if (this.groupsFromParent.length == 1) {
      return this.groupsFromParent[0]
    } else if (this.groupsFromParent.length < 4) {
      let groups = ''
      this.groupsFromParent.forEach((group, i) => {
        if (i > 0) { groups = groups + ',' } //leading comma for 2nd item onwards
        groups += group;
      })
      return groups
    } else if (this.groupsFromParent.length == this.constants.allGroups.length-1){
      return 'All Groups'
    }else{
      return 'Groups'
    }
  }

  generateGroupsList() {
    this.groupSelectedDescriptions = this.groupsFromParent

    //recreate local list
    this.groups = []
    
    this.constants.allGroups.forEach(x=>{
      this.groups.push({Description:x,isSelected:false})
    })
    
    //tag if it's selected
    this.groups.forEach(s => {
      if (this.groupSelectedDescriptions.includes(s.Description)) {
        s.isSelected = true;
      }
    })
  }

  
  toggleItem(item: any) {
    if (!this.onlyOneSite){item.isSelected = !item.isSelected}
    else {
      this.groups.forEach(s => {
        s.isSelected = false
      })
      item.isSelected = true;
    }
    
  }

  selectGroups() {
    this.updateGroups.emit(this.groups.filter(e => e.isSelected).map(x=>x.Description));
  }

 

  quickSelectTotal() {
    //if all selected (except site 0), select none else select all
    const isTrue = (currentValue) => currentValue.isSelected;

    if (this.groups.every(isTrue)){
      this.groups.forEach(s=>s.isSelected = false)
    }else{
      this.groups.forEach(s => {
          s.isSelected = true;
      })
    }
  }

  


}


