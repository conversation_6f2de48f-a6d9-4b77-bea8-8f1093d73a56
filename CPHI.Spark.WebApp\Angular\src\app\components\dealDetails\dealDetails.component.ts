//core angular
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
//Angular things, non-standard
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { StockListRow } from "src/app/pages/stockList/StockListRow";
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
//model
//services
import { ConstantsService } from '../../services/constants.service';
import { GetDealDataService } from '../../services/getDeals.service';
import { SelectionsService } from '../../services/selections.service';
import { StockItemModalService } from '../stockItemModal/stockItemModal.service';
import { DealModalWithStockItem } from 'src/app/model/DealModalWithStockItem';
import { AutoPriceInsightsModalService } from '../autoPriceInsightsModal/autoPriceInsightsModal.service';
import { AutoPriceInsightsModalComponent } from '../autoPriceInsightsModal/autoPriceInsightsModal.component';
import { SalesmanWithIdParams } from 'src/app/model/sales.model';
import { ExecWithId } from './dealDetails.model';


@Component({
  selector: 'app-dealDetails',
  templateUrl: './dealDetails.component.html',
  styleUrls: ['./dealDetails.component.scss']
})


export class DealDetailsComponent implements OnInit {

  @ViewChild('dealModal', { static: true }) dealModal: ElementRef;
  @Input() public givenDealId: number;

  //canSetQualifyingPartEx: boolean;
  deal: DealModalWithStockItem;
  //comments: DealComment[];
  showStockDetails: boolean = false;
  selectedTab:'dealDetails'|'websiteDetails'|'autotraderDetails'
  stockItem: StockListRow;
  salesmen: ExecWithId[];

  newSalesExec: string = null;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public dataDeals: GetDealDataService,
    public getDealData: GetDealDataService,
    public getData: GetDataMethodsService,
    public stockItemModalService: StockItemModalService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService
  ) {



  }

  get commentsOperationsMessage(){
    if(this.constants.environment.dealDetailModal_dealDetailsSection_showQualifyingPartEx){
      return 'Comments and Operations';
    }
    return 'Comments';
  }

  ngOnDestroy() {
    this.stockItem = null;
  }



  ngOnInit() {
    this.initParams()
  }

  initParams() {
    this.selectedTab = 'dealDetails'
    this.showStockDetails = false;

    this.selections.triggerSpinner.next({show:true,message:this.constants.translatedText.Loading});

    this.getDealData.getDealDetailModal(this.givenDealId).subscribe(result => {
      this.deal = result;
      this.stockItemModalService.initialise(this.deal.StockListRow,null)
     // console.log(this.deal, "this.deal!")
      //this.comments = result.comments;

    }, (e) => {
      //didn't get a deal, perhaps it's been deleted since last refresh or something
      console.error(e, "Error getting Deal Details");
      this.deal = null

    }, () => {

      if(this.selections.user.RoleName == 'System Administrator' && this.constants.environment.dealDetailModal_enableSalesExecPicker)
      {

        let parms = this.getParams();

        this.getDealData.getVindisExecsForSiteForMonth(parms).subscribe(result => {
          
          this.salesmen = result;

        }, e => {

          // Errors
          throw(e);
          
        }, () => {
    
    
        })

      }
    
      //carry on with the rest
      //launch modal
      this.selections.triggerSpinner.next({show:false});

      this.modalService.open(this.dealModal, { 
        windowClass: 'dealDetailsModal', 
        size: this.constants.environment.dealDetailModal_showSmallModal ? null : 'lg', 
        keyboard: false, 
        ariaLabelledBy: 'modal-basic-title' 
      }).result.then((result) => {

        //'okd'
        this.passBack()
      },
        //closed
        (reason) => {
          //cancelled, so no passback      
          this.activeModal.close()
        });

    })

  }

  getParams(): SalesmanWithIdParams
  {
    return {
      Year: new Date(this.deal.DealDetailModal.OrderDate).getFullYear(), // Extract the year
      Month: new Date(this.deal.DealDetailModal.OrderDate).getMonth() + 1, // Extract the month (0-based index, so add 1)
      SiteId: this.deal.DealDetailModal.Site_Id // Assuming this is correctly defined
    };
  }

  onCheckboxChange(isChecked: boolean) {


    this.getDealData.setQualifyingPartEx(this.deal.DealDetailModal.DealId, this.deal.DealDetailModal.StockNumber, isChecked).subscribe((commentsChanged) => {

      this.constants.toastSuccess("Changes saved");
      this.selections.triggerSpinner.emit()

    })

    this.selections.triggerSpinner.next({ show: false });
  }

  passBack() {
    let thingToPassBack = 'foo';
    this.activeModal.close(thingToPassBack); //<--put things in here
  }

  showWarrantyInAddons(){
    return this.constants.environment.dealDetailModal_addonsSection_showWarrantyForNewCar || this.deal.DealDetailModal.VehicleSuperType=='New'
  }

  showDealDetails(){
    this.showStockDetails = false;
    this.selectedTab = 'dealDetails'
  }

  showWebsiteDetails() {
    this.showStockDetails = true;
    this.selectedTab = 'websiteDetails'
  }

  showAutotraderDetails(){
    if (!this.deal.StockListRow.VehicleAdvertId ) {return;}
    //this.selectedTab = 'autotraderDetails'

    this.autoPriceInsightsModalService.initialise(this.deal.StockListRow.VehicleAdvertId,[])
    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });
  }

  // Function to handle selection
  onSalesmanSelect(salesmanId: number): string {

    if(salesmanId != undefined)
    {
      const selectedSalesman = this.salesmen.find(s => s.SalesmanId === salesmanId);

      this.getDealData.updateSalesmanId(this.deal.DealDetailModal.DealId, selectedSalesman.SalesmanId).subscribe((res) => {

        this.constants.toastSuccess("Salesman updated");
        this.newSalesExec = selectedSalesman.Label;
        this.deal.DealDetailModal.Salesman_Id = salesmanId;
        this.deal.DealDetailModal.Salesman = selectedSalesman.Label;
        this.selections.triggerSpinner.emit()
  
      })

      
    }

    return null;
  }



 
 
}
