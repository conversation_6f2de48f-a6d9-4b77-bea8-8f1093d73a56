<menu id="sidenav" [ngClass]="{'wide': menuIsWide}" (mouseenter)="makeMenuWide($event, true)"
    (mouseleave)="makeMenuWide($event, false)">
    <div id="personDetails" [class]="{ 'show': menuIsWide }">
        <div class="profilePicContainer">
            <profilePic class="profilePic pointer"></profilePic>
        </div>
        <h2>{{ selections.user.Name }}</h2>
        <h5>
            <span
                *ngIf="constants.environment.customer == 'RRGSpain' && selections.user.JobTitle == 'System Administrator'">
                Administrador de Usuarios
            </span>
            <span
                *ngIf="constants.environment.customer != 'RRGSpain' || selections.user.JobTitle != 'System Administrator'">
                {{ selections.user.JobTitle }}
            </span>,
            <span *ngIf="!selections.user.AccessAllSites">{{ selections.user.site.SiteDescription }}</span>
            <span *ngIf="selections.user.AccessAllSites">{{ constants.environment.fullSideMenu.description }}</span>
        </h5>
    </div>

    <ng-container *ngFor="let menuItem of constants.menuNew">
        <button *ngIf="menuItem.visible" class="btn btn-primary menuItem"
            [ngClass]="{ 'supressCaret': !showMenuLabels || !menuItem.subItems, 'active': menuItem.isActive, 'hovered': hoveredMenuItem }"
            [ngbPopover]="popContent" placement="right-top" popoverClass="menu" (mouseenter)="openPopover(p, menuItem)"
            triggers="manual" #p="ngbPopover" (document:click)="closePopover(p,$event)"
            (click)="!menuItem.subItems ? goTo(menuItem) : null">
            <div class="iconAndMenuItemName">
                <i class="menuIcon" [ngClass]="menuItem.icon"></i>
                <span *ngIf="showMenuLabels">{{ menuItem.name }}</span>
            </div>
        </button>
    </ng-container>

    <div *ngIf="menuIsWide && showMenuLabels" id="sidenav-footer" class="animated">
        <div>
            <img src="./assets/imgs/cphi-app-logo.png">
            <img src="./assets/imgs/sparkLogoWhitePng.png">
        </div>
        <div id="version">v{{ constants.environment.version }}</div>
    </div>
</menu>

<ng-template #popContent let-item="item">
    <div id="subItemMenu" class="d-flex flex-column subMenu" [ngClass]="{ 'visible': activePopover }">
        <ng-container *ngFor="let subItem of hoveredMenuItem.subItems">
            <button *ngIf="subItem.visible" class="btn btn-primary menuItem supressCaret"
                [ngClass]="{ 'active': subItem.isActive }" (click)="goTo(subItem, hoveredMenuItem.group)">
                <div class="iconAndMenuItemName">
                    <i class="menuIcon" [ngClass]="subItem.icon"></i>
                    {{subItem.name}}
                </div>
            </button>
        </ng-container>
    </div>
</ng-template>