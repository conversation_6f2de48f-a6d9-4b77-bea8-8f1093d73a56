
--UnifiedDB - updated.  TODO: Check why SiteId is hardcoded and if it will have an impact.
CREATE OR ALTER PROCEDURE [dbo].[GET_PerformanceLeagueVindis]
(
    @Departments nvarchar(100),
	@YearMonths nvarchar(100),
    @IncludeFleetSites INT,
	@ExcludeGroupFleetSite INT = NULL,
	@IsSalesExecView bit,
    @UserId int
)
AS
BEGIN

SET NOCOUNT ON;

    DECLARE @DealerGroupId INT = (SELECT DealerGroup_Id FROM People WHERE Id = @UserId)

    SELECT LEFT(VALUE,4) as Year, SUBSTRING(Value,5,2) as Month INTO #yearMonths FROM STRING_SPLIT(@YearMonths,',');

    -- Create the main table
    select Id INTO #salesmanIgnoreList from people where HasLeft = 1 OR CurrentSite_Id in (select id from sites where description like '%Fleet%')

    ;WITH Main AS (

        SELECT
            CAST(YEAR(AccountingDate) as varchar) + CAST(MONTH(AccountingDate)  as varchar) as YearMonth, --just turning accounting date into YearMonth
            CASE WHEN @IsSalesExecView = 1 THEN Salesman_Id ELSE ISNULL(manager.Id,0) END as SalesmanId,
            SUM( IIF(OrderTypes.Type <> 'Trade', Units, 0 )) as DealCount,
            SUM( IIF(DealLatests.IsDelivered = 1, DealLatests.Units, 0)) as DeliveredCount,
            SUM( IIF(DealLatests.IsFinanced = 1, DealLatests.Units, 0)) as FinancedCount,
            SUM( IIF(DealLatests.HasCosmeticInsurance = 1, DealLatests.Units, 0)) as CosmeticCount,
            SUM( IIF(DealLatests.HasPaintProtection = 1, DealLatests.Units, 0)) as PaintProtectionCount,
            SUM( IIF(DealLatests.HasGapInsurance = 1, DealLatests.Units, 0)) as GapCount,
            SUM( IIF(DealLatests.HasWarranty = 1, DealLatests.Units, 0)) as WarrantyCount,
			SUM(CAST(TotalProductCount AS FLOAT)) 
			  - SUM(CASE WHEN HasWarranty = 1 THEN 1 ELSE 0 END)
			  - SUM(CASE WHEN HasServicePlan = 1 THEN 1 ELSE 0 END) AS ProductCount,
            SUM(TotalNLProfit) as Margin
            FROM DealLatests
            INNER join VehicleTypes on VehicleTypes.Id = DealLatests.VehicleType_Id
            INNER join OrderTypes on OrderTypes.Id = DealLatests.OrderType_Id
            INNER JOIN Departments ON VehicleTypes.Id= Departments.VehicleTypeId AND OrderTypes.Id = Departments.OrderTypeId
            INNER JOIN People ON People.Id=DealLatests.Salesman_Id
            INNER JOIN Sites ON Sites.Id = DealLatests.Site_Id
            INNER JOIN #yearMonths ym ON YEAR(DealLatests.AccountingDate) = ym.YEAR AND MONTH(DealLatests.AccountingDate) = ym.Month
			LEFT JOIN ExecManagerMappings emp on emp.ExecId = DealLatests.Salesman_Id and MONTH(emp.month) = MONTH(DealLatests.AccountingDate) and YEAR(emp.month) = YEAR(DealLatests.AccountingDate)
			LEFT JOIN People manager ON manager.Id = emp.ManagerId
        WHERE AccountingDate >= '2019-07-01' and Salesman_Id not in (select * from #salesmanIgnoreList)
        AND (@Departments IS NULL OR Departments.DepartmentName IN (SELECT * FROM STRING_SPLIT(@Departments,',')) )
        AND OrderType_Id not in (select Id from OrderTypes where([Description] = 'Trade' or [Description] = 'Auction') )
        AND (Sites.[Description] NOT LIKE '%Fleet%' OR @IncludeFleetSites = 1)
	    AND (Sites.Id != 22 OR @ExcludeGroupFleetSite IS NULL OR @ExcludeGroupFleetSite = 0)
       AND Sites.DealerGroup_Id = @DealerGroupId
        GROUP BY
        CASE WHEN @IsSalesExecView = 1 THEN Salesman_Id ELSE ISNULL(manager.Id,0) END,
        cast(year(AccountingDate) as varchar) + cast(month(AccountingDate)  as varchar), 
        People.CurrentSite_Id
    )

	

    SELECT
    MAX(Main.YearMonth) as YearMonth,
    Main.SalesmanId,
    SUM(Main.DealCount) as DealCount,
    SUM(Main.DeliveredCount) as DeliveredCount,
    SUM(Main.FinancedCount) as FinancedCount,
    SUM(Main.CosmeticCount) as CosmeticCount,
    SUM(Main.PaintProtectionCount) as PaintProtectionCount,
    SUM(Main.GapCount) as GapCount,
    SUM(Main.WarrantyCount) as WarrantyCount,
    SUM(Main.ProductCount) as ProductCount,
    SUM(Main.Margin) as Margin,
    ISNULL(People.Id,0) as Id,
    ISNULL(People.Name,'UNASSIGNED') AS SalesmanName,
    People.CurrentSite_Id AS CurrentSiteId,
    People.HasLeft, 
    Sites.[Description] AS CurrentSite
    FROM Main
    LEFT JOIN People ON People.Id = SalesmanId
    LEFT JOIN Sites ON People.CurrentSite_Id = Sites.Id
    GROUP BY
    ISNULL(People.Id,0), Main.SalesmanId, ISNULL(People.Name,'UNASSIGNED'), People.CurrentSite_Id, People.HasLeft, Sites.[Description]
    ORDER BY
	DealCount DESC, Margin DESC

    DROP TABLE  #yearMonths
	DROP TABLE #salesmanIgnoreList

END

GO
