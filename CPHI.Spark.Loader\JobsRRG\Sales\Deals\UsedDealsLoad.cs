﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Text.RegularExpressions;
using System.Globalization;
using Microsoft.EntityFrameworkCore;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Loader.Comparers;
using System.Threading.Tasks;
using CPHI.Spark.BusinessLogic.Vindis.Comparers;

namespace CPHI.Spark.Loader
{
    class UsedDealsLoad
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(UsedDealsLoad));
        private string fileSearch = "*GB_SPK2.csv";
        /*
| Column                | Example                                                      | Sensitive           |
|-----------------------|--------------------------------------------------------------|---------------------|
| Suffix                | 10008032                                                     | No                  |
| Customer              | Mrs <PERSON>                                                     | YES - Customer name |
| Sales Exec            | <PERSON>han <PERSON>-<PERSON>                                       | No                  |
| Reg No                | EK71MWV                                                      | No                  |
| Type                  | HIQ                                                          | No                  |
| Fr                    | R                                                            | No                  |
| Enquiry               | RETAIL                                                       | No                  |
| Fin                   | 0                                                            | No                  |
| NL Total              | -1439.51                                                     | No                  |
| VM profit             | -1439.51                                                     | No                  |
| S Type                | retail                                                       | No                  |
| Bran                  | 22                                                           | No                  |
| Unit                  | 1                                                            | No                  |
| Sale                  | 14833.33                                                     | No                  |
| O/Allow               | 0                                                            | No                  |
| COS                   | -16127.51                                                    | No                  |
| Fin Sub               | 0                                                            | No                  |
| Mech Prep             | -385                                                         | No                  |
| Body Prep             | 0                                                            | No                  |
| Acc Sale              | 0                                                            | No                  |
| Acc COS               | 0                                                            | No                  |
| Fuel Sale             | 0                                                            | No                  |
| Fuel COS              | 0                                                            | No                  |
| Warr Sale             | 0                                                            | No                  |
| Warr COS              | 0                                                            | No                  |
| SP Sale               | 0                                                            | No                  |
| SP COS                | 0                                                            | No                  |
| Cosm Comm             | 0                                                            | No                  |
| Cosm Sale             | 269                                                          | No                  |
| Cosm COS              | -157.83                                                      | No                  |
| GAP Comm              | 0                                                            | No                  |
| GAP Sale              | 375                                                          | No                  |
| GAP COS               | -246.5                                                       | No                  |
| RCI Comm              | 0                                                            | No                  |
| Fin Comm              | 0                                                            | No                  |
| Select                | 0                                                            | No                  |
| Intro                 | 0                                                            | No                  |
| Error                 | 0                                                            | No                  |
| Inv Date              | 45021                                                        | No                  |
| LC                    |                                                              | No                  |
| Exec Code             | ag04625                                                      | No                  |
| Finance Company       |                                                              | No                  |
| Age                   | 36                                                           | No                  |
| Model Code            | ZOC                                                          | No                  |
| Vehicle Description   | RENAULT ZOE 100kW GT Line R135 50kWh Rapid Charge 5dr Auto   | No                  |
| Reg Date              | 44469                                                        | No                  |
| Delivery Date         | 45021                                                        | No                  |
| Tactical Support      | 0                                                            | No                  |
| Veh Type              | HIQ                                                          | No                  |
| Manufact              | RENAULT                                                      | No                  |
| Model                 | Zoe                                                          | No                  |
| Variant               | 97312                                                        | No                  |
| Manuf Text            | Renault                                                      | No                  |
| Variant Text          | Zoe 100kW GT Line R135 50kWh Rapid Charge 5dr Auto Hatchback | No                  |
| Age                   | 36                                                           | No                  |
| Adopted               | 44985                                                        | No                  |
| VarClass              | CAR                                                          | No                  |
| Confirmed Date        | 45018                                                        | No                  |
| Delivered             | 1                                                            | No                  |
| Model Year            | 2021                                                         | No                  |
| Handover Date         | 45020                                                        | No                  |
| Handover Time         | 0.757638889                                                  | No                  |
| Prev Usage            | PH                                                           | No                  |
| Enquiry               | 1243262                                                      | No                  |
| Paint Protection Cost | 0                                                            | No                  |
| Paint Protection Sale | 0                                                            | No                  |

*/
        public async Task LoadProfitReport()
        {

            string[] allMatchingFiles = Directory.GetFiles(ConfigService.incomingRoot, fileSearch);

            if (allMatchingFiles.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.UsedDeals;
                if (age.Minutes > 120)
                {
                    PulsesService.UsedDeals = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}] | No files found matching pattern *GB_SPK2.csv");
                }
                return;
            }

            string fileToProcess = allMatchingFiles[0];

            //try opening the file, if fail, return (common problem is loader trying to open file whilst scraper is saving it).
            try { FileStream fs = File.Open(fileToProcess, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None); fs.Close(); }
            catch (IOException) { return; }


            //create lock file to prevent other instances running
            //Locks.Deals = true;


            //define Lists
            List<EnquiryAndOrderDate> dbEnquiriesAndOrders;
            List<DealHistoricHeaderDataItem> dbDealsAtInvoiceStatus;
            List<Deal> dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater;
            List<Site> sites;
            List<StandingValue> standingValues;
            List<VehicleType> vehicleTypes;
            List<OrderType> orderTypes;
            List<Person> people;

            using (var db = new CPHIDbContext())

            {
                using (var dapper = new Dapperr())
                {
                    //new DynamicParameters(new { leaverIdsList = string.Join(",", leaverIds) }) 

                    int errorCount = 0;
                    LogMessage logMessage = new LogMessage();
                    logMessage.DealerGroup_Id = 1;

                    try
                    {
                        //LocksService.UsedDeals = true;
                        if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
                        {
                           // System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
                        }
                        DateTime start = DateTime.UtcNow;
                        //set Lists
                        int openingDbCount = dapper.Get<int>("SELECT COUNT(*) FROM Deals WHERE IsRemoved = 0", null, System.Data.CommandType.Text);

                        DateTime threshold = DateTime.UtcNow.AddMonths(-6);

                        // this line
                        int newVTypeId = db.VehicleTypes.First(x => x.Code == "New").Id;
                        dbEnquiriesAndOrders = dapper.GetAll<EnquiryAndOrderDate>("SELECT EnquiryNumber,OrderDate FROM Deals", null, System.Data.CommandType.Text).ToList();// db.Deals.Select(x => new EnquiryAndOrderDate { EnquiryNumber = x.EnquiryNumber, OrderDate = x.OrderDate }).ToList();
                        dbDealsAtInvoiceStatus = db.Deals.Include(x => x.Site).Where(d => d.VehicleType_Id != newVTypeId && !d.IsRemoved && d.IsInvoiced).Select(x => new DealHistoricHeaderDataItem()
                        {
                            StockNumber = x.StockNumber,
                            Customer = x.Customer,
                            SiteCode = x.Site.Code,
                            OrderDate = x.OrderDate,
                            Salesman_Id = x.Salesman_Id != null ? (int)x.Salesman_Id : 0,
                            InvoiceDate = x.InvoiceDate,
                            OrderType_Id = (int)x.OrderType_Id
                        }).ToList();

                        sites = db.Sites.ToList();
                        standingValues = db.StandingValues.ToList();
                        vehicleTypes = db.VehicleTypes.ToList();
                        orderTypes = db.OrderTypes.ToList();
                        people = db.People.ToList();


                        logMessage.SourceDate = DateTime.UtcNow;
                        logMessage.Job = GetType().Name;

                        Logger.Info($@"[{DateTime.UtcNow}] New file found at {ConfigService.incomingRoot}.  Starting {fileToProcess}");   //update logger 

                        //define variables for use in processing this file
                        int removedCount = 0;
                        int newCount = 0;
                        int changedCount = 0;
                        int incomingProcessCount = 0;
                        List<Diff> diffs = new List<Diff>(); //create empty list

                        if (File.Exists(fileToProcess.Replace(".csv", "-p.csv")))
                        {
                            //already processing a file of this type, skip
                            Logger.Error($@"Could not interpret {fileToProcess}, -p file already found ");
                            logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                            throw new Exception("Processing file already found");
                        }

                        string fileName = fileToProcess.Split('\\')[4];
                        DateTime fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                        DateTime fileDateMidday = new DateTime(fileDate.Year, fileDate.Month, fileDate.Day, 12, 00, 00);
                        logMessage.SourceDate = fileDate;
                        File.Move(fileToProcess, fileToProcess.Replace(".csv", "-p.csv")); //append _processing to the file to prevent any other instances also processing these files
                        var newFilepath = fileToProcess.Replace(".csv", "-p.csv");


                        List<Deal> incomingDeals = new List<Deal>(10000);  //preset the list size (slightly quicker than growing it each time)
                        var rows = GetDataFromFilesService.GetRowsCsv(newFilepath);
                        if (rows.Length < 2)
                        {
                            //file is too short to have anything meaningful in, just move then finish
                            //Locks.Deals = false;
                            File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                            Logger.Error($@"File had less than 2 rows, moving and finishing");
                            return;
                        }
                        var headers = rows.Skip(1).First().ToUpper().Split(',');


                        //firstly get all the stocknumbers
                        List<string> stockNumbers = new List<string>(rows.Count());
                        foreach (var row in rows.Skip(2))
                        {
                            if (string.IsNullOrEmpty(row)) { continue; }
                            var cells = Regex.Matches(row, "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))")
                                    .Cast<Match>()
                                    .Select(m => m.Value)
                                    .ToArray();

                            if (cells.Length != headers.Length)
                            {
                                //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                                continue;
                            }
                            string stockNumber = cells[Array.IndexOf(headers, "SUFFIX")];
                            if (stockNumber == "Totals") { continue; }

                            int slashIndex = stockNumber.IndexOf("/");
                            if (slashIndex == -1)
                            {
                                stockNumber = $"{stockNumber.Substring(0, 7)}/{stockNumber.Substring(7, 1)}";
                            }
                            stockNumbers.Add(stockNumber);
                        }

                        List<SpecCommissionLine> allSpecCommLines = HelpersService.GetSpecCommissionLines(stockNumbers, dapper);

                        foreach (var row in rows.Skip(2))
                        {
                            incomingProcessCount++;
                            try
                            {
                                if (string.IsNullOrEmpty(row)) { continue; } //skip empties

                                var cells = Regex.Matches(row, "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))")
                                    .Cast<Match>()
                                    .Select(m => m.Value)
                                    .ToArray();

                                if (cells.Length != headers.Length)
                                {
                                    //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                                    logMessage.FailNotes = logMessage.FailNotes + $"{cells[Array.IndexOf(headers, "SUFFIX")]}: Skipped rowCol as had {cells.Length} rowCols and needed {headers.Length}";
                                    errorCount++;
                                    continue;
                                }

                                string suffix = cells[Array.IndexOf(headers, "SUFFIX")];
                                if (suffix == "Totals") { continue; }


                                //check if ECOP car, if so, skip on
                                string enquiry = cells[Array.IndexOf(headers, "ENQUIRY")];
                                if (enquiry.Replace(" ", "").Length > 0)  // don't test if enquiry length is very short which likely indicates is a cancelled deal
                                {
                                    string enquiryRight4 = enquiry.Substring(enquiry.Length - 4, 4);
                                    if (enquiryRight4 == "ECOP" || enquiryRight4 == "LOAN") { continue; }  //unlikely for used but leave in
                                }

                                //things that we might need to get from matching Deal
                                string customer = HelpersService.LimitTo(cells[Array.IndexOf(headers, "CUSTOMER")], 50); ;
                                string givenExecCode = cells[Array.IndexOf(headers, "EXEC CODE")];

                                int? siteCode = null;
                                DateTime orderDate = DateTime.UtcNow;

                                //check if salesman is blank if so lookup salesman and customer from existing deals, must be there (!)
                                DealHistoricHeaderDataItem matchingDeal = null;
                                if (givenExecCode.Replace(" ", "").Length == 0)
                                {
                                    try { matchingDeal = dbDealsAtInvoiceStatus.First(a => a.StockNumber == suffix); } catch { continue; } //for now just move on if can't find matching.  Is because we don't have enough historic data to pull this in
                                    givenExecCode = people.First(p => p.Id == matchingDeal.Salesman_Id).DmsId;
                                    customer = matchingDeal.Customer;
                                    siteCode = matchingDeal.SiteCode;
                                    orderDate = matchingDeal.OrderDate;
                                }
                                else
                                {
                                    siteCode = int.Parse(cells[Array.IndexOf(headers, "BRAN")]);
                                    string orderDateString = cells[Array.IndexOf(headers, "CONFIRMED DATE")];

                                    // Ensure we don't parse a blank string here
                                    if(orderDateString != "--/--/----" && orderDateString != "")
                                    {
                                        orderDate = DateTime.ParseExact(cells[Array.IndexOf(headers, "CONFIRMED DATE")], "dd/MM/yyyy", null);
                                    }
                                    
                                }


                                //adjust the order date back to a previous order date if we have a new order with an enquiry number matching that of a previously removed order.
                                string enquiryNumber = cells[Array.LastIndexOf(headers, "PREV USAGE") + 1];
                                if (enquiryNumber != "0" && enquiryNumber != null && dbEnquiriesAndOrders.Select(x => x.EnquiryNumber.ToString()).Contains(enquiryNumber))
                                {
                                    orderDate = dbEnquiriesAndOrders.First(x => x.EnquiryNumber.ToString() == enquiryNumber).OrderDate;
                                }

                                Site site = null;
                                try { site = sites.First(s => s.Code == siteCode); } catch { continue; } //if can't find site, skip on
                                if (site.Description == "ECOP") { continue; } //skip on if ECOP
                                if (site.Description == "Nissan Bolton") { continue; } //skip on if Nissan Bolton, closed site, tiny numbers come through, don't want in Spark
                                if (site.Description == "Slough") { continue; } //skip on if Slough
                                if (site.Description == "Southern Fleet") { continue; }//skip on for these

                                var fran = standingValues.First(s => s.Code == "R"); //start with Renault.  In testing found 1 in a thousand cars is blank.
                                try { fran = standingValues.First(s => s.Code == cells[Array.IndexOf(headers, "FR")]); } catch { }
                                OrderType oType = null;
                                if (matchingDeal == null)
                                {
                                    try { oType = orderTypes.First(o => o.Code == cells[Array.IndexOf(headers, "ENQUIRY")]); }
                                    catch { oType = orderTypes.First(o => o.Code == "RETAIL"); }
                                }
                                else { oType = orderTypes.First(o => o.Id == matchingDeal.OrderType_Id); }


                                var salesman = people.FirstOrDefault(o => o.DmsId == givenExecCode);
                                if (salesman == null)
                                {
                                    throw new Exception($"Could not find person matching {givenExecCode}");
                                }


                                var vehClass = standingValues.First(o => o.Code == cells[Array.IndexOf(headers, "VARCLASS")]);
                                var vehType = vehicleTypes.First(v => v.Code == cells[Array.IndexOf(headers, "VEH TYPE")]);



                                //other things to work out
                                bool isLateCost = cells[Array.IndexOf(headers, "LC")] == "***";




                                //dates: nullable
                                DateTime? stockDate = null;
                                DateTime? registeredDate = null;
                                DateTime invoiceDate;
                                DateTime? invoiceDateGiven = null;
                                DateTime deliveryDate;
                                DateTime? deliveryDateProvided = null;
                                DateTime? handoverDate = null;

                                try { stockDate = DateTime.ParseExact(cells[Array.IndexOf(headers, "ADOPTED")], "dd/MM/yyyy", null); } catch { }
                                try { registeredDate = DateTime.ParseExact(cells[Array.IndexOf(headers, "REG DATE")], "dd/MM/yyyy", null); } catch { }
                                try { invoiceDateGiven = DateTime.ParseExact(cells[Array.IndexOf(headers, "INV DATE")], "dd/MM/yyyy", null); } catch { }
                                try { invoiceDate = DateTime.ParseExact(cells[Array.IndexOf(headers, "INV DATE")], "dd/MM/yyyy", null); } catch { }
                                try { deliveryDateProvided = DateTime.ParseExact(cells[Array.IndexOf(headers, "DELIVERY DATE")], "dd/MM/yyyy", null); } catch { }
                                try
                                {
                                    string handoverDateDate = cells[Array.IndexOf(headers, "HANDOVER DATE")];
                                    string handoverDateTime = cells[Array.IndexOf(headers, "HANDOVER TIME")];
                                    if (handoverDateTime == "") handoverDateDate = "09:00";
                                    string dateAndTime = handoverDateDate + handoverDateTime;
                                    handoverDate = DateTime.ParseExact(dateAndTime, "dd/MM/yyyyHH:mm", CultureInfo.InvariantCulture);
                                }
                                catch { }


                                //if no delivery date (in testing, found occasionally vehicle wouldn't have one) then use invoice date or if not available use confirmed order date + 7 for used + 60 for new
                                deliveryDate = deliveryDateProvided ?? invoiceDateGiven ?? orderDate.AddDays(7);

                                //invoice date
                                if (matchingDeal != null)
                                {
                                    invoiceDate = invoiceDateGiven ?? matchingDeal.InvoiceDate ?? deliveryDateProvided ?? fileDate;
                                }
                                else
                                {
                                    invoiceDate = invoiceDateGiven ?? deliveryDateProvided ?? fileDate;
                                }

                                string vehicleSource = "";
                                try { vehicleSource = cells[Array.IndexOf(headers, "PREV USAGE")]; } catch { }
                                int units = int.Parse(cells[Array.IndexOf(headers, "UNIT")]);
                                string stockNumber = cells[Array.IndexOf(headers, "SUFFIX")];

                                //updated 2 October. CDK had a problem with stocknumbers > 999999 as they couldn't fit characters into the report so 999999/1 is ok but 1000000/1 didn't work so they switched to 10000001 i.e. dropped the /
                                //have to put the slash back in
                                //example old: 105976/1
                                //example now: 10043651
                                int slashIndex = stockNumber.IndexOf("/");
                                if (slashIndex == -1)
                                {
                                    stockNumber = $"{stockNumber.Substring(0, 7)}/{stockNumber.Substring(7, 1)}";
                                }


                                SpecCommissionLine specCommLine = null;
                                try
                                {
                                    specCommLine = allSpecCommLines.Where(x => x.StockNumber == stockNumber && x.EnquiryNum == int.Parse(enquiryNumber)).Last();
                                }
                                catch
                                {
                                    { }
                                }

                                decimal salePaint = 0;
                                decimal saleGap = 0;
                                decimal saleCosmetic = 0;
                                decimal saleAccidentRepair = 0;
                                decimal saleTyre = 0;
                                decimal saleAlloy = 0;
                                decimal saleWarranty = 0;
                                decimal saleTyreAlloy = 0;
                                decimal saleWheelGuard = 0;
                                decimal saleServicePlanSpecLines = 0;

                                decimal costPaint = 0;
                                decimal costGap = 0;
                                decimal costCosmetic = 0;
                                decimal costAccidentRepair = 0;
                                decimal costTyre = 0;
                                decimal costAlloy = 0;
                                decimal costWarranty = 0;
                                decimal costTyreAlloy = 0;
                                decimal costWheelGuard = 0;
                                decimal costServicePlanSpecLines = 0;

                                if (specCommLine != null && !isLateCost && units != 0)
                                {
                                    salePaint = specCommLine.SalePaint * units;
                                    saleGap = specCommLine.SaleGap * units;
                                    saleCosmetic = specCommLine.SaleCosmetic * units;
                                    saleAccidentRepair = specCommLine.SaleAccidentRepair * units;
                                    saleTyre = specCommLine.SaleTyre * units;
                                    saleAlloy = specCommLine.SaleAlloy * units;
                                    saleWarranty = specCommLine.SaleWarranty * units;
                                    saleTyreAlloy = specCommLine.SaleTyreAlloy * units;
                                    saleWheelGuard = specCommLine.SaleWheelGuard * units;
                                    saleServicePlanSpecLines = specCommLine.SaleServicePlan * units;

                                    costPaint = specCommLine.CostPaint * -1 * units;
                                    costGap = specCommLine.CostGap * -1 * units;
                                    costCosmetic = specCommLine.CostCosmetic * -1 * units;
                                    costAccidentRepair = specCommLine.CostAccidentRepair * -1 * units;
                                    costTyre = specCommLine.CostTyre * -1 * units;
                                    costAlloy = specCommLine.CostAlloy * -1 * units;
                                    costWarranty = specCommLine.CostWarranty * -1 * units;
                                    costTyreAlloy = specCommLine.CostTyreAlloy * -1 * units;
                                    costWheelGuard = specCommLine.CostWheelGuard * -1 * units;
                                    costServicePlanSpecLines = specCommLine.CostServicePlan * -1 * units;
                                }

                                //columns from report
                                decimal gapSaleAsGiven = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "GAP SALE")]);
                                decimal cosmeticSaleAsGiven = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "COSM SALE")]);
                                decimal accsSaleAsGiven = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "ACC SALE")]);

                                decimal gapCostAsGiven = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "GAP COS")]);
                                decimal cosmeticCostAsGiven = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "COSM COS")]);
                                decimal accsCostAsGiven = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "ACC COS")]);

                                //notes
                                // gap comes through in gap cols and is correct
                                // cosmetic comes in cosmetic cols but includes tyrealloy and tyre
                                // accessories is in accs cols but includes paint and wheelguard

                                decimal saleServicePlanProfitReport = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "SP SALE")]); 
                                decimal costServicePlanProfitReport = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "SP COS")]);


                                // If there are differences between ProfitReport and SpecLines, add to an Error property
                                decimal serviceError = (saleServicePlanProfitReport - saleServicePlanSpecLines) + (costServicePlanProfitReport - costServicePlanSpecLines);



                                if (salesman == null)
                                {
                                    throw new Exception("No salesmanId");
                                }

                                decimal financeSubsidy = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "FIN SUB")]);
                                decimal rciFinanceCommission = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "RCI COMM")]);
                                decimal financeCommission = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "FIN COMM")]);
                                decimal selectCommission = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "SELECT")]);

                                bool isFinanced = (financeSubsidy + rciFinanceCommission + financeCommission + selectCommission) != 0;

                                Deal d = new Deal(); //initialise new one
                                d.CreatedDate = fileDate;
                                d.WhenNew = fileDate;
                                d.StockNumber = stockNumber;
                                d.Customer = customer;
                                d.EnquiryNumber = enquiryNumber;
                                d.Reg = cells[Array.IndexOf(headers, "REG NO")];
                                d.Franchise_Id = fran.Id;
                                d.OrderType_Id = oType.Id;
                                d.IsFinanced = isFinanced;
                                d.TotalNLProfit = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "NL TOTAL")]);
                                //d.TotalVehicleProfit = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "VM PROFIT")]);
                                d.Site_Id = site.Id;
                                d.Sale = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "SALE")]);
                                d.PartExOverAllowance1 = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "O/ALLOW")]);
                                d.CoS = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "COS")]);
                                d.FinanceSubsidy = financeSubsidy;
                                d.MechPrep = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "MECH PREP")]);
                                d.BodyPrep = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "BODY PREP")]);
                                d.AccessoriesSale = accsSaleAsGiven - salePaint - saleWheelGuard;
                                d.AccessoriesCost = accsCostAsGiven - costPaint - costWheelGuard;
                                d.FuelSale = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "FUEL SALE")]);
                                d.FuelCost = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "FUEL COS")]);
                                d.WarrantySale = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "WARR SALE")]);
                                d.WarrantyCost = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "WARR COS")]);
                                d.CosmeticInsuranceCommission = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "COSM COMM")]);
                                d.CosmeticInsuranceSale = cosmeticSaleAsGiven - saleTyreAlloy - saleTyre;
                                d.CosmeticInsuranceCost = cosmeticCostAsGiven - costTyreAlloy - costTyre;
                                d.PaintProtectionSale = salePaint;
                                d.PaintProtectionCost = costPaint;
                                d.GapInsuranceCommission = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "GAP COMM")]);
                                d.GapInsuranceSale = gapSaleAsGiven;
                                d.GapInsuranceCost = gapCostAsGiven;
                                d.TyreInsuranceSale = saleTyre;
                                d.TyreInsuranceCost = costTyre;
                                d.TyreAndAlloyInsuranceSale = saleTyreAlloy;
                                d.TyreAndAlloyInsuranceCost = costTyreAlloy;
                                d.WheelGuardSale = saleWheelGuard;
                                d.WheelGuardCost = costWheelGuard;
                                //d.ServicePlanSale = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "SP SALE")]);
                                //d.ServicePlanCost = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "SP COS")]);
                                d.RCIFinanceCommission = rciFinanceCommission;
                                d.FinanceCommission = financeCommission;
                                d.SelectCommission = selectCommission;
                                d.IntroCommission = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "INTRO")]);
                                d.Error = HelpersService.DecimalParseAndValidate(cells[Array.IndexOf(headers, "ERROR")]) + serviceError;
                                d.InvoiceDate = invoiceDate;
                                d.IsLateCost = isLateCost;
                                d.Salesman_Id = salesman.Id;
                                d.FinanceCo = cells[Array.IndexOf(headers, "FINANCE COMPANY")];
                                d.RegisteredDate = registeredDate;
                                d.Model = cells[Array.IndexOf(headers, "MODEL")];
                                d.Description = HelpersService.LimitTo(cells[Array.IndexOf(headers, "VEHICLE DESCRIPTION")], 50);
                                d.ActualDeliveryDate = deliveryDate;
                                d.VehicleAge = int.Parse(cells[Array.IndexOf(headers, "AGE")]);
                                d.StockDate = stockDate;
                                d.VehicleClass_Id = vehClass.Id;
                                d.OrderDate = orderDate;
                                d.IsDelivered = int.Parse(cells[Array.IndexOf(headers, "DELIVERED")]) == 1;
                                d.VariantTxt = HelpersService.LimitTo(cells[Array.IndexOf(headers, "VARIANT TEXT")], 50);
                                d.VehicleType_Id = vehType.Id;
                                d.Units = units;
                                d.HandoverDate = handoverDate;
                                d.VehicleSource = vehicleSource;
                                d.ServicePlanCost = costServicePlanSpecLines;
                                d.ServicePlanSale = saleServicePlanSpecLines;

                                //profit subtotals
                                //d.FAndIProfit = d.FinanceSubsidy
                                //    + d.WarrantySale + d.WarrantyCost
                                //    + d.CosmeticInsuranceSale + d.CosmeticInsuranceCost + d.CosmeticInsuranceCommission
                                //    + d.PaintProtectionSale + d.PaintProtectionCost
                                //    + d.GapInsuranceSale + d.GapInsuranceCost + d.GapInsuranceCommission
                                //    + d.ServicePlanSale + d.ServicePlanCost
                                //    + d.RCIFinanceCommission + d.FinanceCommission + d.SelectCommission + d.ProPlusCommission + d.StandardsCommission
                                //    //+ d.TyreAlloySale + d.TyreAlloyCost
                                //    //+ d.TyreSale + d.TyreCost
                                //    + d.WheelGuardSale + d.WheelGuardCost;
                                //;

                                //products
                                d.IsInvoiced = true;
                                d.HasServicePlan = d.ServicePlanSale > 0;
                                d.HasCosmeticInsurance = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasCosmetic : false;// d.CosmeticInsuranceSale > 0 || d.CosmeticInsuranceCommission > 0;
                                d.HasGapInsurance = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasGap : false;// d.GapInsuranceSale > 0 || d.GapInsuranceCommission > 0;
                                d.HasPaintProtection = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasPaint : false;
                                d.HasWarranty = specCommLine != null && !isLateCost && units != 0 ? (specCommLine.HasWarranty || specCommLine.HasWarranty2Yr || specCommLine.HasWarrantyLifetime) : false;
                                d.HasShortWarranty = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasShortWarranty : false;
                                d.HasTyreInsurance = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasTyre : false;
                                d.HasTyreAndAlloyInsurance = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasTyreAndAlloy : false;
                                d.HasWheelGuard = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasWheelGuard : false;
                                // d.TotalProductCount = HelpersService.CountTrue(d.HasServicePlan, d.HasPaintProtection, d.HasCosmeticInsurance, d.HasGapInsurance, d.HasWarranty, d.HasTyreAlloy, d.HasTyre, d.HasWheelGuard.Value);

                                d.LastUpdated = DateTime.UtcNow;




                                incomingDeals.Add(d);

                            }

                            catch (Exception err)
                            {

                                if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                                errorCount++;
                                continue;
                            }

                        }


                        //now add accouting year and Month
                        //find out what month the incoming profit report relates to
                        //define initially
                        DateTime incomingDealsDate = incomingDeals.Where(x => x.InvoiceDate != null && !x.IsLateCost).Select(x => x.InvoiceDate).OrderBy(x => x).Take(1).ToList()[0] ?? fileDate; //shouldn't need the alternative as have already qualified that the date isn't null
                        DateTime incomingLinesOpeningDate = new DateTime(incomingDealsDate.Year, incomingDealsDate.Month, 1, 0, 0, 0);
                        int daysInMonth = DateTime.DaysInMonth(incomingDealsDate.Year, incomingDealsDate.Month);
                        DateTime incomingLinesClosingDate = new DateTime(incomingDealsDate.Year, incomingDealsDate.Month, daysInMonth, 22, 59, 59);
                        DateTime earlierOfReportDateAndIncomingLinesClosingDate = fileDateMidday > incomingLinesClosingDate ? incomingLinesClosingDate : fileDateMidday;
                        DateTime middayOnFirstDayOfMonth = new DateTime(incomingLinesOpeningDate.Year, incomingLinesOpeningDate.Month, incomingLinesOpeningDate.Day, 12, 00, 00);




                        dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater = db.Deals.Where(d => d.VehicleType_Id != newVTypeId && !d.IsRemoved && d.IsInvoiced && d.AccountingDate >= incomingLinesOpeningDate).ToList();

                        DateTime finishedInterpetFile = DateTime.UtcNow;

                        foreach (Deal deal in incomingDeals)
                        {
                            try
                            {
                                if (deal.InvoiceDate < incomingLinesOpeningDate)
                                {
                                    //prevent accounting date being earlier than this accounting period
                                    deal.AccountingDate = middayOnFirstDayOfMonth;
                                }
                                //else if (deal.InvoiceDate > incomingLinesClosingDate)
                                //{
                                //    //prevent accounting date being later than this accounting period
                                //    deal.AccountingDate = incomingLinesClosingDate;
                                //}
                                else
                                {
                                    //otherwise use invoice date.  Which won't be null.
                                    deal.AccountingDate = (DateTime)deal.InvoiceDate;
                                }

                            }

                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + $" failed on adding accountingYearAndMonth {deal.StockNumber} {err.ToString()}";
                                errorCount++;
                                continue;
                            }
                        }





                        //############ NEWLY LOADED CARS

                        //add the new ones that are not late costs (pretty unlikely, would have been an order before being invoiced probably)
                        var newNonLateCostItems = incomingDeals.Where(d => !d.IsLateCost).Except(dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.Where(d => !d.IsLateCost), new DealStockNoComp());
                        newCount = newNonLateCostItems.Count();

                        try
                        {
                            foreach (var item in newNonLateCostItems)
                            {
                                item.OriginalSource = "UsedDeals" + fileDate.ToString("yyyyMMdd_HHmmss");
                            }
                            db.Deals.AddRange(newNonLateCostItems);  //add them all in one go
                        }
                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $"Failed addingNewDeals range {err.ToString()}";
                            errorCount++;
                        }

                        //add the new late costs to the database
                        var newLateCostItems = incomingDeals.Where(d => d.IsLateCost).Except(dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.Where(d => d.IsLateCost), new DealStockNoComp());
                        newCount = newCount + newLateCostItems.Count();

                        try
                        {
                            foreach (var item in newLateCostItems)
                            {
                                item.OriginalSource = "UsedDeals" + fileDate.ToString("yyyyMMdd_HHmmss");
                            }
                            db.Deals.AddRange(newLateCostItems);  //add them all in one go
                        }
                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $"Failed adding range {err.ToString()}";
                            errorCount++;
                        }



                        //############  REMOVED CARS

                        //standard method
                        void removeDeals(List<Deal> deals)
                        {
                            foreach (var Deal in deals)
                            {
                                if (Deal.StockNumber == "949125/1")
                                {
                                    { }
                                }

                                try
                                {
                                    Deal.IsRemoved = true;
                                    Deal.RemovedDate = DateTime.UtcNow;
                                    Deal.IsUpdated = true;
                                    Deal.LastUpdated = DateTime.UtcNow;
                                    removedCount++;

                                    //generate a diff also
                                    Diff newDiff = new Diff()
                                    {
                                        Model = "Deal",
                                        ModelIdent = Deal.StockNumber,
                                        Key = "IsRemoved",
                                        OldValue = "False",
                                        NewValue = "True",
                                        UpdateDate = fileDate,
                                    };

                                    diffs.Add(newDiff);
                                }

                                catch (Exception err)
                                {
                                    logMessage.FailNotes = logMessage.FailNotes + $" failed on removing item {Deal.StockNumber}" + err.ToString();
                                    errorCount++;
                                }

                            }
                        }

                        //START HERE------>


                        //find any invoiced cars that have now been removed (must be uninvoiced).  Only relevant for invoiced cars from db.  
                        //Firstly for nonLate costs
                        List<Deal> removedNonLates = dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.Where(r => !r.IsLateCost).Except(incomingDeals.Where(d => !d.IsLateCost), new DealStockNoComp()).ToList();
                        removeDeals(removedNonLates);

                        //Secondly for late costs.  For lates need to also ensure we are only looking at lates that were loaded to the same accounting period as the current report
                        List<Deal> removedLates = dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.Where(r => r.IsLateCost).Except(incomingDeals.Where(d => d.IsLateCost), new DealStockNoComp()).ToList();
                        removeDeals(removedLates);


                        if (removedLates.Count() > 200 || removedNonLates.Count() > 200) throw new Exception("Too many removed!");


                        //#############  CHANGED
                        List<Deal> changed = new List<Deal>(10000);

                        void diffDeals(List<Deal> dealsToDiff)
                        {
                            var DealDeepComparer = new DealDeepComp(); //instantiate
                            foreach (var incomingDeal in dealsToDiff)
                            {

                                try
                                {
                                    var oldDeal = dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.First(s => s.StockNumber == incomingDeal.StockNumber);
                                    var diffsThisItem = DealDeepComp.GetDiffs(oldDeal, incomingDeal, "Used", incomingDeal.StockNumber);
                                    if (diffsThisItem.Count > 0)
                                    {

                                        //they are not the same so..
                                        diffs.AddRange(diffsThisItem);

                                        //update the oldReg to pickup the new value for every property
                                        DealDeepComp.updateExistingDeal(incomingDeal, oldDeal);
                                        oldDeal.LastUpdated = DateTime.UtcNow; //update the lastUpdated date
                                        changed.Add(oldDeal);
                                        changedCount++;
                                    }

                                }

                                catch (Exception err)
                                {
                                    logMessage.FailNotes = logMessage.FailNotes + $" failed on making change to item {incomingDeal.StockNumber}" + err.ToString();
                                    errorCount++;
                                }
                            }
                        }

                        //find changed non-late costs
                        var sameNonLateItems = incomingDeals.Where(d => !d.IsLateCost).Except(newNonLateCostItems, new DealStockNoComp()).ToList(); //quick piece to ensure we don't bother trying to diff deals we already know are new
                        diffDeals(sameNonLateItems);

                        var sameLateItems = incomingDeals.Where(d => d.IsLateCost).Except(newLateCostItems, new DealStockNoComp()).ToList(); //quick piece to ensure we don't bother trying to diff deals we already know are new
                        diffDeals(sameLateItems);



                        //add diffs to db
                        try
                        {
                            foreach (Diff diff in diffs)
                            {
                                diff.UpdateDate = fileDate;
                            }
                            db.Diffs.AddRange(diffs);  //add them all in one go
                        }
                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $"Failed adding diffs to DB {err.ToString()}";
                            errorCount++;
                        }



                        logMessage.FinishDate = DateTime.UtcNow;
                        logMessage.ProcessedCount = incomingDeals.Count;
                        logMessage.AddedCount = newCount;
                        logMessage.RemovedCount = removedCount;
                        logMessage.ChangedCount = changedCount;
                        logMessage.IsCompleted = true;
                        logMessage.ErrorCount = errorCount;
                        logMessage.StartCount = openingDbCount;
                        int closingDbCount = 0;



                        try
                        {
                            File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                            if (errorCount > 0)
                            {
                                //we have errors so use the reporter
                                logMessage.FailNotes = $"{errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                                await CentralLoggingService.ReportError("UsedDeals", logMessage, true);
                            }
                            //completed , do final tihngs
                            DateTime finishedUpdateDb = DateTime.UtcNow;
                            try
                            {
                                GlobalParam lastUpdate = db.GlobalParams.First(x => x.Description == "usedDealLastUpdate");
                                lastUpdate.TextValue = DateTime.UtcNow.ToString();
                                lastUpdate.DateFrom = DateTime.UtcNow;


                                //update latestAccountingDate within globalParams
                                GlobalParam latestAccountingDate = db.GlobalParams.First(x => x.Description == "Latest Accounting Date");
                                latestAccountingDate.TextValue = incomingDealsDate.Year.ToString() + incomingDealsDate.Month.ToString();
                                latestAccountingDate.DateFrom = incomingDealsDate.Date;
                                latestAccountingDate.DateTo = incomingDealsDate.Date;

                                db.SaveChanges();
                                closingDbCount = db.Deals.Where(d => !d.IsRemoved).Count();
                                finishedUpdateDb = DateTime.UtcNow;
                                logMessage.FinishCount = closingDbCount;
                            }
                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + "Failed to save to DB" + err.ToString();
                                logMessage.ErrorCount++;
                            }

                            logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                            logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;

                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();
                            

                            Logger.Info($"Leaving via door2 ");
                            return;

                            //Logger.Info($"[{DateTime.UtcNow}] | Result: Started with {openingDbCount} item(s), interpreted {incomingDeals.Count} item(s),  found {newCount} new, {removedCount} removed and {changedCount} changed.  Closed with {closingDbCount } item(s)");
                        }

                        catch (Exception err)
                        {
                            logMessage.FailNotes += " FAILED moving file and logging to server" + err.ToString();
                            logMessage.ErrorCount++;
                            await CentralLoggingService.ReportError("UsedDeals", logMessage);
                            
                            Logger.Error($"Leaving via door3 ");
                            return;
                        }

                        //return; //should not get here



                    }

                    catch (Exception err)
                    {
                        //Locks.Deals = false;
                        //var tst = decimal.DecimalPar
                        logMessage.FailNotes += $"General failure " + err.ToString();
                        logMessage.ErrorCount++;
                        logMessage.FailNotes = $"{logMessage.ErrorCount} errors " + logMessage.FailNotes;
                        await CentralLoggingService.ReportError("UsedDeals", logMessage);
                        Logger.Error($"Leaving via door4 ");
                        return;
                    }
                    finally
                    {
                        db.ChangeTracker.Clear();
                        //LocksService.UsedDeals = false;
                    }



                }
            }





        }


    }



}
