import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { ConstantsService } from '../../../../services/constants.service';
import { SelectionsService } from '../../../../services/selections.service';
import { DashboardDataItem, DashboardDataPack, DashboardDataParams } from '../../dashboard.model';
import { DashboardService } from '../../dashboard.service';



@Component({
  selector: 'dashboardAfterSalesVindis',
  templateUrl: './dashboardAfterSalesVindis.component.html',
  styleUrls: ['./dashboardAfterSalesVindis.component.scss']
})


export class DashboardAfterSalesVindisComponent implements OnInit {


  dataItems: DashboardDataItem[];
  weekStart: Date;
  dataPack: DashboardDataPack;
  sub: Subscription;


  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public service: DashboardService

  ) {


  }



  ngOnDestroy() {
    if(!!this.sub)this.sub.unsubscribe();
   }


  ngOnInit() {

    //launch control
    this.initParams()
    this.getData();

    this.sub = this.service.getNewDataTrigger.subscribe(res=>{
      this.getData();
    })

  }

  initParams() {

    this.weekStart = this.constants.startOfThisWeek();
    this.dataItems =
      [
        DashboardDataItem.EvhcQuotedAndSold,
        DashboardDataItem.CitNowsVsWips,
        DashboardDataItem.ServiceGuageMonth,
        DashboardDataItem.PartsGuageMonth,
        DashboardDataItem.PartsStock6To12,
        //DashboardDataItem.PartsStock6To12,
        DashboardDataItem.AgedWips,
        DashboardDataItem.VocNPS,
        DashboardDataItem.WipAgeingSummary,
        DashboardDataItem.ServiceBookings
       
      ]

  
  }


  getData() {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    let parms: DashboardDataParams = {
      SiteIds: this.service.chosenSites.map(x=>x.SiteId).join(','),
      WeekStart: this.weekStart,
      DataItems: this.dataItems,
      Department:'',
    }

    this.getDataService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {
      
      if(!this.dataPack){
        this.dataPack = res;
      }else{
        Object.assign(this.dataPack,res)
      }

      //in order for the template to update the object it passes through the input tag to the child tile.   
      //need a timeout for this template to update the Input() property on the tile 
      //else the child tile regenerates its data again from the old dataPack
      setTimeout(()=>{
        this.service.aftersalesDashboardRedraw.emit();
        this.selections.triggerSpinner.next({ show: false });
      },50)
    })

  }





}
