﻿using log4net;
using log4net.Config;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.EventLog;

using CPHI.Spark.WebScraper.Scheduling;
using CPHI.Spark.WebScraper.Services;
using System.IO;
using System.Reflection;
using System;
using System.Net.Http;
using CPHI.Repository;

namespace CPHI.Spark.WebScraper
{
    public class Program
    {




        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        public static void Main(string[] args)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

            var logRepository = LogManager.GetRepository(Assembly.GetEntryAssembly());
            System.IO.Directory.SetCurrentDirectory(System.AppDomain.CurrentDomain.BaseDirectory);
            XmlConfigurator.Configure(logRepository, new FileInfo("log4net.config"));

            //var textToASCII = $"{ ConfigService.CustomerName}";
            Console.WriteLine(Figgle.FiggleFonts.Standard.Render($"Web Scraper"));

            try
            {
                var host = CreateHostBuilder(args).Build();

                // Initialize the HttpClientFactoryService
                try
                {
                    HttpClientFactoryService.Initialize(host.Services);
                    log?.Info("HttpClientFactoryService Initialized.");
                }
                catch (Exception ex)
                {
                    log?.Fatal("Failed to initialize HttpClientFactoryService.", ex);
                    throw; // Fail fast if essential services can't be resolved
                }

                host.Run();
            }
            catch (Exception ex)
            {
                log?.Fatal("Application failed to start", ex);
                throw;
            }
        }



        public static IHostBuilder CreateHostBuilder(string[] args) =>
         Host.CreateDefaultBuilder(args)
           .ConfigureLogging(
             options => options.AddFilter<EventLogLoggerProvider>(level => level >= LogLevel.Information))
           .ConfigureServices((hostContext, services) =>
           {
               // Register HttpClientFactory
               services.AddHttpClient();

               services.AddHostedService<SchedulerService>()
               .Configure<EventLogSettings>(config =>
               {
                   config.LogName = ConfigService.ServiceName;
                   config.SourceName = $"{ConfigService.ServiceName} Source";
               });

               CPHIDbContext.envi = "webScraperProject";
           }).UseWindowsService();

    }
}



