﻿using log4net;
using log4net.Config;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.EventLog;

using CPHI.Spark.WebScraper.Scheduling;
using System.IO;
using System.Reflection;
using System;
using CPHI.Repository;

namespace CPHI.Spark.WebScraper
{
    public class Program
    {




        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        public static void Main(string[] args)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

            var logRepository = LogManager.GetRepository(Assembly.GetEntryAssembly());
            System.IO.Directory.SetCurrentDirectory(System.AppDomain.CurrentDomain.BaseDirectory);
            XmlConfigurator.Configure(logRepository, new FileInfo("log4net.config"));

            //var textToASCII = $"{ ConfigService.CustomerName}";
            Console.WriteLine(Figgle.FiggleFonts.Standard.Render($"Web Scraper"));

            CreateHostBuilder(args).Build().Run();
        }



        public static IHostBuilder CreateHostBuilder(string[] args) =>
         Host.CreateDefaultBuilder(args)
           .ConfigureLogging(
             options => options.AddFilter<EventLogLoggerProvider>(level => level >= LogLevel.Information))
           .ConfigureServices((hostContext, services) =>
           {
               services.AddHostedService<SchedulerService>()
               .Configure<EventLogSettings>(config =>
               {
                   config.LogName = ConfigService.ServiceName;
                   config.SourceName = $"{ConfigService.ServiceName} Source";
               });

               CPHIDbContext.envi = "webScraperProject";
           }).UseWindowsService();

    }
}



