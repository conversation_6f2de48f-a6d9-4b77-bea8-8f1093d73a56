import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MultiSelectMonth } from 'src/app/components/datePickerMultiSelect/datePickerMultiSelect.component';

import { CphPipe } from 'src/app/cph.pipe';
import { ProfilePicSize, selectableItem, SimpleType, SiteVM, YearMonth } from 'src/app/model/main.model';

import { ConstantsService } from 'src/app/services/constants.service';
import { GetDealDataService } from 'src/app/services/getDeals.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { OrderBookService } from '../orderBook/orderBook.service';

import { PersonPerformanceSummary, PerformanceLeagueSummaryItem, League } from './performanceLeague.model';


@Injectable({
  providedIn: 'root'
})
export class PerformanceLeagueService {

  profilePicSize: ProfilePicSize;
  profilePicSizeOnMenu: ProfilePicSize;
  showProfit: boolean;
  showDelivered: boolean;

  performanceLeagueSummaryItems: PerformanceLeagueSummaryItem[];
  people: Array<PersonPerformanceSummary>;

  vehicleTypeTypes: string[];
  orderTypeTypes: string[];
  franchises: string[];
  departments: selectableItem[];

  leagues: League[];
  league: League;

  sites: SiteVM[];
  sitesIds: number[];

  sortBys: SimpleType[];
  sortBy: SimpleType;

  months: MultiSelectMonth[];

  selectedYTD: boolean;
  selectedLastYear: boolean;

  salesExecName: string;
  salesExecId: number;
  //salesExecSummary: SalesmanStat[];
  isSalesExecView: boolean = true;

  //salesManagerSummary: SalesmanStat[];
  // Use this to prevent click through to Orderbook
  nonConsecutiveMonthsSelected: boolean = false;

  constructor(
    public constants: ConstantsService,
    public orderBookService: OrderBookService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public getDealData: GetDealDataService,
    public cphPipe: CphPipe

  ) {


  }



  initParams(): void {

      //if(!this.months)
      //{
        let today: Date = this.constants.appStartTime;
  
        let currentMonth: MultiSelectMonth = {
          startDate: this.constants.deductTimezoneOffset(new Date(today.getFullYear(), today.getMonth(), 1)),
          isSelected: true
        }
      
        this.months = [currentMonth];
      //};

      //if(!this.vehicleTypeTypes)
      //{
        this.vehicleTypeTypes = this.constants.vehicleTypeTypes;
      //} 

      //if(!this.orderTypeTypes)
      //{
        this.orderTypeTypes = this.constants.clone(this.constants.orderTypeTypesNoTrade);
      //} 

      //if(!this.franchises)
      //{
        this.franchises = this.constants.clone(this.constants.FranchiseCodes);
      //}
      
      //if(!this.departments)
      //{
        this.departments =  [
          { label: this.constants.translatedText.New, isSelected: true },
          { label: this.constants.translatedText.Fleet, isSelected: true },
          { label: this.constants.translatedText.Used, isSelected: true },
        ];
      //}


      this.sites = [this.constants.clone(this.selections.userSite)];
      this.sitesIds = this.selections.userSiteIds;

      this.sortBys = [
        { description: this.constants.translatedText.Units, value: 'dealCount' },
        { description: this.constants.translatedText.Profit, value: 'profit' }
      ];

      //if(!this.sortBy)
      //{
        this.sortBy = this.sortBys[0];
      //}
      

  }

  public getData(): void {

    let departments = this.getDepartmentsAsString();
    let selectedMonths = this.constants.formatMonthsForParams(this.months);

    this.selections.triggerSpinner.emit({show:true,message:this.constants.translatedText.Loading})

    this.getDealData.getPerformanceLeague(selectedMonths, departments, this.isSalesExecView).subscribe(res => {
     this.performanceLeagueSummaryItems = res;
     this.makeLeagues();
     this.selections.triggerSpinner.emit({show:false});
    });

  }

  public makeLeagues(): void {

    this.people = [];

    this.performanceLeagueSummaryItems.forEach(person => {

      let personPerformanceSummary: PersonPerformanceSummary = {
        ...person,

        Id: person.SalesmanId,
        Name: person.SalesmanName,
        CurrentSite: person.CurrentSite,
        dealCount: person.DealCount,
        deliveredCount: person.DeliveredCount,
        toGoCount: person.DealCount - person.DeliveredCount,
        cosmeticPen: this.constants.div(person.CosmeticCount, person.DealCount),
        financePen: this.constants.div(person.FinancedCount, person.DealCount),
        servicePen: 0,
        gapPen: this.constants.div(person.GapCount, person.DealCount),
        warrantyPen: this.constants.div(person.WarrantyCount, person.DealCount),
        paintProtectionPen: this.constants.div(person.PaintProtectionCount, person.DealCount),
        productsPU: this.constants.div(person.ProductCount, person.DealCount),
        rank: 0,
        rankLabel: '',
        league: '',
        profit: person.Margin,
      }

      this.people.push(personPerformanceSummary);

    });

    if(this.sortBy.value == 'dealCount')
    {
      this.people = this.people.sort((a, b) => b.dealCount - a.dealCount);
    }
    
    if(this.sortBy.value == 'profit')
    {
      this.people = this.people.sort((a, b) => b.profit - a.profit);
    }
    
    this.leagues = [];

    this.leagues.push({ label: this.constants.translatedText.PerformanceLeague_Gold, people: this.people.slice(0, 20), skipCount: 0 })
    this.leagues.push({ label: this.constants.translatedText.PerformanceLeague_Silver, people: this.people.slice(20, 50), skipCount: 20, })
    this.leagues.push({ label: this.constants.translatedText.PerformanceLeague_Bronze, people: this.people.slice(50, 9999), skipCount: 50, })

    if (!this.league) {
      //if not exists (first time here), load Gold
      this.league = this.leagues[0];
    } else {
      //reload the same one
      this.league = this.leagues.find(x => x.label == this.league.label);
    }

    this.selections.triggerSpinner.next({ show: false })

  }

  private getDepartmentsAsString(): string
  {
    let departments: string = '';
    
    this.departments.filter(x => x.isSelected).forEach((d, i) => {
      // To align with Department name on table
      let toAppend = d.label == 'New Retail' ? 'New' : d.label;
      departments += i > 0 ? ',' + toAppend : toAppend;
    })

    return departments;
  }




}
