import { Component, EventEmitter, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { ConstantsService } from '../../../../services/constants.service';
import { SelectionsService } from '../../../../services/selections.service';
import { DashboardService } from '../../dashboard.service';
import { DashboardDataItem, DashboardDataPack, DashboardDataParams, DonutData } from '../../dashboard.model';








@Component({
  selector: 'dashboardOverviewVindis',
  templateUrl: './dashboardOverviewVindis.component.html',
  styleUrls: ['./dashboardOverviewVindis.component.scss']
})


export class DashboardOverviewComponent implements OnInit {



  dataItems: DashboardDataItem[];
  weekStart: Date;
  dataPack: DashboardDataPack;
  sub: Subscription;

  newDataEmitter:EventEmitter<void>
  donutDataMonthNew: DonutData;
  donutDataMonthFleet: DonutData;
  donutDataMonthUsed: DonutData;
  donutDataWTDNew: DonutData;
  donutDataWTDFleet: DonutData;
  donutDataWTDUsed: DonutData;
  donutDataYesterdayNew: DonutData;
  donutDataYesterdayFleet: DonutData;
  donutDataYesterdayUsed: DonutData;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
    public service: DashboardService

  ) {


  }



  ngOnDestroy() {
    if(!!this.sub)this.sub.unsubscribe();
   }


  ngOnInit() {

    //launch control
    this.initParams()
    this.getData();

    this.sub = this.service.getNewDataTrigger.subscribe(res=>{
      this.getData();
    })

  }

  initParams() {

    this.newDataEmitter = new EventEmitter();
    this.weekStart = this.constants.startOfThisWeek();
    this.dataItems =
      [
        DashboardDataItem.DonutDataSets,
        DashboardDataItem.DonutDataSetsWTD,
        DashboardDataItem.DonutDataSetsYesterday,

        DashboardDataItem.OverageStockSummary,
        DashboardDataItem.ActivityLevelsAndOverdues,
        
        DashboardDataItem.ServiceGuageMonth,
        DashboardDataItem.ServiceGuageWTD,
        DashboardDataItem.ServiceGuageYesterday,

        DashboardDataItem.PartsGuageMonth,
        DashboardDataItem.PartsGuageWTD,
        DashboardDataItem.PartsGuageYesterday,

        DashboardDataItem.ServiceBookingsNext5,
        DashboardDataItem.WipAgeingSummary,

        DashboardDataItem.DepartmentProfitPerUnitsMonth,
        DashboardDataItem.DepartmentProfitPerUnitsWTD,
        DashboardDataItem.DepartmentProfitPerUnitsYesterday,
      ]

  
  }


  getData() {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    let parms: DashboardDataParams = {
      SiteIds: this.service.chosenSites.map(x=>x.SiteId).join(','),
      WeekStart: this.weekStart,
      DataItems: this.dataItems,
      Department:'',
      WeekStartActivitiesTile: this.service.activitesTileStartDate
    }

    this.getDataService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {
      
      if(!this.dataPack){
        this.dataPack = res;
        this.donutDataMonthNew = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='New')
        this.donutDataMonthFleet = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Fleet')
        this.donutDataMonthUsed = this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Used')
        this.donutDataWTDNew = this.dataPack.DonutDataSetsWTD.find(x=>x.Department==='New')
        this.donutDataWTDFleet = this.dataPack.DonutDataSetsWTD.find(x=>x.Department==='Fleet')
        this.donutDataWTDUsed = this.dataPack.DonutDataSetsWTD.find(x=>x.Department==='Used')
        this.donutDataYesterdayNew = this.dataPack.DonutDataSetsYesterday.find(x=>x.Department==='New')
        this.donutDataYesterdayFleet = this.dataPack.DonutDataSetsYesterday.find(x=>x.Department==='Fleet')
        this.donutDataYesterdayUsed = this.dataPack.DonutDataSetsYesterday.find(x=>x.Department==='Used')
      }else{
        Object.assign(this.dataPack,res);
        Object.assign(this.donutDataMonthNew, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='New'));
        Object.assign(this.donutDataMonthFleet, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Fleet'));
        Object.assign(this.donutDataMonthUsed, this.dataPack.DonutDataSetsMonth.find(x=>x.Department==='Used'));
        Object.assign(this.donutDataWTDNew, this.dataPack.DonutDataSetsWTD.find(x=>x.Department==='New'));
        Object.assign(this.donutDataWTDFleet, this.dataPack.DonutDataSetsWTD.find(x=>x.Department==='Fleet'));
        Object.assign(this.donutDataWTDUsed, this.dataPack.DonutDataSetsWTD.find(x=>x.Department==='Used'));
        Object.assign(this.donutDataYesterdayNew, this.dataPack.DonutDataSetsYesterday.find(x=>x.Department==='New'));
        Object.assign(this.donutDataYesterdayFleet, this.dataPack.DonutDataSetsYesterday.find(x=>x.Department==='Fleet'));
        Object.assign(this.donutDataYesterdayUsed, this.dataPack.DonutDataSetsYesterday.find(x=>x.Department==='Used'));
      }

      //in order for the template to update the object it passes through the input tag to the child tile.   else the child tile regenerates its data again from the old dataPack
      setTimeout(()=>{
        this.newDataEmitter.emit();
        this.selections.triggerSpinner.next({ show: false });
      },50)
    })

  }





}
