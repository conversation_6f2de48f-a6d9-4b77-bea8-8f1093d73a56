﻿using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Model.ViewModels.AutoPricing.Taxonomy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.BusinessLogic.AutoPrice
{
   public class TaxonomyService
   {
      private readonly AutoTraderApiTokenClient tokenClient;
      public readonly AutoTraderTaxonomyDataAccess autoTraderTaxonomyDataAccess;

      public TaxonomyService(IHttpClientFactory httpClientFactory, string atApiKeyIn, string atApiSecretIn, string atBaseURLIn)
      {
         autoTraderTaxonomyDataAccess = new AutoTraderTaxonomyDataAccess(httpClientFactory, atApiKeyIn, atApiSecretIn, atBaseURLIn);
         tokenClient = new AutoTraderApiTokenClient(httpClientFactory, atApiKeyIn, atApiSecretIn, atBaseURLIn);
      }


      public async Task<List<string>> GetFacetOptions(TaxonomyFacet facet, int userRetailerSiteRetailerId, string generationId)
      {
         var tokenResponse = await tokenClient.GetToken();
         return await autoTraderTaxonomyDataAccess.GetFacet(tokenResponse, userRetailerSiteRetailerId, facet, generationId);
      }

      public async Task<List<AtMakeItem>> GetMakes(int userRetailerSiteRetailerId, string vehicleType)
      {

         var tokenResponse = await tokenClient.GetToken();
         return await autoTraderTaxonomyDataAccess.GetMakes(tokenResponse, userRetailerSiteRetailerId, vehicleType);
      }

      public async Task<List<AtModelItem>> GetModels(int userRetailerSiteRetailerId, string makeId)
      {
         var tokenResponse = await tokenClient.GetToken();
         return await autoTraderTaxonomyDataAccess.GetModels(tokenResponse, userRetailerSiteRetailerId, makeId);
      }

      public async Task<List<AtGenerationItem>> GetGenerations(int userRetailerSiteRetailerId, string modelId)
      {
         var tokenResponse = await tokenClient.GetToken();
         return await autoTraderTaxonomyDataAccess.GetGenerations(tokenResponse, userRetailerSiteRetailerId, modelId);
      }

      public async Task<List<string>> GetVehicleTypes(int userRetailerSiteRetailerId)
      {
         var tokenResponse = await tokenClient.GetToken();
         return (await autoTraderTaxonomyDataAccess.GetVehicleTypes(tokenResponse, userRetailerSiteRetailerId)).Select(x => x.name).ToList();
      }

      public async Task<List<AtDerivativeItem>> GetDerivatives(int userRetailerSiteRetailerId, GetDerivativesParams parms)
      {
         var tokenResponse = await tokenClient.GetToken();
         return await autoTraderTaxonomyDataAccess.GetDerivatives(tokenResponse, userRetailerSiteRetailerId, parms);
      }

      public async Task<List<TaxonomyFacetAndChoices>> GetAllFacetsForGeneration(int advertiserId, string generationId)
      {
         // Create a list of tasks to run in parallel
         var tasks = Enum.GetValues(typeof(TaxonomyFacet))
                .Cast<TaxonomyFacet>()
                .Select(async facet => new TaxonomyFacetAndChoices
                {
                   Facet = facet,
                   Choices = await GetFacetOptions(facet, advertiserId, generationId)
                }).ToList();


         // Await all tasks to complete
         var results = await Task.WhenAll(tasks);

         return results.ToList();
      }

      public async Task<AtDerivativeItemFull> GetDerivative(int userRetailerSiteRetailerId, string derivativeId)
      {
         var tokenResponse = await tokenClient.GetToken();
         return await autoTraderTaxonomyDataAccess.GetDerivative(tokenResponse, userRetailerSiteRetailerId, derivativeId);
      }

      public async Task<List<TaxonomyFacetAndChoices>> GetDerivativeTaxonomies(int retailerSiteId, string derivativeId)
      {
         // 1. Identify the generationId for the derivativeId
         var derivative = await GetDerivative(retailerSiteId, derivativeId);

         if (derivative.generationId == null)
         {
            return null;
         }

         // 2. Identify the various taxonomy options for the generationId
         var taxonomies = await GetAllFacetsForGeneration(retailerSiteId, derivative.generationId);

         return taxonomies;
      }


   }
}
