//SMALL SCREEN AND ALL SCREENS UNLESS OVERRIDEN BELOW
.modal-dialog {
    margin: 1em auto;
    --bs-modal-width: 60vw;
    width: 60vw;
    max-width: 1200px;

    &.modal-sm {
        --bs-modal-width: 30vw;
        width: 30vw;
    }

    //make medium modals a little wider
    &.modal-md {
        --bs-modal-width: 75vw;
        width: 75vw;
    }

    &.modal-lg {
        --bs-modal-width: 98vw;
        width: 98vw;
    }

    .modal-header {
        padding: 0.5em;
        background-color: var(--grey95);

        .modal-title {
            font-weight: 500;
        }

        button.close {
            border: none;
            padding: 0;
            line-height: 1;
        }
    }

    .modal-body {
        min-height: 100px;
        max-height: 83vh;
        overflow-y: auto;
        background-color: var(--grey95);
        padding: 0.5em;
    }

    .modal-footer {
        padding: 0.5em;
        background-color: var(--grey95);

        button {
            margin-top: 0;
            margin-bottom: 0;
        }
    }
}





//MEDIUM SCREEN
@media only screen and (min-width: 1527px) {
    .modal-dialog {
        max-width: 1700px;
    }

    //make medium modals back to normal bootstrap width
    .modal-dialog {
        &.modal-md {
            --bs-modal-width: 65vw;
            width: 65vw;
        }
    }

}





//LARGE SCREEN
@media only screen and (min-width: 1920px) {
    .modal-dialog {
        max-width: 2000px;
    }

}