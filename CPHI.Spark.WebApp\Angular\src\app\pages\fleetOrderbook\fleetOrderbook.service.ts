
import { EventEmitter, Injectable } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CellClickedEvent, ColumnApi, ColumnState, GridApi, IRowNode, SelectionChangedEvent } from 'ag-grid-community';
import { CphPipe } from 'src/app/cph.pipe';
import { DashboardMeasure } from 'src/app/model/DashboardMeasure';
import { FleetOrderbookEditableOption } from 'src/app/model/FleetOrderbookEditableOption';
import { FleetOrderbookRowCollection } from 'src/app/model/FleetOrderbookRowCollection';
import { FleetOrderbookSummaryRow } from 'src/app/model/FleetOrderbookSummaryRow';
import { GetFleetOrderbookRowsParams } from 'src/app/model/GetFleetOrderbookRowsParams';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { GetDealDataService } from 'src/app/services/getDeals.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { FleetOrderbookRow } from "../../model/FleetOrderbookRow";
import { DashboardService } from '../dashboard/dashboard.service';
import { FleetOrderbookColumnService } from './fleetOrderbookTable/fleetOrderbookColumn.service';
import { BIChartTileDataType } from 'src/app/components/biChartTile/biChartTile.component';
import { FleetOrderbookTableStateParams } from 'src/app/model/FleetOrderbookTableStateParams';
import { TableLayoutManagementParams } from 'src/app/model/TableLayoutManagementParams';
import { FleetOrderbookComment } from 'src/app/model/FleetOrderbookComment';
import { TableLayoutManagementService } from 'src/app/components/tableLayoutManagement/tableLayoutManagement.service';
import { CPHAutoPriceColDef } from 'src/app/model/CPHColDef';
import { MenuItemNew } from 'src/app/model/main.model';

@Injectable({
  providedIn: 'root'
})
export class FleetOrderbookService {
 


  editedNodeIds: string[];
  //pivotMode: boolean;
  //rows: FleetOrderbookRow[];
  isRenault: boolean = true;
  includeHidden: boolean = false;
  showIncludeRemovedPicker:boolean=false;
  includeRemovedSince: Date | null = null;
  showTiles: boolean = false;
  rowData: FleetOrderbookRow[];
  //gridApi: GridApi;
  //gridColumnApi: ColumnApi;
  searchTerm: UntypedFormControl = new UntypedFormControl();
  //currentFilterState: any;

  //the users's choice by clicking on the grid  
  chosenRowNodes: IRowNode[];
  lastClickedCell: CellClickedEvent;

  newItemsCount:number;
  itemsNotOnLatestDownloadCount:number;

  dashboardRawData: FleetOrderbookSummaryRow[];
  dashboardRawDataFiltered: FleetOrderbookSummaryRow[];
  dashboardRawDataHighlighted: FleetOrderbookSummaryRow[]; //subset of filtered.  

  dashboardFilterChoices: DashboardMeasure[]
  dashboardHighlightChoices: DashboardMeasure[]
  dashboardRefreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
  dashboardRefreshTileEmitter: EventEmitter<void> = new EventEmitter();

  tableStateLabels: string[];
  chosenTableStateLabel: string
  currentColumnState: ColumnState[];
  currentFilterState:any
  skipNextFilterStateApplication:boolean;
  loadedReport:FleetOrderbookTableStateParams

  //for those fields the user can edit which are foreign keys, what are the values to choose from
  editableOptions: FleetOrderbookEditableOption[]

  includeNotRegistered: boolean;

  tableLayoutManagement: TableLayoutManagementParams;
  constructor(
    //public selections: SelectionsService,
    public constants: ConstantsService,
    public router: Router,
    public selections: SelectionsService,
    

    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public excel: ExcelExportService,
    public getDealData: GetDealDataService,
    public getDataMethods: GetDataMethodsService,
    public columnDefsService: FleetOrderbookColumnService,
    public activeModal: NgbActiveModal,
    public dataMethods: GetDataMethodsService,

    public apiAccess: ApiAccessService,
    public dashboardService: DashboardService,
    public tableLayoutManagementService: TableLayoutManagementService
  ) {

  }



  getDashboardData(): void {
    // this.dashboardRawData = null;
    this.dashboardRawDataFiltered = null;
    this.dashboardRawDataHighlighted = null;
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading })
    //let params: DashboardDataVNParams = this.getParms(this.chosenMonth)
    const method = this.isRenault ? 'GetFleetOrderbookSummaryRowsRenault' : 'GetFleetOrderbookSummaryRowsNissan'
    this.apiAccess.get('api/FleetOrderbook', method).subscribe((res: FleetOrderbookSummaryRow[]) => {
      res.map(item => {
        if (item.FRD) { item.FRD = new Date(item.FRD) }
        if (item.OrderDate) { item.OrderDate = new Date(item.OrderDate) }
        //if (item.DeliveryDate) { item.DeliveryDate = new Date(item.DeliveryDate) }
      });
      this.dashboardRawData = res;
      this.dashboardRawDataFiltered = this.filterData(this.dashboardRawData, this.dashboardFilterChoices);
      this.dashboardRawDataHighlighted = this.filterData(this.dashboardRawDataFiltered, this.dashboardHighlightChoices)
      this.dashboardRefreshFilterListsEmitter.emit();
      this.dashboardRefreshTileEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    }, error => {
      console.error("ERROR: ", error);
    })

  }

  getTableState(label: string, isRenault: boolean, doColumns:boolean,doFilters:boolean) {

    if(label == null || label == undefined){ return; }

    this.chosenTableStateLabel = label;
    let payload = [{ key: 'label', value: label }, { key: 'isRenault', value: isRenault }];
    this.apiAccess.get('api/FleetOrderbook', 'GetTableState', payload).subscribe((res: FleetOrderbookTableStateParams) => {
      //set loaded report
      this.loadedReport = res;
      this.currentColumnState = JSON.parse(res.State as string) as ColumnState[];
      this.currentFilterState = JSON.parse(this.loadedReport.FilterModel);

      this.restoreGridState(doColumns,doFilters);
    })
  }
  
  restoreGridState(doColumns:boolean,doFilters:boolean){
    if(doColumns){
      this.tableLayoutManagement.gridColumnApi.applyColumnState({ state: this.currentColumnState, applyOrder: true, });
    }

    if(doFilters){
      this.tableLayoutManagement.gridApi.setFilterModel(this.currentFilterState);
    }
  }





  // getTableStateOnlyColumns(label: string, isRenault: boolean) {
  //   if(label == null || label == undefined) { return; }

  //   this.chosenTableStateLabel = label;
  //   let payload = [{ key: 'label', value: label }, { key: 'isRenault', value: isRenault }];

  //   this.apiAccess.get('api/FleetOrderbook', 'GetTableState', payload).subscribe((res: FleetOrderbookTableStateParams) => {
  //     this.currentColumnState = JSON.parse(res.State as string) as ColumnState[];
  //     this.gridColumnApi.applyColumnState({ state: this.currentColumnState, applyOrder: true });
  //     this.gridColumnApi.setPivotMode(res.IsPivoted);
  //     //this.pivotMode = res.IsPivoted;
  //   })
  // }

  // getTableStateOnlyFilters(label: string, isRenault: boolean) {
  //   if(label == null || label == undefined) { return; }

  //   this.chosenTableStateLabel = label;
  //   let payload = [{ key: 'label', value: label }, { key: 'isRenault', value: isRenault }];

  //   this.apiAccess.get('api/FleetOrderbook', 'GetTableState', payload).subscribe((res: FleetOrderbookTableStateParams) => {
  //     this.filterModel = JSON.parse(res.FilterModel)
  //     this.tableLayoutManagement.gridApi.setFilterModel(this.filterModel);
  //   })
  // }


  filterData(dataIn: FleetOrderbookSummaryRow[], stringFilterChoices: DashboardMeasure[]): FleetOrderbookSummaryRow[] {
    let results = [];
    dataIn.forEach(item => {
      //check all chosen strings
      let filterOutThisItem: boolean = false;
      stringFilterChoices.forEach(choice => {
        if (choice.DataType === BIChartTileDataType.month) {
          let itemPropertyAsDateString = this.cphPipe.transform(item[choice.FieldName], 'month', 0);
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(itemPropertyAsDateString)) { filterOutThisItem = true; } }
        } else {
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(item[choice.FieldName])) { filterOutThisItem = true; } }
        }
      })

      if (!filterOutThisItem) { results.push(item) }
    })

    return results;
  }



  showOrderbook() {
    // Go to Orderbook
    let navLocn: string = this.constants.environment.fleetOrderbookURL;
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl(navLocn);
    if (menuItem) { this.constants.navigateByUrl(menuItem); } //, 'operationreports'
  }


  testFilter(){
    console.log(this.tableLayoutManagement.gridApi.getFilterModel() );
  }


  filterForUserChoices(res: FleetOrderbookRow[]): FleetOrderbookRow[] {

    let results = res;

    return results;

  }

  reloadData() {
    this.getData(this.isRenault, this.includeHidden, this.includeRemovedSince, false)
  }

  initialiseTableLayoutManagement() {
    if (this.tableLayoutManagement) { return; }
    this.tableLayoutManagement = {
      pageName: 'fleetOrderbook',
      ownTableStates: null,
      standardTableStates: null,
      sparkTableStates: null,
      sharedTableStates: null,
      availableTableStates: null,
      selectedTableState: null,
      loadedTableState: null,
      usersToShareWith: null,
      gridApi: null,
      gridColumnApi: null,
      filterModel: null,
      isRenault: true,
      refreshReportsListEmitter: new EventEmitter<void>(),
      originalColDefs:null
    }
  }

  getData(isRenault: boolean, includeHidden: boolean, includeRemovedSince: Date | null, andGetLatestTableState: boolean) {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText ? this.constants.translatedText.Loading : 'Loading' })

    

    const brandChoice: string = isRenault ? 'Renault' : 'Nissan';
    let parms: GetFleetOrderbookRowsParams = {
      brand: brandChoice,
      includeHidden: includeHidden,
      includeArchivedSince: includeRemovedSince,
      filterChoices: null
    }

    this.apiAccess.post('api/FleetOrderbook', 'GetFleetOrderbookRows', parms).subscribe((res: FleetOrderbookRowCollection) => {
      let expanded: FleetOrderbookRow[] = this.constants.expandTransmittedStrings(res.RowsAsStrings, res.Fields);
      console.log(expanded.map(x=>x.LeaseNumber))
      expanded.forEach(rowItem => {
        if(rowItem.DeliveryDate){
          rowItem.DeliveryDate = new Date(rowItem.DeliveryDate)
          if(rowItem.DeliveryDate < this.constants.todayEnd  && !rowItem.RegistrationDate){
            rowItem.delDueNotRegd = true
          }
        };
        
        rowItem.regMismatch = !!rowItem.DealsReg && !!rowItem.Reg && rowItem.DealsReg !== rowItem.Reg
      })
      
      let sorted = isRenault ? this.chrisCustomSort(expanded) : expanded;
      this.editedNodeIds = [];

      //now expand the comments
      sorted.map(row=>{
        row.comments = [];
        row.SparkComments.split('^#^').forEach(comm => {
          const splitOut: string[] = comm.split('|')
          row.comments.push({
            CommentId: parseInt(splitOut[0]),
            FleetOrderbookItemId: row.RenaultOrNissanOrderItemId,
            IsRenault: row.Brand === 'Renault',
            CommentDate: new Date(splitOut[1]),
            Text: splitOut[2],
            Person: splitOut[3]
          })
        })
      })

     

      //prevent slow running getRenault replacing quick running Nissan
      if (sorted[0].Brand === brandChoice) {
        this.rowData = [];
        sorted.forEach(x=>{
          this.rowData.push(new FleetOrderbookRow(x));
        })
        if (this.tableLayoutManagement.gridApi) {
          this.tableLayoutManagement.gridApi.setRowData(this.rowData);
          this.updateCounts();
          this.resetRowChoices();
        }
        
        
        if (andGetLatestTableState) {
          this.getTableStateLabels(this.isRenault);
          if(this.tableLayoutManagement.gridApi){
            this.tableLayoutManagement.gridApi.setColumnDefs(this.isRenault ? this.columnDefsService.provideRenaultColDefs(this.rowData) : this.columnDefsService.provideNissanColDefs(this.rowData))
          }
          //this.getLastLoadedTableState(this.isRenault);
        }else{
          this.constants.toastSuccess('Grid updated');
        }
        this.selections.triggerSpinner.emit()
      } else {
        //perhaps we toggled to the other brand whilst the api get was in progress, so now just do nothing
      }

    })
  }

  chrisCustomSort(rowData: FleetOrderbookRow[]) {
    // Define the custom sort order
    const sortOrder: string[] = [
      'REG',
      'AAD/>90',
      'AAD',
      'EXIC',
      'ATIC',
      'EXPE',
      'FR.T',
      'PROD',
      'FIFE',
      'FACT',
      'M+1',
      'M+2',
      'NAF',
    ];
  
    // Function to get the index of the status in the sortOrder array
    // Items not found will have an index of sortOrder.length, thus sorting them at the end
    const getStatusIndex = (status: string | null | undefined) => {
      const index = sortOrder.indexOf(status ?? '');
      return index === -1 ? sortOrder.length : index;
    };
  
    // Sort the rowData array based on the custom sortOrder
    rowData.sort((a, b) => {
      // Sort by GFCStockAge first, descending
      const stockAgeA = a.GFCStockAge ?? 0; 
      const stockAgeB = b.GFCStockAge ?? 0; 
      if (stockAgeB !== stockAgeA) {
        return stockAgeB - stockAgeA;
        
      }else{
        // If GFCStockAge is equal, then sort by the custom sortOrder for Status
        const statusIndexA = getStatusIndex(a.Status);
        const statusIndexB = getStatusIndex(b.Status);
        return statusIndexA - statusIndexB;
      }
    });
  
    // Return the sorted array
    return rowData;
  }

  private updateMainTableRows() {
    //let currentState;
    //if(this.gridColumnApi){currentState = this.gridColumnApi.getColumnState()}
    //if(this.gridColumnApi){this.gridColumnApi.applyColumnState({state:currentState,applyOrder:true})}
    this.tableLayoutManagement.gridApi.setRowData(this.rowData);
    this.updateCounts();
  }

  updateCounts() {
    this.newItemsCount = 0;
    this.itemsNotOnLatestDownloadCount = 0;

    this.tableLayoutManagement.gridApi.forEachNodeAfterFilter(node => {
      if (node.data) 
      {
        const row: FleetOrderbookRow = node.data;
        if (!row.StockCategory) {
          this.newItemsCount++;
        }

        if(!row.IsOnLatestDownload)
        {
          this.itemsNotOnLatestDownloadCount++;
        }
      }
    });


  }

  public resetRowChoices() {
    this.lastClickedCell = null;
  }

  refreshTable() {
    if (this.tableLayoutManagement.gridApi) { this.tableLayoutManagement.gridApi.setRowData(this.rowData) }
  }


  showNewItems(){
    let filterModel: any = this.tableLayoutManagement.gridApi.getFilterModel();
    //filterModel.IsMatchedInMonth = {filterType:'set',values:['true']}
    filterModel.StockCategory = {filterType:'set',values:[null]}
    this.currentFilterState = filterModel;
    this.tableLayoutManagement.gridApi.setFilterModel(this.currentFilterState)
  }

  showItemsNotOnLatestDownload(){
    let filterModel: any = this.tableLayoutManagement.gridApi.getFilterModel();

    filterModel.IsOnLatestDownload = {filterType:'set',values:['false']};

    this.currentFilterState = filterModel;
    this.tableLayoutManagement.gridApi.setFilterModel(this.currentFilterState)
  }


  toggleIncludeHidden() {
    this.includeHidden = !this.includeHidden;
    //const includeRemovedSince = this.includeRemoved ? this.includeRemovedSince : null;
    this.getData(this.isRenault, this.includeHidden, this.includeRemovedSince, true);
  }

  toggleIncludeRemoved() {
    this.showIncludeRemovedPicker = !this.showIncludeRemovedPicker;
    if(!this.showIncludeRemovedPicker){
      this.includeRemovedSince = null
      this.getData(this.isRenault, this.includeHidden,this.includeRemovedSince, true);
    }
  }

  excelOldStyle() {
    this.constants.exportExcel(this.rowData, null)
  }

  public chooseIncludeRemovedSince(event) {
    //const includeRemovedSince = this.includeRemoved ? this.includeRemovedSince : null;
    this.getData(this.isRenault, this.includeHidden,this.includeRemovedSince, true);
  }

  toggleShowTiles() {
    this.showTiles = !this.showTiles;

    // Get the relevant data 
    if (!this.showTiles) {

      let brand = this.isRenault ? 'Renault' : 'Nissan';

      // We already have the correct row data - don't reload
      if(this.rowData != null && this.rowData[0].Brand == brand)
      {
        return;
      }

      this.getData(this.isRenault, this.includeHidden, this.includeRemovedSince, true);

    // Tiles
    } else {
      this.getDashboardData();
    }
  }

  onTableSelectionChanged(params: SelectionChangedEvent) {
    this.chosenRowNodes = params.api.getSelectedNodes();
  }


  public provideColDefs():CPHAutoPriceColDef[] {
    let rowData = this.rowData ?? [];
    return this.isRenault ? this.columnDefsService.provideRenaultColDefs(rowData) : this.columnDefsService.provideNissanColDefs(rowData)
  }


  getTableStateLabels(isRenault: boolean) {
    let payload = [{ key: 'isRenault', value: isRenault }];
    this.apiAccess.get('api/FleetOrderbook', 'GetTableStateLabels', payload).subscribe((res: string[]) => {
      this.tableStateLabels = res;
    })
  }

  togleIncludeNotRegistered() {
    this.includeNotRegistered = !this.includeNotRegistered;

    let filterModel: any = this.tableLayoutManagement.gridApi.getFilterModel();
    filterModel.delDueNotRegd = this.includeNotRegistered ? { filterType: 'set', values: ['true'] } : { }
    this.currentFilterState = filterModel;
    this.tableLayoutManagement.gridApi.setFilterModel(this.currentFilterState);
  }





}




