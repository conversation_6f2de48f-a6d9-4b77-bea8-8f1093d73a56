import { Component, Input, OnInit } from "@angular/core";
import { WipSummary } from "src/app/model/main.model";
import { PartsSummaryService } from "src/app/pages/partsSummary/partsSummary.service";
import { ServiceSummaryService } from "src/app/pages/serviceSummary/serviceSummary.service";
import { ConstantsService } from "src/app/services/constants.service";

interface WipBarsFormatted {
  Label: string;
  Value: number;
  Height: number;
}

@Component({
    selector: "wipAgeingSummaryTile",
    template: `
      <div class="dashboard-tile-inner">
        <div class="dashboard-tile-header">
          {{ constants.translatedText.Dashboard_WipAgeingSummary }}
        </div>
        <div class="dashboard-tile-body">
          <div id="wip-chart">
            <ng-container *ngFor="let bar of bars, let i = index">
              <div class="wip-bar" [ngClass]="'bar-' + (i + 1)" [ngStyle]="{ 'height.%': bar.Height }">
                <div class="bar-inner">
                  <span>{{ bar.Value | cph:'currency':0 }}</span>
                </div>
              </div>
              <div class="wip-bar-label" [ngClass]="'label-' + (i + 1)">{{ bar.Label }}</div>
            </ng-container>
            <div class="axis"></div>
          </div>
        </div>
      </div>
    `,
    styles: [
      `
      #wip-chart { width: 80%; margin: 0 auto; position: relative; height: calc(100% - 2em) }

      .wip-bar { width:28%; border-radius: 0.0em 0.0em 0px 0px; position: absolute; bottom: 0px; }
      .wip-bar.bar-1 { left: 4%; background: var(--goodColour) }
      .wip-bar.bar-2 { left: 36%; background: var(--brightColour) }
      .wip-bar.bar-3 { right: 4%; background: var(--badColour) }

      .wip-bar .bar-inner { display: flex; position: relative; height: 100%; width: 100%; }
      .wip-bar .bar-inner span { position: absolute; top: -1.4em; width: 100%; text-align: center; }

      .wip-bar-label { width: 28%; border-radius: 0.3em 0.3em 0px 0px; position: absolute; bottom:-2em; text-align: center; }
      .wip-bar-label.label-1 { left: 4%; }
      .wip-bar-label.label-2 { left: 36%; }
      .wip-bar-label.label-3 { right: 4%; }

      .axis { width: 100%; position: absolute; border-bottom: 1px solid var(--grey70); bottom: 0px; }
      `,
    ],
  })

  export class WipAgeingSummaryTileComponent implements OnInit {
    @Input() page: string;
    
    bars: WipBarsFormatted[];

    constructor (
      public constants: ConstantsService,
      public serviceSummaryService: ServiceSummaryService,
      public partsSummaryService: PartsSummaryService
    ) { }
    
    ngOnInit(): void {
      let bars: WipSummary = this[`${this.page}SummaryService`].wipSummary;
      let barsFormatted: WipBarsFormatted[] = [];

      let height: number = 0;
      let maxValue: number = 0;

      for (let bar in bars) {
        // Work out the label
        let label: string;

        if (bar.includes('Less')) { label = `<30 ${this.constants.translatedText.DaysLower}`; }
        else if (bar.includes('Greater')) { label = `60+ ${this.constants.translatedText.DaysLower}`; }
        else { label = `30-60 ${this.constants.translatedText.DaysLower}`; }

        // Work out the bar height
        if (bars[bar] > maxValue) { maxValue = bars[bar] }
        maxValue = maxValue * 1.1;
        height = bars[bar] / maxValue * 100;

        // Push the values to the array
        barsFormatted.push({
          Label: label,
          Value: bars[bar],
          Height: height
        })
      }

      this.bars = barsFormatted;
    }

  }