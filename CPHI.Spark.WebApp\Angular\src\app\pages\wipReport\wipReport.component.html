<nav class="navbar">
  <nav class="generic" >
    <h4 id="pageTitle">
      <div>
        {{constants.translatedText.Dashboard_WipReport_Title}}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Wip"></sourceDataUpdate>
      </div>
    </h4>

    <ng-container *ngIf="selections.wipReport">

      <!-- Site selector -->
      <ng-container *ngIf="constants.environment.dealDone_showRRGSitePicker && selections.wipReport.showDetail">
          <sitePickerRRG [allSites]="constants.sitesActiveSales" [sitesFromParent]="selections.selectedSites"
            [buttonClass]="" (updateSites)="onUpdateSites($event)">
          </sitePickerRRG>
      </ng-container>

      <ng-container *ngIf="constants.environment.dealDone_showVindisSitePicker && selections.wipReport.showDetail">
          <sitePicker [allSites]="constants.sitesActiveSales" [sitesFromParent]="selections.wipReport.sitesRows"
            [buttonClass]="" (updateSites)="onUpdateSites($event)"></sitePicker>
      </ng-container> 

      <!-- As at -->
      <div ngbDropdown dropright class="d-inline-block">
        <button class=" btn btn-primary" ngbDropdownToggle>
          <span *ngIf="!selections.wipReport.ageAtMonthEnd">{{constants.translatedText.Dashboard_WipReport_AsAtNow}}</span>
          <span *ngIf="selections.wipReport.ageAtMonthEnd">{{constants.translatedText.Dashboard_WipReport_AsAtMonthEnd}}</span>
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <button (click)="setAsAtMonthEnd(false)" ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>
            {{constants.translatedText.Now}}
          </button>
          <button (click)="setAsAtMonthEnd(true)" ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>
            {{constants.translatedText.Dashboard_WipReport_MonthEnd}}
          </button>
        </div>
      </div>

      <!-- Zero wips -->
      <button
        *ngIf="selections.wipReport.showDetail"
        class="btn btn-primary"
        [ngClass]="{ 'active': selections.wipReport.includeZeroWips}"
        (click)="includeZeroWips()"
      >
        {{ constants.translatedText.Dashboard_WipReport_IncludeZeroWips }}
      </button>

    </ng-container>
  </nav>

  <nav *ngIf="selections.wipReport && selections.wipReport.showDetail" class="pageSpecific">
    <!-- Search box for wips -->
    <form>
      <i class="fas fa-search"></i>
      <input placeholder="Search" class="form-control ml-2" type="text" [formControl]="filterWips" />
    </form>
  </nav>
</nav>

<!-- Main Page -->
<div class="content-new">
  <div *ngIf="selections.wipReport" class="content-inner-new">
    <div *ngIf="!selections.wipReport.showDetail" id="gridHolder">
      <!-- Sites table -->
      <wipSitesTable *ngIf="selections.wipReport.sitesRows" (clickedSite)="selectSite($event)"></wipSitesTable>
      <div class="tableSpacer"></div>
      <!-- Regions table -->
      <wipSitesTable *ngIf="selections.wipReport.regionsRows" [isRegional]="true" (clickedSite)="selectSite($event)"></wipSitesTable>
    </div>
    <!-- <div *ngIf="selections.wipReport.showDetail" id="gridHolder"> -->
      <!-- Detail table -->
      <wipsDetailTable *ngIf="selections.wipReport.showDetail && selections.wipReport.wipsRowsFiltered"></wipsDetailTable>
    <!-- </div> -->
  </div>
</div> 