# Public Holiday API Usage Examples

## API Endpoints

### 1. Add a New Public Holiday
**POST** `/api/PublicHolidays/AddPublicHoliday`

**Request Body:**
```json
{
  "date": "2024-12-25T00:00:00",
  "dealerGroupId": 1,
  "regionIds": [1, 2, 3]
}
```

**Response:**
```json
{
  "id": 123,
  "date": "2024-12-25T00:00:00",
  "dealerGroupId": 1,
  "regionIds": [1, 2, 3],
  "success": true,
  "message": "Public holiday added successfully"
}
```

### 2. Get Public Holidays for Current User's Dealer Group
**GET** `/api/PublicHolidays/GetPublicHolidays`

**Response:**
```json
[
  {
    "id": 123,
    "date": "2024-12-25T00:00:00",
    "dealerGroup_Id": 1,
    "dealerGroup": null,
    "regionMaps": []
  }
]
```

### 3. Get Available Public Holiday Regions
**GET** `/api/PublicHolidays/GetPublicHolidayRegions`

**Response:**
```json
[
  {
    "id": 1,
    "region": "England",
    "holidayMaps": []
  },
  {
    "id": 2,
    "region": "Scotland",
    "holidayMaps": []
  },
  {
    "id": 3,
    "region": "Wales",
    "holidayMaps": []
  }
]
```

## JavaScript/TypeScript Usage Examples

### Adding a Public Holiday
```typescript
const addPublicHoliday = async (date: Date, dealerGroupId: number, regionIds: number[]) => {
  const request = {
    date: date.toISOString(),
    dealerGroupId: dealerGroupId,
    regionIds: regionIds
  };

  try {
    const response = await fetch('/api/PublicHolidays/AddPublicHoliday', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(request)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Public holiday added successfully:', result);
    } else {
      console.error('Error adding public holiday:', result.message);
    }
    
    return result;
  } catch (error) {
    console.error('Network error:', error);
  }
};

// Usage
addPublicHoliday(new Date('2024-12-25'), 1, [1, 2, 3]);
```

### Getting Public Holidays
```typescript
const getPublicHolidays = async () => {
  try {
    const response = await fetch('/api/PublicHolidays/GetPublicHolidays', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const holidays = await response.json();
    console.log('Public holidays:', holidays);
    return holidays;
  } catch (error) {
    console.error('Error fetching public holidays:', error);
  }
};
```

### Getting Available Regions
```typescript
const getPublicHolidayRegions = async () => {
  try {
    const response = await fetch('/api/PublicHolidays/GetPublicHolidayRegions', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const regions = await response.json();
    console.log('Available regions:', regions);
    return regions;
  } catch (error) {
    console.error('Error fetching regions:', error);
  }
};
```

## C# Usage Examples

### Using HttpClient
```csharp
public class PublicHolidayApiClient
{
    private readonly HttpClient _httpClient;

    public PublicHolidayApiClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<PublicHolidayResponse> AddPublicHolidayAsync(PublicHolidayRequest request)
    {
        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PostAsync("/api/PublicHolidays/AddPublicHoliday", content);
        var responseJson = await response.Content.ReadAsStringAsync();
        
        return JsonSerializer.Deserialize<PublicHolidayResponse>(responseJson);
    }
}
```

## Notes

- All endpoints require authentication (Bearer token)
- The `date` field should be in ISO 8601 format
- The API automatically handles the creation of region mappings
- Error handling is built into the response structure
- The `GetPublicHolidays` endpoint automatically filters by the current user's dealer group
