
import { Injectable } from '@angular/core';
import { ConstantsService } from './constants.service'

//excel 
import * as excelJS from 'exceljs';
import * as fs from 'file-saver';
import { SheetToExtract, SimpleSheet } from '../model/main.model';
import { SeedDataService } from './seedData.service';
import { CphPipe } from '../cph.pipe'
import { WorkSheet } from 'xlsx/types';
import { IRowModel } from 'ag-grid-community';

@Injectable({
  providedIn: 'root'
})


export class ExcelExportService {

  constructor(
    public constants: ConstantsService,
    public seed: SeedDataService,
    public cphPipe: CphPipe,


  ) { }



  createSheetObject(tableModel: any, tableName: string, overrideWidthsFactor?: number, providedInitialColumnsToHighlight?: number, doNotExportJustReturn?: boolean) {
    //setup arrays to populate
    let finalHeaders: Array<any[]> = []
    let finalRows: Array<any[]> = []
    let notes: Array<any[]> = []
    let finalFooters: Array<any[]> = []

    // //the normal headings
    let columns = tableModel.columnModel.displayedColumns
    let columnTypes: string[] = []
    let columnWidths: number[] = []
    //let columnHeadings: string[] = []
    let columnFields: string[] = []

    columns.forEach(col => {

      if (col.visible) 
      {
        if (col.colDef.type == 'labelPercent') {
          columnTypes.push('percent');
        } else if (typeof col.colDef.type !== 'undefined') {
          columnTypes.push(col.colDef.type);
        }
        let width = col.actualWidth / 8 //divide by 8 seems about right

        if (overrideWidthsFactor)
        {
          width = overrideWidthsFactor * width
        } 

        // Make Comments column a lot wider for Fleet Orderbook
        if(tableName == 'Fleet Orderbook' && col.userProvidedColDef.headerName == 'Comments')
        {
          width = width * 6;
        }

        columnWidths.push(width);
        //columnHeadings.push(col.colDef.headerName)
        let field = col.colDef.excelField || col.colDef.field
        columnFields.push(field)
      }
    })

    // finalHeaders.push(columnHeadings);

    let totalHeaderRows: number = tableModel.columnModel.primaryHeaderRowCount;

    //build headerRowsArray
    let headerRows: string[][] = []

    for (let i = 0; i < totalHeaderRows; i++) {
      headerRows.push([])
    }

    //now walk down the columnTree populating these
    let headingTree = totalHeaderRows > 1 ? tableModel.columnModel.primaryColumnTree : tableModel.columnModel.displayedColumns

    headingTree.forEach((row, i) => {

      if (row.colId === 'ag-Grid-AutoColumn') return;
      //firstly for top row
      let label = ""
      if (!!row.colGroupDef && !!row.colGroupDef.headerName) label = row.colGroupDef.headerName;
      if (!!row.userProvidedColDef && !!row.userProvidedColDef.headerName) label = row.userProvidedColDef.headerName

      headerRows[0].push(label);

      if(label == 'Answer Rate' && tableName == 'Tele Stats')
      {
        headerRows[0].push('');
        headerRows[0].push('');
        headerRows[0].push('');
        headerRows[0].push('');
        headerRows[0].push('');
      }

      //for 2nd row
      if (totalHeaderRows > 1) {

        if(tableName == 'Tele Stats')
        {
          headerRows[1].push('');
          headerRows[1].push('');
        }

        row.children.forEach((child, i) => {
          let label = ""

          if (row.colGroupDef?.children) {
            const headerName = row.colGroupDef.children[i].headerName;

            if (
              tableName === this.constants.translatedText.Common_Aftersales + ' ' + this.constants.translatedText.Dashboard_KPIs &&
              (headerName === 'Service' || headerName === 'Productivity' || headerName === 'Technical Control')
            ) {
              // Do nothing
            } else {
              label = headerName;
            }
          }

          if (!!child.userProvidedColDef && !!child.userProvidedColDef.headerName) {
            label = child.userProvidedColDef.headerName
          }

          if (child.visible) {
            headerRows[1].push(label);
            if (i > 0) headerRows[0].push('');
          }

          //for 3rd row
          if (totalHeaderRows > 2) {
            child.children.forEach((grandchild, j) => {
              let label = ""
              if (!!grandchild.userProvidedColDef && !!grandchild.userProvidedColDef.headerName) { label = grandchild.userProvidedColDef.headerName }
              if (grandchild.visible) {
                headerRows[2].push(label);
                if (j > 0) headerRows[1].push('');
                if (j > 0) headerRows[0].push('');
              }
            })
          }
        })
      }

    })


    if(tableName == 'Tele Stats')
    {
      headerRows[1].splice(-2);
    }

    finalHeaders = headerRows

    //normal rows
    if (tableModel.rowsToDisplay.length > 0 && tableModel.rowsToDisplay[0].data) {
      tableModel.rowsToDisplay.forEach(row => {

        let dataRow = []
        let noteRow = []

        columnFields.forEach(field => {

          let dataValue = null;
          let noteValue = null;

          if (field && field.substr(0, 2) === "!!") {
            field = field.substr(2, field.length - 2);
            let val = this.constants.getNestedItem(row.data, field)
            dataValue = !!val;
          } else {
            if (field) {

              dataValue = this.constants.getNestedItem(row.data, field)

              if (field == 'PartsStock.PercentOver6months' ||
                field == 'PartsStock.Percent6to12Months') {
                columnTypes[10] = "percent"
                columnTypes[11] = "percent"
              }

            }
          }

          if (Array.isArray(dataValue)) dataValue = dataValue.length
          if(dataValue == null || dataValue.isNaN){ dataValue = 0; }

          if(tableName == 'Fleet Orderbook' && field == 'SparkComments')
          {
            let commentValue: string = this.formatFleetOrderbookComment(dataValue);
            dataValue = commentValue;

            if(commentValue.length >= 1)
            {
              noteValue = this.formatFleetOrderbookCommentNote(commentValue);
            }
            
          }

          dataRow.push(dataValue);
          noteRow.push(noteValue);
        })

        finalRows.push(dataRow);
        notes.push(noteRow);
      })
    } 
    // Group rows
    else {
      tableModel.rowsToDisplay.forEach(group => {
        group.allLeafChildren.forEach(row => {

          let dataRow = []
          let noteRow = []

          columnFields.forEach(field => {
            if (typeof field === 'undefined') return;

            let dataValue = null;
            let noteValue = null;

            if (field && field.substr(0, 2) === "!!") {
              field = field.substr(2, field.length - 2);
              let val = this.constants.getNestedItem(row.data, field)
              dataValue = !!val;
            } else {
              if (field) {

                dataValue = this.constants.getNestedItem(row.data, field)

                if (field == 'PartsStock.PercentOver6months' ||
                  field == 'PartsStock.Percent6to12Months') {
                  columnTypes[10] = "percent"
                  columnTypes[11] = "percent"
                }

              }
            }

            if (Array.isArray(dataValue)) dataValue = dataValue.length
            if(dataValue == null || dataValue.isNaN){ dataValue = 0; }

            if(tableName == 'Fleet Orderbook' && field == 'SparkComments')
            {
              let commentValue: string = this.formatFleetOrderbookComment(dataValue);
              dataValue = commentValue;

              if(commentValue.length >= 1)
              {
                noteValue = this.formatFleetOrderbookCommentNote(commentValue);
              }
              
            }

            dataRow.push(dataValue);
            noteRow.push(noteValue);
          })

          finalRows.push(dataRow);
          notes.push(noteRow);
        })
      })
    }

    //pinned bottom rows
    tableModel.gridOptionsService.api.rowRenderer.pinnedRowModel.pinnedBottomRows.forEach(row => {
      let dataRow = []

      columnFields.forEach(field => {
        if (typeof field === 'undefined') return;
        
        let dataValue = null;

        if (field) { dataValue = this.constants.getNestedItem(row.data, field) }

        if (Array.isArray(dataValue)) dataValue = dataValue.length

        // if (!dataValue) return;
        if (dataValue == null || dataValue.isNaN) { dataValue = 0; }
        dataRow.push(dataValue)
      })

      finalFooters.push(dataRow);
    })

    let initialColumnsToHighlight = providedInitialColumnsToHighlight || 1

    let sheetDataToExtract: SheetToExtract[] = [];

    sheetDataToExtract.push({ headers: finalHeaders, rows: finalRows, footers: finalFooters, tableName: tableName, columnWidths: columnWidths, columnTypes: columnTypes, initialColumnsToHighlight: initialColumnsToHighlight, notes: notes });

    if (doNotExportJustReturn) {
      return sheetDataToExtract;
    } else {
      this.exportSheetsToExcel(sheetDataToExtract)
    }

  }

  formatFleetOrderbookCommentNote(comment: string): string
  {
    return 'Spark Comment History:\n' + comment.replace(/\|/g, "\n");
  }

  formatFleetOrderbookComment(inputString: string): string
  {
    // Split the result based on '^#^'
    let finalArray = inputString.split('^#^');

    let result = "";

    let commentCount = 1;

    // Each one of these is a comment
    finalArray.forEach(element => {

      // Split the string at each '|' character
      let parts = element.split('|');

      // Remove the first part (everything before the first '|')
      let removeStart = parts.slice(1).join('|');

      element = this.removeFirstCharIfPipe(removeStart); // Need to do this if second comment

      if(commentCount > 1)
      {
        result += '|';
      }
      
      // If there is a comment, should always split into x3 parts
      if(parts.length > 2)
      {
        // Get the first part of the split string, which is the date
        let dateString = parts[1];

        result += parts[2]; // Comment
        result += ' - '
        result += this.removeAfterSymbol(parts[3]); // Name
        result += ' '
        result += this.formatDate(dateString); // Date
        result += ' '
        
      }

      commentCount += 1;
    });

    return result;
  }

  removeFirstCharIfPipe(inputString: string): string {
    // Check if the first character is '|'
    if (inputString.startsWith('|')) {
        // Remove the first character
        return inputString.slice(1);
    }
    // Return the original string if the first character is not '|'
    return inputString;
  }

  formatDate(inputString: string): string {

    if(inputString == null || inputString.length < 1){ return ''; }

    //console.log(inputString, "inputString to format")

    // Manually parse the date string
    let [datePart, timePart] = inputString.split(' ');
    let [year, month, day] = datePart.split('-').map(Number);
    let [hours, minutes] = timePart.split(':').map(Number);

    // Define an array of month names
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    // Get the last two digits of the year
    let shortYear = year % 100;

    // Convert 24-hour time to 12-hour format
    let ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    let formattedMinutes = minutes < 10 ? '0' + minutes.toString() : minutes.toString();

    // Format the date string
    return `${day} ${monthNames[month - 1]} ${shortYear} ${hours}:${formattedMinutes}${ampm}`;
  }

  removeAfterSymbol(inputString: string): string {
    // Split the string at '^#^' and take the first part
    let parts = inputString.split('^#^');
    return parts[0];
  }

  createSheetObjectLiveForecastDownload(dataRows: any[], tableName: string) {

    try {
      const workbook = new excelJS.Workbook();
      const worksheet = workbook.addWorksheet(tableName);

      worksheet.views = [
        { state: 'frozen', xSplit: 1, ySplit: 3, zoomScale: 85 }
      ];

      worksheet.columns = new Array(10).fill({ width: 45 });

      worksheet.getRow(1).font = { name: 'Calibri', family: 4, size: 12, bold: true, fill: { bgColor: { rgb: '220,230,241' } } };
      worksheet.getCell('A1').value = tableName;

      const extractedDateTime = new Date().toString();
      worksheet.getCell('D1').value = 'Extracted ' + extractedDateTime.substring(0, 24);
      worksheet.getCell('D1').font = { italic: true, size: 12 };

      worksheet.getCell('A3').value = 'Site';
      worksheet.getCell('B3').value = 'Department';
      worksheet.getCell('C3').value = 'Account';

      worksheet.getRow('3').font = { name: 'Calibri', bold: true, size: 12 }

      const longestArrayIndex = dataRows
        .map(row => row.ForecastValues.length)
        .indexOf(Math.max(...dataRows.map(row => row.ForecastValues.length)));

      const forecastCount = dataRows[longestArrayIndex].ForecastValues.length;

      let cell: string = 'D';

      for (let k = 0; k < forecastCount; k++) {
        worksheet.getCell(cell + '3').value = dataRows[longestArrayIndex].ForecastValues[k].ForecastLabel;
        worksheet.getCell(cell + '3').font = { bold: true, size: 12, color: { argb: "FF0000" } };
        cell = String.fromCharCode(cell.charCodeAt(0) + 1).toUpperCase();
      }

      // Do the main rows
      const defaultFont = { name: 'Calibri', size: 11 };
      const defaultFill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'fffff2cc' } };
      const defaultNumFmt = '#,##0;-#,##0;-';
      const defaultAlignment = { horizontal: 'right', vertical: 'middle' };

      let i = 0;
      let startRow = 4;

      dataRows.forEach(element => {
        const row = worksheet.getRow(startRow + i);
        row.font = defaultFont;

        const siteCell = row.getCell(1);
        siteCell.fill = defaultFill;
        siteCell.value = element.Site;

        const departmentCell = row.getCell(2);
        departmentCell.fill = defaultFill;
        departmentCell.value = element.Department;

        const accountCell = row.getCell(3);
        accountCell.fill = defaultFill;
        accountCell.value = element.Account;

        for (let j = 0; j < forecastCount; j++) {
          const forecast = dataRows[longestArrayIndex].ForecastValues[j].ForecastLabel;
          const forecastForEntry = element.ForecastValues.filter(x => x.ForecastLabel == forecast)[0];

          const forecastCell = row.getCell(4 + j);
          forecastCell.fill = defaultFill;
          forecastCell.value = forecastForEntry != null ? forecastForEntry.Value : 0;
          forecastCell.numFmt = defaultNumFmt;
          forecastCell.alignment = defaultAlignment;
        }

        i = i + 1;
      });

      workbook.xlsx.writeBuffer().then((data) => {
        let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        fs.saveAs(blob, tableName + '.xlsx');
      });

    }
    catch (err) {
      console.log(err, "err!")
    }


  }

  exportSheetsToExcel(sheets: SheetToExtract[]) {

    let workbook = new excelJS.Workbook();

    var cphLogo = workbook.addImage({
      base64: this.seed.provideSparkLogo(),
      extension: 'png'
    });

    sheets.forEach(sheet => {

      try {
        //define worksheet
        let worksheet = workbook.addWorksheet(sheet.tableName)

        //generic stuff for worksheet
        worksheet.views = [
          { state: 'frozen', xSplit: 1, ySplit: 3 + sheet.headers.length, zoomScale: 85 }
        ];

        //columns things
        let columns = []

        sheet.columnWidths.forEach(w => {
          columns.push({ width: w })
        })

        worksheet.columns = columns;


        //rows
        let titleRow = worksheet.addRow([sheet.tableName])//title
        titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

        worksheet.getCell('A2').value = 'extracted ' + this.cphPipe.transform(new Date(), 'dateShortYear', 0) + ' ' + this.cphPipe.transform(new Date(), 'shortTimeAM', 0);
        worksheet.getCell('A2').font = { italic: true }
        worksheet.addRow([]);//blank

        //the table headerRow   
        let rowsSoFar = worksheet.rowCount + 1

        sheet.headers.forEach((headerRow, i) => {
          let row = worksheet.getRow(rowsSoFar + i);
        
          headerRow.forEach((cellValue, j) => {

            let cell = row.getCell(j + 1);
        
            // Apply formatting to the cell
            cell.value = cellValue;
            cell.alignment = { vertical: 'middle', horizontal: 'left' };
            cell.font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } };
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF02373b' } };
        
            // These special subheaders need to be right-aligned
            if (cellValue === 'Q' || cellValue === '£') {
              cell.alignment = { vertical: 'middle', horizontal: 'right' };
            }
          });
        });

        if(sheet.tableName == this.constants.translatedText.UsageReport_Title)
        {
          // merge required cells
          sheet.headers.forEach((headerRow, i) => {
            let row = worksheet.getRow(rowsSoFar + i);
          
            headerRow.forEach((cellValue, j) => {

              // Only merge cells for the first set of headerRows
              if (i === 0 && (cellValue === 'Last week' || cellValue === 'This week' || cellValue === 'Total Daily Logins By Week' || cellValue.includes('w/c'))) {
                worksheet.mergeCells(row.number, j + 1, row.number, j + 3); // Merge the cells
              } 
        
            });
          });
          
        }

        let currencySymbol: string = this.constants.environment.displayCurrencySymbol;

        //add the data rows
        rowsSoFar = worksheet.rowCount + 1

        sheet.rows.forEach((rowData, i) => {

          let row = worksheet.getRow(rowsSoFar + i)

          let notes = null;
          
          if(sheet.notes)
          {
            notes = sheet.notes[i] != undefined && sheet.notes[i] != null ? sheet.notes[i] : null;
          }

          //loop through each cell
          for (let j = 0; j < sheet.columnTypes.length; j++) { //loop through the columnTypes which effectively lets you walk across the columns

            let cell = row.getCell(j + 1)
            //format and value
            let format = sheet.columnTypes[j] //pick the format based on the columnType
            this.formatCell(format, cell, rowData, j, currencySymbol, notes);      
          }

        })

        //same for footers
        rowsSoFar = worksheet.rowCount + 1
        sheet.footers.forEach((footer, i) => {
          let row = worksheet.getRow(rowsSoFar + i)
          //loop through each cell
          for (let j = 0; j < sheet.columnTypes.length; j++) { //loop through the columnTypes which effectively lets you walk across the columns
            let cell = row.getCell(j + 1)

            cell.font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF02373b' } }
            //format
            let format = sheet.columnTypes[j] //pick the format based on the columnType
            this.formatCell(format, cell, footer, j, currencySymbol, null);

            row.height = 30;
            row.alignment = { vertical: 'middle' }

          }

        })



        //loop through the initial nth cols of each row and colour
        let rowCount = worksheet.rowCount + 1;
        for (let i = 4; i < rowCount; i++) {
          let row = worksheet.getRow(i)
          for (let j = 0; j < sheet.initialColumnsToHighlight; j++) {
            let cell = row.getCell(j + 1)
            cell.font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF02373b' } }
            cell.alignment = { horizontal: 'left', vertical: 'middle' }

          }

        }

        //images
        let columnCount = worksheet.columns.length
        worksheet.addImage(cphLogo, {
          tl: { col: columnCount - 1, row: 0 }, //tl = top left
          //br: {col: 13, row: 2},
          ext: { width: 90, height: 36 },
          editAs: 'absolute'
        });

      }
      catch (e) {
        //carry on?
      }


    })


    let workbookName = 'Spark ' + new Date().getDate() + new Date().toLocaleString(this.constants.translatedText.LocaleCode, { month: 'short' }) + new Date().getFullYear();
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, workbookName + '.xlsx');
    });



    //XLSX.writeFile(workbook, 'my_file.xls', { bookType: 'xls', type: 'buffer' });
  }

  private formatCell(format: string, cell: any, rowData: any[], j: number, currencySymbol: string, notes: any[]) {

    let note: string = '';
    let noteTest: any = null;
    
    try
    {
      if(notes != null && notes.length > j)
      {
        noteTest = notes[j];

        if(noteTest == 0)
        {
          note = undefined;
        }
        else if(noteTest == null)
        {
          note = undefined;
        }
        else if(typeof noteTest !== "string")
        {
          note = undefined;
        }
        else
        {
          note = noteTest;
          cell.note = noteTest;
        }
      }
      else
      {
        note = undefined;
      }
      
    }
    catch(e)
    {
      console.log(e, "error!")
    }

    try
    {
      switch (format) {
        case ('number'):
        case ('numberWithColour'):
        case ('numberWithPlusMinusAndColour'):
        case ('numberWithPlusMinusAndColourSwitch'):
        case ('numberWithPlusMinus'):
        case ('numberEditable'):
        case ('numberEditableNoCommas'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = 0;
          cell.numFmt = '#,##0;-#,##0;-';
          cell.alignment = { horizontal: 'right' };
          break;
        }
        case ('number1dp'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = 0;
          cell.numFmt = '#,##0.0;-#,##0.0;-';
          cell.alignment = { horizontal: 'right' };
          break;
        }
        case ('number2dp'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = 0;
          cell.numFmt = '#,##0.00;-#,##0.00;-';
          cell.alignment = { horizontal: 'right' };
          break;
        }

        case ('dateShort'):
        case ('dateYearAndTime'):
        case ('dateShortYear'):
        case ('dateLongYear'):
        case ('dateEditable'):
        case ('date'): {
  
          try {
            if (!!rowData[j]) {
              cell.value = new Date(rowData[j])
            } else {
              cell.value = '';
            }
          } catch {
            cell.value = '';
          }
          cell.alignment = { horizontal: 'left' };
          break;
        }

        case ('currency'):
        case ('currencyWithPlusMinus'):
        case ('currencyWithFontColour'):
        case ('currencySetFilter'):
        case ('currencyFlipColour'):
        case ('currencyLiveForecast'):
        case ('currencyWithPrefix'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = 0;
          cell.numFmt = `\\${currencySymbol}#,##0;-\\${currencySymbol}#,##0;-`;
          cell.alignment = { horizontal: 'right' };
          break;
        }
        case ('currency2dp'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = 0;
          cell.numFmt = `\\${currencySymbol}#,##0.00;-\\${currencySymbol}#,##0.00;-`;
          cell.alignment = { horizontal: 'right' };
          break;
        }



        case ('percent'):
        case ('percentNoRenderer'):
        case ('percentWithColour'):
        case ('percentWithPlusMinus'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = 0;
          cell.numFmt = '0%';
          cell.alignment = { horizontal: 'right' };
          break;
        } //style the cell
        case ('percent1dp'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = 0;
          cell.numFmt = '0.0%';
          cell.alignment = { horizontal: 'right' };
          break;
        } //style the cell
        case ('percent1dp'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = 0;
          cell.numFmt = '0.00%';
          cell.alignment = { horizontal: 'right' };
          break;
        } //style the cell

        case ('time'): {
          cell.value = parseFloat(rowData[j]);
          if (isNaN(cell.value)) cell.value = '-';
          else { cell.value = this.cphPipe.transform(cell.value, 'timeDuration', 0) }
          cell.alignment = { horizontal: 'left' };
          break;
        }



        case ('booleanWithFloatingFilter'):
          case ('booleanEditable'):
          case ('boolean'): {
          cell.value = rowData[j] ? true : false;
          break;
        }



        case ('labelSetFilter'):
        case ('labelLowPad'):
        case ('labelWrap'):
        case ('labelNoZeroes'):
        case ('labelCentre'):
        case ('label'):
          cell.value = rowData[j] == 0 ? null : rowData[j];
          cell.alignment = { horizontal: 'left' };
          break;
        default: {
          cell.value = rowData[j] == 0 ? null : rowData[j];
          cell.alignment = { horizontal: 'center' };
        }

      }
    }
    catch(e)
    {
      console.log(e, "error!")

    }


  }

  exportSimpleSheetToExcel(sheet: SimpleSheet) {
    let workbook = new excelJS.Workbook();

    var cphLogo = workbook.addImage({
      base64: this.seed.provideSparkLogo(),
      extension: 'png'
    });

    try {
      //define worksheet
      let worksheet = workbook.addWorksheet(sheet.tableName)

      //generic stuff for worksheet
      worksheet.views = [
        { state: 'frozen', xSplit: 1, ySplit: 3, zoomScale: 85 }
      ];

      //columns things
      let columns = []
      sheet.columnWidths.forEach(w => {
        columns.push({ width: w })
      })
      worksheet.columns = columns;


      //rows
      let titleRow = worksheet.addRow([sheet.tableName])//title
      titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };
      worksheet.addRow([]);//blank


      //the table headerRow      
      let tableHeaderRow = worksheet.addRow(Object.keys(sheet.tableData[0]))
      let colCount = Object.keys(sheet.tableData[0]).length

      //loop through each column in active range and colour cells
      for (let i = 0; i < colCount; i++) {
        let colLetter = String.fromCharCode(65 + i)
        worksheet.getCell(colLetter + '3').font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell(colLetter + '3').fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF02373b' } }
      }

      //the data rows
      sheet.tableData.forEach(x => {
        let newRow = worksheet.addRow(Object.values(x))
      })

      //loop through the first cell of each row and colour
      let rowCount = worksheet.rowCount + 1;
      for (let i = 4; i < rowCount; i++) {
        worksheet.getCell('A' + i.toString()).font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell('A' + i.toString()).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF02373b' } }
      }

      //images
      let columnCount = worksheet.columns.length
      worksheet.addImage(cphLogo, {
        tl: { col: columnCount - 1, row: 0 }, //tl = top left
        //br: {col: 13, row: 2},
        ext: { width: 90, height: 36 },
        editAs: 'absolute'
      });
    }
    catch (e) {
      //carry on
    }

    let workbookName = 'Spark Extract ' + new Date().getDate() + new Date().toLocaleString(this.constants.translatedText.LocaleCode, { month: 'short' }) + new Date().getFullYear();
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, workbookName + '.xlsx');
    });
  }






}