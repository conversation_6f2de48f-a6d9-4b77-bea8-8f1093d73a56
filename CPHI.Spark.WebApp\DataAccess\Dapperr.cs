﻿using Microsoft.Data.SqlClient;
using Dapper;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Repository;
using Microsoft.EntityFrameworkCore;
using CPHI.Spark.Model;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.DataAccess
{

    public interface IDapperr : IDisposable
    {
        //DbConnection GetDbconnection();

        //DataTable GetDataTable(string query, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        T Get<T>(string sp, DynamicParameters parms, DealerGroupName dealerGroup, CommandType commandType = CommandType.StoredProcedure);
        Task<T> GetAsync<T>(string sp, DynamicParameters parms, DealerGroupName dealerGroup, CommandType commandType = CommandType.StoredProcedure);

        //Task<IEnumerable<T>> GetAllAsync<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        Task<int> ExecuteAsync(string sp, DynamicParameters parms, DealerGroupName dealerGroup, CommandType commandType = CommandType.StoredProcedure);
        //T Insert<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        //Task<T> InsertAsync<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);

        //Task<T> InsertAsync<T>(string sp, DynamicParameters parms, IDbTransaction tran, IDbConnection db, CommandType commandType = CommandType.StoredProcedure);
        //T Update<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        Task<IEnumerable<T>> GetAllAsync<T>(string sp, DynamicParameters parms, DealerGroupName dealerGroup, CommandType commandType = CommandType.StoredProcedure, int? timeout = null);

        //Task<SearchResult> GetMultipleAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        Task<IEnumerable<T>> GetAllAsyncForStockpulse<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
    }

    public class Dapperr : IDapperr
    {
        private readonly IConfiguration _config;
        private readonly CPHIDbContext db;

        public Dapperr(IConfiguration config, CPHIDbContext db)
        {
            _config = config;
            this.db = db;
        }
        public void Dispose()
        {

        }
     



        public async Task<int> ExecuteAsync(string sp, DynamicParameters parms, DealerGroupName dealerGroup, CommandType commandType = CommandType.StoredProcedure)
        {
            string conString = _config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(dealerGroup));
            using IDbConnection db = new SqlConnection(conString);
            return await db.ExecuteAsync(sp, parms, commandType: commandType);
        }

        public T Get<T>(string sp, DynamicParameters parms, DealerGroupName dealerGroup, CommandType commandType = CommandType.Text)
        {
            string conString = _config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(dealerGroup));
            using IDbConnection db = new SqlConnection(conString);
            return db.Query<T>(sp, parms, commandType: commandType).FirstOrDefault();
        }

        public async Task<T> GetAsync<T>(string sp, DynamicParameters parms, DealerGroupName dealerGroup, CommandType commandType = CommandType.StoredProcedure)
        {
            string conString = _config.GetConnectionString(DealerGroupConnectionName.GetConnectionName(dealerGroup));
            using IDbConnection db = new SqlConnection(conString);
            return await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType);
        }


        public async Task<IEnumerable<T>> GetAllAsync<T>(string sp, DynamicParameters parms, DealerGroupName dealerGroup, CommandType commandType = CommandType.StoredProcedure, int? timeout = null)
        {
            string dg = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string conString = _config.GetConnectionString(dg);
            using IDbConnection db = new SqlConnection(conString);
            var commandTimeout = timeout.HasValue ? timeout.Value : 30;
            return await db.QueryAsync<T>(sp, parms, commandType: commandType, commandTimeout: commandTimeout);
        }

        public async Task<IEnumerable<T>> GetAllAsyncForStockpulse<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            string conString = _config.GetConnectionString("StockTakePhotos");
            using IDbConnection db = new SqlConnection(conString);
            return await db.QueryAsync<T>(sp, parms, commandType: commandType);
        }

        /*
        public DbConnection GetDbconnection()
        {
            return new SqlConnection(this.db.Database.GetDbConnection().ConnectionString);
        }

        public async Task<T> InsertAsync<T>(string sp, DynamicParameters parms, IDbTransaction tran, IDbConnection db, CommandType commandType = CommandType.StoredProcedure)
        {
            return await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType, transaction: tran);
        }

        public T Insert<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            T result;
            using IDbConnection db = new SqlConnection(this.db.Database.GetConnectionString());
            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = db.Query<T>(sp, parms, commandType: commandType, transaction: tran).FirstOrDefault();
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }
        public async Task<T> InsertAsync<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            T result;
            using IDbConnection db = new SqlConnection(this.db.Database.GetConnectionString());
            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType, transaction: tran);
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }

        public T Update<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        {
            T result;
            using IDbConnection db = new SqlConnection(this.db.Database.GetConnectionString());
            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = db.Query<T>(sp, parms, commandType: commandType, transaction: tran).FirstOrDefault();
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }
        */
    }
}
