﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using CPHI.Spark.Model.Import;

namespace CPHI.Spark.Model
{
   public class Deal
   {
      public Deal() { }
      public Deal(Deal_JJPremium deal)
      {
         Id = deal.Id;
         Reg = deal.Reg;
         Customer = deal.Customer;
         Description = deal.Description;
         LastPhysicalLocation = deal.LastPhysicalLocation;
         OrderDate = deal.OrderDate;
         InvoiceDate = deal.InvoiceDate;
         AccountingDate = deal.AccountingDate;
         StockDate = deal.StockDate;
         IsInvoiced = deal.IsInvoiced;
         InvoiceNo = deal.InvoiceNo;
         Salesman_Id = deal.Salesman_Id;
         Sale = deal.Sale;
         CoS = deal.CoS;
         VatCost = deal.VatCost;
         MechPrep = deal.MechPrep;
         AccessoriesSale = deal.AccessoriesSale;
         OemDeliverySale = deal.OemDeliverySale;
         IsFinanced = deal.IsFinanced;
         HasWarranty = deal.HasWarranty;
         HasPaintProtection = deal.HasPaintProtection;
         FinanceCommission = deal.FinanceCommission;
         WarrantyCost = deal.WarrantyCost;
         PaintProtectionAccessoryCost = deal.PaintProtectionAccessoryCost;
         TotalNLProfit = deal.TotalNLProfit;
         Franchise_Id = deal.Franchise_Id;
         Units = deal.Units;
         StockNumber = deal.StockNumber;

         //FK notation
         Site_Id = deal.Site_Id;
         VehicleClass_Id = deal.VehicleClass_Id;
         Franchise_Id = deal.Franchise_Id;
         OrderType_Id = deal.OrderType_Id;
         VehicleType_Id = deal.VehicleType_Id;
         DeliverySite_Id = deal.DeliverySite_Id;
         VehicleAdvert_Id = deal.VehicleAdvert_Id;
      }

      public Deal(Deal_CarCo deal)
      {
         Id = deal.Id;
         Reg = deal.Reg;
         Customer = deal.Customer;
         Description = deal.Description;
         LastPhysicalLocation = deal.LastPhysicalLocation;
         OrderDate = deal.OrderDate;
         InvoiceDate = deal.InvoiceDate;
         AccountingDate = deal.AccountingDate;
         StockDate = deal.StockDate;
         IsInvoiced = deal.IsInvoiced;
         InvoiceNo = deal.InvoiceNo;
         Salesman_Id = deal.Salesman_Id;
         Sale = deal.Sale;
         CoS = deal.CoS;
         VatCost = deal.VatCost;
         MechPrep = deal.MechPrep;
         AccessoriesSale = deal.AccessoriesSale;
         OemDeliverySale = deal.OemDeliverySale;
         IsFinanced = deal.IsFinanced;
         HasWarranty = deal.HasWarranty;
         HasPaintProtection = deal.HasPaintProtection;
         FinanceCommission = deal.FinanceCommission;
         WarrantyCost = deal.WarrantyCost;
         PaintProtectionAccessoryCost = deal.PaintProtectionAccessoryCost;
         TotalNLProfit = deal.TotalNLProfit;
         Franchise_Id = deal.Franchise_Id;
         Units = deal.Units;
         StockNumber = deal.StockNumber;
         EnquiryNumber = deal.EnquiryNumber;
         IsRemoved = deal.IsRemoved;
         CreatedDate = deal.CreatedDate;
         IsFinanced = deal.IsFinanced;
         VehicleClass_Id = deal.VehicleClass_Id;

         Model = deal.Model;
         ModelYear = deal.ModelYear;
         VehicleAge = deal.VehicleAge;
         Variant = deal.Variant;
         VariantTxt = deal.VariantTxt;
         RegisteredDate = deal.RegisteredDate;

         IsLateCost = deal.IsLateCost;
         IsDelivered = deal.IsDelivered;
         ActualDeliveryDate = deal.ActualDeliveryDate;
         OemReference = deal.OemReference;
         FinanceCo = deal.FinanceCo;
         FinanceType = deal.FinanceType;
         IsClosed = deal.IsClosed;
         AuditPass = deal.AuditPass;
         HandoverDate = deal.HandoverDate;
         VehicleSource = deal.VehicleSource;
         FinanceCo = deal.FinanceCo;

         Discount = deal.Discount;
         PartExOverAllowance1 = deal.PartExOverAllowance1;
         NewBonus1 = deal.NewBonus1;
         NewBonus2 = deal.NewBonus2;

         AccessoriesCost = deal.AccessoriesCost;
         FuelSale = deal.FuelSale;
         FuelCost = deal.FuelCost;
         BrokerCost = deal.BrokerCost;
         IntroCommission = deal.IntroCommission;
         OemDeliveryCost = deal.OemDeliveryCost;
         PDICost = deal.PDICost;
         BodyPrep = deal.BodyPrep;
         Other = deal.Other;
         Error = deal.Error;

         FinanceSubsidy = deal.FinanceSubsidy;
         SelectCommission = deal.SelectCommission;
         RCIFinanceCommission = deal.RCIFinanceCommission;
         StandardsCommission = deal.StandardsCommission;
         ProPlusCommission = deal.ProPlusCommission;

         OrigOrderDate = deal.OrigOrderDate;
         OrigDeliveryDate = deal.OrigDeliveryDate;
         WhenNew = deal.WhenNew;
         LastUpdated = deal.LastUpdated;
         OriginalSource = deal.OriginalSource;

         ModelCode = deal.ModelCode;
         MarketCode = deal.MarketCode;
         SegmentCode = deal.SegmentCode;
         StatusCode = deal.StatusCode;
         ChannelVoDescription = deal.ChannelVoDescription;
         TypeVoDescription = deal.TypeVoDescription;
         PaintProtectionSale = deal.PaintProtectionSale;
         WarrantySale = deal.WarrantySale;
         WarrantyCost = deal.WarrantyCost;
         HasWheelGuard = deal.HasWheelGuard;
         WheelGuardSale = deal.WheelGuardSale;
         WheelGuardCost = deal.WheelGuardCost;
         WheelGuardCommission = deal.WheelGuardCommission;
         HasTyreInsurance = deal.HasTyreInsurance;
         TyreInsuranceSale = deal.TyreInsuranceSale;
         TyreInsuranceCost = deal.TyreInsuranceCost;
         TyreInsuranceCommission = deal.TyreInsuranceCommission;
         HasAlloyInsurance = deal.HasAlloyInsurance;
         AlloyInsuranceSale = deal.AlloyInsuranceSale;
         AlloyInsuranceCost = deal.AlloyInsuranceCost;
         AlloyInsuranceCommission = deal.AlloyInsuranceCommission;
         HasTyreAndAlloyInsurance = deal.HasTyreAndAlloyInsurance;
         TyreAndAlloyInsuranceSale = deal.TyreAndAlloyInsuranceSale;
         TyreAndAlloyInsuranceCost = deal.TyreAndAlloyInsuranceCost;
         TyreAndAlloyInsuranceCommission = deal.TyreAndAlloyInsuranceCommission;

         // Other Profit
         AccessoriesSale = deal.AccessoriesSale;
         AccessoriesCost = deal.AccessoriesCost;
         FuelSale = deal.FuelSale;
         FuelCost = deal.FuelCost;
         BrokerCost = deal.BrokerCost;
         IntroCommission = deal.IntroCommission;
         OemDeliverySale = deal.OemDeliverySale;
         OemDeliveryCost = deal.OemDeliveryCost;
         PDICost = deal.PDICost;
         MechPrep = deal.MechPrep;
         BodyPrep = deal.BodyPrep;
         Other = deal.Other;
         Error = deal.Error;
         StandardWarrantyCost = deal.StandardWarrantyCost;
         HasPaintProtectionAccessory = deal.HasPaintProtectionAccessory;
         PaintProtectionAccessorySale = deal.PaintProtectionAccessorySale;
         PaintProtectionAccessoryCost = deal.PaintProtectionAccessoryCost;

         // Finance Profit
         FinanceCommission = deal.FinanceCommission;
         FinanceSubsidy = deal.FinanceSubsidy;
         SelectCommission = deal.SelectCommission;
         RCIFinanceCommission = deal.RCIFinanceCommission;
         StandardsCommission = deal.StandardsCommission;
         ProPlusCommission = deal.ProPlusCommission;

         // Addon Profit
         HasCosmeticInsurance = deal.HasCosmeticInsurance;
         CosmeticInsuranceSale = deal.CosmeticInsuranceSale;
         CosmeticInsuranceCost = deal.CosmeticInsuranceCost;
         CosmeticInsuranceCommission = deal.CosmeticInsuranceCommission;
         HasGapInsurance = deal.HasGapInsurance;
         GapInsuranceSale = deal.GapInsuranceSale;
         GapInsuranceCost = deal.GapInsuranceCost;
         GapInsuranceCommission = deal.GapInsuranceCommission;
         HasPaintProtection = deal.HasPaintProtection;
         PaintProtectionSale = deal.PaintProtectionSale;
         PaintProtectionCost = deal.PaintProtectionCost;
         HasServicePlan = deal.HasServicePlan;
         ServicePlanSale = deal.ServicePlanSale;
         ServicePlanCost = deal.ServicePlanCost;

         HasWarranty = deal.HasWarranty;
         HasShortWarranty = deal.HasShortWarranty;
         HandoverDate = deal.HandoverDate;
         VehicleSource = deal.VehicleSource;

         // Metal Profit
         Sale = deal.Sale;
         Discount = deal.Discount;
         CoS = deal.CoS;
         PartExOverAllowance1 = deal.PartExOverAllowance1;
         VatCost = deal.VatCost;
         NewBonus1 = deal.NewBonus1;
         NewBonus2 = deal.NewBonus2;
         IsDelivered = deal.IsDelivered;


         //FK notation
         Site_Id = deal.Site_Id;
         VehicleClass_Id = deal.VehicleClass_Id;
         Franchise_Id = deal.Franchise_Id;
         OrderType_Id = deal.OrderType_Id;
         VehicleType_Id = deal.VehicleType_Id;
         DeliverySite_Id = deal.DeliverySite_Id;
         VehicleAdvert_Id = deal.VehicleAdvert_Id;
      }


      public int Id { get; set; }


      //Foreign Keys, many in this table go to 1 in that table
      public int? Salesman_Id { get; set; }
      [ForeignKey("Salesman_Id")]
      public Person Salesman { get; set; }
      public int? Site_Id { get; set; }
      [ForeignKey("Site_Id")]
      public Site Site { get; set; }
      public int VehicleClass_Id { get; set; }
      [ForeignKey("VehicleClass_Id")]
      public StandingValue VehicleClass { get; set; }
      public int Franchise_Id { get; set; }
      [ForeignKey("Franchise_Id")]
      public StandingValue Franchise { get; set; }
      public int? OrderType_Id { get; set; }
      [ForeignKey("OrderType_Id")]
      public OrderType OrderType { get; set; }
      public int VehicleType_Id { get; set; }
      [ForeignKey("VehicleType_Id")]
      public VehicleType VehicleType { get; set; }
      public int? DeliverySite_Id { get; set; }
      [ForeignKey("DeliverySite_Id")]
      public Site DeliverySite { get; set; }

      public int? VehicleAdvert_Id { get; set; }
      [ForeignKey("VehicleAdvert_Id")]
      public VehicleAdvert VehicleAdvert { get; set; }



      //FK notation, one in this table to many in other
      public virtual ICollection<Comment> Comments { get; set; }




      //Vehicle
      public string Reg { get; set; }
      public string StockNumber { get; set; }
      public string Model { get; set; }
      public int ModelYear { get; set; }
      public DateTime? StockDate { get; set; }
      public string Description { get; set; }
      public int VehicleAge { get; set; }
      public string Variant { get; set; }
      public string VariantTxt { get; set; }
      public string LastPhysicalLocation { get; set; }
      public DateTime? RegisteredDate { get; set; }




      //Order
      public bool IsRemoved { get; set; }
      public DateTime? RemovedDate { get; set; }
      public bool IsUpdated { get; set; }
      public int EnquiryNumber { get; set; }
      public string Customer { get; set; }
      public bool IsLateCost { get; set; }
      public bool IsDelivered { get; set; }
      public DateTime OrderDate { get; set; }
      public DateTime? ActualDeliveryDate { get; set; }
      public DateTime? InvoiceDate { get; set; }
      public DateTime AccountingDate { get; set; }
      public string OemReference { get; set; }
      public bool IsFinanced { get; set; }
      public string FinanceCo { get; set; }
      public string FinanceType { get; set; }
      public bool IsClosed { get; set; }
      public bool? AuditPass { get; set; }
      public int Units { get; set; }
      public DateTime? HandoverDate { get; set; }
      public string VehicleSource { get; set; }


      //Metal Profit
      public decimal Sale { get; set; }
      public decimal Discount { get; set; }
      public decimal CoS { get; set; }
      public decimal PartExOverAllowance1 { get; set; }
      public decimal VatCost { get; set; }
      public decimal NewBonus1 { get; set; }
      public decimal NewBonus2 { get; set; }


      //Other Profit
      public decimal AccessoriesSale { get; set; }
      public decimal AccessoriesCost { get; set; }
      public decimal FuelSale { get; set; }
      public decimal FuelCost { get; set; }
      public decimal BrokerCost { get; set; }
      public decimal IntroCommission { get; set; }
      public decimal OemDeliverySale { get; set; }
      public decimal OemDeliveryCost { get; set; }
      public decimal PDICost { get; set; }
      public decimal MechPrep { get; set; }
      public decimal BodyPrep { get; set; }
      public decimal Other { get; set; }
      public decimal Error { get; set; }
      public decimal StandardWarrantyCost { get; set; }
      public bool HasPaintProtectionAccessory { get; set; }
      public decimal PaintProtectionAccessorySale { get; set; }
      public decimal PaintProtectionAccessoryCost { get; set; }

      //Finance Profit
      public decimal FinanceCommission { get; set; }
      public decimal FinanceSubsidy { get; set; }
      public decimal SelectCommission { get; set; }
      public decimal RCIFinanceCommission { get; set; }
      public decimal StandardsCommission { get; set; }
      public decimal ProPlusCommission { get; set; }


      //Addon Profit
      public bool HasCosmeticInsurance { get; set; }
      public decimal CosmeticInsuranceSale { get; set; }
      public decimal CosmeticInsuranceCost { get; set; }
      public decimal CosmeticInsuranceCommission { get; set; }
      public bool HasGapInsurance { get; set; }
      public decimal GapInsuranceSale { get; set; }
      public decimal GapInsuranceCost { get; set; }
      public decimal GapInsuranceCommission { get; set; }
      public bool HasPaintProtection { get; set; }
      public decimal PaintProtectionSale { get; set; }
      public decimal PaintProtectionCost { get; set; }
      public bool HasServicePlan { get; set; }
      public decimal ServicePlanSale { get; set; }
      public decimal ServicePlanCost { get; set; }

      public bool HasWarranty { get; set; }
      public bool HasShortWarranty { get; set; }

      public decimal WarrantySale { get; set; }
      public decimal WarrantyCost { get; set; }
      public bool? HasWheelGuard { get; set; }
      public decimal WheelGuardSale { get; set; }
      public decimal WheelGuardCost { get; set; }
      public decimal WheelGuardCommission { get; set; }  //renamed
      public bool HasTyreInsurance { get; set; }
      public decimal TyreInsuranceSale { get; set; }
      public decimal TyreInsuranceCost { get; set; }
      public decimal TyreInsuranceCommission { get; set; }
      public bool HasAlloyInsurance { get; set; }
      public decimal AlloyInsuranceSale { get; set; }
      public decimal AlloyInsuranceCost { get; set; }
      public decimal AlloyInsuranceCommission { get; set; }
      public bool HasTyreAndAlloyInsurance { get; set; }
      public decimal TyreAndAlloyInsuranceSale { get; set; }
      public decimal TyreAndAlloyInsuranceCost { get; set; }
      public decimal TyreAndAlloyInsuranceCommission { get; set; }

      public decimal TotalNLProfit { get; set; }  //ok

      //Other fields to retain but not used in front end

      [StringLength(250)]
      public DateTime? OrigOrderDate { get; set; } //for late costs, order date of original deal
      public DateTime? OrigDeliveryDate { get; set; } //for late costs, delivery date of original deal

      public DateTime CreatedDate { get; set; }
      public DateTime WhenNew { get; set; }
      public bool IsInvoiced { get; set; }
      public DateTime LastUpdated { get; set; }
      public string OriginalSource { get; set; }

      public string ModelCode { get; set; }
      public string MarketCode { get; set; }
      public string SegmentCode { get; set; }
      public string StatusCode { get; set; }

      [MaxLength(50)]
      public string ChannelVoDescription { get; set; }
      [MaxLength(50)]
      public string TypeVoDescription { get; set; }
      [MaxLength(50)]
      public string InvoiceNo { get; set; }

   }
}

