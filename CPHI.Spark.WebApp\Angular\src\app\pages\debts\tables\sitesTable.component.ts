import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { CellClassParams, GridApi, GridOptions, ValueGetterParams } from 'ag-grid-community';
import { ColDefCph } from 'src/app/model/ColDefCPH';
import { ColGroupDefCPH } from 'src/app/model/ColGroupDefCPH';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../../cph.pipe';
import { DebtsSitesSummaryRow } from '../../../model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from '../../../services/agGridMethods.service';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from '../../../services/selections.service';
import { DebtsService } from '../debts.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';


//type ColumnDef = ColDefCPH | ColGroupDef;


@Component({
  selector: 'sitesTable',
  template: `
    <div id="gridHolder" [ngClass]="{ 'simpleSitesTable': this.constants.environment.debts_simpleSitesTable }">
      <div id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular id="DebtorsTable" class="ag-theme-balham" [gridOptions]="mainTableGridOptions"></ag-grid-angular>
    </div>
  `
  ,
  styleUrls: ["./../../../../styles/components/_agGrid.scss"],
  styles: [
    `
    ag-grid-angular.hidden{opacity:0}
    #gridHolder{position:relative;margin: 0 auto;width: 100%;}
    /* #gridHolder.simpleSitesTable { width: 60% !important; } */
    ag-grid-angular{    width: 100%;       margin: 0em auto; }
    
     
  `
  ]
})

export class SitesTableComponent implements OnInit {
  @Input() isRegional: boolean;
  @Output() clickedSite = new EventEmitter<DebtsSitesSummaryRow>();

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  mainTableGridOptions: GridOptionsCph;
  filterBy: string;
 

  constructor(
    public columnTypeService: ColumnTypesService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpersService: AGGridMethodsService,
    private service: DebtsService
  ) { }


  ngOnInit() {
    this.filterBy = this.isRegional ? 'regions' : 'sites';
    

    this.setGridOptions();


    this.selections.debts.sitesDataChangedEmitter.subscribe(() => {
      if (this.gridApi) {
        this.gridApi.setRowData(this.getRowData());
        this.gridApi.setPinnedBottomRowData(this.getPinnedBottomRowData());
      }
    })
  }

  setGridOptions() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.getRowData(),
      
      pinnedBottomRowData: this.getPinnedBottomRowData(),
      onGridReady: (params) => this.onGridReady(params),
      domLayout: 'autoHeight',
      getMainMenuItems:(params)=>this.gridHelpersService.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlights),
      onCellClicked: (params) => {
        this.onCellClick(params);
      },
      
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },
      columnDefs: this.getColumnDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }
  }
  getPinnedBottomRowData() {
    return this.selections.debts.siteSummaryRows.filter(x=>x.IsTotal);
  }


  // cellClassProvider(params:CellClassParams){
  //     return this.agGridMethodsService.cellClassProviderWithColourFontNew(params,this.service.topBottomHighlights);
  // }

  getRowData(){
    return this.selections.debts.siteSummaryRows.filter(x=>this.isRegional && x.IsRegion || !this.isRegional && x.IsSite)
  }


  getColumnDefs() {
    if(this.constants.environment.debts_simpleSitesTable){
      if(this.constants.environment.debts_includeBonuses ){
        return this.getDebtsAndBonusesColumns()
      }else{
        return this.getJustDebtsColumn();
      }
    }else{
      return this.getDetailedColumns();
    }
  }



  getDetailedColumns(): (ColDefCph | ColGroupDefCPH)[] {
    return [
      { headerName: '', field: 'SiteDescription', valueGetter:(params)=>this.labelGetter(params), colId: 'SiteDescription', width: 200, type: 'label' },
      {
        headerName: 'SL Sales Ledger',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'ALSLSalesLedgerAll',isFlipped:true, colId: 'ALSLSalesLedgerAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>60', field: 'ALSLSalesLedgerOver', colId: 'ALSLSalesLedgerOver',isFlipped:true, width: 95, type: 'currencyWithFontColour' }
        ]
      },
      {
        headerName: 'VS Sales Ledger',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'ALVSSalesLedgerAll',isFlipped:true, colId: 'ALVSSalesLedgerAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>14', field: 'ALVSSalesLedgerOver',isFlipped:true, colId: 'ALVSSalesLedgerOver', width: 95, type: 'currencyWithFontColour' }
        ]
      },
      {
        headerName: 'Vehicle HP',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'ALHPVehicleHPAll',isFlipped:true, colId: 'ALHPVehicleHPAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>14', field: 'ALHPVehicleHPOver',isFlipped:true, colId: 'ALHPVehicleHPOver', width: 95, type: 'currencyWithFontColour' }
        ]
      },
      {
        headerName: 'Vehicle Finance',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'ALVFVehicleFinanceAll',isFlipped:true, colId: 'ALVFVehicleFinanceAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>14', field: 'ALVFVehicleFinanceOver',isFlipped:true, colId: 'ALVFVehicleFinanceOver', width: 95, type: 'currencyWithFontColour' }
        ]
      },
      {
        headerName: 'Warranty Claims',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'ALWCWarrantyClaimsAll',isFlipped:true, colId: 'ALWCWarrantyClaimsAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>7', field: 'ALWCWarrantyClaimsOver',isFlipped:true, colId: 'ALWCWarrantyClaimsOver', width: 95, type: 'currencyWithFontColour' }
        ]
      },
      {
        headerName: 'Service Cash',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'ALS_ServiceAll',isFlipped:true, colId: 'ALS_ServiceAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>7', field: 'ALS_ServiceOver',isFlipped:true, colId: 'ALS_ServiceOver', width: 95, type: 'currencyWithFontColour' }
        ]
      },
      {
        headerName: 'Parts Cash',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'ALPCPartsCashAll',isFlipped:true, colId: 'ALPCPartsCashAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>7', field: 'ALPCPartsCashOver',isFlipped:true, colId: 'ALPCPartsCashOver', width: 95, type: 'currencyWithFontColour' }
        ]
      },
      {
        headerName: 'Total Debt',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'DebtsAll',isFlipped:true, colId: 'DebtsAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>60', field: 'DebtsOver',isFlipped:true, colId: 'DebtsOver', width: 95, type: 'currencyWithFontColour' }
        ]
      },
      {
        headerName: 'Vehicle Deposits',
        children: [
          { headerName: this.constants.translatedText.Total, field: 'ALVDVehicleDepositAll',isFlipped:true, colId: 'ALVDVehicleDepositAll', width: 95, type: 'currencyWithFontColour' },
          { headerName: '>7', field: 'ALVDVehicleDepositOver',isFlipped:true, colId: 'ALVDVehicleDepositOver', width: 95, type: 'currencyWithFontColour' }
        ]
      }
    ]
  }
  labelGetter(params: ValueGetterParams): any {
    const row: DebtsSitesSummaryRow = params.data;
    if(row.IsTotal){return this.constants.translatedText.Total}
    if(row.IsRegion){return row.RegionDescription;}
    return row.SiteDescription;
  }


  getJustDebtsColumn() {
    return [
      { headerName: '', field: 'SiteDescription', valueGetter:(params)=>this.labelGetter(params), colId: 'SiteDescription', width: 200, type: 'label' },
      {
        headerName: this.constants.translatedText.Dashboard_Debtors_DebtsAging,
        children: [
          { headerName: this.constants.translatedText.Dashboard_Debtors_TotalDebts, field: 'OtherDebtAll',isFlipped:true, colId: 'OtherDebtAll', width: 95, type: 'currency' },
          { headerName: this.constants.translatedText.Dashboard_Debtors_OfWhichOver30Days, field: 'OtherDebtOver',isFlipped:true, colId: 'OtherDebtOver', width: 95, type: 'currency' },
        ]
      },
    ]
  }




  getDebtsAndBonusesColumns() {
    return [
      { headerName: '', field: 'SiteDescription', valueGetter:(params)=>this.labelGetter(params), colId: 'SiteDescription', width: 200, type: 'label' },
      {
        headerName: this.constants.translatedText.Dashboard_Debtors_DebtsAging,
        children: [
          { headerName: this.constants.translatedText.Dashboard_Debtors_TotalDebts, field: 'OtherDebtAll', isFlipped:true, colId: 'OtherDebtAll', width: 95, type: 'currency' },
          { headerName: this.constants.translatedText.Dashboard_Debtors_OfWhichOver30Days, field: 'OtherDebtOver',isFlipped:true, colId: 'OtherDebtOver', width: 95, type: 'currency' },
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_Debtors_BonusesAging,
        children: [
          { headerName: this.constants.translatedText.Dashboard_Debtors_TotalBonuses, field: 'BonusAll',isFlipped:true, colId: 'BonusAll', width: 95, type: 'currency' },
          { headerName: this.constants.translatedText.Dashboard_Debtors_OfWhichOver60Days, field: 'BonusOver',isFlipped:true, colId: 'BonusOver', width: 95, type: 'currency' }
        ]
      }
    ]
  }


  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }
  }

  onCellClick(params) {
    this.clickedSite.next(params.node.data);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };
    this.selections.triggerSpinner.next({ show: false });
  }

  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, `Debts Site Summary - Aged at ${this.selections.debts.ageAtMonthEnd ? 'month end' : 'now'}`, 1, 1);
  }
}
