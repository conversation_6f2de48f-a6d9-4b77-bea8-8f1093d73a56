﻿using CPHI.Spark.Model;
using System.Data;
using CPHI.Repository;
using Microsoft.EntityFrameworkCore;
using Azure;

namespace CPHI.Spark.DataAccess
{
    public interface IApiCallLogsDataAccess
    {
        Task<ApiCallLog> AddDeals(DealerGroupName dealerGroup, string url, string request);
        Task<List<ApiCallLog>> GetPendingDeals(DealerGroupName dealerGroup);
        Task UpdateApiCallLog(DealerGroupName dealerGroupName, ApiCallLog newApiCallLog);
    }

    public class ApiCallLogsDataAccess : IApiCallLogsDataAccess
    {
        private readonly string _connectionString;


        public ApiCallLogsDataAccess(string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task<List<ApiCallLog>> GetPendingDeals(DealerGroupName dealerGroup)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                return await db.ApiCallLogs.Where(x => x.DealerGroup_Id == (int)dealerGroup && x.Type == "Deal" && x.Status == "").ToListAsync();
            }
        }

        public async Task<ApiCallLog> AddDeals(DealerGroupName dealerGroup, string url, string request)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                var apiCallLog = new ApiCallLog()
                {
                    DealerGroup_Id = (int)dealerGroup,
                    Type = "Deal",
                    CreatedDate = DateTime.UtcNow,
                    Status = "",
                    Request = request,
                    URL = url
                };

                db.ApiCallLogs.Add(apiCallLog);
                await db.SaveChangesAsync();
                return apiCallLog;
            }
        }
       

        public async Task UpdateApiCallLog(DealerGroupName dealerGroupName, ApiCallLog apiCallLog)
        {
            using (var db = new CPHIDbContext(_connectionString))
            {
                db.ApiCallLogs.Update(apiCallLog);
                await db.SaveChangesAsync();
            }
        }
    }


}


