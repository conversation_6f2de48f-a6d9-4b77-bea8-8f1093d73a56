import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConstantsService } from './../services/constants.service';
import { SelectionsService } from './../services/selections.service';



@Component({
  selector: 'confirmModal',
  template: `
    
  <ng-template #modalRef let-modal>
      <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">{{header}}</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body alertModalBody lowHeight">
        {{ body }}
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" (click)="modal.close()">{{constants.translatedText.OKUpper}}</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">{{constants.translatedText.Cancel}}</button>
      </div>
</ng-template>
    
   `,

  styles: [`
 
  
    `]
})


export class ConfirmModalComponent  {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;
  chosenBomItemEmitter: any;
 public header:string;
   public body: string;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,

  ) { }


  

  showModal(headerMessage:string, bodyMessage:string) {

    this.header = headerMessage;
    this.body = bodyMessage
    this.modalService.open(this.modalRef, { windowClass: "confirmModal", size: 'sm', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //'ok'
      this.selections.confirmModalEmitter.next(true)

    }, (reason) => {
      //cancel
      this.selections.confirmModalEmitter.next(false)

    });





  }


}


