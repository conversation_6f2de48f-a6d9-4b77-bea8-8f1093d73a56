﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;

namespace CPHI.WebScraper.ViewModel
{

   public class ModixPriceUpdate
   {
      public string Reg { get; set; }
      public int Price { get; set; }
      public int RetailerSiteId { get; set; }
      public bool IsCupra { get; set; }
      public bool PriceChanged { get; set; } = false;

      // Properties for SaveChangeResult
      public int PriceChangeId { get; set; }
      public bool IsAutoPriceChange { get; set; } = true; // Believe this should always be true
      public bool SavedOk { get; set; }
      public string SaveError { get; set; }

      public ModixPriceUpdate(PricingChangeNew pr)
      {
         bool isCupra = pr.Make.ToUpper().Contains("CUPRA");
         Reg = pr.VehicleReg;
         Price = pr.NewPrice;
         RetailerSiteId = pr.RetailerSiteId;
         IsCupra = isCupra;
         PriceChangeId = pr.PriceChangeId;
      }

      // To create test items
      public ModixPriceUpdate(string reg, int price, int retailerSiteId, bool isCupra)
      {
         Reg = reg;
         Price = price;
         RetailerSiteId = retailerSiteId;
         IsCupra = isCupra;
      }

   }


}
