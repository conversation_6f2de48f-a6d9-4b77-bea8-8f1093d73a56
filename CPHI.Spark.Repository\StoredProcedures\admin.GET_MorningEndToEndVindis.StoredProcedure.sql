DROP PROCEDURE IF EXISTS [admin].[GET_MorningEndToEndVindis]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [admin].[GET_MorningEndToEndVindis]
--(
	--@ChosenMonth Date = getdate()
--)
AS
BEGIN
SET NOCOUNT ON
	--Declare @NonPayingOrderTypes table (Id int);
	--INSERT @NonPayingOrderTypes (Id) (SELECT Id from OrderTypes where Code = 'TRADE' OR Code = 'Auction' );
--Create temp tables
CREATE TABLE #ExpectedJobs(
OrderBy decimal(10,5),
WhenShouldRun varchar(10),
[Job] [nvarchar](50) NULL
)
INSERT INTO #ExpectedJobs (OrderBy,WhenShouldRun,Job)
VALUES
(1, '01:00', 'DebtsVindisJob'),
(2, '01:00', 'PartsStockVindisJob'),
(3, '01:00', 'WipVindisJob'),
(4, '01:00', 'BookingHoursJobVindis'),
(5, '02:00', 'BookingJobsJobVindis'),
(6, '02:02', 'StocksVindisJob'),
(7, '03:00', 'FinancialLinesVindisJob'),
(8, '05:00', 'CitNowJobVindis'),
(9, '08:00', 'VhcJobVindis'),
(10, '08:03', 'DealsFleetVindisJob'),

--Autotrader ones
(41,'06:00','FetchAdverts'),
-- (41,'06:00','PopulateCacheTable'),
(41,'06:00','CountPriceChanges'),
--(41,'06:00','CreateOptOuts'),
(41,'06:00', 'LeavingItems'),
(41, '06:00', 'StrategyPrices'),
(41, '06:00', 'UpdateDaysToSell'),
(41, '06:00', 'CompetitorInformation'),
(41, '06:00', 'GlobalParams'),
(41, '06:00', 'TriggerCache'),
(41, '06:00', 'GenerateAutoChanges'),
(41, '06:00', 'SendEmailWeekday'),
(41, '06:00', 'DeleteOldValuations'),
(41, '08:00', 'ModixUpdatePricesJob'),

(41, '06:00', 'InStockNotOnPortal'),
(41, '06:00', 'LocationOptimiser: CalcStrategy')

;
WITH RecentJobs AS
(
   SELECT *,
         ROW_NUMBER() OVER (PARTITION BY Job ORDER BY SourceDate DESC) AS rn
   FROM LogMessages
   WHERE finishDate > dateadd(hour,19,DATEDIFF(d,0,getdate()-1))
)
SELECT e.WhenShouldRun,e.Job,
IIF ((lm.Job IS NULL), 'Not Found', IIF(lm.ErrorCount>0,'Errors', 'Ok')) as Status,lm.SourceDate,lm.FinishDate,lm.IsCompleted,lm.FailNotes,lm.ProcessedCount,lm.AddedCount,lm.RemovedCount,lm.ChangedCount,lm.ErrorCount
FROM #ExpectedJobs e
LEFT JOIN RecentJobs lm on lm.Job = e.Job AND lm.rn = 1
ORDER BY e.OrderBy
DROP TABLE #ExpectedJobs
END




--select * from LogMessages where sourcedate > '2021-09-01' 
GO
