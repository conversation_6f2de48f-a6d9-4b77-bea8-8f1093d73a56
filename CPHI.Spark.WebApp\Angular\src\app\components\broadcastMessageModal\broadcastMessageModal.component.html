<div class="modal-header">
   <h4 class="modal-title" id="modal-basic-title">
      Spark Broadcast Message Created
      {{ messageToDisplay.CreatedDate | cph : "date" : 0 }}
   </h4>
   <button
      type="button"
      class="close"
      aria-label="Close"
      (click)="closeMessage()"
   >
      <span aria-hidden="true">&times;</span>
   </button>
</div>

<div class="modal-body" [ngClass]="constants.environment.customer">
   <div class="autotraderCard">
      <div class="cardInner">
         <div class="cardBody">
            <div
               id="messageContainer"
               [innerHTML]="messageToDisplay.Message"
            ></div>
         </div>
      </div>
   </div>
</div>

<div class="modal-footer">
   <sliderSwitch
      [text]="constants.translatedText.DoNotShowAgain"
      [blackFont]="true"
      [defaultValue]="doNotShowAgain"
      (toggle)="toggleDoNoShowAgain()"
   ></sliderSwitch>
   <button type="button" class="btn btn-primary" (click)="closeMessage()">
      {{ constants.translatedText.Close }}
   </button>
</div>
