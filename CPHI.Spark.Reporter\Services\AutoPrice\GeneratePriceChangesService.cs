﻿using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using CPHI.Spark.Repository;
using CPHI.Spark.WebApp.DataAccess;
using Datadog.Trace;
using log4net;
using Microsoft.AspNetCore.DataProtection;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using OfficeOpenXml.FormulaParsing.Utilities;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class GeneratePriceChangesService
   {
      private static DateTime runDate = DateTime.Now;
      private readonly HttpClient httpClient;

      public GeneratePriceChangesService(HttpClient httpClient)
      {
         this.httpClient = httpClient;
      }

      public async Task GeneratePriceChanges(ILog logger, List<DealerGroupName> dealerGroups)
      {
         if (dealerGroups.Count == 0) { return; }
         try
         {
            logger.Info("----------------------------------------------------------");
            logger.Info("GenerateAutoChanges");

            using (var parentScope = Tracer.Instance.StartActive("GenerateAutoChanges"))
            {
               foreach (DealerGroupName dealerGroup in dealerGroups)
               {
                  using (var childScope = Tracer.Instance.StartActive($"Begin dealergroup {dealerGroup}"))
                  {
                     childScope.Span.SetTag("DealerGroup", dealerGroup.ToString());

                     logger.Info($"GenerateAutoChanges: {dealerGroup}");
                     var logMessage = LoggingService.InitLogMessage();

                     try
                     {
                        RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                        List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

                        if (retailers.Count > 0)
                        {
                           try
                           {
                              await GeneratePriceChangesForThisDealerGroupNew(dealerGroup);
                              logger.Info($"Generated price changes for {dealerGroup}");
                              await UpdateDaysToSellAndPriceIndicatorForNewPrices(dealerGroup, logger);
                              logger.Info($"Updated Days to sell and price indicator for {dealerGroup}");
                           }
                           catch (Exception ex)
                           {
                              logger.Error(ex);
                              await EmailerService.SendMailOnError(dealerGroup, "GenerateAutoChanges - Error", ex);
                              LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                           }
                        }
                     }
                     catch (Exception ex)
                     {
                        await EmailerService.LogException(ex, logger, "GenerateAutoChanges");
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                     finally
                     {
                        await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, "GenerateAutoChanges");
                     }
                  }
               }

               logger.Info("Completed GenerateAutoChanges");
               logger.Info("----------------------------------------------------------");
            }
         }
         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }

      private async Task UpdateDaysToSellAndPriceIndicatorForNewPrices(DealerGroupName dealerGroupName, ILog logger)
      {
         //Get the list of PriceChangeAutoItems to be updated
         string connString = ConfigService.GetConnectionString(dealerGroupName);
         var priceChangesDataAccess = new PriceChangesDataAccess(connString);
         IEnumerable<PriceChangeAutoItemsVM> priceChangeAutoItemsVM = await priceChangesDataAccess.GetAutoPriceChangesFromTodayWhichHaveAPrice(dealerGroupName);
         priceChangeAutoItemsVM = RemoveUnwatedRows(priceChangeAutoItemsVM);

         //  HttpClient httpClient = new HttpClient();
         AutoTraderVehicleMetricsClient atMetricsClient = new AutoTraderVehicleMetricsClient(HttpClientFactoryService.HttpClientFactory,
         ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         var atValuationsClient = new AutoTraderValuationsClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey,
          ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         var atTokenClient = new AutoTraderApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.AutotraderApiKey, ConfigService.AutotraderApiSecret, ConfigService.AutotraderBaseURL);
         var token = (await atTokenClient.GetToken()).AccessToken;

         ConcurrentBag<PricingChange> priceChanges = new ConcurrentBag<PricingChange>();
         priceChangeAutoItemsVM = RemoveUnwatedRows(priceChangeAutoItemsVM);
         priceChangeAutoItemsVM = priceChangeAutoItemsVM;
         IEnumerable<PriceChangeAutoItemsVM[]> chunks = priceChangeAutoItemsVM.Chunk(40);

         foreach (var chunk in chunks)
         {
            List<Task> tasks = new List<Task>();

            foreach (var priceChangeAutoItem in chunk)
            {
               tasks.Add(ExecuteUpdateDaysToSellAndPriceIndicator(token, priceChangeAutoItem,
                atMetricsClient, priceChanges, logger, atValuationsClient));
            }

            await Task.WhenAll(tasks); //execute all items within the chunk of items at once in aprallel
            tasks.Clear();
         }

         await priceChangesDataAccess.UpdatePriceChangeAutoItemsDaysToSellAndPriceIndicator(priceChanges.ToList());

      }

      private IEnumerable<PriceChangeAutoItemsVM> RemoveUnwatedRows(IEnumerable<PriceChangeAutoItemsVM> priceChangeAutoItemsVM)
      {
         return priceChangeAutoItemsVM = priceChangeAutoItemsVM.Where(x => x.DerivativeId != null).ToList(); //Mandatory col
      }

      private async Task ExecuteUpdateDaysToSellAndPriceIndicator(
         string token,
         PriceChangeAutoItemsVM priceChangeAutoItem,
         AutoTraderVehicleMetricsClient atMetricsClient,
         ConcurrentBag<PricingChange> priceChanges,
         ILog logger,
         AutoTraderValuationsClient atValuationsClient)
      {

         bool useOptions = priceChangeAutoItem.VehicleAdvertPortalOptions?.Length > 0;
         GetAdvertPriceAdjustedDaysToSellParams daysToSellParams = new GetAdvertPriceAdjustedDaysToSellParams()
         {
            AutotraderBaseURL = ConfigService.AutotraderBaseURL,
            AdvertiserId = priceChangeAutoItem.AdvertiserId,
            DerivativeId = priceChangeAutoItem.DerivativeId,
            FirstRegistrationDate = priceChangeAutoItem.FirstRegistrationDate,
            OdometerReadingMiles = priceChangeAutoItem.OdometerReadingMiles,
            Amount = priceChangeAutoItem.NowPrice,

            AdjustedValuation = priceChangeAutoItem.ValuationAdjusted,
            AverageValuation = priceChangeAutoItem.ValuationAverage,

            UseSpecificOptions = useOptions,
            SpecificOptionNames = useOptions ? priceChangeAutoItem.VehicleAdvertPortalOptions.Split(',') : null
         };


         if (priceChangeAutoItem.NowPrice > 1.1M * priceChangeAutoItem.ValuationAdjusted || priceChangeAutoItem.NowPrice < 0.9M * priceChangeAutoItem.ValuationAdjusted)
         {
            //the endpoint won't like this so just return
            priceChanges.Add(new PricingChange() { PriceIndicator = PriceStrategyClassifierService.ProvidePriceIndicatorName(null), DaysToSell = 0, PriceChangeId = priceChangeAutoItem.Id });
            return;
         }

         if (priceChangeAutoItem.NowPrice > 1.1M * priceChangeAutoItem.ValuationAverage || priceChangeAutoItem.NowPrice < 0.9M * priceChangeAutoItem.ValuationAverage)
         {
            //the endpoint won't like this so just return
            priceChanges.Add(new PricingChange() { PriceIndicator = PriceStrategyClassifierService.ProvidePriceIndicatorName(null), DaysToSell = 0, PriceChangeId = priceChangeAutoItem.Id });
            return;
         }




         decimal? daysToSell = null;
         string cleanedPriceIndicator = string.Empty;
         try
         {
            var taskPriceAdjustedDaysToSell = atMetricsClient.GetAdvertPriceAdjustedDaysToSell(
               daysToSellParams, token, logger);

            decimal adjustmentFactorForPriceIndicator = 1; //to do properly
            var taskPriceIndicator = atValuationsClient.GetAdvertPriceIndicator(daysToSellParams,
             token, adjustmentFactorForPriceIndicator);

            await Task.WhenAll(taskPriceAdjustedDaysToSell, taskPriceIndicator);
            string priceIndicator = taskPriceIndicator.Result;
            cleanedPriceIndicator = PriceStrategyClassifierService.ProvidePriceIndicatorName(priceIndicator);
            daysToSell = taskPriceAdjustedDaysToSell.Result != 0 ? Math.Ceiling(taskPriceAdjustedDaysToSell.Result) : null;
         }
         catch (Exception ex)
         {
            logger.Error($"Errored on calculating a revised set of price indicators, exception is: {ex}");
            daysToSell = null;
         }



         priceChanges.Add(new PricingChange() { PriceIndicator = cleanedPriceIndicator, DaysToSell = daysToSell, PriceChangeId = priceChangeAutoItem.Id });

      }







      private async Task GeneratePriceChangesForThisDealerGroupNew(DealerGroupName dealerGroup)
      {
         string connString = ConfigService.GetConnectionString(dealerGroup);
         var priceChangesDataAccess = new PriceChangesDataAccess(connString);
         List<PriceChangeAutoItemWithDates> priceChanges = await priceChangesDataAccess.GetTodayAutoPriceChanges(DateTime.Today.Date, dealerGroup);

         //now we do stuff to them 
         var globalParamsDataAccess = new GlobalParamDataAccess(connString);

         //SPK-5601 suppress increases that are over age
         var allParams = await globalParamsDataAccess.GetAll(dealerGroup);
         var supressUps = allParams.FirstOrDefault(x => x.Description == "SupressPriceUpWhenDaysOverX");
         if(supressUps!=null && supressUps.Value.IsNumeric())
         {
            int eliminatePriceUpsOver = (int)supressUps.Value;
            priceChanges = priceChanges.Where(x => x.DaysListed <= eliminatePriceUpsOver || (x.NowPrice < x.WasPrice)).ToList();
         }

         //SPK-5602 tag as key changes
         if (dealerGroup == DealerGroupName.Sytner)
         {
            var changesBySite = priceChanges.ToLookup(x => x.RetailerSiteId);
            foreach (var siteGrouping in changesBySite)
            {
               //tag the top 5 and bottom 5 biggest changes
               var ordered = siteGrouping.ToList().OrderBy(x=>x.NowPrice - x.WasPrice).ToList();
               foreach (var change in ordered.Take(5))
               {
                  change.IsKeyChange = true;
               }

               foreach (var change in ordered.TakeLast(5))
               {
                  change.IsKeyChange = true;
               }
            }
         }
        // var tst = priceChanges.Where(x=>x.IsKeyChange).ToList();

         //Now save
         List<PriceChangeAutoItem> changesToPersist = priceChanges.Select(x => new PriceChangeAutoItem(x)).ToList();
         await priceChangesDataAccess.SaveAutoPriceChanges(changesToPersist);
      }





   }
}
