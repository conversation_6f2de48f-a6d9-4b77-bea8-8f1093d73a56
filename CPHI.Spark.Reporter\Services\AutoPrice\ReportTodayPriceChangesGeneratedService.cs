﻿//using CPHI.Spark.DataAccess;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Repository;
using CPHI.Spark.WebApp.DataAccess;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Reporter.Services.AutoPrice
{
   public class ReportTodayPriceChangesGeneratedService
   {
      private static DateTime runDate = DateTime.Now;

      public async Task SendEmails(ILog logger, List<DealerGroupName> dealerGroups, bool isWeekend)
      {
         if (dealerGroups.Count == 0)
         { return; }
         try
         {
            string logName = isWeekend ? "SendEmailWeekend" : "SendEmailWeekday";
            logger.Info("----------------------------------------------------------");
            logger.Info(logName);

            foreach (DealerGroupName dealerGroup in dealerGroups)
            {
               logger.Info($"{logName}: {dealerGroup}");
               var logMessage = LoggingService.InitLogMessage();
               try
               {

                  RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
                  List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);
                  Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions = await retailerSitesDataAccess.GetRetailerStrategyBandings();

                  if (retailers.Count > 0)
                  {
                     try
                     {
                        await SendEmailsThisDealerGroup(logger, dealerGroup, bandingDefinitions);
                        logger.Info($"{logName} done for {dealerGroup}");
                     }
                     catch (Exception ex)
                     {
                        logger.Error(ex);
                        await EmailerService.SendMailOnError(dealerGroup, $"{logName} - Error", ex);
                        LoggingService.AddErrorLogMessage(logMessage, ex.Message);
                     }
                  }
               }
               catch (Exception ex)
               {
                  await EmailerService.LogException(ex, logger, logName);
                  LoggingService.AddErrorLogMessage(logMessage, ex.Message);
               }
               finally
               {
                  await LoggingService.FinalizeAndSaveLogMessage(dealerGroup, logMessage, logName);

               }
            }

            logger.Info("Completed SendEmails");
            logger.Info("----------------------------------------------------------");
         }

         catch (Exception e)
         {
            logger.Error(e);
            throw new Exception(e.Message, e);
         }
      }

      private static async Task SendEmailsThisDealerGroup(ILog Logger, DealerGroupName customer, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions)
      {
         var retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(customer));
         List<RetailerSite> retailerSites = await retailerSitesDataAccess.GetRetailerSites(customer);
         Dictionary<int, RetailerSite> sitesLookup = retailerSites.ToDictionary(x => x.Id);
         List<TodaySiteStats> todaySiteStatsNew = await GetTodaySiteStats(customer, true, false);
         List<TodaySiteStats> todaySiteStatsUsed = await GetTodaySiteStats(customer, false, true);

         string retailerSiteIds = string.Join(',', todaySiteStatsUsed.Select(x => x.RetailerSiteId));
         List<PricingChangeNew> todaysPriceChanges = (await GetTodayPriceChanges(retailerSiteIds, customer, bandingDefinitions))
                     .Where(x => !x.IsSmallChange)
                     .OrderBy(x => x.DaysListed).ToList();

         if (todaysPriceChanges.Count == 0)
         {
            throw new Exception("Zero price changes found, throwing exception.");
         }

         // NEW ! SPK-4680 limit changes if defaultStockTypes is chosen
         //todaysPriceChanges = await FilterForChosenStockTypes(customer, todaysPriceChanges);

         var globalParamsDataAccess = new GlobalParamDataAccess(ConfigService.GetConnectionString(customer));
         var globals = await globalParamsDataAccess.GetAll(customer);
         GlobalParam onlyKeyProp = globals.FirstOrDefault(x => x.Description == "webAppShowOnlyKeyPriceChanges");
         todaysPriceChanges = await filterChangesForOnlyKeyIfRequired(customer, todaysPriceChanges, onlyKeyProp);
         bool includeRecentAppearances = customer == DealerGroupName.Sytner; //hacky
         GlobalParam includeAgeOwnersColourProp = globals.FirstOrDefault(x => x.Description == "ShowAgeOwnersColourOnPriceChanges");
         bool includeAgeOwnersColour = false;
         if (includeAgeOwnersColourProp != null && includeAgeOwnersColourProp.TextValue == "True")
         {
            includeAgeOwnersColour = true;
         }
         GlobalParam skipSummaryTablesForHighValueChangesProp = globals.FirstOrDefault(x => x.Description == "EmailSkipSummaryTablesForHighValueChanges");
         bool includeSummaryTablesForHighValueChanges = true;
         bool includeRankingCols = customer != DealerGroupName.Sytner; //hacky
         if (skipSummaryTablesForHighValueChangesProp != null && skipSummaryTablesForHighValueChangesProp.TextValue == "True")
         {
            includeSummaryTablesForHighValueChanges = false;
         }

         var autoPriceDataAccess = new AutoPriceDataAccess(ConfigService.GetConnectionString(customer));
         IEnumerable<RetailerSitesPerPerson> userSites = await autoPriceDataAccess.GetRetailerSitesPerUser((int)customer);
         IEnumerable<IGrouping<string, RetailerSitesPerPerson>> userSitesGroupedByPerson = userSites.GroupBy(x => x.Name);
         GetVehicleOptOutSummaryItemsParams optOutParams = new GetVehicleOptOutSummaryItemsParams()
         {
            ChosenDate = runDate,
            RetailerSiteIds = todaySiteStatsUsed.Select(x => x.RetailerSiteId).ToList(),
            DealerGroupId = (int)customer
         };
         var optOutsDataAccess = new OptOutsDataAccess(ConfigService.GetConnectionString(customer));
         IEnumerable<VehicleOptOutSummaryItemNew> optOuts = await optOutsDataAccess.GetVehicleOptOutSummaryItems(optOutParams);
         ILookup<int, VehicleOptOutSummaryItemNew> optOutsBySite = optOuts.ToLookup(x => x.RetailerSiteId);
         foreach (var personGrouping in userSitesGroupedByPerson)
         {
            //var thisPersonRetailerSiteIds = grouping.ToList().Select(x => x.RetailerSiteId);
            var thisPersonRetailerSiteIds = personGrouping.ToList().Select(x => x.RetailerSiteId);
            List<PricingChangeNew> thisPersonPriceChanges = todaysPriceChanges
                .Where(x => thisPersonRetailerSiteIds.Contains(x.RetailerSiteId) && !x.IsSmallChange)
                .ToList();

            //sort the list by retailerId, then by the required sort order for each RetailerId
            List<PricingChangeNew> sortedPrices = new List<PricingChangeNew>();
            foreach (var retailerSiteGrouping in thisPersonPriceChanges.GroupBy(x => x.RetailerSiteId))
            {
               string sortApproach = personGrouping.First(x => x.RetailerSiteId == retailerSiteGrouping.First().RetailerSiteId).SortPriceChangesReportBy;
               var sorted = retailerSiteGrouping.ToList();
               if (sortApproach == "daysListedDesc")
               {
                  sorted = retailerSiteGrouping.OrderByDescending(x => x.DaysListed).ToList();
               }
               else if (sortApproach == "daysListedAsc")
               {
                  sorted = retailerSiteGrouping.OrderBy(x => x.DaysListed).ToList();
               }
               else if (sortApproach == "DaysListedAsc")
               {
                  sorted = retailerSiteGrouping.OrderBy(x => x.DaysInStock).ToList();
               }
               else if (sortApproach == "DaysListedDesc")
               {
                  sorted = retailerSiteGrouping.OrderByDescending(x => x.DaysInStock).ToList();
               }
               sortedPrices.AddRange(sorted);
            }


            var thisPersonSiteStatsUsed = todaySiteStatsUsed.Where(x => thisPersonRetailerSiteIds.Contains(x.RetailerSiteId)).ToList();
            var thisPersonSiteStatsNew = todaySiteStatsNew.Where(x => thisPersonRetailerSiteIds.Contains(x.RetailerSiteId)).ToList();
            var thisPersonSites = personGrouping.ToList().Select(x => new RetailerSiteIdAndName(x)).ToList();
            var thisPersonEmail = personGrouping.First().Email;
            var thisPersonName = personGrouping.First().Name;

            //***************************************
            //***************************************
            //***************************************
            //  temp - restrict to me
            //***************************************
            //***************************************
            //***************************************
            //if (thisPersonName != "Richard Procter") { continue; }
            await GenerateAndSendEmail(Logger, thisPersonSiteStatsUsed, thisPersonSiteStatsNew, sortedPrices, thisPersonName, thisPersonEmail, thisPersonSites, optOutsBySite, customer, bandingDefinitions, sitesLookup, includeRecentAppearances, includeAgeOwnersColour, includeSummaryTablesForHighValueChanges, includeRankingCols);
         }
      }

      private static async Task<List<PricingChangeNew>> filterChangesForOnlyKeyIfRequired(DealerGroupName customer, List<PricingChangeNew> todaysPriceChanges, GlobalParam onlyKeyProp)
      {

         if (onlyKeyProp != null && onlyKeyProp.TextValue == "True")
         {
            todaysPriceChanges = todaysPriceChanges.Where(x => x.IsKeyChange).ToList();
         }

         return todaysPriceChanges;
      }

      private static async Task<List<PricingChangeNew>> FilterForChosenStockTypes(DealerGroupName customer, List<PricingChangeNew> todaysPriceChanges)
      {
         var retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(customer));
         Dictionary<int, RetailerSite> retailers = (await retailerSitesDataAccess.GetRetailerSites(customer)).ToDictionary(x => x.Id);

         List<PricingChangeNew> filteredForVehicleTypes = new List<PricingChangeNew>();
         foreach (var change in todaysPriceChanges)
         {
            var retailer = retailers[change.RetailerSiteId];

            //filter down for default vehicle types
            if (retailer.DefaultVehicleTypes != null)
            {
               //determine if this is one of the default vehicle types
               var vehicleTypes = retailer.DefaultVehicleTypes.Split(',');
               if (!vehicleTypes.Contains(change.VehicleTypeDesc))
               {
                  continue;
               }
            }




         }
         todaysPriceChanges = filteredForVehicleTypes;
         return todaysPriceChanges;
      }

      private static async Task GenerateAndSendEmail(
          ILog logger,
          List<TodaySiteStats> todaySiteStatsUsed,
          List<TodaySiteStats> todaySiteStatsNew,
          List<PricingChangeNew> todaysPriceChanges,
          string name,
          string email,
          List<RetailerSiteIdAndName> thisPersonSites,
          ILookup<int, VehicleOptOutSummaryItemNew> optOutsBySite,
          DealerGroupName customer,
          Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions,
          Dictionary<int, RetailerSite> sitesLookup
, bool includeRecentAppearances, bool includeAgeOwnersColour, bool includeSummaryTablesForHighValueChanges, bool includeRankingCols)

      {
         StringBuilder allText = new StringBuilder();
         allText.Append($"** Automated pricing message for: <strong>{name}</strong> **");
         allText.Append("<br>");
         allText.Append(GenerateSummarySiteTable(todaysPriceChanges, todaySiteStatsUsed, thisPersonSites, "Used", customer));
         allText.Append("<br><br><br><br><br><br>");
         allText.Append(GenerateDetailTables(todaysPriceChanges, optOutsBySite, bandingDefinitions, sitesLookup, includeRecentAppearances, includeAgeOwnersColour, includeSummaryTablesForHighValueChanges, includeRankingCols));

         var emails = new List<string>() { email };
         await EmailerService.SendMailOldStyle(customer, "Spark - Today's Price Changes Generated", allText.ToString(), null, logger, emails, new List<string>() { });
         //logger.Info($"SendEmails: Sent email to {name}");
      }

      private static async Task<List<PricingChangeNew>> GetTodayPriceChanges(string retailerSiteIds, DealerGroupName customer, Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingsDict)
      {
         var priceChangesDataAccess = new PriceChangesDataAccess(ConfigService.GetConnectionString(customer));
         var parms = new GetPriceChangesNewParams
         {
            ChosenDate = DateTime.Today,
            RetailerSiteIds = retailerSiteIds,
            IncludeNewVehicles = false,
            IncludeSmallChanges = false,
            DealerGroupId = (int)customer,
            BandingsDict = bandingsDict,
            IncludeUnPublishedAds = false,
            IncludeUnPublishedAdBasedOnSiteSetting = true,
            IncludeNewVehiclesBasedOnSiteSetting = true,
            IncludeLifecycleStatusesBasedOnSiteSetting = true,
            FilterDaysInStockBasedOnSiteSetting = true,
            IncludeVehicleTypesBasedOnSiteSetting = true
         };

         var priceChanges = await priceChangesDataAccess.GetPriceChanges(parms);

         return priceChanges.ToList();
      }

      private static async Task<List<TodaySiteStats>> GetTodaySiteStats(DealerGroupName customer, bool getNew, bool getUsed)
      {
         var autoPriceDataAccess = new AutoPriceDataAccess(ConfigService.GetConnectionString(customer));
         IEnumerable<TodaySiteStats> stats = await autoPriceDataAccess.GetTodaySiteStats(null, DateTime.Today, getNew, getUsed, (int)customer);
         return stats.ToList();
      }




      private static string GenerateSummarySiteTable(List<PricingChangeNew> todaysPriceChanges, List<TodaySiteStats> todaySiteStats, List<RetailerSiteIdAndName> thisPersonSites,
         string ownershipCondition, DealerGroupName customer)
      {
         //filter down price changes
         todaysPriceChanges = todaysPriceChanges.Where(x => x.OwnershipCondition == ownershipCondition).ToList();

         //make summary table
         var summaryTable = new StringBuilder();
         summaryTable.Append($"<h2> Today's Price Changes - summary by site - {ownershipCondition}</h2>");
         //summaryTable.Append("<p> Scroll down for site by site detail</p>");
         SummaryTableHeaderRows(summaryTable, customer);
         List<TodaySiteStats> thisPersonSiteStatsList = new List<TodaySiteStats>();
         foreach (var site in thisPersonSites)
         {
            var priceChangesThisSite = todaysPriceChanges.Where(x => site.RetailerSiteId == x.RetailerSiteId).ToList();
            //if (priceChangesThisSite.Count > 0)
            //{
            TodaySiteStats siteStats = todaySiteStats.FirstOrDefault(x => x.Name == site.RetailerSiteName);
            SummaryTableBodyRows(summaryTable, priceChangesThisSite, site.RetailerSiteName, siteStats, false, customer);
            thisPersonSiteStatsList.Add(siteStats);
         }

         var totalSiteStatsThisPerson = new TodaySiteStats(thisPersonSiteStatsList);
         SummaryTableBodyRows(summaryTable, todaysPriceChanges, "Total", totalSiteStatsThisPerson, true, customer);

         summaryTable.Append(@"</tbody></table>");//close off

         var summaryBodyText = summaryTable.ToString();
         return summaryBodyText;
      }

      private static string GenerateDetailTables(List<PricingChangeNew> todaysPriceChanges, ILookup<int, VehicleOptOutSummaryItemNew> optOutsBySite,
          Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions, Dictionary<int, RetailerSite> sitesLookup, bool includeRecentAppearances, bool includeAgeOwnersColour,
          bool includeSummaryTablesForHighValueChanges, bool includeRankingCols)
      {
         var result = new StringBuilder();
         result.Append("<h2> Today's Price Changes - detail</h2>");
         foreach (var siteGrouping in todaysPriceChanges.ToList().ToLookup(x => x.RetailerName))
         {
            int retailerSiteId = siteGrouping.First().RetailerSiteId;
            var retailerSite = sitesLookup[retailerSiteId];
            var bandingDefinition = bandingDefinitions[retailerSiteId];


            //note the colour definitions for this site
            var definition = bandingDefinitions[retailerSiteId];
            string greenDef = definition.OverUnderIsPercent
                ? $"Below {definition.OverUnderThreshold * 100:0}% is green" // Formats as integer percentage
                : $"Below £{(definition.OverUnderThreshold):0} is green"; // Formats as integer currency amount

            string orangeDef = definition.OverUnderIsPercent ? $"over {definition.OverUnderThreshold * 100:0}% is orange" : $"over £{definition.OverUnderThreshold:0} is orange";
            string redDef = definition.VeryThresholdIsPercent ? $"over {definition.VeryThreshold * 100:0}% is red." : $"over £{definition.VeryThreshold:0} is red.";

            //site header
            result.Append("<br>");
            result.Append("<br>");
            result.Append("<br>");
            result.Append($"<h3> {siteGrouping.Key}</h3>");
            result.Append($"Colours indicate the size of the price change.  {greenDef}, {orangeDef} and {redDef}");

            var usedChanges = siteGrouping.Where(x => x.OwnershipCondition == "Used");
            GenerateDetailTablePriceChangeDetails(result, bandingDefinition, usedChanges, false, retailerSite, includeRecentAppearances, includeAgeOwnersColour, includeRankingCols);

            result.Append("<br>");
         }

         result.Append("<br>");
         result.Append("<br>");
         result.Append("<br>");
         result.Append("<br>");
         if (optOutsBySite.Count > 0)
         {

            result.Append("<br>");
            result.Append("<br>");
            result.Append("<br>");
            result.Append("<br>");
            result.Append("<br>");
            result.Append("<h2> All Opted Out Vehicles - detail</h2>");
            foreach (IGrouping<int, VehicleOptOutSummaryItemNew> siteOptOutsSet in optOutsBySite)
            {
               var exampleItem = siteOptOutsSet.First();
               result.Append($"<h3> {exampleItem.RetailerName}</h3>");
               OptOutTableHeaders(result);
               OptOutTableBodyRows(result, siteOptOutsSet);
               result.Append(@"</tbody></table>");//close off
            }
         }


         //new SPK-4495 now we add on a new summary of just the high value changes

         if (includeSummaryTablesForHighValueChanges)
         {

            result.Append("<br>");
            result.Append("<br>");
            result.Append("<br>");
            result.Append("<br>");
            result.Append("<h2> Today's Price Changes - Summary of large changes</h2>");
            result.Append("<br>");
            result.Append("<br>");
            result.Append("<br>");
            result.Append("<br>");
            foreach (var siteGrouping in todaysPriceChanges.ToList().ToLookup(x => x.RetailerName))
            {
               int retailerSiteId = siteGrouping.First().RetailerSiteId;
               var retailerSite = sitesLookup[retailerSiteId];
               var bandingDefinition = bandingDefinitions[retailerSiteId];
               result.Append($"<h3> {siteGrouping.Key}</h3>");
               var usedChanges = siteGrouping.Where(x => x.OwnershipCondition == "Used");
               GenerateDetailTablePriceChangeDetails(result, bandingDefinition, usedChanges, true, retailerSite, includeRecentAppearances, includeAgeOwnersColour, includeRankingCols);
               result.Append("<br>");
            }

         }

         return result.ToString();
      }

      private static void GenerateDetailTablePriceChangeDetails(StringBuilder result, RetailerSiteStrategyBandingDefinition bandingDefinition, IEnumerable<PricingChangeNew> allSiteChanges, bool skipNonVeryChanges, RetailerSite retailerSite, bool includeRecentAppearances, bool includeAgeOwnersColour, bool includeRankingCols)
      {
         //Firstly identify which changes to draw
         List<PricingChangeNew> priceChangesToUse = new List<PricingChangeNew>();
         foreach (var priceChange in allSiteChanges)
         {
            //skip any where the was price is zero
            if ((priceChange.WasPrice ?? 0) == 0)
            {
               continue;
            }

            if (skipNonVeryChanges)
            {
               var vsStrategyBanding = BandingsService.ProvidePriceVsStrategyBanding(priceChange.NewPrice, (int)priceChange.WasPrice, (int)priceChange.WasPrice, bandingDefinition);
               if (vsStrategyBanding != "VeryOverPriced" && vsStrategyBanding != "VeryUnderPriced")
               {
                  continue;
               }
            }

            priceChangesToUse.Add(priceChange);
         }



         result.Append($"<h4> Used price changes ({priceChangesToUse.Count()})</h4>");
         if (priceChangesToUse.Count() > 0)
         {
            bool includeExAdminCol = priceChangesToUse.Any(x => x.RetailerAdminFee > 0);
            DetailTableHeaders(result, includeExAdminCol, retailerSite.DaysMeasure, includeRecentAppearances, includeAgeOwnersColour, includeRankingCols);
            DetailTableBodyRows(result, priceChangesToUse, bandingDefinition, includeExAdminCol, retailerSite.DaysMeasure, includeRecentAppearances, includeAgeOwnersColour, includeRankingCols);
            result.Append(@"</tbody></table>");//close off
         }
         else
         {
            result.Append("No price changes");
         }

         result.Append("<br>");
         result.Append("<br>");
      }

      private static void SummaryTableHeaderRows(StringBuilder emailBody, DealerGroupName customer)
      {
         // Different headers if Brindley
         if (customer == DealerGroupName.BrindleyGroup)
         {
            emailBody.Append($@"<table><thead>

      <tr>
	      <th style=""padding:3px 10px; text-align:left"">Site</th>  
	      <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"">Adverts</th>  		
	      <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"">Opt-outs</th>  		
	      <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"">Changes</th>  
	      <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""8"">By Retail Rating</th>  
	      <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""2"">Averages</th>  
	      <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""3"">Today increases</th>  
	      <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""3"">Today decreases</th>  
	      <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"">Net</th>  
      </tr>
      <tr>
	      <th style=""min-width: 160px;width: 100px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
	      <th style=""min-width: 80px;width: 30px;border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
	      <th style=""min-width: 80px;width: 30px;border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
	      <th style=""min-width: 80px;width: 30px;border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>

	      <th style=""min-width: 30px;width: 30px; border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><20</th>
	      <th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><40</th>
	      <th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><50</th>
	      <th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><60</th>
	      <th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><70</th>
	      <th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><80</th>
	      <th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><90</th>
	      <th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><100</th>

	      <th style=""min-width: 80px;width: 30px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Retail Rating</th>
	      <th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Days</th>

	      <th style=""min-width: 80px;width: 30px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Number</th>
	      <th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Average</th>
	      <th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Total</th>

	      <th style=""min-width: 80px;width: 30px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Number</th>
	      <th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Average</th>
	      <th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Total</th>

	      <th style=""min-width: 80px;width: 30px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
      </tr>
      </thead>
      <tbody>");
         }
         else
         {
            emailBody.Append($@"<table><thead>

<tr >
		<th style=""padding:3px 10px; text-align:left"" >Site</th>  
<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" >Adverts</th>  		
<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" >Opt-outs</th>  		
<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" >Changes</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""5"">By Retail Rating</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""2"">Averages</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""3"">Today increases</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""3"">Today decreases</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" >Net</th>  
	</tr>
	<tr>
		<th style=""min-width: 160px;width: 100px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
		<th style=""min-width: 80px;width: 30px;border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
		<th style=""min-width: 80px;width: 30px;border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
		<th style=""min-width: 80px;width: 30px;border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
		<th style=""min-width: 30px;width: 30px; border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><20</th>
		<th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><40</th>
		<th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><60</th>
		<th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""><80</th>
		<th style=""min-width: 30px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">80+</th>

		<th style=""min-width: 80px;width: 30px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Retail Rating</th>
		<th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Days</th>
		
		<th style=""min-width: 80px;width: 30px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Number</th>
		<th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Average</th>
		<th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Total</th>

<th style=""min-width: 80px;width: 30px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Number</th>
		<th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Average</th>
		<th style=""min-width: 80px;width: 30px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Total</th>
		
		<th style=""min-width: 80px;width: 30px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
	</tr>
</thead>
<tbody>");
         }

      }

      private static void SummaryTableBodyRows(StringBuilder emailBody, List<PricingChangeNew> siteGrouping, string siteName, TodaySiteStats siteStats, bool isTotal,
         DealerGroupName customer)
      {
         string changeStyle = string.Empty;

         decimal totalRetailRating = 0;
         decimal totalDays = 0;

         int increases = 0;
         decimal increaseValue = 0;
         int decreases = 0;
         decimal decreaseValue = 0;

         foreach (PricingChangeNew change in siteGrouping)
         {
            totalRetailRating += change.RetailRating;
            totalDays += change.DaysListed;

            if (change.IsIncrease)
            {
               increases++;

               // Do not include items where current price is 0 as this inflates the figure
               if ((change.WasPrice ?? 0) != 0)
               {
                  increaseValue += change.NewPrice - (change.WasPrice ?? 0);
               }

            }
            else
            {
               decreases++;
               decreaseValue += change.NewPrice - (change.WasPrice ?? 0);

            }
         }

         decimal avDays = siteGrouping.Count() > 0 ? totalDays / siteGrouping.Count() : 0;
         decimal avRetailRating = siteGrouping.Count() > 0 ? totalRetailRating / siteGrouping.Count() : 0;

         string everyCellStyle = isTotal ? " font-weight:700; border-top:1px solid #808080; padding:1px 10px;" : " padding:1px 10px;";
         string textRight = @"text-align:right;";

         var rowBuilder = new StringBuilder();
         rowBuilder.Append($@"<tr style=""font-size:14px;"">
    <td style=""white-space: nowrap; {everyCellStyle}"">{siteName}</td>
    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{(siteStats != null ? siteStats.ListedCount : string.Empty)}</td>
    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{(siteStats != null ? siteStats.OptOutCount : string.Empty)}</td>
    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{siteGrouping.Count()}</td>");

         if (customer == DealerGroupName.BrindleyGroup)
         {
            rowBuilder.Append($@"
    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating <= 20)}</td>
    <td style=""{everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 20 && x.RetailRating <= 40)}</td>
    <td style=""{everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 40 && x.RetailRating <= 50)}</td>
    <td style=""{everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 50 && x.RetailRating <= 60)}</td>
    <td style=""{everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 60 && x.RetailRating <= 70)}</td>
    <td style=""{everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 70 && x.RetailRating <= 80)}</td>
    <td style=""{everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 80 && x.RetailRating <= 90)}</td>
    <td style=""{everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 90 && x.RetailRating < 100)}</td>");
         }
         else
         {
            rowBuilder.Append($@"
    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating <= 20)}</td>
    <td style=""white-space: nowrap; {everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 20 && x.RetailRating <= 40)}</td>
    <td style=""white-space: nowrap; {everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 40 && x.RetailRating <= 60)}</td>
    <td style=""white-space: nowrap; {everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 60 && x.RetailRating <= 80)}</td>
    <td style=""white-space: nowrap; {everyCellStyle}{textRight}"">{siteGrouping.Count(x => x.RetailRating > 80)}</td>");
         }

         rowBuilder.Append($@"
    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{Format0Dp(avRetailRating)}</td>
    <td style=""{everyCellStyle}{textRight}"">{Format0Dp(avDays)}</td>

    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{increases}</td>
    <td style=""{everyCellStyle}{textRight}"">{FormatAsCurrency(increases > 0 ? increaseValue / increases : 0)}</td>
    <td style=""{everyCellStyle}{textRight}"">{FormatAsCurrency(increaseValue)}</td>

    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{decreases}</td>
    <td style=""{everyCellStyle}{textRight}"">{FormatAsCurrency(decreases > 0 ? decreaseValue / decreases : 0)}</td>
    <td style=""{everyCellStyle}{textRight}"">{FormatAsCurrency(decreaseValue)}</td>

    <td style=""border-left:1px solid #808080; {everyCellStyle}{textRight}"">{FormatAsCurrency(increaseValue + decreaseValue)}</td>
</tr>");

         emailBody.Append(rowBuilder.ToString());
      }

      private static void OptOutTableHeaders(StringBuilder emailBody)
      {
         emailBody.Append($@"<table><thead>

<tr >
		<th style="" text-align:left; padding:10px"" colspan=""4"">Vehicle</th> 
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""1"">Days online</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""3"">Opted-Out</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""2"">Opt-Out end</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""2"">Matrix Pricing</th>  
		<th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""3"">Manual Price</th>  
	</tr>
	<tr>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Reg</th>
        <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Make</th>
        <th style=""min-width: 100px;width: 100px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Model</th>
		<th style=""min-width: 300px;width: 300px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Vehicle</th>

		<th style=""min-width: 80px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080""></th>
		<th style=""min-width: 80px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">When</th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Days</th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">By</th>
		
		<th style=""min-width: 80px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">When</th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Days</th>
		
<th style=""min-width: 80px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Retail rating</th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Strategy Price</th>

<th style=""min-width: 80px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Price</th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Vs Matrix</th>
		<th style=""min-width: 80px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Last change</th>
		<th style=""min-width: 200px;width: 50px; border-left:1px solid #808080; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Notes</th>
		
		
	</tr>
</thead>
<tbody>");
      }

      private static void DetailTableHeaders(StringBuilder emailBody, bool includeExAdminFeeCol, string daysMeasure, bool includeRecentAppearances, bool includeAgeOwnersColour, bool includeRankingCols)
      {
         int newPriceSpan = includeExAdminFeeCol ? 4 : 3;
         int metricsSpan = 3;
         if (includeAgeOwnersColour)
         {
            metricsSpan += 2;
         }
         if (includeRankingCols)
         {
            metricsSpan += 2;
         }
         emailBody.Append($@"<table><thead>

    <tr>
        <th style=""text-align:left; padding:10px"" colspan=""4"">Vehicle</th>
        <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""{metricsSpan}"">Metrics</th>  		
        <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""3"">Existing Price</th>  
        <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""{newPriceSpan}"">New Price</th>  
        <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""4"">Change</th>  
        <th style=""border-left:1px solid #808080;padding:3px 10px; text-align:left"" colspan=""1""></th>  
    </tr>

    <tr>
        <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Reg</th>
        <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Make</th>
        <th style=""min-width: 100px;width: 100px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Model</th>
        <th style=""min-width: 300px;width: 300px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Description</th>");

         if (includeAgeOwnersColour)
         {
            emailBody.Append($@"
            <th style=""min-width: 50px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Age and Owners</th>
            <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Colour</th>
<th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Valuation</th>
");
         }
         else
         {
            emailBody.Append($@"
        <th style=""min-width: 50px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Valuation</th>");
         }


         if (daysMeasure == "DaysInStock")
         {
            emailBody.Append($@"<th style=""min-width: 20px;width: 20px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Days in stock</th>");
         }
         else if (daysMeasure == "DaysListed")
         {
            emailBody.Append($@"<th style=""min-width: 20px;width: 20px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Days listed</th>");
         }

         emailBody.Append($@"
         <th style=""min-width: 20px;width: 20px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">RR</th>
");
         if (includeRankingCols)
         {
            emailBody.Append($@"
         <th style=""min-width: 20px;width: 20px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">PP% Rank</th>
        <th style=""min-width: 20px;width: 20px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Value Rank</th>
");
         }
         emailBody.Append($@"
        <th style=""min-width: 50px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Price</th>
        <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Indicator</th>
        <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Days to Sell</th>
        <th style=""min-width: 50px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Price</th>
");

         if (includeExAdminFeeCol)
         {
            emailBody.Append($@"<th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Excl Admin</th>");
         }

         emailBody.Append($@"<th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Indicator</th>
        <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Days to Sell</th>
        <th style=""min-width: 60px;width: 60px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Up £</th>
        <th style=""min-width: 40px;width: 40px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Up %</th>
        <th style=""min-width: 60px;width: 60px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Down £</th>
        <th style=""min-width: 40px;width: 40px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Down %</th>
        ");

         if (includeRecentAppearances)
         {
            emailBody.Append($@"
         <th style=""min-width: 50px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Appearances</th>
         <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Last change</th>
         <th style=""min-width: 50px;width: 50px; text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">When</th>
");
         }

         emailBody.Append($@"
         <th style=""min-width: 200px;width: 50px; border-left:1px solid #808080;text-align:left; padding:3px 10px;border-bottom: 1px solid #808080"">Notes</th>
    </tr>
</thead>
<tbody>");
      }

      private static void OptOutTableBodyRows(StringBuilder emailBody, IGrouping<int, VehicleOptOutSummaryItemNew> optOuts)
      {

         if (optOuts.Count() > 0)
         {

            foreach (var optOut in optOuts)
            {

               int daysOptedOutFor = (DateTime.Now - optOut.OptOutCreatedDate).Days;
               int daysOptedOutToGo = (optOut.OptOutEndDate - DateTime.Now).Days;

               string lastChangeMessage = optOut.TotalChangesCount > 0 ? GetTimeDifferenceString((DateTime)optOut.LastChangeDate) : "No changes made";

               emailBody.Append($@"<tr style=""font-size:14px;"">

	<td style=""white-space: nowrap; padding:1px 10px;"" > {optOut.VehicleReg}</td>
    <td style=""white-space: nowrap; padding:1px 10px;"" > {optOut.Make}</td> 
    <td style=""white-space: nowrap; padding:1px 10px;"" > {optOut.Model}</td>
	<td style="" white-space: nowrap;padding:1px 10px;"" >{optOut.Derivative}</td>
	
	<td  style="" border-left:1px solid #808080;padding:1px 10px;"" > {optOut.DaysListed}</td>
	<td  style="" border-left:1px solid #808080;padding:1px 10px;"" > {optOut.OptOutCreatedDate.ToString("dd-MMM-yy")}</td>
    <td  style=""white-space: nowrap; padding:1px 10px;"" > {daysOptedOutFor} </td>
	<td   style=""white-space: nowrap; padding:1px 10px;"" > {optOut.OptOutPerson} </td>
	
	<td  style=""white-space: nowrap; border-left:1px solid #808080;padding:1px 10px;"" > {optOut.OptOutEndDate.ToString("dd-MMM-yy")}</td>
	<td  style=""white-space: nowrap; padding:1px 10px;"" > {daysOptedOutToGo}</td>

	<td  style=""white-space: nowrap; border-left:1px solid #808080;padding:1px 10px;"" > {optOut.RetailRating}</td>
	<td  style=""white-space: nowrap; padding:1px 10px;"" > {FormatAsCurrency(optOut.StrategyPrice)} </td>
	
<td  style=""white-space: nowrap; border-left:1px solid #808080;padding:1px 10px;"" > {FormatAsCurrency(optOut.SellingPrice)}</td>
<td  style=""white-space: nowrap; padding:1px 10px;"" > {FormatAsCurrency((optOut.SellingPrice - optOut.StrategyPrice))}</td>
<td  style=""white-space: nowrap; padding:1px 10px;"" > {lastChangeMessage}</td>
<td  style=""white-space: nowrap; border-left:1px solid #808080;padding:1px 10px;"" > {optOut.LastComment}</td>
	
	
</tr>");

            }
         }
         else
         {
            emailBody.Append("No price changes");
         }
      }

      private static void DetailTableBodyRows(StringBuilder emailBody, IEnumerable<PricingChangeNew> allSiteChanges, RetailerSiteStrategyBandingDefinition bandingDefinition, bool includeExAdminCol, string daysMeasure, bool includeRecentAppearances, bool includeAgeOwnersColour, bool includeRankingCols)
      {


         if (allSiteChanges.Count() > 0)
         {

            foreach (var priceChange in allSiteChanges)
            {
               //public static string ProvidePriceVsStrategyBanding(decimal strategyPrice, int? suppliedPrice, int? valuation)
               var vsStrategyBanding = BandingsService.ProvidePriceVsStrategyBanding(priceChange.NewPrice, (int)(priceChange.WasPrice ?? 0), (int)(priceChange.WasPrice ?? 0), bandingDefinition);

               string normalCell = @"white-space: nowrap; padding:1px 10px;";
               string firstCell = @"border-left:1px solid #808080;";
               string textRight = @"text-align:right;";

               string colouredCell = firstCell;
               if (vsStrategyBanding == "VeryUnderPriced" || vsStrategyBanding == "VeryOverPriced")
               { colouredCell = "background: rgb(255,77,77);"; }
               if (vsStrategyBanding == "UnderPriced" || vsStrategyBanding == "OverPriced")
               { colouredCell = "background: rgb(255,192,0);"; }
               if (vsStrategyBanding == "OnStrategyPrice")
               { colouredCell = "background: rgb(0, 176, 80);"; }


               emailBody.Append($@"<tr style=""font-size:14px;"">

	<td style=""{normalCell}"" > {priceChange.VehicleReg}</td>
    <td style=""{normalCell}"" >{priceChange.Make}</td>
    <td style=""{normalCell}"" >{priceChange.Model}</td>
	<td style=""{normalCell}"" > {priceChange.Derivative}</td>
");


               if (includeAgeOwnersColour)
               {
                  emailBody.Append($@"
<td  style=""{normalCell}{firstCell}{textRight}"" > {priceChange.AgeAndOwners}</td>
<td  style=""{normalCell}{textRight}"" > {priceChange.SpecificColour}</td>
<td  style=""{normalCell}{textRight}"" > {FormatAsCurrency(priceChange.ValuationAdjustedRetail)}</td>
");
               }
               else
               {
                  emailBody.Append($@"
<td  style=""{normalCell}{firstCell}{textRight}"" > {FormatAsCurrency(priceChange.ValuationAdjustedRetail)}</td>
");
               }

               if (daysMeasure == "DaysListed")
               {
                  emailBody.Append($@"<td  style=""{normalCell}{textRight}"" > {Format0Dp(priceChange.DaysListed)}</td>");
               }
               else if (daysMeasure == "DaysInStock")
               {
                  emailBody.Append($@"<td  style=""{normalCell}{textRight}"" > {Format0Dp(priceChange.DaysInStock)}</td>");
               }


               emailBody.Append($@"
<td  style=""{normalCell}{textRight}"" > {Format0Dp(priceChange.RetailRating)}</td>
");
               if (includeRankingCols)
               {
                  emailBody.Append($@"
<td  style=""{normalCell}{textRight}"" > {Format0Dp(priceChange.OurPPRank)}</td>
<td  style=""{normalCell}{textRight}"" > {Format0Dp(priceChange.OurValueRank)}</td>
");
               }
               emailBody.Append($@"
<td  style=""{normalCell}{firstCell}{textRight}"" > {FormatAsCurrency((priceChange.WasPrice ?? 0))}</td>
<td  style=""{normalCell}{textRight}"" > {priceChange.PriceIndicatorRatingAtCurrentSelling}</td>
<td   style=""{normalCell}{textRight}"" > {Format0Dp(priceChange.DaysToSellAtCurrentSelling)}</td>
<td  style=""{normalCell}{firstCell}{textRight}"" > {FormatAsCurrency(priceChange.NewPrice)}</td>
");
               if (includeExAdminCol)
               {
                  emailBody.Append($@"<td  style=""{normalCell}{textRight}"" > {FormatAsCurrency(priceChange.NewPriceExclAdminFee)}</td>");
               }
               emailBody.Append($@"  <td  style=""{normalCell}{textRight}"" > {priceChange.NewPriceIndicatorRating}</td>
	<td   style=""{normalCell}{textRight}"" > {Format0Dp(priceChange.NewDaysToSell)}</td>

	<td  style=""{normalCell}{firstCell}{(priceChange.ChangeValueUp > 0 ? colouredCell : null)}{textRight}"" > {(priceChange.ChangeValueUp > 0 ? FormatAsCurrencyWithLeadingSign(priceChange.ChangeValueUp) : null)} </td>
	<td  style=""{normalCell}{(priceChange.ChangeValueUp > 0 ? colouredCell : null)}{textRight}"" > {(priceChange.ChangeValueUp > 0 ? FormatAsPercentage(priceChange.ChangePercentUp) : null)}</td>
    <td  style=""{normalCell}{(priceChange.ChangeValueDown < 0 ? colouredCell : null)}{textRight}"" > {(priceChange.ChangeValueDown < 0 ? FormatAsCurrencyWithLeadingSign(priceChange.ChangeValueDown) : null)} </td>
	<td  style=""{normalCell}{(priceChange.ChangeValueDown < 0 ? colouredCell : null)}{textRight}"" > {(priceChange.ChangeValueDown < 0 ? FormatAsPercentage(priceChange.ChangePercentDown) : null)}</td>


	");

               if (includeRecentAppearances)
               {
                  emailBody.Append($@"
                     <td  style=""{normalCell}{firstCell}"" > {Format0Dp(priceChange.PrevAppearances)} </td>
                     <td  style=""{normalCell}"" > {FormatAsCurrencyWithLeadingSign(priceChange.MostRecentDailyPriceMove)} </td>
                     <td  style=""{normalCell}"" > {FormatAsDateddMMMyyyy(priceChange.MostRecentDailyPriceMoveDate)} </td>
                  ");
               }


               emailBody.Append($@"
<td  style=""{normalCell}{firstCell}"" > {priceChange.LastComment} </td>
</tr>");

            }
         }
         else
         {
            emailBody.Append("No price changes");
         }
      }










      private static string FormatAsCurrency(decimal value)
      {
         string formattedPrice = string.Format("{0:£#,##0}", Math.Abs(value));
         formattedPrice = value < 0 ? "-" + formattedPrice : formattedPrice;
         return formattedPrice;
      }
      private static string FormatAsCurrencyWithLeadingSign(decimal value)
      {
         // Format the absolute value with the currency symbol
         string formattedPrice = string.Format("£{0:#,##0}", Math.Abs(value));

         // Prepend "+" for positive values, "-" for negative values, nothing for zero
         formattedPrice = value < 0 ? "-" + formattedPrice : "+" + formattedPrice;

         return formattedPrice;
      }

      private static string FormatAsPercentage(decimal value)
      {
         string formattedPercentage = string.Format("{0:P1}", value);
         return formattedPercentage;
      }

      private static string Format1Dp(decimal value)
      {
         try
         {
            return string.Format("{0:F1}", value);
         }
         catch (Exception)
         {
            { }
            return "err";
         }
      }
      private static string Format2Dp(decimal value)
      {
         try
         {
            return string.Format("{0:F2}", value);
         }
         catch (Exception)
         {
            { }
            return "err";
         }
      }
      private static string Format0Dp(decimal value)
      {
         return string.Format("{0:F0}", value);
      }

      private static string FormatAsDateddMMMyyyy(DateTime? value)
      {
         if (value.HasValue)
         {
            return value.Value.Date.ToString("dd MMM");
         }
         else
         {
            return null;
         }
      }

      public static string GetTimeDifferenceString(DateTime optOutDate)
      {
         // Assuming you want the difference in days based on dates and not specific time
         TimeSpan difference = DateTime.Today - optOutDate.Date;

         int daysDifference = difference.Days;

         if (daysDifference == 0)
         {
            return "today";
         }
         else if (daysDifference == 1)
         {
            return "1 day ago";
         }
         else
         {
            return $"{daysDifference} days ago";
         }
      }

   }
}
