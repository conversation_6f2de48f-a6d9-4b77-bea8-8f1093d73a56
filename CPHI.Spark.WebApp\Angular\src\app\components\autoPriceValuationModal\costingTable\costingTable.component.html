<div class="d-flex flex-column h-100">
    <div class="costsTableHolder">
        <table id="costsTable">
            <tbody>
                <tr>
                    <td>
                        <label for="costs">Is Vat Qualifying</label>
                    </td>
                    <td>
                        <sliderSwitch (toggle)="toggleIsVatQualifying(!service.valuationCosting.isVatQualifying)"
                            [defaultValue]="service.valuationCosting.isVatQualifying">
                        </sliderSwitch>
                    </td>
                    <td>

                    </td>
                </tr>

                <tr>
                    <td>
                        <label for="costs">PP %</label>
                    </td>
                    <td>
                        <div *ngIf="service.amCalculating">
                            Calculating..
                        </div>


                        <ng-container *ngIf="!service.amCalculating">
                            <div *ngIf="showPriceIndicator()" class="priceIndicatorLozenge floatRight"
                                [ngClass]="service.valuationModalResultNew.ValuationPriceSet.PriceIndicator">
                                {{ service.valuationModalResultNew.ValuationPriceSet.PriceIndicator | titlecase }}
                            </div>
                            <div *ngIf="service.valuationCosting.pricePosition < 0.9">
                                Not available as price position is below 90%
                            </div>
                            <div *ngIf="service.valuationCosting.pricePosition > 1.1">
                                Not available as price position is above 110%
                            </div>
                        </ng-container>
                    </td>
                    <td>
                        <input id="sales" (input)="handleInputChange($event,ValuationCostingFieldEnum.PricePosition)"
                            [ngClass]="{'flash-green': flashState['PricePosition']}"
                            [ngModel]="service.valuationCosting.pricePosition | cph:'percent':1"
                            [placeholder]="'Enter pricePosition'">
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="costs">Sell price</label>
                    </td>
                    <td>

                        <div *ngIf="service.amCalculating">
                            Calculating..
                        </div>
                        <ng-container *ngIf="!service.amCalculating">
                            <span *ngIf="service.valuationModalResultNew.LocationStrategyPrices[0]?.DaysToSell">
                                {{ service.valuationModalResultNew.LocationStrategyPrices[0]?.DaysToSell }} days to sell
                            </span>
                            <span *ngIf="!service.valuationModalResultNew.LocationStrategyPrices[0]?.DaysToSell">
                                Days to sell unavailable
                            </span>
                        </ng-container>

                    </td>
                    <td>
                        <input id="sales" (input)="handleInputChange($event, ValuationCostingFieldEnum.Sales)"
                            [ngClass]="{'flash-green': flashState['Sales']}"
                            [ngModel]="service.valuationCosting.sales | cph:'currency':0" [placeholder]="'Enter sales'">
                    </td>
                </tr>
                <tr *ngIf="service.chosenRetailerSite?.AdminFee > 0">
                    <td class="italic textLeft" colspan="3">Includes Admin Fee of {{ service.chosenRetailerSite?.AdminFee | cph:'currency':0 }}</td>
                <tr>
                    <td colspan="3">&nbsp;</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div id="costsScrollable">
        <table>
            <tbody>
                <tr>
                    <td colspan="2">
                        Valet
                    </td>
                    <td>
                        <input id="valet" (input)="handleInputChange($event,ValuationCostingFieldEnum.Valet)"
                            [ngClass]="{'flash-green': flashState['Valet']}"
                            [ngModel]="service.valuationCosting.valet | cph:'currencyNoDash':0"
                            [placeholder]="'Enter valet'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Spare Key</td>
                    <td>
                        <input id="spareKey" (input)="handleInputChange($event, ValuationCostingFieldEnum.SpareKey)"
                            [ngClass]="{'flash-green': flashState['SpareKey']}"
                            [ngModel]="service.valuationCosting.spareKey | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">MOT</td>
                    <td>
                        <input id="mot" (input)="handleInputChange($event, ValuationCostingFieldEnum.MOT)"
                            [ngClass]="{'flash-green': flashState['MOT']}"
                            [ngModel]="service.valuationCosting.mot | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">MOT Advisories</td>
                    <td>
                        <input id="motAdvisory"
                            (input)="handleInputChange($event, ValuationCostingFieldEnum.MOTAdvisory )"
                            [ngClass]="{'flash-green': flashState['MOTAdvisory']}"
                            [ngModel]="service.valuationCosting.motAdvisory | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Service</td>
                    <td>
                        <input id="servicing" (input)="handleInputChange($event, ValuationCostingFieldEnum.Servicing)"
                            [ngClass]="{'flash-green': flashState['Servicing']}"
                            [ngModel]="service.valuationCosting.servicing | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Paintwork</td>
                    <td>
                        <input id="paint" (input)="handleInputChange($event, ValuationCostingFieldEnum.Paint)"
                            [ngClass]="{'flash-green': flashState['Paint']}"
                            [ngModel]="service.valuationCosting.paint | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Tyres</td>
                    <td>
                        <input id="tyres" (input)="handleInputChange($event, ValuationCostingFieldEnum.Tyres )"
                            [ngClass]="{'flash-green': flashState['Tyres']}"
                            [ngModel]="service.valuationCosting.tyres | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Warranty</td>
                    <td>
                        <input id="warranty" (input)="handleInputChange($event, ValuationCostingFieldEnum.Warranty)"
                            [ngClass]="{'flash-green': flashState['Warranty']}"
                            [ngModel]="service.valuationCosting.warranty | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Parts</td>
                    <td>
                        <input id="parts" (input)="handleInputChange($event, ValuationCostingFieldEnum.Parts)"
                            [ngClass]="{'flash-green': flashState['Parts']}"
                            [ngModel]="service.valuationCosting.parts | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Additional Mech.</td>
                    <td>
                        <input id="additionalMech"
                            (input)="handleInputChange($event, ValuationCostingFieldEnum.AdditionalMech)"
                            [ngClass]="{'flash-green': flashState['AdditionalMech']}"
                            [ngModel]="service.valuationCosting.additionalMech | cph:'currencyNoDash':0"
                            [placeholder]="'Enter cost'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Fee</td>
                    <td>
                        <input id="fee" (input)="handleInputChange($event, ValuationCostingFieldEnum.Fee)"
                            [ngClass]="{'flash-green': flashState['Fee']}"
                            [ngModel]="service.valuationCosting.fee | cph:'currencyNoDash':0"
                            [placeholder]="'Enter fee'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Delivery</td>
                    <td>
                        <input id="delivery" (input)="handleInputChange($event, ValuationCostingFieldEnum.Delivery)"
                            [ngClass]="{'flash-green': flashState['Delivery']}"
                            [ngModel]="service.valuationCosting.delivery | cph:'currencyNoDash':0"
                            [placeholder]="'Enter delivery'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">Other</td>
                    <td>
                        <input id="other" (input)="handleInputChange($event,  ValuationCostingFieldEnum.Other)"
                            [ngClass]="{'flash-green': flashState['Other']}"
                            [ngModel]="service.valuationCosting.other | cph:'currencyNoDash':0"
                            [placeholder]="'Enter costs'">
                    </td>
                </tr>
                <tr>
                    <td colspan="2">VAT Cost</td>
                    <td>
                        <input id="vatCost" (input)="handleInputChange($event,  ValuationCostingFieldEnum.VatCost)"
                            [disabled]="true" [ngClass]="{'flash-green': flashState['VatCost']}"
                            [ngModel]="service.valuationCosting.vatCost | cph:'currencyNoDash':0"
                            [placeholder]="'Enter vat'">
                    </td>
                </tr>
            </tbody>
        </table>
    </div>


    <div class="costsTableHolder">
        <table>
            <tbody>
                <tr>
                    <td colspan="3">&nbsp;</td>
                </tr>
                <tr class="boldRow">
                    <td colspan="2">
                        <label for="costs">Maximum Buy Price</label>
                    </td>
                    <td>
                        <input id="costs" (input)="handleInputChange($event, ValuationCostingFieldEnum.Cost)"
                            [ngClass]="{'flash-green': flashState['Cost']}"
                            [ngModel]="service.valuationCosting.cost | cph:'currency':0" [placeholder]="'Enter costs'">

                    </td>
                </tr>
                <tr>
                    <td colspan="3">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="2">
                        Profit
                    </td>
                    <td>
                        <input id="profit" (input)="handleInputChange($event, ValuationCostingFieldEnum.Profit)"
                            [ngClass]="{'flash-green': flashState['Profit']}"
                            [ngModel]="service.valuationCosting.profit | cph:'currency':0"
                            [placeholder]="'Enter profit'">
                    </td>
                </tr>
            </tbody>
        </table>
    </div>