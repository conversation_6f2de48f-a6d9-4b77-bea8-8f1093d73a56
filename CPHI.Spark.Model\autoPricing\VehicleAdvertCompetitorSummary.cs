﻿using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CPHI.Spark.Model
{
   public class VehicleAdvertCompetitorSummary
   {
      public VehicleAdvertCompetitorSummary(VehicleListing listing, VehicleAdvertWithRating advert, SiteVM site, int radius)
      {
         try
         {

            AdvertDetailsForCompetitorList details = new AdvertDetailsForCompetitorList(advert);
            int valuation = (int)(advert.ValuationAdjRetail ?? advert.ValuationMktAvRetail);
            CompetitorSummary competitorSummary = ConstantMethodsService.BuildCompetitorSummary(
              details,
              (int)advert.AdvertisedPrice,
              valuation,
              site.GeoY,
              site.GeoX,
              listing,
              radius,
              advert.RetailerSiteRetailerId,
              advert.RetailerType);

            decimal ToThreeDps(decimal valIn)
            {
               return Math.Round(valIn * 1000m, MidpointRounding.AwayFromZero) / 1000m;
            }

            string ProvideSellerType(bool isOurAdvert, string segment)
            {
               Dictionary<string, string> SellerTypes = new Dictionary<string, string>()
                {
                    {"Franchise","F" },
                    {"Independent","I" },
                    {"Private","P" },
                };

               if (isOurAdvert) { return "O"; }
              ;
               try
               {
                  return SellerTypes[segment];
               }
               catch (Exception e)
               {
                  return "Unknown";
               }
            }

            decimal totalPP = 0;
            decimal independentsPP = 0;
            decimal franchisedPP = 0;
            decimal supermarketsPP = 0;
            decimal privatesPP = 0;


            int competitorCount = 0;
            int independentCount = 0;
            int franchisedCount = 0;
            int supermarketCount = 0;
            int privateCount = 0;

            // Only veh & therefore cheapest veh
            if (competitorSummary == null || competitorSummary.CompetitorVehicles == null)
            {
               AdvertId = advert.AdId;
               VehicleAdvertSnapshotId = advert.SnapshotId;
               competitorCount = 1;
               CheapestVehicle = true;
               OnlyVehicle = true;
               OurPPRank = 1;
               return;
            }

            var sorted = competitorSummary.CompetitorVehicles
              .Where(x => x.PricePosition > 0)
              .OrderBy(x => x.PricePosition).ToList();

            foreach (var item in sorted)
            {
               competitorCount++;
               totalPP += item.PricePosition;

               if (item.Segment == "Independent") { 
                  independentsPP += item.PricePosition;
                  independentCount++;
               }
               else if (item.Segment == "Supermarket") { 
                  supermarketsPP += item.PricePosition; 
                  supermarketCount++;
               }
               else if (item.Segment == "Private" || item.Segment==null) { 
                  privatesPP += item.PricePosition;
                  privateCount++;
               }
               else if (item.Segment == "Franchise") {
                  franchisedPP += item.PricePosition; 
                  franchisedCount++;
               }
               else
               {
                  { }
               }
            }

            // if(advert.VehicleReg == "BP73OLH")
            // {
            //     //we should be cheapest as we are unique.  And veh shld be uniqeu
            //     { }
            // }

            decimal avg = competitorCount > 0 ? totalPP / competitorCount : 0;
            decimal independentAvg = independentCount > 0 ? independentsPP / independentCount : 0;
            decimal franchisedAvg = franchisedCount > 0 ? franchisedPP / franchisedCount : 0;
            decimal supermarketAvg = supermarketCount > 0 ? supermarketsPP / supermarketCount : 0;
            decimal privateAvg = privateCount > 0 ? privatesPP / privateCount : 0;




            decimal variance = sorted.Average(x => (x.PricePosition - avg) * (x.PricePosition - avg));
            decimal standardDeviation = (decimal)Math.Sqrt((double)variance);
            string cheapestSellerType = ProvideSellerType(sorted.First().IsOurVehicle, sorted.First().Segment);

            AdvertId = advert.AdId;
            VehicleAdvertSnapshotId = advert.SnapshotId;
            CompetitorCount = competitorCount;
            AveragePP = ToThreeDps(avg);
            StDevPP = ToThreeDps(standardDeviation);
            HighestPP = ToThreeDps(sorted.Count > 0 ? sorted.Last().PricePosition : 0);
            LowestPP = ToThreeDps(sorted.Count > 0 ? sorted.First().PricePosition : 0);
            OurPPRank = sorted.FindIndex(x => x.IsOurVehicle) + 1;
            OurValueRank = CalculateOurValueRank(competitorSummary);
            CheapestSellerName = cheapestSellerType == "P" ? "Private" : sorted.First().CompetitorName;
            CheapestSellerType = cheapestSellerType;
            OnlyVehicle = competitorCount == 1;
            CheapestVehicle = OurPPRank == 1;// && competitorCount > 1;

            PPAverageFranchised = ToThreeDps(franchisedAvg);
            PPAverageIndependents = ToThreeDps(independentAvg);
            PPAverageSupermarkets = ToThreeDps(supermarketAvg);
            PPAveragePrivates = ToThreeDps(privateAvg);

            if (OurPPRank < competitorCount && advert.ValuationAdjRetail.HasValue)
            {
               var nextCheapest = sorted[OurPPRank]; //our rank + 1, converted to zero indexed so -1
               var newPrice = (nextCheapest.PricePosition * (int)advert.ValuationAdjRetail) - 1; //we can go to the competitor with the next highest price position x our Valn - £1
               PriceUpMaintainRank = newPrice - advert.AdvertisedPrice;
            }
            else
            {
               //we must be last.   We cannot price up.
               PriceUpMaintainRank = 0;
            }

            if (OurPPRank > 1)
            {
               //we are not cheapest.   

               //What do we have to do to improve one spot
               var nearestCheaperCompetitor = sorted[OurPPRank - 2]; //our rank - 1, convert to zero indexed so -1
               var newPrice = (nearestCheaperCompetitor.PricePosition * (int)advert.ValuationAdjRetail) - 1;
               PriceDownImproveRank = newPrice - advert.AdvertisedPrice;

               //What do we have to do to become cheapest
               var cheapest = sorted[0];
               var newPriceToBeCheapest = (cheapest.PricePosition * (int)(advert.ValuationAdjRetail) - 1); ;

               if (cheapest.PricePosition == 0 || newPriceToBeCheapest < 10)
               {
                  PriceToBeCheapest = 0;
               }
               else
               {
                  PriceToBeCheapest = newPriceToBeCheapest - advert.AdvertisedPrice;
               }
            }

            string allPrices = string.Empty;
            List<string> allPriceItems = new List<string>();
            foreach (var item in sorted)
            {
               allPriceItems.Add($"{ProvideSellerType(item.IsOurVehicle, item.Segment)}_{ToThreeDps(item.PricePosition)}");
            }
            AllPrices = string.Join(',', allPriceItems);
         }
         catch (Exception ex)
         {
            { }
         }

      }

      // Find where the vehicle ranks based on advertised price alone
      private int CalculateOurValueRank(CompetitorSummary competitorSummary)
      {
         var sorted = competitorSummary.CompetitorVehicles.OrderBy(x => x.AdvertisedPrice).ToList();
         return sorted.FindIndex(x => x.IsOurVehicle) + 1; ;
      }

      public int AdvertId { get; set; }
      public int VehicleAdvertSnapshotId { get; set; }
      public int CompetitorCount { get; set; }
      public decimal? AveragePP { get; set; }
      public decimal? StDevPP { get; set; }
      public decimal? HighestPP { get; set; }
      public decimal? LowestPP { get; set; }
      public int OurPPRank { get; set; }
      public int OurValueRank { get; set; }
      public string CheapestSellerName { get; set; }
      public string CheapestSellerType { get; set; }
      public bool CheapestVehicle { get; set; }
      public bool OnlyVehicle { get; set; }
      public string AllPrices { get; set; }

      public decimal PriceUpMaintainRank { get; set; }
      public decimal PriceDownImproveRank { get; set; }
      public decimal PriceToBeCheapest { get; set; }

      //SPK-5470
      public decimal PPAverageIndependents { get; set; }
      public decimal PPAverageFranchised { get; set; }
      public decimal PPAverageSupermarkets { get; set; }
      public decimal PPAveragePrivates { get; set; }
   }
}
