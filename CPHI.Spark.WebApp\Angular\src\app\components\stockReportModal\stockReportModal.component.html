<ng-template #stockReportModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
     {{selections.stockReportModal.headerName}}
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div [ngClass]="constants.environment.customer" class="modal-body vehicleDetailsModal">

    <div class="spaceBetween chartAndTableHolder">


      <div id="charts">

        <chart></chart>
        <!-- <canvas id="chartCanvas" #myChart></canvas> -->
        <!-- chart -->

        <div *ngIf="selections.stockReport?.report?.name=='OverAge Vehicles' || selections.stockReport?.report?.name=='Overage Stock'" id="goingOverSoon">

          <!-- The going over in 30 bar graph -->
          <div id="goingOverChart">

            <div id="titleSection">

              <h3>
                {{constants.pluralise(selections.stockReportModal?.totalVehicleCount, this.constants.translatedText.Vehicle, this.constants.translatedText.Vehicles)}}
                going over-age in the next 30 days</h3>
            </div>
            <div id="barHolder">
              <!-- (click)="showModalForVehiclesGoingOver(30-i,bar)" -->
              <div class="bar" *ngFor="let bar of selections.stockReportModal?.overIn30; let i = index"
                
                [ngStyle]="{'height.%': bar.length > 1 ? bar.length / selections.stockReportModal.maxOverIn30BarLength *100 : 0}">
                <div class="label">
                  <span *ngIf="bar.length>1"> {{bar.length-1}}</span>
                </div>
              </div>
            </div>
            <div id="xAxis">
              <div class="xAxisLabel" *ngFor="let bar of selections.stockReportModal?.overIn30; let i = index">
                <div class="xAxisLabelText">
                  {{30-i}}
                </div>
              </div>
              <div id="xAxisLabel">Days to go until vehicle is overage</div>
            </div>
          </div>
        </div>
      </div>


      <!-- The table -->

      <div id="tableHolder">

        <stockReportVehicleTable  [showUsedCols]="true" [rowData]="selections.stockReportModal.stocks" (clickedRow)="loadVehicle($event)"></stockReportVehicleTable>
      </div>


      
    </div>


  </div>
  <div class="modal-footer">

    <!-- <button type="button" class="btn btn-success" (click)="modal.close('Save')">OK</button> -->
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
  </div>

</ng-template>