﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using OpenQA.Selenium.Interactions;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Xunit;
using CPHI.WebScraper.ViewModel;
using System.Diagnostics;
using System.Threading.Tasks;

namespace CPHI.Spark.WebScraper.Jobs
{
    public  class DealScrapeDailyJob : IJob
    {
        private static readonly ILog logger = LogManager.GetLogger(typeof(DealScrapeDailyJob));
        private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
        private string customerName;
        private string fileDestination;
        //private string fileDestinationDev;

        public void Execute() { }
        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            fileDestination = ConfigService.FileDestination;
            //fileDestinationDev = ConfigService.FileDestinationDev;
            fileDestination = fileDestination.Replace("{destinationFolder}", "vindis");
            //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "vindis");
            customerName = "Vindis";

            try
            {

                ScraperMethodsService.ClearDownloadsFolder();

                //chromeInstances = Process.GetProcessesByName("chrome");
                //foreach (Process p in chromeInstances) p.Kill();

                //chromeDriverInstances = Process.GetProcessesByName("chromedriver");
                //foreach (Process p in chromeDriverInstances) p.Kill();

                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";

                ChromeOptions options = ScraperMethodsService.SetChromeOptions("VindisDealScrapeDaily", 9223);

                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

                try
                {
                    GetDeals();

                    _driver.Quit();
                    _driver.Dispose();
                    stopwatch.Stop();
                }
                catch 
                {
                    //Emailer.SendMail("Vindis main deal scraper failed", $"{e.StackTrace}");
                }


                //Emailer.SendMail("Vindis main deal scraper succeeded", "Have a nice day!");

            }
            catch (Exception e)
            {
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");

                stopwatch.Stop();
                errorMessage = e.ToString();
                //Emailer.SendMail("Vindis main deal scraper failed", $"{e.StackTrace}");

                logger.Error($"Problem {e.ToString()}");

                _driver.Quit();
                _driver.Dispose();

                //chromeInstances = Process.GetProcessesByName("chrome");
                //foreach (Process p in chromeInstances) p.Kill();

                //chromeDriverInstances = Process.GetProcessesByName("chromedriver");
                //foreach (Process p in chromeDriverInstances) p.Kill();
            }
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WebScraper",
                    Customer = "Vindis",
                    Environment = "PROD",
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };
                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }

            
        }










        private void GetDeals()
        {


            try
            {

                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

                //go to login page
                _driver.Navigate().GoToUrl("https://enquirymax.net/account/logon");

                //wait for it to appear

                System.Threading.Thread.Sleep(1000);

                IWebElement loginButton = wait.Until(ExpectedConditions.ElementExists(By.Id("UserName")));

                System.Threading.Thread.Sleep(1000);

                Assert.Equal("Login - enquiryMAX", _driver.Title);

                System.Threading.Thread.Sleep(1000);

                WaitAndFind("//input [@Id='UserName']", false).SendKeys("james.smith");
                System.Threading.Thread.Sleep(2000);
                WaitAndFind("//input [@Id='NextButton']", true);
                //WaitAndFind("//input [@Id='UserName']", false).SendKeys(Keys.Tab);
                System.Threading.Thread.Sleep(2000);
                WaitAndFind("//input [@Id='Password']", false).SendKeys(ConfigService.EnquiryMAXPassword);
                System.Threading.Thread.Sleep(2000);
                WaitAndFind("//input [@Id='SubmitButton']", true);
                System.Threading.Thread.Sleep(2000);
                //wait for links to show
                IWebElement dashboardLink = wait.Until(ExpectedConditions.ElementExists(By.ClassName("top-navbar")));
                System.Threading.Thread.Sleep(1000);

                //bypass a popup
                IWebElement dontShowSystemMessageAgain = null;
                try
                {
                    dontShowSystemMessageAgain = WaitAndFind("//input [@Id='DontShowSystemMessageAgain']", false);
                }
                catch { };

                if (dontShowSystemMessageAgain != null) 
                { 
                    dontShowSystemMessageAgain.Click();
                    WaitAndFind("//input [@value='OK']", true);
                };
                

                System.Threading.Thread.Sleep(1000);

                _driver.Navigate().GoToUrl("https://enquirymax.net/Report/DOC#!/");

                IWebElement datatableWrapper = wait.Until(ExpectedConditions.ElementExists(By.ClassName("repohead-title")));

                foreach (DealSelection dealSelection in dealSelections)
                {
                    System.Threading.Thread.Sleep(10000);

                    for (int i = 0; i <= 9; i++)
                    {
                        string year = DateTime.Now.AddYears(-1).AddMonths(i * -1).Year.ToString();
                        string month = monthStringLookup[DateTime.Now.AddMonths(i * -1).Month];

                        applyOneFilter("IsNew", dealSelection.IsNew);
                        applyOneFilter("CustomerType", dealSelection.CustomerType);
                        applyOneFilter("Year", year);
                        applyOneFilter("Month", month);

                        DateTime start = DateTime.Now;
                        var exportBut = WaitAndFind("//input [@value='Export']", true);

                        string downloadPath = ConfigService.FileDownloadLocation;

                        ScraperMethodsService.WaitUntilFileDownloaded("Export");

                        System.Threading.Thread.Sleep(2000);

                        logger.Info($"Succesfully saved down export file for report {dealSelection.ReportName}");

                        DirectoryInfo directory = new DirectoryInfo(downloadPath);
                        FileInfo generatedFile = directory.GetFiles().Where(x => x.LastWriteTime > start && x.Name.Contains("Export")).First();

                        string newFilePathAndName = downloadPath + @"\" + dealSelection.ReportName + ".xlsx";
                        //rename the file
                        File.Move(generatedFile.FullName, newFilePathAndName);
                        logger.Info($"Succesfully changed name from {generatedFile.FullName} to {newFilePathAndName}");
                        //move to the incoming folder
                        moveFile(dealSelection.ReportName + ".xlsx");

                    }


                }
            }
            catch (Exception e)
            {
                //Emailer.SendMail("Vindis main deal scraper failed", $"{e.StackTrace}");
                logger.Info("Row122");
               logger.Error(e.Message);
                { }
            }
        }



        Dictionary<string, string> filterElementLookup = new Dictionary<string, string>()
        {
            {"CustomerType", "//div [@customer-type-list] //button"},
            {"Month", "//div [@month]"},
            {"Year", "//div [@year]"},
            {"IsNew", "//div [@is-new]"},
        };


        Dictionary<int, string> monthStringLookup = new Dictionary<int, string>()
        {
            {1, "January"},
            {2, "February"},
            {3, "March"},
            {4, "April"},
            {5, "May"},
            {6, "June"},
            {7, "July"},
            {8, "August"},
            {9, "September"},
            {10, "October"},
            {11, "November"},
            {12, "December"},
        };


        private List<DealSelection> dealSelections = new List<DealSelection>
        {
            new DealSelection { ReportName = "RETAIL_NEW_ORDERS_DAILY_SPKV1", CustomerType = "Retail", IsNew = "New"},
            new DealSelection { ReportName = "RETAIL_USED_ORDERS_DAILY_SPKV1", CustomerType = "Retail", IsNew = "Used"},
            new DealSelection { ReportName = "FLEET_ORDERS_DAILY_SPKV1", CustomerType = "Fleet", IsNew = "New / Used"},
            new DealSelection { ReportName = "COMMERCIAL_ORDERS_DAILY_SPKV1", CustomerType = "Commercial", IsNew = "New / Used"},
            new DealSelection { ReportName = "CORPORATE_ORDERS_DAILY_SPKV1", CustomerType = "Corporate", IsNew = "New / Used"},
            new DealSelection { ReportName = "MOTABILITY_ORDERS_DAILY_SPKV1", CustomerType = "Motability", IsNew = "New / Used"},
            new DealSelection { ReportName = "TRADE_ORDERS_DAILY_SPKV1", CustomerType = "Trade", IsNew = "New / Used"},
        };






        private IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "DealScrape", findXPath, andClick);

            return result;
        }












        private void moveFile(string fileName)
        {
            string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
            string newFilePathAndName = $@"{fileDestination}{prefix}{fileName}";
            //string newFilePathAndNameDev = $@"{fileDestinationDev}{prefix}{fileName}";

            string path = ConfigService.FileDownloadLocation;
            string oldLocation = path + @"\" + fileName;

            System.Threading.Thread.Sleep(2000);

            File.Move(oldLocation, newFilePathAndName); //move them to incoming
            //File.Copy(newFilePathAndName, newFilePathAndNameDev); //copy to dev

            logger.Info($"Moved file from {oldLocation} to {newFilePathAndName}");

            System.Threading.Thread.Sleep(2000);
        }





        private void applyOneFilter(string filterType, string filterSelection = null, List<string> filterSelections = null)
        {

            Actions keyPresser = new Actions(_driver);

            try
            {
                IWebElement filter = WaitAndFind(filterElementLookup[filterType]);
                filter.Click();

                if (filterType == "QuickDate" || filterType == "IsNew" || filterType == "Year" || filterType == "Month")
                {
                    IWebElement selection = WaitAndFind("//option[text() = '" + filterSelection + "']");
                    selection.Click();
                }
                else if (filterType == "CustomerType" || filterType == "Status")
                {
                    WaitAndFind(filterElementLookup[filterType] + "[contains(text(), 'Clear')]", true);

                    WaitAndFind("//span[contains(text(), '" + filterSelection + "')]", true);
                }
                else
                {
                    WaitAndFind(filterElementLookup[filterType] + "[contains(text(), 'Clear')]", true);

                    if (filterSelections != null)
                    {
                        foreach (string dealership in filterSelections)
                        {
                            WaitAndFind("//span[contains(text(), '" + dealership + "')]", true);
                        }
                    }

                }

                keyPresser.SendKeys(Keys.Escape).Build().Perform();
            }
            catch (Exception e)
            {
                //Emailer.SendMail("Vindis main deal scraper failed", $"{e.StackTrace}");
               logger.Error(e.Message);
            }
        }








    }
}
