<nav class="navbar">
  <nav class="generic">
    <div class="navbar-grid cols-22">

      <div class="grid-col-1-7">

        <div id="menuButtonHolder">

          <div id ="monthsAndFranchiseHolder">

            <span id="reportsLabel" class="d-flex align-items-center">{{constants.translatedText.Dashboard_AftersalesReports}}:</span>

            <div id="aftersalesButtons">
            
              <div class="buttonGroup">
                <ng-container *ngIf="service.chosenSection">
                  <button *ngFor="let page of service.chosenSection.pages" class="btn btn-primary subButton"
                    [id]="page.pageName + 'DashButton'"
                    [ngClass]="{'active':!!service.chosenPage && page.pageName === service.chosenPage?.pageName}"
                    (click)="choosePage(page)">{{page.translatedTextValue}}</button>
                </ng-container>
              </div>

            </div>

            <!-- FOR SELECTING MONTH -->
            <div class="buttonGroup">
              <!-- previousMonth -->
              <button class="btn btn-primary" (click)="service.changeMonth(-1)"><i
                  class="fas fa-caret-left"></i></button>

              <!-- dropdownMonth -->
              <div ngbDropdown class="d-inline-block" [autoClose]="true">

                <button class="btn btn-primary centreButton"
                  ngbDropdownToggle>{{service.chosenMonthStart|cph:'month':0}}</button>
                <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

                  <!-- the ngFor buttons -->
                  <button *ngFor="let month of service.months" (click)="service.selectMonth(month)"
                    ngbDropdownItem>{{month
                    |cph:'month':0}}</button>
                </div>
              </div>
              
              <!-- nextMonth -->
              <button class="btn btn-primary" (click)="service.changeMonth(1)" [disabled]="service.currentMonthSelected"><i
                  class="fas fa-caret-right"></i></button>
            </div>

          </div>

        </div>
      </div>

    </div>
  </nav>
</nav>

<!-- Main Page -->
<div *ngIf="!!dataPack" class="dashboard-grid-container sales stack-on-smaller-screens">
  <div class="dashboard-grid cols-14">
    <div class="dashboard-tile grid-col-1-4 grid-row-1-7">
      <serviceGuageTile [tileType]="'Month'" [includeCompareCommentary]="false" [data]="dataPack.ServiceGuageMonth" [dataSource]="'Quiter'"></serviceGuageTile>
    </div>
    <div class="dashboard-tile grid-col-4-7 grid-row-1-7">
      <partsGuageTile [tileType]="'Month'" [includeCompareCommentary]="false" [data]="dataPack.PartsGuageMonth" [dataSource]="'Quiter'"></partsGuageTile>
    </div>
    <div class="dashboard-tile grid-col-7-9 grid-row-1-5">
      <runChaseTile
        [data]="dataPack.Service"
        [page]="'service'"
        [customTitle]="constants.translatedText.Service"
        [isSmallCard]="true"
        [dataSource]="'Quiter'"
        [newDataEmitter]="newDataEmitter"
        >
      </runChaseTile>
    </div>

    <div class="dashboard-tile grid-col-9-11 grid-row-1-5">
      <runChaseTile
      [data]="dataPack.Parts"
      [page]="'parts'"
      [customTitle]="constants.translatedText.Parts"
      [isSmallCard]="true"
      [dataSource]="'Quiter'"
      [newDataEmitter]="newDataEmitter"
      >
    </runChaseTile>
      
    </div>



    <div class="dashboard-tile grid-col-9-11 grid-row-8-11">
      <percentageAndFigures [data]="dataPack.TechControlAndConvRates[3]" [tileClass]="'small-tile'" [dataSource]="'Eserpubli'"></percentageAndFigures>

    </div>

    <div class="dashboard-tile grid-col-11-15 grid-row-1-7">
      <turnoverPerOperative [newDataEmitter]="newDataEmitter" [data]="dataPack.TurnoverPerOperative" [dataSource]="'Quiter'"></turnoverPerOperative>
    </div>
    
    <div class="dashboard-tile grid-col-1-7 grid-row-7-12">
      <serviceDetailedTile [title]="constants.translatedText.Dashboard_Aftersales_Mechanical" [newDataEmitter]="newDataEmitter" [data]="dataPack.ServiceDetailedMechanical" [dataSource]="'Quiter'"> </serviceDetailedTile>
    </div>

    <div class="dashboard-tile grid-col-7-9 grid-row-5-8">
      <wipBarChart [newDataEmitter]="newDataEmitter" [data]="dataPack.WipSummaries[1]" [title]="constants.translatedText.Dashboard_Aftersales_Parts" [dataSource]="'Quiter'" [disabled]="!service.currentMonthSelected"></wipBarChart>
    </div>

    <div class="dashboard-tile grid-col-9-11 grid-row-5-8">
      <wipBarChart [newDataEmitter]="newDataEmitter" [data]="dataPack.WipSummaries[0]" [title]="constants.translatedText.Dashboard_Aftersales_Labour" [dataSource]="'Quiter'" [disabled]="!service.currentMonthSelected"></wipBarChart>
    </div>

    <div class="dashboard-tile grid-col-7-9 grid-row-8-11">
      <percentageAndFigures [data]="dataPack.TechControlAndConvRates[2]" [tileClass]="'small-tile'" [dataSource]="'Quiter'"></percentageAndFigures>
    </div>

    <div class="dashboard-tile grid-col-1-7 grid-row-12-17">

      <serviceDetailedTile [title]="constants.translatedText.Dashboard_Aftersales_Bodyshop" [newDataEmitter]="newDataEmitter" [data]="dataPack.ServiceDetailedBodyshop" [dataSource]="'Quiter'"> </serviceDetailedTile>

    </div>
    
    <div class="dashboard-tile grid-col-7-9 grid-row-11-14">
      <percentageAndFigures [data]="dataPack.TechControlAndConvRates[0]" [tileClass]="'small-tile'" [dataSource]="'Quiter'" [dataSource2]="'Eserpubli'"></percentageAndFigures>
      
    </div>

    <div class="dashboard-tile grid-col-9-11 grid-row-11-14">
      <percentageAndFigures [data]="dataPack.TechControlAndConvRates[1]" [tileClass]="'small-tile'" [dataSource]="'Eserpubli'"></percentageAndFigures>
    </div>

    <div class="dashboard-tile grid-col-11-15 grid-row-7-17">
      <detailedBookingsTile [newDataEmitter]="newDataEmitter" [nextDayCount]="14" [data]="dataPack.Bookings" [dataSource]="'Quiter'" [disabled]="!service.currentMonthSelected"></detailedBookingsTile> 
    </div>

    <div class="dashboard-tile grid-col-7-9 grid-row-14-17">
      <percentageAndFigures [data]="dataPack.TechControlAndConvRates[4]" [tileClass]="'small-tile'" [dataSource]="'Quiter'"></percentageAndFigures>

    </div>

    <div class="dashboard-tile grid-col-9-11 grid-row-14-17">
      <percentageAndFigures [data]="dataPack.TechControlAndConvRates[5]" [tileClass]="'small-tile'" [dataSource]="'Eserpubli'"></percentageAndFigures>
    </div>

  </div>
</div>