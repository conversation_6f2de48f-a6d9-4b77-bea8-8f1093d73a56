﻿using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using System;
using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
  public class PricingChangeNew
  {
    public string RetailerName { get; set; }
    public int RetailerSiteId { get; set; }
    public DateTime WhenToActionChangesEachDay { get; set; }
    public string VehicleReg { get; set; }
    public string Make { get; set; }
    public string Model { get; set; }
    public string Derivative { get; set; }
    public string WebsiteStockIdentifier { get; set; }
    public string WebSiteSearchIdentifier { get; set; }
    public int AdvertId { get; set; }
    public int DaysListed { get; set; }
    public int DaysInStock { get; set; }
    public int PriceChangeId { get; set; }
    public decimal? WasPrice { get; set; }

    public string PriceIndicatorRatingAtCurrentSelling { get; set; }
    public decimal DaysToSellAtCurrentSelling { get; set; }
    public int ValuationMktAvRetail { get; set; }
    public int ValuationAdjustedRetail { get; set; }
    public int StrategyPrice { get; set; }
    public string WasPriceVsStrategyBanding { get; set; }
    public int RetailRating { get; set; }
    public int NewPrice { get; set; }
    public int NewDaysToSell { get; set; }
    public string NewPriceIndicatorRating { get; set; }
    public decimal DaysToSellChange { get => NewDaysToSell - DaysToSellAtCurrentSelling; }
    public bool IsOptedOutOnDay { get; set; }

    public string OwnershipCondition { get; set; }
    public string LastComment { get; set; }
    public DateTime? DateConfirmed { get; set; }
    public bool IsSmallChange { get; set; }
    public bool IsIncrease { get; set; }

    public void UpdateVsStrategyBanding(Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingDefinitions)
    {
      var defn = bandingDefinitions[RetailerSiteId];
      WasPriceVsStrategyBanding = BandingsService.ProvidePriceVsStrategyBanding(StrategyPrice, (int)WasPrice, (int)ValuationMktAvRetail, defn);
    }


    public decimal TotalChangeValue { get => NewPrice - (WasPrice ?? 0); }
    public decimal TotalChangePercent { get => (WasPrice ?? 0) != 0 ? (decimal)TotalChangeValue / WasPrice.Value : 0; }
    public string Status { get; private set; }

    public DateTime? ApprovedDate { get; set; }
    public int? ApprovedById { get; set; }
    public bool ApproverHasBeenEmailed { get; set; }

    public decimal ChangeValueUp { get => TotalChangeValue > 0 ? TotalChangeValue : 0; }
    public decimal ChangePercentUp { get => TotalChangePercent > 0 ? TotalChangePercent : 0; }
    public decimal ChangeValueDown { get => TotalChangeValue < 0 ? TotalChangeValue : 0; }
    public decimal ChangePercentDown { get => TotalChangePercent < 0 ? TotalChangePercent : 0; }

    public void workoutStatus(string actionTime, int dealerGroupId)
    {
      string message = PriceChangeMessageService.GeneratePriceChangeMessage(
        actionTime,
        ApprovedDate,
        DateConfirmed,
        IsSmallChange,
        IsOptedOutOnDay,
        StockNumber,
        dealerGroupId);
      Status = message;
    }

    public int RetailerAdminFee { get; set; }
    public int NewPriceExclAdminFee { get => NewPrice - RetailerAdminFee; }

    public string VehicleTypeDesc { get; set; }
    public int OurValueRank { get; set; }
    public int OurPPRank { get; set; }

    public string StockNumber { get; set; }

    public bool IsSetToAutoUpdatePrice { get; set; }
  }
}
