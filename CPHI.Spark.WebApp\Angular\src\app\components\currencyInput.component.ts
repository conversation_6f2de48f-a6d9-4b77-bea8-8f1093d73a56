import {
  Component,
  ElementRef,
  EventEmitter,
  forwardRef,
  Input,
  Output,
  ViewChild
} from '@angular/core';
import {
  ControlValueAccessor,
  NG_VALUE_ACCESSOR
} from '@angular/forms';

@Component({
  selector: 'currencyInput',
  template: `
    <input
      #inputEl
      type="text"
      [value]="isFocused ? rawDisplay : format(modelValue)"
      (input)="onInput($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
    />
  `,
  styles: [
    `
      input {
        padding: 4px;
        font-size: 16px;
      }
    `
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CurrencyInputComponent),
      multi: true
    }
  ]
})
export class CurrencyInputComponent implements ControlValueAccessor {
  @ViewChild('inputEl') inputEl!: ElementRef<HTMLInputElement>;
   @Output() enterPressed = new EventEmitter<void>();

  modelValue: number | null = null;
  rawDisplay: string = '';
  isFocused = false;

  private onChange: (value: number | null) => void = () => {};
  private onTouched: () => void = () => {};

  writeValue(value: number | null): void {
    this.modelValue = value;
    this.rawDisplay = value != null ? value.toString() : '';
  }

  registerOnChange(fn: (value: number | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  onInput(event: Event): void {
    const input = (event.target as HTMLInputElement).value;
    this.rawDisplay = input.replace(/[^0-9.]/g, '');
    const parsed = parseFloat(this.rawDisplay);
    this.modelValue = isNaN(parsed) ? null : parsed;
    this.onChange(this.modelValue);
  }

  onFocus(): void {
    this.isFocused = true;
  }

  onBlur(): void {
    this.isFocused = false;
    this.onTouched();
  }

  onEnter(): void {
    this.enterPressed.emit();
  }

  format(value: number | null): string {
    return value != null && !isNaN(value)
      ? '£' + value.toLocaleString('en-GB', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        })
      : '';
  }
}
