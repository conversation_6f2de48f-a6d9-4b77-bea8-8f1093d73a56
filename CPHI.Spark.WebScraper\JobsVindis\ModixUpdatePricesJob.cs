﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using OpenQA.Selenium.Interactions;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;

using System.Collections.Generic;
using System.IO;
using System.Linq;
using Xunit;
using CPHI.WebScraper.ViewModel;
using System.Diagnostics;
using System.Threading.Tasks;
using Xunit.Sdk;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.Model;
using CPHI.Spark.Repository;
using System.Runtime.Versioning;
using CPHI.Spark.Model.ViewModels;
using CPHI.Repository;
using CPHI.Spark.BusinessLogic.AutoPrice;

namespace CPHI.Spark.WebScraper.Jobs
{

   public class ModixUpdatePricesJob : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(ModixUpdatePricesJob));
      private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
      private const string homePageLink = "https://content.uk1.modix.biz";
      private const int seatSiteId = 14;

      private DealerGroupName dealerGroup;

      private string fileDestination;
      private string fileDestinationDev;

      Dictionary<int, string> brandDictionary;
      Dictionary<int, string> siteCodeDictionary;

      public void Execute() { }
      public async Task Execute(IJobExecutionContext context)
      {
         logger.Info("");
         logger.Info("========================= Starting ModixScrapeJob =====================================");

         Stopwatch stopwatch = new Stopwatch();
         string errorMessage = string.Empty;
         stopwatch.Start();

         brandDictionary = InitiateBrandDictionary();
         siteCodeDictionary = InitiateSiteCodeDictionary();

         // This is a Vindis only scraper
         fileDestination = ConfigService.FileDestination;
         fileDestination = fileDestination.Replace("{destinationFolder}", "vindis");
         dealerGroup = DealerGroupName.Vindis;

         try
         {
            ////early return if today is public holiday
            //if (CheckIfTodayIsPubHoliday())
            //{
            //   logger.Info("Today is a public holiday.  Returning...");
            //   return;
            //}

            var service = ChromeDriverService.CreateDefaultService();
            service.HostName = "127.0.0.1";
            ScraperMethodsService.ClearDownloadsFolder();

            ChromeOptions options = ScraperMethodsService.SetChromeOptions("VindisModix2", 9228);

            _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

            List<ModixVehicle> vehicles = await GetPriceChangesToProcess(dealerGroup);

            // Need to do some kind of function here to get these from DB
            // Create a list to hold ModixVehicle objects
            //List<ModixVehicle> testVehicles =
            //[
            //    new ModixVehicle("AJ72KBV", 17594, 7, false), // GX24XWZ SEAT
            //    ];

            await UpdateModix(vehicles);

            _driver.Quit();
            _driver.Dispose();
            stopwatch.Stop();

            logger.Info("========================= Completed ModixScrapeJob =====================================");
         }
         catch (Exception e)
         {
            stopwatch.Stop();
            errorMessage = e.ToString();
            //Emailer.SendMail("Vindis activity scraper failed", $"{e}");

            logger.Error($"Problem {e.ToString()}");

            _driver.Quit();
            _driver.Dispose();
         }
         finally
         {
            Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
            {
               Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
               Project = "WebScraper",
               Customer = "Vindis",
               //Environment = env,
               Task = this.GetType().Name,
               StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
               EndDate = DateTime.UtcNow,
               Status = errorMessage.Length > 0 ? "Fail" : "Pass",
               Notes = errorMessage,
               HTML = string.Empty
            };
            await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
         }

      }

      public async Task<List<ModixVehicle>> GetPriceChangesToProcess(DealerGroupName dealerGroup)
      {
         PriceChangesService priceChangesService = new PriceChangesService(ConfigService.GetConnectionString(dealerGroup));
         RetailerSitesDataAccess retailerSitesDataAccess = new RetailerSitesDataAccess(ConfigService.GetConnectionString(dealerGroup));
         List<RetailerSite> retailers = await retailerSitesDataAccess.GetRetailerSites(dealerGroup);

         bool includeUnPublished = retailers.First().IncludeUnPublishedAds;
         GetPriceChangesNewParams parms = await priceChangesService.CreateParmsToGetChanges(dealerGroup, retailers, includeUnPublished);
         var todayChangesFirstPass = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);

         //Early return if none
         if (todayChangesFirstPass.totalChanges.Count == 0)
         {
            return new List<ModixVehicle>();
         }

         // There are some changes, so now re-run the vehicle opt-out updater
         var optOutsDataAccess = new OptOutsDataAccess(ConfigService.GetConnectionString(dealerGroup));
         await optOutsDataAccess.CreateDailyOptOuts(dealerGroup);

         //have to run again in case we just made some optouts
         GetTodayChangesResponse todayChangesResponse = await priceChangesService.getTodayChangesForUpdateWebsite(dealerGroup, parms);


         string _connectionString = ConfigService.GetConnectionString(dealerGroup);

         var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);

         List<ModixVehicle> result = todayChangesResponse.totalChanges.ConvertAll(x => new ModixVehicle(x));
         return result;
      }



      public async Task UpdateModix(List<ModixVehicle> vehicles)
      {

         try
         {
            logger.Info("Starting Update Modix");
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            DateTime start = DateTime.Now;

            bool loginSuccessful = await LoginAsync(wait);
            logger.Info($"Login Ok is {loginSuccessful}");

            var vehiclesBySite = vehicles.ToLookup(x => x.RetailerSiteId);

            // Iterate over each site grouping
            foreach (IGrouping<int, ModixVehicle> siteGroup in vehiclesBySite)
            {
               // If SEAT, we need to split between CUPRA and SEAT regular
               if (siteGroup.Key == seatSiteId)
               {
                  //do the cupra ones separately
                  var cupraGroup = siteGroup.Where(x => x.IsCupra);
                  logger.Info($"Processing site SEAT CUPRA {cupraGroup.First().RetailerSiteId} with {cupraGroup.Count()} vehicles");
                  NavigateToSiteAndUpdatePrices(wait, cupraGroup.ToList());

                  //nonCupra
                  var nonCupra = siteGroup.Where(x => !x.IsCupra);
                  logger.Info($"Processing site SEAT  {nonCupra.First().RetailerSiteId} with {nonCupra.Count()} vehicles");
                  NavigateToSiteAndUpdatePrices(wait, nonCupra.ToList());
               }
               else
               {
                  logger.Info($"Processing site {siteGroup.Key} with {siteGroup.Count()} vehicles");
                  NavigateToSiteAndUpdatePrices(wait, siteGroup.ToList());
               }

               // Go back to Home Page
               // (should arrive on page with list of brands on left menu)
               _driver.Navigate().GoToUrl(homePageLink);
            }
            logger.Info("Complete iterating over vehicles by site");


         }
         catch (Exception e)
         {
            throw e;
         }
      }

      private async Task<bool> LoginAsync(WebDriverWait wait)
      {

         IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

         //go to login page
         try
         {
            _driver.Navigate().GoToUrl(homePageLink + "/login/");

            System.Threading.Thread.Sleep(1000);

            IWebElement usernameEntryField = wait.Until(ExpectedConditions.ElementExists(By.Name("username")));

            System.Threading.Thread.Sleep(1000);

            //Assert.Equal("Login - enquiryMAX", _driver.Title);

            System.Threading.Thread.Sleep(1000);

            WaitAndFind("//input [@Name='username']", false).SendKeys("<EMAIL>");
            System.Threading.Thread.Sleep(1000);
            //WaitAndFind("//input [@Id='NextButton']", true);
            WaitAndFind("//input [@Name='username']", false).SendKeys(Keys.Tab);
            System.Threading.Thread.Sleep(1000);
            WaitAndFind("//input [@Name='password']", false).SendKeys(ConfigService.ModixPassword);
            System.Threading.Thread.Sleep(1000);
            WaitAndFind("//div[2]/form/div[2]/div[2]/button", true); // Not really a better link to this no ID etc.
            System.Threading.Thread.Sleep(1000);
         }
         catch
         {
            return false;
         }


         try
         {
            IWebElement passwordUpdateRequired = wait.Until(ExpectedConditions.ElementExists(By.Name("old_password")));
            EmailerService eService = new EmailerService();
            await eService.SendMail("Modix password update required.", "Please update Modix password.");
            return false;
         }
         catch
         {
            return true;
         }

      }

      // The vehicles list MUST be grouped by SiteId
      private void NavigateToSiteAndUpdatePrices(WebDriverWait wait, List<ModixVehicle> grouping)
      {

         int siteId = grouping.First().RetailerSiteId;
         bool isCupra = siteId == seatSiteId && grouping.First().IsCupra;

         string brandName = GetBrand(siteId, isCupra);

         string siteCodeDictionary = GetSiteCode(siteId, isCupra);

         // Click Brand
         WaitAndFind($"//a[contains(text(), '{brandName}')]", true);
         System.Threading.Thread.Sleep(2000);

         // Click Site from List 
         WaitAndFind($"//a[contains(text(), '{siteCodeDictionary}')]", true);

         System.Threading.Thread.Sleep(2000);

         // Should arrive at Vehicle management dashboard for site
         // Vehicle management
         WaitAndFind("//span[contains(text(), 'Vehicle management')]", true);
         System.Threading.Thread.Sleep(2000);

         // Vehicle management -> Inventory
         WaitAndFind("//a[@title='Inventory']", true);
         System.Threading.Thread.Sleep(3000);


         foreach (ModixVehicle veh in grouping)
         {
            bool foundItem = SearchInventory(wait, veh);

            if (foundItem)
            {
               logger.Info($"Found item: {veh.Reg}");

               AmendPrices(wait, veh);

               System.Threading.Thread.Sleep(3000);

               // Go back to the Inventory to find the next item
               WaitAndFind("//a[@title='Inventory']", true);
            }

         }

         System.Threading.Thread.Sleep(1000);
      }


      private string GetBrand(int siteId, bool isCupra)
      {
         if (siteId == seatSiteId)
         {
            return isCupra ? "CUPRA UK" : "SEAT UK";
         }
         else if (brandDictionary.ContainsKey(siteId))
         {
            Console.WriteLine("The value for key '{0}' is '{1}'", siteId, brandDictionary[siteId]);
            return brandDictionary[siteId];
         }
         else
         {
            Console.WriteLine("GetBrand - Key not found.");
            return null;
         }
      }

      private string GetSiteCode(int siteId, bool isCupra)
      {
         if (siteId == seatSiteId)
         {
            return isCupra ? "70175789" : "1139023";
         }
         else if (siteCodeDictionary.ContainsKey(siteId))
         {
            Console.WriteLine("The value for key '{0}' is '{1}'", siteId, siteCodeDictionary[siteId]);
            return siteCodeDictionary[siteId];
         }
         else
         {
            Console.WriteLine("GetSiteCode - Key not found.");
            return null;
         }
      }

      private Dictionary<int, string> InitiateBrandDictionary()
      {
         // Create a dictionary that holds integer and string pairs
         Dictionary<int, string> result = new Dictionary<int, string>();

         // Populate the dictionary with three entries
         // result.Add(14, "CUPRA UK"); Seperately done for SEAT
         // result.Add(14, "SEAT UK"); // CUPRA is SEAT? Need to clarify
         result.Add(12, "SKODA UK");
         result.Add(13, "SKODA UK");

         // Audi
         result.Add(1, "UK Audi");
         result.Add(2, "UK Audi");
         result.Add(3, "UK Audi");
         result.Add(4, "UK Audi");
         result.Add(5, "UK Audi");

         result.Add(11, "VWCV UK");

         // VW NON-VC
         result.Add(6, "VWPC UK");
         result.Add(7, "VWPC UK");
         result.Add(8, "VWPC UK");
         result.Add(9, "VWPC UK");

         return result;
      }

      private Dictionary<int, string> InitiateSiteCodeDictionary()
      {
         // Create a dictionary that holds integer and string pairs
         Dictionary<int, string> result = new Dictionary<int, string>();

         // Populate the dictionary with three entries
         // result.Add(14, "70175789"); Seperately done for SEAT

         result.Add(12, "3268913");
         result.Add(13, "744203");

         result.Add(1, "21257");
         result.Add(2, "6507");
         result.Add(3, "11255");
         result.Add(4, "1057008");
         result.Add(5, "19654");

         result.Add(11, "3494674");

         result.Add(6, "42145");
         result.Add(7, "42147");
         result.Add(8, "123189");
         result.Add(9, "42148");

         return result;
      }

      private void AmendPrices(WebDriverWait wait, ModixVehicle veh)
      {
         logger.Info($"Amending price for: {veh.Reg}");

         WaitAndFind("//div[@class='ml-column ml-row1 ml-col1']/a[2]", true);

         System.Threading.Thread.Sleep(1000);

         string retailPriceFieldName = "Retail price";
         string makeAnOfferFieldName = "Make an offer";

         string regOnPage = GetRegOnUpdatePage();

         if(veh.Reg != regOnPage)
         {
            System.Console.WriteLine("Failed. Reg on page does not match price change reg.");
            return;
         }


         int retailPrice = veh.Price - 99;
         int offerPrice = retailPrice - 1;

         try
         {
            // Validate that the field exists
            var retailPriceRow = WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]", false);

            var makeAnOfferRow = WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]", false);

            WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false).Clear();

            WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false).SendKeys(retailPrice.ToString() + Keys.Tab);

            WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false).Clear();

            WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false).SendKeys(offerPrice.ToString() + Keys.Tab);

            // Check we have updated them correctly

            var retailPriceField = WaitAndFind($"//td[div[contains(., '{retailPriceFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false);

            string retailPriceValueStr = retailPriceField.GetAttribute("value");

            int retailPriceSet = ConvertPriceToInt(retailPriceValueStr);


            var offerPriceField = WaitAndFind($"//td[div[contains(., '{makeAnOfferFieldName}')]]/following-sibling::td[1]//input[contains(concat(' ', @class, ' '), ' dijitReset ') and contains(concat(' ', @class, ' '), ' dijitInputInner ')]", false);

            string offerPriceValueStr = offerPriceField.GetAttribute("value");

            int offerPriceSet = ConvertPriceToInt(offerPriceValueStr);

            if(retailPrice != retailPriceSet)
            {
               System.Console.WriteLine("Retail Price set does not match.");
               return;
            }

            if (offerPrice != offerPriceSet)
            {
               System.Console.WriteLine("Offer Price set does not match.");
               return;
            }

         }
         catch (Exception ex)
         {
            System.Console.WriteLine("Unknown error updating price fields.");
            return;
         }

         System.Threading.Thread.Sleep(1000);

         // Hit save
         try
         {
            WaitAndFind("//a[@title='Save']", true);

            logger.Info($"Amended price for: {veh.Reg} to Retail Price: {retailPrice} and Offer Price: {offerPrice}");
         }
         catch (Exception ex)
         {
            System.Console.WriteLine("Unknown error saving.");
         }

         //bool errorsFound = CheckForErrors(wait, veh);
      }

      private int ConvertPriceToInt(string price)
      {
         // Remove £ and commas, then parse
         string cleaned = price.Replace("£", "").Replace(",", "").Trim();
         return int.Parse(cleaned);
      }


      private string GetRegOnUpdatePage()
      {
         string regXpath = "//input[@name='licenceNumber' and contains(@class, 'dijitInputInner')]";

         var test = WaitAndFind(regXpath, false);

         return test.GetAttribute("value").ToUpper().Trim();
      }



      private bool SearchInventory(WebDriverWait wait, ModixVehicle veh)
      {
         try
         {

            //if (veh.Reg == "FN15YJX")
            //{

            //}

            logger.Info($"Searching item: {veh.Reg}");

            IWebElement inventorySearchBox = wait.Until(ExpectedConditions.ElementExists(By.Id("inventorySearchTxt2")));

            System.Threading.Thread.Sleep(500);

            // Clear search box
            inventorySearchBox.Clear();

            System.Threading.Thread.Sleep(500);

            // Search for reg
            WaitAndFind("//input [@Id='inventorySearchTxt2']", false).SendKeys(veh.Reg + Keys.Enter);

            // Takes a second or so to return
            System.Threading.Thread.Sleep(2000);

            // If this isn't here, we don't have any entries
            try
            {
               IWebElement result = WaitAndFind("(//*[contains(concat(' ', normalize-space(@class), ' '), ' ml-group ml-level0 ')])[1]", false);
            }
            catch
            {
               logger.Info($"No entries found for: {veh.Reg}");
               return false;
            }

            // Path to get to the data panel of first entry
            string xpath = "//ul[@id='inventoryList']/li[contains(@class, 'ml-row')][1]" +
               "//div[contains(@class, 'ml-innerRow') and contains(@class, 'ml-row2')]" +
               "//div[contains(@class, 'ml-column') and contains(@class, 'ml-col2')]" +
               "//div[contains(@class, 'content')]/div[contains(@class, 'data')][3]";

            IWebElement dataElement = WaitAndFind(xpath, false);

            string dataStringUnclean = dataElement.Text.Trim().ToUpper();

            string reg = ExtractReg(dataStringUnclean);

            if(reg == veh.Reg)
            {
               return true;
            }

            logger.Info($"No matching entries found: {veh.Reg}");
            return false;
         }
         catch (Exception ex)
         {
            logger.Info($"Error finding: {veh.Reg}");
            return false;
         }
      }

      private string ExtractReg(string text)
      {
         string[] lines = text.Split(new[] { "\r\n" }, StringSplitOptions.None);

         return lines.FirstOrDefault(line => !string.IsNullOrWhiteSpace(line)) ?? string.Empty;
      }

      public IWebElement WaitAndFind(string findXPath, bool andClick = false)
      {
         IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "GDPRScrape", findXPath, andClick);

         return result;
      }

   }
}
