import { Component, EventEmitter, OnInit, ViewChild } from '@angular/core';
import { SelectionsService } from 'src/app/services/selections.service';
import { Month } from '../../model/main.model';
import { ConstantsService } from '../../services/constants.service';
import { GetSEReviewFormParams, SaveSEReviewFormParams, UpdateSEReviewFormApprovalStateParams } from './salesExecReview.model';
import { SalesExecReviewService } from './salesExecReview.service';
import { ExecManagerMappingComponent } from './execManagerMapping/execManagerMapping.component';

@Component({
  selector: 'app-salesExecReview',
  templateUrl: './salesExecReview.component.html',
  styleUrls: ['./salesExecReview.component.scss']
})

export class SalesExecReviewComponent implements OnInit {

  @ViewChild('execManagerMappingModal', { static: true }) execManagerMappingModal: ExecManagerMappingComponent;
  
  months: Month[];
  measures: string[];

  saveFormSubscription: any;
  refreshFormSubscription: any;
  updateFormSubscription: any;

  constructor(
    public constants: ConstantsService,
    public service: SalesExecReviewService,
    public selections: SelectionsService
  ) { }

  ngOnInit(): void {
    this.service.initParams();
    this.makeMonths();
    this.makeMeasures();

    this.saveFormSubscription = this.service.salesExecReview.saveFormEmitter.subscribe((params: { SaveSEReviewFormParams: SaveSEReviewFormParams, thenUpdateApprovalState?: boolean }) => {
      this.service.saveForm(params.SaveSEReviewFormParams, params.thenUpdateApprovalState);
    })

    this.refreshFormSubscription = this.service.salesExecReview.refreshFormEmitter.subscribe((params: GetSEReviewFormParams) => {
      this.service.refreshForm(params);
    })

    this.updateFormSubscription = this.service.salesExecReview.updateFormApprovalStateEmitter.subscribe((params: UpdateSEReviewFormApprovalStateParams) => {
      this.service.updateFormApprovalState(params);
    })
  }

  ngOnDestroy(): void {
    if (this.saveFormSubscription) this.saveFormSubscription.unsubscribe();
    if (this.refreshFormSubscription) this.refreshFormSubscription.unsubscribe();
    if (this.updateFormSubscription) this.updateFormSubscription.unsubscribe();
  }

  makeMonths(offset?: number) {
    this.months = this.constants.makeMonths(0, offset);
  }

  makeMeasures() {
    this.measures = [
      'Overall Score',
      'Profit Pot',
      'New CEM (Rolling 3 months)',
      'Used CEM (Rolling 3 months)',
      'New Units',
      'Used & Demo Units',
      'iStoreDocs Compliance',
      'New Finance Penetration',
      'Used Finance Penetration',
      'Products per Unit',
      'Profit per Unit (Products)',
      'Deals with CEM Response %',
      'New Renewals',
      'Used Renewals',
      'New Enquiries',
      'EMAX Test Drive Ratio',
      'EMAX Conversion',
      'EMAX Offers Sent Ratio',
      'Telephone Appointment Ratio',
      'Martec Right Words Score',
      'Orders Taken in the Month',
      'Undelivered Orders',
      'Days to Deliver'
    ]
    this.service.salesExecReview.chosenMeasure = this.measures[0];
  }

  selectMonth(date: Date) {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.salesExecReview.chosenMonth = date;
    this.refreshView();
  }

  changeMonth(changeAmount: number) {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.salesExecReview.chosenMonth = this.constants.addMonths(this.service.salesExecReview.chosenMonth, changeAmount);
    this.refreshView();
  }

  refreshView() {
    if (this.service.salesExecReview.sitesView) return this.service.getSiteByMeasureRows(true);
    if (this.service.salesExecReview.peopleView) return this.service.getPeopleByMeasureRows(true);
    if (this.service.salesExecReview.form) return this.service.getFormData(this.service.salesExecReview.chosenPersonId, null, true);
  }

  updateChosenMeasure(measure: string) {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.salesExecReview.chosenMeasure = measure;

    if (this.service.salesExecReview.sitesView) return this.service.getSiteByMonthRows();
    this.service.getPeopleByMonthRows();
  }

  showExecManagerMappingModal(show: boolean)
  {
    this.service.salesExecReview.showExecManagerMappings = show;
    this.execManagerMappingModal.showModal();
  }

}
