<ng-template #dealModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      {{deal.DealDetailModal.StockNumber}} {{deal.DealDetailModal.Description}} - {{deal.DealDetailModal.Customer}}
    </h4>



    <button *ngIf="deal.StockListRow?.VehicleAdvertId" class="btn btn-primary"
      [ngClass]="{ 'active': selectedTab==='autotraderDetails' }" (click)="showAutotraderDetails()">Autotrader Detail
    </button>


    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>


  </div>

  <div [ngClass]="constants.environment.customer" class="modal-body">



    <!-- Deal stuff -->
    <div *ngIf="!showStockDetails; else stockDetails" id="panelContainer" [ngClass]="constants.environment.customer">

      <!-- Deal Header panel -->
      <div class="autotrader-tile mb-3" id="dealDetails">
        <div class="tile-inner">
          <div class="tile-header">
            {{constants.translatedText.DealDetails_Title}}
          </div>
          <div class="tile-body">
            <table>
              <tbody>
                <tr>
                  <td class="label">{{constants.translatedText.Common_StockNumber}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.StockNumber}}</div>
                  </td>
                  <td class="label">{{constants.translatedText.Common_Id}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.DealId}}</div>
                  </td>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.Common_Site}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.SiteName}} ({{deal.DealDetailModal.SiteCode}})</div>
                  </td>
                  <td class="label">{{constants.translatedText.Common_Reg}}</td>
                  <td class="value ">
                    <div class="regPlate">{{deal.DealDetailModal.Reg|cph:'numberPlate':0}}</div>
                  </td>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.Common_Franchise}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.Franchise}}</div>
                  </td>
                  <td class="label">{{constants.translatedText.Common_VehicleType}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.VehicleType}} ({{deal.DealDetailModal.VehicleSuperType}})
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.Common_Customer}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.Customer}}</div>
                  </td>
                  <td class="label">{{constants.translatedText.Common_OrderType}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.OrderType}} ({{deal.DealDetailModal.OrderTypeCode}})</div>
                  </td>
                </tr>
                <tr>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showFinanceCo">
                    <td class="label">{{constants.translatedText.DealDetails_FinanceCo}}</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.FinanceCo}}</div>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showFinanceType">
                    <td class="label">Finance Type</td>
                    <td>
                      <div class="value emptyValue">{{deal.DealDetailModal.FinanceType}}</div>
                    </td>
                  </ng-container>
                  <td class="label">{{constants.translatedText.Common_VehicleClass}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.VehicleClass}}</div>
                  </td>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.Common_ModelYear}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.ModelYear}}</div>
                  </td>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showVehicleAge">
                    <td class="label">{{ constants.translatedText.Common_VehicleAge }}</td>
                    <td>
                      <div class="value">{{ deal.DealDetailModal.VehicleAge }}</div>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showPhysicalLocation">
                    <td class="label">Physical Location</td>
                    <td>
                      <div class="value">{{ deal.DealDetailModal.LastPhysicalLocation }}</div>
                    </td>
                  </ng-container>



                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.Common_Model}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.Model}}</div>
                  </td>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showVariant">
                    <td class="label">{{constants.translatedText.DealDetails_Variant}}</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.Variant}}</div>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showWebsiteDiscount">
                    <td class="label">{{constants.translatedText.DealDetails_WebsitePrice}}</td>
                    <td>
                      <div class="value">
                        {{deal.DealDetailModal.WebsitePrice|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                        (discount of
                        {{deal.DealDetailModal.WebsiteDiscount|cph:'currency':constants.environment.dealDetailModal_currencyDP}})
                      </div>
                    </td>
                  </ng-container>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.DealDetails_VariantText}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.VariantTxt}}</div>
                  </td>
                  <td class="label">{{constants.translatedText.Common_SalesExec}}</td>
                  <td>
                    <div class="value"> {{deal.DealDetailModal.Salesman}} </div>
                  </td>

                </tr>
                <tr>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showDescription">
                    <td class="label">{{constants.translatedText.Common_Description}}</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.Description}}</div>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showIsLateCost">
                    <td class="label">{{constants.translatedText.DealDetails_IsLateCost}}</td>
                    <td>
                      <div class="value"><input type="checkbox" disabled [ngModel]="deal.DealDetailModal.IsLateCost" />
                      </div>
                    </td>
                  </ng-container>
                </tr>
                <tr>

                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showUnits">
                    <td class="label">{{constants.translatedText.Common_Units}}</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.Units}}</div>
                    </td>
                  </ng-container>

                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showIsClosed">
                    <td class="label">Is Closed?</td>
                    <td>
                      <div class="value"><input type="checkbox" disabled [ngModel]="deal.DealDetailModal.IsClosed" />
                      </div>
                    </td>
                  </ng-container>

                  <td class="label">{{constants.translatedText.DealDetails_IsDelivered}}</td>
                  <td>
                    <div class="value"><input type="checkbox" disabled [ngModel]="deal.DealDetailModal.IsDelivered" />
                    </div>
                  </td>

                </tr>

                <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showInvoiceNo">
                  <td class="label">{{constants.translatedText.DealDetails_InvoiceNo}}</td>
                  <td>
                    <div class="value">{{ deal.DealDetailModal.InvoiceNo }}</div>
                  </td>
                </ng-container>

                <tr>
                  <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showOEMReference">
                    <td class="label">OEM Reference</td>
                    <td>
                      <div class="value">{{ deal.DealDetailModal.OemReference }}</div>
                    </td>
                  </ng-container>
                </tr>

                <tr *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showAuditPass">
                  <td class="label">Audit Pass?</td>
                  <td>
                    <i
                      [ngClass]="{'goodFont':deal.DealDetailModal.AuditPass, 'badFont' : !deal.DealDetailModal.AuditPass, 'okFont' : deal.DealDetailModal.AuditPass == null}">⬤</i>
                  </td>
                </tr>

              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Metal profit area -->
      <div class="autotrader-tile mb-3" id="metalProfit">
        <div class="tile-inner">
          <div class="tile-header">
            {{constants.translatedText[constants.environment.dealDetailModal_metalProfitSection_headerTranslation]}}
          </div>
          <div class="tile-body">
            <table class="profitTable">
              <thead>
                <tr>
                  <th></th>
                  <th>{{constants.translatedText.Common_Sale}}</th>
                  <th>{{constants.translatedText[constants.environment.dealDetailModal_costColumnTranslation]}}</th>
                  <th>{{constants.translatedText.Common_Discount}}</th>
                  <th>{{constants.translatedText.Common_Net}}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="label">{{constants.translatedText.Common_Vehicle}}</td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.Sale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.CoS|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div title="Discount" class="value">
                      {{deal.DealDetailModal.Discount|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">{{+(deal.DealDetailModal.Sale + deal.DealDetailModal.CoS +
                      deal.DealDetailModal.Discount)|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr
                  *ngIf="constants.environment.dealDetailModal_metalProfitSection_showVATCost && deal.DealDetailModal_VehicleSuperType !== 'New'">
                  <td class="label">VAT Cost</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.VatCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.VatCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.DealDetails_PartExchange}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.PartExOverAllowance1|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.PartExOverAllowance1|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.DealDetails_FactoryBonus}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.NewBonus1|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.NewBonus1|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>


                <tr class="totalsRow">
                  <td class="label ">{{constants.translatedText.DealDetails_TinProfit}}</td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.Sale )|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.CoS + deal.DealDetailModal.VatCost +
                      deal.DealDetailModal.PartExOverAllowance1
                      )|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.Discount+deal.DealDetailModal.NewBonus1
                      )|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.MetalProfitTotal
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Other profit area -->
      <div class="autotrader-tile mb-3" id="metalProfit">
        <div class="tile-inner">
          <div class="tile-header">
            {{constants.translatedText.Common_OtherProfit}}
          </div>
          <div class="tile-body">
            <table class="profitTable">
              <thead>
                <tr>
                  <th></th>
                  <th>{{constants.translatedText.Common_Sale}}</th>
                  <th>{{constants.translatedText[constants.environment.dealDetailModal_costColumnTranslation]}}</th>
                  <th *ngIf="selections.user.JobTitle != 'Advanced User'">{{constants.translatedText.Common_Commission}}
                  </th>
                  <th *ngIf="selections.user.JobTitle == 'Advanced User'">
                    <div class="value"></div>
                  </th>
                  <th>{{constants.translatedText.Common_Net}}</th>
                </tr>
              </thead>
              <tbody>

                <!-- RegBonus -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showRegBonus">
                  <td class="label">{{constants.translatedText.DealDetails_RegBonus}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value" *ngIf="selections.user.JobTitle != 'Advanced User'">
                      {{deal.DealDetailModal.NewBonus2|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.NewBonus2|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- Into commission -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showIntroComm">
                  <td class="label">{{constants.translatedText.DealDetails_IntroCommission}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value" *ngIf="selections.user.JobTitle != 'Advanced User'">
                      {{deal.DealDetailModal.IntroCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.IntroCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- BrokerCost -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showBrokerCost">
                  <td class="label">{{constants.translatedText.DealDetails_BrokerCost}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.BrokerCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.BrokerCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- Accessories -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showAccessories">
                  <td class="label">{{constants.translatedText.DealDetails_Accessories}}</td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.AccessoriesSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.AccessoriesCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">{{+(deal.DealDetailModal.AccessoriesSale + deal.DealDetailModal.AccessoriesCost)
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}</div>
                  </td>
                </tr>

                <!-- PaintProtection Accessory -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showPaintProtectionAccessory">
                  <td class="label">{{ constants.translatedText.DealDetails_PaintProtectionAccessory }}</td>
                  <td>
                    <div class="value">{{ deal.DealDetailModal.PaintProtectionAccessorySale |
                      cph:'currency':constants.environment.dealDetailModal_currencyDP }}</div>
                  </td>
                  <td>
                    <div class="value">{{ deal.DealDetailModal.PaintProtectionAccessoryCost |
                      cph:'currency':constants.environment.dealDetailModal_currencyDP }}</div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">{{ +(deal.DealDetailModal.PaintProtectionAccessorySale +
                      deal.DealDetailModal.PaintProtectionAccessoryCost) |
                      cph:'currency':constants.environment.dealDetailModal_currencyDP }}</div>
                  </td>
                </tr>


                <!-- Fuel -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showFuel">
                  <td class="label">{{constants.translatedText.DealDetails_Fuel}}</td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.FuelSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.FuelCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">{{+(deal.DealDetailModal.FuelSale + deal.DealDetailModal.FuelCost)
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}</div>
                  </td>
                </tr>

                <!-- Delivery -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showDelivery">
                  <td class="label">{{constants.translatedText.DealDetails_Delivery}}</td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.OemDeliverySale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.OemDeliveryCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">{{+(deal.DealDetailModal.OemDeliverySale + deal.DealDetailModal.OemDeliveryCost)
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}</div>
                  </td>
                </tr>



                <!-- PDI  -->
                <tr
                  *ngIf="constants.environment.dealDetailModal_otherProfitSection_showPdi && deal.DealDetailModal.VehicleSuperType=='New'">

                  <td class="label">{{constants.translatedText.DealDetails_Pdi}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.PDICost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.PDICost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- MechPrep -->
                <tr
                  *ngIf="constants.environment.dealDetailModal_otherProfitSection_showMechPrep && deal.DealDetailModal.VehicleSuperType !=='New'">
                  <td class="label">{{constants.translatedText.DealDetails_MechanicalPrep}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.MechPrep|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.MechPrep|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- BodyPrep -->
                <tr
                  *ngIf="constants.environment.dealDetailModal_otherProfitSection_showBodyPrep && deal.DealDetailModal.VehicleSuperType !=='New'">
                  <td class="label">{{constants.translatedText.DealDetails_BodyPrep}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.BodyPrep|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.BodyPrep|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- Standard Warranty -->
                <tr
                  *ngIf="constants.environment.dealDetailModal_otherProfitSection_showStandardWarranty && deal.DealDetailModal.VehicleSuperType !=='New'">
                  <td class="label">{{ constants.translatedText.DealDetails_StandardWarranty }}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">{{ deal.DealDetailModal.StandardWarrantyCost |
                      cph:'currency':constants.environment.dealDetailModal_currencyDP }}</div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">{{ +deal.DealDetailModal.StandardWarrantyCost |
                      cph:'currency':constants.environment.dealDetailModal_currencyDP }}</div>
                  </td>
                </tr>



                <!-- Other -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showOther">
                  <td class="label">{{constants.translatedText.Common_Other}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.Other|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.Other|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>


                <!-- Error -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showError">
                  <td class="label">{{constants.translatedText.Common_Error}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.Error|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.Error|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- TOTAL -->
                <tr *ngIf="constants.environment.dealDetailModal_otherProfitSection_showTotal" class="totalsRow">
                  <td class="label ">{{constants.translatedText.Common_OtherProfit}}</td>
                  <td>
                    <div class="value">
                      {{+( deal.DealDetailModal.AccessoriesSale + deal.DealDetailModal.FuelSale +
                      deal.DealDetailModal.OemDeliverySale)|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+( + deal.DealDetailModal.BrokerCost + deal.DealDetailModal.AccessoriesCost +
                      deal.DealDetailModal.FuelCost + deal.DealDetailModal.OemDeliveryCost +
                      deal.DealDetailModal.PDICost + deal.DealDetailModal.MechPrep + deal.DealDetailModal.BodyPrep +
                      deal.DealDetailModal.Error + deal.DealDetailModal.StandardWarrantyCost +
                      deal.DealDetailModal.Other)|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+( + deal.DealDetailModal.NewBonus2 +
                      deal.DealDetailModal.IntroCommission)|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.OtherProfitTotal|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>



      <!-- Dates-->
      <div class="autotrader-tile mb-3" id="dates">
        <div class="tile-inner">
          <div class="tile-header">
            {{constants.translatedText.Common_Dates}}
          </div>
          <div class="tile-body">
            <table class="profitTable">

              <tbody>
                <tr>
                  <td class="label">{{constants.translatedText.DealDetails_StockDate}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.StockDate|cph:'dateShortYear':0}}</div>
                  </td>
                  <td class="label">{{constants.translatedText.DealDetails_OrderDate}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.OrderDate|cph:'dateShortYear':0}}</div>
                  </td>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.DealDetails_RegisteredDate}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.RegisteredDate|cph:'dateShortYear':0}}</div>
                  </td>
                  <td class="label">{{constants.translatedText.DealDetails_InvoiceDate}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.InvoiceDate|cph:'dateShortYear':0}}</div>
                  </td>
                </tr>

                <tr>
                  <td class="label">{{constants.translatedText.DealDetails_AccountingDate}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.AccountingDate|cph:'dateShortYear':0}}</div>
                  </td>
                  <td class="label">{{constants.translatedText.Common_DeliveryDate}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.ActualDeliveryDate|cph:'dateShortYear':0}}</div>
                  </td>
                </tr>

                <tr>
                  <ng-container
                    *ngIf="constants.environment.dealDetailModal_datesSection_showCustomerDestinationDeliveryDate">
                    <td class="label">Customer Des Del Dte</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.Cdd|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="constants.environment.dealDetailModal_datesSection_showEnterImportCentreDate">
                    <td class="label">Enter Import Centre</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.EnterImportCentre|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                </tr>

                <tr>
                  <ng-container *ngIf="constants.environment.dealDetailModal_datesSection_showShipDate">
                    <td class="label">Ship Date</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.ShipDate|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="constants.environment.dealDetailModal_datesSection_showExitImportCentreDate">
                    <td class="label">Exit Import Centre</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.ExitImportCentre|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                </tr>

                <tr>
                  <ng-container *ngIf="constants.environment.dealDetailModal_datesSection_showAllocationDate">
                    <td class="label">Allocation Date</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.AllocationDate|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="constants.environment.dealDetailModal_datesSection_showDateVehicleRecondition">
                    <td class="label">{{constants.translatedText.DealDetails_DateVehicleRecondition}}</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.DateVehicleRecondition|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                </tr>

                <tr>
                  <ng-container
                    *ngIf="constants.environment.dealDetailModal_datesSection_showDateFactoryTransportation">
                    <td class="label">{{constants.translatedText.DealDetails_DateFactoryTransportation}}</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.DateFactoryTransportation|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                  <ng-container *ngIf="constants.environment.dealDetailModal_datesSection_showDateSiteArrival">
                    <td class="label">{{constants.translatedText.DealDetails_DateSiteArrival}}</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.DateSiteArrival|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                </tr>

                <tr>
                  <ng-container *ngIf="constants.environment.dealDetailModal_datesSection_showDateSiteTransportation">
                    <td class="label">{{constants.translatedText.DealDetails_DateSiteTransportation}}</td>
                    <td>
                      <div class="value">{{deal.DealDetailModal.DateSiteTransportation|cph:'dateShortYear':0}}</div>
                    </td>
                  </ng-container>
                  <!-- <ng-container *ngIf="constants.environment.dealDetailModal_datesSection_showAllocationDate">
                <td class="label">Allocation Date</td>
                <td>
                  <div class="value">{{deal.DealDetailModal.AllocationDate|cph:'dateShortYear':0}}</div>
                </td>
              </ng-container> -->
                </tr>

              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Finance Profit area -->
      <div *ngIf="constants.environment.dealDetailModal_financeProfitSection_show" class="autotrader-tile mb-3"
        id="financeProfit">
        <div class="tile-inner">
          <div class="tile-header">
            {{constants.translatedText.Common_FinanceProfit}}
          </div>
          <div class="tile-body">
            <table class="profitTable">
              <thead>
                <tr>
                  <th></th>
                  <th>{{constants.translatedText.Common_Sale}}</th>
                  <th>{{constants.translatedText[constants.environment.dealDetailModal_costColumnTranslation]}}</th>
                  <th>{{constants.translatedText.Common_Commission}}</th>
                  <th>{{constants.translatedText.Common_Net}}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="label">
                    {{constants.translatedText[constants.environment.dealDetailModal_financeProfitSection_rciFinanceCommissionText]}}
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.RCIFinanceCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.RCIFinanceCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="label">
                    {{constants.translatedText[constants.environment.dealDetailModal_financeProfitSection_financeCommissionText]}}
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.FinanceCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.FinanceCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="label">{{constants.translatedText.DealDetails_FinanceSubsidy}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.FinanceSubsidy|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.FinanceSubsidy|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr *ngIf="constants.environment.dealDetailModal_financeProfitSection_showSelectCommission">
                  <td class="label">{{constants.translatedText.DealDetails_SelectCommission}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.SelectCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.SelectCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr *ngIf="constants.environment.dealDetailModal_financeProfitSection_showProPlusCommission">
                  <td class="label">{{constants.translatedText.DealDetails_ProPlusCommision}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.ProPlusCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.ProPlusCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr *ngIf="constants.environment.dealDetailModal_financeProfitSection_showStandardsCommission">
                  <td class="label">{{constants.translatedText.DealDetails_StandardsCommission}}</td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.StandardsCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.StandardsCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>
                <tr class="totalsRow">
                  <td class="label ">{{constants.translatedText.Common_FinanceProfit}}</td>
                  <td>
                    {{constants.translatedText.DealDetails_IsOnFinance}}

                  </td>
                  <td>
                    <input type="checkbox" disabled [ngModel]="deal.DealDetailModal.IsFinanced" />
                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.RCIFinanceCommission + deal.DealDetailModal.FinanceCommission +
                      deal.DealDetailModal.SelectCommission +
                      deal.DealDetailModal.ProPlusCommission +
                      deal.DealDetailModal.StandardsCommission)|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.RCIFinanceCommission + deal.DealDetailModal.FinanceCommission +
                      deal.DealDetailModal.FinanceSubsidy + deal.DealDetailModal.SelectCommission +
                      deal.DealDetailModal.ProPlusCommission +
                      deal.DealDetailModal.StandardsCommission)|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Add-ons area -->
      <div *ngIf="constants.environment.dealDetailModal_showAddOnProfit" class="autotrader-tile mb-3"
        id="financeProfit">
        <div class="tile-inner">
          <div class="tile-header">
            {{constants.translatedText.Common_AddOnProfit}}
          </div>
          <div class="tile-body">
            <table class="profitTable">
              <thead>
                <tr>
                  <th></th>
                  <th>{{constants.translatedText.Common_Sale}}</th>
                  <th>{{constants.translatedText[constants.environment.dealDetailModal_costColumnTranslation]}}</th>
                  <th>{{constants.translatedText.Common_Commission}}</th>
                  <th>{{constants.translatedText.Common_Net}}</th>
                </tr>
              </thead>
              <tbody>

                <!-- Cosmetic Insurance -->
                <tr class="addOnRow">
                  <td class="label">
                    <i *ngIf="!deal.DealDetailModal.IsLateCost"
                      [ngClass]="{'solid':deal.DealDetailModal.HasCosmeticInsurance}" title="Cosmetic Insurance"
                      class="far fa-shield-check"></i>
                    {{constants.translatedText.DealDetails_CosmeticInsurance}}
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.CosmeticInsuranceSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.CosmeticInsuranceCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.CosmeticInsuranceCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.CosmeticInsuranceSale+deal.DealDetailModal.CosmeticInsuranceCost +
                      deal.DealDetailModal.CosmeticInsuranceCommission)|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- Paint Protection -->
                <tr *ngIf="constants.environment.dealDetailModal_addonsSection_showPaintProtection" class="addOnRow">
                  <td class="label">
                    <i *ngIf="!deal.DealDetailModal.IsLateCost"
                      [ngClass]="{'solid':deal.DealDetailModal.HasPaintProtection}" title="Paint Protection"
                      class="fas fa-claw-marks"></i>
                    {{constants.translatedText.DealDetails_PaintProtection}}
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.PaintProtectionSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.PaintProtectionCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>

                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.PaintProtectionSale+deal.DealDetailModal.PaintProtectionCost
                      )|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>


                <!-- GAP Insurance -->
                <tr class="addOnRow">
                  <td class="label">
                    <i *ngIf="!deal.DealDetailModal.IsLateCost"
                      [ngClass]="{'solid':deal.DealDetailModal.HasGapInsurance}" title='Gap Insurance'
                      class="fas fa-car-crash"></i>
                    {{constants.translatedText.DealDetails_GapInsurance}}
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.GapInsuranceSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.GapInsuranceCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.GapInsuranceCommission|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.GapInsuranceSale+deal.DealDetailModal.GapInsuranceCost +
                      deal.DealDetailModal.GapInsuranceCommission)|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- Tyre Insurance -->
                <tr class="addOnRow">
                  <td class="label">
                    <i *ngIf="!deal.DealDetailModal.IsLateCost"
                      [ngClass]="{'solid':deal.DealDetailModal.HasTyreInsurance}" title="Tyre Insurance"
                      class="fal fa-tire"></i>
                    {{constants.translatedText.DealDetails_TyreInsurance}}
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.TyreInsuranceSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.TyreInsuranceCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>

                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.TyreInsuranceSale+deal.DealDetailModal.TyreInsuranceCost
                      )|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- TyreAndAlloy Insurance -->
                <tr class="addOnRow">
                  <td class="label">
                    <i *ngIf="!deal.DealDetailModal.IsLateCost"
                      [ngClass]="{'solid':deal.DealDetailModal.HasTyreAndAlloyInsurance}" title="Tyre+Alloy Insurance"
                      class="fas fa-tire"></i>
                    {{constants.translatedText.DealDetails_TyreAlloyInsurance}}
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.TyreAndAlloyInsuranceSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.TyreAndAlloyInsuranceCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>

                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.TyreAndAlloyInsuranceSale+deal.DealDetailModal.TyreAndAlloyInsuranceCost
                      )|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>

                <!-- WheelGuard -->
                <tr class="addOnRow">
                  <td class="label">
                    <i *ngIf="!deal.DealDetailModal.IsLateCost" [ngClass]="{'solid':deal.DealDetailModal.HasWheelGuard}"
                      title="WheelGuard Insurance" class="fal fa-circle"></i>
                    {{constants.translatedText.Common_WheelGuard}}
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.WheelGuardSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.WheelGuardCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>

                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.WheelGuardSale+deal.DealDetailModal.WheelGuardCost
                      )|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                </tr>


                <!-- ServicePlan -->
                <tr class="addOnRow">
                  <td class="label">
                    <i *ngIf="!deal.DealDetailModal.IsLateCost"
                      [ngClass]="{'solid':deal.DealDetailModal.HasServicePlan}" title="Service Plan"
                      class="fas fa-car-mechanic"></i>
                    {{constants.translatedText.Common_ServicePlan}}
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.ServicePlanSale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.ServicePlanCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">{{+(deal.DealDetailModal.ServicePlanSale+deal.DealDetailModal.ServicePlanCost)
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}</div>
                  </td>
                </tr>

                <!-- Warranty -->
                <tr
                  *ngIf="constants.environment.dealDetailModal_addonsSection_showWarrantyForNewCar || deal.DealDetailModal.VehicleSuperType=='New'"
                  class="addOnRow">
                  <td class="label">
                    <i *ngIf="!deal.DealDetailModal.IsLateCost" [ngClass]="{'solid':deal.DealDetailModal.HasWarranty}"
                      title="Warranty" class="fas fa-file-certificate"></i>
                    {{constants.translatedText.Common_Warranty}}
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.WarrantySale|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{deal.DealDetailModal.WarrantyCost|cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value"></div>
                  </td>
                  <td>
                    <div class="value">{{+(deal.DealDetailModal.WarrantySale+deal.DealDetailModal.WarrantyCost)
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}</div>
                  </td>
                </tr>

                <!-- Total AddOnProfit -->
                <tr class="totalsRow">
                  <td class="label">
                    {{constants.translatedText.Common_AddOnProfit}}
                  </td>
                  <td>
                    <div class="value">
                      {{+deal.DealDetailModal.CosmeticInsuranceSale + deal.DealDetailModal.GapInsuranceSale +
                      deal.DealDetailModal.ServicePlanSale + deal.DealDetailModal.WarrantySale
                      + deal.DealDetailModal.PaintProtectionSale + deal.DealDetailModal.TyreAndAlloyInsuranceSale +
                      deal.DealDetailModal.TyreInsuranceSale +
                      deal.DealDetailModal.WheelGuardSale
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+deal.DealDetailModal.CosmeticInsuranceCost + deal.DealDetailModal.GapInsuranceCost +
                      deal.DealDetailModal.ServicePlanCost + deal.DealDetailModal.WarrantyCost
                      + deal.DealDetailModal.PaintProtectionCost + deal.DealDetailModal.TyreAndAlloyInsuranceCost +
                      deal.DealDetailModal.TyreInsuranceCost +
                      deal.DealDetailModal.WheelGuardCost
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}
                    </div>
                  </td>
                  <td>
                    <div class="value">
                      {{+(deal.DealDetailModal.CosmeticInsuranceCommission + deal.DealDetailModal.GapInsuranceCommission
                      )|cph:'currency':constants.environment.dealDetailModal_currencyDP}}</div>
                  </td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.AddOnProfitTotal
                      |cph:'currency':constants.environment.dealDetailModal_currencyDP}}</div>
                  </td>
                </tr>



              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Total F and I profit -->
      <div *ngIf="constants.environment.dealDetailModal_showAddOnProfit" class="autotrader-tile mb-3" id="fAndIProfit">
        <div class="tile-inner">
          <div class="dealSubTotal">
            <div>
              {{constants.translatedText.DealDetails_FinanceAddOnProfit}}
            </div>
            <div>
              {{deal.DealDetailModal.FinanceProfitTotal + deal.DealDetailModal.AddOnProfitTotal
              |cph:'currency':constants.environment.dealDetailModal_currencyDP}}
            </div>
          </div>
        </div>
      </div>

      <!-- OEM reference -->
      <div *ngIf="deal.DealDetailModal.OemReference" class="autotrader-tile mb-3" id="oemDetails">
        <div class="tile-inner">
          <div class="tile-header">
            OEM Details
          </div>
          <div class="tile-body">
            <table class="profitTable">

              <tbody>
                <tr>
                  <td class="label">{{constants.translatedText.StockItemModal_Chassis}}</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.Chassis}}</div>
                  </td>
                  <td class="label">OWM Order Reference</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.OemReference}}</div>
                  </td>
                </tr>
                <tr>
                  <td class="label">Sales Channel</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.SalesChannel}}</div>
                  </td>
                  <td class="label">Order type</td>
                  <td>
                    <div class="value">{{deal.DealDetailModal.OrdType}}</div>
                  </td>
                </tr>

              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Total profit exc. Bonus -->
      <div *ngIf="constants.environment.dealDetailModal_showTotalProfitExludingFactoryBonusSection"
        id="totalProfitExcBonus" class="autotrader-tile mb-3">
        <div class="tile-inner">
          <div class="dealSubTotal">
            <div>Total Profit Exc. Factory Bonus</div>
            <div>{{ deal.DealDetailModal.TotalProfit - deal.DealDetailModal.NewBonus1 |
              cph:'currency':constants.environment.dealDetailModal_currencyDP
              }}</div>
          </div>
        </div>
      </div>

      <!-- Total  profit -->
      <div *ngIf="constants.environment.dealDetailModal_showTotalProfitSection" class="autotrader-tile mb-3"
        id="totalProfit">
        <div class="tile-inner">
          <div class="dealSubTotal">
            <div>
              {{constants.translatedText.Common_TotalProfit}}
            </div>
            <div *ngIf="deal.DealDetailModal.IsRemoved" id="dealRemoved">
              {{constants.translatedText.DealDetails_DealRemoved}}
              {{deal.DealDetailModal.RemovedDate|cph:'dateShortYear':0}}
            </div>
            <div>
              {{deal.DealDetailModal.TotalProfit |cph:'currency':constants.environment.dealDetailModal_currencyDP}}
            </div>
          </div>
        </div>
      </div>

      <!-- Comments area -->
      <div class="autotrader-tile mb-3">
        <div class="tile-inner">

          <div class="tile-header">

            {{commentsOperationsMessage}}
           
          </div>
          <div class="tile-body">

            <div class=" d-flex justify-content-between align-items-center" id="commentsAndOps">
              <div class="d-flex justify-content-center">
                <div>Comments</div>
                <dealDetailComment [comments]="deal.Comments" [deal]="deal.DealDetailModal"></dealDetailComment>
              </div>


              <ng-container *ngIf="constants.environment.dealDetailModal_showDealFileSentDate">
                <div class="d-flex justify-content-center align-items-center">
                  <div>Dealfile Sent Date</div>
                  <dealfileSentDates [deal]="deal.DealDetailModal">
                  </dealfileSentDates>
                </div>
              </ng-container>


              <ng-container *ngIf="constants.environment.dealDetailModal_dealDetailsSection_showQualifyingPartEx">

                <div class="d-flex justify-content-center align-items-center">

                  <div class="canSetQualifyingPartExCheckBox"
                    *ngIf="selections.user.permissions?.canSetQualifyingPartEx">
                    <div>Qualifying Part-Exchange?</div>
                    <input type="checkbox" [(ngModel)]="deal.DealDetailModal.QualifyingPartEx"
                      (ngModelChange)="onCheckboxChange($event)" />
                  </div>

                  <div class="canSetQualifyingPartExCheckBox"
                    *ngIf="!selections.user.permissions?.canSetQualifyingPartEx">
                    <div>Qualifying Part-Exchange?</div>
                    <input type="checkbox" disabled [ngModel]="deal.DealDetailModal.QualifyingPartEx" />
                  </div>

                </div>

              </ng-container>
            </div>
          </div>
        </div>
      </div>

      <div
        *ngIf="selections.user.RoleName == 'System Administrator' && constants.environment.dealDetailModal_enableSalesExecPicker && deal.DealDetailModal.IsDelivered"
        class="autotrader-tile mb-3">
        <div class="tile-inner">

          <div class="tile-header">
            Amend Sales Exec
          </div>
          <div class="tile-body">

            <ng-container>

              <!-- On Vindis, system admins can change salesman id on delivered deals -->
              <div class="assignDifferentExec">

                <div ngbDropdown #dropdown="ngbDropdown" container="body" class="d-inline-block">
                  <button class="btn btn-primary" id="dropdownBasic1" ngbDropdownToggle>
                    {{ newSalesExec || 'Select New Salesman' }}
                  </button>
                
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <button class="dropdown-item" *ngFor="let salesman of salesmen"
                      (click)="onSalesmanSelect(salesman.SalesmanId); dropdown.close()">
                      {{ salesman.Label }}
                    </button>
                  </div>
                </div>

                </div>

            </ng-container>

          </div>
        </div>
      </div>

    </div>

    <!-- Stock item stuff -->
    <ng-template #stockDetails>
      <div id="stockDetailsContainer">
        <app-stockItemModalBody *ngIf="!constants.environment.dealDetailModal_enableStockItemModalAutotrader"></app-stockItemModalBody>
        <app-stockItemModalBodyAutoTrader
          *ngIf="constants.environment.dealDetailModal_enableStockItemModalAutotrader"></app-stockItemModalBodyAutoTrader>
      </div>
    </ng-template>
  </div>
  <div class="modal-footer">

    <button type="button" class="btn btn-primary"
      (click)="modal.dismiss('Cancelled')">{{constants.translatedText.Common_Close}}</button>
  </div>

</ng-template>