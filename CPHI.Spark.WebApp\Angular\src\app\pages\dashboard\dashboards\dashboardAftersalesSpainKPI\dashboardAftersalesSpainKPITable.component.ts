//core angular
import { Component, HostListener, Input, OnInit, Output } from '@angular/core';
import { localeEs } from 'src/environments/locale.es.js';
//pipes and interceptors
import { CphPipe } from 'src/app/cph.pipe';
//model and cell renderers

import { SiteVM } from 'src/app/model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { AutotraderService } from 'src/app/services/autotrader.service';
//services
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { SelectionsService } from 'src/app/services/selections.service';
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';

//Angular things, non-standard
import { AftersalesKPIRow } from './dashboardAftersalesSpainKPI.model';

import { DashboardKPIService } from './dashboardAftersalesSpainKPI.service';
import { BarComponent } from 'src/app/_cellRenderers/bar.component';
import { BarCurrencyAndPercentageComponent } from 'src/app/_cellRenderers/barCurrencyAndPercentage.component';
import { DashboardService } from '../../dashboard.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';



@Component({
  selector: 'dashboardAftersalesSpainKPITable',
  template:    `
    <div id='gridHolder'>

    <ag-grid-angular 
      id="FinanceAddOnsTable"
      [ngClass]="{'hidden':!showGrid}" class="ag-theme-balham" [gridOptions]="mainTableGridOptions"
      [pinnedBottomRowData]="totalRow"
      [rowData]="rowData"  [domLayout]="domLayout" [frameworkComponents]="frameworkComponents"  
      (gridReady)="onGridReady($event)" 
      [getRowNodeId]="getRowNodeId" 
      [animateRows]="false"
      [getMainMenuItems]="getMainMenuItems"> 
      
    </ag-grid-angular>
    <div  id="excelExport" (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>
  </div>
  `,
    styleUrls: ['./../../../../../styles/components/_agGrid.scss'],
    styles: [
      `
`
]
})



export class DashboardAftersalesSpainKPITableComponent implements OnInit {

  @Input() public rowData: Array<AftersalesKPIRow>;
  @Input() public totalRow: Array<AftersalesKPIRow>;

  @HostListener("window:resize", [])
  
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if(this.gridApi){
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  //main declarations
  //for agGrid
  showGrid = false;
  public gridApi;
  public importGridApi;
  public gridColumnApi;
  public getRowNodeId;

  amAlreadyUpdatingGrid: boolean = false;
  mainTableGridOptions: GridOptionsCph

  gridApiColumnDefinitions: any;


  pinnedBottomRowData: Array<any>;
  flipCols: Array<string>;
  currentRowHeight: number;
  domLayout: string;
  frameworkComponents: { agColumnHeader: any; };

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: DashboardKPIService,
    public dashService: DashboardService,
    public analysis: AutotraderService,
    public cphPipe: CphPipe,
    public columnTypeService: ColumnTypesService,
    public excel: ExcelExportService,
    public gridHelpers: AGGridMethodsService,

  ) {

    this.frameworkComponents = { agColumnHeader: CustomHeaderComponent };

    this.domLayout= "autoHeight";

    this.flipCols = []

    this.mainTableGridOptions = {
      localeTextFunc: (key: string, defaultValue: string) =>  this.constants.currentLang == 'es' ? localeEs[key] || defaultValue : defaultValue,
      context : { thisComponent: this },
      suppressPropertyNamesCheck : true,
      // onCellMouseOver: (params) => {
      //   //this.onCellMouseOver(params);
      // },
      onCellClicked: (params) => {
        this.onCellClick(params);
      },
    
      getRowHeight: (params)=> {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return normalHeight * 1.5;
        } else {
          return normalHeight;
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpers.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: [],
      getRowClass: (params) => {
        if (params.data.SiteId == 0) {
          return 'total';
        }
      }

    }
    
    this.mainTableGridOptions.columnDefs = [

      { headerName: "", field: "Label", colId: "Label", width: 150, type: "label", },
      {
        headerName: this.constants.translatedText.Dashboard_Aftersales_Bookings, width: 50, minWidth: 40, colId: 'Bookings', children: [

          { headerName: this.constants.translatedText.Dashboard_Aftersales_Mechanical, field: "Mechanical", colId: "Mechanical", width: 80, minWidth: 50, type: "number", children: [
            { headerName: this.constants.translatedText.Dashboard_Aftersales_1to3days, field: "MechanicalLessThan3", colId: "MechanicalLessThan3", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent, cellRendererParams: this.service.barThresholds },
            { headerName: this.constants.translatedText.Dashboard_Aftersales_4to5days, field: "Mechanical4To5", colId: "Mechanical4To5", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent, cellRendererParams: this.service.barThresholds },
            { headerName: this.constants.translatedText.Dashboard_Aftersales_5plusdays, field: "MechanicalGreaterThan5", colId: "MechanicalGreaterThan5", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent, cellRendererParams: this.service.barThresholds },
          ]},

          { headerName: this.constants.translatedText.Dashboard_Aftersales_Bodyshop, field: "Bodyshop", colId: "Bodyshop", width: 80, minWidth: 50, type: "number", children: [
            { headerName: this.constants.translatedText.Dashboard_Aftersales_1to3days, field: "BodyshopLessThan3", colId: "BodyshopLessThan3", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent, cellRendererParams: this.service.barThresholds },
            { headerName: this.constants.translatedText.Dashboard_Aftersales_4to5days, field: "Bodyshop4To5", colId: "Bodyshop4To5", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent, cellRendererParams: this.service.barThresholds },
            { headerName: this.constants.translatedText.Dashboard_Aftersales_5plusdays, field: "BodyshopGreaterThan5", colId: "BodyshopGreaterThan5", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent, cellRendererParams: this.service.barThresholds },
          ]},

        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_Aftersales_Revenue, width: 50, minWidth: 40, colId: 'Revenue', children: [
          { headerName: this.constants.translatedText.Dashboard_Aftersales_Service, field: "Service", colId: "Service", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarCurrencyAndPercentageComponent, cellRendererParams: { fieldForTotal: 'ServiceTarget', barClass: 'default' } },
          { headerName: this.constants.translatedText.Dashboard_Aftersales_Parts, field: "Parts", colId: "Parts", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarCurrencyAndPercentageComponent, cellRendererParams: { fieldForTotal: 'PartsTarget', barClass: 'default' } },
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_Aftersales_Performance, width: 50, minWidth: 40, colId: 'Performance', children: [
          { headerName: this.constants.translatedText.Dashboard_Aftersales_Productivity, field: "Productivity", colId: "Productivity", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent },
          { headerName: this.constants.translatedText.Dashboard_Aftersales_Efficiency, field: "Efficiency", colId: "Efficiency", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent },
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_Aftersales_WIP, width: 50, minWidth: 40, colId: 'WIP', children: [
          {
            headerName: this.constants.translatedText.Dashboard_Aftersales_Labour, colId: "Labour", width: 80, minWidth: 50, type: "label", children: [
              { headerName: this.constants.translatedText.Dashboard_Aftersales_lessThan15, field: "Labour1", colId: "Labour1", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarCurrencyAndPercentageComponent, cellRendererParams: { barClass: 'good', fieldForTotal: 'LabourTotal' } },
              { headerName: this.constants.translatedText.Dashboard_Aftersales_15to30, field: "Labour2", colId: "Labour2", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarCurrencyAndPercentageComponent, cellRendererParams: { barClass: 'ok', fieldForTotal: 'LabourTotal' } },
              { headerName: this.constants.translatedText.Dashboard_Aftersales_plus30, field: "Labour3", colId: "Labour3", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarCurrencyAndPercentageComponent, cellRendererParams: { barClass: 'bad', fieldForTotal: 'LabourTotal' } }
            ]
          },
          {
            headerName: this.constants.translatedText.Dashboard_Aftersales_Parts, colId: "WIPParts", width: 80, minWidth: 50, type: "label", children: [
              { headerName: this.constants.translatedText.Dashboard_Aftersales_lessThan15, field: "WIPParts1", colId: "WIPParts1", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarCurrencyAndPercentageComponent, cellRendererParams: { barClass: 'good', fieldForTotal: 'WIPPartsTotal' } },
              { headerName: this.constants.translatedText.Dashboard_Aftersales_15to30, field: "WIPParts2", colId: "WIPParts2", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarCurrencyAndPercentageComponent, cellRendererParams: { barClass: 'ok', fieldForTotal: 'WIPPartsTotal' } },
              { headerName: this.constants.translatedText.Dashboard_Aftersales_plus30,  field: "WIPParts3", colId: "WIPParts", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarCurrencyAndPercentageComponent, cellRendererParams: { barClass: 'bad', fieldForTotal: 'WIPPartsTotal' } }
            ]
          },
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_Aftersales_VHC, width: 50, minWidth: 40, colId: 'VHC', children: [
          { headerName: this.constants.translatedText.Dashboard_Aftersales_TechnicalControl, field: "TechnicalControl", colId: "TechnicalControl", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent },
          { headerName: this.constants.translatedText.Dashboard_Aftersales_AnomaliesPerVehicle, field: "AnomaliesPerVeh", colId: "AnomaliesPerVeh", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent },
          { headerName: this.constants.translatedText.Dashboard_Aftersales_VideoConversion,field: "VideoConversion", colId: "VideoConversion", width: 80, minWidth: 50, type: "label", cellRendererFramework: BarComponent },
        ]
      },
    ]

  }

  ngOnDestroy() { }

  ngOnInit() {
    //this.gridHelpers.topBottomHighlights = [];
  }

  initParams() {
  }

  onCellClick(params): void
  {

    let sites: SiteVM[] = [];
    
    this.dashService.chosenSites = [];

    // Filter for Region
    if(params.data.Label == 'Levante' || params.data.Label == 'Madrid')
    {
      let regionId: number = params.data.Label != 'Levante' ? 1 : 3;
      sites = this.constants.sitesActive.filter(x => x.RegionId == regionId).filter(x => x.IsEligibleForUser);;
    }
    // Total
    else if(params.data.Label == 'Total')
    {
      sites = this.constants.sitesActive.filter(x => x.IsEligibleForUser);;
    }
    // Individual site
    else
    {
      sites = this.constants.sitesActive.filter(x => x.SiteDescription == params.data.Label);
    }

    //console.log(this.dashService.chosenSites, "this.dashService.chosenSites!")
    this.dashService.chosenSites = sites;
    
    setTimeout(() => {

      this.goToDetail();

      // this.dashService.getNewDataTrigger.emit(null);
      
    }, 50)

    //console.log(this.dashService.chosenSites, "this.dashService.chosenSites1");

  };


  onGridReady(params): void {

    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);


    if(this.rowData){
      this.gridApi.setRowData(this.rowData);
      this.showGrid = true;
    }

    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();
    // this.selections.triggerSpinner.next({show:false});

  }

  private resizeGrid(): void {    if (this.gridApi)       this.gridApi.sizeColumnsToFit();  }

  private highlightTopAndWorstPerformers(params): string
  {

    if(!params){ return null; }

    if (params.colDef.goods && params.colDef.goods.indexOf(params.data.siteName) > -1) {
      return 'good agAlignRight';
    } else if (params.colDef.bads && params.colDef.bads.indexOf(params.data.siteName) > -1) {
      return 'bad agAlignRight';
    }

    return null;
  }


  cellClassProviderWithColourFont(params): string {

    let goodOrBadFormat: string = this.highlightTopAndWorstPerformers(params);

    return goodOrBadFormat == null ? this.numberValueFormatting(params) : goodOrBadFormat ;
  }

  private numberValueFormatting(params): string { 
    return params && params.value < 0 ? 'badFont  agAlignRight': 'agAlignRight' 
  }

  getMainMenuItems() {    return this.gridHelpers.getMainMenuItems()  }

  highlightTopBottom(colDef: any, topBottomN: number) {

    let values = [];
    let rows = [];
    let flip = 1;
    if (this.flipCols.indexOf(colDef.colId) > -1) { flip = -1; }

    this.gridApi.forEachNodeAfterFilter((node) => {
      values.push(node.data);
      rows.push(node);
    })

    values.sort((a, b) => {
      return this.constants.getNestedItem(a, colDef.field) * flip - this.constants.getNestedItem(b, colDef.field) * flip
    })

    let goods = []
    for (let i = values.length - 1; i > values.length - topBottomN - 1; i--) {
      goods.push(values[i].siteName)
    }

    let bads = []
    for (let i = 0; i < topBottomN; i++) {
      bads.push(values[i].siteName)
    }

    colDef.goods = goods;
    colDef.bads = bads;

    this.gridApi.redrawRows(rows)

  }

  clearHighlighting(colDef: any) {    this.gridHelpers.clearHighlighting(colDef,this.gridApi)  }

  removeHighlighting() {
    this.mainTableGridOptions.columnDefs.forEach(colDef => {
      this.clearHighlighting(colDef);
    })
  }

  goToDetail(){
    let page = this.dashService.chosenSection.pages.filter(x => x.pageName == 'AftersalesDetail')[0];
    this.dashService.chosenPage = page;
  }


  excelExport(): void {

    let tableModel = this.gridApi.getModel();

    // console.log(tableModel, "tableModel!")

    // Changes here to amend the column formatting
    tableModel.columnModel.primaryColumns.forEach(element => {

      if(element.colDef?.field?.includes('WIP') || element.colDef?.field?.includes('Labour') )
      {
        element.colDef.type = 'currency';
      }

      if(element.colDef?.field?.includes('Service') || element.colDef?.field?.includes('Parts') )
      {
        element.colDef.type = 'currency';
      }

    });

    // There are also some changes specific to this table within this function below to prevent duplicating headers
    this.excel.createSheetObject(tableModel, this.constants.translatedText.Aftersales + ' ' + this.constants.translatedText.Dashboard_KPIs, 1.6, 1);
  }

}
