<!-- <nav class="navbar">
  <nav *ngIf="dataPack" class="generic">
    
  </nav>
</nav> -->

<!-- Main Page -->
<div class="navbar-grid cols-14">

  <div class="navbar-grid-col grid-col-1-7">

    <div id="menuButtonHolder">

      {{constants.translatedText.New}}: &nbsp;

      <!-- Order type picker - New -->
      <pickerSimple 
        [pickerItemsFromParent]="service.newOrderTypeTypes"
        [pickerLabel]="constants.translatedText.DealOptions_OrderTypes"
        (selectedPickerItems)="saveTypesAndReload($event, true)">
      </pickerSimple>


      <div id ="monthsAndFranchiseHolder">

        <!-- FOR SELECTING MONTH -->
        <div class="buttonGroup">
          <!-- previousMonth -->
          <button class="btn btn-primary" (click)="service.changeMonth(-1)"><i
              class="fas fa-caret-left"></i></button>

          <!-- dropdownMonth -->
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button class="btn btn-primary centreButton"
              ngbDropdownToggle>{{service.chosenMonthStart|cph:'month':0}}</button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

              <!-- the ngFor buttons -->
              <button *ngFor="let month of service.months" (click)="service.selectMonth(month)"
                ngbDropdownItem>{{month
                |cph:'month':0}}</button>
            </div>
          </div>
          <!-- nextMonth -->
          <button class="btn btn-primary" (click)="service.changeMonth(1)"><i
              class="fas fa-caret-right"></i></button>
        </div>

      </div>

    </div>
  </div>

  <div class="navbar-grid-col grid-col-7-14">

        <!-- Franchise selector -->
        <franchisePicker [franchisesFromParent]="service.franchises"
          [buttonClass]="{ 'buttonGroupRight': constants.environment.spainFranchisePickerClass }"
          (updateFranchises)="service.onUpdateFranchises($event)">
        </franchisePicker>

          <!-- Order type picker - Used -->
        {{constants.translatedText.Used}}: &nbsp;
        <pickerSimple [pickerItemsFromParent]="service.usedOrderTypeTypes"
          [pickerLabel]="constants.translatedText.DealOptions_OrderTypes"
          (selectedPickerItems)="saveTypesAndReload($event)">
        </pickerSimple>
  </div>

  <!-- <div class="grid-col-7-15">

  </div> -->

</div>

<div class="dashboard-grid-container" *ngIf="!!dataPack">
  <div id="spain-overview-grid" class="dashboard-grid cols-14">

    <!-- Daily Orders 7 day chart New  -->
    <div class="dashboard-tile grid-col-1-4 grid-row-1-3 spainNewTile">
      <dailyOrdersVsLastYear [newDataEmitter]="newDataEmitter" [tileType]="'New'"
        [data]="dataPack.SpainDailyNetOrdersNew" [dataSource]="'DistriNET'"></dailyOrdersVsLastYear>

    </div>


    <!-- Orders donut Tile New -->
    <div class="dashboard-tile grid-col-4-7 grid-row-1-3 spainNewTile">
      <ordersDonut [newDataEmitter]="newDataEmitter" [data]="dataPack.OrdersDonutSpainNew" [departmentName]="'New'"
        [dataSource]="'DistriNET'">
      </ordersDonut>
    </div>

    <!-- Daily Orders 7 day chart Used  -->
    <div class="dashboard-tile grid-col-7-10 grid-row-1-3 spainUsedTitle">
      <dailyOrdersVsLastYear [newDataEmitter]="newDataEmitter" [tileType]="'Used'"
        [data]="dataPack.SpainDailyNetOrdersUsed" [dataSource]="'RATIO'"></dailyOrdersVsLastYear>
    </div>


    <!-- Orders donut Tile Used -->
    <div class="dashboard-tile grid-col-10-13 grid-row-1-3 spainUsedTitle">
      <ordersDonut [newDataEmitter]="newDataEmitter" [data]="dataPack.OrdersDonutSpainUsed" [departmentName]="'Used'"
        [dataSource]="'RATIO'">
      </ordersDonut>
    </div>



    <!-- --------------------------------------
    All the right hand column tiles
    -------------------------------------- -->


    <!-- Scrap Vehicles -->
    <div class="dashboard-tile grid-col-13-15 grid-row-1-2 spainUsedTitle">
      <unitsAndValue [title]="constants.translatedText.ScrapVehicles" [data]="dataPack.ScrapVehicles"
        [newDataEmitter]="newDataEmitter" [dataSource]="'Quiter'">
      </unitsAndValue>
    </div>

    <!-- Assigment Vehicles -->
    <div class="dashboard-tile grid-col-13-15 grid-row-2-3 spainUsedTitle" *ngIf="selections.user.JobTitle != 'Advanced User'">

      <assignmentTile [title]="constants.translatedText.Assignments" [data]="dataPack.AssigmentVehicles"
      [newDataEmitter]="newDataEmitter" [dataSource]="'Quiter'">
      </assignmentTile>
      
    </div>

    <!-- Reconditioning Tile -->
    <div class="dashboard-tile grid-col-13-15 grid-row-3-4 spainUsedTitle">
      <reconditioningTile [title]="constants.translatedText.Reconditioning"
        [data]="dataPack.ReconditioningSummary" [newDataEmitter]="newDataEmitter" [dataSource]="'Quiter'">
      </reconditioningTile>
    </div>

    <!-- Commissions Tile -->
    <div class="dashboard-tile grid-col-13-15 grid-row-4-5 spainUsedTitle" *ngIf="selections.user.JobTitle != 'Advanced User'">
      <commissionsTile [newDataEmitter]="newDataEmitter" [title]="constants.translatedText.Commission"
        [data]="dataPack.Commissions" [dataSource]="'BPC'"></commissionsTile>
    </div>

    <!-- Alcopas Summary Tile -->
    <div class="dashboard-tile grid-col-13-15 grid-row-5-6 spainUsedTitle">
      <alcopasSummaryTile [title]="constants.translatedText.Dashboard_Alcopa_Title" [data]="dataPack.Alcopas"
        [dataSource]="'Alcopa'"></alcopasSummaryTile>
    </div>





    <!-- Invoicing Peformance New (franchise split) -->
    <div class="dashboard-tile grid-col-1-4 grid-row-3-5 spainNewTile">
      <!-- <invoicingPerformance [title]="constants.translatedText.Dashboard_InvoicingPerformanceNew"
        [data]="dataPack.InvoicingPerformanceNew" [dataSource]="'Quiter'">
      </invoicingPerformance> -->
    </div>


    <!-- Invoice donut New -->
    <div class="dashboard-tile grid-col-4-7 grid-row-3-5 spainNewTile">
      <donutTile [newDataEmitter]="newDataEmitter" [data]="dataPack.InvoicedDonutDataNew" [timePeriod]="'Month'"
        [departmentName]="'New'" [dataSource]="'Quiter'"></donutTile>
    </div>

    <!-- Invoicing Peformance Used (franchise split) -->
    <div class="dashboard-tile grid-col-7-10 grid-row-3-5 spainUsedTitle">
      <!-- <invoicingPerformance [title]="constants.translatedText.Dashboard_InvoicingPerformanceUsed"
        [data]="dataPack.InvoicingPerformanceUsed" [dataSource]="'Quiter'">
      </invoicingPerformance> -->
    </div>

    <!-- Invoice donut Used -->
    <div class="dashboard-tile grid-col-10-13 grid-row-3-5 spainUsedTitle">
      <donutTile [newDataEmitter]="newDataEmitter" [data]="dataPack.InvoicedDonutDataUsed" [timePeriod]="'Month'"
        [departmentName]="'Used'" [dataSource]="'Quiter'"></donutTile>
    </div>

    <!-- --------------------------------------
    All the overage tiles
    -------------------------------------- -->

    <!-- Overage New 0 -->
    <div class="dashboard-tile grid-col-1-3 grid-row-5-6 spainNewTile">
      <stockOverageTile [newDataEmitter]="newDataEmitter" [department]="'New'" [agedOver]="0"
        [data]="dataPack.OverageStockSummary" [dataSource]="'DistriNET'">
      </stockOverageTile>
    </div>

    <!-- Overage New 90 -->
    <div class="dashboard-tile grid-col-3-5 grid-row-5-6 spainNewTile">
      <stockOverageTile [newDataEmitter]="newDataEmitter" [department]="'New'" [agedOver]="90"
        [data]="dataPack.OverageStockSummary" [dataSource]="'DistriNET'">
      </stockOverageTile>
    </div>


    <!-- Overage New 180 -->
    <div class="dashboard-tile grid-col-5-7 grid-row-5-6 spainNewTile">
      <stockOverageTile [newDataEmitter]="newDataEmitter" [department]="'New'" [agedOver]="180"
        [data]="dataPack.OverageStockSummary" [dataSource]="'DistriNET'">
      </stockOverageTile>
    </div>


    <!-- Overage Used 0 -->
    <div class="dashboard-tile grid-col-7-9 grid-row-5-6 spainUsedTitle">
      <stockOverageTile [newDataEmitter]="newDataEmitter" [department]="'Used'" [agedOver]="0"
        [data]="dataPack.OverageStockSummary" [dataSource]="'Quiter'">
      </stockOverageTile>
    </div>

    <!-- Overage Used 90 -->
    <div class="dashboard-tile grid-col-9-11 grid-row-5-6 spainUsedTitle">
      <stockOverageTile [newDataEmitter]="newDataEmitter" [department]="'Used'" [agedOver]="90"
        [data]="dataPack.OverageStockSummary" [dataSource]="'Quiter'">
      </stockOverageTile>
    </div>

    <!-- Overage Used 180 -->
    <div class="dashboard-tile grid-col-11-13 grid-row-5-6 spainUsedTitle">
      <stockOverageTile [newDataEmitter]="newDataEmitter" [department]="'Used'" [agedOver]="180"
        [data]="dataPack.OverageStockSummary" [dataSource]="'Quiter'">
      </stockOverageTile>
    </div>
  </div>
</div>