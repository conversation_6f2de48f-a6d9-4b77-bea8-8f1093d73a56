﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace CPHI.Spark.Model.Import
{
    public class Deal_CarCo  //IS AN EXACT COPY OF DEAL
    {
        public int Id { get; set; }


        //Foreign Keys, many in this table go to 1 in that table
        public int? Salesman_Id { get; set; }
        public int? Site_Id { get; set; }
        public int VehicleClass_Id { get; set; }
        public int Franchise_Id { get; set; }
        public int? OrderType_Id { get; set; }
        public int VehicleType_Id { get; set; }
        public int? DeliverySite_Id { get; set; }
        public int? VehicleAdvert_Id { get; set; }







        //Vehicle
        public string Reg { get; set; }
        public string StockNumber { get; set; }
        public string Model { get; set; }
        public int ModelYear { get; set; }
        public DateTime? StockDate { get; set; }
        public string Description { get; set; }
        public int VehicleAge { get; set; }
        public string Variant { get; set; }
        public string VariantTxt { get; set; }
        public string LastPhysicalLocation { get; set; }
        public DateTime? RegisteredDate { get; set; }




        //Order
        public bool IsRemoved { get; set; }
        public DateTime? RemovedDate { get; set; }
        public bool IsUpdated { get; set; }
        public int EnquiryNumber { get; set; }
        public string Customer { get; set; }
        public bool IsLateCost { get; set; }
        public bool IsDelivered { get; set; }
        public DateTime OrderDate { get; set; }
        public DateTime? ActualDeliveryDate { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public DateTime AccountingDate { get; set; }
        public string OemReference { get; set; }
        public bool IsFinanced { get; set; }
        public string FinanceCo { get; set; }
        public string FinanceType { get; set; }
        public bool IsClosed { get; set; }
        public bool? AuditPass { get; set; }
        public int Units { get; set; }
        public DateTime? HandoverDate { get; set; }
        public string VehicleSource { get; set; }


        //Metal Profit
        public decimal Sale { get; set; }
        public decimal Discount { get; set; }
        public decimal CoS { get; set; }
        public decimal PartExOverAllowance1 { get; set; }
        public decimal VatCost { get; set; }
        public decimal NewBonus1 { get; set; }
        public decimal NewBonus2 { get; set; }


        //Other Profit
        public decimal AccessoriesSale { get; set; }
        public decimal AccessoriesCost { get; set; }
        public decimal FuelSale { get; set; }
        public decimal FuelCost { get; set; }
        public decimal BrokerCost { get; set; }
        public decimal IntroCommission { get; set; }
        public decimal OemDeliverySale { get; set; }
        public decimal OemDeliveryCost { get; set; }
        public decimal PDICost { get; set; }
        public decimal MechPrep { get; set; }
        public decimal BodyPrep { get; set; }
        public decimal Other { get; set; }
        public decimal Error { get; set; }
        public decimal StandardWarrantyCost { get; set; }
        public bool HasPaintProtectionAccessory { get; set; }
        public decimal PaintProtectionAccessorySale { get; set; }
        public decimal PaintProtectionAccessoryCost { get; set; }

        //Finance Profit
        public decimal FinanceCommission { get; set; }
        public decimal FinanceSubsidy { get; set; }
        public decimal SelectCommission { get; set; }
        public decimal RCIFinanceCommission { get; set; }
        public decimal StandardsCommission { get; set; }
        public decimal ProPlusCommission { get; set; }


        //Addon Profit
        public bool HasCosmeticInsurance { get; set; }
        public decimal CosmeticInsuranceSale { get; set; }
        public decimal CosmeticInsuranceCost { get; set; }
        public decimal CosmeticInsuranceCommission { get; set; }
        public bool HasGapInsurance { get; set; }
        public decimal GapInsuranceSale { get; set; }
        public decimal GapInsuranceCost { get; set; }
        public decimal GapInsuranceCommission { get; set; }
        public bool HasPaintProtection { get; set; }
        public decimal PaintProtectionSale { get; set; }
        public decimal PaintProtectionCost { get; set; }
        public bool HasServicePlan { get; set; }
        public decimal ServicePlanSale { get; set; }
        public decimal ServicePlanCost { get; set; }

        public bool HasWarranty { get; set; }
        public bool HasShortWarranty { get; set; }

        public decimal WarrantySale { get; set; }
        public decimal WarrantyCost { get; set; }
        public bool? HasWheelGuard { get; set; }
        public decimal WheelGuardSale { get; set; }
        public decimal WheelGuardCost { get; set; }
        public decimal WheelGuardCommission { get; set; }  //renamed
        public bool HasTyreInsurance { get; set; }
        public decimal TyreInsuranceSale { get; set; }
        public decimal TyreInsuranceCost { get; set; }
        public decimal TyreInsuranceCommission { get; set; }
        public bool HasAlloyInsurance { get; set; }
        public decimal AlloyInsuranceSale { get; set; }
        public decimal AlloyInsuranceCost { get; set; }
        public decimal AlloyInsuranceCommission { get; set; }
        public bool HasTyreAndAlloyInsurance { get; set; }
        public decimal TyreAndAlloyInsuranceSale { get; set; }
        public decimal TyreAndAlloyInsuranceCost { get; set; }
        public decimal TyreAndAlloyInsuranceCommission { get; set; }

        public decimal TotalNLProfit { get; set; }  //ok

        //Other fields to retain but not used in front end

        [StringLength(250)]
        public DateTime? OrigOrderDate { get; set; } //for late costs, order date of original deal
        public DateTime? OrigDeliveryDate { get; set; } //for late costs, delivery date of original deal

        public DateTime CreatedDate { get; set; }
        public DateTime WhenNew { get; set; }
        public bool IsInvoiced { get; set; }
        public DateTime LastUpdated { get; set; }
        public string OriginalSource { get; set; }

        public string ModelCode { get; set; }
        public string MarketCode { get; set; }
        public string SegmentCode { get; set; }
        public string StatusCode { get; set; }

        [MaxLength(50)]
        public string ChannelVoDescription { get; set; }
        [MaxLength(50)]
        public string TypeVoDescription { get; set; }
        [MaxLength(50)]
        public string InvoiceNo { get; set; }
    }
}

