﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.DataAccess;
using Microsoft.Extensions.Configuration;

namespace CPHI.Spark.WebApp
{
    public interface IStockListCache
    {
        void EmptyCache(string dealerGroupName);
        Task<IEnumerable<StockListRow>> GetStockListRows(Model.DealerGroupName dealerGroup);
    }


    public class StockListCache : IStockListCache
    {
        private readonly IConfiguration _configuration;

        public StockListCache(IConfiguration configuration)
        {
            this._configuration = configuration;
        }



        public async Task<IEnumerable<StockListRow>> GetStockListRows(Model.DealerGroupName dealerGroup)
        {
            string cacheName = $"{dealerGroup.ToString()}|StockListRows";

            if (!MemoryCache.Default.Contains(cacheName))
            {
                await FillCache(dealerGroup);
            }

            return (IEnumerable<StockListRow>)MemoryCache.Default.GetCacheItem(cacheName).Value;
        }


        public void EmptyCache(string dealerGroupName)
        {
            MemoryCache.Default.Remove($"{dealerGroupName}|StockListRows");
        }










        //--------- Private Methods
        private async Task FillCache(Model.DealerGroupName dealerGroup)
        {
            IEnumerable<StockListRow> rows = await MakeStockListRows(dealerGroup);

            lock (MemoryCache.Default)
            {
                DateTime tonight = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);
                MemoryCache.Default.Add(new CacheItem($"{dealerGroup.ToString()}|StockListRows", rows), new CacheItemPolicy() { AbsoluteExpiration = tonight });
            }

        }


        private async Task<IEnumerable<StockListRow>> MakeStockListRows(Model.DealerGroupName dealerGroup)
        {
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string _connectionString = _configuration.GetConnectionString(dgName);
            StockDataAccess stockDataAccess = new StockDataAccess(_connectionString);

            IEnumerable<StockListRow> mainDetails = await stockDataAccess.GetStockListRows(dealerGroup);
            int dealerGroupId = DealerGroupStockpulseId.Get(dealerGroup);
            if (dealerGroupId != 0)
            {
                //we use stockpulse, so get the latest
                string spConnString = _configuration.GetConnectionString("StockTakePhotos");
                IEnumerable<StockDetailStockpulse> stockCheckDetails = await stockDataAccess.GetLatestStockCheckScans(dealerGroupId, spConnString);
                foreach (var item in stockCheckDetails)
                {
                    if (IsValidField(item.Reg))
                    {
                        StockListRow matchingItem = mainDetails.FirstOrDefault(x => x.Reg == item.Reg);
                        UpdateMatchingItem(item, matchingItem);
                    }
                    else if (IsValidField(item.Vin))
                    {
                        StockListRow matchingItem = mainDetails.FirstOrDefault(x => x.Chassis.Substring(Math.Max(0, x.Chassis.Length - 7)) == item.Vin);
                        UpdateMatchingItem(item, matchingItem);
                    }
                }
                return mainDetails;
            }
            else
            {
                return mainDetails;
            }

        }


        private static void UpdateMatchingItem(StockDetailStockpulse item, StockListRow matchingItem)
        {
            if (matchingItem != null)
            {
                matchingItem.StockcheckLocation = item.Location;
                matchingItem.StockcheckScanDate = item.ScanDateTime;
                matchingItem.StockcheckScannedBy = item.ScannerName;
                matchingItem.SeenAtLatestStkchk = true;
            }
        }

        private bool IsValidField(string fieldValue)
        {
            return fieldValue != null && fieldValue != "" && fieldValue != string.Empty && fieldValue != " " && fieldValue != "INPUT" && fieldValue != "TBC";
        }



    }




}

