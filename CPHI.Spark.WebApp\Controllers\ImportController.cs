﻿using System;
using System.Collections.Generic;
using CPHI.Spark.Model.ViewModels;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.Model.ViewModels.Vindis;
using Microsoft.Extensions.Primitives;
using System.Text.Json;
using CPHI.Spark.Model.ViewModels.Vindis.EventHub;
using System.Linq;

namespace CPHI.Spark.WebApp.Controllers
{
    [Route("api/[controller]")]
    [ApiController]

    public class ImportController : ControllerBase
    {

        private readonly IDealService dealService;
        private readonly int userId;
        public ImportController(IDealService dealService)
        {
            this.dealService = dealService;
        }



        [HttpPost]
        [Route("Deal")]
        public async Task<IActionResult> UpdateDealForVindis([FromBody] object quotation)
        {
            //Validate the Key from header - key will harded coded
            StringValues key;
            var headers = Request.Headers.TryGetValue("Key", out key);

            //Validate the Key
            if (key == "THIS-IS-A-KEY-FOR-VINDIS")
            {
                try
                {

                    var quotationString = quotation.ToString();
                    await this.dealService.InsertDealQuotation(DealerGroupName.Vindis, Request.Path.Value, quotationString);

                    return Ok();
                }
                catch (Exception ex)
                {
                    return BadRequest(ex.Message);
                }
            }
            else
            {
                //Invalid Key
                return BadRequest();
            }


        }




       

    }
}