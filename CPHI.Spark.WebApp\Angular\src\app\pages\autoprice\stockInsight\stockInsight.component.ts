import { TitleCasePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { BIChartTileDataType } from 'src/app/components/biChartTile/biChartTile.component';
import { BlobItem } from 'src/app/components/blobChart/blobItem';
import { CphPipe } from 'src/app/cph.pipe';
import { AnalysisDimensionPipeTypeEnum } from "src/app/model/AnalysisDimensionPipeTypeEnum";
import { StockInsightFiltersForStockReport } from 'src/app/model/StockInsightFiltersForStockReport';
import { VehicleValutionBatch } from 'src/app/model/VehicleValuationBatch';
import { AdvertListingDetailService } from 'src/app/pages/autoprice/advertListingDetail/advertListingDetail.service';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { StockInsightService } from './stockInsight.service';
import { VehicleAdvertWithRating } from 'src/app/model/VehicleAdvertWithRating';
import { GlobalParamsService } from 'src/app/services/globalParams.service';
import { GlobalParamKey } from 'src/app/model/GlobalParam';
import { MenuItemNew } from 'src/app/model/main.model';

@Component({
  selector: 'app-stockInsight',
  templateUrl: './stockInsight.component.html',
  styleUrls: ['./stockInsight.component.scss']
})
export class StockInsightComponent implements OnInit {

  public dataTypes = BIChartTileDataType;

  //Subs
  vehicleTableDataUpdatedSub: Subscription;
  highlightChoiceMadeSub: Subscription;
  filterChoiceMadeSub: Subscription;
  filterSiteSub: Subscription;
  refreshTileSubscriber: Subscription;

  PipeTypes = AnalysisDimensionPipeTypeEnum;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public service: StockInsightService,
    public cphPipe: CphPipe,
    public titleCasePipe: TitleCasePipe,
    public modalService: NgbModal,
    public router: Router,
    public advertListingDetailService: AdvertListingDetailService,
    private globalParamsService: GlobalParamsService
  ) { }

  ngOnInit() {
    this.initParams()
    this.buildSubscriptions();

  }

  initParams() {

    if (!this.service.includeUnPublishedAdverts) {
      this.service.includeUnPublishedAdverts = (this.globalParamsService.getGlobalParam(GlobalParamKey.webAppShowDefaultShowUnPublishedVehicles) as boolean);
    }
    if (!this.service.includeNewVehicles) {
      this.service.includeNewVehicles = this.constants.autopriceEnvironment.defaultShowNewVehicles
    }

    if (!this.service.rawData) {
      this.service.initParams();
      this.service.getAnalysisDimensionsThenData();
    }
    else {
      this.selections.triggerSpinner.emit({ show: false })
    }

  }
  
  toggleIncludeNewVehicles(): void {
    this.service.includeNewVehicles = !this.service.includeNewVehicles;
    this.service.getData();
  }

  toggleIncludeUnPublishedAds(): void {
    this.service.includeUnPublishedAdverts = !this.service.includeUnPublishedAdverts;
    this.service.getData();
  }

  provideVehTypeCount(vehType: string) {
    if (this.service.rawData) {
      return this.service.rawData.filter(x => x.VehicleTypeDesc == vehType).length
    }
  }


  provideLifecycleStatusCount(vehType: string) {
    return this.service.rawData.filter(x => x.LifecycleStatus == vehType).length
  }

  onChosenLifecycleStatusChange() {
    this.service.getAnalysisDimensionsThenData();
  }
  onChosenVehTypesChange() {
    this.service.getAnalysisDimensionsThenData();
  }

  private buildSubscriptions() {

    this.vehicleTableDataUpdatedSub = this.service.vehicleTableDataUpdatedEmitter.subscribe((res: VehicleAdvertWithRating[]) => {
      let newBlobs: BlobItem[];
      const lookup = {};

      if (this.service.viewByBulkUpload) {

      } else {
        newBlobs = this.service.makeBlobItems(this.service.rawDataFiltered, this.service.analysisDimensions);

        res.map(x => x.AdId).forEach(item => {
          lookup[item] = true;
        });

        newBlobs.map(blob => {
          blob.IsActive = lookup[blob.Id];
        });
        this.service.newBlobsEmitter.emit(newBlobs);
      }


    });


    this.highlightChoiceMadeSub = this.service.highlightChoiceMadeEmitter.subscribe(res => {
      this.service.highlightItems();
      let newBlobs: BlobItem[];
      const lookup = {};

      if (this.service.viewByBulkUpload) {
        this.service.recalculateSummaryStatsBulkUpload();
        newBlobs = this.service.makeBlobItemsBulk(this.service.vehicleValuationBatchResults, this.service.analysisDimensions);

        this.service.vehicleValuationBatchResultsHighlighted.map(x => x.ValuationId).forEach(item => {
          lookup[item] = true;
        });

        newBlobs.map(blob => {
          blob.IsActive = lookup[blob.Id];
        });
        this.service.newBlobsEmitter.emit(newBlobs);

      } else {

        //have to re-do blobs here to work out if active
        this.service.blobItems = this.service.makeBlobItems(this.service.rawDataFiltered, this.service.analysisDimensions);
        this.service.newBlobsEmitter.emit(this.service.blobItems);
        this.service.recalculateSummaryStats();
      }


    });

    this.filterChoiceMadeSub = this.service.filterChoiceMadeEmitter.subscribe(res => {
      this.service.filterItems();
      if (this.service.viewByBulkUpload) {
        this.service.recalculateSummaryStatsBulkUpload();
      } else {
        this.service.recalculateSummaryStats();
      }
    });

    this.refreshTileSubscriber = this.service.refreshTileEmitter.subscribe(res => {
      // this.mainTableGridOptions = this.initialiseVehicleDetailsGrid();
    });
  }

  ngOnDestroy() {
    if (!!this.vehicleTableDataUpdatedSub) { this.vehicleTableDataUpdatedSub.unsubscribe() }
    if (!!this.highlightChoiceMadeSub) { this.highlightChoiceMadeSub.unsubscribe() }
    if (!!this.filterSiteSub) this.filterSiteSub.unsubscribe();
    if (this.refreshTileSubscriber) this.refreshTileSubscriber.unsubscribe();
  }


  clearHighlights() {

    this.service.highlightChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.service.highlightItems();
    this.service.initParams(true);
    this.service.refreshTileEmitter.emit();
  }

  viewByBulkUpload(shouldShow: boolean) {
    this.selections.triggerSpinner.emit({ show: true, message: 'Loading...' });

    this.service.viewByBulkUpload = shouldShow;

    this.service.getAnalysisDimensionsThenData();
    //this.service.getData();

    if (!shouldShow) {
      this.service.recalculateSummaryStats();
      this.service.refreshTileEmitter.emit();
    }
  }

  maybeRevalueVehicleValuationBatch() {
    this.constants.confirmModal.showModal('Are you sure?', 'This will generate a new set of valuations for these vehicles. The existing valuation set will also remain available');

    const confirmModalSubscription: Subscription = this.selections.confirmModalEmitter.subscribe(res => {
      if (res) this.service.revalueVehicleValuationBatch();
      confirmModalSubscription.unsubscribe();
    })
  }

  selectBatch(batch: VehicleValutionBatch) {
    this.service.chosenVehicleValuationBatch = batch;
    this.service.getValuationBatchResults();
  }

  goToStockReports() {
    this.buildFilterParams();
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/stockReports');
    if (menuItem) { this.constants.navigateByUrl(menuItem); } //, 'operationreports'
  }

  buildFilterParams() {
    let filterModel = {};
    const filters: StockInsightFiltersForStockReport = this.service.stockInsightFiltersForStockReport;

    if (filters.PerformanceRatingScoreBand) {
      filterModel['PerformanceRatingScoreBand'] = {
        filterType: 'set',
        values: filters.PerformanceRatingScoreBand
      }
    }

    if (filters.DaysListedBand) {
      filterModel['DaysListedBand'] = {
        filterType: 'set',
        values: filters.DaysListedBand
      }
    }

    if (filters.ValueBand) {
      filterModel['ValueBand'] = {
        filterType: 'set',
        values: filters.ValueBand
      }
    }

    if (filters.RetailerSiteName) {
      filterModel['RetailerSiteName'] = {
        filterType: 'set',
        values: filters.RetailerSiteName
      }
    }

    if (filters.SiteBrand) {
      filterModel['SiteBrand'] = {
        filterType: 'set',
        values: filters.SiteBrand
      }
    }

    if (filters.AgeBand) {
      filterModel['AgeBand'] = {
        filterType: 'set',
        values: filters.AgeBand
      }
    }

    if (filters.Model) {
      filterModel['Model'] = {
        filterType: 'set',
        values: filters.Model
      }
    }

    if (filters.RetailRatingBand) {
      filterModel['RetailRatingBand'] = {
        filterType: 'set',
        values: filters.RetailRatingBand
      }
    }

    this.advertListingDetailService.setExternalFilterModel(filterModel);
  }

  setChosenDate(event: any) {
    this.service.chosenEffectiveDate = event.target.value;
    //this.service.getData();
    this.service.getAnalysisDimensionsThenData();
  }

  toggleUseTestStrategy(): void
  {
    this.service.useTestStrategy = !this.service.useTestStrategy;
    this.service.getAnalysisDimensionsThenData();
  }
}
