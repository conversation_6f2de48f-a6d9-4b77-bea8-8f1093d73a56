﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Migrations\AppDb\**" />
    <EmbeddedResource Remove="Migrations\AppDb\**" />
    <None Remove="Migrations\AppDb\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Functions\functions.text" />
    <None Remove="Indexes\indexes.txt" />
    <None Remove="MgmtScripts\AutoPriceUpdateTable.txt" />
    <None Remove="MgmtScripts\BCP_bounce.txt" />
    <None Remove="MgmtScripts\CreateExampleItemsRRGUK.txt" />
    <None Remove="MgmtScripts\DaysToSellAndPriceIndicator.sql" />
    <None Remove="MgmtScripts\DepartmentDeal Canonical Record.txt" />
    <None Remove="MgmtScripts\MoveSps to admin schema.txt" />
    <None Remove="MgmtScripts\reloadTranslations.sql" />
    <None Remove="MgmtScripts\SpainUpgradeOrderTypes.txt" />
    <None Remove="MgmtScripts\updateToVersion3 RRG.txt" />
    <None Remove="MgmtScripts\updateToVersion3 Spain.txt" />
    <None Remove="MgmtScripts\updateToVersion3 Vindis.txt" />
    <None Remove="MgmtScripts\updateToVersion3.txt" />
    <None Remove="ReleaseScripts\BCP_bounce.txt" />
    <None Remove="ReleaseScripts\newSpainSite.sql" />
    <None Remove="ReleaseScripts\release24June2022.txt" />
    <None Remove="Types\Types.txt" />
    <Compile Remove="Migrations\20211218120856_added departments.cs" />
    <Compile Remove="Migrations\20211218120856_added departments.Designer.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CPHI.Spark.Model\CPHI.Spark.Model.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="morelinq" Version="4.2.0" />
    <PackageReference Include="Microsoft.DotNet.UpgradeAssistant.Extensions.Default.Analyzers" Version="0.4.421302">
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Functions\" />
    <Folder Include="Indexes\" />
    <Folder Include="ReleaseScripts\" />
    <Folder Include="StoredProcedures\SPs_for_Spain\" />
    <Folder Include="Types\" />
  </ItemGroup>
</Project>