import { Component, HostListener, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CellClickedEvent, ColDef, ColGroupDef, GridApi, RowClickedEvent } from 'ag-grid-community';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../cph.pipe';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from '../../services/agGridMethods.service';
import { AutotraderService } from '../../services/autotrader.service';
import { ConstantsService } from '../../services/constants.service';
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from '../../services/selections.service';
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { OrdersBySiteBarComponent } from './horizontalBar.component';
import { OrdersForSite, OrdersSpainSummaryBySiteParams, OrdersSummarySpain } from './ordersBySite.model';
import { OrdersBySiteService } from './ordersBySite.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';



@Component({
  selector: 'ordersBySiteTable',
  template: `
    <div id='gridHolder'>

    <ag-grid-angular 
    class="ag-theme-balham" 
    [gridOptions]="mainTableGridOptions"
    >    
    </ag-grid-angular>
    <div  id="excelExport" (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>
  </div>
  `,
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
  styles: [
    `
      #gridHolder {
        position: relative;
        margin: 0.5em;
      }
      ag-grid-angular {
        width: 100%;
        /* margin: 1em auto .5em auto; */
        max-width: 3000px;
      }
      #excelExport {
        right: 0;
      }
`
  ]
})



export class OrdersBySiteTableComponent implements OnInit,OnDestroy {
  private destroy$ = new Subject<void>();
  @Input() public isRegionalTable: boolean;

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  public gridApi: GridApi;
  public gridColumnApi;

  mainTableGridOptions: GridOptionsCph






  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public gridHelpers: AGGridMethodsService,
    public service: OrdersBySiteService,
    public gridHelpersService: AGGridMethodsService,
    public getDataService: GetDataMethodsService,

  ) {







  }

  
  ngOnInit() {
    this.initParams();
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
}

  initParams() {


    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpers.getContextMenuItems(params),
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.provideRowData(),
      onGridReady: (params) => this.onGridReady(params),
      domLayout: 'autoHeight',
      onCellMouseOver: (params) => {
      },
      onCellClicked: (params) => {
        this.onCellClick(params);
      },
      
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
        noRenderer: { cellClass: 'ag-right-aligned-cell',  filter: 'agNumberColumnFilter', },
      },

      pinnedBottomRowData:this.provideBottomRowData(),

      columnDefs:this.provideColDefs(),
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }

    }
  }

  dealWithNewData(data:OrdersSummarySpain[]){
    this.gridApi.setRowData(this.provideRowData())
    this.gridApi.setPinnedBottomRowData(this.provideBottomRowData())
  }



  
  resizeGrid() {
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();
    }
  }



  provideBottomRowData() {
    return this.service.orders.filter(x => !!x.IsTotal)
  }


  onCellClick(params:CellClickedEvent) {
    this.loadSiteOrderModal(params.data, params.colDef)
  }

  loadSiteOrderModal(data, colDef){
    this.getSiteDetailView(data);
  }
  

  provideColDefs(){
    return [
      {  headerName: '', field: 'Label', colId: 'Label', width: 200, type: 'label', },

      //Daily
      {headerName: this.constants.translatedText.Daily, children:[
        {  headerName: this.constants.translatedText.Yesterday,type: 'number', field: 'Yesterday', colId: 'Yesterday', width: 100,  },
        {  headerName: this.constants.translatedText.Today,type: 'number', field: 'Today', colId: 'Today', width: 100,  },
        {  headerName: this.constants.translatedText.vsYesterday,type: 'number', field: 'TodayVs', colId: 'TodayVs', width: 100,  },
      ]},
      
      //Week
      {headerName:this.constants.translatedText.TheWeek, children:[
        {  headerName: this.constants.translatedText.LastWeek,type: 'number', field: 'WTDlw', colId: 'WTDlw', width: 100,  },
        {  headerName: this.constants.translatedText.ThisWeek,type: 'number', field: 'WTD', colId: 'WTD', width: 100,  },
        {  headerName: this.constants.translatedText.vsLastWeek,type: 'number', field: 'WTDVs', colId: 'WTDVs', width: 100,  },
      ]},
     
     //Month vs LY
      {headerName:this.constants.translatedText.TheMonth + " " + this.constants.translatedText.vsLastYear, children:[
        {  headerName: this.constants.translatedText.LastYear,type: 'number', field: 'ThisMonthLY', colId: 'ThisMonthLy', width: 100,  },
        {  headerName: this.constants.translatedText.ThisMonth,type: 'number', field: 'ThisMonth', colId: 'ThisMonth', width: 100,  },
        {  headerName: this.constants.translatedText.vsLastYear,type: 'noRenderer', field: 'MonthVsLastYr', colId: 'MonthVsLastYr',cellRenderer: OrdersBySiteBarComponent, width: 200,  },
      ]},
     
      //Month vs LastMonth
      {headerName:this.constants.translatedText.TheMonth + " " + this.constants.translatedText.vsLastMonth, children:[
        {  headerName: this.constants.translatedText.LastMonth,type: 'number', field: 'LastMonth', colId: 'LastMonth', width: 100,  },
        {  headerName: this.constants.translatedText.ThisMonth,type: 'number', field: 'ThisMonth', colId: 'ThisMonth', width: 100,  },
        {  headerName: this.constants.translatedText.vsLastMonth,type: 'noRenderer', field: 'MonthVsLastMonth', colId: 'MonthVsLastMonth',cellRenderer: OrdersBySiteBarComponent, width: 200,  },
      ]},

       //Month vs Target
       {headerName:this.constants.translatedText.TheMonth + " " + this.constants.translatedText.vsTarget, children:[
        {  headerName: this.constants.translatedText.Target,type: 'number', field: 'ThisMonthTgt', colId: 'ThisMonthTgt', width: 100,  },
        {  headerName: this.constants.translatedText.ThisMonth,type: 'number', field: 'ThisMonth', colId: 'ThisMonth', width: 100,  },
        {  headerName: this.constants.translatedText.vsTarget,type: 'noRenderer', field: 'MonthVsTarget', colId: 'MonthVsTarget',  cellRenderer: OrdersBySiteBarComponent, width: 200,  },
      ]},

    ]
  }







  onGridReady(params): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    //this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    this.gridApi.sizeColumnsToFit();

    this.service.newTableDataEmitter
    .pipe(takeUntil(this.destroy$))
    .subscribe(res => {
      //new data!
      this.gridApi.setRowData(this.provideRowData());
      this.gridApi.setPinnedBottomRowData(this.provideBottomRowData());
    })

  }



  // highlightTopBottom(colDef: any, topBottomN: number) {
  //   this.agGrid.highlightTopBottom(colDef, topBottomN, this.flipCols, this.gridApi)
  // }

  getSiteDetailView(data)
  {
    let parms: OrdersSpainSummaryBySiteParams = {
      OrderTypeTypes: this.service.orderTypeTypes,
      FranchiseCodes: this.service.franchises,
      SiteIds: [],
      IncludeNew: this.service.vehicleTypeTypes.includes('New'),
      IncludeUsed: this.service.vehicleTypeTypes.includes('Used'),
    }

    if(data.IsTotal)
    {
      parms.SiteIds = this.selections.userSiteIds;
    }
    else 
    {
      parms.SiteIds.push(data.SiteId);
    }
    
    
    this.getDataService.getRatioOrdersForSite(parms).subscribe((res: OrdersForSite[])=>{
      this.service.ordersForSite = res;
    })
  }


  refreshCells() {
    if (this.gridApi) {
      this.gridApi.refreshCells();
    }
  }

 


  provideRowData(){
    if(!!this.isRegionalTable){
      return this.service.orders.filter(x=>!!x.IsRegion && !x.IsTotal) 
    }else{
      return  this.service.orders.filter(x=>!x.IsRegion && !x.IsTotal)
    }
  }





  // private numberValueFormatting(params): string {
  //   return params && params.value < 0 ? 'badFont  ag-right-aligned-cell' : 'ag-right-aligned-cell'
  // }

  //getMainMenuItems() { return this.gridHelpers.getMainMenuItems() }



  clearHighlighting(colDef: any) { this.gridHelpers.clearHighlighting(colDef, this.gridApi) }
  removeHighlighting() {
    this.mainTableGridOptions.columnDefs.forEach(colDef => {
      this.clearHighlighting(colDef);
    })
  }


  excelExport(): void {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Orders By Site', 1, 1);
  }

}
