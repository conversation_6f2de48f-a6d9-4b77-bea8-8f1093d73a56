<div class="modal-header">
  <h4 *ngIf="userAndLogin.AppUserId">Update Existing User 1</h4>
  <h4 *ngIf="!userAndLogin.AppUserId">Add New User</h4>
  <button type="button" class="close" aria-label="Close" (click)="cancelModal()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body alertModalBody lowHeight" [ngClass]="constants.environment.customer">

  <div class="contentTile">


    <table class="cph fullWidth">

      <tbody>

        <!-- Name -->
        <tr>
          <td class="rowHeader">Name</td>
          <td>
            <input [(ngModel)]="userAndLogin.Name" value="{{userAndLogin.Name}}" (ngModelChange)="checkOkToSave()" />
        </tr>



        <!-- Email -->
        <tr>
          <td class="rowHeader">Email </td>
          <td>
            <div class="spaceBetween column emailSection">
              <input value="{{userAndLogin.Email}}" [(ngModel)]="userAndLogin.Email"
                (ngModelChange)="checkOkToSave()" />
            </div>
        </tr>


        <!-- JobTitle -->
        <tr>
          <td class="rowHeader">Job Title</td>
          <td>
            <input [(ngModel)]="userAndLogin.JobTitle" value="{{userAndLogin.JobTitle}}"
              (ngModelChange)="checkOkToSave()" />
        </tr>


        <!-- Role -->
        <tr>
          <td class="rowHeader">Role</td>
          <td class="alignRight">


            <div ngbDropdown container="body" id="chooseNewRole" class="d-inline-block" [autoClose]="true">
              <button class="btn btn-primary" id="" ngbDropdownToggle>
                <span *ngIf="userAndLogin.role">{{userAndLogin.role.Translation}}</span>
                <span *ngIf="!userAndLogin.role">Choose Role</span>
              </button>
              <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                <button *ngFor="let role of constants.Roles" ngbDropdownItem
                  (click)="chooseRole(role)">{{role.Translation}}</button>
              </div>
            </div>


          </td>

        </tr>

        <!-- Retailer Site -->
        <tr>
          <td class="rowHeader">Retailer Site</td>
          <td class="alignRight">
            <!-- Site dropdown -->
            <div ngbDropdown container="body" id="chooseNewSite" class="d-inline-block" [autoClose]="true">
              <button class="btn btn-primary" id="" ngbDropdownToggle>
                <span *ngIf="userAndLogin.chosenRetailerSite">{{userAndLogin.chosenRetailerSite.Name }}</span>
                <span *ngIf="!userAndLogin.chosenRetailerSite">Choose Retailer Site</span>
              </button>
              <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                <button *ngFor="let site of constants.RetailerSites" ngbDropdownItem (click)="chooseRetailerSite(site)">
                  {{ site.Name }}
                </button>
              </div>
            </div>
          </td>
        </tr>

        <!-- Site -->
        <tr>
          <td class="rowHeader">Home Site</td>
          <td class="alignRight">

            <!-- Site dropdown -->
            <div ngbDropdown container="body" id="chooseNewSite" class="d-inline-block" [autoClose]="true">

              <button class="btn btn-primary" id="" ngbDropdownToggle>
                <span *ngIf="userAndLogin?.site?.SiteDescription">{{userAndLogin.site.SiteDescription}}</span>
                <span *ngIf="!userAndLogin?.site?.SiteDescription">Choose Site</span>
              </button>

              <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                <button (click)="chooseSite(site)" *ngFor="let site of constants.Sites"
                  ngbDropdownItem>{{site.SiteDescription}}</button>
              </div>

            </div>

          </td>
        </tr>


        <!-- Sites -->
        <tr>
          <td class="rowHeader">Sites</td>
          <td class="alignRight">

            <!-- Site selector -->
            <userSitePicker [width]="250" [sitesFromParent]="userAndLogin.chosenSites" [buttonClass]="''"
              (updateSites)="onUpdateSites($event)"></userSitePicker>
          </td>
        </tr>


        <!-- Access all sites -->
        <tr>
          <td class="rowHeader">Access All Sites</td>
          <td>
            <button class="custom-checkbox" [ngClass]="{ 'checked': userAndLogin.AccessAllSites }"
              (click)="userAndLogin.AccessAllSites = !userAndLogin.AccessAllSites; checkOkToSave()">
              <span *ngIf="userAndLogin.AccessAllSites">
                <i class="fa fa-check"></i>
              </span>
            </button>
        </tr>

        <!-- Sales Exec -->
        <tr *ngIf="!constants.environment.userSetup.hideIsSalesExec">
          <td class="rowHeader">Sales Exec</td>
          <td>
            <button class="custom-checkbox" [ngClass]="{ 'checked': userAndLogin.IsSalesExec }"
              (click)="userAndLogin.IsSalesExec = !userAndLogin.IsSalesExec; checkOkToSave()">
              <span *ngIf="userAndLogin.IsSalesExec">
                <i class="fa fa-check"></i>
              </span>
            </button>
        </tr>


        <!-- SalesRoles -->
        <tr *ngIf="!constants.environment.userSetup.hideSalesRoles">

          <td class="rowHeader">Sales Roles</td>

          <td class="alignRight">
            <div id="salesRolesPickers">

              <div *ngFor="let monthNumber of [1,2,3,4,5,6,7,8,9,10,11,12];" class="salesRolePicker">

                <span class="monthName">{{getMonthName(monthNumber)}}</span>

                <div ngbDropdown container="body" id="chooseNewSalesRole" class="d-inline-block" [autoClose]="true">
                  <button class="btn btn-primary" ngbDropdownToggle>
                    <span *ngIf="salesRoles">{{getRoleForMonth(monthNumber)}}</span>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <button *ngFor="let salesRole of allSalesRoles" ngbDropdownItem
                      (click)="chooseSalesRole(salesRole, monthNumber)">{{salesRole}}</button>
                  </div>
                </div>


                <!-- Site dropdown -->
                <div ngbDropdown container="body" id="chooseNewSite" class="d-inline-block" [autoClose]="true">

                  <button class="btn btn-primary" ngbDropdownToggle>
                    <span *ngIf="salesRoles">{{getSiteForMonth(monthNumber)}}</span>
                  </button>

                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <button (click)="chooseSiteSalesrole(site, monthNumber)" *ngFor="let site of constants.Sites"
                      ngbDropdownItem>{{site.SiteDescription}}</button>
                  </div>

                </div>

              </div>
            </div>

          </td>
        </tr>

        <!-- Transaction manager -->
        <tr *ngIf="!constants.environment.userSetup.hideTMgr">
          <td class="rowHeader">Is TMgr</td>
          <td>
            <button class="custom-checkbox" [ngClass]="{ 'checked': isManager }"
              (click)="isManager = !isManager; checkOkToSave()">
              <span *ngIf="isManager">
                <i class="fa fa-check"></i>
              </span>
            </button>
          </td>
        </tr>

        <!-- all new page level claims -->
        <tr *ngFor="let claim of claims">
          <!-- - {{claim.ClaimValue}}   -->
          <td class="rowHeader">Claim: {{claim.ClaimType}} </td>
          <td>



            <!-- if dropdown choice -->
            <div ngbDropdown  *ngIf="!claim.isBoolean" container="body" id="chooseNewRole" class="d-inline-block" [autoClose]="true">
              <!-- The dropdown button -->
              <button class="btn btn-primary" id="" ngbDropdownToggle>
                {{claim.ClaimValue}}
              </button>

              <!-- The choices -->
              <div  ngbDropdownMenu aria-labelledby="dropdownBasic1">
                <button *ngFor="let choice of claim.ClaimChoices" ngbDropdownItem
                  (click)="setClaim(claim.ClaimType, choice)">{{choice}}
                </button>
              </div>
            </div>



            <!-- if boolean choice -->
            <button *ngIf="claim.isBoolean" class="custom-checkbox"
              [ngClass]="{ 'checked': claim.ClaimValue == 'true' }" (click)="toggleBooleanClaim(claim)">
              <span *ngIf="claim.ClaimValue && claim.ClaimValue === 'true'">
                <i class="fa fa-check"></i>
              </span>
            </button>


           
          </td>
        </tr>

      </tbody>
    </table>

  </div>
</div>

<div class="modal-footer">


  <!-- <button type="button" class="btn btn-primary" (click)="modal.close()">OK</button> -->

  <!-- Delete button -->
  <div [ngbPopover]="!checkOkToDelete() ? errorMessage : null" placement="top" container="body" 
  triggers="mouseenter:mouseleave"
  >
  <button [disabled]="!checkOkToDelete()" class="btn btn-danger" (click)="maybeDeleteUser()">Delete user</button>
  </div>

  <!-- Save button -->
  <div [ngbPopover]="!checkOkToSave() ? errorMessage : null" placement="top" container="body" 
  triggers="mouseenter:mouseleave"
  >

  <button 
    [disabled]="!checkOkToSave()" 
    class="btn btn-success" 
    (click)="saveUser()" 
    triggers="hover">
    {{saveButtonMessage()}}
  </button>

  </div>
  
  <button type="button" class="btn btn-primary"
    (click)="cancelModal()">{{constants.translatedText.Common_Cancel}}</button>
</div>
