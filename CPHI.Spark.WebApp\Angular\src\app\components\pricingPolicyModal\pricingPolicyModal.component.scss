.factorWorkings {
    //display: flex;
    // width: 100em;
    align-items: flex-end;

    table {
        width: 100%;
        table-layout: auto;
        margin-bottom: 1em;

        th {
            text-align: center;
        }

        input {
            padding: 0em 0.5em;
            height: 2em;
            border: 1px solid var(--grey80);
            text-align: center;
            width: 5em;
        }

        th.buttonHolding<PERSON>ell {
            button {
                width: 2.4em;
                margin-left: 0.1em;
                padding: 7px 4px;
            }
        }

        td.buttonHoldingCell {
            text-align: right;
            text-wrap: nowrap;

            button {
                width: 3em;
                margin-left: 0.4em;
            }
        }

        td {
            text-align: center;
            ;

            input.valueInput {
                width: 4em;
            }

            input.valueInput.extraWide{
                width: 18em;
            }

            div.showInformationInline {
                display: inline-flex;
            }


        }
        td.competitorType {
            text-align: left;
        }
        td.competitorTypeNotes {
            text-align: right;
        }

        td.textAreaCell {
            padding: 0px;
            vertical-align: bottom;
        }

        textarea {
            text-align: center;
            resize: vertical;
            height: 2.2em;
            border: 1px solid var(--grey80);
            width: 20em;
        }
    }

    border: 1px solid var(--grey90);
    padding: 1em;

}


.removeFactorHolder {
    width: 100%;
    display: flex;
    justify-content: flex-end;
}

.addFactor {
    button.btn.btn-success {
        width: 10em;
    }
}


.layerHeader {
    width: 100%;
    color:var(--grey40);
    padding:1em;
    
    .layerIcon {
        color:var(--grey40);
        width: 10em;
        
    }

    //.layerIcon{width:60em;}
    .layerButton {
        width: 10em;
    }
}





textarea {
    overflow: hidden;
    /* Removes scrollbar */
    resize: none;
    /* Removes the resize handle in the bottom right corner */
    transition: height 0.2s;
    /* Optional: smooth transition */
}


#modalBlobArea {
    border: 1px solid var(--grey80);
    height: 60em;
    width: 100%;
    background: white;
    padding: 2em 1em 0em 1em;
    position: relative;
}

.autotraderCard {

    margin-bottom: 1em;
}

.factorName {
    //
}


#topTable {
    width: 100%;

    td:nth-of-type(1) {
        width: 10em;
    }

    td {
        vertical-align: middle;

        input {
            width: 50em;
        }
    }

}

.newFactorTable {
    .newFactorRow {
        margin-bottom: 0.3em;
        background-color: var(--grey90);
        padding: 1em;

        .icon {
            width: 10%;
        }

        .name {
            width: 20%
        }

        .explanation {
            width: 60%;
        }

        .chooseFactor {
            width: 10%;
            text-align: right;
        }
    }
}

.newFactorNotes {
    margin-bottom: 0em !important;
    width: 50em;
}

tr.exampleRow {
    td {
        font-style: italic;
        padding-bottom: 3em;
    }
}


tr.exampleRowSpecificColour {
    td {
        font-style: italic;
        padding-bottom: 1.5em;
    }
}

.smallGap{
    height: 5px;
}

.redHighlight {
    background-color: rgba(255, 0, 0, 0.3);
}

.age-category {
    margin-right: 5em; 
}