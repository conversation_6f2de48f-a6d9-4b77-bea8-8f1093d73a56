{
  "ConnectionStrings": { //DefaultConnection value is required.
    "DefaultConnection": ""
  },
  "serviceName": "CPHI.Spark.FTPSaveService",

   "FtpScraperSettings": {
      "mailUser": "<EMAIL>",
      "mailPwd": "X4KkGrS46MKHqEXR",

      "hostname": "access832882062.webspace-data.io",
      "username": "u101337964",
      "password": "oN3yXNX[mRk?Lpk",

      "hostnameCphi": "sftpuk.cphinsight.com",
      "usernameCphi": "ukrootsftp",
      "passwordCphi": "TbBXQhhETf7c!a3j",

      "fileStream": "c:\\temp\\",
      "fileDestination": "c:\\cphiRoot\\{destinationFolder}\\inbound\\",
      "jardineFileDestination": "c:\\cphiRoot\\jardine\\inbound\\",
      "mmgFileDestination": "c:\\cphiRoot\\mmg\\inbound\\",
      "lithiaFileDestination": "c:\\cphiRoot\\lithia\\inbound\\",
      "driveFileDestination": "c:\\cphiRoot\\drive\\inbound\\",
      "overrideRunJobNow": null // remove old implementation of forerun and set overrideRunJobNow like in Loader project
   },


   "Quartz": {
      "Spark_devVM_Fetch": "0 0/3 * * * ?", //Every 3 minutes
      "SparkRRGCphiSiteFetch": "0 0/3 * * * ?", //Every 3 minutes
      "SparkRRGEmacSiteFetch": "0 1/3 * * * ?", //At second :00, every 15 minutes starting at minute :05, of every hour
      "SparkRRGSpainCphiSiteFetch": "0 2/3 * * * ?", //At second :00, every 1 minutes starting at minute :00, of every hour   https://www.freeformatter.com/cron-expression-generator-quartz.html
      "SparkVindisCphiSiteFetch": "30 0/3 * * * ?", //At second :00, every 1 minutes starting at minute :00, of every hour   https://www.freeformatter.com/cron-expression-generator-quartz.html
      "StockpulseJardineFetch": "0 0/5 * * * ?", //Every 5 minutes   
      "StockpulseLithiaFetch": "0 0/5 * * * ?", //Every 5 minutes   
      "StockpulseMmgFetch": "0 0/5 * * * ?", //Every 5 minutes   
      "StockpulseDriveFetch": "0 0/2 * * * ?", //Every 2 minutes   
      "CleanupJob": "0 0 20 ? * * *" //daily 8pm
   },
  "Monitor": {
    "AddLogMessageURL": "https://cphimonitorapi.azurewebsites.net/api/monitor/",
    "AppKey": "4"
  }
}
