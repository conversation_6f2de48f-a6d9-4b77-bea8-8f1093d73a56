import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { report } from 'process';
import { CphPipe } from '../../cph.pipe';
import { OemOrdersSiteRow, RegistrationsSiteRow } from '../../model/sales.model';
import { AutotraderService } from '../../services/autotrader.service';
import { ConstantsService } from '../../services/constants.service';
import { GetDataMethodsService } from '../../services/getDataMethods.service';
import { GetDealDataService } from '../../services/getDeals.service';
import { SelectionsService } from '../../services/selections.service';
import { RegistrationsService } from './registrations.service';
import { RegistrationsModalComponent } from './registrationsModal.component';


export interface RegistrationsReportType {
  label: string,
  value: string,
  type: RegReportType,
  isMonthly:boolean;
}

export enum RegReportType {
  oemOrders = "oemOrders", 
  registrations = "registrations"
}


@Component({
  selector: 'app-registrationsPosition',
  templateUrl: './registrationsPosition.component.html',
  styleUrls: ['./registrationsPosition.component.scss']
})


export class RegistrationsPositionComponent implements OnInit {

  //main declarations
  @ViewChild('registrationsTable', { static: true }) registrationsTable: ElementRef;
  @ViewChild('registrationsModal', { static: true }) registrationsModal: RegistrationsModalComponent;

  months: Date[];
  quarters: Date[];
  reportTypes: RegistrationsReportType[] = [
    { label: 'LMT Orders', value: 'LMTOrders', type: RegReportType.oemOrders, isMonthly:false },
    { label: 'Dacia Orders', value: 'DaciaOrders', type: RegReportType.oemOrders , isMonthly:false },
    { label: 'BEV Orders', value: 'BEVOrders', type: RegReportType.oemOrders , isMonthly:false },
    { label: 'LMT Regs', value: 'LMTRegs', type: RegReportType.registrations, isMonthly:true  },
    // { label: 'LMT Zoe Regs', value: 'LMTZoeRegs', type: RegReportType.registrations , isMonthly:false },
    { label: 'Dacia Regs', value: 'DaciaRegs', type: RegReportType.registrations , isMonthly:true },
  ]

  allRegReportTypes = RegReportType;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public analysis: AutotraderService,
    public getDealData: GetDealDataService,
    public getDataMethods: GetDataMethodsService,
    public service: RegistrationsService

  ) {

  }









  ngOnInit() {
    //launch control
    this.initParams();
    this.getData();
  }


  orderReports() { return this.reportTypes.filter(x => x.type === RegReportType.oemOrders) }
  regReports() { return this.reportTypes.filter(x => x.type === RegReportType.registrations) }

  initParams() {

    this.service.modalInstance = this.registrationsModal;
    this.quarters = this.constants.makeQuarters();
    this.months = this.constants.makeMonthsNewV3();

    this.service.chosenMonth = this.constants.deductTimezoneOffset(new Date(this.constants.thisMonthStart));
    this.pickThisQuarter();
    
    //this.service.amSelectingMonth = false;
    this.service.chosenReportType = this.reportTypes[0];
  }

  pickThisQuarter() {
    let thisQuarterInt = Math.floor((new Date().getMonth() + 3) / 3)
    this.service.chosenQuarterStartDate = this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), (thisQuarterInt - 1)*3, 1))
  }


  ngOnDestroy() {
    this.service.modalInstance = null;
  }

  getData() {

    this.selections.triggerSpinner.emit({ show: true, message: 'Loading data' })
    if (this.service.chosenReportType.type == RegReportType.oemOrders) {
      this.getOrdersData()
    } else {
      this.getRegsData()
    }


  }

  getOrdersData() {
    let startPoint = this.service.chosenReportType.isMonthly ? this.service.chosenMonth : this.service.chosenQuarterStartDate;
    this.getDataMethods.getOemOrdersSiteRows(startPoint, this.service.chosenReportType.value, this.service.chosenReportType.isMonthly).subscribe((res: OemOrdersSiteRow[]) => {
      this.service.siteOemOrdersRows = res;
      if (!!this.service.sitesTableRef) {
        this.service.sitesTableRef.dealWithNewData(res)
      }
      if (!!this.service.regionsTableRef) {
        this.service.regionsTableRef.dealWithNewData(res)
      }
      this.selections.triggerSpinner.emit({ show: false })
    })
  }


  getRegsData() {
    let startPoint = this.service.chosenReportType.isMonthly ? this.service.chosenMonth : this.service.chosenQuarterStartDate;
    this.getDataMethods.getRegistrationsSiteRows(startPoint, this.service.chosenReportType.value, this.service.chosenReportType.isMonthly).subscribe((res: RegistrationsSiteRow[]) => {
      this.service.siteRegsRows = res;
      if (!!this.service.sitesTableRef) {
        this.service.sitesTableRef.dealWithNewData(res)
      }
      if (!!this.service.regionsTableRef) {
        this.service.regionsTableRef.dealWithNewData(res)
      }
      this.selections.triggerSpinner.emit({ show: false })
    })
  }



  chooseReportType(reportType: RegistrationsReportType) {
    if (reportType.isMonthly && !this.service.chosenReportType.isMonthly) {
      //need to switch to quarterly
      //this.service.amSelectingMonth = false;
      this.pickThisQuarter();
    } else if (!reportType.isMonthly  && this.service.chosenReportType.isMonthly) {
      //need to switch to monthly
      //this.service.amSelectingMonth = true;
      this.service.chosenMonth = this.constants.deductTimezoneOffset(new Date(this.constants.thisMonthStart));
    }

    this.service.chosenReportType = reportType;
    this.getData();

  }



  recalculateAllSiteDetails() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })
  }









  selectMonth(month: Date) {
    //this.service.amSelectingMonth = true;
    this.service.chosenMonth = month;
    this.getData();
  }

  changeMonth(changeAmount: number) {
    //this.service.amSelectingMonth = true;
    this.service.chosenMonth = this.constants.addMonths(this.service.chosenMonth, changeAmount);
    this.getData();
  }

  changeQuarter(amountBy: number) {

    let currentQuarterIndex = this.quarters.findIndex(x => x.getMonth() == this.service.chosenQuarterStartDate.getMonth() && x.getFullYear() == this.service.chosenQuarterStartDate.getFullYear() );
    if (currentQuarterIndex == 0 && amountBy == -1 || currentQuarterIndex == this.quarters.length-1 && amountBy == 1) { return; } //prevent going off beginning or end of predefined quarters

    this.service.chosenQuarterStartDate = this.quarters[currentQuarterIndex + amountBy];
    this.getData();
  }

  selectQuarter(quarter: Date) {
    this.service.chosenQuarterStartDate = quarter;
    this.getData();
  }

  selectReportType(reportType: RegistrationsReportType) {
    this.service.chosenReportType = reportType;
    this.getData();
  }


  backToSites() {
    // this.chosenSite = null;
  }








}
