﻿using CPHI.Spark.Model.AutoPrice;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{

   public class AdvertParamsForStrategyCalculator
   {
      //used when constructing params for a valuation
      public AdvertParamsForStrategyCalculator(ATNewVehicleGet atDetailIn, int retailRatingIn, int odometerReadingIn, int valuationAdjustedIn, bool isOnBrand, decimal daysToSellIn, RetailerSite retailerSite, TokenResponse token)
      {
         //we only use this if we then have a strategy of getting a competitor link in order to match cheapest competitor.   should change that strategy step so that instead we build up the link from the ad details
         //as we would not have a search identifier for a new vehicle being valued
         websiteSearchIdentifier = null;

         //we will not have these as we don't have an advert, we're doing a new valuation
         snapshotId = 0;
         dateOnForecourt = DateTime.Now;
         DaysListed = 0;
         DaysInStock = 0;
         DaysBookedIn = 0;
         siv = 0;
         prepCost = 0;
         isVatQ = true;
         originalPurchasePrice = 0;
         AdvertId = null;

         //done
         firstRegisteredDate = atDetailIn.vehicle.firstRegistrationDate;
         derivativeId = atDetailIn.vehicle.derivativeId;
         portalOptions = string.Join(',', atDetailIn.features.ToList().Where(x => x.factoryFitted != null && (bool)x.factoryFitted).Select(x => x.name).ToList());
         hasOptionsSpecified = atDetailIn.features.ToList().Where(x => x.factoryFitted != null).Count() > 0;  //email from technicalmanagement 22Feb24
         ownershipCondition = atDetailIn.vehicle.ownershipCondition;
         RetailRating = retailRatingIn;
         DaysToSell = daysToSellIn;
         OdometerReading = odometerReadingIn;
         valuationAdjusted = valuationAdjustedIn;
         valuationAverage = valuationAdjustedIn;
         onBrand = isOnBrand;

         currentStrategyPrice = valuationAdjustedIn;
         VehicleReg = atDetailIn.vehicle.registration;

         Make = atDetailIn.vehicle.make;
         Model = atDetailIn.vehicle.model;
         CompetitorLink = atDetailIn.links?.competitors?.href;
         Trim = atDetailIn.vehicle.trim;
         TransmissionType = atDetailIn.vehicle.transmissionType;
         FuelType = atDetailIn.vehicle.fuelType;
         BodyType = atDetailIn.vehicle.bodyType;
         VehicleType = atDetailIn.vehicle.vehicleType;
         VehicleTypeDMS = atDetailIn.vehicle.vehicleType;
         Colour = atDetailIn.vehicle.oem?.colour;
         SpecificColour = atDetailIn.vehicle.oem?.colour ?? atDetailIn.vehicle.colour;
         Drivetrain = atDetailIn.vehicle.drivetrain;
         Doors = atDetailIn.vehicle.doors.ToString();
         BadgeEngineSizeLitres = atDetailIn.vehicle.badgeEngineSizeLitres.ToString();

         if (atDetailIn.vehicle.enginePowerBHP != null)
         {
            MaxEnginePowerBHP = atDetailIn.vehicle.enginePowerBHP.ToString();
         }
         RetailerName = retailerSite.Name;
         RetailerSiteRetailerId = retailerSite.RetailerId;
         Postcode = retailerSite.Postcode;
         PlateRange = retailerSite.CompetitorPlateRange;
         Token = token;
         StockPrefix = null;

         AgeAndOwners = BandingsService.ProvideAgeAndOwners(atDetailIn.vehicle.firstRegistrationDate, atDetailIn.vehicle.owners != null ? atDetailIn.vehicle.owners.ToString() : "Unknown");
         decimal ageInYears = (DateTime.Now.Date - (DateTime)atDetailIn.vehicle.firstRegistrationDate).Days / 365M;
         AgeBand = BandingsService.ProvideAgeBand(ageInYears);

      }

      //this constructor is used when we are looking at local bargains
      public AdvertParamsForStrategyCalculator(AutoTraderVehicleListing vehicleListing, bool isOnBrand, RetailerSite retailerSite, TokenResponse token)
      {
         websiteSearchIdentifier = vehicleListing.metadata.searchId;

         //we will not have these as we don't have an advert, we're doing a new valuation
         snapshotId = 0;
         dateOnForecourt = DateTime.Now;

         // 11/11/24: Set these to zero as they're not in stock, we're evaluating competitor vehicles that we might buy
         DaysListed = 0;
         DaysInStock = 0;
         DaysBookedIn = 0;

         siv = 0;
         prepCost = 0;
         isVatQ = true;
         originalPurchasePrice = 0;
         AdvertId = null;
         //StrategyPrice = 0;


         firstRegisteredDate = vehicleListing.vehicle.firstRegistrationDate;
         derivativeId = vehicleListing.vehicle.derivativeId;
         portalOptions = string.Join(',', vehicleListing.features.ToList().Select(x => x.name).ToList());
         hasOptionsSpecified = true;
         ownershipCondition = vehicleListing.vehicle.ownershipCondition;
         RetailRating = vehicleListing.vehicleMetrics.local?.retail?.rating?.value ?? vehicleListing.vehicleMetrics.national?.retail?.rating?.value ?? 0;
         DaysToSell = vehicleListing.vehicleMetrics.local.retail.daysToSell?.value ?? 0;
         OdometerReading = vehicleListing.vehicle.odometerReadingMiles;
         valuationAdjusted = vehicleListing.valuations?.adjusted?.retail?.amountGBP ?? 0;
         valuationAverage = vehicleListing.valuations?.marketAverage?.retail?.amountGBP ?? 0;
         onBrand = isOnBrand;

         currentStrategyPrice = vehicleListing.valuations?.adjusted?.retail?.amountGBP ?? vehicleListing.valuations?.adjusted?.retail.amountExcludingVatGBP ?? 0;
         VehicleReg = vehicleListing.vehicle.registration;

         Make = vehicleListing.vehicle.make;
         CompetitorLink = vehicleListing.links?.competitor?.href;
         Model = vehicleListing.vehicle.model;
         Trim = vehicleListing.vehicle.trim;
         TransmissionType = vehicleListing.vehicle.transmissionType;
         if (vehicleListing.vehicle.enginePowerBHP != null)
         {
            MaxEnginePowerBHP = vehicleListing.vehicle.enginePowerBHP.ToString();
         }
         FuelType = vehicleListing.vehicle.fuelType;
         BodyType = vehicleListing.vehicle.bodyType;
         AgeAndOwners = BandingsService.ProvideAgeAndOwners(vehicleListing.vehicle.firstRegistrationDate, (vehicleListing.vehicle.owners ?? 1).ToString());
         decimal ageInYears = (DateTime.Now.Date - (DateTime)vehicleListing.vehicle.firstRegistrationDate).Days / 365M;
         AgeBand = BandingsService.ProvideAgeBand(ageInYears);
         VehicleType = vehicleListing.vehicle.vehicleType;
         VehicleTypeDMS = vehicleListing.vehicle.vehicleType;
         Colour = vehicleListing.vehicle.standard?.colour;
         SpecificColour = vehicleListing.vehicle.oem?.colour ?? vehicleListing.vehicle.colour;
         Drivetrain = vehicleListing.vehicle.drivetrain;
         Doors = vehicleListing.vehicle.doors.ToString();
         BadgeEngineSizeLitres = vehicleListing.vehicle.badgeEngineSizeLitres.ToString();
         RetailerSiteRetailerId = retailerSite.RetailerId;

         RetailerName = retailerSite.Name;
         Postcode = retailerSite.Postcode;
         PlateRange = retailerSite.CompetitorPlateRange;
         Token = token;
         StockPrefix = null;
      }




      //used when constructing params for a proper advert
      public AdvertParamsForStrategyCalculator(VehicleAdvertWithRating advert, RetailerSite retailerSite, TokenResponse token, string competitorLink)
      {
         RetailRating = advert.RetailRating;
         DaysToSell = advert.DaysToSellAtCurrentSelling ?? 0;
         currentStrategyPrice = (int)(advert.ValuationAdjRetail ?? advert.ValuationMktAvRetail ?? advert.ValuationAdjRetailExVat ?? advert.ValuationMktAvRetailExVat);
         snapshotId = advert.SnapshotId;
         OdometerReading = advert.OdometerReading;
         websiteSearchIdentifier = advert.WebSiteSearchIdentifier;
         firstRegisteredDate = advert.FirstRegisteredDate;
         dateOnForecourt = advert.DateOnForecourt;
         valuationAdjusted = advert.ValuationAdjRetail;
         valuationAverage = advert.ValuationMktAvRetail;
         derivativeId = advert.DerivativeId;
         portalOptions = advert.PortalOptions;
         hasOptionsSpecified = advert.VehicleHasOptionsSpecified;
         DaysListed = advert.DaysListed;
         DaysInStock = advert.DaysInStock;
         DaysBookedIn = advert.DaysBookedIn;
         onBrand = !string.IsNullOrEmpty(retailerSite?.Makes) && retailerSite.Makes.Split(",").Contains(advert.Make);
         siv = advert.SIV;
         prepCost = advert.PrepCost;
         ownershipCondition = advert.OwnershipCondition;
         isVatQ = advert.IsVatQ;
         originalPurchasePrice = advert.OriginalPurchasePrice;
         AdvertId = advert.AdId;
         VehicleReg = advert.VehicleReg;
         StockPrefix = advert.StockPrefix;

         Make = advert.Make;
         Model = advert.Model;
         CompetitorLink = competitorLink;
         Trim = advert.Trim;
         TransmissionType = advert.TransmissionType;
         FuelType = advert.FuelType;
         BodyType = advert.BodyType;
         VehicleType = advert.VehicleType;
         VehicleTypeDMS = advert.VehicleTypeDesc;
         Colour = advert.Colour;
         SpecificColour = advert.SpecificColour;
         Drivetrain = advert.Drivetrain;
         Doors = advert.Doors.ToString();
         AgeBand = advert.AgeBand;
         AgeAndOwners = advert.AgeAndOwners;

         BadgeEngineSizeLitres = advert.BadgeEngineSizeLitres;
         MaxEnginePowerBHP = advert.EnginePowerBHP;
         RetailerSiteRetailerId = retailerSite.RetailerId;

         RetailerName = retailerSite.Name;
         Postcode = retailerSite.Postcode;
         PlateRange = retailerSite.CompetitorPlateRange;
         Token = token;
         PerfRatingScore = advert.PerfRatingScore;

      }


      //when we are doing strategy for other site locations for same dealerGroup
      public AdvertParamsForStrategyCalculator(VehicleAdvertWithRating advert, decimal retailRating, RetailerSite retailerSite, TokenResponse token, string competitorLink)
      {
         RetailRating = retailRating;
         DaysToSell = advert.DaysToSellAtCurrentSelling ?? 0;
         currentStrategyPrice = (int)advert.ValuationAdjRetail;
         snapshotId = advert.SnapshotId;
         OdometerReading = advert.OdometerReading;
         websiteSearchIdentifier = advert.WebSiteSearchIdentifier;
         firstRegisteredDate = advert.FirstRegisteredDate;
         dateOnForecourt = advert.DateOnForecourt;
         valuationAdjusted = advert.ValuationAdjRetail;
         valuationAverage = advert.ValuationMktAvRetail;
         derivativeId = advert.DerivativeId;
         portalOptions = advert.PortalOptions;
         hasOptionsSpecified = advert.VehicleHasOptionsSpecified;
         DaysListed = advert.DaysListed;
         DaysInStock = advert.DaysInStock;
         DaysBookedIn = advert.DaysBookedIn;
         onBrand = advert.OnBrand;
         siv = advert.SIV;
         prepCost = advert.PrepCost;
         ownershipCondition = advert.OwnershipCondition;
         isVatQ = advert.IsVatQ;
         originalPurchasePrice = advert.OriginalPurchasePrice;
         AdvertId = advert.AdId;
         VehicleReg = advert.VehicleReg;
         StockPrefix = advert.StockPrefix;


         Make = advert.Make;
         Model = advert.Model;
         CompetitorLink = competitorLink;
         Trim = advert.Trim;
         TransmissionType = advert.TransmissionType;
         FuelType = advert.FuelType;
         BodyType = advert.BodyType;
         VehicleType = advert.VehicleType;
         VehicleTypeDMS = advert.VehicleTypeDesc;
         Colour = advert.Colour;
         SpecificColour = advert.SpecificColour;
         AgeBand = advert.AgeBand;
         AgeAndOwners = advert.AgeAndOwners;
         Drivetrain = advert.Drivetrain;
         Doors = advert.Doors.ToString();
         BadgeEngineSizeLitres = advert.BadgeEngineSizeLitres;
         MaxEnginePowerBHP = advert.EnginePowerBHP;
         RetailerSiteRetailerId = retailerSite.RetailerId;

         RetailerName = retailerSite.Name;
         Postcode = retailerSite.Postcode;
         PlateRange = retailerSite.CompetitorPlateRange;
         Token = token;
         PerfRatingScore = advert.PerfRatingScore;

      }

      //Metrics
      public decimal? RetailRating { get; set; }
      public int DaysListed { get; set; }
      public int DaysInStock { get; set; }
      public int? DaysBookedIn { get; set; }
      public decimal DaysToSell { get; set; }

      public decimal currentStrategyPrice { get; set; } //the latest strategy price.   This will be getting constantly updated as we work through the layers


      public int snapshotId { get; set; }
      public int? OdometerReading { get; set; }
      public string websiteSearchIdentifier { get; set; }
      public DateTime? firstRegisteredDate { get; set; }
      public DateTime? dateOnForecourt { get; set; }
      public int? valuationAdjusted { get; set; }
      public int? valuationAverage { get; set; }
      public string derivativeId { get; set; }
      public string portalOptions { get; set; }
      public bool hasOptionsSpecified { get; set; }
      public bool onBrand { get; set; }
      public int? siv { get; set; }
      public decimal? prepCost { get; set; }
      public string ownershipCondition { get; set; }
      public bool isVatQ { get; set; }
      public decimal originalPurchasePrice { get; set; }

      public int? AdvertId { get; set; }
      //things about the vehicle, for competitor search
      public string Make { get; set; } //we need this for Pentagon for criteria comparison
                                       //public bool StrategyPriceHasBeenCalculated { get; set; } = false;
      public string VehicleReg { get; set; }
      public string Model { get; set; }
      public string Trim { get; set; }
      public string CompetitorLink { get; set; }
      public string TransmissionType { get; set; }
      public string FuelType { get; set; }
      public string BodyType { get; set; }
      public string Drivetrain { get; set; }
      public string Doors { get; set; }
      public string Colour { get; set; }
      public string SpecificColour { get; set; }
      public string VehicleType { get; set; }
      public string VehicleTypeDMS { get; set; }
      public string BadgeEngineSizeLitres { get; set; }
      public string RetailerName { get; set; }
      public string MaxEnginePowerBHP { get; set; }
      //things about the site, for competitor search
      public int RetailerSiteRetailerId { get; set; }

      public string Postcode { get; set; }
      public int PlateRange { get; set; }
      public TokenResponse Token { get; set; }
      public string StockPrefix { get; set; }
      public string AgeBand { get; set; }
      public string AgeAndOwners { get; set; }
      public decimal? PerfRatingScore { get; set; }
   }



}