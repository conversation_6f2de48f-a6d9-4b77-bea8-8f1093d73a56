<div class="dashboard-tile-inner">
  <div class="tileHeader clickable" (click)="navigateToMainPage()">
    <div class="headerWords">
      <h4>{{constants.translatedText.Dashboard_PartsStockValue}} 6-12 {{constants.translatedText.Month}}
      </h4>
    </div>
  </div>


  <div class="spaceAround">
    <div class="spaceBetween column">
      <h1>
        {{data.StockValue6To12 | cph:'currency':0}}
      </h1>
      <div class="label">
        {{ constants.translatedText.Dashboard_PartsStock_TotalValue }}
      </div>
    </div>

    <div class="spaceBetween column" title="Total Stock is {{data.TotalStockValue|cph:'currency':0}}">
      <div class="percentage" [ngClass]="{'goodFont' : data.Percentage <= 0.08 , 'badFont' : data.Percentage > 0.08}">
        <h1><strong>{{data.Percentage|cph:'percent':0}}</strong></h1>
      </div>
      <div class="label">
        {{ constants.translatedText.Dashboard_PartsStock_OfTotalStock }}
      </div>
    </div>


  </div>
</div>