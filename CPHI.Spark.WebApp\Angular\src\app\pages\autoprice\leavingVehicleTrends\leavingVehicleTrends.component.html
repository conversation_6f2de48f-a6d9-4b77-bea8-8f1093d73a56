<nav class="navbar">
  <nav class="generic">
    <h4 id="pageTitle">
      Leaving Vehicle Trends&nbsp;
      <sourceDataUpdate *ngIf="!constants.environment.showLatestSnapshotDate"
        [chosenDate]="constants.LastUpdatedDates.AutotraderAdverts"></sourceDataUpdate>
    </h4>

    <div class="d-flex align-items-center">
      From:
      <div class="buttonGroup">
        <div ngbDropdown class="d-inline-block" [autoClose]="true">
          <button (click)="makeMonthsDropdownOnClick()" class="btn btn-primary centreButton" ngbDropdownToggle>{{
            getMonthName(service.startDate) }}
          </button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let month of months" (click)="selectMonthOnClick(month, true)" ngbDropdownItem>{{
              getMonthName(month) }}
            </button>
          </div>
        </div>
      </div>
      To:
      <div class="buttonGroup">
        <div ngbDropdown class="d-inline-block" [autoClose]="true">
          <button (click)="makeMonthsDropdownOnClick()" class="btn btn-primary centreButton" ngbDropdownToggle>{{
            getMonthName(service.endDate) }}
          </button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let month of months" (click)="selectMonthOnClick(month)" ngbDropdownItem>{{
              getMonthName(month) }}
            </button>
          </div>
        </div>
      </div>
    </div>


    <div class="buttonGroup">
      <button class="btn btn-primary" [ngClass]="{'active':service.displayChoice==='smallCharts'}"
        (click)="service.displayChoice='smallCharts'">
        Small Charts
      </button>
      <button class="btn btn-primary" [ngClass]="{'active':service.displayChoice==='bigChart'}"
        (click)="service.displayChoice='bigChart'">
        Large Chart
      </button>
    </div>

    <!-- Show First PP slider -->
    <ng-container *ngIf="service.displayChoice==='bigChart'">
      <sliderSwitch (toggle)="service.toggleSetShowPricePoints('first') " [disabled]="false"
        [defaultValue]="service.showFirstPricePoint" [text]="'First Price Position'"></sliderSwitch>


      <!-- Show Last PP slider -->
      <sliderSwitch *ngIf="service.displayChoice==='bigChart'" (toggle)="service.toggleSetShowPricePoints('last') "
        [disabled]="false" [defaultValue]="service.showLastPricePoint" [text]="'Final Price Position'"></sliderSwitch>

    </ng-container>
  </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
  <div class="content-new">
    <div class="content-inner-new">
      <div class="dashboard-grid-container">
        <div class="dashboard-grid cols-13">

          <!-- All the slicers  -->

          <!-- ############################## -->
          <!-- Left hand column -->
          <!-- ############################## -->

          <!-- Total -->
          <div class="dashboard-tile grid-col-1-3 grid-row-1-3">
            <ng-container *ngIf="service.rawDataHighlighted">
              <div class="tileHeader">
                <div class="h4 headerWords">
                  Total Vehicles
                </div>
              </div>
              <div class="contentsHolder">
                <div id="totalCountContent" class="d-flex flex-column align-items-center justify-content-center h-100">
                  <div id="clickToViewDetail" *ngIf="pageIsFiltered()">
                    <instructionRow [message]="'Click number to view detail'"></instructionRow>
                  </div>
                  <h1 class="bigNumber clickable" id="totalCount" (click)="goToLeavingVehiclesAnalysis()">
                    <strong>
                      {{ service.rawDataHighlighted.length | cph:'number':0 }}
                    </strong>
                  </h1>
                </div>
              </div>
            </ng-container>
          </div>

          <!-- Region -->
          <div class="dashboard-tile grid-col-1-3 grid-row-3-5">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Region'"
              [tileType]="'VerticalBar'" [labelWidth]="50" [pageParams]="service.getPageParams()"
              [fieldName]="'Region'">
            </biChartTile>
          </div>

          <!-- Retailer Site Name -->
          <div class="dashboard-tile grid-col-3-5 grid-row-1-5">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Retailer Site Name'"
              [tileType]="'VerticalBar'" [labelWidth]="60" [pageParams]="service.getPageParams()"
              [fieldName]="'RetailerSiteName'">
            </biChartTile>
          </div>

          <!-- BodyType -->
          <div class="dashboard-tile grid-col-3-5 grid-row-5-7">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Body Type'"
              [customSort]="'HighToLow'" [tileType]="'VerticalBar'" [labelWidth]="30"
              [pageParams]="service.getPageParams()" [fieldName]="'BodyType'">
            </biChartTile>
          </div>

          <!-- FuelType -->
          <div class="dashboard-tile grid-col-1-3 grid-row-7-9">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Fuel Type'"
              [customSort]="'HighToLow'" [tileType]="'VerticalBar'" [labelWidth]="40"
              [pageParams]="service.getPageParams()" [fieldName]="'FuelType'">
            </biChartTile>
          </div>


          <!-- AchievedSaleType -->
          <!-- <div class="dashboard-tile grid-col-1-3 grid-row-13-15">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Achieved Sale Type'"
              [customSort]="'HighToLow'" [tileType]="'VerticalBar'" [labelWidth]="30"
              [pageParams]="service.getPageParams()" [fieldName]="'AchievedSaleType'">
            </biChartTile>
          </div> -->

           <!-- IsOnStrategy -->
           <div class="dashboard-tile grid-col-1-3 grid-row-13-15">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'On Strategy'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'IsOnStrategy'" >
            </biChartTile>
          </div>
           <!-- OptedOutPctBand -->
           <div class="dashboard-tile grid-col-5-7 grid-row-9-11">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Opted Out Percentage Band'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'OptedOutPctBand'" >
            </biChartTile>
          </div>

          <!-- ############################## -->
          <!-- Second Column -->
          <!-- ############################## -->

          <!-- Make -->
          <div class="dashboard-tile grid-col-1-3 grid-row-11-13">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Make'"
              [customSort]="'HighToLow'" [tileType]="'VerticalBar'" [labelWidth]="30"
              [pageParams]="service.getPageParams()" [fieldName]="'Make'">
            </biChartTile>
          </div>
          <!-- Model -->
          <div class="dashboard-tile grid-col-3-5 grid-row-11-15">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Model'"
              [customSort]="'HighToLow'" [tileType]="'VerticalBar'" [labelWidth]="50"
              [pageParams]="service.getPageParams()" [fieldName]="'Model'">
            </biChartTile>
          </div>
          <!-- LastPriceBand -->
          <div class="dashboard-tile grid-col-3-5 grid-row-9-11">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Price Band'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'LastPriceBand'" [customSort]="'LastPriceBand'">
            </biChartTile>
          </div>

          <!-- TransmissionType -->
          <div class="dashboard-tile grid-col-3-5 grid-row-7-9">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'TransmissionType'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'TransmissionType'">
            </biChartTile>
          </div>

          <!-- RegYear -->
          <div class="dashboard-tile grid-col-1-3 grid-row-5-7">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Registration Year'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'RegYear'" [customSort]="'RegYear'">
            </biChartTile>
          </div>
          <!-- MileageBand -->
          <div class="dashboard-tile grid-col-1-3 grid-row-9-11">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Mileage'"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'MileageBand'" [customSort]="'Mileage'">
            </biChartTile>
          </div>
         

          <!-- ############################## -->
          <!-- Few more pickers beneath charts -->
          <!-- ############################## -->

          <!-- RetailRatingBand -->
          <!-- <div class="dashboard-tile grid-col-5-7 grid-row-11-13">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="retailRatingTitle()"
              [tileType]="'HorizontalBar'" [labelHeight]="20" [pageParams]="service.getPageParams()"
              [fieldName]="'RetailRatingBand'" [customSort]="'DaysListedBand'">
            </biChartTile>
          </div> -->

          <!-- DaysListedBand -->
          <!-- <div class="dashboard-tile grid-col-7-9 grid-row-11-13">
           <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="daysListedTitle()"
             [labelHeight]="20" [tileType]="'HorizontalBar'"  [pageParams]="service.getPageParams()"
             [fieldName]="'DaysListedBand'" [customSort]="'DaysListedBand'">
           </biChartTile>
         </div> -->

          <!-- FirstPPBand -->
          <div class="dashboard-tile grid-col-5-7 grid-row-13-15">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="firstPPTitle()"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'FirstPPBand'" [customSort]="'PPBand'">
            </biChartTile>
          </div>

          <!-- Vehicle Type  -->
          <div class="dashboard-tile grid-col-5-7 grid-row-11-13">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Vehicle Type'"
              [customSort]="'HighToLow'" [tileType]="'VerticalBar'" [labelWidth]="30"
              [pageParams]="service.getPageParams()" [fieldName]="'VehicleType'">
            </biChartTile>
          </div>

          <!-- LastPPBand -->
          <div class="dashboard-tile grid-col-7-9 grid-row-13-15">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="lastPPTitle()"
              [tileType]="'VerticalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'LastPPBand'" [customSort]="'PPBand'">
            </biChartTile>
          </div>

          <!-- Price Indicator -->
          <!-- <div class="dashboard-tile grid-col-7-9 grid-row-13-15">
            <biChartTile *ngIf="service.rawData" [dataType]="dataTypes.label" [title]="'Price Indicator'"
              [tileType]="'HorizontalBar'" [labelWidth]="30" [pageParams]="service.getPageParams()"
              [fieldName]="'LastPriceIndicator'" [customSort]="'PriceIndicator'">
            </biChartTile>
          </div> -->


          <div class="dashboard-tile grid-col-9-14 grid-row-11-15">
            <table *ngIf="service.summaryStats" id="summaryStatsTable">
              <thead>
                <tr>
                  <th></th>
                  <th>All</th>
                  <th>Selected</th>
                  <th>Vs</th>
                  <th>Not selected</th>
                  <th>Vs not selected</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Vehicles</td>
                  <td>{{ service.summaryStats.all.vehicleCount|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.highlighted.vehicleCount|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.vs.vehicleCount|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.notHighlighted.vehicleCount|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.highlightedVsNot.vehicleCount|cph:'number':0 }}</td>
                </tr>
                <tr>
                  <td>Retail Rating</td>
                  <td>{{ service.summaryStats.all.retailRating|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.highlighted.retailRating|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.vs.retailRating|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.notHighlighted.retailRating|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.highlightedVsNot.retailRating|cph:'number':0 }}</td>
                </tr>
                <tr>
                  <td>Days Listed</td>
                  <td>{{ service.summaryStats.all.daysListed|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.highlighted.daysListed|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.vs.daysListed|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.notHighlighted.daysListed|cph:'number':0 }}</td>
                  <td>{{ service.summaryStats.highlightedVsNot.daysListed|cph:'number':0 }}</td>
                </tr>
                <tr>
                  <td>First Price Position</td>
                  <td>{{ service.summaryStats.all.firstPP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.highlighted.firstPP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.vs.firstPP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.notHighlighted.firstPP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.highlightedVsNot.firstPP|cph:'percent':1 }}</td>
                </tr>
                <tr>
                  <td>Final Price Position</td>
                  <td>{{ service.summaryStats.all.lastPP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.highlighted.lastPP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.vs.lastPP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.notHighlighted.lastPP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.highlightedVsNot.lastPP|cph:'percent':1 }}</td>
                </tr>
                <tr>
                  <td>Change to Price Position</td>
                  <td>{{ service.summaryStats.all.changePP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.highlighted.changePP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.notHighlighted.changePP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.notHighlighted.changePP|cph:'percent':1 }}</td>
                  <td>{{ service.summaryStats.highlightedVsNot.changePP|cph:'percent':1 }}</td>
                </tr>
              </tbody>
            </table>
          </div>


          <!-- ############################## -->
          <!-- The charts -->
          <!-- ############################## -->

          <!-- Big chart -->
          <ng-container *ngIf="service.displayChoice==='bigChart'">

            <!-- Big chart itself -->
            <div class="dashboard-tile grid-col-5-14 grid-row-1-11">
              <bigLeavingChart [params]="service.bigChartParams" *ngIf="service.bigChartParams"
                [newDataEmitter]="service.newBigChartDataEmitter"></bigLeavingChart>
            </div>

          </ng-container>

          <!-- Small charts -->
          <ng-container *ngIf="service.displayChoice==='smallCharts'">
            <!-- Sold volume by retail rating -->
            <div class="dashboard-tile grid-col-5-8 grid-row-1-4">
              <ng-container *ngIf="service.smallChartSetData">
                <div class="tileHeader">
                  <div class="h4 headerWords">
                    {{ service.smallChartSetData.rrSoldVolume.Title }}
                  </div>
                </div>
                <div class="contentsHolder">
                  <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrSoldVolume'"
                    [newDataEmitter]="service.newSmallChartDataEmitter"
                    [params]="service.smallChartSetData.rrSoldVolume" [doesFilter]="true"
                    [pageParams]="service.getPageParams()" [dataType]="dataTypes.label" [tileType]="'HorizontalBar'"
                    [fieldName]="'RetailRatingBand'" [showPercentageOfTotal]="true">
                  </barChart>
                </div>
              </ng-container>
            </div>

            <!-- First price position -->
            <div class="dashboard-tile grid-col-8-11 grid-row-1-4">
              <ng-container *ngIf="service.smallChartSetData">
                <div class="tileHeader">
                  <div class="h4 headerWords">
                    {{ service.smallChartSetData.rrFirstPP.Title }}
                  </div>
                </div>
                <div class="contentsHolder">
                  <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrFirstPP'"
                            [newDataEmitter]="service.newSmallChartDataEmitter"
                            [doesFilter]="true" [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                            [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"
                            [params]="service.smallChartSetData.rrFirstPP">
                  </barChart>
                </div>
              </ng-container>
            </div>

            <!-- Sold volume by Days Listed -->
            <div class="dashboard-tile grid-col-11-14 grid-row-1-4">
              <ng-container *ngIf="service.smallChartSetData">
                <div class="tileHeader">
                  <div class="h4 headerWords">
                    {{ service.smallChartSetData.dlSoldVolume.Title }}
                  </div>
                </div>
                <div class="contentsHolder">
                  <barChart *ngIf="service.smallChartSetData" [dataKey]="'dlSoldVolume'"
                            [newDataEmitter]="service.newSmallChartDataEmitter"
                            [params]="service.smallChartSetData.dlSoldVolume"
                            [doesFilter]="true" [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                            [tileType]="'HorizontalBar'" [fieldName]="'DaysListedBand'"
                            [showPercentageOfTotal]="true">
                  </barChart>
                </div>
              </ng-container>
            </div>


            <!-- Days to sell by Retail Rating -->
            <div class="dashboard-tile grid-col-5-8 grid-row-4-7">
              <ng-container *ngIf="service.smallChartSetData">
                <div class="tileHeader">
                  <div class="h4 headerWords">
                    {{ service.smallChartSetData.rrDaysListed.Title }}
                  </div>
                </div>
                <div class="contentsHolder">
                  <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrDaysListed'"
                            [newDataEmitter]="service.newSmallChartDataEmitter"
                            [doesFilter]="true" [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                            [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"
                            [params]="service.smallChartSetData.rrDaysListed">
                  </barChart>
                </div>
              </ng-container>
            </div>

            <!-- Final PP -->
            <div class="dashboard-tile grid-col-8-11 grid-row-4-7">
              <ng-container *ngIf="service.smallChartSetData">
                <div class="tileHeader">
                  <div class="h4 headerWords">
                    {{ service.smallChartSetData.rrLastPP.Title }}
                  </div>
                </div>
                <div class="contentsHolder">
                  <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrLastPP'"
                            [newDataEmitter]="service.newSmallChartDataEmitter"

                            [doesFilter]="true" [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                            [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"

                            [params]="service.smallChartSetData.rrLastPP">
                  </barChart>
                </div>
              </ng-container>
            </div>

            <!-- Average days listed by Days Listed -->
            <div class="dashboard-tile grid-col-11-14 grid-row-4-7">
              <ng-container *ngIf="service.smallChartSetData">
                <div class="tileHeader">
                  <div class="h4 headerWords">
                    {{ service.smallChartSetData.dlDaysListed.Title }}
                  </div>
                </div>
                <div class="contentsHolder">
                  <barChart *ngIf="service.smallChartSetData" [dataKey]="'dlDaysListed'"
                            [newDataEmitter]="service.newSmallChartDataEmitter"
                            [doesFilter]="true"
                            [pageParams]="service.getPageParams()"
                            [fieldName]="'DaysListedBand'"
                            [params]="service.smallChartSetData.dlDaysListed">
                  </barChart>
                </div>
              </ng-container>
            </div>

            <!-- Row 3 -->

            <!-- PP change -->
            <div class="dashboard-tile grid-col-8-11 grid-row-7-10">
              <ng-container *ngIf="service.smallChartSetData">
                <div class="tileHeader">
                  <div class="h4 headerWords">
                    {{ service.smallChartSetData.rrChangedPP.Title }}
                  </div>
                </div>
                <div class="contentsHolder">
                  <barChart *ngIf="service.smallChartSetData" [dataKey]="'rrChangedPP'"
                            [newDataEmitter]="service.newSmallChartDataEmitter"

                            [doesFilter]="true" [pageParams]="service.getPageParams()" [dataType]="dataTypes.label"
                            [tileType]="'HorizontalBar'" [fieldName]="'RetailRatingBand'"

                            [params]="service.smallChartSetData.rrChangedPP">
                  </barChart>
                </div>
              </ng-container>
            </div>

            <!-- Days listed final PP -->
            <div class="dashboard-tile grid-col-11-14 grid-row-7-10">
              <ng-container *ngIf="service.smallChartSetData">
                <div class="tileHeader">
                  <div class="h4 headerWords">
                    {{ service.smallChartSetData.dlLastPP.Title }}
                  </div>
                </div>
                <div class="contentsHolder">
                  <barChart *ngIf="service.smallChartSetData" [dataKey]="'dlLastPP'"
                            [newDataEmitter]="service.newSmallChartDataEmitter"
                            [doesFilter]="true"
                            [pageParams]="service.getPageParams()"
                            [fieldName]="'DaysListedBand'"
                            [params]="service.smallChartSetData.dlLastPP">
                  </barChart>
                </div>
              </ng-container>
            </div>
          </ng-container>


        </div>
      </div>
    </div>
  </div>
</div>