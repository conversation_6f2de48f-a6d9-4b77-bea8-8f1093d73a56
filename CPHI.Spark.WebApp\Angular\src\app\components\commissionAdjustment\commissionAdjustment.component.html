<ng-template #commissionAdjustmentModalRef let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title"><span *ngIf="!readOnly">Edit</span> Adjustments</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div [ngClass]="constants.environment.customer" class="modal-body">
        <table *ngIf="commissionAdjustments.length > 0 || amAddingAdjustment; else noAdjustments">
            <thead>
                <tr>
                    <td *ngIf="readOnly">Site</td>
                    <td *ngIf="readOnly">Exec</td>
                    <td>Created By</td>
                    <td>Description</td>
                    <td>Value</td>
                    <td *ngIf="!readOnly"></td>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let commissionAdjustment of commissionAdjustments">
                    <td *ngIf="readOnly" class="uneditable">{{ commissionAdjustment.Site }}</td>
                    <td *ngIf="readOnly" class="uneditable">{{ commissionAdjustment.SalesExecName }}</td>
                    <td class="uneditable">{{ commissionAdjustment.CreatedByName }}</td>
                    <td *ngIf="!readOnly; else disabledDescription">
                        <input type="text" [(ngModel)]="commissionAdjustment.Description" (focusout)="checkIfUpdated()">
                    </td>
                    <ng-template #disabledDescription>
                        <td class="uneditable">{{ commissionAdjustment.Description }}</td>
                    </ng-template>
                    <td *ngIf="!readOnly; else disabledValue" class="adjustment-value">
                        <input type="text" [(ngModel)]="commissionAdjustment.Value" (focusout)="checkIfUpdated()">
                    </td>
                    <ng-template #disabledValue>
                        <td class="uneditable text-end">{{ commissionAdjustment.Value | cph:'currency':2 }}</td>
                    </ng-template>
                    <td *ngIf="!readOnly" class="with-button">
                        <button class="btn red" [disabled]="amAddingAdjustment" (click)="delete(commissionAdjustment.Id)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                <tr *ngIf="readOnly">
                    <td colspan="4" class="ghost-cell"></td>
                    <td class="uneditable text-end">{{ totalAdjustmentsValue | cph:'currency':2 }}</td>
                </tr>

                <tr *ngIf="amAddingAdjustment">
                    <td class="uneditable">{{ selections.user.Name }}</td>
                    <td>
                        <input type="text" [(ngModel)]="newDescription">
                    </td>
                    <td class="adjustment-value">
                        <input type="number text-end" [(ngModel)]="newValue">
                    </td>
                    <td class="with-button">
                        <button class="btn red" (click)="cancel()">
                            <i class="fa fa-times"></i>
                        </button>
                        <button *ngIf="newDescription && newValue" class="btn green ml-2" (click)="create()">
                            <i class="fas fa-save"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>

        <ng-template #noAdjustments>
            <p class="mb-0">No adjustments to display. <span *ngIf="!readOnly">Click the button below to add one.</span></p>
        </ng-template>

        <button *ngIf="!amAddingAdjustment && !readOnly" class="btn btn-primary mt-2" (click)="amAddingAdjustment = true">
            Add Adjustment
        </button>
    </div>

    <div class="modal-footer">
        <button *ngIf="!adjustmentsUpdated; else changesMade" type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">
            {{constants.translatedText.Close}}
        </button>

        <ng-template #changesMade>
            <button class="btn btn-warning" (click)="cancelAllChanges()">
                Cancel
            </button>
            <button class="btn btn-success" (click)="update()">
                Save Changes
            </button>
        </ng-template>
    </div>
</ng-template>