﻿using CPHI.Spark.Model;
using CPHI.Spark.BusinessLogic.AutoPrice;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using System.IO;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Model.Services;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using Newtonsoft.Json;
using System.Net.Http;
using Microsoft.IdentityModel.Tokens;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using Microsoft.AspNetCore.Http.HttpResults;
using log4net;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{

   public interface IVehicleValuationService
   {
      Task<ValuationSummary> GetLatestVehicleValuationSummaryForAdvert(int advertId);
      Task<ValuationPriceSet> GetNewValuationForAdvert(GetValuationPriceSetParams parms);
      Task<ValuationResultForAdvert> GetPreviousValuationResultForAdvert(int advertId);
      Task<IEnumerable<ValuationBatchResult>> GetValuationBatchResults(GetValuationBatchResultsParams parms, Model.DealerGroupName dealerGroup);
      Task<VehicleValuationInformation> GetVehicleInformation(string vehicleReg, int mileage, int? vehicleValuationId);
      Task<List<VehicleSpecOption>> GetVehicleSpecOptions(GetVehicleSpecOptionsParams parms);
      Task<List<VehicleValuationBatchDTO>> GetVehicleValuationBatches(Model.DealerGroupName dealerGroup, bool? single);
      Task SaveVehicleSpecBuildForAdvert(ValuationResultForAdvertToSave valuation);
      Task SaveVehicleValuation(ValuationResultForNewVehicleToSave parms);
      Task DeleteVehicleSpecBuildForAdvert(int advertId);
      Task<IEnumerable<StockLevelAndCover>> GetStockLevelAndCover(string model, int retailerSiteId);
      Task<ValuationPriceSet> GetExistingValuationForAdvert(int batchId, int vehicleValuationId);

      Task<VehicleValuation> GetVehicleValuation(int vehicleValuationId);
      Task<ValuationAndOptionChangeImpact> GetOptionChangeImpact(GetValuationPriceSetChangeParams parms);
      Task<ValuationModalNew> GetValuationModalNew(GetValuationModalNewParams parms);
      Task<DayToSellAndPriceIndicator> GetEstimatedDayToSellAndPriceIndicator(GetEstimatedDayToSellAndPriceIndicatorParams parms);
      //Task<TrendedValuationsDaysToSellAndPriceIndicator> GetTrendedValuationsDaysToSellAndPriceIndicator(GetEstimatedDayToSellAndPriceIndicatorParams parms, int chosenRetailerId);
      Task<decimal> WorkoutAdjustmentFactor(string derivativeId, DateTime? firstRegisteredDate, int odometerReading, List<string> vehicleAdvertPortalOptions, decimal adjustedValuation);
      Task<VehicleRecallStatus> GetVehicleRecallStatus(string reg);
      Task UploadVehiclesForValuation(string dealerGroupBlobName, IFormFile file, List<VehiclesForValuationParams> vehicles, string batchName, bool applyPriceScenarios, BulkUploadPredefinedTemplateType bulkUploadPredefinedTemplateType);
      Task ParseAndUploadVehiclesForValuation(IFormFile file, string vehiclesForValuationParams, string batchName, bool applyPriceScenarios, BulkUploadPredefinedTemplateType bulkUploadPredefinedTemplateType);
      Task<CompetitorSummary> GetValuationModalCompetitorAnalysis(CompetitorSearchParams parms, OurVehicleParams ourVehicleParams, DealerGroupName dealerGroup);
      Task<NewVehicleValuationModal> GetNewValuationModal(GetNewVehicleModalParams parms);
      Task<IEnumerable<VehicleHistoryItem>> GetVehicleHistory(GetVehicleHistoryParams parms);
      Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItemsForModel(GetLeavingVehicleItemsForModalParams parms);
      Task<IEnumerable<SameModelAdvert>> GetSameModelAdverts(GetSameModelAdvertsParams parms);
      Task<IEnumerable<StockLevelAndCover>> GetStockCover(GetSameModelAdvertsParams parms);

   }

   public class VehicleValuationService : IVehicleValuationService
   {
      private readonly IUserService userService;
      private readonly AutoTraderVehiclesClient atVehiclesClient;
      private readonly AutoTraderVehicleMetricsClient atMetricsClient;
      private readonly AutoTraderFutureValuationsClient atFutureValsClient;
      private readonly AutoTraderValuationsClient atValuationsClient;
      private readonly AutoTraderTaxonomyClient atTaxonomyClient;
      private readonly AutoTraderCompetitorClient atCompetitorClient;
      private readonly IAutoTraderApiTokenClient tokenClient;
      private readonly IImageService imageService;

      private readonly IConfiguration configuration;
      private readonly ValuationsDataAccess _valuationsDataAccess;
      private readonly ILeavingPricesDataAccess leavingPricesDataAccess;
      private readonly IVehicleAdvertsDataAccess vehicleAdvertsDataAccess;

      private readonly IDVSAApiClient dVSAApiClient;
      private readonly string apiKey;
      private readonly string apiSecret;
      private readonly string baseURL;
      private readonly HttpClient httpClient;
      private readonly IHttpClientFactory _httpClientFactory;

      private readonly string _connectionString;

      public VehicleValuationService(
                                     IUserService userService,
                                     IConfiguration configuration,

                                     IImageService imageService,
                                     IDVSAApiClient dVSAApiClient,
                                     IHttpClientFactory httpClientFactory,
                                     HttpClient httpClient)
      {
         this.userService = userService;

         this._httpClientFactory = httpClientFactory;
         baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         atVehiclesClient = new AutoTraderVehiclesClient(httpClientFactory, apiKey, apiSecret, baseURL);
         atMetricsClient = new AutoTraderVehicleMetricsClient(httpClientFactory, apiKey, apiSecret, baseURL);
         atFutureValsClient = new AutoTraderFutureValuationsClient(httpClientFactory, apiKey, apiSecret, baseURL);
         atValuationsClient = new AutoTraderValuationsClient(httpClientFactory, apiKey, apiSecret, baseURL);
         atTaxonomyClient = new AutoTraderTaxonomyClient(httpClientFactory, apiKey, apiSecret, baseURL);
         atCompetitorClient = new AutoTraderCompetitorClient(httpClientFactory, apiKey, apiSecret, baseURL);
         tokenClient = new AutoTraderApiTokenClient(httpClientFactory, apiKey, apiSecret, baseURL);


         this.imageService = imageService;
         this.configuration = configuration;
         Model.DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
         string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
         _connectionString = configuration.GetConnectionString(dgName);
         _valuationsDataAccess = new ValuationsDataAccess(_connectionString);
         this.dVSAApiClient = dVSAApiClient;
         leavingPricesDataAccess = new LeavingPricesDataAccess(_connectionString);
         vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         this.httpClient = httpClient;
      }

      public async Task<ValuationResultForAdvert> GetPreviousValuationResultForAdvert(int advertId)
      {
         var vehicleAdvertSnapshotTask = _valuationsDataAccess.GetMarketAndAdjustPricesForAdvert(advertId);
         var previousValuationTask = _valuationsDataAccess.GetPreviousValuationResultForAdvert(advertId);

         await Task.WhenAll(vehicleAdvertSnapshotTask, previousValuationTask);

         var result = new ValuationResultForAdvert();

         if (previousValuationTask.Result != null)
         {
            result.ThisVehiclePrices = previousValuationTask.Result.ThisVehiclePrices;
            result.LastUpdatedDate = previousValuationTask.Result.LastUpdatedDate;
            result.LastUpdatedByName = previousValuationTask.Result.LastUpdatedByName;
            result.AverageSpecPrices = previousValuationTask.Result.AverageSpecPrices;
            result.VehicleSpecOptions = previousValuationTask.Result.VehicleSpecOptions;
         }

         if (vehicleAdvertSnapshotTask.Result != null)
         {
            result.SavedMktAveragePrices = new ValuationPriceSet(vehicleAdvertSnapshotTask.Result, true);
            result.SavedAdjAveragePrices = new ValuationPriceSet(vehicleAdvertSnapshotTask.Result, false);
         }

         return result;



      }

      public async Task<ValuationSummary> GetLatestVehicleValuationSummaryForAdvert(int advertId)
      {
         return await _valuationsDataAccess.GetLatestVehicleValuationSummaryForAdvert(advertId);
      }

      public async Task<ValuationPriceSet> GetExistingValuationForAdvert(int batchId, int vehicleValuationId)
      {
         var vehicleValuations = await _valuationsDataAccess.GetAllVehicleValuations(vehicleValuationId);
         return new ValuationPriceSet(vehicleValuations.First());
      }

      public async Task<ValuationPriceSet> GetNewValuationForAdvert(GetValuationPriceSetParams parms)
      {

         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];

         //int userId = userService.GetUserId();
         int userRetailerSiteRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");// await userService.GetUserRetailerSiteRetailerId(userId, userService.GetUserDealerGroupName());
         ValuationAPIParam valuationAPIParam = CreateParamforVehicleMetricApi(parms, userRetailerSiteRetailerId);

         string _bearerToken = (await tokenClient.GetToken()).AccessToken;
         ValuationAPIResponseRoot result = await atValuationsClient.GetValuation(valuationAPIParam, baseURL, _bearerToken);

         return new ValuationPriceSet(result);
      }

      public async Task<ValuationAndOptionChangeImpact> GetOptionChangeImpact(GetValuationPriceSetChangeParams parms)
      {
         GetValuationPriceSetParams wasChoicesParams = new GetValuationPriceSetParams()
         {
            DerivativeId = parms.DerivativeId,
            FirstRegistrationDate = parms.FirstRegistrationDate,
            Mileage = parms.Mileage,
            Condition = parms.Condition,
            HaveProvidedFeatures = true,
            Features = parms.ChoicesWas
         };

         GetValuationPriceSetParams nowChoicesParams = new GetValuationPriceSetParams()
         {
            DerivativeId = parms.DerivativeId,
            FirstRegistrationDate = parms.FirstRegistrationDate,
            Mileage = parms.Mileage,
            Condition = parms.Condition,
            HaveProvidedFeatures = true,
            Features = parms.ChoicesNow
         };

         int userRetailerSiteRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");// await userService.GetUserRetailerSiteRetailerId(userId, userService.GetUserDealerGroupName());
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         string _bearerToken = (await tokenClient.GetToken()).AccessToken;

         ValuationAPIParam wasApiParams = CreateParamforVehicleMetricApi(wasChoicesParams, userRetailerSiteRetailerId);
         Task<ValuationAPIResponseRoot> wasTask = atValuationsClient.GetValuation(wasApiParams, baseURL, _bearerToken);

         ValuationAPIParam nowApiParams = CreateParamforVehicleMetricApi(nowChoicesParams, userRetailerSiteRetailerId);
         Task<ValuationAPIResponseRoot> nowTask = atValuationsClient.GetValuation(nowApiParams, baseURL, _bearerToken);

         await Task.WhenAll(wasTask, nowTask);

         var wasResult = new ValuationPriceSet(wasTask.Result);
         var nowResult = new ValuationPriceSet(nowTask.Result);

         ValuationAndOptionChangeImpact result = new ValuationAndOptionChangeImpact()
         {
            OptionChangeImpact = (nowResult.RetailThisVehicle ?? 0) - (wasResult.RetailThisVehicle ?? 0),
            ThisVehiclePrices = nowResult
         };

         return result;
      }

      public async Task SaveVehicleSpecBuildForAdvert(ValuationResultForAdvertToSave valuation)
      {
         //SPK-3434
         ///approach is:
         ///1. Find if we already have a VehicleAdvertSpecBuild for this advertId.  If so we can update it, otherwise create a new one.  Use the UserId for LastUpdatedBy_Id and NOW() for lastUpdated
         var vehicleAdvertsDataAccess = new VehicleAdvertsDataAccess(_connectionString);
         var vehicleAdvertSpecBuild = await vehicleAdvertsDataAccess.GetVehicleAdvertSpecBuild(valuation.VehicleAdvertId);

         if (vehicleAdvertSpecBuild != null)
         {
            //Update
            var updatedVehicleAdvertSpecBuild = new VehicleAdvertSpecBuild(valuation, userService.GetUserId());
            updatedVehicleAdvertSpecBuild.Id = vehicleAdvertSpecBuild.Id;
            await vehicleAdvertsDataAccess.UpdateVehicleAdvertSpecBuild(updatedVehicleAdvertSpecBuild);
         }
         else
         {
            //Add
            VehicleAdvertSpecBuild newVehicleAdvertSpecBuild = new VehicleAdvertSpecBuild(valuation, userService.GetUserId());
            vehicleAdvertSpecBuild = await vehicleAdvertsDataAccess.AddVehicleAdvertSpecBuild(newVehicleAdvertSpecBuild);
         }

         ///2. Should now have a VehicleAdvertSpecBuild.  Next determine if we need to create any new VehicleOptions by looking through the incoming list
         var autoPriceDataAccess = new AutoPriceDataAccess(_connectionString);
         var allVehicleOptions = await autoPriceDataAccess.GetAllVehicleOptions();

         List<VehicleOption> vehicleOptionsToAdd = new List<VehicleOption>();
         foreach (var vehicleOption in valuation.VehicleSpecOptions)
         {
            var curretObj = allVehicleOptions.Where(v => v.Name == vehicleOption.Name && v.Category == vehicleOption.Category && v.OriginalPrice == vehicleOption.OriginalPrice);
            if (!curretObj.Any()) { vehicleOptionsToAdd.Add(new VehicleOption(vehicleOption)); }
         }

         var vehicleOptionsAdded = await autoPriceDataAccess.AddVehicleOptions(vehicleOptionsToAdd);
         allVehicleOptions.AddRange(vehicleOptionsAdded);
         var allVehicleOptionsDict = allVehicleOptions.ToDictionary(a => a.Id);


         ///and finding matches based on Name AND Category AND OriginalPrice.  Create any new ones
         ///3. Save the VehicleAdvertSpecBuildOptions.
         List<VehicleAdvertSpecBuildOption> allVehicleAdvertSpecBuildOptions = new List<VehicleAdvertSpecBuildOption>();
         List<VehicleAdvertSpecBuildOption> vehicleAdvertSpecBuildOptionsToUpdate = new List<VehicleAdvertSpecBuildOption>();
         List<VehicleAdvertSpecBuildOption> vehicleAdvertSpecBuildOptionsToAdd = new List<VehicleAdvertSpecBuildOption>();

         allVehicleAdvertSpecBuildOptions = await vehicleAdvertsDataAccess.GetVehicleAdvertSpecBuildOptions(vehicleAdvertSpecBuild.Id);


         foreach (var vehicleOption in valuation.VehicleSpecOptions)
         {
            var vehicleSpec = allVehicleOptions.Where(v => v.Name == vehicleOption.Name && v.Category == vehicleOption.Category && v.OriginalPrice == vehicleOption.OriginalPrice).FirstOrDefault();
            var vehicleAdvertSpecBuildOption = allVehicleAdvertSpecBuildOptions.Where(v => v.VehicleOption_Id == vehicleSpec.Id).FirstOrDefault();
            if (vehicleAdvertSpecBuildOption != null)
            {
               vehicleAdvertSpecBuildOption.Impact = vehicleOption.Impact;
               vehicleAdvertSpecBuildOption.IsChosen = vehicleOption.IsChosen;
               vehicleAdvertSpecBuildOption.IsStandard = vehicleOption.IsStandard;

               vehicleAdvertSpecBuildOptionsToUpdate.Add(vehicleAdvertSpecBuildOption);
            }
            else
            {
               vehicleAdvertSpecBuildOptionsToAdd.Add(new VehicleAdvertSpecBuildOption()
               {
                  VehicleAdvertSpecBuild_Id = vehicleAdvertSpecBuild.Id,
                  VehicleOption_Id = vehicleSpec.Id,
                  Impact = vehicleOption.Impact,
                  IsChosen = vehicleOption.IsChosen,
                  IsStandard = vehicleOption.IsStandard
               });
            }
         }

         await vehicleAdvertsDataAccess.UpdateVehicleAdvertSpecBuildOptions(vehicleAdvertSpecBuildOptionsToUpdate);
         await vehicleAdvertsDataAccess.AddVehicleAdvertSpecBuildOptions(vehicleAdvertSpecBuildOptionsToAdd);

      }


      public async Task SaveVehicleValuation(ValuationResultForNewVehicleToSave parms)
      {
         if (parms.ValuationId == null)
         {
            await SaveNewValuation(parms);
         }
         else
         {
            await SaveExistingValuation(parms);
         }
      }


      private async Task SaveExistingValuation(ValuationResultForNewVehicleToSave parms)
      {
         //update valuation
         await _valuationsDataAccess.UpdateVehicleValuationCostings((int)parms.ValuationId, parms.Costing);
         await UpdateValuationOptionsMapping(parms);
         await UpdateValuationsBySite(parms);

      }


      private async Task SaveNewValuation(ValuationResultForNewVehicleToSave parms)
      {
         //create a new batchId
         int userId = userService.GetUserId();
         var newBatchParams = new VehicleValuationBatch(userId, $"{parms.VehicleReg} - Single Valuation", false, true, BulkUploadPredefinedTemplateType.Other);
         newBatchParams.Status = "Complete";
         newBatchParams.ValuationCompleteDate = DateTime.UtcNow;
         var newBatch = await _valuationsDataAccess.CreateVehicleValuationBatch(newBatchParams);
         int batchId = newBatch.Id;

         //create new valuation
         VehicleValuation vehicleValuation = await CreateNewVehicleValuation(parms, batchId);
         parms.ValuationId = vehicleValuation.Id; //put it into there
         await CreateNewValuationToOptionsMapping(parms);
         await CreateNewValuationsBySite(parms);

      }

      private async Task CreateNewValuationsBySite(ValuationResultForNewVehicleToSave parms)
      {

         //-----------------------------------------------
         //Create VV Rating by Site
         List<VehicleValuationRatingBySite> vehicleValuationRatingBySiteToAdd = new();
         foreach (var siteStrategy in parms.SitesStrategy)
         {
            vehicleValuationRatingBySiteToAdd.Add(new VehicleValuationRatingBySite((int)parms.ValuationId, siteStrategy));
         }

         await _valuationsDataAccess.CreateVehicleValuationRatingBySite(vehicleValuationRatingBySiteToAdd);
      }

      private async Task UpdateValuationsBySite(ValuationResultForNewVehicleToSave parms)
      {
         //remove the old ones
         await _valuationsDataAccess.RemoveVehicleValuationRatingBySite((int)parms.ValuationId);


         //Create new ones
         List<VehicleValuationRatingBySite> vehicleValuationRatingBySiteToAdd = new();
         foreach (var siteStrategy in parms.SitesStrategy)
         {
            vehicleValuationRatingBySiteToAdd.Add(new VehicleValuationRatingBySite((int)parms.ValuationId, siteStrategy));
         }

         await _valuationsDataAccess.CreateVehicleValuationRatingBySite(vehicleValuationRatingBySiteToAdd);
      }

      private async Task UpdateValuationOptionsMapping(ValuationResultForNewVehicleToSave parms)
      {
         List<VehicleOption> allOptions = await CreateNewOptionsAsRequired(parms);

         //Just remove the old mappings
         await _valuationsDataAccess.RemoveVehicleValuationOptionMappingsAsync((int)parms.ValuationId);

         //Now create the latest ones
         if (parms.VehicleSpecOptions != null)
         {
            List<VehicleValuationOption> vehicleValuationOptionsToAdd = new();

            foreach (var vehicleSpecOption in parms.VehicleSpecOptions.Where(v => v.IsChosen == true))
            {
               var currectVehicleOption = allOptions.Where(v => v.Name == vehicleSpecOption.Name && v.Category == vehicleSpecOption.Category && v.OriginalPrice == vehicleSpecOption.OriginalPrice).FirstOrDefault();
               vehicleValuationOptionsToAdd.Add(new VehicleValuationOption((int)parms.ValuationId, currectVehicleOption.Id, vehicleSpecOption));
            }
            await _valuationsDataAccess.AddVehicleValuationOptions(vehicleValuationOptionsToAdd);
         }
      }



      private async Task CreateNewValuationToOptionsMapping(ValuationResultForNewVehicleToSave parms)
      {
         List<VehicleOption> allOptions = await CreateNewOptionsAsRequired(parms);


         //Create VVOptions
         if (parms.VehicleSpecOptions != null)
         {
            List<VehicleValuationOption> vehicleValuationOptionsToAdd = new();

            foreach (var vehicleSpecOption in parms.VehicleSpecOptions.Where(v => v.IsChosen == true))
            {
               var currectVehicleOption = allOptions.Where(v => v.Name == vehicleSpecOption.Name && v.Category == vehicleSpecOption.Category && v.OriginalPrice == vehicleSpecOption.OriginalPrice).FirstOrDefault();
               vehicleValuationOptionsToAdd.Add(new VehicleValuationOption((int)parms.ValuationId, currectVehicleOption.Id, vehicleSpecOption));
            }
            await _valuationsDataAccess.AddVehicleValuationOptions(vehicleValuationOptionsToAdd);
         }
      }

      private async Task<List<VehicleOption>> CreateNewOptionsAsRequired(ValuationResultForNewVehicleToSave parms)
      {
         //Check and create new VehicleOptions
         var autoPriceDataAccess = new AutoPriceDataAccess(_connectionString);
         var allOptions = await autoPriceDataAccess.InsertVehicleOptionsIfNotExistsAsync(parms.VehicleSpecOptions);
         return allOptions;
      }

      private async Task<VehicleValuation> CreateNewVehicleValuation(ValuationResultForNewVehicleToSave parms, int batchId)
      {
         //Create Vehicle valuation
         VehicleValuation vehicleValuation = new VehicleValuation(parms, batchId);
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string token = await GetToken(baseURL);
         int userRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");
         ATNewVehicleGet advert = await atVehiclesClient.GetIndividualVehicle(vehicleValuation.VehicleReg, vehicleValuation.Mileage, userRetailerId, token, baseURL);
         UpdateVehicleValuationProperties(vehicleValuation, advert);
         if (vehicleValuation.SpecificColour == null)
         {
            vehicleValuation.SpecificColour = parms.SpecificColour;
         }
         vehicleValuation = await _valuationsDataAccess.CreateVehicleValuation(vehicleValuation);
         return vehicleValuation;
      }

      private async Task<string> GetToken(string baseURL)
      {
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         var token = (await tokenClient.GetToken()).AccessToken;
         return token;
      }


      public async Task<VehicleValuation> GetVehicleValuation(int vehicleValuationId)
      {
         return (await _valuationsDataAccess.GetAllVehicleValuations(vehicleValuationId)).First();
      }


      public async Task<VehicleValuationInformation> GetVehicleInformation(string vehicleReg, int mileage, int? vehicleValuationId)
      {
         if (vehicleValuationId.HasValue && vehicleValuationId.Value > 0)
         {
            var vehicleValuation = await GetVehicleValuation(vehicleValuationId.Value);
            return new VehicleValuationInformation(vehicleValuation);
         }

         //Otherwise we get from AT
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];

         var token = (await tokenClient.GetToken()).AccessToken;
         int userId = userService.GetUserId();
         int userRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");//await userService.GetUserRetailerSiteRetailerId(userId, userService.GetUserDealerGroupName());
         var result = await atVehiclesClient.GetIndividualVehicle(vehicleReg, mileage, userRetailerId, token, baseURL);

         return new VehicleValuationInformation(result);
      }




      public async Task<List<VehicleSpecOption>> GetVehicleSpecOptions(GetVehicleSpecOptionsParams parms)
      {
         //Nimish to build from here on - SPK-3433
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];

         string firstRegisteredDate = parms.FirstRegisteredDate.HasValue ? parms.FirstRegisteredDate.Value.ToString("yyyy-MM-dd") : string.Empty;
         int userId = userService.GetUserId();
         int retailerSiteRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");//await userService.GetUserRetailerSiteRetailerId(userId, userService.GetUserDealerGroupName());
         List<ATFeature> aTFeatures = await atTaxonomyClient.GetTaxonomyFeatures(apiKey, apiSecret, baseURL, parms.DerivativeId, firstRegisteredDate, retailerSiteRetailerId);


         List<VehicleSpecOption> vehicleSpecOptions = new List<VehicleSpecOption>();
         foreach (var aTFeature in aTFeatures)
         {
            vehicleSpecOptions.Add(new VehicleSpecOption(aTFeature));
         }


         if (parms.BatchId.HasValue && parms.BatchId.Value > 0)
         {
            var result = (await _valuationsDataAccess.GetVehicleSpecOptions(parms.BatchId.Value)).ToList();
            var userChosenSpecsForValuation = result.Where(r => r.IsChosen == true).ToList();

            foreach (var spec in userChosenSpecsForValuation)
            {
               var specOption = vehicleSpecOptions.Where(v => v.Category == spec.Category && v.Name == spec.Name).First();
               specOption.IsChosen = spec.IsChosen;
            }
         }


         return vehicleSpecOptions;

      }


      //private async Task<string> GetAutotraderToken(string baseURL)
      //{
      //   string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
      //   string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];

      //   string atToken = (await tokenClient.GetToken()).AccessToken;
      //   return atToken;
      //}

      private ValuationAPIParam CreateParamforVehicleMetricApi(GetValuationPriceSetParams parms, int advertiserId)
      {

         var res = new ValuationAPIParam()
         {
            vehicle = new VehicleMetricAPI_Vehicle()
            {
               derivativeId = parms.DerivativeId,
               firstRegistrationDate = parms.FirstRegistrationDate.HasValue ? parms.FirstRegistrationDate.Value.ToString("yyyy-MM-dd") : null,
               odometerReadingMiles = parms.Mileage.HasValue ? parms.Mileage.Value : 0,
            },
            features = this.GetFeatureList(parms),
            conditionRating = parms.Condition,
            advertiserId = advertiserId.ToString()
         };

         if (parms.SellingPrice > 0)
         {
            res.adverts = new ValuationAPI_Price();
            res.adverts.retailAdverts = new ValuationAPI_RetailAdverts();
            res.adverts.retailAdverts.price = new ATNewVehicleGet_Valuation();
            res.adverts.retailAdverts.price.amountGBP = parms.SellingPrice;
         }



         return res;
      }

      private List<ValuationAPI_Feature> GetFeatureList(GetValuationPriceSetParams parms)
      {
         if (parms.HaveProvidedFeatures)
         {
            return parms.Features.Count > 0 ? parms.Features.Select(f => new ValuationAPI_Feature() { name = f.ToString() }).ToList() : new List<ValuationAPI_Feature>() { new ValuationAPI_Feature() { name = string.Empty } };
         }
         return null;
      }

      public async Task UploadVehiclesForValuation(string dealerGroupBlobName, IFormFile file, List<VehiclesForValuationParams> vehicles, string batchName, bool applyPriceScenarios, BulkUploadPredefinedTemplateType bulkUploadPredefinedTemplateType)
      {
         //Create a new batch
         List<VehicleValuation> vehicleValuations = new List<VehicleValuation>();

         foreach (var vehicle in vehicles)
         {
            vehicleValuations.Add(new VehicleValuation(vehicle));
         }

         var newBatch = await _valuationsDataAccess.CreateVehicleValuationBatch(new VehicleValuationBatch(userService.GetUserId(), batchName, applyPriceScenarios, false, bulkUploadPredefinedTemplateType, vehicleValuations));

         //Upload file to blob
         using (Stream stream = file.OpenReadStream())
         {
            var result = await imageService.UploadVehicleBatchFile(dealerGroupBlobName, stream, newBatch.Id);
            if (result.Equals(false))
            {
               throw new Exception($"Upload Failed for file : {file.Name}");
            }
         }


      }

      //Valuation
      public async Task<List<VehicleValuationBatchDTO>> GetVehicleValuationBatches(Model.DealerGroupName dealerGroup, bool? single)
      {
         var valuationsDataAccess = new ValuationsDataAccess(_connectionString);
         return await valuationsDataAccess.GetVehicleValuationBatches(dealerGroup, single);
      }

      public async Task<IEnumerable<ValuationBatchResult>> GetValuationBatchResults(GetValuationBatchResultsParams parms, Model.DealerGroupName dealerGroup)
      {
         var valuationsDataAccess = new ValuationsDataAccess(_connectionString);
         var userRetailerSiteIds = userService.GetUserRetailSiteIdsString();

         if (parms.IsApplyPriceScenarios)
         {
            List<ValuationPriceScenarioBatchResult> detailedResults = (await valuationsDataAccess.GetValuationPriceScenarioBatchResults(parms, dealerGroup)).ToList();

            List<ValuationBatchResult> results = new List<ValuationBatchResult>();

            foreach (ValuationPriceScenarioBatchResult valuationPriceScenarioBatchResult in detailedResults)
            {
               ValuationBatchResult result = new ValuationBatchResult();

               result.ValuationId = valuationPriceScenarioBatchResult.Id;

               result.Reg = valuationPriceScenarioBatchResult.VehicleReg;
               result.Mileage = valuationPriceScenarioBatchResult.Mileage;
               result.Make = valuationPriceScenarioBatchResult.Make;
               result.Model = valuationPriceScenarioBatchResult.Model;
               result.Derivative = valuationPriceScenarioBatchResult.Derivative;
               result.RetailerName = valuationPriceScenarioBatchResult.RetailerName;
               result.FuelType = valuationPriceScenarioBatchResult.FuelType;
               result.Vin = valuationPriceScenarioBatchResult.Vin;
               result.VehicleType = valuationPriceScenarioBatchResult.VehicleType;
               result.Trim = valuationPriceScenarioBatchResult.Trim;
               result.PriceScenario = valuationPriceScenarioBatchResult.PriceScenario;
               result.RegisteredDate = valuationPriceScenarioBatchResult.RegisteredDate;
               result.Reference1 = valuationPriceScenarioBatchResult.Reference1;
               result.Reference2 = valuationPriceScenarioBatchResult.Reference2;
               result.Reference3 = valuationPriceScenarioBatchResult.Reference3;
               result.SIV = valuationPriceScenarioBatchResult.SIV;
               result.StrategyPrice = valuationPriceScenarioBatchResult.StrategyPrice;
               result.RetailRating = valuationPriceScenarioBatchResult.RetailRating.HasValue ? (decimal)valuationPriceScenarioBatchResult.RetailRating : 0;
               result.CurrentPP = valuationPriceScenarioBatchResult.CurrentPP;
               result.ValuationAdjRetail = (decimal)valuationPriceScenarioBatchResult.ValuationAdjRetail;

               result.LowestPP = valuationPriceScenarioBatchResult.LowestPP;
               result.SecondLowestPP = valuationPriceScenarioBatchResult.SecondLowestPP;
               result.ThirdLowestPP = valuationPriceScenarioBatchResult.ThirdLowestPP;

               result.LowestPPMileage = valuationPriceScenarioBatchResult.LowestPPMileage;
               result.LowestPPRetailer = valuationPriceScenarioBatchResult.LowestPPRetailer;
               result.LowestPPVehicleReg = valuationPriceScenarioBatchResult.LowestPPVehicleReg;

               result.SecondLowestPPMileage = valuationPriceScenarioBatchResult.SecondLowestPPMileage;
               result.SecondLowestPPRetailer = valuationPriceScenarioBatchResult.SecondLowestPPRetailer;
               result.SecondLowestPPVehicleReg = valuationPriceScenarioBatchResult.SecondLowestPPVehicleReg;

               result.ThirdLowestPPMileage = valuationPriceScenarioBatchResult.ThirdLowestPPMileage;
               result.ThirdLowestPPRetailer = valuationPriceScenarioBatchResult.ThirdLowestPPRetailer;
               result.ThirdLowestPPVehicleReg = valuationPriceScenarioBatchResult.ThirdLowestPPVehicleReg;

               result.CapValuation = valuationPriceScenarioBatchResult.CAPValuation;
               result.CapNew = valuationPriceScenarioBatchResult.CapNew;
               result.CapRetail = valuationPriceScenarioBatchResult.CapRetail;
               result.CapAverage = valuationPriceScenarioBatchResult.CapAverage;
               result.CapBelow = valuationPriceScenarioBatchResult.CapBelow;

               result.D100 = valuationPriceScenarioBatchResult.D100;
               result.D99 = valuationPriceScenarioBatchResult.D99;
               result.D98 = valuationPriceScenarioBatchResult.D98;
               result.D97 = valuationPriceScenarioBatchResult.D97;
               result.D96 = valuationPriceScenarioBatchResult.D96;
               result.D95 = valuationPriceScenarioBatchResult.D95;
               result.D94 = valuationPriceScenarioBatchResult.D94;
               result.D93 = valuationPriceScenarioBatchResult.D93;
               result.D92 = valuationPriceScenarioBatchResult.D92;
               result.D91 = valuationPriceScenarioBatchResult.D91;
               result.D90 = valuationPriceScenarioBatchResult.D90;

               result.P100 = valuationPriceScenarioBatchResult.P100;
               result.P99 = valuationPriceScenarioBatchResult.P99;
               result.P98 = valuationPriceScenarioBatchResult.P98;
               result.P97 = valuationPriceScenarioBatchResult.P97;
               result.P96 = valuationPriceScenarioBatchResult.P96;
               result.P95 = valuationPriceScenarioBatchResult.P95;
               result.P94 = valuationPriceScenarioBatchResult.P94;
               result.P93 = valuationPriceScenarioBatchResult.P93;
               result.P92 = valuationPriceScenarioBatchResult.P92;
               result.P91 = valuationPriceScenarioBatchResult.P91;
               result.P90 = valuationPriceScenarioBatchResult.P90;
               result.P89 = valuationPriceScenarioBatchResult.P89;
               result.P88 = valuationPriceScenarioBatchResult.P88;
               result.P87 = valuationPriceScenarioBatchResult.P87;
               result.P86 = valuationPriceScenarioBatchResult.P86;
               result.P85 = valuationPriceScenarioBatchResult.P85;

               result.DaysToSellAtCurrentSelling = valuationPriceScenarioBatchResult.DaysToSellAtCurrentSelling;
               results.Add(result);
            }

            return results;
         }
         else
         {
            return (await valuationsDataAccess.GetValuationBatchResults(parms, userRetailerSiteIds));
         }

      }

      public async Task DeleteVehicleSpecBuildForAdvert(int advertId)
      {
         var valuationsDataAccess = new ValuationsDataAccess(_connectionString);
         await valuationsDataAccess.DeleteVehicleSpecBuildForAdvert(advertId);
      }

      public async Task<decimal?> GetFixedCostPrep(int dealerGroupId, VehicleInformation vehicle)
      {

         var valuationsDataAccess = new ValuationsDataAccess(_connectionString);
         return await valuationsDataAccess.GetFixedCostPrep(dealerGroupId, vehicle);
      }


      public async Task<List<PrepCostItem>> GetPrepCosts(string model)
      {

         var valuationsDataAccess = new ValuationsDataAccess(_connectionString);
         return (await valuationsDataAccess.GetPrepCosts(model)).ToList();


      }

      public async Task<IEnumerable<StockLevelAndCover>> GetStockLevelAndCover(string model, int retailerSiteId)
      {
         var valuationsDataAccess = new ValuationsDataAccess(_connectionString);
         return (await valuationsDataAccess.GetStockLevelAndCover(model, retailerSiteId));
      }


      private void UpdateVehicleValuationProperties(VehicleValuation vehicleValuation, ATNewVehicleGet individualVehicleResult)
      {
         vehicleValuation.HasBeenValued = true;

         vehicleValuation.BatteryCapacityKWH = individualVehicleResult.vehicle.batteryCapacityKWH;
         vehicleValuation.BatteryRangeMiles = individualVehicleResult.vehicle.batteryRangeMiles;
         vehicleValuation.BodyType = individualVehicleResult.vehicle.bodyType;
         vehicleValuation.Co2EmissionGPKM = individualVehicleResult.vehicle.co2EmissionGPKM;
         vehicleValuation.Colour = individualVehicleResult.vehicle.colour;
         vehicleValuation.SpecificColour = individualVehicleResult.vehicle.oem?.colour;
         vehicleValuation.Cylinders = individualVehicleResult.vehicle.cylinders;
         vehicleValuation.Derivative = individualVehicleResult.vehicle.derivative;
         vehicleValuation.DerivativeId = individualVehicleResult.vehicle.derivativeId;
         vehicleValuation.Doors = individualVehicleResult.vehicle.doors;
         vehicleValuation.Drivetrain = individualVehicleResult.vehicle.drivetrain;
         vehicleValuation.DriveType = individualVehicleResult.vehicle.driveType;
         vehicleValuation.EngineCapacityCC = individualVehicleResult.vehicle.engineCapacityCC;
         vehicleValuation.EnginePowerBHP = individualVehicleResult.vehicle.enginePowerBHP;
         vehicleValuation.FirstRegistered = individualVehicleResult.vehicle.firstRegistrationDate;
         vehicleValuation.FuelType = individualVehicleResult.vehicle.fuelType;
         vehicleValuation.Gears = individualVehicleResult.vehicle.gears;
         vehicleValuation.Generation = individualVehicleResult.vehicle.generation;
         vehicleValuation.Make = individualVehicleResult.vehicle.make;
         vehicleValuation.Model = individualVehicleResult.vehicle.model;
         vehicleValuation.Owners = individualVehicleResult.vehicle.owners;
         vehicleValuation.OwnershipCondition = individualVehicleResult.vehicle.ownershipCondition;
         vehicleValuation.Seats = individualVehicleResult.vehicle.seats;
         vehicleValuation.sector = individualVehicleResult.vehicle.sector;
         vehicleValuation.StartStop = individualVehicleResult.vehicle.startStop;
         vehicleValuation.TopSpeedMPH = individualVehicleResult.vehicle.topSpeedMPH;
         vehicleValuation.TransmissionType = individualVehicleResult.vehicle.transmissionType;
         vehicleValuation.Trim = individualVehicleResult.vehicle.trim;
         vehicleValuation.VehicleExciseDutyWithoutSupplementGBP = individualVehicleResult.vehicle.vehicleExciseDutyWithoutSupplementGBP;
         vehicleValuation.VehicleType = individualVehicleResult.vehicle.vehicleType;
         vehicleValuation.Vin = individualVehicleResult.vehicle.vin;
         vehicleValuation.ZeroToSixtyMPHSeconds = individualVehicleResult.vehicle.zeroToSixtyMPHSeconds;

      }

      public async Task<VehicleRecallStatus> GetVehicleRecallStatus(string reg)
      {
         try
         {
            //throw new Exception("Test");
            string baseURL = Startup.Configuration["DVSASettings:BaseURLForRecallApi"];
            string apiKey = Startup.Configuration["DVSASettings:ApiKey"];
            string tokenURL = Startup.Configuration["DVSASettings:TokenURL"];
            string clientId = Startup.Configuration["DVSASettings:ClientId"];
            string clientSecret = Startup.Configuration["DVSASettings:ClientSecret"];
            string scope = Startup.Configuration["DVSASettings:Scope"];
            var r = await dVSAApiClient.GetRecallInformation(reg, baseURL, apiKey, tokenURL, clientId, clientSecret, scope);
            return r;
         }
         catch (Exception ex)
         {
            Console.WriteLine($"Failed getting Vehicle Recall Status for {reg}");
            return VehicleRecallStatus.Unavailable;
         }
      }




      public async Task<DayToSellAndPriceIndicator> GetEstimatedDayToSellAndPriceIndicator(GetEstimatedDayToSellAndPriceIndicatorParams parms)
      {
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];

         GetAdvertPriceAdjustedDaysToSellParams daysToSellParams = new GetAdvertPriceAdjustedDaysToSellParams()
         {
            AutotraderBaseURL = baseURL,
            AdvertiserId = parms.AdvertiserIds[0],
            DerivativeId = parms.DerivativeId,
            FirstRegistrationDate = parms.FirstRegisteredDate,
            OdometerReadingMiles = parms.Mileage,
            Amount = parms.StrategyPrice,
            UseSpecificOptions = parms.VehicleHasOptionsSpecified,
            SpecificOptionNames = parms.VehicleAdvertPortalOptions,

            AdjustedValuation = parms.AdjustedValuation,
            AverageValuation = parms.AverageValuation
         };

         GetAdvertPriceAdjustedDaysToSellMultiSiteParams priceIndicatorParams = new GetAdvertPriceAdjustedDaysToSellMultiSiteParams()
         {
            AutotraderBaseURL = baseURL,
            AdvertiserIds = parms.AdvertiserIds,
            DerivativeId = parms.DerivativeId,
            FirstRegistrationDate = parms.FirstRegisteredDate,
            OdometerReadingMiles = parms.Mileage,
            Amount = parms.StrategyPrice,
            UseSpecificOptions = parms.VehicleHasOptionsSpecified,
            SpecificOptionNames = parms.VehicleAdvertPortalOptions,

            AdjustedValuation = parms.AdjustedValuation,
            AverageValuation = parms.AverageValuation
         };


         string _bearerToken = (await tokenClient.GetToken()).AccessToken;

         try
         {
            Task<List<LocationDaysToSellAndRetailRating>> taskPriceAdjustedDaysToSell = atMetricsClient.GetAdvertPriceAdjustedDaysToSellMultiSite(priceIndicatorParams, _bearerToken);  //DTS is different for different sites
            List<string> optionsForAdjustmentFactor = parms.VehicleHasOptionsSpecified ? parms.VehicleAdvertPortalOptions : null;
            var adjustmentFactor = await WorkoutAdjustmentFactor(parms.DerivativeId, parms.FirstRegisteredDate, parms.Mileage, optionsForAdjustmentFactor, parms.AdjustedValuation);
            var taskPriceIndicator = atValuationsClient.GetAdvertPriceIndicator(daysToSellParams, _bearerToken, adjustmentFactor); //price indicator is same everywhere
            await Task.WhenAll(taskPriceAdjustedDaysToSell, taskPriceIndicator);
            List<LocationDaysToSellAndRetailRating> priceAdjDaysToSellRes = taskPriceAdjustedDaysToSell.Result;
            foreach (var item in priceAdjDaysToSellRes)
            {
               item.DaysToSell = Math.Ceiling(item.DaysToSell);
            }
            string priceIndicatorRes = taskPriceIndicator.Result ?? "NOANALYSIS";

            return new DayToSellAndPriceIndicator
            {
               DaysToSellResults = priceAdjDaysToSellRes ?? null,
               PriceIndicator = PriceStrategyClassifierService.ProvidePriceIndicatorName(priceIndicatorRes)
            };
         }
         catch (Exception ex)
         {
            Console.WriteLine(ex.Message);
            //throw new Exception("Failed to get estimated days to sell and price indicator");

            return new DayToSellAndPriceIndicator()
            {
               PriceIndicator = null,
               DaysToSellResults = new List<LocationDaysToSellAndRetailRating>()
            };
         }


      }

      public async Task<decimal> WorkoutAdjustmentFactor(string derivativeId, DateTime? firstRegisteredDate, int odometerReading, List<string> vehicleAdvertPortalOptions, decimal adjustedValuation)
      {
         if (firstRegisteredDate == null)
         {
            return 1;
         }
         //firstly get a valuation to see how far 'wrong' the valuation will come back if the spec has not been confirmed
         var initialParams = new GetValuationPriceSetParams(derivativeId, (DateTime)firstRegisteredDate, odometerReading, "Excellent", vehicleAdvertPortalOptions);
         ValuationPriceSet valuationPriceSet = await GetNewValuationForAdvert(initialParams);
         decimal scenarioAdjust = adjustedValuation == 0 ? 1 : (decimal)valuationPriceSet.RetailThisVehicle / adjustedValuation;
         return scenarioAdjust;
      }



      private static async Task<TrendValuationRoot> GetTrendedValuations(
          AutoTraderValuationsClient atValuationsClient,
          int mileage,
          string derivativeId,
          DateTime firstRegistration,
          int retailerId,
          string baseURL,
          string apiKey,
          string apiSecret,
          List<string>? features,
          DateTime startDate,
          DateTime endDate,
          string frequency,
          TokenResponse token
          )
      {




         GetFutureValuationsTrendsParams futureValuationsTrendsParams = new GetFutureValuationsTrendsParams()
         {
            apiKey = apiKey,
            apiSecret = apiSecret,
            baseUrl = baseURL,
            retailerId = retailerId,
            payload = new FutureValuationTrendsPayload()
            {
               vehicle = new FutureValuationTrendsPayload.Vehicle()
               {
                  derivativeId = derivativeId,
                  firstRegistrationDate = firstRegistration.ToString("yyyy-MM-dd")
               },
               conditionRating = "EXCELLENT",
               valuations = new FutureValuationTrendsPayload.Valuations()
               {
                  markets = new List<string>() { "retail", "private" },
                  frequency = frequency,
                  start = new FutureValuationTrendsPayload.FrequencyDetails()
                  {
                     date = startDate.ToString("yyyy-MM-dd"),
                     odometerReadingMiles = mileage
                  },
                  end = new FutureValuationTrendsPayload.FrequencyDetails()
                  {
                     date = endDate.ToString("yyyy-MM-dd"),
                     odometerReadingMiles = mileage
                  }
               }
            }
         };

         if (features != null)
         {
            var payloadFeatures = new List<FutureValuationTrendsPayload.Feature>();
            foreach (var feature in features)
            {
               payloadFeatures.Add(new FutureValuationTrendsPayload.Feature() { name = feature });
            }
            futureValuationsTrendsParams.payload.features = payloadFeatures;
         }


         return await atValuationsClient.GetFutureValuationTrends(futureValuationsTrendsParams, token);
      }


      public async Task<NewVehicleValuationModal> GetNewValuationModal(GetNewVehicleModalParams parms)
      {
         var autoTraderTaxonomyDataAccess = new AutoTraderTaxonomyDataAccess(_httpClientFactory, apiKey, apiSecret, baseURL);
         //var autoTraderApiClient = new AutoTraderApiClient(httpClient, apiKey, apiSecret, baseURL);
         var tokenResponse = await tokenClient.GetToken();
         var userRetailerSiteRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");
         var userDealerGroupName = userService.GetUserDealerGroupName();
         var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var retailerSites = await retailerSitesDataAccess.GetRetailerSites(userDealerGroupName);
         var userRetailer = retailerSites.First(x => x.RetailerId == userRetailerSiteRetailerId);
         var vehicleInformation = new VehicleInformation();

         //TODO - get the technical details
         //use this link:  https://api.autotrader.co.uk/taxonomy/derivatives/9178f90f12d842be8eb18ea8c10212fc?advertiserId=133
         //need to add method in taxonomyDA.    And Viewmodel.   Return it with the result and display it in front end
         var derivative = await autoTraderTaxonomyDataAccess.GetDerivative(tokenResponse, userRetailerSiteRetailerId, parms.derivativeId);


         //TODO - use the body type, trim, make, model etc. to hit the AutoTraderApiClient GetCompetitorNew method to get all the competitors for this vehicle
         //Then can use the single vehicle valuation stuff in the new vehicle valuation modal

         Task<CompetitorSummary> competitorCheckResultTask = Task.FromResult<CompetitorSummary>(null);
         if (!derivative.derivativeId.IsNullOrEmpty())
         {
            //Do a competitor check
            //var advertPlate = WorkoutPlateYear(vehicleInformation.FirstRegistered);
            //var minMaxPlateRange = AutoPriceMinMaxPlateService.GetMinMaxPlate(advertPlate, advertPlate, userRetailer.CompetitorPlateRange);
            int radius = 1000;

            var atVehicleInformation = new ATNewVehicleGet()
            {
               vehicle = new ATNewVehicleGet_Vehicle()
               {
                  firstRegistrationDate = DateTime.Now.AddYears(-1),// TODO: set correct date
                  make = derivative.make,
                  model = derivative.model,
                  transmissionType = derivative.transmissionType,
                  fuelType = derivative.fuelType,
                  bodyType = derivative.bodyType,
                  drivetrain = derivative.drivetrain,
                  doors = derivative.doors,
                  trim = derivative.trim,
                  badgeEngineSizeLitres = derivative.badgeEngineSizeLitres,
                  enginePowerBHP = derivative.enginePowerBHP,

                  vehicleType = derivative.vehicleType,
                  sector = derivative.sector,
                  generation = derivative.generation,
                  colour = derivative.oem?.colour,
                  wheelbaseType = derivative.wheelbaseType,
                  seats = derivative.seats,
                  cylinders = derivative.cylinders,
                  co2EmissionGPKM = derivative.co2EmissionGPKM,
                  emissionClass = derivative.emissionClass,
                  engineCapacityCC = derivative.engineCapacityCC,
                  fuelEconomyNEDCCombinedMPG = derivative.fuelEconomyNEDCCombinedMPG,
                  insuranceGroup = derivative.insuranceGroup,
                  insuranceSecurityCode = derivative.insuranceSecurityCode,

               },
               features = new List<ATNewVehicleGet_Feature>(),
               history = new ATVehicleGet_History()
               {
                  keeperChanges = new List<KeeperChange>(),
                  v5cs = new List<V5CIssue>()
               },
               vehicleMetrics = new ATNewVehicleGet_vehicleMetrics()
               {
                  retail = new ATNewVehicleGet_vehicleMetric()
               },
               motTests = new List<ATVehicleGet_Mots>()
            };


            vehicleInformation = new VehicleInformation(atVehicleInformation, VehicleRecallStatus.Unavailable, radius, null);
            CompetitorSearchParams searchparams = new CompetitorSearchParams(atVehicleInformation, userRetailer, tokenResponse, radius);
            searchparams.selfRegToExclude = "ABC123";
            OurVehicleParams ourVehicleParams = new OurVehicleParams();
            competitorCheckResultTask = GetValuationModalCompetitorAnalysis(searchparams, ourVehicleParams, userDealerGroupName);
         }



         var featuresTask = autoTraderTaxonomyDataAccess.GetFeatureItems(tokenResponse, userRetailerSiteRetailerId, parms.derivativeId);
         var pricesTask = autoTraderTaxonomyDataAccess.GetPriceItems(tokenResponse, userRetailerSiteRetailerId, parms.derivativeId);
         await Task.WhenAll(featuresTask, pricesTask, competitorCheckResultTask);

         var currentPriceItem = pricesTask.Result.FirstOrDefault(x => x.effectiveTo == null);
         decimal currentPrice = currentPriceItem.basicPrice + currentPriceItem.deliveryPrice;
         List<FutureValuationPoint> future6k = await GenerateFutureValuesTask(parms.derivativeId, 6000, atFutureValsClient, userRetailerSiteRetailerId, currentPrice,null);
         List<FutureValuationPoint> future10k = await GenerateFutureValuesTask(parms.derivativeId, 10000, atFutureValsClient, userRetailerSiteRetailerId, currentPrice,null);
         List<FutureValuationPoint> future20k = await GenerateFutureValuesTask(parms.derivativeId, 20000, atFutureValsClient, userRetailerSiteRetailerId, currentPrice,null);
         List<FutureValuationPoint> future30k = await GenerateFutureValuesTask(parms.derivativeId, 30000, atFutureValsClient, userRetailerSiteRetailerId, currentPrice,null);

         var competitorCheckResult = competitorCheckResultTask.Result;

         return new NewVehicleValuationModal()
         {
            Features = featuresTask.Result,
            Prices = pricesTask.Result,
            future6k = future6k,
            future10k = future10k,
            future20k = future20k,
            future30k = future30k,
            CompetitorCheckResult = competitorCheckResult,
            VehicleInformation = vehicleInformation,
         };

      }

      private static Task<List<FutureValuationPoint>> GenerateFutureValuesTask(string derivativeId, int annualMileage, AutoTraderFutureValuationsClient atFutureValsClient, 
         int userRetailerSiteRetailerId, decimal currentPrice, ILog? logger)
      {
         List<FutureValuationDateAndMileage> futureDates = new List<FutureValuationDateAndMileage>();
         int monthlyMileage = annualMileage / 12;
         DateTime latestDate = DateTime.Now;
         int latestMileage = 0;
         for (int i = 0; i < 36; i++)
         {
            latestDate = latestDate.AddMonths(1);
            latestMileage += monthlyMileage;
            futureDates.Add(new FutureValuationDateAndMileage() { date = latestDate, odometerReading = latestMileage });
         }

         GetFutureValuationsNewParams futureValsParams = new GetFutureValuationsNewParams()
         {
            derivativeId = derivativeId,
            retailerId = userRetailerSiteRetailerId,
            currentValuation = currentPrice,
            futureValuationPoints = futureDates
         };
         var futureValsTask = atFutureValsClient.GetFutureValuationForNewVehicle(futureValsParams, logger);
         return futureValsTask;
      }

      public async Task<ValuationModalNew> GetValuationModalNew(GetValuationModalNewParams parms)
      {
         //here we will assemble all that we need for the new modal

         //----------------------------------------------
         // 1. Find out about the user and RetailerSites
         //----------------------------------------------
         int userId = userService.GetUserId();
         var userDealerGroupName = userService.GetUserDealerGroupName();
         var userDealerGroupNameId = userService.GetDealerGroupNameId();
         var userRetailerSiteId = userService.GetIdFromAccessToken("CurrentRetailerSiteId");// await userService.GetUserRetailerSiteId(userId, userDealerGroupName);
         var userRetailerSiteRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");// await userService.GetUserRetailerSiteId(userId, userDealerGroupName);
         var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var userRetailerSiteIds = userService.GetUserRetailerSiteIds();
         var retailerSitesTask = retailerSitesDataAccess.GetRetailerSites(userDealerGroupName);
         var retailerSitesWithStrategiesTask = retailerSitesDataAccess.GetRetailerSitesWithBuyingStrategies(DateTime.Now, userDealerGroupName);

         var dealerSiteWarrantyFeesTask = retailerSitesDataAccess.GetDealerGroupFixedCostWarranties(userDealerGroupNameId);


         //----------------------------------------------
         // 2. Get a token
         //----------------------------------------------
         string apiBaseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         var tokenResponseTask = tokenClient.GetToken();



         //----------------------------------------------
         //Execute first set of tasks
         await Task.WhenAll(retailerSitesTask, tokenResponseTask, retailerSitesWithStrategiesTask, dealerSiteWarrantyFeesTask);

         List<RetailerSite> retailerSites = retailerSitesTask.Result;
         List<FixedCostWarranty> dealerSiteWarrantyFees = dealerSiteWarrantyFeesTask.Result;

         //1. Result
         var userRetailer = retailerSites.First(x => x.Id == userRetailerSiteId);// await retailerSitesDataAccess.GetRetailerSite(userDealerGroupName, userRetailerSiteId);
         var retailerSitesByName = retailerSites.ToDictionary(x => x.Name);
         VehicleLocationStrategyPriceBuild userSiteLocationStrategyPrice = null;

         var retailerSitesWithStrategies = retailerSitesWithStrategiesTask.Result;

         //2. Result
         var tokenResponse = tokenResponseTask.Result;
         //----------------------------------------------



         //----------------------------------------------
         // 3. Hit AT and obtain the vehicle details including vehicle history, plus the DVLA o/s recall point
         //----------------------------------------------
         ATNewVehicleGet atVehicleInformation = await atVehiclesClient.GetIndividualVehicle(parms.vehicleReg, 100, userRetailerSiteRetailerId, tokenResponse.AccessToken, apiBaseURL);
         VehicleInformation vehicleInformation = new VehicleInformation(atVehicleInformation, VehicleRecallStatus.Unavailable, parms.mileage, parms.chosenOptions);

         // If a colour was specified in the parameters, override the one from AT
         if (!string.IsNullOrEmpty(parms.colour))
         {
            vehicleInformation.SpecificColour = parms.colour;
            if (atVehicleInformation.vehicle.oem != null)
            {
               atVehicleInformation.vehicle.oem.colour = parms.colour;
            }
            else
            {
               atVehicleInformation.vehicle.oem = new ATNewVehicleGet_Oem() { colour = parms.colour };
            }
         }




         //----------------------------------------------
         // 4. Get the valuations
         //----------------------------------------------
         var optionsToUse = parms.chosenOptions ?? vehicleInformation.ChosenOptions ?? null;//  if chosen options use that else FFO else none
         var valuationPriceSetParams = new GetValuationPriceSetParams(
             vehicleInformation.DerivativeId,
             vehicleInformation.FirstRegistered,
             parms.mileage,
             parms.condition,
             optionsToUse);
         var valuationPriceSetTask = GetNewValuationForAdvert(valuationPriceSetParams);


         //----------------------------------------------
         // 5. Get the retail rating and days to sell at each location in the dealergroup
         //----------------------------------------------
         bool useLongLatApproach = userDealerGroupName == DealerGroupName.Enterprise;
         Task<VehicleMetricProcessedResponse> locationResultsTask = WorkoutRetailRatingAndDaysToSellPerLocation(parms.mileage, userRetailerSiteRetailerId, retailerSites, atVehicleInformation, useLongLatApproach);

         //----------------------------------------------
         //Execute second set of tasks
         await Task.WhenAll(valuationPriceSetTask, locationResultsTask);
         ValuationPriceSet valuationPriceSet = valuationPriceSetTask.Result;
         VehicleMetricProcessedResponse locationResults = locationResultsTask.Result;
         //----------------------------------------------


         //----------------------------------------------
         // 6. Run a future and historic valuation using the trended valuations endpoint
         //----------------------------------------------
         DateTime start = DateTime.Now.AddMonths(-3);
         DateTime end = DateTime.Now.AddMonths(3);
         var valuationsTask = GetTrendedValuations(
             atValuationsClient,
             parms.mileage,
             vehicleInformation.DerivativeId,
             vehicleInformation.FirstRegistered,
             userRetailerSiteRetailerId,
             apiBaseURL,
             apiKey,
             apiSecret,
             optionsToUse,
             start,
             end,
             "week", tokenResponse);
         VehicleMetricProcessedLocation userSiteResult = locationResults.locations.FirstOrDefault(x => x.RetailerSiteId == userRetailerSiteId);
         int daysToSell = userSiteResult.daysToSell;// (int)Math.Ceiling(userSiteResult?.daysToSell.value ?? 0);

         //----------------------------------------------
         // 7. Workout the strategy build up for each location
         //----------------------------------------------

         //now go ahead and apply the rule
         var valuationToUse = valuationPriceSet.RetailThisVehicle ?? 0;
         int priceWeWillAdvertiseAtInUserSite = 0;

         var taskList = new List<Task>();

         List<VehicleLocationStrategyPriceBuild> locationStrategyPrices = new List<VehicleLocationStrategyPriceBuild>();

         if (valuationToUse != 0)
         {
            foreach (VehicleMetricProcessedLocation locationResult in locationResults.locations)
            {
               //int retailerSiteRetailerId = int.Parse(locationResult.advertiserId);
               int retailerSiteId = locationResult.RetailerSiteId;
               var rsWithRuleSet = retailerSitesWithStrategies.FirstOrDefault(x => x.Id == retailerSiteId);// ruleSetLookup.TryGetValue(retailerSiteRetailerId, out ruleSet);
               if (rsWithRuleSet == null || rsWithRuleSet.BuyingStrategySelectionRuleSet == null)
               {
                  continue;
               }


               var retailerSite = retailerSitesByName[rsWithRuleSet.Name];
               RetailerSite userRetailerSite = retailerSites.Find(x => x.Id == userRetailerSiteId);
               VehicleLocationStrategyPriceBuild thisLocationBuildUp = new VehicleLocationStrategyPriceBuild(locationResult, retailerSite, userRetailerSite.Site.GeoX, userRetailerSite.Site.GeoY);

               bool isOnBrand = retailerSite.Makes.ToLower().Contains(vehicleInformation.Make.ToLower());
               AdvertParamsForStrategyCalculator calcParams = new AdvertParamsForStrategyCalculator(
                   atVehicleInformation,
                   thisLocationBuildUp.RetailRating,
                   parms.mileage,
                   valuationPriceSet.RetailThisVehicle ?? 0,
                   isOnBrand,
                   thisLocationBuildUp.DaysToSell,
                   retailerSite,
                   tokenResponse
                   );

               await ApplyStrategyService.ApplyRuleSet(
                   DateTime.Now,
                   calcParams,
                   rsWithRuleSet.BuyingStrategySelectionRuleSet,
                   retailerSite,
                   retailerSite.Site,
                   thisLocationBuildUp.BuildUpItems,
                   tokenResponse,

                   atCompetitorClient,
                   apiBaseURL,
                   null, null, atFutureValsClient, atMetricsClient);
               thisLocationBuildUp.StrategyPrice = (int)Math.Round(calcParams.currentStrategyPrice, 0, MidpointRounding.AwayFromZero);
               //if(thisLocationBuildUp.RetailerSite.Id == userRetailerSiteId) { thisLocationBuildUp.RetailerSite.Name = thisLocationBuildUp.RetailerSite.Name + "( user home site)"; }
               locationStrategyPrices.Add(thisLocationBuildUp);
            }


            locationStrategyPrices = locationStrategyPrices.OrderByDescending(x => x.StrategyPrice).ToList();
            userSiteLocationStrategyPrice = locationStrategyPrices.FirstOrDefault(x => x.RetailerSite.Id == userRetailerSiteId);
            priceWeWillAdvertiseAtInUserSite = (userSiteLocationStrategyPrice != null) ? userSiteLocationStrategyPrice.StrategyPrice : 0;

            // Get fixed prep costs based on age
            var fixedCostPrep = await GetFixedCostPrep(userDealerGroupNameId, vehicleInformation);


            locationStrategyPrices
                .Where(price => price.RetailerSite.TargetAdditionalMech == 0)
                .ToList()
                .ForEach(price =>
                {
                   price.RetailerSite.TargetAdditionalMech = (fixedCostPrep ?? 0);
                   price.RetailerSite.TargetWarrantyFee = WarrantyCostService.GetFixedCostWarrantyFee(
                      price.RetailerSite.DealerGroup_Id,
                      price.RetailerSite.Id,
                      vehicleInformation.FirstRegistered,
                      vehicleInformation.Make,
                      vehicleInformation.Model,
                      dealerSiteWarrantyFees) ?? 0;
                });
         }


         //----------------------------------------------
         // 8. Do a competitor check for the user site
         //----------------------------------------------

         Task<CompetitorSummary> competitorCheckResultTask = Task.FromResult<CompetitorSummary>(null);
         if (valuationToUse != 0)
         {
            //Do a competitor check
            int radius = 1000;


            CompetitorSearchParams searchparams = new CompetitorSearchParams(atVehicleInformation, userRetailer, tokenResponse, radius);
            OurVehicleParams ourVehicleParams = new OurVehicleParams()
            {
               AdvertisedPrice = priceWeWillAdvertiseAtInUserSite,
               Valuation = valuationPriceSet.RetailThisVehicle ?? 0,
               VehicleReg = parms.vehicleReg,
               Mileage = parms.mileage,
               FirstRegisteredYear = vehicleInformation.FirstRegistered.Year
            };
            competitorCheckResultTask = GetValuationModalCompetitorAnalysis(searchparams, ourVehicleParams, userDealerGroupName);
            taskList.Add(competitorCheckResultTask);
         }


         //----------------------------------------------
         // 8.5. Hit valuations endpoint again this time with strategy price as selling price
         //----------------------------------------------
         valuationPriceSetParams.SellingPrice = priceWeWillAdvertiseAtInUserSite;
         var valuationPriceSet2Task = GetNewValuationForAdvert(valuationPriceSetParams);
         taskList.Add(valuationPriceSet2Task);


         //8.6 Get DaysToSell again based on strategy price // Calling the endpoint to used by the modal
         GetAdvertPriceAdjustedDaysToSellMultiSiteParams priceIndicatorParams = new GetAdvertPriceAdjustedDaysToSellMultiSiteParams()
         {
            AutotraderBaseURL = apiBaseURL,
            AdvertiserIds = retailerSites.Select(x => x.RetailerId).ToList(),
            DerivativeId = atVehicleInformation.vehicle.derivativeId,
            FirstRegistrationDate = atVehicleInformation.vehicle.firstRegistrationDate,
            OdometerReadingMiles = parms.mileage,
            Amount = priceWeWillAdvertiseAtInUserSite,
            UseSpecificOptions = (parms.chosenOptions != null) ? true : false,
            SpecificOptionNames = parms.chosenOptions,

            AdjustedValuation = valuationPriceSet.RetailThisVehicle.HasValue ? valuationPriceSet.RetailThisVehicle.Value : 0,
            AverageValuation = valuationPriceSet.RetailAverageSpec.HasValue ? valuationPriceSet.RetailAverageSpec.Value : 0,
         };


         List<LocationDaysToSellAndRetailRating> priceAdjustedDaysToSells = await atMetricsClient.GetAdvertPriceAdjustedDaysToSellMultiSite(priceIndicatorParams, tokenResponse.AccessToken);  //DTS is different for different sites
         foreach (var locationStrategyPrice in locationStrategyPrices)
         {
            if (priceAdjustedDaysToSells != null && priceAdjustedDaysToSells.Where(p => p.RetailerSiteRetailerId == locationStrategyPrice.RetailerSite.RetailerId).Any())
            {
               var newDaysToSell = priceAdjustedDaysToSells.Where(p => p.RetailerSiteRetailerId == locationStrategyPrice.RetailerSite.RetailerId)?.FirstOrDefault().DaysToSell;
               locationStrategyPrice.DaysToSell = newDaysToSell.HasValue ? Convert.ToInt32(Math.Ceiling(newDaysToSell.Value)) : 0;
            }
         }


         //----------------------------------------------
         // 9. Find recently sold of same model
         //----------------------------------------------
         GetLeavingVehicleItemsParams leavingParams = new GetLeavingVehicleItemsParams()
         {
            ChosenRetailerSiteIds = userRetailerSiteIds.ToList(),
            StartDate = DateTime.Now.AddMonths(-3),
            EndDate = DateTime.Now,
            IncludeNewVehicles = false,
            IncludeUsedVehicles = true
         };

         Task<IEnumerable<LeavingVehicleItem>> recentlySoldThisModelTask = leavingPricesDataAccess.GetLeavingVehicleItemsForModel(leavingParams, vehicleInformation.Model, userRetailerSiteIds);
         taskList.Add(recentlySoldThisModelTask);

         //----------------------------------------------
         // 10. Find what current stock we have (same model adverts)
         //----------------------------------------------
         Task<IEnumerable<SameModelAdvert>> sameModelAdvertsTask = vehicleAdvertsDataAccess.GetSameModelAdverts(vehicleInformation.Model, userDealerGroupName, parms.includeUnpublishedAds);
         taskList.Add(sameModelAdvertsTask);

         //----------------------------------------------
         // 11. Get prep costs for this model
         //----------------------------------------------
         Task<List<PrepCostItem>> prepCostItemsTask = GetPrepCosts(vehicleInformation.Model);
         taskList.Add(prepCostItemsTask);

         //----------------------------------------------
         // 11.2 Get recall status
         //----------------------------------------------
         Task<VehicleRecallStatus> recallStatusTask = GetVehicleRecallStatus(parms.vehicleReg);
         taskList.Add(recallStatusTask);

         //----------------------------------------------
         // 11.5 Get Spec options
         //----------------------------------------------
         GetVehicleSpecOptionsParams specOptionsParams = new GetVehicleSpecOptionsParams()
         {
            BatchId = null,
            FirstRegisteredDate = vehicleInformation.FirstRegistered,
            DerivativeId = vehicleInformation.DerivativeId
         };

         var specOptionsTask = GetVehicleSpecOptions(specOptionsParams);
         taskList.Add(specOptionsTask);

         //13. Do we have any TradePrice adjustments
         var tradePriceSettingTask = _valuationsDataAccess.GetTradePriceSettingsForRetailerSite(userRetailerSiteId);
         taskList.Add(tradePriceSettingTask);

         //----------------------------------------------
         // 12. If we have a valuationId, we can retrieve that
         //----------------------------------------------

         Task<VehicleValuationWithStrategyPrice> valuationTask = null;

         if (parms.vehicleValuationId != null && parms.vehicleValuationId != 0)
         {
            valuationTask = _valuationsDataAccess.LoadVehicleValuation((int)parms.vehicleValuationId, userDealerGroupName);
            taskList.Add(valuationTask);

         }

         await Task.WhenAll(taskList);

         //6 Trended Vals Result
         var valuations = valuationsTask.Result;
         var trendedVals = new TrendedValuationCollection(valuations, daysToSell);

         //8 Competitors Result
         var competitorCheckResult = competitorCheckResultTask.Result;

         //8.5 ValuationSet Result
         valuationPriceSet = valuationPriceSet2Task.Result;

         //9.Recent sold Result
         var recentlySoldThisModel = (recentlySoldThisModelTask.Result).ToList();

         //10.Same model adverts Result
         List<SameModelAdvert> sameModelAdverts = (sameModelAdvertsTask.Result).ToList();
         SameModelAdvertSummary sameModelSummary = new SameModelAdvertSummary()
         {
            SameModelAdverts = sameModelAdverts,
            AverageDaysListed = 0,
            AverageDaysToSell = 0,
            AveragePricePosition = 0,
            AverageProfit = 0,
         };
         foreach (var item in sameModelAdverts)
         {
            sameModelSummary.AverageDaysListed += item.DaysListed;
            sameModelSummary.AverageDaysToSell += item.DaysToSell;
            sameModelSummary.AveragePricePosition += item.PricePosition;
            sameModelSummary.AverageProfit += item.Profit;
         }
         if (sameModelAdverts.Count > 0)
         {
            sameModelSummary.AverageDaysListed = sameModelSummary.AverageDaysListed / sameModelAdverts.Count;
            sameModelSummary.AverageDaysToSell = sameModelSummary.AverageDaysToSell / sameModelAdverts.Count;
            sameModelSummary.AveragePricePosition = sameModelSummary.AveragePricePosition / sameModelAdverts.Count;
            sameModelSummary.AverageProfit = sameModelSummary.AverageProfit / sameModelAdverts.Count;
         }

         List<StockLevelAndCover> stockLevelAndCovers = new List<StockLevelAndCover>();

         int totalStock = 0;
         int totalSales = 0;

         foreach (var rs in retailerSites)
         {
            var stock = sameModelAdverts.Where(x => x.RetailerId == rs.Id).ToList();

            totalStock += stock.Count();
            var sales = recentlySoldThisModel.Where(x => x.RetailerId == rs.Id).ToList();
            totalSales += sales.Count();
            stockLevelAndCovers.Add(new StockLevelAndCover(stock.Count(), sales.Count(), rs.Name, false, 0));
         }

         int totalSalesPerMonth = stockLevelAndCovers.Sum(r => r.SalesPerMonth);

         StockLevelAndCover totalRow = new StockLevelAndCover(totalStock, totalSales, "Total", true, totalSalesPerMonth);
         stockLevelAndCovers.Add(totalRow);

         //11. Prep costs Result
         var prepCostItems = (prepCostItemsTask.Result);

         //11.2 Recall status Result
         var recallStatus = recallStatusTask.Result;
         vehicleInformation.RecallStatus = recallStatus;


         //11.5 Spec options Result
         List<VehicleSpecOption> specOptions = specOptionsTask.Result;

         //12.Valuation Result
         VehicleValuationWithStrategyPrice valuation = null;
         if (valuationTask != null)
         {
            valuation = valuationTask.Result;
         }

         var tradePriceSetting = tradePriceSettingTask.Result;

         ValuationModalNew result = new ValuationModalNew()
         {
            VehicleInformation = vehicleInformation,
            TrendedValuations = trendedVals,
            ValuationPriceSet = valuationPriceSet,
            LocationStrategyPrices = locationStrategyPrices,
            CompetitorCheckResult = competitorCheckResult,
            RecentlySoldThisModel = recentlySoldThisModel,
            SameModelSummary = sameModelSummary,
            PrepCostItems = prepCostItems,
            Valuation = valuation,
            StockLevelAndCover = stockLevelAndCovers,
            SpecOptions = specOptions,
            TradePriceSetting = tradePriceSetting
         };


         return result;
      }

      private async Task<VehicleMetricProcessedResponse> WorkoutRetailRatingAndDaysToSellPerLocation(int mileage, int userRetailerSiteRetailerId, List<RetailerSite> retailerSites, ATNewVehicleGet atVehicleInformation, bool useLongLatApproach)
      {

         List<VehicleMetricLocationParams> locations = new List<VehicleMetricLocationParams>() { };
         foreach (var rs in retailerSites)
         {
            var location = new VehicleMetricLocationParams(rs, useLongLatApproach);

            locations.Add(location);
         }
         VehicleMetricParams newParms = new VehicleMetricParams()
         {
            vehicleAdvertSnapshotId = 0,
            advertiserId = userRetailerSiteRetailerId.ToString(),
            derivativeId = atVehicleInformation.vehicle.derivativeId,
            firstRegistrationDate = atVehicleInformation.vehicle.firstRegistrationDate.ToString("yyyy-MM-dd"),
            odometerReadingMiles = mileage,
            locations = locations
         };
         List<VehicleMetricParams> listParms = new List<VehicleMetricParams>() { newParms };
         var token = await tokenClient.GetToken();
         List<VehicleMetricProcessedResponse> locationResults = await atMetricsClient.GetAdvertsWithRetailRatingAtMultipleLocationsNEW(listParms, Startup.Configuration["AutotraderSettings:BaseURL"], token);
         return locationResults.First();
      }

      public async Task<CompetitorSummary> GetValuationModalCompetitorAnalysis(CompetitorSearchParams parms, OurVehicleParams ourVehicleParams, DealerGroupName dealerGroup)
      {
         var userRetailerName = userService.GetUserRetailerSiteRetailerName();
         var userRetailerSiteId = userService.GetIdFromAccessToken("CurrentRetailerSiteId");
         string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         string token = (await tokenClient.GetToken()).AccessToken;

         var userPostcode = userService.GetUserRetailerSitePostcode();
         var userRetailerSiteRetailerId = userService.GetRetailerSiteRetailerId();
         var userRetailerSites = await userService.GetUserRetailSites(dealerGroup);
         var tradePriceSettings = await _valuationsDataAccess.GetTradePriceSettings((int)dealerGroup);

         foreach (var item in userRetailerSites)
         {
            item.TradePriceSetting = tradePriceSettings.FirstOrDefault(x => x.RetailerSiteId == item.Id);
         }

         if (parms.selfRegToExclude == string.Empty)
         {
            parms.selfRegToExclude = "ABC123";
         }

         VehicleListing competitors = await atCompetitorClient.GetCompetitorNew(parms, null);


         var geoX = userService.GetUserSiteGeoX();
         var geoY = userService.GetUserSiteGeoY();
         int plateRange = 0;

         //only required if there are no competitors
         if (competitors.results == null)
         {
            plateRange = userRetailerSites.First(x => x.Id == userRetailerSiteRetailerId).CompetitorPlateRange;
         }

         bool hasOurVehicle = !ourVehicleParams.VehicleReg.IsNullOrEmpty();

         CompetitorSummary competitorSummary = new CompetitorSummary(competitors, geoY, geoX, ourVehicleParams.AdvertisedPrice, ourVehicleParams.Valuation, parms.radius, null, hasOurVehicle);

         if (hasOurVehicle)
         {
            var tradeBlob = new CompetitorVehicle(userRetailerName, ourVehicleParams.VehicleReg, ourVehicleParams.Mileage, ourVehicleParams.FirstRegisteredYear, ourVehicleParams.AdvertisedPrice, competitorSummary.PricePosition, null);

            // Get the default retailsite for the current user
            var ourRetailSite = userRetailerSites.Where(x => x.Id == userRetailerSiteId).FirstOrDefault();

            if (ourRetailSite?.TradePriceSetting != null)
            {
               var tradePriceSetting = ourRetailSite.TradePriceSetting;

               if (tradePriceSetting.IsTradePricing && tradePriceSetting.MarginPercentage > 0)
               {
                  tradeBlob.AdvertisedPrice = (int)(tradeBlob.AdvertisedPrice / tradePriceSetting.MarginPercentage);
                  tradeBlob.AdvertisedPrice += tradePriceSetting.MarginAmount;
                  tradeBlob.IsTradeAdjusted = true;
                  tradeBlob.PricePosition = (decimal) tradeBlob.AdvertisedPrice / ourVehicleParams.Valuation;
                  tradeBlob.IsOurVehicle = true;
               }
            }

            // This is where we adjust the trade pricing
            competitorSummary.CompetitorVehicles.Add(tradeBlob);
         }

         return competitorSummary;
      }



      //private (NumberPlateYear min, NumberPlateYear max) GetMinMaxPlateYear(DateTime firstRegisteredDate)
      //{

      //   List<NumberPlateYear> numberPlateYear = new List<NumberPlateYear>();
      //   DateTime modalItemRegisteredDate = Convert.ToDateTime(firstRegisteredDate);
      //   int month = modalItemRegisteredDate.Month;
      //   int year = modalItemRegisteredDate.Year - 2;

      //   string shortYear;

      //   if (month < 9)
      //   {
      //      shortYear = year.ToString().Substring(2);
      //   }
      //   else
      //   {
      //      if (year < 2010 && year > 2000) { shortYear = "5" + year.ToString().Substring(3); }
      //      if (year < 2020 && year > 2010) { shortYear = "6" + year.ToString().Substring(3); }
      //      if (year < 2030 && year > 2020) { shortYear = "7" + year.ToString().Substring(3); }
      //   }
      //   int maxYear = DateTime.Now.Year;

      //   for (int i = 0; i < maxYear - year; i++)
      //   {
      //      int thisYear = year + i;
      //      string thisPlate = "";

      //      if (thisYear < 2010 && thisYear >= 2000) { thisPlate = "5" + thisYear.ToString().Substring(3); }
      //      if (thisYear < 2020 && thisYear >= 2010) { thisPlate = "6" + thisYear.ToString().Substring(3); }
      //      if (thisYear < 2030 && thisYear >= 2020) { thisPlate = "7" + thisYear.ToString().Substring(3); }

      //      numberPlateYear.Add(new NumberPlateYear() { Year = thisYear, Plate = int.Parse(thisYear.ToString().Substring(2)) });
      //      numberPlateYear.Add(new NumberPlateYear() { Year = thisYear, Plate = int.Parse(thisPlate) });
      //   }

      //   int minMaxPlatePosition = month < 9 ? 4 : 5;
      //   NumberPlateYear selectedMinPlate = numberPlateYear[minMaxPlatePosition];
      //   NumberPlateYear selectedMaxPlate = numberPlateYear[minMaxPlatePosition];

      //   return (min: selectedMinPlate, max: selectedMaxPlate);

      //}


      //private (NumberPlateYear min, NumberPlateYear max) GetMinMaxPlateYearReWrittenRP(DateTime firstRegisteredDate)
      //{
      //   var plateYear = WorkoutPlateYear(firstRegisteredDate);

      //   // Assuming the min and max plate years are the same for this calculation
      //   var plate = new NumberPlateYear()
      //   {
      //      Year = firstRegisteredDate.Year,
      //      Plate = plateYear
      //   };

      //   return (min: plate, max: plate);
      //}

      //private static int WorkoutPlateYear(DateTime firstRegisteredDate)
      //{
      //   int year = firstRegisteredDate.Year;
      //   int month = firstRegisteredDate.Month;


      //   // Calculate the plate year value by subtracting 2000 from the registration year
      //   int plateYear = year - 2000;

      //   // If the month is September or later, add 50 to the plate year
      //   if (month >= 9)
      //   {
      //      plateYear += 50;
      //   }

      //   return plateYear;
      //}

      //public async Task<SellingOutlook> GetSellingOutlook(SellingOutlookParams parms)
      //{
      //   //work out price adjusted days to sell.
      //   var apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
      //   var apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
      //   var autotraderBaseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
      //   GetAdvertPriceAdjustedDaysToSellParams priceParms = new GetAdvertPriceAdjustedDaysToSellParams()
      //   {
      //      AdvertiserId = parms.RetailerSiteRetailerId,
      //      AutotraderBaseURL = autotraderBaseURL,
      //      DerivativeId = parms.DerivativeId,
      //      FirstRegistrationDate = parms.FirstRegisteredDate,
      //      OdometerReadingMiles = parms.Mileage,
      //      Amount = parms.StrategyPrice,
      //      UseSpecificOptions = parms.VehicleHasOptionsSpecified,
      //      SpecificOptionNames = parms.VehicleAdvertPortalOptions,

      //      AdjustedValuation = parms.CurrentAdjustedValuation,
      //      AverageValuation = parms.CurrentValuation
      //   };

      //   TokenResponse token = await tokenClient.GetToken();
      //   decimal daysToSell = 0;
      //   try
      //   {
      //      decimal daysToSellResult = await atMetricsClient.GetAdvertPriceAdjustedDaysToSell(priceParms, token.AccessToken);
      //      if (daysToSellResult != 0)
      //      {
      //         daysToSell = Math.Ceiling(daysToSellResult);
      //      }

      //   }
      //   catch (Exception ex)
      //   {
      //      Console.WriteLine(ex.Message);
      //   }

      //   //use this to do the future valuations thing
      //   List<FutureValuationPoint> futureVals = await GetFutureValuations(
      //       parms.Mileage,
      //       parms.DerivativeId,
      //       daysToSell,
      //       parms.FirstRegisteredDate,
      //       parms.RetailerSiteRetailerId,
      //       parms.CurrentValuation,
      //       parms.CurrentAdjustedValuation);


      //   //draw chart
      //   SellingOutlook results = new SellingOutlook()
      //   {
      //      Dates = new List<DateTime>(),
      //      StrategyPrices = new List<decimal>(),
      //      Valuations = new List<decimal>(),
      //      DaysToSell = daysToSell
      //   };
      //   DateTime currentDate = DateTime.Now.Date;
      //   for (int i = 0; i <= daysToSell; i++)
      //   {
      //      results.Dates.Add(currentDate.AddDays(i));
      //      results.StrategyPrices.Add(parms.StrategyPrice);
      //   }
      //   results.Valuations.Add(parms.CurrentValuation); //Adding only the first record

      //   PopulateFutureValuationItemsOnChart(parms.CurrentValuation, futureVals, results.Valuations, daysToSell);

      //   //return
      //   return results;
      //}


      //private async Task<List<FutureValuationPoint>> GetFutureValuations(int mileage, string derivativeId, decimal daysToSell, DateTime firstRegistration, int retailerId, decimal averageValuation, decimal adjustedValuation)
      //{
      //   List<DateTime> futureValnPoints = new List<DateTime>();//

      //   int finalPoint = Math.Max(45, (int)daysToSell);

      //   futureValnPoints.Add(DateTime.Now.AddDays(5));
      //   futureValnPoints.Add(DateTime.Now.AddDays(10));
      //   futureValnPoints.Add(DateTime.Now.AddDays(20));
      //   futureValnPoints.Add(DateTime.Now.AddDays(finalPoint));


      //   string baseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
      //   string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
      //   string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
      //   GetFutureValuationsParams parms = new GetFutureValuationsParams()
      //   {
      //      futureValuationPoints = futureValnPoints,
      //      derivativeId = derivativeId,
      //      firstRegistrationDate = firstRegistration,
      //      odometerReading = mileage,
      //      // apiKey = apiKey,
      //      // apiSecret = apiSecret,
      //      // baseUrl = baseURL,
      //      retailerId = retailerId,
      //      currentValuation = averageValuation,
      //      currentAdjustedValuation = adjustedValuation
      //   };
      //   var token = await tokenClient.GetToken();
      //   return await atFutureValsClient.GetFutureValuation(parms, token);
      //}


      //private static void PopulateFutureValuationItemsOnChart(
      //   decimal valuationPrice,
      //   List<FutureValuationPoint> futureValuations,
      //   List<decimal> valuations,
      //   decimal daysToSell)
      //{
      //   decimal cumValuationPrice = valuationPrice;
      //   //now go forwards to future points
      //   int totalDaysForward = 1;
      //   int futurePointIndex = 0;
      //   int finalPoint = Math.Max(45, (int)daysToSell);
      //   List<int> futurePoints = new List<int>() { 5, 10, 20, finalPoint };


      //   int lastFuturePointDaysElapsed = 0;
      //   decimal futurePointStartingValue = cumValuationPrice;
      //   int futurePointDaysElapsed = 0;
      //   decimal dailyIncrement = 0;

      //   futurePointDaysElapsed = futurePoints[futurePointIndex];
      //   dailyIncrement = (futureValuations[0].RetailValue - futurePointStartingValue) / (futurePointDaysElapsed - lastFuturePointDaysElapsed);

      //   while (totalDaysForward < finalPoint)
      //   {
      //      try
      //      {

      //         if (futurePoints.Contains(totalDaysForward))
      //         {
      //            //we are at a next future time point
      //            cumValuationPrice = futureValuations[futurePointIndex].RetailValue;
      //            valuations.Add(cumValuationPrice);

      //            lastFuturePointDaysElapsed = futurePointDaysElapsed;
      //            futurePointIndex++;
      //            futurePointDaysElapsed = futurePoints[futurePointIndex];
      //            futurePointStartingValue = cumValuationPrice;
      //            dailyIncrement = (decimal)(futureValuations[futurePointIndex].RetailValue - futureValuations[futurePointIndex - 1].RetailValue) / (decimal)(futurePointDaysElapsed - lastFuturePointDaysElapsed);

      //            totalDaysForward++;

      //         }
      //         else
      //         {

      //            cumValuationPrice += dailyIncrement;
      //            valuations.Add(cumValuationPrice);
      //            totalDaysForward++;
      //         }


      //      }
      //      catch (Exception ex)
      //      {
      //         {
      //            { }
      //         }
      //      }
      //   }
      //   valuations.Add(futureValuations[3].RetailValue);
      //}

      public async Task ParseAndUploadVehiclesForValuation(IFormFile file, string vehiclesForValuationParams, string batchName, bool applyPriceScenarios, BulkUploadPredefinedTemplateType bulkUploadPredefinedTemplateType)
      {
         //we initially parse it like so
         List<VehiclesForValuationParamsInitialRead> parsedData = JsonConvert.DeserializeObject<List<VehiclesForValuationParamsInitialRead>>(vehiclesForValuationParams);

         //then we can perform custom interpretation of condition
         List<VehiclesForValuationParams> data = parsedData.ConvertAll(x => new VehiclesForValuationParams(x)).ToList();

         string dealerGroupBlobName = DealerGroupBlobname.Get(userService.GetUserDealerGroupName());
         await UploadVehiclesForValuation(dealerGroupBlobName, file, data, batchName, applyPriceScenarios, bulkUploadPredefinedTemplateType);
      }

      public async Task<IEnumerable<VehicleHistoryItem>> GetVehicleHistory(GetVehicleHistoryParams parms)
      {

         //----------------------------------------------
         // 1. Find out about the user and RetailerSites
         //----------------------------------------------
         int userId = userService.GetUserId();
         var userDealerGroupName = userService.GetUserDealerGroupName();
         var userRetailerSiteId = userService.GetIdFromAccessToken("CurrentRetailerSiteId");// await userService.GetUserRetailerSiteId(userId, userDealerGroupName);
         var userRetailerSiteRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");// await userService.GetUserRetailerSiteId(userId, userDealerGroupName);
         var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var userRetailerSiteIds = userService.GetUserRetailerSiteIds();
         var retailerSitesTask = retailerSitesDataAccess.GetRetailerSites(userDealerGroupName);
         var retailerSitesWithStrategiesTask = retailerSitesDataAccess.GetRetailerSitesWithBuyingStrategies(DateTime.Now, userDealerGroupName);



         //----------------------------------------------
         // 2. Get a token
         //----------------------------------------------
         string apiBaseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         var tokenResponseTask = tokenClient.GetToken();



         //----------------------------------------------
         //Execute first set of tasks
         await Task.WhenAll(retailerSitesTask, tokenResponseTask, retailerSitesWithStrategiesTask);
         List<RetailerSite> retailerSites = retailerSitesTask.Result;

         //1. Result
         var userRetailer = retailerSites.First(x => x.Id == userRetailerSiteId);// await retailerSitesDataAccess.GetRetailerSite(userDealerGroupName, userRetailerSiteId);
         var retailerSitesById = retailerSites.ToDictionary(x => x.RetailerId);
         var retailerSitesWithStrategies = retailerSitesWithStrategiesTask.Result;

         //2. Result
         var tokenResponse = tokenResponseTask.Result;

         ATNewVehicleGet atVehicleInformation = await atVehiclesClient.GetIndividualVehicle(parms.Reg, parms.Mileage, userRetailerSiteRetailerId, tokenResponse.AccessToken, apiBaseURL); // Reg
         VehicleInformation vehicleInformation = new VehicleInformation(atVehicleInformation, VehicleRecallStatus.Unavailable, parms.Mileage, null); // Mileage

         return vehicleInformation.VehicleHistoryItems;
      }

      public async Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItemsForModel(GetLeavingVehicleItemsForModalParams parms)
      {
         var userRetailerSiteIds = userService.GetUserRetailerSiteIds();

         GetLeavingVehicleItemsParams leavingParams = new GetLeavingVehicleItemsParams()
         {
            ChosenRetailerSiteIds = userRetailerSiteIds.ToList(),
            StartDate = DateTime.Now.AddMonths(-3),
            EndDate = DateTime.Now,
            IncludeNewVehicles = false,
            IncludeUsedVehicles = true
         };

         return await leavingPricesDataAccess.GetLeavingVehicleItemsForModel(leavingParams, parms.Model, userRetailerSiteIds);
      }

      public async Task<IEnumerable<SameModelAdvert>> GetSameModelAdverts(GetSameModelAdvertsParams parms)
      {
         var userDealerGroupName = userService.GetUserDealerGroupName();
         return await vehicleAdvertsDataAccess.GetSameModelAdverts(parms.Model, userDealerGroupName, parms.IncludeUnpublishedAds);
      }

      public async Task<IEnumerable<StockLevelAndCover>> GetStockCover(GetSameModelAdvertsParams parms)
      {
         int userId = userService.GetUserId();
         var userDealerGroupName = userService.GetUserDealerGroupName();
         var userRetailerSiteId = userService.GetIdFromAccessToken("CurrentRetailerSiteId");// await userService.GetUserRetailerSiteId(userId, userDealerGroupName);
         var userRetailerSiteRetailerId = userService.GetIdFromAccessToken("CurrentRetailerSiteRetailerId");// await userService.GetUserRetailerSiteId(userId, userDealerGroupName);
         var retailerSitesDataAccess = new RetailerSitesDataAccess(_connectionString);
         var userRetailerSiteIds = userService.GetUserRetailerSiteIds();
         var retailerSitesTask = retailerSitesDataAccess.GetRetailerSites(userDealerGroupName);
         var retailerSitesWithStrategiesTask = retailerSitesDataAccess.GetRetailerSitesWithBuyingStrategies(DateTime.Now, userDealerGroupName);

         //----------------------------------------------
         // 2. Get a token
         //----------------------------------------------
         string apiBaseURL = Startup.Configuration["AutotraderSettings:BaseURL"];
         string apiKey = Startup.Configuration["AutotraderSettings:ApiKey"];
         string apiSecret = Startup.Configuration["AutotraderSettings:ApiSecret"];
         var tokenResponseTask = tokenClient.GetToken();


         //----------------------------------------------
         //Execute first set of tasks
         await Task.WhenAll(retailerSitesTask, tokenResponseTask, retailerSitesWithStrategiesTask);
         List<RetailerSite> retailerSites = retailerSitesTask.Result;

         Task<IEnumerable<SameModelAdvert>> sameModelAdvertsTask = vehicleAdvertsDataAccess.GetSameModelAdverts(parms.Model, userDealerGroupName, parms.IncludeUnpublishedAds);

         GetLeavingVehicleItemsParams leavingParams = new GetLeavingVehicleItemsParams()
         {
            ChosenRetailerSiteIds = userRetailerSiteIds.ToList(),
            StartDate = DateTime.Now.AddMonths(-3),
            EndDate = DateTime.Now,
            IncludeNewVehicles = false,
            IncludeUsedVehicles = true
         };

         Task<IEnumerable<LeavingVehicleItem>> recentlySoldThisModelTask = leavingPricesDataAccess.GetLeavingVehicleItemsForModel(leavingParams, parms.Model, userRetailerSiteIds);


         //10.Same model adverts Result
         List<SameModelAdvert> sameModelAdverts = (sameModelAdvertsTask.Result).ToList();

         SameModelAdvertSummary sameModelSummary = new SameModelAdvertSummary()
         {
            SameModelAdverts = sameModelAdverts,
            AverageDaysListed = 0,
            AverageDaysToSell = 0,
            AveragePricePosition = 0,
            AverageProfit = 0,
         };

         foreach (var item in sameModelAdverts)
         {
            sameModelSummary.AverageDaysListed += item.DaysListed;
            sameModelSummary.AverageDaysToSell += item.DaysToSell;
            sameModelSummary.AveragePricePosition += item.PricePosition;
            sameModelSummary.AverageProfit += item.Profit;
         }

         if (sameModelAdverts.Count > 0)
         {
            sameModelSummary.AverageDaysListed = sameModelSummary.AverageDaysListed / sameModelAdverts.Count;
            sameModelSummary.AverageDaysToSell = sameModelSummary.AverageDaysToSell / sameModelAdverts.Count;
            sameModelSummary.AveragePricePosition = sameModelSummary.AveragePricePosition / sameModelAdverts.Count;
            sameModelSummary.AverageProfit = sameModelSummary.AverageProfit / sameModelAdverts.Count;
         }

         List<StockLevelAndCover> stockLevelAndCovers = new List<StockLevelAndCover>();

         int totalStock = 0;
         int totalSales = 0;

         var recentlySoldThisModel = (recentlySoldThisModelTask.Result).ToList();

         foreach (var rs in retailerSites)
         {
            var stock = sameModelAdverts.Where(x => x.RetailerId == rs.Id).ToList();

            totalStock += stock.Count();
            var sales = recentlySoldThisModel.Where(x => x.RetailerId == rs.Id).ToList();
            totalSales += sales.Count();
            stockLevelAndCovers.Add(new StockLevelAndCover(stock.Count(), sales.Count(), rs.Name, false, 0));
         }

         int totalSalesPerMonth = stockLevelAndCovers.Sum(r => r.SalesPerMonth);
         StockLevelAndCover totalRow = new StockLevelAndCover(totalStock, totalSales, "Total", true, totalSalesPerMonth);
         stockLevelAndCovers.Add(totalRow);

         return stockLevelAndCovers;
      }

   }





}