﻿using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class VehicleOptOutSummaryItemNew
   {
      public int RetailerSiteId { get; set; }
      public string RetailerName { get; set; }
      public int SnapshotId { get; set; }
      public int VehicleAdvertId { get; set; }
      public string VehicleReg { get; set; }
      public string Make { get; set; }
      public string Model { get; set; }
      public string Chassis { get; set; }
      public int StockItemId { get; set; }
      public string StockNumber { get; set; }
      public string Derivative { get; set; }
      public int DaysListed { get; set; }
      public decimal RetailRating { get; set; }
      public DateTime OptOutCreatedDate { get; set; }
      public DateTime OptOutEndDate { get; set; }
      public string OptOutPerson { get; set; }
      public string OptOutType { get; set; }
      public decimal StrategyPrice { get; set; }
      public decimal SellingPrice { get; set; }
      public decimal LastChangeValue { get; set; }
      public DateTime? LastChangeDate { get; set; }
      public string LastChangedBy { get; set; }
      public int TotalChangesCount { get; set; }
      public decimal TotalChangeValue { get; set; }
      public int Days { get; set; }
      public string OwnershipCondition { get; set; }
      public string LastComment { get; set; }
      public string LifecycleStatus { get; set;}
   }
}