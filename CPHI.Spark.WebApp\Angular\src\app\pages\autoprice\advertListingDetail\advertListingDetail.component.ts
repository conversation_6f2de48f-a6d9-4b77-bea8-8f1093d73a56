import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AutoPriceInsightsModalComponent } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import { CphPipe } from 'src/app/cph.pipe';
import { VehicleValutionBatch } from 'src/app/model/VehicleValuationBatch';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { AdvertListingDetailService } from './advertListingDetail.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-advertListingDetail',
  templateUrl: './advertListingDetail.component.html',
  styleUrls: ['./advertListingDetail.component.scss']
})
export class AdvertListingDetailComponent implements OnInit {

  constructor(
    public selections: SelectionsService,
    public service: AdvertListingDetailService,
    public modalService: NgbModal,
    public constants: ConstantsService,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.selections.triggerSpinner.next({ show: false });

    this.service.initParams();
    if (!this.service.vehicleAdvertsRowData) { this.service.getData(); }

    this.route.paramMap.subscribe(params => {
      const regNumber = params.get('regNumber');
      //Get data for reg number
      if (regNumber) {
        this.service.getRegDataAndOpenModal(regNumber);
      }
    });
  }



  toggleIncludeNewVehicles(): void {
    this.service.includeNewVehicles = !this.service.includeNewVehicles;
    this.service.resetChosenLifecycleStatues()
    this.service.getData();
  }

  toggleIncludeUnPublishedAds(): void {
    this.service.includeUnPublishedAds = !this.service.includeUnPublishedAds;
    this.service.resetChosenLifecycleStatues()
    this.service.getData();
  }

  viewByBulkUpload(shouldShow: boolean) {
    this.selections.triggerSpinner.emit({ show: true, message: 'Loading...' });
    this.service.viewByBulkUpload = shouldShow;

    if (shouldShow) this.service.getVehicleValuationBatches();
  }

  selectBatch(batch: VehicleValutionBatch) {
    this.service.chosenVehicleValuationBatch = batch;
    this.service.getValuationBatchResults();
  }

  refresh(){
    this.service.getData();
  }
  setChosenDate(event: any) {
    this.service.chosenDate = event.target.value;
    this.service.getData();
  }


  onChosenLifeCycleStatusesChange() {
    this.service.reFilterVehicleAds();
  }

  onChosenVehTypesChange() {
    this.service.reFilterVehicleAds();
  }

  provideLifecyleCount(lifecycleStatus: string) {
    return this.service.vehicleAdvertsRowData.filter(x => x.LifecycleStatus == lifecycleStatus).length
  }

  provideVehTypeCount(vehType: string) {
    return this.service.vehicleAdvertsRowData.filter(x => x.VehicleTypeDesc == vehType).length
  }

  infoMessage(): string {
    return `The table below details all current stock.  Double click an item to see more detail.  Click on 'Customise Columns' to add / change columns used in the report.  Right click the table or use the report buttons to open a previously saved report.`
  }
}
