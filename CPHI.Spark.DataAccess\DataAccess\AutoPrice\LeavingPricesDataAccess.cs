﻿using System;
using System.Linq;
using System.Data;
using System.Collections.Generic;
using System.Threading.Tasks;
using CPHI.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Microsoft.EntityFrameworkCore;
using Dapper;
using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;

namespace CPHI.Spark.DataAccess.AutoPrice
{
   public interface ILeavingPricesDataAccess
   {
      Task<IEnumerable<LeavingPriceBasicItem>> GetLeavingPriceBasicItems(GetLeavingPriceItemsParams parms);
      Task<IEnumerable<LeavingPriceSummaryItem>> GetLeavingPriceItemsForDerivative(GetLeavingPriceItemsForDerivativeParams parms);
      Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItems(GetLeavingVehicleItemsParams parms, IEnumerable<int> userRetailerSiteIds);
      Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItemsForModel(GetLeavingVehicleItemsParams parms, string model, IEnumerable<int> userRetailerSiteIds);
      Task<IEnumerable<LeavingVehicleModelItem>> GetLeavingVehicleModelItems(GetStatsStockProfileItemsParams parms, int userId);
      Task UpdateLeavingPriceItemDaysOnStrategy(DealerGroupName dealerGroup);
      Task UpdateLeavingPriceItemDaysOptedOut(DealerGroupName dealerGroup);
      Task<List<LeavingRegAndAdId>> GetLeavingRegAndAdIds(DealerGroupName dealerGroup);
      Task UpdateLeavingPriceItems(DateTime leavingOnOrAfter, DateTime leavingBefore, DealerGroupName dealerGroup, bool includeUnPublished);
      Task<LeavingPriceBasicItem?> GetLeavingPriceForAdvertId(int advertId);
      Task<List<string>> ConfirmWhichRegsWeSold(List<string> competitorRegs);
   }



  public class LeavingPricesDataAccess : ILeavingPricesDataAccess
  {

    private readonly string _connectionString;
    public LeavingPricesDataAccess(string connectionString)
    {
      this._connectionString = connectionString;
    }




      public async Task UpdateLeavingPriceItems(DateTime leavingOnOrAfter, DateTime leavingBefore, DealerGroupName dealerGroup, bool includeUnPublished)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            //paramList.Add("leavingOnOrAfterDate", leavingOnOrAfter);
            //paramList.Add("leavingBeforeDate", leavingBefore);
            paramList.Add("dealerGroupId", (int)dealerGroup);
            paramList.Add("includeUnPublished", includeUnPublished ? 1 : 0);
            await dapper.ExecuteAsync("autoprice.UPDATE_LeavingPriceItems", paramList, CommandType.StoredProcedure, 60);
         }

    }



    public async Task UpdateLeavingPriceItemDaysOnStrategy(DealerGroupName dealerGroup)
    {
      using (var dapper = new DADapperr(_connectionString))
      {
        var paramList = new DynamicParameters();
        paramList.Add("dealerGroupId", (int)dealerGroup);
        await dapper.ExecuteAsync("autoprice.UPDATE_AddDaysOnStrategyToLeavingPriceItems", paramList, CommandType.StoredProcedure, 60);
      }

      }
      public async Task UpdateLeavingPriceItemDaysOptedOut(DealerGroupName dealerGroup)
      {
         using (var dapper = new DADapperr(_connectionString))
         {
            var paramList = new DynamicParameters();
            paramList.Add("dealerGroupId", (int)dealerGroup);
            await dapper.ExecuteAsync("autoprice.UPDATE_AddDaysOptedOutToLeavingPriceItems", paramList, CommandType.StoredProcedure, 60);
         }

      }

    public async Task<IEnumerable<LeavingPriceSummaryItem>> GetLeavingPriceItemsForDerivative(GetLeavingPriceItemsForDerivativeParams parms)
    {


      var parameters = new DynamicParameters();
      parameters.Add("@chosenRetailerSiteIds", string.Join(",", parms.ChosenRetailerSiteIds));
      parameters.Add("@eligibleSiteIds", string.Join(",", parms.EligibleSiteIds));
      parameters.Add("@startDate", parms.StartDate);
      parameters.Add("@endDate", parms.EndDate);
      parameters.Add("@derivativeId", parms.DerivativeId);


      using (var dapper = new DADapperr(_connectionString))
      {
        IEnumerable<LeavingPriceSummaryItemDTO> dtos = await dapper.GetAllAsync<LeavingPriceSummaryItemDTO>(
        "[autoprice].[GET_LeavingPriceSummaryItemsForDerivative]",
        parameters, System.Data.CommandType.StoredProcedure);

        List<LeavingPriceSummaryItem> results = new List<LeavingPriceSummaryItem>();
        foreach (var item in dtos)
        {
          results.Add(new LeavingPriceSummaryItem(item));
        }

        return results;
      }
    }














    public async Task<IEnumerable<LeavingVehicleModelItem>> GetLeavingVehicleModelItems(GetStatsStockProfileItemsParams parms, int userId)
    {
      var parameters = new DynamicParameters();
      parameters.Add("@chosenRetailerSiteIds", string.Join(",", parms.ChosenRetailerSiteIds));
      parameters.Add("@userId", userId);


      using (var dapper = new DADapperr(_connectionString))
      {
        IEnumerable<LeavingVehicleModelItem> results = await dapper.GetAllAsync<LeavingVehicleModelItem>(
        "[autoprice].[GET_LeavingVehicleModelItems]",
        parameters, System.Data.CommandType.StoredProcedure
        );

        return results;
      }
    }




    public async Task<IEnumerable<LeavingPriceBasicItem>> GetLeavingPriceBasicItems(GetLeavingPriceItemsParams parms)
    {

      using (var db = new CPHIDbContext(_connectionString))
      {
        var items = await db.LeavingPriceItems
                .Include(x => x.FirstVehicleAdvertSnapshot)
                .ThenInclude(x => x.VehicleAdvert)
                .ThenInclude(x => x.RetailerSite)
                .Include(x => x.LastVehicleAdvertSnapshot)
                .Where(x =>
                    parms.ChosenRetailerSiteIds.Contains(x.VehicleAdvert.RetailerSite_Id) &&
                    parms.EligibleSiteIds.Contains(x.VehicleAdvert.RetailerSite.Site_Id) &&
                    x.LastVehicleAdvertSnapshot.SnapshotDate >= parms.StartDate &&
                    x.LastVehicleAdvertSnapshot.SnapshotDate <= parms.EndDate
                    )
                .Select(x => new LeavingPriceBasicItem(x))
                .ToListAsync();

        return items;//.Where(x=>!x.RetailerSiteName.StartsWith("Alpine"));
      }
    }

      public async Task<List<LeavingRegAndAdId>> GetLeavingRegAndAdIds(DealerGroupName dealerGroup)
      {
         var parameters = new DynamicParameters();
         parameters.Add("@dealerGroupId", (int)dealerGroup);


         using (var dapper = new DADapperr(_connectionString))
         {
            try
            {

               IEnumerable<LeavingRegAndAdId> res = await dapper.GetAllAsync<LeavingRegAndAdId>("[autoprice].[GET_LeavingRegAndAdIds]", parameters, System.Data.CommandType.StoredProcedure);
               return res.ToList();
               
            }
            catch (Exception ex)
            {
               { }
               throw new Exception("Failed during SP GetLeavingRegAndAdIds");
            }
         }
      }

      public async Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItems(GetLeavingVehicleItemsParams parms, IEnumerable<int> userRetailerSiteIds)
      {
         var parameters = new DynamicParameters();
         var retailerSiteIds = parms.ChosenRetailerSiteIds.Intersect(userRetailerSiteIds);
         string rsString = string.Join(',', retailerSiteIds);
         parameters.Add("@chosenRetailerSiteIds", string.Join(",", retailerSiteIds));
         parameters.Add("@startDate", parms.StartDate);
         parameters.Add("@endDate", parms.EndDate);
         parameters.Add("@includeNewVehicles", parms.IncludeNewVehicles);
         parameters.Add("@includeUsedVehicles", parms.IncludeUsedVehicles);


      using (var dapper = new DADapperr(_connectionString))
      {
        IEnumerable<LeavingVehicleItem> res = await dapper.GetAllAsync<LeavingVehicleItem>("[autoprice].[GET_LeavingVehicleItems]", parameters, System.Data.CommandType.StoredProcedure);

        return res.Where(x => x.FirstPP > 0.7M && x.LastPP > 0);
      }
    }



    public async Task<IEnumerable<LeavingVehicleItem>> GetLeavingVehicleItemsForModel(GetLeavingVehicleItemsParams parms, string model, IEnumerable<int> userRetailerSiteIds)
    {
      var parameters = new DynamicParameters();
      var retailerSiteIds = parms.ChosenRetailerSiteIds.Intersect(userRetailerSiteIds);
      string rsString = string.Join(',', retailerSiteIds);

      parameters.Add("@chosenRetailerSiteIds", string.Join(",", retailerSiteIds));
      parameters.Add("@startDate", parms.StartDate);
      parameters.Add("@endDate", parms.EndDate);
      parameters.Add("@includeNewVehicles", parms.IncludeNewVehicles);
      parameters.Add("@includeUsedVehicles", parms.IncludeUsedVehicles);
      parameters.Add("@model", model);


      using (var dapper = new DADapperr(_connectionString))
      {
        try
        {

          IEnumerable<LeavingVehicleItem> res = await dapper.GetAllAsync<LeavingVehicleItem>("[autoprice].[GET_LeavingVehicleItemsForModel]", parameters, System.Data.CommandType.StoredProcedure);

          return res.Where(x => x.FirstPP > 0.7M && x.LastPP > 0);
        }
        catch (Exception ex)
        {
          { }
          throw new Exception("oh");
        }
      }
    }






    public async Task<LeavingPriceBasicItem?> GetLeavingPriceForAdvertId(int advertId)
    {
      using (var db = new CPHIDbContext(_connectionString))
      {
        var item = await db.LeavingPriceItems
                .Include(x => x.FirstVehicleAdvertSnapshot)
                .ThenInclude(x => x.VehicleAdvert)
                .ThenInclude(x => x.RetailerSite)
                .Include(x => x.LastVehicleAdvertSnapshot)
                .Where(x => x.VehicleAdvert_Id == advertId)
                .Select(x => new LeavingPriceBasicItem(x))
                .FirstOrDefaultAsync();

        return item;
      }

    }

      public async Task<List<string>> ConfirmWhichRegsWeSold(List<string> competitorRegs)
      {
         using (var db = new CPHIDbContext(_connectionString))
         {
            var results = await db.LeavingPriceItems
                    .Where(x => competitorRegs.Contains(x.VehicleAdvert.VehicleReg) )
                    .Select(x => x.VehicleAdvert.VehicleReg)
                    .ToListAsync();

            return results;
         }
      }
   }

}

