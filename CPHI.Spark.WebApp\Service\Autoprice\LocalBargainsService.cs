﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;using CPHI.Spark.Model;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
    public interface ILocalBargainsService
    {
        Task<IEnumerable<LocalBargainDetail>> GetLocalBargains(string chosenDate);
    }
    public class LocalBargainsService : ILocalBargainsService
    {
        private readonly IUserService userService;
        private readonly IConfiguration configuration;
        private readonly string _connectionString;

        public LocalBargainsService(
            IUserService userService, IConfiguration configuration
            )
        {
            this.userService = userService;
            this.configuration = configuration;
            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            _connectionString = configuration.GetConnectionString(dgName);
        }

        public async Task<IEnumerable<LocalBargainDetail>> GetLocalBargains(string chosenDate)
        {
            var userId = userService.GetUserId();
            var localBargainsDataAccess = new LocalBargainDataAccess(_connectionString);
            var res = (await localBargainsDataAccess.GetLocalBargains(chosenDate,userId)).ToList();
            foreach (var item in res)
            {
                if (item.AdvertiserSegment == string.Empty || item.AdvertiserSegment == null)
                {
                    item.AdvertiserSegment = "Private Seller";
                }
                if (item.AdvertiserName== string.Empty)
                {
                    item.AdvertiserName= "Private Seller";
                }
            }
            return res;

        }
    }
}