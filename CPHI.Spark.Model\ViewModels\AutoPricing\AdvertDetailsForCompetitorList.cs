﻿using System;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class AdvertDetailsForCompetitorList
   {
      public AdvertDetailsForCompetitorList(VehicleAdvertDetail ad)
      {
         AdSiteName = ad.AdSiteName;
         OdometerReading = ad.OdometerReading;
         FirstRegisteredDate = ad.FirstRegisteredDate;
         AdvertisedPrice = (int)ad.AdvertisedPrice;
         VehicleReg = ad.VehicleReg;
         ImageUrl = ad.ImageUrl;
         WebSiteSearchIdentifier = ad.WebSiteSearchIdentifier;
         IsTradePricing = ad.IsTradePricing;
         TradeMarginPercentage = ad.TradeMarginPercentage;
         TradeMarginAmount = ad.TradeMarginAmount;
         Derivative = ad.Derivative;
        }
      public AdvertDetailsForCompetitorList(VehicleAdvertWithRating ad)
      {
         AdSiteName = ad.RetailerSiteName;
         OdometerReading = ad.OdometerReading;
         FirstRegisteredDate = ad.FirstRegisteredDate;
         AdvertisedPrice = ad.AdvertisedPrice;
         VehicleReg = ad.VehicleReg;
         ImageUrl = ad.ImageURL;
         WebSiteSearchIdentifier = ad.WebSiteSearchIdentifier;

         IsTradePricing = ad.IsTradePricing;
         TradeMarginPercentage = ad.TradeMarginPercentage;
         TradeMarginAmount = ad.TradeMarginAmount;
         Derivative = ad.Derivative;
      }

      public string AdSiteName { get; set; }
      public int? OdometerReading { get; set; }
      public DateTime? FirstRegisteredDate { get; set; }
      public int AdvertisedPrice { get; set; }
      public string VehicleReg { get; set; }
      public string ImageUrl { get; set; }
      public string WebSiteSearchIdentifier { get; set; }
      public bool? IsTradePricing { get; set; }
      public decimal? TradeMarginPercentage { get; set; }
      public int? TradeMarginAmount { get; set; }
      public string Derivative { get; set; }
    }


}
