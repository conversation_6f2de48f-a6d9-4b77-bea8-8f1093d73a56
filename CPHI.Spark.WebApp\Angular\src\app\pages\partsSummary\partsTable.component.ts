//core angular
import { Component, OnInit, Output, EventEmitter, HostListener, Input } from '@angular/core';
//model and cell renderers
import { PartsSalesSiteRow } from '../../model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
//services
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
//pipes and interceptors
import { CphPipe } from '../../cph.pipe';
//Angular things, non-standard
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { ExcelExportService } from '../../services/excelExportService';
import { localeEs } from 'src/environments/locale.es.js';
import { AGGridMethodsService } from '../../services/agGridMethods.service';
import { PartsSummaryService } from './partsSummary.service';
import { CellClassParams, ColDef } from 'ag-grid-community';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';


interface ColDefWithChildren extends ColDef {
  children?: ColDef[];
}

@Component({
  selector: 'partsTable',
  template: `
    <div id="gridHolder">
      <div  id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular
        id="PartsSalesTable"
        class="ag-theme-balham" 
        [gridOptions]="mainTableGridOptions" 
      > 
      </ag-grid-angular>
    </div>
  `
  ,
  styles: [
    `
   
     
  `

  ],
  styleUrls: ['./../../../styles/components/_agGrid.scss'],
})

export class PartsTableComponent implements OnInit {
  @Input() isRegional: boolean;
  @Output() clickedSite = new EventEmitter<PartsSalesSiteRow>();

  @HostListener("window:resize", [])
  private onresize(event) {
    this.selections.screenWidth = window.innerWidth;
    this.selections.screenHeight = window.innerHeight;
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.resizeGrid();
    }
  }

  showGrid = false;
  public gridApi;
  public gridColumnApi;
  mainTableGridOptions: GridOptionsCph;
  filterBy: string;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public agGrid: AGGridMethodsService,
    public service: PartsSummaryService,
    public gridHelpersService: AGGridMethodsService,
    public columnTypeService: ColumnTypesService
  ) { }


  ngOnInit() {
    this.filterBy = this.isRegional ? 'Regions' : 'Sites';
    this.initParams();

    this.selections.partsSummary.sitesUpdateTrigger.subscribe(() => {
      if (this.gridApi) { this.updateGrid(); }
    })

    this.selections.partsSummary.summaryHasChangedEvent.subscribe(() => {
      if (this.gridApi) { this.updateGrid(); }
    })
  }

  updateGrid() {
    this.gridApi.setColumnDefs([]);
    this.gridApi.setColumnDefs(this.getColumnDef())
    this.gridApi.setRowData(this.getRowData());
    this.gridApi.setPinnedBottomRowData(this.getPinnedBottomRowData());
    this.gridApi.refreshCells({ force: true });
    this.gridApi.sizeColumnsToFit();
  }

  getPinnedBottomRowData(): any {

    if (this.selections.partsSummary.showDailyView) {
      return this.service.partsDailySalesTotalRows
    } else {
      return this.service.partsSalesTotalRow
    }

  }

  initParams() {
    this.mainTableGridOptions = {
      getMainMenuItems: (params) => this.gridHelpersService.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlights),
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      // getLocaleText: (key: string, defaultValue: string) => {
      //   this.constants.currentLang == 'es' ? localeEs[key] || defaultValue : defaultValue
      // },
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      rowData: this.service[`partsSales${this.filterBy}Rows`],
      pinnedBottomRowData: this.getPinnedBottomRowData(),
      onGridReady: (params) => this.onGridReady(params),
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },
      domLayout: 'autoHeight',
      onCellClicked: (params) => { this.onCellClick(params); },
      columnDefs: this.getColumnDef(),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() }, autoHeight: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlights),
      },
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      }
    }


  }

  resizeGrid() {
    if (this.gridApi) { this.gridApi.sizeColumnsToFit(); }
  }

  onCellClick(params) {
    if (params.data.IsSite) {
      this.clickedSite.next(this.service.partsSalesSitesRows.find(s => s.Label == params.data.Label));
    } else if (params.data.IsRegion) {
      this.clickedSite.next(this.service.partsSalesRegionsRows.find(s => s.Label == params.data.Label));
    } else {
      this.clickedSite.next(this.service.partsSalesTotalRow[0]);
    }
  }

  onGridReady(params) {
    //this.gridHelpers.topBottomHighlights = [];
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.setColumnDefs(this.mainTableGridOptions.columnDefs);
    this.gridApi.sizeColumnsToFit();
    this.mainTableGridOptions.context = { thisComponent: this };
  }

  excelExport() {
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Service Dept - ' + this.selections.partsSummary.timeOption, 1, 1);
  }

  getRowData(): any {

    if (this.selections.partsSummary.showDailyView) {
      //return this.service.serviceDailySalesSitesRows;
      // console.log(this.service[`serviceDailySales${this.filterBy}Rows`], "this.service[`serviceDailySales${this.filterBy}Rows`]!")
      return this.service[`partsDailySales${this.filterBy}Rows`]
    } else {
      return this.service[`partsSales${this.filterBy}Rows`]
    }

  }

  getColumnDef(): any {

    if (this.selections.partsSummary.showDailyView) {

      return this.provideDailyColDefs()

    }
    else {
      return this.provideCumulativeColDefs()
    }

  }



  private provideDailyColDefs() {

    let siteLabel: ColDefWithChildren[] = [
      { headerName: '', field: 'Label', colId: 'Label', width: 200, type: 'label', },
    ]

    let dailyCols: ColDefWithChildren[] = this.buildUpDailyCols();

    let mtdCols: ColDefWithChildren[] = [
      {
        headerName: 'Sales vs Target (£)',
        children: [
          { headerName: 'Tgt', field: 'Target', colId: 'Target', width: 70, type: 'currency', },
          { headerName: 'Act', field: 'Actual', colId: 'Actual', width: 70, type: 'currency', },
          { headerName: 'Vs', field: 'ActualVsTarget', colId: 'ActualVsTarget', width: 70, type: 'currency', },
          { headerName: '%', field: 'DonePercent', colId: 'DonePercent', width: 70, type: 'percent', },
        ],
      },

      {
        headerName: 'Avg.', children: [
          { headerName: '£', field: 'AverageDone', colId: 'AverageDone', width: 70, type: 'currency', },
        ]
      },]

    let combined = []

    siteLabel.map(x => combined.push(x))
    dailyCols.map(x => combined.push(x))
    mtdCols.map(x => combined.push(x))

    // if (this.selections.partsSummary.showTechGroupColumns){
    //   techCols.map(x => combined.push(x))
    // }

    // console.log(combined, "combined coldefs!")
    
    return combined
  }


  buildUpDailyCols() {

    let yesterday = new Date(new Date().setDate(new Date().getDate() - 1))
    let dailyCols: ColDefWithChildren[] = [];

    for (let i = 0; i < this.selections.partsSummary.month.endDate.getDate(); i++) {

      let date = new Date(this.selections.partsSummary.month.startDate.getFullYear(), this.selections.partsSummary.month.startDate.getMonth(), i + 1);
      let isWeekend = [0, 6].includes(date.getDay())
      let isSunday = date.getDay() === 0;

      if (isSunday) continue;

      //let dayLabel = date.toLocaleString(this.constants.translatedText.LocaleCode, { weekday: 'short' }) + date.toLocaleString(this.constants.translatedText.LocaleCode, { day: '2-digit' });
      let dayLabelSmall = date.getDate().toString();

      let columnWidth = 50;
      let hidden = true;
      if (date.getTime() < yesterday.getTime()) { columnWidth = 70, hidden = false }

      dailyCols.push(
        {
          headerName: dayLabelSmall,
          cellStyle: { 'background': isWeekend ? '#ECF0F1' : '#FFFFFF' },
          field: `d${dayLabelSmall}`, colId: `d${dayLabelSmall}`, width: columnWidth, type: 'currency', hide: hidden
        }
      );

    }

    let mainCol = { headerName: 'Daily Figures (£)', children: dailyCols }

    return [mainCol];
  }


  provideCumulativeColDefs(): (import("ag-grid-community").ColDef | import("ag-grid-community").ColGroupDef)[] {

    var colDefs = [
      { headerName: '', field: 'Label', colId: 'Label', width: 400, type: 'label' },
      { headerName: this.constants.translatedText.Common_MonthTarget, field: 'MonthTarget', colId: 'MonthTarget', width: 115, type: 'currency' },
      {
        headerName: this.constants.translatedText.Dashboard_ServiceSales_SalesVsTarget,
        children: [
          { headerName: this.constants.translatedText.Common_Target, field: 'TargetToDate', colId: 'TargetToDate', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.Common_Actual, field: 'DoneToDate', colId: 'DoneToDate', width: 115, type: 'currency' },
          { headerName: 'Vs', field: 'VsToDate', colId: 'VsToDate', width: 115, type: 'currencyWithFontColour' },
          { headerName: this.constants.translatedText.Common_Achievement, field: 'AchievementToDate', colId: 'AchievementToDate', width: 115, type: 'percent' }
        ]
      },
      {
        headerName: this.constants.translatedText.Dashboard_ServiceSales_SalesPerDay,
        children: [
          { headerName: this.constants.translatedText.Common_Target, field: 'TargetPerDay', colId: 'TargetPerDay', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.Common_Done, field: 'DonePerDay', colId: 'DonePerDay', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.Common_Required, valueGetter: (params) => this.selections.partsSummary.timeOption == 'WTD' || this.selections.partsSummary.timeOption == 'Yesterday' ? '' : params.data.RequiredPerDay, field: 'RequiredPerDay', colId: 'RequiredPerDay', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.Common_StepUp, field: 'StepUpPerDay', colId: 'StepUpPerDay', width: 115, type: 'currencyFlipColour' }
        ]
      }
    ]

    if (this.constants.environment.partsSales.includeMarginCols) {
      let marginCols = {
        headerName: this.constants.translatedText.Common_Margin,
        children: [
          { headerName: this.constants.translatedText.Common_Target, field: 'TargetToDateMargin', colId: 'MonthTargetMargin', width: 115, type: 'currency' },
          { headerName: this.constants.translatedText.Common_Actual, field: 'DoneToDateMargin', colId: 'DoneToDateMargin', width: 115, type: 'currency' },
          { headerName: 'Vs', field: 'VsToDateMargin', colId: 'VsToDateMargin', width: 115, type: 'currencyWithFontColour' },
          { headerName: this.constants.translatedText.Common_Achievement, field: 'AchievementToDateMargin', colId: 'AchievementToDateMargin', width: 115, type: 'percent' }
        ]
      }

      colDefs.push(marginCols);
    }

    return colDefs;
  }


}
