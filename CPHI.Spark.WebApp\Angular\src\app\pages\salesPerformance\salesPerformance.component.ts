import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridApi } from 'ag-grid-community';
import { DatePickerModalComponent } from 'src/app/components/datePickerModal/datePickerModal.component';
import { SalesPerformanceOrderRateParams } from 'src/app/model/SalesPerformanceOrderRateParams';
import { SalesPerformanceOrderRateRow } from 'src/app/model/SalesPerformanceOrderRateRow';
import { SalesPerformanceParams } from 'src/app/model/SalesPerformanceParams';
import { SalesPerformanceReportType } from 'src/app/model/SalesPerformanceReportType';
import { SalesPerformanceRow } from 'src/app/model/SalesPerformanceRow';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { CphPipe } from '../../cph.pipe';
import { DateSelectionObject, Day, Department, Month, Week } from '../../model/main.model';
import { LateCostOption, OrderOption } from '../../model/sales.model';
import { ChartService } from '../../services/chart.service';
import { ConstantsService } from '../../services/constants.service';
import { GetDealDataService } from '../../services/getDeals.service';
import { SelectionsService } from '../../services/selections.service';
import { DashboardService } from '../dashboard/dashboard.service';
import { SalesPerformanceService } from './salesPerformance.service';


@Component({
  selector: 'app-salesPerformance',
  templateUrl: './salesPerformance.component.html',
  styleUrls: ['./salesPerformance.component.scss']
})

export class SalesPerformanceComponent implements OnInit {

  //@ViewChild('salesProjectionModal2', { static: true }) salesProjectionModal: SalesProjectionModalComponent;

  dateTypes: string[];


  salesPerformanceReportTypes: SalesPerformanceReportType[];
  months: Month[];
  weeks: Week[];
  days: Day[];
  monthsOffsetNumber: number;
  weeksOffsetNumber: number;
  daysOffsetNumber: number;

  //rowDataSubject: any;
  //pinnedRowDataSubject: any;
  //franchiseEncoded: string;
  //siteIdsForUser: number[]

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,

    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public chart: ChartService,
    public router: Router,
    public getDealData: GetDealDataService,
    public dashboardService: DashboardService,
    public service: SalesPerformanceService,
    public apiAccess: ApiAccessService,
  ) {
    this.dateTypes = ['Del Date', 'Inv Date', 'Accg Date']
    this.monthsOffsetNumber = 0;
    this.weeksOffsetNumber = 0;
    this.daysOffsetNumber = 0;

    this.service.measures = [
      { measure: 'Units', translation: this.constants.translatedText.Units },
      { measure: 'Margin', translation: this.constants.translatedText.Margin }
    ]
    this.service.chosenMeasure = this.service.measures[0].measure;
  }

  ngOnInit() {
    //launch control
    this.initParams();
    this.getData();
  }


  ngOnDestroy() {
    //this.service.salesProjectionModal = null;
    this.dashboardService.chosenSection.enableSitesSelector = true;

    this.service.gridApi = null;
    this.service.gridApiRegions = null;
    this.service.gridApiOrderRate = null;
    this.service.gridApiRegionsOrderRate = null;
    this.service.gridApiCustom = null;
    this.service.gridApiRegions = null;
    this.service.gridApiRegionsOrderRate = null;
    this.service.gridApiRegionsCustom = null;
  }


  reportTypeTranslated(reportType: SalesPerformanceReportType) {
    return this.constants.translatedText[`${reportType}`];
  }



  initParams() {

    if (this.constants.environment.salesPerformance_showAllSites) {
      this.dashboardService.chosenSection.enableSitesSelector = false;
    }

    //this.service.salesProjectionModal = this.salesProjectionModal;
    this.salesPerformanceReportTypes = this.populateReportTypes();

    //generate initial settings
    if (!this.service.sites) {
      //this.service.lastInitiated = new Date();
      this.service.sites = this.constants.clone(this.constants.sitesActiveSales.filter(s => s.Code !== 0 && s.IsEligibleForUser));
      this.service.totalSite = this.constants.clone(this.constants.Sites.filter(s => s.Code == 0));

      this.service.department = this.constants.clone(this.constants.departments[0]);
      this.service.franchises = this.constants.clone(this.constants.FranchiseCodes);
      this.service.lateCostOption = this.constants.lateCostOptions[1];
      this.service.orderOption = this.constants.orderOptions[0];
      //this.service.comparative = this.constants.clone(this.constants.comparatives[0]);
      this.service.orderDate = this.defaultOrderDateOptions();
      this.service.deliveryDate = this.defaultDeliveryDateOptions();
      this.service.salesPerformanceReportType = 'vsBudget';
      this.service.includeTradeUnits = false;
      this.service.includeMotabUnits = true;
      this.service.onlyMotabUnits = false;
      this.service.IsCurrentMonth = true;
      this.service.vehicleTypeIds = this.constants.VehicleTypes.filter(x => this.service.department.VehicleTypeTypes.includes(x.SuperType)).map(x => x.Id);
   
      // this.service.siteRows = [];
      // this.service.regionRows = [];
      // this.service.totalRows = [];
      // this.service.orderRateSiteRows = [];
      // this.service.orderRateRegionRows = [];
      // this.service.orderRateTotalRows = [];

      //  console.log(this.service.orderDate)
      //debugger
      this.calculateDays();
    }

    //console.log(this.constants.departments, "this.constants.departments!");

    //this.service.isCustom = false;

  }

  private defaultOrderDateOptions(): { startDate: Date; endDate: Date; monthName: string; weekName: string; dayName: string; amSelectingMonth: boolean; amSelectingAnytime: boolean; amSelectingCustom: boolean; amSelectingWeek: boolean; amSelectingDay: boolean; lastSelectedMonthStartDate: Date; lastSelectedWeekStartDate: Date; lastSelectedDayStartDate: Date; } {
    return {
      startDate: new Date(2008, 0, 1),
      endDate: this.constants.endOfDay(new Date()),// this.constants.addYears(this.constants.todayStart, 5),
      monthName: this.constants.appStartTime.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' }),
      dayName: 'Today',
      weekName: this.constants.translatedText.ThisWeek,
      amSelectingAnytime: true,
      amSelectingWeek: false,
      amSelectingDay: false,
      amSelectingMonth: false,
      amSelectingCustom: false,
      lastSelectedDayStartDate: this.constants.todayStart,
      lastSelectedMonthStartDate: this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), 1)),
      lastSelectedWeekStartDate: this.constants.mostRecentMonday(this.constants.appStartTime),
    };
  }

  private defaultDeliveryDateOptions(): { dateType: string; startDate: Date; endDate: Date; monthName: string; dayName: string; amSelectingMonth: boolean; amSelectingDay: boolean; amSelectingAnytime: boolean; amSelectingCustom: boolean; lastSelectedMonthStartDate: Date; lastSelectedDayStartDate: Date; } {
    return {
      dateType: 'Accg Date',
      startDate: this.constants.thisMonthStart,
      endDate: new Date(this.constants.thisMonthEnd),
      monthName: this.constants.appStartTime.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' }),
      amSelectingAnytime: false,
      amSelectingMonth: true,
      amSelectingCustom: false,
      lastSelectedMonthStartDate: this.constants.deductTimezoneOffset(new Date(this.constants.appStartTime.getFullYear(), this.constants.appStartTime.getMonth(), 1)),
      lastSelectedDayStartDate: this.constants.todayStart,
      dayName: 'Today',
      amSelectingDay: false,
    };
  }

  private populateReportTypes(): SalesPerformanceReportType[] {
    let salesPerformanceReportTypes: SalesPerformanceReportType[] = [];

    // Do translations
    salesPerformanceReportTypes = ['vsLastYear', 'vsBudget']

    if (this.constants.environment.salesPerformance_showOrderRateReportType) {
      salesPerformanceReportTypes.push('OrderRate');
    }

    if (this.constants.environment.salesPerformance_showCustomReportType) {
      salesPerformanceReportTypes.push('Custom');
    }

    return salesPerformanceReportTypes

  }


  private getParams(): SalesPerformanceParams {

    console.log(this.service.onlyMotabUnits, "this.service.onlyMotabUnits!");

    return {

      Department: this.service.department.ShortName,
      OrderDateStart: this.service.orderDate.startDate,
      OrderDateEnd: this.service.orderDate.endDate,

      DeliveryDateStart: this.service.deliveryDate.startDate,
      DeliveryDateEnd: this.service.deliveryDate.endDate,
      DeliveryDateType: this.service.deliveryDate.dateType,

      IsVsBudget: this.service.salesPerformanceReportType === 'vsBudget', // this.service.isVsBudgetOrCustom,
      IsCustom: false, //we will remove
      Franchises: this.service.franchises,
      IncludeLateCosts: this.service.lateCostOption.includeLateCosts,
      IncludeNonLateCosts: this.service.lateCostOption.includeNormalCosts,
      IncludeOrders: this.service.orderOption.includeOrders,
      IncludeNonOrders: this.service.orderOption.includeInvoiced,
      IncludeTrade: this.service.includeTradeUnits,
      IncludeMotab: this.service.includeMotabUnits,
      OnlyMotab: this.service.onlyMotabUnits

    };
  }

  private getOrderRateParams(): SalesPerformanceOrderRateParams {
    return {

      Department: this.service.department.ShortName,
      DeliveryMonthStart: this.service.deliveryDate.startDate,

      Franchises: this.service.franchises,
      IncludeLateCosts: this.service.lateCostOption.includeLateCosts,
      IncludeNonLateCosts: this.service.lateCostOption.includeNormalCosts,
      IncludeOrders: this.service.orderOption.includeOrders,
      IncludeNonOrders: this.service.orderOption.includeInvoiced,
      IncludeTrade: this.service.includeTradeUnits,
      IncludeMotab: this.service.includeMotabUnits,
      OnlyMotab: this.service.onlyMotabUnits
    };
  }


  public getData(): void {

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.LoadingPage });

    if (this.service.salesPerformanceReportType === 'OrderRate') {
      let params: SalesPerformanceOrderRateParams = this.getOrderRateParams();
      this.getOrderRateData(params);
    }
    else {
      let params: SalesPerformanceParams = this.getParams();
      this.getSalesPerformanceData(params);
    }


  }


  updateGridMainRows(gridApi: GridApi, newData: (SalesPerformanceRow | SalesPerformanceOrderRateRow)[], existingData: (SalesPerformanceRow | SalesPerformanceOrderRateRow)[]) {
    const transaction = this.buildTransaction(newData, existingData);
    gridApi.applyTransaction(transaction);
  }





  private buildTransaction(newData: (SalesPerformanceRow | SalesPerformanceOrderRateRow)[], existingData: (SalesPerformanceRow | SalesPerformanceOrderRateRow)[]) {
    const transaction = {
      add: [],
      remove: [],
      update: [] // For rows to update
    };

    //early exit if no array at all currently
    if(!existingData){
      newData.forEach(item=>{
        transaction.add.push(item)
      })
      return transaction;
    }


    newData.forEach(newRow => {
      // Find the row in the existing data
      const oldRow = existingData.find(row => row.SiteId === newRow.SiteId);

      // If the row exists in the existing data, add it to the update array
      if (oldRow) {
        transaction.update.push(newRow);
      } else {
        // If the row doesn't exist, add it to the add array
        transaction.add.push(newRow);
      }
    });
    return transaction;
  }

  private getSalesPerformanceData(params: SalesPerformanceParams) {
    this.apiAccess.post('api/Deals', 'GetSalesPerformanceNew', params).subscribe((res: SalesPerformanceRow[]) => {

      //update total rows
      const totalRows = res.filter(x => x.IsTotal);
      this.service.totalRows = totalRows;

      //update sites table
      const siteRows = res.filter(x => x.IsSite);
      this.service.siteRows = siteRows;

      if(this.service.gridApi){
        this.updateGridMainRows(this.service.gridApi, siteRows, this.service.siteRows);
        this.service.gridApi.setPinnedBottomRowData(totalRows)
      }
      else if(this.service.gridApiCustom)
      {
        this.updateGridMainRows(this.service.gridApiCustom, siteRows, this.service.siteRows);
        this.service.gridApiCustom.setPinnedBottomRowData(totalRows);
      }

      //update regions table
      const regionRows = res.filter(x => x.IsRegion);
      this.service.regionRows = regionRows;

      if(this.service.gridApiRegions){
        this.updateGridMainRows(this.service.gridApiRegions, regionRows, this.service.regionRows);
        this.service.gridApiRegions.setPinnedBottomRowData(totalRows)
      }
      else if(this.service.gridApiCustom)
      {
        this.updateGridMainRows(this.service.gridApiRegionsCustom, regionRows, this.service.regionRows);
        this.service.gridApiRegionsCustom.setPinnedBottomRowData(totalRows);
      }

    }, error => {
      console.error("ERROR: ", error);
    }, () => {
      this.selections.triggerSpinner.next({ show: false });

    });
  }

  private getOrderRateData(params: SalesPerformanceOrderRateParams) {
    this.apiAccess.post('api/Deals', 'GetSalesPerformanceOrderRate', params).subscribe((res: SalesPerformanceOrderRateRow[]) => {

      //update total rows
      const totalRows = res.filter(x => x.IsTotal);
      this.service.orderRateTotalRows = totalRows;


      //update sites table
      const siteRows = res.filter(x => x.IsSite);
      this.service.orderRateSiteRows = siteRows;
      if(this.service.gridApiOrderRate){
        this.updateGridMainRows(this.service.gridApiOrderRate, siteRows, this.service.orderRateSiteRows);
        this.service.gridApiOrderRate.setPinnedBottomRowData(totalRows)
      }

      //update regions table
      const regionRows = res.filter(x => x.IsRegion);
      this.service.orderRateRegionRows = regionRows;
      if (this.service.gridApiRegionsOrderRate) {
        this.updateGridMainRows(this.service.gridApiRegionsOrderRate, regionRows, this.service.orderRateRegionRows);
        this.service.gridApiRegionsOrderRate.setPinnedBottomRowData(totalRows)
      }


    }, error => {
      console.error("ERROR: ", error);
    }, () => {
      this.selections.triggerSpinner.next({ show: false });

    });
  }


  calculateDays(): void {
    //days
    let daysInMonth = this.constants.daysInMonth(this.service.deliveryDate.endDate);
    let daysElapsed = Math.min(daysInMonth, this.constants.differenceInDays(this.constants.todayStart, this.service.deliveryDate.startDate))
    this.service.Days = {
      elapsed: daysElapsed,
      remaining: daysInMonth - daysElapsed,
      total: daysInMonth,
    }

    if (this.service.deliveryDate.amSelectingMonth && this.constants.datesAreSame(this.service.deliveryDate.startDate, this.constants.thisMonthStart)) {
      this.service.IsCurrentMonth = true;
    }
    else {
      this.service.IsCurrentMonth = false;
    }
  }


  setDeliveryDateType(type: string) {
    this.service.deliveryDate.dateType = type;
    this.getData();
  }

  includeTradeUnits(isTrue: boolean): void {
    this.service.includeTradeUnits = isTrue
    this.getData();
  }

  includeMotabUnits(isTrue: boolean): void {
    this.service.onlyMotabUnits = false;
    this.service.includeMotabUnits = isTrue;
    this.getData();
  }

  onlyMotabUnits(isTrue:boolean): void {
    this.service.includeMotabUnits = false;
    this.service.onlyMotabUnits = isTrue;
    this.getData();
  }

  onUpdateFranchises(franchises: string[]) {
    this.service.franchises = franchises
    this.getData()
  }



  selectLateCostOption(lateCostOption: LateCostOption) {
    this.service.lateCostOption = lateCostOption;
    this.getData()
  }

  selectOrderOption(orderOption: OrderOption) {
    this.service.orderOption = orderOption;
    this.getData()
  }

  selectDepartment(department: Department) {
    this.service.department = department;
    this.service.vehicleTypeIds = this.constants.VehicleTypes.filter(x => this.service.department.VehicleTypeTypes.includes(x.Type)).map(x => x.Id);
    this.getData();
  }

  selectReportType(reportType: SalesPerformanceReportType): void {

    if(reportType===this.service.salesPerformanceReportType){return;} //no change
    

    this.service.salesPerformanceReportType = reportType;
    
    //if coming back off custom, ensure we reset everything to normal dates
    if (this.service.salesPerformanceReportType === 'Custom' && reportType !== 'Custom') {
      this.service.deliveryDate = this.defaultDeliveryDateOptions()
      this.service.orderDate = this.defaultOrderDateOptions()
    }

    //if going to vs-last year or vs-budget ensure we reset franchises
    if (reportType !== 'Custom') {
      this.service.franchises = this.constants.clone(this.constants.FranchiseCodes)
    }

    this.getData();
  }



  selectMonth(object: DateSelectionObject, date: Date) {
    this.constants.selectMonth(object, date)
    object.lastSelectedMonthStartDate = object.startDate;
    object.amSelectingAnytime = false;
    object.amSelectingDay = false;
    object.amSelectingMonth = true;
    object.amSelectingWeek = false;
    object.amSelectingCustom = false;
    this.calculateDays();
    this.getData();
  }

  selectWeek(object: DateSelectionObject, date: Date) {
    this.constants.selectWeek(object, date);
    object.amSelectingAnytime = false;
    object.amSelectingDay = false;
    object.amSelectingMonth = false;
    object.amSelectingWeek = true;
    object.amSelectingCustom = false;
    this.getData();
  }

  selectDay(object: DateSelectionObject, date: Date) {
    this.constants.selectDay(object, date);
    object.amSelectingAnytime = false;
    object.amSelectingDay = true;
    object.amSelectingMonth = false;
    object.amSelectingWeek = false;
    object.amSelectingCustom = false;
    this.getData();
  }

  changeMonth(object: DateSelectionObject, changeAmount: number) {

    this.constants.changeMonth(object, changeAmount);


    object.amSelectingAnytime = false;
    object.amSelectingDay = false;
    object.amSelectingMonth = true;
    object.amSelectingWeek = false;
    object.amSelectingCustom = false;
    this.calculateDays();
    this.getData();
  }

  changeMonthButNotToPast(object: DateSelectionObject, changeAmount: number) {
    if (object.startDate.getMonth() == this.constants.appStartTime.getMonth()) { return; }
    this.constants.changeMonth(object, changeAmount);
    object.amSelectingAnytime = false;
    object.amSelectingDay = false;
    object.amSelectingMonth = true;
    object.amSelectingWeek = false;
    object.amSelectingCustom = false;
    this.calculateDays();
    this.getData();
  }

  changeWeek(object: DateSelectionObject, changeAmount: number) {
    this.constants.changeWeek(object, changeAmount);
    object.amSelectingAnytime = false;
    object.amSelectingDay = false;
    object.amSelectingMonth = false;
    object.amSelectingWeek = true;
    object.amSelectingCustom = false;
    this.getData();
  }

  changeDay(object: DateSelectionObject, changeAmount: number) {
    this.constants.changeDay(object, changeAmount);
    object.amSelectingAnytime = false;
    object.amSelectingDay = true;
    object.amSelectingMonth = false;
    object.amSelectingWeek = false;
    object.amSelectingCustom = false;
    this.getData();
  }

  selectAnytime(object: DateSelectionObject, isDeliveryDate: boolean) {


    object.startDate = new Date(2015, 0, 1);
    object.endDate = this.constants.addYears(this.constants.todayStart, 5);

    object.amSelectingAnytime = true;
    object.amSelectingDay = false;
    object.amSelectingMonth = false;
    object.amSelectingWeek = false;
    object.amSelectingCustom = false;

    this.getData();
  }


  makeMonths(offset?: number) {
    this.months = this.constants.makeMonths(this.monthsOffsetNumber, offset);
  }

  makeMonthsOnlyNowAndFuture(offset?: number) {

    let months = [];

    for (let i = 0; i < 4; i++) {
      months.push(this.constants.generateMonth(this.constants.appStartTime, offset + i));
    }

    this.months = [...new Set(months)];

  }

  makeWeeks(offset?: number) {
    this.weeks = this.constants.makeWeeks(this.weeksOffsetNumber, offset);
  }

  makeDays(offset?: number) {
    this.days = this.constants.makeDays(this.daysOffsetNumber, -8, 4, offset);
  }

  openDatePickerModal(isOrderDates: boolean) {
    let heading = 'Choose Delivery Dates';
    let startDate = this.service.deliveryDate.startDate;

    if (isOrderDates) {
      heading = 'Choose Order Dates'
      startDate = this.service.orderDate.startDate;
    }
    const modalRef = this.modalService.open(DatePickerModalComponent);
    //I give to modal
    modalRef.componentInstance.heading = heading;
    modalRef.componentInstance.startDate = startDate;

    modalRef.result.then((result) => { //I get back from modal
      if (result) {
        if (isOrderDates) {
          this.service.orderDate.startDate = result.startDate;
          this.service.orderDate.endDate = result.endDate;
          this.service.orderDate.amSelectingAnytime = false;
          this.service.orderDate.amSelectingDay = false;
          this.service.orderDate.amSelectingMonth = false;
          this.service.orderDate.amSelectingWeek = false;
          this.service.orderDate.amSelectingCustom = true;
          this.getData();
        } else {
          this.service.deliveryDate.startDate = result.startDate;
          this.service.deliveryDate.endDate = result.endDate;
          this.service.deliveryDate.amSelectingAnytime = false;
          this.service.deliveryDate.amSelectingDay = false;
          this.service.deliveryDate.amSelectingMonth = false;
          this.service.deliveryDate.amSelectingCustom = true;
          this.getData();
        }
      }
    });
  }

  abs(number: number): number {
    return Math.abs(number);
  }



}
