<div id="accountPage" class="accountPage">

  <div *ngIf="blur" id="mainLoginBox" class="inner boxShadow">

    <div *ngIf="constants.environment.languagePickerOnLogin" ngbDropdown id="languageDropdownHolder" class="d-inline-block language-select"> <!-- **here** -->
      <button class="btn btn-primary language-select-button"
          id="languageDropdown" ngbDropdownToggle>{{languageMsg}}</button>
      <div ngbDropdownMenu>
        <button ngbDropdownItem (click)="onLanguageChange('EN')"><img [src]="'/assets/imgs/flags/en.png'" alt="Language" height="14px" class="mr-2">English</button>
        <button ngbDropdownItem (click)="onLanguageChange('ES')"><img [src]="'/assets/imgs/flags/es.png'" alt="Language" height="14px" class="mr-2">Espanol</button>
      </div>
    </div>


    <noWebAccess *ngIf="showNoWebAccess"></noWebAccess>

    <ng-container *ngIf="!showNoWebAccess">
      <div class="imgHolder">
        <img src="assets/imgs/sparkLogoPng.png">
      </div>
      <section id="loginForm">
        <form [formGroup]="loginFormGroup" (ngSubmit)="submit()">
          <div *ngIf="loginError" class="validation-summary-errors text-danger">
            <ul>
              <li>{{incorrectPasswordMessage}}</li>
            </ul>
          </div>
          <div class="form-group">
            <div class="inputAndIcon">
              <input class="form-control" formControlName="email" (keydown.enter)="submit()" placeholder="{{emailPlaceholder}}"
                type="email" name="email" email="true" value="">
              <i class="fas fa-user-alt" aria-hidden="true"></i>
            </div>
          </div>
          <div class="form-group">
            <div class="inputAndIcon">
              <!--<fa-icon [icon]="icon.faLock" [fixedWidth]="true"></fa-icon>-->
              <i class="fas fa-lock" aria-hidden="true"></i>
              <input class="form-control" formControlName="password" (keydown.enter)="submit()" placeholder="{{passwordPlaceholder}}" type="password">
              <span class="field-validation-valid text-danger" data-valmsg-for="Password"
                data-valmsg-replace="true"></span>
            </div>
          </div>
        </form>
        <button class="btn btn-primary actionButton" [disabled]="loginFormGroup.pristine || loginFormGroup.invalid" (click)="submit()">
          {{ loginMessage }}
        </button>
        <div class="btn btn-danger actionButton">
          <input type="button" value="{{forgotPasswordMessage}}" class="btn btn-danger" (click)="redirectToForgotPassword()">
        </div>



      </section>
    </ng-container>
  </div>



</div>