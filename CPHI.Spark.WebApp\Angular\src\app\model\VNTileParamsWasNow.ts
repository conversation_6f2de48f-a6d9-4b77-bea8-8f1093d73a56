import {EventEmitter} from '@angular/core';
import {DashboardMeasure} from './DashboardMeasure';
import {VNTileParentMethodsWasNow} from './VNTileParentMethodsWasNow';
import {WasNowEnum} from '../pages/autoprice/leavingVehicles/leavingVehicleWasNowEnum';

export interface VNTileParamsWasNow {
   highlightChoices: DashboardMeasure[];
   filterChoices: DashboardMeasure[];
   highlightChoiceHasBeenMade: EventEmitter<void>;
   filterChoiceHasBeenMade: EventEmitter<void>;
   updateThisPicker: EventEmitter<void>;
   updateThisTile: EventEmitter<void>;
   parentMethods: VNTileParentMethodsWasNow;
}
