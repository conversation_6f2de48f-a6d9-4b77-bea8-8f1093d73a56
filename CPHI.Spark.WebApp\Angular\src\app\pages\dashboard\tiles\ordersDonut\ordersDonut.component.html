<div class="dashboard-tile-inner">
  <div [ngClass]="{'clickable':isClickableHeader()}" class="tileHeader" (click)="navigateToRatioOrders()">
    <div class="headerWords">
      <h4>
        <span *ngIf="departmentName=='New'">{{ constants.translatedText.Common_New }}</span>
        <span *ngIf="departmentName=='Used'">{{ constants.translatedText.Common_Used }}</span>
        <span *ngIf="departmentName=='Fleet'">{{ constants.translatedText.Common_Fleet }}</span>

        <ng-container *ngIf="constants.environment.customer == 'RRGSpain'; else notSpain">
          {{ constants.translatedText.Common_Orders | titlecase }}
        </ng-container>

        <ng-template #notSpain>
          <span class="visibleAboveSm">{{ constants.translatedText.Common_Orders }} {{ constants.translatedText.Common_For }}</span>
          <span>{{ constants.translatedText.Common_TheMonth }}</span>
        </ng-template>
      </h4>

      <div *ngIf="constants.environment.customer == 'RRGSpain'" class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
    </div>
  </div>


  <div id="innerContentHolder">
    <div id="chartHolder">
      <div (click)="navigateAway()" id="actualUnits">
        <h1><strong>{{data?.ThisMonth|cph:'number':0}}</strong></h1>
      </div>
      <div *ngIf="constants.environment.customer == 'RRGSpain' && this.departmentName == 'New'; else showBudget"></div>
      <ng-template #showBudget>
        <div id="budgetUnits">/{{data.ThisMonthTgt|cph:'number':0}}</div>
      </ng-template>
      <canvas id="donutCanvas" #donutCanvas></canvas>
    </div>

    <!-- <div id="bars">
      <div id="labels">
        <div class="amountActual clickable" (click)="navigateToOrderBook()">
          {{donutData.ActualMargin/1000|cph:'currency':0}}k</div>
        <div class="amountBudget">{{donutData.TargetMargin/1000|cph:'currency':0}}k</div>
      </div>
      <div id="barRange">
        <div id="actualBar" (click)="navigateToOrderBook()" class="bar" [ngStyle]="{'width.%':actualWidth}"></div>
        <div id="budgetBar" class="bar" [ngStyle]="{'width.%':budgetWidth}"></div>
      </div>
    </div> -->

    <!-- Spain additional measures - Absolutely positioned in corners -->
    <ng-container *ngIf="constants.environment.customer == 'RRGSpain'">
      <!-- Top left -->
      <div class="donut-extra-measure top-left">
        {{ constants.translatedText.Common_vsTarget }}
        <br>
        {{ (data.ThisMonth / data.ThisMonthTgt) | cph:'percent':0:true }}
      </div>

      <!-- Bottom left -->
      <div class="donut-extra-measure bottom-left">
        {{ constants.translatedText.Common_vsLastMonth }}
        <br>
        {{ data.MonthVsLastMonth | cph:'percent':0:true }}
      </div>

      <!-- Bottom right -->
      <div class="donut-extra-measure bottom-right">
        {{ constants.translatedText.Common_vsLastYear }}
        <br>
        {{ (data.ThisMonth / data.ThisMonthLY) | cph:'percent':0:true }}
      </div>

      <div *ngIf="constants.environment.donutShowLastYearUnits" class="lastYearUnits">
        {{ constants.translatedText.Common_LastYear }}: {{ data.ThisMonthLY }} {{ constants.translatedText.Common_Units }}
      </div>
    </ng-container>
  </div>
</div>