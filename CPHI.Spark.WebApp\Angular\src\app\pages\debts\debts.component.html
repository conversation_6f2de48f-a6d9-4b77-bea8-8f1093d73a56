<nav class="navbar">
  <nav class="generic" >
    <h4 id="pageTitle">
      <div>
        {{ selections.debts?.debtType?.translation }} &nbsp;
      </div>
      <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.Debtors"></sourceDataUpdate>
    </h4>

    <ng-container *ngIf="selections.debts">

      

   

      <!-- Debts report type -->
      <div class="buttonGroup">
        <button class="btn btn-primary" *ngFor="let debtType of service.debtTypes" [ngClass]="{
          active: debtType.value == selections.debts?.debtType.value
        }" (click)="chooseDebtType(debtType)">
          {{ debtType?.translation }}
        </button>
      </div>

      <sitePickerRRG
        *ngIf="selections.debts?.debtType.value != 'sites'"
        [allSites]="constants.sitesActiveSales"
        [sitesFromParent]="selections.selectedSites"
        (updateSites)="onUpdateSites($event)"
      >
      </sitePickerRRG>

      <!-- Ageing options -->

      <!-- Agedon -->
      <div ngbDropdown dropright class="d-inline-block"
        *ngIf="constants.environment.debts.showAgedOnPicker && (selections.debts?.debtType.value == 'sites' || selections.debts?.debtType.value == 'debts')">
        <button class=" btn btn-primary" ngbDropdownToggle>
          <span *ngIf="selections.debts.ageOnDueDate">{{constants.translatedText.Debts_AgedDebtsOn}}: {{constants.translatedText.Debts_DueDate}}</span>
          <span *ngIf="!selections.debts.ageOnDueDate">{{constants.translatedText.Debts_AgedDebtsOn}}: {{constants.translatedText.Debts_DocDate}}</span>
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <button (click)="setAgeOnDueDate(true)" ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>
            {{constants.translatedText.Debts_DueDate}}
          </button>
          <button (click)="setAgeOnDueDate(false)" ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>
            {{constants.translatedText.Debts_DocDate}}
          </button>
        </div>
      </div>

      <!-- As at -->
      <div ngbDropdown dropright class="d-inline-block">
        <button class=" btn btn-primary" ngbDropdownToggle>
          <span *ngIf="!selections.debts.ageAtMonthEnd">{{constants.translatedText.Debts_AsAt}}: {{constants.translatedText.Common_Now}}</span>
          <span *ngIf="selections.debts.ageAtMonthEnd">{{constants.translatedText.Debts_AsAt}}: {{constants.translatedText.Debts_MonthEnd}}</span>
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          <button (click)="setAsAtMonthEnd(false)" ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>
            {{constants.translatedText.Common_Now}}
          </button>
          <button (click)="setAsAtMonthEnd(true)" ngbDropdownToggle class="manualToggleCloseItem" ngbDropdownItem>
            {{constants.translatedText.Debts_MonthEnd}}
          </button>
        </div>
      </div>

    </ng-container>
  </nav>

  <nav class="pageSpecific" *ngIf="selections.debts">
    <!-- Search box for debts -->
    <div *ngIf="selections.debts.debtType.value == 'debts'">
      <form>
        <i class="fas fa-search"></i>
        <input placeholder="Search" class="form-control ml-2" type="text" [formControl]="filterDebts" />
      </form>
    </div>

    <!-- Search box for bonuses -->
    <div *ngIf="selections.debts.debtType.value == 'bonuses'">
      <form>
        <i class="fas fa-search"></i>
        <input placeholder="Search" class="form-control ml-2" type="text" [formControl]="filterBonuses" />
      </form>
    </div>
    <div></div>
  </nav>
</nav>

<!-- Main Page -->
<div *ngIf="selections.debts" class="content-new">
  <div class="content-inner-new">
  
    <ng-container *ngIf="selections.debts.debtType.value == 'sites' && selections.debts.siteSummaryRows">
      <!-- The sites debts table -->
      <sitesTable [isRegional]="false" (clickedSite)="selectSite($event)"  ></sitesTable>
      <div class="tableSpacer"></div>
      <!-- The regional debts table -->
      <sitesTable [isRegional]="true" (clickedSite)="selectSite($event)"></sitesTable>
    </ng-container>
    
    <!-- The debts table -->
    <!-- <div *ngIf="selections.debts.debtType.value == 'debts' && selections.debts.debtsFiltered" id="gridHolder"> -->
      
      <debtsTable *ngIf="selections.debts.debtType.value == 'debts' && selections.debts.debtsFiltered"></debtsTable>
    <!-- </div> -->
    
    <!-- The bonuses table -->
    <!-- <div *ngIf="selections.debts.debtType.value == 'bonuses' && selections.debts.bonusesFiltered" id="gridHolder"> -->
      <bonusesTable *ngIf="selections.debts.debtType.value == 'bonuses' && selections.debts.bonusesFiltered"></bonusesTable>
    <!-- </div> -->
  </div>

</div>