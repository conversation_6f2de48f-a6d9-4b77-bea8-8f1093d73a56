﻿using log4net;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.IO;
using System.Linq;
using Xunit;
using System.Diagnostics;
using System.Threading.Tasks;

namespace CPHI.Spark.WebScraper.Jobs
{
    public  class ActivityScrapeJob : IJob
    {

        private static IWebDriver _driver;  //So, we instantiate the IWebDriver object by using the ChromeDriver class and in the Dispose method, dispose of it.
        private static readonly ILog logger = LogManager.GetLogger(typeof(ActivityScrapeJob));

        private string customerName;
        private string fileDestination;
        //private string fileDestinationDev;

        public void Execute() { }

        public async Task Execute(IJobExecutionContext context)
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            fileDestination = ConfigService.FileDestination;
            //fileDestinationDev = ConfigService.FileDestinationDev;
            fileDestination = fileDestination.Replace("{destinationFolder}", "vindis");
            //fileDestinationDev = fileDestinationDev.Replace("{destinationFolder}", "vindis");
            customerName = "Vindis";

            try
            {
                ScraperMethodsService.ClearDownloadsFolder();
                var service = ChromeDriverService.CreateDefaultService();
                service.HostName = "127.0.0.1";

                ChromeOptions options = ScraperMethodsService.SetChromeOptions("VindisActivity", 9220);

                _driver = new ChromeDriver(service, options, TimeSpan.FromSeconds(130));

                GetActivities();

                _driver.Quit();
                _driver.Dispose();
                stopwatch.Stop();

            }
            catch (Exception e)
            {
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");
                //Emailer.SendMail("Vindis activity scraper failed", $"{e}");
                stopwatch.Stop();
                errorMessage = e.ToString();
                logger.Error($"Problem {e.ToString()}");

                _driver.Quit();
                _driver.Dispose();

                Process[] chromeInstances = Process.GetProcessesByName("chrome");

                //foreach (Process p in chromeInstances) p.Kill();
            }

            
            finally
            {
                Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                {
                    Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                    Project = "WebScraper",
                    Customer = "Vindis",
                    //Environment = env,
                    Task = this.GetType().Name,
                    StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                    EndDate = DateTime.UtcNow,
                    Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                    Notes = errorMessage,
                    HTML = string.Empty
                };
                await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }
        }










        public void GetActivities()
        {


            try
            {

                WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
                IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

                //go to login page
                _driver.Navigate().GoToUrl("https://enquirymax.net/account/logon");

                //wait for it to appear

                System.Threading.Thread.Sleep(1000);

                IWebElement loginButton = wait.Until(ExpectedConditions.ElementExists(By.Id("UserName")));

                System.Threading.Thread.Sleep(1000);

                Assert.Equal("Login - enquiryMAX", _driver.Title);

                System.Threading.Thread.Sleep(1000);

                WaitAndFind("//input [@Id='UserName']", false).SendKeys("james.smith");
                System.Threading.Thread.Sleep(1000);
                //WaitAndFind("//input [@Id='NextButton']", true);
                WaitAndFind("//input [@Id='UserName']", false).SendKeys(Keys.Tab);
                System.Threading.Thread.Sleep(1000);
                WaitAndFind("//input [@Id='Password']", false).SendKeys(ConfigService.EnquiryMAXPassword);
                System.Threading.Thread.Sleep(1000);
                WaitAndFind("//input [@Id='SubmitButton']", true);
                System.Threading.Thread.Sleep(1000);
                //wait for links to show
                IWebElement dashboardLink = wait.Until(ExpectedConditions.ElementExists(By.ClassName("top-navbar")));
                System.Threading.Thread.Sleep(1000);
                _driver.Navigate().GoToUrl("https://enquirymax.net/reporting/index?report=Sales/EnquiryTrackingFranchise#!/Sales/EnquiryTrackingFranchise");

                //Emailer.SendMail("Logged into enquiryMAX for activities scrape", "Have a nice day!");

                IWebElement datatableWrapper = wait.Until(ExpectedConditions.ElementExists(By.ClassName("repohead-title")));


                DateTime start = DateTime.Now;
                var exportBut = WaitAndFind("//input [@value='Export']", true);

                var yesBut = WaitAndFind("//a [contains(text(), 'Yes')]", true);

                string downloadPath = ConfigService.FileDownloadLocation;
                    
                ScraperMethodsService.WaitUntilFileDownloaded("Enquiry TrackingExport");
                logger.Info($"Succesfully saved down export file for report Activities");

                DirectoryInfo directory = new DirectoryInfo(downloadPath);
                FileInfo generatedFile = directory.GetFiles().Where(x => x.LastWriteTime > start && x.Name.Contains("Enquiry TrackingExport")).First();
                    
                string newFilePathAndName = downloadPath + @"\" + "Activities_SPKV8" + ".xlsx";
                //rename the file
                File.Move(generatedFile.FullName, newFilePathAndName);
                logger.Info($"Succesfully changed name from {generatedFile.FullName} to {newFilePathAndName}");
                //move to the incoming folder
                moveFile("Activities_SPKV8" + ".xlsx");

            }
            catch
            {

            }
        }




        public IWebElement WaitAndFind(string findXPath, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "ActivityScrape", findXPath, andClick);
            
            return result;
        }




        public void moveFile(string fileName)
        {
            string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
            string newFilePathAndName = $@"{fileDestination}{prefix}{fileName}";
            //string newFilePathAndNameDev = $@"{fileDestinationDev}{prefix}{fileName}";

            string path = ConfigService.FileDownloadLocation;
            string oldLocation = path + @"\" + fileName;
            
            File.Move(oldLocation, newFilePathAndName); //move them to incoming
            //File.Copy(newFilePathAndName, newFilePathAndNameDev); //copy to dev

            logger.Info($"Moved file from {oldLocation} to {newFilePathAndName}");
        }





    }
}
