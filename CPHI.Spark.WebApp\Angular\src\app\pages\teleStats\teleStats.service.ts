
import { EventEmitter, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe } from 'src/app/cph.pipe';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardService } from '../dashboard/dashboard.service';

import { TopBottomHighlightRule } from 'src/app/model/TopBottomHighlightRule';
import { TeleStatRow } from "../../model/TeleStatRow";
import { ColDef, ColGroupDef, ICellRendererParams, IRowNode, LineSparklineOptions, RowNode, ValueGetterParams } from 'ag-grid-community';
import { ExcelChoices, ExcelReportNames } from 'src/app/model/main.model';

@Injectable({
  providedIn: 'root'
})
export class TeleStatsService {

  public newDataEmitter: EventEmitter<boolean>;

  rowsToHide: string[];
  rowData: TeleStatRow[];
  topBottomHighlightsUsed: TopBottomHighlightRule[] = [];

  fourPriorWeeks: Date[];
  selectedWeek: Date;

  bySite: boolean = true;

  constructor(
    //public selections: SelectionsService,
    public constants: ConstantsService,
    public router: Router,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public modalService: NgbModal,
    public excel: ExcelExportService,
    public getDataMethods: GetDataMethodsService,
    public activeModal: NgbActiveModal,
    public dataMethods: GetDataMethodsService,
    public apiAccess: ApiAccessService,
    public getDataService: GetDataMethodsService,
    public dashboardService: DashboardService,
  ) {

  }

  initParams() {
    this.newDataEmitter = new EventEmitter();
    this.selectedWeek = this.constants.lastWeekStartDate;
  }

  getData() {

    let payload = [{ key: 'weekStart', value: this.selectedWeek.toISOString() }];

    this.apiAccess.get('api/TeleStats', 'GetTeleStatsForWeek', payload).subscribe((res: TeleStatRow[]) => {

      this.rowData = res;
      this.newDataEmitter.emit(true);
      this.selections.triggerSpinner.emit({ show: false })
    })
  }

  getExcelFullDownload(): void
  {

    let params: ExcelChoices = {
      DealIds: null,
      SheetName: "Telestats",
      WeekStart: this.selectedWeek

    }

    this.getDataMethods.getDetailedExcel(ExcelReportNames.TeleStats, params);
  }

  provideColDefs(isSiteTable: boolean) {

    let colDefs: (ColDef | ColGroupDef)[] = [];

    if (isSiteTable) {
      colDefs.push({
        headerName: this.constants.translatedText.Site,
        field: 'SiteName',
        colId: 'SiteName',
        type: 'labelSetFilter',
        rowGroup: true,
        rowGroupIndex: this.bySite ? 0 : 1,
        width: 100,
        hide: true
      },
      { headerName: 'Department', field: 'Department', type: 'labelSetFilter', rowGroup: true, rowGroupIndex: this.bySite ? 1 : 0, hide: true, },
      { headerName: 'Line', field: 'Line', type: 'labelSetFilter', rowGroup: this.bySite ? true : false, rowGroupIndex: 2, hide: true }
    
    );
    } else {
      colDefs.push({
        headerName: this.constants.translatedText.Region,
        field: 'RegionName',
        colId: 'RegionName',
        type: 'labelSetFilter',
        rowGroup: true,
        rowGroupIndex: 0,
        width: 100,
        hide: true
      },
      {
        headerName: this.constants.translatedText.Site,
        field: 'SiteName',
        colId: 'SiteName',
        type: 'labelSetFilter',
        rowGroup: true,
        rowGroupIndex: 1,
        width: 100,
        hide: true
      },
      { headerName: 'Department', field: 'Department', type: 'labelSetFilter', rowGroup: true, rowGroupIndex: 2, hide: true, },
      { headerName: 'Line', field: 'Line', type: 'labelSetFilter', rowGroup: true, rowGroupIndex: 3, hide: true }
    );
    }

    let toAdd: (ColDef | ColGroupDef)[] = [

      {
        headerName: 'Answer Rate',
        children: [
          {
            headerName: this.cphPipe.transform(this.fourPriorWeeks[3], 'shortDate', 0), valueGetter: (params) => this.getCellValue(params), field: 'WeekOnePercAnswered',
            colId: 'WeekOnePercAnswered', width: 60, type: 'percent', cellRenderer: (params) => { return this.teleStatsRenderer(params); }
          },

          {
            headerName: this.cphPipe.transform(this.fourPriorWeeks[2], 'shortDate', 0), valueGetter: (params) => this.getCellValue(params), field: 'WeekTwoPercAnswered',
            colId: 'WeekTwoPercAnswered', width: 60, type: 'percent', cellRenderer: (params) => { return this.teleStatsRenderer(params); }
          },

          {
            headerName: this.cphPipe.transform(this.fourPriorWeeks[1], 'shortDate', 0), valueGetter: (params) => this.getCellValue(params), field: 'WeekThreePercAnswered',
            colId: 'WeekThreePercAnswered', width: 60, type: 'percent', cellRenderer: (params) => { return this.teleStatsRenderer(params); }
          },

          {
            headerName: this.cphPipe.transform(this.fourPriorWeeks[0], 'shortDate', 0), valueGetter: (params) => this.getCellValue(params), field: 'WeekFourPercAnswered',
            colId: 'WeekFourPercAnswered', width: 60, type: 'percent', cellRenderer: (params) => { return this.teleStatsRenderer(params); }
          },


          {
            headerName: 'Avg',
            valueGetter: (params) => this.getCellValue(params),
            field: 'FourWeekAverageAnswered', colId: 'FourWeekAverageAnswered', width: 65, type: 'percent', cellRenderer: (params) => { return this.teleStatsRenderer(params); },
          },

          {
            headerName: 'Current Week', children: [
              {
                headerName: 'Calls', field: 'Calls', colId: 'Calls', width: 80, type: 'number',
                valueGetter: (params) => this.getCellValue(params),
                cellRenderer: (params) => { return this.teleStatsRenderer(params); }
              },

              {
                headerName: 'Answered', field: 'Answered', colId: 'Answered', width: 80, type: 'number',valueGetter: (params) => this.getCellValue(params),
                cellRenderer: (params) => { return this.teleStatsRenderer(params); }
              },

              {
                headerName: 'Answer %', field: 'PercentageAnswered', colId: 'PercentageAnswered', width: 80, type: 'percent',valueGetter: (params) => this.getCellValue(params),
                cellRenderer: (params) => { return this.teleStatsRenderer(params); }
              },

              {
                headerName: 'vs Last Week', field: 'VsLastWeek', colId: 'VsLastWeek', width: 80, type: 'label', cellClass: 'ag-right-aligned-cell',valueGetter: (params) => this.getCellValue(params),
                cellRenderer: (params) => { return this.teleStatsRenderer(params); }
              },

              {
                headerName: 'vs 4 week', field: 'Vs4Week', colId: 'Vs4Week', width: 80, type: 'label', cellClass: 'ag-right-aligned-cell',valueGetter: (params) => this.getCellValue(params),
                cellRenderer: (params) => { return this.teleStatsRenderer(params); }
              },

              {
                headerName: 'Answered < 15', field: 'PercentageAnsweredInLessThan15', colId: 'PercentageAnsweredInLessThan15', width: 80, type: 'percent',valueGetter: (params) => this.getCellValue(params),
                cellRenderer: (params) => { return this.teleStatsRenderer(params); }
              },

              {
                headerName: 'Graph',
                field: 'Last5',
                colId: 'Last5',
                valueGetter: (params) => { return this.getValueForGraph(params); },
                cellRenderer: 'agSparklineCellRenderer',
                cellRendererParams: this.provideSparkLineParams(),
                width: 200,
              },
            ]
          },
        ],

      },

      {
        headerName: 'Abandon Rate',
        children: [
          {
            headerName: '', children: [
              { headerName: 'Calls', field: 'Abandoned', colId: 'Abandoned', width: 80, type: 'number', 
              cellRenderer: (params) => { return this.teleStatsRenderer(params); } ,valueGetter: (params) => this.getCellValue(params),
            },
            
              { headerName: '%', field: 'PercentageAbandoned', colId: 'PercentageAbandoned', width: 80, type: 'percent', 
              cellRenderer: (params) => { return this.teleStatsRenderer(params); } ,valueGetter: (params) => this.getCellValue(params),
            },
            
            ]
          },
        ],

      },

      {
        headerName: 'Transferred Calls Rate', children: [
          {
            headerName: '',
            children: [
              { headerName: 'DTMF', field: 'TransferredDTMF', colId: 'TransferredDTMF', width: 90, type: 'number', 
              cellRenderer: (params) => { return this.teleStatsRenderer(params); } ,valueGetter: (params) => this.getCellValue(params),
            },
            
              { headerName: 'Busy', field: 'TransferredBusy', colId: 'TransferredBusy', width: 90, type: 'number', 
              cellRenderer: (params) => { return this.teleStatsRenderer(params); } ,valueGetter: (params) => this.getCellValue(params),
            },
            
              { headerName: 'No Answer', field: 'TransferredNoAnswer', colId: 'TransferredNoAnswer', width: 90, type: 'number', 
              cellRenderer: (params) => { return this.teleStatsRenderer(params); } ,valueGetter: (params) => this.getCellValue(params),
            },
            
              { headerName: 'Total', field: 'Transferred', colId: 'Transferred', width: 90, type: 'number', 
              cellRenderer: (params) => { return this.teleStatsRenderer(params); },valueGetter: (params) => this.getCellValue(params),
             }
             
            ]
          }
        ]
      },

      {
        headerName: 'Durations', children: [
          {
            headerName: '',
            children: [
              { headerName: 'First Call', field: 'FirstCallDuration', colId: 'FirstCallDuration', width: 75, type: 'time', headerClass: 'rightAlignHeader', cellClass: 'ag-right-aligned-cell', 
              cellRenderer: (params) => { return this.teleStatsRenderer(params); } ,valueGetter: (params) => this.getCellValue(params),
            },
            
              { headerName: 'Calls & Hold', field: 'CallsAndHold', colId: 'CallsAndHold', width: 75, type: 'time', headerClass: 'rightAlignHeader', cellClass: 'ag-right-aligned-cell', 
              cellRenderer: (params) => { return this.teleStatsRenderer(params); } ,valueGetter: (params) => this.getCellValue(params),
            },
            
            ]
          }
        ]
      },

    ];

    // Add additional column definitions to the colDefs array
    colDefs.push(...toAdd);

    return colDefs;
  }

  provideSparkLineParams(): LineSparklineOptions {
    return {
      sparklineOptions: {
        type: 'line',
        line: {
          stroke: '#4871D9',
          strokeWidth: 2,
        },
        padding: {
          top: 5,
          bottom: 5,
        },
        highlightStyle: {
          size: 7,
          fill: 'rgb(255,0,0)',
          strokeWidth: 3,
        },
      }
    } as LineSparklineOptions
  }


  teleStatsRenderer(params: ICellRendererParams) {

    // Initialize variable to store the value
    //let val:string|number;

    if (params.node.key == 'All Lines' || params.node.key == 'All Departments' || params.node.key == 'All Sites') {
      return;
    }

    if (params.colDef.field == 'VsLastWeek' || params.colDef.field == 'Vs4Week') {
      // Determine the formatted value based on 'percent' type
      return this.vsRenderer(params, params.value);
    }
    else if (params.colDef.field == 'PercentageAbandoned') {
      // Determine the formatted value based on 'percent' type
      return this.percentageAbandonedRenderer(params, params.value);
    }
    else if (params.colDef.field == 'PercentageAnswered') {
      // Determine the formatted value based on 'percent' type
      return this.percentageAnsweredRenderer(params, params.value);
    }
    else {
      if (params.colDef.type == 'percent') {
        return (isNaN(params.value as number)) ? '' : this.cphPipe.transform(params.value, 'percent', 0)
      }
      else if (params.colDef.type == 'time') {
        return (isNaN(params.value as number)) ? '' : this.cphPipe.transform(params.value, 'timeDuration', 0)
      }
      else {
        return (isNaN(params.value as number)) ? '' : this.cphPipe.transform(params.value, 'number', 0);
      }
    }

  }

  private getCellValue(params: ValueGetterParams) : any {

    if (params.node.rowPinned) {
      // For pinned rows, access the data directly since they might not conform to the standard data structure
      return params.data[params.colDef.field];
    }
  
    let value = null;
    if (params.node) { value = params.node[params.colDef.field] }

    var isRegionTable = !params.context.thisComponent.isSiteTable;

    let val: string | number;

    let leafChildren: IRowNode<any>[] = this.orderLeafChildren(params.node, isRegionTable);

    if(params.node.group)
    {
      val = leafChildren[leafChildren.length - 1].data[params.colDef.field];

      // if(!isRegionTable)
      // {
      //   console.log(params.colDef.field, val, leafChildren, '0--')
      // }
    }
    else {
      // Use the current cell's value
      val = value;
    }

    return val;
  }

  // Ensures that if it is a Total child, it will be at the end of the child array
  orderLeafChildren(node, isRegionalTable: boolean) {
    // Define a function to check if a node is special
    const isSpecial = (node) => {
        if (isRegionalTable) {
            return node.data?.SiteName === 'All Sites';
        } else {
            return node.data?.Department === 'All Departments' || node.data?.Line === 'All Lines';
        }
    };

    // Assuming node.allLeafChildren is the array to be sorted
    return node.allLeafChildren.sort((a, b) => {
        const aIsSpecial = isSpecial(a);
        const bIsSpecial = isSpecial(b);

        // Sort to move special cases to the end
        if (aIsSpecial && !bIsSpecial) {
            return 1; // 'a' goes after 'b'
        } else if (!aIsSpecial && bIsSpecial) {
            return -1; // 'a' goes before 'b'
        }
        return 0; // Preserve the order of 'a' and 'b' if both are special or neither are
    });
  }


  // This is only for the line graph
  getValueForGraph(params: any) {

    if (params.node.key == 'All Lines' || params.node.key == 'All Departments' || params.node.key == 'All Sites') {
      return;
    };

    // Initialize variable to store the value
    let val;

    // Check if the row is a group row
    if (params.node.group) {
      // Extract the last leaf child's value for the column
      val = params.node.allLeafChildren[params.node.allLeafChildren.length - 1].data[params.colDef.field];
    }
    else if (params.node.rowPinned == "bottom") {
      val = params.data.Last5;
    }
    else {
      // Use the current cell's value
      val = params.value;
    }

    return val;
  }

  vsRenderer(params: ICellRendererParams, val: string | number) {
    let formattedValue = (params.colDef.type === 'percent' && !isNaN(val as number)) ? this.cphPipe.transform(val, 'percent', 0) : val;

    // Determine the text color based on the value
    let textColor;

    if (val == 'Improved' || val == 'Same') {
      textColor = 'green';
    }
    else {
      textColor = 'red';
    }

    // Return the formatted value wrapped in a span with the background color
    return `<span style="color: ${textColor}; font-weight: bold;">${formattedValue}</span>`;
  }

  private percentageAnsweredRenderer(params: ICellRendererParams, val: string | number) {
    let formattedValue = (params.colDef.type === 'percent' && !isNaN(val as number)) ? this.cphPipe.transform(val, 'percent', 0) : val;

    // Determine the text color based on the value
    let textColor = (val as number < 0.70) ? 'red' : 'green';

    // Return the formatted value wrapped in a span with the background color
    return `<span style="color: ${textColor}; font-weight: bold;">${formattedValue}</span>`;
  }

  private percentageAbandonedRenderer(params: ICellRendererParams, val: string | number) {
    let formattedValue = (params.colDef.type === 'percent' && !isNaN(val as number)) ? this.cphPipe.transform(val, 'percent', 0) : val;

    // Determine the text color based on the value
    let textColor = (val as number > 0.10) ? 'red' : 'green';

    // Return the formatted value wrapped in a span with the background color
    return `<span style="color: ${textColor}; font-weight: bold;">${formattedValue}</span>`;
  }

  provideRowData(isSiteTable : boolean) {

    // SITE TABLE
    if(isSiteTable)
    {
      if (this.rowData && this.bySite) {
        return this.rowData.filter(x => !x.IsTotal && x.IsSiteTableRow);
      }
      else {
        return this.rowData.filter(x => !x.IsTotal && !x.IsSiteTableRow);
      }
    }
    // REGIONAL TABLE - ALWAYS SAME
    else
    {
      return this.rowData.filter(x => !x.IsTotal && x.IsSiteTableRow);
    }


    return;
  }

}




