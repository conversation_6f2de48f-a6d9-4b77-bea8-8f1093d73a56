export enum ValuationCostingFieldEnum {
    'PricePosition', 'Sales', 'Cost', 'Valet', 'SpareKey', 'MOT', 'MOTAdvisory', 'Servicing', 'Paint', 'Tyres', 'Warranty', 'Parts', 'AdditionalMech', 'Fee', 'Delivery','Other','IsVatQualifying','VatCost', 'Valuation', 'Profit'
}

export interface ValuationCostingConstructParams {
    Valn: number;
    Sales: number;
    Profit: number;
    Valet?: number;
    SpareKey?: number;
    MOT?: number;
    MOTAdvisory?: number;
    Servicing?: number;
    Paint?: number;
    Tyres?: number;
    Warranty?: number;
    Parts?: number;
    AdditionalMech?: number;
    Fee?: number;
    Delivery?: number;
    Other?: number;
    IsVatQualifying?: boolean;
    VatCost?: number;
}

export class ValuationCostingDTO {

    constructor(params: ValuationCostingConstructParams) {
        this.Valuation = params.Valn;
        this.Sales = params.Sales;
        this.Profit = params.Profit;
        let costs = 0;
        if (params.Valet) {this.Valet = params.Valet; costs += params.Valet }
        if (params.SpareKey) {this.SpareKey = params.SpareKey; costs += params.SpareKey}
        if (params.MOT) {this.MOT = params.MOT; costs += params.MOT}
        if (params.MOTAdvisory) {this.MOTAdvisory = params.MOTAdvisory; costs += params.MOTAdvisory}
        if (params.Servicing) {this.Servicing = params.Servicing; costs += params.Servicing}
        if (params.Paint) {this.Paint = params.Paint; costs += params.Paint}
        if (params.Tyres) {this.Tyres = params.Tyres; costs += params.Tyres}
        if (params.Warranty) {this.Warranty = params.Warranty; costs += params.Warranty}
        if (params.Parts) {this.Parts = params.Parts; costs += params.Parts}
        if (params.AdditionalMech) {this.AdditionalMech = params.AdditionalMech; costs += params.AdditionalMech}
        if (params.Fee) {this.Fee = params.Fee; costs += params.Fee}
        if (params.Delivery) {this.Delivery = params.Delivery; costs += params.Delivery}
        if (params.Other) {this.Other = params.Other; costs += params.Other}
        if (params.IsVatQualifying == false || params.IsVatQualifying == null || params.IsVatQualifying == undefined) {
            this.IsVatQualifying = false;
            this.VatCost = 0;
        }
        else {
            this.IsVatQualifying = params.IsVatQualifying;
            this.VatCost = params.VatCost;
        }

        this.Cost = params.Sales - params.Profit - costs - (params.VatCost || 0);
    }
    Valuation: number;  // ?? not sure what this is for


    Sales: number;
    Valet: number = 0;
    SpareKey: number = 0;
    MOT: number = 0;
    MOTAdvisory: number = 0;
    Servicing: number = 0;
    Paint: number = 0;
    Tyres: number = 0;
    Warranty: number = 0;
    Parts: number = 0;
    AdditionalMech: number = 0;
    Fee: number = 0;
    Delivery: number = 0;
    Other: number = 0;

    IsVatQualifying: boolean = false;
    VatCost: number = 0;


    Cost: number;
    Profit: number;
}


