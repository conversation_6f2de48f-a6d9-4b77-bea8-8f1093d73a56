﻿using CPHI.WebScraper.ViewModel;
using log4net;
using OpenQA.Selenium;

using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using Quartz;
using SeleniumExtras.WaitHelpers;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

using WinSCP;

namespace CPHI.Spark.WebScraper.Jobs
{

    public class DistrinetScrapeJob : IJob
    {
        private DateTime startTime;
        private ChromeDriver _driver;
        private static readonly ILog logger = LogManager.GetLogger(typeof(DistrinetScrapeJob));

        private const string incomingFileExt = ".zip";

        private TransferOptions transferOptions;

        private string link = "https://dcs.renault.com/pdi/control/login";

        public void Execute() { }
        public async Task Execute(IJobExecutionContext context)
        {
            try
            {

                List<DistrinetScraperRun> scraperRuns = CreateScraperRuns();

                foreach (DistrinetScraperRun run in scraperRuns)
                {
                    KillChrome();

                    ChromeOptions options = SetChromeOptions();

                    options.AddUserProfilePreference("download.default_directory", @ConfigService.FileDownloadLocation);

                    // Requires different Chrome profile for each run
                    options.AddArgument("user-data-dir=C:\\Users\\<USER>\\Documents\\ChromeProfiles\\UserData");
                    options.AddArgument($"profile-directory={run.Profile}");

                    _driver = new ChromeDriver(AppDomain.CurrentDomain.BaseDirectory, options, TimeSpan.FromSeconds(130));
                    _driver.Manage().Cookies.DeleteAllCookies();
                    System.Threading.Thread.Sleep(100);

                    List<string> brands = new List<string>();
                    if (run.Name == "Madrid") { brands = new List<string>() { "ESP:RENAULT", "ESD:DACIA" }; }
                    if (run.Name == "MadridAlpine") { brands = new List<string>() { "ESP:ALPINE" }; }
                    if (run.Name == "Levante") { brands = new List<string>() { "ESP:RENAULT", "ESD:DACIA", "ESP:ALPINE" }; }

                    _driver.Navigate().GoToUrl(run.Link);

                    IWebElement siteDropdown = WaitAndFind("//form/table/tbody/tr[3]/td/select", 1, true);
                    SelectElement siteDropdownSelect = new SelectElement(siteDropdown);
                    siteDropdownSelect.SelectByText(run.Site);

                    // Hit submit
                    WaitAndFind("//form/table/tbody/tr[4]/td/input", 1, true);

                    // Hit Renault
                    _driver.SwitchTo().DefaultContent();
                    _driver.SwitchTo().Frame(_driver.FindElement(By.XPath("/html/body/div[1]/div/table[2]/tbody/tr/td[2]/iframe")));

                    new WebDriverWait(_driver, TimeSpan.FromSeconds(20))
                        .Until(ExpectedConditions.ElementToBeClickable(By.XPath("/html/body/table/tbody/tr[4]/td[1]/a"))).Click();

                    _driver.Navigate().GoToUrl(link);

                    // If we need to get past a submit button
                    if (_driver.FindElements(By.XPath("//form/table/tbody/tr[4]/td/input")).Count() > 0)
                    {
                        siteDropdown = WaitAndFind("//form/table/tbody/tr[3]/td/select", 1, true);
                        siteDropdownSelect = new SelectElement(siteDropdown);
                        siteDropdownSelect.SelectByText(run.Site);
                        WaitAndFind("//form/table/tbody/tr[4]/td/input", 1, true);
                    }

                    foreach (string brand in brands)
                    {
                        GoToPage(brand);
                        GetAndUploadStockReport(brand, run.Name);
                    }

                    foreach (string brand in brands)
                    {
                        logger.Info($"starting brand {brand}");
                        GoToPage(brand);
                        logger.Info($"gone to page for brand {brand}");
                        GetAndUploadOrdersReport(brand, run.Name);
                        logger.Info($"uploaded orders report for brand {brand}");
                    }

                    foreach (string brand in brands)
                    {
                        GoToPage(brand);
                        GetAndUploadBookReport(brand, run.Name);
                    }

                    _driver.Quit();
                    _driver.Dispose();
                }


                logger.Info("Finished");
            }

            catch (Exception e)
            {
                logger.Error($"General Problem {e.ToString()}");
                EmailerService eService = new EmailerService();
                await eService.SendMail($"❌ FAILURE {this.GetType().Name} ", $"{e.StackTrace}");
            }
            
        }

        private List<DistrinetScraperRun> CreateScraperRuns()
        {
            List<DistrinetScraperRun> results = new List<DistrinetScraperRun>();

            results.Add(new DistrinetScraperRun()
            {
                Name = "Madrid",
                Link = "https://dcs.renault.com/rnz_div/formLogin?urlTarget=https%3A%2F%2Fdcs.renault.com%2Frne_hpa2%2F",
                Site = "rrgcentral - ps11415 - PROCTER RICHARD",
                Profile = "Profile 1"
            });

            results.Add(new DistrinetScraperRun()
            {
                Name = "MadridAlpine",
                Link = "https://dcs.renault.com/rnz_div/formLogin?urlTarget=https%3A%2F%2Fdcs.renault.com%2Frne_hpa2%2F",
                Site = "rrgavenidaburgos - ds55213 - PROCTER RICHARD",
                Profile = "Profile 2"
            });

            results.Add(new DistrinetScraperRun()
            {
                Name = "Levante",
                Link = "https://dcs.renault.com/rnz_div/formLogin?urlTarget=https%3A%2F%2Fdcs.renault.com%2Frne_hpa2%2F",
                Site = "rrgpalaciocongresos - ds55209 - PROCTER RICHARD",
                Profile = "Profile 3"
            });

            return results;
        }

        private void KillChrome()
        {
            Process[] chromeInstances = Process.GetProcessesByName("chrome");
            foreach (Process p in chromeInstances) p.Kill();

            Process[] chromeDriverInstances = Process.GetProcessesByName("chromedriver");
            foreach (Process p in chromeDriverInstances) p.Kill();
        }

        private ChromeOptions SetChromeOptions()
        {
            logger.Info("Starting..");

            startTime = DateTime.Now;

            ChromeOptions options = new ChromeOptions();
            options.Proxy = null;
            options.AddArguments("--start-maximized");
            options.AddArgument("no-sandbox");
            options.AddUserProfilePreference("profile.default_content_setting_values.automatic_downloads", 1);

            return options;
        }

        private void GoToPage(string brand)
        {
            //go to login page
            _driver.Navigate().GoToUrl(link);

            // select the drop down list
            var education = _driver.FindElement(By.Name("perimetre"));

            //create select element object 
            var selectElement = new SelectElement(education);

            //select by value
            selectElement.SelectByValue(brand);

            //WaitAndFind("/html/body/form/table/tbody/tr/td/table/tbody/tr[2]/td[2]/table/tbody/tr[4]/td[2]/select", 100, true);

            //WaitAndFind("/html/body/form/table/tbody/tr/td/table/tbody/tr[2]/td[2]/table/tbody/tr[4]/td[2]/select/option[@value='ESD:DACIA']", 100, true);

            // Click okay
            WaitAndFind("/html/body/form/table/tbody/tr/td/table/tbody/tr[2]/td[2]/table/tbody/tr[5]/td/input", 100, true);

            System.Threading.Thread.Sleep(1000);
        }

        private void GetAndUploadStockReport(string brand, string runName)
        {
            try
            {
                string page = "https://dcs.renault.com/pdi/control/recherche?typeRecherche=RechercheVehicule";

                _driver.Navigate()
                    .GoToUrl(page);

                //// Busqueda de vehiculos
                //WaitAndFind("/html/body/div/span[2]", 150, true);

                // Busqueda de vehiculos -> Open site filter 
                WaitAndFind("//*[@id='idDivcomptes_stk']/div/button/div/div/div", 100, true);

                // Busqueda de vehiculos -> Open site filter -> Select all sites
                WaitAndFind("/html/body/div[30]/div/div[2]/div/button[1]", 100, true); /// Here

                // Busqueda de vehiculos -> Open filters -> Select Todos -> Click off filter
                WaitAndFind("/html/body/form/div[2]/div[2]/div[2]/div[1]/div[@class='bandeauBlock']", 100, true);

                // Busqueda de vehiculos -> Open filters -> Select Todos -> Click off filter -> Lista
                WaitAndFind("/html/body/form/div[2]/div[2]/div[1]/div[2]/button[@class='actionImage']", 100, true);

                System.Threading.Thread.Sleep(3000);

                //maybe run the report?
                WaitAndFind("/html/body/div[4]/div/div[5]/form/div[1]/div[7]/a/img[@class='imgNav']", 100, true);

                string oldFilepath = $"{ConfigService.FileDownloadLocation}\\vehicules.xlsx";
                WaitUntilFileDownloaded(oldFilepath);

                PutFileToFTPAndMoveToProcessed(oldFilepath, brand, "DistStock", runName);

            }
            catch(Exception ex)
            {
                logger.Error($"Error in GetAndUploadStockReport for brand{brand} runName {runName}", ex);
            }

        }

        private async void GetAndUploadOrdersReport(string brand, string runName)
        {
            try
            {
                string page = "https://dcs.renault.com/pdi/control/recherche?typeRecherche=RechercheCommande";

                _driver.Navigate().GoToUrl(page);

                //// Busqueda de vehiculos
                //WaitAndFind("/html/body/div/span[2]", 150, true);

                // Busqueda de pedidos -> Open filters 
                WaitAndFind("//*[@id='idDivcomptes_cde']/div/button/div/div/div", 100, true);

                // Busqueda de pedidos -> Open filters -> Select Todos
                WaitAndFind("/html/body/div[30]/div/div[2]/div/button[1]", 100, true);

                // Busqueda de vehiculos -> Open filters -> Select Todos -> Click off filter
                //WaitAndFind("/html/body/form/div[2]/div[2]/div[2]/div[1]/div[@class='bandeauBlock']", 100, true);

                // Cancelados
                WaitAndFind("//*[@id='idselectDateAnnulation_cde']/table/tbody/tr/td[2]/div/button/div/div/div", 100, true);

                // Cancelados -> Todos
                WaitAndFind("//*[@id='idselectDateAnnulation_cde']/table/tbody/tr/td[2]/div/div/div/ul/li[3]/a/span[2]", 100, true);

                // Entregados
                WaitAndFind("//*[@id='idselectDateLivraison_cde']/table/tbody/tr/td[2]/div/button/div/div/div", 100, true);

                // Entregados -> Todos
                WaitAndFind("//*[@id='idselectDateLivraison_cde']/table/tbody/tr/td[2]/div/div/div/ul/li[3]/a", 100, true);

                DateTime date = DateTime.Today;
                var firstDayOfMonth = new DateTime(date.Year, date.Month, 1);
                var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

                _driver.FindElement(By.Name("dateCreationCdeMin_cde")).SendKeys(firstDayOfMonth.ToString("ddMMyyyy"));
                System.Threading.Thread.Sleep(400);
                _driver.FindElement(By.Name("dateCreationCdeMax_cde")).SendKeys(lastDayOfMonth.ToString("ddMMyyyy"));

                // Busqueda de vehiculos -> Open filters -> Select Todos -> Click off filter -> Lista
                WaitAndFind("/html/body/form/div[2]/div[2]/div[1]/div[2]/button[@class='actionImage']", 100, true);

                System.Threading.Thread.Sleep(3000);

                IWebElement noResults = WaitAndFind("/html/body/div[7]/div/div[5]/form/div[2]", 100, false);

                string noResultsText = noResults.Text;

                if (!noResultsText.Contains("lista está vacía"))
                {
                    WaitAndFind("/html/body/div[7]/div/div[5]/form/div[1]/div[7]/a/img[@class='imgNav']", 100, true);
                    string oldFilepath = $"{ConfigService.FileDownloadLocation}\\commandes.xlsx";
                    WaitUntilFileDownloaded(oldFilepath);
                    PutFileToFTPAndMoveToProcessed(oldFilepath, brand, "DistOrders", runName);
                }
                // No results - send blank file
                else
                {
                    //string noOrdersFoundFilePath = $"{ConfigService.FileDownloadLocation}\\NoOrders-DistOrders.xlsx";
                    //File.Create(noOrdersFoundFilePath).Dispose();
                    //PutFileToFTPAndMoveToProcessed(noOrdersFoundFilePath, brand, "DistOrders", runName);

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "RRG Spain",
                        Environment = "Prod",
                        Task = "DistrinetOrdersJob",
                        StartDate = DateTime.UtcNow,
                        EndDate = DateTime.UtcNow,
                        Status = "Pass",
                        Notes = "No distrinet orders" + brand + runName,
                        HTML = string.Empty
                    };

                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);

                }

            }
            catch (Exception ex)
            {
                logger.Error($"Error in GetAndUploadOrdersReport for brand{brand} runName {runName}", ex);
            }

        }

        private async void GetAndUploadBookReport(string brand, string runName)
        {
            try
            {
                string page = "https://dcs.renault.com/pdi/control/recherche?typeRecherche=RechercheCommande";

                _driver.Navigate().GoToUrl(page);

                //// Busqueda de vehiculos
                //WaitAndFind("/html/body/div/span[2]", 150, true);

                // Busqueda de pedidos -> Open filters 
                WaitAndFind("//*[@id='idDivcomptes_cde']/div/button/div/div/div", 100, true);

                // Busqueda de pedidos -> Open filters -> Select Todos
                WaitAndFind("/html/body/div[30]/div/div[2]/div/button[1]", 100, true);

                // Busqueda de vehiculos -> Open filters -> Select Todos -> Click off filter
                WaitAndFind("/html/body/form/div[2]/div[2]/div[2]/div[1]/div[@class='bandeauBlock']", 100, true);

                DateTime date = DateTime.Today;
                var startPoint = new DateTime(date.Year, date.Month, 1).AddYears(-2);
                var endPoint = new DateTime(date.Year, date.Month, 1).AddMonths(1).AddDays(-1);

                _driver.FindElement(By.Name("dateCreationCdeMin_cde")).SendKeys(startPoint.ToString("ddMMyyyy"));
                System.Threading.Thread.Sleep(400);
                _driver.FindElement(By.Name("dateCreationCdeMax_cde")).SendKeys(endPoint.ToString("ddMMyyyy"));

                // Busqueda de vehiculos -> Open filters -> Select Todos -> Click off filter -> Lista
                WaitAndFind("/html/body/form/div[2]/div[2]/div[1]/div[2]/button[@class='actionImage']", 100, true);

                System.Threading.Thread.Sleep(3000);

                IWebElement noResults = WaitAndFind("/html/body/div[7]/div/div[5]/form/div[2]", 100, false);

                string noResultsText = noResults.Text;

                if (!noResultsText.Contains("lista está vacía"))
                {
                    WaitAndFind("/html/body/div[7]/div/div[5]/form/div[1]/div[7]/a/img[@class='imgNav']", 100, true);
                    string oldFilepath = $"{ConfigService.FileDownloadLocation}\\commandes.xlsx";
                    WaitUntilFileDownloaded(oldFilepath);
                    PutFileToFTPAndMoveToProcessed(oldFilepath, brand, "DistBook", runName);
                }
                // No results - send blank file
                else
                {
                    //string prefix = DateTime.Now.ToString("yyyyMMdd_HHmmss") + "-";
                    //string noOrdersFoundFilePath = $"{ConfigService.FileDownloadLocation}\\NoBook-DistBook.xlsx";
                    //File.Create(noOrdersFoundFilePath).Dispose();
                    //PutFileToFTPAndMoveToProcessed(noOrdersFoundFilePath, brand, "DistBook", runName);

                    Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                    {
                        Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                        Project = "Loader",
                        Customer = "RRG Spain",
                        Environment = "Prod",
                        Task = "DistrinetBooksJob",
                        StartDate = DateTime.UtcNow,
                        EndDate = DateTime.UtcNow,
                        Status = "Pass",
                        Notes = "No distrinet books" + brand + runName,
                        HTML = string.Empty
                    };

                    await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);


                }

            }
            catch (Exception ex)
            {
                logger.Error($"Error in GetAndUploadBookReport for brand{brand} runName {runName}", ex);
            }

        }

        private static void WaitUntilFileDownloaded(string filePath)
        {
            for (var i = 0; i < 30; i++)
            {
                if (File.Exists(filePath)) { break; }
                System.Threading.Thread.Sleep(1000);
            }
            var length = new FileInfo(filePath).Length;
            for (var i = 0; i < 30; i++)
            {
                System.Threading.Thread.Sleep(1000);
                var newLength = new FileInfo(filePath).Length;
                if (newLength == length && length != 0) { break; }
                length = newLength;
            }
        }


        public void PutFileToFTPAndMoveToProcessed(string oldFilepath, string brand, string reportType,string runName)
        {
            string brandForFilename = GetBrandForFilename(brand);
            string region = runName.Contains("Madrid") ? "Madrid" : "Valencia";
            string dateTimePrefix = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            string newFileName = $"{dateTimePrefix}-{region}-{brandForFilename}-{reportType}.xlsx";
            string outboundFilePath = $"{ConfigService.FileDestination}\\{newFileName}";
            string processedFilePath = $"{ConfigService.FileDestinationProcessed}\\{newFileName}";

            File.Move(oldFilepath, outboundFilePath);
            SFTPPut(outboundFilePath, newFileName);
            File.Move(outboundFilePath, processedFilePath);
        }

        private string GetBrandForFilename(string inputBrand)
        {

            if (inputBrand == "ESP:RENAULT")
            {
                return "Renault";
            }
            if (inputBrand == "ESD:DACIA")
            {
                return "Dacia";
            }
            if (inputBrand == "ESP:ALPINE")
            {
                return "Alpine";
            }

            return "";
        }

        private static FileInfo[] FindFiles(string filenameFragment)
        {

            FileInfo[] recentlySavedFiles = new DirectoryInfo(ConfigService.FileDownloadLocation).EnumerateFiles().Select(x =>
            {
                x.Refresh();
                return x;
            }).Where(x => x.Name.Contains(filenameFragment) && x.Extension == incomingFileExt).ToArray();

            return recentlySavedFiles;
        }

        private IWebElement WaitAndFind(string target, int sleepTime, bool andClick = false)
        {
            IWebElement result = ScraperMethodsService.WaitAndFind(_driver, "DistrinetScraper", target, andClick);

            System.Threading.Thread.Sleep(sleepTime);

            return result;
        }



        private void UploadFilesToFtp(Session session, string filePath, string fileName)
        {
            TransferOperationResult transferResult = session.PutFiles(filePath, session.HomePath + "/DISTRINET/" + fileName, false, transferOptions);
            transferResult.Check();// Throw on any error
        }

        private int SFTPPut(string filePath, string fileName)
        {

            try
            {
                // Setup session options
                SessionOptions sessionOptions = new SessionOptions
                {
                    Protocol = Protocol.Sftp,
                    HostName = ConfigService.FtpHostname,
                    UserName = ConfigService.FtpUsername,
                    Password = ConfigService.FtpPassword,
                };

                sessionOptions.SshHostKeyPolicy = SshHostKeyPolicy.GiveUpSecurityAndAcceptAny;


                using (Session session = new Session())
                {
                    // Connect
                    session.Open(sessionOptions);

                    transferOptions = new TransferOptions();
                    transferOptions.TransferMode = TransferMode.Binary;
                    transferOptions.ResumeSupport.State = TransferResumeSupportState.Off;

                    UploadFilesToFtp(session, filePath, fileName);

                }

                return 0;
            }


            catch
            {
                return 1;
            }
        }


    }
}

