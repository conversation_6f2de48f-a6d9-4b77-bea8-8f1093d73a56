<nav class="navbar">

  <nav class="generic">
    <h4 id="pageTitle">
      <div>
        {{ service.constants.translatedText.Whiteboard_Title }}
        <sourceDataUpdate [chosenDate]="service.constants.LastUpdatedDates.Deals"></sourceDataUpdate>
      </div>
    </h4>

    <ng-container>

      <vehicleTypePickerSpain *ngIf="service.constants.environment.vehicleTypePicker_showVehicleTypePickerSpain"
        [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"
        (updateVehicleTypes)="onUpdateVehicleTypesOnClick($event)">
      </vehicleTypePickerSpain>

      <div class="buttonGroup topDropdownButtons">
        <!-- Site selector -->
        <ng-container *ngIf="service.constants.environment.dealDone_showRRGSitePicker">
          <sitePickerRRG [allSites]="service.constants.sitesActiveSales"
            [sitesFromParent]="service.selections.selectedSites" [buttonClass]="'buttonGroupLeft'"
            (updateSites)="onUpdateSitesOnClick($event)"></sitePickerRRG>
        </ng-container>

        <ng-container *ngIf="service.constants.environment.dealDone_showVindisSitePicker">
          <sitePicker [allSites]="service.constants.sitesActiveSales"
            [sitesFromParent]="service.selections.selectedSites" [buttonClass]="'buttonGroupLeft'"
            (updateSites)="onUpdateSitesOnClick($event)"></sitePicker>
        </ng-container>

        <!-- VehicleType selector -->
        <vehicleTypePicker *ngIf="!service.constants.environment.vehicleTypePicker_showVehicleTypePickerSpain"
          [vehicleTypeTypesFromParent]="service.vehicleTypeTypes" [buttonClass]="'buttonGroupCenter'"
          (updateVehicleTypes)="onUpdateVehicleTypesOnClick($event)"></vehicleTypePicker>

        <!-- OrderType selector -->
        <orderTypePicker *ngIf="service.constants.environment.orderTypePicker"
          [orderTypeTypesFromParent]="service.orderTypeTypes" [buttonClass]="'buttonGroupCenter'"
          (updateOrderTypes)="onUpdateOrderTypesOnClick($event)"></orderTypePicker>

        <!-- Franchise selector -->
        <franchisePicker [franchisesFromParent]="service.franchises" [buttonClass]="'buttonGroupCenter'"
          (updateFranchises)="onUpdateFranchisesOnClick($event)"></franchisePicker>

        <!-- Manager -->
        <div *ngIf="constants.environment.whiteboard_showManagerSelector" ngbDropdown dropright class="d-inline-block">
          <button (click)="generatePeopleSummary()" class="buttonGroupRight btn btn-primary" ngbDropdownToggle>
            <span *ngIf="!service.managerName">Manager </span>
            <span *ngIf="!service.managerName"><i class="fas fa-user-tie"></i></span>
            <span *ngIf="service.managerName">
              <profilePicImage [personId]="service.managerId" [size]="profilePicSizeOnMenu"></profilePicImage>
              {{service.managerName}}
            </span>
          </button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

            <button *ngFor="let salesManagerSummaryItem of service?.salesManagerSummaryItems" ngbDropdownToggle
              (click)="selectManagerExec(salesManagerSummaryItem?.managerName, salesManagerSummaryItem?.managerId)"
              ngbDropdownItem class="personDropdownButton manualToggleCloseItem">
              <div class="spaceBetween">

                <profilePicImage [size]="profilePicSizeOnMenu" [personId]="salesManagerSummaryItem?.managerId">
                </profilePicImage>
                <div class="name">{{salesManagerSummaryItem?.managerName}} </div>
                <div class="dealsCount">{{salesManagerSummaryItem?.dealsCount|cph:'number':0}} </div>
                <!-- <div class="productsPU">{{salesManagerSummaryItem?.productsPU|cph:'number':1}} </div>
              <div class="profitPU">{{salesManagerSummaryItem?.profitPU|cph:'currency':0}} </div> -->

              </div>
            </button>
            <button ngbDropdownItem *ngIf="service.managerName" class="personDropdownButton manualToggleCloseItem"
              (click)="selectManagerExec(null,null)" ngbDropdownToggle>
              <div class="spaceBetween">
                <div><i class="fas fa-user-slash"></i></div>
                <div>{{ constants.translatedText.Clear }}</div>
              </div>
            </button>

          </div>
        </div>

        <!-- Late Costs -->
        <div ngbDropdown dropright class="d-inline-block" [autoClose]="true"
          *ngIf="service.constants.environment.whiteboard_showLateCostPicker">
          <button class="buttonGroupRight btn btn-primary" ngbDropdownToggle>{{service?.lateCostOption?.name}}</button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let lateCostOption of service.constants.lateCostOptions"
              (click)="selectLateCostOptionOnClick(lateCostOption)" ngbDropdownItem>{{lateCostOption.name}}</button>

          </div>
        </div>


        <!-- FOR SELECTING MONTH -->
        <div class="buttonGroup">
          <!-- previousMonth -->
          <button class="btn btn-primary" (click)="changeMonthOnClick(-1)"><i class="fas fa-caret-left"></i></button>

          <!-- dropdownMonth -->
          <div ngbDropdown class="d-inline-block" [autoClose]="true">
            <button (click)="makeMonths()" class="btn btn-primary centreButton"
              ngbDropdownToggle>{{getMonthName(service.deliveryDate.startDate)}}</button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

              <!-- the ngFor buttons -->
              <button *ngFor="let month of service.months" (click)="selectMonth(month)"
                ngbDropdownItem>{{getMonthName(month)}}</button>

            </div>
          </div>
          <!-- nextMonth -->
          <button class="btn btn-primary" (click)="changeMonthOnClick(1)"><i class="fas fa-caret-right"></i></button>
        </div>


      </div>

    </ng-container>


  </nav>

  <nav class="pageSpecific">
    <div *ngIf="service && shouldShowDeliverySummary()">
      <h4><b>{{service.summaryTable.percentDelivered|cph:'percent':0}} {{
        service.constants.translatedText.Whiteboard_Delivered }}
        {{service.summaryTable.toGo|cph:'number':0}} {{ service.constants.translatedText.Whiteboard_ToDo }}</b></h4>
    </div>


  </nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constants.environment.customer">

  <div class="content-new">

    <table id="mainTable" *ngIf="service">
      <thead>
        <tr id="topHeaderRow" id="whiteboard">
          <th></th>
          <th></th>

          <th *ngIf="service.constants.environment.whiteboard_showFinance">
            <span>Fin.</span>
          </th>

          <th *ngIf="service.constants.environment.whiteboard_showAddons">
            <span>Add</span>
          </th>

          <th *ngIf="!service.constants.environment.whiteboard_showFinance">
            <span></span>
          </th>

          <th *ngIf="!service.constants.environment.whiteboard_showAddons">
            <span></span>
          </th>

          <th>
            <span>{{ service.constants.translatedText.Whiteboard_Profit }}</span>
          </th>
          <th><i class="fas fa-car"></i></th>
        </tr>

        <tr id="secondHeaderRow" *ngIf="service?.summaryTable">
          <th></th>
          <th>
            <div class="tileNumberHeaderHolder">
              <div class="tileNumberHeader" *ngFor="let each of service.fakeTiles; let i = index"
                [ngStyle]="{'width.%':dealGroupHolderWidth()}">
                <span> {{(i+1)*service.dealGroupingCount}}</span>
              </div>
            </div>
          </th>

          <th *ngIf="service.constants.environment.whiteboard_showFinance">
            <span>{{service.summaryTable.financePen|cph:'percent':0}}</span>
          </th>

          <th *ngIf="service.constants.environment.whiteboard_showAddons">
            <span>{{service.summaryTable.productsPU|cph:'number':1}}</span>
          </th>

          <th *ngIf="!service.constants.environment.whiteboard_showFinance">
            <span></span>
          </th>

          <th *ngIf="!service.constants.environment.whiteboard_showAddons">
            <span></span>
          </th>

          <th>
            <span>{{service.summaryTable.profit/1000|cph:'currency':0}}k</span>
          </th>
          <th>
            <span>{{service.summaryTable.dealsCount|cph:'number':0}}</span>
          </th>
        </tr>
      </thead>

      <tbody *ngIf="service?.salesExecSummaryItems.length>0">
        <tr *ngFor="let person of service.salesExecSummaryItems">
          <td>
            <div class="nameAndPhoto" (click)="showPersonDealsOnClick(person)">
              <profilePicImage [personId]="person.id" [size]="profilePicSize"></profilePicImage>
              <div class="spaceBetween nameAndSite column">
                <div title="{{person.salesmanName}}" class="personName">{{person.salesmanName}}</div>
                <div class="personRole">{{person.roleName}}</div>
                <div class="personSite" *ngIf="service.showSiteName">{{person.currentSite}}</div>
              </div>
            </div>
          </td>
          <!-- All the tiles  -->
          <td>
            <div class="tileHolder">

              <!-- proper tiles  -->
              <div class="dealGroupHolder" *ngFor="let dealGroup of person.dealGroups; let j=index"
                [ngStyle]="{'width.%':dealGroupHolderWidth(),'max-height.em':service.dealGroupingMaxHeight}">
                <div *ngFor="let deal of dealGroup;let i = index" class="tile dealTile"
                  [ngClass]="getTileClass(deal,i,person)" [ngStyle]="{'height.vh':service.tileHeight}">

                  <ng-template #popContent>

                    <ng-container *ngIf="service.constants.environment.dealDone_showRRGPopoverContent">
                      <popoverContentRRG [dealId]="deal.Id"></popoverContentRRG>
                    </ng-container>

                    <ng-container *ngIf="service.constants.environment.dealDone_showVindisPopoverContent">
                      <popoverContent [dealId]="deal.Id"></popoverContent>
                    </ng-container>

                  </ng-template>

                  <ng-template #popoverTitle>
                    <div class="popoverHeader" (click)="openDealOnClick(deal)">
                      {{deal.Customer + ' - ' + deal.VehicleTypeCode + ': ' + deal.OrderTypeDescription +', '+
                      deal.SitesDescription}}
                    </div>
                  </ng-template>

                  <!-- Normal tiles -->
                  <div class="tileInner" *ngIf="!deal.isFiller && deal.Units > 0" [ngbPopover]="popContent"
                    container="body" [popoverTitle]="popoverTitle" placement="auto">


                    <div *ngIf="deal.isDealBroughtIn" title="Deal is brought in" class="broughtInBlob">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                        <path d="M448 96c0-17.7-14.3-32-32-32s-32 14.3-32 32V416c0 17.7 14.3 32 32 32s32-14.3 32-32V96zM310.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-128-128c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L210.7 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l178.7 0-73.4 73.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l128-128z"/>
                      </svg>
                    </div>

                    <div *ngIf="deal.hasComments" title="Deal has comments" class="commentBlob"
                      [ngClass]="{ 'shiftDown': deal.isDealBroughtIn }">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <path d="M123.6 391.3c12.9-9.4 29.6-11.8 44.6-6.4c26.5 9.6 56.2 15.1 87.8 15.1c124.7 0 208-80.5 208-160s-83.3-160-208-160S48 160.5 48 240c0 32 12.4 62.8 35.7 89.2c8.6 9.7 12.8 22.5 11.8 35.5c-1.4 18.1-5.7 34.7-11.3 49.4c17-7.9 31.1-16.7 39.4-22.7zM21.2 431.9c1.8-2.7 3.5-5.4 5.1-8.1c10-16.6 19.5-38.4 21.4-62.9C17.7 326.8 0 285.1 0 240C0 125.1 114.6 32 256 32s256 93.1 256 208s-114.6 208-256 208c-37.1 0-72.3-6.4-104.1-17.9c-11.9 8.7-31.3 20.6-54.3 30.6c-15.1 6.6-32.3 12.6-50.1 16.1c-.8 .2-1.6 .3-2.4 .5c-4.4 .8-8.7 1.5-13.2 1.9c-.2 0-.5 .1-.7 .1c-5.1 .5-10.2 .8-15.3 .8c-6.5 0-12.3-3.9-14.8-9.9c-2.5-6-1.1-12.8 3.4-17.4c4.1-4.2 7.8-8.7 11.3-13.5c1.7-2.3 3.3-4.6 4.8-6.9c.1-.2 .2-.3 .3-.5z"/>
                      </svg>
                    </div>

                    <div *ngIf="deal.hasFileSentDate && service.constants.environment.whiteboard_rrgUKSettings"
                      class="fileSentBlob"> </div>

                    <div class="customer">{{deal.Customer}}</div>
                    <div *ngIf="service.showRegPlate" class="reg">{{deal.Reg.slice(0,8)}}</div>
                    <div class="totalCount" *ngIf="deal.isLastRealDeal && service.dealGroupingCount>1">{{deal.index+1}}
                    </div>
                  </div>

                  <!-- Filler tiles -->
                  <div class="tileInner" *ngIf="deal.isFiller">
                    <div class="tileTargetNumber">{{person.target}}</div>
                  </div>

                </div>
              </div>

            </div>
          </td>

          <td *ngIf="service.constants.environment.whiteboard_showFinance">{{person.financePen|cph:'percent':0}}</td>
          <td *ngIf="service.constants.environment.whiteboard_showAddons">{{person.productsPU|cph:'number':1}}</td>

          <td *ngIf="!service.constants.environment.whiteboard_showFinance"></td>
          <td *ngIf="!service.constants.environment.whiteboard_showAddons"></td>

          <td>{{person.profit/1000|cph:'currency':0}}k</td>
          <td (click)="service.constants.exportExcel(person.deals, null)">{{person.dealsCount|cph:'number':0}}</td>

        </tr>
      </tbody>

    </table>



    <div *ngIf="!service?.salesExecSummaryItems" class="noDealsToDisplayMsg">No deals to display!</div>

  </div>
</div>