import { EventEmitter, Injectable } from "@angular/core";
import { TopBottomHighlightRule } from "src/app/model/TopBottomHighlightRule";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { CommissionBusinessManagerRow, CommissionItemRRG, CommissionRowRRG } from "./salesCommissionRRG.model";
import { CommissionSitesTableRRGComponent } from "./tables/commissionSitesTableRRG.component";
import { CommissionStatementTableRRGComponent } from "./tables/commissionStatementTableRRG.component";







@Injectable({
  providedIn: 'root'
})


export class SalesCommissionServiceRRG {

  topBottomHighlightsBMs: TopBottomHighlightRule[] = []
  topBottomHighlightsExecs: TopBottomHighlightRule[] = []

  chosenMonth: Date;

  siteRows: CommissionRowRRG[]
  peopleRows: CommissionRowRRG[]
  commissionItems: CommissionItemRRG[]

  chosenSite: CommissionRowRRG;
  chosenPerson: CommissionRowRRG;
  mainTableRef: CommissionSitesTableRRGComponent;
  mainTableRefRegions: CommissionSitesTableRRGComponent;
  statementTableRef: CommissionStatementTableRRGComponent
  showTables: boolean;

  schemeToShow: 'Execs' | 'LBDMs' | 'BMs'
  businessManagerRows: CommissionBusinessManagerRow[];
  lbdmRows: CommissionRowRRG[]

  rowDataUpdated: EventEmitter<boolean> = new EventEmitter<boolean>();

  topBottomHighlights: TopBottomHighlightRule[] = [];

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public getDataMethods: GetDataMethodsService,
  ) {

  }


  initParams() {
    this.schemeToShow = 'Execs';
    this.chosenMonth = this.constants.thisMonthStart;

  }

  getData(monthChange:boolean) {
    if (this.schemeToShow === 'Execs') {

      if (!!this.chosenPerson) {
        //get the chosen person's items
        this.getCommissionItems(this.chosenPerson)
      } else if (!!this.chosenSite) {
        //get the site rows and the people rows
        this.getSiteRows()
        this.getPeopleRows(this.chosenSite)
      } else {
        //get the site rows
        this.getSiteRows()
      }
    }
    else if (this.schemeToShow === 'BMs') {
      this.getBusinessManagerRows();
    }
    else {

      if (!!this.chosenPerson && !monthChange) {
        //get LBDM's items
        this.getCommissionItems(this.chosenPerson)

      } 
      else if (!!this.chosenPerson && monthChange) {
        this.chosenPerson = null;
        this.getLBDMRows();

      } 
      else {

        //get summary by LBDM
        if(!this.lbdmRows || monthChange)
        {
          this.getLBDMRows();
        }
        

      }
    }
  }


  getSiteRows() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })
    this.getDataMethods.getCommissionSiteRows(this.chosenMonth).subscribe((res: CommissionRowRRG[]) => {
      this.siteRows = res
      if (!!this.mainTableRef) { this.mainTableRef.dealWithNewData(); }
      if (!!this.mainTableRefRegions) { this.mainTableRefRegions.dealWithNewData(); }
      this.selections.triggerSpinner.next({ show: false });
      this.showTables = true;
    }, () => {
    })
  }



  getCommissionItems(chosenPerson: CommissionRowRRG) {
    //work out siteIds
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })
    const isLBDM = this.schemeToShow === 'LBDMs';

    this.getDataMethods.getCommissionItemsForPerson(this.chosenMonth, chosenPerson.RowId, isLBDM).subscribe((res: CommissionItemRRG[]) => {
      this.commissionItems = res
      if (!!this.statementTableRef) { this.statementTableRef.dealWithNewData(); }
      this.selections.triggerSpinner.next({ show: false });
      this.showTables = true;
    }, () => {
    })
  }


  onSiteTableClick(row: CommissionRowRRG) {

    this.showTables = false;

    if (!!this.chosenSite) {

      //we are the people table
      this.chosenPerson = row;
      this.getData(false);
    } 
    else if(this.schemeToShow === 'LBDMs')
    {
      this.chosenPerson = row;
      this.getData(false);
    }
    else {
      //we are the sites table
      this.chosenSite = row;
      this.getData(false);
    }
  }

  getBusinessManagerRows() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.getDataMethods.getCommissionBusinessManagerRows(this.chosenMonth).subscribe((res: CommissionBusinessManagerRow[]) => {
      this.businessManagerRows = res;
      this.rowDataUpdated.emit();
      this.selections.triggerSpinner.next({ show: false });
    }, () => { })
  }

  getPeopleRows(chosenSite: CommissionRowRRG) {
    //work out siteIds
    let siteIds: number[] = [];
    if (chosenSite.IsRegion) {
      siteIds = this.constants.sitesActive.filter(x => x.IsSales && x.RegionDescription === chosenSite.Label).map(x => x.SiteId);
    } else if (this.chosenSite.IsTotal) {
      siteIds = this.constants.sitesActive.filter(x => x.IsSales).map(x => x.SiteId);
    } else {
      siteIds = [chosenSite.RowId]
    }

    const isLBDM = this.schemeToShow === 'LBDMs';

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    this.getDataMethods.getCommissionPeopleRows(this.chosenMonth, siteIds, isLBDM, true).subscribe((res: CommissionRowRRG[]) => {
      this.peopleRows = res
      if (!!this.mainTableRef) { this.mainTableRef.dealWithNewData(); }
      this.selections.triggerSpinner.next({ show: false });
      this.showTables = true;
    }, () => {
    })

  }




  getLBDMRows() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });
    this.getDataMethods.getLBDMRows(this.chosenMonth).subscribe((res: CommissionRowRRG[]) => {
      this.lbdmRows = res;
      this.rowDataUpdated.emit();
      if (!!this.mainTableRef) { this.mainTableRef.dealWithNewData(); }
      this.selections.triggerSpinner.next({ show: false });
      this.showTables = true;
    }, () => { })
  }

}