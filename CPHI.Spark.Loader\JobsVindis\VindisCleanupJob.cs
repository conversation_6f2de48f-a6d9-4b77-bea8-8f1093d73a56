﻿using Quartz;
using System;
using CPHI.Repository;
using CPHI.Spark.Model;
using log4net;
using System.Threading.Tasks;
using System.Diagnostics;
using System.IO;
using System.Linq;

namespace CPHI.Spark.Loader
{


   public class VindisCleanUpJob : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(VindisCleanUpJob));
      string errorMessage = string.Empty;
      public async Task Execute(IJobExecutionContext context)
      {
         Stopwatch stopwatch = new Stopwatch();
         stopwatch.Start();

         using (var db = new CPHIDbContext())
         {

            LogMessage logMessage = new LogMessage();
            logMessage.DealerGroup_Id = 3;
            int errorCount = 0;

            try
            {

               logMessage.SourceDate = DateTime.UtcNow;
               logMessage.Job = this.GetType().Name;


               logger.Info("Trying to perform cleanup");
               //-----------------------------------
               string processedFileFolderPath = ConfigService.incomingRoot.Replace("inbound", "processed");

               if (ConfigService.isDev)
               {
                  //recheck that the delete path contains Dev.
                  if (processedFileFolderPath.Contains("Dev"))
                  {
                     //Deleting all files
                     DirectoryInfo directory = new DirectoryInfo(processedFileFolderPath);
                     FileInfo[] filesToDelete = directory.GetFiles();
                     DeleteFiles(filesToDelete);
                  }
                  else
                  {
                     errorMessage = "Config is set to Dev but PATH does not contain Dev";
                     logger.Error(errorMessage);
                  }
               }
               else
               {
                  //Deleting files older than 6 months
                  DateTime maxDateTimeForFile = DateTime.Now.AddMonths(-6);
                  DirectoryInfo directory = new DirectoryInfo(processedFileFolderPath);
                  FileInfo[] filesToDelete = directory.GetFiles().Where(f => f.CreationTime < maxDateTimeForFile).ToArray();
                  DeleteFiles(filesToDelete);


                  //Deleting files between 8am and 7pm EXCEPT *ATG-Spark.csv
                  //DateTime fromDate = DateTime.Now.AddDays(-1);
                  int fromHour = 10; //10AM
                  int toHour = 18; //6PM
                  filesToDelete = directory.GetFiles().Where(
                      f => f.CreationTime.Date < DateTime.Now.AddDays(-7).Date &&  //keep 1 week
                      f.CreationTime.Hour >= fromHour && f.CreationTime.Hour < toHour).ToArray();
                  DeleteFiles(filesToDelete);


                  //-----------------------------------
               }
               logger.Info($"Succesfully ran CleanUpJob");
               stopwatch.Stop();
            }
            catch (Exception e)
            {
               stopwatch.Stop();
               if (e.InnerException != null)
               {
                  errorMessage = e.InnerException.ToString();
               }
               else
               {
                  errorMessage = e.Message;
               }
               logger.Error($"Had problem with CleanUpJob{errorMessage}");
               await EmailerService.SendErrorMail("Had problem with CleanUpJob", errorMessage);
               errorCount++;
            }

            finally
            {
               Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
               {
                  Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                  Project = "Loader",
                  Customer = "Vindis",
                  Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                  Task = this.GetType().Name,
                  StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                  EndDate = DateTime.UtcNow,
                  Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                  Notes = errorMessage,
                  HTML = string.Empty
               };
               await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
            }

            //log that we did a thing
            logMessage.StartCount = 0;
            logMessage.FinishCount = 0;
            logMessage.FailNotes = null;
            logMessage.FinishDate = DateTime.UtcNow;
            logMessage.ProcessedCount = 0;
            logMessage.AddedCount = 0;
            logMessage.RemovedCount = 0;
            logMessage.ChangedCount = 0;
            logMessage.IsCompleted = true;
            logMessage.ErrorCount = errorCount;

            db.LogMessages.Add(logMessage);
            db.SaveChanges();

            db.ChangeTracker.Clear();

         }






      }

      private void DeleteFiles(FileInfo[] filesToDelete)
      {
         foreach (FileInfo file in filesToDelete)
         {
            try
            {
               string destinationPath = file.FullName.Replace("processed", "DELETE");
               string destinationDirectory = Path.GetDirectoryName(destinationPath);

               // Ensure destination folder exists
               Directory.CreateDirectory(destinationDirectory);

               // Move the file
               File.Move(file.FullName, destinationPath);
            }
            catch (Exception ex)
            {
               errorMessage = string.Empty;
               errorMessage += $"Unable to delete file {file.Name}. Error: {ex.Message}";
               logger.Error(errorMessage, ex);
            }
         }
      }





   }


}
