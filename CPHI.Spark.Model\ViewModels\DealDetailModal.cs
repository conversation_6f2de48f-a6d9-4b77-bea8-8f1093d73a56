using System;

namespace CPHI.Spark.Model.ViewModels
{


    public class DealDetailModal 
    {
        public int DealId { get; set; }

        //Vehicle 
        public string Reg { get; set; }
        public string StockNumber { get; set; }
        public string Model { get; set; }
        public int ModelYear { get; set; }
        public DateTime? StockDate { get; set; }
        public string Description { get; set; }
        public int VehicleAge { get; set; }
        public string Variant { get; set; }
        public string VariantTxt { get; set; }
        public string LastPhysicalLocation { get; set; }
        public DateTime? RegisteredDate { get; set; }
        public DateTime? Cdd { get; set; }
        public DateTime? ShipDate { get; set; }
        public DateTime? EnterImportCentre { get; set; }
        public DateTime? ExitImportCentre { get; set; }
        public DateTime? AllocationDate { get; set; }
        public string Chassis { get; set; }
        public string SalesChannel { get; set; }
        public string OrdType { get; set; }



        //Order 
        public bool IsRemoved { get; set; }
        public DateTime? RemovedDate { get; set; }
        public string EnquiryNumber { get; set; }
        public string Customer { get; set; }
        public bool IsLateCost { get; set; }
        public bool IsConfirmed { get; set; }
        public bool IsDelivered { get; set; }
        public DateTime OrderDate { get; set; }
        public DateTime ActualDeliveryDate { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public DateTime AccountingDate { get; set; }
        public int DaysToDeliver { get; set; }
        public string OemReference { get; set; }
        public bool IsFinanced { get; set; }
        public string FinanceCo { get; set; }
        public string FinanceType { get; set; }
        public string Salesman { get; set; }
        public string SiteName { get; set; }
        public string VehicleClass { get; set; }
        public string Franchise { get; set; }
        public string OrderType { get; set; }
        public int Site_Id { get; set; }
        public string SiteCode { get; set; }
        public string VehicleType { get; set; }
        public string VehicleSuperType { get; set; }
        public string OrderTypeCode { get; set; }
        public bool IsClosed { get; set; }
        public bool? AuditPass { get; set; }
        public int Units { get; set; }
        public DateTime? HandoverDate { get; set; }
        public string VehicleSource { get; set; }


        public decimal WebsitePrice { get; set; }
        public decimal WebsiteDiscount { get; set; }

        //Metal profit
        public decimal Sale { get; set; }
        public decimal Discount { get; set; }
        public decimal CoS { get; set; }
        public decimal PartExOverAllowance1 { get; set; }
        public decimal VatCost { get; set; }
        public decimal NewBonus1 { get; set; } //was FactoryBonus
        public decimal NewBonus2 { get; set; } // was RegBonus
        public decimal MetalProfitTotal { get => Sale + Discount + CoS + PartExOverAllowance1 + VatCost + NewBonus1 + NewBonus2; }


        //Other profit
        public decimal AccessoriesSale { get; set; }
        public decimal AccessoriesCost { get; set; }
        public decimal FuelSale { get; set; }
        public decimal FuelCost { get; set; }
        public decimal BrokerCost { get; set; }
        public decimal IntroCommission { get; set; }
        public decimal OemDeliverySale { get; set; }
        public decimal OemDeliveryCost { get; set; }
        public decimal PDICost { get; set; }
        public decimal MechPrep { get; set; }
        public decimal BodyPrep { get; set; }
        public decimal Other { get; set; }
        public decimal Error { get; set; }
        public decimal StandardWarrantyCost { get; set; }
        public bool HasPaintProtectionAccessory { get; set; }
        public decimal PaintProtectionAccessorySale { get; set; }
        public decimal PaintProtectionAccessoryCost { get; set; }
        public decimal OtherProfitTotal { get => AccessoriesSale + AccessoriesCost + FuelSale + FuelCost + BrokerCost + IntroCommission + 
                OemDeliverySale + OemDeliveryCost + PDICost + MechPrep + BodyPrep + Other + Error + StandardWarrantyCost
                + PaintProtectionAccessorySale + PaintProtectionAccessoryCost; }


        //Finance Profit
        public decimal FinanceCommission { get; set; }
        public decimal FinanceSubsidy { get; set; }
        public decimal SelectCommission { get; set; }
        public decimal RCIFinanceCommission { get; set; }
        public decimal StandardsCommission { get; set; }
        public decimal ProPlusCommission { get; set; }
        public decimal FinanceProfitTotal { get => FinanceCommission + FinanceSubsidy + SelectCommission + RCIFinanceCommission + StandardsCommission + ProPlusCommission; }


        //Addon Profit
        public bool HasCosmeticInsurance { get; set; }
        public decimal CosmeticInsuranceSale { get; set; }
        public decimal CosmeticInsuranceCost { get; set; }
        public decimal CosmeticInsuranceCommission { get; set; }
        public bool HasGapInsurance { get; set; }
        public decimal GapInsuranceSale { get; set; }
        public decimal GapInsuranceCost { get; set; }
        public decimal GapInsuranceCommission { get; set; }
        public bool HasPaintProtection { get; set; }
        public decimal PaintProtectionSale { get; set; }
        public decimal PaintProtectionCost { get; set; }
        public bool HasServicePlan { get; set; }
        public decimal ServicePlanSale { get; set; }
        public decimal ServicePlanCost { get; set; }
        public bool HasWarranty { get; set; }
        public decimal WarrantySale { get; set; }
        public decimal WarrantyCost { get; set; }
        public bool HasWheelGuard { get; set; }
        public decimal WheelGuardSale { get; set; }
        public decimal WheelGuardCost { get; set; }
        public bool HasTyreInsurance { get; set; }
        public decimal TyreInsuranceSale { get; set; }
        public decimal TyreInsuranceCost { get; set; }
        public decimal TyreInsuranceCommission { get; set; }
        public bool HasAlloyInsurance { get; set; }
        public decimal AlloyInsuranceSale { get; set; }
        public decimal AlloyInsuranceCost { get; set; }
        public decimal AlloyInsuranceCommission { get; set; }
        public bool HasTyreAndAlloyInsurance { get; set; }
        public decimal TyreAndAlloyInsuranceSale { get; set; }
        public decimal TyreAndAlloyInsuranceCost { get; set; }
        public decimal TyreAndAlloyInsuranceCommission { get; set; }
        public decimal AddOnProfitTotal { get => CosmeticInsuranceSale + CosmeticInsuranceCost + CosmeticInsuranceCommission + GapInsuranceSale 
                + GapInsuranceCost + GapInsuranceCommission + PaintProtectionSale + PaintProtectionCost + ServicePlanSale + ServicePlanCost 
                + WarrantySale + WarrantyCost + WheelGuardSale + WheelGuardCost + TyreInsuranceSale + TyreInsuranceCost + TyreInsuranceCommission  
                + AlloyInsuranceSale + AlloyInsuranceCost + AlloyInsuranceCommission + TyreAndAlloyInsuranceSale + TyreAndAlloyInsuranceCost 
                + TyreAndAlloyInsuranceCommission; }

        
        public decimal TotalProfit { get; set; }

        public int? StockItemId { get; set; }

        public string InvoiceNo { get; set; } // Spain only

        public DateTime? DateFactoryTransportation { get; set; } // Spain only

        public DateTime? DateSiteArrival { get; set; } // Spain only

        public DateTime? DateSiteTransportation { get; set; } // Spain only

        public DateTime? DateVehicleRecondition { get; set; } // Spain only

        public bool QualifyingPartEx { get; set; }
    }
}
