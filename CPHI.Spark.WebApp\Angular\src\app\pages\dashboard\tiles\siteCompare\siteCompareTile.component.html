<div  class="tileHeader withLink" >
  <div class="headerWords">
    <h4>{{constants.translatedText.Dashboard_SitePerformanceLeague_Title}}
    </h4>
  </div>
</div>



  <div class="innerContentHolder">

    <!-- Time period choice buttons -->
    <div class="buttonGroup" id="chooseTimePeriod">
      <button class="btn btn-primary" *ngFor="let tp of timePeriods" (click)="chooseTimePeriod(tp)" [ngClass]="{'active':chosenTimePeriod == tp}">{{tp}}</button>
    </div>

    <!-- Measure choice buttons -->
    <div  id="chooseMeasure">
      <button class="btn measureButton btn-primary" *ngFor="let measure of measures" (click)="chooseMeasure(measure)" 
      [ngClass]="{'active':measure.description === chosenMeasure.description }">{{measure.description}}</button>

    </div>

    <table class="cph fullWidth">
      <thead>
        <tr>
          <th>{{constants.translatedText.Rank}}</th>
          <th>{{constants.translatedText.Site}}</th>
          <th></th>
        </tr>
      </thead>

      <tbody>
      <ng-container   *ngFor="let row of data; let i = index;trackBy:trackByFunction ">
        <tr *ngIf="data && !chosenMeasure.isMargin"  [ngClass]="rowBrightClass(row)">
          <td>{{i+1}}</td>
          <td>{{row.SiteDescription}}</td>
           <td>
           <span>{{row.Value|cph:'number':0}}</span>
           </td>
        </tr>
        <tr *ngIf="data && chosenMeasure.isMargin"  [ngClass]="rowBrightClass(row)">
          <td>{{i+1}}</td>
          <td>{{row.SiteDescription}}</td>
           <td>
           <span>{{row.Value|cph:'currency':0}}</span>
           </td>
        </tr>
        </ng-container>
      </tbody>
    </table>


  </div>