﻿// This Startup file is based on ASP.NET Core new project templates and is included
// as a starting point for DI registration and HTTP request processing pipeline configuration.
// This file will need updated according to the specific scenario of the application being upgraded.
// For more information on ASP.NET Core startup files, see https://docs.microsoft.com/aspnet/core/fundamentals/startup

using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using CPHI.Repository;
using Microsoft.EntityFrameworkCore;
using CPHI.Spark.WebApp.Service;
using CPHI.Spark.WebApp.DataAccess;
using Newtonsoft.Json.Serialization;
using CPHI.Spark.WebApp.Service.Vindis;
using CPHI.Spark.WebApp.Caches;
using Microsoft.Extensions.Caching.Memory;
using CPHI.Spark.DataAccess;
using Dapperr = CPHI.Spark.WebApp.DataAccess.Dapperr;
using CPHI.Spark.DataAccess.AutoPrice;
using CPHI.Spark.WebApp.Service.AutoPrice;
using CPHI.Spark.WebApp.Service.Autoprice;
using CPHI.Spark.Model;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Newtonsoft.Json.Converters;
using CPHI.Spark.DataAccess.DataAccess.AutoPrice;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.BusinessLogic.Services;

namespace CPHI.Spark.WebApp
{
    public class ExceptionHandlingMiddleware
    {
        private readonly RequestDelegate _next;

        public ExceptionHandlingMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
#if DEBUG
                throw new Exception(ex.Message, ex);
#endif
                await HandleExceptionAsync(context, ex);

            }
        }

        private static Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            if (exception.Message == "Invalid token")
            {
                context.Response.StatusCode = 401; // Unauthorized
                //context.Response.ContentType = "application/json";
                return context.Response.WriteAsync(new { error = exception.Message }.ToString());
            }

            // Handle other exceptions or pass through the original exception
            context.Response.StatusCode = 500; // Internal Server Error
            return context.Response.WriteAsync("An error occurred.");
        }
    }

    public static class DealerGroupStockpulseId
    {
        public static int Get(DealerGroupName dealerGroup)
        {
            switch (dealerGroup)
            {
                case DealerGroupName.RRGUK: return 2;
                case DealerGroupName.Vindis: return 3;
                default: return 0;
            }
        }
    }

    public static class DealerGroupBlobname
    {
        public static string Get(DealerGroupName dealerGroup)
        {
            switch (dealerGroup)
            {



                //case DealerGroupName.None:
                //    return string.Empty;
                case DealerGroupName.RRGUK:
                    return "renault";
                case DealerGroupName.RRGSpain:
                    return "spain";
                case DealerGroupName.Vindis:
                    return "vindis";
                default:
                    return "autoPrice";
            }
        }

    }

    public static class DealerGroupDisplayName
    {
        public static string Get(DealerGroupName dealerGroup)
        {
            switch (dealerGroup)
            {
                case DealerGroupName.RRGUK:
                    return "RRG UK";
                case DealerGroupName.RRGSpain:
                    return "RRG Spain";
                default: 
                   return dealerGroup.ToString();
                
            }
        }

    }

    public static class DealerGroupConnectionName
    {
        public static string GetConnectionName(DealerGroupName dealerGroup)
        {
            switch (dealerGroup)
            {
                
                case DealerGroupName.RRGUK:
                    return "RRGUKConnection";
                case DealerGroupName.RRGSpain:
                    return "RRGSpainConnection";
                case DealerGroupName.Vindis:
                    return "VindisConnection";
                //case DealerGroupName.Jardine:
                //case DealerGroupName.LMC:
                //case DealerGroupName.PinkStones:
                //case DealerGroupName.V12:
                //case DealerGroupName.MJMotorCo:
                //case DealerGroupName.BrindleyGroup:
                //case DealerGroupName.WaylandsGroup:
                //case DealerGroupName.KCSOfSurrey:
                //case DealerGroupName.SturgessGroup:
                //case DealerGroupName.JJPremiumCars:
                //case DealerGroupName.PentagonGroup:
                //case DealerGroupName.HippoApproved:
                //case DealerGroupName.LMCOfFarnham:
                //case DealerGroupName.Startin:
                //    return "DefaultConnection";
                default:
                    return "DefaultConnection";
            }
        }

    }


    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        //public IConfiguration Configuration { get; }
        public static IConfiguration Configuration { get; set; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddHttpContextAccessor();

            services.AddIdentity<CPHI.Spark.Model.ViewModels.ApplicationUser, IdentityRole>()
                .AddEntityFrameworkStores<CPHIDbContext>()
                .AddDefaultTokenProviders();

            // Verify - Update as per old app
            services.Configure<IdentityOptions>(options =>
            {
                options.User.RequireUniqueEmail = false;

                options.Password.RequiredLength = 6;
                options.Password.RequireNonAlphanumeric = false;
                options.Password.RequireDigit = false;
                options.Password.RequireLowercase = true;
                options.Password.RequireUppercase = true;


                options.Lockout.AllowedForNewUsers = true;
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
                options.Lockout.MaxFailedAccessAttempts = 5;
            }
            );



            services.AddCors(o => o.AddPolicy("CorsPolicy", builder =>
            {
                builder.AllowAnyOrigin()
                       .AllowAnyMethod()
                       .AllowAnyHeader();
            }));


            services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            }).AddJwtBearer(x =>
            {
                x.RequireHttpsMetadata = false; ;
                x.SaveToken = false;
                x.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(AuthConfiguration.GetTokenKey()),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ClockSkew = TimeSpan.Zero
                };
            });


          
            services.AddControllers().AddNewtonsoftJson(
                options =>
                {
                    options.SerializerSettings.PreserveReferencesHandling = Newtonsoft.Json.PreserveReferencesHandling.None;
                    options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                    options.SerializerSettings.DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Utc;
                    options.SerializerSettings.DateParseHandling = Newtonsoft.Json.DateParseHandling.DateTimeOffset;
                    options.SerializerSettings.ContractResolver = new DefaultContractResolver();
                    options.SerializerSettings.Converters.Add(new StringEnumConverter());

                });

            AddServiceClasses(services);
            AddCacheClasses(services);
            AddDataAccessClasses(services);

            //services.AddHttpClient<AutoTraderApiClient>();

            //database
             services.AddEntityFrameworkSqlServer().AddDbContext<CPHIDbContext>(options =>
             options.UseSqlServer(Configuration.GetConnectionString("MigrationConnection")).LogTo(msg =>
             {
                 System.Diagnostics.Debug.WriteLine(msg); //spits the ef query out in Output logs
             }).EnableDetailedErrors());


            //database - swap in this bit if need to extend migration timeout when applying a migration
            // services.AddEntityFrameworkSqlServer().AddDbContext<CPHIDbContext>(options =>
            //     options.UseSqlServer(
            //         Configuration.GetConnectionString("DefaultConnection"),
            //         sqlServerOptionsAction: sqlOptions =>
            //         {
            //             sqlOptions.CommandTimeout(3000); // Sets the command timeout to 300 seconds
            //         }
            //     ).LogTo(msg =>
            //     {
            //         System.Diagnostics.Debug.WriteLine(msg); // Spits the EF query out in Output logs
            //     }).EnableDetailedErrors()
            // );




        }

        public static void SetDBContextConnectionString(DealerGroupName dealerGroup, CPHIDbContext db)
        {
            //string connectionStringName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            string connString = Configuration.GetConnectionString(DealerGroupConnectionName.GetConnectionName(dealerGroup));
            //string connString = Configuration.GetSection("ConnectionStrings")[connectionStringName];
            db.Database.GetDbConnection().ConnectionString = connString;
        }


        private static void AddServiceClasses(IServiceCollection services)
        {
            services.AddScoped<IDapperr, Dapperr>();
            services.AddScoped<IDADapperr, DADapperr>();
            services.AddScoped<IEmailSenderService, EmailSenderService>();
            services.AddScoped<IRefreshTokenService, RefreshTokenService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IScratchcardService, ScratchcardService>();
            services.AddScoped<IDealService, DealService>();
            services.AddScoped<IGlobalParamService, GlobalParamService>();
            services.AddScoped<ISitesService, SitesService>();
            services.AddScoped<IOrderTypesService, OrderTypesService>();
            services.AddScoped<IPublicHolidaysService, PublicHolidaysService>();
            services.AddScoped<ISarsLinesService, SarsLinesService>();
            services.AddScoped<IApiTestService, ApiTestService>();
            services.AddScoped<ITranslationService, TranslationService>();
            services.AddScoped<IStockLandingsService, StockLandingsService>();
            services.AddScoped<IDashboardService, DashboardService>();
            services.AddScoped<ISalesmanEfficiencyService, SalesmanEfficiencyService>();
            services.AddScoped<ICitNowService, CitNowService>();
            services.AddScoped<IExecManagerMappingService, ExecManagerMappingService>();
            services.AddScoped<IWorkingDaysService, WorkingDaysService>();
            services.AddScoped<IServiceService, ServiceService>();
            services.AddScoped<IPartsService, PartsService>();
            services.AddScoped<IStockService, StockService>();
            services.AddScoped<IOrderbookService, OrderbookService>();
            services.AddScoped<IGenerateExcelService, GenerateExcelService>();
            services.AddScoped<IExcelHelpersService, ExcelHelpersService>();
            services.AddScoped<ITeleStatsExcelSheetService, TeleStatsExcelSheetService>();
            services.AddScoped<IGenerateExcelService, GenerateExcelService>();
            services.AddScoped<IDetailedOrderbookExcelSheetService, DetailedOrderbookExcelSheetService>();
            services.AddScoped<IRegistrationsService, RegistrationsService>();
            services.AddScoped<IDashboardCache, DashboardCache>();
            services.AddScoped<IDebtorsService, DebtorsService>();
            services.AddScoped<IOrdersSpainService, OrdersSpainService>();
            services.AddScoped<IImageRatiosService, ImageRatiosService>();
            services.AddScoped<IClientAppStartService, ClientAppStartService>();
            services.AddScoped<IBookingsService, BookingsService>();
            services.AddScoped<ILeaverReportingService, LeaverReportingService>();
            services.AddScoped<IOrdersSpainService, OrdersSpainService>();
            services.AddScoped<IOrdersSpainService, OrdersSpainService>();
            services.AddScoped<IPartsStockService, PartsStockService>();
            services.AddScoped<Service.RRG.ICommissionsService, Service.RRG.CommissionsService>();
            services.AddScoped<Service.Vindis.ICommissionsService, Service.Vindis.CommissionsService>();
            services.AddScoped<IWipsService, WipsService>();
            services.AddScoped<IVocService, VocService>();
            services.AddScoped<IPartsStockService, PartsStockService>();
            services.AddScoped<ISourceDataUpdateService, SourceDataUpdateService>();
            services.AddScoped<IAccessTokenService, AccessTokenService>();
            services.AddScoped<IImageService, ImageService>();
            services.AddScoped<ICommentsService, CommentsService>();
            //services.AddScoped<IStockListReportService, StockListReportService>();
            services.AddScoped<IAlcopaService, AlcopaService>();
            services.AddScoped<ICommissionAdjustmentService, CommissionAdjustmentService>();
            services.AddScoped<ILiveForecastService, LiveForecastService>();
            services.AddScoped<ISalesExecReviewService, SalesExecReviewService>();
            services.AddScoped<IDistrinetService, DistrinetService>();
            services.AddScoped<IDealLatestService, DealLatestService>();
            services.AddScoped<IUpsellService, UpsellService>();
            services.AddScoped<IExecManagerMappingService, ExecManagerMappingService>();
           // services.AddScoped<IPricingRuleSetService, PricingRuleSetService>();
            services.AddScoped<IAftersalesDatasetsService, AftersalesDatasetsService>();
            services.AddScoped<IFleetOrderbookService, FleetOrderbookService>();
            services.AddScoped<IEvhcService, EvhcService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IImportMaskService, ImportMaskService>();
            services.AddScoped<IBroadcastMessageService, BroadcastMessageService>();
            services.AddScoped<ITeleStatsService, TeleStatsService>();
            
            //AutoPrice
            services.AddScoped<IAdvertModalService, AdvertModalService>();
            services.AddScoped<IAutoPriceDashboardService, AutoPriceDashboardService>();
            services.AddScoped<ILeavingService, LeavingService>();
            services.AddScoped<IOptOutsService, OptOutsService>();  
            services.AddScoped<IPriceChangesService, PriceChangesService>();
            services.AddScoped<IPricesService,PricesService>();
           // services.AddScoped<IPricingRuleSetService,PricingRuleSetService>();
            services.AddScoped<IStrategyService, StrategyService>();
            services.AddScoped<ITableStatesService, TableStatesService>();
            services.AddScoped<IVehicleAdvertService, VehicleAdvertService>();
            services.AddScoped<IVehicleValuationService, VehicleValuationService>();
            services.AddScoped<ILocalBargainsService, LocalBargainsService>();
            services.AddScoped<ISiteSettingsService, SiteSettingsService>();
            services.AddScoped<IAutoPriceNewDealerGroupService, AutoPriceNewDealerGroupService>();


            //services that live within BusinessLogic

            services.AddScoped<IExampleItemsService>(provider =>
            {
                var userService = provider.GetRequiredService<IUserService>();

                // Get the dealer group and the connection string
                var dealerGroup = userService.GetUserDealerGroupName();
                string connectionString = userService.GetConnectionString();

                // Instantiate ExampleItemsService with the connection string
                return new ExampleItemsService(connectionString);
            });


        }

        private static void AddCacheClasses(IServiceCollection services)
        {
            services.AddScoped<IStockListCache, StockListCache>();
            services.AddScoped<IDashboardCache, DashboardCache>();
            services.AddScoped<RRG.ICommissionItemsCache, RRG.CommissionItemsCache>();
            services.AddScoped<Vindis.ICommissionItemsCache, Vindis.CommissionItemsCache>();
            services.AddScoped<ITodayNewUsedOrdersCache, TodayNewUsedOrdersCache>();
            services.AddScoped<IServiceSummaryCache, ServiceSummaryCache>();
            services.AddScoped<IDistrinetCache, DistrinetCache>();
            services.AddScoped<IFleetOrderbookCache, FleetOrderbookCache>();
            services.AddScoped<IUserCache, UserCache>();
            services.AddScoped<IAutoPriceCache, AutoPriceCache>();  
        }

        private static void AddDataAccessClasses(IServiceCollection services)
        {
            //services.AddScoped<IPartsStockDataAccess, PartsStockDataAccess>();
            services.AddScoped<IDebtorsDataAccess, DebtorsDataAccess>();
            services.AddScoped<IVocDataAccess, VocDataAccess>();
            services.AddScoped<ILeaverReportingDataAccess, LeaverReportingDataAccess>();
            services.AddScoped<IPartsStockDataAccess, PartsStockDataAccess>();
            services.AddScoped<ILeaverReportingDataAccess, LeaverReportingDataAccess>();
            services.AddScoped<IVocDataAccess, VocDataAccess>();
            services.AddScoped<IDashboardDataAccess, DashboardDataAccess>();
            services.AddScoped<IScratchcardDataAccess, ScratchcardDataAccess>();
            //services.AddScoped<ISiteDataAccess, SiteDataAccess>();
            //services.AddScoped<IDealDataAccess, DealDataAccess>();
            //services.AddScoped<IUserDataAccess, UserDataAccess>();
            //services.AddScoped<IGlobalParamDataAccess, GlobalParamDataAccess>();
            services.AddScoped<IOrderTypesDataAccess, OrderTypesDataAccess>();
            services.AddScoped<IPublicHolidaysDataAccess, PublicHolidaysDataAccess>();
            services.AddScoped<ISarsLinesDataAccess, SarsLinesDataAccess>();
            services.AddScoped<IStockLandingsDataAccess, StockLandingsDataAccess>();
            services.AddScoped<IApiTestDataAccess, ApiTestDataAccess>();
            services.AddScoped<ITranslationDataAccess, TranslationDataAccess>();
            services.AddScoped<IPeopleDataAccess, PeopleDataAccess>();
            services.AddScoped<IOrdersSpainDataAccess, OrdersSpainDataAccess>();
            services.AddScoped<IWipsDataAccess, WipsDataAccess>();
            services.AddScoped<ICitNowDataAccess, CitNowDataAccess>();
            services.AddScoped<IBroadcastMessageDataAccess, BroadcastMessageDataAccess>();
            services.AddScoped<IEvhcDataAccess, EvhcDataAccess>();
            //services.AddScoped<IStockDataAccess, StockDataAccess>();
            services.AddScoped<IOrderbookDataAccess, OrderbookDataAccess>();
            services.AddScoped<IOrdersSpainDataAccess, OrdersSpainDataAccess>();
            services.AddScoped<IRegistrationsDataAccess, RegistrationsDataAccess>();
            services.AddScoped<IWorkingDaysDataAccess, WorkingDaysDataAccess>();
            services.AddScoped<IServiceDataAccess, ServiceDataAccess>();
            services.AddScoped<IPartsDataAccess, PartsDataAccess>();
            services.AddScoped<IDebtorsDataAccess, DebtorsDataAccess>();
            services.AddScoped<ICommentsDataAccess, CommentsDataAccess>();
            services.AddScoped<IClientAppStartDataAccess, ClientAppStartDataAccess>();
            services.AddScoped<IFinancialLinesDataAccess, FinancialLinesDataAccess>();
            //services.AddScoped<IStockListReportDataAccess, StockListReportDataAccess>();
            services.AddScoped<IAlcopasDataAccess, AlcopasDataAccess>();
            services.AddScoped<ICommissionAdjustmentDataAccess, CommissionAdjustmentDataAccess>();
            services.AddScoped<ILiveForecastDataAccess, LiveForecastDataAccess>();
            services.AddScoped<ISalesExecReviewDataAccess, SalesExecReviewDataAccess>();
            services.AddScoped<IDistrinetDataAccess, DistrinetDataAccess>();
            services.AddScoped<IDealLatestDataAccess, DealLatestDataAccess>();
            services.AddScoped<IStandingValuesDataAccess, StandingValuesDataAccess>();
            services.AddScoped<ICommissionsDataAccess, CommissionsDataAccess>();
            services.AddScoped<IUpsellDataAccess, UpsellDataAccess>();
            services.AddScoped<IAftersalesDatasetsDataAccess, AftersalesDatasetsDataAccess>();
            services.AddScoped<IFleetOrderbookDataAccess, FleetOrderbookDataAccess>();
            services.AddScoped<IReportDataAccess, ReportDataAccess>();
            services.AddScoped<IExecManagerMappingDataAccess, ExecManagerMappingDataAccess>();
            services.AddScoped<ITeleStatsDataAccess, TeleStatsDataAccess>();
            services.AddScoped<IImageRatiosDataAccess, ImageRatiosDataAccess>();
            services.AddScoped<IDVSAApiClient, DVSAApiClient>();

            //database
            //services.AddEntityFrameworkSqlServer().AddDbContext<CPHIDbContext>(options =>
            // options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection")));

            services.AddSingleton<CPHIDbContextFactory>();

        }



        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IServiceProvider serviceProvider, IMemoryCache memoryCache, CPHIDbContext db)
        {
            CPHIDbContext.envi = env.EnvironmentName;

            if (env.IsDevelopment() || env.EnvironmentName == "Dev")
            {
                app.UseDeveloperExceptionPage();
            }
#if DEBUG
            app.UseDeveloperExceptionPage();
#endif


            app.UseHttpsRedirection();
            //app.UseResponseCompression();
            app.UseRouting();

            app.UseCors("CorsPolicy");
            //-- Auth Start
            app.UseAuthentication();

            app.UseMiddleware<CompressionMiddleware>();
            app.UseMiddleware<ExceptionHandlingMiddleware>();

            //-- Auth End
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });


            //populate my statics, if we add a new connection, make sure to add it in AccountsController as well.
            Dapperr dapp = new Dapperr(Configuration, db);
            string connStringRUK = Configuration.GetConnectionString("RRGUKConnection");
            string connStringVindis = Configuration.GetConnectionString("VindisConnection");
            string connStringSpain = Configuration.GetConnectionString("RRGSpainConnection");
            string connStringAutoprice = Configuration.GetConnectionString("DefaultConnection");
            
            var globalParamDA_RUK = new GlobalParamDataAccess(connStringRUK);
            var globalParamDA_Spain = new GlobalParamDataAccess(connStringSpain);
            var globalParamDA_Vindis = new GlobalParamDataAccess(connStringVindis);
            var globalParamDA_Autoprice = new GlobalParamDataAccess(connStringAutoprice);

            //globalParamDA_Autoprice.TempClearDbDown();

            foreach (var item in Enum.GetValues(typeof(DealerGroupName)) )
            {
                if((DealerGroupName)item==DealerGroupName.RRGUK)
                {
                    ConstantsCache.LastUpdatedDatesDictionary.Add((DealerGroupName)item, globalParamDA_RUK.GetLastUpdateDates((DealerGroupName)item).Result);
                }
                else if ((DealerGroupName)item == DealerGroupName.Vindis)
                {
                    ConstantsCache.LastUpdatedDatesDictionary.Add((DealerGroupName)item, globalParamDA_Vindis.GetLastUpdateDates((DealerGroupName)item).Result);
                }
                else if ((DealerGroupName)item == DealerGroupName.RRGSpain)
                {
                    ConstantsCache.LastUpdatedDatesDictionary.Add((DealerGroupName)item, globalParamDA_Spain.GetLastUpdateDates((DealerGroupName)item).Result);
                }
                else
                {
                    ConstantsCache.LastUpdatedDatesDictionary.Add((DealerGroupName)item, globalParamDA_Autoprice.GetLastUpdateDates((DealerGroupName)item).Result);
                }
            }





            var userCache = new UserCache(Configuration, memoryCache);
            _ = userCache.ReLoadUserSiteRoleCache().Result;

            StandingValuesDataAccess standingValuesDataAccess = new StandingValuesDataAccess(dapp);
            ConstantsCache.rrgUKFranchiseString = standingValuesDataAccess.GetFranchiseCodes(DealerGroupName.RRGUK).Result;
            ConstantsCache.rrgSpainFranchiseString = standingValuesDataAccess.GetFranchiseCodes(DealerGroupName.RRGSpain).Result;
            ConstantsCache.vindisFranchiseString = standingValuesDataAccess.GetFranchiseCodes(DealerGroupName.Vindis).Result;


            //Retailer Site Strategy Banding Definitions
            var rrgUKRSDA = new RetailerSitesDataAccess(connStringRUK);
            var vindisRSDA = new RetailerSitesDataAccess(connStringVindis);
            var autopriceRSDA = new RetailerSitesDataAccess(connStringAutoprice);
            
            ConstantsCache.bandingDefinitionsRRG = rrgUKRSDA.GetRetailerStrategyBandings().Result;
            ConstantsCache.bandingDefinitionsVindis = vindisRSDA.GetRetailerStrategyBandings().Result;
            ConstantsCache.bandingDefinitionsAutoprice = autopriceRSDA.GetRetailerStrategyBandings().Result;

        }



    }

    


}
