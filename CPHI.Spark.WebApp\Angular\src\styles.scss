@import "bootstrap/scss/bootstrap";

@import 'styles/global/cellRenderers';
@import 'styles/global/colours';
@import 'styles/global/fontSizes';
@import 'styles/global/helpers';
@import 'styles/global/variables';
@import 'styles/global/mediaQueries';

@import 'styles/components/accordion';
@import 'styles/components/agGrid';
@import 'styles/components/button';
@import 'styles/components/card';
@import 'styles/components/checkbox';
@import 'styles/components/chip';
@import 'styles/components/grid';
@import 'styles/components/gridHeader';
@import 'styles/components/dropdown';
@import 'styles/components/input';
@import 'styles/components/form';
@import 'styles/components/loginBox';
@import 'styles/components/lozenge';
@import 'styles/components/modal';
@import 'styles/components/navbar';
@import 'styles/components/pagination';
@import 'styles/components/performanceRating';
@import 'styles/components/popover';
@import 'styles/components/regChassis';
@import 'styles/components/retailRating';
@import 'styles/components/searchBox';
@import 'styles/components/select';
@import 'styles/components/table';
@import 'styles/components/tile';
@import 'styles/components/typeahead';

@import 'ag-grid-community/styles/ag-grid.css';
@import 'ag-grid-community/styles/ag-theme-balham.css';


//load other files is done through angular.json

@import './styles/global/_autoTrader.scss';


//Fonts
@font-face {
  font-family: 'NumberPlate';
  font-style: normal;
  font-weight: normal;
  src: url('assets/fonts/RobotoMono-SemiBold.ttf');
}

@font-face {
  font-family: 'ScratchCard';
  font-style: normal;
  font-weight: normal;
  src: url('assets/fonts/LuckiestGuy-Regular.ttf');
}

@font-face {
  font-family: 'ScratchCard2';
  font-style: normal;
  font-weight: normal;
  src: url('assets/fonts/BLOBBYCHUG-Bold.ttf');
}

@font-face {
  font-family: 'StarWars';
  font-style: normal;
  font-weight: normal;
  src: url('assets/fonts/PathwayGothicOne-Regular.ttf');
}

.pulsing {
  animation-name: pulse_animation;
  animation-duration: 1000ms;
  transform-origin: 70% 70%;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

.textRight{
  text-align:right;
}

@keyframes pulse_animation {
  0% {
    background: var(--brightColour);
    transform: scale(1);
  }

  30% {
    transform: scale(1);
  }

  40% {
    transform: scale(1.08);
  }

  50% {
    background: var(--brightColourLightest);
    transform: scale(1);
  }

  60% {
    transform: scale(1);
  }

  70% {
    transform: scale(1.05);
  }

  80% {
    transform: scale(1);
  }

  100% {
    background: var(--brightColour);
    transform: scale(1);
  }
}

body,
html {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  margin: 0 auto;
  font-family: "-apple-system", "BlinkMacSystemFont", "Segoe UI", 'Roboto', 'Helvetica Neue', 'Helvetica', 'Arial';
}

//for excel button
#excelExport {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 30px;
  z-index: 1;
  animation-duration: 0.3s;
  cursor: pointer
}

#excelExport img {
  width: 2em;
}

productivity-cell {
  width: 100%;
  text-align: center;
}

spinnerBM {
  z-index: 1000
}

#overallHolder {
  position: relative;
  display: flex;
  margin-top: 35px;
  margin-left: 10px;
  min-height: calc(100vh - 35px);
  transition: all 0.5s;
  background: var(--backgroundDealershipImage);
  background-size: cover;

  @media (max-width: 1680px) {
    margin-top: 30px;
    min-height: calc(100vh - 30px);
  }
  @media (max-width: 1280px) {
    margin-top: 25px;
    min-height: calc(100vh - 25px);
  }
}

#appWrapper.fixSideMenu #overallHolder {
  margin-left: 220px;

  @media (max-width: 1680px) {
    margin-left: 190px;
  }

  @media (max-width: 1280px) {
    margin-left: 160px;
  }
}





#overallHolder.fleetOrderbook {
  margin-top: 3em;
  margin-left: 1em;
}

.content-new {
  position: absolute;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.content-inner-new {
  margin: 0em 0.5em;
  padding: 0.5em 0;
  height: 100%;
}
