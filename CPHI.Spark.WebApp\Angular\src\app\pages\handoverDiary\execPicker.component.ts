import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { ProfilePictureService } from "src/app/services/profilePicture.service";

import { selectableItem, Person, ExecPickerPerson, ProfilePicSize } from '../../model/main.model';

//services
import { ConstantsService } from '../../services/constants.service'
import { SelectionsService } from '../../services/selections.service'
import { HandoverDiaryService } from "./handoverDiary.service";


@Component({
  selector: 'execPicker',
  template: `

      <!-- Sales Exec -->
      <div ngbDropdown dropright class="d-inline-block">
        
        <button [ngClass]="buttonClass" class="btn btn-primary dropdown-toggle buttonGroupRight" (click)="generatePeopleSummary()" ngbDropdownToggle>
          <span>{{title}} <i class="fas" [ngClass]="[IsManager ? 'fa-user-tie': 'fa-user']"></i></span>
        </button>

        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
          
        <ng-container *ngFor="let Person of this.uniqueSalesPeople">

         
        <button  (click)="toggleItem(Person)" [ngClass]="{'active': execInList(Person)}" ngbDropdownItem class="personDropdownButton">
        <div class="spaceBetween">
          <profilePicImage  [personId]="Person.Id"  [size]="profilePicSize"></profilePicImage>
            
              <div class="name">{{Person.Name}}</div>
              <div class="dealsCount">{{Person.DealCount}} </div>
            </div>
          </button>
        

        </ng-container>

        <div class="spaceBetween"> 
        <div><i class="fas fa-user-slash"></i></div>
        <button (click)="toggleNone()" ngbDropdownItem>{{ constants.translatedText.Clear }}</button>
        </div>

        </div>

      </div> 
    `
  ,
  styles: [`

    
    .buttonGroupRight {margin:0px!important;border-radius:0em 0.3em 0.3em 0em!important;}

    .smallPersonImage{width:1.6em; border-radius:20%;  margin-left: 10px; margin-right: 10px;}

    .fa-user-slash{width:1.6em; border-radius:20%;  margin-left: 10px; margin-right: 10px; color: #C0C0C0;}

    /* .dropdown-item{width:15em; height:2em; } */

    .dropdown-menu{
    max-height: 500px; /*Provide height in pixels or in other units as per your layout*/
    overflow-y: auto; /*Provide an auto overflow to diaply scroll*/
    }

    .personDropdownButton{width:23em;height:3em;}
    .personDropdownButton .name{width:10em;}
    .personDropdownButton .dealsCount{width:7em;text-align:right}


    `]
})
export class ExecPickerComponent implements OnInit {
  @Input() franchisesFromParent: string[];
  @Input() buttonClass: string;

  @Input() IsManager: boolean;

  @Input() IsExec: boolean;

  @Output() updateExecs = new EventEmitter<ExecPickerPerson[]>();

  // @Output() updateExecs = new EventEmitter<Person>();

  public franchises: selectableItem[];
  public uniqueSalesPeople: Array<ExecPickerPerson> = [];

  public selectedExec: Person = null;

  public title: string;

  // Adding for multi-select
  public selectedExecs: Array<ExecPickerPerson> = [];

  public profilePicSize: ProfilePicSize;
  public profilePictureService: ProfilePictureService;

  constructor(
    public service: HandoverDiaryService,
    public constants: ConstantsService,
    public selections: SelectionsService,

  ) {




  }


  ngOnInit(): void {

    this.profilePicSize = ProfilePicSize.small;

    this.title = this.IsManager ? this.title = "Manager" : this.IsExec ? "Exec" : ""

  }

  execInList(salesExec: Person) {
    var n = this.selectedExecs.includes(salesExec);

    if (n == true) {
      return true
    }

    return false
  }

  selectExecs() {
    this.updateExecs.emit(this.selectedExecs);
  }

  toggleNone() {

    if (this.uniqueSalesPeople.length > 0) {
      this.selectedExecs = []
    }

    this.selectExecs();

  }

  // If person in list, delete. If not, add.
  toggleItem(exec: Person) {

    if (this.execInList(exec)) {

      this.selectedExecs.forEach((item, index) => {

        if (item === exec) this.selectedExecs.splice(index, 1);
      });

    }

    else {
      this.selectedExecs.push(exec);
    }

    this.selectExecs()
  }

  // Gets all the unique salespeople in current selection
  generatePeopleSummary() {
    // If no sales execs selected, get unique ids for menu
    var personTypeIds = (this.IsManager) ? 'managerIds' : this.IsExec ? 'salesmanIds' : ''
    var personTypeId = (this.IsManager) ? 'ManagerId' : this.IsExec ? 'SalesmanId' : ''
    var personType = (this.IsManager) ? 'Manager' : this.IsExec ? 'Salesman' : ''

    if (this.service[personTypeIds].length == 0) {
      this.uniqueSalesPeople = [];

      //find unique salespeople
      let uniqueIds = [...new Set(this.service.deals.map(e => e[personTypeId]))]

      uniqueIds.forEach(m => {

        var salesmenDeals = this.service.deals.filter(d => d[personTypeId] == m);
        if (salesmenDeals.length > 0) {
          var newP: ExecPickerPerson = { Id: m, Name: salesmenDeals[0][personType], DealCount: salesmenDeals.length };
          this.uniqueSalesPeople.push(newP);
        }


      })

      // Sort alphabetically
      this.uniqueSalesPeople.sort((a, b) => a.Name.localeCompare(b.Name))
    }


  }

}
