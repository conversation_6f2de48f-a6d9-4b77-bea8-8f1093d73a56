
import { EventEmitter, Injectable } from '@angular/core';
import { TopBottomHighlightRule } from "src/app/model/TopBottomHighlightRule";
import { WipDetailParams, WipDetailRow, WipSiteRow } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Injectable({
  providedIn: 'root'
})

export class WipReportService {
    topBottomHighlights: TopBottomHighlightRule[] = [];

    constructor(
        public getData: GetDataMethodsService,
        public selections: SelectionsService,
        public constants: ConstantsService
    ) { }

    getWipDataForAllSites() {
        this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });

        this.getData.getWipSiteRows(this.selections.wipReport.ageAtMonthEnd).subscribe((res: WipSiteRow[]) => {
            this.selections.wipReport.sitesRows = res.filter(x => x.IsSite);
            this.selections.wipReport.sitesTotal = res.filter(x => x.IsTotal);
            this.selections.wipReport.regionsRows = res.filter(x => x.IsRegion);
            this.selections.wipReport.chosenSiteIds = this.selections.wipReport.sitesRows.map(x => x.SiteId);
        }, e => {
            console.error('Error retrieving WIP sites data: ' + JSON.stringify(e));
        }, () => {
            this.selections.wipReport.wipsChangedEmitter.next(true);
            this.selections.wipReport.showDetail = false;
            this.selections.triggerSpinner.next({ show: false });
        })
    }

    getWipDataForSite() {
        this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
        
        let params: WipDetailParams = {
            asAtMonthEnd: this.selections.wipReport.ageAtMonthEnd,
            includeZeroWips: this.selections.wipReport.includeZeroWips,
            siteIds: this.selections.wipReport.chosenSiteIds
        }

        this.getData.getWipsDetails(params).subscribe((res: WipDetailRow[]) => {
            this.selections.wipReport.wipsRows = res;
            this.selections.wipReport.wipsRowsFiltered = res;
        }, e => {
            console.error('Error retrieving WIP detail data: ' + JSON.stringify(e));
        }, () => {
            this.selections.wipReport.wipsChangedEmitter.next(true);
            this.selections.wipReport.showDetail = true;
            this.selections.triggerSpinner.next({ show: false });
        })
    }



    initParams() {
        this.selections.wipReport = {
          ageAtMonthEnd: false,
          includeZeroWips: false,
          chosenSiteIds: null,
          regionsRows: null,
          sitesRows: null,
          sitesTotal: null,
          showDetail: false,
          wipsRows: null,
          wipsRowsFiltered: null,
          wipsChangedEmitter: new EventEmitter()
        }
    
        this.getWipDataForAllSites();
       
      }
}
