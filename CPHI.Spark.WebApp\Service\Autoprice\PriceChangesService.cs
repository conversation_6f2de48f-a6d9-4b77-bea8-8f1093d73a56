﻿using CPHI.Spark.Model.ViewModels.AutoPricing;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using System.Linq;
using CPHI.Spark.DataAccess.AutoPrice;
using Microsoft.Extensions.Configuration;
using CPHI.Spark.Repository;
using CPHI.Spark.Model;
using CPHI.Spark.Model;
using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Model.ViewModels;
using Microsoft.IdentityModel.Tokens;

namespace CPHI.Spark.WebApp.Service.AutoPrice
{
    public interface IPriceChangesService
    {
        Task<IEnumerable<PricingChangeNew>> GetPriceChanges(GetPriceChangesParams parms);
        //Task<IEnumerable<PricingChangeOld>> GetPriceChangesOld(DateTime? date, bool showPriceChangesBelow100, List<int> retailerSiteIds, bool includeNewVehicles);
        Task SetPriceChanges(SetPriceChangesParams parms);
    }
    public class PriceChangesService : IPriceChangesService
    {
        private readonly IUserService userService;
        private readonly IConfiguration configuration;
        private readonly string _connectionString;

        public PriceChangesService(
            IUserService userService, IConfiguration configuration
            )
        {
            this.userService = userService;
            this.configuration = configuration;
            DealerGroupName dealerGroup = userService.GetUserDealerGroupName();
            string dgName = DealerGroupConnectionName.GetConnectionName(dealerGroup);
            _connectionString = configuration.GetConnectionString(dgName);
        }


        //Price Changes
        public async Task<IEnumerable<PricingChangeNew>> GetPriceChanges(GetPriceChangesParams parms)
        {
            IEnumerable<int> userRetailerSiteIds = userService.GetUserRetailerSiteIds();
            var userDealerGroup = userService.GetUserDealerGroupName();
            int userDealerGroupId = userService.GetIdFromAccessToken("DealerGroupId");
            List<int> retailerSiteIds = userRetailerSiteIds.ToList();

            if (parms.retailerSiteIds.Count > 0)
            {
                retailerSiteIds = retailerSiteIds.Intersect(parms.retailerSiteIds).ToList();
            }

            if(parms.lifeCycleStatuses.IsNullOrEmpty())
            {
               parms.lifeCycleStatuses = null;
            }

            string retailerSiteIdsString = string.Join(',', retailerSiteIds);
            var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);
            Dictionary<int, RetailerSiteStrategyBandingDefinition> bandingsDefinitionDict = ConstantsCache.ProvideBandingsDictionary(userDealerGroup);
            
            var priceChangeParms = new GetPriceChangesNewParams
            {
                ChosenDate = parms.date,
                RetailerSiteIds = retailerSiteIdsString,
                IncludeNewVehicles = parms.includeNewVehicles,
                IncludeSmallChanges = parms.showSmallPriceChanges,
                DealerGroupId = userDealerGroupId,
                BandingsDict = bandingsDefinitionDict,
                IncludeUnPublishedAds = parms.includeUnPublishedAds,
                IncludeUnPublishedAdBasedOnSiteSetting = false,
                IncludeNewVehiclesBasedOnSiteSetting = false,
                IncludeLifecycleStatusesBasedOnSiteSetting = true,
                FilterDaysInStockBasedOnSiteSetting = false,
                IncludeVehicleTypesBasedOnSiteSetting = true,
                OnlyKeyChanges = parms.onlyKeyChanges,
                LifeCycleStatuses = parms.lifeCycleStatuses,
                
            };

            var changes = await priceChangesDataAccess.GetPriceChanges(priceChangeParms);

            foreach (var change in changes)
            {
                change.PriceIndicatorRatingAtCurrentSelling = PriceStrategyClassifierService.ProvidePriceIndicatorName(change.PriceIndicatorRatingAtCurrentSelling);
            }


            return changes.OrderByDescending(x => x.DaysListed);
        }

        public async Task SetPriceChanges(SetPriceChangesParams parms)
        {
            int userDealerGroupId = userService.GetIdFromAccessToken("DealerGroupId");
            int userId = userService.GetUserId();
            var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);
            await priceChangesDataAccess.SetPriceChanges(parms.priceChangeIds, userDealerGroupId, userId);
        }



        //public async Task<IEnumerable<PricingChangeOld>> GetPriceChangesOld(DateTime? date, bool showPriceChangesBelow100, List<int> retailerSiteIds, bool includeNewVehicles)
        //{
        //    string userEligibleSites = string.Join(',', userService.GetUserSiteIds());
        //    IEnumerable<int> userRetailerSiteIds = userService.GetUserRetailerSiteIds();
        //    List<int> retailerSiteIdsToUse = userRetailerSiteIds.ToList();
        //    if (retailerSiteIds.Count > 0)
        //    {
        //        retailerSiteIdsToUse = userRetailerSiteIds.Intersect(retailerSiteIds).ToList();
        //    }
        //    string retailerSiteIdsString = string.Join(',', retailerSiteIdsToUse);
        //    //DealerGroup dealerGroup = _userService.GetUserDealerGroup();
        //    var priceChangesDataAccess = new PriceChangesDataAccess(_connectionString);
        //    var changes = (await priceChangesDataAccess.GetPriceChangesOld(userEligibleSites, date, showPriceChangesBelow100, retailerSiteIdsString, includeNewVehicles)).Where(x => x.CouldGenerateNewPrice);

        //    return changes.OrderByDescending(x => x.CouldGenerateNewPrice).ThenBy(x => x.DaysListed);
        //}
    }
}