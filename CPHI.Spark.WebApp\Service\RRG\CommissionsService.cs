﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.RRG;
using CPHI.Spark.Repository;using CPHI.Spark.Model;
using CPHI.Spark.WebApp.DataAccess;
using CPHI.Spark.WebApp.RRG;
using MoreLinq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CPHI.Spark.BusinessLogic.RRG;
using Microsoft.Extensions.Configuration;

namespace CPHI.Spark.WebApp.Service.RRG
{
    public interface ICommissionsService
    {
        Task<IEnumerable<CommissionItem>> GetCommissionPersonItems(DateTime monthStart, int salesmanId);
        Task<IEnumerable<CommissionRow>> GetCommissionPersonRows(DateTime monthStart, IEnumerable<int> siteIds);
        Task<IEnumerable<CommissionRow>> GetCommissionSiteRows(DateTime monthStart);
        Task<CommissionTileSummary> GetCommissionSummarySpain(DateTime MonthStart, List<int> SiteIds, Model.DealerGroupName dealerGroup);
        Task<IEnumerable<CommissionBusinessManagerRow>> GetCommissionBusinessManagerRows(DateTime monthStart);
        Task<IEnumerable<CommissionStatement>> GetLBDMCommission(DateTime startOfMonth);
        Task<IEnumerable<CommissionRow>> GetLBDMCommissionPersonRows(DateTime monthStart);
        Task<IEnumerable<CommissionItem>> GetCommissionLBDMPersonItems(DateTime monthStart, int lbdmId);
    }

    public class CommissionsService : ICommissionsService
    {
        private readonly ICommissionItemsCache commissionItemsCache;
        private readonly ICommissionAdjustmentDataAccess commissionAdjustmentDataAccess;
        private readonly IUserService userService;
        private readonly ISitesService sitesService;
        private readonly ICommissionsDataAccess commissionsDataAccess;
        private readonly IConfiguration _configuration;

        public CommissionsService(ICommissionItemsCache commissionItemsCache, IUserService userService, ISitesService sitesService,
            ICommissionAdjustmentDataAccess commissionAdjustmentDataAccess, ICommissionsDataAccess commissionsDataAccess, IConfiguration configuration)
        {
            this.commissionItemsCache = commissionItemsCache;
            this.userService = userService;
            this.sitesService = sitesService;
            this.commissionAdjustmentDataAccess = commissionAdjustmentDataAccess;
            this.commissionsDataAccess = commissionsDataAccess;
            _configuration = configuration;
        }

        public async Task<IEnumerable<CommissionRow>> GetCommissionSiteRows(DateTime monthStart)
        {
            int userId = userService.GetUserId();
            IEnumerable<int> userSiteIds = userService.GetUserSiteIds();// await userService.GetUserSiteIdsFomDatabase(userId,userService.GetUserDealerGroupName());
            IEnumerable<CommissionStatement> statements = await commissionItemsCache.GetCommissionItems(monthStart);

            //is user isn't allowed to review all statements, chop down to only those for the user
            string commissionAuthority = userService.GetUserCommissionAuthority();
            if(commissionAuthority == "selfOnly") { statements = statements.Where(x => x.SalesmanId == userId); }
            else if(commissionAuthority != "review") { return new List<CommissionRow>(); }
            


            IEnumerable<SiteVM> sites = (await sitesService.GetSites(userId, userService.GetUserDealerGroupName())).Where(x => x.IsActive && x.IsSales).OrderBy(x => x.SortOrder);

            //have to summarise them by site
            List<CommissionRow> siteResults = new List<CommissionRow>();
            ILookup<int, CommissionStatement> resultsBySite = statements.ToLookup(x => x.SiteId);
            foreach (var site in sites)
            {
                IEnumerable<CommissionStatement> statementsForSite = resultsBySite[site.SiteId];

                //aggregate up siteItems into a site row
                CommissionRow siteRow = new CommissionRow()
                {
                    Label = site.SiteDescription,
                    RowId = site.SiteId,
                    SiteId = site.SiteId
                };

                if (statementsForSite.Count() > 0)
                {
                    foreach (var statement in statementsForSite)
                    {
                        string role = statement.Items.First().Role;
                        if (role == "New") { siteRow.ExecsNew++; }
                        if (role == "Used") { siteRow.ExecsUsed++; }
                        if (role == "NewUsed") { siteRow.ExecsNewUsed++; }
                        if (role == "Fleet") { siteRow.ExecsFleet++; }

                        AccumulateItemsIntoSiteRow(statement, siteRow);
                    }
                }

                siteResults.Add(siteRow);
            }

            //add regions
            List<CommissionRow> regionResults = new List<CommissionRow>();

            foreach (var siteGroup in sites.ToLookup(x => x.RegionDescription))
            {
                var thisRegionSites = siteResults.Where(x => siteGroup.Select(x => x.SiteDescription).Contains(x.Label));
                CommissionRow regionRow = new CommissionRow()
                {
                    Label = siteGroup.Key,
                    IsRegion = true,
                    RegionDescription = siteGroup.Key
                };
                AccumulateRowsIntoRow(thisRegionSites, regionRow);
                regionResults.Add(regionRow);
            }


            //add total
            List<CommissionRow> results = siteResults.Concat(regionResults).ToList();
            CommissionRow totalRow = new CommissionRow()
            {
                Label = "Total",
                IsTotal = true,
            };
            AccumulateRowsIntoRow(regionResults, totalRow);
            results.Add(totalRow);

            return results;
        }



        public async Task<IEnumerable<CommissionRow>> GetCommissionPersonRows(DateTime monthStart, IEnumerable<int> siteIds)
        {
            int userId = userService.GetUserId();
            IEnumerable<int> userSiteIds = userService.GetUserSiteIds();// await userService.GetUserSiteIdsFomDatabase(userId,userService.GetUserDealerGroupName());
            IEnumerable<int> siteIdsToLimitTo = userSiteIds.Intersect(siteIds);
            IEnumerable<CommissionStatement> allStatements = await commissionItemsCache.GetCommissionItems(monthStart);
            IEnumerable<CommissionStatement> statementsForSites = allStatements.Where(x => siteIdsToLimitTo.Contains(x.SiteId));

            //is user isn't allowed to review all statements, chop down to only those for the user
            string commissionAuthority = userService.GetUserCommissionAuthority();
            if (commissionAuthority == "selfOnly") { statementsForSites = statementsForSites.Where(x => x.SalesmanId == userId); }
            else if (commissionAuthority != "review") { return new List<CommissionRow>(); }

            //now build into a person summary
            List<CommissionRow> results = new List<CommissionRow>();
            foreach (var statement in statementsForSites)
            {
                CommissionRow personRow = new CommissionRow()
                {
                    Label = statement.Items.First().SalesmanName,
                    RowId = statement.SalesmanId,
                    ExecsNew = statement.Items.First().Role == "New" ? 1 : 0,
                    ExecsFleet = statement.Items.First().Role == "Fleet" ? 1 : 0,
                    ExecsUsed = statement.Items.First().Role == "Used" ? 1 : 0,
                    ExecsNewUsed = statement.Items.First().Role == "NewUsed" ? 1 : 0
                };
                AccumulateItemsIntoSiteRow(statement, personRow);
                results.Add(personRow);
            }

            return results.OrderBy(x => x.Label);
        }

        public async Task<IEnumerable<CommissionRow>> GetLBDMCommissionPersonRows(DateTime monthStart)
        {
            int userId = userService.GetUserId();
            IEnumerable<int> userSiteIds = userService.GetUserSiteIds();// await userService.GetUserSiteIdsFomDatabase(userId,userService.GetUserDealerGroupName());
            IEnumerable<int> siteIdsToLimitTo = userSiteIds;//.Intersect(siteIds);
            IEnumerable<CommissionStatement> allStatements = await GetLBDMCommission(monthStart);// commissionItemsCache.GetCommissionItems(monthStart);
            IEnumerable<CommissionStatement> statementsForSites = allStatements.Where(x => siteIdsToLimitTo.Contains(x.SiteId));

            //is user isn't allowed to review all statements, chop down to only those for the user
            string commissionAuthority = userService.GetUserCommissionAuthority();
            if (commissionAuthority == "selfOnly") { statementsForSites = statementsForSites.Where(x => x.SalesmanId == userId); }
            else if (commissionAuthority != "review") { return new List<CommissionRow>(); }

            //now build into a person summary
            List<CommissionRow> results = new List<CommissionRow>();

            foreach (var statement in statementsForSites)
            {
                CommissionRow lbdmRow = new CommissionRow()
                {
                    Label = statement.Items.First().SalesmanName,
                    RowId = statement.Items.First().SalesmanId,
                    ExecsNew = 0,
                    ExecsFleet = 0,
                    ExecsUsed = 0,
                    ExecsNewUsed = 0,
                };

                AccumulateItemsIntoSiteRow(statement, lbdmRow);
                results.Add(lbdmRow);
            }

            CommissionRow totalRow = new CommissionRow()
            {
                Label = "Total",
                RowId = 0,
                ExecsNew = 0,
                ExecsFleet = 0,
                ExecsUsed = 0,
                ExecsNewUsed = 0,
                IsTotal = true,
                UnitsRenault = results.Sum(x => x.UnitsRenault),
                UnitsMotability = results.Sum(x => x.UnitsMotability),
                UnitsDacia = results.Sum(x => x.UnitsDacia),
                UnitsNissan = results.Sum(x => x.UnitsNissan),
                UnitsOther = results.Sum(x => x.UnitsOther),
                UnitsUsed = results.Sum(x => x.UnitsUsed),
                UnitsFleetCar = results.Sum(x => x.UnitsFleetCar),
                UnitsFleetVan = results.Sum(x => x.UnitsFleetVan),
                UnitsRenaultCar = results.Sum(x => x.UnitsRenaultCar),
                UnitsRenaultCarEV = results.Sum(x => x.UnitsRenaultCarEV),
                UnitsRenaultVan = results.Sum(x => x.UnitsRenaultVan),
                UnitsRenaultVanEV = results.Sum(x => x.UnitsRenaultVanEV),
                UnitsUsedCar = results.Sum(x => x.UnitsUsedCar),
                UnitsUsedVan = results.Sum(x => x.UnitsUsedVan),
                UnitsCoreFleet = results.Sum(x => x.UnitsCoreFleet),
                PaintCount = results.Sum(x => x.PaintCount),
                GapCount = results.Sum(x => x.GapCount),
                CosmeticCount = results.Sum(x => x.CosmeticCount),
                TyreCount = results.Sum(x => x.TyreCount),
                TyreAlloyCount = results.Sum(x => x.TyreAlloyCount),
                WheelGuardCount = results.Sum(x => x.WheelGuardCount),
                WarrantyCount = results.Sum(x => x.WarrantyCount),
                MotCount = results.Sum(x => x.MotCount),
                RoadsideAssistCount = results.Sum(x => x.RoadsideAssistCount),
                FinanceCount = results.Sum(x => x.FinanceCount),
                PartExCount = results.Sum(x => x.PartExCount),
                ServiceCount = results.Sum(x => x.ServiceCount),
                TotalCommission = results.Sum(x => x.TotalCommission)
            };

            results.Add(totalRow);

            return results.OrderBy(x => x.Label);
        }

        public async Task<IEnumerable<CommissionItem>> GetCommissionLBDMPersonItems(DateTime monthStart, int lbdmId)
        {
            int userId = userService.GetUserId();
            IEnumerable<CommissionStatement> allStatements = await GetLBDMCommission(monthStart);
            var result = allStatements.First(x => x.SalesmanId == lbdmId).Items;
            return result;
        }


        public async Task<IEnumerable<CommissionItem>> GetCommissionPersonItems(DateTime monthStart, int salesmanId)
        {
            int userId = userService.GetUserId();
            IEnumerable<CommissionStatement> statements = await commissionItemsCache.GetCommissionItems(monthStart);
            var result =  statements.First(x => x.SalesmanId == salesmanId).Items;
            return result;
        }


        public  async Task<IEnumerable<CommissionStatement>> GetLBDMCommission(DateTime startOfMonth)
        {
            string dgName = DealerGroupConnectionName.GetConnectionName(DealerGroupName.RRGUK);
            string connectionString = _configuration.GetConnectionString(dgName);

            LBDMCommissionLogic logic = new LBDMCommissionLogic(connectionString);
            return await logic.CalcCommissionsForWebApp(startOfMonth.Date, DealerGroupName.RRGUK);
        }


        private static void AccumulateItemsIntoSiteRow(CommissionStatement statement, CommissionRow newItem)
        {
            foreach (var item in statement.Items)
            {
                if (item.StockNumber == "610680/2")
                {
                    { }
                }
                newItem.UnitsRenault += item.Units_Renault;
                newItem.UnitsMotability += item.Units_Motability;
                newItem.UnitsDacia += item.Units_Dacia;
                newItem.UnitsNissan += item.Units_Nissan;
                newItem.UnitsOther += item.Units_Other;
                newItem.UnitsUsed += item.Units_Used;
                newItem.UnitsFleetCar += item.Units_FleetCar;
                newItem.UnitsFleetVan += item.Units_FleetVan;
                newItem.PaintCount += item.PaintProtection_Count;
                newItem.GapCount += item.Gap_Count;
                newItem.CosmeticCount += item.Cosmetic_Count;
                newItem.TyreCount += item.Tyre_Count;
                newItem.TyreAlloyCount += item.TyreAlloy_Count;
                newItem.WheelGuardCount += item.WheelGuard_Count;
                newItem.WarrantyCount += (item.Warranty_Count + item.Warranty2Yr_Count + item.WarrantyLifetime_Count);
                newItem.MotCount += (item.Mot12_Count + item.Mot24_Count + item.Mot36_Count);
                newItem.RoadsideAssistCount += (item.RoadsideAssist12_Count + item.RoadsideAssist24_Count + item.RoadsideAssist36_Count);
                newItem.FinanceCount += item.Finance_Count;
                newItem.PartExCount += item.PartEx_Count;
                newItem.ServiceCount += item.ServicePlan_Count;


                //LBDM properties
                if(item.VehicleCommissionType == "Core Fleet") { newItem.UnitsCoreFleet += item.Units_Count; }
                else if(item.VehicleCommissionType == "Renault Van EV") { newItem.UnitsRenaultVanEV += item.Units_Count; }
                else if(item.VehicleCommissionType == "Renault Van") { newItem.UnitsRenaultVan += item.Units_Count; }
                else if(item.VehicleCommissionType == "Renault Car") { newItem.UnitsRenaultCar += item.Units_Count; }
                else if(item.VehicleCommissionType == "Renault Car EV") { newItem.UnitsRenaultCarEV += item.Units_Count; }
                else if(item.VehicleCommissionType == "Dacia") { newItem.UnitsDacia += item.Units_Count; }
                else if(item.VehicleCommissionType == "Used vans") { newItem.UnitsUsedVan += item.Units_Count; }
                else if(item.VehicleCommissionType == "Used cars") { newItem.UnitsUsedCar += item.Units_Count; }



                // For non-Used
                newItem.TotalCommission += item.TotalCommission;

                
            }
        }

        private static void AccumulateRowsIntoRow(IEnumerable<CommissionRow> items, CommissionRow newItem)
        {
            foreach (var item in items)
            {
                newItem.ExecsFleet += item.ExecsFleet;
                newItem.ExecsUsed += item.ExecsUsed;
                newItem.ExecsNew += item.ExecsNew;
                newItem.ExecsNewUsed += item.ExecsNewUsed;

                newItem.UnitsRenault += item.UnitsRenault;
                newItem.UnitsMotability += item.UnitsMotability;
                newItem.UnitsDacia += item.UnitsDacia;
                newItem.UnitsNissan += item.UnitsNissan;
                newItem.UnitsOther += item.UnitsOther;
                newItem.UnitsUsed += item.UnitsUsed;
                newItem.UnitsFleetCar += item.UnitsFleetCar;
                newItem.UnitsFleetVan += item.UnitsFleetVan;
                newItem.PaintCount += item.PaintCount;
                newItem.GapCount += item.GapCount;
                newItem.CosmeticCount += item.CosmeticCount;
                newItem.TyreCount += item.TyreCount;
                newItem.TyreAlloyCount += item.TyreAlloyCount;
                newItem.WheelGuardCount += item.WheelGuardCount;
                newItem.WarrantyCount += item.WarrantyCount;
                newItem.MotCount += item.MotCount;
                newItem.RoadsideAssistCount += item.RoadsideAssistCount;
                newItem.FinanceCount += item.FinanceCount;
                newItem.PartExCount += item.PartExCount;
                newItem.ServiceCount += item.ServiceCount;
                newItem.TotalCommission += item.TotalCommission;
            }
        }

        public async Task<CommissionTileSummary> GetCommissionSummarySpain(DateTime MonthStart, List<int> SiteIds, Model.DealerGroupName dealerGroup)
        {
            int userId = userService.GetUserId();
            return await commissionAdjustmentDataAccess.GetCommissionSummarySpain(MonthStart, SiteIds, userId, userService.GetUserDealerGroupName());
        }

        public async Task<IEnumerable<CommissionBusinessManagerRow>> GetCommissionBusinessManagerRows(DateTime monthStart)
        {
            IEnumerable<int> userSites = userService.GetUserSiteIds();
            List<CommissionBusinessManagerRow> results = (await commissionsDataAccess.GetCommissionBusinessManagerRows(monthStart, userService.GetUserDealerGroupName())).ToList();

            //build into a summary by person.  One person may cover multiple sites.
            List<CommissionBusinessManagerRow> combinedResults = new List<CommissionBusinessManagerRow>();
            foreach (var item in results.ToLookup(x=>x.Label))
            {
                combinedResults.Add( new CommissionBusinessManagerRow(item.ToList(),item.Key,false));
            }


            List<CommissionBusinessManagerRow> forUser = results.Where(x=>userSites.Contains(x.SiteId)).ToList();
            combinedResults.Add(new CommissionBusinessManagerRow(forUser, "Total",true));
            return combinedResults;

           

        }


     

    }
}