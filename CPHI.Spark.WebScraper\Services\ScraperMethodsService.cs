﻿using OpenQA.Selenium;
using SeleniumExtras.WaitHelpers;
using System;
using OpenQA.Selenium.Support.UI;
using OpenQA.Selenium.Chrome;
using System.IO;
using System.Linq;
using OpenQA.Selenium.IE;
using System.Diagnostics;

namespace CPHI.Spark.WebScraper
{
    public static class ScraperMethodsService
    {

        

        //define wait and find method
        public static IWebElement WaitAndFind(IWebDriver _driver, string JobName, string findXPath, bool andClick = false, decimal extend = 0)
        {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10 + (double) extend));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;

            IWebElement element = wait.Until(ExpectedConditions.ElementExists(By.XPath(findXPath)));
            js.ExecuteScript("arguments[0].setAttribute('style', ' border: 1px solid red;');", element); //background: orange;
            //also click if required
            if (andClick)
            {
                js.ExecuteScript("arguments[0].click();", element);
                System.Threading.Thread.Sleep(500); //just for safety
            }
            return element;
        }

        public static ChromeOptions SetChromeOptions(string chromeProfile, int portNumber)
        {

            ChromeOptions options = new ChromeOptions();
            options.Proxy = null;

            options.AddArguments("--start-maximized");
            options.AddArgument("no-sandbox");

            options.AddArguments("--disable-dev-shm-usage");
            options.AddArgument($"--remote-debugging-port={portNumber}");
            options.AddArgument($"user-data-dir=C:\\cphiRoot\\ChromeProfiles\\{chromeProfile}");

            options.AddUserProfilePreference("profile.default_content_setting_values.automatic_downloads", 1);
            options.AddUserProfilePreference("download.prompt_for_download", false);
            options.AddUserProfilePreference("disable-popup-blocking", "true");
            options.AddUserProfilePreference("download.default_directory", @ConfigService.FileDownloadLocation);

            // May need this for cookies - potentially have as a boolean parameter
            // options.AddUserProfilePreference("profile.default_content_setting_values.cookies", 1);

            return options;
        }

        public static InternetExplorerOptions SetIEOptions()
        {
            var ieOptions = new InternetExplorerOptions();
            ieOptions.AttachToEdgeChrome = true;

            // Change the path accordingly, using backslashes
            ieOptions.EdgeExecutablePath = @"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe";
            ieOptions.IntroduceInstabilityByIgnoringProtectedModeSettings = true;
            //ieOptions.InitialBrowserUrl = "https://www.google.com/";     //adding google made it work on RRG VM but removing it made it work in Vindis.
            return ieOptions;
        }

        public static void KillOldEdgeInstances()
        {
            Process[] chromeInstances = Process.GetProcessesByName("msedge");
            foreach (Process p in chromeInstances) { p.Kill(); }
        }

        public static void ClearDownloadsFolder()
        {
            if(ConfigService.FileDownloadLocation != null)
            {
                System.IO.DirectoryInfo di = new DirectoryInfo(ConfigService.FileDownloadLocation);

                foreach (FileInfo file in di.GetFiles())
                {
                    file.Delete();
                }
                foreach (DirectoryInfo dir in di.GetDirectories())
                {
                    dir.Delete(true);
                }
            }

        }


        public static IWebElement WaitAndFindById(IWebDriver _driver, string JobName, string findId, decimal extend = 0)
        {
            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10 + (double)extend));

            IWebElement element = wait.Until(ExpectedConditions.ElementExists(By.Id(findId)));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            js.ExecuteScript("arguments[0].setAttribute('style', 'border: 1px solid red;');", element);  //background: orange; 

            return element;
        }


        public static IWebElement WaitAndClickById(IWebDriver _driver, string JobName, string findId)
        {

            WebDriverWait wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
            IWebElement element = wait.Until(ExpectedConditions.ElementExists(By.Id(findId)));
            IJavaScriptExecutor js = (IJavaScriptExecutor)_driver;
            js.ExecuteScript("arguments[0].setAttribute('style', ' border: 1px solid red;');", element); //background: orange;

            js.ExecuteScript("arguments[0].click();", element);
            System.Threading.Thread.Sleep(500); //just for safety

            return element;

        }

        public static void TakeScreenshot(IWebDriver _driver)
        {
            var screenshot = (_driver as ITakesScreenshot).GetScreenshot();
            screenshot.SaveAsFile("c:\\cphiroot\\screenshot.png");
        }

        public static void WaitUntilFileDownloaded(string filenameFragment)
        {
            DateTime start = DateTime.Now;

            FileInfo[] recentlySavedFiles = FindFiles(filenameFragment);

            var x = recentlySavedFiles.Length;

            TimeSpan waitTime = DateTime.Now - start;

            while (recentlySavedFiles.Length == 0 && waitTime.Seconds < 120)
            {
                recentlySavedFiles = FindFiles(filenameFragment);
                System.Threading.Thread.Sleep(200);
                waitTime = DateTime.Now - start;

            }

            //TimeSpan duration = DateTime.Now - startTime;

            //emerged so can now continue
        }

        public static FileInfo[] FindFiles(string filenameFragment)
        {

            FileInfo[] recentlySavedFiles = new DirectoryInfo(ConfigService.FileDownloadLocation).EnumerateFiles().Select(x =>
            {
                x.Refresh();
                return x;
            }).Where(x => x.Name.Contains(filenameFragment) && (x.Extension == ".xlsx" || x.Extension == ".xls" || x.Extension == ".csv")).ToArray();
            return recentlySavedFiles;
        }



    }
}
