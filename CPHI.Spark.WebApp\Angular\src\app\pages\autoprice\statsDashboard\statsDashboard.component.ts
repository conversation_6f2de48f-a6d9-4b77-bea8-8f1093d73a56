
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { BIChartTileDataType } from 'src/app/components/biChartTile/biChartTile.component';
import { StatsDashboardFiltersForStockReport } from 'src/app/model/StatsDashboardFiltersForStockReport';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { AdvertListingDetailService } from '../advertListingDetail/advertListingDetail.service';
import { StatsDashboardService } from './statsDashboard.service';
import { StatsDaysListed } from 'src/app/model/StatsDaysListed';
import { StatsRetailRating } from 'src/app/model/StatsRetailRating';
import { StatsVsStrategyPrice } from 'src/app/model/StatsVsStrategyPrice';
import { BarChartParams } from 'src/app/model/BarChartParams';
import { StatsDashboard } from 'src/app/model/StatsDashboard';
import { StatsDashboardTableParams } from './statsDashboardTable/statsDashboardTable.component';
import { StatsPerformanceRating } from 'src/app/model/StatsPerformanceRating';
import { CphPipe } from 'src/app/cph.pipe';

export enum StatsDashboardPageComponentType {
  blobDisplayer, grid, page
}


@Component({
  selector: 'statsDashboard',
  templateUrl: './statsDashboard.component.html',
  styleUrls: ['./statsDashboard.component.scss']
})



export class StatsDashboardComponent implements OnInit {
  //indicateNewData: boolean;

  public dataTypes = BIChartTileDataType;

  advertsByRetailRating: BarChartParams;
  advertsByDaysListed: BarChartParams;
  vsStrategyPrice: BarChartParams;
  statsDashboard: StatsDashboard;
  tableParams: StatsDashboardTableParams;
  performanceRating: BarChartParams;

  advertQualityFilters: string[] = ['IsLowQuality', 'NoAttentionGrabber', 'LessThan9Images', 'NoImages', 'NoVideo'];

  @ViewChild('vsStrategyBarsContainer', { static: true }) vsStrategyBarsContainer: ElementRef;


  

  constructor(
    public service: StatsDashboardService,
    public constants: ConstantsService,
    public advertListingDetailService: AdvertListingDetailService,
    public router: Router,
    public selections: SelectionsService,
    public cphPipe: CphPipe

  ) {


  }

  // get countChosenItems() {
  //   return this.service.simpleExampleItemsFiltered.filter(x => x.isChosen).length;
  // }




  async ngOnInit() {
    this.selections.triggerSpinner.next({ show: true, message: 'Loading...' });
    this.service.initParams();
    this.service.pageRef = this;
    await this.service.getData();

  }



  ngOnDestroy() {

  }



  public async onChosenSitesChange() {
    this.service.filterModel.RetailerSiteName = this.retailerSiteNameFilter();
    await this.service.getData();
  }

  public async onChosenRegionsChange() {
    const chosenSitesForRegions = this.constants.Sites.filter(x => Array.from(this.service.chosenRegionNames).includes(x.RegionDescription)).map(x => x.SiteDescription);
    this.service.chosenSiteNames = new Set<string>(this.constants.RetailerSites.filter(x => chosenSitesForRegions.includes(x.Name)).map(x => x.Name));
    this.service.filterModel.RetailerSiteName = this.retailerSiteNameFilter();
    await this.service.getData();
  }


  dealWithNewData(data: StatsDashboard): void {

    this.statsDashboard = data;
    this.service.showTestStrategySlider = data.TestStrategyPriceAvailable ? true : false;

    this.formatDataForBarCharts();
    this.formatDataForTable();
    //this.indicateNewData = true;

    if (this.service.gridRef) {
      this.service.gridRef.dealWithNewData(data.Vehicles);
    }

    this.service.newSmallChartDataEmitter.emit(this);
    this.service.selectionsService.triggerSpinner.next({ show: false });

  }

  barHeight() {
    return this.constants.div((this.vsStrategyBarsContainer.nativeElement.clientHeight - 150), 6);
  }

  get filteringForAdvertQuality() {
    const f: StatsDashboardFiltersForStockReport = this.service.filters;
    if (f.LessThan9Images || f.IsLowQuality || f.NoAttentionGrabber || f.NoImages || f.NoVideo) { return true; }
    return false;
  }

  isFilterActive(filter: string) {
    return this.service.filters[filter];
  }

  hasFilters() {
    return Object.keys(this.service.filterModel).length > 0;
  }


  formatDataForBarCharts() {
    const rrSub20 = getComputedStyle(document.documentElement).getPropertyValue('--rrSub20');
    const rrSub40 = getComputedStyle(document.documentElement).getPropertyValue('--rrSub40');
    const rrSub60 = getComputedStyle(document.documentElement).getPropertyValue('--rrSub60');
    const rrSub80 = getComputedStyle(document.documentElement).getPropertyValue('--rrSub80');
    const rr80Plus = getComputedStyle(document.documentElement).getPropertyValue('--rr80Plus');
    const piNoAnalysis = getComputedStyle(document.documentElement).getPropertyValue('--piNoAnalysis');

    const rr: StatsRetailRating = this.statsDashboard.RetailRating;

    this.advertsByRetailRating = {
      Labels: ['<20', '20 to 39', '40 to 59', '60 to 79', '80+'],
      Values: [rr.Below20, rr.Below40, rr.Below60, rr.Below80, rr.EightyPlus],
      Colours: [rrSub20, rrSub40, rrSub60, rrSub80, rr80Plus],
      Title: '',
      AverageLineValue: 0,
      DataFormat: 'number',
      yMax: undefined,
      yMin: undefined
    };

    const dl: StatsDaysListed = this.statsDashboard.DaysListed;
    this.advertsByDaysListed = {
      Labels: ['<20', '<40', '<60', '60+'],
      Values: [dl.Under20, dl.Under40, dl.Under60, dl.SixtyPlus],
      Colours: [rr80Plus, rrSub80, rrSub40, rrSub20],
      Title: '',
      AverageLineValue: 0,
      DataFormat: 'number',
      yMax: undefined,
      yMin: undefined
    };

    const vs: StatsVsStrategyPrice = this.statsDashboard.VsStrategyPrice;
    this.vsStrategyPrice = {
      Labels: ['V. Underpriced', 'Underpriced', 'On Strategy', 'Overpriced', 'V. Overpriced', 'No Strat Price'],
      Values: [vs.VeryUnderPriced, vs.UnderPriced, vs.OnStrategy, vs.OverPriced, vs.VeryOverPriced, vs.NoStrategyPrice],
      Colours: [rrSub20, rrSub60, rr80Plus, rrSub60, rrSub20, piNoAnalysis],
      Title: '',
      AverageLineValue: 0,
      DataFormat: 'number',
      yMax: undefined,
      yMin: undefined
    };

    const pr: StatsPerformanceRating = this.statsDashboard.PerformanceRating;
    this.performanceRating = {
      Labels: ['Low', '< Average', '> Average', 'Excellent', 'No Rating'],
      Values: [pr.Low, pr.BelowAvg, pr.AboveAvg, pr.Excellent, pr.None],
      Colours: [rrSub20, rrSub60, rrSub80, rr80Plus, piNoAnalysis],
      Title: '',
      AverageLineValue: 0,
      DataFormat: 'number',
      yMax: undefined,
      yMin: undefined
    };
  }

  formatDataForTable() {
    this.tableParams = {
      vehicles: this.statsDashboard.Vehicles,
      gridRef: null,
      dealWithFilteredItems: null
    }
  }

  get getRowCount() {
    return `(${this.cphPipe.transform(this.service.gridApi?.getDisplayedRowCount(), 'number', 0)})`;
  }

  get isFiltered() {
    return Object.keys(this.service.filterModel).length === 0;
  }

  retailerSiteNameFilter() {
    return {
      filterType: 'labelSetFilter',
      values: this.service.chosenSiteNames
    }
  }

  toggleUseTestStrategy(): void
  {
    this.service.useTestStrategy = !this.service.useTestStrategy;
    this.service.getData();
  }
  
}

