import { Component, Input, OnInit } from '@angular/core';
import { RetailRatingParams } from 'src/app/model/RetailRatingParams';
import { VehicleAdvertDetail } from "src/app/model/VehicleAdvertDetail";
import { ConstantsService } from 'src/app/services/constants.service';
import { AutoPriceInsightsModalService } from '../autoPriceInsightsModal.service';
import { SimpleTextModalComponent } from 'src/app/pages/fleetOrderbook/simpleTextModal/simpleTextModal.component';
import { BasicInfoModalComponent } from '../../basicInfoModal/basicInfoModal.component';
import { AutopriceRendererService } from 'src/app/services/autopriceRenderer.service';
import { LocationOptimiserService } from 'src/app/pages/autoprice/locationOptimiser/locationOptimiser.service';

interface BestMove {
  location: string;
  priceChange: number;
  daysToSellChange: number;
}

@Component({
  selector: 'vehicleDetails',
  templateUrl: './vehicleDetails.component.html',
  styleUrls: ['./vehicleDetails.component.scss']
})
export class VehicleDetailsComponent implements OnInit {
  //@Input() data: VehicleAdvertDetail;

  NationalRetailMktConditionPopupTextPart1: string;
  NationalRetailMktConditionPopupTextPart2: string;
  MarketDemandPopupText: string;
  MarketSupplyPopupText: string;
  bestMoveDetails: BestMove;

  constructor(

    public service: AutoPriceInsightsModalService,
    public constantsService: ConstantsService,
    private autopriceRendererService: AutopriceRendererService,
    public locationOptimiserService: LocationOptimiserService

  ) { }

  ngOnInit(): void {
    this.NationalRetailMktConditionPopupTextPart1 = "This shows the difference between the supply available in the market and the demand from potential buyers on Auto Trader. It's an indicator of the current market conditions for this vehicle nationally."
    this.NationalRetailMktConditionPopupTextPart2 = "You can use it to gauge if current market conditions are more or less favourable than normal and then consider the impact this might have on how long the vehicle will take to sell."
    this.MarketDemandPopupText = "This shows if the current level of buyer demand for this particular vehicle on Auto Trader is higher or lower than usual. We calculate this by analysing how many people have been searching for the vehicle on Auto Trader over the last 7 days in comparison to the level of interest we have seen over the last 6 months."
    this.MarketSupplyPopupText = "This shows if the current level of supply in the national market is relatively high or low for this particular vehicle. We calculate this by comparing the national supply level for the vehicle over the last 7 days to the usual level of supply that has been available over the last 6 months.";

    if (this.service.modalItem.LocationChanges.length > 0) {
      // Re-order to get the best move
      const bestMove = this.service.modalItem.LocationChanges.sort((a, b) => {
        if (b.StrategyPriceNew !== a.StrategyPriceNew) {
            return b.StrategyPriceNew - a.StrategyPriceNew;
        }
        return a.RetailerSiteDaysToSellNew - b.RetailerSiteDaysToSellNew;
      })[0];

      // Only set the best move if either price or DTS is better than current
      if (bestMove.StrategyPriceNew > bestMove.StrategyPriceCurrent ) {  //|| bestMove.RetailerSiteDaysToSellNew < bestMove.RetailerSiteDaysToSellCurrent
        this.bestMoveDetails = {
          location: bestMove.RetailerSiteNameNew,
          priceChange: bestMove.StrategyPriceNew - bestMove.StrategyPriceCurrent,
          daysToSellChange: bestMove.RetailerSiteDaysToSellCurrent - bestMove.RetailerSiteDaysToSellNew
        }
      }
    }
  }

  get data(): VehicleAdvertDetail {
    return this.service.modalItem.AdvertDetail
  }

  get description():string{
    return this.data.Description ?? 'No description'
  }

  getRetailRatingParams(data: VehicleAdvertDetail): RetailRatingParams {
    return {
      Make: data.Make,
      RetailRating: data.RetailRating,
      NationalRetailRating: data.NationalRetailRating
    }
  }

  buildPortalUrl() {
    return this.constantsService.buildPortalUrl(this.data.WebSiteStockIdentifier);
  }

  buildAdUrl() {
    return this.constantsService.buildAdUrl(this.data.WebSiteSearchIdentifier, this.data.VehicleType);
  }

  launchDescriptionModal() {

    const modalRef = this.service.modalService.open(BasicInfoModalComponent, { keyboard: true, size: 'md' });
    modalRef.componentInstance.header = 'Full advert description'
    modalRef.componentInstance.body = this.data.Description
    modalRef.result.then((result) => {
      if (result) { }
    })
  }

  updateUrl() {
    let imageElement: HTMLImageElement = document.getElementById('vehicleImage') as HTMLImageElement;
    imageElement.src = '../../../../../assets/imgs/autoTrader/palceholder-car.png';
  }

  copyReg() {
    // Get the VehicleReg from this.data and replace any spaces with an empty string
    const vehicleRegWithoutSpaces = this.data.VehicleReg.replace(/\s+/g, '');

    // Use the Clipboard API to copy the value to the clipboard
    navigator.clipboard.writeText(vehicleRegWithoutSpaces)
      .then(() => {
        console.log('Vehicle registration copied to clipboard successfully!');
        this.constantsService.toastSuccess(`Copied ${vehicleRegWithoutSpaces} to clipboard!`)
      })
      .catch(err => {
        console.error('Failed to copy vehicle registration to clipboard: ', err);
      });


  }


  get ageAndOwnersString(){
    
    return this.autopriceRendererService.provideAgeAndOwnersString(this.data.AgeAndOwners)
    
  }

  goToSiteTransfer() {
    this.locationOptimiserService.initialiseTableLayoutManagement();
    this.service.getLocationOptimiserAdverts(this.service.modalItem.AdvertDetail.AdId);
    this.service.modalView = 'transfer';
  }
 

}
