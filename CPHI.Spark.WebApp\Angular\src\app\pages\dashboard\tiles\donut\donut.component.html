<div class="dashboard-tile-inner">
  <div [ngClass]="{'clickable':isClickableHeader()}" class="tileHeader" (click)="navigateToDealsForTheMonth()">
    <div class="headerWords">
      <h4>
        <span *ngIf="departmentName=='New'"> {{constants.translatedText.New}}</span>
        <span *ngIf="departmentName=='Used'"> {{constants.translatedText.Used}} </span>
        <span *ngIf="departmentName=='Fleet'"> {{constants.translatedText.Fleet}} </span>

        <ng-container *ngIf="constants.environment.donutTile_ShowInvoicingTitle; else notSpain">
          {{ constants.translatedText.Invoicing }}
        </ng-container>

        <ng-template #notSpain>

          <span class="visibleAboveSm"> {{constants.translatedText.Performance}} {{constants.translatedText.For}}</span>
          
          <span *ngIf="timePeriod=='Month'"> {{constants.translatedText.TheMonth}} </span>
          <span *ngIf="timePeriod=='Week'"> {{constants.translatedText.TheWeek}} </span>
          <span *ngIf="timePeriod=='Yesterday'"> {{constants.translatedText.Yesterday}} </span>

        </ng-template>

      </h4>

      <div *ngIf="dataSource" class="right-aligned">
        <div *ngIf="showWarning" class="warning"></div>
        <div class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
      </div>

    </div>
  </div>


  <div id="innerContentHolder">
    <div id="chartHolder">
      <h1 (click)="navigateToOrderBook()" id="actualUnits">{{data?.ActualUnits|cph:'number':0}}</h1>
      <h3 id="budgetUnits">/{{data.TargetUnits|cph:'number':0}}</h3>
      <canvas id="chartCanvas" #myChart></canvas>
    </div>

    <div id="bars">
      <div id="labels">
        <div class="amountActual clickable" (click)="navigateToOrderBook()">
          {{data.ActualMargin/1000|cph:'currency':0}}k</div>
        <div class="amountBudget">{{data.TargetMargin/1000|cph:'currency':0}}k</div>
      </div>
      <div id="barRange">
        <div id="actualBar" (click)="navigateToOrderBook()" class="bar" [ngStyle]="{'width.%':actualWidth}"></div>
        <div id="budgetBar" class="bar" [ngStyle]="{'width.%':budgetWidth}"></div>
      </div>
    </div>

    <!-- Spain additional measures - Absolutely positioned in corners -->
    <ng-container *ngIf="constants.environment.donutTile_SpainAdditionalMeasures">
      <!-- Top left -->
      <div class="donut-extra-measure top-left">
        {{ constants.translatedText.vsTarget }}
        <br>
        {{ (data.ActualUnits / data.TargetUnits) | cph:'percent':0:true }}
      </div>

      <!-- Bottom left -->
      <div class="donut-extra-measure bottom-left">
        {{ constants.translatedText.vsLastMonth }}
        <br>
        {{ (data.ActualUnits / data.ActualUnitsLastMonth) | cph:'percent':0:true }}
      </div>

      <!-- Bottom right -->
      <div class="donut-extra-measure bottom-right">
        {{ constants.translatedText.vsLastYear }}
        <br>
        {{ (data.ActualUnits / data.ActualUnitsLastYear) | cph:'percent':0:true }}
      </div>

      <div *ngIf="constants.environment.donutTile_ShowLastYearUnits" class="lastYearUnits">
        {{ constants.translatedText.LastYear }}: {{ data.ActualUnitsLastYear }} {{ constants.translatedText.Units }}
      </div>
    </ng-container>
  </div>
</div>