
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CphPipe } from 'src/app/cph.pipe';
import { Month, OrderbookDateType, OrderbookTimePeriod } from 'src/app/model/main.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { OrderBookService } from '../orderBook/orderBook.service';

import { SalesmanEfficiencyTableComponent } from './salesmanEfficiencyTable/salesmanEfficiencyTable.component';
import { SalesmanEfficiencyRow } from './SalesmanEfficiencyRow';
import { SalesmanEfficiencyParams } from './SalesmanEfficiencyParams';
import { TopBottomHighlightRule } from 'src/app/model/TopBottomHighlightRule';





@Injectable({
  providedIn: 'root'
})
export class SalesmanEfficiencyService {
 

  chosenRow: SalesmanEfficiencyRow;
  chosenVehicleTypeTypes: string[];
  chosenOrderTypeTypes: string[];
  chosenFranchises: string[];
  chosenMonthStartDate: Date;
  chosenMonthStartDateLabel: string;
  siteTableRows: SalesmanEfficiencyRow[]
  peopleTableRows: SalesmanEfficiencyRow[]
  months: Month[];
  peopleGrid:SalesmanEfficiencyTableComponent;
  siteGrid:SalesmanEfficiencyTableComponent;
  regionsGrid:SalesmanEfficiencyTableComponent;
  topBottomHighlights:TopBottomHighlightRule[]=[]

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    private getDataService: GetDataMethodsService,
    private router: Router,
    public orderBookService: OrderBookService
  ) {


  }

 

  getData() {
    this.selections.triggerSpinner.emit({show:true,message:this.constants.translatedText.Loading})
    let parms: SalesmanEfficiencyParams = this.getParams();
    this.getDataService.getSalesmanEfficiencyRows(parms).subscribe((res: SalesmanEfficiencyRow[]) => {
      res.map(x=>{
        x.totalDealCount = x.DealCountNew + x.DealCountUsed
      })
      this.selections.triggerSpinner.emit({show:false})
      if(this.chosenRow){
        this.updatePeopleGrid(res)
      }else{
        
        this.updateSiteGrid(res)
      }
    })
  }


  updateSiteGrid(data: SalesmanEfficiencyRow[]) {
    this.siteTableRows = data;
    if(!!this.siteGrid){
      this.siteGrid.updateGrid(data)
      this.regionsGrid.updateGrid(data)
    }
  }

  updatePeopleGrid(data: SalesmanEfficiencyRow[]) {
    this.peopleTableRows = data;
    if(!!this.peopleGrid){
      this.peopleGrid.updateGrid(data)
    }
  }




  getParams(): SalesmanEfficiencyParams {
    let monthLabel = this.chosenMonthStartDate.toISOString();

    if(this.chosenMonthStartDateLabel == 'Year To Date'){ monthLabel = 'YTD'}
    if(this.chosenMonthStartDateLabel == 'Previous Year'){ monthLabel = 'PreviousYear'}

    let result: SalesmanEfficiencyParams = {
      ChosenLabel: this.chosenRow ? this.chosenRow.Label : null,
      IsSite: this.chosenRow ? this.chosenRow.IsSite : false,
      IsRegion: this.chosenRow ? this.chosenRow.IsRegion : false,
      Month: monthLabel,
      VehicleTypeTypes: this.chosenVehicleTypeTypes,
      OrderTypeTypes: this.chosenOrderTypeTypes,
      Franchises: this.chosenFranchises
    }

    return result;
  }

  onClickedSite(row: SalesmanEfficiencyRow) {
    this.chosenRow = row;
    this.getData();
  }
  
  onClickedPerson(row: SalesmanEfficiencyRow) { 
    let startDate = this.chosenMonthStartDate;
    let endDate = new Date(startDate.getFullYear(), startDate.getMonth()+1,0,23,59,59);

    if(this.chosenMonthStartDateLabel=='YTD'){
    startDate = new Date(this.constants.appStartTime.getFullYear(),0,1);
    endDate = this.constants.thisMonthEnd
    }

    this.orderBookService.initOrderbook();

    this.orderBookService.vehicleTypeTypes = this.chosenVehicleTypeTypes;
    this.orderBookService.orderTypeTypes = this.chosenOrderTypeTypes;
    this.orderBookService.franchises = this.chosenFranchises;

    this.orderBookService.orderDate.timePeriod = OrderbookTimePeriod.Day,

    this.orderBookService.accountingDate.startDate = this.chosenMonthStartDate;
    this.orderBookService.accountingDate.endDate = this.constants.endOfMonth(this.chosenMonthStartDate);
    this.orderBookService.accountingDate.timePeriod = OrderbookTimePeriod.Anytime;

    this.orderBookService.salesExecId = row.PersonId;
    this.orderBookService.salesExecName = row.Label;

    this.orderBookService.showOrderbook();
  }
 










}




