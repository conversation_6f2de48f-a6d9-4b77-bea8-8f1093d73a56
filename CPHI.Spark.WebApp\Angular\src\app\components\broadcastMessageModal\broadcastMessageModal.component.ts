import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SelectionsService } from 'src/app/services/selections.service';
import { ConstantsService } from 'src/app/services/constants.service';

import '../../../assets/priceBoardFonts/Arial-normal.js';
import '../../../assets/priceBoardFonts/Arial-bold.js';
import { CphPipe } from 'src/app/cph.pipe';
import { BroadcastMessage } from 'src/app/model/BroadcastMessage';
import { CreateBroadcastMessageViewParams } from 'src/app/model/CreateBroadcastMessageViewParams';
import { ApiAccessService } from 'src/app/services/apiAccess.service';


@Component({
  selector: 'broadcastMessageModal',
  templateUrl: './broadcastMessageModal.component.html',
  styleUrls: ['./broadcastMessageModal.component.scss']
})

export class BroadcastMessageModalComponent implements OnInit {

  closeOrNext: string;
  messageToDisplay: BroadcastMessage;
  messageIndex: number = 0;
  numberOfMessages: number;
  doNotShowAgain:boolean=false;

  constructor(
    public activeModal: NgbActiveModal,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public apiAccess: ApiAccessService,
    public cphPipe: CphPipe
  ) {

  }

  ngOnInit(): void {
    this.numberOfMessages = this.constants.broadcastMessages.length;
    this.messageToDisplay = this.constants.broadcastMessages[this.messageIndex];
    this.doNotShowAgain = false;
    //this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading + '...' });
  }

  ngOnDestroy(): void {
    this.messageToDisplay = null;
  }

  private closeAndDoNotShowAgain() {

    const params: CreateBroadcastMessageViewParams = {
      BroadcastMessageId: this.messageToDisplay.Id
    };

    this.apiAccess.post('api/BroadcastMessage', 'CreateBroadcastMessageView', params).subscribe((res) => {

    }, (error: any) => {
      // console.error('Failed to save broadcast message', error);
      // this.selections.triggerSpinner.next({ show: false });
    });

    // Last message to view - then close
    this.displayNextMessageIfExists();
    
  }

  // Close the message but will display again
  public closeMessage() {

    if(this.doNotShowAgain){
      this.closeAndDoNotShowAgain();
      this.doNotShowAgain = false;
    }else{
      this.displayNextMessageIfExists();
    }


  }

  displayNextMessageIfExists()
  {
    // Last message to view - then close
    if(this.numberOfMessages-1 == this.messageIndex)
    {
      this.activeModal.close();
    }
    // Otherwise move to next message
    else
    {
      this.messageIndex += 1;
      this.messageToDisplay = this.constants.broadcastMessages[this.messageIndex];
    }
  }

  toggleDoNoShowAgain(){
    this.doNotShowAgain = !this.doNotShowAgain;
  }


}
