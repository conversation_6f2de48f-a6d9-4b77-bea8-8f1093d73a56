//Navbar
nav.navbar {
    height: 35px;
    padding: 0;
    position: fixed;
    z-index: 4;
    color: white;
    background: var(--mainAppColourVeryDark);
    padding-right: 20em;
    padding-left: 40px;
    top: 0;
    right: 0;
    left: 0;

    @media (max-width: 1680px) {
        padding-left: 35px;
        height: 30px;
    }
    @media (max-width: 1280px) {
        padding-left: 30px;
        height: 25px;
    }

    .btn {
        
        margin: 0em 0.5em;
        height: 2em;
        padding: 0em 0.9em;

        i {
            margin: 0em 1em;
        }
    }

    .buttonGroup .btn {
        margin: 0em 0em;
    }

    .generic {
        display: flex;
        align-items: center;

        #pageTitle {
            display: flex;
            min-width: 15em;
            // justify-content: space-between;
            
            align-items: center;
        }

    }

    .pageSpecific {
        left: 22em;
        display: flex;
        align-items: center;
        margin-right: 16em;

        div,
        form {
            display: flex;
            align-items: center;

            input {
                height: 1.9em;
            }
        }

    }

    .dropdown-menu .btn {
    }

    input {
        padding-left: 3em;
    }
    label {
        padding-right: 0.5em;
    }

    .fa-search {
        position: absolute;
        transform: translateX(1.3em);
        color: grey;
        z-index: 10;
    }
}

nav.navbar.main {
    z-index: 5;
}

.fullSizeCard .navbar {
    z-index: 5;
}

.buttonGroup {
    margin-bottom: 0 !important;
}

.fixSideMenu .navbar {
    margin-left: 220px;
    padding-left: 0;

    #pageTitle {
        margin-left: 2em;
    }

    @media (max-width: 1680px) {
        margin-left: 190px;
    }
    @media (max-width: 1280px) {
        margin-left: 170px;
    }
}

.navbar-grid .btn {
   
        margin: 0em 0.5em;
        height: 2em;
        padding: 0em 0.9em;

        i {
            margin: 0em 1em;
        }
}