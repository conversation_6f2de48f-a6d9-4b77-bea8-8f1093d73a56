<nav class="navbar">
 <nav  class="generic">
    <h4 id="pageTitle">
      <div>
        {{ constants.translatedText.Dashboard_PartsSales_Title }}
        <sourceDataUpdate [chosenDate]="constants.LastUpdatedDates.FinancialLines"></sourceDataUpdate>
        <span *ngIf="selections.partsSummary.chosenSite">: {{ selections.partsSummary.chosenSite }}</span>
      </div>
    </h4>

    <ng-container *ngIf="selections.partsSummary">

      <!-- Month selector -->
      <div class="buttonGroup">
        <!-- Previous -->
        <button class="btn btn-primary" (click)="changeMonth(selections.partsSummary.month, -1)">
          <i class="fas fa-caret-left"></i>
        </button>

        <!-- Dropdown -->
        <div class="d-inline-block" [autoClose]="true" ngbDropdown>
          <button class="btn btn-primary centreButton" ngbDropdownToggle (click)="makeMonths(0)">
            {{ selections.partsSummary.month.name | titlecase }}
          </button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <!-- the ngFor buttons -->
            <button *ngFor="let month of months" ngbDropdownItem (click)="selectMonth(selections.partsSummary.month, month.startDate)">
              {{ month.startDate | cph:'month':0 | titlecase }}
            </button>
          </div>
        </div>

        <!-- Next -->
        <button class="btn btn-primary" (click)="changeMonth(selections.partsSummary.month, 1)">
          <i class="fas fa-caret-right"></i>
        </button>
      </div>

      <ng-container *ngIf="!selections.partsSummary.chosenSite">

        <!-- Table type selector -->
        <div *ngIf="constants.environment.partsSummary.showTableTypeSelector" class="buttonGroup">
          <button *ngFor="let tableType of selections.partsSummary.tableTypes" class="btn btn-primary"
            [ngClass]="{ 'active': tableType == selections.partsSummary.tableType }"
            (click)="chooseTableType(tableType)">
            {{ tableType }}
          </button>
        </div>

        <!-- Time selector -->
        <div class="buttonGroup" *ngIf="!selections.partsSummary.showDailyView">
          <button
            *ngFor="let timeOption of selections.partsSummary.timeOptions"
            class="btn btn-primary"
            [ngClass]="{ 'active': selections.partsSummary.timeOption == timeOption }"
            (click)="selectTimeOption(timeOption)"
          >
            {{ timeOption }}
          </button>
        </div>

        <!-- Channel selector -->
        <!-- [disabled]="disabled"  -->
        <div class="d-inline-block" ngbDropdown dropright>
          <button
            class="btn btn-primary"
            [ngClass]="{ 'active': !showAllChannels }"
           
            ngbDropdownToggle
            (click)="generateChannelList()"
          >
            {{ channelChosenLabel() }}
          </button>
            
          <div ngbDropdownMenu> 
            <button
              *ngFor="let channel of channelsDropdownList"
              [ngClass]="{ 'active': channel.isSelected }"
              ngbDropdownItem
              (click)="toggleItem(false, channel)"
            >
              {{ channel.label }}
            </button>
    
            <button class="quickSelect" ngbDropdownItem (click)="toggleItem(true)">{{ constants.translatedText.Common_Total }}</button>
            <div class="spaceBetween">
              <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle (click)="selectChannels()">OK</button>
              <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{ constants.translatedText.Common_Cancel }}</button>
            </div>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </nav>

  <nav  class="pageSpecific"></nav>
</nav>

<!-- Main Page -->
<div *ngIf="selections.partsSummary" class="content-new">
  <div class="content-inner-new">
    <!-- Sites overview (Tables) -->
    <div *ngIf="!selections.partsSummary.chosenSite && service.partsSalesSitesRows" id="tableHolder">
      <partsTable (clickedSite)="selectSite($event)"></partsTable>
      <div class="tableSpacer"></div>
      <partsTable [isRegional]="true" (clickedSite)="selectSite($event)"></partsTable>
    </div>
    <!-- Site overview (Tiles) -->
    <div *ngIf="selections.partsSummary.chosenSite && selections.partsSummary" class="dashboard-grid-container">
      <!-- Floating back button -->
      <button class="btn btn-primary back-to-sites" (click)="selections.partsSummary.chosenSite = null">
        <i class="fas fa-undo"></i>
      </button>
      <!-- The tiles -->
      <div class="dashboard-grid">
        <div class="dashboard-tile grid-col-1-1 grid-row-1-6">
          <salesPositionTile *ngIf="service.partsVsTarget" [page]="'parts'"></salesPositionTile>
        </div>
        <div class="dashboard-tile grid-col-2-2 grid-row-1-9">
          <salesByDayTile *ngIf="service.partsDailyDoneVsTarget" [page]="'parts'"></salesByDayTile>
        </div>
        <div class="dashboard-tile grid-col-3-5 grid-row-1-5">
          <partsSalesByTypeTile *ngIf="service.partsChannelSplits"></partsSalesByTypeTile>
        </div>
        <div class="dashboard-tile grid-col-1-1 grid-row-6-9">
          <wipAgeingSummaryTile *ngIf="service.wipSummary" [page]="'parts'"></wipAgeingSummaryTile>
        </div>
        <div class="dashboard-tile grid-col-3-3 grid-row-5-9">
          <runChaseTile *ngIf="service.runChaseDataPoints" [page]="'parts'"></runChaseTile>
        </div>
        <div class="dashboard-tile grid-col-4-4 grid-row-5-9">
          <runRateAndRequirementTile *ngIf="service.partsRunRate" [page]="'parts'"></runRateAndRequirementTile>
        </div>
      </div>
    </div>
  </div>
</div>