import { Compo<PERSON>, ElementRef, <PERSON>E<PERSON>ter, OnInit, ViewChild } from '@angular/core';
import { ColumnApi, GetContextMenuItemsParams, GridApi, GridOptions, ICellRendererParams, IRowNode, MenuItemDef, ValueGetterParams } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { CustomHeaderService } from 'src/app/components/customHeader/customHeader.service';
import { CphPipe } from 'src/app/cph.pipe';
import { AppUserRole } from 'src/app/model/AppUserRole';
import { ClaimTypeAndValues } from 'src/app/model/ClaimTypeAndValues';
import { UserAndLogin } from 'src/app/model/UserAndLogin';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { SiteVM } from '../../model/main.model';
import { AutotraderService } from '../../services/autotrader.service';
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';
import { CreateBroadcastMessageParams } from 'src/app/model/CreateBroadcastMessageParams';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UserModalComponent } from './userModal/userModal.component';
import { UserSetupService } from './userSetup.service';


@Component({
  selector: 'app-userSetup',
  templateUrl: './userSetup.component.html',
  styleUrls: ['./userSetup.component.scss']
})
export class UserSetupComponent implements OnInit {

  @ViewChild('newBroadcastModal', { static: true }) newBroadcastModal: ElementRef;
  @ViewChild('tableContainer', { static: true }) tableContainer: ElementRef;


  gridOptions: GridOptions;
  disableSaveButton: boolean;
  showGrid: boolean
  subscription: Subscription;
  newBroadcastMessage: string;
  untilDate: string;
  isSysAdmin: boolean;

  cancelPriceUpdateEmitter: EventEmitter<boolean>;

  public components: {
    [p: string]: any;
  } = {
      agColumnHeader: CustomHeaderNew,
    };

  constructor(
    public constants: ConstantsService,
    public modalService: NgbModal,
    public columnTypeService: ColumnTypesService,
    public selections: SelectionsService,
    public analysis: AutotraderService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public apiAccess: ApiAccessService,
    public getDataMethods: GetDataMethodsService,
    public gridHelpers: AGGridMethodsService,
    public customHeader: CustomHeaderService,
    public service: UserSetupService
  ) { }

  ngOnInit() {

    this.disableSaveButton = true;
    this.isSysAdmin = this.selections.user.RoleName == 'System Administrator'

    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading })

    this.initParams()

    this.loadClaims();

    this.loadRoles();

    this.loadSites();

    this.service.pageComponent = this;

    this.service.getData()

  }

  loadSites() {

    //unsure why we're reloading the sites, already have them in constants from appstart
    this.getDataMethods.GetAllSites().subscribe((res: SiteVM[]) => {
      this.constants.Sites = res;
    })

  }


  loadRoles() {

    this.apiAccess.get('api/User', 'Roles').subscribe((roles: AppUserRole[]) => {

      this.constants.Roles = roles;

      this.setRoleTranslations();

    });

  }

  loadClaims() {
    this.apiAccess.get('api/User', 'GetAllClaims').subscribe((claims: ClaimTypeAndValues[]) => {
      this.constants.ClaimTypes = claims;
    });

  }



  ngOnDestroy() {
    if (this.subscription){ this.subscription.unsubscribe()}
    if (this.service.pageComponent) { this.service.pageComponent = null; }
  }

  setRoleTranslations() {
    this.constants.Roles.forEach(x => {

      if (x.Name == 'System Administrator' && this.constants.environment.userSetup_showSpanishJobTitles) {
        x.Translation = "Administrador de Usuarios"
      }
      else {
        x.Translation = x.Name;
      }

    });


  }


  initParams() {
    this.service.rowData = []

  }

  checkboxCellRenderer(params: any) {

    if (params.value !== 'true' && params.value !== 'false') {

      params.setValue(params.value === true || params.value === 'true' ? 'true' : 'false');

    } else {

      var input = document.createElement("input");

      input.type = "checkbox";
      input.value = params.value === true || params.value === 'true' ? 'true' : 'false';
      input.checked = params.value === true || params.value === 'true' ? true : false;

      input.disabled = true;

      return input;
    }

  }

  initGridParams() {
    let baseHeight = window.innerWidth > 1550 ? 4 : 0
    let gridScaleValue = this.tableContainer.nativeElement.clientWidth / 1300;
    this.gridOptions = {
      getContextMenuItems: (params) => this.getContextMenuItems(params),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      getLocaleText: (params) => this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      suppressColumnMoveAnimation: true,
      getRowHeight: (params) => {
        return 80;
      },
      getRowId: (params) => params.data.PersonId.toString(),

      headerHeight: 50,
      rowData: this.service.rowData,
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),

      },

      statusBar: {
        statusPanels: [
          { statusPanel: 'agAggregationComponent', align: 'right' }
        ]
      },
      context: { thisComponent: this },
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        floatingFilter: true,
        filterParams: { applyButton: false, clearButton: true, applyMiniFilterWhileTyping: true, cellHeight: this.gridHelpers.getFilterListItemHeight() },
        cellClass: 'agAlignCentreVertically',
        headerComponentParams: { showPinAndRemoveOptions: false },
        autoHeaderHeight: true
      },

      enableRangeSelection: true,
      pivotColumnGroupTotals: "after",
      pivotMode: false,
      onRowGroupOpened: (params) => this.onRowGroupOpened(params),
      suppressAggFuncInHeader: true,//hides the extra header row saying (Sum..)
      onColumnGroupOpened: () => this.sizeGrid(),
      rowClassRules: { 'changed': (params) => params.data.hasChanged },
      onCellDoubleClicked: (params) => {
        this.modifyUser(params);
      },
      columnDefs: [
        { headerName: 'Id', field: 'PersonId', colId: 'PersonId', width: 40 * gridScaleValue, type: 'label', },
        { headerName: 'Name', field: 'Name', colId: 'Name', width: 90 * gridScaleValue, type: 'label', },

        {
          headerName: 'Username', field: 'UserName', colId: 'UserName', width: 90 * gridScaleValue, type: 'label'
        },
        {
          headerName: 'Role', field: 'RoleName', colId: 'RoleName',
          width: 50 * gridScaleValue, type: 'label'
        },
        { headerName: 'Job Title', field: 'JobTitle', colId: 'JobTitle', width: 90 * gridScaleValue, type: 'label', },
        {
          headerName: 'Login', field: 'CanLogin', colId: 'CanLogin',
          valueGetter: (params) => { return params.data.CanLogin ? 'Yes' : 'No' },
          width: 45 * gridScaleValue
        },
        {
          headerName: 'Email', field: 'Email', colId: 'Email', width: 125 * gridScaleValue, type: 'label',
        },
        {
          headerName: this.constants.translatedText.Sites, field: 'Sites', colId: 'Sites',
          filterValueGetter: (params) => this.sitesFilterGetter(params),
          width: 125 * gridScaleValue,
          valueGetter: (parms) => this.siteNamesRenderer(parms, 'Sites'),
          type: 'label'
        },

        {
          headerName: 'Home Site', field: 'SiteId', colId: 'SiteId',
          filterValueGetter: (params) => this.sitesFilterGetter(params),
          width: 100 * gridScaleValue,
          valueGetter: (parms) => this.siteNamesRenderer(parms, 'SiteId'),
          type: 'label'
        },

        {
          headerName: this.constants.translatedText.SalesExec, field: 'IsSalesExec', colId: 'IsSalesExec',
          width: 35 * gridScaleValue,
          valueGetter: (params) => { return params.data.IsSalesExec ? 'Yes' : 'No' },
          hide: this.constants.environment.userSetup_hideIsSalesExec,
        },

        {
          headerName: 'Upload Reports', field: 'AllowReportUpload', colId: 'AllowReportUpload',
          width: 50 * gridScaleValue,
          valueGetter: (params) => { return params.data.AllowReportUpload ? 'Yes' : 'No' },
          hide: this.constants.environment.userSetup_hideUploadReports,
        },

        {
          headerName: 'Access rep centre', field: 'AccessReportCentre', colId: 'AccessReportCentre',
          width: 50 * gridScaleValue,
          valueGetter: (params) => { return params.data.AccessReportCentre ? 'Yes' : 'No' },
          hide: this.constants.environment.userSetup_hideViewReports,
        },

        {
          headerName: 'Live Forecast',
          field: 'LiveForecast',
          colId: 'LiveForecast',
          width: 125 * gridScaleValue,
          // cellRenderer: LiveForecastCellRendererComponent,
          hide: this.constants.environment.userSetup_hideLiveforecast,
          type: 'label'
        },


        {
          headerName: 'Sales Exec Review',
          field: 'SalesExecReview',
          colId: 'SalesExecReview',
          width: 125 * gridScaleValue,
          //cellRenderer: SalesExecReviewCellRendererComponent,
          hide: this.constants.environment.userSetup_hideSerSubmitter,
          type: 'label'
        },


        {
          headerName: 'Access All Sites ', field: 'AccessAllSites', colId: 'AccessAllSites',
          width: 40 * gridScaleValue,
          valueGetter: (params) => { return params.data.AccessAllSites ? 'Yes' : 'No' },
        },



        {
          headerName: 'Is TMgr', field: 'IsTMgr', colId: 'IsTMgr',
          width: 60 * gridScaleValue,
          cellRenderer: this.checkboxCellRenderer,
          hide: this.constants.environment.userSetup_hideTMgr,
        },


        //All the user's claims
        {
          headerName: 'Permissions', colId: 'permissions',
          width: 350 * gridScaleValue,
          floatingFilter: true,
          type: 'labelWrap',
          valueGetter: (params) => this.permissionsGetter(params)

        },

      ],
      suppressClickEdit: true,


    }



  }
  permissionsGetter(params: ValueGetterParams): string {
    let userAndLogin: UserAndLogin = params.data;

    let result: string[] = [];
    userAndLogin.Claims.forEach(claim => {
      if (claim.ClaimValue !== 'false') {
        if (claim.ClaimValue == 'true') {
          result.push(claim.ClaimType)
        } else {
          result.push(`${claim.ClaimType}: ${claim.ClaimValue}`)
        }
      }
    })

    return result.join(', ');
  }

  getContextMenuItems(params: GetContextMenuItemsParams<any, any>): (string | MenuItemDef)[] {

    let normal = this.gridHelpers.getContextMenuItems(params);

    let row: UserAndLogin = params.node.data;

    if (row.AppUserId) {

      normal.push(
        'separator',
        {
          name: 'Delete User',
          cssClasses: ['redFont'],
          action: () => {
            this.service.maybeDelete(params.node.data);
          }

        },
      )
    }

    return normal;

  }


  initiateAndRefresh() {
    setTimeout(() => {
      if (!this.gridOptions) {
        this.initGridParams()
      } else {
        this.refreshGrid();
      }

      this.showGrid = true

    }, 350)
  }




  siteNamesRenderer(parms: any, field: string): string | HTMLElement {
    const siteIds: any = parms.data[field];   //might be string or number
    return this.renderNamesFromIds(siteIds);
  }

  sitesFilterGetter(parms) {
    const currentValue = parms.data[parms.column.colId];
    return this.renderNamesFromIds(currentValue);
  }


  private renderNamesFromIds(siteIdVal: any) {
    let resultNames: string[] = [];
    try {

      if (siteIdVal === "") { return 'None' }
      if (isNaN(siteIdVal)) {
        siteIdVal.split(',').map(x => resultNames.push(this.constants.Sites.find(s => s.SiteId == parseInt(x)).SiteDescShort));
      } else {
        resultNames.push(this.constants.Sites.find(s => s.SiteId == parseInt(siteIdVal)).SiteDescription);
      }
      return resultNames.join(',');
    }
    catch {
      debugger;
      return 'foo';
    }
  }

  modifyUser(params) {
    if (params.colDef.colId === 'Delete') { return; }  //early return if delete

    let row: UserAndLogin = params.data;
    let user = new UserAndLogin(row);


    if (row.AppUserId) {
      // Only update if login has already been created
      this.openModal(user);

    } else {
      this.createLoginForUser(user);
    }
  }





  refreshGrid() {
    if (this.service.gridApi) this.service.gridApi.setRowData(this.service.rowData);
    if (this.service.gridApi) this.service.gridApi.refreshCells();
  }



  excelExport(): void {
    //get tableModel from ag-grid
    let tableModel = this.service.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Users', 1, 1);
  }



  sizeGrid() {
    if (this.service.gridApi) {
      //this.grid.autoSizeAll(this.service.gridApi, this.gridColumnApi, 220, true);
    }
  }




  onGridReady(params) {
    this.service.gridApi = params.api;
    this.service.gridColumnApi = params.columnApi;
    this.service.gridApi.sizeColumnsToFit();
    this.selections.triggerSpinner.next({ show: false });
    


  }





  onRowGroupOpened(params) {
    this.service.gridApi.sizeColumnsToFit();
  }


  cellStyler(params) {
    let colour: string = 'red';
    if (params.value > 0) colour = 'red';
    if (params.value < 0) colour = 'green';

    let fontWeight = params.node.level < 1 ? 700 : 400
    let result: any = { color: colour, 'font-weight': fontWeight }
    return result;

  }

  addUser() {
    let newUser: UserAndLogin = new UserAndLogin();
    this.openModal(newUser);
  }

  addBroadcastMessage(): void {

    //this.selections.triggerSpinner.next({ message:'Loading...', show: true });

    this.newBroadcastMessage = null;
    this.untilDate = null;

    this.modalService.open(this.newBroadcastModal, { windowClass: 'newBroadcastModal', keyboard: false, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //ok'd

    },
      //closed
      (reason) => {
        //cancelled, so no passback    
        this.modalService.dismissAll();

      });

  }

  saveNewBroadcastMessage(): void {

    let now: Date = new Date();
    let untilDate: Date = this.stringToDate(this.untilDate);

    const params: CreateBroadcastMessageParams = {
      StartDate: now,
      EndDate: untilDate,
      Message: this.newBroadcastMessage
    };

    this.apiAccess.post('api/BroadcastMessage', 'CreateBroadcastMessage', params).subscribe((res) => {

      this.selections.triggerSpinner.emit({ show: false });
      this.modalService.dismissAll();
      this.constants.toastSuccess('Saved new broadcast message');

    }, (error: any) => {

      console.error('Failed to save broadcast message', error);
      this.modalService.dismissAll();
      this.selections.triggerSpinner.next({ show: false });
    });


  }

  checkEnableSaveButton(): void {
    if (this.newBroadcastMessage == null) {
      this.disableSaveButton = true;
    }
    else if (this.newBroadcastMessage == 'Start typing here...') {
      this.disableSaveButton = true;
    }
    else if (this.untilDate == null) {
      this.disableSaveButton = true;
    }
    else {
      this.disableSaveButton = false;
    }
  }

  maybeClearPlaceholder(event: any): void {
    if (event.target.innerHTML.includes('Start typing here...')) event.target.innerHTML = null;
  }

  updateMessage(event: any) {
    this.newBroadcastMessage = event.target.innerText.replace(/\n/g, '<br>');
    this.checkEnableSaveButton();
  }

  // createLoginForUser(user: any) {
  //   this.constants.addUserModal.initiateNewUser();


  createLoginForUser(user: UserAndLogin) {

    let newUser: UserAndLogin = new UserAndLogin();
    newUser.PersonId = user.PersonId;
    newUser.Name = user.Name;
    user.Email = user.Email;
    user.JobTitle = user.JobTitle;
    user.SiteId = user.SiteId
    user.Sites = user.Sites

    this.openModal(newUser);


  }

  setDate(event: any): void {
    this.untilDate = event.target.value;
    this.checkEnableSaveButton();
  }

  stringToDate(dateString: string): Date {
    // Parse the date string into a Date object
    const date = new Date(dateString);

    // Return the Date object
    return date;
  }


  private openModal(newUser: UserAndLogin) {
    const modalRef = this.service.modalService.open(UserModalComponent);
    modalRef.componentInstance.initialiseModal(newUser);


    modalRef.result.then(res => {
      //modal is closed
      //alert('modal closed');
      this.service.gridApi.refreshCells();
    }, () => {
      //chose to cancel
    });
  }
}
