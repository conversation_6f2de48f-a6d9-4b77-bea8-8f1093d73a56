﻿using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels
{
    public class DashboardDataPackSpainOverview
    {
        #nullable enable
        public OrdersDonutSpainData? OrdersDonutSpainNew { get; set; }
        public OrdersDonutSpainData? OrdersDonutSpainUsed { get; set; }
        public DonutMonthlyData? InvoicedDonutDataNew { get; set; }
        public DonutMonthlyData? InvoicedDonutDataUsed { get; set; }
        public InvoicingPerformance? InvoicingPerformanceNew { get; set; }
        public InvoicingPerformance? InvoicingPerformanceUsed { get; set; }
        public UnitsAndValue? ScrapVehicles { get; set; }
        public UnitsAndValue? FixedAssetVehicles { get; set; }
        public IEnumerable<AssignmentVM>? AssigmentVehicles { get; set; }
        public ReconditioningSummary? ReconditioningSummary { get; set; }


        //public IEnumerable<DonutMonthlyData>? DonutDataSetsMonth { get; set; }
        public IEnumerable<OverageStockSummaryItem>? OverageStockSummary { get; set; }

        public IEnumerable<SpainDailyNetOrderItem>? SpainDailyNetOrdersUsed { get; set; }
        public IEnumerable<SpainDailyNetOrderItem>? SpainDailyNetOrdersNew { get; set; }

        public CommissionTileSummary? Commissions { get; set; }
        public AlcopasSummary? Alcopas { get; set; }
        public IEnumerable<DataOriginsUpdate>? DataOrigins { get; set; }
}
}
