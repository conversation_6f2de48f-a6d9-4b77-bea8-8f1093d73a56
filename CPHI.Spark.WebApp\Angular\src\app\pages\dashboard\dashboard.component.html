<nav class="navbar main">

  <nav id="dashboard-nav" class="generic">

    <h4 id="pageTitle">
      <div>

        <h4 *ngIf="constants.translatedText && (constants.chosenPage?.isDashboard || !constants.chosenPage)" onError="Dashboard">
          {{ constants.translatedText.Dashboard_Title }}
        </h4>

      </div>
    </h4>


    <div class="button-group-container">

      <!-- Reports selector -->
      <!-- Operational -->


      <!-- Normal menu no extra pickers used by non-Spain -->
      <ng-container *ngIf="service.chosenPage?.pageName != 'spainKPIs' && (constants.chosenPage?.isDashboard || !constants.chosenPage)">
        <div class="buttonGroup">
          <sitePickerRRG *ngIf="constants.sitesActive && (constants.environment.customer == 'RRGUK')" id="sitePicker"
            [allSites]="constants.sitesActive" 
            [disabled]="!['dashboardAfterSalesRRG','dashboardSalesRRG','dashboardSiteCompare'].includes(service.chosenPage?.pageName)" 
            [sitesFromParent]="service.chosenSites"
            (updateSites)="onUpdateSites($event)">
          </sitePickerRRG>

          <sitePicker *ngIf="constants.sitesActive && (constants.environment.customer == 'Vindis' || constants.environment.customer == 'Jardine')" id="sitePicker"
            [allSites]="constants.sitesActive"  [sitesFromParent]="service.chosenSites"
            (updateSites)="onUpdateSites($event)">
          </sitePicker>
        </div>
      </ng-container>


      <!-- Menu with extra pickers, used by Spain -->
      <ng-container *ngIf="!!constants.environment.dashboard.includeExtraSpainMenuButtons && (constants.chosenPage?.isDashboard || !constants.chosenPage)">

        <!-- For picking sites -->
        <div class="buttonGroup">
          <sitePickerRRG id="sitePicker" [allSites]="constants.sitesActive" [disabled]="(!service.chosenSection || !service.chosenSection.enableSitesSelector) 
          && service.chosenPage?.pageName != 'dashboardAfterSalesRRG'
          && service.chosenPage?.pageName != 'dashboardSalesRRG'" [sitesFromParent]="service.chosenSites"
            (updateSites)="onUpdateSites($event)">
          </sitePickerRRG>
        </div>
      </ng-container>




      <!-- Main Pages -->
      <!-- <div class="buttonGroup">
        <ng-container *ngFor="let section of sections">
          <button class="btn btn-primary ensureFirstButtonShowsButtonGroupCentre" [id]="section.pageName + 'DashButton'"
           [ngClass]="{'active':service.chosenPage?.pageName === section.pageName}"
            (click)="chooseSection(section)">
            {{section.translatedTextValue}}
          </button>
        </ng-container>
      </div> -->
    </div>


    <!-- <span id="reportsLabel" *ngIf="!service.hideReportButtons" class="d-flex align-items-center">{{reportsLabel()}} </span> -->

    <!-- Report sub pages -->
    <!-- <div *ngIf="!service.showMenuItemsAsDropdown" class="buttonGroup">
      
      <ng-container *ngIf="service.chosenSection && !service.hideReportButtons">
        <button *ngFor="let page of service.chosenSection.pages" class="btn btn-primary subButton"
          [id]="page.pageName + 'DashButton'"
          [ngClass]="{'active':!!service.chosenPage && page.pageName === service.chosenPage?.pageName}"
          (click)="choosePage(page)">{{page.translatedTextValue}}</button>
      </ng-container>
    </div>
    
    <div *ngIf="service.showMenuItemsAsDropdown" class="buttonGroup">
      
      <ng-container *ngIf="service.chosenSection && !service.hideReportButtons">
        <div ngbDropdown dropright class="d-inline-block" id="siteDropdown">

          <button class="btn btn-primary" ngbDropdownToggle>
            {{ reportsDropdownLabel() }}
          </button>
            
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button
              *ngFor="let page of service.chosenSection.pages"
              [id]="page.pageName + 'DashButton'"
              [ngClass]="{ 'active': !!service.chosenPage && page.pageName === service.chosenPage?.pageName }"
              ngbDropdownItem
              (click)="choosePage(page)">
              {{ page.translatedTextValue }}
            </button>
          </div>
        </div>
      </ng-container>
    </div> -->

  </nav>

  <nav class="pageSpecific">
  

  </nav>

  <!-- Data origins bit -->
  <!-- Hiding for now except us -->
  <button *ngIf="constants.environment.customer == 'RRGSpain'" id="dataOriginsUpdatesModalButton"
    class="btn btn-primary" (click)="openDataOriginsUpdatesModal()">
    {{ constants.translatedText.Dashboard_DataOriginsUpdates }}
  </button> 

</nav>

<!-- Main Page -->
<div [ngClass]="[constants.environment.customer]"
  id="overallHolder">
  <div tabindex="0" class="fullSizeCard">

    <!-- <div class="test-container">
      <autoTraderPerformanceRating></autoTraderPerformanceRating>
    </div> -->

    <!-- Overview ones -->
    <dashboardOverviewVindis *ngIf="service.chosenPage?.pageName === 'dashboardOverview'"> </dashboardOverviewVindis>
    <dashboardOverviewSpain *ngIf="service.chosenPage?.pageName === 'dashboardOverviewSpain'"> </dashboardOverviewSpain>


    <!-- Sales -->
    <dashboardSalesRRG *ngIf="service.chosenPage?.pageName === 'dashboardSalesRRG'"> </dashboardSalesRRG>
    <dashboardSalesVindis *ngIf="service.chosenPage?.pageName === 'dashboardSalesVindis'"> </dashboardSalesVindis>
    <dashboardInvoicedDealsSpain *ngIf="service.chosenPage?.pageName === 'dashboardInvoicedDealsSpain'" [department]="'New'"></dashboardInvoicedDealsSpain>
    <dashboardNewVNSpain *ngIf="service.chosenPage?.pageName === 'dashboardNewVNSpain'"></dashboardNewVNSpain>
    <dashboardUsedSpain *ngIf="service.chosenPage?.pageName === 'dashboardUsedSpain'"></dashboardUsedSpain>
    <dashboardPricing *ngIf="service.chosenPage?.pageName === 'dashboardPricing'"> </dashboardPricing>
    <ratioOrdersBySiteSitesTable *ngIf="service.chosenPage?.pageName === 'OrderRate'"> </ratioOrdersBySiteSitesTable>
    <app-salesPerformance *ngIf="service.chosenPage?.pageName === 'SalesPerformance'"></app-salesPerformance>
    <app-alcopaSummary *ngIf="service.chosenPage?.pageName === 'Alcopa'"></app-alcopaSummary>

    <app-registrationsPosition *ngIf="service.chosenPage?.pageName === 'Registrations'"></app-registrationsPosition>
    <app-salesmanEfficiency *ngIf="service.chosenPage?.pageName === 'SalesmanEfficiency'"></app-salesmanEfficiency>
    <app-fAndISummary *ngIf="service.chosenPage?.pageName === 'FinanceAddOns'"></app-fAndISummary>
    <app-gdpr *ngIf="service.chosenPage?.pageName === 'GDPR'"></app-gdpr>
    <app-salesActivity *ngIf="service.chosenPage?.pageName === 'Activities'"></app-salesActivity>
    <app-stockReport *ngIf="service.chosenPage?.pageName === 'StockReport'"></app-stockReport>
    <app-debts *ngIf="service.chosenPage?.pageName === 'Debtors' || service.chosenPage?.pageName === 'DebtorsAftersales'"></app-debts>
    <app-citNow *ngIf="service.chosenPage?.pageName === 'CitNow' || service.chosenPage?.pageName === 'CitNowAftersales'"></app-citNow>
    <app-imageRatios *ngIf="service.chosenPage?.pageName === 'ImageRatios'"></app-imageRatios>
    <app-voc *ngIf="service.chosenPage?.pageName === 'Voc'"></app-voc>

    <!-- Pricing -->
    <app-stockInsight *ngIf="service.chosenPage?.pageName === 'stockDashboard'"></app-stockInsight>
    <app-siteDetailDashboard *ngIf="service.chosenPage?.pageName === 'siteReports'"></app-siteDetailDashboard>
    <app-leavingVehicles *ngIf="service.chosenPage?.pageName === 'soldDashboard'"></app-leavingVehicles>
    <app-strategyBuilder *ngIf="service.chosenPage?.pageName === 'strategyBuilder'"></app-strategyBuilder>
    <app-applyStrategy *ngIf="service.chosenPage?.pageName === 'applyStrategy'"></app-applyStrategy>
    <app-locationOptimiser *ngIf="service.chosenPage?.pageName === 'locationOptimiser'"></app-locationOptimiser>
    <app-vehicleValuation *ngIf="service.chosenPage?.pageName === 'vehicleValuation'"></app-vehicleValuation>
    <app-localBargains *ngIf="service.chosenPage?.pageName === 'localBargains'"></app-localBargains>


    <!-- Aftersales -->
    <dashboardAfterSalesVindis *ngIf="service.chosenPage?.pageName === 'dashboardAfterSalesVindis'">
    </dashboardAfterSalesVindis>
    
    <dashboardAftersalesSpain *ngIf="service.chosenPage?.pageName === 'dashboardAftersales'">
    </dashboardAftersalesSpain>

    <dashboardAftersalesSpainKPI *ngIf="service.chosenPage?.pageName === 'AftersalesKPIs'">
    </dashboardAftersalesSpainKPI>

    <dashboardAftersalesSpainDetail *ngIf="service.chosenPage?.pageName === 'AftersalesDetail'">
    </dashboardAftersalesSpainDetail>

    <dashboardAftersalesSpainDatasets *ngIf="service.chosenPage?.pageName === 'AftersalesDatasets'">
    </dashboardAftersalesSpainDatasets>

    <dashboardAfterSalesRRG *ngIf="service.chosenPage?.pageName === 'dashboardAfterSalesRRG'"> </dashboardAfterSalesRRG>
    <app-serviceSummary *ngIf="service.chosenPage?.pageName === 'ServiceSales'"></app-serviceSummary>
    <app-partsSummary *ngIf="service.chosenPage?.pageName === 'PartsSales'"></app-partsSummary>
    <app-serviceBookings *ngIf="service.chosenPage?.pageName === 'ServiceBookings'"></app-serviceBookings>
    <app-partsStock *ngIf="service.chosenPage?.pageName === 'PartsStock'"></app-partsStock>
    <app-evhc *ngIf="service.chosenPage?.pageName === 'EVHC'"></app-evhc>
    <!-- <app-citNow *ngIf="service.chosenPage?.pageName === 'CitNow'"></app-citNow> -->
    <!-- <app-voc *ngIf="service.chosenPage?.pageName === 'Voc'"></app-voc> -->
    <app-wipReport *ngIf="service.chosenPage?.pageName === 'Wip'"></app-wipReport>
    <app-upsells *ngIf="service.chosenPage?.pageName === 'Upsells'"></app-upsells>

    <!-- Site Compare -->
    <dashboardSiteCompare *ngIf="service.chosenPage?.pageName == 'dashboardSiteCompare'"></dashboardSiteCompare>
    <teleStats *ngIf="service.chosenPage?.pageName=='telephoneStats'"></teleStats>

  </div>
</div>