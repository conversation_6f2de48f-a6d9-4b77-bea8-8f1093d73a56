﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CPHI.Spark.Model
{
    public class ApiCallLog
    {
        [Key]
        public int Id { get; set; }
        public int DealerGroup_Id { get; set; }
        [ForeignKey("DealerGroup_Id")]
        
        public string Type { get; set; }
        public string URL { get; set; }
        public string Request { get; set; }

        public string Response { get; set; }

        public DateTime CreatedDate { get; set; }

        public string Status { get; set; }
        public string ErrorNotes { get; set; }

    }
}
