<nav class="navbar">

  <nav class="generic" >
    <h4 id="pageTitle">
      <div >
        {{service.constants.translatedText.DealsDoneThisWeek_Title}}
        <sourceDataUpdate [chosenDate]="service.constants.LastUpdatedDates.Deals"></sourceDataUpdate>

      </div>
    </h4>

    <ng-container *ngIf="service.deals">

      <vehicleTypePickerSpain
        *ngIf="service.constants.environment.customer == 'RRGSpain'"
        [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"
        (updateVehicleTypes)="onUpdateVehicleTypes($event)"
      >
      </vehicleTypePickerSpain>

      <div class="buttonGroup topDropdownButtons">

        <!-- Site selector -->
        <ng-container *ngIf="service.constants.environment.dealDone.showRRGSitePicker">
          <sitePickerRRG [allSites]="service.constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites"
            [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePickerRRG>
        </ng-container>
        <ng-container *ngIf="service.constants.environment.dealDone.showVindisSitePicker">
          <sitePicker [allSites]="service.constants.sitesActiveSales" [sitesFromParent]="service.selections.selectedSites"
            [buttonClass]="'buttonGroupLeft'" (updateSites)="onUpdateSites($event)"></sitePicker>
        </ng-container>

        <!-- VehicleType selector -->
        <vehicleTypePicker *ngIf="service.constants.environment.customer != 'RRGSpain'"
        [vehicleTypeTypesFromParent]="service.vehicleTypeTypes"
        [buttonClass]="'buttonGroupCenter'"
        (updateVehicleTypes)="onUpdateVehicleTypes($event)"></vehicleTypePicker>

        <!-- OrderType selector -->
        <orderTypePicker *ngIf="service.constants.environment.orderTypePicker" [orderTypeTypesFromParent]="service.orderTypeTypes"
          [buttonClass]="'buttonGroupCenter'" (updateOrderTypes)="onUpdateOrderTypes($event)"></orderTypePicker>

        <!-- Franchise selector -->
        <franchisePicker *ngIf="this.service.constants.environment.franchisePicker" [franchisesFromParent]="service.franchises"
          [buttonClass]="'buttonGroupRight'" (updateFranchises)="onUpdateFranchises($event)"></franchisePicker>



      </div>

      <!-- FOR SELECTING WEEK -->
      <div class="buttonGroup">
        <!-- previousWeek -->
        <button class="btn btn-primary" id="weekDown" (click)="onChangeWeekClick(-1)"><i
            class="fas fa-caret-left"></i></button>

        <!-- dropdownWeek -->
        <div ngbDropdown class="d-inline-block" [autoClose]="true">
          <button id="dropdownWeek" (click)="onDropdownWeekClick()" class="btn btn-primary weekDropdown centreButton weekButton"
            ngbDropdownToggle>{{constants.provideWeekLabel(service.chosenWeekStart)}}</button>
          <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

            <!-- the ngFor buttons -->
            <button *ngFor="let week of weeks"
              (click)="onWeekClick(week)"
              ngbDropdownItem>{{constants.provideWeekLabel(week)}}
            </button>

          </div>
        </div>
        
        <!-- nextWeek -->
        <button class="btn btn-primary" id="weekUp" (click)="onChangeWeekClick(1)"><i
            class="fas fa-caret-right"></i></button>
      </div>

    </ng-container>

  </nav>

  <nav class="pageSpecific" >


  </nav>
</nav>

<!-- Main Page -->
<div id="overallHolder" class="single-line-nav" [ngClass]="service.constants.environment.customer">
  <div class="content-new">

    <div id="dealsDoneThisWeek" class="content-inner-new">

      <div id="plotOptionButtons">

        <div class="buttonGroup" *ngIf="service.constants.environment.dealsDoneThisWeek.showPlotOptions">
          <button class="btn btn-primary" *ngFor="let option of plotOptions"
            [ngClass]="{'active':option.label === service.chosenPlotOption.label}"
            (click)="onPlotOptionClick(option)">{{option.label}}</button>
        </div>
    
      </div>

      <div id="tableHolder" *ngIf="service.showTable">
        <div class="buttonGroup zoomButtons">
          <button class="btn btn-primary zoom" id="zoomMinus" (click)="onZoomMinusClick()">-</button>
          <button class="btn btn-primary zoom" id="zoomPlus" (click)="onZoomPlusClick()">+</button>
        </div>


        <button class="btn btn-primary bigbutton" id="bigButtonLeft"
          (click)="onChangeWeekClick(-1)"><i class="fas fa-caret-left"></i></button>

        <button class="btn btn-primary bigbutton" id="bigButtonRight"
          (click)="onChangeWeekClick(1)"><i class="fas fa-caret-right"></i></button>


        <table class="mainTable">

          <tbody>
            <tr class="dealBlobs">

              <td class="yAxisLabels">
                <div class="yAxisHolder">

                  <div class="maxMarker" *ngIf="service.chosenPlotOption.field != 'TotalProductCount'"> {{service.maxValue|cph:'currency':0}} </div>
                  <div class="maxMarker" *ngIf="service.chosenPlotOption.field == 'TotalProductCount'"> {{service.maxValue|cph:'number':0}} </div>

                  <!-- <div class="label"> {{chosenPlotOption.label}} </div> -->
                  <div class="minMarker" *ngIf="service.chosenPlotOption.field != 'TotalProductCount'"> {{service.minValue|cph:'currency':0}} </div>
                  <div class="minMarker" *ngIf="service.chosenPlotOption.field == 'TotalProductCount'"> {{service.minValue|cph:'number':0}} </div>
                </div>
              </td>

              <td *ngFor="let day of service.days">
                <div class="blobHolderBox">
                  <div class="blob gridFont boxShadowSubtle" *ngFor="let deal of day.deals; let i = index;trackBy:trackByFunction"
                    [ngbPopover]="popContent" container="body" placement="auto" [popoverTitle]="popoverTitle"
                    [ngStyle]="{'left.%':i/day.deals.length*80,'bottom.%':deal.height*90,'background-color':'rgba('+deal.red+','+deal.green+',0,0.95)'}">
                    <div class="label gridFont">
                      <div class="spaceBetween column">
                        <div>{{makeBlobLabel(deal)}}</div>
                        <div>{{deal.subLabel}}</div>
                      </div>
                    </div>


                    <ng-template #popContent>

                      <ng-container *ngIf="service.constants.environment.dealDone.showRRGPopoverContent">
                        <popoverContentRRG [dealId]="deal.Id"></popoverContentRRG>
                      </ng-container>

                      <ng-container *ngIf="service.constants.environment.dealDone.showVindisPopoverContent">
                        <popoverContent [dealId]="deal.Id"></popoverContent>
                      </ng-container>

                    </ng-template>

                    <ng-template #popoverTitle>
                      <div class="popoverHeader" (click)="onOpenDealModalClick(deal)">
                        {{deal.Customer + ' - ' + deal.VehicleType + ': ' + deal.OrderType +', '+
                        deal.SiteDescription}}
                      </div>
                    </ng-template>


                  </div>
                </div>

              </td>

            </tr>

            <tr class="xAxisLabels">
              <td></td>
              <td *ngFor="let day of service.days">
                <button class="btn btn-primary dayLabel" (click)="onDayButtonClick(day)"><span>{{day.label}}</span><span>{{dayButtonName(day)}}</span></button>
              </td>
            </tr>
            <tr>
              <td class="xAxislabel" colspan="8">{{service.constants.translatedText.Common_Days}}</td>
            </tr>
          </tbody>

        </table>

      </div>

    </div>

  </div>
</div>