<div class="accountPage">
    <div class="inner boxShadow">
      <div class="imgHolder">
        <img src="assets/imgs/sparkLogo.svg">
      </div>
  
      <h2>{{constants.translatedText.UserMaintenance_ChooseNewPassword}}</h2>
      <div *ngIf="resetSuccess">
        {{constants.translatedText.UserMaintenance_PasswordChangeSuccess}}<br>
        {{constants.translatedText.UserMaintenance_ClickButtonMessage}}
      </div>
      <div *ngIf="resetFailed" class="validation-summary-errors text-danger">{{constants.translatedText.UserMaintenance_FailedToChangePassword}}</div>
      <div *ngIf="passwordMatchFailed" class="validation-summary-errors text-danger">{{constants.translatedText.UserMaintenance_PasswordMismatch}}</div>
      <div *ngIf="passwordcriteriaFailed" class="validation-summary-errors text-danger">
        {{constants.translatedText.UserMaintenance_YourPasswordNeedsTo}}
        <ul>
          <li>{{constants.translatedText.UserMaintenance_BeAtLeast6Characters}}</li>
          <li>{{constants.translatedText.UserMaintenance_IncludeBothUpperAndLower}}</li>
        </ul>
      </div>
  
      <section id="loginForm">
        <form [formGroup]="passwordResetFormGroup" (ngSubmit)="Submit()">
          <div class="form-group" *ngIf="!resetSuccess">
            <div class="inputAndIcon">
              <input class="form-control" formControlName="currentPassword" placeholder="{{constants.translatedText.UserMaintenance_CurrentPassword}}" type="password" value="">
              <!--<fa-icon [icon]="icon.faUser" [fixedWidth]="true"></fa-icon>-->
              <i class="fas fa-lock" aria-hidden="true"></i>
            </div>
          </div>
          <!-- Password -->
          <div class="form-group" *ngIf="!resetSuccess">
            <div class="inputAndIcon">
              <input class="form-control" formControlName="password" data-val-length="The Password must be at least 6 characters long." data-val-length-max="100" data-val-length-min="6" data-val-required="The Password field is required." placeholder="{{constants.translatedText.Password}}" type="password">
              <!--<fa-icon [icon]="icon.faLock" [fixedWidth]="true"></fa-icon>-->
              <i class="fas fa-lock" aria-hidden="true"></i>
            </div>
          </div>
          <!-- ConfirmPassword -->
          <div class="form-group" *ngIf="!resetSuccess">
            <div class="inputAndIcon">
              <input class="form-control" formControlName="confirmPassword" data-val-equalto="The password and confirmation password do not match." data-val-equalto-other="*.Password" placeholder="{{constants.translatedText.UserMaintenance_ConfirmPassword}}" type="password">
              <!--<fa-icon [icon]="icon.faLock" [fixedWidth]="true"></fa-icon>-->
              <i class="fas fa-lock" aria-hidden="true"></i>
            </div>
          </div>
          <div class="btn btn-primary actionButton">
            <input *ngIf="!resetSuccess" type="submit" [disabled]="passwordResetFormGroup.pristine || passwordResetFormGroup.invalid" value="{{constants.translatedText.Reset}}" class="btn btn-default ">
            <input *ngIf="resetSuccess" type="button" class="btn btn-default" value="Login" (click)="RedirectToLogin()">
          </div>
        </form>
      </section>
  
    </div>
  
  
  
  
  </div>
  