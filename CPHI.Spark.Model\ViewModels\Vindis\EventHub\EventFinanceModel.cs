﻿using System;

namespace CPHI.Spark.Model.ViewModels.Vindis.EventHub
{
    public class EventFinanceModel
    {
        public string? QuoteReference { get; set; }
        
        public string? ThirdPartyReference { get; set; }
        
        public string? ProductId { get; set; }
        
        public string? ProductName { get; set; }
        
        public string? ProductType { get; set; }
        
        public decimal Deposit { get; set; }
        
        public int Term { get; set; }
        
        public int? OdometerValue { get; set; }
        
        public decimal Apr { get; set; }
        
        public decimal FirstPaymentAmount { get; set; }
        
        public decimal MonthlyPaymentAmount { get; set; }
        
        public decimal? BalloonPaymentAmount { get; set; }
        
        public decimal? BorrowedAmount { get; set; }
        
        public decimal TotalAmountOfCredit { get; set; }
        
        public decimal? OptionToPurchaseFee { get; set; }
        
        public DateTime? LastModified { get; set; }
        
        public decimal? Cashback { get; set; }

        public decimal? RetailerDepositContribution { get; set; }

        public decimal? ManufacturerDepositContribution { get; set; }

        public decimal? LenderDepositContribution { get; set; }
        
        public decimal? BalanceToFinance { get; set; }
        
        public decimal? ExcessMileageCharge { get; set; }
        
        public bool IsManual { get; set; }

        public string? FinanceType { get; set; }
    }
}
