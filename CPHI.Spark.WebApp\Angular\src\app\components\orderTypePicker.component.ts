import { Component, OnInit, Input, ElementRef, Output, EventEmitter } from "@angular/core";
import { selectableItem } from '../model/main.model';
import { ConstantsService } from '../services/constants.service';



@Component({
  selector: 'orderTypePicker',
  template:    `
    <!-- OrderType selector -->
    <div ngbDropdown dropright class="d-inline-block">
    <button [ngClass]="buttonClass" class=" btn btn-primary" (click)="generateOrderTypesList()"
      ngbDropdownToggle>{{orderTypeChosenLabel()}}</button>
    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
      
      <!-- ngFor buttons -->
      <button  *ngFor="let orderType of orderTypeTypes" (click)="toggleItem(orderType)"
        [ngClass]="{'active':orderType.isSelected}" ngbDropdownItem>{{orderType.translation}}</button>

      <!-- quick select -->
      <button class="quickSelect" *ngIf="constants.environment.orderTypePicker_showRetail" (click)="quickSelectAllRetail()" ngbDropdownItem>Retail</button>
      <button class="quickSelect" *ngIf="constants.environment.orderTypePicker_showFleet" (click)="quickSelectAllFleet()" ngbDropdownItem>Fleet</button>

      <button class="quickSelect" (click)="quickSelectAll()" ngbDropdownItem>{{constants.translatedText.All}}</button>
      
      <div class="spaceBetween">
        <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle
          (click)="selectOrderTypes()">{{constants.translatedText.OKUpper}}</button>
        <button class="dropdownBottomButton" ngbDropdownItem ngbDropdownToggle>{{constants.translatedText.Cancel}}</button>
      </div>

    </div>
  </div>
    
    `
  ,
  styles: [`
  
    

    `]
})
export class OrderTypePickerComponent implements OnInit {
  @Input() orderTypeTypesFromParent: string[];
  @Input() buttonClass: string;
  @Output() updateOrderTypes = new EventEmitter<string[]>();

  public orderTypeTypes: selectableItem[];


  
  constructor(
    public constants: ConstantsService,
    
  ) {




  }


  ngOnInit(): void {
    


  }


  orderTypeChosenLabel() {
    if (this.orderTypeTypesFromParent?.length == 0) {
      return this.constants.translatedText.NoOrderType
    } else if (this.orderTypeTypesFromParent?.length == 1) {
      return this.orderTypeTypesFromParent[0]
    } else {
      return this.constants.translatedText.OrderType
    }
  }

  generateOrderTypesList() {
    //recreate local list
    this.orderTypeTypes = [];
    this.constants.orderTypeTypes.forEach(type => {

      if(type == 'Other'){
        this.orderTypeTypes.push(
          { label: type, isSelected: false, translation: this.constants.translatedText.Other  }
        )
      }

      else{
        this.orderTypeTypes.push(
          { label: type, isSelected: false, translation: type  }
        )
      }

    })
    //tag if it's selected
    this.orderTypeTypes.forEach(s => {
      if (this.orderTypeTypesFromParent.indexOf(s.label) > -1) {
        s.isSelected = true;
      }
    })
  }

  
  toggleItem(item: any) {
    item.isSelected = !item.isSelected;
  }

  selectOrderTypes() {
    this.updateOrderTypes.emit(this.orderTypeTypes.filter(e => e.isSelected).map(e => e.label));
  }

  quickSelectAllRetail() {
    this.orderTypeTypes.forEach(s => {
      if (['Motability', 'Retail', 'Distance', 'Staff'].indexOf(s.label) > -1) {
        s.isSelected = true;
      } else {
        s.isSelected = false;
      }
    })
  }
 

  quickSelectAllFleet() {
    this.orderTypeTypes.forEach(s => {
      if (['Fleet'].indexOf(s.label) > -1) {
        s.isSelected = true;
      } else {
        s.isSelected = false;
      }
    })
  }


  quickSelectAll() {
    //if all selected, select none else select all
    if(this.orderTypeTypes.filter(x=>x.isSelected).length == this.orderTypeTypes.length){
      this.orderTypeTypes.forEach(s=>s.isSelected = false)
    }else{
      this.orderTypeTypes.forEach(s => {
          s.isSelected = true;
      })
    }
  }


}


