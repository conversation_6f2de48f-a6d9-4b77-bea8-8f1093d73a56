﻿CREATE OR ALTER PROCEDURE [autoprice].[GET_TodayAutoPriceChanges]
(
		@chosenDate DATE,
		
		
		@dealerGroupId int
)
  
AS  
BEGIN  


SET NOCOUNT ON;  

DECLARE @runDate DateTime = getDate();
DECLARE @dayAfterChosenDate DateTime = DATEADD(day,1,@chosenDate);

DECLARE @supressPriceUpIfDaysListedOver INT = 
	(
		SELECT ISNULL(value,999) 
		FROM GlobalParams 
		WHERE DealerGroup_Id = @dealerGroupId 
		AND Description = 'SupressPriceUpWhenDaysOverX'
	);

-----------------------------------
-- Latest Snapshots
-----------------------------------
WITH todaySnapshots as
(
	SELECT
	snaps.Id as SnapshotId,
	ads.Id as AdvertId,
	ROW_NUMBER() OVER (PARTITION BY snaps.VehicleAdvert_Id ORDER BY snaps.Id desc) as RowN<PERSON><PERSON>
	FROM autoprice.VehicleAdvertSnapshots snaps
	INNER JOIN autoprice.VehicleAdverts ads on ads.Id = snaps.VehicleAdvert_Id
	INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
	WHERE snaps.SnapshotDate >= @chosenDate AND snaps.SnapshotDate < @dayAfterChosenDate
	AND rs.DealerGroup_Id = @dealerGroupId
	AND snaps.LifecycleStatus <> 'WASTEBIN'
	AND snaps.LifecycleStatus <> 'SOLD'
	AND rs.IsActive = 1
)
SELECT
SnapshotId,AdvertId
INTO #latestSnapshotIds
FROM todaySnapshots 
WHERE RowNumber = 1

-----------------------------------
-- Any optouts in place
-----------------------------------
select 
ads.Id as AdvertId,
ROW_NUMBER() OVER (PARTITION BY ads.Id ORDER BY ads.Id desc) AS RowNumber   
INTO #optouts
FROM autoprice.VehicleOptOuts opts --2871
INNER JOIN autoprice.VehicleAdverts ads on ads.id = opts.VehicleAdvert_Id
INNER JOIN #latestSnapshotIds latests on latests.AdvertId = ads.Id
WHERE CONVERT(date,opts.ActualEndDate) > CONVERT(date,@chosenDate) AND CONVERT(date,opts.CreatedDate) <= CONVERT(date,@chosenDate) 

-----------------------------------
-- Generate the changes
-----------------------------------
SELECT
snaps.Id as SnapshotId,
@runDate as CreatedDate,
snaps.TotalPrice as WasPrice,
snaps.StrategyPrice as NowPrice,
IIF(opts.AdvertId IS NOT NULL,1,0) as WasOptedOutOfWhenGenerated,
IIF (snaps.ValuationMktAvRetail <> 0,1,0) as HasValuation,
ads.DateOnForecourt,
ads.CreatedInSparkDate,
ads.RetailerSite_Id


INTO #changes
FROM autoprice.VehicleAdvertSnapshots snaps
INNER JOIN #latestSnapshotIds snapsLatest on snapsLatest.SnapshotId = snaps.Id
INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
LEFT JOIN #optouts opts on opts.AdvertId = ads.Id AND RowNumber = 1
WHERE snaps.TotalPrice IS NOT NULL
AND ads.DerivativeId IS NOT NULL
AND snaps.StrategyPrice > 0


-----------------------------------
-- Insert the changes
-----------------------------------
--INSERT INTO autoprice.PriceChangeAutoItems
--(VehicleAdvertSnapshot_Id,IsApproved,CreatedDate,WasPrice,NowPrice,WasOptedOutOfWhenGenerated,CouldNotGenerateNoDaysAdvertised,CouldNotGenerateNoRetailRating,CouldNotGenerateNoRuleSet,CouldNotGenerateNoValuation,
--CouldGenerateNewPrice)
SELECT 
SnapshotId as VehicleAdvertSnapshot_Id,
0 as IsApproved,
CreatedDate,
WasPrice,
NowPrice,
IIF(HasValuation = 1 AND WasOptedOutOfWhenGenerated = 1,1,0) AS WasOptedOutOfWhenGenerated,
0 as CouldNotGenerateNoDaysAdvertised,
0 as CouldNotGenerateNoRetailRating,
0 as CouldNotGenerateNoRuleSet,
IIF(HasValuation = 0,1,0) AS CouldNotGenerateNoValuation,
IIF(WasOptedOutOfWhenGenerated = 0 AND HasValuation = 1,1,0) as CouldGenerateNewPrice,
DateOnForecourt,
CreatedInSparkDate,
RetailerSite_Id as RetailerSiteId,
0 as IsKeyChange
FROM #changes

-----------------------------------
-- Cleanup
-----------------------------------
DROP TABLE #latestSnapshotIds
DROP TABLE #changes
DROP TABLE #optouts


END
GO