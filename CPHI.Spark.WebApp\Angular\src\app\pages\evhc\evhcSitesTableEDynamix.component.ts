import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { ColDef, ColGroupDef, GridApi, IRowModel, RowClickedEvent } from 'ag-grid-community';
import { AGGridMethodsService } from "src/app/services/agGridMethods.service";
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from "../../cph.pipe";
import { EvhcSiteRow } from '../../model/main.model';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ConstantsService } from "../../services/constants.service";
import { ExcelExportService } from '../../services/excelExportService';
import { SelectionsService } from "../../services/selections.service";
import { EhvcService } from "./evhc.service";
import { ColumnTypesService } from "src/app/services/columnTypes.service";

@Component({
  selector: "evhcSitesTableEDynamix",
  template: `
    <div id="gridHolder">
      <div id="excelExport" (click)="excelExport()">
        <img [src]="constants.provideExcelLogo()">
      </div>
      <ag-grid-angular id="EVHCTable" class="ag-theme-balham" [gridOptions]="mainTableGridOptions"></ag-grid-angular>
    </div>
  `,
  styleUrls: ["./../../../styles/components/_agGrid.scss"],
  styles: [
    `
      ag-grid-angular.hidden{opacity:0}
      ag-grid-angular {
        width: 100%;
        margin: 0em auto;
      }

      #gridHolder {
        position: relative;
      }
    `,
  ],
})
export class evhcSitesTableEDynamixComponent implements OnInit {
  @Input() isPeopleRows: boolean;
  @Input() isRegional?: boolean;
  @Input() isTech?: boolean;
  @Output() rowClicked = new EventEmitter();

  mainTableGridOptions: GridOptionsCph;
  gridApi: GridApi;
  pinnedTop: EvhcSiteRow[];

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public service: EhvcService,
    public gridHelpersService: AGGridMethodsService,
    public columnTypeService: ColumnTypesService,
  ) { }

  ngOnInit() {
    this.setGridOptions();

    this.service.numbersHaveChanged.subscribe(res => {
      this.refreshTable();
    })
  }

  setGridOptions() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params),
      getLocaleText: (params) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      
      domLayout: 'autoHeight',
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        cellClass: "ag-right-aligned-cell",
        filterParams: {
          applyButton: false,
          clearButton: true,
          cellHeight: this.gridHelpersService.getFilterListItemHeight()
        },
        autoHeight: true
      },
      rowData: this.getRowData(),
      pinnedBottomRowData: this.getPinnedBottomRowData(),
      pinnedTopRowData: this.getPinnedTopRowData(),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      onRowClicked: (params) => this.onRowClick(params),
      onGridReady: (params) => this.onGridReady(params),
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsEdynamix),
      },
      columnDefs: this.getColumnDefinitions()
    }
  }

  getColumnDefinitions() {
    let distinctColumns: ColDef[];

    if (this.isPeopleRows) {
      distinctColumns = [
        { headerName: "", field: "PersonName", colId: "PersonName", width: 200, type: "label" },
        { headerName: "Vehicles Checked", field: "VehiclesChecked", colId: "VehiclesChecked", width: 120, type: "number" }
      ]
    } else {
      distinctColumns = [
        { headerName: "", field: this.isRegional ? "RegionDescription" : "SiteDescription", colId: this.isRegional ? "RegionDescription" : "SiteDescription", width: 200, type: "label" },
        { headerName: "Vehicles Through", field: "VehiclesThrough", colId: "VehiclesThrough", width: 170, type: "number" },
        { headerName: "Vehicles Checked", field: "VehiclesChecked", colId: "VehiclesChecked", width: 120, type: "number" },
        { headerName: "Vehicles Checked %", field: "VehiclesCheckedPercent", colId: "VehiclesCheckedPercent", width: 120, type: "percent" }
      ]
    }

    let columns: ColGroupDef[] = [
      {
        headerName: "Red", children:
          [
            { headerName: "Quoted", field: "RedWorkQuoted", colId: "RedWorkQuoted", width: 120, type: "currency" },
            { headerName: "Sold", field: "RedWorkSold", colId: "RedWorkSold", width: 120, type: "currency" },
            { headerName: "Sold %", field: "RedWorkSoldPercent", colId: "RedWorkSoldPercent", width: 120, type: "percent" }
          ]
      },
      {
        headerName: "Amber", children:
          [
            { headerName: "Quoted", field: "AmberWorkQuoted", colId: "AmberWorkQuoted", width: 120, type: "currency" },
            { headerName: "Sold", field: "AmberWorkSold", colId: "AmberWorkSold", width: 120, type: "currency" },
            { headerName: "Sold %", field: "AmberWorkSoldPercent", colId: "AmberWorkSoldPercent", width: 120, type: "percent" }
          ]
      },
      {
        headerName: "Total", children:
          [
            { headerName: "Quoted", field: "TotalWorkQuoted", colId: "TotalWorkQuoted", width: 120, type: "currency" },
            { headerName: "Sold", field: "TotalWorkSold", colId: "TotalWorkSold", width: 120, type: "currency" },
            { headerName: "Sold %", field: "TotalWorkSoldPercent", colId: "TotalWorkSoldPercent", width: 120, type: "percent" }
          ]
      },
      {
        headerName: "All", children:
          [
            { headerName: "Deferred", field: "RedWorkDeferred", colId: "RedWorkDeferred", width: 120, type: "currency" },
            { headerName: "Deferred %", field: "RedWorkDeferredPercent", colId: "RedWorkDeferredPercent", width: 120, type: "percent" },
            { headerName: "Declined", field: "WorkDeleted", colId: "WorkDeleted", width: 120, type: "currency" },
            { headerName: "Declined %", field: "WorkDeletedPercent", colId: "WorkDeletedPercent", width: 120, type: "percent" }
          ]
      }
    ]

    return distinctColumns.concat(columns);
  }

  getPinnedBottomRowData(): any[] {
    if (this.isPeopleRows) return this.getPeopleTotalRow();
    return this.service.siteRowsEDynamix.filter(r => r.IsTotal);
  }

  getPeopleTotalRow() {
    let numberOfRows: number = this.isTech ? this.service.personRowsEDynamix.filter(x => x.IsTechnician).length : this.service.personRowsEDynamix.filter(x => !x.IsTechnician).length;

    let filteredPersonRowsEDynamix = this.service.personRowsEDynamix.filter(x => this.isTech && x.IsTechnician || !this.isTech && !x.IsTechnician);
    
    return [{
      PersonName: '',
      VehiclesChecked: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.VehiclesChecked)),
      RedWorkQuoted: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.RedWorkQuoted)),
      RedWorkSold: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.RedWorkSold)),
      RedWorkSoldPercent: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.RedWorkSoldPercent)) / numberOfRows,
      AmberWorkQuoted: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.AmberWorkQuoted)),
      AmberWorkSold: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.AmberWorkSold)),
      AmberWorkSoldPercent: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.AmberWorkSoldPercent)) / numberOfRows,
      TotalWorkQuoted: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.TotalWorkQuoted)),
      TotalWorkSold: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.TotalWorkSold)),
      TotalWorkSoldPercent: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.TotalWorkSoldPercent)) / numberOfRows,
      RedWorkDeferred: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.RedWorkDeferred)),
      RedWorkDeferredPercent: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.RedWorkDeferredPercent)) / numberOfRows,
      WorkDeleted: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.WorkDeleted)),
      WorkDeletedPercent: this.constants.sum(filteredPersonRowsEDynamix.map(x => x.WorkDeletedPercent)) / numberOfRows
    }]
  }

  getPinnedTopRowData(): any[] {
    if (this.isRegional || this.isPeopleRows) return;
    
    return [{
      Label: this.constants.translatedText.Common_Target,
      VehiclesCheckedPercent: this.constants.environment.evhc.vehiclesCheckedPercent / 100,
      RedWorkSoldPercent: this.constants.environment.evhc.redWorkSoldPercent / 100,
      AmberWorkSoldPercent: this.constants.environment.evhc.amberWorkSoldPercent / 100
    }]
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
  }

  onRowClick(event: RowClickedEvent): void {
    if (event.rowPinned === 'top' || this.isPeopleRows) return;

    this.rowClicked.next(event.data);
  }

  refreshTable(): void {
    if (!this.gridApi) return;

    this.gridApi.setRowData(this.getRowData());
    this.gridApi.setPinnedBottomRowData(this.getPinnedBottomRowData());
    this.gridApi.sizeColumnsToFit();
  }


  excelExport() {
    let tableModel: IRowModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Vehicle Health Check Summary', 1, 1);
  }

  getRowData(): any[] {
    if (this.isPeopleRows) {
      return this.service.personRowsEDynamix.filter(x => this.isTech && x.IsTechnician || !this.isTech && !x.IsTechnician);
    } else {
      return this.service.siteRowsEDynamix.filter(x => this.isRegional && x.IsRegion || !this.isRegional && x.IsSite);
    }
  }
}
