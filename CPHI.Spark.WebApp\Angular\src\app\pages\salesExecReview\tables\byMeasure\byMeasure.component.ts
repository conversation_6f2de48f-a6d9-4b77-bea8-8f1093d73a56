import { Component, ElementRef, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import { ColumnApi, GridApi } from 'ag-grid-community';
import { Subscriber, Subscription } from 'rxjs';
import { CphPipe } from 'src/app/cph.pipe';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { SelectionsService } from 'src/app/services/selections.service';
import { BarComponent } from 'src/app/_cellRenderers/bar.component';
import { CustomHeaderComponent } from 'src/app/_cellRenderers/customHeader.component';
import { localeEs } from 'src/environments/locale.es.js';
import { PeopleSummaryByMeasure, SEReviewMeasure, SiteSummaryByMeasure } from '../../salesExecReview.model';
import { SalesExecReviewService } from '../../salesExecReview.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';

interface ByMeasureSummaryRow {
  Id: number;
  IsRegion: boolean;
  IsSite: boolean;
  IsTotal: boolean;
  Label: string;
  LockedPercentage: number;
  NonScoringMeasures_DealsWithCEMResponse: number;
  NonScoringMeasures_EMAXConversion: number;
  NonScoringMeasures_EMAXOffersSentRatio: number;
  NonScoringMeasures_EMAXTestDriveRatio: number;
  NonScoringMeasures_IStoreDocsCompliance: number;
  NonScoringMeasures_MartecRightWordsScore: number;
  NonScoringMeasures_NewEnquiries: number;
  NonScoringMeasures_NewFinancePenetration: number;
  NonScoringMeasures_NewRenewals: number;
  NonScoringMeasures_OffersSentRatioz: number;
  NonScoringMeasures_ProductsPerUnit: number;
  NonScoringMeasures_ProfitPerUnit: number;
  NonScoringMeasures_TelephoneAppointmentRatio: number;
  NonScoringMeasures_UsedFinancePenetration: number;
  NonScoringMeasures_UsedRenewals: number;
  ScoringMeasures_NewCEM: number;
  ScoringMeasures_NewUnits: number;
  ScoringMeasures_NonFranchiseUnits: number;
  ScoringMeasures_OverallScore: number;
  ScoringMeasures_ProfitPot: number;
  ScoringMeasures_UsedAndDemoUnits: number;
  ScoringMeasures_UsedCEM: number;
  State: string;
  EPR: any; // can be either a number or string
}

@Component({
  selector: 'salesExecReviewByMeasureTable',
  templateUrl: './byMeasure.component.html',
  styleUrls: ['./byMeasure.component.scss', './../../../../../styles/components/_agGrid.scss']
})

export class SalesExecReviewByMeasureTableComponent implements OnInit {
  @ViewChild('gridHolder', { static: true }) tableContainer: ElementRef;

  @HostListener('window:resize', [])

  @Input() isRegional: boolean;

  rowData: ByMeasureSummaryRow[];
  pinnedBottomRowData: ByMeasureSummaryRow[];
  mainTableGridOptions: GridOptionsCph;
  sub: Subscription;
  public gridApi: GridApi;
  public gridColumnApi: ColumnApi;

  constructor(
    public gridHelpersService: AGGridMethodsService,
    public selections: SelectionsService,
    public excel: ExcelExportService,
    public columnTypeService: ColumnTypesService,
    public constants: ConstantsService,
    public service: SalesExecReviewService,
    public cphPipe: CphPipe,
    public gridHelpers: AGGridMethodsService
  ) { }

  ngOnInit() {
    this.formatRowData();
    this.setGridOptions();
    
    this.sub = this.service.salesExecReview.byMeasureDataChangeEmitter.subscribe(() => {
      this.refreshView();
    })
  }

  ngOnDestroy()
  {
    if(this.sub){ this.sub.unsubscribe(); }
  }
  
  formatRowData(refresh?: boolean) {
    let unformattedRows: (SiteSummaryByMeasure | PeopleSummaryByMeasure)[];

    if (this.service.salesExecReview.sitesView) {
      if (this.isRegional) {
        unformattedRows = this.service.salesExecReview.siteByMeasureRows.filter(x => !x.IsSite);
      } else {
        unformattedRows = this.service.salesExecReview.siteByMeasureRows.filter(x => !x.IsRegion);
      }
    } else {
      unformattedRows = this.service.salesExecReview.peopleByMeasureRows;
    }

    let formattedRows: ByMeasureSummaryRow[] = [];

    unformattedRows.forEach(row => {
      let scoring: SEReviewMeasure[] = row.ScoringMeasureActualPercentages;
      let nonScoring: SEReviewMeasure[] = row.NonScoringMeasureActualPercentages;

      let formattedRow: ByMeasureSummaryRow = {
        Id: this.service.salesExecReview.sitesView ? row['SiteId'] : row['PersonId'],
        IsRegion: row.IsRegion,
        IsSite: row.IsSite,
        IsTotal: row.IsTotal,
        Label: row.Label,
        LockedPercentage: this.service.salesExecReview.sitesView ? row['LockedPercentage'] : null,
        ScoringMeasures_ProfitPot: scoring.length > 1 ? scoring.find(x => x.MeasureName == 'Profit Pot')?.Actual : 0,
        ScoringMeasures_NewCEM: scoring.length > 1 ? scoring.find(x => x.MeasureName == 'New CEM (Rolling 3 months)')?.Actual : 0,
        ScoringMeasures_UsedCEM: scoring.length > 1 ? scoring.find(x => x.MeasureName == 'Used CEM (Rolling 3 months)')?.Actual : 0,
        ScoringMeasures_NewUnits: scoring.length > 1 ? scoring.find(x => x.MeasureName == 'New Units')?.Actual : 0,
        ScoringMeasures_UsedAndDemoUnits: scoring.length > 1 ? scoring.find(x => x.MeasureName == 'Used & Demo Units')?.Actual : 0,
        ScoringMeasures_NonFranchiseUnits: scoring.length > 1 ? scoring.find(x => x.MeasureName == 'Non-Franchise Units')?.Actual : 0,
        ScoringMeasures_OverallScore: row.OverallActualPercentage,
        NonScoringMeasures_IStoreDocsCompliance: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'iStoreDocs Compliance')?.Actual : 0,
        NonScoringMeasures_NewFinancePenetration: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'New Finance Penetration')?.Actual : 0,
        NonScoringMeasures_UsedFinancePenetration: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'Used Finance Penetration')?.Actual : 0,
        NonScoringMeasures_ProductsPerUnit: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'Products per Unit')?.Actual : 0,
        NonScoringMeasures_ProfitPerUnit: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'Profit per Unit (Products)')?.Actual : 0,
        NonScoringMeasures_DealsWithCEMResponse: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'Deals with CEM Response %')?.Actual : 0,
        NonScoringMeasures_NewRenewals: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'New Renewals')?.Actual : 0,
        NonScoringMeasures_UsedRenewals: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'Used Renewals')?.Actual : 0,
        NonScoringMeasures_NewEnquiries: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'New Enquiries')?.Actual : 0,
        NonScoringMeasures_EMAXTestDriveRatio: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'EMAX Test Drive Ratio')?.Actual : 0,
        NonScoringMeasures_EMAXConversion: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'EMAX Conversion')?.Actual : 0,
        NonScoringMeasures_EMAXOffersSentRatio: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'EMAX Offers Sent Ratio')?.Actual : 0,
        NonScoringMeasures_OffersSentRatioz: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'EMAX Offers Sent Ratio')?.Actual : 0,
        NonScoringMeasures_TelephoneAppointmentRatio: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'Telephone Appointment Ratio')?.Actual : 0,
        NonScoringMeasures_MartecRightWordsScore: nonScoring.length > 1 ? nonScoring.find(x => x.MeasureName == 'Martec Right Words Score')?.Actual : 0,
        State: this.service.salesExecReview.peopleView ? row['ApprovalState'] : null,
        EPR: row.EPR
      }

      if (row.IsTotal) this.pinnedBottomRowData = [formattedRow];
      else formattedRows.push(formattedRow);
    })

    this.rowData = formattedRows;

    if (refresh) {
      this.gridApi.setRowData(this.rowData);
      this.gridApi.setPinnedBottomRowData(this.pinnedBottomRowData);
    }
  }

  setGridOptions() {
    this.mainTableGridOptions = {
      getContextMenuItems: (params) => this.gridHelpers.getContextMenuItems(params),
      
      getLocaleText: (params: any) =>  this.constants.currentLang === 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      context: { thisComponent: this },
      domLayout: 'autoHeight',
      suppressPropertyNamesCheck: true,
      onRowClicked: (params) => this.onRowClick(params),
      onGridReady: (params) => this.onGridReady(params),
      getRowHeight: (params) => {
        const normalHeight = Math.min(25, Math.max(19, Math.round(25 * this.selections.screenHeight / 960)))
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      groupHeaderHeight: this.gridHelpersService.getRowPinnedHeight(),
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      getMainMenuItems:(params) => this.gridHelpers.getColMenuWithTopBottomHighlight(params, this.service.topBottomHighlightsByMeasure),
      defaultColDef: {
        resizable: true,
        sortable: true
      },
      rowData: this.rowData,
      pinnedBottomRowData: this.pinnedBottomRowData,
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsByMeasure),
      },
      columnDefs: this.getColumnDefs()
    }
  }

  refreshView() {
    this.rowData = null;
    this.pinnedBottomRowData = null;
    this.gridApi.setColumnDefs([]);
    this.gridApi.setColumnDefs(this.getColumnDefs());
    this.formatRowData(true);
    this.gridApi.sizeColumnsToFit();
    this.selections.triggerSpinner.emit({ show: false });
  }

  getColumnDefs() {
    return [
      { headerName: this.service.salesExecReview.sitesView ? 'Site' : 'Sales Exec', colId: 'Label', field: 'Label', width: 150, type: 'label' },
      {
        headerName: 'Scoring Measures', children: [
          { headerName: 'Profit Pot', colId: 'ProfitPot', field: 'ScoringMeasures_ProfitPot', width: 80, type: 'percent' },
          { headerName: 'New CEM (Rolling 3)', colId: 'NewCEM', field: 'ScoringMeasures_NewCEM', width: 80, type: 'percent' },
          { headerName: 'Used CEM (Rolling 3)', colId: 'UsedCEM', field: 'ScoringMeasures_UsedCEM', width: 80, type: 'percent' },
          { headerName: 'New Units', colId: 'NewUnits', field: 'ScoringMeasures_NewUnits', width: 80, type: 'percent' },
          { headerName: 'Used & Demo Units', colId: 'UsedAndDemoUnits', field: 'ScoringMeasures_UsedAndDemoUnits', width: 80, type: 'percent' },
          { headerName: 'Non-Franchise Units', colId: 'NonFranchiseUnits', field: 'ScoringMeasures_NonFranchiseUnits', width: 80, type: 'percent' },
          { headerName: 'Overall Score', colId: 'OverallScore', field: 'ScoringMeasures_OverallScore', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.salesExecReview.overallScoreThresholds }
        ]
      },
      {
        headerName: 'Non-Scoring Measures', children: [
          { headerName: 'iStoreDocs Compliance', colId: 'iStoreDocsCompliance', field: 'NonScoringMeasures_IStoreDocsCompliance', width: 80, type: 'percent' },
          { headerName: 'New Finance Penetration', colId: 'NewFinancePenetration', field: 'NonScoringMeasures_NewFinancePenetration', width: 80, type: 'percent' },
          { headerName: 'Used Finance Penetration', colId: 'UsedFinancePenetration', field: 'NonScoringMeasures_UsedFinancePenetration', width: 80, type: 'percent' },
          { headerName: 'Products per Unit', colId: 'ProductsPerUnit', field: 'NonScoringMeasures_ProductsPerUnit', width: 80, type: 'percent' },
          { headerName: 'Profit per Unit (Products)', colId: 'ProfitPerUnit', field: 'NonScoringMeasures_ProfitPerUnit', width: 80, type: 'percent' },
          { headerName: 'Deals with CEM Response', colId: 'DealsWithCEMResponse', field: 'NonScoringMeasures_DealsWithCEMResponse', width: 80, type: 'percent' },
          { headerName: 'New Renewals', colId: 'NewRenewals', field: 'NonScoringMeasures_NewRenewals', width: 60, type: 'percent' },
          { headerName: 'Used Renewals', colId: 'UsedRenewals', field: 'NonScoringMeasures_UsedRenewals', width: 60, type: 'percent' },
          { headerName: 'New Enquiries', colId: 'NewEnquiries', field: 'NonScoringMeasures_NewEnquiries', width: 60, type: 'percent' },
          { headerName: 'EMAX Test Drive Ratio', colId: 'EMAXTestDriveRatio', field: 'NonScoringMeasures_EMAXTestDriveRatio', width: 80, type: 'percent' },
          { headerName: 'EMAX Conversion', colId: 'EMAXConversion', field: 'NonScoringMeasures_EMAXConversion', width: 80, type: 'percent' },
          { headerName: 'EMAX Offers Sent Ratio', colId: 'EMAXOffersSentRatio', field: 'NonScoringMeasures_EMAXOffersSentRatio', width: 80, type: 'percent' },
          { headerName: 'Telephone Appointment Ratio', colId: 'TelephoneAppointmentRatio', field: 'NonScoringMeasures_TelephoneAppointmentRatio', width: 80, type: 'percent' },
          { headerName: 'Martec Right Words Score', colId: 'MartecRightWordsScore', field: 'NonScoringMeasures_MartecRightWordsScore', width: 80, type: 'percent' }
        ]
      },
      { headerName: 'State', colId: 'State', field: 'State', width: 80, type: 'label', cellClass: params => { return params.data.State }, hide: this.service.salesExecReview.sitesView },
      { headerName: 'Locked Percentage', colId: 'LockedPercentage', field: 'LockedPercentage', width: 80, type: 'labelPercent', cellRenderer: BarComponent, cellRendererParams: this.service.salesExecReview.lockedPercentageThresholds, hide: this.service.salesExecReview.peopleView },


      { headerName: 'EPR?', colId: 'EPR', field: 'EPR', width: 50, type: 'number', hide: !this.service.salesExecReview.sitesView }, // Site view - show number of EPRs
      { headerName: 'EPR?', colId: 'EPR', field: 'EPR', width: 50, type: 'label', hide: this.service.salesExecReview.sitesView  }, // Person view - show Yes/No
      
    ]
  }

  onRowClick(params): void {
    if (!this.service.salesExecReview.sitesView && (params.data.IsRegion || params.data.IsTotal)) return;

    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.salesExecReview.lastTableView = 'measure';

    if (this.service.salesExecReview.sitesView) {
      let ids: number[];
      if (params.data.IsSite) ids = [params.data.Id];
      if (params.data.IsRegion) ids = this.constants.sitesActive.filter(x => x.RegionDescription == params.data.Label).map(x => x.SiteId);
      if (params.data.IsTotal) ids = this.constants.sitesActive.map(x => x.SiteId);

      this.service.salesExecReview.chosenSiteIds = ids;
      this.service.getPeopleByMeasureRows(true, params.data.Label);
    } else {
      this.service.getFormData(params.data.Id, params.data.Label);
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.mainTableGridOptions.context = { thisComponent: this }
    this.gridApi.sizeColumnsToFit();
  }

  excelExport() {
    const tableModel = this.gridApi.getModel();
    this.excel.createSheetObject(tableModel, 'Sales Exec Review - Sites', 1, 1);
  }

  backToSites() {

    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading });
    this.service.salesExecReview.lastTableView = 'measure';

    this.service.salesExecReview.chosenSiteIds = this.constants.sitesActive.map(x => x.SiteId);
    this.service.getSiteByMeasureRows(true);
  }

  private onresize(event) {
    if (this.gridApi) {
      this.gridApi.resetRowHeights();
      this.gridApi.sizeColumnsToFit();
    }
  }
}
