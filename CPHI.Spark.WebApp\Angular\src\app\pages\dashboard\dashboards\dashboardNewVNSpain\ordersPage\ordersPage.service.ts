
import { EventEmitter, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CphPipe } from 'src/app/cph.pipe';
import { DistrinetService } from 'src/app/pages/distrinet/distrinet.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardService } from '../../../dashboard.service';
import { DashboardVNOrder } from "../../../../../model/DashboardVNOrder";
import { VNTileParams } from "../../../../../model/VNTileParams";
import { DashboardMeasure } from "../../../../../model/DashboardMeasure";
import { DashboardDataVNParams } from 'src/app/model/DashboardDataVNParams';
import { VNTileTableRow } from 'src/app/model/VNTileTableRow';
import { BIChartTileDataType } from 'src/app/components/biChartTile/biChartTile.component';
import { MenuItemNew } from 'src/app/model/main.model';

@Injectable({
  providedIn: 'root'
})


export class SpainDashboardNewVNOrdersPageService {



  filterChoices: DashboardMeasure[]
  highlightChoices: DashboardMeasure[]


  chosenMonth: Date;

  rawData: DashboardVNOrder[];
  rawDataFiltered: DashboardVNOrder[];
  rawDataHighlighted: DashboardVNOrder[]; //subset of filtered.  
  months: Date[];

  refreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
  refreshTileEmitter: EventEmitter<void> = new EventEmitter();
  highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();




  constructor(
    public getDataService: GetDataMethodsService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public dashboardService: DashboardService,
    public cphPipe: CphPipe,
    public distrinetPageService: DistrinetService,
    private router: Router
  ) { }



  initParams() {


    this.filterChoices = [
      { FieldName: 'Franchise', FieldNameTranslation: this.constants.translatedText.Franchises, IsDate: false, ChosenValues: [] },
      { FieldName: 'Model', FieldNameTranslation: this.constants.translatedText.Model + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'ModelCode', FieldNameTranslation: this.constants.translatedText.ModelCode + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Advance', FieldNameTranslation: this.constants.translatedText.Advance + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'SiteDescription', FieldNameTranslation: this.constants.translatedText.Sites, IsDate: false, ChosenValues: [] },
      { FieldName: 'CustomerType', FieldNameTranslation: this.constants.translatedText.CustomerType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'RegionDescription', FieldNameTranslation: this.constants.translatedText.Region + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'EnergyType', FieldNameTranslation: this.constants.translatedText.EnergyType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'OrderDate', FieldNameTranslation: this.constants.translatedText.OrderDate + 's', IsDate: true, ChosenValues: [] }
    ];

    this.highlightChoices = [
      { FieldName: 'Franchise', FieldNameTranslation: this.constants.translatedText.Franchises, IsDate: false, ChosenValues: [] },
      { FieldName: 'Model', FieldNameTranslation: this.constants.translatedText.Model + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'ModelCode', FieldNameTranslation: this.constants.translatedText.ModelCode + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Advance', FieldNameTranslation: this.constants.translatedText.Advance + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'SiteDescription', FieldNameTranslation: this.constants.translatedText.Sites, IsDate: false, ChosenValues: [] },
      { FieldName: 'CustomerType', FieldNameTranslation: this.constants.translatedText.CustomerType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'RegionDescription', FieldNameTranslation: this.constants.translatedText.Region + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'EnergyType', FieldNameTranslation: this.constants.translatedText.EnergyType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'OrderDate', FieldNameTranslation: this.constants.translatedText.OrderDate + 's', IsDate: true, ChosenValues: [] }
    ];


    this.months = this.constants.makeDistrinetMonths();
    this.chosenMonth = this.months[this.months.length - 1];

  }




  getPageParams(): VNTileParams {
    return {
      highlightChoices: this.highlightChoices,
      filterChoices: this.filterChoices,
      
      filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
      highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
      updateThisPicker: this.refreshFilterListsEmitter,
      updateThisTile: this.refreshTileEmitter,
      parentMethods: {
        buildRows: (fieldName, dataType) => this.buildTableRows(fieldName, dataType),
        highlightRow: (row, fieldName) => this.highlightRow(row, fieldName),
        provideItemsList: (fieldName, isDateField) => this.provideItemsList(fieldName, isDateField),
      }
    }
  }



  highlightRow(row: VNTileTableRow, fieldName: string) {
    let userChoice = this.highlightChoices.find(x => x.FieldName === fieldName);
    let isItemSelected = userChoice.ChosenValues.length === 0 || userChoice.ChosenValues.includes(row.Label);
    if (userChoice.ChosenValues.length === 0) {
      this.highlightChoices.find(x => x.FieldName === fieldName).ChosenValues = [row.Label];
    } else if (isItemSelected) {
      userChoice.ChosenValues = userChoice.ChosenValues.filter(x => x !== row.Label)
    } else {
      userChoice.ChosenValues.push(row.Label)
    }

    //this.pageParams.highlightChoiceHasBeenMade.emit(true);
  }

  getData(): void {
    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading })
    let params: DashboardDataVNParams = this.getParms(this.chosenMonth)
    this.getDataService.getDashboardVNOrders(params).subscribe((res: DashboardVNOrder[]) => {
      res.map(x => {
        x.OrderDate = new Date(x.OrderDate)
      })

      this.rawData = res;
      this.rawDataFiltered = this.filterData(this.rawData, this.filterChoices);;
      this.rawDataHighlighted = this.filterData(this.rawDataFiltered, this.highlightChoices)
      this.refreshFilterListsEmitter.emit();
      this.refreshTileEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });
    }, error => {
      console.error("ERROR: ", error);
    });


  }

  selectSnapshot(snapshot: Date) {
    this.chosenMonth = snapshot;
    this.getData()
  }


  isHighlightFiltersOn() {
    let isHighlights = false;
    let i = 0;
    while (!isHighlights && i < this.highlightChoices.length) {
      isHighlights = this.highlightChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isHighlights;
  }

  isFiltersOn() {
    if (!this.filterChoices) { return false; }
    let isFilters = false;
    let i = 0;
    while (!isFilters && i < this.filterChoices.length) {
      isFilters = this.filterChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isFilters;
  }


  private getParms(snapShot: Date): DashboardDataVNParams {
    return {
      SiteIds: this.dashboardService.chosenSites.map(x => x.SiteId),
      SnapshotDate: snapShot,
    };
  }

  clearHighlights() {
    this.highlightChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.highlightItems();
    this.refreshTileEmitter.emit();
  }
  clearFilters() {
    this.filterChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.filterItems();
    this.refreshTileEmitter.emit();
  }


  filterData(dataIn: DashboardVNOrder[], stringFilterChoices: DashboardMeasure[]): DashboardVNOrder[] {
    let results = [];
    dataIn.forEach(item => {
      //check all chosen strings
      let filterOutThisItem: boolean = false;
      stringFilterChoices.forEach(choice => {
        if (choice.IsDate) {
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) {
            choice.ChosenValues.forEach(date => {
              // Want to check if date falls within a week range not just if date = date label on chosen bar(s)
              const orderDate: Date = new Date(date);
              const orderDatePlus7Days: Date = this.constants.addDays(new Date(date), 7);

              if (!((item[choice.FieldName].getTime() >= orderDate.getTime()) && item[choice.FieldName].getTime() < orderDatePlus7Days.getTime())) {
                filterOutThisItem = true;
              }
            })
          }
        } else {
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(item[choice.FieldName])) { filterOutThisItem = true; } }
        }
      })
      //check all chosen dates
      // if (!filterOutThisItem) {
      //   Object.keys(dateFilterChoices).forEach(key => {
      //     if (!filterOutThisItem && dateFilterChoices[key].length !== 0) { if (!dateFilterChoices[key].includes(item[key])) { filterOutThisItem = true; } }
      //   })
      // }

      if (!filterOutThisItem) { results.push(item) }
    })

    return results;
  }




  filterItems() {
    //have chosen ok from a dropdown picker
    this.rawDataFiltered = this.filterData(this.rawData, this.filterChoices)
    this.highlightItems()
  }

  highlightItems() {
    //have clicked a row in a tile.  
    this.rawDataHighlighted = this.filterData(this.rawDataFiltered, this.highlightChoices)
    this.refreshTileEmitter.emit()
  }





  navigateToDistrinetPage() {
    //initialise distrinet page
    this.distrinetPageService.initParams()
    this.distrinetPageService.chosenOriginTypes = ['Orders']
    this.distrinetPageService.chosenSnapshot = this.chosenMonth;

    //set franchise codes on distrinet page
    let filterFranchises = this.filterChoices.find(x => x.FieldName === 'Franchise').ChosenValues;
    let highlightFranchises = this.highlightChoices.find(x => x.FieldName === 'Franchise').ChosenValues;
    let chosenFranchises = this.intersectChoices(filterFranchises, highlightFranchises);
    if (chosenFranchises.length === 0) {
      this.distrinetPageService.chosenFranchises = this.constants.FranchiseCodes;
    } else {
      this.distrinetPageService.chosenFranchises = this.highlightChoices.find(x => x.FieldName === 'Franchise').ChosenValues.map(x => x.substring(0, 1).toUpperCase());
    }


    //build up filter model to set on the distrinet page
    let filterModel = {};
    //let filterChoicesNoFran = this.filterChoices.filter(x => x.FieldName !== 'Franchise');
    //let highlightChoicesNoFran = this.highlightChoices.filter(x => x.FieldName !== 'Franchise');
    this.filterChoices.forEach((choice, i) => {
      let filterChoices = choice.ChosenValues;
      let highlightChoices = this.highlightChoices[i].ChosenValues;
      let chosenItems = this.intersectChoices(filterChoices, highlightChoices);
      if (chosenItems.length > 1) {
        if (choice.IsDate) {
          filterModel[choice.FieldName] = { filterType: 'date', operator: 'OR' }
        } else {
          filterModel[choice.FieldName] = { filterType: 'text', operator: 'OR' }
        }
        let i = 0;
        chosenItems.forEach(value => {
          if (choice.IsDate) {
            filterModel[choice.FieldName][`condition${i + 1}`] = {
              filterType: 'date', type: 'equals', dateFrom: this.constants.deductTimezoneOffset(new Date(this.cphPipe.transform(value, 'date', 0))).toISOString().split('T')[0], dateTo: null
            }
          }
          else {
            filterModel[choice.FieldName][`condition${i + 1}`] = {
              filterType: 'text', type: 'contains', filter: value
            }
          }
          i++;
        })
      } else if (chosenItems.length > 0) {
        if (choice.IsDate) {
          filterModel[choice.FieldName] = { filterType: 'date', type: 'equals', dateFrom: this.constants.deductTimezoneOffset(new Date(this.cphPipe.transform(chosenItems[0], 'date', 0))).toISOString().split('T')[0], dateTo: null }
        }
        else {
          filterModel[choice.FieldName] = { filterType: 'text', type: 'contains', filter: chosenItems[0] }
        }
      }
    })

    this.distrinetPageService.filterModel = filterModel;
    
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/distrinet');
    if (menuItem) { this.constants.navigateByUrl(menuItem); } //, 'operationreports'
  }

  provideItemsList(fieldName: string, isDateField: boolean) {
    if (!this.rawData) { return [] }
    return [...new Set(this.rawData.map(x => x[fieldName]))]
  }

  buildTableRows(fieldName: string, dataType: BIChartTileDataType) {
    let tableRows: VNTileTableRow[] = []
    if (dataType===BIChartTileDataType.day) {
      tableRows = this.buildTableRowsDatesBasis(fieldName, false)
    } else if(dataType===BIChartTileDataType.weekly){
      tableRows = this.buildTableRowsDatesBasis(fieldName, true)
    }
    else {
      tableRows = this.buildTableRowsNonDatesBasis(fieldName)
    }

    //limit to 20
    return tableRows;//.slice(0, 20);
  }

  buildTableRowsNonDatesBasis(fieldName: string): VNTileTableRow[] {
    let tableRows: VNTileTableRow[] = [];

    //go through filteredData to find unique labels and countup
    let labels: string[] = [];
    this.rawDataFiltered.forEach(item => {
      const itemLabel = item[fieldName];
      let labelsIndex = labels.findIndex(x => x === itemLabel);
      if (labelsIndex === -1) {
        //don't already have label, must be new
        labels.push(itemLabel)
        tableRows.push({ Label: itemLabel, FilteredTotal: item.IsCancelled ? 0 : 1, HighlightedTotal: 0 })
      } else {
        tableRows[labelsIndex].FilteredTotal += item.IsCancelled ? 0 : 1;
      }
    })

    //find out values to show
    this.rawDataHighlighted.forEach(item => {
      const itemLabel = item[fieldName];
      const indexInLabels = labels.findIndex(x => x === itemLabel);
      //should exist or something has gone wrong as orderDataHighlighted should be subset of orderDataFiltered
      tableRows[indexInLabels].HighlightedTotal += item.IsCancelled ? 0 : 1;
    })

    tableRows = tableRows.sort((a, b) => b.FilteredTotal - a.FilteredTotal)

    return tableRows;

  }




  buildTableRowsDatesBasis(fieldName: string, isWeekly?: boolean): VNTileTableRow[] {
    //similar approach to non-dates, but looks odd if we have gaps in the days, so we find the earliest date then iterate every day at a time since then even if it has no data
    let earliestDate: Date = this.rawDataFiltered.map(x => x[fieldName]).sort((a, b) => a.getTime() - b.getTime())[0];
    let latestDate: Date = this.rawDataFiltered.map(x => x[fieldName]).sort((a, b) => b.getTime() - a.getTime())[0];

    let tableRows = [];
    let currentDate = new Date(earliestDate);
    //walk through creating tableRows, with zero values
    while (!this.constants.datesAreSame(currentDate, latestDate)) {
      const label = this.cphPipe.transform(currentDate, 'date', 0);
      tableRows.push({ Label: label, FilteredTotal: 0, HighlightedTotal: 0 })
      currentDate = this.constants.addDays(currentDate, 1);
    }

    //update these new rows with orderData
    const orderDataSorted = this.rawDataFiltered.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    let currentTableRowIndex: number = 0;
    orderDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].FilteredTotal += dataItem.IsCancelled ? 0 : 1; }
    })

    //update with highlighted data
    const highlightedDataSorted = this.rawDataHighlighted.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    currentTableRowIndex = 0;
    highlightedDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].HighlightedTotal += dataItem.IsCancelled ? 0 : 1; }
    })

    let tableRowsWeekly: any[] = [];

    if (isWeekly) {
      let day: number = 1;
      let weekNumber: number = 0;

      tableRows.forEach(row => {
        if (!tableRowsWeekly[weekNumber]) {
          tableRowsWeekly[weekNumber] = {
            FilteredTotal: 0,
            HighlightedTotal: 0,
            Label: tableRows[7 * weekNumber].Label
          };
        }

        tableRowsWeekly[weekNumber].FilteredTotal = tableRowsWeekly[weekNumber].FilteredTotal + row.FilteredTotal;
        tableRowsWeekly[weekNumber].HighlightedTotal = tableRowsWeekly[weekNumber].HighlightedTotal + row.HighlightedTotal;


        weekNumber = day % 7 === 0 ? weekNumber + 1 : weekNumber;
        day++;
      })
    }

    return isWeekly ? tableRowsWeekly : tableRows;

  }



  private intersectChoices(filterFranchises: string[], highlightFranchises: string[]) {
    let chosenFranchises = [];
    if (filterFranchises.length !== 0) {
      if (highlightFranchises.length !== 0) {
        chosenFranchises = filterFranchises.filter(x => highlightFranchises.includes(x));
      } else {
        chosenFranchises = filterFranchises;
      }
    } else {
      if (highlightFranchises.length !== 0) {
        chosenFranchises = highlightFranchises;
      } else {
        chosenFranchises = [];
      }
    }
    return chosenFranchises;
  }
}
