import { Component, ElementRef, EventEmitter, Input, OnInit, ViewChild } from "@angular/core";
import { ConstantsService } from "src/app/services/constants.service";
import { SpainDailyNetOrderItem } from "../../dashboard.model";
import { CphPipe } from "src/app/cph.pipe";
import { DashboardService } from "../../dashboard.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { DistrinetService } from "src/app/pages/distrinet/distrinet.service";
import { Router } from "@angular/router";
import { Subscription } from "rxjs";
import { SelectionsService } from "src/app/services/selections.service";

import { Chart, ChartConfiguration, ChartDataset, ChartDatasetCustomTypesPerDataset, registerables } from 'chart.js';
Chart.register(...registerables);

const valueOnBarPlugin = {
    id: 'valueOnBarPlugin',
    afterDraw: (chart) => {
      const ctx = chart.ctx;
      ctx.font = '9px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.fillStyle = '#000'; // Text color
  
      chart.data.datasets.forEach((dataset, i) => {
        const meta = chart.getDatasetMeta(i);
        meta.data.forEach((bar, index) => {
          const data = dataset.data[index];
          if (typeof data === 'number' && data > 0) {
            ctx.fillText(data.toString(), bar.x, bar.y - 5);
          }
        });
      });
    }
};

//Chart.register(valueOnBarPlugin);

export interface DailyOrdersVsLastYearParams {
    IsNew: boolean;
    SiteIds: string;
    WeekStart: Date;
    Franchises: string[];
    OrderTypeTypesNew: string[];
    OrderTypeTypesUsed: string[];
}

@Component({
    selector: 'dailyOrdersVsLastYear',
    templateUrl: './dailyOrdersVsLastYear.component.html',
    styleUrls: ['./dailyOrdersVsLastYear.component.scss']
})

export class DailyOrdersVsLastYearComponent implements OnInit {
    @Input() data: SpainDailyNetOrderItem[];
    @Input() tileType: string;
    @Input() dataSource: string;
    @Input() public newDataEmitter: EventEmitter<void>;
    @ViewChild('chartCanvas', { static: true }) chartCanvas: ElementRef;

    chart: any;
    weekStart: Date;
    subscription: Subscription;

    constructor(
        public constants: ConstantsService,
        public selections: SelectionsService,
        public cph: CphPipe,
        public dashboardService: DashboardService,
        public getData: GetDataMethodsService,
        public distrinetService: DistrinetService,
        public router: Router
    ) { }

    ngOnInit(): void {

        this.weekStart = this.constants.deductTimezoneOffset(new Date(this.constants.thisWeekStartDate));
        this.makeChart();

        this.subscription = this.newDataEmitter.subscribe(res => {
          this.makeChart();
        })
    }

    makeChart(){

        if(this.chart)
        {
            this.chart.destroy()
        }

        let labels: (string | Date)[] = this.data.map(x => this.cph.transform(x.DayDate, 'dayDateVertical', 0).split(' '));
        let thisYear: number[] = this.data.map(x => x.OrdersThisYear); 
        let lastYear: number[] = this.data.map(x => x.OrdersLastYear);

        let thisCopy = this;

        const maxDataValue = this.getMaxDataValue(thisYear);

        // Set the y-axis max to be 10% higher than the max data value (stops it overlapping the legend)
        const maxYAxisValue = maxDataValue * 1.1;

        let config: ChartConfiguration = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: this.constants.translatedText.DailyOrdersVsLastYearTile_CurrentYear,
                        backgroundColor: '#FFD966',
                        data: thisYear
                    },
                    {
                        label: this.constants.translatedText.DailyOrdersVsLastYearTile_YearBefore,
                        backgroundColor: '#FF7F27',
                        data: lastYear
                    }
                ],
            },
            plugins: [valueOnBarPlugin],
            options: {
                // layout: {
                //     padding: {
                //       top: 10 // Adjust this value as needed
                //     }
                // },
                maintainAspectRatio: false,
                responsive: true,
                // hover: {
                //     animationDuration: 0
                // },
                plugins: 
                {
                    legend: {
                        position: 'top',
                        
                    },
                    title: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    },
                    datalabels: {
                        display: false
                    },
                },
                scales: {
                    x: 
                    {
                        display: true,
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            font: {
                                size: 9
                            },
                            autoSkip: false,
                            maxRotation: 0,
                            minRotation: 0,
                            // callback: (value, index, values) => {
                            //     let date: string = this.cph.transform(value, 'dayDateVertical', 0);
                            //     return date.split(' ');
                            // }
                        },
                        // scaleLabel: {
                        //     display: false
                        // },
                        // gridLines: {
                        //     drawOnChartArea: false
                        // }
                    },
                    y: {
                        max: maxYAxisValue,
                        display: false
                    }
                },              
                onClick: function (event) {
                    // Assuming 'this' is correctly bound to the chart instance
                    const clickedElements = this.getElementsAtEventForMode(event, 'nearest', { intersect: true }, false);
                  
                    if (clickedElements.length > 0 && thisCopy.tileType === 'New') 
                    {
                      // Removed some of the dead logic here as you cannot filter for data in Distrinet orders 
                      thisCopy.distrinetService.chosenFranchises = thisCopy.dashboardService.franchises;
                      thisCopy.selections.selectedSites = thisCopy.dashboardService.chosenSites;
                      thisCopy.distrinetService.chosenOriginTypes = ['Orders'];
                      thisCopy.router.navigate(['/distrinet']);
                    }
                },
                onHover: function(event, activeElements, chart) {
                    // Access the canvas element directly from the chart instance
                    if (chart.canvas) {
                      chart.canvas.style.cursor = activeElements.length > 0 ? 'pointer' : 'default';
                    }
                },
            },

        };

        let context = this.chartCanvas.nativeElement.getContext('2d');
        this.chart = new Chart(context, config);
        return this.chart;
    }


    getMaxDataValue(dataset) {
        let max = 0;

        dataset.forEach((value) => {
            if (value > max) {
              max = value;
            }
        });

        return max;
    }
      
    changeWeek(days: number) {

        this.weekStart = this.constants.addDays(this.weekStart, days);

        let params: DailyOrdersVsLastYearParams = {
            IsNew: this.tileType == 'New' ? true : false,
            SiteIds: this.dashboardService.chosenSites.map(x => x.SiteId).join(','),
            WeekStart: this.weekStart,
            Franchises: this.dashboardService.franchises,
            OrderTypeTypesNew: this.dashboardService.newOrderTypeTypes.filter(x=>x.isSelected).map(x=>x.label),
            OrderTypeTypesUsed:  this.dashboardService.usedOrderTypeTypes.filter(x=>x.isSelected).map(x=>x.label),
        }
        
        this.getData.getDailyOrdersVsLastYear(params).subscribe((res: SpainDailyNetOrderItem[]) => {
            res.map(x => x.DayDate = this.constants.deductTimezoneOffset(new Date(x.DayDate)));
            this.data = res;
            this.chart.destroy();
            this.makeChart();
          }, error => {
            console.error("Failed to load daily orders vs last year: ", error);
          }, () => {
      
          });
    }
}