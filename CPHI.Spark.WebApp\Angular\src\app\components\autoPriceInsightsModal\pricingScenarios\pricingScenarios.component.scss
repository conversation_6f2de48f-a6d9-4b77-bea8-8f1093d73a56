table {
    width: 100%;
    table-layout: fixed;

    th {
        text-align: right;
        font-weight: 400 !important;
    }

    th:nth-of-type(1),
    td:nth-of-type(1) {
        width: 50%;
        white-space: nowrap;
    }

    td:nth-of-type(2),
    td:nth-of-type(3) {
        text-align: right;
    }

    tr.highlightRow td {
        font-weight: 700;
        background: var(--grey95);
    }

    span.underline {
        border-bottom: 1px solid;
    }

    span.underline-top-bottom {
        display: inline-block;
        border-top: 1px solid;
        border-bottom: 2px solid;
        width: 75%;
    }
}

// .noAnalysisIndicator {
//     background-color: var(--grey90);
//     width: 90px;
//     
//     border-radius: 8px 0 8px 0;
//     //font-weight: 700;
//     height: auto;
//     text-align: center;
// }

#optOutTable {
    margin-top: 3em;
}

#updatePriceTable {
    margin-top: 2em;
}

#newPriceInput {
    border: 1px solid var(--grey80)
}

#inputAndGo {
    display: flex;

    input {
        max-width: 75%;
        border-radius: 5px 0 0 5px;

        &.preventSave {
            border-color: var(--danger);
            background-color: var(--dangerLightest);
        }
    }

    button {
        border-radius: 0 0.375em 0.375em 0;
    }
}


.priceIndicatorImage {
    height: 21px;
}

.floatRight{
    float:right;
}