CREATE OR ALTER PROCEDURE [autoprice].[GET_AutoPriceChangesNew]
(
	@RetailerSiteIds nvarchar(MAX),
	@chosenDate Date,
    @includeNewVehicles bit,
    @includeSmall<PERSON>hang<PERSON> bit,
	
	@includeUnPublishedAds bit,
	
	@includeUnPublishedAdBasedOnSiteSetting bit,
	@includeNewVehiclesBasedOnSiteSetting bit,
	@includeLifecycleStatusesBasedOnSiteSetting bit,
	@filterDaysInStockBasedOnSiteSetting bit,
	@includeVehicleTypesBasedOnSiteSetting bit,
	
	@dealerGroupId int
)

  
AS  
BEGIN  


IF @chosenDate IS NULL 
BEGIN SET @chosenDate = CONVERT(date,getDate())
END

SELECT Value as Id INTO #chosenRetailerSite from STRING_SPLIT(@retailerSiteIds,',') ;

----------------------------------------------
-- Sub table for latest snapshots
----------------------------------------------
SELECT MAX(r.Id) as Id
INTO #veryLatestSnapshotIds
FROM autoprice.VehicleAdvertSnapshots r
INNER JOIN autoprice.VehicleAdverts ads on ads.id = r.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.id = ads.RetailerSite_Id
WHERE CONVERT(date,r.SnapshotDate) = @chosenDate
AND rs.DealerGroup_Id = @dealerGroupId
AND rs.IsActive = 1

GROUP BY r.VehicleAdvert_Id;


----------------------------------------------
--Sub table for optouts in play
----------------------------------------------
SELECT
ads.Id as AdId,
opts.id as OptOutId,
opts.CreatedDate,
opts.ActualEndDate,
ROW_NUMBER() OVER (PARTITION BY ads.Id ORDER BY opts.Id desc) AS RowNumber
INTO #optOutsInPlay
FROM autoprice.VehicleOptOuts opts 
INNER JOIN autoprice.VehicleAdverts  ads on opts.VehicleAdvert_Id = ads.id 
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.VehicleAdvert_Id = ads.id
INNER JOIN #veryLatestSnapshotIds latests on latests.id = snaps.Id
;	

----------------------------------------------
--COMMENTS
----------------------------------------------
WITH latestAds AS
(
	SELECT ads.id
	FROM autoprice.VehicleAdverts ads
	INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.VehicleAdvert_Id = ads.id
	INNER JOIN #veryLatestSnapshotIds v ON v.Id = snaps.id
)
SELECT
comm.Text as LastCommentText,
p.Name as LastCommentName,
comm.Date,
comm.VehicleAdvert_Id as AdvertId,
ROW_NUMBER() OVER (PARTITION BY la.Id ORDER BY comm.Date desc ) AS RowNumber 

INTO #latestComments
FROM autoprice.VehicleAdvertComments comm
INNER JOIN latestAds la on la.Id = comm.VehicleAdvert_Id
INNER JOIN people p on p.Id = comm.Person_Id
WHERE comm.IsRemoved = 0


----------------------------------------------
-- FINAL QUERY
----------------------------------------------
SELECT
prices.DateConfirmed,
rs.LifecycleStatusDefaults,
DATEADD(HOUR, rs.WhenToActionChangesEachDay, CAST(@chosenDate as DateTime)) AS WhenToActionChangesEachDay,
rs.LifecycleStatusDefaults, --remove
rs.Name AS RetailerName,
rs.Id as RetailerSiteId,
ads.VehicleReg,
ads.Make,
ads.Model,
prices.Id as PriceChangeId,
ads.Derivative,
ads.WebsiteStockIdentifier,
ads.WebSiteSearchIdentifier,
ads.StockNumber,
DATEDIFF(day,IIF(ads.DateOnForecourt < ads.CreatedInSparkDate,ads.DateOnForecourt, ads.CreatedInSparkDate),CONVERT(date,getDate())) AS DaysListed,
DATEDIFF(day, ISNULL(st.StockDate,ads.DateOnForecourt), CONVERT(date,@chosenDate)) AS DaysInStock,
prices.WasPrice,
snaps.PriceIndicatorRatingAtCurrentSelling,
snaps.DaysToSellAtCurrentSelling,
snaps.ValuationMktAvRetail,
snaps.ValuationAdjRetail AS ValuationAdjustedRetail,
snaps.RetailRating,
snaps.OurPPRank,
snaps.OurValueRank, 
snaps.StrategyPrice as NewPrice,
prices.DaysToSell as NewDaysToSell, 
prices.PriceIndicator as NewPriceIndicatorRating,
IIF(opts1.OptOutId IS NOT NULL,1,0) as IsOptedOutOnDay,
IIF(
	(
		(prices.NowPrice - COALESCE(prices.WasPrice, 0)) >= rs.MinimumAutoPriceIncrease AND 
			(
				COALESCE(prices.WasPrice, 0) = 0 OR
				(
					COALESCE(prices.WasPrice, 0) <> 0 AND
					(prices.NowPrice / NULLIF(COALESCE(prices.WasPrice, 0),0)) -1 >= rs.MinimumAutoPricePercentIncrease
				)
			)
	)
			OR
		(
			(prices.NowPrice - COALESCE(prices.WasPrice, 0)) <= rs.MinimumAutoPriceDecrease AND 
			(
				COALESCE(prices.WasPrice, 0) = 0 OR
				(
					COALESCE(prices.WasPrice, 0) <> 0 AND
					(prices.NowPrice / NULLIF(COALESCE(prices.WasPrice, 0),0)) -1 <= rs.MinimumAutoPricePercentDecrease
				)
			)
		)
		,
		0,1
		) as IsSmallChange,
		IIF(prices.NowPrice - COALESCE(prices.WasPrice, 0) > 0,1,0) as IsIncrease,
ads.OwnershipCondition,
prices.ApprovedDate,
prices.ApprovedBy_Id as ApprovedById,
prices.ApproverHasBeenEmailed,
prices.DateConfirmed as PriceChangedDate,
ads.Id as AdvertId,
snaps.StrategyPrice,
CONCAT(lc.LastCommentText,' - ',lc.LastCommentName,' ',FORMAT(lc.Date, 'dd MMM yy')) as LastComment,
vt.Description as VehicleTypeDesc


FROM autoprice.PriceChangeAutoItems prices
INNER JOIN autoprice.VehicleAdvertSnapshots snaps on snaps.id = prices.VehicleAdvertSnapshot_Id
INNER JOIN autoprice.VehicleAdverts ads on ads.id = snaps.VehicleAdvert_Id
INNER JOIN autoprice.RetailerSites rs on rs.Id = ads.RetailerSite_Id
LEFT JOIN #optOutsInPlay opts1 on opts1.AdId = ads.Id
AND opts1.CreatedDate > prices.CreatedDate
AND opts1.ActualEndDate > getDate()
AND prices.DateSent IS NULL
AND opts1.RowNumber = 1
AND rs.IsActive = 1

LEFT JOIN stocks st on st.id = ads.Stock_Id
LEFT JOIN vehicleTypes vt on vt.id = st.VehicleType_Id
LEFT JOIN #latestComments lc on lc.AdvertId = ads.Id AND lc.RowNumber = 1
INNER JOIN #chosenRetailerSite crs on crs.Id = rs.Id

WHERE 
CONVERT(date,prices.CreatedDate) = @chosenDate
AND CouldGenerateNewPrice = 1
--AND rs.UpdatePricesAutomatically = 1
--AND
--(
--	(
--		NOT (ads.StockNumber = 'Unknown' AND ads.VehicleReg IS NULL)
--		AND ads.StockNumber NOT LIKE '%/0'
--	)
--	OR @includeNewVehicles = 1
--)

AND 
(
    (
        (
            (prices.NowPrice - COALESCE(prices.WasPrice, 0)) >= rs.MinimumAutoPriceIncrease AND
            (
				COALESCE(prices.WasPrice, 0) = 0 OR
				(
					COALESCE(prices.WasPrice, 0) <> 0 AND 
					(prices.NowPrice / NULLIF(COALESCE(prices.WasPrice, 0),0)) - 1 >= rs.MinimumAutoPricePercentIncrease
				)
			)
        )
        OR
        (
            (prices.NowPrice - COALESCE(prices.WasPrice, 0)) <= rs.MinimumAutoPriceDecrease AND
            (
				COALESCE(prices.wasPrice, 0) = 0 OR
				(
					COALESCE(prices.WasPrice, 0) <> 0 AND
					(prices.NowPrice / NULLIF(COALESCE(prices.WasPrice, 0),0)) - 1 <= rs.MinimumAutoPricePercentDecrease
				)
			)
        )
    )
    OR @includeSmallChanges = 1
)


--restrict for published
AND
(
	ads.AutotraderAdvertStatus = 'PUBLISHED' OR 
	@includeUnPublishedAds = 1 OR 
	(
		@includeUnPublishedAdBasedOnSiteSetting = 1 AND
		rs.IncludeUnPublishedAds = 1
	)
)

--restrict for new vehicle
AND
(
	 @includeNewVehicles = 1 
	 
	 OR
	
	(
		@includeNewVehiclesBasedOnSiteSetting = 1
		AND
		rs.ShowNewVehicles = 1
	) 
	
	OR
	ads.OwnershipCondition = 'Used' 
)

--restrict for vehicle type
AND
(
	--1=1 OR
	@includeVehicleTypesBasedOnSiteSetting = 0 OR --we don't worry about this constraint
	rs.DefaultVehicleTypes IS NULL OR  --these are not set for this site
	vt.Description = ANY (SELECT Value FROM STRING_SPLIT(rs.DefaultVehicleTypes,',')) --any match here
)

--restrict for lifecycle statuses
AND
(
	--1=1 OR
	@includeLifecycleStatusesBasedOnSiteSetting = 0 OR --we don't worry about this constraint
	rs.LifecycleStatusDefaults IS NULL OR  --these are not set for this site
	snaps.LifecycleStatus = ANY (SELECT Value FROM STRING_SPLIT(rs.LifecycleStatusDefaults,',')) --any match here
)

--restrict for days in stock
AND
(
	@filterDaysInStockBasedOnSiteSetting = 0 OR  --we don't worry about this constraint
	rs.MorningReportDISUpTo = 0 OR
	rs.MorningReportDISUpTo IS NULL OR  --not set for this site
	DATEDIFF(day, ISNULL(st.StockDate,ads.DateOnForecourt), CONVERT(date,getDate())) <= rs.MorningReportDISUpTo --or it's low enough
)

AND rs.DealerGroup_Id = @dealerGroupId


DROP TABLE #chosenRetailerSite
DROP TABLE #veryLatestSnapshotIds
DROP TABLE #latestComments
DROP TABLE #optOutsInPlay

END
GO

