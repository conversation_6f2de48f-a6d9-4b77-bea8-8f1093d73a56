import { StrategyFactorItemVM } from "./StrategyFactorItemVM";
import { AutotraderService } from "../services/autotrader.service";
import { StrategyFactorName } from "./StrategyFactorName";
import { RetailerSite } from "./AutoPrice.model";


export class StrategyFactorVM {

   Id?: number;
   Name: StrategyFactorName;
   StrategyFactorItems: StrategyFactorItemVM[];
   horizontalBandLabels?: StrategyFactorHorizontalBand[];

   constructor(type: StrategyFactorName, retailerSites: RetailerSite[], existingFactor: StrategyFactorVM) {

      if (existingFactor != null) {

         this.Id = existingFactor.Id;
         this.Name = existingFactor.Name;
         this.StrategyFactorItems = existingFactor.StrategyFactorItems;
      } else {
         //we are constructing a new one
         this.createNew(type, retailerSites);

      }

   }



   private createNew(type: StrategyFactorName, retailerSites: RetailerSite[]) {
      this.Id = null;
      this.Name = type;
      this.StrategyFactorItems = [];


      switch (type) {
         case StrategyFactorName.RetailRatingBand:
            this.StrategyFactorItems = AutotraderService.buildRetailRatingFactorItems();
            break;
            case StrategyFactorName.PerformanceRatingScore:
         this.StrategyFactorItems = AutotraderService.buildPerformanceRatingItems();
            break;
         case StrategyFactorName.DaysListedBand:
            this.StrategyFactorItems = AutotraderService.buildDaysListedBandFactorItems();
            break;

         case StrategyFactorName.DaysInStockBand:
            this.StrategyFactorItems = AutotraderService.buildDaysInStockBandFactorItems();
            break;

         case StrategyFactorName.Mileage:
            this.StrategyFactorItems = AutotraderService.buildMileageBandFactorItems();
            break;

         case StrategyFactorName.DaysListed:
         case StrategyFactorName.DaysInStock:
            this.StrategyFactorItems = AutotraderService.buildDaysFactorItems();
            break;

         case StrategyFactorName.OnBrandCheck:
            this.StrategyFactorItems = AutotraderService.buildOnBrandCheckFactorItems();
            break;

         case StrategyFactorName.RetailerName:
            const values = retailerSites.map(x => x.Name);
            values.forEach(val => {
               this.StrategyFactorItems.push(new StrategyFactorItemVM(null, val, 100));
            });
            break;

         case StrategyFactorName.SpecificColour:
         case StrategyFactorName.AgeAndOwners:
         case StrategyFactorName.MakeFuelType:
         case StrategyFactorName.MakeAgeBand:
            this.StrategyFactorItems.push(new StrategyFactorItemVM(null, '', 100));
            break;

         case StrategyFactorName.MatchCheapestCompetitor:
         case StrategyFactorName.AchieveMarketPositionScore:
            const ranking = type === StrategyFactorName.MatchCheapestCompetitor ? 2 : 60;
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, 'Radius', 50, null),
               new StrategyFactorItemVM(null, 'Ranking', ranking, null),
               new StrategyFactorItemVM(null, 'PlateSteps', 1, null),
               new StrategyFactorItemVM(null, 'MileageSteps', 30000, null),
               Object.assign(new StrategyFactorItemVM(null, 'Independent', 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, 'Franchise', 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, 'Supermarket', 0, null), { BoolValue: true }),
               Object.assign(new StrategyFactorItemVM(null, 'Private', 0, null), { BoolValue: false })
            ];
            break;

         case StrategyFactorName.RR_DL_Matrix:
         case StrategyFactorName.RR_DS_Matrix:
         case StrategyFactorName.RR_DB_Matrix:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, '20', 0, [{ id: null, value: 100 }, { id: null, value: 99 }, {
                  id: null,
                  value: 98
               }]),
               new StrategyFactorItemVM(null, '40', 0, [{ id: null, value: 101 }, { id: null, value: 100 }, {
                  id: null,
                  value: 99
               }]),
               new StrategyFactorItemVM(null, '60', 0, [{ id: null, value: 102 }, { id: null, value: 101 }, {
                  id: null,
                  value: 100
               }]),
               new StrategyFactorItemVM(null, '80', 0, [{ id: null, value: 103 }, { id: null, value: 102 }, {
                  id: null,
                  value: 101
               }]),
               new StrategyFactorItemVM(null, '100', 0, [{ id: null, value: 104 }, { id: null, value: 103 }, {
                  id: null,
                  value: 102
               }])
            ];
            this.horizontalBandLabels = [{ value: 20 }, { value: 40 }, { value: 999 }];
            break;

         case StrategyFactorName.DTS_DL_Matrix:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, '20', 0, [{ id: null, value: 100 }, { id: null, value: 99 }, {
                  id: null,
                  value: 98
               }]),
               new StrategyFactorItemVM(null, '40', 0, [{ id: null, value: 101 }, { id: null, value: 100 }, {
                  id: null,
                  value: 99
               }]),
               new StrategyFactorItemVM(null, '60', 0, [{ id: null, value: 102 }, { id: null, value: 101 }, {
                  id: null,
                  value: 100
               }]),
               new StrategyFactorItemVM(null, '90', 0, [{ id: null, value: 103 }, { id: null, value: 102 }, {
                  id: null,
                  value: 101
               }])
            ];
            this.horizontalBandLabels = [
               { value: 20 },
               { value: 40 },
               { value: 999 }
            ];
            break;

         case StrategyFactorName.ValuationChangeUntilSell:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, 'ValuationChangeUntilSell', 100)];
            break;

         case StrategyFactorName.MinimumProfit:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, 'Minimum Profit', 1000)];
            break;

         case StrategyFactorName.RoundToNearest:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, 'Round to', 50)];
            break;

         case StrategyFactorName.RoundToPriceBreak:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, 'Round to Price Break', 50)];
            break;

         case StrategyFactorName.DaysToSell:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, 'DaysToSell', 30)];
            break;

         case StrategyFactorName.WholesaleAdjustment:
            this.StrategyFactorItems = [
               new StrategyFactorItemVM(null, 'AdjustmentPct', 0),
               new StrategyFactorItemVM(null, 'AdjustmentValue', 0)
            ];
            break;


         case StrategyFactorName.RetailRating10sBand:
            this.StrategyFactorItems = AutotraderService.buildRetailRating10sFactorItems();
            break;

         case StrategyFactorName.Brand:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, '', 100)];
            break;
         case StrategyFactorName.ModelName:
            this.StrategyFactorItems = [new StrategyFactorItemVM(null, '', 100)];
            break;

         default:
            this.StrategyFactorItems = [];
            break;
      }


   }

   limitMaximumFactorItem() {
      if (this.Name === StrategyFactorName.DaysListed) {
         const penultimateItem = this.StrategyFactorItems[this.StrategyFactorItems.length - 2];
         let finalItemStart = 0;
         if (penultimateItem) {
            try {
               finalItemStart = parseInt(penultimateItem.Label.split('-')[1]) + 1;
            } catch (error) {
            }
         }
         finalItemStart = Math.min(finalItemStart, 999);
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = `${finalItemStart}-999`;
      }
      if (this.Name === StrategyFactorName.PerformanceRatingScore) {
         const penultimateItem = this.StrategyFactorItems[this.StrategyFactorItems.length - 2];
         let finalItemStart = 0;
         if (penultimateItem) {
            try {
               finalItemStart = parseInt(penultimateItem.Label.split('-')[1]) + 1;
            } catch (error) {
            }
         }
         finalItemStart = Math.min(finalItemStart, 99);
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = `${finalItemStart}-99`;
      }
      if (this.Name === StrategyFactorName.DaysInStock) {
         const penultimateItem = this.StrategyFactorItems[this.StrategyFactorItems.length - 2];
         let finalItemStart = 0;
         if (penultimateItem) {
            try {
               finalItemStart = parseInt(penultimateItem.Label.split('-')[1]) + 1;
            } catch (error) {
            }
         }
         finalItemStart = Math.min(finalItemStart, 999);
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = `${finalItemStart}-999`;
      }
      if (this.Name === StrategyFactorName.RR_DL_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = '99';
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999
         }
      }
      if (this.Name === StrategyFactorName.RR_DS_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Value = 99;
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999
         }
      }
      if (this.Name === StrategyFactorName.RR_DB_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Value = 99;
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999
         }
      }
      if (this.Name === StrategyFactorName.DTS_DL_Matrix) {
         this.StrategyFactorItems[this.StrategyFactorItems.length - 1].Label = '999';
         if (this.horizontalBandLabels) {
            this.horizontalBandLabels[this.horizontalBandLabels.length - 1].value = 999
         }
      }
   }


   getCheapestCompetitorFactorItem(itemLabel: string) {
      if (this.Name != "MatchCheapestCompetitor") {
         return null;
      }
      return this.StrategyFactorItems.find(item => item.Label === itemLabel);
   }

   getAchieveMarketPositionScoreFactorItem(itemLabel: string) {
      if (this.Name != "AchieveMarketPositionScore") {
         return null;
      }
      return this.StrategyFactorItems.find(item => item.Label === itemLabel);
   }

   getWholesaleAdjustmentFactorItem(itemLabel: string) {
      if (this.Name != "WholesaleAdjustment") {
         return null;
      }
      return this.StrategyFactorItems.find(item => item.Label === itemLabel);
   }




}

export interface StrategyFactorHorizontalBand {
   value: number
}



