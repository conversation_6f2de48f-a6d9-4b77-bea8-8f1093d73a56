import { Component, EventEmitter, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { SelectionsService } from 'src/app/services/selections.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SimplePickerItem } from 'src/app/components/pickerSimple.component';
import { ConstantsService } from 'src/app/services/constants.service';
import { DashboardDataPackSpainOverview, DashboardDataSpainOverviewParams } from '../../dashboard.model';
import { DashboardService } from '../../dashboard.service';
import { DashboardPageNew } from '../../dashboard.component';

@Component({
  selector: 'dashboardOverviewSpain',
  templateUrl: './dashboardOverviewSpain.component.html',
  styleUrls: ['./dashboardOverviewSpain.component.scss']
})

export class SpainKPIsComponent implements OnInit {
  dataPack: DashboardDataPackSpainOverview;
  weekStart: Date;
  sub: Subscription;
  newDataEmitter: EventEmitter<void>;


  constructor(
    public constants: ConstantsService,
    public service: DashboardService,
    public selections: SelectionsService,
    public getDataService: GetDataMethodsService,
  ) { }

  ngOnDestroy() {
    if (!!this.sub) this.sub.unsubscribe();
  }

  ngOnInit() {
    this.initParams();
    this.getData();

    this.sub = this.service.getNewDataTrigger.subscribe(res => {
      this.getData();
    })
  }

  initParams() {
    this.newDataEmitter = new EventEmitter();
    this.weekStart = this.constants.startOfThisWeek();
   

    let newTypes: SimplePickerItem[] = [];
   
    let uniqueTypes = ['Particulares','Vehiculo concesión','Flotas','Flotas de proximidad']
    uniqueTypes.forEach(x => {
      newTypes.push({ label: x, isSelected: true });
    })

    this.service.newOrderTypeTypes = newTypes;


    let usedTypes: SimplePickerItem[] = [];
    let usedOrderTypeUniqueTypes = ['Particulares','Compraventas'] //just hard typed for now
    usedOrderTypeUniqueTypes.forEach(x => {
      usedTypes.push({ label: x, isSelected: true });
    })

    this.service.usedOrderTypeTypes = usedTypes;    
    
  }

  getData() {
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Loading });

    let parms: DashboardDataSpainOverviewParams = {
      SiteIds: this.service.chosenSites.map(x => x.SiteId).join(','),
      MonthStart:this.service.chosenMonthStart,
      OrderTypeTypesNew: this.service.newOrderTypeTypes.filter(x=>x.isSelected).map(x=>x.label),
      OrderTypeTypesUsed:  this.service.usedOrderTypeTypes.filter(x=>x.isSelected).map(x=>x.label),
      FranchiseCodes: this.service.franchises,
      WeekStart: this.constants.thisWeekStartDate
    }

    this.getDataService.getDashboardDataSpainOverview(parms).subscribe((res: DashboardDataPackSpainOverview) => {

      if (!this.dataPack) {
        this.dataPack = res;
        this.service.dataOriginUpdates = this.dataPack.DataOrigins;
        
      } else {
        Object.assign(this.dataPack, res)
      }

      //in order for the template to update the object it passes through the input tag to the child tile.   else the child tile regenerates its data again from the old dataPack
      setTimeout(() => {
        this.newDataEmitter.emit();
        this.selections.triggerSpinner.next({ show: false });
      }, 50)
    })
  }

  saveTypesAndReload(orderTypes: SimplePickerItem[], isNew?: boolean) {
  
    
    if (isNew) {
      this.service.newOrderTypeTypes.map(x => {
        x.isSelected = orderTypes.map(x=>x.label).includes(x.label)
      })
    } else {
      this.service.usedOrderTypeTypes.map(x => {
        x.isSelected = orderTypes.map(x=>x.label).includes(x.label)
      })
  
    }

    this.getData();
  }

  choosePage(page:DashboardPageNew){
    this.service.chosenPage = page;
    this.service.singleLineNav = page.pageName.includes('dashboard');
  }
  
}
