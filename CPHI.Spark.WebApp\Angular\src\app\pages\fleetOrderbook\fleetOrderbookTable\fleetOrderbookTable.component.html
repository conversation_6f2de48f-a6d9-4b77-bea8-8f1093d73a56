<div id="gridHolder" #gridHolder>
  <div id="gridHeader">
    <statusBar (excelExportClick)="excelExport()" [gridColumnApi]="service.tableLayoutManagement.gridColumnApi" [gridApi]="service.tableLayoutManagement.gridApi" [gridOptions]="gridOptions">
    </statusBar>

    <tableLayoutManagement *ngIf="service.tableLayoutManagement.gridApi" ></tableLayoutManagement>
    <div class="searchBox autoHeight">
      <div class="searchBoxIconContainer">
        <i class="searchBoxIcon fas fa-search"></i>
      </div>
      <form>
        <input placeholder="{{ constants.translatedText.Search }}" class="form-control ml-2" type="text"
          [formControl]="service.searchTerm" />
        <div *ngIf="!!service.searchTerm.value" (click)="clearSearchTerm()" id="searchBarClearButton">
          <i class="fas fa-times-circle"></i>
        </div>
      </form>
    </div>
    <!-- <div (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()" [ngStyle]="{'width': '2em'}">
    </div> -->
  </div>

 



  <ag-grid-angular class="ag-theme-balham" [components]="components" [gridOptions]="gridOptions"> </ag-grid-angular>

</div>

