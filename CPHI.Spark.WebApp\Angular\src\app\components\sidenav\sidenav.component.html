<div *ngIf="!keepMenuFixed" id="menuToggle" class="h4" (mouseenter)="makeMenuWide()"
    (mouseleave)="maybeHideMenu($event)">
    <i class="fas fa-bars"></i>
</div>

<div id="menuTrigger" (mouseenter)="makeMenuWide()"></div>

<menu id="sidenav" [ngClass]="{'wide': menuIsWide}" (mouseenter)="amHoveringSideMenu = true" (mouseleave)="maybeHideMenu($event)">
    <div *ngIf="showFixMenuToggle()" id="fixMenuButtonContainer" (click)="fixMenu()">
        
        <!-- The little button to keep menu fixed -->
        <div id="fixMenuButton">
            <div [ngClass]="{ 'rotate': keepMenuFixed }">
                <i class="fas fa-angles-right"></i>
            </div>
        </div>
    </div>
    
    <div id="menuToggle" class="h4" (mouseenter)="makeMenuWide()"
        (mouseleave)="maybeHideMenu($event)">
        <i class="fas fa-bars"></i>
    </div>

    <!-- <div *ngIf="menuIsWide" id="userDetails">
        <div class="d-flex align-items-center">
            <div id="profileImageContainer">
                <profilePic class="profilePic pointer"></profilePic>
            </div>
            <h4>{{ selections.user.Name }}</h4>
        </div>
    </div> -->

    <!-- Nested -->
    <!-- *ngIf="constants.environment.showNestedSideMenu" -->
    <div  id="menuItemsContainer">
        <ng-container *ngFor="let menuSection of constants.menuSections">
            
            
            <!-- *ngIf="menuItem.visible" -->
            <!-- [ngClass]="{ 'active': (menuItem.isActive || routeIsActive(menuItem)) && !menuItem.subItems, 'hasSubItems': menuItem.subItems }" -->
            
            
            
            <!-- ------------------------------------- -->
            <!-- The menu section -->
            <!-- ------------------------------------- -->
            <button  type="button" class="btn btn-primary menuItem"
            (click)="onMenuSectionClicked($event, menuSection)" [attr.aria-expanded]="!menuSection.expanded" aria-controls="collapseExample">
            <div class="iconAndMenuItemName">
                <ng-container *ngIf="menuIsWide">
                    <span *ngIf="!menuSection.expanded">
                        <i class="menuIcon fal fa-angle-right"></i>
                    </span>
                    <span *ngIf="menuSection.expanded">
                        <i class="menuIcon fal fa-angle-down"></i>
                    </span>
                    <span *ngIf="showMenuLabels">{{ menuSection.name }}</span>    
                </ng-container>
                <!-- <div *ngIf="!menuIsWide" class="menuItemsAbbreviated">{{ menuItem.nameAbbreviated }}</div> -->
            </div>
        </button>
        
        
        <!-- ------------------------------------- -->
        <!-- The actual buttons -->
        <!-- ------------------------------------- -->
            <div #collapse="ngbCollapse" [(ngbCollapse)]="!menuSection.expanded">
                <ng-container *ngFor="let subItem of menuSection.subItems">
                    <button *ngIf="subItem.visible" class="btn btn-primary menuItem subMenuItem"
                        [ngClass]="{ 'sideMenuCollapsed': !menuIsWide, 'active': subItem.isActive }"
                        (click)="onMenuItemClicked($event, subItem)">
                        <div *ngIf="menuIsWide" class="iconAndMenuItemName">
                            <i class="menuIcon" [ngClass]="subItem.icon"></i>
                            <span *ngIf="menuIsWide && showSubMenuLabels">{{ subItem.name }}</span>
                        </div>
                        <div *ngIf="!menuIsWide" class="menuItemsAbbreviated">{{ subItem.nameAbbreviated }}</div>
                    </button>
                </ng-container>
            </div>
        </ng-container>
    </div>


    <!-- Not nested -->
    <!-- <div *ngIf="!constants.environment.showNestedSideMenu" id="menuItemsContainer">
        <ng-container *ngFor="let menuItem of constants.menuNew">
            <button *ngIf="!menuItem.subItems && menuItem.visible" class="btn btn-primary menuItem notNested"
                [ngClass]="{ 'active': menuItem.isActive }" (click)="onMenuItemClicked($event, menuItem)">
                <div class="iconAndMenuItemName">
                    <i class="menuIcon" [ngClass]="menuItem.icon"></i>
                    <span *ngIf="menuIsWide && showSubMenuLabels">{{ menuItem.name }}</span>
                </div>
            </button>
            <ng-container *ngFor="let subItem of menuItem.subItems">
                <button *ngIf="subItem.visible" class="btn btn-primary menuItem notNested"
                    [ngClass]="{ 'active': subItem.isActive }" (click)="onMenuItemClicked($event, subItem, menuItem.group)">
                    <div class="iconAndMenuItemName">
                        <i class="menuIcon" [ngClass]="subItem.icon"></i>
                        <span *ngIf="menuIsWide && showSubMenuLabels">{{ subItem.name }}</span>
                    </div>
                </button>
            </ng-container>
        </ng-container>
    </div> -->

    <div id="sidenav-footer" class="animated" [class]="{ 'show': menuIsWide }">
        <a href="https://www.cphi.co.uk/">
            <img id="cphi-logo" src="./assets/imgs/cphi-app-logo.png">
        </a>
        <img id="spark-logo" src="./assets/imgs/sparkLogoWhitePng.png">
    </div>
</menu>