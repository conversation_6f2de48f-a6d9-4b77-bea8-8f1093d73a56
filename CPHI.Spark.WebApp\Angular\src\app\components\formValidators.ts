import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function over110Validator(referenceValue: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    return typeof value === 'number' && value > 1.1 * referenceValue
      ? { tooHigh: true }
      : null;
  };
}

export function below90Validator(referenceValue: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    return typeof value === 'number' && value < 0.9 * referenceValue
      ? { tooLow: true }
      : null;
  };
}

