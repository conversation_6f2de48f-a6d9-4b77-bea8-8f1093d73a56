
#tableHolder {
   display: flex;
   flex-direction: column;
   flex: 1;
   overflow-y: hidden;
   display: flex;
   flex-direction: column;
}

table.saveTable {
   width: 90%;
   margin: 2em auto;

   svg {
      color: var(--brightColour)
   }

   tr {
      cursor: pointer;
   }
}

table#importFilters {
   width: 100%;
   margin: 2em auto;

   td {
      padding: 0.5em 0;
   }
}

.mapName {
   width: 100%;
   margin: 1em auto;
   line-height: 2em;
}

.topRowButtons {
   display: flex;
   justify-content: space-between;
}

#cancelBulkUpload {
   margin-left: 10em;
}

.uploadFileWrapper {
   display: flex;
   align-items: center;
   height: 100%;
   width: 100%;

   .chooseFileInput {
      width: 0.1px;
      height: 0.1px;
      opacity: 0;
      overflow: hidden;
      position: absolute;
      z-index: -1;
   }

   .chooseFileInput + label {
      // background-color: #323130;
      color: #ffffff;
      padding: 0.375em 0.75em;
      display: inline-block;
      font-weight: 400;
      text-align: center;
      vertical-align: middle;
      margin-bottom: 0;
      // transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
      cursor: pointer;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: transparent;


      fa-icon {
         margin-right: 0.75em;
      }
   }

   .chooseFileInput:focus + label,
   .chooseFileInput + label:hover {
      // background-color: var(--brightColourDark);
      color: #323130;
   }

   .fileName {
      margin-right: 0.75em;
   }
}

#saveMask {
   border: none;
   border-radius: 0;

   fa-icon {
      margin-right: 0.75em;
   }
}

.upload {
   border-radius: 0;
   border: none;
   margin-right: 1em !important;
   margin-left: 2em !important;

   fa-icon {
      margin-right: 0.75em;
   }
}

.tablesHolder {
   display: flex;
   flex-grow: 1;
   flex: 1 1 auto;
   overflow: hidden;
   flex-direction: column;
}

.upload.disable {
   cursor: default;
   opacity: 0.65;
}

.requiredColumns {
   height: 20px;
   color: var(--danger);
}

bulkUploadTable,
destinationTable {
   flex: 1;
   overflow-y: auto;
}

#newBatchName {
   width: 100%;
   line-height: 2.2em;
   border: 1px solid var(--grey80);
   margin: 2em auto;
}

bulkuploaddetailtable {
   height: 100%;
}

.content-new {
   overflow: hidden;
}

.content-inner-new {
   display: flex;
   flex-direction: column;
}

#excelTableHolderHolder {
   margin-bottom: 1em;
}

.lds-ring {
   display: inline-block;
   position: relative;
   width: 40px;
   height: 40px;
}

.lds-ring div {
   box-sizing: border-box;
   display: block;
   position: absolute;
   width: 24px;
   height: 24px;
   margin: 8px;
   border: 2px solid #fff;
   border-radius: 50%;
   animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
   border-color: #fff transparent transparent transparent;
}

.lds-ring div:nth-child(1) {
   animation-delay: -0.45s;
}

.lds-ring div:nth-child(2) {
   animation-delay: -0.3s;
}

.lds-ring div:nth-child(3) {
   animation-delay: -0.15s;
}

@keyframes lds-ring {
   0% {
      transform: rotate(0deg);
   }
   100% {
      transform: rotate(360deg);
   }
}


.buttonsContainer {
   width: 80%;
    text-align: center;
    flex-direction: row;
    grid-gap: 2em;
    flex-wrap: wrap;
    justify-content: center;
    display: flex;
    padding-top: 200px;
    margin-left: 10%;

   .buttonBlob {
      width: 20em;
      height: 10em;
      display: inline-block;
      padding: 0;

      img {
         width: 80%;
      }
   }

   .halfButtonBlob {
      width: 20em;
      height: 5em;
      display: inline-block;
      padding: 0;

      img {
         width: 80%;
      }
   }
}

app-bulkuploadtable {
   flex: 1;
}


#regMileageConditionTable {
   width: 100%;
   table-layout: fixed;

   td {
      padding: 0.6em;

      input {
         width: 100%;
         line-height: 2.5em;
      }

      &:nth-of-type(2) {
         text-align: right;
      }
   }
}

.regInput {
   background-color: var(--numberPlate);
   border-radius: 5px;
   border: 1px solid;
   text-align: center;
   font-weight: 700;
   font-family: 'NumberPlate';

   line-height: 2.6em;
   text-transform: uppercase;
   float: right;
   padding: 0 1em;
}

.mileageInput {
   background-color: #000000;
   color: #FFFFFF;
   padding: 0;
   border: 1px solid;
   text-align: right;
   letter-spacing: 0.75em;
   float: right;

}
