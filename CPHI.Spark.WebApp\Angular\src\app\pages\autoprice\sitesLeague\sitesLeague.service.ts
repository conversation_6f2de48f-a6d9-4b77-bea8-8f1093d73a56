import {Injectable} from '@angular/core';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {CphPipe} from 'src/app/cph.pipe';

import {GetStatsDashboardParams} from 'src/app/model/GetStatsDashboardParams';
import {StatsDashboard, StatsDashboardDTO} from 'src/app/model/StatsDashboard';
import {ConstantsService} from 'src/app/services/constants.service';
import {GetDataMethodsService} from 'src/app/services/getDataMethods.service';
import {SelectionsService} from 'src/app/services/selections.service';
import {SitesLeagueComponent, StatsSiteDashboardPageComponentType} from './sitesLeague.component';
import {SitesLeagueTableComponent, StatsSiteDashboardTableParams} from './sitesLeagueTable/sitesLeagueTable.component';
import {StatsSiteDashboard, StatsSiteDashboardDTO} from 'src/app/model/StatsSiteDashboard';
import {GetStatsStockProfileItemsParams} from 'src/app/model/GetStatsStockProfileItemsParams';

@Injectable({
  providedIn: 'root'
})
export class StatsSiteDashboardService implements StatsSiteDashboardTableParams {

  chosenSiteNames: Set<string> = new Set<string>();
  sites: StatsSiteDashboard[];

  showDaysListed: boolean = false;
  showByRetailRating: boolean = false;
  showByPerformanceIndicator: boolean = false;
  serviceHasBeenInitialised: boolean = false;

  // For Test Strat
  showTestStrategySlider: boolean = false;
  useTestStrategy: boolean = false;

  //references to components
  showByLowImageCountIndicator = false;

  gridRef: SitesLeagueTableComponent;
  pageRef: SitesLeagueComponent;


  constructor(
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public modalService: NgbModal,
    public getDataMethodsService: GetDataMethodsService,
    public cph: CphPipe
  ) {


  }


  get siteNames(): string[] {
    return this.constantsService.RetailerSites.filter(x => x.IsActive).map(x => x.Name);
  };


  initParams(): void {


    if (!this.serviceHasBeenInitialised) {
      this.chosenSiteNames = new Set<string>(this.siteNames)  //initially set to all
      this.serviceHasBeenInitialised = true;
    }

  }

  async getData() {

    try {
      const parms: GetStatsDashboardParams = {
        RetailerSiteIds: this.constantsService.RetailerSites.filter(x => this.chosenSiteNames.has(x.Name)).map(x => x.Id),
        UseTestStrategy: this.useTestStrategy
      };

      const res: StatsSiteDashboardDTO[] = await this.getDataMethodsService.getStatsSitesDashboard(parms);

      const statsDashboard: StatsSiteDashboard[] = res.map(x => new StatsSiteDashboard(x));
      console.log(statsDashboard, "statsDashboard");
      this.dealWithNewData(statsDashboard);

      this.showTestStrategySlider = this.showTestStrategySliderCheck();
      this.selectionsService.triggerSpinner.next({ show: false });

    } catch (error) {
      //console.log(error, "error");
      this.constantsService.toastDanger("Error fetching data");
    }
  }


  //do any triggering of the various components
  dealWithNewData(data: StatsSiteDashboard[]) {
    this.sites = [...data];

    if(this.gridRef)
    {
      this.gridRef.dealWithNewDataSitesLeague(data);
    }
    
  }


  dealWithFilteredItems(filteredItems: StatsSiteDashboard[], callingComponent: StatsSiteDashboardPageComponentType) {
    //todo
  }

  showTestStrategySliderCheck(): boolean {
    return (
      this.constantsService.userRetailerSites.some(site => site.AllowTestStrategy) && // At least one site with it enabled
      this.sites?.some(row => row.TestStrategyPriceAvailable == true) // At least one item with a price
    );
  }


}
