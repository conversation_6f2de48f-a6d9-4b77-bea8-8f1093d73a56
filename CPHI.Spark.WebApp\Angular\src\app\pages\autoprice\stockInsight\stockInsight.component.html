<nav class="navbar">
    <nav class="generic">
        <h4 id="pageTitle">
            Stock Dashboard&nbsp;
            <sourceDataUpdate *ngIf="!constants.environment.showLatestSnapshotDate"
            [chosenDate]="constants.LastUpdatedDates.AutotraderAdverts"></sourceDataUpdate>
        </h4>

        <div *ngIf="constants.environment.sideMenu.bulkValuation" class="buttonGroup ms-5">
            <button class="btn btn-primary active" [ngClass]="{ 'active': !service.viewByBulkUpload }"
                (click)="viewByBulkUpload(false)">
                Today's Stock Vehicles
            </button>
            <!-- <button class="btn btn-primary" [ngClass]="{ 'active': service.viewByBulkUpload }"
                (click)="viewByBulkUpload(true)">
                Bulk Uploaded Vehicle Details
            </button> -->
        </div>

        <input *ngIf="!service.viewByBulkUpload" class="mx-2" type="date" [value]="service.chosenEffectiveDate" (change)="setChosenDate($event)">




        <!-- Include new -->
        <sliderSwitch text="New Vehicles" (toggle)="toggleIncludeNewVehicles()"
            [defaultValue]="service.includeNewVehicles">
        </sliderSwitch>

        <!-- Include unpublished -->
        <sliderSwitch text="Un-published Ads" (toggle)="toggleIncludeUnPublishedAds()"
            [defaultValue]="service.includeUnPublishedAdverts">
        </sliderSwitch>

        <!-- Use Test Strategy (only if they have access) -->
        <sliderSwitch *ngIf="service.showTestStrategySlider" text="Use Test Strategy" (toggle)="toggleUseTestStrategy()" [defaultValue]="service.useTestStrategy"></sliderSwitch>

        <!-- VEhicle Type picker -->
        <multiPickerWithCount *ngIf="constants.autopriceEnvironment.vehicleTypes != null && !service.viewByBulkUpload"
            [label]="'Vehicle Types'" [menuItems]="constants.autopriceEnvironment.vehicleTypes"
            [chosenItems]="service.chosenVehicleTypes" [itemCount]="provideVehTypeCount.bind(this)"
            [onChosenItemsChange]="onChosenVehTypesChange.bind(this)"></multiPickerWithCount>

        <!-- Lifecycle status picker -->
        <multiPickerWithCount [label]="'Lifecycle Statuses'" [menuItems]="service.allLifecycleStatuses"
            [chosenItems]="service.chosenLifecycleStatuses" [itemCount]="provideLifecycleStatusCount.bind(this)"
            [onChosenItemsChange]="onChosenLifecycleStatusChange.bind(this)"></multiPickerWithCount>



        <div *ngIf="service.viewByBulkUpload && service.chosenVehicleValuationBatch" ngbDropdown dropright
            class="d-inline-block">
            <button id="chooseBatchButton" class="buttonGroupCenter btn btn-primary" ngbDropdownToggle>
                Uploaded {{ service.chosenVehicleValuationBatch.LastRunDate | cph:'dateTime':0 }} by
                {{ service.chosenVehicleValuationBatch.LastRunBy }} ({{
                service.chosenVehicleValuationBatch.TotalVehicles }} vehicles)
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                <button *ngFor="let batch of service.vehicleValuationBatches" ngbDropdownToggle
                    class="manualToggleCloseItem" ngbDropdownItem (click)="selectBatch(batch)">
                    Uploaded {{ batch.LastRunDate | cph:'dateTime':0 }} by {{ batch.LastRunBy }} ({{ batch.TotalVehicles
                    }} vehicles)
                </button>
            </div>
        </div>

        <button *ngIf="service.viewByBulkUpload" class="btn btn-success" (click)="maybeRevalueVehicleValuationBatch()">
            Revalue Vehicles
        </button>
    </nav>
</nav>

<div id="overallHolder" class="single-line-nav" [ngClass]="constants.environment.customer">
    <div class="content-new">
        <div class="content-inner-new">


            <!-- Main Page -->
            <div *ngIf="!service.viewByBulkUpload && !!service.rawDataHighlighted" class="dashboard-grid-container">

                <instructionRow
                    [message]="'Click on any blue bar to filter for matching items.  The summary cards will update to show the averages for the chosen items.  Click any vehicle to view.'">
                </instructionRow>
                <div class="d-flex h-100">
                    <div id="leftSide">

                        <div id="leftSideTop">

                            <div class="dashboard-grid cols-8 rows-3">

                                <!-- Total Vehicles -->
                                <div class="dashboard-tile grid-col-1-3 grid-row-1-2 ">

                                    <ng-container
                                        *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Total Vehicles'}"></ng-container>


                                    <div class="contentsHolder ">
                                        <!-- (click)="" -->
                                        <h1 class="bigNumber clickable" id="totalCount" (click)="goToStockReports()">
                                            {{service.summaryStats.vehicleCount|cph:'number':0}}</h1>
                                    </div>

                                </div>




                                <!-- Average Days Listed / Days In Stock-->
                                <div class="dashboard-tile grid-col-3-5 grid-row-1-2">

                                    <ng-container *ngIf="!service.useDaysInStock">
                                        <ng-container
                                            *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Days Listed'}"></ng-container>

                                        <div class="contentsHolder">
                                            <h1 class="bigNumber" id="totalProfit">
                                                {{service.summaryStats.averageDaysListed|cph:'number':0}}</h1>
                                        </div>
                                    </ng-container>

                                    <ng-container *ngIf="service.useDaysInStock">
                                        <ng-container
                                            *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Days In Stock'}"></ng-container>

                                        <div class="contentsHolder">
                                            <h1 class="bigNumber" id="totalProfit">
                                                {{service.summaryStats.averageDaysInStock|cph:'number':0}}</h1>
                                        </div>
                                    </ng-container>

                                </div>



                                <!-- Average retail rating -->
                                <div class="dashboard-tile grid-col-5-7 grid-row-1-2">

                                    <ng-container
                                        *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Retail Rating'}"></ng-container>

                                    <div class="contentsHolder">
                                        <h1 class="bigNumber" id="totalProfit">
                                            {{service.summaryStats.averageRetailRating|cph:'number':0}}</h1>
                                    </div>

                                </div>



                                <!-- Average perf rating -->
                                <div class="dashboard-tile grid-col-7-9 grid-row-1-2">

                                    <ng-container
                                        *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Performance Rating'}"></ng-container>
                                    <div class="contentsHolder">
                                        <h1 class="bigNumber" id="totalProfit">
                                            {{service.summaryStats.averagePerformanceRating
                                            |cph:'number':0}}</h1>
                                    </div>

                                </div>




                                <!-- Average selling price -->
                                <div class="dashboard-tile grid-col-1-3 grid-row-2-3">

                                    <ng-container
                                        *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Selling Price'}"></ng-container>

                                    <div class="contentsHolder">
                                        <h1 class="bigNumber" id="">{{service.summaryStats.averagePrice
                                            |cph:'currency':0}}</h1>
                                    </div>
                                </div>


                                <!-- Price Position -->
                                <div class="dashboard-tile grid-col-3-5 grid-row-2-3">

                                    <ng-container
                                        *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Price Position'}"></ng-container>

                                    <div class="contentsHolder">
                                        <h1 class="bigNumber" id="">{{service.summaryStats.pricePosition
                                            |cph:'percent1dp':0}}
                                        </h1>
                                    </div>
                                </div>

                                <!-- Profit per vehicle tile -->
                                <div class="dashboard-tile grid-col-5-7 grid-row-2-3">

                                    <ng-container
                                        *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Profit / Vehicle'}"></ng-container>

                                    <div class="contentsHolder">
                                        <h1 class="bigNumber" id="totalProfit">{{ service.summaryStats.averageProfit
                                            |cph:'currency':0}}</h1>
                                    </div>

                                </div>


                                <!-- Prep cost -->
                                <div *ngIf="constants.environment.customer !== 'Vindis'"
                                    class="dashboard-tile grid-col-7-9 grid-row-2-3">
                                    <ng-container
                                        *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Prep Cost'}"></ng-container>
                                    <div class="contentsHolder">
                                        <h1 class="bigNumber" id="">{{service.summaryStats.prepCost|cph:'currency':0}}
                                        </h1>
                                    </div>

                                </div>

                                <!-- DIS -->
                                <div *ngIf="constants.environment.customer === 'Vindis'"
                                    class="dashboard-tile grid-col-7-9 grid-row-2-3">
                                    <ng-container
                                        *ngTemplateOutlet="tileHeaderTemplate; context: { title: 'Average Days in Stock' }"></ng-container>
                                    <div class="contentsHolder">
                                        <h1 class="bigNumber">{{ service.summaryStats.averageDaysInStock |
                                            cph:'number':0
                                            }}</h1>
                                    </div>

                                </div>

                            </div>


                        </div>

                        <div id="leftSideBottom" [ngClass]="constants.environment.isSingleSiteGroup ? 'singleSite' : 'multiSite'">

                            <div class="dashboard-grid cols-8 rows-6">


                                <!-- PerformanceRatingScoreBand -->
                                <div class="dashboard-tile grid-col-1-3 grid-row-1-3 ">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Performance Rating'"
                                        [tileType]="'VerticalBar'" [labelWidth]="30"
                                        [pageParams]="service.getPageParams()"
                                        [fieldName]="'PerformanceRatingScoreBand'">
                                    </biChartTile>
                                </div>

                                <!-- Days Listed -->
                                <div class="dashboard-tile grid-col-3-5 grid-row-1-3 ">

                                    <ng-container *ngIf="!service.useDaysInStock">
                                        <biChartTile [dataType]="dataTypes.label" [title]="'Days Listed Band'"
                                            [tileType]="'VerticalBar'" [labelWidth]="30"
                                            [pageParams]="service.getPageParams()" [fieldName]="'DaysListedBand'">
                                        </biChartTile>
                                    </ng-container>
                                    <ng-container *ngIf="service.useDaysInStock">
                                        <biChartTile [dataType]="dataTypes.label" [title]="'Days In Stock Band'"
                                            [tileType]="'VerticalBar'" [labelWidth]="30"
                                            [pageParams]="service.getPageParams()" [fieldName]="'DaysInStockBand'">
                                        </biChartTile>
                                    </ng-container>
                                </div>

                                <!-- Brand  -->
                                <div class="dashboard-tile grid-col-5-7 grid-row-1-3">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Brand'"
                                        [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'SimpleBrand'"
                                        [customSort]="'SimpleBrand'" [pageParams]="service.getPageParams()"
                                        [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- ValueBand  -->
                                <div class="dashboard-tile grid-col-7-9 grid-row-1-3 ">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Value Band'"
                                        [tileType]="'HorizontalBar'" [pageParams]="service.getPageParams()"
                                        [fieldName]="'ValueBand'" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>


                                <!-- RetailerName  -->
                                <div *ngIf="!constants.environment.isSingleSiteGroup" class="dashboard-tile grid-col-1-5 grid-row-3-5">
                                    <biChartTileAutoTrader [dataType]="dataTypes.label"
                                        [title]="constants.translatedText.Common_Site" [tileType]="'VerticalBar'"
                                        [labelWidth]="35" [pageParams]="service.getPageParams()"
                                        [fieldName]="'RetailerSiteName'">
                                    </biChartTileAutoTrader>
                                </div>


                                <!-- SiteBrand  -->
                                <div *ngIf="!constants.environment.isSingleSiteGroup" class="dashboard-tile grid-col-5-7 grid-row-3-5">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Site Brand'"
                                        [tileType]="'VerticalBar'" [labelWidth]="30"
                                        [pageParams]="service.getPageParams()" [fieldName]="'SiteBrand'"
                                        [isAutoTrader]="true"></biChartTile>
                                </div>


                                <!-- AgeBand  -->
                                <div class="dashboard-tile grid-col-7-9 grid-row-3-5">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Age Band'"
                                        [tileType]="'VerticalBar'" [labelWidth]="30"
                                        [pageParams]="service.getPageParams()" [fieldName]="'AgeBand'"
                                        [isAutoTrader]="true"></biChartTile>
                                </div>



                                <!-- Model  -->
                                <div class="dashboard-tile grid-col-1-7"
                                    [ngClass]="constants.environment.isSingleSiteGroup ? 'grid-row-3-5' : 'grid-row-5-7'">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Model'"
                                        [tileType]="'HorizontalBar'" [pageParams]="service.getPageParams()"
                                        [fieldName]="'Model'" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>



                                <!-- RetailRatingBand  -->
                                <div class="dashboard-tile grid-col-7-9 grid-row-5-7 ">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Retail Rating Band'"
                                        [tileType]="'HorizontalBar'" [pageParams]="service.getPageParams()"
                                        [fieldName]="'RetailRatingBand'" [isAutoTrader]="true"></biChartTile>
                                </div>

                                <!-- FuelType -->
                                <div class="dashboard-tile grid-col-1-3"
                                    [ngClass]="constants.environment.isSingleSiteGroup ? 'grid-row-5-7' : 'grid-row-11-13'">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Fuel Type'"
                                        [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'FuelType'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- VehicleType -->
                                <div class="dashboard-tile grid-col-3-5"
                                    [ngClass]="constants.environment.isSingleSiteGroup ? 'grid-row-5-7' : 'grid-row-11-13'">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Vehicle Type'"
                                        [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'VehicleType'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- BodyType -->
                                <div class="dashboard-tile grid-col-5-7"
                                    [ngClass]="constants.environment.isSingleSiteGroup ? 'grid-row-5-7' : 'grid-row-11-13'">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Body Type'"
                                        [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'BodyType'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- Make -->
                                <div class="dashboard-tile grid-col-1-7 grid-row-7-9">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Make'"
                                        [tileType]="'HorizontalBar'" [labelWidth]="40" [fieldName]="'Make'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- PriceIndicator -->
                                <div class="dashboard-tile grid-col-7-9 grid-row-7-9">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Price Indicator'"
                                        [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'PriceIndicatorRatingAtCurrentSelling'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- DaysToSell -->
                                <div class="dashboard-tile grid-col-1-3 grid-row-9-11">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Days to Sell'"
                                        [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'DaysToSellBanding'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- ImagesBand -->
                                <div class="dashboard-tile grid-col-3-5 grid-row-9-11">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Images Count'"
                                    [customSort]="'imagesCount'"
                                    [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'ImagesBand'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- Transmission -->
                                <div class="dashboard-tile grid-col-5-7 grid-row-9-11">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Transmission'"
                                        [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'TransmissionType'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                                <!-- VsStrategy -->
                                <div class="dashboard-tile grid-col-7-9 grid-row-9-11">
                                    <biChartTile [dataType]="dataTypes.label" [title]="'Vs Strategy'"
                                    [customSort]="'VsStrategy'"    
                                        [tileType]="'VerticalBar'" [labelWidth]="40" [fieldName]="'VsStrategyBanding'"
                                        [pageParams]="service.getPageParams()" [isAutoTrader]="true">
                                    </biChartTile>
                                </div>

                            </div>

                        </div>





                    </div>


                    <div id="rightSide" class="dashboard-grid cols-10 rows-9">
                        <!-- Blobs chart -->
                        <div class="dashboard-tile grid-col-1-11 grid-row-1-5">

                            <ng-container
                                *ngTemplateOutlet="tileHeaderTemplate; context: { title: 'Vehicle distribution' }"></ng-container>

                            <blobChart [analysisDimensions]="service.analysisDimensions" [showAxisDropdowns]="true"
                                [updatedBlobItems]="service.newBlobsEmitter"
                                [xAnalysisDimension]="service.xAnalysisDimension"
                                [yAnalysisDimension]="service.yAnalysisDimension" [blobItems]="service.blobItems"
                                [canOpenVehicleAdvertModal]="true" [service]="service">
                            </blobChart>


                        </div>


                        <!-- Vehicle details tile -->
                        <div class="dashboard-tile grid-col-1-11 grid-row-5-9">
                            <div class="tileInner">
                                <div class="tileHeader">
                                    <div class="headerWords">
                                        Vehicle Details
                                    </div>
                                </div>
                                <div *ngIf="service.rawDataHighlighted" class="contentHolder" id="gridHolder"
                                    #gridHolder>
                                    <vehicleDetailsTable [rowData]="service.rawDataHighlighted"></vehicleDetailsTable>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div *ngIf="service.viewByBulkUpload && !!service.vehicleValuationBatchResultsHighlighted"
                class="dashboard-grid-container">

                <instructionRow
                    [message]="'Click on any blue bar to filter for matching items.  The summary cards will update to show the averages for the chosen items.  Click any vehicle to view.'">
                </instructionRow>
                <div id="spain-overview-grid" class="dashboard-grid cols-18">

                    <!-- Total -->
                    <div class="dashboard-tile grid-col-1-3 grid-row-1-2">

                        <ng-container
                            *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Total Vehicles'}"></ng-container>


                        <div class="contentsHolder ">
                            <!-- (click)="" -->
                            <h1 class="bigNumber clickable" id="totalCount">
                                {{ service.summaryStats.vehicleCount|cph:'number':0}}</h1>
                        </div>

                    </div>

                    <!-- Average retail rating -->
                    <div class="dashboard-tile grid-col-3-5 grid-row-1-2">

                        <ng-container
                            *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Retail Rating'}"></ng-container>

                        <div class="contentsHolder">
                            <h1 class="bigNumber" id="totalProfit">
                                {{service.summaryStats.averageRetailRating|cph:'number':0}}</h1>
                        </div>

                    </div>

                    <!-- Average selling price -->
                    <div class="dashboard-tile grid-col-5-7 grid-row-1-2">

                        <ng-container
                            *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Average Selling Price'}"></ng-container>

                        <div class="contentsHolder">
                            <h1 class="bigNumber" id="">{{service.summaryStats.averagePrice |cph:'currency':0}}</h1>
                        </div>
                    </div>

                    <!-- Price position -->
                    <div class="dashboard-tile grid-col-7-9 grid-row-1-2">

                        <ng-container
                            *ngTemplateOutlet="tileHeaderTemplate ;context: {title:'Price Position'}"></ng-container>

                        <div class="contentsHolder">
                            <h1 class="bigNumber" id="">{{service.summaryStats.pricePosition |cph:'percent1dp':0}}
                            </h1>
                        </div>
                    </div>

                    <!-- Make -->
                    <div class="dashboard-tile grid-col-1-7 grid-row-2-4">
                        <biChartTile [dataType]="dataTypes.label" [title]="'Make'" [tileType]="'HorizontalBar'"
                            [pageParams]="service.getPageParams()" [fieldName]="'Make'" [isAutoTrader]="true">
                        </biChartTile>
                    </div>

                    <!-- Age band -->
                    <div class="dashboard-tile grid-col-7-9 grid-row-2-4">
                        <biChartTile [dataType]="dataTypes.label" [title]="'Age Band'" [tileType]="'VerticalBar'"
                            [labelWidth]="30" [pageParams]="service.getPageParams()" [fieldName]="'AgeBand'"
                            [isAutoTrader]="true"></biChartTile>
                    </div>

                    <!-- Model -->
                    <div class="dashboard-tile grid-col-1-7 grid-row-4-6">
                        <biChartTile [dataType]="dataTypes.label" [title]="'Model'" [tileType]="'HorizontalBar'"
                            [pageParams]="service.getPageParams()" [fieldName]="'Model'" [isAutoTrader]="true">
                        </biChartTile>
                    </div>

                    <!-- Fuel -->
                    <div class="dashboard-tile grid-col-7-9 grid-row-4-6">
                        <biChartTile [dataType]="dataTypes.label" [title]="'Fuel'" [tileType]="'VerticalBar'"
                            [labelWidth]="30" [pageParams]="service.getPageParams()" [fieldName]="'FuelType'"
                            [isAutoTrader]="true"></biChartTile>
                    </div>

                    <!-- Value band -->
                    <div class="dashboard-tile grid-col-1-4 grid-row-6-8">
                        <biChartTile [dataType]="dataTypes.label" [title]="'Value Band'" [tileType]="'HorizontalBar'"
                            [pageParams]="service.getPageParams()" [fieldName]="'ValueBand'" [isAutoTrader]="true">
                        </biChartTile>
                    </div>

                    <!-- Retail rating band -->
                    <div class="dashboard-tile grid-col-4-7 grid-row-6-8">
                        <biChartTile [dataType]="dataTypes.label" [title]="'Retail Rating Band'"
                            [tileType]="'HorizontalBar'" [pageParams]="service.getPageParams()"
                            [fieldName]="'RetailRatingBand'" [isAutoTrader]="true"></biChartTile>
                    </div>

                    <!-- Colour -->
                    <div class="dashboard-tile grid-col-7-9 grid-row-6-8">
                        <biChartTile [dataType]="dataTypes.label" [title]="'Colour'" [tileType]="'VerticalBar'"
                            [labelWidth]="30" [pageParams]="service.getPageParams()" [fieldName]="'Colour'"
                            [isAutoTrader]="true"></biChartTile>
                    </div>

                    <!-- Blobs chart -->
                    <div class="dashboard-tile grid-col-9-19 grid-row-1-4">

                        <ng-container
                                *ngTemplateOutlet="tileHeaderTemplate; context: { title: 'Vehicle distribution' }"></ng-container>

                        <blobChart [analysisDimensions]="service.analysisDimensions" [showAxisDropdowns]="true"
                            [updatedBlobItems]="service.newBlobsEmitter"
                            [xAnalysisDimension]="service.xAnalysisDimension"
                            [yAnalysisDimension]="service.yAnalysisDimension" [blobItems]="service.blobItems"
                            [canOpenVehicleAdvertModal]="false" [service]="service">
                        </blobChart>


                    </div>

                    <!-- Vehicle details tile -->
                    <div class="dashboard-tile grid-col-9-19 grid-row-4-8">
                        <div class="tileInner">
                            <div class="tileHeader">
                                <div class="headerWords">
                                    Vehicle Details
                                </div>
                            </div>
                            <div *ngIf="service.vehicleValuationBatchResultsHighlighted" class="contentHolder"
                                id="gridHolder" #gridHolder>
                                <vehicleDetailsBulkTable [rowData]="service.vehicleValuationBatchResultsHighlighted">
                                </vehicleDetailsBulkTable>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <ng-template #tileHeaderTemplate let-title="title">
                <!-- Tile header -->
                <div class="tileHeader ">
                    <div class="headerWords">
                       <h4> {{title}}</h4>

                    </div>

                    <div class="interactionIconsHolder">
                        <!-- Icon that shows if you are filtering measures on this tile -->
                        <div *ngIf="service.isFiltersOn()" class="cancelFilterHolder clickable"
                            (click)="service.clearFilters()">
                            <i class="fas fa-filter"></i>
                        </div>

                        <!-- Icon that shows if you are highlighting measures on this tile -->
                        <div *ngIf="service.isHighlightFiltersOn()" class="cancelHighlightsHolder clickable"
                            (click)="service.clearHighlights()">
                            <i class="fas fa-filter"></i>
                        </div>
                    </div>
                </div>
            </ng-template>






        </div>
    </div>
</div>