import { Pipe, PipeTransform } from '@angular/core';
import { CurrencyPipe, DatePipe, DecimalPipe, registerLocaleData } from '@angular/common';
import { CurrencySpainPipe } from './spainCurrency.pipe';
import { EnvironmentService } from './services/environment.service';
import localeEN from '@angular/common/locales/en';
import localeES from '@angular/common/locales/es';
import { ConstantsService } from './services/constants.service';
import { DecimalSpainPipe } from './spainDecimal.pipe';

export type FormatType =
  | 'number'
  | 'number0dp'
  | 'number1dp'
  | 'none'
  | 'percent1dp'
  | 'percent2dp'
  | 'outOf100'
  | 'percent'
  | 'currency'
  | 'currencyNoDash'
  | 'currencyK'
  | 'date'
  | 'dateNumbers'
  | 'dateAndTime'
  | 'shortDate'
  | 'days'
  | 'dateLongYear'
  | 'shortDateWithDayName'
  | 'dayName'
  | 'dateShortYear'
  | 'dateMed'
  | 'day'
  | 'short'
  | 'shortTime'
  | 'shortTimeAM'
  | 'dateTime'
  | 'dayAndDayNumber'
  | 'dayAndDate'
  | 'dayDateVertical'
  | 'month'
  | 'monthNoGap'
  | 'quarter'
  | 'week'
  | 'dayNew'
  | 'monthName'
  | 'intToMonth'
  | 'numberPlate'
  | 'dis'
  | 'chassis'
  | 'makeModelOnly'
  | 'splitPascalCase'
  | 'year'
  | 'timeDuration'
  | 'dayMonth'
  | 'trimWhitespace'
  | 'dayLetterEng'
  | 'dayLetter';

@Pipe({ name: 'cph' })
export class CphPipe implements PipeTransform {

  displayCurrency: string;

  constructor(
    private currencyPipe: CurrencyPipe,
    private spainCurrency: CurrencySpainPipe,
    private decimalPipe: DecimalPipe,
    private spainDecimal: DecimalSpainPipe,
    private datePipe: DatePipe,
    public environmentService: EnvironmentService,
    public constants: ConstantsService,
  ) {
    registerLocaleData(localeEN, 'en');
    registerLocaleData(localeES, 'es');

  }


  transform(value: any, pipeType: FormatType, decimalPlaces: number, includePrefix?: boolean, noDashIfEmpty?: boolean): any {
    //if given empty string
    this.displayCurrency = this.environmentService.get().displayCurrency;


    let prefix = ''
    if (includePrefix) {
      //prefix = '';
      if (value >= 0) { prefix = '+' }
    }


    if (pipeType == 'currencyNoDash') {
      if (value == 0) { return '' }
      return this.displayCurrency == 'EUR' ? prefix + this.spainCurrency.transform(value) : prefix + this.currencyPipe.transform(value, this.displayCurrency, 'symbol', '1.' + decimalPlaces + '-' + decimalPlaces)
    }

    if (!value && noDashIfEmpty) return null;
    if (!value) return '-'
    if (value === "-") return "-"
    if (value === '.') { return '0.' }
    if (['currency', 'number'].includes(pipeType) && value === 0) return '-'
    if (value === 0 && pipeType == 'percent') { return '0%' }


    //various return paths
    try {

      switch (pipeType) {
        //number / percent
        case 'number': { return this.constants.currentLang == 'es' ? prefix + this.spainDecimal.transform(value) : prefix + this.decimalPipe.transform(value, '1.' + decimalPlaces + '-' + decimalPlaces); }
        case 'number0dp': { return this.constants.currentLang == 'es' ? prefix + this.spainDecimal.transform(value) : prefix + this.decimalPipe.transform(value, '1.0-0'); }
        case 'number1dp': { return this.constants.currentLang == 'es' ? prefix + this.spainDecimal.transform(value) : prefix + this.decimalPipe.transform(value, '1.1-1'); }
        case 'none': { return value }
        //case 'percent': { return value == 0 ? '-' : prefix + this.decimalPipe.transform(value * 100, '1.' + decimalPlaces + '-' + decimalPlaces) + '%' }
        case 'percent1dp': { return value == 0 ? '-' : prefix + this.decimalPipe.transform(value * 100, '1.1-1') + '%' }
        case 'percent2dp': { return value == 0 ? '-' : prefix + this.decimalPipe.transform(value * 100, '1.1-2') + '%' }
        case 'outOf100': { return `${this.decimalPipe.transform(value, '1.0-0')} / 100` }
        case 'percent': { return prefix + this.decimalPipe.transform(value * 100, '1.' + decimalPlaces + '-' + decimalPlaces) + '%' }

        //currencies
        case 'currency': { return this.displayCurrency == 'EUR' ? prefix + this.spainCurrency.transform(value) : prefix + this.currencyPipe.transform(value, this.displayCurrency, 'symbol', '1.' + decimalPlaces + '-' + decimalPlaces) }
        case 'currencyK': { return this.displayCurrency == 'EUR' ? prefix + this.spainCurrency.transform(value / 1000) : prefix + this.currencyPipe.transform(value / 1000, this.displayCurrency, 'symbol', '1.' + decimalPlaces + '-' + decimalPlaces) }

        //dates
        case 'date': { return this.datePipe.transform(value, 'd MMM yyyy', null, this.constants.translatedText.LocaleCode); } // "8 Nov 2023"
        case 'dateNumbers': { return this.datePipe.transform(value, 'dd/MM/yyyy', null, this.constants.translatedText.LocaleCode); }
        case 'shortDate': { return this.datePipe.transform(value, 'd MMM', null, this.constants.translatedText.LocaleCode); } // "8 Nov"

        case 'dateLongYear': { return this.datePipe.transform(value, 'dd/MM/yyyy', null, this.constants.translatedText.LocaleCode); } // "08/11/2023"
        case 'dateAndTime': { return this.transform(value, 'date', 0, includePrefix, noDashIfEmpty) + ' ' + this.transform(value, 'shortTimeAM', 0, includePrefix, noDashIfEmpty) }
        case 'shortDateWithDayName': { return this.datePipe.transform(value, 'EE d MMM', null, this.constants.translatedText.LocaleCode); } // "Tue 8 Nov"
        case 'dayName': { return this.datePipe.transform(value, 'EEEE', null, this.constants.translatedText.LocaleCode) } // "Tuesday"
        case 'dayLetter': { return this.datePipe.transform(value, 'E', null, this.constants.translatedText.LocaleCode) } // "Tuesday"
        case 'dayLetterEng': { return this.datePipe.transform(value, 'E', null, 'en-gb') } // Force to UK version if necessary
        case 'dateShortYear': { return this.datePipe.transform(value, 'dd/MM/yy', null, this.constants.translatedText.LocaleCode); } // "8 Nov 23"
        case 'dateMed': { return this.datePipe.transform(value, 'd MMM yy', null, this.constants.translatedText.LocaleCode); }
        case 'day': { return this.datePipe.transform(value, 'EEEE dd LLLL', null, this.constants.translatedText.LocaleCode); } // "Tuesday 08 November"
        case 'short': { return this.datePipe.transform(value, 'M/d/yy, h:mm a'); } // "11/8/23, 7:00 AM"
        case 'shortTime': { return this.datePipe.transform(value, 'H:mm'); } // "19:00"
        case 'shortTimeAM': { return this.datePipe.transform(value, 'h:mm a'); } // "7:00 PM"
        case 'dateTime': { return this.datePipe.transform(value, 'h:mm a d MMM', null, this.constants.translatedText.LocaleCode); } // "7:00 PM 8 Nov"
        case 'dayAndDayNumber': { return this.datePipe.transform(value, 'EEE dd', null, this.constants.translatedText.LocaleCode); } // "Tue 08"
        case 'dayAndDate': { return this.datePipe.transform(value, 'EEEE', null, this.constants.translatedText.LocaleCode) + ' ' + this.ordinalSuffix(value.getDate()) + ' ' + this.datePipe.transform(value, 'MMM', null, this.constants.translatedText.LocaleCode); } // "Tuesday 8th Nov"
        case 'dayDateVertical': { return this.dayDateVertical(value) } //see function
        case 'month': { return this.datePipe.transform(value, 'MMM yy', null, this.constants.translatedText.LocaleCode); } // "Nov 23"
        case 'monthNoGap': { return this.datePipe.transform(value, 'MMMyy', null, this.constants.translatedText.LocaleCode); } // "Nov23"

        case 'quarter': { return this.workoutQuarter(value) } // "2023 Q3"
        case 'week': { return this.workoutWeek(value) } // "w/c 6 Nov" 
        case 'dayNew': { return this.workoutDay(value) } // "6 Nov" 

        case 'monthName': { return this.datePipe.transform(value, 'MMMM', null, this.constants.translatedText.LocaleCode); } // "November"
        case 'intToMonth': { return new Date(`${value} 01 2000`).toLocaleDateString(`en`, { month: `long` }) } // "January"
        case 'timeDuration': { return this.timeDuration(value) }

        //special
        case 'numberPlate': { return this.workoutNumberPlateValue(value, pipeType) }
        case 'dis': { return Math.round(value) + 'd' }
        case 'chassis': { return this.workoutChassisValue(value, pipeType); }
        case 'makeModelOnly': { return value.split(' ').slice(0, 2).join(' '); }
        case 'splitPascalCase': { return this.splitPascalCase(value) };
        case 'year': return this.datePipe.transform(value, 'YYYY');
        case 'dayMonth': return this.datePipe.transform(value, 'dd/MM');
        case 'days': return this.constants.pluralise(value, 'day', 'days');

        case 'trimWhitespace': return value.replace(/\s+/g, '');

        default: debugger; console.error('pipeType not found! ' + pipeType);
      }
    }
    catch {
      console.error(`Pipe failed converting ${value} using pipe ${pipeType}.  Stacktrace follows..`)
      console.trace()
      return value;
    }
  }

  splitPascalCase(value: any): any {
    return this.constants.splitPascalCase(value)
  }


  timeDuration(value: number): string {
    if (value === null || value === undefined) return '';

    const minutes = Math.floor(value / 60);
    const seconds = Math.round(value % 60);
    let formattedTime = '';

    if (minutes > 0) {
      formattedTime += `${minutes}m `;
    }
    if (seconds > 0 || minutes === 0) {
      formattedTime += `${seconds}s`;
    }

    return formattedTime.trim();
  }

  dayDateVertical(date: Date) {
    return `${this.datePipe.transform(date, 'EEE', null, this.constants.translatedText.LocaleCode)}
${this.ordinalSuffix(date.getDate())} 
${this.datePipe.transform(date, 'MMM', null, this.constants.translatedText.LocaleCode)}`
  }

  ordinalSuffix = (i) => {
    if (this.constants.environment.customer == 'RRGSpain') return i;

    var j = i % 10,
      k = i % 100;
    if (j == 1 && k != 11) {
      return i + "st";
    }
    if (j == 2 && k != 12) {
      return i + "nd";
    }
    if (j == 3 && k != 13) {
      return i + "rd";
    }
    return i + "th";
  }
  private workoutChassisValue(value: any, pipeType: string) {
    let chassisValue = value;
    if (pipeType === 'chassis' && (value === '' || value === null)) {
      chassisValue = '-';
    }
    return chassisValue;
  }

  private workoutNumberPlateValue(value: any, pipeType: string) {
    let numberPlateValue = value;
    if (pipeType == 'numberPlate') {
      if (value && value.length == 7) {
        //test if is in the format LLNNLLL
        if (isNaN(value[0]) && isNaN(value[1]) && !isNaN(value[2]) && !isNaN(value[3]) && isNaN(value[4]) && isNaN(value[5]) && isNaN(value[6])) {
          numberPlateValue = value.substring(0, 4) + ' ' + value.substring(4, 7);
        } else {
          numberPlateValue = value;
        }

      }
    }
    return numberPlateValue;
  }


  private workoutQuarter(value: Date) {
    let quarterNumber = Math.floor((value.getMonth() + 3) / 3);
    return `${this.datePipe.transform(value, 'yyyy', null, this.constants.translatedText.LocaleCode)} Q${quarterNumber}`;
  }

  private workoutWeek(value: Date) {
    if (this.constants.datesAreSame(value, this.constants.thisWeekStartDate)) {
      return this.constants.translatedText.Common_ThisWeek
    } else if (this.constants.datesAreSame(value, this.constants.lastWeekStartDate)) {
      return this.constants.translatedText.Common_LastWeek
    } else if (this.constants.datesAreSame(value, this.constants.nextWeekStartDate)) {
      return this.constants.translatedText.Common_NextWeek
    }
    else return `${this.constants.translatedText.Common_WeekCommencing} ${this.datePipe.transform(value, 'd MMM', null, this.constants.translatedText.LocaleCode)}`
  }

  private workoutDay(value: Date) {
    if (this.constants.datesAreSame(value, this.constants.appStartTime)) {
      return this.constants.translatedText.Common_Today
    } else if (this.constants.datesAreSame(value, this.constants.yesterdayStart)) {
      return this.constants.translatedText.Common_Yesterday
    } else if (this.constants.datesAreSame(value, this.constants.tomorrowStart)) {
      return this.constants.translatedText.Common_Tomorrow
    }
    else {
      return this.datePipe.transform(value, 'd MMM', null, this.constants.translatedText.LocaleCode);
    }
  }


}

