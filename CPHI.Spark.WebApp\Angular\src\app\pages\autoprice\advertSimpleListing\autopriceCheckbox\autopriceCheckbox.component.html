<!-- Any checkbox -->
<div *ngIf="allCheckBoxes.length > 1"  class="d-flex mb-2">
    <button class="custom-checkbox me-2" [ngClass]="{ 'checked': isCheckBoxChosen('any') }"
        (click)="toggleCheckBox('any')">
        <span *ngIf="isCheckBoxChosen('any')">
            <i class="fa fa-check"></i>
        </span>
    </button>
    <span>Any ({{ relevantRowData.length }})</span>
</div>

<!-- The others -->
<ng-container *ngFor="let checkBox of allCheckBoxes; index as i;">
    <div class="d-flex mb-2">
        <button class="custom-checkbox me-2" [ngClass]="{ 'checked': isCheckBoxChosen(checkBox) }"
            (click)="toggleCheckBox(checkBox)">
            <span *ngIf="isCheckBoxChosen(checkBox)">
                <i class="fa fa-check"></i>
            </span>
        </button>
        <span>{{ getCleanCheckboxLabel(checkBox) }} ({{ getCheckBoxCount(checkBox) }})</span>
    </div>
</ng-container>