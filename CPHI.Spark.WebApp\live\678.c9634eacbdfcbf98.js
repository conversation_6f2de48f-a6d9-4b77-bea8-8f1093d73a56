"use strict";(self.webpackChunkstockpulseweb=self.webpackChunkstockpulseweb||[]).push([[678],{7678:(dn,Pe,ke)=>{ke.r(Pe),ke.d(Pe,{default:()=>Bt});const{entries:ve,setPrototypeOf:Ue,isFrozen:Rt,getPrototypeOf:yt,getOwnPropertyDescriptor:Ot}=Object;let{freeze:g,seal:O,create:Fe}=Object,{apply:ue,construct:me}=typeof Reflect<"u"&&Reflect;g||(g=function(o){return o}),O||(O=function(o){return o}),ue||(ue=function(o,l,s){return o.apply(l,s)}),me||(me=function(o,l){return new o(...l)});const Q=A(Array.prototype.forEach),Lt=A(Array.prototype.lastIndexOf),He=A(Array.prototype.pop),G=A(Array.prototype.push),bt=A(Array.prototype.splice),ee=A(String.prototype.toLowerCase),pe=A(String.prototype.toString),ze=A(String.prototype.match),W=A(String.prototype.replace),Dt=A(String.prototype.indexOf),Nt=A(String.prototype.trim),L=A(Object.prototype.hasOwnProperty),h=A(RegExp.prototype.test),B=function It(r){return function(){for(var o=arguments.length,l=new Array(o),s=0;s<o;s++)l[s]=arguments[s];return me(r,l)}}(TypeError);function A(r){return function(o){o instanceof RegExp&&(o.lastIndex=0);for(var l=arguments.length,s=new Array(l>1?l-1:0),T=1;T<l;T++)s[T-1]=arguments[T];return ue(r,o,s)}}function a(r,o){let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ee;Ue&&Ue(r,null);let s=o.length;for(;s--;){let T=o[s];if("string"==typeof T){const N=l(T);N!==T&&(Rt(o)||(o[s]=N),T=N)}r[T]=!0}return r}function Mt(r){for(let o=0;o<r.length;o++)L(r,o)||(r[o]=null);return r}function D(r){const o=Fe(null);for(const[l,s]of ve(r))L(r,l)&&(o[l]=Array.isArray(s)?Mt(s):s&&"object"==typeof s&&s.constructor===Object?D(s):s);return o}function Y(r,o){for(;null!==r;){const s=Ot(r,o);if(s){if(s.get)return A(s.get);if("function"==typeof s.value)return A(s.value)}r=yt(r)}return function l(){return null}}const Ge=g(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),de=g(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Te=g(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ct=g(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ee=g(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),wt=g(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),We=g(["#text"]),Be=g(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),_e=g(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ye=g(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),te=g(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),xt=O(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Pt=O(/<%[\w\W]*|[\w\W]*%>/gm),kt=O(/\$\{[\w\W]*/gm),vt=O(/^data-[\-\w.\u00B7-\uFFFF]+$/),Ut=O(/^aria-[\-\w]+$/),Xe=O(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ft=O(/^(?:\w+script|data):/i),Ht=O(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),je=O(/^html$/i),zt=O(/^[a-z][.\w]*(-[.\w]+)+$/i);var Ve=Object.freeze({__proto__:null,ARIA_ATTR:Ut,ATTR_WHITESPACE:Ht,CUSTOM_ELEMENT:zt,DATA_ATTR:vt,DOCTYPE_NAME:je,ERB_EXPR:Pt,IS_ALLOWED_URI:Xe,IS_SCRIPT_OR_DATA:Ft,MUSTACHE_EXPR:xt,TMPLIT_EXPR:kt});const Gt=function(){return typeof window>"u"?null:window},Wt=function(o,l){if("object"!=typeof o||"function"!=typeof o.createPolicy)return null;let s=null;const T="data-tt-policy-suffix";l&&l.hasAttribute(T)&&(s=l.getAttribute(T));const N="dompurify"+(s?"#"+s:"");try{return o.createPolicy(N,{createHTML:x=>x,createScriptURL:x=>x})}catch{return console.warn("TrustedTypes policy "+N+" could not be created."),null}};var Bt=function qe(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Gt();const o=i=>qe(i);if(o.version="3.2.6",o.removed=[],!r||!r.document||9!==r.document.nodeType||!r.Element)return o.isSupported=!1,o;let{document:l}=r;const s=l,T=s.currentScript,{DocumentFragment:N,HTMLTemplateElement:x,Node:ge,Element:Ke,NodeFilter:j,NamedNodeMap:Yt=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:Xt,DOMParser:jt,trustedTypes:ne}=r,V=Ke.prototype,Vt=Y(V,"cloneNode"),$t=Y(V,"remove"),qt=Y(V,"nextSibling"),Kt=Y(V,"childNodes"),oe=Y(V,"parentNode");if("function"==typeof x){const i=l.createElement("template");i.content&&i.content.ownerDocument&&(l=i.content.ownerDocument)}let S,$="";const{implementation:he,createNodeIterator:Zt,createDocumentFragment:Jt,getElementsByTagName:Qt}=l,{importNode:en}=s;let R={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};o.isSupported="function"==typeof ve&&"function"==typeof oe&&he&&void 0!==he.createHTMLDocument;const{MUSTACHE_EXPR:Ae,ERB_EXPR:Se,TMPLIT_EXPR:Re,DATA_ATTR:tn,ARIA_ATTR:nn,IS_SCRIPT_OR_DATA:on,ATTR_WHITESPACE:Ze,CUSTOM_ELEMENT:an}=Ve;let{IS_ALLOWED_URI:Je}=Ve,m=null;const Qe=a({},[...Ge,...de,...Te,...Ee,...We]);let d=null;const et=a({},[...Be,..._e,...Ye,...te]);let f=Object.seal(Fe(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),q=null,ye=null,tt=!0,Oe=!0,nt=!1,ot=!0,P=!1,ie=!0,w=!1,Le=!1,be=!1,k=!1,ae=!1,re=!1,it=!0,at=!1;const rn="user-content-";let De=!0,K=!1,v={},U=null;const rt=a({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let st=null;const lt=a({},["audio","video","img","source","image","track"]);let Ne=null;const ct=a({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),se="http://www.w3.org/1998/Math/MathML",le="http://www.w3.org/2000/svg",I="http://www.w3.org/1999/xhtml";let F=I,Ie=!1,Me=null;const sn=a({},[se,le,I],pe);let ce=a({},["mi","mo","mn","ms","mtext"]),fe=a({},["annotation-xml"]);const ln=a({},["title","style","font","a","script"]);let Z=null;const cn=["application/xhtml+xml","text/html"],fn="text/html";let p=null,H=null;const un=l.createElement("form"),ft=function(e){return e instanceof RegExp||e instanceof Function},Ce=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!H||H!==e){if((!e||"object"!=typeof e)&&(e={}),e=D(e),Z=-1===cn.indexOf(e.PARSER_MEDIA_TYPE)?fn:e.PARSER_MEDIA_TYPE,p="application/xhtml+xml"===Z?pe:ee,m=L(e,"ALLOWED_TAGS")?a({},e.ALLOWED_TAGS,p):Qe,d=L(e,"ALLOWED_ATTR")?a({},e.ALLOWED_ATTR,p):et,Me=L(e,"ALLOWED_NAMESPACES")?a({},e.ALLOWED_NAMESPACES,pe):sn,Ne=L(e,"ADD_URI_SAFE_ATTR")?a(D(ct),e.ADD_URI_SAFE_ATTR,p):ct,st=L(e,"ADD_DATA_URI_TAGS")?a(D(lt),e.ADD_DATA_URI_TAGS,p):lt,U=L(e,"FORBID_CONTENTS")?a({},e.FORBID_CONTENTS,p):rt,q=L(e,"FORBID_TAGS")?a({},e.FORBID_TAGS,p):D({}),ye=L(e,"FORBID_ATTR")?a({},e.FORBID_ATTR,p):D({}),v=!!L(e,"USE_PROFILES")&&e.USE_PROFILES,tt=!1!==e.ALLOW_ARIA_ATTR,Oe=!1!==e.ALLOW_DATA_ATTR,nt=e.ALLOW_UNKNOWN_PROTOCOLS||!1,ot=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,P=e.SAFE_FOR_TEMPLATES||!1,ie=!1!==e.SAFE_FOR_XML,w=e.WHOLE_DOCUMENT||!1,k=e.RETURN_DOM||!1,ae=e.RETURN_DOM_FRAGMENT||!1,re=e.RETURN_TRUSTED_TYPE||!1,be=e.FORCE_BODY||!1,it=!1!==e.SANITIZE_DOM,at=e.SANITIZE_NAMED_PROPS||!1,De=!1!==e.KEEP_CONTENT,K=e.IN_PLACE||!1,Je=e.ALLOWED_URI_REGEXP||Xe,F=e.NAMESPACE||I,ce=e.MATHML_TEXT_INTEGRATION_POINTS||ce,fe=e.HTML_INTEGRATION_POINTS||fe,f=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ft(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(f.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ft(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(f.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(f.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),P&&(Oe=!1),ae&&(k=!0),v&&(m=a({},We),d=[],!0===v.html&&(a(m,Ge),a(d,Be)),!0===v.svg&&(a(m,de),a(d,_e),a(d,te)),!0===v.svgFilters&&(a(m,Te),a(d,_e),a(d,te)),!0===v.mathMl&&(a(m,Ee),a(d,Ye),a(d,te))),e.ADD_TAGS&&(m===Qe&&(m=D(m)),a(m,e.ADD_TAGS,p)),e.ADD_ATTR&&(d===et&&(d=D(d)),a(d,e.ADD_ATTR,p)),e.ADD_URI_SAFE_ATTR&&a(Ne,e.ADD_URI_SAFE_ATTR,p),e.FORBID_CONTENTS&&(U===rt&&(U=D(U)),a(U,e.FORBID_CONTENTS,p)),De&&(m["#text"]=!0),w&&a(m,["html","head","body"]),m.table&&(a(m,["tbody"]),delete q.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw B('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw B('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');S=e.TRUSTED_TYPES_POLICY,$=S.createHTML("")}else void 0===S&&(S=Wt(ne,T)),null!==S&&"string"==typeof $&&($=S.createHTML(""));g&&g(e),H=e}},ut=a({},[...de,...Te,...Ct]),mt=a({},[...Ee,...wt]),mn=function(e){let t=oe(e);(!t||!t.tagName)&&(t={namespaceURI:F,tagName:"template"});const n=ee(e.tagName),c=ee(t.tagName);return!!Me[e.namespaceURI]&&(e.namespaceURI===le?t.namespaceURI===I?"svg"===n:t.namespaceURI===se?"svg"===n&&("annotation-xml"===c||ce[c]):Boolean(ut[n]):e.namespaceURI===se?t.namespaceURI===I?"math"===n:t.namespaceURI===le?"math"===n&&fe[c]:Boolean(mt[n]):e.namespaceURI===I?!(t.namespaceURI===le&&!fe[c]||t.namespaceURI===se&&!ce[c])&&!mt[n]&&(ln[n]||!ut[n]):!("application/xhtml+xml"!==Z||!Me[e.namespaceURI]))},b=function(e){G(o.removed,{element:e});try{oe(e).removeChild(e)}catch{$t(e)}},z=function(e,t){try{G(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch{G(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(k||ae)try{b(t)}catch{}else try{t.setAttribute(e,"")}catch{}},pt=function(e){let t=null,n=null;if(be)e="<remove></remove>"+e;else{const u=ze(e,/^[\r\n\t ]+/);n=u&&u[0]}"application/xhtml+xml"===Z&&F===I&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const c=S?S.createHTML(e):e;if(F===I)try{t=(new jt).parseFromString(c,Z)}catch{}if(!t||!t.documentElement){t=he.createDocument(F,"template",null);try{t.documentElement.innerHTML=Ie?$:c}catch{}}const E=t.body||t.documentElement;return e&&n&&E.insertBefore(l.createTextNode(n),E.childNodes[0]||null),F===I?Qt.call(t,w?"html":"body")[0]:w?t.documentElement:E},dt=function(e){return Zt.call(e.ownerDocument||e,e,j.SHOW_ELEMENT|j.SHOW_COMMENT|j.SHOW_TEXT|j.SHOW_PROCESSING_INSTRUCTION|j.SHOW_CDATA_SECTION,null)},we=function(e){return e instanceof Xt&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof Yt)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Tt=function(e){return"function"==typeof ge&&e instanceof ge};function M(i,e,t){Q(i,n=>{n.call(o,e,t,H)})}const Et=function(e){let t=null;if(M(R.beforeSanitizeElements,e,null),we(e))return b(e),!0;const n=p(e.nodeName);if(M(R.uponSanitizeElement,e,{tagName:n,allowedTags:m}),ie&&e.hasChildNodes()&&!Tt(e.firstElementChild)&&h(/<[/\w!]/g,e.innerHTML)&&h(/<[/\w!]/g,e.textContent)||7===e.nodeType||ie&&8===e.nodeType&&h(/<[/\w]/g,e.data))return b(e),!0;if(!m[n]||q[n]){if(!q[n]&&gt(n)&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,n)||f.tagNameCheck instanceof Function&&f.tagNameCheck(n)))return!1;if(De&&!U[n]){const c=oe(e)||e.parentNode,E=Kt(e)||e.childNodes;if(E&&c)for(let y=E.length-1;y>=0;--y){const C=Vt(E[y],!0);C.__removalCount=(e.__removalCount||0)+1,c.insertBefore(C,qt(e))}}return b(e),!0}return e instanceof Ke&&!mn(e)||("noscript"===n||"noembed"===n||"noframes"===n)&&h(/<\/no(script|embed|frames)/i,e.innerHTML)?(b(e),!0):(P&&3===e.nodeType&&(t=e.textContent,Q([Ae,Se,Re],c=>{t=W(t,c," ")}),e.textContent!==t&&(G(o.removed,{element:e.cloneNode()}),e.textContent=t)),M(R.afterSanitizeElements,e,null),!1)},_t=function(e,t,n){if(it&&("id"===t||"name"===t)&&(n in l||n in un))return!1;if((!Oe||ye[t]||!h(tn,t))&&(!tt||!h(nn,t)))if(!d[t]||ye[t]){if(!(gt(e)&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,e)||f.tagNameCheck instanceof Function&&f.tagNameCheck(e))&&(f.attributeNameCheck instanceof RegExp&&h(f.attributeNameCheck,t)||f.attributeNameCheck instanceof Function&&f.attributeNameCheck(t))||"is"===t&&f.allowCustomizedBuiltInElements&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,n)||f.tagNameCheck instanceof Function&&f.tagNameCheck(n))))return!1}else if(!Ne[t]&&!h(Je,W(n,Ze,""))&&("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Dt(n,"data:")||!st[e])&&(!nt||h(on,W(n,Ze,"")))&&n)return!1;return!0},gt=function(e){return"annotation-xml"!==e&&ze(e,an)},ht=function(e){M(R.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||we(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:d,forceKeepAttr:void 0};let c=t.length;for(;c--;){const E=t[c],{name:u,namespaceURI:y,value:C}=E,J=p(u),xe=C;let _="value"===u?xe:Nt(xe);if(n.attrName=J,n.attrValue=_,n.keepAttr=!0,n.forceKeepAttr=void 0,M(R.uponSanitizeAttribute,e,n),_=n.attrValue,at&&("id"===J||"name"===J)&&(z(u,e),_=rn+_),ie&&h(/((--!?|])>)|<\/(style|title)/i,_)){z(u,e);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr){z(u,e);continue}if(!ot&&h(/\/>/i,_)){z(u,e);continue}P&&Q([Ae,Se,Re],St=>{_=W(_,St," ")});const At=p(e.nodeName);if(_t(At,J,_)){if(S&&"object"==typeof ne&&"function"==typeof ne.getAttributeType&&!y)switch(ne.getAttributeType(At,J)){case"TrustedHTML":_=S.createHTML(_);break;case"TrustedScriptURL":_=S.createScriptURL(_)}if(_!==xe)try{y?e.setAttributeNS(y,u,_):e.setAttribute(u,_),we(e)?b(e):He(o.removed)}catch{z(u,e)}}else z(u,e)}M(R.afterSanitizeAttributes,e,null)},pn=function i(e){let t=null;const n=dt(e);for(M(R.beforeSanitizeShadowDOM,e,null);t=n.nextNode();)M(R.uponSanitizeShadowNode,t,null),Et(t),ht(t),t.content instanceof N&&i(t.content);M(R.afterSanitizeShadowDOM,e,null)};return o.sanitize=function(i){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=null,n=null,c=null,E=null;if(Ie=!i,Ie&&(i="\x3c!--\x3e"),"string"!=typeof i&&!Tt(i)){if("function"!=typeof i.toString)throw B("toString is not a function");if("string"!=typeof(i=i.toString()))throw B("dirty is not a string, aborting")}if(!o.isSupported)return i;if(Le||Ce(e),o.removed=[],"string"==typeof i&&(K=!1),K){if(i.nodeName){const C=p(i.nodeName);if(!m[C]||q[C])throw B("root node is forbidden and cannot be sanitized in-place")}}else if(i instanceof ge)t=pt("\x3c!----\x3e"),n=t.ownerDocument.importNode(i,!0),1===n.nodeType&&"BODY"===n.nodeName||"HTML"===n.nodeName?t=n:t.appendChild(n);else{if(!k&&!P&&!w&&-1===i.indexOf("<"))return S&&re?S.createHTML(i):i;if(t=pt(i),!t)return k?null:re?$:""}t&&be&&b(t.firstChild);const u=dt(K?i:t);for(;c=u.nextNode();)Et(c),ht(c),c.content instanceof N&&pn(c.content);if(K)return i;if(k){if(ae)for(E=Jt.call(t.ownerDocument);t.firstChild;)E.appendChild(t.firstChild);else E=t;return(d.shadowroot||d.shadowrootmode)&&(E=en.call(s,E,!0)),E}let y=w?t.outerHTML:t.innerHTML;return w&&m["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&h(je,t.ownerDocument.doctype.name)&&(y="<!DOCTYPE "+t.ownerDocument.doctype.name+">\n"+y),P&&Q([Ae,Se,Re],C=>{y=W(y,C," ")}),S&&re?S.createHTML(y):y},o.setConfig=function(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Ce(i),Le=!0},o.clearConfig=function(){H=null,Le=!1},o.isValidAttribute=function(i,e,t){H||Ce({});const n=p(i),c=p(e);return _t(n,c,t)},o.addHook=function(i,e){"function"==typeof e&&G(R[i],e)},o.removeHook=function(i,e){if(void 0!==e){const t=Lt(R[i],e);return-1===t?void 0:bt(R[i],t,1)[0]}return He(R[i])},o.removeHooks=function(i){R[i]=[]},o.removeAllHooks=function(){R={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}()}}]);