import { Injectable } from "@angular/core";
import { ICellRendererParams, IRowNode } from "ag-grid-community";
import { CphPipe } from "../cph.pipe";
import { AutotraderService } from "./autotrader.service";
import { ConstantsService } from "./constants.service";

@Injectable({
    providedIn: 'root'
})
export class AutopriceRendererService {
    constructor(
        private autotraderService: AutotraderService,
        private constantsService: ConstantsService,
        private cphPipe: CphPipe
    ) { }

    customImageCellRenderer = (params: ICellRendererParams) => {
        const url = this.getImageURL(params);
        const showImg = this.showImage(params);
        if(showImg){
            return `<img style="max-height: 60px;max-width: 100%; width: auto;height: auto;" src="${url}" />`;
        }
        return null;
    }

    getImageURL(params: ICellRendererParams) {
        if (params.data?.ImageURL) { return params.data?.ImageURL; }
        if (params.data?.AdvertFirstImage) { return params.data?.AdvertFirstImage; }
        if (!params.data?.ImageURL && !params.data?.ImageUrl && !params.data?.AdvertFirstImage) { return '/assets/imgs/autoTrader/palceholder-car.png'; }
    }

    provideAgeAndOwnersString(ageAndOwners:string){
        if(!ageAndOwners || !ageAndOwners.includes('|')){return ageAndOwners}
        const age = ageAndOwners.split('|')[0];
        const owners = ageAndOwners.split('|')[1];
        if(owners.includes('+')){
          return `${age}, ${owners} owners`
        }
        else if(owners === 'Unknown'){
            return  `${age}, Unknown owners`
        }
        else{
          return `${age}, ${this.constantsService.pluralise(parseInt(owners),'owner','owners')}`
        }
      }

    showImage(params: ICellRendererParams) {
        if (!params.data || params.node.isRowPinned()) { return false; } 
         return true ;
    }

    customAdLinkRenderer(params) {
        const showIcon = params.data && !params.node.isRowPinned()
        const autoTraderListingIdentifier = params.data?.WebSiteSearchIdentifier;

        let url: string = `https://www.autotrader.co.uk/${params.data.VehicleType.toLowerCase()}-details/${autoTraderListingIdentifier}`;

        return `<a *ngIf="${showIcon}" id="iconWrapper" target="_blank" href="${url}">
        <i class="fas fa-car"></i></div>`;
    }

    customPortalLinkRenderer(params: ICellRendererParams) {
        const showIcon = params.data && !params.node.isRowPinned()
        const autoTraderListingIdentifier = params.data?.WebSiteStockIdentifier;

        let url: string = `https://portal.autotrader.co.uk/portal/#/edit/${autoTraderListingIdentifier}/vehicle-insight`;

        return `<a *ngIf="${showIcon}" id="iconWrapper" target="_blank" href="${url}">
        <i class="fas fa-car"></i></div>`;
    }

    addEnergyTypeIcon(params: ICellRendererParams): string {
        if (!params.data) { return '' }
        if (params.node.isRowPinned()) { return '' }
        let icon: string;
        let icon2: string;

        if (params.value == 'Diesel') icon = 'fas fa-gas-pump';
        if (params.value == 'Electric') icon = 'fas fa-charging-station electric-green';
        if (params.value == 'Petrol') icon = 'fas fa-gas-pump petrol-green';
        if (params.value == 'Petrol Hybrid' || params.value == 'Petrol Plug-in Hybrid' || params.value == 'Bi Fuel') {
            icon = 'fas fa-plug blue';
            icon2 = 'fas fa-gas-pump petrol-green';
        }

        return `${params.value}<i class="${icon}"></i><i class="${icon2}"></i>`;
    }

    autoTraderLozengeRenderer(params: ICellRendererParams) {
        
        // Early exit for pinned rows or if the value is undefined.
        if (params.node.isRowPinned() || params.value === undefined) { return null; }
    
        // Determining the columns by which the grid is grouped.
        const columnIsGrouped = params.colDef.headerName=='Group';
    
        // If the grid is grouped by a different column, and this row is a group row, return null.
        if (!columnIsGrouped && params.node.group) {
            return null;
        }
    
        let text: string;
        if (params.colDef.colId === 'PerfRating' || params.node.field === 'PerfRating') {
            text = params.value;
            const backgroundColour = this.autotraderService.getPerformanceRatingColour(text);
            return `<div class="auto-trader-cell-lozenge" style="background-color: ${backgroundColour}">${text}</div>`;
        } 
        else if (params.colDef.colId === 'VsStrategyBanding' || params.node.field === 'VsStrategyBanding') {
            text = params.value;
            const backgroundColour = this.autotraderService.getStrategyBandColour(text);
            return `<div class="auto-trader-cell-lozenge" style="background-color: ${backgroundColour}">${text}</div>`;
        } 
        else if (params.colDef.colId === 'VsTestStrategyBanding' || params.node.field === 'VsTestStrategyBanding') {
            text = params.value;
            const backgroundColour = this.autotraderService.getStrategyBandColour(text);
            return `<div class="auto-trader-cell-lozenge" style="background-color: ${backgroundColour}">${text}</div>`;
        } 
        else if (params.colDef.colId === 'RetailRatingBand' || params.node.field === 'RetailRatingBand') {
            text = params.value;
            const backgroundColour = this.autotraderService.getRetailRatingBandColour(text);
            return `<div class="auto-trader-cell-lozenge" style="background-color: ${backgroundColour};">${text}</div>`;
        }
         else {
            text = params.value;
            return `<div class="auto-trader-cell-lozenge">${text}</div>`;
        }
    
    }
    

    lastCommentRenderer(params: ICellRendererParams) {
        const row = params.data;
        if (!row) { return '' }
        if (!row.LastCommentText || !row.LastCommentName) { return '' }
        return `<div class=commentHolder> <div class="commentText">${row.LastCommentText}</div> &nbsp; - &nbsp; <div class="initials">${this.constantsService.makeInitials(row.LastCommentName)}</div></div>`;
    }

    autoTraderRetailRatingRenderer(params: any) {
        if(!params.params.RetailRating){return null}
        return `<div class="bar-and-score in-table-cell">
            <div class="score">
                <span class="prominent">${params.params.RetailRating ? this.cphPipe.transform(params.params.RetailRating, 'number', 0) : 0}</span> out of 100
            </div>
            <div class="bar">
                <div class="bar-inner" style="width: ${params.params.RetailRating}%"></div>
                <div class="bar-break left"></div>
                <div class="bar-break left-middle"></div>
                <div class="bar-break right-middle"></div>
                <div class="bar-break right"></div>
            </div>
        </div>`
    }

    autoTraderPriceIndicatorRenderer(params: ICellRendererParams) : string {

        const isPinned: boolean = params.node.isRowPinned();
        const text: string = this.autotraderService.provideLabelForPriceIndicator(params.value);
        const lozengeClass: string = `priceIndicatorLozenge stockReportsGrid ${params.value}`;
        if (isPinned) { return ''; }
        // Prevent this ever displaying as undefined
        if (text == undefined) { return ''; }

        return `<div class="${lozengeClass}">${text}</div>`;
    }

    autoTraderAdvertImageRenderer(params: ICellRendererParams) {
        let imageUrl: string;

        if (!params.data || params.node.rowPinned) { return ''; }
        if (params.data?.ImageURL) { imageUrl = params.data?.ImageURL; }
        if (params.data?.ImageUrl) { imageUrl = params.data?.ImageUrl; }
        if (params.data?.AdvertFirstImage) { imageUrl = params.data?.AdvertFirstImage; }
        if (!params.data?.ImageURL && !params.data?.ImageUrl && !params.data?.AdvertFirstImage) { imageUrl = '/assets/imgs/autoTrader/palceholder-car.png'; }

        return `<img class="advertImageInCell" src="${imageUrl}" />`;
    }

    regPlateRenderer(params: ICellRendererParams, extraParams?: any) {
        const node: IRowNode  = params.node;
        const isPinned: boolean = node.isRowPinned();

        if (isPinned) {return '';}
        if(node.group){return null}

        const text: string = this.cphPipe.transform(params.value, 'numberPlate', 0);

        return `<div class="regPlate" style="margin: ${extraParams?.noMargin ? '0' : '0em 2em'}">${text}</div>`
    }

    autoTraderPerformanceRatingSimpleRenderer(params: ICellRendererParams) {
        const score: number = params.value;

        let barClass: string = 'bar ';

        if (score <= 25 && score > 0) { barClass += 'performanceLow'; }
        if (score > 25 && score <= 50) { barClass += 'performanceFair'; }
        if (score > 50 && score <= 75) { barClass += 'performanceGood'; }
        if (score > 75) { barClass += 'performanceGreat'; }

        const leftPercent = Math.min(97,score);

        return `<div title="${score}" class="barContainerSimple"><div class="${barClass}"></div><div class="indicator" style="left: ${leftPercent}%"></div></div>`
    }

    autoTraderPerformanceRatingRenderer(params) {
        return `<div class="ratingContainer inTableCell">
            <div class="ratingTextInCell">
                <span>${params.params.score} out of 100</span>
            </div>
        
            <div class="barContainer">
                <div class="segment performanceLow" class="${params.params.score <= 25 && params.params.score > 0 ? 'current' : null}"></div>
                <div class="segment performanceFair" class="${params.params.score > 25 && params.params.score <= 50 ? 'current' : null}"></div>
                <div class="segment performanceGood" class="${params.params.score > 50 && params.params.score <= 75 ? 'current' : null}"></div>
                <div class="segment performanceGreat" class="${params.params.score > 75 ? 'current' : null}"></div>
                <div class="indicator" style="left: ${params.params.score}%"></div>
            </div>
        </div>
        `
    }

    autoTraderHorizontalBarRenderer(params) {
        let bars: string;
        params.barData.foreach(bar => {
            bars += `
                <div class="bar"
                    style="background-color: ${bar.backgroundColour}, width: ${(bar.value / getTotal()) * 100}%">
                    ${bar.value > 0 ? bar.value : null}
                </div>
            `
        })

        function getTotal() {
            return this.barData.map(x => x.value).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
        }

        return `<div class="bar-container">${bars}</div>`
    }
}