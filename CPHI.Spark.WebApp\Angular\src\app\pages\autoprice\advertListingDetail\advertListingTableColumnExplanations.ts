export  class AdvertListingTableColumnExplanations{


    static getExplanation(colId:string):string{
        if (colId == 'RetailSupply') { return `A comparison of the national market supply for a vehicle on Auto Trader over the last 7 days relative to the usual level of market supply that has been observed over the last 6 months.` }
        else if (colId == 'RetailDemand') { return `A comparison of the national demand for a vehicle on Auto Trader over the last 7 days relative to the level of demand that has been observed over the last 6 months. Demand is calculated by aggregating consumer activity on equivalent vehicles.` }
        else if (colId == 'NationalRetailMarketCondition') { return `A metric of the current market condition, taking into account the market supply and buyer demand for a vehicle.` }
        else if (colId == 'RetailMarketCondition') { return `A metric of the current market condition, taking into account the market supply and buyer demand for a vehicle, within a 50 mile radius of the dealership.` }
        
        else if (colId == 'SiteBrand') { return `The list of franchises which this site retails.` }
        else if (colId == 'VehicleType') { return `The type of vehicle being advertised e.g. car / van.` }
        else if (colId == 'BodyType') { return `The body type of the vehicle e.g. Hatchback / Saloon / Estate etc.` }
        else if (colId == 'ModelCleanedUp') { return `This is the model field, but cleaned up e.g. 'CLIO', 'Clio' and 'clio' will all be shown as 'Clio'` }
        else if (colId == 'ModelSellRate') { return `This shows the number of units of this model sold at this site within the last 30 days` }
        else if (colId == 'weeksCover') { return `This the number of days cover of this model based on the 30 day sales rate and current stock.` }
        else if (colId == 'VehicleTypeDesc') { return `This is the vehicle type as recorded in the DMS.` }
        else if (colId == 'AdLink') { return `This adds a link to the report which can be clicked to open the advert in a new tab.` }
        else if (colId == 'ImagesBand') { return `This summarises how many images there are for this advert, broken into 'No images', <4, <14, <26, <37<, <48 or 48+`}
        else if (colId == 'lastComment') { return `This shows the most recently made comment and initials of the commenter.`}
        else if (colId == 'RetailRating') { return `Retail Rating represents the retail potential of your vehicle when being sold at market value (100% price position). The rating is based on the derivative and age of the vehicle and is uniquely tailored to the exact location of your dealership.  The higher the Retail Rating, the quicker we predict you'll be able to sell it at the current market value. To calculate the rating we consider a number of key measures to predict how a vehicle is likely to perform including Average Days To Sell, Live Market Supply and Live Market Demand.`}
        else if (colId == 'DaysToSellAtCurrentSelling') { return `This is the average number of days we expect it will take to sell this vehicle at its current retail price from your location. You can use this as a guide to see how your pricing could affect the vehicle's speed of sale.  It's calculated for the whole of the UK and then adjusted for the variations we've observed locally in your area. Your local area is a 50-mile radius from your location, which has been chosen based on our latest insight into how buyers are searching for vehicles on Auto Trader.  Please note that this is a prediction based on the average time to sell we have observed over the last 6 months. Some vehicles will sell faster and some will sell slower, but the average should be the number of days shown.`}
        else if (colId == 'CompetitorCount') { return `Spark runs a national competitor check daily on each advert and includes the results in here.   The plate range to search around each advert can be updated on the site settings page.`}
        else if (colId == 'AveragePP') { return `The average price position of all competitors selling this vehicle nationally.`}
        else if (colId == 'LowestPP') { return `The lowest price position of all competitors selling this vehicle nationally.`}
        else if (colId == 'HighestPP') { return `The highest price position of all competitors selling this vehicle nationally.`}
        else if (colId == 'OurPPRank') { return `Where our vehicle sits within all competitor prices, when ranked best to worst on price PP%.   1 is best.`}
        else if (colId == 'OurValueRank') { return `Where our vehicle sits within all competitor prices, when ranked best to worst purely on price.   1 is best.`}
        else if (colId == 'CheapestSellerType') { return `The type of the cheapest seller.   P = Private, I = Independent, F = Franchised, O = Our vehicle`}
        else if (colId == 'CheapestVehicle') { return `Is a tick if we are the cheapest in the market.`}
        else if (colId == 'CheapestVehicle') { return `Is a tick if this vehicle is the only one of its kind in the market.`}
        else if (colId == 'PriceUpMaintainRank') { return `Details the amount by which the retail price could be increased without the advert becoming more expensive than the next competitor.` }
        else if (colId == 'PriceDownImproveRank') { return `Details the amount by which the retail price needs to be decreased to become cheaper than the next lower competitor.` }
        else if (colId == 'PriceToBeCheapest') { return `Details the amount by which the retail price needs to be decreased to be the cheapest.` }
        else if (colId == 'MarketPositionScore') { return `This is a score out of 100 indicating how low this advert is priced vs the competition.   An advert that is the cheapest out of 100 competitors will score 100.   Similarly an advert that is the most expensive out of 20 competitors will score 5.    Cheapest and most expensive is measured by Price Position %.` }
        else if (colId == 'SIV') { return `The standing value of this vehicle, taken from the DMS.` }
        else if (colId == 'PrepCost') { return `The accumulated prep costs on this vehicle, taken from the DMS.` }
        else if (colId == 'PricedProfit') { return `The profit on this vehicle, considering standing value and VAT status.` }
        else if (colId == 'HaveProfit') { return `A tick if Spark is able to show the profit on this vehicle.` }
        else if (colId == 'ValuationMktAvPartEx') { return `The part exchange valuation for this vehicle assuming the average level of specification.` }
        else if (colId == 'ValuationMktAvTrade') { return `The trade for this vehicle assuming the average level of specification.` }
        else if (colId == 'ValuationMktAvPrivate') { return `The private valuation for this vehicle assuming the average level of specification.` }
        else if (colId == 'ValuationMktAvRetailExVat') { return `The retail ex-VAT valuation for this vehicle assuming the average level of specification.` }
        else if (colId == 'ValuationMktAvRetail') { return `The retail valuation for this vehicle assuming the average level of specification.` }
        
        else if (colId == 'ValuationAdjAvPartEx') { return `The part exchange valuation for this vehicle based on the vehicle's actual specification.` }
        else if (colId == 'ValuationAdjAvTrade') { return `The trade for this vehicle based on the vehicle's actual specification.` }
        else if (colId == 'ValuationAdjAvPrivate') { return `The private valuation for this vehicle based on the vehicle's actual specification.` }
        else if (colId == 'ValuationAdjAvRetailExVat') { return `The retail ex-VAT valuation for this vehicle based on the vehicle's actual specification.` }
        else if (colId == 'ValuationAdjAvRetail') { return `The retail valuation for this vehicle based on the vehicle's actual specification.` }
        
        else if (colId == 'RelevantValuation') { return `The specific retail valuation for this vehicle.` }
        else if (colId == 'ThisVehicleValnVsAverage') { return `The specific retail valuation for this vehicle vs this vehicle with average specification.` }
        else if (colId == 'ValuationMonthPlus1') { return `The retail valuation for this vehicle in 1 month's time.` }
        else if (colId == 'ValuationMonthPlus2') { return `The retail valuation for this vehicle in 2 month's time.` }
        else if (colId == 'ValuationMonthPlus3') { return `The retail valuation for this vehicle in 3 month's time.` }
        
        else if (colId == 'AdvertisedPriceExclAdminFee') { return `The retail price for this vehicle, excluding the admin fee.` }
        else if (colId == 'PricePosition') { return `This shows the position of your current retail price relative to your vehicle’s market value. The Auto Trader adjusted retail price is used as the market value if available, otherwise the market average retail price is used.` }
        else if (colId == 'StrategyPriceHasBeenCalculated') { return `Shows a tick if the strategy price has been sucessfully calculated for this vehicle.` }
        else if (colId == 'StrategyPrice') { return `The strategy price that has been calculated for this advert using the prevailing pricing strategy active for this site in Spark.` }
        else if (colId == 'TestStrategyPrice') { return `This shows the test strategy price calculated for this advert using the currently selected test strategy for this site in Spark.` }
        else if (colId == 'VsStrategyPrice') { return `The difference between the current retail price and the strategy price.   A positive number indicates the advert is over-priced vs strategy price.` }
        else if (colId == 'VsTestStrategyPrice') { return `The difference between the current retail price and the test strategy price.   A positive number indicates the advert is over-priced vs test strategy price.` }
        else if (colId == 'PriceIndicatorRating') { return `The Price Indicator Rating is a feature that helps buyers understand how a vehicle's price compares to similar cars in the marketplace. AutoTrader calculates the Price Indicator by comparing the listed price of the vehicle with its own valuation based on market data. Some vehicles may not get a Price Indicator due to specific factors, such as being very new, rare, or if they are sold by private sellers. Cars priced below £1,500 or over £70,000, or that are over 15 years old, typically don’t receive an indicator.` }
        else if (colId == 'DMSSellingPrice') { return `The selling price for this vehicle, in the DMS.` }
        else if (colId == 'VsDMSSellingPrice') { return `The retail price of this advert on Autotrader, vs the selling price in the DMS.` }
        else if (colId == 'PhysicalLocation') { return `The physical location of this stock item, taken from the DMS.` }
        
        else if (colId == 'PerfRatingScore') { return `This is an overall view of how your advert performed yesterday. The rating considers three key measures: search appearances, advert views and leads & connections.` }
        else if (colId == 'PerfRating') { return `Excellent: Your advert generated much more response than expected; Above average: Your advert generated more response than expected; Below Average: Your advert generated less response than expected; Low: Your advert generated much less response than expected.` }
        else if (colId == 'SearchViews7Day') { return `This indicates the number of search appearences for this advert in the last 7 days.  A search appearance is when your advert has appeared in search results on a page viewed by a potential buyer.    There are several factors that can affect search appearances - namely right price, quality of ads & pay per click.` }
        else if (colId == 'AdvertViews7Day') { return `This indicates the number of advert views for this advert in the last 7 days.  An advert view is when your advert has been clicked on after appearing in a search – it is the equivalent of a buyer checking a vehicle on your forecourt. There are several factors that can affect advert views - right price, quality of images, number of images, details of the vehicle, pay per click etc.` }
        else if (colId == 'DailySearchViewsLast7') { return `This shows the search views over the last 7 days, in a sparkline format.` }
        else if (colId == 'DailyAdvertViewsLast7') { return `This shows the advert views over the last 7 days, in a sparkline format.` }
        
        else if (colId == 'IsOptedOut') { return `Shows a tick if this vehicle is currently opted out of automatic daily pricing  (only relevant for sites currently using Spark auto-pricing).` }
        else if (colId == 'OptedOutBy') { return `Who opted the advert out from automatic daily pricing.` }
        else if (colId == 'WhenOptedOut') { return `When the advert was opted out from automatic daily pricing.` }
        else if (colId == 'OptedOutUntil') { return `When the advert is opted out until.` }
        
        else if (colId == 'TotalPriceChanges') { return `The total value of all changes in price to this advert, since it was first listed.` }
        else if (colId == 'DailyPriceMovesCount') { return `The total quantity of price changes for this advert, since it was first listed.` }
        else if (colId == 'MostRecentDailyPriceMove') { return `The value of the most recent change in price for this advert.` }
        else if (colId == 'MostRecentDailyPriceMoveDate') { return `The date that this advert's price was most recently changed.` }
        else if (colId == 'DaysSinceMostRecentPriceMove') { return `Days since this advert was changed in price.` }

        else if (colId == 'LastPriceChangeValue') { return `The amount of the most recent change to this advert's price, carried out via Spark whilst the advert was opted-out from auto-pricing.   This is only relevant for sites that switched on for auto-pricing in Spark.` }
        else if (colId == 'DaysSinceLastPriceChange') { return `The amount of days since this advert's price was changed via Spark whilst the advert was opted-out from auto-pricing.   This is only relevant for sites that switched on for auto-pricing in Spark.` }
        else if (colId == 'WhenPriceLastManuallyChanged') { return `When this advert was most recently changed via Spark whilst the advert was opted-out from auto-pricing.   This is only relevant for sites that switched on for auto-pricing in Spark.` }
        else if (colId == 'WhoLastManuallyChanged') { return `Who most recently changed this advert's price via Spark whilst the advert was opted-out from auto-pricing.   This is only relevant for sites that switched on for auto-pricing in Spark.` }
        else if (colId == 'TotalManualPriceChangesCount') { return `The total number of changes made to this advert's price via Spark whilst the advert was opted-out from auto-pricing.   This is only relevant for sites that switched on for auto-pricing in Spark.` }
        else if (colId == 'TotalManualPriceChangesValue') { return `The total value of changes made to this advert's price via Spark whilst the advert was opted-out from auto-pricing.   This is only relevant for sites that switched on for auto-pricing in Spark.` }
        
        else if (colId == 'NewPrice') { return `The recommended new price for this vehicle based on the strategy price that was calculated this morning.  ` }
        else if (colId == 'NewPriceExAdminFee') { return `The recommended new price for this vehicle, excluding the admin fee (the admin fee can be set within the site settings page in Spark).` }
        else if (colId == 'NewPP') { return `The price position % at this new price.` }
        else if (colId == 'todayPriceChange') { return `The difference between the recommended new price, and the current retail price on this advert.` }
        else if (colId == 'IsSmallPriceChange') { return `Whether the current retail price is within a small amount (defined on the site settings page) of the strategy price.  Small changes do not appear by default on the price changes page, or in the daily email.` }
        else if (colId == 'DaysToSellAtNewPrice') { return `How many days this vehicle will take to sell, based on the new price.` }
        else if (colId == 'PriceIndicatorAtNewPrice') { return `The price indicator for the new price.` }
        
        else if (colId == 'AutotraderAdvertStatus') { return `The Autotrader advert status.   For example CAPPED, NOT_PUBLISHED, REJECTED, PUBLISHED.   For groups who feed in DMS stock, this will also indicate if a vehicle has been included that is in DMS stock but not on Autotrader.` }
        else if (colId == 'AdvertiserAdvertStatus') { return `The advertiser advert status.` }
        else if (colId == 'PortalOptions') { return `The options which have been chosen for this vehicle.` }
        else if (colId == 'DaysToAdvertise') { return `This shows the difference in days between the date the vehicle was in stock per the DMS and the date the vehicle was first on forecourt on Autotrader.` }
        
        else if (colId == 'PPAverageFranchised') { return `This shows the average price position % across franchised advertisers..` }
        else if (colId == 'PPAverageIndependents') { return `This shows the average price position % across independent advertisers..` }
        else if (colId == 'PPAverageSupermarkets') { return `This shows the average price position % across supermarket advertisers..` }
        else if (colId == 'PPAveragePrivates') { return `This shows the average price position % across private advertisers..` }
        

        
        return '';
    }
}