﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection.PortableExecutable;

namespace CPHI.Spark.Loader.Services.GenericStockLoader
{

   public class Enterprise_StockJobService : IGenericStockJobServiceParams
   {

      public int RowsToSkip { get; set; }
      public bool IncludePreviousUses { get; set; }
      public bool IncludeAccountStatuses { get; set; }
      public DealerGroupName DealerGroupName { get; set; }
      public string FileExt { get; set; }
      public string DbConnectionName { get; set; }
      public string DbConnectionString { get; set; }
      public string[] AllMatchingFiles { get; set; }
      public string FinalSPToRun { get; set; }
      public bool TriggerUpdate { get; set; }
      public string JobName { get; set; }
      public string? TabName { get; set; }
      public string OnlyForSiteIds { get; set; }
      public Enterprise_StockJobService(string incomingRoot)
      {
         GetMatchingFilesAndImportParams(incomingRoot);
      }


      public void GetMatchingFilesAndImportParams(string incomingRoot)
      {
         DealerGroupName = DealerGroupName.Enterprise;
         RowsToSkip = 0;
         JobName = string.Concat(DealerGroupName.Enterprise.ToString(), "StocksJob");
         IncludePreviousUses = false;
         IncludeAccountStatuses = false;
         FileExt = ".xlsx";
         DbConnectionString = ConfigService.autopriceConnectionString;
         DbConnectionName = "AutoPriceConnection";
         AllMatchingFiles = Directory.GetFiles(incomingRoot, $"*Enterprise stock*");
         TriggerUpdate = true;
         TabName = "Sheet1";

      }

      public DataTable ConvertToDataTable(List<Stock> toReturn)
      {
         var dataTable = toReturn.ToDataTable();

         dataTable.Columns.Remove("PreviousSite");
         dataTable.Columns.Remove("Site");
         dataTable.Columns.Remove("DisposalRoute");
         dataTable.Columns.Remove("ProgressCode");
         dataTable.Columns.Remove("VehicleType");
         dataTable.Columns.Remove("AccountStatus");
         dataTable.Columns.Remove("PreviousUse");
         dataTable.Columns.Remove("VehicleAdvert");

         return dataTable;
      }


      public Stock ConvertRowToStock(List<string> rowCells, GenericStockLoaderDbLookups lookups, Dictionary<string, int> headerDictionary)
      {

         Stock newRow = new Stock();

         Site site = lookups.sites.First(); //put all to first site
         newRow.Site_Id = site != null ? site.Id : null;

         newRow.Reg = rowCells[headerDictionary["REGNO"]]; //
         string mileAge = rowCells[headerDictionary["ODOM"]];
         newRow.Mileage = mileAge == "" ? 0 : int.Parse(mileAge); //
         newRow.StockNumberFull =  rowCells[headerDictionary["UNITNO"]]; //
         int daysListed = int.Parse(rowCells[headerDictionary["DA"]]);
         DateTime stockDate = DateTime.Now.Date.AddDays(daysListed * -1);
         newRow.StockDate = stockDate; //
         newRow.BranchStockDate = stockDate; //
         newRow.Selling = RowInterpretationService.GetDecimal(rowCells[headerDictionary["GT ADJ"]]);
         //newRow.Make = rowCells[headerDictionary["MAKE"]];
         //newRow.Model = rowCells[headerDictionary["MODL"]];
         //newRow.Fuel = rowCells[headerDictionary["F"]];
         //newRow.Description = rowCells[headerDictionary["VERSION"]];
         //newRow.Options = rowCells[headerDictionary["OPTIONAL EQUIP"]];
         //newRow.Colour = rowCells[headerDictionary["EXT COLOUR"]];


         //newRow.IsVatQ = RowInterpretationService.GetVatQualifying(rowCells[headerDictionary["VAT"]]); //
         //newRow.Colour = rowCells[headerDictionary["COLOUR/WHEELBASE"]];
         //newRow.Fuel = rowCells[headerDictionary["FUEL"]]; //
         //newRow.CapCode = rowCells[headerDictionary["CAP CODE"]]; //

         //newRow.StockNumber = ExtractNumbers(rowCells[headerDictionary[""]]);

         // Sometimes this can be null
         //string regDateString = rowCells[headerDictionary["DATE AVAILABLE FOR SALE"]];
         //DateTime? regDate = regDateString == "" ? null : DateTime.Parse(rowCells[headerDictionary["REG. DATE"]]); //
         //newRow.RegDate = regDate;

         //DateTime branchStockDate = DateTime.Parse(rowCells[headerDictionary["AVAILABLE FOR SALE"]]); //
         //newRow.BranchStockDate = branchStockDate; //       

         //newRow.Purchased = RowInterpretationService.GetDecimal(rowCells[headerDictionary["WASPRICE"]]);
         //newRow.OriginalPurchasePrice = RowInterpretationService.GetDecimal(rowCells[headerDictionary["WASPRICE"]]);

         //string description = rowCells[headerDictionary["VERSION"]]; //

         // This field can be no longer than 50 chars
         //if (description.Length > 50)
         //{
         //   description = description.Substring(0, 50);
         //}

         //newRow.Description = description; //

         //newRow.Options = rowCells[headerDictionary["OPTIONS"]];

         // These FKs will always be Retail for now
         int disposalRouteId = lookups.disposalRoutes.Where(x => x.Description == "Retail").First().Id;
         newRow.DisposalRoute_Id = disposalRouteId;

         int vehTypeId = lookups.vehicleTypes.Where(x => x.Description == "Retail").First().Id;
         newRow.VehicleType_Id = vehTypeId;
         newRow.LastUpdatedDate = DateTime.Now;

         return newRow;
      }




      public Dictionary<string, int> BuildHeaderDictionary(List<string> headers)
      {
         var toUpper = headers.Select(x => x.ToUpper()).ToList();
         Dictionary<string, int> result = new Dictionary<string, int>
            {
               { "UNITNO", toUpper.IndexOf("UNITNO") },
               { "REGNO", toUpper.IndexOf("REGNO") },
               { "ODOM", toUpper.IndexOf("ODOM") },
               { "GT ADJ", toUpper.IndexOf("GT ADJ") },
               { "DA", toUpper.IndexOf("DA") }

               //{ "MAKE", toUpper.IndexOf("MAKE") },
               //{ "MODL", toUpper.IndexOf("MODL") },
               //{ "SYR", toUpper.IndexOf("SYR") },
               //{ "TRIM", toUpper.IndexOf("TRIM") },
               //{ "SERS", toUpper.IndexOf("SERS") },
               //{ "GB", toUpper.IndexOf("GB") },
               //{ "F", toUpper.IndexOf("F") },
               //{ "VERSION", toUpper.IndexOf("VERSION") },
               //{ "OPTIONAL EQUIP", toUpper.IndexOf("OPTIONAL EQUIP") },
               //{ "EXT COLOUR", toUpper.IndexOf("EXT COLOUR") },
            };

         return result;
      }



   


   }
}
