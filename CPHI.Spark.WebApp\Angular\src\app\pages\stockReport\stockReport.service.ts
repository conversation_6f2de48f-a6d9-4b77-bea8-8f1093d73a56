
import { EventEmitter, Injectable } from '@angular/core';
import { TopBottomHighlightRule } from "src/app/model/TopBottomHighlightRule";
import { StockMerchSiteRow } from 'src/app/model/sales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { StockSiteRow } from './stockReport.component';

@Injectable({
  providedIn: 'root'
})



export class StockReportService {

    

    stockRowData: StockSiteRow[];
    usedMerchRowData: StockMerchSiteRow[];
    overageRowData: StockSiteRow[];
    topBottomHighlightsUsed: TopBottomHighlightRule[] = [];
    topBottomHighlightsAllStock: TopBottomHighlightRule[] = [];
    topBottomHighlightsOverAge: TopBottomHighlightRule[] = [];
    topBottomHighlightsMerch: TopBottomHighlightRule[] = [];


    filterUpdated: EventEmitter<any> = new EventEmitter();

    reportName = 'Used Stock';
    showFranchisePicker: boolean;
    
    constructor(
        public getData: GetDataMethodsService,
        public constants: ConstantsService,
        public selections: SelectionsService
    ) { }

    ngOnInit()
    {

      if(this.constants && this.constants.translatedText){
        this.reportName = this.constants.translatedText[this.constants.environment.stockReport.initialStockReport];
      }
    }


}
