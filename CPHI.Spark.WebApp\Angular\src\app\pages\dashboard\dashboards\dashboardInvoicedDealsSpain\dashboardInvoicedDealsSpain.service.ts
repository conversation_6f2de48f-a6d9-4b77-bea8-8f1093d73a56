
import { EventEmitter, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CphPipe } from 'src/app/cph.pipe';
import { DistrinetService } from 'src/app/pages/distrinet/distrinet.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { DashboardService } from '../../dashboard.service';
import { VNTileParams } from "../../../../model/VNTileParams";
import { DashboardMeasure } from "../../../../model/DashboardMeasure";
import { DashboardDataFrameParams, DealLatestVM } from './dashboardInvoicedDealsSpain.model';
import { VNTileTableRow } from 'src/app/model/VNTileTableRow';
import { BIChartTileDataType } from 'src/app/components/biChartTile/biChartTile.component';
import { MenuItemNew } from 'src/app/model/main.model';

@Injectable({
  providedIn: 'root'
})


export class SpainDashboardInvoicedDealsService {

 

  filterChoices: DashboardMeasure[]
  highlightChoices: DashboardMeasure[]

  department: string;
  chosenSnapshotDate: Date;

  rawData: DealLatestVM[];
  rawDataFiltered: DealLatestVM[];
  rawDataHighlighted: DealLatestVM[]; //subset of filtered.  
  snapshotChoices: Date[]

  refreshFilterListsEmitter: EventEmitter<void> = new EventEmitter();
  refreshTileEmitter: EventEmitter<void> = new EventEmitter();
  highlightChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();
  filterChoiceMadeEmitter: EventEmitter<void> = new EventEmitter();



  constructor(
    public getDataService: GetDataMethodsService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public distrinetPageService: DistrinetService,
    private router: Router,

    public dashboardService: DashboardService,

    
  ) { }


  



  initParams() {


    this.filterChoices = [
      { FieldName: 'Franchise', FieldNameTranslation: this.constants.translatedText.Franchises, IsDate: false, ChosenValues: [] },

      
      { FieldName: 'StatusCode', FieldNameTranslation: this.constants.translatedText.OrderDate + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Make', FieldNameTranslation: this.constants.translatedText.Make, IsDate: false, ChosenValues: [] },
      { FieldName: 'RegionDescription', FieldNameTranslation: this.constants.translatedText.Region + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'SegmentCode', FieldNameTranslation: this.constants.translatedText.Segment + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'MarketCode', FieldNameTranslation: this.constants.translatedText.Market + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'SiteDescription', FieldNameTranslation: this.constants.translatedText.Sites, IsDate: false, ChosenValues: [] },
      { FieldName: 'ModelCode', FieldNameTranslation: this.constants.translatedText.Distrinet_ModelCode + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Salesman', FieldNameTranslation: this.constants.translatedText.OrderDate + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'OrderType', FieldNameTranslation: this.constants.translatedText.OrderType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Variant', FieldNameTranslation: this.constants.translatedText.Model + 's', IsDate: false, ChosenValues: [] },

    ];

    this.highlightChoices = [
      { FieldName: 'Franchise', FieldNameTranslation: this.constants.translatedText.Franchises, IsDate: false, ChosenValues: [] },
      
            
      { FieldName: 'StatusCode', FieldNameTranslation: this.constants.translatedText.OrderDate + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Make', FieldNameTranslation: this.constants.translatedText.Make, IsDate: false, ChosenValues: [] },
      { FieldName: 'RegionDescription', FieldNameTranslation: this.constants.translatedText.Region + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'SegmentCode', FieldNameTranslation: this.constants.translatedText.Segment + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'MarketCode', FieldNameTranslation: this.constants.translatedText.Market + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'SiteDescription', FieldNameTranslation: this.constants.translatedText.Sites, IsDate: false, ChosenValues: [] },
      { FieldName: 'ModelCode', FieldNameTranslation: this.constants.translatedText.Distrinet_ModelCode + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Salesman', FieldNameTranslation: this.constants.translatedText.OrderDate + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'OrderType', FieldNameTranslation: this.constants.translatedText.OrderType + 's', IsDate: false, ChosenValues: [] },
      { FieldName: 'Variant', FieldNameTranslation: this.constants.translatedText.Model + 's', IsDate: false, ChosenValues: [] },
    ];



  }


  // getSnapshotOptions() {
  //   this.getDataService.getDistrinetSnapshotDates('orders').subscribe(res => {
  //     this.snapshotChoices = res;
  //     this.snapshotChoices.unshift(null)
  //   })
  // }

  getPageParams(): VNTileParams {
    return {
      highlightChoices: this.highlightChoices,
      filterChoices: this.filterChoices,
      //rawData: this.rawData,
      //rawDataFiltered: this.rawDataFiltered,
      //rawDataHighlighted: this.rawDataHighlighted,
      filterChoiceHasBeenMade: this.filterChoiceMadeEmitter,
      highlightChoiceHasBeenMade: this.highlightChoiceMadeEmitter,
      updateThisPicker: this.refreshFilterListsEmitter,
      updateThisTile: this.refreshTileEmitter,
      parentMethods: {
        buildRows: (fieldName, dataType) => this.buildTableRows(fieldName, dataType),
        highlightRow: (row, fieldName) => this.highlightRow(row, fieldName),
        provideItemsList:(fieldName, isDateField)=>this.provideItemsList(fieldName,isDateField),
      }
    }
  }



  highlightRow(row: VNTileTableRow, fieldName: string) {

    try 
    {
      let userChoice = this.highlightChoices.find(x => x.FieldName === fieldName);
      let isItemSelected = userChoice.ChosenValues.length === 0 || userChoice.ChosenValues.includes(row.Label);
      if (userChoice.ChosenValues.length === 0) {
        this.highlightChoices.find(x => x.FieldName === fieldName).ChosenValues = [row.Label];
      } else if (isItemSelected) {
        userChoice.ChosenValues = userChoice.ChosenValues.filter(x => x !== row.Label)
      } else {
        userChoice.ChosenValues.push(row.Label)
      }
    }
    catch 
    {
      
    }


  }

  getData(unselectTrade:boolean): void {

    this.selections.triggerSpinner.emit({ show: true, message: this.constants.translatedText.Loading })
    let params: DashboardDataFrameParams = this.getParms(this.chosenSnapshotDate)

    this.getDataService.getDashboardInvoicedDealsSpain(params).subscribe((res: DealLatestVM[]) => {
      // res.map(x => {
      //   x.OrderDate = new Date(x.OrderDate)
      // })

      this.rawData = res;
      this.rawDataFiltered = this.filterData(this.rawData, this.filterChoices);;
      this.rawDataHighlighted = this.filterData(this.rawDataFiltered, this.highlightChoices)
      this.refreshFilterListsEmitter.emit();
      this.refreshTileEmitter.emit();
      this.selections.triggerSpinner.next({ show: false });

      if(unselectTrade){
        this.setNoTrade();
      }
    }, error => {
      console.error("ERROR: ", error);
    });


  }

  setNoTrade(){
     //get the rows:
     const orderTypeRows = this.buildTableRowsNonDatesBasis('OrderType');
     this.highlightRow(orderTypeRows.find(x=>x.Label=='Particulares'), 'OrderType');
     //this.highlightRow(orderTypeRows.find(x=>x.Label=='Compraventas'), 'OrderType');
     //this.highlightRow(orderTypeRows.find(x=>x.Label=='Inmovilizado'), 'OrderType');


     this.highlightItems();
  }

  selectSnapshot(snapshot: Date) {
    this.chosenSnapshotDate = snapshot;
    this.getData(false)
  }


  isHighlightFiltersOn() {
    let isHighlights = false;
    let i = 0;
    while (!isHighlights && i < this.highlightChoices.length) {
      isHighlights = this.highlightChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isHighlights;
  }

  isFiltersOn() {
    if (!this.filterChoices) { return false; }
    let isFilters = false;
    let i = 0;
    while (!isFilters && i < this.filterChoices.length) {
      isFilters = this.filterChoices[i].ChosenValues.length !== 0;
      i++;
    }
    return isFilters;
  }


  private getParms(snapShot: Date): DashboardDataFrameParams {
    return {
      SiteIds: this.dashboardService.chosenSites.map(x => x.SiteId),
      SnapshotDate: snapShot,
      Department: this.department
    };
  }

  clearHighlights() {
    this.highlightChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.highlightItems();
    this.refreshTileEmitter.emit();
  }
  clearFilters() {
    this.filterChoices.forEach(choice => {
      choice.ChosenValues = [];
    })
    this.filterItems();
    this.refreshTileEmitter.emit();
  }


  filterData(dataIn: DealLatestVM[], stringFilterChoices: DashboardMeasure[]): DealLatestVM[] {
    let results = [];
    dataIn.forEach(item => {
      //check all chosen strings
      let filterOutThisItem: boolean = false;
      stringFilterChoices.forEach(choice => {
        if (choice.IsDate) {
          let itemPropertyAsDateString = this.cphPipe.transform(item[choice.FieldName], 'date', 0);
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(itemPropertyAsDateString)) { filterOutThisItem = true; } }
        } else {
          if (!filterOutThisItem && choice.ChosenValues.length !== 0) { if (!choice.ChosenValues.includes(item[choice.FieldName])) { filterOutThisItem = true; } }
        }
      })
      // check all chosen dates
      // if (!filterOutThisItem) {
      //   Object.keys(dateFilterChoices).forEach(key => {
      //     if (!filterOutThisItem && dateFilterChoices[key].length !== 0) { if (!dateFilterChoices[key].includes(item[key])) { filterOutThisItem = true; } }
      //   })
      // }

      if (!filterOutThisItem) { results.push(item) }
    })

    return results;
  }




  filterItems() {
    //have chosen ok from a dropdown picker
    this.rawDataFiltered = this.filterData(this.rawData, this.filterChoices)
    this.highlightItems()
  }

  highlightItems() {
    //have clicked a row in a tile.  
    this.rawDataHighlighted = this.filterData(this.rawDataFiltered, this.highlightChoices)
    this.refreshTileEmitter.emit()
  }


  



  navigateToDistrinetPage() {
    //initialise distrinet page
    this.distrinetPageService.initParams()
    this.distrinetPageService.chosenOriginTypes = ['Orders']
    this.distrinetPageService.chosenSnapshot = this.chosenSnapshotDate;

    //set franchise codes on distrinet page
    let filterFranchises = this.filterChoices.find(x => x.FieldName === 'Franchise').ChosenValues;
    let highlightFranchises = this.highlightChoices.find(x => x.FieldName === 'Franchise').ChosenValues;
    let chosenFranchises = this.intersectChoices(filterFranchises, highlightFranchises);
    if (chosenFranchises.length === 0) {
      this.distrinetPageService.chosenFranchises = this.constants.FranchiseCodes;
    } else {
      this.distrinetPageService.chosenFranchises = this.highlightChoices.find(x => x.FieldName === 'Franchise').ChosenValues.map(x => x.substring(0, 1).toUpperCase());
    }


    //build up filter model to set on the distrinet page
    let filterModel = {};
    //let filterChoicesNoFran = this.filterChoices.filter(x => x.FieldName !== 'Franchise');
    //let highlightChoicesNoFran = this.highlightChoices.filter(x => x.FieldName !== 'Franchise');
    this.filterChoices.forEach((choice, i) => {
      let filterChoices = choice.ChosenValues;
      let highlightChoices = this.highlightChoices[i].ChosenValues;
      let chosenItems = this.intersectChoices(filterChoices, highlightChoices);
      if (chosenItems.length > 1) {
        if (choice.IsDate){
          filterModel[choice.FieldName] = { filterType: 'date', operator: 'OR' }
        } else{
        filterModel[choice.FieldName] = { filterType: 'text', operator: 'OR' }
        }
        let i = 0;
        chosenItems.forEach(value => {
          if (choice.IsDate){
            filterModel[choice.FieldName][`condition${i + 1}`] = {
              filterType: 'date', type: 'equals', dateFrom: this.constants.deductTimezoneOffset(new Date(this.cphPipe.transform(value, 'date', 0))).toISOString().split('T')[0], dateTo: null
            }
          }
          else{
          filterModel[choice.FieldName][`condition${i + 1}`] = {
            filterType: 'text', type: 'contains', filter: value
          }
        }
          i++;
        })
      } else if (chosenItems.length > 0) {
        if (choice.IsDate){
          filterModel[choice.FieldName] = { filterType: 'date', type: 'equals', dateFrom: this.constants.deductTimezoneOffset(new Date(this.cphPipe.transform(chosenItems[0], 'date', 0))).toISOString().split('T')[0] , dateTo: null }  
        }
        else{
          filterModel[choice.FieldName] = { filterType: 'text', type: 'contains', filter: chosenItems[0] }
        }
      }
    })

    this.distrinetPageService.filterModel = filterModel;
    
    let menuItem: MenuItemNew | undefined = this.constants.getMenuItemFromUrl('/distrinet');
    if (menuItem) { this.constants.navigateByUrl(menuItem); } //, 'operationreports'
  }

  provideItemsList(fieldName:string,isDateField:boolean){
    if (!this.rawData) { return [] }
        return [...new Set(this.rawData.map(x => x[fieldName]))]
  }

  buildTableRows(fieldName: string, dataType: BIChartTileDataType) {
    let tableRows: VNTileTableRow[] = [] 
    if (dataType===BIChartTileDataType.day) {
      tableRows = this.buildTableRowsDatesBasis(fieldName)
    } else {
      tableRows =  this.buildTableRowsNonDatesBasis(fieldName)
    }

    //limit to 20
    return tableRows.slice(0,20);
  }

  buildTableRowsNonDatesBasis(fieldName: string): VNTileTableRow[] {
    let tableRows: VNTileTableRow[] = [];

    //go through filteredData to find unique labels and countup
    let labels: string[] = [];
    this.rawDataFiltered.forEach(item => {
      const itemLabel = item[fieldName];
      let labelsIndex = labels.findIndex(x => x === itemLabel);
      if (labelsIndex === -1) {
        labels.push(itemLabel)
        tableRows.push({ Label: itemLabel, FilteredTotal: 1, HighlightedTotal: 0 })
      } else {
        tableRows[labelsIndex].FilteredTotal++;
      }
    })

    //find out values to show
    this.rawDataHighlighted.forEach(item => {
      const itemLabel = item[fieldName];
      const indexInLabels = labels.findIndex(x => x === itemLabel);
      //should exist or something has gone wrong as orderDataHighlighted should be subset of orderDataFiltered
      tableRows[indexInLabels].HighlightedTotal+= item.Total;
    })

    tableRows = tableRows.sort((a, b) => b.FilteredTotal - a.FilteredTotal)

    return tableRows;

  }




  buildTableRowsDatesBasis(fieldName: string): VNTileTableRow[] {
    //similar approach to non-dates, but looks odd if we have gaps in the days, so we find the earliest date then iterate every day at a time since then even if it has no data
    let earliestDate: Date = this.rawDataFiltered.map(x => x[fieldName]).sort((a, b) => a.getTime() - b.getTime())[0];
    let latestDate: Date = this.rawDataFiltered.map(x => x[fieldName]).sort((a, b) => b.getTime() - a.getTime())[0];

    let tableRows = [];
    let currentDate = new Date(earliestDate);
    //walk through creating tableRows, with zero values
    while (!this.constants.datesAreSame(currentDate, latestDate)) {
      const label = this.cphPipe.transform(currentDate, 'date', 0);
      tableRows.push({ Label: label, FilteredTotal: 0, HighlightedTotal: 0 })
      currentDate = this.constants.addDays(currentDate, 1);
    }

    //update these new rows with orderData
    const orderDataSorted = this.rawDataFiltered.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    let currentTableRowIndex: number = 0;
    orderDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].FilteredTotal++; }
    })

    //update with highlighted data
    const highlightedDataSorted = this.rawDataHighlighted.sort((a, b) => a[fieldName].getTime() - b[fieldName].getTime());
    currentTableRowIndex = 0;
    highlightedDataSorted.forEach(dataItem => {
      let currentDayLabel = this.cphPipe.transform(dataItem[fieldName], 'date', 0);
      while (!!tableRows[currentTableRowIndex] && currentDayLabel !== tableRows[currentTableRowIndex].Label) {
        currentTableRowIndex++
      }
      if (!!tableRows[currentTableRowIndex]) { tableRows[currentTableRowIndex].HighlightedTotal++; }
    })
    return tableRows

  }



  private intersectChoices(filterFranchises: string[], highlightFranchises: string[]) {
    let chosenFranchises = [];
    if (filterFranchises.length !== 0) {
      if (highlightFranchises.length !== 0) {
        chosenFranchises = filterFranchises.filter(x => highlightFranchises.includes(x));
      } else {
        chosenFranchises = filterFranchises;
      }
    } else {
      if (highlightFranchises.length !== 0) {
        chosenFranchises = highlightFranchises;
      } else {
        chosenFranchises = [];
      }
    }
    return chosenFranchises;
  }


}
