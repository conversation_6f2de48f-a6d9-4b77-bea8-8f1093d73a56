{

  //-------------------------------------------------x
  // THIS IS THE ADDITIONAL PROPERTIES USED BY RRG dev
  //-------------------------------------------------x
   "ConnectionStrings": {
      "DefaultConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkAutoPrice; Persist Security Info=true; User ID=SparkAutoPriceUser; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "RRGUKConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkrrg; Persist Security Info=true; User ID=sparkRRGUser; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "RRGSpainConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkRRGspain; Persist Security Info=true; User ID=sparkSpainUser; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "VindisConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkVindis; Persist Security Info=true; User ID=sparkVindisUser; Password=***************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "SytnerConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=sparkSytner; Persist Security Info=true; User ID=SparkSytnerUser; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;",
      "StockTakePhotos": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=stockpulse; Persist Security Info=true; User ID=StockpulseRo; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;"
   },

  "AppSettings": {
    "Environment": "prod",
    "ConfirmLeaverEmail": "false"
  },

  "BlobStorage": {
    //"Environment": "renault",
    "StorageAccount": "stockpulseimages",
    "StorageAccountKey": "****************************************************************************************",
    "ProfileFilePath": "******************************************************************/",
    "VehicleFilePath": "************************************************************/",
    "ReadOnlyKey": "sp=r&st=2022-05-04T14:16:11Z&se=2051-11-30T22:16:11Z&spr=https&sv=2020-08-04&sr=c&sig=omIg02aQdEU5pJdsv8m8d7rMDcx0Qef1cw905MVRqFY%3D"
  },

  "WebApp": {
    "URL": "https://spark.cphi.co.uk"
  },

  "AutotraderSettings": {
    "ApiKey": "CPHInsight-PricingBI-22-07-24",
    "ApiSecret": "nyGQrMe4oUVTM7iGPZdgghrDP8nTeZKw",
    "BaseURL": "https://api.autotrader.co.uk",
    "BaseURLForChangingPrices": "https://api-sandbox.autotrader.co.uk"
  }




}
