<div  class="tileHeader" >
    <div class="headerWords">
      <h5>{{ title }}</h5>
      <div class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
    </div>
  </div>
<div class="dashboard-tile-inner">
    <div class="dataRow imageRow">
        <div *ngFor="let invoicingPerformance of data.BreakdownByBrand" class="dataCol">
        <img class="brandImage" *ngIf="constants.getBrandImage(invoicingPerformance.Brand)" [src]="constants.getBrandImage(invoicingPerformance.Brand)" alt="{{invoicingPerformance.Brand}}">

        <span *ngIf="!constants.getBrandImage(invoicingPerformance.Brand) && invoicingPerformance.Brand != 'Non-franchise'">{{invoicingPerformance.Brand}}</span>
        <span *ngIf="!constants.getBrandImage(invoicingPerformance.Brand) && invoicingPerformance.Brand == 'Non-franchise'">{{constants.translatedText.NonFranchise}}</span>

    </div>
    </div>
    <div class="headerWords headerTitle"><h5>{{ constants.translatedText.Dashboard_InvoicingPerformance_InvoicedProfit }} %</h5></div>
    <div class="dataRow">
        <span *ngFor="let invoicingPerformance of data.BreakdownByBrand" class="dataCol">{{invoicingPerformance.InvoicedProfit|cph:'percent':1}}</span>
    </div>
    <div class="headerWords headerTitle"><h5>{{ constants.translatedText.Dashboard_InvoicingPerformance_UnitInvoicingVsTarget }} %</h5></div>
    <div class="dataRow">
        <span  *ngFor="let invoicingPerformance of data.BreakdownByBrand" class="dataCol" >{{invoicingPerformance.UnitInvoicingVsTgt|cph:'percent':1}}</span>
    </div>
</div>
