import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CellClickedEvent, ColumnApi, FilterChangedEvent, GridOptions, GridReadyEvent, RowDoubleClickedEvent, ValueGetterParams } from 'ag-grid-community';
import { AutoPriceInsightsModalComponent } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import { CustomHeaderNew } from 'src/app/components/customHeader/customHeader.component';
import { CphPipe } from 'src/app/cph.pipe';
import { BuildTotalAndAverageRowsParams } from 'src/app/model/BuildTotalAndAverageRowsParams';
import { CPHColDef } from 'src/app/model/CPHColDef';
import { CPHColGroupDef } from 'src/app/model/CPHColGroupDef';

import { GetPriceChangesParams } from 'src/app/model/GetPriceChangesParams';
import { PricingChange } from 'src/app/model/PricingChange';
import { PricingChangeNew } from 'src/app/model/PricingChangeNew';
import { SetPriceChangesParams } from "src/app/model/SetPriceChangesParams";

import { AGGridMethodsService } from 'src/app/services/agGridMethods.service';
import { AutopriceRendererService } from 'src/app/services/autopriceRenderer.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ExcelExportService } from 'src/app/services/excelExportService';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';

import { Subscription } from 'rxjs';
import { AutoPriceInsightsModalService } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service';
import { CustomHeaderService } from 'src/app/components/customHeader/customHeader.service';
import { TodayPricesService } from './todayPrices.service';
import { CellClassParams } from 'ag-grid-community';
import { GlobalParamsService } from 'src/app/services/globalParams.service';
import { GlobalParamKey } from 'src/app/model/GlobalParam';
import { CustomHeaderAdDetail } from 'src/app/components/customHeaderAdDetail/customHeaderAdDetail.component';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';

@Component({
  selector: 'app-todayPrices',
  templateUrl: './todayPrices.component.html',
  styleUrls: ['./todayPrices.component.scss']
})
export class TodayPricesComponent implements OnInit {
  public components: { [p: string]: any; } = {
    agColumnHeader: CustomHeaderAdDetail
  };

  gridOptions: GridOptionsCph;

  constructor(
    public service: TodayPricesService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public gridHelpersService: AGGridMethodsService,
    public columnTypeService: ColumnTypesService,
    public getData: GetDataMethodsService,
    public customHeader: CustomHeaderService,
    public modalService: NgbModal,
    public excel: ExcelExportService,
    public cphPipe: CphPipe,
    private autopriceRendererService: AutopriceRendererService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService,
    private globalParmsService: GlobalParamsService
  ) { }

  ngOnInit() {
    if (!this.service.includeUnPublishedAds) {
      this.service.includeUnPublishedAds = (this.globalParmsService.getGlobalParam(GlobalParamKey.webAppShowDefaultShowUnPublishedVehicles) as boolean);
    }
    if (!this.service.includeNewVehicles) {
      this.service.includeNewVehicles = this.constants.autopriceEnvironment.defaultShowNewVehicles;
    }
    if (!this.service.priceChangesRowData) {
      this.getPriceChangesData()
    } else {
      this.setGridOptions();
    }
  }

  ngOnDestroy() {
    this.service.gridApi = null;
  }

  getPriceChangesData() {
    this.selections.triggerSpinner.next({ message: 'Loading...', show: true });

    const params: GetPriceChangesParams = {
      retailerSiteIds: this.service.chosenRetailerSiteIds != null || this.service.chosenRetailerSiteIds?.length > 0 ? this.service.chosenRetailerSiteIds : [],
      date: this.service.chosenDate,
      showSmallPriceChanges: this.service.showSmallPriceChanges,
      includeNewVehicles: this.service.includeNewVehicles,
      includeUnPublishedAds: this.service.includeUnPublishedAds
    }

    this.getData.getPriceChanges(params).subscribe((res: PricingChangeNew[]) => {

      // If WasPrice == 0, net will always be zero (prevents net being inflated)
      res.map(x => x.net = (x.ChangeValueUp??0) + (x.ChangeValueDown??0));
    
      this.service.priceChangesRowData = res;

      if (this.service.gridApi) {
        this.service.gridApi.setRowData(this.service.priceChangesRowData);
        this.service.gridApi.redrawRows();
      } else {
        this.setGridOptions();
      }

      this.selections.triggerSpinner.next({ show: false });
    }, (error: any) => {
      console.error('Failed to retrieve price changes', error);
      this.selections.triggerSpinner.next({ show: false });
    });
  }

  setGridOptions() {
    this.gridOptions = {
      getContextMenuItems: (params) => this.gridHelpersService.getContextMenuItems(params, true),
      context: { thisComponent: this },
      suppressPropertyNamesCheck: true,
      suppressAggFuncInHeader: true,
      rowData: this.service.priceChangesRowData,
      onGridReady: (params) => this.onGridReady(params),
      onFirstDataRendered: (params) => this.onDataFirstRendered(params),
      getMainMenuItems: (params) => this.customHeader.getMainMenuItems(params),
      getRowHeight: (params) => {
        let normalHeight = Math.min(25, Math.max(15, Math.round(33 * this.selections.screenHeight / 960)));
        if (params.node.rowPinned) {
          return this.gridHelpersService.getRowPinnedHeight();
        } else {
          return this.gridHelpersService.getStandardHeight();
        }
      },
      //isFullWidthRow: params => !params.rowNode.group,
      headerHeight: this.gridHelpersService.getHeaderHeight(),
      onCellClicked: (params) => this.onCellClicked(params),
      floatingFiltersHeight: this.gridHelpersService.getFloatingFilterHeight(),
      groupHeaderHeight: this.gridHelpersService.getGroupHeaderHeight(),
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        floatingFilter: true,
        filterParams: { applyButton: false, clearButton: true, applyMiniFilterWhileTyping: true, cellHeight: this.gridHelpersService.getFilterListItemHeight() },
        cellClass: 'agAlignCentreVertically',
        headerComponentParams: { showPinAndRemoveOptions: false },
        autoHeaderHeight: true,
        floatingFilterComponentParams: { suppressFilterButton: true }
      },
      animateRows: true,
      groupDisplayType: 'singleColumn',
      autoGroupColumnDef: {
        // Hide the group column for child rows
        width: 100, // Collapse the width of the group column
      },
      rowSelection: 'multiple',
      //suppressRowClickSelection: true,
      columnTypes: {
        ...this.columnTypeService.provideColTypes([]),
      },
      columnDefs: this.provideColumnDefs(),
      getRowClass: (params) => {
        if (params.data?.Description === 'Total') return 'total';
      },
      onRowDoubleClicked: (params) => this.rowDoubleClicked(params),
      pinnedBottomRowData: this.providePinnedBottomRowData(),
      onRowDataUpdated: (event) => this.resizeGrid(),
      onRowSelected: (params) => this.onRowSelectionChange(),
      onFilterChanged:(params)=>this.onFilterChanged(params),
      onSortChanged: (params) => { this.service.columnState = this.service.gridColumnApi?.getColumnState(); },
      onColumnResized: (params) => {
        this.service.columnState = this.service.gridColumnApi?.getColumnState();
      },
      onColumnEverythingChanged: (params) => {
        if (params.source !== "gridInitializing") {
          this.service.columnState = this.service.gridColumnApi?.getColumnState();
        }
      },
      groupUseEntireRow: false
      //rowMultiSelectWithClick: true
    }
  }
  onFilterChanged(params: FilterChangedEvent<any, any>): void {
    if (!this.service.gridApi) { return }
    this.service.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData())
    this.service.filterModel = this.service.gridApi?.getFilterModel();
  }
  onCellClicked(params: CellClickedEvent<any, any>): void {
    if (params.column.getColDef().colId !== 'checkBox') { return; }
    params.node.setSelected(!params.node.isSelected());
  }

  onRowSelectionChange(): void {
    if (!this.service.gridApi) { return }
    this.service.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData())
  }

  providePinnedBottomRowData(): any[] {
    const params = this.buildUpParamsForBottomRows();
    return this.gridHelpersService.buildTotalAndAverageRows(params);
  }


  public buildUpParamsForBottomRows() {
    const params: BuildTotalAndAverageRowsParams = {
      colsToSkipAverageIfZero: [],
      colsToTotalOrAverage: [],
      colsToTotal: [],
      colsToSetToTrue: [],
      selectedCountFieldName: 'RetailerName', //where we put the '7 selected'
      labelFieldName: 'NONE',  //where we put the 'Average per vehicle'
      itemName: 'vehicle',  //used in forming the text e.g. 'Average per vehicle'
      api: this.service.gridApi,
      includeTotalRow:false,
      showTotalInAverage:true
    }

    let colDefs = this.provideColumnDefs();
    this.gridHelpersService.extractAverageAndTotalCols(colDefs, params);
    return params;
  }



  provideColumnDefs(): (CPHColDef | CPHColGroupDef)[] {
    return [

      { headerName: '', field: 'checkBox', colId: 'checkBox', headerCheckboxSelection: true, checkboxSelection: params => {
        // Return false if this is a row group row
        return !params.node.group;
      }, width: 12, minWidth: 12, 
        cellStyle: params => { return { padding: '0 0.25em' } } 
      },
      {
        headerName: 'Vehicle',
        children: [
          { headerName: 'Site Name', field: 'RetailerName', colId: 'RetailerName', width: 50, type: 'labelSetFilter', rowGroup: this.service.summariseBySite },
          { headerName: 'Reg', field: 'VehicleReg', colId: 'VehicleReg', width: 30, minWidth: 30, type: 'label' },
          { headerName: 'Make', field: 'Make', colId: 'Make', width: 30, minWidth: 30, type: 'labelSetFilter' },
          { headerName: 'Model', field: 'Model', colId: 'Model', width: 30, minWidth: 30, type: 'labelSetFilter' },
          { headerName: 'Description', field: 'Derivative', colId: 'Derivative', width: 100, type: 'label' }
        ]
      },
      {
        headerName: 'Metrics', children: [
          { headerName: 'Valuation', shouldAverageIfValue: true, field: 'ValuationAdjustedRetail', colId: 'ValuationAdjustedRetail', width: 25, type: 'currency' },
          { headerName: 'Days Listed', shouldAverageIfValue: true, field: 'DaysListed', colId: 'DaysListed', width: 20, type: 'number' },
          { headerName: 'Retail Rating', shouldAverageIfValue: true, field: 'RetailRating', colId: 'RetailRating', width: 20, type: 'number' }
        ]
      },


      {
        headerName: 'Existing Price', children: [
          { headerName: 'Price', shouldAverageIfValue: true, field: 'WasPrice', colId: 'WasPrice', width: 25, minWidth: 25, type: 'currency' },
          {
            headerName: 'Price Rating',  field: 'PriceIndicatorRatingAtCurrentSelling', colId: 'PriceIndicatorRatingAtCurrentSelling', minWidth: 35, width: 35, type: 'labelSetFilter',
            cellRenderer: (params) => this.autopriceRendererService.autoTraderPriceIndicatorRenderer(params)
          },
          { headerName: 'Days to Sell', shouldAverageIfValue: true, field: 'DaysToSellAtCurrentSelling', colId: 'DaysToSellAtCurrentSelling', width: 20, type: 'number' },
        ]
      },
      {
        headerName: 'New Price', children: [
          { headerName: 'Price', shouldAverageIfValue: true, field: 'NewPrice', colId: 'NewPrice', width: 25, minWidth: 25, type: 'currency' },
          { headerName: 'Excl Admin Fee', hide: this.hideExclAdminFeeCol(), shouldAverageIfValue: true, field: 'NewPriceExclAdminFee', colId: 'NewPriceExclAdminFee', width: 25, type: 'currency' },
          {
            headerName: 'Price Rating',  field: 'NewPriceIndicatorRating', colId: 'NewPriceIndicatorRating', minWidth: 35, width: 35, type: 'labelSetFilter',  //TO DO
            cellRenderer: (params) => this.autopriceRendererService.autoTraderPriceIndicatorRenderer(params)
          },
          { headerName: 'Days to Sell', shouldAverageIfValue: true, field: 'NewDaysToSell', colId: 'NewDaysToSell', width: 20, type: 'number' },
        ]
      },
      {
        headerName: 'Change', children: [
          { headerName: 'Up £', 
            cellClass: (params) => this.colourChanges(params), 
            shouldAverageIfValue: true, 
            colId: 'ChangeValueUp', 
            field: 'ChangeValueUp', 
            aggFunc: (params) => {

              const rows = params.rowNode.childrenAfterGroup.map(node => node.data);

              const filteredRows = rows.filter(row => row?.WasPrice !== 0);
          
              const total = filteredRows.reduce((sum, row) => sum + (row?.ChangeValueUp || 0), 0);
              const count = filteredRows.length;
          
              return count > 0 ? total / count : 0;
            },
            width: 20, 
            type: 'currency' },
          {
            headerName: 'Up %', 
            cellClass: (params) => this.colourChanges(params), 
            shouldAverageIfValue: true, 
            field: 'ChangePercentUp', 
            colId: 'ChangePercentUp',
            aggFunc: (params) => {

              const rows = params.rowNode.childrenAfterGroup.map(node => node.data);

              const filteredRows = rows.filter(row => row?.WasPrice !== 0);
          
              const total = filteredRows.reduce((sum, row) => sum + (row?.ChangePercentUp || 0), 0);
              const count = filteredRows.length;
          
              return count > 0 ? total / count : 0;
            },
            width: 20, 
            type: 'percentWithPlusMinus',

          },
          { headerName: 'Down £', 
            cellClass: (params) => this.colourChanges(params), 
            shouldAverageIfValue: true, 
            colId: 'ChangeValueDown', 
            field: 'ChangeValueDown',
            aggFunc: (params) => {

              const rows = params.rowNode.childrenAfterGroup.map(node => node.data);

              const filteredRows = rows.filter(row => row?.WasPrice !== 0);
          
              const total = filteredRows.reduce((sum, row) => sum + (row?.ChangeValueDown || 0), 0);
              const count = filteredRows.length;
          
              return count > 0 ? total / count : 0;
            },
            width: 20, 
            type: 'currency' },
          {
            headerName: 'Down %', 
            cellClass: (params) => this.colourChanges(params), 
            shouldAverageIfValue: true, 
            field: 'ChangePercentDown', 
            colId: 'ChangePercentDown', 
            width: 20,
            aggFunc: (params) => {

              const rows = params.rowNode.childrenAfterGroup.map(node => node.data);

              const filteredRows = rows.filter(row => row?.WasPrice !== 0);
          
              const total = filteredRows.reduce((sum, row) => sum + (row?.ChangePercentDown || 0), 0);
              const count = filteredRows.length;
          
              return count > 0 ? total / count : 0;
            },
            type: 'percentWithPlusMinus',
          },

          // { headerName: 'Days to Sell', shouldAverage: true, field: 'DaysToSellChange', colId: 'DaysToSellChange', width: 20, type: 'number' },

          {
            headerName: 'Net',
            colId: 'Net',
            field: 'net',
            width: 20,
            type: 'currency',
            aggFunc: (params) => {
              const rows = params.rowNode.childrenAfterGroup.map(node => node.data);
          
              const filteredRows = rows.filter(row => row?.WasPrice !== 0);
          
              const total = filteredRows.reduce((sum, row) => sum + (row?.net || 0), 0);
          
              return total; 
            },
            shouldTotal: true
          },
          
          { headerName: 'Status', colId: 'Status', field: 'Status', width: 100, type: 'labelSetFilter' }
        ]
      }
    ]
  }
  colourChanges(params: CellClassParams<any, any>): string | string[] {
    if (params.node.group) { return; }

    let row: PricingChangeNew = params.data;
    let colId = params.colDef.colId;

    //nothing if no price up and is an up col
    if (row.ChangeValueUp > 0) {
      if (colId !== 'ChangeValueUp' && colId !== 'ChangePercentUp') {
        return null;
      }
    }
    if (row.ChangeValueDown < 0) {
      if (colId !== 'ChangeValueDown' && colId !== 'ChangePercentDown') {
        return null;
      }
    }


    if (row.WasPriceVsStrategyBanding == 'VeryUnderPriced' || row.WasPriceVsStrategyBanding == 'VeryOverPriced') { return 'veryOverUnder' }
    if (row.WasPriceVsStrategyBanding == 'UnderPriced' || row.WasPriceVsStrategyBanding == 'OverPriced') { return 'overUnder' }
    if (row.WasPriceVsStrategyBanding == 'OnStrategyPrice') { return 'onStrategyPrice' }
  }


  hideExclAdminFeeCol() {
    if (!this.service.priceChangesRowData) {
      return false;
    }
    var res = !this.service.priceChangesRowData.some(x => x.RetailerAdminFee > 0);
    return res;
  }


  totalChangePercentGetter(params: ValueGetterParams<any>): any {
    if (params.node.isRowPinned()) {
      //is a total one
      return params.node.data.WasPrice > 0 ? params.node.data.NewPrice / params.node.data.WasPrice - 1 : 0
    }
    if (params.node.data) {
      return params.node.data.WasPrice > 0 ? params.node.data.NewPrice / params.node.data.WasPrice - 1 : 0
    }
    return 0;
  }
  changePercentRenderer(params: any) {
    if (!params.data.CouldGenerateNewPrice) { return '' }
    if (!!params.node.rowPinned) { return '' }
    return this.cphPipe.transform(params.data.NowPriceChangePct, 'percent', 1)
  }

  stylePriceChangeCurrencyCell(params: any) {
    let backgroundColour: string;
    const row: PricingChange = params.data;
    //console.log(params)
    if (!!params.node.rowPinned) { return }
    if (row.NowPriceChange < -500) backgroundColour = "orangered";
    else if (row.NowPriceChangePct <= -0.02) backgroundColour = "lightcoral";
    else if (row.NowPriceChangePct >= 0.02) backgroundColour = "lightGreen";
    else backgroundColour = "lightgoldenrodyellow";

    return { backgroundColor: backgroundColour }
  }

  rowDoubleClicked(params: RowDoubleClickedEvent) {
    const row: PricingChangeNew = params.data;

    if (!row.AdvertId) { return; }


    let allAdIds: number[] = []
    this.service.gridApi.forEachNodeAfterFilter(node => {
      if (node.data && !node.isRowPinned()) {
        let nodeRow: PricingChangeNew = node.data;
        allAdIds.push(nodeRow.AdvertId)
      }
    })

    this.autoPriceInsightsModalService.initialise(row.AdvertId, allAdIds);

    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });
  }

  resizeGrid() {
    if (this.service.gridApi) this.service.gridApi.sizeColumnsToFit();
  }

  onGridReady(params: GridReadyEvent) {
    this.service.gridApi = params.api;
    this.service.gridColumnApi = params.columnApi;
    this.service.gridApi.sizeColumnsToFit();
    this.gridOptions.context = { thisComponent: this };
    if (this.service.summariseBySite) {
      this.service.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
    }
    this.selections.triggerSpinner.emit({ show: false });

    if (!this.service.gridApi) { return }
    this.service.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData())
    
    if (this.service.filterModel) {
      this.service.gridApi.setFilterModel(this.service.filterModel);
    }

    if (this.service.columnState) {
      this.service.gridColumnApi.applyColumnState({
        state: this.service.columnState,
        applyOrder: true
      });
    }
  }

  onDataFirstRendered(params) {
    this.service.gridApi.sizeColumnsToFit();
  }

  excelExport() {
    this.service.gridApiForExcelDownload = this.constants.clone(this.service.gridApi);
    const currentColumnDefs = this.service.gridApiForExcelDownload.getColumnDefs();
    const newColumnDefs = [...currentColumnDefs]
    newColumnDefs.splice(0, 1); //removing the first checkbox column 
    this.service.gridApiForExcelDownload.setColumnDefs(newColumnDefs);
    let tableModel = this.service.gridApiForExcelDownload.getModel()
    this.excel.createSheetObject(tableModel, 'Stock Pricing - Price Changes', 1, 1);
    this.service.gridApiForExcelDownload = null;
  }

  onChosenNewEffectiveDate(newDateString: string) {
    const newDate = new Date(newDateString);
    this.service.chosenDate = newDate;
    this.getPriceChangesData();
  }

  toggleIncludeNewVehicles() {
    this.service.includeNewVehicles = !this.service.includeNewVehicles;
    this.getPriceChangesData();
  }

  toggleShowSmallPriceChanges() {
    this.service.showSmallPriceChanges = !this.service.showSmallPriceChanges;
    this.getPriceChangesData();
  }
  toggleIncludeUnPublishedAds() {
    this.service.includeUnPublishedAds = !this.service.includeUnPublishedAds;
    this.getPriceChangesData();
  }

  toggleSummariseBySite() {
    this.service.summariseBySite = !this.service.summariseBySite;

    if (this.service.summariseBySite) {
      this.gridOptions.columnApi.addRowGroupColumn('RetailerName');
      this.service.gridApi.setPinnedBottomRowData(this.providePinnedBottomRowData());
    } else {
      this.gridOptions.columnApi.removeRowGroupColumn('RetailerName');
      this.service.gridApi.setPinnedBottomRowData([]);
    }

    this.resizeGrid();
  }

  priceChangesHeaderMessage() {
    let baseMessage = `This page matches to the daily email and shows all price changes generated today.`;
    let mayBeSomeAutoPricing = this.service.priceChangesRowData?.some(x => x.Status.includes('will be actioned'));
    if (mayBeSomeAutoPricing) {
      baseMessage += `  To opt out of any change, simply double click a row.`
    }
    return baseMessage;

  }

  markSelectedAdvertAsApproved() {
    const priceChangeIds: number[] = this.service.gridApi.getSelectedRows().map(r => r.PriceChangeId);

    const params: SetPriceChangesParams = {
      priceChangeIds: priceChangeIds
    }

    this.setPriceChangesData(params);
  }

  getSelectedRowsCount() {
    if (this.service.gridApi) {
      const selectedRows = this.service.gridApi.getSelectedRows();
      return selectedRows ? selectedRows.length : 0;
    }

    return 0;
  }


  confirmPriceChange() {
    if (this.getSelectedRowsCount() == 0) { return };

    const confirmModalHeader = 'Approve price changes - are you sure?';
    const confirmModalBody = 'Spark will action the price changes within the next 15 minutes.';
    let modalResultSubscription: Subscription = this.selections.confirmModalEmitter.subscribe(res => {
      if (res) { this.markSelectedAdvertAsApproved(); }
      modalResultSubscription.unsubscribe();
    })

    this.constants.confirmModal.showModal(confirmModalHeader, confirmModalBody);
  }


  setPriceChangesData(params: SetPriceChangesParams) {
    this.selections.triggerSpinner.next({ message: 'Loading...', show: true });

    this.getData.setPriceChanges(params).subscribe(() => {
      let message = params.priceChangeIds.length > 1 ? 'Prices' : 'Price';
      this.constants.toastSuccess(`${message} marked as approved`)
      this.getPriceChangesData();

      this.selections.triggerSpinner.next({ show: false });
    }, (error: any) => {
      console.error('Failed to approve price changes', error);
      this.selections.triggerSpinner.next({ show: false });
    });
  }

  markPricesApprovedMessage() {
    return `Mark ${this.constants.pluralise(this.getSelectedRowsCount(), 'price', 'prices')} as approved`
  }



}
