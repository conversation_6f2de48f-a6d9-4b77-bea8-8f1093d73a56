# Ask user to select environment (d for Development, p for Production), default to 'd' (Development)
$environmentInput = Read-Host "Enter the environment to update (d or blank for Development, p for Production)"

# Default to 'd' (Development) if user just presses Enter
if ([string]::IsNullOrWhiteSpace($environmentInput)) {
    $environmentInput = "d"
}

# Map the user input to the correct environment
switch ($environmentInput.ToLower()) {
    "p" { $environment = "Production" }
    default { $environment = "Development" } # Default to Development for any other input, including 'd'
}

# Set the environment variable ASPNETCORE_ENVIRONMENT to the selected environment
[System.Environment]::SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", $environment, [System.EnvironmentVariableTarget]::Process)

# Define the path to the appropriate appsettings.json based on user input
$targetAppSettings = ".\CPHI.Spark.WebApp\appsettings.$environment.json"
$projectPath = ".\CPHI.Spark.WebApp"
$repositoryProject = ".\CPHI.Spark.Repository"

# Define the connection names and their placeholders
$databases = @(
    @{ Name = "RRGUKConnection"; Placeholder = "MigrationConnection" },
    @{ Name = "VindisConnection"; Placeholder = "MigrationConnection" },
    @{ Name = "RRGSpainConnection"; Placeholder = "MigrationConnection" }
    @{ Name = "SytnerConnection"; Placeholder = "MigrationConnection" }
	@{ Name = "DefaultConnection"; Placeholder = "MigrationConnection" }
)

# Step 1: Build the project only once

# Clean the solution before building
Write-Host "Cleaning the solution for $environment environment..."
dotnet clean $projectPath
dotnet clean $repositoryProject


Write-Host "Building the solution for $environment environment..."
dotnet build $projectPath
dotnet build $repositoryProject


# Step 2: Apply the migration to all databases
foreach ($db in $databases) {
    Write-Host "Switching $($db.Name) to MigrationConnection..."

    # Build the pattern and replacement strings for renaming the db connection
    $pattern = '"' + $db.Name + '"'
    $replacement = '"MigrationConnection"'

    # Replace the current database connection name with MigrationConnection in appsettings
    (Get-Content $targetAppSettings) -replace $pattern, $replacement | Set-Content $targetAppSettings

    # Apply the migration using EntityFrameworkCore or dotnet CLI
    dotnet ef database update --context CPHIDbContext --project $repositoryProject --startup-project $projectPath --no-build

    Write-Host "Migration applied to $($db.Name)."

    # Build the pattern and replacement for changing the db connection name back
    $pattern = '"MigrationConnection"'
    $replacement = '"' + $db.Name + '"'

	Write-Host "Reverting connection string to $($replacement)."
    # Revert MigrationConnection back to the original database connection name
    (Get-Content $targetAppSettings) -replace $pattern, $replacement | Set-Content $targetAppSettings
	Write-Host "Reverted."
}

Write-Host "Migration process completed for all databases!"

