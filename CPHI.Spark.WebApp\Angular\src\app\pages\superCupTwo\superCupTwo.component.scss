nav.navbar .generic #pageTitle {min-width:10em;}
.topDropdownButtons{display:flex;
    .dropdown-toggle{padding:0em 2em;}
}


#totalRow .team{background:none;
    .homeTeam{}
    .awayTeam{}
} 

nav.navbar h3{margin:0.3em 0em;}

#magic{left:0px}
.magicLogoHolder {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;

    img {
        height: 100%;
    }
}    


.content-new{   background-size:cover!important;

    .content-inner-new{width:90%;height:90%;margin:1em auto;

        // the container for the front page summary of all the teams 
        #matchesContainer{ width:70%;margin:1em auto;height:100%;position:relative;
            #header{ width:100%;background:var(--glass);border-radius:0.3em;padding:0.5em 2em;position: relative;
                .headline{display:flex;justify-content: space-around;align-items:center;
                    svg{}
                    .words{}
                }
                .tagLine{width:100%;text-align: center;padding:0.4em 2em;}
            }
            
            #matches{width: 100%; display: flex;flex-direction:column;margin-top: 1em;padding-bottom: 4em; 
                
            }
        }

        // once you have clicked on a match
        #matchContainer{width:100%;margin:1em auto;height:100%;position:relative;
            #backButton{position:absolute;top:1em;left:1em;}  
            
            #peopleContainersArea{display: flex;align-items:top;    height: calc(100vh - 13em);
                #homeContainer{left:0%;}
                #awayContainer{right:0%;}
                .teamContainer{width: 50%;                display: inline-block;
                    .teamContainerInner{
                        height:80%;
                        margin-top: 2em;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-around;
                        
                    }
                }
            }
            
            }
        
        }

        //this sits within both matchesContainer (match is within each team pairing) plus in matchContainer (used at top as a summary)
        .match{margin: 0.4em auto 2.5em auto;               display: flex;                               width: 73%;                
            .team{border-radius:0.3em;background:var(--glass);border-radius:0.3em;width:44em;height:60px;
                .teamInner{display: flex;align-items:center;height:100%;position:relative;
                    .managerMoney{    position: absolute;
                        border-radius: 0.3em;
                        background: var(--brightColourLight);
                        color: var(--grey20);;
                        
                        padding: 0.2em 0.5em;
                        font-weight: 700;}
                    .name{right:100%;position:absolute;right:4em;top:0px;display:flex;align-items: center;white-space: nowrap;}
                    .score{width:3em;text-align:center;height:100%;color:black;font-weight: 700;
                        background:var(--brightColourDark);border-radius:0px 0.2em 0.2em 0px;height:100%;position: absolute;right:-5px;top:0px;display:flex;align-items:center;justify-content: center;}
                }
            }
            
           
            .awayTeam .score{left:-5px;right:unset;border-radius:0.2em 0px 0px 0.2em !important;}
            .awayTeam.team .name{left:4em !important} //away team names should line up left

            .awayTeam .managerMoney{right:-5em;}
            .homeTeam .managerMoney{left:-5em;}
        }

        #matchContainer .match{
            
            .team{height:100px}
        } 


    //1366px monitor
    @media (max-width: 1920px)  {
        #matches{margin-top:3em!important;}
        .match{width:85%}
        .match .team{width: 50%  !important; height: 38px !important;}
        .personChip{height: 57px!important;
            img{width:62px!important;}
        }
        #peopleContainersArea{padding-top: 2em!important;}
    }

    }


   

    .chipHolder{text-align: center;}
    .chipHolder.ownRow{width:100%;}
    
    .personChip.fullSalesman{opacity:1!important}
    .personChip{border-radius:0.5em;background:var(--glass);width:23em;height:80px;display: inline-block;margin:3.2em 2em 0.2em 4em;opacity:0.5;
        .personChipInner{display: flex;align-items:center;height:100%;position:relative;
            img{width:80px;border-radius:50%;position:absolute;left:-40px}
            .name{ 
                white-space: nowrap;
                padding-left: 4em;
                    flex-direction: column;
                width: calc(100% - 6.4em);
               text-align: left;
                align-items: flex-start;
                .actualName{    width: 8.7em;                    text-overflow: ellipsis;                    overflow: hidden;}
                // .payoutCalc.topUp{display:flex;}
                .payoutCalc{display:flex;
                    .payoutRow{  display:flex;  white-space: nowrap;    align-items: center;                    width: 100%;                        display: flex;
                        .normalPayout{    margin-left: 1em;
                            
                            background: lightblue;
                            padding: 2px 9px;
                            border-radius: 4px;
                            font-weight: 700;}
                        .superPayout{    margin-left: 1em;
                            
                            background: var(--brightColour);
                            padding: 2px 9px;
                            border-radius: 4px;
                            font-weight: 700;}
                    }
                }
            }
            .score{width:3em;text-align:center;height:100%;color:black;font-weight:700;background:var(--brightColourDark) ;border-radius:0px 0.2em 0.2em 0px;
                height:100%;position: absolute;right:-5px;top:0px;display:flex;align-items:center;justify-content: center;}
        }
    


}

.superCupBackground.content-new { background: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), url("./../../../assets/imgs/renault5.jpg");background-size: cover; }

#header.dark {
    background-color: rgba(0,0,0,0.6) !important;
    color: #FFFFFF !important;
}

.image {
    position: absolute;
    top: 0;
    bottom: 0;

    img {
        height: 100%;
    }
}

.image.left {
    left: 0;
}

.image.right {
    right: 0;
}

.match-stacked {
    background-color: rgba(0,0,0,0.9);
    margin: 0.5em 10%;
    color: #FFFFFF;
    padding: 0.5em 14em;
    border-bottom: 2px solid #FDB60D;

    .team {
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        
        width: 95%;

        .score {
            font-weight: bold;
        }

        .winner {
            position: absolute;
            right: -10%;
            color: #FDB60D;
        }
    }
}