import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { EvhcPersonRow, EvhcSiteRow, Month } from '../../model/main.model';
import { ConstantsService } from "../../services/constants.service";
import { GetDataMethodsService } from '../../services/getDataMethods.service';
import { SelectionsService } from "../../services/selections.service";
import { EhvcService } from "./evhc.service";

@Component({
  selector: "app-evhc",
  templateUrl: "./evhc.component.html",
  styleUrls: ["./evhc.component.scss"],
})
export class EvhcComponent implements OnInit {

  months: Array<Month>;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,

    public dataMethods: GetDataMethodsService,
    public datePipe: DatePipe,
    public service: EhvcService,
  ) { }

  ngOnInit() {
    if(!this.service.isInitiated) this.service.initiateEvhc();
    this.service.getData();
  }

  revertToSiteTable(): void {
    this.service.chosenSiteRow = null;
    if (this.constants.environment.evhc_eDynamixView) {
      this.service.personRowsEDynamix = null;
    } else {
      this.service.personRows = null;
    }
  }



  selectSite(site: EvhcSiteRow): void {
    this.service.getSiteData(site)
  }

  selectWorkType(workType: string): void {
    this.service.isAmberSelected = false;
    this.service.isRedSelected = false;

    if (workType === 'Red Work') {
      this.service.isRedSelected = true;

    } else if (workType === 'Amber Work') {
      this.service.isAmberSelected = true;

    } else {
      this.service.isAmberSelected = true;
      this.service.isRedSelected = true;
    }

    this.service.chosenWorkType = workType
    this.service.getData();

    if (this.service.chosenSiteRow) {
      this.service.getSiteData(this.service.chosenSiteRow);
    }

  }


  makeMonths() {
    this.months = this.constants.makeMonths(0, 0);
  }


  selectMonth(month: Month) {
    this.service.monthStartDate = month.startDate;
    this.service.getData()

    if (this.service.chosenSiteRow) {
      this.service.getSiteData(this.service.chosenSiteRow);
    }
  }


  // changeMonth(changeAmount: number) {
  //   this.service.monthStartDate = new Date(this.service.monthStartDate.getFullYear(), this.service.monthStartDate.getMonth() + changeAmount, 1);
  //   this.service.month.endDate = new Date(this.service.monthStartDate.getFullYear(), this.service.monthStartDate.getMonth() + 1, 0);
  //   this.service.month.name = this.service.monthStartDate.toLocaleDateString(this.constants.translatedText.LocaleCode, { month: 'short', year: '2-digit' });
  //   this.getData()

  //   if (this.service.site) {
  //     this.getSiteData(this.service.site);
  //   }
  // }


  changeMonth(changeAmount: number) {
    this.selections.triggerSpinner.next({ show: true });
    this.service.monthStartDate = this.constants.addMonths(this.service.monthStartDate,changeAmount);
    this.service.getData()

    if (this.service.chosenSiteRow) {
      this.service.getSiteData(this.service.chosenSiteRow);
    }
  }


}
