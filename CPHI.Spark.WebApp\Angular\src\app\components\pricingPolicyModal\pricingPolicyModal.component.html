<div class="modal-header">
   <h4 class="modal-title" id="modal-basic-title">Pricing Policy Builder</h4>

   <button type="button" class="close" aria-label="Close" (click)="dismissModal(false)">
      <span aria-hidden="true">&times;</span>
   </button>
</div>
<div class="modal-body" [ngClass]="constants.environment.customer">
   <div class="autotraderCard">
      <div class="cardBody">
         <table id="topTable">
            <tbody>
               <tr>
                  <td>
                     <label for="factorId">Policy Id:</label>
                  </td>
                  <td>
                     <div id="factorId">{{ service.chosenPolicy?.Id }}</div>
                  </td>
               </tr>
               <tr>
                  <td>
                     <label for="factorName">Policy Name:</label>
                  </td>
                  <td>
                     <input id="factorName" [(ngModel)]="service.chosenPolicy.Name" />
                  </td>
               </tr>
               <tr>
                  <td>
                     <label for="factorComment">Comment:</label>
                  </td>
                  <td>
                     <input id="factorComment" [(ngModel)]="service.chosenPolicy.Comment" />
                  </td>
               </tr>
            </tbody>
         </table>
      </div>
   </div>

   <instructionRow [message]="instructionMessage"></instructionRow>
   <instructionRow [message]="newStategyInstructionMessage" *ngIf="service.chosenPolicy?.StrategyFactors?.length == 0">
   </instructionRow>

   <!-- The Baseline panel -->
   <div class="panel autotraderCard">
      <div class="cardInner">
         <div class="cardHeader">
            <div class="layerHeader d-flex justify-content-between align-items-center">
               <div class="h2 layerIcon">
                  <i class="fas fa-circle-sterling"></i>
               </div>
               <div class="factorName">Retail Valuation Baseline</div>
               <div class="layerButton"></div>
            </div>
         </div>
         <div class="cardBody"></div>
      </div>
   </div>

   <form #pricingPolicyForm="ngForm">
      <div ngModelGroup="pricingPolicyGroup" #pricingPolicyGroupRef="ngModelGroup">
         <!-- Each of the chosen factors -->
         <div
            class="panel autotraderCard"
            *ngFor="let factor of service.chosenPolicy?.StrategyFactors; let factorIndex = index"
         >
            <div class="cardInner">
               <div class="cardHeader">
                  <div class="layerHeader d-flex justify-content-between align-items-center">
                     <!-- The icon -->
                     <div class="h2 layerIcon">
                        <i
                           *ngIf="factor.Name == StrategyFactorName.RetailRatingBand"
                           class="fas fa-tachometer-average"
                        ></i>
                        <i
                           *ngIf="factor.Name == StrategyFactorName.RetailRating10sBand"
                           class="fas fa-tachometer-average"
                        ></i>
                        <i *ngIf="factor.Name == StrategyFactorName.DaysListedBand" class="fas fa-calendar-day"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.OnBrandCheck" class="fas fa-shield-check"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.RetailerName" class="fas fa-map-marker"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.Brand" class="fas fa-car"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.ModelName" class="fas fa-car"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.RR_DL_Matrix" class="fas fa-grid"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.RR_DS_Matrix" class="fas fa-grid"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.DTS_DL_Matrix" class="fas fa-grid"></i>
                        <i
                           *ngIf="factor.Name == StrategyFactorName.MatchCheapestCompetitor"
                           class="fas fa-ranking-star"
                        ></i>
                        <i *ngIf="factor.Name == StrategyFactorName.DaysToSell" class="fas fa-calendar-exclamation"></i>
                        <i
                           *ngIf="factor.Name == StrategyFactorName.ValuationChangeUntilSell"
                           class="fas fa-calendar-exclamation"
                        ></i>
                        <i *ngIf="factor.Name == StrategyFactorName.MinimumProfit" class="fas fa-sterling-sign"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.RoundToNearest" class="fas fa-sterling-sign"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.RoundToPriceBreak" class="fas fa-sterling-sign"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.SpecificColour" class="fas fa-palette"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.AgeAndOwners" class="fas fa-users"></i>
                        <i
                           *ngIf="factor.Name == StrategyFactorName.AchieveMarketPositionScore"
                           class="fas fa-ranking-star"
                        ></i>
                        <i
                           *ngIf="factor.Name == StrategyFactorName.WholesaleAdjustment"
                           class="fas fa-circle-sterling"
                        ></i>
                        <i *ngIf="factor.Name == StrategyFactorName.MakeFuelType" class="fas fa-gas-pump"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.MakeAgeBand" class="fas fa-clock-three"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.PerformanceRatingScore" class="fas fa-eyes"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.FuelType" class="fas fa-gas-pump"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.ValueBand" class="fas fa-pound-sign"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.RegYear" class="fas fa-calendar-alt"></i>
                        <i *ngIf="factor.Name == StrategyFactorName.LiveMarketCondition" class="fas fa-chart-line"></i>
                     </div>

                     <!-- The label of the factor -->
                     <div class="factorName">
                        {{ service.getFactorNameToPrettyPrint(factor) | prettyPrintEnum }}
                     </div>

                     <div class="layerButton">
                        <!-- <button class="btn btn-danger" (click)="maybeDeleteFactor($event,factor)">
                            Remove this factor
                        </button> -->
                     </div>
                  </div>
               </div>
               <div class="cardBody">
                  <instructionRow class="newFactorNotes" [message]="service.factorExplanation(factor)"></instructionRow>

                  <div class="factorWorkings">
                     <table>
                        <!-- ######################################################### -->
                        <!-- The various headings depending on which factor we are looking at -->
                        <!-- ######################################################### -->
                        <thead>
                           <tr *ngIf="factor.Name == StrategyFactorName.DaysToSell">
                              <th></th>
                              <th>Days to sell</th>
                              <th>Optional Notes</th>
                           </tr>
                           <tr *ngIf="factor.Name == StrategyFactorName.MakeFuelType">
                              <th>Brand</th>
                              <th>Fuel Type</th>
                              <th>Impact %</th>
                              <th colspan="2"></th>
                              <th>Optional Notes</th>
                           </tr>
                           <tr *ngIf="factor.Name == StrategyFactorName.MakeAgeBand">
                              <th>Brand</th>
                              <th>Age Band</th>
                              <th>Impact %</th>
                              <th colspan="2"></th>
                              <th>Optional Notes</th>
                           </tr>

                           <tr *ngIf="factor.Name == StrategyFactorName.MinimumProfit">
                              <th></th>
                              <th>Minimum £</th>
                           </tr>
                           <tr *ngIf="factor.Name == StrategyFactorName.MinimumPricePosition">
                              <th></th>
                              <th>Minimum %</th>
                           </tr>
                           <tr *ngIf="factor.Name == StrategyFactorName.RoundToNearest">
                              <th></th>
                              <th>Round to Nearest £</th>
                              <th>Optional Notes</th>
                           </tr>
                           <tr *ngIf="factor.Name == StrategyFactorName.RoundToPriceBreak">
                              <th></th>
                              <th>Round if Within £</th>
                              <th>Optional Notes</th>
                           </tr>

                           <tr *ngIf="factor.Name == StrategyFactorName.DaysListed">
                              <th></th>
                              <th>Impact %</th>
                              <th>Optional Notes</th>
                              <th></th>
                              <th></th>
                           </tr>
                           <tr *ngIf="factor.Name == StrategyFactorName.DaysInStock">
                              <th></th>
                              <th>Impact %</th>
                              <th>Optional Notes</th>
                              <th></th>
                              <th></th>
                           </tr>
                           <tr
                              *ngIf="
                                 [
                                    StrategyFactorName.RetailRatingBand,
                                    StrategyFactorName.DaysListedBand,
                                    StrategyFactorName.OnBrandCheck,
                                    StrategyFactorName.RetailerName,
                                    StrategyFactorName.MatchCheapestCompetitor,
                                    StrategyFactorName.ValuationChangeUntilSell,
                                    StrategyFactorName.DaysInStockBand,
                                    StrategyFactorName.SpecificColour,
                                    StrategyFactorName.AgeAndOwners,
                                    StrategyFactorName.AchieveMarketPositionScore,
                                    StrategyFactorName.WholesaleAdjustment,
                                    StrategyFactorName.RetailRating10sBand,
                                    StrategyFactorName.Brand,
                                    StrategyFactorName.ModelName,
                                    StrategyFactorName.Mileage,
                                    StrategyFactorName.PerformanceRatingScore,
                                    StrategyFactorName.FuelType,
                                    StrategyFactorName.ValueBand,
                                    StrategyFactorName.RegYear,
                                    StrategyFactorName.LiveMarketCondition
                                 ].includes(factor.Name)
                              "
                           >
                              <th></th>
                              <th>Impact %</th>
                              <th>Optional Notes</th>
                           </tr>

                           <!-- ######################################################### -->
                           <!-- If matrix type layer -->
                           <!-- ######################################################### -->
                           <ng-container
                              *ngIf="
                                 [
                                    StrategyFactorName.RR_DL_Matrix,
                                    StrategyFactorName.RR_DS_Matrix,
                                    StrategyFactorName.RR_DB_Matrix,
                                    StrategyFactorName.DTS_DL_Matrix
                                 ].includes(factor.Name)
                              "
                           >
                              <!-- Days listead heading row -->
                              <tr>
                                 <th colspan="2">
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton class="newFactorNote" [note]="pasteMatrixNote()">
                                       </instructionButton>
                                    </div>
                                 </th>
                                 <th [attr.colspan]="factor.horizontalBandLabels.length">
                                    <span *ngIf="factor.Name === StrategyFactorName.RR_DL_Matrix"
                                       >Days Listed up to and including</span
                                    >
                                    <span *ngIf="factor.Name === StrategyFactorName.RR_DS_Matrix"
                                       >Days In Stock up to and including</span
                                    >
                                    <span *ngIf="factor.Name === StrategyFactorName.RR_DB_Matrix"
                                       >Days Booked In up to and including</span
                                    >
                                    <span *ngIf="factor.Name === StrategyFactorName.DTS_DL_Matrix"
                                       >Days Listed up to and including</span
                                    >

                                    <i class="ps-3 fas fa-circle-arrow-right"></i>
                                 </th>
                                 <th></th>
                              </tr>

                              <!-- The row for adding / deleting bands -->
                              <tr>
                                 <th><input (paste)="handlePasteMatrix($event, factor)" id="excelQuickDrop" /></th>
                                 <th></th>
                                 <th
                                    class="buttonHoldingCell"
                                    *ngFor="let label of factor.horizontalBandLabels; let labelIndex = index"
                                 >
                                    <button
                                       placement="left"
                                       popoverClass="vehiclePopUpImage"
                                       container="body"
                                       triggers="mouseenter:mouseleave"
                                       [ngbPopover]="'Remove this days band'"
                                       *ngIf="labelIndex > 0"
                                       class="btn btn-danger addRemoveButton"
                                       (click)="maybeDeleteHorizontalBand($event, factor, labelIndex)"
                                    >
                                       <i class="fas fa-circle-minus"></i>
                                    </button>
                                    <button
                                       placement="left"
                                       popoverClass="vehiclePopUpImage"
                                       container="body"
                                       triggers="mouseenter:mouseleave"
                                       [ngbPopover]="'Add another days band to the right'"
                                       class="btn btn-success"
                                       (click)="addHorizontalBand($event, factor, labelIndex + 1)"
                                    >
                                       <i class="fas fa-circle-plus"></i>
                                    </button>
                                 </th>
                              </tr>
                              <!-- Retail Rating and days  thresholds row -->
                              <tr>
                                 <th></th>
                                 <th></th>
                                 <th *ngFor="let label of factor.horizontalBandLabels; let bandLabelIndex = index">
                                    <input
                                       class="valueInput"
                                       tabindex="1"
                                       required
                                       name="bandLabel-value:{{ factorIndex }}-{{ bandLabelIndex }}"
                                       attr.name="bandLabel-value:{{ factorIndex }}-{{ bandLabelIndex }}"
                                       #bandLabelValue="ngModel"
                                       [disabled]="factor.horizontalBandLabels.length - 1 == bandLabelIndex"
                                       type="number"
                                       [(ngModel)]="label.value"
                                       (ngModelChange)="
                                          onHorizontalBandValueChange(factor, factorIndex, bandLabelIndex, $event)
                                       "
                                       [ngClass]="{
                                          redHighlight: colourHorizontalBandRed(factor, bandLabelIndex, label.value)
                                       }"
                                    />
                                 </th>
                                 <th></th>
                              </tr>

                              <tr>
                                 <th colspan="2">
                                    <span *ngIf="factor.Name !== StrategyFactorName.DTS_DL_Matrix">
                                       Retail Rating up to and including</span
                                    >
                                    <span *ngIf="factor.Name === StrategyFactorName.DTS_DL_Matrix"> Days to sell</span>
                                    <i class="ps-3 fas fa-circle-arrow-down"></i>
                                 </th>
                                 <th *ngFor="let label of factor.horizontalBandLabels; let bandLabelIndex = index"></th>
                                 <th>Optional Notes</th>
                              </tr>
                           </ng-container>

                           <ng-container *ngIf="[StrategyFactorName.DaysListed].includes(factor.Name)">
                              <!-- Days listead heading row -->
                              <tr>
                                 <th>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton class="newFactorNote" [note]="pasteMatrixNote()">
                                       </instructionButton>
                                    </div>
                                 </th>
                              </tr>
                              <tr>
                                 <th>
                                    <div class="d-flex align-items-center">
                                       <input (paste)="handlePasteDaysListed($event, factor)" id="excelQuickDrop" />
                                    </div>
                                 </th>
                              </tr>
                           </ng-container>

                           <ng-container *ngIf="[StrategyFactorName.RetailRating].includes(factor.Name)">
                              <!-- Retail Rating heading row -->
                              <tr>
                                 <th>
                                    <div class="d-flex align-items-center">
                                       <div>Excel quick drop &nbsp;</div>
                                       <instructionButton class="newFactorNote" [note]="pasteMatrixNote()">
                                       </instructionButton>
                                    </div>
                                 </th>
                              </tr>
                              <tr>
                                 <th>
                                    <div class="d-flex align-items-center">
                                       <input (paste)="handlePasteRetailRating($event, factor)" id="excelQuickDrop" />
                                    </div>
                                 </th>
                              </tr>
                           </ng-container>
                        </thead>
                        <tbody>
                           <!-- ######################################################### -->
                           <!-- Example row See template link below-->
                           <!-- ######################################################### -->

                           <ng-container *ngTemplateOutlet="exampleRow; context: { $implicit: factor }"></ng-container>

                           <!-- ######################################################### -->
                           <!-- The  row build up showing each item -->
                           <!-- ######################################################### -->
                           <!--.............................................-->
                           <!-- Add quick drop if editable label -->
                           <!--.............................................-->
                           <tr
                              *ngIf="
                                 [
                                    StrategyFactorName.SpecificColour,
                                    StrategyFactorName.Brand,
                                    StrategyFactorName.ModelName
                                 ].includes(factor.Name)
                              "
                           >
                              <td>
                                 <div class="d-flex align-items-center">
                                    <div>Excel quick drop &nbsp;</div>
                                    <instructionButton class="newFactorNote" [note]="pasteEditableLabelNote()">
                                    </instructionButton>
                                 </div>
                              </td>

                              <td>
                                 <input (paste)="handlePasteEditableLabel($event, factor)" id="excelQuickDrop" />
                              </td>
                           </tr>

                           <!--.............................................-->
                           <!-- Repeat the factor items -->
                           <!--.............................................-->
                           <ng-container
                              *ngFor="
                                 let factorItem of factor.StrategyFactorItems;
                                 let factorItemIndex = index;
                                 trackBy: trackByIndex
                              "
                           >
                              <!--.............................................-->
                              <!-- Standard rows like retail rating band -->
                              <!--.............................................-->
                              <tr
                                 *ngIf="
                                    [
                                       StrategyFactorName.RetailRatingBand,
                                       StrategyFactorName.DaysListedBand,
                                       StrategyFactorName.DaysInStockBand,
                                       StrategyFactorName.OnBrandCheck,
                                       StrategyFactorName.RetailerName,
                                       StrategyFactorName.FuelType,
                                       StrategyFactorName.ValueBand,
                                       StrategyFactorName.RegYear,
                                       StrategyFactorName.DaysToSell,
                                       StrategyFactorName.MinimumProfit,
                                       StrategyFactorName.MinimumPricePosition,
                                       StrategyFactorName.RoundToNearest,
                                       StrategyFactorName.RoundToPriceBreak,
                                       StrategyFactorName.RetailRating10sBand
                                    ].includes(factor.Name)
                                 "
                              >
                                 <!-- The fixed label -->
                                 <td>{{ factorItem.Label }}</td>

                                 <!-- The value -->
                                 <td>
                                    <input
                                       required
                                       name="{{ factor.Name }}-value:X-{{ factorItemIndex }}"
                                       #factorItemValue="ngModel"
                                       factorItemIndex
                                       class="valueInput"
                                       type="number"
                                       [(ngModel)]="factorItem.Value"
                                    />
                                 </td>

                                 <td>
                                    <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                 </td>
                              </tr>

                              <!--.............................................-->
                              <!-- Row where we can edit the label e.g. Days Listed layer -->
                              <!--.............................................-->

                              <tr
                                 *ngIf="
                                    [
                                       StrategyFactorName.DaysListed,
                                       StrategyFactorName.DaysInStock,
                                       StrategyFactorName.RetailRating,
                                       StrategyFactorName.SpecificColour,
                                       StrategyFactorName.Brand,
                                       StrategyFactorName.ModelName,
                                       StrategyFactorName.PerformanceRatingScore,
                                       StrategyFactorName.LiveMarketCondition
                                    ].includes(factor.Name)
                                 "
                              >
                                 <!-- The editable label -->
                                 <td>
                                    <input
                                       class="valueInput extraWide"
                                       required
                                       type="text"
                                       name="{{ factor.Name }}-label:X-{{ factorItemIndex }}"
                                       #factorItemLabel="ngModel"
                                       [(ngModel)]="factorItem.Label"
                                       [readOnly]="factorItem.IsReadOnly"
                                    />
                                 </td>

                                 <!-- The value -->
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-value:X-{{ factorItemIndex }}"
                                       #factorItemValue="ngModel"
                                       type="number"
                                       [(ngModel)]="factorItem.Value"
                                       [readOnly]="factorItem.IsReadOnly"
                                    />
                                 </td>

                                 <!-- Buttons for remove and add -->
                                 <td>
                                    <button
                                       [disabled]="factorItemIndex == 0 || factorItem.IsReadOnly"
                                       class="btn btn-danger"
                                       (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Remove
                                    </button>
                                 </td>
                                 <td>
                                    <button
                                       class="btn btn-success"
                                       [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                       (click)="addFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Add
                                    </button>
                                 </td>
                                 <td>
                                    <textarea
                                       *ngIf="!factorItem.IsReadOnly"
                                       placeholder="add notes"
                                       [(ngModel)]="factorItem.Comment"
                                    ></textarea>
                                 </td>
                              </tr>
                              <tr *ngIf="[StrategyFactorName.Mileage].includes(factor.Name)">
                                 <!-- The editable label -->
                                 <td>
                                    <input
                                       class="valueInput extraWide"
                                       required
                                       [pattern]="'^<.*'"
                                       name="mileageFactor-value:X-{{ factorItemIndex }}"
                                       #mileageFactor="ngModel"
                                       title="Input must start with '<' symbol."
                                       type="text"
                                       [readonly]="factorItem.Label.replace(',', '') == '<999999'"
                                       [(ngModel)]="factorItem.Label"
                                    />
                                 </td>

                                 <!-- The value -->
                                 <td>
                                    <input
                                       class="valueInput"
                                       type="number"
                                       required
                                       #mileageValue="ngModel"
                                       name="mileage-impact:X-{{ factorItemIndex }}"
                                       [(ngModel)]="factorItem.Value"
                                    />
                                 </td>

                                 <!-- Buttons for remove and add -->
                                 <td>
                                    <button
                                       [disabled]="factorItemIndex == 0"
                                       class="btn btn-danger"
                                       (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Remove
                                    </button>
                                 </td>
                                 <td>
                                    <button
                                       class="btn btn-success"
                                       [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                       (click)="addFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Add
                                    </button>
                                 </td>
                                 <td>
                                    <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                 </td>
                              </tr>

                              <!-- ------------------------------------------------------ -->
                              <!-- MakeFuelType ROW -->
                              <!-- ------------------------------------------------------ -->
                              <tr *ngIf="factor.Name == StrategyFactorName.MakeFuelType">
                                 <!-- The editable label -->
                                 <td>
                                    <input
                                       class="valueInput extraWide"
                                       type="text"
                                       required
                                       name="{{ factor.Name }}-brand:X-{{ factorItemIndex }}"
                                       #factorItemMake="ngModel"
                                       [ngModel]="factorItem.selectedMake"
                                       (change)="updateMakeForMakeAndFuelType($event, factorItem)"
                                    />
                                 </td>
                                 <!-- The editable 2 label -->
                                 <td>
                                    <!-- Dropdown for Fuel Type -->
                                    <select
                                       class="selectDropdown"
                                       [ngModel]="factorItem.selectedFuelType"
                                       required
                                       name="{{ factor.Name }}-fuelType:X-{{ factorItemIndex }}"
                                       #factorItemFuelType="ngModel"
                                       (change)="updateFuelType($event, factorItem)"
                                    >
                                       <option value="">Select a fuel type</option>
                                       <option *ngFor="let fuelType of fuelTypes" [value]="fuelType">
                                          {{ fuelType }}
                                       </option>
                                    </select>
                                 </td>

                                 <!-- The value -->
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       type="number"
                                       name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                       #factorItemValue="ngModel"
                                       [(ngModel)]="factorItem.Value"
                                    />
                                 </td>

                                 <!-- Buttons for remove and add -->
                                 <td>
                                    <button
                                       [disabled]="factorItemIndex == 0"
                                       class="btn btn-danger"
                                       (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Remove
                                    </button>
                                 </td>
                                 <td>
                                    <button
                                       class="btn btn-success"
                                       [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                       (click)="addFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Add
                                    </button>
                                 </td>
                                 <td>
                                    <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                 </td>
                              </tr>

                              <!-- ------------------------------------------------------ -->
                              <!-- MakeAgeBand ROW -->
                              <!-- ------------------------------------------------------ -->
                              <tr *ngIf="factor.Name == StrategyFactorName.MakeAgeBand">
                                 <!-- The editable label -->
                                 <td>
                                    <input
                                       class="valueInput extraWide"
                                       required
                                       name="{{ factor.Name }}-brand:X-{{ factorItemIndex }}"
                                       #factorItemMake="ngModel"
                                       type="text"
                                       [ngModel]="factorItem.selectedMake"
                                       (change)="updateMakeForMakeAndAgeBand($event, factorItem)"
                                    />
                                 </td>
                                 <!-- The editable 2 label -->
                                 <td>
                                    <!-- Dropdown for Age Band -->
                                    <select
                                       class="selectDropdown"
                                       required
                                       name="{{ factor.Name }}-age:X-{{ factorItemIndex }}"
                                       #factorItemDropdown="ngModel"
                                       [ngModel]="factorItem.selectedAgeBand"
                                       (change)="updateAgeBand($event, factorItem)"
                                    >
                                       <option value="">Select an age band</option>
                                       <option *ngFor="let ageBand of getAgeBands" [value]="ageBand">
                                          {{ ageBand }}
                                       </option>
                                    </select>
                                 </td>

                                 <!-- The value -->
                                 <td>
                                    <input
                                       required
                                       name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                       #factorItemValue="ngModel"
                                       class="valueInput"
                                       type="number"
                                       [(ngModel)]="factorItem.Value"
                                    />
                                 </td>

                                 <!-- Buttons for remove and add -->
                                 <td>
                                    <button
                                       [disabled]="factorItemIndex == 0"
                                       class="btn btn-danger"
                                       (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Remove
                                    </button>
                                 </td>
                                 <td>
                                    <button
                                       class="btn btn-success"
                                       [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                       (click)="addFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Add
                                    </button>
                                 </td>
                                 <td>
                                    <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                 </td>
                              </tr>

                              <!--.............................................-->
                              <!-- Age and Owners -->
                              <!--.............................................-->
                              <tr *ngIf="factor.Name === StrategyFactorName.AgeAndOwners">
                                 <td>
                                    <!-- Dropdown for Age Category -->
                                    <select
                                       class="selectDropdown me-5"
                                       required
                                       [ngModel]="factorItem.selectedAgeCategory"
                                       name="{{ factor.Name }}-ageband-dropdown:X-{{ factorItemIndex }}"
                                       #factorItemDropDown="ngModel"
                                       (change)="updateAgeAndOwnersLabelForAge($event, factorItem)"
                                    >
                                       <option value="">Select an age category</option>
                                       <option *ngFor="let category of ageCategories" [value]="category">
                                          {{ category }}
                                       </option>
                                    </select>

                                    <!-- Dropdown for Previous Owners -->
                                    <select
                                       class="selectDropdown"
                                       required
                                       [ngModel]="factorItem.selectedPreviousOwners"
                                       name="{{ factor.Name }}-previous-owners-dropdown:X-{{ factorItemIndex }}"
                                       #factorItemDropdown="ngModel"
                                       (change)="updateAgeAndOwnersLabelForOwners($event, factorItem)"
                                    >
                                       <option value="">Select Previous owners</option>
                                       <option *ngFor="let owner of previousOwners" [value]="owner">{{ owner }}</option>
                                    </select>
                                 </td>
                                 <!-- Hidden input for Label -->
                                 <!-- <input type="hidden" [(ngModel)]="factorItem.Label" class="extraWide"
                       [ngModel]="factorItem.selectedAgeCategory + '|' + factorItem.selectedPreviousOwners" /> -->

                                 <!-- Where we put the value e.g. 101 -->
                                 <td>
                                    <input
                                       class="valueInput"
                                       required
                                       name="{{ factor.Name }}-impact:X-{{ factorItemIndex }}"
                                       #factorItemValue="ngModel"
                                       type="number"
                                       [(ngModel)]="factorItem.Value"
                                    />
                                 </td>

                                 <!-- Buttons for remove and add -->
                                 <td>
                                    <button
                                       [disabled]="factorItemIndex == 0"
                                       class="btn btn-danger"
                                       (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Remove
                                    </button>
                                 </td>
                                 <td>
                                    <button
                                       class="btn btn-success"
                                       *ngIf="factorItemIndex == factor.StrategyFactorItems.length - 1"
                                       [disabled]="disableAddFactorButtonForEditableFactorItem(factorItem)"
                                       (click)="addFactorItem($event, factor, factorItemIndex)"
                                    >
                                       Add
                                    </button>
                                 </td>
                                 <td>
                                    <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                 </td>
                              </tr>
                              <!--.............................................-->
                              <!-- Match Cheapest Competitor.   Due to the different way we store values for this particular factor, we only want
                       to show the first factorItem from the ngFor.   We then instead use the factor reference below, to set all the factorItem values
                   -->
                              <!--.............................................-->
                              <ng-container *ngIf="factor.Name == 'MatchCheapestCompetitor' && factorItemIndex == 0">
                                 <!-- The fixed label -->
                                 <tr>
                                    <td>Competitor match settings</td>
                                    <td></td>
                                 </tr>

                                 <!-- Radius -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Radius: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="radiusNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-radius"
                                          #factorItemRadius="ngModel"
                                          type="number"
                                          [(ngModel)]="factor.getCheapestCompetitorFactorItem('Radius').Value"
                                       />
                                    </td>
                                 </tr>
                                 <!-- Plate steps -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Plate steps: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="plateStepsNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-plateSteps"
                                          #factorItemPlateSteps="ngModel"
                                          type="number"
                                          [(ngModel)]="factor.getCheapestCompetitorFactorItem('PlateSteps').Value"
                                       />
                                    </td>
                                 </tr>
                                 <!-- Mileage steps -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Mileage steps :&nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="mileageStepsNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-MileageSteps"
                                          #factorItemMileageSteps="ngModel"
                                          type="number"
                                          [(ngModel)]="factor.getCheapestCompetitorFactorItem('MileageSteps').Value"
                                       />
                                    </td>
                                 </tr>

                                 <!-- Ranking -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Ranking to achieve: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="rankingToAchieveNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-CompetitorRanking"
                                          #factorItemCompetitorRanking="ngModel"
                                          type="number"
                                          [(ngModel)]="factor.getCheapestCompetitorFactorItem('Ranking').Value"
                                       />
                                    </td>
                                 </tr>
                                 <!-- Channels -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Seller types: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="sellerTypeNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td class="competitorType textRight">
                                       <!-- Competitor Type: Independent -->
                                       <sliderSwitch
                                          [width]="150"
                                          [defaultValue]="
                                             factor.getCheapestCompetitorFactorItem('Independent').BoolValue
                                          "
                                          [text]="factor.getCheapestCompetitorFactorItem('Independent').Label"
                                          (valueChanged)="
                                             toggleFactorItemBoolValue(
                                                factor.getCheapestCompetitorFactorItem('Independent')
                                             )
                                          "
                                       >
                                       </sliderSwitch>
                                       <div class="smallGap"></div>
                                       <!--  Franchise -->
                                       <sliderSwitch
                                          [width]="150"
                                          [defaultValue]="factor.getCheapestCompetitorFactorItem('Franchise').BoolValue"
                                          [text]="factor.getCheapestCompetitorFactorItem('Franchise').Label"
                                          (valueChanged)="
                                             toggleFactorItemBoolValue(
                                                factor.getCheapestCompetitorFactorItem('Franchise')
                                             )
                                          "
                                       >
                                       </sliderSwitch>
                                       <div class="smallGap"></div>
                                       <!--  Supermarket -->
                                       <sliderSwitch
                                          [width]="150"
                                          [defaultValue]="
                                             factor.getCheapestCompetitorFactorItem('Supermarket').BoolValue
                                          "
                                          [text]="factor.getCheapestCompetitorFactorItem('Supermarket').Label"
                                          (valueChanged)="
                                             toggleFactorItemBoolValue(
                                                factor.getCheapestCompetitorFactorItem('Supermarket')
                                             )
                                          "
                                       >
                                       </sliderSwitch>
                                       <div class="smallGap"></div>
                                       <!--  Private -->
                                       <sliderSwitch
                                          [width]="150"
                                          [defaultValue]="factor.getCheapestCompetitorFactorItem('Private').BoolValue"
                                          [text]="factor.getCheapestCompetitorFactorItem('Private').Label"
                                          (valueChanged)="
                                             toggleFactorItemBoolValue(
                                                factor.getCheapestCompetitorFactorItem('Private')
                                             )
                                          "
                                       >
                                       </sliderSwitch>
                                    </td>
                                 </tr>
                                 <!-- Comment  -->
                                 <tr>
                                    <td>Comment</td>
                                    <td class="competitorTypeNotes">
                                       <textarea
                                          placeholder="add notes"
                                          [(ngModel)]="factor.getCheapestCompetitorFactorItem('Radius').Comment"
                                       ></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              <!--.............................................-->
                              <!-- Achieve Market Position Score. Similar to Match Cheapest Competitor.
                    The only difference being that instead of trying to achieve an exact position e.g. 2nd, we are trying to achieve a set MarketPositionScore.
                   -->
                              <!--.............................................-->
                              <ng-container *ngIf="factor.Name == 'AchieveMarketPositionScore' && factorItemIndex == 0">
                                 <!-- The fixed label -->
                                 <tr>
                                    <td>Market position settings</td>
                                    <td></td>
                                 </tr>

                                 <!-- Radius -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Radius: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="radiusNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-PositionRadius"
                                          #factorMarketPositionRadius="ngModel"
                                          type="number"
                                          [(ngModel)]="factor.getAchieveMarketPositionScoreFactorItem('Radius').Value"
                                       />
                                    </td>
                                 </tr>
                                 <!-- Plate steps -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Plate steps: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="plateStepsNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-MarketPositionPlateSteps"
                                          #factorMarketPositionPlateSteps="ngModel"
                                          type="number"
                                          [(ngModel)]="
                                             factor.getAchieveMarketPositionScoreFactorItem('PlateSteps').Value
                                          "
                                       />
                                    </td>
                                 </tr>
                                 <!-- Mileage steps -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Mileage steps :&nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="mileageStepsNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-MarketPositionMileageSteps"
                                          #factorMarketPositionMileageSteps="ngModel"
                                          type="number"
                                          [(ngModel)]="
                                             factor.getAchieveMarketPositionScoreFactorItem('MileageSteps').Value
                                          "
                                       />
                                    </td>
                                 </tr>
                                 <!-- Ranking -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Market Position Score to achieve: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="marketPositionScoreToAchieveNote()"
                                          >
                                          </instructionButton>
                                       </div>
                                    </td>
                                    <td>
                                       <input
                                          class="valueInput"
                                          required
                                          name="{{ factor.Name }}-MarketPositionRanking"
                                          #factorMarketPositionRanking="ngModel"
                                          type="number"
                                          [(ngModel)]="factor.getAchieveMarketPositionScoreFactorItem('Ranking').Value"
                                       />
                                    </td>
                                 </tr>
                                 <!-- Channels -->
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Seller types: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="sellerTypeNote()"
                                          ></instructionButton>
                                       </div>
                                    </td>
                                    <td class="competitorType textRight">
                                       <!-- Competitor Type: Independent -->
                                       <sliderSwitch
                                          [width]="150"
                                          [defaultValue]="
                                             factor.getAchieveMarketPositionScoreFactorItem('Independent').BoolValue
                                          "
                                          [text]="factor.getAchieveMarketPositionScoreFactorItem('Independent').Label"
                                          (valueChanged)="
                                             toggleFactorItemBoolValue(
                                                factor.getAchieveMarketPositionScoreFactorItem('Independent')
                                             )
                                          "
                                       >
                                       </sliderSwitch>
                                       <div class="smallGap"></div>
                                       <!--  Franchise -->
                                       <sliderSwitch
                                          [width]="150"
                                          [defaultValue]="
                                             factor.getAchieveMarketPositionScoreFactorItem('Franchise').BoolValue
                                          "
                                          [text]="factor.getAchieveMarketPositionScoreFactorItem('Franchise').Label"
                                          (valueChanged)="
                                             toggleFactorItemBoolValue(
                                                factor.getAchieveMarketPositionScoreFactorItem('Franchise')
                                             )
                                          "
                                       >
                                       </sliderSwitch>
                                       <div class="smallGap"></div>
                                       <!--  Supermarket -->
                                       <sliderSwitch
                                          [width]="150"
                                          [defaultValue]="
                                             factor.getAchieveMarketPositionScoreFactorItem('Supermarket').BoolValue
                                          "
                                          [text]="factor.getAchieveMarketPositionScoreFactorItem('Supermarket').Label"
                                          (valueChanged)="
                                             toggleFactorItemBoolValue(
                                                factor.getAchieveMarketPositionScoreFactorItem('Supermarket')
                                             )
                                          "
                                       >
                                       </sliderSwitch>
                                       <div class="smallGap"></div>
                                       <!--  Private -->
                                       <sliderSwitch
                                          [width]="150"
                                          [defaultValue]="
                                             factor.getAchieveMarketPositionScoreFactorItem('Private').BoolValue
                                          "
                                          [text]="factor.getAchieveMarketPositionScoreFactorItem('Private').Label"
                                          (valueChanged)="
                                             toggleFactorItemBoolValue(
                                                factor.getAchieveMarketPositionScoreFactorItem('Private')
                                             )
                                          "
                                       >
                                       </sliderSwitch>
                                    </td>
                                 </tr>
                                 <!-- Comment  -->
                                 <tr>
                                    <td>Comment</td>
                                    <td class="competitorTypeNotes">
                                       <textarea
                                          placeholder="add notes"
                                          [(ngModel)]="factor.getAchieveMarketPositionScoreFactorItem('Radius').Comment"
                                       ></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              <!--.............................................-->
                              <!-- Wholesale Adjustment -->
                              <!--.............................................-->
                              <ng-container *ngIf="factor.Name == 'WholesaleAdjustment' && factorItemIndex == 0">
                                 <tr>
                                    <td style="white-space: nowrap">
                                       <div class="showInformationInline">
                                          Adjustment %: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="wholesaleAdjustmentPctNote()"
                                          >
                                          </instructionButton>
                                       </div>
                                    </td>
                                    <td class="text-start">
                                       <input
                                          class="valueInput"
                                          required
                                          name="WholesaleAdjustmentPct"
                                          #factorWholesaleAdjustmentPct="ngModel"
                                          type="number"
                                          min="80"
                                          max="120"
                                          oninput="this.value = Math.max(0, this.value)"
                                          [(ngModel)]="factor.getWholesaleAdjustmentFactorItem('AdjustmentPct').Value"
                                       />
                                    </td>
                                 </tr>
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">
                                          Adjustment &pound;: &nbsp;
                                          <instructionButton
                                             class="newFactorNote"
                                             [note]="wholesaleAdjustmentValueNote()"
                                          >
                                          </instructionButton>
                                       </div>
                                    </td>
                                    <td class="text-start">
                                       <input
                                          class="valueInput"
                                          required
                                          type="number"
                                          name="WholesaleAdjustmentValue"
                                          #factorWholesaleAdjustmentValue="ngModel"
                                          [(ngModel)]="factor.getWholesaleAdjustmentFactorItem('AdjustmentValue').Value"
                                       />
                                    </td>
                                 </tr>
                                 <tr>
                                    <td>
                                       <div class="showInformationInline">Comment:</div>
                                    </td>
                                    <td class="text-start">
                                       <textarea
                                          placeholder="add notes"
                                          [(ngModel)]="factor.getWholesaleAdjustmentFactorItem('AdjustmentPct').Comment"
                                       ></textarea>
                                    </td>
                                 </tr>
                              </ng-container>
                              <!--.............................................-->
                              <!-- Row where we make a matrix  -->
                              <!--.............................................-->
                              <tr
                                 *ngIf="
                                    [
                                       StrategyFactorName.RR_DL_Matrix,
                                       StrategyFactorName.RR_DS_Matrix,
                                       StrategyFactorName.RR_DB_Matrix,
                                       StrategyFactorName.DTS_DL_Matrix
                                    ].includes(factor.Name)
                                 "
                              >
                                 <!-- Buttons for remove and add -->
                                 <td class="buttonHoldingCell">
                                    <button
                                       *ngIf="factorItemIndex > 0"
                                       class="btn btn-danger"
                                       placement="left"
                                       popoverClass="vehiclePopUpImage"
                                       container="body"
                                       triggers="mouseenter:mouseleave"
                                       [ngbPopover]="'Remove this retail rating band'"
                                       (click)="maybeDeleteFactorItem($event, factor, factorItemIndex)"
                                    >
                                       <i class="fas fa-circle-minus"></i>
                                    </button>
                                    <button
                                       class="btn btn-success"
                                       placement="left"
                                       popoverClass="vehiclePopUpImage"
                                       container="body"
                                       triggers="mouseenter:mouseleave"
                                       [ngbPopover]="'Add another retail rating band below this band'"
                                       (click)="addFactorItem($event, factor, factorItemIndex + 1)"
                                    >
                                       <i class="fas fa-circle-plus"></i>
                                    </button>
                                 </td>

                                 <!-- The editable label -->
                                 <td>
                                    <input
                                       [disabled]="factor.StrategyFactorItems.length - 1 == factorItemIndex"
                                       tabindex="2"
                                       required
                                       name="{{ factor.Name }}-label:X-{{ factorItemIndex }}"
                                       #factorItemLabel="ngModel"
                                       class="valueInput"
                                       type="number"
                                       [(ngModel)]="factorItem.Label"
                                       [ngClass]="{
                                          redHighlight: colourVerticalBandRed(factor, factorItemIndex, factorItem.Label)
                                       }"
                                    />
                                 </td>

                                 <!-- This iterates out the horizontal  input boxes -->
                                 <td *ngFor="let horizontalBand of factorItem.horizontalBands; let bandIndex = index">
                                    <input
                                       class="valueInput"
                                       type="number"
                                       required
                                       name="{{ factor.Name }}-value:{{ bandIndex }}-{{ factorItemIndex }}"
                                       #factorItemValue="ngModel"
                                       tabindex="3"
                                       [(ngModel)]="horizontalBand.value"
                                       min="80"
                                       max="120"
                                    />
                                 </td>

                                 <td>
                                    <textarea placeholder="add notes" [(ngModel)]="factorItem.Comment"></textarea>
                                 </td>
                              </tr>
                           </ng-container>
                        </tbody>
                     </table>
                     <div class="d-flex justify-content-end">
                        <button class="btn btn-danger" (click)="maybeDeleteFactor($event, factor)">
                           Remove this adjustment layer
                        </button>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>

      <!-- Add another factor panel -->
      <div class="panel autotraderCard">
         <div class="cardInner">
            <div class="cardHeader">
               <div class="layerHeader d-flex justify-content-between align-items-center">
                  <!-- The icon -->
                  <div class="h2 layerIcon"></div>

                  <div class="layerName"></div>

                  <div class="layerButton">
                     <button class="btn btn-success" (click)="showAddFactorModal()">
                        Add another adjustment layer..
                     </button>
                  </div>
               </div>
            </div>
            <div class="cardBody"></div>
         </div>
      </div>
   </form>
</div>

<div class="modal-footer">
   <div
      *ngIf="showDeleteButton()"
      [ngbPopover]="canDeletePolicy() ? null : 'Policy has been used so cannot be deleted'"
      placement="top"
      container="body"
      triggers="mouseenter:mouseleave"
   >
      <button type="button" class="btn btn-danger" [disabled]="!canDeletePolicy()" (click)="deletePolicy()">
         Delete Pricing Policy
      </button>
   </div>

   <div [ngbTooltip]="formErrorTooltip" [disableTooltip]="pricingPolicyForm.valid">
      <button
         type="button"
         class="btn btn-success"
         [disabled]="pricingPolicyForm.invalid"
         (click)="savePolicy({ saveAsNew: true })"
      >
         Save As New Pricing Policy
      </button>
   </div>

   <div [ngbTooltip]="formErrorTooltip" [disableTooltip]="pricingPolicyForm.valid">
      <button
         type="button"
         class="btn btn-success"
         [disabled]="!changesMade() || pricingPolicyForm.invalid"
         (click)="savePolicy({ saveAsNew: false })"
      >
         Save Pricing Policy
      </button>
   </div>

   <button type="button" class="btn btn-primary" (click)="dismissModal(false)">Close</button>
</div>

<ng-template #formErrorTooltip>
   <div *ngFor="let error of showFormErrors()">
      <div>{{ error }}</div>
   </div>
</ng-template>

<!-- Example row template -->
<ng-template #exampleRow let-factor>
   <!-- <ng-container *ngIf="getTableConfiguration(factor)=='Competitor'">
       <tr class="exampleRow">
           <td>
               Example: search within 50 miles, be the 2nd cheapest within 1 plate step
           </td>
           <td>
               50
           </td>
           <td>
               2
           </td>
           <td>
               1
           </td>
           <td></td>
       </tr>
   </ng-container> -->
   <ng-container
      *ngIf="
         [
            StrategyFactorName.RR_DL_Matrix,
            StrategyFactorName.RR_DS_Matrix,
            StrategyFactorName.RR_DB_Matrix,
            StrategyFactorName.DTS_DL_Matrix
         ].includes(factor.Name)
      "
   >
      <tr class="exampleRow"></tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.DaysToSell">
      <tr class="exampleRow">
         <td>Example: sell within 30 days</td>
         <td>30</td>
      </tr>
   </ng-container>

   <ng-container *ngIf="factor.Name == StrategyFactorName.DaysInStock">
      <tr class="exampleRow">
         <td>Example: add 1% above the valuation for vehicles up to 10 days</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>0-10</td>
         <td>101</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.DaysListed">
      <tr class="exampleRow">
         <td>Example: add 1% above the valuation for vehicles up to 10 days</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>10</td>
         <td>101</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.RetailRating">
      <tr class="exampleRow">
         <td>Example: add 1% above the valuation for retail rating up to 20</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>20</td>
         <td>101</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.PerformanceRatingScore">
      <tr class="exampleRow">
         <td>Example: take off 2% for performance rating up to 25</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>0-25</td>
         <td>98</td>
      </tr>
   </ng-container>
   <ng-container
      *ngIf="
         [
            StrategyFactorName.RetailRatingBand,
            StrategyFactorName.DaysListedBand,
            StrategyFactorName.OnBrandCheck,
            StrategyFactorName.RetailerName,
            StrategyFactorName.MatchCheapestCompetitor,
            StrategyFactorName.ValuationChangeUntilSell,
            StrategyFactorName.DaysInStockBand,
            StrategyFactorName.SpecificColour,
            StrategyFactorName.AchieveMarketPositionScore,
            StrategyFactorName.WholesaleAdjustment,
            StrategyFactorName.RetailRating10sBand,
            StrategyFactorName.Brand,
            StrategyFactorName.ModelName,
            StrategyFactorName.FuelType,
            StrategyFactorName.ValueBand,
            StrategyFactorName.RegYear
         ].includes(factor.Name)
      "
   >
      <tr class="exampleRow">
         <td>Example: add 1% above the valuation</td>
         <td>101</td>
         <td></td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.SpecificColour">
      <tr class="exampleRowSpecificColour">
         <td>Example: add 1% above the valuation for Urban Grey</td>
         <td></td>
         <td></td>
      </tr>
      <tr class="exampleRowSpecificColour">
         <td>Urban Grey</td>
         <td>101</td>
         <td></td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.AgeAndOwners">
      <tr class="exampleRowSpecificColour">
         <td>Example: add 1% above the valuation for 3-5yr old cars with 1 previous owner</td>
         <td></td>
         <td></td>
      </tr>
      <tr class="exampleRowSpecificColour">
         <td style="display: flex; gap: 10em; justify-content: center; align-items: center">
            <span>3-5yrs</span>
            <span>1</span>
         </td>
         <td>101</td>
         <td></td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.MinimumProfit">
      <tr class="exampleRow">
         <td>Example: £1,000 minimum profit</td>
         <td>1000</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.MinimumPricePosition">
      <tr class="exampleRow">
         <td>Example: 90% price position</td>
         <td>90.0</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.RoundToNearest">
      <tr class="exampleRow">
         <td>Example: nearest £50</td>
         <td>50</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.RoundToPriceBreak">
      <tr class="exampleRow">
         <td>Example: within £50</td>
         <td>50</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.MakeFuelType">
      <tr class="exampleRow">
         <td colspan="2">Example: Ford Electric take off 1%</td>
         <td>99</td>
      </tr>
   </ng-container>
   <ng-container *ngIf="factor.Name == StrategyFactorName.MakeAgeBand">
      <tr class="exampleRow">
         <td colspan="2">Example: Ford &lt;1 year take off 1%</td>
         <td>99</td>
      </tr>
   </ng-container>


   <ng-container *ngIf="factor.Name == StrategyFactorName.LiveMarketCondition">
      <tr class="exampleRow">
         <td>Example: Apply 99% if market condition is up to -50%</td>
         <td></td>
      </tr>
      <tr class="exampleRow">
         <td>-50</td>
         <td>99</td>
      </tr>
   </ng-container>



</ng-template>
