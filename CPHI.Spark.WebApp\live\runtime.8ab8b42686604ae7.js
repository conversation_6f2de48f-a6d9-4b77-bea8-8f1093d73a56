(()=>{"use strict";var e,v={},g={};function r(e){var n=g[e];if(void 0!==n)return n.exports;var t=g[e]={id:e,loaded:!1,exports:{}};return v[e].call(t.exports,t,t.exports,r),t.loaded=!0,t.exports}r.m=v,e=[],r.O=(n,t,o,i)=>{if(!t){var a=1/0;for(f=0;f<e.length;f++){for(var[t,o,i]=e[f],l=!0,s=0;s<t.length;s++)(!1&i||a>=i)&&Object.keys(r.O).every(b=>r.O[b](t[s]))?t.splice(s--,1):(l=!1,i<a&&(a=i));if(l){e.splice(f--,1);var d=o();void 0!==d&&(n=d)}}return n}i=i||0;for(var f=e.length;f>0&&e[f-1][2]>i;f--)e[f]=e[f-1];e[f]=[t,o,i]},r.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return r.d(n,{a:n}),n},(()=>{var n,e=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__;r.t=function(t,o){if(1&o&&(t=this(t)),8&o||"object"==typeof t&&t&&(4&o&&t.__esModule||16&o&&"function"==typeof t.then))return t;var i=Object.create(null);r.r(i);var f={};n=n||[null,e({}),e([]),e(e)];for(var a=2&o&&t;"object"==typeof a&&!~n.indexOf(a);a=e(a))Object.getOwnPropertyNames(a).forEach(l=>f[l]=()=>t[l]);return f.default=()=>t,r.d(i,f),i}})(),r.d=(e,n)=>{for(var t in n)r.o(n,t)&&!r.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:n[t]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce((n,t)=>(r.f[t](e,n),n),[])),r.u=e=>e+"."+{159:"0baa9e30b5084f06",210:"ef12c8271e654f01",678:"c9634eacbdfcbf98"}[e]+".js",r.miniCssF=e=>{},r.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),(()=>{var e={},n="stockpulseweb:";r.l=(t,o,i,f)=>{if(e[t])e[t].push(o);else{var a,l;if(void 0!==i)for(var s=document.getElementsByTagName("script"),d=0;d<s.length;d++){var c=s[d];if(c.getAttribute("src")==t||c.getAttribute("data-webpack")==n+i){a=c;break}}a||(l=!0,(a=document.createElement("script")).type="module",a.charset="utf-8",a.timeout=120,r.nc&&a.setAttribute("nonce",r.nc),a.setAttribute("data-webpack",n+i),a.src=r.tu(t)),e[t]=[o];var u=(_,b)=>{a.onerror=a.onload=null,clearTimeout(p);var h=e[t];if(delete e[t],a.parentNode&&a.parentNode.removeChild(a),h&&h.forEach(y=>y(b)),_)return _(b)},p=setTimeout(u.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=u.bind(null,a.onerror),a.onload=u.bind(null,a.onload),l&&document.head.appendChild(a)}}})(),r.r=e=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e;r.tt=()=>(void 0===e&&(e={createScriptURL:n=>n},typeof trustedTypes<"u"&&trustedTypes.createPolicy&&(e=trustedTypes.createPolicy("angular#bundler",e))),e)})(),r.tu=e=>r.tt().createScriptURL(e),r.p="",(()=>{var e={666:0};r.f.j=(o,i)=>{var f=r.o(e,o)?e[o]:void 0;if(0!==f)if(f)i.push(f[2]);else if(666!=o){var a=new Promise((c,u)=>f=e[o]=[c,u]);i.push(f[2]=a);var l=r.p+r.u(o),s=new Error;r.l(l,c=>{if(r.o(e,o)&&(0!==(f=e[o])&&(e[o]=void 0),f)){var u=c&&("load"===c.type?"missing":c.type),p=c&&c.target&&c.target.src;s.message="Loading chunk "+o+" failed.\n("+u+": "+p+")",s.name="ChunkLoadError",s.type=u,s.request=p,f[1](s)}},"chunk-"+o,o)}else e[o]=0},r.O.j=o=>0===e[o];var n=(o,i)=>{var s,d,[f,a,l]=i,c=0;if(f.some(p=>0!==e[p])){for(s in a)r.o(a,s)&&(r.m[s]=a[s]);if(l)var u=l(r)}for(o&&o(i);c<f.length;c++)r.o(e,d=f[c])&&e[d]&&e[d][0](),e[d]=0;return r.O(u)},t=self.webpackChunkstockpulseweb=self.webpackChunkstockpulseweb||[];t.forEach(n.bind(null,0)),t.push=n.bind(null,t.push.bind(t))})()})();