import { Component, OnInit, Input } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AutoPriceInsightsModalComponent } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.component';
import { AutoPriceInsightsModalService } from 'src/app/components/autoPriceInsightsModal/autoPriceInsightsModal.service';
import { AppUser } from 'src/app/model/main.model';
import { VehicleAdvertNewCommentParams } from 'src/app/model/VehicleAdvertNewCommentParams.model';
import { VehicleAdvertWithRating } from 'src/app/model/VehicleAdvertWithRating';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Component({
  selector: 'simpleAdvertCard',
  templateUrl: './simpleAdvertCard.component.html',
  styleUrls: ['./simpleAdvertCard.component.scss']
})
export class SimpleAdvertCardComponent implements OnInit {
  @Input() advert: VehicleAdvertWithRating;
  subImages: string[];
  newComment: string;

  constructor(
    public selectionsService: SelectionsService,
    public getDataMethodsService: GetDataMethodsService,
    public constantsService: ConstantsService,
    private autoPriceInsightsModalService: AutoPriceInsightsModalService,
    private modalService: NgbModal
  ) {

  }

  ngOnInit() {
    
  }

  splitImageURLs() {
    return this.advert.AllImageURLs.split('|');
  }

  onEnterPressed(event: Event): void {
    if (event instanceof KeyboardEvent && !event.shiftKey) {
      if (this.newComment && this.newComment !== '') {
        this.saveNewComment();
      }
      event.preventDefault();
    }
  }

  get advertAdjustedVsAvValn(): number{
    return this.advert.ValuationAdjAvRetail - this.advert.ValuationMktAvRetail
  }

  saveNewComment() {
    const user: AppUser = this.selectionsService.user;

    const params: VehicleAdvertNewCommentParams = {
      Text: this.newComment,
      VehicleAdvertId: this.advert.AdId,
      UserId: parseInt(user.PersonId.toString())
    }

    this.getDataMethodsService.saveNewVehicleAdvertComment(params).subscribe((res: number) => {
      this.constantsService.toastSuccess('Comment saved');
      this.advert.LastCommentName = user.Name;
      this.advert.LastCommentText = this.newComment;
      this.newComment = null;
    }, error => {
      this.constantsService.toastDanger('Failed to save comment');
      console.error('Failed to save comment', error);
    })
  }

  openModal() {
    this.autoPriceInsightsModalService.initialise(this.advert.AdId, [this.advert.AdId]);

    const modalRef = this.modalService.open(AutoPriceInsightsModalComponent, { keyboard: true, size: 'lg' });
    modalRef.result.then((result) => { });
  }
}
