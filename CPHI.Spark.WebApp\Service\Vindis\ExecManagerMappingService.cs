﻿using CPHI.Spark.WebApp.DataAccess;
using System.Collections.Generic;
using System.Threading.Tasks;
using CPHI.Spark.Model.ViewModels.Vindis;
using CPHI.Spark.DataAccess;
using CPHI.Spark.Repository;using CPHI.Spark.Model;

namespace CPHI.Spark.WebApp.Service
{
    public interface IExecManagerMappingService
    {
        Task<IEnumerable<ExecManagerMappingRow>> GetExecManagerMappingRows(int year, DealerGroupName dealerGroup);
        Task<IEnumerable<ExecManager>> GetManagers(DealerGroupName dealerGroup);
        Task<int> UpdateManagers(ExecManagerUpdateParams parms, DealerGroupName dealerGroup);
    }


    public class ExecManagerMappingService : IExecManagerMappingService
    {

        private readonly IExecManagerMappingDataAccess execManagerMappingDataAccess;
        private readonly int userId;


        public ExecManagerMappingService(IExecManagerMappingDataAccess execManagerMappingDataAccess, IUserService userService)
        {
            this.execManagerMappingDataAccess = execManagerMappingDataAccess;
            userId = userService.GetUserId();
        }


        public async Task<IEnumerable<ExecManagerMappingRow>> GetExecManagerMappingRows(int year, DealerGroupName dealerGroup)
        {
            return await execManagerMappingDataAccess.GetExecManagerMappingRows(year, this.userId, dealerGroup);
        }

        public async Task<IEnumerable<ExecManager>> GetManagers(DealerGroupName dealerGroup)
        {
            return await execManagerMappingDataAccess.GetManagers(dealerGroup);
        }

        public async Task<int> UpdateManagers(ExecManagerUpdateParams parms, DealerGroupName dealerGroup)
        {
            return await execManagerMappingDataAccess.UpdateManagers(parms, dealerGroup);
        }


    }



}
