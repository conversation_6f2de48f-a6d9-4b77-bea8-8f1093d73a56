import { Component, Input, OnInit } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DealDetailsComponent } from "src/app/components/dealDetails/dealDetails.component";
import { Deal } from 'src/app/model/sales.model';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Component({
    selector: 'dealListPopover',
    template:        `
    
        <div class="outerContainer"  >
            <div class="popoverContainer" 
             placement="left" 
             container="body" 
             [ngbPopover]="popContent" 
             triggers="click" 
             popoverTitle="Order Details (click for more)"
             >
                {{score|cph:'number':0}}    
            </div>

        </div>

        
        <ng-template class="popover" #popContent>
            <table>
            <tbody>
            <tr (click)="loadDeal(deal)"  *ngFor="let deal of deals" >
                <td>   {{deal.Customer}}</td>
                <td> Ordered  {{deal.OrderDate|cph:'shortDate'}}</td>
                <td>  Delivery {{deal.ActualDeliveryDate|cph:'shortDate':0}}</td>
            </tr>
          
            </tbody>
            </table>
        </ng-template>


    `
    ,
    styles: [
        `
        .outerContainer{cursor:pointer;}
        .outerContainer:hover{transform:scale(1.2);color:var(--brightColour)}
        tr:hover td{background:var(--brightColourLighter)}
        
    
    `]
})
export class DealListPopoverComponent implements OnInit {
    @Input() score: number;
    @Input() deals: Deal[];
    

    constructor(
        public constants: ConstantsService,
        public selections: SelectionsService,
        public modalService: NgbModal,
    ) {
    }

    

    ngOnInit(): void {

    }

    refresh(): boolean {
        return false;
    }

    loadDeal(deal:Deal){
        this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading })
        setTimeout(() => {
            
            let modalRef;

            if(this.constants.environment.dealDetails.componentName == 'DealDetailsRRGComponent'){
              modalRef = this.modalService.open(DealDetailsComponent);
            }
        
            if(this.constants.environment.dealDetails.componentName == 'DealDetailsComponent'){
                modalRef = this.modalService.open(DealDetailsComponent);
            }
        
          //I give to modal
          modalRef.componentInstance.givenDealId = deal.Id;
          modalRef.result.then((result) => { //I get back from modal
            if (result) {
              //thing
            }
          });
        }, 10)
    }

   
}
