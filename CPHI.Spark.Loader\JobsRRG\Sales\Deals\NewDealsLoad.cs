﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using CPHI.Repository;
using log4net;
using System.Text.RegularExpressions;
using System.Globalization;
using Microsoft.EntityFrameworkCore;
using CPHI.Spark.Loader.Comparers;
using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using System.Diagnostics;
using System.Threading.Tasks;

namespace CPHI.Spark.Loader


{

    class NewDealsLoad
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(NewDealsLoad));
        private string fileSearch = "*SPK1t.csv";
        /*
| Column                | Example                        | Sensitive           |
|-----------------------|--------------------------------|---------------------|
| Suffix                | 10049710                       | No                  |
| Customer              | Mr <PERSON>                    | YES - Customer name |
| Sales Exec            | LEFT Stephen Kent<PERSON>            | No                  |
| Reg No                | DG22AZP                        | No                  |
| Fr                    | D                              | No                  |
| Enquiry               | RETAIL                         | No                  |
| Fin                   | 0                              | No                  |
| NL Total              | -33                            | No                  |
| VM Profit             | 922.93                         | No                  |
| Bran                  | 45                             | No                  |
| Sale                  | 0                              | No                  |
| Discount              | 0                              | No                  |
| O/Allow               | 0                              | No                  |
| COS                   | 0                              | No                  |
| Fin Sub               | 0                              | No                  |
| Fac Bon               | 0                              | No                  |
| Reg Bon               | 0                              | No                  |
| Broker                | 0                              | No                  |
| Acc Sale              | 0                              | No                  |
| Acc COS               | 0                              | No                  |
| Fuel Sale             | 0                              | No                  |
| Fuel COS              | 0                              | No                  |
| Warr Sale             | 0                              | No                  |
| Warr COS              | 0                              | No                  |
| Cosm Comm             | 0                              | No                  |
| Cosm Sale             | 0                              | No                  |
| Cosm COS              | 0                              | No                  |
| GAP Comm              | 0                              | No                  |
| GAP Sale              | 0                              | No                  |
| GAP COS               | 0                              | No                  |
| SP Sale               | 0                              | No                  |
| SP COS                | -33                            | No                  |
| Del Sale              | 0                              | No                  |
| Del COS               | 0                              | No                  |
| RCI Comm              | 0                              | No                  |
| Fin Comm              | 0                              | No                  |
| Select                | 0                              | No                  |
| Pro+                  | 0                              | No                  |
| Stds                  | 0                              | No                  |
| PDI                   | 0                              | No                  |
| Intro                 | 0                              | No                  |
| Error                 | 0                              | No                  |
| Inv Date              | 44662                          | No                  |
| LC                    | ***                            | No                  |
| Exec Code             | dg59480                        | No                  |
| Finance Co            |                                | No                  |
| Reg Date              | 44659                          | No                  |
| Source                | Sales Department               | No                  |
| Model                 | Duster                         | No                  |
| Vehicle Desc          | Duster 1.0 TCe 90 Prestige 5dr | No                  |
| Del Date              | 44662                          | No                  |
| Age                   | 46                             | No                  |
| Adopted               | 44616                          | No                  |
| VarClass              | CAR                            | No                  |
| Confirmed Date        | 44659                          | No                  |
| Delivered             | 1                              | No                  |
| Hand Date             | 44662                          | No                  |
| Hand Time             | 0.734722222                    | No                  |
| Model Year            | 2021                           | No                  |
| Enquiry               | 1105884                        | No                  |
| Sorder                | 1459240                        | No                  |
| Paint Protection Cost | 0                              | No                  |
| Paint Protection Sale | 0                              | No                  |

*/



        public async Task LoadProfitReport()
        {
            Stopwatch stopwatch = new Stopwatch();
            string errorMessage = string.Empty;
            stopwatch.Start();

            string[] allMatchingFiles = Directory.GetFiles(ConfigService.incomingRoot, fileSearch);


            if (allMatchingFiles.Length == 0)
            {
                //nothing found
                TimeSpan age = DateTime.UtcNow - PulsesService.NewDeals;

                if (age.Minutes > 120)
                {
                    PulsesService.NewDeals = DateTime.UtcNow;
                    Logger.Info($@"[{DateTime.UtcNow}]  | No files found matching pattern *SPK1t.csv");
                }
                return;
            }

            string fileToProcess = allMatchingFiles[0];

            //try opening the file, if fail, return (common problem is loader trying to open file whilst scraper is saving it).
            try { FileStream fs = File.Open(fileToProcess, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None); fs.Close(); }
            catch (IOException) { return; }


            //define Lists
            List<DealHistoricHeaderDataItem> dbDealsAtInvoiceStatus;
            List<Deal> dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater;

            List<EnquiryAndOrderDate> dbEnquiriesAndOrders;
            List<Site> sites;
            List<StandingValue> standingValues;
            List<VehicleType> vehicleTypes;
            List<OrderType> orderTypes;
            List<Person> people;
            List<GlobalParam> dbGlobalParams;

            using (var db = new CPHIDbContext())

            {
                using (var dapper = new Dapperr())
                {
                    int errorCount = 0;
                    LogMessage logMessage = new LogMessage();
                    logMessage.DealerGroup_Id = 1;


                    try
                    {
                        //LocksService.NewDeals = true;
                        if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
                        {
                            System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
                        }
                        DateTime start = DateTime.UtcNow;
                        //set Lists
                        int openingDbCount = dapper.Get<int>("SELECT COUNT(*) FROM Deals WHERE IsRemoved = 0", null, System.Data.CommandType.Text);
                        DateTime threshold = DateTime.UtcNow.AddMonths(-6);



                        int newVehTypeId = db.VehicleTypes.First(x => x.Code == "NEW").Id;
                        dbDealsAtInvoiceStatus = db.Deals.Include(x => x.Site).Where(d => d.VehicleType_Id == newVehTypeId && !d.IsRemoved && d.IsInvoiced).Select(x => new DealHistoricHeaderDataItem()
                        {
                            //StockNumber, Customer, SiteCode, OrderDate, Salesman_Id
                            StockNumber = x.StockNumber,
                            Customer = x.Customer,
                            SiteCode = x.Site.Code,
                            OrderDate = x.OrderDate,
                            Salesman_Id = x.Salesman_Id != null ? (int)x.Salesman_Id : 0,
                            InvoiceDate = x.InvoiceDate,
                            OrderType_Id = (int)x.OrderType_Id
                        }).ToList();

                        dbEnquiriesAndOrders = dapper.GetAll<EnquiryAndOrderDate>("SELECT EnquiryNumber,OrderDate FROM Deals", null, System.Data.CommandType.Text).ToList();// db.Deals.Select(x => new EnquiryAndOrderDate { EnquiryNumber = x.EnquiryNumber, OrderDate = x.OrderDate }).ToList();

                        dbGlobalParams = db.GlobalParams.ToList();
                        sites = db.Sites.ToList();
                        standingValues = db.StandingValues.ToList();
                        vehicleTypes = db.VehicleTypes.ToList();
                        orderTypes = db.OrderTypes.ToList();
                        people = db.People.ToList();


                        logMessage.SourceDate = DateTime.UtcNow;
                        logMessage.Job = GetType().Name;

                        Logger.Info($@"[{DateTime.UtcNow}] New file found at {ConfigService.incomingRoot}.  Starting {fileToProcess} ");   //update logger 

                        //define variables for use in processing this file
                        int removedCount = 0;
                        int newCount = 0;
                        int changedCount = 0;
                        int incomingProcessCount = 0;
                        List<Diff> diffs = new List<Diff>(); //create empty list

                        if (File.Exists(fileToProcess.Replace(".csv", "-p.csv")))
                        {
                            //already processing a file of this type, skip
                            Logger.Error($@"Could not interpret {fileToProcess}, -p file already found ");
                            logMessage.FailNotes = logMessage.FailNotes + "Processing file already found ";
                            throw new Exception("Processing file already found");
                        }

                        string fileName = fileToProcess.Split('\\')[4];
                        var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
                        DateTime fileDateMidday = new DateTime(fileDate.Year, fileDate.Month, fileDate.Day, 12, 00, 00);
                        logMessage.SourceDate = fileDate;
                        File.Move(fileToProcess, fileToProcess.Replace(".csv", "-p.csv")); //append _processing to the file to prevent any other instances also processing these files
                        var newFilepath = fileToProcess.Replace(".csv", "-p.csv");

                        List<Deal> incomingDeals = new List<Deal>(10000);  //preset the list size (slightly quicker than growing it each time)
                        var rows = GetDataFromFilesService.GetRowsCsv(newFilepath);

                        if (rows.Length < 2)
                        {
                            //file is too short to have anything meaningful in, just move then finish
                            File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                            Logger.Error($@"File had less than 2 rows, moving and finishing");
                            return;
                        }

                        var headers = rows.Skip(1).First().ToUpper().Split(',');
                        //List<Deal> removedEnquiryNumbers = db.Deals.Where(x => x.IsRemoved).ToList();

                        //firstly get all the stocknumbers
                        List<string> stockNumbers = new List<string>(rows.Count());
                        foreach (var row in rows.Skip(2))
                        {
                            if (string.IsNullOrEmpty(row)) { continue; }
                            var cells = Regex.Matches(row, "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))")
                                    .Cast<Match>()
                                    .Select(m => m.Value)
                                    .ToArray();

                            if (cells.Length != headers.Length)
                            {
                                //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                                continue;
                            }
                            string stockNumber = cells[Array.IndexOf(headers, "SUFFIX")];
                            if (stockNumber == "Totals") { continue; }
                            if(stockNumber== "695635") { continue; }  //some snafu
                            int slashIndex = stockNumber.IndexOf("/");
                            if (slashIndex == -1)
                            {
                                if (stockNumber.Length != 8) { continue; }
                                stockNumber = $"{stockNumber.Substring(0, 7)}/{stockNumber.Substring(7, 1)}";
                            }
                            stockNumbers.Add(stockNumber);
                        }

                        List<SpecCommissionLine> allSpecCommLines = HelpersService.GetSpecCommissionLines(stockNumbers, dapper);


                        Dictionary<string, int> headerLookup = createHeaderDictionary(headers);

                        foreach (var row in rows.Skip(2))
                        {
                            incomingProcessCount++;

                            try
                            {
                                if (string.IsNullOrEmpty(row)) { continue; } //skip empties

                                var rowCols = Regex.Matches(row, "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))")
                                    .Cast<Match>()
                                    .Select(m => m.Value)
                                    .ToArray();

                                if (rowCols.Length != headers.Length)
                                {
                                    //something weird happened, not got enough rowCols for the number of headerCols, skip this record

                                    logMessage.FailNotes = logMessage.FailNotes + $"{rowCols[headerLookup["SUFFIX"]]}: Skipped rowCol as had {rowCols.Length} rowCols and needed {headers.Length}"; ;
                                    errorCount++;
                                    continue;
                                }

                                //check if Totals row, if so skip on
                                string suffix = rowCols[headerLookup["SUFFIX"]];
                                if (suffix == "Totals") { continue; }

                                //check if ECOP car, if so, skip on
                                string enquiry = rowCols[headerLookup["ENQUIRY"]];
                                if (enquiry.Replace(" ", "").Length > 0)  // don't test if enquiry length is very short which likely indicates is a cancelled deal
                                {
                                    string enquiryRight4 = enquiry.Substring(enquiry.Length - 4, 4);
                                    if (enquiryRight4 == "ECOP" || enquiryRight4 == "LOAN") { continue; }  //unlikely 
                                }

                                string stockNumber = rowCols[headerLookup["SUFFIX"]];
                                if (stockNumber.Count() < 8) continue;
                                //updated 2 October. CDK had a problem with stocknumbers > 999999 as they couldn't fit characters into the report so 999999/1 is ok but 1000000/1 didn't work so they switched to 10000001 i.e. dropped the /
                                //have to put the slash back in
                                //example old: 105976/1
                                //example now: 10043651
                                int slashIndex = stockNumber.IndexOf("/");
                                if (slashIndex == -1)
                                {
                                    stockNumber = $"{stockNumber.Substring(0, 7)}/{stockNumber.Substring(7, 1)}";
                                }

                                //things that we might need to get from matching Deal
                                string customer = HelpersService.LimitTo(rowCols[headerLookup["CUSTOMER"]], 50); ;
                                string givenExecCode = rowCols[headerLookup["EXEC CODE"]];
                                int? siteCode = null;
                                DateTime orderDate = DateTime.UtcNow;

                                //check if salesman is blank if so lookup salesman and customer from existing deals, must be there (!)
                                DealHistoricHeaderDataItem historicDeal = null;
                                if (givenExecCode.Replace(" ", "").Length == 0)
                                {
                                    try { historicDeal = dbDealsAtInvoiceStatus.First(a => a.StockNumber == suffix); } catch { continue; }
                                    givenExecCode = people.First(p => p.Id == historicDeal.Salesman_Id).DmsId;
                                    customer = historicDeal.Customer;
                                    siteCode = historicDeal.SiteCode;
                                    orderDate = historicDeal.OrderDate;
                                }
                                else // StockNumber, Customer, SiteCode, OrderDate, Salesman_Id
                                {
                                    siteCode = int.Parse(rowCols[headerLookup["BRAN"]]);
                                    orderDate = DateTime.ParseExact(rowCols[headerLookup["CONFIRMED DATE"]], "dd/MM/yyyy", null);
                                }






                                //adjust the order date back to a previous order date if we have a new order with an enquiry number matching that of a previously removed order.
                                string enquiryNumber = rowCols[Array.LastIndexOf(headers, "MODEL YEAR") + 1];

                                if (enquiryNumber != "0" && enquiryNumber != null)
                                {
                                    EnquiryAndOrderDate match = dbEnquiriesAndOrders.FirstOrDefault(x => x.EnquiryNumber == int.Parse(enquiryNumber));
                                    if (match != null) orderDate = match.OrderDate;
                                }

                                Site site = null;
                                try { site = sites.First(s => s.Code == siteCode); } catch { continue; } //if can't find site, skip on
                                if (site.Description == "ECOP") { continue; } //skip on if ECOP
                                if (site.Description == "Nissan Bolton") { continue; } //skip on if Nissan Bolton, closed site, tiny numbers come through, don't want in Spark
                                if (site.Description == "Slough") { continue; } //skip on if Slough
                                if (site.Description == "Southern Fleet") { continue; }//skip on for these

                                var fran = standingValues.First(s => s.Code == "R"); //start with Renault.  In testing found 1 in a thousand cars is blank.
                                try { fran = standingValues.First(s => s.Code == rowCols[headerLookup["FR"]]); } catch { }
                                OrderType oType = null;
                                if (historicDeal == null)
                                {
                                    try { oType = orderTypes.First(o => o.Code == rowCols[headerLookup["ENQUIRY"]]); }
                                    catch { oType = orderTypes.First(o => o.Code == "RETAIL"); }
                                }
                                else { oType = orderTypes.First(o => o.Id == historicDeal.OrderType_Id); }

                                var salesman = people.FirstOrDefault(o => o.DmsId == givenExecCode);

                                if (salesman == null)
                                {
                                    throw new Exception($"Could not find person matching {givenExecCode}");
                                }

                                var vehClass = standingValues.First(o => o.Code == rowCols[headerLookup["VARCLASS"]]);
                                var vehType = vehicleTypes.First(v => v.Code == "NEW");

                                //other things to work out
                                var units = 1;
                                bool isLateCost = rowCols[headerLookup["LC"]] == "***";
                                var saleValue = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["SALE"]]);




                                //is it a reversal
                                if (saleValue < -5000) //less than -£5k sales 
                                {
                                    units = -1;
                                }
                                if (saleValue >= -5000 && saleValue < 200) { units = 0; }  //

                                //dates: nullable
                                DateTime? stockDate = null;
                                DateTime? registeredDate = null;
                                DateTime invoiceDate;
                                DateTime? invoiceDateGiven = null;
                                DateTime deliveryDate;
                                DateTime? deliveryDateProvided = null;
                                DateTime? handoverDate = null;

                                try { stockDate = DateTime.ParseExact(rowCols[headerLookup["ADOPTED"]], "dd/MM/yyyy", null); } catch { }
                                try { registeredDate = DateTime.ParseExact(rowCols[headerLookup["REG DATE"]], "dd/MM/yyyy", null); } catch { }
                                try { invoiceDateGiven = DateTime.ParseExact(rowCols[headerLookup["INV DATE"]], "dd/MM/yyyy", null); } catch { }
                                try { deliveryDateProvided = DateTime.ParseExact(rowCols[headerLookup["DEL DATE"]], "dd/MM/yyyy", null); } catch { }
                                try
                                {
                                    string handoverDateDate = rowCols[headerLookup["HAND DATE"]];
                                    string handoverDateTime = rowCols[headerLookup["HAND TIME"]];
                                    if (handoverDateTime == "") handoverDateDate = "09:00";
                                    handoverDate = DateTime.ParseExact(handoverDateDate + handoverDateTime, "dd/MM/yyyyHH:mm", CultureInfo.InvariantCulture);
                                }
                                catch 
                                {
                                    { }
                                }


                                // db.SpecCommissionLines.Where(x => !x.IsRemoved && x.StockNumber == stockNumber && x.EnquiryNum == int.Parse(enquiryNumber)).ToList().Last();
                                SpecCommissionLine specCommLine = null;
                                try
                                {
                                    specCommLine = allSpecCommLines.Where(x => x.StockNumber == stockNumber && x.EnquiryNum == int.Parse(enquiryNumber)).Last();
                                }
                                catch
                                {

                                }

                                //if no delivery date (in testing, found occasionally vehicle wouldn't have one) then use invoice date or if not available use confirmed order date + 7 for used + 60 for new
                                DateTime deliveryDateProxy = orderDate.AddDays(60);
                                if (oType.Code2 == "FLEET") { deliveryDateProxy = orderDate.AddDays(365); }

                                deliveryDate = deliveryDateProvided ?? invoiceDateGiven ?? deliveryDateProxy;

                                //invoice date
                                if (historicDeal != null)
                                {
                                    invoiceDate = invoiceDateGiven ?? historicDeal.InvoiceDate ?? deliveryDateProvided ?? fileDate;
                                }
                                else
                                {
                                    invoiceDate = invoiceDateGiven ?? deliveryDateProvided ?? fileDate;
                                }

                                //find insurance products

                                decimal salePaint = 0;
                                decimal saleGap = 0;
                                decimal saleCosmetic = 0;
                                decimal saleAccidentRepair = 0;
                                decimal saleTyre = 0;
                                decimal saleAlloy = 0;
                                decimal saleWarranty = 0;
                                decimal saleTyreAlloy = 0;
                                decimal saleWheelGuard = 0;
                                decimal saleServicePlanSpecLines = 0;

                                decimal costPaint = 0;
                                decimal costGap = 0;
                                decimal costCosmetic = 0;
                                decimal costAccidentRepair = 0;
                                decimal costTyre = 0;
                                decimal costAlloy = 0;
                                decimal costWarranty = 0;
                                decimal costTyreAlloy = 0;
                                decimal costWheelGuard = 0;
                                decimal costServicePlanSpecLines = 0;

                                if (!isLateCost && units != 0 && specCommLine != null)
                                {
                                    salePaint = specCommLine.SalePaint * units;
                                    saleGap = specCommLine.SaleGap * units;
                                    saleCosmetic = specCommLine.SaleCosmetic * units;
                                    saleAccidentRepair = specCommLine.SaleAccidentRepair * units;
                                    saleTyre = specCommLine.SaleTyre * units;
                                    saleAlloy = specCommLine.SaleAlloy * units;
                                    saleWarranty = specCommLine.SaleWarranty * units;
                                    saleTyreAlloy = specCommLine.SaleTyreAlloy * units;
                                    saleWheelGuard = specCommLine.SaleWheelGuard * units;
                                    saleServicePlanSpecLines = specCommLine.SaleServicePlan * units;

                                    costPaint = specCommLine.CostPaint * -1 * units;
                                    costGap = specCommLine.CostGap * -1 * units;
                                    costCosmetic = specCommLine.CostCosmetic * -1 * units;
                                    costAccidentRepair = specCommLine.CostAccidentRepair * -1 * units;
                                    costTyre = specCommLine.CostTyre * -1 * units;
                                    costAlloy = specCommLine.CostAlloy * -1 * units;
                                    costWarranty = specCommLine.CostWarranty * -1 * units;
                                    costTyreAlloy = specCommLine.CostTyreAlloy * -1 * units;
                                    costWheelGuard = specCommLine.CostWheelGuard * -1 * units;
                                    costServicePlanSpecLines = specCommLine.CostServicePlan * -1 * units;
                                }

                                //columns from report
                                decimal gapSaleAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["GAP SALE"]]);
                                decimal cosmeticSaleAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["COSM SALE"]]);
                                decimal accsSaleAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["ACC SALE"]]);

                                decimal gapCostAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["GAP COS"]]);
                                decimal cosmeticCostAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["COSM COS"]]);
                                decimal accsCostAsGiven = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["ACC COS"]]);

                                if (salesman == null)
                                {
                                    throw new Exception("No salesmanId");
                                }

                                decimal financeSubsidy = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FIN SUB"]]);
                                decimal rciFinanceCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["RCI COMM"]]);
                                decimal financeCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FIN COMM"]]);
                                decimal selectCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["SELECT"]]);
                                decimal proPlusCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["PRO+"]]);

                                bool isFinanced = (financeSubsidy + rciFinanceCommission + financeCommission + selectCommission + proPlusCommission) != 0;

                                Deal d = new Deal(); //initialise new one
                                d.CreatedDate = fileDate;
                                d.WhenNew = fileDate;
                                d.StockNumber = stockNumber;
                                d.EnquiryNumber = int.Parse(enquiryNumber);
                                d.Customer = customer;
                                d.Reg = rowCols[headerLookup["REG NO"]];
                                d.Franchise_Id = fran.Id;
                                d.OrderType_Id = oType.Id;
                                d.IsFinanced = isFinanced;
                                d.TotalNLProfit = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["NL TOTAL"]]);
                                //d.TotalVehicleProfit = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["VM PROFIT"]]);
                                d.Site_Id = site.Id;
                                d.Sale = saleValue;
                                d.Discount = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["DISCOUNT"]]);
                                d.PartExOverAllowance1 = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["O/ALLOW"]]);
                                d.CoS = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["COS"]]);
                                d.FinanceSubsidy = financeSubsidy;
                                d.NewBonus1 = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FAC BON"]]);
                                d.NewBonus2 = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["REG BON"]]);
                                d.BrokerCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["BROKER"]]);
                                d.AccessoriesSale = accsSaleAsGiven - salePaint - saleWheelGuard;
                                d.AccessoriesCost = accsCostAsGiven - costPaint - costWheelGuard;
                                d.FuelSale = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FUEL SALE"]]);
                                d.FuelCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["FUEL COS"]]);
                                d.WarrantySale = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["WARR SALE"]]);
                                d.WarrantyCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["WARR COS"]]);
                                d.CosmeticInsuranceCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["COSM COMM"]]);
                                d.CosmeticInsuranceSale = cosmeticSaleAsGiven - saleTyreAlloy - saleTyre;
                                d.CosmeticInsuranceCost = cosmeticCostAsGiven - costTyreAlloy - costTyre;
                                d.PaintProtectionSale = salePaint;
                                d.PaintProtectionCost = costPaint;
                                d.GapInsuranceCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["GAP COMM"]]);
                                d.GapInsuranceSale = gapSaleAsGiven;
                                d.GapInsuranceCost = gapCostAsGiven;
                                d.TyreInsuranceSale = saleTyre;
                                d.TyreInsuranceCost = costTyre;
                                d.TyreAndAlloyInsuranceSale = saleTyreAlloy;
                                d.TyreAndAlloyInsuranceCost = costTyreAlloy;
                                d.WheelGuardSale = saleWheelGuard;
                                d.WheelGuardCost = costWheelGuard;

                                // From Profit Report
                                decimal saleServicePlanProfitReport = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["SP SALE"]]);
                                decimal costServicePlanProfitReport = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["SP COS"]]);

                                // If there are differences between ProfitReport and SpecLines, add to an Error property
                                decimal serviceError = (saleServicePlanProfitReport - saleServicePlanSpecLines) + (costServicePlanProfitReport - costServicePlanSpecLines);

                                // From SpecLInes if applicable
                                d.ServicePlanCost = costServicePlanSpecLines;
                                d.ServicePlanSale = saleServicePlanSpecLines;

                                d.OemDeliverySale = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["DEL SALE"]]);
                                d.OemDeliveryCost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["DEL COS"]]);

                                d.RCIFinanceCommission = rciFinanceCommission;
                                d.FinanceCommission = financeCommission;
                                d.SelectCommission = selectCommission;
                                d.ProPlusCommission = proPlusCommission;
                                d.StandardsCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["STDS"]]);
                                d.PDICost = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["PDI"]]);
                                d.IntroCommission = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["INTRO"]]);
                                d.Error = HelpersService.DecimalParseAndValidate(rowCols[headerLookup["ERROR"]]) + serviceError;
                                d.InvoiceDate = invoiceDate;
                                d.IsLateCost = isLateCost;
                                d.Salesman_Id = salesman?.Id;
                                d.FinanceCo = rowCols[headerLookup["FINANCE CO"]];
                                d.RegisteredDate = registeredDate;
                                d.Model = rowCols[headerLookup["MODEL"]];
                                d.Description = HelpersService.LimitTo(rowCols[headerLookup["VEHICLE DESC"]], 50);
                                d.ActualDeliveryDate = deliveryDate;
                                d.VehicleAge = int.Parse(rowCols[headerLookup["AGE"]]);
                                d.StockDate = stockDate;
                                d.VehicleClass_Id = vehClass.Id;
                                d.OrderDate = orderDate;
                                d.IsDelivered = int.Parse(rowCols[headerLookup["DELIVERED"]]) == 1;
                                d.VariantTxt = HelpersService.LimitTo(rowCols[headerLookup["VEHICLE DESC"]], 50);
                                d.VehicleType_Id = vehType.Id;
                                d.Units = units;
                                d.HandoverDate = handoverDate;




                                string modelYearStr = rowCols[headerLookup["MODEL YEAR"]].Trim();

                                if (modelYearStr == "")
                                {
                                    d.ModelYear = 0;
                                }
                                else
                                {
                                    d.ModelYear = int.Parse(modelYearStr);
                                }
                                

                                //profit subtotals
                                //d.FAndIProfit = d.FinanceSubsidy + d.WarrantySale + d.PaintProtectionSale + d.PaintProtectionCost
                                //    + d.WarrantyCost + d.CosmeticInsuranceSale + d.CosmeticInsuranceCost + d.CosmeticInsuranceCommission
                                //    + d.GapInsuranceSale + d.GapInsuranceCost
                                //    + d.GapInsuranceCommission + d.ServicePlanSale + d.ServicePlanCost
                                //    + d.RCIFinanceCommission + d.FinanceCommission + d.SelectCommission + d.ProPlusCommission + d.StandardsCommission
                                //    + d.TyreAlloySale + d.TyreAlloyCost 
                                //    + d.WheelGuardSale + d.WheelGuardCost;

                                //products
                                d.IsInvoiced = true;
                                d.HasServicePlan = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasServicePlan : false;
                                d.HasCosmeticInsurance = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasCosmetic : false;// d.CosmeticInsuranceSale > 0 || d.CosmeticInsuranceCommission > 0;
                                d.HasGapInsurance = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasGap : false;// d.GapInsuranceSale > 0 || d.GapInsuranceCommission > 0;
                                d.HasPaintProtection = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasPaint : false;
                                d.HasWarranty = specCommLine != null && !isLateCost && units != 0 ? (specCommLine.HasWarranty || specCommLine.HasWarranty2Yr || specCommLine.HasWarrantyLifetime) : false;
                                d.HasShortWarranty = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasShortWarranty : false;
                                d.HasTyreInsurance = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasTyre : false;
                                d.HasTyreAndAlloyInsurance = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasTyreAndAlloy : false;
                                d.HasWheelGuard = specCommLine != null && !isLateCost && units != 0 ? specCommLine.HasWheelGuard : false;
                                //d.TotalProductCount = HelpersService.CountTrue(d.HasServicePlan, d.HasPaintProtection, d.HasCosmeticInsurance, d.HasGapInsurance, d.HasWarranty, d.HasTyreAlloy,  d.HasWheelGuard.Value);

                                d.LastUpdated = DateTime.UtcNow;
                                incomingDeals.Add(d);
                            }

                            catch (Exception err)
                            {
                                if (errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()} \r\r\n";
                                errorCount++;
                                continue;
                            }

                        }


                        //now add accouting year and Month
                        //find out what month the incoming profit report relates to
                        //sort all deals which have an invoice date and that have sale > 10k by invoice date and find the youngest.  This will tell you the month this report relates to
                        DateTime incomingDealsDate = incomingDeals.Where(x => x.InvoiceDate != null && x.Sale > 10000).Select(x => x.InvoiceDate).OrderBy(x => x).Take(1).ToList()[0] ?? fileDate;
                        DateTime incomingLinesOpeningDate = new DateTime(incomingDealsDate.Year, incomingDealsDate.Month, 1, 0, 0, 0);

                        int daysInMonth = DateTime.DaysInMonth(incomingDealsDate.Year, incomingDealsDate.Month);
                        DateTime incomingLinesClosingDate = new DateTime(incomingDealsDate.Year, incomingDealsDate.Month, daysInMonth, 22, 59, 59);
                        DateTime earlierOfReportDateAndIncomingLinesClosingDate = fileDateMidday > incomingLinesClosingDate ? incomingLinesClosingDate : fileDateMidday;
                        DateTime middayOnFirstDayOfMonth = new DateTime(incomingLinesOpeningDate.Year, incomingLinesOpeningDate.Month, incomingLinesOpeningDate.Day, 12, 00, 00);

                        //update latestAccountingDate within globalParams
                        //GlobalParam latestAccountingDate = dbGlobalParams.First(x => x.Description == "Latest Accounting Date");
                        //latestAccountingDate.TextValue = incomingDealsDate.Year.ToString() + incomingDealsDate.Month.ToString();

                        dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater = db.Deals.Where(d => d.VehicleType_Id == newVehTypeId && !d.IsRemoved && d.IsInvoiced && d.AccountingDate >= incomingLinesOpeningDate).ToList();



                        //set accounting year/month and work out if it is a late cost
                        foreach (var deal in incomingDeals)
                        {
                            try
                            {
                                //if deal is invoiced prior to the start of this month, should give it earlier of today's date or end of this month.  Otherwise go with its invoice date
                                if (deal.InvoiceDate < incomingLinesOpeningDate)
                                {
                                    deal.AccountingDate = middayOnFirstDayOfMonth;
                                }
                                else
                                {
                                    DateTime invDate = deal.InvoiceDate ?? deal.OrderDate; //the invoice date will always be not null for deals where invoiced is true i.e. everything this loader is looking at
                                    deal.AccountingDate = invDate;
                                }



                            }
                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + $" failed on adding accountingYearAndMonth {deal.StockNumber} {err.ToString()}";
                                errorCount++;
                                continue;
                            }

                        }

                        DateTime finishedInterpetFile = DateTime.UtcNow;



                        //############ NEW CARS
                        //add the new ones that are not late costs 
                        var newNonLateCostItems = incomingDeals.Where(d => !d.IsLateCost).Except(dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.Where(d => !d.IsLateCost), new DealStockNoComp());
                        newCount = newNonLateCostItems.Count();

                        try
                        {
                            foreach (var item in newNonLateCostItems)
                            {
                                item.OriginalSource = "NewDeals" + fileDate.ToString("yyyyMMdd_HHmmss");
                            }
                            db.Deals.AddRange(newNonLateCostItems);  //add them all in one go
                        }
                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $"Failed addingNewDeals range {err.ToString()}";
                            errorCount++;
                        }

                        //add the new late costs to the database
                        var newLateCostItems = incomingDeals.Where(d => d.IsLateCost).Except(dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.Where(d => d.IsLateCost), new DealStockNoComp());
                        newCount = newCount + newLateCostItems.Count();

                        try
                        {
                            foreach (var item in newLateCostItems)
                            {
                                item.OriginalSource = "NewDeals" + fileDate.ToString("yyyyMMdd_HHmmss");
                            }

                            db.Deals.AddRange(newLateCostItems);  //add them all in one go
                        }
                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $"Failed addingNewDeals range {err.ToString()}";
                            errorCount++;
                        }



                        //############  REMOVED CARS
                        //standard method
                        void removeDeals(List<Deal> deals)
                        {
                            foreach (var Deal in deals)
                            {
                                try
                                {
                                    Deal.IsRemoved = true;
                                    Deal.RemovedDate = DateTime.UtcNow;
                                    Deal.IsUpdated = true;
                                    Deal.LastUpdated = DateTime.UtcNow;
                                    removedCount++;

                                    //generate a diff also
                                    Diff newDiff = new Diff()
                                    {
                                        Model = "Deal",
                                        ModelIdent = Deal.StockNumber,
                                        Key = "IsRemoved",
                                        OldValue = "False",
                                        NewValue = "True",
                                        UpdateDate = fileDate,
                                    };

                                    diffs.Add(newDiff);
                                }

                                catch (Exception err)
                                {
                                    logMessage.FailNotes = logMessage.FailNotes + $" failed on removing item {Deal.StockNumber}" + err.ToString();
                                    errorCount++;
                                }

                            }
                        }

                        //START HERE-->


                        //find any invoiced cars that have now been removed from the current month.  Only relevant for invoiced cars from db.  
                        //List<Deal> incomingDealsForCurrentMonth = incomingDeals.Where(x => x.AccountingDate <= incomingLinesClosingDate).ToList();
                        //Firstly for nonLate costs
                        List<Deal> removedNonLates = dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.Where(r => !r.IsLateCost).Except(incomingDeals.Where(d => !d.IsLateCost), new DealStockNoComp()).ToList();
                        removeDeals(removedNonLates);

                        //Secondly for late costs.  For lates need to also ensure we are only looking at lates that were loaded to the same accounting period as the current report
                        List<Deal> removedLates = dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.Where(r => r.IsLateCost).Except(incomingDeals.Where(d => d.IsLateCost), new DealStockNoComp()).ToList();
                        removeDeals(removedLates);


                        if (removedLates.Count() > 200 || removedNonLates.Count() > 200) throw new Exception("Too many removed!");


                        //#############  CHANGED
                        List<Deal> changed = new List<Deal>(10000);

                        //define logic
                        void diffDeals(List<Deal> dealsToDiff)
                        {
                            //var DealDeepComparer = new DealDeepComp(); //instantiate
                            foreach (var incomingDeal in dealsToDiff)
                            {
                                try
                                {
                                    Deal existingDeal = dbDealsAtInvoiceStatusForThisAccountingPeriodOrLater.First(s => s.StockNumber == incomingDeal.StockNumber);
                                    List<Diff> thisDealDiffs = DealDeepComp.GetDiffs(existingDeal, incomingDeal, "New", incomingDeal.StockNumber.ToString());
                                    if (thisDealDiffs.Count > 0)
                                    {
                                        //they are not the same so..
                                        diffs.AddRange(thisDealDiffs); //find the diffs and record

                                        //update the oldReg to pickup the new value for every property
                                        DealDeepComp.updateExistingDeal(incomingDeal, existingDeal);
                                        changed.Add(existingDeal);
                                        changedCount++;
                                    }

                                }

                                catch (Exception err)
                                {
                                    logMessage.FailNotes = logMessage.FailNotes + $" failed on making change to item {incomingDeal.StockNumber}" + err.ToString();
                                    errorCount++;
                                }
                            }
                        }

                        //find changed non-late costs
                        var sameNonLateItems = incomingDeals.Where(d => !d.IsLateCost).Except(newNonLateCostItems, new DealStockNoComp()).ToList(); //quick piece to ensure we don't bother trying to diff deals we already know are new
                        diffDeals(sameNonLateItems);

                        var sameLateItems = incomingDeals.Where(d => d.IsLateCost).Except(newLateCostItems, new DealStockNoComp()).ToList(); //quick piece to ensure we don't bother trying to diff deals we already know are new
                        diffDeals(sameLateItems);


                        //add diffs to db
                        try
                        {
                            foreach (Diff diff in diffs)
                            {
                                diff.UpdateDate = fileDate;
                            }
                            db.Diffs.AddRange(diffs);  //add them all in one go
                        }
                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $"Failed adding diffs to DB {err.ToString()}";
                            errorCount++;
                        }


                        logMessage.FinishDate = DateTime.UtcNow;
                        logMessage.ProcessedCount = incomingDeals.Count;
                        logMessage.AddedCount = newCount;
                        logMessage.RemovedCount = removedCount;
                        logMessage.ChangedCount = changedCount;
                        logMessage.IsCompleted = true;
                        logMessage.ErrorCount = errorCount;
                        logMessage.StartCount = openingDbCount;

                        int closingDbCount = 0;


                        //move file
                        try
                        {
                            File.Move(newFilepath, newFilepath.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
                            if (errorCount > 0)
                            {
                                //we have errors so use the reporter
                                logMessage.FailNotes = $"{errorCount} errors \r\r\n\r\r\n ------------------------ Errors ----------------------------- \r\r\n{logMessage.FailNotes}";
                                await CentralLoggingService.ReportError("NewDeals", logMessage, true);
                            }
                            //completed, do final things
                            DateTime finishedUpdateDb = DateTime.UtcNow;
                            try
                            {
                                GlobalParam lastUpdate = db.GlobalParams.First(x => x.Description == "newDealLastUpdate");
                                lastUpdate.TextValue = DateTime.UtcNow.ToString();
                                lastUpdate.DateFrom = DateTime.UtcNow;

                                GlobalParam latestAccountingDate = db.GlobalParams.First(x => x.Description == "Latest Accounting Date");
                                latestAccountingDate.TextValue = incomingDealsDate.Year.ToString() + incomingDealsDate.Month.ToString();
                                latestAccountingDate.DateFrom = incomingDealsDate.Date;
                                latestAccountingDate.DateTo = incomingDealsDate.Date;


                                db.SaveChanges();
                                finishedUpdateDb = DateTime.UtcNow;
                                closingDbCount = db.Deals.Where(d => !d.IsRemoved).Count();
                                logMessage.FinishCount = closingDbCount;

                            }
                            catch (Exception err)
                            {
                                logMessage.FailNotes = logMessage.FailNotes + "Failed to save to DB" + err.ToString();
                                logMessage.ErrorCount++;
                            }


                            logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
                            logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
                            db.LogMessages.Add(logMessage);
                            db.SaveChanges();

                            Logger.Info($"Leaving via door2 ");


                            //Logger.Info($"[{DateTime.UtcNow}]  | Result: Started with {openingDbCount} item(s), interpreted {incomingDeals.Count} item(s),  found {newCount} new, {removedCount} removed and {changedCount} changed.  Closed with {closingDbCount } item(s)");
                        }

                        catch (Exception err)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
                            logMessage.ErrorCount++;
                            await CentralLoggingService.ReportError("NewDeals", logMessage);
                            Logger.Error($"Leaving via door3 ");
                        }

                        stopwatch.Stop();
                        return;
                    }


                    catch (Exception err)
                    {
                        stopwatch.Stop();
                        errorMessage = err.ToString();
                        //Locks.Deals = false;
                        logMessage.FailNotes += $"General failure " + err.ToString();
                        logMessage.ErrorCount++;
                        logMessage.FailNotes = $"{logMessage.ErrorCount} error(s) " + logMessage.FailNotes;
                        await CentralLoggingService.ReportError("NewDeals", logMessage);
                        Logger.Error($"Leaving via door4 ");
                        return;
                    }

                    finally
                    {
                        db.ChangeTracker.Clear();

                        //LocksService.NewDeals = false;

                        Monitor.PostLogs.Model.MonitorMessage monitorMessage = new Monitor.PostLogs.Model.MonitorMessage()
                        {
                            Application = "Spark", // This value will not be used, instead the Id from config file will be posted. 
                            Project = "Loader",
                            Customer = "RRG",
                            Environment = ConfigService.isDev == true ? "Dev" : "Prod",
                            Task = this.GetType().Name,
                            StartDate = DateTime.UtcNow.AddMilliseconds(-stopwatch.ElapsedMilliseconds),
                            EndDate = DateTime.UtcNow,
                            Status = errorMessage.Length > 0 ? "Fail" : "Pass",
                            Notes = errorMessage,
                            HTML = string.Empty
                        };
                        await Monitor.PostLogs.Monitor.AddMonitorMessage(monitorMessage);
                    }

                    //return; // should not get here

                }


            }

        }


        private static Dictionary<string, int> createHeaderDictionary(string[] headers)
        {
            return new Dictionary<string, int>()
                    {
                      {"SUFFIX",Array.IndexOf(headers,"SUFFIX")},
                        {"CUSTOMER",Array.IndexOf(headers,"CUSTOMER")},
                        {"SALES EXEC",Array.IndexOf(headers,"SALES EXEC")},
                        {"REG NO",Array.IndexOf(headers,"REG NO")},
                        {"FR",Array.IndexOf(headers,"FR")},
                        {"ENQUIRY",Array.IndexOf(headers,"ENQUIRY")},
                        {"FIN",Array.IndexOf(headers,"FIN")},
                        {"NL TOTAL",Array.IndexOf(headers,"NL TOTAL")},
                        {"VM PROFIT",Array.IndexOf(headers,"VM PROFIT")},
                        {"BRAN",Array.IndexOf(headers,"BRAN")},
                        {"SALE",Array.IndexOf(headers,"SALE")},
                        {"DISCOUNT",Array.IndexOf(headers,"DISCOUNT")},
                        {"O/ALLOW",Array.IndexOf(headers,"O/ALLOW")},
                        {"COS",Array.IndexOf(headers,"COS")},
                        {"FIN SUB",Array.IndexOf(headers,"FIN SUB")},
                        {"FAC BON",Array.IndexOf(headers,"FAC BON")},
                        {"REG BON",Array.IndexOf(headers,"REG BON")},
                        {"BROKER",Array.IndexOf(headers,"BROKER")},
                        {"ACC SALE",Array.IndexOf(headers,"ACC SALE")},
                        {"ACC COS",Array.IndexOf(headers,"ACC COS")},
                        {"FUEL SALE",Array.IndexOf(headers,"FUEL SALE")},
                        {"FUEL COS",Array.IndexOf(headers,"FUEL COS")},
                        {"WARR SALE",Array.IndexOf(headers,"WARR SALE")},
                        {"WARR COS",Array.IndexOf(headers,"WARR COS")},
                        {"COSM COMM",Array.IndexOf(headers,"COSM COMM")},
                        {"COSM SALE",Array.IndexOf(headers,"COSM SALE")},
                        {"COSM COS",Array.IndexOf(headers,"COSM COS")},
                        {"GAP COMM",Array.IndexOf(headers,"GAP COMM")},
                        {"GAP SALE",Array.IndexOf(headers,"GAP SALE")},
                        {"GAP COS",Array.IndexOf(headers,"GAP COS")},
                        {"SP SALE",Array.IndexOf(headers,"SP SALE")},
                        {"SP COS",Array.IndexOf(headers,"SP COS")},
                        {"DEL SALE",Array.IndexOf(headers,"DEL SALE")},
                        {"DEL COS",Array.IndexOf(headers,"DEL COS")},
                        {"RCI COMM",Array.IndexOf(headers,"RCI COMM")},
                        {"FIN COMM",Array.IndexOf(headers,"FIN COMM")},
                        {"SELECT",Array.IndexOf(headers,"SELECT")},
                        {"PRO+",Array.IndexOf(headers,"PRO+")},
                        {"STDS",Array.IndexOf(headers,"STDS")},
                        {"PDI",Array.IndexOf(headers,"PDI")},
                        {"INTRO",Array.IndexOf(headers,"INTRO")},
                        {"ERROR",Array.IndexOf(headers,"ERROR")},
                        {"INV DATE",Array.IndexOf(headers,"INV DATE")},
                        {"LC",Array.IndexOf(headers,"LC")},
                        {"EXEC CODE",Array.IndexOf(headers,"EXEC CODE")},
                        {"FINANCE CO",Array.IndexOf(headers,"FINANCE CO")},
                        {"REG DATE",Array.IndexOf(headers,"REG DATE")},
                        {"SOURCE",Array.IndexOf(headers,"SOURCE")},
                        {"MODEL",Array.IndexOf(headers,"MODEL")},
                        {"VEHICLE DESC",Array.IndexOf(headers,"VEHICLE DESC")},
                        {"DEL DATE",Array.IndexOf(headers,"DEL DATE")},
                        {"AGE",Array.IndexOf(headers,"AGE")},
                        {"ADOPTED",Array.IndexOf(headers,"ADOPTED")},
                        {"VARCLASS",Array.IndexOf(headers,"VARCLASS")},
                        {"CONFIRMED DATE",Array.IndexOf(headers,"CONFIRMED DATE")},
                        {"DELIVERED",Array.IndexOf(headers,"DELIVERED")},
                        {"HAND DATE",Array.IndexOf(headers,"HAND DATE")},
                        {"HAND TIME",Array.IndexOf(headers,"HAND TIME")},
                        {"MODEL YEAR",Array.IndexOf(headers,"MODEL YEAR")},
                        {"SORDER",Array.IndexOf(headers,"SORDER")},
                        {"PAINT PROTECTION COST",Array.IndexOf(headers,"PAINT PROTECTION COST")},
                        {"PAINT PROTECTION SALE",Array.IndexOf(headers,"PAINT PROTECTION SALE")}
                    };
        }

    }
}
