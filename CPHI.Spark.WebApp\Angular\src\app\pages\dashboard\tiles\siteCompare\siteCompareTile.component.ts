import { Component, Input, OnInit } from "@angular/core";
import { SiteVM } from "src/app/model/main.model";
import { Deal } from "src/app/model/sales.model";
import { AutotraderService } from "src/app/services/autotrader.service";
import { ConstantsService } from "src/app/services/constants.service";
import { GetDataMethodsService } from "src/app/services/getDataMethods.service";
import { SelectionsService } from "src/app/services/selections.service";
import { DashboardDataItem, DashboardDataPack, DashboardDataParams, DashboardTimePeriod, SiteCompareRow } from "../../dashboard.model";
import { DashboardService } from "../../dashboard.service";

interface Measure {
  description: string,
  isMargin: boolean;
  department: string;
  //translation: string;
};

@Component({
  selector: 'siteCompare',
  templateUrl: './siteCompareTile.component.html',
  styles: [
    `
    .innerContentHolder{padding:0.5em;}
    tr.bright td{background:var(--brightColourLightest);}
    #chooseTimePeriod{text-align: right;}
    #chooseMeasure{width:100%;margin-top: 0.5em;}
    #chooseMeasure .btn{width: 32%;            margin: 0.2em 1% 0.2em 0px;}
    table{table-layout:fixed;}
    table td:nth-of-type(2),table th:nth-of-type(2){text-align:left;width: 60%;}
    table td{line-height:1.4em;}
    .measureButton{    height: 3.4em;
    padding:  2px 1em;}

  `
  ]
})



export class SiteCompareComponent implements OnInit {

  @Input() public data: SiteCompareRow[]

  timePeriods: string[]
  chosenTimePeriod: string
  chosenMeasure: Measure
  measures: Measure[]


  constructor(

    public selections: SelectionsService,
    public constants: ConstantsService,
    public analysis: AutotraderService,
    public getDataMethodsService: GetDataMethodsService,
    public service: DashboardService,
  ) {

    this.measures = [
      { description: this.constants.translatedText.Dashboard_SitePerformanceLeague_NewUnits , department: 'New', isMargin: false,  },
      { description: this.constants.translatedText.Dashboard_SitePerformanceLeague_NewMargin , department: 'New', isMargin: true,  },
      { description: this.constants.translatedText.Dashboard_SitePerformanceLeague_FleetUnits  , department: 'Fleet', isMargin: false,  },
      { description: this.constants.translatedText.Dashboard_SitePerformanceLeague_UsedUnits  , department: 'Used', isMargin: false,  },
      { description: this.constants.translatedText.Dashboard_SitePerformanceLeague_UsedMargin  , department: 'Used', isMargin: true,  },
      { description: this.constants.translatedText.Dashboard_SitePerformanceLeague_ServiceSales  , department: 'Service', isMargin: true,  },
      { description: this.constants.translatedText.Dashboard_SitePerformanceLeague_PartsSales  , department: 'Parts', isMargin: true,  },
    ]
    this.chosenMeasure = this.measures[0];

    this.timePeriods = ['Month','Week','Yesterday'];//'Yesterday','Today','Month']
    if(this.constants.environment.sitesLeague.includeToday){this.timePeriods.push('Today')}
    
    this.constants.environment.dashboard.showActivityOverdues
    this.chosenTimePeriod = 'Month'

  }

  params: Date;

  ngOnInit(): void {
    if(this.constants.environment.customer!=='RRGUK'){
      this.service.hideReportButtons = true;
    }
  }

  rowBrightClass(site: SiteVM) {
    if (site.SiteId ===  this.selections.user.SiteId) return 'bright'
  }

  getData() {
    let parms: DashboardDataParams = {
      SiteIds: this.service.chosenSites.map(x => x.SiteId).join(','),
      DataItems: [DashboardDataItem.SiteCompare],
      Department: this.chosenMeasure.department,
      IsMargin: this.chosenMeasure.isMargin,
      TimePeriod: DashboardTimePeriod[this.chosenTimePeriod] 
    }

    this.getDataMethodsService.getDashboardData(parms).subscribe((res: DashboardDataPack) => {
      this.data = res.SiteCompareRows;
    })
  }




  chooseMeasure(measure: Measure) {
    this.chosenMeasure = measure;
    this.getData()
  }


  chooseTimePeriod(tp: string){
    this.chosenTimePeriod = tp;
    this.getData();
  }

  trackByFunction(index:number,deal:Deal){    return index;  }



}


