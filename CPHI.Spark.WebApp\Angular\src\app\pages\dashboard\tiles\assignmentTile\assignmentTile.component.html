<div class="dashboard-tile-inner">
  <div class="tileHeader">
    <div class="headerWords">
      <h4>{{ title }}</h4>
      <div *ngIf="dataSource" class="right-aligned">
        <div *ngIf="showWarning" class="warning"></div>
        <div class="chip" [ngClass]="dataSource">{{ dataSource }}</div>
      </div>
    </div>
  </div>

  <div class="numberHolder">
    <table>
      <tbody>
        
        <tr class="numberRow">
          <td>
            <h1 class="spaceBetween column value"><strong>{{ data[0].Units | cph:'number':0 }}</strong></h1>
            <h4 class="spaceBetween column currency">{{ data[0].Value | cph: 'currency':0 }}</h4>
          </td>
          <td>
            <h1 class="spaceBetween column value"><strong>{{ data[1].Units | cph:'number':0 }}</strong></h1>
            <h4 class="spaceBetween column currency">{{ data[1].Value | cph: 'currency':0 }}</h4>
          </td>
        </tr>

        <tr class="labelRow">
          <td>
            <h4 class="spaceBetween column label">{{ constants.translatedText.Used }}</h4>
          </td>
          <td>
            <h4 class="spaceBetween column label">{{ constants.translatedText.New }}</h4>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- <div id="usedHolder">
    <div id="chartHolder">
      <div id="actualUnits" (click)="navigateToOrderBook()">{{data.Units|cph:'number':0}}</div>
    </div>

    <div class="lastYearUnits">
      {{ data.Value|cph:'currency':0 }}  
    </div>

    <div class="lastYearUnits">
      {{constants.translatedText.Used}}
    </div>
  </div>

  <div id="newHolder">
    <div id="chartHolder">
      <div id="actualUnits" (click)="navigateToOrderBook()">{{data.Units|cph:'number':0}}</div>
    </div>

    <div class="lastYearUnits">
      {{ data.Value|cph:'currency':0 }}  
    </div>

    <div class="lastYearUnits">
      {{constants.translatedText.New}}
    </div>
  </div> -->

</div>