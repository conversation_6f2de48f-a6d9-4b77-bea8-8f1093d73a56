import { Component, Input, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CellClassParams, ColDef, DomLayoutType, GridOptions } from 'ag-grid-community';
import { StockReportModalComponent } from 'src/app/components/stockReportModal/stockReportModal.component';
import { StockModalRowWithNext30 } from 'src/app/model/sales.model';
import { GetDataMethodsService } from 'src/app/services/getDataMethods.service';
import { localeEs } from 'src/environments/locale.es.js';
import { CphPipe } from '../../../cph.pipe';
import { AGGridMethodsService } from '../../../services/agGridMethods.service';
import { ConstantsService } from '../../../services/constants.service';
import { ExcelExportService } from '../../../services/excelExportService';
import { SelectionsService } from '../../../services/selections.service';
import { CustomHeaderComponent } from '../../../_cellRenderers/customHeader.component';
import { HorizontalBarComponent } from '../../../_cellRenderers/horizontalBar.component';
import { StockReportService } from '../stockReport.service';
import { ColumnTypesService } from 'src/app/services/columnTypes.service';





@Component({
  selector: 'usedStockTable',
  template: `
    <div id="gridHolder">
    <div  id="excelExport" (click)="excelExport()">
      <img [src]="constants.provideExcelLogo()">
    </div>
    
    <!-- [domLayout]="domLayout" -->
    <ag-grid-angular 
    id="StockReportTable"
     class="ag-theme-balham" 
    [gridOptions]="mainTableGridOptions"
    
      
      (gridReady)="onGridReady($event)"
     
      > 
      
    </ag-grid-angular>
    </div>
    <div *ngIf="!isRegionalTable" class="tableSpacer"></div>
    `
  ,
  styleUrls: ['./../../../../styles/components/_agGrid.scss'],
  styles: [
    `
    #gridHolder{position:relative;}



  `
  ]
})



export class UsedStockTableComponent implements OnInit {

  @Input() public isRegionalTable: boolean;

  @Input() public showUsedCols: boolean;

  showGrid = false;
  public gridApi;
  public gridColumnApi;


  mainTableGridOptions: GridOptions

  gridApiColumnDefinitions: any;

  flipCols: Array<string>;

  domLayout: DomLayoutType;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public columnTypeService: ColumnTypesService,
    public cphPipe: CphPipe,
    public excel: ExcelExportService,
    public modalService: NgbModal,
    public agGridMethods: AGGridMethodsService,
    public getData: GetDataMethodsService,
    public service: StockReportService,

  ) {






  }

  ngOnDestroy() { }


  ngOnInit() {

    // Set table params
    this.selections.triggerSpinner.next({show:true});

    this.initParams();

    //this.setRowData();

    this.service.filterUpdated.subscribe(value => {

      //this.initParams();
      // this.initParams();
      this.setRowData();
      
    });

  }


  initParams() {

    this.domLayout= "autoHeight";

    this.flipCols = ['ExDemoOverage', 'ExManagementOverage', 'TacticalOverage', 'CoreUsedOverage']

    this.mainTableGridOptions = {
      getMainMenuItems:(params)=>this.agGridMethods.getColMenuWithTopBottomHighlight(params,this.service.topBottomHighlightsUsed),
      getContextMenuItems: (params) => this.agGridMethods.getContextMenuItems(params),
      domLayout: this.domLayout,
      getLocaleText: (params: any) =>  this.constants.currentLang == 'es' ? localeEs[params.key] || params.defaultValue : params.defaultValue,
      suppressPropertyNamesCheck: true,
      context: { thisComponent: this },
      
      onCellMouseOver: (params) => {
        //this.onCellMouseOver(params);
      },
      onCellDoubleClicked: (params) => {
        this.onCellDblClick(params);
      },
      getRowHeight: (params) => {
        return params.node.rowPinned == "bottom" ? 40 : 25
      },
      onFirstDataRendered: () => { this.showGrid = true; this.selections.triggerSpinner.next({ show: false }); },

      rowData: this.provideRowData(),
      pinnedBottomRowData: this.service.stockRowData ? this.service.stockRowData.filter(x=>x.IsTotal) : null,
      
      animateRows: true,
      defaultColDef: {
        resizable: true,
        sortable: true,
        hide: false,
        filterParams: { applyButton: false, clearButton: true, cellHeight: this.agGridMethods.getFilterListItemHeight() }, autoHeight: true,
       // suppressColumnMoveAnimation: true,
      },
      columnTypes: {
        ...this.columnTypeService.provideColTypes(this.service.topBottomHighlightsUsed),
      },
      getRowClass: (params) => {
        if (params.data.Description == 'Total Sites') {
          return 'total';
        }
      },

      columnDefs: this.provideColDefs()
    }
  }

  // cellClassProvider(params: CellClassParams){
  //   if (this.isRegionalTable) {
  //     return params?.value < 0 ? 'badFont ag-right-aligned-cell' : 'ag-right-aligned-cell';
  //   } else {
  //     return this.agGridMethods.cellClassProviderWithColourFontNew(params,this.service.topBottomHighlights);
  //   }
  // }

  setRowData(): void
  {
    if(this.gridApi && this.service.stockRowData)
    {
      this.gridApi.setRowData(this.provideRowData())
      
      this.gridApi.setPinnedBottomRowData(this.service.stockRowData.filter(x=>x.IsTotal));
    }
  }


  private provideRowData(){

    if(this.service.stockRowData){
      return this.isRegionalTable ? this.service.stockRowData.filter(x=>x.IsRegion) : this.service.stockRowData.filter(x=>x.IsSite);
    }
    
    return;
  }
  
  private provideColDefs() {

    let colDefs:any[] = [
      //site name
      { headerName: this.constants.translatedText.Site, field: 'Label', colId: 'Site', width: 180, type: 'label' },


      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[0].name, field: 'Stock.Ages.CoreUsed_' + this.constants.stockMonths[0].name + 'Count', colId: 'CoreUsed_' + this.constants.stockMonths[0].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[1].name, field: 'Stock.Ages.CoreUsed_' + this.constants.stockMonths[1].name + 'Count', colId: 'CoreUsed_' + this.constants.stockMonths[1].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[2].name, field: 'Stock.Ages.CoreUsed_' + this.constants.stockMonths[2].name + 'Count', colId: 'CoreUsed_' + this.constants.stockMonths[2].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[3].name, field: 'Stock.Ages.CoreUsed_' + this.constants.stockMonths[3].name + 'Count', colId: 'CoreUsed_' + this.constants.stockMonths[3].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[4].name, field: 'Stock.Ages.CoreUsed_' + this.constants.stockMonths[4].name + 'Count', colId: 'CoreUsed_' + this.constants.stockMonths[4].name, width: 60, type: 'number', hide: true },
      { headerName: this.constants.translatedText.CoreUsed, field: 'CoreUsed', colId: 'CoreUsed', width: 110, type: 'number' },

      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[0].name, field: 'Stock.Ages.Tactical_' + this.constants.stockMonths[0].name + 'Count', colId: 'Tactical_' + this.constants.stockMonths[0].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[1].name, field: 'Stock.Ages.Tactical_' + this.constants.stockMonths[1].name + 'Count', colId: 'Tactical_' + this.constants.stockMonths[1].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[2].name, field: 'Stock.Ages.Tactical_' + this.constants.stockMonths[2].name + 'Count', colId: 'Tactical_' + this.constants.stockMonths[2].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[3].name, field: 'Stock.Ages.Tactical_' + this.constants.stockMonths[3].name + 'Count', colId: 'Tactical_' + this.constants.stockMonths[3].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[4].name, field: 'Stock.Ages.Tactical_' + this.constants.stockMonths[4].name + 'Count', colId: 'Tactical_' + this.constants.stockMonths[4].name, width: 60, type: 'number', hide: true },
      { headerName: 'Tactical', field: 'Tactical', colId: 'Tactical', width: 110, type: 'number', hide: !this.constants.environment.usedStockTable_tactical },

      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[0].name, field: 'Stock.Ages.ExManagement_' + this.constants.stockMonths[0].name + 'Count', colId: 'ExManagement_' + this.constants.stockMonths[0].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[1].name, field: 'Stock.Ages.ExManagement_' + this.constants.stockMonths[1].name + 'Count', colId: 'ExManagement_' + this.constants.stockMonths[1].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[2].name, field: 'Stock.Ages.ExManagement_' + this.constants.stockMonths[2].name + 'Count', colId: 'ExManagement_' + this.constants.stockMonths[2].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[3].name, field: 'Stock.Ages.ExManagement_' + this.constants.stockMonths[3].name + 'Count', colId: 'ExManagement_' + this.constants.stockMonths[3].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[4].name, field: 'Stock.Ages.ExManagement_' + this.constants.stockMonths[4].name + 'Count', colId: 'ExManagement_' + this.constants.stockMonths[4].name, width: 60, type: 'number', hide: true },
      { headerName: 'Ex-Management', field: 'ExManagement', colId: 'ExManagement', width: 110, type: 'number', hide: !this.constants.environment.usedStockTable_exManagementCount },

      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[0].name, field: 'Stock.Ages.ExDemo_' + this.constants.stockMonths[0].name + 'Count', colId: 'ExDemo_' + this.constants.stockMonths[0].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[1].name, field: 'Stock.Ages.ExDemo_' + this.constants.stockMonths[1].name + 'Count', colId: 'ExDemo_' + this.constants.stockMonths[1].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[2].name, field: 'Stock.Ages.ExDemo_' + this.constants.stockMonths[2].name + 'Count', colId: 'ExDemo_' + this.constants.stockMonths[2].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[3].name, field: 'Stock.Ages.ExDemo_' + this.constants.stockMonths[3].name + 'Count', colId: 'ExDemo_' + this.constants.stockMonths[3].name, width: 60, type: 'number', hide: true },
      // { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[4].name, field: 'Stock.Ages.ExDemo_' + this.constants.stockMonths[4].name + 'Count', colId: 'ExDemo_' + this.constants.stockMonths[4].name, width: 60, type: 'number', hide: true },
      { headerName: 'Ex-Demo', field: 'ExDemo', colId: 'ExDemo', width: 110, type: 'number', hide: !this.constants.environment.usedStockTable_exDemo },
    ]

    if(this.constants.environment.usedStockTable_vindisFormatting){
      colDefs = colDefs.concat([
        { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[0].name, field: 'Stock.Ages.Demo_' + this.constants.stockMonths[0].name + 'Count', colId: 'Demo_' + this.constants.stockMonths[0].name, width: 60, type: 'number', hide: true },
        { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[1].name, field: 'Stock.Ages.Demo_' + this.constants.stockMonths[1].name + 'Count', colId: 'Demo_' + this.constants.stockMonths[1].name, width: 60, type: 'number', hide: true },
        { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[2].name, field: 'Stock.Ages.Demo_' + this.constants.stockMonths[2].name + 'Count', colId: 'Demo_' + this.constants.stockMonths[2].name, width: 60, type: 'number', hide: true },
        { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[3].name, field: 'Stock.Ages.Demo_' + this.constants.stockMonths[3].name + 'Count', colId: 'Demo_' + this.constants.stockMonths[3].name, width: 60, type: 'number', hide: true },
        { cellStyle: { 'background-color': '#ECF0F1' }, headerName: this.constants.stockMonths[4].name, field: 'Stock.Ages.Demo_' + this.constants.stockMonths[4].name + 'Count', colId: 'Demo_' + this.constants.stockMonths[4].name, width: 60, type: 'number', hide: true },
        { headerName: 'Demo', field: 'Demo', colId: 'Demo', width: 110, type: 'number' },
      ])
    }

    colDefs = colDefs.concat([
      { headerName: this.constants.translatedText.TotalUsed, field: 'TotalUsed', colId: 'Total Used', width: 80, type: 'number', cellClass: 'total ag-right-aligned-cell' },

      //bars
      { headerName: '% by type', suppressSizeToFit: true, colId: 'SplitByType', width: 480, cellRenderer: HorizontalBarComponent, cellClass: 'total ag-right-aligned-cell' },

      { headerName: this.constants.translatedText.Trade, field: 'Trade', colId: 'Trade', width: 80, type: 'number' },
      { headerName: 'Total Used Inc. Trade', field: 'TotalUsedIncTrade', colId: 'Total Used Inc Trade', width: 100, type: 'number', cellClass: 'total ag-right-aligned-cell' },
    ])


    return [{ headerName: "", children: colDefs }];
  }


  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.mainTableGridOptions.context = { thisComponent: this };  // to be able to then reference this things within custom menu methods
    setTimeout(() => {
      this.gridApi.sizeColumnsToFit();
    }, 10)
    this.selections.triggerSpinner.next({ show: false });

  }



  highlightTopBottom(colDef: any, topBottomN: number) {
    this.agGridMethods.highlightTopBottom(colDef, topBottomN, this.flipCols, this.gridApi)
  }



  onCellDblClick(params: any, skipProcessingGoingOver?: boolean) {

    if(params.colDef.colId == "Site" || params.colDef.colId == "SplitByType"){ return; }
    
    let siteIds: number[] = [];

    if(params.data.IsRegion || params.data.IsTotal)
    {
      siteIds = this.constants.getSiteIdsForRegion(params.data.Label);
    }
    else 
    {
      siteIds.push(params.data.SiteId);
    }

    if (params.colDef.colId == 'SplitByType'){ return; }

    let chosenReport = params.colDef.colId.replace(/\s/g, '');;

    if (this.constants.environment.usedStockTable_includeDemoWithUsed) {
      if (chosenReport == 'TotalUsed') chosenReport = chosenReport + 'IncDemo';
      if (chosenReport == 'TotalUsedIncTrade') chosenReport = chosenReport + 'AndDemo';
    }

    this.getData.getStockModalRows(chosenReport, 'now', siteIds.toString(), 0, false, this.selections.stockReport.franchises.toString()).subscribe((res:StockModalRowWithNext30) => {

      setTimeout(() => {
        this.selections.triggerSpinner.next({show:false});
      }, 20)

      
      if (params.colDef.colId.indexOf('_') > -1) { skipProcessingGoingOver = true }
  
      let dataPath: string = params.colDef.field.replace('count', 'vehicles').replace('Count', '');
  
  
      let header = params.data.Label + ': ' + res.AgedNow.length + ' ' + params.colDef.headerName + ' vehicles';
      if (this.selections.stockReport.report.name == 'Stock Ageing') { header = header + ' over ' + this.selections.stockReport.ageingOption.description + ' as at ' + this.selections.stockReport.asAt.description }
  
      let showUsedColumns = ['CoreUsed', 'Tactical', 'ExManagement', 'ExDemo'].includes(params.colDef.headerName);
  
      this.selections.initiateStockReportModal(skipProcessingGoingOver, res.AgedNow, res.AgedIn30, header, showUsedColumns, params.data.Label);
      //open modal
  
      const modalRef = this.modalService.open(StockReportModalComponent, { keyboard: true, size: 'lg' });
      //I give to modal
      //modalRef.componentInstance.givenStockItem = this.stockItem;
  
      modalRef.result.then((result) => { //I get back from modal
        if (result) {
  
        }
      });

      

    }, error => {

      console.error("ERROR: ", error);

    }, () => {

     

    });



  }


  showAgeingColumns(params) {
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[0].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[1].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[2].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[3].name, true)
    this.gridColumnApi.setColumnVisible(params.column.colId + '_' + this.constants.stockMonths[4].name, true)
    this.resizeGrid();
  }


  resizeGrid() {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  clearHighlighting(colDef: ColDef) {
    this.agGridMethods.clearHighlighting(colDef, this.gridApi);

  }



  excelExport() {
    //get tableModel from ag-grid
    let tableModel = this.gridApi.getModel()
    this.excel.createSheetObject(tableModel, 'Used Stock');
  }






}
