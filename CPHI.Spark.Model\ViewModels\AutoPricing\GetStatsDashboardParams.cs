﻿using System.Collections.Generic;

namespace CPHI.Spark.Model.ViewModels.AutoPricing
{
   public class GetStatsDashboardParams
   {
      public List<int> RetailerSiteIds { get; set; }

      public bool UseTestStrategy { get; set; }

      public bool IncludeNewVehicles { get; set; }

      public bool IncludeUnPublishedAdverts { get; set; }

      public List<string> LifecycleStatuses { get; set; }

      public bool IncludeLCV { get; set; }
   }
}