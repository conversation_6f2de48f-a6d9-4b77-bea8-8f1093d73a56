(self.webpackChunkstockpulseweb=self.webpackChunkstockpulseweb||[]).push([[210],{4270:function(d){(function(){var b,n,h,v,c,g;typeof performance<"u"&&null!==performance&&performance.now?d.exports=function(){return performance.now()}:typeof process<"u"&&null!==process&&process.hrtime?(d.exports=function(){return(b()-c)/1e6},n=process.hrtime,v=(b=function(){var y;return 1e9*(y=n())[0]+y[1]})(),g=1e9*process.uptime(),c=v-g):Date.now?(d.exports=function(){return Date.now()-h},h=Date.now()):(d.exports=function(){return(new Date).getTime()-h},h=(new Date).getTime())}).call(this)},6489:(d,b,n)=>{for(var h=n(4270),v=typeof window>"u"?global:window,c=["moz","webkit"],g="AnimationFrame",y=v["request"+g],O=v["cancel"+g]||v["cancelRequest"+g],x=0;!y&&x<c.length;x++)y=v[c[x]+"Request"+g],O=v[c[x]+"Cancel"+g]||v[c[x]+"CancelRequest"+g];if(!y||!O){var S=0,C=0,A=[];y=function(P){if(0===A.length){var w=h(),M=Math.max(0,16.666666666666668-(w-S));S=M+w,setTimeout(function(){var I=A.slice(0);A.length=0;for(var B=0;B<I.length;B++)if(!I[B].cancelled)try{I[B].callback(S)}catch(H){setTimeout(function(){throw H},0)}},Math.round(M))}return A.push({handle:++C,callback:P,cancelled:!1}),C},O=function(P){for(var w=0;w<A.length;w++)A[w].handle===P&&(A[w].cancelled=!0)}}d.exports=function(P){return y.call(v,P)},d.exports.cancel=function(){O.apply(v,arguments)},d.exports.polyfill=function(P){P||(P=v),P.requestAnimationFrame=y,P.cancelAnimationFrame=O}},1239:d=>{d.exports=function(b){this.ok=!1,this.alpha=1,"#"==b.charAt(0)&&(b=b.substr(1,6)),b=(b=b.replace(/ /g,"")).toLowerCase();var n={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};b=n[b]||b;for(var h=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(x){return[parseInt(x[1]),parseInt(x[2]),parseInt(x[3]),parseFloat(x[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(x){return[parseInt(x[1]),parseInt(x[2]),parseInt(x[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(x){return[parseInt(x[1],16),parseInt(x[2],16),parseInt(x[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(x){return[parseInt(x[1]+x[1],16),parseInt(x[2]+x[2],16),parseInt(x[3]+x[3],16)]}}],v=0;v<h.length;v++){var g=h[v].process,y=h[v].re.exec(b);if(y){var O=g(y);this.r=O[0],this.g=O[1],this.b=O[2],O.length>3&&(this.alpha=O[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var x=this.r.toString(16),S=this.g.toString(16),C=this.b.toString(16);return 1==x.length&&(x="0"+x),1==S.length&&(S="0"+S),1==C.length&&(C="0"+C),"#"+x+S+C},this.getHelpXML=function(){for(var x=new Array,S=0;S<h.length;S++)for(var C=h[S].example,A=0;A<C.length;A++)x[x.length]=C[A];for(var R in n)x[x.length]=R;var P=document.createElement("ul");for(P.setAttribute("id","rgbcolor-examples"),S=0;S<x.length;S++)try{var w=document.createElement("li"),M=new RGBColor(x[S]),I=document.createElement("div");I.style.cssText="margin: 3px; border: 1px solid black; background:"+M.toHex()+"; color:"+M.toHex(),I.appendChild(document.createTextNode("test"));var B=document.createTextNode(" "+x[S]+" -> "+M.toRGB()+" -> "+M.toHex());w.appendChild(I),w.appendChild(B),P.appendChild(w)}catch{}return P}}},509:(d,b,n)=>{"use strict";var h=n(3029),v=n(3691),c=TypeError;d.exports=function(g){if(h(g))return g;throw new c(v(g)+" is not a function")}},2655:(d,b,n)=>{"use strict";var h=n(9429),v=n(3691),c=TypeError;d.exports=function(g){if(h(g))return g;throw new c(v(g)+" is not a constructor")}},3550:(d,b,n)=>{"use strict";var h=n(598),v=String,c=TypeError;d.exports=function(g){if(h(g))return g;throw new c("Can't set "+v(g)+" as a prototype")}},7370:(d,b,n)=>{"use strict";var h=n(4201),v=n(5391),c=n(2560).f,g=h("unscopables"),y=Array.prototype;void 0===y[g]&&c(y,g,{configurable:!0,value:v(null)}),d.exports=function(O){y[g][O]=!0}},1514:(d,b,n)=>{"use strict";var h=n(730).charAt;d.exports=function(v,c,g){return c+(g?h(v,c).length:1)}},767:(d,b,n)=>{"use strict";var h=n(3622),v=TypeError;d.exports=function(c,g){if(h(g,c))return c;throw new v("Incorrect invocation")}},5027:(d,b,n)=>{"use strict";var h=n(8999),v=String,c=TypeError;d.exports=function(g){if(h(g))return g;throw new c(v(g)+" is not an object")}},4328:(d,b,n)=>{"use strict";var h=n(5290),v=n(7578),c=n(6310),g=function(y){return function(O,x,S){var C=h(O),A=c(C);if(0===A)return!y&&-1;var P,R=v(S,A);if(y&&x!=x){for(;A>R;)if((P=C[R++])!=P)return!0}else for(;A>R;R++)if((y||R in C)&&C[R]===x)return y||R||0;return!y&&-1}};d.exports={includes:g(!0),indexOf:g(!1)}},6834:(d,b,n)=>{"use strict";var h=n(3689);d.exports=function(v,c){var g=[][v];return!!g&&h(function(){g.call(null,c||function(){return 1},1)})}},8820:(d,b,n)=>{"use strict";var h=n(509),v=n(690),c=n(4413),g=n(6310),y=TypeError,O="Reduce of empty array with no initial value",x=function(S){return function(C,A,R,P){var w=v(C),M=c(w),I=g(w);if(h(A),0===I&&R<2)throw new y(O);var B=S?I-1:0,H=S?-1:1;if(R<2)for(;;){if(B in M){P=M[B],B+=H;break}if(B+=H,S?B<0:I<=B)throw new y(O)}for(;S?B>=0:I>B;B+=H)B in M&&(P=A(P,M[B],B,w));return P}};d.exports={left:x(!1),right:x(!0)}},6004:(d,b,n)=>{"use strict";var h=n(8844);d.exports=h([].slice)},6431:(d,b,n)=>{"use strict";var v=n(4201)("iterator"),c=!1;try{var g=0,y={next:function(){return{done:!!g++}},return:function(){c=!0}};y[v]=function(){return this},Array.from(y,function(){throw 2})}catch{}d.exports=function(O,x){try{if(!x&&!c)return!1}catch{return!1}var S=!1;try{var C={};C[v]=function(){return{next:function(){return{done:S=!0}}}},O(C)}catch{}return S}},6648:(d,b,n)=>{"use strict";var h=n(8844),v=h({}.toString),c=h("".slice);d.exports=function(g){return c(v(g),8,-1)}},926:(d,b,n)=>{"use strict";var h=n(3043),v=n(3029),c=n(6648),y=n(4201)("toStringTag"),O=Object,x="Arguments"===c(function(){return arguments}());d.exports=h?c:function(C){var A,R,P;return void 0===C?"Undefined":null===C?"Null":"string"==typeof(R=function(C,A){try{return C[A]}catch{}}(A=O(C),y))?R:x?c(A):"Object"===(P=c(A))&&v(A.callee)?"Arguments":P}},8758:(d,b,n)=>{"use strict";var h=n(6812),v=n(9152),c=n(2474),g=n(2560);d.exports=function(y,O,x){for(var S=v(O),C=g.f,A=c.f,R=0;R<S.length;R++){var P=S[R];!h(y,P)&&(!x||!h(x,P))&&C(y,P,A(O,P))}}},7413:(d,b,n)=>{"use strict";var v=n(4201)("match");d.exports=function(c){var g=/./;try{"/./"[c](g)}catch{try{return g[v]=!1,"/./"[c](g)}catch{}}return!1}},1748:(d,b,n)=>{"use strict";var h=n(3689);d.exports=!h(function(){function v(){}return v.prototype.constructor=null,Object.getPrototypeOf(new v)!==v.prototype})},7807:d=>{"use strict";d.exports=function(b,n){return{value:b,done:n}}},5773:(d,b,n)=>{"use strict";var h=n(7697),v=n(2560),c=n(5684);d.exports=h?function(g,y,O){return v.f(g,y,c(1,O))}:function(g,y,O){return g[y]=O,g}},5684:d=>{"use strict";d.exports=function(b,n){return{enumerable:!(1&b),configurable:!(2&b),writable:!(4&b),value:n}}},2148:(d,b,n)=>{"use strict";var h=n(8702),v=n(2560);d.exports=function(c,g,y){return y.get&&h(y.get,g,{getter:!0}),y.set&&h(y.set,g,{setter:!0}),v.f(c,g,y)}},1880:(d,b,n)=>{"use strict";var h=n(3029),v=n(2560),c=n(8702),g=n(5014);d.exports=function(y,O,x,S){S||(S={});var C=S.enumerable,A=void 0!==S.name?S.name:O;if(h(x)&&c(x,A,S),S.global)C?y[O]=x:g(O,x);else{try{S.unsafe?y[O]&&(C=!0):delete y[O]}catch{}C?y[O]=x:v.f(y,O,{value:x,enumerable:!1,configurable:!S.nonConfigurable,writable:!S.nonWritable})}return y}},5014:(d,b,n)=>{"use strict";var h=n(1087),v=Object.defineProperty;d.exports=function(c,g){try{v(h,c,{value:g,configurable:!0,writable:!0})}catch{h[c]=g}return g}},7697:(d,b,n)=>{"use strict";var h=n(3689);d.exports=!h(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},6420:(d,b,n)=>{"use strict";var h=n(1087),v=n(8999),c=h.document,g=v(c)&&v(c.createElement);d.exports=function(y){return g?c.createElement(y):{}}},6338:d=>{"use strict";d.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},3265:(d,b,n)=>{"use strict";var v=n(6420)("span").classList,c=v&&v.constructor&&v.constructor.prototype;d.exports=c===Object.prototype?void 0:c},2739:d=>{"use strict";d.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},7636:(d,b,n)=>{"use strict";var h=n(8017);d.exports=/ipad|iphone|ipod/i.test(h)&&typeof Pebble<"u"},1489:(d,b,n)=>{"use strict";var h=n(8017);d.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(h)},240:(d,b,n)=>{"use strict";var h=n(9791);d.exports="NODE"===h},9976:(d,b,n)=>{"use strict";var h=n(8017);d.exports=/web0s(?!.*chrome)/i.test(h)},8017:(d,b,n)=>{"use strict";var v=n(1087).navigator,c=v&&v.userAgent;d.exports=c?String(c):""},1352:(d,b,n)=>{"use strict";var x,S,h=n(1087),v=n(8017),c=h.process,g=h.Deno,y=c&&c.versions||g&&g.version,O=y&&y.v8;O&&(S=(x=O.split("."))[0]>0&&x[0]<4?1:+(x[0]+x[1])),!S&&v&&(!(x=v.match(/Edge\/(\d+)/))||x[1]>=74)&&(x=v.match(/Chrome\/(\d+)/))&&(S=+x[1]),d.exports=S},9791:(d,b,n)=>{"use strict";var h=n(1087),v=n(8017),c=n(6648),g=function(y){return v.slice(0,y.length)===y};d.exports=g("Bun/")?"BUN":g("Cloudflare-Workers")?"CLOUDFLARE":g("Deno/")?"DENO":g("Node.js/")?"NODE":h.Bun&&"string"==typeof Bun.version?"BUN":h.Deno&&"object"==typeof Deno.version?"DENO":"process"===c(h.process)?"NODE":h.window&&h.document?"BROWSER":"REST"},9989:(d,b,n)=>{"use strict";var h=n(1087),v=n(2474).f,c=n(5773),g=n(1880),y=n(5014),O=n(8758),x=n(5266);d.exports=function(S,C){var M,I,B,H,$,A=S.target,R=S.global,P=S.stat;if(M=R?h:P?h[A]||y(A,{}):h[A]&&h[A].prototype)for(I in C){if(H=C[I],B=S.dontCallGetSet?($=v(M,I))&&$.value:M[I],!x(R?I:A+(P?".":"#")+I,S.forced)&&void 0!==B){if(typeof H==typeof B)continue;O(H,B)}(S.sham||B&&B.sham)&&c(H,"sham",!0),g(M,I,H,S)}}},3689:d=>{"use strict";d.exports=function(b){try{return!!b()}catch{return!0}}},8678:(d,b,n)=>{"use strict";n(4043);var h=n(2615),v=n(1880),c=n(6308),g=n(3689),y=n(4201),O=n(5773),x=y("species"),S=RegExp.prototype;d.exports=function(C,A,R,P){var w=y(C),M=!g(function(){var $={};return $[w]=function(){return 7},7!==""[C]($)}),I=M&&!g(function(){var $=!1,F=/a/;return"split"===C&&((F={}).constructor={},F.constructor[x]=function(){return F},F.flags="",F[w]=/./[w]),F.exec=function(){return $=!0,null},F[w](""),!$});if(!M||!I||R){var B=/./[w],H=A(w,""[C],function($,F,G,Y,k){var nt=F.exec;return nt===c||nt===S.exec?M&&!k?{done:!0,value:h(B,F,G,Y)}:{done:!0,value:h($,G,F,Y)}:{done:!1}});v(String.prototype,C,H[0]),v(S,w,H[1])}P&&O(S[w],"sham",!0)}},1735:(d,b,n)=>{"use strict";var h=n(7215),v=Function.prototype,c=v.apply,g=v.call;d.exports="object"==typeof Reflect&&Reflect.apply||(h?g.bind(c):function(){return g.apply(c,arguments)})},4071:(d,b,n)=>{"use strict";var h=n(6576),v=n(509),c=n(7215),g=h(h.bind);d.exports=function(y,O){return v(y),void 0===O?y:c?g(y,O):function(){return y.apply(O,arguments)}}},7215:(d,b,n)=>{"use strict";var h=n(3689);d.exports=!h(function(){var v=function(){}.bind();return"function"!=typeof v||v.hasOwnProperty("prototype")})},2615:(d,b,n)=>{"use strict";var h=n(7215),v=Function.prototype.call;d.exports=h?v.bind(v):function(){return v.apply(v,arguments)}},1236:(d,b,n)=>{"use strict";var h=n(7697),v=n(6812),c=Function.prototype,g=h&&Object.getOwnPropertyDescriptor,y=v(c,"name"),O=y&&"something"===function(){}.name,x=y&&(!h||h&&g(c,"name").configurable);d.exports={EXISTS:y,PROPER:O,CONFIGURABLE:x}},2743:(d,b,n)=>{"use strict";var h=n(8844),v=n(509);d.exports=function(c,g,y){try{return h(v(Object.getOwnPropertyDescriptor(c,g)[y]))}catch{}}},6576:(d,b,n)=>{"use strict";var h=n(6648),v=n(8844);d.exports=function(c){if("Function"===h(c))return v(c)}},8844:(d,b,n)=>{"use strict";var h=n(7215),v=Function.prototype,c=v.call,g=h&&v.bind.bind(c,c);d.exports=h?g:function(y){return function(){return c.apply(y,arguments)}}},6058:(d,b,n)=>{"use strict";var h=n(1087),v=n(3029),c=function(g){return v(g)?g:void 0};d.exports=function(g,y){return arguments.length<2?c(h[g]):h[g]&&h[g][y]}},1664:(d,b,n)=>{"use strict";var h=n(926),v=n(4849),c=n(981),g=n(9478),O=n(4201)("iterator");d.exports=function(x){if(!c(x))return v(x,O)||v(x,"@@iterator")||g[h(x)]}},5185:(d,b,n)=>{"use strict";var h=n(2615),v=n(509),c=n(5027),g=n(3691),y=n(1664),O=TypeError;d.exports=function(x,S){var C=arguments.length<2?y(x):S;if(v(C))return c(h(C,x));throw new O(g(x)+" is not iterable")}},4849:(d,b,n)=>{"use strict";var h=n(509),v=n(981);d.exports=function(c,g){var y=c[g];return v(y)?void 0:h(y)}},7017:(d,b,n)=>{"use strict";var h=n(8844),v=n(690),c=Math.floor,g=h("".charAt),y=h("".replace),O=h("".slice),x=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,S=/\$([$&'`]|\d{1,2})/g;d.exports=function(C,A,R,P,w,M){var I=R+C.length,B=P.length,H=S;return void 0!==w&&(w=v(w),H=x),y(M,H,function($,F){var G;switch(g(F,0)){case"$":return"$";case"&":return C;case"`":return O(A,0,R);case"'":return O(A,I);case"<":G=w[O(F,1,-1)];break;default:var Y=+F;if(0===Y)return $;if(Y>B){var k=c(Y/10);return 0===k?$:k<=B?void 0===P[k-1]?g(F,1):P[k-1]+g(F,1):$}G=P[Y-1]}return void 0===G?"":G})}},1087:function(d){"use strict";var b=function(n){return n&&n.Math===Math&&n};d.exports=b("object"==typeof globalThis&&globalThis)||b("object"==typeof window&&window)||b("object"==typeof self&&self)||b("object"==typeof global&&global)||b("object"==typeof this&&this)||function(){return this}()||Function("return this")()},6812:(d,b,n)=>{"use strict";var h=n(8844),v=n(690),c=h({}.hasOwnProperty);d.exports=Object.hasOwn||function(y,O){return c(v(y),O)}},7248:d=>{"use strict";d.exports={}},920:d=>{"use strict";d.exports=function(b,n){try{1===arguments.length?console.error(b):console.error(b,n)}catch{}}},1422:(d,b,n)=>{"use strict";var h=n(6058);d.exports=h("document","documentElement")},8506:(d,b,n)=>{"use strict";var h=n(7697),v=n(3689),c=n(6420);d.exports=!h&&!v(function(){return 7!==Object.defineProperty(c("div"),"a",{get:function(){return 7}}).a})},4413:(d,b,n)=>{"use strict";var h=n(8844),v=n(3689),c=n(6648),g=Object,y=h("".split);d.exports=v(function(){return!g("z").propertyIsEnumerable(0)})?function(O){return"String"===c(O)?y(O,""):g(O)}:g},6738:(d,b,n)=>{"use strict";var h=n(8844),v=n(3029),c=n(4091),g=h(Function.toString);v(c.inspectSource)||(c.inspectSource=function(y){return g(y)}),d.exports=c.inspectSource},618:(d,b,n)=>{"use strict";var P,w,M,h=n(9834),v=n(1087),c=n(8999),g=n(5773),y=n(6812),O=n(4091),x=n(2713),S=n(7248),C="Object already initialized",A=v.TypeError;if(h||O.state){var H=O.state||(O.state=new(0,v.WeakMap));H.get=H.get,H.has=H.has,H.set=H.set,P=function(F,G){if(H.has(F))throw new A(C);return G.facade=F,H.set(F,G),G},w=function(F){return H.get(F)||{}},M=function(F){return H.has(F)}}else{var $=x("state");S[$]=!0,P=function(F,G){if(y(F,$))throw new A(C);return G.facade=F,g(F,$,G),G},w=function(F){return y(F,$)?F[$]:{}},M=function(F){return y(F,$)}}d.exports={set:P,get:w,has:M,enforce:function(F){return M(F)?w(F):P(F,{})},getterFor:function(F){return function(G){var Y;if(!c(G)||(Y=w(G)).type!==F)throw new A("Incompatible receiver, "+F+" required");return Y}}}},3292:(d,b,n)=>{"use strict";var h=n(4201),v=n(9478),c=h("iterator"),g=Array.prototype;d.exports=function(y){return void 0!==y&&(v.Array===y||g[c]===y)}},2297:(d,b,n)=>{"use strict";var h=n(6648);d.exports=Array.isArray||function(c){return"Array"===h(c)}},3029:d=>{"use strict";var b="object"==typeof document&&document.all;d.exports=typeof b>"u"&&void 0!==b?function(n){return"function"==typeof n||n===b}:function(n){return"function"==typeof n}},9429:(d,b,n)=>{"use strict";var h=n(8844),v=n(3689),c=n(3029),g=n(926),y=n(6058),O=n(6738),x=function(){},S=y("Reflect","construct"),C=/^\s*(?:class|function)\b/,A=h(C.exec),R=!C.test(x),P=function(I){if(!c(I))return!1;try{return S(x,[],I),!0}catch{return!1}},w=function(I){if(!c(I))return!1;switch(g(I)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return R||!!A(C,O(I))}catch{return!0}};w.sham=!0,d.exports=!S||v(function(){var M;return P(P.call)||!P(Object)||!P(function(){M=!0})||M})?w:P},5266:(d,b,n)=>{"use strict";var h=n(3689),v=n(3029),c=/#|\.prototype\./,g=function(C,A){var R=O[y(C)];return R===S||R!==x&&(v(A)?h(A):!!A)},y=g.normalize=function(C){return String(C).replace(c,".").toLowerCase()},O=g.data={},x=g.NATIVE="N",S=g.POLYFILL="P";d.exports=g},981:d=>{"use strict";d.exports=function(b){return null==b}},8999:(d,b,n)=>{"use strict";var h=n(3029);d.exports=function(v){return"object"==typeof v?null!==v:h(v)}},598:(d,b,n)=>{"use strict";var h=n(8999);d.exports=function(v){return h(v)||null===v}},3931:d=>{"use strict";d.exports=!1},1245:(d,b,n)=>{"use strict";var h=n(8999),v=n(6648),g=n(4201)("match");d.exports=function(y){var O;return h(y)&&(void 0!==(O=y[g])?!!O:"RegExp"===v(y))}},734:(d,b,n)=>{"use strict";var h=n(6058),v=n(3029),c=n(3622),g=n(9525),y=Object;d.exports=g?function(O){return"symbol"==typeof O}:function(O){var x=h("Symbol");return v(x)&&c(x.prototype,y(O))}},8734:(d,b,n)=>{"use strict";var h=n(4071),v=n(2615),c=n(5027),g=n(3691),y=n(3292),O=n(6310),x=n(3622),S=n(5185),C=n(1664),A=n(2125),R=TypeError,P=function(M,I){this.stopped=M,this.result=I},w=P.prototype;d.exports=function(M,I,B){var nt,J,mt,lt,et,ht,ut,$=!(!B||!B.AS_ENTRIES),F=!(!B||!B.IS_RECORD),G=!(!B||!B.IS_ITERATOR),Y=!(!B||!B.INTERRUPTED),k=h(I,B&&B.that),_=function(rt){return nt&&A(nt,"normal",rt),new P(!0,rt)},Q=function(rt){return $?(c(rt),Y?k(rt[0],rt[1],_):k(rt[0],rt[1])):Y?k(rt,_):k(rt)};if(F)nt=M.iterator;else if(G)nt=M;else{if(!(J=C(M)))throw new R(g(M)+" is not iterable");if(y(J)){for(mt=0,lt=O(M);lt>mt;mt++)if((et=Q(M[mt]))&&x(w,et))return et;return new P(!1)}nt=S(M,J)}for(ht=F?M.next:nt.next;!(ut=v(ht,nt)).done;){try{et=Q(ut.value)}catch(rt){A(nt,"throw",rt)}if("object"==typeof et&&et&&x(w,et))return et}return new P(!1)}},2125:(d,b,n)=>{"use strict";var h=n(2615),v=n(5027),c=n(4849);d.exports=function(g,y,O){var x,S;v(g);try{if(!(x=c(g,"return"))){if("throw"===y)throw O;return O}x=h(x,g)}catch(C){S=!0,x=C}if("throw"===y)throw O;if(S)throw x;return v(x),O}},974:(d,b,n)=>{"use strict";var h=n(2013).IteratorPrototype,v=n(5391),c=n(5684),g=n(5997),y=n(9478),O=function(){return this};d.exports=function(x,S,C,A){var R=S+" Iterator";return x.prototype=v(h,{next:c(+!A,C)}),g(x,R,!1,!0),y[R]=O,x}},1934:(d,b,n)=>{"use strict";var h=n(9989),v=n(2615),c=n(3931),g=n(1236),y=n(3029),O=n(974),x=n(1868),S=n(9385),C=n(5997),A=n(5773),R=n(1880),P=n(4201),w=n(9478),M=n(2013),I=g.PROPER,B=g.CONFIGURABLE,H=M.IteratorPrototype,$=M.BUGGY_SAFARI_ITERATORS,F=P("iterator"),G="keys",Y="values",k="entries",nt=function(){return this};d.exports=function(J,mt,lt,et,ht,ut,_){O(lt,mt,et);var bt,Pt,Ft,Q=function(V){if(V===ht&&it)return it;if(!$&&V&&V in at)return at[V];switch(V){case G:case Y:case k:return function(){return new lt(this,V)}}return function(){return new lt(this)}},rt=mt+" Iterator",Bt=!1,at=J.prototype,ct=at[F]||at["@@iterator"]||ht&&at[ht],it=!$&&ct||Q(ht),Ot="Array"===mt&&at.entries||ct;if(Ot&&(bt=x(Ot.call(new J)))!==Object.prototype&&bt.next&&(!c&&x(bt)!==H&&(S?S(bt,H):y(bt[F])||R(bt,F,nt)),C(bt,rt,!0,!0),c&&(w[rt]=nt)),I&&ht===Y&&ct&&ct.name!==Y&&(!c&&B?A(at,"name",Y):(Bt=!0,it=function(){return v(ct,this)})),ht)if(Pt={values:Q(Y),keys:ut?it:Q(G),entries:Q(k)},_)for(Ft in Pt)($||Bt||!(Ft in at))&&R(at,Ft,Pt[Ft]);else h({target:mt,proto:!0,forced:$||Bt},Pt);return(!c||_)&&at[F]!==it&&R(at,F,it,{name:ht}),w[mt]=it,Pt}},2013:(d,b,n)=>{"use strict";var R,P,w,h=n(3689),v=n(3029),c=n(8999),g=n(5391),y=n(1868),O=n(1880),x=n(4201),S=n(3931),C=x("iterator"),A=!1;[].keys&&("next"in(w=[].keys())?(P=y(y(w)))!==Object.prototype&&(R=P):A=!0),!c(R)||h(function(){var I={};return R[C].call(I)!==I})?R={}:S&&(R=g(R)),v(R[C])||O(R,C,function(){return this}),d.exports={IteratorPrototype:R,BUGGY_SAFARI_ITERATORS:A}},9478:d=>{"use strict";d.exports={}},6310:(d,b,n)=>{"use strict";var h=n(3126);d.exports=function(v){return h(v.length)}},8702:(d,b,n)=>{"use strict";var h=n(8844),v=n(3689),c=n(3029),g=n(6812),y=n(7697),O=n(1236).CONFIGURABLE,x=n(6738),S=n(618),C=S.enforce,A=S.get,R=String,P=Object.defineProperty,w=h("".slice),M=h("".replace),I=h([].join),B=y&&!v(function(){return 8!==P(function(){},"length",{value:8}).length}),H=String(String).split("String"),$=d.exports=function(F,G,Y){"Symbol("===w(R(G),0,7)&&(G="["+M(R(G),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),Y&&Y.getter&&(G="get "+G),Y&&Y.setter&&(G="set "+G),(!g(F,"name")||O&&F.name!==G)&&(y?P(F,"name",{value:G,configurable:!0}):F.name=G),B&&Y&&g(Y,"arity")&&F.length!==Y.arity&&P(F,"length",{value:Y.arity});try{Y&&g(Y,"constructor")&&Y.constructor?y&&P(F,"prototype",{writable:!1}):F.prototype&&(F.prototype=void 0)}catch{}var k=C(F);return g(k,"source")||(k.source=I(H,"string"==typeof G?G:"")),F};Function.prototype.toString=$(function(){return c(this)&&A(this).source||x(this)},"toString")},8828:d=>{"use strict";var b=Math.ceil,n=Math.floor;d.exports=Math.trunc||function(v){var c=+v;return(c>0?n:b)(c)}},231:(d,b,n)=>{"use strict";var I,B,H,$,F,h=n(1087),v=n(517),c=n(4071),g=n(9886).set,y=n(4410),O=n(1489),x=n(7636),S=n(9976),C=n(240),A=h.MutationObserver||h.WebKitMutationObserver,R=h.document,P=h.process,w=h.Promise,M=v("queueMicrotask");if(!M){var G=new y,Y=function(){var k,nt;for(C&&(k=P.domain)&&k.exit();nt=G.get();)try{nt()}catch(J){throw G.head&&I(),J}k&&k.enter()};O||C||S||!A||!R?!x&&w&&w.resolve?(($=w.resolve(void 0)).constructor=w,F=c($.then,$),I=function(){F(Y)}):C?I=function(){P.nextTick(Y)}:(g=c(g,h),I=function(){g(Y)}):(B=!0,H=R.createTextNode(""),new A(Y).observe(H,{characterData:!0}),I=function(){H.data=B=!B}),M=function(k){G.head||I(),G.add(k)}}d.exports=M},8742:(d,b,n)=>{"use strict";var h=n(509),v=TypeError,c=function(g){var y,O;this.promise=new g(function(x,S){if(void 0!==y||void 0!==O)throw new v("Bad Promise constructor");y=x,O=S}),this.resolve=h(y),this.reject=h(O)};d.exports.f=function(g){return new c(g)}},2124:(d,b,n)=>{"use strict";var h=n(1245),v=TypeError;d.exports=function(c){if(h(c))throw new v("The method doesn't accept regular expressions");return c}},5391:(d,b,n)=>{"use strict";var H,h=n(5027),v=n(8920),c=n(2739),g=n(7248),y=n(1422),O=n(6420),x=n(2713),A="prototype",R="script",P=x("IE_PROTO"),w=function(){},M=function(F){return"<"+R+">"+F+"</"+R+">"},I=function(F){F.write(M("")),F.close();var G=F.parentWindow.Object;return F=null,G},$=function(){try{H=new ActiveXObject("htmlfile")}catch{}$=typeof document<"u"?document.domain&&H?I(H):function(){var Y,F=O("iframe");return F.style.display="none",y.appendChild(F),F.src=String("javascript:"),(Y=F.contentWindow.document).open(),Y.write(M("document.F=Object")),Y.close(),Y.F}():I(H);for(var F=c.length;F--;)delete $[A][c[F]];return $()};g[P]=!0,d.exports=Object.create||function(G,Y){var k;return null!==G?(w[A]=h(G),k=new w,w[A]=null,k[P]=G):k=$(),void 0===Y?k:v.f(k,Y)}},8920:(d,b,n)=>{"use strict";var h=n(7697),v=n(5648),c=n(2560),g=n(5027),y=n(5290),O=n(300);b.f=h&&!v?Object.defineProperties:function(S,C){g(S);for(var M,A=y(C),R=O(C),P=R.length,w=0;P>w;)c.f(S,M=R[w++],A[M]);return S}},2560:(d,b,n)=>{"use strict";var h=n(7697),v=n(8506),c=n(5648),g=n(5027),y=n(8360),O=TypeError,x=Object.defineProperty,S=Object.getOwnPropertyDescriptor,C="enumerable",A="configurable",R="writable";b.f=h?c?function(w,M,I){if(g(w),M=y(M),g(I),"function"==typeof w&&"prototype"===M&&"value"in I&&R in I&&!I[R]){var B=S(w,M);B&&B[R]&&(w[M]=I.value,I={configurable:A in I?I[A]:B[A],enumerable:C in I?I[C]:B[C],writable:!1})}return x(w,M,I)}:x:function(w,M,I){if(g(w),M=y(M),g(I),v)try{return x(w,M,I)}catch{}if("get"in I||"set"in I)throw new O("Accessors not supported");return"value"in I&&(w[M]=I.value),w}},2474:(d,b,n)=>{"use strict";var h=n(7697),v=n(2615),c=n(9556),g=n(5684),y=n(5290),O=n(8360),x=n(6812),S=n(8506),C=Object.getOwnPropertyDescriptor;b.f=h?C:function(R,P){if(R=y(R),P=O(P),S)try{return C(R,P)}catch{}if(x(R,P))return g(!v(c.f,R,P),R[P])}},2741:(d,b,n)=>{"use strict";var h=n(4948),c=n(2739).concat("length","prototype");b.f=Object.getOwnPropertyNames||function(y){return h(y,c)}},7518:(d,b)=>{"use strict";b.f=Object.getOwnPropertySymbols},1868:(d,b,n)=>{"use strict";var h=n(6812),v=n(3029),c=n(690),g=n(2713),y=n(1748),O=g("IE_PROTO"),x=Object,S=x.prototype;d.exports=y?x.getPrototypeOf:function(C){var A=c(C);if(h(A,O))return A[O];var R=A.constructor;return v(R)&&A instanceof R?R.prototype:A instanceof x?S:null}},3622:(d,b,n)=>{"use strict";var h=n(8844);d.exports=h({}.isPrototypeOf)},4948:(d,b,n)=>{"use strict";var h=n(8844),v=n(6812),c=n(5290),g=n(4328).indexOf,y=n(7248),O=h([].push);d.exports=function(x,S){var P,C=c(x),A=0,R=[];for(P in C)!v(y,P)&&v(C,P)&&O(R,P);for(;S.length>A;)v(C,P=S[A++])&&(~g(R,P)||O(R,P));return R}},300:(d,b,n)=>{"use strict";var h=n(4948),v=n(2739);d.exports=Object.keys||function(g){return h(g,v)}},9556:(d,b)=>{"use strict";var n={}.propertyIsEnumerable,h=Object.getOwnPropertyDescriptor,v=h&&!n.call({1:2},1);b.f=v?function(g){var y=h(this,g);return!!y&&y.enumerable}:n},9385:(d,b,n)=>{"use strict";var h=n(2743),v=n(8999),c=n(4684),g=n(3550);d.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var x,y=!1,O={};try{(x=h(Object.prototype,"__proto__","set"))(O,[]),y=O instanceof Array}catch{}return function(C,A){return c(C),g(A),v(C)&&(y?x(C,A):C.__proto__=A),C}}():void 0)},5899:(d,b,n)=>{"use strict";var h=n(2615),v=n(3029),c=n(8999),g=TypeError;d.exports=function(y,O){var x,S;if("string"===O&&v(x=y.toString)&&!c(S=h(x,y))||v(x=y.valueOf)&&!c(S=h(x,y))||"string"!==O&&v(x=y.toString)&&!c(S=h(x,y)))return S;throw new g("Can't convert object to primitive value")}},9152:(d,b,n)=>{"use strict";var h=n(6058),v=n(8844),c=n(2741),g=n(7518),y=n(5027),O=v([].concat);d.exports=h("Reflect","ownKeys")||function(S){var C=c.f(y(S)),A=g.f;return A?O(C,A(S)):C}},9302:d=>{"use strict";d.exports=function(b){try{return{error:!1,value:b()}}catch(n){return{error:!0,value:n}}}},7073:(d,b,n)=>{"use strict";var h=n(1087),v=n(7919),c=n(3029),g=n(5266),y=n(6738),O=n(4201),x=n(9791),S=n(3931),C=n(1352),A=v&&v.prototype,R=O("species"),P=!1,w=c(h.PromiseRejectionEvent),M=g("Promise",function(){var I=y(v),B=I!==String(v);if(!B&&66===C||S&&(!A.catch||!A.finally))return!0;if(!C||C<51||!/native code/.test(I)){var H=new v(function(G){G(1)}),$=function(G){G(function(){},function(){})};if((H.constructor={})[R]=$,!(P=H.then(function(){})instanceof $))return!0}return!(B||"BROWSER"!==x&&"DENO"!==x||w)});d.exports={CONSTRUCTOR:M,REJECTION_EVENT:w,SUBCLASSING:P}},7919:(d,b,n)=>{"use strict";var h=n(1087);d.exports=h.Promise},2945:(d,b,n)=>{"use strict";var h=n(5027),v=n(8999),c=n(8742);d.exports=function(g,y){if(h(g),v(y)&&y.constructor===g)return y;var O=c.f(g);return(0,O.resolve)(y),O.promise}},562:(d,b,n)=>{"use strict";var h=n(7919),v=n(6431),c=n(7073).CONSTRUCTOR;d.exports=c||!v(function(g){h.all(g).then(void 0,function(){})})},4410:d=>{"use strict";var b=function(){this.head=null,this.tail=null};b.prototype={add:function(n){var h={item:n,next:null},v=this.tail;v?v.next=h:this.head=h,this.tail=h},get:function(){var n=this.head;if(n)return null===(this.head=n.next)&&(this.tail=null),n.item}},d.exports=b},6100:(d,b,n)=>{"use strict";var h=n(2615),v=n(5027),c=n(3029),g=n(6648),y=n(6308),O=TypeError;d.exports=function(x,S){var C=x.exec;if(c(C)){var A=h(C,x,S);return null!==A&&v(A),A}if("RegExp"===g(x))return h(y,x,S);throw new O("RegExp#exec called on incompatible receiver")}},6308:(d,b,n)=>{"use strict";var k,nt,h=n(2615),v=n(8844),c=n(9663),g=n(9633),y=n(7901),O=n(3430),x=n(5391),S=n(618).get,C=n(2100),A=n(6422),R=O("native-string-replace",String.prototype.replace),P=RegExp.prototype.exec,w=P,M=v("".charAt),I=v("".indexOf),B=v("".replace),H=v("".slice),$=(nt=/b*/g,h(P,k=/a/,"a"),h(P,nt,"a"),0!==k.lastIndex||0!==nt.lastIndex),F=y.BROKEN_CARET,G=void 0!==/()??/.exec("")[1];($||G||F||C||A)&&(w=function(nt){var ht,ut,_,Q,rt,Bt,at,J=this,mt=S(J),lt=c(nt),et=mt.raw;if(et)return et.lastIndex=J.lastIndex,ht=h(w,et,lt),J.lastIndex=et.lastIndex,ht;var ct=mt.groups,it=F&&J.sticky,Ot=h(g,J),bt=J.source,Pt=0,Ft=lt;if(it&&(Ot=B(Ot,"y",""),-1===I(Ot,"g")&&(Ot+="g"),Ft=H(lt,J.lastIndex),J.lastIndex>0&&(!J.multiline||J.multiline&&"\n"!==M(lt,J.lastIndex-1))&&(bt="(?: "+bt+")",Ft=" "+Ft,Pt++),ut=new RegExp("^(?:"+bt+")",Ot)),G&&(ut=new RegExp("^"+bt+"$(?!\\s)",Ot)),$&&(_=J.lastIndex),Q=h(P,it?ut:J,Ft),it?Q?(Q.input=H(Q.input,Pt),Q[0]=H(Q[0],Pt),Q.index=J.lastIndex,J.lastIndex+=Q[0].length):J.lastIndex=0:$&&Q&&(J.lastIndex=J.global?Q.index+Q[0].length:_),G&&Q&&Q.length>1&&h(R,Q[0],ut,function(){for(rt=1;rt<arguments.length-2;rt++)void 0===arguments[rt]&&(Q[rt]=void 0)}),Q&&ct)for(Q.groups=Bt=x(null),rt=0;rt<ct.length;rt++)Bt[(at=ct[rt])[0]]=Q[at[1]];return Q}),d.exports=w},9633:(d,b,n)=>{"use strict";var h=n(5027);d.exports=function(){var v=h(this),c="";return v.hasIndices&&(c+="d"),v.global&&(c+="g"),v.ignoreCase&&(c+="i"),v.multiline&&(c+="m"),v.dotAll&&(c+="s"),v.unicode&&(c+="u"),v.unicodeSets&&(c+="v"),v.sticky&&(c+="y"),c}},3477:(d,b,n)=>{"use strict";var h=n(2615),v=n(6812),c=n(3622),g=n(9633),y=RegExp.prototype;d.exports=function(O){var x=O.flags;return void 0!==x||"flags"in y||v(O,"flags")||!c(y,O)?x:h(g,O)}},7901:(d,b,n)=>{"use strict";var h=n(3689),c=n(1087).RegExp,g=h(function(){var x=c("a","y");return x.lastIndex=2,null!==x.exec("abcd")}),y=g||h(function(){return!c("a","y").sticky}),O=g||h(function(){var x=c("^r","gy");return x.lastIndex=2,null!==x.exec("str")});d.exports={BROKEN_CARET:O,MISSED_STICKY:y,UNSUPPORTED_Y:g}},2100:(d,b,n)=>{"use strict";var h=n(3689),c=n(1087).RegExp;d.exports=h(function(){var g=c(".","s");return!(g.dotAll&&g.test("\n")&&"s"===g.flags)})},6422:(d,b,n)=>{"use strict";var h=n(3689),c=n(1087).RegExp;d.exports=h(function(){var g=c("(?<a>b)","g");return"b"!==g.exec("b").groups.a||"bc"!=="b".replace(g,"$<a>c")})},4684:(d,b,n)=>{"use strict";var h=n(981),v=TypeError;d.exports=function(c){if(h(c))throw new v("Can't call method on "+c);return c}},517:(d,b,n)=>{"use strict";var h=n(1087),v=n(7697),c=Object.getOwnPropertyDescriptor;d.exports=function(g){if(!v)return h[g];var y=c(h,g);return y&&y.value}},4241:(d,b,n)=>{"use strict";var h=n(6058),v=n(2148),c=n(4201),g=n(7697),y=c("species");d.exports=function(O){var x=h(O);g&&x&&!x[y]&&v(x,y,{configurable:!0,get:function(){return this}})}},5997:(d,b,n)=>{"use strict";var h=n(2560).f,v=n(6812),g=n(4201)("toStringTag");d.exports=function(y,O,x){y&&!x&&(y=y.prototype),y&&!v(y,g)&&h(y,g,{configurable:!0,value:O})}},2713:(d,b,n)=>{"use strict";var h=n(3430),v=n(4630),c=h("keys");d.exports=function(g){return c[g]||(c[g]=v(g))}},4091:(d,b,n)=>{"use strict";var h=n(3931),v=n(1087),c=n(5014),g="__core-js_shared__",y=d.exports=v[g]||c(g,{});(y.versions||(y.versions=[])).push({version:"3.42.0",mode:h?"pure":"global",copyright:"\xa9 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE",source:"https://github.com/zloirock/core-js"})},3430:(d,b,n)=>{"use strict";var h=n(4091);d.exports=function(v,c){return h[v]||(h[v]=c||{})}},6373:(d,b,n)=>{"use strict";var h=n(5027),v=n(2655),c=n(981),y=n(4201)("species");d.exports=function(O,x){var C,S=h(O).constructor;return void 0===S||c(C=h(S)[y])?x:v(C)}},730:(d,b,n)=>{"use strict";var h=n(8844),v=n(8700),c=n(9663),g=n(4684),y=h("".charAt),O=h("".charCodeAt),x=h("".slice),S=function(C){return function(A,R){var I,B,P=c(g(A)),w=v(R),M=P.length;return w<0||w>=M?C?"":void 0:(I=O(P,w))<55296||I>56319||w+1===M||(B=O(P,w+1))<56320||B>57343?C?y(P,w):I:C?x(P,w,w+2):B-56320+(I-55296<<10)+65536}};d.exports={codeAt:S(!1),charAt:S(!0)}},5984:(d,b,n)=>{"use strict";var h=n(1236).PROPER,v=n(3689),c=n(6350);d.exports=function(y){return v(function(){return!!c[y]()||"\u200b\x85\u180e"!=="\u200b\x85\u180e"[y]()||h&&c[y].name!==y})}},1435:(d,b,n)=>{"use strict";var h=n(8844),v=n(4684),c=n(9663),g=n(6350),y=h("".replace),O=RegExp("^["+g+"]+"),x=RegExp("(^|[^"+g+"])["+g+"]+$"),S=function(C){return function(A){var R=c(v(A));return 1&C&&(R=y(R,O,"")),2&C&&(R=y(R,x,"$1")),R}};d.exports={start:S(1),end:S(2),trim:S(3)}},146:(d,b,n)=>{"use strict";var h=n(1352),v=n(3689),g=n(1087).String;d.exports=!!Object.getOwnPropertySymbols&&!v(function(){var y=Symbol("symbol detection");return!g(y)||!(Object(y)instanceof Symbol)||!Symbol.sham&&h&&h<41})},9886:(d,b,n)=>{"use strict";var nt,J,mt,lt,h=n(1087),v=n(1735),c=n(4071),g=n(3029),y=n(6812),O=n(3689),x=n(1422),S=n(6004),C=n(6420),A=n(1500),R=n(1489),P=n(240),w=h.setImmediate,M=h.clearImmediate,I=h.process,B=h.Dispatch,H=h.Function,$=h.MessageChannel,F=h.String,G=0,Y={},k="onreadystatechange";O(function(){nt=h.location});var et=function(Q){if(y(Y,Q)){var rt=Y[Q];delete Y[Q],rt()}},ht=function(Q){return function(){et(Q)}},ut=function(Q){et(Q.data)},_=function(Q){h.postMessage(F(Q),nt.protocol+"//"+nt.host)};(!w||!M)&&(w=function(rt){A(arguments.length,1);var Bt=g(rt)?rt:H(rt),at=S(arguments,1);return Y[++G]=function(){v(Bt,void 0,at)},J(G),G},M=function(rt){delete Y[rt]},P?J=function(Q){I.nextTick(ht(Q))}:B&&B.now?J=function(Q){B.now(ht(Q))}:$&&!R?(lt=(mt=new $).port2,mt.port1.onmessage=ut,J=c(lt.postMessage,lt)):h.addEventListener&&g(h.postMessage)&&!h.importScripts&&nt&&"file:"!==nt.protocol&&!O(_)?(J=_,h.addEventListener("message",ut,!1)):J=k in C("script")?function(Q){x.appendChild(C("script"))[k]=function(){x.removeChild(this),et(Q)}}:function(Q){setTimeout(ht(Q),0)}),d.exports={set:w,clear:M}},7578:(d,b,n)=>{"use strict";var h=n(8700),v=Math.max,c=Math.min;d.exports=function(g,y){var O=h(g);return O<0?v(O+y,0):c(O,y)}},5290:(d,b,n)=>{"use strict";var h=n(4413),v=n(4684);d.exports=function(c){return h(v(c))}},8700:(d,b,n)=>{"use strict";var h=n(8828);d.exports=function(v){var c=+v;return c!=c||0===c?0:h(c)}},3126:(d,b,n)=>{"use strict";var h=n(8700),v=Math.min;d.exports=function(c){var g=h(c);return g>0?v(g,9007199254740991):0}},690:(d,b,n)=>{"use strict";var h=n(4684),v=Object;d.exports=function(c){return v(h(c))}},8732:(d,b,n)=>{"use strict";var h=n(2615),v=n(8999),c=n(734),g=n(4849),y=n(5899),O=n(4201),x=TypeError,S=O("toPrimitive");d.exports=function(C,A){if(!v(C)||c(C))return C;var P,R=g(C,S);if(R){if(void 0===A&&(A="default"),P=h(R,C,A),!v(P)||c(P))return P;throw new x("Can't convert object to primitive value")}return void 0===A&&(A="number"),y(C,A)}},8360:(d,b,n)=>{"use strict";var h=n(8732),v=n(734);d.exports=function(c){var g=h(c,"string");return v(g)?g:g+""}},3043:(d,b,n)=>{"use strict";var c={};c[n(4201)("toStringTag")]="z",d.exports="[object z]"===String(c)},9663:(d,b,n)=>{"use strict";var h=n(926),v=String;d.exports=function(c){if("Symbol"===h(c))throw new TypeError("Cannot convert a Symbol value to a string");return v(c)}},3691:d=>{"use strict";var b=String;d.exports=function(n){try{return b(n)}catch{return"Object"}}},4630:(d,b,n)=>{"use strict";var h=n(8844),v=0,c=Math.random(),g=h(1..toString);d.exports=function(y){return"Symbol("+(void 0===y?"":y)+")_"+g(++v+c,36)}},9525:(d,b,n)=>{"use strict";var h=n(146);d.exports=h&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},5648:(d,b,n)=>{"use strict";var h=n(7697),v=n(3689);d.exports=h&&v(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},1500:d=>{"use strict";var b=TypeError;d.exports=function(n,h){if(n<h)throw new b("Not enough arguments");return n}},9834:(d,b,n)=>{"use strict";var h=n(1087),v=n(3029),c=h.WeakMap;d.exports=v(c)&&/native code/.test(String(c))},4201:(d,b,n)=>{"use strict";var h=n(1087),v=n(3430),c=n(6812),g=n(4630),y=n(146),O=n(9525),x=h.Symbol,S=v("wks"),C=O?x.for||x:x&&x.withoutSetter||g;d.exports=function(A){return c(S,A)||(S[A]=y&&c(x,A)?x[A]:C("Symbol."+A)),S[A]}},6350:d=>{"use strict";d.exports="\t\n\v\f\r \xa0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029\ufeff"},7195:(d,b,n)=>{"use strict";var h=n(9989),v=n(6576),c=n(4328).indexOf,g=n(6834),y=v([].indexOf),O=!!y&&1/y([1],1,-0)<0;h({target:"Array",proto:!0,forced:O||!g("indexOf")},{indexOf:function(C){var A=arguments.length>1?arguments[1]:void 0;return O?y(this,C,A)||0:c(this,C,A)}})},752:(d,b,n)=>{"use strict";var h=n(5290),v=n(7370),c=n(9478),g=n(618),y=n(2560).f,O=n(1934),x=n(7807),S=n(3931),C=n(7697),A="Array Iterator",R=g.set,P=g.getterFor(A);d.exports=O(Array,"Array",function(M,I){R(this,{type:A,target:h(M),index:0,kind:I})},function(){var M=P(this),I=M.target,B=M.index++;if(!I||B>=I.length)return M.target=null,x(void 0,!0);switch(M.kind){case"keys":return x(B,!1);case"values":return x(I[B],!1)}return x([B,I[B]],!1)},"values");var w=c.Arguments=c.Array;if(v("keys"),v("values"),v("entries"),!S&&C&&"values"!==w.name)try{y(w,"name",{value:"values"})}catch{}},278:(d,b,n)=>{"use strict";var h=n(9989),v=n(8820).left,c=n(6834),g=n(1352);h({target:"Array",proto:!0,forced:!n(240)&&g>79&&g<83||!c("reduce")},{reduce:function(C){var A=arguments.length;return v(this,C,A,A>1?arguments[1]:void 0)}})},3374:(d,b,n)=>{"use strict";var h=n(9989),v=n(8844),c=n(2297),g=v([].reverse),y=[1,2];h({target:"Array",proto:!0,forced:String(y)===String(y.reverse())},{reverse:function(){return c(this)&&(this.length=this.length),g(this)}})},1692:(d,b,n)=>{"use strict";var h=n(9989),v=n(2615),c=n(509),g=n(8742),y=n(9302),O=n(8734);h({target:"Promise",stat:!0,forced:n(562)},{all:function(C){var A=this,R=g.f(A),P=R.resolve,w=R.reject,M=y(function(){var I=c(A.resolve),B=[],H=0,$=1;O(C,function(F){var G=H++,Y=!1;$++,v(I,A,F).then(function(k){Y||(Y=!0,B[G]=k,--$||P(B))},w)}),--$||P(B)});return M.error&&w(M.value),R.promise}})},5089:(d,b,n)=>{"use strict";var h=n(9989),v=n(3931),c=n(7073).CONSTRUCTOR,g=n(7919),y=n(6058),O=n(3029),x=n(1880),S=g&&g.prototype;if(h({target:"Promise",proto:!0,forced:c,real:!0},{catch:function(A){return this.then(void 0,A)}}),!v&&O(g)){var C=y("Promise").prototype.catch;S.catch!==C&&x(S,"catch",C,{unsafe:!0})}},6697:(d,b,n)=>{"use strict";var Zt,ee,re,h=n(9989),v=n(3931),c=n(240),g=n(1087),y=n(2615),O=n(1880),x=n(9385),S=n(5997),C=n(4241),A=n(509),R=n(3029),P=n(8999),w=n(767),M=n(6373),I=n(9886).set,B=n(231),H=n(920),$=n(9302),F=n(4410),G=n(618),Y=n(7919),k=n(7073),nt=n(8742),J="Promise",mt=k.CONSTRUCTOR,lt=k.REJECTION_EVENT,et=k.SUBCLASSING,ht=G.getterFor(J),ut=G.set,_=Y&&Y.prototype,Q=Y,rt=_,Bt=g.TypeError,at=g.document,ct=g.process,it=nt.f,Ot=it,bt=!!(at&&at.createEvent&&g.dispatchEvent),Pt="unhandledrejection",ne=function(X){var Z;return!(!P(X)||!R(Z=X.then))&&Z},ue=function(X,Z){var ve,Ge,$e,pt=Z.value,gt=1===Z.state,St=gt?X.ok:X.fail,ae=X.resolve,Me=X.reject,fe=X.domain;try{St?(gt||(2===Z.rejection&&nr(Z),Z.rejection=1),!0===St?ve=pt:(fe&&fe.enter(),ve=St(pt),fe&&(fe.exit(),$e=!0)),ve===X.promise?Me(new Bt("Promise-chain cycle")):(Ge=ne(ve))?y(Ge,ve,ae,Me):ae(ve)):Me(pt)}catch(ar){fe&&!$e&&fe.exit(),Me(ar)}},le=function(X,Z){X.notified||(X.notified=!0,B(function(){for(var gt,pt=X.reactions;gt=pt.get();)ue(gt,X);X.notified=!1,Z&&!X.rejection&&Ne(X)}))},xe=function(X,Z,pt){var gt,St;bt?((gt=at.createEvent("Event")).promise=Z,gt.reason=pt,gt.initEvent(X,!1,!0),g.dispatchEvent(gt)):gt={promise:Z,reason:pt},!lt&&(St=g["on"+X])?St(gt):X===Pt&&H("Unhandled promise rejection",pt)},Ne=function(X){y(I,g,function(){var St,Z=X.facade,pt=X.value;if(ze(X)&&(St=$(function(){c?ct.emit("unhandledRejection",pt,Z):xe(Pt,Z,pt)}),X.rejection=c||ze(X)?2:1,St.error))throw St.value})},ze=function(X){return 1!==X.rejection&&!X.parent},nr=function(X){y(I,g,function(){var Z=X.facade;c?ct.emit("rejectionHandled",Z):xe("rejectionhandled",Z,X.value)})},he=function(X,Z,pt){return function(gt){X(Z,gt,pt)}},$t=function(X,Z,pt){X.done||(X.done=!0,pt&&(X=pt),X.value=Z,X.state=2,le(X,!0))},Ie=function(X,Z,pt){if(!X.done){X.done=!0,pt&&(X=pt);try{if(X.facade===Z)throw new Bt("Promise can't be resolved itself");var gt=ne(Z);gt?B(function(){var St={done:!1};try{y(gt,Z,he(Ie,St,X),he($t,St,X))}catch(ae){$t(St,ae,X)}}):(X.value=Z,X.state=1,le(X,!1))}catch(St){$t({done:!1},St,X)}}};if(mt&&(Q=function(Z){w(this,rt),A(Z),y(Zt,this);var pt=ht(this);try{Z(he(Ie,pt),he($t,pt))}catch(gt){$t(pt,gt)}},(Zt=function(Z){ut(this,{type:J,done:!1,notified:!1,parent:!1,reactions:new F,rejection:!1,state:0,value:null})}).prototype=O(rt=Q.prototype,"then",function(Z,pt){var gt=ht(this),St=it(M(this,Q));return gt.parent=!0,St.ok=!R(Z)||Z,St.fail=R(pt)&&pt,St.domain=c?ct.domain:void 0,0===gt.state?gt.reactions.add(St):B(function(){ue(St,gt)}),St.promise}),ee=function(){var X=new Zt,Z=ht(X);this.promise=X,this.resolve=he(Ie,Z),this.reject=he($t,Z)},nt.f=it=function(X){return X===Q||void 0===X?new ee(X):Ot(X)},!v&&R(Y)&&_!==Object.prototype)){re=_.then,et||O(_,"then",function(Z,pt){var gt=this;return new Q(function(St,ae){y(re,gt,St,ae)}).then(Z,pt)},{unsafe:!0});try{delete _.constructor}catch{}x&&x(_,rt)}h({global:!0,constructor:!0,wrap:!0,forced:mt},{Promise:Q}),S(Q,J,!1,!0),C(J)},3964:(d,b,n)=>{"use strict";n(6697),n(1692),n(5089),n(8829),n(7261),n(7905)},8829:(d,b,n)=>{"use strict";var h=n(9989),v=n(2615),c=n(509),g=n(8742),y=n(9302),O=n(8734);h({target:"Promise",stat:!0,forced:n(562)},{race:function(C){var A=this,R=g.f(A),P=R.reject,w=y(function(){var M=c(A.resolve);O(C,function(I){v(M,A,I).then(R.resolve,P)})});return w.error&&P(w.value),R.promise}})},7261:(d,b,n)=>{"use strict";var h=n(9989),v=n(8742);h({target:"Promise",stat:!0,forced:n(7073).CONSTRUCTOR},{reject:function(y){var O=v.f(this);return(0,O.reject)(y),O.promise}})},7905:(d,b,n)=>{"use strict";var h=n(9989),v=n(6058),c=n(3931),g=n(7919),y=n(7073).CONSTRUCTOR,O=n(2945),x=v("Promise"),S=c&&!y;h({target:"Promise",stat:!0,forced:c||y},{resolve:function(A){return O(S&&this===x?g:this,A)}})},4043:(d,b,n)=>{"use strict";var h=n(9989),v=n(6308);h({target:"RegExp",proto:!0,forced:/./.exec!==v},{exec:v})},2826:(d,b,n)=>{"use strict";var h=n(1236).PROPER,v=n(1880),c=n(5027),g=n(9663),y=n(3689),O=n(3477),x="toString",S=RegExp.prototype,C=S[x];(y(function(){return"/a/b"!==C.call({source:"a",flags:"b"})})||h&&C.name!==x)&&v(S,x,function(){var w=c(this);return"/"+g(w.source)+"/"+g(O(w))},{unsafe:!0})},2918:(d,b,n)=>{"use strict";var M,h=n(9989),v=n(6576),c=n(2474).f,g=n(3126),y=n(9663),O=n(2124),x=n(4684),S=n(7413),C=n(3931),A=v("".slice),R=Math.min,P=S("endsWith");h({target:"String",proto:!0,forced:!(!C&&!P&&(M=c(String.prototype,"endsWith"),M&&!M.writable)||P)},{endsWith:function(I){var B=y(x(this));O(I);var H=arguments.length>1?arguments[1]:void 0,$=B.length,F=void 0===H?$:R(g(H),$),G=y(I);return A(B,F-G.length,F)===G}})},3843:(d,b,n)=>{"use strict";var h=n(9989),v=n(8844),c=n(2124),g=n(4684),y=n(9663),O=n(7413),x=v("".indexOf);h({target:"String",proto:!0,forced:!O("includes")},{includes:function(C){return!!~x(y(g(this)),y(c(C)),arguments.length>1?arguments[1]:void 0)}})},2462:(d,b,n)=>{"use strict";var h=n(2615),v=n(8678),c=n(5027),g=n(8999),y=n(3126),O=n(9663),x=n(4684),S=n(4849),C=n(1514),A=n(6100);v("match",function(R,P,w){return[function(I){var B=x(this),H=g(I)?S(I,R):void 0;return H?h(H,I,B):new RegExp(I)[R](O(B))},function(M){var I=c(this),B=O(M),H=w(P,I,B);if(H.done)return H.value;if(!I.global)return A(I,B);var $=I.unicode;I.lastIndex=0;for(var Y,F=[],G=0;null!==(Y=A(I,B));){var k=O(Y[0]);F[G]=k,""===k&&(I.lastIndex=C(B,y(I.lastIndex),$)),G++}return 0===G?null:F}]})},7267:(d,b,n)=>{"use strict";var h=n(1735),v=n(2615),c=n(8844),g=n(8678),y=n(3689),O=n(5027),x=n(3029),S=n(8999),C=n(8700),A=n(3126),R=n(9663),P=n(4684),w=n(1514),M=n(4849),I=n(7017),B=n(6100),$=n(4201)("replace"),F=Math.max,G=Math.min,Y=c([].concat),k=c([].push),nt=c("".indexOf),J=c("".slice),mt=function(ut){return void 0===ut?ut:String(ut)},lt="$0"==="a".replace(/./,"$0"),et=!!/./[$]&&""===/./[$]("a","$0");g("replace",function(ut,_,Q){var rt=et?"$":"$0";return[function(at,ct){var it=P(this),Ot=S(at)?M(at,$):void 0;return Ot?v(Ot,at,it,ct):v(_,R(it),at,ct)},function(Bt,at){var ct=O(this),it=R(Bt);if("string"==typeof at&&-1===nt(at,rt)&&-1===nt(at,"$<")){var Ot=Q(_,ct,it,at);if(Ot.done)return Ot.value}var bt=x(at);bt||(at=R(at));var Ft,Pt=ct.global;Pt&&(Ft=ct.unicode,ct.lastIndex=0);for(var jt,V=[];null!==(jt=B(ct,it))&&(k(V,jt),Pt);)""===R(jt[0])&&(ct.lastIndex=w(it,A(ct.lastIndex),Ft));for(var kt="",te=0,Zt=0;Zt<V.length;Zt++){for(var ne,ee=R((jt=V[Zt])[0]),oe=F(G(C(jt.index),it.length),0),re=[],ue=1;ue<jt.length;ue++)k(re,mt(jt[ue]));var le=jt.groups;if(bt){var xe=Y([ee],re,oe,it);void 0!==le&&k(xe,le),ne=R(h(at,void 0,xe))}else ne=I(ee,it,oe,re,le,at);oe>=te&&(kt+=J(it,te,oe)+ne,te=oe+ee.length)}return kt+J(it,te)}]},!!y(function(){var ut=/./;return ut.exec=function(){var _=[];return _.groups={a:"7"},_},"7"!=="".replace(ut,"$<a>")})||!lt||et)},9873:(d,b,n)=>{"use strict";var h=n(2615),v=n(8844),c=n(8678),g=n(5027),y=n(8999),O=n(4684),x=n(6373),S=n(1514),C=n(3126),A=n(9663),R=n(4849),P=n(6100),w=n(7901),M=n(3689),I=w.UNSUPPORTED_Y,H=Math.min,$=v([].push),F=v("".slice),G=!M(function(){var k=/(?:)/,nt=k.exec;k.exec=function(){return nt.apply(this,arguments)};var J="ab".split(k);return 2!==J.length||"a"!==J[0]||"b"!==J[1]}),Y="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;c("split",function(k,nt,J){var mt="0".split(void 0,0).length?function(lt,et){return void 0===lt&&0===et?[]:h(nt,this,lt,et)}:nt;return[function(et,ht){var ut=O(this),_=y(et)?R(et,k):void 0;return _?h(_,et,ut,ht):h(mt,A(ut),et,ht)},function(lt,et){var ht=g(this),ut=A(lt);if(!Y){var _=J(mt,ht,ut,et,mt!==nt);if(_.done)return _.value}var Q=x(ht,RegExp),rt=ht.unicode,at=new Q(I?"^(?:"+ht.source+")":ht,(ht.ignoreCase?"i":"")+(ht.multiline?"m":"")+(ht.unicode?"u":"")+(I?"g":"y")),ct=void 0===et?4294967295:et>>>0;if(0===ct)return[];if(0===ut.length)return null===P(at,ut)?[ut]:[];for(var it=0,Ot=0,bt=[];Ot<ut.length;){at.lastIndex=I?0:Ot;var Ft,Pt=P(at,I?F(ut,Ot):ut);if(null===Pt||(Ft=H(C(at.lastIndex+(I?Ot:0)),ut.length))===it)Ot=S(ut,Ot,rt);else{if($(bt,F(ut,it,Ot)),bt.length===ct)return bt;for(var V=1;V<=Pt.length-1;V++)if($(bt,Pt[V]),bt.length===ct)return bt;Ot=it=Ft}}return $(bt,F(ut,it)),bt}]},Y||!G,I)},268:(d,b,n)=>{"use strict";var M,h=n(9989),v=n(6576),c=n(2474).f,g=n(3126),y=n(9663),O=n(2124),x=n(4684),S=n(7413),C=n(3931),A=v("".slice),R=Math.min,P=S("startsWith");h({target:"String",proto:!0,forced:!(!C&&!P&&(M=c(String.prototype,"startsWith"),M&&!M.writable)||P)},{startsWith:function(I){var B=y(x(this));O(I);var H=g(R(arguments.length>1?arguments[1]:void 0,B.length)),$=y(I);return A(B,H,H+$.length)===$}})},8436:(d,b,n)=>{"use strict";var h=n(9989),v=n(1435).trim;h({target:"String",proto:!0,forced:n(5984)("trim")},{trim:function(){return v(this)}})},6265:(d,b,n)=>{"use strict";var h=n(1087),v=n(6338),c=n(3265),g=n(752),y=n(5773),O=n(5997),S=n(4201)("iterator"),C=g.values,A=function(P,w){if(P){if(P[S]!==C)try{y(P,S,C)}catch{P[S]=C}if(O(P,w,!0),v[w])for(var M in g)if(P[M]!==g[M])try{y(P,M,g[M])}catch{P[M]=g[M]}}};for(var R in v)A(h[R]&&h[R].prototype,R);A(c,"DOMTokenList")},210:(d,b,n)=>{"use strict";function v(l,t,e,r,a,i,o){try{var s=l[i](o),u=s.value}catch(f){return void e(f)}s.done?t(u):Promise.resolve(u).then(r,a)}function c(l){return function(){var t=this,e=arguments;return new Promise(function(r,a){var i=l.apply(t,e);function o(u){v(i,r,a,o,s,"next",u)}function s(u){v(i,r,a,o,s,"throw",u)}o(void 0)})}}n.r(b),n.d(b,{AElement:()=>sn,AnimateColorElement:()=>_r,AnimateElement:()=>Qe,AnimateTransformElement:()=>tn,BoundingBox:()=>Qt,CB1:()=>or,CB2:()=>ur,CB3:()=>lr,CB4:()=>hr,Canvg:()=>Se,CircleElement:()=>Hr,ClipPathElement:()=>pn,DefsElement:()=>kr,DescElement:()=>bn,Document:()=>Cn,Element:()=>Nt,EllipseElement:()=>Yr,FeColorMatrixElement:()=>Tr,FeCompositeElement:()=>En,FeDropShadowElement:()=>mn,FeGaussianBlurElement:()=>Tn,FeMorphologyElement:()=>xn,FilterElement:()=>yn,Font:()=>Oe,FontElement:()=>en,FontFaceElement:()=>rn,GElement:()=>We,GlyphElement:()=>yr,GradientElement:()=>Er,ImageElement:()=>ln,LineElement:()=>Xr,LinearGradientElement:()=>Zr,MarkerElement:()=>Kr,MaskElement:()=>gn,Matrix:()=>dr,MissingGlyphElement:()=>nn,Mouse:()=>Dr,PSEUDO_ZERO:()=>Te,Parser:()=>Ye,PathElement:()=>xt,PathParser:()=>st,PatternElement:()=>Qr,Point:()=>Rt,PolygonElement:()=>Wr,PolylineElement:()=>xr,Property:()=>tt,QB1:()=>fr,QB2:()=>vr,QB3:()=>cr,RadialGradientElement:()=>Jr,RectElement:()=>mr,RenderedElement:()=>ce,Rotate:()=>Ur,SVGElement:()=>we,SVGFontLoader:()=>fn,Scale:()=>jr,Screen:()=>gr,Skew:()=>pr,SkewX:()=>zr,SkewY:()=>Gr,StopElement:()=>qr,StyleElement:()=>vn,SymbolElement:()=>hn,TRefElement:()=>an,TSpanElement:()=>Fe,TextElement:()=>ie,TextPathElement:()=>un,TitleElement:()=>On,Transform:()=>Xe,Translate:()=>Fr,UnknownElement:()=>$r,UseElement:()=>cn,ViewPort:()=>Vr,compressSpaces:()=>$t,default:()=>Se,getSelectorSpecificity:()=>wr,normalizeAttributeName:()=>gt,normalizeColor:()=>ae,parseExternalUrl:()=>St,presets:()=>he,toNumbers:()=>Z,trimLeft:()=>Ie,trimRight:()=>X,vectorMagnitude:()=>ir,vectorsAngle:()=>sr,vectorsRatio:()=>He}),n(3964),n(2462),n(7267),n(268),n(752),n(6265);var C=n(1002);function P(l,t,e){return(t=function R(l){var t=function A(l,t){if("object"!=(0,C.Z)(l)||!l)return l;var e=l[Symbol.toPrimitive];if(void 0!==e){var r=e.call(l,t||"default");if("object"!=(0,C.Z)(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(l)}(l,"string");return"symbol"==(0,C.Z)(t)?t:t+""}(t))in l?Object.defineProperty(l,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):l[t]=e,l}n(278),n(2918),n(9873);var B=n(6489),$=(n(8436),n(1239)),k=(n(7195),n(3843),n(3374),function(l,t){return(k=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])})(l,t)});function nt(l,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function e(){this.constructor=l}k(l,t),l.prototype=null===t?Object.create(t):(e.prototype=t.prototype,new e)}function mt(l,t){var e=l[0],r=l[1];return[e*Math.cos(t)-r*Math.sin(t),e*Math.sin(t)+r*Math.cos(t)]}function lt(){for(var l=[],t=0;t<arguments.length;t++)l[t]=arguments[t];for(var e=0;e<l.length;e++)if("number"!=typeof l[e])throw new Error("assertNumbers arguments["+e+"] is not a number. "+typeof l[e]+" == typeof "+l[e]);return!0}var et=Math.PI;function ht(l,t,e){l.lArcFlag=0===l.lArcFlag?0:1,l.sweepFlag=0===l.sweepFlag?0:1;var r=l.rX,a=l.rY,i=l.x,o=l.y;r=Math.abs(l.rX),a=Math.abs(l.rY);var s=mt([(t-i)/2,(e-o)/2],-l.xRot/180*et),u=s[0],f=s[1],p=Math.pow(u,2)/Math.pow(r,2)+Math.pow(f,2)/Math.pow(a,2);1<p&&(r*=Math.sqrt(p),a*=Math.sqrt(p)),l.rX=r,l.rY=a;var E=Math.pow(r,2)*Math.pow(f,2)+Math.pow(a,2)*Math.pow(u,2),T=(l.lArcFlag!==l.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(r,2)*Math.pow(a,2)-E)/E)),m=r*f/a*T,N=-a*u/r*T,D=mt([m,N],l.xRot/180*et);l.cX=D[0]+(t+i)/2,l.cY=D[1]+(e+o)/2,l.phi1=Math.atan2((f-N)/a,(u-m)/r),l.phi2=Math.atan2((-f-N)/a,(-u-m)/r),0===l.sweepFlag&&l.phi2>l.phi1&&(l.phi2-=2*et),1===l.sweepFlag&&l.phi2<l.phi1&&(l.phi2+=2*et),l.phi1*=180/et,l.phi2*=180/et}function ut(l,t,e){lt(l,t,e);var r=l*l+t*t-e*e;if(0>r)return[];if(0===r)return[[l*e/(l*l+t*t),t*e/(l*l+t*t)]];var a=Math.sqrt(r);return[[(l*e+t*a)/(l*l+t*t),(t*e-l*a)/(l*l+t*t)],[(l*e-t*a)/(l*l+t*t),(t*e+l*a)/(l*l+t*t)]]}var _,Q=Math.PI/180;function rt(l,t,e){return(1-e)*l+e*t}function Bt(l,t,e,r){return l+Math.cos(r/180*et)*t+Math.sin(r/180*et)*e}function at(l,t,e,r){var a=1e-6,i=t-l,o=e-t,s=3*i+3*(r-e)-6*o,u=6*(o-i),f=3*i;return Math.abs(s)<a?[-f/u]:function(p,E,T){void 0===T&&(T=1e-6);var m=p*p/4-E;if(m<-T)return[];if(m<=T)return[-p/2];var N=Math.sqrt(m);return[-p/2-N,-p/2+N]}(u/s,f/s,a)}function ct(l,t,e,r,a){var i=1-a;return l*(i*i*i)+t*(3*i*i*a)+e*(3*i*a*a)+r*(a*a*a)}!function(l){function t(){return a(function(s,u,f){return s.relative&&(void 0!==s.x1&&(s.x1+=u),void 0!==s.y1&&(s.y1+=f),void 0!==s.x2&&(s.x2+=u),void 0!==s.y2&&(s.y2+=f),void 0!==s.x&&(s.x+=u),void 0!==s.y&&(s.y+=f),s.relative=!1),s})}function e(){var s=NaN,u=NaN,f=NaN,p=NaN;return a(function(E,T,m){return E.type&V.SMOOTH_CURVE_TO&&(E.type=V.CURVE_TO,s=isNaN(s)?T:s,u=isNaN(u)?m:u,E.x1=E.relative?T-s:2*T-s,E.y1=E.relative?m-u:2*m-u),E.type&V.CURVE_TO?(s=E.relative?T+E.x2:E.x2,u=E.relative?m+E.y2:E.y2):(s=NaN,u=NaN),E.type&V.SMOOTH_QUAD_TO&&(E.type=V.QUAD_TO,f=isNaN(f)?T:f,p=isNaN(p)?m:p,E.x1=E.relative?T-f:2*T-f,E.y1=E.relative?m-p:2*m-p),E.type&V.QUAD_TO?(f=E.relative?T+E.x1:E.x1,p=E.relative?m+E.y1:E.y1):(f=NaN,p=NaN),E})}function r(){var s=NaN,u=NaN;return a(function(f,p,E){if(f.type&V.SMOOTH_QUAD_TO&&(f.type=V.QUAD_TO,s=isNaN(s)?p:s,u=isNaN(u)?E:u,f.x1=f.relative?p-s:2*p-s,f.y1=f.relative?E-u:2*E-u),f.type&V.QUAD_TO){s=f.relative?p+f.x1:f.x1,u=f.relative?E+f.y1:f.y1;var T=f.x1,m=f.y1;f.type=V.CURVE_TO,f.x1=((f.relative?0:p)+2*T)/3,f.y1=((f.relative?0:E)+2*m)/3,f.x2=(f.x+2*T)/3,f.y2=(f.y+2*m)/3}else s=NaN,u=NaN;return f})}function a(s){var u=0,f=0,p=NaN,E=NaN;return function(T){if(isNaN(p)&&!(T.type&V.MOVE_TO))throw new Error("path must start with moveto");var m=s(T,u,f,p,E);return T.type&V.CLOSE_PATH&&(u=p,f=E),void 0!==T.x&&(u=T.relative?u+T.x:T.x),void 0!==T.y&&(f=T.relative?f+T.y:T.y),T.type&V.MOVE_TO&&(p=u,E=f),m}}function i(s,u,f,p,E,T){return lt(s,u,f,p,E,T),a(function(m,N,D,L){var U=m.x1,K=m.x2,z=m.relative&&!isNaN(L),j=void 0!==m.x?m.x:z?0:N,q=void 0!==m.y?m.y:z?0:D;function ot(zt){return zt*zt}m.type&V.HORIZ_LINE_TO&&0!==u&&(m.type=V.LINE_TO,m.y=m.relative?0:D),m.type&V.VERT_LINE_TO&&0!==f&&(m.type=V.LINE_TO,m.x=m.relative?0:N),void 0!==m.x&&(m.x=m.x*s+q*f+(z?0:E)),void 0!==m.y&&(m.y=j*u+m.y*p+(z?0:T)),void 0!==m.x1&&(m.x1=m.x1*s+m.y1*f+(z?0:E)),void 0!==m.y1&&(m.y1=U*u+m.y1*p+(z?0:T)),void 0!==m.x2&&(m.x2=m.x2*s+m.y2*f+(z?0:E)),void 0!==m.y2&&(m.y2=K*u+m.y2*p+(z?0:T));var W=s*p-u*f;if(void 0!==m.xRot&&(1!==s||0!==u||0!==f||1!==p))if(0===W)delete m.rX,delete m.rY,delete m.xRot,delete m.lArcFlag,delete m.sweepFlag,m.type=V.LINE_TO;else{var ft=m.xRot*Math.PI/180,vt=Math.sin(ft),yt=Math.cos(ft),Et=1/ot(m.rX),Ct=1/ot(m.rY),Vt=ot(yt)*Et+ot(vt)*Ct,Dt=2*vt*yt*(Et-Ct),At=ot(vt)*Et+ot(yt)*Ct,Mt=Vt*p*p-Dt*u*p+At*u*u,It=Dt*(s*p+u*f)-2*(Vt*f*p+At*s*u),Ut=Vt*f*f-Dt*s*f+At*s*s,Tt=(Math.atan2(It,Mt-Ut)+Math.PI)%Math.PI/2,dt=Math.sin(Tt),Lt=Math.cos(Tt);m.rX=Math.abs(W)/Math.sqrt(Mt*ot(Lt)+It*dt*Lt+Ut*ot(dt)),m.rY=Math.abs(W)/Math.sqrt(Mt*ot(dt)-It*dt*Lt+Ut*ot(Lt)),m.xRot=180*Tt/Math.PI}return void 0!==m.sweepFlag&&0>W&&(m.sweepFlag=+!m.sweepFlag),m})}l.ROUND=function(s){function u(f){return Math.round(f*s)/s}return void 0===s&&(s=1e13),lt(s),function(f){return void 0!==f.x1&&(f.x1=u(f.x1)),void 0!==f.y1&&(f.y1=u(f.y1)),void 0!==f.x2&&(f.x2=u(f.x2)),void 0!==f.y2&&(f.y2=u(f.y2)),void 0!==f.x&&(f.x=u(f.x)),void 0!==f.y&&(f.y=u(f.y)),void 0!==f.rX&&(f.rX=u(f.rX)),void 0!==f.rY&&(f.rY=u(f.rY)),f}},l.TO_ABS=t,l.TO_REL=function(){return a(function(s,u,f){return s.relative||(void 0!==s.x1&&(s.x1-=u),void 0!==s.y1&&(s.y1-=f),void 0!==s.x2&&(s.x2-=u),void 0!==s.y2&&(s.y2-=f),void 0!==s.x&&(s.x-=u),void 0!==s.y&&(s.y-=f),s.relative=!0),s})},l.NORMALIZE_HVZ=function(s,u,f){return void 0===s&&(s=!0),void 0===u&&(u=!0),void 0===f&&(f=!0),a(function(p,E,T,m,N){if(isNaN(m)&&!(p.type&V.MOVE_TO))throw new Error("path must start with moveto");return u&&p.type&V.HORIZ_LINE_TO&&(p.type=V.LINE_TO,p.y=p.relative?0:T),f&&p.type&V.VERT_LINE_TO&&(p.type=V.LINE_TO,p.x=p.relative?0:E),s&&p.type&V.CLOSE_PATH&&(p.type=V.LINE_TO,p.x=p.relative?m-E:m,p.y=p.relative?N-T:N),p.type&V.ARC&&(0===p.rX||0===p.rY)&&(p.type=V.LINE_TO,delete p.rX,delete p.rY,delete p.xRot,delete p.lArcFlag,delete p.sweepFlag),p})},l.NORMALIZE_ST=e,l.QT_TO_C=r,l.INFO=a,l.SANITIZE=function(s){void 0===s&&(s=0),lt(s);var u=NaN,f=NaN,p=NaN,E=NaN;return a(function(T,m,N,D,L){var U=Math.abs,K=!1,z=0,j=0;if(T.type&V.SMOOTH_CURVE_TO&&(z=isNaN(u)?0:m-u,j=isNaN(f)?0:N-f),T.type&(V.CURVE_TO|V.SMOOTH_CURVE_TO)?(u=T.relative?m+T.x2:T.x2,f=T.relative?N+T.y2:T.y2):(u=NaN,f=NaN),T.type&V.SMOOTH_QUAD_TO?(p=isNaN(p)?m:2*m-p,E=isNaN(E)?N:2*N-E):T.type&V.QUAD_TO?(p=T.relative?m+T.x1:T.x1,E=T.relative?N+T.y1:T.y2):(p=NaN,E=NaN),T.type&V.LINE_COMMANDS||T.type&V.ARC&&(0===T.rX||0===T.rY||!T.lArcFlag)||T.type&V.CURVE_TO||T.type&V.SMOOTH_CURVE_TO||T.type&V.QUAD_TO||T.type&V.SMOOTH_QUAD_TO){var q=void 0===T.x?0:T.relative?T.x:T.x-m,ot=void 0===T.y?0:T.relative?T.y:T.y-N;z=isNaN(p)?void 0===T.x1?z:T.relative?T.x:T.x1-m:p-m,j=isNaN(E)?void 0===T.y1?j:T.relative?T.y:T.y1-N:E-N;var W=void 0===T.x2?0:T.relative?T.x:T.x2-m,ft=void 0===T.y2?0:T.relative?T.y:T.y2-N;U(q)<=s&&U(ot)<=s&&U(z)<=s&&U(j)<=s&&U(W)<=s&&U(ft)<=s&&(K=!0)}return T.type&V.CLOSE_PATH&&U(m-D)<=s&&U(N-L)<=s&&(K=!0),K?[]:T})},l.MATRIX=i,l.ROTATE=function(s,u,f){void 0===u&&(u=0),void 0===f&&(f=0),lt(s,u,f);var p=Math.sin(s),E=Math.cos(s);return i(E,p,-p,E,u-u*E+f*p,f-u*p-f*E)},l.TRANSLATE=function(s,u){return void 0===u&&(u=0),lt(s,u),i(1,0,0,1,s,u)},l.SCALE=function(s,u){return void 0===u&&(u=s),lt(s,u),i(s,0,0,u,0,0)},l.SKEW_X=function(s){return lt(s),i(1,0,Math.atan(s),1,0,0)},l.SKEW_Y=function(s){return lt(s),i(1,Math.atan(s),0,1,0,0)},l.X_AXIS_SYMMETRY=function(s){return void 0===s&&(s=0),lt(s),i(-1,0,0,1,s,0)},l.Y_AXIS_SYMMETRY=function(s){return void 0===s&&(s=0),lt(s),i(1,0,0,-1,0,s)},l.A_TO_C=function(){return a(function(s,u,f){return V.ARC===s.type?function(p,E,T){var m,N,D,L;p.cX||ht(p,E,T);for(var U=Math.min(p.phi1,p.phi2),K=Math.max(p.phi1,p.phi2)-U,z=Math.ceil(K/90),j=new Array(z),q=E,ot=T,W=0;W<z;W++){var ft=rt(p.phi1,p.phi2,W/z),vt=rt(p.phi1,p.phi2,(W+1)/z),Et=4/3*Math.tan((vt-ft)*Q/4),Ct=[Math.cos(ft*Q)-Et*Math.sin(ft*Q),Math.sin(ft*Q)+Et*Math.cos(ft*Q)],Vt=Ct[0],Dt=Ct[1],At=[Math.cos(vt*Q),Math.sin(vt*Q)],Mt=At[0],It=At[1],Ut=[Mt+Et*Math.sin(vt*Q),It-Et*Math.cos(vt*Q)],Tt=Ut[0],dt=Ut[1];j[W]={relative:p.relative,type:V.CURVE_TO};var Lt=function(zt,Ht){var Gt=mt([zt*p.rX,Ht*p.rY],p.xRot);return[p.cX+Gt[0],p.cY+Gt[1]]};m=Lt(Vt,Dt),j[W].x1=m[0],j[W].y1=m[1],N=Lt(Tt,dt),j[W].x2=N[0],j[W].y2=N[1],D=Lt(Mt,It),j[W].x=D[0],j[W].y=D[1],p.relative&&(j[W].x1-=q,j[W].y1-=ot,j[W].x2-=q,j[W].y2-=ot,j[W].x-=q,j[W].y-=ot),q=(L=[j[W].x,j[W].y])[0],ot=L[1]}return j}(s,s.relative?0:u,s.relative?0:f):s})},l.ANNOTATE_ARCS=function(){return a(function(s,u,f){return s.relative&&(u=0,f=0),V.ARC===s.type&&ht(s,u,f),s})},l.CLONE=function o(){return function(s){var u={};for(var f in s)u[f]=s[f];return u}},l.CALCULATE_BOUNDS=function(){var u=t(),f=r(),p=e(),E=a(function(T,m,N){var D=p(f(u(function(T){var m={};for(var N in T)m[N]=T[N];return m}(T))));function L(dt){dt>E.maxX&&(E.maxX=dt),dt<E.minX&&(E.minX=dt)}function U(dt){dt>E.maxY&&(E.maxY=dt),dt<E.minY&&(E.minY=dt)}if(D.type&V.DRAWING_COMMANDS&&(L(m),U(N)),D.type&V.HORIZ_LINE_TO&&L(D.x),D.type&V.VERT_LINE_TO&&U(D.y),D.type&V.LINE_TO&&(L(D.x),U(D.y)),D.type&V.CURVE_TO){L(D.x),U(D.y);for(var K=0,z=at(m,D.x1,D.x2,D.x);K<z.length;K++)0<(Tt=z[K])&&1>Tt&&L(ct(m,D.x1,D.x2,D.x,Tt));for(var j=0,q=at(N,D.y1,D.y2,D.y);j<q.length;j++)0<(Tt=q[j])&&1>Tt&&U(ct(N,D.y1,D.y2,D.y,Tt))}if(D.type&V.ARC){L(D.x),U(D.y),ht(D,m,N);for(var ot=D.xRot/180*Math.PI,W=Math.cos(ot)*D.rX,ft=Math.sin(ot)*D.rX,vt=-Math.sin(ot)*D.rY,yt=Math.cos(ot)*D.rY,Et=D.phi1<D.phi2?[D.phi1,D.phi2]:-180>D.phi2?[D.phi2+360,D.phi1+360]:[D.phi2,D.phi1],Ct=Et[0],Vt=Et[1],Dt=function(dt){var Ht=180*Math.atan2(dt[1],dt[0])/Math.PI;return Ht<Ct?Ht+360:Ht},At=0,Mt=ut(vt,-W,0).map(Dt);At<Mt.length;At++)(Tt=Mt[At])>Ct&&Tt<Vt&&L(Bt(D.cX,W,vt,Tt));for(var It=0,Ut=ut(yt,-ft,0).map(Dt);It<Ut.length;It++){var Tt;(Tt=Ut[It])>Ct&&Tt<Vt&&U(Bt(D.cY,ft,yt,Tt))}}return T});return E.minX=1/0,E.maxX=-1/0,E.minY=1/0,E.maxY=-1/0,E}}(_||(_={}));var it,Ot=function(){function l(){}return l.prototype.round=function(t){return this.transform(_.ROUND(t))},l.prototype.toAbs=function(){return this.transform(_.TO_ABS())},l.prototype.toRel=function(){return this.transform(_.TO_REL())},l.prototype.normalizeHVZ=function(t,e,r){return this.transform(_.NORMALIZE_HVZ(t,e,r))},l.prototype.normalizeST=function(){return this.transform(_.NORMALIZE_ST())},l.prototype.qtToC=function(){return this.transform(_.QT_TO_C())},l.prototype.aToC=function(){return this.transform(_.A_TO_C())},l.prototype.sanitize=function(t){return this.transform(_.SANITIZE(t))},l.prototype.translate=function(t,e){return this.transform(_.TRANSLATE(t,e))},l.prototype.scale=function(t,e){return this.transform(_.SCALE(t,e))},l.prototype.rotate=function(t,e,r){return this.transform(_.ROTATE(t,e,r))},l.prototype.matrix=function(t,e,r,a,i,o){return this.transform(_.MATRIX(t,e,r,a,i,o))},l.prototype.skewX=function(t){return this.transform(_.SKEW_X(t))},l.prototype.skewY=function(t){return this.transform(_.SKEW_Y(t))},l.prototype.xSymmetry=function(t){return this.transform(_.X_AXIS_SYMMETRY(t))},l.prototype.ySymmetry=function(t){return this.transform(_.Y_AXIS_SYMMETRY(t))},l.prototype.annotateArcs=function(){return this.transform(_.ANNOTATE_ARCS())},l}(),bt=function(l){return" "===l||"\t"===l||"\r"===l||"\n"===l},Pt=function(l){return"0".charCodeAt(0)<=l.charCodeAt(0)&&l.charCodeAt(0)<="9".charCodeAt(0)},Ft=function(l){function t(){var e=l.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return nt(t,l),t.prototype.finish=function(e){if(void 0===e&&(e=[]),this.parse(" ",e),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return e},t.prototype.parse=function(e,r){var a=this;void 0===r&&(r=[]);for(var i=function(E){r.push(E),a.curArgs.length=0,a.canParseCommandOrComma=!0},o=0;o<e.length;o++){var s=e[o],u=!(this.curCommandType!==V.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),f=Pt(s)&&("0"===this.curNumber&&"0"===s||u);if(!Pt(s)||f)if("e"!==s&&"E"!==s)if("-"!==s&&"+"!==s||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==s||this.curNumberHasExp||this.curNumberHasDecimal||u){if(this.curNumber&&-1!==this.curCommandType){var p=Number(this.curNumber);if(isNaN(p))throw new SyntaxError("Invalid number ending at "+o);if(this.curCommandType===V.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>p)throw new SyntaxError('Expected positive number, got "'+p+'" at index "'+o+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+o+'"');this.curArgs.push(p),this.curArgs.length===jt[this.curCommandType]&&(V.HORIZ_LINE_TO===this.curCommandType?i({type:V.HORIZ_LINE_TO,relative:this.curCommandRelative,x:p}):V.VERT_LINE_TO===this.curCommandType?i({type:V.VERT_LINE_TO,relative:this.curCommandRelative,y:p}):this.curCommandType===V.MOVE_TO||this.curCommandType===V.LINE_TO||this.curCommandType===V.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),V.MOVE_TO===this.curCommandType&&(this.curCommandType=V.LINE_TO)):this.curCommandType===V.CURVE_TO?i({type:V.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===V.SMOOTH_CURVE_TO?i({type:V.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===V.QUAD_TO?i({type:V.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===V.ARC&&i({type:V.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!bt(s))if(","===s&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==s&&"-"!==s&&"."!==s)if(f)this.curNumber=s,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+o+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+s+'" at index '+o+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==s&&"Z"!==s)if("h"===s||"H"===s)this.curCommandType=V.HORIZ_LINE_TO,this.curCommandRelative="h"===s;else if("v"===s||"V"===s)this.curCommandType=V.VERT_LINE_TO,this.curCommandRelative="v"===s;else if("m"===s||"M"===s)this.curCommandType=V.MOVE_TO,this.curCommandRelative="m"===s;else if("l"===s||"L"===s)this.curCommandType=V.LINE_TO,this.curCommandRelative="l"===s;else if("c"===s||"C"===s)this.curCommandType=V.CURVE_TO,this.curCommandRelative="c"===s;else if("s"===s||"S"===s)this.curCommandType=V.SMOOTH_CURVE_TO,this.curCommandRelative="s"===s;else if("q"===s||"Q"===s)this.curCommandType=V.QUAD_TO,this.curCommandRelative="q"===s;else if("t"===s||"T"===s)this.curCommandType=V.SMOOTH_QUAD_TO,this.curCommandRelative="t"===s;else{if("a"!==s&&"A"!==s)throw new SyntaxError('Unexpected character "'+s+'" at index '+o+".");this.curCommandType=V.ARC,this.curCommandRelative="a"===s}else r.push({type:V.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=s,this.curNumberHasDecimal="."===s}else this.curNumber+=s,this.curNumberHasDecimal=!0;else this.curNumber+=s;else this.curNumber+=s,this.curNumberHasExp=!0;else this.curNumber+=s,this.curNumberHasExpDigits=this.curNumberHasExp}return r},t.prototype.transform=function(e){return Object.create(this,{parse:{value:function(r,a){void 0===a&&(a=[]);for(var i=0,o=Object.getPrototypeOf(this).parse.call(this,r);i<o.length;i++){var u=e(o[i]);Array.isArray(u)?a.push.apply(a,u):a.push(u)}return a}}})},t}(Ot),V=function(l){function t(e){var r=l.call(this)||this;return r.commands="string"==typeof e?t.parse(e):e,r}return nt(t,l),t.prototype.encode=function(){return t.encode(this.commands)},t.prototype.getBounds=function(){var e=_.CALCULATE_BOUNDS();return this.transform(e),e},t.prototype.transform=function(e){for(var r=[],a=0,i=this.commands;a<i.length;a++){var o=e(i[a]);Array.isArray(o)?r.push.apply(r,o):r.push(o)}return this.commands=r,this},t.encode=function(e){return function J(l){var t="";Array.isArray(l)||(l=[l]);for(var e=0;e<l.length;e++){var r=l[e];if(r.type===V.CLOSE_PATH)t+="z";else if(r.type===V.HORIZ_LINE_TO)t+=(r.relative?"h":"H")+r.x;else if(r.type===V.VERT_LINE_TO)t+=(r.relative?"v":"V")+r.y;else if(r.type===V.MOVE_TO)t+=(r.relative?"m":"M")+r.x+" "+r.y;else if(r.type===V.LINE_TO)t+=(r.relative?"l":"L")+r.x+" "+r.y;else if(r.type===V.CURVE_TO)t+=(r.relative?"c":"C")+r.x1+" "+r.y1+" "+r.x2+" "+r.y2+" "+r.x+" "+r.y;else if(r.type===V.SMOOTH_CURVE_TO)t+=(r.relative?"s":"S")+r.x2+" "+r.y2+" "+r.x+" "+r.y;else if(r.type===V.QUAD_TO)t+=(r.relative?"q":"Q")+r.x1+" "+r.y1+" "+r.x+" "+r.y;else if(r.type===V.SMOOTH_QUAD_TO)t+=(r.relative?"t":"T")+r.x+" "+r.y;else{if(r.type!==V.ARC)throw new Error('Unexpected command type "'+r.type+'" at index '+e+".");t+=(r.relative?"a":"A")+r.rX+" "+r.rY+" "+r.xRot+" "+ +r.lArcFlag+" "+ +r.sweepFlag+" "+r.x+" "+r.y}}return t}(e)},t.parse=function(e){var r=new Ft,a=[];return r.parse(e,a),r.finish(a),a},t.CLOSE_PATH=1,t.MOVE_TO=2,t.HORIZ_LINE_TO=4,t.VERT_LINE_TO=8,t.LINE_TO=16,t.CURVE_TO=32,t.SMOOTH_CURVE_TO=64,t.QUAD_TO=128,t.SMOOTH_QUAD_TO=256,t.ARC=512,t.LINE_COMMANDS=t.LINE_TO|t.HORIZ_LINE_TO|t.VERT_LINE_TO,t.DRAWING_COMMANDS=t.HORIZ_LINE_TO|t.VERT_LINE_TO|t.LINE_TO|t.CURVE_TO|t.SMOOTH_CURVE_TO|t.QUAD_TO|t.SMOOTH_QUAD_TO|t.ARC,t}(Ot),jt=((it={})[V.MOVE_TO]=2,it[V.LINE_TO]=2,it[V.HORIZ_LINE_TO]=1,it[V.VERT_LINE_TO]=1,it[V.CLOSE_PATH]=0,it[V.QUAD_TO]=4,it[V.SMOOTH_QUAD_TO]=2,it[V.CURVE_TO]=6,it[V.SMOOTH_CURVE_TO]=4,it[V.ARC]=7,it);function kt(l){return(kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(l)}n(2826);var Zt=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],ee=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function ne(l,t,e,r,a,i){if(!(isNaN(i)||i<1)){i|=0;var o=function re(l,t,e,r,a){if("string"==typeof l&&(l=document.getElementById(l)),!l||"object"!==kt(l)||!("getContext"in l))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var i=l.getContext("2d");try{return i.getImageData(t,e,r,a)}catch(o){throw new Error("unable to access image data: "+o)}}(l,t,e,r,a);o=function ue(l,t,e,r,a,i){for(var N,o=l.data,s=2*i+1,u=r-1,f=a-1,p=i+1,E=p*(p+1)/2,T=new Ne,m=T,D=1;D<s;D++)m=m.next=new Ne,D===p&&(N=m);m.next=T;for(var L=null,U=null,K=0,z=0,j=Zt[i],q=ee[i],ot=0;ot<a;ot++){m=T;for(var W=o[z],ft=o[z+1],vt=o[z+2],yt=o[z+3],Et=0;Et<p;Et++)m.r=W,m.g=ft,m.b=vt,m.a=yt,m=m.next;for(var Ct=0,Vt=0,Dt=0,At=0,Mt=p*W,It=p*ft,Ut=p*vt,Tt=p*yt,dt=E*W,Lt=E*ft,zt=E*vt,Ht=E*yt,Gt=1;Gt<p;Gt++){var Yt=z+((u<Gt?u:Gt)<<2),Kt=o[Yt],Jt=o[Yt+1],Ve=o[Yt+2],De=o[Yt+3],ge=p-Gt;dt+=(m.r=Kt)*ge,Lt+=(m.g=Jt)*ge,zt+=(m.b=Ve)*ge,Ht+=(m.a=De)*ge,Ct+=Kt,Vt+=Jt,Dt+=Ve,At+=De,m=m.next}L=T,U=N;for(var de=0;de<r;de++){var pe=Ht*j>>q;if(o[z+3]=pe,0!==pe){var ye=255/pe;o[z]=(dt*j>>q)*ye,o[z+1]=(Lt*j>>q)*ye,o[z+2]=(zt*j>>q)*ye}else o[z]=o[z+1]=o[z+2]=0;dt-=Mt,Lt-=It,zt-=Ut,Ht-=Tt,Mt-=L.r,It-=L.g,Ut-=L.b,Tt-=L.a;var qt=de+i+1;dt+=Ct+=L.r=o[qt=K+(qt<u?qt:u)<<2],Lt+=Vt+=L.g=o[qt+1],zt+=Dt+=L.b=o[qt+2],Ht+=At+=L.a=o[qt+3],L=L.next;var Le=U.r,Be=U.g,Ce=U.b,Ue=U.a;Mt+=Le,It+=Be,Ut+=Ce,Tt+=Ue,Ct-=Le,Vt-=Be,Dt-=Ce,At-=Ue,U=U.next,z+=4}K+=r}for(var _t=0;_t<r;_t++){var Ae=o[z=_t<<2],Pe=o[z+1],Re=o[z+2],Xt=o[z+3],br=p*Ae,Sr=p*Pe,Cr=p*Re,Ar=p*Xt,Je=E*Ae,qe=E*Pe,_e=E*Re,tr=E*Xt;m=T;for(var Pn=0;Pn<p;Pn++)m.r=Ae,m.g=Pe,m.b=Re,m.a=Xt,m=m.next;for(var Rn=r,Pr=0,Rr=0,Nr=0,Ir=0,er=1;er<=i;er++){var rr=p-er;Je+=(m.r=Ae=o[z=Rn+_t<<2])*rr,qe+=(m.g=Pe=o[z+1])*rr,_e+=(m.b=Re=o[z+2])*rr,tr+=(m.a=Xt=o[z+3])*rr,Ir+=Ae,Pr+=Pe,Rr+=Re,Nr+=Xt,m=m.next,er<f&&(Rn+=r)}z=_t,L=T,U=N;for(var Mr=0;Mr<a;Mr++){var Wt=z<<2;o[Wt+3]=Xt=tr*j>>q,Xt>0?(o[Wt]=(Je*j>>q)*(Xt=255/Xt),o[Wt+1]=(qe*j>>q)*Xt,o[Wt+2]=(_e*j>>q)*Xt):o[Wt]=o[Wt+1]=o[Wt+2]=0,Je-=br,qe-=Sr,_e-=Cr,tr-=Ar,br-=L.r,Sr-=L.g,Cr-=L.b,Ar-=L.a,Wt=_t+((Wt=Mr+p)<f?Wt:f)*r<<2,Je+=Ir+=L.r=o[Wt],qe+=Pr+=L.g=o[Wt+1],_e+=Rr+=L.b=o[Wt+2],tr+=Nr+=L.a=o[Wt+3],L=L.next,br+=Ae=U.r,Sr+=Pe=U.g,Cr+=Re=U.b,Ar+=Xt=U.a,Ir-=Ae,Pr-=Pe,Rr-=Re,Nr-=Xt,U=U.next,z+=r}}return l}(o,0,0,r,a,i),l.getContext("2d").putImageData(o,t,e)}}var Ne=function l(){(function te(l,t){if(!(l instanceof t))throw new TypeError("Cannot call a class as a function")})(this,l),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null},he=Object.freeze({__proto__:null,offscreen:function ze(){var{DOMParser:l}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:l,createCanvas:(e,r)=>new OffscreenCanvas(e,r),createImage:e=>c(function*(){var r=yield fetch(e),a=yield r.blob();return yield createImageBitmap(a)})()};return(typeof DOMParser<"u"||typeof l>"u")&&Reflect.deleteProperty(t,"DOMParser"),t},node:function nr(l){var{DOMParser:t,canvas:e,fetch:r}=l;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,fetch:r,createCanvas:e.createCanvas,createImage:e.loadImage}}});function $t(l){return l.replace(/(?!\u3000)\s+/gm," ")}function Ie(l){return l.replace(/^[\n \t]+/,"")}function X(l){return l.replace(/[\n \t]+$/,"")}function Z(l){return((l||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var pt=/^[A-Z-]+$/;function gt(l){return pt.test(l)?l.toLowerCase():l}function St(l){var t=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(l)||[];return t[2]||t[3]||t[4]}function ae(l){if(!l.startsWith("rgb"))return l;var t=3;return l.replace(/\d+(\.\d+)?/g,(r,a)=>t--&&a?String(Math.round(parseFloat(r))):r)}var Me=/(\[[^\]]+\])/g,fe=/(#[^\s+>~.[:]+)/g,ve=/(\.[^\s+>~.[:]+)/g,Ge=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,$e=/(:[\w-]+\([^)]*\))/gi,ar=/(:[^\s+>~.[:]+)/g,Nn=/([^\s+>~.[:]+)/g;function Ee(l,t){var e=t.exec(l);return e?[l.replace(t," "),e.length]:[l,0]}function wr(l){var t=[0,0,0],e=l.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),r=0;return[e,r]=Ee(e,Me),t[1]+=r,[e,r]=Ee(e,fe),t[0]+=r,[e,r]=Ee(e,ve),t[1]+=r,[e,r]=Ee(e,Ge),t[2]+=r,[e,r]=Ee(e,$e),t[1]+=r,[e,r]=Ee(e,ar),t[1]+=r,e=e.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[e,r]=Ee(e,Nn),t[2]+=r,t.join("")}var Te=1e-8;function ir(l){return Math.sqrt(Math.pow(l[0],2)+Math.pow(l[1],2))}function He(l,t){return(l[0]*t[0]+l[1]*t[1])/(ir(l)*ir(t))}function sr(l,t){return(l[0]*t[1]<l[1]*t[0]?-1:1)*Math.acos(He(l,t))}function or(l){return l*l*l}function ur(l){return 3*l*l*(1-l)}function lr(l){return 3*l*(1-l)*(1-l)}function hr(l){return(1-l)*(1-l)*(1-l)}function fr(l){return l*l}function vr(l){return 2*l*(1-l)}function cr(l){return(1-l)*(1-l)}let tt=(()=>{class l{constructor(e,r,a){this.document=e,this.name=r,this.value=a,this.isNormalizedColor=!1}static empty(e){return new l(e,"EMPTY","")}split(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:r,name:a}=this;return $t(this.getString()).trim().split(e).map(i=>new l(r,a,i))}hasValue(e){var{value:r}=this;return null!==r&&""!==r&&(e||0!==r)&&typeof r<"u"}isString(e){var{value:r}=this,a="string"==typeof r;return a&&e?e.test(r):a}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var e=this.getString();switch(!0){case e.endsWith("px"):case/^[0-9]+$/.test(e):return!0;default:return!1}}setValue(e){return this.value=e,this}getValue(e){return typeof e>"u"||this.hasValue()?this.value:e}getNumber(e){if(!this.hasValue())return typeof e>"u"?0:parseFloat(e);var{value:r}=this,a=parseFloat(r);return this.isString(/%$/)&&(a/=100),a}getString(e){return typeof e>"u"||this.hasValue()?typeof this.value>"u"?"":String(this.value):String(e)}getColor(e){var r=this.getString(e);return this.isNormalizedColor||(this.isNormalizedColor=!0,r=ae(r),this.value=r),r}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[a,i]="boolean"==typeof e?[void 0,e]:[e],{viewPort:o}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(o.computeSize("x"),o.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(o.computeSize("x"),o.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*o.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*o.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*o.computeSize(a);default:var s=this.getNumber();return r&&s<1?s*o.computeSize(a):s}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var e=this.getString(),r=/#([^)'"]+)/.exec(e);return r&&(r=r[1]),r||(r=e),this.document.definitions[r]}getFillStyleDefinition(e,r){var a=this.getDefinition();if(!a)return null;if("function"==typeof a.createGradient)return a.createGradient(this.document.ctx,e,r);if("function"==typeof a.createPattern){if(a.getHrefAttribute().hasValue()){var i=a.getAttribute("patternTransform");a=a.getHrefAttribute().getDefinition(),i.hasValue()&&a.getAttribute("patternTransform",!0).setValue(i.value)}return a.createPattern(this.document.ctx,e,r)}return null}getTextBaseline(){return this.hasValue()?l.textBaselineMapping[this.getString()]:null}addOpacity(e){for(var r=this.getColor(),a=r.length,i=0,o=0;o<a&&(","===r[o]&&i++,3!==i);o++);if(e.hasValue()&&this.isString()&&3!==i){var s=new $(r);s.ok&&(s.alpha=e.getNumber(),r=s.toRGBA())}return new l(this.document,this.name,r)}}return l.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"},l})();class Vr{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class Rt{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,a=e]=Z(t);return new Rt(r,a)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,a=r]=Z(t);return new Rt(r,a)}static parsePath(t){for(var e=Z(t),r=e.length,a=[],i=0;i<r;i+=2)a.push(new Rt(e[i],e[i+1]));return a}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[1]+r*t[3]+t[5];this.x=e*t[0]+r*t[2]+t[4],this.y=i}}class Dr{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,a=t.ctx.canvas;a.onclick=e,a.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:a}=t.ctx.canvas;a&&(a.cursor=""),e.forEach((i,o)=>{for(var{run:s}=i,u=r[o];u;)s(u),u=u.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:a}=this;r.forEach((i,o)=>{var{x:s,y:u}=i;!a[o]&&e.isPointInPath&&e.isPointInPath(s,u)&&(a[o]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:a}=this;r.forEach((i,o)=>{var{x:s,y:u}=i;!a[o]&&e.isPointInBox(s,u)&&(a[o]=t)})}}mapXY(t,e){for(var{window:r,ctx:a}=this.screen,i=new Rt(t,e),o=a.canvas;o;)i.x-=o.offsetLeft,i.y-=o.offsetTop,o=o.offsetParent;return r.scrollX&&(i.x+=r.scrollX),r.scrollY&&(i.y+=r.scrollY),i}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(a){a.onClick&&a.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(a){a.onMouseMove&&a.onMouseMove()}})}}var Lr=typeof window<"u"?window:null,Br=typeof fetch<"u"?fetch.bind(void 0):null;let gr=(()=>{class l{constructor(e){var{fetch:r=Br,window:a=Lr}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=e,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new Vr,this.mouse=new Dr(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=a,this.fetch=r}wait(e){this.waits.push(e)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var e=this.waits.every(r=>r());return e&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=e,e}setDefaults(e){e.strokeStyle="rgba(0,0,0,0)",e.lineCap="butt",e.lineJoin="miter",e.miterLimit=4}setViewBox(e){var{document:r,ctx:a,aspectRatio:i,width:o,desiredWidth:s,height:u,desiredHeight:f,minX:p=0,minY:E=0,refX:T,refY:m,clip:N=!1,clipX:D=0,clipY:L=0}=e,U=$t(i).replace(/^defer\s/,""),[K,z]=U.split(" "),j=K||"xMidYMid",q=z||"meet",ot=o/s,W=u/f,ft=Math.min(ot,W),vt=Math.max(ot,W),yt=s,Et=f;"meet"===q&&(yt*=ft,Et*=ft),"slice"===q&&(yt*=vt,Et*=vt);var Ct=new tt(r,"refX",T),Vt=new tt(r,"refY",m),Dt=Ct.hasValue()&&Vt.hasValue();if(Dt&&a.translate(-ft*Ct.getPixels("x"),-ft*Vt.getPixels("y")),N){var At=ft*D,Mt=ft*L;a.beginPath(),a.moveTo(At,Mt),a.lineTo(o,Mt),a.lineTo(o,u),a.lineTo(At,u),a.closePath(),a.clip()}if(!Dt){var It="meet"===q&&ft===W,Ut="slice"===q&&vt===W,Tt="meet"===q&&ft===ot,dt="slice"===q&&vt===ot;j.startsWith("xMid")&&(It||Ut)&&a.translate(o/2-yt/2,0),j.endsWith("YMid")&&(Tt||dt)&&a.translate(0,u/2-Et/2),j.startsWith("xMax")&&(It||Ut)&&a.translate(o-yt,0),j.endsWith("YMax")&&(Tt||dt)&&a.translate(0,u-Et)}switch(!0){case"none"===j:a.scale(ot,W);break;case"meet"===q:a.scale(ft,ft);break;case"slice"===q:a.scale(vt,vt)}a.translate(-p,-E)}start(e){var{enableRedraw:r=!1,ignoreMouse:a=!1,ignoreAnimation:i=!1,ignoreDimensions:o=!1,ignoreClear:s=!1,forceRedraw:u,scaleWidth:f,scaleHeight:p,offsetX:E,offsetY:T}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:m,mouse:N}=this,D=1e3/m;if(this.frameDuration=D,this.readyPromise=new Promise(j=>{this.resolveReady=j}),this.isReady()&&this.render(e,o,s,f,p,E,T),r){var L=Date.now(),U=L,K=0,z=()=>{L=Date.now(),(K=L-U)>=D&&(U=L-K%D,this.shouldUpdate(i,u)&&(this.render(e,o,s,f,p,E,T),N.runEvents())),this.intervalId=B(z)};a||N.start(),this.intervalId=B(z)}}stop(){this.intervalId&&(B.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(e,r){if(!e){var{frameDuration:a}=this;if(this.animations.reduce((o,s)=>s.update(a)||o,!1))return!0}return!!("function"==typeof r&&r()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(e,r,a,i,o,s,u){var{CLIENT_WIDTH:f,CLIENT_HEIGHT:p,viewPort:E,ctx:T,isFirstRender:m}=this,N=T.canvas;E.clear(),N.width&&N.height?E.setCurrent(N.width,N.height):E.setCurrent(f,p);var D=e.getStyle("width"),L=e.getStyle("height");!r&&(m||"number"!=typeof i&&"number"!=typeof o)&&(D.hasValue()&&(N.width=D.getPixels("x"),N.style&&(N.style.width="".concat(N.width,"px"))),L.hasValue()&&(N.height=L.getPixels("y"),N.style&&(N.style.height="".concat(N.height,"px"))));var U=N.clientWidth||N.width,K=N.clientHeight||N.height;if(r&&D.hasValue()&&L.hasValue()&&(U=D.getPixels("x"),K=L.getPixels("y")),E.setCurrent(U,K),"number"==typeof s&&e.getAttribute("x",!0).setValue(s),"number"==typeof u&&e.getAttribute("y",!0).setValue(u),"number"==typeof i||"number"==typeof o){var z=Z(e.getAttribute("viewBox").getString()),j=0,q=0;if("number"==typeof i){var ot=e.getStyle("width");ot.hasValue()?j=ot.getPixels("x")/i:isNaN(z[2])||(j=z[2]/i)}if("number"==typeof o){var W=e.getStyle("height");W.hasValue()?q=W.getPixels("y")/o:isNaN(z[3])||(q=z[3]/o)}j||(j=q),q||(q=j),e.getAttribute("width",!0).setValue(i),e.getAttribute("height",!0).setValue(o);var ft=e.getStyle("transform",!0,!0);ft.setValue("".concat(ft.getString()," scale(").concat(1/j,", ").concat(1/q,")"))}a||T.clearRect(0,0,U,K),e.render(T),m&&(this.isFirstRender=!1)}}return l.defaultWindow=Lr,l.defaultFetch=Br,l})();var{defaultFetch:In}=gr,Mn=typeof DOMParser<"u"?DOMParser:null;class Ye{constructor(){var{fetch:t=In,DOMParser:e=Mn}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return c(function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)})()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch{return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return c(function*(){var r=yield e.fetch(t),a=yield r.text();return e.parseFromString(a)})()}}class Fr{constructor(t,e){this.type="translate",this.point=null,this.point=Rt.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class Ur{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var a=Z(e);this.angle=new tt(t,"angle",a[0]),this.originX=r[0],this.originY=r[1],this.cx=a[1]||0,this.cy=a[2]||0}apply(t){var{cx:e,cy:r,originX:a,originY:i,angle:o}=this,s=e+a.getPixels("x"),u=r+i.getPixels("y");t.translate(s,u),t.rotate(o.getRadians()),t.translate(-s,-u)}unapply(t){var{cx:e,cy:r,originX:a,originY:i,angle:o}=this,s=e+a.getPixels("x"),u=r+i.getPixels("y");t.translate(s,u),t.rotate(-1*o.getRadians()),t.translate(-s,-u)}applyToPoint(t){var{cx:e,cy:r,angle:a}=this,i=a.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(i),Math.sin(i),-Math.sin(i),Math.cos(i),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class jr{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var a=Rt.parseScale(e);(0===a.x||0===a.y)&&(a.x=Te,a.y=Te),this.scale=a,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:a,originY:i}=this,o=a.getPixels("x"),s=i.getPixels("y");t.translate(o,s),t.scale(e,r||e),t.translate(-o,-s)}unapply(t){var{scale:{x:e,y:r},originX:a,originY:i}=this,o=a.getPixels("x"),s=i.getPixels("y");t.translate(o,s),t.scale(1/e,1/r||e),t.translate(-o,-s)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class dr{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=Z(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:a}=this,i=e.getPixels("x"),o=r.getPixels("y");t.translate(i,o),t.transform(a[0],a[1],a[2],a[3],a[4],a[5]),t.translate(-i,-o)}unapply(t){var{originX:e,originY:r,matrix:a}=this,i=a[0],o=a[2],s=a[4],u=a[1],f=a[3],p=a[5],N=1/(i*(1*f-0*p)-o*(1*u-0*p)+s*(0*u-0*f)),D=e.getPixels("x"),L=r.getPixels("y");t.translate(D,L),t.transform(N*(1*f-0*p),N*(0*p-1*u),N*(0*s-1*o),N*(1*i-0*s),N*(o*p-s*f),N*(s*u-i*p)),t.translate(-D,-L)}applyToPoint(t){t.applyTransform(this.matrix)}}class pr extends dr{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new tt(t,"angle",e)}}class zr extends pr{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class Gr extends pr{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}let Xe=(()=>{class l{constructor(e,r,a){this.document=e,this.transforms=[];var i=function wn(l){return $t(l).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(r);i.forEach(o=>{if("none"!==o){var[s,u]=function Vn(l){var[t,e]=l.split("(");return[t.trim(),e.trim().replace(")","")]}(o),f=l.transformTypes[s];typeof f<"u"&&this.transforms.push(new f(this.document,u,a))}})}static fromElement(e,r){var a=r.getStyle("transform",!1,!0),[i,o=i]=r.getStyle("transform-origin",!1,!0).split(),s=[i,o];return a.hasValue()?new l(e,a.getString(),s):null}apply(e){for(var{transforms:r}=this,a=r.length,i=0;i<a;i++)r[i].apply(e)}unapply(e){for(var{transforms:r}=this,i=r.length-1;i>=0;i--)r[i].unapply(e)}applyToPoint(e){for(var{transforms:r}=this,a=r.length,i=0;i<a;i++)r[i].applyToPoint(e)}}return l.transformTypes={translate:Fr,rotate:Ur,scale:jr,matrix:dr,skewX:zr,skewY:Gr},l})(),Nt=(()=>{class l{constructor(e,r){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=e,this.node=r,this.captureTextNodes=a,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],r&&1===r.nodeType){Array.from(r.attributes).forEach(u=>{var f=gt(u.nodeName);this.attributes[f]=new tt(e,f,u.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()&&this.getAttribute("style").getString().split(";").map(u=>u.trim()).forEach(u=>{if(u){var[f,p]=u.split(":").map(E=>E.trim());this.styles[f]=new tt(e,f,p)}});var{definitions:o}=e,s=this.getAttribute("id");s.hasValue()&&(o[s.getString()]||(o[s.getString()]=this)),Array.from(r.childNodes).forEach(u=>{if(1===u.nodeType)this.addChild(u);else if(a&&(3===u.nodeType||4===u.nodeType)){var f=e.createTextNode(u);f.getText().length>0&&this.addChild(f)}})}}getAttribute(e){var a=this.attributes[e];if(!a&&arguments.length>1&&void 0!==arguments[1]&&arguments[1]){var i=new tt(this.document,e,"");return this.attributes[e]=i,i}return a||tt.empty(this.document)}getHrefAttribute(){for(var e in this.attributes)if("href"===e||e.endsWith(":href"))return this.attributes[e];return tt.empty(this.document)}getStyle(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[e];if(i)return i;var o=this.getAttribute(e);if(null!=o&&o.hasValue())return this.styles[e]=o,o;if(!a){var{parent:s}=this;if(s){var u=s.getStyle(e);if(null!=u&&u.hasValue())return u}}if(r){var f=new tt(this.document,e,"");return this.styles[e]=f,f}return i||tt.empty(this.document)}render(e){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(e.save(),this.getStyle("mask").hasValue()){var r=this.getStyle("mask").getDefinition();r&&(this.applyEffects(e),r.apply(e,this))}else if("none"!==this.getStyle("filter").getValue("none")){var a=this.getStyle("filter").getDefinition();a&&(this.applyEffects(e),a.apply(e,this))}else this.setContext(e),this.renderChildren(e),this.clearContext(e);e.restore()}}setContext(e){}applyEffects(e){var r=Xe.fromElement(this.document,this);r&&r.apply(e);var a=this.getStyle("clip-path",!1,!0);if(a.hasValue()){var i=a.getDefinition();i&&i.apply(e)}}clearContext(e){}renderChildren(e){this.children.forEach(r=>{r.render(e)})}addChild(e){var r=e instanceof l?e:this.document.createElement(e);r.parent=this,l.ignoreChildTypes.includes(r.type)||this.children.push(r)}matchesSelector(e){var r,{node:a}=this;if("function"==typeof a.matches)return a.matches(e);var i=null===(r=a.getAttribute)||void 0===r?void 0:r.call(a,"class");return!(!i||""===i)&&i.split(" ").some(o=>".".concat(o)===e)}addStylesFromStyleDefinition(){var{styles:e,stylesSpecificity:r}=this.document;for(var a in e)if(!a.startsWith("@")&&this.matchesSelector(a)){var i=e[a],o=r[a];if(i)for(var s in i){var u=this.stylesSpecificity[s];typeof u>"u"&&(u="000"),o>=u&&(this.styles[s]=i[s],this.stylesSpecificity[s]=o)}}}removeStyles(e,r){return r.reduce((i,o)=>{var s=e.getStyle(o);if(!s.hasValue())return i;var u=s.getString();return s.setValue(""),[...i,[o,u]]},[])}restoreStyles(e,r){r.forEach(a=>{var[i,o]=a;e.getStyle(i,!0).setValue(o)})}isFirstChild(){var e;return 0===(null===(e=this.parent)||void 0===e?void 0:e.children.indexOf(this))}}return l.ignoreChildTypes=["title"],l})();class $r extends Nt{constructor(t,e,r){super(t,e,r)}}function Dn(l){var t=l.trim();return/^('|")/.test(t)?t:'"'.concat(t,'"')}function Ln(l){return typeof process>"u"?l:l.trim().split(",").map(Dn).join(",")}function Bn(l){if(!l)return"";var t=l.trim().toLowerCase();switch(t){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return t;default:return/^oblique\s+(-|)\d+deg$/.test(t)?t:""}}function Fn(l){if(!l)return"";var t=l.trim().toLowerCase();switch(t){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return t;default:return/^[\d.]+$/.test(t)?t:""}}let Oe=(()=>{class l{constructor(e,r,a,i,o,s){var u=s?"string"==typeof s?l.parse(s):s:{};this.fontFamily=o||u.fontFamily,this.fontSize=i||u.fontSize,this.fontStyle=e||u.fontStyle,this.fontWeight=a||u.fontWeight,this.fontVariant=r||u.fontVariant}static parse(){var r=arguments.length>1?arguments[1]:void 0,a="",i="",o="",s="",u="",f=$t(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),p={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return f.forEach(E=>{switch(!0){case!p.fontStyle&&l.styles.includes(E):"inherit"!==E&&(a=E),p.fontStyle=!0;break;case!p.fontVariant&&l.variants.includes(E):"inherit"!==E&&(i=E),p.fontStyle=!0,p.fontVariant=!0;break;case!p.fontWeight&&l.weights.includes(E):"inherit"!==E&&(o=E),p.fontStyle=!0,p.fontVariant=!0,p.fontWeight=!0;break;case!p.fontSize:"inherit"!==E&&([s]=E.split("/")),p.fontStyle=!0,p.fontVariant=!0,p.fontWeight=!0,p.fontSize=!0;break;default:"inherit"!==E&&(u+=E)}}),new l(a,i,o,s,u,r)}toString(){return[Bn(this.fontStyle),this.fontVariant,Fn(this.fontWeight),this.fontSize,Ln(this.fontFamily)].join(" ").trim()}}return l.styles="normal|italic|oblique|inherit",l.variants="normal|small-caps|inherit",l.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit",l})();class Qt{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=a,this.addPoint(t,e),this.addPoint(r,a)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){typeof t<"u"&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),typeof e<"u"&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:a,y2:i}=t;this.addPoint(e,r),this.addPoint(a,i)}}sumCubic(t,e,r,a,i){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*a+Math.pow(t,3)*i}bezierCurveAdd(t,e,r,a,i){var o=6*e-12*r+6*a,s=-3*e+9*r-9*a+3*i,u=3*r-3*e;if(0!==s){var p=Math.pow(o,2)-4*u*s;if(!(p<0)){var E=(-o+Math.sqrt(p))/(2*s);0<E&&E<1&&(t?this.addX(this.sumCubic(E,e,r,a,i)):this.addY(this.sumCubic(E,e,r,a,i)));var T=(-o-Math.sqrt(p))/(2*s);0<T&&T<1&&(t?this.addX(this.sumCubic(T,e,r,a,i)):this.addY(this.sumCubic(T,e,r,a,i)))}}else{if(0===o)return;var f=-u/o;0<f&&f<1&&(t?this.addX(this.sumCubic(f,e,r,a,i)):this.addY(this.sumCubic(f,e,r,a,i)))}}addBezierCurve(t,e,r,a,i,o,s,u){this.addPoint(t,e),this.addPoint(s,u),this.bezierCurveAdd(!0,t,r,i,s),this.bezierCurveAdd(!1,e,a,o,u)}addQuadraticCurve(t,e,r,a,i,o){var s=t+.6666666666666666*(r-t),u=e+2/3*(a-e);this.addBezierCurve(t,e,s,s+1/3*(i-t),u,u+1/3*(o-e),i,o)}isPointInBox(t,e){var{x1:r,y1:a,x2:i,y2:o}=this;return r<=t&&t<=i&&a<=e&&e<=o}}class st extends V{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new Rt(0,0),this.control=new Rt(0,0),this.current=new Rt(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var r=new Rt(this.command[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x"],this.command[arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y"]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==V.CURVE_TO&&t!==V.SMOOTH_CURVE_TO&&t!==V.QUAD_TO&&t!==V.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:a,y:i}}=this;return new Rt(2*e-a,2*r-i)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:a,angles:i}=this;r&&i.length>0&&!i[i.length-1]&&(i[i.length-1]=a[a.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var a=r+1;a<e;a++)if(t[a]){t[r]=t[a];break}return t}}class ce extends Nt{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),a=this.getStyle("fill-opacity"),i=this.getStyle("stroke"),o=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,a);s&&(t.fillStyle=s)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var u=r.getColor();"inherit"!==u&&(t.fillStyle="none"===u?"rgba(0,0,0,0)":u)}if(a.hasValue()){var f=new tt(this.document,"fill",t.fillStyle).addOpacity(a).getColor();t.fillStyle=f}if(i.isUrlDefinition()){var p=i.getFillStyleDefinition(this,o);p&&(t.strokeStyle=p)}else if(i.hasValue()){"currentColor"===i.getString()&&i.setValue(this.getStyle("color").getColor());var E=i.getString();"inherit"!==E&&(t.strokeStyle="none"===E?"rgba(0,0,0,0)":E)}if(o.hasValue()){var T=new tt(this.document,"stroke",t.strokeStyle).addOpacity(o).getString();t.strokeStyle=T}var m=this.getStyle("stroke-width");if(m.hasValue()){var N=m.getPixels();t.lineWidth=N||Te}var D=this.getStyle("stroke-linecap"),L=this.getStyle("stroke-linejoin"),U=this.getStyle("stroke-miterlimit"),K=this.getStyle("stroke-dasharray"),z=this.getStyle("stroke-dashoffset");if(D.hasValue()&&(t.lineCap=D.getString()),L.hasValue()&&(t.lineJoin=L.getString()),U.hasValue()&&(t.miterLimit=U.getNumber()),K.hasValue()&&"none"!==K.getString()){var j=Z(K.getString());typeof t.setLineDash<"u"?t.setLineDash(j):typeof t.webkitLineDash<"u"?t.webkitLineDash=j:typeof t.mozDash<"u"&&(1!==j.length||0!==j[0])&&(t.mozDash=j);var q=z.getPixels();typeof t.lineDashOffset<"u"?t.lineDashOffset=q:typeof t.webkitLineDashOffset<"u"?t.webkitLineDashOffset=q:typeof t.mozDashOffset<"u"&&(t.mozDashOffset=q)}}if(this.modifiedEmSizeStack=!1,typeof t.font<"u"){var ot=this.getStyle("font"),W=this.getStyle("font-style"),ft=this.getStyle("font-variant"),vt=this.getStyle("font-weight"),yt=this.getStyle("font-size"),Et=this.getStyle("font-family"),Ct=new Oe(W.getString(),ft.getString(),vt.getString(),yt.hasValue()?"".concat(yt.getPixels(!0),"px"):"",Et.getString(),Oe.parse(ot.getString(),t.font));W.setValue(Ct.fontStyle),ft.setValue(Ct.fontVariant),vt.setValue(Ct.fontWeight),yt.setValue(Ct.fontSize),Et.setValue(Ct.fontFamily),t.font=Ct.toString(),yt.isPixels()&&(this.document.emSize=yt.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class xt extends ce{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new st(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new Qt;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case st.MOVE_TO:this.pathM(t,r);break;case st.LINE_TO:this.pathL(t,r);break;case st.HORIZ_LINE_TO:this.pathH(t,r);break;case st.VERT_LINE_TO:this.pathV(t,r);break;case st.CURVE_TO:this.pathC(t,r);break;case st.SMOOTH_CURVE_TO:this.pathS(t,r);break;case st.QUAD_TO:this.pathQ(t,r);break;case st.SMOOTH_QUAD_TO:this.pathT(t,r);break;case st.ARC:this.pathA(t,r);break;case st.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map((i,o)=>[i,r[o]])}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var a=r.length-1,i=this.getStyle("marker-start"),o=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(i.isUrlDefinition()){var u=i.getDefinition(),[f,p]=r[0];u.render(t,f,p)}if(o.isUrlDefinition())for(var E=o.getDefinition(),T=1;T<a;T++){var[m,N]=r[T];E.render(t,m,N)}if(s.isUrlDefinition()){var D=s.getDefinition(),[L,U]=r[a];D.render(t,L,U)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:a}=xt.pathM(r),{x:i,y:o}=a;r.addMarker(a),e.addPoint(i,o),t&&t.moveTo(i,o)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:a,point:i}=xt.pathL(r),{x:o,y:s}=i;r.addMarker(i,a),e.addPoint(o,s),t&&t.lineTo(o,s)}static pathH(t){var{current:e,command:r}=t,a=new Rt((r.relative?e.x:0)+r.x,e.y);return t.current=a,{current:e,point:a}}pathH(t,e){var{pathParser:r}=this,{current:a,point:i}=xt.pathH(r),{x:o,y:s}=i;r.addMarker(i,a),e.addPoint(o,s),t&&t.lineTo(o,s)}static pathV(t){var{current:e,command:r}=t,a=new Rt(e.x,(r.relative?e.y:0)+r.y);return t.current=a,{current:e,point:a}}pathV(t,e){var{pathParser:r}=this,{current:a,point:i}=xt.pathV(r),{x:o,y:s}=i;r.addMarker(i,a),e.addPoint(o,s),t&&t.lineTo(o,s)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:a,point:i,controlPoint:o,currentPoint:s}=xt.pathC(r);r.addMarker(s,o,i),e.addBezierCurve(a.x,a.y,i.x,i.y,o.x,o.y,s.x,s.y),t&&t.bezierCurveTo(i.x,i.y,o.x,o.y,s.x,s.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:a,point:i,controlPoint:o,currentPoint:s}=xt.pathS(r);r.addMarker(s,o,i),e.addBezierCurve(a.x,a.y,i.x,i.y,o.x,o.y,s.x,s.y),t&&t.bezierCurveTo(i.x,i.y,o.x,o.y,s.x,s.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:a,controlPoint:i,currentPoint:o}=xt.pathQ(r);r.addMarker(o,i,i),e.addQuadraticCurve(a.x,a.y,i.x,i.y,o.x,o.y),t&&t.quadraticCurveTo(i.x,i.y,o.x,o.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:a,controlPoint:i,currentPoint:o}=xt.pathT(r);r.addMarker(o,i,i),e.addQuadraticCurve(a.x,a.y,i.x,i.y,o.x,o.y),t&&t.quadraticCurveTo(i.x,i.y,o.x,o.y)}static pathA(t){var{current:e,command:r}=t,{rX:a,rY:i,xRot:o,lArcFlag:s,sweepFlag:u}=r,f=o*(Math.PI/180),p=t.getAsCurrentPoint(),E=new Rt(Math.cos(f)*(e.x-p.x)/2+Math.sin(f)*(e.y-p.y)/2,-Math.sin(f)*(e.x-p.x)/2+Math.cos(f)*(e.y-p.y)/2),T=Math.pow(E.x,2)/Math.pow(a,2)+Math.pow(E.y,2)/Math.pow(i,2);T>1&&(a*=Math.sqrt(T),i*=Math.sqrt(T));var m=(s===u?-1:1)*Math.sqrt((Math.pow(a,2)*Math.pow(i,2)-Math.pow(a,2)*Math.pow(E.y,2)-Math.pow(i,2)*Math.pow(E.x,2))/(Math.pow(a,2)*Math.pow(E.y,2)+Math.pow(i,2)*Math.pow(E.x,2)));isNaN(m)&&(m=0);var N=new Rt(m*a*E.y/i,m*-i*E.x/a),D=new Rt((e.x+p.x)/2+Math.cos(f)*N.x-Math.sin(f)*N.y,(e.y+p.y)/2+Math.sin(f)*N.x+Math.cos(f)*N.y),L=sr([1,0],[(E.x-N.x)/a,(E.y-N.y)/i]),U=[(E.x-N.x)/a,(E.y-N.y)/i],K=[(-E.x-N.x)/a,(-E.y-N.y)/i],z=sr(U,K);return He(U,K)<=-1&&(z=Math.PI),He(U,K)>=1&&(z=0),{currentPoint:p,rX:a,rY:i,sweepFlag:u,xAxisRotation:f,centp:D,a1:L,ad:z}}pathA(t,e){var{pathParser:r}=this,{currentPoint:a,rX:i,rY:o,sweepFlag:s,xAxisRotation:u,centp:f,a1:p,ad:E}=xt.pathA(r),T=1-s?1:-1,m=p+T*(E/2),N=new Rt(f.x+i*Math.cos(m),f.y+o*Math.sin(m));if(r.addMarkerAngle(N,m-T*Math.PI/2),r.addMarkerAngle(a,m-T*Math.PI),e.addPoint(a.x,a.y),t&&!isNaN(p)&&!isNaN(E)){var D=i>o?i:o,L=i>o?1:i/o,U=i>o?o/i:1;t.translate(f.x,f.y),t.rotate(u),t.scale(L,U),t.arc(0,0,D,p,p+E,Boolean(1-s)),t.scale(1/L,1/U),t.rotate(-u),t.translate(-f.x,-f.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){xt.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class yr extends xt{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class ie extends ce{constructor(t,e,r){super(t,e,new.target===ie||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){super.setContext(t,arguments.length>1&&void 0!==arguments[1]&&arguments[1]);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,a)=>{var i=this.getChildBoundingBox(t,this,this,a);e?e.addBoundingBox(i):e=i}),e}getFontSize(){var{document:t,parent:e}=this,r=Oe.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new Qt(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var a=e[r],i=null;if(t.isArabic){var o=e.length,s=e[r-1],u=e[r+1],f="isolated";if((0===r||" "===s)&&r<o-1&&" "!==u&&(f="terminal"),r>0&&" "!==s&&r<o-1&&" "!==u&&(f="medial"),r>0&&" "!==s&&(r===o-1||" "===u)&&(f="initial"),typeof t.glyphs[a]<"u"){var p=t.glyphs[a];i=p instanceof yr?p:p[f]}}else i=t.glyphs[a];return i||(i=t.missingGlyph),i}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),a=r.indexOf(e),i=r.length-1,o=$t(e.textContent||"");return 0===a&&(o=Ie(o)),a===i&&(o=X(o)),o}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((r,a)=>{this.renderChild(t,this,this,a)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,a=this.getText(),i=r.getStyle("font-family").getDefinition();if(i)for(var{unitsPerEm:o}=i.fontFace,s=Oe.parse(e.ctx.font),u=r.getStyle("font-size").getNumber(s.fontSize),f=r.getStyle("font-style").getString(s.fontStyle),p=u/o,E=i.isRTL?a.split("").reverse().join(""):a,T=Z(r.getAttribute("dx").getString()),m=E.length,N=0;N<m;N++){var D=this.getGlyph(i,E,N);t.translate(this.x,this.y),t.scale(p,-p);var L=t.lineWidth;t.lineWidth=t.lineWidth*o/u,"italic"===f&&t.transform(1,0,.4,1,0,0),D.render(t),"italic"===f&&t.transform(1,0,-.4,1,0,0),t.lineWidth=L,t.scale(1/p,-1/p),t.translate(-this.x,-this.y),this.x+=u*(D.horizAdvX||i.horizAdvX)/o,typeof T[N]<"u"&&!isNaN(T[N])&&(this.x+=T[N])}else{var{x:U,y:K}=this;t.fillStyle&&t.fillText(a,U,K),t.strokeStyle&&t.strokeText(a,U,K)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var a,t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start");a="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=a;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,a){var i=r.children[a];i.children.length>0?i.children.forEach((o,s)=>{e.adjustChildCoordinatesRecursiveCore(t,e,i,s)}):this.adjustChildCoordinates(t,e,r,a)}adjustChildCoordinates(t,e,r,a){var i=r.children[a];if("function"!=typeof i.measureText)return i;t.save(),i.setContext(t,!0);var o=i.getAttribute("x"),s=i.getAttribute("y"),u=i.getAttribute("dx"),f=i.getAttribute("dy"),p=i.getStyle("font-family").getDefinition(),E=Boolean(p)&&p.isRTL;0===a&&(o.hasValue()||o.setValue(i.getInheritedAttribute("x")),s.hasValue()||s.setValue(i.getInheritedAttribute("y")),u.hasValue()||u.setValue(i.getInheritedAttribute("dx")),f.hasValue()||f.setValue(i.getInheritedAttribute("dy")));var T=i.measureText(t);return E&&(e.x-=T),o.hasValue()?(e.applyAnchoring(),i.x=o.getPixels("x"),u.hasValue()&&(i.x+=u.getPixels("x"))):(u.hasValue()&&(e.x+=u.getPixels("x")),i.x=e.x),e.x=i.x,E||(e.x+=T),s.hasValue()?(i.y=s.getPixels("y"),f.hasValue()&&(i.y+=f.getPixels("y"))):(f.hasValue()&&(e.y+=f.getPixels("y")),i.y=e.y),e.y=i.y,e.leafTexts.push(i),e.minX=Math.min(e.minX,i.x,i.x+T),e.maxX=Math.max(e.maxX,i.x,i.x+T),i.clearContext(t),t.restore(),i}getChildBoundingBox(t,e,r,a){var i=r.children[a];if("function"!=typeof i.getBoundingBox)return null;var o=i.getBoundingBox(t);return o?(i.children.forEach((s,u)=>{var f=e.getChildBoundingBox(t,e,i,u);o.addBoundingBox(f)}),o):null}renderChild(t,e,r,a){var i=r.children[a];i.render(t),i.children.forEach((o,s)=>{e.renderChild(t,e,i,s)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),a=this.measureTargetText(t,r);return this.measureCache=a,a}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,a=r.getStyle("font-family").getDefinition();if(a){for(var i=this.getFontSize(),o=a.isRTL?e.split("").reverse().join(""):e,s=Z(r.getAttribute("dx").getString()),u=o.length,f=0,p=0;p<u;p++)f+=(this.getGlyph(a,o,p).horizAdvX||a.horizAdvX)*i/a.fontFace.unitsPerEm,typeof s[p]<"u"&&!isNaN(s[p])&&(f+=s[p]);return f}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:T}=t.measureText(e);return this.clearContext(t),t.restore(),T}getInheritedAttribute(t){for(var e=this;e instanceof ie&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class Fe extends ie{constructor(t,e,r){super(t,e,new.target===Fe||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class Un extends Fe{constructor(){super(...arguments),this.type="textNode"}}class we extends ce{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:a,window:i}=r,o=t.canvas;if(a.setDefaults(t),o.style&&typeof t.font<"u"&&i&&typeof i.getComputedStyle<"u"){t.font=i.getComputedStyle(o).getPropertyValue("font");var s=new tt(r,"fontSize",Oe.parse(t.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:u,height:f}=a.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var p=this.getAttribute("refX"),E=this.getAttribute("refY"),T=this.getAttribute("viewBox"),m=T.hasValue()?Z(T.getString()):null,N=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),D=0,L=0,U=0,K=0;m&&(D=m[0],L=m[1]),this.root||(u=this.getStyle("width").getPixels("x"),f=this.getStyle("height").getPixels("y"),"marker"===this.type&&(U=D,K=L,D=0,L=0)),a.viewPort.setCurrent(u,f),this.node&&(!this.parent||"foreignObject"===(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName))&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),m&&(u=m[2],f=m[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a.viewPort.width,desiredWidth:u,height:a.viewPort.height,desiredHeight:f,minX:D,minY:L,refX:p.getValue(),refY:E.getValue(),clip:N,clipX:U,clipY:K}),m&&(a.viewPort.removeCurrent(),a.viewPort.setCurrent(u,f))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=this.getAttribute("width",!0),i=this.getAttribute("height",!0),o=this.getAttribute("viewBox"),s=this.getAttribute("style"),u=a.getNumber(0),f=i.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var p=this.getAttribute("preserveAspectRatio");p.hasValue()&&p.setValue(p.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(a.setValue(t),i.setValue(e),o.hasValue()||o.setValue("0 0 ".concat(u||t," ").concat(f||e)),s.hasValue()){var E=this.getStyle("width"),T=this.getStyle("height");E.hasValue()&&E.setValue("".concat(t,"px")),T.hasValue()&&T.setValue("".concat(e,"px"))}}}class mr extends xt{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),a=this.getStyle("width",!1,!0).getPixels("x"),i=this.getStyle("height",!1,!0).getPixels("y"),o=this.getAttribute("rx"),s=this.getAttribute("ry"),u=o.getPixels("x"),f=s.getPixels("y");if(o.hasValue()&&!s.hasValue()&&(f=u),s.hasValue()&&!o.hasValue()&&(u=f),u=Math.min(u,a/2),f=Math.min(f,i/2),t){var p=(Math.sqrt(2)-1)/3*4;t.beginPath(),i>0&&a>0&&(t.moveTo(e+u,r),t.lineTo(e+a-u,r),t.bezierCurveTo(e+a-u+p*u,r,e+a,r+f-p*f,e+a,r+f),t.lineTo(e+a,r+i-f),t.bezierCurveTo(e+a,r+i-f+p*f,e+a-u+p*u,r+i,e+a-u,r+i),t.lineTo(e+u,r+i),t.bezierCurveTo(e+u-p*u,r+i,e,r+i-f+p*f,e,r+i-f),t.lineTo(e,r+f),t.bezierCurveTo(e,r+f-p*f,e+u-p*u,r,e+u,r),t.closePath())}return new Qt(e,r,e+a,r+i)}getMarkers(){return null}}class Hr extends xt{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),a=this.getAttribute("r").getPixels();return t&&a>0&&(t.beginPath(),t.arc(e,r,a,0,2*Math.PI,!1),t.closePath()),new Qt(e-a,r-a,e+a,r+a)}getMarkers(){return null}}class Yr extends xt{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),a=this.getAttribute("ry").getPixels("y"),i=this.getAttribute("cx").getPixels("x"),o=this.getAttribute("cy").getPixels("y");return t&&r>0&&a>0&&(t.beginPath(),t.moveTo(i+r,o),t.bezierCurveTo(i+r,o+e*a,i+e*r,o+a,i,o+a),t.bezierCurveTo(i-e*r,o+a,i-r,o+e*a,i-r,o),t.bezierCurveTo(i-r,o-e*a,i-e*r,o-a,i,o-a),t.bezierCurveTo(i+e*r,o-a,i+r,o-e*a,i+r,o),t.closePath()),new Qt(i-r,o-a,i+r,o+a)}getMarkers(){return null}}class Xr extends xt{constructor(){super(...arguments),this.type="line"}getPoints(){return[new Rt(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new Rt(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:a,y:i}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(a,i)),new Qt(e,r,a,i)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class xr extends xt{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=Rt.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:a}]=e,i=new Qt(r,a);return t&&(t.beginPath(),t.moveTo(r,a)),e.forEach(o=>{var{x:s,y:u}=o;i.addPoint(s,u),t&&t.lineTo(s,u)}),i}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((a,i)=>{i!==e&&r.push([a,a.angleTo(t[i+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class Wr extends xr{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:a}]=this.points;return t&&(t.lineTo(r,a),t.closePath()),e}}class Qr extends Nt{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var a=this.getStyle("width").getPixels("x",!0),i=this.getStyle("height").getPixels("y",!0),o=new we(this.document,null);o.attributes.viewBox=new tt(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.width=new tt(this.document,"width","".concat(a,"px")),o.attributes.height=new tt(this.document,"height","".concat(i,"px")),o.attributes.transform=new tt(this.document,"transform",this.getAttribute("patternTransform").getValue()),o.children=this.children;var s=this.document.createCanvas(a,i),u=s.getContext("2d"),f=this.getAttribute("x"),p=this.getAttribute("y");f.hasValue()&&p.hasValue()&&u.translate(f.getPixels("x",!0),p.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var E=-1;E<=1;E++)for(var T=-1;T<=1;T++)u.save(),o.attributes.x=new tt(this.document,"x",E*s.width),o.attributes.y=new tt(this.document,"y",T*s.height),o.render(u),u.restore();return t.createPattern(s,"repeat")}}class Kr extends Nt{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:a,y:i}=e,o=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(a,i),"auto"===o&&t.rotate(r),"strokeWidth"===s&&t.scale(t.lineWidth,t.lineWidth),t.save();var u=new we(this.document,null);u.type=this.type,u.attributes.viewBox=new tt(this.document,"viewBox",this.getAttribute("viewBox").getValue()),u.attributes.refX=new tt(this.document,"refX",this.getAttribute("refX").getValue()),u.attributes.refY=new tt(this.document,"refY",this.getAttribute("refY").getValue()),u.attributes.width=new tt(this.document,"width",this.getAttribute("markerWidth").getValue()),u.attributes.height=new tt(this.document,"height",this.getAttribute("markerHeight").getValue()),u.attributes.overflow=new tt(this.document,"overflow",this.getAttribute("overflow").getValue()),u.attributes.fill=new tt(this.document,"fill",this.getAttribute("fill").getColor("black")),u.attributes.stroke=new tt(this.document,"stroke",this.getAttribute("stroke").getValue("none")),u.children=this.children,u.render(t),t.restore(),"strokeWidth"===s&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===o&&t.rotate(-r),t.translate(-a,-i)}}}class kr extends Nt{constructor(){super(...arguments),this.type="defs"}render(){}}class We extends ce{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new Qt;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}class Er extends Nt{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:a,children:i}=this;i.forEach(o=>{"stop"===o.type&&a.push(o)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var a=this;this.getHrefAttribute().hasValue()&&(a=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(a));var{stops:i}=a,o=this.getGradient(t,e);if(!o)return this.addParentOpacity(r,i[i.length-1].color);if(i.forEach(L=>{o.addColorStop(L.offset,this.addParentOpacity(r,L.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:u,viewPort:f}=s.screen,[p]=f.viewPorts,E=new mr(s,null);E.attributes.x=new tt(s,"x",-u/3),E.attributes.y=new tt(s,"y",-u/3),E.attributes.width=new tt(s,"width",u),E.attributes.height=new tt(s,"height",u);var T=new We(s,null);T.attributes.transform=new tt(s,"transform",this.getAttribute("gradientTransform").getValue()),T.children=[E];var m=new we(s,null);m.attributes.x=new tt(s,"x",0),m.attributes.y=new tt(s,"y",0),m.attributes.width=new tt(s,"width",p.width),m.attributes.height=new tt(s,"height",p.height),m.children=[T];var N=s.createCanvas(p.width,p.height),D=N.getContext("2d");return D.fillStyle=o,m.render(D),D.createPattern(N,"no-repeat")}return o}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new tt(this.document,"color",e).addOpacity(t).getColor():e}}class Zr extends Er{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),a=r?e.getBoundingBox(t):null;if(r&&!a)return null;!this.getAttribute("x1").hasValue()&&!this.getAttribute("y1").hasValue()&&!this.getAttribute("x2").hasValue()&&!this.getAttribute("y2").hasValue()&&(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var i=r?a.x+a.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),o=r?a.y+a.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?a.x+a.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),u=r?a.y+a.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return i===s&&o===u?null:t.createLinearGradient(i,o,s,u)}}class Jr extends Er{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),a=e.getBoundingBox(t);if(r&&!a)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var i=r?a.x+a.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),o=r?a.y+a.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=i,u=o;this.getAttribute("fx").hasValue()&&(s=r?a.x+a.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(u=r?a.y+a.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var f=r?(a.width+a.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),p=this.getAttribute("fr").getPixels();return t.createRadialGradient(s,u,p,i,o,f)}}class qr extends Nt{constructor(t,e,r){super(t,e,r),this.type="stop";var a=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),i=this.getStyle("stop-opacity"),o=this.getStyle("stop-color",!0);""===o.getString()&&o.setValue("#000"),i.hasValue()&&(o=o.addOpacity(i)),this.offset=a,this.color=o.getColor()}}class Qe extends Nt{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new tt(t,"values",null);var a=this.getAttribute("values");a.hasValue()&&this.values.setValue(a.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:a}=this.getProgress(),i=r.getNumber()+(a.getNumber()-r.getNumber())*e;return"%"===t&&(i*=100),"".concat(i).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var a=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==a||this.frozen){if("remove"===a&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var i=!1;if(this.begin<this.duration){var o=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var u=s.getString();o="".concat(u,"(").concat(o,")")}r.setValue(o),i=!0}return i}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var a=r.progress*(e.getValue().length-1),i=Math.floor(a),o=Math.ceil(a);r.from=new tt(t,"from",parseFloat(e.getValue()[i])),r.to=new tt(t,"to",parseFloat(e.getValue()[o])),r.progress=(a-i)/(o-i)}else r.from=this.from,r.to=this.to;return r}}class _r extends Qe{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),a=new $(e.getColor()),i=new $(r.getColor());if(a.ok&&i.ok){var s=a.g+(i.g-a.g)*t,u=a.b+(i.b-a.b)*t;return"rgb(".concat(Math.floor(a.r+(i.r-a.r)*t),", ").concat(Math.floor(s),", ").concat(Math.floor(u),")")}return this.getAttribute("from").getColor()}}class tn extends Qe{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),a=Z(e.getString()),i=Z(r.getString());return a.map((s,u)=>s+(i[u]-s)*t).join(" ")}}class en extends Nt{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:a}=t,{children:i}=this;for(var o of i)switch(o.type){case"font-face":this.fontFace=o;var s=o.getStyle("font-family");s.hasValue()&&(a[s.getString()]=this);break;case"missing-glyph":this.missingGlyph=o;break;case"glyph":var u=o;u.arabicForm?(this.isRTL=!0,this.isArabic=!0,typeof this.glyphs[u.unicode]>"u"&&(this.glyphs[u.unicode]=Object.create(null)),this.glyphs[u.unicode][u.arabicForm]=u):this.glyphs[u.unicode]=u}}render(){}}class rn extends Nt{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class nn extends xt{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class an extends ie{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class sn extends ie{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:a}=e,i=a[0],o=a.length>0&&Array.from(a).every(s=>3===s.nodeType);this.hasText=o,this.text=o?this.getTextFromNode(i):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:a}=this,{mouse:i}=e.screen,o=new tt(e,"fontSize",Oe.parse(e.ctx.font).fontSize);i.isWorking()&&i.checkBoundingBox(this,new Qt(r,a-o.getPixels("y"),r+this.measureText(t),a))}else if(this.children.length>0){var s=new We(this.document,null);s.children=this.children,s.parent=this,s.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function on(l,t){var e=Object.keys(l);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(l);t&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(l,a).enumerable})),e.push.apply(e,r)}return e}function Ke(l){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?on(Object(e),!0).forEach(function(r){P(l,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(e)):on(Object(e)).forEach(function(r){Object.defineProperty(l,r,Object.getOwnPropertyDescriptor(e,r))})}return l}class un extends ie{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var a=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(a)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(r=>{var{type:a,points:i}=r;switch(a){case st.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case st.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case st.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case st.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case st.ARC:var[o,s,u,f,p,E,T,m]=i,N=u>f?u:f,D=u>f?1:u/f,L=u>f?f/u:1;t&&(t.translate(o,s),t.rotate(T),t.scale(D,L),t.arc(0,0,N,p,p+E,Boolean(1-m)),t.scale(1/D,1/L),t.rotate(-T),t.translate(-o,-s));break;case st.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:a}=this,i=t.fillStyle;"underline"===e&&t.beginPath(),a.forEach((o,s)=>{var{p0:u,p1:f,rotation:p,text:E}=o;t.save(),t.translate(u.x,u.y),t.rotate(p),t.fillStyle&&t.fillText(E,0,0),t.strokeStyle&&t.strokeText(E,0,0),t.restore(),"underline"===e&&(0===s&&t.moveTo(u.x,u.y+r/8),t.lineTo(f.x,f.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=i,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){return this.letterSpacingCache[arguments.length>0&&void 0!==arguments[0]?arguments[0]:0]||0}findSegmentToFitChar(t,e,r,a,i,o,s,u,f){var p=o,E=this.measureText(t,u);" "===u&&"justify"===e&&r<a&&(E+=(a-r)/i),f>-1&&(p+=this.getLetterSpacingAt(f));var T=this.textHeight/20,m=this.getEquidistantPointOnPath(p,T,0),N=this.getEquidistantPointOnPath(p+E,T,0),D={p0:m,p1:N},L=m&&N?Math.atan2(N.y-m.y,N.x-m.x):0;if(s){var U=Math.cos(Math.PI/2+L)*s,K=Math.cos(-L)*s;D.p0=Ke(Ke({},m),{},{x:m.x+U,y:m.y+K}),D.p1=Ke(Ke({},N),{},{x:N.x+U,y:N.y+K})}return{offset:p+=E,segment:D,rotation:L}}measureText(t,e){var{measuresCache:r}=this,a=e||this.getText();if(r.has(a))return r.get(a);var i=this.measureTargetText(t,a);return r.set(a,i),i}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),a=e.split(" ").length-1,i=this.parent.getAttribute("dx").split().map(j=>j.getPixels("x")),o=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),u=this.getStyle("letter-spacing"),f=this.parent.getStyle("letter-spacing"),p=0;u.hasValue()&&"inherit"!==u.getValue()?u.hasValue()&&"initial"!==u.getValue()&&"unset"!==u.getValue()&&(p=u.getPixels()):p=f.getPixels();var E=[],T=e.length;this.letterSpacingCache=E;for(var m=0;m<T;m++)E.push(typeof i[m]<"u"?i[m]:p);var N=E.reduce((j,q,ot)=>0===ot?0:j+q||0,0),D=this.measureText(t),L=Math.max(D+N,0);this.textWidth=D,this.textHeight=this.getFontSize(),this.glyphInfo=[];var U=this.getPathLength(),K=this.getStyle("startOffset").getNumber(0)*U,z=0;("middle"===s||"center"===s)&&(z=-L/2),("end"===s||"right"===s)&&(z=-L),z+=K,r.forEach((j,q)=>{var{offset:ot,segment:W,rotation:ft}=this.findSegmentToFitChar(t,s,L,U,a,z,o,j,q);z=ot,W.p0&&W.p1&&this.glyphInfo.push({text:r[q],p0:W.p0,p1:W.p1,rotation:ft})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:a}=r,i=a?a.x:0,o=a?a.y:0,s=r.next(),u=s.type,f=[];switch(s.type){case st.MOVE_TO:this.pathM(r,f);break;case st.LINE_TO:u=this.pathL(r,f);break;case st.HORIZ_LINE_TO:u=this.pathH(r,f);break;case st.VERT_LINE_TO:u=this.pathV(r,f);break;case st.CURVE_TO:this.pathC(r,f);break;case st.SMOOTH_CURVE_TO:u=this.pathS(r,f);break;case st.QUAD_TO:this.pathQ(r,f);break;case st.SMOOTH_QUAD_TO:u=this.pathT(r,f);break;case st.ARC:f=this.pathA(r);break;case st.CLOSE_PATH:xt.pathZ(r)}e.push(s.type!==st.CLOSE_PATH?{type:u,points:f,start:{x:i,y:o},pathLength:this.calcLength(i,o,u,f)}:{type:st.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:a}=xt.pathM(t).point;e.push(r,a)}pathL(t,e){var{x:r,y:a}=xt.pathL(t).point;return e.push(r,a),st.LINE_TO}pathH(t,e){var{x:r,y:a}=xt.pathH(t).point;return e.push(r,a),st.LINE_TO}pathV(t,e){var{x:r,y:a}=xt.pathV(t).point;return e.push(r,a),st.LINE_TO}pathC(t,e){var{point:r,controlPoint:a,currentPoint:i}=xt.pathC(t);e.push(r.x,r.y,a.x,a.y,i.x,i.y)}pathS(t,e){var{point:r,controlPoint:a,currentPoint:i}=xt.pathS(t);return e.push(r.x,r.y,a.x,a.y,i.x,i.y),st.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:a}=xt.pathQ(t);e.push(r.x,r.y,a.x,a.y)}pathT(t,e){var{controlPoint:r,currentPoint:a}=xt.pathT(t);return e.push(r.x,r.y,a.x,a.y),st.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:a,xAxisRotation:i,centp:o,a1:s,ad:u}=xt.pathA(t);return 0===a&&u>0&&(u-=2*Math.PI),1===a&&u<0&&(u+=2*Math.PI),[o.x,o.y,e,r,s,u,i,a]}calcLength(t,e,r,a){var i=0,o=null,s=null,u=0;switch(r){case st.LINE_TO:return this.getLineLength(t,e,a[0],a[1]);case st.CURVE_TO:for(i=0,o=this.getPointOnCubicBezier(0,t,e,a[0],a[1],a[2],a[3],a[4],a[5]),u=.01;u<=1;u+=.01)s=this.getPointOnCubicBezier(u,t,e,a[0],a[1],a[2],a[3],a[4],a[5]),i+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return i;case st.QUAD_TO:for(i=0,o=this.getPointOnQuadraticBezier(0,t,e,a[0],a[1],a[2],a[3]),u=.01;u<=1;u+=.01)s=this.getPointOnQuadraticBezier(u,t,e,a[0],a[1],a[2],a[3]),i+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return i;case st.ARC:i=0;var f=a[4],p=a[5],E=a[4]+p,T=Math.PI/180;if(Math.abs(f-E)<T&&(T=Math.abs(f-E)),o=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],f,0),p<0)for(u=f-T;u>E;u-=T)s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],u,0),i+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;else for(u=f+T;u<E;u+=T)s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],u,0),i+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],E,0),i+this.getLineLength(o.x,o.y,s.x,s.y)}return 0}getPointOnLine(t,e,r,a,i){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,u=(i-r)/(a-e+Te),f=Math.sqrt(t*t/(1+u*u));a<e&&(f*=-1);var p=u*f,E=null;if(a===e)E={x:o,y:s+p};else if((s-r)/(o-e+Te)===u)E={x:o+f,y:s+p};else{var T,m,N=this.getLineLength(e,r,a,i);if(N<Te)return null;var D=(o-e)*(a-e)+(s-r)*(i-r),L=this.getLineLength(o,s,T=e+(D/=N*N)*(a-e),m=r+D*(i-r)),U=Math.sqrt(t*t-L*L);f=Math.sqrt(U*U/(1+u*u)),a<e&&(f*=-1),E={x:T+f,y:m+(p=u*f)}}return E}getPointOnPath(t){var e=this.getPathLength(),r=0,a=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:i}=this;for(var o of i){if(!o||!(o.pathLength<5e-5||r+o.pathLength+5e-5<t)){var s=t-r,u=0;switch(o.type){case st.LINE_TO:a=this.getPointOnLine(s,o.start.x,o.start.y,o.points[0],o.points[1],o.start.x,o.start.y);break;case st.ARC:var p=o.points[5],E=o.points[4]+p;if(u=o.points[4]+s/o.pathLength*p,p<0&&u<E||p>=0&&u>E)break;a=this.getPointOnEllipticalArc(o.points[0],o.points[1],o.points[2],o.points[3],u,o.points[6]);break;case st.CURVE_TO:(u=s/o.pathLength)>1&&(u=1),a=this.getPointOnCubicBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3],o.points[4],o.points[5]);break;case st.QUAD_TO:(u=s/o.pathLength)>1&&(u=1),a=this.getPointOnQuadraticBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3])}if(a)return a;break}r+=o.pathLength}return null}getLineLength(t,e,r,a){return Math.sqrt((r-t)*(r-t)+(a-e)*(a-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,a,i,o,s,u,f){return{x:u*or(t)+o*ur(t)+a*lr(t)+e*hr(t),y:f*or(t)+s*ur(t)+i*lr(t)+r*hr(t)}}getPointOnQuadraticBezier(t,e,r,a,i,o,s){return{x:o*fr(t)+a*vr(t)+e*cr(t),y:s*fr(t)+i*vr(t)+r*cr(t)}}getPointOnEllipticalArc(t,e,r,a,i,o){var s=Math.cos(o),u=Math.sin(o),f_x=r*Math.cos(i),f_y=a*Math.sin(i);return{x:t+(f_x*s-f_y*u),y:e+(f_x*u+f_y*s)}}buildEquidistantCache(t,e){var r=this.getPathLength(),a=e||.25,i=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==i||this.equidistantCache.precision!==a){this.equidistantCache={step:i,precision:a,points:[]};for(var o=0,s=0;s<=r;s+=a){var u=this.getPointOnPath(s),f=this.getPointOnPath(s+a);!u||!f||(o+=this.getLineLength(u.x,u.y,f.x,f.y))>=i&&(this.equidistantCache.points.push({x:u.x,y:u.y,distance:s}),o-=i)}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var a=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[a]||null}}var jn=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class ln extends ce{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var a=this.getHrefAttribute().getString();if(a){var i=a.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(a);t.images.push(this),i?this.loadSvg(a):this.loadImage(a),this.isSvg=i}}loadImage(t){var e=this;return c(function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(a){console.error('Error while loading image "'.concat(t,'":'),a)}e.loaded=!0})()}loadSvg(t){var e=this;return c(function*(){var r=jn.exec(t);if(r){var a=r[5];e.image="base64"===r[4]?atob(a):decodeURIComponent(a)}else try{var i=yield e.document.fetch(t),o=yield i.text();e.image=o}catch(s){console.error('Error while loading image "'.concat(t,'":'),s)}e.loaded=!0})()}renderChildren(t){var{document:e,image:r,loaded:a}=this,i=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(a&&r&&s&&u){if(t.save(),t.translate(i,o),this.isSvg){var f=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:u});f.document.documentElement.parent=this,f.render()}else{var p=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:p.width,height:u,desiredHeight:p.height}),this.loaded&&(typeof p.complete>"u"||p.complete)&&t.drawImage(p,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");return new Qt(t,e,t+r,e+a)}}class hn extends ce{constructor(){super(...arguments),this.type="symbol"}render(t){}}class fn{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return c(function*(){try{var{document:a}=r,o=(yield a.canvg.parser.load(e)).getElementsByTagName("font");Array.from(o).forEach(s=>{var u=a.createElement(s);a.definitions[t]=u})}catch(s){console.error('Error while loading font "'.concat(e,'":'),s)}r.loaded=!0})()}}let vn=(()=>{class l extends Nt{constructor(e,r,a){super(e,r,a),this.type="style",$t(Array.from(r.childNodes).map(s=>s.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(s=>{var u=s.trim();if(u){var f=u.split("{"),p=f[0].split(","),E=f[1].split(";");p.forEach(T=>{var m=T.trim();if(m){var N=e.styles[m]||{};if(E.forEach(U=>{var K=U.indexOf(":"),z=U.substr(0,K).trim(),j=U.substr(K+1,U.length-K).trim();z&&j&&(N[z]=new tt(e,z,j))}),e.styles[m]=N,e.stylesSpecificity[m]=wr(m),"@font-face"===m){var D=N["font-family"].getString().replace(/"|'/g,"");N.src.getString().split(",").forEach(U=>{if(U.indexOf('format("svg")')>0){var K=St(U);K&&new fn(e).load(D,K)}})}}})}})}}return l.parseExternalUrl=St,l})();class cn extends ce{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var a=r;if("symbol"===r.type&&((a=new we(e,null)).attributes.viewBox=new tt(e,"viewBox",r.getAttribute("viewBox").getString()),a.attributes.preserveAspectRatio=new tt(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),a.attributes.overflow=new tt(e,"overflow",r.getAttribute("overflow").getString()),a.children=r.children,r.styles.opacity=new tt(e,"opacity",this.calculateOpacity())),"svg"===a.type){var i=this.getStyle("width",!1,!0),o=this.getStyle("height",!1,!0);i.hasValue()&&(a.attributes.width=new tt(e,"width",i.getString())),o.hasValue()&&(a.attributes.height=new tt(e,"height",o.getString()))}var s=a.parent;a.parent=this,a.render(t),a.parent=s}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return Xe.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function ke(l,t,e,r,a,i){return l[e*r*4+4*t+i]}function Ze(l,t,e,r,a,i,o){l[e*r*4+4*t+i]=o}function wt(l,t,e){return l[t]*e}function se(l,t,e,r){return t+Math.cos(l)*e+Math.sin(l)*r}class Tr extends Nt{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var a=Z(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var i=a[0];a=[.213+.787*i,.715-.715*i,.072-.072*i,0,0,.213-.213*i,.715+.285*i,.072-.072*i,0,0,.213-.213*i,.715-.715*i,.072+.928*i,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var o=a[0]*Math.PI/180;a=[se(o,.213,.787,-.213),se(o,.715,-.715,-.715),se(o,.072,-.072,.928),0,0,se(o,.213,-.213,.143),se(o,.715,.285,.14),se(o,.072,-.072,-.283),0,0,se(o,.213,-.213,-.787),se(o,.715,-.715,.715),se(o,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=a,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,a,i){for(var{includeOpacity:o,matrix:s}=this,u=t.getImageData(0,0,a,i),f=0;f<i;f++)for(var p=0;p<a;p++){var E=ke(u.data,p,f,a,0,0),T=ke(u.data,p,f,a,0,1),m=ke(u.data,p,f,a,0,2),N=ke(u.data,p,f,a,0,3),D=wt(s,0,E)+wt(s,1,T)+wt(s,2,m)+wt(s,3,N)+wt(s,4,1),L=wt(s,5,E)+wt(s,6,T)+wt(s,7,m)+wt(s,8,N)+wt(s,9,1),U=wt(s,10,E)+wt(s,11,T)+wt(s,12,m)+wt(s,13,N)+wt(s,14,1),K=wt(s,15,E)+wt(s,16,T)+wt(s,17,m)+wt(s,18,N)+wt(s,19,1);o&&(D=0,L=0,U=0,K*=N/255),Ze(u.data,p,f,a,0,0,D),Ze(u.data,p,f,a,0,1,L),Ze(u.data,p,f,a,0,2,U),Ze(u.data,p,f,a,0,3,K)}t.clearRect(0,0,a,i),t.putImageData(u,0,0)}}let gn=(()=>{class l extends Nt{constructor(){super(...arguments),this.type="mask"}apply(e,r){var{document:a}=this,i=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(!s&&!u){var f=new Qt;this.children.forEach(D=>{f.addBoundingBox(D.getBoundingBox(e))}),i=Math.floor(f.x1),o=Math.floor(f.y1),s=Math.floor(f.width),u=Math.floor(f.height)}var p=this.removeStyles(r,l.ignoreStyles),E=a.createCanvas(i+s,o+u),T=E.getContext("2d");a.screen.setDefaults(T),this.renderChildren(T),new Tr(a,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(T,0,0,i+s,o+u);var m=a.createCanvas(i+s,o+u),N=m.getContext("2d");a.screen.setDefaults(N),r.render(N),N.globalCompositeOperation="destination-in",N.fillStyle=T.createPattern(E,"no-repeat"),N.fillRect(0,0,i+s,o+u),e.fillStyle=N.createPattern(m,"no-repeat"),e.fillRect(0,0,i+s,o+u),this.restoreStyles(r,p)}render(e){}}return l.ignoreStyles=["mask","transform","clip-path"],l})();var dn=()=>{};class pn extends Nt{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:a,closePath:i}=t;r&&(r.beginPath=dn,r.closePath=dn),Reflect.apply(a,t,[]),this.children.forEach(o=>{if(!(typeof o.path>"u")){var s=typeof o.elementTransform<"u"?o.elementTransform():null;s||(s=Xe.fromElement(e,o)),s&&s.apply(t),o.path(t),r&&(r.closePath=i),s&&s.unapply(t)}}),Reflect.apply(i,t,[]),t.clip(),r&&(r.beginPath=a,r.closePath=i)}render(t){}}let yn=(()=>{class l extends Nt{constructor(){super(...arguments),this.type="filter"}apply(e,r){var{document:a,children:i}=this,o=r.getBoundingBox(e);if(o){var s=0,u=0;i.forEach(K=>{var z=K.extraFilterDistance||0;s=Math.max(s,z),u=Math.max(u,z)});var f=Math.floor(o.width),p=Math.floor(o.height),E=f+2*s,T=p+2*u;if(!(E<1||T<1)){var m=Math.floor(o.x),N=Math.floor(o.y),D=this.removeStyles(r,l.ignoreStyles),L=a.createCanvas(E,T),U=L.getContext("2d");a.screen.setDefaults(U),U.translate(-m+s,-N+u),r.render(U),i.forEach(K=>{"function"==typeof K.apply&&K.apply(U,0,0,E,T)}),e.drawImage(L,0,0,E,T,m-s,N-u,E,T),this.restoreStyles(r,D)}}}render(e){}}return l.ignoreStyles=["filter","transform","clip-path"],l})();class mn extends Nt{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,a,i){}}class xn extends Nt{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,a,i){}}class En extends Nt{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,a,i){}}class Tn extends Nt{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,a,i){var{document:o,blurRadius:s}=this,u=o.window?o.window.document.body:null,f=t.canvas;f.id=o.getUniqueId(),u&&(f.style.display="none",u.appendChild(f)),ne(f,e,r,a,i,s),u&&u.removeChild(f)}}class On extends Nt{constructor(){super(...arguments),this.type="title"}}class bn extends Nt{constructor(){super(...arguments),this.type="desc"}}var zn={svg:we,rect:mr,circle:Hr,ellipse:Yr,line:Xr,polyline:xr,polygon:Wr,path:xt,pattern:Qr,marker:Kr,defs:kr,linearGradient:Zr,radialGradient:Jr,stop:qr,animate:Qe,animateColor:_r,animateTransform:tn,font:en,"font-face":rn,"missing-glyph":nn,glyph:yr,text:ie,tspan:Fe,tref:an,a:sn,textPath:un,image:ln,g:We,symbol:hn,style:vn,use:cn,mask:gn,clipPath:pn,filter:yn,feDropShadow:mn,feMorphology:xn,feComposite:En,feColorMatrix:Tr,feGaussianBlur:Tn,title:On,desc:bn};function Sn(l,t){var e=Object.keys(l);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(l);t&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(l,a).enumerable})),e.push.apply(e,r)}return e}function $n(l,t){var e=document.createElement("canvas");return e.width=l,e.height=t,e}function Hn(l){return Or.apply(this,arguments)}function Or(){return Or=c(function*(l){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],e=document.createElement("img");return t&&(e.crossOrigin="Anonymous"),new Promise((r,a)=>{e.onload=()=>{r(e)},e.onerror=(i,o,s,u,f)=>{a(f)},e.src=l})}),Or.apply(this,arguments)}let Cn=(()=>{class l{constructor(e){var{rootEmSize:r=12,emSize:a=12,createCanvas:i=l.createCanvas,createImage:o=l.createImage,anonymousCrossOrigin:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=e,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=e.screen,this.rootEmSize=r,this.emSize=a,this.createCanvas=i,this.createImage=this.bindCreateImage(o,s),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(e,r){return"boolean"==typeof r?(a,i)=>e(a,"boolean"==typeof i?i:r):e}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:e}=this;return e[e.length-1]}set emSize(e){var{emSizeStack:r}=this;r.push(e)}popEmSize(){var{emSizeStack:e}=this;e.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(e=>e.loaded)}isFontsLoaded(){return this.fonts.every(e=>e.loaded)}createDocumentElement(e){var r=this.createElement(e.documentElement);return r.root=!0,r.addStylesFromStyleDefinition(),this.documentElement=r,r}createElement(e){var r=e.nodeName.replace(/^[^:]+:/,""),a=l.elementTypes[r];return typeof a<"u"?new a(this,e):new $r(this,e)}createTextNode(e){return new Un(this,e)}setViewBox(e){this.screen.setViewBox(function Gn(l){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?Sn(Object(e),!0).forEach(function(r){P(l,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(e)):Sn(Object(e)).forEach(function(r){Object.defineProperty(l,r,Object.getOwnPropertyDescriptor(e,r))})}return l}({document:this},e))}}return l.createCanvas=$n,l.createImage=Hn,l.elementTypes=zn,l})();function An(l,t){var e=Object.keys(l);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(l);t&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(l,a).enumerable})),e.push.apply(e,r)}return e}function be(l){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?An(Object(e),!0).forEach(function(r){P(l,r,e[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(e)):An(Object(e)).forEach(function(r){Object.defineProperty(l,r,Object.getOwnPropertyDescriptor(e,r))})}return l}class Se{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new Ye(r),this.screen=new gr(t,r),this.options=r;var a=new Cn(this,r),i=a.createDocumentElement(e);this.document=a,this.documentElement=i}static from(t,e){var r=arguments;return c(function*(){var a=r.length>2&&void 0!==r[2]?r[2]:{},i=new Ye(a),o=yield i.parse(e);return new Se(t,o,a)})()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new Ye(r).parseFromString(e);return new Se(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Se.from(t,e,be(be({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Se.fromString(t,e,be(be({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return c(function*(){e.start(be({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},t.length>0&&void 0!==t[0]?t[0]:{})),yield e.ready(),e.stop()})()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:a}=this;r.start(e,be(be({enableRedraw:!0},a),t))}stop(){this.screen.stop()}resize(t){this.documentElement.resize(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,arguments.length>2&&void 0!==arguments[2]&&arguments[2])}}}}]);